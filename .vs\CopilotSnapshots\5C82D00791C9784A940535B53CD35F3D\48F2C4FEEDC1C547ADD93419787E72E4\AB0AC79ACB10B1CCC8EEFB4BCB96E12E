﻿using System.ComponentModel.DataAnnotations;

namespace omsnext.shared.DTO
{
    public class ProcessPasswordResetDto
    {
        [Required]
        public string Action { get; set; } = string.Empty; // "Approved", "Rejected", "Completed"
        
        [Required]
        public string AdminEmail { get; set; } = string.Empty;
        
        public string? Notes { get; set; }
    }

    public class PasswordResetRequestDto
    {
        public long Id { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserDisplayName { get; set; } = string.Empty;
        public DateTime RequestDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ProcessedByAdminName { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public string? AdminNotes { get; set; }
        public string RequestSource { get; set; } = string.Empty;
    }
}