{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/tree_view/m_tree_view.base.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/tree_view/m_tree_view.base.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    fx\r\n} from \"../../../common/core/animation\";\r\nimport {\r\n    name as clickEventName\r\n} from \"../../../common/core/events/click\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    name as dblclickEvent\r\n} from \"../../../common/core/events/double_click\";\r\nimport pointerEvents from \"../../../common/core/events/pointer\";\r\nimport {\r\n    addNamespace\r\n} from \"../../../common/core/events/utils/index\";\r\nimport messageLocalization from \"../../../common/core/localization/message\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../core/element\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    Deferred,\r\n    fromPromise,\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    getImageContainer\r\n} from \"../../../core/utils/icon\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getHeight\r\n} from \"../../../core/utils/size\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isPrimitive,\r\n    isString\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport CheckBox from \"../../../ui/check_box\";\r\nimport LoadIndicator from \"../../../ui/load_indicator\";\r\nimport supportUtils from \"../../core/utils/m_support\";\r\nimport HierarchicalCollectionWidget from \"../../ui/hierarchical_collection/m_hierarchical_collection_widget\";\r\nimport {\r\n    DIRECTION_HORIZONTAL,\r\n    DIRECTION_VERTICAL,\r\n    SCROLLABLE_CONTENT_CLASS\r\n} from \"../../ui/scroll_view/consts\";\r\nimport Scrollable from \"../../ui/scroll_view/m_scrollable\";\r\nimport {\r\n    getRelativeOffset\r\n} from \"../../ui/scroll_view/utils/get_relative_offset\";\r\nconst WIDGET_CLASS = \"dx-treeview\";\r\nconst NODE_CLASS = `${WIDGET_CLASS}-node`;\r\nconst NODE_CONTAINER_CLASS = `${NODE_CLASS}-container`;\r\nconst NODE_LOAD_INDICATOR_CLASS = `${NODE_CLASS}-loadindicator`;\r\nconst OPENED_NODE_CONTAINER_CLASS = `${NODE_CLASS}-container-opened`;\r\nconst IS_LEAF = `${NODE_CLASS}-is-leaf`;\r\nconst ITEM_CLASS = `${WIDGET_CLASS}-item`;\r\nconst ITEM_WITH_CHECKBOX_CLASS = `${ITEM_CLASS}-with-checkbox`;\r\nconst ITEM_WITH_CUSTOM_EXPANDER_ICON_CLASS = `${ITEM_CLASS}-with-custom-expander-icon`;\r\nconst CUSTOM_EXPANDER_ICON_ITEM_CONTAINER_CLASS = `${WIDGET_CLASS}-custom-expander-icon-item-container`;\r\nconst ITEM_WITHOUT_CHECKBOX_CLASS = `${ITEM_CLASS}-without-checkbox`;\r\nconst ITEM_DATA_KEY = `${ITEM_CLASS}-data`;\r\nconst TOGGLE_ITEM_VISIBILITY_CLASS = `${WIDGET_CLASS}-toggle-item-visibility`;\r\nconst CUSTOM_COLLAPSE_ICON_CLASS = `${WIDGET_CLASS}-custom-collapse-icon`;\r\nconst CUSTOM_EXPAND_ICON_CLASS = `${WIDGET_CLASS}-custom-expand-icon`;\r\nconst LOAD_INDICATOR_CLASS = `${WIDGET_CLASS}-loadindicator`;\r\nconst LOAD_INDICATOR_WRAPPER_CLASS = `${WIDGET_CLASS}-loadindicator-wrapper`;\r\nconst TOGGLE_ITEM_VISIBILITY_OPENED_CLASS = `${WIDGET_CLASS}-toggle-item-visibility-opened`;\r\nconst SELECT_ALL_ITEM_CLASS = `${WIDGET_CLASS}-select-all-item`;\r\nconst INVISIBLE_STATE_CLASS = \"dx-state-invisible\";\r\nconst DISABLED_STATE_CLASS = \"dx-state-disabled\";\r\nconst SELECTED_ITEM_CLASS = \"dx-state-selected\";\r\nconst EXPAND_EVENT_NAMESPACE = \"dxTreeView_expand\";\r\nconst DATA_ITEM_ID = \"data-item-id\";\r\nconst ITEM_URL_CLASS = \"dx-item-url\";\r\nconst CHECK_BOX_CLASS = \"dx-checkbox\";\r\nconst CHECK_BOX_ICON_CLASS = \"dx-checkbox-icon\";\r\nconst ROOT_NODE_CLASS = `${WIDGET_CLASS}-root-node`;\r\nconst EXPANDER_ICON_STUB_CLASS = `${WIDGET_CLASS}-expander-icon-stub`;\r\nclass TreeViewBase extends HierarchicalCollectionWidget {\r\n    _supportedKeys() {\r\n        const click = e => {\r\n            const {\r\n                focusedElement: focusedElement\r\n            } = this.option();\r\n            const $itemElement = $(focusedElement);\r\n            if (!$itemElement.length) {\r\n                return\r\n            }\r\n            e.target = $itemElement;\r\n            e.currentTarget = $itemElement;\r\n            this._itemClickHandler(e, $itemElement.children(`.${ITEM_CLASS}`));\r\n            const expandEventName = this._getEventNameByOption(this.option(\"expandEvent\"));\r\n            const expandByClick = expandEventName === addNamespace(clickEventName, \"dxTreeView_expand\");\r\n            if (expandByClick) {\r\n                this._expandEventHandler(e)\r\n            }\r\n        };\r\n        const select = e => {\r\n            e.preventDefault();\r\n            const {\r\n                focusedElement: focusedElement\r\n            } = this.option();\r\n            const $focusedElement = $(focusedElement);\r\n            const checkboxInstance = this._getCheckBoxInstance($focusedElement);\r\n            if (!checkboxInstance.option(\"disabled\")) {\r\n                const currentState = checkboxInstance.option(\"value\");\r\n                this._updateItemSelection(!currentState, $focusedElement.find(`.${ITEM_CLASS}`).get(0), true)\r\n            }\r\n        };\r\n        const toggleExpandedNestedItems = function(state, e) {\r\n            if (!this.option(\"expandAllEnabled\")) {\r\n                return\r\n            }\r\n            e.preventDefault();\r\n            const $rootElement = $(this.option(\"focusedElement\"));\r\n            if (!$rootElement.length) {\r\n                return\r\n            }\r\n            const rootItem = this._getItemData($rootElement.find(`.${ITEM_CLASS}`));\r\n            this._toggleExpandedNestedItems([rootItem], state)\r\n        };\r\n        return _extends({}, super._supportedKeys(), {\r\n            enter: this._showCheckboxes() ? select : click,\r\n            space: this._showCheckboxes() ? select : click,\r\n            asterisk: toggleExpandedNestedItems.bind(this, true),\r\n            minus: toggleExpandedNestedItems.bind(this, false)\r\n        })\r\n    }\r\n    _toggleExpandedNestedItems(items, state) {\r\n        if (!items) {\r\n            return\r\n        }\r\n        for (let i = 0, len = items.length; i < len; i++) {\r\n            const item = items[i];\r\n            const node = this._dataAdapter.getNodeByItem(item);\r\n            this._toggleExpandedState(node, state);\r\n            this._toggleExpandedNestedItems(item.items, state)\r\n        }\r\n    }\r\n    _getNodeElement(node, cache) {\r\n        const key = this._encodeString(node.internalFields.key);\r\n        if (cache) {\r\n            if (!cache.$nodeByKey) {\r\n                cache.$nodeByKey = {};\r\n                this.$element().find(`.${NODE_CLASS}`).each((function() {\r\n                    const $node = $(this);\r\n                    const key = $node.attr(DATA_ITEM_ID);\r\n                    cache.$nodeByKey[key] = $node\r\n                }))\r\n            }\r\n            return cache.$nodeByKey[key] || $()\r\n        }\r\n        const element = this.$element().get(0).querySelector(`[${DATA_ITEM_ID}=\"${key}\"]`);\r\n        return $(element)\r\n    }\r\n    _widgetClass() {\r\n        return WIDGET_CLASS\r\n    }\r\n    _getDefaultOptions() {\r\n        const defaultOptions = extend(super._getDefaultOptions(), {\r\n            animationEnabled: true,\r\n            dataStructure: \"tree\",\r\n            deferRendering: true,\r\n            expandAllEnabled: false,\r\n            hasItemsExpr: \"hasItems\",\r\n            selectNodesRecursive: true,\r\n            expandNodesRecursive: true,\r\n            showCheckBoxesMode: \"none\",\r\n            expandIcon: null,\r\n            collapseIcon: null,\r\n            selectAllText: messageLocalization.format(\"dxList-selectAll\"),\r\n            onItemSelectionChanged: null,\r\n            onItemExpanded: null,\r\n            onItemCollapsed: null,\r\n            scrollDirection: \"vertical\",\r\n            useNativeScrolling: true,\r\n            virtualModeEnabled: false,\r\n            rootValue: 0,\r\n            focusStateEnabled: false,\r\n            selectionMode: \"multiple\",\r\n            expandEvent: \"dblclick\",\r\n            selectByClick: false,\r\n            createChildren: null,\r\n            onSelectAllValueChanged: null,\r\n            _supportItemUrl: false\r\n        });\r\n        return extend(true, defaultOptions, {\r\n            integrationOptions: {\r\n                useDeferUpdateForTemplates: false\r\n            }\r\n        })\r\n    }\r\n    _defaultOptionsRules() {\r\n        return super._defaultOptionsRules().concat([{\r\n            device: () => !supportUtils.nativeScrolling,\r\n            options: {\r\n                useNativeScrolling: false\r\n            }\r\n        }])\r\n    }\r\n    _initSelectedItems() {}\r\n    _syncSelectionOptions() {\r\n        return Deferred().resolve().promise()\r\n    }\r\n    _fireSelectionChanged() {\r\n        this._createActionByOption(\"onSelectionChanged\", {\r\n            excludeValidators: [\"disabled\", \"readOnly\"]\r\n        })()\r\n    }\r\n    _createSelectAllValueChangedAction() {\r\n        this._selectAllValueChangedAction = this._createActionByOption(\"onSelectAllValueChanged\", {\r\n            excludeValidators: [\"disabled\", \"readOnly\"]\r\n        })\r\n    }\r\n    _fireSelectAllValueChanged(value) {\r\n        var _this$_selectAllValue;\r\n        null === (_this$_selectAllValue = this._selectAllValueChangedAction) || void 0 === _this$_selectAllValue || _this$_selectAllValue.call(this, {\r\n            value: value\r\n        })\r\n    }\r\n    _checkBoxModeChange(value, previousValue) {\r\n        const searchEnabled = this.option(\"searchEnabled\");\r\n        const previousSelectAllEnabled = this._selectAllEnabled(previousValue);\r\n        const previousItemsContainer = this._itemContainer(searchEnabled, previousSelectAllEnabled);\r\n        this._detachClickEvent(previousItemsContainer);\r\n        this._detachExpandEvent(previousItemsContainer);\r\n        if (\"none\" === previousValue || \"none\" === value) {\r\n            return\r\n        }\r\n        const selectAllExists = this._$selectAllItem && this._$selectAllItem.length;\r\n        switch (value) {\r\n            case \"selectAll\":\r\n                if (!selectAllExists) {\r\n                    this._createSelectAllValueChangedAction();\r\n                    this._renderSelectAllItem()\r\n                }\r\n                break;\r\n            case \"normal\":\r\n                if (selectAllExists) {\r\n                    var _this$_$selectAllItem;\r\n                    null === (_this$_$selectAllItem = this._$selectAllItem) || void 0 === _this$_$selectAllItem || _this$_$selectAllItem.remove();\r\n                    delete this._$selectAllItem\r\n                }\r\n        }\r\n    }\r\n    _removeSelection() {\r\n        const that = this;\r\n        each(this._dataAdapter.getFullData(), ((_, node) => {\r\n            if (!that._hasChildren(node)) {\r\n                return\r\n            }\r\n            that._dataAdapter.toggleSelection(node.internalFields.key, false, true)\r\n        }))\r\n    }\r\n    _optionChanged(args) {\r\n        const {\r\n            name: name,\r\n            value: value,\r\n            previousValue: previousValue\r\n        } = args;\r\n        switch (name) {\r\n            case \"selectAllText\":\r\n                if (this._$selectAllItem) {\r\n                    this._$selectAllItem.dxCheckBox(\"instance\").option(\"text\", value)\r\n                }\r\n                break;\r\n            case \"showCheckBoxesMode\":\r\n                this._checkBoxModeChange(value, previousValue);\r\n                this._invalidate();\r\n                break;\r\n            case \"scrollDirection\":\r\n                this.getScrollable().option(\"direction\", value);\r\n                break;\r\n            case \"useNativeScrolling\":\r\n                this.getScrollable().option(\"useNative\", value);\r\n                break;\r\n            case \"items\":\r\n                delete this._$selectAllItem;\r\n                super._optionChanged(args);\r\n                break;\r\n            case \"dataSource\":\r\n                super._optionChanged(args);\r\n                this._initDataAdapter();\r\n                this._filter = {};\r\n                break;\r\n            case \"hasItemsExpr\":\r\n                this._initAccessors();\r\n                this.repaint();\r\n                break;\r\n            case \"expandEvent\":\r\n                this._attachExpandEvent();\r\n                break;\r\n            case \"deferRendering\":\r\n            case \"dataStructure\":\r\n            case \"rootValue\":\r\n            case \"createChildren\":\r\n            case \"expandNodesRecursive\":\r\n            case \"onItemSelectionChanged\":\r\n            case \"onItemExpanded\":\r\n            case \"onItemCollapsed\":\r\n            case \"expandAllEnabled\":\r\n            case \"animationEnabled\":\r\n            case \"virtualModeEnabled\":\r\n            case \"selectByClick\":\r\n            case \"_supportItemUrl\":\r\n                break;\r\n            case \"selectionMode\":\r\n                this._initDataAdapter();\r\n                super._optionChanged(args);\r\n                break;\r\n            case \"onSelectAllValueChanged\":\r\n                this._createSelectAllValueChangedAction();\r\n                break;\r\n            case \"selectNodesRecursive\":\r\n                this._dataAdapter.setOption(\"recursiveSelection\", args.value);\r\n                this.repaint();\r\n                break;\r\n            case \"expandIcon\":\r\n            case \"collapseIcon\":\r\n                this.repaint();\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    _initDataSource() {\r\n        if (this._useCustomChildrenLoader()) {\r\n            this._loadChildrenByCustomLoader(null).done((newItems => {\r\n                if (newItems && newItems.length) {\r\n                    this.option(\"items\", newItems)\r\n                }\r\n            }))\r\n        } else {\r\n            super._initDataSource();\r\n            this._isVirtualMode() && this._initVirtualMode()\r\n        }\r\n    }\r\n    _initVirtualMode() {\r\n        const filter = this._filter;\r\n        if (!filter.custom) {\r\n            filter.custom = this._dataSource.filter()\r\n        }\r\n        if (!filter.internal) {\r\n            filter.internal = [this.option(\"parentIdExpr\"), this.option(\"rootValue\")]\r\n        }\r\n    }\r\n    _useCustomChildrenLoader() {\r\n        return isFunction(this.option(\"createChildren\")) && this._isDataStructurePlain()\r\n    }\r\n    _loadChildrenByCustomLoader(parentNode) {\r\n        const invocationResult = this.option(\"createChildren\").call(this, parentNode);\r\n        if (Array.isArray(invocationResult)) {\r\n            return Deferred().resolve(invocationResult).promise()\r\n        }\r\n        if (invocationResult && isFunction(invocationResult.then)) {\r\n            return fromPromise(invocationResult)\r\n        }\r\n        return Deferred().resolve([]).promise()\r\n    }\r\n    _combineFilter() {\r\n        if (!this._filter.custom || !this._filter.custom.length) {\r\n            return this._filter.internal\r\n        }\r\n        return [this._filter.custom, this._filter.internal]\r\n    }\r\n    _dataSourceLoadErrorHandler() {\r\n        this._renderEmptyMessage()\r\n    }\r\n    _init() {\r\n        this._filter = {};\r\n        super._init();\r\n        this._activeStateUnit = `.${ITEM_CLASS}`;\r\n        this._initStoreChangeHandlers()\r\n    }\r\n    _dataSourceChangedHandler(newItems) {\r\n        const items = this.option(\"items\");\r\n        if (this._initialized && this._isVirtualMode() && items.length) {\r\n            return\r\n        }\r\n        this.option(\"items\", newItems)\r\n    }\r\n    _removeTreeViewLoadIndicator() {\r\n        if (!this._treeViewLoadIndicator) {\r\n            return\r\n        }\r\n        this._treeViewLoadIndicator.remove();\r\n        this._treeViewLoadIndicator = null\r\n    }\r\n    _createTreeViewLoadIndicator() {\r\n        this._treeViewLoadIndicator = $(\"<div>\").addClass(LOAD_INDICATOR_CLASS);\r\n        this._createComponent(this._treeViewLoadIndicator, LoadIndicator, {});\r\n        return this._treeViewLoadIndicator\r\n    }\r\n    _dataSourceLoadingChangedHandler(isLoading) {\r\n        let resultFilter;\r\n        if (this._isVirtualMode()) {\r\n            resultFilter = this._combineFilter();\r\n            this._dataSource.filter(resultFilter)\r\n        }\r\n        if (isLoading && !this._dataSource.isLoaded()) {\r\n            this.option(\"items\", []);\r\n            const $wrapper = $(\"<div>\").addClass(LOAD_INDICATOR_WRAPPER_CLASS);\r\n            this._createTreeViewLoadIndicator().appendTo($wrapper);\r\n            this.itemsContainer().append($wrapper);\r\n            if (this._isVirtualMode() && this._dataSource.filter() !== resultFilter) {\r\n                this._dataSource.filter([])\r\n            }\r\n        } else {\r\n            this._removeTreeViewLoadIndicator()\r\n        }\r\n    }\r\n    _initStoreChangeHandlers() {\r\n        const {\r\n            dataStructure: dataStructure\r\n        } = this.option();\r\n        if (\"plain\" !== dataStructure) {\r\n            return\r\n        }\r\n        this._dataSource && this._dataSource.store().on(\"inserted\", (newItem => {\r\n            this.option().items = this.option(\"items\").concat(newItem);\r\n            this._dataAdapter.addItem(newItem);\r\n            if (!this._dataAdapter.isFiltered(newItem)) {\r\n                return\r\n            }\r\n            this._updateLevel(this._parentIdGetter(newItem))\r\n        })).on(\"removed\", (removedKey => {\r\n            const node = this._dataAdapter.getNodeByKey(removedKey);\r\n            if (isDefined(node)) {\r\n                this.option(\"items\")[this._dataAdapter.getIndexByKey(node.internalFields.key)] = 0;\r\n                this._markChildrenItemsToRemove(node);\r\n                this._removeItems();\r\n                this._dataAdapter.removeItem(removedKey);\r\n                this._updateLevel(this._parentIdGetter(node))\r\n            }\r\n        }))\r\n    }\r\n    _markChildrenItemsToRemove(node) {\r\n        const keys = node.internalFields.childrenKeys;\r\n        each(keys, ((_, key) => {\r\n            this.option(\"items\")[this._dataAdapter.getIndexByKey(key)] = 0;\r\n            this._markChildrenItemsToRemove(this._dataAdapter.getNodeByKey(key))\r\n        }))\r\n    }\r\n    _removeItems() {\r\n        const items = extend(true, [], this.option(\"items\"));\r\n        let counter = 0;\r\n        each(items, ((index, item) => {\r\n            if (!item) {\r\n                this.option(\"items\").splice(index - counter, 1);\r\n                counter++\r\n            }\r\n        }))\r\n    }\r\n    _updateLevel(parentId) {\r\n        const $container = this._getContainerByParentKey(parentId);\r\n        this._renderItems($container, this._dataAdapter.getChildrenNodes(parentId))\r\n    }\r\n    _getOldContainer($itemElement) {\r\n        if ($itemElement.length) {\r\n            return $itemElement.children(`.${NODE_CONTAINER_CLASS}`)\r\n        }\r\n        const scrollable = this.getScrollable();\r\n        if (scrollable) {\r\n            return $(scrollable.content()).children()\r\n        }\r\n        return $()\r\n    }\r\n    _getContainerByParentKey(parentId) {\r\n        const node = this._dataAdapter.getNodeByKey(parentId);\r\n        const $itemElement = node ? this._getNodeElement(node) : [];\r\n        this._getOldContainer($itemElement).remove();\r\n        const $container = this._renderNodeContainer($itemElement);\r\n        if (this._isRootLevel(parentId)) {\r\n            const scrollable = this.getScrollable();\r\n            if (!scrollable) {\r\n                this._renderScrollableContainer()\r\n            }\r\n            $(scrollable.content()).append($container)\r\n        }\r\n        return $container\r\n    }\r\n    _isRootLevel(parentId) {\r\n        return parentId === this.option(\"rootValue\")\r\n    }\r\n    _getAccessors() {\r\n        const accessors = super._getAccessors();\r\n        accessors.push(\"hasItems\");\r\n        return accessors\r\n    }\r\n    _getDataAdapterOptions() {\r\n        var _this$_dataSource, _this$_dataSource2, _this$_dataSource2$lo;\r\n        return {\r\n            rootValue: this.option(\"rootValue\"),\r\n            multipleSelection: !this._isSingleSelection(),\r\n            recursiveSelection: this._isRecursiveSelection(),\r\n            recursiveExpansion: this.option(\"expandNodesRecursive\"),\r\n            selectionRequired: this.option(\"selectionRequired\"),\r\n            dataType: this.option(\"dataStructure\"),\r\n            sort: null === (_this$_dataSource = this._dataSource) || void 0 === _this$_dataSource ? void 0 : _this$_dataSource.sort(),\r\n            langParams: null === (_this$_dataSource2 = this._dataSource) || void 0 === _this$_dataSource2 || null === (_this$_dataSource2$lo = _this$_dataSource2.loadOptions) || void 0 === _this$_dataSource2$lo || null === (_this$_dataSource2$lo = _this$_dataSource2$lo.call(_this$_dataSource2)) || void 0 === _this$_dataSource2$lo ? void 0 : _this$_dataSource2$lo.langParams\r\n        }\r\n    }\r\n    _initMarkup() {\r\n        this._renderScrollableContainer();\r\n        this._renderEmptyMessage(this._dataAdapter.getRootNodes());\r\n        super._initMarkup();\r\n        this._setAriaRole()\r\n    }\r\n    _setAriaRole() {\r\n        const {\r\n            items: items\r\n        } = this.option();\r\n        if (items && items.length) {\r\n            this.setAria({\r\n                role: \"tree\"\r\n            })\r\n        }\r\n    }\r\n    _renderContentImpl() {\r\n        const $nodeContainer = this._renderNodeContainer();\r\n        $(this.getScrollable().content()).append($nodeContainer);\r\n        if (!this.option(\"items\") || !this.option(\"items\").length) {\r\n            return\r\n        }\r\n        this._renderItems($nodeContainer, this._dataAdapter.getRootNodes());\r\n        this._attachExpandEvent();\r\n        if (this._selectAllEnabled()) {\r\n            this._createSelectAllValueChangedAction();\r\n            this._renderSelectAllItem($nodeContainer)\r\n        }\r\n    }\r\n    _isVirtualMode() {\r\n        return this.option(\"virtualModeEnabled\") && this._isDataStructurePlain() && !!this.option(\"dataSource\")\r\n    }\r\n    _isDataStructurePlain() {\r\n        const {\r\n            dataStructure: dataStructure\r\n        } = this.option();\r\n        return \"plain\" === dataStructure\r\n    }\r\n    _fireContentReadyAction() {\r\n        const dataSource = this.getDataSource();\r\n        const skipContentReadyAction = dataSource && !dataSource.isLoaded() || this._skipContentReadyAndItemExpanded;\r\n        const scrollable = this.getScrollable();\r\n        if (scrollable && hasWindow()) {\r\n            scrollable.update()\r\n        }\r\n        if (!skipContentReadyAction) {\r\n            super._fireContentReadyAction()\r\n        }\r\n        if (scrollable && hasWindow()) {\r\n            scrollable.update()\r\n        }\r\n    }\r\n    _renderScrollableContainer() {\r\n        this._scrollable = this._createComponent($(\"<div>\").appendTo(this.$element()), Scrollable, {\r\n            useNative: this.option(\"useNativeScrolling\"),\r\n            direction: this.option(\"scrollDirection\"),\r\n            useKeyboard: false\r\n        })\r\n    }\r\n    _renderNodeContainer($parent) {\r\n        const $container = $(\"<ul>\").addClass(NODE_CONTAINER_CLASS);\r\n        this.setAria(\"role\", \"group\", $container);\r\n        if (null !== $parent && void 0 !== $parent && $parent.length) {\r\n            const itemData = this._getItemData($parent.children(`.${ITEM_CLASS}`));\r\n            if (this._expandedGetter(itemData)) {\r\n                $container.addClass(OPENED_NODE_CONTAINER_CLASS)\r\n            }\r\n            $container.appendTo($parent)\r\n        }\r\n        return $container\r\n    }\r\n    _createDOMElement($nodeContainer, node) {\r\n        var _node$internalFields;\r\n        const $node = $(\"<li>\").addClass(NODE_CLASS).attr(DATA_ITEM_ID, this._encodeString(node.internalFields.key)).prependTo($nodeContainer);\r\n        const attrs = {\r\n            role: \"treeitem\",\r\n            label: this._displayGetter(node.internalFields.item) || \"\",\r\n            level: this._getLevel($nodeContainer)\r\n        };\r\n        const hasChildNodes = !!(null !== node && void 0 !== node && null !== (_node$internalFields = node.internalFields) && void 0 !== _node$internalFields && null !== (_node$internalFields = _node$internalFields.childrenKeys) && void 0 !== _node$internalFields && _node$internalFields.length);\r\n        if (hasChildNodes) {\r\n            attrs.expanded = node.internalFields.expanded || false\r\n        }\r\n        this.setAria(attrs, $node);\r\n        return $node\r\n    }\r\n    _getLevel($nodeContainer) {\r\n        const parent = $nodeContainer.parent();\r\n        return parent.hasClass(\"dx-scrollable-content\") ? 1 : parseInt(parent.attr(\"aria-level\")) + 1\r\n    }\r\n    _showCheckboxes() {\r\n        const {\r\n            showCheckBoxesMode: showCheckBoxesMode\r\n        } = this.option();\r\n        return \"none\" !== showCheckBoxesMode\r\n    }\r\n    _hasCustomExpanderIcons() {\r\n        return this.option(\"expandIcon\") || this.option(\"collapseIcon\")\r\n    }\r\n    _selectAllEnabled(showCheckBoxesMode) {\r\n        const mode = showCheckBoxesMode ?? this.option(\"showCheckBoxesMode\");\r\n        return \"selectAll\" === mode && !this._isSingleSelection()\r\n    }\r\n    _renderItems($nodeContainer, nodes) {\r\n        const length = nodes.length - 1;\r\n        for (let i = length; i >= 0; i--) {\r\n            this._renderItem(i, nodes[i], $nodeContainer)\r\n        }\r\n        this._renderedItemsCount += nodes.length\r\n    }\r\n    _renderItem(nodeIndex, node, $nodeContainer) {\r\n        const $node = this._createDOMElement($nodeContainer, node);\r\n        const nodeData = node.internalFields;\r\n        const showCheckBox = this._showCheckboxes();\r\n        $node.addClass(showCheckBox ? ITEM_WITH_CHECKBOX_CLASS : ITEM_WITHOUT_CHECKBOX_CLASS);\r\n        $node.toggleClass(\"dx-state-invisible\", false === nodeData.item.visible);\r\n        if (this._hasCustomExpanderIcons()) {\r\n            $node.addClass(ITEM_WITH_CUSTOM_EXPANDER_ICON_CLASS);\r\n            $nodeContainer.addClass(CUSTOM_EXPANDER_ICON_ITEM_CONTAINER_CLASS)\r\n        }\r\n        this.setAria(\"selected\", nodeData.selected, $node);\r\n        this._toggleSelectedClass($node, nodeData.selected);\r\n        if (nodeData.disabled) {\r\n            this.setAria(\"disabled\", nodeData.disabled, $node)\r\n        }\r\n        super._renderItem(this._renderedItemsCount + nodeIndex, nodeData.item, $node);\r\n        const parent = this._getNode(node.internalFields.parentKey);\r\n        if (!parent) {\r\n            $node.addClass(ROOT_NODE_CLASS)\r\n        }\r\n        if (false !== nodeData.item.visible) {\r\n            this._renderChildren($node, node)\r\n        }\r\n    }\r\n    _setAriaSelectionAttribute() {}\r\n    _renderChildren($node, node) {\r\n        if (!this._hasChildren(node)) {\r\n            this._addLeafClass($node);\r\n            $(\"<div>\").addClass(EXPANDER_ICON_STUB_CLASS).appendTo(this._getItem($node));\r\n            return\r\n        }\r\n        if (this._hasCustomExpanderIcons()) {\r\n            this._renderCustomExpanderIcons($node, node)\r\n        } else {\r\n            this._renderDefaultExpanderIcons($node, node)\r\n        }\r\n        if (this._shouldRenderSublevel(node.internalFields.expanded)) {\r\n            this._loadSublevel(node).done((childNodes => {\r\n                this._renderSublevel($node, this._getActualNode(node), childNodes)\r\n            }))\r\n        }\r\n    }\r\n    _shouldRenderSublevel(expanded) {\r\n        return expanded || !this.option(\"deferRendering\")\r\n    }\r\n    _getActualNode(cachedNode) {\r\n        return this._dataAdapter.getNodeByKey(cachedNode.internalFields.key)\r\n    }\r\n    _hasChildren(node) {\r\n        if (this._isVirtualMode() || this._useCustomChildrenLoader()) {\r\n            return false !== this._hasItemsGetter(node.internalFields.item)\r\n        }\r\n        return super._hasChildren(node)\r\n    }\r\n    _loadSublevel(node) {\r\n        const deferred = Deferred();\r\n        const childrenNodes = this._getChildNodes(node);\r\n        if (childrenNodes.length) {\r\n            deferred.resolve(childrenNodes)\r\n        } else {\r\n            this._loadNestedItems(node).done((items => {\r\n                deferred.resolve(this._dataAdapter.getNodesByItems(items))\r\n            }))\r\n        }\r\n        return deferred.promise()\r\n    }\r\n    _getItemExtraPropNames() {\r\n        return [\"url\", \"linkAttr\"]\r\n    }\r\n    _addContent($container, itemData) {\r\n        const {\r\n            html: html,\r\n            url: url\r\n        } = itemData;\r\n        if (this.option(\"_supportItemUrl\") && url) {\r\n            $container.html(html);\r\n            const link = this._getLinkContainer(this._getIconContainer(itemData), this._getTextContainer(itemData), itemData);\r\n            $container.append(link)\r\n        } else {\r\n            super._addContent($container, itemData)\r\n        }\r\n    }\r\n    _postprocessRenderItem(args) {\r\n        const {\r\n            itemData: itemData,\r\n            itemElement: itemElement\r\n        } = args;\r\n        if (this._showCheckboxes()) {\r\n            this._renderCheckBox(itemElement, this._getNode(itemData))\r\n        }\r\n        super._postprocessRenderItem(args)\r\n    }\r\n    _renderSublevel($node, node, childNodes) {\r\n        const $nestedNodeContainer = this._renderNodeContainer($node);\r\n        const childNodesByChildrenKeys = childNodes.filter((childNode => -1 !== node.internalFields.childrenKeys.indexOf(childNode.internalFields.key)));\r\n        this._renderItems($nestedNodeContainer, childNodesByChildrenKeys);\r\n        if (childNodesByChildrenKeys.length && !node.internalFields.selected) {\r\n            const firstChild = childNodesByChildrenKeys[0];\r\n            this._updateParentsState(firstChild, this._getNodeElement(firstChild))\r\n        }\r\n        this._normalizeIconState($node, childNodesByChildrenKeys.length);\r\n        if (node.internalFields.expanded) {\r\n            $nestedNodeContainer.addClass(OPENED_NODE_CONTAINER_CLASS)\r\n        }\r\n    }\r\n    _executeItemRenderAction(itemIndex, itemData, itemElement) {\r\n        const node = this._getNode(itemElement);\r\n        this._getItemRenderAction()({\r\n            itemElement: itemElement,\r\n            itemIndex: itemIndex,\r\n            itemData: itemData,\r\n            node: this._dataAdapter.getPublicNode(node)\r\n        })\r\n    }\r\n    _addLeafClass($node) {\r\n        $node.addClass(IS_LEAF)\r\n    }\r\n    _expandEventHandler(e) {\r\n        const $nodeElement = $(e.currentTarget.parentNode);\r\n        if (!$nodeElement.hasClass(IS_LEAF)) {\r\n            this._toggleExpandedState(e.currentTarget, void 0, e)\r\n        }\r\n    }\r\n    _attachExpandEvent() {\r\n        const expandedEventName = this._getEventNameByOption(this.option(\"expandEvent\"));\r\n        const $itemsContainer = this._itemContainer();\r\n        this._detachExpandEvent($itemsContainer);\r\n        eventsEngine.on($itemsContainer, expandedEventName, this._itemSelector(), this._expandEventHandler.bind(this))\r\n    }\r\n    _detachExpandEvent(itemsContainer) {\r\n        eventsEngine.off(itemsContainer, \".dxTreeView_expand\", this._itemSelector())\r\n    }\r\n    _getEventNameByOption(name) {\r\n        const event = \"click\" === name ? clickEventName : dblclickEvent;\r\n        return addNamespace(event, \"dxTreeView_expand\")\r\n    }\r\n    _getNode(identifier) {\r\n        if (!isDefined(identifier)) {\r\n            return null\r\n        }\r\n        if (identifier.internalFields) {\r\n            return identifier\r\n        }\r\n        if (isPrimitive(identifier)) {\r\n            return this._dataAdapter.getNodeByKey(identifier)\r\n        }\r\n        const itemElement = $(identifier).get(0);\r\n        if (!itemElement) {\r\n            return null\r\n        }\r\n        if (domAdapter.isElementNode(itemElement)) {\r\n            return this._getNodeByElement(itemElement)\r\n        }\r\n        return this._dataAdapter.getNodeByItem(itemElement)\r\n    }\r\n    _getNodeByElement(itemElement) {\r\n        const $node = $(itemElement).closest(`.${NODE_CLASS}`);\r\n        const key = this._decodeString($node.attr(DATA_ITEM_ID));\r\n        return this._dataAdapter.getNodeByKey(key)\r\n    }\r\n    _toggleExpandedState(itemElement, state, e) {\r\n        const node = this._getNode(itemElement);\r\n        if (!node) {\r\n            return Deferred().reject().promise()\r\n        }\r\n        if (node.internalFields.disabled) {\r\n            return Deferred().reject().promise()\r\n        }\r\n        const currentState = node.internalFields.expanded;\r\n        if (currentState === state) {\r\n            return Deferred().resolve().promise()\r\n        }\r\n        if (this._hasChildren(node)) {\r\n            const $node = this._getNodeElement(node);\r\n            if ($node.find(`.${NODE_LOAD_INDICATOR_CLASS}:not(.dx-state-invisible)`).length) {\r\n                return Deferred().reject().promise()\r\n            }\r\n            if (!currentState && !this._nodeHasRenderedChildren($node)) {\r\n                this._createLoadIndicator($node)\r\n            }\r\n        }\r\n        if (!isDefined(state)) {\r\n            state = !currentState\r\n        }\r\n        this._dataAdapter.toggleExpansion(node.internalFields.key, state);\r\n        return this._updateExpandedItemsUI(node, state, e)\r\n    }\r\n    _nodeHasRenderedChildren($node) {\r\n        const $nodeContainer = $node.children(`.${NODE_CONTAINER_CLASS}`);\r\n        return $nodeContainer.not(\":empty\").length\r\n    }\r\n    _getItem($node) {\r\n        return $node.children(`.${ITEM_CLASS}`).eq(0)\r\n    }\r\n    _createLoadIndicator($node) {\r\n        const $treeviewItem = this._getItem($node);\r\n        this._createComponent($(\"<div>\").addClass(NODE_LOAD_INDICATOR_CLASS), LoadIndicator, {}).$element().appendTo($treeviewItem);\r\n        const $icon = $treeviewItem.children(`.${TOGGLE_ITEM_VISIBILITY_CLASS},.${CUSTOM_EXPAND_ICON_CLASS}`);\r\n        $icon.hide()\r\n    }\r\n    _renderExpanderIcon($node, node, $icon, iconClass) {\r\n        $icon.appendTo(this._getItem($node));\r\n        $icon.addClass(iconClass);\r\n        if (node.internalFields.disabled) {\r\n            $icon.addClass(\"dx-state-disabled\")\r\n        }\r\n        this._renderToggleItemVisibilityIconClick($icon, node)\r\n    }\r\n    _renderDefaultExpanderIcons($node, node) {\r\n        const $treeViewItem = this._getItem($node);\r\n        const $icon = $(\"<div>\").addClass(TOGGLE_ITEM_VISIBILITY_CLASS).appendTo($treeViewItem);\r\n        if (node.internalFields.expanded) {\r\n            $icon.addClass(TOGGLE_ITEM_VISIBILITY_OPENED_CLASS);\r\n            $node.parent().addClass(OPENED_NODE_CONTAINER_CLASS)\r\n        }\r\n        if (node.internalFields.disabled) {\r\n            $icon.addClass(\"dx-state-disabled\")\r\n        }\r\n        this._renderToggleItemVisibilityIconClick($icon, node)\r\n    }\r\n    _renderCustomExpanderIcons($node, node) {\r\n        const {\r\n            expandIcon: expandIcon,\r\n            collapseIcon: collapseIcon\r\n        } = this.option();\r\n        const $expandIcon = getImageContainer(expandIcon ?? collapseIcon);\r\n        const $collapseIcon = getImageContainer(collapseIcon ?? expandIcon);\r\n        this._renderExpanderIcon($node, node, $expandIcon, CUSTOM_EXPAND_ICON_CLASS);\r\n        this._renderExpanderIcon($node, node, $collapseIcon, CUSTOM_COLLAPSE_ICON_CLASS);\r\n        const isNodeExpanded = node.internalFields.expanded;\r\n        if (isNodeExpanded) {\r\n            $node.parent().addClass(OPENED_NODE_CONTAINER_CLASS)\r\n        }\r\n        this._toggleCustomExpanderIcons($expandIcon, $collapseIcon, isNodeExpanded)\r\n    }\r\n    _renderToggleItemVisibilityIconClick($icon, node) {\r\n        const eventName = addNamespace(clickEventName, this.NAME);\r\n        eventsEngine.off($icon, eventName);\r\n        eventsEngine.on($icon, eventName, (e => {\r\n            this._toggleExpandedState(node.internalFields.key, void 0, e);\r\n            return false\r\n        }))\r\n    }\r\n    _toggleCustomExpanderIcons($expandIcon, $collapseIcon, isNodeExpanded) {\r\n        $collapseIcon.toggle(isNodeExpanded);\r\n        $expandIcon.toggle(!isNodeExpanded)\r\n    }\r\n    _updateExpandedItemsUI(node, state, e) {\r\n        const $node = this._getNodeElement(node);\r\n        const isHiddenNode = !$node.length || state && $node.is(\":hidden\");\r\n        if (this.option(\"expandNodesRecursive\") && isHiddenNode) {\r\n            const parentNode = this._getNode(node.internalFields.parentKey);\r\n            if (parentNode) {\r\n                this._updateExpandedItemsUI(parentNode, state, e)\r\n            }\r\n        }\r\n        if (!this._hasCustomExpanderIcons()) {\r\n            const $icon = this._getItem($node).children(`.${TOGGLE_ITEM_VISIBILITY_CLASS}`);\r\n            $icon.toggleClass(TOGGLE_ITEM_VISIBILITY_OPENED_CLASS, state)\r\n        } else if (this._nodeHasRenderedChildren($node)) {\r\n            const $item = this._getItem($node);\r\n            const $childExpandIcons = $item.children(`.${CUSTOM_EXPAND_ICON_CLASS}`);\r\n            const $childCollapseIcons = $item.children(`.${CUSTOM_COLLAPSE_ICON_CLASS}`);\r\n            this._toggleCustomExpanderIcons($childExpandIcons, $childCollapseIcons, state)\r\n        }\r\n        const $nodeContainer = $node.children(`.${NODE_CONTAINER_CLASS}`);\r\n        const nodeContainerExists = $nodeContainer.length > 0;\r\n        const completionCallback = Deferred();\r\n        if (!state || nodeContainerExists && !$nodeContainer.is(\":empty\")) {\r\n            this._animateNodeContainer(node, state, e, completionCallback);\r\n            return completionCallback.promise()\r\n        }\r\n        if (0 === node.internalFields.childrenKeys.length && (this._isVirtualMode() || this._useCustomChildrenLoader())) {\r\n            this._loadNestedItemsWithUpdate(node, state, e, completionCallback);\r\n            return completionCallback.promise()\r\n        }\r\n        this._renderSublevel($node, node, this._getChildNodes(node));\r\n        this._fireContentReadyAction();\r\n        this._animateNodeContainer(node, state, e, completionCallback);\r\n        return completionCallback.promise()\r\n    }\r\n    _loadNestedItemsWithUpdate(node, state, e, completionCallback) {\r\n        const $node = this._getNodeElement(node);\r\n        this._loadNestedItems(node).done((items => {\r\n            const actualNodeData = this._getActualNode(node);\r\n            this._renderSublevel($node, actualNodeData, this._dataAdapter.getNodesByItems(items));\r\n            if (!items || !items.length) {\r\n                completionCallback.resolve();\r\n                return\r\n            }\r\n            this._fireContentReadyAction();\r\n            this._animateNodeContainer(actualNodeData, state, e, completionCallback)\r\n        }))\r\n    }\r\n    _loadNestedItems(node) {\r\n        if (this._useCustomChildrenLoader()) {\r\n            const publicNode = this._dataAdapter.getPublicNode(node);\r\n            return this._loadChildrenByCustomLoader(publicNode).done((newItems => {\r\n                if (!this._areNodesExists(newItems)) {\r\n                    this._appendItems(newItems)\r\n                }\r\n            }))\r\n        }\r\n        if (!this._isVirtualMode()) {\r\n            return Deferred().resolve([]).promise()\r\n        }\r\n        this._filter.internal = [this.option(\"parentIdExpr\"), node.internalFields.key];\r\n        this._dataSource.filter(this._combineFilter());\r\n        return this._dataSource.load().done((newItems => {\r\n            if (!this._areNodesExists(newItems)) {\r\n                this._appendItems(newItems)\r\n            }\r\n        }))\r\n    }\r\n    _areNodesExists(newItems) {\r\n        const keyOfRootItem = this.keyOf(newItems[0]);\r\n        const fullData = this._dataAdapter.getFullData();\r\n        return !!this._dataAdapter.getNodeByKey(keyOfRootItem, fullData)\r\n    }\r\n    _appendItems(newItems) {\r\n        const {\r\n            items: items = []\r\n        } = this.option();\r\n        this.option().items = items.concat(newItems);\r\n        this._initDataAdapter()\r\n    }\r\n    _animateNodeContainer(node, state, e, completionCallback) {\r\n        const $node = this._getNodeElement(node);\r\n        const $nodeContainer = $node.children(`.${NODE_CONTAINER_CLASS}`);\r\n        if (node && completionCallback && 0 === $nodeContainer.length) {\r\n            completionCallback.resolve()\r\n        }\r\n        $nodeContainer.addClass(OPENED_NODE_CONTAINER_CLASS);\r\n        const nodeHeight = getHeight($nodeContainer);\r\n        fx.stop($nodeContainer, true);\r\n        fx.animate($nodeContainer, {\r\n            type: \"custom\",\r\n            duration: this.option(\"animationEnabled\") ? 400 : 0,\r\n            from: {\r\n                maxHeight: state ? 0 : nodeHeight\r\n            },\r\n            to: {\r\n                maxHeight: state ? nodeHeight : 0\r\n            },\r\n            complete: function() {\r\n                $nodeContainer.css(\"maxHeight\", \"none\");\r\n                $nodeContainer.toggleClass(OPENED_NODE_CONTAINER_CLASS, state);\r\n                this.setAria(\"expanded\", state, $node);\r\n                this.getScrollable().update();\r\n                this._fireExpandedStateUpdatedEvent(state, node, e);\r\n                if (completionCallback) {\r\n                    completionCallback.resolve()\r\n                }\r\n            }.bind(this)\r\n        })\r\n    }\r\n    _fireExpandedStateUpdatedEvent(isExpanded, node, e) {\r\n        if (!this._hasChildren(node) || this._skipContentReadyAndItemExpanded) {\r\n            return\r\n        }\r\n        const optionName = isExpanded ? \"onItemExpanded\" : \"onItemCollapsed\";\r\n        if (isDefined(e)) {\r\n            this._itemDXEventHandler(e, optionName, {\r\n                node: this._dataAdapter.getPublicNode(node)\r\n            })\r\n        } else {\r\n            const target = this._getNodeElement(node);\r\n            this._itemEventHandler(target, optionName, {\r\n                event: e,\r\n                node: this._dataAdapter.getPublicNode(node)\r\n            })\r\n        }\r\n    }\r\n    _normalizeIconState($node, hasNewItems) {\r\n        const $loadIndicator = $node.find(`.${NODE_LOAD_INDICATOR_CLASS}`);\r\n        if ($loadIndicator.length) {\r\n            var _LoadIndicator$getIns;\r\n            null === (_LoadIndicator$getIns = LoadIndicator.getInstance($loadIndicator)) || void 0 === _LoadIndicator$getIns || _LoadIndicator$getIns.option(\"visible\", false)\r\n        }\r\n        const $treeViewItem = this._getItem($node);\r\n        const $toggleItem = $treeViewItem.children(`.${CUSTOM_COLLAPSE_ICON_CLASS},.${TOGGLE_ITEM_VISIBILITY_CLASS}`);\r\n        if (hasNewItems) {\r\n            $toggleItem.show();\r\n            return\r\n        }\r\n        $toggleItem.removeClass(TOGGLE_ITEM_VISIBILITY_CLASS);\r\n        $node.addClass(IS_LEAF)\r\n    }\r\n    _emptyMessageContainer() {\r\n        const scrollable = this.getScrollable();\r\n        return scrollable ? $(scrollable.content()) : super._emptyMessageContainer()\r\n    }\r\n    _renderContent() {\r\n        const {\r\n            items: items\r\n        } = this.option();\r\n        if (items && items.length) {\r\n            this._contentAlreadyRendered = true\r\n        }\r\n        super._renderContent()\r\n    }\r\n    _renderSelectAllItem($container) {\r\n        const {\r\n            selectAllText: selectAllText,\r\n            focusStateEnabled: focusStateEnabled\r\n        } = this.option();\r\n        $container = $container || this.$element().find(`.${NODE_CONTAINER_CLASS}`).first();\r\n        this._$selectAllItem = $(\"<div>\").addClass(SELECT_ALL_ITEM_CLASS);\r\n        const value = this._dataAdapter.isAllSelected();\r\n        this._createComponent(this._$selectAllItem, CheckBox, {\r\n            value: value,\r\n            elementAttr: {\r\n                \"aria-label\": \"Select All\"\r\n            },\r\n            text: selectAllText,\r\n            focusStateEnabled: focusStateEnabled,\r\n            onValueChanged: this._onSelectAllCheckboxValueChanged.bind(this),\r\n            onInitialized: _ref => {\r\n                let {\r\n                    component: component\r\n                } = _ref;\r\n                component.registerKeyHandler(\"enter\", (() => {\r\n                    component.option(\"value\", !component.option(\"value\"))\r\n                }))\r\n            }\r\n        });\r\n        this._toggleSelectedClass(this._$selectAllItem, value);\r\n        $container.before(this._$selectAllItem)\r\n    }\r\n    _onSelectAllCheckboxValueChanged(args) {\r\n        this._toggleSelectAll(args);\r\n        this._fireSelectAllValueChanged(args.value)\r\n    }\r\n    _toggleSelectAll(args) {\r\n        this._dataAdapter.toggleSelectAll(args.value);\r\n        this._updateItemsUI();\r\n        this._fireSelectionChanged()\r\n    }\r\n    _renderCheckBox($node, node) {\r\n        const $checkbox = $(\"<div>\").appendTo($node);\r\n        this._createComponent($checkbox, CheckBox, {\r\n            value: node.internalFields.selected,\r\n            onValueChanged: this._changeCheckboxValue.bind(this),\r\n            focusStateEnabled: false,\r\n            elementAttr: {\r\n                \"aria-label\": messageLocalization.format(\"CheckState\")\r\n            },\r\n            disabled: this._disabledGetter(node)\r\n        })\r\n    }\r\n    _toggleSelectedClass($node, value) {\r\n        $node.toggleClass(\"dx-state-selected\", !!value)\r\n    }\r\n    _toggleNodeDisabledState(node, state) {\r\n        const $node = this._getNodeElement(node);\r\n        const $item = $node.find(`.${ITEM_CLASS}`).eq(0);\r\n        this._dataAdapter.toggleNodeDisabledState(node.internalFields.key, state);\r\n        $item.toggleClass(\"dx-state-disabled\", !!state);\r\n        if (this._showCheckboxes()) {\r\n            const checkbox = this._getCheckBoxInstance($node);\r\n            checkbox.option(\"disabled\", !!state)\r\n        }\r\n    }\r\n    _itemOptionChanged(item, property, value) {\r\n        const node = this._dataAdapter.getNodeByItem(item);\r\n        if (property === this.option(\"disabledExpr\")) {\r\n            this._toggleNodeDisabledState(node, value)\r\n        }\r\n    }\r\n    _changeCheckboxValue(e) {\r\n        const $node = $(e.element).closest(`.${NODE_CLASS}`);\r\n        const $item = this._getItem($node);\r\n        const item = this._getItemData($item);\r\n        const node = this._getNodeByElement($item);\r\n        const {\r\n            value: value\r\n        } = e;\r\n        if (node && node.internalFields.selected === value) {\r\n            return\r\n        }\r\n        this._updateItemSelection(value, item, e.event)\r\n    }\r\n    _isSingleSelection() {\r\n        const {\r\n            selectionMode: selectionMode\r\n        } = this.option();\r\n        return \"single\" === selectionMode\r\n    }\r\n    _isRecursiveSelection() {\r\n        const {\r\n            selectionMode: selectionMode\r\n        } = this.option();\r\n        return this.option(\"selectNodesRecursive\") && \"single\" !== selectionMode\r\n    }\r\n    _isLastSelectedBranch(publicNode, selectedNodesKeys, deep) {\r\n        const keyIndex = selectedNodesKeys.indexOf(publicNode.key);\r\n        if (keyIndex >= 0) {\r\n            selectedNodesKeys.splice(keyIndex, 1)\r\n        }\r\n        if (deep) {\r\n            each(publicNode.children, ((_, childNode) => {\r\n                this._isLastSelectedBranch(childNode, selectedNodesKeys, true)\r\n            }))\r\n        }\r\n        if (publicNode.parent) {\r\n            this._isLastSelectedBranch(publicNode.parent, selectedNodesKeys)\r\n        }\r\n        return 0 === selectedNodesKeys.length\r\n    }\r\n    _isLastRequired(node) {\r\n        const selectionRequired = this.option(\"selectionRequired\");\r\n        const isSingleMode = this._isSingleSelection();\r\n        const selectedNodesKeys = this.getSelectedNodeKeys();\r\n        if (!selectionRequired) {\r\n            return\r\n        }\r\n        if (isSingleMode) {\r\n            return 1 === selectedNodesKeys.length\r\n        }\r\n        return this._isLastSelectedBranch(node.internalFields.publicNode, selectedNodesKeys.slice(), true)\r\n    }\r\n    _updateItemSelection(value, itemElement, dxEvent) {\r\n        const node = this._getNode(itemElement);\r\n        if (!node || false === node.visible) {\r\n            return false\r\n        }\r\n        if (node.internalFields.selected === value) {\r\n            return true\r\n        }\r\n        if (!value && this._isLastRequired(node)) {\r\n            if (this._showCheckboxes()) {\r\n                const $node = this._getNodeElement(node);\r\n                this._getCheckBoxInstance($node).option(\"value\", true)\r\n            }\r\n            return false\r\n        }\r\n        if (value && this._isSingleSelection()) {\r\n            const selectedKeys = this.getSelectedNodeKeys();\r\n            each(selectedKeys, ((index, key) => {\r\n                this._dataAdapter.toggleSelection(key, false);\r\n                this._updateItemsUI();\r\n                this._fireItemSelectionChanged(this._getNode(key))\r\n            }))\r\n        }\r\n        this._dataAdapter.toggleSelection(node.internalFields.key, value);\r\n        const isAllSelected = this._dataAdapter.isAllSelected();\r\n        const needFireSelectAllChanged = this._selectAllEnabled() && this._$selectAllItem.dxCheckBox(\"instance\").option(\"value\") !== isAllSelected;\r\n        this._updateItemsUI();\r\n        this._fireItemSelectionChanged(node, dxEvent);\r\n        this._fireSelectionChanged();\r\n        if (needFireSelectAllChanged) {\r\n            this._fireSelectAllValueChanged(isAllSelected)\r\n        }\r\n        return true\r\n    }\r\n    _fireItemSelectionChanged(node, dxEvent) {\r\n        const initiator = dxEvent || this._findItemElementByItem(node.internalFields.item);\r\n        const handler = dxEvent ? this._itemDXEventHandler : this._itemEventHandler;\r\n        handler.call(this, initiator, \"onItemSelectionChanged\", {\r\n            node: this._dataAdapter.getPublicNode(node),\r\n            itemData: node.internalFields.item\r\n        })\r\n    }\r\n    _getCheckBoxInstance($node) {\r\n        const $treeViewItem = this._getItem($node);\r\n        return $treeViewItem.children(\".dx-checkbox\").dxCheckBox(\"instance\")\r\n    }\r\n    _updateItemsUI() {\r\n        const cache = {};\r\n        each(this._dataAdapter.getData(), ((_, node) => {\r\n            const $node = this._getNodeElement(node, cache);\r\n            const nodeSelection = node.internalFields.selected;\r\n            if (!$node.length) {\r\n                return\r\n            }\r\n            this._toggleSelectedClass($node, nodeSelection);\r\n            this.setAria(\"selected\", nodeSelection, $node);\r\n            if (this._showCheckboxes()) {\r\n                this._getCheckBoxInstance($node).option(\"value\", nodeSelection)\r\n            }\r\n        }));\r\n        if (this._selectAllEnabled()) {\r\n            const selectAllCheckbox = this._$selectAllItem.dxCheckBox(\"instance\");\r\n            selectAllCheckbox.option(\"onValueChanged\", void 0);\r\n            selectAllCheckbox.option(\"value\", this._dataAdapter.isAllSelected());\r\n            selectAllCheckbox.option(\"onValueChanged\", this._onSelectAllCheckboxValueChanged.bind(this))\r\n        }\r\n    }\r\n    _updateParentsState(node, $node) {\r\n        if (!$node) {\r\n            return\r\n        }\r\n        const parentNode = this._dataAdapter.getNodeByKey(node.internalFields.parentKey);\r\n        const $parentNode = $($node.parents(`.${NODE_CLASS}`)[0]);\r\n        if (this._showCheckboxes()) {\r\n            var _this$_getCheckBoxIns;\r\n            const parentValue = parentNode.internalFields.selected;\r\n            null === (_this$_getCheckBoxIns = this._getCheckBoxInstance($parentNode)) || void 0 === _this$_getCheckBoxIns || _this$_getCheckBoxIns.option(\"value\", parentValue);\r\n            this._toggleSelectedClass($parentNode, parentValue)\r\n        }\r\n        if (parentNode.internalFields.parentKey !== this.option(\"rootValue\")) {\r\n            this._updateParentsState(parentNode, $parentNode)\r\n        }\r\n    }\r\n    _itemEventHandlerImpl(initiator, action, actionArgs) {\r\n        const $itemElement = $(initiator).closest(`.${NODE_CLASS}`).children(`.${ITEM_CLASS}`);\r\n        return action(extend(this._extendActionArgs($itemElement), actionArgs))\r\n    }\r\n    _itemContextMenuHandler(e) {\r\n        this._createEventHandler(\"onItemContextMenu\", e)\r\n    }\r\n    _itemHoldHandler(e) {\r\n        this._createEventHandler(\"onItemHold\", e)\r\n    }\r\n    _createEventHandler(eventName, e) {\r\n        const node = this._getNodeByElement(e.currentTarget);\r\n        this._itemDXEventHandler(e, eventName, {\r\n            node: this._dataAdapter.getPublicNode(node)\r\n        })\r\n    }\r\n    _itemClass() {\r\n        return ITEM_CLASS\r\n    }\r\n    _itemDataKey() {\r\n        return ITEM_DATA_KEY\r\n    }\r\n    _attachClickEvent() {\r\n        const $itemContainer = this._itemContainer();\r\n        this._detachClickEvent($itemContainer);\r\n        const {\r\n            clickEventNamespace: clickEventNamespace,\r\n            itemSelector: itemSelector,\r\n            pointerDownEventNamespace: pointerDownEventNamespace,\r\n            nodeSelector: nodeSelector\r\n        } = this._getItemClickEventData();\r\n        eventsEngine.on($itemContainer, clickEventNamespace, itemSelector, (e => {\r\n            if ($(e.target).hasClass(\"dx-checkbox-icon\") || $(e.target).hasClass(\"dx-checkbox\")) {\r\n                return\r\n            }\r\n            this._itemClickHandler(e, $(e.currentTarget))\r\n        }));\r\n        eventsEngine.on($itemContainer, pointerDownEventNamespace, nodeSelector, (e => {\r\n            this._itemPointerDownHandler(e)\r\n        }))\r\n    }\r\n    _detachClickEvent(itemsContainer) {\r\n        const {\r\n            clickEventNamespace: clickEventNamespace,\r\n            itemSelector: itemSelector,\r\n            pointerDownEventNamespace: pointerDownEventNamespace,\r\n            nodeSelector: nodeSelector\r\n        } = this._getItemClickEventData();\r\n        eventsEngine.off(itemsContainer, clickEventNamespace, itemSelector);\r\n        eventsEngine.off(itemsContainer, pointerDownEventNamespace, nodeSelector)\r\n    }\r\n    _getItemClickEventData() {\r\n        const itemSelector = `.${this._itemClass()}`;\r\n        const nodeSelector = `.${NODE_CLASS}, .${SELECT_ALL_ITEM_CLASS}`;\r\n        const clickEventNamespace = addNamespace(clickEventName, this.NAME);\r\n        const pointerDownEventNamespace = addNamespace(pointerEvents.down, this.NAME);\r\n        return {\r\n            clickEventNamespace: clickEventNamespace,\r\n            itemSelector: itemSelector,\r\n            pointerDownEventNamespace: pointerDownEventNamespace,\r\n            nodeSelector: nodeSelector\r\n        }\r\n    }\r\n    _itemClick(actionArgs) {\r\n        const args = actionArgs.args[0];\r\n        const target = args.event.target[0] || args.event.target;\r\n        const link = target.getElementsByClassName(\"dx-item-url\")[0];\r\n        if (args.itemData.url && link) {\r\n            link.click()\r\n        }\r\n    }\r\n    _itemClickHandler(e, $item) {\r\n        const itemData = this._getItemData($item);\r\n        const node = this._getNodeByElement($item);\r\n        this._itemDXEventHandler(e, \"onItemClick\", {\r\n            node: this._dataAdapter.getPublicNode(node)\r\n        }, {\r\n            beforeExecute: this._itemClick\r\n        });\r\n        if (this.option(\"selectByClick\") && !e.isDefaultPrevented()) {\r\n            this._updateItemSelection(!node.internalFields.selected, itemData, e)\r\n        }\r\n    }\r\n    _updateSelectionToFirstItem($items, startIndex) {\r\n        let itemIndex = startIndex;\r\n        while (itemIndex >= 0) {\r\n            const $item = $($items[itemIndex]);\r\n            this._updateItemSelection(true, $item.find(`.${ITEM_CLASS}`).get(0));\r\n            itemIndex--\r\n        }\r\n    }\r\n    _updateSelectionToLastItem($items, startIndex) {\r\n        const {\r\n            length: length\r\n        } = $items;\r\n        let itemIndex = startIndex;\r\n        while (itemIndex < length) {\r\n            const $item = $($items[itemIndex]);\r\n            this._updateItemSelection(true, $item.find(`.${ITEM_CLASS}`).get(0));\r\n            itemIndex++\r\n        }\r\n    }\r\n    focus() {\r\n        if (this._selectAllEnabled()) {\r\n            eventsEngine.trigger(this._$selectAllItem, \"focus\");\r\n            return\r\n        }\r\n        super.focus()\r\n    }\r\n    _focusInHandler(e) {\r\n        this._updateFocusState(e, true);\r\n        const isSelectAllItem = $(e.target).hasClass(SELECT_ALL_ITEM_CLASS);\r\n        if (isSelectAllItem || this.option(\"focusedElement\")) {\r\n            clearTimeout(this._setFocusedItemTimeout);\r\n            this._setFocusedItemTimeout = setTimeout((() => {\r\n                const {\r\n                    focusedElement: focusedElement\r\n                } = this.option();\r\n                const element = isSelectAllItem ? getPublicElement(this._$selectAllItem) : $(focusedElement);\r\n                this._setFocusedItem(element)\r\n            }));\r\n            return\r\n        }\r\n        const $activeItem = this._getActiveItem();\r\n        this.option(\"focusedElement\", getPublicElement($activeItem.closest(`.${NODE_CLASS}`)))\r\n    }\r\n    _itemPointerDownHandler(e) {\r\n        if (!this.option(\"focusStateEnabled\")) {\r\n            return\r\n        }\r\n        const $target = $(e.target).closest(`.${NODE_CLASS}, .${SELECT_ALL_ITEM_CLASS}`);\r\n        if (!$target.length) {\r\n            return\r\n        }\r\n        const itemElement = $target.hasClass(\"dx-state-disabled\") ? null : $target;\r\n        this.option(\"focusedElement\", getPublicElement(itemElement))\r\n    }\r\n    _findNonDisabledNodes($nodes) {\r\n        return $nodes.not((function() {\r\n            return $(this).children(`.${ITEM_CLASS}`).hasClass(\"dx-state-disabled\")\r\n        }))\r\n    }\r\n    _moveFocus(location, e) {\r\n        const FOCUS_LEFT = this.option(\"rtlEnabled\") ? \"right\" : \"left\";\r\n        const FOCUS_RIGHT = this.option(\"rtlEnabled\") ? \"left\" : \"right\";\r\n        this.$element().find(`.${NODE_CONTAINER_CLASS}`).each((function() {\r\n            fx.stop(this, true)\r\n        }));\r\n        const $items = this._nodeElements();\r\n        if (!$items || !$items.length) {\r\n            return\r\n        }\r\n        switch (location) {\r\n            case \"up\": {\r\n                const $prevItem = this._prevItem($items);\r\n                this.option(\"focusedElement\", getPublicElement($prevItem));\r\n                const prevItemElement = this._getNodeItemElement($prevItem);\r\n                this.getScrollable().scrollToElement(prevItemElement);\r\n                if (e.shiftKey && this._showCheckboxes()) {\r\n                    this._updateItemSelection(true, prevItemElement)\r\n                }\r\n                break\r\n            }\r\n            case \"down\": {\r\n                const $nextItem = this._nextItem($items);\r\n                this.option(\"focusedElement\", getPublicElement($nextItem));\r\n                const nextItemElement = this._getNodeItemElement($nextItem);\r\n                this.getScrollable().scrollToElement(nextItemElement);\r\n                if (e.shiftKey && this._showCheckboxes()) {\r\n                    this._updateItemSelection(true, nextItemElement)\r\n                }\r\n                break\r\n            }\r\n            case \"first\": {\r\n                const $firstItem = $items.first();\r\n                if (e.shiftKey && this._showCheckboxes()) {\r\n                    this._updateSelectionToFirstItem($items, $items.index(this._prevItem($items)))\r\n                }\r\n                this.option(\"focusedElement\", getPublicElement($firstItem));\r\n                this.getScrollable().scrollToElement(this._getNodeItemElement($firstItem));\r\n                break\r\n            }\r\n            case \"last\": {\r\n                const $lastItem = $items.last();\r\n                if (e.shiftKey && this._showCheckboxes()) {\r\n                    this._updateSelectionToLastItem($items, $items.index(this._nextItem($items)))\r\n                }\r\n                this.option(\"focusedElement\", getPublicElement($lastItem));\r\n                this.getScrollable().scrollToElement(this._getNodeItemElement($lastItem));\r\n                break\r\n            }\r\n            case FOCUS_RIGHT:\r\n                this._expandFocusedContainer();\r\n                break;\r\n            case FOCUS_LEFT:\r\n                this._collapseFocusedContainer();\r\n                break;\r\n            default:\r\n                super._moveFocus.apply(this, arguments)\r\n        }\r\n    }\r\n    _getNodeItemElement($node) {\r\n        return $node.find(`.${ITEM_CLASS}`).get(0)\r\n    }\r\n    _nodeElements() {\r\n        return this.$element().find(`.${NODE_CLASS}`).not(\":hidden\")\r\n    }\r\n    _expandFocusedContainer() {\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        const $focusedNode = $(focusedElement);\r\n        if (!$focusedNode.length || $focusedNode.hasClass(IS_LEAF)) {\r\n            return\r\n        }\r\n        const $node = $focusedNode.find(`.${NODE_CONTAINER_CLASS}`).eq(0);\r\n        if ($node.hasClass(OPENED_NODE_CONTAINER_CLASS)) {\r\n            const $nextItem = this._nextItem(this._findNonDisabledNodes(this._nodeElements()));\r\n            this.option(\"focusedElement\", getPublicElement($nextItem));\r\n            this.getScrollable().scrollToElement(this._getNodeItemElement($nextItem));\r\n            return\r\n        }\r\n        const node = this._getNodeByElement(this._getItem($focusedNode));\r\n        this._toggleExpandedState(node, true)\r\n    }\r\n    _getClosestNonDisabledNode($node) {\r\n        do {\r\n            $node = $node.parent().closest(`.${NODE_CLASS}`)\r\n        } while ($node.children(\".dx-treeview-item.dx-state-disabled\").length);\r\n        return $node\r\n    }\r\n    _collapseFocusedContainer() {\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        const $focusedNode = $(focusedElement);\r\n        if (!$focusedNode.length) {\r\n            return\r\n        }\r\n        const nodeElement = $focusedNode.find(`.${NODE_CONTAINER_CLASS}`).eq(0);\r\n        if (!$focusedNode.hasClass(IS_LEAF) && nodeElement.hasClass(OPENED_NODE_CONTAINER_CLASS)) {\r\n            const node = this._getNodeByElement(this._getItem($focusedNode));\r\n            this._toggleExpandedState(node, false)\r\n        } else {\r\n            const collapsedNode = this._getClosestNonDisabledNode($focusedNode);\r\n            collapsedNode.length && this.option(\"focusedElement\", getPublicElement(collapsedNode));\r\n            this.getScrollable().scrollToElement(this._getNodeItemElement(collapsedNode))\r\n        }\r\n    }\r\n    _encodeString(value) {\r\n        return isString(value) ? encodeURI(value) : value\r\n    }\r\n    _decodeString(value) {\r\n        return isString(value) ? decodeURI(value) : value\r\n    }\r\n    getScrollable() {\r\n        return this._scrollable\r\n    }\r\n    updateDimensions() {\r\n        const deferred = Deferred();\r\n        const scrollable = this.getScrollable();\r\n        if (scrollable) {\r\n            scrollable.update().done((() => {\r\n                deferred.resolveWith(this)\r\n            }))\r\n        } else {\r\n            deferred.resolveWith(this)\r\n        }\r\n        return deferred.promise()\r\n    }\r\n    selectItem(itemElement) {\r\n        return this._updateItemSelection(true, itemElement)\r\n    }\r\n    unselectItem(itemElement) {\r\n        return this._updateItemSelection(false, itemElement)\r\n    }\r\n    expandItem(itemElement) {\r\n        return this._toggleExpandedState(itemElement, true)\r\n    }\r\n    collapseItem(itemElement) {\r\n        return this._toggleExpandedState(itemElement, false)\r\n    }\r\n    getNodes() {\r\n        return this._dataAdapter.getTreeNodes()\r\n    }\r\n    getSelectedNodes() {\r\n        return this.getSelectedNodeKeys().map((key => {\r\n            const node = this._dataAdapter.getNodeByKey(key);\r\n            return this._dataAdapter.getPublicNode(node)\r\n        }))\r\n    }\r\n    getSelectedNodeKeys() {\r\n        return this._dataAdapter.getSelectedNodesKeys()\r\n    }\r\n    selectAll() {\r\n        if (this._selectAllEnabled()) {\r\n            this._$selectAllItem.dxCheckBox(\"instance\").option(\"value\", true)\r\n        } else {\r\n            this._toggleSelectAll({\r\n                value: true\r\n            })\r\n        }\r\n    }\r\n    unselectAll() {\r\n        if (this._selectAllEnabled()) {\r\n            this._$selectAllItem.dxCheckBox(\"instance\").option(\"value\", false)\r\n        } else {\r\n            this._toggleSelectAll({\r\n                value: false\r\n            })\r\n        }\r\n    }\r\n    _allItemsExpandedHandler() {\r\n        this._skipContentReadyAndItemExpanded = false;\r\n        this._fireContentReadyAction()\r\n    }\r\n    expandAll() {\r\n        const nodes = this._dataAdapter.getData();\r\n        const expandingPromises = [];\r\n        this._skipContentReadyAndItemExpanded = true;\r\n        nodes.forEach((node => expandingPromises.push(this._toggleExpandedState(node.internalFields.key, true))));\r\n        Promise.allSettled(expandingPromises).then((() => {\r\n            var _this$_allItemsExpand;\r\n            return null === (_this$_allItemsExpand = this._allItemsExpandedHandler) || void 0 === _this$_allItemsExpand ? void 0 : _this$_allItemsExpand.call(this)\r\n        }))\r\n    }\r\n    collapseAll() {\r\n        each(this._dataAdapter.getExpandedNodesKeys(), ((_, key) => {\r\n            this._toggleExpandedState(key, false)\r\n        }))\r\n    }\r\n    scrollToItem(keyOrItemOrElement) {\r\n        const node = this._getNode(keyOrItemOrElement);\r\n        if (!node) {\r\n            return Deferred().reject().promise()\r\n        }\r\n        const nodeKeysToExpand = [];\r\n        let parentNode = node.internalFields.publicNode.parent;\r\n        while (null != parentNode) {\r\n            if (!parentNode.expanded) {\r\n                nodeKeysToExpand.push(parentNode.key)\r\n            }\r\n            parentNode = parentNode.parent\r\n        }\r\n        const scrollCallback = Deferred();\r\n        this._expandNodes(nodeKeysToExpand.reverse()).always((() => {\r\n            const $element = this._getNodeElement(node);\r\n            if ($element && $element.length) {\r\n                this.scrollToElementTopLeft($element.get(0));\r\n                scrollCallback.resolve()\r\n            } else {\r\n                scrollCallback.reject()\r\n            }\r\n        }));\r\n        return scrollCallback.promise()\r\n    }\r\n    scrollToElementTopLeft(targetElement) {\r\n        const scrollable = this.getScrollable();\r\n        const {\r\n            scrollDirection: scrollDirection,\r\n            rtlEnabled: rtlEnabled\r\n        } = this.option();\r\n        const targetLocation = {\r\n            top: 0,\r\n            left: 0\r\n        };\r\n        const relativeOffset = getRelativeOffset(SCROLLABLE_CONTENT_CLASS, targetElement);\r\n        if (scrollDirection !== DIRECTION_VERTICAL) {\r\n            const containerElement = $(scrollable.container()).get(0);\r\n            targetLocation.left = rtlEnabled ? relativeOffset.left + targetElement.offsetWidth - containerElement.clientWidth : relativeOffset.left\r\n        }\r\n        if (scrollDirection !== DIRECTION_HORIZONTAL) {\r\n            targetLocation.top = relativeOffset.top\r\n        }\r\n        scrollable.scrollTo(targetLocation)\r\n    }\r\n    _expandNodes(keysToExpand) {\r\n        if (!keysToExpand || 0 === keysToExpand.length) {\r\n            return Deferred().resolve().promise()\r\n        }\r\n        const resultCallback = Deferred();\r\n        const callbacksByNodes = keysToExpand.map((key => this.expandItem(key)));\r\n        when.apply($, callbacksByNodes).done((() => resultCallback.resolve())).fail((() => resultCallback.reject()));\r\n        return resultCallback.promise()\r\n    }\r\n    _dispose() {\r\n        super._dispose();\r\n        clearTimeout(this._setFocusedItemTimeout);\r\n        this._allItemsExpandedHandler = null\r\n    }\r\n}\r\nexport default TreeViewBase;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAKA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAMA;AAAA;AAGA;AACA;AACA;AAAA;AACA;AACA;AAKA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,eAAe;AACrB,MAAM,aAAa,GAAG,aAAa,KAAK,CAAC;AACzC,MAAM,uBAAuB,GAAG,WAAW,UAAU,CAAC;AACtD,MAAM,4BAA4B,GAAG,WAAW,cAAc,CAAC;AAC/D,MAAM,8BAA8B,GAAG,WAAW,iBAAiB,CAAC;AACpE,MAAM,UAAU,GAAG,WAAW,QAAQ,CAAC;AACvC,MAAM,aAAa,GAAG,aAAa,KAAK,CAAC;AACzC,MAAM,2BAA2B,GAAG,WAAW,cAAc,CAAC;AAC9D,MAAM,uCAAuC,GAAG,WAAW,0BAA0B,CAAC;AACtF,MAAM,4CAA4C,GAAG,aAAa,oCAAoC,CAAC;AACvG,MAAM,8BAA8B,GAAG,WAAW,iBAAiB,CAAC;AACpE,MAAM,gBAAgB,GAAG,WAAW,KAAK,CAAC;AAC1C,MAAM,+BAA+B,GAAG,aAAa,uBAAuB,CAAC;AAC7E,MAAM,6BAA6B,GAAG,aAAa,qBAAqB,CAAC;AACzE,MAAM,2BAA2B,GAAG,aAAa,mBAAmB,CAAC;AACrE,MAAM,uBAAuB,GAAG,aAAa,cAAc,CAAC;AAC5D,MAAM,+BAA+B,GAAG,aAAa,sBAAsB,CAAC;AAC5E,MAAM,sCAAsC,GAAG,aAAa,8BAA8B,CAAC;AAC3F,MAAM,wBAAwB,GAAG,aAAa,gBAAgB,CAAC;AAC/D,MAAM,wBAAwB;AAC9B,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB;AAC/B,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,uBAAuB;AAC7B,MAAM,kBAAkB,GAAG,aAAa,UAAU,CAAC;AACnD,MAAM,2BAA2B,GAAG,aAAa,mBAAmB,CAAC;AACrE,MAAM,qBAAqB,uNAAA,CAAA,UAA4B;IACnD,iBAAiB;QACb,MAAM,QAAQ,CAAA;YACV,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;YACf,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;YACvB,IAAI,CAAC,aAAa,MAAM,EAAE;gBACtB;YACJ;YACA,EAAE,MAAM,GAAG;YACX,EAAE,aAAa,GAAG;YAClB,IAAI,CAAC,iBAAiB,CAAC,GAAG,aAAa,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY;YAChE,MAAM,kBAAkB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;YAC/D,MAAM,gBAAgB,oBAAoB,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,uKAAA,CAAA,OAAc,EAAE;YACvE,IAAI,eAAe;gBACf,IAAI,CAAC,mBAAmB,CAAC;YAC7B;QACJ;QACA,MAAM,SAAS,CAAA;YACX,EAAE,cAAc;YAChB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;YACf,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;YAC1B,MAAM,mBAAmB,IAAI,CAAC,oBAAoB,CAAC;YACnD,IAAI,CAAC,iBAAiB,MAAM,CAAC,aAAa;gBACtC,MAAM,eAAe,iBAAiB,MAAM,CAAC;gBAC7C,IAAI,CAAC,oBAAoB,CAAC,CAAC,cAAc,gBAAgB,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC,IAAI;YAC5F;QACJ;QACA,MAAM,4BAA4B,SAAS,KAAK,EAAE,CAAC;YAC/C,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB;gBAClC;YACJ;YACA,EAAE,cAAc;YAChB,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC;YACnC,IAAI,CAAC,aAAa,MAAM,EAAE;gBACtB;YACJ;YACA,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY;YACrE,IAAI,CAAC,0BAA0B,CAAC;gBAAC;aAAS,EAAE;QAChD;QACA,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,kBAAkB;YACxC,OAAO,IAAI,CAAC,eAAe,KAAK,SAAS;YACzC,OAAO,IAAI,CAAC,eAAe,KAAK,SAAS;YACzC,UAAU,0BAA0B,IAAI,CAAC,IAAI,EAAE;YAC/C,OAAO,0BAA0B,IAAI,CAAC,IAAI,EAAE;QAChD;IACJ;IACA,2BAA2B,KAAK,EAAE,KAAK,EAAE;QACrC,IAAI,CAAC,OAAO;YACR;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,MAAM,MAAM,MAAM,EAAE,IAAI,KAAK,IAAK;YAC9C,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;YAC7C,IAAI,CAAC,oBAAoB,CAAC,MAAM;YAChC,IAAI,CAAC,0BAA0B,CAAC,KAAK,KAAK,EAAE;QAChD;IACJ;IACA,gBAAgB,IAAI,EAAE,KAAK,EAAE;QACzB,MAAM,MAAM,IAAI,CAAC,aAAa,CAAC,KAAK,cAAc,CAAC,GAAG;QACtD,IAAI,OAAO;YACP,IAAI,CAAC,MAAM,UAAU,EAAE;gBACnB,MAAM,UAAU,GAAG,CAAC;gBACpB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAI,CAAE;oBACzC,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI;oBACpB,MAAM,MAAM,MAAM,IAAI,CAAC;oBACvB,MAAM,UAAU,CAAC,IAAI,GAAG;gBAC5B;YACJ;YACA,OAAO,MAAM,UAAU,CAAC,IAAI,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD;QACpC;QACA,MAAM,UAAU,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,GAAG,aAAa,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,EAAE,IAAI,EAAE,CAAC;QACjF,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;IACb;IACA,eAAe;QACX,OAAO;IACX;IACA,qBAAqB;QACjB,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,KAAK,CAAC,sBAAsB;YACtD,kBAAkB;YAClB,eAAe;YACf,gBAAgB;YAChB,kBAAkB;YAClB,cAAc;YACd,sBAAsB;YACtB,sBAAsB;YACtB,oBAAoB;YACpB,YAAY;YACZ,cAAc;YACd,eAAe,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC1C,wBAAwB;YACxB,gBAAgB;YAChB,iBAAiB;YACjB,iBAAiB;YACjB,oBAAoB;YACpB,oBAAoB;YACpB,WAAW;YACX,mBAAmB;YACnB,eAAe;YACf,aAAa;YACb,eAAe;YACf,gBAAgB;YAChB,yBAAyB;YACzB,iBAAiB;QACrB;QACA,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,gBAAgB;YAChC,oBAAoB;gBAChB,4BAA4B;YAChC;QACJ;IACJ;IACA,uBAAuB;QACnB,OAAO,KAAK,CAAC,uBAAuB,MAAM,CAAC;YAAC;gBACxC,QAAQ,IAAM,CAAC,gMAAA,CAAA,UAAY,CAAC,eAAe;gBAC3C,SAAS;oBACL,oBAAoB;gBACxB;YACJ;SAAE;IACN;IACA,qBAAqB,CAAC;IACtB,wBAAwB;QACpB,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;IACvC;IACA,wBAAwB;QACpB,IAAI,CAAC,qBAAqB,CAAC,sBAAsB;YAC7C,mBAAmB;gBAAC;gBAAY;aAAW;QAC/C;IACJ;IACA,qCAAqC;QACjC,IAAI,CAAC,4BAA4B,GAAG,IAAI,CAAC,qBAAqB,CAAC,2BAA2B;YACtF,mBAAmB;gBAAC;gBAAY;aAAW;QAC/C;IACJ;IACA,2BAA2B,KAAK,EAAE;QAC9B,IAAI;QACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,4BAA4B,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,IAAI,EAAE;YACzI,OAAO;QACX;IACJ;IACA,oBAAoB,KAAK,EAAE,aAAa,EAAE;QACtC,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;QAClC,MAAM,2BAA2B,IAAI,CAAC,iBAAiB,CAAC;QACxD,MAAM,yBAAyB,IAAI,CAAC,cAAc,CAAC,eAAe;QAClE,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,kBAAkB,CAAC;QACxB,IAAI,WAAW,iBAAiB,WAAW,OAAO;YAC9C;QACJ;QACA,MAAM,kBAAkB,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM;QAC3E,OAAQ;YACJ,KAAK;gBACD,IAAI,CAAC,iBAAiB;oBAClB,IAAI,CAAC,kCAAkC;oBACvC,IAAI,CAAC,oBAAoB;gBAC7B;gBACA;YACJ,KAAK;gBACD,IAAI,iBAAiB;oBACjB,IAAI;oBACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,eAAe,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,MAAM;oBAC3H,OAAO,IAAI,CAAC,eAAe;gBAC/B;QACR;IACJ;IACA,mBAAmB;QACf,MAAM,OAAO,IAAI;QACjB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,CAAC,WAAW,IAAK,CAAC,GAAG;YACvC,IAAI,CAAC,KAAK,YAAY,CAAC,OAAO;gBAC1B;YACJ;YACA,KAAK,YAAY,CAAC,eAAe,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE,OAAO;QACtE;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,MAAM,EACF,MAAM,IAAI,EACV,OAAO,KAAK,EACZ,eAAe,aAAa,EAC/B,GAAG;QACJ,OAAQ;YACJ,KAAK;gBACD,IAAI,IAAI,CAAC,eAAe,EAAE;oBACtB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,YAAY,MAAM,CAAC,QAAQ;gBAC/D;gBACA;YACJ,KAAK;gBACD,IAAI,CAAC,mBAAmB,CAAC,OAAO;gBAChC,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa;gBACzC;YACJ,KAAK;gBACD,IAAI,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa;gBACzC;YACJ,KAAK;gBACD,OAAO,IAAI,CAAC,eAAe;gBAC3B,KAAK,CAAC,eAAe;gBACrB;YACJ,KAAK;gBACD,KAAK,CAAC,eAAe;gBACrB,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,OAAO,GAAG,CAAC;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,cAAc;gBACnB,IAAI,CAAC,OAAO;gBACZ;YACJ,KAAK;gBACD,IAAI,CAAC,kBAAkB;gBACvB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;gBACD,IAAI,CAAC,gBAAgB;gBACrB,KAAK,CAAC,eAAe;gBACrB;YACJ,KAAK;gBACD,IAAI,CAAC,kCAAkC;gBACvC;YACJ,KAAK;gBACD,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,sBAAsB,KAAK,KAAK;gBAC5D,IAAI,CAAC,OAAO;gBACZ;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,OAAO;gBACZ;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,wBAAwB,IAAI;YACjC,IAAI,CAAC,2BAA2B,CAAC,MAAM,IAAI,CAAE,CAAA;gBACzC,IAAI,YAAY,SAAS,MAAM,EAAE;oBAC7B,IAAI,CAAC,MAAM,CAAC,SAAS;gBACzB;YACJ;QACJ,OAAO;YACH,KAAK,CAAC;YACN,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,gBAAgB;QAClD;IACJ;IACA,mBAAmB;QACf,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI,CAAC,OAAO,MAAM,EAAE;YAChB,OAAO,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;QAC3C;QACA,IAAI,CAAC,OAAO,QAAQ,EAAE;YAClB,OAAO,QAAQ,GAAG;gBAAC,IAAI,CAAC,MAAM,CAAC;gBAAiB,IAAI,CAAC,MAAM,CAAC;aAAa;QAC7E;IACJ;IACA,2BAA2B;QACvB,OAAO,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,sBAAsB,IAAI,CAAC,qBAAqB;IAClF;IACA,4BAA4B,UAAU,EAAE;QACpC,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,IAAI,EAAE;QAClE,IAAI,MAAM,OAAO,CAAC,mBAAmB;YACjC,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,CAAC,kBAAkB,OAAO;QACvD;QACA,IAAI,oBAAoB,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,IAAI,GAAG;YACvD,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;QACvB;QACA,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,CAAC,EAAE,EAAE,OAAO;IACzC;IACA,iBAAiB;QACb,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,MAAM,EAAE;YACrD,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ;QAChC;QACA,OAAO;YAAC,IAAI,CAAC,OAAO,CAAC,MAAM;YAAE,IAAI,CAAC,OAAO,CAAC,QAAQ;SAAC;IACvD;IACA,8BAA8B;QAC1B,IAAI,CAAC,mBAAmB;IAC5B;IACA,QAAQ;QACJ,IAAI,CAAC,OAAO,GAAG,CAAC;QAChB,KAAK,CAAC;QACN,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,YAAY;QACxC,IAAI,CAAC,wBAAwB;IACjC;IACA,0BAA0B,QAAQ,EAAE;QAChC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,cAAc,MAAM,MAAM,MAAM,EAAE;YAC5D;QACJ;QACA,IAAI,CAAC,MAAM,CAAC,SAAS;IACzB;IACA,+BAA+B;QAC3B,IAAI,CAAC,IAAI,CAAC,sBAAsB,EAAE;YAC9B;QACJ;QACA,IAAI,CAAC,sBAAsB,CAAC,MAAM;QAClC,IAAI,CAAC,sBAAsB,GAAG;IAClC;IACA,+BAA+B;QAC3B,IAAI,CAAC,sBAAsB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAClD,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,sBAAsB,EAAE,yJAAA,CAAA,UAAa,EAAE,CAAC;QACnE,OAAO,IAAI,CAAC,sBAAsB;IACtC;IACA,iCAAiC,SAAS,EAAE;QACxC,IAAI;QACJ,IAAI,IAAI,CAAC,cAAc,IAAI;YACvB,eAAe,IAAI,CAAC,cAAc;YAClC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QAC5B;QACA,IAAI,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,QAAQ,IAAI;YAC3C,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;YACvB,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;YACrC,IAAI,CAAC,4BAA4B,GAAG,QAAQ,CAAC;YAC7C,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC;YAC7B,IAAI,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,cAAc;gBACrE,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;YAC9B;QACJ,OAAO;YACH,IAAI,CAAC,4BAA4B;QACrC;IACJ;IACA,2BAA2B;QACvB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,YAAY,eAAe;YAC3B;QACJ;QACA,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK,GAAG,EAAE,CAAC,YAAa,CAAA;YACzD,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,CAAC;YAClD,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;YAC1B,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,UAAU;gBACxC;YACJ;YACA,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;QAC3C,GAAI,EAAE,CAAC,WAAY,CAAA;YACf,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;YAC5C,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO;gBACjB,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE,GAAG;gBACjF,IAAI,CAAC,0BAA0B,CAAC;gBAChC,IAAI,CAAC,YAAY;gBACjB,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;gBAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;YAC3C;QACJ;IACJ;IACA,2BAA2B,IAAI,EAAE;QAC7B,MAAM,OAAO,KAAK,cAAc,CAAC,YAAY;QAC7C,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,MAAO,CAAC,GAAG;YACZ,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,KAAK,GAAG;YAC7D,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;QACnE;IACJ;IACA,eAAe;QACX,MAAM,QAAQ,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,MAAM,CAAC;QAC3C,IAAI,UAAU;QACd,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;YACjB,IAAI,CAAC,MAAM;gBACP,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,CAAC,QAAQ,SAAS;gBAC7C;YACJ;QACJ;IACJ;IACA,aAAa,QAAQ,EAAE;QACnB,MAAM,aAAa,IAAI,CAAC,wBAAwB,CAAC;QACjD,IAAI,CAAC,YAAY,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;IACrE;IACA,iBAAiB,YAAY,EAAE;QAC3B,IAAI,aAAa,MAAM,EAAE;YACrB,OAAO,aAAa,QAAQ,CAAC,CAAC,CAAC,EAAE,sBAAsB;QAC3D;QACA,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,IAAI,YAAY;YACZ,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO,IAAI,QAAQ;QAC3C;QACA,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD;IACX;IACA,yBAAyB,QAAQ,EAAE;QAC/B,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;QAC5C,MAAM,eAAe,OAAO,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE;QAC3D,IAAI,CAAC,gBAAgB,CAAC,cAAc,MAAM;QAC1C,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC;QAC7C,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW;YAC7B,MAAM,aAAa,IAAI,CAAC,aAAa;YACrC,IAAI,CAAC,YAAY;gBACb,IAAI,CAAC,0BAA0B;YACnC;YACA,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO,IAAI,MAAM,CAAC;QACnC;QACA,OAAO;IACX;IACA,aAAa,QAAQ,EAAE;QACnB,OAAO,aAAa,IAAI,CAAC,MAAM,CAAC;IACpC;IACA,gBAAgB;QACZ,MAAM,YAAY,KAAK,CAAC;QACxB,UAAU,IAAI,CAAC;QACf,OAAO;IACX;IACA,yBAAyB;QACrB,IAAI,mBAAmB,oBAAoB;QAC3C,OAAO;YACH,WAAW,IAAI,CAAC,MAAM,CAAC;YACvB,mBAAmB,CAAC,IAAI,CAAC,kBAAkB;YAC3C,oBAAoB,IAAI,CAAC,qBAAqB;YAC9C,oBAAoB,IAAI,CAAC,MAAM,CAAC;YAChC,mBAAmB,IAAI,CAAC,MAAM,CAAC;YAC/B,UAAU,IAAI,CAAC,MAAM,CAAC;YACtB,MAAM,SAAS,CAAC,oBAAoB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,IAAI;YACvH,YAAY,SAAS,CAAC,qBAAqB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,sBAAsB,SAAS,CAAC,wBAAwB,mBAAmB,WAAW,KAAK,KAAK,MAAM,yBAAyB,SAAS,CAAC,wBAAwB,sBAAsB,IAAI,CAAC,mBAAmB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,UAAU;QAC/W;IACJ;IACA,cAAc;QACV,IAAI,CAAC,0BAA0B;QAC/B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY;QACvD,KAAK,CAAC;QACN,IAAI,CAAC,YAAY;IACrB;IACA,eAAe;QACX,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,SAAS,MAAM,MAAM,EAAE;YACvB,IAAI,CAAC,OAAO,CAAC;gBACT,MAAM;YACV;QACJ;IACJ;IACA,qBAAqB;QACjB,MAAM,iBAAiB,IAAI,CAAC,oBAAoB;QAChD,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,aAAa,GAAG,OAAO,IAAI,MAAM,CAAC;QACzC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,EAAE;YACvD;QACJ;QACA,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,CAAC,YAAY,CAAC,YAAY;QAChE,IAAI,CAAC,kBAAkB;QACvB,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC1B,IAAI,CAAC,kCAAkC;YACvC,IAAI,CAAC,oBAAoB,CAAC;QAC9B;IACJ;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,MAAM,CAAC,yBAAyB,IAAI,CAAC,qBAAqB,MAAM,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IAC9F;IACA,wBAAwB;QACpB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,YAAY;IACvB;IACA,0BAA0B;QACtB,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,yBAAyB,cAAc,CAAC,WAAW,QAAQ,MAAM,IAAI,CAAC,gCAAgC;QAC5G,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,IAAI,cAAc,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YAC3B,WAAW,MAAM;QACrB;QACA,IAAI,CAAC,wBAAwB;YACzB,KAAK,CAAC;QACV;QACA,IAAI,cAAc,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YAC3B,WAAW,MAAM;QACrB;IACJ;IACA,6BAA6B;QACzB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,QAAQ,KAAK,uLAAA,CAAA,UAAU,EAAE;YACvF,WAAW,IAAI,CAAC,MAAM,CAAC;YACvB,WAAW,IAAI,CAAC,MAAM,CAAC;YACvB,aAAa;QACjB;IACJ;IACA,qBAAqB,OAAO,EAAE;QAC1B,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,QAAQ,CAAC;QACtC,IAAI,CAAC,OAAO,CAAC,QAAQ,SAAS;QAC9B,IAAI,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,MAAM,EAAE;YAC1D,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,QAAQ,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY;YACpE,IAAI,IAAI,CAAC,eAAe,CAAC,WAAW;gBAChC,WAAW,QAAQ,CAAC;YACxB;YACA,WAAW,QAAQ,CAAC;QACxB;QACA,OAAO;IACX;IACA,kBAAkB,cAAc,EAAE,IAAI,EAAE;QACpC,IAAI;QACJ,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,QAAQ,CAAC,YAAY,IAAI,CAAC,cAAc,IAAI,CAAC,aAAa,CAAC,KAAK,cAAc,CAAC,GAAG,GAAG,SAAS,CAAC;QACvH,MAAM,QAAQ;YACV,MAAM;YACN,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,cAAc,CAAC,IAAI,KAAK;YACxD,OAAO,IAAI,CAAC,SAAS,CAAC;QAC1B;QACA,MAAM,gBAAgB,CAAC,CAAC,CAAC,SAAS,QAAQ,KAAK,MAAM,QAAQ,SAAS,CAAC,uBAAuB,KAAK,cAAc,KAAK,KAAK,MAAM,wBAAwB,SAAS,CAAC,uBAAuB,qBAAqB,YAAY,KAAK,KAAK,MAAM,wBAAwB,qBAAqB,MAAM;QAC9R,IAAI,eAAe;YACf,MAAM,QAAQ,GAAG,KAAK,cAAc,CAAC,QAAQ,IAAI;QACrD;QACA,IAAI,CAAC,OAAO,CAAC,OAAO;QACpB,OAAO;IACX;IACA,UAAU,cAAc,EAAE;QACtB,MAAM,SAAS,eAAe,MAAM;QACpC,OAAO,OAAO,QAAQ,CAAC,2BAA2B,IAAI,SAAS,OAAO,IAAI,CAAC,iBAAiB;IAChG;IACA,kBAAkB;QACd,MAAM,EACF,oBAAoB,kBAAkB,EACzC,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,WAAW;IACtB;IACA,0BAA0B;QACtB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC;IACpD;IACA,kBAAkB,kBAAkB,EAAE;QAClC,MAAM,OAAO,sBAAsB,IAAI,CAAC,MAAM,CAAC;QAC/C,OAAO,gBAAgB,QAAQ,CAAC,IAAI,CAAC,kBAAkB;IAC3D;IACA,aAAa,cAAc,EAAE,KAAK,EAAE;QAChC,MAAM,SAAS,MAAM,MAAM,GAAG;QAC9B,IAAK,IAAI,IAAI,QAAQ,KAAK,GAAG,IAAK;YAC9B,IAAI,CAAC,WAAW,CAAC,GAAG,KAAK,CAAC,EAAE,EAAE;QAClC;QACA,IAAI,CAAC,mBAAmB,IAAI,MAAM,MAAM;IAC5C;IACA,YAAY,SAAS,EAAE,IAAI,EAAE,cAAc,EAAE;QACzC,MAAM,QAAQ,IAAI,CAAC,iBAAiB,CAAC,gBAAgB;QACrD,MAAM,WAAW,KAAK,cAAc;QACpC,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,MAAM,QAAQ,CAAC,eAAe,2BAA2B;QACzD,MAAM,WAAW,CAAC,sBAAsB,UAAU,SAAS,IAAI,CAAC,OAAO;QACvE,IAAI,IAAI,CAAC,uBAAuB,IAAI;YAChC,MAAM,QAAQ,CAAC;YACf,eAAe,QAAQ,CAAC;QAC5B;QACA,IAAI,CAAC,OAAO,CAAC,YAAY,SAAS,QAAQ,EAAE;QAC5C,IAAI,CAAC,oBAAoB,CAAC,OAAO,SAAS,QAAQ;QAClD,IAAI,SAAS,QAAQ,EAAE;YACnB,IAAI,CAAC,OAAO,CAAC,YAAY,SAAS,QAAQ,EAAE;QAChD;QACA,KAAK,CAAC,YAAY,IAAI,CAAC,mBAAmB,GAAG,WAAW,SAAS,IAAI,EAAE;QACvE,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,CAAC,SAAS;QAC1D,IAAI,CAAC,QAAQ;YACT,MAAM,QAAQ,CAAC;QACnB;QACA,IAAI,UAAU,SAAS,IAAI,CAAC,OAAO,EAAE;YACjC,IAAI,CAAC,eAAe,CAAC,OAAO;QAChC;IACJ;IACA,6BAA6B,CAAC;IAC9B,gBAAgB,KAAK,EAAE,IAAI,EAAE;QACzB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO;YAC1B,IAAI,CAAC,aAAa,CAAC;YACnB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,0BAA0B,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;YACrE;QACJ;QACA,IAAI,IAAI,CAAC,uBAAuB,IAAI;YAChC,IAAI,CAAC,0BAA0B,CAAC,OAAO;QAC3C,OAAO;YACH,IAAI,CAAC,2BAA2B,CAAC,OAAO;QAC5C;QACA,IAAI,IAAI,CAAC,qBAAqB,CAAC,KAAK,cAAc,CAAC,QAAQ,GAAG;YAC1D,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAE,CAAA;gBAC3B,IAAI,CAAC,eAAe,CAAC,OAAO,IAAI,CAAC,cAAc,CAAC,OAAO;YAC3D;QACJ;IACJ;IACA,sBAAsB,QAAQ,EAAE;QAC5B,OAAO,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;IACpC;IACA,eAAe,UAAU,EAAE;QACvB,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,WAAW,cAAc,CAAC,GAAG;IACvE;IACA,aAAa,IAAI,EAAE;QACf,IAAI,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,wBAAwB,IAAI;YAC1D,OAAO,UAAU,IAAI,CAAC,eAAe,CAAC,KAAK,cAAc,CAAC,IAAI;QAClE;QACA,OAAO,KAAK,CAAC,aAAa;IAC9B;IACA,cAAc,IAAI,EAAE;QAChB,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,MAAM,gBAAgB,IAAI,CAAC,cAAc,CAAC;QAC1C,IAAI,cAAc,MAAM,EAAE;YACtB,SAAS,OAAO,CAAC;QACrB,OAAO;YACH,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAE,CAAA;gBAC9B,SAAS,OAAO,CAAC,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;YACvD;QACJ;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,yBAAyB;QACrB,OAAO;YAAC;YAAO;SAAW;IAC9B;IACA,YAAY,UAAU,EAAE,QAAQ,EAAE;QAC9B,MAAM,EACF,MAAM,IAAI,EACV,KAAK,GAAG,EACX,GAAG;QACJ,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK;YACvC,WAAW,IAAI,CAAC;YAChB,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB,CAAC,WAAW,IAAI,CAAC,iBAAiB,CAAC,WAAW;YACxG,WAAW,MAAM,CAAC;QACtB,OAAO;YACH,KAAK,CAAC,YAAY,YAAY;QAClC;IACJ;IACA,uBAAuB,IAAI,EAAE;QACzB,MAAM,EACF,UAAU,QAAQ,EAClB,aAAa,WAAW,EAC3B,GAAG;QACJ,IAAI,IAAI,CAAC,eAAe,IAAI;YACxB,IAAI,CAAC,eAAe,CAAC,aAAa,IAAI,CAAC,QAAQ,CAAC;QACpD;QACA,KAAK,CAAC,uBAAuB;IACjC;IACA,gBAAgB,KAAK,EAAE,IAAI,EAAE,UAAU,EAAE;QACrC,MAAM,uBAAuB,IAAI,CAAC,oBAAoB,CAAC;QACvD,MAAM,2BAA2B,WAAW,MAAM,CAAE,CAAA,YAAa,CAAC,MAAM,KAAK,cAAc,CAAC,YAAY,CAAC,OAAO,CAAC,UAAU,cAAc,CAAC,GAAG;QAC7I,IAAI,CAAC,YAAY,CAAC,sBAAsB;QACxC,IAAI,yBAAyB,MAAM,IAAI,CAAC,KAAK,cAAc,CAAC,QAAQ,EAAE;YAClE,MAAM,aAAa,wBAAwB,CAAC,EAAE;YAC9C,IAAI,CAAC,mBAAmB,CAAC,YAAY,IAAI,CAAC,eAAe,CAAC;QAC9D;QACA,IAAI,CAAC,mBAAmB,CAAC,OAAO,yBAAyB,MAAM;QAC/D,IAAI,KAAK,cAAc,CAAC,QAAQ,EAAE;YAC9B,qBAAqB,QAAQ,CAAC;QAClC;IACJ;IACA,yBAAyB,SAAS,EAAE,QAAQ,EAAE,WAAW,EAAE;QACvD,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG;YACxB,aAAa;YACb,WAAW;YACX,UAAU;YACV,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAC1C;IACJ;IACA,cAAc,KAAK,EAAE;QACjB,MAAM,QAAQ,CAAC;IACnB;IACA,oBAAoB,CAAC,EAAE;QACnB,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa,CAAC,UAAU;QACjD,IAAI,CAAC,aAAa,QAAQ,CAAC,UAAU;YACjC,IAAI,CAAC,oBAAoB,CAAC,EAAE,aAAa,EAAE,KAAK,GAAG;QACvD;IACJ;IACA,qBAAqB;QACjB,MAAM,oBAAoB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC;QACjE,MAAM,kBAAkB,IAAI,CAAC,cAAc;QAC3C,IAAI,CAAC,kBAAkB,CAAC;QACxB,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,iBAAiB,mBAAmB,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;IAChH;IACA,mBAAmB,cAAc,EAAE;QAC/B,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,gBAAgB,sBAAsB,IAAI,CAAC,aAAa;IAC7E;IACA,sBAAsB,IAAI,EAAE;QACxB,MAAM,QAAQ,YAAY,OAAO,uKAAA,CAAA,OAAc,GAAG,0KAAA,CAAA,OAAa;QAC/D,OAAO,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,OAAO;IAC/B;IACA,SAAS,UAAU,EAAE;QACjB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa;YACxB,OAAO;QACX;QACA,IAAI,WAAW,cAAc,EAAE;YAC3B,OAAO;QACX;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,aAAa;YACzB,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;QAC1C;QACA,MAAM,cAAc,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,YAAY,GAAG,CAAC;QACtC,IAAI,CAAC,aAAa;YACd,OAAO;QACX;QACA,IAAI,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC,cAAc;YACvC,OAAO,IAAI,CAAC,iBAAiB,CAAC;QAClC;QACA,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;IAC3C;IACA,kBAAkB,WAAW,EAAE;QAC3B,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,aAAa,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;QACrD,MAAM,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC;QAC1C,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;IAC1C;IACA,qBAAqB,WAAW,EAAE,KAAK,EAAE,CAAC,EAAE;QACxC,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;QAC3B,IAAI,CAAC,MAAM;YACP,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM,GAAG,OAAO;QACtC;QACA,IAAI,KAAK,cAAc,CAAC,QAAQ,EAAE;YAC9B,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM,GAAG,OAAO;QACtC;QACA,MAAM,eAAe,KAAK,cAAc,CAAC,QAAQ;QACjD,IAAI,iBAAiB,OAAO;YACxB,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;QACvC;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO;YACzB,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC;YACnC,IAAI,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,0BAA0B,yBAAyB,CAAC,EAAE,MAAM,EAAE;gBAC7E,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM,GAAG,OAAO;YACtC;YACA,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,wBAAwB,CAAC,QAAQ;gBACxD,IAAI,CAAC,oBAAoB,CAAC;YAC9B;QACJ;QACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACnB,QAAQ,CAAC;QACb;QACA,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE;QAC3D,OAAO,IAAI,CAAC,sBAAsB,CAAC,MAAM,OAAO;IACpD;IACA,yBAAyB,KAAK,EAAE;QAC5B,MAAM,iBAAiB,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,sBAAsB;QAChE,OAAO,eAAe,GAAG,CAAC,UAAU,MAAM;IAC9C;IACA,SAAS,KAAK,EAAE;QACZ,OAAO,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,CAAC;IAC/C;IACA,qBAAqB,KAAK,EAAE;QACxB,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,gBAAgB,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,4BAA4B,yJAAA,CAAA,UAAa,EAAE,CAAC,GAAG,QAAQ,GAAG,QAAQ,CAAC;QAC7G,MAAM,QAAQ,cAAc,QAAQ,CAAC,CAAC,CAAC,EAAE,6BAA6B,EAAE,EAAE,0BAA0B;QACpG,MAAM,IAAI;IACd;IACA,oBAAoB,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE;QAC/C,MAAM,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC;QAC7B,MAAM,QAAQ,CAAC;QACf,IAAI,KAAK,cAAc,CAAC,QAAQ,EAAE;YAC9B,MAAM,QAAQ,CAAC;QACnB;QACA,IAAI,CAAC,oCAAoC,CAAC,OAAO;IACrD;IACA,4BAA4B,KAAK,EAAE,IAAI,EAAE;QACrC,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC;QACpC,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,8BAA8B,QAAQ,CAAC;QACzE,IAAI,KAAK,cAAc,CAAC,QAAQ,EAAE;YAC9B,MAAM,QAAQ,CAAC;YACf,MAAM,MAAM,GAAG,QAAQ,CAAC;QAC5B;QACA,IAAI,KAAK,cAAc,CAAC,QAAQ,EAAE;YAC9B,MAAM,QAAQ,CAAC;QACnB;QACA,IAAI,CAAC,oCAAoC,CAAC,OAAO;IACrD;IACA,2BAA2B,KAAK,EAAE,IAAI,EAAE;QACpC,MAAM,EACF,YAAY,UAAU,EACtB,cAAc,YAAY,EAC7B,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,cAAc,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc;QACpD,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;QACxD,IAAI,CAAC,mBAAmB,CAAC,OAAO,MAAM,aAAa;QACnD,IAAI,CAAC,mBAAmB,CAAC,OAAO,MAAM,eAAe;QACrD,MAAM,iBAAiB,KAAK,cAAc,CAAC,QAAQ;QACnD,IAAI,gBAAgB;YAChB,MAAM,MAAM,GAAG,QAAQ,CAAC;QAC5B;QACA,IAAI,CAAC,0BAA0B,CAAC,aAAa,eAAe;IAChE;IACA,qCAAqC,KAAK,EAAE,IAAI,EAAE;QAC9C,MAAM,YAAY,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,uKAAA,CAAA,OAAc,EAAE,IAAI,CAAC,IAAI;QACxD,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,OAAO;QACxB,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,OAAO,WAAY,CAAA;YAC/B,IAAI,CAAC,oBAAoB,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE,KAAK,GAAG;YAC3D,OAAO;QACX;IACJ;IACA,2BAA2B,WAAW,EAAE,aAAa,EAAE,cAAc,EAAE;QACnE,cAAc,MAAM,CAAC;QACrB,YAAY,MAAM,CAAC,CAAC;IACxB;IACA,uBAAuB,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE;QACnC,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC;QACnC,MAAM,eAAe,CAAC,MAAM,MAAM,IAAI,SAAS,MAAM,EAAE,CAAC;QACxD,IAAI,IAAI,CAAC,MAAM,CAAC,2BAA2B,cAAc;YACrD,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,KAAK,cAAc,CAAC,SAAS;YAC9D,IAAI,YAAY;gBACZ,IAAI,CAAC,sBAAsB,CAAC,YAAY,OAAO;YACnD;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,uBAAuB,IAAI;YACjC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,OAAO,QAAQ,CAAC,CAAC,CAAC,EAAE,8BAA8B;YAC9E,MAAM,WAAW,CAAC,qCAAqC;QAC3D,OAAO,IAAI,IAAI,CAAC,wBAAwB,CAAC,QAAQ;YAC7C,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;YAC5B,MAAM,oBAAoB,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,0BAA0B;YACvE,MAAM,sBAAsB,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,4BAA4B;YAC3E,IAAI,CAAC,0BAA0B,CAAC,mBAAmB,qBAAqB;QAC5E;QACA,MAAM,iBAAiB,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,sBAAsB;QAChE,MAAM,sBAAsB,eAAe,MAAM,GAAG;QACpD,MAAM,qBAAqB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QAClC,IAAI,CAAC,SAAS,uBAAuB,CAAC,eAAe,EAAE,CAAC,WAAW;YAC/D,IAAI,CAAC,qBAAqB,CAAC,MAAM,OAAO,GAAG;YAC3C,OAAO,mBAAmB,OAAO;QACrC;QACA,IAAI,MAAM,KAAK,cAAc,CAAC,YAAY,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,wBAAwB,EAAE,GAAG;YAC7G,IAAI,CAAC,0BAA0B,CAAC,MAAM,OAAO,GAAG;YAChD,OAAO,mBAAmB,OAAO;QACrC;QACA,IAAI,CAAC,eAAe,CAAC,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC;QACtD,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,qBAAqB,CAAC,MAAM,OAAO,GAAG;QAC3C,OAAO,mBAAmB,OAAO;IACrC;IACA,2BAA2B,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,kBAAkB,EAAE;QAC3D,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC;QACnC,IAAI,CAAC,gBAAgB,CAAC,MAAM,IAAI,CAAE,CAAA;YAC9B,MAAM,iBAAiB,IAAI,CAAC,cAAc,CAAC;YAC3C,IAAI,CAAC,eAAe,CAAC,OAAO,gBAAgB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC;YAC9E,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,EAAE;gBACzB,mBAAmB,OAAO;gBAC1B;YACJ;YACA,IAAI,CAAC,uBAAuB;YAC5B,IAAI,CAAC,qBAAqB,CAAC,gBAAgB,OAAO,GAAG;QACzD;IACJ;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,IAAI,CAAC,wBAAwB,IAAI;YACjC,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;YACnD,OAAO,IAAI,CAAC,2BAA2B,CAAC,YAAY,IAAI,CAAE,CAAA;gBACtD,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW;oBACjC,IAAI,CAAC,YAAY,CAAC;gBACtB;YACJ;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI;YACxB,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,CAAC,EAAE,EAAE,OAAO;QACzC;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,GAAG;YAAC,IAAI,CAAC,MAAM,CAAC;YAAiB,KAAK,cAAc,CAAC,GAAG;SAAC;QAC9E,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;QAC3C,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG,IAAI,CAAE,CAAA;YACjC,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW;gBACjC,IAAI,CAAC,YAAY,CAAC;YACtB;QACJ;IACJ;IACA,gBAAgB,QAAQ,EAAE;QACtB,MAAM,gBAAgB,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE;QAC5C,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,WAAW;QAC9C,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,eAAe;IAC3D;IACA,aAAa,QAAQ,EAAE;QACnB,MAAM,EACF,OAAO,QAAQ,EAAE,EACpB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,MAAM,MAAM,CAAC;QACnC,IAAI,CAAC,gBAAgB;IACzB;IACA,sBAAsB,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,kBAAkB,EAAE;QACtD,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC;QACnC,MAAM,iBAAiB,MAAM,QAAQ,CAAC,CAAC,CAAC,EAAE,sBAAsB;QAChE,IAAI,QAAQ,sBAAsB,MAAM,eAAe,MAAM,EAAE;YAC3D,mBAAmB,OAAO;QAC9B;QACA,eAAe,QAAQ,CAAC;QACxB,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;QAC7B,uMAAA,CAAA,KAAE,CAAC,IAAI,CAAC,gBAAgB;QACxB,uMAAA,CAAA,KAAE,CAAC,OAAO,CAAC,gBAAgB;YACvB,MAAM;YACN,UAAU,IAAI,CAAC,MAAM,CAAC,sBAAsB,MAAM;YAClD,MAAM;gBACF,WAAW,QAAQ,IAAI;YAC3B;YACA,IAAI;gBACA,WAAW,QAAQ,aAAa;YACpC;YACA,UAAU,CAAA;gBACN,eAAe,GAAG,CAAC,aAAa;gBAChC,eAAe,WAAW,CAAC,6BAA6B;gBACxD,IAAI,CAAC,OAAO,CAAC,YAAY,OAAO;gBAChC,IAAI,CAAC,aAAa,GAAG,MAAM;gBAC3B,IAAI,CAAC,8BAA8B,CAAC,OAAO,MAAM;gBACjD,IAAI,oBAAoB;oBACpB,mBAAmB,OAAO;gBAC9B;YACJ,CAAA,EAAE,IAAI,CAAC,IAAI;QACf;IACJ;IACA,+BAA+B,UAAU,EAAE,IAAI,EAAE,CAAC,EAAE;QAChD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,CAAC,gCAAgC,EAAE;YACnE;QACJ;QACA,MAAM,aAAa,aAAa,mBAAmB;QACnD,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI;YACd,IAAI,CAAC,mBAAmB,CAAC,GAAG,YAAY;gBACpC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;YAC1C;QACJ,OAAO;YACH,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;YACpC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,YAAY;gBACvC,OAAO;gBACP,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;YAC1C;QACJ;IACJ;IACA,oBAAoB,KAAK,EAAE,WAAW,EAAE;QACpC,MAAM,iBAAiB,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,2BAA2B;QACjE,IAAI,eAAe,MAAM,EAAE;YACvB,IAAI;YACJ,SAAS,CAAC,wBAAwB,yJAAA,CAAA,UAAa,CAAC,WAAW,CAAC,eAAe,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,MAAM,CAAC,WAAW;QAChK;QACA,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC;QACpC,MAAM,cAAc,cAAc,QAAQ,CAAC,CAAC,CAAC,EAAE,2BAA2B,EAAE,EAAE,8BAA8B;QAC5G,IAAI,aAAa;YACb,YAAY,IAAI;YAChB;QACJ;QACA,YAAY,WAAW,CAAC;QACxB,MAAM,QAAQ,CAAC;IACnB;IACA,yBAAyB;QACrB,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,OAAO,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO,MAAM,KAAK,CAAC;IACxD;IACA,iBAAiB;QACb,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,SAAS,MAAM,MAAM,EAAE;YACvB,IAAI,CAAC,uBAAuB,GAAG;QACnC;QACA,KAAK,CAAC;IACV;IACA,qBAAqB,UAAU,EAAE;QAC7B,MAAM,EACF,eAAe,aAAa,EAC5B,mBAAmB,iBAAiB,EACvC,GAAG,IAAI,CAAC,MAAM;QACf,aAAa,cAAc,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,sBAAsB,EAAE,KAAK;QACjF,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC3C,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,aAAa;QAC7C,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,eAAe,EAAE,oJAAA,CAAA,UAAQ,EAAE;YAClD,OAAO;YACP,aAAa;gBACT,cAAc;YAClB;YACA,MAAM;YACN,mBAAmB;YACnB,gBAAgB,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,IAAI;YAC/D,eAAe,CAAA;gBACX,IAAI,EACA,WAAW,SAAS,EACvB,GAAG;gBACJ,UAAU,kBAAkB,CAAC,SAAU;oBACnC,UAAU,MAAM,CAAC,SAAS,CAAC,UAAU,MAAM,CAAC;gBAChD;YACJ;QACJ;QACA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,eAAe,EAAE;QAChD,WAAW,MAAM,CAAC,IAAI,CAAC,eAAe;IAC1C;IACA,iCAAiC,IAAI,EAAE;QACnC,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,0BAA0B,CAAC,KAAK,KAAK;IAC9C;IACA,iBAAiB,IAAI,EAAE;QACnB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,KAAK;QAC5C,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,qBAAqB;IAC9B;IACA,gBAAgB,KAAK,EAAE,IAAI,EAAE;QACzB,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACtC,IAAI,CAAC,gBAAgB,CAAC,WAAW,oJAAA,CAAA,UAAQ,EAAE;YACvC,OAAO,KAAK,cAAc,CAAC,QAAQ;YACnC,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI;YACnD,mBAAmB;YACnB,aAAa;gBACT,cAAc,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC7C;YACA,UAAU,IAAI,CAAC,eAAe,CAAC;QACnC;IACJ;IACA,qBAAqB,KAAK,EAAE,KAAK,EAAE;QAC/B,MAAM,WAAW,CAAC,qBAAqB,CAAC,CAAC;IAC7C;IACA,yBAAyB,IAAI,EAAE,KAAK,EAAE;QAClC,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC;QACnC,MAAM,QAAQ,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,EAAE,CAAC;QAC9C,IAAI,CAAC,YAAY,CAAC,uBAAuB,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE;QACnE,MAAM,WAAW,CAAC,qBAAqB,CAAC,CAAC;QACzC,IAAI,IAAI,CAAC,eAAe,IAAI;YACxB,MAAM,WAAW,IAAI,CAAC,oBAAoB,CAAC;YAC3C,SAAS,MAAM,CAAC,YAAY,CAAC,CAAC;QAClC;IACJ;IACA,mBAAmB,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE;QACtC,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAC7C,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,iBAAiB;YAC1C,IAAI,CAAC,wBAAwB,CAAC,MAAM;QACxC;IACJ;IACA,qBAAqB,CAAC,EAAE;QACpB,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;QACnD,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC;QAC5B,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC;QAC/B,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC;QACpC,MAAM,EACF,OAAO,KAAK,EACf,GAAG;QACJ,IAAI,QAAQ,KAAK,cAAc,CAAC,QAAQ,KAAK,OAAO;YAChD;QACJ;QACA,IAAI,CAAC,oBAAoB,CAAC,OAAO,MAAM,EAAE,KAAK;IAClD;IACA,qBAAqB;QACjB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,aAAa;IACxB;IACA,wBAAwB;QACpB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,IAAI,CAAC,MAAM,CAAC,2BAA2B,aAAa;IAC/D;IACA,sBAAsB,UAAU,EAAE,iBAAiB,EAAE,IAAI,EAAE;QACvD,MAAM,WAAW,kBAAkB,OAAO,CAAC,WAAW,GAAG;QACzD,IAAI,YAAY,GAAG;YACf,kBAAkB,MAAM,CAAC,UAAU;QACvC;QACA,IAAI,MAAM;YACN,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,WAAW,QAAQ,EAAG,CAAC,GAAG;gBAC3B,IAAI,CAAC,qBAAqB,CAAC,WAAW,mBAAmB;YAC7D;QACJ;QACA,IAAI,WAAW,MAAM,EAAE;YACnB,IAAI,CAAC,qBAAqB,CAAC,WAAW,MAAM,EAAE;QAClD;QACA,OAAO,MAAM,kBAAkB,MAAM;IACzC;IACA,gBAAgB,IAAI,EAAE;QAClB,MAAM,oBAAoB,IAAI,CAAC,MAAM,CAAC;QACtC,MAAM,eAAe,IAAI,CAAC,kBAAkB;QAC5C,MAAM,oBAAoB,IAAI,CAAC,mBAAmB;QAClD,IAAI,CAAC,mBAAmB;YACpB;QACJ;QACA,IAAI,cAAc;YACd,OAAO,MAAM,kBAAkB,MAAM;QACzC;QACA,OAAO,IAAI,CAAC,qBAAqB,CAAC,KAAK,cAAc,CAAC,UAAU,EAAE,kBAAkB,KAAK,IAAI;IACjG;IACA,qBAAqB,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE;QAC9C,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;QAC3B,IAAI,CAAC,QAAQ,UAAU,KAAK,OAAO,EAAE;YACjC,OAAO;QACX;QACA,IAAI,KAAK,cAAc,CAAC,QAAQ,KAAK,OAAO;YACxC,OAAO;QACX;QACA,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC,OAAO;YACtC,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC;gBACnC,IAAI,CAAC,oBAAoB,CAAC,OAAO,MAAM,CAAC,SAAS;YACrD;YACA,OAAO;QACX;QACA,IAAI,SAAS,IAAI,CAAC,kBAAkB,IAAI;YACpC,MAAM,eAAe,IAAI,CAAC,mBAAmB;YAC7C,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,cAAe,CAAC,OAAO;gBACxB,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK;gBACvC,IAAI,CAAC,cAAc;gBACnB,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC;YACjD;QACJ;QACA,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE;QAC3D,MAAM,gBAAgB,IAAI,CAAC,YAAY,CAAC,aAAa;QACrD,MAAM,2BAA2B,IAAI,CAAC,iBAAiB,MAAM,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,YAAY,MAAM,CAAC,aAAa;QAC7H,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,yBAAyB,CAAC,MAAM;QACrC,IAAI,CAAC,qBAAqB;QAC1B,IAAI,0BAA0B;YAC1B,IAAI,CAAC,0BAA0B,CAAC;QACpC;QACA,OAAO;IACX;IACA,0BAA0B,IAAI,EAAE,OAAO,EAAE;QACrC,MAAM,YAAY,WAAW,IAAI,CAAC,sBAAsB,CAAC,KAAK,cAAc,CAAC,IAAI;QACjF,MAAM,UAAU,UAAU,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB;QAC3E,QAAQ,IAAI,CAAC,IAAI,EAAE,WAAW,0BAA0B;YACpD,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;YACtC,UAAU,KAAK,cAAc,CAAC,IAAI;QACtC;IACJ;IACA,qBAAqB,KAAK,EAAE;QACxB,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC;QACpC,OAAO,cAAc,QAAQ,CAAC,gBAAgB,UAAU,CAAC;IAC7D;IACA,iBAAiB;QACb,MAAM,QAAQ,CAAC;QACf,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,CAAC,OAAO,IAAK,CAAC,GAAG;YACnC,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,MAAM;YACzC,MAAM,gBAAgB,KAAK,cAAc,CAAC,QAAQ;YAClD,IAAI,CAAC,MAAM,MAAM,EAAE;gBACf;YACJ;YACA,IAAI,CAAC,oBAAoB,CAAC,OAAO;YACjC,IAAI,CAAC,OAAO,CAAC,YAAY,eAAe;YACxC,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,CAAC,oBAAoB,CAAC,OAAO,MAAM,CAAC,SAAS;YACrD;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC1B,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC;YAC1D,kBAAkB,MAAM,CAAC,kBAAkB,KAAK;YAChD,kBAAkB,MAAM,CAAC,SAAS,IAAI,CAAC,YAAY,CAAC,aAAa;YACjE,kBAAkB,MAAM,CAAC,kBAAkB,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,IAAI;QAC9F;IACJ;IACA,oBAAoB,IAAI,EAAE,KAAK,EAAE;QAC7B,IAAI,CAAC,OAAO;YACR;QACJ;QACA,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC,KAAK,cAAc,CAAC,SAAS;QAC/E,MAAM,cAAc,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,CAAC,EAAE;QACxD,IAAI,IAAI,CAAC,eAAe,IAAI;YACxB,IAAI;YACJ,MAAM,cAAc,WAAW,cAAc,CAAC,QAAQ;YACtD,SAAS,CAAC,wBAAwB,IAAI,CAAC,oBAAoB,CAAC,YAAY,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,MAAM,CAAC,SAAS;YACvJ,IAAI,CAAC,oBAAoB,CAAC,aAAa;QAC3C;QACA,IAAI,WAAW,cAAc,CAAC,SAAS,KAAK,IAAI,CAAC,MAAM,CAAC,cAAc;YAClE,IAAI,CAAC,mBAAmB,CAAC,YAAY;QACzC;IACJ;IACA,sBAAsB,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE;QACjD,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY;QACrF,OAAO,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,iBAAiB,CAAC,eAAe;IAC/D;IACA,wBAAwB,CAAC,EAAE;QACvB,IAAI,CAAC,mBAAmB,CAAC,qBAAqB;IAClD;IACA,iBAAiB,CAAC,EAAE;QAChB,IAAI,CAAC,mBAAmB,CAAC,cAAc;IAC3C;IACA,oBAAoB,SAAS,EAAE,CAAC,EAAE;QAC9B,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC,EAAE,aAAa;QACnD,IAAI,CAAC,mBAAmB,CAAC,GAAG,WAAW;YACnC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAC1C;IACJ;IACA,aAAa;QACT,OAAO;IACX;IACA,eAAe;QACX,OAAO;IACX;IACA,oBAAoB;QAChB,MAAM,iBAAiB,IAAI,CAAC,cAAc;QAC1C,IAAI,CAAC,iBAAiB,CAAC;QACvB,MAAM,EACF,qBAAqB,mBAAmB,EACxC,cAAc,YAAY,EAC1B,2BAA2B,yBAAyB,EACpD,cAAc,YAAY,EAC7B,GAAG,IAAI,CAAC,sBAAsB;QAC/B,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,gBAAgB,qBAAqB,cAAe,CAAA;YAChE,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,uBAAuB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC,gBAAgB;gBACjF;YACJ;YACA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa;QAC/C;QACA,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,gBAAgB,2BAA2B,cAAe,CAAA;YACtE,IAAI,CAAC,uBAAuB,CAAC;QACjC;IACJ;IACA,kBAAkB,cAAc,EAAE;QAC9B,MAAM,EACF,qBAAqB,mBAAmB,EACxC,cAAc,YAAY,EAC1B,2BAA2B,yBAAyB,EACpD,cAAc,YAAY,EAC7B,GAAG,IAAI,CAAC,sBAAsB;QAC/B,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,gBAAgB,qBAAqB;QACtD,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,gBAAgB,2BAA2B;IAChE;IACA,yBAAyB;QACrB,MAAM,eAAe,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,IAAI;QAC5C,MAAM,eAAe,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE,uBAAuB;QAChE,MAAM,sBAAsB,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,uKAAA,CAAA,OAAc,EAAE,IAAI,CAAC,IAAI;QAClE,MAAM,4BAA4B,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yKAAA,CAAA,UAAa,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;QAC5E,OAAO;YACH,qBAAqB;YACrB,cAAc;YACd,2BAA2B;YAC3B,cAAc;QAClB;IACJ;IACA,WAAW,UAAU,EAAE;QACnB,MAAM,OAAO,WAAW,IAAI,CAAC,EAAE;QAC/B,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,MAAM;QACxD,MAAM,OAAO,OAAO,sBAAsB,CAAC,cAAc,CAAC,EAAE;QAC5D,IAAI,KAAK,QAAQ,CAAC,GAAG,IAAI,MAAM;YAC3B,KAAK,KAAK;QACd;IACJ;IACA,kBAAkB,CAAC,EAAE,KAAK,EAAE;QACxB,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;QACnC,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC;QACpC,IAAI,CAAC,mBAAmB,CAAC,GAAG,eAAe;YACvC,MAAM,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAC1C,GAAG;YACC,eAAe,IAAI,CAAC,UAAU;QAClC;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,EAAE,kBAAkB,IAAI;YACzD,IAAI,CAAC,oBAAoB,CAAC,CAAC,KAAK,cAAc,CAAC,QAAQ,EAAE,UAAU;QACvE;IACJ;IACA,4BAA4B,MAAM,EAAE,UAAU,EAAE;QAC5C,IAAI,YAAY;QAChB,MAAO,aAAa,EAAG;YACnB,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,CAAC,UAAU;YACjC,IAAI,CAAC,oBAAoB,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC;YACjE;QACJ;IACJ;IACA,2BAA2B,MAAM,EAAE,UAAU,EAAE;QAC3C,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,IAAI,YAAY;QAChB,MAAO,YAAY,OAAQ;YACvB,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,CAAC,UAAU;YACjC,IAAI,CAAC,oBAAoB,CAAC,MAAM,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC;YACjE;QACJ;IACJ;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC1B,uLAAA,CAAA,UAAY,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe,EAAE;YAC3C;QACJ;QACA,KAAK,CAAC;IACV;IACA,gBAAgB,CAAC,EAAE;QACf,IAAI,CAAC,iBAAiB,CAAC,GAAG;QAC1B,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM,EAAE,QAAQ,CAAC;QAC7C,IAAI,mBAAmB,IAAI,CAAC,MAAM,CAAC,mBAAmB;YAClD,aAAa,IAAI,CAAC,sBAAsB;YACxC,IAAI,CAAC,sBAAsB,GAAG,WAAY;gBACtC,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;gBACf,MAAM,UAAU,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,eAAe,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;gBAC7E,IAAI,CAAC,eAAe,CAAC;YACzB;YACA;QACJ;QACA,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;IACvF;IACA,wBAAwB,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB;YACnC;QACJ;QACA,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,WAAW,GAAG,EAAE,uBAAuB;QAC/E,IAAI,CAAC,QAAQ,MAAM,EAAE;YACjB;QACJ;QACA,MAAM,cAAc,QAAQ,QAAQ,CAAC,uBAAuB,OAAO;QACnE,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;IACnD;IACA,sBAAsB,MAAM,EAAE;QAC1B,OAAO,OAAO,GAAG,CAAE;YACf,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,EAAE,QAAQ,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,QAAQ,CAAC;QACvD;IACJ;IACA,WAAW,QAAQ,EAAE,CAAC,EAAE;QACpB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,gBAAgB,UAAU;QACzD,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,gBAAgB,SAAS;QACzD,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,sBAAsB,EAAE,IAAI,CAAE;YACnD,uMAAA,CAAA,KAAE,CAAC,IAAI,CAAC,IAAI,EAAE;QAClB;QACA,MAAM,SAAS,IAAI,CAAC,aAAa;QACjC,IAAI,CAAC,UAAU,CAAC,OAAO,MAAM,EAAE;YAC3B;QACJ;QACA,OAAQ;YACJ,KAAK;gBAAM;oBACP,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC;oBACjC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC/C,MAAM,kBAAkB,IAAI,CAAC,mBAAmB,CAAC;oBACjD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;oBACrC,IAAI,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI;wBACtC,IAAI,CAAC,oBAAoB,CAAC,MAAM;oBACpC;oBACA;gBACJ;YACA,KAAK;gBAAQ;oBACT,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC;oBACjC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC/C,MAAM,kBAAkB,IAAI,CAAC,mBAAmB,CAAC;oBACjD,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC;oBACrC,IAAI,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI;wBACtC,IAAI,CAAC,oBAAoB,CAAC,MAAM;oBACpC;oBACA;gBACJ;YACA,KAAK;gBAAS;oBACV,MAAM,aAAa,OAAO,KAAK;oBAC/B,IAAI,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI;wBACtC,IAAI,CAAC,2BAA2B,CAAC,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;oBACzE;oBACA,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC/C,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC;oBAC9D;gBACJ;YACA,KAAK;gBAAQ;oBACT,MAAM,YAAY,OAAO,IAAI;oBAC7B,IAAI,EAAE,QAAQ,IAAI,IAAI,CAAC,eAAe,IAAI;wBACtC,IAAI,CAAC,0BAA0B,CAAC,QAAQ,OAAO,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;oBACxE;oBACA,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;oBAC/C,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC;oBAC9D;gBACJ;YACA,KAAK;gBACD,IAAI,CAAC,uBAAuB;gBAC5B;YACJ,KAAK;gBACD,IAAI,CAAC,yBAAyB;gBAC9B;YACJ;gBACI,KAAK,CAAC,WAAW,KAAK,CAAC,IAAI,EAAE;QACrC;IACJ;IACA,oBAAoB,KAAK,EAAE;QACvB,OAAO,MAAM,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC;IAC5C;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,GAAG,CAAC;IACtD;IACA,0BAA0B;QACtB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACvB,IAAI,CAAC,aAAa,MAAM,IAAI,aAAa,QAAQ,CAAC,UAAU;YACxD;QACJ;QACA,MAAM,QAAQ,aAAa,IAAI,CAAC,CAAC,CAAC,EAAE,sBAAsB,EAAE,EAAE,CAAC;QAC/D,IAAI,MAAM,QAAQ,CAAC,8BAA8B;YAC7C,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,aAAa;YAC9E,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YAC/C,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC;YAC9D;QACJ;QACA,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;QAClD,IAAI,CAAC,oBAAoB,CAAC,MAAM;IACpC;IACA,2BAA2B,KAAK,EAAE;QAC9B,GAAG;YACC,QAAQ,MAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,EAAE,YAAY;QACnD,QAAS,MAAM,QAAQ,CAAC,uCAAuC,MAAM,CAAE;QACvE,OAAO;IACX;IACA,4BAA4B;QACxB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACvB,IAAI,CAAC,aAAa,MAAM,EAAE;YACtB;QACJ;QACA,MAAM,cAAc,aAAa,IAAI,CAAC,CAAC,CAAC,EAAE,sBAAsB,EAAE,EAAE,CAAC;QACrE,IAAI,CAAC,aAAa,QAAQ,CAAC,YAAY,YAAY,QAAQ,CAAC,8BAA8B;YACtF,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,QAAQ,CAAC;YAClD,IAAI,CAAC,oBAAoB,CAAC,MAAM;QACpC,OAAO;YACH,MAAM,gBAAgB,IAAI,CAAC,0BAA0B,CAAC;YACtD,cAAc,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YACvE,IAAI,CAAC,aAAa,GAAG,eAAe,CAAC,IAAI,CAAC,mBAAmB,CAAC;QAClE;IACJ;IACA,cAAc,KAAK,EAAE;QACjB,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,UAAU,SAAS;IAChD;IACA,cAAc,KAAK,EAAE;QACjB,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,UAAU,SAAS;IAChD;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,mBAAmB;QACf,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,IAAI,YAAY;YACZ,WAAW,MAAM,GAAG,IAAI,CAAE;gBACtB,SAAS,WAAW,CAAC,IAAI;YAC7B;QACJ,OAAO;YACH,SAAS,WAAW,CAAC,IAAI;QAC7B;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,WAAW,WAAW,EAAE;QACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM;IAC3C;IACA,aAAa,WAAW,EAAE;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC,OAAO;IAC5C;IACA,WAAW,WAAW,EAAE;QACpB,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa;IAClD;IACA,aAAa,WAAW,EAAE;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa;IAClD;IACA,WAAW;QACP,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY;IACzC;IACA,mBAAmB;QACf,OAAO,IAAI,CAAC,mBAAmB,GAAG,GAAG,CAAE,CAAA;YACnC,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,YAAY,CAAC;YAC5C,OAAO,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QAC3C;IACJ;IACA,sBAAsB;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,oBAAoB;IACjD;IACA,YAAY;QACR,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC1B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,YAAY,MAAM,CAAC,SAAS;QAChE,OAAO;YACH,IAAI,CAAC,gBAAgB,CAAC;gBAClB,OAAO;YACX;QACJ;IACJ;IACA,cAAc;QACV,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC1B,IAAI,CAAC,eAAe,CAAC,UAAU,CAAC,YAAY,MAAM,CAAC,SAAS;QAChE,OAAO;YACH,IAAI,CAAC,gBAAgB,CAAC;gBAClB,OAAO;YACX;QACJ;IACJ;IACA,2BAA2B;QACvB,IAAI,CAAC,gCAAgC,GAAG;QACxC,IAAI,CAAC,uBAAuB;IAChC;IACA,YAAY;QACR,MAAM,QAAQ,IAAI,CAAC,YAAY,CAAC,OAAO;QACvC,MAAM,oBAAoB,EAAE;QAC5B,IAAI,CAAC,gCAAgC,GAAG;QACxC,MAAM,OAAO,CAAE,CAAA,OAAQ,kBAAkB,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,KAAK,cAAc,CAAC,GAAG,EAAE;QACjG,QAAQ,UAAU,CAAC,mBAAmB,IAAI,CAAE;YACxC,IAAI;YACJ,OAAO,SAAS,CAAC,wBAAwB,IAAI,CAAC,wBAAwB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,CAAC,IAAI;QAC1J;IACJ;IACA,cAAc;QACV,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,CAAC,oBAAoB,IAAK,CAAC,GAAG;YAChD,IAAI,CAAC,oBAAoB,CAAC,KAAK;QACnC;IACJ;IACA,aAAa,kBAAkB,EAAE;QAC7B,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC;QAC3B,IAAI,CAAC,MAAM;YACP,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM,GAAG,OAAO;QACtC;QACA,MAAM,mBAAmB,EAAE;QAC3B,IAAI,aAAa,KAAK,cAAc,CAAC,UAAU,CAAC,MAAM;QACtD,MAAO,QAAQ,WAAY;YACvB,IAAI,CAAC,WAAW,QAAQ,EAAE;gBACtB,iBAAiB,IAAI,CAAC,WAAW,GAAG;YACxC;YACA,aAAa,WAAW,MAAM;QAClC;QACA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QAC9B,IAAI,CAAC,YAAY,CAAC,iBAAiB,OAAO,IAAI,MAAM,CAAE;YAClD,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;YACtC,IAAI,YAAY,SAAS,MAAM,EAAE;gBAC7B,IAAI,CAAC,sBAAsB,CAAC,SAAS,GAAG,CAAC;gBACzC,eAAe,OAAO;YAC1B,OAAO;gBACH,eAAe,MAAM;YACzB;QACJ;QACA,OAAO,eAAe,OAAO;IACjC;IACA,uBAAuB,aAAa,EAAE;QAClC,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,EACF,iBAAiB,eAAe,EAChC,YAAY,UAAU,EACzB,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,iBAAiB;YACnB,KAAK;YACL,MAAM;QACV;QACA,MAAM,iBAAiB,CAAA,GAAA,uMAAA,CAAA,oBAAiB,AAAD,EAAE,iLAAA,CAAA,2BAAwB,EAAE;QACnE,IAAI,oBAAoB,iLAAA,CAAA,qBAAkB,EAAE;YACxC,MAAM,mBAAmB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,SAAS,IAAI,GAAG,CAAC;YACvD,eAAe,IAAI,GAAG,aAAa,eAAe,IAAI,GAAG,cAAc,WAAW,GAAG,iBAAiB,WAAW,GAAG,eAAe,IAAI;QAC3I;QACA,IAAI,oBAAoB,iLAAA,CAAA,uBAAoB,EAAE;YAC1C,eAAe,GAAG,GAAG,eAAe,GAAG;QAC3C;QACA,WAAW,QAAQ,CAAC;IACxB;IACA,aAAa,YAAY,EAAE;QACvB,IAAI,CAAC,gBAAgB,MAAM,aAAa,MAAM,EAAE;YAC5C,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;QACvC;QACA,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QAC9B,MAAM,mBAAmB,aAAa,GAAG,CAAE,CAAA,MAAO,IAAI,CAAC,UAAU,CAAC;QAClE,iLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,qJAAA,CAAA,UAAC,EAAE,kBAAkB,IAAI,CAAE,IAAM,eAAe,OAAO,IAAK,IAAI,CAAE,IAAM,eAAe,MAAM;QACxG,OAAO,eAAe,OAAO;IACjC;IACA,WAAW;QACP,KAAK,CAAC;QACN,aAAa,IAAI,CAAC,sBAAsB;QACxC,IAAI,CAAC,wBAAwB,GAAG;IACpC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/tree_view/m_tree_view.search.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/tree_view/m_tree_view.search.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport registerComponent from \"../../../core/component_registrator\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport TextBox from \"../../../ui/text_box\";\r\nimport searchBoxMixin from \"../../../ui/widget/ui.search_box_mixin\";\r\nimport TreeViewBase from \"./m_tree_view.base\";\r\nsearchBoxMixin.setEditorClass(TextBox);\r\nconst WIDGET_CLASS = \"dx-treeview\";\r\nconst NODE_CONTAINER_CLASS = `${WIDGET_CLASS}-node-container`;\r\nconst TreeViewSearch = TreeViewBase.inherit(searchBoxMixin).inherit({\r\n    _addWidgetPrefix: className => `${WIDGET_CLASS}-${className}`,\r\n    _optionChanged(args) {\r\n        switch (args.name) {\r\n            case \"searchValue\":\r\n                if (this._showCheckboxes() && this._isRecursiveSelection()) {\r\n                    this._removeSelection()\r\n                }\r\n                this._initDataAdapter();\r\n                this._updateSearch();\r\n                this._repaintContainer();\r\n                this.option(\"focusedElement\", null);\r\n                break;\r\n            case \"searchExpr\":\r\n                this._initDataAdapter();\r\n                this.repaint();\r\n                break;\r\n            case \"searchMode\":\r\n                this.option(\"expandNodesRecursive\") ? this._updateDataAdapter() : this._initDataAdapter();\r\n                this.repaint();\r\n                break;\r\n            default:\r\n                this.callBase(args)\r\n        }\r\n    },\r\n    _updateDataAdapter() {\r\n        this._setOptionWithoutOptionChange(\"expandNodesRecursive\", false);\r\n        this._initDataAdapter();\r\n        this._setOptionWithoutOptionChange(\"expandNodesRecursive\", true)\r\n    },\r\n    _getDataAdapterOptions() {\r\n        return extend(this.callBase(), {\r\n            searchValue: this.option(\"searchValue\"),\r\n            searchMode: this.option(\"searchMode\") || \"contains\",\r\n            searchExpr: this.option(\"searchExpr\")\r\n        })\r\n    },\r\n    _getNodeContainer() {\r\n        return this.$element().find(`.${NODE_CONTAINER_CLASS}`).first()\r\n    },\r\n    _updateSearch() {\r\n        if (this._searchEditor) {\r\n            const editorOptions = this._getSearchEditorOptions();\r\n            this._searchEditor.option(editorOptions)\r\n        }\r\n    },\r\n    _repaintContainer() {\r\n        const $container = this._getNodeContainer();\r\n        let rootNodes;\r\n        if ($container.length) {\r\n            $container.empty();\r\n            rootNodes = this._dataAdapter.getRootNodes();\r\n            this._renderEmptyMessage(rootNodes);\r\n            this._renderItems($container, rootNodes);\r\n            this._fireContentReadyAction()\r\n        }\r\n    },\r\n    _focusTarget() {\r\n        return this._itemContainer(this.option(\"searchEnabled\"))\r\n    },\r\n    _cleanItemContainer() {\r\n        this.$element().empty()\r\n    },\r\n    _itemContainer(isSearchMode, selectAllEnabled) {\r\n        selectAllEnabled ?? (selectAllEnabled = this._selectAllEnabled());\r\n        if (selectAllEnabled) {\r\n            return this._getNodeContainer()\r\n        }\r\n        if (this._scrollable && isSearchMode) {\r\n            return $(this._scrollable.content())\r\n        }\r\n        return this.callBase()\r\n    },\r\n    _addWidgetClass() {\r\n        this.$element().addClass(this._widgetClass())\r\n    },\r\n    _clean() {\r\n        this.callBase();\r\n        this._removeSearchBox()\r\n    }\r\n});\r\nregisterComponent(\"dxTreeView\", TreeViewSearch);\r\nexport default TreeViewSearch;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AAAA;AAGA;AACA;AACA;;;;;;;AACA,2KAAA,CAAA,UAAc,CAAC,cAAc,CAAC,mJAAA,CAAA,UAAO;AACrC,MAAM,eAAe;AACrB,MAAM,uBAAuB,GAAG,aAAa,eAAe,CAAC;AAC7D,MAAM,iBAAiB,4LAAA,CAAA,UAAY,CAAC,OAAO,CAAC,2KAAA,CAAA,UAAc,EAAE,OAAO,CAAC;IAChE,kBAAkB,CAAA,YAAa,GAAG,aAAa,CAAC,EAAE,WAAW;IAC7D,gBAAe,IAAI;QACf,OAAQ,KAAK,IAAI;YACb,KAAK;gBACD,IAAI,IAAI,CAAC,eAAe,MAAM,IAAI,CAAC,qBAAqB,IAAI;oBACxD,IAAI,CAAC,gBAAgB;gBACzB;gBACA,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,aAAa;gBAClB,IAAI,CAAC,iBAAiB;gBACtB,IAAI,CAAC,MAAM,CAAC,kBAAkB;gBAC9B;YACJ,KAAK;gBACD,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,OAAO;gBACZ;YACJ,KAAK;gBACD,IAAI,CAAC,MAAM,CAAC,0BAA0B,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,gBAAgB;gBACvF,IAAI,CAAC,OAAO;gBACZ;YACJ;gBACI,IAAI,CAAC,QAAQ,CAAC;QACtB;IACJ;IACA;QACI,IAAI,CAAC,6BAA6B,CAAC,wBAAwB;QAC3D,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,6BAA6B,CAAC,wBAAwB;IAC/D;IACA;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;YAC3B,aAAa,IAAI,CAAC,MAAM,CAAC;YACzB,YAAY,IAAI,CAAC,MAAM,CAAC,iBAAiB;YACzC,YAAY,IAAI,CAAC,MAAM,CAAC;QAC5B;IACJ;IACA;QACI,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,sBAAsB,EAAE,KAAK;IACjE;IACA;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,MAAM,gBAAgB,IAAI,CAAC,uBAAuB;YAClD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC9B;IACJ;IACA;QACI,MAAM,aAAa,IAAI,CAAC,iBAAiB;QACzC,IAAI;QACJ,IAAI,WAAW,MAAM,EAAE;YACnB,WAAW,KAAK;YAChB,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY;YAC1C,IAAI,CAAC,mBAAmB,CAAC;YACzB,IAAI,CAAC,YAAY,CAAC,YAAY;YAC9B,IAAI,CAAC,uBAAuB;QAChC;IACJ;IACA;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC;IAC3C;IACA;QACI,IAAI,CAAC,QAAQ,GAAG,KAAK;IACzB;IACA,gBAAe,YAAY,EAAE,gBAAgB;QACzC,oBAAoB,CAAC,mBAAmB,IAAI,CAAC,iBAAiB,EAAE;QAChE,IAAI,kBAAkB;YAClB,OAAO,IAAI,CAAC,iBAAiB;QACjC;QACA,IAAI,IAAI,CAAC,WAAW,IAAI,cAAc;YAClC,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO;QACrC;QACA,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;QACI,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,IAAI,CAAC,YAAY;IAC9C;IACA;QACI,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,gBAAgB;IACzB;AACJ;AACA,CAAA,GAAA,kKAAA,CAAA,UAAiB,AAAD,EAAE,cAAc;uCACjB", "ignoreList": [0], "debugId": null}}]}