"use client";

import { useState } from "react";
import Form, {
  SimpleItem,
  GroupItem,
  Label,
  RequiredRule,
  EmailRule,
  CompareRule,
} from "devextreme-react/form";
import Button from "devextreme-react/button";
import { useAuth } from "@/hooks/useAuth";
import PermissionGuard from "@/components/PermissionGuard";
import { hasPermission } from "@/lib/permissions";

interface Profile {
  firstName: string;
  lastName: string;
  email: string;
  currentPassword: string;
  newPassword: string;
  confirmNewPassword: string;
}

export default function ProfilePage() {
  const { user } = useAuth();
  const [profile, setProfile] = useState<Profile>({
    firstName: "John",
    lastName: "Doe",
    email: user?.email || "",
    currentPassword: "",
    newPassword: "",
    confirmNewPassword: "",
  });
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccessMessage("");

    try {
      // In a real application, this would be an API call
      // await api.updateProfile(profile);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setSuccessMessage("Profil sikeresen frissítve!");
    } catch (error) {
      console.error("Failed to update profile:", error);
      setSuccessMessage("Hiba történt a profil frissítése során.");
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (e: any) => {
    const field = e.dataField as string;
    const value = e.value;
    setProfile({ ...profile, [field]: value });
  };

  return (
    <PermissionGuard permission="edit_profile">
      <div className="content-block">
        <h2 className="content-block-title">Profil</h2>

        <form onSubmit={handleSubmit}>
          <Form
            formData={profile}
            onFieldDataChanged={handleFieldChange}
            labelLocation="top"
            colCount={2}
          >
            <GroupItem caption="Személyes adatok">
              <SimpleItem dataField="firstName" editorType="dxTextBox">
                <Label text="Keresztnév" />
                <RequiredRule message="A keresztnév megadása kötelező" />
              </SimpleItem>

              <SimpleItem dataField="lastName" editorType="dxTextBox">
                <Label text="Vezetéknév" />
                <RequiredRule message="A vezetéknév megadása kötelező" />
              </SimpleItem>

              <SimpleItem dataField="email" editorType="dxTextBox">
                <Label text="Email" />
                <EmailRule message="Érvényes email cím megadása kötelező" />
              </SimpleItem>
            </GroupItem>

            <GroupItem caption="Jelszó módosítása">
              <SimpleItem
                dataField="currentPassword"
                editorType="dxTextBox"
                editorOptions={{ mode: "password" }}
              >
                <Label text="Jelenlegi jelszó" />
              </SimpleItem>

              <SimpleItem
                dataField="newPassword"
                editorType="dxTextBox"
                editorOptions={{ mode: "password" }}
              >
                <Label text="Új jelszó" />
              </SimpleItem>

              <SimpleItem
                dataField="confirmNewPassword"
                editorType="dxTextBox"
                editorOptions={{ mode: "password" }}
              >
                <Label text="Új jelszó megerősítése" />
                <CompareRule
                  message="A jelszavak nem egyeznek"
                  comparisonTarget={() => profile.newPassword}
                  comparisonType="=="
                />
              </SimpleItem>
            </GroupItem>
          </Form>

          <div
            style={{
              marginTop: "20px",
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            {successMessage && (
              <div
                style={{
                  marginRight: "20px",
                  padding: "10px",
                  backgroundColor: successMessage.includes("sikeresen")
                    ? "#d4edda"
                    : "#f8d7da",
                  color: successMessage.includes("sikeresen")
                    ? "#155724"
                    : "#721c24",
                  borderRadius: "4px",
                }}
              >
                {successMessage}
              </div>
            )}
            <Button
              text="Mentés"
              type="success"
              useSubmitBehavior={true}
              disabled={loading}
            />
          </div>
        </form>
      </div>
    </PermissionGuard>
  );
}
