﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DevExpress.Xpo;

namespace omsnext.shared.Models
{
    public class User : XPObject
    {
        public User(Session session) : base(session) { }

        [Indexed(Unique = true)]
        public string Email { get; set; }
        public string PasswordHash { get; set; }
        public string DisplayName { get; set; }
        public bool IsActive { get; set; } = true;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

        [Association("User-Roles")]
        public XPCollection<Role> Roles => GetCollection<Role>(nameof(Roles));
    }

    public class Role : XPObject
    {
        public Role(Session session) : base(session) { }
        public string Name { get; set; }
        [Association("Role-Permissions")]
        public XPCollection<Permission> Permissions => GetCollection<Permission>(nameof(Permissions));
        [Association("User-Roles")]
        public XPCollection<User> Users => GetCollection<User>(nameof(Users));
    }

    public class Permission : XPObject
    {
        public Permission(Session session) : base(session) { }
        public string Name { get; set; }
        [Association("Role-Permissions")]
        public XPCollection<Role> Roles => GetCollection<Role>(nameof(Roles));
    }
}
