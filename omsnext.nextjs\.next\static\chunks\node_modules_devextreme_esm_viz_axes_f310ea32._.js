(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme/esm/viz/axes/smart_formatter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/smart_formatter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "formatRange": ()=>formatRange,
    "smartFormatter": ()=>smartFormatter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/format_helper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
;
;
;
;
;
const _format = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format;
const { abs: abs, floor: floor } = Math;
const EXPONENTIAL = "exponential";
const formats = [
    "fixedPoint",
    "thousands",
    "millions",
    "billions",
    "trillions",
    EXPONENTIAL
];
const dateUnitIntervals = [
    "millisecond",
    "second",
    "minute",
    "hour",
    "day",
    "month",
    "year"
];
const INTERVALS_MAP = {
    week: "day",
    quarter: "month",
    shorttime: "hour",
    longtime: "second"
};
function patchFirstTickDiff(differences, tickFormatIndex) {
    for(let i = tickFormatIndex; i < dateUnitIntervals.length - 1; i++){
        const dateUnitInterval = dateUnitIntervals[i];
        if (i === tickFormatIndex) {
            setDateUnitInterval(differences, tickFormatIndex + (differences.millisecond ? 2 : 1));
            break;
        } else if (differences[dateUnitInterval] && differences.count > 1) {
            resetDateUnitInterval(differences, i);
            break;
        }
    }
}
function patchTickDiff(differences, tickFormatIndex) {
    let patched = false;
    for(let i = dateUnitIntervals.length - 1; i >= tickFormatIndex; i--){
        const dateUnitInterval = dateUnitIntervals[i];
        if (differences[dateUnitInterval]) {
            if (i - tickFormatIndex > 1) {
                for(let j = 0; j <= tickFormatIndex; j++){
                    resetDateUnitInterval(differences, j);
                    patched = true;
                }
                break;
            }
        }
    }
    return patched;
}
function getDatesDifferences(prevDate, curDate, nextDate, tickIntervalFormat) {
    tickIntervalFormat = INTERVALS_MAP[tickIntervalFormat] || tickIntervalFormat;
    const tickFormatIndex = dateUnitIntervals.indexOf(tickIntervalFormat);
    if (nextDate) {
        const nextDifferences = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDatesDifferences(curDate, nextDate);
        if (nextDifferences[tickIntervalFormat]) {
            patchFirstTickDiff(nextDifferences, tickFormatIndex);
        }
        return nextDifferences;
    } else {
        const prevDifferences = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDatesDifferences(prevDate, curDate);
        const patched = patchTickDiff(prevDifferences, tickFormatIndex);
        if (!patched && 1 === prevDifferences.count) {
            setDateUnitInterval(prevDifferences, tickFormatIndex);
        }
        return prevDifferences;
    }
}
function resetDateUnitInterval(differences, intervalIndex) {
    const dateUnitInterval = dateUnitIntervals[intervalIndex];
    if (differences[dateUnitInterval]) {
        differences[dateUnitInterval] = false;
        differences.count--;
    }
}
function setDateUnitInterval(differences, intervalIndex) {
    const dateUnitInterval = dateUnitIntervals[intervalIndex];
    if (false === differences[dateUnitInterval]) {
        differences[dateUnitInterval] = true;
        differences.count++;
    }
}
function getNoZeroIndex(str) {
    return str.length - parseInt(str).toString().length;
}
function getTransitionTickIndex(ticks, value) {
    let i;
    let curDiff;
    let minDiff;
    let nearestTickIndex = 0;
    minDiff = abs(value - ticks[0]);
    for(i = 1; i < ticks.length; i++){
        curDiff = abs(value - ticks[i]);
        if (curDiff < minDiff) {
            minDiff = curDiff;
            nearestTickIndex = i;
        }
    }
    return nearestTickIndex;
}
function splitDecimalNumber(value) {
    return value.toString().split(".");
}
function createFormat(type) {
    let formatter;
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(type)) {
        formatter = type;
        type = null;
    }
    return {
        type: type,
        formatter: formatter
    };
}
function formatLogarithmicNumber(tick) {
    const log10Tick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAdjustedLog10"])(abs(tick));
    let type;
    if (log10Tick > 0) {
        type = formats[floor(log10Tick / 3)] || EXPONENTIAL;
    } else if (log10Tick < -4) {
        type = EXPONENTIAL;
    } else {
        return _format((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(tick));
    }
    return _format(tick, {
        type: type,
        precision: 0
    });
}
function getDateTimeFormat(tick, _ref) {
    let { showTransition: showTransition, ticks: ticks, tickInterval: tickInterval } = _ref;
    let typeFormat = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByTickInterval(tickInterval);
    let prevDateIndex;
    let nextDateIndex;
    if (showTransition && ticks.length) {
        const indexOfTick = ticks.map(Number).indexOf(+tick);
        if (1 === ticks.length && 0 === indexOfTick) {
            typeFormat = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByTicks(ticks);
        } else {
            if (-1 === indexOfTick) {
                prevDateIndex = getTransitionTickIndex(ticks, tick);
            } else {
                prevDateIndex = 0 === indexOfTick ? ticks.length - 1 : indexOfTick - 1;
                nextDateIndex = 0 === indexOfTick ? 1 : -1;
            }
            const datesDifferences = getDatesDifferences(ticks[prevDateIndex], tick, ticks[nextDateIndex], typeFormat);
            typeFormat = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByDifferences(datesDifferences, typeFormat);
        }
    }
    return createFormat(typeFormat);
}
function getFormatExponential(tick, tickInterval) {
    const stringTick = abs(tick).toString();
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExponential"])(tick)) {
        return Math.max(abs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getExponent"])(tick) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getExponent"])(tickInterval)), abs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrecision"])(tick) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrecision"])(tickInterval)));
    } else {
        return abs(getNoZeroIndex(stringTick.split(".")[1]) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getExponent"])(tickInterval) + 1);
    }
}
function getFormatWithModifier(tick, tickInterval) {
    const tickIntervalIndex = floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAdjustedLog10"])(tickInterval));
    let tickIndex;
    let precision = 0;
    let actualIndex = tickIndex = floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAdjustedLog10"])(abs(tick)));
    if (tickIndex - tickIntervalIndex >= 2) {
        actualIndex = tickIntervalIndex;
    }
    let indexOfFormat = floor(actualIndex / 3);
    const offset = 3 * indexOfFormat;
    if (indexOfFormat < 0) {
        indexOfFormat = 0;
    }
    const typeFormat = formats[indexOfFormat] || formats[formats.length - 1];
    if (offset > 0) {
        const separatedTickInterval = splitDecimalNumber(tickInterval / Math.pow(10, offset));
        if (separatedTickInterval[1]) {
            precision = separatedTickInterval[1].length;
        }
    }
    return {
        precision: precision,
        type: typeFormat
    };
}
function getHighDiffFormat(diff) {
    let stop = false;
    for(const i in diff){
        if (true === diff[i] || "hour" === i || stop) {
            diff[i] = false;
            stop = true;
        } else if (false === diff[i]) {
            diff[i] = true;
        }
    }
    return createFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByDifferences(diff));
}
function getHighAndSelfDiffFormat(diff, interval) {
    let stop = false;
    for(const i in diff){
        if (stop) {
            diff[i] = false;
        } else if (i === interval) {
            stop = true;
        } else {
            diff[i] = true;
        }
    }
    return createFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByDifferences(diff));
}
function formatDateRange(startValue, endValue, tickInterval) {
    const diff = getDatesDifferences(startValue, endValue);
    const typeFormat = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByTickInterval(tickInterval);
    const diffFormatType = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByDifferences(diff, typeFormat);
    const diffFormat = createFormat(diffFormatType);
    const values = [];
    if (tickInterval in diff) {
        const rangeFormat = getHighAndSelfDiffFormat(getDatesDifferences(startValue, endValue), tickInterval);
        const value = _format(startValue, rangeFormat);
        if (value) {
            values.push(value);
        }
    } else {
        const rangeFormat = getHighDiffFormat(getDatesDifferences(startValue, endValue));
        const highValue = _format(startValue, rangeFormat);
        if (highValue) {
            values.push(highValue);
        }
        values.push("".concat(_format(startValue, diffFormat), " - ").concat(_format(endValue, diffFormat)));
    }
    return values.join(", ");
}
function processDateInterval(interval) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isObject"])(interval)) {
        const dateUnits = Object.keys(interval);
        const sum = dateUnits.reduce((sum, k)=>interval[k] + sum, 0);
        if (1 === sum) {
            const dateUnit = dateUnits.filter((k)=>1 === interval[k])[0];
            return dateUnit.slice(0, dateUnit.length - 1);
        }
    }
    return interval;
}
function smartFormatter(tick, options) {
    let tickInterval = options.tickInterval;
    const stringTick = abs(tick).toString();
    let format = options.labelOptions.format;
    const ticks = options.ticks;
    const isLogarithmic = "logarithmic" === options.type;
    if (1 === ticks.length && 0 === ticks.indexOf(tick) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tickInterval)) {
        tickInterval = abs(tick) >= 1 ? 1 : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(1 - abs(tick), tick);
    }
    if (Object.is(tick, -0)) {
        tick = 0;
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(format) && "discrete" !== options.type && tick && (10 === options.logarithmBase || !isLogarithmic)) {
        if ("datetime" !== options.dataType && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tickInterval)) {
            if (ticks.length && -1 === ticks.indexOf(tick)) {
                const indexOfTick = getTransitionTickIndex(ticks, tick);
                tickInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(abs(tick - ticks[indexOfTick]), tick);
            }
            if (isLogarithmic) {
                return formatLogarithmicNumber(tick);
            } else {
                let separatedTickInterval = splitDecimalNumber(tickInterval);
                if (separatedTickInterval < 2) {
                    separatedTickInterval = splitDecimalNumber(tick);
                }
                if (separatedTickInterval.length > 1 && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExponential"])(tickInterval)) {
                    format = {
                        type: formats[0],
                        precision: separatedTickInterval[1].length
                    };
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExponential"])(tickInterval) && (-1 !== stringTick.indexOf(".") || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isExponential"])(tick))) {
                    format = {
                        type: EXPONENTIAL,
                        precision: getFormatExponential(tick, tickInterval)
                    };
                } else {
                    format = getFormatWithModifier(tick, tickInterval);
                }
            }
        } else if ("datetime" === options.dataType) {
            format = getDateTimeFormat(tick, options);
        }
    }
    return _format(tick, format);
}
function formatRange(_ref2) {
    let { startValue: startValue, endValue: endValue, tickInterval: tickInterval, argumentFormat: argumentFormat, axisOptions: { dataType: dataType, type: type, logarithmBase: logarithmBase } } = _ref2;
    if ("discrete" === type) {
        return "";
    }
    if ("datetime" === dataType) {
        return formatDateRange(startValue, endValue, processDateInterval(tickInterval));
    }
    const formatOptions = {
        ticks: [],
        type: type,
        dataType: dataType,
        tickInterval: tickInterval,
        logarithmBase: logarithmBase,
        labelOptions: {
            format: argumentFormat
        }
    };
    return "".concat(smartFormatter(startValue, formatOptions), " - ").concat(smartFormatter(endValue, formatOptions));
}
}),
"[project]/node_modules/devextreme/esm/viz/axes/axes_constants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/axes_constants.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    logarithmic: "logarithmic",
    discrete: "discrete",
    numeric: "numeric",
    left: "left",
    right: "right",
    top: "top",
    bottom: "bottom",
    center: "center",
    horizontal: "horizontal",
    vertical: "vertical",
    convertTicksToValues: function(ticks) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(ticks || [], function(item) {
            return item.value;
        });
    },
    validateOverlappingMode: function(mode) {
        return "ignore" === mode || "none" === mode ? mode : "hide";
    },
    getTicksCountInRange: function(ticks, valueKey, range) {
        let i = 1;
        if (ticks.length > 1) {
            for(; i < ticks.length; i++){
                if (Math.abs(ticks[i].coords[valueKey] - ticks[0].coords[valueKey]) >= range) {
                    break;
                }
            }
        }
        return i;
    },
    areLabelsOverlap: function(bBox1, bBox2, spacing, alignment) {
        const horizontalInverted = bBox1.x > bBox2.x;
        const verticalInverted = bBox1.y > bBox2.y;
        let x1 = bBox1.x;
        let x2 = bBox2.x;
        const width1 = bBox1.width;
        const width2 = bBox2.width;
        if ("left" === alignment) {
            x1 += width1 / 2;
            x2 += width2 / 2;
        } else if ("right" === alignment) {
            x1 -= width1 / 2;
            x2 -= width2 / 2;
        }
        const hasHorizontalOverlapping = horizontalInverted ? x2 + width2 + spacing > x1 : x1 + width1 + spacing > x2;
        const hasVerticalOverlapping = verticalInverted ? bBox2.y + bBox2.height > bBox1.y : bBox1.y + bBox1.height > bBox2.y;
        return hasHorizontalOverlapping && hasVerticalOverlapping;
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/axes/tick_generator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/tick_generator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "tickGenerator": ()=>tickGenerator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
;
;
;
;
const convertDateUnitToMilliseconds = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertDateUnitToMilliseconds;
const dateToMilliseconds = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds;
const math = Math;
const mathAbs = math.abs;
const mathFloor = math.floor;
const mathCeil = math.ceil;
const mathPow = math.pow;
const NUMBER_MULTIPLIERS = [
    1,
    2,
    2.5,
    5
];
const LOGARITHMIC_MULTIPLIERS = [
    1,
    2,
    3,
    5
];
const DATETIME_MULTIPLIERS = {
    millisecond: [
        1,
        2,
        5,
        10,
        25,
        50,
        100,
        250,
        500
    ],
    second: [
        1,
        2,
        3,
        5,
        10,
        15,
        20,
        30
    ],
    minute: [
        1,
        2,
        3,
        5,
        10,
        15,
        20,
        30
    ],
    hour: [
        1,
        2,
        3,
        4,
        6,
        8,
        12
    ],
    day: [
        1,
        2
    ],
    week: [
        1,
        2
    ],
    month: [
        1,
        2,
        3,
        6
    ]
};
const DATETIME_MULTIPLIERS_WITH_BIG_WEEKEND = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, DATETIME_MULTIPLIERS, {
    day: [
        1
    ]
});
const DATETIME_MINOR_MULTIPLIERS = {
    millisecond: [
        1,
        2,
        5,
        10,
        25,
        50,
        100,
        250,
        500
    ],
    second: [
        1,
        2,
        3,
        5,
        10,
        15,
        20,
        30
    ],
    minute: [
        1,
        2,
        3,
        5,
        10,
        15,
        20,
        30
    ],
    hour: [
        1,
        2,
        3,
        4,
        6,
        8,
        12
    ],
    day: [
        1,
        2,
        3,
        7,
        14
    ],
    month: [
        1,
        2,
        3,
        6
    ]
};
const MINOR_DELIMITERS = [
    2,
    4,
    5,
    8,
    10
];
const VISIBILITY_DELIMITER = 3;
const MINUTE = 6e4;
function dummyGenerator(options) {
    return function(data, screenDelta, tickInterval, forceTickInterval) {
        let count = mathFloor(screenDelta / options.axisDivisionFactor);
        count = count < 1 ? 1 : count;
        const interval = screenDelta / count;
        return {
            ticks: interval > 0 ? Array.apply(null, new Array(count + 1)).map((_, i)=>interval * i) : [],
            tickInterval: interval
        };
    };
}
function discreteGenerator(options) {
    return function(data, screenDelta, tickInterval, forceTickInterval) {
        const categories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(data.categories, data.min, data.max).categories;
        return {
            ticks: categories,
            tickInterval: mathCeil(categories.length * options.axisDivisionFactor / screenDelta)
        };
    };
}
const getValue = (value)=>value;
const getLogValue = (base, allowNegatives, linearThreshold)=>(value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(value, base, allowNegatives, linearThreshold);
const raiseTo = (base, allowNegatives, linearThreshold)=>(value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raiseToExt"])(value, base, allowNegatives, linearThreshold);
const mathRaiseTo = (base)=>(value)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raiseTo"])(value, base);
const logAbsValue = (base)=>(value)=>0 === value ? 0 : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLog"])(mathAbs(value), base);
const correctValueByInterval = (post, round, getValue)=>(value, interval)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(post(round((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(getValue(value) / interval)) * interval));
function correctMinValueByEndOnTick(floorFunc, ceilFunc, resolveEndOnTick, endOnTick) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(endOnTick)) {
        return endOnTick ? floorFunc : ceilFunc;
    }
    return function(value, interval, businessViewInfo, forceEndOnTick) {
        const floorTickValue = floorFunc(value, interval);
        if (value - floorTickValue === 0 || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(businessViewInfo) || resolveEndOnTick(value, floorTickValue, interval, businessViewInfo) || forceEndOnTick) {
            return floorTickValue;
        }
        return ceilFunc(value, interval);
    };
}
function resolveEndOnTick(curValue, tickValue, interval, businessViewInfo) {
    const prevTickDataDiff = interval - mathAbs(tickValue - curValue);
    const intervalCount = math.max(mathCeil(businessViewInfo.businessDelta / interval), 2);
    const businessRatio = businessViewInfo.screenDelta / (intervalCount * interval);
    const potentialTickScreenDiff = math.round(businessRatio * prevTickDataDiff);
    const delimiterFactor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(businessRatio * interval / businessViewInfo.axisDivisionFactor, 2) + 1;
    const delimiterMultiplier = (businessViewInfo.isSpacedMargin ? 2 : 1) * delimiterFactor;
    const screenDelimiter = math.round(3 * delimiterMultiplier);
    return businessViewInfo.businessDelta > businessViewInfo.interval && potentialTickScreenDiff >= screenDelimiter;
}
function resolveEndOnTickLog(base) {
    return function(curValue, tickValue, interval, businessViewInfo) {
        return resolveEndOnTick((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(curValue, base), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(tickValue, base), interval, businessViewInfo);
    };
}
function resolveEndOnTickDate(curValue, tickValue, interval, businessViewInfo) {
    return resolveEndOnTick(curValue.valueOf(), tickValue.valueOf(), dateToMilliseconds(interval), businessViewInfo);
}
function getBusinessDelta(data, breaks) {
    let spacing = 0;
    if (breaks) {
        spacing = breaks.reduce((prev, item)=>prev + (item.to - item.from), 0);
    }
    return mathAbs(data.max - data.min - spacing);
}
function getBusinessDeltaLog(base, allowNegatives, linearThreshold) {
    const getLog = getLogValue(base, allowNegatives, linearThreshold);
    return function(data, breaks) {
        let spacing = 0;
        if (breaks) {
            spacing = breaks.reduce((prev, item)=>prev + mathAbs(getLog(item.to / item.from)), 0);
        }
        return mathCeil(mathAbs(getLog(data.max) - getLog(data.min)) - spacing);
    };
}
function getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor, addTickCount) {
    let count = screenDelta / axisDivisionFactor - (addTickCount || 0);
    count = count < 1 ? 1 : count;
    return businessDelta / count;
}
function getMultiplierFactor(interval, factorDelta) {
    return mathPow(10, mathFloor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(interval, 10)) + (factorDelta || 0));
}
function calculateTickInterval(businessDelta, screenDelta, tickInterval, forceTickInterval, axisDivisionFactor, multipliers, allowDecimals, addTickCount, _, minTickInterval) {
    const interval = getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor, addTickCount);
    let result = 1;
    const onlyIntegers = false === allowDecimals;
    if (!forceTickInterval || !tickInterval) {
        if (interval >= 1 || !onlyIntegers && interval > 0) {
            result = adjustInterval(interval, multipliers, onlyIntegers);
        }
        if (!tickInterval || !forceTickInterval && tickInterval < result) {
            tickInterval = result;
        }
    }
    if (!forceTickInterval && minTickInterval) {
        minTickInterval = adjustInterval(minTickInterval, multipliers, onlyIntegers);
        if (minTickInterval > tickInterval) {
            tickInterval = minTickInterval;
        }
    }
    return tickInterval;
}
function adjustInterval(interval, multipliers, onlyIntegers) {
    const factor = getMultiplierFactor(interval, -1);
    let result = 1;
    multipliers = multipliers || NUMBER_MULTIPLIERS;
    if (interval > 0) {
        interval /= factor;
        result = multipliers.concat(10 * multipliers[0]).map((m)=>10 * m).reduce((r, m)=>{
            if (.1 === factor && onlyIntegers && 25 === m) {
                return r;
            }
            return r < interval ? m : r;
        }, 0);
        result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(result * factor, factor);
    }
    return result;
}
function calculateMinorTickInterval(businessDelta, screenDelta, tickInterval, axisDivisionFactor) {
    const interval = getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor);
    return tickInterval || MINOR_DELIMITERS.reduce((r, d)=>{
        const cur = businessDelta / d;
        return cur >= interval ? cur : r;
    }, 0);
}
function getCalculateTickIntervalLog(skipCalculationLimits) {
    return function(businessDelta, screenDelta, tickInterval, forceTickInterval, axisDivisionFactor, multipliers, allowDecimals, _, __, minTickInterval) {
        const interval = getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor);
        let result = 0;
        const adjustInterval = getAdjustIntervalLog(skipCalculationLimits);
        if (!forceTickInterval || !tickInterval) {
            if (interval > 0) {
                result = adjustInterval(interval, multipliers);
            }
            if (!tickInterval || !forceTickInterval && tickInterval < result) {
                tickInterval = result;
            }
        }
        if (!forceTickInterval && minTickInterval) {
            minTickInterval = adjustInterval(minTickInterval, multipliers);
            if (minTickInterval > tickInterval) {
                tickInterval = minTickInterval;
            }
        }
        return tickInterval;
    };
}
function getAdjustIntervalLog(skipCalculationLimits) {
    return function(interval, multipliers) {
        let factor = getMultiplierFactor(interval);
        multipliers = multipliers || LOGARITHMIC_MULTIPLIERS;
        if (!skipCalculationLimits && factor < 1) {
            factor = 1;
        }
        return multipliers.concat(10 * multipliers[0]).reduce((r, m)=>r < interval ? m * factor : r, 0);
    };
}
function getDataTimeMultipliers(gapSize) {
    if (gapSize && gapSize > 2) {
        return DATETIME_MULTIPLIERS_WITH_BIG_WEEKEND;
    } else {
        return DATETIME_MULTIPLIERS;
    }
}
function numbersReducer(interval, key) {
    return function(r, m) {
        if (!r && interval <= convertDateUnitToMilliseconds(key, m)) {
            r = {};
            r[key + "s"] = m;
        }
        return r;
    };
}
function yearsReducer(interval, factor) {
    return function(r, m) {
        const years = factor * m;
        if (!r && interval <= convertDateUnitToMilliseconds("year", years) && 2.5 !== years) {
            r = {
                years: years
            };
        }
        return r;
    };
}
function calculateTickIntervalDateTime(businessDelta, screenDelta, tickInterval, forceTickInterval, axisDivisionFactor, multipliers, allowDecimals, addTickCount, gapSize, minTickInterval) {
    if (!forceTickInterval || !tickInterval) {
        const result = adjustIntervalDateTime(getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor), multipliers, null, gapSize);
        if (!tickInterval || !forceTickInterval && dateToMilliseconds(tickInterval) <= dateToMilliseconds(result)) {
            tickInterval = result;
        }
    }
    if (!forceTickInterval && minTickInterval) {
        minTickInterval = adjustIntervalDateTime(minTickInterval, multipliers, null, gapSize);
        if (dateToMilliseconds(minTickInterval) > dateToMilliseconds(tickInterval)) {
            tickInterval = minTickInterval;
        }
    }
    return tickInterval;
}
function adjustIntervalDateTime(interval, multipliers, _, gapSize) {
    let result;
    multipliers = multipliers || getDataTimeMultipliers(gapSize);
    for(const key in multipliers){
        result = multipliers[key].reduce(numbersReducer(interval, key), result);
        if (result) {
            break;
        }
    }
    if (!result) {
        for(let factor = 1;; factor *= 10){
            result = NUMBER_MULTIPLIERS.reduce(yearsReducer(interval, factor), result);
            if (result) {
                break;
            }
        }
    }
    return result;
}
function calculateMinorTickIntervalDateTime(businessDelta, screenDelta, tickInterval, axisDivisionFactor) {
    return calculateTickIntervalDateTime(businessDelta, screenDelta, tickInterval, true, axisDivisionFactor, DATETIME_MINOR_MULTIPLIERS);
}
function getTickIntervalByCustomTicks(getValue, postProcess) {
    return (ticks)=>ticks ? postProcess(mathAbs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(getValue(ticks[1]) - getValue(ticks[0])))) || void 0 : void 0;
}
function addInterval(value, interval, isNegative) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].addInterval(value, interval, isNegative);
}
function addIntervalLog(log, raise) {
    return (value, interval, isNegative)=>raise(addInterval(log(value), interval, isNegative));
}
function addIntervalDate(value, interval, isNegative) {
    return addInterval(value, interval, isNegative);
}
function addIntervalWithBreaks(addInterval, breaks, correctValue) {
    breaks = breaks.filter((b)=>!b.gapSize);
    return function(value, interval, isNegative) {
        let breakSize;
        value = addInterval(value, interval, isNegative);
        if (!breaks.every((item)=>{
            if (value >= addInterval(item.from, interval) && addInterval(value, interval) < item.to) {
                breakSize = item.to - item.from - 2 * (addInterval(item.from, interval) - item.from);
            }
            return !breakSize;
        })) {
            value = correctValue(addInterval(value, breakSize), interval);
        }
        return value;
    };
}
function calculateTicks(addInterval, correctMinValue, adjustInterval, resolveEndOnTick) {
    return function(data, tickInterval, endOnTick, gaps, breaks, businessDelta, screenDelta, axisDivisionFactor, generateExtraTick) {
        const correctTickValue = correctTickValueOnGapSize(addInterval, gaps);
        const min = data.min;
        const max = data.max;
        const businessViewInfo = {
            screenDelta: screenDelta,
            businessDelta: businessDelta,
            axisDivisionFactor: axisDivisionFactor,
            isSpacedMargin: data.isSpacedMargin,
            interval: tickInterval
        };
        let cur = correctMinValue(min, tickInterval, businessViewInfo);
        const ticks = [];
        if (null !== breaks && void 0 !== breaks && breaks.length) {
            addInterval = addIntervalWithBreaks(addInterval, breaks, correctMinValue);
        }
        if (cur > max) {
            cur = correctMinValue(min, adjustInterval(businessDelta / 2), businessViewInfo);
            if (cur > max) {
                endOnTick = true;
                cur = correctMinValue(min, tickInterval, businessViewInfo, endOnTick);
            }
        }
        cur = correctTickValue(cur);
        let prev;
        while(cur < max && cur !== prev || generateExtraTick && cur <= max){
            ticks.push(cur);
            prev = cur;
            cur = correctTickValue(addInterval(cur, tickInterval));
        }
        if (endOnTick || cur - max === 0 || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(endOnTick) && resolveEndOnTick(max, cur, tickInterval, businessViewInfo)) {
            ticks.push(cur);
        }
        return ticks;
    };
}
function calculateMinorTicks(updateTickInterval, addInterval, correctMinValue, correctTickValue, ceil) {
    return function(min, max, majorTicks, minorTickInterval, tickInterval, breaks, maxCount) {
        const factor = tickInterval / minorTickInterval;
        const lastMajor = majorTicks[majorTicks.length - 1];
        const firstMajor = majorTicks[0];
        let tickBalance = maxCount - 1;
        if (null !== breaks && void 0 !== breaks && breaks.length) {
            addInterval = addIntervalWithBreaks(addInterval, breaks, correctMinValue);
        }
        minorTickInterval = updateTickInterval(minorTickInterval, firstMajor, firstMajor, factor);
        if (0 === minorTickInterval) {
            return [];
        }
        let cur = correctTickValue(correctMinValue(min, tickInterval, min), minorTickInterval);
        minorTickInterval = updateTickInterval(minorTickInterval, firstMajor, cur, factor);
        let ticks = [];
        while(cur < firstMajor && (!tickBalance || tickBalance > 0)){
            cur >= min && ticks.push(cur);
            tickBalance--;
            cur = addInterval(cur, minorTickInterval);
        }
        const middleTicks = majorTicks.reduce((r, tick)=>{
            tickBalance = maxCount - 1;
            if (null === r.prevTick) {
                r.prevTick = tick;
                return r;
            }
            minorTickInterval = updateTickInterval(minorTickInterval, tick, r.prevTick, factor);
            let cur = correctTickValue(r.prevTick, minorTickInterval);
            while(cur < tick && (!tickBalance || tickBalance > 0)){
                cur !== r.prevTick && r.minors.push(cur);
                tickBalance--;
                cur = addInterval(cur, minorTickInterval);
            }
            r.prevTick = tick;
            return r;
        }, {
            prevTick: null,
            minors: []
        });
        ticks = ticks.concat(middleTicks.minors);
        const maxValue = ceil(max, tickInterval, min);
        minorTickInterval = updateTickInterval(minorTickInterval, maxValue, maxValue, factor);
        cur = correctTickValue(lastMajor, minorTickInterval);
        let prev;
        while(cur < max && cur !== prev){
            ticks.push(cur);
            prev = cur;
            cur = addInterval(cur, minorTickInterval);
        }
        if (lastMajor - max !== 0 && cur - max === 0) {
            ticks.push(cur);
        }
        return ticks;
    };
}
function filterTicks(ticks, breaks) {
    if (breaks.length) {
        const result = breaks.reduce((result, b)=>{
            const tmpTicks = [];
            let i;
            for(i = result[1]; i < ticks.length; i++){
                const tickValue = ticks[i];
                if (tickValue < b.from) {
                    tmpTicks.push(tickValue);
                }
                if (tickValue >= b.to) {
                    break;
                }
            }
            return [
                result[0].concat(tmpTicks),
                i
            ];
        }, [
            [],
            0
        ]);
        return result[0].concat(ticks.slice(result[1]));
    }
    return ticks;
}
function correctTickValueOnGapSize(addInterval, breaks) {
    return function(value) {
        let gapSize;
        if (!breaks.every((item)=>{
            if (value >= item.from && value < item.to) {
                gapSize = item.gapSize;
            }
            return !gapSize;
        })) {
            value = addInterval(value, gapSize);
        }
        return value;
    };
}
function generator(options, getBusinessDelta, calculateTickInterval, calculateMinorTickInterval, getMajorTickIntervalByCustomTicks, getMinorTickIntervalByCustomTicks, convertTickInterval, calculateTicks, calculateMinorTicks, processScaleBreaks) {
    function correctUserTickInterval(tickInterval, businessDelta, limit) {
        if (tickInterval && businessDelta / convertTickInterval(tickInterval) >= limit + 1) {
            options.incidentOccurred("W2003");
            tickInterval = void 0;
        }
        return tickInterval;
    }
    return function(data, screenDelta, tickInterval, forceTickInterval, customTicks, minorTickInterval, minorTickCount, breaks) {
        customTicks = customTicks || {};
        const businessDelta = getBusinessDelta(data, breaks);
        let result = function(customTicks) {
            return {
                tickInterval: getMajorTickIntervalByCustomTicks(customTicks.majors),
                ticks: customTicks.majors || [],
                minorTickInterval: getMinorTickIntervalByCustomTicks(customTicks.minors),
                minorTicks: customTicks.minors || []
            };
        }(customTicks);
        if (!isNaN(businessDelta)) {
            if (0 === businessDelta && !customTicks.majors) {
                result.ticks = [
                    data.min
                ];
            } else {
                result = function(ticks, data, businessDelta, screenDelta, tickInterval, forceTickInterval, customTicks, breaks) {
                    if (customTicks.majors) {
                        ticks.breaks = breaks;
                        return ticks;
                    }
                    const gaps = breaks.filter((b)=>b.gapSize);
                    let majorTicks;
                    tickInterval = options.skipCalculationLimits ? tickInterval : correctUserTickInterval(tickInterval, businessDelta, screenDelta);
                    tickInterval = calculateTickInterval(businessDelta, screenDelta, tickInterval, forceTickInterval, options.axisDivisionFactor, options.numberMultipliers, options.allowDecimals, breaks.length, gaps[0] && gaps[0].gapSize.days, options.minTickInterval);
                    if (!options.skipTickGeneration) {
                        majorTicks = calculateTicks(data, tickInterval, options.endOnTick, gaps, breaks, businessDelta, screenDelta, options.axisDivisionFactor, options.generateExtraTick);
                        breaks = processScaleBreaks(breaks, majorTicks, tickInterval);
                        majorTicks = filterTicks(majorTicks, breaks);
                        ticks.breaks = breaks;
                        ticks.ticks = ticks.ticks.concat(majorTicks);
                    }
                    ticks.tickInterval = tickInterval;
                    return ticks;
                }(result, data, businessDelta, screenDelta, tickInterval, forceTickInterval, customTicks, breaks || []);
                if (!options.skipTickGeneration && businessDelta > 0) {
                    result = function(ticks, data, businessDelta, screenDelta, minorTickInterval, minorTickCount, customTicks) {
                        if (!options.calculateMinors) {
                            return ticks;
                        }
                        if (customTicks.minors) {
                            return ticks;
                        }
                        const minorBusinessDelta = convertTickInterval(ticks.tickInterval);
                        const minorScreenDelta = screenDelta * minorBusinessDelta / businessDelta;
                        const breaks = ticks.breaks;
                        if (!minorTickInterval && minorTickCount) {
                            minorTickInterval = getMinorTickIntervalByCustomTicks([
                                minorBusinessDelta / (minorTickCount + 1),
                                minorBusinessDelta / (minorTickCount + 1) * 2
                            ]);
                        } else {
                            minorTickCount = void 0;
                        }
                        minorTickInterval = correctUserTickInterval(minorTickInterval, minorBusinessDelta, minorScreenDelta);
                        minorTickInterval = calculateMinorTickInterval(minorBusinessDelta, minorScreenDelta, minorTickInterval, options.minorAxisDivisionFactor);
                        ticks.minorTicks = filterTicks(ticks.minorTicks.concat(calculateMinorTicks(data.min, data.max, ticks.ticks, minorTickInterval, ticks.tickInterval, breaks, minorTickCount)), breaks);
                        ticks.minorTickInterval = minorTickInterval;
                        return ticks;
                    }(result, data, businessDelta, screenDelta, minorTickInterval, minorTickCount, customTicks);
                }
            }
        }
        return result;
    };
}
function getBaseTick(breakValue, _ref, interval, getValue) {
    let [tick, insideTick] = _ref;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tick) || mathAbs(getValue(breakValue) - getValue(tick)) / interval > .25) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(insideTick) && mathAbs(getValue(insideTick) - getValue(tick)) / interval < 2) {
            tick = insideTick;
        } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tick)) {
            tick = breakValue;
        }
    }
    return tick;
}
function getScaleBreaksProcessor(convertTickInterval, getValue, addCorrection) {
    return function(breaks, ticks, tickInterval) {
        const interval = convertTickInterval(tickInterval);
        const correction = .5 * interval;
        return breaks.reduce((result, b)=>{
            let breakTicks = ticks.filter((tick)=>tick <= b.from);
            const from = addCorrection(getBaseTick(b.from, [].concat(breakTicks[breakTicks.length - 1], ticks[breakTicks.length]), interval, getValue), correction);
            breakTicks = ticks.filter((tick)=>tick >= b.to);
            const to = addCorrection(getBaseTick(b.to, [].concat(breakTicks[0], ticks[ticks.length - breakTicks.length - 1]), interval, getValue), -correction);
            if (getValue(to) - getValue(from) < interval && !b.gapSize) {
                return result;
            }
            if (b.gapSize) {
                return result.concat([
                    b
                ]);
            }
            return result.concat([
                {
                    from: from,
                    to: to,
                    cumulativeWidth: b.cumulativeWidth
                }
            ]);
        }, []);
    };
}
function numericGenerator(options) {
    const floor = correctValueByInterval(getValue, mathFloor, getValue);
    const ceil = correctValueByInterval(getValue, mathCeil, getValue);
    const calculateTickIntervalByCustomTicks = getTickIntervalByCustomTicks(getValue, getValue);
    return generator(options, getBusinessDelta, calculateTickInterval, calculateMinorTickInterval, calculateTickIntervalByCustomTicks, calculateTickIntervalByCustomTicks, getValue, calculateTicks(addInterval, correctMinValueByEndOnTick(floor, ceil, resolveEndOnTick, options.endOnTick), adjustInterval, resolveEndOnTick), calculateMinorTicks(getValue, addInterval, floor, addInterval, getValue), getScaleBreaksProcessor(getValue, getValue, (value, correction)=>value + correction));
}
const correctValueByIntervalLog = (post, getRound, getValue)=>(value, interval)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sign"])(value) * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(post(getRound(value)((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(getValue(value) / interval)) * interval));
function logarithmicGenerator(options) {
    const base = options.logBase;
    const raise = raiseTo(base, options.allowNegatives, options.linearThreshold);
    const log = getLogValue(base, options.allowNegatives, options.linearThreshold);
    const absLog = logAbsValue(base);
    const absRaise = mathRaiseTo(base);
    const floor = correctValueByIntervalLog(absRaise, (value)=>value < 0 ? mathCeil : mathFloor, absLog);
    const ceil = correctValueByIntervalLog(absRaise, (value)=>value < 0 ? mathFloor : mathCeil, absLog);
    const ceilNumber = correctValueByInterval(getValue, mathCeil, getValue);
    return generator(options, getBusinessDeltaLog(base, options.allowNegatives, options.linearThreshold), getCalculateTickIntervalLog(options.skipCalculationLimits), calculateMinorTickInterval, getTickIntervalByCustomTicks(log, getValue), getTickIntervalByCustomTicks(getValue, getValue), getValue, calculateTicks(addIntervalLog(log, raise), correctMinValueByEndOnTick(floor, ceil, resolveEndOnTickLog(base), options.endOnTick), getAdjustIntervalLog(options.skipCalculationLimits), resolveEndOnTickLog(base)), calculateMinorTicks((_, tick, prevTick, factor)=>Math.max(Math.abs(tick), Math.abs(prevTick)) / factor, addInterval, floor, ceilNumber, ceil), getScaleBreaksProcessor(getValue, log, (value, correction)=>raise(log(value) + correction)));
}
function dateGenerator(options) {
    function floor(value, interval) {
        const floorNumber = correctValueByInterval(getValue, mathFloor, getValue);
        let intervalObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isString"])(interval) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateIntervalByString(interval.toLowerCase()) : interval;
        const divider = dateToMilliseconds(interval);
        if (intervalObject.days % 7 === 0 || interval.quarters) {
            intervalObject = adjustIntervalDateTime(divider);
        }
        const correctDateWithUnitBeginning = (v)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].correctDateWithUnitBeginning(v, intervalObject, null, options.firstDayOfWeek);
        const floorAtStartDate = (v)=>new Date(mathFloor((v.getTime() - 6e4 * v.getTimezoneOffset()) / divider) * divider + 6e4 * v.getTimezoneOffset());
        value = correctDateWithUnitBeginning(value);
        if ("years" in intervalObject) {
            value.setFullYear(floorNumber(value.getFullYear(), intervalObject.years));
        } else if ("quarters" in intervalObject) {
            value = correctDateWithUnitBeginning(floorAtStartDate(value));
        } else if ("months" in intervalObject) {
            value.setMonth(floorNumber(value.getMonth(), intervalObject.months));
        } else if ("weeks" in intervalObject || "days" in intervalObject) {
            value = correctDateWithUnitBeginning(floorAtStartDate(value));
        } else if ("hours" in intervalObject) {
            value.setHours(floorNumber(value.getHours(), intervalObject.hours));
        } else if ("minutes" in intervalObject) {
            value.setMinutes(floorNumber(value.getMinutes(), intervalObject.minutes));
        } else if ("seconds" in intervalObject) {
            value.setSeconds(floorNumber(value.getSeconds(), intervalObject.seconds));
        } else if ("milliseconds" in intervalObject) {
            value = floorAtStartDate(value);
        }
        return value;
    }
    const calculateTickIntervalByCustomTicks = getTickIntervalByCustomTicks(getValue, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertMillisecondsToDateUnits);
    return generator(options, getBusinessDelta, calculateTickIntervalDateTime, calculateMinorTickIntervalDateTime, calculateTickIntervalByCustomTicks, calculateTickIntervalByCustomTicks, dateToMilliseconds, calculateTicks(addIntervalDate, correctMinValueByEndOnTick(floor, function(value, interval) {
        let newValue = floor(value, interval);
        while(value - newValue > 0){
            newValue = addIntervalDate(newValue, interval);
        }
        return newValue;
    }, resolveEndOnTickDate, options.endOnTick), adjustIntervalDateTime, resolveEndOnTickDate), calculateMinorTicks(getValue, addIntervalDate, floor, addIntervalDate, getValue), getScaleBreaksProcessor(dateToMilliseconds, getValue, (value, correction)=>new Date(value.getTime() + correction)));
}
const tickGenerator = function(options) {
    let result;
    if (options.rangeIsEmpty) {
        result = dummyGenerator(options);
    } else if ("discrete" === options.axisType) {
        result = discreteGenerator(options);
    } else if ("logarithmic" === options.axisType) {
        result = logarithmicGenerator(options);
    } else if ("datetime" === options.dataType) {
        result = dateGenerator(options);
    } else {
        result = numericGenerator(options);
    }
    return result;
};
}),
"[project]/node_modules/devextreme/esm/viz/axes/tick.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/tick.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "tick": ()=>createTick
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
;
;
;
function getPathStyle(options) {
    return {
        stroke: options.color,
        "stroke-width": options.width,
        "stroke-opacity": options.opacity,
        opacity: 1
    };
}
function createTick(axis, renderer, tickOptions, gridOptions, skippedCategory, skipLabels, offset) {
    const tickOffset = offset || axis._tickOffset;
    const lineGroup = axis._axisLineGroup;
    const elementsGroup = axis._axisElementsGroup;
    const tickStyle = getPathStyle(tickOptions);
    const gridStyle = getPathStyle(gridOptions);
    const emptyStrRegExp = /^\s+$/;
    const axisOptions = axis.getOptions();
    const labelOptions = axisOptions.label;
    const labelStyle = axis._textOptions;
    function getLabelFontStyle(tick) {
        let fontStyle = axis._textFontStyles;
        const customizeColor = labelOptions.customizeColor;
        if (customizeColor && customizeColor.call) {
            fontStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, axis._textFontStyles, {
                fill: customizeColor.call(tick, tick)
            });
        }
        return fontStyle;
    }
    function createLabelHint(tick, range) {
        const labelHint = axis.formatHint(tick.value, labelOptions, range);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(labelHint) && "" !== labelHint) {
            tick.getContentContainer().setTitle(labelHint);
        }
    }
    return function(value) {
        const tick = {
            value: value,
            updateValue (newValue) {
                this.value = value = newValue;
            },
            initCoords: function() {
                this.coords = axis._getTranslatedValue(value, tickOffset);
                this.labelCoords = axis._getTranslatedValue(value);
            },
            saveCoords () {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"])(this._templateDef).done(()=>{
                    this._lastStoredCoordinates = {
                        coords: this._storedCoords,
                        labelCoords: this._storedLabelsCoords
                    };
                    this._storedCoords = this.coords;
                    this._storedLabelsCoords = this.templateContainer ? this._getTemplateCoords() : this.labelCoords;
                });
            },
            resetCoordinates () {
                if (this._lastStoredCoordinates) {
                    this._storedCoords = this._lastStoredCoordinates.coords;
                    this._storedLabelsCoords = this._lastStoredCoordinates.labelCoords;
                }
            },
            drawMark (options) {
                if (!tickOptions.visible || skippedCategory === value) {
                    return;
                }
                if (axis.areCoordsOutsideAxis(this.coords)) {
                    return;
                }
                if (this.mark) {
                    this.mark.append(lineGroup);
                    axis.sharp(this.mark, axis.getSharpDirectionByCoords(this.coords));
                    this.updateTickPosition(options);
                } else {
                    this.mark = axis._createPathElement([], tickStyle, axis.getSharpDirectionByCoords(this.coords)).append(lineGroup);
                    this.updateTickPosition(options);
                }
            },
            setSkippedCategory (category) {
                skippedCategory = category;
            },
            _updateLine (lineElement, settings, storedSettings, animate, isGridLine) {
                if (!lineElement) {
                    return;
                }
                if (null === settings.points || null === settings.r) {
                    lineElement.remove();
                    return;
                }
                if (animate && storedSettings && null !== storedSettings.points) {
                    settings.opacity = 1;
                    lineElement.attr(storedSettings);
                    lineElement.animate(settings);
                } else {
                    settings.opacity = animate ? 0 : 1;
                    lineElement.attr(settings);
                    animate && lineElement.animate({
                        opacity: 1
                    }, {
                        delay: .5,
                        partitionDuration: .5
                    });
                }
                this.coords.angle && axis._rotateTick(lineElement, this.coords, isGridLine);
            },
            updateTickPosition: function(options, animate) {
                this._updateLine(this.mark, {
                    points: axis._getTickMarkPoints(tick.coords, tickOptions.length, options)
                }, this._storedCoords && {
                    points: axis._getTickMarkPoints(tick._storedCoords, tickOptions.length, options)
                }, animate, false);
            },
            drawLabel: function(range, template) {
                if (this.templateContainer && axis.isRendered()) {
                    this.updateLabelPosition();
                    return;
                }
                const labelIsVisible = labelOptions.visible && !skipLabels && !axis.getTranslator().getBusinessRange().isEmpty() && !axis.areCoordsOutsideAxis(this.labelCoords);
                if (!labelIsVisible) {
                    if (this.label) {
                        this.removeLabel();
                    }
                    return;
                }
                const templateOption = labelOptions.template;
                const text = axis.formatLabel(value, labelOptions, range);
                if (this.label) {
                    this.label.attr({
                        text: text,
                        rotate: 0
                    }).append(elementsGroup);
                    createLabelHint(this, range);
                    this.updateLabelPosition();
                    return;
                }
                if (templateOption) {
                    this.templateContainer = renderer.g().append(elementsGroup);
                    this._templateDef && this._templateDef.reject();
                    this._templateDef = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
                    template.render({
                        model: {
                            valueText: text,
                            value: this.value,
                            labelFontStyle: getLabelFontStyle(this),
                            labelStyle: labelStyle
                        },
                        container: this.templateContainer.element,
                        onRendered: ()=>{
                            this.updateLabelPosition();
                            this._templateDef && this._templateDef.resolve();
                        }
                    });
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(text) && "" !== text && !emptyStrRegExp.test(text)) {
                    this.label = renderer.text(text).css(getLabelFontStyle(this)).attr(labelStyle).append(elementsGroup);
                    this.updateLabelPosition();
                    createLabelHint(this, range);
                }
                const containerForData = this.getContentContainer();
                containerForData && containerForData.data("chart-data-argument", this.value);
                this.templateContainer && createLabelHint(this, range);
            },
            getTemplateDeferred () {
                return this._templateDef;
            },
            getContentContainer () {
                return this.templateContainer || this.label;
            },
            fadeOutElements () {
                const startSettings = {
                    opacity: 1
                };
                const endSettings = {
                    opacity: 0
                };
                const animationSettings = {
                    partitionDuration: .5
                };
                if (this.getContentContainer()) {
                    this._fadeOutLabel();
                }
                if (this.grid) {
                    this.grid.append(axis._axisGridGroup).attr(startSettings).animate(endSettings, animationSettings);
                }
                if (this.mark) {
                    this.mark.append(axis._axisLineGroup).attr(startSettings).animate(endSettings, animationSettings);
                }
            },
            _fadeInLabel () {
                const group = axis._renderer.g().attr({
                    opacity: 0
                }).append(axis._axisElementsGroup).animate({
                    opacity: 1
                }, {
                    delay: .5,
                    partitionDuration: .5
                });
                this.getContentContainer().append(group);
            },
            _fadeOutLabel () {
                const group = axis._renderer.g().attr({
                    opacity: 1
                }).animate({
                    opacity: 0
                }, {
                    partitionDuration: .5
                }).append(axis._axisElementsGroup).toBackground();
                this.getContentContainer().append(group);
            },
            _getTemplateCoords () {
                return axis._getLabelAdjustedCoord(this, (axis._constantLabelOffset || 0) + (tick.labelOffset || 0));
            },
            updateLabelPosition: function(animate) {
                const templateContainer = this.templateContainer;
                if (!this.getContentContainer()) {
                    return;
                }
                if (animate && this._storedLabelsCoords) {
                    if (templateContainer) {
                        templateContainer.attr(this._storedLabelsCoords);
                        const lCoords = this._getTemplateCoords();
                        templateContainer.animate(lCoords);
                    } else {
                        this.label.attr({
                            x: this._storedLabelsCoords.x,
                            y: this._storedLabelsCoords.y
                        });
                        this.label.animate({
                            x: this.labelCoords.x,
                            y: this.labelCoords.y
                        });
                    }
                } else {
                    if (templateContainer) {
                        const lCoords = this._getTemplateCoords();
                        templateContainer.attr(lCoords);
                    } else {
                        this.label.attr({
                            x: this.labelCoords.x,
                            y: this.labelCoords.y
                        });
                    }
                    if (animate) {
                        this._fadeInLabel();
                    }
                }
            },
            updateMultilineTextAlignment () {
                if (labelOptions.template || !this.label) {
                    return;
                }
                this.label.attr({
                    textsAlignment: this.labelAlignment || axis.getOptions().label.alignment
                });
            },
            drawGrid: function(drawLine) {
                if (gridOptions.visible && skippedCategory !== this.value) {
                    if (this.grid) {
                        this.grid.append(axis._axisGridGroup);
                        axis.sharp(this.grid, axis.getSharpDirectionByCoords(this.coords));
                        this.updateGridPosition();
                    } else {
                        this.grid = drawLine(this, gridStyle);
                        this.grid && this.grid.append(axis._axisGridGroup);
                    }
                }
            },
            updateGridPosition: function(animate) {
                this._updateLine(this.grid, axis._getGridPoints(tick.coords), this._storedCoords && axis._getGridPoints(this._storedCoords), animate, true);
            },
            removeLabel () {
                const contentContainer = this.getContentContainer();
                contentContainer && contentContainer.remove();
                this._templateDef && this._templateDef.reject();
                this._templateDef = this.templateContainer = this.label = null;
            }
        };
        return tick;
    };
}
;
}),
"[project]/node_modules/devextreme/esm/viz/axes/datetime_breaks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/datetime_breaks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "generateDateBreaks": ()=>generateDateBreaks
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
;
const days = [
    0,
    1,
    2,
    3,
    4,
    5,
    6
];
function getWeekendDays(workdays) {
    return days.filter(function(day) {
        return !workdays.some(function(workDay) {
            return workDay === day;
        });
    });
}
function getNextDayIndex(dayIndex) {
    return (dayIndex + 1) % 7;
}
function dayBetweenWeekend(weekend, day) {
    let start = weekend.start;
    const end = weekend.end;
    while(start !== end){
        if (start === day) {
            return true;
        }
        start = getNextDayIndex(start);
    }
    return false;
}
function getDaysDistance(day, end) {
    let length = 0;
    while(day !== end){
        day = getNextDayIndex(day);
        length++;
    }
    return length;
}
function separateBreak(scaleBreak, day) {
    const result = [];
    const dayEnd = new Date(day);
    dayEnd.setDate(day.getDate() + 1);
    if (day > scaleBreak.from) {
        result.push({
            from: scaleBreak.from,
            to: day
        });
    }
    if (dayEnd < scaleBreak.to) {
        result.push({
            from: dayEnd,
            to: scaleBreak.to
        });
    }
    return result;
}
function getWeekEndDayIndices(workDays) {
    const indices = getWeekendDays(workDays);
    if (indices.length < 7) {
        while(getNextDayIndex(indices[indices.length - 1]) === indices[0]){
            indices.unshift(indices.pop());
        }
    }
    return indices;
}
function generateDateBreaksForWeekend(min, max, weekendDayIndices) {
    let day = min.getDate();
    const breaks = [];
    const weekends = weekendDayIndices.reduce(function(obj, day) {
        let currentWeekEnd = obj[1];
        if (void 0 === currentWeekEnd.start) {
            currentWeekEnd = {
                start: day,
                end: getNextDayIndex(day)
            };
            obj[0].push(currentWeekEnd);
            return [
                obj[0],
                currentWeekEnd
            ];
        } else if (currentWeekEnd.end === day) {
            currentWeekEnd.end = getNextDayIndex(day);
            return obj;
        }
        currentWeekEnd = {
            start: day,
            end: getNextDayIndex(day)
        };
        obj[0].push(currentWeekEnd);
        return [
            obj[0],
            currentWeekEnd
        ];
    }, [
        [],
        {}
    ]);
    weekends[0].forEach(function(weekend) {
        let currentDate = new Date(min);
        currentDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trimTime(currentDate);
        while(currentDate < max){
            day = currentDate.getDay();
            const date = currentDate.getDate();
            if (dayBetweenWeekend(weekend, day)) {
                const from = new Date(currentDate);
                currentDate.setDate(date + getDaysDistance(day, weekend.end));
                const to = new Date(currentDate);
                breaks.push({
                    from: from,
                    to: to
                });
            }
            currentDate.setDate(currentDate.getDate() + 1);
        }
    });
    return breaks;
}
function excludeWorkDaysFromWeekEndBreaks(breaks, exactWorkDays) {
    const result = breaks.slice();
    let i;
    const processWorkDay = function(workday) {
        workday = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trimTime(new Date(workday));
        if (result[i].from <= workday && result[i].to > workday) {
            const separatedBreak = separateBreak(result[i], workday);
            if (2 === separatedBreak.length) {
                result.splice(i, 1, separatedBreak[0], separatedBreak[1]);
            } else if (1 === separatedBreak.length) {
                result.splice(i, 1, separatedBreak[0]);
            } else {
                result.splice(i, 1);
            }
        }
    };
    for(i = 0; i < result.length; i++){
        exactWorkDays.forEach(processWorkDay);
    }
    return result;
}
function generateBreaksForHolidays(min, max, holidays, weekendDayIndices) {
    let day;
    const dayInWeekend = function(dayIndex) {
        return dayIndex === day;
    };
    const adjustedMin = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trimTime(min);
    const adjustedMax = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trimTime(max);
    adjustedMax.setDate(max.getDate() + 1);
    return holidays.reduce(function(breaks, holiday) {
        let holidayStart;
        let holidayEnd;
        holiday = new Date(holiday);
        day = holiday.getDay();
        if (!weekendDayIndices.some(dayInWeekend) && holiday >= adjustedMin && holiday <= adjustedMax) {
            holidayStart = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trimTime(holiday);
            holidayEnd = new Date(holidayStart);
            holidayEnd.setDate(holidayStart.getDate() + 1);
            breaks.push({
                from: holidayStart,
                to: holidayEnd
            });
        }
        return breaks;
    }, []);
}
function calculateGaps(breaks) {
    return breaks.map(function(b) {
        return {
            from: b.from,
            to: b.to,
            gapSize: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertMillisecondsToDateUnits(b.to - b.from)
        };
    });
}
function generateDateBreaks(min, max, workWeek, singleWorkdays, holidays) {
    const weekendDayIndices = getWeekEndDayIndices(workWeek);
    const breaks = generateDateBreaksForWeekend(min, max, weekendDayIndices);
    breaks.push.apply(breaks, generateBreaksForHolidays(min, max, holidays || [], weekendDayIndices));
    return calculateGaps(excludeWorkDaysFromWeekEndBreaks(breaks, singleWorkdays || []));
}
}),
"[project]/node_modules/devextreme/esm/viz/axes/xy_axes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/xy_axes.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/range.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/format_helper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$datetime_breaks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/datetime_breaks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/axes_constants.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
const getNextDateUnit = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getNextDateUnit;
const correctDateWithUnitBeginning = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].correctDateWithUnitBeginning;
const _math = Math;
const _max = _math.max;
const TOP = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].top;
const BOTTOM = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bottom;
const LEFT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].left;
const RIGHT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].right;
const CENTER = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].center;
const SCALE_BREAK_OFFSET = 3;
const RANGE_RATIO = .3;
const WAVED_LINE_CENTER = 2;
const WAVED_LINE_TOP = 0;
const WAVED_LINE_BOTTOM = 4;
const WAVED_LINE_LENGTH = 24;
const TICKS_CORRECTIONS = {
    left: -1,
    top: -1,
    right: 0,
    bottom: 0,
    center: -.5
};
function prepareDatesDifferences(datesDifferences, tickInterval) {
    let dateUnitInterval;
    let i;
    if ("week" === tickInterval) {
        tickInterval = "day";
    }
    if ("quarter" === tickInterval) {
        tickInterval = "month";
    }
    if (datesDifferences[tickInterval]) {
        for(i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateUnitIntervals.length; i++){
            dateUnitInterval = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateUnitIntervals[i];
            if (datesDifferences[dateUnitInterval]) {
                datesDifferences[dateUnitInterval] = false;
                datesDifferences.count--;
            }
            if (dateUnitInterval === tickInterval) {
                break;
            }
        }
    }
}
function sortingBreaks(breaks) {
    return breaks.sort(function(a, b) {
        return a.from - b.from;
    });
}
function getMarkerDates(min, max, markerInterval) {
    const origMin = min;
    let dates;
    min = correctDateWithUnitBeginning(min, markerInterval);
    max = correctDateWithUnitBeginning(max, markerInterval);
    dates = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getSequenceByInterval(min, max, markerInterval);
    if (dates.length && origMin > dates[0]) {
        dates = dates.slice(1);
    }
    return dates;
}
function getStripHorizontalAlignmentPosition(alignment) {
    let position = "start";
    if ("center" === alignment) {
        position = "center";
    }
    if ("right" === alignment) {
        position = "end";
    }
    return position;
}
function getStripVerticalAlignmentPosition(alignment) {
    let position = "start";
    if ("center" === alignment) {
        position = "center";
    }
    if ("bottom" === alignment) {
        position = "end";
    }
    return position;
}
function getMarkerInterval(tickInterval) {
    let markerInterval = getNextDateUnit(tickInterval);
    if ("quarter" === markerInterval) {
        markerInterval = getNextDateUnit(markerInterval);
    }
    return markerInterval;
}
function getMarkerFormat(curDate, prevDate, tickInterval, markerInterval) {
    let format = markerInterval;
    const datesDifferences = prevDate && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDatesDifferences(prevDate, curDate);
    if (prevDate && "year" !== tickInterval) {
        prepareDatesDifferences(datesDifferences, tickInterval);
        format = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByDifferences(datesDifferences);
    }
    return format;
}
function getMaxSide(act, boxes) {
    return boxes.reduce(function(prevValue, box) {
        return _max(prevValue, act(box));
    }, 0);
}
function getDistanceByAngle(bBox, rotationAngle) {
    rotationAngle = _math.abs(rotationAngle);
    rotationAngle = rotationAngle % 180 >= 90 ? 90 - rotationAngle % 90 : rotationAngle % 90;
    const a = rotationAngle * (_math.PI / 180);
    if (a >= _math.atan(bBox.height / bBox.width)) {
        return bBox.height / _math.abs(_math.sin(a));
    } else {
        return bBox.width;
    }
}
function getMaxConstantLinePadding(constantLines) {
    return constantLines.reduce(function(padding, options) {
        return _max(padding, options.paddingTopBottom);
    }, 0);
}
function getConstantLineLabelMarginForVerticalAlignment(constantLines, alignment, labelHeight) {
    return constantLines.some(function(options) {
        return options.label.verticalAlignment === alignment;
    }) && labelHeight || 0;
}
function getLeftMargin(bBox) {
    return _math.abs(bBox.x) || 0;
}
function getRightMargin(bBox) {
    return _math.abs(bBox.width - _math.abs(bBox.x)) || 0;
}
function generateRangesOnPoints(points, edgePoints, getRange) {
    let i;
    let length;
    let maxRange = null;
    const ranges = [];
    let curValue;
    let prevValue;
    let curRange;
    for(i = 1, length = points.length; i < length; i++){
        curValue = points[i];
        prevValue = points[i - 1];
        curRange = getRange(curValue, prevValue);
        if (edgePoints.indexOf(curValue) >= 0) {
            if (!maxRange || curRange > maxRange.length) {
                maxRange = {
                    start: curValue,
                    end: prevValue,
                    length: curRange
                };
            }
        } else {
            if (maxRange && curRange < maxRange.length) {
                ranges.push(maxRange);
            } else {
                ranges.push({
                    start: curValue,
                    end: prevValue,
                    length: curRange
                });
            }
            maxRange = null;
        }
    }
    if (maxRange) {
        ranges.push(maxRange);
    }
    return ranges;
}
function generateAutoBreaks(_ref, series, _ref2) {
    let { logarithmBase: logarithmBase, type: type, maxAutoBreakCount: maxAutoBreakCount } = _ref;
    let { minVisible: minVisible, maxVisible: maxVisible } = _ref2;
    const breaks = [];
    const getRange = "logarithmic" === type ? (min, max)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLog"])(max / min, logarithmBase) : (min, max)=>max - min;
    let visibleRange = getRange(minVisible, maxVisible);
    const points = series.reduce((result, s)=>{
        const points = s.getPointsInViewPort();
        result[0] = result[0].concat(points[0]);
        result[1] = result[1].concat(points[1]);
        return result;
    }, [
        [],
        []
    ]);
    const sortedAllPoints = points[0].concat(points[1]).sort((a, b)=>b - a);
    const edgePoints = points[1].filter((p)=>points[0].indexOf(p) < 0);
    let minDiff = .3 * visibleRange;
    const ranges = generateRangesOnPoints(sortedAllPoints, edgePoints, getRange).filter((_ref3)=>{
        let { length: length } = _ref3;
        return !!length;
    }).sort((a, b)=>b.length - a.length);
    const epsilon = _math.min.apply(null, ranges.map((r)=>r.length)) / 1e3;
    const _maxAutoBreakCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(maxAutoBreakCount) ? _math.min(maxAutoBreakCount, ranges.length) : ranges.length;
    for(let i = 0; i < _maxAutoBreakCount; i++){
        if (ranges[i].length >= minDiff) {
            if (visibleRange <= ranges[i].length) {
                break;
            }
            visibleRange -= ranges[i].length;
            if (visibleRange > epsilon || visibleRange < -epsilon) {
                breaks.push({
                    from: ranges[i].start,
                    to: ranges[i].end
                });
                minDiff = .3 * visibleRange;
            }
        } else {
            break;
        }
    }
    sortingBreaks(breaks);
    return breaks;
}
const __TURBOPACK__default__export__ = {
    linear: {
        _getStep: function(boxes, rotationAngle) {
            const spacing = this._options.label.minSpacing;
            const func = this._isHorizontal ? function(box) {
                return box.width + spacing;
            } : function(box) {
                return box.height;
            };
            let maxLabelLength = getMaxSide(func, boxes);
            if (rotationAngle) {
                maxLabelLength = getDistanceByAngle({
                    width: maxLabelLength,
                    height: this._getMaxLabelHeight(boxes, 0)
                }, rotationAngle);
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTicksCountInRange(this._majorTicks, this._isHorizontal ? "x" : "y", maxLabelLength);
        },
        _getMaxLabelHeight: function(boxes, spacing) {
            return getMaxSide(function(box) {
                return box.height;
            }, boxes) + spacing;
        },
        _validateOverlappingMode: function(mode, displayMode) {
            if (this._isHorizontal && ("rotate" === displayMode || "stagger" === displayMode) || !this._isHorizontal) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].validateOverlappingMode(mode);
            }
            return mode;
        },
        _validateDisplayMode: function(mode) {
            return this._isHorizontal ? mode : "standard";
        },
        getMarkerTrackers: function() {
            return this._markerTrackers;
        },
        _getSharpParam: function(opposite) {
            return this._isHorizontal ^ opposite ? "h" : "v";
        },
        _createAxisElement: function() {
            return this._renderer.path([], "line");
        },
        _updateAxisElementPosition: function() {
            const axisCoord = this._axisPosition;
            const canvas = this._getCanvasStartEnd();
            this._axisElement.attr({
                points: this._isHorizontal ? [
                    canvas.start,
                    axisCoord,
                    canvas.end,
                    axisCoord
                ] : [
                    axisCoord,
                    canvas.start,
                    axisCoord,
                    canvas.end
                ]
            });
        },
        _getTranslatedCoord: function(value, offset) {
            return this._translator.translate(value, offset);
        },
        _initAxisPositions () {
            const that = this;
            if (that.customPositionIsAvailable()) {
                that._customBoundaryPosition = that.getCustomBoundaryPosition();
            }
            if (!that.customPositionIsAvailable() || that.customPositionIsBoundary()) {
                that._axisPosition = that.getPredefinedPosition(that.getResolvedBoundaryPosition());
            } else {
                that._axisPosition = that.getCustomPosition();
            }
        },
        _getTickMarkPoints (coords, length, tickOptions) {
            const isHorizontal = this._isHorizontal;
            const tickOrientation = this._options.tickOrientation;
            const labelPosition = this._options.label.position;
            let tickStartCoord;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tickOrientation)) {
                tickStartCoord = TICKS_CORRECTIONS[tickOrientation] * length;
            } else {
                let shift = tickOptions.shift || 0;
                if (!isHorizontal && labelPosition === LEFT || isHorizontal && labelPosition !== BOTTOM) {
                    shift = -shift;
                }
                tickStartCoord = shift + this.getTickStartPositionShift(length);
            }
            return [
                coords.x + (isHorizontal ? 0 : tickStartCoord),
                coords.y + (isHorizontal ? tickStartCoord : 0),
                coords.x + (isHorizontal ? 0 : tickStartCoord + length),
                coords.y + (isHorizontal ? tickStartCoord + length : 0)
            ];
        },
        getTickStartPositionShift (length) {
            const width = this._options.width;
            const position = this.getResolvedBoundaryPosition();
            return length % 2 === 1 ? width % 2 === 0 && (position === LEFT || position === TOP) || width % 2 === 1 && (position === RIGHT || position === BOTTOM) && !this.hasNonBoundaryPosition() ? Math.floor(-length / 2) : -Math.floor(length / 2) : -length / 2 + (width % 2 === 0 ? 0 : position === BOTTOM || position === RIGHT ? -1 : 1);
        },
        _getTitleCoords: function() {
            const horizontal = this._isHorizontal;
            let x = this._axisPosition;
            let y = this._axisPosition;
            const align = this._options.title.alignment;
            const canvas = this._getCanvasStartEnd();
            const fromStartToEnd = horizontal || this._options.position === LEFT;
            const canvasStart = fromStartToEnd ? canvas.start : canvas.end;
            const canvasEnd = fromStartToEnd ? canvas.end : canvas.start;
            const coord = align === LEFT ? canvasStart : align === RIGHT ? canvasEnd : canvas.start + (canvas.end - canvas.start) / 2;
            if (horizontal) {
                x = coord;
            } else {
                y = coord;
            }
            return {
                x: x,
                y: y
            };
        },
        _drawTitleText: function(group, coords) {
            const options = this._options;
            const titleOptions = options.title;
            const attrs = {
                opacity: titleOptions.opacity,
                align: titleOptions.alignment,
                class: titleOptions.cssClass
            };
            if (!titleOptions.text || !group) {
                return;
            }
            coords = coords || this._getTitleCoords();
            if (!this._isHorizontal) {
                attrs.rotate = options.position === LEFT ? 270 : 90;
            }
            const text = this._renderer.text(titleOptions.text, coords.x, coords.y).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["patchFontOptions"])(titleOptions.font)).attr(attrs).append(group);
            this._checkTitleOverflow(text);
            return text;
        },
        _updateTitleCoords: function() {
            this._title && this._title.element.attr(this._getTitleCoords());
        },
        _drawTitle: function() {
            const title = this._drawTitleText(this._axisTitleGroup);
            if (title) {
                this._title = {
                    element: title
                };
            }
        },
        _measureTitle: function() {
            if (this._title) {
                if (this._title.bBox && !this._title.originalSize) {
                    this._title.originalSize = this._title.bBox;
                }
                this._title.bBox = this._title.element.getBBox();
            }
        },
        _drawDateMarker: function(date, options, range) {
            const that = this;
            const markerOptions = that._options.marker;
            const invert = that._translator.getBusinessRange().invert;
            const textIndent = markerOptions.width + markerOptions.textLeftIndent;
            let pathElement;
            if (null === options.x) {
                return;
            }
            if (!options.withoutStick) {
                pathElement = that._renderer.path([
                    options.x,
                    options.y,
                    options.x,
                    options.y + markerOptions.separatorHeight
                ], "line").attr({
                    "stroke-width": markerOptions.width,
                    stroke: markerOptions.color,
                    "stroke-opacity": markerOptions.opacity,
                    sharp: "h"
                }).append(that._axisElementsGroup);
            }
            const text = String(that.formatLabel(date, options.labelOptions, range));
            return {
                date: date,
                x: options.x,
                y: options.y,
                cropped: options.withoutStick,
                label: that._renderer.text(text, options.x, options.y).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["patchFontOptions"])(markerOptions.label.font)).append(that._axisElementsGroup),
                line: pathElement,
                getContentContainer () {
                    return this.label;
                },
                getEnd: function() {
                    return this.x + (invert ? -1 : 1) * (textIndent + this.labelBBox.width);
                },
                setTitle: function() {
                    this.title = text;
                },
                hideLabel: function() {
                    this.label.dispose();
                    this.label = null;
                    this.title = text;
                },
                hide: function() {
                    if (pathElement) {
                        pathElement.dispose();
                        pathElement = null;
                    }
                    this.label.dispose();
                    this.label = null;
                    this.hidden = true;
                }
            };
        },
        _drawDateMarkers: function() {
            const that = this;
            const options = that._options;
            const translator = that._translator;
            const viewport = that._getViewportRange();
            const minBound = viewport.minVisible;
            let dateMarkers = [];
            let dateMarker;
            function draw(markerDate, format, withoutStick) {
                return that._drawDateMarker(markerDate, {
                    x: translator.translate(markerDate),
                    y: markersAreaTop,
                    labelOptions: that._getLabelFormatOptions(format),
                    withoutStick: withoutStick
                }, viewport);
            }
            if (viewport.isEmpty() || !options.marker.visible || "datetime" !== options.argumentType || "discrete" === options.type || that._majorTicks.length <= 1) {
                return [];
            }
            const markersAreaTop = that._axisPosition + options.marker.topIndent;
            const tickInterval = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateUnitInterval(this._tickInterval);
            const markerInterval = getMarkerInterval(tickInterval);
            const markerDates = getMarkerDates(minBound, viewport.maxVisible, markerInterval);
            if (markerDates.length > 1 || 1 === markerDates.length && minBound < markerDates[0]) {
                dateMarkers = markerDates.reduce(function(markers, curDate, i, dates) {
                    const marker = draw(curDate, getMarkerFormat(curDate, dates[i - 1] || minBound < curDate && minBound, tickInterval, markerInterval));
                    marker && markers.push(marker);
                    return markers;
                }, []);
                if (minBound < markerDates[0]) {
                    dateMarker = draw(minBound, getMarkerFormat(minBound, markerDates[0], tickInterval, markerInterval), true);
                    dateMarker && dateMarkers.unshift(dateMarker);
                }
            }
            return dateMarkers;
        },
        _adjustDateMarkers: function(offset) {
            offset = offset || 0;
            const that = this;
            const markerOptions = this._options.marker;
            const textIndent = markerOptions.width + markerOptions.textLeftIndent;
            const invert = this._translator.getBusinessRange().invert;
            const canvas = that._getCanvasStartEnd();
            const dateMarkers = this._dateMarkers;
            if (!dateMarkers.length) {
                return offset;
            }
            if (dateMarkers[0].cropped) {
                if (!this._checkMarkersPosition(invert, dateMarkers[1], dateMarkers[0])) {
                    dateMarkers[0].hideLabel();
                }
            }
            let prevDateMarker;
            dateMarkers.forEach(function(marker, i, markers) {
                if (marker.cropped) {
                    return;
                }
                if (invert ? marker.getEnd() < canvas.end : marker.getEnd() > canvas.end) {
                    marker.hideLabel();
                } else if (that._checkMarkersPosition(invert, marker, prevDateMarker)) {
                    prevDateMarker = marker;
                } else {
                    marker.hide();
                }
            });
            this._dateMarkers.forEach(function(marker) {
                if (marker.label) {
                    const labelBBox = marker.labelBBox;
                    const dy = marker.y + markerOptions.textTopIndent - labelBBox.y;
                    marker.label.attr({
                        translateX: invert ? marker.x - textIndent - labelBBox.x - labelBBox.width : marker.x + textIndent - labelBBox.x,
                        translateY: dy + offset
                    });
                }
                if (marker.line) {
                    marker.line.attr({
                        translateY: offset
                    });
                }
            });
            that._initializeMarkersTrackers(offset);
            return offset + markerOptions.topIndent + markerOptions.separatorHeight;
        },
        _checkMarkersPosition: function(invert, dateMarker, prevDateMarker) {
            if (void 0 === prevDateMarker) {
                return true;
            }
            return invert ? dateMarker.x < prevDateMarker.getEnd() : dateMarker.x > prevDateMarker.getEnd();
        },
        _initializeMarkersTrackers: function(offset) {
            const separatorHeight = this._options.marker.separatorHeight;
            const renderer = this._renderer;
            const businessRange = this._translator.getBusinessRange();
            const canvas = this._getCanvasStartEnd();
            const group = this._axisElementsGroup;
            this._markerTrackers = this._dateMarkers.filter(function(marker) {
                return !marker.hidden;
            }).map(function(marker, i, markers) {
                const nextMarker = markers[i + 1] || {
                    x: canvas.end,
                    date: businessRange.max
                };
                const x = marker.x;
                const y = marker.y + offset;
                const markerTracker = renderer.path([
                    x,
                    y,
                    x,
                    y + separatorHeight,
                    nextMarker.x,
                    y + separatorHeight,
                    nextMarker.x,
                    y,
                    x,
                    y
                ], "area").attr({
                    "stroke-width": 1,
                    stroke: "grey",
                    fill: "grey",
                    opacity: 1e-4
                }).append(group);
                markerTracker.data("range", {
                    startValue: marker.date,
                    endValue: nextMarker.date
                });
                if (marker.title) {
                    markerTracker.setTitle(marker.title);
                }
                return markerTracker;
            });
        },
        _getLabelFormatOptions: function(formatString) {
            const that = this;
            let markerLabelOptions = that._markerLabelOptions;
            if (!markerLabelOptions) {
                that._markerLabelOptions = markerLabelOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, that._options.marker.label);
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(that._options.marker.label.format)) {
                markerLabelOptions.format = formatString;
            }
            return markerLabelOptions;
        },
        _adjustConstantLineLabels: function(constantLines) {
            const that = this;
            const axisPosition = that._options.position;
            const canvas = that.getCanvas();
            const canvasLeft = canvas.left;
            const canvasRight = canvas.width - canvas.right;
            const canvasTop = canvas.top;
            const canvasBottom = canvas.height - canvas.bottom;
            const verticalCenter = canvasTop + (canvasBottom - canvasTop) / 2;
            const horizontalCenter = canvasLeft + (canvasRight - canvasLeft) / 2;
            let maxLabel = 0;
            constantLines.forEach(function(item) {
                const isHorizontal = that._isHorizontal;
                const linesOptions = item.options;
                const paddingTopBottom = linesOptions.paddingTopBottom;
                const paddingLeftRight = linesOptions.paddingLeftRight;
                const labelOptions = linesOptions.label;
                const labelVerticalAlignment = labelOptions.verticalAlignment;
                const labelHorizontalAlignment = labelOptions.horizontalAlignment;
                const labelIsInside = "inside" === labelOptions.position;
                const label = item.label;
                const box = item.labelBBox;
                let translateX;
                let translateY;
                if (null === label || box.isEmpty) {
                    return;
                }
                if (isHorizontal) {
                    if (labelIsInside) {
                        if (labelHorizontalAlignment === LEFT) {
                            translateX = item.coord - paddingLeftRight - box.x - box.width;
                        } else {
                            translateX = item.coord + paddingLeftRight - box.x;
                        }
                        switch(labelVerticalAlignment){
                            case CENTER:
                                translateY = verticalCenter - box.y - box.height / 2;
                                break;
                            case BOTTOM:
                                translateY = canvasBottom - paddingTopBottom - box.y - box.height;
                                break;
                            default:
                                translateY = canvasTop + paddingTopBottom - box.y;
                        }
                    } else {
                        if (axisPosition === labelVerticalAlignment) {
                            maxLabel = _max(maxLabel, box.height + paddingTopBottom);
                        }
                        translateX = item.coord - box.x - box.width / 2;
                        if (labelVerticalAlignment === BOTTOM) {
                            translateY = canvasBottom + paddingTopBottom - box.y;
                        } else {
                            translateY = canvasTop - paddingTopBottom - box.y - box.height;
                        }
                    }
                } else if (labelIsInside) {
                    if (labelVerticalAlignment === BOTTOM) {
                        translateY = item.coord + paddingTopBottom - box.y;
                    } else {
                        translateY = item.coord - paddingTopBottom - box.y - box.height;
                    }
                    switch(labelHorizontalAlignment){
                        case CENTER:
                            translateX = horizontalCenter - box.x - box.width / 2;
                            break;
                        case RIGHT:
                            translateX = canvasRight - paddingLeftRight - box.x - box.width;
                            break;
                        default:
                            translateX = canvasLeft + paddingLeftRight - box.x;
                    }
                } else {
                    if (axisPosition === labelHorizontalAlignment) {
                        maxLabel = _max(maxLabel, box.width + paddingLeftRight);
                    }
                    translateY = item.coord - box.y - box.height / 2;
                    if (labelHorizontalAlignment === RIGHT) {
                        translateX = canvasRight + paddingLeftRight - box.x;
                    } else {
                        translateX = canvasLeft - paddingLeftRight - box.x - box.width;
                    }
                }
                label.attr({
                    translateX: translateX,
                    translateY: translateY
                });
            });
            return maxLabel;
        },
        _drawConstantLinesForEstimating: function(constantLines) {
            const that = this;
            const renderer = this._renderer;
            const group = renderer.g();
            constantLines.forEach(function(options) {
                that._drawConstantLineLabelText(options.label.text, 0, 0, options.label, group).attr({
                    align: "center"
                });
            });
            return group.append(renderer.root);
        },
        _estimateLabelHeight: function(bBox, labelOptions) {
            let height = bBox.height;
            const drawingType = labelOptions.drawingType;
            if ("stagger" === this._validateDisplayMode(drawingType) || "stagger" === this._validateOverlappingMode(labelOptions.overlappingBehavior, drawingType)) {
                height = 2 * height + labelOptions.staggeringSpacing;
            }
            if ("rotate" === this._validateDisplayMode(drawingType) || "rotate" === this._validateOverlappingMode(labelOptions.overlappingBehavior, drawingType)) {
                const sinCos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(labelOptions.rotationAngle);
                height = height * sinCos.cos + bBox.width * sinCos.sin;
            }
            return height && (height + labelOptions.indentFromAxis || 0) || 0;
        },
        estimateMargins: function(canvas) {
            this.updateCanvas(canvas);
            const { position: position, placeholderSize: placeholderSize } = this._options;
            const range = this._getViewportRange();
            const ticksData = this._createTicksAndLabelFormat(range);
            const ticks = ticksData.ticks;
            const tickInterval = ticksData.tickInterval;
            const options = this._options;
            const constantLineOptions = this._outsideConstantLines.filter((l)=>l.labelOptions.visible).map((l)=>l.options);
            const rootElement = this._renderer.root;
            const labelIsVisible = options.label.visible && !range.isEmpty() && ticks.length;
            const labelValue = labelIsVisible && this.formatLabel(ticks[ticks.length - 1], options.label, void 0, void 0, tickInterval, ticks);
            const labelElement = labelIsVisible && this._renderer.text(labelValue, 0, 0).css(this._textFontStyles).attr(this._textOptions).append(rootElement);
            const titleElement = this._drawTitleText(rootElement, {
                x: 0,
                y: 0
            });
            const constantLinesLabelsElement = this._drawConstantLinesForEstimating(constantLineOptions);
            const labelBox = !options.label.template && labelElement && labelElement.getBBox() || {
                x: 0,
                y: 0,
                width: 0,
                height: 0
            };
            const titleBox = titleElement && titleElement.getBBox() || {
                x: 0,
                y: 0,
                width: 0,
                height: 0
            };
            const constantLinesBox = constantLinesLabelsElement.getBBox();
            const titleHeight = titleBox.height ? titleBox.height + options.title.margin : 0;
            const labelHeight = this._estimateLabelHeight(labelBox, options.label);
            const constantLinesHeight = constantLinesBox.height ? constantLinesBox.height + getMaxConstantLinePadding(constantLineOptions) : 0;
            const height = labelHeight + titleHeight;
            const margins = {
                left: _max(getLeftMargin(labelBox), getLeftMargin(constantLinesBox)),
                right: _max(getRightMargin(labelBox), getRightMargin(constantLinesBox)),
                top: ("top" === options.position ? height : 0) + getConstantLineLabelMarginForVerticalAlignment(constantLineOptions, "top", constantLinesHeight),
                bottom: ("top" !== options.position ? height : 0) + getConstantLineLabelMarginForVerticalAlignment(constantLineOptions, "bottom", constantLinesHeight)
            };
            if (placeholderSize) {
                margins[position] = placeholderSize;
            }
            labelElement && labelElement.remove();
            titleElement && titleElement.remove();
            constantLinesLabelsElement && constantLinesLabelsElement.remove();
            return margins;
        },
        _checkAlignmentConstantLineLabels: function(labelOptions) {
            const position = labelOptions.position;
            let verticalAlignment = (labelOptions.verticalAlignment || "").toLowerCase();
            let horizontalAlignment = (labelOptions.horizontalAlignment || "").toLowerCase();
            if (this._isHorizontal) {
                if ("outside" === position) {
                    verticalAlignment = verticalAlignment === BOTTOM ? BOTTOM : TOP;
                    horizontalAlignment = CENTER;
                } else {
                    verticalAlignment = verticalAlignment === CENTER ? CENTER : verticalAlignment === BOTTOM ? BOTTOM : TOP;
                    horizontalAlignment = horizontalAlignment === LEFT ? LEFT : RIGHT;
                }
            } else if ("outside" === position) {
                verticalAlignment = CENTER;
                horizontalAlignment = horizontalAlignment === LEFT ? LEFT : RIGHT;
            } else {
                verticalAlignment = verticalAlignment === BOTTOM ? BOTTOM : TOP;
                horizontalAlignment = horizontalAlignment === RIGHT ? RIGHT : horizontalAlignment === CENTER ? CENTER : LEFT;
            }
            labelOptions.verticalAlignment = verticalAlignment;
            labelOptions.horizontalAlignment = horizontalAlignment;
        },
        _getConstantLineLabelsCoords: function(value, lineLabelOptions) {
            const that = this;
            let x = value;
            let y = value;
            if (that._isHorizontal) {
                y = that._orthogonalPositions["top" === lineLabelOptions.verticalAlignment ? "start" : "end"];
            } else {
                x = that._orthogonalPositions["right" === lineLabelOptions.horizontalAlignment ? "end" : "start"];
            }
            return {
                x: x,
                y: y
            };
        },
        _getAdjustedStripLabelCoords: function(strip) {
            const stripOptions = strip.options;
            const paddingTopBottom = stripOptions.paddingTopBottom;
            const paddingLeftRight = stripOptions.paddingLeftRight;
            const horizontalAlignment = stripOptions.label.horizontalAlignment;
            const verticalAlignment = stripOptions.label.verticalAlignment;
            const box = strip.labelBBox;
            const labelHeight = box.height;
            const labelWidth = box.width;
            const labelCoords = strip.labelCoords;
            let y = labelCoords.y - box.y;
            let x = labelCoords.x - box.x;
            if (verticalAlignment === TOP) {
                y += paddingTopBottom;
            } else if (verticalAlignment === CENTER) {
                y -= labelHeight / 2;
            } else if (verticalAlignment === BOTTOM) {
                y -= paddingTopBottom + labelHeight;
            }
            if (horizontalAlignment === LEFT) {
                x += paddingLeftRight;
            } else if (horizontalAlignment === CENTER) {
                x -= labelWidth / 2;
            } else if (horizontalAlignment === RIGHT) {
                x -= paddingLeftRight + labelWidth;
            }
            return {
                translateX: x,
                translateY: y
            };
        },
        _adjustTitle: function(offset) {
            offset = offset || 0;
            if (!this._title) {
                return;
            }
            const options = this._options;
            const position = options.position;
            const margin = options.title.margin;
            const title = this._title;
            const boxTitle = title.bBox;
            const x = boxTitle.x;
            const y = boxTitle.y;
            const width = boxTitle.width;
            const height = boxTitle.height;
            const axisPosition = this._axisPosition;
            const loCoord = axisPosition - margin - offset;
            const hiCoord = axisPosition + margin + offset;
            const params = {};
            if (this._isHorizontal) {
                if (position === TOP) {
                    params.translateY = loCoord - (y + height);
                } else {
                    params.translateY = hiCoord - y;
                }
            } else if (position === LEFT) {
                params.translateX = loCoord - (x + width);
            } else {
                params.translateX = hiCoord - x;
            }
            title.element.attr(params);
        },
        _checkTitleOverflow: function(titleElement) {
            if (!this._title && !titleElement) {
                return;
            }
            const canvasLength = this._getScreenDelta();
            const title = titleElement ? {
                bBox: titleElement.getBBox(),
                element: titleElement
            } : this._title;
            const titleOptions = this._options.title;
            const boxTitle = title.bBox;
            if ((this._isHorizontal ? boxTitle.width : boxTitle.height) > canvasLength) {
                title.element.setMaxSize(canvasLength, void 0, {
                    wordWrap: titleOptions.wordWrap || "none",
                    textOverflow: titleOptions.textOverflow || "ellipsis"
                });
                this._wrapped = titleOptions.wordWrap && "none" !== titleOptions.wordWrap;
            } else {
                const moreThanOriginalSize = title.originalSize && canvasLength > (this._isHorizontal ? title.originalSize.width : title.originalSize.height);
                !this._wrapped && moreThanOriginalSize && title.element.restoreText();
            }
        },
        coordsIn: function(x, y) {
            const canvas = this.getCanvas();
            const isHorizontal = this._options.isHorizontal;
            const position = this._options.position;
            const coord = isHorizontal ? y : x;
            if (isHorizontal && (x < canvas.left || x > canvas.width - canvas.right) || !isHorizontal && (y < canvas.top || y > canvas.height - canvas.bottom)) {
                return false;
            }
            if (isHorizontal && position === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].top || !isHorizontal && position === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].left) {
                return coord < canvas[position];
            }
            return coord > canvas[isHorizontal ? "height" : "width"] - canvas[position];
        },
        _boundaryTicksVisibility: {
            min: true,
            max: true
        },
        adjust () {
            const seriesData = this._seriesData;
            const viewport = this._series.filter((s)=>s.isVisible()).reduce((range, s)=>{
                const seriesRange = s.getViewport();
                range.min = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(seriesRange.min) ? range.min < seriesRange.min ? range.min : seriesRange.min : range.min;
                range.max = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(seriesRange.max) ? range.max > seriesRange.max ? range.max : seriesRange.max : range.max;
                if (s.showZero) {
                    range = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"](range);
                    range.correctValueZeroLevel();
                }
                return range;
            }, {});
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.min) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.max)) {
                seriesData.minVisible = viewport.min;
                seriesData.maxVisible = viewport.max;
            }
            seriesData.userBreaks = this._getScaleBreaks(this._options, {
                minVisible: seriesData.minVisible,
                maxVisible: seriesData.maxVisible
            }, this._series, this.isArgumentAxis);
            this._translator.updateBusinessRange(this._getViewportRange());
        },
        hasWrap () {
            return this._wrapped;
        },
        getAxisPosition () {
            return this._axisPosition;
        },
        _getStick: function() {
            return !this._options.valueMarginsEnabled;
        },
        _getStripLabelCoords: function(from, to, stripLabelOptions) {
            const orthogonalPositions = this._orthogonalPositions;
            const isHorizontal = this._isHorizontal;
            const horizontalAlignment = stripLabelOptions.horizontalAlignment;
            const verticalAlignment = stripLabelOptions.verticalAlignment;
            let x;
            let y;
            if (isHorizontal) {
                if (horizontalAlignment === CENTER) {
                    x = from + (to - from) / 2;
                } else if (horizontalAlignment === LEFT) {
                    x = from;
                } else if (horizontalAlignment === RIGHT) {
                    x = to;
                }
                y = orthogonalPositions[getStripVerticalAlignmentPosition(verticalAlignment)];
            } else {
                x = orthogonalPositions[getStripHorizontalAlignmentPosition(horizontalAlignment)];
                if (verticalAlignment === TOP) {
                    y = from;
                } else if (verticalAlignment === CENTER) {
                    y = to + (from - to) / 2;
                } else if (verticalAlignment === BOTTOM) {
                    y = to;
                }
            }
            return {
                x: x,
                y: y
            };
        },
        _getTranslatedValue: function(value, offset) {
            let interval;
            if ("semidiscrete" === this._options.type) {
                interval = this._options.tickInterval;
            }
            const pos1 = this._translator.translate(value, offset, false, interval);
            const pos2 = this._axisPosition;
            const isHorizontal = this._isHorizontal;
            return {
                x: isHorizontal ? pos1 : pos2,
                y: isHorizontal ? pos2 : pos1
            };
        },
        areCoordsOutsideAxis: function(coords) {
            const coord = this._isHorizontal ? coords.x : coords.y;
            const visibleArea = this.getVisibleArea();
            if (coord < visibleArea[0] || coord > visibleArea[1]) {
                return true;
            }
            return false;
        },
        _getSkippedCategory: function(ticks) {
            let skippedCategory;
            if (this._options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete && this._tickOffset && 0 !== ticks.length) {
                skippedCategory = ticks[ticks.length - 1];
            }
            return skippedCategory;
        },
        _filterBreaks: function(breaks, viewport, breakStyle) {
            const minVisible = viewport.minVisible;
            const maxVisible = viewport.maxVisible;
            const breakSize = breakStyle ? breakStyle.width : 0;
            return breaks.reduce(function(result, currentBreak) {
                let from = currentBreak.from;
                let to = currentBreak.to;
                const lastResult = result[result.length - 1];
                let newBreak;
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(from) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(to)) {
                    return result;
                }
                if (from > to) {
                    to = [
                        from,
                        from = to
                    ][0];
                }
                if (result.length && from < lastResult.to) {
                    if (to > lastResult.to) {
                        lastResult.to = to > maxVisible ? maxVisible : to;
                        if (lastResult.gapSize) {
                            lastResult.gapSize = void 0;
                            lastResult.cumulativeWidth += breakSize;
                        }
                    }
                } else if (from >= minVisible && from < maxVisible || to <= maxVisible && to > minVisible) {
                    from = from >= minVisible ? from : minVisible;
                    to = to <= maxVisible ? to : maxVisible;
                    if (to - from < maxVisible - minVisible) {
                        var _ref;
                        newBreak = {
                            from: from,
                            to: to,
                            cumulativeWidth: ((_ref = null === lastResult || void 0 === lastResult ? void 0 : lastResult.cumulativeWidth) !== null && _ref !== void 0 ? _ref : 0) + breakSize
                        };
                        if (currentBreak.gapSize) {
                            newBreak.gapSize = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertMillisecondsToDateUnits(to - from);
                            var _ref1;
                            newBreak.cumulativeWidth = (_ref1 = null === lastResult || void 0 === lastResult ? void 0 : lastResult.cumulativeWidth) !== null && _ref1 !== void 0 ? _ref1 : 0;
                        }
                        result.push(newBreak);
                    }
                }
                return result;
            }, []);
        },
        _getScaleBreaks: function(axisOptions, viewport, series, isArgumentAxis) {
            const that = this;
            let breaks = (axisOptions.breaks || []).map(function(b) {
                return {
                    from: that.parser(b.startValue),
                    to: that.parser(b.endValue)
                };
            });
            if ("discrete" !== axisOptions.type && "datetime" === axisOptions.dataType && axisOptions.workdaysOnly) {
                breaks = breaks.concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$datetime_breaks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateDateBreaks"])(viewport.minVisible, viewport.maxVisible, axisOptions.workWeek, axisOptions.singleWorkdays, axisOptions.holidays));
            }
            if (!isArgumentAxis && "discrete" !== axisOptions.type && "datetime" !== axisOptions.dataType && axisOptions.autoBreaksEnabled && 0 !== axisOptions.maxAutoBreakCount) {
                breaks = breaks.concat(generateAutoBreaks(axisOptions, series, viewport));
            }
            return sortingBreaks(breaks);
        },
        _drawBreak: function(translatedEnd, positionFrom, positionTo, width, options, group) {
            const breakStart = translatedEnd - (!this._translator.isInverted() ? width + 1 : 0);
            const attr = {
                "stroke-width": 1,
                stroke: options.borderColor,
                sharp: !options.isWaved ? options.isHorizontal ? "h" : "v" : void 0
            };
            const spaceAttr = {
                stroke: options.color,
                "stroke-width": width
            };
            const getPoints = this._isHorizontal ? rotateLine : function(p) {
                return p;
            };
            const drawer = getLineDrawer(this._renderer, group, getPoints, positionFrom, breakStart, positionTo, options.isWaved);
            drawer(width / 2, spaceAttr);
            drawer(0, attr);
            drawer(width, attr);
        },
        _createBreakClipRect: function(from, to) {
            const that = this;
            const canvas = that._canvas;
            const clipWidth = to - from;
            let clipRect;
            if (that._isHorizontal) {
                clipRect = that._renderer.clipRect(canvas.left, from, canvas.width, clipWidth);
            } else {
                clipRect = that._renderer.clipRect(from, canvas.top, clipWidth, canvas.height);
            }
            that._breaksElements = that._breaksElements || [];
            that._breaksElements.push(clipRect);
            return clipRect.id;
        },
        _createBreaksGroup: function(clipFrom, clipTo) {
            const group = this._renderer.g().attr({
                class: this._axisCssPrefix + "breaks",
                "clip-path": this._createBreakClipRect(clipFrom, clipTo)
            }).append(this._scaleBreaksGroup);
            this._breaksElements = this._breaksElements || [];
            this._breaksElements.push(group);
            return group;
        },
        _disposeBreaksGroup: function() {
            (this._breaksElements || []).forEach(function(clipRect) {
                clipRect.dispose();
            });
            this._breaksElements = null;
        },
        drawScaleBreaks: function(customCanvas) {
            const that = this;
            const options = that._options;
            const breakStyle = options.breakStyle;
            const position = options.position;
            let positionFrom;
            let positionTo;
            const breaks = that._translator.getBusinessRange().breaks || [];
            let additionGroup;
            let additionBreakFrom;
            let additionBreakTo;
            that._disposeBreaksGroup();
            if (!(breaks && breaks.length)) {
                return;
            }
            const breakOptions = {
                color: that._options.containerColor,
                borderColor: breakStyle.color,
                isHorizontal: that._isHorizontal,
                isWaved: "straight" !== breakStyle.line.toLowerCase()
            };
            if (customCanvas) {
                positionFrom = customCanvas.start;
                positionTo = customCanvas.end;
            } else {
                positionFrom = that._orthogonalPositions.start - (options.visible && !that._axisShift && (position === LEFT || position === TOP) ? 3 : 0);
                positionTo = that._orthogonalPositions.end + (options.visible && (position === RIGHT || position === BOTTOM) ? 3 : 0);
            }
            const mainGroup = that._createBreaksGroup(positionFrom, positionTo);
            if (that._axisShift && options.visible) {
                additionBreakFrom = that._axisPosition - that._axisShift - 3;
                additionBreakTo = additionBreakFrom + 6;
                additionGroup = that._createBreaksGroup(additionBreakFrom, additionBreakTo);
            }
            breaks.forEach(function(br) {
                if (!br.gapSize) {
                    const breakCoord = that._getTranslatedCoord(br.to);
                    that._drawBreak(breakCoord, positionFrom, positionTo, breakStyle.width, breakOptions, mainGroup);
                    if (that._axisShift && options.visible) {
                        that._drawBreak(breakCoord, additionBreakFrom, additionBreakTo, breakStyle.width, breakOptions, additionGroup);
                    }
                }
            });
        },
        _getSpiderCategoryOption: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
        shift: function(margins) {
            const options = this._options;
            const isHorizontal = options.isHorizontal;
            const axesSpacing = this.getMultipleAxesSpacing();
            const constantLinesGroups = this._axisConstantLineGroups;
            function shiftGroup(side, group) {
                const attr = {
                    translateX: 0,
                    translateY: 0
                };
                const shift = margins[side] ? margins[side] + axesSpacing : 0;
                attr[isHorizontal ? "translateY" : "translateX"] = (side === LEFT || side === TOP ? -1 : 1) * shift;
                (group[side] || group).attr(attr);
                return shift;
            }
            this._axisShift = shiftGroup(options.position, this._axisGroup);
            shiftGroup(options.position, this._axisElementsGroup);
            (isHorizontal ? [
                TOP,
                BOTTOM
            ] : [
                LEFT,
                RIGHT
            ]).forEach((side)=>{
                shiftGroup(side, constantLinesGroups.above);
                shiftGroup(side, constantLinesGroups.under);
            });
        },
        getCustomPosition (position) {
            const that = this;
            const orthogonalAxis = that.getOrthogonalAxis();
            const resolvedPosition = position !== null && position !== void 0 ? position : that.getResolvedPositionOption();
            const offset = that.getOptions().offset;
            const orthogonalTranslator = orthogonalAxis.getTranslator();
            const orthogonalAxisType = orthogonalAxis.getOptions().type;
            let validPosition = orthogonalAxis.validateUnit(resolvedPosition);
            let currentPosition;
            if ("discrete" === orthogonalAxisType && (!orthogonalTranslator._categories || orthogonalTranslator._categories.indexOf(validPosition) < 0)) {
                validPosition = void 0;
            }
            if (that.positionIsBoundary(resolvedPosition)) {
                currentPosition = that.getPredefinedPosition(resolvedPosition);
            } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(validPosition)) {
                currentPosition = that.getPredefinedPosition(that.getOptions().position);
            } else {
                currentPosition = orthogonalTranslator.to(validPosition, -1);
            }
            if (isFinite(currentPosition) && isFinite(offset)) {
                currentPosition += offset;
            }
            return currentPosition;
        },
        getCustomBoundaryPosition (position) {
            const that = this;
            const { customPosition: customPosition, offset: offset } = that.getOptions();
            const resolvedPosition = position !== null && position !== void 0 ? position : that.getResolvedPositionOption();
            const orthogonalAxis = that.getOrthogonalAxis();
            const orthogonalTranslator = orthogonalAxis.getTranslator();
            const visibleArea = orthogonalTranslator.getCanvasVisibleArea();
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(orthogonalAxis._orthogonalPositions) || 0 === orthogonalTranslator.canvasLength) {
                return;
            }
            const currentPosition = that.getCustomPosition(resolvedPosition);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(currentPosition)) {
                return that.getResolvedBoundaryPosition();
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(customPosition)) {
                if (currentPosition <= visibleArea.min) {
                    return that._isHorizontal ? TOP : LEFT;
                } else if (currentPosition >= visibleArea.max) {
                    return that._isHorizontal ? BOTTOM : RIGHT;
                }
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(offset)) {
                if (currentPosition <= that._orthogonalPositions.start) {
                    return that._isHorizontal ? TOP : LEFT;
                } else if (currentPosition >= that._orthogonalPositions.end) {
                    return that._isHorizontal ? BOTTOM : RIGHT;
                }
            }
            return currentPosition;
        },
        getResolvedPositionOption () {
            const options = this.getOptions();
            var _options_customPosition;
            return (_options_customPosition = options.customPosition) !== null && _options_customPosition !== void 0 ? _options_customPosition : options.position;
        },
        customPositionIsAvailable () {
            const options = this.getOptions();
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.getOrthogonalAxis()) && ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.customPosition) || isFinite(options.offset));
        },
        hasNonBoundaryPosition () {
            return this.customPositionIsAvailable() && !this.customPositionIsBoundary();
        },
        getResolvedBoundaryPosition () {
            return this.customPositionIsBoundary() ? this._customBoundaryPosition : this.getOptions().position;
        },
        customPositionEqualsToPredefined () {
            return this.customPositionIsBoundary() && this._customBoundaryPosition === this.getOptions().position;
        },
        customPositionIsBoundary () {
            return this.positionIsBoundary(this._customBoundaryPosition);
        },
        positionIsBoundary: (position)=>[
                TOP,
                LEFT,
                BOTTOM,
                RIGHT
            ].indexOf(position) >= 0,
        getPredefinedPosition (position) {
            var _this$_orthogonalPosi;
            return null === (_this$_orthogonalPosi = this._orthogonalPositions) || void 0 === _this$_orthogonalPosi ? void 0 : _this$_orthogonalPosi[position === TOP || position === LEFT ? "start" : "end"];
        },
        resolveOverlappingForCustomPositioning (oppositeAxes) {
            const that = this;
            if (!that.hasNonBoundaryPosition() && !that.customPositionIsBoundary() && !oppositeAxes.some((a)=>a.hasNonBoundaryPosition())) {
                return;
            }
            const overlappingObj = {
                axes: [],
                ticks: []
            };
            oppositeAxes.filter((orthogonalAxis)=>orthogonalAxis.pane === that.pane).forEach((orthogonalAxis)=>{
                for(let i = 0; i < that._majorTicks.length; i++){
                    const tick = that._majorTicks[i];
                    const label = tick.label;
                    if (label) {
                        if (overlappingObj.axes.indexOf(orthogonalAxis) < 0 && that._detectElementsOverlapping(label, orthogonalAxis._axisElement)) {
                            overlappingObj.axes.push(orthogonalAxis);
                            that._shiftThroughOrthogonalAxisOverlappedTick(label, orthogonalAxis);
                        }
                        for(let j = 0; j < orthogonalAxis._majorTicks.length; j++){
                            const oppositeTick = orthogonalAxis._majorTicks[j];
                            const oppositeLabel = oppositeTick.label;
                            if (oppositeLabel && that._detectElementsOverlapping(label, oppositeLabel)) {
                                overlappingObj.ticks.push(tick);
                                that._shiftThroughAxisOverlappedTick(tick);
                                i = that._majorTicks.length;
                                break;
                            }
                        }
                    }
                    if (tick.mark && overlappingObj.ticks.indexOf(tick) < 0) {
                        if (that._isHorizontal && tick.mark.attr("translateY")) {
                            tick.mark.attr({
                                translateY: 0
                            });
                        } else if (!that._isHorizontal && tick.mark.attr("translateX")) {
                            tick.mark.attr({
                                translateX: 0
                            });
                        }
                    }
                }
            });
        },
        _shiftThroughOrthogonalAxisOverlappedTick (label, orthogonalAxis) {
            const labelBBox = label.getBBox();
            const orthogonalAxisPosition = orthogonalAxis.getAxisPosition();
            const orthogonalAxisLabelOptions = orthogonalAxis.getOptions().label;
            const orthogonalAxisLabelPosition = orthogonalAxisLabelOptions.position;
            const orthogonalAxisLabelIndent = orthogonalAxisLabelOptions.indentFromAxis / 2;
            const translateCoordName = this._isHorizontal ? "translateX" : "translateY";
            const defaultOrthogonalAxisLabelPosition = this._isHorizontal ? LEFT : TOP;
            const translate = label.attr(translateCoordName);
            const labelCoord = (this._isHorizontal ? labelBBox.x : labelBBox.y) + translate;
            const labelSize = this._isHorizontal ? labelBBox.width : labelBBox.height;
            const outsidePart = orthogonalAxisPosition - labelCoord;
            const insidePart = labelCoord + labelSize - orthogonalAxisPosition;
            const attr = {};
            attr[translateCoordName] = translate;
            if (outsidePart > 0 && insidePart > 0) {
                if (insidePart - outsidePart > 1) {
                    attr[translateCoordName] += outsidePart + orthogonalAxisLabelIndent;
                } else if (outsidePart - insidePart > 1) {
                    attr[translateCoordName] -= insidePart + orthogonalAxisLabelIndent;
                } else {
                    attr[translateCoordName] += orthogonalAxisLabelPosition === defaultOrthogonalAxisLabelPosition ? outsidePart + orthogonalAxisLabelIndent : -(insidePart + orthogonalAxisLabelIndent);
                }
                label.attr(attr);
            }
        },
        _shiftThroughAxisOverlappedTick (tick) {
            var _tick$mark;
            const that = this;
            const label = tick.label;
            if (!label) {
                return;
            }
            const labelBBox = label.getBBox();
            const tickMarkBBox = null === (_tick$mark = tick.mark) || void 0 === _tick$mark ? void 0 : _tick$mark.getBBox();
            const axisPosition = that.getAxisPosition();
            const labelOptions = that.getOptions().label;
            const labelIndent = labelOptions.indentFromAxis;
            const labelPosition = labelOptions.position;
            const defaultLabelPosition = that._isHorizontal ? TOP : LEFT;
            const translateCoordName = that._isHorizontal ? "translateY" : "translateX";
            const translate = label.attr(translateCoordName);
            const labelCoord = (that._isHorizontal ? labelBBox.y : labelBBox.x) + translate;
            const labelSize = that._isHorizontal ? labelBBox.height : labelBBox.width;
            const attr = {};
            attr[translateCoordName] = translate + (labelPosition === defaultLabelPosition ? axisPosition - labelCoord + labelIndent : -(labelCoord - axisPosition + labelSize + labelIndent));
            label.attr(attr);
            if (tick.mark) {
                const markerSize = that._isHorizontal ? tickMarkBBox.height : tickMarkBBox.width;
                const dir = labelPosition === defaultLabelPosition ? 1 : -1;
                attr[translateCoordName] = dir * (markerSize - 1);
                tick.mark.attr(attr);
            }
        },
        _detectElementsOverlapping (element1, element2) {
            if (!element1 || !element2) {
                return false;
            }
            const bBox1 = element1.getBBox();
            const x1 = bBox1.x + element1.attr("translateX");
            const y1 = bBox1.y + element1.attr("translateY");
            const bBox2 = element2.getBBox();
            const x2 = bBox2.x + element2.attr("translateX");
            const y2 = bBox2.y + element2.attr("translateY");
            return (x2 >= x1 && x2 <= x1 + bBox1.width || x1 >= x2 && x1 <= x2 + bBox2.width) && (y2 >= y1 && y2 <= y1 + bBox1.height || y1 >= y2 && y1 <= y2 + bBox2.height);
        }
    }
};
function getLineDrawer(renderer, root, rotatePoints, positionFrom, breakStart, positionTo, isWaved) {
    const elementType = isWaved ? "bezier" : "line";
    const group = renderer.g().append(root);
    return function(offset, attr) {
        renderer.path(rotatePoints(getPoints(positionFrom, breakStart, positionTo, offset, isWaved)), elementType).attr(attr).append(group);
    };
}
function getPoints(positionFrom, breakStart, positionTo, offset, isWaved) {
    if (!isWaved) {
        return [
            positionFrom,
            breakStart + offset,
            positionTo,
            breakStart + offset
        ];
    }
    breakStart += offset;
    let currentPosition;
    const topPoint = breakStart + 0;
    const centerPoint = breakStart + 2;
    const bottomPoint = breakStart + 4;
    const points = [
        [
            positionFrom,
            centerPoint
        ]
    ];
    for(currentPosition = positionFrom; currentPosition < positionTo + 24; currentPosition += 24){
        points.push([
            currentPosition + 6,
            topPoint,
            currentPosition + 6,
            topPoint,
            currentPosition + 12,
            centerPoint,
            currentPosition + 18,
            bottomPoint,
            currentPosition + 18,
            bottomPoint,
            currentPosition + 24,
            centerPoint
        ]);
    }
    return [].concat.apply([], points);
}
function rotateLine(lineCoords) {
    const points = [];
    let i;
    for(i = 0; i < lineCoords.length; i += 2){
        points.push(lineCoords[i + 1]);
        points.push(lineCoords[i]);
    }
    return points;
}
}),
"[project]/node_modules/devextreme/esm/viz/axes/axes_utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/axes_utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "calculateCanvasMargins": ()=>calculateCanvasMargins,
    "measureLabels": ()=>measureLabels
});
const _max = Math.max;
const calculateCanvasMargins = function(bBoxes, canvas) {
    const cLeft = canvas.left;
    const cTop = canvas.top;
    const cRight = canvas.width - canvas.right;
    const cBottom = canvas.height - canvas.bottom;
    return bBoxes.reduce(function(margins, bBox) {
        if (!bBox || bBox.isEmpty) {
            return margins;
        }
        return {
            left: _max(margins.left, cLeft - bBox.x),
            top: _max(margins.top, cTop - bBox.y),
            right: _max(margins.right, bBox.x + bBox.width - cRight),
            bottom: _max(margins.bottom, bBox.y + bBox.height - cBottom)
        };
    }, {
        left: 0,
        right: 0,
        top: 0,
        bottom: 0
    });
};
const measureLabels = function(items) {
    items.forEach(function(item) {
        const label = item.getContentContainer();
        item.labelBBox = label ? label.getBBox() : {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
    });
};
}),
"[project]/node_modules/devextreme/esm/viz/axes/polar_axes.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/polar_axes.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "circular": ()=>circular,
    "circularSpider": ()=>circularSpider,
    "linear": ()=>linear,
    "linearSpider": ()=>linearSpider
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/axes_constants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$xy_axes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/xy_axes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/tick.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/axes_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
const { PI: PI, abs: abs, atan: atan, round: round } = Math;
const _min = Math.min;
const _max = Math.max;
const xyAxesLinear = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$xy_axes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].linear;
const HALF_PI_ANGLE = 90;
function getPolarQuarter(angle) {
    let quarter;
    angle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(angle);
    if (angle >= 315 && angle <= 360 || angle < 45 && angle >= 0) {
        quarter = 1;
    } else if (angle >= 45 && angle < 135) {
        quarter = 2;
    } else if (angle >= 135 && angle < 225) {
        quarter = 3;
    } else if (angle >= 225 && angle < 315) {
        quarter = 4;
    }
    return quarter;
}
const circularAxes = {
    _calculateValueMargins (ticks) {
        let { minVisible: minVisible, maxVisible: maxVisible } = this._getViewportRange();
        if (ticks && ticks.length > 1) {
            minVisible = minVisible < ticks[0].value ? minVisible : ticks[0].value;
            maxVisible = minVisible > ticks[ticks.length - 1].value ? maxVisible : ticks[ticks.length - 1].value;
        }
        return {
            minValue: minVisible,
            maxValue: maxVisible
        };
    },
    applyMargins () {
        const margins = this._calculateValueMargins(this._majorTicks);
        const br = this._translator.getBusinessRange();
        br.addRange({
            minVisible: margins.minValue,
            maxVisible: margins.maxValue,
            interval: this._calculateRangeInterval(br.interval)
        });
        this._translator.updateBusinessRange(br);
    },
    _getTranslatorOptions: function() {
        return {
            isHorizontal: true,
            conversionValue: true,
            addSpiderCategory: this._getSpiderCategoryOption(),
            stick: this._getStick()
        };
    },
    getCenter: function() {
        return this._center;
    },
    getRadius: function() {
        return this._radius;
    },
    getAngles: function() {
        const options = this._options;
        return [
            options.startAngle,
            options.endAngle
        ];
    },
    _updateRadius (canvas) {
        const rad = _min(canvas.width - canvas.left - canvas.right, canvas.height - canvas.top - canvas.bottom) / 2;
        this._radius = rad < 0 ? 0 : rad;
    },
    _updateCenter: function(canvas) {
        this._center = {
            x: canvas.left + (canvas.width - canvas.right - canvas.left) / 2,
            y: canvas.top + (canvas.height - canvas.top - canvas.bottom) / 2
        };
    },
    _processCanvas: function(canvas) {
        this._updateRadius(canvas);
        this._updateCenter(canvas);
        return {
            left: 0,
            right: 0,
            width: this._getScreenDelta()
        };
    },
    _createAxisElement: function() {
        return this._renderer.circle();
    },
    _updateAxisElementPosition: function() {
        const center = this.getCenter();
        this._axisElement.attr({
            cx: center.x,
            cy: center.y,
            r: this.getRadius()
        });
    },
    _boundaryTicksVisibility: {
        min: true
    },
    _getSpiderCategoryOption: function() {
        return this._options.firstPointOnStartAngle;
    },
    _validateOptions (options) {
        const that = this;
        let originValue = options.originValue;
        const wholeRange = options.wholeRange = {};
        const period = options.period;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(originValue)) {
            originValue = that.validateUnit(originValue);
        }
        if (period > 0 && options.argumentType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].numeric) {
            originValue = originValue || 0;
            wholeRange.endValue = originValue + period;
            that._viewport = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])([
                originValue,
                wholeRange.endValue
            ]);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(originValue)) {
            wholeRange.startValue = originValue;
        }
    },
    getMargins () {
        const tickOptions = this._options.tick;
        const tickOuterLength = _max(tickOptions.visible ? tickOptions.length / 2 + tickOptions.shift : 0, 0);
        const radius = this.getRadius();
        const { x: x, y: y } = this._center;
        const labelBoxes = this._majorTicks.map((t)=>t.label && t.label.getBBox()).filter((b)=>b);
        const canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, this._canvas, {
            left: x - radius,
            top: y - radius,
            right: this._canvas.width - (x + radius),
            bottom: this._canvas.height - (y + radius)
        });
        const margins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateCanvasMargins"])(labelBoxes, canvas);
        Object.keys(margins).forEach((k)=>margins[k] = margins[k] < tickOuterLength ? tickOuterLength : margins[k]);
        return margins;
    },
    _updateLabelsPosition () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(this._majorTicks);
        this._adjustLabelsCoord(0, 0, true);
        this._checkBoundedLabelsOverlapping(this._majorTicks, this._majorTicks.map((t)=>t.labelBBox));
    },
    _setVisualRange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    applyVisualRangeSetter: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _getStick: function() {
        return this._options.firstPointOnStartAngle || this._options.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete;
    },
    _getTranslatedCoord: function(value, offset) {
        return this._translator.translate(value, offset) - 90;
    },
    _getCanvasStartEnd: function() {
        return {
            start: -90,
            end: 270
        };
    },
    _getStripGraphicAttributes: function(fromAngle, toAngle) {
        const center = this.getCenter();
        const angle = this.getAngles()[0];
        const r = this.getRadius();
        return {
            x: center.x,
            y: center.y,
            innerRadius: 0,
            outerRadius: r,
            startAngle: -toAngle - angle,
            endAngle: -fromAngle - angle
        };
    },
    _createStrip: function(coords) {
        return this._renderer.arc(coords.x, coords.y, coords.innerRadius, coords.outerRadius, coords.startAngle, coords.endAngle);
    },
    _getStripLabelCoords: function(from, to) {
        const coords = this._getStripGraphicAttributes(from, to);
        const angle = coords.startAngle + (coords.endAngle - coords.startAngle) / 2;
        const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(angle);
        const halfRad = this.getRadius() / 2;
        const center = this.getCenter();
        const x = round(center.x + halfRad * cosSin.cos);
        const y = round(center.y - halfRad * cosSin.sin);
        return {
            x: x,
            y: y,
            align: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].center
        };
    },
    _getConstantLineGraphicAttributes: function(value) {
        const center = this.getCenter();
        const r = this.getRadius();
        return {
            points: [
                center.x,
                center.y,
                center.x + r,
                center.y
            ]
        };
    },
    _createConstantLine: function(value, attr) {
        return this._createPathElement(this._getConstantLineGraphicAttributes(value).points, attr);
    },
    _rotateConstantLine (line, value) {
        const { x: x, y: y } = this.getCenter();
        line.rotate(value + this.getAngles()[0], x, y);
    },
    _getConstantLineLabelsCoords: function(value) {
        const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(-value - this.getAngles()[0]);
        const halfRad = this.getRadius() / 2;
        const center = this.getCenter();
        const x = round(center.x + halfRad * cosSin.cos);
        const y = round(center.y - halfRad * cosSin.sin);
        return {
            x: x,
            y: y
        };
    },
    _checkAlignmentConstantLineLabels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _adjustDivisionFactor: function(val) {
        return 180 * val / (this.getRadius() * PI);
    },
    _getScreenDelta: function() {
        const angles = this.getAngles();
        return abs(angles[0] - angles[1]);
    },
    _getTickMarkPoints: function(coords, length, _ref) {
        let { shift: shift = 0 } = _ref;
        const center = this.getCenter();
        const radiusWithTicks = this.getRadius() + length * ({
            inside: -1,
            center: -.5,
            outside: 0
        })[this._options.tickOrientation || "center"];
        return [
            center.x + radiusWithTicks + shift,
            center.y,
            center.x + radiusWithTicks + length + shift,
            center.y
        ];
    },
    _getLabelAdjustedCoord: function(tick, _offset, _maxWidth, checkCanvas) {
        const that = this;
        const labelCoords = tick.labelCoords;
        const labelY = labelCoords.y;
        const labelAngle = labelCoords.angle;
        const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(labelAngle);
        const cos = cosSin.cos;
        const sin = cosSin.sin;
        const box = tick.labelBBox;
        const halfWidth = box.width / 2;
        const halfHeight = box.height / 2;
        const indentFromAxis = that._options.label.indentFromAxis || 0;
        const x = labelCoords.x + indentFromAxis * cos;
        const y = labelY + (labelY - box.y - halfHeight) + indentFromAxis * sin;
        let shiftX = 0;
        let shiftY = 0;
        switch(getPolarQuarter(labelAngle)){
            case 1:
                shiftX = halfWidth;
                shiftY = halfHeight * sin;
                break;
            case 2:
                shiftX = halfWidth * cos;
                shiftY = halfHeight;
                break;
            case 3:
                shiftX = -halfWidth;
                shiftY = halfHeight * sin;
                break;
            case 4:
                shiftX = halfWidth * cos;
                shiftY = -halfHeight;
        }
        if (checkCanvas) {
            const canvas = that._canvas;
            const boxShiftX = x - labelCoords.x + shiftX;
            const boxShiftY = y - labelCoords.y + shiftY;
            if (box.x + boxShiftX < canvas.originalLeft) {
                shiftX -= box.x + boxShiftX - canvas.originalLeft;
            }
            if (box.x + box.width + boxShiftX > canvas.width - canvas.originalRight) {
                shiftX -= box.x + box.width + boxShiftX - (canvas.width - canvas.originalRight);
            }
            if (box.y + boxShiftY < canvas.originalTop) {
                shiftY -= box.y + boxShiftY - canvas.originalTop;
            }
            if (box.y + box.height + boxShiftY > canvas.height - canvas.originalBottom) {
                shiftY -= box.y + box.height + boxShiftY - (canvas.height - canvas.originalBottom);
            }
        }
        return {
            x: x + shiftX,
            y: y + shiftY
        };
    },
    _getGridLineDrawer: function() {
        const that = this;
        return function(tick, gridStyle) {
            const center = that.getCenter();
            return that._createPathElement(that._getGridPoints().points, gridStyle).rotate(tick.coords.angle, center.x, center.y);
        };
    },
    _getGridPoints: function() {
        const r = this.getRadius();
        const center = this.getCenter();
        return {
            points: [
                center.x,
                center.y,
                center.x + r,
                center.y
            ]
        };
    },
    _getTranslatedValue: function(value, offset) {
        const startAngle = this.getAngles()[0];
        const angle = this._translator.translate(value, -offset);
        const coords = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertPolarToXY"])(this.getCenter(), startAngle, angle, this.getRadius());
        return {
            x: coords.x,
            y: coords.y,
            angle: this.getTranslatedAngle(angle)
        };
    },
    _getAdjustedStripLabelCoords: function(strip) {
        const box = strip.labelBBox;
        return {
            translateY: strip.label.attr("y") - box.y - box.height / 2
        };
    },
    coordsIn: function(x, y) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertXYToPolar"])(this.getCenter(), x, y).r > this.getRadius();
    },
    _rotateTick: function(element, coords) {
        const center = this.getCenter();
        element.rotate(coords.angle, center.x, center.y);
    },
    _validateOverlappingMode: function(mode) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].validateOverlappingMode(mode);
    },
    _validateDisplayMode: function() {
        return "standard";
    },
    _getStep: function(boxes) {
        const radius = this.getRadius() + (this._options.label.indentFromAxis || 0);
        const maxLabelBox = boxes.reduce(function(prevValue, box) {
            const curValue = prevValue;
            if (prevValue.width < box.width) {
                curValue.width = box.width;
            }
            if (prevValue.height < box.height) {
                curValue.height = box.height;
            }
            return curValue;
        }, {
            width: 0,
            height: 0
        });
        const angle1 = abs(2 * atan(maxLabelBox.height / (2 * radius - maxLabelBox.width)) * 180 / PI);
        const angle2 = abs(2 * atan(maxLabelBox.width / (2 * radius - maxLabelBox.height)) * 180 / PI);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTicksCountInRange(this._majorTicks, "angle", _max(angle1, angle2));
    },
    _checkBoundedLabelsOverlapping: function(majorTicks, boxes, mode) {
        const labelOpt = this._options.label;
        mode = mode || this._validateOverlappingMode(labelOpt.overlappingBehavior);
        if ("hide" !== mode) {
            return;
        }
        const lastVisibleLabelIndex = majorTicks.reduce((lastVisibleLabelIndex, tick, index)=>tick.label ? index : lastVisibleLabelIndex, null);
        if (!lastVisibleLabelIndex) {
            return;
        }
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].areLabelsOverlap(boxes[0], boxes[lastVisibleLabelIndex], labelOpt.minSpacing, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].center)) {
            "first" === labelOpt.hideFirstOrLast ? majorTicks[0].removeLabel() : majorTicks[lastVisibleLabelIndex].removeLabel();
        }
    },
    shift: function(margins) {
        this._axisGroup.attr({
            translateX: margins.right,
            translateY: margins.bottom
        });
        this._axisElementsGroup.attr({
            translateX: margins.right,
            translateY: margins.bottom
        });
    },
    getTranslatedAngle (angle) {
        const startAngle = this.getAngles()[0];
        return angle + startAngle - 90;
    }
};
const circular = circularAxes;
const circularSpider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, circularAxes, {
    _createAxisElement: function() {
        return this._renderer.path([], "area");
    },
    _updateAxisElementPosition: function() {
        this._axisElement.attr({
            points: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(this.getSpiderTicks(), function(tick) {
                return {
                    x: tick.coords.x,
                    y: tick.coords.y
                };
            })
        });
    },
    _getStick: function() {
        return true;
    },
    _getSpiderCategoryOption: function() {
        return true;
    },
    getSpiderTicks: function() {
        const ticks = this.getFullTicks();
        this._spiderTicks = ticks.map((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tick"])(this, this.renderer, {}, {}, this._getSkippedCategory(ticks), true));
        this._spiderTicks.forEach(function(tick) {
            tick.initCoords();
        });
        return this._spiderTicks;
    },
    _getStripGraphicAttributes: function(fromAngle, toAngle) {
        const center = this.getCenter();
        const spiderTicks = this.getSpiderTicks();
        let firstTick;
        let lastTick;
        let nextTick;
        let tick;
        const points = [];
        let i = 0;
        const len = spiderTicks.length;
        while(i < len){
            tick = spiderTicks[i].coords;
            if (tick.angle >= fromAngle && tick.angle <= toAngle) {
                if (!firstTick) {
                    firstTick = (spiderTicks[i - 1] || spiderTicks[spiderTicks.length - 1]).coords;
                    points.push((tick.x + firstTick.x) / 2, (tick.y + firstTick.y) / 2);
                }
                points.push(tick.x, tick.y);
                nextTick = (spiderTicks[i + 1] || spiderTicks[0]).coords;
                lastTick = {
                    x: (tick.x + nextTick.x) / 2,
                    y: (tick.y + nextTick.y) / 2
                };
            }
            i++;
        }
        points.push(lastTick.x, lastTick.y);
        points.push(center.x, center.y);
        return {
            points: points
        };
    },
    _createStrip: function(_ref2) {
        let { points: points } = _ref2;
        return this._renderer.path(points, "area");
    },
    _getTranslatedCoord: function(value, offset) {
        return this._translator.translate(value, offset) - 90;
    },
    _setTickOffset: function() {
        this._tickOffset = false;
    }
});
const linear = {
    _resetMargins () {
        this._reinitTranslator(this._getViewportRange());
    },
    _getStick: xyAxesLinear._getStick,
    _getSpiderCategoryOption: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _getTranslatorOptions: function() {
        return {
            isHorizontal: true,
            stick: this._getStick()
        };
    },
    getRadius: circularAxes.getRadius,
    getCenter: circularAxes.getCenter,
    getAngles: circularAxes.getAngles,
    _updateRadius: circularAxes._updateRadius,
    _updateCenter: circularAxes._updateCenter,
    _processCanvas (canvas) {
        this._updateRadius(canvas);
        this._updateCenter(canvas);
        return {
            left: 0,
            right: 0,
            startPadding: canvas.startPadding,
            endPadding: canvas.endPadding,
            width: this.getRadius()
        };
    },
    _createAxisElement: xyAxesLinear._createAxisElement,
    _updateAxisElementPosition: function() {
        const centerCoord = this.getCenter();
        this._axisElement.attr({
            points: [
                centerCoord.x,
                centerCoord.y,
                centerCoord.x + this.getRadius(),
                centerCoord.y
            ]
        }).rotate(this.getAngles()[0] - 90, centerCoord.x, centerCoord.y);
    },
    _getScreenDelta: function() {
        return this.getRadius();
    },
    _getTickMarkPoints: function(coords, length) {
        return [
            coords.x - length / 2,
            coords.y,
            coords.x + length / 2,
            coords.y
        ];
    },
    _getLabelAdjustedCoord: function(tick) {
        const labelCoords = tick.labelCoords;
        const labelY = labelCoords.y;
        const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(labelCoords.angle);
        const indentFromAxis = this._options.label.indentFromAxis || 0;
        const box = tick.labelBBox;
        const x = labelCoords.x - abs(indentFromAxis * cosSin.sin) + abs(box.width / 2 * cosSin.cos) - box.width / 2;
        const y = labelY + (labelY - box.y) - abs(box.height / 2 * cosSin.sin) + abs(indentFromAxis * cosSin.cos);
        return {
            x: x,
            y: y
        };
    },
    _getGridLineDrawer: function() {
        const that = this;
        return function(tick, gridStyle) {
            const grid = that._getGridPoints(tick.coords);
            return that._renderer.circle(grid.cx, grid.cy, grid.r).attr(gridStyle).sharp();
        };
    },
    _getGridPoints: function(coords) {
        const pos = this.getCenter();
        const radius = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDistance"])(pos.x, pos.y, coords.x, coords.y);
        if (radius > this.getRadius()) {
            return {
                cx: null,
                cy: null,
                r: null
            };
        }
        return {
            cx: pos.x,
            cy: pos.y,
            r: radius
        };
    },
    _getTranslatedValue: function(value, offset) {
        const startAngle = this.getAngles()[0];
        const xy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertPolarToXY"])(this.getCenter(), startAngle, 0, this._translator.translate(value, offset));
        return {
            x: xy.x,
            y: xy.y,
            angle: startAngle - 90
        };
    },
    _getTranslatedCoord: function(value, offset) {
        return this._translator.translate(value, offset);
    },
    _getCanvasStartEnd () {
        const invert = this.getTranslator().getBusinessRange().invert;
        const coords = [
            0,
            this.getRadius()
        ];
        invert && coords.reverse();
        return {
            start: coords[0],
            end: coords[1]
        };
    },
    _getStripGraphicAttributes: function(fromPoint, toPoint) {
        const center = this.getCenter();
        return {
            x: center.x,
            y: center.y,
            innerRadius: fromPoint,
            outerRadius: toPoint
        };
    },
    _createStrip: function(attrs) {
        return this._renderer.arc(attrs.x, attrs.y, attrs.innerRadius, attrs.outerRadius, 0, 360);
    },
    _getAdjustedStripLabelCoords: circularAxes._getAdjustedStripLabelCoords,
    _getStripLabelCoords: function(from, to) {
        const labelPos = from + (to - from) / 2;
        const center = this.getCenter();
        const y = round(center.y - labelPos);
        return {
            x: center.x,
            y: y,
            align: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].center
        };
    },
    _getConstantLineGraphicAttributes: function(value) {
        const center = this.getCenter();
        return {
            cx: center.x,
            cy: center.y,
            r: value
        };
    },
    _createConstantLine: function(value, attr) {
        const attrs = this._getConstantLineGraphicAttributes(value);
        return this._renderer.circle(attrs.cx, attrs.cy, attrs.r).attr(attr).sharp();
    },
    _getConstantLineLabelsCoords: function(value) {
        const center = this.getCenter();
        const y = round(center.y - value);
        return {
            x: center.x,
            y: y
        };
    },
    _checkAlignmentConstantLineLabels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _rotateTick: function(element, coords, isGridLine) {
        !isGridLine && element.rotate(coords.angle + 90, coords.x, coords.y);
    },
    _validateOverlappingMode: circularAxes._validateOverlappingMode,
    _validateDisplayMode: circularAxes._validateDisplayMode,
    _getStep: function(boxes) {
        const quarter = getPolarQuarter(this.getAngles()[0]);
        const spacing = this._options.label.minSpacing;
        const func = 2 === quarter || 4 === quarter ? function(box) {
            return box.width + spacing;
        } : function(box) {
            return box.height;
        };
        const maxLabelLength = boxes.reduce((prevValue, box)=>_max(prevValue, func(box)), 0);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTicksCountInRange(this._majorTicks, 2 === quarter || 4 === quarter ? "x" : "y", maxLabelLength);
    }
};
const linearSpider = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, linear, {
    _createPathElement: function(points, attr) {
        return this._renderer.path(points, "area").attr(attr).sharp();
    },
    setSpiderTicks: function(ticks) {
        this._spiderTicks = ticks;
    },
    _getGridLineDrawer: function() {
        const that = this;
        return function(tick, gridStyle) {
            return that._createPathElement(that._getGridPoints(tick.coords).points, gridStyle);
        };
    },
    _getGridPoints: function(coords) {
        const pos = this.getCenter();
        const radius = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDistance"])(pos.x, pos.y, coords.x, coords.y);
        return this._getGridPointsByRadius(radius);
    },
    _getGridPointsByRadius: function(radius) {
        const pos = this.getCenter();
        if (radius > this.getRadius()) {
            return {
                points: null
            };
        }
        return {
            points: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(this._spiderTicks, function(tick) {
                const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(tick.coords.angle);
                return {
                    x: round(pos.x + radius * cosSin.cos),
                    y: round(pos.y + radius * cosSin.sin)
                };
            })
        };
    },
    _getStripGraphicAttributes: function(fromPoint, toPoint) {
        const innerPoints = this._getGridPointsByRadius(toPoint).points;
        const outerPoints = this._getGridPointsByRadius(fromPoint).points;
        return {
            points: [
                outerPoints,
                innerPoints.reverse()
            ]
        };
    },
    _createStrip: circularSpider._createStrip,
    _getConstantLineGraphicAttributes: function(value) {
        return this._getGridPointsByRadius(value);
    },
    _createConstantLine: function(value, attr) {
        return this._createPathElement(this._getConstantLineGraphicAttributes(value).points, attr);
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/axes/constant_line.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/constant_line.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>createConstantLine
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
;
function createConstantLine(axis, options) {
    const labelOptions = options.label || {};
    const labelPosition = labelOptions.position || "inside";
    let parsedValue;
    let valueIsParsed = false;
    let lastStoredCoordinates;
    axis._checkAlignmentConstantLineLabels(labelOptions);
    let storedCoord;
    return {
        options: options,
        labelOptions: labelOptions,
        labelPosition: labelPosition,
        label: null,
        line: null,
        getParsedValue () {
            if (!valueIsParsed) {
                parsedValue = axis.validateUnit(options.value, "E2105", "constantLine");
                valueIsParsed = true;
                return parsedValue;
            }
            return parsedValue;
        },
        draw () {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.value) || axis._translator.getBusinessRange().isEmpty()) {
                return this;
            }
            const canvas = axis._getCanvasStartEnd();
            const parsedValue = this.getParsedValue();
            this.coord = axis._getConstantLinePos(parsedValue, canvas.start, canvas.end);
            const rootGroup = options.displayBehindSeries ? axis._axisConstantLineGroups.under : axis._axisConstantLineGroups.above;
            let group = rootGroup[labelPosition];
            if (!group) {
                const side = axis._isHorizontal ? labelOptions.verticalAlignment : labelOptions.horizontalAlignment;
                group = rootGroup[side];
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.coord)) {
                return this;
            }
            const path = axis._createConstantLine(this.coord, {
                stroke: options.color,
                "stroke-width": options.width,
                dashStyle: options.dashStyle
            });
            this.line = path.append(rootGroup.inside);
            this.label = labelOptions.visible ? axis._drawConstantLineLabels(parsedValue, labelOptions, this.coord, group) : null;
            this.updatePosition();
            return this;
        },
        getContentContainer () {
            return this.label;
        },
        removeLabel () {
            this.label && this.label.remove();
        },
        updatePosition (animate) {
            const canvas = axis._getCanvasStartEnd();
            const coord = axis._getConstantLinePos(this.getParsedValue(), canvas.start, canvas.end);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(coord)) {
                return;
            }
            this.coord = coord;
            if (animate && storedCoord) {
                this.label && this.label.attr(axis._getConstantLineLabelsCoords(storedCoord, this.labelOptions));
                this.line && this.line.attr(axis._getConstantLineGraphicAttributes(storedCoord));
                this.label && this.label.animate(axis._getConstantLineLabelsCoords(this.coord, this.labelOptions));
                this.line && this.line.animate(axis._getConstantLineGraphicAttributes(this.coord));
            } else {
                this.label && this.label.attr(axis._getConstantLineLabelsCoords(this.coord, this.labelOptions));
                this.line && this.line.attr(axis._getConstantLineGraphicAttributes(this.coord));
                axis._rotateConstantLine(this.line, this.coord);
            }
        },
        saveCoords () {
            lastStoredCoordinates = storedCoord;
            storedCoord = this.coord;
        },
        resetCoordinates () {
            storedCoord = lastStoredCoordinates;
        }
    };
}
}),
"[project]/node_modules/devextreme/esm/viz/axes/strip.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/strip.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>createStrip
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
;
;
function createStrip(axis, options) {
    let storedCoord;
    let lastStoredCoordinates;
    const labelOptions = options.label || {};
    return {
        options: options,
        label: null,
        rect: null,
        _getCoord () {
            const canvas = axis._getCanvasStartEnd();
            const range = axis._translator.getBusinessRange();
            return axis._getStripPos(options.startValue, options.endValue, canvas.start, canvas.end, range);
        },
        _drawLabel: (coords)=>axis._renderer.text(labelOptions.text, coords.x, coords.y).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["patchFontOptions"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, axis.getOptions().label.font, labelOptions.font))).attr({
                align: "center",
                class: labelOptions.cssClass
            }).append(axis._axisStripLabelGroup),
        draw () {
            if (axis._translator.getBusinessRange().isEmpty()) {
                return;
            }
            if (((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.startValue) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.endValue)) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.color)) {
                const stripPos = this._getCoord();
                this.labelCoords = labelOptions.text ? axis._getStripLabelCoords(stripPos.from, stripPos.to, labelOptions) : null;
                if (stripPos.outOfCanvas || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(stripPos.to) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(stripPos.from)) {
                    return;
                }
                this.rect = axis._createStrip(axis._getStripGraphicAttributes(stripPos.from, stripPos.to)).attr({
                    fill: options.color
                }).append(axis._axisStripGroup);
                this.label = labelOptions.text ? this._drawLabel(this.labelCoords) : null;
            }
        },
        getContentContainer () {
            return this.label;
        },
        removeLabel () {},
        updatePosition (animate) {
            const stripPos = this._getCoord();
            if (animate && storedCoord) {
                this.label && this.label.attr(axis._getStripLabelCoords(storedCoord.from, storedCoord.to, options.label));
                this.rect && this.rect.attr(axis._getStripGraphicAttributes(storedCoord.from, storedCoord.to));
                this.label && this.label.animate(axis._getStripLabelCoords(stripPos.from, stripPos.to, options.label));
                this.rect && this.rect.animate(axis._getStripGraphicAttributes(stripPos.from, stripPos.to));
            } else {
                this.label && this.label.attr(axis._getStripLabelCoords(stripPos.from, stripPos.to, options.label));
                this.rect && this.rect.attr(axis._getStripGraphicAttributes(stripPos.from, stripPos.to));
            }
        },
        saveCoords () {
            lastStoredCoordinates = storedCoord;
            storedCoord = this._getCoord();
        },
        resetCoordinates () {
            storedCoord = lastStoredCoordinates;
        }
    };
}
}),
"[project]/node_modules/devextreme/esm/viz/axes/base_axis.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/axes/base_axis.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Axis": ()=>Axis
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$smart_formatter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/smart_formatter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/axes_constants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/format_helper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$parse_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/parse_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick_generator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/tick_generator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$translator2d$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/translator2d.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/range.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/tick.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$xy_axes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/xy_axes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$polar_axes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/polar_axes.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$constant_line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/constant_line.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$strip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/strip.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/axes_utils.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const convertTicksToValues = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertTicksToValues;
const _math = Math;
const _abs = _math.abs;
const _max = _math.max;
const _min = _math.min;
const _isArray = Array.isArray;
const DEFAULT_AXIS_LABEL_SPACING = 5;
const MAX_GRID_BORDER_ADHENSION = 4;
const TOP = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].top;
const BOTTOM = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].bottom;
const LEFT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].left;
const RIGHT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].right;
const CENTER = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].center;
const KEEP = "keep";
const SHIFT = "shift";
const RESET = "reset";
const ROTATE = "rotate";
const DEFAULT_AXIS_DIVISION_FACTOR = 50;
const DEFAULT_MINOR_AXIS_DIVISION_FACTOR = 15;
const SCROLL_THRESHOLD = 5;
const MIN_BAR_MARGIN = 5;
const MAX_MARGIN_VALUE = .8;
const dateIntervals = {
    day: 864e5,
    week: 6048e5
};
function getTickGenerator(options, incidentOccurred, skipTickGeneration, rangeIsEmpty, adjustDivisionFactor, _ref) {
    var _options$workWeek;
    let { allowNegatives: allowNegatives, linearThreshold: linearThreshold } = _ref;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick_generator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tickGenerator"])({
        axisType: options.type,
        dataType: options.dataType,
        logBase: options.logarithmBase,
        allowNegatives: allowNegatives,
        linearThreshold: linearThreshold,
        axisDivisionFactor: adjustDivisionFactor(options.axisDivisionFactor || 50),
        minorAxisDivisionFactor: adjustDivisionFactor(options.minorAxisDivisionFactor || 15),
        numberMultipliers: options.numberMultipliers,
        calculateMinors: options.minorTick.visible || options.minorGrid.visible || options.calculateMinors,
        allowDecimals: options.allowDecimals,
        endOnTick: options.endOnTick,
        incidentOccurred: incidentOccurred,
        firstDayOfWeek: null === (_options$workWeek = options.workWeek) || void 0 === _options$workWeek ? void 0 : _options$workWeek[0],
        skipTickGeneration: skipTickGeneration,
        skipCalculationLimits: options.skipCalculationLimits,
        generateExtraTick: options.generateExtraTick,
        minTickInterval: options.minTickInterval,
        rangeIsEmpty: rangeIsEmpty
    });
}
function createMajorTick(axis, renderer, skippedCategory) {
    const options = axis.getOptions();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tick"])(axis, renderer, options.tick, options.grid, skippedCategory, false);
}
function createMinorTick(axis, renderer) {
    const options = axis.getOptions();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tick"])(axis, renderer, options.minorTick, options.minorGrid);
}
function createBoundaryTick(axis, renderer, isFirst) {
    const options = axis.getOptions();
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$tick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["tick"])(axis, renderer, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, options.tick, {
        visible: options.showCustomBoundaryTicks
    }), options.grid, void 0, false, isFirst ? -1 : 1);
}
function callAction(elements, action, actionArgument1, actionArgument2) {
    (elements || []).forEach((e)=>e[action](actionArgument1, actionArgument2));
}
function initTickCoords(ticks) {
    callAction(ticks, "initCoords");
}
function drawTickMarks(ticks, options) {
    callAction(ticks, "drawMark", options);
}
function drawGrids(ticks, drawLine) {
    callAction(ticks, "drawGrid", drawLine);
}
function updateTicksPosition(ticks, options, animate) {
    callAction(ticks, "updateTickPosition", options, animate);
}
function updateGridsPosition(ticks, animate) {
    callAction(ticks, "updateGridPosition", animate);
}
function cleanUpInvalidTicks(ticks) {
    let i = ticks.length - 1;
    for(i; i >= 0; i--){
        if (!removeInvalidTick(ticks, i)) {
            break;
        }
    }
    for(i = 0; i < ticks.length; i++){
        if (removeInvalidTick(ticks, i)) {
            i--;
        } else {
            break;
        }
    }
}
function removeInvalidTick(ticks, i) {
    if (null === ticks[i].coords.x || null === ticks[i].coords.y) {
        ticks.splice(i, 1);
        return true;
    }
    return false;
}
function validateAxisOptions(options) {
    const labelOptions = options.label;
    let position = options.position;
    const defaultPosition = options.isHorizontal ? BOTTOM : LEFT;
    const secondaryPosition = options.isHorizontal ? TOP : RIGHT;
    let labelPosition = labelOptions.position;
    if (position !== defaultPosition && position !== secondaryPosition) {
        position = defaultPosition;
    }
    if (!labelPosition || "outside" === labelPosition) {
        labelPosition = position;
    } else if ("inside" === labelPosition) {
        labelPosition = ({
            [TOP]: BOTTOM,
            [BOTTOM]: TOP,
            [LEFT]: RIGHT,
            [RIGHT]: LEFT
        })[position];
    }
    if (labelPosition !== defaultPosition && labelPosition !== secondaryPosition) {
        labelPosition = position;
    }
    if (labelOptions.alignment !== CENTER && !labelOptions.userAlignment) {
        labelOptions.alignment = ({
            [TOP]: CENTER,
            [BOTTOM]: CENTER,
            [LEFT]: RIGHT,
            [RIGHT]: LEFT
        })[labelPosition];
    }
    options.position = position;
    labelOptions.position = labelPosition;
    options.hoverMode = options.hoverMode ? options.hoverMode.toLowerCase() : "none";
    var _labelOptions_minSpacing;
    labelOptions.minSpacing = (_labelOptions_minSpacing = labelOptions.minSpacing) !== null && _labelOptions_minSpacing !== void 0 ? _labelOptions_minSpacing : 5;
    options.type && (options.type = options.type.toLowerCase());
    options.argumentType && (options.argumentType = options.argumentType.toLowerCase());
    options.valueType && (options.valueType = options.valueType.toLowerCase());
}
function getOptimalAngle(boxes, labelOpt) {
    const angle = 180 * _math.asin((boxes[0].height + labelOpt.minSpacing) / (boxes[1].x - boxes[0].x)) / _math.PI;
    return angle < 45 ? -45 : -90;
}
function updateLabels(ticks, step, func) {
    ticks.forEach(function(tick, index) {
        if (tick.getContentContainer()) {
            if (index % step !== 0) {
                tick.removeLabel();
            } else if (func) {
                func(tick, index);
            }
        }
    });
}
function getZoomBoundValue(optionValue, dataValue) {
    if (void 0 === optionValue) {
        return dataValue;
    } else if (null === optionValue) {
        return;
    } else {
        return optionValue;
    }
}
function configureGenerator(options, axisDivisionFactor, viewPort, screenDelta, minTickInterval) {
    const tickGeneratorOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, options, {
        endOnTick: true,
        axisDivisionFactor: axisDivisionFactor,
        skipCalculationLimits: true,
        generateExtraTick: true,
        minTickInterval: minTickInterval
    });
    return function(tickInterval, skipTickGeneration, min, max, breaks) {
        return getTickGenerator(tickGeneratorOptions, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"], skipTickGeneration, viewPort.isEmpty(), (v)=>v, viewPort)({
            min: min,
            max: max,
            categories: viewPort.categories,
            isSpacedMargin: viewPort.isSpacedMargin
        }, screenDelta, tickInterval, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tickInterval), void 0, void 0, void 0, breaks);
    };
}
function getConstantLineSharpDirection(coord, axisCanvas) {
    return Math.max(axisCanvas.start, axisCanvas.end) !== coord ? 1 : -1;
}
const Axis = function(renderSettings) {
    this._renderer = renderSettings.renderer;
    this._incidentOccurred = renderSettings.incidentOccurred;
    this._eventTrigger = renderSettings.eventTrigger;
    this._stripsGroup = renderSettings.stripsGroup;
    this._stripLabelAxesGroup = renderSettings.stripLabelAxesGroup;
    this._labelsAxesGroup = renderSettings.labelsAxesGroup;
    this._constantLinesGroup = renderSettings.constantLinesGroup;
    this._scaleBreaksGroup = renderSettings.scaleBreaksGroup;
    this._axesContainerGroup = renderSettings.axesContainerGroup;
    this._gridContainerGroup = renderSettings.gridGroup;
    this._axisCssPrefix = renderSettings.widgetClass + "-" + (renderSettings.axisClass ? renderSettings.axisClass + "-" : "");
    this._setType(renderSettings.axisType, renderSettings.drawingType);
    this._createAxisGroups();
    this._translator = this._createTranslator();
    this.isArgumentAxis = renderSettings.isArgumentAxis;
    this._viewport = {};
    this._prevDataInfo = {};
    this._firstDrawing = true;
    this._initRange = {};
    this._getTemplate = renderSettings.getTemplate;
};
Axis.prototype = {
    constructor: Axis,
    _drawAxis () {
        const options = this._options;
        if (!options.visible) {
            return;
        }
        this._axisElement = this._createAxisElement();
        this._updateAxisElementPosition();
        this._axisElement.attr({
            "stroke-width": options.width,
            stroke: options.color,
            "stroke-opacity": options.opacity
        }).sharp(this._getSharpParam(true), this.getAxisSharpDirection()).append(this._axisLineGroup);
    },
    _createPathElement (points, attr, sharpDirection) {
        return this.sharp(this._renderer.path(points, "line").attr(attr), sharpDirection);
    },
    sharp (svgElement) {
        let sharpDirection = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 1;
        return svgElement.sharp(this._getSharpParam(), sharpDirection);
    },
    customPositionIsAvailable: ()=>false,
    getOrthogonalAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getCustomPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getCustomBoundaryPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    resolveOverlappingForCustomPositioning: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    hasNonBoundaryPosition: ()=>false,
    customPositionIsBoundaryOrthogonalAxis: ()=>false,
    getResolvedBoundaryPosition () {
        return this.getOptions().position;
    },
    getAxisSharpDirection () {
        const position = this.getResolvedBoundaryPosition();
        return this.hasNonBoundaryPosition() || position !== BOTTOM && position !== RIGHT ? 1 : -1;
    },
    getSharpDirectionByCoords (coords) {
        const canvas = this._getCanvasStartEnd();
        const maxCoord = Math.max(canvas.start, canvas.end);
        return this.getRadius ? 0 : maxCoord !== coords[this._isHorizontal ? "x" : "y"] ? 1 : -1;
    },
    _getGridLineDrawer: function() {
        const that = this;
        return function(tick, gridStyle) {
            const grid = that._getGridPoints(tick.coords);
            if (grid.points) {
                return that._createPathElement(grid.points, gridStyle, that.getSharpDirectionByCoords(tick.coords));
            }
            return null;
        };
    },
    _getGridPoints: function(coords) {
        const isHorizontal = this._isHorizontal;
        const tickPositionField = isHorizontal ? "x" : "y";
        const orthogonalPositions = this._orthogonalPositions;
        const positionFrom = orthogonalPositions.start;
        const positionTo = orthogonalPositions.end;
        const borderOptions = this.borderOptions;
        const canvasStart = isHorizontal ? LEFT : TOP;
        const canvasEnd = isHorizontal ? RIGHT : BOTTOM;
        const axisCanvas = this.getCanvas();
        const canvas = {
            left: axisCanvas.left,
            right: axisCanvas.width - axisCanvas.right,
            top: axisCanvas.top,
            bottom: axisCanvas.height - axisCanvas.bottom
        };
        const firstBorderLinePosition = borderOptions.visible && borderOptions[canvasStart] ? canvas[canvasStart] : void 0;
        const lastBorderLinePosition = borderOptions.visible && borderOptions[canvasEnd] ? canvas[canvasEnd] : void 0;
        const minDelta = 4 + firstBorderLinePosition;
        const maxDelta = lastBorderLinePosition - 4;
        if (this.areCoordsOutsideAxis(coords) || void 0 === coords[tickPositionField] || coords[tickPositionField] < minDelta || coords[tickPositionField] > maxDelta) {
            return {
                points: null
            };
        }
        return {
            points: isHorizontal ? null !== coords[tickPositionField] ? [
                coords[tickPositionField],
                positionFrom,
                coords[tickPositionField],
                positionTo
            ] : null : null !== coords[tickPositionField] ? [
                positionFrom,
                coords[tickPositionField],
                positionTo,
                coords[tickPositionField]
            ] : null
        };
    },
    _getConstantLinePos: function(parsedValue, canvasStart, canvasEnd) {
        const value = this._getTranslatedCoord(parsedValue);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value) || value < _min(canvasStart, canvasEnd) || value > _max(canvasStart, canvasEnd)) {
            return;
        }
        return value;
    },
    _getConstantLineGraphicAttributes: function(value) {
        const positionFrom = this._orthogonalPositions.start;
        const positionTo = this._orthogonalPositions.end;
        return {
            points: this._isHorizontal ? [
                value,
                positionFrom,
                value,
                positionTo
            ] : [
                positionFrom,
                value,
                positionTo,
                value
            ]
        };
    },
    _createConstantLine: function(value, attr) {
        return this._createPathElement(this._getConstantLineGraphicAttributes(value).points, attr, getConstantLineSharpDirection(value, this._getCanvasStartEnd()));
    },
    _drawConstantLineLabelText: function(text, x, y, _ref2, group) {
        let { font: font, cssClass: cssClass } = _ref2;
        return this._renderer.text(text, x, y).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["patchFontOptions"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, this._options.label.font, font))).attr({
            align: "center",
            class: cssClass
        }).append(group);
    },
    _drawConstantLineLabels: function(parsedValue, lineLabelOptions, value, group) {
        let text = lineLabelOptions.text;
        const options = this._options;
        const labelOptions = options.label;
        this._checkAlignmentConstantLineLabels(lineLabelOptions);
        text = text !== null && text !== void 0 ? text : this.formatLabel(parsedValue, labelOptions);
        const coords = this._getConstantLineLabelsCoords(value, lineLabelOptions);
        return this._drawConstantLineLabelText(text, coords.x, coords.y, lineLabelOptions, group);
    },
    _getStripPos: function(startValue, endValue, canvasStart, canvasEnd, range) {
        const isContinuous = !!(range.minVisible || range.maxVisible);
        const categories = (range.categories || []).reduce(function(result, cat) {
            result.push(cat.valueOf());
            return result;
        }, []);
        let start;
        let end;
        let swap;
        let startCategoryIndex;
        let endCategoryIndex;
        if (!isContinuous) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(startValue) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(endValue)) {
                const parsedStartValue = this.parser(startValue);
                const parsedEndValue = this.parser(endValue);
                var _ref;
                startCategoryIndex = categories.indexOf((_ref = null === parsedStartValue || void 0 === parsedStartValue ? void 0 : parsedStartValue.valueOf()) !== null && _ref !== void 0 ? _ref : void 0);
                var _ref1;
                endCategoryIndex = categories.indexOf((_ref1 = null === parsedEndValue || void 0 === parsedEndValue ? void 0 : parsedEndValue.valueOf()) !== null && _ref1 !== void 0 ? _ref1 : void 0);
                if (-1 === startCategoryIndex || -1 === endCategoryIndex) {
                    return {
                        from: 0,
                        to: 0,
                        outOfCanvas: true
                    };
                }
                if (startCategoryIndex > endCategoryIndex) {
                    swap = endValue;
                    endValue = startValue;
                    startValue = swap;
                }
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(startValue)) {
            startValue = this.validateUnit(startValue, "E2105", "strip");
            start = this._getTranslatedCoord(startValue, -1);
        } else {
            start = canvasStart;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(endValue)) {
            endValue = this.validateUnit(endValue, "E2105", "strip");
            end = this._getTranslatedCoord(endValue, 1);
        } else {
            end = canvasEnd;
        }
        const stripPosition = start < end ? {
            from: start,
            to: end
        } : {
            from: end,
            to: start
        };
        const visibleArea = this.getVisibleArea();
        if (stripPosition.from <= visibleArea[0] && stripPosition.to <= visibleArea[0] || stripPosition.from >= visibleArea[1] && stripPosition.to >= visibleArea[1]) {
            stripPosition.outOfCanvas = true;
        }
        return stripPosition;
    },
    _getStripGraphicAttributes: function(fromPoint, toPoint) {
        let x;
        let y;
        let width;
        let height;
        const orthogonalPositions = this._orthogonalPositions;
        const positionFrom = orthogonalPositions.start;
        const positionTo = orthogonalPositions.end;
        if (this._isHorizontal) {
            x = fromPoint;
            y = _min(positionFrom, positionTo);
            width = toPoint - fromPoint;
            height = _abs(positionFrom - positionTo);
        } else {
            x = _min(positionFrom, positionTo);
            y = fromPoint;
            width = _abs(positionFrom - positionTo);
            height = _abs(fromPoint - toPoint);
        }
        return {
            x: x,
            y: y,
            width: width,
            height: height
        };
    },
    _createStrip: function(attrs) {
        return this._renderer.rect(attrs.x, attrs.y, attrs.width, attrs.height);
    },
    _adjustStripLabels: function() {
        const that = this;
        this._strips.forEach(function(strip) {
            if (strip.label) {
                strip.label.attr(that._getAdjustedStripLabelCoords(strip));
            }
        });
    },
    _adjustLabelsCoord (offset, maxWidth, checkCanvas) {
        const getContainerAttrs = (tick)=>this._getLabelAdjustedCoord(tick, offset + (tick.labelOffset || 0), maxWidth, checkCanvas);
        this._majorTicks.forEach(function(tick) {
            if (tick.label) {
                tick.updateMultilineTextAlignment();
                tick.label.attr(getContainerAttrs(tick));
            } else {
                tick.templateContainer && tick.templateContainer.attr(getContainerAttrs(tick));
            }
        });
    },
    _adjustLabels: function(offset) {
        const options = this.getOptions();
        const positionsAreConsistent = options.position === options.label.position;
        const maxSize = this._majorTicks.reduce(function(size, tick) {
            if (!tick.getContentContainer()) {
                return size;
            }
            const bBox = tick.labelRotationAngle ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rotateBBox"])(tick.labelBBox, [
                tick.labelCoords.x,
                tick.labelCoords.y
            ], -tick.labelRotationAngle) : tick.labelBBox;
            return {
                width: _max(size.width || 0, bBox.width),
                height: _max(size.height || 0, bBox.height),
                offset: _max(size.offset || 0, tick.labelOffset || 0)
            };
        }, {});
        const additionalOffset = positionsAreConsistent ? this._isHorizontal ? maxSize.height : maxSize.width : 0;
        this._adjustLabelsCoord(offset, maxSize.width);
        return offset + additionalOffset + (additionalOffset && this._options.label.indentFromAxis) + (positionsAreConsistent ? maxSize.offset : 0);
    },
    _getLabelAdjustedCoord: function(tick, offset, maxWidth) {
        offset = offset || 0;
        const options = this._options;
        const templateBox = tick.templateContainer && tick.templateContainer.getBBox();
        const box = templateBox || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rotateBBox"])(tick.labelBBox, [
            tick.labelCoords.x,
            tick.labelCoords.y
        ], -tick.labelRotationAngle || 0);
        const textAlign = tick.labelAlignment || options.label.alignment;
        const isDiscrete = "discrete" === this._options.type;
        const isFlatLabel = tick.labelRotationAngle % 90 === 0;
        const indentFromAxis = options.label.indentFromAxis;
        const labelPosition = options.label.position;
        const axisPosition = this._axisPosition;
        const labelCoords = tick.labelCoords;
        const labelX = labelCoords.x;
        let translateX;
        let translateY;
        if (this._isHorizontal) {
            if (labelPosition === BOTTOM) {
                translateY = axisPosition + indentFromAxis - box.y + offset;
            } else {
                translateY = axisPosition - indentFromAxis - (box.y + box.height) - offset;
            }
            if (textAlign === RIGHT) {
                translateX = isDiscrete && isFlatLabel ? tick.coords.x - (box.x + box.width) : labelX - box.x - box.width;
            } else if (textAlign === LEFT) {
                translateX = isDiscrete && isFlatLabel ? labelX - box.x - (tick.coords.x - labelX) : labelX - box.x;
            } else {
                translateX = labelX - box.x - box.width / 2;
            }
        } else {
            translateY = labelCoords.y - box.y - box.height / 2;
            if (labelPosition === LEFT) {
                if (textAlign === LEFT) {
                    translateX = axisPosition - indentFromAxis - maxWidth - box.x;
                } else if (textAlign === CENTER) {
                    translateX = axisPosition - indentFromAxis - maxWidth / 2 - box.x - box.width / 2;
                } else {
                    translateX = axisPosition - indentFromAxis - box.x - box.width;
                }
                translateX -= offset;
            } else {
                if (textAlign === RIGHT) {
                    translateX = axisPosition + indentFromAxis + maxWidth - box.x - box.width;
                } else if (textAlign === CENTER) {
                    translateX = axisPosition + indentFromAxis + maxWidth / 2 - box.x - box.width / 2;
                } else {
                    translateX = axisPosition + indentFromAxis - box.x;
                }
                translateX += offset;
            }
        }
        return {
            translateX: translateX,
            translateY: translateY
        };
    },
    _createAxisConstantLineGroups: function() {
        const renderer = this._renderer;
        const classSelector = this._axisCssPrefix;
        const constantLinesClass = classSelector + "constant-lines";
        const insideGroup = renderer.g().attr({
            class: constantLinesClass
        });
        const outsideGroup1 = renderer.g().attr({
            class: constantLinesClass
        });
        const outsideGroup2 = renderer.g().attr({
            class: constantLinesClass
        });
        return {
            inside: insideGroup,
            outside1: outsideGroup1,
            left: outsideGroup1,
            top: outsideGroup1,
            outside2: outsideGroup2,
            right: outsideGroup2,
            bottom: outsideGroup2,
            remove: function() {
                this.inside.remove();
                this.outside1.remove();
                this.outside2.remove();
            },
            clear: function() {
                this.inside.clear();
                this.outside1.clear();
                this.outside2.clear();
            }
        };
    },
    _createAxisGroups: function() {
        const renderer = this._renderer;
        const classSelector = this._axisCssPrefix;
        this._axisGroup = renderer.g().attr({
            class: classSelector + "axis"
        }).enableLinks();
        this._axisStripGroup = renderer.g().attr({
            class: classSelector + "strips"
        });
        this._axisGridGroup = renderer.g().attr({
            class: classSelector + "grid"
        });
        this._axisElementsGroup = renderer.g().attr({
            class: classSelector + "elements"
        });
        this._axisLineGroup = renderer.g().attr({
            class: classSelector + "line"
        }).linkOn(this._axisGroup, "axisLine").linkAppend();
        this._axisTitleGroup = renderer.g().attr({
            class: classSelector + "title"
        }).append(this._axisGroup);
        this._axisConstantLineGroups = {
            above: this._createAxisConstantLineGroups(),
            under: this._createAxisConstantLineGroups()
        };
        this._axisStripLabelGroup = renderer.g().attr({
            class: classSelector + "axis-labels"
        });
    },
    _clearAxisGroups: function() {
        const that = this;
        that._axisGroup.remove();
        that._axisStripGroup.remove();
        that._axisStripLabelGroup.remove();
        that._axisConstantLineGroups.above.remove();
        that._axisConstantLineGroups.under.remove();
        that._axisGridGroup.remove();
        that._axisTitleGroup.clear();
        if (!that._options.label.template || !that.isRendered()) {
            that._axisElementsGroup.remove();
            that._axisElementsGroup.clear();
        }
        that._axisLineGroup && that._axisLineGroup.clear();
        that._axisStripGroup && that._axisStripGroup.clear();
        that._axisGridGroup && that._axisGridGroup.clear();
        that._axisConstantLineGroups.above.clear();
        that._axisConstantLineGroups.under.clear();
        that._axisStripLabelGroup && that._axisStripLabelGroup.clear();
    },
    _getLabelFormatObject: function(value, labelOptions, range, point, tickInterval, ticks) {
        range = range || this._getViewportRange();
        const formatObject = {
            value: value,
            valueText: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$smart_formatter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["smartFormatter"])(value, {
                labelOptions: labelOptions,
                ticks: ticks || convertTicksToValues(this._majorTicks),
                tickInterval: tickInterval !== null && tickInterval !== void 0 ? tickInterval : this._tickInterval,
                dataType: this._options.dataType,
                logarithmBase: this._options.logarithmBase,
                type: this._options.type,
                showTransition: !this._options.marker.visible,
                point: point
            }) || "",
            min: range.minVisible,
            max: range.maxVisible
        };
        if (point) {
            formatObject.point = point;
        }
        return formatObject;
    },
    formatLabel: function(value, labelOptions, range, point, tickInterval, ticks) {
        const formatObject = this._getLabelFormatObject(value, labelOptions, range, point, tickInterval, ticks);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(labelOptions.customizeText) ? labelOptions.customizeText.call(formatObject, formatObject) : formatObject.valueText;
    },
    formatHint: function(value, labelOptions, range) {
        const formatObject = this._getLabelFormatObject(value, labelOptions, range);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(labelOptions.customizeHint) ? labelOptions.customizeHint.call(formatObject, formatObject) : void 0;
    },
    formatRange (startValue, endValue, interval, argumentFormat) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$smart_formatter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["formatRange"])({
            startValue: startValue,
            endValue: endValue,
            tickInterval: interval,
            argumentFormat: argumentFormat,
            axisOptions: this.getOptions()
        });
    },
    _setTickOffset: function() {
        const options = this._options;
        const discreteAxisDivisionMode = options.discreteAxisDivisionMode;
        this._tickOffset = +("crossLabels" !== discreteAxisDivisionMode || !discreteAxisDivisionMode);
    },
    aggregatedPointBetweenTicks () {
        return "crossTicks" === this._options.aggregatedPointsPosition;
    },
    resetApplyingAnimation: function(isFirstDrawing) {
        this._resetApplyingAnimation = true;
        if (isFirstDrawing) {
            this._firstDrawing = true;
        }
    },
    isFirstDrawing () {
        return this._firstDrawing;
    },
    getMargins: function() {
        const that = this;
        const { position: position, offset: offset, customPosition: customPosition, placeholderSize: placeholderSize, grid: grid, tick: tick, crosshairMargin: crosshairMargin } = that._options;
        const isDefinedCustomPositionOption = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(customPosition);
        const boundaryPosition = that.getResolvedBoundaryPosition();
        const canvas = that.getCanvas();
        const cLeft = canvas.left;
        const cTop = canvas.top;
        const cRight = canvas.width - canvas.right;
        const cBottom = canvas.height - canvas.bottom;
        const edgeMarginCorrection = _max(grid.visible && grid.width || 0, tick.visible && tick.width || 0);
        const constantLineAboveSeries = that._axisConstantLineGroups.above;
        const constantLineUnderSeries = that._axisConstantLineGroups.under;
        const boxes = [
            that._axisElementsGroup,
            constantLineAboveSeries.outside1,
            constantLineAboveSeries.outside2,
            constantLineUnderSeries.outside1,
            constantLineUnderSeries.outside2,
            that._axisLineGroup
        ].map((group)=>group && group.getBBox()).concat(function(group) {
            const box = group && group.getBBox();
            if (!box || box.isEmpty) {
                return box;
            }
            if (that._isHorizontal) {
                box.x = cLeft;
                box.width = cRight - cLeft;
            } else {
                box.y = cTop;
                box.height = cBottom - cTop;
            }
            return box;
        }(that._axisTitleGroup));
        const margins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateCanvasMargins"])(boxes, canvas);
        margins[position] += crosshairMargin;
        if (that.hasNonBoundaryPosition() && isDefinedCustomPositionOption) {
            margins[boundaryPosition] = 0;
        }
        if (placeholderSize) {
            margins[position] = placeholderSize;
        }
        if (edgeMarginCorrection) {
            if (that._isHorizontal && canvas.right < edgeMarginCorrection && margins.right < edgeMarginCorrection) {
                margins.right = edgeMarginCorrection;
            }
            if (!that._isHorizontal && canvas.bottom < edgeMarginCorrection && margins.bottom < edgeMarginCorrection) {
                margins.bottom = edgeMarginCorrection;
            }
        }
        if (!isDefinedCustomPositionOption && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(offset)) {
            const moveByOffset = that.customPositionIsBoundary() && (offset > 0 && (boundaryPosition === LEFT || boundaryPosition === TOP) || offset < 0 && (boundaryPosition === RIGHT || boundaryPosition === BOTTOM));
            margins[boundaryPosition] -= moveByOffset ? offset : 0;
        }
        return margins;
    },
    validateUnit: function(unit, idError, parameters) {
        const that = this;
        unit = that.parser(unit);
        if (void 0 === unit && idError) {
            that._incidentOccurred(idError, [
                parameters
            ]);
        }
        return unit;
    },
    _setType: function(axisType, drawingType) {
        let axisTypeMethods;
        switch(axisType){
            case "xyAxes":
                axisTypeMethods = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$xy_axes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
                break;
            case "polarAxes":
                axisTypeMethods = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$polar_axes$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this, axisTypeMethods[drawingType]);
    },
    _getSharpParam: function() {
        return true;
    },
    _disposeBreaksGroup: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    dispose: function() {
        [
            this._axisElementsGroup,
            this._axisStripGroup,
            this._axisGroup
        ].forEach(function(g) {
            g.dispose();
        });
        this._strips = this._title = null;
        this._axisStripGroup = this._axisConstantLineGroups = this._axisStripLabelGroup = this._axisBreaksGroup = null;
        this._axisLineGroup = this._axisElementsGroup = this._axisGridGroup = null;
        this._axisGroup = this._axisTitleGroup = null;
        this._axesContainerGroup = this._stripsGroup = this._constantLinesGroup = this._labelsAxesGroup = null;
        this._renderer = this._options = this._textOptions = this._textFontStyles = null;
        this._translator = null;
        this._majorTicks = this._minorTicks = null;
        this._disposeBreaksGroup();
        this._templatesRendered && this._templatesRendered.reject();
    },
    getOptions: function() {
        return this._options;
    },
    setPane: function(pane) {
        this.pane = pane;
        this._options.pane = pane;
    },
    setTypes: function(type, axisType, typeSelector) {
        this._options.type = type || this._options.type;
        this._options[typeSelector] = axisType || this._options[typeSelector];
        this._updateTranslator();
    },
    resetTypes: function(typeSelector) {
        this._options.type = this._initTypes.type;
        this._options[typeSelector] = this._initTypes[typeSelector];
    },
    getTranslator: function() {
        return this._translator;
    },
    updateOptions: function(options) {
        const that = this;
        const labelOpt = options.label;
        validateAxisOptions(options);
        that._options = options;
        options.tick = options.tick || {};
        options.minorTick = options.minorTick || {};
        options.grid = options.grid || {};
        options.minorGrid = options.minorGrid || {};
        options.title = options.title || {};
        options.marker = options.marker || {};
        that._initTypes = {
            type: options.type,
            argumentType: options.argumentType,
            valueType: options.valueType
        };
        that._setTickOffset();
        that._isHorizontal = options.isHorizontal;
        that.pane = options.pane;
        that.name = options.name;
        that.priority = options.priority;
        that._hasLabelFormat = "" !== labelOpt.format && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(labelOpt.format);
        that._textOptions = {
            opacity: labelOpt.opacity,
            align: "center",
            class: labelOpt.cssClass
        };
        that._textFontStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["patchFontOptions"])(labelOpt.font);
        if (options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logarithmic) {
            if (options.logarithmBaseError) {
                that._incidentOccurred("E2104");
                delete options.logarithmBaseError;
            }
        }
        that._updateTranslator();
        that._createConstantLines();
        that._strips = (options.strips || []).map((o)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$strip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(that, o));
        that._majorTicks = that._minorTicks = null;
        that._firstDrawing = true;
    },
    calculateInterval: function(value, prevValue) {
        const options = this._options;
        if (!options || options.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logarithmic) {
            return _abs(value - prevValue);
        }
        const { allowNegatives: allowNegatives, linearThreshold: linearThreshold } = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"](this.getTranslator().getBusinessRange());
        return _abs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(value, options.logarithmBase, allowNegatives, linearThreshold) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(prevValue, options.logarithmBase, allowNegatives, linearThreshold));
    },
    getCanvasRange () {
        const translator = this._translator;
        return {
            startValue: translator.from(translator.translate("canvas_position_start")),
            endValue: translator.from(translator.translate("canvas_position_end"))
        };
    },
    _processCanvas: function(canvas) {
        return canvas;
    },
    updateCanvas: function(canvas, canvasRedesign) {
        if (!canvasRedesign) {
            const positions = this._orthogonalPositions = {
                start: !this._isHorizontal ? canvas.left : canvas.top,
                end: !this._isHorizontal ? canvas.width - canvas.right : canvas.height - canvas.bottom
            };
            positions.center = positions.start + (positions.end - positions.start) / 2;
        } else {
            this._orthogonalPositions = null;
        }
        this._canvas = canvas;
        this._translator.updateCanvas(this._processCanvas(canvas));
        this._initAxisPositions();
    },
    getCanvas: function() {
        return this._canvas;
    },
    getAxisShift () {
        return this._axisShift || 0;
    },
    hideTitle: function() {
        const that = this;
        if (that._options.title.text) {
            that._incidentOccurred("W2105", [
                that._isHorizontal ? "horizontal" : "vertical"
            ]);
            that._axisTitleGroup.clear();
        }
    },
    getTitle: function() {
        return this._title;
    },
    hideOuterElements: function() {
        const that = this;
        const options = that._options;
        if ((options.label.visible || that._outsideConstantLines.length) && !that._translator.getBusinessRange().isEmpty()) {
            that._incidentOccurred("W2106", [
                that._isHorizontal ? "horizontal" : "vertical"
            ]);
            that._axisElementsGroup.clear();
            callAction(that._outsideConstantLines, "removeLabel");
        }
    },
    _resolveLogarithmicOptionsForRange (range) {
        const options = this._options;
        if (options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logarithmic) {
            range.addRange({
                allowNegatives: void 0 !== options.allowNegatives ? options.allowNegatives : range.min <= 0
            });
            if (!isNaN(options.linearThreshold)) {
                range.linearThreshold = options.linearThreshold;
            }
        }
    },
    adjustViewport (businessRange) {
        const options = this._options;
        const isDiscrete = options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete;
        let categories = this._seriesData && this._seriesData.categories || [];
        const wholeRange = this.adjustRange((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])(options.wholeRange));
        const visualRange = this.getViewport() || {};
        const result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"](businessRange);
        this._addConstantLinesToRange(result);
        let minDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(visualRange.startValue);
        let maxDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(visualRange.endValue);
        if (!isDiscrete) {
            minDefined = minDefined && (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(wholeRange.endValue) || visualRange.startValue < wholeRange.endValue);
            maxDefined = maxDefined && (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(wholeRange.startValue) || visualRange.endValue > wholeRange.startValue);
        }
        const minVisible = minDefined ? visualRange.startValue : result.minVisible;
        const maxVisible = maxDefined ? visualRange.endValue : result.maxVisible;
        if (!isDiscrete) {
            var _wholeRange_startValue;
            result.min = (_wholeRange_startValue = wholeRange.startValue) !== null && _wholeRange_startValue !== void 0 ? _wholeRange_startValue : result.min;
            var _wholeRange_endValue;
            result.max = (_wholeRange_endValue = wholeRange.endValue) !== null && _wholeRange_endValue !== void 0 ? _wholeRange_endValue : result.max;
        } else {
            const categoriesInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(categories, wholeRange.startValue, wholeRange.endValue);
            categories = categoriesInfo.categories;
            result.categories = categories;
        }
        const adjustedVisualRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjustVisualRange"])({
            axisType: options.type,
            dataType: options.dataType,
            base: options.logarithmBase
        }, {
            startValue: minDefined ? visualRange.startValue : void 0,
            endValue: maxDefined ? visualRange.endValue : void 0,
            length: visualRange.length
        }, {
            categories: categories,
            min: wholeRange.startValue,
            max: wholeRange.endValue
        }, {
            categories: categories,
            min: minVisible,
            max: maxVisible
        });
        result.minVisible = adjustedVisualRange.startValue;
        result.maxVisible = adjustedVisualRange.endValue;
        !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(result.min) && (result.min = result.minVisible);
        !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(result.max) && (result.max = result.maxVisible);
        result.addRange({});
        this._resolveLogarithmicOptionsForRange(result);
        return result;
    },
    adjustRange (range) {
        range = range || {};
        const isDiscrete = this._options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete;
        const isLogarithmic = this._options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logarithmic;
        const disabledNegatives = false === this._options.allowNegatives;
        if (isLogarithmic) {
            range.startValue = disabledNegatives && range.startValue <= 0 ? null : range.startValue;
            range.endValue = disabledNegatives && range.endValue <= 0 ? null : range.endValue;
        }
        if (!isDiscrete && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(range.startValue) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(range.endValue) && range.startValue > range.endValue) {
            const tmp = range.endValue;
            range.endValue = range.startValue;
            range.startValue = tmp;
        }
        return range;
    },
    _getVisualRangeUpdateMode (viewport, newRange, oppositeValue) {
        let value = this._options.visualRangeUpdateMode;
        const translator = this._translator;
        const range = this._seriesData;
        const prevDataInfo = this._prevDataInfo;
        if (prevDataInfo.isEmpty && !prevDataInfo.containsConstantLine) {
            return KEEP;
        }
        if (!this.isArgumentAxis) {
            const viewport = this.getViewport();
            const isViewportNotDefined = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.endValue) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.length);
            if (isViewportNotDefined) {
                const visualRange = this.visualRange();
                const isVisualRangeNotDefined = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(visualRange.startValue) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(visualRange.endValue);
                if (isVisualRangeNotDefined) {
                    return RESET;
                }
            }
        }
        if (this.isArgumentAxis) {
            if (-1 === [
                SHIFT,
                KEEP,
                RESET
            ].indexOf(value)) {
                if (range.axisType === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete) {
                    const categories = range.categories;
                    const newCategories = newRange.categories;
                    const visualRange = this.visualRange();
                    if (categories && newCategories && categories.length && -1 !== newCategories.map((c)=>c.valueOf()).join(",").indexOf(categories.map((c)=>c.valueOf()).join(",")) && (visualRange.startValue.valueOf() !== categories[0].valueOf() || visualRange.endValue.valueOf() !== categories[categories.length - 1].valueOf())) {
                        value = KEEP;
                    } else {
                        value = RESET;
                    }
                } else {
                    const minPoint = translator.translate(range.min);
                    const minVisiblePoint = translator.translate(viewport.startValue);
                    const maxPoint = translator.translate(range.max);
                    const maxVisiblePoint = translator.translate(viewport.endValue);
                    if (minPoint === minVisiblePoint && maxPoint === maxVisiblePoint) {
                        value = RESET;
                    } else if (minPoint !== minVisiblePoint && maxPoint === maxVisiblePoint) {
                        value = SHIFT;
                    } else {
                        value = KEEP;
                    }
                }
                if (value === KEEP && prevDataInfo.isEmpty && prevDataInfo.containsConstantLine) {
                    value = RESET;
                }
            }
        } else if (-1 === [
            KEEP,
            RESET
        ].indexOf(value)) {
            if (oppositeValue === KEEP) {
                value = KEEP;
            } else {
                value = RESET;
            }
        }
        return value;
    },
    _handleBusinessRangeChanged (oppositeVisualRangeUpdateMode, axisReinitialized, newRange) {
        const that = this;
        const visualRange = this.visualRange();
        if (axisReinitialized || that._translator.getBusinessRange().isEmpty()) {
            return;
        }
        const visualRangeUpdateMode = that._lastVisualRangeUpdateMode = that._getVisualRangeUpdateMode(visualRange, newRange, oppositeVisualRangeUpdateMode);
        if (visualRangeUpdateMode === KEEP) {
            that._setVisualRange([
                visualRange.startValue,
                visualRange.endValue
            ]);
        } else if (visualRangeUpdateMode === RESET) {
            that._setVisualRange([
                null,
                null
            ]);
        } else if (visualRangeUpdateMode === SHIFT) {
            that._setVisualRange({
                length: that.getVisualRangeLength()
            });
        }
    },
    getVisualRangeLength (range) {
        const currentBusinessRange = range || this._translator.getBusinessRange();
        const { type: type } = this._options;
        let length;
        if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logarithmic) {
            length = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(this.calculateInterval(currentBusinessRange.maxVisible, currentBusinessRange.minVisible));
        } else if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete) {
            const categoriesInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(currentBusinessRange.categories, currentBusinessRange.minVisible, currentBusinessRange.maxVisible);
            length = categoriesInfo.categories.length;
        } else {
            length = currentBusinessRange.maxVisible - currentBusinessRange.minVisible;
        }
        return length;
    },
    getVisualRangeCenter (range, useMerge) {
        const translator = this.getTranslator();
        const businessRange = translator.getBusinessRange();
        const currentBusinessRange = useMerge ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, businessRange, range || {}) : range || businessRange;
        const { type: type, logarithmBase: logarithmBase } = this._options;
        let center;
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(currentBusinessRange.minVisible) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(currentBusinessRange.maxVisible)) {
            return;
        }
        if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].logarithmic) {
            const { allowNegatives: allowNegatives, linearThreshold: linearThreshold, minVisible: minVisible, maxVisible: maxVisible } = currentBusinessRange;
            center = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["raiseToExt"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(maxVisible, logarithmBase, allowNegatives, linearThreshold) + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLogExt"])(minVisible, logarithmBase, allowNegatives, linearThreshold)) / 2, logarithmBase, allowNegatives, linearThreshold);
        } else if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete) {
            const categoriesInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(currentBusinessRange.categories, currentBusinessRange.minVisible, currentBusinessRange.maxVisible);
            const index = Math.ceil(categoriesInfo.categories.length / 2) - 1;
            center = businessRange.categories.indexOf(categoriesInfo.categories[index]);
        } else {
            center = translator.toValue((currentBusinessRange.maxVisible.valueOf() + currentBusinessRange.minVisible.valueOf()) / 2);
        }
        return center;
    },
    setBusinessRange (range, axisReinitialized, oppositeVisualRangeUpdateMode, argCategories) {
        const that = this;
        const options = that._options;
        const isDiscrete = options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete;
        that._handleBusinessRangeChanged(oppositeVisualRangeUpdateMode, axisReinitialized, range);
        that._seriesData = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"](range);
        const dataIsEmpty = that._seriesData.isEmpty();
        const rangeWithConstantLines = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"](that._seriesData);
        that._addConstantLinesToRange(rangeWithConstantLines);
        that._prevDataInfo = {
            isEmpty: dataIsEmpty,
            containsConstantLine: rangeWithConstantLines.containsConstantLine
        };
        that._seriesData.addRange({
            categories: options.categories,
            dataType: options.dataType,
            axisType: options.type,
            base: options.logarithmBase,
            invert: options.inverted
        });
        that._resolveLogarithmicOptionsForRange(that._seriesData);
        if (!isDiscrete) {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(that._seriesData.min) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(that._seriesData.max)) {
                const visualRange = that.getViewport();
                visualRange && that._seriesData.addRange({
                    min: visualRange.startValue,
                    max: visualRange.endValue
                });
            }
            const synchronizedValue = options.synchronizedValue;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(synchronizedValue)) {
                that._seriesData.addRange({
                    min: synchronizedValue,
                    max: synchronizedValue
                });
            }
        }
        var _that__seriesData_minVisible;
        that._seriesData.minVisible = (_that__seriesData_minVisible = that._seriesData.minVisible) !== null && _that__seriesData_minVisible !== void 0 ? _that__seriesData_minVisible : that._seriesData.min;
        var _that__seriesData_maxVisible;
        that._seriesData.maxVisible = (_that__seriesData_maxVisible = that._seriesData.maxVisible) !== null && _that__seriesData_maxVisible !== void 0 ? _that__seriesData_maxVisible : that._seriesData.max;
        if (!that.isArgumentAxis && options.showZero) {
            that._seriesData.correctValueZeroLevel();
        }
        that._seriesData.sortCategories(that.getCategoriesSorter(argCategories));
        that._seriesData.userBreaks = that._seriesData.isEmpty() ? [] : that._getScaleBreaks(options, that._seriesData, that._series, that.isArgumentAxis);
        that._translator.updateBusinessRange(that._getViewportRange());
    },
    _addConstantLinesToRange (dataRange) {
        this._outsideConstantLines.concat(this._insideConstantLines || []).forEach((cl)=>{
            if (cl.options.extendAxis) {
                const value = cl.getParsedValue();
                dataRange.addRange({
                    containsConstantLine: true,
                    minVisible: value,
                    maxVisible: value,
                    min: !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(dataRange.min) ? value : dataRange.min,
                    max: !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(dataRange.max) ? value : dataRange.max
                });
            }
        });
    },
    setGroupSeries: function(series) {
        this._series = series;
    },
    getLabelsPosition: function() {
        const options = this._options;
        const position = options.position;
        const labelShift = options.label.indentFromAxis + (this._axisShift || 0) + this._constantLabelOffset;
        const axisPosition = this._axisPosition;
        return position === TOP || position === LEFT ? axisPosition - labelShift : axisPosition + labelShift;
    },
    getFormattedValue: function(value, options, point) {
        const labelOptions = this._options.label;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value) ? this.formatLabel(value, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, labelOptions, options), void 0, point) : null;
    },
    _getBoundaryTicks: function(majors, viewPort) {
        const that = this;
        const length = majors.length;
        const options = that._options;
        const customBounds = options.customBoundTicks;
        const min = viewPort.minVisible;
        const max = viewPort.maxVisible;
        const addMinMax = options.showCustomBoundaryTicks ? that._boundaryTicksVisibility : {};
        let boundaryTicks = [];
        if (options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete) {
            if (that._tickOffset && 0 !== majors.length) {
                boundaryTicks = [
                    majors[0],
                    majors[majors.length - 1]
                ];
            }
        } else if (customBounds) {
            if (addMinMax.min && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(customBounds[0])) {
                boundaryTicks.push(customBounds[0]);
            }
            if (addMinMax.max && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(customBounds[1])) {
                boundaryTicks.push(customBounds[1]);
            }
        } else {
            if (addMinMax.min && (0 === length || majors[0] > min)) {
                boundaryTicks.push(min);
            }
            if (addMinMax.max && (0 === length || majors[length - 1] < max)) {
                boundaryTicks.push(max);
            }
        }
        return boundaryTicks;
    },
    setPercentLabelFormat: function() {
        if (!this._hasLabelFormat) {
            this._options.label.format = "percent";
        }
    },
    resetAutoLabelFormat: function() {
        if (!this._hasLabelFormat) {
            delete this._options.label.format;
        }
    },
    getMultipleAxesSpacing: function() {
        return this._options.multipleAxesSpacing || 0;
    },
    getTicksValues: function() {
        return {
            majorTicksValues: convertTicksToValues(this._majorTicks),
            minorTicksValues: convertTicksToValues(this._minorTicks)
        };
    },
    estimateTickInterval: function(canvas) {
        this.updateCanvas(canvas);
        return this._tickInterval !== this._getTicks(this._getViewportRange(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"], true).tickInterval;
    },
    setTicks: function(ticks) {
        const majors = ticks.majorTicks || [];
        this._majorTicks = majors.map(createMajorTick(this, this._renderer, this._getSkippedCategory(majors)));
        this._minorTicks = (ticks.minorTicks || []).map(createMinorTick(this, this._renderer));
        this._isSynchronized = true;
    },
    _adjustDivisionFactor: function(val) {
        return val;
    },
    _getTicks: function(viewPort, incidentOccurred, skipTickGeneration) {
        const options = this._options;
        const customTicks = options.customTicks;
        const customMinorTicks = options.customMinorTicks;
        return getTickGenerator(options, incidentOccurred || this._incidentOccurred, skipTickGeneration, this._translator.getBusinessRange().isEmpty(), this._adjustDivisionFactor.bind(this), viewPort)({
            min: viewPort.minVisible,
            max: viewPort.maxVisible,
            categories: viewPort.categories,
            isSpacedMargin: viewPort.isSpacedMargin
        }, this._getScreenDelta(), options.tickInterval, "ignore" === options.label.overlappingBehavior || options.forceUserTickInterval, {
            majors: customTicks,
            minors: customMinorTicks
        }, options.minorTickInterval, options.minorTickCount, this._initialBreaks);
    },
    _createTicksAndLabelFormat: function(range, incidentOccurred) {
        const options = this._options;
        const ticks = this._getTicks(range, incidentOccurred, false);
        if (!range.isEmpty() && options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete && "datetime" === options.dataType && !this._hasLabelFormat && ticks.ticks.length) {
            options.label.format = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDateFormatByTicks(ticks.ticks);
        }
        return ticks;
    },
    getAggregationInfo (useAllAggregatedPoints, range) {
        var _that$_seriesData;
        const options = this._options;
        const businessRange = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"](this.getTranslator().getBusinessRange()).addRange(range);
        const visualRange = this.getViewport();
        var _ref;
        const minVisible = (_ref = null === visualRange || void 0 === visualRange ? void 0 : visualRange.startValue) !== null && _ref !== void 0 ? _ref : businessRange.minVisible;
        var _ref1;
        const maxVisible = (_ref1 = null === visualRange || void 0 === visualRange ? void 0 : visualRange.endValue) !== null && _ref1 !== void 0 ? _ref1 : businessRange.maxVisible;
        const aggregationInterval = options.aggregationInterval;
        const aggregationGroupWidth = this._getAggregationGroupWidth();
        const minInterval = !options.aggregationGroupWidth && !aggregationInterval && range.interval;
        const generateTicks = configureGenerator(options, aggregationGroupWidth, businessRange, this._getScreenDelta(), minInterval);
        const tickInterval = generateTicks(aggregationInterval, true, minVisible, maxVisible, null === (_that$_seriesData = this._seriesData) || void 0 === _that$_seriesData ? void 0 : _that$_seriesData.breaks).tickInterval;
        const ticks = this._generateTick(useAllAggregatedPoints, businessRange, minVisible, maxVisible, tickInterval, generateTicks);
        this._aggregationInterval = tickInterval;
        return {
            interval: tickInterval,
            ticks: ticks
        };
    },
    _getAggregationGroupWidth () {
        const { checkInterval: checkInterval, sizePointNormalState: sizePointNormalState } = this._marginOptions || {};
        const { aggregationGroupWidth: aggregationGroupWidth, axisDivisionFactor: axisDivisionFactor } = this._options;
        if (aggregationGroupWidth) {
            return aggregationGroupWidth;
        }
        if (sizePointNormalState) {
            return Math.min(sizePointNormalState, axisDivisionFactor);
        }
        if (checkInterval) {
            return axisDivisionFactor;
        }
        return aggregationGroupWidth;
    },
    _generateTick (useAllAggregatedPoints, businessRange, minVisible, maxVisible, tickInterval, generateTicks) {
        const min = useAllAggregatedPoints ? businessRange.min : minVisible;
        const max = useAllAggregatedPoints ? businessRange.max : maxVisible;
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(min) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(max)) {
            return [];
        }
        const that = this;
        const options = that._options;
        const add = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAddFunction"])({
            base: options.logarithmBase,
            axisType: options.type,
            dataType: options.dataType
        }, false);
        let start = min;
        let end = max;
        if (!useAllAggregatedPoints && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tickInterval)) {
            const maxMinDistance = Math.max(that.calculateInterval(max, min), "datetime" === options.dataType ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds(tickInterval) : tickInterval);
            start = add(min, maxMinDistance, -1);
            end = add(max, maxMinDistance);
        }
        start = start < businessRange.min ? businessRange.min : start;
        end = end > businessRange.max ? businessRange.max : end;
        const breaks = that._getScaleBreaks(options, {
            minVisible: start,
            maxVisible: end
        }, that._series, that.isArgumentAxis);
        const filteredBreaks = that._filterBreaks(breaks, {
            minVisible: start,
            maxVisible: end
        }, options.breakStyle);
        return generateTicks(tickInterval, false, start, end, filteredBreaks).ticks;
    },
    getTickInterval () {
        return this._tickInterval;
    },
    getAggregationInterval () {
        return this._aggregationInterval;
    },
    createTicks: function(canvas) {
        const that = this;
        const renderer = that._renderer;
        const options = that._options;
        if (!canvas) {
            return;
        }
        that._isSynchronized = false;
        that.updateCanvas(canvas);
        const range = that._getViewportRange();
        that._initialBreaks = range.breaks = this._seriesData.breaks = that._filterBreaks(this._seriesData.userBreaks, range, options.breakStyle);
        that._estimatedTickInterval = that._getTicks(that.adjustViewport(this._seriesData), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"], true).tickInterval;
        const margins = this._calculateValueMargins();
        range.addRange({
            minVisible: margins.minValue,
            maxVisible: margins.maxValue,
            isSpacedMargin: margins.isSpacedMargin
        });
        const ticks = that._createTicksAndLabelFormat(range);
        const boundaryTicks = that._getBoundaryTicks(ticks.ticks, that._getViewportRange());
        if (options.showCustomBoundaryTicks && boundaryTicks.length) {
            that._boundaryTicks = [
                boundaryTicks[0]
            ].map(createBoundaryTick(that, renderer, true));
            if (boundaryTicks.length > 1) {
                that._boundaryTicks = that._boundaryTicks.concat([
                    boundaryTicks[1]
                ].map(createBoundaryTick(that, renderer, false)));
            }
        } else {
            that._boundaryTicks = [];
        }
        const minors = (ticks.minorTicks || []).filter(function(minor) {
            return !boundaryTicks.some(function(boundary) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(boundary) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(minor);
            });
        });
        that._tickInterval = ticks.tickInterval;
        that._minorTickInterval = ticks.minorTickInterval;
        const oldMajorTicks = that._majorTicks || [];
        const majorTicksByValues = oldMajorTicks.reduce((r, t)=>{
            r[t.value.valueOf()] = t;
            return r;
        }, {});
        const sameType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(ticks.ticks[0]) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(oldMajorTicks[0] && oldMajorTicks[0].value);
        const skippedCategory = that._getSkippedCategory(ticks.ticks);
        const majorTicks = ticks.ticks.map((v)=>{
            const tick = majorTicksByValues[v.valueOf()];
            if (tick && sameType) {
                delete majorTicksByValues[v.valueOf()];
                tick.setSkippedCategory(skippedCategory);
                return tick;
            } else {
                return createMajorTick(that, renderer, skippedCategory)(v);
            }
        });
        that._majorTicks = majorTicks;
        const oldMinorTicks = that._minorTicks || [];
        that._minorTicks = minors.map((v, i)=>{
            const minorTick = oldMinorTicks[i];
            if (minorTick) {
                minorTick.updateValue(v);
                return minorTick;
            }
            return createMinorTick(that, renderer)(v);
        });
        that._ticksToRemove = Object.keys(majorTicksByValues).map((k)=>majorTicksByValues[k]).concat(oldMinorTicks.slice(that._minorTicks.length, oldMinorTicks.length));
        that._ticksToRemove.forEach((t)=>{
            var _t$label;
            return null === (_t$label = t.label) || void 0 === _t$label ? void 0 : _t$label.removeTitle();
        });
        if (ticks.breaks) {
            that._seriesData.breaks = ticks.breaks;
        }
        that._reinitTranslator(that._getViewportRange());
    },
    _reinitTranslator: function(range) {
        const translator = this._translator;
        if (this._isSynchronized) {
            return;
        }
        translator.updateBusinessRange(range);
    },
    _getViewportRange () {
        return this.adjustViewport(this._seriesData);
    },
    setMarginOptions: function(options) {
        this._marginOptions = options;
    },
    getMarginOptions () {
        var _this__marginOptions;
        return (_this__marginOptions = this._marginOptions) !== null && _this__marginOptions !== void 0 ? _this__marginOptions : {};
    },
    _calculateRangeInterval: function(interval) {
        const isDateTime = "datetime" === this._options.dataType;
        const minArgs = [];
        const addToArgs = function(value) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value) && minArgs.push(isDateTime ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds(value) : value);
        };
        addToArgs(this._tickInterval);
        addToArgs(this._estimatedTickInterval);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(interval) && minArgs.push(interval);
        addToArgs(this._aggregationInterval);
        return this._calculateWorkWeekInterval(_min.apply(this, minArgs));
    },
    _calculateWorkWeekInterval (businessInterval) {
        const options = this._options;
        if ("datetime" === options.dataType && options.workdaysOnly && businessInterval) {
            const workWeek = options.workWeek.length * dateIntervals.day;
            const weekend = dateIntervals.week - workWeek;
            if (workWeek !== businessInterval && weekend < businessInterval) {
                const weekendsCount = Math.ceil(businessInterval / dateIntervals.week);
                businessInterval -= weekend * weekendsCount;
            } else if (weekend >= businessInterval && businessInterval > dateIntervals.day) {
                businessInterval = dateIntervals.day;
            }
        }
        return businessInterval;
    },
    _getConvertIntervalCoefficient (intervalInPx, screenDelta) {
        const ratioOfCanvasRange = this._translator.ratioOfCanvasRange();
        return ratioOfCanvasRange / (ratioOfCanvasRange * screenDelta / (intervalInPx + screenDelta));
    },
    _calculateValueMargins (ticks) {
        this._resetMargins();
        const that = this;
        const margins = that.getMarginOptions();
        const marginSize = (margins.size || 0) / 2;
        const options = that._options;
        const dataRange = that._getViewportRange();
        const viewPort = that.getViewport();
        const screenDelta = that._getScreenDelta();
        const isDiscrete = -1 !== (options.type || "").indexOf(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete);
        const valueMarginsEnabled = options.valueMarginsEnabled && !isDiscrete && !that.customPositionIsBoundaryOrthogonalAxis();
        const translator = that._translator;
        const minValueMargin = options.minValueMargin;
        const maxValueMargin = options.maxValueMargin;
        let minPadding = 0;
        let maxPadding = 0;
        let interval = 0;
        let rangeInterval;
        if (dataRange.stubData || !screenDelta) {
            return {
                startPadding: 0,
                endPadding: 0
            };
        }
        if (that.isArgumentAxis && margins.checkInterval) {
            rangeInterval = that._calculateRangeInterval(dataRange.interval);
            const pxInterval = translator.getInterval(rangeInterval);
            if (isFinite(pxInterval)) {
                interval = Math.ceil(pxInterval / (2 * that._getConvertIntervalCoefficient(pxInterval, screenDelta)));
            } else {
                rangeInterval = 0;
            }
        }
        let minPercentPadding;
        let maxPercentPadding;
        const maxPaddingValue = .8 * screenDelta / 2;
        if (valueMarginsEnabled) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(minValueMargin)) {
                minPercentPadding = isFinite(minValueMargin) ? minValueMargin : 0;
            } else if (!that.isArgumentAxis && margins.checkInterval && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(dataRange.minVisible) > 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(dataRange.minVisible) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(dataRange.min)) {
                minPadding = 5;
            } else {
                minPadding = Math.max(marginSize, interval);
                minPadding = Math.min(maxPaddingValue, minPadding);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(maxValueMargin)) {
                maxPercentPadding = isFinite(maxValueMargin) ? maxValueMargin : 0;
            } else if (!that.isArgumentAxis && margins.checkInterval && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(dataRange.maxVisible) < 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(dataRange.maxVisible) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(dataRange.max)) {
                maxPadding = 5;
            } else {
                maxPadding = Math.max(marginSize, interval);
                maxPadding = Math.min(maxPaddingValue, maxPadding);
            }
        }
        const percentStick = margins.percentStick && !this.isArgumentAxis;
        if (percentStick) {
            if (1 === _abs(dataRange.max)) {
                maxPadding = 0;
            }
            if (1 === _abs(dataRange.min)) {
                minPadding = 0;
            }
        }
        const canvasStartEnd = that._getCanvasStartEnd();
        const commonMargin = 1 + (minPercentPadding || 0) + (maxPercentPadding || 0);
        const screenDeltaWithMargins = (screenDelta - minPadding - maxPadding) / commonMargin || screenDelta;
        if (void 0 !== minPercentPadding || void 0 !== maxPercentPadding) {
            if (void 0 !== minPercentPadding) {
                minPadding = screenDeltaWithMargins * minPercentPadding;
            }
            if (void 0 !== maxPercentPadding) {
                maxPadding = screenDeltaWithMargins * maxPercentPadding;
            }
        }
        let minValue;
        let maxValue;
        if (options.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete && ticks && ticks.length > 1 && !options.skipViewportExtending && !viewPort.action && false !== options.endOnTick) {
            const length = ticks.length;
            const firstTickPosition = translator.translate(ticks[0].value);
            const lastTickPosition = translator.translate(ticks[length - 1].value);
            const invertMultiplier = firstTickPosition > lastTickPosition ? -1 : 1;
            const minTickPadding = _max(invertMultiplier * (canvasStartEnd.start - firstTickPosition), 0);
            const maxTickPadding = _max(invertMultiplier * (lastTickPosition - canvasStartEnd.end), 0);
            if (minTickPadding > minPadding || maxTickPadding > maxPadding) {
                const commonPadding = maxTickPadding + minTickPadding;
                const coeff = that._getConvertIntervalCoefficient(commonPadding, screenDelta);
                if (minTickPadding >= minPadding) {
                    minValue = ticks[0].value;
                }
                if (maxTickPadding >= maxPadding) {
                    maxValue = ticks[length - 1].value;
                }
                minPadding = _max(minTickPadding, minPadding) / coeff;
                maxPadding = _max(maxTickPadding, maxPadding) / coeff;
            }
        }
        minPercentPadding = void 0 === minPercentPadding ? minPadding / screenDeltaWithMargins : minPercentPadding;
        maxPercentPadding = void 0 === maxPercentPadding ? maxPadding / screenDeltaWithMargins : maxPercentPadding;
        if (!isDiscrete) {
            if (this._translator.isInverted()) {
                minValue = minValue !== null && minValue !== void 0 ? minValue : translator.from(canvasStartEnd.start + screenDelta * minPercentPadding, -1);
                maxValue = maxValue !== null && maxValue !== void 0 ? maxValue : translator.from(canvasStartEnd.end - screenDelta * maxPercentPadding, 1);
            } else {
                minValue = minValue !== null && minValue !== void 0 ? minValue : translator.from(canvasStartEnd.start - screenDelta * minPercentPadding, -1);
                maxValue = maxValue !== null && maxValue !== void 0 ? maxValue : translator.from(canvasStartEnd.end + screenDelta * maxPercentPadding, 1);
            }
        }
        const { correctedMin: correctedMin, correctedMax: correctedMax, start: start, end: end } = that.getCorrectedValuesToZero(minValue, maxValue);
        minPadding = start !== null && start !== void 0 ? start : minPadding;
        maxPadding = end !== null && end !== void 0 ? end : maxPadding;
        return {
            startPadding: translator.isInverted() ? maxPadding : minPadding,
            endPadding: translator.isInverted() ? minPadding : maxPadding,
            minValue: correctedMin !== null && correctedMin !== void 0 ? correctedMin : minValue,
            maxValue: correctedMax !== null && correctedMax !== void 0 ? correctedMax : maxValue,
            interval: rangeInterval,
            isSpacedMargin: minPadding === maxPadding && 0 !== minPadding
        };
    },
    getCorrectedValuesToZero (minValue, maxValue) {
        const that = this;
        const translator = that._translator;
        const canvasStartEnd = that._getCanvasStartEnd();
        const dataRange = that._getViewportRange();
        const screenDelta = that._getScreenDelta();
        const options = that._options;
        let start;
        let end;
        let correctedMin;
        let correctedMax;
        const correctZeroLevel = (minPoint, maxPoint)=>{
            const minExpectedPadding = _abs(canvasStartEnd.start - minPoint);
            const maxExpectedPadding = _abs(canvasStartEnd.end - maxPoint);
            const coeff = that._getConvertIntervalCoefficient(minExpectedPadding + maxExpectedPadding, screenDelta);
            start = minExpectedPadding / coeff;
            end = maxExpectedPadding / coeff;
        };
        if (!that.isArgumentAxis && "datetime" !== options.dataType) {
            if (minValue * dataRange.min <= 0 && minValue * dataRange.minVisible <= 0) {
                correctZeroLevel(translator.translate(0), translator.translate(maxValue));
                correctedMin = 0;
            }
            if (maxValue * dataRange.max <= 0 && maxValue * dataRange.maxVisible <= 0) {
                correctZeroLevel(translator.translate(minValue), translator.translate(0));
                correctedMax = 0;
            }
        }
        return {
            start: isFinite(start) ? start : null,
            end: isFinite(end) ? end : null,
            correctedMin: correctedMin,
            correctedMax: correctedMax
        };
    },
    applyMargins () {
        if (this._isSynchronized) {
            return;
        }
        const margins = this._calculateValueMargins(this._majorTicks);
        const canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, this._canvas, {
            startPadding: margins.startPadding,
            endPadding: margins.endPadding
        });
        this._translator.updateCanvas(this._processCanvas(canvas));
        if (isFinite(margins.interval)) {
            const br = this._translator.getBusinessRange();
            br.addRange({
                interval: margins.interval
            });
            this._translator.updateBusinessRange(br);
        }
    },
    _resetMargins: function() {
        this._reinitTranslator(this._getViewportRange());
        if (this._canvas) {
            this._translator.updateCanvas(this._processCanvas(this._canvas));
        }
    },
    _createConstantLines () {
        const constantLines = (this._options.constantLines || []).map((o)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$constant_line$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this, o));
        this._outsideConstantLines = constantLines.filter((l)=>"outside" === l.labelPosition);
        this._insideConstantLines = constantLines.filter((l)=>"inside" === l.labelPosition);
    },
    draw: function(canvas, borderOptions) {
        const that = this;
        const options = this._options;
        that.borderOptions = borderOptions || {
            visible: false
        };
        that._resetMargins();
        that.createTicks(canvas);
        that.applyMargins();
        that._clearAxisGroups();
        initTickCoords(that._majorTicks);
        initTickCoords(that._minorTicks);
        initTickCoords(that._boundaryTicks);
        that._axisGroup.append(that._axesContainerGroup);
        that._drawAxis();
        that._drawTitle();
        drawTickMarks(that._majorTicks, options.tick);
        drawTickMarks(that._minorTicks, options.minorTick);
        drawTickMarks(that._boundaryTicks, options.tick);
        const drawGridLine = that._getGridLineDrawer();
        drawGrids(that._majorTicks, drawGridLine);
        drawGrids(that._minorTicks, drawGridLine);
        callAction(that._majorTicks, "drawLabel", that._getViewportRange(), that._getTemplate(options.label.template));
        that._templatesRendered && that._templatesRendered.reject();
        that._templatesRendered = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        that._majorTicks.forEach(function(tick) {
            tick.labelRotationAngle = 0;
            tick.labelAlignment = void 0;
            tick.labelOffset = 0;
        });
        callAction(that._outsideConstantLines.concat(that._insideConstantLines), "draw");
        callAction(that._strips, "draw");
        that._dateMarkers = that._drawDateMarkers() || [];
        that._stripLabelAxesGroup && that._axisStripLabelGroup.append(that._stripLabelAxesGroup);
        that._gridContainerGroup && that._axisGridGroup.append(that._gridContainerGroup);
        that._stripsGroup && that._axisStripGroup.append(that._stripsGroup);
        that._labelsAxesGroup && that._axisElementsGroup.append(that._labelsAxesGroup);
        if (that._constantLinesGroup) {
            that._axisConstantLineGroups.above.inside.append(that._constantLinesGroup.above);
            that._axisConstantLineGroups.above.outside1.append(that._constantLinesGroup.above);
            that._axisConstantLineGroups.above.outside2.append(that._constantLinesGroup.above);
            that._axisConstantLineGroups.under.inside.append(that._constantLinesGroup.under);
            that._axisConstantLineGroups.under.outside1.append(that._constantLinesGroup.under);
            that._axisConstantLineGroups.under.outside2.append(that._constantLinesGroup.under);
        }
        that._measureTitle();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(that._majorTicks);
        !options.label.template && that._applyWordWrap();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(that._outsideConstantLines);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(that._insideConstantLines);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(that._strips);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(that._dateMarkers);
        that._adjustConstantLineLabels(that._insideConstantLines);
        that._adjustStripLabels();
        let offset = that._constantLabelOffset = that._adjustConstantLineLabels(that._outsideConstantLines);
        if (!that._translator.getBusinessRange().isEmpty()) {
            that._setLabelsPlacement();
            offset = that._adjustLabels(offset);
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"].apply(this, that._majorTicks.map((tick)=>tick.getTemplateDeferred())).done(()=>{
            that._templatesRendered.resolve();
        });
        offset = that._adjustDateMarkers(offset);
        that._adjustTitle(offset);
    },
    getTemplatesDef () {
        return this._templatesRendered;
    },
    setRenderedState (state) {
        this._drawn = state;
    },
    isRendered () {
        return this._drawn;
    },
    _applyWordWrap () {
        const that = this;
        let convertedTickInterval;
        let textWidth;
        let textHeight;
        const options = this._options;
        const tickInterval = that._tickInterval;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tickInterval)) {
            convertedTickInterval = that.getTranslator().getInterval("datetime" === options.dataType ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds(tickInterval) : tickInterval);
        }
        const displayMode = that._validateDisplayMode(options.label.displayMode);
        const overlappingMode = that._validateOverlappingMode(options.label.overlappingBehavior, displayMode);
        const wordWrapMode = options.label.wordWrap || "none";
        const overflowMode = options.label.textOverflow || "none";
        if (("none" !== wordWrapMode || "none" !== overflowMode) && displayMode !== ROTATE && overlappingMode !== ROTATE && "auto" !== overlappingMode) {
            const usefulSpace = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.placeholderSize) ? options.placeholderSize - options.label.indentFromAxis : void 0;
            if (that._isHorizontal) {
                textWidth = convertedTickInterval;
                textHeight = usefulSpace;
            } else {
                textWidth = usefulSpace;
                textHeight = convertedTickInterval;
            }
            let correctByWidth = false;
            let correctByHeight = false;
            if (textWidth) {
                if (that._majorTicks.some((tick)=>tick.labelBBox.width > textWidth)) {
                    correctByWidth = true;
                }
            }
            if (textHeight) {
                if (that._majorTicks.some((tick)=>tick.labelBBox.height > textHeight)) {
                    correctByHeight = true;
                }
            }
            if (correctByWidth || correctByHeight) {
                that._majorTicks.forEach((tick)=>{
                    tick.label && tick.label.setMaxSize(textWidth, textHeight, options.label);
                });
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["measureLabels"])(that._majorTicks);
            }
        }
    },
    _measureTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    animate () {
        callAction(this._majorTicks, "animateLabels");
    },
    updateSize (canvas, animate) {
        let updateTitle = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : true;
        const that = this;
        that.updateCanvas(canvas);
        if (updateTitle) {
            that._checkTitleOverflow();
            that._measureTitle();
            that._updateTitleCoords();
        }
        that._reinitTranslator(that._getViewportRange());
        that.applyMargins();
        const animationEnabled = !that._firstDrawing && animate;
        const options = that._options;
        initTickCoords(that._majorTicks);
        initTickCoords(that._minorTicks);
        initTickCoords(that._boundaryTicks);
        if (that._resetApplyingAnimation && !that._firstDrawing) {
            that._resetStartCoordinates();
        }
        cleanUpInvalidTicks(that._majorTicks);
        cleanUpInvalidTicks(that._minorTicks);
        cleanUpInvalidTicks(that._boundaryTicks);
        if (that._axisElement) {
            that._updateAxisElementPosition();
        }
        updateTicksPosition(that._majorTicks, options.tick, animationEnabled);
        updateTicksPosition(that._minorTicks, options.minorTick, animationEnabled);
        updateTicksPosition(that._boundaryTicks, options.tick);
        callAction(that._majorTicks, "updateLabelPosition", animationEnabled);
        that._outsideConstantLines.concat(that._insideConstantLines || []).forEach((l)=>l.updatePosition(animationEnabled));
        callAction(that._strips, "updatePosition", animationEnabled);
        updateGridsPosition(that._majorTicks, animationEnabled);
        updateGridsPosition(that._minorTicks, animationEnabled);
        if (animationEnabled) {
            callAction(that._ticksToRemove || [], "fadeOutElements");
        }
        that.prepareAnimation();
        that._ticksToRemove = null;
        if (!that._translator.getBusinessRange().isEmpty()) {
            that._firstDrawing = false;
        }
        that._resetApplyingAnimation = false;
        that._updateLabelsPosition();
    },
    _updateLabelsPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    prepareAnimation () {
        const action = "saveCoords";
        callAction(this._majorTicks, action);
        callAction(this._minorTicks, action);
        callAction(this._insideConstantLines, action);
        callAction(this._outsideConstantLines, action);
        callAction(this._strips, action);
    },
    _resetStartCoordinates () {
        const action = "resetCoordinates";
        callAction(this._majorTicks, action);
        callAction(this._minorTicks, action);
        callAction(this._insideConstantLines, action);
        callAction(this._outsideConstantLines, action);
        callAction(this._strips, action);
    },
    applyClipRects: function(elementsClipID, canvasClipID) {
        this._axisGroup.attr({
            "clip-path": canvasClipID
        });
        this._axisStripGroup.attr({
            "clip-path": elementsClipID
        });
        this._axisElementsGroup.attr({
            "clip-path": canvasClipID
        });
    },
    _validateVisualRange (optionValue) {
        const range = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])(optionValue);
        if (void 0 !== range.startValue) {
            range.startValue = this.validateUnit(range.startValue);
        }
        if (void 0 !== range.endValue) {
            range.endValue = this.validateUnit(range.endValue);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertVisualRangeObject"])(range, !_isArray(optionValue));
    },
    _validateOptions (options) {
        options.wholeRange = this._validateVisualRange(options.wholeRange);
        options.visualRange = options._customVisualRange = this._validateVisualRange(options._customVisualRange);
        this._setVisualRange(options._customVisualRange);
    },
    validate () {
        const options = this._options;
        const dataType = this.isArgumentAxis ? options.argumentType : options.valueType;
        const parser = dataType ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$parse_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getParser"])(dataType) : function(unit) {
            return unit;
        };
        this.parser = parser;
        options.dataType = dataType;
        this._validateOptions(options);
    },
    resetVisualRange (isSilent) {
        this._seriesData.minVisible = this._seriesData.min;
        this._seriesData.maxVisible = this._seriesData.max;
        this.handleZooming([
            null,
            null
        ], {
            start: !!isSilent,
            end: !!isSilent
        });
    },
    _setVisualRange (visualRange, allowPartialUpdate) {
        const range = this.adjustRange((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])(visualRange));
        if (allowPartialUpdate) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(range.startValue) && (this._viewport.startValue = range.startValue);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(range.endValue) && (this._viewport.endValue = range.endValue);
        } else {
            this._viewport = range;
        }
    },
    _applyZooming (visualRange, allowPartialUpdate) {
        this._resetVisualRangeOption();
        this._setVisualRange(visualRange, allowPartialUpdate);
        const viewPort = this.getViewport();
        this._seriesData.userBreaks = this._getScaleBreaks(this._options, {
            minVisible: viewPort.startValue,
            maxVisible: viewPort.endValue
        }, this._series, this.isArgumentAxis);
        this._translator.updateBusinessRange(this._getViewportRange());
    },
    getZoomStartEventArg (event, actionType) {
        return {
            axis: this,
            range: this.visualRange(),
            cancel: false,
            event: event,
            actionType: actionType
        };
    },
    _getZoomEndEventArg (previousRange, event, actionType, zoomFactor, shift) {
        const newRange = this.visualRange();
        return {
            axis: this,
            previousRange: previousRange,
            range: newRange,
            cancel: false,
            event: event,
            actionType: actionType,
            zoomFactor: zoomFactor,
            shift: shift,
            rangeStart: newRange.startValue,
            rangeEnd: newRange.endValue
        };
    },
    getZoomBounds () {
        const wholeRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])(this._options.wholeRange);
        const range = this.getTranslator().getBusinessRange();
        const secondPriorityRange = {
            startValue: getZoomBoundValue(this._initRange.startValue, range.min),
            endValue: getZoomBoundValue(this._initRange.endValue, range.max)
        };
        return {
            startValue: getZoomBoundValue(wholeRange.startValue, secondPriorityRange.startValue),
            endValue: getZoomBoundValue(wholeRange.endValue, secondPriorityRange.endValue)
        };
    },
    setInitRange () {
        this._initRange = {};
        if (0 === Object.keys(this._options.wholeRange || {}).length) {
            this._initRange = this.getZoomBounds();
        }
    },
    _resetVisualRangeOption () {
        this._options._customVisualRange = {};
    },
    getTemplatesGroups () {
        const ticks = this._majorTicks;
        if (ticks) {
            return this._majorTicks.map((tick)=>tick.templateContainer).filter((item)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(item));
        } else {
            return [];
        }
    },
    setCustomVisualRange (range) {
        this._options._customVisualRange = range;
    },
    visualRange () {
        const that = this;
        const args = arguments;
        let visualRange;
        if (0 === args.length) {
            const adjustedRange = that._getAdjustedBusinessRange();
            let startValue = adjustedRange.minVisible;
            let endValue = adjustedRange.maxVisible;
            if (that._options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete) {
                startValue = startValue !== null && startValue !== void 0 ? startValue : adjustedRange.categories[0];
                endValue = endValue !== null && endValue !== void 0 ? endValue : adjustedRange.categories[adjustedRange.categories.length - 1];
                return {
                    startValue: startValue,
                    endValue: endValue,
                    categories: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(adjustedRange.categories, startValue, endValue).categories
                };
            }
            return {
                startValue: startValue,
                endValue: endValue
            };
        } else if (_isArray(args[0])) {
            visualRange = args[0];
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(args[0])) {
            visualRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, args[0]);
        } else {
            visualRange = [
                args[0],
                args[1]
            ];
        }
        const zoomResults = that.handleZooming(visualRange, args[1]);
        if (!zoomResults.isPrevented) {
            that._visualRange(that, zoomResults);
        }
    },
    handleZooming (visualRange, preventEvents, domEvent, action) {
        const that = this;
        preventEvents = preventEvents || {};
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(visualRange)) {
            visualRange = that._validateVisualRange(visualRange);
            visualRange.action = action;
        }
        const zoomStartEvent = that.getZoomStartEventArg(domEvent, action);
        const previousRange = zoomStartEvent.range;
        !preventEvents.start && that._eventTrigger("zoomStart", zoomStartEvent);
        const zoomResults = {
            isPrevented: zoomStartEvent.cancel,
            skipEventRising: preventEvents.skipEventRising,
            range: visualRange || zoomStartEvent.range
        };
        if (!zoomStartEvent.cancel) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(visualRange) && that._applyZooming(visualRange, preventEvents.allowPartialUpdate);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(that._storedZoomEndParams)) {
                that._storedZoomEndParams = {
                    startRange: previousRange,
                    type: this.getOptions().type
                };
            }
            that._storedZoomEndParams.event = domEvent;
            that._storedZoomEndParams.action = action;
            that._storedZoomEndParams.prevent = !!preventEvents.end;
        }
        return zoomResults;
    },
    handleZoomEnd () {
        const that = this;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(that._storedZoomEndParams) && !that._storedZoomEndParams.prevent) {
            const previousRange = that._storedZoomEndParams.startRange;
            const domEvent = that._storedZoomEndParams.event;
            const action = that._storedZoomEndParams.action;
            const previousBusinessRange = {
                minVisible: previousRange.startValue,
                maxVisible: previousRange.endValue,
                categories: previousRange.categories
            };
            const typeIsNotChanged = that.getOptions().type === that._storedZoomEndParams.type;
            const shift = typeIsNotChanged ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["adjust"])(that.getVisualRangeCenter() - that.getVisualRangeCenter(previousBusinessRange, false)) : NaN;
            const zoomFactor = typeIsNotChanged ? +(Math.round(that.getVisualRangeLength(previousBusinessRange) / (that.getVisualRangeLength() || 1) + "e+2") + "e-2") : NaN;
            const zoomEndEvent = that._getZoomEndEventArg(previousRange, domEvent, action, zoomFactor, shift);
            zoomEndEvent.cancel = that.checkZoomingLowerLimitOvercome(1 === zoomFactor ? "pan" : "zoom", zoomFactor).stopInteraction;
            that._eventTrigger("zoomEnd", zoomEndEvent);
            if (zoomEndEvent.cancel) {
                that._restorePreviousVisualRange(previousRange);
            }
            that._storedZoomEndParams = null;
        }
    },
    _restorePreviousVisualRange (previousRange) {
        this._storedZoomEndParams = null;
        this._applyZooming(previousRange);
        this._visualRange(this, previousRange);
    },
    checkZoomingLowerLimitOvercome (actionType, zoomFactor, range) {
        const that = this;
        const options = that._options;
        const translator = that._translator;
        let minZoom = options.minVisualRangeLength;
        let correctedRange = range;
        let visualRange;
        let isOvercoming = "zoom" === actionType && zoomFactor >= 1;
        const businessRange = translator.getBusinessRange();
        if (range) {
            visualRange = that.adjustRange((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])(range));
            visualRange = {
                minVisible: visualRange.startValue,
                maxVisible: visualRange.endValue,
                categories: businessRange.categories
            };
        }
        const beforeVisualRangeLength = that.getVisualRangeLength(businessRange);
        const afterVisualRangeLength = that.getVisualRangeLength(visualRange);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(minZoom) || "discrete" === options.type) {
            minZoom = translator.convert(minZoom);
            if (visualRange && minZoom < beforeVisualRangeLength && minZoom >= afterVisualRangeLength) {
                correctedRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVizRangeObject"])(translator.getRangeByMinZoomValue(minZoom, visualRange));
                isOvercoming = false;
            } else {
                isOvercoming &= minZoom > afterVisualRangeLength;
            }
        } else {
            const canvasLength = that._translator.canvasLength;
            const fullRange = {
                minVisible: businessRange.min,
                maxVisible: businessRange.max,
                categories: businessRange.categories
            };
            isOvercoming &= that.getVisualRangeLength(fullRange) / canvasLength >= afterVisualRangeLength;
        }
        return {
            stopInteraction: !!isOvercoming,
            correctedRange: correctedRange
        };
    },
    isExtremePosition (isMax) {
        let extremeDataValue;
        let seriesData;
        if ("discrete" === this._options.type) {
            seriesData = this._translator.getBusinessRange();
            extremeDataValue = isMax ? seriesData.categories[seriesData.categories.length - 1] : seriesData.categories[0];
        } else {
            seriesData = this.getZoomBounds();
            extremeDataValue = isMax ? seriesData.endValue : seriesData.startValue;
        }
        const translator = this.getTranslator();
        const extremePoint = translator.translate(extremeDataValue);
        const visualRange = this.visualRange();
        const visualRangePoint = isMax ? translator.translate(visualRange.endValue) : translator.translate(visualRange.startValue);
        return _abs(visualRangePoint - extremePoint) < 5;
    },
    getViewport () {
        return this._viewport;
    },
    getFullTicks: function() {
        const majors = this._majorTicks || [];
        if (this._options.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].discrete) {
            return convertTicksToValues(majors);
        } else {
            return convertTicksToValues(majors.concat(this._minorTicks, this._boundaryTicks)).sort(function(a, b) {
                return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(a) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["valueOf"])(b);
            });
        }
    },
    measureLabels: function(canvas, withIndents) {
        const that = this;
        const options = that._options;
        const widthAxis = options.visible ? options.width : 0;
        let ticks;
        const indent = withIndents ? options.label.indentFromAxis + .5 * options.tick.length : 0;
        let tickInterval;
        const viewportRange = that._getViewportRange();
        if (viewportRange.isEmpty() || !options.label.visible || !that._axisElementsGroup) {
            return {
                height: widthAxis,
                width: widthAxis,
                x: 0,
                y: 0
            };
        }
        if (that._majorTicks) {
            ticks = convertTicksToValues(that._majorTicks);
        } else {
            that.updateCanvas(canvas);
            ticks = that._createTicksAndLabelFormat(viewportRange, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"]);
            tickInterval = ticks.tickInterval;
            ticks = ticks.ticks;
        }
        const maxText = ticks.reduce(function(prevLabel, tick, index) {
            const label = that.formatLabel(tick, options.label, viewportRange, void 0, tickInterval, ticks);
            if (prevLabel.length < label.length) {
                return label;
            } else {
                return prevLabel;
            }
        }, that.formatLabel(ticks[0], options.label, viewportRange, void 0, tickInterval, ticks));
        const text = that._renderer.text(maxText, 0, 0).css(that._textFontStyles).attr(that._textOptions).append(that._renderer.root);
        const box = text.getBBox();
        text.remove();
        return {
            x: box.x,
            y: box.y,
            width: box.width + indent,
            height: box.height + indent
        };
    },
    _setLabelsPlacement: function() {
        if (!this._options.label.visible) {
            return;
        }
        const that = this;
        const labelOpt = that._options.label;
        const displayMode = that._validateDisplayMode(labelOpt.displayMode);
        const overlappingMode = that._validateOverlappingMode(labelOpt.overlappingBehavior, displayMode);
        const ignoreOverlapping = "none" === overlappingMode || "ignore" === overlappingMode;
        const behavior = {
            rotationAngle: labelOpt.rotationAngle,
            staggeringSpacing: labelOpt.staggeringSpacing
        };
        let notRecastStep;
        const boxes = that._majorTicks.map(function(tick) {
            return tick.labelBBox;
        });
        let step = that._getStep(boxes);
        switch(displayMode){
            case ROTATE:
                if (ignoreOverlapping) {
                    notRecastStep = true;
                    step = 1;
                }
                that._applyLabelMode(displayMode, step, boxes, labelOpt, notRecastStep);
                break;
            case "stagger":
                if (ignoreOverlapping) {
                    step = 2;
                }
                that._applyLabelMode(displayMode, _max(step, 2), boxes, labelOpt);
                break;
            default:
                that._applyLabelOverlapping(boxes, overlappingMode, step, behavior);
        }
    },
    _applyLabelOverlapping: function(boxes, mode, step, behavior) {
        const that = this;
        const labelOpt = that._options.label;
        const majorTicks = that._majorTicks;
        if ("none" === mode || "ignore" === mode) {
            return;
        }
        if (step > 1 && boxes.some(function(box, index, array) {
            if (0 === index) {
                return false;
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$axes_constants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].areLabelsOverlap(box, array[index - 1], labelOpt.minSpacing, labelOpt.alignment);
        })) {
            that._applyLabelMode(mode, step, boxes, behavior);
        }
        that._checkBoundedLabelsOverlapping(majorTicks, boxes, mode);
        that._checkShiftedLabels(majorTicks, boxes, labelOpt.minSpacing, labelOpt.alignment);
    },
    _applyLabelMode: function(mode, step, boxes, behavior, notRecastStep) {
        const that = this;
        const majorTicks = that._majorTicks;
        const labelOpt = that._options.label;
        const angle = behavior.rotationAngle;
        let labelHeight;
        let alignment;
        let func;
        switch(mode){
            case ROTATE:
                if (!labelOpt.userAlignment) {
                    alignment = angle < 0 ? RIGHT : LEFT;
                    if (angle % 90 === 0) {
                        alignment = CENTER;
                    }
                }
                step = notRecastStep ? step : that._getStep(boxes, angle);
                func = function(tick) {
                    const contentContainer = tick.getContentContainer();
                    if (!contentContainer) {
                        return;
                    }
                    contentContainer.rotate(angle);
                    tick.labelRotationAngle = angle;
                    alignment && (tick.labelAlignment = alignment);
                };
                updateLabels(majorTicks, step, func);
                break;
            case "stagger":
                labelHeight = that._getMaxLabelHeight(boxes, behavior.staggeringSpacing);
                func = function(tick, index) {
                    if (index / (step - 1) % 2 !== 0) {
                        tick.labelOffset = labelHeight;
                    }
                };
                updateLabels(majorTicks, step - 1, func);
                break;
            case "auto":
            case "_auto":
                if (2 === step) {
                    that._applyLabelMode("stagger", step, boxes, behavior);
                } else {
                    that._applyLabelMode(ROTATE, step, boxes, {
                        rotationAngle: getOptimalAngle(boxes, labelOpt)
                    });
                }
                break;
            default:
                updateLabels(majorTicks, step);
        }
    },
    getMarkerTrackers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _drawDateMarkers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _adjustDateMarkers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    coordsIn: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    areCoordsOutsideAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _getSkippedCategory: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _initAxisPositions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _drawTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _updateTitleCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _adjustConstantLineLabels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _createTranslator: function() {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$translator2d$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Translator2D"]({}, {}, {});
    },
    _updateTranslator: function() {
        const translator = this._translator;
        translator.update(translator.getBusinessRange(), this._canvas || {}, this._getTranslatorOptions());
    },
    _getTranslatorOptions: function() {
        var _options$workWeek2, _options$breakStyle;
        const options = this._options;
        var _ref;
        return {
            isHorizontal: this._isHorizontal,
            shiftZeroValue: !this.isArgumentAxis,
            interval: options.semiDiscreteInterval,
            firstDayOfWeek: null === (_options$workWeek2 = options.workWeek) || void 0 === _options$workWeek2 ? void 0 : _options$workWeek2[0],
            stick: this._getStick(),
            breaksSize: (_ref = null === (_options$breakStyle = options.breakStyle) || void 0 === _options$breakStyle ? void 0 : _options$breakStyle.width) !== null && _ref !== void 0 ? _ref : 0
        };
    },
    getVisibleArea () {
        const canvas = this._getCanvasStartEnd();
        return [
            canvas.start,
            canvas.end
        ].sort((a, b)=>a - b);
    },
    _getCanvasStartEnd: function() {
        const isHorizontal = this._isHorizontal;
        const canvas = this._canvas || {};
        const invert = this._translator.getBusinessRange().invert;
        const coords = isHorizontal ? [
            canvas.left,
            canvas.width - canvas.right
        ] : [
            canvas.height - canvas.bottom,
            canvas.top
        ];
        invert && coords.reverse();
        return {
            start: coords[0],
            end: coords[1]
        };
    },
    _getScreenDelta: function() {
        const canvas = this._getCanvasStartEnd();
        const breaks = this._seriesData ? this._seriesData.breaks || [] : [];
        const breaksLength = breaks.length;
        const screenDelta = _abs(canvas.start - canvas.end);
        return screenDelta - (breaksLength ? breaks[breaksLength - 1].cumulativeWidth : 0);
    },
    _getScaleBreaks: function() {
        return [];
    },
    _filterBreaks: function() {
        return [];
    },
    _adjustTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _checkTitleOverflow: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getSpiderTicks: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    setSpiderTicks: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _checkBoundedLabelsOverlapping: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _checkShiftedLabels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    drawScaleBreaks: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _visualRange: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _rotateConstantLine: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    applyVisualRangeSetter (visualRangeSetter) {
        this._visualRange = visualRangeSetter;
    },
    getCategoriesSorter (argCategories) {
        let sort;
        if (this.isArgumentAxis) {
            sort = argCategories;
        } else {
            const categoriesSortingMethod = this._options.categoriesSortingMethod;
            sort = categoriesSortingMethod !== null && categoriesSortingMethod !== void 0 ? categoriesSortingMethod : this._options.categories;
        }
        return sort;
    },
    _getAdjustedBusinessRange () {
        return this.adjustViewport(this._translator.getBusinessRange());
    }
};
}),
}]);

//# sourceMappingURL=node_modules_devextreme_esm_viz_axes_f310ea32._.js.map