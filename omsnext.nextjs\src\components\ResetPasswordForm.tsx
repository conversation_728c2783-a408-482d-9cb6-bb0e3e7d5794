"use client";
import { useState, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import Form, {
  Item,
  Label,
  ButtonItem,
  RequiredRule,
} from "devextreme-react/form";
import LoadIndicator from "devextreme-react/load-indicator";
import styles from "./LoginForm.module.css"; // Reuse the same styles as LoginForm

// CardAuth komponens (reusing the same component from LoginForm)
const CardAuth = ({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) => {
  return (
    <div className={styles.authCard}>
      <div className={`dx-card ${styles.cardContent}`}>
        <div className={styles.header}>
          <div className={styles.title}>{title}</div>
          <div className={styles.description}>{description}</div>
        </div>
        {children}
      </div>
    </div>
  );
};

export default function ResetPasswordForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const formData = useRef({ password: "", confirmPassword: "" });

  const onSubmit = useCallback(
    async (e: any) => {
      e.preventDefault();
      const { password, confirmPassword } = formData.current;
      setLoading(true);
      setError(null);
      setSuccess(null);

      // Basic validation
      if (password !== confirmPassword) {
        setError("A jelszavak nem egyeznek!");
        setLoading(false);
        return;
      }

      if (password.length < 6) {
        setError("A jelszónak legalább 6 karakter hosszúnak kell lennie!");
        setLoading(false);
        return;
      }

      try {
        // In a real app, this would call your API endpoint
        // For now, we'll simulate the request
        await new Promise((resolve) => setTimeout(resolve, 1000));

        // Mock success response
        setSuccess(
          "Jelszó sikeresen megváltoztatva! Átirányítás a bejelentkezéshez..."
        );

        // Redirect to login after a short delay
        setTimeout(() => {
          router.push("/login");
        }, 2000);
      } catch (err: any) {
        setError(err.message || "Hiba történt a jelszó megváltoztatása során.");
        setLoading(false);
      }
    },
    [router]
  );

  const passwordEditorOptions = {
    stylingMode: "filled" as const,
    mode: "password" as const,
    placeholder: "Új jelszó",
    onValueChanged: (e: any) => {
      formData.current.password = e.value;
    },
  };

  const confirmPasswordEditorOptions = {
    stylingMode: "filled" as const,
    mode: "password" as const,
    placeholder: "Jelszó megerősítése",
    onValueChanged: (e: any) => {
      formData.current.confirmPassword = e.value;
    },
  };

  const submitButtonOptions = {
    width: "100%",
    type: "default" as const,
    useSubmitBehavior: true,
    text: loading ? undefined : "Jelszó megváltoztatása",
    template: loading
      ? () => <LoadIndicator width={24} height={24} />
      : undefined,
    disabled: loading,
  };

  return (
    <CardAuth
      title="Jelszó megváltoztatása"
      description="Adja meg új jelszavát a fiókja biztonsága érdekében."
    >
      <form onSubmit={onSubmit} className={styles.loginForm}>
        <Form formData={formData.current}>
          <Item
            dataField="password"
            editorType="dxTextBox"
            editorOptions={passwordEditorOptions}
          >
            <RequiredRule message="Jelszó megadása kötelező" />
            <Label text="Új jelszó" />
          </Item>

          <Item
            dataField="confirmPassword"
            editorType="dxTextBox"
            editorOptions={confirmPasswordEditorOptions}
          >
            <RequiredRule message="Jelszó megerősítése kötelező" />
            <Label text="Jelszó megerősítése" />
          </Item>

          {error && (
            <Item>
              <div className={styles.errorMessage}>{error}</div>
            </Item>
          )}

          {success && (
            <Item>
              <div className={styles.successMessage}>{success}</div>
            </Item>
          )}

          <ButtonItem
            buttonOptions={submitButtonOptions}
            cssClass="login-button"
          />
        </Form>
      </form>
    </CardAuth>
  );
}
