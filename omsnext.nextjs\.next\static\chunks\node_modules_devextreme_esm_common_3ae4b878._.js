(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator_callbacks.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/event_registrator_callbacks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$memorized_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/memorized_callbacks.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$memorized_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/hook_touch_props.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/hook_touch_props.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_hook_touch_props$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_hook_touch_props.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/hook_touch_props.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_hook_touch_props$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_hook_touch_props.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$hook_touch_props$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/hook_touch_props.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/utils/event_target.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/utils/event_target.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_target.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/utils/event_target.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_target.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/event_target.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/config.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/config.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_config.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/common/guid.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/guid.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_guid.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Guid"];
}),
"[project]/node_modules/devextreme/esm/common/set_template_engine.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/set_template_engine.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_set_template_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_set_template_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template_engine_registry$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template_engine_registry.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template_engine_registry$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setTemplateEngine"];
}),
"[project]/node_modules/devextreme/esm/common/config.js [app-client] (ecmascript) <export default as config>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "config": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/config.js [app-client] (ecmascript)");
}),
"[project]/node_modules/devextreme/esm/common/guid.js [app-client] (ecmascript) <export default as Guid>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "Guid": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/guid.js [app-client] (ecmascript)");
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/events_engine.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/localization/cldr-data/parent_locales.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/cldr-data/parent_locales.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ // !!! AUTO-GENERATED FILE, DO NOT EDIT
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {
    "en-150": "en-001",
    "en-AG": "en-001",
    "en-AI": "en-001",
    "en-AU": "en-001",
    "en-BB": "en-001",
    "en-BM": "en-001",
    "en-BS": "en-001",
    "en-BW": "en-001",
    "en-BZ": "en-001",
    "en-CC": "en-001",
    "en-CK": "en-001",
    "en-CM": "en-001",
    "en-CX": "en-001",
    "en-CY": "en-001",
    "en-DG": "en-001",
    "en-DM": "en-001",
    "en-ER": "en-001",
    "en-FJ": "en-001",
    "en-FK": "en-001",
    "en-FM": "en-001",
    "en-GB": "en-001",
    "en-GD": "en-001",
    "en-GG": "en-001",
    "en-GH": "en-001",
    "en-GI": "en-001",
    "en-GM": "en-001",
    "en-GY": "en-001",
    "en-HK": "en-001",
    "en-IE": "en-001",
    "en-IL": "en-001",
    "en-IM": "en-001",
    "en-IN": "en-001",
    "en-IO": "en-001",
    "en-JE": "en-001",
    "en-JM": "en-001",
    "en-KE": "en-001",
    "en-KI": "en-001",
    "en-KN": "en-001",
    "en-KY": "en-001",
    "en-LC": "en-001",
    "en-LR": "en-001",
    "en-LS": "en-001",
    "en-MG": "en-001",
    "en-MO": "en-001",
    "en-MS": "en-001",
    "en-MT": "en-001",
    "en-MU": "en-001",
    "en-MV": "en-001",
    "en-MW": "en-001",
    "en-MY": "en-001",
    "en-NA": "en-001",
    "en-NF": "en-001",
    "en-NG": "en-001",
    "en-NR": "en-001",
    "en-NU": "en-001",
    "en-NZ": "en-001",
    "en-PG": "en-001",
    "en-PK": "en-001",
    "en-PN": "en-001",
    "en-PW": "en-001",
    "en-RW": "en-001",
    "en-SB": "en-001",
    "en-SC": "en-001",
    "en-SD": "en-001",
    "en-SG": "en-001",
    "en-SH": "en-001",
    "en-SL": "en-001",
    "en-SS": "en-001",
    "en-SX": "en-001",
    "en-SZ": "en-001",
    "en-TC": "en-001",
    "en-TK": "en-001",
    "en-TO": "en-001",
    "en-TT": "en-001",
    "en-TV": "en-001",
    "en-TZ": "en-001",
    "en-UG": "en-001",
    "en-VC": "en-001",
    "en-VG": "en-001",
    "en-VU": "en-001",
    "en-WS": "en-001",
    "en-ZA": "en-001",
    "en-ZM": "en-001",
    "en-ZW": "en-001",
    "en-AT": "en-150",
    "en-BE": "en-150",
    "en-CH": "en-150",
    "en-DE": "en-150",
    "en-DK": "en-150",
    "en-FI": "en-150",
    "en-NL": "en-150",
    "en-SE": "en-150",
    "en-SI": "en-150",
    "hi-Latn": "en-IN",
    "es-AR": "es-419",
    "es-BO": "es-419",
    "es-BR": "es-419",
    "es-BZ": "es-419",
    "es-CL": "es-419",
    "es-CO": "es-419",
    "es-CR": "es-419",
    "es-CU": "es-419",
    "es-DO": "es-419",
    "es-EC": "es-419",
    "es-GT": "es-419",
    "es-HN": "es-419",
    "es-MX": "es-419",
    "es-NI": "es-419",
    "es-PA": "es-419",
    "es-PE": "es-419",
    "es-PR": "es-419",
    "es-PY": "es-419",
    "es-SV": "es-419",
    "es-US": "es-419",
    "es-UY": "es-419",
    "es-VE": "es-419",
    nb: "no",
    nn: "no",
    "pt-AO": "pt-PT",
    "pt-CH": "pt-PT",
    "pt-CV": "pt-PT",
    "pt-FR": "pt-PT",
    "pt-GQ": "pt-PT",
    "pt-GW": "pt-PT",
    "pt-LU": "pt-PT",
    "pt-MO": "pt-PT",
    "pt-MZ": "pt-PT",
    "pt-ST": "pt-PT",
    "pt-TL": "pt-PT",
    "az-Arab": "und",
    "az-Cyrl": "und",
    "bal-Latn": "und",
    "blt-Latn": "und",
    "bm-Nkoo": "und",
    "bs-Cyrl": "und",
    "byn-Latn": "und",
    "cu-Glag": "und",
    "dje-Arab": "und",
    "dyo-Arab": "und",
    "en-Dsrt": "und",
    "en-Shaw": "und",
    "ff-Adlm": "und",
    "ff-Arab": "und",
    "ha-Arab": "und",
    "iu-Latn": "und",
    "kk-Arab": "und",
    "ks-Deva": "und",
    "ku-Arab": "und",
    "ky-Arab": "und",
    "ky-Latn": "und",
    "ml-Arab": "und",
    "mn-Mong": "und",
    "mni-Mtei": "und",
    "ms-Arab": "und",
    "pa-Arab": "und",
    "sat-Deva": "und",
    "sd-Deva": "und",
    "sd-Khoj": "und",
    "sd-Sind": "und",
    "shi-Latn": "und",
    "so-Arab": "und",
    "sr-Latn": "und",
    "sw-Arab": "und",
    "tg-Arab": "und",
    "ug-Cyrl": "und",
    "uz-Arab": "und",
    "uz-Cyrl": "und",
    "vai-Latn": "und",
    "wo-Arab": "und",
    "yo-Arab": "und",
    "yue-Hans": "und",
    "zh-Hant": "und",
    "zh-Hant-MO": "zh-Hant-HK"
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/parentLocale.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/parentLocale.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const PARENT_LOCALE_SEPARATOR = "-";
const __TURBOPACK__default__export__ = (parentLocales, locale)=>{
    const parentLocale = parentLocales[locale];
    if (parentLocale) {
        return "root" !== parentLocale && parentLocale;
    }
    return locale.substr(0, locale.lastIndexOf("-"));
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/core.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$cldr$2d$data$2f$parent_locales$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/cldr-data/parent_locales.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$parentLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/parentLocale.js [app-client] (ecmascript)");
;
;
;
const DEFAULT_LOCALE = "en";
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    locale: (()=>{
        let currentLocale = "en";
        return (locale)=>{
            if (!locale) {
                return currentLocale;
            }
            currentLocale = locale;
        };
    })(),
    getValueByClosestLocale: function(getter) {
        let locale = this.locale();
        let value = getter(locale);
        let isRootLocale;
        while(!value && !isRootLocale){
            locale = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$parentLocale$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$cldr$2d$data$2f$parent_locales$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], locale);
            if (locale) {
                value = getter(locale);
            } else {
                isRootLocale = true;
            }
        }
        if (void 0 === value && "en" !== locale) {
            return getter("en");
        }
        return value;
    }
});
}),
"[project]/node_modules/devextreme/esm/common/core/localization/default_messages.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/default_messages.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ // !!! AUTO-GENERATED FILE, DO NOT EDIT
__turbopack_context__.s({
    "defaultMessages": ()=>defaultMessages
});
const defaultMessages = {
    en: {
        Yes: "Yes",
        No: "No",
        Cancel: "Cancel",
        CheckState: "Check state",
        Close: "Close",
        Clear: "Clear",
        Done: "Done",
        Loading: "Loading...",
        Select: "Select...",
        Search: "Search",
        Back: "Back",
        OK: "OK",
        Today: "Today",
        Yesterday: "Yesterday",
        "dxCollectionWidget-noDataText": "No data to display",
        "dxDropDownEditor-selectLabel": "Select",
        "validation-required": "Required",
        "validation-required-formatted": "{0} is required",
        "validation-numeric": "Value must be a number",
        "validation-numeric-formatted": "{0} must be a number",
        "validation-range": "Value is out of range",
        "validation-range-formatted": "{0} is out of range",
        "validation-stringLength": "The length of the value is not correct",
        "validation-stringLength-formatted": "The length of {0} is not correct",
        "validation-custom": "Value is invalid",
        "validation-custom-formatted": "{0} is invalid",
        "validation-async": "Value is invalid",
        "validation-async-formatted": "{0} is invalid",
        "validation-compare": "Values do not match",
        "validation-compare-formatted": "{0} does not match",
        "validation-pattern": "Value does not match pattern",
        "validation-pattern-formatted": "{0} does not match pattern",
        "validation-email": "Email is invalid",
        "validation-email-formatted": "{0} is invalid",
        "validation-mask": "Value is invalid",
        "dxLookup-searchPlaceholder": "Minimum character number: {0}",
        "dxList-pullingDownText": "Pull down to refresh...",
        "dxList-pulledDownText": "Release to refresh...",
        "dxList-refreshingText": "Refreshing...",
        "dxList-pageLoadingText": "Loading...",
        "dxList-nextButtonText": "More",
        "dxList-selectAll": "Select All",
        "dxList-listAriaLabel": "Items",
        "dxList-listAriaLabel-deletable": "Deletable items",
        "dxListEditDecorator-delete": "Delete",
        "dxListEditDecorator-more": "More",
        "dxList-selectAll-indeterminate": "Half-checked",
        "dxList-selectAll-checked": "Checked",
        "dxList-selectAll-notChecked": "Not checked",
        "dxList-ariaRoleDescription": "List",
        "dxList-listAriaLabel-itemContent": "List item content",
        "dxScrollView-pullingDownText": "Pull down to refresh...",
        "dxScrollView-pulledDownText": "Release to refresh...",
        "dxScrollView-refreshingText": "Refreshing...",
        "dxScrollView-reachBottomText": "Loading...",
        "dxDateBox-simulatedDataPickerTitleTime": "Select time",
        "dxDateBox-simulatedDataPickerTitleDate": "Select date",
        "dxDateBox-simulatedDataPickerTitleDateTime": "Select date and time",
        "dxDateBox-validation-datetime": "Value must be a date or time",
        "dxDateRangeBox-invalidStartDateMessage": "Start value must be a date",
        "dxDateRangeBox-invalidEndDateMessage": "End value must be a date",
        "dxDateRangeBox-startDateOutOfRangeMessage": "Start date is out of range",
        "dxDateRangeBox-endDateOutOfRangeMessage": "End date is out of range",
        "dxDateRangeBox-startDateLabel": "Start Date",
        "dxDateRangeBox-endDateLabel": "End Date",
        "dxFileUploader-selectFile": "Select a file",
        "dxFileUploader-dropFile": "or Drop a file here",
        "dxFileUploader-bytes": "bytes",
        "dxFileUploader-kb": "KB",
        "dxFileUploader-Mb": "MB",
        "dxFileUploader-Gb": "GB",
        "dxFileUploader-upload": "Upload",
        "dxFileUploader-uploaded": "Uploaded",
        "dxFileUploader-readyToUpload": "Ready to upload",
        "dxFileUploader-uploadAbortedMessage": "Upload cancelled",
        "dxFileUploader-uploadFailedMessage": "Upload failed",
        "dxFileUploader-invalidFileExtension": "File type is not allowed",
        "dxFileUploader-invalidMaxFileSize": "File is too large",
        "dxFileUploader-invalidMinFileSize": "File is too small",
        "dxRangeSlider-ariaFrom": "From",
        "dxRangeSlider-ariaTill": "Till",
        "dxSwitch-switchedOnText": "ON",
        "dxSwitch-switchedOffText": "OFF",
        "dxForm-optionalMark": "optional",
        "dxForm-requiredMessage": "{0} is required",
        "dxNumberBox-invalidValueMessage": "Value must be a number",
        "dxNumberBox-noDataText": "No data",
        "dxDataGrid-emptyHeaderWithColumnChooserText": "Use {0} to display columns",
        "dxDataGrid-emptyHeaderWithGroupPanelText": "Drag a column from the group panel here",
        "dxDataGrid-emptyHeaderWithColumnChooserAndGroupPanelText": "Use {0} or drag a column from the group panel",
        "dxDataGrid-emptyHeaderColumnChooserText": "column chooser",
        "dxDataGrid-columnChooserTitle": "Column Chooser",
        "dxDataGrid-columnChooserEmptyText": "Drag a column here to hide it",
        "dxDataGrid-groupContinuesMessage": "Continues on the next page",
        "dxDataGrid-groupContinuedMessage": "Continued from the previous page",
        "dxDataGrid-groupHeaderText": "Group by This Column",
        "dxDataGrid-ungroupHeaderText": "Ungroup",
        "dxDataGrid-ungroupAllText": "Ungroup All",
        "dxDataGrid-editingEditRow": "Edit",
        "dxDataGrid-editingSaveRowChanges": "Save",
        "dxDataGrid-editingCancelRowChanges": "Cancel",
        "dxDataGrid-editingDeleteRow": "Delete",
        "dxDataGrid-editingUndeleteRow": "Undelete",
        "dxDataGrid-editingConfirmDeleteMessage": "Are you sure you want to delete this record?",
        "dxDataGrid-validationCancelChanges": "Cancel changes",
        "dxDataGrid-groupPanelEmptyText": "Drag a column header here to group by that column",
        "dxDataGrid-noDataText": "No data",
        "dxDataGrid-searchPanelPlaceholder": "Search...",
        "dxDataGrid-filterRowShowAllText": "(All)",
        "dxDataGrid-filterRowResetOperationText": "Reset",
        "dxDataGrid-filterRowOperationEquals": "Equals",
        "dxDataGrid-filterRowOperationNotEquals": "Does not equal",
        "dxDataGrid-filterRowOperationLess": "Less than",
        "dxDataGrid-filterRowOperationLessOrEquals": "Less than or equal to",
        "dxDataGrid-filterRowOperationGreater": "Greater than",
        "dxDataGrid-filterRowOperationGreaterOrEquals": "Greater than or equal to",
        "dxDataGrid-filterRowOperationStartsWith": "Starts with",
        "dxDataGrid-filterRowOperationContains": "Contains",
        "dxDataGrid-filterRowOperationNotContains": "Does not contain",
        "dxDataGrid-filterRowOperationEndsWith": "Ends with",
        "dxDataGrid-filterRowOperationBetween": "Between",
        "dxDataGrid-filterRowOperationBetweenStartText": "Start",
        "dxDataGrid-filterRowOperationBetweenEndText": "End",
        "dxDataGrid-ariaSearchBox": "Search box",
        "dxDataGrid-applyFilterText": "Apply filter",
        "dxDataGrid-trueText": "true",
        "dxDataGrid-falseText": "false",
        "dxDataGrid-sortingAscendingText": "Sort Ascending",
        "dxDataGrid-sortingDescendingText": "Sort Descending",
        "dxDataGrid-sortingClearText": "Clear Sorting",
        "dxDataGrid-ariaNotSortedColumn": "Not sorted column",
        "dxDataGrid-ariaSortedAscendingColumn": "Column sorted in ascending order",
        "dxDataGrid-ariaSortedDescendingColumn": "Column sorted in descending order",
        "dxDataGrid-ariaSortIndex": "Sort index {0}",
        "dxDataGrid-editingSaveAllChanges": "Save changes",
        "dxDataGrid-editingCancelAllChanges": "Discard changes",
        "dxDataGrid-editingAddRow": "Add a row",
        "dxDataGrid-summaryMin": "Min: {0}",
        "dxDataGrid-summaryMinOtherColumn": "Min of {1} is {0}",
        "dxDataGrid-summaryMax": "Max: {0}",
        "dxDataGrid-summaryMaxOtherColumn": "Max of {1} is {0}",
        "dxDataGrid-summaryAvg": "Avg: {0}",
        "dxDataGrid-summaryAvgOtherColumn": "Avg of {1} is {0}",
        "dxDataGrid-summarySum": "Sum: {0}",
        "dxDataGrid-summarySumOtherColumn": "Sum of {1} is {0}",
        "dxDataGrid-summaryCount": "Count: {0}",
        "dxDataGrid-columnFixingFix": "Set Fixed Position",
        "dxDataGrid-columnFixingUnfix": "Unfix",
        "dxDataGrid-columnFixingLeftPosition": "Left",
        "dxDataGrid-columnFixingRightPosition": "Right",
        "dxDataGrid-columnFixingStickyPosition": "Sticky",
        "dxDataGrid-exportTo": "Export",
        "dxDataGrid-exportToExcel": "Export to Excel file",
        "dxDataGrid-exporting": "Exporting...",
        "dxDataGrid-excelFormat": "Excel file",
        "dxDataGrid-selectedRows": "Selected rows",
        "dxDataGrid-exportSelectedRows": "Export selected rows to {0}",
        "dxDataGrid-exportAll": "Export all data to {0}",
        "dxDataGrid-headerFilterLabel": "Filter options",
        "dxDataGrid-headerFilterIndicatorLabel": "Show filter options for column '{0}'",
        "dxDataGrid-headerFilterEmptyValue": "(Blanks)",
        "dxDataGrid-headerFilterOK": "OK",
        "dxDataGrid-headerFilterCancel": "Cancel",
        "dxDataGrid-ariaAdaptiveCollapse": "Hide additional data",
        "dxDataGrid-ariaAdaptiveExpand": "Display additional data",
        "dxDataGrid-ariaColumn": "Column",
        "dxDataGrid-ariaColumnHeader": "Column header",
        "dxDataGrid-ariaValue": "Value",
        "dxDataGrid-ariaError": "Error",
        "dxDataGrid-ariaRevertButton": "Press Escape to discard the changes",
        "dxDataGrid-ariaFilterCell": "Filter cell",
        "dxDataGrid-ariaCollapse": "Collapse",
        "dxDataGrid-ariaModifiedCell": "Modified",
        "dxDataGrid-ariaDeletedCell": "Deleted",
        "dxDataGrid-ariaEditableCell": "Editable",
        "dxDataGrid-ariaExpand": "Expand",
        "dxDataGrid-ariaCollapsedRow": "Collapsed row",
        "dxDataGrid-ariaExpandedRow": "Expanded row",
        "dxDataGrid-ariaDataGrid": "Data grid with {0} rows and {1} columns",
        "dxDataGrid-ariaSearchInGrid": "Search in the data grid",
        "dxDataGrid-ariaSelectAll": "Select all",
        "dxDataGrid-ariaSelectRow": "Select row",
        "dxDataGrid-ariaToolbar": "Data grid toolbar",
        "dxDataGrid-ariaEditForm": "Edit form",
        "dxDataGrid-filterBuilderPopupTitle": "Filter Builder",
        "dxDataGrid-filterPanelCreateFilter": "Create Filter",
        "dxDataGrid-filterPanelClearFilter": "Clear",
        "dxDataGrid-filterPanelFilterEnabledHint": "Enable the filter",
        "dxDataGrid-masterDetail": "Cell with details",
        "dxDataGrid-moveColumnToTheRight": "Move to the right",
        "dxDataGrid-moveColumnToTheLeft": "Move to the left",
        "dxTreeList-ariaTreeList": "Tree list with {0} rows and {1} columns",
        "dxTreeList-ariaExpandableInstruction": "Press Ctrl + right arrow to expand the focused node and Ctrl + left arrow to collapse it",
        "dxTreeList-ariaSearchInGrid": "Search in the tree list",
        "dxTreeList-ariaToolbar": "Tree list toolbar",
        "dxTreeList-editingAddRowToNode": "Add",
        "dxPager-infoText": "Page {0} of {1} ({2} items)",
        "dxPager-pagesCountText": "of",
        "dxPager-pageSize": "Items per page: {0}",
        "dxPager-pageSizesAllText": "All",
        "dxPager-page": "Page {0}",
        "dxPager-prevPage": "Previous page",
        "dxPager-nextPage": "Next page",
        "dxPager-ariaLabel": "Page navigation",
        "dxPager-ariaPageSize": "Page size",
        "dxPager-ariaPageNumber": "Page number",
        "dxPagination-infoText": "Page {0} of {1} ({2} items)",
        "dxPagination-pagesCountText": "of",
        "dxPagination-pageSize": "Items per page: {0}",
        "dxPagination-pageSizesAllText": "All",
        "dxPagination-page": "Page {0}",
        "dxPagination-prevPage": "Previous page",
        "dxPagination-nextPage": "Next page",
        "dxPagination-ariaLabel": "Page navigation",
        "dxPagination-ariaPageSize": "Page size",
        "dxPagination-ariaPageNumber": "Page number",
        "dxPivotGrid-grandTotal": "Grand Total",
        "dxPivotGrid-total": "{0} Total",
        "dxPivotGrid-fieldChooserTitle": "Field Chooser",
        "dxPivotGrid-showFieldChooser": "Show Field Chooser",
        "dxPivotGrid-expandAll": "Expand All",
        "dxPivotGrid-collapseAll": "Collapse All",
        "dxPivotGrid-sortColumnBySummary": 'Sort "{0}" by This Column',
        "dxPivotGrid-sortRowBySummary": 'Sort "{0}" by This Row',
        "dxPivotGrid-removeAllSorting": "Remove All Sorting",
        "dxPivotGrid-dataNotAvailable": "N/A",
        "dxPivotGrid-rowFields": "Row Fields",
        "dxPivotGrid-columnFields": "Column Fields",
        "dxPivotGrid-dataFields": "Data Fields",
        "dxPivotGrid-filterFields": "Filter Fields",
        "dxPivotGrid-allFields": "All Fields",
        "dxPivotGrid-columnFieldArea": "Drop Column Fields Here",
        "dxPivotGrid-dataFieldArea": "Drop Data Fields Here",
        "dxPivotGrid-rowFieldArea": "Drop Row Fields Here",
        "dxPivotGrid-filterFieldArea": "Drop Filter Fields Here",
        "dxScheduler-dateRange": "from {0} to {1}",
        "dxScheduler-ariaLabel": "Scheduler. {0} view: {1} with {2} appointments",
        "dxScheduler-ariaLabel-currentIndicator-present": "The current time indicator is visible in the view",
        "dxScheduler-ariaLabel-currentIndicator-not-present": "The current time indicator is not visible on the screen",
        "dxScheduler-appointmentAriaLabel-group": "Group: {0}",
        "dxScheduler-appointmentAriaLabel-recurring": "Recurring appointment",
        "dxScheduler-appointmentListAriaLabel": "Appointment list",
        "dxScheduler-editorLabelTitle": "Subject",
        "dxScheduler-editorLabelStartDate": "Start Date",
        "dxScheduler-editorLabelEndDate": "End Date",
        "dxScheduler-editorLabelDescription": "Description",
        "dxScheduler-editorLabelRecurrence": "Repeat",
        "dxScheduler-navigationToday": "Today",
        "dxScheduler-navigationPrevious": "Previous page",
        "dxScheduler-navigationNext": "Next page",
        "dxScheduler-openAppointment": "Open appointment",
        "dxScheduler-recurrenceNever": "Never",
        "dxScheduler-recurrenceMinutely": "Every minute",
        "dxScheduler-recurrenceHourly": "Hourly",
        "dxScheduler-recurrenceDaily": "Daily",
        "dxScheduler-recurrenceWeekly": "Weekly",
        "dxScheduler-recurrenceMonthly": "Monthly",
        "dxScheduler-recurrenceYearly": "Yearly",
        "dxScheduler-recurrenceRepeatEvery": "Repeat Every",
        "dxScheduler-recurrenceRepeatOn": "Repeat On",
        "dxScheduler-recurrenceEnd": "End repeat",
        "dxScheduler-recurrenceAfter": "After",
        "dxScheduler-recurrenceOn": "On",
        "dxScheduler-recurrenceUntilDateLabel": "Date when repeat ends",
        "dxScheduler-recurrenceOccurrenceLabel": "Number of occurrences",
        "dxScheduler-recurrenceRepeatMinutely": "minute(s)",
        "dxScheduler-recurrenceRepeatHourly": "hour(s)",
        "dxScheduler-recurrenceRepeatDaily": "day(s)",
        "dxScheduler-recurrenceRepeatWeekly": "week(s)",
        "dxScheduler-recurrenceRepeatMonthly": "month(s)",
        "dxScheduler-recurrenceRepeatYearly": "year(s)",
        "dxScheduler-switcherDay": "Day",
        "dxScheduler-switcherWeek": "Week",
        "dxScheduler-switcherWorkWeek": "Work Week",
        "dxScheduler-switcherMonth": "Month",
        "dxScheduler-switcherAgenda": "Agenda",
        "dxScheduler-switcherTimelineDay": "Timeline Day",
        "dxScheduler-switcherTimelineWeek": "Timeline Week",
        "dxScheduler-switcherTimelineWorkWeek": "Timeline Work Week",
        "dxScheduler-switcherTimelineMonth": "Timeline Month",
        "dxScheduler-recurrenceRepeatOnDate": "on date",
        "dxScheduler-recurrenceRepeatCount": "occurrence(s)",
        "dxScheduler-allDay": "All day",
        "dxScheduler-ariaEditForm": "Edit form",
        "dxScheduler-confirmRecurrenceEditTitle": "Edit Recurring Appointment",
        "dxScheduler-confirmRecurrenceDeleteTitle": "Delete Recurring Appointment",
        "dxScheduler-confirmRecurrenceEditMessage": "Do you want to edit only this appointment or the whole series?",
        "dxScheduler-confirmRecurrenceDeleteMessage": "Do you want to delete only this appointment or the whole series?",
        "dxScheduler-confirmRecurrenceEditSeries": "Edit series",
        "dxScheduler-confirmRecurrenceDeleteSeries": "Delete series",
        "dxScheduler-confirmRecurrenceEditOccurrence": "Edit appointment",
        "dxScheduler-confirmRecurrenceDeleteOccurrence": "Delete appointment",
        "dxScheduler-noTimezoneTitle": "No timezone",
        "dxScheduler-moreAppointments": "{0} more",
        "dxCalendar-currentDay": "Today",
        "dxCalendar-currentMonth": "Current month",
        "dxCalendar-currentYear": "Current year",
        "dxCalendar-currentYearRange": "Current year range",
        "dxCalendar-todayButtonText": "Today",
        "dxCalendar-ariaWidgetName": "Calendar",
        "dxCalendar-previousMonthButtonLabel": "Previous month",
        "dxCalendar-previousYearButtonLabel": "Previous year",
        "dxCalendar-previousDecadeButtonLabel": "Previous decade",
        "dxCalendar-previousCenturyButtonLabel": "Previous century",
        "dxCalendar-nextMonthButtonLabel": "Next month",
        "dxCalendar-nextYearButtonLabel": "Next year",
        "dxCalendar-nextDecadeButtonLabel": "Next decade",
        "dxCalendar-nextCenturyButtonLabel": "Next century",
        "dxCalendar-captionMonthLabel": "Month selection",
        "dxCalendar-captionYearLabel": "Year selection",
        "dxCalendar-captionDecadeLabel": "Decade selection",
        "dxCalendar-captionCenturyLabel": "Century selection",
        "dxCalendar-selectedDate": "The selected date is {0}",
        "dxCalendar-selectedDates": "The selected dates",
        "dxCalendar-selectedDateRange": "The selected date range is from {0} to {1}",
        "dxCalendar-selectedMultipleDateRange": "from {0} to {1}",
        "dxCalendar-selectedDateRangeCount": "There are {0} selected date ranges",
        "dxCalendar-readOnlyLabel": "Read-only calendar",
        "dxCardView-ariaSearchInGrid": "Search in the card view",
        "dxCardView-ariaHeaderItemLabel": "Field name {0}",
        "dxCardView-ariaHeaderItemSortingAscendingLabel": "Sorted in ascending order",
        "dxCardView-ariaHeaderItemSortingDescendingLabel": "Sorted in descending order",
        "dxCardView-ariaHeaderItemSortingIndexLabel": "Sort index {0}",
        "dxCardView-ariaHeaderHasHeaderFilterLabel": "Header filter applied",
        "dxCardView-ariaSelectCard": "Select card",
        "dxCardView-ariaCardView": "Card view with {0} cards. Each card has {1} fields",
        "dxCardView-ariaCard": "Card",
        "dxCardView-ariaEditableCard": "Editable card",
        "dxCardView-ariaCardPosition": "Row {0}, column {1}",
        "dxCardView-ariaSelectedCardState": "Selected",
        "dxCardView-ariaNotSelectedCardState": "Not selected",
        "dxCardView-selectAll": "Select all",
        "dxCardView-clearSelection": "Clear selection",
        "dxCardView-cardNoImageAriaLabel": "No image",
        "dxCardView-headerItemDropZoneText": "Drop the header item here",
        "dxCardView-emptyHeaderPanelText": "Use {0} to display columns",
        "dxCardView-emptyHeaderPanelColumnChooserText": "column chooser",
        "dxAvatar-defaultImageAlt": "Avatar",
        "dxChat-elementAriaLabel": "Chat",
        "dxChat-textareaPlaceholder": "Type a message",
        "dxChat-sendButtonAriaLabel": "Send",
        "dxChat-cancelEditingButtonAriaLabel": "Cancel",
        "dxChat-editingMessageCaption": "Edit Message",
        "dxChat-defaultUserName": "Unknown User",
        "dxChat-messageListAriaLabel": "Message list",
        "dxChat-alertListAriaLabel": "Error list",
        "dxChat-emptyListMessage": "There are no messages in this chat",
        "dxChat-emptyListPrompt": "Write your first message",
        "dxChat-typingMessageSingleUser": "{0} is typing...",
        "dxChat-typingMessageTwoUsers": "{0} and {1} are typing...",
        "dxChat-typingMessageThreeUsers": "{0}, {1} and {2} are typing...",
        "dxChat-typingMessageMultipleUsers": "{0} and others are typing...",
        "dxChat-editedMessageText": "Edited",
        "dxChat-editingEditMessage": "Edit",
        "dxChat-editingDeleteMessage": "Delete",
        "dxChat-editingDeleteConfirmText": "Are you sure you want to delete this message?",
        "dxChat-deletedMessageText": "This message was deleted",
        "dxChat-defaultImageAlt": "Image shared in chat",
        "dxColorView-ariaRed": "Red",
        "dxColorView-ariaGreen": "Green",
        "dxColorView-ariaBlue": "Blue",
        "dxColorView-ariaAlpha": "Transparency",
        "dxColorView-ariaHex": "Color code",
        "dxTagBox-selected": "{0} selected",
        "dxTagBox-allSelected": "All selected ({0})",
        "dxTagBox-moreSelected": "{0} more",
        "dxTagBox-tagRoleDescription": "Tag. Press the delete button to remove this tag",
        "dxTagBox-ariaRoleDescription": "Tag box",
        "vizExport-printingButtonText": "Print",
        "vizExport-titleMenuText": "Exporting/Printing",
        "vizExport-exportButtonText": "{0} file",
        "dxFilterBuilder-and": "And",
        "dxFilterBuilder-or": "Or",
        "dxFilterBuilder-notAnd": "Not And",
        "dxFilterBuilder-notOr": "Not Or",
        "dxFilterBuilder-addCondition": "Add Condition",
        "dxFilterBuilder-addGroup": "Add Group",
        "dxFilterBuilder-enterValueText": "<enter a value>",
        "dxFilterBuilder-filterOperationEquals": "Equals",
        "dxFilterBuilder-filterOperationNotEquals": "Does not equal",
        "dxFilterBuilder-filterOperationLess": "Is less than",
        "dxFilterBuilder-filterOperationLessOrEquals": "Is less than or equal to",
        "dxFilterBuilder-filterOperationGreater": "Is greater than",
        "dxFilterBuilder-filterOperationGreaterOrEquals": "Is greater than or equal to",
        "dxFilterBuilder-filterOperationStartsWith": "Starts with",
        "dxFilterBuilder-filterOperationContains": "Contains",
        "dxFilterBuilder-filterOperationNotContains": "Does not contain",
        "dxFilterBuilder-filterOperationEndsWith": "Ends with",
        "dxFilterBuilder-filterOperationIsBlank": "Is blank",
        "dxFilterBuilder-filterOperationIsNotBlank": "Is not blank",
        "dxFilterBuilder-filterOperationBetween": "Is between",
        "dxFilterBuilder-filterOperationAnyOf": "Is any of",
        "dxFilterBuilder-filterOperationNoneOf": "Is none of",
        "dxFilterBuilder-filterAriaRootElement": "Filter builder",
        "dxFilterBuilder-filterAriaGroupLevel": "Level {0}",
        "dxFilterBuilder-filterAriaGroupItem": "Group item",
        "dxFilterBuilder-filterAriaOperationButton": "Operation",
        "dxFilterBuilder-filterAriaAddButton": "Add",
        "dxFilterBuilder-filterAriaRemoveButton": "Remove {0}",
        "dxFilterBuilder-filterAriaItemField": "Item field",
        "dxFilterBuilder-filterAriaItemOperation": "Item operation",
        "dxFilterBuilder-filterAriaItemValue": "Item value",
        "dxHtmlEditor-dialogColorCaption": "Change Font Color",
        "dxHtmlEditor-dialogBackgroundCaption": "Change Background Color",
        "dxHtmlEditor-dialogLinkCaption": "Add Link",
        "dxHtmlEditor-dialogLinkUrlField": "URL",
        "dxHtmlEditor-dialogLinkTextField": "Text",
        "dxHtmlEditor-dialogLinkTargetField": "Open link in new window",
        "dxHtmlEditor-dialogImageCaption": "Add Image",
        "dxHtmlEditor-dialogImageUrlField": "URL",
        "dxHtmlEditor-dialogImageAltField": "Alternate text",
        "dxHtmlEditor-dialogImageWidthField": "Width (px)",
        "dxHtmlEditor-dialogImageHeightField": "Height (px)",
        "dxHtmlEditor-dialogInsertTableRowsField": "Rows",
        "dxHtmlEditor-dialogInsertTableColumnsField": "Columns",
        "dxHtmlEditor-dialogInsertTableCaption": "Insert Table",
        "dxHtmlEditor-dialogUpdateImageCaption": "Update Image",
        "dxHtmlEditor-dialogImageUpdateButton": "Update",
        "dxHtmlEditor-dialogImageAddButton": "Add",
        "dxHtmlEditor-dialogImageSpecifyUrl": "From the Web",
        "dxHtmlEditor-dialogImageSelectFile": "From This Device",
        "dxHtmlEditor-dialogImageKeepAspectRatio": "Keep Aspect Ratio",
        "dxHtmlEditor-dialogImageEncodeToBase64": "Encode to Base64",
        "dxHtmlEditor-heading": "Heading",
        "dxHtmlEditor-normalText": "Normal text",
        "dxHtmlEditor-background": "Background Color",
        "dxHtmlEditor-bold": "Bold",
        "dxHtmlEditor-color": "Font Color",
        "dxHtmlEditor-font": "Font",
        "dxHtmlEditor-italic": "Italic",
        "dxHtmlEditor-link": "Add Link",
        "dxHtmlEditor-image": "Add Image",
        "dxHtmlEditor-size": "Size",
        "dxHtmlEditor-strike": "Strikethrough",
        "dxHtmlEditor-subscript": "Subscript",
        "dxHtmlEditor-superscript": "Superscript",
        "dxHtmlEditor-underline": "Underline",
        "dxHtmlEditor-blockquote": "Blockquote",
        "dxHtmlEditor-header": "Header",
        "dxHtmlEditor-increaseIndent": "Increase Indent",
        "dxHtmlEditor-decreaseIndent": "Decrease Indent",
        "dxHtmlEditor-orderedList": "Ordered List",
        "dxHtmlEditor-bulletList": "Bullet List",
        "dxHtmlEditor-alignLeft": "Align Left",
        "dxHtmlEditor-alignCenter": "Align Center",
        "dxHtmlEditor-alignRight": "Align Right",
        "dxHtmlEditor-alignJustify": "Align Justify",
        "dxHtmlEditor-codeBlock": "Code Block",
        "dxHtmlEditor-variable": "Add Variable",
        "dxHtmlEditor-undo": "Undo",
        "dxHtmlEditor-redo": "Redo",
        "dxHtmlEditor-clear": "Clear Formatting",
        "dxHtmlEditor-insertTable": "Insert Table",
        "dxHtmlEditor-insertHeaderRow": "Insert Header Row",
        "dxHtmlEditor-insertRowAbove": "Insert Row Above",
        "dxHtmlEditor-insertRowBelow": "Insert Row Below",
        "dxHtmlEditor-insertColumnLeft": "Insert Column Left",
        "dxHtmlEditor-insertColumnRight": "Insert Column Right",
        "dxHtmlEditor-deleteColumn": "Delete Column",
        "dxHtmlEditor-deleteRow": "Delete Row",
        "dxHtmlEditor-deleteTable": "Delete Table",
        "dxHtmlEditor-cellProperties": "Cell Properties",
        "dxHtmlEditor-tableProperties": "Table Properties",
        "dxHtmlEditor-insert": "Insert",
        "dxHtmlEditor-delete": "Delete",
        "dxHtmlEditor-border": "Border",
        "dxHtmlEditor-style": "Style",
        "dxHtmlEditor-width": "Width",
        "dxHtmlEditor-height": "Height",
        "dxHtmlEditor-borderColor": "Color",
        "dxHtmlEditor-borderWidth": "Border Width",
        "dxHtmlEditor-tableBackground": "Background",
        "dxHtmlEditor-dimensions": "Dimensions",
        "dxHtmlEditor-alignment": "Alignment",
        "dxHtmlEditor-horizontal": "Horizontal",
        "dxHtmlEditor-vertical": "Vertical",
        "dxHtmlEditor-paddingVertical": "Vertical Padding",
        "dxHtmlEditor-paddingHorizontal": "Horizontal Padding",
        "dxHtmlEditor-pixels": "Pixels",
        "dxHtmlEditor-list": "List",
        "dxHtmlEditor-ordered": "Ordered",
        "dxHtmlEditor-bullet": "Bullet",
        "dxHtmlEditor-align": "Align",
        "dxHtmlEditor-center": "Center",
        "dxHtmlEditor-left": "Left",
        "dxHtmlEditor-right": "Right",
        "dxHtmlEditor-indent": "Indent",
        "dxHtmlEditor-justify": "Justify",
        "dxHtmlEditor-borderStyleNone": "none",
        "dxHtmlEditor-borderStyleHidden": "hidden",
        "dxHtmlEditor-borderStyleDotted": "dotted",
        "dxHtmlEditor-borderStyleDashed": "dashed",
        "dxHtmlEditor-borderStyleSolid": "solid",
        "dxHtmlEditor-borderStyleDouble": "double",
        "dxHtmlEditor-borderStyleGroove": "groove",
        "dxHtmlEditor-borderStyleRidge": "ridge",
        "dxHtmlEditor-borderStyleInset": "inset",
        "dxHtmlEditor-borderStyleOutset": "outset",
        "dxHtmlEditor-aiDialogTitle": "AI Assistant",
        "dxHtmlEditor-aiDialogError": "Something went wrong. Please try again.",
        "dxHtmlEditor-aiDialogCanceled": "Generation canceled",
        "dxHtmlEditor-aiReplace": "Replace",
        "dxHtmlEditor-aiInsertAbove": "Insert above",
        "dxHtmlEditor-aiInsertBelow": "Insert below",
        "dxHtmlEditor-aiCopy": "Copy",
        "dxHtmlEditor-aiRegenerate": "Regenerate",
        "dxHtmlEditor-aiGenerate": "Generate",
        "dxHtmlEditor-aiCancel": "Cancel",
        "dxHtmlEditor-aiToolbarItemAriaLabel": "AI Assistant toolbar item",
        "dxHtmlEditor-aiResultTextAreaAriaLabel": "AI Assistant result",
        "dxHtmlEditor-aiAskPlaceholder": "Ask AI to modify text",
        "dxFileManager-newDirectoryName": "Untitled directory",
        "dxFileManager-rootDirectoryName": "Files",
        "dxFileManager-errorNoAccess": "Access Denied. Operation could not be completed.",
        "dxFileManager-errorDirectoryExistsFormat": "Directory '{0}' already exists.",
        "dxFileManager-errorFileExistsFormat": "File '{0}' already exists.",
        "dxFileManager-errorFileNotFoundFormat": "File '{0}' not found.",
        "dxFileManager-errorDirectoryNotFoundFormat": "Directory '{0}' not found.",
        "dxFileManager-errorWrongFileExtension": "File extension is not allowed.",
        "dxFileManager-errorMaxFileSizeExceeded": "File size exceeds the maximum allowed size.",
        "dxFileManager-errorInvalidSymbols": "This name contains invalid characters.",
        "dxFileManager-errorDefault": "Unspecified error.",
        "dxFileManager-errorDirectoryOpenFailed": "The directory cannot be opened",
        "dxFileManager-commandCreate": "New directory",
        "dxFileManager-commandRename": "Rename",
        "dxFileManager-commandMove": "Move to",
        "dxFileManager-commandCopy": "Copy to",
        "dxFileManager-commandDelete": "Delete",
        "dxFileManager-commandDownload": "Download",
        "dxFileManager-commandUpload": "Upload files",
        "dxFileManager-commandRefresh": "Refresh",
        "dxFileManager-commandThumbnails": "Thumbnails View",
        "dxFileManager-commandDetails": "Details View",
        "dxFileManager-commandClearSelection": "Clear selection",
        "dxFileManager-commandShowNavPane": "Toggle navigation pane",
        "dxFileManager-dialogDirectoryChooserMoveTitle": "Move to",
        "dxFileManager-dialogDirectoryChooserMoveButtonText": "Move",
        "dxFileManager-dialogDirectoryChooserCopyTitle": "Copy to",
        "dxFileManager-dialogDirectoryChooserCopyButtonText": "Copy",
        "dxFileManager-dialogRenameItemTitle": "Rename",
        "dxFileManager-dialogRenameItemButtonText": "Save",
        "dxFileManager-dialogCreateDirectoryTitle": "New directory",
        "dxFileManager-dialogCreateDirectoryButtonText": "Create",
        "dxFileManager-dialogDeleteItemTitle": "Delete",
        "dxFileManager-dialogDeleteItemButtonText": "Delete",
        "dxFileManager-dialogDeleteItemSingleItemConfirmation": "Are you sure you want to delete {0}?",
        "dxFileManager-dialogDeleteItemMultipleItemsConfirmation": "Are you sure you want to delete {0} items?",
        "dxFileManager-dialogButtonCancel": "Cancel",
        "dxFileManager-editingCreateSingleItemProcessingMessage": "Creating a directory inside {0}",
        "dxFileManager-editingCreateSingleItemSuccessMessage": "Created a directory inside {0}",
        "dxFileManager-editingCreateSingleItemErrorMessage": "Directory was not created",
        "dxFileManager-editingCreateCommonErrorMessage": "Directory was not created",
        "dxFileManager-editingRenameSingleItemProcessingMessage": "Renaming an item inside {0}",
        "dxFileManager-editingRenameSingleItemSuccessMessage": "Renamed an item inside {0}",
        "dxFileManager-editingRenameSingleItemErrorMessage": "Item was not renamed",
        "dxFileManager-editingRenameCommonErrorMessage": "Item was not renamed",
        "dxFileManager-editingDeleteSingleItemProcessingMessage": "Deleting an item from {0}",
        "dxFileManager-editingDeleteMultipleItemsProcessingMessage": "Deleting {0} items from {1}",
        "dxFileManager-editingDeleteSingleItemSuccessMessage": "Deleted an item from {0}",
        "dxFileManager-editingDeleteMultipleItemsSuccessMessage": "Deleted {0} items from {1}",
        "dxFileManager-editingDeleteSingleItemErrorMessage": "Item was not deleted",
        "dxFileManager-editingDeleteMultipleItemsErrorMessage": "{0} items were not deleted",
        "dxFileManager-editingDeleteCommonErrorMessage": "Some items were not deleted",
        "dxFileManager-editingMoveSingleItemProcessingMessage": "Moving an item to {0}",
        "dxFileManager-editingMoveMultipleItemsProcessingMessage": "Moving {0} items to {1}",
        "dxFileManager-editingMoveSingleItemSuccessMessage": "Moved an item to {0}",
        "dxFileManager-editingMoveMultipleItemsSuccessMessage": "Moved {0} items to {1}",
        "dxFileManager-editingMoveSingleItemErrorMessage": "Item was not moved",
        "dxFileManager-editingMoveMultipleItemsErrorMessage": "{0} items were not moved",
        "dxFileManager-editingMoveCommonErrorMessage": "Some items were not moved",
        "dxFileManager-editingCopySingleItemProcessingMessage": "Copying an item to {0}",
        "dxFileManager-editingCopyMultipleItemsProcessingMessage": "Copying {0} items to {1}",
        "dxFileManager-editingCopySingleItemSuccessMessage": "Copied an item to {0}",
        "dxFileManager-editingCopyMultipleItemsSuccessMessage": "Copied {0} items to {1}",
        "dxFileManager-editingCopySingleItemErrorMessage": "Item was not copied",
        "dxFileManager-editingCopyMultipleItemsErrorMessage": "{0} items were not copied",
        "dxFileManager-editingCopyCommonErrorMessage": "Some items were not copied",
        "dxFileManager-editingUploadSingleItemProcessingMessage": "Uploading an item to {0}",
        "dxFileManager-editingUploadMultipleItemsProcessingMessage": "Uploading {0} items to {1}",
        "dxFileManager-editingUploadSingleItemSuccessMessage": "Uploaded an item to {0}",
        "dxFileManager-editingUploadMultipleItemsSuccessMessage": "Uploaded {0} items to {1}",
        "dxFileManager-editingUploadSingleItemErrorMessage": "Item was not uploaded",
        "dxFileManager-editingUploadMultipleItemsErrorMessage": "{0} items were not uploaded",
        "dxFileManager-editingUploadCanceledMessage": "Canceled",
        "dxFileManager-editingDownloadSingleItemErrorMessage": "Item was not downloaded",
        "dxFileManager-editingDownloadMultipleItemsErrorMessage": "{0} items were not downloaded",
        "dxFileManager-listDetailsColumnCaptionName": "Name",
        "dxFileManager-listDetailsColumnCaptionDateModified": "Date Modified",
        "dxFileManager-listDetailsColumnCaptionFileSize": "File Size",
        "dxFileManager-listThumbnailsTooltipTextSize": "Size",
        "dxFileManager-listThumbnailsTooltipTextDateModified": "Date Modified",
        "dxFileManager-notificationProgressPanelTitle": "Progress",
        "dxFileManager-notificationProgressPanelEmptyListText": "No operations",
        "dxFileManager-notificationProgressPanelOperationCanceled": "Canceled",
        "dxDiagram-categoryGeneral": "General",
        "dxDiagram-categoryFlowchart": "Flowchart",
        "dxDiagram-categoryOrgChart": "Org Chart",
        "dxDiagram-categoryContainers": "Containers",
        "dxDiagram-categoryCustom": "Custom",
        "dxDiagram-commandExportToSvg": "Export to SVG",
        "dxDiagram-commandExportToPng": "Export to PNG",
        "dxDiagram-commandExportToJpg": "Export to JPEG",
        "dxDiagram-commandUndo": "Undo",
        "dxDiagram-commandRedo": "Redo",
        "dxDiagram-commandFontName": "Font Name",
        "dxDiagram-commandFontSize": "Font Size",
        "dxDiagram-commandBold": "Bold",
        "dxDiagram-commandItalic": "Italic",
        "dxDiagram-commandUnderline": "Underline",
        "dxDiagram-commandTextColor": "Font Color",
        "dxDiagram-commandLineColor": "Line Color",
        "dxDiagram-commandLineWidth": "Line Width",
        "dxDiagram-commandLineStyle": "Line Style",
        "dxDiagram-commandLineStyleSolid": "Solid",
        "dxDiagram-commandLineStyleDotted": "Dotted",
        "dxDiagram-commandLineStyleDashed": "Dashed",
        "dxDiagram-commandFillColor": "Fill Color",
        "dxDiagram-commandAlignLeft": "Align Left",
        "dxDiagram-commandAlignCenter": "Align Center",
        "dxDiagram-commandAlignRight": "Align Right",
        "dxDiagram-commandConnectorLineType": "Connector Line Type",
        "dxDiagram-commandConnectorLineStraight": "Straight",
        "dxDiagram-commandConnectorLineOrthogonal": "Orthogonal",
        "dxDiagram-commandConnectorLineStart": "Connector Line Start",
        "dxDiagram-commandConnectorLineEnd": "Connector Line End",
        "dxDiagram-commandConnectorLineNone": "None",
        "dxDiagram-commandConnectorLineArrow": "Arrow",
        "dxDiagram-commandFullscreen": "Full Screen",
        "dxDiagram-commandUnits": "Units",
        "dxDiagram-commandPageSize": "Page Size",
        "dxDiagram-commandPageOrientation": "Page Orientation",
        "dxDiagram-commandPageOrientationLandscape": "Landscape",
        "dxDiagram-commandPageOrientationPortrait": "Portrait",
        "dxDiagram-commandPageColor": "Page Color",
        "dxDiagram-commandShowGrid": "Show Grid",
        "dxDiagram-commandSnapToGrid": "Snap to Grid",
        "dxDiagram-commandGridSize": "Grid Size",
        "dxDiagram-commandZoomLevel": "Zoom Level",
        "dxDiagram-commandAutoZoom": "Auto Zoom",
        "dxDiagram-commandFitToContent": "Fit to Content",
        "dxDiagram-commandFitToWidth": "Fit to Width",
        "dxDiagram-commandAutoZoomByContent": "Auto Zoom by Content",
        "dxDiagram-commandAutoZoomByWidth": "Auto Zoom by Width",
        "dxDiagram-commandSimpleView": "Simple View",
        "dxDiagram-commandCut": "Cut",
        "dxDiagram-commandCopy": "Copy",
        "dxDiagram-commandPaste": "Paste",
        "dxDiagram-commandSelectAll": "Select All",
        "dxDiagram-commandDelete": "Delete",
        "dxDiagram-commandBringToFront": "Bring to Front",
        "dxDiagram-commandSendToBack": "Send to Back",
        "dxDiagram-commandLock": "Lock",
        "dxDiagram-commandUnlock": "Unlock",
        "dxDiagram-commandInsertShapeImage": "Insert Image...",
        "dxDiagram-commandEditShapeImage": "Change Image...",
        "dxDiagram-commandDeleteShapeImage": "Delete Image",
        "dxDiagram-commandLayoutLeftToRight": "Left-to-right",
        "dxDiagram-commandLayoutRightToLeft": "Right-to-left",
        "dxDiagram-commandLayoutTopToBottom": "Top-to-bottom",
        "dxDiagram-commandLayoutBottomToTop": "Bottom-to-top",
        "dxDiagram-unitIn": "in",
        "dxDiagram-unitCm": "cm",
        "dxDiagram-unitPx": "px",
        "dxDiagram-dialogButtonOK": "OK",
        "dxDiagram-dialogButtonCancel": "Cancel",
        "dxDiagram-dialogInsertShapeImageTitle": "Insert Image",
        "dxDiagram-dialogEditShapeImageTitle": "Change Image",
        "dxDiagram-dialogEditShapeImageSelectButton": "Select image",
        "dxDiagram-dialogEditShapeImageLabelText": "or drop a file here",
        "dxDiagram-uiExport": "Export",
        "dxDiagram-uiProperties": "Properties",
        "dxDiagram-uiSettings": "Settings",
        "dxDiagram-uiShowToolbox": "Show Toolbox",
        "dxDiagram-uiSearch": "Search",
        "dxDiagram-uiStyle": "Style",
        "dxDiagram-uiLayout": "Layout",
        "dxDiagram-uiLayoutTree": "Tree",
        "dxDiagram-uiLayoutLayered": "Layered",
        "dxDiagram-uiDiagram": "Diagram",
        "dxDiagram-uiText": "Text",
        "dxDiagram-uiObject": "Object",
        "dxDiagram-uiConnector": "Connector",
        "dxDiagram-uiPage": "Page",
        "dxDiagram-shapeText": "Text",
        "dxDiagram-shapeRectangle": "Rectangle",
        "dxDiagram-shapeEllipse": "Ellipse",
        "dxDiagram-shapeCross": "Cross",
        "dxDiagram-shapeTriangle": "Triangle",
        "dxDiagram-shapeDiamond": "Diamond",
        "dxDiagram-shapeHeart": "Heart",
        "dxDiagram-shapePentagon": "Pentagon",
        "dxDiagram-shapeHexagon": "Hexagon",
        "dxDiagram-shapeOctagon": "Octagon",
        "dxDiagram-shapeStar": "Star",
        "dxDiagram-shapeArrowLeft": "Left Arrow",
        "dxDiagram-shapeArrowUp": "Up Arrow",
        "dxDiagram-shapeArrowRight": "Right Arrow",
        "dxDiagram-shapeArrowDown": "Down Arrow",
        "dxDiagram-shapeArrowUpDown": "Up Down Arrow",
        "dxDiagram-shapeArrowLeftRight": "Left Right Arrow",
        "dxDiagram-shapeProcess": "Process",
        "dxDiagram-shapeDecision": "Decision",
        "dxDiagram-shapeTerminator": "Terminator",
        "dxDiagram-shapePredefinedProcess": "Predefined Process",
        "dxDiagram-shapeDocument": "Document",
        "dxDiagram-shapeMultipleDocuments": "Multiple Documents",
        "dxDiagram-shapeManualInput": "Manual Input",
        "dxDiagram-shapePreparation": "Preparation",
        "dxDiagram-shapeData": "Data",
        "dxDiagram-shapeDatabase": "Database",
        "dxDiagram-shapeHardDisk": "Hard Disk",
        "dxDiagram-shapeInternalStorage": "Internal Storage",
        "dxDiagram-shapePaperTape": "Paper Tape",
        "dxDiagram-shapeManualOperation": "Manual Operation",
        "dxDiagram-shapeDelay": "Delay",
        "dxDiagram-shapeStoredData": "Stored Data",
        "dxDiagram-shapeDisplay": "Display",
        "dxDiagram-shapeMerge": "Merge",
        "dxDiagram-shapeConnector": "Connector",
        "dxDiagram-shapeOr": "Or",
        "dxDiagram-shapeSummingJunction": "Summing Junction",
        "dxDiagram-shapeContainerDefaultText": "Container",
        "dxDiagram-shapeVerticalContainer": "Vertical Container",
        "dxDiagram-shapeHorizontalContainer": "Horizontal Container",
        "dxDiagram-shapeCardDefaultText": "Person's Name",
        "dxDiagram-shapeCardWithImageOnLeft": "Card with Image on the Left",
        "dxDiagram-shapeCardWithImageOnTop": "Card with Image on the Top",
        "dxDiagram-shapeCardWithImageOnRight": "Card with Image on the Right",
        "dxGantt-dialogTitle": "Title",
        "dxGantt-dialogStartTitle": "Start",
        "dxGantt-dialogEndTitle": "End",
        "dxGantt-dialogProgressTitle": "Progress",
        "dxGantt-dialogResourcesTitle": "Resources",
        "dxGantt-dialogResourceManagerTitle": "Resource Manager",
        "dxGantt-dialogTaskDetailsTitle": "Task Details",
        "dxGantt-dialogEditResourceListHint": "Edit Resource List",
        "dxGantt-dialogEditNoResources": "No resources",
        "dxGantt-dialogButtonAdd": "Add",
        "dxGantt-contextMenuNewTask": "New Task",
        "dxGantt-contextMenuNewSubtask": "New Subtask",
        "dxGantt-contextMenuDeleteTask": "Delete Task",
        "dxGantt-contextMenuDeleteDependency": "Delete Dependency",
        "dxGantt-dialogTaskDeleteConfirmation": "Deleting a task also deletes all its dependencies and subtasks. Are you sure you want to delete this task?",
        "dxGantt-dialogDependencyDeleteConfirmation": "Are you sure you want to delete the dependency from the task?",
        "dxGantt-dialogResourcesDeleteConfirmation": "Deleting a resource also deletes it from tasks to which this resource is assigned. Are you sure you want to delete these resources? Resources: {0}",
        "dxGantt-dialogConstraintCriticalViolationMessage": "The task you are attempting to move is linked to a second task by a dependency relation. This change would conflict with dependency rules. How would you like to proceed?",
        "dxGantt-dialogConstraintViolationMessage": "The task you are attempting to move is linked to a second task by a dependency relation. How would you like to proceed?",
        "dxGantt-dialogCancelOperationMessage": "Cancel the operation",
        "dxGantt-dialogDeleteDependencyMessage": "Delete the dependency",
        "dxGantt-dialogMoveTaskAndKeepDependencyMessage": "Move the task and keep the dependency",
        "dxGantt-dialogConstraintCriticalViolationSeveralTasksMessage": "The task you are attempting to move is linked to another tasks by dependency relations. This change would conflict with dependency rules. How would you like to proceed?",
        "dxGantt-dialogConstraintViolationSeveralTasksMessage": "The task you are attempting to move is linked to another tasks by dependency relations. How would you like to proceed?",
        "dxGantt-dialogDeleteDependenciesMessage": "Delete the dependency relations",
        "dxGantt-dialogMoveTaskAndKeepDependenciesMessage": "Move the task and keep the dependencies",
        "dxGantt-undo": "Undo",
        "dxGantt-redo": "Redo",
        "dxGantt-expandAll": "Expand All",
        "dxGantt-collapseAll": "Collapse All",
        "dxGantt-addNewTask": "Add New Task",
        "dxGantt-deleteSelectedTask": "Delete Selected Task",
        "dxGantt-zoomIn": "Zoom In",
        "dxGantt-zoomOut": "Zoom Out",
        "dxGantt-fullScreen": "Full Screen",
        "dxGantt-quarter": "Q{0}",
        "dxGantt-sortingAscendingText": "Sort Ascending",
        "dxGantt-sortingDescendingText": "Sort Descending",
        "dxGantt-sortingClearText": "Clear Sorting",
        "dxGantt-showResources": "Show Resources",
        "dxGantt-showDependencies": "Show Dependencies",
        "dxGantt-dialogStartDateValidation": "Start date must be after {0}",
        "dxGantt-dialogEndDateValidation": "End date must be after {0}",
        "dxGallery-itemName": "Gallery item",
        "dxMultiView-elementAriaRoleDescription": "MultiView",
        "dxMultiView-elementAriaLabel": "Use the arrow keys or swipe to navigate between views",
        "dxMultiView-itemAriaRoleDescription": "View",
        "dxMultiView-itemAriaLabel": "{0} of {1}",
        "dxSplitter-resizeHandleAriaLabel": "Split bar",
        "dxSplitter-resizeHandleAriaRoleDescription": "Separator",
        "dxStepper-optionalMark": "(Optional)"
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/message.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/message.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$string$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/string.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_string.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_messages$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/default_messages.js [app-client] (ecmascript)");
;
;
;
;
;
;
const baseDictionary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_messages$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["defaultMessages"]);
const getDataByLocale = (localeData, locale)=>{
    var _Object$entries$find;
    return localeData[locale] || (null === locale || void 0 === locale ? void 0 : locale.toLowerCase) && (null === (_Object$entries$find = Object.entries(localeData).find((_ref)=>{
        let [key] = _ref;
        return key.toLowerCase() === locale.toLowerCase();
    })) || void 0 === _Object$entries$find ? void 0 : _Object$entries$find[1]) || {};
};
const newMessages = {};
const messageLocalization = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    engine: function() {
        return "base";
    },
    _dictionary: baseDictionary,
    load: function(messages) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, this._dictionary, messages);
    },
    _localizablePrefix: "@",
    setup: function(localizablePrefix) {
        this._localizablePrefix = localizablePrefix;
    },
    localizeString: function(text) {
        const that = this;
        const regex = new RegExp("(^|[^a-zA-Z_0-9" + that._localizablePrefix + "-]+)(" + that._localizablePrefix + "{1,2})([a-zA-Z_0-9-]+)", "g");
        const escapeString = that._localizablePrefix + that._localizablePrefix;
        return text.replace(regex, (str, prefix, escape, localizationKey)=>{
            const defaultResult = that._localizablePrefix + localizationKey;
            let result;
            if (escape !== escapeString) {
                result = that.format(localizationKey);
            }
            if (!result) {
                newMessages[localizationKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["humanize"])(localizationKey);
            }
            return prefix + (result || defaultResult);
        });
    },
    getMessagesByLocales: function() {
        return this._dictionary;
    },
    getDictionary: function(onlyNew) {
        if (onlyNew) {
            return newMessages;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, newMessages, this.getMessagesByLocales()[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale()]);
    },
    getFormatter: function(key) {
        return this._getFormatterBase(key) || this._getFormatterBase(key, "en");
    },
    _getFormatterBase: function(key, locale) {
        const message = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getValueByClosestLocale((locale)=>getDataByLocale(this._dictionary, locale)[key]);
        if (message) {
            return function() {
                const args = 1 === arguments.length && Array.isArray(arguments[0]) ? arguments[0].slice(0) : Array.prototype.slice.call(arguments, 0);
                args.unshift(message);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["format"].apply(this, args);
            };
        }
    },
    format: function(key) {
        const formatter = this.getFormatter(key);
        const values = Array.prototype.slice.call(arguments, 1);
        return formatter && formatter.apply(this, values) || "";
    }
});
const __TURBOPACK__default__export__ = messageLocalization;
}),
"[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/utils/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/frame.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "cancelAnimationFrame": ()=>cancelAnimationFrame,
    "requestAnimationFrame": ()=>requestAnimationFrame
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/call_once.js [app-client] (ecmascript)");
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasWindow"])() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])() : {};
;
const FRAME_ANIMATION_STEP_TIME = 1e3 / 60;
let request = function(callback) {
    return setTimeout(callback, 16.666666666666668);
};
let cancel = function(requestID) {
    clearTimeout(requestID);
};
const setAnimationFrameMethods = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(function() {
    const nativeRequest = window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame;
    const nativeCancel = window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || window.oCancelAnimationFrame || window.msCancelAnimationFrame;
    if (nativeRequest && nativeCancel) {
        request = nativeRequest;
        cancel = nativeCancel;
    }
});
function requestAnimationFrame() {
    setAnimationFrameMethods();
    return request.apply(window, arguments);
}
function cancelAnimationFrame() {
    setAnimationFrameMethods();
    cancel.apply(window, arguments);
}
}),
"[project]/node_modules/devextreme/esm/common/core/animation/translator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/translator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "clearCache": ()=>clearCache,
    "getTranslate": ()=>getTranslate,
    "getTranslateCss": ()=>getTranslateCss,
    "locate": ()=>locate,
    "move": ()=>move,
    "parseTranslate": ()=>parseTranslate,
    "resetPosition": ()=>resetPosition
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element_data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
;
;
;
const TRANSLATOR_DATA_KEY = "dxTranslator";
const TRANSFORM_MATRIX_REGEX = /matrix(3d)?\((.+?)\)/;
const TRANSLATE_REGEX = /translate(?:3d)?\((.+?)\)/;
const locate = function($element) {
    $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])($element);
    const translate = getTranslate($element);
    return {
        left: translate.x,
        top: translate.y
    };
};
function isPercentValue(value) {
    return "string" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(value) && "%" === value[value.length - 1];
}
function cacheTranslate($element, translate) {
    if ($element.length) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])($element.get(0), "dxTranslator", translate);
    }
}
const clearCache = function($element) {
    if ($element.length) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeData"])($element.get(0), "dxTranslator");
    }
};
const getTranslateCss = function(translate) {
    translate.x = translate.x || 0;
    translate.y = translate.y || 0;
    const xValueString = isPercentValue(translate.x) ? translate.x : translate.x + "px";
    const yValueString = isPercentValue(translate.y) ? translate.y : translate.y + "px";
    return "translate(" + xValueString + ", " + yValueString + ")";
};
const getTranslate = function($element) {
    let result = $element.length ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])($element.get(0), "dxTranslator") : null;
    if (!result) {
        const transformValue = $element.css("transform") || getTranslateCss({
            x: 0,
            y: 0
        });
        let matrix = transformValue.match(TRANSFORM_MATRIX_REGEX);
        const is3D = matrix && matrix[1];
        if (matrix) {
            matrix = matrix[2].split(",");
            if ("3d" === is3D) {
                matrix = matrix.slice(12, 15);
            } else {
                matrix.push(0);
                matrix = matrix.slice(4, 7);
            }
        } else {
            matrix = [
                0,
                0,
                0
            ];
        }
        result = {
            x: parseFloat(matrix[0]),
            y: parseFloat(matrix[1]),
            z: parseFloat(matrix[2])
        };
        cacheTranslate($element, result);
    }
    return result;
};
const move = function($element, position) {
    $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])($element);
    const left = position.left;
    const top = position.top;
    let translate;
    if (void 0 === left) {
        translate = getTranslate($element);
        translate.y = top || 0;
    } else if (void 0 === top) {
        translate = getTranslate($element);
        translate.x = left || 0;
    } else {
        translate = {
            x: left || 0,
            y: top || 0,
            z: 0
        };
        cacheTranslate($element, translate);
    }
    $element.css({
        transform: getTranslateCss(translate)
    });
    if (isPercentValue(left) || isPercentValue(top)) {
        clearCache($element);
    }
};
const resetPosition = function($element, finishTransition) {
    $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])($element);
    let originalTransition;
    const stylesConfig = {
        left: 0,
        top: 0,
        transform: "none"
    };
    if (finishTransition) {
        originalTransition = $element.css("transition");
        stylesConfig.transition = "none";
    }
    $element.css(stylesConfig);
    clearCache($element);
    if (finishTransition) {
        $element.get(0).offsetHeight;
        $element.css("transition", originalTransition);
    }
};
const parseTranslate = function(translateString) {
    let result = translateString.match(TRANSLATE_REGEX);
    if (!result || !result[1]) {
        return;
    }
    result = result[1].split(",");
    result = {
        x: parseFloat(result[0]),
        y: parseFloat(result[1]),
        z: parseFloat(result[2])
    };
    return result;
};
}),
"[project]/node_modules/devextreme/esm/common/core/animation/easing.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/easing.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "convertTransitionTimingFuncToEasing": ()=>convertTransitionTimingFuncToEasing,
    "getEasing": ()=>getEasing,
    "setEasing": ()=>setEasing
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
;
const CSS_TRANSITION_EASING_REGEX = /cubic-bezier\((\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\s*,\s*(\d+(?:\.\d+)?)\)/;
const TransitionTimingFuncMap = {
    linear: "cubic-bezier(0, 0, 1, 1)",
    swing: "cubic-bezier(0.445, 0.05, 0.55, 0.95)",
    ease: "cubic-bezier(0.25, 0.1, 0.25, 1)",
    "ease-in": "cubic-bezier(0.42, 0, 1, 1)",
    "ease-out": "cubic-bezier(0, 0, 0.58, 1)",
    "ease-in-out": "cubic-bezier(0.42, 0, 0.58, 1)"
};
const polynomBezier = function(x1, y1, x2, y2) {
    const Cx = 3 * x1;
    const Bx = 3 * (x2 - x1) - Cx;
    const Ax = 1 - Cx - Bx;
    const Cy = 3 * y1;
    const By = 3 * (y2 - y1) - Cy;
    const Ay = 1 - Cy - By;
    const bezierX = function(t) {
        return t * (Cx + t * (Bx + t * Ax));
    };
    const derivativeX = function(t) {
        return Cx + t * (2 * Bx + 3 * t * Ax);
    };
    return function(t) {
        return function(t) {
            return t * (Cy + t * (By + t * Ay));
        }(function(t) {
            let x = t;
            let i = 0;
            let z;
            while(i < 14){
                z = bezierX(x) - t;
                if (Math.abs(z) < .001) {
                    break;
                }
                x -= z / derivativeX(x);
                i++;
            }
            return x;
        }(t));
    };
};
let easing = {};
const convertTransitionTimingFuncToEasing = function(cssTransitionEasing) {
    cssTransitionEasing = TransitionTimingFuncMap[cssTransitionEasing] || cssTransitionEasing;
    let coeffs = cssTransitionEasing.match(CSS_TRANSITION_EASING_REGEX);
    let forceName;
    if (!coeffs) {
        forceName = "linear";
        coeffs = TransitionTimingFuncMap[forceName].match(CSS_TRANSITION_EASING_REGEX);
    }
    coeffs = coeffs.slice(1, 5);
    for(let i = 0; i < coeffs.length; i++){
        coeffs[i] = parseFloat(coeffs[i]);
    }
    const easingName = forceName || "cubicbezier_" + coeffs.join("_").replace(/\./g, "p");
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(easing[easingName])) {
        easing[easingName] = function(x, t, b, c, d) {
            return c * polynomBezier(coeffs[0], coeffs[1], coeffs[2], coeffs[3])(t / d) + b;
        };
    }
    return easingName;
};
function setEasing(value) {
    easing = value;
}
function getEasing(name) {
    return easing[name];
}
}),
"[project]/node_modules/devextreme/esm/common/core/environment/devices.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment/devices.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/environment/devices.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/devices.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/environment/hide_callback.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment/hide_callback.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "hideCallback": ()=>hideCallback
});
const hideCallback = function() {
    let callbacks = [];
    return {
        add: function(callback) {
            if (!callbacks.includes(callback)) {
                callbacks.push(callback);
            }
        },
        remove: function(callback) {
            const indexOfCallback = callbacks.indexOf(callback);
            if (-1 !== indexOfCallback) {
                callbacks.splice(indexOfCallback, 1);
            }
        },
        fire: function() {
            const callback = callbacks.pop();
            const result = !!callback;
            if (result) {
                callback();
            }
            return result;
        },
        hasCallback: function() {
            return callbacks.length > 0;
        }
    };
}();
}),
"[project]/node_modules/devextreme/esm/common/core/environment/hide_top_overlay.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment/hide_top_overlay.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$hide_callback$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/hide_callback.js [app-client] (ecmascript)");
;
function __TURBOPACK__default__export__() {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$hide_callback$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hideCallback"].fire();
}
}),
"[project]/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport/init_mobile_viewport.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment/init_mobile_viewport/init_mobile_viewport.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "initMobileViewport": ()=>initMobileViewport
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$resize_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/resize_callbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/style.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <locals>");
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
;
;
;
;
;
;
;
const initMobileViewport = function(options) {
    options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, options);
    let realDevice = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real();
    const allowZoom = options.allowZoom;
    const allowPan = options.allowPan;
    const allowSelection = "allowSelection" in options ? options.allowSelection : "generic" === realDevice.platform;
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("meta[name=viewport]").length) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<meta>").attr("name", "viewport").appendTo("head");
    }
    const metaVerbs = [
        "width=device-width"
    ];
    const msTouchVerbs = [];
    if (allowZoom) {
        msTouchVerbs.push("pinch-zoom");
    } else {
        metaVerbs.push("initial-scale=1.0", "maximum-scale=1.0, user-scalable=no");
    }
    if (allowPan) {
        msTouchVerbs.push("pan-x", "pan-y");
    }
    if (!allowPan && !allowZoom) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("html, body").css({
            msContentZooming: "none",
            msUserSelect: "none",
            overflow: "hidden"
        });
    } else {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("html").css("msOverflowStyle", "-ms-autohiding-scrollbar");
    }
    if (!allowSelection && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].supportProp("userSelect")) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(".dx-viewport").css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["styleProp"])("userSelect"), "none");
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("meta[name=viewport]").attr("content", metaVerbs.join());
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("html").css("msTouchAction", msTouchVerbs.join(" ") || "none");
    realDevice = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real();
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].touch) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument(), ".dxInitMobileViewport");
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument(), "dxpointermove.dxInitMobileViewport", function(e) {
            const count = e.pointers.length;
            const isTouchEvent = "touch" === e.pointerType;
            const zoomDisabled = !allowZoom && count > 1;
            const panDisabled = !allowPan && 1 === count && !e.isScrollingEvent;
            if (isTouchEvent && (zoomDisabled || panDisabled)) {
                e.preventDefault();
            }
        });
    }
    if (realDevice.ios) {
        const isPhoneGap = "file:" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getLocation().protocol;
        if (!isPhoneGap) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$resize_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].add(function() {
                const windowWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(window);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setWidth"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("body"), windowWidth);
            });
        }
    }
    if (realDevice.android) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$resize_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].add(function() {
            setTimeout(function() {
                const activeElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getActiveElement();
                activeElement.scrollIntoViewIfNeeded ? activeElement.scrollIntoViewIfNeeded() : activeElement.scrollIntoView(false);
            });
        });
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment/init_mobile_viewport.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$init_mobile_viewport$2f$init_mobile_viewport$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport/init_mobile_viewport.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$init_mobile_viewport$2f$init_mobile_viewport$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["initMobileViewport"];
}),
"[project]/node_modules/devextreme/esm/common/core/localization/default_date_names.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/default_date_names.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
;
const MONTHS = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December"
];
const DAYS = [
    "Sunday",
    "Monday",
    "Tuesday",
    "Wednesday",
    "Thursday",
    "Friday",
    "Saturday"
];
const PERIODS = [
    "AM",
    "PM"
];
const QUARTERS = [
    "Q1",
    "Q2",
    "Q3",
    "Q4"
];
const cutCaptions = (captions, format)=>{
    const lengthByFormat = {
        abbreviated: 3,
        short: 2,
        narrow: 1
    };
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(captions, (caption)=>caption.substr(0, lengthByFormat[format]));
};
const __TURBOPACK__default__export__ = {
    getMonthNames: function(format) {
        return cutCaptions(MONTHS, format);
    },
    getDayNames: function(format) {
        return cutCaptions(DAYS, format);
    },
    getQuarterNames: function(format) {
        return QUARTERS;
    },
    getPeriodNames: function(format) {
        return PERIODS;
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/ldml/date.formatter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/ldml/date.formatter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getFormatter": ()=>getFormatter
});
function leftPad(text, length) {
    while(text.length < length){
        text = "0" + text;
    }
    return text;
}
const FORMAT_TYPES = {
    3: "abbreviated",
    4: "wide",
    5: "narrow"
};
const LDML_FORMATTERS = {
    y: function(date, count, useUtc) {
        let year = date[useUtc ? "getUTCFullYear" : "getFullYear"]();
        if (2 === count) {
            year %= 100;
        }
        return leftPad(year.toString(), count);
    },
    M: function(date, count, useUtc, dateParts) {
        const month = date[useUtc ? "getUTCMonth" : "getMonth"]();
        const formatType = FORMAT_TYPES[count];
        if (formatType) {
            return dateParts.getMonthNames(formatType, "format")[month];
        }
        return leftPad((month + 1).toString(), Math.min(count, 2));
    },
    L: function(date, count, useUtc, dateParts) {
        const month = date[useUtc ? "getUTCMonth" : "getMonth"]();
        const formatType = FORMAT_TYPES[count];
        if (formatType) {
            return dateParts.getMonthNames(formatType, "standalone")[month];
        }
        return leftPad((month + 1).toString(), Math.min(count, 2));
    },
    Q: function(date, count, useUtc, dateParts) {
        const month = date[useUtc ? "getUTCMonth" : "getMonth"]();
        const quarter = Math.floor(month / 3);
        const formatType = FORMAT_TYPES[count];
        if (formatType) {
            return dateParts.getQuarterNames(formatType)[quarter];
        }
        return leftPad((quarter + 1).toString(), Math.min(count, 2));
    },
    E: function(date, count, useUtc, dateParts) {
        const day = date[useUtc ? "getUTCDay" : "getDay"]();
        const formatType = FORMAT_TYPES[count < 3 ? 3 : count];
        return dateParts.getDayNames(formatType)[day];
    },
    a: function(date, count, useUtc, dateParts) {
        const hours = date[useUtc ? "getUTCHours" : "getHours"]();
        const period = hours < 12 ? 0 : 1;
        const formatType = FORMAT_TYPES[count];
        return dateParts.getPeriodNames(formatType)[period];
    },
    d: function(date, count, useUtc) {
        return leftPad(date[useUtc ? "getUTCDate" : "getDate"]().toString(), Math.min(count, 2));
    },
    H: function(date, count, useUtc) {
        return leftPad(date[useUtc ? "getUTCHours" : "getHours"]().toString(), Math.min(count, 2));
    },
    h: function(date, count, useUtc) {
        const hours = date[useUtc ? "getUTCHours" : "getHours"]();
        return leftPad((hours % 12 || 12).toString(), Math.min(count, 2));
    },
    m: function(date, count, useUtc) {
        return leftPad(date[useUtc ? "getUTCMinutes" : "getMinutes"]().toString(), Math.min(count, 2));
    },
    s: function(date, count, useUtc) {
        return leftPad(date[useUtc ? "getUTCSeconds" : "getSeconds"]().toString(), Math.min(count, 2));
    },
    S: function(date, count, useUtc) {
        return leftPad(date[useUtc ? "getUTCMilliseconds" : "getMilliseconds"]().toString(), 3).substr(0, count);
    },
    x: function(date, count, useUtc) {
        const timezoneOffset = useUtc ? 0 : date.getTimezoneOffset();
        const signPart = timezoneOffset > 0 ? "-" : "+";
        const timezoneOffsetAbs = Math.abs(timezoneOffset);
        const hours = Math.floor(timezoneOffsetAbs / 60);
        const minutes = timezoneOffsetAbs % 60;
        const hoursPart = leftPad(hours.toString(), 2);
        const minutesPart = leftPad(minutes.toString(), 2);
        return signPart + hoursPart + (count >= 3 ? ":" : "") + (count > 1 || minutes ? minutesPart : "");
    },
    X: function(date, count, useUtc) {
        if (useUtc || !date.getTimezoneOffset()) {
            return "Z";
        }
        return LDML_FORMATTERS.x(date, count, useUtc);
    },
    Z: function(date, count, useUtc) {
        return LDML_FORMATTERS.X(date, count >= 5 ? 3 : 2, useUtc);
    }
};
const getFormatter = function(format, dateParts) {
    return function(date) {
        let charIndex;
        let formatter;
        let char;
        let charCount = 0;
        let isEscaping = false;
        let isCurrentCharEqualsNext;
        let result = "";
        if (!date) {
            return null;
        }
        if (!format) {
            return date;
        }
        const useUtc = "Z" === format[format.length - 1] || "'Z'" === format.slice(-3);
        for(charIndex = 0; charIndex < format.length; charIndex++){
            char = format[charIndex];
            formatter = LDML_FORMATTERS[char];
            isCurrentCharEqualsNext = char === format[charIndex + 1];
            charCount++;
            if (!isCurrentCharEqualsNext) {
                if (formatter && !isEscaping) {
                    result += formatter(date, charCount, useUtc, dateParts);
                }
                charCount = 0;
            }
            if ("'" === char && !isCurrentCharEqualsNext) {
                isEscaping = !isEscaping;
            } else if (isEscaping || !formatter) {
                result += char;
            }
            if ("'" === char && isCurrentCharEqualsNext) {
                charIndex++;
            }
        }
        return result;
    };
};
}),
"[project]/node_modules/devextreme/esm/common/core/environment/time_zone_utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment/time_zone_utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getTimeZones": ()=>getTimeZones
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$m_utils_time_zone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/m_utils_time_zone.js [app-client] (ecmascript)");
;
const getTimeZones = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$m_utils_time_zone$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTimeZones;
}),
"[project]/node_modules/devextreme/esm/common/core/environment.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/environment.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/devices.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$hide_top_overlay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/hide_top_overlay.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$init_mobile_viewport$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$time_zone_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/time_zone_utils.js [app-client] (ecmascript)");
;
;
;
;
;
}),
"[project]/node_modules/devextreme/esm/common/core/environment.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/devices.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$hide_top_overlay$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/hide_top_overlay.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$init_mobile_viewport$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$time_zone_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/time_zone_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/animation/position.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/position.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/position.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_position.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/translator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/support.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/style.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_style.js [app-client] (ecmascript)");
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
;
;
;
;
;
;
;
;
;
const horzRe = /left|right/;
const vertRe = /top|bottom/;
const collisionRe = /fit|flip|none/;
const scaleRe = /scale\(.+?\)/;
const IS_SAFARI = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].safari;
const normalizeAlign = function(raw) {
    const result = {
        h: "center",
        v: "center"
    };
    const pair = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["splitPair"])(raw);
    if (pair) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(pair, function() {
            const w = String(this).toLowerCase();
            if (horzRe.test(w)) {
                result.h = w;
            } else if (vertRe.test(w)) {
                result.v = w;
            }
        });
    }
    return result;
};
const normalizeOffset = function(raw, preventRound) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["pairToObject"])(raw, preventRound);
};
const normalizeCollision = function(raw) {
    const pair = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["splitPair"])(raw);
    let h = String(pair && pair[0]).toLowerCase();
    let v = String(pair && pair[1]).toLowerCase();
    if (!collisionRe.test(h)) {
        h = "none";
    }
    if (!collisionRe.test(v)) {
        v = h;
    }
    return {
        h: h,
        v: v
    };
};
const getAlignFactor = function(align) {
    switch(align){
        case "center":
            return .5;
        case "right":
        case "bottom":
            return 1;
        default:
            return 0;
    }
};
const inverseAlign = function(align) {
    switch(align){
        case "left":
            return "right";
        case "right":
            return "left";
        case "top":
            return "bottom";
        case "bottom":
            return "top";
        default:
            return align;
    }
};
const calculateOversize = function(data, bounds) {
    let oversize = 0;
    if (data.myLocation < bounds.min) {
        oversize += bounds.min - data.myLocation;
    }
    if (data.myLocation > bounds.max) {
        oversize += data.myLocation - bounds.max;
    }
    return oversize;
};
const collisionSide = function(direction, data, bounds) {
    if (data.myLocation < bounds.min) {
        return "h" === direction ? "left" : "top";
    }
    if (data.myLocation > bounds.max) {
        return "h" === direction ? "right" : "bottom";
    }
    return "none";
};
const initMyLocation = function(data) {
    data.myLocation = data.atLocation + getAlignFactor(data.atAlign) * data.atSize - getAlignFactor(data.myAlign) * data.mySize + data.offset;
};
const collisionResolvers = {
    fit: function(data, bounds) {
        let result = false;
        if (data.myLocation > bounds.max) {
            data.myLocation = bounds.max;
            result = true;
        }
        if (data.myLocation < bounds.min) {
            data.myLocation = bounds.min;
            result = true;
        }
        data.fit = result;
    },
    flip: function(data, bounds) {
        data.flip = false;
        if ("center" === data.myAlign && "center" === data.atAlign) {
            return;
        }
        if (data.myLocation < bounds.min || data.myLocation > bounds.max) {
            const inverseData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, data, {
                myAlign: inverseAlign(data.myAlign),
                atAlign: inverseAlign(data.atAlign),
                offset: -data.offset
            });
            initMyLocation(inverseData);
            inverseData.oversize = calculateOversize(inverseData, bounds);
            if (inverseData.myLocation >= bounds.min && inverseData.myLocation <= bounds.max || data.oversize > inverseData.oversize) {
                data.myLocation = inverseData.myLocation;
                data.oversize = inverseData.oversize;
                data.flip = true;
            }
        }
    },
    flipfit: function(data, bounds) {
        this.flip(data, bounds);
        this.fit(data, bounds);
    },
    none: function(data) {
        data.oversize = 0;
    }
};
let scrollbarWidth;
const calculateScrollbarWidth = function() {
    const $scrollDiv = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").css({
        width: 100,
        height: 100,
        overflow: "scroll",
        position: "absolute",
        top: -9999
    }).appendTo((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("body"));
    const result = $scrollDiv.get(0).offsetWidth - $scrollDiv.get(0).clientWidth;
    $scrollDiv.remove();
    scrollbarWidth = result;
};
const defaultPositionResult = {
    h: {
        location: 0,
        flip: false,
        fit: false,
        oversize: 0
    },
    v: {
        location: 0,
        flip: false,
        fit: false,
        oversize: 0
    }
};
const calculatePosition = function(what, options) {
    const $what = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(what);
    const currentOffset = $what.offset();
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, defaultPositionResult, {
        h: {
            location: currentOffset.left
        },
        v: {
            location: currentOffset.top
        }
    });
    if (!options) {
        return result;
    }
    const my = normalizeAlign(options.my);
    const at = normalizeAlign(options.at);
    let of = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options.of).length && options.of || window;
    const offset = normalizeOffset(options.offset, options.precise);
    const collision = normalizeCollision(options.collision);
    const boundary = options.boundary;
    const boundaryOffset = normalizeOffset(options.boundaryOffset, options.precise);
    const h = {
        mySize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($what),
        myAlign: my.h,
        atAlign: at.h,
        offset: offset.h,
        collision: collision.h,
        boundaryOffset: boundaryOffset.h
    };
    const v = {
        mySize: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($what),
        myAlign: my.v,
        atAlign: at.v,
        offset: offset.v,
        collision: collision.v,
        boundaryOffset: boundaryOffset.v
    };
    if (of.preventDefault) {
        h.atLocation = of.pageX;
        v.atLocation = of.pageY;
        h.atSize = 0;
        v.atSize = 0;
    } else {
        of = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(of);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(of[0])) {
            h.atLocation = of.scrollLeft();
            v.atLocation = of.scrollTop();
            if ("phone" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real().deviceType && of[0].visualViewport) {
                h.atLocation = Math.max(h.atLocation, of[0].visualViewport.offsetLeft);
                v.atLocation = Math.max(v.atLocation, of[0].visualViewport.offsetTop);
                h.atSize = of[0].visualViewport.width;
                v.atSize = of[0].visualViewport.height;
            } else {
                h.atSize = of[0].innerWidth > of[0].outerWidth ? of[0].innerWidth : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(of);
                v.atSize = of[0].innerHeight > of[0].outerHeight || IS_SAFARI ? of[0].innerHeight : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])(of);
            }
        } else if (9 === of[0].nodeType) {
            h.atLocation = 0;
            v.atLocation = 0;
            h.atSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(of);
            v.atSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])(of);
        } else {
            const ofRect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBoundingRect"])(of.get(0));
            const o = getOffsetWithoutScale(of);
            h.atLocation = o.left;
            v.atLocation = o.top;
            h.atSize = Math.max(ofRect.width, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])(of));
            v.atSize = Math.max(ofRect.height, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])(of));
        }
    }
    initMyLocation(h);
    initMyLocation(v);
    const bounds = function() {
        const win = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(window);
        const windowWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(win);
        const windowHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])(win);
        let left = win.scrollLeft();
        let top = win.scrollTop();
        const documentElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocumentElement();
        const hZoomLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["touch"] ? documentElement.clientWidth / windowWidth : 1;
        const vZoomLevel = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["touch"] ? documentElement.clientHeight / windowHeight : 1;
        if (void 0 === scrollbarWidth) {
            calculateScrollbarWidth();
        }
        let boundaryWidth = windowWidth;
        let boundaryHeight = windowHeight;
        if (boundary && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(boundary)) {
            const $boundary = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(boundary);
            const boundaryPosition = $boundary.offset();
            left = boundaryPosition.left;
            top = boundaryPosition.top;
            boundaryWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])($boundary);
            boundaryHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])($boundary);
        }
        return {
            h: {
                min: left + h.boundaryOffset,
                max: left + boundaryWidth / hZoomLevel - h.mySize - h.boundaryOffset
            },
            v: {
                min: top + v.boundaryOffset,
                max: top + boundaryHeight / vZoomLevel - v.mySize - v.boundaryOffset
            }
        };
    }();
    h.oversize = calculateOversize(h, bounds.h);
    v.oversize = calculateOversize(v, bounds.v);
    h.collisionSide = collisionSide("h", h, bounds.h);
    v.collisionSide = collisionSide("v", v, bounds.v);
    if (collisionResolvers[h.collision]) {
        collisionResolvers[h.collision](h, bounds.h);
    }
    if (collisionResolvers[v.collision]) {
        collisionResolvers[v.collision](v, bounds.v);
    }
    const preciser = function(number) {
        return options.precise ? number : Math.round(number);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, result, {
        h: {
            location: preciser(h.myLocation),
            oversize: preciser(h.oversize),
            fit: h.fit,
            flip: h.flip,
            collisionSide: h.collisionSide
        },
        v: {
            location: preciser(v.myLocation),
            oversize: preciser(v.oversize),
            fit: v.fit,
            flip: v.flip,
            collisionSide: v.collisionSide
        },
        precise: options.precise
    });
    return result;
};
const setScaleProperty = function(element, scale, styleAttr, isEmpty) {
    const stylePropIsValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(element.style) && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isNode(element.style);
    const newStyleValue = isEmpty ? styleAttr.replace(scale, "") : styleAttr;
    if (stylePropIsValid) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setStyle"])(element, newStyleValue, false);
    } else {
        const styleAttributeNode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAttribute("style");
        styleAttributeNode.value = newStyleValue;
        element.setAttributeNode(styleAttributeNode);
    }
};
const getOffsetWithoutScale = function($startElement) {
    var _currentElement$getAt, _style$match;
    let $currentElement = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : $startElement;
    const currentElement = $currentElement.get(0);
    if (!currentElement) {
        return $startElement.offset();
    }
    const style = (null === (_currentElement$getAt = currentElement.getAttribute) || void 0 === _currentElement$getAt ? void 0 : _currentElement$getAt.call(currentElement, "style")) || "";
    const scale = null === (_style$match = style.match(scaleRe)) || void 0 === _style$match ? void 0 : _style$match[0];
    let offset;
    if (scale) {
        setScaleProperty(currentElement, scale, style, true);
        offset = getOffsetWithoutScale($startElement, $currentElement.parent());
        setScaleProperty(currentElement, scale, style, false);
    } else {
        offset = getOffsetWithoutScale($startElement, $currentElement.parent());
    }
    return offset;
};
const position = function(what, options) {
    const $what = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(what);
    if (!options) {
        return $what.offset();
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetPosition"])($what, true);
    const offset = getOffsetWithoutScale($what);
    const targetPosition = options.h && options.v ? options : calculatePosition($what, options);
    const preciser = function(number) {
        return options.precise ? number : Math.round(number);
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["move"])($what, {
        left: targetPosition.h.location - preciser(offset.left),
        top: targetPosition.v.location - preciser(offset.top)
    });
    return targetPosition;
};
const offset = function(element) {
    element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element).get(0);
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(element)) {
        return null;
    } else if (element && "pageY" in element && "pageX" in element) {
        return {
            top: element.pageY,
            left: element.pageX
        };
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element).offset();
};
if (!position.inverseAlign) {
    position.inverseAlign = inverseAlign;
}
if (!position.normalizeAlign) {
    position.normalizeAlign = normalizeAlign;
}
const __TURBOPACK__default__export__ = {
    calculateScrollbarWidth: calculateScrollbarWidth,
    calculate: calculatePosition,
    setup: position,
    offset: offset
};
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/event_registrator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/remove.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/remove.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_remove.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/remove.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_remove.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/remove.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/fx.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/translator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$easing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/easing.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/position.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/remove.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_remove.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const removeEventName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeEvent"], "dxFX");
;
const RELATIVE_VALUE_REGEX = /^([+-])=(.*)/i;
const ANIM_DATA_KEY = "dxAnimData";
const ANIM_QUEUE_KEY = "dxAnimQueue";
const TRANSFORM_PROP = "transform";
const TransitionAnimationStrategy = {
    initAnimation: function($element, config) {
        $element.css({
            transitionProperty: "none"
        });
        if ("string" === typeof config.from) {
            $element.addClass(config.from);
        } else {
            setProps($element, config.from);
        }
        const that = this;
        const deferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        const cleanupWhen = config.cleanupWhen;
        config.transitionAnimation = {
            deferred: deferred,
            finish: function() {
                that._finishTransition($element);
                if (cleanupWhen) {
                    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"])(deferred, cleanupWhen).always(function() {
                        that._cleanup($element, config);
                    });
                } else {
                    that._cleanup($element, config);
                }
                deferred.resolveWith($element, [
                    config,
                    $element
                ]);
            }
        };
        this._completeAnimationCallback($element, config).done(function() {
            config.transitionAnimation.finish();
        }).fail(function() {
            deferred.rejectWith($element, [
                config,
                $element
            ]);
        });
        if (!config.duration) {
            config.transitionAnimation.finish();
        }
        $element.css("transform");
    },
    animate: function($element, config) {
        this._startAnimation($element, config);
        return config.transitionAnimation.deferred.promise();
    },
    _completeAnimationCallback: function($element, config) {
        const that = this;
        const startTime = Date.now() + config.delay;
        const deferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        const transitionEndFired = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        const simulatedTransitionEndFired = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        let simulatedEndEventTimer;
        const transitionEndEventFullName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].transitionEndEventName() + ".dxFX";
        config.transitionAnimation.cleanup = function() {
            clearTimeout(simulatedEndEventTimer);
            clearTimeout(waitForJSCompleteTimer);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($element, transitionEndEventFullName);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($element, removeEventName);
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].one($element, transitionEndEventFullName, function() {
            if (Date.now() - startTime >= config.duration) {
                transitionEndFired.reject();
            }
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($element, removeEventName);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, removeEventName, function() {
            that.stop($element, config);
            deferred.reject();
        });
        const waitForJSCompleteTimer = setTimeout(function() {
            simulatedEndEventTimer = setTimeout(function() {
                simulatedTransitionEndFired.reject();
            }, config.duration + config.delay + fx._simulatedTransitionEndDelay);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"])(transitionEndFired, simulatedTransitionEndFired).fail((function() {
                deferred.resolve();
            }).bind(this));
        });
        return deferred.promise();
    },
    _startAnimation: function($element, config) {
        $element.css({
            transitionProperty: "all",
            transitionDelay: config.delay + "ms",
            transitionDuration: config.duration + "ms",
            transitionTimingFunction: config.easing
        });
        if ("string" === typeof config.to) {
            $element[0].className += " " + config.to;
        } else if (config.to) {
            setProps($element, config.to);
        }
    },
    _finishTransition: function($element) {
        $element.css("transition", "none");
    },
    _cleanup: function($element, config) {
        config.transitionAnimation.cleanup();
        if ("string" === typeof config.from) {
            $element.removeClass(config.from);
            $element.removeClass(config.to);
        }
    },
    stop: function($element, config, jumpToEnd) {
        if (!config) {
            return;
        }
        if (jumpToEnd) {
            config.transitionAnimation.finish();
        } else {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(config.to)) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(config.to, function(key) {
                    $element.css(key, $element.css(key));
                });
            }
            this._finishTransition($element);
            this._cleanup($element, config);
        }
    }
};
const FrameAnimationStrategy = {
    initAnimation: function($element, config) {
        setProps($element, config.from);
    },
    animate: function($element, config) {
        const deferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        const that = this;
        if (!config) {
            return deferred.reject().promise();
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(config.to, function(prop) {
            if (void 0 === config.from[prop]) {
                config.from[prop] = that._normalizeValue($element.css(prop));
            }
        });
        if (config.to.transform) {
            config.from.transform = that._parseTransform(config.from.transform);
            config.to.transform = that._parseTransform(config.to.transform);
        }
        config.frameAnimation = {
            to: config.to,
            from: config.from,
            currentValue: config.from,
            easing: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$easing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertTransitionTimingFuncToEasing"])(config.easing),
            duration: config.duration,
            startTime: (new Date).valueOf(),
            finish: function() {
                this.currentValue = this.to;
                this.draw();
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(config.frameAnimation.animationFrameId);
                deferred.resolve();
            },
            draw: function() {
                if (config.draw) {
                    config.draw(this.currentValue);
                    return;
                }
                const currentValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, this.currentValue);
                if (currentValue.transform) {
                    currentValue.transform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(currentValue.transform, function(value, prop) {
                        if ("translate" === prop) {
                            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslateCss"])(value);
                        } else if ("scale" === prop) {
                            return "scale(" + value + ")";
                        } else if ("rotate" === prop.substr(0, prop.length - 1)) {
                            return prop + "(" + value + "deg)";
                        }
                    }).join(" ");
                }
                $element.css(currentValue);
            }
        };
        if (config.delay) {
            config.frameAnimation.startTime += config.delay;
            config.frameAnimation.delayTimeout = setTimeout(function() {
                that._startAnimation($element, config);
            }, config.delay);
        } else {
            that._startAnimation($element, config);
        }
        return deferred.promise();
    },
    _startAnimation: function($element, config) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($element, removeEventName);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, removeEventName, function() {
            if (config.frameAnimation) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(config.frameAnimation.animationFrameId);
            }
        });
        this._animationStep($element, config);
    },
    _parseTransform: function(transformString) {
        const result = {};
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(transformString.match(/\w+\d*\w*\([^)]*\)\s*/g), function(i, part) {
            const translateData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseTranslate"])(part);
            const scaleData = part.match(/scale\((.+?)\)/);
            const rotateData = part.match(/(rotate.)\((.+)deg\)/);
            if (translateData) {
                result.translate = translateData;
            }
            if (scaleData && scaleData[1]) {
                result.scale = parseFloat(scaleData[1]);
            }
            if (rotateData && rotateData[1]) {
                result[rotateData[1]] = parseFloat(rotateData[2]);
            }
        });
        return result;
    },
    stop: function($element, config, jumpToEnd) {
        const frameAnimation = config && config.frameAnimation;
        if (!frameAnimation) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(frameAnimation.animationFrameId);
        clearTimeout(frameAnimation.delayTimeout);
        if (jumpToEnd) {
            frameAnimation.finish();
        }
        delete config.frameAnimation;
    },
    _animationStep: function($element, config) {
        const frameAnimation = config && config.frameAnimation;
        if (!frameAnimation) {
            return;
        }
        const now = (new Date).valueOf();
        if (now >= frameAnimation.startTime + frameAnimation.duration) {
            frameAnimation.finish();
            return;
        }
        frameAnimation.currentValue = this._calcStepValue(frameAnimation, now - frameAnimation.startTime);
        frameAnimation.draw();
        const that = this;
        frameAnimation.animationFrameId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["requestAnimationFrame"])(function() {
            that._animationStep($element, config);
        });
    },
    _calcStepValue: function(frameAnimation, currentDuration) {
        const calcValueRecursively = function(from, to) {
            const result = Array.isArray(to) ? [] : {};
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(to, function(propName, endPropValue) {
                if ("string" === typeof endPropValue && false === parseFloat(endPropValue)) {
                    return true;
                }
                result[propName] = "object" === typeof endPropValue ? calcValueRecursively(from[propName], endPropValue) : function(propName) {
                    const x = currentDuration / frameAnimation.duration;
                    const t = currentDuration;
                    const b = 1 * from[propName];
                    const c = to[propName] - from[propName];
                    const d = frameAnimation.duration;
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$easing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEasing"])(frameAnimation.easing)(x, t, b, c, d);
                }(propName);
            });
            return result;
        };
        return calcValueRecursively(frameAnimation.from, frameAnimation.to);
    },
    _normalizeValue: function(value) {
        const numericValue = parseFloat(value);
        if (false === numericValue) {
            return value;
        }
        return numericValue;
    }
};
const FallbackToNoAnimationStrategy = {
    initAnimation: function() {},
    animate: function() {
        return (new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"]).resolve().promise();
    },
    stop: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    isSynchronous: true
};
const getAnimationStrategy = function(config) {
    config = config || {};
    const animationStrategies = {
        transition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].transition() ? TransitionAnimationStrategy : FrameAnimationStrategy,
        frame: FrameAnimationStrategy,
        noAnimation: FallbackToNoAnimationStrategy
    };
    let strategy = config.strategy || "transition";
    if ("css" === config.type && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].transition()) {
        strategy = "noAnimation";
    }
    return animationStrategies[strategy];
};
const baseConfigValidator = function(config, animationType, validate, typeMessage) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])([
        "from",
        "to"
    ], function() {
        if (!validate(config[this])) {
            throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error("E0010", animationType, this, typeMessage);
        }
    });
};
const isObjectConfigValidator = function(config, animationType) {
    return baseConfigValidator(config, animationType, function(target) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(target);
    }, "a plain object");
};
const isStringConfigValidator = function(config, animationType) {
    return baseConfigValidator(config, animationType, function(target) {
        return "string" === typeof target;
    }, "a string");
};
const CustomAnimationConfigurator = {
    setup: function() {}
};
const CssAnimationConfigurator = {
    validateConfig: function(config) {
        isStringConfigValidator(config, "css");
    },
    setup: function() {}
};
const positionAliases = {
    top: {
        my: "bottom center",
        at: "top center"
    },
    bottom: {
        my: "top center",
        at: "bottom center"
    },
    right: {
        my: "left center",
        at: "right center"
    },
    left: {
        my: "right center",
        at: "left center"
    }
};
const SlideAnimationConfigurator = {
    validateConfig: function(config) {
        isObjectConfigValidator(config, "slide");
    },
    setup: function($element, config) {
        const location = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["locate"])($element);
        if ("slide" !== config.type) {
            const positioningConfig = "slideIn" === config.type ? config.from : config.to;
            positioningConfig.position = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                of: window
            }, positionAliases[config.direction]);
            setupPosition($element, positioningConfig);
        }
        this._setUpConfig(location, config.from);
        this._setUpConfig(location, config.to);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clearCache"])($element);
    },
    _setUpConfig: function(location, config) {
        config.left = "left" in config ? config.left : "+=0";
        config.top = "top" in config ? config.top : "+=0";
        this._initNewPosition(location, config);
    },
    _initNewPosition: function(location, config) {
        const position = {
            left: config.left,
            top: config.top
        };
        delete config.left;
        delete config.top;
        let relativeValue = this._getRelativeValue(position.left);
        if (void 0 !== relativeValue) {
            position.left = relativeValue + location.left;
        } else {
            config.left = 0;
        }
        relativeValue = this._getRelativeValue(position.top);
        if (void 0 !== relativeValue) {
            position.top = relativeValue + location.top;
        } else {
            config.top = 0;
        }
        config.transform = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslateCss"])({
            x: position.left,
            y: position.top
        });
    },
    _getRelativeValue: function(value) {
        let relativeValue;
        if ("string" === typeof value && (relativeValue = RELATIVE_VALUE_REGEX.exec(value))) {
            return parseInt(relativeValue[1] + "1") * relativeValue[2];
        }
    }
};
const FadeAnimationConfigurator = {
    setup: function($element, config) {
        const from = config.from;
        const to = config.to;
        const defaultFromOpacity = "fadeOut" === config.type ? 1 : 0;
        const defaultToOpacity = "fadeOut" === config.type ? 0 : 1;
        var _from_opacity;
        let fromOpacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(from) ? String((_from_opacity = from.opacity) !== null && _from_opacity !== void 0 ? _from_opacity : defaultFromOpacity) : String(from);
        var _to_opacity;
        let toOpacity = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(to) ? String((_to_opacity = to.opacity) !== null && _to_opacity !== void 0 ? _to_opacity : defaultToOpacity) : String(to);
        if (!config.skipElementInitialStyles) {
            fromOpacity = $element.css("opacity");
        }
        switch(config.type){
            case "fadeIn":
                toOpacity = 1;
                break;
            case "fadeOut":
                toOpacity = 0;
        }
        config.from = {
            visibility: "visible",
            opacity: fromOpacity
        };
        config.to = {
            opacity: toOpacity
        };
    }
};
const PopAnimationConfigurator = {
    validateConfig: function(config) {
        isObjectConfigValidator(config, "pop");
    },
    setup: function($element, config) {
        const from = config.from;
        const to = config.to;
        const fromOpacity = "opacity" in from ? from.opacity : $element.css("opacity");
        const toOpacity = "opacity" in to ? to.opacity : 1;
        const fromScale = "scale" in from ? from.scale : 0;
        const toScale = "scale" in to ? to.scale : 1;
        config.from = {
            opacity: fromOpacity
        };
        const translate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslate"])($element);
        config.from.transform = this._getCssTransform(translate, fromScale);
        config.to = {
            opacity: toOpacity
        };
        config.to.transform = this._getCssTransform(translate, toScale);
    },
    _getCssTransform: function(translate, scale) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getTranslateCss"])(translate) + "scale(" + scale + ")";
    }
};
const animationConfigurators = {
    custom: CustomAnimationConfigurator,
    slide: SlideAnimationConfigurator,
    slideIn: SlideAnimationConfigurator,
    slideOut: SlideAnimationConfigurator,
    fade: FadeAnimationConfigurator,
    fadeIn: FadeAnimationConfigurator,
    fadeOut: FadeAnimationConfigurator,
    pop: PopAnimationConfigurator,
    css: CssAnimationConfigurator
};
const getAnimationConfigurator = function(config) {
    const result = animationConfigurators[config.type];
    if (!result) {
        throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error("E0011", config.type);
    }
    return result;
};
const defaultJSConfig = {
    type: "custom",
    from: {},
    to: {},
    duration: 400,
    start: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    complete: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    easing: "ease",
    delay: 0
};
const defaultCssConfig = {
    duration: 400,
    easing: "ease",
    delay: 0
};
function setupAnimationOnElement() {
    const $element = this.element;
    const config = this.config;
    setupPosition($element, config.from);
    setupPosition($element, config.to);
    this.configurator.setup($element, config);
    $element.data("dxAnimData", this);
    if (fx.off) {
        config.duration = 0;
        config.delay = 0;
    }
    this.strategy.initAnimation($element, config);
    if (config.start) {
        const element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])($element);
        config.start.apply(this, [
            element,
            config
        ]);
    }
}
const onElementAnimationComplete = function(animation) {
    const $element = animation.element;
    const config = animation.config;
    $element.removeData("dxAnimData");
    if (config.complete) {
        const element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])($element);
        config.complete.apply(this, [
            element,
            config
        ]);
    }
    animation.deferred.resolveWith(this, [
        $element,
        config
    ]);
};
const startAnimationOnElement = function() {
    const animation = this;
    const $element = animation.element;
    const config = animation.config;
    animation.isStarted = true;
    return animation.strategy.animate($element, config).done(function() {
        onElementAnimationComplete(animation);
    }).fail(function() {
        animation.deferred.rejectWith(this, [
            $element,
            config
        ]);
    });
};
const stopAnimationOnElement = function(jumpToEnd) {
    const animation = this;
    const $element = animation.element;
    const config = animation.config;
    clearTimeout(animation.startTimeout);
    if (!animation.isStarted) {
        animation.start();
    }
    animation.strategy.stop($element, config, jumpToEnd);
};
const scopedRemoveEvent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeEvent"], "dxFXStartAnimation");
const subscribeToRemoveEvent = function(animation) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(animation.element, scopedRemoveEvent);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(animation.element, scopedRemoveEvent, function() {
        fx.stop(animation.element);
    });
    animation.deferred.always(function() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(animation.element, scopedRemoveEvent);
    });
};
const createAnimation = function(element, initialConfig) {
    const defaultConfig = "css" === initialConfig.type ? defaultCssConfig : defaultJSConfig;
    const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, defaultConfig, initialConfig);
    const configurator = getAnimationConfigurator(config);
    const strategy = getAnimationStrategy(config);
    const animation = {
        element: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element),
        config: config,
        configurator: configurator,
        strategy: strategy,
        isSynchronous: strategy.isSynchronous,
        setup: setupAnimationOnElement,
        start: startAnimationOnElement,
        stop: stopAnimationOnElement,
        deferred: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"]
    };
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(configurator.validateConfig)) {
        configurator.validateConfig(config);
    }
    subscribeToRemoveEvent(animation);
    return animation;
};
const animate = function(element, config) {
    const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
    if (!$element.length) {
        return (new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"]).resolve().promise();
    }
    const animation = createAnimation($element, config);
    pushInAnimationQueue($element, animation);
    return animation.deferred.promise();
};
function pushInAnimationQueue($element, animation) {
    const queueData = getAnimQueueData($element);
    writeAnimQueueData($element, queueData);
    queueData.push(animation);
    if (!isAnimating($element)) {
        shiftFromAnimationQueue($element, queueData);
    }
}
function getAnimQueueData($element) {
    return $element.data("dxAnimQueue") || [];
}
function writeAnimQueueData($element, queueData) {
    $element.data("dxAnimQueue", queueData);
}
const destroyAnimQueueData = function($element) {
    $element.removeData("dxAnimQueue");
};
function isAnimating($element) {
    return !!$element.data("dxAnimData");
}
function shiftFromAnimationQueue($element, queueData) {
    queueData = getAnimQueueData($element);
    if (!queueData.length) {
        return;
    }
    const animation = queueData.shift();
    if (0 === queueData.length) {
        destroyAnimQueueData($element);
    }
    executeAnimation(animation).done(function() {
        if (!isAnimating($element)) {
            shiftFromAnimationQueue($element);
        }
    });
}
function executeAnimation(animation) {
    animation.setup();
    if (fx.off || animation.isSynchronous) {
        animation.start();
    } else {
        animation.startTimeout = setTimeout(function() {
            animation.start();
        });
    }
    return animation.deferred.promise();
}
function setupPosition($element, config) {
    if (!config || !config.position) {
        return;
    }
    const win = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(window);
    let left = 0;
    let top = 0;
    const position = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].calculate($element, config.position);
    const offset = $element.offset();
    const currentPosition = $element.position();
    if (currentPosition.top > offset.top) {
        top = win.scrollTop();
    }
    if (currentPosition.left > offset.left) {
        left = win.scrollLeft();
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(config, {
        left: position.h.location - offset.left + currentPosition.left - left,
        top: position.v.location - offset.top + currentPosition.top - top
    });
    delete config.position;
}
function setProps($element, props) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(props, function(key, value) {
        try {
            $element.css(key, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(value) ? value() : value);
        } catch (e) {}
    });
}
const stop = function(element, jumpToEnd) {
    const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
    const queueData = getAnimQueueData($element);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(queueData, function(_, animation) {
        animation.config.delay = 0;
        animation.config.duration = 0;
        animation.isSynchronous = true;
    });
    if (!isAnimating($element)) {
        shiftFromAnimationQueue($element, queueData);
    }
    const animation = $element.data("dxAnimData");
    if (animation) {
        animation.stop(jumpToEnd);
    }
    $element.removeData("dxAnimData");
    destroyAnimQueueData($element);
};
const fx = {
    off: false,
    animationTypes: animationConfigurators,
    animate: animate,
    createAnimation: createAnimation,
    isAnimating: isAnimating,
    stop: stop,
    _simulatedTransitionEndDelay: 100
};
const __TURBOPACK__default__export__ = fx;
}),
"[project]/node_modules/devextreme/esm/common/core/animation/presets/presets.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/presets/presets.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "PresetCollection": ()=>AnimationPresetCollection,
    "presets": ()=>animationPresets
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/component.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/widget/component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment/devices.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript)");
;
;
;
;
;
;
const directionPostfixes = {
    forward: " dx-forward",
    backward: " dx-backward",
    none: " dx-no-direction",
    undefined: " dx-no-direction"
};
const optionPrefix = "preset_";
const AnimationPresetCollection = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"].inherit({
    ctor: function() {
        this.callBase.apply(this, arguments);
        this._registeredPresets = [];
        this.resetToDefaults();
    },
    _getDefaultOptions: function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this.callBase(), {
            defaultAnimationDuration: 400,
            defaultAnimationDelay: 0,
            defaultStaggerAnimationDuration: 300,
            defaultStaggerAnimationDelay: 40,
            defaultStaggerAnimationStartDelay: 500
        });
    },
    _defaultOptionsRules: function() {
        return this.callBase().concat([
            {
                device: function(device) {
                    return device.phone;
                },
                options: {
                    defaultStaggerAnimationDuration: 350,
                    defaultStaggerAnimationDelay: 50,
                    defaultStaggerAnimationStartDelay: 0
                }
            },
            {
                device: function() {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].current().android || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real.android;
                },
                options: {
                    defaultAnimationDelay: 100
                }
            }
        ]);
    },
    _getPresetOptionName: function(animationName) {
        return "preset_" + animationName;
    },
    _createAndroidSlideAnimationConfig: function(throughOpacity, widthMultiplier) {
        const that = this;
        const createBaseConfig = function(configModifier) {
            return {
                type: "slide",
                delay: void 0 === configModifier.delay ? that.option("defaultAnimationDelay") : configModifier.delay,
                duration: void 0 === configModifier.duration ? that.option("defaultAnimationDuration") : configModifier.duration
            };
        };
        return {
            enter: function($element, configModifier) {
                const width = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])($element.parent()) * widthMultiplier;
                const direction = configModifier.direction;
                const config = createBaseConfig(configModifier);
                config.to = {
                    left: 0,
                    opacity: 1
                };
                if ("forward" === direction) {
                    config.from = {
                        left: width,
                        opacity: throughOpacity
                    };
                } else if ("backward" === direction) {
                    config.from = {
                        left: -width,
                        opacity: throughOpacity
                    };
                } else {
                    config.from = {
                        left: 0,
                        opacity: 0
                    };
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, config);
            },
            leave: function($element, configModifier) {
                const width = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])($element.parent()) * widthMultiplier;
                const direction = configModifier.direction;
                const config = createBaseConfig(configModifier);
                config.from = {
                    left: 0,
                    opacity: 1
                };
                if ("forward" === direction) {
                    config.to = {
                        left: -width,
                        opacity: throughOpacity
                    };
                } else if ("backward" === direction) {
                    config.to = {
                        left: width,
                        opacity: throughOpacity
                    };
                } else {
                    config.to = {
                        left: 0,
                        opacity: 0
                    };
                }
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, config);
            }
        };
    },
    _createOpenDoorConfig: function() {
        const that = this;
        const createBaseConfig = function(configModifier) {
            return {
                type: "css",
                extraCssClasses: "dx-opendoor-animation",
                delay: void 0 === configModifier.delay ? that.option("defaultAnimationDelay") : configModifier.delay,
                duration: void 0 === configModifier.duration ? that.option("defaultAnimationDuration") : configModifier.duration
            };
        };
        return {
            enter: function($element, configModifier) {
                const direction = configModifier.direction;
                const config = createBaseConfig(configModifier);
                config.delay = "none" === direction ? config.delay : config.duration;
                config.from = "dx-enter dx-opendoor-animation" + directionPostfixes[direction];
                config.to = "dx-enter-active";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, config);
            },
            leave: function($element, configModifier) {
                const direction = configModifier.direction;
                const config = createBaseConfig(configModifier);
                config.from = "dx-leave dx-opendoor-animation" + directionPostfixes[direction];
                config.to = "dx-leave-active";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, config);
            }
        };
    },
    _createWinPopConfig: function() {
        const that = this;
        const baseConfig = {
            type: "css",
            extraCssClasses: "dx-win-pop-animation",
            duration: that.option("defaultAnimationDuration")
        };
        return {
            enter: function($element, configModifier) {
                const config = baseConfig;
                const direction = configModifier.direction;
                config.delay = "none" === direction ? that.option("defaultAnimationDelay") : that.option("defaultAnimationDuration") / 2;
                config.from = "dx-enter dx-win-pop-animation" + directionPostfixes[direction];
                config.to = "dx-enter-active";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, config);
            },
            leave: function($element, configModifier) {
                const config = baseConfig;
                const direction = configModifier.direction;
                config.delay = that.option("defaultAnimationDelay");
                config.from = "dx-leave dx-win-pop-animation" + directionPostfixes[direction];
                config.to = "dx-leave-active";
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, config);
            }
        };
    },
    resetToDefaults: function() {
        this.clear();
        this.registerDefaultPresets();
        this.applyChanges();
    },
    clear: function(name) {
        const that = this;
        const newRegisteredPresets = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._registeredPresets, function(index, preset) {
            if (!name || name === preset.name) {
                that.option(that._getPresetOptionName(preset.name), void 0);
            } else {
                newRegisteredPresets.push(preset);
            }
        });
        this._registeredPresets = newRegisteredPresets;
        this.applyChanges();
    },
    registerPreset: function(name, config) {
        this._registeredPresets.push({
            name: name,
            config: config
        });
    },
    applyChanges: function() {
        const that = this;
        const customRules = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._registeredPresets, function(index, preset) {
            const rule = {
                device: preset.config.device,
                options: {}
            };
            rule.options[that._getPresetOptionName(preset.name)] = preset.config.animation;
            customRules.push(rule);
        });
        this._setOptionsByDevice(customRules);
    },
    getPreset: function(name) {
        let result = name;
        while("string" === typeof result){
            result = this.option(this._getPresetOptionName(result));
        }
        return result;
    },
    registerDefaultPresets: function() {
        this.registerPreset("pop", {
            animation: {
                extraCssClasses: "dx-android-pop-animation",
                delay: this.option("defaultAnimationDelay"),
                duration: this.option("defaultAnimationDuration")
            }
        });
        this.registerPreset("openDoor", {
            animation: this._createOpenDoorConfig()
        });
        this.registerPreset("win-pop", {
            animation: this._createWinPopConfig()
        });
        this.registerPreset("fade", {
            animation: {
                extraCssClasses: "dx-fade-animation",
                delay: this.option("defaultAnimationDelay"),
                duration: this.option("defaultAnimationDuration")
            }
        });
        this.registerPreset("slide", {
            device: function() {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].current().android || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real.android;
            },
            animation: this._createAndroidSlideAnimationConfig(1, 1)
        });
        this.registerPreset("slide", {
            device: function() {
                return !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].current().android && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real.android;
            },
            animation: {
                extraCssClasses: "dx-slide-animation",
                delay: this.option("defaultAnimationDelay"),
                duration: this.option("defaultAnimationDuration")
            }
        });
        this.registerPreset("ios7-slide", {
            animation: {
                extraCssClasses: "dx-ios7-slide-animation",
                delay: this.option("defaultAnimationDelay"),
                duration: this.option("defaultAnimationDuration")
            }
        });
        this.registerPreset("overflow", {
            animation: {
                extraCssClasses: "dx-overflow-animation",
                delay: this.option("defaultAnimationDelay"),
                duration: this.option("defaultAnimationDuration")
            }
        });
        this.registerPreset("ios7-toolbar", {
            device: function() {
                return !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].current().android && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real.android;
            },
            animation: {
                extraCssClasses: "dx-ios7-toolbar-animation",
                delay: this.option("defaultAnimationDelay"),
                duration: this.option("defaultAnimationDuration")
            }
        });
        this.registerPreset("ios7-toolbar", {
            device: function() {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].current().android || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real.android;
            },
            animation: this._createAndroidSlideAnimationConfig(0, .4)
        });
        this.registerPreset("stagger-fade", {
            animation: {
                extraCssClasses: "dx-fade-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-slide", {
            animation: {
                extraCssClasses: "dx-slide-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-fade-slide", {
            animation: {
                extraCssClasses: "dx-fade-slide-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-drop", {
            animation: {
                extraCssClasses: "dx-drop-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-fade-drop", {
            animation: {
                extraCssClasses: "dx-fade-drop-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-fade-rise", {
            animation: {
                extraCssClasses: "dx-fade-rise-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-3d-drop", {
            animation: {
                extraCssClasses: "dx-3d-drop-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
        this.registerPreset("stagger-fade-zoom", {
            animation: {
                extraCssClasses: "dx-fade-zoom-animation",
                staggerDelay: this.option("defaultStaggerAnimationDelay"),
                duration: this.option("defaultStaggerAnimationDuration"),
                delay: this.option("defaultStaggerAnimationStartDelay")
            }
        });
    }
});
const animationPresets = new AnimationPresetCollection;
;
}),
"[project]/node_modules/devextreme/esm/common/core/animation/presets.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/presets.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$presets$2f$presets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/presets/presets.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$presets$2f$presets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["presets"];
}),
"[project]/node_modules/devextreme/esm/common/core/animation/transition_executor/transition_executor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/transition_executor/transition_executor.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "TransitionExecutor": ()=>TransitionExecutor
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$presets$2f$presets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/presets/presets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
const directionPostfixes = {
    forward: " dx-forward",
    backward: " dx-backward",
    none: " dx-no-direction",
    undefined: " dx-no-direction"
};
const DX_ANIMATING_CLASS = "dx-animating";
const TransitionExecutor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor: function() {
        this._accumulatedDelays = {
            enter: 0,
            leave: 0
        };
        this._animations = [];
        this.reset();
    },
    _createAnimations: function($elements, initialConfig, configModifier, type) {
        $elements = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])($elements);
        const that = this;
        const result = [];
        configModifier = configModifier || {};
        const animationConfig = this._prepareElementAnimationConfig(initialConfig, configModifier, type);
        if (animationConfig) {
            $elements.each(function() {
                const animation = that._createAnimation((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this), animationConfig, configModifier);
                if (animation) {
                    animation.element.addClass("dx-animating");
                    animation.setup();
                    result.push(animation);
                }
            });
        }
        return result;
    },
    _prepareElementAnimationConfig: function(config, configModifier, type) {
        let result;
        if ("string" === typeof config) {
            const presetName = config;
            config = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$presets$2f$presets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["presets"].getPreset(presetName);
        }
        if (!config) {
            result = void 0;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(config[type])) {
            result = config[type];
        } else {
            result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                skipElementInitialStyles: true,
                cleanupWhen: this._completePromise
            }, config, configModifier);
            if (!result.type || "css" === result.type) {
                const cssClass = "dx-" + type;
                const extraCssClasses = (result.extraCssClasses ? " " + result.extraCssClasses : "") + directionPostfixes[result.direction];
                result.type = "css";
                result.from = (result.from || cssClass) + extraCssClasses;
                result.to = result.to || cssClass + "-active";
            }
            result.staggerDelay = result.staggerDelay || 0;
            result.delay = result.delay || 0;
            if (result.staggerDelay) {
                result.delay += this._accumulatedDelays[type];
                this._accumulatedDelays[type] += result.staggerDelay;
            }
        }
        return result;
    },
    _createAnimation: function($element, animationConfig, configModifier) {
        let result;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(animationConfig)) {
            result = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createAnimation($element, animationConfig);
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(animationConfig)) {
            result = animationConfig($element, configModifier);
        }
        return result;
    },
    _startAnimations: function() {
        const animations = this._animations;
        for(let i = 0; i < animations.length; i++){
            animations[i].start();
        }
    },
    _stopAnimations: function(jumpToEnd) {
        const animations = this._animations;
        for(let i = 0; i < animations.length; i++){
            animations[i].stop(jumpToEnd);
        }
    },
    _clearAnimations: function() {
        const animations = this._animations;
        for(let i = 0; i < animations.length; i++){
            animations[i].element.removeClass("dx-animating");
        }
        this._animations.length = 0;
    },
    reset: function() {
        this._accumulatedDelays.enter = 0;
        this._accumulatedDelays.leave = 0;
        this._clearAnimations();
        this._completeDeferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        this._completePromise = this._completeDeferred.promise();
    },
    enter: function($elements, animationConfig, configModifier) {
        const animations = this._createAnimations($elements, animationConfig, configModifier, "enter");
        this._animations.push.apply(this._animations, animations);
    },
    leave: function($elements, animationConfig, configModifier) {
        const animations = this._createAnimations($elements, animationConfig, configModifier, "leave");
        this._animations.push.apply(this._animations, animations);
    },
    start: function() {
        const that = this;
        let result;
        if (!this._animations.length) {
            that.reset();
            result = (new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"]).resolve().promise();
        } else {
            const animationDeferreds = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(this._animations, function(animation) {
                const result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
                animation.deferred.always(function() {
                    result.resolve();
                });
                return result.promise();
            });
            result = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"].apply(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], animationDeferreds).always(function() {
                that._completeDeferred.resolve();
                that.reset();
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].executeAsync(function() {
                that._startAnimations();
            });
        }
        return result;
    },
    stop: function(jumpToEnd) {
        this._stopAnimations(jumpToEnd);
    }
});
}),
"[project]/node_modules/devextreme/esm/common/core/animation/transition_executor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation/transition_executor.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$transition_executor$2f$transition_executor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/transition_executor/transition_executor.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$transition_executor$2f$transition_executor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TransitionExecutor"];
}),
"[project]/node_modules/devextreme/esm/common/core/animation.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/animation.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$presets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/presets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$transition_executor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/transition_executor.js [app-client] (ecmascript)");
;
;
;
;
;
}),
"[project]/node_modules/devextreme/esm/common/core/animation.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$presets$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/presets.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$transition_executor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/transition_executor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/emitter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/wheel.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/wheel.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_wheel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_wheel.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/wheel.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_wheel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_wheel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$wheel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/wheel.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/base.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/pointer/base.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_base.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/base.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/base.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/observer.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/pointer/observer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_observer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_observer.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/observer.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_observer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_observer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$observer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/observer.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/pointer/mouse.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/touch.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/pointer/touch.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_touch.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/touch.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_touch.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/touch.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse_and_touch.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/pointer/mouse_and_touch.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse_and_touch.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse_and_touch.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse_and_touch.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse_and_touch.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/pointer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/emitter_registrator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/utils/event_nodes_disposing.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/utils/event_nodes_disposing.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_nodes_disposing.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/utils/event_nodes_disposing.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_nodes_disposing.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/event_nodes_disposing.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/click.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/click.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_click.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/click.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_click.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/click.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/double_click.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/double_click.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_dblclick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_dblclick.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
;
;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_dblclick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["name"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_dblclick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dblClick"]);
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/double_click.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_dblclick$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_dblclick.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$double_click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/double_click.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/errors.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/errors.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_errors.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/errors.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/errors.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/utils.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_utils.js [app-client] (ecmascript)");
;
;
;
}),
"[project]/node_modules/devextreme/esm/common/data/utils.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/utils.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/array_utils.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/array_utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_utils.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/array_utils.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$array_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/array_utils.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/array_query.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/array_query.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_query.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/array_query.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_query.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$array_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/array_query.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/store_helper.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/store_helper.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_store_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_store_helper.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/store_helper.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_store_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_store_helper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$store_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/store_helper.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/abstract_store.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/abstract_store.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_abstract_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_abstract_store.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/abstract_store.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_abstract_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_abstract_store.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$abstract_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/abstract_store.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/custom_store.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/custom_store.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "isGroupItemsArray": ()=>isGroupItemsArray,
    "isItemsArray": ()=>isItemsArray,
    "isLoadResultObject": ()=>isLoadResultObject
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_custom_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_custom_store.js [app-client] (ecmascript)");
;
function isGroupItem(item) {
    if (void 0 === item || null === item || "object" !== typeof item) {
        return false;
    }
    return "key" in item && "items" in item;
}
function isLoadResultObject(res) {
    return !Array.isArray(res) && "data" in res;
}
function isGroupItemsArray(res) {
    return Array.isArray(res) && !!res.length && isGroupItem(res[0]);
}
function isItemsArray(res) {
    return Array.isArray(res) && !isGroupItem(res[0]);
}
;
}),
"[project]/node_modules/devextreme/esm/common/data/custom_store.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_custom_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_custom_store.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$custom_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/custom_store.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/query_adapters.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/query_adapters.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {};
}),
"[project]/node_modules/devextreme/esm/common/data/remote_query.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/remote_query.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_remote_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_remote_query.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/remote_query.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_remote_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_remote_query.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$remote_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/remote_query.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/query_implementation.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/query_implementation.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "queryImpl": ()=>queryImpl
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$array_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/array_query.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_query.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$remote_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/remote_query.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_remote_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_remote_query.js [app-client] (ecmascript)");
;
;
const queryImpl = {
    array: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
    remote: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_remote_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
};
}),
"[project]/node_modules/devextreme/esm/common/data/query.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/query.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_query.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/query.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_query.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$query$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/query.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/array_store.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/array_store.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_store.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/array_store.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_array_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_array_store.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$array_store$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/array_store.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/data_source/utils.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/data_source/utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_utils.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/data_source/utils.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$data_source$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/data_source/utils.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/data_source/operation_manager.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/data_source/operation_manager.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_operation_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_operation_manager.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/data_source/operation_manager.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_operation_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_operation_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$data_source$2f$operation_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/data_source/operation_manager.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/data_source/data_source.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/data_source/data_source.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_data_source.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/data/data_source/data_source.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_data_source.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$data_source$2f$data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/data_source/data_source.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/data/data_source.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/data/data_source.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$data_source$2f$data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/data_source/data_source.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_data_source.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DataSource"];
}),
"[project]/node_modules/devextreme/esm/common/core/localization/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "toFixed": ()=>toFixed
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
;
const DECIMAL_BASE = 10;
function roundByAbs(value) {
    const valueSign = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sign"])(value);
    return valueSign * Math.round(Math.abs(value));
}
function adjustValue(value, precision) {
    const precisionMultiplier = Math.pow(10, precision);
    const intermediateValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["multiplyInExponentialForm"])(value, precision);
    return roundByAbs(intermediateValue) / precisionMultiplier;
}
function toFixed(value, precision) {
    const valuePrecision = precision || 0;
    const adjustedValue = valuePrecision > 0 ? adjustValue(...arguments) : value;
    return adjustedValue.toFixed(valuePrecision);
}
}),
"[project]/node_modules/devextreme/esm/common/core/localization/ldml/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/ldml/number.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getFormat": ()=>getFormat,
    "getFormatter": ()=>getFormatter
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/utils.js [app-client] (ecmascript)");
;
;
const DEFAULT_CONFIG = {
    thousandsSeparator: ",",
    decimalSeparator: "."
};
const ESCAPING_CHAR = "'";
const MAXIMUM_NUMBER_LENGTH = 15;
const PERCENT_EXPONENT_SHIFT = 2;
function getGroupSizes(formatString) {
    return formatString.split(",").slice(1).map(function(str) {
        let singleQuotesLeft = 0;
        return str.split("").filter(function(char, index) {
            singleQuotesLeft += "'" === char;
            const isDigit = "#" === char || "0" === char;
            const isInStub = singleQuotesLeft % 2;
            return isDigit && !isInStub;
        }).length;
    });
}
function splitSignParts(format) {
    let separatorChar = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : ";";
    let escapingChar = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "'";
    const parts = [];
    let currentPart = "";
    let state = "searchingSeparator";
    for(let i = 0; i < format.length; i++){
        const char = format[i];
        if ("searchingSeparator" === state && char === escapingChar) {
            state = "skippingSeparationInsideEscaping";
        } else if ("skippingSeparationInsideEscaping" === state && char === escapingChar) {
            state = "searchingSeparator";
        } else if ("searchingSeparator" === state && char === separatorChar) {
            state = "separating";
            parts.push(currentPart);
            currentPart = "";
        }
        if ("separating" !== state) {
            currentPart += char;
        } else {
            state = "searchingSeparator";
        }
    }
    parts.push(currentPart);
    return parts;
}
function getSignParts(format) {
    const signParts = splitSignParts(format);
    if (1 === signParts.length) {
        signParts.push("-" + signParts[0]);
    }
    return signParts;
}
function reverseString(str) {
    return str.toString().split("").reverse().join("");
}
function isPercentFormat(format) {
    return -1 !== format.indexOf("%") && !format.match(/'[^']*%[^']*'/g);
}
function removeStubs(str) {
    return str.replace(/'[^']*'/g, "");
}
function getNonRequiredDigitCount(floatFormat) {
    if (!floatFormat) {
        return 0;
    }
    const format = removeStubs(floatFormat);
    return format.length - format.replace(/[#]/g, "").length;
}
function getRequiredDigitCount(floatFormat) {
    if (!floatFormat) {
        return 0;
    }
    const format = removeStubs(floatFormat);
    return format.length - format.replace(/[0]/g, "").length;
}
function normalizeValueString(valuePart, minDigitCount, maxDigitCount) {
    if (!valuePart) {
        return "";
    }
    if (valuePart.length > maxDigitCount) {
        valuePart = valuePart.substr(0, maxDigitCount);
    }
    while(valuePart.length > minDigitCount && "0" === valuePart.slice(-1)){
        valuePart = valuePart.substr(0, valuePart.length - 1);
    }
    while(valuePart.length < minDigitCount){
        valuePart += "0";
    }
    return valuePart;
}
function applyGroups(valueString, groupSizes, thousandsSeparator) {
    if (!groupSizes.length) {
        return valueString;
    }
    const groups = [];
    let index = 0;
    while(valueString){
        const groupSize = groupSizes[index];
        if (!groupSize) {
            break;
        }
        groups.push(valueString.slice(0, groupSize));
        valueString = valueString.slice(groupSize);
        if (index < groupSizes.length - 1) {
            index++;
        }
    }
    return groups.join(thousandsSeparator);
}
function formatNumberPart(format, valueString) {
    return format.split("'").map(function(formatPart, escapeIndex) {
        const isEscape = escapeIndex % 2;
        if (!formatPart && isEscape) {
            return "'";
        }
        return isEscape ? formatPart : formatPart.replace(/[,#0]+/, valueString);
    }).join("");
}
function getFloatPointIndex(format) {
    let isEscape = false;
    for(let index = 0; index < format.length; index++){
        if ("'" === format[index]) {
            isEscape = !isEscape;
        }
        if ("." === format[index] && !isEscape) {
            return index;
        }
    }
    return format.length;
}
function getFormatter(format, config) {
    config = config || DEFAULT_CONFIG;
    return function(value) {
        if ("number" !== typeof value || isNaN(value)) {
            return "";
        }
        const signFormatParts = getSignParts(format);
        const isPositiveZero = 1 / value === 1 / 0;
        const isPositive = value > 0 || isPositiveZero;
        const numberFormat = signFormatParts[isPositive ? 0 : 1];
        const floatPointIndex = getFloatPointIndex(numberFormat);
        const floatFormatParts = [
            numberFormat.substr(0, floatPointIndex),
            numberFormat.substr(floatPointIndex + 1)
        ];
        const minFloatPrecision = getRequiredDigitCount(floatFormatParts[1]);
        const maxFloatPrecision = minFloatPrecision + getNonRequiredDigitCount(floatFormatParts[1]);
        if (isPercentFormat(numberFormat)) {
            value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["multiplyInExponentialForm"])(value, 2);
        }
        if (!isPositive) {
            value = -value;
        }
        const minIntegerPrecision = getRequiredDigitCount(floatFormatParts[0]);
        const maxIntegerPrecision = getNonRequiredDigitCount(floatFormatParts[0]) || config.unlimitedIntegerDigits ? void 0 : minIntegerPrecision;
        const integerLength = Math.floor(value).toString().length;
        const floatPrecision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fitIntoRange"])(maxFloatPrecision, 0, 15 - integerLength);
        const groupSizes = getGroupSizes(floatFormatParts[0]).reverse();
        const valueParts = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toFixed"])(value, floatPrecision < 0 ? 0 : floatPrecision).split(".");
        let valueIntegerPart = normalizeValueString(reverseString(valueParts[0]), minIntegerPrecision, maxIntegerPrecision);
        const valueFloatPart = normalizeValueString(valueParts[1], minFloatPrecision, maxFloatPrecision);
        valueIntegerPart = applyGroups(valueIntegerPart, groupSizes, config.thousandsSeparator);
        const integerString = reverseString(formatNumberPart(reverseString(floatFormatParts[0]), valueIntegerPart));
        const floatString = maxFloatPrecision ? formatNumberPart(floatFormatParts[1], valueFloatPart) : "";
        const result = integerString + (floatString.match(/\d/) ? config.decimalSeparator : "") + floatString;
        return result;
    };
}
function parseValue(text, isPercent, isNegative) {
    const value = (isPercent ? .01 : 1) * parseFloat(text) || 0;
    return isNegative ? -value : value;
}
function prepareValueText(valueText, formatter, isPercent, isIntegerPart) {
    let nextValueText = valueText;
    let char;
    let text;
    let nextText;
    do {
        if (nextText) {
            char = text.length === nextText.length ? "0" : "1";
            valueText = isIntegerPart ? char + valueText : valueText + char;
        }
        text = nextText || formatter(parseValue(nextValueText, isPercent));
        nextValueText = isIntegerPart ? "1" + nextValueText : nextValueText + "1";
        nextText = formatter(parseValue(nextValueText, isPercent));
    }while (text !== nextText && (isIntegerPart ? text.length === nextText.length : text.length <= nextText.length))
    if (isIntegerPart && nextText.length > text.length) {
        const hasGroups = -1 === formatter(12345).indexOf("12345");
        do {
            valueText = "1" + valueText;
        }while (hasGroups && parseValue(valueText, isPercent) < 1e5)
    }
    return valueText;
}
function getFormatByValueText(valueText, formatter, isPercent, isNegative) {
    let format = formatter(parseValue(valueText, isPercent, isNegative));
    const valueTextParts = valueText.split(".");
    const valueTextWithModifiedFloat = valueTextParts[0] + ".3" + valueTextParts[1].slice(1);
    const valueWithModifiedFloat = parseValue(valueTextWithModifiedFloat, isPercent, isNegative);
    const decimalSeparatorIndex = formatter(valueWithModifiedFloat).indexOf("3") - 1;
    format = format.replace(/(\d)\D(\d)/g, "$1,$2");
    if (decimalSeparatorIndex >= 0) {
        format = format.slice(0, decimalSeparatorIndex) + "." + format.slice(decimalSeparatorIndex + 1);
    }
    format = format.replace(/1+/, "1").replace(/1/g, "#");
    if (!isPercent) {
        format = format.replace(/%/g, "'%'");
    }
    return format;
}
function getFormat(formatter) {
    let valueText = ".";
    const isPercent = formatter(1).indexOf("100") >= 0;
    valueText = prepareValueText(valueText, formatter, isPercent, true);
    valueText = prepareValueText(valueText, formatter, isPercent, false);
    const positiveFormat = getFormatByValueText(valueText, formatter, isPercent, false);
    const negativeFormat = getFormatByValueText(valueText, formatter, isPercent, true);
    return negativeFormat === "-" + positiveFormat ? positiveFormat : positiveFormat + ";" + negativeFormat;
}
}),
"[project]/node_modules/devextreme/esm/common/core/localization/currency.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/currency.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    _formatNumberCore: function(value, format, formatConfig) {
        if ("currency" === format) {
            formatConfig.precision = formatConfig.precision || 0;
            let result = this.format(value, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, formatConfig, {
                type: "fixedpoint"
            }));
            const currencyPart = this.getCurrencySymbol().symbol.replace(/\$/g, "$$$$");
            result = result.replace(/^(\D*)(\d.*)/, "$1" + currencyPart + "$2");
            return result;
        }
        return this.callBase.apply(this, arguments);
    },
    getCurrencySymbol: function() {
        return {
            symbol: "$"
        };
    },
    getOpenXmlCurrencyFormat: function() {
        return "$#,##0{0}_);\\($#,##0{0}\\)";
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/open_xml_currency_format.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/open_xml_currency_format.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = (currencySymbol, accountingFormat)=>{
    if (!accountingFormat) {
        return;
    }
    let encodedCurrencySymbol = currencySymbol;
    if ("string" === typeof currencySymbol) {
        encodedCurrencySymbol = "";
        for(let i = 0; i < currencySymbol.length; i++){
            if ("$" !== currencySymbol[i]) {
                encodedCurrencySymbol += "\\";
            }
            encodedCurrencySymbol += currencySymbol[i];
        }
    }
    const encodeSymbols = {
        ".00": "{0}",
        "'": "\\'",
        "\\(": "\\(",
        "\\)": "\\)",
        " ": "\\ ",
        '"': "&quot;",
        "\\\xa4": encodedCurrencySymbol
    };
    const result = accountingFormat.split(";");
    for(let i = 0; i < result.length; i++){
        for(const symbol in encodeSymbols){
            if (Object.prototype.hasOwnProperty.call(encodeSymbols, symbol)) {
                result[i] = result[i].replace(new RegExp(symbol, "g"), encodeSymbols[symbol]);
            }
        }
    }
    return 2 === result.length ? result[0] + "_);" + result[1] : result[0];
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/cldr-data/accounting_formats.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/cldr-data/accounting_formats.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ // !!! AUTO-GENERATED FILE, DO NOT EDIT
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {
    af: "\xa4#,##0.00;(\xa4#,##0.00)",
    "af-NA": "\xa4#,##0.00;(\xa4#,##0.00)",
    agq: "#,##0.00\xa4",
    ak: "\xa4#,##0.00",
    am: "\xa4#,##0.00;(\xa4#,##0.00)",
    ar: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-AE": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-BH": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-DJ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-DZ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-EG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-EH": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-ER": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-IL": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-IQ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-JO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-KM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-KW": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-LB": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-LY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-MA": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-MR": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-OM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-PS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-QA": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-SA": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-SD": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-SO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-SS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-SY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-TD": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-TN": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ar-YE": "\xa4#,##0.00;(\xa4#,##0.00)",
    as: "\xa4\xa0#,##,##0.00",
    asa: "#,##0.00\xa0\xa4",
    ast: "#,##0.00\xa0\xa4",
    az: "#,##0.00\xa0\xa4",
    "az-Cyrl": "#,##0.00\xa0\xa4",
    "az-Latn": "#,##0.00\xa0\xa4",
    bas: "#,##0.00\xa0\xa4",
    be: "#,##0.00\xa0\xa4",
    "be-tarask": "#,##0.00\xa0\xa4",
    bem: "\xa4#,##0.00;(\xa4#,##0.00)",
    bez: "#,##0.00\xa4",
    bg: "0.00\xa0\xa4;(0.00\xa0\xa4)",
    bm: "\xa4#,##0.00;(\xa4#,##0.00)",
    bn: "#,##,##0.00\xa4;(#,##,##0.00\xa4)",
    "bn-IN": "#,##,##0.00\xa4;(#,##,##0.00\xa4)",
    bo: "\xa4\xa0#,##0.00",
    "bo-IN": "\xa4\xa0#,##0.00",
    br: "#,##0.00\xa0\xa4",
    brx: "\xa4\xa0#,##,##0.00",
    bs: "#,##0.00\xa0\xa4",
    "bs-Cyrl": "#,##0.00\xa0\xa4",
    "bs-Latn": "#,##0.00\xa0\xa4",
    ca: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "ca-AD": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "ca-ES-valencia": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "ca-FR": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "ca-IT": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    ccp: "#,##,##0.00\xa4;(#,##,##0.00\xa4)",
    "ccp-IN": "#,##,##0.00\xa4;(#,##,##0.00\xa4)",
    ce: "#,##0.00\xa0\xa4",
    ceb: "\xa4#,##0.00;(\xa4#,##0.00)",
    cgg: "\xa4#,##0.00",
    chr: "\xa4#,##0.00;(\xa4#,##0.00)",
    ckb: "\xa4\xa0#,##0.00",
    "ckb-IR": "\xa4\xa0#,##0.00",
    cs: "#,##0.00\xa0\xa4",
    cy: "\xa4#,##0.00;(\xa4#,##0.00)",
    da: "#,##0.00\xa0\xa4",
    "da-GL": "#,##0.00\xa0\xa4",
    dav: "\xa4#,##0.00;(\xa4#,##0.00)",
    de: "#,##0.00\xa0\xa4",
    "de-AT": "#,##0.00\xa0\xa4",
    "de-BE": "#,##0.00\xa0\xa4",
    "de-CH": "#,##0.00\xa0\xa4",
    "de-IT": "#,##0.00\xa0\xa4",
    "de-LI": "#,##0.00\xa0\xa4",
    "de-LU": "#,##0.00\xa0\xa4",
    dje: "#,##0.00\xa4",
    doi: "\xa4#,##0.00",
    dsb: "#,##0.00\xa0\xa4",
    dua: "#,##0.00\xa0\xa4",
    dyo: "#,##0.00\xa0\xa4",
    dz: "\xa4#,##,##0.00",
    ebu: "\xa4#,##0.00;(\xa4#,##0.00)",
    ee: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ee-TG": "\xa4#,##0.00;(\xa4#,##0.00)",
    el: "#,##0.00\xa0\xa4",
    "el-CY": "#,##0.00\xa0\xa4",
    en: "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-001": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-150": "#,##0.00\xa0\xa4",
    "en-AE": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-AG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-AI": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-AS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-AT": "\xa4\xa0#,##0.00",
    "en-AU": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-BB": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-BE": "#,##0.00\xa0\xa4",
    "en-BI": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-BM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-BS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-BW": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-BZ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-CA": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-CC": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-CH": "\xa4\xa0#,##0.00;\xa4-#,##0.00",
    "en-CK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-CM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-CX": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-CY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-DE": "#,##0.00\xa0\xa4",
    "en-DG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-DK": "#,##0.00\xa0\xa4",
    "en-DM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-ER": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-FI": "#,##0.00\xa0\xa4",
    "en-FJ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-FK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-FM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GB": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GD": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GH": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GI": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GU": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-GY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-HK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-IE": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-IL": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-IM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-IN": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-IO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-JE": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-JM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-KE": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-KI": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-KN": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-KY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-LC": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-LR": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-LS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MH": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MP": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MT": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MU": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MV": "\xa4\xa0#,##0.00",
    "en-MW": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-MY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-NA": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-NF": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-NG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-NL": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "en-NR": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-NU": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-NZ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-PG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-PH": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-PK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-PN": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-PR": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-PW": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-RW": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SB": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SC": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SD": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SE": "#,##0.00\xa0\xa4",
    "en-SG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SH": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SI": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "en-SL": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SX": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-SZ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-TC": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-TK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-TO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-TT": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-TV": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-TZ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-UG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-UM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-VC": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-VG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-VI": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-VU": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-WS": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-ZA": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-ZM": "\xa4#,##0.00;(\xa4#,##0.00)",
    "en-ZW": "\xa4#,##0.00;(\xa4#,##0.00)",
    eo: "\xa4\xa0#,##0.00",
    es: "#,##0.00\xa0\xa4",
    "es-419": "\xa4#,##0.00",
    "es-AR": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "es-BO": "\xa4#,##0.00",
    "es-BR": "\xa4#,##0.00",
    "es-BZ": "\xa4#,##0.00",
    "es-CL": "\xa4#,##0.00",
    "es-CO": "\xa4#,##0.00",
    "es-CR": "\xa4#,##0.00",
    "es-CU": "\xa4#,##0.00",
    "es-DO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "es-EA": "#,##0.00\xa0\xa4",
    "es-EC": "\xa4#,##0.00",
    "es-GQ": "#,##0.00\xa0\xa4",
    "es-GT": "\xa4#,##0.00",
    "es-HN": "\xa4#,##0.00",
    "es-IC": "#,##0.00\xa0\xa4",
    "es-MX": "\xa4#,##0.00",
    "es-NI": "\xa4#,##0.00",
    "es-PA": "\xa4#,##0.00",
    "es-PE": "\xa4#,##0.00",
    "es-PH": "#,##0.00\xa0\xa4",
    "es-PR": "\xa4#,##0.00",
    "es-PY": "\xa4#,##0.00",
    "es-SV": "\xa4#,##0.00",
    "es-US": "\xa4#,##0.00",
    "es-UY": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "es-VE": "\xa4#,##0.00",
    et: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    eu: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    ewo: "#,##0.00\xa0\xa4",
    fa: "\u200e\xa4\xa0#,##0.00;\u200e(\xa4\xa0#,##0.00)",
    "fa-AF": "\xa4\xa0#,##0.00;\u200e(\xa4\xa0#,##0.00)",
    ff: "#,##0.00\xa0\xa4",
    "ff-Adlm": "\xa4\xa0#,##0.00",
    "ff-Adlm-BF": "\xa4\xa0#,##0.00",
    "ff-Adlm-CM": "\xa4\xa0#,##0.00",
    "ff-Adlm-GH": "\xa4\xa0#,##0.00",
    "ff-Adlm-GM": "\xa4\xa0#,##0.00",
    "ff-Adlm-GW": "\xa4\xa0#,##0.00",
    "ff-Adlm-LR": "\xa4\xa0#,##0.00",
    "ff-Adlm-MR": "\xa4\xa0#,##0.00",
    "ff-Adlm-NE": "\xa4\xa0#,##0.00",
    "ff-Adlm-NG": "\xa4\xa0#,##0.00",
    "ff-Adlm-SL": "\xa4\xa0#,##0.00",
    "ff-Adlm-SN": "\xa4\xa0#,##0.00",
    "ff-Latn": "#,##0.00\xa0\xa4",
    "ff-Latn-BF": "#,##0.00\xa0\xa4",
    "ff-Latn-CM": "#,##0.00\xa0\xa4",
    "ff-Latn-GH": "#,##0.00\xa0\xa4",
    "ff-Latn-GM": "#,##0.00\xa0\xa4",
    "ff-Latn-GN": "#,##0.00\xa0\xa4",
    "ff-Latn-GW": "#,##0.00\xa0\xa4",
    "ff-Latn-LR": "#,##0.00\xa0\xa4",
    "ff-Latn-MR": "#,##0.00\xa0\xa4",
    "ff-Latn-NE": "#,##0.00\xa0\xa4",
    "ff-Latn-NG": "#,##0.00\xa0\xa4",
    "ff-Latn-SL": "#,##0.00\xa0\xa4",
    fi: "#,##0.00\xa0\xa4",
    fil: "\xa4#,##0.00;(\xa4#,##0.00)",
    fo: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fo-DK": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    fr: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-BE": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-BF": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-BI": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-BJ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-BL": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CA": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CD": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CF": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CG": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CH": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CI": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-CM": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-DJ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-DZ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-GA": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-GF": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-GN": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-GP": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-GQ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-HT": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-KM": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-LU": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MA": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MC": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MF": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MG": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-ML": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MQ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MR": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-MU": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-NC": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-NE": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-PF": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-PM": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-RE": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-RW": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-SC": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-SN": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-SY": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-TD": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-TG": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-TN": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-VU": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-WF": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "fr-YT": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    fur: "\xa4\xa0#,##0.00",
    fy: "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    ga: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ga-GB": "\xa4#,##0.00;(\xa4#,##0.00)",
    gd: "\xa4#,##0.00;(\xa4#,##0.00)",
    gl: "#,##0.00\xa0\xa4",
    gsw: "#,##0.00\xa0\xa4",
    "gsw-FR": "#,##0.00\xa0\xa4",
    "gsw-LI": "#,##0.00\xa0\xa4",
    gu: "\xa4#,##,##0.00;(\xa4#,##,##0.00)",
    guz: "\xa4#,##0.00;(\xa4#,##0.00)",
    gv: "\xa4#,##0.00",
    ha: "\xa4\xa0#,##0.00",
    "ha-GH": "\xa4\xa0#,##0.00",
    "ha-NE": "\xa4\xa0#,##0.00",
    haw: "\xa4#,##0.00;(\xa4#,##0.00)",
    he: "#,##0.00\xa0\xa4",
    hi: "\xa4#,##,##0.00",
    "hi-Latn": "\xa4#,##,##0.00",
    hr: "#,##0.00\xa0\xa4",
    "hr-BA": "#,##0.00\xa0\xa4",
    hsb: "#,##0.00\xa0\xa4",
    hu: "#,##0.00\xa0\xa4",
    hy: "#,##0.00\xa0\xa4",
    ia: "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    id: "\xa4#,##0.00",
    ig: "\xa4#,##0.00;(\xa4#,##0.00)",
    ii: "\xa4\xa0#,##0.00",
    is: "#,##0.00\xa0\xa4",
    it: "#,##0.00\xa0\xa4",
    "it-CH": "#,##0.00\xa0\xa4",
    "it-SM": "#,##0.00\xa0\xa4",
    "it-VA": "#,##0.00\xa0\xa4",
    ja: "\xa4#,##0.00;(\xa4#,##0.00)",
    jgo: "\xa4\xa0#,##0.00",
    jmc: "\xa4#,##0.00",
    jv: "\xa4\xa0#,##0.00",
    ka: "#,##0.00\xa0\xa4",
    kab: "#,##0.00\xa4",
    kam: "\xa4#,##0.00;(\xa4#,##0.00)",
    kde: "\xa4#,##0.00;(\xa4#,##0.00)",
    kea: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    kgp: "\xa4\xa0#,##0.00",
    khq: "#,##0.00\xa4",
    ki: "\xa4#,##0.00;(\xa4#,##0.00)",
    kk: "#,##0.00\xa0\xa4",
    kkj: "\xa4\xa0#,##0.00",
    kl: "\xa4#,##0.00;\xa4-#,##0.00",
    kln: "\xa4#,##0.00;(\xa4#,##0.00)",
    km: "#,##0.00\xa4;(#,##0.00\xa4)",
    kn: "\xa4#,##0.00;(\xa4#,##0.00)",
    ko: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ko-KP": "\xa4#,##0.00;(\xa4#,##0.00)",
    kok: "\xa4#,##0.00;(\xa4#,##0.00)",
    ks: "\xa4#,##0.00",
    "ks-Arab": "\xa4#,##0.00",
    "ks-Deva": "\xa4\xa0#,##0.00",
    ksb: "#,##0.00\xa4",
    ksf: "#,##0.00\xa0\xa4",
    ksh: "#,##0.00\xa0\xa4",
    ku: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    kw: "\xa4#,##0.00",
    ky: "#,##0.00\xa0\xa4",
    lag: "\xa4\xa0#,##0.00",
    lb: "#,##0.00\xa0\xa4",
    lg: "#,##0.00\xa4",
    lkt: "\xa4\xa0#,##0.00",
    ln: "#,##0.00\xa0\xa4",
    "ln-AO": "#,##0.00\xa0\xa4",
    "ln-CF": "#,##0.00\xa0\xa4",
    "ln-CG": "#,##0.00\xa0\xa4",
    lo: "\xa4#,##0.00;\xa4-#,##0.00",
    lrc: "\xa4\xa0#,##0.00",
    "lrc-IQ": "\xa4\xa0#,##0.00",
    lt: "#,##0.00\xa0\xa4",
    lu: "#,##0.00\xa4",
    luo: "#,##0.00\xa4",
    luy: "\xa4#,##0.00;\xa4-\xa0#,##0.00",
    lv: "#,##0.00\xa0\xa4",
    mai: "\xa4\xa0#,##0.00",
    mas: "\xa4#,##0.00;(\xa4#,##0.00)",
    "mas-TZ": "\xa4#,##0.00;(\xa4#,##0.00)",
    mer: "\xa4#,##0.00;(\xa4#,##0.00)",
    mfe: "\xa4\xa0#,##0.00",
    mg: "\xa4#,##0.00",
    mgh: "\xa4\xa0#,##0.00",
    mgo: "\xa4\xa0#,##0.00",
    mi: "\xa4\xa0#,##0.00",
    mk: "#,##0.00\xa0\xa4",
    ml: "\xa4#,##0.00;(\xa4#,##0.00)",
    mn: "\xa4\xa0#,##0.00",
    mni: "\xa4\xa0#,##0.00",
    "mni-Beng": "\xa4\xa0#,##0.00",
    mr: "\xa4#,##0.00;(\xa4#,##0.00)",
    ms: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ms-BN": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ms-ID": "\xa4#,##0.00",
    "ms-SG": "\xa4#,##0.00;(\xa4#,##0.00)",
    mt: "\xa4#,##0.00",
    mua: "\xa4#,##0.00;(\xa4#,##0.00)",
    my: "\xa4\xa0#,##0.00",
    mzn: "\xa4\xa0#,##0.00",
    naq: "\xa4#,##0.00",
    nb: "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nb-SJ": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    nd: "\xa4#,##0.00;(\xa4#,##0.00)",
    nds: "\xa4\xa0#,##0.00",
    "nds-NL": "\xa4\xa0#,##0.00",
    ne: "\xa4\xa0#,##,##0.00",
    "ne-IN": "\xa4\xa0#,##,##0.00",
    nl: "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nl-AW": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nl-BE": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nl-BQ": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nl-CW": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nl-SR": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    "nl-SX": "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    nmg: "#,##0.00\xa0\xa4",
    nn: "#,##0.00\xa0\xa4",
    nnh: "\xa4\xa0#,##0.00",
    no: "\xa4\xa0#,##0.00;(\xa4\xa0#,##0.00)",
    nus: "\xa4#,##0.00;(\xa4#,##0.00)",
    nyn: "\xa4#,##0.00",
    om: "\xa4#,##0.00",
    "om-KE": "\xa4#,##0.00",
    or: "\xa4#,##0.00;(\xa4#,##0.00)",
    os: "\xa4\xa0#,##0.00",
    "os-RU": "\xa4\xa0#,##0.00",
    pa: "\xa4\xa0#,##0.00",
    "pa-Arab": "\xa4\xa0#,##0.00",
    "pa-Guru": "\xa4\xa0#,##0.00",
    pcm: "\xa4#,##0.00",
    pl: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    ps: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ps-PK": "\xa4#,##0.00;(\xa4#,##0.00)",
    pt: "\xa4\xa0#,##0.00",
    "pt-AO": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-CH": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-CV": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-GQ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-GW": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-LU": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-MO": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-MZ": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-PT": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-ST": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "pt-TL": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    qu: "\xa4\xa0#,##0.00",
    "qu-BO": "\xa4\xa0#,##0.00",
    "qu-EC": "\xa4\xa0#,##0.00",
    rm: "#,##0.00\xa0\xa4",
    rn: "#,##0.00\xa4",
    ro: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "ro-MD": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    rof: "\xa4#,##0.00",
    ru: "#,##0.00\xa0\xa4",
    "ru-BY": "#,##0.00\xa0\xa4",
    "ru-KG": "#,##0.00\xa0\xa4",
    "ru-KZ": "#,##0.00\xa0\xa4",
    "ru-MD": "#,##0.00\xa0\xa4",
    "ru-UA": "#,##0.00\xa0\xa4",
    rw: "\xa4\xa0#,##0.00",
    rwk: "#,##0.00\xa4",
    sa: "\xa4\xa0#,##0.00",
    sah: "#,##0.00\xa0\xa4",
    saq: "\xa4#,##0.00;(\xa4#,##0.00)",
    sat: "\xa4\xa0#,##0.00",
    "sat-Olck": "\xa4\xa0#,##0.00",
    sbp: "#,##0.00\xa4",
    sc: "#,##0.00\xa0\xa4",
    sd: "\xa4\xa0#,##0.00",
    "sd-Arab": "\xa4\xa0#,##0.00",
    "sd-Deva": "\xa4\xa0#,##0.00",
    se: "#,##0.00\xa0\xa4",
    "se-FI": "#,##0.00\xa0\xa4",
    "se-SE": "#,##0.00\xa0\xa4",
    seh: "#,##0.00\xa4",
    ses: "#,##0.00\xa4",
    sg: "\xa4#,##0.00;\xa4-#,##0.00",
    shi: "#,##0.00\xa4",
    "shi-Latn": "#,##0.00\xa4",
    "shi-Tfng": "#,##0.00\xa4",
    si: "\xa4#,##0.00;(\xa4#,##0.00)",
    sk: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    sl: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    smn: "#,##0.00\xa0\xa4",
    sn: "\xa4#,##0.00;(\xa4#,##0.00)",
    so: "\xa4#,##0.00;(\xa4#,##0.00)",
    "so-DJ": "\xa4#,##0.00;(\xa4#,##0.00)",
    "so-ET": "\xa4#,##0.00;(\xa4#,##0.00)",
    "so-KE": "\xa4#,##0.00;(\xa4#,##0.00)",
    sq: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sq-MK": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sq-XK": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    sr: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Cyrl": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Cyrl-BA": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Cyrl-ME": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Cyrl-XK": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Latn": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Latn-BA": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Latn-ME": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    "sr-Latn-XK": "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    su: "\xa4#,##0.00",
    "su-Latn": "\xa4#,##0.00",
    sv: "#,##0.00\xa0\xa4",
    "sv-AX": "#,##0.00\xa0\xa4",
    "sv-FI": "#,##0.00\xa0\xa4",
    sw: "\xa4\xa0#,##0.00",
    "sw-CD": "\xa4\xa0#,##0.00",
    "sw-KE": "\xa4\xa0#,##0.00",
    "sw-UG": "\xa4\xa0#,##0.00",
    ta: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ta-LK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ta-MY": "\xa4#,##0.00;(\xa4#,##0.00)",
    "ta-SG": "\xa4#,##0.00;(\xa4#,##0.00)",
    te: "\xa4#,##0.00;(\xa4#,##0.00)",
    teo: "\xa4#,##0.00;(\xa4#,##0.00)",
    "teo-KE": "\xa4#,##0.00;(\xa4#,##0.00)",
    tg: "#,##0.00\xa0\xa4",
    th: "\xa4#,##0.00;(\xa4#,##0.00)",
    ti: "\xa4#,##0.00",
    "ti-ER": "\xa4#,##0.00",
    tk: "#,##0.00\xa0\xa4",
    to: "\xa4\xa0#,##0.00",
    tr: "\xa4#,##0.00;(\xa4#,##0.00)",
    "tr-CY": "\xa4#,##0.00;(\xa4#,##0.00)",
    tt: "#,##0.00\xa0\xa4",
    twq: "#,##0.00\xa4",
    tzm: "#,##0.00\xa0\xa4",
    ug: "\xa4#,##0.00;(\xa4#,##0.00)",
    uk: "#,##0.00\xa0\xa4",
    und: "\xa4\xa0#,##0.00",
    ur: "\xa4#,##0.00;(\xa4#,##0.00)",
    "ur-IN": "\xa4#,##0.00;(\xa4#,##0.00)",
    uz: "#,##0.00\xa0\xa4",
    "uz-Arab": "\xa4\xa0#,##0.00",
    "uz-Cyrl": "#,##0.00\xa0\xa4",
    "uz-Latn": "#,##0.00\xa0\xa4",
    vai: "\xa4#,##0.00;(\xa4#,##0.00)",
    "vai-Latn": "\xa4#,##0.00;(\xa4#,##0.00)",
    "vai-Vaii": "\xa4#,##0.00;(\xa4#,##0.00)",
    vi: "#,##0.00\xa0\xa4",
    vun: "\xa4#,##0.00",
    wae: "\xa4\xa0#,##0.00",
    wo: "\xa4\xa0#,##0.00",
    xh: "\xa4#,##0.00",
    xog: "#,##0.00\xa0\xa4",
    yav: "#,##0.00\xa0\xa4;(#,##0.00\xa0\xa4)",
    yi: "\xa4\xa0#,##0.00",
    yo: "\xa4#,##0.00;(\xa4#,##0.00)",
    "yo-BJ": "\xa4#,##0.00;(\xa4#,##0.00)",
    yrl: "\xa4\xa0#,##0.00",
    "yrl-CO": "\xa4\xa0#,##0.00",
    "yrl-VE": "\xa4\xa0#,##0.00",
    yue: "\xa4#,##0.00;(\xa4#,##0.00)",
    "yue-Hans": "\xa4#,##0.00;(\xa4#,##0.00)",
    "yue-Hant": "\xa4#,##0.00;(\xa4#,##0.00)",
    zgh: "#,##0.00\xa4",
    zh: "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hans": "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hans-HK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hans-MO": "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hans-SG": "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hant": "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hant-HK": "\xa4#,##0.00;(\xa4#,##0.00)",
    "zh-Hant-MO": "\xa4#,##0.00;(\xa4#,##0.00)",
    zu: "\xa4#,##0.00;(\xa4#,##0.00)"
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/intl/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/intl/number.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/config.js [app-client] (ecmascript) <export default as config>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$open_xml_currency_format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/open_xml_currency_format.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$cldr$2d$data$2f$accounting_formats$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/cldr-data/accounting_formats.js [app-client] (ecmascript)");
;
;
;
;
const CURRENCY_STYLES = [
    "standard",
    "accounting"
];
const MAX_FRACTION_DIGITS = 20;
const detectCurrencySymbolRegex = /([^\s0]+)?(\s*)0*[.,]*0*(\s*)([^\s0]+)?/;
const formattersCache = {};
const getFormatter = (format)=>{
    const key = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale() + "/" + JSON.stringify(format);
    if (!formattersCache[key]) {
        formattersCache[key] = new Intl.NumberFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale(), format).format;
    }
    return formattersCache[key];
};
const getCurrencyFormatter = (currency)=>new Intl.NumberFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale(), {
        style: "currency",
        currency: currency
    });
const __TURBOPACK__default__export__ = {
    engine: function() {
        return "intl";
    },
    _formatNumberCore: function(value, format, formatConfig) {
        if ("exponential" === format) {
            return this.callBase.apply(this, arguments);
        }
        return getFormatter(this._normalizeFormatConfig(format, formatConfig, value))(value);
    },
    _normalizeFormatConfig: function(format, formatConfig, value) {
        let config;
        if ("decimal" === format) {
            const fractionDigits = String(value).split(".")[1];
            config = {
                minimumIntegerDigits: formatConfig.precision || void 0,
                useGrouping: false,
                maximumFractionDigits: fractionDigits && fractionDigits.length,
                round: value < 0 ? "ceil" : "floor"
            };
        } else {
            config = this._getPrecisionConfig(formatConfig.precision);
        }
        if ("percent" === format) {
            config.style = "percent";
        } else if ("currency" === format) {
            var _formatConfig_useCurrencyAccountingStyle;
            const useAccountingStyle = (_formatConfig_useCurrencyAccountingStyle = formatConfig.useCurrencyAccountingStyle) !== null && _formatConfig_useCurrencyAccountingStyle !== void 0 ? _formatConfig_useCurrencyAccountingStyle : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__["config"])().defaultUseCurrencyAccountingStyle;
            config.style = "currency";
            config.currency = formatConfig.currency || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__["config"])().defaultCurrency;
            config.currencySign = CURRENCY_STYLES[+useAccountingStyle];
        }
        return config;
    },
    _getPrecisionConfig: function(precision) {
        let config;
        if (null === precision) {
            config = {
                minimumFractionDigits: 0,
                maximumFractionDigits: 20
            };
        } else {
            config = {
                minimumFractionDigits: precision || 0,
                maximumFractionDigits: precision || 0
            };
        }
        return config;
    },
    format: function(value, format) {
        if ("number" !== typeof value) {
            return value;
        }
        format = this._normalizeFormat(format);
        if ("default" === format.currency) {
            format.currency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__["config"])().defaultCurrency;
        }
        if (!format || "function" !== typeof format && !format.type && !format.formatter) {
            return getFormatter(format)(value);
        }
        const result = this.callBase.apply(this, arguments);
        return result;
    },
    _getCurrencySymbolInfo: function(currency) {
        const formatter = getCurrencyFormatter(currency);
        return this._extractCurrencySymbolInfo(formatter.format(0));
    },
    _extractCurrencySymbolInfo: function(currencyValueString) {
        const match = detectCurrencySymbolRegex.exec(currencyValueString) || [];
        const position = match[1] ? "before" : "after";
        const symbol = match[1] || match[4] || "";
        const delimiter = match[2] || match[3] || "";
        return {
            position: position,
            symbol: symbol,
            delimiter: delimiter
        };
    },
    getCurrencySymbol: function(currency) {
        if (!currency) {
            currency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__["config"])().defaultCurrency;
        }
        const symbolInfo = this._getCurrencySymbolInfo(currency);
        return {
            symbol: symbolInfo.symbol
        };
    },
    getOpenXmlCurrencyFormat: function(currency) {
        const targetCurrency = currency || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__["config"])().defaultCurrency;
        const currencySymbol = this._getCurrencySymbolInfo(targetCurrency).symbol;
        const closestAccountingFormat = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getValueByClosestLocale((locale)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$cldr$2d$data$2f$accounting_formats$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][locale]);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$open_xml_currency_format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(currencySymbol, closestAccountingFormat);
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/number.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/number.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/ldml/number.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$currency$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/currency.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$intl$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/intl/number.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const hasIntl = "undefined" !== typeof Intl;
const MAX_LARGE_NUMBER_POWER = 4;
const DECIMAL_BASE = 10;
const NUMERIC_FORMATS = [
    "currency",
    "fixedpoint",
    "exponential",
    "percent",
    "decimal"
];
const LargeNumberFormatPostfixes = {
    1: "K",
    2: "M",
    3: "B",
    4: "T"
};
const LargeNumberFormatPowers = {
    largenumber: "auto",
    thousands: 1,
    millions: 2,
    billions: 3,
    trillions: 4
};
const numberLocalization = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    engine: function() {
        return "base";
    },
    numericFormats: NUMERIC_FORMATS,
    defaultLargeNumberFormatPostfixes: LargeNumberFormatPostfixes,
    _parseNumberFormatString: function(formatType) {
        const formatObject = {};
        if (!formatType || "string" !== typeof formatType) {
            return;
        }
        const formatList = formatType.toLowerCase().split(" ");
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(formatList, (index, value)=>{
            if (NUMERIC_FORMATS.includes(value)) {
                formatObject.formatType = value;
            } else if (value in LargeNumberFormatPowers) {
                formatObject.power = LargeNumberFormatPowers[value];
            }
        });
        if (formatObject.power && !formatObject.formatType) {
            formatObject.formatType = "fixedpoint";
        }
        if (formatObject.formatType) {
            return formatObject;
        }
    },
    _calculateNumberPower: function(value, base, minPower, maxPower) {
        let number = Math.abs(value);
        let power = 0;
        if (number > 1) {
            while(number && number >= base && (void 0 === maxPower || power < maxPower)){
                power++;
                number /= base;
            }
        } else if (number > 0 && number < 1) {
            while(number < 1 && (void 0 === minPower || power > minPower)){
                power--;
                number *= base;
            }
        }
        return power;
    },
    _getNumberByPower: function(number, power, base) {
        let result = number;
        while(power > 0){
            result /= base;
            power--;
        }
        while(power < 0){
            result *= base;
            power++;
        }
        return result;
    },
    _formatNumber: function(value, formatObject, formatConfig) {
        if ("auto" === formatObject.power) {
            formatObject.power = this._calculateNumberPower(value, 1e3, 0, 4);
        }
        if (formatObject.power) {
            value = this._getNumberByPower(value, formatObject.power, 1e3);
        }
        const powerPostfix = this.defaultLargeNumberFormatPostfixes[formatObject.power] || "";
        let result = this._formatNumberCore(value, formatObject.formatType, formatConfig);
        result = result.replace(/(\d|.$)(\D*)$/, "$1" + powerPostfix + "$2");
        return result;
    },
    _formatNumberExponential: function(value, formatConfig) {
        let power = this._calculateNumberPower(value, 10);
        let number = this._getNumberByPower(value, power, 10);
        if (void 0 === formatConfig.precision) {
            formatConfig.precision = 1;
        }
        if (number.toFixed(formatConfig.precision || 0) >= 10) {
            power++;
            number /= 10;
        }
        const powString = (power >= 0 ? "+" : "") + power.toString();
        return this._formatNumberCore(number, "fixedpoint", formatConfig) + "E" + powString;
    },
    _addZeroes: function(value, precision) {
        const multiplier = Math.pow(10, precision);
        const sign = value < 0 ? "-" : "";
        value = (Math.abs(value) * multiplier >>> 0) / multiplier;
        let result = value.toString();
        while(result.length < precision){
            result = "0" + result;
        }
        return sign + result;
    },
    _addGroupSeparators: function(value) {
        const parts = value.toString().split(".");
        return parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().thousandsSeparator) + (parts[1] ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().decimalSeparator + parts[1] : "");
    },
    _formatNumberCore: function(value, format, formatConfig) {
        if ("exponential" === format) {
            return this._formatNumberExponential(value, formatConfig);
        }
        if ("decimal" !== format && null !== formatConfig.precision) {
            formatConfig.precision = formatConfig.precision || 0;
        }
        if ("percent" === format) {
            value *= 100;
        }
        if (void 0 !== formatConfig.precision) {
            if ("decimal" === format) {
                value = this._addZeroes(value, formatConfig.precision);
            } else {
                value = null === formatConfig.precision ? value.toPrecision() : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["toFixed"])(value, formatConfig.precision);
            }
        }
        if ("decimal" !== format) {
            value = this._addGroupSeparators(value);
        } else {
            value = value.toString().replace(".", (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().decimalSeparator);
        }
        if ("percent" === format) {
            value += "%";
        }
        return value;
    },
    _normalizeFormat: function(format) {
        if (!format) {
            return {};
        }
        if ("function" === typeof format) {
            return format;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPlainObject"])(format)) {
            format = {
                type: format
            };
        }
        return format;
    },
    _getSeparators: function() {
        return {
            decimalSeparator: this.getDecimalSeparator(),
            thousandsSeparator: this.getThousandsSeparator()
        };
    },
    getThousandsSeparator: function() {
        return this.format(1e4, "fixedPoint")[2];
    },
    getDecimalSeparator: function() {
        return this.format(1.2, {
            type: "fixedPoint",
            precision: 1
        })[1];
    },
    convertDigits: function(value, toStandard) {
        const digits = this.format(90, "decimal");
        if ("string" !== typeof value || "0" === digits[1]) {
            return value;
        }
        const fromFirstDigit = toStandard ? digits[1] : "0";
        const toFirstDigit = toStandard ? "0" : digits[1];
        const fromLastDigit = toStandard ? digits[0] : "9";
        const regExp = new RegExp("[" + fromFirstDigit + "-" + fromLastDigit + "]", "g");
        return value.replace(regExp, (char)=>String.fromCharCode(char.charCodeAt(0) + (toFirstDigit.charCodeAt(0) - fromFirstDigit.charCodeAt(0))));
    },
    getNegativeEtalonRegExp: function(format) {
        const separators = this._getSeparators();
        const digitalRegExp = new RegExp("[0-9" + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["escapeRegExp"])(separators.decimalSeparator + separators.thousandsSeparator) + "]+", "g");
        let negativeEtalon = this.format(-1, format).replace(digitalRegExp, "1");
        [
            "\\",
            "(",
            ")",
            "[",
            "]",
            "*",
            "+",
            "$",
            "^",
            "?",
            "|",
            "{",
            "}"
        ].forEach((char)=>{
            negativeEtalon = negativeEtalon.replace(new RegExp("\\".concat(char), "g"), "\\".concat(char));
        });
        negativeEtalon = negativeEtalon.replace(/ /g, "\\s");
        negativeEtalon = negativeEtalon.replace(/1/g, ".*");
        return new RegExp(negativeEtalon, "g");
    },
    getSign: function(text, format) {
        if (!format) {
            if ("-" === text.replace(/[^0-9-]/g, "").charAt(0)) {
                return -1;
            }
            return 1;
        }
        const negativeEtalon = this.getNegativeEtalonRegExp(format);
        return text.match(negativeEtalon) ? -1 : 1;
    },
    format: function(value, format) {
        if ("number" !== typeof value) {
            return value;
        }
        if ("number" === typeof format) {
            return value;
        }
        format = format && format.formatter || format;
        if ("function" === typeof format) {
            return format(value);
        }
        format = this._normalizeFormat(format);
        if (!format.type) {
            format.type = "decimal";
        }
        const numberConfig = this._parseNumberFormatString(format.type);
        if (!numberConfig) {
            const formatterConfig = this._getSeparators();
            formatterConfig.unlimitedIntegerDigits = format.unlimitedIntegerDigits;
            const formatter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFormatter"])(format.type, formatterConfig)(value);
            const result = this.convertDigits(formatter);
            return result;
        }
        return this._formatNumber(value, numberConfig, format);
    },
    parse: function(text, format) {
        if (!text) {
            return;
        }
        if (format && format.parser) {
            return format.parser(text);
        }
        text = this.convertDigits(text, true);
        if (format && "string" !== typeof format) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].log("W0011");
        }
        const decimalSeparator = this.getDecimalSeparator();
        const regExp = new RegExp("[^0-9" + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["escapeRegExp"])(decimalSeparator) + "]", "g");
        const cleanedText = text.replace(regExp, "").replace(decimalSeparator, ".").replace(/\.$/g, "");
        if ("." === cleanedText || "" === cleanedText) {
            return null;
        }
        if (this._calcSignificantDigits(cleanedText) > 15) {
            return NaN;
        }
        let parsed = +cleanedText * this.getSign(text, format);
        format = this._normalizeFormat(format);
        const formatConfig = this._parseNumberFormatString(format.type);
        let power = null === formatConfig || void 0 === formatConfig ? void 0 : formatConfig.power;
        if (power) {
            if ("auto" === power) {
                const match = text.match(/\d(K|M|B|T)/);
                if (match) {
                    power = Object.keys(LargeNumberFormatPostfixes).find((power)=>LargeNumberFormatPostfixes[power] === match[1]);
                }
            }
            parsed *= Math.pow(10, 3 * power);
        }
        if ("percent" === (null === formatConfig || void 0 === formatConfig ? void 0 : formatConfig.formatType)) {
            parsed /= 100;
        }
        return parsed;
    },
    _calcSignificantDigits: function(text) {
        const [integer, fractional] = text.split(".");
        const calcDigitsAfterLeadingZeros = (digits)=>{
            let index = -1;
            for(let i = 0; i < digits.length; i++){
                if ("0" !== digits[i]) {
                    index = i;
                    break;
                }
            }
            return index > -1 ? digits.length - index : 0;
        };
        let result = 0;
        if (integer) {
            result += calcDigitsAfterLeadingZeros(integer.split(""));
        }
        if (fractional) {
            result += calcDigitsAfterLeadingZeros(fractional.split("").reverse());
        }
        return result;
    }
});
numberLocalization.inject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$currency$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
if (hasIntl) {
    numberLocalization.inject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$intl$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
}
const __TURBOPACK__default__export__ = numberLocalization;
}),
"[project]/node_modules/devextreme/esm/common/core/localization/ldml/date.format.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/ldml/date.format.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getFormat": ()=>getFormat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/number.js [app-client] (ecmascript)");
;
const ARABIC_COMMA = "\u060c";
const FORMAT_SEPARATORS = " .,:;/\\<>()-[]\u060c";
const AM_PM_PATTERN = ". m.";
const checkDigit = function(char) {
    const code = char && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertDigits(char, false).charCodeAt(0);
    const zeroCode = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertDigits("0", false).charCodeAt(0);
    return zeroCode <= code && code < zeroCode + 10;
};
const checkPatternContinue = function(text, patterns, index, isDigit) {
    const char = text[index];
    const nextChar = text[index + 1];
    if (!isDigit) {
        if ("." === char || " " === char && ". m." === text.slice(index - 1, index + 3)) {
            return true;
        }
        if ("-" === char && !checkDigit(nextChar)) {
            return true;
        }
    }
    const isDigitChanged = isDigit && patterns.some((pattern)=>text[index] !== pattern[index]);
    return FORMAT_SEPARATORS.indexOf(char) < 0 && isDigit === checkDigit(char) && (!isDigit || isDigitChanged);
};
const getPatternStartIndex = function(defaultPattern, index) {
    if (!checkDigit(defaultPattern[index])) {
        while(index > 0 && !checkDigit(defaultPattern[index - 1]) && ("." === defaultPattern[index - 1] || FORMAT_SEPARATORS.indexOf(defaultPattern[index - 1]) < 0)){
            index--;
        }
    }
    return index;
};
const getDifference = function(defaultPattern, patterns, processedIndexes, isDigit) {
    let i = 0;
    const result = [];
    const patternsFilter = function(pattern) {
        return defaultPattern[i] !== pattern[i] && (void 0 === isDigit || checkDigit(defaultPattern[i]) === isDigit);
    };
    if (!Array.isArray(patterns)) {
        patterns = [
            patterns
        ];
    }
    for(i = 0; i < defaultPattern.length; i++){
        if (processedIndexes.indexOf(i) < 0 && patterns.filter(patternsFilter).length) {
            i = getPatternStartIndex(defaultPattern, i);
            do {
                isDigit = checkDigit(defaultPattern[i]);
                if (!result.length && !isDigit && checkDigit(patterns[0][i])) {
                    break;
                }
                result.push(i);
                processedIndexes.unshift(i);
                i++;
            }while (defaultPattern[i] && checkPatternContinue(defaultPattern, patterns, i, isDigit))
            break;
        }
    }
    if (1 === result.length && ("0" === defaultPattern[processedIndexes[0] - 1] || "\u0660" === defaultPattern[processedIndexes[0] - 1])) {
        processedIndexes.unshift(processedIndexes[0] - 1);
    }
    return result;
};
const replaceCharsCore = function(pattern, indexes, char, patternPositions) {
    const baseCharIndex = indexes[0];
    const patternIndex = baseCharIndex < patternPositions.length ? patternPositions[baseCharIndex] : baseCharIndex;
    indexes.forEach(function(_, index) {
        pattern = pattern.substr(0, patternIndex + index) + (char.length > 1 ? char[index] : char) + pattern.substr(patternIndex + index + 1);
    });
    if (1 === indexes.length) {
        pattern = pattern.replace("0" + char, char + char);
        pattern = pattern.replace("\u0660" + char, char + char);
    }
    return pattern;
};
const replaceChars = function(pattern, indexes, char, patternPositions) {
    let i;
    let index;
    let patternIndex;
    if (!checkDigit(pattern[indexes[0]] || "0")) {
        const letterCount = Math.max(indexes.length <= 3 ? 3 : 4, char.length);
        while(indexes.length > letterCount){
            index = indexes.pop();
            patternIndex = patternPositions[index];
            patternPositions[index] = -1;
            for(i = index + 1; i < patternPositions.length; i++){
                patternPositions[i]--;
            }
            pattern = pattern.substr(0, patternIndex) + pattern.substr(patternIndex + 1);
        }
        index = indexes[indexes.length - 1] + 1, patternIndex = index < patternPositions.length ? patternPositions[index] : index;
        while(indexes.length < letterCount){
            indexes.push(indexes[indexes.length - 1] + 1);
            for(i = index; i < patternPositions.length; i++){
                patternPositions[i]++;
            }
            pattern = pattern.substr(0, patternIndex) + " " + pattern.substr(patternIndex);
        }
    }
    pattern = replaceCharsCore(pattern, indexes, char, patternPositions);
    return pattern;
};
const formatValue = function(value, formatter) {
    if (Array.isArray(value)) {
        return value.map(function(value) {
            return (formatter(value) || "").toString();
        });
    }
    return (formatter(value) || "").toString();
};
const ESCAPE_CHARS_REGEXP = /[a-zA-Z]/g;
const escapeChars = function(pattern, defaultPattern, processedIndexes, patternPositions) {
    const escapeIndexes = defaultPattern.split("").map(function(char, index) {
        if (processedIndexes.indexOf(index) < 0 && (char.match(ESCAPE_CHARS_REGEXP) || "'" === char)) {
            return patternPositions[index];
        }
        return -1;
    });
    pattern = pattern.split("").map(function(char, index) {
        let result = char;
        const isCurrentCharEscaped = escapeIndexes.indexOf(index) >= 0;
        const isPrevCharEscaped = index > 0 && escapeIndexes.indexOf(index - 1) >= 0;
        const isNextCharEscaped = escapeIndexes.indexOf(index + 1) >= 0;
        if (isCurrentCharEscaped) {
            if (!isPrevCharEscaped) {
                result = "'" + result;
            }
            if (!isNextCharEscaped) {
                result += "'";
            }
        }
        return result;
    }).join("");
    return pattern;
};
const getFormat = function(formatter) {
    const processedIndexes = [];
    const defaultPattern = formatValue(new Date(2009, 8, 8, 6, 5, 4), formatter);
    const patternPositions = defaultPattern.split("").map(function(_, index) {
        return index;
    });
    let result = defaultPattern;
    const replacedPatterns = {};
    const datePatterns = [
        {
            date: new Date(2009, 8, 8, 6, 5, 4, 111),
            pattern: "S"
        },
        {
            date: new Date(2009, 8, 8, 6, 5, 2),
            pattern: "s"
        },
        {
            date: new Date(2009, 8, 8, 6, 2, 4),
            pattern: "m"
        },
        {
            date: new Date(2009, 8, 8, 18, 5, 4),
            pattern: "H",
            isDigit: true
        },
        {
            date: new Date(2009, 8, 8, 2, 5, 4),
            pattern: "h",
            isDigit: true
        },
        {
            date: new Date(2009, 8, 8, 18, 5, 4),
            pattern: "a",
            isDigit: false
        },
        {
            date: new Date(2009, 8, 1, 6, 5, 4),
            pattern: "d"
        },
        {
            date: [
                new Date(2009, 8, 2, 6, 5, 4),
                new Date(2009, 8, 3, 6, 5, 4),
                new Date(2009, 8, 4, 6, 5, 4)
            ],
            pattern: "E"
        },
        {
            date: new Date(2009, 9, 6, 6, 5, 4),
            pattern: "M"
        },
        {
            date: new Date(1998, 8, 8, 6, 5, 4),
            pattern: "y"
        }
    ];
    if (!result) {
        return;
    }
    datePatterns.forEach(function(test) {
        const diff = getDifference(defaultPattern, formatValue(test.date, formatter), processedIndexes, test.isDigit);
        const pattern = "M" === test.pattern && !replacedPatterns.d ? "L" : test.pattern;
        result = replaceChars(result, diff, pattern, patternPositions);
        replacedPatterns[pattern] = diff.length;
    });
    result = escapeChars(result, defaultPattern, processedIndexes, patternPositions);
    if (processedIndexes.length) {
        return result;
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/ldml/date.parser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/ldml/date.parser.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getParser": ()=>getParser,
    "getPatternSetters": ()=>getPatternSetters,
    "getRegExpInfo": ()=>getRegExpInfo,
    "isPossibleForParsingFormat": ()=>isPossibleForParsingFormat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$console$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/console.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_console.js [app-client] (ecmascript)");
;
;
const FORMAT_TYPES = {
    3: "abbreviated",
    4: "wide",
    5: "narrow"
};
const monthRegExpGenerator = function(count, dateParts) {
    if (count > 2) {
        return Object.keys(FORMAT_TYPES).map(function(count) {
            return [
                "format",
                "standalone"
            ].map(function(type) {
                return dateParts.getMonthNames(FORMAT_TYPES[count], type).join("|");
            }).join("|");
        }).join("|");
    }
    return 2 === count ? "1[012]|0?[1-9]" : "0??[1-9]|1[012]";
};
const PATTERN_REGEXPS = {
    ":": function(count, dateParts) {
        const countSuffix = count > 1 ? "{".concat(count, "}") : "";
        let timeSeparator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["escapeRegExp"])(dateParts.getTimeSeparator());
        ":" !== timeSeparator && (timeSeparator = "".concat(timeSeparator, "|:"));
        return "".concat(timeSeparator).concat(countSuffix);
    },
    y: function(count) {
        return 2 === count ? "[0-9]{".concat(count, "}") : "[0-9]+?";
    },
    M: monthRegExpGenerator,
    L: monthRegExpGenerator,
    Q: function(count, dateParts) {
        if (count > 2) {
            return dateParts.getQuarterNames(FORMAT_TYPES[count], "format").join("|");
        }
        return "0?[1-4]";
    },
    E: function(count, dateParts) {
        return "\\D*";
    },
    a: function(count, dateParts) {
        return dateParts.getPeriodNames(FORMAT_TYPES[count < 3 ? 3 : count], "format").join("|");
    },
    d: function(count) {
        return 2 === count ? "3[01]|[12][0-9]|0?[1-9]" : "0??[1-9]|[12][0-9]|3[01]";
    },
    H: function(count) {
        return 2 === count ? "2[0-3]|1[0-9]|0?[0-9]" : "0??[0-9]|1[0-9]|2[0-3]";
    },
    h: function(count) {
        return 2 === count ? "1[012]|0?[1-9]" : "0??[1-9]|1[012]";
    },
    m: function(count) {
        return 2 === count ? "[1-5][0-9]|0?[0-9]" : "0??[0-9]|[1-5][0-9]";
    },
    s: function(count) {
        return 2 === count ? "[1-5][0-9]|0?[0-9]" : "0??[0-9]|[1-5][0-9]";
    },
    S: function(count) {
        return "[0-9]{1,".concat(count, "}");
    },
    w: function(count) {
        return 2 === count ? "[1-5][0-9]|0?[0-9]" : "0??[0-9]|[1-5][0-9]";
    },
    x: function(count) {
        return 3 === count ? "[+-](?:2[0-3]|[01][0-9]):(?:[0-5][0-9])|Z" : "[+-](?:2[0-3]|[01][0-9])(?:[0-5][0-9])|Z";
    }
};
const parseNumber = Number;
const caseInsensitiveIndexOf = function(array, value) {
    return array.map((item)=>item.toLowerCase()).indexOf(value.toLowerCase());
};
const monthPatternParser = function(text, count, dateParts) {
    if (count > 2) {
        return [
            "format",
            "standalone"
        ].map(function(type) {
            return Object.keys(FORMAT_TYPES).map(function(count) {
                const monthNames = dateParts.getMonthNames(FORMAT_TYPES[count], type);
                return caseInsensitiveIndexOf(monthNames, text);
            });
        }).reduce(function(a, b) {
            return a.concat(b);
        }).filter(function(index) {
            return index >= 0;
        })[0];
    }
    return parseNumber(text) - 1;
};
const PATTERN_PARSERS = {
    y: function(text, count) {
        const year = parseNumber(text);
        if (2 === count) {
            return year < 30 ? 2e3 + year : 1900 + year;
        }
        return year;
    },
    M: monthPatternParser,
    L: monthPatternParser,
    Q: function(text, count, dateParts) {
        if (count > 2) {
            return dateParts.getQuarterNames(FORMAT_TYPES[count], "format").indexOf(text);
        }
        return parseNumber(text) - 1;
    },
    E: function(text, count, dateParts) {
        const dayNames = dateParts.getDayNames(FORMAT_TYPES[count < 3 ? 3 : count], "format");
        return caseInsensitiveIndexOf(dayNames, text);
    },
    a: function(text, count, dateParts) {
        const periodNames = dateParts.getPeriodNames(FORMAT_TYPES[count < 3 ? 3 : count], "format");
        return caseInsensitiveIndexOf(periodNames, text);
    },
    d: parseNumber,
    H: parseNumber,
    h: parseNumber,
    m: parseNumber,
    s: parseNumber,
    S: function(text, count) {
        count = Math.max(count, 3);
        text = text.slice(0, 3);
        while(count < 3){
            text += "0";
            count++;
        }
        return parseNumber(text);
    }
};
const ORDERED_PATTERNS = [
    "y",
    "M",
    "d",
    "h",
    "m",
    "s",
    "S"
];
const PATTERN_SETTERS = {
    y: "setFullYear",
    M: "setMonth",
    L: "setMonth",
    a: function(date, value, datePartValues) {
        let hours = date.getHours();
        const hourPartValue = datePartValues.h;
        if (void 0 !== hourPartValue && hourPartValue !== hours) {
            hours--;
        }
        if (!value && 12 === hours) {
            hours = 0;
        } else if (value && 12 !== hours) {
            hours += 12;
        }
        date.setHours(hours);
    },
    d: "setDate",
    H: "setHours",
    h: "setHours",
    m: "setMinutes",
    s: "setSeconds",
    S: "setMilliseconds"
};
const getSameCharCount = function(text, index) {
    const char = text[index];
    if (!char) {
        return 0;
    }
    let count = 0;
    do {
        index++;
        count++;
    }while (text[index] === char)
    return count;
};
const createPattern = function(char, count) {
    let result = "";
    for(let i = 0; i < count; i++){
        result += char;
    }
    return result;
};
const getRegExpInfo = function(format, dateParts) {
    let regexpText = "";
    let stubText = "";
    let isEscaping;
    const patterns = [];
    const addPreviousStub = function() {
        if (stubText) {
            patterns.push("'".concat(stubText, "'"));
            regexpText += "".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["escapeRegExp"])(stubText), ")");
            stubText = "";
        }
    };
    for(let i = 0; i < format.length; i++){
        const char = format[i];
        const isEscapeChar = "'" === char;
        const regexpPart = PATTERN_REGEXPS[char];
        if (isEscapeChar) {
            isEscaping = !isEscaping;
            if ("'" !== format[i - 1]) {
                continue;
            }
        }
        if (regexpPart && !isEscaping) {
            const count = getSameCharCount(format, i);
            const pattern = createPattern(char, count);
            addPreviousStub();
            patterns.push(pattern);
            regexpText += "(".concat(regexpPart(count, dateParts), ")");
            i += count - 1;
        } else {
            if (!stubText) {
                regexpText += "(";
            }
            stubText += char;
        }
    }
    addPreviousStub();
    if (!isPossibleForParsingFormat(patterns)) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["logger"].warn("The following format may be parsed incorrectly: ".concat(format, "."));
    }
    return {
        patterns: patterns,
        regexp: new RegExp("^".concat(regexpText, "$"), "i")
    };
};
const digitFieldSymbols = [
    "d",
    "H",
    "h",
    "m",
    "s",
    "w",
    "M",
    "L",
    "Q"
];
const isPossibleForParsingFormat = function(patterns) {
    const isDigitPattern = (pattern)=>{
        if (!pattern) {
            return false;
        }
        const char = pattern[0];
        return [
            "y",
            "S"
        ].includes(char) || digitFieldSymbols.includes(char) && pattern.length < 3;
    };
    let possibleForParsing = true;
    let ambiguousDigitPatternsCount = 0;
    return patterns.every((pattern, index, patterns)=>{
        if (isDigitPattern(pattern)) {
            if (((pattern)=>"S" !== pattern[0] && 2 !== pattern.length)(pattern)) {
                possibleForParsing = ++ambiguousDigitPatternsCount < 2;
            }
            if (!isDigitPattern(patterns[index + 1])) {
                ambiguousDigitPatternsCount = 0;
            }
        }
        return possibleForParsing;
    });
};
const getPatternSetters = function() {
    return PATTERN_SETTERS;
};
const setPatternPart = function(date, pattern, text, dateParts, datePartValues) {
    const patternChar = pattern[0];
    const partSetter = PATTERN_SETTERS[patternChar];
    const partParser = PATTERN_PARSERS[patternChar];
    if (partSetter && partParser) {
        const value = partParser(text, pattern.length, dateParts);
        datePartValues[pattern] = value;
        if (date[partSetter]) {
            date[partSetter](value);
        } else {
            partSetter(date, value, datePartValues);
        }
    }
};
const setPatternPartFromNow = function(date, pattern, now) {
    const setterName = PATTERN_SETTERS[pattern];
    const getterName = "g" + setterName.substr(1);
    const value = now[getterName]();
    date[setterName](value);
};
const getShortPatterns = function(fullPatterns) {
    return fullPatterns.map(function(pattern) {
        if ("'" === pattern[0]) {
            return "";
        } else {
            return "H" === pattern[0] ? "h" : pattern[0];
        }
    });
};
const getMaxOrderedPatternIndex = function(patterns) {
    const indexes = patterns.map(function(pattern) {
        return ORDERED_PATTERNS.indexOf(pattern);
    });
    return Math.max.apply(Math, indexes);
};
const getOrderedFormatPatterns = function(formatPatterns) {
    const otherPatterns = formatPatterns.filter(function(pattern) {
        return ORDERED_PATTERNS.indexOf(pattern) < 0;
    });
    return ORDERED_PATTERNS.concat(otherPatterns);
};
const getParser = function(format, dateParts) {
    const regExpInfo = getRegExpInfo(format, dateParts);
    return function(text) {
        const regExpResult = regExpInfo.regexp.exec(text);
        if (regExpResult) {
            const now = new Date;
            const date = new Date(now.getFullYear(), 0, 1);
            const formatPatterns = getShortPatterns(regExpInfo.patterns);
            const maxPatternIndex = getMaxOrderedPatternIndex(formatPatterns);
            const orderedFormatPatterns = getOrderedFormatPatterns(formatPatterns);
            const datePartValues = {};
            orderedFormatPatterns.forEach(function(pattern, index) {
                if (!pattern || index < ORDERED_PATTERNS.length && index > maxPatternIndex) {
                    return;
                }
                const patternIndex = formatPatterns.indexOf(pattern);
                if (patternIndex >= 0) {
                    const regExpPattern = regExpInfo.patterns[patternIndex];
                    const regExpText = regExpResult[patternIndex + 1];
                    setPatternPart(date, regExpPattern, regExpText, dateParts, datePartValues);
                } else {
                    setPatternPartFromNow(date, pattern, now);
                }
            });
            return date;
        }
        return null;
    };
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/cldr-data/first_day_of_week_data.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/cldr-data/first_day_of_week_data.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ // !!! AUTO-GENERATED FILE, DO NOT EDIT
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {
    "af-NA": 1,
    agq: 1,
    ak: 1,
    ar: 6,
    "ar-EH": 1,
    "ar-ER": 1,
    "ar-KM": 1,
    "ar-LB": 1,
    "ar-MA": 1,
    "ar-MR": 1,
    "ar-PS": 1,
    "ar-SO": 1,
    "ar-SS": 1,
    "ar-TD": 1,
    "ar-TN": 1,
    asa: 1,
    ast: 1,
    az: 1,
    "az-Cyrl": 1,
    bas: 1,
    be: 1,
    bem: 1,
    bez: 1,
    bg: 1,
    bm: 1,
    br: 1,
    bs: 1,
    "bs-Cyrl": 1,
    ca: 1,
    ce: 1,
    cgg: 1,
    ckb: 6,
    cs: 1,
    cy: 1,
    da: 1,
    de: 1,
    dje: 1,
    dsb: 1,
    dua: 1,
    dyo: 1,
    ee: 1,
    el: 1,
    "en-001": 1,
    "en-AE": 6,
    "en-BI": 1,
    "en-MP": 1,
    "en-MV": 5,
    "en-SD": 6,
    eo: 1,
    es: 1,
    et: 1,
    eu: 1,
    ewo: 1,
    fa: 6,
    ff: 1,
    "ff-Adlm": 1,
    fi: 1,
    fo: 1,
    fr: 1,
    "fr-DJ": 6,
    "fr-DZ": 6,
    "fr-SY": 6,
    fur: 1,
    fy: 1,
    ga: 1,
    gd: 1,
    gl: 1,
    gsw: 1,
    gv: 1,
    ha: 1,
    hr: 1,
    hsb: 1,
    hu: 1,
    hy: 1,
    ia: 1,
    ig: 1,
    is: 1,
    it: 1,
    jgo: 1,
    jmc: 1,
    ka: 1,
    kab: 6,
    kde: 1,
    kea: 1,
    khq: 1,
    kk: 1,
    kkj: 1,
    kl: 1,
    "ko-KP": 1,
    ksb: 1,
    ksf: 1,
    ksh: 1,
    ku: 1,
    kw: 1,
    ky: 1,
    lag: 1,
    lb: 1,
    lg: 1,
    ln: 1,
    lrc: 6,
    lt: 1,
    lu: 1,
    lv: 1,
    "mas-TZ": 1,
    mfe: 1,
    mg: 1,
    mgo: 1,
    mi: 1,
    mk: 1,
    mn: 1,
    ms: 1,
    mua: 1,
    mzn: 6,
    naq: 1,
    nds: 1,
    nl: 1,
    nmg: 1,
    nnh: 1,
    no: 1,
    nus: 1,
    nyn: 1,
    os: 1,
    pcm: 1,
    pl: 1,
    ps: 6,
    "pt-AO": 1,
    "pt-CH": 1,
    "pt-CV": 1,
    "pt-GQ": 1,
    "pt-GW": 1,
    "pt-LU": 1,
    "pt-ST": 1,
    "pt-TL": 1,
    "qu-BO": 1,
    "qu-EC": 1,
    rm: 1,
    rn: 1,
    ro: 1,
    rof: 1,
    ru: 1,
    rw: 1,
    rwk: 1,
    sah: 1,
    sbp: 1,
    sc: 1,
    se: 1,
    ses: 1,
    sg: 1,
    shi: 1,
    "shi-Latn": 1,
    si: 1,
    sk: 1,
    sl: 1,
    smn: 1,
    so: 1,
    "so-DJ": 6,
    sq: 1,
    sr: 1,
    "sr-Latn": 1,
    sv: 1,
    sw: 1,
    "ta-LK": 1,
    "ta-MY": 1,
    teo: 1,
    tg: 1,
    "ti-ER": 1,
    tk: 1,
    to: 1,
    tr: 1,
    tt: 1,
    twq: 1,
    tzm: 1,
    uk: 1,
    uz: 1,
    "uz-Arab": 6,
    "uz-Cyrl": 1,
    vai: 1,
    "vai-Latn": 1,
    vi: 1,
    vun: 1,
    wae: 1,
    wo: 1,
    xog: 1,
    yav: 1,
    yi: 1,
    yo: 1,
    zgh: 1
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/intl/date.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/intl/date.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)");
;
;
const SYMBOLS_TO_REMOVE_REGEX = /[\u200E\u200F]/g;
const NARROW_NO_BREAK_SPACE_REGEX = /[\u202F]/g;
const getIntlFormatter = (format)=>(date)=>{
        if (!format.timeZoneName) {
            const year = date.getFullYear();
            const recognizableAsTwentyCentury = String(year).length < 3;
            const safeYearShift = 400;
            const temporaryYearValue = recognizableAsTwentyCentury ? year + safeYearShift : year;
            const utcDate = new Date(Date.UTC(temporaryYearValue, date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));
            if (recognizableAsTwentyCentury) {
                utcDate.setFullYear(year);
            }
            const utcFormat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                timeZone: "UTC"
            }, format);
            return formatDateTime(utcDate, utcFormat);
        }
        return formatDateTime(date, format);
    };
const formattersCache = {};
const getFormatter = (format)=>{
    const key = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale() + "/" + JSON.stringify(format);
    if (!formattersCache[key]) {
        formattersCache[key] = new Intl.DateTimeFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale(), format).format;
    }
    return formattersCache[key];
};
function formatDateTime(date, format) {
    return getFormatter(format)(date).replace(SYMBOLS_TO_REMOVE_REGEX, "").replace(NARROW_NO_BREAK_SPACE_REGEX, " ");
}
const formatNumber = (number)=>new Intl.NumberFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale()).format(number);
const getAlternativeNumeralsMap = (()=>{
    const numeralsMapCache = {};
    return (locale)=>{
        if (!(locale in numeralsMapCache)) {
            if ("0" === formatNumber(0)) {
                numeralsMapCache[locale] = false;
                return false;
            }
            numeralsMapCache[locale] = {};
            for(let i = 0; i < 10; ++i){
                numeralsMapCache[locale][formatNumber(i)] = i;
            }
        }
        return numeralsMapCache[locale];
    };
})();
const normalizeNumerals = (dateString)=>{
    const alternativeNumeralsMap = getAlternativeNumeralsMap(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale());
    if (!alternativeNumeralsMap) {
        return dateString;
    }
    return dateString.split("").map((sign)=>sign in alternativeNumeralsMap ? String(alternativeNumeralsMap[sign]) : sign).join("");
};
const removeLeadingZeroes = (str)=>str.replace(/(\D)0+(\d)/g, "$1$2");
const dateStringEquals = (actual, expected)=>removeLeadingZeroes(actual) === removeLeadingZeroes(expected);
const normalizeMonth = (text)=>text.replace("d\u2019", "de ");
const intlFormats = {
    day: {
        day: "numeric"
    },
    date: {
        year: "numeric",
        month: "long",
        day: "numeric"
    },
    dayofweek: {
        weekday: "long"
    },
    longdate: {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric"
    },
    longdatelongtime: {
        weekday: "long",
        year: "numeric",
        month: "long",
        day: "numeric",
        hour: "numeric",
        minute: "numeric",
        second: "numeric"
    },
    longtime: {
        hour: "numeric",
        minute: "numeric",
        second: "numeric"
    },
    month: {
        month: "long"
    },
    monthandday: {
        month: "long",
        day: "numeric"
    },
    monthandyear: {
        year: "numeric",
        month: "long"
    },
    shortdate: {},
    shorttime: {
        hour: "numeric",
        minute: "numeric"
    },
    shortyear: {
        year: "2-digit"
    },
    year: {
        year: "numeric"
    }
};
Object.defineProperty(intlFormats, "shortdateshorttime", {
    get: function() {
        const defaultOptions = Intl.DateTimeFormat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale()).resolvedOptions();
        return {
            year: defaultOptions.year,
            month: defaultOptions.month,
            day: defaultOptions.day,
            hour: "numeric",
            minute: "numeric"
        };
    }
});
const getIntlFormat = (format)=>"string" === typeof format && intlFormats[format.toLowerCase()];
const monthNameStrategies = {
    standalone: function(monthIndex, monthFormat) {
        const date = new Date(1999, monthIndex, 13, 1);
        const dateString = getIntlFormatter({
            month: monthFormat
        })(date);
        return dateString;
    },
    format: function(monthIndex, monthFormat) {
        const date = new Date(0, monthIndex, 13, 1);
        const dateString = normalizeMonth(getIntlFormatter({
            day: "numeric",
            month: monthFormat
        })(date));
        const parts = dateString.split(" ").filter((part)=>part.indexOf("13") < 0);
        if (1 === parts.length) {
            return parts[0];
        } else if (2 === parts.length) {
            return parts[0].length > parts[1].length ? parts[0] : parts[1];
        }
        return monthNameStrategies.standalone(monthIndex, monthFormat);
    }
};
const __TURBOPACK__default__export__ = {
    engine: function() {
        return "intl";
    },
    getMonthNames: function(format, type) {
        const monthFormat = {
            wide: "long",
            abbreviated: "short",
            narrow: "narrow"
        }[format || "wide"];
        type = "format" === type ? type : "standalone";
        return Array.apply(null, new Array(12)).map((_, monthIndex)=>monthNameStrategies[type](monthIndex, monthFormat));
    },
    getDayNames: function(format) {
        const result = ((format)=>Array.apply(null, new Array(7)).map((_, dayIndex)=>getIntlFormatter({
                    weekday: format
                })(new Date(0, 0, dayIndex))))({
            wide: "long",
            abbreviated: "short",
            short: "narrow",
            narrow: "narrow"
        }[format || "wide"]);
        return result;
    },
    getPeriodNames: function() {
        const hour12Formatter = getIntlFormatter({
            hour: "numeric",
            hour12: true
        });
        return [
            1,
            13
        ].map((hours)=>{
            const hourNumberText = formatNumber(1);
            const timeParts = hour12Formatter(new Date(0, 0, 1, hours)).split(hourNumberText);
            if (2 !== timeParts.length) {
                return "";
            }
            const biggerPart = timeParts[0].length > timeParts[1].length ? timeParts[0] : timeParts[1];
            return biggerPart.trim();
        });
    },
    format: function(date, format) {
        if (!date) {
            return;
        }
        if (!format) {
            return date;
        }
        if ("function" !== typeof format && !format.formatter) {
            format = format.type || format;
        }
        const intlFormat = getIntlFormat(format);
        if (intlFormat) {
            return getIntlFormatter(intlFormat)(date);
        }
        const formatType = typeof format;
        if (format.formatter || "function" === formatType || "string" === formatType) {
            return this.callBase.apply(this, arguments);
        }
        return getIntlFormatter(format)(date);
    },
    parse: function(dateString, format) {
        let formatter;
        if (format && !format.parser && "string" === typeof dateString) {
            dateString = normalizeMonth(dateString);
            formatter = (date)=>normalizeMonth(this.format(date, format));
        }
        return this.callBase(dateString, formatter || format);
    },
    _parseDateBySimpleFormat: function(dateString, format) {
        dateString = normalizeNumerals(dateString);
        const formatParts = this.getFormatParts(format);
        const dateParts = dateString.split(/\D+/).filter((part)=>part.length > 0);
        if (formatParts.length !== dateParts.length) {
            return;
        }
        const dateArgs = this._generateDateArgs(formatParts, dateParts);
        const constructValidDate = (ampmShift)=>{
            const parsedDate = ((dateArgs, ampmShift)=>{
                const hoursShift = ampmShift ? 12 : 0;
                return new Date(dateArgs.year, dateArgs.month, dateArgs.day, (dateArgs.hours + hoursShift) % 24, dateArgs.minutes, dateArgs.seconds);
            })(dateArgs, ampmShift);
            if (dateStringEquals(normalizeNumerals(this.format(parsedDate, format)), dateString)) {
                return parsedDate;
            }
        };
        return constructValidDate(false) || constructValidDate(true);
    },
    _generateDateArgs: function(formatParts, dateParts) {
        const currentDate = new Date;
        const dateArgs = {
            year: currentDate.getFullYear(),
            month: currentDate.getMonth(),
            day: currentDate.getDate(),
            hours: 0,
            minutes: 0,
            seconds: 0
        };
        formatParts.forEach((formatPart, index)=>{
            const datePart = dateParts[index];
            let parsed = parseInt(datePart, 10);
            if ("month" === formatPart) {
                parsed -= 1;
            }
            dateArgs[formatPart] = parsed;
        });
        return dateArgs;
    },
    formatUsesMonthName: function(format) {
        if ("object" === typeof format && !(format.type || format.format)) {
            return "long" === format.month;
        }
        return this.callBase.apply(this, arguments);
    },
    formatUsesDayName: function(format) {
        if ("object" === typeof format && !(format.type || format.format)) {
            return "long" === format.weekday;
        }
        return this.callBase.apply(this, arguments);
    },
    getTimeSeparator: function() {
        return normalizeNumerals(formatDateTime(new Date(2001, 1, 1, 11, 11), {
            hour: "numeric",
            minute: "numeric",
            hour12: false
        })).replace(/\d/g, "");
    },
    getFormatParts: function(format) {
        if ("string" === typeof format) {
            return this.callBase(format);
        }
        const intlFormat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, intlFormats[format.toLowerCase()]);
        const date = new Date(2001, 2, 4, 5, 6, 7);
        let formattedDate = getIntlFormatter(intlFormat)(date);
        formattedDate = normalizeNumerals(formattedDate);
        return [
            {
                name: "year",
                value: 1
            },
            {
                name: "month",
                value: 3
            },
            {
                name: "day",
                value: 4
            },
            {
                name: "hours",
                value: 5
            },
            {
                name: "minutes",
                value: 6
            },
            {
                name: "seconds",
                value: 7
            }
        ].map((part)=>({
                name: part.name,
                index: formattedDate.indexOf(part.value)
            })).filter((part)=>part.index > -1).sort((a, b)=>a.index - b.index).map((part)=>part.name);
    }
};
}),
"[project]/node_modules/devextreme/esm/common/core/localization/date.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization/date.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$date$2e$formatter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/ldml/date.formatter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$date$2e$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/ldml/date.format.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$date$2e$parser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/ldml/date.parser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_date_names$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/default_date_names.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$cldr$2d$data$2f$first_day_of_week_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/cldr-data/first_day_of_week_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/number.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$intl$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/intl/date.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const DEFAULT_DAY_OF_WEEK_INDEX = 0;
const hasIntl = "undefined" !== typeof Intl;
const FORMATS_TO_PATTERN_MAP = {
    shortdate: "M/d/y",
    shorttime: "h:mm a",
    longdate: "EEEE, MMMM d, y",
    longtime: "h:mm:ss a",
    monthandday: "MMMM d",
    monthandyear: "MMMM y",
    quarterandyear: "QQQ y",
    day: "d",
    year: "y",
    shortdateshorttime: "M/d/y, h:mm a",
    longdatelongtime: "EEEE, MMMM d, y, h:mm:ss a",
    month: "LLLL",
    shortyear: "yy",
    dayofweek: "EEEE",
    quarter: "QQQ",
    hour: "HH",
    minute: "mm",
    second: "ss",
    millisecond: "SSS",
    "datetime-local": "yyyy-MM-ddTHH':'mm':'ss"
};
const possiblePartPatterns = {
    year: [
        "y",
        "yy",
        "yyyy"
    ],
    day: [
        "d",
        "dd"
    ],
    month: [
        "M",
        "MM",
        "MMM",
        "MMMM"
    ],
    hours: [
        "H",
        "HH",
        "h",
        "hh",
        "ah"
    ],
    minutes: [
        "m",
        "mm"
    ],
    seconds: [
        "s",
        "ss"
    ],
    milliseconds: [
        "S",
        "SS",
        "SSS"
    ]
};
const dateLocalization = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    engine: function() {
        return "base";
    },
    _getPatternByFormat: function(format) {
        return FORMATS_TO_PATTERN_MAP[format.toLowerCase()];
    },
    _expandPattern: function(pattern) {
        return this._getPatternByFormat(pattern) || pattern;
    },
    formatUsesMonthName: function(format) {
        return -1 !== this._expandPattern(format).indexOf("MMMM");
    },
    formatUsesDayName: function(format) {
        return -1 !== this._expandPattern(format).indexOf("EEEE");
    },
    getFormatParts: function(format) {
        const pattern = this._getPatternByFormat(format) || format;
        const result = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(pattern.split(/\W+/), (_, formatPart)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(possiblePartPatterns, (partName, possiblePatterns)=>{
                if (possiblePatterns.includes(formatPart)) {
                    result.push(partName);
                }
            });
        });
        return result;
    },
    getMonthNames: function(format) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_date_names$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getMonthNames(format);
    },
    getDayNames: function(format) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_date_names$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDayNames(format);
    },
    getQuarterNames: function(format) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_date_names$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getQuarterNames(format);
    },
    getPeriodNames: function(format) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$default_date_names$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPeriodNames(format);
    },
    getTimeSeparator: function() {
        return ":";
    },
    is24HourFormat: function(format) {
        const amTime = new Date(2017, 0, 20, 11, 0, 0, 0);
        const pmTime = new Date(2017, 0, 20, 23, 0, 0, 0);
        const amTimeFormatted = this.format(amTime, format);
        const pmTimeFormatted = this.format(pmTime, format);
        for(let i = 0; i < amTimeFormatted.length; i++){
            if (amTimeFormatted[i] !== pmTimeFormatted[i]) {
                return !isNaN(parseInt(amTimeFormatted[i]));
            }
        }
    },
    format: function(date, format) {
        if (!date) {
            return;
        }
        if (!format) {
            return date;
        }
        let formatter;
        if ("function" === typeof format) {
            formatter = format;
        } else if (format.formatter) {
            formatter = format.formatter;
        } else {
            format = format.type || format;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isString"])(format)) {
                format = FORMATS_TO_PATTERN_MAP[format.toLowerCase()] || format;
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertDigits((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$date$2e$formatter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFormatter"])(format, this)(date));
            }
        }
        if (!formatter) {
            return;
        }
        return formatter(date);
    },
    parse: function(text, format) {
        const that = this;
        let ldmlFormat;
        let formatter;
        if (!text) {
            return;
        }
        if (!format) {
            return this.parse(text, "shortdate");
        }
        if (format.parser) {
            return format.parser(text);
        }
        if ("string" === typeof format && !FORMATS_TO_PATTERN_MAP[format.toLowerCase()]) {
            ldmlFormat = format;
        } else {
            formatter = (value)=>{
                const text = that.format(value, format);
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertDigits(text, true);
            };
            try {
                ldmlFormat = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$date$2e$format$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFormat"])(formatter);
            } catch (e) {}
        }
        if (ldmlFormat) {
            text = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].convertDigits(text, true);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$ldml$2f$date$2e$parser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getParser"])(ldmlFormat, this)(text);
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].log("W0012");
        const result = new Date(text);
        if (!result || isNaN(result.getTime())) {
            return;
        }
        return result;
    },
    firstDayOfWeekIndex: function() {
        const index = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getValueByClosestLocale((locale)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$cldr$2d$data$2f$first_day_of_week_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"][locale]);
        return void 0 === index ? 0 : index;
    }
});
if (hasIntl) {
    dateLocalization.inject(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$intl$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
}
const __TURBOPACK__default__export__ = dateLocalization;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/emitter.feedback.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/emitter.feedback.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$feedback$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.feedback.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/emitter.feedback.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$feedback$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.feedback.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$feedback$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.feedback.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/hover.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/hover.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hover$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_hover.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/hover.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hover$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_hover.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$hover$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/hover.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/keyboard_processor.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/core/keyboard_processor.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_keyboard_processor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_keyboard_processor.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/core/keyboard_processor.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_keyboard_processor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_keyboard_processor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$keyboard_processor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/keyboard_processor.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/short.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/short.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_short$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_short.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/short.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_short$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_short.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$short$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/short.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/visibility_change.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/visibility_change.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "triggerHidingEvent": ()=>triggerHidingEvent,
    "triggerResizeEvent": ()=>triggerResizeEvent,
    "triggerShownEvent": ()=>triggerShownEvent
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_visibility_change$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_visibility_change.js [app-client] (ecmascript)");
;
const triggerShownEvent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_visibility_change$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].triggerShownEvent;
const triggerHidingEvent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_visibility_change$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].triggerHidingEvent;
const triggerResizeEvent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_visibility_change$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].triggerResizeEvent;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_visibility_change$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript) <export default as fx>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "fx": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript)");
}),
"[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/gesture/emitter.gesture.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$gesture$2f$emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/drag.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.scroll.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/gesture/emitter.gesture.scroll.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$scroll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.scroll.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.scroll.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$scroll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.scroll.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$gesture$2f$emitter$2e$gesture$2e$scroll$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.scroll.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/hold.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/hold.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_hold.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/hold.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_hold.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$hold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/hold.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/contextmenu.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/contextmenu.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_contextmenu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_contextmenu.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/contextmenu.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_contextmenu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_contextmenu.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$contextmenu$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/contextmenu.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/swipe.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/swipe.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_swipe.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/swipe.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_swipe.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/swipe.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events/gesture/swipeable.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/gesture/swipeable.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_swipeable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_swipeable.js [app-client] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/common/core/events/gesture/swipeable.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_swipeable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_swipeable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$gesture$2f$swipeable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/gesture/swipeable.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common/core/events.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Event": ()=>Event,
    "off": ()=>off,
    "on": ()=>on,
    "one": ()=>one,
    "trigger": ()=>trigger
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
;
const on = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on;
const one = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].one;
const off = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off;
const trigger = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger;
const Event = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Event;
}),
"[project]/node_modules/devextreme/esm/common/core/events/transform.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/events/transform.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "pinch": ()=>pinch,
    "pinchend": ()=>pinchend,
    "pinchstart": ()=>pinchstart,
    "rotate": ()=>rotate,
    "rotateend": ()=>rotateend,
    "rotatestart": ()=>rotatestart,
    "transform": ()=>transform,
    "transformend": ()=>transformend,
    "transformstart": ()=>transformstart,
    "translate": ()=>translate,
    "translateend": ()=>translateend,
    "translatestart": ()=>translatestart,
    "zoom": ()=>zoom,
    "zoomend": ()=>zoomend,
    "zoomstart": ()=>zoomstart
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_transform$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_transform.js [app-client] (ecmascript)");
;
const { transformstart: transformstart, transform: transform, transformend: transformend, translatestart: translatestart, translate: translate, translateend: translateend, zoomstart: zoomstart, zoom: zoom, zoomend: zoomend, pinchstart: pinchstart, pinch: pinch, pinchend: pinchend, rotatestart: rotatestart, rotate: rotate, rotateend: rotateend } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_transform$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["exportNames"];
}),
"[project]/node_modules/devextreme/esm/common/core/localization.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common/core/localization.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "disableIntl": ()=>disableIntl,
    "formatDate": ()=>formatDate,
    "formatMessage": ()=>formatMessage,
    "formatNumber": ()=>formatNumber,
    "loadMessages": ()=>loadMessages,
    "locale": ()=>locale,
    "parseDate": ()=>parseDate,
    "parseNumber": ()=>parseNumber
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/message.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/number.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$currency$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/currency.js [app-client] (ecmascript)");
;
;
;
;
;
const locale = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].locale.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const loadMessages = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].load.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const formatMessage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const formatNumber = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const parseNumber = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].parse.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const formatDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
const parseDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].parse.bind(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
;
function disableIntl() {
    if ("intl" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].engine()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resetInjection();
    }
    if ("intl" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].engine()) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resetInjection();
    }
}
}),
"[project]/node_modules/devextreme/esm/common/core/localization.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$core$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/core.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/message.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/number.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$currency$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/currency.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization.js [app-client] (ecmascript) <locals>");
}),
}]);

//# sourceMappingURL=node_modules_devextreme_esm_common_3ae4b878._.js.map