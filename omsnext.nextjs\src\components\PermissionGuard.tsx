// src/components/PermissionGuard.tsx
"use client";

import { ReactNode, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/hooks/useAuth";
import { hasPermission, Permission } from "@/lib/permissions";

interface PermissionGuardProps {
  children: ReactNode;
  permission: Permission;
}

export default function PermissionGuard({
  children,
  permission,
}: PermissionGuardProps) {
  const router = useRouter();
  const { user, loading, authenticated } = useAuth();

  useEffect(() => {
    // Redirect to login if not authenticated
    if (!loading && !authenticated) {
      router.push("/login");
    }

    // Redirect to dashboard if authenticated but doesn't have permission
    if (
      !loading &&
      authenticated &&
      user &&
      !hasPermission(user.roles as any, permission)
    ) {
      router.push("/dashboard");
    }
  }, [authenticated, loading, permission, router, user]);

  // Show nothing while checking permissions
  if (loading || !authenticated || !user) {
    return <div>Loading...</div>;
  }

  // Check if user has the required permission
  if (!hasPermission(user.roles as any, permission)) {
    return <div>Nincs jogosultsága az oldal megtekintéséhez.</div>;
  }

  return <>{children}</>;
}
