﻿using System.Windows;
using DevExpress.Xpf.Core;
using omsnext.wpf.Services;

namespace omsnext.wpf;

public partial class LoginWindow : ThemedWindow
{
    private readonly ApiClient _apiClient;

    public LoginWindow()
    {
        InitializeComponent();
        _apiClient = new ApiClient();
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        ErrorMessage.Visibility = Visibility.Collapsed;
        LoadingIndicator.DeferedVisibility = true;
        LoginButton.IsEnabled = false;

        try
        {
            var email = EmailTextEdit.Text?.Trim();
            var password = PasswordBoxEdit.Password;

            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
            {
                ShowError("K<PERSON>rjük, töltse ki az összes mezőt!");
                return;
            }

            // Itt: LoginResponse-t várunk vissza!
            var loginResponse = await _apiClient.LoginAsync(email, password);

            if (loginResponse != null && !string.IsNullOrEmpty(loginResponse.Token))
            {
                // Továbbadod az ApiClient-et vagy a LoginResponse.User-t
                var mainWindow = new MainWindow(loginResponse, _apiClient); // vagy loginResponse.User, _apiClient
                mainWindow.Show();
                this.Close();
            }
            else
            {
                ShowError("Hibás e-mail cím vagy jelszó!");
            }
        }
        catch (Exception ex)
        {
            ShowError($"Hiba történt: {ex.Message}");
        }
        finally
        {
            LoadingIndicator.DeferedVisibility = false;
            LoginButton.IsEnabled = true;
        }
    }

    private void ShowError(string message)
    {
        ErrorMessage.Text = message;
        ErrorMessage.Visibility = Visibility.Visible;
    }
}