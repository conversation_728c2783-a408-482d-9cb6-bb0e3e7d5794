﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>DevExpress.Xpf.NavBar.v25.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Xpf.NavBar">
      <summary>
        <para>Contains classes which implement the main functionality of the DXNavBar for WPF suite. To use these classes in XAML code, add the xmlns:dxn=”“ namespace reference.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.DisplayMode">
      <summary>
        <para>Contains values that specify by which characteristics (the text, image, or both) content elements (such as group headers or items) should be represented within the view.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.DisplayMode.Default">
      <summary>
        <para>Acts identically to the ImageAndText value.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.DisplayMode.Image">
      <summary>
        <para>Only an image is used to represent the content of the related content element (an item or group header); the text is not displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.DisplayMode.ImageAndText">
      <summary>
        <para>Both text and an image are used to represent the content of the related content element (an item or group header).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.DisplayMode.Text">
      <summary>
        <para>Only text is used to represent the content of the related content element (an item or group header); the image is not displayed, if any.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.DisplaySource">
      <summary>
        <para>Contains values that specify which property (<see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Items">NavBarGroup.Items</see> or <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Content">NavBarGroup.Content</see>) defines the group’s content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.DisplaySource.Content">
      <summary>
        <para>Specifies that group content is represented by the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Content">NavBarGroup.Content</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.DisplaySource.Items">
      <summary>
        <para>Specifies that group content is represented by the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Items">NavBarGroup.Items</see> property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.Element">
      <summary>
        <para>Contains values that identify a visual element corresponding to an object to which this property applies.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.Element.ActiveGroup">
      <summary>
        <para>Identifies the active group in the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">expanded Navigation Pane</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.Element.CollapsedActiveGroup">
      <summary>
        <para>Identifies the active group in the <see href="https://docs.devexpress.com/WPF/6690/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/collapsed-navigation-pane">collapsed Navigation Pane</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.Element.GroupButtonPanel">
      <summary>
        <para>Identifies the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.Element.OverflowPanel">
      <summary>
        <para>Identifies the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ElementAddingEventArgs">
      <summary>
        <para>Provides data for events which concern adding an item or group.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ElementAddingEventArgs.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.ElementAddingEventArgs"/> class with the specified setting.</para>
      </summary>
      <param name="sourceObject">The data item object that supplies data for a NavBar element (an item or group) related to the event.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ElementAddingEventArgs.SourceObject">
      <summary>
        <para>Gets the data item to which the event related element (a group or item) is bound.</para>
      </summary>
      <value>The data item object that supplies data for an added NavBar element (group or item).</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ExpandButtonMode">
      <summary>
        <para>Contains values that specify the display mode of the <see href="https://docs.devexpress.com/WPF/6695/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-expand-button">expand button</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ExpandButtonMode.Inverted">
      <summary>
        <para>Specifies the inverted display mode of the <see href="https://docs.devexpress.com/WPF/6695/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-expand-button">expand button</see> within the <see href="https://docs.devexpress.com/WPF/6690/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/collapsed-navigation-pane">collapsed Navigation Pane</see>. This mode is useful when the Navigation Pane is collapsed to the right (the NavBarControl’s HorizontalAlignment property is set to Right).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ExpandButtonMode.Normal">
      <summary>
        <para>Specifies the normal (default) display mode of the <see href="https://docs.devexpress.com/WPF/6695/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-expand-button">expand button</see> within the <see href="https://docs.devexpress.com/WPF/6690/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/collapsed-navigation-pane">collapsed Navigation Pane</see>. This mode is useful when the Navigation Pane is collapsed to the left (the NavBarControl’s HorizontalAlignment property is set to Left).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ExplorerBarView">
      <summary>
        <para>Represents a view that allows multiple groups to be expanded at the same time.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ExplorerBarView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.ExplorerBarView"/> class with default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanged">
      <summary>
        <para>Fires after the expanded state of a group has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanged">ExplorerBarView.GroupExpandedChanged</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanging">
      <summary>
        <para>Fires before the expanded state of a group is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChangingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanging">ExplorerBarView.GroupExpandedChanging</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ExplorerBarView.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.FontSettings">
      <summary>
        <para>Contains font settings, such as font family, size and weight, applied to the nav bar group and item captions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.FontSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.FontSettings"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.FontSettings.AddOwner(System.Windows.Controls.Control)">
      <summary>
        <para>Adds the specified control to the list of owners of the current settings.</para>
      </summary>
      <param name="control">A Control object to add to the list of owners of the current settings.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.FontSettings.CheckAllProperties(System.Windows.Controls.Control)">
      <summary>
        <para>Checks the font settings of the specified control and updates the property values, if necessary, according to the current settings.</para>
      </summary>
      <param name="control">A <see cref="T:System.Windows.Controls.Control"/> on which to check the font settings.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.FontSettings.Default">
      <summary>
        <para>Provides access to the default font settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.FontSettings"/> object that comprises the default font settings.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.FontSettings.FontFamily">
      <summary>
        <para>Gets or sets the family of the specified font.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.FontFamily"/> object representing the family of the specified font.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.FontSettings.FontSize">
      <summary>
        <para>Gets or sets the size of the specified font.</para>
      </summary>
      <value>A Double structure representing the size of the specified font.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.FontSettings.FontStretch">
      <summary>
        <para>Gets or sets the degree to which a font is condensed or expanded on the screen.</para>
      </summary>
      <value>A <see cref="T:System.Windows.FontStretch"/> structure representing the degree to which a font is condensed or expanded on the screen.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.FontSettings.FontStyle">
      <summary>
        <para>Gets or sets the style of the specified font.</para>
      </summary>
      <value>A <see cref="T:System.Windows.FontStyle"/> structure representing the style of the specified font.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.FontSettings.FontWeight">
      <summary>
        <para>Gets or sets the weight or thickness of the specified font.</para>
      </summary>
      <value>A <see cref="T:System.Windows.FontWeight"/> structure representing the font weight or thickness of the specified font.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.FontSettings.PropertyChanged">
      <summary>
        <para>Occurs when a property value changes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.FontSettings.RemoveOwner(System.Windows.Controls.Control)">
      <summary>
        <para>Removes the specified control from the list of owners of the current settings.</para>
      </summary>
      <param name="control">A Control object to remove from the list of owners of the current settings.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.FontSettings.SetValueIfNotDefaultOrClearIfUnset(System.Windows.Controls.Control,System.String)">
      <summary>
        <para>Sets the specified dependency property on the specified control according to the value contained the current settings, or clears the dependency property if the corresponding value is not specified in the current settings.</para>
      </summary>
      <param name="control">A Control on which to set the dependency property.</param>
      <param name="propertyName">A string value that specifies name of the dependency property to set.</param>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.FontSettings.WeakPropertyChanged">
      <summary>
        <para>Occurs when a property value changes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.GroupAddingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.GroupAdding">NavBarViewBase.GroupAdding</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.GroupAddingEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.GroupAddingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group related to the event.</param>
      <param name="sourceObject">The data item object that supplies data for the NavBar group being added.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.GroupAddingEventArgs.Group">
      <summary>
        <para>Gets a group object related to the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object being added to the NavBar control.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.GroupAddingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.GroupAdding">NavBarViewBase.GroupAdding</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.GroupAddingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.GroupPosition">
      <summary>
        <para>Contains values that identify a group’s position within the control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.GroupPosition.First">
      <summary>
        <para>The group corresponds to the first visible group header (in the Navigation Pane - to the first header displayed within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.GroupPosition.Last">
      <summary>
        <para>The group corresponds to the last visible group header (in the Navigation Pane - to the last header displayed within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.GroupPosition.Middle">
      <summary>
        <para>The group corresponds to a middle header among the visible group headers (in the Navigation Pane - to a middle header displayed within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>)</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.GroupPosition.Single">
      <summary>
        <para>The group corresponds to a single visible group header (in the Navigation Pane - to a single header displayed within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ImageSettings">
      <summary>
        <para>Contains settings that define images to be displayed within group headers or items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ImageSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.ImageSettings"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ImageSettings.GroupDefault">
      <summary>
        <para>Stores default image settings for <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s.</para>
      </summary>
      <value>An ImageSettings object that stores default image settings for <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ImageSettings.Height">
      <summary>
        <para>Gets or set the image height.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Double"/>The height of an image, in device-independent units (1/96th inch per unit).</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ImageSettings.ItemDefault">
      <summary>
        <para>Stores default image settings for <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s.</para>
      </summary>
      <value>An ImageSettings object that stores default image settings for <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.ImageSettings.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.Xpf.NavBar.ImageSettings"/> properties has changed its value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ImageSettings.Stretch">
      <summary>
        <para>Gets or sets a value that specifies how an image should be stretched to fill the available space.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Media.Stretch"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ImageSettings.StretchDirection">
      <summary>
        <para>Gets or sets a value that specifies how an image is scaled.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Controls.StretchDirection"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ImageSettings.Width">
      <summary>
        <para>Gets or set the image width.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Double"/>The width of an image, in device-independent units (1/96th inch per unit).</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ItemAddingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemAdding">NavBarViewBase.ItemAdding</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ItemAddingEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarItem,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.ItemAddingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the item related to the event.</param>
      <param name="sourceObject">The data item object that supplies data for the NavBar item being added.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.ItemAddingEventArgs.Item">
      <summary>
        <para>Gets an item object related to the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object being added to the NavBar control.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ItemAddingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemAdding">NavBarViewBase.ItemAdding</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.ItemAddingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.LayoutSettings">
      <summary>
        <para>Contains layout settings to arrange images and text displayed by group headers or items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.LayoutSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.LayoutSettings"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.LayoutSettings.Default">
      <summary>
        <para>Stores default layout settings.</para>
      </summary>
      <value>A LayoutSettings object that stores default values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.LayoutSettings.ImageDocking">
      <summary>
        <para>Gets or sets a value that specifies the dock position of an image within a parent’s layout slot.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Controls.Dock"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.LayoutSettings.ImageHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the image within a parent’s layout slot.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.HorizontalAlignment"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.LayoutSettings.ImageVerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of an image within a parent’s layout slot.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.VerticalAlignment"/> enumeration values.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.LayoutSettings.PropertyChanged">
      <summary>
        <para>Occurs every time any of the <see cref="T:DevExpress.Xpf.NavBar.LayoutSettings"/> properties has changed its value.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.LayoutSettings.TextHorizontalAlignment">
      <summary>
        <para>Gets or sets the horizontal alignment of the text within a parent’s layout slot.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.HorizontalAlignment"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.LayoutSettings.TextVerticalAlignment">
      <summary>
        <para>Gets or sets the vertical alignment of the text within a parent’s layout slot.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.VerticalAlignment"/> enumeration values.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanged">NavBarViewBase.ActiveGroupChanged</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarActiveGroupChangedEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangedEventArgs"/> class with the specified setting.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group related to the generated event.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarActiveGroupChangedEventArgs.Group">
      <summary>
        <para>Gets a group object related to the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object, manipulations on which forced the event to be raised.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanged">NavBarViewBase.ActiveGroupChanged</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanging">NavBarViewBase.ActiveGroupChanging</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup,DevExpress.Xpf.NavBar.NavBarGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="prevGroup">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the previously active group.</param>
      <param name="newGroup">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group that is going to be activated.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs.Cancel">
      <summary>
        <para>Gets or sets a value indicating whether the operation which raised an event should be canceled.</para>
      </summary>
      <value>true if the operation raising the event should be canceled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs.NewGroup">
      <summary>
        <para>Gets a group object that is going to be activated.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group being activated.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs.PrevGroup">
      <summary>
        <para>Gets the previously activated group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the previously active group.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanging">NavBarViewBase.ActiveGroupChanging</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavBarActiveGroupChangingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarCommands">
      <summary>
        <para>Provides access to common navbar commands.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarCommands.ChangeGroupExpanded">
      <summary>
        <para>Toggles the specified group’s expanded state.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.RoutedCommand"/> object that defines the command.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarCommands.SelectItem">
      <summary>
        <para>Selects the specified item.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.RoutedCommand"/> object that defines the command.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarCommands.SetActiveGroup">
      <summary>
        <para>Activates the specified group.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.RoutedCommand"/> object that defines the command.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarControl">
      <summary>
        <para>A control that enables you to implement a sidebar menu or allow users to switch between application modules. Supports three layouts: Navigation Pane, Explorer Bar View, and Side Bar View.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.ActiveGroup">
      <summary>
        <para>Gets or sets the currently active group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the currently active group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.ActiveGroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ActiveGroup">NavBarControl.ActiveGroup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.AllowSelectDisabledItem">
      <summary>
        <para>Gets or sets a value that specifies whether disabled items are allowed to be selected.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if disabled items can be selected; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.AllowSelectDisabledItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.AllowSelectDisabledItem">NavBarControl.AllowSelectDisabledItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.AllowSelectItem">
      <summary>
        <para>Gets or sets a value specifying whether items can be selected.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if items can be selected; otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.AllowSelectItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.AllowSelectItem">NavBarControl.AllowSelectItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.Compact">
      <summary>
        <para>Toggles the Navigation Bar’s Compact mode on or off.</para>
      </summary>
      <value>true, if the Compact mode is enabled; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarControl.CreateDefaultView">
      <summary>
        <para>Creates an <see cref="T:DevExpress.Xpf.NavBar.ExplorerBarView"/> class instance that represents the default view within the DXNavBar control.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant (<see cref="T:DevExpress.Xpf.NavBar.ExplorerBarView"/>) representing the control’s default view.</returns>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.EachGroupHasSelectedItem">
      <summary>
        <para>Gets or sets a value specifying whether each group can have a selected item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if each group can have a selected item; otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.EachGroupHasSelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.EachGroupHasSelectedItem">NavBarControl.EachGroupHasSelectedItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.GroupDescription">
      <summary>
        <para>Gets or sets the name of a data source field, by whose values to group data source items represented by the NavBar control.</para>
      </summary>
      <value>A string value that specifies the name of the required data source field.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.GroupDescriptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.GroupDescription">NavBarControl.GroupDescription</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.Groups">
      <summary>
        <para>Gets the collection of groups in the NavBarControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupCollection"/> object which represents the collection of groups within the NavBarControl.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.ItemsSource">
      <summary>
        <para>Gets or sets the data source that is used to generate the content of the NavBar control.</para>
      </summary>
      <value>A data source object that implements the <see cref="T:System.Collections.IEnumerable"/> interface.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemsSource">NavBarControl.ItemsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.ItemStyle">
      <summary>
        <para>Gets or sets a style applied to <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> objects defined as <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplate">NavBarControl.ItemTemplate</see> content. This is a dependency property.</para>
      </summary>
      <value>A Style object that contains custom style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.ItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemStyle">NavBarControl.ItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemsSource">NavBarControl.ItemsSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate that is the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplate">NavBarControl.ItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemsSource">NavBarControl.ItemsSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.ItemTemplateSelector">NavBarControl.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarControl.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarControl.ReloadGroups">
      <summary>
        <para>Recreates the collection of groups and items by reloading data from the bound data source.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.SelectedGroup">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> currently selected within this <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/>. This is a dependency property.</para>
      </summary>
      <value>An object that is the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> currently selected within this <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.SelectedGroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.SelectedGroup">NavBarControl.SelectedGroup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.SelectedItem">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> currently selected within this <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/>.</para>
      </summary>
      <value>An Object that is the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> currently selected within this <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.SelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.SelectedItem">NavBarControl.SelectedItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.SelectedItems">
      <summary>
        <para>Gets or sets the collection of <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s currently selected within this <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/>.</para>
      </summary>
      <value>An IEnumerable object that stores <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s currently selected within this <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.SelectedItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.SelectedItems">NavBarControl.SelectedItems</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarControl.View">
      <summary>
        <para>Gets or sets an object specifying the control’s display style.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant specifying the control’s view used to display data.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarControl.ViewProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.View">NavBarControl.View</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarGroup">
      <summary>
        <para>Represents an individual group within the NavBarControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> class with default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarGroup.Activate">
      <summary>
        <para>Fires when the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is activated.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualDisplayMode">
      <summary>
        <para>Gets the display mode for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</para>
      </summary>
      <value>A DisplayMode enumerator value currently applied to this <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualDisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualDisplayMode">NavBarGroup.ActualDisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualDisplayModePropertyKey">
      <summary>
        <para>Provides a dependency property identifier for limited write access to a read-only <see cref="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualDisplayModeProperty">NavBarGroup.ActualDisplayModeProperty</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualFontSettings">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s current font settings.</para>
      </summary>
      <value>A FontSettings object that contains font settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualFontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualFontSettings">NavBarGroup.ActualFontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualGroupHeaderTemplate">
      <summary>
        <para>Gets or sets the actual template applied to the group’s header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object that specifies the actual template applied to the group’s header.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualGroupHeaderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualGroupHeaderTemplate">NavBarGroup.ActualGroupHeaderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualHeaderTemplateSelector">
      <summary>
        <para>Gets the actual template selector that chooses a group header template based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualHeaderTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualHeaderTemplateSelector">NavBarGroup.ActualHeaderTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualImageSettings">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s current icon settings.</para>
      </summary>
      <value>An ImageSettings object that contains icon settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualImageSettings">NavBarGroup.ActualImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualImageSettingsPropertyKey">
      <summary>
        <para>Provides a dependency property identifier for limited write access to a read-only <see cref="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualImageSettingsProperty">NavBarGroup.ActualImageSettingsProperty</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualIsVisible">
      <summary>
        <para>Gets if this <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is currently visible. This is a dependency property.</para>
      </summary>
      <value>true if this <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is currently visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualIsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualIsVisible">NavBarGroup.ActualIsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualItemTemplateSelector">
      <summary>
        <para>Gets the actual template selector that chooses an item template based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualItemTemplateSelector">NavBarGroup.ActualItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualLayoutSettings">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s current icon and text layout settings.</para>
      </summary>
      <value>A LayoutSettings object that contains layout settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s icon and text.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualLayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualLayoutSettings">NavBarGroup.ActualLayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualLayoutSettingsPropertyKey">
      <summary>
        <para>Provides a dependency property identifier for limited write access to a read-only <see cref="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualLayoutSettingsProperty">NavBarGroup.ActualLayoutSettingsProperty</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualNavPaneGroupButtonTemplateSelector">
      <summary>
        <para>Gets the actual template selector that chooses a template for the group’s header represented within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>, based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualNavPaneGroupButtonTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualNavPaneGroupButtonTemplateSelector">NavBarGroup.ActualNavPaneGroupButtonTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualNavPaneOverflowGroupTemplateSelector">
      <summary>
        <para>Gets the actual template selector that chooses a template for an item representing the group within the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>, based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualNavPaneOverflowGroupTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualNavPaneOverflowGroupTemplateSelector">NavBarGroup.ActualNavPaneOverflowGroupTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualScrollMode">
      <summary>
        <para>Gets the actual scroll mode for a group.</para>
      </summary>
      <value>A ScrollMode value that specifies how a NavBar group is scrolled.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualScrollModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualScrollMode">NavBarGroup.ActualScrollMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualVisualStyle">
      <summary>
        <para>Gets the actual visual style applied to the group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that represents the visual style applied to the group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ActualVisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ActualVisualStyle">NavBarGroup.ActualVisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.AnimateGroupExpansion">
      <summary>
        <para>Gets whether the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is animated when expanding/collapsing.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is animated when expanding/collapsing; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.AnimateGroupExpansionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.AnimateGroupExpansion">NavBarGroup.AnimateGroupExpansion</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarGroup.Click">
      <summary>
        <para>Fires immediately after an end-user clicks the current group’s header.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItems">
      <summary>
        <para>Gets or sets the collection of items displayed in the <see href="https://docs.devexpress.com/WPF/6690/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/collapsed-navigation-pane">collapsed Navigation Pane</see> when the current group is active.</para>
      </summary>
      <value>The collection of items displayed in the collapsed Navigation Pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItems">NavBarGroup.CollapsedNavPaneItems</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize items for the collapsed Navigation Pane when the current group is active.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as collapsed state items.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsSource">NavBarGroup.CollapsedNavPaneItemsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsStyle">
      <summary>
        <para>Gets or sets the style applied to items in the collapsed Navigation Pane. This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsStyle">NavBarGroup.CollapsedNavPaneItemsStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsTemplate">
      <summary>
        <para>Gets or sets the DataTemplate used to render items in the collapsed Navigation Pane. This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object used to render items in the collapsed Navigation Pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsTemplate">NavBarGroup.CollapsedNavPaneItemsTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsTemplateSelector">
      <summary>
        <para>Gets or sets the DataTemplateSelector object that provides a way to choose a DataTemplate used to render items in the collapsed Navigation Pane. This is a dependency property.</para>
      </summary>
      <value>A DataTemplateSelector object that provides a way to choose a DataTemplate used to render items in the collapsed Navigation Pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneItemsTemplateSelector">NavBarGroup.CollapsedNavPaneItemsTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneSelectedItem">
      <summary>
        <para>Gets or sets the selected item of the current group in the collapsed Navigation Pane.</para>
      </summary>
      <value>An Object that represents the selected item of the current group in the collapsed Navigation Pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneSelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CollapsedNavPaneSelectedItem">NavBarGroup.CollapsedNavPaneSelectedItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.Command">
      <summary>
        <para>Gets or sets the command to invoke when the NavBar group is activated.</para>
      </summary>
      <value>The ICommand object to invoke when the NavBar group is activated.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CommandParameter">
      <summary>
        <para>Gets or sets the parameter to pass to the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Command">NavBarGroup.Command</see>.</para>
      </summary>
      <value>The Object specifying the parameter to pass to the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Command">NavBarGroup.Command</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CommandParameterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CommandParameter">NavBarGroup.CommandParameter</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CommandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Command">NavBarGroup.Command</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.CommandTarget">
      <summary>
        <para>Gets or sets the element on which to raise the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Command">NavBarGroup.Command</see>.</para>
      </summary>
      <value>The IInputElement on which to raise the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Command">NavBarGroup.Command</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.CommandTargetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.CommandTarget">NavBarGroup.CommandTarget</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.Content">
      <summary>
        <para>Gets or sets the group’s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An object that specifies the content of the group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Content">NavBarGroup.Content</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ContentTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of the group’s content when the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.DisplaySource">NavBarGroup.DisplaySource</see> is set to <see cref="F:DevExpress.Xpf.NavBar.DisplaySource.Content">DisplaySource.Content</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ContentTemplate">NavBarGroup.ContentTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.DisplayMode">
      <summary>
        <para>Gets or sets whether the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> should display its text, icon, or both icon and text. This is a dependency property.</para>
      </summary>
      <value>A DisplayMode enumerator value that specifies whether the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> should display its text, icon, or both icon and text.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.DisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.DisplayMode">NavBarGroup.DisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.DisplaySource">
      <summary>
        <para>Gets or sets a value that specifies which property (<see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Items">NavBarGroup.Items</see> or <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Content">NavBarGroup.Content</see>) defines the group’s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Xpf.NavBar.DisplaySource"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.DisplaySourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.DisplaySource">NavBarGroup.DisplaySource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.FontSettings">
      <summary>
        <para>Gets or sets an object that contains font settings for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</para>
      </summary>
      <value>A FontSettings object that contains properties to customize the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s font.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.FontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.FontSettings">NavBarGroup.FontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.GroupControl">
      <summary>
        <para>Gets or sets a NavBarGroupControl object used to draw the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> on-screen.</para>
      </summary>
      <value>A NavBarGroupControl object used to draw the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> on-screen.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.GroupHeaderTemplate">
      <summary>
        <para>Gets or sets a template, which defines how data is represented in the group’s header area.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object, which represents a template for the group’s header.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.GroupHeaderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.GroupHeaderTemplate">NavBarGroup.GroupHeaderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.GroupScrollMode">
      <summary>
        <para>Gets or sets how a specific NavBar group is scrolled, using a scroll bar or scroll buttons.</para>
      </summary>
      <value>A nullable ScrollMode value.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.GroupScrollModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.GroupScrollMode">NavBarGroup.GroupScrollMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.Header">
      <summary>
        <para>Gets or sets the content of the group’s header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The object to use for the group header.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.HeaderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Header">NavBarGroup.Header</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.HeaderTemplate">
      <summary>
        <para>Gets or sets the template used to display the content of the group’s header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"/>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.HeaderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.HeaderTemplate">NavBarGroup.HeaderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.HeaderTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses the group’s header template based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.HeaderTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.HeaderTemplateSelector">NavBarGroup.HeaderTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSettings">
      <summary>
        <para>Gets or sets an object that contains settings for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s icon.</para>
      </summary>
      <value>An ImageSettings object that contains properties to customize the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s icon.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSettings">NavBarGroup.ImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSource">
      <summary>
        <para>Gets or sets the image displayed by the group within its header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.ImageSource"/> object specifying the image displayed by the group.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSourceInNavPaneMenu">
      <summary>
        <para>Gets or sets the image displayed by the group in a <see href="https://docs.devexpress.com/WPF/6685/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-header">Navigation Pane Header</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:System.Windows.Media.ImageSource"/> object specifying the image displayed by the group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ImageSourceInNavPaneMenuProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSourceInNavPaneMenu">NavBarGroup.ImageSourceInNavPaneMenu</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ImageSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSource">NavBarGroup.ImageSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.IsActive">
      <summary>
        <para>Gets whether the group is active.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the group is active; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.IsActiveProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.IsActive">NavBarGroup.IsActive</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.IsCollapsing">
      <summary>
        <para>Gets a value that indicates whether the group is being collapsed.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the group’s expansion state changes to collapsed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.IsCollapsingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.IsCollapsing">NavBarGroup.IsCollapsing</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.IsEnabled">
      <summary>
        <para>Gets or sets whether this <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is currently enabled.</para>
      </summary>
      <value>true, if this <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> is currently enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.IsExpanded">
      <summary>
        <para>Gets or sets a value specifying whether the group is expanded.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the group is expanded, otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.IsExpandedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.IsExpanded">NavBarGroup.IsExpanded</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.IsExpanding">
      <summary>
        <para>Gets a value that indicates whether the group is being expanded.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the group’s expansion state changes to expanded; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.IsExpandingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.IsExpanding">NavBarGroup.IsExpanding</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.IsVisible">
      <summary>
        <para>Gets or sets a value specifying the visibility of the group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the group is visible; otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.IsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.IsVisible">NavBarGroup.IsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemDisplayMode">
      <summary>
        <para>Gets or sets whether icon only, text only or both of these should be displayed for all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</para>
      </summary>
      <value>A DisplayMode enumerator value specifying whether icon only, text only or both of these should be displayed for all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemDisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemDisplayMode">NavBarGroup.ItemDisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemFontSettings">
      <summary>
        <para>Gets or sets an object containing font settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</para>
      </summary>
      <value>A FontSettings object containing font settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemFontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemFontSettings">NavBarGroup.ItemFontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemImageSettings">
      <summary>
        <para>Gets or sets an object that contains icon settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</para>
      </summary>
      <value>An ImageSettings object that contains icon settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemImageSettings">NavBarGroup.ItemImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemLayoutSettings">
      <summary>
        <para>Gets or sets an object that provides settings for customizing the content of the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s items.</para>
      </summary>
      <value>A LayoutSettings object that contains settings for customizing the content of the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>‘s items.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemLayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemLayoutSettings">NavBarGroup.ItemLayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.Items">
      <summary>
        <para>Gets the collection of items within the group and provides indexed access to them.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItemCollection"/> object representing the collection of the group’s items.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize navbar items for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> container.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemsSource">NavBarGroup.ItemsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemStyle">
      <summary>
        <para>Gets or sets a style applied to <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> objects defined as <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplate">NavBarGroup.ItemTemplate</see> content. This is a dependency property.</para>
      </summary>
      <value>A Style object that contains custom style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemStyle">NavBarGroup.ItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of the group’s items.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplate">NavBarGroup.ItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses the group’s item template based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemTemplateSelector">NavBarGroup.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemVisualStyle">
      <summary>
        <para>Gets or sets a style object that defines the appearance of all items within the current group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object containing item style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.ItemVisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ItemVisualStyle">NavBarGroup.ItemVisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.LayoutSettings">
      <summary>
        <para>Provides access to a group’s image and text layout settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.LayoutSettings"/> object containing layout settings to arrange images and text displayed by the header or items within the current group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.LayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.LayoutSettings">NavBarGroup.LayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.MenuItemDisplayBinding">
      <summary>
        <para>Gets or sets the binding that determines which Header object’s property is displayed in the NavBar <see href="https://docs.devexpress.com/WPF/6684/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-customization-menu">menu</see>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Data.BindingBase"/> object specifying which property is displayed in the NavBar <see href="https://docs.devexpress.com/WPF/6684/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-customization-menu">menu</see>.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavBar">
      <summary>
        <para>Gets a NavBarControl object to which the current group belongs.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> object which owns the group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavBarProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavBar">NavBarGroup.NavBar</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavigationPaneVisible">
      <summary>
        <para>Gets or sets a value that specifies whether the group is visible within the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">Navigation Pane</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the group is visible within the Navigation Pane; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavigationPaneVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavigationPaneVisible">NavBarGroup.NavigationPaneVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneGroupButtonTemplate">
      <summary>
        <para>Gets or sets the template that defines the visualization of the group’s header represented within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneGroupButtonTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneGroupButtonTemplate">NavBarGroup.NavPaneGroupButtonTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneGroupButtonTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template for the group’s header represented within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>, based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneGroupButtonTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneGroupButtonTemplateSelector">NavBarGroup.NavPaneGroupButtonTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneOverflowGroupTemplate">
      <summary>
        <para>Gets or sets the template that defines the visualization of the item representing the group within the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneOverflowGroupTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneOverflowGroupTemplate">NavBarGroup.NavPaneOverflowGroupTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneOverflowGroupTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template for an item representing the group within the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>, based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneOverflowGroupTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneOverflowGroupTemplateSelector">NavBarGroup.NavPaneOverflowGroupTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneShowMode">
      <summary>
        <para>Gets or sets the mode in which the active group is displayed in the collapsed Navigation Pane.</para>
      </summary>
      <value>A ShowMode enumeration value that specifies the mode in which the active group is displayed in the collapsed Navigation Pane. The default is ShowMode.MaximizedDefaultItem.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneShowModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.NavPaneShowMode">NavBarGroup.NavPaneShowMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.OwnerCollection">
      <summary>
        <para>Returns the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupCollection"/> object that owns the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>. This collection is also specified via the <see cref="P:DevExpress.Xpf.NavBar.NavBarControl.Groups">NavBarControl.Groups</see> property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.PeekFormTemplate">
      <summary>
        <para>Gets or sets a DataTemplate used to render the PeekForm for the current group.</para>
      </summary>
      <value>A DataTemplate used to render the PeekForm for the current group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.PeekFormTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.PeekFormTemplate">NavBarGroup.PeekFormTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.PeekFormTemplateSelector">
      <summary>
        <para>Gets or sets the DataTemplateSelector descendant that provides a way to choose a DataTemplate used to render the PeekForm for the current group. This is a dependency property.</para>
      </summary>
      <value>A DataTemplateSelector descendant that provides a way to choose a DataTemplate used to render the PeekForm for the current group.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.PeekFormTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.PeekFormTemplateSelector">NavBarGroup.PeekFormTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.SelectedItem">
      <summary>
        <para>Gets or sets an object representing the group’s selected item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the group’s selected item. null (Nothing in Visual Basic) if none of group items are selected.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.SelectedItemIndex">
      <summary>
        <para>Gets or sets a value specifying the index of the group’s selected item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An integer value specifying the zero-based index of the selected item within the group. -1 if none of the group’s items are curretly selected.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.SelectedItemIndexProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.SelectedItemIndex">NavBarGroup.SelectedItemIndex</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.SelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.SelectedItem">NavBarGroup.SelectedItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.SynchronizedItems">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.Template">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, to hide it in MS Visual Studio designer tools.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.TemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.Template">NavBarGroup.Template</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroup.VisualStyle">
      <summary>
        <para>Gets or sets a style object that defines the appearance of the current group header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object containing group style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarGroup.VisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.VisualStyle">NavBarGroup.VisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarGroupCollection">
      <summary>
        <para>Represents a collection of groups within the navbar control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarGroupCollection.#ctor(DevExpress.Xpf.NavBar.NavBarControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupCollection"/> class with the specified owner.</para>
      </summary>
      <param name="navBar">A <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> object specifying the control that owns the created collection.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroupCollection.Item(System.String)">
      <summary>
        <para>Provides access to individual <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s by their names.</para>
      </summary>
      <param name="name">A String value specifying the name for the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> to be obtained.</param>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object with the specified name, contained in the current <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupCollection"/>.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanged">ExplorerBarView.GroupExpandedChanged</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group related to the generated event.</param>
      <param name="isExpanded">true if the related group is expanded; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventArgs.Group">
      <summary>
        <para>Gets a group object related to the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object, manipulations on which forced the event to be raised.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventArgs.IsExpanded">
      <summary>
        <para>Gets a value indicating whether the processed group is expanded.</para>
      </summary>
      <value>true if the group is expanded, otherwise false.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanged">ExplorerBarView.GroupExpandedChanged</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (<see cref="T:DevExpress.Xpf.NavBar.ExplorerBarView"/>) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanging">ExplorerBarView.GroupExpandedChanging</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangingEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group related to the generated event.</param>
      <param name="isExpanded">true if the related group is expanded; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangingEventArgs.Cancel">
      <summary>
        <para>Gets or sets a value indicating whether the operation which raised an event should be canceled.</para>
      </summary>
      <value>true if the operation raising the event should be canceled; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.ExplorerBarView.GroupExpandedChanging">ExplorerBarView.GroupExpandedChanging</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (<see cref="T:DevExpress.Xpf.NavBar.ExplorerBarView"/>) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroupExpandedChangingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItem">
      <summary>
        <para>Represents an individual item within the NavBarControl control.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ActualDisplayMode">
      <summary>
        <para>Gets the display mode for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>.</para>
      </summary>
      <value>A DisplayMode enumerator value currently applied to this <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualDisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ActualDisplayMode">NavBarItem.ActualDisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualDisplayModePropertyKey">
      <summary>
        <para>Provides a dependency property identifier for limited write access to a read-only <see cref="F:DevExpress.Xpf.NavBar.NavBarItem.ActualDisplayModeProperty">NavBarItem.ActualDisplayModeProperty</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ActualFontSettings">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s current font settings.</para>
      </summary>
      <value>A FontSettings object that contains font settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualFontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ActualFontSettings">NavBarItem.ActualFontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ActualImageSettings">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s current icon settings.</para>
      </summary>
      <value>An ImageSettings object that contains settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s icon.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ActualImageSettings">NavBarItem.ActualImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualImageSettingsPropertyKey">
      <summary>
        <para>Provides a dependency property identifier for limited write access to a read-only <see cref="F:DevExpress.Xpf.NavBar.NavBarItem.ActualImageSettingsProperty">NavBarItem.ActualImageSettingsProperty</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ActualLayoutSettings">
      <summary>
        <para>Gets the layout settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s text and icon.</para>
      </summary>
      <value>A LayoutSettings object that contains layout settings currently applied to the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s text and icon.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualLayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ActualLayoutSettings">NavBarItem.ActualLayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualLayoutSettingsPropertyKey">
      <summary>
        <para>Provides a dependency property identifier for limited write access to a read-only <see cref="F:DevExpress.Xpf.NavBar.NavBarItem.ActualLayoutSettingsProperty">NavBarItem.ActualLayoutSettingsProperty</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ActualVisualStyle">
      <summary>
        <para>Gets the actual visual style applied to the item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object that represents the visual style applied to the item.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ActualVisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ActualVisualStyle">NavBarItem.ActualVisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarItem.Click">
      <summary>
        <para>Fires immediately after an end-user has clicked a group item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItem.CoerceImageSource(System.Windows.DependencyObject,System.Windows.Media.ImageSource)">
      <summary>
        <para>Called whenever the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ImageSource">NavBarItem.ImageSource</see>, <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSource">NavBarGroup.ImageSource</see> or <see cref="P:DevExpress.Xpf.NavBar.NavBarGroup.ImageSourceInNavPaneMenu">NavBarGroup.ImageSourceInNavPaneMenu</see> dependency property value is being re-evaluated, or coercion is specifically requested.</para>
      </summary>
      <param name="dObj">The object that the property exists on. When the CoerceImageSource method is invoked, the property system will pass this value.</param>
      <param name="imageSource">The new value of the property, prior to any coercion attempt.</param>
      <returns>The coerced value.</returns>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.Command">
      <summary>
        <para>Gets or sets the command to invoke when the NavBar item is clicked.</para>
      </summary>
      <value>An ICommand object to invoke when the NavBar item is clicked.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.CommandParameter">
      <summary>
        <para>Gets or sets the parameter to pass to the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Command">NavBarItem.Command</see>.</para>
      </summary>
      <value>The Object specifying the parameter to pass to the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Command">NavBarItem.Command</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.CommandParameterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.CommandParameter">NavBarItem.CommandParameter</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.CommandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Command">NavBarItem.Command</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.CommandTarget">
      <summary>
        <para>Gets or sets the element on which to raise the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Command">NavBarItem.Command</see>.</para>
      </summary>
      <value>The IInputElement on which to raise the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Command">NavBarItem.Command</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.CommandTargetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.CommandTarget">NavBarItem.CommandTarget</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.Content">
      <summary>
        <para>Gets or sets the item’s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An object specifying the item’s content.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Content">NavBarItem.Content</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.DisplayMode">
      <summary>
        <para>Gets or sets whether icon only, text only or both of these should be displayed within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>.</para>
      </summary>
      <value>A DisplayMode enumerator value that specifies whether icon only, text only or both of these should be displayed within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.DisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.DisplayMode">NavBarItem.DisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.FontSettings">
      <summary>
        <para>Gets or sets an object that contains font settings for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>.</para>
      </summary>
      <value>A FontSettings object that contains properties to customize the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s font.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.FontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.FontSettings">NavBarItem.FontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.Group">
      <summary>
        <para>Gets the group to which the item belongs.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group that contains the item.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.GroupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Group">NavBarItem.Group</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ImageSettings">
      <summary>
        <para>Gets or sets an object that contains settings for the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s icon.</para>
      </summary>
      <value>An ImageSettings object that contains properties to customize the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>‘s icon.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ImageSettings">NavBarItem.ImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.ImageSource">
      <summary>
        <para>Gets or sets the image displayed by the item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.ImageSource"/> object specifying the image displayed by the item.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.ImageSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.ImageSource">NavBarItem.ImageSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.IsEnabled">
      <summary>
        <para>Gets or sets whether this <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> is currently enabled.</para>
      </summary>
      <value>true, if this <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> is currently enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.IsSelected">
      <summary>
        <para>Gets a value that specifies whether the item is selected.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the item is selected; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.IsSelectedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.IsSelected">NavBarItem.IsSelected</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.IsVisible">
      <summary>
        <para>Gets or sets a value specifying the visibility of the item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the item is visible; otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.IsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.IsVisible">NavBarItem.IsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.LayoutSettings">
      <summary>
        <para>Provides access to an item’s image and text layout settings.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.LayoutSettings"/> object containing layout settings to arrange an image and text displayed within the current item.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.LayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.LayoutSettings">NavBarItem.LayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarItem.Select">
      <summary>
        <para>Fires when a <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> is selected.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.Template">
      <summary>
        <para>Gets or sets the template that defines the presentation of the item’s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.TemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.Template">NavBarItem.Template</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItem.VisualStyle">
      <summary>
        <para>Gets or sets a style object that defines the current item’s appearance.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object containing item style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItem.VisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItem.VisualStyle">NavBarItem.VisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItemCollection">
      <summary>
        <para>Represents a collection of items within a group.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItemCollection.#ctor(DevExpress.Xpf.NavBar.NavBarGroup)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemCollection"/> class with the specified owner.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object specifying the group that owns the created collection.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemCollection.Item(System.String)">
      <summary>
        <para>Provides access to individual <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s by their names.</para>
      </summary>
      <param name="name">A String value specifying the name for the child object to be obtained.</param>
      <value>An object with the specified name, contained in the current <see cref="T:DevExpress.Xpf.NavBar.NavBarItemCollection"/>.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItemControl">
      <summary>
        <para>Represents a control (visual object) used to render the <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object (logical object) onscreen.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItemControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.DisplayMode">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/> displays text, icon, or both icon and text. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.DisplayMode"/> enumeration value that specifies whether the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/> displays text, icon, or both icon and text.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItemControl.DisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItemControl.DisplayMode">NavBarItemControl.DisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.FontSettings">
      <summary>
        <para>Gets or sets an object that contains font settings for the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/>, such as font family, font size, etc. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.FontSettings"/> object that comprises the font settings, such as font family, font size, etc.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItemControl.FontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItemControl.FontSettings">NavBarItemControl.FontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.ImageSettings">
      <summary>
        <para>Gets or sets an object that contains settings for the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/>‘s icon. This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Xpf.NavBar.ImageSettings"/> object that contains properties to customize the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/>‘s icon.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItemControl.ImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItemControl.ImageSettings">NavBarItemControl.ImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.ItemsPanelOrientation">
      <summary>
        <para>Gets or sets the orientation of items in groups. This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Controls.Orientation"/> enumeration values.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.LayoutSettings">
      <summary>
        <para>Gets or sets an object that contains the image and text layout settings for the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.LayoutSettings"/> object containing layout settings to arrange an image and text displayed in the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItemControl.LayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItemControl.LayoutSettings">NavBarItemControl.LayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.NavBar">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> to which the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/> belongs. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> object to which the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemControl"/> belongs.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarItemControl.NavBarProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarItemControl.NavBar">NavBarItemControl.NavBar</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItemControl.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemControl.ViewOrientation">
      <summary>
        <para>Gets or sets the orientation of groups in the view. This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Controls.Orientation"/> enumeration values.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItemSelectedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelected">NavBarViewBase.ItemSelected</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItemSelectedEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup,DevExpress.Xpf.NavBar.NavBarItem)">
      <summary>
        <para>Initializes a new <see cref="T:DevExpress.Xpf.NavBar.NavBarItemSelectedEventArgs"/> class instance with the specified settings.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group that owns the newly selected item.</param>
      <param name="item">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the newly selected item related to the event.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectedEventArgs.Group">
      <summary>
        <para>Gets a group object that contains an item related to the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object containing an item, manipulations on which forced the event to be raised.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectedEventArgs.Item">
      <summary>
        <para>Gets the newly selected item related to the event.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the newly selected item.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItemSelectedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelected">NavBarViewBase.ItemSelected</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItemSelectedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelecting">NavBarViewBase.ItemSelecting</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs.#ctor(DevExpress.Xpf.NavBar.NavBarGroup,DevExpress.Xpf.NavBar.NavBarItem,DevExpress.Xpf.NavBar.NavBarGroup,DevExpress.Xpf.NavBar.NavBarItem)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="prevGroup">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object that represents the group containing the previously selected item.</param>
      <param name="prevItem">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object that represents the previously selected item.</param>
      <param name="newGroup">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object that represents the group containing the item that is going to be selected.</param>
      <param name="newItem">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the item that is going to be selected.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs.Cancel">
      <summary>
        <para>Gets or sets a value indicating whether the operation which raised an event should be canceled.</para>
      </summary>
      <value>true if the operation raising the event should be canceled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs.NewGroup">
      <summary>
        <para>Gets a group object containing an item that is going to be selected.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object identifying the group whose item is being selected.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs.NewItem">
      <summary>
        <para>Gets an item object that is going to be selected.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the item being selected.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs.PrevGroup">
      <summary>
        <para>Gets the group containing the previously selected item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group where the previously selected item is contained.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs.PrevItem">
      <summary>
        <para>Gets the previously selected item.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the previously selected item.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarItemSelectingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelecting">NavBarViewBase.ItemSelecting</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItemSelectingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarLocalizer">
      <summary>
        <para>Represents an object that provides the means to localize the NavBarControl’s user interface elements.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarLocalizer.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarLocalizer"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarLocalizer.CreateDefaultLocalizer">
      <summary>
        <para>Returns an XtraLocalizer object representing default resources, based on the thread’s language and regional settings (culture).</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread’s culture.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarLocalizer.CreateResXLocalizer">
      <summary>
        <para>Returns an XtraLocalizer object representing resources based on the thread’s language and regional settings (culture).</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Utils.Localization.XtraLocalizer`1"/> object representing resources based on the thread’s culture.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarLocalizer.GetString(DevExpress.Xpf.NavBar.NavBarStringId)">
      <summary>
        <para>Returns a localized string for the given string identifier.</para>
      </summary>
      <param name="id">A <see cref="T:DevExpress.Xpf.NavBar.NavBarStringId"/> enumeration value identifying the string to localize.</param>
      <returns>A <see cref="T:System.String"/> corresponding to the specified identifier.</returns>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarPositionPanel">
      <summary>
        <para>This class is mainly intended to be used internally.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarPositionPanel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarPositionPanel"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarPositionPanel.GetGroupPosition(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarPositionPanel.GroupPosition">NavBarPositionPanel.GroupPosition</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.NavBarPositionPanel.GroupPosition">NavBarPositionPanel.GroupPosition</see> property value for the element.</returns>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarPositionPanel.GroupPositionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarPositionPanel.GroupPosition">NavBarPositionPanel.GroupPosition</see> attached property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarStringId">
      <summary>
        <para>Contains values corresponding to strings that can be localized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarStringId.GroupIsAlreadyAddedToAnotherNavBarException">
      <summary>
        <para>The text of the exception that occurs when an attempt is made to add a group object belonging to one NavBarControl to another NavBarControl.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarStringId.ItemIsAlreadyAddedToAnotherGroupException">
      <summary>
        <para>The text of the exception that occurs when an attempt is made to add an item object belonging to one group to another group.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarStringId.NavPaneMenuAddRemoveButtons">
      <summary>
        <para>The text of the <see href="https://docs.devexpress.com/WPF/6684/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-customization-menu">Navigation Pane Customization Menu</see>‘s menu item that invokes a submenu, allowing the visibility of group headers to be changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarStringId.NavPaneMenuShowFewerButtons">
      <summary>
        <para>The text of the <see href="https://docs.devexpress.com/WPF/6684/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-customization-menu">Navigation Pane Customization Menu</see>‘s menu item that allows fewer group headers to be displayed within the Navigation Pane.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarStringId.NavPaneMenuShowMoreButtons">
      <summary>
        <para>The text of the <see href="https://docs.devexpress.com/WPF/6684/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-customization-menu">Navigation Pane Customization Menu</see>‘s menu item that allows more group headers to be displayed within the Navigation Pane.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarViewBase">
      <summary>
        <para>Serves as the base for objects representing Views in a NavBarControl.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> class with default settings.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanged">
      <summary>
        <para>Fires after the active group has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanged">NavBarViewBase.ActiveGroupChanged</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanging">
      <summary>
        <para>Fires before the active group is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChangingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ActiveGroupChanging">NavBarViewBase.ActiveGroupChanging</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.AnimateGroupExpansion">
      <summary>
        <para>Gets or sets whether expanding/collapsing <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current View is animated.</para>
      </summary>
      <value>true if expanding/collapsing <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current View is animated; otherwise, false. Default is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.AnimateGroupExpansionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.AnimateGroupExpansion">NavBarViewBase.AnimateGroupExpansion</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.Background">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.BorderBrush">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.BorderThickness">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, to hide it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.ChangeGroupExpanded(DevExpress.Xpf.NavBar.NavBarGroup)">
      <summary>
        <para>Toggles the specified group’s expanded state.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object specifying the group whose expanded state should be changed.</param>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.Click">
      <summary>
        <para>Fires immediately after an end-user performs a click within the view.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ClickEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.Click">NavBarViewBase.Click</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.DisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.DisplayMode">NavBarViewBase.DisplayMode</see> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontFamily">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.FontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontSettings">NavBarViewBase.FontSettings</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontSize">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontStretch">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontStyle">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontWeight">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.Foreground">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.GetDisplayMode(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.DisplayMode">NavBarViewBase.DisplayMode</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.DisplayMode">NavBarViewBase.DisplayMode</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.GetFontSettings(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontSettings">NavBarViewBase.FontSettings</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="obj">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontSettings">NavBarViewBase.FontSettings</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.GetImageSettings(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ImageSettings">NavBarViewBase.ImageSettings</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ImageSettings">NavBarViewBase.ImageSettings</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.GetLayoutSettings(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.LayoutSettings">NavBarViewBase.LayoutSettings</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.LayoutSettings">NavBarViewBase.LayoutSettings</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.GetNavBarGroup(System.Windows.RoutedEventArgs)">
      <summary>
        <para>Gets a group related to the event whose data is passed via the method’s parameter.</para>
      </summary>
      <param name="e">A <see cref="T:System.Windows.RoutedEventArgs"/> descendant specifying the routed event.</param>
      <returns>A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object representing the group related to the specified event. If no group relates to the event, null (Nothing in Visual Basic) is returned.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.GetNavBarItem(System.Windows.RoutedEventArgs)">
      <summary>
        <para>Gets an item related to the event whose data is passed via the method’s parameter.</para>
      </summary>
      <param name="e">A <see cref="T:System.Windows.RoutedEventArgs"/> descendant specifying the routed event.</param>
      <returns>A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object representing the item related to the specified event. If no item relates to the event, null (Nothing in Visual Basic) is returned.</returns>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.GroupAdding">
      <summary>
        <para>Occurs after a group has been implicitly created and bound to a data source, but prior to adding it to the NavBar control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupDisplayMode">
      <summary>
        <para>Gets or sets whether <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> should display their text, icon, or both icon and text.</para>
      </summary>
      <value>A DisplayMode enumerator value that specifies whether <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> should display their text, icon, or both icon and text.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.GroupDisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupDisplayMode">NavBarViewBase.GroupDisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupFontSettings">
      <summary>
        <para>Gets or sets an object that stores font settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</para>
      </summary>
      <value>A FontSettings object that stores font settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.GroupFontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupFontSettings">NavBarViewBase.GroupFontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupHeaderTemplate">
      <summary>
        <para>Gets or sets a template, which defines how data is represented in the group header areas within the current view.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.ControlTemplate"/> object, which represents a template for group headers.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.GroupHeaderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupHeaderTemplate">NavBarViewBase.GroupHeaderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupImageSettings">
      <summary>
        <para>Gets or sets an object that stores image settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</para>
      </summary>
      <value>An ImageSettings object that stores image settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.GroupImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupImageSettings">NavBarViewBase.GroupImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupLayoutSettings">
      <summary>
        <para>Gets or sets an object that stores layout settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</para>
      </summary>
      <value>A LayoutSettings object that stores layout settings for all <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/>s within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.GroupLayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupLayoutSettings">NavBarViewBase.GroupLayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupVisualStyle">
      <summary>
        <para>Gets or sets a style object that defines the appearance of all group headers in the current View.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object containing group style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.GroupVisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupVisualStyle">NavBarViewBase.GroupVisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.HeaderTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of all group headers.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that defines the presentation of group headers.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.HeaderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.HeaderTemplate">NavBarViewBase.HeaderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.HeaderTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a group header template based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.HeaderTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.HeaderTemplateSelector">NavBarViewBase.HeaderTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.HorizontalContentAlignment">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set to hide it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ImageSettings">NavBarViewBase.ImageSettings</see> attached property.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemAdding">
      <summary>
        <para>Occurs after an item has been implicitly created and bound to a data source, but prior to adding it to the NavBar control.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemControlTemplate">
      <summary>
        <para>Gets or sets a control template that defines how items are represented on-screen.</para>
      </summary>
      <value>A ControlTemplate object.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemControlTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemControlTemplate">NavBarViewBase.ItemControlTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemDisplayMode">
      <summary>
        <para>Gets or sets whether icon only, text only or both of these should be displayed for all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</para>
      </summary>
      <value>A DisplayMode enumerator value specifying whether icon only, text only or both of these should be displayed for all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> within the current <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemDisplayModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemDisplayMode">NavBarViewBase.ItemDisplayMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemFontSettings">
      <summary>
        <para>Gets or sets an object that contains font settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current View.</para>
      </summary>
      <value>A FontSettings object that contains font settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current View.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemFontSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemFontSettings">NavBarViewBase.ItemFontSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemForeground">
      <summary>
        <para>Gets or sets the foreground brush applied to items within this view. This is a dependency property.</para>
      </summary>
      <value>A Brush applied to items within this view.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemForegroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemForeground">NavBarViewBase.ItemForeground</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemImageSettings">
      <summary>
        <para>Gets or sets an object that contains settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> icons within the current View.</para>
      </summary>
      <value>An ImageSettings object that contains icon settings for all NavBarItems within the current View.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemImageSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemImageSettings">NavBarViewBase.ItemImageSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemLayoutSettings">
      <summary>
        <para>Gets or sets an object that contains layout settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current View.</para>
      </summary>
      <value>A LayoutSettings object that contains layout settings common to all <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/>s within the current View.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemLayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemLayoutSettings">NavBarViewBase.ItemLayoutSettings</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelected">
      <summary>
        <para>Fires immediately after an item has been selected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelectedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelected">NavBarViewBase.ItemSelected</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelecting">
      <summary>
        <para>Fires before an item is selected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelectingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavBarViewBase.ItemSelecting">NavBarViewBase.ItemSelecting</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemsPanelOrientation">
      <summary>
        <para>Gets or sets the orientation of items within groups.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Controls.Orientation"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemsPanelOrientationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemsPanelOrientation">NavBarViewBase.ItemsPanelOrientation</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemsPanelTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of a specific container panel that is used by groups to layout group items.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An <see cref="T:System.Windows.Controls.ItemsPanelTemplate"/> object that is used within each group to layout its items.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemsPanelTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemsPanelTemplate">NavBarViewBase.ItemsPanelTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of all group items.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemTemplate">NavBarViewBase.ItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a group item template based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemTemplateSelector">NavBarViewBase.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemVisualStyle">
      <summary>
        <para>Gets or sets a style object that defines the appearance of all items in the current View.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object containing item style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ItemVisualStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ItemVisualStyle">NavBarViewBase.ItemVisualStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.LayoutSettingsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.LayoutSettings">NavBarViewBase.LayoutSettings</see> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.NavBar">
      <summary>
        <para>Gets a NavBarControl object to which the current view belongs.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> object that owns the view.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.NavBarProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.NavBar">NavBarViewBase.NavBar</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.NavBarViewKind">
      <summary>
        <para>Gets a value that identifies the currently applied view type.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewKind"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.NavBarViewKindProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.NavBarViewKind">NavBarViewBase.NavBarViewKind</see> attached property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.OnChangeGroupExpanded(System.Object,System.Windows.Input.ExecutedRoutedEventArgs)">
      <summary>
        <para>Handles the <see cref="P:DevExpress.Xpf.NavBar.NavBarCommands.ChangeGroupExpanded">NavBarCommands.ChangeGroupExpanded</see> command by calling the <see cref="M:DevExpress.Xpf.NavBar.NavBarViewBase.ChangeGroupExpanded(DevExpress.Xpf.NavBar.NavBarGroup)">NavBarViewBase.ChangeGroupExpanded</see> method.</para>
      </summary>
      <param name="sender">An object identifying the command sender (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant). This object serves as the command’s CommandTarget attribute.</param>
      <param name="e">A <see cref="T:System.Windows.Input.ExecutedRoutedEventArgs"/> object that provides data for the executed command.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.OnGroupHeaderTemplateChanged(System.Windows.DependencyObject,System.Windows.DependencyPropertyChangedEventArgs)">
      <summary>
        <para>This method is called when the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.GroupHeaderTemplate">NavBarViewBase.GroupHeaderTemplate</see> property is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.OnSelectItem(System.Object,System.Windows.Input.ExecutedRoutedEventArgs)">
      <summary>
        <para>Handles the <see cref="P:DevExpress.Xpf.NavBar.NavBarCommands.SelectItem">NavBarCommands.SelectItem</see> command by calling the <see cref="M:DevExpress.Xpf.NavBar.NavBarViewBase.SelectItem(DevExpress.Xpf.NavBar.NavBarItem)">NavBarViewBase.SelectItem</see> method.</para>
      </summary>
      <param name="sender">An object identifying the command sender (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant). This object serves as the command’s CommandTarget attribute.</param>
      <param name="e">A <see cref="T:System.Windows.Input.ExecutedRoutedEventArgs"/> object that provides data for the executed command.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.OnSetActiveGroup(System.Object,System.Windows.Input.ExecutedRoutedEventArgs)">
      <summary>
        <para>Handles the <see cref="P:DevExpress.Xpf.NavBar.NavBarCommands.SetActiveGroup">NavBarCommands.SetActiveGroup</see> command by calling the <see cref="M:DevExpress.Xpf.NavBar.NavBarViewBase.SetActiveGroup(DevExpress.Xpf.NavBar.NavBarGroup)">NavBarViewBase.SetActiveGroup</see> method.</para>
      </summary>
      <param name="sender">An object identifying the command sender (a <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> descendant). This object serves as the command’s CommandTarget attribute.</param>
      <param name="e">A <see cref="T:System.Windows.Input.ExecutedRoutedEventArgs"/> object that provides data for the executed command.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.Orientation">
      <summary>
        <para>Gets or sets the orientation of groups within the view.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:System.Windows.Controls.Orientation"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.OrientationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.Orientation">NavBarViewBase.Orientation</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.Padding">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, hiding it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.SelectItem(DevExpress.Xpf.NavBar.NavBarItem)">
      <summary>
        <para>Selects the specified item.</para>
      </summary>
      <param name="item">A <see cref="T:DevExpress.Xpf.NavBar.NavBarItem"/> object specifying the item to be selected.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.SetActiveGroup(DevExpress.Xpf.NavBar.NavBarGroup)">
      <summary>
        <para>Activates the specified group.</para>
      </summary>
      <param name="group">A <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> object specifying the group to be activated.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.SetDisplayMode(System.Windows.DependencyObject,DevExpress.Xpf.NavBar.DisplayMode)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.DisplayMode">NavBarViewBase.DisplayMode</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="settings">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.SetFontSettings(System.Windows.DependencyObject,DevExpress.Xpf.NavBar.FontSettings)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.FontSettings">NavBarViewBase.FontSettings</see> attached property for a given object.</para>
      </summary>
      <param name="obj">The element on which to set the attached property.</param>
      <param name="value">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.SetImageSettings(System.Windows.DependencyObject,DevExpress.Xpf.NavBar.ImageSettings)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ImageSettings">NavBarViewBase.ImageSettings</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="settings">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavBarViewBase.SetLayoutSettings(System.Windows.DependencyObject,DevExpress.Xpf.NavBar.LayoutSettings)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.LayoutSettings">NavBarViewBase.LayoutSettings</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="settings">The property value to set.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.ShowBorder">
      <summary>
        <para>Gets or sets whether <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> borders should be displayed.</para>
      </summary>
      <value>true, if <see cref="T:DevExpress.Xpf.NavBar.NavBarGroup"/> borders should be displayed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewBase.ShowBorderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavBarViewBase.ShowBorder">NavBarViewBase.ShowBorder</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavBarViewBase.VerticalContentAlignment">
      <summary>
        <para>This inherited property is not in effect for the <see cref="T:DevExpress.Xpf.NavBar.NavBarViewBase"/> object.</para>
        <para>To prevent developers from using this property, its code attributes are set, to hide it in MS Visual Studio designer tools.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavBarViewKind">
      <summary>
        <para>Contains values that identify the currently applied view type.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewKind.ExplorerBar">
      <summary>
        <para>Identifies the Explorer Bar view type (the control’s view is represented by a <see cref="T:DevExpress.Xpf.NavBar.ExplorerBarView"/> instance).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewKind.NavigationPane">
      <summary>
        <para>Identifies the Navigation Pane view type (the control’s view is represented by a <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> instance).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavBarViewKind.SideBar">
      <summary>
        <para>Identifies the Side Bar view type  (the control’s view is represented by a <see cref="T:DevExpress.Xpf.NavBar.SideBarView"/> instance).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavigationPaneCommands">
      <summary>
        <para>Provides access to commands which are specific to the Navigation Pane view.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneCommands.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneCommands"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneCommands.ChangeNavPaneExpanded">
      <summary>
        <para>Toggles the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">Navigation Pane</see>‘s expanded state.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.RoutedCommand"/> object that defines the command.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneCommands.ShowFewerGroups">
      <summary>
        <para>Decreases the number of visible group headers displayed within the Navigation Pane’s <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.RoutedCommand"/> object that defines the command.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneCommands.ShowMoreGroups">
      <summary>
        <para>Increments (by one) the number of visible group headers displayed within the Navigation Pane’s <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.RoutedCommand"/> object that defines the command.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavigationPaneView">
      <summary>
        <para>Represents a view similar to the Navigation Pane found in Microsoft Outlook 2007.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ActiveGroupCollapsedNavPaneSelectedItem">
      <summary>
        <para>Gets or sets the item selected in the active group of the collapsed Navigation Pane.</para>
      </summary>
      <value>An Object that represents the selected item in the active group of the collapsed Navigation Pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ActiveGroupCollapsedNavPaneSelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ActiveGroupCollapsedNavPaneSelectedItem">NavigationPaneView.ActiveGroupCollapsedNavPaneSelectedItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ActiveGroupMinHeight">
      <summary>
        <para>Gets or sets the minimum height of the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">active group’s content</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A double precision floating-point value specifying the minimum height of the active group’s content.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ActiveGroupMinHeightProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ActiveGroupMinHeight">NavigationPaneView.ActiveGroupMinHeight</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ActualMaxVisibleGroupCount">
      <summary>
        <para>Gets the limit of currently visible groups for this <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>. This is a dependency property.</para>
      </summary>
      <value>An Int32 value that is the limit of currently visible groups for this <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ActualMaxVisibleGroupCountProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ActualMaxVisibleGroupCount">NavigationPaneView.ActualMaxVisibleGroupCount</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.ChangeNavPaneExpanded">
      <summary>
        <para>Toggles the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">Navigation Pane</see>‘s expanded state.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.CollapsedActiveGroupControlTemplate">
      <summary>
        <para>Gets or sets a control template that defines the presentation of a collapsed Navigation Pane.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A ControlTemplate object.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.CollapsedActiveGroupControlTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.CollapsedActiveGroupControlTemplate">NavigationPaneView.CollapsedActiveGroupControlTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.CollapsedTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of the <see href="https://docs.devexpress.com/WPF/6690/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/collapsed-navigation-pane">collapsed Navigation Pane</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.CollapsedTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.CollapsedTemplate">NavigationPaneView.CollapsedTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.Content">
      <summary>
        <para>Gets or sets the view’s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An object specifying the view’s content.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.Content">NavigationPaneView.Content</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.EachCollapsedGroupHasSelectedItem">
      <summary>
        <para>Gets or sets whether each group in the collapsed Navigation Pane can have a selected item. This is a dependency property.</para>
      </summary>
      <value>true, if each group in the collapsed Navigation Pane can have a selected item; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.EachCollapsedGroupHasSelectedItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.EachCollapsedGroupHasSelectedItem">NavigationPaneView.EachCollapsedGroupHasSelectedItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ElementProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.Element">NavigationPaneView.Element</see> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandButtonMode">
      <summary>
        <para>Gets or sets the display mode of the <see href="https://docs.devexpress.com/WPF/6695/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-expand-button">expand button</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Xpf.NavBar.ExpandButtonMode"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandButtonModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandButtonMode">NavigationPaneView.ExpandButtonMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandedTemplate">
      <summary>
        <para>Gets or sets the template that defines the presentation of the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">expanded Navigation Pane</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandedTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandedTemplate">NavigationPaneView.ExpandedTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandedWidth">
      <summary>
        <para>Gets or sets the width of the Navigation Pane when it is expanded from a collapsed state.</para>
      </summary>
      <value>A Double value specifying the width of the Navigation Pane when it is expanded from the collapsed state.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandedWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ExpandedWidth">NavigationPaneView.ExpandedWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.Expander">
      <summary>
        <para>Gets an expander used within the current <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Core.DXExpander"/> object used within the current <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.GetElement(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.Element">NavigationPaneView.Element</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.Element">NavigationPaneView.Element</see> property value for the element.</returns>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonControlTemplate">
      <summary>
        <para>Gets or sets a control template that defines the visualization of group headers within the group button panel.</para>
      </summary>
      <value>A ControlTemplate object.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonControlTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonControlTemplate">NavigationPaneView.GroupButtonControlTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonTemplate">
      <summary>
        <para>Gets or sets the template that defines the visualization of group headers represented within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"/>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonTemplate">NavigationPaneView.GroupButtonTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template for group headers represented within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>, based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.GroupButtonTemplateSelector">NavigationPaneView.GroupButtonTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsExpandButtonVisible">
      <summary>
        <para>Gets or sets a value that specifies whether the <see href="https://docs.devexpress.com/WPF/6695/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-expand-button">Navigation Pane Expand Button</see> is displayed, allowing end-users to expand/collapse the Navigation Pane.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the expand button is displayed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.IsExpandButtonVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsExpandButtonVisible">NavigationPaneView.IsExpandButtonVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsExpanded">
      <summary>
        <para>Gets or sets a value specifying whether the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">Navigation Pane</see> is expanded.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the Navigation Pane is expanded, otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.IsExpandedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsExpanded">NavigationPaneView.IsExpanded</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsOverflowPanelVisible">
      <summary>
        <para>Gets or sets a value specifying the visibility of the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>. This is a dependency property.</para>
      </summary>
      <value>true if the overflow panel is visible; otherwise false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.IsOverflowPanelVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsOverflowPanelVisible">NavigationPaneView.IsOverflowPanelVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsPopupOpen">
      <summary>
        <para>Gets or sets whether the pop-up pane displaying the contents of the active group is opened. This property is in effect if the <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> is collapsed. This is a dependency property.</para>
      </summary>
      <value>true, if the pop-up pane is opened; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.IsPopupOpenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsPopupOpen">NavigationPaneView.IsPopupOpen</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsSplitterVisible">
      <summary>
        <para>Gets or sets a value that specifies whether the <see href="https://docs.devexpress.com/WPF/6687/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-splitter">splitter</see> is displayed, allowing end-users to resize the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the splitter is displayed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.IsSplitterVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.IsSplitterVisible">NavigationPaneView.IsSplitterVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ItemsControlGroupCount">
      <summary>
        <para>Gets the actual number of visible group headers displayed within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
      </summary>
      <value>An integer value that represents the number of visible group headers displayed within the group button panel.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ItemsControlGroupCountProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ItemsControlGroupCount">NavigationPaneView.ItemsControlGroupCount</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ItemsControlGroups">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ItemsControlGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ItemsControlGroups">NavigationPaneView.ItemsControlGroups</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.ItemVisualStyleInPopup">
      <summary>
        <para>Gets or sets a style applied to the popup window, displaying a group’s content while the <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> is expanded.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object applied to the popup window, displaying a group’s content while the <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> is expanded.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.ItemVisualStyleInPopupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.ItemVisualStyleInPopup">NavigationPaneView.ItemVisualStyleInPopup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.MaxPopupWidth">
      <summary>
        <para>Gets or sets the maximum width of the pop-up pane for the current <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>.</para>
      </summary>
      <value>A Double value that is the maximum width of the pop-up pane for the current <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.MaxPopupWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.MaxPopupWidth">NavigationPaneView.MaxPopupWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.MaxVisibleGroupCount">
      <summary>
        <para>Gets or sets the maximum number of visible group headers that can be displayed within the <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An integer value that specifies the maximum number of displayed group headers allowed.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.MaxVisibleGroupCountProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.MaxVisibleGroupCount">NavigationPaneView.MaxVisibleGroupCount</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.MenuCustomizations">
      <summary>
        <para>Allows you to customize the <see href="https://docs.devexpress.com/WPF/6684/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-customization-menu">Navigation Pane Customization Menu</see> by adding new menu items or removing existing items.</para>
      </summary>
      <value>The ObservableCollection that contains customization menu items.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanged">
      <summary>
        <para>Fires after the expanded state of the Navigation Pane has been changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanged">NavigationPaneView.NavPaneExpandedChanged</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanging">
      <summary>
        <para>Fires before the expanded state of the Navigation Pane is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChangingEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanging">NavigationPaneView.NavPaneExpandedChanging</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.OnShowFewerGroups(System.Object,System.Windows.Input.ExecutedRoutedEventArgs)">
      <summary>
        <para>Handles the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneCommands.ShowFewerGroups">NavigationPaneCommands.ShowFewerGroups</see> command by calling the <see cref="M:DevExpress.Xpf.NavBar.NavigationPaneView.ShowFewerGroups(System.Object)">NavigationPaneView.ShowFewerGroups</see> method.</para>
      </summary>
      <param name="sender">An object identifying the command sender (a <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> instance). This object serves as the command’s CommandTarget attribute.</param>
      <param name="e">A <see cref="T:System.Windows.Input.ExecutedRoutedEventArgs"/> object that provides data for the executed command.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.OnShowMoreGroups(System.Object,System.Windows.Input.ExecutedRoutedEventArgs)">
      <summary>
        <para>Handles the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneCommands.ShowMoreGroups">NavigationPaneCommands.ShowMoreGroups</see> command by calling the <see cref="M:DevExpress.Xpf.NavBar.NavigationPaneView.ShowMoreGroups(System.Object)">NavigationPaneView.ShowMoreGroups</see> method.</para>
      </summary>
      <param name="sender">An object identifying the command sender (a <see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/> instance). This object serves as the command’s CommandTarget attribute.</param>
      <param name="e">A <see cref="T:System.Windows.Input.ExecutedRoutedEventArgs"/> object that provides data for the executed command.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupControlTemplate">
      <summary>
        <para>Gets or sets the template that defines the visualization of items representing groups within the overflow panel.</para>
      </summary>
      <value>A ControlTemplate object.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupControlTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupControlTemplate">NavigationPaneView.OverflowGroupControlTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupTemplate">
      <summary>
        <para>Gets or sets the template that defines the visualization of items representing groups within the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>Type: <see cref="T:System.Windows.DataTemplate"></see>A data template. The default is a null reference (Nothing in Visual Basic).</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupTemplate">NavigationPaneView.OverflowGroupTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template for items representing groups within the <see href="https://docs.devexpress.com/WPF/6686/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-overflow-panel">overflow panel</see>, based on custom logic.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> descendant that chooses a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowGroupTemplateSelector">NavigationPaneView.OverflowGroupTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowPanelGroups">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowPanelGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.OverflowPanelGroups">NavigationPaneView.OverflowPanelGroups</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormHideDelay">
      <summary>
        <para>Gets or sets the delay before a PeekForm is hidden.</para>
      </summary>
      <value>An integer value that specifies the delay, in milliseconds, before a PeekForm is hidden.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormHideDelayProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormHideDelay">NavigationPaneView.PeekFormHideDelay</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormShowDelay">
      <summary>
        <para>Gets or sets the delay before a PeekForm is displayed.</para>
      </summary>
      <value>An integer value that specifies the delay, in milliseconds, before a PeekForm is displayed.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormShowDelayProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormShowDelay">NavigationPaneView.PeekFormShowDelay</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormShowMode">
      <summary>
        <para>Gets or sets whether Peek Forms are shown for inactive groups in the collapsed Navigation Pane, in the Overflow Panel, both or not shown.</para>
      </summary>
      <value>A PeekFormShowMode enumeration value that specifies the mode in which Peek Forms are shown. Default is None.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormShowModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.PeekFormShowMode">NavigationPaneView.PeekFormShowMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.SetElement(System.Windows.DependencyObject,DevExpress.Xpf.NavBar.Element)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.NavigationPaneView.Element">NavigationPaneView.Element</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="element">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.ShowFewerGroups(System.Object)">
      <summary>
        <para>Decreases the number of visible group headers displayed within the Navigation Pane’s <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
      </summary>
      <param name="parameter">An object specifying the number by which to decrease the number of visible group headers. If set to null, the number of visible group headers is decreased by one.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavigationPaneView.ShowMoreGroups(System.Object)">
      <summary>
        <para>Increments the number of visible group headers displayed within the Navigation Pane’s <see href="https://docs.devexpress.com/WPF/6696/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/navigation-pane-visible-group-headers-group-button-panel">group button panel</see>.</para>
      </summary>
      <param name="parameter">An object specifying the number by which to increase the number of visible group headers. If set to null, the number of visible group headers is increased by one.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangedEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanged">NavigationPaneView.NavPaneExpandedChanged</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavPaneExpandedChangedEventArgs.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangedEventArgs"/> class with the specified setting.</para>
      </summary>
      <param name="isExpanded">true if the Navigation Pane is expanded; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavPaneExpandedChangedEventArgs.IsExpanded">
      <summary>
        <para>Gets a value indicating whether the <see href="https://docs.devexpress.com/WPF/6689/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/expanded-navigation-pane">Navigation Pane</see> is expanded.</para>
      </summary>
      <value>true if the group is expanded, otherwise false.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangedEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanged">NavigationPaneView.NavPaneExpandedChanged</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (<see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangedEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangingEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanging">NavigationPaneView.NavPaneExpandedChanging</see> event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.NavPaneExpandedChangingEventArgs.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangingEventArgs"/> class with the specified setting.</para>
      </summary>
      <param name="isExpanded">true if the Navigation Pane is going to be expanded; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.NavPaneExpandedChangingEventArgs.Cancel">
      <summary>
        <para>Gets or sets a value indicating whether the operation which raised an event should be canceled.</para>
      </summary>
      <value>true if the operation raising the event should be canceled; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangingEventHandler">
      <summary>
        <para>Represents a method that will handle the <see cref="E:DevExpress.Xpf.NavBar.NavigationPaneView.NavPaneExpandedChanging">NavigationPaneView.NavPaneExpandedChanging</see> event.</para>
      </summary>
      <param name="sender">The event source. Identifies the view (<see cref="T:DevExpress.Xpf.NavBar.NavigationPaneView"/>) that raised the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.NavBar.NavPaneExpandedChangingEventArgs"/> object that contains event data.</param>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ScrollingSettings">
      <summary>
        <para>Contains scrolling settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.ScrollingSettings"/> class with default settings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.AccelerationRatioProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.AccelerationRatio">ScrollingSettings.AccelerationRatio</see> attached property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.ClickModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ClickMode">ScrollingSettings.ClickMode</see> attached property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.DecelerationRatioProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.DecelerationRatio">ScrollingSettings.DecelerationRatio</see> attached property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetAccelerationRatio(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.AccelerationRatio">ScrollingSettings.AccelerationRatio</see> attached property for a specified dependency object.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.AccelerationRatio">ScrollingSettings.AccelerationRatio</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetClickMode(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ClickMode">ScrollingSettings.ClickMode</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ClickMode">ScrollingSettings.ClickMode</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetDecelerationRatio(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.DecelerationRatio">ScrollingSettings.DecelerationRatio</see> attached property for a specified dependency object.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.DecelerationRatio">ScrollingSettings.DecelerationRatio</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetIsManipulationEnabled(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.IsManipulationEnabled">ScrollingSettings.IsManipulationEnabled</see> attached property.</para>
      </summary>
      <param name="obj">The DependencyObject to which the property is attached.</param>
      <returns>true, if manipulation of the <see cref="T:DevExpress.Xpf.NavBar.NavBarControl"/> contents is enabled; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetMinScrollValue(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.MinScrollValue">ScrollingSettings.MinScrollValue</see> property.</para>
      </summary>
      <param name="obj">The DependencyObject to which the property is attached.</param>
      <returns>A Double value that specifies the swipe sensitivity.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetScrollMode(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollMode">ScrollingSettings.ScrollMode</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="obj">The object from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollMode">ScrollingSettings.ScrollMode</see> property value for the object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetScrollSpeed(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollSpeed">ScrollingSettings.ScrollSpeed</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollSpeed">ScrollingSettings.ScrollSpeed</see> property value for the element.</returns>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.GetTopBottomIndent(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.TopBottomIndent">ScrollingSettings.TopBottomIndent</see> attached property for a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="d">The element from which the property value is read.</param>
      <returns>The <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.TopBottomIndent">ScrollingSettings.TopBottomIndent</see> property value for the element.</returns>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.IsManipulationEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.IsManipulationEnabled">ScrollingSettings.IsManipulationEnabled</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.MinScrollValueProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.MinScrollValue">ScrollingSettings.MinScrollValue</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollMode">ScrollingSettings.ScrollMode</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollSpeedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollSpeed">ScrollingSettings.ScrollSpeed</see> attached property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetAccelerationRatio(System.Windows.DependencyObject,System.Double)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.AccelerationRatio">ScrollingSettings.AccelerationRatio</see> attached property to a specified dependency object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="value">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetClickMode(System.Windows.DependencyObject,System.Windows.Controls.ClickMode)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ClickMode">ScrollingSettings.ClickMode</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="value">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetDecelerationRatio(System.Windows.DependencyObject,System.Double)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.DecelerationRatio">ScrollingSettings.DecelerationRatio</see> attached property for a given dependency object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="value">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetIsManipulationEnabled(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.IsManipulationEnabled">ScrollingSettings.IsManipulationEnabled</see> attached property for a specific object.</para>
      </summary>
      <param name="obj">The DependencyObject whose <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.IsManipulationEnabled">ScrollingSettings.IsManipulationEnabled</see> attached property is to be set.</param>
      <param name="value">The new value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.IsManipulationEnabled">ScrollingSettings.IsManipulationEnabled</see> attached property for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetMinScrollValue(System.Windows.DependencyObject,System.Nullable{System.Double})">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.MinScrollValue">ScrollingSettings.MinScrollValue</see> attached property for a specific object.</para>
      </summary>
      <param name="obj">The DependencyObject whose <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.MinScrollValue">ScrollingSettings.MinScrollValue</see> attached property is to be set.</param>
      <param name="value">The new value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.MinScrollValue">ScrollingSettings.MinScrollValue</see> attached property for the specified object.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetScrollMode(System.Windows.DependencyObject,DevExpress.Xpf.NavBar.ScrollMode)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollMode">ScrollingSettings.ScrollMode</see> attached property to a specified <see cref="T:System.Windows.DependencyObject"/>.</para>
      </summary>
      <param name="obj">The object to which the attached property is written.</param>
      <param name="value">The required <see cref="T:DevExpress.Xpf.NavBar.ScrollMode"/> value.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetScrollSpeed(System.Windows.DependencyObject,System.Double)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.ScrollSpeed">ScrollingSettings.ScrollSpeed</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="value">The property value to set.</param>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.ScrollingSettings.SetTopBottomIndent(System.Windows.DependencyObject,System.Double)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.TopBottomIndent">ScrollingSettings.TopBottomIndent</see> attached property for a given object.</para>
      </summary>
      <param name="d">The element on which to set the attached property.</param>
      <param name="value">The property value to set.</param>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollingSettings.TopBottomIndentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.ScrollingSettings.TopBottomIndent">ScrollingSettings.TopBottomIndent</see> attached property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.ScrollMode">
      <summary>
        <para>Enumerates values that specify how the content of the NavBarControl is scrolled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollMode.Buttons">
      <summary>
        <para>A specific NavBar object is scrolled via <see href="https://docs.devexpress.com/WPF/6688/controls-and-libraries/navigation-controls/navigation-bar/visual-elements/scroll-buttons">scroll buttons</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollMode.None">
      <summary>
        <para>No visual scrolling elements are displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.ScrollMode.ScrollBar">
      <summary>
        <para>A specific NavBar object is scrolled via a Scroll Bar.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.NavBar.SideBarView">
      <summary>
        <para>Represents a view similar to the side bars found in Microsoft Outlook, prior to version 2003.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.NavBar.SideBarView.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.NavBar.SideBarView"/> class with default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.SideBarView.ActiveGroupMinHeight">
      <summary>
        <para>Gets or sets the minimum height of the active group’s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A double precision floating-point value specifying the minimum height of the active group’s content.</value>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.SideBarView.ActiveGroupMinHeightProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.SideBarView.ActiveGroupMinHeight">SideBarView.ActiveGroupMinHeight</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.NavBar.SideBarView.SideBarPanel">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.NavBar.SideBarView.SideBarPanelProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.NavBar.SideBarView.SideBarPanel">SideBarView.SideBarPanel</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
  </members>
</doc>