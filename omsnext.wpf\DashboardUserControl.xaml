﻿<UserControl x:Class="omsnext.wpf.DashboardUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">
    <ScrollViewer VerticalScrollBarVisibility="Auto">
        <dxlc:TileLayoutControl Padding="20">
            <dxlc:Tile Size="Large" Background="Transparent" BorderThickness="0">
                <TextBlock x:Name="WelcomeLabel" FontSize="24" FontWeight="SemiBold" TextWrapping="Wrap" VerticalAlignment="Center"/>
            </dxlc:Tile>
            <dxlc:Tile Size="Large" Background="Transparent" BorderThickness="0"/>
            <dxlc:Tile Header="Felhasználók" Name="UsersTile" Size="Small" Background="#007BFF" Cursor="Hand" MouseLeftButtonUp="UsersTile_MouseLeftButtonUp">
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="👥" FontSize="36" HorizontalAlignment="Center"/>
                    <TextBlock Text="Felhasználók kezelése" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                </StackPanel>
            </dxlc:Tile>
            <dxlc:Tile Header="Beállítások" Name="SettingsTile" Size="Small" Background="#28A745" Cursor="Hand" MouseLeftButtonUp="SettingsTile_MouseLeftButtonUp">
                <StackPanel VerticalAlignment="Center">
                    <TextBlock Text="⚙️" FontSize="36" HorizontalAlignment="Center"/>
                    <TextBlock Text="Rendszerbeállítások" HorizontalAlignment="Center" Margin="0,10,0,0"/>
                </StackPanel>
            </dxlc:Tile>
        </dxlc:TileLayoutControl>
    </ScrollViewer>
</UserControl>