{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 66, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/app/api/auth/logout/route.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\n\r\nexport async function POST() {\r\n  const response = NextResponse.json({ success: true });\r\n  response.cookies.set(\"oms_token\", \"\", {\r\n    httpOnly: true,\r\n    secure: process.env.NODE_ENV === \"production\",\r\n    sameSite: \"strict\",\r\n    path: \"/\",\r\n    maxAge: 0,\r\n  });\r\n  return response;\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;;AAEO,eAAe;IACpB,MAAM,WAAW,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAAE,SAAS;IAAK;IACnD,SAAS,OAAO,CAAC,GAAG,CAAC,aAAa,IAAI;QACpC,UAAU;QACV,QAAQ,oDAAyB;QACjC,UAAU;QACV,MAAM;QACN,QAAQ;IACV;IACA,OAAO;AACT", "debugId": null}}]}