{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/editing/const.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/editing/const.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Scrollable from \"../../../../ui/scroll_view/ui.scrollable\";\r\nexport const EDITOR_CELL_CLASS = \"dx-editor-cell\";\r\nexport const ROW_CLASS = \"dx-row\";\r\nexport const CELL_MODIFIED_CLASS = \"dx-cell-modified\";\r\nexport const ROW_SELECTED_CLASS = \"dx-selection\";\r\nexport const EDIT_FORM_CLASS = \"edit-form\";\r\nexport const DATA_EDIT_DATA_INSERT_TYPE = \"insert\";\r\nexport const DATA_EDIT_DATA_REMOVE_TYPE = \"remove\";\r\nexport const EDITING_POPUP_OPTION_NAME = \"editing.popup\";\r\nexport const EDITING_FORM_OPTION_NAME = \"editing.form\";\r\nexport const EDITING_EDITROWKEY_OPTION_NAME = \"editing.editRowKey\";\r\nexport const EDITING_EDITCOLUMNNAME_OPTION_NAME = \"editing.editColumnName\";\r\nexport const TARGET_COMPONENT_NAME = \"targetComponent\";\r\nexport const EDITORS_INPUT_SELECTOR = \"input:not([type='hidden'])\";\r\nexport const FOCUSABLE_ELEMENT_SELECTOR = `[tabindex]:not([disabled]), ${EDITORS_INPUT_SELECTOR}:not([disabled])`;\r\nexport const EDIT_MODE_BATCH = \"batch\";\r\nexport const EDIT_MODE_ROW = \"row\";\r\nexport const EDIT_MODE_CELL = \"cell\";\r\nexport const EDIT_MODE_FORM = \"form\";\r\nexport const EDIT_MODE_POPUP = \"popup\";\r\nexport const FIRST_NEW_ROW_POSITION = \"first\";\r\nexport const LAST_NEW_ROW_POSITION = \"last\";\r\nexport const PAGE_BOTTOM_NEW_ROW_POSITION = \"pageBottom\";\r\nexport const PAGE_TOP_NEW_ROW_POSITION = \"pageTop\";\r\nexport const VIEWPORT_BOTTOM_NEW_ROW_POSITION = \"viewportBottom\";\r\nexport const VIEWPORT_TOP_NEW_ROW_POSITION = \"viewportTop\";\r\nexport const EDIT_MODES = [\"batch\", \"row\", \"cell\", \"form\", \"popup\"];\r\nexport const ROW_BASED_MODES = [\"row\", \"form\", \"popup\"];\r\nexport const CELL_BASED_MODES = [\"batch\", \"cell\"];\r\nexport const REQUIRED_EDITOR_LABELLEDBY_MODES = [\"batch\", \"row\", \"cell\"];\r\nexport const MODES_WITH_DELAYED_FOCUS = [\"row\", \"form\"];\r\nexport const READONLY_CLASS = \"readonly\";\r\nexport const LINK_CLASS = \"dx-link\";\r\nexport const LINK_ICON_CLASS = \"dx-link-icon\";\r\nexport const ROW_SELECTED = \"dx-selection\";\r\nexport const EDIT_BUTTON_CLASS = \"dx-edit-button\";\r\nexport const COMMAND_EDIT_CLASS = \"dx-command-edit\";\r\nexport const COMMAND_EDIT_WITH_ICONS_CLASS = \"dx-command-edit-with-icons\";\r\nexport const INSERT_INDEX = \"__DX_INSERT_INDEX__\";\r\nexport const ROW_INSERTED = \"dx-row-inserted\";\r\nexport const ROW_MODIFIED = \"dx-row-modified\";\r\nexport const CELL_MODIFIED = \"dx-cell-modified\";\r\nexport const EDITING_NAMESPACE = \"dxDataGridEditing\";\r\nexport const CELL_FOCUS_DISABLED_CLASS = \"dx-cell-focus-disabled\";\r\nexport const DATA_EDIT_DATA_UPDATE_TYPE = \"update\";\r\nexport const DEFAULT_START_EDIT_ACTION = \"click\";\r\nexport const EDIT_LINK_CLASS = {\r\n    save: \"dx-link-save\",\r\n    cancel: \"dx-link-cancel\",\r\n    edit: \"dx-link-edit\",\r\n    undelete: \"dx-link-undelete\",\r\n    delete: \"dx-link-delete\",\r\n    add: \"dx-link-add\"\r\n};\r\nexport const EDIT_ICON_CLASS = {\r\n    save: \"save\",\r\n    cancel: \"revert\",\r\n    edit: \"edit\",\r\n    undelete: \"revert\",\r\n    delete: \"trash\",\r\n    add: \"add\"\r\n};\r\nexport const METHOD_NAMES = {\r\n    edit: \"editRow\",\r\n    delete: \"deleteRow\",\r\n    undelete: \"undeleteRow\",\r\n    save: \"saveEditData\",\r\n    cancel: \"cancelEditData\",\r\n    add: \"addRowByRowIndex\"\r\n};\r\nexport const ACTION_OPTION_NAMES = {\r\n    add: \"allowAdding\",\r\n    edit: \"allowUpdating\",\r\n    delete: \"allowDeleting\"\r\n};\r\nexport const BUTTON_NAMES = [\"edit\", \"save\", \"cancel\", \"delete\", \"undelete\"];\r\nexport const EDITING_CHANGES_OPTION_NAME = \"editing.changes\";\r\nexport const FOCUS_OVERLAY_CLASS = \"focus-overlay\";\r\nexport const ADD_ROW_BUTTON_CLASS = \"addrow-button\";\r\nexport const DROPDOWN_EDITOR_OVERLAY_CLASS = \"dx-dropdowneditor-overlay\";\r\nexport const DATA_ROW_CLASS = \"dx-data-row\";\r\nexport const ROW_REMOVED = \"dx-row-removed\";\r\nexport const FILTER_ROW_CLASS = \"filter-row\";\r\nconst isRenovatedScrollable = !!Scrollable.IS_RENOVATED_WIDGET;\r\nexport const EDIT_FORM_ITEM_CLASS = \"edit-form-item\";\r\nexport const EDIT_POPUP_CLASS = \"edit-popup\";\r\nexport const EDIT_POPUP_FORM_CLASS = \"edit-popup-form\";\r\nexport const FOCUSABLE_ELEMENT_CLASS = isRenovatedScrollable ? \"dx-scrollable\" : \"dx-scrollable-container\";\r\nexport const BUTTON_CLASS = \"dx-button\";\r\nexport const FORM_BUTTONS_CONTAINER_CLASS = \"form-buttons-container\";\r\nexport const EDIT_ROW = \"dx-edit-row\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;;AACO,MAAM,oBAAoB;AAC1B,MAAM,YAAY;AAClB,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,MAAM,6BAA6B;AACnC,MAAM,6BAA6B;AACnC,MAAM,4BAA4B;AAClC,MAAM,2BAA2B;AACjC,MAAM,iCAAiC;AACvC,MAAM,qCAAqC;AAC3C,MAAM,wBAAwB;AAC9B,MAAM,yBAAyB;AAC/B,MAAM,6BAA6B,AAAC,+BAAqD,OAAvB,wBAAuB;AACzF,MAAM,kBAAkB;AACxB,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,yBAAyB;AAC/B,MAAM,wBAAwB;AAC9B,MAAM,+BAA+B;AACrC,MAAM,4BAA4B;AAClC,MAAM,mCAAmC;AACzC,MAAM,gCAAgC;AACtC,MAAM,aAAa;IAAC;IAAS;IAAO;IAAQ;IAAQ;CAAQ;AAC5D,MAAM,kBAAkB;IAAC;IAAO;IAAQ;CAAQ;AAChD,MAAM,mBAAmB;IAAC;IAAS;CAAO;AAC1C,MAAM,mCAAmC;IAAC;IAAS;IAAO;CAAO;AACjE,MAAM,2BAA2B;IAAC;IAAO;CAAO;AAChD,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,kBAAkB;AACxB,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB;AAC3B,MAAM,gCAAgC;AACtC,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,gBAAgB;AACtB,MAAM,oBAAoB;AAC1B,MAAM,4BAA4B;AAClC,MAAM,6BAA6B;AACnC,MAAM,4BAA4B;AAClC,MAAM,kBAAkB;IAC3B,MAAM;IACN,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,KAAK;AACT;AACO,MAAM,kBAAkB;IAC3B,MAAM;IACN,QAAQ;IACR,MAAM;IACN,UAAU;IACV,QAAQ;IACR,KAAK;AACT;AACO,MAAM,eAAe;IACxB,MAAM;IACN,QAAQ;IACR,UAAU;IACV,MAAM;IACN,QAAQ;IACR,KAAK;AACT;AACO,MAAM,sBAAsB;IAC/B,KAAK;IACL,MAAM;IACN,QAAQ;AACZ;AACO,MAAM,eAAe;IAAC;IAAQ;IAAQ;IAAU;IAAU;CAAW;AACrE,MAAM,8BAA8B;AACpC,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,gCAAgC;AACtC,MAAM,iBAAiB;AACvB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AAChC,MAAM,wBAAwB,CAAC,CAAC,6KAAA,CAAA,UAAU,CAAC,mBAAmB;AACvD,MAAM,uBAAuB;AAC7B,MAAM,mBAAmB;AACzB,MAAM,wBAAwB;AAC9B,MAAM,0BAA0B,wBAAwB,kBAAkB;AAC1E,MAAM,eAAe;AACrB,MAAM,+BAA+B;AACrC,MAAM,WAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 200, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/editing/m_editing_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/editing/m_editing_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Guid from \"../../../../core/guid\";\r\nimport {\r\n    isObject\r\n} from \"../../../../core/utils/type\";\r\nconst NEW_ROW_TEMP_KEY_PREFIX = \"_DX_KEY_\";\r\nconst GUID_LENGTH = 36;\r\nexport const createFailureHandler = function(deferred) {\r\n    return function(arg) {\r\n        const error = arg instanceof Error ? arg : new Error(arg && String(arg) || \"Unknown error\");\r\n        deferred.reject(error)\r\n    }\r\n};\r\nexport const isEditingCell = function(isEditRow, cellOptions) {\r\n    return cellOptions.isEditing || isEditRow && cellOptions.column.allowEditing\r\n};\r\nexport const isEditingOrShowEditorAlwaysDataCell = function(isEditRow, cellOptions) {\r\n    const isCommandCell = !!cellOptions.column.command;\r\n    const isEditing = isEditingCell(isEditRow, cellOptions);\r\n    const isEditorCell = !isCommandCell && (isEditing || cellOptions.column.showEditorAlways);\r\n    return \"data\" === cellOptions.rowType && isEditorCell\r\n};\r\nexport const getEditingTexts = options => {\r\n    const editingTexts = options.component.option(\"editing.texts\") || {};\r\n    return {\r\n        save: editingTexts.saveRowChanges,\r\n        cancel: editingTexts.cancelRowChanges,\r\n        edit: editingTexts.editRow,\r\n        undelete: editingTexts.undeleteRow,\r\n        delete: editingTexts.deleteRow,\r\n        add: editingTexts.addRowToNode\r\n    }\r\n};\r\nexport const generateNewRowTempKey = () => `_DX_KEY_${new Guid}`;\r\nexport const isNewRowTempKey = key => \"string\" === typeof key && key.startsWith(\"_DX_KEY_\") && 44 === key.length;\r\nexport const getButtonIndex = (buttons, name) => {\r\n    let result = -1;\r\n    buttons.some(((button, index) => {\r\n        if (getButtonName(button) === name) {\r\n            result = index;\r\n            return true\r\n        }\r\n    }));\r\n    return result\r\n};\r\nexport function getButtonName(button) {\r\n    return isObject(button) ? button.name : button\r\n}\r\nexport function isEditable($element) {\r\n    return $element && ($element.is(\"input\") || $element.is(\"textarea\"))\r\n}\r\nexport const getEditorType = item => {\r\n    var _column$formItem;\r\n    const {\r\n        column: column\r\n    } = item;\r\n    return item.isCustomEditorType ? item.editorType : null === (_column$formItem = column.formItem) || void 0 === _column$formItem ? void 0 : _column$formItem.editorType\r\n};\r\nexport const forEachFormItems = (items, callBack) => {\r\n    items.forEach((item => {\r\n        if (item.items || item.tabs) {\r\n            forEachFormItems(item.items || item.tabs, callBack)\r\n        } else {\r\n            callBack(item)\r\n        }\r\n    }))\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;AACD;AACA;AAAA;;;AAGA,MAAM,0BAA0B;AAChC,MAAM,cAAc;AACb,MAAM,uBAAuB,SAAS,QAAQ;IACjD,OAAO,SAAS,GAAG;QACf,MAAM,QAAQ,eAAe,QAAQ,MAAM,IAAI,MAAM,OAAO,OAAO,QAAQ;QAC3E,SAAS,MAAM,CAAC;IACpB;AACJ;AACO,MAAM,gBAAgB,SAAS,SAAS,EAAE,WAAW;IACxD,OAAO,YAAY,SAAS,IAAI,aAAa,YAAY,MAAM,CAAC,YAAY;AAChF;AACO,MAAM,sCAAsC,SAAS,SAAS,EAAE,WAAW;IAC9E,MAAM,gBAAgB,CAAC,CAAC,YAAY,MAAM,CAAC,OAAO;IAClD,MAAM,YAAY,cAAc,WAAW;IAC3C,MAAM,eAAe,CAAC,iBAAiB,CAAC,aAAa,YAAY,MAAM,CAAC,gBAAgB;IACxF,OAAO,WAAW,YAAY,OAAO,IAAI;AAC7C;AACO,MAAM,kBAAkB,CAAA;IAC3B,MAAM,eAAe,QAAQ,SAAS,CAAC,MAAM,CAAC,oBAAoB,CAAC;IACnE,OAAO;QACH,MAAM,aAAa,cAAc;QACjC,QAAQ,aAAa,gBAAgB;QACrC,MAAM,aAAa,OAAO;QAC1B,UAAU,aAAa,WAAW;QAClC,QAAQ,aAAa,SAAS;QAC9B,KAAK,aAAa,YAAY;IAClC;AACJ;AACO,MAAM,wBAAwB,IAAM,AAAC,WAAmB,OAAT,IAAI,oJAAA,CAAA,UAAI;AACvD,MAAM,kBAAkB,CAAA,MAAO,aAAa,OAAO,OAAO,IAAI,UAAU,CAAC,eAAe,OAAO,IAAI,MAAM;AACzG,MAAM,iBAAiB,CAAC,SAAS;IACpC,IAAI,SAAS,CAAC;IACd,QAAQ,IAAI,CAAE,CAAC,QAAQ;QACnB,IAAI,cAAc,YAAY,MAAM;YAChC,SAAS;YACT,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACO,SAAS,cAAc,MAAM;IAChC,OAAO,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,OAAO,IAAI,GAAG;AAC5C;AACO,SAAS,WAAW,QAAQ;IAC/B,OAAO,YAAY,CAAC,SAAS,EAAE,CAAC,YAAY,SAAS,EAAE,CAAC,WAAW;AACvE;AACO,MAAM,gBAAgB,CAAA;IACzB,IAAI;IACJ,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;IACJ,OAAO,KAAK,kBAAkB,GAAG,KAAK,UAAU,GAAG,SAAS,CAAC,mBAAmB,OAAO,QAAQ,KAAK,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,UAAU;AAC1K;AACO,MAAM,mBAAmB,CAAC,OAAO;IACpC,MAAM,OAAO,CAAE,CAAA;QACX,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;YACzB,iBAAiB,KAAK,KAAK,IAAI,KAAK,IAAI,EAAE;QAC9C,OAAO;YACH,SAAS;QACb;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 289, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/editing/m_editing.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/editing/m_editing.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    name as clickEventName\r\n} from \"../../../../common/core/events/click\";\r\nimport eventsEngine from \"../../../../common/core/events/core/events_engine\";\r\nimport pointerEvents from \"../../../../common/core/events/pointer\";\r\nimport {\r\n    removeEvent\r\n} from \"../../../../common/core/events/remove\";\r\nimport {\r\n    addNamespace\r\n} from \"../../../../common/core/events/utils/index\";\r\nimport messageLocalization from \"../../../../common/core/localization/message\";\r\nimport {\r\n    createObjectWithChanges\r\n} from \"../../../../common/data/array_utils\";\r\nimport devices from \"../../../../core/devices\";\r\nimport domAdapter from \"../../../../core/dom_adapter\";\r\nimport Guid from \"../../../../core/guid\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    equalByValue,\r\n    getKeyHash\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    Deferred,\r\n    fromPromise,\r\n    when\r\n} from \"../../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport * as iconUtils from \"../../../../core/utils/icon\";\r\nimport {\r\n    each\r\n} from \"../../../../core/utils/iterator\";\r\nimport {\r\n    deepExtendArraySafe\r\n} from \"../../../../core/utils/object\";\r\nimport {\r\n    isDefined,\r\n    isEmptyObject,\r\n    isFunction,\r\n    isObject\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    confirm\r\n} from \"../../../../ui/dialog\";\r\nimport {\r\n    current,\r\n    isFluent\r\n} from \"../../../../ui/themes\";\r\nimport domUtils from \"../../../core/utils/m_dom\";\r\nimport modules from \"../m_modules\";\r\nimport gridCoreUtils from \"../m_utils\";\r\nimport {\r\n    ACTION_OPTION_NAMES,\r\n    BUTTON_NAMES,\r\n    CELL_BASED_MODES,\r\n    CELL_FOCUS_DISABLED_CLASS,\r\n    CELL_MODIFIED,\r\n    COMMAND_EDIT_CLASS,\r\n    COMMAND_EDIT_WITH_ICONS_CLASS,\r\n    DATA_EDIT_DATA_INSERT_TYPE,\r\n    DATA_EDIT_DATA_REMOVE_TYPE,\r\n    DATA_EDIT_DATA_UPDATE_TYPE,\r\n    DEFAULT_START_EDIT_ACTION,\r\n    EDIT_BUTTON_CLASS,\r\n    EDIT_FORM_CLASS,\r\n    EDIT_ICON_CLASS,\r\n    EDIT_LINK_CLASS,\r\n    EDIT_MODE_POPUP,\r\n    EDIT_MODE_ROW,\r\n    EDIT_MODES,\r\n    EDITING_CHANGES_OPTION_NAME,\r\n    EDITING_EDITCOLUMNNAME_OPTION_NAME,\r\n    EDITING_EDITROWKEY_OPTION_NAME,\r\n    EDITING_NAMESPACE,\r\n    EDITING_POPUP_OPTION_NAME,\r\n    EDITOR_CELL_CLASS,\r\n    EDITORS_INPUT_SELECTOR,\r\n    FIRST_NEW_ROW_POSITION,\r\n    FOCUSABLE_ELEMENT_SELECTOR,\r\n    INSERT_INDEX,\r\n    LAST_NEW_ROW_POSITION,\r\n    LINK_CLASS,\r\n    LINK_ICON_CLASS,\r\n    METHOD_NAMES,\r\n    PAGE_BOTTOM_NEW_ROW_POSITION,\r\n    PAGE_TOP_NEW_ROW_POSITION,\r\n    READONLY_CLASS,\r\n    REQUIRED_EDITOR_LABELLEDBY_MODES,\r\n    ROW_BASED_MODES,\r\n    ROW_CLASS,\r\n    ROW_INSERTED,\r\n    ROW_MODIFIED,\r\n    ROW_SELECTED,\r\n    TARGET_COMPONENT_NAME,\r\n    VIEWPORT_BOTTOM_NEW_ROW_POSITION,\r\n    VIEWPORT_TOP_NEW_ROW_POSITION\r\n} from \"./const\";\r\nimport {\r\n    createFailureHandler,\r\n    generateNewRowTempKey,\r\n    getButtonIndex,\r\n    getButtonName,\r\n    getEditingTexts,\r\n    isEditingCell,\r\n    isEditingOrShowEditorAlwaysDataCell\r\n} from \"./m_editing_utils\";\r\nclass EditingControllerImpl extends modules.ViewController {\r\n    init() {\r\n        this._columnsController = this.getController(\"columns\");\r\n        this._dataController = this.getController(\"data\");\r\n        this._adaptiveColumnsController = this.getController(\"adaptiveColumns\");\r\n        this._validatingController = this.getController(\"validating\");\r\n        this._editorFactoryController = this.getController(\"editorFactory\");\r\n        this._focusController = this.getController(\"focus\");\r\n        this._keyboardNavigationController = this.getController(\"keyboardNavigation\");\r\n        this._columnsResizerController = this.getController(\"columnsResizer\");\r\n        this._errorHandlingController = this.getController(\"errorHandling\");\r\n        this._rowsView = this.getView(\"rowsView\");\r\n        this._headerPanelView = this.getView(\"headerPanel\");\r\n        this._lastOperation = null;\r\n        this._changes = [];\r\n        if (this._deferreds) {\r\n            this._deferreds.forEach((d => {\r\n                d.reject(\"cancel\")\r\n            }))\r\n        }\r\n        this._deferreds = [];\r\n        if (!this._dataChangedHandler) {\r\n            this._dataChangedHandler = this._handleDataChanged.bind(this);\r\n            this._dataController.changed.add(this._dataChangedHandler)\r\n        }\r\n        if (!this._saveEditorHandler) {\r\n            this.createAction(\"onInitNewRow\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onRowInserting\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onRowInserted\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onEditingStart\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onRowUpdating\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onRowUpdated\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onRowRemoving\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onRowRemoved\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onSaved\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onSaving\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onEditCanceling\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            });\r\n            this.createAction(\"onEditCanceled\", {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            })\r\n        }\r\n        this._updateEditColumn();\r\n        this._updateEditButtons();\r\n        if (!this._internalState) {\r\n            this._internalState = new Map\r\n        }\r\n        this.component._optionsByReference[EDITING_EDITROWKEY_OPTION_NAME] = true;\r\n        this.component._optionsByReference[EDITING_CHANGES_OPTION_NAME] = true\r\n    }\r\n    getEditMode() {\r\n        const editMode = this.option(\"editing.mode\") ?? EDIT_MODE_ROW;\r\n        if (EDIT_MODES.includes(editMode)) {\r\n            return editMode\r\n        }\r\n        return EDIT_MODE_ROW\r\n    }\r\n    isCellBasedEditMode() {\r\n        const editMode = this.getEditMode();\r\n        return CELL_BASED_MODES.includes(editMode)\r\n    }\r\n    _getDefaultEditorTemplate() {\r\n        return (container, options) => {\r\n            const $editor = $(\"<div>\").appendTo(container);\r\n            const editorOptions = extend({}, options.column, {\r\n                value: options.value,\r\n                setValue: options.setValue,\r\n                row: options.row,\r\n                parentType: \"dataRow\",\r\n                width: null,\r\n                readOnly: !options.setValue,\r\n                isOnForm: options.isOnForm,\r\n                id: options.id\r\n            });\r\n            const needLabel = REQUIRED_EDITOR_LABELLEDBY_MODES.includes(this.getEditMode());\r\n            if (needLabel) {\r\n                editorOptions[\"aria-labelledby\"] = options.column.headerId\r\n            }\r\n            this._editorFactoryController.createEditor($editor, editorOptions)\r\n        }\r\n    }\r\n    _getNewRowPosition() {\r\n        const newRowPosition = this.option(\"editing.newRowPosition\");\r\n        const scrollingMode = this.option(\"scrolling.mode\");\r\n        if (\"virtual\" === scrollingMode) {\r\n            switch (newRowPosition) {\r\n                case PAGE_TOP_NEW_ROW_POSITION:\r\n                    return VIEWPORT_TOP_NEW_ROW_POSITION;\r\n                case PAGE_BOTTOM_NEW_ROW_POSITION:\r\n                    return VIEWPORT_BOTTOM_NEW_ROW_POSITION;\r\n                default:\r\n                    return newRowPosition\r\n            }\r\n        }\r\n        return newRowPosition\r\n    }\r\n    getChanges() {\r\n        return this.option(EDITING_CHANGES_OPTION_NAME)\r\n    }\r\n    getInsertRowCount() {\r\n        const changes = this.option(EDITING_CHANGES_OPTION_NAME);\r\n        return changes.filter((change => \"insert\" === change.type)).length\r\n    }\r\n    resetChanges() {\r\n        const changes = this.getChanges();\r\n        const needReset = null === changes || void 0 === changes ? void 0 : changes.length;\r\n        if (needReset) {\r\n            this._silentOption(EDITING_CHANGES_OPTION_NAME, []);\r\n            this._internalState.clear()\r\n        }\r\n    }\r\n    _getInternalData(key) {\r\n        return this._internalState.get(getKeyHash(key))\r\n    }\r\n    _addInternalData(params) {\r\n        const internalData = this._getInternalData(params.key);\r\n        if (internalData) {\r\n            return extend(internalData, params)\r\n        }\r\n        this._internalState.set(getKeyHash(params.key), params);\r\n        return params\r\n    }\r\n    _getOldData(key) {\r\n        var _this$_getInternalDat;\r\n        return null === (_this$_getInternalDat = this._getInternalData(key)) || void 0 === _this$_getInternalDat ? void 0 : _this$_getInternalDat.oldData\r\n    }\r\n    getUpdatedData(data) {\r\n        const key = this._dataController.keyOf(data);\r\n        const changes = this.getChanges();\r\n        const editIndex = gridCoreUtils.getIndexByKey(key, changes);\r\n        if (changes[editIndex]) {\r\n            return createObjectWithChanges(data, changes[editIndex].data)\r\n        }\r\n        return data\r\n    }\r\n    getInsertedData() {\r\n        return this.getChanges().filter((change => change.data && change.type === DATA_EDIT_DATA_INSERT_TYPE)).map((change => change.data))\r\n    }\r\n    getRemovedData() {\r\n        return this.getChanges().filter((change => this._getOldData(change.key) && change.type === DATA_EDIT_DATA_REMOVE_TYPE)).map((change => this._getOldData(change.key)))\r\n    }\r\n    _fireDataErrorOccurred(arg) {\r\n        if (\"cancel\" === arg) {\r\n            return\r\n        }\r\n        const $popupContent = this.getPopupContent();\r\n        this._dataController.dataErrorOccurred.fire(arg, $popupContent)\r\n    }\r\n    _needToCloseEditableCell($targetElement) {}\r\n    _closeEditItem($targetElement) {}\r\n    _handleDataChanged(args) {}\r\n    _isDefaultButtonVisible(button, options) {\r\n        let result = true;\r\n        switch (button.name) {\r\n            case \"delete\":\r\n                result = this.allowDeleting(options);\r\n                break;\r\n            case \"undelete\":\r\n                result = false\r\n        }\r\n        return result\r\n    }\r\n    isPopupEditMode() {\r\n        const editMode = this.option(\"editing.mode\");\r\n        return editMode === EDIT_MODE_POPUP\r\n    }\r\n    _isButtonVisible(button, options) {\r\n        const {\r\n            visible: visible\r\n        } = button;\r\n        if (!isDefined(visible)) {\r\n            return this._isDefaultButtonVisible(button, options)\r\n        }\r\n        return isFunction(visible) ? visible.call(button, {\r\n            component: options.component,\r\n            row: options.row,\r\n            column: options.column\r\n        }) : visible\r\n    }\r\n    _isButtonDisabled(button, options) {\r\n        const {\r\n            disabled: disabled\r\n        } = button;\r\n        return isFunction(disabled) ? disabled.call(button, {\r\n            component: options.component,\r\n            row: options.row,\r\n            column: options.column\r\n        }) : !!disabled\r\n    }\r\n    _getButtonConfig(button, options) {\r\n        const config = isObject(button) ? button : {};\r\n        const buttonName = getButtonName(button);\r\n        const editingTexts = getEditingTexts(options);\r\n        const methodName = METHOD_NAMES[buttonName];\r\n        const editingOptions = this.option(\"editing\");\r\n        const actionName = ACTION_OPTION_NAMES[buttonName];\r\n        const allowAction = actionName ? editingOptions[actionName] : true;\r\n        return extend({\r\n            name: buttonName,\r\n            text: editingTexts[buttonName],\r\n            cssClass: EDIT_LINK_CLASS[buttonName]\r\n        }, {\r\n            onClick: methodName && (e => {\r\n                const {\r\n                    event: event\r\n                } = e;\r\n                event.stopPropagation();\r\n                event.preventDefault();\r\n                setTimeout((() => {\r\n                    options.row && allowAction && this[methodName] && this[methodName](options.row.rowIndex)\r\n                }))\r\n            })\r\n        }, config)\r\n    }\r\n    _getEditingButtons(options) {\r\n        let buttonIndex;\r\n        const haveCustomButtons = !!options.column.buttons;\r\n        let buttons = (options.column.buttons || []).slice();\r\n        if (haveCustomButtons) {\r\n            buttonIndex = getButtonIndex(buttons, \"edit\");\r\n            if (buttonIndex >= 0) {\r\n                if (getButtonIndex(buttons, \"save\") < 0) {\r\n                    buttons.splice(buttonIndex + 1, 0, \"save\")\r\n                }\r\n                if (getButtonIndex(buttons, \"cancel\") < 0) {\r\n                    buttons.splice(getButtonIndex(buttons, \"save\") + 1, 0, \"cancel\")\r\n                }\r\n            }\r\n            buttonIndex = getButtonIndex(buttons, \"delete\");\r\n            if (buttonIndex >= 0 && getButtonIndex(buttons, \"undelete\") < 0) {\r\n                buttons.splice(buttonIndex + 1, 0, \"undelete\")\r\n            }\r\n        } else {\r\n            buttons = BUTTON_NAMES.slice()\r\n        }\r\n        return buttons.map((button => this._getButtonConfig(button, options)))\r\n    }\r\n    _renderEditingButtons($container, buttons, options, change) {\r\n        buttons.forEach((button => {\r\n            if (this._isButtonVisible(button, options)) {\r\n                this._createButton($container, button, options, change)\r\n            }\r\n        }))\r\n    }\r\n    _getEditCommandCellTemplate() {\r\n        return (container, options, change) => {\r\n            const $container = $(container);\r\n            if (\"data\" === options.rowType) {\r\n                const buttons = this._getEditingButtons(options);\r\n                this._renderEditingButtons($container, buttons, options, change);\r\n                if (options.watch) {\r\n                    const dispose = options.watch((() => buttons.map((button => ({\r\n                        visible: this._isButtonVisible(button, options),\r\n                        disabled: this._isButtonDisabled(button, options)\r\n                    })))), (() => {\r\n                        $container.empty();\r\n                        this._renderEditingButtons($container, buttons, options)\r\n                    }));\r\n                    eventsEngine.on($container, removeEvent, dispose)\r\n                }\r\n            } else {\r\n                gridCoreUtils.setEmptyText($container)\r\n            }\r\n        }\r\n    }\r\n    isRowBasedEditMode() {\r\n        const editMode = this.getEditMode();\r\n        return ROW_BASED_MODES.includes(editMode)\r\n    }\r\n    getFirstEditableColumnIndex() {\r\n        let columnIndex;\r\n        const visibleColumns = this._columnsController.getVisibleColumns();\r\n        each(visibleColumns, ((index, column) => {\r\n            if (column.allowEditing) {\r\n                columnIndex = index;\r\n                return false\r\n            }\r\n        }));\r\n        return columnIndex\r\n    }\r\n    getFirstEditableCellInRow(rowIndex) {\r\n        var _this$_rowsView;\r\n        const columnIndex = this.getFirstEditableColumnIndex();\r\n        return null === (_this$_rowsView = this._rowsView) || void 0 === _this$_rowsView ? void 0 : _this$_rowsView._getCellElement(rowIndex || 0, columnIndex)\r\n    }\r\n    getFocusedCellInRow(rowIndex) {\r\n        return this.getFirstEditableCellInRow(rowIndex)\r\n    }\r\n    getIndexByKey(key, items) {\r\n        return gridCoreUtils.getIndexByKey(key, items)\r\n    }\r\n    hasChanges(rowIndex) {\r\n        const changes = this.getChanges();\r\n        let result = false;\r\n        for (let i = 0; i < (null === changes || void 0 === changes ? void 0 : changes.length); i++) {\r\n            if (changes[i].type && (!isDefined(rowIndex) || this._dataController.getRowIndexByKey(changes[i].key) === rowIndex)) {\r\n                result = true;\r\n                break\r\n            }\r\n        }\r\n        return result\r\n    }\r\n    dispose() {\r\n        super.dispose();\r\n        clearTimeout(this._inputFocusTimeoutID);\r\n        eventsEngine.off(domAdapter.getDocument(), pointerEvents.up, this._pointerUpEditorHandler);\r\n        eventsEngine.off(domAdapter.getDocument(), pointerEvents.down, this._pointerDownEditorHandler);\r\n        eventsEngine.off(domAdapter.getDocument(), clickEventName, this._saveEditorHandler)\r\n    }\r\n    _silentOption(name, value) {\r\n        if (\"editing.changes\" === name) {\r\n            this._changes = deepExtendArraySafe([], value)\r\n        }\r\n        super._silentOption(name, value)\r\n    }\r\n    optionChanged(args) {\r\n        if (\"editing\" === args.name) {\r\n            const {\r\n                fullName: fullName\r\n            } = args;\r\n            if (fullName === EDITING_EDITROWKEY_OPTION_NAME) {\r\n                this._handleEditRowKeyChange(args)\r\n            } else if (fullName === EDITING_CHANGES_OPTION_NAME) {\r\n                const isEqual = equalByValue(args.value, this._changes, {\r\n                    maxDepth: 4\r\n                });\r\n                if (!isEqual) {\r\n                    this._changes = deepExtendArraySafe([], args.value);\r\n                    this._handleChangesChange(args)\r\n                }\r\n            } else if (!args.handled) {\r\n                this._columnsController.reinit();\r\n                this.init();\r\n                this.resetChanges();\r\n                this._resetEditColumnName();\r\n                this._resetEditRowKey()\r\n            }\r\n            args.handled = true\r\n        } else {\r\n            super.optionChanged(args)\r\n        }\r\n    }\r\n    _handleEditRowKeyChange(args) {\r\n        const rowIndex = this._dataController.getRowIndexByKey(args.value);\r\n        const oldRowIndexCorrection = this._getEditRowIndexCorrection();\r\n        const oldRowIndex = this._dataController.getRowIndexByKey(args.previousValue) + oldRowIndexCorrection;\r\n        if (isDefined(args.value)) {\r\n            if (args.value !== args.previousValue) {\r\n                this._editRowFromOptionChanged(rowIndex, oldRowIndex)\r\n            }\r\n        } else {\r\n            this.cancelEditData()\r\n        }\r\n    }\r\n    _handleChangesChange(args) {\r\n        const dataController = this._dataController;\r\n        const changes = args.value;\r\n        if (!args.value.length && !args.previousValue.length) {\r\n            return\r\n        }\r\n        changes.forEach((change => {\r\n            if (\"insert\" === change.type) {\r\n                this._addInsertInfo(change)\r\n            } else {\r\n                var _dataController$items;\r\n                const items = dataController.getCachedStoreData() || (null === (_dataController$items = dataController.items()) || void 0 === _dataController$items ? void 0 : _dataController$items.map((item => item.data)));\r\n                const rowIndex = gridCoreUtils.getIndexByKey(change.key, items, dataController.key());\r\n                this._addInternalData({\r\n                    key: change.key,\r\n                    oldData: items[rowIndex]\r\n                })\r\n            }\r\n        }));\r\n        dataController.updateItems({\r\n            repaintChangesOnly: true,\r\n            isLiveUpdate: false,\r\n            isOptionChanged: true\r\n        })\r\n    }\r\n    publicMethods() {\r\n        return [\"addRow\", \"deleteRow\", \"undeleteRow\", \"editRow\", \"saveEditData\", \"cancelEditData\", \"hasEditData\"]\r\n    }\r\n    refresh() {\r\n        if (!isDefined(this._pageIndex)) {\r\n            return\r\n        }\r\n        this._refreshCore.apply(this, arguments)\r\n    }\r\n    _refreshCore(params) {}\r\n    isEditing() {\r\n        const isEditRowKeyDefined = isDefined(this.option(EDITING_EDITROWKEY_OPTION_NAME));\r\n        return isEditRowKeyDefined\r\n    }\r\n    isEditRow(rowIndex) {\r\n        return false\r\n    }\r\n    _setEditRowKey(value, silent) {\r\n        if (silent) {\r\n            this._silentOption(EDITING_EDITROWKEY_OPTION_NAME, value)\r\n        } else {\r\n            this.option(EDITING_EDITROWKEY_OPTION_NAME, value)\r\n        }\r\n        if (this._refocusEditCell) {\r\n            this._refocusEditCell = false;\r\n            this._focusEditingCell()\r\n        }\r\n    }\r\n    _setEditRowKeyByIndex(rowIndex, silent) {\r\n        const key = this._dataController.getKeyByRowIndex(rowIndex);\r\n        if (void 0 === key) {\r\n            this._dataController.fireError(\"E1043\");\r\n            return\r\n        }\r\n        this._setEditRowKey(key, silent)\r\n    }\r\n    getEditRowIndex() {\r\n        return this._getVisibleEditRowIndex()\r\n    }\r\n    getEditFormRowIndex() {\r\n        return -1\r\n    }\r\n    isEditRowByIndex(rowIndex) {\r\n        const key = this._dataController.getKeyByRowIndex(rowIndex);\r\n        const isKeyEqual = isDefined(key) && equalByValue(this.option(EDITING_EDITROWKEY_OPTION_NAME), key);\r\n        if (isKeyEqual) {\r\n            return this._getVisibleEditRowIndex() === rowIndex\r\n        }\r\n        return isKeyEqual\r\n    }\r\n    isEditCell(visibleRowIndex, columnIndex) {\r\n        return this.isEditRowByIndex(visibleRowIndex) && this._getVisibleEditColumnIndex() === columnIndex\r\n    }\r\n    getPopupContent() {}\r\n    _isProcessedItem(item) {\r\n        return false\r\n    }\r\n    _getInsertRowIndex(items, change, isProcessedItems) {\r\n        let result = -1;\r\n        const dataController = this._dataController;\r\n        const key = this._getInsertAfterOrBeforeKey(change);\r\n        if (!isDefined(key) && 0 === items.length) {\r\n            result = 0\r\n        } else if (isDefined(key)) {\r\n            items.some(((item, index) => {\r\n                const isProcessedItem = isProcessedItems || this._isProcessedItem(item);\r\n                if (isObject(item)) {\r\n                    if (isProcessedItem || isDefined(item[INSERT_INDEX])) {\r\n                        if (equalByValue(item.key, key)) {\r\n                            result = index\r\n                        }\r\n                    } else if (equalByValue(dataController.keyOf(item), key)) {\r\n                        result = index\r\n                    }\r\n                }\r\n                if (result >= 0) {\r\n                    const nextItem = items[result + 1];\r\n                    if (nextItem && (\"detail\" === nextItem.rowType || \"detailAdaptive\" === nextItem.rowType) && isDefined(change.insertAfterKey)) {\r\n                        return\r\n                    }\r\n                    if (isDefined(change.insertAfterKey)) {\r\n                        result += 1\r\n                    }\r\n                    return true\r\n                }\r\n            }))\r\n        }\r\n        return result\r\n    }\r\n    _generateNewItem(key) {\r\n        var _this$_getInternalDat2;\r\n        const item = {\r\n            key: key\r\n        };\r\n        const insertInfo = null === (_this$_getInternalDat2 = this._getInternalData(key)) || void 0 === _this$_getInternalDat2 ? void 0 : _this$_getInternalDat2.insertInfo;\r\n        if (null !== insertInfo && void 0 !== insertInfo && insertInfo[INSERT_INDEX]) {\r\n            item[INSERT_INDEX] = insertInfo[INSERT_INDEX]\r\n        }\r\n        return item\r\n    }\r\n    _getLoadedRowIndex(items, change, isProcessedItems) {\r\n        let loadedRowIndex = this._getInsertRowIndex(items, change, isProcessedItems);\r\n        const dataController = this._dataController;\r\n        if (loadedRowIndex < 0) {\r\n            const newRowPosition = this._getNewRowPosition();\r\n            const pageIndex = dataController.pageIndex();\r\n            const insertAfterOrBeforeKey = this._getInsertAfterOrBeforeKey(change);\r\n            if (newRowPosition !== LAST_NEW_ROW_POSITION && 0 === pageIndex && !isDefined(insertAfterOrBeforeKey)) {\r\n                loadedRowIndex = 0\r\n            } else if (newRowPosition === LAST_NEW_ROW_POSITION && dataController.isLastPageLoaded()) {\r\n                loadedRowIndex = items.length\r\n            }\r\n        }\r\n        return loadedRowIndex\r\n    }\r\n    processItems(items, e) {\r\n        const {\r\n            changeType: changeType\r\n        } = e;\r\n        this.update(changeType);\r\n        const changes = this.getChanges();\r\n        changes.forEach((change => {\r\n            var _this$_getInternalDat3;\r\n            const isInsert = change.type === DATA_EDIT_DATA_INSERT_TYPE;\r\n            if (!isInsert) {\r\n                return\r\n            }\r\n            let {\r\n                key: key\r\n            } = change;\r\n            let insertInfo = null === (_this$_getInternalDat3 = this._getInternalData(key)) || void 0 === _this$_getInternalDat3 ? void 0 : _this$_getInternalDat3.insertInfo;\r\n            if (!isDefined(key) || !isDefined(insertInfo)) {\r\n                insertInfo = this._addInsertInfo(change);\r\n                key = insertInfo.key\r\n            }\r\n            const loadedRowIndex = this._getLoadedRowIndex(items, change);\r\n            const item = this._generateNewItem(key);\r\n            if (loadedRowIndex >= 0) {\r\n                items.splice(loadedRowIndex, 0, item)\r\n            }\r\n        }));\r\n        return items\r\n    }\r\n    processDataItem(item, options, generateDataValues) {\r\n        const columns = options.visibleColumns;\r\n        const key = item.data[INSERT_INDEX] ? item.data.key : item.key;\r\n        const changes = this.getChanges();\r\n        const editIndex = gridCoreUtils.getIndexByKey(key, changes);\r\n        item.isEditing = false;\r\n        if (editIndex >= 0) {\r\n            this._processDataItemCore(item, changes[editIndex], key, columns, generateDataValues)\r\n        }\r\n    }\r\n    _processDataItemCore(item, change, key, columns, generateDataValues) {\r\n        const {\r\n            data: data,\r\n            type: type\r\n        } = change;\r\n        switch (type) {\r\n            case DATA_EDIT_DATA_INSERT_TYPE:\r\n                item.isNewRow = true;\r\n                item.key = key;\r\n                item.data = data;\r\n                break;\r\n            case DATA_EDIT_DATA_UPDATE_TYPE:\r\n                item.modified = true;\r\n                item.oldData = item.data;\r\n                item.data = createObjectWithChanges(item.data, data);\r\n                item.modifiedValues = generateDataValues(data, columns, true);\r\n                break;\r\n            case DATA_EDIT_DATA_REMOVE_TYPE:\r\n                item.removed = true\r\n        }\r\n    }\r\n    _initNewRow(options) {\r\n        this.executeAction(\"onInitNewRow\", options);\r\n        if (options.promise) {\r\n            const deferred = new Deferred;\r\n            when(fromPromise(options.promise)).done(deferred.resolve).fail(createFailureHandler(deferred)).fail((arg => this._fireDataErrorOccurred(arg)));\r\n            return deferred\r\n        }\r\n    }\r\n    _createInsertInfo() {\r\n        const insertInfo = {};\r\n        insertInfo[INSERT_INDEX] = this._getInsertIndex();\r\n        return insertInfo\r\n    }\r\n    _addInsertInfo(change, parentKey) {\r\n        var _this$_getInternalDat4;\r\n        let insertInfo;\r\n        change.key = this.getChangeKeyValue(change);\r\n        const {\r\n            key: key\r\n        } = change;\r\n        insertInfo = null === (_this$_getInternalDat4 = this._getInternalData(key)) || void 0 === _this$_getInternalDat4 ? void 0 : _this$_getInternalDat4.insertInfo;\r\n        if (!isDefined(insertInfo)) {\r\n            const insertAfterOrBeforeKey = this._getInsertAfterOrBeforeKey(change);\r\n            insertInfo = this._createInsertInfo();\r\n            if (!isDefined(insertAfterOrBeforeKey)) {\r\n                this._setInsertAfterOrBeforeKey(change, parentKey)\r\n            }\r\n        }\r\n        this._addInternalData({\r\n            insertInfo: insertInfo,\r\n            key: key\r\n        });\r\n        return {\r\n            insertInfo: insertInfo,\r\n            key: key\r\n        }\r\n    }\r\n    getChangeKeyValue(change) {\r\n        if (isDefined(change.key)) {\r\n            return change.key\r\n        }\r\n        const keyExpr = this._dataController.key();\r\n        let keyValue;\r\n        if (change.data && keyExpr && !Array.isArray(keyExpr)) {\r\n            keyValue = change.data[keyExpr]\r\n        }\r\n        if (!isDefined(keyValue)) {\r\n            keyValue = generateNewRowTempKey()\r\n        }\r\n        return keyValue\r\n    }\r\n    _setInsertAfterOrBeforeKey(change, parentKey) {\r\n        const rowsView = this.getView(\"rowsView\");\r\n        const dataController = this._dataController;\r\n        const allItems = dataController.items(true);\r\n        const newRowPosition = this._getNewRowPosition();\r\n        switch (newRowPosition) {\r\n            case FIRST_NEW_ROW_POSITION:\r\n            case LAST_NEW_ROW_POSITION:\r\n                break;\r\n            case PAGE_TOP_NEW_ROW_POSITION:\r\n                if (allItems.length) {\r\n                    change.insertBeforeKey = allItems[0].key\r\n                }\r\n                break;\r\n            case PAGE_BOTTOM_NEW_ROW_POSITION:\r\n                if (allItems.length) {\r\n                    change.insertAfterKey = allItems[allItems.length - 1].key\r\n                }\r\n                break;\r\n            default: {\r\n                const isViewportBottom = newRowPosition === VIEWPORT_BOTTOM_NEW_ROW_POSITION;\r\n                let visibleItemIndex = isViewportBottom ? null === rowsView || void 0 === rowsView ? void 0 : rowsView.getBottomVisibleItemIndex() : null === rowsView || void 0 === rowsView ? void 0 : rowsView.getTopVisibleItemIndex();\r\n                const row = dataController.getVisibleRows()[visibleItemIndex];\r\n                if (row && (!row.isEditing && \"detail\" === row.rowType || \"detailAdaptive\" === row.rowType)) {\r\n                    visibleItemIndex++\r\n                }\r\n                const insertKey = dataController.getKeyByRowIndex(visibleItemIndex);\r\n                if (isDefined(insertKey)) {\r\n                    change.insertBeforeKey = insertKey\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _getInsertIndex() {\r\n        let maxInsertIndex = 0;\r\n        this.getChanges().forEach((editItem => {\r\n            var _this$_getInternalDat5;\r\n            const insertInfo = null === (_this$_getInternalDat5 = this._getInternalData(editItem.key)) || void 0 === _this$_getInternalDat5 ? void 0 : _this$_getInternalDat5.insertInfo;\r\n            if (isDefined(insertInfo) && editItem.type === DATA_EDIT_DATA_INSERT_TYPE && insertInfo[INSERT_INDEX] > maxInsertIndex) {\r\n                maxInsertIndex = insertInfo[INSERT_INDEX]\r\n            }\r\n        }));\r\n        return maxInsertIndex + 1\r\n    }\r\n    _getInsertAfterOrBeforeKey(insertChange) {\r\n        return insertChange.insertAfterKey ?? insertChange.insertBeforeKey\r\n    }\r\n    _getPageIndexToInsertRow() {\r\n        const newRowPosition = this._getNewRowPosition();\r\n        const dataController = this._dataController;\r\n        const pageIndex = dataController.pageIndex();\r\n        const lastPageIndex = dataController.pageCount() - 1;\r\n        if (newRowPosition === FIRST_NEW_ROW_POSITION && 0 !== pageIndex) {\r\n            return 0\r\n        }\r\n        if (newRowPosition === LAST_NEW_ROW_POSITION && pageIndex !== lastPageIndex) {\r\n            return lastPageIndex\r\n        }\r\n        return -1\r\n    }\r\n    addRow(parentKey) {\r\n        const dataController = this._dataController;\r\n        const store = dataController.store();\r\n        if (!store) {\r\n            dataController.fireError(\"E1052\", this.component.NAME);\r\n            return (new Deferred).reject()\r\n        }\r\n        return this._addRow(parentKey)\r\n    }\r\n    _addRow(parentKey) {\r\n        const dataController = this._dataController;\r\n        const store = dataController.store();\r\n        const key = store && store.key();\r\n        const param = {\r\n            data: {}\r\n        };\r\n        const oldEditRowIndex = this._getVisibleEditRowIndex();\r\n        const deferred = new Deferred;\r\n        this.refresh({\r\n            allowCancelEditing: true\r\n        });\r\n        if (!this._allowRowAdding()) {\r\n            when(this._navigateToNewRow(oldEditRowIndex)).done(deferred.resolve).fail(deferred.reject);\r\n            return deferred.promise()\r\n        }\r\n        if (!key) {\r\n            param.data.__KEY__ = String(new Guid)\r\n        }\r\n        when(this._initNewRow(param, parentKey)).done((() => {\r\n            if (this._allowRowAdding()) {\r\n                when(this._addRowCore(param.data, parentKey, oldEditRowIndex)).done(deferred.resolve).fail(deferred.reject)\r\n            } else {\r\n                deferred.reject(\"cancel\")\r\n            }\r\n        })).fail(deferred.reject);\r\n        return deferred.promise()\r\n    }\r\n    _allowRowAdding(params) {\r\n        const insertIndex = this._getInsertIndex();\r\n        if (insertIndex > 1) {\r\n            return false\r\n        }\r\n        return true\r\n    }\r\n    _addRowCore(data, parentKey, initialOldEditRowIndex) {\r\n        const change = {\r\n            data: data,\r\n            type: DATA_EDIT_DATA_INSERT_TYPE\r\n        };\r\n        const editRowIndex = this._getVisibleEditRowIndex();\r\n        const insertInfo = this._addInsertInfo(change, parentKey);\r\n        const {\r\n            key: key\r\n        } = insertInfo;\r\n        this._setEditRowKey(key, true);\r\n        this._addChange(change);\r\n        return this._navigateToNewRow(initialOldEditRowIndex, change, editRowIndex)\r\n    }\r\n    _navigateToNewRow(oldEditRowIndex, change, editRowIndex) {\r\n        const d = new Deferred;\r\n        const dataController = this._dataController;\r\n        editRowIndex = editRowIndex ?? -1;\r\n        change = change ?? this.getChanges().filter((c => c.type === DATA_EDIT_DATA_INSERT_TYPE))[0];\r\n        if (!change) {\r\n            return d.reject(\"cancel\").promise()\r\n        }\r\n        const pageIndexToInsertRow = this._getPageIndexToInsertRow();\r\n        let rowIndex = this._getLoadedRowIndex(dataController.items(), change, true);\r\n        const navigateToRowByKey = key => {\r\n            var _this$_focusControlle;\r\n            when(null === (_this$_focusControlle = this._focusController) || void 0 === _this$_focusControlle ? void 0 : _this$_focusControlle.navigateToRow(key)).done((() => {\r\n                rowIndex = dataController.getRowIndexByKey(change.key);\r\n                d.resolve()\r\n            }))\r\n        };\r\n        const insertAfterOrBeforeKey = this._getInsertAfterOrBeforeKey(change);\r\n        if (pageIndexToInsertRow >= 0) {\r\n            dataController.pageIndex(pageIndexToInsertRow).done((() => {\r\n                navigateToRowByKey(change.key)\r\n            })).fail(d.reject)\r\n        } else if (rowIndex < 0 && isDefined(insertAfterOrBeforeKey)) {\r\n            navigateToRowByKey(insertAfterOrBeforeKey)\r\n        } else {\r\n            dataController.updateItems({\r\n                changeType: \"update\",\r\n                rowIndices: [oldEditRowIndex, editRowIndex, rowIndex]\r\n            });\r\n            rowIndex = dataController.getRowIndexByKey(change.key);\r\n            if (rowIndex < 0) {\r\n                navigateToRowByKey(change.key)\r\n            } else {\r\n                d.resolve()\r\n            }\r\n        }\r\n        d.done((() => {\r\n            var _this$_rowsView2;\r\n            null === (_this$_rowsView2 = this._rowsView) || void 0 === _this$_rowsView2 || _this$_rowsView2.waitAsyncTemplates(true).done((() => {\r\n                this._showAddedRow(rowIndex);\r\n                this._afterInsertRow(change.key)\r\n            }))\r\n        }));\r\n        return d.promise()\r\n    }\r\n    _showAddedRow(rowIndex) {\r\n        this._focusFirstEditableCellInRow(rowIndex)\r\n    }\r\n    _beforeFocusElementInRow(rowIndex) {}\r\n    _focusFirstEditableCellInRow(rowIndex) {\r\n        var _this$_keyboardNaviga;\r\n        const dataController = this._dataController;\r\n        const key = dataController.getKeyByRowIndex(rowIndex);\r\n        const $firstCell = this.getFirstEditableCellInRow(rowIndex);\r\n        null === (_this$_keyboardNaviga = this._keyboardNavigationController) || void 0 === _this$_keyboardNaviga || _this$_keyboardNaviga.focus($firstCell);\r\n        this.option(\"focusedRowKey\", key);\r\n        this._editCellInProgress = true;\r\n        this._delayedInputFocus($firstCell, (() => {\r\n            rowIndex = dataController.getRowIndexByKey(key);\r\n            this._editCellInProgress = false;\r\n            this._beforeFocusElementInRow(rowIndex)\r\n        }))\r\n    }\r\n    _isEditingStart(options) {\r\n        this.executeAction(\"onEditingStart\", options);\r\n        return options.cancel\r\n    }\r\n    _beforeUpdateItems(rowIndices, rowIndex) {}\r\n    _getVisibleEditColumnIndex() {\r\n        const editColumnName = this.option(EDITING_EDITCOLUMNNAME_OPTION_NAME);\r\n        if (!isDefined(editColumnName)) {\r\n            return -1\r\n        }\r\n        return this._columnsController.getVisibleColumnIndex(editColumnName)\r\n    }\r\n    _setEditColumnNameByIndex(index, silent) {\r\n        var _visibleColumns$index;\r\n        const visibleColumns = this._columnsController.getVisibleColumns();\r\n        this._setEditColumnName(null === (_visibleColumns$index = visibleColumns[index]) || void 0 === _visibleColumns$index ? void 0 : _visibleColumns$index.name, silent)\r\n    }\r\n    _setEditColumnName(name, silent) {\r\n        if (silent) {\r\n            this._silentOption(EDITING_EDITCOLUMNNAME_OPTION_NAME, name)\r\n        } else {\r\n            this.option(EDITING_EDITCOLUMNNAME_OPTION_NAME, name)\r\n        }\r\n    }\r\n    _resetEditColumnName() {\r\n        this._setEditColumnName(null, true)\r\n    }\r\n    _getEditColumn() {\r\n        const editColumnName = this.option(EDITING_EDITCOLUMNNAME_OPTION_NAME);\r\n        return this._getColumnByName(editColumnName)\r\n    }\r\n    _getColumnByName(name) {\r\n        const visibleColumns = this._columnsController.getVisibleColumns();\r\n        let editColumn;\r\n        isDefined(name) && visibleColumns.some((column => {\r\n            if (column.name === name) {\r\n                editColumn = column;\r\n                return true\r\n            }\r\n        }));\r\n        return editColumn\r\n    }\r\n    _getVisibleEditRowIndex(columnName) {\r\n        const dataController = this._dataController;\r\n        const editRowKey = this.option(EDITING_EDITROWKEY_OPTION_NAME);\r\n        const rowIndex = dataController.getRowIndexByKey(editRowKey);\r\n        if (-1 === rowIndex) {\r\n            return rowIndex\r\n        }\r\n        return rowIndex + this._getEditRowIndexCorrection(columnName)\r\n    }\r\n    _getEditRowIndexCorrection(columnName) {\r\n        const editColumn = columnName ? this._getColumnByName(columnName) : this._getEditColumn();\r\n        const isColumnHidden = \"adaptiveHidden\" === (null === editColumn || void 0 === editColumn ? void 0 : editColumn.visibleWidth);\r\n        return isColumnHidden ? 1 : 0\r\n    }\r\n    _resetEditRowKey() {\r\n        this._refocusEditCell = false;\r\n        this._setEditRowKey(null, true)\r\n    }\r\n    _resetEditIndices() {\r\n        this._resetEditColumnName();\r\n        this._resetEditRowKey()\r\n    }\r\n    editRow(rowIndex) {\r\n        const dataController = this._dataController;\r\n        const items = dataController.items();\r\n        const item = items[rowIndex];\r\n        const params = {\r\n            data: item && item.data,\r\n            cancel: false\r\n        };\r\n        const oldRowIndex = this._getVisibleEditRowIndex();\r\n        if (!item) {\r\n            return\r\n        }\r\n        if (rowIndex === oldRowIndex) {\r\n            return true\r\n        }\r\n        if (void 0 === item.key) {\r\n            this._dataController.fireError(\"E1043\");\r\n            return\r\n        }\r\n        if (!item.isNewRow) {\r\n            params.key = item.key\r\n        }\r\n        if (this._isEditingStart(params)) {\r\n            return\r\n        }\r\n        this.resetChanges();\r\n        this.init();\r\n        this._resetEditColumnName();\r\n        this._pageIndex = dataController.pageIndex();\r\n        this._addInternalData({\r\n            key: item.key,\r\n            oldData: item.oldData ?? item.data\r\n        });\r\n        this._setEditRowKey(item.key)\r\n    }\r\n    _editRowFromOptionChanged(rowIndex, oldRowIndex) {\r\n        const rowIndices = [oldRowIndex, rowIndex];\r\n        this._beforeUpdateItems(rowIndices, rowIndex, oldRowIndex);\r\n        this._editRowFromOptionChangedCore(rowIndices, rowIndex)\r\n    }\r\n    _editRowFromOptionChangedCore(rowIndices, rowIndex, preventRendering) {\r\n        this._needFocusEditor = true;\r\n        this._dataController.updateItems({\r\n            changeType: \"update\",\r\n            rowIndices: rowIndices,\r\n            cancel: preventRendering\r\n        })\r\n    }\r\n    _focusEditorIfNeed() {}\r\n    _showEditPopup(rowIndex, repaintForm) {}\r\n    _repaintEditPopup() {}\r\n    _getEditPopupHiddenHandler() {\r\n        return e => {\r\n            if (this.isEditing()) {\r\n                this.cancelEditData()\r\n            }\r\n        }\r\n    }\r\n    _getPopupEditFormTemplate(rowIndex) {}\r\n    _getSaveButtonConfig() {\r\n        const buttonConfig = {\r\n            text: this.option(\"editing.texts.saveRowChanges\"),\r\n            onClick: this.saveEditData.bind(this)\r\n        };\r\n        if (isFluent(current())) {\r\n            buttonConfig.stylingMode = \"contained\";\r\n            buttonConfig.type = \"default\"\r\n        }\r\n        return buttonConfig\r\n    }\r\n    _getCancelButtonConfig() {\r\n        const buttonConfig = {\r\n            text: this.option(\"editing.texts.cancelRowChanges\"),\r\n            onClick: this.cancelEditData.bind(this)\r\n        };\r\n        if (isFluent(current())) {\r\n            buttonConfig.stylingMode = \"outlined\"\r\n        }\r\n        return buttonConfig\r\n    }\r\n    _removeInternalData(key) {\r\n        this._internalState.delete(getKeyHash(key))\r\n    }\r\n    _updateInsertAfterOrBeforeKeys(changes, index) {\r\n        const removeChange = changes[index];\r\n        changes.forEach((change => {\r\n            if (change.type === DATA_EDIT_DATA_INSERT_TYPE) {\r\n                const insertAfterOrBeforeKey = this._getInsertAfterOrBeforeKey(change);\r\n                if (equalByValue(insertAfterOrBeforeKey, removeChange.key)) {\r\n                    change[isDefined(change.insertAfterKey) ? \"insertAfterKey\" : \"insertBeforeKey\"] = this._getInsertAfterOrBeforeKey(removeChange)\r\n                }\r\n            }\r\n        }))\r\n    }\r\n    _removeChange(index) {\r\n        if (index >= 0) {\r\n            const changes = [...this.getChanges()];\r\n            const {\r\n                key: key\r\n            } = changes[index];\r\n            this._removeInternalData(key);\r\n            this._updateInsertAfterOrBeforeKeys(changes, index);\r\n            changes.splice(index, 1);\r\n            this._silentOption(EDITING_CHANGES_OPTION_NAME, changes);\r\n            if (equalByValue(this.option(EDITING_EDITROWKEY_OPTION_NAME), key)) {\r\n                this._resetEditIndices()\r\n            }\r\n        }\r\n    }\r\n    executeOperation(deferred, func) {\r\n        this._lastOperation && this._lastOperation.reject();\r\n        this._lastOperation = deferred;\r\n        this.waitForDeferredOperations().done((() => {\r\n            if (\"rejected\" === deferred.state()) {\r\n                return\r\n            }\r\n            func();\r\n            this._lastOperation = null\r\n        })).fail((() => {\r\n            deferred.reject();\r\n            this._lastOperation = null\r\n        }))\r\n    }\r\n    waitForDeferredOperations() {\r\n        return when(...this._deferreds)\r\n    }\r\n    _processCanceledEditingCell() {}\r\n    _repaintEditCell(column, oldColumn, oldEditRowIndex) {\r\n        if (!column || !column.showEditorAlways || oldColumn && !oldColumn.showEditorAlways) {\r\n            this._editCellInProgress = true;\r\n            this._needFocusEditor = true;\r\n            this._editorFactoryController.loseFocus();\r\n            this._dataController.updateItems({\r\n                changeType: \"update\",\r\n                rowIndices: [oldEditRowIndex, this._getVisibleEditRowIndex()]\r\n            })\r\n        } else if (column !== oldColumn) {\r\n            this._needFocusEditor = true;\r\n            this._dataController.updateItems({\r\n                changeType: \"update\",\r\n                rowIndices: []\r\n            })\r\n        }\r\n    }\r\n    _delayedInputFocus($cell, beforeFocusCallback, callBeforeFocusCallbackAlways) {\r\n        const inputFocus = () => {\r\n            if (beforeFocusCallback) {\r\n                beforeFocusCallback()\r\n            }\r\n            if ($cell) {\r\n                const $focusableElement = $cell.find(FOCUSABLE_ELEMENT_SELECTOR).first();\r\n                gridCoreUtils.focusAndSelectElement(this, $focusableElement)\r\n            }\r\n            this._beforeFocusCallback = null\r\n        };\r\n        if (devices.real().ios || devices.real().android) {\r\n            inputFocus()\r\n        } else {\r\n            if (this._beforeFocusCallback) {\r\n                this._beforeFocusCallback()\r\n            }\r\n            clearTimeout(this._inputFocusTimeoutID);\r\n            if (callBeforeFocusCallbackAlways) {\r\n                this._beforeFocusCallback = beforeFocusCallback\r\n            }\r\n            this._inputFocusTimeoutID = setTimeout(inputFocus)\r\n        }\r\n    }\r\n    _focusEditingCell(beforeFocusCallback, $editCell, callBeforeFocusCallbackAlways) {\r\n        const editColumnIndex = this._getVisibleEditColumnIndex();\r\n        $editCell = $editCell || this._rowsView && this._rowsView._getCellElement(this._getVisibleEditRowIndex(), editColumnIndex);\r\n        if ($editCell) {\r\n            this._delayedInputFocus($editCell, beforeFocusCallback, callBeforeFocusCallbackAlways)\r\n        }\r\n    }\r\n    deleteRow(rowIndex) {\r\n        this._checkAndDeleteRow(rowIndex)\r\n    }\r\n    _checkAndDeleteRow(rowIndex) {\r\n        const editingOptions = this.option(\"editing\");\r\n        const editingTexts = null === editingOptions || void 0 === editingOptions ? void 0 : editingOptions.texts;\r\n        const confirmDelete = null === editingOptions || void 0 === editingOptions ? void 0 : editingOptions.confirmDelete;\r\n        const confirmDeleteMessage = null === editingTexts || void 0 === editingTexts ? void 0 : editingTexts.confirmDeleteMessage;\r\n        const item = this._dataController.items()[rowIndex];\r\n        const allowDeleting = !this.isEditing() || item.isNewRow;\r\n        if (item && allowDeleting) {\r\n            if (!confirmDelete || !confirmDeleteMessage) {\r\n                this._deleteRowCore(rowIndex)\r\n            } else {\r\n                const confirmDeleteTitle = editingTexts && editingTexts.confirmDeleteTitle;\r\n                const showDialogTitle = isDefined(confirmDeleteTitle) && confirmDeleteTitle.length > 0;\r\n                confirm(confirmDeleteMessage, confirmDeleteTitle, showDialogTitle).done((confirmResult => {\r\n                    if (confirmResult) {\r\n                        this._deleteRowCore(rowIndex)\r\n                    }\r\n                }))\r\n            }\r\n        }\r\n    }\r\n    _deleteRowCore(rowIndex) {\r\n        const dataController = this._dataController;\r\n        const item = dataController.items()[rowIndex];\r\n        const key = item && item.key;\r\n        const oldEditRowIndex = this._getVisibleEditRowIndex();\r\n        this.refresh();\r\n        const changes = this.getChanges();\r\n        const editIndex = gridCoreUtils.getIndexByKey(key, changes);\r\n        if (editIndex >= 0) {\r\n            if (changes[editIndex].type === DATA_EDIT_DATA_INSERT_TYPE) {\r\n                this._removeChange(editIndex)\r\n            } else {\r\n                this._addChange({\r\n                    key: key,\r\n                    type: DATA_EDIT_DATA_REMOVE_TYPE\r\n                })\r\n            }\r\n        } else {\r\n            this._addChange({\r\n                key: key,\r\n                oldData: item.data,\r\n                type: DATA_EDIT_DATA_REMOVE_TYPE\r\n            })\r\n        }\r\n        return this._afterDeleteRow(rowIndex, oldEditRowIndex)\r\n    }\r\n    _afterDeleteRow(rowIndex, oldEditRowIndex) {\r\n        return this.saveEditData()\r\n    }\r\n    undeleteRow(rowIndex) {\r\n        const dataController = this._dataController;\r\n        const item = dataController.items()[rowIndex];\r\n        const oldEditRowIndex = this._getVisibleEditRowIndex();\r\n        const key = item && item.key;\r\n        const changes = this.getChanges();\r\n        if (item) {\r\n            const editIndex = gridCoreUtils.getIndexByKey(key, changes);\r\n            if (editIndex >= 0) {\r\n                const {\r\n                    data: data\r\n                } = changes[editIndex];\r\n                if (isEmptyObject(data)) {\r\n                    this._removeChange(editIndex)\r\n                } else {\r\n                    this._addChange({\r\n                        key: key,\r\n                        type: DATA_EDIT_DATA_UPDATE_TYPE\r\n                    })\r\n                }\r\n                dataController.updateItems({\r\n                    changeType: \"update\",\r\n                    rowIndices: [oldEditRowIndex, rowIndex]\r\n                })\r\n            }\r\n        }\r\n    }\r\n    _fireOnSaving() {\r\n        const onSavingParams = {\r\n            cancel: false,\r\n            promise: null,\r\n            changes: [...this.getChanges()]\r\n        };\r\n        this.executeAction(\"onSaving\", onSavingParams);\r\n        const d = new Deferred;\r\n        when(fromPromise(onSavingParams.promise)).done((() => {\r\n            d.resolve(onSavingParams)\r\n        })).fail((arg => {\r\n            createFailureHandler(d);\r\n            this._fireDataErrorOccurred(arg);\r\n            d.resolve({\r\n                cancel: true\r\n            })\r\n        }));\r\n        return d\r\n    }\r\n    _executeEditingAction(actionName, params, func) {\r\n        if (this.component._disposed) {\r\n            return null\r\n        }\r\n        const deferred = new Deferred;\r\n        this.executeAction(actionName, params);\r\n        when(fromPromise(params.cancel)).done((cancel => {\r\n            if (cancel) {\r\n                setTimeout((() => {\r\n                    deferred.resolve(\"cancel\")\r\n                }))\r\n            } else {\r\n                func(params).done(deferred.resolve).fail(createFailureHandler(deferred))\r\n            }\r\n        })).fail(createFailureHandler(deferred));\r\n        return deferred\r\n    }\r\n    _processChanges(deferreds, results, dataChanges, changes) {\r\n        const store = this._dataController.store();\r\n        each(changes, ((index, change) => {\r\n            const oldData = this._getOldData(change.key);\r\n            const {\r\n                data: data,\r\n                type: type\r\n            } = change;\r\n            const changeCopy = _extends({}, change);\r\n            let deferred;\r\n            let params;\r\n            if (this._beforeSaveEditData(change, index)) {\r\n                return\r\n            }\r\n            switch (type) {\r\n                case DATA_EDIT_DATA_REMOVE_TYPE:\r\n                    params = {\r\n                        data: oldData,\r\n                        key: change.key,\r\n                        cancel: false\r\n                    };\r\n                    deferred = this._executeEditingAction(\"onRowRemoving\", params, (() => store.remove(change.key).done((key => {\r\n                        dataChanges.push({\r\n                            type: \"remove\",\r\n                            key: key\r\n                        })\r\n                    }))));\r\n                    break;\r\n                case DATA_EDIT_DATA_INSERT_TYPE:\r\n                    params = {\r\n                        data: data,\r\n                        cancel: false\r\n                    };\r\n                    deferred = this._executeEditingAction(\"onRowInserting\", params, (() => store.insert(params.data).done(((data, key) => {\r\n                        if (isDefined(key)) {\r\n                            changeCopy.key = key\r\n                        }\r\n                        if (data && isObject(data) && data !== params.data) {\r\n                            changeCopy.data = data\r\n                        }\r\n                        dataChanges.push({\r\n                            type: \"insert\",\r\n                            data: data,\r\n                            index: 0\r\n                        })\r\n                    }))));\r\n                    break;\r\n                case DATA_EDIT_DATA_UPDATE_TYPE:\r\n                    params = {\r\n                        newData: data,\r\n                        oldData: oldData,\r\n                        key: change.key,\r\n                        cancel: false\r\n                    };\r\n                    deferred = this._executeEditingAction(\"onRowUpdating\", params, (() => store.update(change.key, params.newData).done(((data, key) => {\r\n                        if (data && isObject(data) && data !== params.newData) {\r\n                            changeCopy.data = data\r\n                        }\r\n                        dataChanges.push({\r\n                            type: \"update\",\r\n                            key: key,\r\n                            data: data\r\n                        })\r\n                    }))))\r\n            }\r\n            changes[index] = changeCopy;\r\n            if (deferred) {\r\n                const doneDeferred = new Deferred;\r\n                deferred.always((data => {\r\n                    results.push({\r\n                        key: change.key,\r\n                        result: data\r\n                    })\r\n                })).always(doneDeferred.resolve);\r\n                deferreds.push(doneDeferred.promise())\r\n            }\r\n        }))\r\n    }\r\n    _processRemoveIfError(changes, editIndex) {\r\n        const change = changes[editIndex];\r\n        if ((null === change || void 0 === change ? void 0 : change.type) === DATA_EDIT_DATA_REMOVE_TYPE) {\r\n            if (editIndex >= 0) {\r\n                changes.splice(editIndex, 1)\r\n            }\r\n        }\r\n        return true\r\n    }\r\n    _processRemove(changes, editIndex, cancel) {\r\n        const change = changes[editIndex];\r\n        if (!cancel || !change || change.type === DATA_EDIT_DATA_REMOVE_TYPE) {\r\n            return this._processRemoveCore(changes, editIndex, !cancel || !change)\r\n        }\r\n    }\r\n    _processRemoveCore(changes, editIndex, processIfBatch) {\r\n        if (editIndex >= 0) {\r\n            changes.splice(editIndex, 1)\r\n        }\r\n        return true\r\n    }\r\n    _processSaveEditDataResult(results) {\r\n        let hasSavedData = false;\r\n        const originalChanges = this.getChanges();\r\n        const changes = [...originalChanges];\r\n        const changesLength = changes.length;\r\n        for (let i = 0; i < results.length; i++) {\r\n            const arg = results[i].result;\r\n            const cancel = \"cancel\" === arg;\r\n            const editIndex = gridCoreUtils.getIndexByKey(results[i].key, changes);\r\n            const change = changes[editIndex];\r\n            const isError = arg && arg instanceof Error;\r\n            if (isError) {\r\n                if (change) {\r\n                    this._addInternalData({\r\n                        key: change.key,\r\n                        error: arg\r\n                    })\r\n                }\r\n                this._fireDataErrorOccurred(arg);\r\n                if (this._processRemoveIfError(changes, editIndex)) {\r\n                    break\r\n                }\r\n            } else if (this._processRemove(changes, editIndex, cancel)) {\r\n                hasSavedData = !cancel;\r\n                const removedChangeIndex = gridCoreUtils.getIndexByKey(results[i].key, originalChanges);\r\n                this._updateInsertAfterOrBeforeKeys(originalChanges, removedChangeIndex)\r\n            }\r\n        }\r\n        if (changes.length < changesLength) {\r\n            this._silentOption(EDITING_CHANGES_OPTION_NAME, changes)\r\n        }\r\n        return hasSavedData\r\n    }\r\n    _fireSaveEditDataEvents(changes) {\r\n        each(changes, ((_, _ref) => {\r\n            let {\r\n                data: data,\r\n                key: key,\r\n                type: type\r\n            } = _ref;\r\n            const internalData = this._addInternalData({\r\n                key: key\r\n            });\r\n            const params = {\r\n                key: key,\r\n                data: data\r\n            };\r\n            if (internalData.error) {\r\n                params.error = internalData.error\r\n            }\r\n            switch (type) {\r\n                case DATA_EDIT_DATA_REMOVE_TYPE:\r\n                    this.executeAction(\"onRowRemoved\", extend({}, params, {\r\n                        data: internalData.oldData\r\n                    }));\r\n                    break;\r\n                case DATA_EDIT_DATA_INSERT_TYPE:\r\n                    this.executeAction(\"onRowInserted\", params);\r\n                    break;\r\n                case DATA_EDIT_DATA_UPDATE_TYPE:\r\n                    this.executeAction(\"onRowUpdated\", params)\r\n            }\r\n        }));\r\n        this.executeAction(\"onSaved\", {\r\n            changes: changes\r\n        })\r\n    }\r\n    saveEditData() {\r\n        const deferred = new Deferred;\r\n        this.waitForDeferredOperations().done((() => {\r\n            if (this.isSaving()) {\r\n                this._resolveAfterSave(deferred);\r\n                return\r\n            }\r\n            when(this._beforeSaveEditData()).done((cancel => {\r\n                if (cancel) {\r\n                    this._resolveAfterSave(deferred, {\r\n                        cancel: cancel\r\n                    });\r\n                    return\r\n                }\r\n                this._saving = true;\r\n                this._saveEditDataInner().always((() => {\r\n                    this._saving = false;\r\n                    if (this._refocusEditCell) {\r\n                        this._focusEditingCell()\r\n                    }\r\n                })).done(deferred.resolve).fail(deferred.reject)\r\n            })).fail(deferred.reject)\r\n        })).fail(deferred.reject);\r\n        return deferred.promise()\r\n    }\r\n    _resolveAfterSave(deferred) {\r\n        let {\r\n            cancel: cancel,\r\n            error: error\r\n        } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};\r\n        when(this._afterSaveEditData(cancel)).done((() => {\r\n            deferred.resolve(error)\r\n        })).fail(deferred.reject)\r\n    }\r\n    _saveEditDataInner() {\r\n        const result = new Deferred;\r\n        const results = [];\r\n        const deferreds = [];\r\n        const dataChanges = [];\r\n        const dataSource = this._dataController.dataSource();\r\n        when(this._fireOnSaving()).done((_ref2 => {\r\n            let {\r\n                cancel: cancel,\r\n                changes: changes\r\n            } = _ref2;\r\n            if (cancel) {\r\n                return result.resolve().promise()\r\n            }\r\n            this._processChanges(deferreds, results, dataChanges, changes);\r\n            if (deferreds.length) {\r\n                this._refocusEditCell = true;\r\n                null === dataSource || void 0 === dataSource || dataSource.beginLoading();\r\n                when(...deferreds).done((() => {\r\n                    if (this._processSaveEditDataResult(results)) {\r\n                        this._endSaving(dataChanges, changes, result)\r\n                    } else {\r\n                        null === dataSource || void 0 === dataSource || dataSource.endLoading();\r\n                        result.resolve()\r\n                    }\r\n                })).fail((error => {\r\n                    null === dataSource || void 0 === dataSource || dataSource.endLoading();\r\n                    result.resolve(error)\r\n                }));\r\n                return result.always((() => {\r\n                    this._refocusEditCell = true\r\n                })).promise()\r\n            }\r\n            this._cancelSaving(result)\r\n        })).fail(result.reject);\r\n        return result.promise()\r\n    }\r\n    _beforeEndSaving(changes) {\r\n        this._resetEditIndices()\r\n    }\r\n    _endSaving(dataChanges, changes, deferred) {\r\n        const dataSource = this._dataController.dataSource();\r\n        this._beforeEndSaving(changes);\r\n        null === dataSource || void 0 === dataSource || dataSource.endLoading();\r\n        this._refreshDataAfterSave(dataChanges, changes, deferred)\r\n    }\r\n    _cancelSaving(result) {\r\n        this.executeAction(\"onSaved\", {\r\n            changes: []\r\n        });\r\n        this._resolveAfterSave(result)\r\n    }\r\n    _refreshDataAfterSave(dataChanges, changes, deferred) {\r\n        const dataController = this._dataController;\r\n        const refreshMode = this.option(\"editing.refreshMode\");\r\n        const isFullRefresh = \"reshape\" !== refreshMode && \"repaint\" !== refreshMode;\r\n        if (!isFullRefresh) {\r\n            dataController.push(dataChanges)\r\n        }\r\n        when(dataController.refresh({\r\n            selection: isFullRefresh,\r\n            reload: isFullRefresh,\r\n            load: \"reshape\" === refreshMode,\r\n            changesOnly: this.option(\"repaintChangesOnly\")\r\n        })).always((() => {\r\n            this._fireSaveEditDataEvents(changes)\r\n        })).done((() => {\r\n            this._resolveAfterSave(deferred)\r\n        })).fail((error => {\r\n            this._resolveAfterSave(deferred, {\r\n                error: error\r\n            })\r\n        }))\r\n    }\r\n    isSaving() {\r\n        return this._saving\r\n    }\r\n    _updateEditColumn() {\r\n        const isEditColumnVisible = this._isEditColumnVisible();\r\n        const useIcons = this.option(\"editing.useIcons\");\r\n        const cssClass = COMMAND_EDIT_CLASS + (useIcons ? ` ${COMMAND_EDIT_WITH_ICONS_CLASS}` : \"\");\r\n        this._columnsController.addCommandColumn({\r\n            type: \"buttons\",\r\n            command: \"edit\",\r\n            visible: isEditColumnVisible,\r\n            cssClass: cssClass,\r\n            width: \"auto\",\r\n            alignment: \"center\",\r\n            cellTemplate: this._getEditCommandCellTemplate(),\r\n            fixedPosition: \"right\"\r\n        });\r\n        this._columnsController.columnOption(\"command:edit\", {\r\n            visible: isEditColumnVisible,\r\n            cssClass: cssClass\r\n        })\r\n    }\r\n    _isEditColumnVisible() {\r\n        const editingOptions = this.option(\"editing\");\r\n        return editingOptions.allowDeleting\r\n    }\r\n    _isEditButtonDisabled() {\r\n        const hasChanges = this.hasChanges();\r\n        const isEditRowDefined = isDefined(this.option(\"editing.editRowKey\"));\r\n        return !(isEditRowDefined || hasChanges)\r\n    }\r\n    _updateEditButtons() {\r\n        const isButtonDisabled = this._isEditButtonDisabled();\r\n        if (this._headerPanelView) {\r\n            this._headerPanelView.setToolbarItemDisabled(\"saveButton\", isButtonDisabled);\r\n            this._headerPanelView.setToolbarItemDisabled(\"revertButton\", isButtonDisabled)\r\n        }\r\n    }\r\n    _applyModified($element, options) {\r\n        $element && $element.addClass(CELL_MODIFIED)\r\n    }\r\n    _beforeCloseEditCellInBatchMode(rowIndices) {}\r\n    cancelEditData() {\r\n        const changes = this.getChanges();\r\n        const params = {\r\n            cancel: false,\r\n            changes: changes\r\n        };\r\n        this.executeAction(\"onEditCanceling\", params);\r\n        if (!params.cancel) {\r\n            this._cancelEditDataCore();\r\n            this.executeAction(\"onEditCanceled\", {\r\n                changes: changes\r\n            })\r\n        }\r\n    }\r\n    _cancelEditDataCore() {\r\n        const rowIndex = this._getVisibleEditRowIndex();\r\n        this._beforeCancelEditData();\r\n        this.init();\r\n        this.resetChanges();\r\n        this._resetEditColumnName();\r\n        this._resetEditRowKey();\r\n        this._afterCancelEditData(rowIndex)\r\n    }\r\n    _afterCancelEditData(rowIndex) {\r\n        const dataController = this._dataController;\r\n        dataController.updateItems({\r\n            repaintChangesOnly: this.option(\"repaintChangesOnly\")\r\n        })\r\n    }\r\n    _hideEditPopup() {}\r\n    hasEditData() {\r\n        return this.hasChanges()\r\n    }\r\n    update(changeType) {\r\n        const dataController = this._dataController;\r\n        if (dataController && this._pageIndex !== dataController.pageIndex()) {\r\n            if (\"refresh\" === changeType) {\r\n                this.refresh({\r\n                    isPageChanged: true\r\n                })\r\n            }\r\n            this._pageIndex = dataController.pageIndex()\r\n        }\r\n        this._updateEditButtons()\r\n    }\r\n    _getRowIndicesForCascadeUpdating(row, skipCurrentRow) {\r\n        return skipCurrentRow ? [] : [row.rowIndex]\r\n    }\r\n    addDeferred(deferred) {\r\n        if (!this._deferreds.includes(deferred)) {\r\n            this._deferreds.push(deferred);\r\n            deferred.always((() => {\r\n                const index = this._deferreds.indexOf(deferred);\r\n                if (index >= 0) {\r\n                    this._deferreds.splice(index, 1)\r\n                }\r\n            }))\r\n        }\r\n    }\r\n    _prepareChange(options, value, text) {\r\n        var _options$row;\r\n        const newData = {};\r\n        const oldData = null === (_options$row = options.row) || void 0 === _options$row ? void 0 : _options$row.data;\r\n        const rowKey = options.key;\r\n        const deferred = new Deferred;\r\n        if (void 0 !== rowKey) {\r\n            options.value = value;\r\n            const setCellValueResult = fromPromise(options.column.setCellValue(newData, value, extend(true, {}, oldData), text));\r\n            setCellValueResult.done((() => {\r\n                deferred.resolve({\r\n                    data: newData,\r\n                    key: rowKey,\r\n                    oldData: oldData,\r\n                    type: DATA_EDIT_DATA_UPDATE_TYPE\r\n                })\r\n            })).fail(createFailureHandler(deferred)).fail((arg => this._fireDataErrorOccurred(arg)));\r\n            if (isDefined(text) && options.column.displayValueMap) {\r\n                options.column.displayValueMap[value] = text\r\n            }\r\n            this._updateRowValues(options);\r\n            this.addDeferred(deferred)\r\n        }\r\n        return deferred\r\n    }\r\n    _updateRowValues(options) {\r\n        if (options.values) {\r\n            const dataController = this._dataController;\r\n            const rowIndex = dataController.getRowIndexByKey(options.key);\r\n            const row = dataController.getVisibleRows()[rowIndex];\r\n            if (row) {\r\n                options.row.values = row.values;\r\n                options.values = row.values\r\n            }\r\n            options.values[options.columnIndex] = options.value\r\n        }\r\n    }\r\n    updateFieldValue(options, value, text, forceUpdateRow) {\r\n        const rowKey = options.key;\r\n        const deferred = new Deferred;\r\n        if (void 0 === rowKey) {\r\n            this._dataController.fireError(\"E1043\")\r\n        }\r\n        if (options.column.setCellValue) {\r\n            this._prepareChange(options, value, text).done((params => {\r\n                when(this._applyChange(options, params, forceUpdateRow)).always((() => {\r\n                    deferred.resolve()\r\n                }))\r\n            }))\r\n        } else {\r\n            deferred.resolve()\r\n        }\r\n        return deferred.promise()\r\n    }\r\n    _focusPreviousEditingCellIfNeed(options) {\r\n        if (this.hasEditData() && !this.isEditCell(options.rowIndex, options.columnIndex)) {\r\n            this._focusEditingCell();\r\n            this._updateEditRow(options.row, true);\r\n            return true\r\n        }\r\n    }\r\n    _needUpdateRow(column) {\r\n        const visibleColumns = this._columnsController.getVisibleColumns();\r\n        if (!column) {\r\n            column = this._getEditColumn()\r\n        }\r\n        const isCustomSetCellValue = column && column.setCellValue !== column.defaultSetCellValue;\r\n        const isCustomCalculateCellValue = visibleColumns.some((visibleColumn => visibleColumn.calculateCellValue !== visibleColumn.defaultCalculateCellValue));\r\n        return isCustomSetCellValue || isCustomCalculateCellValue\r\n    }\r\n    _applyChange(options, params, forceUpdateRow) {\r\n        const changeOptions = _extends({}, options, {\r\n            forceUpdateRow: forceUpdateRow\r\n        });\r\n        this._addChange(params, changeOptions);\r\n        this._updateEditButtons();\r\n        return this._applyChangeCore(options, changeOptions.forceUpdateRow)\r\n    }\r\n    _applyChangeCore(options, forceUpdateRow) {\r\n        const isCustomSetCellValue = options.column.setCellValue !== options.column.defaultSetCellValue;\r\n        const {\r\n            row: row\r\n        } = options;\r\n        if (row) {\r\n            if (forceUpdateRow || isCustomSetCellValue) {\r\n                this._updateEditRow(row, forceUpdateRow, isCustomSetCellValue)\r\n            } else if (row.update) {\r\n                row.update()\r\n            }\r\n        }\r\n    }\r\n    _updateEditRowCore(row, skipCurrentRow, isCustomSetCellValue) {\r\n        this._dataController.updateItems({\r\n            changeType: \"update\",\r\n            rowIndices: this._getRowIndicesForCascadeUpdating(row, skipCurrentRow)\r\n        })\r\n    }\r\n    _updateEditRow(row, forceUpdateRow, isCustomSetCellValue) {\r\n        if (forceUpdateRow) {\r\n            this._updateRowImmediately(row, forceUpdateRow, isCustomSetCellValue)\r\n        } else {\r\n            this._updateRowWithDelay(row, isCustomSetCellValue)\r\n        }\r\n    }\r\n    _updateRowImmediately(row, forceUpdateRow, isCustomSetCellValue) {\r\n        this._updateEditRowCore(row, !forceUpdateRow, isCustomSetCellValue);\r\n        this._validateEditFormAfterUpdate(row, isCustomSetCellValue);\r\n        if (!forceUpdateRow) {\r\n            this._focusEditingCell()\r\n        }\r\n    }\r\n    _updateRowWithDelay(row, isCustomSetCellValue) {\r\n        const deferred = new Deferred;\r\n        this.addDeferred(deferred);\r\n        setTimeout((() => {\r\n            var _this$_editForm;\r\n            const elementContainer = (null === (_this$_editForm = this._editForm) || void 0 === _this$_editForm ? void 0 : _this$_editForm.element()) || this.component.$element().get(0);\r\n            const $focusedElement = $(domAdapter.getActiveElement(elementContainer));\r\n            const columnIndex = this._rowsView.getCellIndex($focusedElement, row.rowIndex);\r\n            let focusedElement = $focusedElement.get(0);\r\n            const selectionRange = gridCoreUtils.getSelectionRange(focusedElement);\r\n            this._updateEditRowCore(row, false, isCustomSetCellValue);\r\n            this._validateEditFormAfterUpdate(row, isCustomSetCellValue);\r\n            if (columnIndex >= 0) {\r\n                const $focusedItem = this._rowsView._getCellElement(row.rowIndex, columnIndex);\r\n                this._delayedInputFocus($focusedItem, (() => {\r\n                    setTimeout((() => {\r\n                        var _this$component$$elem;\r\n                        focusedElement = domAdapter.getActiveElement(null === (_this$component$$elem = this.component.$element()) || void 0 === _this$component$$elem ? void 0 : _this$component$$elem.get(0));\r\n                        if (selectionRange.selectionStart >= 0) {\r\n                            gridCoreUtils.setSelectionRange(focusedElement, selectionRange)\r\n                        }\r\n                    }))\r\n                }))\r\n            }\r\n            deferred.resolve()\r\n        }))\r\n    }\r\n    _validateEditFormAfterUpdate() {}\r\n    _addChange(changeParams, options) {\r\n        var _this$getChanges;\r\n        const row = null === options || void 0 === options ? void 0 : options.row;\r\n        const changes = [...this.getChanges()];\r\n        let index = gridCoreUtils.getIndexByKey(changeParams.key, changes);\r\n        if (index < 0) {\r\n            index = changes.length;\r\n            this._addInternalData({\r\n                key: changeParams.key,\r\n                oldData: changeParams.oldData\r\n            });\r\n            delete changeParams.oldData;\r\n            changes.push(changeParams)\r\n        }\r\n        const change = _extends({}, changes[index]);\r\n        if (change) {\r\n            if (changeParams.data) {\r\n                change.data = createObjectWithChanges(change.data, changeParams.data)\r\n            }\r\n            if ((!change.type || !changeParams.data) && changeParams.type) {\r\n                change.type = changeParams.type\r\n            }\r\n            if (row) {\r\n                row.oldData = this._getOldData(row.key);\r\n                row.data = createObjectWithChanges(row.data, changeParams.data)\r\n            }\r\n        }\r\n        changes[index] = change;\r\n        this._silentOption(EDITING_CHANGES_OPTION_NAME, changes);\r\n        if (options && change !== (null === (_this$getChanges = this.getChanges()) || void 0 === _this$getChanges ? void 0 : _this$getChanges[index])) {\r\n            options.forceUpdateRow = true\r\n        }\r\n        return change\r\n    }\r\n    _getFormEditItemTemplate(cellOptions, column) {\r\n        return column.editCellTemplate || this._getDefaultEditorTemplate()\r\n    }\r\n    getColumnTemplate(options) {\r\n        const {\r\n            column: column\r\n        } = options;\r\n        const rowIndex = options.row && options.row.rowIndex;\r\n        let template;\r\n        const isRowMode = this.isRowBasedEditMode();\r\n        const isRowEditing = this.isEditRow(rowIndex);\r\n        const isCellEditing = this.isEditCell(rowIndex, options.columnIndex);\r\n        let editingStartOptions;\r\n        if ((column.showEditorAlways || column.setCellValue && (isRowEditing && column.allowEditing || isCellEditing)) && (\"data\" === options.rowType || \"detailAdaptive\" === options.rowType) && !column.command) {\r\n            const allowUpdating = this.allowUpdating(options);\r\n            if (((allowUpdating || isRowEditing) && column.allowEditing || isCellEditing) && (isRowEditing || !isRowMode)) {\r\n                if (column.showEditorAlways && !isRowMode) {\r\n                    editingStartOptions = {\r\n                        cancel: false,\r\n                        key: options.row.isNewRow ? void 0 : options.row.key,\r\n                        data: options.row.data,\r\n                        column: column\r\n                    };\r\n                    this._isEditingStart(editingStartOptions)\r\n                }\r\n                if (!editingStartOptions || !editingStartOptions.cancel) {\r\n                    options.setValue = (value, text) => {\r\n                        this.updateFieldValue(options, value, text)\r\n                    }\r\n                }\r\n            }\r\n            template = column.editCellTemplate || this._getDefaultEditorTemplate()\r\n        } else if (\"detail\" === column.command && \"detail\" === options.rowType && isRowEditing) {\r\n            template = null === this || void 0 === this ? void 0 : this.getEditFormTemplate(options)\r\n        }\r\n        return template\r\n    }\r\n    _createButton($container, button, options, change) {\r\n        let icon = EDIT_ICON_CLASS[button.name];\r\n        const useIcons = this.option(\"editing.useIcons\");\r\n        const useLegacyColumnButtonTemplate = this.option(\"useLegacyColumnButtonTemplate\");\r\n        let $button = $(\"<a>\").attr(\"href\", \"#\").addClass(LINK_CLASS).addClass(button.cssClass);\r\n        if (button.template && useLegacyColumnButtonTemplate) {\r\n            this._rowsView.renderTemplate($container, button.template, options, true)\r\n        } else {\r\n            if (button.template) {\r\n                $button = $(\"<span>\").addClass(button.cssClass)\r\n            } else if (useIcons && icon || button.icon) {\r\n                icon = button.icon || icon;\r\n                const iconType = iconUtils.getImageSourceType(icon);\r\n                if (\"image\" === iconType || \"svg\" === iconType) {\r\n                    $button = iconUtils.getImageContainer(icon).addClass(button.cssClass)\r\n                } else {\r\n                    $button.addClass(`dx-icon${\"dxIcon\"===iconType?\"-\":\" \"}${icon}`).attr(\"title\", button.text)\r\n                }\r\n                $button.addClass(LINK_ICON_CLASS);\r\n                $container.addClass(COMMAND_EDIT_WITH_ICONS_CLASS);\r\n                const localizationName = this.getButtonLocalizationNames()[button.name];\r\n                localizationName && $button.attr(\"aria-label\", messageLocalization.format(localizationName))\r\n            } else {\r\n                $button.text(button.text)\r\n            }\r\n            if (isDefined(button.hint)) {\r\n                $button.attr(\"title\", button.hint)\r\n            }\r\n            if (this._isButtonDisabled(button, options)) {\r\n                $button.addClass(\"dx-state-disabled\")\r\n            } else if (!button.template || button.onClick) {\r\n                eventsEngine.on($button, addNamespace(\"click\", EDITING_NAMESPACE), this.createAction((e => {\r\n                    var _button$onClick;\r\n                    null === (_button$onClick = button.onClick) || void 0 === _button$onClick || _button$onClick.call(button, extend({}, e, {\r\n                        row: options.row,\r\n                        column: options.column\r\n                    }));\r\n                    e.event.preventDefault();\r\n                    e.event.stopPropagation()\r\n                })))\r\n            }\r\n            $container.append($button);\r\n            if (button.template) {\r\n                options.renderAsync = false;\r\n                this._rowsView.renderTemplate($button, button.template, options, true, change)\r\n            }\r\n        }\r\n    }\r\n    getButtonLocalizationNames() {\r\n        return {\r\n            edit: \"dxDataGrid-editingEditRow\",\r\n            save: \"dxDataGrid-editingSaveRowChanges\",\r\n            delete: \"dxDataGrid-editingDeleteRow\",\r\n            undelete: \"dxDataGrid-editingUndeleteRow\",\r\n            cancel: \"dxDataGrid-editingCancelRowChanges\"\r\n        }\r\n    }\r\n    prepareButtonItem(headerPanel, name, methodName, sortIndex) {\r\n        const editingTexts = this.option(\"editing.texts\") ?? {};\r\n        const titleButtonTextByClassNames = {\r\n            revert: editingTexts.cancelAllChanges,\r\n            save: editingTexts.saveAllChanges,\r\n            addRow: editingTexts.addRow\r\n        };\r\n        const className = {\r\n            revert: \"cancel\",\r\n            save: \"save\",\r\n            addRow: \"addrow\"\r\n        } [name];\r\n        const hintText = titleButtonTextByClassNames[name];\r\n        const isButtonDisabled = (\"save\" === className || \"cancel\" === className) && this._isEditButtonDisabled();\r\n        return {\r\n            widget: \"dxButton\",\r\n            options: {\r\n                onInitialized: e => {\r\n                    $(e.element).addClass(headerPanel._getToolbarButtonClass(`${EDIT_BUTTON_CLASS} ${this.addWidgetPrefix(className)}-button`))\r\n                },\r\n                icon: `edit-button-${className}`,\r\n                disabled: isButtonDisabled,\r\n                onClick: () => {\r\n                    setTimeout((() => {\r\n                        this[methodName]()\r\n                    }))\r\n                },\r\n                text: hintText,\r\n                hint: hintText\r\n            },\r\n            showText: \"inMenu\",\r\n            name: `${name}Button`,\r\n            location: \"after\",\r\n            locateInMenu: \"auto\",\r\n            sortIndex: sortIndex\r\n        }\r\n    }\r\n    prepareEditButtons(headerPanel) {\r\n        const editingOptions = this.option(\"editing\") ?? {};\r\n        const buttonItems = [];\r\n        if (editingOptions.allowAdding) {\r\n            buttonItems.push(this.prepareButtonItem(headerPanel, \"addRow\", \"addRow\", 20))\r\n        }\r\n        return buttonItems\r\n    }\r\n    highlightDataCell($cell, params) {\r\n        this.shouldHighlightCell(params) && $cell.addClass(CELL_MODIFIED)\r\n    }\r\n    _afterInsertRow(key) {}\r\n    _beforeSaveEditData(change) {\r\n        if (change && !isDefined(change.key) && isDefined(change.type)) {\r\n            return true\r\n        }\r\n    }\r\n    _afterSaveEditData() {}\r\n    _beforeCancelEditData() {}\r\n    _allowEditAction(actionName, options) {\r\n        let allowEditAction = this.option(`editing.${actionName}`);\r\n        if (isFunction(allowEditAction)) {\r\n            allowEditAction = allowEditAction({\r\n                component: this.component,\r\n                row: options.row\r\n            })\r\n        }\r\n        return allowEditAction\r\n    }\r\n    allowUpdating(options, eventName) {\r\n        const startEditAction = this.option(\"editing.startEditAction\") ?? DEFAULT_START_EDIT_ACTION;\r\n        const needCallback = arguments.length > 1 ? startEditAction === eventName || \"down\" === eventName : true;\r\n        return needCallback && this._allowEditAction(\"allowUpdating\", options)\r\n    }\r\n    allowDeleting(options) {\r\n        return this._allowEditAction(\"allowDeleting\", options)\r\n    }\r\n    isCellModified(parameters) {\r\n        var _parameters$row, _parameters$row2;\r\n        const {\r\n            columnIndex: columnIndex\r\n        } = parameters;\r\n        let modifiedValue = null === parameters || void 0 === parameters || null === (_parameters$row = parameters.row) || void 0 === _parameters$row || null === (_parameters$row = _parameters$row.modifiedValues) || void 0 === _parameters$row ? void 0 : _parameters$row[columnIndex];\r\n        if (null !== parameters && void 0 !== parameters && null !== (_parameters$row2 = parameters.row) && void 0 !== _parameters$row2 && _parameters$row2.isNewRow) {\r\n            modifiedValue = parameters.value\r\n        }\r\n        return void 0 !== modifiedValue\r\n    }\r\n    isNewRowInEditMode() {\r\n        const visibleEditRowIndex = this._getVisibleEditRowIndex();\r\n        const rows = this._dataController.items();\r\n        return visibleEditRowIndex >= 0 ? rows[visibleEditRowIndex].isNewRow : false\r\n    }\r\n    _isRowDeleteAllowed() {}\r\n    shouldHighlightCell(parameters) {\r\n        const cellModified = this.isCellModified(parameters);\r\n        return cellModified && parameters.column.setCellValue && (this.getEditMode() !== EDIT_MODE_ROW || !parameters.row.isEditing)\r\n    }\r\n}\r\nexport const dataControllerEditingExtenderMixin = Base => class extends Base {\r\n    reload(full, repaintChangesOnly) {\r\n        !repaintChangesOnly && this._editingController.refresh();\r\n        return super.reload.apply(this, arguments)\r\n    }\r\n    repaintRows() {\r\n        if (this._editingController.isSaving()) {\r\n            return\r\n        }\r\n        return super.repaintRows.apply(this, arguments)\r\n    }\r\n    _updateEditRow(items) {\r\n        const editRowKey = this.option(EDITING_EDITROWKEY_OPTION_NAME);\r\n        const editRowIndex = gridCoreUtils.getIndexByKey(editRowKey, items);\r\n        const editItem = items[editRowIndex];\r\n        if (editItem) {\r\n            var _this$_updateEditItem;\r\n            editItem.isEditing = true;\r\n            null === (_this$_updateEditItem = this._updateEditItem) || void 0 === _this$_updateEditItem || _this$_updateEditItem.call(this, editItem)\r\n        }\r\n    }\r\n    _updateItemsCore(change) {\r\n        super._updateItemsCore(change);\r\n        this._updateEditRow(this.items(true))\r\n    }\r\n    _applyChangeUpdate(change) {\r\n        this._updateEditRow(change.items);\r\n        super._applyChangeUpdate(change)\r\n    }\r\n    _applyChangesOnly(change) {\r\n        this._updateEditRow(change.items);\r\n        super._applyChangesOnly(change)\r\n    }\r\n    _processItems(items, change) {\r\n        items = this._editingController.processItems(items, change);\r\n        return super._processItems(items, change)\r\n    }\r\n    _processDataItem(dataItem, options) {\r\n        this._editingController.processDataItem(dataItem, options, this.generateDataValues);\r\n        return super._processDataItem(dataItem, options)\r\n    }\r\n    _processItem(item, options) {\r\n        item = super._processItem(item, options);\r\n        if (item.isNewRow) {\r\n            options.dataIndex--;\r\n            delete item.dataIndex\r\n        }\r\n        return item\r\n    }\r\n    _getChangedColumnIndices(oldItem, newItem, rowIndex, isLiveUpdate) {\r\n        if (oldItem.isNewRow !== newItem.isNewRow || oldItem.removed !== newItem.removed) {\r\n            return\r\n        }\r\n        return super._getChangedColumnIndices.apply(this, arguments)\r\n    }\r\n    _isCellChanged(oldRow, newRow, visibleRowIndex, columnIndex, isLiveUpdate) {\r\n        const cell = oldRow.cells && oldRow.cells[columnIndex];\r\n        const isEditing = this._editingController && this._editingController.isEditCell(visibleRowIndex, columnIndex);\r\n        if (isLiveUpdate && isEditing) {\r\n            return false\r\n        }\r\n        if (cell && cell.column && !cell.column.showEditorAlways && cell.isEditing !== isEditing) {\r\n            return true\r\n        }\r\n        return super._isCellChanged.apply(this, arguments)\r\n    }\r\n    needToRefreshOnDataSourceChange(args) {\r\n        const isParasiteChange = Array.isArray(args.value) && args.value === args.previousValue && this._editingController.isSaving();\r\n        return !isParasiteChange\r\n    }\r\n    _handleDataSourceChange(args) {\r\n        const result = super._handleDataSourceChange(args);\r\n        const changes = this.option(\"editing.changes\");\r\n        const dataSource = args.value;\r\n        if (Array.isArray(dataSource) && changes.length) {\r\n            const dataSourceKeys = dataSource.map((item => this.keyOf(item)));\r\n            const newChanges = changes.filter((change => \"insert\" === change.type || dataSourceKeys.some((key => equalByValue(change.key, key)))));\r\n            if (newChanges.length !== changes.length) {\r\n                this.option(\"editing.changes\", newChanges)\r\n            }\r\n            const editRowKey = this.option(\"editing.editRowKey\");\r\n            const isEditNewItem = newChanges.some((change => \"insert\" === change.type && equalByValue(editRowKey, change.key)));\r\n            if (!isEditNewItem && dataSourceKeys.every((key => !equalByValue(editRowKey, key)))) {\r\n                this.option(\"editing.editRowKey\", null)\r\n            }\r\n        }\r\n        return result\r\n    }\r\n};\r\nconst rowsView = Base => class extends Base {\r\n    getCellIndex($cell, rowIndex) {\r\n        if (!$cell.is(\"td\") && rowIndex >= 0) {\r\n            const $cellElements = this.getCellElements(rowIndex);\r\n            let cellIndex = -1;\r\n            each($cellElements, ((index, cellElement) => {\r\n                if ($(cellElement).find($cell).length) {\r\n                    cellIndex = index\r\n                }\r\n            }));\r\n            return cellIndex\r\n        }\r\n        return super.getCellIndex.apply(this, arguments)\r\n    }\r\n    publicMethods() {\r\n        return super.publicMethods().concat([\"cellValue\"])\r\n    }\r\n    _getCellTemplate(options) {\r\n        const template = this._editingController.getColumnTemplate(options);\r\n        return template || super._getCellTemplate(options)\r\n    }\r\n    _createRow(row) {\r\n        const $row = super._createRow.apply(this, arguments);\r\n        if (row) {\r\n            const isRowRemoved = !!row.removed;\r\n            const isRowInserted = !!row.isNewRow;\r\n            const isRowModified = !!row.modified;\r\n            isRowInserted && $row.addClass(ROW_INSERTED);\r\n            isRowModified && $row.addClass(ROW_MODIFIED);\r\n            if (isRowInserted || isRowRemoved) {\r\n                $row.removeClass(ROW_SELECTED)\r\n            }\r\n        }\r\n        return $row\r\n    }\r\n    _getColumnIndexByElement($element) {\r\n        let $tableElement = $element.closest(\"table\");\r\n        const $tableElements = this.getTableElements();\r\n        while ($tableElement.length && !$tableElements.filter($tableElement).length) {\r\n            $element = $tableElement.closest(\"td\");\r\n            $tableElement = $element.closest(\"table\")\r\n        }\r\n        return this._getColumnIndexByElementCore($element)\r\n    }\r\n    _getColumnIndexByElementCore($element) {\r\n        const $targetElement = $element.closest(`.${ROW_CLASS}> td:not(.dx-master-detail-cell)`);\r\n        return this.getCellIndex($targetElement)\r\n    }\r\n    _editCellByClick(e, eventName) {\r\n        const editingController = this._editingController;\r\n        const $targetElement = $(e.event.target);\r\n        const columnIndex = this._getColumnIndexByElement($targetElement);\r\n        const row = this._dataController.items()[e.rowIndex];\r\n        const allowUpdating = editingController.allowUpdating({\r\n            row: row\r\n        }, eventName) || row && row.isNewRow;\r\n        const column = this._columnsController.getVisibleColumns()[columnIndex];\r\n        const isEditedCell = editingController.isEditCell(e.rowIndex, columnIndex);\r\n        const allowEditing = allowUpdating && column && (column.allowEditing || isEditedCell);\r\n        const startEditAction = this.option(\"editing.startEditAction\") || \"click\";\r\n        const isShowEditorAlways = column && column.showEditorAlways;\r\n        if (isEditedCell) {\r\n            return true\r\n        }\r\n        if (\"down\" === eventName) {\r\n            if (devices.real().ios || devices.real().android) {\r\n                domUtils.resetActiveElement()\r\n            }\r\n            return isShowEditorAlways && allowEditing && editingController.editCell(e.rowIndex, columnIndex)\r\n        }\r\n        if (\"click\" === eventName && \"dblClick\" === startEditAction && this._pointerDownTarget === $targetElement.get(0)) {\r\n            const isError = false;\r\n            const withoutSaveEditData = null === row || void 0 === row ? void 0 : row.isNewRow;\r\n            editingController.closeEditCell(isError, withoutSaveEditData)\r\n        }\r\n        if (allowEditing && eventName === startEditAction) {\r\n            return editingController.editCell(e.rowIndex, columnIndex) || editingController.isEditRow(e.rowIndex)\r\n        }\r\n    }\r\n    _rowPointerDown(e) {\r\n        this._pointerDownTarget = e.event.target;\r\n        this._pointerDownTimeout = setTimeout((() => {\r\n            this._editCellByClick(e, \"down\")\r\n        }))\r\n    }\r\n    _rowClickTreeListHack(e) {\r\n        super._rowClick.apply(this, arguments)\r\n    }\r\n    _rowClick(e) {\r\n        const isEditForm = $(e.rowElement).hasClass(this.addWidgetPrefix(EDIT_FORM_CLASS));\r\n        e.event[TARGET_COMPONENT_NAME] = this.component;\r\n        if (!this._editCellByClick(e, \"click\") && !isEditForm) {\r\n            super._rowClick.apply(this, arguments)\r\n        }\r\n    }\r\n    _rowDblClickTreeListHack(e) {\r\n        super._rowDblClick.apply(this, arguments)\r\n    }\r\n    _rowDblClick(e) {\r\n        if (!this._editCellByClick(e, \"dblClick\")) {\r\n            super._rowDblClick.apply(this, arguments)\r\n        }\r\n    }\r\n    _cellPrepared($cell, parameters) {\r\n        var _parameters$column;\r\n        const editingController = this._editingController;\r\n        const isCommandCell = !!parameters.column.command;\r\n        const isEditableCell = parameters.setValue;\r\n        const isEditRow = editingController.isEditRow(parameters.rowIndex);\r\n        const isEditing = isEditingCell(isEditRow, parameters);\r\n        if (isEditingOrShowEditorAlwaysDataCell(isEditRow, parameters)) {\r\n            const {\r\n                alignment: alignment\r\n            } = parameters.column;\r\n            $cell.toggleClass(this.addWidgetPrefix(READONLY_CLASS), !isEditableCell).toggleClass(CELL_FOCUS_DISABLED_CLASS, !isEditableCell);\r\n            if (alignment) {\r\n                $cell.find(EDITORS_INPUT_SELECTOR).first().css(\"textAlign\", alignment)\r\n            }\r\n        }\r\n        if (isEditing) {\r\n            this._editCellPrepared($cell)\r\n        }\r\n        const hasTemplate = !!(null !== (_parameters$column = parameters.column) && void 0 !== _parameters$column && _parameters$column.cellTemplate);\r\n        if (parameters.column && !isCommandCell && (!hasTemplate || editingController.shouldHighlightCell(parameters))) {\r\n            editingController.highlightDataCell($cell, parameters)\r\n        }\r\n        super._cellPrepared.apply(this, arguments)\r\n    }\r\n    _getCellOptions(options) {\r\n        const cellOptions = super._getCellOptions(options);\r\n        const {\r\n            columnIndex: columnIndex,\r\n            row: row\r\n        } = options;\r\n        cellOptions.isEditing = this._editingController.isEditCell(cellOptions.rowIndex, cellOptions.columnIndex);\r\n        cellOptions.removed = row.removed;\r\n        if (row.modified) {\r\n            cellOptions.modified = void 0 !== row.modifiedValues[columnIndex]\r\n        }\r\n        return cellOptions\r\n    }\r\n    _setCellAriaAttributes($cell, cellOptions, options) {\r\n        super._setCellAriaAttributes($cell, cellOptions, options);\r\n        if (cellOptions.removed) {\r\n            this.setAria(\"roledescription\", messageLocalization.format(\"dxDataGrid-ariaDeletedCell\"), $cell)\r\n        }\r\n        if (cellOptions.modified) {\r\n            this.setAria(\"roledescription\", messageLocalization.format(\"dxDataGrid-ariaModifiedCell\"), $cell)\r\n        }\r\n        const isEditableCell = cellOptions.column.allowEditing && !cellOptions.removed && !cellOptions.modified && \"data\" === cellOptions.rowType && cellOptions.column.calculateCellValue === cellOptions.column.defaultCalculateCellValue && this._editingController.isCellBasedEditMode();\r\n        if (isEditableCell) {\r\n            this.setAria(\"roledescription\", messageLocalization.format(\"dxDataGrid-ariaEditableCell\"), $cell)\r\n        }\r\n    }\r\n    _createCell(options) {\r\n        const $cell = super._createCell(options);\r\n        const isEditRow = this._editingController.isEditRow(options.rowIndex);\r\n        isEditingOrShowEditorAlwaysDataCell(isEditRow, options) && $cell.addClass(EDITOR_CELL_CLASS);\r\n        return $cell\r\n    }\r\n    cellValue(rowIndex, columnIdentifier, value, text) {\r\n        const cellOptions = this.getCellOptions(rowIndex, columnIdentifier);\r\n        if (cellOptions) {\r\n            if (void 0 === value) {\r\n                return cellOptions.value\r\n            }\r\n            this._editingController.updateFieldValue(cellOptions, value, text, true)\r\n        }\r\n    }\r\n    dispose() {\r\n        super.dispose.apply(this, arguments);\r\n        clearTimeout(this._pointerDownTimeout)\r\n    }\r\n    _renderCore() {\r\n        super._renderCore.apply(this, arguments);\r\n        return this.waitAsyncTemplates(true).done((() => {\r\n            this._editingController._focusEditorIfNeed()\r\n        }))\r\n    }\r\n    _editCellPrepared() {}\r\n    _formItemPrepared() {}\r\n};\r\nconst headerPanel = Base => class extends Base {\r\n    optionChanged(args) {\r\n        const {\r\n            fullName: fullName\r\n        } = args;\r\n        switch (args.name) {\r\n            case \"editing\": {\r\n                const excludedOptions = [EDITING_POPUP_OPTION_NAME, EDITING_CHANGES_OPTION_NAME, EDITING_EDITCOLUMNNAME_OPTION_NAME, EDITING_EDITROWKEY_OPTION_NAME];\r\n                const shouldInvalidate = fullName && !excludedOptions.some((optionName => optionName === fullName));\r\n                shouldInvalidate && this._invalidate();\r\n                super.optionChanged(args);\r\n                break\r\n            }\r\n            case \"useLegacyColumnButtonTemplate\":\r\n                args.handled = true;\r\n                break;\r\n            default:\r\n                super.optionChanged(args)\r\n        }\r\n    }\r\n    _getToolbarItems() {\r\n        const items = super._getToolbarItems();\r\n        const editButtonItems = this._editingController.prepareEditButtons(this);\r\n        return editButtonItems.concat(items)\r\n    }\r\n};\r\nexport const editingModule = {\r\n    defaultOptions: () => ({\r\n        editing: {\r\n            mode: \"row\",\r\n            refreshMode: \"full\",\r\n            newRowPosition: VIEWPORT_TOP_NEW_ROW_POSITION,\r\n            allowAdding: false,\r\n            allowUpdating: false,\r\n            allowDeleting: false,\r\n            useIcons: false,\r\n            selectTextOnEditStart: false,\r\n            confirmDelete: true,\r\n            texts: {\r\n                editRow: messageLocalization.format(\"dxDataGrid-editingEditRow\"),\r\n                saveAllChanges: messageLocalization.format(\"dxDataGrid-editingSaveAllChanges\"),\r\n                saveRowChanges: messageLocalization.format(\"dxDataGrid-editingSaveRowChanges\"),\r\n                cancelAllChanges: messageLocalization.format(\"dxDataGrid-editingCancelAllChanges\"),\r\n                cancelRowChanges: messageLocalization.format(\"dxDataGrid-editingCancelRowChanges\"),\r\n                addRow: messageLocalization.format(\"dxDataGrid-editingAddRow\"),\r\n                deleteRow: messageLocalization.format(\"dxDataGrid-editingDeleteRow\"),\r\n                undeleteRow: messageLocalization.format(\"dxDataGrid-editingUndeleteRow\"),\r\n                confirmDeleteMessage: messageLocalization.format(\"dxDataGrid-editingConfirmDeleteMessage\"),\r\n                confirmDeleteTitle: \"\"\r\n            },\r\n            form: {\r\n                colCount: 2\r\n            },\r\n            popup: {},\r\n            startEditAction: \"click\",\r\n            editRowKey: null,\r\n            editColumnName: null,\r\n            changes: []\r\n        },\r\n        useLegacyColumnButtonTemplate: false\r\n    }),\r\n    controllers: {\r\n        editing: EditingControllerImpl\r\n    },\r\n    extenders: {\r\n        controllers: {\r\n            data: dataControllerEditingExtenderMixin\r\n        },\r\n        views: {\r\n            rowsView: rowsView,\r\n            headerPanel: headerPanel\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AACA;AAAA;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AAAA;AAIA;AAAA;AAKA;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAMA;AAAA;AAGA;AAIA;AACA;AACA;AACA;AA8CA;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,MAAM,8BAA8B,wLAAA,CAAA,UAAO,CAAC,cAAc;IACtD,OAAO;QACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAC1C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,aAAa,CAAC;QACrD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC;QAChD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,aAAa,CAAC;QACxD,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,QAAQ,GAAG,EAAE;QAClB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;gBACrB,EAAE,MAAM,CAAC;YACb;QACJ;QACA,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC3B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;YAC5D,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QAC7D;QACA,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,gBAAgB;gBAC9B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,kBAAkB;gBAChC,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBAC/B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,kBAAkB;gBAChC,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBAC/B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;gBAC9B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,iBAAiB;gBAC/B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,gBAAgB;gBAC9B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,WAAW;gBACzB,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,YAAY;gBAC1B,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,mBAAmB;gBACjC,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;YACA,IAAI,CAAC,YAAY,CAAC,kBAAkB;gBAChC,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;QACJ;QACA,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,IAAI;QAC9B;QACA,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,+LAAA,CAAA,iCAA8B,CAAC,GAAG;QACrE,IAAI,CAAC,SAAS,CAAC,mBAAmB,CAAC,+LAAA,CAAA,8BAA2B,CAAC,GAAG;IACtE;IACA,cAAc;YACO;QAAjB,MAAM,WAAW,CAAA,eAAA,IAAI,CAAC,MAAM,CAAC,6BAAZ,0BAAA,eAA+B,+LAAA,CAAA,gBAAa;QAC7D,IAAI,+LAAA,CAAA,aAAU,CAAC,QAAQ,CAAC,WAAW;YAC/B,OAAO;QACX;QACA,OAAO,+LAAA,CAAA,gBAAa;IACxB;IACA,sBAAsB;QAClB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,+LAAA,CAAA,mBAAgB,CAAC,QAAQ,CAAC;IACrC;IACA,4BAA4B;QACxB,OAAO,CAAC,WAAW;YACf,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;YACpC,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ,MAAM,EAAE;gBAC7C,OAAO,QAAQ,KAAK;gBACpB,UAAU,QAAQ,QAAQ;gBAC1B,KAAK,QAAQ,GAAG;gBAChB,YAAY;gBACZ,OAAO;gBACP,UAAU,CAAC,QAAQ,QAAQ;gBAC3B,UAAU,QAAQ,QAAQ;gBAC1B,IAAI,QAAQ,EAAE;YAClB;YACA,MAAM,YAAY,+LAAA,CAAA,mCAAgC,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;YAC5E,IAAI,WAAW;gBACX,aAAa,CAAC,kBAAkB,GAAG,QAAQ,MAAM,CAAC,QAAQ;YAC9D;YACA,IAAI,CAAC,wBAAwB,CAAC,YAAY,CAAC,SAAS;QACxD;IACJ;IACA,qBAAqB;QACjB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;QACnC,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;QAClC,IAAI,cAAc,eAAe;YAC7B,OAAQ;gBACJ,KAAK,+LAAA,CAAA,4BAAyB;oBAC1B,OAAO,+LAAA,CAAA,gCAA6B;gBACxC,KAAK,+LAAA,CAAA,+BAA4B;oBAC7B,OAAO,+LAAA,CAAA,mCAAgC;gBAC3C;oBACI,OAAO;YACf;QACJ;QACA,OAAO;IACX;IACA,aAAa;QACT,OAAO,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,8BAA2B;IAClD;IACA,oBAAoB;QAChB,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,8BAA2B;QACvD,OAAO,QAAQ,MAAM,CAAE,CAAA,SAAU,aAAa,OAAO,IAAI,EAAG,MAAM;IACtE;IACA,eAAe;QACX,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,YAAY,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,MAAM;QAClF,IAAI,WAAW;YACX,IAAI,CAAC,aAAa,CAAC,+LAAA,CAAA,8BAA2B,EAAE,EAAE;YAClD,IAAI,CAAC,cAAc,CAAC,KAAK;QAC7B;IACJ;IACA,iBAAiB,GAAG,EAAE;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAE;IAC9C;IACA,iBAAiB,MAAM,EAAE;QACrB,MAAM,eAAe,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;QACrD,IAAI,cAAc;YACd,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,cAAc;QAChC;QACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAE,OAAO,GAAG,GAAG;QAChD,OAAO;IACX;IACA,YAAY,GAAG,EAAE;QACb,IAAI;QACJ,OAAO,SAAS,CAAC,wBAAwB,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,OAAO;IACrJ;IACA,eAAe,IAAI,EAAE;QACjB,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;QACvC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,YAAY,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,KAAK;QACnD,IAAI,OAAO,CAAC,UAAU,EAAE;YACpB,OAAO,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,MAAM,OAAO,CAAC,UAAU,CAAC,IAAI;QAChE;QACA,OAAO;IACX;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,UAAU,GAAG,MAAM,CAAE,CAAA,SAAU,OAAO,IAAI,IAAI,OAAO,IAAI,KAAK,+LAAA,CAAA,6BAA0B,EAAG,GAAG,CAAE,CAAA,SAAU,OAAO,IAAI;IACrI;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,UAAU,GAAG,MAAM,CAAE,CAAA,SAAU,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,KAAK,OAAO,IAAI,KAAK,+LAAA,CAAA,6BAA0B,EAAG,GAAG,CAAE,CAAA,SAAU,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG;IACtK;IACA,uBAAuB,GAAG,EAAE;QACxB,IAAI,aAAa,KAAK;YAClB;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,eAAe;QAC1C,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,IAAI,CAAC,KAAK;IACrD;IACA,yBAAyB,cAAc,EAAE,CAAC;IAC1C,eAAe,cAAc,EAAE,CAAC;IAChC,mBAAmB,IAAI,EAAE,CAAC;IAC1B,wBAAwB,MAAM,EAAE,OAAO,EAAE;QACrC,IAAI,SAAS;QACb,OAAQ,OAAO,IAAI;YACf,KAAK;gBACD,SAAS,IAAI,CAAC,aAAa,CAAC;gBAC5B;YACJ,KAAK;gBACD,SAAS;QACjB;QACA,OAAO;IACX;IACA,kBAAkB;QACd,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC;QAC7B,OAAO,aAAa,+LAAA,CAAA,kBAAe;IACvC;IACA,iBAAiB,MAAM,EAAE,OAAO,EAAE;QAC9B,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;QACJ,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,UAAU;YACrB,OAAO,IAAI,CAAC,uBAAuB,CAAC,QAAQ;QAChD;QACA,OAAO,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,WAAW,QAAQ,IAAI,CAAC,QAAQ;YAC9C,WAAW,QAAQ,SAAS;YAC5B,KAAK,QAAQ,GAAG;YAChB,QAAQ,QAAQ,MAAM;QAC1B,KAAK;IACT;IACA,kBAAkB,MAAM,EAAE,OAAO,EAAE;QAC/B,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;QACJ,OAAO,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,YAAY,SAAS,IAAI,CAAC,QAAQ;YAChD,WAAW,QAAQ,SAAS;YAC5B,KAAK,QAAQ,GAAG;YAChB,QAAQ,QAAQ,MAAM;QAC1B,KAAK,CAAC,CAAC;IACX;IACA,iBAAiB,MAAM,EAAE,OAAO,EAAE;QAC9B,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,SAAS,CAAC;QAC5C,MAAM,aAAa,CAAA,GAAA,yMAAA,CAAA,gBAAa,AAAD,EAAE;QACjC,MAAM,eAAe,CAAA,GAAA,yMAAA,CAAA,kBAAe,AAAD,EAAE;QACrC,MAAM,aAAa,+LAAA,CAAA,eAAY,CAAC,WAAW;QAC3C,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;QACnC,MAAM,aAAa,+LAAA,CAAA,sBAAmB,CAAC,WAAW;QAClD,MAAM,cAAc,aAAa,cAAc,CAAC,WAAW,GAAG;QAC9D,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;YACV,MAAM;YACN,MAAM,YAAY,CAAC,WAAW;YAC9B,UAAU,+LAAA,CAAA,kBAAe,CAAC,WAAW;QACzC,GAAG;YACC,SAAS,cAAc,CAAC,CAAA;gBACpB,MAAM,EACF,OAAO,KAAK,EACf,GAAG;gBACJ,MAAM,eAAe;gBACrB,MAAM,cAAc;gBACpB,WAAY;oBACR,QAAQ,GAAG,IAAI,eAAe,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,GAAG,CAAC,QAAQ;gBAC3F;YACJ,CAAC;QACL,GAAG;IACP;IACA,mBAAmB,OAAO,EAAE;QACxB,IAAI;QACJ,MAAM,oBAAoB,CAAC,CAAC,QAAQ,MAAM,CAAC,OAAO;QAClD,IAAI,UAAU,CAAC,QAAQ,MAAM,CAAC,OAAO,IAAI,EAAE,EAAE,KAAK;QAClD,IAAI,mBAAmB;YACnB,cAAc,CAAA,GAAA,yMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;YACtC,IAAI,eAAe,GAAG;gBAClB,IAAI,CAAA,GAAA,yMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,UAAU,GAAG;oBACrC,QAAQ,MAAM,CAAC,cAAc,GAAG,GAAG;gBACvC;gBACA,IAAI,CAAA,GAAA,yMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,YAAY,GAAG;oBACvC,QAAQ,MAAM,CAAC,CAAA,GAAA,yMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,UAAU,GAAG,GAAG;gBAC3D;YACJ;YACA,cAAc,CAAA,GAAA,yMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS;YACtC,IAAI,eAAe,KAAK,CAAA,GAAA,yMAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,cAAc,GAAG;gBAC7D,QAAQ,MAAM,CAAC,cAAc,GAAG,GAAG;YACvC;QACJ,OAAO;YACH,UAAU,+LAAA,CAAA,eAAY,CAAC,KAAK;QAChC;QACA,OAAO,QAAQ,GAAG,CAAE,CAAA,SAAU,IAAI,CAAC,gBAAgB,CAAC,QAAQ;IAChE;IACA,sBAAsB,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM,EAAE;QACxD,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,UAAU;gBACxC,IAAI,CAAC,aAAa,CAAC,YAAY,QAAQ,SAAS;YACpD;QACJ;IACJ;IACA,8BAA8B;QAC1B,OAAO,CAAC,WAAW,SAAS;YACxB,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;YACrB,IAAI,WAAW,QAAQ,OAAO,EAAE;gBAC5B,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC;gBACxC,IAAI,CAAC,qBAAqB,CAAC,YAAY,SAAS,SAAS;gBACzD,IAAI,QAAQ,KAAK,EAAE;oBACf,MAAM,UAAU,QAAQ,KAAK,CAAE,IAAM,QAAQ,GAAG,CAAE,CAAA,SAAU,CAAC;gCACzD,SAAS,IAAI,CAAC,gBAAgB,CAAC,QAAQ;gCACvC,UAAU,IAAI,CAAC,iBAAiB,CAAC,QAAQ;4BAC7C,CAAC,IAAO;wBACJ,WAAW,KAAK;wBAChB,IAAI,CAAC,qBAAqB,CAAC,YAAY,SAAS;oBACpD;oBACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,YAAY,2KAAA,CAAA,cAAW,EAAE;gBAC7C;YACJ,OAAO;gBACH,sLAAA,CAAA,UAAa,CAAC,YAAY,CAAC;YAC/B;QACJ;IACJ;IACA,qBAAqB;QACjB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO,+LAAA,CAAA,kBAAe,CAAC,QAAQ,CAAC;IACpC;IACA,8BAA8B;QAC1B,IAAI;QACJ,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAChE,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;YAC1B,IAAI,OAAO,YAAY,EAAE;gBACrB,cAAc;gBACd,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,0BAA0B,QAAQ,EAAE;QAChC,IAAI;QACJ,MAAM,cAAc,IAAI,CAAC,2BAA2B;QACpD,OAAO,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,eAAe,CAAC,YAAY,GAAG;IAC/I;IACA,oBAAoB,QAAQ,EAAE;QAC1B,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C;IACA,cAAc,GAAG,EAAE,KAAK,EAAE;QACtB,OAAO,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,KAAK;IAC5C;IACA,WAAW,QAAQ,EAAE;QACjB,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,MAAM,GAAG,IAAK;YACzF,IAAI,OAAO,CAAC,EAAE,CAAC,IAAI,IAAI,CAAC,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,MAAM,QAAQ,GAAG;gBACjH,SAAS;gBACT;YACJ;QACJ;QACA,OAAO;IACX;IACA,UAAU;QACN,KAAK,CAAC;QACN,aAAa,IAAI,CAAC,oBAAoB;QACtC,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,4KAAA,CAAA,UAAa,CAAC,EAAE,EAAE,IAAI,CAAC,uBAAuB;QACzF,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,4KAAA,CAAA,UAAa,CAAC,IAAI,EAAE,IAAI,CAAC,yBAAyB;QAC7F,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,0KAAA,CAAA,OAAc,EAAE,IAAI,CAAC,kBAAkB;IACtF;IACA,cAAc,IAAI,EAAE,KAAK,EAAE;QACvB,IAAI,sBAAsB,MAAM;YAC5B,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,EAAE;QAC5C;QACA,KAAK,CAAC,cAAc,MAAM;IAC9B;IACA,cAAc,IAAI,EAAE;QAChB,IAAI,cAAc,KAAK,IAAI,EAAE;YACzB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;YACJ,IAAI,aAAa,+LAAA,CAAA,iCAA8B,EAAE;gBAC7C,IAAI,CAAC,uBAAuB,CAAC;YACjC,OAAO,IAAI,aAAa,+LAAA,CAAA,8BAA2B,EAAE;gBACjD,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,KAAK,KAAK,EAAE,IAAI,CAAC,QAAQ,EAAE;oBACpD,UAAU;gBACd;gBACA,IAAI,CAAC,SAAS;oBACV,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,kLAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,EAAE,KAAK,KAAK;oBAClD,IAAI,CAAC,oBAAoB,CAAC;gBAC9B;YACJ,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE;gBACtB,IAAI,CAAC,kBAAkB,CAAC,MAAM;gBAC9B,IAAI,CAAC,IAAI;gBACT,IAAI,CAAC,YAAY;gBACjB,IAAI,CAAC,oBAAoB;gBACzB,IAAI,CAAC,gBAAgB;YACzB;YACA,KAAK,OAAO,GAAG;QACnB,OAAO;YACH,KAAK,CAAC,cAAc;QACxB;IACJ;IACA,wBAAwB,IAAI,EAAE;QAC1B,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,KAAK,KAAK;QACjE,MAAM,wBAAwB,IAAI,CAAC,0BAA0B;QAC7D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,KAAK,aAAa,IAAI;QAChF,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,GAAG;YACvB,IAAI,KAAK,KAAK,KAAK,KAAK,aAAa,EAAE;gBACnC,IAAI,CAAC,yBAAyB,CAAC,UAAU;YAC7C;QACJ,OAAO;YACH,IAAI,CAAC,cAAc;QACvB;IACJ;IACA,qBAAqB,IAAI,EAAE;QACvB,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,UAAU,KAAK,KAAK;QAC1B,IAAI,CAAC,KAAK,KAAK,CAAC,MAAM,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,EAAE;YAClD;QACJ;QACA,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI,aAAa,OAAO,IAAI,EAAE;gBAC1B,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;gBACH,IAAI;gBACJ,MAAM,QAAQ,eAAe,kBAAkB,MAAM,CAAC,SAAS,CAAC,wBAAwB,eAAe,KAAK,EAAE,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,GAAG,CAAE,CAAA,OAAQ,KAAK,IAAI,CAAE;gBAC7M,MAAM,WAAW,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,OAAO,GAAG,EAAE,OAAO,eAAe,GAAG;gBAClF,IAAI,CAAC,gBAAgB,CAAC;oBAClB,KAAK,OAAO,GAAG;oBACf,SAAS,KAAK,CAAC,SAAS;gBAC5B;YACJ;QACJ;QACA,eAAe,WAAW,CAAC;YACvB,oBAAoB;YACpB,cAAc;YACd,iBAAiB;QACrB;IACJ;IACA,gBAAgB;QACZ,OAAO;YAAC;YAAU;YAAa;YAAe;YAAW;YAAgB;YAAkB;SAAc;IAC7G;IACA,UAAU;QACN,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,UAAU,GAAG;YAC7B;QACJ;QACA,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,IAAI,EAAE;IAClC;IACA,aAAa,MAAM,EAAE,CAAC;IACtB,YAAY;QACR,MAAM,sBAAsB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B;QAChF,OAAO;IACX;IACA,UAAU,QAAQ,EAAE;QAChB,OAAO;IACX;IACA,eAAe,KAAK,EAAE,MAAM,EAAE;QAC1B,IAAI,QAAQ;YACR,IAAI,CAAC,aAAa,CAAC,+LAAA,CAAA,iCAA8B,EAAE;QACvD,OAAO;YACH,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B,EAAE;QAChD;QACA,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,iBAAiB;QAC1B;IACJ;IACA,sBAAsB,QAAQ,EAAE,MAAM,EAAE;QACpC,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAClD,IAAI,KAAK,MAAM,KAAK;YAChB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YAC/B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,KAAK;IAC7B;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,uBAAuB;IACvC;IACA,sBAAsB;QAClB,OAAO,CAAC;IACZ;IACA,iBAAiB,QAAQ,EAAE;QACvB,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAClD,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B,GAAG;QAC/F,IAAI,YAAY;YACZ,OAAO,IAAI,CAAC,uBAAuB,OAAO;QAC9C;QACA,OAAO;IACX;IACA,WAAW,eAAe,EAAE,WAAW,EAAE;QACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,oBAAoB,IAAI,CAAC,0BAA0B,OAAO;IAC3F;IACA,kBAAkB,CAAC;IACnB,iBAAiB,IAAI,EAAE;QACnB,OAAO;IACX;IACA,mBAAmB,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE;QAChD,IAAI,SAAS,CAAC;QACd,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,MAAM,IAAI,CAAC,0BAA0B,CAAC;QAC5C,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,MAAM,MAAM,EAAE;YACvC,SAAS;QACb,OAAO,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM;YACvB,MAAM,IAAI,CAAE,CAAC,MAAM;gBACf,MAAM,kBAAkB,oBAAoB,IAAI,CAAC,gBAAgB,CAAC;gBAClE,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;oBAChB,IAAI,mBAAmB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,+LAAA,CAAA,eAAY,CAAC,GAAG;wBAClD,IAAI,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,KAAK,GAAG,EAAE,MAAM;4BAC7B,SAAS;wBACb;oBACJ,OAAO,IAAI,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,eAAe,KAAK,CAAC,OAAO,MAAM;wBACtD,SAAS;oBACb;gBACJ;gBACA,IAAI,UAAU,GAAG;oBACb,MAAM,WAAW,KAAK,CAAC,SAAS,EAAE;oBAClC,IAAI,YAAY,CAAC,aAAa,SAAS,OAAO,IAAI,qBAAqB,SAAS,OAAO,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,cAAc,GAAG;wBAC1H;oBACJ;oBACA,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,cAAc,GAAG;wBAClC,UAAU;oBACd;oBACA,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,iBAAiB,GAAG,EAAE;QAClB,IAAI;QACJ,MAAM,OAAO;YACT,KAAK;QACT;QACA,MAAM,aAAa,SAAS,CAAC,yBAAyB,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,KAAK,MAAM,yBAAyB,KAAK,IAAI,uBAAuB,UAAU;QACnK,IAAI,SAAS,cAAc,KAAK,MAAM,cAAc,UAAU,CAAC,+LAAA,CAAA,eAAY,CAAC,EAAE;YAC1E,IAAI,CAAC,+LAAA,CAAA,eAAY,CAAC,GAAG,UAAU,CAAC,+LAAA,CAAA,eAAY,CAAC;QACjD;QACA,OAAO;IACX;IACA,mBAAmB,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE;QAChD,IAAI,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,OAAO,QAAQ;QAC5D,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,IAAI,iBAAiB,GAAG;YACpB,MAAM,iBAAiB,IAAI,CAAC,kBAAkB;YAC9C,MAAM,YAAY,eAAe,SAAS;YAC1C,MAAM,yBAAyB,IAAI,CAAC,0BAA0B,CAAC;YAC/D,IAAI,mBAAmB,+LAAA,CAAA,wBAAqB,IAAI,MAAM,aAAa,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,yBAAyB;gBACnG,iBAAiB;YACrB,OAAO,IAAI,mBAAmB,+LAAA,CAAA,wBAAqB,IAAI,eAAe,gBAAgB,IAAI;gBACtF,iBAAiB,MAAM,MAAM;YACjC;QACJ;QACA,OAAO;IACX;IACA,aAAa,KAAK,EAAE,CAAC,EAAE;QACnB,MAAM,EACF,YAAY,UAAU,EACzB,GAAG;QACJ,IAAI,CAAC,MAAM,CAAC;QACZ,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI;YACJ,MAAM,WAAW,OAAO,IAAI,KAAK,+LAAA,CAAA,6BAA0B;YAC3D,IAAI,CAAC,UAAU;gBACX;YACJ;YACA,IAAI,EACA,KAAK,GAAG,EACX,GAAG;YACJ,IAAI,aAAa,SAAS,CAAC,yBAAyB,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,KAAK,MAAM,yBAAyB,KAAK,IAAI,uBAAuB,UAAU;YACjK,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa;gBAC3C,aAAa,IAAI,CAAC,cAAc,CAAC;gBACjC,MAAM,WAAW,GAAG;YACxB;YACA,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,OAAO;YACtD,MAAM,OAAO,IAAI,CAAC,gBAAgB,CAAC;YACnC,IAAI,kBAAkB,GAAG;gBACrB,MAAM,MAAM,CAAC,gBAAgB,GAAG;YACpC;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,IAAI,EAAE,OAAO,EAAE,kBAAkB,EAAE;QAC/C,MAAM,UAAU,QAAQ,cAAc;QACtC,MAAM,MAAM,KAAK,IAAI,CAAC,+LAAA,CAAA,eAAY,CAAC,GAAG,KAAK,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG;QAC9D,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,YAAY,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,KAAK;QACnD,KAAK,SAAS,GAAG;QACjB,IAAI,aAAa,GAAG;YAChB,IAAI,CAAC,oBAAoB,CAAC,MAAM,OAAO,CAAC,UAAU,EAAE,KAAK,SAAS;QACtE;IACJ;IACA,qBAAqB,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,kBAAkB,EAAE;QACjE,MAAM,EACF,MAAM,IAAI,EACV,MAAM,IAAI,EACb,GAAG;QACJ,OAAQ;YACJ,KAAK,+LAAA,CAAA,6BAA0B;gBAC3B,KAAK,QAAQ,GAAG;gBAChB,KAAK,GAAG,GAAG;gBACX,KAAK,IAAI,GAAG;gBACZ;YACJ,KAAK,+LAAA,CAAA,6BAA0B;gBAC3B,KAAK,QAAQ,GAAG;gBAChB,KAAK,OAAO,GAAG,KAAK,IAAI;gBACxB,KAAK,IAAI,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,IAAI,EAAE;gBAC/C,KAAK,cAAc,GAAG,mBAAmB,MAAM,SAAS;gBACxD;YACJ,KAAK,+LAAA,CAAA,6BAA0B;gBAC3B,KAAK,OAAO,GAAG;QACvB;IACJ;IACA,YAAY,OAAO,EAAE;QACjB,IAAI,CAAC,aAAa,CAAC,gBAAgB;QACnC,IAAI,QAAQ,OAAO,EAAE;YACjB,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;YAC7B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,OAAO,GAAG,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,CAAA,GAAA,yMAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,IAAI,CAAE,CAAA,MAAO,IAAI,CAAC,sBAAsB,CAAC;YACxI,OAAO;QACX;IACJ;IACA,oBAAoB;QAChB,MAAM,aAAa,CAAC;QACpB,UAAU,CAAC,+LAAA,CAAA,eAAY,CAAC,GAAG,IAAI,CAAC,eAAe;QAC/C,OAAO;IACX;IACA,eAAe,MAAM,EAAE,SAAS,EAAE;QAC9B,IAAI;QACJ,IAAI;QACJ,OAAO,GAAG,GAAG,IAAI,CAAC,iBAAiB,CAAC;QACpC,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,aAAa,SAAS,CAAC,yBAAyB,IAAI,CAAC,gBAAgB,CAAC,IAAI,KAAK,KAAK,MAAM,yBAAyB,KAAK,IAAI,uBAAuB,UAAU;QAC7J,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa;YACxB,MAAM,yBAAyB,IAAI,CAAC,0BAA0B,CAAC;YAC/D,aAAa,IAAI,CAAC,iBAAiB;YACnC,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,yBAAyB;gBACpC,IAAI,CAAC,0BAA0B,CAAC,QAAQ;YAC5C;QACJ;QACA,IAAI,CAAC,gBAAgB,CAAC;YAClB,YAAY;YACZ,KAAK;QACT;QACA,OAAO;YACH,YAAY;YACZ,KAAK;QACT;IACJ;IACA,kBAAkB,MAAM,EAAE;QACtB,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG,GAAG;YACvB,OAAO,OAAO,GAAG;QACrB;QACA,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,GAAG;QACxC,IAAI;QACJ,IAAI,OAAO,IAAI,IAAI,WAAW,CAAC,MAAM,OAAO,CAAC,UAAU;YACnD,WAAW,OAAO,IAAI,CAAC,QAAQ;QACnC;QACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YACtB,WAAW,CAAA,GAAA,yMAAA,CAAA,wBAAqB,AAAD;QACnC;QACA,OAAO;IACX;IACA,2BAA2B,MAAM,EAAE,SAAS,EAAE;QAC1C,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC;QAC9B,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,WAAW,eAAe,KAAK,CAAC;QACtC,MAAM,iBAAiB,IAAI,CAAC,kBAAkB;QAC9C,OAAQ;YACJ,KAAK,+LAAA,CAAA,yBAAsB;YAC3B,KAAK,+LAAA,CAAA,wBAAqB;gBACtB;YACJ,KAAK,+LAAA,CAAA,4BAAyB;gBAC1B,IAAI,SAAS,MAAM,EAAE;oBACjB,OAAO,eAAe,GAAG,QAAQ,CAAC,EAAE,CAAC,GAAG;gBAC5C;gBACA;YACJ,KAAK,+LAAA,CAAA,+BAA4B;gBAC7B,IAAI,SAAS,MAAM,EAAE;oBACjB,OAAO,cAAc,GAAG,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,GAAG;gBAC7D;gBACA;YACJ;gBAAS;oBACL,MAAM,mBAAmB,mBAAmB,+LAAA,CAAA,mCAAgC;oBAC5E,IAAI,mBAAmB,mBAAmB,SAAS,YAAY,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,yBAAyB,KAAK,SAAS,YAAY,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,sBAAsB;oBACxN,MAAM,MAAM,eAAe,cAAc,EAAE,CAAC,iBAAiB;oBAC7D,IAAI,OAAO,CAAC,CAAC,IAAI,SAAS,IAAI,aAAa,IAAI,OAAO,IAAI,qBAAqB,IAAI,OAAO,GAAG;wBACzF;oBACJ;oBACA,MAAM,YAAY,eAAe,gBAAgB,CAAC;oBAClD,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;wBACtB,OAAO,eAAe,GAAG;oBAC7B;gBACJ;QACJ;IACJ;IACA,kBAAkB;QACd,IAAI,iBAAiB;QACrB,IAAI,CAAC,UAAU,GAAG,OAAO,CAAE,CAAA;YACvB,IAAI;YACJ,MAAM,aAAa,SAAS,CAAC,yBAAyB,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC,KAAK,KAAK,MAAM,yBAAyB,KAAK,IAAI,uBAAuB,UAAU;YAC5K,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe,SAAS,IAAI,KAAK,+LAAA,CAAA,6BAA0B,IAAI,UAAU,CAAC,+LAAA,CAAA,eAAY,CAAC,GAAG,gBAAgB;gBACpH,iBAAiB,UAAU,CAAC,+LAAA,CAAA,eAAY,CAAC;YAC7C;QACJ;QACA,OAAO,iBAAiB;IAC5B;IACA,2BAA2B,YAAY,EAAE;YAC9B;QAAP,OAAO,CAAA,+BAAA,aAAa,cAAc,cAA3B,0CAAA,+BAA+B,aAAa,eAAe;IACtE;IACA,2BAA2B;QACvB,MAAM,iBAAiB,IAAI,CAAC,kBAAkB;QAC9C,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,YAAY,eAAe,SAAS;QAC1C,MAAM,gBAAgB,eAAe,SAAS,KAAK;QACnD,IAAI,mBAAmB,+LAAA,CAAA,yBAAsB,IAAI,MAAM,WAAW;YAC9D,OAAO;QACX;QACA,IAAI,mBAAmB,+LAAA,CAAA,wBAAqB,IAAI,cAAc,eAAe;YACzE,OAAO;QACX;QACA,OAAO,CAAC;IACZ;IACA,OAAO,SAAS,EAAE;QACd,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,QAAQ,eAAe,KAAK;QAClC,IAAI,CAAC,OAAO;YACR,eAAe,SAAS,CAAC,SAAS,IAAI,CAAC,SAAS,CAAC,IAAI;YACrD,OAAO,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,MAAM;QAChC;QACA,OAAO,IAAI,CAAC,OAAO,CAAC;IACxB;IACA,QAAQ,SAAS,EAAE;QACf,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,QAAQ,eAAe,KAAK;QAClC,MAAM,MAAM,SAAS,MAAM,GAAG;QAC9B,MAAM,QAAQ;YACV,MAAM,CAAC;QACX;QACA,MAAM,kBAAkB,IAAI,CAAC,uBAAuB;QACpD,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,IAAI,CAAC,OAAO,CAAC;YACT,oBAAoB;QACxB;QACA,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI;YACzB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,MAAM;YACzF,OAAO,SAAS,OAAO;QAC3B;QACA,IAAI,CAAC,KAAK;YACN,MAAM,IAAI,CAAC,OAAO,GAAG,OAAO,IAAI,oJAAA,CAAA,UAAI;QACxC;QACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,WAAW,CAAC,OAAO,YAAY,IAAI,CAAE;YAC3C,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,EAAE,WAAW,kBAAkB,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,MAAM;YAC9G,OAAO;gBACH,SAAS,MAAM,CAAC;YACpB;QACJ,GAAI,IAAI,CAAC,SAAS,MAAM;QACxB,OAAO,SAAS,OAAO;IAC3B;IACA,gBAAgB,MAAM,EAAE;QACpB,MAAM,cAAc,IAAI,CAAC,eAAe;QACxC,IAAI,cAAc,GAAG;YACjB,OAAO;QACX;QACA,OAAO;IACX;IACA,YAAY,IAAI,EAAE,SAAS,EAAE,sBAAsB,EAAE;QACjD,MAAM,SAAS;YACX,MAAM;YACN,MAAM,+LAAA,CAAA,6BAA0B;QACpC;QACA,MAAM,eAAe,IAAI,CAAC,uBAAuB;QACjD,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC,QAAQ;QAC/C,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,IAAI,CAAC,cAAc,CAAC,KAAK;QACzB,IAAI,CAAC,UAAU,CAAC;QAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,QAAQ;IAClE;IACA,kBAAkB,eAAe,EAAE,MAAM,EAAE,YAAY,EAAE;QACrD,MAAM,IAAI,IAAI,oLAAA,CAAA,WAAQ;QACtB,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,eAAe,yBAAA,0BAAA,eAAgB,CAAC;QAChC,SAAS,mBAAA,oBAAA,SAAU,IAAI,CAAC,UAAU,GAAG,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,+LAAA,CAAA,6BAA0B,CAAE,CAAC,EAAE;QAC5F,IAAI,CAAC,QAAQ;YACT,OAAO,EAAE,MAAM,CAAC,UAAU,OAAO;QACrC;QACA,MAAM,uBAAuB,IAAI,CAAC,wBAAwB;QAC1D,IAAI,WAAW,IAAI,CAAC,kBAAkB,CAAC,eAAe,KAAK,IAAI,QAAQ;QACvE,MAAM,qBAAqB,CAAA;YACvB,IAAI;YACJ,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,SAAS,CAAC,wBAAwB,IAAI,CAAC,gBAAgB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,aAAa,CAAC,MAAM,IAAI,CAAE;gBACzJ,WAAW,eAAe,gBAAgB,CAAC,OAAO,GAAG;gBACrD,EAAE,OAAO;YACb;QACJ;QACA,MAAM,yBAAyB,IAAI,CAAC,0BAA0B,CAAC;QAC/D,IAAI,wBAAwB,GAAG;YAC3B,eAAe,SAAS,CAAC,sBAAsB,IAAI,CAAE;gBACjD,mBAAmB,OAAO,GAAG;YACjC,GAAI,IAAI,CAAC,EAAE,MAAM;QACrB,OAAO,IAAI,WAAW,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,yBAAyB;YAC1D,mBAAmB;QACvB,OAAO;YACH,eAAe,WAAW,CAAC;gBACvB,YAAY;gBACZ,YAAY;oBAAC;oBAAiB;oBAAc;iBAAS;YACzD;YACA,WAAW,eAAe,gBAAgB,CAAC,OAAO,GAAG;YACrD,IAAI,WAAW,GAAG;gBACd,mBAAmB,OAAO,GAAG;YACjC,OAAO;gBACH,EAAE,OAAO;YACb;QACJ;QACA,EAAE,IAAI,CAAE;YACJ,IAAI;YACJ,SAAS,CAAC,mBAAmB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,kBAAkB,CAAC,MAAM,IAAI,CAAE;gBAC3H,IAAI,CAAC,aAAa,CAAC;gBACnB,IAAI,CAAC,eAAe,CAAC,OAAO,GAAG;YACnC;QACJ;QACA,OAAO,EAAE,OAAO;IACpB;IACA,cAAc,QAAQ,EAAE;QACpB,IAAI,CAAC,4BAA4B,CAAC;IACtC;IACA,yBAAyB,QAAQ,EAAE,CAAC;IACpC,6BAA6B,QAAQ,EAAE;QACnC,IAAI;QACJ,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,MAAM,eAAe,gBAAgB,CAAC;QAC5C,MAAM,aAAa,IAAI,CAAC,yBAAyB,CAAC;QAClD,SAAS,CAAC,wBAAwB,IAAI,CAAC,6BAA6B,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,KAAK,CAAC;QACzI,IAAI,CAAC,MAAM,CAAC,iBAAiB;QAC7B,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,kBAAkB,CAAC,YAAa;YACjC,WAAW,eAAe,gBAAgB,CAAC;YAC3C,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,CAAC,wBAAwB,CAAC;QAClC;IACJ;IACA,gBAAgB,OAAO,EAAE;QACrB,IAAI,CAAC,aAAa,CAAC,kBAAkB;QACrC,OAAO,QAAQ,MAAM;IACzB;IACA,mBAAmB,UAAU,EAAE,QAAQ,EAAE,CAAC;IAC1C,6BAA6B;QACzB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,qCAAkC;QACrE,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;YAC5B,OAAO,CAAC;QACZ;QACA,OAAO,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC;IACzD;IACA,0BAA0B,KAAK,EAAE,MAAM,EAAE;QACrC,IAAI;QACJ,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAChE,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,wBAAwB,cAAc,CAAC,MAAM,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,EAAE;IAChK;IACA,mBAAmB,IAAI,EAAE,MAAM,EAAE;QAC7B,IAAI,QAAQ;YACR,IAAI,CAAC,aAAa,CAAC,+LAAA,CAAA,qCAAkC,EAAE;QAC3D,OAAO;YACH,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,qCAAkC,EAAE;QACpD;IACJ;IACA,uBAAuB;QACnB,IAAI,CAAC,kBAAkB,CAAC,MAAM;IAClC;IACA,iBAAiB;QACb,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,qCAAkC;QACrE,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC;IACA,iBAAiB,IAAI,EAAE;QACnB,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAChE,IAAI;QACJ,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,eAAe,IAAI,CAAE,CAAA;YACpC,IAAI,OAAO,IAAI,KAAK,MAAM;gBACtB,aAAa;gBACb,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,wBAAwB,UAAU,EAAE;QAChC,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B;QAC7D,MAAM,WAAW,eAAe,gBAAgB,CAAC;QACjD,IAAI,CAAC,MAAM,UAAU;YACjB,OAAO;QACX;QACA,OAAO,WAAW,IAAI,CAAC,0BAA0B,CAAC;IACtD;IACA,2BAA2B,UAAU,EAAE;QACnC,MAAM,aAAa,aAAa,IAAI,CAAC,gBAAgB,CAAC,cAAc,IAAI,CAAC,cAAc;QACvF,MAAM,iBAAiB,qBAAqB,CAAC,SAAS,cAAc,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,YAAY;QAC5H,OAAO,iBAAiB,IAAI;IAChC;IACA,mBAAmB;QACf,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,cAAc,CAAC,MAAM;IAC9B;IACA,oBAAoB;QAChB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,gBAAgB;IACzB;IACA,QAAQ,QAAQ,EAAE;QACd,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,QAAQ,eAAe,KAAK;QAClC,MAAM,OAAO,KAAK,CAAC,SAAS;QAC5B,MAAM,SAAS;YACX,MAAM,QAAQ,KAAK,IAAI;YACvB,QAAQ;QACZ;QACA,MAAM,cAAc,IAAI,CAAC,uBAAuB;QAChD,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,aAAa,aAAa;YAC1B,OAAO;QACX;QACA,IAAI,KAAK,MAAM,KAAK,GAAG,EAAE;YACrB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;YAC/B;QACJ;QACA,IAAI,CAAC,KAAK,QAAQ,EAAE;YAChB,OAAO,GAAG,GAAG,KAAK,GAAG;QACzB;QACA,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS;YAC9B;QACJ;QACA,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,UAAU,GAAG,eAAe,SAAS;YAG7B;QAFb,IAAI,CAAC,gBAAgB,CAAC;YAClB,KAAK,KAAK,GAAG;YACb,SAAS,CAAA,gBAAA,KAAK,OAAO,cAAZ,2BAAA,gBAAgB,KAAK,IAAI;QACtC;QACA,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;IAChC;IACA,0BAA0B,QAAQ,EAAE,WAAW,EAAE;QAC7C,MAAM,aAAa;YAAC;YAAa;SAAS;QAC1C,IAAI,CAAC,kBAAkB,CAAC,YAAY,UAAU;QAC9C,IAAI,CAAC,6BAA6B,CAAC,YAAY;IACnD;IACA,8BAA8B,UAAU,EAAE,QAAQ,EAAE,gBAAgB,EAAE;QAClE,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;YAC7B,YAAY;YACZ,YAAY;YACZ,QAAQ;QACZ;IACJ;IACA,qBAAqB,CAAC;IACtB,eAAe,QAAQ,EAAE,WAAW,EAAE,CAAC;IACvC,oBAAoB,CAAC;IACrB,6BAA6B;QACzB,OAAO,CAAA;YACH,IAAI,IAAI,CAAC,SAAS,IAAI;gBAClB,IAAI,CAAC,cAAc;YACvB;QACJ;IACJ;IACA,0BAA0B,QAAQ,EAAE,CAAC;IACrC,uBAAuB;QACnB,MAAM,eAAe;YACjB,MAAM,IAAI,CAAC,MAAM,CAAC;YAClB,SAAS,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QACxC;QACA,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,MAAM;YACrB,aAAa,WAAW,GAAG;YAC3B,aAAa,IAAI,GAAG;QACxB;QACA,OAAO;IACX;IACA,yBAAyB;QACrB,MAAM,eAAe;YACjB,MAAM,IAAI,CAAC,MAAM,CAAC;YAClB,SAAS,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;QAC1C;QACA,IAAI,CAAA,GAAA,oJAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,oJAAA,CAAA,UAAO,AAAD,MAAM;YACrB,aAAa,WAAW,GAAG;QAC/B;QACA,OAAO;IACX;IACA,oBAAoB,GAAG,EAAE;QACrB,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAE;IAC1C;IACA,+BAA+B,OAAO,EAAE,KAAK,EAAE;QAC3C,MAAM,eAAe,OAAO,CAAC,MAAM;QACnC,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI,OAAO,IAAI,KAAK,+LAAA,CAAA,6BAA0B,EAAE;gBAC5C,MAAM,yBAAyB,IAAI,CAAC,0BAA0B,CAAC;gBAC/D,IAAI,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,wBAAwB,aAAa,GAAG,GAAG;oBACxD,MAAM,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,cAAc,IAAI,mBAAmB,kBAAkB,GAAG,IAAI,CAAC,0BAA0B,CAAC;gBACtH;YACJ;QACJ;IACJ;IACA,cAAc,KAAK,EAAE;QACjB,IAAI,SAAS,GAAG;YACZ,MAAM,UAAU;mBAAI,IAAI,CAAC,UAAU;aAAG;YACtC,MAAM,EACF,KAAK,GAAG,EACX,GAAG,OAAO,CAAC,MAAM;YAClB,IAAI,CAAC,mBAAmB,CAAC;YACzB,IAAI,CAAC,8BAA8B,CAAC,SAAS;YAC7C,QAAQ,MAAM,CAAC,OAAO;YACtB,IAAI,CAAC,aAAa,CAAC,+LAAA,CAAA,8BAA2B,EAAE;YAChD,IAAI,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B,GAAG,MAAM;gBAChE,IAAI,CAAC,iBAAiB;YAC1B;QACJ;IACJ;IACA,iBAAiB,QAAQ,EAAE,IAAI,EAAE;QAC7B,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM;QACjD,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAE;YACnC,IAAI,eAAe,SAAS,KAAK,IAAI;gBACjC;YACJ;YACA;YACA,IAAI,CAAC,cAAc,GAAG;QAC1B,GAAI,IAAI,CAAE;YACN,SAAS,MAAM;YACf,IAAI,CAAC,cAAc,GAAG;QAC1B;IACJ;IACA,4BAA4B;QACxB,OAAO,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,KAAK,IAAI,CAAC,UAAU;IAClC;IACA,8BAA8B,CAAC;IAC/B,iBAAiB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;QACjD,IAAI,CAAC,UAAU,CAAC,OAAO,gBAAgB,IAAI,aAAa,CAAC,UAAU,gBAAgB,EAAE;YACjF,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,wBAAwB,CAAC,SAAS;YACvC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;gBAC7B,YAAY;gBACZ,YAAY;oBAAC;oBAAiB,IAAI,CAAC,uBAAuB;iBAAG;YACjE;QACJ,OAAO,IAAI,WAAW,WAAW;YAC7B,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;gBAC7B,YAAY;gBACZ,YAAY,EAAE;YAClB;QACJ;IACJ;IACA,mBAAmB,KAAK,EAAE,mBAAmB,EAAE,6BAA6B,EAAE;QAC1E,MAAM,aAAa;YACf,IAAI,qBAAqB;gBACrB;YACJ;YACA,IAAI,OAAO;gBACP,MAAM,oBAAoB,MAAM,IAAI,CAAC,+LAAA,CAAA,6BAA0B,EAAE,KAAK;gBACtE,sLAAA,CAAA,UAAa,CAAC,qBAAqB,CAAC,IAAI,EAAE;YAC9C;YACA,IAAI,CAAC,oBAAoB,GAAG;QAChC;QACA,IAAI,uJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,GAAG,IAAI,uJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,OAAO,EAAE;YAC9C;QACJ,OAAO;YACH,IAAI,IAAI,CAAC,oBAAoB,EAAE;gBAC3B,IAAI,CAAC,oBAAoB;YAC7B;YACA,aAAa,IAAI,CAAC,oBAAoB;YACtC,IAAI,+BAA+B;gBAC/B,IAAI,CAAC,oBAAoB,GAAG;YAChC;YACA,IAAI,CAAC,oBAAoB,GAAG,WAAW;QAC3C;IACJ;IACA,kBAAkB,mBAAmB,EAAE,SAAS,EAAE,6BAA6B,EAAE;QAC7E,MAAM,kBAAkB,IAAI,CAAC,0BAA0B;QACvD,YAAY,aAAa,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,CAAC,uBAAuB,IAAI;QAC1G,IAAI,WAAW;YACX,IAAI,CAAC,kBAAkB,CAAC,WAAW,qBAAqB;QAC5D;IACJ;IACA,UAAU,QAAQ,EAAE;QAChB,IAAI,CAAC,kBAAkB,CAAC;IAC5B;IACA,mBAAmB,QAAQ,EAAE;QACzB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;QACnC,MAAM,eAAe,SAAS,kBAAkB,KAAK,MAAM,iBAAiB,KAAK,IAAI,eAAe,KAAK;QACzG,MAAM,gBAAgB,SAAS,kBAAkB,KAAK,MAAM,iBAAiB,KAAK,IAAI,eAAe,aAAa;QAClH,MAAM,uBAAuB,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,oBAAoB;QAC1H,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,SAAS;QACnD,MAAM,gBAAgB,CAAC,IAAI,CAAC,SAAS,MAAM,KAAK,QAAQ;QACxD,IAAI,QAAQ,eAAe;YACvB,IAAI,CAAC,iBAAiB,CAAC,sBAAsB;gBACzC,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;gBACH,MAAM,qBAAqB,gBAAgB,aAAa,kBAAkB;gBAC1E,MAAM,kBAAkB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB,mBAAmB,MAAM,GAAG;gBACrF,CAAA,GAAA,uKAAA,CAAA,UAAO,AAAD,EAAE,sBAAsB,oBAAoB,iBAAiB,IAAI,CAAE,CAAA;oBACrE,IAAI,eAAe;wBACf,IAAI,CAAC,cAAc,CAAC;oBACxB;gBACJ;YACJ;QACJ;IACJ;IACA,eAAe,QAAQ,EAAE;QACrB,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,OAAO,eAAe,KAAK,EAAE,CAAC,SAAS;QAC7C,MAAM,MAAM,QAAQ,KAAK,GAAG;QAC5B,MAAM,kBAAkB,IAAI,CAAC,uBAAuB;QACpD,IAAI,CAAC,OAAO;QACZ,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,YAAY,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,KAAK;QACnD,IAAI,aAAa,GAAG;YAChB,IAAI,OAAO,CAAC,UAAU,CAAC,IAAI,KAAK,+LAAA,CAAA,6BAA0B,EAAE;gBACxD,IAAI,CAAC,aAAa,CAAC;YACvB,OAAO;gBACH,IAAI,CAAC,UAAU,CAAC;oBACZ,KAAK;oBACL,MAAM,+LAAA,CAAA,6BAA0B;gBACpC;YACJ;QACJ,OAAO;YACH,IAAI,CAAC,UAAU,CAAC;gBACZ,KAAK;gBACL,SAAS,KAAK,IAAI;gBAClB,MAAM,+LAAA,CAAA,6BAA0B;YACpC;QACJ;QACA,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU;IAC1C;IACA,gBAAgB,QAAQ,EAAE,eAAe,EAAE;QACvC,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,YAAY,QAAQ,EAAE;QAClB,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,OAAO,eAAe,KAAK,EAAE,CAAC,SAAS;QAC7C,MAAM,kBAAkB,IAAI,CAAC,uBAAuB;QACpD,MAAM,MAAM,QAAQ,KAAK,GAAG;QAC5B,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,MAAM;YACN,MAAM,YAAY,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,KAAK;YACnD,IAAI,aAAa,GAAG;gBAChB,MAAM,EACF,MAAM,IAAI,EACb,GAAG,OAAO,CAAC,UAAU;gBACtB,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;oBACrB,IAAI,CAAC,aAAa,CAAC;gBACvB,OAAO;oBACH,IAAI,CAAC,UAAU,CAAC;wBACZ,KAAK;wBACL,MAAM,+LAAA,CAAA,6BAA0B;oBACpC;gBACJ;gBACA,eAAe,WAAW,CAAC;oBACvB,YAAY;oBACZ,YAAY;wBAAC;wBAAiB;qBAAS;gBAC3C;YACJ;QACJ;IACJ;IACA,gBAAgB;QACZ,MAAM,iBAAiB;YACnB,QAAQ;YACR,SAAS;YACT,SAAS;mBAAI,IAAI,CAAC,UAAU;aAAG;QACnC;QACA,IAAI,CAAC,aAAa,CAAC,YAAY;QAC/B,MAAM,IAAI,IAAI,oLAAA,CAAA,WAAQ;QACtB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,eAAe,OAAO,GAAG,IAAI,CAAE;YAC5C,EAAE,OAAO,CAAC;QACd,GAAI,IAAI,CAAE,CAAA;YACN,CAAA,GAAA,yMAAA,CAAA,uBAAoB,AAAD,EAAE;YACrB,IAAI,CAAC,sBAAsB,CAAC;YAC5B,EAAE,OAAO,CAAC;gBACN,QAAQ;YACZ;QACJ;QACA,OAAO;IACX;IACA,sBAAsB,UAAU,EAAE,MAAM,EAAE,IAAI,EAAE;QAC5C,IAAI,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE;YAC1B,OAAO;QACX;QACA,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,IAAI,CAAC,aAAa,CAAC,YAAY;QAC/B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,OAAO,MAAM,GAAG,IAAI,CAAE,CAAA;YACnC,IAAI,QAAQ;gBACR,WAAY;oBACR,SAAS,OAAO,CAAC;gBACrB;YACJ,OAAO;gBACH,KAAK,QAAQ,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,CAAA,GAAA,yMAAA,CAAA,uBAAoB,AAAD,EAAE;YAClE;QACJ,GAAI,IAAI,CAAC,CAAA,GAAA,yMAAA,CAAA,uBAAoB,AAAD,EAAE;QAC9B,OAAO;IACX;IACA,gBAAgB,SAAS,EAAE,OAAO,EAAE,WAAW,EAAE,OAAO,EAAE;QACtD,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK;QACxC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,CAAC,OAAO;YACnB,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG;YAC3C,MAAM,EACF,MAAM,IAAI,EACV,MAAM,IAAI,EACb,GAAG;YACJ,MAAM,aAAa,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;YAChC,IAAI;YACJ,IAAI;YACJ,IAAI,IAAI,CAAC,mBAAmB,CAAC,QAAQ,QAAQ;gBACzC;YACJ;YACA,OAAQ;gBACJ,KAAK,+LAAA,CAAA,6BAA0B;oBAC3B,SAAS;wBACL,MAAM;wBACN,KAAK,OAAO,GAAG;wBACf,QAAQ;oBACZ;oBACA,WAAW,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,QAAS,IAAM,MAAM,MAAM,CAAC,OAAO,GAAG,EAAE,IAAI,CAAE,CAAA;4BACjG,YAAY,IAAI,CAAC;gCACb,MAAM;gCACN,KAAK;4BACT;wBACJ;oBACA;gBACJ,KAAK,+LAAA,CAAA,6BAA0B;oBAC3B,SAAS;wBACL,MAAM;wBACN,QAAQ;oBACZ;oBACA,WAAW,IAAI,CAAC,qBAAqB,CAAC,kBAAkB,QAAS,IAAM,MAAM,MAAM,CAAC,OAAO,IAAI,EAAE,IAAI,CAAE,CAAC,MAAM;4BAC1G,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM;gCAChB,WAAW,GAAG,GAAG;4BACrB;4BACA,IAAI,QAAQ,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,SAAS,OAAO,IAAI,EAAE;gCAChD,WAAW,IAAI,GAAG;4BACtB;4BACA,YAAY,IAAI,CAAC;gCACb,MAAM;gCACN,MAAM;gCACN,OAAO;4BACX;wBACJ;oBACA;gBACJ,KAAK,+LAAA,CAAA,6BAA0B;oBAC3B,SAAS;wBACL,SAAS;wBACT,SAAS;wBACT,KAAK,OAAO,GAAG;wBACf,QAAQ;oBACZ;oBACA,WAAW,IAAI,CAAC,qBAAqB,CAAC,iBAAiB,QAAS,IAAM,MAAM,MAAM,CAAC,OAAO,GAAG,EAAE,OAAO,OAAO,EAAE,IAAI,CAAE,CAAC,MAAM;4BACxH,IAAI,QAAQ,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,SAAS,OAAO,OAAO,EAAE;gCACnD,WAAW,IAAI,GAAG;4BACtB;4BACA,YAAY,IAAI,CAAC;gCACb,MAAM;gCACN,KAAK;gCACL,MAAM;4BACV;wBACJ;YACR;YACA,OAAO,CAAC,MAAM,GAAG;YACjB,IAAI,UAAU;gBACV,MAAM,eAAe,IAAI,oLAAA,CAAA,WAAQ;gBACjC,SAAS,MAAM,CAAE,CAAA;oBACb,QAAQ,IAAI,CAAC;wBACT,KAAK,OAAO,GAAG;wBACf,QAAQ;oBACZ;gBACJ,GAAI,MAAM,CAAC,aAAa,OAAO;gBAC/B,UAAU,IAAI,CAAC,aAAa,OAAO;YACvC;QACJ;IACJ;IACA,sBAAsB,OAAO,EAAE,SAAS,EAAE;QACtC,MAAM,SAAS,OAAO,CAAC,UAAU;QACjC,IAAI,CAAC,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,IAAI,MAAM,+LAAA,CAAA,6BAA0B,EAAE;YAC9F,IAAI,aAAa,GAAG;gBAChB,QAAQ,MAAM,CAAC,WAAW;YAC9B;QACJ;QACA,OAAO;IACX;IACA,eAAe,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE;QACvC,MAAM,SAAS,OAAO,CAAC,UAAU;QACjC,IAAI,CAAC,UAAU,CAAC,UAAU,OAAO,IAAI,KAAK,+LAAA,CAAA,6BAA0B,EAAE;YAClE,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,WAAW,CAAC,UAAU,CAAC;QACnE;IACJ;IACA,mBAAmB,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE;QACnD,IAAI,aAAa,GAAG;YAChB,QAAQ,MAAM,CAAC,WAAW;QAC9B;QACA,OAAO;IACX;IACA,2BAA2B,OAAO,EAAE;QAChC,IAAI,eAAe;QACnB,MAAM,kBAAkB,IAAI,CAAC,UAAU;QACvC,MAAM,UAAU;eAAI;SAAgB;QACpC,MAAM,gBAAgB,QAAQ,MAAM;QACpC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,MAAM,OAAO,CAAC,EAAE,CAAC,MAAM;YAC7B,MAAM,SAAS,aAAa;YAC5B,MAAM,YAAY,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE;YAC9D,MAAM,SAAS,OAAO,CAAC,UAAU;YACjC,MAAM,UAAU,OAAO,eAAe;YACtC,IAAI,SAAS;gBACT,IAAI,QAAQ;oBACR,IAAI,CAAC,gBAAgB,CAAC;wBAClB,KAAK,OAAO,GAAG;wBACf,OAAO;oBACX;gBACJ;gBACA,IAAI,CAAC,sBAAsB,CAAC;gBAC5B,IAAI,IAAI,CAAC,qBAAqB,CAAC,SAAS,YAAY;oBAChD;gBACJ;YACJ,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,WAAW,SAAS;gBACxD,eAAe,CAAC;gBAChB,MAAM,qBAAqB,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE;gBACvE,IAAI,CAAC,8BAA8B,CAAC,iBAAiB;YACzD;QACJ;QACA,IAAI,QAAQ,MAAM,GAAG,eAAe;YAChC,IAAI,CAAC,aAAa,CAAC,+LAAA,CAAA,8BAA2B,EAAE;QACpD;QACA,OAAO;IACX;IACA,wBAAwB,OAAO,EAAE;QAC7B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,CAAC,GAAG;YACf,IAAI,EACA,MAAM,IAAI,EACV,KAAK,GAAG,EACR,MAAM,IAAI,EACb,GAAG;YACJ,MAAM,eAAe,IAAI,CAAC,gBAAgB,CAAC;gBACvC,KAAK;YACT;YACA,MAAM,SAAS;gBACX,KAAK;gBACL,MAAM;YACV;YACA,IAAI,aAAa,KAAK,EAAE;gBACpB,OAAO,KAAK,GAAG,aAAa,KAAK;YACrC;YACA,OAAQ;gBACJ,KAAK,+LAAA,CAAA,6BAA0B;oBAC3B,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ;wBAClD,MAAM,aAAa,OAAO;oBAC9B;oBACA;gBACJ,KAAK,+LAAA,CAAA,6BAA0B;oBAC3B,IAAI,CAAC,aAAa,CAAC,iBAAiB;oBACpC;gBACJ,KAAK,+LAAA,CAAA,6BAA0B;oBAC3B,IAAI,CAAC,aAAa,CAAC,gBAAgB;YAC3C;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,WAAW;YAC1B,SAAS;QACb;IACJ;IACA,eAAe;QACX,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAE;YACnC,IAAI,IAAI,CAAC,QAAQ,IAAI;gBACjB,IAAI,CAAC,iBAAiB,CAAC;gBACvB;YACJ;YACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAE,CAAA;gBACnC,IAAI,QAAQ;oBACR,IAAI,CAAC,iBAAiB,CAAC,UAAU;wBAC7B,QAAQ;oBACZ;oBACA;gBACJ;gBACA,IAAI,CAAC,OAAO,GAAG;gBACf,IAAI,CAAC,kBAAkB,GAAG,MAAM,CAAE;oBAC9B,IAAI,CAAC,OAAO,GAAG;oBACf,IAAI,IAAI,CAAC,gBAAgB,EAAE;wBACvB,IAAI,CAAC,iBAAiB;oBAC1B;gBACJ,GAAI,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,MAAM;YACnD,GAAI,IAAI,CAAC,SAAS,MAAM;QAC5B,GAAI,IAAI,CAAC,SAAS,MAAM;QACxB,OAAO,SAAS,OAAO;IAC3B;IACA,kBAAkB,QAAQ,EAAE;QACxB,IAAI,EACA,QAAQ,MAAM,EACd,OAAO,KAAK,EACf,GAAG,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QACtE,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,kBAAkB,CAAC,SAAS,IAAI,CAAE;YACxC,SAAS,OAAO,CAAC;QACrB,GAAI,IAAI,CAAC,SAAS,MAAM;IAC5B;IACA,qBAAqB;QACjB,MAAM,SAAS,IAAI,oLAAA,CAAA,WAAQ;QAC3B,MAAM,UAAU,EAAE;QAClB,MAAM,YAAY,EAAE;QACpB,MAAM,cAAc,EAAE;QACtB,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,UAAU;QAClD,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAE,CAAA;YAC7B,IAAI,EACA,QAAQ,MAAM,EACd,SAAS,OAAO,EACnB,GAAG;YACJ,IAAI,QAAQ;gBACR,OAAO,OAAO,OAAO,GAAG,OAAO;YACnC;YACA,IAAI,CAAC,eAAe,CAAC,WAAW,SAAS,aAAa;YACtD,IAAI,UAAU,MAAM,EAAE;gBAClB,IAAI,CAAC,gBAAgB,GAAG;gBACxB,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,YAAY;gBACvE,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,KAAK,WAAW,IAAI,CAAE;oBACrB,IAAI,IAAI,CAAC,0BAA0B,CAAC,UAAU;wBAC1C,IAAI,CAAC,UAAU,CAAC,aAAa,SAAS;oBAC1C,OAAO;wBACH,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,UAAU;wBACrE,OAAO,OAAO;oBAClB;gBACJ,GAAI,IAAI,CAAE,CAAA;oBACN,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,UAAU;oBACrE,OAAO,OAAO,CAAC;gBACnB;gBACA,OAAO,OAAO,MAAM,CAAE;oBAClB,IAAI,CAAC,gBAAgB,GAAG;gBAC5B,GAAI,OAAO;YACf;YACA,IAAI,CAAC,aAAa,CAAC;QACvB,GAAI,IAAI,CAAC,OAAO,MAAM;QACtB,OAAO,OAAO,OAAO;IACzB;IACA,iBAAiB,OAAO,EAAE;QACtB,IAAI,CAAC,iBAAiB;IAC1B;IACA,WAAW,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE;QACvC,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,UAAU;QAClD,IAAI,CAAC,gBAAgB,CAAC;QACtB,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,UAAU;QACrE,IAAI,CAAC,qBAAqB,CAAC,aAAa,SAAS;IACrD;IACA,cAAc,MAAM,EAAE;QAClB,IAAI,CAAC,aAAa,CAAC,WAAW;YAC1B,SAAS,EAAE;QACf;QACA,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IACA,sBAAsB,WAAW,EAAE,OAAO,EAAE,QAAQ,EAAE;QAClD,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC;QAChC,MAAM,gBAAgB,cAAc,eAAe,cAAc;QACjE,IAAI,CAAC,eAAe;YAChB,eAAe,IAAI,CAAC;QACxB;QACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,eAAe,OAAO,CAAC;YACxB,WAAW;YACX,QAAQ;YACR,MAAM,cAAc;YACpB,aAAa,IAAI,CAAC,MAAM,CAAC;QAC7B,IAAI,MAAM,CAAE;YACR,IAAI,CAAC,uBAAuB,CAAC;QACjC,GAAI,IAAI,CAAE;YACN,IAAI,CAAC,iBAAiB,CAAC;QAC3B,GAAI,IAAI,CAAE,CAAA;YACN,IAAI,CAAC,iBAAiB,CAAC,UAAU;gBAC7B,OAAO;YACX;QACJ;IACJ;IACA,WAAW;QACP,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,oBAAoB;QAChB,MAAM,sBAAsB,IAAI,CAAC,oBAAoB;QACrD,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC;QAC7B,MAAM,WAAW,+LAAA,CAAA,qBAAkB,GAAG,CAAC,WAAW,AAAC,IAAiC,OAA9B,+LAAA,CAAA,gCAA6B,IAAK,EAAE;QAC1F,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC;YACrC,MAAM;YACN,SAAS;YACT,SAAS;YACT,UAAU;YACV,OAAO;YACP,WAAW;YACX,cAAc,IAAI,CAAC,2BAA2B;YAC9C,eAAe;QACnB;QACA,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,gBAAgB;YACjD,SAAS;YACT,UAAU;QACd;IACJ;IACA,uBAAuB;QACnB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;QACnC,OAAO,eAAe,aAAa;IACvC;IACA,wBAAwB;QACpB,MAAM,aAAa,IAAI,CAAC,UAAU;QAClC,MAAM,mBAAmB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC;QAC/C,OAAO,CAAC,CAAC,oBAAoB,UAAU;IAC3C;IACA,qBAAqB;QACjB,MAAM,mBAAmB,IAAI,CAAC,qBAAqB;QACnD,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,cAAc;YAC3D,IAAI,CAAC,gBAAgB,CAAC,sBAAsB,CAAC,gBAAgB;QACjE;IACJ;IACA,eAAe,QAAQ,EAAE,OAAO,EAAE;QAC9B,YAAY,SAAS,QAAQ,CAAC,+LAAA,CAAA,gBAAa;IAC/C;IACA,gCAAgC,UAAU,EAAE,CAAC;IAC7C,iBAAiB;QACb,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,SAAS;YACX,QAAQ;YACR,SAAS;QACb;QACA,IAAI,CAAC,aAAa,CAAC,mBAAmB;QACtC,IAAI,CAAC,OAAO,MAAM,EAAE;YAChB,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,aAAa,CAAC,kBAAkB;gBACjC,SAAS;YACb;QACJ;IACJ;IACA,sBAAsB;QAClB,MAAM,WAAW,IAAI,CAAC,uBAAuB;QAC7C,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,oBAAoB,CAAC;IAC9B;IACA,qBAAqB,QAAQ,EAAE;QAC3B,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,eAAe,WAAW,CAAC;YACvB,oBAAoB,IAAI,CAAC,MAAM,CAAC;QACpC;IACJ;IACA,iBAAiB,CAAC;IAClB,cAAc;QACV,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,OAAO,UAAU,EAAE;QACf,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,IAAI,kBAAkB,IAAI,CAAC,UAAU,KAAK,eAAe,SAAS,IAAI;YAClE,IAAI,cAAc,YAAY;gBAC1B,IAAI,CAAC,OAAO,CAAC;oBACT,eAAe;gBACnB;YACJ;YACA,IAAI,CAAC,UAAU,GAAG,eAAe,SAAS;QAC9C;QACA,IAAI,CAAC,kBAAkB;IAC3B;IACA,iCAAiC,GAAG,EAAE,cAAc,EAAE;QAClD,OAAO,iBAAiB,EAAE,GAAG;YAAC,IAAI,QAAQ;SAAC;IAC/C;IACA,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW;YACrC,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACrB,SAAS,MAAM,CAAE;gBACb,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;gBACtC,IAAI,SAAS,GAAG;oBACZ,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,OAAO;gBAClC;YACJ;QACJ;IACJ;IACA,eAAe,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;QACjC,IAAI;QACJ,MAAM,UAAU,CAAC;QACjB,MAAM,UAAU,SAAS,CAAC,eAAe,QAAQ,GAAG,KAAK,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,IAAI;QAC7G,MAAM,SAAS,QAAQ,GAAG;QAC1B,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,IAAI,KAAK,MAAM,QAAQ;YACnB,QAAQ,KAAK,GAAG;YAChB,MAAM,qBAAqB,CAAA,GAAA,oLAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,MAAM,CAAC,YAAY,CAAC,SAAS,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,UAAU;YAC9G,mBAAmB,IAAI,CAAE;gBACrB,SAAS,OAAO,CAAC;oBACb,MAAM;oBACN,KAAK;oBACL,SAAS;oBACT,MAAM,+LAAA,CAAA,6BAA0B;gBACpC;YACJ,GAAI,IAAI,CAAC,CAAA,GAAA,yMAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,IAAI,CAAE,CAAA,MAAO,IAAI,CAAC,sBAAsB,CAAC;YAClF,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,MAAM,CAAC,eAAe,EAAE;gBACnD,QAAQ,MAAM,CAAC,eAAe,CAAC,MAAM,GAAG;YAC5C;YACA,IAAI,CAAC,gBAAgB,CAAC;YACtB,IAAI,CAAC,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,iBAAiB,OAAO,EAAE;QACtB,IAAI,QAAQ,MAAM,EAAE;YAChB,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM,WAAW,eAAe,gBAAgB,CAAC,QAAQ,GAAG;YAC5D,MAAM,MAAM,eAAe,cAAc,EAAE,CAAC,SAAS;YACrD,IAAI,KAAK;gBACL,QAAQ,GAAG,CAAC,MAAM,GAAG,IAAI,MAAM;gBAC/B,QAAQ,MAAM,GAAG,IAAI,MAAM;YAC/B;YACA,QAAQ,MAAM,CAAC,QAAQ,WAAW,CAAC,GAAG,QAAQ,KAAK;QACvD;IACJ;IACA,iBAAiB,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,cAAc,EAAE;QACnD,MAAM,SAAS,QAAQ,GAAG;QAC1B,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,IAAI,KAAK,MAAM,QAAQ;YACnB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;QACnC;QACA,IAAI,QAAQ,MAAM,CAAC,YAAY,EAAE;YAC7B,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,MAAM,IAAI,CAAE,CAAA;gBAC5C,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,CAAC,SAAS,QAAQ,iBAAiB,MAAM,CAAE;oBAC7D,SAAS,OAAO;gBACpB;YACJ;QACJ,OAAO;YACH,SAAS,OAAO;QACpB;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,gCAAgC,OAAO,EAAE;QACrC,IAAI,IAAI,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,EAAE,QAAQ,WAAW,GAAG;YAC/E,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,EAAE;YACjC,OAAO;QACX;IACJ;IACA,eAAe,MAAM,EAAE;QACnB,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAChE,IAAI,CAAC,QAAQ;YACT,SAAS,IAAI,CAAC,cAAc;QAChC;QACA,MAAM,uBAAuB,UAAU,OAAO,YAAY,KAAK,OAAO,mBAAmB;QACzF,MAAM,6BAA6B,eAAe,IAAI,CAAE,CAAA,gBAAiB,cAAc,kBAAkB,KAAK,cAAc,yBAAyB;QACrJ,OAAO,wBAAwB;IACnC;IACA,aAAa,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE;QAC1C,MAAM,gBAAgB,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,SAAS;YACxC,gBAAgB;QACpB;QACA,IAAI,CAAC,UAAU,CAAC,QAAQ;QACxB,IAAI,CAAC,kBAAkB;QACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC,SAAS,cAAc,cAAc;IACtE;IACA,iBAAiB,OAAO,EAAE,cAAc,EAAE;QACtC,MAAM,uBAAuB,QAAQ,MAAM,CAAC,YAAY,KAAK,QAAQ,MAAM,CAAC,mBAAmB;QAC/F,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,IAAI,KAAK;YACL,IAAI,kBAAkB,sBAAsB;gBACxC,IAAI,CAAC,cAAc,CAAC,KAAK,gBAAgB;YAC7C,OAAO,IAAI,IAAI,MAAM,EAAE;gBACnB,IAAI,MAAM;YACd;QACJ;IACJ;IACA,mBAAmB,GAAG,EAAE,cAAc,EAAE,oBAAoB,EAAE;QAC1D,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;YAC7B,YAAY;YACZ,YAAY,IAAI,CAAC,gCAAgC,CAAC,KAAK;QAC3D;IACJ;IACA,eAAe,GAAG,EAAE,cAAc,EAAE,oBAAoB,EAAE;QACtD,IAAI,gBAAgB;YAChB,IAAI,CAAC,qBAAqB,CAAC,KAAK,gBAAgB;QACpD,OAAO;YACH,IAAI,CAAC,mBAAmB,CAAC,KAAK;QAClC;IACJ;IACA,sBAAsB,GAAG,EAAE,cAAc,EAAE,oBAAoB,EAAE;QAC7D,IAAI,CAAC,kBAAkB,CAAC,KAAK,CAAC,gBAAgB;QAC9C,IAAI,CAAC,4BAA4B,CAAC,KAAK;QACvC,IAAI,CAAC,gBAAgB;YACjB,IAAI,CAAC,iBAAiB;QAC1B;IACJ;IACA,oBAAoB,GAAG,EAAE,oBAAoB,EAAE;QAC3C,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,IAAI,CAAC,WAAW,CAAC;QACjB,WAAY;YACR,IAAI;YACJ,MAAM,mBAAmB,CAAC,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,OAAO,EAAE,KAAK,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,GAAG,CAAC;YAC3K,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,2JAAA,CAAA,UAAU,CAAC,gBAAgB,CAAC;YACtD,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,iBAAiB,IAAI,QAAQ;YAC7E,IAAI,iBAAiB,gBAAgB,GAAG,CAAC;YACzC,MAAM,iBAAiB,sLAAA,CAAA,UAAa,CAAC,iBAAiB,CAAC;YACvD,IAAI,CAAC,kBAAkB,CAAC,KAAK,OAAO;YACpC,IAAI,CAAC,4BAA4B,CAAC,KAAK;YACvC,IAAI,eAAe,GAAG;gBAClB,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,IAAI,QAAQ,EAAE;gBAClE,IAAI,CAAC,kBAAkB,CAAC,cAAe;oBACnC,WAAY;wBACR,IAAI;wBACJ,iBAAiB,2JAAA,CAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,CAAC,wBAAwB,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,GAAG,CAAC;wBACnL,IAAI,eAAe,cAAc,IAAI,GAAG;4BACpC,sLAAA,CAAA,UAAa,CAAC,iBAAiB,CAAC,gBAAgB;wBACpD;oBACJ;gBACJ;YACJ;YACA,SAAS,OAAO;QACpB;IACJ;IACA,+BAA+B,CAAC;IAChC,WAAW,YAAY,EAAE,OAAO,EAAE;QAC9B,IAAI;QACJ,MAAM,MAAM,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,GAAG;QACzE,MAAM,UAAU;eAAI,IAAI,CAAC,UAAU;SAAG;QACtC,IAAI,QAAQ,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,aAAa,GAAG,EAAE;QAC1D,IAAI,QAAQ,GAAG;YACX,QAAQ,QAAQ,MAAM;YACtB,IAAI,CAAC,gBAAgB,CAAC;gBAClB,KAAK,aAAa,GAAG;gBACrB,SAAS,aAAa,OAAO;YACjC;YACA,OAAO,aAAa,OAAO;YAC3B,QAAQ,IAAI,CAAC;QACjB;QACA,MAAM,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM;QAC1C,IAAI,QAAQ;YACR,IAAI,aAAa,IAAI,EAAE;gBACnB,OAAO,IAAI,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,IAAI,EAAE,aAAa,IAAI;YACxE;YACA,IAAI,CAAC,CAAC,OAAO,IAAI,IAAI,CAAC,aAAa,IAAI,KAAK,aAAa,IAAI,EAAE;gBAC3D,OAAO,IAAI,GAAG,aAAa,IAAI;YACnC;YACA,IAAI,KAAK;gBACL,IAAI,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,GAAG;gBACtC,IAAI,IAAI,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,IAAI,EAAE,aAAa,IAAI;YAClE;QACJ;QACA,OAAO,CAAC,MAAM,GAAG;QACjB,IAAI,CAAC,aAAa,CAAC,+LAAA,CAAA,8BAA2B,EAAE;QAChD,IAAI,WAAW,WAAW,CAAC,SAAS,CAAC,mBAAmB,IAAI,CAAC,UAAU,EAAE,KAAK,KAAK,MAAM,mBAAmB,KAAK,IAAI,gBAAgB,CAAC,MAAM,GAAG;YAC3I,QAAQ,cAAc,GAAG;QAC7B;QACA,OAAO;IACX;IACA,yBAAyB,WAAW,EAAE,MAAM,EAAE;QAC1C,OAAO,OAAO,gBAAgB,IAAI,IAAI,CAAC,yBAAyB;IACpE;IACA,kBAAkB,OAAO,EAAE;QACvB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,MAAM,WAAW,QAAQ,GAAG,IAAI,QAAQ,GAAG,CAAC,QAAQ;QACpD,IAAI;QACJ,MAAM,YAAY,IAAI,CAAC,kBAAkB;QACzC,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC;QACpC,MAAM,gBAAgB,IAAI,CAAC,UAAU,CAAC,UAAU,QAAQ,WAAW;QACnE,IAAI;QACJ,IAAI,CAAC,OAAO,gBAAgB,IAAI,OAAO,YAAY,IAAI,CAAC,gBAAgB,OAAO,YAAY,IAAI,aAAa,CAAC,KAAK,CAAC,WAAW,QAAQ,OAAO,IAAI,qBAAqB,QAAQ,OAAO,KAAK,CAAC,OAAO,OAAO,EAAE;YACvM,MAAM,gBAAgB,IAAI,CAAC,aAAa,CAAC;YACzC,IAAI,CAAC,CAAC,iBAAiB,YAAY,KAAK,OAAO,YAAY,IAAI,aAAa,KAAK,CAAC,gBAAgB,CAAC,SAAS,GAAG;gBAC3G,IAAI,OAAO,gBAAgB,IAAI,CAAC,WAAW;oBACvC,sBAAsB;wBAClB,QAAQ;wBACR,KAAK,QAAQ,GAAG,CAAC,QAAQ,GAAG,KAAK,IAAI,QAAQ,GAAG,CAAC,GAAG;wBACpD,MAAM,QAAQ,GAAG,CAAC,IAAI;wBACtB,QAAQ;oBACZ;oBACA,IAAI,CAAC,eAAe,CAAC;gBACzB;gBACA,IAAI,CAAC,uBAAuB,CAAC,oBAAoB,MAAM,EAAE;oBACrD,QAAQ,QAAQ,GAAG,CAAC,OAAO;wBACvB,IAAI,CAAC,gBAAgB,CAAC,SAAS,OAAO;oBAC1C;gBACJ;YACJ;YACA,WAAW,OAAO,gBAAgB,IAAI,IAAI,CAAC,yBAAyB;QACxE,OAAO,IAAI,aAAa,OAAO,OAAO,IAAI,aAAa,QAAQ,OAAO,IAAI,cAAc;YACpF,WAAW,SAAS,IAAI,IAAI,KAAK,MAAM,IAAI,GAAG,KAAK,IAAI,IAAI,CAAC,mBAAmB,CAAC;QACpF;QACA,OAAO;IACX;IACA,cAAc,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE;QAC/C,IAAI,OAAO,+LAAA,CAAA,kBAAe,CAAC,OAAO,IAAI,CAAC;QACvC,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC;QAC7B,MAAM,gCAAgC,IAAI,CAAC,MAAM,CAAC;QAClD,IAAI,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,OAAO,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,+LAAA,CAAA,aAAU,EAAE,QAAQ,CAAC,OAAO,QAAQ;QACtF,IAAI,OAAO,QAAQ,IAAI,+BAA+B;YAClD,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,OAAO,QAAQ,EAAE,SAAS;QACxE,OAAO;YACH,IAAI,OAAO,QAAQ,EAAE;gBACjB,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ,CAAC,OAAO,QAAQ;YAClD,OAAO,IAAI,YAAY,QAAQ,OAAO,IAAI,EAAE;gBACxC,OAAO,OAAO,IAAI,IAAI;gBACtB,MAAM,WAAW,gLAAA,CAAA,qBAA4B,CAAC;gBAC9C,IAAI,YAAY,YAAY,UAAU,UAAU;oBAC5C,UAAU,gLAAA,CAAA,oBAA2B,CAAC,MAAM,QAAQ,CAAC,OAAO,QAAQ;gBACxE,OAAO;oBACH,QAAQ,QAAQ,CAAC,AAAC,UAAuC,OAA9B,aAAW,WAAS,MAAI,KAAW,OAAL,OAAQ,IAAI,CAAC,SAAS,OAAO,IAAI;gBAC9F;gBACA,QAAQ,QAAQ,CAAC,+LAAA,CAAA,kBAAe;gBAChC,WAAW,QAAQ,CAAC,+LAAA,CAAA,gCAA6B;gBACjD,MAAM,mBAAmB,IAAI,CAAC,0BAA0B,EAAE,CAAC,OAAO,IAAI,CAAC;gBACvE,oBAAoB,QAAQ,IAAI,CAAC,cAAc,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC9E,OAAO;gBACH,QAAQ,IAAI,CAAC,OAAO,IAAI;YAC5B;YACA,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,IAAI,GAAG;gBACxB,QAAQ,IAAI,CAAC,SAAS,OAAO,IAAI;YACrC;YACA,IAAI,IAAI,CAAC,iBAAiB,CAAC,QAAQ,UAAU;gBACzC,QAAQ,QAAQ,CAAC;YACrB,OAAO,IAAI,CAAC,OAAO,QAAQ,IAAI,OAAO,OAAO,EAAE;gBAC3C,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,SAAS,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,SAAS,+LAAA,CAAA,oBAAiB,GAAG,IAAI,CAAC,YAAY,CAAE,CAAA;oBAClF,IAAI;oBACJ,SAAS,CAAC,kBAAkB,OAAO,OAAO,KAAK,KAAK,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,QAAQ,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,GAAG;wBACpH,KAAK,QAAQ,GAAG;wBAChB,QAAQ,QAAQ,MAAM;oBAC1B;oBACA,EAAE,KAAK,CAAC,cAAc;oBACtB,EAAE,KAAK,CAAC,eAAe;gBAC3B;YACJ;YACA,WAAW,MAAM,CAAC;YAClB,IAAI,OAAO,QAAQ,EAAE;gBACjB,QAAQ,WAAW,GAAG;gBACtB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,SAAS,OAAO,QAAQ,EAAE,SAAS,MAAM;YAC3E;QACJ;IACJ;IACA,6BAA6B;QACzB,OAAO;YACH,MAAM;YACN,MAAM;YACN,QAAQ;YACR,UAAU;YACV,QAAQ;QACZ;IACJ;IACA,kBAAkB,WAAW,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE;YACnC;QAArB,MAAM,eAAe,CAAA,eAAA,IAAI,CAAC,MAAM,CAAC,8BAAZ,0BAAA,eAAgC,CAAC;QACtD,MAAM,8BAA8B;YAChC,QAAQ,aAAa,gBAAgB;YACrC,MAAM,aAAa,cAAc;YACjC,QAAQ,aAAa,MAAM;QAC/B;QACA,MAAM,YAAY;YACd,QAAQ;YACR,MAAM;YACN,QAAQ;QACZ,CAAE,CAAC,KAAK;QACR,MAAM,WAAW,2BAA2B,CAAC,KAAK;QAClD,MAAM,mBAAmB,CAAC,WAAW,aAAa,aAAa,SAAS,KAAK,IAAI,CAAC,qBAAqB;QACvG,OAAO;YACH,QAAQ;YACR,SAAS;gBACL,eAAe,CAAA;oBACX,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,OAAO,EAAE,QAAQ,CAAC,YAAY,sBAAsB,CAAC,AAAC,GAAuB,OAArB,+LAAA,CAAA,oBAAiB,EAAC,KAAmC,OAAhC,IAAI,CAAC,eAAe,CAAC,YAAW;gBACrH;gBACA,MAAM,AAAC,eAAwB,OAAV;gBACrB,UAAU;gBACV,SAAS;oBACL,WAAY;wBACR,IAAI,CAAC,WAAW;oBACpB;gBACJ;gBACA,MAAM;gBACN,MAAM;YACV;YACA,UAAU;YACV,MAAM,AAAC,GAAO,OAAL,MAAK;YACd,UAAU;YACV,cAAc;YACd,WAAW;QACf;IACJ;IACA,mBAAmB,WAAW,EAAE;YACL;QAAvB,MAAM,iBAAiB,CAAA,eAAA,IAAI,CAAC,MAAM,CAAC,wBAAZ,0BAAA,eAA0B,CAAC;QAClD,MAAM,cAAc,EAAE;QACtB,IAAI,eAAe,WAAW,EAAE;YAC5B,YAAY,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,UAAU,UAAU;QAC7E;QACA,OAAO;IACX;IACA,kBAAkB,KAAK,EAAE,MAAM,EAAE;QAC7B,IAAI,CAAC,mBAAmB,CAAC,WAAW,MAAM,QAAQ,CAAC,+LAAA,CAAA,gBAAa;IACpE;IACA,gBAAgB,GAAG,EAAE,CAAC;IACtB,oBAAoB,MAAM,EAAE;QACxB,IAAI,UAAU,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,IAAI,GAAG;YAC5D,OAAO;QACX;IACJ;IACA,qBAAqB,CAAC;IACtB,wBAAwB,CAAC;IACzB,iBAAiB,UAAU,EAAE,OAAO,EAAE;QAClC,IAAI,kBAAkB,IAAI,CAAC,MAAM,CAAC,AAAC,WAAqB,OAAX;QAC7C,IAAI,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;YAC7B,kBAAkB,gBAAgB;gBAC9B,WAAW,IAAI,CAAC,SAAS;gBACzB,KAAK,QAAQ,GAAG;YACpB;QACJ;QACA,OAAO;IACX;IACA,cAAc,OAAO,EAAE,SAAS,EAAE;YACN;QAAxB,MAAM,kBAAkB,CAAA,eAAA,IAAI,CAAC,MAAM,CAAC,wCAAZ,0BAAA,eAA0C,+LAAA,CAAA,4BAAyB;QAC3F,MAAM,eAAe,UAAU,MAAM,GAAG,IAAI,oBAAoB,aAAa,WAAW,YAAY;QACpG,OAAO,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;IAClE;IACA,cAAc,OAAO,EAAE;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,iBAAiB;IAClD;IACA,eAAe,UAAU,EAAE;QACvB,IAAI,iBAAiB;QACrB,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG;QACJ,IAAI,gBAAgB,SAAS,cAAc,KAAK,MAAM,cAAc,SAAS,CAAC,kBAAkB,WAAW,GAAG,KAAK,KAAK,MAAM,mBAAmB,SAAS,CAAC,kBAAkB,gBAAgB,cAAc,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,eAAe,CAAC,YAAY;QAClR,IAAI,SAAS,cAAc,KAAK,MAAM,cAAc,SAAS,CAAC,mBAAmB,WAAW,GAAG,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,QAAQ,EAAE;YAC1J,gBAAgB,WAAW,KAAK;QACpC;QACA,OAAO,KAAK,MAAM;IACtB;IACA,qBAAqB;QACjB,MAAM,sBAAsB,IAAI,CAAC,uBAAuB;QACxD,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK;QACvC,OAAO,uBAAuB,IAAI,IAAI,CAAC,oBAAoB,CAAC,QAAQ,GAAG;IAC3E;IACA,sBAAsB,CAAC;IACvB,oBAAoB,UAAU,EAAE;QAC5B,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC;QACzC,OAAO,gBAAgB,WAAW,MAAM,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,WAAW,OAAO,+LAAA,CAAA,gBAAa,IAAI,CAAC,WAAW,GAAG,CAAC,SAAS;IAC/H;AACJ;AACO,MAAM,qCAAqC,CAAA;IAAQ,qBAAc;QACpE,OAAO,IAAI,EAAE,kBAAkB,EAAE;YAC7B,CAAC,sBAAsB,IAAI,CAAC,kBAAkB,CAAC,OAAO;YACtD,OAAO,KAAK,CAAC,OAAO,KAAK,CAAC,IAAI,EAAE;QACpC;QACA,cAAc;YACV,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ,IAAI;gBACpC;YACJ;YACA,OAAO,KAAK,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE;QACzC;QACA,eAAe,KAAK,EAAE;YAClB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B;YAC7D,MAAM,eAAe,sLAAA,CAAA,UAAa,CAAC,aAAa,CAAC,YAAY;YAC7D,MAAM,WAAW,KAAK,CAAC,aAAa;YACpC,IAAI,UAAU;gBACV,IAAI;gBACJ,SAAS,SAAS,GAAG;gBACrB,SAAS,CAAC,wBAAwB,IAAI,CAAC,eAAe,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,IAAI,EAAE;YACpI;QACJ;QACA,iBAAiB,MAAM,EAAE;YACrB,KAAK,CAAC,iBAAiB;YACvB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC;QACnC;QACA,mBAAmB,MAAM,EAAE;YACvB,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK;YAChC,KAAK,CAAC,mBAAmB;QAC7B;QACA,kBAAkB,MAAM,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC,OAAO,KAAK;YAChC,KAAK,CAAC,kBAAkB;QAC5B;QACA,cAAc,KAAK,EAAE,MAAM,EAAE;YACzB,QAAQ,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,OAAO;YACpD,OAAO,KAAK,CAAC,cAAc,OAAO;QACtC;QACA,iBAAiB,QAAQ,EAAE,OAAO,EAAE;YAChC,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,UAAU,SAAS,IAAI,CAAC,kBAAkB;YAClF,OAAO,KAAK,CAAC,iBAAiB,UAAU;QAC5C;QACA,aAAa,IAAI,EAAE,OAAO,EAAE;YACxB,OAAO,KAAK,CAAC,aAAa,MAAM;YAChC,IAAI,KAAK,QAAQ,EAAE;gBACf,QAAQ,SAAS;gBACjB,OAAO,KAAK,SAAS;YACzB;YACA,OAAO;QACX;QACA,yBAAyB,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE;YAC/D,IAAI,QAAQ,QAAQ,KAAK,QAAQ,QAAQ,IAAI,QAAQ,OAAO,KAAK,QAAQ,OAAO,EAAE;gBAC9E;YACJ;YACA,OAAO,KAAK,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE;QACtD;QACA,eAAe,MAAM,EAAE,MAAM,EAAE,eAAe,EAAE,WAAW,EAAE,YAAY,EAAE;YACvE,MAAM,OAAO,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,YAAY;YACtD,MAAM,YAAY,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,iBAAiB;YACjG,IAAI,gBAAgB,WAAW;gBAC3B,OAAO;YACX;YACA,IAAI,QAAQ,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,gBAAgB,IAAI,KAAK,SAAS,KAAK,WAAW;gBACtF,OAAO;YACX;YACA,OAAO,KAAK,CAAC,eAAe,KAAK,CAAC,IAAI,EAAE;QAC5C;QACA,gCAAgC,IAAI,EAAE;YAClC,MAAM,mBAAmB,MAAM,OAAO,CAAC,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,aAAa,IAAI,IAAI,CAAC,kBAAkB,CAAC,QAAQ;YAC3H,OAAO,CAAC;QACZ;QACA,wBAAwB,IAAI,EAAE;YAC1B,MAAM,SAAS,KAAK,CAAC,wBAAwB;YAC7C,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC;YAC5B,MAAM,aAAa,KAAK,KAAK;YAC7B,IAAI,MAAM,OAAO,CAAC,eAAe,QAAQ,MAAM,EAAE;gBAC7C,MAAM,iBAAiB,WAAW,GAAG,CAAE,CAAA,OAAQ,IAAI,CAAC,KAAK,CAAC;gBAC1D,MAAM,aAAa,QAAQ,MAAM,CAAE,CAAA,SAAU,aAAa,OAAO,IAAI,IAAI,eAAe,IAAI,CAAE,CAAA,MAAO,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,OAAO,GAAG,EAAE;gBAC9H,IAAI,WAAW,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACtC,IAAI,CAAC,MAAM,CAAC,mBAAmB;gBACnC;gBACA,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;gBAC/B,MAAM,gBAAgB,WAAW,IAAI,CAAE,CAAA,SAAU,aAAa,OAAO,IAAI,IAAI,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,YAAY,OAAO,GAAG;gBAChH,IAAI,CAAC,iBAAiB,eAAe,KAAK,CAAE,CAAA,MAAO,CAAC,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,YAAY,OAAQ;oBACjF,IAAI,CAAC,MAAM,CAAC,sBAAsB;gBACtC;YACJ;YACA,OAAO;QACX;IACJ;;;AACA,MAAM,WAAW,CAAA;IAAQ,qBAAc;QACnC,aAAa,KAAK,EAAE,QAAQ,EAAE;YAC1B,IAAI,CAAC,MAAM,EAAE,CAAC,SAAS,YAAY,GAAG;gBAClC,MAAM,gBAAgB,IAAI,CAAC,eAAe,CAAC;gBAC3C,IAAI,YAAY,CAAC;gBACjB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,eAAgB,CAAC,OAAO;oBACzB,IAAI,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,aAAa,IAAI,CAAC,OAAO,MAAM,EAAE;wBACnC,YAAY;oBAChB;gBACJ;gBACA,OAAO;YACX;YACA,OAAO,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE;QAC1C;QACA,gBAAgB;YACZ,OAAO,KAAK,CAAC,gBAAgB,MAAM,CAAC;gBAAC;aAAY;QACrD;QACA,iBAAiB,OAAO,EAAE;YACtB,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC;YAC3D,OAAO,YAAY,KAAK,CAAC,iBAAiB;QAC9C;QACA,WAAW,GAAG,EAAE;YACZ,MAAM,OAAO,KAAK,CAAC,WAAW,KAAK,CAAC,IAAI,EAAE;YAC1C,IAAI,KAAK;gBACL,MAAM,eAAe,CAAC,CAAC,IAAI,OAAO;gBAClC,MAAM,gBAAgB,CAAC,CAAC,IAAI,QAAQ;gBACpC,MAAM,gBAAgB,CAAC,CAAC,IAAI,QAAQ;gBACpC,iBAAiB,KAAK,QAAQ,CAAC,+LAAA,CAAA,eAAY;gBAC3C,iBAAiB,KAAK,QAAQ,CAAC,+LAAA,CAAA,eAAY;gBAC3C,IAAI,iBAAiB,cAAc;oBAC/B,KAAK,WAAW,CAAC,+LAAA,CAAA,eAAY;gBACjC;YACJ;YACA,OAAO;QACX;QACA,yBAAyB,QAAQ,EAAE;YAC/B,IAAI,gBAAgB,SAAS,OAAO,CAAC;YACrC,MAAM,iBAAiB,IAAI,CAAC,gBAAgB;YAC5C,MAAO,cAAc,MAAM,IAAI,CAAC,eAAe,MAAM,CAAC,eAAe,MAAM,CAAE;gBACzE,WAAW,cAAc,OAAO,CAAC;gBACjC,gBAAgB,SAAS,OAAO,CAAC;YACrC;YACA,OAAO,IAAI,CAAC,4BAA4B,CAAC;QAC7C;QACA,6BAA6B,QAAQ,EAAE;YACnC,MAAM,iBAAiB,SAAS,OAAO,CAAC,AAAC,IAAa,OAAV,+LAAA,CAAA,YAAS,EAAC;YACtD,OAAO,IAAI,CAAC,YAAY,CAAC;QAC7B;QACA,iBAAiB,CAAC,EAAE,SAAS,EAAE;YAC3B,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,KAAK,CAAC,MAAM;YACvC,MAAM,cAAc,IAAI,CAAC,wBAAwB,CAAC;YAClD,MAAM,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC;YACpD,MAAM,gBAAgB,kBAAkB,aAAa,CAAC;gBAClD,KAAK;YACT,GAAG,cAAc,OAAO,IAAI,QAAQ;YACpC,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,YAAY;YACvE,MAAM,eAAe,kBAAkB,UAAU,CAAC,EAAE,QAAQ,EAAE;YAC9D,MAAM,eAAe,iBAAiB,UAAU,CAAC,OAAO,YAAY,IAAI,YAAY;YACpF,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,8BAA8B;YAClE,MAAM,qBAAqB,UAAU,OAAO,gBAAgB;YAC5D,IAAI,cAAc;gBACd,OAAO;YACX;YACA,IAAI,WAAW,WAAW;gBACtB,IAAI,uJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,GAAG,IAAI,uJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,OAAO,EAAE;oBAC9C,+KAAA,CAAA,UAAQ,CAAC,kBAAkB;gBAC/B;gBACA,OAAO,sBAAsB,gBAAgB,kBAAkB,QAAQ,CAAC,EAAE,QAAQ,EAAE;YACxF;YACA,IAAI,YAAY,aAAa,eAAe,mBAAmB,IAAI,CAAC,kBAAkB,KAAK,eAAe,GAAG,CAAC,IAAI;gBAC9G,MAAM,UAAU;gBAChB,MAAM,sBAAsB,SAAS,OAAO,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,QAAQ;gBAClF,kBAAkB,aAAa,CAAC,SAAS;YAC7C;YACA,IAAI,gBAAgB,cAAc,iBAAiB;gBAC/C,OAAO,kBAAkB,QAAQ,CAAC,EAAE,QAAQ,EAAE,gBAAgB,kBAAkB,SAAS,CAAC,EAAE,QAAQ;YACxG;QACJ;QACA,gBAAgB,CAAC,EAAE;YACf,IAAI,CAAC,kBAAkB,GAAG,EAAE,KAAK,CAAC,MAAM;YACxC,IAAI,CAAC,mBAAmB,GAAG,WAAY;gBACnC,IAAI,CAAC,gBAAgB,CAAC,GAAG;YAC7B;QACJ;QACA,sBAAsB,CAAC,EAAE;YACrB,KAAK,CAAC,UAAU,KAAK,CAAC,IAAI,EAAE;QAChC;QACA,UAAU,CAAC,EAAE;YACT,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,UAAU,EAAE,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,kBAAe;YAChF,EAAE,KAAK,CAAC,+LAAA,CAAA,wBAAqB,CAAC,GAAG,IAAI,CAAC,SAAS;YAC/C,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,YAAY,CAAC,YAAY;gBACnD,KAAK,CAAC,UAAU,KAAK,CAAC,IAAI,EAAE;YAChC;QACJ;QACA,yBAAyB,CAAC,EAAE;YACxB,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE;QACnC;QACA,aAAa,CAAC,EAAE;YACZ,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,GAAG,aAAa;gBACvC,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE;YACnC;QACJ;QACA,cAAc,KAAK,EAAE,UAAU,EAAE;YAC7B,IAAI;YACJ,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,MAAM,gBAAgB,CAAC,CAAC,WAAW,MAAM,CAAC,OAAO;YACjD,MAAM,iBAAiB,WAAW,QAAQ;YAC1C,MAAM,YAAY,kBAAkB,SAAS,CAAC,WAAW,QAAQ;YACjE,MAAM,YAAY,CAAA,GAAA,yMAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YAC3C,IAAI,CAAA,GAAA,yMAAA,CAAA,sCAAmC,AAAD,EAAE,WAAW,aAAa;gBAC5D,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,WAAW,MAAM;gBACrB,MAAM,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,iBAAc,GAAG,CAAC,gBAAgB,WAAW,CAAC,+LAAA,CAAA,4BAAyB,EAAE,CAAC;gBACjH,IAAI,WAAW;oBACX,MAAM,IAAI,CAAC,+LAAA,CAAA,yBAAsB,EAAE,KAAK,GAAG,GAAG,CAAC,aAAa;gBAChE;YACJ;YACA,IAAI,WAAW;gBACX,IAAI,CAAC,iBAAiB,CAAC;YAC3B;YACA,MAAM,cAAc,CAAC,CAAC,CAAC,SAAS,CAAC,qBAAqB,WAAW,MAAM,KAAK,KAAK,MAAM,sBAAsB,mBAAmB,YAAY;YAC5I,IAAI,WAAW,MAAM,IAAI,CAAC,iBAAiB,CAAC,CAAC,eAAe,kBAAkB,mBAAmB,CAAC,WAAW,GAAG;gBAC5G,kBAAkB,iBAAiB,CAAC,OAAO;YAC/C;YACA,KAAK,CAAC,cAAc,KAAK,CAAC,IAAI,EAAE;QACpC;QACA,gBAAgB,OAAO,EAAE;YACrB,MAAM,cAAc,KAAK,CAAC,gBAAgB;YAC1C,MAAM,EACF,aAAa,WAAW,EACxB,KAAK,GAAG,EACX,GAAG;YACJ,YAAY,SAAS,GAAG,IAAI,CAAC,kBAAkB,CAAC,UAAU,CAAC,YAAY,QAAQ,EAAE,YAAY,WAAW;YACxG,YAAY,OAAO,GAAG,IAAI,OAAO;YACjC,IAAI,IAAI,QAAQ,EAAE;gBACd,YAAY,QAAQ,GAAG,KAAK,MAAM,IAAI,cAAc,CAAC,YAAY;YACrE;YACA,OAAO;QACX;QACA,uBAAuB,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE;YAChD,KAAK,CAAC,uBAAuB,OAAO,aAAa;YACjD,IAAI,YAAY,OAAO,EAAE;gBACrB,IAAI,CAAC,OAAO,CAAC,mBAAmB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC,+BAA+B;YAC9F;YACA,IAAI,YAAY,QAAQ,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,mBAAmB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC,gCAAgC;YAC/F;YACA,MAAM,iBAAiB,YAAY,MAAM,CAAC,YAAY,IAAI,CAAC,YAAY,OAAO,IAAI,CAAC,YAAY,QAAQ,IAAI,WAAW,YAAY,OAAO,IAAI,YAAY,MAAM,CAAC,kBAAkB,KAAK,YAAY,MAAM,CAAC,yBAAyB,IAAI,IAAI,CAAC,kBAAkB,CAAC,mBAAmB;YAClR,IAAI,gBAAgB;gBAChB,IAAI,CAAC,OAAO,CAAC,mBAAmB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC,gCAAgC;YAC/F;QACJ;QACA,YAAY,OAAO,EAAE;YACjB,MAAM,QAAQ,KAAK,CAAC,YAAY;YAChC,MAAM,YAAY,IAAI,CAAC,kBAAkB,CAAC,SAAS,CAAC,QAAQ,QAAQ;YACpE,CAAA,GAAA,yMAAA,CAAA,sCAAmC,AAAD,EAAE,WAAW,YAAY,MAAM,QAAQ,CAAC,+LAAA,CAAA,oBAAiB;YAC3F,OAAO;QACX;QACA,UAAU,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,IAAI,EAAE;YAC/C,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC,UAAU;YAClD,IAAI,aAAa;gBACb,IAAI,KAAK,MAAM,OAAO;oBAClB,OAAO,YAAY,KAAK;gBAC5B;gBACA,IAAI,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,aAAa,OAAO,MAAM;YACvE;QACJ;QACA,UAAU;YACN,KAAK,CAAC,QAAQ,KAAK,CAAC,IAAI,EAAE;YAC1B,aAAa,IAAI,CAAC,mBAAmB;QACzC;QACA,cAAc;YACV,KAAK,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE;YAC9B,OAAO,IAAI,CAAC,kBAAkB,CAAC,MAAM,IAAI,CAAE;gBACvC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;YAC9C;QACJ;QACA,oBAAoB,CAAC;QACrB,oBAAoB,CAAC;IACzB;;;AACA,MAAM,cAAc,CAAA;IAAQ,qBAAc;QACtC,cAAc,IAAI,EAAE;YAChB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;YACJ,OAAQ,KAAK,IAAI;gBACb,KAAK;oBAAW;wBACZ,MAAM,kBAAkB;4BAAC,+LAAA,CAAA,4BAAyB;4BAAE,+LAAA,CAAA,8BAA2B;4BAAE,+LAAA,CAAA,qCAAkC;4BAAE,+LAAA,CAAA,iCAA8B;yBAAC;wBACpJ,MAAM,mBAAmB,YAAY,CAAC,gBAAgB,IAAI,CAAE,CAAA,aAAc,eAAe;wBACzF,oBAAoB,IAAI,CAAC,WAAW;wBACpC,KAAK,CAAC,cAAc;wBACpB;oBACJ;gBACA,KAAK;oBACD,KAAK,OAAO,GAAG;oBACf;gBACJ;oBACI,KAAK,CAAC,cAAc;YAC5B;QACJ;QACA,mBAAmB;YACf,MAAM,QAAQ,KAAK,CAAC;YACpB,MAAM,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,CAAC,IAAI;YACvE,OAAO,gBAAgB,MAAM,CAAC;QAClC;IACJ;;;AACO,MAAM,gBAAgB;IACzB,gBAAgB,IAAM,CAAC;YACnB,SAAS;gBACL,MAAM;gBACN,aAAa;gBACb,gBAAgB,+LAAA,CAAA,gCAA6B;gBAC7C,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,UAAU;gBACV,uBAAuB;gBACvB,eAAe;gBACf,OAAO;oBACH,SAAS,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBACpC,gBAAgB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBAC3C,gBAAgB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBAC3C,kBAAkB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBAC7C,kBAAkB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBAC7C,QAAQ,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBACnC,WAAW,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBACtC,aAAa,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBACxC,sBAAsB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;oBACjD,oBAAoB;gBACxB;gBACA,MAAM;oBACF,UAAU;gBACd;gBACA,OAAO,CAAC;gBACR,iBAAiB;gBACjB,YAAY;gBACZ,gBAAgB;gBAChB,SAAS,EAAE;YACf;YACA,+BAA+B;QACnC,CAAC;IACD,aAAa;QACT,SAAS;IACb;IACA,WAAW;QACP,aAAa;YACT,MAAM;QACV;QACA,OAAO;YACH,UAAU;YACV,aAAa;QACjB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2656, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/editing/m_editing_row_based.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/editing/m_editing_row_based.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    equalByValue\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    EDIT_FORM_CLASS,\r\n    EDIT_MODE_ROW,\r\n    EDIT_ROW,\r\n    EDITING_EDITROWKEY_OPTION_NAME,\r\n    MODES_WITH_DELAYED_FOCUS,\r\n    ROW_SELECTED_CLASS\r\n} from \"./const\";\r\nconst editingControllerExtender = Base => class extends Base {\r\n    isRowEditMode() {\r\n        return this.getEditMode() === EDIT_MODE_ROW\r\n    }\r\n    _afterCancelEditData(rowIndex) {\r\n        const dataController = this._dataController;\r\n        if (this.isRowBasedEditMode() && rowIndex >= 0) {\r\n            dataController.updateItems({\r\n                changeType: \"update\",\r\n                rowIndices: [rowIndex, rowIndex + 1]\r\n            })\r\n        } else {\r\n            super._afterCancelEditData(rowIndex)\r\n        }\r\n    }\r\n    _isDefaultButtonVisible(button, options) {\r\n        const isRowMode = this.isRowBasedEditMode();\r\n        const isPopupEditMode = this.isPopupEditMode();\r\n        const isEditRow = !isPopupEditMode && options.row && equalByValue(options.row.key, this.option(EDITING_EDITROWKEY_OPTION_NAME));\r\n        if (isRowMode) {\r\n            switch (button.name) {\r\n                case \"edit\":\r\n                    return !isEditRow && this.allowUpdating(options);\r\n                case \"delete\":\r\n                    return super._isDefaultButtonVisible(button, options) && !isEditRow;\r\n                case \"save\":\r\n                case \"cancel\":\r\n                    return isEditRow;\r\n                default:\r\n                    return super._isDefaultButtonVisible(button, options)\r\n            }\r\n        }\r\n        return super._isDefaultButtonVisible(button, options)\r\n    }\r\n    isEditRow(rowIndex) {\r\n        return this.isRowBasedEditMode() && this.isEditRowByIndex(rowIndex)\r\n    }\r\n    _cancelSaving(result) {\r\n        if (this.isRowBasedEditMode()) {\r\n            if (!this.hasChanges()) {\r\n                this._cancelEditDataCore()\r\n            }\r\n        }\r\n        super._cancelSaving(result)\r\n    }\r\n    _refreshCore(params) {\r\n        const {\r\n            allowCancelEditing: allowCancelEditing\r\n        } = params ?? {};\r\n        if (this.isRowBasedEditMode()) {\r\n            const hasUpdateChanges = this.getChanges().filter((it => \"update\" === it.type)).length > 0;\r\n            this.init();\r\n            allowCancelEditing && hasUpdateChanges && this._cancelEditDataCore()\r\n        }\r\n        super._refreshCore(params)\r\n    }\r\n    _isEditColumnVisible() {\r\n        const result = super._isEditColumnVisible();\r\n        const editingOptions = this.option(\"editing\");\r\n        const isRowEditMode = this.isRowEditMode();\r\n        const isVisibleInRowEditMode = editingOptions.allowUpdating || editingOptions.allowAdding;\r\n        return result || isRowEditMode && isVisibleInRowEditMode\r\n    }\r\n    _focusEditorIfNeed() {\r\n        const editMode = this.getEditMode();\r\n        if (this._needFocusEditor) {\r\n            if (MODES_WITH_DELAYED_FOCUS.includes(editMode)) {\r\n                const $editingCell = this.getFocusedCellInRow(this._getVisibleEditRowIndex());\r\n                this._delayedInputFocus($editingCell, (() => {\r\n                    $editingCell && this.component.focus($editingCell)\r\n                }))\r\n            }\r\n            this._needFocusEditor = false\r\n        }\r\n    }\r\n};\r\nconst data = Base => class extends Base {\r\n    _getChangedColumnIndices(oldItem, newItem, rowIndex, isLiveUpdate) {\r\n        if (this._editingController.isRowBasedEditMode() && oldItem.isEditing !== newItem.isEditing) {\r\n            return\r\n        }\r\n        return super._getChangedColumnIndices.apply(this, arguments)\r\n    }\r\n};\r\nconst rowsView = Base => class extends Base {\r\n    _createRow(row) {\r\n        const $row = super._createRow.apply(this, arguments);\r\n        if (row) {\r\n            const editingController = this._editingController;\r\n            const isEditRow = editingController.isEditRow(row.rowIndex);\r\n            if (isEditRow) {\r\n                $row.addClass(EDIT_ROW);\r\n                $row.removeClass(ROW_SELECTED_CLASS);\r\n                if (\"detail\" === row.rowType) {\r\n                    $row.addClass(this.addWidgetPrefix(EDIT_FORM_CLASS))\r\n                }\r\n            }\r\n        }\r\n        return $row\r\n    }\r\n    _update(change) {\r\n        super._update(change);\r\n        if (\"updateSelection\" === change.changeType) {\r\n            this.getTableElements().children(\"tbody\").children(`.${EDIT_ROW}`).removeClass(ROW_SELECTED_CLASS)\r\n        }\r\n    }\r\n};\r\nexport const editingRowBasedModule = {\r\n    extenders: {\r\n        controllers: {\r\n            editing: editingControllerExtender,\r\n            data: data\r\n        },\r\n        views: {\r\n            rowsView: rowsView\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;;;AAQA,MAAM,4BAA4B,CAAA;IAAQ,qBAAc;QACpD,gBAAgB;YACZ,OAAO,IAAI,CAAC,WAAW,OAAO,+LAAA,CAAA,gBAAa;QAC/C;QACA,qBAAqB,QAAQ,EAAE;YAC3B,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,IAAI,IAAI,CAAC,kBAAkB,MAAM,YAAY,GAAG;gBAC5C,eAAe,WAAW,CAAC;oBACvB,YAAY;oBACZ,YAAY;wBAAC;wBAAU,WAAW;qBAAE;gBACxC;YACJ,OAAO;gBACH,KAAK,CAAC,qBAAqB;YAC/B;QACJ;QACA,wBAAwB,MAAM,EAAE,OAAO,EAAE;YACrC,MAAM,YAAY,IAAI,CAAC,kBAAkB;YACzC,MAAM,kBAAkB,IAAI,CAAC,eAAe;YAC5C,MAAM,YAAY,CAAC,mBAAmB,QAAQ,GAAG,IAAI,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,GAAG,CAAC,GAAG,EAAE,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B;YAC7H,IAAI,WAAW;gBACX,OAAQ,OAAO,IAAI;oBACf,KAAK;wBACD,OAAO,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC;oBAC5C,KAAK;wBACD,OAAO,KAAK,CAAC,wBAAwB,QAAQ,YAAY,CAAC;oBAC9D,KAAK;oBACL,KAAK;wBACD,OAAO;oBACX;wBACI,OAAO,KAAK,CAAC,wBAAwB,QAAQ;gBACrD;YACJ;YACA,OAAO,KAAK,CAAC,wBAAwB,QAAQ;QACjD;QACA,UAAU,QAAQ,EAAE;YAChB,OAAO,IAAI,CAAC,kBAAkB,MAAM,IAAI,CAAC,gBAAgB,CAAC;QAC9D;QACA,cAAc,MAAM,EAAE;YAClB,IAAI,IAAI,CAAC,kBAAkB,IAAI;gBAC3B,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;oBACpB,IAAI,CAAC,mBAAmB;gBAC5B;YACJ;YACA,KAAK,CAAC,cAAc;QACxB;QACA,aAAa,MAAM,EAAE;YACjB,MAAM,EACF,oBAAoB,kBAAkB,EACzC,GAAG,mBAAA,oBAAA,SAAU,CAAC;YACf,IAAI,IAAI,CAAC,kBAAkB,IAAI;gBAC3B,MAAM,mBAAmB,IAAI,CAAC,UAAU,GAAG,MAAM,CAAE,CAAA,KAAM,aAAa,GAAG,IAAI,EAAG,MAAM,GAAG;gBACzF,IAAI,CAAC,IAAI;gBACT,sBAAsB,oBAAoB,IAAI,CAAC,mBAAmB;YACtE;YACA,KAAK,CAAC,aAAa;QACvB;QACA,uBAAuB;YACnB,MAAM,SAAS,KAAK,CAAC;YACrB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;YACnC,MAAM,gBAAgB,IAAI,CAAC,aAAa;YACxC,MAAM,yBAAyB,eAAe,aAAa,IAAI,eAAe,WAAW;YACzF,OAAO,UAAU,iBAAiB;QACtC;QACA,qBAAqB;YACjB,MAAM,WAAW,IAAI,CAAC,WAAW;YACjC,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,IAAI,+LAAA,CAAA,2BAAwB,CAAC,QAAQ,CAAC,WAAW;oBAC7C,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,uBAAuB;oBAC1E,IAAI,CAAC,kBAAkB,CAAC,cAAe;wBACnC,gBAAgB,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;oBACzC;gBACJ;gBACA,IAAI,CAAC,gBAAgB,GAAG;YAC5B;QACJ;IACJ;;;AACA,MAAM,OAAO,CAAA;IAAQ,qBAAc;QAC/B,yBAAyB,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE;YAC/D,IAAI,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,MAAM,QAAQ,SAAS,KAAK,QAAQ,SAAS,EAAE;gBACzF;YACJ;YACA,OAAO,KAAK,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE;QACtD;IACJ;;;AACA,MAAM,WAAW,CAAA;IAAQ,qBAAc;QACnC,WAAW,GAAG,EAAE;YACZ,MAAM,OAAO,KAAK,CAAC,WAAW,KAAK,CAAC,IAAI,EAAE;YAC1C,IAAI,KAAK;gBACL,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;gBACjD,MAAM,YAAY,kBAAkB,SAAS,CAAC,IAAI,QAAQ;gBAC1D,IAAI,WAAW;oBACX,KAAK,QAAQ,CAAC,+LAAA,CAAA,WAAQ;oBACtB,KAAK,WAAW,CAAC,+LAAA,CAAA,qBAAkB;oBACnC,IAAI,aAAa,IAAI,OAAO,EAAE;wBAC1B,KAAK,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,kBAAe;oBACtD;gBACJ;YACJ;YACA,OAAO;QACX;QACA,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC,QAAQ;YACd,IAAI,sBAAsB,OAAO,UAAU,EAAE;gBACzC,IAAI,CAAC,gBAAgB,GAAG,QAAQ,CAAC,SAAS,QAAQ,CAAC,AAAC,IAAY,OAAT,+LAAA,CAAA,WAAQ,GAAI,WAAW,CAAC,+LAAA,CAAA,qBAAkB;YACrG;QACJ;IACJ;;;AACO,MAAM,wBAAwB;IACjC,WAAW;QACP,aAAa;YACT,SAAS;YACT,MAAM;QACV;QACA,OAAO;YACH,UAAU;QACd;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2803, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/editing/m_editing_form_based.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/editing/m_editing_form_based.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport eventsEngine from \"../../../../common/core/events/core/events_engine\";\r\nimport {\r\n    removeEvent\r\n} from \"../../../../common/core/events/remove\";\r\nimport devices from \"../../../../core/devices\";\r\nimport Guid from \"../../../../core/guid\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    equalByValue\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    Deferred\r\n} from \"../../../../core/utils/deferred\";\r\nimport {\r\n    isElementInDom\r\n} from \"../../../../core/utils/dom\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../../core/utils/iterator\";\r\nimport {\r\n    isDefined,\r\n    isString\r\n} from \"../../../../core/utils/type\";\r\nimport Button from \"../../../../ui/button\";\r\nimport Form from \"../../../../ui/form\";\r\nimport Popup from \"../../../../ui/popup/ui.popup\";\r\nimport Scrollable from \"../../../../ui/scroll_view/ui.scrollable\";\r\nimport gridCoreUtils from \"../m_utils\";\r\nimport {\r\n    BUTTON_CLASS,\r\n    DATA_EDIT_DATA_INSERT_TYPE,\r\n    EDIT_FORM_ITEM_CLASS,\r\n    EDIT_MODE_FORM,\r\n    EDIT_POPUP_CLASS,\r\n    EDIT_POPUP_FORM_CLASS,\r\n    EDITING_EDITROWKEY_OPTION_NAME,\r\n    EDITING_FORM_OPTION_NAME,\r\n    EDITING_POPUP_OPTION_NAME,\r\n    FOCUSABLE_ELEMENT_CLASS,\r\n    FOCUSABLE_ELEMENT_SELECTOR,\r\n    FORM_BUTTONS_CONTAINER_CLASS\r\n} from \"./const\";\r\nimport {\r\n    forEachFormItems,\r\n    getEditorType\r\n} from \"./m_editing_utils\";\r\nconst editingControllerExtender = Base => class extends Base {\r\n    init() {\r\n        this._editForm = null;\r\n        this._updateEditFormDeferred = null;\r\n        super.init()\r\n    }\r\n    isEditRow(rowIndex) {\r\n        return !this.isPopupEditMode() && super.isEditRow(rowIndex)\r\n    }\r\n    isFormOrPopupEditMode() {\r\n        return this.isPopupEditMode() || this.isFormEditMode()\r\n    }\r\n    isFormEditMode() {\r\n        const editMode = this.option(\"editing.mode\");\r\n        return editMode === EDIT_MODE_FORM\r\n    }\r\n    getFirstEditableColumnIndex() {\r\n        const firstFormItem = this._firstFormItem;\r\n        if (this.isFormEditMode() && firstFormItem) {\r\n            const editRowKey = this.option(EDITING_EDITROWKEY_OPTION_NAME);\r\n            const editRowIndex = this._dataController.getRowIndexByKey(editRowKey);\r\n            const $editFormElements = this._rowsView.getCellElements(editRowIndex);\r\n            return this._rowsView._getEditFormEditorVisibleIndex($editFormElements, firstFormItem.column)\r\n        }\r\n        return super.getFirstEditableColumnIndex()\r\n    }\r\n    getEditFormRowIndex() {\r\n        return this.isFormOrPopupEditMode() ? this._getVisibleEditRowIndex() : super.getEditFormRowIndex()\r\n    }\r\n    _isEditColumnVisible() {\r\n        const result = super._isEditColumnVisible();\r\n        const editingOptions = this.option(\"editing\");\r\n        return this.isFormOrPopupEditMode() ? editingOptions.allowUpdating || result : result\r\n    }\r\n    _handleDataChanged(args) {\r\n        if (this.isPopupEditMode()) {\r\n            var _args$items, _args$changeTypes;\r\n            const editRowKey = this.option(\"editing.editRowKey\");\r\n            const hasEditRow = null === args || void 0 === args || null === (_args$items = args.items) || void 0 === _args$items ? void 0 : _args$items.some((item => equalByValue(item.key, editRowKey)));\r\n            const onlyInsertChanges = (null === (_args$changeTypes = args.changeTypes) || void 0 === _args$changeTypes ? void 0 : _args$changeTypes.length) && args.changeTypes.every((item => \"insert\" === item));\r\n            if ((\"refresh\" === args.changeType || hasEditRow && args.isOptionChanged) && !onlyInsertChanges) {\r\n                this._repaintEditPopup()\r\n            }\r\n        }\r\n        super._handleDataChanged(args)\r\n    }\r\n    getPopupContent() {\r\n        var _this$_editPopup;\r\n        const popupVisible = null === (_this$_editPopup = this._editPopup) || void 0 === _this$_editPopup ? void 0 : _this$_editPopup.option(\"visible\");\r\n        if (this.isPopupEditMode() && popupVisible) {\r\n            return this._$popupContent\r\n        }\r\n    }\r\n    _showAddedRow(rowIndex) {\r\n        if (this.isPopupEditMode()) {\r\n            this._showEditPopup(rowIndex)\r\n        } else {\r\n            super._showAddedRow(rowIndex)\r\n        }\r\n    }\r\n    _cancelEditDataCore() {\r\n        super._cancelEditDataCore();\r\n        if (this.isPopupEditMode()) {\r\n            this._hideEditPopup()\r\n        }\r\n    }\r\n    _updateEditRowCore(row, skipCurrentRow, isCustomSetCellValue) {\r\n        const editForm = this._editForm;\r\n        if (this.isPopupEditMode()) {\r\n            if (this.option(\"repaintChangesOnly\")) {\r\n                var _row$update;\r\n                null === (_row$update = row.update) || void 0 === _row$update || _row$update.call(row, row);\r\n                this._rowsView.renderDelayedTemplates()\r\n            } else if (editForm) {\r\n                this._updateEditFormDeferred = (new Deferred).done((() => editForm.repaint()));\r\n                if (!this._updateLockCount) {\r\n                    this._updateEditFormDeferred.resolve()\r\n                }\r\n            }\r\n        } else {\r\n            super._updateEditRowCore(row, skipCurrentRow, isCustomSetCellValue)\r\n        }\r\n    }\r\n    _showEditPopup(rowIndex, repaintForm) {\r\n        const isMobileDevice = \"desktop\" !== devices.current().deviceType;\r\n        const editPopupClass = this.addWidgetPrefix(EDIT_POPUP_CLASS);\r\n        const popupOptions = extend({\r\n            showTitle: false,\r\n            fullScreen: isMobileDevice,\r\n            wrapperAttr: {\r\n                class: editPopupClass\r\n            },\r\n            toolbarItems: [{\r\n                toolbar: \"bottom\",\r\n                location: \"after\",\r\n                widget: \"dxButton\",\r\n                options: this._getSaveButtonConfig()\r\n            }, {\r\n                toolbar: \"bottom\",\r\n                location: \"after\",\r\n                widget: \"dxButton\",\r\n                options: this._getCancelButtonConfig()\r\n            }],\r\n            contentTemplate: this._getPopupEditFormTemplate(rowIndex)\r\n        }, this.option(EDITING_POPUP_OPTION_NAME));\r\n        if (!this._editPopup) {\r\n            const $popupContainer = $(\"<div>\").appendTo(this.component.$element()).addClass(editPopupClass);\r\n            this._editPopup = this._createComponent($popupContainer, Popup);\r\n            this._editPopup.on(\"hiding\", this._getEditPopupHiddenHandler());\r\n            this._editPopup.on(\"shown\", (e => {\r\n                eventsEngine.trigger(e.component.$content().find(FOCUSABLE_ELEMENT_SELECTOR).not(`.${FOCUSABLE_ELEMENT_CLASS}`).first(), \"focus\");\r\n                if (repaintForm) {\r\n                    var _this$_editForm;\r\n                    null === (_this$_editForm = this._editForm) || void 0 === _this$_editForm || _this$_editForm.repaint()\r\n                }\r\n            }))\r\n        }\r\n        this._editPopup.option(popupOptions);\r\n        this._editPopup.show();\r\n        super._showEditPopup(rowIndex, repaintForm)\r\n    }\r\n    _getPopupEditFormTemplate(rowIndex) {\r\n        const row = this.component.getVisibleRows()[rowIndex];\r\n        const templateOptions = {\r\n            row: row,\r\n            values: row.values,\r\n            rowType: row.rowType,\r\n            key: row.key,\r\n            rowIndex: rowIndex\r\n        };\r\n        this._rowsView._addWatchMethod(templateOptions, row);\r\n        return container => {\r\n            const formTemplate = this.getEditFormTemplate();\r\n            const scrollable = this._createComponent($(\"<div>\").appendTo(container), Scrollable);\r\n            this._$popupContent = $(scrollable.content());\r\n            formTemplate(this._$popupContent, templateOptions, {\r\n                isPopupForm: true\r\n            });\r\n            this._rowsView.renderDelayedTemplates();\r\n            $(container).parent().attr(\"aria-label\", this.localize(\"dxDataGrid-ariaEditForm\"))\r\n        }\r\n    }\r\n    _repaintEditPopup() {\r\n        const rowIndex = this._getVisibleEditRowIndex();\r\n        if (rowIndex >= 0) {\r\n            var _this$_editPopup2, _this$_editPopup3;\r\n            const defaultAnimation = null === (_this$_editPopup2 = this._editPopup) || void 0 === _this$_editPopup2 ? void 0 : _this$_editPopup2.option(\"animation\");\r\n            null === (_this$_editPopup3 = this._editPopup) || void 0 === _this$_editPopup3 || _this$_editPopup3.option(\"animation\", null);\r\n            this._showEditPopup(rowIndex, true);\r\n            if (void 0 !== defaultAnimation) {\r\n                this._editPopup.option(\"animation\", defaultAnimation)\r\n            }\r\n        }\r\n    }\r\n    _hideEditPopup() {\r\n        var _this$_editPopup4;\r\n        null === (_this$_editPopup4 = this._editPopup) || void 0 === _this$_editPopup4 || _this$_editPopup4.option(\"visible\", false)\r\n    }\r\n    optionChanged(args) {\r\n        if (\"editing\" === args.name && this.isFormOrPopupEditMode()) {\r\n            const {\r\n                fullName: fullName\r\n            } = args;\r\n            if (0 === fullName.indexOf(EDITING_FORM_OPTION_NAME)) {\r\n                this._handleFormOptionChange(args);\r\n                args.handled = true\r\n            } else if (0 === fullName.indexOf(EDITING_POPUP_OPTION_NAME)) {\r\n                this._handlePopupOptionChange(args);\r\n                args.handled = true\r\n            }\r\n        }\r\n        super.optionChanged(args)\r\n    }\r\n    _handleFormOptionChange(args) {\r\n        var _this$_editPopup5;\r\n        if (this.isFormEditMode()) {\r\n            const editRowIndex = this._getVisibleEditRowIndex();\r\n            if (editRowIndex >= 0) {\r\n                this._dataController.updateItems({\r\n                    changeType: \"update\",\r\n                    rowIndices: [editRowIndex]\r\n                })\r\n            }\r\n        } else if (null !== (_this$_editPopup5 = this._editPopup) && void 0 !== _this$_editPopup5 && _this$_editPopup5.option(\"visible\") && 0 === args.fullName.indexOf(EDITING_FORM_OPTION_NAME)) {\r\n            this._repaintEditPopup()\r\n        }\r\n    }\r\n    _handlePopupOptionChange(args) {\r\n        const editPopup = this._editPopup;\r\n        if (editPopup) {\r\n            const popupOptionName = args.fullName.slice(EDITING_POPUP_OPTION_NAME.length + 1);\r\n            if (popupOptionName) {\r\n                editPopup.option(popupOptionName, args.value)\r\n            } else {\r\n                editPopup.option(args.value)\r\n            }\r\n        }\r\n    }\r\n    renderFormEditorTemplate(detailCellOptions, item, formTemplateOptions, container, isReadOnly) {\r\n        const that = this;\r\n        const $container = $(container);\r\n        const {\r\n            column: column\r\n        } = item;\r\n        const editorType = getEditorType(item);\r\n        const row = null === detailCellOptions || void 0 === detailCellOptions ? void 0 : detailCellOptions.row;\r\n        const rowData = null === row || void 0 === row ? void 0 : row.data;\r\n        const form = formTemplateOptions.component;\r\n        const value = column.calculateCellValue(rowData);\r\n        const displayValue = gridCoreUtils.getDisplayValue(column, value, rowData, null === row || void 0 === row ? void 0 : row.rowType);\r\n        const {\r\n            label: label,\r\n            labelMark: labelMark,\r\n            labelMode: labelMode\r\n        } = formTemplateOptions.editorOptions || {};\r\n        const cellOptions = extend({}, detailCellOptions, {\r\n            data: rowData,\r\n            cellElement: null,\r\n            isOnForm: true,\r\n            item: item,\r\n            id: form.getItemID(item.name || item.dataField),\r\n            column: extend({}, column, {\r\n                editorType: editorType,\r\n                editorOptions: extend({\r\n                    label: label,\r\n                    labelMark: labelMark,\r\n                    labelMode: labelMode\r\n                }, column.editorOptions, item.editorOptions)\r\n            }),\r\n            columnIndex: column.index,\r\n            setValue: !isReadOnly && column.allowEditing && function(value, text) {\r\n                that.updateFieldValue(cellOptions, value, text)\r\n            }\r\n        });\r\n        cellOptions.value = value;\r\n        cellOptions.displayValue = displayValue;\r\n        cellOptions.text = !column.command ? gridCoreUtils.formatValue(displayValue, column) : \"\";\r\n        const template = this._getFormEditItemTemplate.bind(this)(cellOptions, column);\r\n        this._rowsView.renderTemplate($container, template, cellOptions, !!isElementInDom($container)).done((() => {\r\n            this._rowsView._updateCell($container, cellOptions)\r\n        }));\r\n        return cellOptions\r\n    }\r\n    getFormEditorTemplate(cellOptions, item) {\r\n        const column = this.component.columnOption(item.name || item.dataField);\r\n        return (options, container) => {\r\n            const $container = $(container);\r\n            const {\r\n                row: row\r\n            } = cellOptions;\r\n            if (null !== row && void 0 !== row && row.watch) {\r\n                const dispose = row.watch((() => column.selector(row.data)), (() => {\r\n                    var _validator;\r\n                    let $editorElement = $container.find(\".dx-widget\").first();\r\n                    let validator = $editorElement.data(\"dxValidator\");\r\n                    const validatorOptions = null === (_validator = validator) || void 0 === _validator ? void 0 : _validator.option();\r\n                    $container.contents().remove();\r\n                    cellOptions = this.renderFormEditorTemplate.bind(this)(cellOptions, item, options, $container);\r\n                    $editorElement = $container.find(\".dx-widget\").first();\r\n                    validator = $editorElement.data(\"dxValidator\");\r\n                    if (validatorOptions && !validator) {\r\n                        $editorElement.dxValidator({\r\n                            validationRules: validatorOptions.validationRules,\r\n                            validationGroup: validatorOptions.validationGroup,\r\n                            dataGetter: validatorOptions.dataGetter\r\n                        })\r\n                    }\r\n                }));\r\n                eventsEngine.on($container, removeEvent, dispose)\r\n            }\r\n            cellOptions = this.renderFormEditorTemplate.bind(this)(cellOptions, item, options, $container)\r\n        }\r\n    }\r\n    getEditFormOptions(detailOptions) {\r\n        var _this$_getValidationG;\r\n        const editFormOptions = null === (_this$_getValidationG = this._getValidationGroupsInForm) || void 0 === _this$_getValidationG ? void 0 : _this$_getValidationG.call(this, detailOptions);\r\n        const userCustomizeItem = this.option(\"editing.form.customizeItem\");\r\n        const editFormItemClass = this.addWidgetPrefix(EDIT_FORM_ITEM_CLASS);\r\n        let items = this.option(\"editing.form.items\");\r\n        const isCustomEditorType = {};\r\n        if (!items) {\r\n            const columns = this._columnsController.getColumns();\r\n            items = [];\r\n            each(columns, ((_, column) => {\r\n                if (!column.isBand && !column.type) {\r\n                    items.push({\r\n                        column: column,\r\n                        name: column.name,\r\n                        dataField: column.dataField\r\n                    })\r\n                }\r\n            }))\r\n        } else {\r\n            forEachFormItems(items, (item => {\r\n                const itemId = (null === item || void 0 === item ? void 0 : item.name) || (null === item || void 0 === item ? void 0 : item.dataField);\r\n                if (itemId) {\r\n                    isCustomEditorType[itemId] = !!item.editorType\r\n                }\r\n            }))\r\n        }\r\n        return extend({}, editFormOptions, {\r\n            items: items,\r\n            formID: `dx-${new Guid}`,\r\n            customizeItem: item => {\r\n                let column;\r\n                const itemId = item.name || item.dataField;\r\n                if (item.column || itemId) {\r\n                    column = item.column || this._columnsController.columnOption(item.name ? `name:${item.name}` : `dataField:${item.dataField}`)\r\n                }\r\n                if (column) {\r\n                    item.label = item.label || {};\r\n                    item.label.text = item.label.text || column.caption;\r\n                    if (\"boolean\" === column.dataType && void 0 === item.label.visible) {\r\n                        const labelMode = this.option(\"editing.form.labelMode\");\r\n                        if (\"floating\" === labelMode || \"static\" === labelMode) {\r\n                            item.label.visible = true\r\n                        }\r\n                    }\r\n                    item.template = item.template || this.getFormEditorTemplate(detailOptions, item);\r\n                    item.column = column;\r\n                    item.isCustomEditorType = isCustomEditorType[itemId];\r\n                    if (column.formItem) {\r\n                        extend(item, column.formItem)\r\n                    }\r\n                    if (void 0 === item.isRequired && column.validationRules) {\r\n                        item.isRequired = column.validationRules.some((rule => \"required\" === rule.type));\r\n                        item.validationRules = []\r\n                    }\r\n                    const itemVisible = isDefined(item.visible) ? item.visible : true;\r\n                    if (!this._firstFormItem && itemVisible) {\r\n                        this._firstFormItem = item\r\n                    }\r\n                }\r\n                null === userCustomizeItem || void 0 === userCustomizeItem || userCustomizeItem.call(this, item);\r\n                item.cssClass = isString(item.cssClass) ? `${item.cssClass} ${editFormItemClass}` : editFormItemClass\r\n            }\r\n        })\r\n    }\r\n    getEditFormTemplate() {\r\n        return ($container, detailOptions, options) => {\r\n            const editFormOptions = this.option(EDITING_FORM_OPTION_NAME);\r\n            const baseEditFormOptions = this.getEditFormOptions(detailOptions);\r\n            const $formContainer = $(\"<div>\").appendTo($container);\r\n            const isPopupForm = null === options || void 0 === options ? void 0 : options.isPopupForm;\r\n            this._firstFormItem = void 0;\r\n            if (isPopupForm) {\r\n                $formContainer.addClass(this.addWidgetPrefix(EDIT_POPUP_FORM_CLASS))\r\n            }\r\n            this._editForm = this._createComponent($formContainer, Form, extend({}, editFormOptions, baseEditFormOptions));\r\n            if (!isPopupForm) {\r\n                const $buttonsContainer = $(\"<div>\").addClass(this.addWidgetPrefix(FORM_BUTTONS_CONTAINER_CLASS)).appendTo($container);\r\n                this._createComponent($(\"<div>\").appendTo($buttonsContainer), Button, this._getSaveButtonConfig());\r\n                this._createComponent($(\"<div>\").appendTo($buttonsContainer), Button, this._getCancelButtonConfig())\r\n            }\r\n            this._editForm.on(\"contentReady\", (() => {\r\n                var _this$_editPopup6;\r\n                this._rowsView.renderDelayedTemplates();\r\n                null === (_this$_editPopup6 = this._editPopup) || void 0 === _this$_editPopup6 || _this$_editPopup6.repaint()\r\n            }))\r\n        }\r\n    }\r\n    getEditForm() {\r\n        return this._editForm\r\n    }\r\n    _endUpdateCore() {\r\n        var _this$_updateEditForm;\r\n        null === (_this$_updateEditForm = this._updateEditFormDeferred) || void 0 === _this$_updateEditForm || _this$_updateEditForm.resolve()\r\n    }\r\n    _beforeEndSaving(changes) {\r\n        super._beforeEndSaving(changes);\r\n        if (this.isPopupEditMode()) {\r\n            var _this$_editPopup7;\r\n            null === (_this$_editPopup7 = this._editPopup) || void 0 === _this$_editPopup7 || _this$_editPopup7.hide()\r\n        }\r\n    }\r\n    _processDataItemCore(item, change, key, columns, generateDataValues) {\r\n        const {\r\n            type: type\r\n        } = change;\r\n        if (this.isPopupEditMode() && type === DATA_EDIT_DATA_INSERT_TYPE) {\r\n            item.visible = false\r\n        }\r\n        super._processDataItemCore(item, change, key, columns, generateDataValues)\r\n    }\r\n    _editRowFromOptionChangedCore(rowIndices, rowIndex) {\r\n        const isPopupEditMode = this.isPopupEditMode();\r\n        super._editRowFromOptionChangedCore(rowIndices, rowIndex, isPopupEditMode);\r\n        if (isPopupEditMode) {\r\n            this._showEditPopup(rowIndex)\r\n        }\r\n    }\r\n};\r\nconst data = Base => class extends Base {\r\n    _updateEditItem(item) {\r\n        if (this._editingController.isFormEditMode()) {\r\n            item.rowType = \"detail\"\r\n        }\r\n    }\r\n    _getChangedColumnIndices(oldItem, newItem, visibleRowIndex, isLiveUpdate) {\r\n        if (false === isLiveUpdate && newItem.isEditing && this._editingController.isFormEditMode()) {\r\n            return\r\n        }\r\n        return super._getChangedColumnIndices.apply(this, arguments)\r\n    }\r\n};\r\nconst rowsView = Base => class extends Base {\r\n    _renderCellContent($cell, options) {\r\n        if (\"data\" === options.rowType && this._editingController.isPopupEditMode() && false === options.row.visible) {\r\n            return\r\n        }\r\n        super._renderCellContent.apply(this, arguments)\r\n    }\r\n    getCellElements(rowIndex) {\r\n        const $cellElements = super.getCellElements(rowIndex);\r\n        const editingController = this._editingController;\r\n        const editForm = editingController.getEditForm();\r\n        const editFormRowIndex = editingController.getEditFormRowIndex();\r\n        if (editFormRowIndex === rowIndex && $cellElements && editForm) {\r\n            return editForm.$element().find(`.${this.addWidgetPrefix(EDIT_FORM_ITEM_CLASS)}, .${BUTTON_CLASS}`)\r\n        }\r\n        return $cellElements\r\n    }\r\n    _getVisibleColumnIndex($cells, rowIndex, columnIdentifier) {\r\n        const editFormRowIndex = this._editingController.getEditFormRowIndex();\r\n        if (editFormRowIndex === rowIndex && isString(columnIdentifier)) {\r\n            const column = this._columnsController.columnOption(columnIdentifier);\r\n            return this._getEditFormEditorVisibleIndex($cells, column)\r\n        }\r\n        return super._getVisibleColumnIndex.apply(this, arguments)\r\n    }\r\n    _getEditFormEditorVisibleIndex($cells, column) {\r\n        let visibleIndex = -1;\r\n        each($cells, ((index, cellElement) => {\r\n            const item = $(cellElement).find(\".dx-field-item-content\").data(\"dx-form-item\");\r\n            if (null !== item && void 0 !== item && item.column && column && item.column.index === column.index) {\r\n                visibleIndex = index;\r\n                return false\r\n            }\r\n        }));\r\n        return visibleIndex\r\n    }\r\n    _isFormItem(parameters) {\r\n        const isDetailRow = \"detail\" === parameters.rowType || \"detailAdaptive\" === parameters.rowType;\r\n        const isPopupEditing = \"data\" === parameters.rowType && this._editingController.isPopupEditMode();\r\n        return (isDetailRow || isPopupEditing) && parameters.item\r\n    }\r\n    _updateCell($cell, parameters) {\r\n        if (this._isFormItem(parameters)) {\r\n            this._formItemPrepared(parameters, $cell)\r\n        } else {\r\n            super._updateCell($cell, parameters)\r\n        }\r\n    }\r\n    _updateContent() {\r\n        const editingController = this._editingController;\r\n        const oldEditForm = editingController.getEditForm();\r\n        const validationGroup = null === oldEditForm || void 0 === oldEditForm ? void 0 : oldEditForm.option(\"validationGroup\");\r\n        const deferred = super._updateContent.apply(this, arguments);\r\n        return deferred.done((() => {\r\n            const newEditForm = editingController.getEditForm();\r\n            if (validationGroup && newEditForm && newEditForm !== oldEditForm) {\r\n                newEditForm.option(\"validationGroup\", validationGroup)\r\n            }\r\n        }))\r\n    }\r\n};\r\nexport const editingFormBasedModule = {\r\n    extenders: {\r\n        controllers: {\r\n            editing: editingControllerExtender,\r\n            data: data\r\n        },\r\n        views: {\r\n            rowsView: rowsView\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AAcA;;;;;;;;;;;;;;;;;;;AAIA,MAAM,4BAA4B,CAAA;IAAQ,qBAAc;QACpD,OAAO;YACH,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,uBAAuB,GAAG;YAC/B,KAAK,CAAC;QACV;QACA,UAAU,QAAQ,EAAE;YAChB,OAAO,CAAC,IAAI,CAAC,eAAe,MAAM,KAAK,CAAC,UAAU;QACtD;QACA,wBAAwB;YACpB,OAAO,IAAI,CAAC,eAAe,MAAM,IAAI,CAAC,cAAc;QACxD;QACA,iBAAiB;YACb,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC;YAC7B,OAAO,aAAa,+LAAA,CAAA,iBAAc;QACtC;QACA,8BAA8B;YAC1B,MAAM,gBAAgB,IAAI,CAAC,cAAc;YACzC,IAAI,IAAI,CAAC,cAAc,MAAM,eAAe;gBACxC,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B;gBAC7D,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;gBAC3D,MAAM,oBAAoB,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;gBACzD,OAAO,IAAI,CAAC,SAAS,CAAC,8BAA8B,CAAC,mBAAmB,cAAc,MAAM;YAChG;YACA,OAAO,KAAK,CAAC;QACjB;QACA,sBAAsB;YAClB,OAAO,IAAI,CAAC,qBAAqB,KAAK,IAAI,CAAC,uBAAuB,KAAK,KAAK,CAAC;QACjF;QACA,uBAAuB;YACnB,MAAM,SAAS,KAAK,CAAC;YACrB,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;YACnC,OAAO,IAAI,CAAC,qBAAqB,KAAK,eAAe,aAAa,IAAI,SAAS;QACnF;QACA,mBAAmB,IAAI,EAAE;YACrB,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,aAAa;gBACjB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;gBAC/B,MAAM,aAAa,SAAS,QAAQ,KAAK,MAAM,QAAQ,SAAS,CAAC,cAAc,KAAK,KAAK,KAAK,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,IAAI,CAAE,CAAA,OAAQ,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,KAAK,GAAG,EAAE;gBACjL,MAAM,oBAAoB,CAAC,SAAS,CAAC,oBAAoB,KAAK,WAAW,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,MAAM,KAAK,KAAK,WAAW,CAAC,KAAK,CAAE,CAAA,OAAQ,aAAa;gBAChM,IAAI,CAAC,cAAc,KAAK,UAAU,IAAI,cAAc,KAAK,eAAe,KAAK,CAAC,mBAAmB;oBAC7F,IAAI,CAAC,iBAAiB;gBAC1B;YACJ;YACA,KAAK,CAAC,mBAAmB;QAC7B;QACA,kBAAkB;YACd,IAAI;YACJ,MAAM,eAAe,SAAS,CAAC,mBAAmB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,MAAM,CAAC;YACrI,IAAI,IAAI,CAAC,eAAe,MAAM,cAAc;gBACxC,OAAO,IAAI,CAAC,cAAc;YAC9B;QACJ;QACA,cAAc,QAAQ,EAAE;YACpB,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;gBACH,KAAK,CAAC,cAAc;YACxB;QACJ;QACA,sBAAsB;YAClB,KAAK,CAAC;YACN,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,CAAC,cAAc;YACvB;QACJ;QACA,mBAAmB,GAAG,EAAE,cAAc,EAAE,oBAAoB,EAAE;YAC1D,MAAM,WAAW,IAAI,CAAC,SAAS;YAC/B,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB;oBACnC,IAAI;oBACJ,SAAS,CAAC,cAAc,IAAI,MAAM,KAAK,KAAK,MAAM,eAAe,YAAY,IAAI,CAAC,KAAK;oBACvF,IAAI,CAAC,SAAS,CAAC,sBAAsB;gBACzC,OAAO,IAAI,UAAU;oBACjB,IAAI,CAAC,uBAAuB,GAAG,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,IAAI,CAAE,IAAM,SAAS,OAAO;oBAC1E,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;wBACxB,IAAI,CAAC,uBAAuB,CAAC,OAAO;oBACxC;gBACJ;YACJ,OAAO;gBACH,KAAK,CAAC,mBAAmB,KAAK,gBAAgB;YAClD;QACJ;QACA,eAAe,QAAQ,EAAE,WAAW,EAAE;YAClC,MAAM,iBAAiB,cAAc,uJAAA,CAAA,UAAO,CAAC,OAAO,GAAG,UAAU;YACjE,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,mBAAgB;YAC5D,MAAM,eAAe,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;gBACxB,WAAW;gBACX,YAAY;gBACZ,aAAa;oBACT,OAAO;gBACX;gBACA,cAAc;oBAAC;wBACX,SAAS;wBACT,UAAU;wBACV,QAAQ;wBACR,SAAS,IAAI,CAAC,oBAAoB;oBACtC;oBAAG;wBACC,SAAS;wBACT,UAAU;wBACV,QAAQ;wBACR,SAAS,IAAI,CAAC,sBAAsB;oBACxC;iBAAE;gBACF,iBAAiB,IAAI,CAAC,yBAAyB,CAAC;YACpD,GAAG,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,4BAAyB;YACxC,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,IAAI,QAAQ,CAAC;gBAChF,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,iBAAiB,kKAAA,CAAA,UAAK;gBAC9D,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,UAAU,IAAI,CAAC,0BAA0B;gBAC5D,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,SAAU,CAAA;oBACzB,0LAAA,CAAA,UAAY,CAAC,OAAO,CAAC,EAAE,SAAS,CAAC,QAAQ,GAAG,IAAI,CAAC,+LAAA,CAAA,6BAA0B,EAAE,GAAG,CAAC,AAAC,IAA2B,OAAxB,+LAAA,CAAA,0BAAuB,GAAI,KAAK,IAAI;oBACzH,IAAI,aAAa;wBACb,IAAI;wBACJ,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,mBAAmB,gBAAgB,OAAO;oBACxG;gBACJ;YACJ;YACA,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACvB,IAAI,CAAC,UAAU,CAAC,IAAI;YACpB,KAAK,CAAC,eAAe,UAAU;QACnC;QACA,0BAA0B,QAAQ,EAAE;YAChC,MAAM,MAAM,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC,SAAS;YACrD,MAAM,kBAAkB;gBACpB,KAAK;gBACL,QAAQ,IAAI,MAAM;gBAClB,SAAS,IAAI,OAAO;gBACpB,KAAK,IAAI,GAAG;gBACZ,UAAU;YACd;YACA,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,iBAAiB;YAChD,OAAO,CAAA;gBACH,MAAM,eAAe,IAAI,CAAC,mBAAmB;gBAC7C,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,YAAY,6KAAA,CAAA,UAAU;gBACnF,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO;gBAC1C,aAAa,IAAI,CAAC,cAAc,EAAE,iBAAiB;oBAC/C,aAAa;gBACjB;gBACA,IAAI,CAAC,SAAS,CAAC,sBAAsB;gBACrC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,MAAM,GAAG,IAAI,CAAC,cAAc,IAAI,CAAC,QAAQ,CAAC;YAC3D;QACJ;QACA,oBAAoB;YAChB,MAAM,WAAW,IAAI,CAAC,uBAAuB;YAC7C,IAAI,YAAY,GAAG;gBACf,IAAI,mBAAmB;gBACvB,MAAM,mBAAmB,SAAS,CAAC,oBAAoB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,MAAM,CAAC;gBAC5I,SAAS,CAAC,oBAAoB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,qBAAqB,kBAAkB,MAAM,CAAC,aAAa;gBACxH,IAAI,CAAC,cAAc,CAAC,UAAU;gBAC9B,IAAI,KAAK,MAAM,kBAAkB;oBAC7B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,aAAa;gBACxC;YACJ;QACJ;QACA,iBAAiB;YACb,IAAI;YACJ,SAAS,CAAC,oBAAoB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,qBAAqB,kBAAkB,MAAM,CAAC,WAAW;QAC1H;QACA,cAAc,IAAI,EAAE;YAChB,IAAI,cAAc,KAAK,IAAI,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBACzD,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;gBACJ,IAAI,MAAM,SAAS,OAAO,CAAC,+LAAA,CAAA,2BAAwB,GAAG;oBAClD,IAAI,CAAC,uBAAuB,CAAC;oBAC7B,KAAK,OAAO,GAAG;gBACnB,OAAO,IAAI,MAAM,SAAS,OAAO,CAAC,+LAAA,CAAA,4BAAyB,GAAG;oBAC1D,IAAI,CAAC,wBAAwB,CAAC;oBAC9B,KAAK,OAAO,GAAG;gBACnB;YACJ;YACA,KAAK,CAAC,cAAc;QACxB;QACA,wBAAwB,IAAI,EAAE;YAC1B,IAAI;YACJ,IAAI,IAAI,CAAC,cAAc,IAAI;gBACvB,MAAM,eAAe,IAAI,CAAC,uBAAuB;gBACjD,IAAI,gBAAgB,GAAG;oBACnB,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC;wBAC7B,YAAY;wBACZ,YAAY;4BAAC;yBAAa;oBAC9B;gBACJ;YACJ,OAAO,IAAI,SAAS,CAAC,oBAAoB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,qBAAqB,kBAAkB,MAAM,CAAC,cAAc,MAAM,KAAK,QAAQ,CAAC,OAAO,CAAC,+LAAA,CAAA,2BAAwB,GAAG;gBACvL,IAAI,CAAC,iBAAiB;YAC1B;QACJ;QACA,yBAAyB,IAAI,EAAE;YAC3B,MAAM,YAAY,IAAI,CAAC,UAAU;YACjC,IAAI,WAAW;gBACX,MAAM,kBAAkB,KAAK,QAAQ,CAAC,KAAK,CAAC,+LAAA,CAAA,4BAAyB,CAAC,MAAM,GAAG;gBAC/E,IAAI,iBAAiB;oBACjB,UAAU,MAAM,CAAC,iBAAiB,KAAK,KAAK;gBAChD,OAAO;oBACH,UAAU,MAAM,CAAC,KAAK,KAAK;gBAC/B;YACJ;QACJ;QACA,yBAAyB,iBAAiB,EAAE,IAAI,EAAE,mBAAmB,EAAE,SAAS,EAAE,UAAU,EAAE;YAC1F,MAAM,OAAO,IAAI;YACjB,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;YACrB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;YACJ,MAAM,aAAa,CAAA,GAAA,yMAAA,CAAA,gBAAa,AAAD,EAAE;YACjC,MAAM,MAAM,SAAS,qBAAqB,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,GAAG;YACvG,MAAM,UAAU,SAAS,OAAO,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,IAAI;YAClE,MAAM,OAAO,oBAAoB,SAAS;YAC1C,MAAM,QAAQ,OAAO,kBAAkB,CAAC;YACxC,MAAM,eAAe,sLAAA,CAAA,UAAa,CAAC,eAAe,CAAC,QAAQ,OAAO,SAAS,SAAS,OAAO,KAAK,MAAM,MAAM,KAAK,IAAI,IAAI,OAAO;YAChI,MAAM,EACF,OAAO,KAAK,EACZ,WAAW,SAAS,EACpB,WAAW,SAAS,EACvB,GAAG,oBAAoB,aAAa,IAAI,CAAC;YAC1C,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,mBAAmB;gBAC9C,MAAM;gBACN,aAAa;gBACb,UAAU;gBACV,MAAM;gBACN,IAAI,KAAK,SAAS,CAAC,KAAK,IAAI,IAAI,KAAK,SAAS;gBAC9C,QAAQ,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ;oBACvB,YAAY;oBACZ,eAAe,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;wBAClB,OAAO;wBACP,WAAW;wBACX,WAAW;oBACf,GAAG,OAAO,aAAa,EAAE,KAAK,aAAa;gBAC/C;gBACA,aAAa,OAAO,KAAK;gBACzB,UAAU,CAAC,cAAc,OAAO,YAAY,IAAI,SAAS,KAAK,EAAE,IAAI;oBAChE,KAAK,gBAAgB,CAAC,aAAa,OAAO;gBAC9C;YACJ;YACA,YAAY,KAAK,GAAG;YACpB,YAAY,YAAY,GAAG;YAC3B,YAAY,IAAI,GAAG,CAAC,OAAO,OAAO,GAAG,sLAAA,CAAA,UAAa,CAAC,WAAW,CAAC,cAAc,UAAU;YACvF,MAAM,WAAW,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa;YACvE,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC,YAAY,UAAU,aAAa,CAAC,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI,CAAE;gBACjG,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY;YAC3C;YACA,OAAO;QACX;QACA,sBAAsB,WAAW,EAAE,IAAI,EAAE;YACrC,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,KAAK,IAAI,IAAI,KAAK,SAAS;YACtE,OAAO,CAAC,SAAS;gBACb,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;gBACrB,MAAM,EACF,KAAK,GAAG,EACX,GAAG;gBACJ,IAAI,SAAS,OAAO,KAAK,MAAM,OAAO,IAAI,KAAK,EAAE;oBAC7C,MAAM,UAAU,IAAI,KAAK,CAAE,IAAM,OAAO,QAAQ,CAAC,IAAI,IAAI,GAAK;wBAC1D,IAAI;wBACJ,IAAI,iBAAiB,WAAW,IAAI,CAAC,cAAc,KAAK;wBACxD,IAAI,YAAY,eAAe,IAAI,CAAC;wBACpC,MAAM,mBAAmB,SAAS,CAAC,aAAa,SAAS,KAAK,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,MAAM;wBAChH,WAAW,QAAQ,GAAG,MAAM;wBAC5B,cAAc,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,MAAM,SAAS;wBACnF,iBAAiB,WAAW,IAAI,CAAC,cAAc,KAAK;wBACpD,YAAY,eAAe,IAAI,CAAC;wBAChC,IAAI,oBAAoB,CAAC,WAAW;4BAChC,eAAe,WAAW,CAAC;gCACvB,iBAAiB,iBAAiB,eAAe;gCACjD,iBAAiB,iBAAiB,eAAe;gCACjD,YAAY,iBAAiB,UAAU;4BAC3C;wBACJ;oBACJ;oBACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,YAAY,2KAAA,CAAA,cAAW,EAAE;gBAC7C;gBACA,cAAc,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI,EAAE,aAAa,MAAM,SAAS;YACvF;QACJ;QACA,mBAAmB,aAAa,EAAE;YAC9B,IAAI;YACJ,MAAM,kBAAkB,SAAS,CAAC,wBAAwB,IAAI,CAAC,0BAA0B,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,CAAC,IAAI,EAAE;YAC3K,MAAM,oBAAoB,IAAI,CAAC,MAAM,CAAC;YACtC,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,uBAAoB;YACnE,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC;YACxB,MAAM,qBAAqB,CAAC;YAC5B,IAAI,CAAC,OAAO;gBACR,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,UAAU;gBAClD,QAAQ,EAAE;gBACV,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,CAAC,GAAG;oBACf,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,OAAO,IAAI,EAAE;wBAChC,MAAM,IAAI,CAAC;4BACP,QAAQ;4BACR,MAAM,OAAO,IAAI;4BACjB,WAAW,OAAO,SAAS;wBAC/B;oBACJ;gBACJ;YACJ,OAAO;gBACH,CAAA,GAAA,yMAAA,CAAA,mBAAgB,AAAD,EAAE,OAAQ,CAAA;oBACrB,MAAM,SAAS,CAAC,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,IAAI,KAAK,CAAC,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,SAAS;oBACrI,IAAI,QAAQ;wBACR,kBAAkB,CAAC,OAAO,GAAG,CAAC,CAAC,KAAK,UAAU;oBAClD;gBACJ;YACJ;YACA,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,iBAAiB;gBAC/B,OAAO;gBACP,QAAQ,AAAC,MAAc,OAAT,IAAI,oJAAA,CAAA,UAAI;gBACtB,eAAe,CAAA;oBACX,IAAI;oBACJ,MAAM,SAAS,KAAK,IAAI,IAAI,KAAK,SAAS;oBAC1C,IAAI,KAAK,MAAM,IAAI,QAAQ;wBACvB,SAAS,KAAK,MAAM,IAAI,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,KAAK,IAAI,GAAG,AAAC,QAAiB,OAAV,KAAK,IAAI,IAAK,AAAC,aAA2B,OAAf,KAAK,SAAS;oBAC9H;oBACA,IAAI,QAAQ;wBACR,KAAK,KAAK,GAAG,KAAK,KAAK,IAAI,CAAC;wBAC5B,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,IAAI,IAAI,OAAO,OAAO;wBACnD,IAAI,cAAc,OAAO,QAAQ,IAAI,KAAK,MAAM,KAAK,KAAK,CAAC,OAAO,EAAE;4BAChE,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC;4BAC9B,IAAI,eAAe,aAAa,aAAa,WAAW;gCACpD,KAAK,KAAK,CAAC,OAAO,GAAG;4BACzB;wBACJ;wBACA,KAAK,QAAQ,GAAG,KAAK,QAAQ,IAAI,IAAI,CAAC,qBAAqB,CAAC,eAAe;wBAC3E,KAAK,MAAM,GAAG;wBACd,KAAK,kBAAkB,GAAG,kBAAkB,CAAC,OAAO;wBACpD,IAAI,OAAO,QAAQ,EAAE;4BACjB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,OAAO,QAAQ;wBAChC;wBACA,IAAI,KAAK,MAAM,KAAK,UAAU,IAAI,OAAO,eAAe,EAAE;4BACtD,KAAK,UAAU,GAAG,OAAO,eAAe,CAAC,IAAI,CAAE,CAAA,OAAQ,eAAe,KAAK,IAAI;4BAC/E,KAAK,eAAe,GAAG,EAAE;wBAC7B;wBACA,MAAM,cAAc,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,IAAI,KAAK,OAAO,GAAG;wBAC7D,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,aAAa;4BACrC,IAAI,CAAC,cAAc,GAAG;wBAC1B;oBACJ;oBACA,SAAS,qBAAqB,KAAK,MAAM,qBAAqB,kBAAkB,IAAI,CAAC,IAAI,EAAE;oBAC3F,KAAK,QAAQ,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,QAAQ,IAAI,AAAC,GAAmB,OAAjB,KAAK,QAAQ,EAAC,KAAqB,OAAlB,qBAAsB;gBACxF;YACJ;QACJ;QACA,sBAAsB;YAClB,OAAO,CAAC,YAAY,eAAe;gBAC/B,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,2BAAwB;gBAC5D,MAAM,sBAAsB,IAAI,CAAC,kBAAkB,CAAC;gBACpD,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;gBAC3C,MAAM,cAAc,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,WAAW;gBACzF,IAAI,CAAC,cAAc,GAAG,KAAK;gBAC3B,IAAI,aAAa;oBACb,eAAe,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,wBAAqB;gBACtE;gBACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,kJAAA,CAAA,UAAI,EAAE,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,iBAAiB;gBACzF,IAAI,CAAC,aAAa;oBACd,MAAM,oBAAoB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,+BAA4B,GAAG,QAAQ,CAAC;oBAC3G,IAAI,CAAC,gBAAgB,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,oBAAoB,oJAAA,CAAA,UAAM,EAAE,IAAI,CAAC,oBAAoB;oBAC/F,IAAI,CAAC,gBAAgB,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,oBAAoB,oJAAA,CAAA,UAAM,EAAE,IAAI,CAAC,sBAAsB;gBACrG;gBACA,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,gBAAiB;oBAC/B,IAAI;oBACJ,IAAI,CAAC,SAAS,CAAC,sBAAsB;oBACrC,SAAS,CAAC,oBAAoB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,qBAAqB,kBAAkB,OAAO;gBAC/G;YACJ;QACJ;QACA,cAAc;YACV,OAAO,IAAI,CAAC,SAAS;QACzB;QACA,iBAAiB;YACb,IAAI;YACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,uBAAuB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,OAAO;QACxI;QACA,iBAAiB,OAAO,EAAE;YACtB,KAAK,CAAC,iBAAiB;YACvB,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI;gBACJ,SAAS,CAAC,oBAAoB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,qBAAqB,kBAAkB,IAAI;YAC5G;QACJ;QACA,qBAAqB,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,kBAAkB,EAAE;YACjE,MAAM,EACF,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,IAAI,CAAC,eAAe,MAAM,SAAS,+LAAA,CAAA,6BAA0B,EAAE;gBAC/D,KAAK,OAAO,GAAG;YACnB;YACA,KAAK,CAAC,qBAAqB,MAAM,QAAQ,KAAK,SAAS;QAC3D;QACA,8BAA8B,UAAU,EAAE,QAAQ,EAAE;YAChD,MAAM,kBAAkB,IAAI,CAAC,eAAe;YAC5C,KAAK,CAAC,8BAA8B,YAAY,UAAU;YAC1D,IAAI,iBAAiB;gBACjB,IAAI,CAAC,cAAc,CAAC;YACxB;QACJ;IACJ;;;AACA,MAAM,OAAO,CAAA;IAAQ,qBAAc;QAC/B,gBAAgB,IAAI,EAAE;YAClB,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,IAAI;gBAC1C,KAAK,OAAO,GAAG;YACnB;QACJ;QACA,yBAAyB,OAAO,EAAE,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE;YACtE,IAAI,UAAU,gBAAgB,QAAQ,SAAS,IAAI,IAAI,CAAC,kBAAkB,CAAC,cAAc,IAAI;gBACzF;YACJ;YACA,OAAO,KAAK,CAAC,yBAAyB,KAAK,CAAC,IAAI,EAAE;QACtD;IACJ;;;AACA,MAAM,WAAW,CAAA;IAAQ,qBAAc;QACnC,mBAAmB,KAAK,EAAE,OAAO,EAAE;YAC/B,IAAI,WAAW,QAAQ,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,eAAe,MAAM,UAAU,QAAQ,GAAG,CAAC,OAAO,EAAE;gBAC1G;YACJ;YACA,KAAK,CAAC,mBAAmB,KAAK,CAAC,IAAI,EAAE;QACzC;QACA,gBAAgB,QAAQ,EAAE;YACtB,MAAM,gBAAgB,KAAK,CAAC,gBAAgB;YAC5C,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,MAAM,WAAW,kBAAkB,WAAW;YAC9C,MAAM,mBAAmB,kBAAkB,mBAAmB;YAC9D,IAAI,qBAAqB,YAAY,iBAAiB,UAAU;gBAC5D,OAAO,SAAS,QAAQ,GAAG,IAAI,CAAC,AAAC,IAAmD,OAAhD,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,uBAAoB,GAAE,OAAkB,OAAb,+LAAA,CAAA,eAAY;YACpG;YACA,OAAO;QACX;QACA,uBAAuB,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE;YACvD,MAAM,mBAAmB,IAAI,CAAC,kBAAkB,CAAC,mBAAmB;YACpE,IAAI,qBAAqB,YAAY,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB;gBAC7D,MAAM,SAAS,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;gBACpD,OAAO,IAAI,CAAC,8BAA8B,CAAC,QAAQ;YACvD;YACA,OAAO,KAAK,CAAC,uBAAuB,KAAK,CAAC,IAAI,EAAE;QACpD;QACA,+BAA+B,MAAM,EAAE,MAAM,EAAE;YAC3C,IAAI,eAAe,CAAC;YACpB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,QAAS,CAAC,OAAO;gBAClB,MAAM,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,aAAa,IAAI,CAAC,0BAA0B,IAAI,CAAC;gBAChE,IAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,KAAK,MAAM,IAAI,UAAU,KAAK,MAAM,CAAC,KAAK,KAAK,OAAO,KAAK,EAAE;oBACjG,eAAe;oBACf,OAAO;gBACX;YACJ;YACA,OAAO;QACX;QACA,YAAY,UAAU,EAAE;YACpB,MAAM,cAAc,aAAa,WAAW,OAAO,IAAI,qBAAqB,WAAW,OAAO;YAC9F,MAAM,iBAAiB,WAAW,WAAW,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,eAAe;YAC/F,OAAO,CAAC,eAAe,cAAc,KAAK,WAAW,IAAI;QAC7D;QACA,YAAY,KAAK,EAAE,UAAU,EAAE;YAC3B,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa;gBAC9B,IAAI,CAAC,iBAAiB,CAAC,YAAY;YACvC,OAAO;gBACH,KAAK,CAAC,YAAY,OAAO;YAC7B;QACJ;QACA,iBAAiB;YACb,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,MAAM,cAAc,kBAAkB,WAAW;YACjD,MAAM,kBAAkB,SAAS,eAAe,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,MAAM,CAAC;YACrG,MAAM,WAAW,KAAK,CAAC,eAAe,KAAK,CAAC,IAAI,EAAE;YAClD,OAAO,SAAS,IAAI,CAAE;gBAClB,MAAM,cAAc,kBAAkB,WAAW;gBACjD,IAAI,mBAAmB,eAAe,gBAAgB,aAAa;oBAC/D,YAAY,MAAM,CAAC,mBAAmB;gBAC1C;YACJ;QACJ;IACJ;;;AACO,MAAM,yBAAyB;IAClC,WAAW;QACP,aAAa;YACT,SAAS;YACT,MAAM;QACV;QACA,OAAO;YACH,UAAU;QACd;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3340, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/editing/m_editing_cell_based.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/editing/m_editing_cell_based.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    name as clickEventName\r\n} from \"../../../../common/core/events/click\";\r\nimport eventsEngine from \"../../../../common/core/events/core/events_engine\";\r\nimport holdEvent from \"../../../../common/core/events/hold\";\r\nimport pointerEvents from \"../../../../common/core/events/pointer\";\r\nimport {\r\n    addNamespace\r\n} from \"../../../../common/core/events/utils/index\";\r\nimport {\r\n    createObjectWithChanges\r\n} from \"../../../../common/data/array_utils\";\r\nimport domAdapter from \"../../../../core/dom_adapter\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    deferRender\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../../../core/utils/deferred\";\r\nimport {\r\n    isElementInDom\r\n} from \"../../../../core/utils/dom\";\r\nimport {\r\n    isDefined,\r\n    isString\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    ADD_ROW_BUTTON_CLASS,\r\n    CELL_MODIFIED_CLASS,\r\n    DATA_EDIT_DATA_REMOVE_TYPE,\r\n    DATA_ROW_CLASS,\r\n    DROPDOWN_EDITOR_OVERLAY_CLASS,\r\n    EDIT_MODE_BATCH,\r\n    EDIT_MODE_CELL,\r\n    EDITING_EDITCOLUMNNAME_OPTION_NAME,\r\n    EDITING_EDITROWKEY_OPTION_NAME,\r\n    EDITOR_CELL_CLASS,\r\n    FOCUS_OVERLAY_CLASS,\r\n    ROW_CLASS,\r\n    ROW_REMOVED,\r\n    TARGET_COMPONENT_NAME\r\n} from \"./const\";\r\nimport {\r\n    isEditable\r\n} from \"./m_editing_utils\";\r\nconst editingControllerExtender = Base => class extends Base {\r\n    init() {\r\n        const needCreateHandlers = !this._saveEditorHandler;\r\n        super.init();\r\n        if (needCreateHandlers) {\r\n            let $pointerDownTarget;\r\n            let isResizing;\r\n            this._pointerUpEditorHandler = () => {\r\n                var _this$_columnsResizer;\r\n                isResizing = null === (_this$_columnsResizer = this._columnsResizerController) || void 0 === _this$_columnsResizer ? void 0 : _this$_columnsResizer.isResizing()\r\n            };\r\n            this._pointerDownEditorHandler = e => $pointerDownTarget = $(e.target);\r\n            this._saveEditorHandler = this.createAction((function(e) {\r\n                const {\r\n                    event: event\r\n                } = e;\r\n                const $target = $(event.target);\r\n                const targetComponent = event[TARGET_COMPONENT_NAME];\r\n                const {\r\n                    component: component\r\n                } = this;\r\n                if (isEditable($pointerDownTarget) && !$pointerDownTarget.is($target)) {\r\n                    return\r\n                }\r\n\r\n                function checkEditorPopup($element) {\r\n                    if (!$element) {\r\n                        return false\r\n                    }\r\n                    const $dropDownEditorOverlay = $element.closest(`.${DROPDOWN_EDITOR_OVERLAY_CLASS}`);\r\n                    const $componentElement = component.$element();\r\n                    return $dropDownEditorOverlay.length > 0 && 0 === $componentElement.closest($dropDownEditorOverlay).length\r\n                }\r\n                if (this.isCellOrBatchEditMode() && !this._editCellInProgress) {\r\n                    const isEditorPopup = checkEditorPopup($target) || checkEditorPopup(null === targetComponent || void 0 === targetComponent ? void 0 : targetComponent.$element());\r\n                    const isAnotherComponent = targetComponent && !targetComponent._disposed && targetComponent !== this.component;\r\n                    const isAddRowButton = !!$target.closest(`.${this.addWidgetPrefix(ADD_ROW_BUTTON_CLASS)}`).length;\r\n                    const isFocusOverlay = $target.hasClass(this.addWidgetPrefix(FOCUS_OVERLAY_CLASS));\r\n                    const isCellEditMode = this.isCellEditMode();\r\n                    if (!isResizing && !isEditorPopup && !isFocusOverlay && !(isAddRowButton && isCellEditMode && this.isEditing()) && (isElementInDom($target) || isAnotherComponent)) {\r\n                        this._closeEditItem.bind(this)($target)\r\n                    }\r\n                }\r\n            }));\r\n            eventsEngine.on(domAdapter.getDocument(), pointerEvents.up, this._pointerUpEditorHandler);\r\n            eventsEngine.on(domAdapter.getDocument(), pointerEvents.down, this._pointerDownEditorHandler);\r\n            eventsEngine.on(domAdapter.getDocument(), clickEventName, this._saveEditorHandler)\r\n        }\r\n    }\r\n    isCellEditMode() {\r\n        return this.option(\"editing.mode\") === EDIT_MODE_CELL\r\n    }\r\n    isBatchEditMode() {\r\n        return this.option(\"editing.mode\") === EDIT_MODE_BATCH\r\n    }\r\n    isCellOrBatchEditMode() {\r\n        return this.isCellEditMode() || this.isBatchEditMode()\r\n    }\r\n    _needToCloseEditableCell($targetElement) {\r\n        const $element = this.component.$element();\r\n        let result = this.isEditing();\r\n        const isCurrentComponentElement = !$element || !!$targetElement.closest($element).length;\r\n        if (isCurrentComponentElement) {\r\n            const isDataRow = $targetElement.closest(`.${DATA_ROW_CLASS}`).length;\r\n            if (isDataRow) {\r\n                const $targetCell = $targetElement.closest(`.${ROW_CLASS}> td`);\r\n                const rowIndex = this._rowsView.getRowIndex($targetCell.parent());\r\n                const cellElements = this._rowsView.getCellElements(rowIndex);\r\n                if (null !== cellElements && void 0 !== cellElements && cellElements.length) {\r\n                    var _visibleColumns$colum;\r\n                    const columnIndex = cellElements.index($targetCell);\r\n                    const visibleColumns = this._columnsController.getVisibleColumns();\r\n                    const allowEditing = null === (_visibleColumns$colum = visibleColumns[columnIndex]) || void 0 === _visibleColumns$colum ? void 0 : _visibleColumns$colum.allowEditing;\r\n                    const isEditingCell = this.isEditCell(rowIndex, columnIndex);\r\n                    result = result && !allowEditing && !isEditingCell\r\n                }\r\n            }\r\n        }\r\n        return result || super._needToCloseEditableCell($targetElement)\r\n    }\r\n    _closeEditItem($targetElement) {\r\n        if (this._needToCloseEditableCell($targetElement)) {\r\n            this.closeEditCell()\r\n        }\r\n    }\r\n    _focusEditorIfNeed() {\r\n        if (this._needFocusEditor && this.isCellOrBatchEditMode()) {\r\n            var _this$_rowsView;\r\n            const editColumnIndex = this._getVisibleEditColumnIndex();\r\n            const $cell = null === (_this$_rowsView = this._rowsView) || void 0 === _this$_rowsView ? void 0 : _this$_rowsView._getCellElement(this._getVisibleEditRowIndex(), editColumnIndex);\r\n            this._refocusEditCell = false;\r\n            clearTimeout(this._inputFocusTimeoutID);\r\n            if ($cell && !$cell.find(\":focus\").length) {\r\n                this._focusEditingCell((() => {\r\n                    this._editCellInProgress = false\r\n                }), $cell, true)\r\n            } else {\r\n                this._editCellInProgress = false\r\n            }\r\n            this._needFocusEditor = false\r\n        } else {\r\n            super._focusEditorIfNeed()\r\n        }\r\n    }\r\n    isEditing() {\r\n        if (this.isCellOrBatchEditMode()) {\r\n            const isEditRowKeyDefined = isDefined(this.option(EDITING_EDITROWKEY_OPTION_NAME));\r\n            const isEditColumnNameDefined = isDefined(this.option(EDITING_EDITCOLUMNNAME_OPTION_NAME));\r\n            return isEditRowKeyDefined && isEditColumnNameDefined\r\n        }\r\n        return super.isEditing()\r\n    }\r\n    _handleEditColumnNameChange(args) {\r\n        const oldRowIndex = this._getVisibleEditRowIndex(args.previousValue);\r\n        if (this.isCellOrBatchEditMode() && -1 !== oldRowIndex && isDefined(args.value) && args.value !== args.previousValue) {\r\n            const columnIndex = this._columnsController.getVisibleColumnIndex(args.value);\r\n            const oldColumnIndex = this._columnsController.getVisibleColumnIndex(args.previousValue);\r\n            this._editCellFromOptionChanged(columnIndex, oldColumnIndex, oldRowIndex)\r\n        }\r\n    }\r\n    _addRow(parentKey) {\r\n        if (this.isCellEditMode() && this.hasChanges()) {\r\n            const deferred = new Deferred;\r\n            this.saveEditData().done((() => {\r\n                if (!this.hasChanges()) {\r\n                    this.addRow(parentKey).done(deferred.resolve).fail(deferred.reject)\r\n                } else {\r\n                    deferred.reject(\"cancel\")\r\n                }\r\n            }));\r\n            return deferred.promise()\r\n        }\r\n        return super._addRow(parentKey)\r\n    }\r\n    editCell(rowIndex, columnIndex) {\r\n        return this._editCell({\r\n            rowIndex: rowIndex,\r\n            columnIndex: columnIndex\r\n        })\r\n    }\r\n    _editCell(options) {\r\n        const d = new Deferred;\r\n        let coreResult;\r\n        this.executeOperation(d, (() => {\r\n            coreResult = this._editCellCore(options);\r\n            when(coreResult).done(d.resolve).fail(d.reject)\r\n        }));\r\n        return void 0 !== coreResult ? coreResult : d.promise()\r\n    }\r\n    _editCellCore(options) {\r\n        const dataController = this._dataController;\r\n        const isEditByOptionChanged = isDefined(options.oldColumnIndex) || isDefined(options.oldRowIndex);\r\n        const {\r\n            columnIndex: columnIndex,\r\n            rowIndex: rowIndex,\r\n            column: column,\r\n            item: item\r\n        } = this._getNormalizedEditCellOptions(options);\r\n        const params = {\r\n            data: null === item || void 0 === item ? void 0 : item.data,\r\n            cancel: false,\r\n            column: column\r\n        };\r\n        if (void 0 === item.key) {\r\n            this._dataController.fireError(\"E1043\");\r\n            return\r\n        }\r\n        if (column && (\"data\" === item.rowType || \"detailAdaptive\" === item.rowType) && !item.removed && this.isCellOrBatchEditMode()) {\r\n            if (!isEditByOptionChanged && this.isEditCell(rowIndex, columnIndex)) {\r\n                return true\r\n            }\r\n            const editRowIndex = rowIndex + dataController.getRowIndexOffset();\r\n            return when(this._beforeEditCell(rowIndex, columnIndex, item)).done((cancel => {\r\n                if (cancel) {\r\n                    return\r\n                }\r\n                if (!this._prepareEditCell(params, item, columnIndex, editRowIndex)) {\r\n                    this._processCanceledEditingCell()\r\n                }\r\n            }))\r\n        }\r\n        return false\r\n    }\r\n    _beforeEditCell(rowIndex, columnIndex, item) {\r\n        if (this.isCellEditMode() && !item.isNewRow && this.hasChanges()) {\r\n            const isSaving = new Deferred;\r\n            this.saveEditData().always((() => {\r\n                isSaving.resolve(this.hasChanges())\r\n            }));\r\n            this.addDeferred(isSaving);\r\n            return isSaving\r\n        }\r\n        return false\r\n    }\r\n    publicMethods() {\r\n        const publicMethods = super.publicMethods();\r\n        return publicMethods.concat([\"editCell\", \"closeEditCell\"])\r\n    }\r\n    _getNormalizedEditCellOptions(_ref) {\r\n        let {\r\n            oldColumnIndex: oldColumnIndex,\r\n            oldRowIndex: oldRowIndex,\r\n            columnIndex: columnIndex,\r\n            rowIndex: rowIndex\r\n        } = _ref;\r\n        const columnsController = this._columnsController;\r\n        const visibleColumns = columnsController.getVisibleColumns();\r\n        const items = this._dataController.items();\r\n        const item = items[rowIndex];\r\n        let oldColumn;\r\n        if (isDefined(oldColumnIndex)) {\r\n            oldColumn = visibleColumns[oldColumnIndex]\r\n        } else {\r\n            oldColumn = this._getEditColumn()\r\n        }\r\n        if (!isDefined(oldRowIndex)) {\r\n            oldRowIndex = this._getVisibleEditRowIndex()\r\n        }\r\n        if (isString(columnIndex)) {\r\n            columnIndex = columnsController.columnOption(columnIndex, \"index\");\r\n            columnIndex = columnsController.getVisibleIndex(columnIndex)\r\n        }\r\n        const column = visibleColumns[columnIndex];\r\n        return {\r\n            oldColumn: oldColumn,\r\n            columnIndex: columnIndex,\r\n            oldRowIndex: oldRowIndex,\r\n            rowIndex: rowIndex,\r\n            column: column,\r\n            item: item\r\n        }\r\n    }\r\n    _prepareEditCell(params, item, editColumnIndex, editRowIndex) {\r\n        if (!item.isNewRow) {\r\n            params.key = item.key\r\n        }\r\n        if (this._isEditingStart(params)) {\r\n            return false\r\n        }\r\n        this._pageIndex = this._dataController.pageIndex();\r\n        this._setEditRowKey(item.key);\r\n        this._setEditColumnNameByIndex(editColumnIndex);\r\n        if (!params.column.showEditorAlways) {\r\n            this._addInternalData({\r\n                key: item.key,\r\n                oldData: item.oldData ?? item.data\r\n            })\r\n        }\r\n        return true\r\n    }\r\n    closeEditCell(isError, withoutSaveEditData) {\r\n        let result = when();\r\n        const oldEditRowIndex = this._getVisibleEditRowIndex();\r\n        if (this.isCellOrBatchEditMode()) {\r\n            const deferred = new Deferred;\r\n            result = new Deferred;\r\n            this.executeOperation(deferred, (() => {\r\n                this._closeEditCellCore(isError, oldEditRowIndex, withoutSaveEditData).always(result.resolve)\r\n            }))\r\n        }\r\n        return result.promise()\r\n    }\r\n    _closeEditCellCore(isError, oldEditRowIndex, withoutSaveEditData) {\r\n        const dataController = this._dataController;\r\n        const deferred = new Deferred;\r\n        const promise = deferred.promise();\r\n        if (this.isCellEditMode() && this.hasChanges()) {\r\n            if (!withoutSaveEditData) {\r\n                this.saveEditData().done((error => {\r\n                    if (!this.hasChanges()) {\r\n                        this.closeEditCell(!!error).always(deferred.resolve);\r\n                        return\r\n                    }\r\n                    deferred.resolve()\r\n                }));\r\n                return promise\r\n            }\r\n        } else {\r\n            this._resetEditRowKey();\r\n            this._resetEditColumnName();\r\n            if (oldEditRowIndex >= 0) {\r\n                const rowIndices = [oldEditRowIndex];\r\n                this._beforeCloseEditCellInBatchMode(rowIndices);\r\n                if (!isError) {\r\n                    dataController.updateItems({\r\n                        changeType: \"update\",\r\n                        rowIndices: rowIndices\r\n                    })\r\n                }\r\n            }\r\n        }\r\n        deferred.resolve();\r\n        return promise\r\n    }\r\n    _resetModifiedClassCells(changes) {\r\n        if (this.isBatchEditMode()) {\r\n            const columnsCount = this._columnsController.getVisibleColumns().length;\r\n            changes.forEach((_ref2 => {\r\n                let {\r\n                    key: key\r\n                } = _ref2;\r\n                const rowIndex = this._dataController.getRowIndexByKey(key);\r\n                for (let columnIndex = 0; columnIndex < columnsCount; columnIndex++) {\r\n                    const cellElement = this._rowsView._getCellElement(rowIndex, columnIndex);\r\n                    null === cellElement || void 0 === cellElement || cellElement.removeClass(CELL_MODIFIED_CLASS)\r\n                }\r\n            }))\r\n        }\r\n    }\r\n    _prepareChange(options, value, text) {\r\n        const $cellElement = $(options.cellElement);\r\n        if (this.isBatchEditMode() && void 0 !== options.key) {\r\n            this._applyModified($cellElement, options)\r\n        }\r\n        return super._prepareChange(options, value, text)\r\n    }\r\n    _cancelSaving(result) {\r\n        const dataController = this._dataController;\r\n        if (this.isCellOrBatchEditMode()) {\r\n            if (this.isBatchEditMode()) {\r\n                this._resetEditIndices()\r\n            }\r\n            dataController.updateItems()\r\n        }\r\n        super._cancelSaving(result)\r\n    }\r\n    optionChanged(args) {\r\n        const {\r\n            fullName: fullName\r\n        } = args;\r\n        if (\"editing\" === args.name && fullName === EDITING_EDITCOLUMNNAME_OPTION_NAME) {\r\n            this._handleEditColumnNameChange(args);\r\n            args.handled = true\r\n        } else {\r\n            super.optionChanged(args)\r\n        }\r\n    }\r\n    _editCellFromOptionChanged(columnIndex, oldColumnIndex, oldRowIndex) {\r\n        const columns = this._columnsController.getVisibleColumns();\r\n        if (columnIndex > -1) {\r\n            deferRender((() => {\r\n                this._repaintEditCell(columns[columnIndex], columns[oldColumnIndex], oldRowIndex)\r\n            }))\r\n        }\r\n    }\r\n    _handleEditRowKeyChange(args) {\r\n        if (this.isCellOrBatchEditMode()) {\r\n            const columnIndex = this._getVisibleEditColumnIndex();\r\n            const oldRowIndexCorrection = this._getEditRowIndexCorrection();\r\n            const oldRowIndex = this._dataController.getRowIndexByKey(args.previousValue) + oldRowIndexCorrection;\r\n            if (isDefined(args.value) && args.value !== args.previousValue) {\r\n                var _this$_editCellFromOp;\r\n                null === (_this$_editCellFromOp = this._editCellFromOptionChanged) || void 0 === _this$_editCellFromOp || _this$_editCellFromOp.call(this, columnIndex, columnIndex, oldRowIndex)\r\n            }\r\n        } else {\r\n            super._handleEditRowKeyChange(args)\r\n        }\r\n    }\r\n    deleteRow(rowIndex) {\r\n        if (this.isCellEditMode() && this.isEditing()) {\r\n            const {\r\n                isNewRow: isNewRow\r\n            } = this._dataController.items()[rowIndex];\r\n            const rowKey = this._dataController.getKeyByRowIndex(rowIndex);\r\n            this.closeEditCell(null, isNewRow).always((() => {\r\n                rowIndex = this._dataController.getRowIndexByKey(rowKey);\r\n                this._checkAndDeleteRow(rowIndex)\r\n            }))\r\n        } else {\r\n            super.deleteRow(rowIndex)\r\n        }\r\n    }\r\n    _checkAndDeleteRow(rowIndex) {\r\n        if (this.isBatchEditMode()) {\r\n            this._deleteRowCore(rowIndex)\r\n        } else {\r\n            super._checkAndDeleteRow(rowIndex)\r\n        }\r\n    }\r\n    _refreshCore(params) {\r\n        const {\r\n            isPageChanged: isPageChanged\r\n        } = params ?? {};\r\n        const needResetIndexes = this.isBatchEditMode() || isPageChanged && \"virtual\" !== this.option(\"scrolling.mode\");\r\n        if (this.isCellOrBatchEditMode()) {\r\n            if (needResetIndexes) {\r\n                this._resetEditColumnName();\r\n                this._resetEditRowKey()\r\n            }\r\n        } else {\r\n            super._refreshCore(params)\r\n        }\r\n    }\r\n    _allowRowAdding(params) {\r\n        if (this.isBatchEditMode()) {\r\n            return true\r\n        }\r\n        return super._allowRowAdding(params)\r\n    }\r\n    _afterDeleteRow(rowIndex, oldEditRowIndex) {\r\n        const dataController = this._dataController;\r\n        if (this.isBatchEditMode()) {\r\n            dataController.updateItems({\r\n                changeType: \"update\",\r\n                rowIndices: [oldEditRowIndex, rowIndex]\r\n            });\r\n            return (new Deferred).resolve()\r\n        }\r\n        return super._afterDeleteRow(rowIndex, oldEditRowIndex)\r\n    }\r\n    _updateEditRow(row, forceUpdateRow, isCustomSetCellValue) {\r\n        if (this.isCellOrBatchEditMode()) {\r\n            this._updateRowImmediately(row, forceUpdateRow, isCustomSetCellValue)\r\n        } else {\r\n            super._updateEditRow(row, forceUpdateRow, isCustomSetCellValue)\r\n        }\r\n    }\r\n    _isDefaultButtonVisible(button, options) {\r\n        if (this.isCellOrBatchEditMode()) {\r\n            const isBatchMode = this.isBatchEditMode();\r\n            switch (button.name) {\r\n                case \"save\":\r\n                case \"cancel\":\r\n                case \"edit\":\r\n                    return false;\r\n                case \"delete\":\r\n                    return super._isDefaultButtonVisible(button, options) && (!isBatchMode || !options.row.removed);\r\n                case \"undelete\":\r\n                    return isBatchMode && this.allowDeleting(options) && options.row.removed;\r\n                default:\r\n                    return super._isDefaultButtonVisible(button, options)\r\n            }\r\n        }\r\n        return super._isDefaultButtonVisible(button, options)\r\n    }\r\n    _isRowDeleteAllowed() {\r\n        const callBaseResult = super._isRowDeleteAllowed();\r\n        return callBaseResult || this.isBatchEditMode()\r\n    }\r\n    _beforeEndSaving(changes) {\r\n        if (this.isCellEditMode()) {\r\n            var _changes$;\r\n            if (\"update\" !== (null === (_changes$ = changes[0]) || void 0 === _changes$ ? void 0 : _changes$.type)) {\r\n                super._beforeEndSaving(changes)\r\n            }\r\n        } else {\r\n            if (this.isBatchEditMode()) {\r\n                this._resetModifiedClassCells(changes)\r\n            }\r\n            super._beforeEndSaving(changes)\r\n        }\r\n    }\r\n    prepareEditButtons(headerPanel) {\r\n        const editingOptions = this.option(\"editing\") ?? {};\r\n        const buttonItems = super.prepareEditButtons(headerPanel);\r\n        const needEditingButtons = editingOptions.allowUpdating || editingOptions.allowAdding || editingOptions.allowDeleting;\r\n        if (needEditingButtons && this.isBatchEditMode()) {\r\n            buttonItems.push(this.prepareButtonItem(headerPanel, \"save\", \"saveEditData\", 21));\r\n            buttonItems.push(this.prepareButtonItem(headerPanel, \"revert\", \"cancelEditData\", 22))\r\n        }\r\n        return buttonItems\r\n    }\r\n    _saveEditDataInner() {\r\n        var _deferred;\r\n        const editRow = this._dataController.getVisibleRows()[this.getEditRowIndex()];\r\n        const editColumn = this._getEditColumn();\r\n        const showEditorAlways = null === editColumn || void 0 === editColumn ? void 0 : editColumn.showEditorAlways;\r\n        const isUpdateInCellMode = this.isCellEditMode() && !(null !== editRow && void 0 !== editRow && editRow.isNewRow);\r\n        let deferred;\r\n        if (isUpdateInCellMode && showEditorAlways) {\r\n            deferred = new Deferred;\r\n            this.addDeferred(deferred)\r\n        }\r\n        return super._saveEditDataInner().always(null === (_deferred = deferred) || void 0 === _deferred ? void 0 : _deferred.resolve)\r\n    }\r\n    _applyChange(options, params, forceUpdateRow) {\r\n        const isUpdateInCellMode = this.isCellEditMode() && options.row && !options.row.isNewRow;\r\n        const {\r\n            showEditorAlways: showEditorAlways\r\n        } = options.column;\r\n        const isCustomSetCellValue = options.column.setCellValue !== options.column.defaultSetCellValue;\r\n        const focusPreviousEditingCell = showEditorAlways && !forceUpdateRow && isUpdateInCellMode && this.hasEditData() && !this.isEditCell(options.rowIndex, options.columnIndex);\r\n        if (focusPreviousEditingCell) {\r\n            this._focusEditingCell();\r\n            this._updateEditRow(options.row, true, isCustomSetCellValue);\r\n            return\r\n        }\r\n        return super._applyChange(options, params, forceUpdateRow)\r\n    }\r\n    _applyChangeCore(options, forceUpdateRow) {\r\n        const {\r\n            showEditorAlways: showEditorAlways\r\n        } = options.column;\r\n        const isUpdateInCellMode = this.isCellEditMode() && options.row && !options.row.isNewRow;\r\n        if (showEditorAlways && !forceUpdateRow) {\r\n            if (isUpdateInCellMode) {\r\n                this._setEditRowKey(options.row.key, true);\r\n                this._setEditColumnNameByIndex(options.columnIndex, true);\r\n                return this.saveEditData()\r\n            }\r\n            if (this.isBatchEditMode()) {\r\n                forceUpdateRow = this._needUpdateRow(options.column);\r\n                return super._applyChangeCore(options, forceUpdateRow)\r\n            }\r\n        }\r\n        return super._applyChangeCore(options, forceUpdateRow)\r\n    }\r\n    _processDataItemCore(item, change, key, columns, generateDataValues) {\r\n        const {\r\n            data: data,\r\n            type: type\r\n        } = change;\r\n        if (this.isBatchEditMode() && type === DATA_EDIT_DATA_REMOVE_TYPE) {\r\n            item.data = createObjectWithChanges(item.data, data)\r\n        }\r\n        super._processDataItemCore(item, change, key, columns, generateDataValues)\r\n    }\r\n    _processRemoveCore(changes, editIndex, processIfBatch) {\r\n        if (this.isBatchEditMode() && !processIfBatch) {\r\n            return\r\n        }\r\n        return super._processRemoveCore(changes, editIndex, processIfBatch)\r\n    }\r\n    _processRemoveIfError(changes, editIndex) {\r\n        if (this.isBatchEditMode()) {\r\n            return\r\n        }\r\n        return super._processRemoveIfError(changes, editIndex)\r\n    }\r\n    _beforeFocusElementInRow(rowIndex) {\r\n        super._beforeFocusElementInRow(rowIndex);\r\n        const editRowIndex = rowIndex >= 0 ? rowIndex : 0;\r\n        const columnIndex = this.getFirstEditableColumnIndex();\r\n        columnIndex >= 0 && this.editCell(editRowIndex, columnIndex)\r\n    }\r\n};\r\nconst rowsView = Base => class extends Base {\r\n    _createTable() {\r\n        const $table = super._createTable.apply(this, arguments);\r\n        const editingController = this._editingController;\r\n        if (editingController.isCellOrBatchEditMode() && this.option(\"editing.allowUpdating\")) {\r\n            eventsEngine.on($table, addNamespace(holdEvent.name, \"dxDataGridRowsView\"), `td:not(.${EDITOR_CELL_CLASS})`, this.createAction((() => {\r\n                if (editingController.isEditing()) {\r\n                    editingController.closeEditCell()\r\n                }\r\n            })))\r\n        }\r\n        return $table\r\n    }\r\n    _createRow(row) {\r\n        const $row = super._createRow.apply(this, arguments);\r\n        if (row) {\r\n            const editingController = this._editingController;\r\n            const isRowRemoved = !!row.removed;\r\n            if (editingController.isBatchEditMode()) {\r\n                isRowRemoved && $row.addClass(ROW_REMOVED)\r\n            }\r\n        }\r\n        return $row\r\n    }\r\n};\r\nexport const editingCellBasedModule = {\r\n    extenders: {\r\n        controllers: {\r\n            editing: editingControllerExtender\r\n        },\r\n        views: {\r\n            rowsView: rowsView\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;AACA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AAIA;AAgBA;;;;;;;;;;;;;;;AAGA,MAAM,4BAA4B,CAAA;IAAQ,qBAAc;QACpD,OAAO;YACH,MAAM,qBAAqB,CAAC,IAAI,CAAC,kBAAkB;YACnD,KAAK,CAAC;YACN,IAAI,oBAAoB;gBACpB,IAAI;gBACJ,IAAI;gBACJ,IAAI,CAAC,uBAAuB,GAAG;oBAC3B,IAAI;oBACJ,aAAa,SAAS,CAAC,wBAAwB,IAAI,CAAC,yBAAyB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,UAAU;gBAClK;gBACA,IAAI,CAAC,yBAAyB,GAAG,CAAA,IAAK,qBAAqB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM;gBACrE,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY,CAAE,SAAS,CAAC;oBACnD,MAAM,EACF,OAAO,KAAK,EACf,GAAG;oBACJ,MAAM,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,MAAM;oBAC9B,MAAM,kBAAkB,KAAK,CAAC,+LAAA,CAAA,wBAAqB,CAAC;oBACpD,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI;oBACR,IAAI,CAAA,GAAA,yMAAA,CAAA,aAAU,AAAD,EAAE,uBAAuB,CAAC,mBAAmB,EAAE,CAAC,UAAU;wBACnE;oBACJ;oBAEA,SAAS,iBAAiB,QAAQ;wBAC9B,IAAI,CAAC,UAAU;4BACX,OAAO;wBACX;wBACA,MAAM,yBAAyB,SAAS,OAAO,CAAC,AAAC,IAAiC,OAA9B,+LAAA,CAAA,gCAA6B;wBACjF,MAAM,oBAAoB,UAAU,QAAQ;wBAC5C,OAAO,uBAAuB,MAAM,GAAG,KAAK,MAAM,kBAAkB,OAAO,CAAC,wBAAwB,MAAM;oBAC9G;oBACA,IAAI,IAAI,CAAC,qBAAqB,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;wBAC3D,MAAM,gBAAgB,iBAAiB,YAAY,iBAAiB,SAAS,mBAAmB,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,QAAQ;wBAC9J,MAAM,qBAAqB,mBAAmB,CAAC,gBAAgB,SAAS,IAAI,oBAAoB,IAAI,CAAC,SAAS;wBAC9G,MAAM,iBAAiB,CAAC,CAAC,QAAQ,OAAO,CAAC,AAAC,IAA8C,OAA3C,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,uBAAoB,IAAK,MAAM;wBACjG,MAAM,iBAAiB,QAAQ,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,+LAAA,CAAA,sBAAmB;wBAChF,MAAM,iBAAiB,IAAI,CAAC,cAAc;wBAC1C,IAAI,CAAC,cAAc,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,CAAC,kBAAkB,kBAAkB,IAAI,CAAC,SAAS,EAAE,KAAK,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,kBAAkB,GAAG;4BAChK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE;wBACnC;oBACJ;gBACJ;gBACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,4KAAA,CAAA,UAAa,CAAC,EAAE,EAAE,IAAI,CAAC,uBAAuB;gBACxF,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,4KAAA,CAAA,UAAa,CAAC,IAAI,EAAE,IAAI,CAAC,yBAAyB;gBAC5F,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,0KAAA,CAAA,OAAc,EAAE,IAAI,CAAC,kBAAkB;YACrF;QACJ;QACA,iBAAiB;YACb,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,+LAAA,CAAA,iBAAc;QACzD;QACA,kBAAkB;YACd,OAAO,IAAI,CAAC,MAAM,CAAC,oBAAoB,+LAAA,CAAA,kBAAe;QAC1D;QACA,wBAAwB;YACpB,OAAO,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,eAAe;QACxD;QACA,yBAAyB,cAAc,EAAE;YACrC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,QAAQ;YACxC,IAAI,SAAS,IAAI,CAAC,SAAS;YAC3B,MAAM,4BAA4B,CAAC,YAAY,CAAC,CAAC,eAAe,OAAO,CAAC,UAAU,MAAM;YACxF,IAAI,2BAA2B;gBAC3B,MAAM,YAAY,eAAe,OAAO,CAAC,AAAC,IAAkB,OAAf,+LAAA,CAAA,iBAAc,GAAI,MAAM;gBACrE,IAAI,WAAW;oBACX,MAAM,cAAc,eAAe,OAAO,CAAC,AAAC,IAAa,OAAV,+LAAA,CAAA,YAAS,EAAC;oBACzD,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,YAAY,MAAM;oBAC9D,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC;oBACpD,IAAI,SAAS,gBAAgB,KAAK,MAAM,gBAAgB,aAAa,MAAM,EAAE;wBACzE,IAAI;wBACJ,MAAM,cAAc,aAAa,KAAK,CAAC;wBACvC,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;wBAChE,MAAM,eAAe,SAAS,CAAC,wBAAwB,cAAc,CAAC,YAAY,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,YAAY;wBACrK,MAAM,gBAAgB,IAAI,CAAC,UAAU,CAAC,UAAU;wBAChD,SAAS,UAAU,CAAC,gBAAgB,CAAC;oBACzC;gBACJ;YACJ;YACA,OAAO,UAAU,KAAK,CAAC,yBAAyB;QACpD;QACA,eAAe,cAAc,EAAE;YAC3B,IAAI,IAAI,CAAC,wBAAwB,CAAC,iBAAiB;gBAC/C,IAAI,CAAC,aAAa;YACtB;QACJ;QACA,qBAAqB;YACjB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBACvD,IAAI;gBACJ,MAAM,kBAAkB,IAAI,CAAC,0BAA0B;gBACvD,MAAM,QAAQ,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,eAAe,CAAC,IAAI,CAAC,uBAAuB,IAAI;gBACnK,IAAI,CAAC,gBAAgB,GAAG;gBACxB,aAAa,IAAI,CAAC,oBAAoB;gBACtC,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,UAAU,MAAM,EAAE;oBACvC,IAAI,CAAC,iBAAiB,CAAE;wBACpB,IAAI,CAAC,mBAAmB,GAAG;oBAC/B,GAAI,OAAO;gBACf,OAAO;oBACH,IAAI,CAAC,mBAAmB,GAAG;gBAC/B;gBACA,IAAI,CAAC,gBAAgB,GAAG;YAC5B,OAAO;gBACH,KAAK,CAAC;YACV;QACJ;QACA,YAAY;YACR,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,MAAM,sBAAsB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,iCAA8B;gBAChF,MAAM,0BAA0B,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,+LAAA,CAAA,qCAAkC;gBACxF,OAAO,uBAAuB;YAClC;YACA,OAAO,KAAK,CAAC;QACjB;QACA,4BAA4B,IAAI,EAAE;YAC9B,MAAM,cAAc,IAAI,CAAC,uBAAuB,CAAC,KAAK,aAAa;YACnE,IAAI,IAAI,CAAC,qBAAqB,MAAM,CAAC,MAAM,eAAe,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,aAAa,EAAE;gBAClH,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,KAAK,KAAK;gBAC5E,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,qBAAqB,CAAC,KAAK,aAAa;gBACvF,IAAI,CAAC,0BAA0B,CAAC,aAAa,gBAAgB;YACjE;QACJ;QACA,QAAQ,SAAS,EAAE;YACf,IAAI,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,UAAU,IAAI;gBAC5C,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;gBAC7B,IAAI,CAAC,YAAY,GAAG,IAAI,CAAE;oBACtB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;wBACpB,IAAI,CAAC,MAAM,CAAC,WAAW,IAAI,CAAC,SAAS,OAAO,EAAE,IAAI,CAAC,SAAS,MAAM;oBACtE,OAAO;wBACH,SAAS,MAAM,CAAC;oBACpB;gBACJ;gBACA,OAAO,SAAS,OAAO;YAC3B;YACA,OAAO,KAAK,CAAC,QAAQ;QACzB;QACA,SAAS,QAAQ,EAAE,WAAW,EAAE;YAC5B,OAAO,IAAI,CAAC,SAAS,CAAC;gBAClB,UAAU;gBACV,aAAa;YACjB;QACJ;QACA,UAAU,OAAO,EAAE;YACf,MAAM,IAAI,IAAI,oLAAA,CAAA,WAAQ;YACtB,IAAI;YACJ,IAAI,CAAC,gBAAgB,CAAC,GAAI;gBACtB,aAAa,IAAI,CAAC,aAAa,CAAC;gBAChC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,YAAY,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,MAAM;YAClD;YACA,OAAO,KAAK,MAAM,aAAa,aAAa,EAAE,OAAO;QACzD;QACA,cAAc,OAAO,EAAE;YACnB,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM,wBAAwB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,cAAc,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,WAAW;YAChG,MAAM,EACF,aAAa,WAAW,EACxB,UAAU,QAAQ,EAClB,QAAQ,MAAM,EACd,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,6BAA6B,CAAC;YACvC,MAAM,SAAS;gBACX,MAAM,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,IAAI;gBAC3D,QAAQ;gBACR,QAAQ;YACZ;YACA,IAAI,KAAK,MAAM,KAAK,GAAG,EAAE;gBACrB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;gBAC/B;YACJ;YACA,IAAI,UAAU,CAAC,WAAW,KAAK,OAAO,IAAI,qBAAqB,KAAK,OAAO,KAAK,CAAC,KAAK,OAAO,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC3H,IAAI,CAAC,yBAAyB,IAAI,CAAC,UAAU,CAAC,UAAU,cAAc;oBAClE,OAAO;gBACX;gBACA,MAAM,eAAe,WAAW,eAAe,iBAAiB;gBAChE,OAAO,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,aAAa,OAAO,IAAI,CAAE,CAAA;oBACjE,IAAI,QAAQ;wBACR;oBACJ;oBACA,IAAI,CAAC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,MAAM,aAAa,eAAe;wBACjE,IAAI,CAAC,2BAA2B;oBACpC;gBACJ;YACJ;YACA,OAAO;QACX;QACA,gBAAgB,QAAQ,EAAE,WAAW,EAAE,IAAI,EAAE;YACzC,IAAI,IAAI,CAAC,cAAc,MAAM,CAAC,KAAK,QAAQ,IAAI,IAAI,CAAC,UAAU,IAAI;gBAC9D,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;gBAC7B,IAAI,CAAC,YAAY,GAAG,MAAM,CAAE;oBACxB,SAAS,OAAO,CAAC,IAAI,CAAC,UAAU;gBACpC;gBACA,IAAI,CAAC,WAAW,CAAC;gBACjB,OAAO;YACX;YACA,OAAO;QACX;QACA,gBAAgB;YACZ,MAAM,gBAAgB,KAAK,CAAC;YAC5B,OAAO,cAAc,MAAM,CAAC;gBAAC;gBAAY;aAAgB;QAC7D;QACA,8BAA8B,IAAI,EAAE;YAChC,IAAI,EACA,gBAAgB,cAAc,EAC9B,aAAa,WAAW,EACxB,aAAa,WAAW,EACxB,UAAU,QAAQ,EACrB,GAAG;YACJ,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,MAAM,iBAAiB,kBAAkB,iBAAiB;YAC1D,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK;YACxC,MAAM,OAAO,KAAK,CAAC,SAAS;YAC5B,IAAI;YACJ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;gBAC3B,YAAY,cAAc,CAAC,eAAe;YAC9C,OAAO;gBACH,YAAY,IAAI,CAAC,cAAc;YACnC;YACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,cAAc;gBACzB,cAAc,IAAI,CAAC,uBAAuB;YAC9C;YACA,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,cAAc;gBACvB,cAAc,kBAAkB,YAAY,CAAC,aAAa;gBAC1D,cAAc,kBAAkB,eAAe,CAAC;YACpD;YACA,MAAM,SAAS,cAAc,CAAC,YAAY;YAC1C,OAAO;gBACH,WAAW;gBACX,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,QAAQ;gBACR,MAAM;YACV;QACJ;QACA,iBAAiB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,YAAY,EAAE;YAC1D,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAChB,OAAO,GAAG,GAAG,KAAK,GAAG;YACzB;YACA,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS;gBAC9B,OAAO;YACX;YACA,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,SAAS;YAChD,IAAI,CAAC,cAAc,CAAC,KAAK,GAAG;YAC5B,IAAI,CAAC,yBAAyB,CAAC;YAC/B,IAAI,CAAC,OAAO,MAAM,CAAC,gBAAgB,EAAE;oBAGpB;gBAFb,IAAI,CAAC,gBAAgB,CAAC;oBAClB,KAAK,KAAK,GAAG;oBACb,SAAS,CAAA,gBAAA,KAAK,OAAO,cAAZ,2BAAA,gBAAgB,KAAK,IAAI;gBACtC;YACJ;YACA,OAAO;QACX;QACA,cAAc,OAAO,EAAE,mBAAmB,EAAE;YACxC,IAAI,SAAS,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD;YAChB,MAAM,kBAAkB,IAAI,CAAC,uBAAuB;YACpD,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;gBAC7B,SAAS,IAAI,oLAAA,CAAA,WAAQ;gBACrB,IAAI,CAAC,gBAAgB,CAAC,UAAW;oBAC7B,IAAI,CAAC,kBAAkB,CAAC,SAAS,iBAAiB,qBAAqB,MAAM,CAAC,OAAO,OAAO;gBAChG;YACJ;YACA,OAAO,OAAO,OAAO;QACzB;QACA,mBAAmB,OAAO,EAAE,eAAe,EAAE,mBAAmB,EAAE;YAC9D,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;YAC7B,MAAM,UAAU,SAAS,OAAO;YAChC,IAAI,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,UAAU,IAAI;gBAC5C,IAAI,CAAC,qBAAqB;oBACtB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAE,CAAA;wBACtB,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI;4BACpB,IAAI,CAAC,aAAa,CAAC,CAAC,CAAC,OAAO,MAAM,CAAC,SAAS,OAAO;4BACnD;wBACJ;wBACA,SAAS,OAAO;oBACpB;oBACA,OAAO;gBACX;YACJ,OAAO;gBACH,IAAI,CAAC,gBAAgB;gBACrB,IAAI,CAAC,oBAAoB;gBACzB,IAAI,mBAAmB,GAAG;oBACtB,MAAM,aAAa;wBAAC;qBAAgB;oBACpC,IAAI,CAAC,+BAA+B,CAAC;oBACrC,IAAI,CAAC,SAAS;wBACV,eAAe,WAAW,CAAC;4BACvB,YAAY;4BACZ,YAAY;wBAChB;oBACJ;gBACJ;YACJ;YACA,SAAS,OAAO;YAChB,OAAO;QACX;QACA,yBAAyB,OAAO,EAAE;YAC9B,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,MAAM,eAAe,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,GAAG,MAAM;gBACvE,QAAQ,OAAO,CAAE,CAAA;oBACb,IAAI,EACA,KAAK,GAAG,EACX,GAAG;oBACJ,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;oBACvD,IAAK,IAAI,cAAc,GAAG,cAAc,cAAc,cAAe;wBACjE,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,eAAe,CAAC,UAAU;wBAC7D,SAAS,eAAe,KAAK,MAAM,eAAe,YAAY,WAAW,CAAC,+LAAA,CAAA,sBAAmB;oBACjG;gBACJ;YACJ;QACJ;QACA,eAAe,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE;YACjC,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,WAAW;YAC1C,IAAI,IAAI,CAAC,eAAe,MAAM,KAAK,MAAM,QAAQ,GAAG,EAAE;gBAClD,IAAI,CAAC,cAAc,CAAC,cAAc;YACtC;YACA,OAAO,KAAK,CAAC,eAAe,SAAS,OAAO;QAChD;QACA,cAAc,MAAM,EAAE;YAClB,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,IAAI,IAAI,CAAC,eAAe,IAAI;oBACxB,IAAI,CAAC,iBAAiB;gBAC1B;gBACA,eAAe,WAAW;YAC9B;YACA,KAAK,CAAC,cAAc;QACxB;QACA,cAAc,IAAI,EAAE;YAChB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;YACJ,IAAI,cAAc,KAAK,IAAI,IAAI,aAAa,+LAAA,CAAA,qCAAkC,EAAE;gBAC5E,IAAI,CAAC,2BAA2B,CAAC;gBACjC,KAAK,OAAO,GAAG;YACnB,OAAO;gBACH,KAAK,CAAC,cAAc;YACxB;QACJ;QACA,2BAA2B,WAAW,EAAE,cAAc,EAAE,WAAW,EAAE;YACjE,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;YACzD,IAAI,cAAc,CAAC,GAAG;gBAClB,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;oBACT,IAAI,CAAC,gBAAgB,CAAC,OAAO,CAAC,YAAY,EAAE,OAAO,CAAC,eAAe,EAAE;gBACzE;YACJ;QACJ;QACA,wBAAwB,IAAI,EAAE;YAC1B,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,MAAM,cAAc,IAAI,CAAC,0BAA0B;gBACnD,MAAM,wBAAwB,IAAI,CAAC,0BAA0B;gBAC7D,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,KAAK,aAAa,IAAI;gBAChF,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,aAAa,EAAE;oBAC5D,IAAI;oBACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,0BAA0B,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,IAAI,EAAE,aAAa,aAAa;gBACzK;YACJ,OAAO;gBACH,KAAK,CAAC,wBAAwB;YAClC;QACJ;QACA,UAAU,QAAQ,EAAE;YAChB,IAAI,IAAI,CAAC,cAAc,MAAM,IAAI,CAAC,SAAS,IAAI;gBAC3C,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,SAAS;gBAC1C,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;gBACrD,IAAI,CAAC,aAAa,CAAC,MAAM,UAAU,MAAM,CAAE;oBACvC,WAAW,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;oBACjD,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;YACJ,OAAO;gBACH,KAAK,CAAC,UAAU;YACpB;QACJ;QACA,mBAAmB,QAAQ,EAAE;YACzB,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;gBACH,KAAK,CAAC,mBAAmB;YAC7B;QACJ;QACA,aAAa,MAAM,EAAE;YACjB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,mBAAA,oBAAA,SAAU,CAAC;YACf,MAAM,mBAAmB,IAAI,CAAC,eAAe,MAAM,iBAAiB,cAAc,IAAI,CAAC,MAAM,CAAC;YAC9F,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,IAAI,kBAAkB;oBAClB,IAAI,CAAC,oBAAoB;oBACzB,IAAI,CAAC,gBAAgB;gBACzB;YACJ,OAAO;gBACH,KAAK,CAAC,aAAa;YACvB;QACJ;QACA,gBAAgB,MAAM,EAAE;YACpB,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,OAAO;YACX;YACA,OAAO,KAAK,CAAC,gBAAgB;QACjC;QACA,gBAAgB,QAAQ,EAAE,eAAe,EAAE;YACvC,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB,eAAe,WAAW,CAAC;oBACvB,YAAY;oBACZ,YAAY;wBAAC;wBAAiB;qBAAS;gBAC3C;gBACA,OAAO,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO;YACjC;YACA,OAAO,KAAK,CAAC,gBAAgB,UAAU;QAC3C;QACA,eAAe,GAAG,EAAE,cAAc,EAAE,oBAAoB,EAAE;YACtD,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,IAAI,CAAC,qBAAqB,CAAC,KAAK,gBAAgB;YACpD,OAAO;gBACH,KAAK,CAAC,eAAe,KAAK,gBAAgB;YAC9C;QACJ;QACA,wBAAwB,MAAM,EAAE,OAAO,EAAE;YACrC,IAAI,IAAI,CAAC,qBAAqB,IAAI;gBAC9B,MAAM,cAAc,IAAI,CAAC,eAAe;gBACxC,OAAQ,OAAO,IAAI;oBACf,KAAK;oBACL,KAAK;oBACL,KAAK;wBACD,OAAO;oBACX,KAAK;wBACD,OAAO,KAAK,CAAC,wBAAwB,QAAQ,YAAY,CAAC,CAAC,eAAe,CAAC,QAAQ,GAAG,CAAC,OAAO;oBAClG,KAAK;wBACD,OAAO,eAAe,IAAI,CAAC,aAAa,CAAC,YAAY,QAAQ,GAAG,CAAC,OAAO;oBAC5E;wBACI,OAAO,KAAK,CAAC,wBAAwB,QAAQ;gBACrD;YACJ;YACA,OAAO,KAAK,CAAC,wBAAwB,QAAQ;QACjD;QACA,sBAAsB;YAClB,MAAM,iBAAiB,KAAK,CAAC;YAC7B,OAAO,kBAAkB,IAAI,CAAC,eAAe;QACjD;QACA,iBAAiB,OAAO,EAAE;YACtB,IAAI,IAAI,CAAC,cAAc,IAAI;gBACvB,IAAI;gBACJ,IAAI,aAAa,CAAC,SAAS,CAAC,YAAY,OAAO,CAAC,EAAE,KAAK,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,IAAI,GAAG;oBACpG,KAAK,CAAC,iBAAiB;gBAC3B;YACJ,OAAO;gBACH,IAAI,IAAI,CAAC,eAAe,IAAI;oBACxB,IAAI,CAAC,wBAAwB,CAAC;gBAClC;gBACA,KAAK,CAAC,iBAAiB;YAC3B;QACJ;QACA,mBAAmB,WAAW,EAAE;gBACL;YAAvB,MAAM,iBAAiB,CAAA,eAAA,IAAI,CAAC,MAAM,CAAC,wBAAZ,0BAAA,eAA0B,CAAC;YAClD,MAAM,cAAc,KAAK,CAAC,mBAAmB;YAC7C,MAAM,qBAAqB,eAAe,aAAa,IAAI,eAAe,WAAW,IAAI,eAAe,aAAa;YACrH,IAAI,sBAAsB,IAAI,CAAC,eAAe,IAAI;gBAC9C,YAAY,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,QAAQ,gBAAgB;gBAC7E,YAAY,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,aAAa,UAAU,kBAAkB;YACrF;YACA,OAAO;QACX;QACA,qBAAqB;YACjB,IAAI;YACJ,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,cAAc,EAAE,CAAC,IAAI,CAAC,eAAe,GAAG;YAC7E,MAAM,aAAa,IAAI,CAAC,cAAc;YACtC,MAAM,mBAAmB,SAAS,cAAc,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,gBAAgB;YAC5G,MAAM,qBAAqB,IAAI,CAAC,cAAc,MAAM,CAAC,CAAC,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,QAAQ;YAChH,IAAI;YACJ,IAAI,sBAAsB,kBAAkB;gBACxC,WAAW,IAAI,oLAAA,CAAA,WAAQ;gBACvB,IAAI,CAAC,WAAW,CAAC;YACrB;YACA,OAAO,KAAK,CAAC,qBAAqB,MAAM,CAAC,SAAS,CAAC,YAAY,QAAQ,KAAK,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,OAAO;QACjI;QACA,aAAa,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE;YAC1C,MAAM,qBAAqB,IAAI,CAAC,cAAc,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ;YACxF,MAAM,EACF,kBAAkB,gBAAgB,EACrC,GAAG,QAAQ,MAAM;YAClB,MAAM,uBAAuB,QAAQ,MAAM,CAAC,YAAY,KAAK,QAAQ,MAAM,CAAC,mBAAmB;YAC/F,MAAM,2BAA2B,oBAAoB,CAAC,kBAAkB,sBAAsB,IAAI,CAAC,WAAW,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,QAAQ,QAAQ,EAAE,QAAQ,WAAW;YAC1K,IAAI,0BAA0B;gBAC1B,IAAI,CAAC,iBAAiB;gBACtB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,EAAE,MAAM;gBACvC;YACJ;YACA,OAAO,KAAK,CAAC,aAAa,SAAS,QAAQ;QAC/C;QACA,iBAAiB,OAAO,EAAE,cAAc,EAAE;YACtC,MAAM,EACF,kBAAkB,gBAAgB,EACrC,GAAG,QAAQ,MAAM;YAClB,MAAM,qBAAqB,IAAI,CAAC,cAAc,MAAM,QAAQ,GAAG,IAAI,CAAC,QAAQ,GAAG,CAAC,QAAQ;YACxF,IAAI,oBAAoB,CAAC,gBAAgB;gBACrC,IAAI,oBAAoB;oBACpB,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,CAAC,GAAG,EAAE;oBACrC,IAAI,CAAC,yBAAyB,CAAC,QAAQ,WAAW,EAAE;oBACpD,OAAO,IAAI,CAAC,YAAY;gBAC5B;gBACA,IAAI,IAAI,CAAC,eAAe,IAAI;oBACxB,iBAAiB,IAAI,CAAC,cAAc,CAAC,QAAQ,MAAM;oBACnD,OAAO,KAAK,CAAC,iBAAiB,SAAS;gBAC3C;YACJ;YACA,OAAO,KAAK,CAAC,iBAAiB,SAAS;QAC3C;QACA,qBAAqB,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,kBAAkB,EAAE;YACjE,MAAM,EACF,MAAM,IAAI,EACV,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,IAAI,CAAC,eAAe,MAAM,SAAS,+LAAA,CAAA,6BAA0B,EAAE;gBAC/D,KAAK,IAAI,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,KAAK,IAAI,EAAE;YACnD;YACA,KAAK,CAAC,qBAAqB,MAAM,QAAQ,KAAK,SAAS;QAC3D;QACA,mBAAmB,OAAO,EAAE,SAAS,EAAE,cAAc,EAAE;YACnD,IAAI,IAAI,CAAC,eAAe,MAAM,CAAC,gBAAgB;gBAC3C;YACJ;YACA,OAAO,KAAK,CAAC,mBAAmB,SAAS,WAAW;QACxD;QACA,sBAAsB,OAAO,EAAE,SAAS,EAAE;YACtC,IAAI,IAAI,CAAC,eAAe,IAAI;gBACxB;YACJ;YACA,OAAO,KAAK,CAAC,sBAAsB,SAAS;QAChD;QACA,yBAAyB,QAAQ,EAAE;YAC/B,KAAK,CAAC,yBAAyB;YAC/B,MAAM,eAAe,YAAY,IAAI,WAAW;YAChD,MAAM,cAAc,IAAI,CAAC,2BAA2B;YACpD,eAAe,KAAK,IAAI,CAAC,QAAQ,CAAC,cAAc;QACpD;IACJ;;;AACA,MAAM,WAAW,CAAA;IAAQ,qBAAc;QACnC,eAAe;YACX,MAAM,SAAS,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE;YAC9C,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,IAAI,kBAAkB,qBAAqB,MAAM,IAAI,CAAC,MAAM,CAAC,0BAA0B;gBACnF,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,yKAAA,CAAA,UAAS,CAAC,IAAI,EAAE,uBAAuB,AAAC,WAA4B,OAAlB,+LAAA,CAAA,oBAAiB,EAAC,MAAI,IAAI,CAAC,YAAY,CAAE;oBAC5H,IAAI,kBAAkB,SAAS,IAAI;wBAC/B,kBAAkB,aAAa;oBACnC;gBACJ;YACJ;YACA,OAAO;QACX;QACA,WAAW,GAAG,EAAE;YACZ,MAAM,OAAO,KAAK,CAAC,WAAW,KAAK,CAAC,IAAI,EAAE;YAC1C,IAAI,KAAK;gBACL,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;gBACjD,MAAM,eAAe,CAAC,CAAC,IAAI,OAAO;gBAClC,IAAI,kBAAkB,eAAe,IAAI;oBACrC,gBAAgB,KAAK,QAAQ,CAAC,+LAAA,CAAA,cAAW;gBAC7C;YACJ;YACA,OAAO;QACX;IACJ;;;AACO,MAAM,yBAAyB;IAClC,WAAW;QACP,aAAa;YACT,SAAS;QACb;QACA,OAAO;YACH,UAAU;QACd;IACJ;AACJ", "ignoreList": [0], "debugId": null}}]}