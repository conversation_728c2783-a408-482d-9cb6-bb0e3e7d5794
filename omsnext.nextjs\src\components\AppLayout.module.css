/* App Layout stílusok */
.appLayout {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: var(--base-bg);
  font-family: "Inter", sans-serif;
}

.content {
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
}

.toolbar {
  background-color: white;
  border-bottom: 1px solid var(--border-color);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  z-index: 1000;
}

.pageContent {
  flex: 1;
  padding: var(--content-padding);
  overflow-y: auto;
  background-color: var(--base-bg);
}

/* Side Menu stílusok */
.sideMenu {
  width: 280px;
  height: 100%;
  background-color: white;
  border-right: 1px solid var(--border-color);
  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.menuHeader {
  padding: 24px 20px;
  border-bottom: 1px solid var(--border-color);
  background-color: var(--accent-color);
  color: white;
}

.menuHeader h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 500;
}

.menuItems {
  flex: 1;
  padding: 16px 0;
}

.menuItem {
  display: flex;
  align-items: center;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  color: var(--base-text-color);
}

.menuItem:hover {
  background-color: #f5f5f5;
}

.menuIcon {
  margin-right: 12px;
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* DevExtreme komponensek felülírása */
.appLayout :global(.dx-toolbar) {
  background-color: white;
  border: none;
  padding: 0 16px;
  height: 56px;
}

.appLayout :global(.dx-toolbar-item) {
  margin: 0 4px;
}

.appLayout :global(.dx-toolbar-center) {
  font-size: 18px;
  font-weight: 500;
  color: var(--base-text-color);
}

.appLayout :global(.dx-button.dx-button-mode-text) {
  color: var(--base-text-color);
}

.appLayout :global(.dx-button.dx-button-mode-text:hover) {
  background-color: rgba(0, 0, 0, 0.04);
}

.appLayout :global(.dx-drawer-panel-content) {
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
}

/* Responsive design */
@media (max-width: 768px) {
  .sideMenu {
    width: 260px;
  }
  
  .pageContent {
    padding: 16px;
  }
  
  .menuHeader {
    padding: 20px 16px;
  }
  
  .menuItem {
    padding: 14px 16px;
  }
}

/* Dark mode támogatás */
.dark .sideMenu {
  background-color: #2d2d2d;
  border-right-color: #404040;
}

.dark .menuHeader {
  background-color: var(--accent-color);
}

.dark .menuItem:hover {
  background-color: #404040;
}

.dark .appLayout :global(.dx-toolbar) {
  background-color: #2d2d2d;
  border-bottom-color: #404040;
}

.dark .toolbar {
  border-bottom-color: #404040;
}

/* Card stílusok a tartalomhoz */
.pageContent :global(.dx-card) {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.04);
  border: none;
  margin-bottom: 20px;
}

.dark .pageContent :global(.dx-card) {
  background-color: #2d2d2d;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.3);
}
