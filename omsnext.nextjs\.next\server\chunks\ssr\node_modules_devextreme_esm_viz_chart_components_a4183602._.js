module.exports = {

"[project]/node_modules/devextreme/esm/viz/chart_components/crosshair.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/crosshair.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Crosshair": ()=>Crosshair,
    "getMargins": ()=>getMargins
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
;
;
const math = Math;
const mathAbs = math.abs;
const mathMin = math.min;
const mathMax = math.max;
const mathFloor = math.floor;
const HORIZONTAL = "horizontal";
const VERTICAL = "vertical";
const LABEL_BACKGROUND_PADDING_X = 8;
const LABEL_BACKGROUND_PADDING_Y = 4;
const CENTER = "center";
const RIGHT = "right";
const LEFT = "left";
const TOP = "top";
const BOTTOM = "bottom";
function getMargins() {
    return {
        x: 8,
        y: 4
    };
}
function getRectangleBBox(bBox) {
    return {
        x: bBox.x - 8,
        y: bBox.y - 4,
        width: bBox.width + 16,
        height: bBox.height + 8
    };
}
function getLabelCheckerPosition(x, y, isHorizontal, canvas) {
    const params = isHorizontal ? [
        "x",
        "width",
        "y",
        "height",
        y,
        0
    ] : [
        "y",
        "height",
        "x",
        "width",
        x,
        1
    ];
    return function(bBox, position, coord) {
        const labelCoord = {
            x: coord.x,
            y: coord.y
        };
        const rectangleBBox = getRectangleBBox(bBox);
        const delta = isHorizontal ? coord.y - bBox.y - bBox.height / 2 : coord.y - bBox.y;
        labelCoord.y = isHorizontal || !isHorizontal && position === BOTTOM ? coord.y + delta : coord.y;
        if (rectangleBBox[params[0]] < 0) {
            labelCoord[params[0]] -= rectangleBBox[params[0]];
        } else if (rectangleBBox[params[0]] + rectangleBBox[params[1]] + delta * params[5] > canvas[params[1]]) {
            labelCoord[params[0]] -= rectangleBBox[params[0]] + rectangleBBox[params[1]] + delta * params[5] - canvas[params[1]];
        }
        if (params[4] - rectangleBBox[params[3]] / 2 < 0) {
            labelCoord[params[2]] -= params[4] - rectangleBBox[params[3]] / 2;
        } else if (params[4] + rectangleBBox[params[3]] / 2 > canvas[params[3]]) {
            labelCoord[params[2]] -= params[4] + rectangleBBox[params[3]] / 2 - canvas[params[3]];
        }
        return labelCoord;
    };
}
function Crosshair(renderer, options, params, group) {
    this._renderer = renderer;
    this._crosshairGroup = group;
    this._options = {};
    this.update(options, params);
}
Crosshair.prototype = {
    constructor: Crosshair,
    update: function(options, params) {
        const canvas = params.canvas;
        this._canvas = {
            top: canvas.top,
            bottom: canvas.height - canvas.bottom,
            left: canvas.left,
            right: canvas.width - canvas.right,
            width: canvas.width,
            height: canvas.height
        };
        this._axes = params.axes;
        this._panes = params.panes;
        this._prepareOptions(options, HORIZONTAL);
        this._prepareOptions(options, VERTICAL);
    },
    dispose: function() {
        this._renderer = this._crosshairGroup = this._options = this._axes = this._canvas = this._horizontalGroup = this._verticalGroup = this._horizontal = this._vertical = this._circle = this._panes = null;
    },
    _prepareOptions: function(options, direction) {
        const lineOptions = options[direction + "Line"];
        this._options[direction] = {
            visible: lineOptions.visible,
            line: {
                stroke: lineOptions.color || options.color,
                "stroke-width": lineOptions.width || options.width,
                dashStyle: lineOptions.dashStyle || options.dashStyle,
                opacity: lineOptions.opacity || options.opacity,
                "stroke-linecap": "butt"
            },
            label: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, options.label, lineOptions.label)
        };
    },
    _createLines: function(options, sharpParam, group) {
        const lines = [];
        const canvas = this._canvas;
        const points = [
            canvas.left,
            canvas.top,
            canvas.left,
            canvas.top
        ];
        for(let i = 0; i < 2; i++){
            lines.push(this._renderer.path(points, "line").attr(options).sharp(sharpParam).append(group));
        }
        return lines;
    },
    render: function() {
        const that = this;
        const renderer = that._renderer;
        const options = that._options;
        const verticalOptions = options.vertical;
        const horizontalOptions = options.horizontal;
        const extraOptions = horizontalOptions.visible ? horizontalOptions.line : verticalOptions.line;
        const circleOptions = {
            stroke: extraOptions.stroke,
            "stroke-width": extraOptions["stroke-width"],
            dashStyle: extraOptions.dashStyle,
            opacity: extraOptions.opacity
        };
        const canvas = that._canvas;
        that._horizontal = {};
        that._vertical = {};
        that._circle = renderer.circle(canvas.left, canvas.top, 0).attr(circleOptions).append(that._crosshairGroup);
        that._horizontalGroup = renderer.g().append(that._crosshairGroup);
        that._verticalGroup = renderer.g().append(that._crosshairGroup);
        if (verticalOptions.visible) {
            that._vertical.lines = that._createLines(verticalOptions.line, "h", that._verticalGroup);
            that._vertical.labels = that._createLabels(that._axes[0], verticalOptions, false, that._verticalGroup);
        }
        if (horizontalOptions.visible) {
            that._horizontal.lines = that._createLines(horizontalOptions.line, "v", that._horizontalGroup);
            that._horizontal.labels = that._createLabels(that._axes[1], horizontalOptions, true, that._horizontalGroup);
        }
        that.hide();
    },
    _createLabels: function(axes, options, isHorizontal, group) {
        const canvas = this._canvas;
        const renderer = this._renderer;
        let x;
        let y;
        let text;
        const labels = [];
        let background;
        let currentLabelPos;
        const labelOptions = options.label;
        if (labelOptions.visible) {
            axes.forEach(function(axis) {
                const position = axis.getOptions().position;
                if (axis.getTranslator().getBusinessRange().isEmpty()) {
                    return;
                }
                currentLabelPos = axis.getLabelsPosition();
                if (isHorizontal) {
                    y = canvas.top;
                    x = currentLabelPos;
                } else {
                    x = canvas.left;
                    y = currentLabelPos;
                }
                const align = position === TOP || position === BOTTOM ? CENTER : position === RIGHT ? LEFT : RIGHT;
                background = renderer.rect(0, 0, 0, 0).attr({
                    fill: labelOptions.backgroundColor || options.line.stroke
                }).append(group);
                text = renderer.text("0", 0, 0).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.label.font)).attr({
                    align: align,
                    class: labelOptions.cssClass
                }).append(group);
                labels.push({
                    text: text,
                    background: background,
                    axis: axis,
                    options: labelOptions,
                    pos: {
                        coord: currentLabelPos,
                        side: position
                    },
                    startXY: {
                        x: x,
                        y: y
                    }
                });
            });
        }
        return labels;
    },
    _updateText: function(value, axisName, labels, point, func) {
        const that = this;
        labels.forEach(function(label) {
            const axis = label.axis;
            const coord = label.startXY;
            const textElement = label.text;
            const backgroundElement = label.background;
            let text = "";
            if (!axis.name || axis.name === axisName) {
                text = axis.getFormattedValue(value, label.options, point);
            }
            if (text) {
                textElement.attr({
                    text: text,
                    x: coord.x,
                    y: coord.y
                });
                textElement.attr(func(textElement.getBBox(), label.pos.side, coord));
                that._updateLinesCanvas(label);
                backgroundElement.attr(getRectangleBBox(textElement.getBBox()));
            } else {
                textElement.attr({
                    text: ""
                });
                backgroundElement.attr({
                    x: 0,
                    y: 0,
                    width: 0,
                    height: 0
                });
            }
        });
    },
    hide: function() {
        this._crosshairGroup.attr({
            visibility: "hidden"
        });
    },
    _updateLinesCanvas: function(label) {
        const position = label.pos.side;
        const labelCoord = label.pos.coord;
        const coords = this._linesCanvas;
        const canvas = this._canvas;
        coords[position] = coords[position] !== canvas[position] && mathAbs(coords[position] - canvas[position]) < mathAbs(labelCoord - canvas[position]) ? coords[position] : labelCoord;
    },
    _updateLines: function(lines, x, y, r, isHorizontal) {
        const coords = this._linesCanvas;
        const canvas = this._canvas;
        const points = isHorizontal ? [
            [
                mathMin(x - r, coords.left),
                canvas.top,
                x - r,
                canvas.top
            ],
            [
                x + r,
                canvas.top,
                mathMax(coords.right, x + r),
                canvas.top
            ]
        ] : [
            [
                canvas.left,
                mathMin(coords.top, y - r),
                canvas.left,
                y - r
            ],
            [
                canvas.left,
                y + r,
                canvas.left,
                mathMax(coords.bottom, y + r)
            ]
        ];
        for(let i = 0; i < 2; i++){
            lines[i].attr({
                points: points[i]
            }).sharp(isHorizontal ? "v" : "h", isHorizontal ? y === canvas.bottom ? -1 : 1 : x === canvas.right ? -1 : 1);
        }
    },
    _resetLinesCanvas: function() {
        const canvas = this._canvas;
        this._linesCanvas = {
            left: canvas.left,
            right: canvas.right,
            top: canvas.top,
            bottom: canvas.bottom
        };
    },
    _getClipRectForPane: function(x, y) {
        const panes = this._panes;
        let i;
        let coords;
        for(i = 0; i < panes.length; i++){
            coords = panes[i].coords;
            if (coords.left <= x && coords.right >= x && coords.top <= y && coords.bottom >= y) {
                return panes[i].clipRect;
            }
        }
        return {
            id: null
        };
    },
    show: function(data) {
        const that = this;
        const point = data.point;
        const pointData = point.getCrosshairData(data.x, data.y);
        const r = point.getPointRadius();
        const horizontal = that._horizontal;
        const vertical = that._vertical;
        const rad = !r ? 0 : r + 3;
        const canvas = that._canvas;
        const x = mathFloor(pointData.x);
        const y = mathFloor(pointData.y);
        if (x >= canvas.left && x <= canvas.right && y >= canvas.top && y <= canvas.bottom) {
            that._crosshairGroup.attr({
                visibility: "visible"
            });
            that._resetLinesCanvas();
            that._circle.attr({
                cx: x,
                cy: y,
                r: rad,
                "clip-path": that._getClipRectForPane(x, y).id
            });
            if (horizontal.lines) {
                that._updateText(pointData.yValue, pointData.axis, horizontal.labels, point, getLabelCheckerPosition(x, y, true, canvas));
                that._updateLines(horizontal.lines, x, y, rad, true);
                that._horizontalGroup.attr({
                    translateY: y - canvas.top
                });
            }
            if (vertical.lines) {
                that._updateText(pointData.xValue, pointData.axis, vertical.labels, point, getLabelCheckerPosition(x, y, false, canvas));
                that._updateLines(vertical.lines, x, y, rad, false);
                that._verticalGroup.attr({
                    translateX: x - canvas.left
                });
            }
        } else {
            that.hide();
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/chart_components/layout_manager.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/layout_manager.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "LayoutManager": ()=>LayoutManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/consts.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/layout_element.js [app-ssr] (ecmascript)");
;
;
;
const { floor: floor, sqrt: sqrt } = Math;
const _min = Math.min;
const _max = Math.max;
const DEFAULT_INNER_RADIUS = .5;
const RADIAL_LABEL_INDENT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].radialLabelIndent;
function getNearestCoord(firstCoord, secondCoord, pointCenterCoord) {
    let nearestCoord;
    if (pointCenterCoord < firstCoord) {
        nearestCoord = firstCoord;
    } else if (secondCoord < pointCenterCoord) {
        nearestCoord = secondCoord;
    } else {
        nearestCoord = pointCenterCoord;
    }
    return nearestCoord;
}
function getLabelLayout(point) {
    if (point._label.isVisible() && "inside" !== point._label.getLayoutOptions().position) {
        return point._label.getBoundingRect();
    }
}
function getPieRadius(series, paneCenterX, paneCenterY, accessibleRadius, minR) {
    series.some(function(singleSeries) {
        return singleSeries.getVisiblePoints().reduce(function(radiusIsFound, point) {
            const labelBBox = getLabelLayout(point);
            if (labelBBox) {
                const xCoords = getNearestCoord(labelBBox.x, labelBBox.x + labelBBox.width, paneCenterX);
                const yCoords = getNearestCoord(labelBBox.y, labelBBox.y + labelBBox.height, paneCenterY);
                accessibleRadius = _min(_max(getLengthFromCenter(xCoords, yCoords, paneCenterX, paneCenterY) - RADIAL_LABEL_INDENT, minR), accessibleRadius);
                radiusIsFound = true;
            }
            return radiusIsFound;
        }, false);
    });
    return accessibleRadius;
}
function getSizeLabels(series) {
    return series.reduce(function(res, singleSeries) {
        let maxWidth = singleSeries.getVisiblePoints().reduce(function(width, point) {
            const labelBBox = getLabelLayout(point);
            if (labelBBox && labelBBox.width > width) {
                width = labelBBox.width;
            }
            return width;
        }, 0);
        let rWidth = maxWidth;
        if (maxWidth) {
            res.outerLabelsCount++;
            if (res.outerLabelsCount > 1) {
                maxWidth += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].pieLabelSpacing;
            }
            rWidth += __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].pieLabelSpacing;
        }
        res.sizes.push(maxWidth);
        res.rSizes.push(rWidth);
        res.common += maxWidth;
        return res;
    }, {
        sizes: [],
        rSizes: [],
        common: 0,
        outerLabelsCount: 0
    });
}
function correctLabelRadius(labelSizes, radius, series, canvas, averageWidthLabels, centerX) {
    let curRadius;
    let i;
    let runningWidth = 0;
    const sizes = labelSizes.sizes;
    const rSizes = labelSizes.rSizes;
    for(i = 0; i < series.length; i++){
        if (0 === sizes[i]) {
            curRadius && (curRadius += rSizes[i - 1]);
            continue;
        }
        curRadius = floor(curRadius ? curRadius + rSizes[i - 1] : radius);
        series[i].correctLabelRadius(curRadius);
        runningWidth += averageWidthLabels || sizes[i];
        rSizes[i] = averageWidthLabels || rSizes[i];
        series[i].setVisibleArea({
            left: floor(centerX - radius - runningWidth),
            right: floor(canvas.width - (centerX + radius + runningWidth)),
            top: canvas.top,
            bottom: canvas.bottom,
            width: canvas.width,
            height: canvas.height
        });
    }
}
function getLengthFromCenter(x, y, paneCenterX, paneCenterY) {
    return sqrt((x - paneCenterX) * (x - paneCenterX) + (y - paneCenterY) * (y - paneCenterY));
}
function getInnerRadius(_ref) {
    let { type: type, innerRadius: innerRadius } = _ref;
    return "pie" === type ? 0 : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(innerRadius) ? Number(innerRadius) : .5;
}
function LayoutManager() {}
function getAverageLabelWidth(centerX, radius, canvas, sizeLabels) {
    return (centerX - radius - RADIAL_LABEL_INDENT - canvas.left) / sizeLabels.outerLabelsCount;
}
function getFullRadiusWithLabels(centerX, canvas, sizeLabels) {
    return centerX - canvas.left - (sizeLabels.outerLabelsCount > 0 ? sizeLabels.common + RADIAL_LABEL_INDENT : 0);
}
function correctAvailableRadius(availableRadius, canvas, series, minR, paneCenterX, paneCenterY) {
    const sizeLabels = getSizeLabels(series);
    let averageWidthLabels;
    const fullRadiusWithLabels = getFullRadiusWithLabels(paneCenterX, canvas, sizeLabels);
    if (fullRadiusWithLabels < minR) {
        availableRadius = minR;
        averageWidthLabels = getAverageLabelWidth(paneCenterX, availableRadius, canvas, sizeLabels);
    } else {
        availableRadius = _min(getPieRadius(series, paneCenterX, paneCenterY, availableRadius, minR), fullRadiusWithLabels);
    }
    correctLabelRadius(sizeLabels, availableRadius + RADIAL_LABEL_INDENT, series, canvas, averageWidthLabels, paneCenterX);
    return availableRadius;
}
function toLayoutElementCoords(canvas) {
    return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WrapperLayoutElement"](null, {
        x: canvas.left,
        y: canvas.top,
        width: canvas.width - canvas.left - canvas.right,
        height: canvas.height - canvas.top - canvas.bottom
    });
}
LayoutManager.prototype = {
    constructor: LayoutManager,
    setOptions: function(options) {
        this._options = options;
    },
    applyPieChartSeriesLayout: function(canvas, series, hideLayoutLabels) {
        const paneSpaceHeight = canvas.height - canvas.top - canvas.bottom;
        const paneSpaceWidth = canvas.width - canvas.left - canvas.right;
        const paneCenterX = paneSpaceWidth / 2 + canvas.left;
        const paneCenterY = paneSpaceHeight / 2 + canvas.top;
        const piePercentage = this._options.piePercentage;
        let availableRadius;
        let minR;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(piePercentage)) {
            availableRadius = minR = piePercentage * _min(canvas.height, canvas.width) / 2;
        } else {
            availableRadius = _min(paneSpaceWidth, paneSpaceHeight) / 2;
            minR = this._options.minPiePercentage * availableRadius;
        }
        if (!hideLayoutLabels) {
            availableRadius = correctAvailableRadius(availableRadius, canvas, series, minR, paneCenterX, paneCenterY);
        }
        return {
            centerX: floor(paneCenterX),
            centerY: floor(paneCenterY),
            radiusInner: floor(availableRadius * getInnerRadius(series[0])),
            radiusOuter: floor(availableRadius)
        };
    },
    applyEqualPieChartLayout: function(series, layout) {
        const radius = layout.radius;
        return {
            centerX: floor(layout.x),
            centerY: floor(layout.y),
            radiusInner: floor(radius * getInnerRadius(series[0])),
            radiusOuter: floor(radius)
        };
    },
    correctPieLabelRadius: function(series, layout, canvas) {
        const sizeLabels = getSizeLabels(series);
        let averageWidthLabels;
        const radius = layout.radiusOuter + RADIAL_LABEL_INDENT;
        const availableLabelWidth = layout.centerX - canvas.left - radius;
        if (sizeLabels.common + RADIAL_LABEL_INDENT > availableLabelWidth) {
            averageWidthLabels = getAverageLabelWidth(layout.centerX, layout.radiusOuter, canvas, sizeLabels);
        }
        correctLabelRadius(sizeLabels, radius, series, canvas, averageWidthLabels, layout.centerX);
    },
    needMoreSpaceForPanesCanvas (panes, rotated, fixedSizeCallback) {
        const options = this._options;
        const width = options.width;
        const height = options.height;
        const piePercentage = options.piePercentage;
        const percentageIsValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(piePercentage);
        let needHorizontalSpace = 0;
        let needVerticalSpace = 0;
        panes.forEach((pane)=>{
            const paneCanvas = pane.canvas;
            const minSize = percentageIsValid ? _min(paneCanvas.width, paneCanvas.height) * piePercentage : void 0;
            const paneSized = fixedSizeCallback ? fixedSizeCallback(pane) : {
                width: false,
                height: false
            };
            const needPaneHorizontalSpace = !paneSized.width ? (percentageIsValid ? minSize : width) - (paneCanvas.width - paneCanvas.left - paneCanvas.right) : 0;
            const needPaneVerticalSpace = !paneSized.height ? (percentageIsValid ? minSize : height) - (paneCanvas.height - paneCanvas.top - paneCanvas.bottom) : 0;
            if (rotated) {
                needHorizontalSpace += needPaneHorizontalSpace > 0 ? needPaneHorizontalSpace : 0;
                needVerticalSpace = _max(needPaneVerticalSpace > 0 ? needPaneVerticalSpace : 0, needVerticalSpace);
            } else {
                needHorizontalSpace = _max(needPaneHorizontalSpace > 0 ? needPaneHorizontalSpace : 0, needHorizontalSpace);
                needVerticalSpace += needPaneVerticalSpace > 0 ? needPaneVerticalSpace : 0;
            }
        });
        return needHorizontalSpace > 0 || needVerticalSpace > 0 ? {
            width: needHorizontalSpace,
            height: needVerticalSpace
        } : false;
    },
    layoutInsideLegend: function(legend, canvas) {
        const layoutOptions = legend.getLayoutOptions();
        if (!layoutOptions) {
            return;
        }
        const position = layoutOptions.position;
        const cutSide = layoutOptions.cutSide;
        const my = {
            horizontal: position.horizontal,
            vertical: position.vertical
        };
        canvas[layoutOptions.cutLayoutSide] += "horizontal" === layoutOptions.cutSide ? layoutOptions.width : layoutOptions.height;
        my[cutSide] = ({
            left: "right",
            right: "left",
            top: "bottom",
            bottom: "top",
            center: "center"
        })[my[cutSide]];
        legend.position({
            of: toLayoutElementCoords(canvas),
            my: my,
            at: position
        });
    }
};
;
}),
"[project]/node_modules/devextreme/esm/viz/chart_components/multi_axes_synchronizer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/multi_axes_synchronizer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/console.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
;
;
;
;
;
const _math = Math;
const _floor = _math.floor;
const _max = _math.max;
const _abs = _math.abs;
function getValueAxesPerPanes(valueAxes) {
    const result = {};
    valueAxes.forEach((axis)=>{
        const pane = axis.pane;
        if (!result[pane]) {
            result[pane] = [];
        }
        result[pane].push(axis);
    });
    return result;
}
const linearConverter = (br)=>({
        transform: function(v, b) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(v, b, br.allowNegatives, br.linearThreshold));
        },
        getTicks: function(interval, tickValues, base) {
            const ticks = [];
            let tick = this.transform(tickValues[0], base);
            while(ticks.length < tickValues.length){
                ticks.push(tick);
                tick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(tick + interval);
            }
            return ticks;
        }
    });
const logConverter = (br)=>({
        transform: function(v, b) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raiseToExt"])(v, b, br.allowNegatives, br.linearThreshold));
        },
        getTicks: function(interval, tickValues, base) {
            const ticks = [];
            let tick;
            for(let i = 0; i < tickValues.length; i += 1){
                tick = this.transform(tickValues[i], base);
                ticks.push(tick);
            }
            return ticks;
        }
    });
function convertAxisInfo(axisInfo, converter) {
    if (!axisInfo.isLogarithmic) {
        return;
    }
    const base = axisInfo.logarithmicBase;
    const tickValues = axisInfo.tickValues;
    axisInfo.minValue = converter.transform(axisInfo.minValue, base);
    axisInfo.oldMinValue = converter.transform(axisInfo.oldMinValue, base);
    axisInfo.maxValue = converter.transform(axisInfo.maxValue, base);
    axisInfo.oldMaxValue = converter.transform(axisInfo.oldMaxValue, base);
    axisInfo.tickInterval = _math.round(axisInfo.tickInterval);
    if (axisInfo.tickInterval < 1) {
        axisInfo.tickInterval = 1;
    }
    const ticks = converter.getTicks(axisInfo.tickInterval, tickValues, base);
    ticks.tickInterval = axisInfo.tickInterval;
    axisInfo.tickValues = ticks;
}
function populateAxesInfo(axes) {
    return axes.reduce(function(result, axis) {
        const ticksValues = axis.getTicksValues();
        const majorTicks = ticksValues.majorTicksValues;
        const options = axis.getOptions();
        const businessRange = axis.getTranslator().getBusinessRange();
        const visibleArea = axis.getVisibleArea();
        let axisInfo;
        let tickInterval = axis._tickInterval;
        const synchronizedValue = options.synchronizedValue;
        const action = axis.getViewport().action;
        if (majorTicks && majorTicks.length > 0 && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(majorTicks[0]) && "discrete" !== options.type && !businessRange.isEmpty() && !(businessRange.breaks && businessRange.breaks.length) && "zoom" !== action && "pan" !== action) {
            axis.applyMargins();
            const startValue = axis.getTranslator().from(visibleArea[0]);
            const endValue = axis.getTranslator().from(visibleArea[1]);
            let minValue = startValue < endValue ? startValue : endValue;
            let maxValue = startValue < endValue ? endValue : startValue;
            if (minValue === maxValue && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(synchronizedValue)) {
                tickInterval = _abs(majorTicks[0] - synchronizedValue) || 1;
                minValue = majorTicks[0] - tickInterval;
                maxValue = majorTicks[0] + tickInterval;
            }
            axisInfo = {
                axis: axis,
                isLogarithmic: "logarithmic" === options.type,
                logarithmicBase: businessRange.base,
                tickValues: majorTicks,
                minorValues: ticksValues.minorTicksValues,
                minorTickInterval: axis._minorTickInterval,
                minValue: minValue,
                oldMinValue: minValue,
                maxValue: maxValue,
                oldMaxValue: maxValue,
                inverted: businessRange.invert,
                tickInterval: tickInterval,
                synchronizedValue: synchronizedValue
            };
            convertAxisInfo(axisInfo, linearConverter(axis.getTranslator().getBusinessRange()));
            result.push(axisInfo);
        }
        return result;
    }, []);
}
function updateTickValues(axesInfo) {
    const maxTicksCount = axesInfo.reduce((max, axisInfo)=>_max(max, axisInfo.tickValues.length), 0);
    axesInfo.forEach((axisInfo)=>{
        let ticksMultiplier;
        let ticksCount;
        let additionalStartTicksCount = 0;
        const synchronizedValue = axisInfo.synchronizedValue;
        const tickValues = axisInfo.tickValues;
        const tickInterval = axisInfo.tickInterval;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(synchronizedValue)) {
            axisInfo.baseTickValue = axisInfo.invertedBaseTickValue = synchronizedValue;
            axisInfo.tickValues = [
                axisInfo.baseTickValue
            ];
        } else {
            if (tickValues.length > 1 && tickInterval) {
                ticksMultiplier = _floor((maxTicksCount + 1) / tickValues.length);
                ticksCount = ticksMultiplier > 1 ? _floor((maxTicksCount + 1) / ticksMultiplier) : maxTicksCount;
                additionalStartTicksCount = _floor((ticksCount - tickValues.length) / 2);
                while(additionalStartTicksCount > 0 && 0 !== tickValues[0]){
                    tickValues.unshift((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(tickValues[0] - tickInterval));
                    additionalStartTicksCount--;
                }
                while(tickValues.length < ticksCount){
                    tickValues.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(tickValues[tickValues.length - 1] + tickInterval));
                }
                axisInfo.tickInterval = tickInterval / ticksMultiplier;
            }
            axisInfo.baseTickValue = tickValues[0];
            axisInfo.invertedBaseTickValue = tickValues[tickValues.length - 1];
        }
    });
}
function getAxisRange(axisInfo) {
    return axisInfo.maxValue - axisInfo.minValue || 1;
}
function getMainAxisInfo(axesInfo) {
    for(let i = 0; i < axesInfo.length; i++){
        if (!axesInfo[i].stubData) {
            return axesInfo[i];
        }
    }
    return null;
}
function correctMinMaxValues(axesInfo) {
    const mainAxisInfo = getMainAxisInfo(axesInfo);
    const mainAxisInfoTickInterval = mainAxisInfo.tickInterval;
    axesInfo.forEach((axisInfo)=>{
        let scale;
        let move;
        let mainAxisBaseValueOffset;
        let valueFromAxisInfo;
        if (axisInfo !== mainAxisInfo) {
            if (mainAxisInfoTickInterval && axisInfo.tickInterval) {
                if (axisInfo.stubData && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(axisInfo.synchronizedValue)) {
                    axisInfo.oldMinValue = axisInfo.minValue = axisInfo.baseTickValue - (mainAxisInfo.baseTickValue - mainAxisInfo.minValue) / mainAxisInfoTickInterval * axisInfo.tickInterval;
                    axisInfo.oldMaxValue = axisInfo.maxValue = axisInfo.baseTickValue - (mainAxisInfo.baseTickValue - mainAxisInfo.maxValue) / mainAxisInfoTickInterval * axisInfo.tickInterval;
                }
                scale = mainAxisInfoTickInterval / getAxisRange(mainAxisInfo) / axisInfo.tickInterval * getAxisRange(axisInfo);
                axisInfo.maxValue = axisInfo.minValue + getAxisRange(axisInfo) / scale;
            }
            if (mainAxisInfo.inverted && !axisInfo.inverted || !mainAxisInfo.inverted && axisInfo.inverted) {
                mainAxisBaseValueOffset = mainAxisInfo.maxValue - mainAxisInfo.invertedBaseTickValue;
            } else {
                mainAxisBaseValueOffset = mainAxisInfo.baseTickValue - mainAxisInfo.minValue;
            }
            valueFromAxisInfo = getAxisRange(axisInfo);
            move = (mainAxisBaseValueOffset / getAxisRange(mainAxisInfo) - (axisInfo.baseTickValue - axisInfo.minValue) / valueFromAxisInfo) * valueFromAxisInfo;
            axisInfo.minValue -= move;
            axisInfo.maxValue -= move;
        }
    });
}
function calculatePaddings(axesInfo) {
    let minPadding;
    let maxPadding;
    let startPadding = 0;
    let endPadding = 0;
    axesInfo.forEach((axisInfo)=>{
        const inverted = axisInfo.inverted;
        minPadding = axisInfo.minValue > axisInfo.oldMinValue ? (axisInfo.minValue - axisInfo.oldMinValue) / getAxisRange(axisInfo) : 0;
        maxPadding = axisInfo.maxValue < axisInfo.oldMaxValue ? (axisInfo.oldMaxValue - axisInfo.maxValue) / getAxisRange(axisInfo) : 0;
        startPadding = _max(startPadding, inverted ? maxPadding : minPadding);
        endPadding = _max(endPadding, inverted ? minPadding : maxPadding);
    });
    return {
        start: startPadding,
        end: endPadding
    };
}
function correctMinMaxValuesByPaddings(axesInfo, paddings) {
    axesInfo.forEach((info)=>{
        const range = getAxisRange(info);
        const inverted = info.inverted;
        info.minValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(info.minValue - paddings[inverted ? "end" : "start"] * range);
        info.maxValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(info.maxValue + paddings[inverted ? "start" : "end"] * range);
    });
}
function updateTickValuesIfSynchronizedValueUsed(axesInfo) {
    let hasSynchronizedValue = false;
    axesInfo.forEach((info)=>{
        hasSynchronizedValue = hasSynchronizedValue || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(info.synchronizedValue);
    });
    axesInfo.forEach((info)=>{
        const tickInterval = info.tickInterval;
        const tickValues = info.tickValues;
        const maxValue = info.maxValue;
        const minValue = info.minValue;
        let tick;
        if (hasSynchronizedValue && tickInterval) {
            while((tick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(tickValues[0] - tickInterval)) >= minValue){
                tickValues.unshift(tick);
            }
            tick = tickValues[tickValues.length - 1];
            while((tick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(tick + tickInterval)) <= maxValue){
                tickValues.push(tick);
            }
        }
        while(tickValues[0] + tickInterval / 10 < minValue){
            tickValues.shift();
        }
        while(tickValues[tickValues.length - 1] - tickInterval / 10 > maxValue){
            tickValues.pop();
        }
    });
}
function applyMinMaxValues(axesInfo) {
    axesInfo.forEach((info)=>{
        const axis = info.axis;
        const range = axis.getTranslator().getBusinessRange();
        if (range.min === range.minVisible) {
            range.min = info.minValue;
        }
        if (range.max === range.maxVisible) {
            range.max = info.maxValue;
        }
        range.minVisible = info.minValue;
        range.maxVisible = info.maxValue;
        if (range.min > range.minVisible) {
            range.min = range.minVisible;
        }
        if (range.max < range.maxVisible) {
            range.max = range.maxVisible;
        }
        axis.getTranslator().updateBusinessRange(range);
        axis.setTicks({
            majorTicks: info.tickValues,
            minorTicks: info.minorValues
        });
    });
}
function correctAfterSynchronize(axesInfo) {
    const invalidAxisInfo = [];
    let correctValue;
    axesInfo.forEach((info)=>{
        if (info.oldMaxValue - info.oldMinValue === 0) {
            invalidAxisInfo.push(info);
        } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(correctValue) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(info.synchronizedValue)) {
            correctValue = _abs((info.maxValue - info.minValue) / (info.tickValues[_floor(info.tickValues.length / 2)] - info.minValue || info.maxValue));
        }
    });
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(correctValue)) {
        return;
    }
    invalidAxisInfo.forEach((info)=>{
        const firstTick = info.tickValues[0];
        const correctedTick = firstTick * correctValue;
        if (firstTick > 0) {
            info.maxValue = correctedTick;
            info.minValue = 0;
        } else if (firstTick < 0) {
            info.minValue = correctedTick;
            info.maxValue = 0;
        }
    });
}
function updateMinorTicks(axesInfo) {
    axesInfo.forEach(function(axisInfo) {
        if (!axisInfo.minorTickInterval) {
            return;
        }
        const ticks = [];
        const interval = axisInfo.minorTickInterval;
        const tickCount = axisInfo.tickInterval / interval - 1;
        for(let i = 1; i < axisInfo.tickValues.length; i++){
            let tick = axisInfo.tickValues[i - 1];
            for(let j = 0; j < tickCount; j++){
                tick += interval;
                ticks.push(tick);
            }
        }
        axisInfo.minorValues = ticks;
    });
}
function allAxesValuesOnSameSideFromZero(axesInfo) {
    let allPositive = true;
    let allNegative = true;
    axesInfo.forEach((axis)=>{
        if (axis.oldMinValue > 0 || axis.oldMaxValue > 0) {
            allNegative = false;
        }
        if (axis.oldMinValue < 0 || axis.oldMaxValue < 0) {
            allPositive = false;
        }
    });
    return allPositive || allNegative;
}
function correctPaddings(axesInfo, paddings) {
    if (!allAxesValuesOnSameSideFromZero(axesInfo)) {
        return paddings;
    }
    return axesInfo.reduce((prev, info)=>{
        const inverted = info.inverted;
        const { start: start, end: end } = info.axis.getCorrectedValuesToZero(info.minValue, info.maxValue);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(start) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(end)) {
            return inverted ? {
                start: prev.start,
                end: Math.min(prev.end, end)
            } : {
                start: Math.min(prev.start, start),
                end: prev.end
            };
        }
        return prev;
    }, paddings);
}
const multiAxesSynchronizer = {
    synchronize: function(valueAxes) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(getValueAxesPerPanes(valueAxes), function(_, axes) {
            let axesInfo;
            let paddings;
            if (axes.length > 1) {
                axesInfo = populateAxesInfo(axes);
                if (axesInfo.length < 2 || !getMainAxisInfo(axesInfo)) {
                    return;
                }
                updateTickValues(axesInfo);
                correctMinMaxValues(axesInfo);
                paddings = calculatePaddings(axesInfo);
                paddings = correctPaddings(axesInfo, paddings);
                correctMinMaxValuesByPaddings(axesInfo, paddings);
                correctAfterSynchronize(axesInfo);
                updateTickValuesIfSynchronizedValueUsed(axesInfo);
                updateMinorTicks(axesInfo);
                axesInfo.forEach((info)=>{
                    convertAxisInfo(info, logConverter(info.axis.getTranslator().getBusinessRange()));
                });
                applyMinMaxValues(axesInfo);
            }
        });
    }
};
const __TURBOPACK__default__export__ = multiAxesSynchronizer;
}),
"[project]/node_modules/devextreme/esm/viz/chart_components/scroll_bar.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/scroll_bar.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "ScrollBar": ()=>ScrollBar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$translator2d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/translator2d.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const _min = Math.min;
const _max = Math.max;
const MIN_SCROLL_BAR_SIZE = 2;
const ScrollBar = function(renderer, group) {
    this._translator = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$translator2d$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Translator2D"]({}, {}, {});
    this._scroll = renderer.rect().append(group);
    this._addEvents();
};
function _getXCoord(canvas, pos, offset, width) {
    let x = 0;
    if ("right" === pos) {
        x = canvas.width - canvas.right + offset;
    } else if ("left" === pos) {
        x = canvas.left - offset - width;
    }
    return x;
}
function _getYCoord(canvas, pos, offset, width) {
    let y = 0;
    if ("top" === pos) {
        y = canvas.top - offset;
    } else if ("bottom" === pos) {
        y = canvas.height - canvas.bottom + width + offset;
    }
    return y;
}
ScrollBar.prototype = {
    _addEvents: function() {
        const scrollElement = this._scroll.element;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on(scrollElement, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["start"], (e)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fireEvent"])({
                type: "dxc-scroll-start",
                originalEvent: e,
                target: scrollElement
            });
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on(scrollElement, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["move"], (e)=>{
            const dX = -e.offset.x * this._scale;
            const dY = -e.offset.y * this._scale;
            const lx = this._offset - (this._layoutOptions.vertical ? dY : dX) / this._scale;
            this._applyPosition(lx, lx + this._translator.canvasLength / this._scale);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fireEvent"])({
                type: "dxc-scroll-move",
                originalEvent: e,
                target: scrollElement,
                offset: {
                    x: dX,
                    y: dY
                }
            });
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on(scrollElement, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["end"], (e)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fireEvent"])({
                type: "dxc-scroll-end",
                originalEvent: e,
                target: scrollElement,
                offset: {
                    x: -e.offset.x * this._scale,
                    y: -e.offset.y * this._scale
                }
            });
        });
    },
    update: function(options) {
        let position = options.position;
        const isVertical = options.rotated;
        const defaultPosition = isVertical ? "right" : "top";
        const secondaryPosition = isVertical ? "left" : "bottom";
        if (position !== defaultPosition && position !== secondaryPosition) {
            position = defaultPosition;
        }
        this._scroll.attr({
            rotate: !options.rotated ? -90 : 0,
            rotateX: 0,
            rotateY: 0,
            fill: options.color,
            width: options.width,
            opacity: options.opacity
        });
        this._layoutOptions = {
            width: options.width,
            offset: options.offset,
            vertical: isVertical,
            position: position
        };
        return this;
    },
    init: function(range, stick) {
        const isDiscrete = "discrete" === range.axisType;
        this._translateWithOffset = isDiscrete && !stick ? 1 : 0;
        this._translator.update((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, range, {
            minVisible: null,
            maxVisible: null,
            visibleCategories: null
        }, isDiscrete && {
            min: null,
            max: null
        } || {}), this._canvas, {
            isHorizontal: !this._layoutOptions.vertical,
            stick: stick
        });
        return this;
    },
    getOptions: function() {
        return this._layoutOptions;
    },
    setPane: function(panes) {
        const position = this._layoutOptions.position;
        let pane;
        if ("left" === position || "top" === position) {
            pane = panes[0];
        } else {
            pane = panes[panes.length - 1];
        }
        this.pane = pane.name;
        return this;
    },
    updateSize: function(canvas) {
        this._canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, canvas);
        const options = this._layoutOptions;
        const pos = options.position;
        const offset = options.offset;
        const width = options.width;
        this._scroll.attr({
            translateX: _getXCoord(canvas, pos, offset, width),
            translateY: _getYCoord(canvas, pos, offset, width)
        });
    },
    getMultipleAxesSpacing: function() {
        return 0;
    },
    estimateMargins: function() {
        return this.getMargins();
    },
    getMargins: function() {
        const options = this._layoutOptions;
        const margins = {
            left: 0,
            top: 0,
            right: 0,
            bottom: 0
        };
        margins[options.position] = options.width + options.offset;
        return margins;
    },
    shift: function(margins) {
        const options = this._layoutOptions;
        const side = options.position;
        const isVertical = options.vertical;
        const attr = {
            translateX: this._scroll.attr("translateX") ?? 0,
            translateY: this._scroll.attr("translateY") ?? 0
        };
        const shift = margins[side];
        attr[isVertical ? "translateX" : "translateY"] += ("left" === side || "top" === side ? -1 : 1) * shift;
        this._scroll.attr(attr);
    },
    hideTitle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    hideOuterElements: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    setPosition: function(min, max) {
        const translator = this._translator;
        const minPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(min) ? translator.translate(min, -this._translateWithOffset) : translator.translate("canvas_position_start");
        const maxPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(max) ? translator.translate(max, this._translateWithOffset) : translator.translate("canvas_position_end");
        this._offset = _min(minPoint, maxPoint);
        this._scale = translator.getScale(min, max);
        this._applyPosition(_min(minPoint, maxPoint), _max(minPoint, maxPoint));
    },
    customPositionIsAvailable: ()=>false,
    dispose: function() {
        this._scroll.dispose();
        this._scroll = this._translator = null;
    },
    _applyPosition: function(x1, x2) {
        const visibleArea = this._translator.getCanvasVisibleArea();
        x1 = _max(x1, visibleArea.min);
        x1 = _min(x1, visibleArea.max);
        x2 = _min(x2, visibleArea.max);
        x2 = _max(x2, visibleArea.min);
        const height = Math.abs(x2 - x1);
        this._scroll.attr({
            y: x1,
            height: height < 2 ? 2 : height
        });
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/chart_components/shutter_zoom.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/shutter_zoom.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-ssr] (ecmascript)");
;
const SHUTTER_EVENTS_NS = ".shutter-zoom";
const DRAG_START_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["start"] + ".shutter-zoom";
const DRAG_UPDATE_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["move"] + ".shutter-zoom";
const DRAG_END_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["end"] + ".shutter-zoom";
function getPointerCoord(rootOffset, canvas, rotated, e) {
    let coord = Math.floor(rotated ? e.pageY - rootOffset.top : e.pageX - rootOffset.left);
    const min = rotated ? canvas.y1 : canvas.x1;
    const max = rotated ? canvas.y2 : canvas.x2;
    if (coord < min) {
        coord = min;
    } else if (coord > max) {
        coord = max;
    }
    return coord;
}
function checkCoords(rootOffset, canvas, e) {
    const x = e.pageX - rootOffset.left;
    const y = e.pageY - rootOffset.top;
    return x >= canvas.x1 && x <= canvas.x2 && y >= canvas.y1 && y <= canvas.y2;
}
function dragStartHandler(ctx) {
    return function(e) {
        const offset = ctx.getRootOffset();
        const canvas = ctx.getCanvas();
        if (!checkCoords(offset, canvas, e)) {
            e.cancel = true;
            return;
        }
        ctx.rootOffset = offset;
        ctx.canvas = canvas;
        ctx.startCoord = getPointerCoord(offset, canvas, ctx.rotated, e);
        ctx.triggerStart();
        ctx.rect.attr({
            x: canvas.x1,
            y: canvas.y1,
            width: canvas.width,
            height: canvas.height
        }).append(ctx.root);
    };
}
function dragHandler(ctx) {
    return function(e) {
        const curCoord = getPointerCoord(ctx.rootOffset, ctx.canvas, ctx.rotated, e);
        const attr = {};
        ctx.curCoord = curCoord;
        attr[ctx.rotated ? "y" : "x"] = Math.min(ctx.startCoord, curCoord);
        attr[ctx.rotated ? "height" : "width"] = Math.abs(ctx.startCoord - curCoord);
        ctx.rect.attr(attr);
    };
}
function dragEndHandler(ctx) {
    return function(e) {
        ctx.triggerEnd();
        ctx.rect.remove();
    };
}
function shutterZoom(options) {
    const chart = options.chart;
    const renderer = options.renderer;
    const rotated = options.rotated;
    const rect = renderer.rect(0, 0, 0, 0).attr(options.shutterOptions);
    const shutter = {
        rect: rect,
        root: renderer.root,
        rotated: rotated,
        triggerStart: function() {
            chart._eventTrigger("zoomStart");
        },
        triggerEnd: function() {
            const tr = chart._argumentAxes[0].getTranslator();
            const rangeStart = Math.min(this.startCoord, this.curCoord);
            const rangeEnd = Math.max(this.startCoord, this.curCoord);
            chart._eventTrigger("zoomEnd", {
                rangeStart: tr.from(rangeStart),
                rangeEnd: tr.from(rangeEnd)
            });
        },
        dispose: function() {
            renderer.root.off(".shutter-zoom");
            rect.dispose();
        },
        getRootOffset: function() {
            return renderer.getRootOffset();
        },
        getCanvas: function() {
            const canvas = chart._canvas;
            const panes = chart.panes;
            const firstPane = panes[0].canvas;
            const lastPane = panes[panes.length - 1].canvas;
            return {
                x1: firstPane.left,
                y1: firstPane.top,
                x2: canvas.width - lastPane.right,
                y2: canvas.height - lastPane.bottom,
                width: canvas.width - firstPane.left - lastPane.right,
                height: canvas.height - firstPane.top - lastPane.bottom
            };
        }
    };
    renderer.root.off(".shutter-zoom").on(DRAG_START_EVENT_NAME, {
        direction: rotated ? "vertical" : "horizontal",
        immediate: true
    }, dragStartHandler(shutter)).on(DRAG_UPDATE_EVENT_NAME, dragHandler(shutter)).on(DRAG_END_EVENT_NAME, dragEndHandler(shutter));
    return shutter;
}
const __TURBOPACK__default__export__ = {
    name: "shutter_zoom",
    init: function() {
        const options = this.option("shutterZoom") || {};
        if (!options.enabled) {
            return;
        }
        this._shutterZoom = shutterZoom({
            chart: this,
            renderer: this._renderer,
            rotated: this.option("rotated"),
            shutterOptions: options
        });
    },
    dispose: function() {
        this._shutterZoom && this._shutterZoom.dispose();
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/chart_components/zoom_and_pan.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/zoom_and_pan.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$wheel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/wheel.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_wheel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_wheel.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/transform.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const EVENTS_NS = ".zoomAndPanNS";
const DRAG_START_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["start"] + EVENTS_NS;
const DRAG_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["move"] + EVENTS_NS;
const DRAG_END_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["end"] + EVENTS_NS;
const PINCH_START_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pinchstart"] + EVENTS_NS;
const PINCH_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pinch"] + EVENTS_NS;
const PINCH_END_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$transform$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pinchend"] + EVENTS_NS;
const SCROLL_BAR_START_EVENT_NAME = "dxc-scroll-start" + EVENTS_NS;
const SCROLL_BAR_MOVE_EVENT_NAME = "dxc-scroll-move" + EVENTS_NS;
const SCROLL_BAR_END_EVENT_NAME = "dxc-scroll-end" + EVENTS_NS;
const GESTURE_TIMEOUT = 300;
const MIN_DRAG_DELTA = 5;
const _min = Math.min;
const _max = Math.max;
const _abs = Math.abs;
function canvasToRect(canvas) {
    return {
        x: canvas.left,
        y: canvas.top,
        width: canvas.width - canvas.left - canvas.right,
        height: canvas.height - canvas.top - canvas.bottom
    };
}
function checkCoords(rect, coords) {
    const x = coords.x;
    const y = coords.y;
    return x >= rect.x && x <= rect.width + rect.x && y >= rect.y && y <= rect.height + rect.y;
}
function sortAxes(axes, onlyAxisToNotify) {
    if (onlyAxisToNotify) {
        axes = axes.sort((a, b)=>{
            if (a === onlyAxisToNotify) {
                return -1;
            }
            if (b === onlyAxisToNotify) {
                return 1;
            }
            return 0;
        });
    }
    return axes;
}
function getFilteredAxes(axes) {
    return axes.filter((a)=>!a.getTranslator().getBusinessRange().isEmpty());
}
function isAxisAvailablePanning(axes) {
    return axes.some((axis)=>!axis.isExtremePosition(false) || !axis.isExtremePosition(true));
}
function axisZoom(axis, onlyAxisToNotify, getRange, getParameters, actionField, scale, e) {
    const silent = onlyAxisToNotify && axis !== onlyAxisToNotify;
    const range = getRange(axis);
    const { stopInteraction: stopInteraction, correctedRange: correctedRange } = axis.checkZoomingLowerLimitOvercome(actionField, scale, range);
    const result = axis.handleZooming(stopInteraction ? null : correctedRange, getParameters(silent), e, actionField);
    stopInteraction && axis.handleZoomEnd();
    return {
        stopInteraction: stopInteraction,
        result: result
    };
}
function zoomAxes(e, axes, getRange, zoom, params, onlyAxisToNotify) {
    axes = sortAxes(axes, onlyAxisToNotify);
    let zoomStarted = false;
    const getParameters = (silent)=>({
            start: !!silent,
            end: !!silent
        });
    getFilteredAxes(axes).some((axis)=>{
        const translator = axis.getTranslator();
        const scale = translator.getMinScale(zoom);
        const { stopInteraction: stopInteraction, result: result } = axisZoom(axis, onlyAxisToNotify, getRange((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
            scale: scale,
            translator: translator,
            axis: axis
        }, params)), getParameters, "zoom", scale, e);
        zoomStarted = !stopInteraction;
        return onlyAxisToNotify && result.isPrevented;
    });
    return zoomStarted;
}
function cancelEvent(e) {
    if (e.originalEvent) {
        cancelEvent(e.originalEvent);
    }
    if (false !== e.cancelable) {
        e.cancel = true;
    }
}
const __TURBOPACK__default__export__ = {
    name: "zoom_and_pan",
    init: function() {
        const chart = this;
        const renderer = this._renderer;
        function getAxesCopy(zoomAndPan, actionField) {
            let axes = [];
            const options = zoomAndPan.options;
            const actionData = zoomAndPan.actionData;
            if (options.argumentAxis[actionField]) {
                axes.push(chart.getArgumentAxis());
            }
            if (options.valueAxis[actionField]) {
                axes = axes.concat(actionData.valueAxes);
            }
            return axes;
        }
        function startAxesViewportChanging(zoomAndPan, actionField, e) {
            const axes = getAxesCopy(zoomAndPan, actionField);
            getFilteredAxes(axes).some((axis)=>axis.handleZooming(null, {
                    end: true
                }, e, actionField).isPrevented) && cancelEvent(e);
        }
        function axesViewportChanging(zoomAndPan, actionField, e, offsetCalc, centerCalc) {
            function zoomAxes(axes, criteria, coordField, e, actionData) {
                let zoom = {
                    zoomed: false
                };
                criteria && getFilteredAxes(axes).forEach((axis)=>{
                    const options = axis.getOptions();
                    const viewport = axis.visualRange();
                    const scale = axis.getTranslator().getEventScale(e);
                    const translate = -offsetCalc(e, actionData, coordField, scale);
                    zoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, zoom, axis.getTranslator().zoom(translate, scale, axis.getZoomBounds()));
                    const range = axis.adjustRange((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getVizRangeObject"])([
                        zoom.min,
                        zoom.max
                    ]));
                    const { stopInteraction: stopInteraction, correctedRange: correctedRange } = axis.checkZoomingLowerLimitOvercome(actionField, scale, range);
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(viewport) || viewport.startValue.valueOf() !== correctedRange.startValue.valueOf() || viewport.endValue.valueOf() !== correctedRange.endValue.valueOf()) {
                        axis.handleZooming(stopInteraction ? null : correctedRange, {
                            start: true,
                            end: true
                        }, e, actionField);
                        if (!stopInteraction) {
                            zoom.zoomed = true;
                            zoom.deltaTranslate = translate - zoom.translate;
                        }
                    } else if ("touch" === e.pointerType && "discrete" === options.type) {
                        const isMinPosition = axis.isExtremePosition(false);
                        const isMaxPosition = axis.isExtremePosition(true);
                        const zoomInEnabled = scale > 1 && !stopInteraction;
                        const zoomOutEnabled = scale < 1 && (!isMinPosition || !isMaxPosition);
                        const panningEnabled = 1 === scale && !(isMinPosition && (translate < 0 && !options.inverted || translate > 0 && options.inverted) || isMaxPosition && (translate > 0 && !options.inverted || translate < 0 && options.inverted));
                        zoom.enabled = zoomInEnabled || zoomOutEnabled || panningEnabled;
                    }
                });
                return zoom;
            }
            function storeOffset(e, actionData, zoom, coordField) {
                if (zoom.zoomed) {
                    actionData.offset[coordField] = (e.offset ? e.offset[coordField] : actionData.offset[coordField]) + zoom.deltaTranslate;
                }
            }
            function storeCenter(center, actionData, zoom, coordField) {
                if (zoom.zoomed) {
                    actionData.center[coordField] = center[coordField] + zoom.deltaTranslate;
                }
            }
            const rotated = chart.option("rotated");
            const actionData = zoomAndPan.actionData;
            const options = zoomAndPan.options;
            let argZoom = {};
            let valZoom = {};
            if (!actionData.fallback) {
                argZoom = zoomAxes(chart._argumentAxes, options.argumentAxis[actionField], rotated ? "y" : "x", e, actionData);
                valZoom = zoomAxes(actionData.valueAxes, options.valueAxis[actionField], rotated ? "x" : "y", e, actionData);
                chart._requestChange([
                    "VISUAL_RANGE"
                ]);
                storeOffset(e, actionData, argZoom, rotated ? "y" : "x");
                storeOffset(e, actionData, valZoom, rotated ? "x" : "y");
            }
            const center = centerCalc(e);
            storeCenter(center, actionData, argZoom, rotated ? "y" : "x");
            storeCenter(center, actionData, valZoom, rotated ? "x" : "y");
            if (!argZoom.zoomed && !valZoom.zoomed) {
                actionData.center = center;
            }
            return argZoom.zoomed || valZoom.zoomed || actionData.fallback || argZoom.enabled || valZoom.enabled;
        }
        function finishAxesViewportChanging(zoomAndPan, actionField, e, offsetCalc) {
            function zoomAxes(axes, coordField, actionData, onlyAxisToNotify) {
                let zoomStarted = false;
                const scale = e.scale || 1;
                const getRange = (axis)=>{
                    const zoom = axis.getTranslator().zoom(-offsetCalc(e, actionData, coordField, scale), scale, axis.getZoomBounds());
                    return {
                        startValue: zoom.min,
                        endValue: zoom.max
                    };
                };
                const getParameters = (silent)=>({
                        start: true,
                        end: silent
                    });
                getFilteredAxes(axes).forEach((axis)=>{
                    zoomStarted = !axisZoom(axis, onlyAxisToNotify, getRange, getParameters, actionField, scale, e).stopInteraction;
                });
                return zoomStarted;
            }
            const rotated = chart.option("rotated");
            const actionData = zoomAndPan.actionData;
            const options = zoomAndPan.options;
            let zoomStarted = true;
            if (actionData.fallback) {
                zoomStarted &= options.argumentAxis[actionField] && zoomAxes(chart._argumentAxes, rotated ? "y" : "x", actionData, chart.getArgumentAxis());
                zoomStarted |= options.valueAxis[actionField] && zoomAxes(actionData.valueAxes, rotated ? "x" : "y", actionData);
            } else {
                const axes = getAxesCopy(zoomAndPan, actionField);
                getFilteredAxes(axes).forEach((axis)=>{
                    axis.handleZooming(null, {
                        start: true
                    }, e, actionField);
                });
                zoomStarted = axes.length;
            }
            zoomStarted && chart._requestChange([
                "VISUAL_RANGE"
            ]);
        }
        function prepareActionData(coords, action) {
            const axes = chart._argumentAxes.filter((axis)=>checkCoords(canvasToRect(axis.getCanvas()), coords));
            return {
                fallback: chart._lastRenderingTime > 300,
                cancel: !axes.length || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(action),
                action: action,
                curAxisRect: axes.length && canvasToRect(axes[0].getCanvas()),
                valueAxes: axes.length && chart._valueAxes.filter((axis)=>checkCoords(canvasToRect(axis.getCanvas()), coords)),
                offset: {
                    x: 0,
                    y: 0
                },
                center: coords,
                startCenter: coords
            };
        }
        function getPointerCoord(rect, e) {
            const rootOffset = renderer.getRootOffset();
            return {
                x: _min(_max(e.pageX - rootOffset.left, rect.x), rect.width + rect.x),
                y: _min(_max(e.pageY - rootOffset.top, rect.y), rect.height + rect.y)
            };
        }
        function calcCenterForPinch(e) {
            const rootOffset = renderer.getRootOffset();
            const x1 = e.pointers[0].pageX;
            const x2 = e.pointers[1].pageX;
            const y1 = e.pointers[0].pageY;
            const y2 = e.pointers[1].pageY;
            return {
                x: _min(x1, x2) + _abs(x2 - x1) / 2 - rootOffset.left,
                y: _min(y1, y2) + _abs(y2 - y1) / 2 - rootOffset.top
            };
        }
        function calcCenterForDrag(e) {
            const rootOffset = renderer.getRootOffset();
            return {
                x: e.pageX - rootOffset.left,
                y: e.pageY - rootOffset.top
            };
        }
        function calcOffsetForDrag(e, actionData, coordField) {
            return e.offset[coordField] - actionData.offset[coordField];
        }
        function preventDefaults(e) {
            if (false !== e.cancelable) {
                e.preventDefault();
                e.stopPropagation();
            }
            chart._stopCurrentHandling();
        }
        const zoomAndPan = {
            dragStartHandler: function(e) {
                const options = zoomAndPan.options;
                const isTouch = "touch" === e.pointerType;
                const wantPan = options.argumentAxis.pan || options.valueAxis.pan;
                const wantZoom = options.argumentAxis.zoom || options.valueAxis.zoom;
                const panKeyPressed = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.panKey) && e[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(options.panKey) + "Key"];
                const dragToZoom = options.dragToZoom;
                let action;
                e._cancelPreventDefault = true;
                if (isTouch) {
                    if (options.allowTouchGestures && wantPan) {
                        const cancelPanning = !zoomAndPan.panningVisualRangeEnabled() || zoomAndPan.skipEvent;
                        action = cancelPanning ? null : "pan";
                    }
                } else if (dragToZoom && wantPan && panKeyPressed || !dragToZoom && wantPan) {
                    action = "pan";
                } else if (dragToZoom && wantZoom) {
                    action = "zoom";
                }
                const actionData = prepareActionData(calcCenterForDrag(e), action);
                if (actionData.cancel) {
                    zoomAndPan.skipEvent = false;
                    if (false !== e.cancelable) {
                        e.cancel = true;
                    }
                    return;
                }
                zoomAndPan.actionData = actionData;
                if ("zoom" === action) {
                    actionData.startCoords = getPointerCoord(actionData.curAxisRect, e);
                    actionData.rect = renderer.rect(0, 0, 0, 0).attr(options.dragBoxStyle).append(renderer.root);
                } else {
                    startAxesViewportChanging(zoomAndPan, "pan", e);
                }
            },
            dragHandler: function(e) {
                const rotated = chart.option("rotated");
                const options = zoomAndPan.options;
                const actionData = zoomAndPan.actionData;
                const isTouch = "touch" === e.pointerType;
                e._cancelPreventDefault = true;
                if (!actionData || isTouch && !zoomAndPan.panningVisualRangeEnabled()) {
                    return;
                }
                if ("zoom" === actionData.action) {
                    preventDefaults(e);
                    const curCanvas = actionData.curAxisRect;
                    const startCoords = actionData.startCoords;
                    const curCoords = getPointerCoord(curCanvas, e);
                    const zoomArg = options.argumentAxis.zoom;
                    const zoomVal = options.valueAxis.zoom;
                    const rect = {
                        x: _min(startCoords.x, curCoords.x),
                        y: _min(startCoords.y, curCoords.y),
                        width: _abs(startCoords.x - curCoords.x),
                        height: _abs(startCoords.y - curCoords.y)
                    };
                    if (!zoomArg || !zoomVal) {
                        if (!zoomArg && !rotated || !zoomVal && rotated) {
                            rect.x = curCanvas.x;
                            rect.width = curCanvas.width;
                        } else {
                            rect.y = curCanvas.y;
                            rect.height = curCanvas.height;
                        }
                    }
                    actionData.rect.attr(rect);
                } else if ("pan" === actionData.action) {
                    axesViewportChanging(zoomAndPan, "pan", e, calcOffsetForDrag, (e)=>e.offset);
                    const deltaOffsetY = Math.abs(e.offset.y - actionData.offset.y);
                    const deltaOffsetX = Math.abs(e.offset.x - actionData.offset.x);
                    if (isTouch && (deltaOffsetY > 5 && deltaOffsetY > Math.abs(actionData.offset.x) || deltaOffsetX > 5 && deltaOffsetX > Math.abs(actionData.offset.y))) {
                        return;
                    }
                    preventDefaults(e);
                }
            },
            dragEndHandler: function(e) {
                const rotated = chart.option("rotated");
                const options = zoomAndPan.options;
                const actionData = zoomAndPan.actionData;
                const isTouch = "touch" === e.pointerType;
                const getRange = (_ref)=>{
                    let { translator: translator, startCoord: startCoord, curCoord: curCoord } = _ref;
                    return ()=>[
                            translator.from(startCoord),
                            translator.from(curCoord)
                        ];
                };
                const getCoords = (curCoords, startCoords, field)=>({
                        curCoord: curCoords[field],
                        startCoord: startCoords[field]
                    });
                const needToZoom = (axisOption, coords)=>axisOption.zoom && _abs(coords.curCoord - coords.startCoord) > 5;
                const panIsEmpty = actionData && "pan" === actionData.action && !actionData.fallback && 0 === actionData.offset.x && 0 === actionData.offset.y;
                if (!actionData || isTouch && !zoomAndPan.panningVisualRangeEnabled() || panIsEmpty) {
                    return;
                }
                !isTouch && preventDefaults(e);
                if ("zoom" === actionData.action) {
                    const curCoords = getPointerCoord(actionData.curAxisRect, e);
                    const argumentCoords = getCoords(curCoords, actionData.startCoords, rotated ? "y" : "x");
                    const valueCoords = getCoords(curCoords, actionData.startCoords, rotated ? "x" : "y");
                    const argumentAxesZoomed = needToZoom(options.argumentAxis, argumentCoords) && zoomAxes(e, chart._argumentAxes, getRange, true, argumentCoords, chart.getArgumentAxis());
                    const valueAxesZoomed = needToZoom(options.valueAxis, valueCoords) && zoomAxes(e, actionData.valueAxes, getRange, true, valueCoords);
                    if (valueAxesZoomed || argumentAxesZoomed) {
                        chart._requestChange([
                            "VISUAL_RANGE"
                        ]);
                    }
                    actionData.rect.dispose();
                } else if ("pan" === actionData.action) {
                    finishAxesViewportChanging(zoomAndPan, "pan", e, calcOffsetForDrag);
                }
                zoomAndPan.actionData = null;
            },
            pinchStartHandler: function(e) {
                const actionData = prepareActionData(calcCenterForPinch(e), "zoom");
                if (actionData.cancel) {
                    cancelEvent(e);
                    return;
                }
                zoomAndPan.actionData = actionData;
                startAxesViewportChanging(zoomAndPan, "zoom", e);
            },
            pinchHandler: function(e) {
                if (!zoomAndPan.actionData) {
                    return;
                }
                axesViewportChanging(zoomAndPan, "zoom", e, (e, actionData, coordField, scale)=>calcCenterForPinch(e)[coordField] - actionData.center[coordField] + (actionData.center[coordField] - actionData.center[coordField] * scale), calcCenterForPinch);
                preventDefaults(e);
            },
            pinchEndHandler: function(e) {
                if (!zoomAndPan.actionData) {
                    return;
                }
                finishAxesViewportChanging(zoomAndPan, "zoom", e, (e, actionData, coordField, scale)=>actionData.center[coordField] - actionData.startCenter[coordField] + (actionData.startCenter[coordField] - actionData.startCenter[coordField] * scale));
                zoomAndPan.actionData = null;
            },
            mouseWheelHandler: function(e) {
                const options = zoomAndPan.options;
                const rotated = chart.option("rotated");
                const getRange = (_ref2)=>{
                    let { translator: translator, coord: coord, scale: scale, axis: axis } = _ref2;
                    return ()=>{
                        const zoom = translator.zoom(-(coord - coord * scale), scale, axis.getZoomBounds());
                        return {
                            startValue: zoom.min,
                            endValue: zoom.max
                        };
                    };
                };
                const coords = calcCenterForDrag(e);
                let axesZoomed = false;
                let targetAxes;
                if (options.valueAxis.zoom) {
                    targetAxes = chart._valueAxes.filter((axis)=>checkCoords(canvasToRect(axis.getCanvas()), coords));
                    if (0 === targetAxes.length) {
                        const targetCanvas = chart._valueAxes.reduce((r, axis)=>{
                            if (!r && axis.coordsIn(coords.x, coords.y)) {
                                r = axis.getCanvas();
                            }
                            return r;
                        }, null);
                        if (targetCanvas) {
                            targetAxes = chart._valueAxes.filter((axis)=>checkCoords(canvasToRect(axis.getCanvas()), {
                                    x: targetCanvas.left,
                                    y: targetCanvas.top
                                }));
                        }
                    }
                    axesZoomed |= zoomAxes(e, targetAxes, getRange, e.delta > 0, {
                        coord: rotated ? coords.x : coords.y
                    });
                }
                if (options.argumentAxis.zoom) {
                    const canZoom = chart._argumentAxes.some((axis)=>{
                        if (checkCoords(canvasToRect(axis.getCanvas()), coords) || axis.coordsIn(coords.x, coords.y)) {
                            return true;
                        }
                        return false;
                    });
                    axesZoomed |= canZoom && zoomAxes(e, chart._argumentAxes, getRange, e.delta > 0, {
                        coord: rotated ? coords.y : coords.x
                    }, chart.getArgumentAxis());
                }
                if (axesZoomed) {
                    chart._requestChange([
                        "VISUAL_RANGE"
                    ]);
                    if (targetAxes && isAxisAvailablePanning(targetAxes) || !targetAxes && zoomAndPan.panningVisualRangeEnabled()) {
                        preventDefaults(e);
                    }
                }
            },
            cleanup: function() {
                renderer.root.off(EVENTS_NS);
                zoomAndPan.actionData && zoomAndPan.actionData.rect && zoomAndPan.actionData.rect.dispose();
                zoomAndPan.actionData = null;
                renderer.root.css({
                    "touch-action": ""
                });
            },
            setup: function(options) {
                zoomAndPan.cleanup();
                if (!options.argumentAxis.pan) {
                    renderer.root.on(SCROLL_BAR_START_EVENT_NAME, cancelEvent);
                }
                if (options.argumentAxis.none && options.valueAxis.none) {
                    return;
                }
                zoomAndPan.options = options;
                if ((options.argumentAxis.zoom || options.valueAxis.zoom) && options.allowMouseWheel) {
                    renderer.root.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_wheel$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["name"] + EVENTS_NS, zoomAndPan.mouseWheelHandler);
                }
                if ((options.argumentAxis.zoom || options.valueAxis.zoom) && options.allowTouchGestures) {
                    renderer.root.on(PINCH_START_EVENT_NAME, {
                        passive: false
                    }, zoomAndPan.pinchStartHandler).on(PINCH_EVENT_NAME, {
                        passive: false
                    }, zoomAndPan.pinchHandler).on(PINCH_END_EVENT_NAME, zoomAndPan.pinchEndHandler);
                }
                renderer.root.on(DRAG_START_EVENT_NAME, {
                    immediate: true,
                    passive: false
                }, zoomAndPan.dragStartHandler).on(DRAG_EVENT_NAME, {
                    immediate: true,
                    passive: false
                }, zoomAndPan.dragHandler).on(DRAG_END_EVENT_NAME, zoomAndPan.dragEndHandler);
                renderer.root.on(SCROLL_BAR_START_EVENT_NAME, function(e) {
                    zoomAndPan.actionData = {
                        valueAxes: [],
                        offset: {
                            x: 0,
                            y: 0
                        },
                        center: {
                            x: 0,
                            y: 0
                        }
                    };
                    preventDefaults(e);
                    startAxesViewportChanging(zoomAndPan, "pan", e);
                }).on(SCROLL_BAR_MOVE_EVENT_NAME, function(e) {
                    preventDefaults(e);
                    axesViewportChanging(zoomAndPan, "pan", e, calcOffsetForDrag, (e)=>e.offset);
                }).on(SCROLL_BAR_END_EVENT_NAME, function(e) {
                    preventDefaults(e);
                    finishAxesViewportChanging(zoomAndPan, "pan", e, calcOffsetForDrag);
                    zoomAndPan.actionData = null;
                });
            },
            panningVisualRangeEnabled: function() {
                return isAxisAvailablePanning(chart._valueAxes) || isAxisAvailablePanning(chart._argumentAxes);
            }
        };
        this._zoomAndPan = zoomAndPan;
    },
    members: {
        _setupZoomAndPan: function() {
            this._zoomAndPan.setup(this._themeManager.getOptions("zoomAndPan"));
        }
    },
    dispose: function() {
        this._zoomAndPan.cleanup();
    },
    customize: function(constructor) {
        constructor.addChange({
            code: "ZOOM_AND_PAN",
            handler: function() {
                this._setupZoomAndPan();
            },
            isThemeDependent: true,
            isOptionChange: true,
            option: "zoomAndPan"
        });
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/chart_components/tracker.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart_components/tracker.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "ChartTracker": ()=>ChartTracker,
    "PieTracker": ()=>PieTracker
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$click$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/click.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_click$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_click.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/consts.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const _floor = Math.floor;
const eventsConsts = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].events;
const statesConsts = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].states;
const HOVER_STATE = statesConsts.hoverMark;
const NORMAL_STATE = statesConsts.normalMark;
const EVENT_NS = "dxChartTracker";
const DOT_EVENT_NS = "." + EVENT_NS;
const POINTER_ACTION = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].down,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].move
], EVENT_NS);
const LEGEND_CLICK = "legendClick";
const SERIES_CLICK = "seriesClick";
const POINT_CLICK = "pointClick";
const POINT_DATA = "chart-data-point";
const SERIES_DATA = "chart-data-series";
const ARG_DATA = "chart-data-argument";
const DELAY = 100;
const HOLD_TIMEOUT = 300;
const NONE_MODE = "none";
const ALL_ARGUMENT_POINTS_MODE = "allargumentpoints";
const INCLUDE_POINTS_MODE = "includepoints";
const EXLUDE_POINTS_MODE = "excludepoints";
const LEGEND_HOVER_MODES = [
    "includepoints",
    "excludepoints",
    "none"
];
function getData(event, dataKey, tryCheckParent) {
    const target = event.target;
    if ("tspan" === target.tagName) {
        return target.parentNode[dataKey];
    }
    const data = target[dataKey];
    if (tryCheckParent && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(data)) {
        const getParentData = function(node) {
            if (node.parentNode) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(node.parentNode[dataKey])) {
                    return node.parentNode[dataKey];
                } else {
                    return getParentData(node.parentNode);
                }
            }
            return;
        };
        return getParentData(target);
    }
    return data;
}
function eventCanceled(_ref, target) {
    let { cancel: cancel } = _ref;
    return cancel || !target.getOptions();
}
function correctLegendHoverMode(mode) {
    if (LEGEND_HOVER_MODES.indexOf(mode) > -1) {
        return mode;
    } else {
        return "includepoints";
    }
}
function correctHoverMode(target) {
    const mode = target.getOptions().hoverMode;
    return "none" === mode ? mode : "allargumentpoints";
}
const baseTrackerPrototype = {
    ctor: function(options) {
        const that = this;
        const data = {
            tracker: that
        };
        that._renderer = options.renderer;
        that._legend = options.legend;
        that._tooltip = options.tooltip;
        that._eventTrigger = options.eventTrigger;
        that._seriesGroup = options.seriesGroup;
        options.seriesGroup.off(DOT_EVENT_NS).on((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])(eventsConsts.showPointTooltip, EVENT_NS), data, that._showPointTooltip).on((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])(eventsConsts.hidePointTooltip, EVENT_NS), data, that._hidePointTooltip);
        that._renderer.root.off(DOT_EVENT_NS).on(POINTER_ACTION, data, that._pointerHandler).on((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].up, EVENT_NS), ()=>clearTimeout(that._holdTimer)).on((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_click$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["name"], EVENT_NS), data, that._clickHandler);
    },
    update: function(options) {
        this._chart = options.chart;
    },
    updateSeries (series, resetDecorations) {
        const that = this;
        const noHoveredSeries = !(null !== series && void 0 !== series && series.some((s)=>s === that.hoveredSeries) || that._hoveredPoint && that._hoveredPoint.series);
        if (that._storedSeries !== series) {
            that._storedSeries = series || [];
        }
        if (noHoveredSeries) {
            that._clean();
            that._renderer.initDefsElements();
        }
        if (resetDecorations) {
            that.clearSelection();
            if (!noHoveredSeries) {
                that._hideTooltip(that.pointAtShownTooltip);
                that.clearHover();
            }
        }
    },
    setCanvases: function(mainCanvas, paneCanvases) {
        this._mainCanvas = mainCanvas;
        this._canvases = paneCanvases;
    },
    repairTooltip: function() {
        const point = this.pointAtShownTooltip;
        if (!point || !point.series || !point.isVisible()) {
            this._hideTooltip(point, true);
        } else {
            this._showTooltip(point);
        }
    },
    _setHoveredPoint: function(point) {
        if (point === this._hoveredPoint) {
            return;
        }
        this._releaseHoveredPoint();
        point.hover();
        this._hoveredPoint = point;
    },
    _releaseHoveredPoint: function(isPointerOut) {
        if (this._hoveredPoint && this._hoveredPoint.getOptions()) {
            this._hoveredPoint.clearHover();
            this._hoveredPoint = null;
            if (this._tooltip.isEnabled()) {
                this._hideTooltip(this._hoveredPoint, false, isPointerOut);
            }
        }
    },
    _setHoveredSeries: function(series, mode) {
        this._releaseHoveredSeries();
        this._releaseHoveredPoint();
        series.hover(mode);
        this.hoveredSeries = series;
    },
    _releaseHoveredSeries () {
        if (this.hoveredSeries) {
            this.hoveredSeries.clearHover();
            this.hoveredSeries = null;
        }
    },
    clearSelection () {
        this._storedSeries.forEach((series)=>{
            if (series) {
                series.clearSelection();
                series.getPoints().forEach((point)=>point.clearSelection());
            }
        });
    },
    _clean: function() {
        this.hoveredPoint = this.hoveredSeries = this._hoveredArgumentPoints = null;
        this._hideTooltip(this.pointAtShownTooltip);
    },
    clearHover: function(isPointerOut) {
        this._resetHoveredArgument();
        this._releaseHoveredSeries();
        this._releaseHoveredPoint(isPointerOut);
    },
    _hideTooltip: function(point, silent, isPointerOut) {
        const that = this;
        if (!that._tooltip || point && that.pointAtShownTooltip !== point) {
            return;
        }
        if (!silent && that.pointAtShownTooltip) {
            that.pointAtShownTooltip = null;
        }
        that._tooltip.hide(!!isPointerOut);
    },
    _showTooltip: function(point) {
        const that = this;
        let tooltipFormatObject;
        const eventData = {
            target: point
        };
        if (null !== point && void 0 !== point && point.getOptions()) {
            tooltipFormatObject = point.getTooltipFormatObject(that._tooltip, that._tooltip.isShared() && that._chart.getStackedPoints(point));
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(tooltipFormatObject.valueText) && !tooltipFormatObject.points || !point.isVisible()) {
                return;
            }
            const coords = point.getTooltipParams(that._tooltip.getLocation());
            const rootOffset = that._renderer.getRootOffset();
            coords.x += rootOffset.left;
            coords.y += rootOffset.top;
            const callback = (result)=>{
                result && (that.pointAtShownTooltip = point);
            };
            callback(that._tooltip.show(tooltipFormatObject, coords, eventData, void 0, callback));
        }
    },
    _showPointTooltip: function(event, point) {
        const that = event.data.tracker;
        const pointWithTooltip = that.pointAtShownTooltip;
        if (pointWithTooltip && pointWithTooltip !== point) {
            that._hideTooltip(pointWithTooltip);
        }
        that._showTooltip(point);
    },
    _hidePointTooltip: function(event, point) {
        event.data.tracker._hideTooltip(point, false, true);
    },
    _enableOutHandler: function() {
        if (this._outHandler) {
            return;
        }
        const that = this;
        const handler = function(e) {
            const rootOffset = that._renderer.getRootOffset();
            const x = _floor(e.pageX - rootOffset.left);
            const y = _floor(e.pageY - rootOffset.top);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pointInCanvas"])(that._mainCanvas, x, y) && !that._isCursorOnTooltip(e)) {
                that._pointerOut();
                that._disableOutHandler();
            }
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDocument(), POINTER_ACTION, handler);
        this._outHandler = handler;
    },
    _isCursorOnTooltip: function(e) {
        return this._tooltip.isEnabled() && this._tooltip.isCursorOnTooltip(e.pageX, e.pageY);
    },
    _disableOutHandler: function() {
        this._outHandler && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].off(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDocument(), POINTER_ACTION, this._outHandler);
        this._outHandler = null;
    },
    stopCurrentHandling: function() {
        this._pointerOut(true);
    },
    _pointerOut: function(force) {
        this.clearHover(true);
        (force || this._tooltip.isEnabled()) && this._hideTooltip(this.pointAtShownTooltip, false, true);
    },
    _triggerLegendClick: function(eventArgs, elementClick) {
        const eventTrigger = this._eventTrigger;
        eventTrigger(LEGEND_CLICK, eventArgs, function() {
            !eventCanceled(eventArgs, eventArgs.target, "legend") && eventTrigger(elementClick, eventArgs);
        });
    },
    _hoverLegendItem: function(x, y) {
        const that = this;
        const item = that._legend.getItemByCoord(x, y);
        let series;
        const legendHoverMode = correctLegendHoverMode(that._legend.getOptions().hoverMode);
        if (item) {
            series = that._storedSeries[item.id];
            if (!series.isHovered() || series.lastHoverMode !== legendHoverMode) {
                that._setHoveredSeries(series, legendHoverMode);
            }
            that._tooltip.isEnabled() && that._hideTooltip(that.pointAtShownTooltip);
        } else {
            that.clearHover();
        }
    },
    _hoverArgument: function(argument, argumentIndex) {
        const that = this;
        const hoverMode = that._getArgumentHoverMode();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(argument)) {
            that._releaseHoveredPoint();
            that._hoveredArgument = argument;
            that._argumentIndex = argumentIndex;
            that._notifySeries({
                action: "pointHover",
                notifyLegend: that._notifyLegendOnHoverArgument,
                target: {
                    argument: argument,
                    fullState: HOVER_STATE,
                    argumentIndex: argumentIndex,
                    getOptions: function() {
                        return {
                            hoverMode: hoverMode
                        };
                    }
                }
            });
        }
    },
    _resetHoveredArgument: function() {
        const that = this;
        let hoverMode;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(that._hoveredArgument)) {
            hoverMode = that._getArgumentHoverMode();
            that._notifySeries({
                action: "clearPointHover",
                notifyLegend: that._notifyLegendOnHoverArgument,
                target: {
                    fullState: NORMAL_STATE,
                    argumentIndex: that._argumentIndex,
                    argument: that._hoveredArgument,
                    getOptions: function() {
                        return {
                            hoverMode: hoverMode
                        };
                    }
                }
            });
            that._hoveredArgument = null;
        }
    },
    _notifySeries: function(data) {
        this._storedSeries.forEach(function(series) {
            series.notify(data);
        });
    },
    _pointerHandler: function(e) {
        var _series;
        const that = e.data.tracker;
        const rootOffset = that._renderer.getRootOffset();
        const x = _floor(e.pageX - rootOffset.left);
        const y = _floor(e.pageY - rootOffset.top);
        const canvas = that._getCanvas(x, y);
        let series = getData(e, SERIES_DATA);
        let point = getData(e, POINT_DATA) || (null === (_series = series) || void 0 === _series ? void 0 : _series.getPointByCoord(x, y));
        that._isHolding = false;
        clearTimeout(that._holdTimer);
        if (e.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].down) {
            that._holdTimer = setTimeout(()=>that._isHolding = true, 300);
        }
        if (point && !point.getMarkerVisibility()) {
            point = void 0;
        }
        that._enableOutHandler();
        if (that._legend.coordsIn(x, y)) {
            that._hoverLegendItem(x, y);
            return;
        }
        if (that.hoveredSeries && that.hoveredSeries !== that._stuckSeries) {
            that._releaseHoveredSeries();
        }
        if (that._hoverArgumentAxis(x, y, e)) {
            return;
        }
        if (that._isPointerOut(canvas, point)) {
            that._pointerOut();
        }
        if (!canvas && !point) {
            return;
        }
        if (series && !point) {
            point = series.getNeighborPoint(x, y);
            if (!that._stickyHovering && point && !point.coordsIn(x, y)) {
                point = null;
            }
            if (series !== that.hoveredSeries) {
                that._setTimeout(function() {
                    that._setHoveredSeries(series);
                    that._setStuckSeries(e, series, x, y);
                    that._pointerComplete(point, x, y);
                }, series);
                return;
            }
        } else if (point) {
            if (e.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].move && "touch" !== e.pointerType) {
                return;
            }
            if (that.hoveredSeries) {
                that._setTimeout(()=>that._pointerOnPoint(point, x, y, e), point);
            } else {
                that._pointerOnPoint(point, x, y, e);
            }
            return;
        } else if (that._setStuckSeries(e, void 0, x, y) && that._stickyHovering) {
            var _point;
            series = that._stuckSeries;
            point = series.getNeighborPoint(x, y);
            that._releaseHoveredSeries();
            (null === (_point = point) || void 0 === _point ? void 0 : _point.getMarkerVisibility()) && that._setHoveredPoint(point);
        } else if (!that._stickyHovering) {
            that._pointerOut();
        }
        that._pointerComplete(point, x, y);
    },
    _pointerOnPoint: function(point, x, y) {
        this._resetHoveredArgument();
        this._setHoveredPoint(point);
        this._pointerComplete(point, x, y);
    },
    _pointerComplete: function(point) {
        this.pointAtShownTooltip !== point && this._tooltip.isEnabled() && this._showTooltip(point);
    },
    _clickHandler: function(e) {
        var _point2;
        const that = e.data.tracker;
        if (that._isHolding) {
            return;
        }
        const rootOffset = that._renderer.getRootOffset();
        const x = _floor(e.pageX - rootOffset.left);
        const y = _floor(e.pageY - rootOffset.top);
        let point = getData(e, POINT_DATA);
        const series = that._stuckSeries || getData(e, SERIES_DATA) || (null === (_point2 = point) || void 0 === _point2 ? void 0 : _point2.series);
        const axis = that._argumentAxis;
        if (that._legend.coordsIn(x, y)) {
            const item = that._legend.getItemByCoord(x, y);
            if (item) {
                that._legendClick(item, e);
            }
        } else if (null !== axis && void 0 !== axis && axis.coordsIn(x, y)) {
            const argument = getData(e, ARG_DATA, true);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(argument)) {
                that._eventTrigger("argumentAxisClick", {
                    argument: argument,
                    event: e
                });
            }
        } else if (series) {
            var _point3;
            point = point || series.getPointByCoord(x, y);
            if (null !== (_point3 = point) && void 0 !== _point3 && _point3.getMarkerVisibility()) {
                that._pointClick(point, e);
            } else {
                getData(e, SERIES_DATA) && that._eventTrigger(SERIES_CLICK, {
                    target: series,
                    event: e
                });
            }
        }
    },
    dispose: function() {
        this._disableOutHandler();
        this._renderer.root.off(DOT_EVENT_NS);
        this._seriesGroup.off(DOT_EVENT_NS);
    }
};
const ChartTracker = function(options) {
    this.ctor(options);
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(ChartTracker.prototype, baseTrackerPrototype, {
    _pointClick: function(point, event) {
        const eventTrigger = this._eventTrigger;
        const series = point.series;
        const eventArgs = {
            target: point,
            event: event
        };
        eventTrigger(POINT_CLICK, eventArgs, function() {
            !eventCanceled(eventArgs, series, "point") && eventTrigger(SERIES_CLICK, {
                target: series,
                event: event
            });
        });
    },
    update: function(options) {
        baseTrackerPrototype.update.call(this, options);
        this._argumentAxis = options.argumentAxis || {};
        this._axisHoverEnabled = this._argumentAxis && "allargumentpoints" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(this._argumentAxis.getOptions().hoverMode);
        this._rotated = options.rotated;
        this._crosshair = options.crosshair;
        this._stickyHovering = options.stickyHovering;
    },
    _getCanvas: function(x, y) {
        const canvases = this._canvases || [];
        for(let i = 0; i < canvases.length; i++){
            const c = canvases[i];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["pointInCanvas"])(c, x, y)) {
                return c;
            }
        }
        return null;
    },
    _isPointerOut: function(canvas, point) {
        return !canvas && this._stuckSeries && (null === point || void 0 === point ? void 0 : point.series) !== this._stuckSeries;
    },
    _hideCrosshair: function() {
        var _this$_crosshair;
        null === (_this$_crosshair = this._crosshair) || void 0 === _this$_crosshair || _this$_crosshair.hide();
    },
    _moveCrosshair: function(point, x, y) {
        if (this._crosshair && null !== point && void 0 !== point && point.isVisible()) {
            this._crosshair.show({
                point: point,
                x: x,
                y: y
            });
        }
    },
    _clean: function() {
        baseTrackerPrototype._clean.call(this);
        this._resetTimer();
        this._stuckSeries = null;
    },
    _getSeriesForShared: function(x, y) {
        var _point4;
        const that = this;
        const points = [];
        let point = null;
        let distance = 1 / 0;
        if (that._tooltip.isShared() && !that.hoveredSeries) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(that._storedSeries, function(_, series) {
                const point = series.getNeighborPoint(x, y);
                point && points.push(point);
            });
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(points, function(_, p) {
                const coords = p.getCrosshairData(x, y);
                const d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDistance"])(x, y, coords.x, coords.y);
                if (d < distance) {
                    point = p;
                    distance = d;
                }
            });
        }
        return null === (_point4 = point) || void 0 === _point4 ? void 0 : _point4.series;
    },
    _setTimeout: function(callback, keeper) {
        const that = this;
        if (that._timeoutKeeper !== keeper) {
            that._resetTimer();
            that._hoverTimeout = setTimeout(function() {
                callback();
                that._timeoutKeeper = null;
            }, 100);
            that._timeoutKeeper = keeper;
        }
    },
    _resetTimer: function() {
        clearTimeout(this._hoverTimeout);
        this._timeoutKeeper = this._hoverTimeout = null;
    },
    _stopEvent: function(e) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(e.cancelable) || e.cancelable) {
            e.preventDefault();
            e.stopPropagation();
        }
    },
    _setStuckSeries: function(e, series, x, y) {
        if ("mouse" !== e.pointerType) {
            this._stuckSeries = null;
        } else {
            this._stuckSeries = series || this._stuckSeries || this._getSeriesForShared(x, y);
        }
        return !!this._stuckSeries;
    },
    _pointerOut: function() {
        this._stuckSeries = null;
        this._hideCrosshair();
        this._resetTimer();
        baseTrackerPrototype._pointerOut.apply(this, arguments);
    },
    _hoverArgumentAxis: function(x, y, e) {
        const that = this;
        that._resetHoveredArgument();
        if (that._axisHoverEnabled && that._argumentAxis.coordsIn(x, y)) {
            that._hoverArgument(getData(e, ARG_DATA, true));
            return true;
        }
    },
    _pointerComplete: function(point, x, y) {
        this.hoveredSeries && this.hoveredSeries.updateHover(x, y);
        this._resetTimer();
        this._moveCrosshair(point, x, y);
        baseTrackerPrototype._pointerComplete.call(this, point);
    },
    _legendClick: function(item, e) {
        const series = this._storedSeries[item.id];
        this._triggerLegendClick({
            target: series,
            event: e
        }, SERIES_CLICK);
    },
    _hoverLegendItem: function(x, y) {
        this._stuckSeries = null;
        this._hideCrosshair();
        baseTrackerPrototype._hoverLegendItem.call(this, x, y);
    },
    _pointerOnPoint: function(point, x, y, e) {
        this._setStuckSeries(e, point.series, x, y);
        this._releaseHoveredSeries();
        baseTrackerPrototype._pointerOnPoint.call(this, point, x, y, e);
    },
    _notifyLegendOnHoverArgument: false,
    _getArgumentHoverMode: function() {
        return correctHoverMode(this._argumentAxis);
    },
    dispose: function() {
        this._resetTimer();
        baseTrackerPrototype.dispose.call(this);
    }
});
const PieTracker = function(options) {
    this.ctor(options);
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(PieTracker.prototype, baseTrackerPrototype, {
    _isPointerOut: function(_, point) {
        return !point;
    },
    _legendClick: function(item, e) {
        const points = [];
        this._storedSeries.forEach((s)=>points.push.apply(points, s.getPointsByKeys(item.argument, item.argumentIndex)));
        this._eventTrigger(LEGEND_CLICK, {
            target: item.argument,
            points: points,
            event: e
        });
    },
    _pointClick: function(point, e) {
        this._eventTrigger(POINT_CLICK, {
            target: point,
            event: e
        });
    },
    _hoverLegendItem: function(x, y) {
        const that = this;
        const item = that._legend.getItemByCoord(x, y);
        if (item && that._hoveredArgument !== item.argument) {
            that._resetHoveredArgument();
            that._hoverArgument(item.argument, item.argumentIndex);
        } else if (!item) {
            that.clearHover();
        }
    },
    _getArgumentHoverMode: function() {
        return correctHoverMode(this._legend);
    },
    _hoverArgumentAxis: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    _setStuckSeries: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    _getCanvas: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    _notifyLegendOnHoverArgument: true
});
}),

};

//# sourceMappingURL=node_modules_devextreme_esm_viz_chart_components_a4183602._.js.map