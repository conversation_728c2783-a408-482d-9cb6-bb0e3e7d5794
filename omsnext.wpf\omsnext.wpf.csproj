﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <!-- Specific DevExpress Packages for v25.1.3 -->

    <PackageReference Include="DevExtreme.AspNet.Data" Version="5.1.0" />

    <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.0-preview.5.24306.7" />

    <PackageReference Include="System.Drawing.Common" Version="9.0.8" />
    <PackageReference Include="System.Text.Json" Version="9.0.0-preview.5.24306.7" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\omsnext.shared\omsnext.shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="DevExpress.Data.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Data.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Mvvm.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Mvvm.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Printing.v25.1.Core">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Printing.v25.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Accordion.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Accordion.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Controls.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Controls.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Core.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Core.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Docking.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Docking.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Grid.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Grid.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Grid.v25.1.Core">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Grid.v25.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Layout.v25.1.Core">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Layout.v25.1.Core.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.LayoutControl.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.LayoutControl.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.NavBar.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.NavBar.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Ribbon.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Ribbon.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpf.Themes.Win11Light.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpf.Themes.Win11Light.v25.1.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
