﻿<dx:ThemedWindow x:Class="omsnext.wpf.RequestDetailDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        Title="Request Details" 
        Height="600" Width="700"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Grid Margin="25">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,20">
            <TextBlock Text="Password Reset Request Details" 
                     FontSize="22" 
                     FontWeight="Bold" 
                     Foreground="#1F2937"
                     Margin="0,0,0,5"/>
            <TextBlock x:Name="RequestIdLabel" 
                     Text="Request ID: #12345" 
                     FontSize="14" 
                     Foreground="#6B7280"/>
        </StackPanel>
        
        <!-- Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="0,0,0,20">
            <StackPanel>
                
                <!-- User Information -->
                <Border CornerRadius="10" 
                      Background="#F8FAFC" 
                      BorderBrush="#E2E8F0" 
                      BorderThickness="1" 
                      Padding="20" 
                      Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="User Information" 
                                 FontSize="16" 
                                 FontWeight="SemiBold" 
                                 Foreground="#374151"
                                 Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Name:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="UserNameLabel" Text="" Margin="0,0,0,8"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="E-mail:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="UserEmailLabel" Text="" Margin="0,0,0,8"/>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="Source:" FontWeight="Medium"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="RequestSourceLabel" Text=""/>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Request Details -->
                <Border CornerRadius="10" 
                      Background="#F0F9FF" 
                      BorderBrush="#BAE6FD" 
                      BorderThickness="1" 
                      Padding="20" 
                      Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Request Details" 
                                 FontSize="16" 
                                 FontWeight="SemiBold" 
                                 Foreground="#374151"
                                 Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Request Date:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="RequestDateLabel" Text="" Margin="0,0,0,8"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Status:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <Border Grid.Row="1" Grid.Column="1" x:Name="StatusBorder" 
                                  CornerRadius="15" 
                                  Padding="10,5" 
                                  HorizontalAlignment="Left"
                                  Margin="0,0,0,8">
                                <TextBlock x:Name="StatusLabel" 
                                         Text="" 
                                         FontWeight="SemiBold" 
                                         FontSize="12"/>
                            </Border>
                            
                            <TextBlock Grid.Row="2" Grid.Column="0" Text="IP Address:" FontWeight="Medium"/>
                            <TextBlock Grid.Row="2" Grid.Column="1" x:Name="IpAddressLabel" Text=""/>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Processing Information -->
                <Border x:Name="ProcessingInfoBorder"
                      CornerRadius="10" 
                      Background="#F0FDF4" 
                      BorderBrush="#BBF7D0" 
                      BorderThickness="1" 
                      Padding="20" 
                      Margin="0,0,0,20"
                      Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="Processing Information" 
                                 FontSize="16" 
                                 FontWeight="SemiBold" 
                                 Foreground="#374151"
                                 Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="150"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>
                            
                            <TextBlock Grid.Row="0" Grid.Column="0" Text="Processed By:" FontWeight="Medium" Margin="0,0,0,8"/>
                            <TextBlock Grid.Row="0" Grid.Column="1" x:Name="ProcessedByLabel" Text="" Margin="0,0,0,8"/>
                            
                            <TextBlock Grid.Row="1" Grid.Column="0" Text="Processed Date:" FontWeight="Medium"/>
                            <TextBlock Grid.Row="1" Grid.Column="1" x:Name="ProcessedDateLabel" Text=""/>
                        </Grid>
                    </StackPanel>
                </Border>
                
                <!-- Admin Notes -->
                <Border x:Name="AdminNotesBorder"
                      CornerRadius="10" 
                      Background="#FFFBEB" 
                      BorderBrush="#FED7AA" 
                      BorderThickness="1" 
                      Padding="20"
                      Visibility="Collapsed">
                    <StackPanel>
                        <TextBlock Text="Admin Notes" 
                                 FontSize="16" 
                                 FontWeight="SemiBold" 
                                 Foreground="#374151"
                                 Margin="0,0,0,15"/>
                        
                        <Border CornerRadius="8" 
                              Background="White" 
                              BorderBrush="#E2E8F0" 
                              BorderThickness="1" 
                              Padding="15">
                            <TextBlock x:Name="AdminNotesLabel" 
                                     Text="" 
                                     TextWrapping="Wrap"
                                     LineHeight="22"/>
                        </Border>
                    </StackPanel>
                </Border>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- Close Button -->
        <Button Grid.Row="2" 
              Content="Close" 
              Width="100" 
              Height="40"
              HorizontalAlignment="Right"
              Click="CloseButton_Click">
            <Button.Style>
                <Style TargetType="Button">
                    <Setter Property="Background" Value="#3B82F6"/>
                    <Setter Property="Foreground" Value="White"/>
                    <Setter Property="BorderThickness" Value="0"/>
                    <Setter Property="Template">
                        <Setter.Value>
                            <ControlTemplate TargetType="Button">
                                <Border Background="{TemplateBinding Background}" 
                                      BorderThickness="{TemplateBinding BorderThickness}" 
                                      CornerRadius="8">
                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                            </ControlTemplate>
                        </Setter.Value>
                    </Setter>
                    <Style.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Setter Property="Background" Value="#2563EB"/>
                        </Trigger>
                    </Style.Triggers>
                </Style>
            </Button.Style>
        </Button>
    </Grid>
</dx:ThemedWindow>