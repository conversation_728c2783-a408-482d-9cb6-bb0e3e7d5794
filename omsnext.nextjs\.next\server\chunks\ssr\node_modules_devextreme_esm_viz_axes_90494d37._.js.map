{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/smart_formatter.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/smart_formatter.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport formatHelper from \"../../format_helper\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isExponential,\r\n    isObject\r\n} from \"../../core/utils/type\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nimport {\r\n    adjust,\r\n    getPrecision,\r\n    getExponent\r\n} from \"../../core/utils/math\";\r\nimport {\r\n    getAdjustedLog10 as log10\r\n} from \"../core/utils\";\r\nconst _format = formatHelper.format;\r\nconst {\r\n    abs: abs,\r\n    floor: floor\r\n} = Math;\r\nconst EXPONENTIAL = \"exponential\";\r\nconst formats = [\"fixedPoint\", \"thousands\", \"millions\", \"billions\", \"trillions\", EXPONENTIAL];\r\nconst dateUnitIntervals = [\"millisecond\", \"second\", \"minute\", \"hour\", \"day\", \"month\", \"year\"];\r\nconst INTERVALS_MAP = {\r\n    week: \"day\",\r\n    quarter: \"month\",\r\n    shorttime: \"hour\",\r\n    longtime: \"second\"\r\n};\r\n\r\nfunction patchFirstTickDiff(differences, tickFormatIndex) {\r\n    for (let i = tickFormatIndex; i < dateUnitIntervals.length - 1; i++) {\r\n        const dateUnitInterval = dateUnitIntervals[i];\r\n        if (i === tickFormatIndex) {\r\n            setDateUnitInterval(differences, tickFormatIndex + (differences.millisecond ? 2 : 1));\r\n            break\r\n        } else if (differences[dateUnitInterval] && differences.count > 1) {\r\n            resetDateUnitInterval(differences, i);\r\n            break\r\n        }\r\n    }\r\n}\r\n\r\nfunction patchTickDiff(differences, tickFormatIndex) {\r\n    let patched = false;\r\n    for (let i = dateUnitIntervals.length - 1; i >= tickFormatIndex; i--) {\r\n        const dateUnitInterval = dateUnitIntervals[i];\r\n        if (differences[dateUnitInterval]) {\r\n            if (i - tickFormatIndex > 1) {\r\n                for (let j = 0; j <= tickFormatIndex; j++) {\r\n                    resetDateUnitInterval(differences, j);\r\n                    patched = true\r\n                }\r\n                break\r\n            }\r\n        }\r\n    }\r\n    return patched\r\n}\r\n\r\nfunction getDatesDifferences(prevDate, curDate, nextDate, tickIntervalFormat) {\r\n    tickIntervalFormat = INTERVALS_MAP[tickIntervalFormat] || tickIntervalFormat;\r\n    const tickFormatIndex = dateUnitIntervals.indexOf(tickIntervalFormat);\r\n    if (nextDate) {\r\n        const nextDifferences = dateUtils.getDatesDifferences(curDate, nextDate);\r\n        if (nextDifferences[tickIntervalFormat]) {\r\n            patchFirstTickDiff(nextDifferences, tickFormatIndex)\r\n        }\r\n        return nextDifferences\r\n    } else {\r\n        const prevDifferences = dateUtils.getDatesDifferences(prevDate, curDate);\r\n        const patched = patchTickDiff(prevDifferences, tickFormatIndex);\r\n        if (!patched && 1 === prevDifferences.count) {\r\n            setDateUnitInterval(prevDifferences, tickFormatIndex)\r\n        }\r\n        return prevDifferences\r\n    }\r\n}\r\n\r\nfunction resetDateUnitInterval(differences, intervalIndex) {\r\n    const dateUnitInterval = dateUnitIntervals[intervalIndex];\r\n    if (differences[dateUnitInterval]) {\r\n        differences[dateUnitInterval] = false;\r\n        differences.count--\r\n    }\r\n}\r\n\r\nfunction setDateUnitInterval(differences, intervalIndex) {\r\n    const dateUnitInterval = dateUnitIntervals[intervalIndex];\r\n    if (false === differences[dateUnitInterval]) {\r\n        differences[dateUnitInterval] = true;\r\n        differences.count++\r\n    }\r\n}\r\n\r\nfunction getNoZeroIndex(str) {\r\n    return str.length - parseInt(str).toString().length\r\n}\r\n\r\nfunction getTransitionTickIndex(ticks, value) {\r\n    let i;\r\n    let curDiff;\r\n    let minDiff;\r\n    let nearestTickIndex = 0;\r\n    minDiff = abs(value - ticks[0]);\r\n    for (i = 1; i < ticks.length; i++) {\r\n        curDiff = abs(value - ticks[i]);\r\n        if (curDiff < minDiff) {\r\n            minDiff = curDiff;\r\n            nearestTickIndex = i\r\n        }\r\n    }\r\n    return nearestTickIndex\r\n}\r\n\r\nfunction splitDecimalNumber(value) {\r\n    return value.toString().split(\".\")\r\n}\r\n\r\nfunction createFormat(type) {\r\n    let formatter;\r\n    if (isFunction(type)) {\r\n        formatter = type;\r\n        type = null\r\n    }\r\n    return {\r\n        type: type,\r\n        formatter: formatter\r\n    }\r\n}\r\n\r\nfunction formatLogarithmicNumber(tick) {\r\n    const log10Tick = log10(abs(tick));\r\n    let type;\r\n    if (log10Tick > 0) {\r\n        type = formats[floor(log10Tick / 3)] || EXPONENTIAL\r\n    } else if (log10Tick < -4) {\r\n        type = EXPONENTIAL\r\n    } else {\r\n        return _format(adjust(tick))\r\n    }\r\n    return _format(tick, {\r\n        type: type,\r\n        precision: 0\r\n    })\r\n}\r\n\r\nfunction getDateTimeFormat(tick, _ref) {\r\n    let {\r\n        showTransition: showTransition,\r\n        ticks: ticks,\r\n        tickInterval: tickInterval\r\n    } = _ref;\r\n    let typeFormat = dateUtils.getDateFormatByTickInterval(tickInterval);\r\n    let prevDateIndex;\r\n    let nextDateIndex;\r\n    if (showTransition && ticks.length) {\r\n        const indexOfTick = ticks.map(Number).indexOf(+tick);\r\n        if (1 === ticks.length && 0 === indexOfTick) {\r\n            typeFormat = formatHelper.getDateFormatByTicks(ticks)\r\n        } else {\r\n            if (-1 === indexOfTick) {\r\n                prevDateIndex = getTransitionTickIndex(ticks, tick)\r\n            } else {\r\n                prevDateIndex = 0 === indexOfTick ? ticks.length - 1 : indexOfTick - 1;\r\n                nextDateIndex = 0 === indexOfTick ? 1 : -1\r\n            }\r\n            const datesDifferences = getDatesDifferences(ticks[prevDateIndex], tick, ticks[nextDateIndex], typeFormat);\r\n            typeFormat = formatHelper.getDateFormatByDifferences(datesDifferences, typeFormat)\r\n        }\r\n    }\r\n    return createFormat(typeFormat)\r\n}\r\n\r\nfunction getFormatExponential(tick, tickInterval) {\r\n    const stringTick = abs(tick).toString();\r\n    if (isExponential(tick)) {\r\n        return Math.max(abs(getExponent(tick) - getExponent(tickInterval)), abs(getPrecision(tick) - getPrecision(tickInterval)))\r\n    } else {\r\n        return abs(getNoZeroIndex(stringTick.split(\".\")[1]) - getExponent(tickInterval) + 1)\r\n    }\r\n}\r\n\r\nfunction getFormatWithModifier(tick, tickInterval) {\r\n    const tickIntervalIndex = floor(log10(tickInterval));\r\n    let tickIndex;\r\n    let precision = 0;\r\n    let actualIndex = tickIndex = floor(log10(abs(tick)));\r\n    if (tickIndex - tickIntervalIndex >= 2) {\r\n        actualIndex = tickIntervalIndex\r\n    }\r\n    let indexOfFormat = floor(actualIndex / 3);\r\n    const offset = 3 * indexOfFormat;\r\n    if (indexOfFormat < 0) {\r\n        indexOfFormat = 0\r\n    }\r\n    const typeFormat = formats[indexOfFormat] || formats[formats.length - 1];\r\n    if (offset > 0) {\r\n        const separatedTickInterval = splitDecimalNumber(tickInterval / Math.pow(10, offset));\r\n        if (separatedTickInterval[1]) {\r\n            precision = separatedTickInterval[1].length\r\n        }\r\n    }\r\n    return {\r\n        precision: precision,\r\n        type: typeFormat\r\n    }\r\n}\r\n\r\nfunction getHighDiffFormat(diff) {\r\n    let stop = false;\r\n    for (const i in diff) {\r\n        if (true === diff[i] || \"hour\" === i || stop) {\r\n            diff[i] = false;\r\n            stop = true\r\n        } else if (false === diff[i]) {\r\n            diff[i] = true\r\n        }\r\n    }\r\n    return createFormat(formatHelper.getDateFormatByDifferences(diff))\r\n}\r\n\r\nfunction getHighAndSelfDiffFormat(diff, interval) {\r\n    let stop = false;\r\n    for (const i in diff) {\r\n        if (stop) {\r\n            diff[i] = false\r\n        } else if (i === interval) {\r\n            stop = true\r\n        } else {\r\n            diff[i] = true\r\n        }\r\n    }\r\n    return createFormat(formatHelper.getDateFormatByDifferences(diff))\r\n}\r\n\r\nfunction formatDateRange(startValue, endValue, tickInterval) {\r\n    const diff = getDatesDifferences(startValue, endValue);\r\n    const typeFormat = dateUtils.getDateFormatByTickInterval(tickInterval);\r\n    const diffFormatType = formatHelper.getDateFormatByDifferences(diff, typeFormat);\r\n    const diffFormat = createFormat(diffFormatType);\r\n    const values = [];\r\n    if (tickInterval in diff) {\r\n        const rangeFormat = getHighAndSelfDiffFormat(getDatesDifferences(startValue, endValue), tickInterval);\r\n        const value = _format(startValue, rangeFormat);\r\n        if (value) {\r\n            values.push(value)\r\n        }\r\n    } else {\r\n        const rangeFormat = getHighDiffFormat(getDatesDifferences(startValue, endValue));\r\n        const highValue = _format(startValue, rangeFormat);\r\n        if (highValue) {\r\n            values.push(highValue)\r\n        }\r\n        values.push(`${_format(startValue,diffFormat)} - ${_format(endValue,diffFormat)}`)\r\n    }\r\n    return values.join(\", \")\r\n}\r\n\r\nfunction processDateInterval(interval) {\r\n    if (isObject(interval)) {\r\n        const dateUnits = Object.keys(interval);\r\n        const sum = dateUnits.reduce(((sum, k) => interval[k] + sum), 0);\r\n        if (1 === sum) {\r\n            const dateUnit = dateUnits.filter((k => 1 === interval[k]))[0];\r\n            return dateUnit.slice(0, dateUnit.length - 1)\r\n        }\r\n    }\r\n    return interval\r\n}\r\nexport function smartFormatter(tick, options) {\r\n    let tickInterval = options.tickInterval;\r\n    const stringTick = abs(tick).toString();\r\n    let format = options.labelOptions.format;\r\n    const ticks = options.ticks;\r\n    const isLogarithmic = \"logarithmic\" === options.type;\r\n    if (1 === ticks.length && 0 === ticks.indexOf(tick) && !isDefined(tickInterval)) {\r\n        tickInterval = abs(tick) >= 1 ? 1 : adjust(1 - abs(tick), tick)\r\n    }\r\n    if (Object.is(tick, -0)) {\r\n        tick = 0\r\n    }\r\n    if (!isDefined(format) && \"discrete\" !== options.type && tick && (10 === options.logarithmBase || !isLogarithmic)) {\r\n        if (\"datetime\" !== options.dataType && isDefined(tickInterval)) {\r\n            if (ticks.length && -1 === ticks.indexOf(tick)) {\r\n                const indexOfTick = getTransitionTickIndex(ticks, tick);\r\n                tickInterval = adjust(abs(tick - ticks[indexOfTick]), tick)\r\n            }\r\n            if (isLogarithmic) {\r\n                return formatLogarithmicNumber(tick)\r\n            } else {\r\n                let separatedTickInterval = splitDecimalNumber(tickInterval);\r\n                if (separatedTickInterval < 2) {\r\n                    separatedTickInterval = splitDecimalNumber(tick)\r\n                }\r\n                if (separatedTickInterval.length > 1 && !isExponential(tickInterval)) {\r\n                    format = {\r\n                        type: formats[0],\r\n                        precision: separatedTickInterval[1].length\r\n                    }\r\n                } else if (isExponential(tickInterval) && (-1 !== stringTick.indexOf(\".\") || isExponential(tick))) {\r\n                    format = {\r\n                        type: EXPONENTIAL,\r\n                        precision: getFormatExponential(tick, tickInterval)\r\n                    }\r\n                } else {\r\n                    format = getFormatWithModifier(tick, tickInterval)\r\n                }\r\n            }\r\n        } else if (\"datetime\" === options.dataType) {\r\n            format = getDateTimeFormat(tick, options)\r\n        }\r\n    }\r\n    return _format(tick, format)\r\n}\r\nexport function formatRange(_ref2) {\r\n    let {\r\n        startValue: startValue,\r\n        endValue: endValue,\r\n        tickInterval: tickInterval,\r\n        argumentFormat: argumentFormat,\r\n        axisOptions: {\r\n            dataType: dataType,\r\n            type: type,\r\n            logarithmBase: logarithmBase\r\n        }\r\n    } = _ref2;\r\n    if (\"discrete\" === type) {\r\n        return \"\"\r\n    }\r\n    if (\"datetime\" === dataType) {\r\n        return formatDateRange(startValue, endValue, processDateInterval(tickInterval))\r\n    }\r\n    const formatOptions = {\r\n        ticks: [],\r\n        type: type,\r\n        dataType: dataType,\r\n        tickInterval: tickInterval,\r\n        logarithmBase: logarithmBase,\r\n        labelOptions: {\r\n            format: argumentFormat\r\n        }\r\n    };\r\n    return `${smartFormatter(startValue,formatOptions)} - ${smartFormatter(endValue,formatOptions)}`\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AACA;AAAA;AAMA;AACA;AAAA;AAKA;;;;;;AAGA,MAAM,UAAU,kJAAA,CAAA,UAAY,CAAC,MAAM;AACnC,MAAM,EACF,KAAK,GAAG,EACR,OAAO,KAAK,EACf,GAAG;AACJ,MAAM,cAAc;AACpB,MAAM,UAAU;IAAC;IAAc;IAAa;IAAY;IAAY;IAAa;CAAY;AAC7F,MAAM,oBAAoB;IAAC;IAAe;IAAU;IAAU;IAAQ;IAAO;IAAS;CAAO;AAC7F,MAAM,gBAAgB;IAClB,MAAM;IACN,SAAS;IACT,WAAW;IACX,UAAU;AACd;AAEA,SAAS,mBAAmB,WAAW,EAAE,eAAe;IACpD,IAAK,IAAI,IAAI,iBAAiB,IAAI,kBAAkB,MAAM,GAAG,GAAG,IAAK;QACjE,MAAM,mBAAmB,iBAAiB,CAAC,EAAE;QAC7C,IAAI,MAAM,iBAAiB;YACvB,oBAAoB,aAAa,kBAAkB,CAAC,YAAY,WAAW,GAAG,IAAI,CAAC;YACnF;QACJ,OAAO,IAAI,WAAW,CAAC,iBAAiB,IAAI,YAAY,KAAK,GAAG,GAAG;YAC/D,sBAAsB,aAAa;YACnC;QACJ;IACJ;AACJ;AAEA,SAAS,cAAc,WAAW,EAAE,eAAe;IAC/C,IAAI,UAAU;IACd,IAAK,IAAI,IAAI,kBAAkB,MAAM,GAAG,GAAG,KAAK,iBAAiB,IAAK;QAClE,MAAM,mBAAmB,iBAAiB,CAAC,EAAE;QAC7C,IAAI,WAAW,CAAC,iBAAiB,EAAE;YAC/B,IAAI,IAAI,kBAAkB,GAAG;gBACzB,IAAK,IAAI,IAAI,GAAG,KAAK,iBAAiB,IAAK;oBACvC,sBAAsB,aAAa;oBACnC,UAAU;gBACd;gBACA;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,QAAQ,EAAE,OAAO,EAAE,QAAQ,EAAE,kBAAkB;IACxE,qBAAqB,aAAa,CAAC,mBAAmB,IAAI;IAC1D,MAAM,kBAAkB,kBAAkB,OAAO,CAAC;IAClD,IAAI,UAAU;QACV,MAAM,kBAAkB,0JAAA,CAAA,UAAS,CAAC,mBAAmB,CAAC,SAAS;QAC/D,IAAI,eAAe,CAAC,mBAAmB,EAAE;YACrC,mBAAmB,iBAAiB;QACxC;QACA,OAAO;IACX,OAAO;QACH,MAAM,kBAAkB,0JAAA,CAAA,UAAS,CAAC,mBAAmB,CAAC,UAAU;QAChE,MAAM,UAAU,cAAc,iBAAiB;QAC/C,IAAI,CAAC,WAAW,MAAM,gBAAgB,KAAK,EAAE;YACzC,oBAAoB,iBAAiB;QACzC;QACA,OAAO;IACX;AACJ;AAEA,SAAS,sBAAsB,WAAW,EAAE,aAAa;IACrD,MAAM,mBAAmB,iBAAiB,CAAC,cAAc;IACzD,IAAI,WAAW,CAAC,iBAAiB,EAAE;QAC/B,WAAW,CAAC,iBAAiB,GAAG;QAChC,YAAY,KAAK;IACrB;AACJ;AAEA,SAAS,oBAAoB,WAAW,EAAE,aAAa;IACnD,MAAM,mBAAmB,iBAAiB,CAAC,cAAc;IACzD,IAAI,UAAU,WAAW,CAAC,iBAAiB,EAAE;QACzC,WAAW,CAAC,iBAAiB,GAAG;QAChC,YAAY,KAAK;IACrB;AACJ;AAEA,SAAS,eAAe,GAAG;IACvB,OAAO,IAAI,MAAM,GAAG,SAAS,KAAK,QAAQ,GAAG,MAAM;AACvD;AAEA,SAAS,uBAAuB,KAAK,EAAE,KAAK;IACxC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,mBAAmB;IACvB,UAAU,IAAI,QAAQ,KAAK,CAAC,EAAE;IAC9B,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAC/B,UAAU,IAAI,QAAQ,KAAK,CAAC,EAAE;QAC9B,IAAI,UAAU,SAAS;YACnB,UAAU;YACV,mBAAmB;QACvB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,mBAAmB,KAAK;IAC7B,OAAO,MAAM,QAAQ,GAAG,KAAK,CAAC;AAClC;AAEA,SAAS,aAAa,IAAI;IACtB,IAAI;IACJ,IAAI,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,OAAO;QAClB,YAAY;QACZ,OAAO;IACX;IACA,OAAO;QACH,MAAM;QACN,WAAW;IACf;AACJ;AAEA,SAAS,wBAAwB,IAAI;IACjC,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,mBAAK,AAAD,EAAE,IAAI;IAC5B,IAAI;IACJ,IAAI,YAAY,GAAG;QACf,OAAO,OAAO,CAAC,MAAM,YAAY,GAAG,IAAI;IAC5C,OAAO,IAAI,YAAY,CAAC,GAAG;QACvB,OAAO;IACX,OAAO;QACH,OAAO,QAAQ,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE;IAC1B;IACA,OAAO,QAAQ,MAAM;QACjB,MAAM;QACN,WAAW;IACf;AACJ;AAEA,SAAS,kBAAkB,IAAI,EAAE,IAAI;IACjC,IAAI,EACA,gBAAgB,cAAc,EAC9B,OAAO,KAAK,EACZ,cAAc,YAAY,EAC7B,GAAG;IACJ,IAAI,aAAa,0JAAA,CAAA,UAAS,CAAC,2BAA2B,CAAC;IACvD,IAAI;IACJ,IAAI;IACJ,IAAI,kBAAkB,MAAM,MAAM,EAAE;QAChC,MAAM,cAAc,MAAM,GAAG,CAAC,QAAQ,OAAO,CAAC,CAAC;QAC/C,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,aAAa;YACzC,aAAa,kJAAA,CAAA,UAAY,CAAC,oBAAoB,CAAC;QACnD,OAAO;YACH,IAAI,CAAC,MAAM,aAAa;gBACpB,gBAAgB,uBAAuB,OAAO;YAClD,OAAO;gBACH,gBAAgB,MAAM,cAAc,MAAM,MAAM,GAAG,IAAI,cAAc;gBACrE,gBAAgB,MAAM,cAAc,IAAI,CAAC;YAC7C;YACA,MAAM,mBAAmB,oBAAoB,KAAK,CAAC,cAAc,EAAE,MAAM,KAAK,CAAC,cAAc,EAAE;YAC/F,aAAa,kJAAA,CAAA,UAAY,CAAC,0BAA0B,CAAC,kBAAkB;QAC3E;IACJ;IACA,OAAO,aAAa;AACxB;AAEA,SAAS,qBAAqB,IAAI,EAAE,YAAY;IAC5C,MAAM,aAAa,IAAI,MAAM,QAAQ;IACrC,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;QACrB,OAAO,KAAK,GAAG,CAAC,IAAI,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,IAAI,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;IAC9G,OAAO;QACH,OAAO,IAAI,eAAe,WAAW,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,CAAA,GAAA,6KAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB;IACtF;AACJ;AAEA,SAAS,sBAAsB,IAAI,EAAE,YAAY;IAC7C,MAAM,oBAAoB,MAAM,CAAA,GAAA,yJAAA,CAAA,mBAAK,AAAD,EAAE;IACtC,IAAI;IACJ,IAAI,YAAY;IAChB,IAAI,cAAc,YAAY,MAAM,CAAA,GAAA,yJAAA,CAAA,mBAAK,AAAD,EAAE,IAAI;IAC9C,IAAI,YAAY,qBAAqB,GAAG;QACpC,cAAc;IAClB;IACA,IAAI,gBAAgB,MAAM,cAAc;IACxC,MAAM,SAAS,IAAI;IACnB,IAAI,gBAAgB,GAAG;QACnB,gBAAgB;IACpB;IACA,MAAM,aAAa,OAAO,CAAC,cAAc,IAAI,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;IACxE,IAAI,SAAS,GAAG;QACZ,MAAM,wBAAwB,mBAAmB,eAAe,KAAK,GAAG,CAAC,IAAI;QAC7E,IAAI,qBAAqB,CAAC,EAAE,EAAE;YAC1B,YAAY,qBAAqB,CAAC,EAAE,CAAC,MAAM;QAC/C;IACJ;IACA,OAAO;QACH,WAAW;QACX,MAAM;IACV;AACJ;AAEA,SAAS,kBAAkB,IAAI;IAC3B,IAAI,OAAO;IACX,IAAK,MAAM,KAAK,KAAM;QAClB,IAAI,SAAS,IAAI,CAAC,EAAE,IAAI,WAAW,KAAK,MAAM;YAC1C,IAAI,CAAC,EAAE,GAAG;YACV,OAAO;QACX,OAAO,IAAI,UAAU,IAAI,CAAC,EAAE,EAAE;YAC1B,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA,OAAO,aAAa,kJAAA,CAAA,UAAY,CAAC,0BAA0B,CAAC;AAChE;AAEA,SAAS,yBAAyB,IAAI,EAAE,QAAQ;IAC5C,IAAI,OAAO;IACX,IAAK,MAAM,KAAK,KAAM;QAClB,IAAI,MAAM;YACN,IAAI,CAAC,EAAE,GAAG;QACd,OAAO,IAAI,MAAM,UAAU;YACvB,OAAO;QACX,OAAO;YACH,IAAI,CAAC,EAAE,GAAG;QACd;IACJ;IACA,OAAO,aAAa,kJAAA,CAAA,UAAY,CAAC,0BAA0B,CAAC;AAChE;AAEA,SAAS,gBAAgB,UAAU,EAAE,QAAQ,EAAE,YAAY;IACvD,MAAM,OAAO,oBAAoB,YAAY;IAC7C,MAAM,aAAa,0JAAA,CAAA,UAAS,CAAC,2BAA2B,CAAC;IACzD,MAAM,iBAAiB,kJAAA,CAAA,UAAY,CAAC,0BAA0B,CAAC,MAAM;IACrE,MAAM,aAAa,aAAa;IAChC,MAAM,SAAS,EAAE;IACjB,IAAI,gBAAgB,MAAM;QACtB,MAAM,cAAc,yBAAyB,oBAAoB,YAAY,WAAW;QACxF,MAAM,QAAQ,QAAQ,YAAY;QAClC,IAAI,OAAO;YACP,OAAO,IAAI,CAAC;QAChB;IACJ,OAAO;QACH,MAAM,cAAc,kBAAkB,oBAAoB,YAAY;QACtE,MAAM,YAAY,QAAQ,YAAY;QACtC,IAAI,WAAW;YACX,OAAO,IAAI,CAAC;QAChB;QACA,OAAO,IAAI,CAAC,GAAG,QAAQ,YAAW,YAAY,GAAG,EAAE,QAAQ,UAAS,aAAa;IACrF;IACA,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA,SAAS,oBAAoB,QAAQ;IACjC,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;QACpB,MAAM,YAAY,OAAO,IAAI,CAAC;QAC9B,MAAM,MAAM,UAAU,MAAM,CAAE,CAAC,KAAK,IAAM,QAAQ,CAAC,EAAE,GAAG,KAAM;QAC9D,IAAI,MAAM,KAAK;YACX,MAAM,WAAW,UAAU,MAAM,CAAE,CAAA,IAAK,MAAM,QAAQ,CAAC,EAAE,CAAE,CAAC,EAAE;YAC9D,OAAO,SAAS,KAAK,CAAC,GAAG,SAAS,MAAM,GAAG;QAC/C;IACJ;IACA,OAAO;AACX;AACO,SAAS,eAAe,IAAI,EAAE,OAAO;IACxC,IAAI,eAAe,QAAQ,YAAY;IACvC,MAAM,aAAa,IAAI,MAAM,QAAQ;IACrC,IAAI,SAAS,QAAQ,YAAY,CAAC,MAAM;IACxC,MAAM,QAAQ,QAAQ,KAAK;IAC3B,MAAM,gBAAgB,kBAAkB,QAAQ,IAAI;IACpD,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM,MAAM,OAAO,CAAC,SAAS,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;QAC7E,eAAe,IAAI,SAAS,IAAI,IAAI,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,IAAI,OAAO;IAC9D;IACA,IAAI,OAAO,EAAE,CAAC,MAAM,CAAC,IAAI;QACrB,OAAO;IACX;IACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,eAAe,QAAQ,IAAI,IAAI,QAAQ,CAAC,OAAO,QAAQ,aAAa,IAAI,CAAC,aAAa,GAAG;QAC/G,IAAI,eAAe,QAAQ,QAAQ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YAC5D,IAAI,MAAM,MAAM,IAAI,CAAC,MAAM,MAAM,OAAO,CAAC,OAAO;gBAC5C,MAAM,cAAc,uBAAuB,OAAO;gBAClD,eAAe,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,OAAO,KAAK,CAAC,YAAY,GAAG;YAC1D;YACA,IAAI,eAAe;gBACf,OAAO,wBAAwB;YACnC,OAAO;gBACH,IAAI,wBAAwB,mBAAmB;gBAC/C,IAAI,wBAAwB,GAAG;oBAC3B,wBAAwB,mBAAmB;gBAC/C;gBACA,IAAI,sBAAsB,MAAM,GAAG,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,eAAe;oBAClE,SAAS;wBACL,MAAM,OAAO,CAAC,EAAE;wBAChB,WAAW,qBAAqB,CAAC,EAAE,CAAC,MAAM;oBAC9C;gBACJ,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,CAAC,CAAC,MAAM,WAAW,OAAO,CAAC,QAAQ,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,GAAG;oBAC/F,SAAS;wBACL,MAAM;wBACN,WAAW,qBAAqB,MAAM;oBAC1C;gBACJ,OAAO;oBACH,SAAS,sBAAsB,MAAM;gBACzC;YACJ;QACJ,OAAO,IAAI,eAAe,QAAQ,QAAQ,EAAE;YACxC,SAAS,kBAAkB,MAAM;QACrC;IACJ;IACA,OAAO,QAAQ,MAAM;AACzB;AACO,SAAS,YAAY,KAAK;IAC7B,IAAI,EACA,YAAY,UAAU,EACtB,UAAU,QAAQ,EAClB,cAAc,YAAY,EAC1B,gBAAgB,cAAc,EAC9B,aAAa,EACT,UAAU,QAAQ,EAClB,MAAM,IAAI,EACV,eAAe,aAAa,EAC/B,EACJ,GAAG;IACJ,IAAI,eAAe,MAAM;QACrB,OAAO;IACX;IACA,IAAI,eAAe,UAAU;QACzB,OAAO,gBAAgB,YAAY,UAAU,oBAAoB;IACrE;IACA,MAAM,gBAAgB;QAClB,OAAO,EAAE;QACT,MAAM;QACN,UAAU;QACV,cAAc;QACd,eAAe;QACf,cAAc;YACV,QAAQ;QACZ;IACJ;IACA,OAAO,GAAG,eAAe,YAAW,eAAe,GAAG,EAAE,eAAe,UAAS,gBAAgB;AACpG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/axes_constants.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/axes_constants.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    map as _map\r\n} from \"../core/utils\";\r\nexport default {\r\n    logarithmic: \"logarithmic\",\r\n    discrete: \"discrete\",\r\n    numeric: \"numeric\",\r\n    left: \"left\",\r\n    right: \"right\",\r\n    top: \"top\",\r\n    bottom: \"bottom\",\r\n    center: \"center\",\r\n    horizontal: \"horizontal\",\r\n    vertical: \"vertical\",\r\n    convertTicksToValues: function(ticks) {\r\n        return _map(ticks || [], (function(item) {\r\n            return item.value\r\n        }))\r\n    },\r\n    validateOverlappingMode: function(mode) {\r\n        return \"ignore\" === mode || \"none\" === mode ? mode : \"hide\"\r\n    },\r\n    getTicksCountInRange: function(ticks, valueKey, range) {\r\n        let i = 1;\r\n        if (ticks.length > 1) {\r\n            for (; i < ticks.length; i++) {\r\n                if (Math.abs(ticks[i].coords[valueKey] - ticks[0].coords[valueKey]) >= range) {\r\n                    break\r\n                }\r\n            }\r\n        }\r\n        return i\r\n    },\r\n    areLabelsOverlap: function(bBox1, bBox2, spacing, alignment) {\r\n        const horizontalInverted = bBox1.x > bBox2.x;\r\n        const verticalInverted = bBox1.y > bBox2.y;\r\n        let x1 = bBox1.x;\r\n        let x2 = bBox2.x;\r\n        const width1 = bBox1.width;\r\n        const width2 = bBox2.width;\r\n        if (\"left\" === alignment) {\r\n            x1 += width1 / 2;\r\n            x2 += width2 / 2\r\n        } else if (\"right\" === alignment) {\r\n            x1 -= width1 / 2;\r\n            x2 -= width2 / 2\r\n        }\r\n        const hasHorizontalOverlapping = horizontalInverted ? x2 + width2 + spacing > x1 : x1 + width1 + spacing > x2;\r\n        const hasVerticalOverlapping = verticalInverted ? bBox2.y + bBox2.height > bBox1.y : bBox1.y + bBox1.height > bBox2.y;\r\n        return hasHorizontalOverlapping && hasVerticalOverlapping\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe;IACX,aAAa;IACb,UAAU;IACV,SAAS;IACT,MAAM;IACN,OAAO;IACP,KAAK;IACL,QAAQ;IACR,QAAQ;IACR,YAAY;IACZ,UAAU;IACV,sBAAsB,SAAS,KAAK;QAChC,OAAO,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,SAAS,EAAE,EAAG,SAAS,IAAI;YACnC,OAAO,KAAK,KAAK;QACrB;IACJ;IACA,yBAAyB,SAAS,IAAI;QAClC,OAAO,aAAa,QAAQ,WAAW,OAAO,OAAO;IACzD;IACA,sBAAsB,SAAS,KAAK,EAAE,QAAQ,EAAE,KAAK;QACjD,IAAI,IAAI;QACR,IAAI,MAAM,MAAM,GAAG,GAAG;YAClB,MAAO,IAAI,MAAM,MAAM,EAAE,IAAK;gBAC1B,IAAI,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,SAAS,KAAK,OAAO;oBAC1E;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS;QACvD,MAAM,qBAAqB,MAAM,CAAC,GAAG,MAAM,CAAC;QAC5C,MAAM,mBAAmB,MAAM,CAAC,GAAG,MAAM,CAAC;QAC1C,IAAI,KAAK,MAAM,CAAC;QAChB,IAAI,KAAK,MAAM,CAAC;QAChB,MAAM,SAAS,MAAM,KAAK;QAC1B,MAAM,SAAS,MAAM,KAAK;QAC1B,IAAI,WAAW,WAAW;YACtB,MAAM,SAAS;YACf,MAAM,SAAS;QACnB,OAAO,IAAI,YAAY,WAAW;YAC9B,MAAM,SAAS;YACf,MAAM,SAAS;QACnB;QACA,MAAM,2BAA2B,qBAAqB,KAAK,SAAS,UAAU,KAAK,KAAK,SAAS,UAAU;QAC3G,MAAM,yBAAyB,mBAAmB,MAAM,CAAC,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC,GAAG,MAAM,CAAC,GAAG,MAAM,MAAM,GAAG,MAAM,CAAC;QACrH,OAAO,4BAA4B;IACvC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 408, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/tick_generator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/tick_generator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getLogExt as getLog,\r\n    getCategoriesInfo,\r\n    raiseToExt,\r\n    getLog as mathLog,\r\n    raiseTo as mathRaise\r\n} from \"../core/utils\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nimport {\r\n    isDefined,\r\n    isString\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    adjust,\r\n    sign\r\n} from \"../../core/utils/math\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nconst convertDateUnitToMilliseconds = dateUtils.convertDateUnitToMilliseconds;\r\nconst dateToMilliseconds = dateUtils.dateToMilliseconds;\r\nconst math = Math;\r\nconst mathAbs = math.abs;\r\nconst mathFloor = math.floor;\r\nconst mathCeil = math.ceil;\r\nconst mathPow = math.pow;\r\nconst NUMBER_MULTIPLIERS = [1, 2, 2.5, 5];\r\nconst LOGARITHMIC_MULTIPLIERS = [1, 2, 3, 5];\r\nconst DATETIME_MULTIPLIERS = {\r\n    millisecond: [1, 2, 5, 10, 25, 50, 100, 250, 500],\r\n    second: [1, 2, 3, 5, 10, 15, 20, 30],\r\n    minute: [1, 2, 3, 5, 10, 15, 20, 30],\r\n    hour: [1, 2, 3, 4, 6, 8, 12],\r\n    day: [1, 2],\r\n    week: [1, 2],\r\n    month: [1, 2, 3, 6]\r\n};\r\nconst DATETIME_MULTIPLIERS_WITH_BIG_WEEKEND = extend({}, DATETIME_MULTIPLIERS, {\r\n    day: [1]\r\n});\r\nconst DATETIME_MINOR_MULTIPLIERS = {\r\n    millisecond: [1, 2, 5, 10, 25, 50, 100, 250, 500],\r\n    second: [1, 2, 3, 5, 10, 15, 20, 30],\r\n    minute: [1, 2, 3, 5, 10, 15, 20, 30],\r\n    hour: [1, 2, 3, 4, 6, 8, 12],\r\n    day: [1, 2, 3, 7, 14],\r\n    month: [1, 2, 3, 6]\r\n};\r\nconst MINOR_DELIMITERS = [2, 4, 5, 8, 10];\r\nconst VISIBILITY_DELIMITER = 3;\r\nconst MINUTE = 6e4;\r\n\r\nfunction dummyGenerator(options) {\r\n    return function(data, screenDelta, tickInterval, forceTickInterval) {\r\n        let count = mathFloor(screenDelta / options.axisDivisionFactor);\r\n        count = count < 1 ? 1 : count;\r\n        const interval = screenDelta / count;\r\n        return {\r\n            ticks: interval > 0 ? Array.apply(null, new Array(count + 1)).map(((_, i) => interval * i)) : [],\r\n            tickInterval: interval\r\n        }\r\n    }\r\n}\r\n\r\nfunction discreteGenerator(options) {\r\n    return function(data, screenDelta, tickInterval, forceTickInterval) {\r\n        const categories = getCategoriesInfo(data.categories, data.min, data.max).categories;\r\n        return {\r\n            ticks: categories,\r\n            tickInterval: mathCeil(categories.length * options.axisDivisionFactor / screenDelta)\r\n        }\r\n    }\r\n}\r\nconst getValue = value => value;\r\nconst getLogValue = (base, allowNegatives, linearThreshold) => value => getLog(value, base, allowNegatives, linearThreshold);\r\nconst raiseTo = (base, allowNegatives, linearThreshold) => value => raiseToExt(value, base, allowNegatives, linearThreshold);\r\nconst mathRaiseTo = base => value => mathRaise(value, base);\r\nconst logAbsValue = base => value => 0 === value ? 0 : mathLog(mathAbs(value), base);\r\nconst correctValueByInterval = (post, round, getValue) => (value, interval) => adjust(post(round(adjust(getValue(value) / interval)) * interval));\r\n\r\nfunction correctMinValueByEndOnTick(floorFunc, ceilFunc, resolveEndOnTick, endOnTick) {\r\n    if (isDefined(endOnTick)) {\r\n        return endOnTick ? floorFunc : ceilFunc\r\n    }\r\n    return function(value, interval, businessViewInfo, forceEndOnTick) {\r\n        const floorTickValue = floorFunc(value, interval);\r\n        if (value - floorTickValue === 0 || !isDefined(businessViewInfo) || resolveEndOnTick(value, floorTickValue, interval, businessViewInfo) || forceEndOnTick) {\r\n            return floorTickValue\r\n        }\r\n        return ceilFunc(value, interval)\r\n    }\r\n}\r\n\r\nfunction resolveEndOnTick(curValue, tickValue, interval, businessViewInfo) {\r\n    const prevTickDataDiff = interval - mathAbs(tickValue - curValue);\r\n    const intervalCount = math.max(mathCeil(businessViewInfo.businessDelta / interval), 2);\r\n    const businessRatio = businessViewInfo.screenDelta / (intervalCount * interval);\r\n    const potentialTickScreenDiff = math.round(businessRatio * prevTickDataDiff);\r\n    const delimiterFactor = getLog(businessRatio * interval / businessViewInfo.axisDivisionFactor, 2) + 1;\r\n    const delimiterMultiplier = (businessViewInfo.isSpacedMargin ? 2 : 1) * delimiterFactor;\r\n    const screenDelimiter = math.round(3 * delimiterMultiplier);\r\n    return businessViewInfo.businessDelta > businessViewInfo.interval && potentialTickScreenDiff >= screenDelimiter\r\n}\r\n\r\nfunction resolveEndOnTickLog(base) {\r\n    return function(curValue, tickValue, interval, businessViewInfo) {\r\n        return resolveEndOnTick(getLog(curValue, base), getLog(tickValue, base), interval, businessViewInfo)\r\n    }\r\n}\r\n\r\nfunction resolveEndOnTickDate(curValue, tickValue, interval, businessViewInfo) {\r\n    return resolveEndOnTick(curValue.valueOf(), tickValue.valueOf(), dateToMilliseconds(interval), businessViewInfo)\r\n}\r\n\r\nfunction getBusinessDelta(data, breaks) {\r\n    let spacing = 0;\r\n    if (breaks) {\r\n        spacing = breaks.reduce(((prev, item) => prev + (item.to - item.from)), 0)\r\n    }\r\n    return mathAbs(data.max - data.min - spacing)\r\n}\r\n\r\nfunction getBusinessDeltaLog(base, allowNegatives, linearThreshold) {\r\n    const getLog = getLogValue(base, allowNegatives, linearThreshold);\r\n    return function(data, breaks) {\r\n        let spacing = 0;\r\n        if (breaks) {\r\n            spacing = breaks.reduce(((prev, item) => prev + mathAbs(getLog(item.to / item.from))), 0)\r\n        }\r\n        return mathCeil(mathAbs(getLog(data.max) - getLog(data.min)) - spacing)\r\n    }\r\n}\r\n\r\nfunction getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor, addTickCount) {\r\n    let count = screenDelta / axisDivisionFactor - (addTickCount || 0);\r\n    count = count < 1 ? 1 : count;\r\n    return businessDelta / count\r\n}\r\n\r\nfunction getMultiplierFactor(interval, factorDelta) {\r\n    return mathPow(10, mathFloor(getLog(interval, 10)) + (factorDelta || 0))\r\n}\r\n\r\nfunction calculateTickInterval(businessDelta, screenDelta, tickInterval, forceTickInterval, axisDivisionFactor, multipliers, allowDecimals, addTickCount, _, minTickInterval) {\r\n    const interval = getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor, addTickCount);\r\n    let result = 1;\r\n    const onlyIntegers = false === allowDecimals;\r\n    if (!forceTickInterval || !tickInterval) {\r\n        if (interval >= 1 || !onlyIntegers && interval > 0) {\r\n            result = adjustInterval(interval, multipliers, onlyIntegers)\r\n        }\r\n        if (!tickInterval || !forceTickInterval && tickInterval < result) {\r\n            tickInterval = result\r\n        }\r\n    }\r\n    if (!forceTickInterval && minTickInterval) {\r\n        minTickInterval = adjustInterval(minTickInterval, multipliers, onlyIntegers);\r\n        if (minTickInterval > tickInterval) {\r\n            tickInterval = minTickInterval\r\n        }\r\n    }\r\n    return tickInterval\r\n}\r\n\r\nfunction adjustInterval(interval, multipliers, onlyIntegers) {\r\n    const factor = getMultiplierFactor(interval, -1);\r\n    let result = 1;\r\n    multipliers = multipliers || NUMBER_MULTIPLIERS;\r\n    if (interval > 0) {\r\n        interval /= factor;\r\n        result = multipliers.concat(10 * multipliers[0]).map((m => 10 * m)).reduce(((r, m) => {\r\n            if (.1 === factor && onlyIntegers && 25 === m) {\r\n                return r\r\n            }\r\n            return r < interval ? m : r\r\n        }), 0);\r\n        result = adjust(result * factor, factor)\r\n    }\r\n    return result\r\n}\r\n\r\nfunction calculateMinorTickInterval(businessDelta, screenDelta, tickInterval, axisDivisionFactor) {\r\n    const interval = getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor);\r\n    return tickInterval || MINOR_DELIMITERS.reduce(((r, d) => {\r\n        const cur = businessDelta / d;\r\n        return cur >= interval ? cur : r\r\n    }), 0)\r\n}\r\n\r\nfunction getCalculateTickIntervalLog(skipCalculationLimits) {\r\n    return function(businessDelta, screenDelta, tickInterval, forceTickInterval, axisDivisionFactor, multipliers, allowDecimals, _, __, minTickInterval) {\r\n        const interval = getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor);\r\n        let result = 0;\r\n        const adjustInterval = getAdjustIntervalLog(skipCalculationLimits);\r\n        if (!forceTickInterval || !tickInterval) {\r\n            if (interval > 0) {\r\n                result = adjustInterval(interval, multipliers)\r\n            }\r\n            if (!tickInterval || !forceTickInterval && tickInterval < result) {\r\n                tickInterval = result\r\n            }\r\n        }\r\n        if (!forceTickInterval && minTickInterval) {\r\n            minTickInterval = adjustInterval(minTickInterval, multipliers);\r\n            if (minTickInterval > tickInterval) {\r\n                tickInterval = minTickInterval\r\n            }\r\n        }\r\n        return tickInterval\r\n    }\r\n}\r\n\r\nfunction getAdjustIntervalLog(skipCalculationLimits) {\r\n    return function(interval, multipliers) {\r\n        let factor = getMultiplierFactor(interval);\r\n        multipliers = multipliers || LOGARITHMIC_MULTIPLIERS;\r\n        if (!skipCalculationLimits && factor < 1) {\r\n            factor = 1\r\n        }\r\n        return multipliers.concat(10 * multipliers[0]).reduce(((r, m) => r < interval ? m * factor : r), 0)\r\n    }\r\n}\r\n\r\nfunction getDataTimeMultipliers(gapSize) {\r\n    if (gapSize && gapSize > 2) {\r\n        return DATETIME_MULTIPLIERS_WITH_BIG_WEEKEND\r\n    } else {\r\n        return DATETIME_MULTIPLIERS\r\n    }\r\n}\r\n\r\nfunction numbersReducer(interval, key) {\r\n    return function(r, m) {\r\n        if (!r && interval <= convertDateUnitToMilliseconds(key, m)) {\r\n            r = {};\r\n            r[key + \"s\"] = m\r\n        }\r\n        return r\r\n    }\r\n}\r\n\r\nfunction yearsReducer(interval, factor) {\r\n    return function(r, m) {\r\n        const years = factor * m;\r\n        if (!r && interval <= convertDateUnitToMilliseconds(\"year\", years) && 2.5 !== years) {\r\n            r = {\r\n                years: years\r\n            }\r\n        }\r\n        return r\r\n    }\r\n}\r\n\r\nfunction calculateTickIntervalDateTime(businessDelta, screenDelta, tickInterval, forceTickInterval, axisDivisionFactor, multipliers, allowDecimals, addTickCount, gapSize, minTickInterval) {\r\n    if (!forceTickInterval || !tickInterval) {\r\n        const result = adjustIntervalDateTime(getIntervalByFactor(businessDelta, screenDelta, axisDivisionFactor), multipliers, null, gapSize);\r\n        if (!tickInterval || !forceTickInterval && dateToMilliseconds(tickInterval) <= dateToMilliseconds(result)) {\r\n            tickInterval = result\r\n        }\r\n    }\r\n    if (!forceTickInterval && minTickInterval) {\r\n        minTickInterval = adjustIntervalDateTime(minTickInterval, multipliers, null, gapSize);\r\n        if (dateToMilliseconds(minTickInterval) > dateToMilliseconds(tickInterval)) {\r\n            tickInterval = minTickInterval\r\n        }\r\n    }\r\n    return tickInterval\r\n}\r\n\r\nfunction adjustIntervalDateTime(interval, multipliers, _, gapSize) {\r\n    let result;\r\n    multipliers = multipliers || getDataTimeMultipliers(gapSize);\r\n    for (const key in multipliers) {\r\n        result = multipliers[key].reduce(numbersReducer(interval, key), result);\r\n        if (result) {\r\n            break\r\n        }\r\n    }\r\n    if (!result) {\r\n        for (let factor = 1;; factor *= 10) {\r\n            result = NUMBER_MULTIPLIERS.reduce(yearsReducer(interval, factor), result);\r\n            if (result) {\r\n                break\r\n            }\r\n        }\r\n    }\r\n    return result\r\n}\r\n\r\nfunction calculateMinorTickIntervalDateTime(businessDelta, screenDelta, tickInterval, axisDivisionFactor) {\r\n    return calculateTickIntervalDateTime(businessDelta, screenDelta, tickInterval, true, axisDivisionFactor, DATETIME_MINOR_MULTIPLIERS)\r\n}\r\n\r\nfunction getTickIntervalByCustomTicks(getValue, postProcess) {\r\n    return ticks => ticks ? postProcess(mathAbs(adjust(getValue(ticks[1]) - getValue(ticks[0])))) || void 0 : void 0\r\n}\r\n\r\nfunction addInterval(value, interval, isNegative) {\r\n    return dateUtils.addInterval(value, interval, isNegative)\r\n}\r\n\r\nfunction addIntervalLog(log, raise) {\r\n    return (value, interval, isNegative) => raise(addInterval(log(value), interval, isNegative))\r\n}\r\n\r\nfunction addIntervalDate(value, interval, isNegative) {\r\n    return addInterval(value, interval, isNegative)\r\n}\r\n\r\nfunction addIntervalWithBreaks(addInterval, breaks, correctValue) {\r\n    breaks = breaks.filter((b => !b.gapSize));\r\n    return function(value, interval, isNegative) {\r\n        let breakSize;\r\n        value = addInterval(value, interval, isNegative);\r\n        if (!breaks.every((item => {\r\n                if (value >= addInterval(item.from, interval) && addInterval(value, interval) < item.to) {\r\n                    breakSize = item.to - item.from - 2 * (addInterval(item.from, interval) - item.from)\r\n                }\r\n                return !breakSize\r\n            }))) {\r\n            value = correctValue(addInterval(value, breakSize), interval)\r\n        }\r\n        return value\r\n    }\r\n}\r\n\r\nfunction calculateTicks(addInterval, correctMinValue, adjustInterval, resolveEndOnTick) {\r\n    return function(data, tickInterval, endOnTick, gaps, breaks, businessDelta, screenDelta, axisDivisionFactor, generateExtraTick) {\r\n        const correctTickValue = correctTickValueOnGapSize(addInterval, gaps);\r\n        const min = data.min;\r\n        const max = data.max;\r\n        const businessViewInfo = {\r\n            screenDelta: screenDelta,\r\n            businessDelta: businessDelta,\r\n            axisDivisionFactor: axisDivisionFactor,\r\n            isSpacedMargin: data.isSpacedMargin,\r\n            interval: tickInterval\r\n        };\r\n        let cur = correctMinValue(min, tickInterval, businessViewInfo);\r\n        const ticks = [];\r\n        if (null !== breaks && void 0 !== breaks && breaks.length) {\r\n            addInterval = addIntervalWithBreaks(addInterval, breaks, correctMinValue)\r\n        }\r\n        if (cur > max) {\r\n            cur = correctMinValue(min, adjustInterval(businessDelta / 2), businessViewInfo);\r\n            if (cur > max) {\r\n                endOnTick = true;\r\n                cur = correctMinValue(min, tickInterval, businessViewInfo, endOnTick)\r\n            }\r\n        }\r\n        cur = correctTickValue(cur);\r\n        let prev;\r\n        while (cur < max && cur !== prev || generateExtraTick && cur <= max) {\r\n            ticks.push(cur);\r\n            prev = cur;\r\n            cur = correctTickValue(addInterval(cur, tickInterval))\r\n        }\r\n        if (endOnTick || cur - max === 0 || !isDefined(endOnTick) && resolveEndOnTick(max, cur, tickInterval, businessViewInfo)) {\r\n            ticks.push(cur)\r\n        }\r\n        return ticks\r\n    }\r\n}\r\n\r\nfunction calculateMinorTicks(updateTickInterval, addInterval, correctMinValue, correctTickValue, ceil) {\r\n    return function(min, max, majorTicks, minorTickInterval, tickInterval, breaks, maxCount) {\r\n        const factor = tickInterval / minorTickInterval;\r\n        const lastMajor = majorTicks[majorTicks.length - 1];\r\n        const firstMajor = majorTicks[0];\r\n        let tickBalance = maxCount - 1;\r\n        if (null !== breaks && void 0 !== breaks && breaks.length) {\r\n            addInterval = addIntervalWithBreaks(addInterval, breaks, correctMinValue)\r\n        }\r\n        minorTickInterval = updateTickInterval(minorTickInterval, firstMajor, firstMajor, factor);\r\n        if (0 === minorTickInterval) {\r\n            return []\r\n        }\r\n        let cur = correctTickValue(correctMinValue(min, tickInterval, min), minorTickInterval);\r\n        minorTickInterval = updateTickInterval(minorTickInterval, firstMajor, cur, factor);\r\n        let ticks = [];\r\n        while (cur < firstMajor && (!tickBalance || tickBalance > 0)) {\r\n            cur >= min && ticks.push(cur);\r\n            tickBalance--;\r\n            cur = addInterval(cur, minorTickInterval)\r\n        }\r\n        const middleTicks = majorTicks.reduce(((r, tick) => {\r\n            tickBalance = maxCount - 1;\r\n            if (null === r.prevTick) {\r\n                r.prevTick = tick;\r\n                return r\r\n            }\r\n            minorTickInterval = updateTickInterval(minorTickInterval, tick, r.prevTick, factor);\r\n            let cur = correctTickValue(r.prevTick, minorTickInterval);\r\n            while (cur < tick && (!tickBalance || tickBalance > 0)) {\r\n                cur !== r.prevTick && r.minors.push(cur);\r\n                tickBalance--;\r\n                cur = addInterval(cur, minorTickInterval)\r\n            }\r\n            r.prevTick = tick;\r\n            return r\r\n        }), {\r\n            prevTick: null,\r\n            minors: []\r\n        });\r\n        ticks = ticks.concat(middleTicks.minors);\r\n        const maxValue = ceil(max, tickInterval, min);\r\n        minorTickInterval = updateTickInterval(minorTickInterval, maxValue, maxValue, factor);\r\n        cur = correctTickValue(lastMajor, minorTickInterval);\r\n        let prev;\r\n        while (cur < max && cur !== prev) {\r\n            ticks.push(cur);\r\n            prev = cur;\r\n            cur = addInterval(cur, minorTickInterval)\r\n        }\r\n        if (lastMajor - max !== 0 && cur - max === 0) {\r\n            ticks.push(cur)\r\n        }\r\n        return ticks\r\n    }\r\n}\r\n\r\nfunction filterTicks(ticks, breaks) {\r\n    if (breaks.length) {\r\n        const result = breaks.reduce(((result, b) => {\r\n            const tmpTicks = [];\r\n            let i;\r\n            for (i = result[1]; i < ticks.length; i++) {\r\n                const tickValue = ticks[i];\r\n                if (tickValue < b.from) {\r\n                    tmpTicks.push(tickValue)\r\n                }\r\n                if (tickValue >= b.to) {\r\n                    break\r\n                }\r\n            }\r\n            return [result[0].concat(tmpTicks), i]\r\n        }), [\r\n            [], 0\r\n        ]);\r\n        return result[0].concat(ticks.slice(result[1]))\r\n    }\r\n    return ticks\r\n}\r\n\r\nfunction correctTickValueOnGapSize(addInterval, breaks) {\r\n    return function(value) {\r\n        let gapSize;\r\n        if (!breaks.every((item => {\r\n                if (value >= item.from && value < item.to) {\r\n                    gapSize = item.gapSize\r\n                }\r\n                return !gapSize\r\n            }))) {\r\n            value = addInterval(value, gapSize)\r\n        }\r\n        return value\r\n    }\r\n}\r\n\r\nfunction generator(options, getBusinessDelta, calculateTickInterval, calculateMinorTickInterval, getMajorTickIntervalByCustomTicks, getMinorTickIntervalByCustomTicks, convertTickInterval, calculateTicks, calculateMinorTicks, processScaleBreaks) {\r\n    function correctUserTickInterval(tickInterval, businessDelta, limit) {\r\n        if (tickInterval && businessDelta / convertTickInterval(tickInterval) >= limit + 1) {\r\n            options.incidentOccurred(\"W2003\");\r\n            tickInterval = void 0\r\n        }\r\n        return tickInterval\r\n    }\r\n    return function(data, screenDelta, tickInterval, forceTickInterval, customTicks, minorTickInterval, minorTickCount, breaks) {\r\n        customTicks = customTicks || {};\r\n        const businessDelta = getBusinessDelta(data, breaks);\r\n        let result = function(customTicks) {\r\n            return {\r\n                tickInterval: getMajorTickIntervalByCustomTicks(customTicks.majors),\r\n                ticks: customTicks.majors || [],\r\n                minorTickInterval: getMinorTickIntervalByCustomTicks(customTicks.minors),\r\n                minorTicks: customTicks.minors || []\r\n            }\r\n        }(customTicks);\r\n        if (!isNaN(businessDelta)) {\r\n            if (0 === businessDelta && !customTicks.majors) {\r\n                result.ticks = [data.min]\r\n            } else {\r\n                result = function(ticks, data, businessDelta, screenDelta, tickInterval, forceTickInterval, customTicks, breaks) {\r\n                    if (customTicks.majors) {\r\n                        ticks.breaks = breaks;\r\n                        return ticks\r\n                    }\r\n                    const gaps = breaks.filter((b => b.gapSize));\r\n                    let majorTicks;\r\n                    tickInterval = options.skipCalculationLimits ? tickInterval : correctUserTickInterval(tickInterval, businessDelta, screenDelta);\r\n                    tickInterval = calculateTickInterval(businessDelta, screenDelta, tickInterval, forceTickInterval, options.axisDivisionFactor, options.numberMultipliers, options.allowDecimals, breaks.length, gaps[0] && gaps[0].gapSize.days, options.minTickInterval);\r\n                    if (!options.skipTickGeneration) {\r\n                        majorTicks = calculateTicks(data, tickInterval, options.endOnTick, gaps, breaks, businessDelta, screenDelta, options.axisDivisionFactor, options.generateExtraTick);\r\n                        breaks = processScaleBreaks(breaks, majorTicks, tickInterval);\r\n                        majorTicks = filterTicks(majorTicks, breaks);\r\n                        ticks.breaks = breaks;\r\n                        ticks.ticks = ticks.ticks.concat(majorTicks)\r\n                    }\r\n                    ticks.tickInterval = tickInterval;\r\n                    return ticks\r\n                }(result, data, businessDelta, screenDelta, tickInterval, forceTickInterval, customTicks, breaks || []);\r\n                if (!options.skipTickGeneration && businessDelta > 0) {\r\n                    result = function(ticks, data, businessDelta, screenDelta, minorTickInterval, minorTickCount, customTicks) {\r\n                        if (!options.calculateMinors) {\r\n                            return ticks\r\n                        }\r\n                        if (customTicks.minors) {\r\n                            return ticks\r\n                        }\r\n                        const minorBusinessDelta = convertTickInterval(ticks.tickInterval);\r\n                        const minorScreenDelta = screenDelta * minorBusinessDelta / businessDelta;\r\n                        const breaks = ticks.breaks;\r\n                        if (!minorTickInterval && minorTickCount) {\r\n                            minorTickInterval = getMinorTickIntervalByCustomTicks([minorBusinessDelta / (minorTickCount + 1), minorBusinessDelta / (minorTickCount + 1) * 2])\r\n                        } else {\r\n                            minorTickCount = void 0\r\n                        }\r\n                        minorTickInterval = correctUserTickInterval(minorTickInterval, minorBusinessDelta, minorScreenDelta);\r\n                        minorTickInterval = calculateMinorTickInterval(minorBusinessDelta, minorScreenDelta, minorTickInterval, options.minorAxisDivisionFactor);\r\n                        ticks.minorTicks = filterTicks(ticks.minorTicks.concat(calculateMinorTicks(data.min, data.max, ticks.ticks, minorTickInterval, ticks.tickInterval, breaks, minorTickCount)), breaks);\r\n                        ticks.minorTickInterval = minorTickInterval;\r\n                        return ticks\r\n                    }(result, data, businessDelta, screenDelta, minorTickInterval, minorTickCount, customTicks)\r\n                }\r\n            }\r\n        }\r\n        return result\r\n    }\r\n}\r\n\r\nfunction getBaseTick(breakValue, _ref, interval, getValue) {\r\n    let [tick, insideTick] = _ref;\r\n    if (!isDefined(tick) || mathAbs(getValue(breakValue) - getValue(tick)) / interval > .25) {\r\n        if (isDefined(insideTick) && mathAbs(getValue(insideTick) - getValue(tick)) / interval < 2) {\r\n            tick = insideTick\r\n        } else if (!isDefined(tick)) {\r\n            tick = breakValue\r\n        }\r\n    }\r\n    return tick\r\n}\r\n\r\nfunction getScaleBreaksProcessor(convertTickInterval, getValue, addCorrection) {\r\n    return function(breaks, ticks, tickInterval) {\r\n        const interval = convertTickInterval(tickInterval);\r\n        const correction = .5 * interval;\r\n        return breaks.reduce(((result, b) => {\r\n            let breakTicks = ticks.filter((tick => tick <= b.from));\r\n            const from = addCorrection(getBaseTick(b.from, [].concat(breakTicks[breakTicks.length - 1], ticks[breakTicks.length]), interval, getValue), correction);\r\n            breakTicks = ticks.filter((tick => tick >= b.to));\r\n            const to = addCorrection(getBaseTick(b.to, [].concat(breakTicks[0], ticks[ticks.length - breakTicks.length - 1]), interval, getValue), -correction);\r\n            if (getValue(to) - getValue(from) < interval && !b.gapSize) {\r\n                return result\r\n            }\r\n            if (b.gapSize) {\r\n                return result.concat([b])\r\n            }\r\n            return result.concat([{\r\n                from: from,\r\n                to: to,\r\n                cumulativeWidth: b.cumulativeWidth\r\n            }])\r\n        }), [])\r\n    }\r\n}\r\n\r\nfunction numericGenerator(options) {\r\n    const floor = correctValueByInterval(getValue, mathFloor, getValue);\r\n    const ceil = correctValueByInterval(getValue, mathCeil, getValue);\r\n    const calculateTickIntervalByCustomTicks = getTickIntervalByCustomTicks(getValue, getValue);\r\n    return generator(options, getBusinessDelta, calculateTickInterval, calculateMinorTickInterval, calculateTickIntervalByCustomTicks, calculateTickIntervalByCustomTicks, getValue, calculateTicks(addInterval, correctMinValueByEndOnTick(floor, ceil, resolveEndOnTick, options.endOnTick), adjustInterval, resolveEndOnTick), calculateMinorTicks(getValue, addInterval, floor, addInterval, getValue), getScaleBreaksProcessor(getValue, getValue, ((value, correction) => value + correction)))\r\n}\r\nconst correctValueByIntervalLog = (post, getRound, getValue) => (value, interval) => sign(value) * adjust(post(getRound(value)(adjust(getValue(value) / interval)) * interval));\r\n\r\nfunction logarithmicGenerator(options) {\r\n    const base = options.logBase;\r\n    const raise = raiseTo(base, options.allowNegatives, options.linearThreshold);\r\n    const log = getLogValue(base, options.allowNegatives, options.linearThreshold);\r\n    const absLog = logAbsValue(base);\r\n    const absRaise = mathRaiseTo(base);\r\n    const floor = correctValueByIntervalLog(absRaise, (value => value < 0 ? mathCeil : mathFloor), absLog);\r\n    const ceil = correctValueByIntervalLog(absRaise, (value => value < 0 ? mathFloor : mathCeil), absLog);\r\n    const ceilNumber = correctValueByInterval(getValue, mathCeil, getValue);\r\n    return generator(options, getBusinessDeltaLog(base, options.allowNegatives, options.linearThreshold), getCalculateTickIntervalLog(options.skipCalculationLimits), calculateMinorTickInterval, getTickIntervalByCustomTicks(log, getValue), getTickIntervalByCustomTicks(getValue, getValue), getValue, calculateTicks(addIntervalLog(log, raise), correctMinValueByEndOnTick(floor, ceil, resolveEndOnTickLog(base), options.endOnTick), getAdjustIntervalLog(options.skipCalculationLimits), resolveEndOnTickLog(base)), calculateMinorTicks(((_, tick, prevTick, factor) => Math.max(Math.abs(tick), Math.abs(prevTick)) / factor), addInterval, floor, ceilNumber, ceil), getScaleBreaksProcessor(getValue, log, ((value, correction) => raise(log(value) + correction))))\r\n}\r\n\r\nfunction dateGenerator(options) {\r\n    function floor(value, interval) {\r\n        const floorNumber = correctValueByInterval(getValue, mathFloor, getValue);\r\n        let intervalObject = isString(interval) ? dateUtils.getDateIntervalByString(interval.toLowerCase()) : interval;\r\n        const divider = dateToMilliseconds(interval);\r\n        if (intervalObject.days % 7 === 0 || interval.quarters) {\r\n            intervalObject = adjustIntervalDateTime(divider)\r\n        }\r\n        const correctDateWithUnitBeginning = v => dateUtils.correctDateWithUnitBeginning(v, intervalObject, null, options.firstDayOfWeek);\r\n        const floorAtStartDate = v => new Date(mathFloor((v.getTime() - 6e4 * v.getTimezoneOffset()) / divider) * divider + 6e4 * v.getTimezoneOffset());\r\n        value = correctDateWithUnitBeginning(value);\r\n        if (\"years\" in intervalObject) {\r\n            value.setFullYear(floorNumber(value.getFullYear(), intervalObject.years))\r\n        } else if (\"quarters\" in intervalObject) {\r\n            value = correctDateWithUnitBeginning(floorAtStartDate(value))\r\n        } else if (\"months\" in intervalObject) {\r\n            value.setMonth(floorNumber(value.getMonth(), intervalObject.months))\r\n        } else if (\"weeks\" in intervalObject || \"days\" in intervalObject) {\r\n            value = correctDateWithUnitBeginning(floorAtStartDate(value))\r\n        } else if (\"hours\" in intervalObject) {\r\n            value.setHours(floorNumber(value.getHours(), intervalObject.hours))\r\n        } else if (\"minutes\" in intervalObject) {\r\n            value.setMinutes(floorNumber(value.getMinutes(), intervalObject.minutes))\r\n        } else if (\"seconds\" in intervalObject) {\r\n            value.setSeconds(floorNumber(value.getSeconds(), intervalObject.seconds))\r\n        } else if (\"milliseconds\" in intervalObject) {\r\n            value = floorAtStartDate(value)\r\n        }\r\n        return value\r\n    }\r\n    const calculateTickIntervalByCustomTicks = getTickIntervalByCustomTicks(getValue, dateUtils.convertMillisecondsToDateUnits);\r\n    return generator(options, getBusinessDelta, calculateTickIntervalDateTime, calculateMinorTickIntervalDateTime, calculateTickIntervalByCustomTicks, calculateTickIntervalByCustomTicks, dateToMilliseconds, calculateTicks(addIntervalDate, correctMinValueByEndOnTick(floor, (function(value, interval) {\r\n        let newValue = floor(value, interval);\r\n        while (value - newValue > 0) {\r\n            newValue = addIntervalDate(newValue, interval)\r\n        }\r\n        return newValue\r\n    }), resolveEndOnTickDate, options.endOnTick), adjustIntervalDateTime, resolveEndOnTickDate), calculateMinorTicks(getValue, addIntervalDate, floor, addIntervalDate, getValue), getScaleBreaksProcessor(dateToMilliseconds, getValue, ((value, correction) => new Date(value.getTime() + correction))))\r\n}\r\nexport const tickGenerator = function(options) {\r\n    let result;\r\n    if (options.rangeIsEmpty) {\r\n        result = dummyGenerator(options)\r\n    } else if (\"discrete\" === options.axisType) {\r\n        result = discreteGenerator(options)\r\n    } else if (\"logarithmic\" === options.axisType) {\r\n        result = logarithmicGenerator(options)\r\n    } else if (\"datetime\" === options.dataType) {\r\n        result = dateGenerator(options)\r\n    } else {\r\n        result = numericGenerator(options)\r\n    }\r\n    return result\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAOA;AACA;AAAA;AAIA;AAAA;AAIA;AAAA;;;;;;AAGA,MAAM,gCAAgC,0JAAA,CAAA,UAAS,CAAC,6BAA6B;AAC7E,MAAM,qBAAqB,0JAAA,CAAA,UAAS,CAAC,kBAAkB;AACvD,MAAM,OAAO;AACb,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,YAAY,KAAK,KAAK;AAC5B,MAAM,WAAW,KAAK,IAAI;AAC1B,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,qBAAqB;IAAC;IAAG;IAAG;IAAK;CAAE;AACzC,MAAM,0BAA0B;IAAC;IAAG;IAAG;IAAG;CAAE;AAC5C,MAAM,uBAAuB;IACzB,aAAa;QAAC;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IACjD,QAAQ;QAAC;QAAG;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;KAAG;IACpC,QAAQ;QAAC;QAAG;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;KAAG;IACpC,MAAM;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;KAAG;IAC5B,KAAK;QAAC;QAAG;KAAE;IACX,MAAM;QAAC;QAAG;KAAE;IACZ,OAAO;QAAC;QAAG;QAAG;QAAG;KAAE;AACvB;AACA,MAAM,wCAAwC,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,sBAAsB;IAC3E,KAAK;QAAC;KAAE;AACZ;AACA,MAAM,6BAA6B;IAC/B,aAAa;QAAC;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;QAAK;QAAK;KAAI;IACjD,QAAQ;QAAC;QAAG;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;KAAG;IACpC,QAAQ;QAAC;QAAG;QAAG;QAAG;QAAG;QAAI;QAAI;QAAI;KAAG;IACpC,MAAM;QAAC;QAAG;QAAG;QAAG;QAAG;QAAG;QAAG;KAAG;IAC5B,KAAK;QAAC;QAAG;QAAG;QAAG;QAAG;KAAG;IACrB,OAAO;QAAC;QAAG;QAAG;QAAG;KAAE;AACvB;AACA,MAAM,mBAAmB;IAAC;IAAG;IAAG;IAAG;IAAG;CAAG;AACzC,MAAM,uBAAuB;AAC7B,MAAM,SAAS;AAEf,SAAS,eAAe,OAAO;IAC3B,OAAO,SAAS,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB;QAC9D,IAAI,QAAQ,UAAU,cAAc,QAAQ,kBAAkB;QAC9D,QAAQ,QAAQ,IAAI,IAAI;QACxB,MAAM,WAAW,cAAc;QAC/B,OAAO;YACH,OAAO,WAAW,IAAI,MAAM,KAAK,CAAC,MAAM,IAAI,MAAM,QAAQ,IAAI,GAAG,CAAE,CAAC,GAAG,IAAM,WAAW,KAAM,EAAE;YAChG,cAAc;QAClB;IACJ;AACJ;AAEA,SAAS,kBAAkB,OAAO;IAC9B,OAAO,SAAS,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB;QAC9D,MAAM,aAAa,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,UAAU,EAAE,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,UAAU;QACpF,OAAO;YACH,OAAO;YACP,cAAc,SAAS,WAAW,MAAM,GAAG,QAAQ,kBAAkB,GAAG;QAC5E;IACJ;AACJ;AACA,MAAM,WAAW,CAAA,QAAS;AAC1B,MAAM,cAAc,CAAC,MAAM,gBAAgB,kBAAoB,CAAA,QAAS,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,OAAO,MAAM,gBAAgB;AAC5G,MAAM,UAAU,CAAC,MAAM,gBAAgB,kBAAoB,CAAA,QAAS,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE,OAAO,MAAM,gBAAgB;AAC5G,MAAM,cAAc,CAAA,OAAQ,CAAA,QAAS,CAAA,GAAA,yJAAA,CAAA,UAAS,AAAD,EAAE,OAAO;AACtD,MAAM,cAAc,CAAA,OAAQ,CAAA,QAAS,MAAM,QAAQ,IAAI,CAAA,GAAA,yJAAA,CAAA,SAAO,AAAD,EAAE,QAAQ,QAAQ;AAC/E,MAAM,yBAAyB,CAAC,MAAM,OAAO,WAAa,CAAC,OAAO,WAAa,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS,aAAa;AAEvI,SAAS,2BAA2B,SAAS,EAAE,QAAQ,EAAE,gBAAgB,EAAE,SAAS;IAChF,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QACtB,OAAO,YAAY,YAAY;IACnC;IACA,OAAO,SAAS,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,cAAc;QAC7D,MAAM,iBAAiB,UAAU,OAAO;QACxC,IAAI,QAAQ,mBAAmB,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB,iBAAiB,OAAO,gBAAgB,UAAU,qBAAqB,gBAAgB;YACvJ,OAAO;QACX;QACA,OAAO,SAAS,OAAO;IAC3B;AACJ;AAEA,SAAS,iBAAiB,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB;IACrE,MAAM,mBAAmB,WAAW,QAAQ,YAAY;IACxD,MAAM,gBAAgB,KAAK,GAAG,CAAC,SAAS,iBAAiB,aAAa,GAAG,WAAW;IACpF,MAAM,gBAAgB,iBAAiB,WAAW,GAAG,CAAC,gBAAgB,QAAQ;IAC9E,MAAM,0BAA0B,KAAK,KAAK,CAAC,gBAAgB;IAC3D,MAAM,kBAAkB,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,gBAAgB,WAAW,iBAAiB,kBAAkB,EAAE,KAAK;IACpG,MAAM,sBAAsB,CAAC,iBAAiB,cAAc,GAAG,IAAI,CAAC,IAAI;IACxE,MAAM,kBAAkB,KAAK,KAAK,CAAC,IAAI;IACvC,OAAO,iBAAiB,aAAa,GAAG,iBAAiB,QAAQ,IAAI,2BAA2B;AACpG;AAEA,SAAS,oBAAoB,IAAI;IAC7B,OAAO,SAAS,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB;QAC3D,OAAO,iBAAiB,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,UAAU,OAAO,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,WAAW,OAAO,UAAU;IACvF;AACJ;AAEA,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,gBAAgB;IACzE,OAAO,iBAAiB,SAAS,OAAO,IAAI,UAAU,OAAO,IAAI,mBAAmB,WAAW;AACnG;AAEA,SAAS,iBAAiB,IAAI,EAAE,MAAM;IAClC,IAAI,UAAU;IACd,IAAI,QAAQ;QACR,UAAU,OAAO,MAAM,CAAE,CAAC,MAAM,OAAS,OAAO,CAAC,KAAK,EAAE,GAAG,KAAK,IAAI,GAAI;IAC5E;IACA,OAAO,QAAQ,KAAK,GAAG,GAAG,KAAK,GAAG,GAAG;AACzC;AAEA,SAAS,oBAAoB,IAAI,EAAE,cAAc,EAAE,eAAe;IAC9D,MAAM,SAAS,YAAY,MAAM,gBAAgB;IACjD,OAAO,SAAS,IAAI,EAAE,MAAM;QACxB,IAAI,UAAU;QACd,IAAI,QAAQ;YACR,UAAU,OAAO,MAAM,CAAE,CAAC,MAAM,OAAS,OAAO,QAAQ,OAAO,KAAK,EAAE,GAAG,KAAK,IAAI,IAAK;QAC3F;QACA,OAAO,SAAS,QAAQ,OAAO,KAAK,GAAG,IAAI,OAAO,KAAK,GAAG,KAAK;IACnE;AACJ;AAEA,SAAS,oBAAoB,aAAa,EAAE,WAAW,EAAE,kBAAkB,EAAE,YAAY;IACrF,IAAI,QAAQ,cAAc,qBAAqB,CAAC,gBAAgB,CAAC;IACjE,QAAQ,QAAQ,IAAI,IAAI;IACxB,OAAO,gBAAgB;AAC3B;AAEA,SAAS,oBAAoB,QAAQ,EAAE,WAAW;IAC9C,OAAO,QAAQ,IAAI,UAAU,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,UAAU,OAAO,CAAC,eAAe,CAAC;AAC1E;AAEA,SAAS,sBAAsB,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,CAAC,EAAE,eAAe;IACxK,MAAM,WAAW,oBAAoB,eAAe,aAAa,oBAAoB;IACrF,IAAI,SAAS;IACb,MAAM,eAAe,UAAU;IAC/B,IAAI,CAAC,qBAAqB,CAAC,cAAc;QACrC,IAAI,YAAY,KAAK,CAAC,gBAAgB,WAAW,GAAG;YAChD,SAAS,eAAe,UAAU,aAAa;QACnD;QACA,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,eAAe,QAAQ;YAC9D,eAAe;QACnB;IACJ;IACA,IAAI,CAAC,qBAAqB,iBAAiB;QACvC,kBAAkB,eAAe,iBAAiB,aAAa;QAC/D,IAAI,kBAAkB,cAAc;YAChC,eAAe;QACnB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,eAAe,QAAQ,EAAE,WAAW,EAAE,YAAY;IACvD,MAAM,SAAS,oBAAoB,UAAU,CAAC;IAC9C,IAAI,SAAS;IACb,cAAc,eAAe;IAC7B,IAAI,WAAW,GAAG;QACd,YAAY;QACZ,SAAS,YAAY,MAAM,CAAC,KAAK,WAAW,CAAC,EAAE,EAAE,GAAG,CAAE,CAAA,IAAK,KAAK,GAAI,MAAM,CAAE,CAAC,GAAG;YAC5E,IAAI,OAAO,UAAU,gBAAgB,OAAO,GAAG;gBAC3C,OAAO;YACX;YACA,OAAO,IAAI,WAAW,IAAI;QAC9B,GAAI;QACJ,SAAS,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,SAAS,QAAQ;IACrC;IACA,OAAO;AACX;AAEA,SAAS,2BAA2B,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,kBAAkB;IAC5F,MAAM,WAAW,oBAAoB,eAAe,aAAa;IACjE,OAAO,gBAAgB,iBAAiB,MAAM,CAAE,CAAC,GAAG;QAChD,MAAM,MAAM,gBAAgB;QAC5B,OAAO,OAAO,WAAW,MAAM;IACnC,GAAI;AACR;AAEA,SAAS,4BAA4B,qBAAqB;IACtD,OAAO,SAAS,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,EAAE,EAAE,EAAE,eAAe;QAC/I,MAAM,WAAW,oBAAoB,eAAe,aAAa;QACjE,IAAI,SAAS;QACb,MAAM,iBAAiB,qBAAqB;QAC5C,IAAI,CAAC,qBAAqB,CAAC,cAAc;YACrC,IAAI,WAAW,GAAG;gBACd,SAAS,eAAe,UAAU;YACtC;YACA,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,eAAe,QAAQ;gBAC9D,eAAe;YACnB;QACJ;QACA,IAAI,CAAC,qBAAqB,iBAAiB;YACvC,kBAAkB,eAAe,iBAAiB;YAClD,IAAI,kBAAkB,cAAc;gBAChC,eAAe;YACnB;QACJ;QACA,OAAO;IACX;AACJ;AAEA,SAAS,qBAAqB,qBAAqB;IAC/C,OAAO,SAAS,QAAQ,EAAE,WAAW;QACjC,IAAI,SAAS,oBAAoB;QACjC,cAAc,eAAe;QAC7B,IAAI,CAAC,yBAAyB,SAAS,GAAG;YACtC,SAAS;QACb;QACA,OAAO,YAAY,MAAM,CAAC,KAAK,WAAW,CAAC,EAAE,EAAE,MAAM,CAAE,CAAC,GAAG,IAAM,IAAI,WAAW,IAAI,SAAS,GAAI;IACrG;AACJ;AAEA,SAAS,uBAAuB,OAAO;IACnC,IAAI,WAAW,UAAU,GAAG;QACxB,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AAEA,SAAS,eAAe,QAAQ,EAAE,GAAG;IACjC,OAAO,SAAS,CAAC,EAAE,CAAC;QAChB,IAAI,CAAC,KAAK,YAAY,8BAA8B,KAAK,IAAI;YACzD,IAAI,CAAC;YACL,CAAC,CAAC,MAAM,IAAI,GAAG;QACnB;QACA,OAAO;IACX;AACJ;AAEA,SAAS,aAAa,QAAQ,EAAE,MAAM;IAClC,OAAO,SAAS,CAAC,EAAE,CAAC;QAChB,MAAM,QAAQ,SAAS;QACvB,IAAI,CAAC,KAAK,YAAY,8BAA8B,QAAQ,UAAU,QAAQ,OAAO;YACjF,IAAI;gBACA,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AAEA,SAAS,8BAA8B,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,kBAAkB,EAAE,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,EAAE,eAAe;IACtL,IAAI,CAAC,qBAAqB,CAAC,cAAc;QACrC,MAAM,SAAS,uBAAuB,oBAAoB,eAAe,aAAa,qBAAqB,aAAa,MAAM;QAC9H,IAAI,CAAC,gBAAgB,CAAC,qBAAqB,mBAAmB,iBAAiB,mBAAmB,SAAS;YACvG,eAAe;QACnB;IACJ;IACA,IAAI,CAAC,qBAAqB,iBAAiB;QACvC,kBAAkB,uBAAuB,iBAAiB,aAAa,MAAM;QAC7E,IAAI,mBAAmB,mBAAmB,mBAAmB,eAAe;YACxE,eAAe;QACnB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,uBAAuB,QAAQ,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO;IAC7D,IAAI;IACJ,cAAc,eAAe,uBAAuB;IACpD,IAAK,MAAM,OAAO,YAAa;QAC3B,SAAS,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,UAAU,MAAM;QAChE,IAAI,QAAQ;YACR;QACJ;IACJ;IACA,IAAI,CAAC,QAAQ;QACT,IAAK,IAAI,SAAS,IAAI,UAAU,GAAI;YAChC,SAAS,mBAAmB,MAAM,CAAC,aAAa,UAAU,SAAS;YACnE,IAAI,QAAQ;gBACR;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,mCAAmC,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,kBAAkB;IACpG,OAAO,8BAA8B,eAAe,aAAa,cAAc,MAAM,oBAAoB;AAC7G;AAEA,SAAS,6BAA6B,QAAQ,EAAE,WAAW;IACvD,OAAO,CAAA,QAAS,QAAQ,YAAY,QAAQ,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,SAAS,KAAK,CAAC,EAAE,IAAI,SAAS,KAAK,CAAC,EAAE,QAAQ,KAAK,IAAI,KAAK;AACnH;AAEA,SAAS,YAAY,KAAK,EAAE,QAAQ,EAAE,UAAU;IAC5C,OAAO,0JAAA,CAAA,UAAS,CAAC,WAAW,CAAC,OAAO,UAAU;AAClD;AAEA,SAAS,eAAe,GAAG,EAAE,KAAK;IAC9B,OAAO,CAAC,OAAO,UAAU,aAAe,MAAM,YAAY,IAAI,QAAQ,UAAU;AACpF;AAEA,SAAS,gBAAgB,KAAK,EAAE,QAAQ,EAAE,UAAU;IAChD,OAAO,YAAY,OAAO,UAAU;AACxC;AAEA,SAAS,sBAAsB,WAAW,EAAE,MAAM,EAAE,YAAY;IAC5D,SAAS,OAAO,MAAM,CAAE,CAAA,IAAK,CAAC,EAAE,OAAO;IACvC,OAAO,SAAS,KAAK,EAAE,QAAQ,EAAE,UAAU;QACvC,IAAI;QACJ,QAAQ,YAAY,OAAO,UAAU;QACrC,IAAI,CAAC,OAAO,KAAK,CAAE,CAAA;YACX,IAAI,SAAS,YAAY,KAAK,IAAI,EAAE,aAAa,YAAY,OAAO,YAAY,KAAK,EAAE,EAAE;gBACrF,YAAY,KAAK,EAAE,GAAG,KAAK,IAAI,GAAG,IAAI,CAAC,YAAY,KAAK,IAAI,EAAE,YAAY,KAAK,IAAI;YACvF;YACA,OAAO,CAAC;QACZ,IAAK;YACL,QAAQ,aAAa,YAAY,OAAO,YAAY;QACxD;QACA,OAAO;IACX;AACJ;AAEA,SAAS,eAAe,WAAW,EAAE,eAAe,EAAE,cAAc,EAAE,gBAAgB;IAClF,OAAO,SAAS,IAAI,EAAE,YAAY,EAAE,SAAS,EAAE,IAAI,EAAE,MAAM,EAAE,aAAa,EAAE,WAAW,EAAE,kBAAkB,EAAE,iBAAiB;QAC1H,MAAM,mBAAmB,0BAA0B,aAAa;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,mBAAmB;YACrB,aAAa;YACb,eAAe;YACf,oBAAoB;YACpB,gBAAgB,KAAK,cAAc;YACnC,UAAU;QACd;QACA,IAAI,MAAM,gBAAgB,KAAK,cAAc;QAC7C,MAAM,QAAQ,EAAE;QAChB,IAAI,SAAS,UAAU,KAAK,MAAM,UAAU,OAAO,MAAM,EAAE;YACvD,cAAc,sBAAsB,aAAa,QAAQ;QAC7D;QACA,IAAI,MAAM,KAAK;YACX,MAAM,gBAAgB,KAAK,eAAe,gBAAgB,IAAI;YAC9D,IAAI,MAAM,KAAK;gBACX,YAAY;gBACZ,MAAM,gBAAgB,KAAK,cAAc,kBAAkB;YAC/D;QACJ;QACA,MAAM,iBAAiB;QACvB,IAAI;QACJ,MAAO,MAAM,OAAO,QAAQ,QAAQ,qBAAqB,OAAO,IAAK;YACjE,MAAM,IAAI,CAAC;YACX,OAAO;YACP,MAAM,iBAAiB,YAAY,KAAK;QAC5C;QACA,IAAI,aAAa,MAAM,QAAQ,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,iBAAiB,KAAK,KAAK,cAAc,mBAAmB;YACrH,MAAM,IAAI,CAAC;QACf;QACA,OAAO;IACX;AACJ;AAEA,SAAS,oBAAoB,kBAAkB,EAAE,WAAW,EAAE,eAAe,EAAE,gBAAgB,EAAE,IAAI;IACjG,OAAO,SAAS,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,iBAAiB,EAAE,YAAY,EAAE,MAAM,EAAE,QAAQ;QACnF,MAAM,SAAS,eAAe;QAC9B,MAAM,YAAY,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QACnD,MAAM,aAAa,UAAU,CAAC,EAAE;QAChC,IAAI,cAAc,WAAW;QAC7B,IAAI,SAAS,UAAU,KAAK,MAAM,UAAU,OAAO,MAAM,EAAE;YACvD,cAAc,sBAAsB,aAAa,QAAQ;QAC7D;QACA,oBAAoB,mBAAmB,mBAAmB,YAAY,YAAY;QAClF,IAAI,MAAM,mBAAmB;YACzB,OAAO,EAAE;QACb;QACA,IAAI,MAAM,iBAAiB,gBAAgB,KAAK,cAAc,MAAM;QACpE,oBAAoB,mBAAmB,mBAAmB,YAAY,KAAK;QAC3E,IAAI,QAAQ,EAAE;QACd,MAAO,MAAM,cAAc,CAAC,CAAC,eAAe,cAAc,CAAC,EAAG;YAC1D,OAAO,OAAO,MAAM,IAAI,CAAC;YACzB;YACA,MAAM,YAAY,KAAK;QAC3B;QACA,MAAM,cAAc,WAAW,MAAM,CAAE,CAAC,GAAG;YACvC,cAAc,WAAW;YACzB,IAAI,SAAS,EAAE,QAAQ,EAAE;gBACrB,EAAE,QAAQ,GAAG;gBACb,OAAO;YACX;YACA,oBAAoB,mBAAmB,mBAAmB,MAAM,EAAE,QAAQ,EAAE;YAC5E,IAAI,MAAM,iBAAiB,EAAE,QAAQ,EAAE;YACvC,MAAO,MAAM,QAAQ,CAAC,CAAC,eAAe,cAAc,CAAC,EAAG;gBACpD,QAAQ,EAAE,QAAQ,IAAI,EAAE,MAAM,CAAC,IAAI,CAAC;gBACpC;gBACA,MAAM,YAAY,KAAK;YAC3B;YACA,EAAE,QAAQ,GAAG;YACb,OAAO;QACX,GAAI;YACA,UAAU;YACV,QAAQ,EAAE;QACd;QACA,QAAQ,MAAM,MAAM,CAAC,YAAY,MAAM;QACvC,MAAM,WAAW,KAAK,KAAK,cAAc;QACzC,oBAAoB,mBAAmB,mBAAmB,UAAU,UAAU;QAC9E,MAAM,iBAAiB,WAAW;QAClC,IAAI;QACJ,MAAO,MAAM,OAAO,QAAQ,KAAM;YAC9B,MAAM,IAAI,CAAC;YACX,OAAO;YACP,MAAM,YAAY,KAAK;QAC3B;QACA,IAAI,YAAY,QAAQ,KAAK,MAAM,QAAQ,GAAG;YAC1C,MAAM,IAAI,CAAC;QACf;QACA,OAAO;IACX;AACJ;AAEA,SAAS,YAAY,KAAK,EAAE,MAAM;IAC9B,IAAI,OAAO,MAAM,EAAE;QACf,MAAM,SAAS,OAAO,MAAM,CAAE,CAAC,QAAQ;YACnC,MAAM,WAAW,EAAE;YACnB,IAAI;YACJ,IAAK,IAAI,MAAM,CAAC,EAAE,EAAE,IAAI,MAAM,MAAM,EAAE,IAAK;gBACvC,MAAM,YAAY,KAAK,CAAC,EAAE;gBAC1B,IAAI,YAAY,EAAE,IAAI,EAAE;oBACpB,SAAS,IAAI,CAAC;gBAClB;gBACA,IAAI,aAAa,EAAE,EAAE,EAAE;oBACnB;gBACJ;YACJ;YACA,OAAO;gBAAC,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC;gBAAW;aAAE;QAC1C,GAAI;YACA,EAAE;YAAE;SACP;QACD,OAAO,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,MAAM,CAAC,EAAE;IACjD;IACA,OAAO;AACX;AAEA,SAAS,0BAA0B,WAAW,EAAE,MAAM;IAClD,OAAO,SAAS,KAAK;QACjB,IAAI;QACJ,IAAI,CAAC,OAAO,KAAK,CAAE,CAAA;YACX,IAAI,SAAS,KAAK,IAAI,IAAI,QAAQ,KAAK,EAAE,EAAE;gBACvC,UAAU,KAAK,OAAO;YAC1B;YACA,OAAO,CAAC;QACZ,IAAK;YACL,QAAQ,YAAY,OAAO;QAC/B;QACA,OAAO;IACX;AACJ;AAEA,SAAS,UAAU,OAAO,EAAE,gBAAgB,EAAE,qBAAqB,EAAE,0BAA0B,EAAE,iCAAiC,EAAE,iCAAiC,EAAE,mBAAmB,EAAE,cAAc,EAAE,mBAAmB,EAAE,kBAAkB;IAC/O,SAAS,wBAAwB,YAAY,EAAE,aAAa,EAAE,KAAK;QAC/D,IAAI,gBAAgB,gBAAgB,oBAAoB,iBAAiB,QAAQ,GAAG;YAChF,QAAQ,gBAAgB,CAAC;YACzB,eAAe,KAAK;QACxB;QACA,OAAO;IACX;IACA,OAAO,SAAS,IAAI,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,iBAAiB,EAAE,cAAc,EAAE,MAAM;QACtH,cAAc,eAAe,CAAC;QAC9B,MAAM,gBAAgB,iBAAiB,MAAM;QAC7C,IAAI,SAAS,SAAS,WAAW;YAC7B,OAAO;gBACH,cAAc,kCAAkC,YAAY,MAAM;gBAClE,OAAO,YAAY,MAAM,IAAI,EAAE;gBAC/B,mBAAmB,kCAAkC,YAAY,MAAM;gBACvE,YAAY,YAAY,MAAM,IAAI,EAAE;YACxC;QACJ,EAAE;QACF,IAAI,CAAC,MAAM,gBAAgB;YACvB,IAAI,MAAM,iBAAiB,CAAC,YAAY,MAAM,EAAE;gBAC5C,OAAO,KAAK,GAAG;oBAAC,KAAK,GAAG;iBAAC;YAC7B,OAAO;gBACH,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,YAAY,EAAE,iBAAiB,EAAE,WAAW,EAAE,MAAM;oBAC3G,IAAI,YAAY,MAAM,EAAE;wBACpB,MAAM,MAAM,GAAG;wBACf,OAAO;oBACX;oBACA,MAAM,OAAO,OAAO,MAAM,CAAE,CAAA,IAAK,EAAE,OAAO;oBAC1C,IAAI;oBACJ,eAAe,QAAQ,qBAAqB,GAAG,eAAe,wBAAwB,cAAc,eAAe;oBACnH,eAAe,sBAAsB,eAAe,aAAa,cAAc,mBAAmB,QAAQ,kBAAkB,EAAE,QAAQ,iBAAiB,EAAE,QAAQ,aAAa,EAAE,OAAO,MAAM,EAAE,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,eAAe;oBACvP,IAAI,CAAC,QAAQ,kBAAkB,EAAE;wBAC7B,aAAa,eAAe,MAAM,cAAc,QAAQ,SAAS,EAAE,MAAM,QAAQ,eAAe,aAAa,QAAQ,kBAAkB,EAAE,QAAQ,iBAAiB;wBAClK,SAAS,mBAAmB,QAAQ,YAAY;wBAChD,aAAa,YAAY,YAAY;wBACrC,MAAM,MAAM,GAAG;wBACf,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC;oBACrC;oBACA,MAAM,YAAY,GAAG;oBACrB,OAAO;gBACX,EAAE,QAAQ,MAAM,eAAe,aAAa,cAAc,mBAAmB,aAAa,UAAU,EAAE;gBACtG,IAAI,CAAC,QAAQ,kBAAkB,IAAI,gBAAgB,GAAG;oBAClD,SAAS,SAAS,KAAK,EAAE,IAAI,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAAE,cAAc,EAAE,WAAW;wBACrG,IAAI,CAAC,QAAQ,eAAe,EAAE;4BAC1B,OAAO;wBACX;wBACA,IAAI,YAAY,MAAM,EAAE;4BACpB,OAAO;wBACX;wBACA,MAAM,qBAAqB,oBAAoB,MAAM,YAAY;wBACjE,MAAM,mBAAmB,cAAc,qBAAqB;wBAC5D,MAAM,SAAS,MAAM,MAAM;wBAC3B,IAAI,CAAC,qBAAqB,gBAAgB;4BACtC,oBAAoB,kCAAkC;gCAAC,qBAAqB,CAAC,iBAAiB,CAAC;gCAAG,qBAAqB,CAAC,iBAAiB,CAAC,IAAI;6BAAE;wBACpJ,OAAO;4BACH,iBAAiB,KAAK;wBAC1B;wBACA,oBAAoB,wBAAwB,mBAAmB,oBAAoB;wBACnF,oBAAoB,2BAA2B,oBAAoB,kBAAkB,mBAAmB,QAAQ,uBAAuB;wBACvI,MAAM,UAAU,GAAG,YAAY,MAAM,UAAU,CAAC,MAAM,CAAC,oBAAoB,KAAK,GAAG,EAAE,KAAK,GAAG,EAAE,MAAM,KAAK,EAAE,mBAAmB,MAAM,YAAY,EAAE,QAAQ,kBAAkB;wBAC7K,MAAM,iBAAiB,GAAG;wBAC1B,OAAO;oBACX,EAAE,QAAQ,MAAM,eAAe,aAAa,mBAAmB,gBAAgB;gBACnF;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AAEA,SAAS,YAAY,UAAU,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;IACrD,IAAI,CAAC,MAAM,WAAW,GAAG;IACzB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,SAAS,cAAc,SAAS,SAAS,WAAW,KAAK;QACrF,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe,QAAQ,SAAS,cAAc,SAAS,SAAS,WAAW,GAAG;YACxF,OAAO;QACX,OAAO,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO;YACzB,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,SAAS,wBAAwB,mBAAmB,EAAE,QAAQ,EAAE,aAAa;IACzE,OAAO,SAAS,MAAM,EAAE,KAAK,EAAE,YAAY;QACvC,MAAM,WAAW,oBAAoB;QACrC,MAAM,aAAa,KAAK;QACxB,OAAO,OAAO,MAAM,CAAE,CAAC,QAAQ;YAC3B,IAAI,aAAa,MAAM,MAAM,CAAE,CAAA,OAAQ,QAAQ,EAAE,IAAI;YACrD,MAAM,OAAO,cAAc,YAAY,EAAE,IAAI,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,EAAE,KAAK,CAAC,WAAW,MAAM,CAAC,GAAG,UAAU,WAAW;YAC5I,aAAa,MAAM,MAAM,CAAE,CAAA,OAAQ,QAAQ,EAAE,EAAE;YAC/C,MAAM,KAAK,cAAc,YAAY,EAAE,EAAE,EAAE,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,EAAE,EAAE,KAAK,CAAC,MAAM,MAAM,GAAG,WAAW,MAAM,GAAG,EAAE,GAAG,UAAU,WAAW,CAAC;YACxI,IAAI,SAAS,MAAM,SAAS,QAAQ,YAAY,CAAC,EAAE,OAAO,EAAE;gBACxD,OAAO;YACX;YACA,IAAI,EAAE,OAAO,EAAE;gBACX,OAAO,OAAO,MAAM,CAAC;oBAAC;iBAAE;YAC5B;YACA,OAAO,OAAO,MAAM,CAAC;gBAAC;oBAClB,MAAM;oBACN,IAAI;oBACJ,iBAAiB,EAAE,eAAe;gBACtC;aAAE;QACN,GAAI,EAAE;IACV;AACJ;AAEA,SAAS,iBAAiB,OAAO;IAC7B,MAAM,QAAQ,uBAAuB,UAAU,WAAW;IAC1D,MAAM,OAAO,uBAAuB,UAAU,UAAU;IACxD,MAAM,qCAAqC,6BAA6B,UAAU;IAClF,OAAO,UAAU,SAAS,kBAAkB,uBAAuB,4BAA4B,oCAAoC,oCAAoC,UAAU,eAAe,aAAa,2BAA2B,OAAO,MAAM,kBAAkB,QAAQ,SAAS,GAAG,gBAAgB,mBAAmB,oBAAoB,UAAU,aAAa,OAAO,aAAa,WAAW,wBAAwB,UAAU,UAAW,CAAC,OAAO,aAAe,QAAQ;AACxd;AACA,MAAM,4BAA4B,CAAC,MAAM,UAAU,WAAa,CAAC,OAAO,WAAa,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,SAAS,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,KAAK,SAAS,OAAO,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,SAAS,SAAS,aAAa;AAErK,SAAS,qBAAqB,OAAO;IACjC,MAAM,OAAO,QAAQ,OAAO;IAC5B,MAAM,QAAQ,QAAQ,MAAM,QAAQ,cAAc,EAAE,QAAQ,eAAe;IAC3E,MAAM,MAAM,YAAY,MAAM,QAAQ,cAAc,EAAE,QAAQ,eAAe;IAC7E,MAAM,SAAS,YAAY;IAC3B,MAAM,WAAW,YAAY;IAC7B,MAAM,QAAQ,0BAA0B,UAAW,CAAA,QAAS,QAAQ,IAAI,WAAW,WAAY;IAC/F,MAAM,OAAO,0BAA0B,UAAW,CAAA,QAAS,QAAQ,IAAI,YAAY,UAAW;IAC9F,MAAM,aAAa,uBAAuB,UAAU,UAAU;IAC9D,OAAO,UAAU,SAAS,oBAAoB,MAAM,QAAQ,cAAc,EAAE,QAAQ,eAAe,GAAG,4BAA4B,QAAQ,qBAAqB,GAAG,4BAA4B,6BAA6B,KAAK,WAAW,6BAA6B,UAAU,WAAW,UAAU,eAAe,eAAe,KAAK,QAAQ,2BAA2B,OAAO,MAAM,oBAAoB,OAAO,QAAQ,SAAS,GAAG,qBAAqB,QAAQ,qBAAqB,GAAG,oBAAoB,QAAQ,oBAAqB,CAAC,GAAG,MAAM,UAAU,SAAW,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,aAAa,QAAS,aAAa,OAAO,YAAY,OAAO,wBAAwB,UAAU,KAAM,CAAC,OAAO,aAAe,MAAM,IAAI,SAAS;AACnuB;AAEA,SAAS,cAAc,OAAO;IAC1B,SAAS,MAAM,KAAK,EAAE,QAAQ;QAC1B,MAAM,cAAc,uBAAuB,UAAU,WAAW;QAChE,IAAI,iBAAiB,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,0JAAA,CAAA,UAAS,CAAC,uBAAuB,CAAC,SAAS,WAAW,MAAM;QACtG,MAAM,UAAU,mBAAmB;QACnC,IAAI,eAAe,IAAI,GAAG,MAAM,KAAK,SAAS,QAAQ,EAAE;YACpD,iBAAiB,uBAAuB;QAC5C;QACA,MAAM,+BAA+B,CAAA,IAAK,0JAAA,CAAA,UAAS,CAAC,4BAA4B,CAAC,GAAG,gBAAgB,MAAM,QAAQ,cAAc;QAChI,MAAM,mBAAmB,CAAA,IAAK,IAAI,KAAK,UAAU,CAAC,EAAE,OAAO,KAAK,MAAM,EAAE,iBAAiB,EAAE,IAAI,WAAW,UAAU,MAAM,EAAE,iBAAiB;QAC7I,QAAQ,6BAA6B;QACrC,IAAI,WAAW,gBAAgB;YAC3B,MAAM,WAAW,CAAC,YAAY,MAAM,WAAW,IAAI,eAAe,KAAK;QAC3E,OAAO,IAAI,cAAc,gBAAgB;YACrC,QAAQ,6BAA6B,iBAAiB;QAC1D,OAAO,IAAI,YAAY,gBAAgB;YACnC,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,IAAI,eAAe,MAAM;QACtE,OAAO,IAAI,WAAW,kBAAkB,UAAU,gBAAgB;YAC9D,QAAQ,6BAA6B,iBAAiB;QAC1D,OAAO,IAAI,WAAW,gBAAgB;YAClC,MAAM,QAAQ,CAAC,YAAY,MAAM,QAAQ,IAAI,eAAe,KAAK;QACrE,OAAO,IAAI,aAAa,gBAAgB;YACpC,MAAM,UAAU,CAAC,YAAY,MAAM,UAAU,IAAI,eAAe,OAAO;QAC3E,OAAO,IAAI,aAAa,gBAAgB;YACpC,MAAM,UAAU,CAAC,YAAY,MAAM,UAAU,IAAI,eAAe,OAAO;QAC3E,OAAO,IAAI,kBAAkB,gBAAgB;YACzC,QAAQ,iBAAiB;QAC7B;QACA,OAAO;IACX;IACA,MAAM,qCAAqC,6BAA6B,UAAU,0JAAA,CAAA,UAAS,CAAC,8BAA8B;IAC1H,OAAO,UAAU,SAAS,kBAAkB,+BAA+B,oCAAoC,oCAAoC,oCAAoC,oBAAoB,eAAe,iBAAiB,2BAA2B,OAAQ,SAAS,KAAK,EAAE,QAAQ;QAClS,IAAI,WAAW,MAAM,OAAO;QAC5B,MAAO,QAAQ,WAAW,EAAG;YACzB,WAAW,gBAAgB,UAAU;QACzC;QACA,OAAO;IACX,GAAI,sBAAsB,QAAQ,SAAS,GAAG,wBAAwB,uBAAuB,oBAAoB,UAAU,iBAAiB,OAAO,iBAAiB,WAAW,wBAAwB,oBAAoB,UAAW,CAAC,OAAO,aAAe,IAAI,KAAK,MAAM,OAAO,KAAK;AAC5R;AACO,MAAM,gBAAgB,SAAS,OAAO;IACzC,IAAI;IACJ,IAAI,QAAQ,YAAY,EAAE;QACtB,SAAS,eAAe;IAC5B,OAAO,IAAI,eAAe,QAAQ,QAAQ,EAAE;QACxC,SAAS,kBAAkB;IAC/B,OAAO,IAAI,kBAAkB,QAAQ,QAAQ,EAAE;QAC3C,SAAS,qBAAqB;IAClC,OAAO,IAAI,eAAe,QAAQ,QAAQ,EAAE;QACxC,SAAS,cAAc;IAC3B,OAAO;QACH,SAAS,iBAAiB;IAC9B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1145, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/tick.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/tick.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../core/utils/deferred\";\r\n\r\nfunction getPathStyle(options) {\r\n    return {\r\n        stroke: options.color,\r\n        \"stroke-width\": options.width,\r\n        \"stroke-opacity\": options.opacity,\r\n        opacity: 1\r\n    }\r\n}\r\n\r\nfunction createTick(axis, renderer, tickOptions, gridOptions, skippedCategory, skipLabels, offset) {\r\n    const tickOffset = offset || axis._tickOffset;\r\n    const lineGroup = axis._axisLineGroup;\r\n    const elementsGroup = axis._axisElementsGroup;\r\n    const tickStyle = getPathStyle(tickOptions);\r\n    const gridStyle = getPathStyle(gridOptions);\r\n    const emptyStrRegExp = /^\\s+$/;\r\n    const axisOptions = axis.getOptions();\r\n    const labelOptions = axisOptions.label;\r\n    const labelStyle = axis._textOptions;\r\n\r\n    function getLabelFontStyle(tick) {\r\n        let fontStyle = axis._textFontStyles;\r\n        const customizeColor = labelOptions.customizeColor;\r\n        if (customizeColor && customizeColor.call) {\r\n            fontStyle = extend({}, axis._textFontStyles, {\r\n                fill: customizeColor.call(tick, tick)\r\n            })\r\n        }\r\n        return fontStyle\r\n    }\r\n\r\n    function createLabelHint(tick, range) {\r\n        const labelHint = axis.formatHint(tick.value, labelOptions, range);\r\n        if (isDefined(labelHint) && \"\" !== labelHint) {\r\n            tick.getContentContainer().setTitle(labelHint)\r\n        }\r\n    }\r\n    return function(value) {\r\n        const tick = {\r\n            value: value,\r\n            updateValue(newValue) {\r\n                this.value = value = newValue\r\n            },\r\n            initCoords: function() {\r\n                this.coords = axis._getTranslatedValue(value, tickOffset);\r\n                this.labelCoords = axis._getTranslatedValue(value)\r\n            },\r\n            saveCoords() {\r\n                when(this._templateDef).done((() => {\r\n                    this._lastStoredCoordinates = {\r\n                        coords: this._storedCoords,\r\n                        labelCoords: this._storedLabelsCoords\r\n                    };\r\n                    this._storedCoords = this.coords;\r\n                    this._storedLabelsCoords = this.templateContainer ? this._getTemplateCoords() : this.labelCoords\r\n                }))\r\n            },\r\n            resetCoordinates() {\r\n                if (this._lastStoredCoordinates) {\r\n                    this._storedCoords = this._lastStoredCoordinates.coords;\r\n                    this._storedLabelsCoords = this._lastStoredCoordinates.labelCoords\r\n                }\r\n            },\r\n            drawMark(options) {\r\n                if (!tickOptions.visible || skippedCategory === value) {\r\n                    return\r\n                }\r\n                if (axis.areCoordsOutsideAxis(this.coords)) {\r\n                    return\r\n                }\r\n                if (this.mark) {\r\n                    this.mark.append(lineGroup);\r\n                    axis.sharp(this.mark, axis.getSharpDirectionByCoords(this.coords));\r\n                    this.updateTickPosition(options)\r\n                } else {\r\n                    this.mark = axis._createPathElement([], tickStyle, axis.getSharpDirectionByCoords(this.coords)).append(lineGroup);\r\n                    this.updateTickPosition(options)\r\n                }\r\n            },\r\n            setSkippedCategory(category) {\r\n                skippedCategory = category\r\n            },\r\n            _updateLine(lineElement, settings, storedSettings, animate, isGridLine) {\r\n                if (!lineElement) {\r\n                    return\r\n                }\r\n                if (null === settings.points || null === settings.r) {\r\n                    lineElement.remove();\r\n                    return\r\n                }\r\n                if (animate && storedSettings && null !== storedSettings.points) {\r\n                    settings.opacity = 1;\r\n                    lineElement.attr(storedSettings);\r\n                    lineElement.animate(settings)\r\n                } else {\r\n                    settings.opacity = animate ? 0 : 1;\r\n                    lineElement.attr(settings);\r\n                    animate && lineElement.animate({\r\n                        opacity: 1\r\n                    }, {\r\n                        delay: .5,\r\n                        partitionDuration: .5\r\n                    })\r\n                }\r\n                this.coords.angle && axis._rotateTick(lineElement, this.coords, isGridLine)\r\n            },\r\n            updateTickPosition: function(options, animate) {\r\n                this._updateLine(this.mark, {\r\n                    points: axis._getTickMarkPoints(tick.coords, tickOptions.length, options)\r\n                }, this._storedCoords && {\r\n                    points: axis._getTickMarkPoints(tick._storedCoords, tickOptions.length, options)\r\n                }, animate, false)\r\n            },\r\n            drawLabel: function(range, template) {\r\n                if (this.templateContainer && axis.isRendered()) {\r\n                    this.updateLabelPosition();\r\n                    return\r\n                }\r\n                const labelIsVisible = labelOptions.visible && !skipLabels && !axis.getTranslator().getBusinessRange().isEmpty() && !axis.areCoordsOutsideAxis(this.labelCoords);\r\n                if (!labelIsVisible) {\r\n                    if (this.label) {\r\n                        this.removeLabel()\r\n                    }\r\n                    return\r\n                }\r\n                const templateOption = labelOptions.template;\r\n                const text = axis.formatLabel(value, labelOptions, range);\r\n                if (this.label) {\r\n                    this.label.attr({\r\n                        text: text,\r\n                        rotate: 0\r\n                    }).append(elementsGroup);\r\n                    createLabelHint(this, range);\r\n                    this.updateLabelPosition();\r\n                    return\r\n                }\r\n                if (templateOption) {\r\n                    this.templateContainer = renderer.g().append(elementsGroup);\r\n                    this._templateDef && this._templateDef.reject();\r\n                    this._templateDef = new Deferred;\r\n                    template.render({\r\n                        model: {\r\n                            valueText: text,\r\n                            value: this.value,\r\n                            labelFontStyle: getLabelFontStyle(this),\r\n                            labelStyle: labelStyle\r\n                        },\r\n                        container: this.templateContainer.element,\r\n                        onRendered: () => {\r\n                            this.updateLabelPosition();\r\n                            this._templateDef && this._templateDef.resolve()\r\n                        }\r\n                    })\r\n                } else if (isDefined(text) && \"\" !== text && !emptyStrRegExp.test(text)) {\r\n                    this.label = renderer.text(text).css(getLabelFontStyle(this)).attr(labelStyle).append(elementsGroup);\r\n                    this.updateLabelPosition();\r\n                    createLabelHint(this, range)\r\n                }\r\n                const containerForData = this.getContentContainer();\r\n                containerForData && containerForData.data(\"chart-data-argument\", this.value);\r\n                this.templateContainer && createLabelHint(this, range)\r\n            },\r\n            getTemplateDeferred() {\r\n                return this._templateDef\r\n            },\r\n            getContentContainer() {\r\n                return this.templateContainer || this.label\r\n            },\r\n            fadeOutElements() {\r\n                const startSettings = {\r\n                    opacity: 1\r\n                };\r\n                const endSettings = {\r\n                    opacity: 0\r\n                };\r\n                const animationSettings = {\r\n                    partitionDuration: .5\r\n                };\r\n                if (this.getContentContainer()) {\r\n                    this._fadeOutLabel()\r\n                }\r\n                if (this.grid) {\r\n                    this.grid.append(axis._axisGridGroup).attr(startSettings).animate(endSettings, animationSettings)\r\n                }\r\n                if (this.mark) {\r\n                    this.mark.append(axis._axisLineGroup).attr(startSettings).animate(endSettings, animationSettings)\r\n                }\r\n            },\r\n            _fadeInLabel() {\r\n                const group = axis._renderer.g().attr({\r\n                    opacity: 0\r\n                }).append(axis._axisElementsGroup).animate({\r\n                    opacity: 1\r\n                }, {\r\n                    delay: .5,\r\n                    partitionDuration: .5\r\n                });\r\n                this.getContentContainer().append(group)\r\n            },\r\n            _fadeOutLabel() {\r\n                const group = axis._renderer.g().attr({\r\n                    opacity: 1\r\n                }).animate({\r\n                    opacity: 0\r\n                }, {\r\n                    partitionDuration: .5\r\n                }).append(axis._axisElementsGroup).toBackground();\r\n                this.getContentContainer().append(group)\r\n            },\r\n            _getTemplateCoords() {\r\n                return axis._getLabelAdjustedCoord(this, (axis._constantLabelOffset || 0) + (tick.labelOffset || 0))\r\n            },\r\n            updateLabelPosition: function(animate) {\r\n                const templateContainer = this.templateContainer;\r\n                if (!this.getContentContainer()) {\r\n                    return\r\n                }\r\n                if (animate && this._storedLabelsCoords) {\r\n                    if (templateContainer) {\r\n                        templateContainer.attr(this._storedLabelsCoords);\r\n                        const lCoords = this._getTemplateCoords();\r\n                        templateContainer.animate(lCoords)\r\n                    } else {\r\n                        this.label.attr({\r\n                            x: this._storedLabelsCoords.x,\r\n                            y: this._storedLabelsCoords.y\r\n                        });\r\n                        this.label.animate({\r\n                            x: this.labelCoords.x,\r\n                            y: this.labelCoords.y\r\n                        })\r\n                    }\r\n                } else {\r\n                    if (templateContainer) {\r\n                        const lCoords = this._getTemplateCoords();\r\n                        templateContainer.attr(lCoords)\r\n                    } else {\r\n                        this.label.attr({\r\n                            x: this.labelCoords.x,\r\n                            y: this.labelCoords.y\r\n                        })\r\n                    }\r\n                    if (animate) {\r\n                        this._fadeInLabel()\r\n                    }\r\n                }\r\n            },\r\n            updateMultilineTextAlignment() {\r\n                if (labelOptions.template || !this.label) {\r\n                    return\r\n                }\r\n                this.label.attr({\r\n                    textsAlignment: this.labelAlignment || axis.getOptions().label.alignment\r\n                })\r\n            },\r\n            drawGrid: function(drawLine) {\r\n                if (gridOptions.visible && skippedCategory !== this.value) {\r\n                    if (this.grid) {\r\n                        this.grid.append(axis._axisGridGroup);\r\n                        axis.sharp(this.grid, axis.getSharpDirectionByCoords(this.coords));\r\n                        this.updateGridPosition()\r\n                    } else {\r\n                        this.grid = drawLine(this, gridStyle);\r\n                        this.grid && this.grid.append(axis._axisGridGroup)\r\n                    }\r\n                }\r\n            },\r\n            updateGridPosition: function(animate) {\r\n                this._updateLine(this.grid, axis._getGridPoints(tick.coords), this._storedCoords && axis._getGridPoints(this._storedCoords), animate, true)\r\n            },\r\n            removeLabel() {\r\n                const contentContainer = this.getContentContainer();\r\n                contentContainer && contentContainer.remove();\r\n                this._templateDef && this._templateDef.reject();\r\n                this._templateDef = this.templateContainer = this.label = null\r\n            }\r\n        };\r\n        return tick\r\n    }\r\n}\r\nexport {\r\n    createTick as tick\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AAGA;AAAA;;;;AAKA,SAAS,aAAa,OAAO;IACzB,OAAO;QACH,QAAQ,QAAQ,KAAK;QACrB,gBAAgB,QAAQ,KAAK;QAC7B,kBAAkB,QAAQ,OAAO;QACjC,SAAS;IACb;AACJ;AAEA,SAAS,WAAW,IAAI,EAAE,QAAQ,EAAE,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,UAAU,EAAE,MAAM;IAC7F,MAAM,aAAa,UAAU,KAAK,WAAW;IAC7C,MAAM,YAAY,KAAK,cAAc;IACrC,MAAM,gBAAgB,KAAK,kBAAkB;IAC7C,MAAM,YAAY,aAAa;IAC/B,MAAM,YAAY,aAAa;IAC/B,MAAM,iBAAiB;IACvB,MAAM,cAAc,KAAK,UAAU;IACnC,MAAM,eAAe,YAAY,KAAK;IACtC,MAAM,aAAa,KAAK,YAAY;IAEpC,SAAS,kBAAkB,IAAI;QAC3B,IAAI,YAAY,KAAK,eAAe;QACpC,MAAM,iBAAiB,aAAa,cAAc;QAClD,IAAI,kBAAkB,eAAe,IAAI,EAAE;YACvC,YAAY,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,KAAK,eAAe,EAAE;gBACzC,MAAM,eAAe,IAAI,CAAC,MAAM;YACpC;QACJ;QACA,OAAO;IACX;IAEA,SAAS,gBAAgB,IAAI,EAAE,KAAK;QAChC,MAAM,YAAY,KAAK,UAAU,CAAC,KAAK,KAAK,EAAE,cAAc;QAC5D,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,OAAO,WAAW;YAC1C,KAAK,mBAAmB,GAAG,QAAQ,CAAC;QACxC;IACJ;IACA,OAAO,SAAS,KAAK;QACjB,MAAM,OAAO;YACT,OAAO;YACP,aAAY,QAAQ;gBAChB,IAAI,CAAC,KAAK,GAAG,QAAQ;YACzB;YACA,YAAY;gBACR,IAAI,CAAC,MAAM,GAAG,KAAK,mBAAmB,CAAC,OAAO;gBAC9C,IAAI,CAAC,WAAW,GAAG,KAAK,mBAAmB,CAAC;YAChD;YACA;gBACI,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,CAAE;oBAC1B,IAAI,CAAC,sBAAsB,GAAG;wBAC1B,QAAQ,IAAI,CAAC,aAAa;wBAC1B,aAAa,IAAI,CAAC,mBAAmB;oBACzC;oBACA,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,MAAM;oBAChC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,WAAW;gBACpG;YACJ;YACA;gBACI,IAAI,IAAI,CAAC,sBAAsB,EAAE;oBAC7B,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,sBAAsB,CAAC,MAAM;oBACvD,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,sBAAsB,CAAC,WAAW;gBACtE;YACJ;YACA,UAAS,OAAO;gBACZ,IAAI,CAAC,YAAY,OAAO,IAAI,oBAAoB,OAAO;oBACnD;gBACJ;gBACA,IAAI,KAAK,oBAAoB,CAAC,IAAI,CAAC,MAAM,GAAG;oBACxC;gBACJ;gBACA,IAAI,IAAI,CAAC,IAAI,EAAE;oBACX,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;oBACjB,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,yBAAyB,CAAC,IAAI,CAAC,MAAM;oBAChE,IAAI,CAAC,kBAAkB,CAAC;gBAC5B,OAAO;oBACH,IAAI,CAAC,IAAI,GAAG,KAAK,kBAAkB,CAAC,EAAE,EAAE,WAAW,KAAK,yBAAyB,CAAC,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;oBACvG,IAAI,CAAC,kBAAkB,CAAC;gBAC5B;YACJ;YACA,oBAAmB,QAAQ;gBACvB,kBAAkB;YACtB;YACA,aAAY,WAAW,EAAE,QAAQ,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU;gBAClE,IAAI,CAAC,aAAa;oBACd;gBACJ;gBACA,IAAI,SAAS,SAAS,MAAM,IAAI,SAAS,SAAS,CAAC,EAAE;oBACjD,YAAY,MAAM;oBAClB;gBACJ;gBACA,IAAI,WAAW,kBAAkB,SAAS,eAAe,MAAM,EAAE;oBAC7D,SAAS,OAAO,GAAG;oBACnB,YAAY,IAAI,CAAC;oBACjB,YAAY,OAAO,CAAC;gBACxB,OAAO;oBACH,SAAS,OAAO,GAAG,UAAU,IAAI;oBACjC,YAAY,IAAI,CAAC;oBACjB,WAAW,YAAY,OAAO,CAAC;wBAC3B,SAAS;oBACb,GAAG;wBACC,OAAO;wBACP,mBAAmB;oBACvB;gBACJ;gBACA,IAAI,CAAC,MAAM,CAAC,KAAK,IAAI,KAAK,WAAW,CAAC,aAAa,IAAI,CAAC,MAAM,EAAE;YACpE;YACA,oBAAoB,SAAS,OAAO,EAAE,OAAO;gBACzC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE;oBACxB,QAAQ,KAAK,kBAAkB,CAAC,KAAK,MAAM,EAAE,YAAY,MAAM,EAAE;gBACrE,GAAG,IAAI,CAAC,aAAa,IAAI;oBACrB,QAAQ,KAAK,kBAAkB,CAAC,KAAK,aAAa,EAAE,YAAY,MAAM,EAAE;gBAC5E,GAAG,SAAS;YAChB;YACA,WAAW,SAAS,KAAK,EAAE,QAAQ;gBAC/B,IAAI,IAAI,CAAC,iBAAiB,IAAI,KAAK,UAAU,IAAI;oBAC7C,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;gBACA,MAAM,iBAAiB,aAAa,OAAO,IAAI,CAAC,cAAc,CAAC,KAAK,aAAa,GAAG,gBAAgB,GAAG,OAAO,MAAM,CAAC,KAAK,oBAAoB,CAAC,IAAI,CAAC,WAAW;gBAC/J,IAAI,CAAC,gBAAgB;oBACjB,IAAI,IAAI,CAAC,KAAK,EAAE;wBACZ,IAAI,CAAC,WAAW;oBACpB;oBACA;gBACJ;gBACA,MAAM,iBAAiB,aAAa,QAAQ;gBAC5C,MAAM,OAAO,KAAK,WAAW,CAAC,OAAO,cAAc;gBACnD,IAAI,IAAI,CAAC,KAAK,EAAE;oBACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;wBACZ,MAAM;wBACN,QAAQ;oBACZ,GAAG,MAAM,CAAC;oBACV,gBAAgB,IAAI,EAAE;oBACtB,IAAI,CAAC,mBAAmB;oBACxB;gBACJ;gBACA,IAAI,gBAAgB;oBAChB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC;oBAC7C,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM;oBAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,iLAAA,CAAA,WAAQ;oBAChC,SAAS,MAAM,CAAC;wBACZ,OAAO;4BACH,WAAW;4BACX,OAAO,IAAI,CAAC,KAAK;4BACjB,gBAAgB,kBAAkB,IAAI;4BACtC,YAAY;wBAChB;wBACA,WAAW,IAAI,CAAC,iBAAiB,CAAC,OAAO;wBACzC,YAAY;4BACR,IAAI,CAAC,mBAAmB;4BACxB,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO;wBAClD;oBACJ;gBACJ,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,OAAO,QAAQ,CAAC,eAAe,IAAI,CAAC,OAAO;oBACrE,IAAI,CAAC,KAAK,GAAG,SAAS,IAAI,CAAC,MAAM,GAAG,CAAC,kBAAkB,IAAI,GAAG,IAAI,CAAC,YAAY,MAAM,CAAC;oBACtF,IAAI,CAAC,mBAAmB;oBACxB,gBAAgB,IAAI,EAAE;gBAC1B;gBACA,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;gBACjD,oBAAoB,iBAAiB,IAAI,CAAC,uBAAuB,IAAI,CAAC,KAAK;gBAC3E,IAAI,CAAC,iBAAiB,IAAI,gBAAgB,IAAI,EAAE;YACpD;YACA;gBACI,OAAO,IAAI,CAAC,YAAY;YAC5B;YACA;gBACI,OAAO,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,KAAK;YAC/C;YACA;gBACI,MAAM,gBAAgB;oBAClB,SAAS;gBACb;gBACA,MAAM,cAAc;oBAChB,SAAS;gBACb;gBACA,MAAM,oBAAoB;oBACtB,mBAAmB;gBACvB;gBACA,IAAI,IAAI,CAAC,mBAAmB,IAAI;oBAC5B,IAAI,CAAC,aAAa;gBACtB;gBACA,IAAI,IAAI,CAAC,IAAI,EAAE;oBACX,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,cAAc,EAAE,IAAI,CAAC,eAAe,OAAO,CAAC,aAAa;gBACnF;gBACA,IAAI,IAAI,CAAC,IAAI,EAAE;oBACX,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,cAAc,EAAE,IAAI,CAAC,eAAe,OAAO,CAAC,aAAa;gBACnF;YACJ;YACA;gBACI,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;oBAClC,SAAS;gBACb,GAAG,MAAM,CAAC,KAAK,kBAAkB,EAAE,OAAO,CAAC;oBACvC,SAAS;gBACb,GAAG;oBACC,OAAO;oBACP,mBAAmB;gBACvB;gBACA,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC;YACtC;YACA;gBACI,MAAM,QAAQ,KAAK,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;oBAClC,SAAS;gBACb,GAAG,OAAO,CAAC;oBACP,SAAS;gBACb,GAAG;oBACC,mBAAmB;gBACvB,GAAG,MAAM,CAAC,KAAK,kBAAkB,EAAE,YAAY;gBAC/C,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC;YACtC;YACA;gBACI,OAAO,KAAK,sBAAsB,CAAC,IAAI,EAAE,CAAC,KAAK,oBAAoB,IAAI,CAAC,IAAI,CAAC,KAAK,WAAW,IAAI,CAAC;YACtG;YACA,qBAAqB,SAAS,OAAO;gBACjC,MAAM,oBAAoB,IAAI,CAAC,iBAAiB;gBAChD,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI;oBAC7B;gBACJ;gBACA,IAAI,WAAW,IAAI,CAAC,mBAAmB,EAAE;oBACrC,IAAI,mBAAmB;wBACnB,kBAAkB,IAAI,CAAC,IAAI,CAAC,mBAAmB;wBAC/C,MAAM,UAAU,IAAI,CAAC,kBAAkB;wBACvC,kBAAkB,OAAO,CAAC;oBAC9B,OAAO;wBACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;4BACZ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;4BAC7B,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC;wBACjC;wBACA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC;4BACf,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;4BACrB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;wBACzB;oBACJ;gBACJ,OAAO;oBACH,IAAI,mBAAmB;wBACnB,MAAM,UAAU,IAAI,CAAC,kBAAkB;wBACvC,kBAAkB,IAAI,CAAC;oBAC3B,OAAO;wBACH,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;4BACZ,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;4BACrB,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;wBACzB;oBACJ;oBACA,IAAI,SAAS;wBACT,IAAI,CAAC,YAAY;oBACrB;gBACJ;YACJ;YACA;gBACI,IAAI,aAAa,QAAQ,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;oBACtC;gBACJ;gBACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;oBACZ,gBAAgB,IAAI,CAAC,cAAc,IAAI,KAAK,UAAU,GAAG,KAAK,CAAC,SAAS;gBAC5E;YACJ;YACA,UAAU,SAAS,QAAQ;gBACvB,IAAI,YAAY,OAAO,IAAI,oBAAoB,IAAI,CAAC,KAAK,EAAE;oBACvD,IAAI,IAAI,CAAC,IAAI,EAAE;wBACX,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,cAAc;wBACpC,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,yBAAyB,CAAC,IAAI,CAAC,MAAM;wBAChE,IAAI,CAAC,kBAAkB;oBAC3B,OAAO;wBACH,IAAI,CAAC,IAAI,GAAG,SAAS,IAAI,EAAE;wBAC3B,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,KAAK,cAAc;oBACrD;gBACJ;YACJ;YACA,oBAAoB,SAAS,OAAO;gBAChC,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,cAAc,CAAC,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,IAAI,KAAK,cAAc,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS;YAC1I;YACA;gBACI,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;gBACjD,oBAAoB,iBAAiB,MAAM;gBAC3C,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,MAAM;gBAC7C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,KAAK,GAAG;YAC9D;QACJ;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1446, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/datetime_breaks.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/datetime_breaks.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport dateUtils from \"../../core/utils/date\";\r\nconst days = [0, 1, 2, 3, 4, 5, 6];\r\n\r\nfunction getWeekendDays(workdays) {\r\n    return days.filter((function(day) {\r\n        return !workdays.some((function(workDay) {\r\n            return workDay === day\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction getNextDayIndex(dayIndex) {\r\n    return (dayIndex + 1) % 7\r\n}\r\n\r\nfunction dayBetweenWeekend(weekend, day) {\r\n    let start = weekend.start;\r\n    const end = weekend.end;\r\n    while (start !== end) {\r\n        if (start === day) {\r\n            return true\r\n        }\r\n        start = getNextDayIndex(start)\r\n    }\r\n    return false\r\n}\r\n\r\nfunction getDaysDistance(day, end) {\r\n    let length = 0;\r\n    while (day !== end) {\r\n        day = getNextDayIndex(day);\r\n        length++\r\n    }\r\n    return length\r\n}\r\n\r\nfunction separateBreak(scaleBreak, day) {\r\n    const result = [];\r\n    const dayEnd = new Date(day);\r\n    dayEnd.setDate(day.getDate() + 1);\r\n    if (day > scaleBreak.from) {\r\n        result.push({\r\n            from: scaleBreak.from,\r\n            to: day\r\n        })\r\n    }\r\n    if (dayEnd < scaleBreak.to) {\r\n        result.push({\r\n            from: dayEnd,\r\n            to: scaleBreak.to\r\n        })\r\n    }\r\n    return result\r\n}\r\n\r\nfunction getWeekEndDayIndices(workDays) {\r\n    const indices = getWeekendDays(workDays);\r\n    if (indices.length < 7) {\r\n        while (getNextDayIndex(indices[indices.length - 1]) === indices[0]) {\r\n            indices.unshift(indices.pop())\r\n        }\r\n    }\r\n    return indices\r\n}\r\n\r\nfunction generateDateBreaksForWeekend(min, max, weekendDayIndices) {\r\n    let day = min.getDate();\r\n    const breaks = [];\r\n    const weekends = weekendDayIndices.reduce((function(obj, day) {\r\n        let currentWeekEnd = obj[1];\r\n        if (void 0 === currentWeekEnd.start) {\r\n            currentWeekEnd = {\r\n                start: day,\r\n                end: getNextDayIndex(day)\r\n            };\r\n            obj[0].push(currentWeekEnd);\r\n            return [obj[0], currentWeekEnd]\r\n        } else if (currentWeekEnd.end === day) {\r\n            currentWeekEnd.end = getNextDayIndex(day);\r\n            return obj\r\n        }\r\n        currentWeekEnd = {\r\n            start: day,\r\n            end: getNextDayIndex(day)\r\n        };\r\n        obj[0].push(currentWeekEnd);\r\n        return [obj[0], currentWeekEnd]\r\n    }), [\r\n        [], {}\r\n    ]);\r\n    weekends[0].forEach((function(weekend) {\r\n        let currentDate = new Date(min);\r\n        currentDate = dateUtils.trimTime(currentDate);\r\n        while (currentDate < max) {\r\n            day = currentDate.getDay();\r\n            const date = currentDate.getDate();\r\n            if (dayBetweenWeekend(weekend, day)) {\r\n                const from = new Date(currentDate);\r\n                currentDate.setDate(date + getDaysDistance(day, weekend.end));\r\n                const to = new Date(currentDate);\r\n                breaks.push({\r\n                    from: from,\r\n                    to: to\r\n                })\r\n            }\r\n            currentDate.setDate(currentDate.getDate() + 1)\r\n        }\r\n    }));\r\n    return breaks\r\n}\r\n\r\nfunction excludeWorkDaysFromWeekEndBreaks(breaks, exactWorkDays) {\r\n    const result = breaks.slice();\r\n    let i;\r\n    const processWorkDay = function(workday) {\r\n        workday = dateUtils.trimTime(new Date(workday));\r\n        if (result[i].from <= workday && result[i].to > workday) {\r\n            const separatedBreak = separateBreak(result[i], workday);\r\n            if (2 === separatedBreak.length) {\r\n                result.splice(i, 1, separatedBreak[0], separatedBreak[1])\r\n            } else if (1 === separatedBreak.length) {\r\n                result.splice(i, 1, separatedBreak[0])\r\n            } else {\r\n                result.splice(i, 1)\r\n            }\r\n        }\r\n    };\r\n    for (i = 0; i < result.length; i++) {\r\n        exactWorkDays.forEach(processWorkDay)\r\n    }\r\n    return result\r\n}\r\n\r\nfunction generateBreaksForHolidays(min, max, holidays, weekendDayIndices) {\r\n    let day;\r\n    const dayInWeekend = function(dayIndex) {\r\n        return dayIndex === day\r\n    };\r\n    const adjustedMin = dateUtils.trimTime(min);\r\n    const adjustedMax = dateUtils.trimTime(max);\r\n    adjustedMax.setDate(max.getDate() + 1);\r\n    return holidays.reduce((function(breaks, holiday) {\r\n        let holidayStart;\r\n        let holidayEnd;\r\n        holiday = new Date(holiday);\r\n        day = holiday.getDay();\r\n        if (!weekendDayIndices.some(dayInWeekend) && holiday >= adjustedMin && holiday <= adjustedMax) {\r\n            holidayStart = dateUtils.trimTime(holiday);\r\n            holidayEnd = new Date(holidayStart);\r\n            holidayEnd.setDate(holidayStart.getDate() + 1);\r\n            breaks.push({\r\n                from: holidayStart,\r\n                to: holidayEnd\r\n            })\r\n        }\r\n        return breaks\r\n    }), [])\r\n}\r\n\r\nfunction calculateGaps(breaks) {\r\n    return breaks.map((function(b) {\r\n        return {\r\n            from: b.from,\r\n            to: b.to,\r\n            gapSize: dateUtils.convertMillisecondsToDateUnits(b.to - b.from)\r\n        }\r\n    }))\r\n}\r\nexport function generateDateBreaks(min, max, workWeek, singleWorkdays, holidays) {\r\n    const weekendDayIndices = getWeekEndDayIndices(workWeek);\r\n    const breaks = generateDateBreaksForWeekend(min, max, weekendDayIndices);\r\n    breaks.push.apply(breaks, generateBreaksForHolidays(min, max, holidays || [], weekendDayIndices));\r\n    return calculateGaps(excludeWorkDaysFromWeekEndBreaks(breaks, singleWorkdays || []))\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AACA,MAAM,OAAO;IAAC;IAAG;IAAG;IAAG;IAAG;IAAG;IAAG;CAAE;AAElC,SAAS,eAAe,QAAQ;IAC5B,OAAO,KAAK,MAAM,CAAE,SAAS,GAAG;QAC5B,OAAO,CAAC,SAAS,IAAI,CAAE,SAAS,OAAO;YACnC,OAAO,YAAY;QACvB;IACJ;AACJ;AAEA,SAAS,gBAAgB,QAAQ;IAC7B,OAAO,CAAC,WAAW,CAAC,IAAI;AAC5B;AAEA,SAAS,kBAAkB,OAAO,EAAE,GAAG;IACnC,IAAI,QAAQ,QAAQ,KAAK;IACzB,MAAM,MAAM,QAAQ,GAAG;IACvB,MAAO,UAAU,IAAK;QAClB,IAAI,UAAU,KAAK;YACf,OAAO;QACX;QACA,QAAQ,gBAAgB;IAC5B;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,GAAG,EAAE,GAAG;IAC7B,IAAI,SAAS;IACb,MAAO,QAAQ,IAAK;QAChB,MAAM,gBAAgB;QACtB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,cAAc,UAAU,EAAE,GAAG;IAClC,MAAM,SAAS,EAAE;IACjB,MAAM,SAAS,IAAI,KAAK;IACxB,OAAO,OAAO,CAAC,IAAI,OAAO,KAAK;IAC/B,IAAI,MAAM,WAAW,IAAI,EAAE;QACvB,OAAO,IAAI,CAAC;YACR,MAAM,WAAW,IAAI;YACrB,IAAI;QACR;IACJ;IACA,IAAI,SAAS,WAAW,EAAE,EAAE;QACxB,OAAO,IAAI,CAAC;YACR,MAAM;YACN,IAAI,WAAW,EAAE;QACrB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,qBAAqB,QAAQ;IAClC,MAAM,UAAU,eAAe;IAC/B,IAAI,QAAQ,MAAM,GAAG,GAAG;QACpB,MAAO,gBAAgB,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,MAAM,OAAO,CAAC,EAAE,CAAE;YAChE,QAAQ,OAAO,CAAC,QAAQ,GAAG;QAC/B;IACJ;IACA,OAAO;AACX;AAEA,SAAS,6BAA6B,GAAG,EAAE,GAAG,EAAE,iBAAiB;IAC7D,IAAI,MAAM,IAAI,OAAO;IACrB,MAAM,SAAS,EAAE;IACjB,MAAM,WAAW,kBAAkB,MAAM,CAAE,SAAS,GAAG,EAAE,GAAG;QACxD,IAAI,iBAAiB,GAAG,CAAC,EAAE;QAC3B,IAAI,KAAK,MAAM,eAAe,KAAK,EAAE;YACjC,iBAAiB;gBACb,OAAO;gBACP,KAAK,gBAAgB;YACzB;YACA,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;YACZ,OAAO;gBAAC,GAAG,CAAC,EAAE;gBAAE;aAAe;QACnC,OAAO,IAAI,eAAe,GAAG,KAAK,KAAK;YACnC,eAAe,GAAG,GAAG,gBAAgB;YACrC,OAAO;QACX;QACA,iBAAiB;YACb,OAAO;YACP,KAAK,gBAAgB;QACzB;QACA,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC;QACZ,OAAO;YAAC,GAAG,CAAC,EAAE;YAAE;SAAe;IACnC,GAAI;QACA,EAAE;QAAE,CAAC;KACR;IACD,QAAQ,CAAC,EAAE,CAAC,OAAO,CAAE,SAAS,OAAO;QACjC,IAAI,cAAc,IAAI,KAAK;QAC3B,cAAc,0JAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;QACjC,MAAO,cAAc,IAAK;YACtB,MAAM,YAAY,MAAM;YACxB,MAAM,OAAO,YAAY,OAAO;YAChC,IAAI,kBAAkB,SAAS,MAAM;gBACjC,MAAM,OAAO,IAAI,KAAK;gBACtB,YAAY,OAAO,CAAC,OAAO,gBAAgB,KAAK,QAAQ,GAAG;gBAC3D,MAAM,KAAK,IAAI,KAAK;gBACpB,OAAO,IAAI,CAAC;oBACR,MAAM;oBACN,IAAI;gBACR;YACJ;YACA,YAAY,OAAO,CAAC,YAAY,OAAO,KAAK;QAChD;IACJ;IACA,OAAO;AACX;AAEA,SAAS,iCAAiC,MAAM,EAAE,aAAa;IAC3D,MAAM,SAAS,OAAO,KAAK;IAC3B,IAAI;IACJ,MAAM,iBAAiB,SAAS,OAAO;QACnC,UAAU,0JAAA,CAAA,UAAS,CAAC,QAAQ,CAAC,IAAI,KAAK;QACtC,IAAI,MAAM,CAAC,EAAE,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,EAAE,CAAC,EAAE,GAAG,SAAS;YACrD,MAAM,iBAAiB,cAAc,MAAM,CAAC,EAAE,EAAE;YAChD,IAAI,MAAM,eAAe,MAAM,EAAE;gBAC7B,OAAO,MAAM,CAAC,GAAG,GAAG,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE;YAC5D,OAAO,IAAI,MAAM,eAAe,MAAM,EAAE;gBACpC,OAAO,MAAM,CAAC,GAAG,GAAG,cAAc,CAAC,EAAE;YACzC,OAAO;gBACH,OAAO,MAAM,CAAC,GAAG;YACrB;QACJ;IACJ;IACA,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QAChC,cAAc,OAAO,CAAC;IAC1B;IACA,OAAO;AACX;AAEA,SAAS,0BAA0B,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,iBAAiB;IACpE,IAAI;IACJ,MAAM,eAAe,SAAS,QAAQ;QAClC,OAAO,aAAa;IACxB;IACA,MAAM,cAAc,0JAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;IACvC,MAAM,cAAc,0JAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;IACvC,YAAY,OAAO,CAAC,IAAI,OAAO,KAAK;IACpC,OAAO,SAAS,MAAM,CAAE,SAAS,MAAM,EAAE,OAAO;QAC5C,IAAI;QACJ,IAAI;QACJ,UAAU,IAAI,KAAK;QACnB,MAAM,QAAQ,MAAM;QACpB,IAAI,CAAC,kBAAkB,IAAI,CAAC,iBAAiB,WAAW,eAAe,WAAW,aAAa;YAC3F,eAAe,0JAAA,CAAA,UAAS,CAAC,QAAQ,CAAC;YAClC,aAAa,IAAI,KAAK;YACtB,WAAW,OAAO,CAAC,aAAa,OAAO,KAAK;YAC5C,OAAO,IAAI,CAAC;gBACR,MAAM;gBACN,IAAI;YACR;QACJ;QACA,OAAO;IACX,GAAI,EAAE;AACV;AAEA,SAAS,cAAc,MAAM;IACzB,OAAO,OAAO,GAAG,CAAE,SAAS,CAAC;QACzB,OAAO;YACH,MAAM,EAAE,IAAI;YACZ,IAAI,EAAE,EAAE;YACR,SAAS,0JAAA,CAAA,UAAS,CAAC,8BAA8B,CAAC,EAAE,EAAE,GAAG,EAAE,IAAI;QACnE;IACJ;AACJ;AACO,SAAS,mBAAmB,GAAG,EAAE,GAAG,EAAE,QAAQ,EAAE,cAAc,EAAE,QAAQ;IAC3E,MAAM,oBAAoB,qBAAqB;IAC/C,MAAM,SAAS,6BAA6B,KAAK,KAAK;IACtD,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,0BAA0B,KAAK,KAAK,YAAY,EAAE,EAAE;IAC9E,OAAO,cAAc,iCAAiC,QAAQ,kBAAkB,EAAE;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1640, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/xy_axes.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/xy_axes.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    Range\r\n} from \"../translators/range\";\r\nimport formatHelper from \"../../format_helper\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    generateDateBreaks\r\n} from \"./datetime_breaks\";\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    getLog,\r\n    patchFontOptions,\r\n    getCosAndSin\r\n} from \"../core/utils\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport constants from \"./axes_constants\";\r\nconst getNextDateUnit = dateUtils.getNextDateUnit;\r\nconst correctDateWithUnitBeginning = dateUtils.correctDateWithUnitBeginning;\r\nconst _math = Math;\r\nconst _max = _math.max;\r\nconst TOP = constants.top;\r\nconst BOTTOM = constants.bottom;\r\nconst LEFT = constants.left;\r\nconst RIGHT = constants.right;\r\nconst CENTER = constants.center;\r\nconst SCALE_BREAK_OFFSET = 3;\r\nconst RANGE_RATIO = .3;\r\nconst WAVED_LINE_CENTER = 2;\r\nconst WAVED_LINE_TOP = 0;\r\nconst WAVED_LINE_BOTTOM = 4;\r\nconst WAVED_LINE_LENGTH = 24;\r\nconst TICKS_CORRECTIONS = {\r\n    left: -1,\r\n    top: -1,\r\n    right: 0,\r\n    bottom: 0,\r\n    center: -.5\r\n};\r\n\r\nfunction prepareDatesDifferences(datesDifferences, tickInterval) {\r\n    let dateUnitInterval;\r\n    let i;\r\n    if (\"week\" === tickInterval) {\r\n        tickInterval = \"day\"\r\n    }\r\n    if (\"quarter\" === tickInterval) {\r\n        tickInterval = \"month\"\r\n    }\r\n    if (datesDifferences[tickInterval]) {\r\n        for (i = 0; i < dateUtils.dateUnitIntervals.length; i++) {\r\n            dateUnitInterval = dateUtils.dateUnitIntervals[i];\r\n            if (datesDifferences[dateUnitInterval]) {\r\n                datesDifferences[dateUnitInterval] = false;\r\n                datesDifferences.count--\r\n            }\r\n            if (dateUnitInterval === tickInterval) {\r\n                break\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nfunction sortingBreaks(breaks) {\r\n    return breaks.sort((function(a, b) {\r\n        return a.from - b.from\r\n    }))\r\n}\r\n\r\nfunction getMarkerDates(min, max, markerInterval) {\r\n    const origMin = min;\r\n    let dates;\r\n    min = correctDateWithUnitBeginning(min, markerInterval);\r\n    max = correctDateWithUnitBeginning(max, markerInterval);\r\n    dates = dateUtils.getSequenceByInterval(min, max, markerInterval);\r\n    if (dates.length && origMin > dates[0]) {\r\n        dates = dates.slice(1)\r\n    }\r\n    return dates\r\n}\r\n\r\nfunction getStripHorizontalAlignmentPosition(alignment) {\r\n    let position = \"start\";\r\n    if (\"center\" === alignment) {\r\n        position = \"center\"\r\n    }\r\n    if (\"right\" === alignment) {\r\n        position = \"end\"\r\n    }\r\n    return position\r\n}\r\n\r\nfunction getStripVerticalAlignmentPosition(alignment) {\r\n    let position = \"start\";\r\n    if (\"center\" === alignment) {\r\n        position = \"center\"\r\n    }\r\n    if (\"bottom\" === alignment) {\r\n        position = \"end\"\r\n    }\r\n    return position\r\n}\r\n\r\nfunction getMarkerInterval(tickInterval) {\r\n    let markerInterval = getNextDateUnit(tickInterval);\r\n    if (\"quarter\" === markerInterval) {\r\n        markerInterval = getNextDateUnit(markerInterval)\r\n    }\r\n    return markerInterval\r\n}\r\n\r\nfunction getMarkerFormat(curDate, prevDate, tickInterval, markerInterval) {\r\n    let format = markerInterval;\r\n    const datesDifferences = prevDate && dateUtils.getDatesDifferences(prevDate, curDate);\r\n    if (prevDate && \"year\" !== tickInterval) {\r\n        prepareDatesDifferences(datesDifferences, tickInterval);\r\n        format = formatHelper.getDateFormatByDifferences(datesDifferences)\r\n    }\r\n    return format\r\n}\r\n\r\nfunction getMaxSide(act, boxes) {\r\n    return boxes.reduce((function(prevValue, box) {\r\n        return _max(prevValue, act(box))\r\n    }), 0)\r\n}\r\n\r\nfunction getDistanceByAngle(bBox, rotationAngle) {\r\n    rotationAngle = _math.abs(rotationAngle);\r\n    rotationAngle = rotationAngle % 180 >= 90 ? 90 - rotationAngle % 90 : rotationAngle % 90;\r\n    const a = rotationAngle * (_math.PI / 180);\r\n    if (a >= _math.atan(bBox.height / bBox.width)) {\r\n        return bBox.height / _math.abs(_math.sin(a))\r\n    } else {\r\n        return bBox.width\r\n    }\r\n}\r\n\r\nfunction getMaxConstantLinePadding(constantLines) {\r\n    return constantLines.reduce((function(padding, options) {\r\n        return _max(padding, options.paddingTopBottom)\r\n    }), 0)\r\n}\r\n\r\nfunction getConstantLineLabelMarginForVerticalAlignment(constantLines, alignment, labelHeight) {\r\n    return constantLines.some((function(options) {\r\n        return options.label.verticalAlignment === alignment\r\n    })) && labelHeight || 0\r\n}\r\n\r\nfunction getLeftMargin(bBox) {\r\n    return _math.abs(bBox.x) || 0\r\n}\r\n\r\nfunction getRightMargin(bBox) {\r\n    return _math.abs(bBox.width - _math.abs(bBox.x)) || 0\r\n}\r\n\r\nfunction generateRangesOnPoints(points, edgePoints, getRange) {\r\n    let i;\r\n    let length;\r\n    let maxRange = null;\r\n    const ranges = [];\r\n    let curValue;\r\n    let prevValue;\r\n    let curRange;\r\n    for (i = 1, length = points.length; i < length; i++) {\r\n        curValue = points[i];\r\n        prevValue = points[i - 1];\r\n        curRange = getRange(curValue, prevValue);\r\n        if (edgePoints.indexOf(curValue) >= 0) {\r\n            if (!maxRange || curRange > maxRange.length) {\r\n                maxRange = {\r\n                    start: curValue,\r\n                    end: prevValue,\r\n                    length: curRange\r\n                }\r\n            }\r\n        } else {\r\n            if (maxRange && curRange < maxRange.length) {\r\n                ranges.push(maxRange)\r\n            } else {\r\n                ranges.push({\r\n                    start: curValue,\r\n                    end: prevValue,\r\n                    length: curRange\r\n                })\r\n            }\r\n            maxRange = null\r\n        }\r\n    }\r\n    if (maxRange) {\r\n        ranges.push(maxRange)\r\n    }\r\n    return ranges\r\n}\r\n\r\nfunction generateAutoBreaks(_ref, series, _ref2) {\r\n    let {\r\n        logarithmBase: logarithmBase,\r\n        type: type,\r\n        maxAutoBreakCount: maxAutoBreakCount\r\n    } = _ref;\r\n    let {\r\n        minVisible: minVisible,\r\n        maxVisible: maxVisible\r\n    } = _ref2;\r\n    const breaks = [];\r\n    const getRange = \"logarithmic\" === type ? (min, max) => getLog(max / min, logarithmBase) : (min, max) => max - min;\r\n    let visibleRange = getRange(minVisible, maxVisible);\r\n    const points = series.reduce(((result, s) => {\r\n        const points = s.getPointsInViewPort();\r\n        result[0] = result[0].concat(points[0]);\r\n        result[1] = result[1].concat(points[1]);\r\n        return result\r\n    }), [\r\n        [],\r\n        []\r\n    ]);\r\n    const sortedAllPoints = points[0].concat(points[1]).sort(((a, b) => b - a));\r\n    const edgePoints = points[1].filter((p => points[0].indexOf(p) < 0));\r\n    let minDiff = .3 * visibleRange;\r\n    const ranges = generateRangesOnPoints(sortedAllPoints, edgePoints, getRange).filter((_ref3 => {\r\n        let {\r\n            length: length\r\n        } = _ref3;\r\n        return !!length\r\n    })).sort(((a, b) => b.length - a.length));\r\n    const epsilon = _math.min.apply(null, ranges.map((r => r.length))) / 1e3;\r\n    const _maxAutoBreakCount = isDefined(maxAutoBreakCount) ? _math.min(maxAutoBreakCount, ranges.length) : ranges.length;\r\n    for (let i = 0; i < _maxAutoBreakCount; i++) {\r\n        if (ranges[i].length >= minDiff) {\r\n            if (visibleRange <= ranges[i].length) {\r\n                break\r\n            }\r\n            visibleRange -= ranges[i].length;\r\n            if (visibleRange > epsilon || visibleRange < -epsilon) {\r\n                breaks.push({\r\n                    from: ranges[i].start,\r\n                    to: ranges[i].end\r\n                });\r\n                minDiff = .3 * visibleRange\r\n            }\r\n        } else {\r\n            break\r\n        }\r\n    }\r\n    sortingBreaks(breaks);\r\n    return breaks\r\n}\r\nexport default {\r\n    linear: {\r\n        _getStep: function(boxes, rotationAngle) {\r\n            const spacing = this._options.label.minSpacing;\r\n            const func = this._isHorizontal ? function(box) {\r\n                return box.width + spacing\r\n            } : function(box) {\r\n                return box.height\r\n            };\r\n            let maxLabelLength = getMaxSide(func, boxes);\r\n            if (rotationAngle) {\r\n                maxLabelLength = getDistanceByAngle({\r\n                    width: maxLabelLength,\r\n                    height: this._getMaxLabelHeight(boxes, 0)\r\n                }, rotationAngle)\r\n            }\r\n            return constants.getTicksCountInRange(this._majorTicks, this._isHorizontal ? \"x\" : \"y\", maxLabelLength)\r\n        },\r\n        _getMaxLabelHeight: function(boxes, spacing) {\r\n            return getMaxSide((function(box) {\r\n                return box.height\r\n            }), boxes) + spacing\r\n        },\r\n        _validateOverlappingMode: function(mode, displayMode) {\r\n            if (this._isHorizontal && (\"rotate\" === displayMode || \"stagger\" === displayMode) || !this._isHorizontal) {\r\n                return constants.validateOverlappingMode(mode)\r\n            }\r\n            return mode\r\n        },\r\n        _validateDisplayMode: function(mode) {\r\n            return this._isHorizontal ? mode : \"standard\"\r\n        },\r\n        getMarkerTrackers: function() {\r\n            return this._markerTrackers\r\n        },\r\n        _getSharpParam: function(opposite) {\r\n            return this._isHorizontal ^ opposite ? \"h\" : \"v\"\r\n        },\r\n        _createAxisElement: function() {\r\n            return this._renderer.path([], \"line\")\r\n        },\r\n        _updateAxisElementPosition: function() {\r\n            const axisCoord = this._axisPosition;\r\n            const canvas = this._getCanvasStartEnd();\r\n            this._axisElement.attr({\r\n                points: this._isHorizontal ? [canvas.start, axisCoord, canvas.end, axisCoord] : [axisCoord, canvas.start, axisCoord, canvas.end]\r\n            })\r\n        },\r\n        _getTranslatedCoord: function(value, offset) {\r\n            return this._translator.translate(value, offset)\r\n        },\r\n        _initAxisPositions() {\r\n            const that = this;\r\n            if (that.customPositionIsAvailable()) {\r\n                that._customBoundaryPosition = that.getCustomBoundaryPosition()\r\n            }\r\n            if (!that.customPositionIsAvailable() || that.customPositionIsBoundary()) {\r\n                that._axisPosition = that.getPredefinedPosition(that.getResolvedBoundaryPosition())\r\n            } else {\r\n                that._axisPosition = that.getCustomPosition()\r\n            }\r\n        },\r\n        _getTickMarkPoints(coords, length, tickOptions) {\r\n            const isHorizontal = this._isHorizontal;\r\n            const tickOrientation = this._options.tickOrientation;\r\n            const labelPosition = this._options.label.position;\r\n            let tickStartCoord;\r\n            if (isDefined(tickOrientation)) {\r\n                tickStartCoord = TICKS_CORRECTIONS[tickOrientation] * length\r\n            } else {\r\n                let shift = tickOptions.shift || 0;\r\n                if (!isHorizontal && labelPosition === LEFT || isHorizontal && labelPosition !== BOTTOM) {\r\n                    shift = -shift\r\n                }\r\n                tickStartCoord = shift + this.getTickStartPositionShift(length)\r\n            }\r\n            return [coords.x + (isHorizontal ? 0 : tickStartCoord), coords.y + (isHorizontal ? tickStartCoord : 0), coords.x + (isHorizontal ? 0 : tickStartCoord + length), coords.y + (isHorizontal ? tickStartCoord + length : 0)]\r\n        },\r\n        getTickStartPositionShift(length) {\r\n            const width = this._options.width;\r\n            const position = this.getResolvedBoundaryPosition();\r\n            return length % 2 === 1 ? width % 2 === 0 && (position === LEFT || position === TOP) || width % 2 === 1 && (position === RIGHT || position === BOTTOM) && !this.hasNonBoundaryPosition() ? Math.floor(-length / 2) : -Math.floor(length / 2) : -length / 2 + (width % 2 === 0 ? 0 : position === BOTTOM || position === RIGHT ? -1 : 1)\r\n        },\r\n        _getTitleCoords: function() {\r\n            const horizontal = this._isHorizontal;\r\n            let x = this._axisPosition;\r\n            let y = this._axisPosition;\r\n            const align = this._options.title.alignment;\r\n            const canvas = this._getCanvasStartEnd();\r\n            const fromStartToEnd = horizontal || this._options.position === LEFT;\r\n            const canvasStart = fromStartToEnd ? canvas.start : canvas.end;\r\n            const canvasEnd = fromStartToEnd ? canvas.end : canvas.start;\r\n            const coord = align === LEFT ? canvasStart : align === RIGHT ? canvasEnd : canvas.start + (canvas.end - canvas.start) / 2;\r\n            if (horizontal) {\r\n                x = coord\r\n            } else {\r\n                y = coord\r\n            }\r\n            return {\r\n                x: x,\r\n                y: y\r\n            }\r\n        },\r\n        _drawTitleText: function(group, coords) {\r\n            const options = this._options;\r\n            const titleOptions = options.title;\r\n            const attrs = {\r\n                opacity: titleOptions.opacity,\r\n                align: titleOptions.alignment,\r\n                class: titleOptions.cssClass\r\n            };\r\n            if (!titleOptions.text || !group) {\r\n                return\r\n            }\r\n            coords = coords || this._getTitleCoords();\r\n            if (!this._isHorizontal) {\r\n                attrs.rotate = options.position === LEFT ? 270 : 90\r\n            }\r\n            const text = this._renderer.text(titleOptions.text, coords.x, coords.y).css(patchFontOptions(titleOptions.font)).attr(attrs).append(group);\r\n            this._checkTitleOverflow(text);\r\n            return text\r\n        },\r\n        _updateTitleCoords: function() {\r\n            this._title && this._title.element.attr(this._getTitleCoords())\r\n        },\r\n        _drawTitle: function() {\r\n            const title = this._drawTitleText(this._axisTitleGroup);\r\n            if (title) {\r\n                this._title = {\r\n                    element: title\r\n                }\r\n            }\r\n        },\r\n        _measureTitle: function() {\r\n            if (this._title) {\r\n                if (this._title.bBox && !this._title.originalSize) {\r\n                    this._title.originalSize = this._title.bBox\r\n                }\r\n                this._title.bBox = this._title.element.getBBox()\r\n            }\r\n        },\r\n        _drawDateMarker: function(date, options, range) {\r\n            const that = this;\r\n            const markerOptions = that._options.marker;\r\n            const invert = that._translator.getBusinessRange().invert;\r\n            const textIndent = markerOptions.width + markerOptions.textLeftIndent;\r\n            let pathElement;\r\n            if (null === options.x) {\r\n                return\r\n            }\r\n            if (!options.withoutStick) {\r\n                pathElement = that._renderer.path([options.x, options.y, options.x, options.y + markerOptions.separatorHeight], \"line\").attr({\r\n                    \"stroke-width\": markerOptions.width,\r\n                    stroke: markerOptions.color,\r\n                    \"stroke-opacity\": markerOptions.opacity,\r\n                    sharp: \"h\"\r\n                }).append(that._axisElementsGroup)\r\n            }\r\n            const text = String(that.formatLabel(date, options.labelOptions, range));\r\n            return {\r\n                date: date,\r\n                x: options.x,\r\n                y: options.y,\r\n                cropped: options.withoutStick,\r\n                label: that._renderer.text(text, options.x, options.y).css(patchFontOptions(markerOptions.label.font)).append(that._axisElementsGroup),\r\n                line: pathElement,\r\n                getContentContainer() {\r\n                    return this.label\r\n                },\r\n                getEnd: function() {\r\n                    return this.x + (invert ? -1 : 1) * (textIndent + this.labelBBox.width)\r\n                },\r\n                setTitle: function() {\r\n                    this.title = text\r\n                },\r\n                hideLabel: function() {\r\n                    this.label.dispose();\r\n                    this.label = null;\r\n                    this.title = text\r\n                },\r\n                hide: function() {\r\n                    if (pathElement) {\r\n                        pathElement.dispose();\r\n                        pathElement = null\r\n                    }\r\n                    this.label.dispose();\r\n                    this.label = null;\r\n                    this.hidden = true\r\n                }\r\n            }\r\n        },\r\n        _drawDateMarkers: function() {\r\n            const that = this;\r\n            const options = that._options;\r\n            const translator = that._translator;\r\n            const viewport = that._getViewportRange();\r\n            const minBound = viewport.minVisible;\r\n            let dateMarkers = [];\r\n            let dateMarker;\r\n\r\n            function draw(markerDate, format, withoutStick) {\r\n                return that._drawDateMarker(markerDate, {\r\n                    x: translator.translate(markerDate),\r\n                    y: markersAreaTop,\r\n                    labelOptions: that._getLabelFormatOptions(format),\r\n                    withoutStick: withoutStick\r\n                }, viewport)\r\n            }\r\n            if (viewport.isEmpty() || !options.marker.visible || \"datetime\" !== options.argumentType || \"discrete\" === options.type || that._majorTicks.length <= 1) {\r\n                return []\r\n            }\r\n            const markersAreaTop = that._axisPosition + options.marker.topIndent;\r\n            const tickInterval = dateUtils.getDateUnitInterval(this._tickInterval);\r\n            const markerInterval = getMarkerInterval(tickInterval);\r\n            const markerDates = getMarkerDates(minBound, viewport.maxVisible, markerInterval);\r\n            if (markerDates.length > 1 || 1 === markerDates.length && minBound < markerDates[0]) {\r\n                dateMarkers = markerDates.reduce((function(markers, curDate, i, dates) {\r\n                    const marker = draw(curDate, getMarkerFormat(curDate, dates[i - 1] || minBound < curDate && minBound, tickInterval, markerInterval));\r\n                    marker && markers.push(marker);\r\n                    return markers\r\n                }), []);\r\n                if (minBound < markerDates[0]) {\r\n                    dateMarker = draw(minBound, getMarkerFormat(minBound, markerDates[0], tickInterval, markerInterval), true);\r\n                    dateMarker && dateMarkers.unshift(dateMarker)\r\n                }\r\n            }\r\n            return dateMarkers\r\n        },\r\n        _adjustDateMarkers: function(offset) {\r\n            offset = offset || 0;\r\n            const that = this;\r\n            const markerOptions = this._options.marker;\r\n            const textIndent = markerOptions.width + markerOptions.textLeftIndent;\r\n            const invert = this._translator.getBusinessRange().invert;\r\n            const canvas = that._getCanvasStartEnd();\r\n            const dateMarkers = this._dateMarkers;\r\n            if (!dateMarkers.length) {\r\n                return offset\r\n            }\r\n            if (dateMarkers[0].cropped) {\r\n                if (!this._checkMarkersPosition(invert, dateMarkers[1], dateMarkers[0])) {\r\n                    dateMarkers[0].hideLabel()\r\n                }\r\n            }\r\n            let prevDateMarker;\r\n            dateMarkers.forEach((function(marker, i, markers) {\r\n                if (marker.cropped) {\r\n                    return\r\n                }\r\n                if (invert ? marker.getEnd() < canvas.end : marker.getEnd() > canvas.end) {\r\n                    marker.hideLabel()\r\n                } else if (that._checkMarkersPosition(invert, marker, prevDateMarker)) {\r\n                    prevDateMarker = marker\r\n                } else {\r\n                    marker.hide()\r\n                }\r\n            }));\r\n            this._dateMarkers.forEach((function(marker) {\r\n                if (marker.label) {\r\n                    const labelBBox = marker.labelBBox;\r\n                    const dy = marker.y + markerOptions.textTopIndent - labelBBox.y;\r\n                    marker.label.attr({\r\n                        translateX: invert ? marker.x - textIndent - labelBBox.x - labelBBox.width : marker.x + textIndent - labelBBox.x,\r\n                        translateY: dy + offset\r\n                    })\r\n                }\r\n                if (marker.line) {\r\n                    marker.line.attr({\r\n                        translateY: offset\r\n                    })\r\n                }\r\n            }));\r\n            that._initializeMarkersTrackers(offset);\r\n            return offset + markerOptions.topIndent + markerOptions.separatorHeight\r\n        },\r\n        _checkMarkersPosition: function(invert, dateMarker, prevDateMarker) {\r\n            if (void 0 === prevDateMarker) {\r\n                return true\r\n            }\r\n            return invert ? dateMarker.x < prevDateMarker.getEnd() : dateMarker.x > prevDateMarker.getEnd()\r\n        },\r\n        _initializeMarkersTrackers: function(offset) {\r\n            const separatorHeight = this._options.marker.separatorHeight;\r\n            const renderer = this._renderer;\r\n            const businessRange = this._translator.getBusinessRange();\r\n            const canvas = this._getCanvasStartEnd();\r\n            const group = this._axisElementsGroup;\r\n            this._markerTrackers = this._dateMarkers.filter((function(marker) {\r\n                return !marker.hidden\r\n            })).map((function(marker, i, markers) {\r\n                const nextMarker = markers[i + 1] || {\r\n                    x: canvas.end,\r\n                    date: businessRange.max\r\n                };\r\n                const x = marker.x;\r\n                const y = marker.y + offset;\r\n                const markerTracker = renderer.path([x, y, x, y + separatorHeight, nextMarker.x, y + separatorHeight, nextMarker.x, y, x, y], \"area\").attr({\r\n                    \"stroke-width\": 1,\r\n                    stroke: \"grey\",\r\n                    fill: \"grey\",\r\n                    opacity: 1e-4\r\n                }).append(group);\r\n                markerTracker.data(\"range\", {\r\n                    startValue: marker.date,\r\n                    endValue: nextMarker.date\r\n                });\r\n                if (marker.title) {\r\n                    markerTracker.setTitle(marker.title)\r\n                }\r\n                return markerTracker\r\n            }))\r\n        },\r\n        _getLabelFormatOptions: function(formatString) {\r\n            const that = this;\r\n            let markerLabelOptions = that._markerLabelOptions;\r\n            if (!markerLabelOptions) {\r\n                that._markerLabelOptions = markerLabelOptions = extend(true, {}, that._options.marker.label)\r\n            }\r\n            if (!isDefined(that._options.marker.label.format)) {\r\n                markerLabelOptions.format = formatString\r\n            }\r\n            return markerLabelOptions\r\n        },\r\n        _adjustConstantLineLabels: function(constantLines) {\r\n            const that = this;\r\n            const axisPosition = that._options.position;\r\n            const canvas = that.getCanvas();\r\n            const canvasLeft = canvas.left;\r\n            const canvasRight = canvas.width - canvas.right;\r\n            const canvasTop = canvas.top;\r\n            const canvasBottom = canvas.height - canvas.bottom;\r\n            const verticalCenter = canvasTop + (canvasBottom - canvasTop) / 2;\r\n            const horizontalCenter = canvasLeft + (canvasRight - canvasLeft) / 2;\r\n            let maxLabel = 0;\r\n            constantLines.forEach((function(item) {\r\n                const isHorizontal = that._isHorizontal;\r\n                const linesOptions = item.options;\r\n                const paddingTopBottom = linesOptions.paddingTopBottom;\r\n                const paddingLeftRight = linesOptions.paddingLeftRight;\r\n                const labelOptions = linesOptions.label;\r\n                const labelVerticalAlignment = labelOptions.verticalAlignment;\r\n                const labelHorizontalAlignment = labelOptions.horizontalAlignment;\r\n                const labelIsInside = \"inside\" === labelOptions.position;\r\n                const label = item.label;\r\n                const box = item.labelBBox;\r\n                let translateX;\r\n                let translateY;\r\n                if (null === label || box.isEmpty) {\r\n                    return\r\n                }\r\n                if (isHorizontal) {\r\n                    if (labelIsInside) {\r\n                        if (labelHorizontalAlignment === LEFT) {\r\n                            translateX = item.coord - paddingLeftRight - box.x - box.width\r\n                        } else {\r\n                            translateX = item.coord + paddingLeftRight - box.x\r\n                        }\r\n                        switch (labelVerticalAlignment) {\r\n                            case CENTER:\r\n                                translateY = verticalCenter - box.y - box.height / 2;\r\n                                break;\r\n                            case BOTTOM:\r\n                                translateY = canvasBottom - paddingTopBottom - box.y - box.height;\r\n                                break;\r\n                            default:\r\n                                translateY = canvasTop + paddingTopBottom - box.y\r\n                        }\r\n                    } else {\r\n                        if (axisPosition === labelVerticalAlignment) {\r\n                            maxLabel = _max(maxLabel, box.height + paddingTopBottom)\r\n                        }\r\n                        translateX = item.coord - box.x - box.width / 2;\r\n                        if (labelVerticalAlignment === BOTTOM) {\r\n                            translateY = canvasBottom + paddingTopBottom - box.y\r\n                        } else {\r\n                            translateY = canvasTop - paddingTopBottom - box.y - box.height\r\n                        }\r\n                    }\r\n                } else if (labelIsInside) {\r\n                    if (labelVerticalAlignment === BOTTOM) {\r\n                        translateY = item.coord + paddingTopBottom - box.y\r\n                    } else {\r\n                        translateY = item.coord - paddingTopBottom - box.y - box.height\r\n                    }\r\n                    switch (labelHorizontalAlignment) {\r\n                        case CENTER:\r\n                            translateX = horizontalCenter - box.x - box.width / 2;\r\n                            break;\r\n                        case RIGHT:\r\n                            translateX = canvasRight - paddingLeftRight - box.x - box.width;\r\n                            break;\r\n                        default:\r\n                            translateX = canvasLeft + paddingLeftRight - box.x\r\n                    }\r\n                } else {\r\n                    if (axisPosition === labelHorizontalAlignment) {\r\n                        maxLabel = _max(maxLabel, box.width + paddingLeftRight)\r\n                    }\r\n                    translateY = item.coord - box.y - box.height / 2;\r\n                    if (labelHorizontalAlignment === RIGHT) {\r\n                        translateX = canvasRight + paddingLeftRight - box.x\r\n                    } else {\r\n                        translateX = canvasLeft - paddingLeftRight - box.x - box.width\r\n                    }\r\n                }\r\n                label.attr({\r\n                    translateX: translateX,\r\n                    translateY: translateY\r\n                })\r\n            }));\r\n            return maxLabel\r\n        },\r\n        _drawConstantLinesForEstimating: function(constantLines) {\r\n            const that = this;\r\n            const renderer = this._renderer;\r\n            const group = renderer.g();\r\n            constantLines.forEach((function(options) {\r\n                that._drawConstantLineLabelText(options.label.text, 0, 0, options.label, group).attr({\r\n                    align: \"center\"\r\n                })\r\n            }));\r\n            return group.append(renderer.root)\r\n        },\r\n        _estimateLabelHeight: function(bBox, labelOptions) {\r\n            let height = bBox.height;\r\n            const drawingType = labelOptions.drawingType;\r\n            if (\"stagger\" === this._validateDisplayMode(drawingType) || \"stagger\" === this._validateOverlappingMode(labelOptions.overlappingBehavior, drawingType)) {\r\n                height = 2 * height + labelOptions.staggeringSpacing\r\n            }\r\n            if (\"rotate\" === this._validateDisplayMode(drawingType) || \"rotate\" === this._validateOverlappingMode(labelOptions.overlappingBehavior, drawingType)) {\r\n                const sinCos = getCosAndSin(labelOptions.rotationAngle);\r\n                height = height * sinCos.cos + bBox.width * sinCos.sin\r\n            }\r\n            return height && (height + labelOptions.indentFromAxis || 0) || 0\r\n        },\r\n        estimateMargins: function(canvas) {\r\n            this.updateCanvas(canvas);\r\n            const {\r\n                position: position,\r\n                placeholderSize: placeholderSize\r\n            } = this._options;\r\n            const range = this._getViewportRange();\r\n            const ticksData = this._createTicksAndLabelFormat(range);\r\n            const ticks = ticksData.ticks;\r\n            const tickInterval = ticksData.tickInterval;\r\n            const options = this._options;\r\n            const constantLineOptions = this._outsideConstantLines.filter((l => l.labelOptions.visible)).map((l => l.options));\r\n            const rootElement = this._renderer.root;\r\n            const labelIsVisible = options.label.visible && !range.isEmpty() && ticks.length;\r\n            const labelValue = labelIsVisible && this.formatLabel(ticks[ticks.length - 1], options.label, void 0, void 0, tickInterval, ticks);\r\n            const labelElement = labelIsVisible && this._renderer.text(labelValue, 0, 0).css(this._textFontStyles).attr(this._textOptions).append(rootElement);\r\n            const titleElement = this._drawTitleText(rootElement, {\r\n                x: 0,\r\n                y: 0\r\n            });\r\n            const constantLinesLabelsElement = this._drawConstantLinesForEstimating(constantLineOptions);\r\n            const labelBox = !options.label.template && labelElement && labelElement.getBBox() || {\r\n                x: 0,\r\n                y: 0,\r\n                width: 0,\r\n                height: 0\r\n            };\r\n            const titleBox = titleElement && titleElement.getBBox() || {\r\n                x: 0,\r\n                y: 0,\r\n                width: 0,\r\n                height: 0\r\n            };\r\n            const constantLinesBox = constantLinesLabelsElement.getBBox();\r\n            const titleHeight = titleBox.height ? titleBox.height + options.title.margin : 0;\r\n            const labelHeight = this._estimateLabelHeight(labelBox, options.label);\r\n            const constantLinesHeight = constantLinesBox.height ? constantLinesBox.height + getMaxConstantLinePadding(constantLineOptions) : 0;\r\n            const height = labelHeight + titleHeight;\r\n            const margins = {\r\n                left: _max(getLeftMargin(labelBox), getLeftMargin(constantLinesBox)),\r\n                right: _max(getRightMargin(labelBox), getRightMargin(constantLinesBox)),\r\n                top: (\"top\" === options.position ? height : 0) + getConstantLineLabelMarginForVerticalAlignment(constantLineOptions, \"top\", constantLinesHeight),\r\n                bottom: (\"top\" !== options.position ? height : 0) + getConstantLineLabelMarginForVerticalAlignment(constantLineOptions, \"bottom\", constantLinesHeight)\r\n            };\r\n            if (placeholderSize) {\r\n                margins[position] = placeholderSize\r\n            }\r\n            labelElement && labelElement.remove();\r\n            titleElement && titleElement.remove();\r\n            constantLinesLabelsElement && constantLinesLabelsElement.remove();\r\n            return margins\r\n        },\r\n        _checkAlignmentConstantLineLabels: function(labelOptions) {\r\n            const position = labelOptions.position;\r\n            let verticalAlignment = (labelOptions.verticalAlignment || \"\").toLowerCase();\r\n            let horizontalAlignment = (labelOptions.horizontalAlignment || \"\").toLowerCase();\r\n            if (this._isHorizontal) {\r\n                if (\"outside\" === position) {\r\n                    verticalAlignment = verticalAlignment === BOTTOM ? BOTTOM : TOP;\r\n                    horizontalAlignment = CENTER\r\n                } else {\r\n                    verticalAlignment = verticalAlignment === CENTER ? CENTER : verticalAlignment === BOTTOM ? BOTTOM : TOP;\r\n                    horizontalAlignment = horizontalAlignment === LEFT ? LEFT : RIGHT\r\n                }\r\n            } else if (\"outside\" === position) {\r\n                verticalAlignment = CENTER;\r\n                horizontalAlignment = horizontalAlignment === LEFT ? LEFT : RIGHT\r\n            } else {\r\n                verticalAlignment = verticalAlignment === BOTTOM ? BOTTOM : TOP;\r\n                horizontalAlignment = horizontalAlignment === RIGHT ? RIGHT : horizontalAlignment === CENTER ? CENTER : LEFT\r\n            }\r\n            labelOptions.verticalAlignment = verticalAlignment;\r\n            labelOptions.horizontalAlignment = horizontalAlignment\r\n        },\r\n        _getConstantLineLabelsCoords: function(value, lineLabelOptions) {\r\n            const that = this;\r\n            let x = value;\r\n            let y = value;\r\n            if (that._isHorizontal) {\r\n                y = that._orthogonalPositions[\"top\" === lineLabelOptions.verticalAlignment ? \"start\" : \"end\"]\r\n            } else {\r\n                x = that._orthogonalPositions[\"right\" === lineLabelOptions.horizontalAlignment ? \"end\" : \"start\"]\r\n            }\r\n            return {\r\n                x: x,\r\n                y: y\r\n            }\r\n        },\r\n        _getAdjustedStripLabelCoords: function(strip) {\r\n            const stripOptions = strip.options;\r\n            const paddingTopBottom = stripOptions.paddingTopBottom;\r\n            const paddingLeftRight = stripOptions.paddingLeftRight;\r\n            const horizontalAlignment = stripOptions.label.horizontalAlignment;\r\n            const verticalAlignment = stripOptions.label.verticalAlignment;\r\n            const box = strip.labelBBox;\r\n            const labelHeight = box.height;\r\n            const labelWidth = box.width;\r\n            const labelCoords = strip.labelCoords;\r\n            let y = labelCoords.y - box.y;\r\n            let x = labelCoords.x - box.x;\r\n            if (verticalAlignment === TOP) {\r\n                y += paddingTopBottom\r\n            } else if (verticalAlignment === CENTER) {\r\n                y -= labelHeight / 2\r\n            } else if (verticalAlignment === BOTTOM) {\r\n                y -= paddingTopBottom + labelHeight\r\n            }\r\n            if (horizontalAlignment === LEFT) {\r\n                x += paddingLeftRight\r\n            } else if (horizontalAlignment === CENTER) {\r\n                x -= labelWidth / 2\r\n            } else if (horizontalAlignment === RIGHT) {\r\n                x -= paddingLeftRight + labelWidth\r\n            }\r\n            return {\r\n                translateX: x,\r\n                translateY: y\r\n            }\r\n        },\r\n        _adjustTitle: function(offset) {\r\n            offset = offset || 0;\r\n            if (!this._title) {\r\n                return\r\n            }\r\n            const options = this._options;\r\n            const position = options.position;\r\n            const margin = options.title.margin;\r\n            const title = this._title;\r\n            const boxTitle = title.bBox;\r\n            const x = boxTitle.x;\r\n            const y = boxTitle.y;\r\n            const width = boxTitle.width;\r\n            const height = boxTitle.height;\r\n            const axisPosition = this._axisPosition;\r\n            const loCoord = axisPosition - margin - offset;\r\n            const hiCoord = axisPosition + margin + offset;\r\n            const params = {};\r\n            if (this._isHorizontal) {\r\n                if (position === TOP) {\r\n                    params.translateY = loCoord - (y + height)\r\n                } else {\r\n                    params.translateY = hiCoord - y\r\n                }\r\n            } else if (position === LEFT) {\r\n                params.translateX = loCoord - (x + width)\r\n            } else {\r\n                params.translateX = hiCoord - x\r\n            }\r\n            title.element.attr(params)\r\n        },\r\n        _checkTitleOverflow: function(titleElement) {\r\n            if (!this._title && !titleElement) {\r\n                return\r\n            }\r\n            const canvasLength = this._getScreenDelta();\r\n            const title = titleElement ? {\r\n                bBox: titleElement.getBBox(),\r\n                element: titleElement\r\n            } : this._title;\r\n            const titleOptions = this._options.title;\r\n            const boxTitle = title.bBox;\r\n            if ((this._isHorizontal ? boxTitle.width : boxTitle.height) > canvasLength) {\r\n                title.element.setMaxSize(canvasLength, void 0, {\r\n                    wordWrap: titleOptions.wordWrap || \"none\",\r\n                    textOverflow: titleOptions.textOverflow || \"ellipsis\"\r\n                });\r\n                this._wrapped = titleOptions.wordWrap && \"none\" !== titleOptions.wordWrap\r\n            } else {\r\n                const moreThanOriginalSize = title.originalSize && canvasLength > (this._isHorizontal ? title.originalSize.width : title.originalSize.height);\r\n                !this._wrapped && moreThanOriginalSize && title.element.restoreText()\r\n            }\r\n        },\r\n        coordsIn: function(x, y) {\r\n            const canvas = this.getCanvas();\r\n            const isHorizontal = this._options.isHorizontal;\r\n            const position = this._options.position;\r\n            const coord = isHorizontal ? y : x;\r\n            if (isHorizontal && (x < canvas.left || x > canvas.width - canvas.right) || !isHorizontal && (y < canvas.top || y > canvas.height - canvas.bottom)) {\r\n                return false\r\n            }\r\n            if (isHorizontal && position === constants.top || !isHorizontal && position === constants.left) {\r\n                return coord < canvas[position]\r\n            }\r\n            return coord > canvas[isHorizontal ? \"height\" : \"width\"] - canvas[position]\r\n        },\r\n        _boundaryTicksVisibility: {\r\n            min: true,\r\n            max: true\r\n        },\r\n        adjust() {\r\n            const seriesData = this._seriesData;\r\n            const viewport = this._series.filter((s => s.isVisible())).reduce(((range, s) => {\r\n                const seriesRange = s.getViewport();\r\n                range.min = isDefined(seriesRange.min) ? range.min < seriesRange.min ? range.min : seriesRange.min : range.min;\r\n                range.max = isDefined(seriesRange.max) ? range.max > seriesRange.max ? range.max : seriesRange.max : range.max;\r\n                if (s.showZero) {\r\n                    range = new Range(range);\r\n                    range.correctValueZeroLevel()\r\n                }\r\n                return range\r\n            }), {});\r\n            if (isDefined(viewport.min) && isDefined(viewport.max)) {\r\n                seriesData.minVisible = viewport.min;\r\n                seriesData.maxVisible = viewport.max\r\n            }\r\n            seriesData.userBreaks = this._getScaleBreaks(this._options, {\r\n                minVisible: seriesData.minVisible,\r\n                maxVisible: seriesData.maxVisible\r\n            }, this._series, this.isArgumentAxis);\r\n            this._translator.updateBusinessRange(this._getViewportRange())\r\n        },\r\n        hasWrap() {\r\n            return this._wrapped\r\n        },\r\n        getAxisPosition() {\r\n            return this._axisPosition\r\n        },\r\n        _getStick: function() {\r\n            return !this._options.valueMarginsEnabled\r\n        },\r\n        _getStripLabelCoords: function(from, to, stripLabelOptions) {\r\n            const orthogonalPositions = this._orthogonalPositions;\r\n            const isHorizontal = this._isHorizontal;\r\n            const horizontalAlignment = stripLabelOptions.horizontalAlignment;\r\n            const verticalAlignment = stripLabelOptions.verticalAlignment;\r\n            let x;\r\n            let y;\r\n            if (isHorizontal) {\r\n                if (horizontalAlignment === CENTER) {\r\n                    x = from + (to - from) / 2\r\n                } else if (horizontalAlignment === LEFT) {\r\n                    x = from\r\n                } else if (horizontalAlignment === RIGHT) {\r\n                    x = to\r\n                }\r\n                y = orthogonalPositions[getStripVerticalAlignmentPosition(verticalAlignment)]\r\n            } else {\r\n                x = orthogonalPositions[getStripHorizontalAlignmentPosition(horizontalAlignment)];\r\n                if (verticalAlignment === TOP) {\r\n                    y = from\r\n                } else if (verticalAlignment === CENTER) {\r\n                    y = to + (from - to) / 2\r\n                } else if (verticalAlignment === BOTTOM) {\r\n                    y = to\r\n                }\r\n            }\r\n            return {\r\n                x: x,\r\n                y: y\r\n            }\r\n        },\r\n        _getTranslatedValue: function(value, offset) {\r\n            let interval;\r\n            if (\"semidiscrete\" === this._options.type) {\r\n                interval = this._options.tickInterval\r\n            }\r\n            const pos1 = this._translator.translate(value, offset, false, interval);\r\n            const pos2 = this._axisPosition;\r\n            const isHorizontal = this._isHorizontal;\r\n            return {\r\n                x: isHorizontal ? pos1 : pos2,\r\n                y: isHorizontal ? pos2 : pos1\r\n            }\r\n        },\r\n        areCoordsOutsideAxis: function(coords) {\r\n            const coord = this._isHorizontal ? coords.x : coords.y;\r\n            const visibleArea = this.getVisibleArea();\r\n            if (coord < visibleArea[0] || coord > visibleArea[1]) {\r\n                return true\r\n            }\r\n            return false\r\n        },\r\n        _getSkippedCategory: function(ticks) {\r\n            let skippedCategory;\r\n            if (this._options.type === constants.discrete && this._tickOffset && 0 !== ticks.length) {\r\n                skippedCategory = ticks[ticks.length - 1]\r\n            }\r\n            return skippedCategory\r\n        },\r\n        _filterBreaks: function(breaks, viewport, breakStyle) {\r\n            const minVisible = viewport.minVisible;\r\n            const maxVisible = viewport.maxVisible;\r\n            const breakSize = breakStyle ? breakStyle.width : 0;\r\n            return breaks.reduce((function(result, currentBreak) {\r\n                let from = currentBreak.from;\r\n                let to = currentBreak.to;\r\n                const lastResult = result[result.length - 1];\r\n                let newBreak;\r\n                if (!isDefined(from) || !isDefined(to)) {\r\n                    return result\r\n                }\r\n                if (from > to) {\r\n                    to = [from, from = to][0]\r\n                }\r\n                if (result.length && from < lastResult.to) {\r\n                    if (to > lastResult.to) {\r\n                        lastResult.to = to > maxVisible ? maxVisible : to;\r\n                        if (lastResult.gapSize) {\r\n                            lastResult.gapSize = void 0;\r\n                            lastResult.cumulativeWidth += breakSize\r\n                        }\r\n                    }\r\n                } else if (from >= minVisible && from < maxVisible || to <= maxVisible && to > minVisible) {\r\n                    from = from >= minVisible ? from : minVisible;\r\n                    to = to <= maxVisible ? to : maxVisible;\r\n                    if (to - from < maxVisible - minVisible) {\r\n                        newBreak = {\r\n                            from: from,\r\n                            to: to,\r\n                            cumulativeWidth: ((null === lastResult || void 0 === lastResult ? void 0 : lastResult.cumulativeWidth) ?? 0) + breakSize\r\n                        };\r\n                        if (currentBreak.gapSize) {\r\n                            newBreak.gapSize = dateUtils.convertMillisecondsToDateUnits(to - from);\r\n                            newBreak.cumulativeWidth = (null === lastResult || void 0 === lastResult ? void 0 : lastResult.cumulativeWidth) ?? 0\r\n                        }\r\n                        result.push(newBreak)\r\n                    }\r\n                }\r\n                return result\r\n            }), [])\r\n        },\r\n        _getScaleBreaks: function(axisOptions, viewport, series, isArgumentAxis) {\r\n            const that = this;\r\n            let breaks = (axisOptions.breaks || []).map((function(b) {\r\n                return {\r\n                    from: that.parser(b.startValue),\r\n                    to: that.parser(b.endValue)\r\n                }\r\n            }));\r\n            if (\"discrete\" !== axisOptions.type && \"datetime\" === axisOptions.dataType && axisOptions.workdaysOnly) {\r\n                breaks = breaks.concat(generateDateBreaks(viewport.minVisible, viewport.maxVisible, axisOptions.workWeek, axisOptions.singleWorkdays, axisOptions.holidays))\r\n            }\r\n            if (!isArgumentAxis && \"discrete\" !== axisOptions.type && \"datetime\" !== axisOptions.dataType && axisOptions.autoBreaksEnabled && 0 !== axisOptions.maxAutoBreakCount) {\r\n                breaks = breaks.concat(generateAutoBreaks(axisOptions, series, viewport))\r\n            }\r\n            return sortingBreaks(breaks)\r\n        },\r\n        _drawBreak: function(translatedEnd, positionFrom, positionTo, width, options, group) {\r\n            const breakStart = translatedEnd - (!this._translator.isInverted() ? width + 1 : 0);\r\n            const attr = {\r\n                \"stroke-width\": 1,\r\n                stroke: options.borderColor,\r\n                sharp: !options.isWaved ? options.isHorizontal ? \"h\" : \"v\" : void 0\r\n            };\r\n            const spaceAttr = {\r\n                stroke: options.color,\r\n                \"stroke-width\": width\r\n            };\r\n            const getPoints = this._isHorizontal ? rotateLine : function(p) {\r\n                return p\r\n            };\r\n            const drawer = getLineDrawer(this._renderer, group, getPoints, positionFrom, breakStart, positionTo, options.isWaved);\r\n            drawer(width / 2, spaceAttr);\r\n            drawer(0, attr);\r\n            drawer(width, attr)\r\n        },\r\n        _createBreakClipRect: function(from, to) {\r\n            const that = this;\r\n            const canvas = that._canvas;\r\n            const clipWidth = to - from;\r\n            let clipRect;\r\n            if (that._isHorizontal) {\r\n                clipRect = that._renderer.clipRect(canvas.left, from, canvas.width, clipWidth)\r\n            } else {\r\n                clipRect = that._renderer.clipRect(from, canvas.top, clipWidth, canvas.height)\r\n            }\r\n            that._breaksElements = that._breaksElements || [];\r\n            that._breaksElements.push(clipRect);\r\n            return clipRect.id\r\n        },\r\n        _createBreaksGroup: function(clipFrom, clipTo) {\r\n            const group = this._renderer.g().attr({\r\n                class: this._axisCssPrefix + \"breaks\",\r\n                \"clip-path\": this._createBreakClipRect(clipFrom, clipTo)\r\n            }).append(this._scaleBreaksGroup);\r\n            this._breaksElements = this._breaksElements || [];\r\n            this._breaksElements.push(group);\r\n            return group\r\n        },\r\n        _disposeBreaksGroup: function() {\r\n            (this._breaksElements || []).forEach((function(clipRect) {\r\n                clipRect.dispose()\r\n            }));\r\n            this._breaksElements = null\r\n        },\r\n        drawScaleBreaks: function(customCanvas) {\r\n            const that = this;\r\n            const options = that._options;\r\n            const breakStyle = options.breakStyle;\r\n            const position = options.position;\r\n            let positionFrom;\r\n            let positionTo;\r\n            const breaks = that._translator.getBusinessRange().breaks || [];\r\n            let additionGroup;\r\n            let additionBreakFrom;\r\n            let additionBreakTo;\r\n            that._disposeBreaksGroup();\r\n            if (!(breaks && breaks.length)) {\r\n                return\r\n            }\r\n            const breakOptions = {\r\n                color: that._options.containerColor,\r\n                borderColor: breakStyle.color,\r\n                isHorizontal: that._isHorizontal,\r\n                isWaved: \"straight\" !== breakStyle.line.toLowerCase()\r\n            };\r\n            if (customCanvas) {\r\n                positionFrom = customCanvas.start;\r\n                positionTo = customCanvas.end\r\n            } else {\r\n                positionFrom = that._orthogonalPositions.start - (options.visible && !that._axisShift && (position === LEFT || position === TOP) ? 3 : 0);\r\n                positionTo = that._orthogonalPositions.end + (options.visible && (position === RIGHT || position === BOTTOM) ? 3 : 0)\r\n            }\r\n            const mainGroup = that._createBreaksGroup(positionFrom, positionTo);\r\n            if (that._axisShift && options.visible) {\r\n                additionBreakFrom = that._axisPosition - that._axisShift - 3;\r\n                additionBreakTo = additionBreakFrom + 6;\r\n                additionGroup = that._createBreaksGroup(additionBreakFrom, additionBreakTo)\r\n            }\r\n            breaks.forEach((function(br) {\r\n                if (!br.gapSize) {\r\n                    const breakCoord = that._getTranslatedCoord(br.to);\r\n                    that._drawBreak(breakCoord, positionFrom, positionTo, breakStyle.width, breakOptions, mainGroup);\r\n                    if (that._axisShift && options.visible) {\r\n                        that._drawBreak(breakCoord, additionBreakFrom, additionBreakTo, breakStyle.width, breakOptions, additionGroup)\r\n                    }\r\n                }\r\n            }))\r\n        },\r\n        _getSpiderCategoryOption: noop,\r\n        shift: function(margins) {\r\n            const options = this._options;\r\n            const isHorizontal = options.isHorizontal;\r\n            const axesSpacing = this.getMultipleAxesSpacing();\r\n            const constantLinesGroups = this._axisConstantLineGroups;\r\n\r\n            function shiftGroup(side, group) {\r\n                const attr = {\r\n                    translateX: 0,\r\n                    translateY: 0\r\n                };\r\n                const shift = margins[side] ? margins[side] + axesSpacing : 0;\r\n                attr[isHorizontal ? \"translateY\" : \"translateX\"] = (side === LEFT || side === TOP ? -1 : 1) * shift;\r\n                (group[side] || group).attr(attr);\r\n                return shift\r\n            }\r\n            this._axisShift = shiftGroup(options.position, this._axisGroup);\r\n            shiftGroup(options.position, this._axisElementsGroup);\r\n            (isHorizontal ? [TOP, BOTTOM] : [LEFT, RIGHT]).forEach((side => {\r\n                shiftGroup(side, constantLinesGroups.above);\r\n                shiftGroup(side, constantLinesGroups.under)\r\n            }))\r\n        },\r\n        getCustomPosition(position) {\r\n            const that = this;\r\n            const orthogonalAxis = that.getOrthogonalAxis();\r\n            const resolvedPosition = position ?? that.getResolvedPositionOption();\r\n            const offset = that.getOptions().offset;\r\n            const orthogonalTranslator = orthogonalAxis.getTranslator();\r\n            const orthogonalAxisType = orthogonalAxis.getOptions().type;\r\n            let validPosition = orthogonalAxis.validateUnit(resolvedPosition);\r\n            let currentPosition;\r\n            if (\"discrete\" === orthogonalAxisType && (!orthogonalTranslator._categories || orthogonalTranslator._categories.indexOf(validPosition) < 0)) {\r\n                validPosition = void 0\r\n            }\r\n            if (that.positionIsBoundary(resolvedPosition)) {\r\n                currentPosition = that.getPredefinedPosition(resolvedPosition)\r\n            } else if (!isDefined(validPosition)) {\r\n                currentPosition = that.getPredefinedPosition(that.getOptions().position)\r\n            } else {\r\n                currentPosition = orthogonalTranslator.to(validPosition, -1)\r\n            }\r\n            if (isFinite(currentPosition) && isFinite(offset)) {\r\n                currentPosition += offset\r\n            }\r\n            return currentPosition\r\n        },\r\n        getCustomBoundaryPosition(position) {\r\n            const that = this;\r\n            const {\r\n                customPosition: customPosition,\r\n                offset: offset\r\n            } = that.getOptions();\r\n            const resolvedPosition = position ?? that.getResolvedPositionOption();\r\n            const orthogonalAxis = that.getOrthogonalAxis();\r\n            const orthogonalTranslator = orthogonalAxis.getTranslator();\r\n            const visibleArea = orthogonalTranslator.getCanvasVisibleArea();\r\n            if (!isDefined(orthogonalAxis._orthogonalPositions) || 0 === orthogonalTranslator.canvasLength) {\r\n                return\r\n            }\r\n            const currentPosition = that.getCustomPosition(resolvedPosition);\r\n            if (!isDefined(currentPosition)) {\r\n                return that.getResolvedBoundaryPosition()\r\n            } else if (isDefined(customPosition)) {\r\n                if (currentPosition <= visibleArea.min) {\r\n                    return that._isHorizontal ? TOP : LEFT\r\n                } else if (currentPosition >= visibleArea.max) {\r\n                    return that._isHorizontal ? BOTTOM : RIGHT\r\n                }\r\n            } else if (isDefined(offset)) {\r\n                if (currentPosition <= that._orthogonalPositions.start) {\r\n                    return that._isHorizontal ? TOP : LEFT\r\n                } else if (currentPosition >= that._orthogonalPositions.end) {\r\n                    return that._isHorizontal ? BOTTOM : RIGHT\r\n                }\r\n            }\r\n            return currentPosition\r\n        },\r\n        getResolvedPositionOption() {\r\n            const options = this.getOptions();\r\n            return options.customPosition ?? options.position\r\n        },\r\n        customPositionIsAvailable() {\r\n            const options = this.getOptions();\r\n            return isDefined(this.getOrthogonalAxis()) && (isDefined(options.customPosition) || isFinite(options.offset))\r\n        },\r\n        hasNonBoundaryPosition() {\r\n            return this.customPositionIsAvailable() && !this.customPositionIsBoundary()\r\n        },\r\n        getResolvedBoundaryPosition() {\r\n            return this.customPositionIsBoundary() ? this._customBoundaryPosition : this.getOptions().position\r\n        },\r\n        customPositionEqualsToPredefined() {\r\n            return this.customPositionIsBoundary() && this._customBoundaryPosition === this.getOptions().position\r\n        },\r\n        customPositionIsBoundary() {\r\n            return this.positionIsBoundary(this._customBoundaryPosition)\r\n        },\r\n        positionIsBoundary: position => [TOP, LEFT, BOTTOM, RIGHT].indexOf(position) >= 0,\r\n        getPredefinedPosition(position) {\r\n            var _this$_orthogonalPosi;\r\n            return null === (_this$_orthogonalPosi = this._orthogonalPositions) || void 0 === _this$_orthogonalPosi ? void 0 : _this$_orthogonalPosi[position === TOP || position === LEFT ? \"start\" : \"end\"]\r\n        },\r\n        resolveOverlappingForCustomPositioning(oppositeAxes) {\r\n            const that = this;\r\n            if (!that.hasNonBoundaryPosition() && !that.customPositionIsBoundary() && !oppositeAxes.some((a => a.hasNonBoundaryPosition()))) {\r\n                return\r\n            }\r\n            const overlappingObj = {\r\n                axes: [],\r\n                ticks: []\r\n            };\r\n            oppositeAxes.filter((orthogonalAxis => orthogonalAxis.pane === that.pane)).forEach((orthogonalAxis => {\r\n                for (let i = 0; i < that._majorTicks.length; i++) {\r\n                    const tick = that._majorTicks[i];\r\n                    const label = tick.label;\r\n                    if (label) {\r\n                        if (overlappingObj.axes.indexOf(orthogonalAxis) < 0 && that._detectElementsOverlapping(label, orthogonalAxis._axisElement)) {\r\n                            overlappingObj.axes.push(orthogonalAxis);\r\n                            that._shiftThroughOrthogonalAxisOverlappedTick(label, orthogonalAxis)\r\n                        }\r\n                        for (let j = 0; j < orthogonalAxis._majorTicks.length; j++) {\r\n                            const oppositeTick = orthogonalAxis._majorTicks[j];\r\n                            const oppositeLabel = oppositeTick.label;\r\n                            if (oppositeLabel && that._detectElementsOverlapping(label, oppositeLabel)) {\r\n                                overlappingObj.ticks.push(tick);\r\n                                that._shiftThroughAxisOverlappedTick(tick);\r\n                                i = that._majorTicks.length;\r\n                                break\r\n                            }\r\n                        }\r\n                    }\r\n                    if (tick.mark && overlappingObj.ticks.indexOf(tick) < 0) {\r\n                        if (that._isHorizontal && tick.mark.attr(\"translateY\")) {\r\n                            tick.mark.attr({\r\n                                translateY: 0\r\n                            })\r\n                        } else if (!that._isHorizontal && tick.mark.attr(\"translateX\")) {\r\n                            tick.mark.attr({\r\n                                translateX: 0\r\n                            })\r\n                        }\r\n                    }\r\n                }\r\n            }))\r\n        },\r\n        _shiftThroughOrthogonalAxisOverlappedTick(label, orthogonalAxis) {\r\n            const labelBBox = label.getBBox();\r\n            const orthogonalAxisPosition = orthogonalAxis.getAxisPosition();\r\n            const orthogonalAxisLabelOptions = orthogonalAxis.getOptions().label;\r\n            const orthogonalAxisLabelPosition = orthogonalAxisLabelOptions.position;\r\n            const orthogonalAxisLabelIndent = orthogonalAxisLabelOptions.indentFromAxis / 2;\r\n            const translateCoordName = this._isHorizontal ? \"translateX\" : \"translateY\";\r\n            const defaultOrthogonalAxisLabelPosition = this._isHorizontal ? LEFT : TOP;\r\n            const translate = label.attr(translateCoordName);\r\n            const labelCoord = (this._isHorizontal ? labelBBox.x : labelBBox.y) + translate;\r\n            const labelSize = this._isHorizontal ? labelBBox.width : labelBBox.height;\r\n            const outsidePart = orthogonalAxisPosition - labelCoord;\r\n            const insidePart = labelCoord + labelSize - orthogonalAxisPosition;\r\n            const attr = {};\r\n            attr[translateCoordName] = translate;\r\n            if (outsidePart > 0 && insidePart > 0) {\r\n                if (insidePart - outsidePart > 1) {\r\n                    attr[translateCoordName] += outsidePart + orthogonalAxisLabelIndent\r\n                } else if (outsidePart - insidePart > 1) {\r\n                    attr[translateCoordName] -= insidePart + orthogonalAxisLabelIndent\r\n                } else {\r\n                    attr[translateCoordName] += orthogonalAxisLabelPosition === defaultOrthogonalAxisLabelPosition ? outsidePart + orthogonalAxisLabelIndent : -(insidePart + orthogonalAxisLabelIndent)\r\n                }\r\n                label.attr(attr)\r\n            }\r\n        },\r\n        _shiftThroughAxisOverlappedTick(tick) {\r\n            var _tick$mark;\r\n            const that = this;\r\n            const label = tick.label;\r\n            if (!label) {\r\n                return\r\n            }\r\n            const labelBBox = label.getBBox();\r\n            const tickMarkBBox = null === (_tick$mark = tick.mark) || void 0 === _tick$mark ? void 0 : _tick$mark.getBBox();\r\n            const axisPosition = that.getAxisPosition();\r\n            const labelOptions = that.getOptions().label;\r\n            const labelIndent = labelOptions.indentFromAxis;\r\n            const labelPosition = labelOptions.position;\r\n            const defaultLabelPosition = that._isHorizontal ? TOP : LEFT;\r\n            const translateCoordName = that._isHorizontal ? \"translateY\" : \"translateX\";\r\n            const translate = label.attr(translateCoordName);\r\n            const labelCoord = (that._isHorizontal ? labelBBox.y : labelBBox.x) + translate;\r\n            const labelSize = that._isHorizontal ? labelBBox.height : labelBBox.width;\r\n            const attr = {};\r\n            attr[translateCoordName] = translate + (labelPosition === defaultLabelPosition ? axisPosition - labelCoord + labelIndent : -(labelCoord - axisPosition + labelSize + labelIndent));\r\n            label.attr(attr);\r\n            if (tick.mark) {\r\n                const markerSize = that._isHorizontal ? tickMarkBBox.height : tickMarkBBox.width;\r\n                const dir = labelPosition === defaultLabelPosition ? 1 : -1;\r\n                attr[translateCoordName] = dir * (markerSize - 1);\r\n                tick.mark.attr(attr)\r\n            }\r\n        },\r\n        _detectElementsOverlapping(element1, element2) {\r\n            if (!element1 || !element2) {\r\n                return false\r\n            }\r\n            const bBox1 = element1.getBBox();\r\n            const x1 = bBox1.x + element1.attr(\"translateX\");\r\n            const y1 = bBox1.y + element1.attr(\"translateY\");\r\n            const bBox2 = element2.getBBox();\r\n            const x2 = bBox2.x + element2.attr(\"translateX\");\r\n            const y2 = bBox2.y + element2.attr(\"translateY\");\r\n            return (x2 >= x1 && x2 <= x1 + bBox1.width || x1 >= x2 && x1 <= x2 + bBox2.width) && (y2 >= y1 && y2 <= y1 + bBox1.height || y1 >= y2 && y1 <= y2 + bBox2.height)\r\n        }\r\n    }\r\n};\r\n\r\nfunction getLineDrawer(renderer, root, rotatePoints, positionFrom, breakStart, positionTo, isWaved) {\r\n    const elementType = isWaved ? \"bezier\" : \"line\";\r\n    const group = renderer.g().append(root);\r\n    return function(offset, attr) {\r\n        renderer.path(rotatePoints(getPoints(positionFrom, breakStart, positionTo, offset, isWaved)), elementType).attr(attr).append(group)\r\n    }\r\n}\r\n\r\nfunction getPoints(positionFrom, breakStart, positionTo, offset, isWaved) {\r\n    if (!isWaved) {\r\n        return [positionFrom, breakStart + offset, positionTo, breakStart + offset]\r\n    }\r\n    breakStart += offset;\r\n    let currentPosition;\r\n    const topPoint = breakStart + 0;\r\n    const centerPoint = breakStart + 2;\r\n    const bottomPoint = breakStart + 4;\r\n    const points = [\r\n        [positionFrom, centerPoint]\r\n    ];\r\n    for (currentPosition = positionFrom; currentPosition < positionTo + 24; currentPosition += 24) {\r\n        points.push([currentPosition + 6, topPoint, currentPosition + 6, topPoint, currentPosition + 12, centerPoint, currentPosition + 18, bottomPoint, currentPosition + 18, bottomPoint, currentPosition + 24, centerPoint])\r\n    }\r\n    return [].concat.apply([], points)\r\n}\r\n\r\nfunction rotateLine(lineCoords) {\r\n    const points = [];\r\n    let i;\r\n    for (i = 0; i < lineCoords.length; i += 2) {\r\n        points.push(lineCoords[i + 1]);\r\n        points.push(lineCoords[i])\r\n    }\r\n    return points\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAGA;AACA;AACA;AAAA;AAGA;AAGA;AAAA;AAGA;AAKA;AAAA;AAGA;;;;;;;;;;AACA,MAAM,kBAAkB,0JAAA,CAAA,UAAS,CAAC,eAAe;AACjD,MAAM,+BAA+B,0JAAA,CAAA,UAAS,CAAC,4BAA4B;AAC3E,MAAM,QAAQ;AACd,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,MAAM,kKAAA,CAAA,UAAS,CAAC,GAAG;AACzB,MAAM,SAAS,kKAAA,CAAA,UAAS,CAAC,MAAM;AAC/B,MAAM,OAAO,kKAAA,CAAA,UAAS,CAAC,IAAI;AAC3B,MAAM,QAAQ,kKAAA,CAAA,UAAS,CAAC,KAAK;AAC7B,MAAM,SAAS,kKAAA,CAAA,UAAS,CAAC,MAAM;AAC/B,MAAM,qBAAqB;AAC3B,MAAM,cAAc;AACpB,MAAM,oBAAoB;AAC1B,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB;IACtB,MAAM,CAAC;IACP,KAAK,CAAC;IACN,OAAO;IACP,QAAQ;IACR,QAAQ,CAAC;AACb;AAEA,SAAS,wBAAwB,gBAAgB,EAAE,YAAY;IAC3D,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW,cAAc;QACzB,eAAe;IACnB;IACA,IAAI,cAAc,cAAc;QAC5B,eAAe;IACnB;IACA,IAAI,gBAAgB,CAAC,aAAa,EAAE;QAChC,IAAK,IAAI,GAAG,IAAI,0JAAA,CAAA,UAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,IAAK;YACrD,mBAAmB,0JAAA,CAAA,UAAS,CAAC,iBAAiB,CAAC,EAAE;YACjD,IAAI,gBAAgB,CAAC,iBAAiB,EAAE;gBACpC,gBAAgB,CAAC,iBAAiB,GAAG;gBACrC,iBAAiB,KAAK;YAC1B;YACA,IAAI,qBAAqB,cAAc;gBACnC;YACJ;QACJ;IACJ;AACJ;AAEA,SAAS,cAAc,MAAM;IACzB,OAAO,OAAO,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;QAC7B,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI;IAC1B;AACJ;AAEA,SAAS,eAAe,GAAG,EAAE,GAAG,EAAE,cAAc;IAC5C,MAAM,UAAU;IAChB,IAAI;IACJ,MAAM,6BAA6B,KAAK;IACxC,MAAM,6BAA6B,KAAK;IACxC,QAAQ,0JAAA,CAAA,UAAS,CAAC,qBAAqB,CAAC,KAAK,KAAK;IAClD,IAAI,MAAM,MAAM,IAAI,UAAU,KAAK,CAAC,EAAE,EAAE;QACpC,QAAQ,MAAM,KAAK,CAAC;IACxB;IACA,OAAO;AACX;AAEA,SAAS,oCAAoC,SAAS;IAClD,IAAI,WAAW;IACf,IAAI,aAAa,WAAW;QACxB,WAAW;IACf;IACA,IAAI,YAAY,WAAW;QACvB,WAAW;IACf;IACA,OAAO;AACX;AAEA,SAAS,kCAAkC,SAAS;IAChD,IAAI,WAAW;IACf,IAAI,aAAa,WAAW;QACxB,WAAW;IACf;IACA,IAAI,aAAa,WAAW;QACxB,WAAW;IACf;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,YAAY;IACnC,IAAI,iBAAiB,gBAAgB;IACrC,IAAI,cAAc,gBAAgB;QAC9B,iBAAiB,gBAAgB;IACrC;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,cAAc;IACpE,IAAI,SAAS;IACb,MAAM,mBAAmB,YAAY,0JAAA,CAAA,UAAS,CAAC,mBAAmB,CAAC,UAAU;IAC7E,IAAI,YAAY,WAAW,cAAc;QACrC,wBAAwB,kBAAkB;QAC1C,SAAS,kJAAA,CAAA,UAAY,CAAC,0BAA0B,CAAC;IACrD;IACA,OAAO;AACX;AAEA,SAAS,WAAW,GAAG,EAAE,KAAK;IAC1B,OAAO,MAAM,MAAM,CAAE,SAAS,SAAS,EAAE,GAAG;QACxC,OAAO,KAAK,WAAW,IAAI;IAC/B,GAAI;AACR;AAEA,SAAS,mBAAmB,IAAI,EAAE,aAAa;IAC3C,gBAAgB,MAAM,GAAG,CAAC;IAC1B,gBAAgB,gBAAgB,OAAO,KAAK,KAAK,gBAAgB,KAAK,gBAAgB;IACtF,MAAM,IAAI,gBAAgB,CAAC,MAAM,EAAE,GAAG,GAAG;IACzC,IAAI,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,KAAK,KAAK,GAAG;QAC3C,OAAO,KAAK,MAAM,GAAG,MAAM,GAAG,CAAC,MAAM,GAAG,CAAC;IAC7C,OAAO;QACH,OAAO,KAAK,KAAK;IACrB;AACJ;AAEA,SAAS,0BAA0B,aAAa;IAC5C,OAAO,cAAc,MAAM,CAAE,SAAS,OAAO,EAAE,OAAO;QAClD,OAAO,KAAK,SAAS,QAAQ,gBAAgB;IACjD,GAAI;AACR;AAEA,SAAS,+CAA+C,aAAa,EAAE,SAAS,EAAE,WAAW;IACzF,OAAO,cAAc,IAAI,CAAE,SAAS,OAAO;QACvC,OAAO,QAAQ,KAAK,CAAC,iBAAiB,KAAK;IAC/C,MAAO,eAAe;AAC1B;AAEA,SAAS,cAAc,IAAI;IACvB,OAAO,MAAM,GAAG,CAAC,KAAK,CAAC,KAAK;AAChC;AAEA,SAAS,eAAe,IAAI;IACxB,OAAO,MAAM,GAAG,CAAC,KAAK,KAAK,GAAG,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM;AACxD;AAEA,SAAS,uBAAuB,MAAM,EAAE,UAAU,EAAE,QAAQ;IACxD,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW;IACf,MAAM,SAAS,EAAE;IACjB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,SAAS,OAAO,MAAM,EAAE,IAAI,QAAQ,IAAK;QACjD,WAAW,MAAM,CAAC,EAAE;QACpB,YAAY,MAAM,CAAC,IAAI,EAAE;QACzB,WAAW,SAAS,UAAU;QAC9B,IAAI,WAAW,OAAO,CAAC,aAAa,GAAG;YACnC,IAAI,CAAC,YAAY,WAAW,SAAS,MAAM,EAAE;gBACzC,WAAW;oBACP,OAAO;oBACP,KAAK;oBACL,QAAQ;gBACZ;YACJ;QACJ,OAAO;YACH,IAAI,YAAY,WAAW,SAAS,MAAM,EAAE;gBACxC,OAAO,IAAI,CAAC;YAChB,OAAO;gBACH,OAAO,IAAI,CAAC;oBACR,OAAO;oBACP,KAAK;oBACL,QAAQ;gBACZ;YACJ;YACA,WAAW;QACf;IACJ;IACA,IAAI,UAAU;QACV,OAAO,IAAI,CAAC;IAChB;IACA,OAAO;AACX;AAEA,SAAS,mBAAmB,IAAI,EAAE,MAAM,EAAE,KAAK;IAC3C,IAAI,EACA,eAAe,aAAa,EAC5B,MAAM,IAAI,EACV,mBAAmB,iBAAiB,EACvC,GAAG;IACJ,IAAI,EACA,YAAY,UAAU,EACtB,YAAY,UAAU,EACzB,GAAG;IACJ,MAAM,SAAS,EAAE;IACjB,MAAM,WAAW,kBAAkB,OAAO,CAAC,KAAK,MAAQ,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,KAAK,iBAAiB,CAAC,KAAK,MAAQ,MAAM;IAC/G,IAAI,eAAe,SAAS,YAAY;IACxC,MAAM,SAAS,OAAO,MAAM,CAAE,CAAC,QAAQ;QACnC,MAAM,SAAS,EAAE,mBAAmB;QACpC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QACtC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;QACtC,OAAO;IACX,GAAI;QACA,EAAE;QACF,EAAE;KACL;IACD,MAAM,kBAAkB,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE,EAAE,IAAI,CAAE,CAAC,GAAG,IAAM,IAAI;IACxE,MAAM,aAAa,MAAM,CAAC,EAAE,CAAC,MAAM,CAAE,CAAA,IAAK,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK;IACjE,IAAI,UAAU,KAAK;IACnB,MAAM,SAAS,uBAAuB,iBAAiB,YAAY,UAAU,MAAM,CAAE,CAAA;QACjF,IAAI,EACA,QAAQ,MAAM,EACjB,GAAG;QACJ,OAAO,CAAC,CAAC;IACb,GAAI,IAAI,CAAE,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;IACvC,MAAM,UAAU,MAAM,GAAG,CAAC,KAAK,CAAC,MAAM,OAAO,GAAG,CAAE,CAAA,IAAK,EAAE,MAAM,KAAM;IACrE,MAAM,qBAAqB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB,MAAM,GAAG,CAAC,mBAAmB,OAAO,MAAM,IAAI,OAAO,MAAM;IACrH,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,IAAK;QACzC,IAAI,MAAM,CAAC,EAAE,CAAC,MAAM,IAAI,SAAS;YAC7B,IAAI,gBAAgB,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE;gBAClC;YACJ;YACA,gBAAgB,MAAM,CAAC,EAAE,CAAC,MAAM;YAChC,IAAI,eAAe,WAAW,eAAe,CAAC,SAAS;gBACnD,OAAO,IAAI,CAAC;oBACR,MAAM,MAAM,CAAC,EAAE,CAAC,KAAK;oBACrB,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG;gBACrB;gBACA,UAAU,KAAK;YACnB;QACJ,OAAO;YACH;QACJ;IACJ;IACA,cAAc;IACd,OAAO;AACX;uCACe;IACX,QAAQ;QACJ,UAAU,SAAS,KAAK,EAAE,aAAa;YACnC,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU;YAC9C,MAAM,OAAO,IAAI,CAAC,aAAa,GAAG,SAAS,GAAG;gBAC1C,OAAO,IAAI,KAAK,GAAG;YACvB,IAAI,SAAS,GAAG;gBACZ,OAAO,IAAI,MAAM;YACrB;YACA,IAAI,iBAAiB,WAAW,MAAM;YACtC,IAAI,eAAe;gBACf,iBAAiB,mBAAmB;oBAChC,OAAO;oBACP,QAAQ,IAAI,CAAC,kBAAkB,CAAC,OAAO;gBAC3C,GAAG;YACP;YACA,OAAO,kKAAA,CAAA,UAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,GAAG,MAAM,KAAK;QAC5F;QACA,oBAAoB,SAAS,KAAK,EAAE,OAAO;YACvC,OAAO,WAAY,SAAS,GAAG;gBAC3B,OAAO,IAAI,MAAM;YACrB,GAAI,SAAS;QACjB;QACA,0BAA0B,SAAS,IAAI,EAAE,WAAW;YAChD,IAAI,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,eAAe,cAAc,WAAW,KAAK,CAAC,IAAI,CAAC,aAAa,EAAE;gBACtG,OAAO,kKAAA,CAAA,UAAS,CAAC,uBAAuB,CAAC;YAC7C;YACA,OAAO;QACX;QACA,sBAAsB,SAAS,IAAI;YAC/B,OAAO,IAAI,CAAC,aAAa,GAAG,OAAO;QACvC;QACA,mBAAmB;YACf,OAAO,IAAI,CAAC,eAAe;QAC/B;QACA,gBAAgB,SAAS,QAAQ;YAC7B,OAAO,IAAI,CAAC,aAAa,GAAG,WAAW,MAAM;QACjD;QACA,oBAAoB;YAChB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE;QACnC;QACA,4BAA4B;YACxB,MAAM,YAAY,IAAI,CAAC,aAAa;YACpC,MAAM,SAAS,IAAI,CAAC,kBAAkB;YACtC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACnB,QAAQ,IAAI,CAAC,aAAa,GAAG;oBAAC,OAAO,KAAK;oBAAE;oBAAW,OAAO,GAAG;oBAAE;iBAAU,GAAG;oBAAC;oBAAW,OAAO,KAAK;oBAAE;oBAAW,OAAO,GAAG;iBAAC;YACpI;QACJ;QACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;YACvC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO;QAC7C;QACA;YACI,MAAM,OAAO,IAAI;YACjB,IAAI,KAAK,yBAAyB,IAAI;gBAClC,KAAK,uBAAuB,GAAG,KAAK,yBAAyB;YACjE;YACA,IAAI,CAAC,KAAK,yBAAyB,MAAM,KAAK,wBAAwB,IAAI;gBACtE,KAAK,aAAa,GAAG,KAAK,qBAAqB,CAAC,KAAK,2BAA2B;YACpF,OAAO;gBACH,KAAK,aAAa,GAAG,KAAK,iBAAiB;YAC/C;QACJ;QACA,oBAAmB,MAAM,EAAE,MAAM,EAAE,WAAW;YAC1C,MAAM,eAAe,IAAI,CAAC,aAAa;YACvC,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,eAAe;YACrD,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,QAAQ;YAClD,IAAI;YACJ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;gBAC5B,iBAAiB,iBAAiB,CAAC,gBAAgB,GAAG;YAC1D,OAAO;gBACH,IAAI,QAAQ,YAAY,KAAK,IAAI;gBACjC,IAAI,CAAC,gBAAgB,kBAAkB,QAAQ,gBAAgB,kBAAkB,QAAQ;oBACrF,QAAQ,CAAC;gBACb;gBACA,iBAAiB,QAAQ,IAAI,CAAC,yBAAyB,CAAC;YAC5D;YACA,OAAO;gBAAC,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,cAAc;gBAAG,OAAO,CAAC,GAAG,CAAC,eAAe,iBAAiB,CAAC;gBAAG,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,iBAAiB,MAAM;gBAAG,OAAO,CAAC,GAAG,CAAC,eAAe,iBAAiB,SAAS,CAAC;aAAE;QAC7N;QACA,2BAA0B,MAAM;YAC5B,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK;YACjC,MAAM,WAAW,IAAI,CAAC,2BAA2B;YACjD,OAAO,SAAS,MAAM,IAAI,QAAQ,MAAM,KAAK,CAAC,aAAa,QAAQ,aAAa,GAAG,KAAK,QAAQ,MAAM,KAAK,CAAC,aAAa,SAAS,aAAa,MAAM,KAAK,CAAC,IAAI,CAAC,sBAAsB,KAAK,KAAK,KAAK,CAAC,CAAC,SAAS,KAAK,CAAC,KAAK,KAAK,CAAC,SAAS,KAAK,CAAC,SAAS,IAAI,CAAC,QAAQ,MAAM,IAAI,IAAI,aAAa,UAAU,aAAa,QAAQ,CAAC,IAAI,CAAC;QAC1U;QACA,iBAAiB;YACb,MAAM,aAAa,IAAI,CAAC,aAAa;YACrC,IAAI,IAAI,IAAI,CAAC,aAAa;YAC1B,IAAI,IAAI,IAAI,CAAC,aAAa;YAC1B,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,SAAS;YAC3C,MAAM,SAAS,IAAI,CAAC,kBAAkB;YACtC,MAAM,iBAAiB,cAAc,IAAI,CAAC,QAAQ,CAAC,QAAQ,KAAK;YAChE,MAAM,cAAc,iBAAiB,OAAO,KAAK,GAAG,OAAO,GAAG;YAC9D,MAAM,YAAY,iBAAiB,OAAO,GAAG,GAAG,OAAO,KAAK;YAC5D,MAAM,QAAQ,UAAU,OAAO,cAAc,UAAU,QAAQ,YAAY,OAAO,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,OAAO,KAAK,IAAI;YACxH,IAAI,YAAY;gBACZ,IAAI;YACR,OAAO;gBACH,IAAI;YACR;YACA,OAAO;gBACH,GAAG;gBACH,GAAG;YACP;QACJ;QACA,gBAAgB,SAAS,KAAK,EAAE,MAAM;YAClC,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM,eAAe,QAAQ,KAAK;YAClC,MAAM,QAAQ;gBACV,SAAS,aAAa,OAAO;gBAC7B,OAAO,aAAa,SAAS;gBAC7B,OAAO,aAAa,QAAQ;YAChC;YACA,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,OAAO;gBAC9B;YACJ;YACA,SAAS,UAAU,IAAI,CAAC,eAAe;YACvC,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;gBACrB,MAAM,MAAM,GAAG,QAAQ,QAAQ,KAAK,OAAO,MAAM;YACrD;YACA,MAAM,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa,IAAI,GAAG,IAAI,CAAC,OAAO,MAAM,CAAC;YACpI,IAAI,CAAC,mBAAmB,CAAC;YACzB,OAAO;QACX;QACA,oBAAoB;YAChB,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,eAAe;QAChE;QACA,YAAY;YACR,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;YACtD,IAAI,OAAO;gBACP,IAAI,CAAC,MAAM,GAAG;oBACV,SAAS;gBACb;YACJ;QACJ;QACA,eAAe;YACX,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,YAAY,EAAE;oBAC/C,IAAI,CAAC,MAAM,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI;gBAC/C;gBACA,IAAI,CAAC,MAAM,CAAC,IAAI,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,OAAO;YAClD;QACJ;QACA,iBAAiB,SAAS,IAAI,EAAE,OAAO,EAAE,KAAK;YAC1C,MAAM,OAAO,IAAI;YACjB,MAAM,gBAAgB,KAAK,QAAQ,CAAC,MAAM;YAC1C,MAAM,SAAS,KAAK,WAAW,CAAC,gBAAgB,GAAG,MAAM;YACzD,MAAM,aAAa,cAAc,KAAK,GAAG,cAAc,cAAc;YACrE,IAAI;YACJ,IAAI,SAAS,QAAQ,CAAC,EAAE;gBACpB;YACJ;YACA,IAAI,CAAC,QAAQ,YAAY,EAAE;gBACvB,cAAc,KAAK,SAAS,CAAC,IAAI,CAAC;oBAAC,QAAQ,CAAC;oBAAE,QAAQ,CAAC;oBAAE,QAAQ,CAAC;oBAAE,QAAQ,CAAC,GAAG,cAAc,eAAe;iBAAC,EAAE,QAAQ,IAAI,CAAC;oBACzH,gBAAgB,cAAc,KAAK;oBACnC,QAAQ,cAAc,KAAK;oBAC3B,kBAAkB,cAAc,OAAO;oBACvC,OAAO;gBACX,GAAG,MAAM,CAAC,KAAK,kBAAkB;YACrC;YACA,MAAM,OAAO,OAAO,KAAK,WAAW,CAAC,MAAM,QAAQ,YAAY,EAAE;YACjE,OAAO;gBACH,MAAM;gBACN,GAAG,QAAQ,CAAC;gBACZ,GAAG,QAAQ,CAAC;gBACZ,SAAS,QAAQ,YAAY;gBAC7B,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,cAAc,KAAK,CAAC,IAAI,GAAG,MAAM,CAAC,KAAK,kBAAkB;gBACrI,MAAM;gBACN;oBACI,OAAO,IAAI,CAAC,KAAK;gBACrB;gBACA,QAAQ;oBACJ,OAAO,IAAI,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,SAAS,CAAC,KAAK;gBAC1E;gBACA,UAAU;oBACN,IAAI,CAAC,KAAK,GAAG;gBACjB;gBACA,WAAW;oBACP,IAAI,CAAC,KAAK,CAAC,OAAO;oBAClB,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,CAAC,KAAK,GAAG;gBACjB;gBACA,MAAM;oBACF,IAAI,aAAa;wBACb,YAAY,OAAO;wBACnB,cAAc;oBAClB;oBACA,IAAI,CAAC,KAAK,CAAC,OAAO;oBAClB,IAAI,CAAC,KAAK,GAAG;oBACb,IAAI,CAAC,MAAM,GAAG;gBAClB;YACJ;QACJ;QACA,kBAAkB;YACd,MAAM,OAAO,IAAI;YACjB,MAAM,UAAU,KAAK,QAAQ;YAC7B,MAAM,aAAa,KAAK,WAAW;YACnC,MAAM,WAAW,KAAK,iBAAiB;YACvC,MAAM,WAAW,SAAS,UAAU;YACpC,IAAI,cAAc,EAAE;YACpB,IAAI;YAEJ,SAAS,KAAK,UAAU,EAAE,MAAM,EAAE,YAAY;gBAC1C,OAAO,KAAK,eAAe,CAAC,YAAY;oBACpC,GAAG,WAAW,SAAS,CAAC;oBACxB,GAAG;oBACH,cAAc,KAAK,sBAAsB,CAAC;oBAC1C,cAAc;gBAClB,GAAG;YACP;YACA,IAAI,SAAS,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC,OAAO,IAAI,eAAe,QAAQ,YAAY,IAAI,eAAe,QAAQ,IAAI,IAAI,KAAK,WAAW,CAAC,MAAM,IAAI,GAAG;gBACrJ,OAAO,EAAE;YACb;YACA,MAAM,iBAAiB,KAAK,aAAa,GAAG,QAAQ,MAAM,CAAC,SAAS;YACpE,MAAM,eAAe,0JAAA,CAAA,UAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,aAAa;YACrE,MAAM,iBAAiB,kBAAkB;YACzC,MAAM,cAAc,eAAe,UAAU,SAAS,UAAU,EAAE;YAClE,IAAI,YAAY,MAAM,GAAG,KAAK,MAAM,YAAY,MAAM,IAAI,WAAW,WAAW,CAAC,EAAE,EAAE;gBACjF,cAAc,YAAY,MAAM,CAAE,SAAS,OAAO,EAAE,OAAO,EAAE,CAAC,EAAE,KAAK;oBACjE,MAAM,SAAS,KAAK,SAAS,gBAAgB,SAAS,KAAK,CAAC,IAAI,EAAE,IAAI,WAAW,WAAW,UAAU,cAAc;oBACpH,UAAU,QAAQ,IAAI,CAAC;oBACvB,OAAO;gBACX,GAAI,EAAE;gBACN,IAAI,WAAW,WAAW,CAAC,EAAE,EAAE;oBAC3B,aAAa,KAAK,UAAU,gBAAgB,UAAU,WAAW,CAAC,EAAE,EAAE,cAAc,iBAAiB;oBACrG,cAAc,YAAY,OAAO,CAAC;gBACtC;YACJ;YACA,OAAO;QACX;QACA,oBAAoB,SAAS,MAAM;YAC/B,SAAS,UAAU;YACnB,MAAM,OAAO,IAAI;YACjB,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,MAAM;YAC1C,MAAM,aAAa,cAAc,KAAK,GAAG,cAAc,cAAc;YACrE,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,MAAM;YACzD,MAAM,SAAS,KAAK,kBAAkB;YACtC,MAAM,cAAc,IAAI,CAAC,YAAY;YACrC,IAAI,CAAC,YAAY,MAAM,EAAE;gBACrB,OAAO;YACX;YACA,IAAI,WAAW,CAAC,EAAE,CAAC,OAAO,EAAE;gBACxB,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,QAAQ,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,GAAG;oBACrE,WAAW,CAAC,EAAE,CAAC,SAAS;gBAC5B;YACJ;YACA,IAAI;YACJ,YAAY,OAAO,CAAE,SAAS,MAAM,EAAE,CAAC,EAAE,OAAO;gBAC5C,IAAI,OAAO,OAAO,EAAE;oBAChB;gBACJ;gBACA,IAAI,SAAS,OAAO,MAAM,KAAK,OAAO,GAAG,GAAG,OAAO,MAAM,KAAK,OAAO,GAAG,EAAE;oBACtE,OAAO,SAAS;gBACpB,OAAO,IAAI,KAAK,qBAAqB,CAAC,QAAQ,QAAQ,iBAAiB;oBACnE,iBAAiB;gBACrB,OAAO;oBACH,OAAO,IAAI;gBACf;YACJ;YACA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAE,SAAS,MAAM;gBACtC,IAAI,OAAO,KAAK,EAAE;oBACd,MAAM,YAAY,OAAO,SAAS;oBAClC,MAAM,KAAK,OAAO,CAAC,GAAG,cAAc,aAAa,GAAG,UAAU,CAAC;oBAC/D,OAAO,KAAK,CAAC,IAAI,CAAC;wBACd,YAAY,SAAS,OAAO,CAAC,GAAG,aAAa,UAAU,CAAC,GAAG,UAAU,KAAK,GAAG,OAAO,CAAC,GAAG,aAAa,UAAU,CAAC;wBAChH,YAAY,KAAK;oBACrB;gBACJ;gBACA,IAAI,OAAO,IAAI,EAAE;oBACb,OAAO,IAAI,CAAC,IAAI,CAAC;wBACb,YAAY;oBAChB;gBACJ;YACJ;YACA,KAAK,0BAA0B,CAAC;YAChC,OAAO,SAAS,cAAc,SAAS,GAAG,cAAc,eAAe;QAC3E;QACA,uBAAuB,SAAS,MAAM,EAAE,UAAU,EAAE,cAAc;YAC9D,IAAI,KAAK,MAAM,gBAAgB;gBAC3B,OAAO;YACX;YACA,OAAO,SAAS,WAAW,CAAC,GAAG,eAAe,MAAM,KAAK,WAAW,CAAC,GAAG,eAAe,MAAM;QACjG;QACA,4BAA4B,SAAS,MAAM;YACvC,MAAM,kBAAkB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,eAAe;YAC5D,MAAM,WAAW,IAAI,CAAC,SAAS;YAC/B,MAAM,gBAAgB,IAAI,CAAC,WAAW,CAAC,gBAAgB;YACvD,MAAM,SAAS,IAAI,CAAC,kBAAkB;YACtC,MAAM,QAAQ,IAAI,CAAC,kBAAkB;YACrC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,CAAE,SAAS,MAAM;gBAC5D,OAAO,CAAC,OAAO,MAAM;YACzB,GAAI,GAAG,CAAE,SAAS,MAAM,EAAE,CAAC,EAAE,OAAO;gBAChC,MAAM,aAAa,OAAO,CAAC,IAAI,EAAE,IAAI;oBACjC,GAAG,OAAO,GAAG;oBACb,MAAM,cAAc,GAAG;gBAC3B;gBACA,MAAM,IAAI,OAAO,CAAC;gBAClB,MAAM,IAAI,OAAO,CAAC,GAAG;gBACrB,MAAM,gBAAgB,SAAS,IAAI,CAAC;oBAAC;oBAAG;oBAAG;oBAAG,IAAI;oBAAiB,WAAW,CAAC;oBAAE,IAAI;oBAAiB,WAAW,CAAC;oBAAE;oBAAG;oBAAG;iBAAE,EAAE,QAAQ,IAAI,CAAC;oBACvI,gBAAgB;oBAChB,QAAQ;oBACR,MAAM;oBACN,SAAS;gBACb,GAAG,MAAM,CAAC;gBACV,cAAc,IAAI,CAAC,SAAS;oBACxB,YAAY,OAAO,IAAI;oBACvB,UAAU,WAAW,IAAI;gBAC7B;gBACA,IAAI,OAAO,KAAK,EAAE;oBACd,cAAc,QAAQ,CAAC,OAAO,KAAK;gBACvC;gBACA,OAAO;YACX;QACJ;QACA,wBAAwB,SAAS,YAAY;YACzC,MAAM,OAAO,IAAI;YACjB,IAAI,qBAAqB,KAAK,mBAAmB;YACjD,IAAI,CAAC,oBAAoB;gBACrB,KAAK,mBAAmB,GAAG,qBAAqB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,KAAK,QAAQ,CAAC,MAAM,CAAC,KAAK;YAC/F;YACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,QAAQ,CAAC,MAAM,CAAC,KAAK,CAAC,MAAM,GAAG;gBAC/C,mBAAmB,MAAM,GAAG;YAChC;YACA,OAAO;QACX;QACA,2BAA2B,SAAS,aAAa;YAC7C,MAAM,OAAO,IAAI;YACjB,MAAM,eAAe,KAAK,QAAQ,CAAC,QAAQ;YAC3C,MAAM,SAAS,KAAK,SAAS;YAC7B,MAAM,aAAa,OAAO,IAAI;YAC9B,MAAM,cAAc,OAAO,KAAK,GAAG,OAAO,KAAK;YAC/C,MAAM,YAAY,OAAO,GAAG;YAC5B,MAAM,eAAe,OAAO,MAAM,GAAG,OAAO,MAAM;YAClD,MAAM,iBAAiB,YAAY,CAAC,eAAe,SAAS,IAAI;YAChE,MAAM,mBAAmB,aAAa,CAAC,cAAc,UAAU,IAAI;YACnE,IAAI,WAAW;YACf,cAAc,OAAO,CAAE,SAAS,IAAI;gBAChC,MAAM,eAAe,KAAK,aAAa;gBACvC,MAAM,eAAe,KAAK,OAAO;gBACjC,MAAM,mBAAmB,aAAa,gBAAgB;gBACtD,MAAM,mBAAmB,aAAa,gBAAgB;gBACtD,MAAM,eAAe,aAAa,KAAK;gBACvC,MAAM,yBAAyB,aAAa,iBAAiB;gBAC7D,MAAM,2BAA2B,aAAa,mBAAmB;gBACjE,MAAM,gBAAgB,aAAa,aAAa,QAAQ;gBACxD,MAAM,QAAQ,KAAK,KAAK;gBACxB,MAAM,MAAM,KAAK,SAAS;gBAC1B,IAAI;gBACJ,IAAI;gBACJ,IAAI,SAAS,SAAS,IAAI,OAAO,EAAE;oBAC/B;gBACJ;gBACA,IAAI,cAAc;oBACd,IAAI,eAAe;wBACf,IAAI,6BAA6B,MAAM;4BACnC,aAAa,KAAK,KAAK,GAAG,mBAAmB,IAAI,CAAC,GAAG,IAAI,KAAK;wBAClE,OAAO;4BACH,aAAa,KAAK,KAAK,GAAG,mBAAmB,IAAI,CAAC;wBACtD;wBACA,OAAQ;4BACJ,KAAK;gCACD,aAAa,iBAAiB,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG;gCACnD;4BACJ,KAAK;gCACD,aAAa,eAAe,mBAAmB,IAAI,CAAC,GAAG,IAAI,MAAM;gCACjE;4BACJ;gCACI,aAAa,YAAY,mBAAmB,IAAI,CAAC;wBACzD;oBACJ,OAAO;wBACH,IAAI,iBAAiB,wBAAwB;4BACzC,WAAW,KAAK,UAAU,IAAI,MAAM,GAAG;wBAC3C;wBACA,aAAa,KAAK,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG;wBAC9C,IAAI,2BAA2B,QAAQ;4BACnC,aAAa,eAAe,mBAAmB,IAAI,CAAC;wBACxD,OAAO;4BACH,aAAa,YAAY,mBAAmB,IAAI,CAAC,GAAG,IAAI,MAAM;wBAClE;oBACJ;gBACJ,OAAO,IAAI,eAAe;oBACtB,IAAI,2BAA2B,QAAQ;wBACnC,aAAa,KAAK,KAAK,GAAG,mBAAmB,IAAI,CAAC;oBACtD,OAAO;wBACH,aAAa,KAAK,KAAK,GAAG,mBAAmB,IAAI,CAAC,GAAG,IAAI,MAAM;oBACnE;oBACA,OAAQ;wBACJ,KAAK;4BACD,aAAa,mBAAmB,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG;4BACpD;wBACJ,KAAK;4BACD,aAAa,cAAc,mBAAmB,IAAI,CAAC,GAAG,IAAI,KAAK;4BAC/D;wBACJ;4BACI,aAAa,aAAa,mBAAmB,IAAI,CAAC;oBAC1D;gBACJ,OAAO;oBACH,IAAI,iBAAiB,0BAA0B;wBAC3C,WAAW,KAAK,UAAU,IAAI,KAAK,GAAG;oBAC1C;oBACA,aAAa,KAAK,KAAK,GAAG,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG;oBAC/C,IAAI,6BAA6B,OAAO;wBACpC,aAAa,cAAc,mBAAmB,IAAI,CAAC;oBACvD,OAAO;wBACH,aAAa,aAAa,mBAAmB,IAAI,CAAC,GAAG,IAAI,KAAK;oBAClE;gBACJ;gBACA,MAAM,IAAI,CAAC;oBACP,YAAY;oBACZ,YAAY;gBAChB;YACJ;YACA,OAAO;QACX;QACA,iCAAiC,SAAS,aAAa;YACnD,MAAM,OAAO,IAAI;YACjB,MAAM,WAAW,IAAI,CAAC,SAAS;YAC/B,MAAM,QAAQ,SAAS,CAAC;YACxB,cAAc,OAAO,CAAE,SAAS,OAAO;gBACnC,KAAK,0BAA0B,CAAC,QAAQ,KAAK,CAAC,IAAI,EAAE,GAAG,GAAG,QAAQ,KAAK,EAAE,OAAO,IAAI,CAAC;oBACjF,OAAO;gBACX;YACJ;YACA,OAAO,MAAM,MAAM,CAAC,SAAS,IAAI;QACrC;QACA,sBAAsB,SAAS,IAAI,EAAE,YAAY;YAC7C,IAAI,SAAS,KAAK,MAAM;YACxB,MAAM,cAAc,aAAa,WAAW;YAC5C,IAAI,cAAc,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,cAAc,IAAI,CAAC,wBAAwB,CAAC,aAAa,mBAAmB,EAAE,cAAc;gBACpJ,SAAS,IAAI,SAAS,aAAa,iBAAiB;YACxD;YACA,IAAI,aAAa,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,aAAa,IAAI,CAAC,wBAAwB,CAAC,aAAa,mBAAmB,EAAE,cAAc;gBAClJ,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,aAAa,aAAa;gBACtD,SAAS,SAAS,OAAO,GAAG,GAAG,KAAK,KAAK,GAAG,OAAO,GAAG;YAC1D;YACA,OAAO,UAAU,CAAC,SAAS,aAAa,cAAc,IAAI,CAAC,KAAK;QACpE;QACA,iBAAiB,SAAS,MAAM;YAC5B,IAAI,CAAC,YAAY,CAAC;YAClB,MAAM,EACF,UAAU,QAAQ,EAClB,iBAAiB,eAAe,EACnC,GAAG,IAAI,CAAC,QAAQ;YACjB,MAAM,QAAQ,IAAI,CAAC,iBAAiB;YACpC,MAAM,YAAY,IAAI,CAAC,0BAA0B,CAAC;YAClD,MAAM,QAAQ,UAAU,KAAK;YAC7B,MAAM,eAAe,UAAU,YAAY;YAC3C,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM,sBAAsB,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,YAAY,CAAC,OAAO,EAAG,GAAG,CAAE,CAAA,IAAK,EAAE,OAAO;YAChH,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,IAAI;YACvC,MAAM,iBAAiB,QAAQ,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,OAAO,MAAM,MAAM,MAAM;YAChF,MAAM,aAAa,kBAAkB,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,EAAE,QAAQ,KAAK,EAAE,KAAK,GAAG,KAAK,GAAG,cAAc;YAC5H,MAAM,eAAe,kBAAkB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG,GAAG,CAAC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC;YACtI,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC,aAAa;gBAClD,GAAG;gBACH,GAAG;YACP;YACA,MAAM,6BAA6B,IAAI,CAAC,+BAA+B,CAAC;YACxE,MAAM,WAAW,CAAC,QAAQ,KAAK,CAAC,QAAQ,IAAI,gBAAgB,aAAa,OAAO,MAAM;gBAClF,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,QAAQ;YACZ;YACA,MAAM,WAAW,gBAAgB,aAAa,OAAO,MAAM;gBACvD,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,QAAQ;YACZ;YACA,MAAM,mBAAmB,2BAA2B,OAAO;YAC3D,MAAM,cAAc,SAAS,MAAM,GAAG,SAAS,MAAM,GAAG,QAAQ,KAAK,CAAC,MAAM,GAAG;YAC/E,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC,UAAU,QAAQ,KAAK;YACrE,MAAM,sBAAsB,iBAAiB,MAAM,GAAG,iBAAiB,MAAM,GAAG,0BAA0B,uBAAuB;YACjI,MAAM,SAAS,cAAc;YAC7B,MAAM,UAAU;gBACZ,MAAM,KAAK,cAAc,WAAW,cAAc;gBAClD,OAAO,KAAK,eAAe,WAAW,eAAe;gBACrD,KAAK,CAAC,UAAU,QAAQ,QAAQ,GAAG,SAAS,CAAC,IAAI,+CAA+C,qBAAqB,OAAO;gBAC5H,QAAQ,CAAC,UAAU,QAAQ,QAAQ,GAAG,SAAS,CAAC,IAAI,+CAA+C,qBAAqB,UAAU;YACtI;YACA,IAAI,iBAAiB;gBACjB,OAAO,CAAC,SAAS,GAAG;YACxB;YACA,gBAAgB,aAAa,MAAM;YACnC,gBAAgB,aAAa,MAAM;YACnC,8BAA8B,2BAA2B,MAAM;YAC/D,OAAO;QACX;QACA,mCAAmC,SAAS,YAAY;YACpD,MAAM,WAAW,aAAa,QAAQ;YACtC,IAAI,oBAAoB,CAAC,aAAa,iBAAiB,IAAI,EAAE,EAAE,WAAW;YAC1E,IAAI,sBAAsB,CAAC,aAAa,mBAAmB,IAAI,EAAE,EAAE,WAAW;YAC9E,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,cAAc,UAAU;oBACxB,oBAAoB,sBAAsB,SAAS,SAAS;oBAC5D,sBAAsB;gBAC1B,OAAO;oBACH,oBAAoB,sBAAsB,SAAS,SAAS,sBAAsB,SAAS,SAAS;oBACpG,sBAAsB,wBAAwB,OAAO,OAAO;gBAChE;YACJ,OAAO,IAAI,cAAc,UAAU;gBAC/B,oBAAoB;gBACpB,sBAAsB,wBAAwB,OAAO,OAAO;YAChE,OAAO;gBACH,oBAAoB,sBAAsB,SAAS,SAAS;gBAC5D,sBAAsB,wBAAwB,QAAQ,QAAQ,wBAAwB,SAAS,SAAS;YAC5G;YACA,aAAa,iBAAiB,GAAG;YACjC,aAAa,mBAAmB,GAAG;QACvC;QACA,8BAA8B,SAAS,KAAK,EAAE,gBAAgB;YAC1D,MAAM,OAAO,IAAI;YACjB,IAAI,IAAI;YACR,IAAI,IAAI;YACR,IAAI,KAAK,aAAa,EAAE;gBACpB,IAAI,KAAK,oBAAoB,CAAC,UAAU,iBAAiB,iBAAiB,GAAG,UAAU,MAAM;YACjG,OAAO;gBACH,IAAI,KAAK,oBAAoB,CAAC,YAAY,iBAAiB,mBAAmB,GAAG,QAAQ,QAAQ;YACrG;YACA,OAAO;gBACH,GAAG;gBACH,GAAG;YACP;QACJ;QACA,8BAA8B,SAAS,KAAK;YACxC,MAAM,eAAe,MAAM,OAAO;YAClC,MAAM,mBAAmB,aAAa,gBAAgB;YACtD,MAAM,mBAAmB,aAAa,gBAAgB;YACtD,MAAM,sBAAsB,aAAa,KAAK,CAAC,mBAAmB;YAClE,MAAM,oBAAoB,aAAa,KAAK,CAAC,iBAAiB;YAC9D,MAAM,MAAM,MAAM,SAAS;YAC3B,MAAM,cAAc,IAAI,MAAM;YAC9B,MAAM,aAAa,IAAI,KAAK;YAC5B,MAAM,cAAc,MAAM,WAAW;YACrC,IAAI,IAAI,YAAY,CAAC,GAAG,IAAI,CAAC;YAC7B,IAAI,IAAI,YAAY,CAAC,GAAG,IAAI,CAAC;YAC7B,IAAI,sBAAsB,KAAK;gBAC3B,KAAK;YACT,OAAO,IAAI,sBAAsB,QAAQ;gBACrC,KAAK,cAAc;YACvB,OAAO,IAAI,sBAAsB,QAAQ;gBACrC,KAAK,mBAAmB;YAC5B;YACA,IAAI,wBAAwB,MAAM;gBAC9B,KAAK;YACT,OAAO,IAAI,wBAAwB,QAAQ;gBACvC,KAAK,aAAa;YACtB,OAAO,IAAI,wBAAwB,OAAO;gBACtC,KAAK,mBAAmB;YAC5B;YACA,OAAO;gBACH,YAAY;gBACZ,YAAY;YAChB;QACJ;QACA,cAAc,SAAS,MAAM;YACzB,SAAS,UAAU;YACnB,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;gBACd;YACJ;YACA,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM,WAAW,QAAQ,QAAQ;YACjC,MAAM,SAAS,QAAQ,KAAK,CAAC,MAAM;YACnC,MAAM,QAAQ,IAAI,CAAC,MAAM;YACzB,MAAM,WAAW,MAAM,IAAI;YAC3B,MAAM,IAAI,SAAS,CAAC;YACpB,MAAM,IAAI,SAAS,CAAC;YACpB,MAAM,QAAQ,SAAS,KAAK;YAC5B,MAAM,SAAS,SAAS,MAAM;YAC9B,MAAM,eAAe,IAAI,CAAC,aAAa;YACvC,MAAM,UAAU,eAAe,SAAS;YACxC,MAAM,UAAU,eAAe,SAAS;YACxC,MAAM,SAAS,CAAC;YAChB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,aAAa,KAAK;oBAClB,OAAO,UAAU,GAAG,UAAU,CAAC,IAAI,MAAM;gBAC7C,OAAO;oBACH,OAAO,UAAU,GAAG,UAAU;gBAClC;YACJ,OAAO,IAAI,aAAa,MAAM;gBAC1B,OAAO,UAAU,GAAG,UAAU,CAAC,IAAI,KAAK;YAC5C,OAAO;gBACH,OAAO,UAAU,GAAG,UAAU;YAClC;YACA,MAAM,OAAO,CAAC,IAAI,CAAC;QACvB;QACA,qBAAqB,SAAS,YAAY;YACtC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,cAAc;gBAC/B;YACJ;YACA,MAAM,eAAe,IAAI,CAAC,eAAe;YACzC,MAAM,QAAQ,eAAe;gBACzB,MAAM,aAAa,OAAO;gBAC1B,SAAS;YACb,IAAI,IAAI,CAAC,MAAM;YACf,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,KAAK;YACxC,MAAM,WAAW,MAAM,IAAI;YAC3B,IAAI,CAAC,IAAI,CAAC,aAAa,GAAG,SAAS,KAAK,GAAG,SAAS,MAAM,IAAI,cAAc;gBACxE,MAAM,OAAO,CAAC,UAAU,CAAC,cAAc,KAAK,GAAG;oBAC3C,UAAU,aAAa,QAAQ,IAAI;oBACnC,cAAc,aAAa,YAAY,IAAI;gBAC/C;gBACA,IAAI,CAAC,QAAQ,GAAG,aAAa,QAAQ,IAAI,WAAW,aAAa,QAAQ;YAC7E,OAAO;gBACH,MAAM,uBAAuB,MAAM,YAAY,IAAI,eAAe,CAAC,IAAI,CAAC,aAAa,GAAG,MAAM,YAAY,CAAC,KAAK,GAAG,MAAM,YAAY,CAAC,MAAM;gBAC5I,CAAC,IAAI,CAAC,QAAQ,IAAI,wBAAwB,MAAM,OAAO,CAAC,WAAW;YACvE;QACJ;QACA,UAAU,SAAS,CAAC,EAAE,CAAC;YACnB,MAAM,SAAS,IAAI,CAAC,SAAS;YAC7B,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC/C,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACvC,MAAM,QAAQ,eAAe,IAAI;YACjC,IAAI,gBAAgB,CAAC,IAAI,OAAO,IAAI,IAAI,IAAI,OAAO,KAAK,GAAG,OAAO,KAAK,KAAK,CAAC,gBAAgB,CAAC,IAAI,OAAO,GAAG,IAAI,IAAI,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG;gBAChJ,OAAO;YACX;YACA,IAAI,gBAAgB,aAAa,kKAAA,CAAA,UAAS,CAAC,GAAG,IAAI,CAAC,gBAAgB,aAAa,kKAAA,CAAA,UAAS,CAAC,IAAI,EAAE;gBAC5F,OAAO,QAAQ,MAAM,CAAC,SAAS;YACnC;YACA,OAAO,QAAQ,MAAM,CAAC,eAAe,WAAW,QAAQ,GAAG,MAAM,CAAC,SAAS;QAC/E;QACA,0BAA0B;YACtB,KAAK;YACL,KAAK;QACT;QACA;YACI,MAAM,aAAa,IAAI,CAAC,WAAW;YACnC,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,SAAS,IAAK,MAAM,CAAE,CAAC,OAAO;gBACvE,MAAM,cAAc,EAAE,WAAW;gBACjC,MAAM,GAAG,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,GAAG,IAAI,MAAM,GAAG,GAAG,YAAY,GAAG,GAAG,MAAM,GAAG,GAAG,YAAY,GAAG,GAAG,MAAM,GAAG;gBAC9G,MAAM,GAAG,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,GAAG,IAAI,MAAM,GAAG,GAAG,YAAY,GAAG,GAAG,MAAM,GAAG,GAAG,YAAY,GAAG,GAAG,MAAM,GAAG;gBAC9G,IAAI,EAAE,QAAQ,EAAE;oBACZ,QAAQ,IAAI,gKAAA,CAAA,QAAK,CAAC;oBAClB,MAAM,qBAAqB;gBAC/B;gBACA,OAAO;YACX,GAAI,CAAC;YACL,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,GAAG,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,GAAG,GAAG;gBACpD,WAAW,UAAU,GAAG,SAAS,GAAG;gBACpC,WAAW,UAAU,GAAG,SAAS,GAAG;YACxC;YACA,WAAW,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE;gBACxD,YAAY,WAAW,UAAU;gBACjC,YAAY,WAAW,UAAU;YACrC,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc;YACpC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;QAC/D;QACA;YACI,OAAO,IAAI,CAAC,QAAQ;QACxB;QACA;YACI,OAAO,IAAI,CAAC,aAAa;QAC7B;QACA,WAAW;YACP,OAAO,CAAC,IAAI,CAAC,QAAQ,CAAC,mBAAmB;QAC7C;QACA,sBAAsB,SAAS,IAAI,EAAE,EAAE,EAAE,iBAAiB;YACtD,MAAM,sBAAsB,IAAI,CAAC,oBAAoB;YACrD,MAAM,eAAe,IAAI,CAAC,aAAa;YACvC,MAAM,sBAAsB,kBAAkB,mBAAmB;YACjE,MAAM,oBAAoB,kBAAkB,iBAAiB;YAC7D,IAAI;YACJ,IAAI;YACJ,IAAI,cAAc;gBACd,IAAI,wBAAwB,QAAQ;oBAChC,IAAI,OAAO,CAAC,KAAK,IAAI,IAAI;gBAC7B,OAAO,IAAI,wBAAwB,MAAM;oBACrC,IAAI;gBACR,OAAO,IAAI,wBAAwB,OAAO;oBACtC,IAAI;gBACR;gBACA,IAAI,mBAAmB,CAAC,kCAAkC,mBAAmB;YACjF,OAAO;gBACH,IAAI,mBAAmB,CAAC,oCAAoC,qBAAqB;gBACjF,IAAI,sBAAsB,KAAK;oBAC3B,IAAI;gBACR,OAAO,IAAI,sBAAsB,QAAQ;oBACrC,IAAI,KAAK,CAAC,OAAO,EAAE,IAAI;gBAC3B,OAAO,IAAI,sBAAsB,QAAQ;oBACrC,IAAI;gBACR;YACJ;YACA,OAAO;gBACH,GAAG;gBACH,GAAG;YACP;QACJ;QACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;YACvC,IAAI;YACJ,IAAI,mBAAmB,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;gBACvC,WAAW,IAAI,CAAC,QAAQ,CAAC,YAAY;YACzC;YACA,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,QAAQ,OAAO;YAC9D,MAAM,OAAO,IAAI,CAAC,aAAa;YAC/B,MAAM,eAAe,IAAI,CAAC,aAAa;YACvC,OAAO;gBACH,GAAG,eAAe,OAAO;gBACzB,GAAG,eAAe,OAAO;YAC7B;QACJ;QACA,sBAAsB,SAAS,MAAM;YACjC,MAAM,QAAQ,IAAI,CAAC,aAAa,GAAG,OAAO,CAAC,GAAG,OAAO,CAAC;YACtD,MAAM,cAAc,IAAI,CAAC,cAAc;YACvC,IAAI,QAAQ,WAAW,CAAC,EAAE,IAAI,QAAQ,WAAW,CAAC,EAAE,EAAE;gBAClD,OAAO;YACX;YACA,OAAO;QACX;QACA,qBAAqB,SAAS,KAAK;YAC/B,IAAI;YACJ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,IAAI,CAAC,WAAW,IAAI,MAAM,MAAM,MAAM,EAAE;gBACrF,kBAAkB,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;YAC7C;YACA,OAAO;QACX;QACA,eAAe,SAAS,MAAM,EAAE,QAAQ,EAAE,UAAU;YAChD,MAAM,aAAa,SAAS,UAAU;YACtC,MAAM,aAAa,SAAS,UAAU;YACtC,MAAM,YAAY,aAAa,WAAW,KAAK,GAAG;YAClD,OAAO,OAAO,MAAM,CAAE,SAAS,MAAM,EAAE,YAAY;gBAC/C,IAAI,OAAO,aAAa,IAAI;gBAC5B,IAAI,KAAK,aAAa,EAAE;gBACxB,MAAM,aAAa,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;gBAC5C,IAAI;gBACJ,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK;oBACpC,OAAO;gBACX;gBACA,IAAI,OAAO,IAAI;oBACX,KAAK;wBAAC;wBAAM,OAAO;qBAAG,CAAC,EAAE;gBAC7B;gBACA,IAAI,OAAO,MAAM,IAAI,OAAO,WAAW,EAAE,EAAE;oBACvC,IAAI,KAAK,WAAW,EAAE,EAAE;wBACpB,WAAW,EAAE,GAAG,KAAK,aAAa,aAAa;wBAC/C,IAAI,WAAW,OAAO,EAAE;4BACpB,WAAW,OAAO,GAAG,KAAK;4BAC1B,WAAW,eAAe,IAAI;wBAClC;oBACJ;gBACJ,OAAO,IAAI,QAAQ,cAAc,OAAO,cAAc,MAAM,cAAc,KAAK,YAAY;oBACvF,OAAO,QAAQ,aAAa,OAAO;oBACnC,KAAK,MAAM,aAAa,KAAK;oBAC7B,IAAI,KAAK,OAAO,aAAa,YAAY;wBACrC,WAAW;4BACP,MAAM;4BACN,IAAI;4BACJ,iBAAiB,CAAC,CAAC,SAAS,cAAc,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,eAAe,KAAK,CAAC,IAAI;wBACnH;wBACA,IAAI,aAAa,OAAO,EAAE;4BACtB,SAAS,OAAO,GAAG,0JAAA,CAAA,UAAS,CAAC,8BAA8B,CAAC,KAAK;4BACjE,SAAS,eAAe,GAAG,CAAC,SAAS,cAAc,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,eAAe,KAAK;wBACvH;wBACA,OAAO,IAAI,CAAC;oBAChB;gBACJ;gBACA,OAAO;YACX,GAAI,EAAE;QACV;QACA,iBAAiB,SAAS,WAAW,EAAE,QAAQ,EAAE,MAAM,EAAE,cAAc;YACnE,MAAM,OAAO,IAAI;YACjB,IAAI,SAAS,CAAC,YAAY,MAAM,IAAI,EAAE,EAAE,GAAG,CAAE,SAAS,CAAC;gBACnD,OAAO;oBACH,MAAM,KAAK,MAAM,CAAC,EAAE,UAAU;oBAC9B,IAAI,KAAK,MAAM,CAAC,EAAE,QAAQ;gBAC9B;YACJ;YACA,IAAI,eAAe,YAAY,IAAI,IAAI,eAAe,YAAY,QAAQ,IAAI,YAAY,YAAY,EAAE;gBACpG,SAAS,OAAO,MAAM,CAAC,CAAA,GAAA,mKAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,UAAU,EAAE,SAAS,UAAU,EAAE,YAAY,QAAQ,EAAE,YAAY,cAAc,EAAE,YAAY,QAAQ;YAC9J;YACA,IAAI,CAAC,kBAAkB,eAAe,YAAY,IAAI,IAAI,eAAe,YAAY,QAAQ,IAAI,YAAY,iBAAiB,IAAI,MAAM,YAAY,iBAAiB,EAAE;gBACnK,SAAS,OAAO,MAAM,CAAC,mBAAmB,aAAa,QAAQ;YACnE;YACA,OAAO,cAAc;QACzB;QACA,YAAY,SAAS,aAAa,EAAE,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK;YAC/E,MAAM,aAAa,gBAAgB,CAAC,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,KAAK,QAAQ,IAAI,CAAC;YAClF,MAAM,OAAO;gBACT,gBAAgB;gBAChB,QAAQ,QAAQ,WAAW;gBAC3B,OAAO,CAAC,QAAQ,OAAO,GAAG,QAAQ,YAAY,GAAG,MAAM,MAAM,KAAK;YACtE;YACA,MAAM,YAAY;gBACd,QAAQ,QAAQ,KAAK;gBACrB,gBAAgB;YACpB;YACA,MAAM,YAAY,IAAI,CAAC,aAAa,GAAG,aAAa,SAAS,CAAC;gBAC1D,OAAO;YACX;YACA,MAAM,SAAS,cAAc,IAAI,CAAC,SAAS,EAAE,OAAO,WAAW,cAAc,YAAY,YAAY,QAAQ,OAAO;YACpH,OAAO,QAAQ,GAAG;YAClB,OAAO,GAAG;YACV,OAAO,OAAO;QAClB;QACA,sBAAsB,SAAS,IAAI,EAAE,EAAE;YACnC,MAAM,OAAO,IAAI;YACjB,MAAM,SAAS,KAAK,OAAO;YAC3B,MAAM,YAAY,KAAK;YACvB,IAAI;YACJ,IAAI,KAAK,aAAa,EAAE;gBACpB,WAAW,KAAK,SAAS,CAAC,QAAQ,CAAC,OAAO,IAAI,EAAE,MAAM,OAAO,KAAK,EAAE;YACxE,OAAO;gBACH,WAAW,KAAK,SAAS,CAAC,QAAQ,CAAC,MAAM,OAAO,GAAG,EAAE,WAAW,OAAO,MAAM;YACjF;YACA,KAAK,eAAe,GAAG,KAAK,eAAe,IAAI,EAAE;YACjD,KAAK,eAAe,CAAC,IAAI,CAAC;YAC1B,OAAO,SAAS,EAAE;QACtB;QACA,oBAAoB,SAAS,QAAQ,EAAE,MAAM;YACzC,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;gBAClC,OAAO,IAAI,CAAC,cAAc,GAAG;gBAC7B,aAAa,IAAI,CAAC,oBAAoB,CAAC,UAAU;YACrD,GAAG,MAAM,CAAC,IAAI,CAAC,iBAAiB;YAChC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,IAAI,EAAE;YACjD,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC1B,OAAO;QACX;QACA,qBAAqB;YACjB,CAAC,IAAI,CAAC,eAAe,IAAI,EAAE,EAAE,OAAO,CAAE,SAAS,QAAQ;gBACnD,SAAS,OAAO;YACpB;YACA,IAAI,CAAC,eAAe,GAAG;QAC3B;QACA,iBAAiB,SAAS,YAAY;YAClC,MAAM,OAAO,IAAI;YACjB,MAAM,UAAU,KAAK,QAAQ;YAC7B,MAAM,aAAa,QAAQ,UAAU;YACrC,MAAM,WAAW,QAAQ,QAAQ;YACjC,IAAI;YACJ,IAAI;YACJ,MAAM,SAAS,KAAK,WAAW,CAAC,gBAAgB,GAAG,MAAM,IAAI,EAAE;YAC/D,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,KAAK,mBAAmB;YACxB,IAAI,CAAC,CAAC,UAAU,OAAO,MAAM,GAAG;gBAC5B;YACJ;YACA,MAAM,eAAe;gBACjB,OAAO,KAAK,QAAQ,CAAC,cAAc;gBACnC,aAAa,WAAW,KAAK;gBAC7B,cAAc,KAAK,aAAa;gBAChC,SAAS,eAAe,WAAW,IAAI,CAAC,WAAW;YACvD;YACA,IAAI,cAAc;gBACd,eAAe,aAAa,KAAK;gBACjC,aAAa,aAAa,GAAG;YACjC,OAAO;gBACH,eAAe,KAAK,oBAAoB,CAAC,KAAK,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,KAAK,UAAU,IAAI,CAAC,aAAa,QAAQ,aAAa,GAAG,IAAI,IAAI,CAAC;gBACxI,aAAa,KAAK,oBAAoB,CAAC,GAAG,GAAG,CAAC,QAAQ,OAAO,IAAI,CAAC,aAAa,SAAS,aAAa,MAAM,IAAI,IAAI,CAAC;YACxH;YACA,MAAM,YAAY,KAAK,kBAAkB,CAAC,cAAc;YACxD,IAAI,KAAK,UAAU,IAAI,QAAQ,OAAO,EAAE;gBACpC,oBAAoB,KAAK,aAAa,GAAG,KAAK,UAAU,GAAG;gBAC3D,kBAAkB,oBAAoB;gBACtC,gBAAgB,KAAK,kBAAkB,CAAC,mBAAmB;YAC/D;YACA,OAAO,OAAO,CAAE,SAAS,EAAE;gBACvB,IAAI,CAAC,GAAG,OAAO,EAAE;oBACb,MAAM,aAAa,KAAK,mBAAmB,CAAC,GAAG,EAAE;oBACjD,KAAK,UAAU,CAAC,YAAY,cAAc,YAAY,WAAW,KAAK,EAAE,cAAc;oBACtF,IAAI,KAAK,UAAU,IAAI,QAAQ,OAAO,EAAE;wBACpC,KAAK,UAAU,CAAC,YAAY,mBAAmB,iBAAiB,WAAW,KAAK,EAAE,cAAc;oBACpG;gBACJ;YACJ;QACJ;QACA,0BAA0B,+KAAA,CAAA,OAAI;QAC9B,OAAO,SAAS,OAAO;YACnB,MAAM,UAAU,IAAI,CAAC,QAAQ;YAC7B,MAAM,eAAe,QAAQ,YAAY;YACzC,MAAM,cAAc,IAAI,CAAC,sBAAsB;YAC/C,MAAM,sBAAsB,IAAI,CAAC,uBAAuB;YAExD,SAAS,WAAW,IAAI,EAAE,KAAK;gBAC3B,MAAM,OAAO;oBACT,YAAY;oBACZ,YAAY;gBAChB;gBACA,MAAM,QAAQ,OAAO,CAAC,KAAK,GAAG,OAAO,CAAC,KAAK,GAAG,cAAc;gBAC5D,IAAI,CAAC,eAAe,eAAe,aAAa,GAAG,CAAC,SAAS,QAAQ,SAAS,MAAM,CAAC,IAAI,CAAC,IAAI;gBAC9F,CAAC,KAAK,CAAC,KAAK,IAAI,KAAK,EAAE,IAAI,CAAC;gBAC5B,OAAO;YACX;YACA,IAAI,CAAC,UAAU,GAAG,WAAW,QAAQ,QAAQ,EAAE,IAAI,CAAC,UAAU;YAC9D,WAAW,QAAQ,QAAQ,EAAE,IAAI,CAAC,kBAAkB;YACpD,CAAC,eAAe;gBAAC;gBAAK;aAAO,GAAG;gBAAC;gBAAM;aAAM,EAAE,OAAO,CAAE,CAAA;gBACpD,WAAW,MAAM,oBAAoB,KAAK;gBAC1C,WAAW,MAAM,oBAAoB,KAAK;YAC9C;QACJ;QACA,mBAAkB,QAAQ;YACtB,MAAM,OAAO,IAAI;YACjB,MAAM,iBAAiB,KAAK,iBAAiB;YAC7C,MAAM,mBAAmB,YAAY,KAAK,yBAAyB;YACnE,MAAM,SAAS,KAAK,UAAU,GAAG,MAAM;YACvC,MAAM,uBAAuB,eAAe,aAAa;YACzD,MAAM,qBAAqB,eAAe,UAAU,GAAG,IAAI;YAC3D,IAAI,gBAAgB,eAAe,YAAY,CAAC;YAChD,IAAI;YACJ,IAAI,eAAe,sBAAsB,CAAC,CAAC,qBAAqB,WAAW,IAAI,qBAAqB,WAAW,CAAC,OAAO,CAAC,iBAAiB,CAAC,GAAG;gBACzI,gBAAgB,KAAK;YACzB;YACA,IAAI,KAAK,kBAAkB,CAAC,mBAAmB;gBAC3C,kBAAkB,KAAK,qBAAqB,CAAC;YACjD,OAAO,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;gBAClC,kBAAkB,KAAK,qBAAqB,CAAC,KAAK,UAAU,GAAG,QAAQ;YAC3E,OAAO;gBACH,kBAAkB,qBAAqB,EAAE,CAAC,eAAe,CAAC;YAC9D;YACA,IAAI,SAAS,oBAAoB,SAAS,SAAS;gBAC/C,mBAAmB;YACvB;YACA,OAAO;QACX;QACA,2BAA0B,QAAQ;YAC9B,MAAM,OAAO,IAAI;YACjB,MAAM,EACF,gBAAgB,cAAc,EAC9B,QAAQ,MAAM,EACjB,GAAG,KAAK,UAAU;YACnB,MAAM,mBAAmB,YAAY,KAAK,yBAAyB;YACnE,MAAM,iBAAiB,KAAK,iBAAiB;YAC7C,MAAM,uBAAuB,eAAe,aAAa;YACzD,MAAM,cAAc,qBAAqB,oBAAoB;YAC7D,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe,oBAAoB,KAAK,MAAM,qBAAqB,YAAY,EAAE;gBAC5F;YACJ;YACA,MAAM,kBAAkB,KAAK,iBAAiB,CAAC;YAC/C,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;gBAC7B,OAAO,KAAK,2BAA2B;YAC3C,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;gBAClC,IAAI,mBAAmB,YAAY,GAAG,EAAE;oBACpC,OAAO,KAAK,aAAa,GAAG,MAAM;gBACtC,OAAO,IAAI,mBAAmB,YAAY,GAAG,EAAE;oBAC3C,OAAO,KAAK,aAAa,GAAG,SAAS;gBACzC;YACJ,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;gBAC1B,IAAI,mBAAmB,KAAK,oBAAoB,CAAC,KAAK,EAAE;oBACpD,OAAO,KAAK,aAAa,GAAG,MAAM;gBACtC,OAAO,IAAI,mBAAmB,KAAK,oBAAoB,CAAC,GAAG,EAAE;oBACzD,OAAO,KAAK,aAAa,GAAG,SAAS;gBACzC;YACJ;YACA,OAAO;QACX;QACA;YACI,MAAM,UAAU,IAAI,CAAC,UAAU;YAC/B,OAAO,QAAQ,cAAc,IAAI,QAAQ,QAAQ;QACrD;QACA;YACI,MAAM,UAAU,IAAI,CAAC,UAAU;YAC/B,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,iBAAiB,OAAO,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,cAAc,KAAK,SAAS,QAAQ,MAAM,CAAC;QAChH;QACA;YACI,OAAO,IAAI,CAAC,yBAAyB,MAAM,CAAC,IAAI,CAAC,wBAAwB;QAC7E;QACA;YACI,OAAO,IAAI,CAAC,wBAAwB,KAAK,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ;QACtG;QACA;YACI,OAAO,IAAI,CAAC,wBAAwB,MAAM,IAAI,CAAC,uBAAuB,KAAK,IAAI,CAAC,UAAU,GAAG,QAAQ;QACzG;QACA;YACI,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB;QAC/D;QACA,oBAAoB,CAAA,WAAY;gBAAC;gBAAK;gBAAM;gBAAQ;aAAM,CAAC,OAAO,CAAC,aAAa;QAChF,uBAAsB,QAAQ;YAC1B,IAAI;YACJ,OAAO,SAAS,CAAC,wBAAwB,IAAI,CAAC,oBAAoB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,qBAAqB,CAAC,aAAa,OAAO,aAAa,OAAO,UAAU,MAAM;QACrM;QACA,wCAAuC,YAAY;YAC/C,MAAM,OAAO,IAAI;YACjB,IAAI,CAAC,KAAK,sBAAsB,MAAM,CAAC,KAAK,wBAAwB,MAAM,CAAC,aAAa,IAAI,CAAE,CAAA,IAAK,EAAE,sBAAsB,KAAM;gBAC7H;YACJ;YACA,MAAM,iBAAiB;gBACnB,MAAM,EAAE;gBACR,OAAO,EAAE;YACb;YACA,aAAa,MAAM,CAAE,CAAA,iBAAkB,eAAe,IAAI,KAAK,KAAK,IAAI,EAAG,OAAO,CAAE,CAAA;gBAChF,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,WAAW,CAAC,MAAM,EAAE,IAAK;oBAC9C,MAAM,OAAO,KAAK,WAAW,CAAC,EAAE;oBAChC,MAAM,QAAQ,KAAK,KAAK;oBACxB,IAAI,OAAO;wBACP,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,kBAAkB,KAAK,KAAK,0BAA0B,CAAC,OAAO,eAAe,YAAY,GAAG;4BACxH,eAAe,IAAI,CAAC,IAAI,CAAC;4BACzB,KAAK,yCAAyC,CAAC,OAAO;wBAC1D;wBACA,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,WAAW,CAAC,MAAM,EAAE,IAAK;4BACxD,MAAM,eAAe,eAAe,WAAW,CAAC,EAAE;4BAClD,MAAM,gBAAgB,aAAa,KAAK;4BACxC,IAAI,iBAAiB,KAAK,0BAA0B,CAAC,OAAO,gBAAgB;gCACxE,eAAe,KAAK,CAAC,IAAI,CAAC;gCAC1B,KAAK,+BAA+B,CAAC;gCACrC,IAAI,KAAK,WAAW,CAAC,MAAM;gCAC3B;4BACJ;wBACJ;oBACJ;oBACA,IAAI,KAAK,IAAI,IAAI,eAAe,KAAK,CAAC,OAAO,CAAC,QAAQ,GAAG;wBACrD,IAAI,KAAK,aAAa,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe;4BACpD,KAAK,IAAI,CAAC,IAAI,CAAC;gCACX,YAAY;4BAChB;wBACJ,OAAO,IAAI,CAAC,KAAK,aAAa,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,eAAe;4BAC5D,KAAK,IAAI,CAAC,IAAI,CAAC;gCACX,YAAY;4BAChB;wBACJ;oBACJ;gBACJ;YACJ;QACJ;QACA,2CAA0C,KAAK,EAAE,cAAc;YAC3D,MAAM,YAAY,MAAM,OAAO;YAC/B,MAAM,yBAAyB,eAAe,eAAe;YAC7D,MAAM,6BAA6B,eAAe,UAAU,GAAG,KAAK;YACpE,MAAM,8BAA8B,2BAA2B,QAAQ;YACvE,MAAM,4BAA4B,2BAA2B,cAAc,GAAG;YAC9E,MAAM,qBAAqB,IAAI,CAAC,aAAa,GAAG,eAAe;YAC/D,MAAM,qCAAqC,IAAI,CAAC,aAAa,GAAG,OAAO;YACvE,MAAM,YAAY,MAAM,IAAI,CAAC;YAC7B,MAAM,aAAa,CAAC,IAAI,CAAC,aAAa,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,IAAI;YACtE,MAAM,YAAY,IAAI,CAAC,aAAa,GAAG,UAAU,KAAK,GAAG,UAAU,MAAM;YACzE,MAAM,cAAc,yBAAyB;YAC7C,MAAM,aAAa,aAAa,YAAY;YAC5C,MAAM,OAAO,CAAC;YACd,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,cAAc,KAAK,aAAa,GAAG;gBACnC,IAAI,aAAa,cAAc,GAAG;oBAC9B,IAAI,CAAC,mBAAmB,IAAI,cAAc;gBAC9C,OAAO,IAAI,cAAc,aAAa,GAAG;oBACrC,IAAI,CAAC,mBAAmB,IAAI,aAAa;gBAC7C,OAAO;oBACH,IAAI,CAAC,mBAAmB,IAAI,gCAAgC,qCAAqC,cAAc,4BAA4B,CAAC,CAAC,aAAa,yBAAyB;gBACvL;gBACA,MAAM,IAAI,CAAC;YACf;QACJ;QACA,iCAAgC,IAAI;YAChC,IAAI;YACJ,MAAM,OAAO,IAAI;YACjB,MAAM,QAAQ,KAAK,KAAK;YACxB,IAAI,CAAC,OAAO;gBACR;YACJ;YACA,MAAM,YAAY,MAAM,OAAO;YAC/B,MAAM,eAAe,SAAS,CAAC,aAAa,KAAK,IAAI,KAAK,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,OAAO;YAC7G,MAAM,eAAe,KAAK,eAAe;YACzC,MAAM,eAAe,KAAK,UAAU,GAAG,KAAK;YAC5C,MAAM,cAAc,aAAa,cAAc;YAC/C,MAAM,gBAAgB,aAAa,QAAQ;YAC3C,MAAM,uBAAuB,KAAK,aAAa,GAAG,MAAM;YACxD,MAAM,qBAAqB,KAAK,aAAa,GAAG,eAAe;YAC/D,MAAM,YAAY,MAAM,IAAI,CAAC;YAC7B,MAAM,aAAa,CAAC,KAAK,aAAa,GAAG,UAAU,CAAC,GAAG,UAAU,CAAC,IAAI;YACtE,MAAM,YAAY,KAAK,aAAa,GAAG,UAAU,MAAM,GAAG,UAAU,KAAK;YACzE,MAAM,OAAO,CAAC;YACd,IAAI,CAAC,mBAAmB,GAAG,YAAY,CAAC,kBAAkB,uBAAuB,eAAe,aAAa,cAAc,CAAC,CAAC,aAAa,eAAe,YAAY,WAAW,CAAC;YACjL,MAAM,IAAI,CAAC;YACX,IAAI,KAAK,IAAI,EAAE;gBACX,MAAM,aAAa,KAAK,aAAa,GAAG,aAAa,MAAM,GAAG,aAAa,KAAK;gBAChF,MAAM,MAAM,kBAAkB,uBAAuB,IAAI,CAAC;gBAC1D,IAAI,CAAC,mBAAmB,GAAG,MAAM,CAAC,aAAa,CAAC;gBAChD,KAAK,IAAI,CAAC,IAAI,CAAC;YACnB;QACJ;QACA,4BAA2B,QAAQ,EAAE,QAAQ;YACzC,IAAI,CAAC,YAAY,CAAC,UAAU;gBACxB,OAAO;YACX;YACA,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI,CAAC;YACnC,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI,CAAC;YACnC,MAAM,QAAQ,SAAS,OAAO;YAC9B,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI,CAAC;YACnC,MAAM,KAAK,MAAM,CAAC,GAAG,SAAS,IAAI,CAAC;YACnC,OAAO,CAAC,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,IAAI,MAAM,MAAM,MAAM,KAAK,MAAM,KAAK,KAAK,CAAC,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,MAAM,MAAM,KAAK,MAAM,MAAM;QACpK;IACJ;AACJ;AAEA,SAAS,cAAc,QAAQ,EAAE,IAAI,EAAE,YAAY,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,OAAO;IAC9F,MAAM,cAAc,UAAU,WAAW;IACzC,MAAM,QAAQ,SAAS,CAAC,GAAG,MAAM,CAAC;IAClC,OAAO,SAAS,MAAM,EAAE,IAAI;QACxB,SAAS,IAAI,CAAC,aAAa,UAAU,cAAc,YAAY,YAAY,QAAQ,WAAW,aAAa,IAAI,CAAC,MAAM,MAAM,CAAC;IACjI;AACJ;AAEA,SAAS,UAAU,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO;IACpE,IAAI,CAAC,SAAS;QACV,OAAO;YAAC;YAAc,aAAa;YAAQ;YAAY,aAAa;SAAO;IAC/E;IACA,cAAc;IACd,IAAI;IACJ,MAAM,WAAW,aAAa;IAC9B,MAAM,cAAc,aAAa;IACjC,MAAM,cAAc,aAAa;IACjC,MAAM,SAAS;QACX;YAAC;YAAc;SAAY;KAC9B;IACD,IAAK,kBAAkB,cAAc,kBAAkB,aAAa,IAAI,mBAAmB,GAAI;QAC3F,OAAO,IAAI,CAAC;YAAC,kBAAkB;YAAG;YAAU,kBAAkB;YAAG;YAAU,kBAAkB;YAAI;YAAa,kBAAkB;YAAI;YAAa,kBAAkB;YAAI;YAAa,kBAAkB;YAAI;SAAY;IAC1N;IACA,OAAO,EAAE,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,EAAE;AAC/B;AAEA,SAAS,WAAW,UAAU;IAC1B,MAAM,SAAS,EAAE;IACjB,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;QACvC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE;QAC7B,OAAO,IAAI,CAAC,UAAU,CAAC,EAAE;IAC7B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3054, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/axes_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/axes_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst _max = Math.max;\r\nexport const calculateCanvasMargins = function(bBoxes, canvas) {\r\n    const cLeft = canvas.left;\r\n    const cTop = canvas.top;\r\n    const cRight = canvas.width - canvas.right;\r\n    const cBottom = canvas.height - canvas.bottom;\r\n    return bBoxes.reduce((function(margins, bBox) {\r\n        if (!bBox || bBox.isEmpty) {\r\n            return margins\r\n        }\r\n        return {\r\n            left: _max(margins.left, cLeft - bBox.x),\r\n            top: _max(margins.top, cTop - bBox.y),\r\n            right: _max(margins.right, bBox.x + bBox.width - cRight),\r\n            bottom: _max(margins.bottom, bBox.y + bBox.height - cBottom)\r\n        }\r\n    }), {\r\n        left: 0,\r\n        right: 0,\r\n        top: 0,\r\n        bottom: 0\r\n    })\r\n};\r\nexport const measureLabels = function(items) {\r\n    items.forEach((function(item) {\r\n        const label = item.getContentContainer();\r\n        item.labelBBox = label ? label.getBBox() : {\r\n            x: 0,\r\n            y: 0,\r\n            width: 0,\r\n            height: 0\r\n        }\r\n    }))\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD,MAAM,OAAO,KAAK,GAAG;AACd,MAAM,yBAAyB,SAAS,MAAM,EAAE,MAAM;IACzD,MAAM,QAAQ,OAAO,IAAI;IACzB,MAAM,OAAO,OAAO,GAAG;IACvB,MAAM,SAAS,OAAO,KAAK,GAAG,OAAO,KAAK;IAC1C,MAAM,UAAU,OAAO,MAAM,GAAG,OAAO,MAAM;IAC7C,OAAO,OAAO,MAAM,CAAE,SAAS,OAAO,EAAE,IAAI;QACxC,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;YACvB,OAAO;QACX;QACA,OAAO;YACH,MAAM,KAAK,QAAQ,IAAI,EAAE,QAAQ,KAAK,CAAC;YACvC,KAAK,KAAK,QAAQ,GAAG,EAAE,OAAO,KAAK,CAAC;YACpC,OAAO,KAAK,QAAQ,KAAK,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG;YACjD,QAAQ,KAAK,QAAQ,MAAM,EAAE,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;QACxD;IACJ,GAAI;QACA,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ;IACZ;AACJ;AACO,MAAM,gBAAgB,SAAS,KAAK;IACvC,MAAM,OAAO,CAAE,SAAS,IAAI;QACxB,MAAM,QAAQ,KAAK,mBAAmB;QACtC,KAAK,SAAS,GAAG,QAAQ,MAAM,OAAO,KAAK;YACvC,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;QACZ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/polar_axes.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/polar_axes.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    map as _map,\r\n    convertPolarToXY,\r\n    convertXYToPolar,\r\n    normalizeAngle,\r\n    getVizRangeObject,\r\n    getCosAndSin,\r\n    getDistance\r\n} from \"../core/utils\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport constants from \"./axes_constants\";\r\nimport xyAxes from \"./xy_axes\";\r\nimport {\r\n    tick\r\n} from \"./tick\";\r\nimport {\r\n    calculateCanvasMargins,\r\n    measureLabels\r\n} from \"./axes_utils\";\r\nimport {\r\n    noop as _noop\r\n} from \"../../core/utils/common\";\r\nconst {\r\n    PI: PI,\r\n    abs: abs,\r\n    atan: atan,\r\n    round: round\r\n} = Math;\r\nconst _min = Math.min;\r\nconst _max = Math.max;\r\nconst xyAxesLinear = xyAxes.linear;\r\nconst HALF_PI_ANGLE = 90;\r\n\r\nfunction getPolarQuarter(angle) {\r\n    let quarter;\r\n    angle = normalizeAngle(angle);\r\n    if (angle >= 315 && angle <= 360 || angle < 45 && angle >= 0) {\r\n        quarter = 1\r\n    } else if (angle >= 45 && angle < 135) {\r\n        quarter = 2\r\n    } else if (angle >= 135 && angle < 225) {\r\n        quarter = 3\r\n    } else if (angle >= 225 && angle < 315) {\r\n        quarter = 4\r\n    }\r\n    return quarter\r\n}\r\nconst circularAxes = {\r\n    _calculateValueMargins(ticks) {\r\n        let {\r\n            minVisible: minVisible,\r\n            maxVisible: maxVisible\r\n        } = this._getViewportRange();\r\n        if (ticks && ticks.length > 1) {\r\n            minVisible = minVisible < ticks[0].value ? minVisible : ticks[0].value;\r\n            maxVisible = minVisible > ticks[ticks.length - 1].value ? maxVisible : ticks[ticks.length - 1].value\r\n        }\r\n        return {\r\n            minValue: minVisible,\r\n            maxValue: maxVisible\r\n        }\r\n    },\r\n    applyMargins() {\r\n        const margins = this._calculateValueMargins(this._majorTicks);\r\n        const br = this._translator.getBusinessRange();\r\n        br.addRange({\r\n            minVisible: margins.minValue,\r\n            maxVisible: margins.maxValue,\r\n            interval: this._calculateRangeInterval(br.interval)\r\n        });\r\n        this._translator.updateBusinessRange(br)\r\n    },\r\n    _getTranslatorOptions: function() {\r\n        return {\r\n            isHorizontal: true,\r\n            conversionValue: true,\r\n            addSpiderCategory: this._getSpiderCategoryOption(),\r\n            stick: this._getStick()\r\n        }\r\n    },\r\n    getCenter: function() {\r\n        return this._center\r\n    },\r\n    getRadius: function() {\r\n        return this._radius\r\n    },\r\n    getAngles: function() {\r\n        const options = this._options;\r\n        return [options.startAngle, options.endAngle]\r\n    },\r\n    _updateRadius(canvas) {\r\n        const rad = _min(canvas.width - canvas.left - canvas.right, canvas.height - canvas.top - canvas.bottom) / 2;\r\n        this._radius = rad < 0 ? 0 : rad\r\n    },\r\n    _updateCenter: function(canvas) {\r\n        this._center = {\r\n            x: canvas.left + (canvas.width - canvas.right - canvas.left) / 2,\r\n            y: canvas.top + (canvas.height - canvas.top - canvas.bottom) / 2\r\n        }\r\n    },\r\n    _processCanvas: function(canvas) {\r\n        this._updateRadius(canvas);\r\n        this._updateCenter(canvas);\r\n        return {\r\n            left: 0,\r\n            right: 0,\r\n            width: this._getScreenDelta()\r\n        }\r\n    },\r\n    _createAxisElement: function() {\r\n        return this._renderer.circle()\r\n    },\r\n    _updateAxisElementPosition: function() {\r\n        const center = this.getCenter();\r\n        this._axisElement.attr({\r\n            cx: center.x,\r\n            cy: center.y,\r\n            r: this.getRadius()\r\n        })\r\n    },\r\n    _boundaryTicksVisibility: {\r\n        min: true\r\n    },\r\n    _getSpiderCategoryOption: function() {\r\n        return this._options.firstPointOnStartAngle\r\n    },\r\n    _validateOptions(options) {\r\n        const that = this;\r\n        let originValue = options.originValue;\r\n        const wholeRange = options.wholeRange = {};\r\n        const period = options.period;\r\n        if (isDefined(originValue)) {\r\n            originValue = that.validateUnit(originValue)\r\n        }\r\n        if (period > 0 && options.argumentType === constants.numeric) {\r\n            originValue = originValue || 0;\r\n            wholeRange.endValue = originValue + period;\r\n            that._viewport = getVizRangeObject([originValue, wholeRange.endValue])\r\n        }\r\n        if (isDefined(originValue)) {\r\n            wholeRange.startValue = originValue\r\n        }\r\n    },\r\n    getMargins() {\r\n        const tickOptions = this._options.tick;\r\n        const tickOuterLength = _max(tickOptions.visible ? tickOptions.length / 2 + tickOptions.shift : 0, 0);\r\n        const radius = this.getRadius();\r\n        const {\r\n            x: x,\r\n            y: y\r\n        } = this._center;\r\n        const labelBoxes = this._majorTicks.map((t => t.label && t.label.getBBox())).filter((b => b));\r\n        const canvas = extend({}, this._canvas, {\r\n            left: x - radius,\r\n            top: y - radius,\r\n            right: this._canvas.width - (x + radius),\r\n            bottom: this._canvas.height - (y + radius)\r\n        });\r\n        const margins = calculateCanvasMargins(labelBoxes, canvas);\r\n        Object.keys(margins).forEach((k => margins[k] = margins[k] < tickOuterLength ? tickOuterLength : margins[k]));\r\n        return margins\r\n    },\r\n    _updateLabelsPosition() {\r\n        measureLabels(this._majorTicks);\r\n        this._adjustLabelsCoord(0, 0, true);\r\n        this._checkBoundedLabelsOverlapping(this._majorTicks, this._majorTicks.map((t => t.labelBBox)))\r\n    },\r\n    _setVisualRange: _noop,\r\n    applyVisualRangeSetter: _noop,\r\n    _getStick: function() {\r\n        return this._options.firstPointOnStartAngle || this._options.type !== constants.discrete\r\n    },\r\n    _getTranslatedCoord: function(value, offset) {\r\n        return this._translator.translate(value, offset) - 90\r\n    },\r\n    _getCanvasStartEnd: function() {\r\n        return {\r\n            start: -90,\r\n            end: 270\r\n        }\r\n    },\r\n    _getStripGraphicAttributes: function(fromAngle, toAngle) {\r\n        const center = this.getCenter();\r\n        const angle = this.getAngles()[0];\r\n        const r = this.getRadius();\r\n        return {\r\n            x: center.x,\r\n            y: center.y,\r\n            innerRadius: 0,\r\n            outerRadius: r,\r\n            startAngle: -toAngle - angle,\r\n            endAngle: -fromAngle - angle\r\n        }\r\n    },\r\n    _createStrip: function(coords) {\r\n        return this._renderer.arc(coords.x, coords.y, coords.innerRadius, coords.outerRadius, coords.startAngle, coords.endAngle)\r\n    },\r\n    _getStripLabelCoords: function(from, to) {\r\n        const coords = this._getStripGraphicAttributes(from, to);\r\n        const angle = coords.startAngle + (coords.endAngle - coords.startAngle) / 2;\r\n        const cosSin = getCosAndSin(angle);\r\n        const halfRad = this.getRadius() / 2;\r\n        const center = this.getCenter();\r\n        const x = round(center.x + halfRad * cosSin.cos);\r\n        const y = round(center.y - halfRad * cosSin.sin);\r\n        return {\r\n            x: x,\r\n            y: y,\r\n            align: constants.center\r\n        }\r\n    },\r\n    _getConstantLineGraphicAttributes: function(value) {\r\n        const center = this.getCenter();\r\n        const r = this.getRadius();\r\n        return {\r\n            points: [center.x, center.y, center.x + r, center.y]\r\n        }\r\n    },\r\n    _createConstantLine: function(value, attr) {\r\n        return this._createPathElement(this._getConstantLineGraphicAttributes(value).points, attr)\r\n    },\r\n    _rotateConstantLine(line, value) {\r\n        const {\r\n            x: x,\r\n            y: y\r\n        } = this.getCenter();\r\n        line.rotate(value + this.getAngles()[0], x, y)\r\n    },\r\n    _getConstantLineLabelsCoords: function(value) {\r\n        const cosSin = getCosAndSin(-value - this.getAngles()[0]);\r\n        const halfRad = this.getRadius() / 2;\r\n        const center = this.getCenter();\r\n        const x = round(center.x + halfRad * cosSin.cos);\r\n        const y = round(center.y - halfRad * cosSin.sin);\r\n        return {\r\n            x: x,\r\n            y: y\r\n        }\r\n    },\r\n    _checkAlignmentConstantLineLabels: _noop,\r\n    _adjustDivisionFactor: function(val) {\r\n        return 180 * val / (this.getRadius() * PI)\r\n    },\r\n    _getScreenDelta: function() {\r\n        const angles = this.getAngles();\r\n        return abs(angles[0] - angles[1])\r\n    },\r\n    _getTickMarkPoints: function(coords, length, _ref) {\r\n        let {\r\n            shift: shift = 0\r\n        } = _ref;\r\n        const center = this.getCenter();\r\n        const radiusWithTicks = this.getRadius() + length * {\r\n            inside: -1,\r\n            center: -.5,\r\n            outside: 0\r\n        } [this._options.tickOrientation || \"center\"];\r\n        return [center.x + radiusWithTicks + shift, center.y, center.x + radiusWithTicks + length + shift, center.y]\r\n    },\r\n    _getLabelAdjustedCoord: function(tick, _offset, _maxWidth, checkCanvas) {\r\n        const that = this;\r\n        const labelCoords = tick.labelCoords;\r\n        const labelY = labelCoords.y;\r\n        const labelAngle = labelCoords.angle;\r\n        const cosSin = getCosAndSin(labelAngle);\r\n        const cos = cosSin.cos;\r\n        const sin = cosSin.sin;\r\n        const box = tick.labelBBox;\r\n        const halfWidth = box.width / 2;\r\n        const halfHeight = box.height / 2;\r\n        const indentFromAxis = that._options.label.indentFromAxis || 0;\r\n        const x = labelCoords.x + indentFromAxis * cos;\r\n        const y = labelY + (labelY - box.y - halfHeight) + indentFromAxis * sin;\r\n        let shiftX = 0;\r\n        let shiftY = 0;\r\n        switch (getPolarQuarter(labelAngle)) {\r\n            case 1:\r\n                shiftX = halfWidth;\r\n                shiftY = halfHeight * sin;\r\n                break;\r\n            case 2:\r\n                shiftX = halfWidth * cos;\r\n                shiftY = halfHeight;\r\n                break;\r\n            case 3:\r\n                shiftX = -halfWidth;\r\n                shiftY = halfHeight * sin;\r\n                break;\r\n            case 4:\r\n                shiftX = halfWidth * cos;\r\n                shiftY = -halfHeight\r\n        }\r\n        if (checkCanvas) {\r\n            const canvas = that._canvas;\r\n            const boxShiftX = x - labelCoords.x + shiftX;\r\n            const boxShiftY = y - labelCoords.y + shiftY;\r\n            if (box.x + boxShiftX < canvas.originalLeft) {\r\n                shiftX -= box.x + boxShiftX - canvas.originalLeft\r\n            }\r\n            if (box.x + box.width + boxShiftX > canvas.width - canvas.originalRight) {\r\n                shiftX -= box.x + box.width + boxShiftX - (canvas.width - canvas.originalRight)\r\n            }\r\n            if (box.y + boxShiftY < canvas.originalTop) {\r\n                shiftY -= box.y + boxShiftY - canvas.originalTop\r\n            }\r\n            if (box.y + box.height + boxShiftY > canvas.height - canvas.originalBottom) {\r\n                shiftY -= box.y + box.height + boxShiftY - (canvas.height - canvas.originalBottom)\r\n            }\r\n        }\r\n        return {\r\n            x: x + shiftX,\r\n            y: y + shiftY\r\n        }\r\n    },\r\n    _getGridLineDrawer: function() {\r\n        const that = this;\r\n        return function(tick, gridStyle) {\r\n            const center = that.getCenter();\r\n            return that._createPathElement(that._getGridPoints().points, gridStyle).rotate(tick.coords.angle, center.x, center.y)\r\n        }\r\n    },\r\n    _getGridPoints: function() {\r\n        const r = this.getRadius();\r\n        const center = this.getCenter();\r\n        return {\r\n            points: [center.x, center.y, center.x + r, center.y]\r\n        }\r\n    },\r\n    _getTranslatedValue: function(value, offset) {\r\n        const startAngle = this.getAngles()[0];\r\n        const angle = this._translator.translate(value, -offset);\r\n        const coords = convertPolarToXY(this.getCenter(), startAngle, angle, this.getRadius());\r\n        return {\r\n            x: coords.x,\r\n            y: coords.y,\r\n            angle: this.getTranslatedAngle(angle)\r\n        }\r\n    },\r\n    _getAdjustedStripLabelCoords: function(strip) {\r\n        const box = strip.labelBBox;\r\n        return {\r\n            translateY: strip.label.attr(\"y\") - box.y - box.height / 2\r\n        }\r\n    },\r\n    coordsIn: function(x, y) {\r\n        return convertXYToPolar(this.getCenter(), x, y).r > this.getRadius()\r\n    },\r\n    _rotateTick: function(element, coords) {\r\n        const center = this.getCenter();\r\n        element.rotate(coords.angle, center.x, center.y)\r\n    },\r\n    _validateOverlappingMode: function(mode) {\r\n        return constants.validateOverlappingMode(mode)\r\n    },\r\n    _validateDisplayMode: function() {\r\n        return \"standard\"\r\n    },\r\n    _getStep: function(boxes) {\r\n        const radius = this.getRadius() + (this._options.label.indentFromAxis || 0);\r\n        const maxLabelBox = boxes.reduce((function(prevValue, box) {\r\n            const curValue = prevValue;\r\n            if (prevValue.width < box.width) {\r\n                curValue.width = box.width\r\n            }\r\n            if (prevValue.height < box.height) {\r\n                curValue.height = box.height\r\n            }\r\n            return curValue\r\n        }), {\r\n            width: 0,\r\n            height: 0\r\n        });\r\n        const angle1 = abs(2 * atan(maxLabelBox.height / (2 * radius - maxLabelBox.width)) * 180 / PI);\r\n        const angle2 = abs(2 * atan(maxLabelBox.width / (2 * radius - maxLabelBox.height)) * 180 / PI);\r\n        return constants.getTicksCountInRange(this._majorTicks, \"angle\", _max(angle1, angle2))\r\n    },\r\n    _checkBoundedLabelsOverlapping: function(majorTicks, boxes, mode) {\r\n        const labelOpt = this._options.label;\r\n        mode = mode || this._validateOverlappingMode(labelOpt.overlappingBehavior);\r\n        if (\"hide\" !== mode) {\r\n            return\r\n        }\r\n        const lastVisibleLabelIndex = majorTicks.reduce(((lastVisibleLabelIndex, tick, index) => tick.label ? index : lastVisibleLabelIndex), null);\r\n        if (!lastVisibleLabelIndex) {\r\n            return\r\n        }\r\n        if (constants.areLabelsOverlap(boxes[0], boxes[lastVisibleLabelIndex], labelOpt.minSpacing, constants.center)) {\r\n            \"first\" === labelOpt.hideFirstOrLast ? majorTicks[0].removeLabel() : majorTicks[lastVisibleLabelIndex].removeLabel()\r\n        }\r\n    },\r\n    shift: function(margins) {\r\n        this._axisGroup.attr({\r\n            translateX: margins.right,\r\n            translateY: margins.bottom\r\n        });\r\n        this._axisElementsGroup.attr({\r\n            translateX: margins.right,\r\n            translateY: margins.bottom\r\n        })\r\n    },\r\n    getTranslatedAngle(angle) {\r\n        const startAngle = this.getAngles()[0];\r\n        return angle + startAngle - 90\r\n    }\r\n};\r\nexport const circular = circularAxes;\r\nexport const circularSpider = extend({}, circularAxes, {\r\n    _createAxisElement: function() {\r\n        return this._renderer.path([], \"area\")\r\n    },\r\n    _updateAxisElementPosition: function() {\r\n        this._axisElement.attr({\r\n            points: _map(this.getSpiderTicks(), (function(tick) {\r\n                return {\r\n                    x: tick.coords.x,\r\n                    y: tick.coords.y\r\n                }\r\n            }))\r\n        })\r\n    },\r\n    _getStick: function() {\r\n        return true\r\n    },\r\n    _getSpiderCategoryOption: function() {\r\n        return true\r\n    },\r\n    getSpiderTicks: function() {\r\n        const ticks = this.getFullTicks();\r\n        this._spiderTicks = ticks.map(tick(this, this.renderer, {}, {}, this._getSkippedCategory(ticks), true));\r\n        this._spiderTicks.forEach((function(tick) {\r\n            tick.initCoords()\r\n        }));\r\n        return this._spiderTicks\r\n    },\r\n    _getStripGraphicAttributes: function(fromAngle, toAngle) {\r\n        const center = this.getCenter();\r\n        const spiderTicks = this.getSpiderTicks();\r\n        let firstTick;\r\n        let lastTick;\r\n        let nextTick;\r\n        let tick;\r\n        const points = [];\r\n        let i = 0;\r\n        const len = spiderTicks.length;\r\n        while (i < len) {\r\n            tick = spiderTicks[i].coords;\r\n            if (tick.angle >= fromAngle && tick.angle <= toAngle) {\r\n                if (!firstTick) {\r\n                    firstTick = (spiderTicks[i - 1] || spiderTicks[spiderTicks.length - 1]).coords;\r\n                    points.push((tick.x + firstTick.x) / 2, (tick.y + firstTick.y) / 2)\r\n                }\r\n                points.push(tick.x, tick.y);\r\n                nextTick = (spiderTicks[i + 1] || spiderTicks[0]).coords;\r\n                lastTick = {\r\n                    x: (tick.x + nextTick.x) / 2,\r\n                    y: (tick.y + nextTick.y) / 2\r\n                }\r\n            }\r\n            i++\r\n        }\r\n        points.push(lastTick.x, lastTick.y);\r\n        points.push(center.x, center.y);\r\n        return {\r\n            points: points\r\n        }\r\n    },\r\n    _createStrip: function(_ref2) {\r\n        let {\r\n            points: points\r\n        } = _ref2;\r\n        return this._renderer.path(points, \"area\")\r\n    },\r\n    _getTranslatedCoord: function(value, offset) {\r\n        return this._translator.translate(value, offset) - 90\r\n    },\r\n    _setTickOffset: function() {\r\n        this._tickOffset = false\r\n    }\r\n});\r\nexport const linear = {\r\n    _resetMargins() {\r\n        this._reinitTranslator(this._getViewportRange())\r\n    },\r\n    _getStick: xyAxesLinear._getStick,\r\n    _getSpiderCategoryOption: _noop,\r\n    _getTranslatorOptions: function() {\r\n        return {\r\n            isHorizontal: true,\r\n            stick: this._getStick()\r\n        }\r\n    },\r\n    getRadius: circularAxes.getRadius,\r\n    getCenter: circularAxes.getCenter,\r\n    getAngles: circularAxes.getAngles,\r\n    _updateRadius: circularAxes._updateRadius,\r\n    _updateCenter: circularAxes._updateCenter,\r\n    _processCanvas(canvas) {\r\n        this._updateRadius(canvas);\r\n        this._updateCenter(canvas);\r\n        return {\r\n            left: 0,\r\n            right: 0,\r\n            startPadding: canvas.startPadding,\r\n            endPadding: canvas.endPadding,\r\n            width: this.getRadius()\r\n        }\r\n    },\r\n    _createAxisElement: xyAxesLinear._createAxisElement,\r\n    _updateAxisElementPosition: function() {\r\n        const centerCoord = this.getCenter();\r\n        this._axisElement.attr({\r\n            points: [centerCoord.x, centerCoord.y, centerCoord.x + this.getRadius(), centerCoord.y]\r\n        }).rotate(this.getAngles()[0] - 90, centerCoord.x, centerCoord.y)\r\n    },\r\n    _getScreenDelta: function() {\r\n        return this.getRadius()\r\n    },\r\n    _getTickMarkPoints: function(coords, length) {\r\n        return [coords.x - length / 2, coords.y, coords.x + length / 2, coords.y]\r\n    },\r\n    _getLabelAdjustedCoord: function(tick) {\r\n        const labelCoords = tick.labelCoords;\r\n        const labelY = labelCoords.y;\r\n        const cosSin = getCosAndSin(labelCoords.angle);\r\n        const indentFromAxis = this._options.label.indentFromAxis || 0;\r\n        const box = tick.labelBBox;\r\n        const x = labelCoords.x - abs(indentFromAxis * cosSin.sin) + abs(box.width / 2 * cosSin.cos) - box.width / 2;\r\n        const y = labelY + (labelY - box.y) - abs(box.height / 2 * cosSin.sin) + abs(indentFromAxis * cosSin.cos);\r\n        return {\r\n            x: x,\r\n            y: y\r\n        }\r\n    },\r\n    _getGridLineDrawer: function() {\r\n        const that = this;\r\n        return function(tick, gridStyle) {\r\n            const grid = that._getGridPoints(tick.coords);\r\n            return that._renderer.circle(grid.cx, grid.cy, grid.r).attr(gridStyle).sharp()\r\n        }\r\n    },\r\n    _getGridPoints: function(coords) {\r\n        const pos = this.getCenter();\r\n        const radius = getDistance(pos.x, pos.y, coords.x, coords.y);\r\n        if (radius > this.getRadius()) {\r\n            return {\r\n                cx: null,\r\n                cy: null,\r\n                r: null\r\n            }\r\n        }\r\n        return {\r\n            cx: pos.x,\r\n            cy: pos.y,\r\n            r: radius\r\n        }\r\n    },\r\n    _getTranslatedValue: function(value, offset) {\r\n        const startAngle = this.getAngles()[0];\r\n        const xy = convertPolarToXY(this.getCenter(), startAngle, 0, this._translator.translate(value, offset));\r\n        return {\r\n            x: xy.x,\r\n            y: xy.y,\r\n            angle: startAngle - 90\r\n        }\r\n    },\r\n    _getTranslatedCoord: function(value, offset) {\r\n        return this._translator.translate(value, offset)\r\n    },\r\n    _getCanvasStartEnd() {\r\n        const invert = this.getTranslator().getBusinessRange().invert;\r\n        const coords = [0, this.getRadius()];\r\n        invert && coords.reverse();\r\n        return {\r\n            start: coords[0],\r\n            end: coords[1]\r\n        }\r\n    },\r\n    _getStripGraphicAttributes: function(fromPoint, toPoint) {\r\n        const center = this.getCenter();\r\n        return {\r\n            x: center.x,\r\n            y: center.y,\r\n            innerRadius: fromPoint,\r\n            outerRadius: toPoint\r\n        }\r\n    },\r\n    _createStrip: function(attrs) {\r\n        return this._renderer.arc(attrs.x, attrs.y, attrs.innerRadius, attrs.outerRadius, 0, 360)\r\n    },\r\n    _getAdjustedStripLabelCoords: circularAxes._getAdjustedStripLabelCoords,\r\n    _getStripLabelCoords: function(from, to) {\r\n        const labelPos = from + (to - from) / 2;\r\n        const center = this.getCenter();\r\n        const y = round(center.y - labelPos);\r\n        return {\r\n            x: center.x,\r\n            y: y,\r\n            align: constants.center\r\n        }\r\n    },\r\n    _getConstantLineGraphicAttributes: function(value) {\r\n        const center = this.getCenter();\r\n        return {\r\n            cx: center.x,\r\n            cy: center.y,\r\n            r: value\r\n        }\r\n    },\r\n    _createConstantLine: function(value, attr) {\r\n        const attrs = this._getConstantLineGraphicAttributes(value);\r\n        return this._renderer.circle(attrs.cx, attrs.cy, attrs.r).attr(attr).sharp()\r\n    },\r\n    _getConstantLineLabelsCoords: function(value) {\r\n        const center = this.getCenter();\r\n        const y = round(center.y - value);\r\n        return {\r\n            x: center.x,\r\n            y: y\r\n        }\r\n    },\r\n    _checkAlignmentConstantLineLabels: _noop,\r\n    _rotateTick: function(element, coords, isGridLine) {\r\n        !isGridLine && element.rotate(coords.angle + 90, coords.x, coords.y)\r\n    },\r\n    _validateOverlappingMode: circularAxes._validateOverlappingMode,\r\n    _validateDisplayMode: circularAxes._validateDisplayMode,\r\n    _getStep: function(boxes) {\r\n        const quarter = getPolarQuarter(this.getAngles()[0]);\r\n        const spacing = this._options.label.minSpacing;\r\n        const func = 2 === quarter || 4 === quarter ? function(box) {\r\n            return box.width + spacing\r\n        } : function(box) {\r\n            return box.height\r\n        };\r\n        const maxLabelLength = boxes.reduce(((prevValue, box) => _max(prevValue, func(box))), 0);\r\n        return constants.getTicksCountInRange(this._majorTicks, 2 === quarter || 4 === quarter ? \"x\" : \"y\", maxLabelLength)\r\n    }\r\n};\r\nexport const linearSpider = extend({}, linear, {\r\n    _createPathElement: function(points, attr) {\r\n        return this._renderer.path(points, \"area\").attr(attr).sharp()\r\n    },\r\n    setSpiderTicks: function(ticks) {\r\n        this._spiderTicks = ticks\r\n    },\r\n    _getGridLineDrawer: function() {\r\n        const that = this;\r\n        return function(tick, gridStyle) {\r\n            return that._createPathElement(that._getGridPoints(tick.coords).points, gridStyle)\r\n        }\r\n    },\r\n    _getGridPoints: function(coords) {\r\n        const pos = this.getCenter();\r\n        const radius = getDistance(pos.x, pos.y, coords.x, coords.y);\r\n        return this._getGridPointsByRadius(radius)\r\n    },\r\n    _getGridPointsByRadius: function(radius) {\r\n        const pos = this.getCenter();\r\n        if (radius > this.getRadius()) {\r\n            return {\r\n                points: null\r\n            }\r\n        }\r\n        return {\r\n            points: _map(this._spiderTicks, (function(tick) {\r\n                const cosSin = getCosAndSin(tick.coords.angle);\r\n                return {\r\n                    x: round(pos.x + radius * cosSin.cos),\r\n                    y: round(pos.y + radius * cosSin.sin)\r\n                }\r\n            }))\r\n        }\r\n    },\r\n    _getStripGraphicAttributes: function(fromPoint, toPoint) {\r\n        const innerPoints = this._getGridPointsByRadius(toPoint).points;\r\n        const outerPoints = this._getGridPointsByRadius(fromPoint).points;\r\n        return {\r\n            points: [outerPoints, innerPoints.reverse()]\r\n        }\r\n    },\r\n    _createStrip: circularSpider._createStrip,\r\n    _getConstantLineGraphicAttributes: function(value) {\r\n        return this._getGridPointsByRadius(value)\r\n    },\r\n    _createConstantLine: function(value, attr) {\r\n        return this._createPathElement(this._getConstantLineGraphicAttributes(value).points, attr)\r\n    }\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AASA;AAAA;AAGA;AAAA;AAGA;AACA;AACA;AAGA;AAIA;AAAA;;;;;;;;;AAGA,MAAM,EACF,IAAI,EAAE,EACN,KAAK,GAAG,EACR,MAAM,IAAI,EACV,OAAO,KAAK,EACf,GAAG;AACJ,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,eAAe,2JAAA,CAAA,UAAM,CAAC,MAAM;AAClC,MAAM,gBAAgB;AAEtB,SAAS,gBAAgB,KAAK;IAC1B,IAAI;IACJ,QAAQ,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE;IACvB,IAAI,SAAS,OAAO,SAAS,OAAO,QAAQ,MAAM,SAAS,GAAG;QAC1D,UAAU;IACd,OAAO,IAAI,SAAS,MAAM,QAAQ,KAAK;QACnC,UAAU;IACd,OAAO,IAAI,SAAS,OAAO,QAAQ,KAAK;QACpC,UAAU;IACd,OAAO,IAAI,SAAS,OAAO,QAAQ,KAAK;QACpC,UAAU;IACd;IACA,OAAO;AACX;AACA,MAAM,eAAe;IACjB,wBAAuB,KAAK;QACxB,IAAI,EACA,YAAY,UAAU,EACtB,YAAY,UAAU,EACzB,GAAG,IAAI,CAAC,iBAAiB;QAC1B,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;YAC3B,aAAa,aAAa,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG,aAAa,KAAK,CAAC,EAAE,CAAC,KAAK;YACtE,aAAa,aAAa,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,GAAG,aAAa,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK;QACxG;QACA,OAAO;YACH,UAAU;YACV,UAAU;QACd;IACJ;IACA;QACI,MAAM,UAAU,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW;QAC5D,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,gBAAgB;QAC5C,GAAG,QAAQ,CAAC;YACR,YAAY,QAAQ,QAAQ;YAC5B,YAAY,QAAQ,QAAQ;YAC5B,UAAU,IAAI,CAAC,uBAAuB,CAAC,GAAG,QAAQ;QACtD;QACA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC;IACzC;IACA,uBAAuB;QACnB,OAAO;YACH,cAAc;YACd,iBAAiB;YACjB,mBAAmB,IAAI,CAAC,wBAAwB;YAChD,OAAO,IAAI,CAAC,SAAS;QACzB;IACJ;IACA,WAAW;QACP,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,WAAW;QACP,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,WAAW;QACP,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,OAAO;YAAC,QAAQ,UAAU;YAAE,QAAQ,QAAQ;SAAC;IACjD;IACA,eAAc,MAAM;QAChB,MAAM,MAAM,KAAK,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK,EAAE,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM,IAAI;QAC1G,IAAI,CAAC,OAAO,GAAG,MAAM,IAAI,IAAI;IACjC;IACA,eAAe,SAAS,MAAM;QAC1B,IAAI,CAAC,OAAO,GAAG;YACX,GAAG,OAAO,IAAI,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,IAAI;YAC/D,GAAG,OAAO,GAAG,GAAG,CAAC,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM,IAAI;QACnE;IACJ;IACA,gBAAgB,SAAS,MAAM;QAC3B,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC;QACnB,OAAO;YACH,MAAM;YACN,OAAO;YACP,OAAO,IAAI,CAAC,eAAe;QAC/B;IACJ;IACA,oBAAoB;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM;IAChC;IACA,4BAA4B;QACxB,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACnB,IAAI,OAAO,CAAC;YACZ,IAAI,OAAO,CAAC;YACZ,GAAG,IAAI,CAAC,SAAS;QACrB;IACJ;IACA,0BAA0B;QACtB,KAAK;IACT;IACA,0BAA0B;QACtB,OAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB;IAC/C;IACA,kBAAiB,OAAO;QACpB,MAAM,OAAO,IAAI;QACjB,IAAI,cAAc,QAAQ,WAAW;QACrC,MAAM,aAAa,QAAQ,UAAU,GAAG,CAAC;QACzC,MAAM,SAAS,QAAQ,MAAM;QAC7B,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;YACxB,cAAc,KAAK,YAAY,CAAC;QACpC;QACA,IAAI,SAAS,KAAK,QAAQ,YAAY,KAAK,kKAAA,CAAA,UAAS,CAAC,OAAO,EAAE;YAC1D,cAAc,eAAe;YAC7B,WAAW,QAAQ,GAAG,cAAc;YACpC,KAAK,SAAS,GAAG,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;gBAAC;gBAAa,WAAW,QAAQ;aAAC;QACzE;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;YACxB,WAAW,UAAU,GAAG;QAC5B;IACJ;IACA;QACI,MAAM,cAAc,IAAI,CAAC,QAAQ,CAAC,IAAI;QACtC,MAAM,kBAAkB,KAAK,YAAY,OAAO,GAAG,YAAY,MAAM,GAAG,IAAI,YAAY,KAAK,GAAG,GAAG;QACnG,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,EACF,GAAG,CAAC,EACJ,GAAG,CAAC,EACP,GAAG,IAAI,CAAC,OAAO;QAChB,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,GAAG,CAAE,CAAA,IAAK,EAAE,KAAK,IAAI,EAAE,KAAK,CAAC,OAAO,IAAK,MAAM,CAAE,CAAA,IAAK;QAC1F,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;YACpC,MAAM,IAAI;YACV,KAAK,IAAI;YACT,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,CAAC,IAAI,MAAM;YACvC,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,MAAM;QAC7C;QACA,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE,YAAY;QACnD,OAAO,IAAI,CAAC,SAAS,OAAO,CAAE,CAAA,IAAK,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,kBAAkB,kBAAkB,OAAO,CAAC,EAAE;QAC3G,OAAO;IACX;IACA;QACI,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,WAAW;QAC9B,IAAI,CAAC,kBAAkB,CAAC,GAAG,GAAG;QAC9B,IAAI,CAAC,8BAA8B,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,WAAW,CAAC,GAAG,CAAE,CAAA,IAAK,EAAE,SAAS;IAChG;IACA,iBAAiB,+KAAA,CAAA,OAAK;IACtB,wBAAwB,+KAAA,CAAA,OAAK;IAC7B,WAAW;QACP,OAAO,IAAI,CAAC,QAAQ,CAAC,sBAAsB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ;IAC5F;IACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;QACvC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,UAAU;IACvD;IACA,oBAAoB;QAChB,OAAO;YACH,OAAO,CAAC;YACR,KAAK;QACT;IACJ;IACA,4BAA4B,SAAS,SAAS,EAAE,OAAO;QACnD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACjC,MAAM,IAAI,IAAI,CAAC,SAAS;QACxB,OAAO;YACH,GAAG,OAAO,CAAC;YACX,GAAG,OAAO,CAAC;YACX,aAAa;YACb,aAAa;YACb,YAAY,CAAC,UAAU;YACvB,UAAU,CAAC,YAAY;QAC3B;IACJ;IACA,cAAc,SAAS,MAAM;QACzB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,WAAW,EAAE,OAAO,WAAW,EAAE,OAAO,UAAU,EAAE,OAAO,QAAQ;IAC5H;IACA,sBAAsB,SAAS,IAAI,EAAE,EAAE;QACnC,MAAM,SAAS,IAAI,CAAC,0BAA0B,CAAC,MAAM;QACrD,MAAM,QAAQ,OAAO,UAAU,GAAG,CAAC,OAAO,QAAQ,GAAG,OAAO,UAAU,IAAI;QAC1E,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;QAC5B,MAAM,UAAU,IAAI,CAAC,SAAS,KAAK;QACnC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,IAAI,MAAM,OAAO,CAAC,GAAG,UAAU,OAAO,GAAG;QAC/C,MAAM,IAAI,MAAM,OAAO,CAAC,GAAG,UAAU,OAAO,GAAG;QAC/C,OAAO;YACH,GAAG;YACH,GAAG;YACH,OAAO,kKAAA,CAAA,UAAS,CAAC,MAAM;QAC3B;IACJ;IACA,mCAAmC,SAAS,KAAK;QAC7C,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,IAAI,IAAI,CAAC,SAAS;QACxB,OAAO;YACH,QAAQ;gBAAC,OAAO,CAAC;gBAAE,OAAO,CAAC;gBAAE,OAAO,CAAC,GAAG;gBAAG,OAAO,CAAC;aAAC;QACxD;IACJ;IACA,qBAAqB,SAAS,KAAK,EAAE,IAAI;QACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iCAAiC,CAAC,OAAO,MAAM,EAAE;IACzF;IACA,qBAAoB,IAAI,EAAE,KAAK;QAC3B,MAAM,EACF,GAAG,CAAC,EACJ,GAAG,CAAC,EACP,GAAG,IAAI,CAAC,SAAS;QAClB,KAAK,MAAM,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,EAAE,GAAG;IAChD;IACA,8BAA8B,SAAS,KAAK;QACxC,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,CAAC,QAAQ,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACxD,MAAM,UAAU,IAAI,CAAC,SAAS,KAAK;QACnC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,IAAI,MAAM,OAAO,CAAC,GAAG,UAAU,OAAO,GAAG;QAC/C,MAAM,IAAI,MAAM,OAAO,CAAC,GAAG,UAAU,OAAO,GAAG;QAC/C,OAAO;YACH,GAAG;YACH,GAAG;QACP;IACJ;IACA,mCAAmC,+KAAA,CAAA,OAAK;IACxC,uBAAuB,SAAS,GAAG;QAC/B,OAAO,MAAM,MAAM,CAAC,IAAI,CAAC,SAAS,KAAK,EAAE;IAC7C;IACA,iBAAiB;QACb,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO,IAAI,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;IACpC;IACA,oBAAoB,SAAS,MAAM,EAAE,MAAM,EAAE,IAAI;QAC7C,IAAI,EACA,OAAO,QAAQ,CAAC,EACnB,GAAG;QACJ,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,kBAAkB,IAAI,CAAC,SAAS,KAAK,SAAS,CAAA;YAChD,QAAQ,CAAC;YACT,QAAQ,CAAC;YACT,SAAS;QACb,CAAA,CAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,IAAI,SAAS;QAC7C,OAAO;YAAC,OAAO,CAAC,GAAG,kBAAkB;YAAO,OAAO,CAAC;YAAE,OAAO,CAAC,GAAG,kBAAkB,SAAS;YAAO,OAAO,CAAC;SAAC;IAChH;IACA,wBAAwB,SAAS,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,WAAW;QAClE,MAAM,OAAO,IAAI;QACjB,MAAM,cAAc,KAAK,WAAW;QACpC,MAAM,SAAS,YAAY,CAAC;QAC5B,MAAM,aAAa,YAAY,KAAK;QACpC,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;QAC5B,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,MAAM,OAAO,GAAG;QACtB,MAAM,MAAM,KAAK,SAAS;QAC1B,MAAM,YAAY,IAAI,KAAK,GAAG;QAC9B,MAAM,aAAa,IAAI,MAAM,GAAG;QAChC,MAAM,iBAAiB,KAAK,QAAQ,CAAC,KAAK,CAAC,cAAc,IAAI;QAC7D,MAAM,IAAI,YAAY,CAAC,GAAG,iBAAiB;QAC3C,MAAM,IAAI,SAAS,CAAC,SAAS,IAAI,CAAC,GAAG,UAAU,IAAI,iBAAiB;QACpE,IAAI,SAAS;QACb,IAAI,SAAS;QACb,OAAQ,gBAAgB;YACpB,KAAK;gBACD,SAAS;gBACT,SAAS,aAAa;gBACtB;YACJ,KAAK;gBACD,SAAS,YAAY;gBACrB,SAAS;gBACT;YACJ,KAAK;gBACD,SAAS,CAAC;gBACV,SAAS,aAAa;gBACtB;YACJ,KAAK;gBACD,SAAS,YAAY;gBACrB,SAAS,CAAC;QAClB;QACA,IAAI,aAAa;YACb,MAAM,SAAS,KAAK,OAAO;YAC3B,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG;YACtC,MAAM,YAAY,IAAI,YAAY,CAAC,GAAG;YACtC,IAAI,IAAI,CAAC,GAAG,YAAY,OAAO,YAAY,EAAE;gBACzC,UAAU,IAAI,CAAC,GAAG,YAAY,OAAO,YAAY;YACrD;YACA,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,YAAY,OAAO,KAAK,GAAG,OAAO,aAAa,EAAE;gBACrE,UAAU,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG,YAAY,CAAC,OAAO,KAAK,GAAG,OAAO,aAAa;YAClF;YACA,IAAI,IAAI,CAAC,GAAG,YAAY,OAAO,WAAW,EAAE;gBACxC,UAAU,IAAI,CAAC,GAAG,YAAY,OAAO,WAAW;YACpD;YACA,IAAI,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG,YAAY,OAAO,MAAM,GAAG,OAAO,cAAc,EAAE;gBACxE,UAAU,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG,YAAY,CAAC,OAAO,MAAM,GAAG,OAAO,cAAc;YACrF;QACJ;QACA,OAAO;YACH,GAAG,IAAI;YACP,GAAG,IAAI;QACX;IACJ;IACA,oBAAoB;QAChB,MAAM,OAAO,IAAI;QACjB,OAAO,SAAS,IAAI,EAAE,SAAS;YAC3B,MAAM,SAAS,KAAK,SAAS;YAC7B,OAAO,KAAK,kBAAkB,CAAC,KAAK,cAAc,GAAG,MAAM,EAAE,WAAW,MAAM,CAAC,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;QACxH;IACJ;IACA,gBAAgB;QACZ,MAAM,IAAI,IAAI,CAAC,SAAS;QACxB,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO;YACH,QAAQ;gBAAC,OAAO,CAAC;gBAAE,OAAO,CAAC;gBAAE,OAAO,CAAC,GAAG;gBAAG,OAAO,CAAC;aAAC;QACxD;IACJ;IACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;QACvC,MAAM,aAAa,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACtC,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,CAAC;QACjD,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,SAAS,IAAI,YAAY,OAAO,IAAI,CAAC,SAAS;QACnF,OAAO;YACH,GAAG,OAAO,CAAC;YACX,GAAG,OAAO,CAAC;YACX,OAAO,IAAI,CAAC,kBAAkB,CAAC;QACnC;IACJ;IACA,8BAA8B,SAAS,KAAK;QACxC,MAAM,MAAM,MAAM,SAAS;QAC3B,OAAO;YACH,YAAY,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG;QAC7D;IACJ;IACA,UAAU,SAAS,CAAC,EAAE,CAAC;QACnB,OAAO,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,SAAS,IAAI,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,SAAS;IACtE;IACA,aAAa,SAAS,OAAO,EAAE,MAAM;QACjC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,QAAQ,MAAM,CAAC,OAAO,KAAK,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;IACnD;IACA,0BAA0B,SAAS,IAAI;QACnC,OAAO,kKAAA,CAAA,UAAS,CAAC,uBAAuB,CAAC;IAC7C;IACA,sBAAsB;QAClB,OAAO;IACX;IACA,UAAU,SAAS,KAAK;QACpB,MAAM,SAAS,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC;QAC1E,MAAM,cAAc,MAAM,MAAM,CAAE,SAAS,SAAS,EAAE,GAAG;YACrD,MAAM,WAAW;YACjB,IAAI,UAAU,KAAK,GAAG,IAAI,KAAK,EAAE;gBAC7B,SAAS,KAAK,GAAG,IAAI,KAAK;YAC9B;YACA,IAAI,UAAU,MAAM,GAAG,IAAI,MAAM,EAAE;gBAC/B,SAAS,MAAM,GAAG,IAAI,MAAM;YAChC;YACA,OAAO;QACX,GAAI;YACA,OAAO;YACP,QAAQ;QACZ;QACA,MAAM,SAAS,IAAI,IAAI,KAAK,YAAY,MAAM,GAAG,CAAC,IAAI,SAAS,YAAY,KAAK,KAAK,MAAM;QAC3F,MAAM,SAAS,IAAI,IAAI,KAAK,YAAY,KAAK,GAAG,CAAC,IAAI,SAAS,YAAY,MAAM,KAAK,MAAM;QAC3F,OAAO,kKAAA,CAAA,UAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,SAAS,KAAK,QAAQ;IAClF;IACA,gCAAgC,SAAS,UAAU,EAAE,KAAK,EAAE,IAAI;QAC5D,MAAM,WAAW,IAAI,CAAC,QAAQ,CAAC,KAAK;QACpC,OAAO,QAAQ,IAAI,CAAC,wBAAwB,CAAC,SAAS,mBAAmB;QACzE,IAAI,WAAW,MAAM;YACjB;QACJ;QACA,MAAM,wBAAwB,WAAW,MAAM,CAAE,CAAC,uBAAuB,MAAM,QAAU,KAAK,KAAK,GAAG,QAAQ,uBAAwB;QACtI,IAAI,CAAC,uBAAuB;YACxB;QACJ;QACA,IAAI,kKAAA,CAAA,UAAS,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,sBAAsB,EAAE,SAAS,UAAU,EAAE,kKAAA,CAAA,UAAS,CAAC,MAAM,GAAG;YAC3G,YAAY,SAAS,eAAe,GAAG,UAAU,CAAC,EAAE,CAAC,WAAW,KAAK,UAAU,CAAC,sBAAsB,CAAC,WAAW;QACtH;IACJ;IACA,OAAO,SAAS,OAAO;QACnB,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACjB,YAAY,QAAQ,KAAK;YACzB,YAAY,QAAQ,MAAM;QAC9B;QACA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACzB,YAAY,QAAQ,KAAK;YACzB,YAAY,QAAQ,MAAM;QAC9B;IACJ;IACA,oBAAmB,KAAK;QACpB,MAAM,aAAa,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACtC,OAAO,QAAQ,aAAa;IAChC;AACJ;AACO,MAAM,WAAW;AACjB,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,cAAc;IACnD,oBAAoB;QAChB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE;IACnC;IACA,4BAA4B;QACxB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACnB,QAAQ,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,IAAI,CAAC,cAAc,IAAK,SAAS,IAAI;gBAC9C,OAAO;oBACH,GAAG,KAAK,MAAM,CAAC,CAAC;oBAChB,GAAG,KAAK,MAAM,CAAC,CAAC;gBACpB;YACJ;QACJ;IACJ;IACA,WAAW;QACP,OAAO;IACX;IACA,0BAA0B;QACtB,OAAO;IACX;IACA,gBAAgB;QACZ,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,IAAI,CAAC,YAAY,GAAG,MAAM,GAAG,CAAC,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,QAAQ,EAAE,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,mBAAmB,CAAC,QAAQ;QACjG,IAAI,CAAC,YAAY,CAAC,OAAO,CAAE,SAAS,IAAI;YACpC,KAAK,UAAU;QACnB;QACA,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA,4BAA4B,SAAS,SAAS,EAAE,OAAO;QACnD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,SAAS,EAAE;QACjB,IAAI,IAAI;QACR,MAAM,MAAM,YAAY,MAAM;QAC9B,MAAO,IAAI,IAAK;YACZ,OAAO,WAAW,CAAC,EAAE,CAAC,MAAM;YAC5B,IAAI,KAAK,KAAK,IAAI,aAAa,KAAK,KAAK,IAAI,SAAS;gBAClD,IAAI,CAAC,WAAW;oBACZ,YAAY,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,EAAE,MAAM;oBAC9E,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI,GAAG,CAAC,KAAK,CAAC,GAAG,UAAU,CAAC,IAAI;gBACrE;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;gBAC1B,WAAW,CAAC,WAAW,CAAC,IAAI,EAAE,IAAI,WAAW,CAAC,EAAE,EAAE,MAAM;gBACxD,WAAW;oBACP,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,IAAI;oBAC3B,GAAG,CAAC,KAAK,CAAC,GAAG,SAAS,CAAC,IAAI;gBAC/B;YACJ;YACA;QACJ;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,EAAE,SAAS,CAAC;QAClC,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;QAC9B,OAAO;YACH,QAAQ;QACZ;IACJ;IACA,cAAc,SAAS,KAAK;QACxB,IAAI,EACA,QAAQ,MAAM,EACjB,GAAG;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ;IACvC;IACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;QACvC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO,UAAU;IACvD;IACA,gBAAgB;QACZ,IAAI,CAAC,WAAW,GAAG;IACvB;AACJ;AACO,MAAM,SAAS;IAClB;QACI,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB;IACjD;IACA,WAAW,aAAa,SAAS;IACjC,0BAA0B,+KAAA,CAAA,OAAK;IAC/B,uBAAuB;QACnB,OAAO;YACH,cAAc;YACd,OAAO,IAAI,CAAC,SAAS;QACzB;IACJ;IACA,WAAW,aAAa,SAAS;IACjC,WAAW,aAAa,SAAS;IACjC,WAAW,aAAa,SAAS;IACjC,eAAe,aAAa,aAAa;IACzC,eAAe,aAAa,aAAa;IACzC,gBAAe,MAAM;QACjB,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,aAAa,CAAC;QACnB,OAAO;YACH,MAAM;YACN,OAAO;YACP,cAAc,OAAO,YAAY;YACjC,YAAY,OAAO,UAAU;YAC7B,OAAO,IAAI,CAAC,SAAS;QACzB;IACJ;IACA,oBAAoB,aAAa,kBAAkB;IACnD,4BAA4B;QACxB,MAAM,cAAc,IAAI,CAAC,SAAS;QAClC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACnB,QAAQ;gBAAC,YAAY,CAAC;gBAAE,YAAY,CAAC;gBAAE,YAAY,CAAC,GAAG,IAAI,CAAC,SAAS;gBAAI,YAAY,CAAC;aAAC;QAC3F,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE,GAAG,IAAI,YAAY,CAAC,EAAE,YAAY,CAAC;IACpE;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,oBAAoB,SAAS,MAAM,EAAE,MAAM;QACvC,OAAO;YAAC,OAAO,CAAC,GAAG,SAAS;YAAG,OAAO,CAAC;YAAE,OAAO,CAAC,GAAG,SAAS;YAAG,OAAO,CAAC;SAAC;IAC7E;IACA,wBAAwB,SAAS,IAAI;QACjC,MAAM,cAAc,KAAK,WAAW;QACpC,MAAM,SAAS,YAAY,CAAC;QAC5B,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,YAAY,KAAK;QAC7C,MAAM,iBAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,IAAI;QAC7D,MAAM,MAAM,KAAK,SAAS;QAC1B,MAAM,IAAI,YAAY,CAAC,GAAG,IAAI,iBAAiB,OAAO,GAAG,IAAI,IAAI,IAAI,KAAK,GAAG,IAAI,OAAO,GAAG,IAAI,IAAI,KAAK,GAAG;QAC3G,MAAM,IAAI,SAAS,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,IAAI,MAAM,GAAG,IAAI,OAAO,GAAG,IAAI,IAAI,iBAAiB,OAAO,GAAG;QACxG,OAAO;YACH,GAAG;YACH,GAAG;QACP;IACJ;IACA,oBAAoB;QAChB,MAAM,OAAO,IAAI;QACjB,OAAO,SAAS,IAAI,EAAE,SAAS;YAC3B,MAAM,OAAO,KAAK,cAAc,CAAC,KAAK,MAAM;YAC5C,OAAO,KAAK,SAAS,CAAC,MAAM,CAAC,KAAK,EAAE,EAAE,KAAK,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,CAAC,WAAW,KAAK;QAChF;IACJ;IACA,gBAAgB,SAAS,MAAM;QAC3B,MAAM,MAAM,IAAI,CAAC,SAAS;QAC1B,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;QAC3D,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI;YAC3B,OAAO;gBACH,IAAI;gBACJ,IAAI;gBACJ,GAAG;YACP;QACJ;QACA,OAAO;YACH,IAAI,IAAI,CAAC;YACT,IAAI,IAAI,CAAC;YACT,GAAG;QACP;IACJ;IACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;QACvC,MAAM,aAAa,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACtC,MAAM,KAAK,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,SAAS,IAAI,YAAY,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO;QAC/F,OAAO;YACH,GAAG,GAAG,CAAC;YACP,GAAG,GAAG,CAAC;YACP,OAAO,aAAa;QACxB;IACJ;IACA,qBAAqB,SAAS,KAAK,EAAE,MAAM;QACvC,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,OAAO;IAC7C;IACA;QACI,MAAM,SAAS,IAAI,CAAC,aAAa,GAAG,gBAAgB,GAAG,MAAM;QAC7D,MAAM,SAAS;YAAC;YAAG,IAAI,CAAC,SAAS;SAAG;QACpC,UAAU,OAAO,OAAO;QACxB,OAAO;YACH,OAAO,MAAM,CAAC,EAAE;YAChB,KAAK,MAAM,CAAC,EAAE;QAClB;IACJ;IACA,4BAA4B,SAAS,SAAS,EAAE,OAAO;QACnD,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO;YACH,GAAG,OAAO,CAAC;YACX,GAAG,OAAO,CAAC;YACX,aAAa;YACb,aAAa;QACjB;IACJ;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,WAAW,EAAE,MAAM,WAAW,EAAE,GAAG;IACzF;IACA,8BAA8B,aAAa,4BAA4B;IACvE,sBAAsB,SAAS,IAAI,EAAE,EAAE;QACnC,MAAM,WAAW,OAAO,CAAC,KAAK,IAAI,IAAI;QACtC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,IAAI,MAAM,OAAO,CAAC,GAAG;QAC3B,OAAO;YACH,GAAG,OAAO,CAAC;YACX,GAAG;YACH,OAAO,kKAAA,CAAA,UAAS,CAAC,MAAM;QAC3B;IACJ;IACA,mCAAmC,SAAS,KAAK;QAC7C,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,OAAO;YACH,IAAI,OAAO,CAAC;YACZ,IAAI,OAAO,CAAC;YACZ,GAAG;QACP;IACJ;IACA,qBAAqB,SAAS,KAAK,EAAE,IAAI;QACrC,MAAM,QAAQ,IAAI,CAAC,iCAAiC,CAAC;QACrD,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,KAAK;IAC9E;IACA,8BAA8B,SAAS,KAAK;QACxC,MAAM,SAAS,IAAI,CAAC,SAAS;QAC7B,MAAM,IAAI,MAAM,OAAO,CAAC,GAAG;QAC3B,OAAO;YACH,GAAG,OAAO,CAAC;YACX,GAAG;QACP;IACJ;IACA,mCAAmC,+KAAA,CAAA,OAAK;IACxC,aAAa,SAAS,OAAO,EAAE,MAAM,EAAE,UAAU;QAC7C,CAAC,cAAc,QAAQ,MAAM,CAAC,OAAO,KAAK,GAAG,IAAI,OAAO,CAAC,EAAE,OAAO,CAAC;IACvE;IACA,0BAA0B,aAAa,wBAAwB;IAC/D,sBAAsB,aAAa,oBAAoB;IACvD,UAAU,SAAS,KAAK;QACpB,MAAM,UAAU,gBAAgB,IAAI,CAAC,SAAS,EAAE,CAAC,EAAE;QACnD,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,UAAU;QAC9C,MAAM,OAAO,MAAM,WAAW,MAAM,UAAU,SAAS,GAAG;YACtD,OAAO,IAAI,KAAK,GAAG;QACvB,IAAI,SAAS,GAAG;YACZ,OAAO,IAAI,MAAM;QACrB;QACA,MAAM,iBAAiB,MAAM,MAAM,CAAE,CAAC,WAAW,MAAQ,KAAK,WAAW,KAAK,OAAQ;QACtF,OAAO,kKAAA,CAAA,UAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,WAAW,MAAM,UAAU,MAAM,KAAK;IACxG;AACJ;AACO,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ;IAC3C,oBAAoB,SAAS,MAAM,EAAE,IAAI;QACrC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,MAAM,KAAK;IAC/D;IACA,gBAAgB,SAAS,KAAK;QAC1B,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,oBAAoB;QAChB,MAAM,OAAO,IAAI;QACjB,OAAO,SAAS,IAAI,EAAE,SAAS;YAC3B,OAAO,KAAK,kBAAkB,CAAC,KAAK,cAAc,CAAC,KAAK,MAAM,EAAE,MAAM,EAAE;QAC5E;IACJ;IACA,gBAAgB,SAAS,MAAM;QAC3B,MAAM,MAAM,IAAI,CAAC,SAAS;QAC1B,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,EAAE,IAAI,CAAC,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC;QAC3D,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC;IACA,wBAAwB,SAAS,MAAM;QACnC,MAAM,MAAM,IAAI,CAAC,SAAS;QAC1B,IAAI,SAAS,IAAI,CAAC,SAAS,IAAI;YAC3B,OAAO;gBACH,QAAQ;YACZ;QACJ;QACA,OAAO;YACH,QAAQ,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,IAAI,CAAC,YAAY,EAAG,SAAS,IAAI;gBAC1C,MAAM,SAAS,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE,KAAK,MAAM,CAAC,KAAK;gBAC7C,OAAO;oBACH,GAAG,MAAM,IAAI,CAAC,GAAG,SAAS,OAAO,GAAG;oBACpC,GAAG,MAAM,IAAI,CAAC,GAAG,SAAS,OAAO,GAAG;gBACxC;YACJ;QACJ;IACJ;IACA,4BAA4B,SAAS,SAAS,EAAE,OAAO;QACnD,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC,SAAS,MAAM;QAC/D,MAAM,cAAc,IAAI,CAAC,sBAAsB,CAAC,WAAW,MAAM;QACjE,OAAO;YACH,QAAQ;gBAAC;gBAAa,YAAY,OAAO;aAAG;QAChD;IACJ;IACA,cAAc,eAAe,YAAY;IACzC,mCAAmC,SAAS,KAAK;QAC7C,OAAO,IAAI,CAAC,sBAAsB,CAAC;IACvC;IACA,qBAAqB,SAAS,KAAK,EAAE,IAAI;QACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iCAAiC,CAAC,OAAO,MAAM,EAAE;IACzF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3823, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/constant_line.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/constant_line.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nexport default function createConstantLine(axis, options) {\r\n    const labelOptions = options.label || {};\r\n    const labelPosition = labelOptions.position || \"inside\";\r\n    let parsedValue;\r\n    let valueIsParsed = false;\r\n    let lastStoredCoordinates;\r\n    axis._checkAlignmentConstantLineLabels(labelOptions);\r\n    let storedCoord;\r\n    return {\r\n        options: options,\r\n        labelOptions: labelOptions,\r\n        labelPosition: labelPosition,\r\n        label: null,\r\n        line: null,\r\n        getParsedValue() {\r\n            if (!valueIsParsed) {\r\n                parsedValue = axis.validateUnit(options.value, \"E2105\", \"constantLine\");\r\n                valueIsParsed = true;\r\n                return parsedValue\r\n            }\r\n            return parsedValue\r\n        },\r\n        draw() {\r\n            if (!isDefined(options.value) || axis._translator.getBusinessRange().isEmpty()) {\r\n                return this\r\n            }\r\n            const canvas = axis._getCanvasStartEnd();\r\n            const parsedValue = this.getParsedValue();\r\n            this.coord = axis._getConstantLinePos(parsedValue, canvas.start, canvas.end);\r\n            const rootGroup = options.displayBehindSeries ? axis._axisConstantLineGroups.under : axis._axisConstantLineGroups.above;\r\n            let group = rootGroup[labelPosition];\r\n            if (!group) {\r\n                const side = axis._isHorizontal ? labelOptions.verticalAlignment : labelOptions.horizontalAlignment;\r\n                group = rootGroup[side]\r\n            }\r\n            if (!isDefined(this.coord)) {\r\n                return this\r\n            }\r\n            const path = axis._createConstantLine(this.coord, {\r\n                stroke: options.color,\r\n                \"stroke-width\": options.width,\r\n                dashStyle: options.dashStyle\r\n            });\r\n            this.line = path.append(rootGroup.inside);\r\n            this.label = labelOptions.visible ? axis._drawConstantLineLabels(parsedValue, labelOptions, this.coord, group) : null;\r\n            this.updatePosition();\r\n            return this\r\n        },\r\n        getContentContainer() {\r\n            return this.label\r\n        },\r\n        removeLabel() {\r\n            this.label && this.label.remove()\r\n        },\r\n        updatePosition(animate) {\r\n            const canvas = axis._getCanvasStartEnd();\r\n            const coord = axis._getConstantLinePos(this.getParsedValue(), canvas.start, canvas.end);\r\n            if (!isDefined(coord)) {\r\n                return\r\n            }\r\n            this.coord = coord;\r\n            if (animate && storedCoord) {\r\n                this.label && this.label.attr(axis._getConstantLineLabelsCoords(storedCoord, this.labelOptions));\r\n                this.line && this.line.attr(axis._getConstantLineGraphicAttributes(storedCoord));\r\n                this.label && this.label.animate(axis._getConstantLineLabelsCoords(this.coord, this.labelOptions));\r\n                this.line && this.line.animate(axis._getConstantLineGraphicAttributes(this.coord))\r\n            } else {\r\n                this.label && this.label.attr(axis._getConstantLineLabelsCoords(this.coord, this.labelOptions));\r\n                this.line && this.line.attr(axis._getConstantLineGraphicAttributes(this.coord));\r\n                axis._rotateConstantLine(this.line, this.coord)\r\n            }\r\n        },\r\n        saveCoords() {\r\n            lastStoredCoordinates = storedCoord;\r\n            storedCoord = this.coord\r\n        },\r\n        resetCoordinates() {\r\n            storedCoord = lastStoredCoordinates\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;AAGe,SAAS,mBAAmB,IAAI,EAAE,OAAO;IACpD,MAAM,eAAe,QAAQ,KAAK,IAAI,CAAC;IACvC,MAAM,gBAAgB,aAAa,QAAQ,IAAI;IAC/C,IAAI;IACJ,IAAI,gBAAgB;IACpB,IAAI;IACJ,KAAK,iCAAiC,CAAC;IACvC,IAAI;IACJ,OAAO;QACH,SAAS;QACT,cAAc;QACd,eAAe;QACf,OAAO;QACP,MAAM;QACN;YACI,IAAI,CAAC,eAAe;gBAChB,cAAc,KAAK,YAAY,CAAC,QAAQ,KAAK,EAAE,SAAS;gBACxD,gBAAgB;gBAChB,OAAO;YACX;YACA,OAAO;QACX;QACA;YACI,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,KAAK,KAAK,KAAK,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI;gBAC5E,OAAO,IAAI;YACf;YACA,MAAM,SAAS,KAAK,kBAAkB;YACtC,MAAM,cAAc,IAAI,CAAC,cAAc;YACvC,IAAI,CAAC,KAAK,GAAG,KAAK,mBAAmB,CAAC,aAAa,OAAO,KAAK,EAAE,OAAO,GAAG;YAC3E,MAAM,YAAY,QAAQ,mBAAmB,GAAG,KAAK,uBAAuB,CAAC,KAAK,GAAG,KAAK,uBAAuB,CAAC,KAAK;YACvH,IAAI,QAAQ,SAAS,CAAC,cAAc;YACpC,IAAI,CAAC,OAAO;gBACR,MAAM,OAAO,KAAK,aAAa,GAAG,aAAa,iBAAiB,GAAG,aAAa,mBAAmB;gBACnG,QAAQ,SAAS,CAAC,KAAK;YAC3B;YACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,KAAK,GAAG;gBACxB,OAAO,IAAI;YACf;YACA,MAAM,OAAO,KAAK,mBAAmB,CAAC,IAAI,CAAC,KAAK,EAAE;gBAC9C,QAAQ,QAAQ,KAAK;gBACrB,gBAAgB,QAAQ,KAAK;gBAC7B,WAAW,QAAQ,SAAS;YAChC;YACA,IAAI,CAAC,IAAI,GAAG,KAAK,MAAM,CAAC,UAAU,MAAM;YACxC,IAAI,CAAC,KAAK,GAAG,aAAa,OAAO,GAAG,KAAK,uBAAuB,CAAC,aAAa,cAAc,IAAI,CAAC,KAAK,EAAE,SAAS;YACjH,IAAI,CAAC,cAAc;YACnB,OAAO,IAAI;QACf;QACA;YACI,OAAO,IAAI,CAAC,KAAK;QACrB;QACA;YACI,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM;QACnC;QACA,gBAAe,OAAO;YAClB,MAAM,SAAS,KAAK,kBAAkB;YACtC,MAAM,QAAQ,KAAK,mBAAmB,CAAC,IAAI,CAAC,cAAc,IAAI,OAAO,KAAK,EAAE,OAAO,GAAG;YACtF,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;gBACnB;YACJ;YACA,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,WAAW,aAAa;gBACxB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,4BAA4B,CAAC,aAAa,IAAI,CAAC,YAAY;gBAC9F,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,iCAAiC,CAAC;gBACnE,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,4BAA4B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY;gBAChG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,iCAAiC,CAAC,IAAI,CAAC,KAAK;YACpF,OAAO;gBACH,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,4BAA4B,CAAC,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,YAAY;gBAC7F,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,iCAAiC,CAAC,IAAI,CAAC,KAAK;gBAC7E,KAAK,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK;YAClD;QACJ;QACA;YACI,wBAAwB;YACxB,cAAc,IAAI,CAAC,KAAK;QAC5B;QACA;YACI,cAAc;QAClB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3921, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/strip.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/strip.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    patchFontOptions\r\n} from \"../core/utils\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nexport default function createStrip(axis, options) {\r\n    let storedCoord;\r\n    let lastStoredCoordinates;\r\n    const labelOptions = options.label || {};\r\n    return {\r\n        options: options,\r\n        label: null,\r\n        rect: null,\r\n        _getCoord() {\r\n            const canvas = axis._getCanvasStartEnd();\r\n            const range = axis._translator.getBusinessRange();\r\n            return axis._getStripPos(options.startValue, options.endValue, canvas.start, canvas.end, range)\r\n        },\r\n        _drawLabel: coords => axis._renderer.text(labelOptions.text, coords.x, coords.y).css(patchFontOptions(extend({}, axis.getOptions().label.font, labelOptions.font))).attr({\r\n            align: \"center\",\r\n            class: labelOptions.cssClass\r\n        }).append(axis._axisStripLabelGroup),\r\n        draw() {\r\n            if (axis._translator.getBusinessRange().isEmpty()) {\r\n                return\r\n            }\r\n            if ((isDefined(options.startValue) || isDefined(options.endValue)) && isDefined(options.color)) {\r\n                const stripPos = this._getCoord();\r\n                this.labelCoords = labelOptions.text ? axis._getStripLabelCoords(stripPos.from, stripPos.to, labelOptions) : null;\r\n                if (stripPos.outOfCanvas || !isDefined(stripPos.to) || !isDefined(stripPos.from)) {\r\n                    return\r\n                }\r\n                this.rect = axis._createStrip(axis._getStripGraphicAttributes(stripPos.from, stripPos.to)).attr({\r\n                    fill: options.color\r\n                }).append(axis._axisStripGroup);\r\n                this.label = labelOptions.text ? this._drawLabel(this.labelCoords) : null\r\n            }\r\n        },\r\n        getContentContainer() {\r\n            return this.label\r\n        },\r\n        removeLabel() {},\r\n        updatePosition(animate) {\r\n            const stripPos = this._getCoord();\r\n            if (animate && storedCoord) {\r\n                this.label && this.label.attr(axis._getStripLabelCoords(storedCoord.from, storedCoord.to, options.label));\r\n                this.rect && this.rect.attr(axis._getStripGraphicAttributes(storedCoord.from, storedCoord.to));\r\n                this.label && this.label.animate(axis._getStripLabelCoords(stripPos.from, stripPos.to, options.label));\r\n                this.rect && this.rect.animate(axis._getStripGraphicAttributes(stripPos.from, stripPos.to))\r\n            } else {\r\n                this.label && this.label.attr(axis._getStripLabelCoords(stripPos.from, stripPos.to, options.label));\r\n                this.rect && this.rect.attr(axis._getStripGraphicAttributes(stripPos.from, stripPos.to))\r\n            }\r\n        },\r\n        saveCoords() {\r\n            lastStoredCoordinates = storedCoord;\r\n            storedCoord = this._getCoord()\r\n        },\r\n        resetCoordinates() {\r\n            storedCoord = lastStoredCoordinates\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAGA;AAAA;;;;AAGe,SAAS,YAAY,IAAI,EAAE,OAAO;IAC7C,IAAI;IACJ,IAAI;IACJ,MAAM,eAAe,QAAQ,KAAK,IAAI,CAAC;IACvC,OAAO;QACH,SAAS;QACT,OAAO;QACP,MAAM;QACN;YACI,MAAM,SAAS,KAAK,kBAAkB;YACtC,MAAM,QAAQ,KAAK,WAAW,CAAC,gBAAgB;YAC/C,OAAO,KAAK,YAAY,CAAC,QAAQ,UAAU,EAAE,QAAQ,QAAQ,EAAE,OAAO,KAAK,EAAE,OAAO,GAAG,EAAE;QAC7F;QACA,YAAY,CAAA,SAAU,KAAK,SAAS,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,KAAK,UAAU,GAAG,KAAK,CAAC,IAAI,EAAE,aAAa,IAAI,IAAI,IAAI,CAAC;gBACrK,OAAO;gBACP,OAAO,aAAa,QAAQ;YAChC,GAAG,MAAM,CAAC,KAAK,oBAAoB;QACnC;YACI,IAAI,KAAK,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI;gBAC/C;YACJ;YACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,UAAU,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,CAAC,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,KAAK,GAAG;gBAC5F,MAAM,WAAW,IAAI,CAAC,SAAS;gBAC/B,IAAI,CAAC,WAAW,GAAG,aAAa,IAAI,GAAG,KAAK,oBAAoB,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,EAAE,gBAAgB;gBAC7G,IAAI,SAAS,WAAW,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,EAAE,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,GAAG;oBAC9E;gBACJ;gBACA,IAAI,CAAC,IAAI,GAAG,KAAK,YAAY,CAAC,KAAK,0BAA0B,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;oBAC5F,MAAM,QAAQ,KAAK;gBACvB,GAAG,MAAM,CAAC,KAAK,eAAe;gBAC9B,IAAI,CAAC,KAAK,GAAG,aAAa,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,WAAW,IAAI;YACzE;QACJ;QACA;YACI,OAAO,IAAI,CAAC,KAAK;QACrB;QACA,gBAAe;QACf,gBAAe,OAAO;YAClB,MAAM,WAAW,IAAI,CAAC,SAAS;YAC/B,IAAI,WAAW,aAAa;gBACxB,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,oBAAoB,CAAC,YAAY,IAAI,EAAE,YAAY,EAAE,EAAE,QAAQ,KAAK;gBACvG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,0BAA0B,CAAC,YAAY,IAAI,EAAE,YAAY,EAAE;gBAC5F,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,oBAAoB,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,EAAE,QAAQ,KAAK;gBACpG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,0BAA0B,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE;YAC7F,OAAO;gBACH,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,oBAAoB,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE,EAAE,QAAQ,KAAK;gBACjG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,0BAA0B,CAAC,SAAS,IAAI,EAAE,SAAS,EAAE;YAC1F;QACJ;QACA;YACI,wBAAwB;YACxB,cAAc,IAAI,CAAC,SAAS;QAChC;QACA;YACI,cAAc;QAClB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4001, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/axes/base_axis.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/axes/base_axis.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    smartFormatter as _format,\r\n    formatRange\r\n} from \"./smart_formatter\";\r\nimport {\r\n    patchFontOptions,\r\n    getVizRangeObject,\r\n    getLogExt as getLog,\r\n    raiseToExt as raiseTo,\r\n    valueOf,\r\n    rotateBBox,\r\n    getCategoriesInfo,\r\n    adjustVisualRange,\r\n    getAddFunction,\r\n    convertVisualRangeObject\r\n} from \"../core/utils\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isPlainObject,\r\n    type\r\n} from \"../../core/utils/type\";\r\nimport constants from \"./axes_constants\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport formatHelper from \"../../format_helper\";\r\nimport {\r\n    getParser\r\n} from \"../components/parse_utils\";\r\nimport {\r\n    tickGenerator\r\n} from \"./tick_generator\";\r\nimport {\r\n    Translator2D\r\n} from \"../translators/translator2d\";\r\nimport {\r\n    Range\r\n} from \"../translators/range\";\r\nimport {\r\n    tick\r\n} from \"./tick\";\r\nimport {\r\n    adjust\r\n} from \"../../core/utils/math\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nimport {\r\n    noop as _noop\r\n} from \"../../core/utils/common\";\r\nimport xyMethods from \"./xy_axes\";\r\nimport * as polarMethods from \"./polar_axes\";\r\nimport createConstantLine from \"./constant_line\";\r\nimport createStrip from \"./strip\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../core/utils/deferred\";\r\nimport {\r\n    calculateCanvasMargins,\r\n    measureLabels\r\n} from \"./axes_utils\";\r\nconst convertTicksToValues = constants.convertTicksToValues;\r\nconst _math = Math;\r\nconst _abs = _math.abs;\r\nconst _max = _math.max;\r\nconst _min = _math.min;\r\nconst _isArray = Array.isArray;\r\nconst DEFAULT_AXIS_LABEL_SPACING = 5;\r\nconst MAX_GRID_BORDER_ADHENSION = 4;\r\nconst TOP = constants.top;\r\nconst BOTTOM = constants.bottom;\r\nconst LEFT = constants.left;\r\nconst RIGHT = constants.right;\r\nconst CENTER = constants.center;\r\nconst KEEP = \"keep\";\r\nconst SHIFT = \"shift\";\r\nconst RESET = \"reset\";\r\nconst ROTATE = \"rotate\";\r\nconst DEFAULT_AXIS_DIVISION_FACTOR = 50;\r\nconst DEFAULT_MINOR_AXIS_DIVISION_FACTOR = 15;\r\nconst SCROLL_THRESHOLD = 5;\r\nconst MIN_BAR_MARGIN = 5;\r\nconst MAX_MARGIN_VALUE = .8;\r\nconst dateIntervals = {\r\n    day: 864e5,\r\n    week: 6048e5\r\n};\r\n\r\nfunction getTickGenerator(options, incidentOccurred, skipTickGeneration, rangeIsEmpty, adjustDivisionFactor, _ref) {\r\n    var _options$workWeek;\r\n    let {\r\n        allowNegatives: allowNegatives,\r\n        linearThreshold: linearThreshold\r\n    } = _ref;\r\n    return tickGenerator({\r\n        axisType: options.type,\r\n        dataType: options.dataType,\r\n        logBase: options.logarithmBase,\r\n        allowNegatives: allowNegatives,\r\n        linearThreshold: linearThreshold,\r\n        axisDivisionFactor: adjustDivisionFactor(options.axisDivisionFactor || 50),\r\n        minorAxisDivisionFactor: adjustDivisionFactor(options.minorAxisDivisionFactor || 15),\r\n        numberMultipliers: options.numberMultipliers,\r\n        calculateMinors: options.minorTick.visible || options.minorGrid.visible || options.calculateMinors,\r\n        allowDecimals: options.allowDecimals,\r\n        endOnTick: options.endOnTick,\r\n        incidentOccurred: incidentOccurred,\r\n        firstDayOfWeek: null === (_options$workWeek = options.workWeek) || void 0 === _options$workWeek ? void 0 : _options$workWeek[0],\r\n        skipTickGeneration: skipTickGeneration,\r\n        skipCalculationLimits: options.skipCalculationLimits,\r\n        generateExtraTick: options.generateExtraTick,\r\n        minTickInterval: options.minTickInterval,\r\n        rangeIsEmpty: rangeIsEmpty\r\n    })\r\n}\r\n\r\nfunction createMajorTick(axis, renderer, skippedCategory) {\r\n    const options = axis.getOptions();\r\n    return tick(axis, renderer, options.tick, options.grid, skippedCategory, false)\r\n}\r\n\r\nfunction createMinorTick(axis, renderer) {\r\n    const options = axis.getOptions();\r\n    return tick(axis, renderer, options.minorTick, options.minorGrid)\r\n}\r\n\r\nfunction createBoundaryTick(axis, renderer, isFirst) {\r\n    const options = axis.getOptions();\r\n    return tick(axis, renderer, extend({}, options.tick, {\r\n        visible: options.showCustomBoundaryTicks\r\n    }), options.grid, void 0, false, isFirst ? -1 : 1)\r\n}\r\n\r\nfunction callAction(elements, action, actionArgument1, actionArgument2) {\r\n    (elements || []).forEach((e => e[action](actionArgument1, actionArgument2)))\r\n}\r\n\r\nfunction initTickCoords(ticks) {\r\n    callAction(ticks, \"initCoords\")\r\n}\r\n\r\nfunction drawTickMarks(ticks, options) {\r\n    callAction(ticks, \"drawMark\", options)\r\n}\r\n\r\nfunction drawGrids(ticks, drawLine) {\r\n    callAction(ticks, \"drawGrid\", drawLine)\r\n}\r\n\r\nfunction updateTicksPosition(ticks, options, animate) {\r\n    callAction(ticks, \"updateTickPosition\", options, animate)\r\n}\r\n\r\nfunction updateGridsPosition(ticks, animate) {\r\n    callAction(ticks, \"updateGridPosition\", animate)\r\n}\r\n\r\nfunction cleanUpInvalidTicks(ticks) {\r\n    let i = ticks.length - 1;\r\n    for (i; i >= 0; i--) {\r\n        if (!removeInvalidTick(ticks, i)) {\r\n            break\r\n        }\r\n    }\r\n    for (i = 0; i < ticks.length; i++) {\r\n        if (removeInvalidTick(ticks, i)) {\r\n            i--\r\n        } else {\r\n            break\r\n        }\r\n    }\r\n}\r\n\r\nfunction removeInvalidTick(ticks, i) {\r\n    if (null === ticks[i].coords.x || null === ticks[i].coords.y) {\r\n        ticks.splice(i, 1);\r\n        return true\r\n    }\r\n    return false\r\n}\r\n\r\nfunction validateAxisOptions(options) {\r\n    const labelOptions = options.label;\r\n    let position = options.position;\r\n    const defaultPosition = options.isHorizontal ? BOTTOM : LEFT;\r\n    const secondaryPosition = options.isHorizontal ? TOP : RIGHT;\r\n    let labelPosition = labelOptions.position;\r\n    if (position !== defaultPosition && position !== secondaryPosition) {\r\n        position = defaultPosition\r\n    }\r\n    if (!labelPosition || \"outside\" === labelPosition) {\r\n        labelPosition = position\r\n    } else if (\"inside\" === labelPosition) {\r\n        labelPosition = {\r\n            [TOP]: BOTTOM,\r\n            [BOTTOM]: TOP,\r\n            [LEFT]: RIGHT,\r\n            [RIGHT]: LEFT\r\n        } [position]\r\n    }\r\n    if (labelPosition !== defaultPosition && labelPosition !== secondaryPosition) {\r\n        labelPosition = position\r\n    }\r\n    if (labelOptions.alignment !== CENTER && !labelOptions.userAlignment) {\r\n        labelOptions.alignment = {\r\n            [TOP]: CENTER,\r\n            [BOTTOM]: CENTER,\r\n            [LEFT]: RIGHT,\r\n            [RIGHT]: LEFT\r\n        } [labelPosition]\r\n    }\r\n    options.position = position;\r\n    labelOptions.position = labelPosition;\r\n    options.hoverMode = options.hoverMode ? options.hoverMode.toLowerCase() : \"none\";\r\n    labelOptions.minSpacing = labelOptions.minSpacing ?? 5;\r\n    options.type && (options.type = options.type.toLowerCase());\r\n    options.argumentType && (options.argumentType = options.argumentType.toLowerCase());\r\n    options.valueType && (options.valueType = options.valueType.toLowerCase())\r\n}\r\n\r\nfunction getOptimalAngle(boxes, labelOpt) {\r\n    const angle = 180 * _math.asin((boxes[0].height + labelOpt.minSpacing) / (boxes[1].x - boxes[0].x)) / _math.PI;\r\n    return angle < 45 ? -45 : -90\r\n}\r\n\r\nfunction updateLabels(ticks, step, func) {\r\n    ticks.forEach((function(tick, index) {\r\n        if (tick.getContentContainer()) {\r\n            if (index % step !== 0) {\r\n                tick.removeLabel()\r\n            } else if (func) {\r\n                func(tick, index)\r\n            }\r\n        }\r\n    }))\r\n}\r\n\r\nfunction getZoomBoundValue(optionValue, dataValue) {\r\n    if (void 0 === optionValue) {\r\n        return dataValue\r\n    } else if (null === optionValue) {\r\n        return\r\n    } else {\r\n        return optionValue\r\n    }\r\n}\r\n\r\nfunction configureGenerator(options, axisDivisionFactor, viewPort, screenDelta, minTickInterval) {\r\n    const tickGeneratorOptions = extend({}, options, {\r\n        endOnTick: true,\r\n        axisDivisionFactor: axisDivisionFactor,\r\n        skipCalculationLimits: true,\r\n        generateExtraTick: true,\r\n        minTickInterval: minTickInterval\r\n    });\r\n    return function(tickInterval, skipTickGeneration, min, max, breaks) {\r\n        return getTickGenerator(tickGeneratorOptions, _noop, skipTickGeneration, viewPort.isEmpty(), (v => v), viewPort)({\r\n            min: min,\r\n            max: max,\r\n            categories: viewPort.categories,\r\n            isSpacedMargin: viewPort.isSpacedMargin\r\n        }, screenDelta, tickInterval, isDefined(tickInterval), void 0, void 0, void 0, breaks)\r\n    }\r\n}\r\n\r\nfunction getConstantLineSharpDirection(coord, axisCanvas) {\r\n    return Math.max(axisCanvas.start, axisCanvas.end) !== coord ? 1 : -1\r\n}\r\nexport const Axis = function(renderSettings) {\r\n    this._renderer = renderSettings.renderer;\r\n    this._incidentOccurred = renderSettings.incidentOccurred;\r\n    this._eventTrigger = renderSettings.eventTrigger;\r\n    this._stripsGroup = renderSettings.stripsGroup;\r\n    this._stripLabelAxesGroup = renderSettings.stripLabelAxesGroup;\r\n    this._labelsAxesGroup = renderSettings.labelsAxesGroup;\r\n    this._constantLinesGroup = renderSettings.constantLinesGroup;\r\n    this._scaleBreaksGroup = renderSettings.scaleBreaksGroup;\r\n    this._axesContainerGroup = renderSettings.axesContainerGroup;\r\n    this._gridContainerGroup = renderSettings.gridGroup;\r\n    this._axisCssPrefix = renderSettings.widgetClass + \"-\" + (renderSettings.axisClass ? renderSettings.axisClass + \"-\" : \"\");\r\n    this._setType(renderSettings.axisType, renderSettings.drawingType);\r\n    this._createAxisGroups();\r\n    this._translator = this._createTranslator();\r\n    this.isArgumentAxis = renderSettings.isArgumentAxis;\r\n    this._viewport = {};\r\n    this._prevDataInfo = {};\r\n    this._firstDrawing = true;\r\n    this._initRange = {};\r\n    this._getTemplate = renderSettings.getTemplate\r\n};\r\nAxis.prototype = {\r\n    constructor: Axis,\r\n    _drawAxis() {\r\n        const options = this._options;\r\n        if (!options.visible) {\r\n            return\r\n        }\r\n        this._axisElement = this._createAxisElement();\r\n        this._updateAxisElementPosition();\r\n        this._axisElement.attr({\r\n            \"stroke-width\": options.width,\r\n            stroke: options.color,\r\n            \"stroke-opacity\": options.opacity\r\n        }).sharp(this._getSharpParam(true), this.getAxisSharpDirection()).append(this._axisLineGroup)\r\n    },\r\n    _createPathElement(points, attr, sharpDirection) {\r\n        return this.sharp(this._renderer.path(points, \"line\").attr(attr), sharpDirection)\r\n    },\r\n    sharp(svgElement) {\r\n        let sharpDirection = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 1;\r\n        return svgElement.sharp(this._getSharpParam(), sharpDirection)\r\n    },\r\n    customPositionIsAvailable: () => false,\r\n    getOrthogonalAxis: _noop,\r\n    getCustomPosition: _noop,\r\n    getCustomBoundaryPosition: _noop,\r\n    resolveOverlappingForCustomPositioning: _noop,\r\n    hasNonBoundaryPosition: () => false,\r\n    customPositionIsBoundaryOrthogonalAxis: () => false,\r\n    getResolvedBoundaryPosition() {\r\n        return this.getOptions().position\r\n    },\r\n    getAxisSharpDirection() {\r\n        const position = this.getResolvedBoundaryPosition();\r\n        return this.hasNonBoundaryPosition() || position !== BOTTOM && position !== RIGHT ? 1 : -1\r\n    },\r\n    getSharpDirectionByCoords(coords) {\r\n        const canvas = this._getCanvasStartEnd();\r\n        const maxCoord = Math.max(canvas.start, canvas.end);\r\n        return this.getRadius ? 0 : maxCoord !== coords[this._isHorizontal ? \"x\" : \"y\"] ? 1 : -1\r\n    },\r\n    _getGridLineDrawer: function() {\r\n        const that = this;\r\n        return function(tick, gridStyle) {\r\n            const grid = that._getGridPoints(tick.coords);\r\n            if (grid.points) {\r\n                return that._createPathElement(grid.points, gridStyle, that.getSharpDirectionByCoords(tick.coords))\r\n            }\r\n            return null\r\n        }\r\n    },\r\n    _getGridPoints: function(coords) {\r\n        const isHorizontal = this._isHorizontal;\r\n        const tickPositionField = isHorizontal ? \"x\" : \"y\";\r\n        const orthogonalPositions = this._orthogonalPositions;\r\n        const positionFrom = orthogonalPositions.start;\r\n        const positionTo = orthogonalPositions.end;\r\n        const borderOptions = this.borderOptions;\r\n        const canvasStart = isHorizontal ? LEFT : TOP;\r\n        const canvasEnd = isHorizontal ? RIGHT : BOTTOM;\r\n        const axisCanvas = this.getCanvas();\r\n        const canvas = {\r\n            left: axisCanvas.left,\r\n            right: axisCanvas.width - axisCanvas.right,\r\n            top: axisCanvas.top,\r\n            bottom: axisCanvas.height - axisCanvas.bottom\r\n        };\r\n        const firstBorderLinePosition = borderOptions.visible && borderOptions[canvasStart] ? canvas[canvasStart] : void 0;\r\n        const lastBorderLinePosition = borderOptions.visible && borderOptions[canvasEnd] ? canvas[canvasEnd] : void 0;\r\n        const minDelta = 4 + firstBorderLinePosition;\r\n        const maxDelta = lastBorderLinePosition - 4;\r\n        if (this.areCoordsOutsideAxis(coords) || void 0 === coords[tickPositionField] || coords[tickPositionField] < minDelta || coords[tickPositionField] > maxDelta) {\r\n            return {\r\n                points: null\r\n            }\r\n        }\r\n        return {\r\n            points: isHorizontal ? null !== coords[tickPositionField] ? [coords[tickPositionField], positionFrom, coords[tickPositionField], positionTo] : null : null !== coords[tickPositionField] ? [positionFrom, coords[tickPositionField], positionTo, coords[tickPositionField]] : null\r\n        }\r\n    },\r\n    _getConstantLinePos: function(parsedValue, canvasStart, canvasEnd) {\r\n        const value = this._getTranslatedCoord(parsedValue);\r\n        if (!isDefined(value) || value < _min(canvasStart, canvasEnd) || value > _max(canvasStart, canvasEnd)) {\r\n            return\r\n        }\r\n        return value\r\n    },\r\n    _getConstantLineGraphicAttributes: function(value) {\r\n        const positionFrom = this._orthogonalPositions.start;\r\n        const positionTo = this._orthogonalPositions.end;\r\n        return {\r\n            points: this._isHorizontal ? [value, positionFrom, value, positionTo] : [positionFrom, value, positionTo, value]\r\n        }\r\n    },\r\n    _createConstantLine: function(value, attr) {\r\n        return this._createPathElement(this._getConstantLineGraphicAttributes(value).points, attr, getConstantLineSharpDirection(value, this._getCanvasStartEnd()))\r\n    },\r\n    _drawConstantLineLabelText: function(text, x, y, _ref2, group) {\r\n        let {\r\n            font: font,\r\n            cssClass: cssClass\r\n        } = _ref2;\r\n        return this._renderer.text(text, x, y).css(patchFontOptions(extend({}, this._options.label.font, font))).attr({\r\n            align: \"center\",\r\n            class: cssClass\r\n        }).append(group)\r\n    },\r\n    _drawConstantLineLabels: function(parsedValue, lineLabelOptions, value, group) {\r\n        let text = lineLabelOptions.text;\r\n        const options = this._options;\r\n        const labelOptions = options.label;\r\n        this._checkAlignmentConstantLineLabels(lineLabelOptions);\r\n        text = text ?? this.formatLabel(parsedValue, labelOptions);\r\n        const coords = this._getConstantLineLabelsCoords(value, lineLabelOptions);\r\n        return this._drawConstantLineLabelText(text, coords.x, coords.y, lineLabelOptions, group)\r\n    },\r\n    _getStripPos: function(startValue, endValue, canvasStart, canvasEnd, range) {\r\n        const isContinuous = !!(range.minVisible || range.maxVisible);\r\n        const categories = (range.categories || []).reduce((function(result, cat) {\r\n            result.push(cat.valueOf());\r\n            return result\r\n        }), []);\r\n        let start;\r\n        let end;\r\n        let swap;\r\n        let startCategoryIndex;\r\n        let endCategoryIndex;\r\n        if (!isContinuous) {\r\n            if (isDefined(startValue) && isDefined(endValue)) {\r\n                const parsedStartValue = this.parser(startValue);\r\n                const parsedEndValue = this.parser(endValue);\r\n                startCategoryIndex = categories.indexOf((null === parsedStartValue || void 0 === parsedStartValue ? void 0 : parsedStartValue.valueOf()) ?? void 0);\r\n                endCategoryIndex = categories.indexOf((null === parsedEndValue || void 0 === parsedEndValue ? void 0 : parsedEndValue.valueOf()) ?? void 0);\r\n                if (-1 === startCategoryIndex || -1 === endCategoryIndex) {\r\n                    return {\r\n                        from: 0,\r\n                        to: 0,\r\n                        outOfCanvas: true\r\n                    }\r\n                }\r\n                if (startCategoryIndex > endCategoryIndex) {\r\n                    swap = endValue;\r\n                    endValue = startValue;\r\n                    startValue = swap\r\n                }\r\n            }\r\n        }\r\n        if (isDefined(startValue)) {\r\n            startValue = this.validateUnit(startValue, \"E2105\", \"strip\");\r\n            start = this._getTranslatedCoord(startValue, -1)\r\n        } else {\r\n            start = canvasStart\r\n        }\r\n        if (isDefined(endValue)) {\r\n            endValue = this.validateUnit(endValue, \"E2105\", \"strip\");\r\n            end = this._getTranslatedCoord(endValue, 1)\r\n        } else {\r\n            end = canvasEnd\r\n        }\r\n        const stripPosition = start < end ? {\r\n            from: start,\r\n            to: end\r\n        } : {\r\n            from: end,\r\n            to: start\r\n        };\r\n        const visibleArea = this.getVisibleArea();\r\n        if (stripPosition.from <= visibleArea[0] && stripPosition.to <= visibleArea[0] || stripPosition.from >= visibleArea[1] && stripPosition.to >= visibleArea[1]) {\r\n            stripPosition.outOfCanvas = true\r\n        }\r\n        return stripPosition\r\n    },\r\n    _getStripGraphicAttributes: function(fromPoint, toPoint) {\r\n        let x;\r\n        let y;\r\n        let width;\r\n        let height;\r\n        const orthogonalPositions = this._orthogonalPositions;\r\n        const positionFrom = orthogonalPositions.start;\r\n        const positionTo = orthogonalPositions.end;\r\n        if (this._isHorizontal) {\r\n            x = fromPoint;\r\n            y = _min(positionFrom, positionTo);\r\n            width = toPoint - fromPoint;\r\n            height = _abs(positionFrom - positionTo)\r\n        } else {\r\n            x = _min(positionFrom, positionTo);\r\n            y = fromPoint;\r\n            width = _abs(positionFrom - positionTo);\r\n            height = _abs(fromPoint - toPoint)\r\n        }\r\n        return {\r\n            x: x,\r\n            y: y,\r\n            width: width,\r\n            height: height\r\n        }\r\n    },\r\n    _createStrip: function(attrs) {\r\n        return this._renderer.rect(attrs.x, attrs.y, attrs.width, attrs.height)\r\n    },\r\n    _adjustStripLabels: function() {\r\n        const that = this;\r\n        this._strips.forEach((function(strip) {\r\n            if (strip.label) {\r\n                strip.label.attr(that._getAdjustedStripLabelCoords(strip))\r\n            }\r\n        }))\r\n    },\r\n    _adjustLabelsCoord(offset, maxWidth, checkCanvas) {\r\n        const getContainerAttrs = tick => this._getLabelAdjustedCoord(tick, offset + (tick.labelOffset || 0), maxWidth, checkCanvas);\r\n        this._majorTicks.forEach((function(tick) {\r\n            if (tick.label) {\r\n                tick.updateMultilineTextAlignment();\r\n                tick.label.attr(getContainerAttrs(tick))\r\n            } else {\r\n                tick.templateContainer && tick.templateContainer.attr(getContainerAttrs(tick))\r\n            }\r\n        }))\r\n    },\r\n    _adjustLabels: function(offset) {\r\n        const options = this.getOptions();\r\n        const positionsAreConsistent = options.position === options.label.position;\r\n        const maxSize = this._majorTicks.reduce((function(size, tick) {\r\n            if (!tick.getContentContainer()) {\r\n                return size\r\n            }\r\n            const bBox = tick.labelRotationAngle ? rotateBBox(tick.labelBBox, [tick.labelCoords.x, tick.labelCoords.y], -tick.labelRotationAngle) : tick.labelBBox;\r\n            return {\r\n                width: _max(size.width || 0, bBox.width),\r\n                height: _max(size.height || 0, bBox.height),\r\n                offset: _max(size.offset || 0, tick.labelOffset || 0)\r\n            }\r\n        }), {});\r\n        const additionalOffset = positionsAreConsistent ? this._isHorizontal ? maxSize.height : maxSize.width : 0;\r\n        this._adjustLabelsCoord(offset, maxSize.width);\r\n        return offset + additionalOffset + (additionalOffset && this._options.label.indentFromAxis) + (positionsAreConsistent ? maxSize.offset : 0)\r\n    },\r\n    _getLabelAdjustedCoord: function(tick, offset, maxWidth) {\r\n        offset = offset || 0;\r\n        const options = this._options;\r\n        const templateBox = tick.templateContainer && tick.templateContainer.getBBox();\r\n        const box = templateBox || rotateBBox(tick.labelBBox, [tick.labelCoords.x, tick.labelCoords.y], -tick.labelRotationAngle || 0);\r\n        const textAlign = tick.labelAlignment || options.label.alignment;\r\n        const isDiscrete = \"discrete\" === this._options.type;\r\n        const isFlatLabel = tick.labelRotationAngle % 90 === 0;\r\n        const indentFromAxis = options.label.indentFromAxis;\r\n        const labelPosition = options.label.position;\r\n        const axisPosition = this._axisPosition;\r\n        const labelCoords = tick.labelCoords;\r\n        const labelX = labelCoords.x;\r\n        let translateX;\r\n        let translateY;\r\n        if (this._isHorizontal) {\r\n            if (labelPosition === BOTTOM) {\r\n                translateY = axisPosition + indentFromAxis - box.y + offset\r\n            } else {\r\n                translateY = axisPosition - indentFromAxis - (box.y + box.height) - offset\r\n            }\r\n            if (textAlign === RIGHT) {\r\n                translateX = isDiscrete && isFlatLabel ? tick.coords.x - (box.x + box.width) : labelX - box.x - box.width\r\n            } else if (textAlign === LEFT) {\r\n                translateX = isDiscrete && isFlatLabel ? labelX - box.x - (tick.coords.x - labelX) : labelX - box.x\r\n            } else {\r\n                translateX = labelX - box.x - box.width / 2\r\n            }\r\n        } else {\r\n            translateY = labelCoords.y - box.y - box.height / 2;\r\n            if (labelPosition === LEFT) {\r\n                if (textAlign === LEFT) {\r\n                    translateX = axisPosition - indentFromAxis - maxWidth - box.x\r\n                } else if (textAlign === CENTER) {\r\n                    translateX = axisPosition - indentFromAxis - maxWidth / 2 - box.x - box.width / 2\r\n                } else {\r\n                    translateX = axisPosition - indentFromAxis - box.x - box.width\r\n                }\r\n                translateX -= offset\r\n            } else {\r\n                if (textAlign === RIGHT) {\r\n                    translateX = axisPosition + indentFromAxis + maxWidth - box.x - box.width\r\n                } else if (textAlign === CENTER) {\r\n                    translateX = axisPosition + indentFromAxis + maxWidth / 2 - box.x - box.width / 2\r\n                } else {\r\n                    translateX = axisPosition + indentFromAxis - box.x\r\n                }\r\n                translateX += offset\r\n            }\r\n        }\r\n        return {\r\n            translateX: translateX,\r\n            translateY: translateY\r\n        }\r\n    },\r\n    _createAxisConstantLineGroups: function() {\r\n        const renderer = this._renderer;\r\n        const classSelector = this._axisCssPrefix;\r\n        const constantLinesClass = classSelector + \"constant-lines\";\r\n        const insideGroup = renderer.g().attr({\r\n            class: constantLinesClass\r\n        });\r\n        const outsideGroup1 = renderer.g().attr({\r\n            class: constantLinesClass\r\n        });\r\n        const outsideGroup2 = renderer.g().attr({\r\n            class: constantLinesClass\r\n        });\r\n        return {\r\n            inside: insideGroup,\r\n            outside1: outsideGroup1,\r\n            left: outsideGroup1,\r\n            top: outsideGroup1,\r\n            outside2: outsideGroup2,\r\n            right: outsideGroup2,\r\n            bottom: outsideGroup2,\r\n            remove: function() {\r\n                this.inside.remove();\r\n                this.outside1.remove();\r\n                this.outside2.remove()\r\n            },\r\n            clear: function() {\r\n                this.inside.clear();\r\n                this.outside1.clear();\r\n                this.outside2.clear()\r\n            }\r\n        }\r\n    },\r\n    _createAxisGroups: function() {\r\n        const renderer = this._renderer;\r\n        const classSelector = this._axisCssPrefix;\r\n        this._axisGroup = renderer.g().attr({\r\n            class: classSelector + \"axis\"\r\n        }).enableLinks();\r\n        this._axisStripGroup = renderer.g().attr({\r\n            class: classSelector + \"strips\"\r\n        });\r\n        this._axisGridGroup = renderer.g().attr({\r\n            class: classSelector + \"grid\"\r\n        });\r\n        this._axisElementsGroup = renderer.g().attr({\r\n            class: classSelector + \"elements\"\r\n        });\r\n        this._axisLineGroup = renderer.g().attr({\r\n            class: classSelector + \"line\"\r\n        }).linkOn(this._axisGroup, \"axisLine\").linkAppend();\r\n        this._axisTitleGroup = renderer.g().attr({\r\n            class: classSelector + \"title\"\r\n        }).append(this._axisGroup);\r\n        this._axisConstantLineGroups = {\r\n            above: this._createAxisConstantLineGroups(),\r\n            under: this._createAxisConstantLineGroups()\r\n        };\r\n        this._axisStripLabelGroup = renderer.g().attr({\r\n            class: classSelector + \"axis-labels\"\r\n        })\r\n    },\r\n    _clearAxisGroups: function() {\r\n        const that = this;\r\n        that._axisGroup.remove();\r\n        that._axisStripGroup.remove();\r\n        that._axisStripLabelGroup.remove();\r\n        that._axisConstantLineGroups.above.remove();\r\n        that._axisConstantLineGroups.under.remove();\r\n        that._axisGridGroup.remove();\r\n        that._axisTitleGroup.clear();\r\n        if (!that._options.label.template || !that.isRendered()) {\r\n            that._axisElementsGroup.remove();\r\n            that._axisElementsGroup.clear()\r\n        }\r\n        that._axisLineGroup && that._axisLineGroup.clear();\r\n        that._axisStripGroup && that._axisStripGroup.clear();\r\n        that._axisGridGroup && that._axisGridGroup.clear();\r\n        that._axisConstantLineGroups.above.clear();\r\n        that._axisConstantLineGroups.under.clear();\r\n        that._axisStripLabelGroup && that._axisStripLabelGroup.clear()\r\n    },\r\n    _getLabelFormatObject: function(value, labelOptions, range, point, tickInterval, ticks) {\r\n        range = range || this._getViewportRange();\r\n        const formatObject = {\r\n            value: value,\r\n            valueText: _format(value, {\r\n                labelOptions: labelOptions,\r\n                ticks: ticks || convertTicksToValues(this._majorTicks),\r\n                tickInterval: tickInterval ?? this._tickInterval,\r\n                dataType: this._options.dataType,\r\n                logarithmBase: this._options.logarithmBase,\r\n                type: this._options.type,\r\n                showTransition: !this._options.marker.visible,\r\n                point: point\r\n            }) || \"\",\r\n            min: range.minVisible,\r\n            max: range.maxVisible\r\n        };\r\n        if (point) {\r\n            formatObject.point = point\r\n        }\r\n        return formatObject\r\n    },\r\n    formatLabel: function(value, labelOptions, range, point, tickInterval, ticks) {\r\n        const formatObject = this._getLabelFormatObject(value, labelOptions, range, point, tickInterval, ticks);\r\n        return isFunction(labelOptions.customizeText) ? labelOptions.customizeText.call(formatObject, formatObject) : formatObject.valueText\r\n    },\r\n    formatHint: function(value, labelOptions, range) {\r\n        const formatObject = this._getLabelFormatObject(value, labelOptions, range);\r\n        return isFunction(labelOptions.customizeHint) ? labelOptions.customizeHint.call(formatObject, formatObject) : void 0\r\n    },\r\n    formatRange(startValue, endValue, interval, argumentFormat) {\r\n        return formatRange({\r\n            startValue: startValue,\r\n            endValue: endValue,\r\n            tickInterval: interval,\r\n            argumentFormat: argumentFormat,\r\n            axisOptions: this.getOptions()\r\n        })\r\n    },\r\n    _setTickOffset: function() {\r\n        const options = this._options;\r\n        const discreteAxisDivisionMode = options.discreteAxisDivisionMode;\r\n        this._tickOffset = +(\"crossLabels\" !== discreteAxisDivisionMode || !discreteAxisDivisionMode)\r\n    },\r\n    aggregatedPointBetweenTicks() {\r\n        return \"crossTicks\" === this._options.aggregatedPointsPosition\r\n    },\r\n    resetApplyingAnimation: function(isFirstDrawing) {\r\n        this._resetApplyingAnimation = true;\r\n        if (isFirstDrawing) {\r\n            this._firstDrawing = true\r\n        }\r\n    },\r\n    isFirstDrawing() {\r\n        return this._firstDrawing\r\n    },\r\n    getMargins: function() {\r\n        const that = this;\r\n        const {\r\n            position: position,\r\n            offset: offset,\r\n            customPosition: customPosition,\r\n            placeholderSize: placeholderSize,\r\n            grid: grid,\r\n            tick: tick,\r\n            crosshairMargin: crosshairMargin\r\n        } = that._options;\r\n        const isDefinedCustomPositionOption = isDefined(customPosition);\r\n        const boundaryPosition = that.getResolvedBoundaryPosition();\r\n        const canvas = that.getCanvas();\r\n        const cLeft = canvas.left;\r\n        const cTop = canvas.top;\r\n        const cRight = canvas.width - canvas.right;\r\n        const cBottom = canvas.height - canvas.bottom;\r\n        const edgeMarginCorrection = _max(grid.visible && grid.width || 0, tick.visible && tick.width || 0);\r\n        const constantLineAboveSeries = that._axisConstantLineGroups.above;\r\n        const constantLineUnderSeries = that._axisConstantLineGroups.under;\r\n        const boxes = [that._axisElementsGroup, constantLineAboveSeries.outside1, constantLineAboveSeries.outside2, constantLineUnderSeries.outside1, constantLineUnderSeries.outside2, that._axisLineGroup].map((group => group && group.getBBox())).concat(function(group) {\r\n            const box = group && group.getBBox();\r\n            if (!box || box.isEmpty) {\r\n                return box\r\n            }\r\n            if (that._isHorizontal) {\r\n                box.x = cLeft;\r\n                box.width = cRight - cLeft\r\n            } else {\r\n                box.y = cTop;\r\n                box.height = cBottom - cTop\r\n            }\r\n            return box\r\n        }(that._axisTitleGroup));\r\n        const margins = calculateCanvasMargins(boxes, canvas);\r\n        margins[position] += crosshairMargin;\r\n        if (that.hasNonBoundaryPosition() && isDefinedCustomPositionOption) {\r\n            margins[boundaryPosition] = 0\r\n        }\r\n        if (placeholderSize) {\r\n            margins[position] = placeholderSize\r\n        }\r\n        if (edgeMarginCorrection) {\r\n            if (that._isHorizontal && canvas.right < edgeMarginCorrection && margins.right < edgeMarginCorrection) {\r\n                margins.right = edgeMarginCorrection\r\n            }\r\n            if (!that._isHorizontal && canvas.bottom < edgeMarginCorrection && margins.bottom < edgeMarginCorrection) {\r\n                margins.bottom = edgeMarginCorrection\r\n            }\r\n        }\r\n        if (!isDefinedCustomPositionOption && isDefined(offset)) {\r\n            const moveByOffset = that.customPositionIsBoundary() && (offset > 0 && (boundaryPosition === LEFT || boundaryPosition === TOP) || offset < 0 && (boundaryPosition === RIGHT || boundaryPosition === BOTTOM));\r\n            margins[boundaryPosition] -= moveByOffset ? offset : 0\r\n        }\r\n        return margins\r\n    },\r\n    validateUnit: function(unit, idError, parameters) {\r\n        const that = this;\r\n        unit = that.parser(unit);\r\n        if (void 0 === unit && idError) {\r\n            that._incidentOccurred(idError, [parameters])\r\n        }\r\n        return unit\r\n    },\r\n    _setType: function(axisType, drawingType) {\r\n        let axisTypeMethods;\r\n        switch (axisType) {\r\n            case \"xyAxes\":\r\n                axisTypeMethods = xyMethods;\r\n                break;\r\n            case \"polarAxes\":\r\n                axisTypeMethods = polarMethods\r\n        }\r\n        extend(this, axisTypeMethods[drawingType])\r\n    },\r\n    _getSharpParam: function() {\r\n        return true\r\n    },\r\n    _disposeBreaksGroup: _noop,\r\n    dispose: function() {\r\n        [this._axisElementsGroup, this._axisStripGroup, this._axisGroup].forEach((function(g) {\r\n            g.dispose()\r\n        }));\r\n        this._strips = this._title = null;\r\n        this._axisStripGroup = this._axisConstantLineGroups = this._axisStripLabelGroup = this._axisBreaksGroup = null;\r\n        this._axisLineGroup = this._axisElementsGroup = this._axisGridGroup = null;\r\n        this._axisGroup = this._axisTitleGroup = null;\r\n        this._axesContainerGroup = this._stripsGroup = this._constantLinesGroup = this._labelsAxesGroup = null;\r\n        this._renderer = this._options = this._textOptions = this._textFontStyles = null;\r\n        this._translator = null;\r\n        this._majorTicks = this._minorTicks = null;\r\n        this._disposeBreaksGroup();\r\n        this._templatesRendered && this._templatesRendered.reject()\r\n    },\r\n    getOptions: function() {\r\n        return this._options\r\n    },\r\n    setPane: function(pane) {\r\n        this.pane = pane;\r\n        this._options.pane = pane\r\n    },\r\n    setTypes: function(type, axisType, typeSelector) {\r\n        this._options.type = type || this._options.type;\r\n        this._options[typeSelector] = axisType || this._options[typeSelector];\r\n        this._updateTranslator()\r\n    },\r\n    resetTypes: function(typeSelector) {\r\n        this._options.type = this._initTypes.type;\r\n        this._options[typeSelector] = this._initTypes[typeSelector]\r\n    },\r\n    getTranslator: function() {\r\n        return this._translator\r\n    },\r\n    updateOptions: function(options) {\r\n        const that = this;\r\n        const labelOpt = options.label;\r\n        validateAxisOptions(options);\r\n        that._options = options;\r\n        options.tick = options.tick || {};\r\n        options.minorTick = options.minorTick || {};\r\n        options.grid = options.grid || {};\r\n        options.minorGrid = options.minorGrid || {};\r\n        options.title = options.title || {};\r\n        options.marker = options.marker || {};\r\n        that._initTypes = {\r\n            type: options.type,\r\n            argumentType: options.argumentType,\r\n            valueType: options.valueType\r\n        };\r\n        that._setTickOffset();\r\n        that._isHorizontal = options.isHorizontal;\r\n        that.pane = options.pane;\r\n        that.name = options.name;\r\n        that.priority = options.priority;\r\n        that._hasLabelFormat = \"\" !== labelOpt.format && isDefined(labelOpt.format);\r\n        that._textOptions = {\r\n            opacity: labelOpt.opacity,\r\n            align: \"center\",\r\n            class: labelOpt.cssClass\r\n        };\r\n        that._textFontStyles = patchFontOptions(labelOpt.font);\r\n        if (options.type === constants.logarithmic) {\r\n            if (options.logarithmBaseError) {\r\n                that._incidentOccurred(\"E2104\");\r\n                delete options.logarithmBaseError\r\n            }\r\n        }\r\n        that._updateTranslator();\r\n        that._createConstantLines();\r\n        that._strips = (options.strips || []).map((o => createStrip(that, o)));\r\n        that._majorTicks = that._minorTicks = null;\r\n        that._firstDrawing = true\r\n    },\r\n    calculateInterval: function(value, prevValue) {\r\n        const options = this._options;\r\n        if (!options || options.type !== constants.logarithmic) {\r\n            return _abs(value - prevValue)\r\n        }\r\n        const {\r\n            allowNegatives: allowNegatives,\r\n            linearThreshold: linearThreshold\r\n        } = new Range(this.getTranslator().getBusinessRange());\r\n        return _abs(getLog(value, options.logarithmBase, allowNegatives, linearThreshold) - getLog(prevValue, options.logarithmBase, allowNegatives, linearThreshold))\r\n    },\r\n    getCanvasRange() {\r\n        const translator = this._translator;\r\n        return {\r\n            startValue: translator.from(translator.translate(\"canvas_position_start\")),\r\n            endValue: translator.from(translator.translate(\"canvas_position_end\"))\r\n        }\r\n    },\r\n    _processCanvas: function(canvas) {\r\n        return canvas\r\n    },\r\n    updateCanvas: function(canvas, canvasRedesign) {\r\n        if (!canvasRedesign) {\r\n            const positions = this._orthogonalPositions = {\r\n                start: !this._isHorizontal ? canvas.left : canvas.top,\r\n                end: !this._isHorizontal ? canvas.width - canvas.right : canvas.height - canvas.bottom\r\n            };\r\n            positions.center = positions.start + (positions.end - positions.start) / 2\r\n        } else {\r\n            this._orthogonalPositions = null\r\n        }\r\n        this._canvas = canvas;\r\n        this._translator.updateCanvas(this._processCanvas(canvas));\r\n        this._initAxisPositions()\r\n    },\r\n    getCanvas: function() {\r\n        return this._canvas\r\n    },\r\n    getAxisShift() {\r\n        return this._axisShift || 0\r\n    },\r\n    hideTitle: function() {\r\n        const that = this;\r\n        if (that._options.title.text) {\r\n            that._incidentOccurred(\"W2105\", [that._isHorizontal ? \"horizontal\" : \"vertical\"]);\r\n            that._axisTitleGroup.clear()\r\n        }\r\n    },\r\n    getTitle: function() {\r\n        return this._title\r\n    },\r\n    hideOuterElements: function() {\r\n        const that = this;\r\n        const options = that._options;\r\n        if ((options.label.visible || that._outsideConstantLines.length) && !that._translator.getBusinessRange().isEmpty()) {\r\n            that._incidentOccurred(\"W2106\", [that._isHorizontal ? \"horizontal\" : \"vertical\"]);\r\n            that._axisElementsGroup.clear();\r\n            callAction(that._outsideConstantLines, \"removeLabel\")\r\n        }\r\n    },\r\n    _resolveLogarithmicOptionsForRange(range) {\r\n        const options = this._options;\r\n        if (options.type === constants.logarithmic) {\r\n            range.addRange({\r\n                allowNegatives: void 0 !== options.allowNegatives ? options.allowNegatives : range.min <= 0\r\n            });\r\n            if (!isNaN(options.linearThreshold)) {\r\n                range.linearThreshold = options.linearThreshold\r\n            }\r\n        }\r\n    },\r\n    adjustViewport(businessRange) {\r\n        const options = this._options;\r\n        const isDiscrete = options.type === constants.discrete;\r\n        let categories = this._seriesData && this._seriesData.categories || [];\r\n        const wholeRange = this.adjustRange(getVizRangeObject(options.wholeRange));\r\n        const visualRange = this.getViewport() || {};\r\n        const result = new Range(businessRange);\r\n        this._addConstantLinesToRange(result);\r\n        let minDefined = isDefined(visualRange.startValue);\r\n        let maxDefined = isDefined(visualRange.endValue);\r\n        if (!isDiscrete) {\r\n            minDefined = minDefined && (!isDefined(wholeRange.endValue) || visualRange.startValue < wholeRange.endValue);\r\n            maxDefined = maxDefined && (!isDefined(wholeRange.startValue) || visualRange.endValue > wholeRange.startValue)\r\n        }\r\n        const minVisible = minDefined ? visualRange.startValue : result.minVisible;\r\n        const maxVisible = maxDefined ? visualRange.endValue : result.maxVisible;\r\n        if (!isDiscrete) {\r\n            result.min = wholeRange.startValue ?? result.min;\r\n            result.max = wholeRange.endValue ?? result.max\r\n        } else {\r\n            const categoriesInfo = getCategoriesInfo(categories, wholeRange.startValue, wholeRange.endValue);\r\n            categories = categoriesInfo.categories;\r\n            result.categories = categories\r\n        }\r\n        const adjustedVisualRange = adjustVisualRange({\r\n            axisType: options.type,\r\n            dataType: options.dataType,\r\n            base: options.logarithmBase\r\n        }, {\r\n            startValue: minDefined ? visualRange.startValue : void 0,\r\n            endValue: maxDefined ? visualRange.endValue : void 0,\r\n            length: visualRange.length\r\n        }, {\r\n            categories: categories,\r\n            min: wholeRange.startValue,\r\n            max: wholeRange.endValue\r\n        }, {\r\n            categories: categories,\r\n            min: minVisible,\r\n            max: maxVisible\r\n        });\r\n        result.minVisible = adjustedVisualRange.startValue;\r\n        result.maxVisible = adjustedVisualRange.endValue;\r\n        !isDefined(result.min) && (result.min = result.minVisible);\r\n        !isDefined(result.max) && (result.max = result.maxVisible);\r\n        result.addRange({});\r\n        this._resolveLogarithmicOptionsForRange(result);\r\n        return result\r\n    },\r\n    adjustRange(range) {\r\n        range = range || {};\r\n        const isDiscrete = this._options.type === constants.discrete;\r\n        const isLogarithmic = this._options.type === constants.logarithmic;\r\n        const disabledNegatives = false === this._options.allowNegatives;\r\n        if (isLogarithmic) {\r\n            range.startValue = disabledNegatives && range.startValue <= 0 ? null : range.startValue;\r\n            range.endValue = disabledNegatives && range.endValue <= 0 ? null : range.endValue\r\n        }\r\n        if (!isDiscrete && isDefined(range.startValue) && isDefined(range.endValue) && range.startValue > range.endValue) {\r\n            const tmp = range.endValue;\r\n            range.endValue = range.startValue;\r\n            range.startValue = tmp\r\n        }\r\n        return range\r\n    },\r\n    _getVisualRangeUpdateMode(viewport, newRange, oppositeValue) {\r\n        let value = this._options.visualRangeUpdateMode;\r\n        const translator = this._translator;\r\n        const range = this._seriesData;\r\n        const prevDataInfo = this._prevDataInfo;\r\n        if (prevDataInfo.isEmpty && !prevDataInfo.containsConstantLine) {\r\n            return KEEP\r\n        }\r\n        if (!this.isArgumentAxis) {\r\n            const viewport = this.getViewport();\r\n            const isViewportNotDefined = !isDefined(viewport.startValue) && !isDefined(viewport.endValue) && !isDefined(viewport.length);\r\n            if (isViewportNotDefined) {\r\n                const visualRange = this.visualRange();\r\n                const isVisualRangeNotDefined = !isDefined(visualRange.startValue) && !isDefined(visualRange.endValue);\r\n                if (isVisualRangeNotDefined) {\r\n                    return RESET\r\n                }\r\n            }\r\n        }\r\n        if (this.isArgumentAxis) {\r\n            if (-1 === [SHIFT, KEEP, RESET].indexOf(value)) {\r\n                if (range.axisType === constants.discrete) {\r\n                    const categories = range.categories;\r\n                    const newCategories = newRange.categories;\r\n                    const visualRange = this.visualRange();\r\n                    if (categories && newCategories && categories.length && -1 !== newCategories.map((c => c.valueOf())).join(\",\").indexOf(categories.map((c => c.valueOf())).join(\",\")) && (visualRange.startValue.valueOf() !== categories[0].valueOf() || visualRange.endValue.valueOf() !== categories[categories.length - 1].valueOf())) {\r\n                        value = KEEP\r\n                    } else {\r\n                        value = RESET\r\n                    }\r\n                } else {\r\n                    const minPoint = translator.translate(range.min);\r\n                    const minVisiblePoint = translator.translate(viewport.startValue);\r\n                    const maxPoint = translator.translate(range.max);\r\n                    const maxVisiblePoint = translator.translate(viewport.endValue);\r\n                    if (minPoint === minVisiblePoint && maxPoint === maxVisiblePoint) {\r\n                        value = RESET\r\n                    } else if (minPoint !== minVisiblePoint && maxPoint === maxVisiblePoint) {\r\n                        value = SHIFT\r\n                    } else {\r\n                        value = KEEP\r\n                    }\r\n                }\r\n                if (value === KEEP && prevDataInfo.isEmpty && prevDataInfo.containsConstantLine) {\r\n                    value = RESET\r\n                }\r\n            }\r\n        } else if (-1 === [KEEP, RESET].indexOf(value)) {\r\n            if (oppositeValue === KEEP) {\r\n                value = KEEP\r\n            } else {\r\n                value = RESET\r\n            }\r\n        }\r\n        return value\r\n    },\r\n    _handleBusinessRangeChanged(oppositeVisualRangeUpdateMode, axisReinitialized, newRange) {\r\n        const that = this;\r\n        const visualRange = this.visualRange();\r\n        if (axisReinitialized || that._translator.getBusinessRange().isEmpty()) {\r\n            return\r\n        }\r\n        const visualRangeUpdateMode = that._lastVisualRangeUpdateMode = that._getVisualRangeUpdateMode(visualRange, newRange, oppositeVisualRangeUpdateMode);\r\n        if (visualRangeUpdateMode === KEEP) {\r\n            that._setVisualRange([visualRange.startValue, visualRange.endValue])\r\n        } else if (visualRangeUpdateMode === RESET) {\r\n            that._setVisualRange([null, null])\r\n        } else if (visualRangeUpdateMode === SHIFT) {\r\n            that._setVisualRange({\r\n                length: that.getVisualRangeLength()\r\n            })\r\n        }\r\n    },\r\n    getVisualRangeLength(range) {\r\n        const currentBusinessRange = range || this._translator.getBusinessRange();\r\n        const {\r\n            type: type\r\n        } = this._options;\r\n        let length;\r\n        if (type === constants.logarithmic) {\r\n            length = adjust(this.calculateInterval(currentBusinessRange.maxVisible, currentBusinessRange.minVisible))\r\n        } else if (type === constants.discrete) {\r\n            const categoriesInfo = getCategoriesInfo(currentBusinessRange.categories, currentBusinessRange.minVisible, currentBusinessRange.maxVisible);\r\n            length = categoriesInfo.categories.length\r\n        } else {\r\n            length = currentBusinessRange.maxVisible - currentBusinessRange.minVisible\r\n        }\r\n        return length\r\n    },\r\n    getVisualRangeCenter(range, useMerge) {\r\n        const translator = this.getTranslator();\r\n        const businessRange = translator.getBusinessRange();\r\n        const currentBusinessRange = useMerge ? extend(true, {}, businessRange, range || {}) : range || businessRange;\r\n        const {\r\n            type: type,\r\n            logarithmBase: logarithmBase\r\n        } = this._options;\r\n        let center;\r\n        if (!isDefined(currentBusinessRange.minVisible) || !isDefined(currentBusinessRange.maxVisible)) {\r\n            return\r\n        }\r\n        if (type === constants.logarithmic) {\r\n            const {\r\n                allowNegatives: allowNegatives,\r\n                linearThreshold: linearThreshold,\r\n                minVisible: minVisible,\r\n                maxVisible: maxVisible\r\n            } = currentBusinessRange;\r\n            center = raiseTo(adjust(getLog(maxVisible, logarithmBase, allowNegatives, linearThreshold) + getLog(minVisible, logarithmBase, allowNegatives, linearThreshold)) / 2, logarithmBase, allowNegatives, linearThreshold)\r\n        } else if (type === constants.discrete) {\r\n            const categoriesInfo = getCategoriesInfo(currentBusinessRange.categories, currentBusinessRange.minVisible, currentBusinessRange.maxVisible);\r\n            const index = Math.ceil(categoriesInfo.categories.length / 2) - 1;\r\n            center = businessRange.categories.indexOf(categoriesInfo.categories[index])\r\n        } else {\r\n            center = translator.toValue((currentBusinessRange.maxVisible.valueOf() + currentBusinessRange.minVisible.valueOf()) / 2)\r\n        }\r\n        return center\r\n    },\r\n    setBusinessRange(range, axisReinitialized, oppositeVisualRangeUpdateMode, argCategories) {\r\n        const that = this;\r\n        const options = that._options;\r\n        const isDiscrete = options.type === constants.discrete;\r\n        that._handleBusinessRangeChanged(oppositeVisualRangeUpdateMode, axisReinitialized, range);\r\n        that._seriesData = new Range(range);\r\n        const dataIsEmpty = that._seriesData.isEmpty();\r\n        const rangeWithConstantLines = new Range(that._seriesData);\r\n        that._addConstantLinesToRange(rangeWithConstantLines);\r\n        that._prevDataInfo = {\r\n            isEmpty: dataIsEmpty,\r\n            containsConstantLine: rangeWithConstantLines.containsConstantLine\r\n        };\r\n        that._seriesData.addRange({\r\n            categories: options.categories,\r\n            dataType: options.dataType,\r\n            axisType: options.type,\r\n            base: options.logarithmBase,\r\n            invert: options.inverted\r\n        });\r\n        that._resolveLogarithmicOptionsForRange(that._seriesData);\r\n        if (!isDiscrete) {\r\n            if (!isDefined(that._seriesData.min) && !isDefined(that._seriesData.max)) {\r\n                const visualRange = that.getViewport();\r\n                visualRange && that._seriesData.addRange({\r\n                    min: visualRange.startValue,\r\n                    max: visualRange.endValue\r\n                })\r\n            }\r\n            const synchronizedValue = options.synchronizedValue;\r\n            if (isDefined(synchronizedValue)) {\r\n                that._seriesData.addRange({\r\n                    min: synchronizedValue,\r\n                    max: synchronizedValue\r\n                })\r\n            }\r\n        }\r\n        that._seriesData.minVisible = that._seriesData.minVisible ?? that._seriesData.min;\r\n        that._seriesData.maxVisible = that._seriesData.maxVisible ?? that._seriesData.max;\r\n        if (!that.isArgumentAxis && options.showZero) {\r\n            that._seriesData.correctValueZeroLevel()\r\n        }\r\n        that._seriesData.sortCategories(that.getCategoriesSorter(argCategories));\r\n        that._seriesData.userBreaks = that._seriesData.isEmpty() ? [] : that._getScaleBreaks(options, that._seriesData, that._series, that.isArgumentAxis);\r\n        that._translator.updateBusinessRange(that._getViewportRange())\r\n    },\r\n    _addConstantLinesToRange(dataRange) {\r\n        this._outsideConstantLines.concat(this._insideConstantLines || []).forEach((cl => {\r\n            if (cl.options.extendAxis) {\r\n                const value = cl.getParsedValue();\r\n                dataRange.addRange({\r\n                    containsConstantLine: true,\r\n                    minVisible: value,\r\n                    maxVisible: value,\r\n                    min: !isDefined(dataRange.min) ? value : dataRange.min,\r\n                    max: !isDefined(dataRange.max) ? value : dataRange.max\r\n                })\r\n            }\r\n        }))\r\n    },\r\n    setGroupSeries: function(series) {\r\n        this._series = series\r\n    },\r\n    getLabelsPosition: function() {\r\n        const options = this._options;\r\n        const position = options.position;\r\n        const labelShift = options.label.indentFromAxis + (this._axisShift || 0) + this._constantLabelOffset;\r\n        const axisPosition = this._axisPosition;\r\n        return position === TOP || position === LEFT ? axisPosition - labelShift : axisPosition + labelShift\r\n    },\r\n    getFormattedValue: function(value, options, point) {\r\n        const labelOptions = this._options.label;\r\n        return isDefined(value) ? this.formatLabel(value, extend(true, {}, labelOptions, options), void 0, point) : null\r\n    },\r\n    _getBoundaryTicks: function(majors, viewPort) {\r\n        const that = this;\r\n        const length = majors.length;\r\n        const options = that._options;\r\n        const customBounds = options.customBoundTicks;\r\n        const min = viewPort.minVisible;\r\n        const max = viewPort.maxVisible;\r\n        const addMinMax = options.showCustomBoundaryTicks ? that._boundaryTicksVisibility : {};\r\n        let boundaryTicks = [];\r\n        if (options.type === constants.discrete) {\r\n            if (that._tickOffset && 0 !== majors.length) {\r\n                boundaryTicks = [majors[0], majors[majors.length - 1]]\r\n            }\r\n        } else if (customBounds) {\r\n            if (addMinMax.min && isDefined(customBounds[0])) {\r\n                boundaryTicks.push(customBounds[0])\r\n            }\r\n            if (addMinMax.max && isDefined(customBounds[1])) {\r\n                boundaryTicks.push(customBounds[1])\r\n            }\r\n        } else {\r\n            if (addMinMax.min && (0 === length || majors[0] > min)) {\r\n                boundaryTicks.push(min)\r\n            }\r\n            if (addMinMax.max && (0 === length || majors[length - 1] < max)) {\r\n                boundaryTicks.push(max)\r\n            }\r\n        }\r\n        return boundaryTicks\r\n    },\r\n    setPercentLabelFormat: function() {\r\n        if (!this._hasLabelFormat) {\r\n            this._options.label.format = \"percent\"\r\n        }\r\n    },\r\n    resetAutoLabelFormat: function() {\r\n        if (!this._hasLabelFormat) {\r\n            delete this._options.label.format\r\n        }\r\n    },\r\n    getMultipleAxesSpacing: function() {\r\n        return this._options.multipleAxesSpacing || 0\r\n    },\r\n    getTicksValues: function() {\r\n        return {\r\n            majorTicksValues: convertTicksToValues(this._majorTicks),\r\n            minorTicksValues: convertTicksToValues(this._minorTicks)\r\n        }\r\n    },\r\n    estimateTickInterval: function(canvas) {\r\n        this.updateCanvas(canvas);\r\n        return this._tickInterval !== this._getTicks(this._getViewportRange(), _noop, true).tickInterval\r\n    },\r\n    setTicks: function(ticks) {\r\n        const majors = ticks.majorTicks || [];\r\n        this._majorTicks = majors.map(createMajorTick(this, this._renderer, this._getSkippedCategory(majors)));\r\n        this._minorTicks = (ticks.minorTicks || []).map(createMinorTick(this, this._renderer));\r\n        this._isSynchronized = true\r\n    },\r\n    _adjustDivisionFactor: function(val) {\r\n        return val\r\n    },\r\n    _getTicks: function(viewPort, incidentOccurred, skipTickGeneration) {\r\n        const options = this._options;\r\n        const customTicks = options.customTicks;\r\n        const customMinorTicks = options.customMinorTicks;\r\n        return getTickGenerator(options, incidentOccurred || this._incidentOccurred, skipTickGeneration, this._translator.getBusinessRange().isEmpty(), this._adjustDivisionFactor.bind(this), viewPort)({\r\n            min: viewPort.minVisible,\r\n            max: viewPort.maxVisible,\r\n            categories: viewPort.categories,\r\n            isSpacedMargin: viewPort.isSpacedMargin\r\n        }, this._getScreenDelta(), options.tickInterval, \"ignore\" === options.label.overlappingBehavior || options.forceUserTickInterval, {\r\n            majors: customTicks,\r\n            minors: customMinorTicks\r\n        }, options.minorTickInterval, options.minorTickCount, this._initialBreaks)\r\n    },\r\n    _createTicksAndLabelFormat: function(range, incidentOccurred) {\r\n        const options = this._options;\r\n        const ticks = this._getTicks(range, incidentOccurred, false);\r\n        if (!range.isEmpty() && options.type === constants.discrete && \"datetime\" === options.dataType && !this._hasLabelFormat && ticks.ticks.length) {\r\n            options.label.format = formatHelper.getDateFormatByTicks(ticks.ticks)\r\n        }\r\n        return ticks\r\n    },\r\n    getAggregationInfo(useAllAggregatedPoints, range) {\r\n        var _that$_seriesData;\r\n        const options = this._options;\r\n        const businessRange = new Range(this.getTranslator().getBusinessRange()).addRange(range);\r\n        const visualRange = this.getViewport();\r\n        const minVisible = (null === visualRange || void 0 === visualRange ? void 0 : visualRange.startValue) ?? businessRange.minVisible;\r\n        const maxVisible = (null === visualRange || void 0 === visualRange ? void 0 : visualRange.endValue) ?? businessRange.maxVisible;\r\n        const aggregationInterval = options.aggregationInterval;\r\n        const aggregationGroupWidth = this._getAggregationGroupWidth();\r\n        const minInterval = !options.aggregationGroupWidth && !aggregationInterval && range.interval;\r\n        const generateTicks = configureGenerator(options, aggregationGroupWidth, businessRange, this._getScreenDelta(), minInterval);\r\n        const tickInterval = generateTicks(aggregationInterval, true, minVisible, maxVisible, null === (_that$_seriesData = this._seriesData) || void 0 === _that$_seriesData ? void 0 : _that$_seriesData.breaks).tickInterval;\r\n        const ticks = this._generateTick(useAllAggregatedPoints, businessRange, minVisible, maxVisible, tickInterval, generateTicks);\r\n        this._aggregationInterval = tickInterval;\r\n        return {\r\n            interval: tickInterval,\r\n            ticks: ticks\r\n        }\r\n    },\r\n    _getAggregationGroupWidth() {\r\n        const {\r\n            checkInterval: checkInterval,\r\n            sizePointNormalState: sizePointNormalState\r\n        } = this._marginOptions || {};\r\n        const {\r\n            aggregationGroupWidth: aggregationGroupWidth,\r\n            axisDivisionFactor: axisDivisionFactor\r\n        } = this._options;\r\n        if (aggregationGroupWidth) {\r\n            return aggregationGroupWidth\r\n        }\r\n        if (sizePointNormalState) {\r\n            return Math.min(sizePointNormalState, axisDivisionFactor)\r\n        }\r\n        if (checkInterval) {\r\n            return axisDivisionFactor\r\n        }\r\n        return aggregationGroupWidth\r\n    },\r\n    _generateTick(useAllAggregatedPoints, businessRange, minVisible, maxVisible, tickInterval, generateTicks) {\r\n        const min = useAllAggregatedPoints ? businessRange.min : minVisible;\r\n        const max = useAllAggregatedPoints ? businessRange.max : maxVisible;\r\n        if (!isDefined(min) || !isDefined(max)) {\r\n            return []\r\n        }\r\n        const that = this;\r\n        const options = that._options;\r\n        const add = getAddFunction({\r\n            base: options.logarithmBase,\r\n            axisType: options.type,\r\n            dataType: options.dataType\r\n        }, false);\r\n        let start = min;\r\n        let end = max;\r\n        if (!useAllAggregatedPoints && isDefined(tickInterval)) {\r\n            const maxMinDistance = Math.max(that.calculateInterval(max, min), \"datetime\" === options.dataType ? dateUtils.dateToMilliseconds(tickInterval) : tickInterval);\r\n            start = add(min, maxMinDistance, -1);\r\n            end = add(max, maxMinDistance)\r\n        }\r\n        start = start < businessRange.min ? businessRange.min : start;\r\n        end = end > businessRange.max ? businessRange.max : end;\r\n        const breaks = that._getScaleBreaks(options, {\r\n            minVisible: start,\r\n            maxVisible: end\r\n        }, that._series, that.isArgumentAxis);\r\n        const filteredBreaks = that._filterBreaks(breaks, {\r\n            minVisible: start,\r\n            maxVisible: end\r\n        }, options.breakStyle);\r\n        return generateTicks(tickInterval, false, start, end, filteredBreaks).ticks\r\n    },\r\n    getTickInterval() {\r\n        return this._tickInterval\r\n    },\r\n    getAggregationInterval() {\r\n        return this._aggregationInterval\r\n    },\r\n    createTicks: function(canvas) {\r\n        const that = this;\r\n        const renderer = that._renderer;\r\n        const options = that._options;\r\n        if (!canvas) {\r\n            return\r\n        }\r\n        that._isSynchronized = false;\r\n        that.updateCanvas(canvas);\r\n        const range = that._getViewportRange();\r\n        that._initialBreaks = range.breaks = this._seriesData.breaks = that._filterBreaks(this._seriesData.userBreaks, range, options.breakStyle);\r\n        that._estimatedTickInterval = that._getTicks(that.adjustViewport(this._seriesData), _noop, true).tickInterval;\r\n        const margins = this._calculateValueMargins();\r\n        range.addRange({\r\n            minVisible: margins.minValue,\r\n            maxVisible: margins.maxValue,\r\n            isSpacedMargin: margins.isSpacedMargin\r\n        });\r\n        const ticks = that._createTicksAndLabelFormat(range);\r\n        const boundaryTicks = that._getBoundaryTicks(ticks.ticks, that._getViewportRange());\r\n        if (options.showCustomBoundaryTicks && boundaryTicks.length) {\r\n            that._boundaryTicks = [boundaryTicks[0]].map(createBoundaryTick(that, renderer, true));\r\n            if (boundaryTicks.length > 1) {\r\n                that._boundaryTicks = that._boundaryTicks.concat([boundaryTicks[1]].map(createBoundaryTick(that, renderer, false)))\r\n            }\r\n        } else {\r\n            that._boundaryTicks = []\r\n        }\r\n        const minors = (ticks.minorTicks || []).filter((function(minor) {\r\n            return !boundaryTicks.some((function(boundary) {\r\n                return valueOf(boundary) === valueOf(minor)\r\n            }))\r\n        }));\r\n        that._tickInterval = ticks.tickInterval;\r\n        that._minorTickInterval = ticks.minorTickInterval;\r\n        const oldMajorTicks = that._majorTicks || [];\r\n        const majorTicksByValues = oldMajorTicks.reduce(((r, t) => {\r\n            r[t.value.valueOf()] = t;\r\n            return r\r\n        }), {});\r\n        const sameType = type(ticks.ticks[0]) === type(oldMajorTicks[0] && oldMajorTicks[0].value);\r\n        const skippedCategory = that._getSkippedCategory(ticks.ticks);\r\n        const majorTicks = ticks.ticks.map((v => {\r\n            const tick = majorTicksByValues[v.valueOf()];\r\n            if (tick && sameType) {\r\n                delete majorTicksByValues[v.valueOf()];\r\n                tick.setSkippedCategory(skippedCategory);\r\n                return tick\r\n            } else {\r\n                return createMajorTick(that, renderer, skippedCategory)(v)\r\n            }\r\n        }));\r\n        that._majorTicks = majorTicks;\r\n        const oldMinorTicks = that._minorTicks || [];\r\n        that._minorTicks = minors.map(((v, i) => {\r\n            const minorTick = oldMinorTicks[i];\r\n            if (minorTick) {\r\n                minorTick.updateValue(v);\r\n                return minorTick\r\n            }\r\n            return createMinorTick(that, renderer)(v)\r\n        }));\r\n        that._ticksToRemove = Object.keys(majorTicksByValues).map((k => majorTicksByValues[k])).concat(oldMinorTicks.slice(that._minorTicks.length, oldMinorTicks.length));\r\n        that._ticksToRemove.forEach((t => {\r\n            var _t$label;\r\n            return null === (_t$label = t.label) || void 0 === _t$label ? void 0 : _t$label.removeTitle()\r\n        }));\r\n        if (ticks.breaks) {\r\n            that._seriesData.breaks = ticks.breaks\r\n        }\r\n        that._reinitTranslator(that._getViewportRange())\r\n    },\r\n    _reinitTranslator: function(range) {\r\n        const translator = this._translator;\r\n        if (this._isSynchronized) {\r\n            return\r\n        }\r\n        translator.updateBusinessRange(range)\r\n    },\r\n    _getViewportRange() {\r\n        return this.adjustViewport(this._seriesData)\r\n    },\r\n    setMarginOptions: function(options) {\r\n        this._marginOptions = options\r\n    },\r\n    getMarginOptions() {\r\n        return this._marginOptions ?? {}\r\n    },\r\n    _calculateRangeInterval: function(interval) {\r\n        const isDateTime = \"datetime\" === this._options.dataType;\r\n        const minArgs = [];\r\n        const addToArgs = function(value) {\r\n            isDefined(value) && minArgs.push(isDateTime ? dateUtils.dateToMilliseconds(value) : value)\r\n        };\r\n        addToArgs(this._tickInterval);\r\n        addToArgs(this._estimatedTickInterval);\r\n        isDefined(interval) && minArgs.push(interval);\r\n        addToArgs(this._aggregationInterval);\r\n        return this._calculateWorkWeekInterval(_min.apply(this, minArgs))\r\n    },\r\n    _calculateWorkWeekInterval(businessInterval) {\r\n        const options = this._options;\r\n        if (\"datetime\" === options.dataType && options.workdaysOnly && businessInterval) {\r\n            const workWeek = options.workWeek.length * dateIntervals.day;\r\n            const weekend = dateIntervals.week - workWeek;\r\n            if (workWeek !== businessInterval && weekend < businessInterval) {\r\n                const weekendsCount = Math.ceil(businessInterval / dateIntervals.week);\r\n                businessInterval -= weekend * weekendsCount\r\n            } else if (weekend >= businessInterval && businessInterval > dateIntervals.day) {\r\n                businessInterval = dateIntervals.day\r\n            }\r\n        }\r\n        return businessInterval\r\n    },\r\n    _getConvertIntervalCoefficient(intervalInPx, screenDelta) {\r\n        const ratioOfCanvasRange = this._translator.ratioOfCanvasRange();\r\n        return ratioOfCanvasRange / (ratioOfCanvasRange * screenDelta / (intervalInPx + screenDelta))\r\n    },\r\n    _calculateValueMargins(ticks) {\r\n        this._resetMargins();\r\n        const that = this;\r\n        const margins = that.getMarginOptions();\r\n        const marginSize = (margins.size || 0) / 2;\r\n        const options = that._options;\r\n        const dataRange = that._getViewportRange();\r\n        const viewPort = that.getViewport();\r\n        const screenDelta = that._getScreenDelta();\r\n        const isDiscrete = -1 !== (options.type || \"\").indexOf(constants.discrete);\r\n        const valueMarginsEnabled = options.valueMarginsEnabled && !isDiscrete && !that.customPositionIsBoundaryOrthogonalAxis();\r\n        const translator = that._translator;\r\n        const minValueMargin = options.minValueMargin;\r\n        const maxValueMargin = options.maxValueMargin;\r\n        let minPadding = 0;\r\n        let maxPadding = 0;\r\n        let interval = 0;\r\n        let rangeInterval;\r\n        if (dataRange.stubData || !screenDelta) {\r\n            return {\r\n                startPadding: 0,\r\n                endPadding: 0\r\n            }\r\n        }\r\n        if (that.isArgumentAxis && margins.checkInterval) {\r\n            rangeInterval = that._calculateRangeInterval(dataRange.interval);\r\n            const pxInterval = translator.getInterval(rangeInterval);\r\n            if (isFinite(pxInterval)) {\r\n                interval = Math.ceil(pxInterval / (2 * that._getConvertIntervalCoefficient(pxInterval, screenDelta)))\r\n            } else {\r\n                rangeInterval = 0\r\n            }\r\n        }\r\n        let minPercentPadding;\r\n        let maxPercentPadding;\r\n        const maxPaddingValue = .8 * screenDelta / 2;\r\n        if (valueMarginsEnabled) {\r\n            if (isDefined(minValueMargin)) {\r\n                minPercentPadding = isFinite(minValueMargin) ? minValueMargin : 0\r\n            } else if (!that.isArgumentAxis && margins.checkInterval && valueOf(dataRange.minVisible) > 0 && valueOf(dataRange.minVisible) === valueOf(dataRange.min)) {\r\n                minPadding = 5\r\n            } else {\r\n                minPadding = Math.max(marginSize, interval);\r\n                minPadding = Math.min(maxPaddingValue, minPadding)\r\n            }\r\n            if (isDefined(maxValueMargin)) {\r\n                maxPercentPadding = isFinite(maxValueMargin) ? maxValueMargin : 0\r\n            } else if (!that.isArgumentAxis && margins.checkInterval && valueOf(dataRange.maxVisible) < 0 && valueOf(dataRange.maxVisible) === valueOf(dataRange.max)) {\r\n                maxPadding = 5\r\n            } else {\r\n                maxPadding = Math.max(marginSize, interval);\r\n                maxPadding = Math.min(maxPaddingValue, maxPadding)\r\n            }\r\n        }\r\n        const percentStick = margins.percentStick && !this.isArgumentAxis;\r\n        if (percentStick) {\r\n            if (1 === _abs(dataRange.max)) {\r\n                maxPadding = 0\r\n            }\r\n            if (1 === _abs(dataRange.min)) {\r\n                minPadding = 0\r\n            }\r\n        }\r\n        const canvasStartEnd = that._getCanvasStartEnd();\r\n        const commonMargin = 1 + (minPercentPadding || 0) + (maxPercentPadding || 0);\r\n        const screenDeltaWithMargins = (screenDelta - minPadding - maxPadding) / commonMargin || screenDelta;\r\n        if (void 0 !== minPercentPadding || void 0 !== maxPercentPadding) {\r\n            if (void 0 !== minPercentPadding) {\r\n                minPadding = screenDeltaWithMargins * minPercentPadding\r\n            }\r\n            if (void 0 !== maxPercentPadding) {\r\n                maxPadding = screenDeltaWithMargins * maxPercentPadding\r\n            }\r\n        }\r\n        let minValue;\r\n        let maxValue;\r\n        if (options.type !== constants.discrete && ticks && ticks.length > 1 && !options.skipViewportExtending && !viewPort.action && false !== options.endOnTick) {\r\n            const length = ticks.length;\r\n            const firstTickPosition = translator.translate(ticks[0].value);\r\n            const lastTickPosition = translator.translate(ticks[length - 1].value);\r\n            const invertMultiplier = firstTickPosition > lastTickPosition ? -1 : 1;\r\n            const minTickPadding = _max(invertMultiplier * (canvasStartEnd.start - firstTickPosition), 0);\r\n            const maxTickPadding = _max(invertMultiplier * (lastTickPosition - canvasStartEnd.end), 0);\r\n            if (minTickPadding > minPadding || maxTickPadding > maxPadding) {\r\n                const commonPadding = maxTickPadding + minTickPadding;\r\n                const coeff = that._getConvertIntervalCoefficient(commonPadding, screenDelta);\r\n                if (minTickPadding >= minPadding) {\r\n                    minValue = ticks[0].value\r\n                }\r\n                if (maxTickPadding >= maxPadding) {\r\n                    maxValue = ticks[length - 1].value\r\n                }\r\n                minPadding = _max(minTickPadding, minPadding) / coeff;\r\n                maxPadding = _max(maxTickPadding, maxPadding) / coeff\r\n            }\r\n        }\r\n        minPercentPadding = void 0 === minPercentPadding ? minPadding / screenDeltaWithMargins : minPercentPadding;\r\n        maxPercentPadding = void 0 === maxPercentPadding ? maxPadding / screenDeltaWithMargins : maxPercentPadding;\r\n        if (!isDiscrete) {\r\n            if (this._translator.isInverted()) {\r\n                minValue = minValue ?? translator.from(canvasStartEnd.start + screenDelta * minPercentPadding, -1);\r\n                maxValue = maxValue ?? translator.from(canvasStartEnd.end - screenDelta * maxPercentPadding, 1)\r\n            } else {\r\n                minValue = minValue ?? translator.from(canvasStartEnd.start - screenDelta * minPercentPadding, -1);\r\n                maxValue = maxValue ?? translator.from(canvasStartEnd.end + screenDelta * maxPercentPadding, 1)\r\n            }\r\n        }\r\n        const {\r\n            correctedMin: correctedMin,\r\n            correctedMax: correctedMax,\r\n            start: start,\r\n            end: end\r\n        } = that.getCorrectedValuesToZero(minValue, maxValue);\r\n        minPadding = start ?? minPadding;\r\n        maxPadding = end ?? maxPadding;\r\n        return {\r\n            startPadding: translator.isInverted() ? maxPadding : minPadding,\r\n            endPadding: translator.isInverted() ? minPadding : maxPadding,\r\n            minValue: correctedMin ?? minValue,\r\n            maxValue: correctedMax ?? maxValue,\r\n            interval: rangeInterval,\r\n            isSpacedMargin: minPadding === maxPadding && 0 !== minPadding\r\n        }\r\n    },\r\n    getCorrectedValuesToZero(minValue, maxValue) {\r\n        const that = this;\r\n        const translator = that._translator;\r\n        const canvasStartEnd = that._getCanvasStartEnd();\r\n        const dataRange = that._getViewportRange();\r\n        const screenDelta = that._getScreenDelta();\r\n        const options = that._options;\r\n        let start;\r\n        let end;\r\n        let correctedMin;\r\n        let correctedMax;\r\n        const correctZeroLevel = (minPoint, maxPoint) => {\r\n            const minExpectedPadding = _abs(canvasStartEnd.start - minPoint);\r\n            const maxExpectedPadding = _abs(canvasStartEnd.end - maxPoint);\r\n            const coeff = that._getConvertIntervalCoefficient(minExpectedPadding + maxExpectedPadding, screenDelta);\r\n            start = minExpectedPadding / coeff;\r\n            end = maxExpectedPadding / coeff\r\n        };\r\n        if (!that.isArgumentAxis && \"datetime\" !== options.dataType) {\r\n            if (minValue * dataRange.min <= 0 && minValue * dataRange.minVisible <= 0) {\r\n                correctZeroLevel(translator.translate(0), translator.translate(maxValue));\r\n                correctedMin = 0\r\n            }\r\n            if (maxValue * dataRange.max <= 0 && maxValue * dataRange.maxVisible <= 0) {\r\n                correctZeroLevel(translator.translate(minValue), translator.translate(0));\r\n                correctedMax = 0\r\n            }\r\n        }\r\n        return {\r\n            start: isFinite(start) ? start : null,\r\n            end: isFinite(end) ? end : null,\r\n            correctedMin: correctedMin,\r\n            correctedMax: correctedMax\r\n        }\r\n    },\r\n    applyMargins() {\r\n        if (this._isSynchronized) {\r\n            return\r\n        }\r\n        const margins = this._calculateValueMargins(this._majorTicks);\r\n        const canvas = extend({}, this._canvas, {\r\n            startPadding: margins.startPadding,\r\n            endPadding: margins.endPadding\r\n        });\r\n        this._translator.updateCanvas(this._processCanvas(canvas));\r\n        if (isFinite(margins.interval)) {\r\n            const br = this._translator.getBusinessRange();\r\n            br.addRange({\r\n                interval: margins.interval\r\n            });\r\n            this._translator.updateBusinessRange(br)\r\n        }\r\n    },\r\n    _resetMargins: function() {\r\n        this._reinitTranslator(this._getViewportRange());\r\n        if (this._canvas) {\r\n            this._translator.updateCanvas(this._processCanvas(this._canvas))\r\n        }\r\n    },\r\n    _createConstantLines() {\r\n        const constantLines = (this._options.constantLines || []).map((o => createConstantLine(this, o)));\r\n        this._outsideConstantLines = constantLines.filter((l => \"outside\" === l.labelPosition));\r\n        this._insideConstantLines = constantLines.filter((l => \"inside\" === l.labelPosition))\r\n    },\r\n    draw: function(canvas, borderOptions) {\r\n        const that = this;\r\n        const options = this._options;\r\n        that.borderOptions = borderOptions || {\r\n            visible: false\r\n        };\r\n        that._resetMargins();\r\n        that.createTicks(canvas);\r\n        that.applyMargins();\r\n        that._clearAxisGroups();\r\n        initTickCoords(that._majorTicks);\r\n        initTickCoords(that._minorTicks);\r\n        initTickCoords(that._boundaryTicks);\r\n        that._axisGroup.append(that._axesContainerGroup);\r\n        that._drawAxis();\r\n        that._drawTitle();\r\n        drawTickMarks(that._majorTicks, options.tick);\r\n        drawTickMarks(that._minorTicks, options.minorTick);\r\n        drawTickMarks(that._boundaryTicks, options.tick);\r\n        const drawGridLine = that._getGridLineDrawer();\r\n        drawGrids(that._majorTicks, drawGridLine);\r\n        drawGrids(that._minorTicks, drawGridLine);\r\n        callAction(that._majorTicks, \"drawLabel\", that._getViewportRange(), that._getTemplate(options.label.template));\r\n        that._templatesRendered && that._templatesRendered.reject();\r\n        that._templatesRendered = new Deferred;\r\n        that._majorTicks.forEach((function(tick) {\r\n            tick.labelRotationAngle = 0;\r\n            tick.labelAlignment = void 0;\r\n            tick.labelOffset = 0\r\n        }));\r\n        callAction(that._outsideConstantLines.concat(that._insideConstantLines), \"draw\");\r\n        callAction(that._strips, \"draw\");\r\n        that._dateMarkers = that._drawDateMarkers() || [];\r\n        that._stripLabelAxesGroup && that._axisStripLabelGroup.append(that._stripLabelAxesGroup);\r\n        that._gridContainerGroup && that._axisGridGroup.append(that._gridContainerGroup);\r\n        that._stripsGroup && that._axisStripGroup.append(that._stripsGroup);\r\n        that._labelsAxesGroup && that._axisElementsGroup.append(that._labelsAxesGroup);\r\n        if (that._constantLinesGroup) {\r\n            that._axisConstantLineGroups.above.inside.append(that._constantLinesGroup.above);\r\n            that._axisConstantLineGroups.above.outside1.append(that._constantLinesGroup.above);\r\n            that._axisConstantLineGroups.above.outside2.append(that._constantLinesGroup.above);\r\n            that._axisConstantLineGroups.under.inside.append(that._constantLinesGroup.under);\r\n            that._axisConstantLineGroups.under.outside1.append(that._constantLinesGroup.under);\r\n            that._axisConstantLineGroups.under.outside2.append(that._constantLinesGroup.under)\r\n        }\r\n        that._measureTitle();\r\n        measureLabels(that._majorTicks);\r\n        !options.label.template && that._applyWordWrap();\r\n        measureLabels(that._outsideConstantLines);\r\n        measureLabels(that._insideConstantLines);\r\n        measureLabels(that._strips);\r\n        measureLabels(that._dateMarkers);\r\n        that._adjustConstantLineLabels(that._insideConstantLines);\r\n        that._adjustStripLabels();\r\n        let offset = that._constantLabelOffset = that._adjustConstantLineLabels(that._outsideConstantLines);\r\n        if (!that._translator.getBusinessRange().isEmpty()) {\r\n            that._setLabelsPlacement();\r\n            offset = that._adjustLabels(offset)\r\n        }\r\n        when.apply(this, that._majorTicks.map((tick => tick.getTemplateDeferred()))).done((() => {\r\n            that._templatesRendered.resolve()\r\n        }));\r\n        offset = that._adjustDateMarkers(offset);\r\n        that._adjustTitle(offset)\r\n    },\r\n    getTemplatesDef() {\r\n        return this._templatesRendered\r\n    },\r\n    setRenderedState(state) {\r\n        this._drawn = state\r\n    },\r\n    isRendered() {\r\n        return this._drawn\r\n    },\r\n    _applyWordWrap() {\r\n        const that = this;\r\n        let convertedTickInterval;\r\n        let textWidth;\r\n        let textHeight;\r\n        const options = this._options;\r\n        const tickInterval = that._tickInterval;\r\n        if (isDefined(tickInterval)) {\r\n            convertedTickInterval = that.getTranslator().getInterval(\"datetime\" === options.dataType ? dateUtils.dateToMilliseconds(tickInterval) : tickInterval)\r\n        }\r\n        const displayMode = that._validateDisplayMode(options.label.displayMode);\r\n        const overlappingMode = that._validateOverlappingMode(options.label.overlappingBehavior, displayMode);\r\n        const wordWrapMode = options.label.wordWrap || \"none\";\r\n        const overflowMode = options.label.textOverflow || \"none\";\r\n        if ((\"none\" !== wordWrapMode || \"none\" !== overflowMode) && displayMode !== ROTATE && overlappingMode !== ROTATE && \"auto\" !== overlappingMode) {\r\n            const usefulSpace = isDefined(options.placeholderSize) ? options.placeholderSize - options.label.indentFromAxis : void 0;\r\n            if (that._isHorizontal) {\r\n                textWidth = convertedTickInterval;\r\n                textHeight = usefulSpace\r\n            } else {\r\n                textWidth = usefulSpace;\r\n                textHeight = convertedTickInterval\r\n            }\r\n            let correctByWidth = false;\r\n            let correctByHeight = false;\r\n            if (textWidth) {\r\n                if (that._majorTicks.some((tick => tick.labelBBox.width > textWidth))) {\r\n                    correctByWidth = true\r\n                }\r\n            }\r\n            if (textHeight) {\r\n                if (that._majorTicks.some((tick => tick.labelBBox.height > textHeight))) {\r\n                    correctByHeight = true\r\n                }\r\n            }\r\n            if (correctByWidth || correctByHeight) {\r\n                that._majorTicks.forEach((tick => {\r\n                    tick.label && tick.label.setMaxSize(textWidth, textHeight, options.label)\r\n                }));\r\n                measureLabels(that._majorTicks)\r\n            }\r\n        }\r\n    },\r\n    _measureTitle: _noop,\r\n    animate() {\r\n        callAction(this._majorTicks, \"animateLabels\")\r\n    },\r\n    updateSize(canvas, animate) {\r\n        let updateTitle = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : true;\r\n        const that = this;\r\n        that.updateCanvas(canvas);\r\n        if (updateTitle) {\r\n            that._checkTitleOverflow();\r\n            that._measureTitle();\r\n            that._updateTitleCoords()\r\n        }\r\n        that._reinitTranslator(that._getViewportRange());\r\n        that.applyMargins();\r\n        const animationEnabled = !that._firstDrawing && animate;\r\n        const options = that._options;\r\n        initTickCoords(that._majorTicks);\r\n        initTickCoords(that._minorTicks);\r\n        initTickCoords(that._boundaryTicks);\r\n        if (that._resetApplyingAnimation && !that._firstDrawing) {\r\n            that._resetStartCoordinates()\r\n        }\r\n        cleanUpInvalidTicks(that._majorTicks);\r\n        cleanUpInvalidTicks(that._minorTicks);\r\n        cleanUpInvalidTicks(that._boundaryTicks);\r\n        if (that._axisElement) {\r\n            that._updateAxisElementPosition()\r\n        }\r\n        updateTicksPosition(that._majorTicks, options.tick, animationEnabled);\r\n        updateTicksPosition(that._minorTicks, options.minorTick, animationEnabled);\r\n        updateTicksPosition(that._boundaryTicks, options.tick);\r\n        callAction(that._majorTicks, \"updateLabelPosition\", animationEnabled);\r\n        that._outsideConstantLines.concat(that._insideConstantLines || []).forEach((l => l.updatePosition(animationEnabled)));\r\n        callAction(that._strips, \"updatePosition\", animationEnabled);\r\n        updateGridsPosition(that._majorTicks, animationEnabled);\r\n        updateGridsPosition(that._minorTicks, animationEnabled);\r\n        if (animationEnabled) {\r\n            callAction(that._ticksToRemove || [], \"fadeOutElements\")\r\n        }\r\n        that.prepareAnimation();\r\n        that._ticksToRemove = null;\r\n        if (!that._translator.getBusinessRange().isEmpty()) {\r\n            that._firstDrawing = false\r\n        }\r\n        that._resetApplyingAnimation = false;\r\n        that._updateLabelsPosition()\r\n    },\r\n    _updateLabelsPosition: _noop,\r\n    prepareAnimation() {\r\n        const action = \"saveCoords\";\r\n        callAction(this._majorTicks, action);\r\n        callAction(this._minorTicks, action);\r\n        callAction(this._insideConstantLines, action);\r\n        callAction(this._outsideConstantLines, action);\r\n        callAction(this._strips, action)\r\n    },\r\n    _resetStartCoordinates() {\r\n        const action = \"resetCoordinates\";\r\n        callAction(this._majorTicks, action);\r\n        callAction(this._minorTicks, action);\r\n        callAction(this._insideConstantLines, action);\r\n        callAction(this._outsideConstantLines, action);\r\n        callAction(this._strips, action)\r\n    },\r\n    applyClipRects: function(elementsClipID, canvasClipID) {\r\n        this._axisGroup.attr({\r\n            \"clip-path\": canvasClipID\r\n        });\r\n        this._axisStripGroup.attr({\r\n            \"clip-path\": elementsClipID\r\n        });\r\n        this._axisElementsGroup.attr({\r\n            \"clip-path\": canvasClipID\r\n        })\r\n    },\r\n    _validateVisualRange(optionValue) {\r\n        const range = getVizRangeObject(optionValue);\r\n        if (void 0 !== range.startValue) {\r\n            range.startValue = this.validateUnit(range.startValue)\r\n        }\r\n        if (void 0 !== range.endValue) {\r\n            range.endValue = this.validateUnit(range.endValue)\r\n        }\r\n        return convertVisualRangeObject(range, !_isArray(optionValue))\r\n    },\r\n    _validateOptions(options) {\r\n        options.wholeRange = this._validateVisualRange(options.wholeRange);\r\n        options.visualRange = options._customVisualRange = this._validateVisualRange(options._customVisualRange);\r\n        this._setVisualRange(options._customVisualRange)\r\n    },\r\n    validate() {\r\n        const options = this._options;\r\n        const dataType = this.isArgumentAxis ? options.argumentType : options.valueType;\r\n        const parser = dataType ? getParser(dataType) : function(unit) {\r\n            return unit\r\n        };\r\n        this.parser = parser;\r\n        options.dataType = dataType;\r\n        this._validateOptions(options)\r\n    },\r\n    resetVisualRange(isSilent) {\r\n        this._seriesData.minVisible = this._seriesData.min;\r\n        this._seriesData.maxVisible = this._seriesData.max;\r\n        this.handleZooming([null, null], {\r\n            start: !!isSilent,\r\n            end: !!isSilent\r\n        })\r\n    },\r\n    _setVisualRange(visualRange, allowPartialUpdate) {\r\n        const range = this.adjustRange(getVizRangeObject(visualRange));\r\n        if (allowPartialUpdate) {\r\n            isDefined(range.startValue) && (this._viewport.startValue = range.startValue);\r\n            isDefined(range.endValue) && (this._viewport.endValue = range.endValue)\r\n        } else {\r\n            this._viewport = range\r\n        }\r\n    },\r\n    _applyZooming(visualRange, allowPartialUpdate) {\r\n        this._resetVisualRangeOption();\r\n        this._setVisualRange(visualRange, allowPartialUpdate);\r\n        const viewPort = this.getViewport();\r\n        this._seriesData.userBreaks = this._getScaleBreaks(this._options, {\r\n            minVisible: viewPort.startValue,\r\n            maxVisible: viewPort.endValue\r\n        }, this._series, this.isArgumentAxis);\r\n        this._translator.updateBusinessRange(this._getViewportRange())\r\n    },\r\n    getZoomStartEventArg(event, actionType) {\r\n        return {\r\n            axis: this,\r\n            range: this.visualRange(),\r\n            cancel: false,\r\n            event: event,\r\n            actionType: actionType\r\n        }\r\n    },\r\n    _getZoomEndEventArg(previousRange, event, actionType, zoomFactor, shift) {\r\n        const newRange = this.visualRange();\r\n        return {\r\n            axis: this,\r\n            previousRange: previousRange,\r\n            range: newRange,\r\n            cancel: false,\r\n            event: event,\r\n            actionType: actionType,\r\n            zoomFactor: zoomFactor,\r\n            shift: shift,\r\n            rangeStart: newRange.startValue,\r\n            rangeEnd: newRange.endValue\r\n        }\r\n    },\r\n    getZoomBounds() {\r\n        const wholeRange = getVizRangeObject(this._options.wholeRange);\r\n        const range = this.getTranslator().getBusinessRange();\r\n        const secondPriorityRange = {\r\n            startValue: getZoomBoundValue(this._initRange.startValue, range.min),\r\n            endValue: getZoomBoundValue(this._initRange.endValue, range.max)\r\n        };\r\n        return {\r\n            startValue: getZoomBoundValue(wholeRange.startValue, secondPriorityRange.startValue),\r\n            endValue: getZoomBoundValue(wholeRange.endValue, secondPriorityRange.endValue)\r\n        }\r\n    },\r\n    setInitRange() {\r\n        this._initRange = {};\r\n        if (0 === Object.keys(this._options.wholeRange || {}).length) {\r\n            this._initRange = this.getZoomBounds()\r\n        }\r\n    },\r\n    _resetVisualRangeOption() {\r\n        this._options._customVisualRange = {}\r\n    },\r\n    getTemplatesGroups() {\r\n        const ticks = this._majorTicks;\r\n        if (ticks) {\r\n            return this._majorTicks.map((tick => tick.templateContainer)).filter((item => isDefined(item)))\r\n        } else {\r\n            return []\r\n        }\r\n    },\r\n    setCustomVisualRange(range) {\r\n        this._options._customVisualRange = range\r\n    },\r\n    visualRange() {\r\n        const that = this;\r\n        const args = arguments;\r\n        let visualRange;\r\n        if (0 === args.length) {\r\n            const adjustedRange = that._getAdjustedBusinessRange();\r\n            let startValue = adjustedRange.minVisible;\r\n            let endValue = adjustedRange.maxVisible;\r\n            if (that._options.type === constants.discrete) {\r\n                startValue = startValue ?? adjustedRange.categories[0];\r\n                endValue = endValue ?? adjustedRange.categories[adjustedRange.categories.length - 1];\r\n                return {\r\n                    startValue: startValue,\r\n                    endValue: endValue,\r\n                    categories: getCategoriesInfo(adjustedRange.categories, startValue, endValue).categories\r\n                }\r\n            }\r\n            return {\r\n                startValue: startValue,\r\n                endValue: endValue\r\n            }\r\n        } else if (_isArray(args[0])) {\r\n            visualRange = args[0]\r\n        } else if (isPlainObject(args[0])) {\r\n            visualRange = extend({}, args[0])\r\n        } else {\r\n            visualRange = [args[0], args[1]]\r\n        }\r\n        const zoomResults = that.handleZooming(visualRange, args[1]);\r\n        if (!zoomResults.isPrevented) {\r\n            that._visualRange(that, zoomResults)\r\n        }\r\n    },\r\n    handleZooming(visualRange, preventEvents, domEvent, action) {\r\n        const that = this;\r\n        preventEvents = preventEvents || {};\r\n        if (isDefined(visualRange)) {\r\n            visualRange = that._validateVisualRange(visualRange);\r\n            visualRange.action = action\r\n        }\r\n        const zoomStartEvent = that.getZoomStartEventArg(domEvent, action);\r\n        const previousRange = zoomStartEvent.range;\r\n        !preventEvents.start && that._eventTrigger(\"zoomStart\", zoomStartEvent);\r\n        const zoomResults = {\r\n            isPrevented: zoomStartEvent.cancel,\r\n            skipEventRising: preventEvents.skipEventRising,\r\n            range: visualRange || zoomStartEvent.range\r\n        };\r\n        if (!zoomStartEvent.cancel) {\r\n            isDefined(visualRange) && that._applyZooming(visualRange, preventEvents.allowPartialUpdate);\r\n            if (!isDefined(that._storedZoomEndParams)) {\r\n                that._storedZoomEndParams = {\r\n                    startRange: previousRange,\r\n                    type: this.getOptions().type\r\n                }\r\n            }\r\n            that._storedZoomEndParams.event = domEvent;\r\n            that._storedZoomEndParams.action = action;\r\n            that._storedZoomEndParams.prevent = !!preventEvents.end\r\n        }\r\n        return zoomResults\r\n    },\r\n    handleZoomEnd() {\r\n        const that = this;\r\n        if (isDefined(that._storedZoomEndParams) && !that._storedZoomEndParams.prevent) {\r\n            const previousRange = that._storedZoomEndParams.startRange;\r\n            const domEvent = that._storedZoomEndParams.event;\r\n            const action = that._storedZoomEndParams.action;\r\n            const previousBusinessRange = {\r\n                minVisible: previousRange.startValue,\r\n                maxVisible: previousRange.endValue,\r\n                categories: previousRange.categories\r\n            };\r\n            const typeIsNotChanged = that.getOptions().type === that._storedZoomEndParams.type;\r\n            const shift = typeIsNotChanged ? adjust(that.getVisualRangeCenter() - that.getVisualRangeCenter(previousBusinessRange, false)) : NaN;\r\n            const zoomFactor = typeIsNotChanged ? +(Math.round(that.getVisualRangeLength(previousBusinessRange) / (that.getVisualRangeLength() || 1) + \"e+2\") + \"e-2\") : NaN;\r\n            const zoomEndEvent = that._getZoomEndEventArg(previousRange, domEvent, action, zoomFactor, shift);\r\n            zoomEndEvent.cancel = that.checkZoomingLowerLimitOvercome(1 === zoomFactor ? \"pan\" : \"zoom\", zoomFactor).stopInteraction;\r\n            that._eventTrigger(\"zoomEnd\", zoomEndEvent);\r\n            if (zoomEndEvent.cancel) {\r\n                that._restorePreviousVisualRange(previousRange)\r\n            }\r\n            that._storedZoomEndParams = null\r\n        }\r\n    },\r\n    _restorePreviousVisualRange(previousRange) {\r\n        this._storedZoomEndParams = null;\r\n        this._applyZooming(previousRange);\r\n        this._visualRange(this, previousRange)\r\n    },\r\n    checkZoomingLowerLimitOvercome(actionType, zoomFactor, range) {\r\n        const that = this;\r\n        const options = that._options;\r\n        const translator = that._translator;\r\n        let minZoom = options.minVisualRangeLength;\r\n        let correctedRange = range;\r\n        let visualRange;\r\n        let isOvercoming = \"zoom\" === actionType && zoomFactor >= 1;\r\n        const businessRange = translator.getBusinessRange();\r\n        if (range) {\r\n            visualRange = that.adjustRange(getVizRangeObject(range));\r\n            visualRange = {\r\n                minVisible: visualRange.startValue,\r\n                maxVisible: visualRange.endValue,\r\n                categories: businessRange.categories\r\n            }\r\n        }\r\n        const beforeVisualRangeLength = that.getVisualRangeLength(businessRange);\r\n        const afterVisualRangeLength = that.getVisualRangeLength(visualRange);\r\n        if (isDefined(minZoom) || \"discrete\" === options.type) {\r\n            minZoom = translator.convert(minZoom);\r\n            if (visualRange && minZoom < beforeVisualRangeLength && minZoom >= afterVisualRangeLength) {\r\n                correctedRange = getVizRangeObject(translator.getRangeByMinZoomValue(minZoom, visualRange));\r\n                isOvercoming = false\r\n            } else {\r\n                isOvercoming &= minZoom > afterVisualRangeLength\r\n            }\r\n        } else {\r\n            const canvasLength = that._translator.canvasLength;\r\n            const fullRange = {\r\n                minVisible: businessRange.min,\r\n                maxVisible: businessRange.max,\r\n                categories: businessRange.categories\r\n            };\r\n            isOvercoming &= that.getVisualRangeLength(fullRange) / canvasLength >= afterVisualRangeLength\r\n        }\r\n        return {\r\n            stopInteraction: !!isOvercoming,\r\n            correctedRange: correctedRange\r\n        }\r\n    },\r\n    isExtremePosition(isMax) {\r\n        let extremeDataValue;\r\n        let seriesData;\r\n        if (\"discrete\" === this._options.type) {\r\n            seriesData = this._translator.getBusinessRange();\r\n            extremeDataValue = isMax ? seriesData.categories[seriesData.categories.length - 1] : seriesData.categories[0]\r\n        } else {\r\n            seriesData = this.getZoomBounds();\r\n            extremeDataValue = isMax ? seriesData.endValue : seriesData.startValue\r\n        }\r\n        const translator = this.getTranslator();\r\n        const extremePoint = translator.translate(extremeDataValue);\r\n        const visualRange = this.visualRange();\r\n        const visualRangePoint = isMax ? translator.translate(visualRange.endValue) : translator.translate(visualRange.startValue);\r\n        return _abs(visualRangePoint - extremePoint) < 5\r\n    },\r\n    getViewport() {\r\n        return this._viewport\r\n    },\r\n    getFullTicks: function() {\r\n        const majors = this._majorTicks || [];\r\n        if (this._options.type === constants.discrete) {\r\n            return convertTicksToValues(majors)\r\n        } else {\r\n            return convertTicksToValues(majors.concat(this._minorTicks, this._boundaryTicks)).sort((function(a, b) {\r\n                return valueOf(a) - valueOf(b)\r\n            }))\r\n        }\r\n    },\r\n    measureLabels: function(canvas, withIndents) {\r\n        const that = this;\r\n        const options = that._options;\r\n        const widthAxis = options.visible ? options.width : 0;\r\n        let ticks;\r\n        const indent = withIndents ? options.label.indentFromAxis + .5 * options.tick.length : 0;\r\n        let tickInterval;\r\n        const viewportRange = that._getViewportRange();\r\n        if (viewportRange.isEmpty() || !options.label.visible || !that._axisElementsGroup) {\r\n            return {\r\n                height: widthAxis,\r\n                width: widthAxis,\r\n                x: 0,\r\n                y: 0\r\n            }\r\n        }\r\n        if (that._majorTicks) {\r\n            ticks = convertTicksToValues(that._majorTicks)\r\n        } else {\r\n            that.updateCanvas(canvas);\r\n            ticks = that._createTicksAndLabelFormat(viewportRange, _noop);\r\n            tickInterval = ticks.tickInterval;\r\n            ticks = ticks.ticks\r\n        }\r\n        const maxText = ticks.reduce((function(prevLabel, tick, index) {\r\n            const label = that.formatLabel(tick, options.label, viewportRange, void 0, tickInterval, ticks);\r\n            if (prevLabel.length < label.length) {\r\n                return label\r\n            } else {\r\n                return prevLabel\r\n            }\r\n        }), that.formatLabel(ticks[0], options.label, viewportRange, void 0, tickInterval, ticks));\r\n        const text = that._renderer.text(maxText, 0, 0).css(that._textFontStyles).attr(that._textOptions).append(that._renderer.root);\r\n        const box = text.getBBox();\r\n        text.remove();\r\n        return {\r\n            x: box.x,\r\n            y: box.y,\r\n            width: box.width + indent,\r\n            height: box.height + indent\r\n        }\r\n    },\r\n    _setLabelsPlacement: function() {\r\n        if (!this._options.label.visible) {\r\n            return\r\n        }\r\n        const that = this;\r\n        const labelOpt = that._options.label;\r\n        const displayMode = that._validateDisplayMode(labelOpt.displayMode);\r\n        const overlappingMode = that._validateOverlappingMode(labelOpt.overlappingBehavior, displayMode);\r\n        const ignoreOverlapping = \"none\" === overlappingMode || \"ignore\" === overlappingMode;\r\n        const behavior = {\r\n            rotationAngle: labelOpt.rotationAngle,\r\n            staggeringSpacing: labelOpt.staggeringSpacing\r\n        };\r\n        let notRecastStep;\r\n        const boxes = that._majorTicks.map((function(tick) {\r\n            return tick.labelBBox\r\n        }));\r\n        let step = that._getStep(boxes);\r\n        switch (displayMode) {\r\n            case ROTATE:\r\n                if (ignoreOverlapping) {\r\n                    notRecastStep = true;\r\n                    step = 1\r\n                }\r\n                that._applyLabelMode(displayMode, step, boxes, labelOpt, notRecastStep);\r\n                break;\r\n            case \"stagger\":\r\n                if (ignoreOverlapping) {\r\n                    step = 2\r\n                }\r\n                that._applyLabelMode(displayMode, _max(step, 2), boxes, labelOpt);\r\n                break;\r\n            default:\r\n                that._applyLabelOverlapping(boxes, overlappingMode, step, behavior)\r\n        }\r\n    },\r\n    _applyLabelOverlapping: function(boxes, mode, step, behavior) {\r\n        const that = this;\r\n        const labelOpt = that._options.label;\r\n        const majorTicks = that._majorTicks;\r\n        if (\"none\" === mode || \"ignore\" === mode) {\r\n            return\r\n        }\r\n        if (step > 1 && boxes.some((function(box, index, array) {\r\n                if (0 === index) {\r\n                    return false\r\n                }\r\n                return constants.areLabelsOverlap(box, array[index - 1], labelOpt.minSpacing, labelOpt.alignment)\r\n            }))) {\r\n            that._applyLabelMode(mode, step, boxes, behavior)\r\n        }\r\n        that._checkBoundedLabelsOverlapping(majorTicks, boxes, mode);\r\n        that._checkShiftedLabels(majorTicks, boxes, labelOpt.minSpacing, labelOpt.alignment)\r\n    },\r\n    _applyLabelMode: function(mode, step, boxes, behavior, notRecastStep) {\r\n        const that = this;\r\n        const majorTicks = that._majorTicks;\r\n        const labelOpt = that._options.label;\r\n        const angle = behavior.rotationAngle;\r\n        let labelHeight;\r\n        let alignment;\r\n        let func;\r\n        switch (mode) {\r\n            case ROTATE:\r\n                if (!labelOpt.userAlignment) {\r\n                    alignment = angle < 0 ? RIGHT : LEFT;\r\n                    if (angle % 90 === 0) {\r\n                        alignment = CENTER\r\n                    }\r\n                }\r\n                step = notRecastStep ? step : that._getStep(boxes, angle);\r\n                func = function(tick) {\r\n                    const contentContainer = tick.getContentContainer();\r\n                    if (!contentContainer) {\r\n                        return\r\n                    }\r\n                    contentContainer.rotate(angle);\r\n                    tick.labelRotationAngle = angle;\r\n                    alignment && (tick.labelAlignment = alignment)\r\n                };\r\n                updateLabels(majorTicks, step, func);\r\n                break;\r\n            case \"stagger\":\r\n                labelHeight = that._getMaxLabelHeight(boxes, behavior.staggeringSpacing);\r\n                func = function(tick, index) {\r\n                    if (index / (step - 1) % 2 !== 0) {\r\n                        tick.labelOffset = labelHeight\r\n                    }\r\n                };\r\n                updateLabels(majorTicks, step - 1, func);\r\n                break;\r\n            case \"auto\":\r\n            case \"_auto\":\r\n                if (2 === step) {\r\n                    that._applyLabelMode(\"stagger\", step, boxes, behavior)\r\n                } else {\r\n                    that._applyLabelMode(ROTATE, step, boxes, {\r\n                        rotationAngle: getOptimalAngle(boxes, labelOpt)\r\n                    })\r\n                }\r\n                break;\r\n            default:\r\n                updateLabels(majorTicks, step)\r\n        }\r\n    },\r\n    getMarkerTrackers: _noop,\r\n    _drawDateMarkers: _noop,\r\n    _adjustDateMarkers: _noop,\r\n    coordsIn: _noop,\r\n    areCoordsOutsideAxis: _noop,\r\n    _getSkippedCategory: _noop,\r\n    _initAxisPositions: _noop,\r\n    _drawTitle: _noop,\r\n    _updateTitleCoords: _noop,\r\n    _adjustConstantLineLabels: _noop,\r\n    _createTranslator: function() {\r\n        return new Translator2D({}, {}, {})\r\n    },\r\n    _updateTranslator: function() {\r\n        const translator = this._translator;\r\n        translator.update(translator.getBusinessRange(), this._canvas || {}, this._getTranslatorOptions())\r\n    },\r\n    _getTranslatorOptions: function() {\r\n        var _options$workWeek2, _options$breakStyle;\r\n        const options = this._options;\r\n        return {\r\n            isHorizontal: this._isHorizontal,\r\n            shiftZeroValue: !this.isArgumentAxis,\r\n            interval: options.semiDiscreteInterval,\r\n            firstDayOfWeek: null === (_options$workWeek2 = options.workWeek) || void 0 === _options$workWeek2 ? void 0 : _options$workWeek2[0],\r\n            stick: this._getStick(),\r\n            breaksSize: (null === (_options$breakStyle = options.breakStyle) || void 0 === _options$breakStyle ? void 0 : _options$breakStyle.width) ?? 0\r\n        }\r\n    },\r\n    getVisibleArea() {\r\n        const canvas = this._getCanvasStartEnd();\r\n        return [canvas.start, canvas.end].sort(((a, b) => a - b))\r\n    },\r\n    _getCanvasStartEnd: function() {\r\n        const isHorizontal = this._isHorizontal;\r\n        const canvas = this._canvas || {};\r\n        const invert = this._translator.getBusinessRange().invert;\r\n        const coords = isHorizontal ? [canvas.left, canvas.width - canvas.right] : [canvas.height - canvas.bottom, canvas.top];\r\n        invert && coords.reverse();\r\n        return {\r\n            start: coords[0],\r\n            end: coords[1]\r\n        }\r\n    },\r\n    _getScreenDelta: function() {\r\n        const canvas = this._getCanvasStartEnd();\r\n        const breaks = this._seriesData ? this._seriesData.breaks || [] : [];\r\n        const breaksLength = breaks.length;\r\n        const screenDelta = _abs(canvas.start - canvas.end);\r\n        return screenDelta - (breaksLength ? breaks[breaksLength - 1].cumulativeWidth : 0)\r\n    },\r\n    _getScaleBreaks: function() {\r\n        return []\r\n    },\r\n    _filterBreaks: function() {\r\n        return []\r\n    },\r\n    _adjustTitle: _noop,\r\n    _checkTitleOverflow: _noop,\r\n    getSpiderTicks: _noop,\r\n    setSpiderTicks: _noop,\r\n    _checkBoundedLabelsOverlapping: _noop,\r\n    _checkShiftedLabels: _noop,\r\n    drawScaleBreaks: _noop,\r\n    _visualRange: _noop,\r\n    _rotateConstantLine: _noop,\r\n    applyVisualRangeSetter(visualRangeSetter) {\r\n        this._visualRange = visualRangeSetter\r\n    },\r\n    getCategoriesSorter(argCategories) {\r\n        let sort;\r\n        if (this.isArgumentAxis) {\r\n            sort = argCategories\r\n        } else {\r\n            const categoriesSortingMethod = this._options.categoriesSortingMethod;\r\n            sort = categoriesSortingMethod ?? this._options.categories\r\n        }\r\n        return sort\r\n    },\r\n    _getAdjustedBusinessRange() {\r\n        return this.adjustViewport(this._translator.getBusinessRange())\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAIA;AAYA;AAAA;AAMA;AACA;AAAA;AAGA;AACA;AAGA;AAGA;AAGA;AAGA;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AACA;AACA;AACA;AACA;AAAA;AAIA;;;;;;;;;;;;;;;;;;;;;AAIA,MAAM,uBAAuB,kKAAA,CAAA,UAAS,CAAC,oBAAoB;AAC3D,MAAM,QAAQ;AACd,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,WAAW,MAAM,OAAO;AAC9B,MAAM,6BAA6B;AACnC,MAAM,4BAA4B;AAClC,MAAM,MAAM,kKAAA,CAAA,UAAS,CAAC,GAAG;AACzB,MAAM,SAAS,kKAAA,CAAA,UAAS,CAAC,MAAM;AAC/B,MAAM,OAAO,kKAAA,CAAA,UAAS,CAAC,IAAI;AAC3B,MAAM,QAAQ,kKAAA,CAAA,UAAS,CAAC,KAAK;AAC7B,MAAM,SAAS,kKAAA,CAAA,UAAS,CAAC,MAAM;AAC/B,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,+BAA+B;AACrC,MAAM,qCAAqC;AAC3C,MAAM,mBAAmB;AACzB,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,gBAAgB;IAClB,KAAK;IACL,MAAM;AACV;AAEA,SAAS,iBAAiB,OAAO,EAAE,gBAAgB,EAAE,kBAAkB,EAAE,YAAY,EAAE,oBAAoB,EAAE,IAAI;IAC7G,IAAI;IACJ,IAAI,EACA,gBAAgB,cAAc,EAC9B,iBAAiB,eAAe,EACnC,GAAG;IACJ,OAAO,CAAA,GAAA,kKAAA,CAAA,gBAAa,AAAD,EAAE;QACjB,UAAU,QAAQ,IAAI;QACtB,UAAU,QAAQ,QAAQ;QAC1B,SAAS,QAAQ,aAAa;QAC9B,gBAAgB;QAChB,iBAAiB;QACjB,oBAAoB,qBAAqB,QAAQ,kBAAkB,IAAI;QACvE,yBAAyB,qBAAqB,QAAQ,uBAAuB,IAAI;QACjF,mBAAmB,QAAQ,iBAAiB;QAC5C,iBAAiB,QAAQ,SAAS,CAAC,OAAO,IAAI,QAAQ,SAAS,CAAC,OAAO,IAAI,QAAQ,eAAe;QAClG,eAAe,QAAQ,aAAa;QACpC,WAAW,QAAQ,SAAS;QAC5B,kBAAkB;QAClB,gBAAgB,SAAS,CAAC,oBAAoB,QAAQ,QAAQ,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,iBAAiB,CAAC,EAAE;QAC/H,oBAAoB;QACpB,uBAAuB,QAAQ,qBAAqB;QACpD,mBAAmB,QAAQ,iBAAiB;QAC5C,iBAAiB,QAAQ,eAAe;QACxC,cAAc;IAClB;AACJ;AAEA,SAAS,gBAAgB,IAAI,EAAE,QAAQ,EAAE,eAAe;IACpD,MAAM,UAAU,KAAK,UAAU;IAC/B,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,QAAQ,IAAI,EAAE,QAAQ,IAAI,EAAE,iBAAiB;AAC7E;AAEA,SAAS,gBAAgB,IAAI,EAAE,QAAQ;IACnC,MAAM,UAAU,KAAK,UAAU;IAC/B,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,QAAQ,SAAS,EAAE,QAAQ,SAAS;AACpE;AAEA,SAAS,mBAAmB,IAAI,EAAE,QAAQ,EAAE,OAAO;IAC/C,MAAM,UAAU,KAAK,UAAU;IAC/B,OAAO,CAAA,GAAA,wJAAA,CAAA,OAAI,AAAD,EAAE,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ,IAAI,EAAE;QACjD,SAAS,QAAQ,uBAAuB;IAC5C,IAAI,QAAQ,IAAI,EAAE,KAAK,GAAG,OAAO,UAAU,CAAC,IAAI;AACpD;AAEA,SAAS,WAAW,QAAQ,EAAE,MAAM,EAAE,eAAe,EAAE,eAAe;IAClE,CAAC,YAAY,EAAE,EAAE,OAAO,CAAE,CAAA,IAAK,CAAC,CAAC,OAAO,CAAC,iBAAiB;AAC9D;AAEA,SAAS,eAAe,KAAK;IACzB,WAAW,OAAO;AACtB;AAEA,SAAS,cAAc,KAAK,EAAE,OAAO;IACjC,WAAW,OAAO,YAAY;AAClC;AAEA,SAAS,UAAU,KAAK,EAAE,QAAQ;IAC9B,WAAW,OAAO,YAAY;AAClC;AAEA,SAAS,oBAAoB,KAAK,EAAE,OAAO,EAAE,OAAO;IAChD,WAAW,OAAO,sBAAsB,SAAS;AACrD;AAEA,SAAS,oBAAoB,KAAK,EAAE,OAAO;IACvC,WAAW,OAAO,sBAAsB;AAC5C;AAEA,SAAS,oBAAoB,KAAK;IAC9B,IAAI,IAAI,MAAM,MAAM,GAAG;IACvB,IAAK,GAAG,KAAK,GAAG,IAAK;QACjB,IAAI,CAAC,kBAAkB,OAAO,IAAI;YAC9B;QACJ;IACJ;IACA,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QAC/B,IAAI,kBAAkB,OAAO,IAAI;YAC7B;QACJ,OAAO;YACH;QACJ;IACJ;AACJ;AAEA,SAAS,kBAAkB,KAAK,EAAE,CAAC;IAC/B,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,IAAI,SAAS,KAAK,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC,EAAE;QAC1D,MAAM,MAAM,CAAC,GAAG;QAChB,OAAO;IACX;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,OAAO;IAChC,MAAM,eAAe,QAAQ,KAAK;IAClC,IAAI,WAAW,QAAQ,QAAQ;IAC/B,MAAM,kBAAkB,QAAQ,YAAY,GAAG,SAAS;IACxD,MAAM,oBAAoB,QAAQ,YAAY,GAAG,MAAM;IACvD,IAAI,gBAAgB,aAAa,QAAQ;IACzC,IAAI,aAAa,mBAAmB,aAAa,mBAAmB;QAChE,WAAW;IACf;IACA,IAAI,CAAC,iBAAiB,cAAc,eAAe;QAC/C,gBAAgB;IACpB,OAAO,IAAI,aAAa,eAAe;QACnC,gBAAgB,CAAA;YACZ,CAAC,IAAI,EAAE;YACP,CAAC,OAAO,EAAE;YACV,CAAC,KAAK,EAAE;YACR,CAAC,MAAM,EAAE;QACb,CAAA,CAAE,CAAC,SAAS;IAChB;IACA,IAAI,kBAAkB,mBAAmB,kBAAkB,mBAAmB;QAC1E,gBAAgB;IACpB;IACA,IAAI,aAAa,SAAS,KAAK,UAAU,CAAC,aAAa,aAAa,EAAE;QAClE,aAAa,SAAS,GAAG,CAAA;YACrB,CAAC,IAAI,EAAE;YACP,CAAC,OAAO,EAAE;YACV,CAAC,KAAK,EAAE;YACR,CAAC,MAAM,EAAE;QACb,CAAA,CAAE,CAAC,cAAc;IACrB;IACA,QAAQ,QAAQ,GAAG;IACnB,aAAa,QAAQ,GAAG;IACxB,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,WAAW,KAAK;IAC1E,aAAa,UAAU,GAAG,aAAa,UAAU,IAAI;IACrD,QAAQ,IAAI,IAAI,CAAC,QAAQ,IAAI,GAAG,QAAQ,IAAI,CAAC,WAAW,EAAE;IAC1D,QAAQ,YAAY,IAAI,CAAC,QAAQ,YAAY,GAAG,QAAQ,YAAY,CAAC,WAAW,EAAE;IAClF,QAAQ,SAAS,IAAI,CAAC,QAAQ,SAAS,GAAG,QAAQ,SAAS,CAAC,WAAW,EAAE;AAC7E;AAEA,SAAS,gBAAgB,KAAK,EAAE,QAAQ;IACpC,MAAM,QAAQ,MAAM,MAAM,IAAI,CAAC,CAAC,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,SAAS,UAAU,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,CAAC,KAAK,MAAM,EAAE;IAC9G,OAAO,QAAQ,KAAK,CAAC,KAAK,CAAC;AAC/B;AAEA,SAAS,aAAa,KAAK,EAAE,IAAI,EAAE,IAAI;IACnC,MAAM,OAAO,CAAE,SAAS,IAAI,EAAE,KAAK;QAC/B,IAAI,KAAK,mBAAmB,IAAI;YAC5B,IAAI,QAAQ,SAAS,GAAG;gBACpB,KAAK,WAAW;YACpB,OAAO,IAAI,MAAM;gBACb,KAAK,MAAM;YACf;QACJ;IACJ;AACJ;AAEA,SAAS,kBAAkB,WAAW,EAAE,SAAS;IAC7C,IAAI,KAAK,MAAM,aAAa;QACxB,OAAO;IACX,OAAO,IAAI,SAAS,aAAa;QAC7B;IACJ,OAAO;QACH,OAAO;IACX;AACJ;AAEA,SAAS,mBAAmB,OAAO,EAAE,kBAAkB,EAAE,QAAQ,EAAE,WAAW,EAAE,eAAe;IAC3F,MAAM,uBAAuB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,SAAS;QAC7C,WAAW;QACX,oBAAoB;QACpB,uBAAuB;QACvB,mBAAmB;QACnB,iBAAiB;IACrB;IACA,OAAO,SAAS,YAAY,EAAE,kBAAkB,EAAE,GAAG,EAAE,GAAG,EAAE,MAAM;QAC9D,OAAO,iBAAiB,sBAAsB,+KAAA,CAAA,OAAK,EAAE,oBAAoB,SAAS,OAAO,IAAK,CAAA,IAAK,GAAI,UAAU;YAC7G,KAAK;YACL,KAAK;YACL,YAAY,SAAS,UAAU;YAC/B,gBAAgB,SAAS,cAAc;QAC3C,GAAG,aAAa,cAAc,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe,KAAK,GAAG,KAAK,GAAG,KAAK,GAAG;IACnF;AACJ;AAEA,SAAS,8BAA8B,KAAK,EAAE,UAAU;IACpD,OAAO,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,WAAW,GAAG,MAAM,QAAQ,IAAI,CAAC;AACvE;AACO,MAAM,OAAO,SAAS,cAAc;IACvC,IAAI,CAAC,SAAS,GAAG,eAAe,QAAQ;IACxC,IAAI,CAAC,iBAAiB,GAAG,eAAe,gBAAgB;IACxD,IAAI,CAAC,aAAa,GAAG,eAAe,YAAY;IAChD,IAAI,CAAC,YAAY,GAAG,eAAe,WAAW;IAC9C,IAAI,CAAC,oBAAoB,GAAG,eAAe,mBAAmB;IAC9D,IAAI,CAAC,gBAAgB,GAAG,eAAe,eAAe;IACtD,IAAI,CAAC,mBAAmB,GAAG,eAAe,kBAAkB;IAC5D,IAAI,CAAC,iBAAiB,GAAG,eAAe,gBAAgB;IACxD,IAAI,CAAC,mBAAmB,GAAG,eAAe,kBAAkB;IAC5D,IAAI,CAAC,mBAAmB,GAAG,eAAe,SAAS;IACnD,IAAI,CAAC,cAAc,GAAG,eAAe,WAAW,GAAG,MAAM,CAAC,eAAe,SAAS,GAAG,eAAe,SAAS,GAAG,MAAM,EAAE;IACxH,IAAI,CAAC,QAAQ,CAAC,eAAe,QAAQ,EAAE,eAAe,WAAW;IACjE,IAAI,CAAC,iBAAiB;IACtB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB;IACzC,IAAI,CAAC,cAAc,GAAG,eAAe,cAAc;IACnD,IAAI,CAAC,SAAS,GAAG,CAAC;IAClB,IAAI,CAAC,aAAa,GAAG,CAAC;IACtB,IAAI,CAAC,aAAa,GAAG;IACrB,IAAI,CAAC,UAAU,GAAG,CAAC;IACnB,IAAI,CAAC,YAAY,GAAG,eAAe,WAAW;AAClD;AACA,KAAK,SAAS,GAAG;IACb,aAAa;IACb;QACI,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,CAAC,QAAQ,OAAO,EAAE;YAClB;QACJ;QACA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB;QAC3C,IAAI,CAAC,0BAA0B;QAC/B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YACnB,gBAAgB,QAAQ,KAAK;YAC7B,QAAQ,QAAQ,KAAK;YACrB,kBAAkB,QAAQ,OAAO;QACrC,GAAG,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,qBAAqB,IAAI,MAAM,CAAC,IAAI,CAAC,cAAc;IAChG;IACA,oBAAmB,MAAM,EAAE,IAAI,EAAE,cAAc;QAC3C,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,OAAO;IACtE;IACA,OAAM,UAAU;QACZ,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QACtF,OAAO,WAAW,KAAK,CAAC,IAAI,CAAC,cAAc,IAAI;IACnD;IACA,2BAA2B,IAAM;IACjC,mBAAmB,+KAAA,CAAA,OAAK;IACxB,mBAAmB,+KAAA,CAAA,OAAK;IACxB,2BAA2B,+KAAA,CAAA,OAAK;IAChC,wCAAwC,+KAAA,CAAA,OAAK;IAC7C,wBAAwB,IAAM;IAC9B,wCAAwC,IAAM;IAC9C;QACI,OAAO,IAAI,CAAC,UAAU,GAAG,QAAQ;IACrC;IACA;QACI,MAAM,WAAW,IAAI,CAAC,2BAA2B;QACjD,OAAO,IAAI,CAAC,sBAAsB,MAAM,aAAa,UAAU,aAAa,QAAQ,IAAI,CAAC;IAC7F;IACA,2BAA0B,MAAM;QAC5B,MAAM,SAAS,IAAI,CAAC,kBAAkB;QACtC,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,OAAO,GAAG;QAClD,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,aAAa,MAAM,CAAC,IAAI,CAAC,aAAa,GAAG,MAAM,IAAI,GAAG,IAAI,CAAC;IAC3F;IACA,oBAAoB;QAChB,MAAM,OAAO,IAAI;QACjB,OAAO,SAAS,IAAI,EAAE,SAAS;YAC3B,MAAM,OAAO,KAAK,cAAc,CAAC,KAAK,MAAM;YAC5C,IAAI,KAAK,MAAM,EAAE;gBACb,OAAO,KAAK,kBAAkB,CAAC,KAAK,MAAM,EAAE,WAAW,KAAK,yBAAyB,CAAC,KAAK,MAAM;YACrG;YACA,OAAO;QACX;IACJ;IACA,gBAAgB,SAAS,MAAM;QAC3B,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,oBAAoB,eAAe,MAAM;QAC/C,MAAM,sBAAsB,IAAI,CAAC,oBAAoB;QACrD,MAAM,eAAe,oBAAoB,KAAK;QAC9C,MAAM,aAAa,oBAAoB,GAAG;QAC1C,MAAM,gBAAgB,IAAI,CAAC,aAAa;QACxC,MAAM,cAAc,eAAe,OAAO;QAC1C,MAAM,YAAY,eAAe,QAAQ;QACzC,MAAM,aAAa,IAAI,CAAC,SAAS;QACjC,MAAM,SAAS;YACX,MAAM,WAAW,IAAI;YACrB,OAAO,WAAW,KAAK,GAAG,WAAW,KAAK;YAC1C,KAAK,WAAW,GAAG;YACnB,QAAQ,WAAW,MAAM,GAAG,WAAW,MAAM;QACjD;QACA,MAAM,0BAA0B,cAAc,OAAO,IAAI,aAAa,CAAC,YAAY,GAAG,MAAM,CAAC,YAAY,GAAG,KAAK;QACjH,MAAM,yBAAyB,cAAc,OAAO,IAAI,aAAa,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,GAAG,KAAK;QAC5G,MAAM,WAAW,IAAI;QACrB,MAAM,WAAW,yBAAyB;QAC1C,IAAI,IAAI,CAAC,oBAAoB,CAAC,WAAW,KAAK,MAAM,MAAM,CAAC,kBAAkB,IAAI,MAAM,CAAC,kBAAkB,GAAG,YAAY,MAAM,CAAC,kBAAkB,GAAG,UAAU;YAC3J,OAAO;gBACH,QAAQ;YACZ;QACJ;QACA,OAAO;YACH,QAAQ,eAAe,SAAS,MAAM,CAAC,kBAAkB,GAAG;gBAAC,MAAM,CAAC,kBAAkB;gBAAE;gBAAc,MAAM,CAAC,kBAAkB;gBAAE;aAAW,GAAG,OAAO,SAAS,MAAM,CAAC,kBAAkB,GAAG;gBAAC;gBAAc,MAAM,CAAC,kBAAkB;gBAAE;gBAAY,MAAM,CAAC,kBAAkB;aAAC,GAAG;QAClR;IACJ;IACA,qBAAqB,SAAS,WAAW,EAAE,WAAW,EAAE,SAAS;QAC7D,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACvC,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,QAAQ,KAAK,aAAa,cAAc,QAAQ,KAAK,aAAa,YAAY;YACnG;QACJ;QACA,OAAO;IACX;IACA,mCAAmC,SAAS,KAAK;QAC7C,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC,KAAK;QACpD,MAAM,aAAa,IAAI,CAAC,oBAAoB,CAAC,GAAG;QAChD,OAAO;YACH,QAAQ,IAAI,CAAC,aAAa,GAAG;gBAAC;gBAAO;gBAAc;gBAAO;aAAW,GAAG;gBAAC;gBAAc;gBAAO;gBAAY;aAAM;QACpH;IACJ;IACA,qBAAqB,SAAS,KAAK,EAAE,IAAI;QACrC,OAAO,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,iCAAiC,CAAC,OAAO,MAAM,EAAE,MAAM,8BAA8B,OAAO,IAAI,CAAC,kBAAkB;IAC3J;IACA,4BAA4B,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,KAAK;QACzD,IAAI,EACA,MAAM,IAAI,EACV,UAAU,QAAQ,EACrB,GAAG;QACJ,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC;YAC1G,OAAO;YACP,OAAO;QACX,GAAG,MAAM,CAAC;IACd;IACA,yBAAyB,SAAS,WAAW,EAAE,gBAAgB,EAAE,KAAK,EAAE,KAAK;QACzE,IAAI,OAAO,iBAAiB,IAAI;QAChC,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,eAAe,QAAQ,KAAK;QAClC,IAAI,CAAC,iCAAiC,CAAC;QACvC,OAAO,QAAQ,IAAI,CAAC,WAAW,CAAC,aAAa;QAC7C,MAAM,SAAS,IAAI,CAAC,4BAA4B,CAAC,OAAO;QACxD,OAAO,IAAI,CAAC,0BAA0B,CAAC,MAAM,OAAO,CAAC,EAAE,OAAO,CAAC,EAAE,kBAAkB;IACvF;IACA,cAAc,SAAS,UAAU,EAAE,QAAQ,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK;QACtE,MAAM,eAAe,CAAC,CAAC,CAAC,MAAM,UAAU,IAAI,MAAM,UAAU;QAC5D,MAAM,aAAa,CAAC,MAAM,UAAU,IAAI,EAAE,EAAE,MAAM,CAAE,SAAS,MAAM,EAAE,GAAG;YACpE,OAAO,IAAI,CAAC,IAAI,OAAO;YACvB,OAAO;QACX,GAAI,EAAE;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,cAAc;YACf,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;gBAC9C,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC;gBACrC,MAAM,iBAAiB,IAAI,CAAC,MAAM,CAAC;gBACnC,qBAAqB,WAAW,OAAO,CAAC,CAAC,SAAS,oBAAoB,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,OAAO,EAAE,KAAK,KAAK;gBACjJ,mBAAmB,WAAW,OAAO,CAAC,CAAC,SAAS,kBAAkB,KAAK,MAAM,iBAAiB,KAAK,IAAI,eAAe,OAAO,EAAE,KAAK,KAAK;gBACzI,IAAI,CAAC,MAAM,sBAAsB,CAAC,MAAM,kBAAkB;oBACtD,OAAO;wBACH,MAAM;wBACN,IAAI;wBACJ,aAAa;oBACjB;gBACJ;gBACA,IAAI,qBAAqB,kBAAkB;oBACvC,OAAO;oBACP,WAAW;oBACX,aAAa;gBACjB;YACJ;QACJ;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa;YACvB,aAAa,IAAI,CAAC,YAAY,CAAC,YAAY,SAAS;YACpD,QAAQ,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC;QAClD,OAAO;YACH,QAAQ;QACZ;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YACrB,WAAW,IAAI,CAAC,YAAY,CAAC,UAAU,SAAS;YAChD,MAAM,IAAI,CAAC,mBAAmB,CAAC,UAAU;QAC7C,OAAO;YACH,MAAM;QACV;QACA,MAAM,gBAAgB,QAAQ,MAAM;YAChC,MAAM;YACN,IAAI;QACR,IAAI;YACA,MAAM;YACN,IAAI;QACR;QACA,MAAM,cAAc,IAAI,CAAC,cAAc;QACvC,IAAI,cAAc,IAAI,IAAI,WAAW,CAAC,EAAE,IAAI,cAAc,EAAE,IAAI,WAAW,CAAC,EAAE,IAAI,cAAc,IAAI,IAAI,WAAW,CAAC,EAAE,IAAI,cAAc,EAAE,IAAI,WAAW,CAAC,EAAE,EAAE;YAC1J,cAAc,WAAW,GAAG;QAChC;QACA,OAAO;IACX;IACA,4BAA4B,SAAS,SAAS,EAAE,OAAO;QACnD,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,sBAAsB,IAAI,CAAC,oBAAoB;QACrD,MAAM,eAAe,oBAAoB,KAAK;QAC9C,MAAM,aAAa,oBAAoB,GAAG;QAC1C,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI;YACJ,IAAI,KAAK,cAAc;YACvB,QAAQ,UAAU;YAClB,SAAS,KAAK,eAAe;QACjC,OAAO;YACH,IAAI,KAAK,cAAc;YACvB,IAAI;YACJ,QAAQ,KAAK,eAAe;YAC5B,SAAS,KAAK,YAAY;QAC9B;QACA,OAAO;YACH,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;QACZ;IACJ;IACA,cAAc,SAAS,KAAK;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,EAAE,MAAM,KAAK,EAAE,MAAM,MAAM;IAC1E;IACA,oBAAoB;QAChB,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,OAAO,CAAC,OAAO,CAAE,SAAS,KAAK;YAChC,IAAI,MAAM,KAAK,EAAE;gBACb,MAAM,KAAK,CAAC,IAAI,CAAC,KAAK,4BAA4B,CAAC;YACvD;QACJ;IACJ;IACA,oBAAmB,MAAM,EAAE,QAAQ,EAAE,WAAW;QAC5C,MAAM,oBAAoB,CAAA,OAAQ,IAAI,CAAC,sBAAsB,CAAC,MAAM,SAAS,CAAC,KAAK,WAAW,IAAI,CAAC,GAAG,UAAU;QAChH,IAAI,CAAC,WAAW,CAAC,OAAO,CAAE,SAAS,IAAI;YACnC,IAAI,KAAK,KAAK,EAAE;gBACZ,KAAK,4BAA4B;gBACjC,KAAK,KAAK,CAAC,IAAI,CAAC,kBAAkB;YACtC,OAAO;gBACH,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,CAAC,kBAAkB;YAC5E;QACJ;IACJ;IACA,eAAe,SAAS,MAAM;QAC1B,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,yBAAyB,QAAQ,QAAQ,KAAK,QAAQ,KAAK,CAAC,QAAQ;QAC1E,MAAM,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,CAAE,SAAS,IAAI,EAAE,IAAI;YACxD,IAAI,CAAC,KAAK,mBAAmB,IAAI;gBAC7B,OAAO;YACX;YACA,MAAM,OAAO,KAAK,kBAAkB,GAAG,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS,EAAE;gBAAC,KAAK,WAAW,CAAC,CAAC;gBAAE,KAAK,WAAW,CAAC,CAAC;aAAC,EAAE,CAAC,KAAK,kBAAkB,IAAI,KAAK,SAAS;YACtJ,OAAO;gBACH,OAAO,KAAK,KAAK,KAAK,IAAI,GAAG,KAAK,KAAK;gBACvC,QAAQ,KAAK,KAAK,MAAM,IAAI,GAAG,KAAK,MAAM;gBAC1C,QAAQ,KAAK,KAAK,MAAM,IAAI,GAAG,KAAK,WAAW,IAAI;YACvD;QACJ,GAAI,CAAC;QACL,MAAM,mBAAmB,yBAAyB,IAAI,CAAC,aAAa,GAAG,QAAQ,MAAM,GAAG,QAAQ,KAAK,GAAG;QACxG,IAAI,CAAC,kBAAkB,CAAC,QAAQ,QAAQ,KAAK;QAC7C,OAAO,SAAS,mBAAmB,CAAC,oBAAoB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,cAAc,IAAI,CAAC,yBAAyB,QAAQ,MAAM,GAAG,CAAC;IAC9I;IACA,wBAAwB,SAAS,IAAI,EAAE,MAAM,EAAE,QAAQ;QACnD,SAAS,UAAU;QACnB,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,cAAc,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,OAAO;QAC5E,MAAM,MAAM,eAAe,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS,EAAE;YAAC,KAAK,WAAW,CAAC,CAAC;YAAE,KAAK,WAAW,CAAC,CAAC;SAAC,EAAE,CAAC,KAAK,kBAAkB,IAAI;QAC5H,MAAM,YAAY,KAAK,cAAc,IAAI,QAAQ,KAAK,CAAC,SAAS;QAChE,MAAM,aAAa,eAAe,IAAI,CAAC,QAAQ,CAAC,IAAI;QACpD,MAAM,cAAc,KAAK,kBAAkB,GAAG,OAAO;QACrD,MAAM,iBAAiB,QAAQ,KAAK,CAAC,cAAc;QACnD,MAAM,gBAAgB,QAAQ,KAAK,CAAC,QAAQ;QAC5C,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,cAAc,KAAK,WAAW;QACpC,MAAM,SAAS,YAAY,CAAC;QAC5B,IAAI;QACJ,IAAI;QACJ,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,kBAAkB,QAAQ;gBAC1B,aAAa,eAAe,iBAAiB,IAAI,CAAC,GAAG;YACzD,OAAO;gBACH,aAAa,eAAe,iBAAiB,CAAC,IAAI,CAAC,GAAG,IAAI,MAAM,IAAI;YACxE;YACA,IAAI,cAAc,OAAO;gBACrB,aAAa,cAAc,cAAc,KAAK,MAAM,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,SAAS,IAAI,CAAC,GAAG,IAAI,KAAK;YAC7G,OAAO,IAAI,cAAc,MAAM;gBAC3B,aAAa,cAAc,cAAc,SAAS,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM,CAAC,CAAC,GAAG,MAAM,IAAI,SAAS,IAAI,CAAC;YACvG,OAAO;gBACH,aAAa,SAAS,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG;YAC9C;QACJ,OAAO;YACH,aAAa,YAAY,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,MAAM,GAAG;YAClD,IAAI,kBAAkB,MAAM;gBACxB,IAAI,cAAc,MAAM;oBACpB,aAAa,eAAe,iBAAiB,WAAW,IAAI,CAAC;gBACjE,OAAO,IAAI,cAAc,QAAQ;oBAC7B,aAAa,eAAe,iBAAiB,WAAW,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG;gBACpF,OAAO;oBACH,aAAa,eAAe,iBAAiB,IAAI,CAAC,GAAG,IAAI,KAAK;gBAClE;gBACA,cAAc;YAClB,OAAO;gBACH,IAAI,cAAc,OAAO;oBACrB,aAAa,eAAe,iBAAiB,WAAW,IAAI,CAAC,GAAG,IAAI,KAAK;gBAC7E,OAAO,IAAI,cAAc,QAAQ;oBAC7B,aAAa,eAAe,iBAAiB,WAAW,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,GAAG;gBACpF,OAAO;oBACH,aAAa,eAAe,iBAAiB,IAAI,CAAC;gBACtD;gBACA,cAAc;YAClB;QACJ;QACA,OAAO;YACH,YAAY;YACZ,YAAY;QAChB;IACJ;IACA,+BAA+B;QAC3B,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,qBAAqB,gBAAgB;QAC3C,MAAM,cAAc,SAAS,CAAC,GAAG,IAAI,CAAC;YAClC,OAAO;QACX;QACA,MAAM,gBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC;YACpC,OAAO;QACX;QACA,MAAM,gBAAgB,SAAS,CAAC,GAAG,IAAI,CAAC;YACpC,OAAO;QACX;QACA,OAAO;YACH,QAAQ;YACR,UAAU;YACV,MAAM;YACN,KAAK;YACL,UAAU;YACV,OAAO;YACP,QAAQ;YACR,QAAQ;gBACJ,IAAI,CAAC,MAAM,CAAC,MAAM;gBAClB,IAAI,CAAC,QAAQ,CAAC,MAAM;gBACpB,IAAI,CAAC,QAAQ,CAAC,MAAM;YACxB;YACA,OAAO;gBACH,IAAI,CAAC,MAAM,CAAC,KAAK;gBACjB,IAAI,CAAC,QAAQ,CAAC,KAAK;gBACnB,IAAI,CAAC,QAAQ,CAAC,KAAK;YACvB;QACJ;IACJ;IACA,mBAAmB;QACf,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAChC,OAAO,gBAAgB;QAC3B,GAAG,WAAW;QACd,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACrC,OAAO,gBAAgB;QAC3B;QACA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACpC,OAAO,gBAAgB;QAC3B;QACA,IAAI,CAAC,kBAAkB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACxC,OAAO,gBAAgB;QAC3B;QACA,IAAI,CAAC,cAAc,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACpC,OAAO,gBAAgB;QAC3B,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY,UAAU;QACjD,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACrC,OAAO,gBAAgB;QAC3B,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU;QACzB,IAAI,CAAC,uBAAuB,GAAG;YAC3B,OAAO,IAAI,CAAC,6BAA6B;YACzC,OAAO,IAAI,CAAC,6BAA6B;QAC7C;QACA,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAC1C,OAAO,gBAAgB;QAC3B;IACJ;IACA,kBAAkB;QACd,MAAM,OAAO,IAAI;QACjB,KAAK,UAAU,CAAC,MAAM;QACtB,KAAK,eAAe,CAAC,MAAM;QAC3B,KAAK,oBAAoB,CAAC,MAAM;QAChC,KAAK,uBAAuB,CAAC,KAAK,CAAC,MAAM;QACzC,KAAK,uBAAuB,CAAC,KAAK,CAAC,MAAM;QACzC,KAAK,cAAc,CAAC,MAAM;QAC1B,KAAK,eAAe,CAAC,KAAK;QAC1B,IAAI,CAAC,KAAK,QAAQ,CAAC,KAAK,CAAC,QAAQ,IAAI,CAAC,KAAK,UAAU,IAAI;YACrD,KAAK,kBAAkB,CAAC,MAAM;YAC9B,KAAK,kBAAkB,CAAC,KAAK;QACjC;QACA,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,KAAK;QAChD,KAAK,eAAe,IAAI,KAAK,eAAe,CAAC,KAAK;QAClD,KAAK,cAAc,IAAI,KAAK,cAAc,CAAC,KAAK;QAChD,KAAK,uBAAuB,CAAC,KAAK,CAAC,KAAK;QACxC,KAAK,uBAAuB,CAAC,KAAK,CAAC,KAAK;QACxC,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,CAAC,KAAK;IAChE;IACA,uBAAuB,SAAS,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK;QAClF,QAAQ,SAAS,IAAI,CAAC,iBAAiB;QACvC,MAAM,eAAe;YACjB,OAAO;YACP,WAAW,CAAA,GAAA,mKAAA,CAAA,iBAAO,AAAD,EAAE,OAAO;gBACtB,cAAc;gBACd,OAAO,SAAS,qBAAqB,IAAI,CAAC,WAAW;gBACrD,cAAc,gBAAgB,IAAI,CAAC,aAAa;gBAChD,UAAU,IAAI,CAAC,QAAQ,CAAC,QAAQ;gBAChC,eAAe,IAAI,CAAC,QAAQ,CAAC,aAAa;gBAC1C,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI;gBACxB,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAC7C,OAAO;YACX,MAAM;YACN,KAAK,MAAM,UAAU;YACrB,KAAK,MAAM,UAAU;QACzB;QACA,IAAI,OAAO;YACP,aAAa,KAAK,GAAG;QACzB;QACA,OAAO;IACX;IACA,aAAa,SAAS,KAAK,EAAE,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,YAAY,EAAE,KAAK;QACxE,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,OAAO,cAAc,OAAO,OAAO,cAAc;QACjG,OAAO,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,aAAa,aAAa,IAAI,aAAa,aAAa,CAAC,IAAI,CAAC,cAAc,gBAAgB,aAAa,SAAS;IACxI;IACA,YAAY,SAAS,KAAK,EAAE,YAAY,EAAE,KAAK;QAC3C,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,OAAO,cAAc;QACrE,OAAO,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,aAAa,aAAa,IAAI,aAAa,aAAa,CAAC,IAAI,CAAC,cAAc,gBAAgB,KAAK;IACvH;IACA,aAAY,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,cAAc;QACtD,OAAO,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE;YACf,YAAY;YACZ,UAAU;YACV,cAAc;YACd,gBAAgB;YAChB,aAAa,IAAI,CAAC,UAAU;QAChC;IACJ;IACA,gBAAgB;QACZ,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,2BAA2B,QAAQ,wBAAwB;QACjE,IAAI,CAAC,WAAW,GAAG,CAAC,CAAC,kBAAkB,4BAA4B,CAAC,wBAAwB;IAChG;IACA;QACI,OAAO,iBAAiB,IAAI,CAAC,QAAQ,CAAC,wBAAwB;IAClE;IACA,wBAAwB,SAAS,cAAc;QAC3C,IAAI,CAAC,uBAAuB,GAAG;QAC/B,IAAI,gBAAgB;YAChB,IAAI,CAAC,aAAa,GAAG;QACzB;IACJ;IACA;QACI,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,YAAY;QACR,MAAM,OAAO,IAAI;QACjB,MAAM,EACF,UAAU,QAAQ,EAClB,QAAQ,MAAM,EACd,gBAAgB,cAAc,EAC9B,iBAAiB,eAAe,EAChC,MAAM,IAAI,EACV,MAAM,IAAI,EACV,iBAAiB,eAAe,EACnC,GAAG,KAAK,QAAQ;QACjB,MAAM,gCAAgC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;QAChD,MAAM,mBAAmB,KAAK,2BAA2B;QACzD,MAAM,SAAS,KAAK,SAAS;QAC7B,MAAM,QAAQ,OAAO,IAAI;QACzB,MAAM,OAAO,OAAO,GAAG;QACvB,MAAM,SAAS,OAAO,KAAK,GAAG,OAAO,KAAK;QAC1C,MAAM,UAAU,OAAO,MAAM,GAAG,OAAO,MAAM;QAC7C,MAAM,uBAAuB,KAAK,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI,GAAG,KAAK,OAAO,IAAI,KAAK,KAAK,IAAI;QACjG,MAAM,0BAA0B,KAAK,uBAAuB,CAAC,KAAK;QAClE,MAAM,0BAA0B,KAAK,uBAAuB,CAAC,KAAK;QAClE,MAAM,QAAQ;YAAC,KAAK,kBAAkB;YAAE,wBAAwB,QAAQ;YAAE,wBAAwB,QAAQ;YAAE,wBAAwB,QAAQ;YAAE,wBAAwB,QAAQ;YAAE,KAAK,cAAc;SAAC,CAAC,GAAG,CAAE,CAAA,QAAS,SAAS,MAAM,OAAO,IAAK,MAAM,CAAC,SAAS,KAAK;YAC/P,MAAM,MAAM,SAAS,MAAM,OAAO;YAClC,IAAI,CAAC,OAAO,IAAI,OAAO,EAAE;gBACrB,OAAO;YACX;YACA,IAAI,KAAK,aAAa,EAAE;gBACpB,IAAI,CAAC,GAAG;gBACR,IAAI,KAAK,GAAG,SAAS;YACzB,OAAO;gBACH,IAAI,CAAC,GAAG;gBACR,IAAI,MAAM,GAAG,UAAU;YAC3B;YACA,OAAO;QACX,EAAE,KAAK,eAAe;QACtB,MAAM,UAAU,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO;QAC9C,OAAO,CAAC,SAAS,IAAI;QACrB,IAAI,KAAK,sBAAsB,MAAM,+BAA+B;YAChE,OAAO,CAAC,iBAAiB,GAAG;QAChC;QACA,IAAI,iBAAiB;YACjB,OAAO,CAAC,SAAS,GAAG;QACxB;QACA,IAAI,sBAAsB;YACtB,IAAI,KAAK,aAAa,IAAI,OAAO,KAAK,GAAG,wBAAwB,QAAQ,KAAK,GAAG,sBAAsB;gBACnG,QAAQ,KAAK,GAAG;YACpB;YACA,IAAI,CAAC,KAAK,aAAa,IAAI,OAAO,MAAM,GAAG,wBAAwB,QAAQ,MAAM,GAAG,sBAAsB;gBACtG,QAAQ,MAAM,GAAG;YACrB;QACJ;QACA,IAAI,CAAC,iCAAiC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;YACrD,MAAM,eAAe,KAAK,wBAAwB,MAAM,CAAC,SAAS,KAAK,CAAC,qBAAqB,QAAQ,qBAAqB,GAAG,KAAK,SAAS,KAAK,CAAC,qBAAqB,SAAS,qBAAqB,MAAM,CAAC;YAC3M,OAAO,CAAC,iBAAiB,IAAI,eAAe,SAAS;QACzD;QACA,OAAO;IACX;IACA,cAAc,SAAS,IAAI,EAAE,OAAO,EAAE,UAAU;QAC5C,MAAM,OAAO,IAAI;QACjB,OAAO,KAAK,MAAM,CAAC;QACnB,IAAI,KAAK,MAAM,QAAQ,SAAS;YAC5B,KAAK,iBAAiB,CAAC,SAAS;gBAAC;aAAW;QAChD;QACA,OAAO;IACX;IACA,UAAU,SAAS,QAAQ,EAAE,WAAW;QACpC,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,kBAAkB,2JAAA,CAAA,UAAS;gBAC3B;YACJ,KAAK;gBACD,kBAAkB;QAC1B;QACA,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,eAAe,CAAC,YAAY;IAC7C;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,qBAAqB,+KAAA,CAAA,OAAK;IAC1B,SAAS;QACL;YAAC,IAAI,CAAC,kBAAkB;YAAE,IAAI,CAAC,eAAe;YAAE,IAAI,CAAC,UAAU;SAAC,CAAC,OAAO,CAAE,SAAS,CAAC;YAChF,EAAE,OAAO;QACb;QACA,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,gBAAgB,GAAG;QAC1G,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,cAAc,GAAG;QACtE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,GAAG;QACzC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,gBAAgB,GAAG;QAClG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG;QAC5E,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,GAAG;QACtC,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,CAAC,MAAM;IAC7D;IACA,YAAY;QACR,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,SAAS,SAAS,IAAI;QAClB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,UAAU,SAAS,IAAI,EAAE,QAAQ,EAAE,YAAY;QAC3C,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,QAAQ,CAAC,IAAI;QAC/C,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,YAAY,IAAI,CAAC,QAAQ,CAAC,aAAa;QACrE,IAAI,CAAC,iBAAiB;IAC1B;IACA,YAAY,SAAS,YAAY;QAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,IAAI;QACzC,IAAI,CAAC,QAAQ,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,aAAa;IAC/D;IACA,eAAe;QACX,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,eAAe,SAAS,OAAO;QAC3B,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,QAAQ,KAAK;QAC9B,oBAAoB;QACpB,KAAK,QAAQ,GAAG;QAChB,QAAQ,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;QAChC,QAAQ,SAAS,GAAG,QAAQ,SAAS,IAAI,CAAC;QAC1C,QAAQ,IAAI,GAAG,QAAQ,IAAI,IAAI,CAAC;QAChC,QAAQ,SAAS,GAAG,QAAQ,SAAS,IAAI,CAAC;QAC1C,QAAQ,KAAK,GAAG,QAAQ,KAAK,IAAI,CAAC;QAClC,QAAQ,MAAM,GAAG,QAAQ,MAAM,IAAI,CAAC;QACpC,KAAK,UAAU,GAAG;YACd,MAAM,QAAQ,IAAI;YAClB,cAAc,QAAQ,YAAY;YAClC,WAAW,QAAQ,SAAS;QAChC;QACA,KAAK,cAAc;QACnB,KAAK,aAAa,GAAG,QAAQ,YAAY;QACzC,KAAK,IAAI,GAAG,QAAQ,IAAI;QACxB,KAAK,IAAI,GAAG,QAAQ,IAAI;QACxB,KAAK,QAAQ,GAAG,QAAQ,QAAQ;QAChC,KAAK,eAAe,GAAG,OAAO,SAAS,MAAM,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM;QAC1E,KAAK,YAAY,GAAG;YAChB,SAAS,SAAS,OAAO;YACzB,OAAO;YACP,OAAO,SAAS,QAAQ;QAC5B;QACA,KAAK,eAAe,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,SAAS,IAAI;QACrD,IAAI,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,WAAW,EAAE;YACxC,IAAI,QAAQ,kBAAkB,EAAE;gBAC5B,KAAK,iBAAiB,CAAC;gBACvB,OAAO,QAAQ,kBAAkB;YACrC;QACJ;QACA,KAAK,iBAAiB;QACtB,KAAK,oBAAoB;QACzB,KAAK,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,EAAE,EAAE,GAAG,CAAE,CAAA,IAAK,CAAA,GAAA,yJAAA,CAAA,UAAW,AAAD,EAAE,MAAM;QAClE,KAAK,WAAW,GAAG,KAAK,WAAW,GAAG;QACtC,KAAK,aAAa,GAAG;IACzB;IACA,mBAAmB,SAAS,KAAK,EAAE,SAAS;QACxC,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,WAAW,EAAE;YACpD,OAAO,KAAK,QAAQ;QACxB;QACA,MAAM,EACF,gBAAgB,cAAc,EAC9B,iBAAiB,eAAe,EACnC,GAAG,IAAI,gKAAA,CAAA,QAAK,CAAC,IAAI,CAAC,aAAa,GAAG,gBAAgB;QACnD,OAAO,KAAK,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,OAAO,QAAQ,aAAa,EAAE,gBAAgB,mBAAmB,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,WAAW,QAAQ,aAAa,EAAE,gBAAgB;IACjJ;IACA;QACI,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,OAAO;YACH,YAAY,WAAW,IAAI,CAAC,WAAW,SAAS,CAAC;YACjD,UAAU,WAAW,IAAI,CAAC,WAAW,SAAS,CAAC;QACnD;IACJ;IACA,gBAAgB,SAAS,MAAM;QAC3B,OAAO;IACX;IACA,cAAc,SAAS,MAAM,EAAE,cAAc;QACzC,IAAI,CAAC,gBAAgB;YACjB,MAAM,YAAY,IAAI,CAAC,oBAAoB,GAAG;gBAC1C,OAAO,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,IAAI,GAAG,OAAO,GAAG;gBACrD,KAAK,CAAC,IAAI,CAAC,aAAa,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,MAAM,GAAG,OAAO,MAAM;YAC1F;YACA,UAAU,MAAM,GAAG,UAAU,KAAK,GAAG,CAAC,UAAU,GAAG,GAAG,UAAU,KAAK,IAAI;QAC7E,OAAO;YACH,IAAI,CAAC,oBAAoB,GAAG;QAChC;QACA,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;QAClD,IAAI,CAAC,kBAAkB;IAC3B;IACA,WAAW;QACP,OAAO,IAAI,CAAC,OAAO;IACvB;IACA;QACI,OAAO,IAAI,CAAC,UAAU,IAAI;IAC9B;IACA,WAAW;QACP,MAAM,OAAO,IAAI;QACjB,IAAI,KAAK,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;YAC1B,KAAK,iBAAiB,CAAC,SAAS;gBAAC,KAAK,aAAa,GAAG,eAAe;aAAW;YAChF,KAAK,eAAe,CAAC,KAAK;QAC9B;IACJ;IACA,UAAU;QACN,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,mBAAmB;QACf,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,IAAI,CAAC,QAAQ,KAAK,CAAC,OAAO,IAAI,KAAK,qBAAqB,CAAC,MAAM,KAAK,CAAC,KAAK,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI;YAChH,KAAK,iBAAiB,CAAC,SAAS;gBAAC,KAAK,aAAa,GAAG,eAAe;aAAW;YAChF,KAAK,kBAAkB,CAAC,KAAK;YAC7B,WAAW,KAAK,qBAAqB,EAAE;QAC3C;IACJ;IACA,oCAAmC,KAAK;QACpC,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,WAAW,EAAE;YACxC,MAAM,QAAQ,CAAC;gBACX,gBAAgB,KAAK,MAAM,QAAQ,cAAc,GAAG,QAAQ,cAAc,GAAG,MAAM,GAAG,IAAI;YAC9F;YACA,IAAI,CAAC,MAAM,QAAQ,eAAe,GAAG;gBACjC,MAAM,eAAe,GAAG,QAAQ,eAAe;YACnD;QACJ;IACJ;IACA,gBAAe,aAAa;QACxB,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,aAAa,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ;QACtD,IAAI,aAAa,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI,EAAE;QACtE,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ,UAAU;QACxE,MAAM,cAAc,IAAI,CAAC,WAAW,MAAM,CAAC;QAC3C,MAAM,SAAS,IAAI,gKAAA,CAAA,QAAK,CAAC;QACzB,IAAI,CAAC,wBAAwB,CAAC;QAC9B,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,UAAU;QACjD,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,QAAQ;QAC/C,IAAI,CAAC,YAAY;YACb,aAAa,cAAc,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,QAAQ,KAAK,YAAY,UAAU,GAAG,WAAW,QAAQ;YAC3G,aAAa,cAAc,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,UAAU,KAAK,YAAY,QAAQ,GAAG,WAAW,UAAU;QACjH;QACA,MAAM,aAAa,aAAa,YAAY,UAAU,GAAG,OAAO,UAAU;QAC1E,MAAM,aAAa,aAAa,YAAY,QAAQ,GAAG,OAAO,UAAU;QACxE,IAAI,CAAC,YAAY;YACb,OAAO,GAAG,GAAG,WAAW,UAAU,IAAI,OAAO,GAAG;YAChD,OAAO,GAAG,GAAG,WAAW,QAAQ,IAAI,OAAO,GAAG;QAClD,OAAO;YACH,MAAM,iBAAiB,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,WAAW,UAAU,EAAE,WAAW,QAAQ;YAC/F,aAAa,eAAe,UAAU;YACtC,OAAO,UAAU,GAAG;QACxB;QACA,MAAM,sBAAsB,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;YAC1C,UAAU,QAAQ,IAAI;YACtB,UAAU,QAAQ,QAAQ;YAC1B,MAAM,QAAQ,aAAa;QAC/B,GAAG;YACC,YAAY,aAAa,YAAY,UAAU,GAAG,KAAK;YACvD,UAAU,aAAa,YAAY,QAAQ,GAAG,KAAK;YACnD,QAAQ,YAAY,MAAM;QAC9B,GAAG;YACC,YAAY;YACZ,KAAK,WAAW,UAAU;YAC1B,KAAK,WAAW,QAAQ;QAC5B,GAAG;YACC,YAAY;YACZ,KAAK;YACL,KAAK;QACT;QACA,OAAO,UAAU,GAAG,oBAAoB,UAAU;QAClD,OAAO,UAAU,GAAG,oBAAoB,QAAQ;QAChD,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,GAAG,OAAO,UAAU;QACzD,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO,GAAG,KAAK,CAAC,OAAO,GAAG,GAAG,OAAO,UAAU;QACzD,OAAO,QAAQ,CAAC,CAAC;QACjB,IAAI,CAAC,kCAAkC,CAAC;QACxC,OAAO;IACX;IACA,aAAY,KAAK;QACb,QAAQ,SAAS,CAAC;QAClB,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ;QAC5D,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,WAAW;QAClE,MAAM,oBAAoB,UAAU,IAAI,CAAC,QAAQ,CAAC,cAAc;QAChE,IAAI,eAAe;YACf,MAAM,UAAU,GAAG,qBAAqB,MAAM,UAAU,IAAI,IAAI,OAAO,MAAM,UAAU;YACvF,MAAM,QAAQ,GAAG,qBAAqB,MAAM,QAAQ,IAAI,IAAI,OAAO,MAAM,QAAQ;QACrF;QACA,IAAI,CAAC,cAAc,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,UAAU,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ,KAAK,MAAM,UAAU,GAAG,MAAM,QAAQ,EAAE;YAC9G,MAAM,MAAM,MAAM,QAAQ;YAC1B,MAAM,QAAQ,GAAG,MAAM,UAAU;YACjC,MAAM,UAAU,GAAG;QACvB;QACA,OAAO;IACX;IACA,2BAA0B,QAAQ,EAAE,QAAQ,EAAE,aAAa;QACvD,IAAI,QAAQ,IAAI,CAAC,QAAQ,CAAC,qBAAqB;QAC/C,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,MAAM,QAAQ,IAAI,CAAC,WAAW;QAC9B,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,IAAI,aAAa,OAAO,IAAI,CAAC,aAAa,oBAAoB,EAAE;YAC5D,OAAO;QACX;QACA,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,MAAM,WAAW,IAAI,CAAC,WAAW;YACjC,MAAM,uBAAuB,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,UAAU,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM;YAC3H,IAAI,sBAAsB;gBACtB,MAAM,cAAc,IAAI,CAAC,WAAW;gBACpC,MAAM,0BAA0B,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,UAAU,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,QAAQ;gBACrG,IAAI,yBAAyB;oBACzB,OAAO;gBACX;YACJ;QACJ;QACA,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,MAAM;gBAAC;gBAAO;gBAAM;aAAM,CAAC,OAAO,CAAC,QAAQ;gBAC5C,IAAI,MAAM,QAAQ,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;oBACvC,MAAM,aAAa,MAAM,UAAU;oBACnC,MAAM,gBAAgB,SAAS,UAAU;oBACzC,MAAM,cAAc,IAAI,CAAC,WAAW;oBACpC,IAAI,cAAc,iBAAiB,WAAW,MAAM,IAAI,CAAC,MAAM,cAAc,GAAG,CAAE,CAAA,IAAK,EAAE,OAAO,IAAK,IAAI,CAAC,KAAK,OAAO,CAAC,WAAW,GAAG,CAAE,CAAA,IAAK,EAAE,OAAO,IAAK,IAAI,CAAC,SAAS,CAAC,YAAY,UAAU,CAAC,OAAO,OAAO,UAAU,CAAC,EAAE,CAAC,OAAO,MAAM,YAAY,QAAQ,CAAC,OAAO,OAAO,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,OAAO,EAAE,GAAG;wBACtT,QAAQ;oBACZ,OAAO;wBACH,QAAQ;oBACZ;gBACJ,OAAO;oBACH,MAAM,WAAW,WAAW,SAAS,CAAC,MAAM,GAAG;oBAC/C,MAAM,kBAAkB,WAAW,SAAS,CAAC,SAAS,UAAU;oBAChE,MAAM,WAAW,WAAW,SAAS,CAAC,MAAM,GAAG;oBAC/C,MAAM,kBAAkB,WAAW,SAAS,CAAC,SAAS,QAAQ;oBAC9D,IAAI,aAAa,mBAAmB,aAAa,iBAAiB;wBAC9D,QAAQ;oBACZ,OAAO,IAAI,aAAa,mBAAmB,aAAa,iBAAiB;wBACrE,QAAQ;oBACZ,OAAO;wBACH,QAAQ;oBACZ;gBACJ;gBACA,IAAI,UAAU,QAAQ,aAAa,OAAO,IAAI,aAAa,oBAAoB,EAAE;oBAC7E,QAAQ;gBACZ;YACJ;QACJ,OAAO,IAAI,CAAC,MAAM;YAAC;YAAM;SAAM,CAAC,OAAO,CAAC,QAAQ;YAC5C,IAAI,kBAAkB,MAAM;gBACxB,QAAQ;YACZ,OAAO;gBACH,QAAQ;YACZ;QACJ;QACA,OAAO;IACX;IACA,6BAA4B,6BAA6B,EAAE,iBAAiB,EAAE,QAAQ;QAClF,MAAM,OAAO,IAAI;QACjB,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,IAAI,qBAAqB,KAAK,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI;YACpE;QACJ;QACA,MAAM,wBAAwB,KAAK,0BAA0B,GAAG,KAAK,yBAAyB,CAAC,aAAa,UAAU;QACtH,IAAI,0BAA0B,MAAM;YAChC,KAAK,eAAe,CAAC;gBAAC,YAAY,UAAU;gBAAE,YAAY,QAAQ;aAAC;QACvE,OAAO,IAAI,0BAA0B,OAAO;YACxC,KAAK,eAAe,CAAC;gBAAC;gBAAM;aAAK;QACrC,OAAO,IAAI,0BAA0B,OAAO;YACxC,KAAK,eAAe,CAAC;gBACjB,QAAQ,KAAK,oBAAoB;YACrC;QACJ;IACJ;IACA,sBAAqB,KAAK;QACtB,MAAM,uBAAuB,SAAS,IAAI,CAAC,WAAW,CAAC,gBAAgB;QACvE,MAAM,EACF,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,QAAQ;QACjB,IAAI;QACJ,IAAI,SAAS,kKAAA,CAAA,UAAS,CAAC,WAAW,EAAE;YAChC,SAAS,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,iBAAiB,CAAC,qBAAqB,UAAU,EAAE,qBAAqB,UAAU;QAC3G,OAAO,IAAI,SAAS,kKAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;YACpC,MAAM,iBAAiB,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,qBAAqB,UAAU,EAAE,qBAAqB,UAAU,EAAE,qBAAqB,UAAU;YAC1I,SAAS,eAAe,UAAU,CAAC,MAAM;QAC7C,OAAO;YACH,SAAS,qBAAqB,UAAU,GAAG,qBAAqB,UAAU;QAC9E;QACA,OAAO;IACX;IACA,sBAAqB,KAAK,EAAE,QAAQ;QAChC,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,gBAAgB,WAAW,gBAAgB;QACjD,MAAM,uBAAuB,WAAW,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,eAAe,SAAS,CAAC,KAAK,SAAS;QAChG,MAAM,EACF,MAAM,IAAI,EACV,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,QAAQ;QACjB,IAAI;QACJ,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB,UAAU,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,qBAAqB,UAAU,GAAG;YAC5F;QACJ;QACA,IAAI,SAAS,kKAAA,CAAA,UAAS,CAAC,WAAW,EAAE;YAChC,MAAM,EACF,gBAAgB,cAAc,EAC9B,iBAAiB,eAAe,EAChC,YAAY,UAAU,EACtB,YAAY,UAAU,EACzB,GAAG;YACJ,SAAS,CAAA,GAAA,yJAAA,CAAA,aAAO,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,YAAY,eAAe,gBAAgB,mBAAmB,CAAA,GAAA,yJAAA,CAAA,YAAM,AAAD,EAAE,YAAY,eAAe,gBAAgB,oBAAoB,GAAG,eAAe,gBAAgB;QACzM,OAAO,IAAI,SAAS,kKAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;YACpC,MAAM,iBAAiB,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,qBAAqB,UAAU,EAAE,qBAAqB,UAAU,EAAE,qBAAqB,UAAU;YAC1I,MAAM,QAAQ,KAAK,IAAI,CAAC,eAAe,UAAU,CAAC,MAAM,GAAG,KAAK;YAChE,SAAS,cAAc,UAAU,CAAC,OAAO,CAAC,eAAe,UAAU,CAAC,MAAM;QAC9E,OAAO;YACH,SAAS,WAAW,OAAO,CAAC,CAAC,qBAAqB,UAAU,CAAC,OAAO,KAAK,qBAAqB,UAAU,CAAC,OAAO,EAAE,IAAI;QAC1H;QACA,OAAO;IACX;IACA,kBAAiB,KAAK,EAAE,iBAAiB,EAAE,6BAA6B,EAAE,aAAa;QACnF,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,aAAa,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ;QACtD,KAAK,2BAA2B,CAAC,+BAA+B,mBAAmB;QACnF,KAAK,WAAW,GAAG,IAAI,gKAAA,CAAA,QAAK,CAAC;QAC7B,MAAM,cAAc,KAAK,WAAW,CAAC,OAAO;QAC5C,MAAM,yBAAyB,IAAI,gKAAA,CAAA,QAAK,CAAC,KAAK,WAAW;QACzD,KAAK,wBAAwB,CAAC;QAC9B,KAAK,aAAa,GAAG;YACjB,SAAS;YACT,sBAAsB,uBAAuB,oBAAoB;QACrE;QACA,KAAK,WAAW,CAAC,QAAQ,CAAC;YACtB,YAAY,QAAQ,UAAU;YAC9B,UAAU,QAAQ,QAAQ;YAC1B,UAAU,QAAQ,IAAI;YACtB,MAAM,QAAQ,aAAa;YAC3B,QAAQ,QAAQ,QAAQ;QAC5B;QACA,KAAK,kCAAkC,CAAC,KAAK,WAAW;QACxD,IAAI,CAAC,YAAY;YACb,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,WAAW,CAAC,GAAG,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,WAAW,CAAC,GAAG,GAAG;gBACtE,MAAM,cAAc,KAAK,WAAW;gBACpC,eAAe,KAAK,WAAW,CAAC,QAAQ,CAAC;oBACrC,KAAK,YAAY,UAAU;oBAC3B,KAAK,YAAY,QAAQ;gBAC7B;YACJ;YACA,MAAM,oBAAoB,QAAQ,iBAAiB;YACnD,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;gBAC9B,KAAK,WAAW,CAAC,QAAQ,CAAC;oBACtB,KAAK;oBACL,KAAK;gBACT;YACJ;QACJ;QACA,KAAK,WAAW,CAAC,UAAU,GAAG,KAAK,WAAW,CAAC,UAAU,IAAI,KAAK,WAAW,CAAC,GAAG;QACjF,KAAK,WAAW,CAAC,UAAU,GAAG,KAAK,WAAW,CAAC,UAAU,IAAI,KAAK,WAAW,CAAC,GAAG;QACjF,IAAI,CAAC,KAAK,cAAc,IAAI,QAAQ,QAAQ,EAAE;YAC1C,KAAK,WAAW,CAAC,qBAAqB;QAC1C;QACA,KAAK,WAAW,CAAC,cAAc,CAAC,KAAK,mBAAmB,CAAC;QACzD,KAAK,WAAW,CAAC,UAAU,GAAG,KAAK,WAAW,CAAC,OAAO,KAAK,EAAE,GAAG,KAAK,eAAe,CAAC,SAAS,KAAK,WAAW,EAAE,KAAK,OAAO,EAAE,KAAK,cAAc;QACjJ,KAAK,WAAW,CAAC,mBAAmB,CAAC,KAAK,iBAAiB;IAC/D;IACA,0BAAyB,SAAS;QAC9B,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,IAAI,EAAE,EAAE,OAAO,CAAE,CAAA;YACxE,IAAI,GAAG,OAAO,CAAC,UAAU,EAAE;gBACvB,MAAM,QAAQ,GAAG,cAAc;gBAC/B,UAAU,QAAQ,CAAC;oBACf,sBAAsB;oBACtB,YAAY;oBACZ,YAAY;oBACZ,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG,IAAI,QAAQ,UAAU,GAAG;oBACtD,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG,IAAI,QAAQ,UAAU,GAAG;gBAC1D;YACJ;QACJ;IACJ;IACA,gBAAgB,SAAS,MAAM;QAC3B,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,mBAAmB;QACf,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,WAAW,QAAQ,QAAQ;QACjC,MAAM,aAAa,QAAQ,KAAK,CAAC,cAAc,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,IAAI,IAAI,CAAC,oBAAoB;QACpG,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,OAAO,aAAa,OAAO,aAAa,OAAO,eAAe,aAAa,eAAe;IAC9F;IACA,mBAAmB,SAAS,KAAK,EAAE,OAAO,EAAE,KAAK;QAC7C,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,KAAK;QACxC,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,WAAW,CAAC,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,cAAc,UAAU,KAAK,GAAG,SAAS;IAChH;IACA,mBAAmB,SAAS,MAAM,EAAE,QAAQ;QACxC,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,OAAO,MAAM;QAC5B,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,eAAe,QAAQ,gBAAgB;QAC7C,MAAM,MAAM,SAAS,UAAU;QAC/B,MAAM,MAAM,SAAS,UAAU;QAC/B,MAAM,YAAY,QAAQ,uBAAuB,GAAG,KAAK,wBAAwB,GAAG,CAAC;QACrF,IAAI,gBAAgB,EAAE;QACtB,IAAI,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;YACrC,IAAI,KAAK,WAAW,IAAI,MAAM,OAAO,MAAM,EAAE;gBACzC,gBAAgB;oBAAC,MAAM,CAAC,EAAE;oBAAE,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;iBAAC;YAC1D;QACJ,OAAO,IAAI,cAAc;YACrB,IAAI,UAAU,GAAG,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,EAAE,GAAG;gBAC7C,cAAc,IAAI,CAAC,YAAY,CAAC,EAAE;YACtC;YACA,IAAI,UAAU,GAAG,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,EAAE,GAAG;gBAC7C,cAAc,IAAI,CAAC,YAAY,CAAC,EAAE;YACtC;QACJ,OAAO;YACH,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,UAAU,MAAM,CAAC,EAAE,GAAG,GAAG,GAAG;gBACpD,cAAc,IAAI,CAAC;YACvB;YACA,IAAI,UAAU,GAAG,IAAI,CAAC,MAAM,UAAU,MAAM,CAAC,SAAS,EAAE,GAAG,GAAG,GAAG;gBAC7D,cAAc,IAAI,CAAC;YACvB;QACJ;QACA,OAAO;IACX;IACA,uBAAuB;QACnB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,GAAG;QACjC;IACJ;IACA,sBAAsB;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM;QACrC;IACJ;IACA,wBAAwB;QACpB,OAAO,IAAI,CAAC,QAAQ,CAAC,mBAAmB,IAAI;IAChD;IACA,gBAAgB;QACZ,OAAO;YACH,kBAAkB,qBAAqB,IAAI,CAAC,WAAW;YACvD,kBAAkB,qBAAqB,IAAI,CAAC,WAAW;QAC3D;IACJ;IACA,sBAAsB,SAAS,MAAM;QACjC,IAAI,CAAC,YAAY,CAAC;QAClB,OAAO,IAAI,CAAC,aAAa,KAAK,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,IAAI,+KAAA,CAAA,OAAK,EAAE,MAAM,YAAY;IACpG;IACA,UAAU,SAAS,KAAK;QACpB,MAAM,SAAS,MAAM,UAAU,IAAI,EAAE;QACrC,IAAI,CAAC,WAAW,GAAG,OAAO,GAAG,CAAC,gBAAgB,IAAI,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,mBAAmB,CAAC;QAC7F,IAAI,CAAC,WAAW,GAAG,CAAC,MAAM,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,IAAI,CAAC,SAAS;QACpF,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,uBAAuB,SAAS,GAAG;QAC/B,OAAO;IACX;IACA,WAAW,SAAS,QAAQ,EAAE,gBAAgB,EAAE,kBAAkB;QAC9D,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,cAAc,QAAQ,WAAW;QACvC,MAAM,mBAAmB,QAAQ,gBAAgB;QACjD,OAAO,iBAAiB,SAAS,oBAAoB,IAAI,CAAC,iBAAiB,EAAE,oBAAoB,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,GAAG,UAAU;YAC7L,KAAK,SAAS,UAAU;YACxB,KAAK,SAAS,UAAU;YACxB,YAAY,SAAS,UAAU;YAC/B,gBAAgB,SAAS,cAAc;QAC3C,GAAG,IAAI,CAAC,eAAe,IAAI,QAAQ,YAAY,EAAE,aAAa,QAAQ,KAAK,CAAC,mBAAmB,IAAI,QAAQ,qBAAqB,EAAE;YAC9H,QAAQ;YACR,QAAQ;QACZ,GAAG,QAAQ,iBAAiB,EAAE,QAAQ,cAAc,EAAE,IAAI,CAAC,cAAc;IAC7E;IACA,4BAA4B,SAAS,KAAK,EAAE,gBAAgB;QACxD,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,kBAAkB;QACtD,IAAI,CAAC,MAAM,OAAO,MAAM,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,eAAe,QAAQ,QAAQ,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,MAAM,KAAK,CAAC,MAAM,EAAE;YAC3I,QAAQ,KAAK,CAAC,MAAM,GAAG,kJAAA,CAAA,UAAY,CAAC,oBAAoB,CAAC,MAAM,KAAK;QACxE;QACA,OAAO;IACX;IACA,oBAAmB,sBAAsB,EAAE,KAAK;QAC5C,IAAI;QACJ,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,gBAAgB,IAAI,gKAAA,CAAA,QAAK,CAAC,IAAI,CAAC,aAAa,GAAG,gBAAgB,IAAI,QAAQ,CAAC;QAClF,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,MAAM,aAAa,CAAC,SAAS,eAAe,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,UAAU,KAAK,cAAc,UAAU;QACjI,MAAM,aAAa,CAAC,SAAS,eAAe,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,QAAQ,KAAK,cAAc,UAAU;QAC/H,MAAM,sBAAsB,QAAQ,mBAAmB;QACvD,MAAM,wBAAwB,IAAI,CAAC,yBAAyB;QAC5D,MAAM,cAAc,CAAC,QAAQ,qBAAqB,IAAI,CAAC,uBAAuB,MAAM,QAAQ;QAC5F,MAAM,gBAAgB,mBAAmB,SAAS,uBAAuB,eAAe,IAAI,CAAC,eAAe,IAAI;QAChH,MAAM,eAAe,cAAc,qBAAqB,MAAM,YAAY,YAAY,SAAS,CAAC,oBAAoB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,MAAM,EAAE,YAAY;QACvN,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,wBAAwB,eAAe,YAAY,YAAY,cAAc;QAC9G,IAAI,CAAC,oBAAoB,GAAG;QAC5B,OAAO;YACH,UAAU;YACV,OAAO;QACX;IACJ;IACA;QACI,MAAM,EACF,eAAe,aAAa,EAC5B,sBAAsB,oBAAoB,EAC7C,GAAG,IAAI,CAAC,cAAc,IAAI,CAAC;QAC5B,MAAM,EACF,uBAAuB,qBAAqB,EAC5C,oBAAoB,kBAAkB,EACzC,GAAG,IAAI,CAAC,QAAQ;QACjB,IAAI,uBAAuB;YACvB,OAAO;QACX;QACA,IAAI,sBAAsB;YACtB,OAAO,KAAK,GAAG,CAAC,sBAAsB;QAC1C;QACA,IAAI,eAAe;YACf,OAAO;QACX;QACA,OAAO;IACX;IACA,eAAc,sBAAsB,EAAE,aAAa,EAAE,UAAU,EAAE,UAAU,EAAE,YAAY,EAAE,aAAa;QACpG,MAAM,MAAM,yBAAyB,cAAc,GAAG,GAAG;QACzD,MAAM,MAAM,yBAAyB,cAAc,GAAG,GAAG;QACzD,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM;YACpC,OAAO,EAAE;QACb;QACA,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE;YACvB,MAAM,QAAQ,aAAa;YAC3B,UAAU,QAAQ,IAAI;YACtB,UAAU,QAAQ,QAAQ;QAC9B,GAAG;QACH,IAAI,QAAQ;QACZ,IAAI,MAAM;QACV,IAAI,CAAC,0BAA0B,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YACpD,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,iBAAiB,CAAC,KAAK,MAAM,eAAe,QAAQ,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,kBAAkB,CAAC,gBAAgB;YACjJ,QAAQ,IAAI,KAAK,gBAAgB,CAAC;YAClC,MAAM,IAAI,KAAK;QACnB;QACA,QAAQ,QAAQ,cAAc,GAAG,GAAG,cAAc,GAAG,GAAG;QACxD,MAAM,MAAM,cAAc,GAAG,GAAG,cAAc,GAAG,GAAG;QACpD,MAAM,SAAS,KAAK,eAAe,CAAC,SAAS;YACzC,YAAY;YACZ,YAAY;QAChB,GAAG,KAAK,OAAO,EAAE,KAAK,cAAc;QACpC,MAAM,iBAAiB,KAAK,aAAa,CAAC,QAAQ;YAC9C,YAAY;YACZ,YAAY;QAChB,GAAG,QAAQ,UAAU;QACrB,OAAO,cAAc,cAAc,OAAO,OAAO,KAAK,gBAAgB,KAAK;IAC/E;IACA;QACI,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA;QACI,OAAO,IAAI,CAAC,oBAAoB;IACpC;IACA,aAAa,SAAS,MAAM;QACxB,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,SAAS;QAC/B,MAAM,UAAU,KAAK,QAAQ;QAC7B,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,KAAK,eAAe,GAAG;QACvB,KAAK,YAAY,CAAC;QAClB,MAAM,QAAQ,KAAK,iBAAiB;QACpC,KAAK,cAAc,GAAG,MAAM,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG,KAAK,aAAa,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU,EAAE,OAAO,QAAQ,UAAU;QACxI,KAAK,sBAAsB,GAAG,KAAK,SAAS,CAAC,KAAK,cAAc,CAAC,IAAI,CAAC,WAAW,GAAG,+KAAA,CAAA,OAAK,EAAE,MAAM,YAAY;QAC7G,MAAM,UAAU,IAAI,CAAC,sBAAsB;QAC3C,MAAM,QAAQ,CAAC;YACX,YAAY,QAAQ,QAAQ;YAC5B,YAAY,QAAQ,QAAQ;YAC5B,gBAAgB,QAAQ,cAAc;QAC1C;QACA,MAAM,QAAQ,KAAK,0BAA0B,CAAC;QAC9C,MAAM,gBAAgB,KAAK,iBAAiB,CAAC,MAAM,KAAK,EAAE,KAAK,iBAAiB;QAChF,IAAI,QAAQ,uBAAuB,IAAI,cAAc,MAAM,EAAE;YACzD,KAAK,cAAc,GAAG;gBAAC,aAAa,CAAC,EAAE;aAAC,CAAC,GAAG,CAAC,mBAAmB,MAAM,UAAU;YAChF,IAAI,cAAc,MAAM,GAAG,GAAG;gBAC1B,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,MAAM,CAAC;oBAAC,aAAa,CAAC,EAAE;iBAAC,CAAC,GAAG,CAAC,mBAAmB,MAAM,UAAU;YAC/G;QACJ,OAAO;YACH,KAAK,cAAc,GAAG,EAAE;QAC5B;QACA,MAAM,SAAS,CAAC,MAAM,UAAU,IAAI,EAAE,EAAE,MAAM,CAAE,SAAS,KAAK;YAC1D,OAAO,CAAC,cAAc,IAAI,CAAE,SAAS,QAAQ;gBACzC,OAAO,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,cAAc,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;YACzC;QACJ;QACA,KAAK,aAAa,GAAG,MAAM,YAAY;QACvC,KAAK,kBAAkB,GAAG,MAAM,iBAAiB;QACjD,MAAM,gBAAgB,KAAK,WAAW,IAAI,EAAE;QAC5C,MAAM,qBAAqB,cAAc,MAAM,CAAE,CAAC,GAAG;YACjD,CAAC,CAAC,EAAE,KAAK,CAAC,OAAO,GAAG,GAAG;YACvB,OAAO;QACX,GAAI,CAAC;QACL,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,MAAM,KAAK,CAAC,EAAE,MAAM,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,aAAa,CAAC,EAAE,IAAI,aAAa,CAAC,EAAE,CAAC,KAAK;QACzF,MAAM,kBAAkB,KAAK,mBAAmB,CAAC,MAAM,KAAK;QAC5D,MAAM,aAAa,MAAM,KAAK,CAAC,GAAG,CAAE,CAAA;YAChC,MAAM,OAAO,kBAAkB,CAAC,EAAE,OAAO,GAAG;YAC5C,IAAI,QAAQ,UAAU;gBAClB,OAAO,kBAAkB,CAAC,EAAE,OAAO,GAAG;gBACtC,KAAK,kBAAkB,CAAC;gBACxB,OAAO;YACX,OAAO;gBACH,OAAO,gBAAgB,MAAM,UAAU,iBAAiB;YAC5D;QACJ;QACA,KAAK,WAAW,GAAG;QACnB,MAAM,gBAAgB,KAAK,WAAW,IAAI,EAAE;QAC5C,KAAK,WAAW,GAAG,OAAO,GAAG,CAAE,CAAC,GAAG;YAC/B,MAAM,YAAY,aAAa,CAAC,EAAE;YAClC,IAAI,WAAW;gBACX,UAAU,WAAW,CAAC;gBACtB,OAAO;YACX;YACA,OAAO,gBAAgB,MAAM,UAAU;QAC3C;QACA,KAAK,cAAc,GAAG,OAAO,IAAI,CAAC,oBAAoB,GAAG,CAAE,CAAA,IAAK,kBAAkB,CAAC,EAAE,EAAG,MAAM,CAAC,cAAc,KAAK,CAAC,KAAK,WAAW,CAAC,MAAM,EAAE,cAAc,MAAM;QAChK,KAAK,cAAc,CAAC,OAAO,CAAE,CAAA;YACzB,IAAI;YACJ,OAAO,SAAS,CAAC,WAAW,EAAE,KAAK,KAAK,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,WAAW;QAC/F;QACA,IAAI,MAAM,MAAM,EAAE;YACd,KAAK,WAAW,CAAC,MAAM,GAAG,MAAM,MAAM;QAC1C;QACA,KAAK,iBAAiB,CAAC,KAAK,iBAAiB;IACjD;IACA,mBAAmB,SAAS,KAAK;QAC7B,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB;QACJ;QACA,WAAW,mBAAmB,CAAC;IACnC;IACA;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW;IAC/C;IACA,kBAAkB,SAAS,OAAO;QAC9B,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA;QACI,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC;IACnC;IACA,yBAAyB,SAAS,QAAQ;QACtC,MAAM,aAAa,eAAe,IAAI,CAAC,QAAQ,CAAC,QAAQ;QACxD,MAAM,UAAU,EAAE;QAClB,MAAM,YAAY,SAAS,KAAK;YAC5B,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,QAAQ,IAAI,CAAC,aAAa,0JAAA,CAAA,UAAS,CAAC,kBAAkB,CAAC,SAAS;QACxF;QACA,UAAU,IAAI,CAAC,aAAa;QAC5B,UAAU,IAAI,CAAC,sBAAsB;QACrC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa,QAAQ,IAAI,CAAC;QACpC,UAAU,IAAI,CAAC,oBAAoB;QACnC,OAAO,IAAI,CAAC,0BAA0B,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE;IAC5D;IACA,4BAA2B,gBAAgB;QACvC,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,eAAe,QAAQ,QAAQ,IAAI,QAAQ,YAAY,IAAI,kBAAkB;YAC7E,MAAM,WAAW,QAAQ,QAAQ,CAAC,MAAM,GAAG,cAAc,GAAG;YAC5D,MAAM,UAAU,cAAc,IAAI,GAAG;YACrC,IAAI,aAAa,oBAAoB,UAAU,kBAAkB;gBAC7D,MAAM,gBAAgB,KAAK,IAAI,CAAC,mBAAmB,cAAc,IAAI;gBACrE,oBAAoB,UAAU;YAClC,OAAO,IAAI,WAAW,oBAAoB,mBAAmB,cAAc,GAAG,EAAE;gBAC5E,mBAAmB,cAAc,GAAG;YACxC;QACJ;QACA,OAAO;IACX;IACA,gCAA+B,YAAY,EAAE,WAAW;QACpD,MAAM,qBAAqB,IAAI,CAAC,WAAW,CAAC,kBAAkB;QAC9D,OAAO,qBAAqB,CAAC,qBAAqB,cAAc,CAAC,eAAe,WAAW,CAAC;IAChG;IACA,wBAAuB,KAAK;QACxB,IAAI,CAAC,aAAa;QAClB,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,gBAAgB;QACrC,MAAM,aAAa,CAAC,QAAQ,IAAI,IAAI,CAAC,IAAI;QACzC,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,YAAY,KAAK,iBAAiB;QACxC,MAAM,WAAW,KAAK,WAAW;QACjC,MAAM,cAAc,KAAK,eAAe;QACxC,MAAM,aAAa,CAAC,MAAM,CAAC,QAAQ,IAAI,IAAI,EAAE,EAAE,OAAO,CAAC,kKAAA,CAAA,UAAS,CAAC,QAAQ;QACzE,MAAM,sBAAsB,QAAQ,mBAAmB,IAAI,CAAC,cAAc,CAAC,KAAK,sCAAsC;QACtH,MAAM,aAAa,KAAK,WAAW;QACnC,MAAM,iBAAiB,QAAQ,cAAc;QAC7C,MAAM,iBAAiB,QAAQ,cAAc;QAC7C,IAAI,aAAa;QACjB,IAAI,aAAa;QACjB,IAAI,WAAW;QACf,IAAI;QACJ,IAAI,UAAU,QAAQ,IAAI,CAAC,aAAa;YACpC,OAAO;gBACH,cAAc;gBACd,YAAY;YAChB;QACJ;QACA,IAAI,KAAK,cAAc,IAAI,QAAQ,aAAa,EAAE;YAC9C,gBAAgB,KAAK,uBAAuB,CAAC,UAAU,QAAQ;YAC/D,MAAM,aAAa,WAAW,WAAW,CAAC;YAC1C,IAAI,SAAS,aAAa;gBACtB,WAAW,KAAK,IAAI,CAAC,aAAa,CAAC,IAAI,KAAK,8BAA8B,CAAC,YAAY,YAAY;YACvG,OAAO;gBACH,gBAAgB;YACpB;QACJ;QACA,IAAI;QACJ,IAAI;QACJ,MAAM,kBAAkB,KAAK,cAAc;QAC3C,IAAI,qBAAqB;YACrB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;gBAC3B,oBAAoB,SAAS,kBAAkB,iBAAiB;YACpE,OAAO,IAAI,CAAC,KAAK,cAAc,IAAI,QAAQ,aAAa,IAAI,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,UAAU,IAAI,KAAK,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,UAAU,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,GAAG,GAAG;gBACvJ,aAAa;YACjB,OAAO;gBACH,aAAa,KAAK,GAAG,CAAC,YAAY;gBAClC,aAAa,KAAK,GAAG,CAAC,iBAAiB;YAC3C;YACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;gBAC3B,oBAAoB,SAAS,kBAAkB,iBAAiB;YACpE,OAAO,IAAI,CAAC,KAAK,cAAc,IAAI,QAAQ,aAAa,IAAI,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,UAAU,IAAI,KAAK,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,UAAU,MAAM,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,UAAU,GAAG,GAAG;gBACvJ,aAAa;YACjB,OAAO;gBACH,aAAa,KAAK,GAAG,CAAC,YAAY;gBAClC,aAAa,KAAK,GAAG,CAAC,iBAAiB;YAC3C;QACJ;QACA,MAAM,eAAe,QAAQ,YAAY,IAAI,CAAC,IAAI,CAAC,cAAc;QACjE,IAAI,cAAc;YACd,IAAI,MAAM,KAAK,UAAU,GAAG,GAAG;gBAC3B,aAAa;YACjB;YACA,IAAI,MAAM,KAAK,UAAU,GAAG,GAAG;gBAC3B,aAAa;YACjB;QACJ;QACA,MAAM,iBAAiB,KAAK,kBAAkB;QAC9C,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAC3E,MAAM,yBAAyB,CAAC,cAAc,aAAa,UAAU,IAAI,gBAAgB;QACzF,IAAI,KAAK,MAAM,qBAAqB,KAAK,MAAM,mBAAmB;YAC9D,IAAI,KAAK,MAAM,mBAAmB;gBAC9B,aAAa,yBAAyB;YAC1C;YACA,IAAI,KAAK,MAAM,mBAAmB;gBAC9B,aAAa,yBAAyB;YAC1C;QACJ;QACA,IAAI;QACJ,IAAI;QACJ,IAAI,QAAQ,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,SAAS,MAAM,MAAM,GAAG,KAAK,CAAC,QAAQ,qBAAqB,IAAI,CAAC,SAAS,MAAM,IAAI,UAAU,QAAQ,SAAS,EAAE;YACvJ,MAAM,SAAS,MAAM,MAAM;YAC3B,MAAM,oBAAoB,WAAW,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK;YAC7D,MAAM,mBAAmB,WAAW,SAAS,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK;YACrE,MAAM,mBAAmB,oBAAoB,mBAAmB,CAAC,IAAI;YACrE,MAAM,iBAAiB,KAAK,mBAAmB,CAAC,eAAe,KAAK,GAAG,iBAAiB,GAAG;YAC3F,MAAM,iBAAiB,KAAK,mBAAmB,CAAC,mBAAmB,eAAe,GAAG,GAAG;YACxF,IAAI,iBAAiB,cAAc,iBAAiB,YAAY;gBAC5D,MAAM,gBAAgB,iBAAiB;gBACvC,MAAM,QAAQ,KAAK,8BAA8B,CAAC,eAAe;gBACjE,IAAI,kBAAkB,YAAY;oBAC9B,WAAW,KAAK,CAAC,EAAE,CAAC,KAAK;gBAC7B;gBACA,IAAI,kBAAkB,YAAY;oBAC9B,WAAW,KAAK,CAAC,SAAS,EAAE,CAAC,KAAK;gBACtC;gBACA,aAAa,KAAK,gBAAgB,cAAc;gBAChD,aAAa,KAAK,gBAAgB,cAAc;YACpD;QACJ;QACA,oBAAoB,KAAK,MAAM,oBAAoB,aAAa,yBAAyB;QACzF,oBAAoB,KAAK,MAAM,oBAAoB,aAAa,yBAAyB;QACzF,IAAI,CAAC,YAAY;YACb,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,IAAI;gBAC/B,WAAW,YAAY,WAAW,IAAI,CAAC,eAAe,KAAK,GAAG,cAAc,mBAAmB,CAAC;gBAChG,WAAW,YAAY,WAAW,IAAI,CAAC,eAAe,GAAG,GAAG,cAAc,mBAAmB;YACjG,OAAO;gBACH,WAAW,YAAY,WAAW,IAAI,CAAC,eAAe,KAAK,GAAG,cAAc,mBAAmB,CAAC;gBAChG,WAAW,YAAY,WAAW,IAAI,CAAC,eAAe,GAAG,GAAG,cAAc,mBAAmB;YACjG;QACJ;QACA,MAAM,EACF,cAAc,YAAY,EAC1B,cAAc,YAAY,EAC1B,OAAO,KAAK,EACZ,KAAK,GAAG,EACX,GAAG,KAAK,wBAAwB,CAAC,UAAU;QAC5C,aAAa,SAAS;QACtB,aAAa,OAAO;QACpB,OAAO;YACH,cAAc,WAAW,UAAU,KAAK,aAAa;YACrD,YAAY,WAAW,UAAU,KAAK,aAAa;YACnD,UAAU,gBAAgB;YAC1B,UAAU,gBAAgB;YAC1B,UAAU;YACV,gBAAgB,eAAe,cAAc,MAAM;QACvD;IACJ;IACA,0BAAyB,QAAQ,EAAE,QAAQ;QACvC,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,KAAK,WAAW;QACnC,MAAM,iBAAiB,KAAK,kBAAkB;QAC9C,MAAM,YAAY,KAAK,iBAAiB;QACxC,MAAM,cAAc,KAAK,eAAe;QACxC,MAAM,UAAU,KAAK,QAAQ;QAC7B,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,mBAAmB,CAAC,UAAU;YAChC,MAAM,qBAAqB,KAAK,eAAe,KAAK,GAAG;YACvD,MAAM,qBAAqB,KAAK,eAAe,GAAG,GAAG;YACrD,MAAM,QAAQ,KAAK,8BAA8B,CAAC,qBAAqB,oBAAoB;YAC3F,QAAQ,qBAAqB;YAC7B,MAAM,qBAAqB;QAC/B;QACA,IAAI,CAAC,KAAK,cAAc,IAAI,eAAe,QAAQ,QAAQ,EAAE;YACzD,IAAI,WAAW,UAAU,GAAG,IAAI,KAAK,WAAW,UAAU,UAAU,IAAI,GAAG;gBACvE,iBAAiB,WAAW,SAAS,CAAC,IAAI,WAAW,SAAS,CAAC;gBAC/D,eAAe;YACnB;YACA,IAAI,WAAW,UAAU,GAAG,IAAI,KAAK,WAAW,UAAU,UAAU,IAAI,GAAG;gBACvE,iBAAiB,WAAW,SAAS,CAAC,WAAW,WAAW,SAAS,CAAC;gBACtE,eAAe;YACnB;QACJ;QACA,OAAO;YACH,OAAO,SAAS,SAAS,QAAQ;YACjC,KAAK,SAAS,OAAO,MAAM;YAC3B,cAAc;YACd,cAAc;QAClB;IACJ;IACA;QACI,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB;QACJ;QACA,MAAM,UAAU,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW;QAC5D,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO,EAAE;YACpC,cAAc,QAAQ,YAAY;YAClC,YAAY,QAAQ,UAAU;QAClC;QACA,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC;QAClD,IAAI,SAAS,QAAQ,QAAQ,GAAG;YAC5B,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,gBAAgB;YAC5C,GAAG,QAAQ,CAAC;gBACR,UAAU,QAAQ,QAAQ;YAC9B;YACA,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC;QACzC;IACJ;IACA,eAAe;QACX,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,iBAAiB;QAC7C,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,WAAW,CAAC,YAAY,CAAC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO;QAClE;IACJ;IACA;QACI,MAAM,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,EAAE,EAAE,GAAG,CAAE,CAAA,IAAK,CAAA,GAAA,iKAAA,CAAA,UAAkB,AAAD,EAAE,IAAI,EAAE;QAC7F,IAAI,CAAC,qBAAqB,GAAG,cAAc,MAAM,CAAE,CAAA,IAAK,cAAc,EAAE,aAAa;QACrF,IAAI,CAAC,oBAAoB,GAAG,cAAc,MAAM,CAAE,CAAA,IAAK,aAAa,EAAE,aAAa;IACvF;IACA,MAAM,SAAS,MAAM,EAAE,aAAa;QAChC,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,KAAK,aAAa,GAAG,iBAAiB;YAClC,SAAS;QACb;QACA,KAAK,aAAa;QAClB,KAAK,WAAW,CAAC;QACjB,KAAK,YAAY;QACjB,KAAK,gBAAgB;QACrB,eAAe,KAAK,WAAW;QAC/B,eAAe,KAAK,WAAW;QAC/B,eAAe,KAAK,cAAc;QAClC,KAAK,UAAU,CAAC,MAAM,CAAC,KAAK,mBAAmB;QAC/C,KAAK,SAAS;QACd,KAAK,UAAU;QACf,cAAc,KAAK,WAAW,EAAE,QAAQ,IAAI;QAC5C,cAAc,KAAK,WAAW,EAAE,QAAQ,SAAS;QACjD,cAAc,KAAK,cAAc,EAAE,QAAQ,IAAI;QAC/C,MAAM,eAAe,KAAK,kBAAkB;QAC5C,UAAU,KAAK,WAAW,EAAE;QAC5B,UAAU,KAAK,WAAW,EAAE;QAC5B,WAAW,KAAK,WAAW,EAAE,aAAa,KAAK,iBAAiB,IAAI,KAAK,YAAY,CAAC,QAAQ,KAAK,CAAC,QAAQ;QAC5G,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,MAAM;QACzD,KAAK,kBAAkB,GAAG,IAAI,iLAAA,CAAA,WAAQ;QACtC,KAAK,WAAW,CAAC,OAAO,CAAE,SAAS,IAAI;YACnC,KAAK,kBAAkB,GAAG;YAC1B,KAAK,cAAc,GAAG,KAAK;YAC3B,KAAK,WAAW,GAAG;QACvB;QACA,WAAW,KAAK,qBAAqB,CAAC,MAAM,CAAC,KAAK,oBAAoB,GAAG;QACzE,WAAW,KAAK,OAAO,EAAE;QACzB,KAAK,YAAY,GAAG,KAAK,gBAAgB,MAAM,EAAE;QACjD,KAAK,oBAAoB,IAAI,KAAK,oBAAoB,CAAC,MAAM,CAAC,KAAK,oBAAoB;QACvF,KAAK,mBAAmB,IAAI,KAAK,cAAc,CAAC,MAAM,CAAC,KAAK,mBAAmB;QAC/E,KAAK,YAAY,IAAI,KAAK,eAAe,CAAC,MAAM,CAAC,KAAK,YAAY;QAClE,KAAK,gBAAgB,IAAI,KAAK,kBAAkB,CAAC,MAAM,CAAC,KAAK,gBAAgB;QAC7E,IAAI,KAAK,mBAAmB,EAAE;YAC1B,KAAK,uBAAuB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,mBAAmB,CAAC,KAAK;YAC/E,KAAK,uBAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,mBAAmB,CAAC,KAAK;YACjF,KAAK,uBAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,mBAAmB,CAAC,KAAK;YACjF,KAAK,uBAAuB,CAAC,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,KAAK,mBAAmB,CAAC,KAAK;YAC/E,KAAK,uBAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,mBAAmB,CAAC,KAAK;YACjF,KAAK,uBAAuB,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,KAAK,mBAAmB,CAAC,KAAK;QACrF;QACA,KAAK,aAAa;QAClB,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,WAAW;QAC9B,CAAC,QAAQ,KAAK,CAAC,QAAQ,IAAI,KAAK,cAAc;QAC9C,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,qBAAqB;QACxC,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,oBAAoB;QACvC,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,OAAO;QAC1B,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,YAAY;QAC/B,KAAK,yBAAyB,CAAC,KAAK,oBAAoB;QACxD,KAAK,kBAAkB;QACvB,IAAI,SAAS,KAAK,oBAAoB,GAAG,KAAK,yBAAyB,CAAC,KAAK,qBAAqB;QAClG,IAAI,CAAC,KAAK,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI;YAChD,KAAK,mBAAmB;YACxB,SAAS,KAAK,aAAa,CAAC;QAChC;QACA,iLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,EAAE,KAAK,WAAW,CAAC,GAAG,CAAE,CAAA,OAAQ,KAAK,mBAAmB,KAAM,IAAI,CAAE;YAC/E,KAAK,kBAAkB,CAAC,OAAO;QACnC;QACA,SAAS,KAAK,kBAAkB,CAAC;QACjC,KAAK,YAAY,CAAC;IACtB;IACA;QACI,OAAO,IAAI,CAAC,kBAAkB;IAClC;IACA,kBAAiB,KAAK;QAClB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;QACI,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;QACI,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,eAAe,KAAK,aAAa;QACvC,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YACzB,wBAAwB,KAAK,aAAa,GAAG,WAAW,CAAC,eAAe,QAAQ,QAAQ,GAAG,0JAAA,CAAA,UAAS,CAAC,kBAAkB,CAAC,gBAAgB;QAC5I;QACA,MAAM,cAAc,KAAK,oBAAoB,CAAC,QAAQ,KAAK,CAAC,WAAW;QACvE,MAAM,kBAAkB,KAAK,wBAAwB,CAAC,QAAQ,KAAK,CAAC,mBAAmB,EAAE;QACzF,MAAM,eAAe,QAAQ,KAAK,CAAC,QAAQ,IAAI;QAC/C,MAAM,eAAe,QAAQ,KAAK,CAAC,YAAY,IAAI;QACnD,IAAI,CAAC,WAAW,gBAAgB,WAAW,YAAY,KAAK,gBAAgB,UAAU,oBAAoB,UAAU,WAAW,iBAAiB;YAC5I,MAAM,cAAc,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,eAAe,IAAI,QAAQ,eAAe,GAAG,QAAQ,KAAK,CAAC,cAAc,GAAG,KAAK;YACvH,IAAI,KAAK,aAAa,EAAE;gBACpB,YAAY;gBACZ,aAAa;YACjB,OAAO;gBACH,YAAY;gBACZ,aAAa;YACjB;YACA,IAAI,iBAAiB;YACrB,IAAI,kBAAkB;YACtB,IAAI,WAAW;gBACX,IAAI,KAAK,WAAW,CAAC,IAAI,CAAE,CAAA,OAAQ,KAAK,SAAS,CAAC,KAAK,GAAG,YAAa;oBACnE,iBAAiB;gBACrB;YACJ;YACA,IAAI,YAAY;gBACZ,IAAI,KAAK,WAAW,CAAC,IAAI,CAAE,CAAA,OAAQ,KAAK,SAAS,CAAC,MAAM,GAAG,aAAc;oBACrE,kBAAkB;gBACtB;YACJ;YACA,IAAI,kBAAkB,iBAAiB;gBACnC,KAAK,WAAW,CAAC,OAAO,CAAE,CAAA;oBACtB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,UAAU,CAAC,WAAW,YAAY,QAAQ,KAAK;gBAC5E;gBACA,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,WAAW;YAClC;QACJ;IACJ;IACA,eAAe,+KAAA,CAAA,OAAK;IACpB;QACI,WAAW,IAAI,CAAC,WAAW,EAAE;IACjC;IACA,YAAW,MAAM,EAAE,OAAO;QACtB,IAAI,cAAc,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QACnF,MAAM,OAAO,IAAI;QACjB,KAAK,YAAY,CAAC;QAClB,IAAI,aAAa;YACb,KAAK,mBAAmB;YACxB,KAAK,aAAa;YAClB,KAAK,kBAAkB;QAC3B;QACA,KAAK,iBAAiB,CAAC,KAAK,iBAAiB;QAC7C,KAAK,YAAY;QACjB,MAAM,mBAAmB,CAAC,KAAK,aAAa,IAAI;QAChD,MAAM,UAAU,KAAK,QAAQ;QAC7B,eAAe,KAAK,WAAW;QAC/B,eAAe,KAAK,WAAW;QAC/B,eAAe,KAAK,cAAc;QAClC,IAAI,KAAK,uBAAuB,IAAI,CAAC,KAAK,aAAa,EAAE;YACrD,KAAK,sBAAsB;QAC/B;QACA,oBAAoB,KAAK,WAAW;QACpC,oBAAoB,KAAK,WAAW;QACpC,oBAAoB,KAAK,cAAc;QACvC,IAAI,KAAK,YAAY,EAAE;YACnB,KAAK,0BAA0B;QACnC;QACA,oBAAoB,KAAK,WAAW,EAAE,QAAQ,IAAI,EAAE;QACpD,oBAAoB,KAAK,WAAW,EAAE,QAAQ,SAAS,EAAE;QACzD,oBAAoB,KAAK,cAAc,EAAE,QAAQ,IAAI;QACrD,WAAW,KAAK,WAAW,EAAE,uBAAuB;QACpD,KAAK,qBAAqB,CAAC,MAAM,CAAC,KAAK,oBAAoB,IAAI,EAAE,EAAE,OAAO,CAAE,CAAA,IAAK,EAAE,cAAc,CAAC;QAClG,WAAW,KAAK,OAAO,EAAE,kBAAkB;QAC3C,oBAAoB,KAAK,WAAW,EAAE;QACtC,oBAAoB,KAAK,WAAW,EAAE;QACtC,IAAI,kBAAkB;YAClB,WAAW,KAAK,cAAc,IAAI,EAAE,EAAE;QAC1C;QACA,KAAK,gBAAgB;QACrB,KAAK,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,WAAW,CAAC,gBAAgB,GAAG,OAAO,IAAI;YAChD,KAAK,aAAa,GAAG;QACzB;QACA,KAAK,uBAAuB,GAAG;QAC/B,KAAK,qBAAqB;IAC9B;IACA,uBAAuB,+KAAA,CAAA,OAAK;IAC5B;QACI,MAAM,SAAS;QACf,WAAW,IAAI,CAAC,WAAW,EAAE;QAC7B,WAAW,IAAI,CAAC,WAAW,EAAE;QAC7B,WAAW,IAAI,CAAC,oBAAoB,EAAE;QACtC,WAAW,IAAI,CAAC,qBAAqB,EAAE;QACvC,WAAW,IAAI,CAAC,OAAO,EAAE;IAC7B;IACA;QACI,MAAM,SAAS;QACf,WAAW,IAAI,CAAC,WAAW,EAAE;QAC7B,WAAW,IAAI,CAAC,WAAW,EAAE;QAC7B,WAAW,IAAI,CAAC,oBAAoB,EAAE;QACtC,WAAW,IAAI,CAAC,qBAAqB,EAAE;QACvC,WAAW,IAAI,CAAC,OAAO,EAAE;IAC7B;IACA,gBAAgB,SAAS,cAAc,EAAE,YAAY;QACjD,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;YACjB,aAAa;QACjB;QACA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtB,aAAa;QACjB;QACA,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACzB,aAAa;QACjB;IACJ;IACA,sBAAqB,WAAW;QAC5B,MAAM,QAAQ,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;QAChC,IAAI,KAAK,MAAM,MAAM,UAAU,EAAE;YAC7B,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,UAAU;QACzD;QACA,IAAI,KAAK,MAAM,MAAM,QAAQ,EAAE;YAC3B,MAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,QAAQ;QACrD;QACA,OAAO,CAAA,GAAA,yJAAA,CAAA,2BAAwB,AAAD,EAAE,OAAO,CAAC,SAAS;IACrD;IACA,kBAAiB,OAAO;QACpB,QAAQ,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,UAAU;QACjE,QAAQ,WAAW,GAAG,QAAQ,kBAAkB,GAAG,IAAI,CAAC,oBAAoB,CAAC,QAAQ,kBAAkB;QACvG,IAAI,CAAC,eAAe,CAAC,QAAQ,kBAAkB;IACnD;IACA;QACI,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,WAAW,IAAI,CAAC,cAAc,GAAG,QAAQ,YAAY,GAAG,QAAQ,SAAS;QAC/E,MAAM,SAAS,WAAW,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,YAAY,SAAS,IAAI;YACzD,OAAO;QACX;QACA,IAAI,CAAC,MAAM,GAAG;QACd,QAAQ,QAAQ,GAAG;QACnB,IAAI,CAAC,gBAAgB,CAAC;IAC1B;IACA,kBAAiB,QAAQ;QACrB,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG;QAClD,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG;QAClD,IAAI,CAAC,aAAa,CAAC;YAAC;YAAM;SAAK,EAAE;YAC7B,OAAO,CAAC,CAAC;YACT,KAAK,CAAC,CAAC;QACX;IACJ;IACA,iBAAgB,WAAW,EAAE,kBAAkB;QAC3C,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;QACjD,IAAI,oBAAoB;YACpB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,UAAU,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,GAAG,MAAM,UAAU;YAC5E,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,MAAM,QAAQ;QAC1E,OAAO;YACH,IAAI,CAAC,SAAS,GAAG;QACrB;IACJ;IACA,eAAc,WAAW,EAAE,kBAAkB;QACzC,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,eAAe,CAAC,aAAa;QAClC,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,QAAQ,EAAE;YAC9D,YAAY,SAAS,UAAU;YAC/B,YAAY,SAAS,QAAQ;QACjC,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc;QACpC,IAAI,CAAC,WAAW,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;IAC/D;IACA,sBAAqB,KAAK,EAAE,UAAU;QAClC,OAAO;YACH,MAAM,IAAI;YACV,OAAO,IAAI,CAAC,WAAW;YACvB,QAAQ;YACR,OAAO;YACP,YAAY;QAChB;IACJ;IACA,qBAAoB,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK;QACnE,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,OAAO;YACH,MAAM,IAAI;YACV,eAAe;YACf,OAAO;YACP,QAAQ;YACR,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,OAAO;YACP,YAAY,SAAS,UAAU;YAC/B,UAAU,SAAS,QAAQ;QAC/B;IACJ;IACA;QACI,MAAM,aAAa,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,UAAU;QAC7D,MAAM,QAAQ,IAAI,CAAC,aAAa,GAAG,gBAAgB;QACnD,MAAM,sBAAsB;YACxB,YAAY,kBAAkB,IAAI,CAAC,UAAU,CAAC,UAAU,EAAE,MAAM,GAAG;YACnE,UAAU,kBAAkB,IAAI,CAAC,UAAU,CAAC,QAAQ,EAAE,MAAM,GAAG;QACnE;QACA,OAAO;YACH,YAAY,kBAAkB,WAAW,UAAU,EAAE,oBAAoB,UAAU;YACnF,UAAU,kBAAkB,WAAW,QAAQ,EAAE,oBAAoB,QAAQ;QACjF;IACJ;IACA;QACI,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,IAAI,CAAC,GAAG,MAAM,EAAE;YAC1D,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa;QACxC;IACJ;IACA;QACI,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG,CAAC;IACxC;IACA;QACI,MAAM,QAAQ,IAAI,CAAC,WAAW;QAC9B,IAAI,OAAO;YACP,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAE,CAAA,OAAQ,KAAK,iBAAiB,EAAG,MAAM,CAAE,CAAA,OAAQ,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;QAC5F,OAAO;YACH,OAAO,EAAE;QACb;IACJ;IACA,sBAAqB,KAAK;QACtB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,GAAG;IACvC;IACA;QACI,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO;QACb,IAAI;QACJ,IAAI,MAAM,KAAK,MAAM,EAAE;YACnB,MAAM,gBAAgB,KAAK,yBAAyB;YACpD,IAAI,aAAa,cAAc,UAAU;YACzC,IAAI,WAAW,cAAc,UAAU;YACvC,IAAI,KAAK,QAAQ,CAAC,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;gBAC3C,aAAa,cAAc,cAAc,UAAU,CAAC,EAAE;gBACtD,WAAW,YAAY,cAAc,UAAU,CAAC,cAAc,UAAU,CAAC,MAAM,GAAG,EAAE;gBACpF,OAAO;oBACH,YAAY;oBACZ,UAAU;oBACV,YAAY,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,UAAU,EAAE,YAAY,UAAU,UAAU;gBAC5F;YACJ;YACA,OAAO;gBACH,YAAY;gBACZ,UAAU;YACd;QACJ,OAAO,IAAI,SAAS,IAAI,CAAC,EAAE,GAAG;YAC1B,cAAc,IAAI,CAAC,EAAE;QACzB,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG;YAC/B,cAAc,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,EAAE;QACpC,OAAO;YACH,cAAc;gBAAC,IAAI,CAAC,EAAE;gBAAE,IAAI,CAAC,EAAE;aAAC;QACpC;QACA,MAAM,cAAc,KAAK,aAAa,CAAC,aAAa,IAAI,CAAC,EAAE;QAC3D,IAAI,CAAC,YAAY,WAAW,EAAE;YAC1B,KAAK,YAAY,CAAC,MAAM;QAC5B;IACJ;IACA,eAAc,WAAW,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM;QACtD,MAAM,OAAO,IAAI;QACjB,gBAAgB,iBAAiB,CAAC;QAClC,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;YACxB,cAAc,KAAK,oBAAoB,CAAC;YACxC,YAAY,MAAM,GAAG;QACzB;QACA,MAAM,iBAAiB,KAAK,oBAAoB,CAAC,UAAU;QAC3D,MAAM,gBAAgB,eAAe,KAAK;QAC1C,CAAC,cAAc,KAAK,IAAI,KAAK,aAAa,CAAC,aAAa;QACxD,MAAM,cAAc;YAChB,aAAa,eAAe,MAAM;YAClC,iBAAiB,cAAc,eAAe;YAC9C,OAAO,eAAe,eAAe,KAAK;QAC9C;QACA,IAAI,CAAC,eAAe,MAAM,EAAE;YACxB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,KAAK,aAAa,CAAC,aAAa,cAAc,kBAAkB;YAC1F,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,oBAAoB,GAAG;gBACvC,KAAK,oBAAoB,GAAG;oBACxB,YAAY;oBACZ,MAAM,IAAI,CAAC,UAAU,GAAG,IAAI;gBAChC;YACJ;YACA,KAAK,oBAAoB,CAAC,KAAK,GAAG;YAClC,KAAK,oBAAoB,CAAC,MAAM,GAAG;YACnC,KAAK,oBAAoB,CAAC,OAAO,GAAG,CAAC,CAAC,cAAc,GAAG;QAC3D;QACA,OAAO;IACX;IACA;QACI,MAAM,OAAO,IAAI;QACjB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,oBAAoB,KAAK,CAAC,KAAK,oBAAoB,CAAC,OAAO,EAAE;YAC5E,MAAM,gBAAgB,KAAK,oBAAoB,CAAC,UAAU;YAC1D,MAAM,WAAW,KAAK,oBAAoB,CAAC,KAAK;YAChD,MAAM,SAAS,KAAK,oBAAoB,CAAC,MAAM;YAC/C,MAAM,wBAAwB;gBAC1B,YAAY,cAAc,UAAU;gBACpC,YAAY,cAAc,QAAQ;gBAClC,YAAY,cAAc,UAAU;YACxC;YACA,MAAM,mBAAmB,KAAK,UAAU,GAAG,IAAI,KAAK,KAAK,oBAAoB,CAAC,IAAI;YAClF,MAAM,QAAQ,mBAAmB,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,KAAK,oBAAoB,KAAK,KAAK,oBAAoB,CAAC,uBAAuB,UAAU;YACjI,MAAM,aAAa,mBAAmB,CAAC,CAAC,KAAK,KAAK,CAAC,KAAK,oBAAoB,CAAC,yBAAyB,CAAC,KAAK,oBAAoB,MAAM,CAAC,IAAI,SAAS,KAAK,IAAI;YAC7J,MAAM,eAAe,KAAK,mBAAmB,CAAC,eAAe,UAAU,QAAQ,YAAY;YAC3F,aAAa,MAAM,GAAG,KAAK,8BAA8B,CAAC,MAAM,aAAa,QAAQ,QAAQ,YAAY,eAAe;YACxH,KAAK,aAAa,CAAC,WAAW;YAC9B,IAAI,aAAa,MAAM,EAAE;gBACrB,KAAK,2BAA2B,CAAC;YACrC;YACA,KAAK,oBAAoB,GAAG;QAChC;IACJ;IACA,6BAA4B,aAAa;QACrC,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,YAAY,CAAC,IAAI,EAAE;IAC5B;IACA,gCAA+B,UAAU,EAAE,UAAU,EAAE,KAAK;QACxD,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,aAAa,KAAK,WAAW;QACnC,IAAI,UAAU,QAAQ,oBAAoB;QAC1C,IAAI,iBAAiB;QACrB,IAAI;QACJ,IAAI,eAAe,WAAW,cAAc,cAAc;QAC1D,MAAM,gBAAgB,WAAW,gBAAgB;QACjD,IAAI,OAAO;YACP,cAAc,KAAK,WAAW,CAAC,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE;YACjD,cAAc;gBACV,YAAY,YAAY,UAAU;gBAClC,YAAY,YAAY,QAAQ;gBAChC,YAAY,cAAc,UAAU;YACxC;QACJ;QACA,MAAM,0BAA0B,KAAK,oBAAoB,CAAC;QAC1D,MAAM,yBAAyB,KAAK,oBAAoB,CAAC;QACzD,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,eAAe,QAAQ,IAAI,EAAE;YACnD,UAAU,WAAW,OAAO,CAAC;YAC7B,IAAI,eAAe,UAAU,2BAA2B,WAAW,wBAAwB;gBACvF,iBAAiB,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW,sBAAsB,CAAC,SAAS;gBAC9E,eAAe;YACnB,OAAO;gBACH,gBAAgB,UAAU;YAC9B;QACJ,OAAO;YACH,MAAM,eAAe,KAAK,WAAW,CAAC,YAAY;YAClD,MAAM,YAAY;gBACd,YAAY,cAAc,GAAG;gBAC7B,YAAY,cAAc,GAAG;gBAC7B,YAAY,cAAc,UAAU;YACxC;YACA,gBAAgB,KAAK,oBAAoB,CAAC,aAAa,gBAAgB;QAC3E;QACA,OAAO;YACH,iBAAiB,CAAC,CAAC;YACnB,gBAAgB;QACpB;IACJ;IACA,mBAAkB,KAAK;QACnB,IAAI;QACJ,IAAI;QACJ,IAAI,eAAe,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE;YACnC,aAAa,IAAI,CAAC,WAAW,CAAC,gBAAgB;YAC9C,mBAAmB,QAAQ,WAAW,UAAU,CAAC,WAAW,UAAU,CAAC,MAAM,GAAG,EAAE,GAAG,WAAW,UAAU,CAAC,EAAE;QACjH,OAAO;YACH,aAAa,IAAI,CAAC,aAAa;YAC/B,mBAAmB,QAAQ,WAAW,QAAQ,GAAG,WAAW,UAAU;QAC1E;QACA,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,eAAe,WAAW,SAAS,CAAC;QAC1C,MAAM,cAAc,IAAI,CAAC,WAAW;QACpC,MAAM,mBAAmB,QAAQ,WAAW,SAAS,CAAC,YAAY,QAAQ,IAAI,WAAW,SAAS,CAAC,YAAY,UAAU;QACzH,OAAO,KAAK,mBAAmB,gBAAgB;IACnD;IACA;QACI,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,cAAc;QACV,MAAM,SAAS,IAAI,CAAC,WAAW,IAAI,EAAE;QACrC,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,kKAAA,CAAA,UAAS,CAAC,QAAQ,EAAE;YAC3C,OAAO,qBAAqB;QAChC,OAAO;YACH,OAAO,qBAAqB,OAAO,MAAM,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;gBACjG,OAAO,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE,KAAK,CAAA,GAAA,yJAAA,CAAA,UAAO,AAAD,EAAE;YAChC;QACJ;IACJ;IACA,eAAe,SAAS,MAAM,EAAE,WAAW;QACvC,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,YAAY,QAAQ,OAAO,GAAG,QAAQ,KAAK,GAAG;QACpD,IAAI;QACJ,MAAM,SAAS,cAAc,QAAQ,KAAK,CAAC,cAAc,GAAG,KAAK,QAAQ,IAAI,CAAC,MAAM,GAAG;QACvF,IAAI;QACJ,MAAM,gBAAgB,KAAK,iBAAiB;QAC5C,IAAI,cAAc,OAAO,MAAM,CAAC,QAAQ,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,kBAAkB,EAAE;YAC/E,OAAO;gBACH,QAAQ;gBACR,OAAO;gBACP,GAAG;gBACH,GAAG;YACP;QACJ;QACA,IAAI,KAAK,WAAW,EAAE;YAClB,QAAQ,qBAAqB,KAAK,WAAW;QACjD,OAAO;YACH,KAAK,YAAY,CAAC;YAClB,QAAQ,KAAK,0BAA0B,CAAC,eAAe,+KAAA,CAAA,OAAK;YAC5D,eAAe,MAAM,YAAY;YACjC,QAAQ,MAAM,KAAK;QACvB;QACA,MAAM,UAAU,MAAM,MAAM,CAAE,SAAS,SAAS,EAAE,IAAI,EAAE,KAAK;YACzD,MAAM,QAAQ,KAAK,WAAW,CAAC,MAAM,QAAQ,KAAK,EAAE,eAAe,KAAK,GAAG,cAAc;YACzF,IAAI,UAAU,MAAM,GAAG,MAAM,MAAM,EAAE;gBACjC,OAAO;YACX,OAAO;gBACH,OAAO;YACX;QACJ,GAAI,KAAK,WAAW,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ,KAAK,EAAE,eAAe,KAAK,GAAG,cAAc;QACnF,MAAM,OAAO,KAAK,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG,GAAG,GAAG,CAAC,KAAK,eAAe,EAAE,IAAI,CAAC,KAAK,YAAY,EAAE,MAAM,CAAC,KAAK,SAAS,CAAC,IAAI;QAC5H,MAAM,MAAM,KAAK,OAAO;QACxB,KAAK,MAAM;QACX,OAAO;YACH,GAAG,IAAI,CAAC;YACR,GAAG,IAAI,CAAC;YACR,OAAO,IAAI,KAAK,GAAG;YACnB,QAAQ,IAAI,MAAM,GAAG;QACzB;IACJ;IACA,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE;YAC9B;QACJ;QACA,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,KAAK;QACpC,MAAM,cAAc,KAAK,oBAAoB,CAAC,SAAS,WAAW;QAClE,MAAM,kBAAkB,KAAK,wBAAwB,CAAC,SAAS,mBAAmB,EAAE;QACpF,MAAM,oBAAoB,WAAW,mBAAmB,aAAa;QACrE,MAAM,WAAW;YACb,eAAe,SAAS,aAAa;YACrC,mBAAmB,SAAS,iBAAiB;QACjD;QACA,IAAI;QACJ,MAAM,QAAQ,KAAK,WAAW,CAAC,GAAG,CAAE,SAAS,IAAI;YAC7C,OAAO,KAAK,SAAS;QACzB;QACA,IAAI,OAAO,KAAK,QAAQ,CAAC;QACzB,OAAQ;YACJ,KAAK;gBACD,IAAI,mBAAmB;oBACnB,gBAAgB;oBAChB,OAAO;gBACX;gBACA,KAAK,eAAe,CAAC,aAAa,MAAM,OAAO,UAAU;gBACzD;YACJ,KAAK;gBACD,IAAI,mBAAmB;oBACnB,OAAO;gBACX;gBACA,KAAK,eAAe,CAAC,aAAa,KAAK,MAAM,IAAI,OAAO;gBACxD;YACJ;gBACI,KAAK,sBAAsB,CAAC,OAAO,iBAAiB,MAAM;QAClE;IACJ;IACA,wBAAwB,SAAS,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,QAAQ;QACxD,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,KAAK;QACpC,MAAM,aAAa,KAAK,WAAW;QACnC,IAAI,WAAW,QAAQ,aAAa,MAAM;YACtC;QACJ;QACA,IAAI,OAAO,KAAK,MAAM,IAAI,CAAE,SAAS,GAAG,EAAE,KAAK,EAAE,KAAK;YAC9C,IAAI,MAAM,OAAO;gBACb,OAAO;YACX;YACA,OAAO,kKAAA,CAAA,UAAS,CAAC,gBAAgB,CAAC,KAAK,KAAK,CAAC,QAAQ,EAAE,EAAE,SAAS,UAAU,EAAE,SAAS,SAAS;QACpG,IAAK;YACL,KAAK,eAAe,CAAC,MAAM,MAAM,OAAO;QAC5C;QACA,KAAK,8BAA8B,CAAC,YAAY,OAAO;QACvD,KAAK,mBAAmB,CAAC,YAAY,OAAO,SAAS,UAAU,EAAE,SAAS,SAAS;IACvF;IACA,iBAAiB,SAAS,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,QAAQ,EAAE,aAAa;QAChE,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,KAAK,WAAW;QACnC,MAAM,WAAW,KAAK,QAAQ,CAAC,KAAK;QACpC,MAAM,QAAQ,SAAS,aAAa;QACpC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,IAAI,CAAC,SAAS,aAAa,EAAE;oBACzB,YAAY,QAAQ,IAAI,QAAQ;oBAChC,IAAI,QAAQ,OAAO,GAAG;wBAClB,YAAY;oBAChB;gBACJ;gBACA,OAAO,gBAAgB,OAAO,KAAK,QAAQ,CAAC,OAAO;gBACnD,OAAO,SAAS,IAAI;oBAChB,MAAM,mBAAmB,KAAK,mBAAmB;oBACjD,IAAI,CAAC,kBAAkB;wBACnB;oBACJ;oBACA,iBAAiB,MAAM,CAAC;oBACxB,KAAK,kBAAkB,GAAG;oBAC1B,aAAa,CAAC,KAAK,cAAc,GAAG,SAAS;gBACjD;gBACA,aAAa,YAAY,MAAM;gBAC/B;YACJ,KAAK;gBACD,cAAc,KAAK,kBAAkB,CAAC,OAAO,SAAS,iBAAiB;gBACvE,OAAO,SAAS,IAAI,EAAE,KAAK;oBACvB,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI,MAAM,GAAG;wBAC9B,KAAK,WAAW,GAAG;oBACvB;gBACJ;gBACA,aAAa,YAAY,OAAO,GAAG;gBACnC;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,MAAM,MAAM;oBACZ,KAAK,eAAe,CAAC,WAAW,MAAM,OAAO;gBACjD,OAAO;oBACH,KAAK,eAAe,CAAC,QAAQ,MAAM,OAAO;wBACtC,eAAe,gBAAgB,OAAO;oBAC1C;gBACJ;gBACA;YACJ;gBACI,aAAa,YAAY;QACjC;IACJ;IACA,mBAAmB,+KAAA,CAAA,OAAK;IACxB,kBAAkB,+KAAA,CAAA,OAAK;IACvB,oBAAoB,+KAAA,CAAA,OAAK;IACzB,UAAU,+KAAA,CAAA,OAAK;IACf,sBAAsB,+KAAA,CAAA,OAAK;IAC3B,qBAAqB,+KAAA,CAAA,OAAK;IAC1B,oBAAoB,+KAAA,CAAA,OAAK;IACzB,YAAY,+KAAA,CAAA,OAAK;IACjB,oBAAoB,+KAAA,CAAA,OAAK;IACzB,2BAA2B,+KAAA,CAAA,OAAK;IAChC,mBAAmB;QACf,OAAO,IAAI,uKAAA,CAAA,eAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IACrC;IACA,mBAAmB;QACf,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,WAAW,MAAM,CAAC,WAAW,gBAAgB,IAAI,IAAI,CAAC,OAAO,IAAI,CAAC,GAAG,IAAI,CAAC,qBAAqB;IACnG;IACA,uBAAuB;QACnB,IAAI,oBAAoB;QACxB,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,OAAO;YACH,cAAc,IAAI,CAAC,aAAa;YAChC,gBAAgB,CAAC,IAAI,CAAC,cAAc;YACpC,UAAU,QAAQ,oBAAoB;YACtC,gBAAgB,SAAS,CAAC,qBAAqB,QAAQ,QAAQ,KAAK,KAAK,MAAM,qBAAqB,KAAK,IAAI,kBAAkB,CAAC,EAAE;YAClI,OAAO,IAAI,CAAC,SAAS;YACrB,YAAY,CAAC,SAAS,CAAC,sBAAsB,QAAQ,UAAU,KAAK,KAAK,MAAM,sBAAsB,KAAK,IAAI,oBAAoB,KAAK,KAAK;QAChJ;IACJ;IACA;QACI,MAAM,SAAS,IAAI,CAAC,kBAAkB;QACtC,OAAO;YAAC,OAAO,KAAK;YAAE,OAAO,GAAG;SAAC,CAAC,IAAI,CAAE,CAAC,GAAG,IAAM,IAAI;IAC1D;IACA,oBAAoB;QAChB,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC;QAChC,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,MAAM;QACzD,MAAM,SAAS,eAAe;YAAC,OAAO,IAAI;YAAE,OAAO,KAAK,GAAG,OAAO,KAAK;SAAC,GAAG;YAAC,OAAO,MAAM,GAAG,OAAO,MAAM;YAAE,OAAO,GAAG;SAAC;QACtH,UAAU,OAAO,OAAO;QACxB,OAAO;YACH,OAAO,MAAM,CAAC,EAAE;YAChB,KAAK,MAAM,CAAC,EAAE;QAClB;IACJ;IACA,iBAAiB;QACb,MAAM,SAAS,IAAI,CAAC,kBAAkB;QACtC,MAAM,SAAS,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,EAAE,GAAG,EAAE;QACpE,MAAM,eAAe,OAAO,MAAM;QAClC,MAAM,cAAc,KAAK,OAAO,KAAK,GAAG,OAAO,GAAG;QAClD,OAAO,cAAc,CAAC,eAAe,MAAM,CAAC,eAAe,EAAE,CAAC,eAAe,GAAG,CAAC;IACrF;IACA,iBAAiB;QACb,OAAO,EAAE;IACb;IACA,eAAe;QACX,OAAO,EAAE;IACb;IACA,cAAc,+KAAA,CAAA,OAAK;IACnB,qBAAqB,+KAAA,CAAA,OAAK;IAC1B,gBAAgB,+KAAA,CAAA,OAAK;IACrB,gBAAgB,+KAAA,CAAA,OAAK;IACrB,gCAAgC,+KAAA,CAAA,OAAK;IACrC,qBAAqB,+KAAA,CAAA,OAAK;IAC1B,iBAAiB,+KAAA,CAAA,OAAK;IACtB,cAAc,+KAAA,CAAA,OAAK;IACnB,qBAAqB,+KAAA,CAAA,OAAK;IAC1B,wBAAuB,iBAAiB;QACpC,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,qBAAoB,aAAa;QAC7B,IAAI;QACJ,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO;QACX,OAAO;YACH,MAAM,0BAA0B,IAAI,CAAC,QAAQ,CAAC,uBAAuB;YACrE,OAAO,2BAA2B,IAAI,CAAC,QAAQ,CAAC,UAAU;QAC9D;QACA,OAAO;IACX;IACA;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,CAAC,gBAAgB;IAChE;AACJ", "ignoreList": [0], "debugId": null}}]}