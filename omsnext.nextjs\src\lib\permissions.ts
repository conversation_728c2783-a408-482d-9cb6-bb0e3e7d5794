// src/lib/permissions.ts

// Define available roles
export type Role = "admin" | "user" | "manager";

// Define available permissions
export type Permission =
  | "view_dashboard"
  | "view_users"
  | "create_users"
  | "edit_users"
  | "delete_users"
  | "view_settings"
  | "edit_settings"
  | "view_profile"
  | "edit_profile";

// Define role to permissions mapping
const rolePermissions: Record<Role, Permission[]> = {
  admin: [
    "view_dashboard",
    "view_users",
    "create_users",
    "edit_users",
    "delete_users",
    "view_settings",
    "edit_settings",
    "view_profile",
    "edit_profile",
  ],
  manager: [
    "view_dashboard",
    "view_users",
    "create_users",
    "edit_users",
    "view_settings",
    "view_profile",
    "edit_profile",
  ],
  user: ["view_dashboard", "view_profile", "edit_profile"],
};

/**
 * Check if a user has a specific permission
 */
export function hasPermission(
  userRoles: Role[],
  permission: Permission
): boolean {
  // Check if any of the user's roles has the required permission
  return userRoles.some((role) => {
    const permissions = rolePermissions[role] || [];
    return permissions.includes(permission);
  });
}

/**
 * Check if a user has all the specified permissions
 */
export function hasAllPermissions(
  userRoles: Role[],
  permissions: Permission[]
): boolean {
  return permissions.every((permission) =>
    hasPermission(userRoles, permission)
  );
}

/**
 * Check if a user has any of the specified permissions
 */
export function hasAnyPermission(
  userRoles: Role[],
  permissions: Permission[]
): boolean {
  return permissions.some((permission) => hasPermission(userRoles, permission));
}

/**
 * Check if a user has a specific role
 */
export function hasRole(userRoles: Role[], role: Role): boolean {
  return userRoles.includes(role);
}

/**
 * Check if a user has all the specified roles
 */
export function hasAllRoles(userRoles: Role[], roles: Role[]): boolean {
  return roles.every((role) => hasRole(userRoles, role));
}

/**
 * Check if a user has any of the specified roles
 */
export function hasAnyRole(userRoles: Role[], roles: Role[]): boolean {
  return roles.some((role) => hasRole(userRoles, role));
}
