﻿using DevExpress.Xpo;
using System;

namespace omsnext.shared.Models
{
    [Persistent("omsnext.password_reset_requests")]
    public class PasswordResetRequest : XPLiteObject
    {
        [Key(AutoGenerate = true)]
        public long Id { get; set; }

        [Size(255)]
        public string UserEmail { get; set; } = string.Empty;

        [Size(255)]
        public string UserDisplayName { get; set; } = string.Empty;

        public Guid UserId { get; set; }

        public DateTime RequestDate { get; set; } = DateTime.UtcNow;

        [Size(50)]
        public string Status { get; set; } = "Pending"; // Pending, Approved, Rejected, Completed

        public Guid? ProcessedByAdminId { get; set; }

        [Size(255)]
        public string? ProcessedByAdminName { get; set; }

        public DateTime? ProcessedDate { get; set; }

        [Size(1000)]
        public string? AdminNotes { get; set; }

        [Size(255)]
        public string RequestSource { get; set; } = "Desktop"; // Desktop, Web, Mobile

        [Size(45)]
        public string? UserIPAddress { get; set; }

        public bool IsEmailSent { get; set; } = false;

        public DateTime? EmailSentDate { get; set; }

        public PasswordResetRequest(Session session) : base(session) { }
    }
}