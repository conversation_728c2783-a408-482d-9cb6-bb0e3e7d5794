﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>DevExpress.Mvvm.v25.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Mvvm">
      <summary>
        <para>Contains controls for building MVVM-aware applications.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.AsyncDisplayMode">
      <summary>
        <para>Lists values that specify how the button indicates executed asynchronous operations.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.AsyncDisplayMode.None">
      <summary>
        <para>Asynchronous operations are executed without a visual indication.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.AsyncDisplayMode.Wait">
      <summary>
        <para>The button displays the wait indicator during the asynchronous operation.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.AsyncDisplayMode.WaitCancel">
      <summary>
        <para>The button displays the wait indicator and changes it to the cancel button when a user hovers the mouse pointer over the wait indicator. If a user presses the cancel button, the associated asynchronous command’s IsCancellationRequested property is changed to true.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.BindableBase">
      <summary>
        <para>Provides support for the INotifyPropertyChanged interface and capabilities for easy implementation of bindable properties with the <see href="https://docs.devexpress.com/WPF/17350/mvvm-framework/viewmodels/bindablebase">GetProperty</see> and <see href="https://docs.devexpress.com/WPF/17350/mvvm-framework/viewmodels/bindablebase">SetProperty</see> methods.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.BindableBase.GetPropertyName``1(System.Linq.Expressions.Expression{System.Func{``0}})">
      <summary>
        <para>Returns the name of a property identified by a lambda expression.</para>
      </summary>
      <param name="expression">A lambda expression selecting the property.</param>
      <returns>The name of the property accessed by expression.</returns>
    </member>
    <member name="E:DevExpress.Mvvm.BindableBase.PropertyChanged">
      <summary>
        <para>Occurs when a property value changes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.CellEditorData">
      <summary>
        <para>An objects that allows you to specify editor’s settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.CellEditorData.#ctor(System.Object,System.String,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.CellEditorData.FieldName">
      <summary>
        <para>Gets the editor’s field name.</para>
      </summary>
      <value>The editor’s field name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.CellEditorData.ReadOnly">
      <summary>
        <para>Gets or sets whether a user can edit data in the editor.</para>
      </summary>
      <value>true if a user cannot edit data in the editor; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.CellEditorData.Value">
      <summary>
        <para>Gets or sets a value that the editor contains.</para>
      </summary>
      <value>A value that the editor contains.</value>
    </member>
    <member name="T:DevExpress.Mvvm.CellValue">
      <summary>
        <para>Contains information about a processed cell.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.CellValue.#ctor(System.Object,System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.CellValue"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.CellValue.Property">
      <summary>
        <para>Gets a field name in a data source. A column bound to this field contains the cell.</para>
      </summary>
      <value>A field name in a data source. A column bound to this field contains the cell.</value>
    </member>
    <member name="P:DevExpress.Mvvm.CellValue.Row">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/WPF/6097/controls-and-libraries/data-grid/grid-view-data-layout/rows-and-cards/identify-rows-and-cards">handle</see> of a row that contains the cell.</para>
      </summary>
      <value>The <see href="https://docs.devexpress.com/WPF/6097/controls-and-libraries/data-grid/grid-view-data-layout/rows-and-cards/identify-rows-and-cards">handle</see> of a row that contains the cell.</value>
    </member>
    <member name="P:DevExpress.Mvvm.CellValue.Value">
      <summary>
        <para>Gets an object that the cell contains.</para>
      </summary>
      <value>An object that the cell contains.</value>
    </member>
    <member name="N:DevExpress.Mvvm.DataAnnotations">
      <summary>
        <para>Contains classes that support data annotations and DevExpress Fluent API functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute">
      <summary>
        <para>Provides settings for generating bindable properties by the POCO mechanism.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute"/> class with the specified settings.</para>
      </summary>
      <param name="isBindable">true, to make the property bindable; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.IsBindable">
      <summary>
        <para>Gets whether the property is bindable.</para>
      </summary>
      <value>true, if the property is bindable; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.OnPropertyChangedMethodName">
      <summary>
        <para>Gets or sets the name of the function that is invoked after the property has been changed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the function.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.BindablePropertyAttribute.OnPropertyChangingMethodName">
      <summary>
        <para>Gets or sets the name of the function that is invoked when the property is being changed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that specifies the name of the function.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandAttribute">
      <summary>
        <para>Allows you to generate commands from methods.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.CommandAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandAttribute.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.CommandAttribute"/> class with specified settings.</para>
      </summary>
      <param name="isCommand">true, to generate a command from the method; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.CanExecuteMethodName">
      <summary>
        <para>Gets or sets the name of the function that defines whether the command can be executed.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the function name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.IsCommand">
      <summary>
        <para>Gets whether to generate a command from the method.</para>
      </summary>
      <value>true, to generate a command from the method; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.Name">
      <summary>
        <para>Gets or sets the name of the generated command.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the name of the generated command.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.CommandAttribute.UseCommandManager">
      <summary>
        <para>Gets or sets whether to use a function that manages the command execution. This property applies to WPF projects.</para>
      </summary>
      <value>true to use the command manager; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilder`1">
      <summary>
        <para>Provides the command generating functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2">
      <summary>
        <para>Provides base command generating functionality.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.EndCommand">
      <summary>
        <para>Moves the method call chain from the command metadata builder to the metadata builder.</para>
      </summary>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageName(System.String)">
      <summary>
        <para>Configures the image name.</para>
      </summary>
      <param name="imageName">An image name.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageUri(System.String)">
      <summary>
        <para>Configures the image Uri.</para>
      </summary>
      <param name="imageUri">An image Uri.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageUriLarge(System.String)">
      <summary>
        <para>Configures the large image Uri.</para>
      </summary>
      <param name="uri">An image Uri.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.ImageUriSmall(System.String)">
      <summary>
        <para>Configures the small image Uri.</para>
      </summary>
      <param name="uri">An image Uri.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.CommandMetadataBuilderBase`2.Parameter``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>
        <para>Specifies the ViewModel property.</para>
      </summary>
      <param name="propertyExpression">A lambda expression that returns a ViewModel property.</param>
      <returns>The metadata builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.CommandMethodMetadataBuilder`1">
      <summary>
        <para>Provides the command generating functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode">
      <summary>
        <para>Lists the values used to specify the formatting applied to date-time values.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode.Date">
      <summary>
        <para>Displays a date.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode.DateTime">
      <summary>
        <para>Displays a date and time.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode.Time">
      <summary>
        <para>Displays a time.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute">
      <summary>
        <para>Provides date-time mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute.AutomaticallyAdvanceCaret">
      <summary>
        <para>Specifies whether to enable the caret automatic movement feature.</para>
      </summary>
      <value>true, to enable the caret automatic movement feature; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DateTimeMaskAttribute.Culture">
      <summary>
        <para>Specifies the name of the culture whose settings are used by the mask.</para>
      </summary>
      <value>A culture’s name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.DXImageAttribute">
      <summary>
        <para>Provides the ability to associate an image file with a data item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.DXImageAttribute"/> class with the specified owner.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.#ctor(System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.DXImageAttribute"/> class with the specified image name.</para>
      </summary>
      <param name="imageName">The image name.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.ImageName">
      <summary>
        <para>Gets the image name.</para>
      </summary>
      <value>The image name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.LargeImageUri">
      <summary>
        <para>Gets or sets the absolute Uri that defines the location of an image file.</para>
      </summary>
      <value>A string that is a Uri to the image file.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.DXImageAttribute.SmallImageUri">
      <summary>
        <para>Gets or sets the Uri that defines the relative location of an image file.</para>
      </summary>
      <value>A string that is a Uri to the image file.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.HiddenAttribute">
      <summary>
        <para>Configures the property to be hidden.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.HiddenAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.HiddenAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.IMetadataLocator">
      <summary>
        <para>Used to implement metadata locators.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.IMetadataLocator.GetMetadataTypes(System.Type)">
      <summary>
        <para>Returns a list of types of metadata classes for the specified type.</para>
      </summary>
      <param name="type">A type for which to get a list of types of metadata classes.</param>
      <returns>A list of types of metadata classes for the specified type.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.IMetadataProvider`1">
      <summary>
        <para>Provides methods to build metadata.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.IMetadataProvider`1.BuildMetadata(DevExpress.Mvvm.DataAnnotations.MetadataBuilder{`0})">
      <summary>
        <para>Builds data source configurations.</para>
      </summary>
      <param name="builder">A metadata builder instance.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute">
      <summary>
        <para>Contains attributes for the instance initializer builder.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute.#ctor(System.Type,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute.#ctor(System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute"/> class.</para>
      </summary>
      <param name="type">A <see cref="T:System.Type"/> object that specifies the instance type.</param>
      <param name="name">A <see cref="T:System.String"/> value that specifies the instance name.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute.#ctor(System.Type)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.InstanceInitializerAttribute"/> class.</para>
      </summary>
      <param name="type">A <see cref="T:System.Type"/> object that specifies the instance type.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.LayoutBuilderBase`2">
      <summary>
        <para>Provides the base functionality for building layouts.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.MaskAttributeBase">
      <summary>
        <para>Provides base mask attributes.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.MaskAttributeBase.Mask">
      <summary>
        <para>Specifies a mask expression.</para>
      </summary>
      <value>A string representing a mask expression.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.MaskAttributeBase.UseAsDisplayFormat">
      <summary>
        <para>Specifies whether display values are still formatted using the mask when the editor is not focused. This is a dependency property.</para>
      </summary>
      <value>true if the mask settings are used to format display values when the editor is not focused; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1">
      <summary>
        <para>The base metadata builder class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Command(System.Linq.Expressions.Expression{System.Func{`0,System.Windows.Input.ICommand}})">
      <summary>
        <para>Returns a command metadata builder for a command property.</para>
      </summary>
      <param name="propertyExpression">A lambda expression which specifies a command property.</param>
      <returns>The command metadata builder.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.CommandFromMethod(System.Linq.Expressions.Expression{System.Action{`0}})">
      <summary>
        <para>Returns a command method metadata builder for a method from which the command property is to be generated.</para>
      </summary>
      <param name="methodExpression">A lambda expression which specifies a command method.</param>
      <returns>The command method metadata builder.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Property``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>
        <para>Initializes a property metadata builder.</para>
      </summary>
      <param name="propertyExpression">A lambda expression that returns a ViewModel property.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.Property``1(System.String)">
      <summary>
        <para>Initializes a property metadata builder.</para>
      </summary>
      <param name="propertyName">The name of a ViewModel property.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.TableLayout">
      <summary>
        <para>Initializes a table group container builder.</para>
      </summary>
      <returns>The table group container builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataBuilder`1.ToolBarLayout">
      <summary>
        <para>Initializes a toolbar layout builder instance.</para>
      </summary>
      <returns>The toolbar layout builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.MetadataLocator">
      <summary>
        <para>Allows you to register metadata classes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata(System.Type,System.Type)">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <param name="type">The data class.</param>
      <param name="metadataType">The metadata class.</param>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata(System.Type)">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <param name="metadataType">The metadata class.</param>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata``1">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.AddMetadata``2">
      <summary>
        <para>Registers a metadata class.</para>
      </summary>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.MetadataLocator.Create">
      <summary>
        <para>Creates a metadata locator instance.</para>
      </summary>
      <returns>The metadata locator instance.</returns>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.MetadataLocator.Default">
      <summary>
        <para>The metadata to be registered.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute">
      <summary>
        <para>Contains attributes that are used to initialize a new item of a dictionary.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute.#ctor(System.Type,System.String,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute"/> class with specified settings.</para>
      </summary>
      <param name="type">Specifies the instance type.</param>
      <param name="name">Specifies the instance name.</param>
      <param name="description">Specifies the instance description.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute.#ctor(System.Type,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute"/> class with specified settings.</para>
      </summary>
      <param name="type">Specifies the instance type.</param>
      <param name="name">Specifies the instance name.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute.#ctor(System.Type)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute"/> class with specified settings.</para>
      </summary>
      <param name="type">Specifies the instance type.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute.CreateInstance(System.ComponentModel.ITypeDescriptorContext,System.Collections.IEnumerable)">
      <summary>
        <para>Creates a dictionary item with a key/value pair.</para>
      </summary>
      <param name="context">Context information about a new dictionary item to be created.</param>
      <param name="dictionary">A new dictionary item to be created.</param>
      <returns>A KeyValuePair object that contains the key/value pair.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute.Equals(System.Object)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NewItemInstanceInitializerAttribute.GetHashCode">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute">
      <summary>
        <para>Provides numeric mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute.AlwaysShowDecimalSeparator">
      <summary>
        <para>Gets or sets whether to display the decimal separator even if the fractional part is zero.</para>
      </summary>
      <value>true to display the decimal separator even if the fractional part is zero; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.NumericMaskAttribute.Culture">
      <summary>
        <para>Specifies the name of the culture whose settings are used by the mask.</para>
      </summary>
      <value>A culture’s name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions">
      <summary>
        <para>Contains methods specific to numeric unsigned data types.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt16}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt32}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.UInt64}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt16})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt32})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.NumericUnsignedPropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.UInt64})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute">
      <summary>
        <para>Provides settings for generating POCO View models.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.ImplementIDataErrorInfo">
      <summary>
        <para>Specifies whether to implement the IDataErrorInfo interface.</para>
        <para>The IDataErrorInfo interface provides the functionality to offer custom error information that a user interface can bind to.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.ImplementINotifyPropertyChanging">
      <summary>
        <para>Specifies whether to implement the INotifyPropertyChanging interface.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.POCOViewModelAttribute.InvokeOnPropertyChangedMethodBeforeRaisingINPC">
      <summary>
        <para>Specifies the order of invoking methods and events. If enabled, the OnPropertyChanging\OnPropertyChanged methods are called before invoking the corresponding PropertyChanging\PropertyChanged events. Otherwise, events are invoked before calling corresponding methods.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PredefinedMasks">
      <summary>
        <para>Contains predefined mask expressions.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyLocation">
      <summary>
        <para>Lists values that specify whether the property is positioned before or after properties without the specified location.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PropertyLocation.AfterPropertiesWithoutSpecifiedLocation">
      <summary>
        <para>The property is positioned after properties without the specified location.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DataAnnotations.PropertyLocation.BeforePropertiesWithoutSpecifiedLocation">
      <summary>
        <para>The property is positioned before properties without the specified location.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder`2">
      <summary>
        <para>Provides data source configuration functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderBase`3">
      <summary>
        <para>The base class for the data source configuration functionality.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions">
      <summary>
        <para>Provides additional data source configuration functionality.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CreditCardDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display credit card data values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Byte})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Decimal})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Double})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int16})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int32})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Int64})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Decimal}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Double}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int16}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int32}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Int64}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.Single}})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.CurrencyDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Single})">
      <summary>
        <para>Configures the property editor to display currency values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.DateTimeDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.DateTime},DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode)">
      <summary>
        <para>Configures the property editor to display date-time values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="displayMode">The required <see cref="T:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode"/> value.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.DateTimeDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{System.DateTime}},DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode)">
      <summary>
        <para>Configures the property editor to display date-time values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="displayMode">The required <see cref="T:DevExpress.Mvvm.DataAnnotations.DateTimeDisplayMode"/> value.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.EmailAddressDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display email address values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.ImageUrlDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String})">
      <summary>
        <para>Configures the property editor to display image url values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.InRange``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,``1},``1,``1,System.Func{System.String})">
      <summary>
        <para>Specifies property value boundaries.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <param name="minimum">The initial boundary.</param>
      <param name="maximum">The final boundary.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.InRange``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Nullable{``1}},System.Nullable{``1},System.Nullable{``1},System.Func{System.String})">
      <summary>
        <para>Specifies property value boundaries.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <param name="minimum">The initial boundary.</param>
      <param name="maximum">The final boundary.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.MultilineTextDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String})">
      <summary>
        <para>Configures the property editor to display multiline data values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.PasswordDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String})">
      <summary>
        <para>Configures the property editor to display password values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.PhoneNumberDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display phone number values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.ScaffoldDetailCollection``2(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.Collections.Generic.IEnumerable{``1}})">
      <summary>
        <para>Scaffolds a detail collection.</para>
      </summary>
      <param name="builder">A property metadata builder instance.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilderExtensions.UrlDataType``1(DevExpress.Mvvm.DataAnnotations.PropertyMetadataBuilder{``0,System.String},System.Func{System.String})">
      <summary>
        <para>Configures the property editor to display URL values.</para>
      </summary>
      <param name="builder">A property metadata builder.</param>
      <param name="errorMessageAccessor">A function that returns an error message.</param>
      <returns>The property metadata builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute">
      <summary>
        <para>Provides RegEx mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegExMaskAttribute.ShowPlaceHolders">
      <summary>
        <para>Gets or sets whether placeholders are displayed in a masked editor.</para>
      </summary>
      <value>true to display placeholders in a masked editor; otherwise false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase">
      <summary>
        <para>Provides base RegEx mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase.IgnoreBlank">
      <summary>
        <para>Specifies whether the editor can lose focus when a value has not been completely filled.</para>
      </summary>
      <value>true, if the editor can lose focus when a value has not been completely filled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegExMaskAttributeBase.PlaceHolder">
      <summary>
        <para>Gets or sets the character used as the placeholder in a masked editor.</para>
      </summary>
      <value>The character used as the placeholder.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute">
      <summary>
        <para>Provides regular mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.RegularMaskAttribute.SaveLiteral">
      <summary>
        <para>Gets or sets whether constantly displayed mask characters (literals) are included in an editor’s value.</para>
      </summary>
      <value>true if the constantly displayed mask characters are included in an editor’s value; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute">
      <summary>
        <para>Provides settings for generating service properties by the POCO mechanism.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.#ctor(DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute"/> class with specified settings.</para>
      </summary>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute"/> class with specified settings.</para>
      </summary>
      <param name="isServiceProperty">true, to allow service generation; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.IsServiceProperty">
      <summary>
        <para>Gets whether service generation is allowed.</para>
      </summary>
      <value>true, if service generation is allowed; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.Key">
      <summary>
        <para>Gets or sets the service key name.</para>
      </summary>
      <value>A <see cref="T:System.String"/> value that is the service key name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.ServicePropertyAttribute.SearchMode">
      <summary>
        <para>Gets or sets whether a search for a service must be carried out within the current service container, or within the current and parent service containers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute">
      <summary>
        <para>Provides simple mask attributes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DataAnnotations.SimpleMaskAttribute.SaveLiteral">
      <summary>
        <para>Gets or sets whether constantly displayed mask characters (literals) are included in an editor’s value.</para>
      </summary>
      <value>true if the constantly displayed mask characters are included in an editor’s value; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1">
      <summary>
        <para>Provides methods for building table group containers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1.EndGroupContainer">
      <summary>
        <para>Moves the method call chain one level higher.</para>
      </summary>
      <returns>The table group container builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1.Group(System.String)">
      <summary>
        <para>Initializes a group builder.</para>
      </summary>
      <param name="groupName">A group name.</param>
      <returns>The table group layout builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupContainerLayoutBuilder`1.GroupContainer(System.String)">
      <summary>
        <para>Initializes a group container builder.</para>
      </summary>
      <param name="groupName">A group container name.</param>
      <returns>The table group container layout builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DataAnnotations.TableGroupLayoutBuilder`1">
      <summary>
        <para>Provides methods for building table groups.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupLayoutBuilder`1.ContainsProperty``1(System.Linq.Expressions.Expression{System.Func{`0,``0}})">
      <summary>
        <para>Configures the specified ViewModel property to be displayed within the group.</para>
      </summary>
      <param name="propertyExpression">A lambda expression that returns a ViewModel property.</param>
      <returns>The table group layout builder instance.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DataAnnotations.TableGroupLayoutBuilder`1.EndGroup">
      <summary>
        <para>Moves the method call chain from the group builder to the table group container builder.</para>
      </summary>
      <returns>The table group container builder instance.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DateTimeRange">
      <summary>
        <para>Time interval which provides extended operations such as Union and Intersect.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.#ctor(DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DateTimeRange"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.#ctor(System.DateTime,System.DateTime)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DateTimeRange"/> class with the specified settings.</para>
      </summary>
      <param name="start">A <see cref="T:System.DateTime"/> object that represents the time range start.</param>
      <param name="end">A <see cref="T:System.DateTime"/> object that represents the time range end.</param>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.Empty">
      <summary>
        <para>Returns an empty datetime range.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> value representing an empty datetime range.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.End">
      <summary>
        <para>Gets the end date and time of the datetime range.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> value representing the end of the datetime range.</value>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Equals(System.Object)">
      <summary>
        <para>Defines whether the given object is equal to the current <see cref="T:DevExpress.Mvvm.DateTimeRange"/> instance.</para>
      </summary>
      <param name="obj">The object to compare with the current instance.</param>
      <returns>true, if the given object is equal to the current <see cref="T:DevExpress.Mvvm.DateTimeRange"/> instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current TimeInterval object.</para>
      </summary>
      <returns>An integer value representing the hash code for the current object.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.IsEmpty">
      <summary>
        <para>Determines whether the current datetime range is empty.</para>
      </summary>
      <value>true, if the range is empty; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.IsValid">
      <summary>
        <para>Gets whether the current datetime range is valid.</para>
      </summary>
      <value>true, if the range is valid; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DateTimeRange.Start">
      <summary>
        <para>Gets the start date and time of the datetime range.</para>
      </summary>
      <value>A <see cref="T:System.DateTime"/> value representing the start of the datetime range.</value>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.ToString">
      <summary>
        <para>Returns the textual representation of the datetime range.</para>
      </summary>
      <returns>A <see cref="T:System.String"/> value which is the textual representation of the datetime range.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.ToString(System.IFormatProvider)">
      <summary>
        <para>Provides a culture-specific string representation of the time range.</para>
      </summary>
      <param name="provider">An object, implementing the <see cref="T:System.IFormatProvider"/> interface, which specifies the datetime formatting template.</param>
      <returns>A string value representing the <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Union(DevExpress.Mvvm.DateTimeRange,DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para>Returns a datetime range which is composed of the two given time ranges.</para>
      </summary>
      <param name="x">A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object which represents the first time range to be united.</param>
      <param name="y">A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object which represents the second time range to be united.</param>
      <returns>A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object representing the union of the two time ranges.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DateTimeRange.Union(DevExpress.Mvvm.DateTimeRange)">
      <summary>
        <para>Returns a datetime range which is composed of the current time range and the given datetime range.</para>
      </summary>
      <param name="x">A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object that is the time range to be united.</param>
      <returns>A <see cref="T:DevExpress.Mvvm.DateTimeRange"/> object representing the union of the two datetime ranges.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DelegateCommand">
      <summary>
        <para>A command that calls your parameterless delegates when Execute and CanExecute logic is invoked on the command.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand.#ctor(System.Action,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand.#ctor(System.Action,System.Func{System.Boolean},System.Nullable{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="canExecuteMethod">The execution status logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand.#ctor(System.Action)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DelegateCommand`1">
      <summary>
        <para>A command that calls your parameterized delegates when Execute and CanExecute logic is invoked on the command.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.#ctor(System.Action{`0},System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand`1"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.#ctor(System.Action{`0},System.Func{`0,System.Boolean},System.Nullable{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand`1"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
      <param name="canExecuteMethod">The execution status logic.</param>
      <param name="useCommandManager">Specifies whether the command is automatically updated by the command manager.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.#ctor(System.Action{`0})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DelegateCommand`1"/> that can always be executed.</para>
      </summary>
      <param name="executeMethod">The execution logic.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DelegateCommand`1.Execute(`0)">
      <summary>
        <para>Defines the method to be called when the command is invoked.</para>
      </summary>
      <param name="parameter">Data used by the command. If the command does not require data to be passed, this object can be set to a null reference.</param>
    </member>
    <member name="T:DevExpress.Mvvm.DialogButtonAlignment">
      <summary>
        <para>Lists the DialogButton’s horizontal alignment values.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DialogButtonAlignment.Center">
      <summary>
        <para>The DialogButton is centered within the dialog’s content area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DialogButtonAlignment.Left">
      <summary>
        <para>The DialogButton is aligned to the left of the dialog’s content area.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DialogButtonAlignment.Right">
      <summary>
        <para>The DialogButton is aligned to the right of the dialog’s content area.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.DialogServiceExtensions">
      <summary>
        <para>Contains extension methods for the <see cref="T:DevExpress.Mvvm.IDialogService"/> interface.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DialogServiceExtensions.ShowDialog(DevExpress.Mvvm.IDialogService,DevExpress.Mvvm.MessageButton,System.String,System.Object)">
      <summary>
        <para>Shows a dialog window with the specified parameters.</para>
      </summary>
      <param name="service">The dialog service.</param>
      <param name="dialogButtons">A set of buttons displayed in the dialog window.</param>
      <param name="title">The dialog window’s header text.</param>
      <param name="viewModel">The dialog’s View Model.</param>
      <returns>The dialog’s result value.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DialogServiceExtensions.ShowDialog(DevExpress.Mvvm.IDialogService,DevExpress.Mvvm.MessageButton,System.String,System.String,System.Object,System.Object)">
      <summary>
        <para>Shows a dialog window with the specified parameters.</para>
      </summary>
      <param name="service">The dialog service.</param>
      <param name="dialogButtons">A set of buttons displayed in the dialog window.</param>
      <param name="title">The dialog window’s header text.</param>
      <param name="documentType">The name of the class displayed in the dialog window.</param>
      <param name="parameter">The parameter that allows you to pass data to the View Model.</param>
      <param name="parentViewModel">The dialog’s parent View Model.</param>
      <returns>The dialog’s result value.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DialogServiceExtensions.ShowDialog(DevExpress.Mvvm.IDialogService,DevExpress.Mvvm.MessageButton,System.String,System.String,System.Object)">
      <summary>
        <para>Shows a dialog window with the specified parameters.</para>
      </summary>
      <param name="service">The dialog service.</param>
      <param name="dialogButtons">A set of buttons displayed in the dialog window.</param>
      <param name="title">The dialog window’s header text.</param>
      <param name="documentType">The name of the class displayed in the dialog window.</param>
      <param name="viewModel">The dialog’s View Model.</param>
      <returns>The dialog’s result value.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DialogServiceExtensions.ShowDialog(DevExpress.Mvvm.IDialogService,System.Collections.Generic.IEnumerable{DevExpress.Mvvm.UICommand},System.String,System.Object)">
      <summary>
        <para>Shows a dialog window with the specified parameters.</para>
      </summary>
      <param name="service">The dialog service.</param>
      <param name="dialogCommands">A set of UICommands displayed in the dialog window.</param>
      <param name="title">The dialog window’s header text.</param>
      <param name="viewModel">The dialog’s View Model.</param>
      <returns>The clicked button’s UICommand value.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DialogServiceExtensions.ShowDialog(DevExpress.Mvvm.IDialogService,System.Collections.Generic.IEnumerable{DevExpress.Mvvm.UICommand},System.String,System.String,System.Object,System.Object)">
      <summary>
        <para>Shows a dialog window with the specified parameters.</para>
      </summary>
      <param name="service">The dialog service.</param>
      <param name="dialogCommands">A set of UICommands displayed in the dialog window.</param>
      <param name="title">The dialog window’s header text.</param>
      <param name="documentType">The name of the class displayed in the dialog window.</param>
      <param name="parameter">The parameter that allows you to pass data to the View Model.</param>
      <param name="parentViewModel">The dialog’s parent View Model.</param>
      <returns>The clicked button’s UICommand value.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DialogServiceExtensions.ShowDialog(DevExpress.Mvvm.IDialogService,System.Collections.Generic.IEnumerable{DevExpress.Mvvm.UICommand},System.String,System.String,System.Object)">
      <summary>
        <para>Shows a dialog window with the specified parameters.</para>
      </summary>
      <param name="service">The dialog service.</param>
      <param name="dialogCommands">A set of UICommands displayed in the dialog window.</param>
      <param name="title">The dialog window’s header text.</param>
      <param name="documentType">The name of the class displayed in the dialog window.</param>
      <param name="viewModel">The dialog’s View Model.</param>
      <returns>The clicked button’s UICommand value.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DocumentManagerServiceExtensions">
      <summary>
        <para>Provides extension methods for <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/> implementation to create and control a service’s documents.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified View Model.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="viewModel">An object specifying the document’s View Models.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.String,System.Object,System.Object,System.Boolean)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified parameters.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.String,System.Object,System.Object)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="documentType">A System.String value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="parameter">A parameter passed to the view model via the <see cref="P:DevExpress.Mvvm.ISupportParameter.Parameter">ISupportParameter.Parameter</see> property.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel">ISupportParentViewModel.ParentViewModel</see> property.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocument(DevExpress.Mvvm.IDocumentManagerService,System.String,System.Object)">
      <summary>
        <para>Static extension method that creates and returns a IDocument descendant with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="documentType">A System.String value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="viewModel">An object specifying the document’s View Models.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.CreateDocumentIfNotExistsAndShow(DevExpress.Mvvm.IDocumentManagerService,DevExpress.Mvvm.IDocument@,System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Static extension method that creates and displays a new document if a document with the specified parameters does not exist. Otherwise, the document with the specified parameters will be shown.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="documentStorage">An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface that represents the document storage.</param>
      <param name="documentType">A System.String value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="parameter">A parameter passed to the view model via the <see cref="P:DevExpress.Mvvm.ISupportParameter.Parameter">ISupportParameter.Parameter</see> property.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel">ISupportParentViewModel.ParentViewModel</see> property.</param>
      <param name="title">A System.String value specifying the document title.</param>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocument(DevExpress.Mvvm.IDocumentManagerService,System.Object,System.Object)">
      <summary>
        <para>Static extension method that finds and returns a document with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="parameter">A parameter passed to the view model via the <see cref="P:DevExpress.Mvvm.ISupportParameter.Parameter">ISupportParameter.Parameter</see> property.</param>
      <param name="parentViewModel">An object that is passed to the view model of the view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel">ISupportParentViewModel.ParentViewModel</see> property.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocument(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that finds and returns a document with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="viewModel">An object specifying the document’s View Models.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocumentById(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that retrieves the document by the specified identifier.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="id">An System.Object object that represents the document’s <see cref="P:DevExpress.Mvvm.IDocument.Id">IDocument.Id</see>.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.FindDocumentByIdOrCreate(DevExpress.Mvvm.IDocumentManagerService,System.Object,System.Func{DevExpress.Mvvm.IDocumentManagerService,DevExpress.Mvvm.IDocument})">
      <summary>
        <para>Static extension method that retrieves the document by the specified identifier. If a document with such an identifier does not exist, it will be created.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="id">An System.Object object that represents the document’s <see cref="P:DevExpress.Mvvm.IDocument.Id">IDocument.Id</see>.</param>
      <param name="createDocumentCallback">A System.Func object specifying the Callback function of the document creation.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.DocumentManagerServiceExtensions.GetDocumentsByParentViewModel(DevExpress.Mvvm.IDocumentManagerService,System.Object)">
      <summary>
        <para>Static extension method that returns a collection of documents whose Parent View Model (<see cref="T:DevExpress.Mvvm.ISupportParentViewModel"/>) is equal to the specified View Model.</para>
      </summary>
      <param name="service">A System.Type value specifying the type of the service.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view via the <see cref="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel">ISupportParentViewModel.ParentViewModel</see> property.</param>
      <returns>A collection of objects implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.DXSplashScreenViewModel">
      <summary>
        <para>A view model that specifies data and options for the <see cref="T:DevExpress.Xpf.Core.SplashScreenManager">SplashScreenManager</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.DXSplashScreenViewModel.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.DXSplashScreenViewModel"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Copyright">
      <summary>
        <para>Specifies the copyright notice.</para>
      </summary>
      <value>The copyright notice text.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.DesignTimeData">
      <summary>
        <para>For internal use. The default view model instance.</para>
      </summary>
      <value>A default view model instance.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.IsIndeterminate">
      <summary>
        <para>Specifies whether the to apply the marquee style to the progress bar displayed by the splash screen.</para>
      </summary>
      <value>true, to apply the marquee style to the progress bar; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Logo">
      <summary>
        <para>Specifies the logo image.</para>
      </summary>
      <value>A logo image in the System.Uri format.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Progress">
      <summary>
        <para>Specifies the current position of the progress bar displayed by the splash screen.</para>
      </summary>
      <value>A System.Double value from 0d to 100d.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Status">
      <summary>
        <para>Specifies the status text.</para>
      </summary>
      <value>The status text.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Subtitle">
      <summary>
        <para>Specifies the subtitle text.</para>
      </summary>
      <value>A string value that is the subtitle.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Tag">
      <summary>
        <para>Specifies the additional data associated with the view model instance.</para>
      </summary>
      <value>An object that contains the additional data is associated with the view model instance.</value>
    </member>
    <member name="P:DevExpress.Mvvm.DXSplashScreenViewModel.Title">
      <summary>
        <para>Specifies the title text.</para>
      </summary>
      <value>The title text.</value>
    </member>
    <member name="T:DevExpress.Mvvm.DXWindowState">
      <summary>
        <para>Lists values that specify the window state (minimized, maximized, restored).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DXWindowState.Maximized">
      <summary>
        <para>The window is maximized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DXWindowState.Minimized">
      <summary>
        <para>The window is minimized.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.DXWindowState.Normal">
      <summary>
        <para>The window is restored.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.EnumMemberInfo">
      <summary>
        <para>An object created from an enumeration member by the EnumItemsSource extension or <see cref="T:DevExpress.Mvvm.UI.EnumItemsSourceBehavior"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.#ctor(System.String,System.String,System.Object,System.Boolean,System.Boolean,System.Func{System.Windows.Media.ImageSource},System.Nullable{System.Int32})">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.#ctor(System.String,System.String,System.Object,System.Boolean,System.Boolean,System.Func{System.Windows.Media.ImageSource},System.Nullable{System.Windows.Size},System.Nullable{System.Int32})">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.#ctor(System.String,System.String,System.Object,System.Windows.Media.ImageSource,System.Boolean,System.Boolean,System.Nullable{System.Int32})">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.#ctor(System.String,System.String,System.Object,System.Windows.Media.ImageSource)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.Description">
      <summary>
        <para>Gets the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description"/> property value of the applied <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute"/>.</para>
      </summary>
      <value>The <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Description"/> property value of the applied <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute"/>.</value>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.Equals(System.Object)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.GetHashCode">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.Id">
      <summary>
        <para>Gets the enumeration member.</para>
      </summary>
      <value>The enumeration member.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.Image">
      <summary>
        <para>Gets the image specified by the DevExpress.Mvvm.DataAnnotations.ImageAttribute.</para>
      </summary>
      <value>The image specified by the DevExpress.Mvvm.DataAnnotations.ImageAttribute.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.ImageHeight">
      <summary>
        <para>Gets the image height.</para>
      </summary>
      <value>The image height.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.ImageWidth">
      <summary>
        <para>Gets the image width.</para>
      </summary>
      <value>The image width.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.Name">
      <summary>
        <para>Gets the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name"/> property value of the applied <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute"/>.</para>
      </summary>
      <value>The <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Name"/> property value of the applied <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.Order">
      <summary>
        <para>Gets the <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order"/> property value of the applied <see cref="T:System.ComponentModel.DataAnnotations.DisplayAttribute"/>.</para>
      </summary>
      <value>The <see cref="P:System.ComponentModel.DataAnnotations.DisplayAttribute.Order"/> property value of the applied <see cref="N:System.ComponentModel.DataAnnotations"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.ShowImage">
      <summary>
        <para>Gets whether an image from the DevExpress.Mvvm.DataAnnotations.ImageAttribute is displayed in the generated item (<see cref="P:DevExpress.Mvvm.UI.EnumItemsSourceBehavior.AllowImages">EnumItemsSourceBehavior.AllowImages</see>).</para>
      </summary>
      <value>true if the image is displayed in the generated item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.EnumMemberInfo.ShowName">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.EnumMemberInfo.ToString">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Mvvm.Gantt">
      <summary>
        <para>Contains the predefined <see cref="T:DevExpress.Xpf.Gantt.GanttControl"/> data classes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttPredecessorLink">
      <summary>
        <para>A <see href="https://docs.devexpress.com/WPF/400349/controls-and-libraries/gantt-control/task-dependencies">task dependency</see> data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttPredecessorLink.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttPredecessorLink"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.Lag">
      <summary>
        <para>Specifies the task dependency time lag.</para>
      </summary>
      <value>A task dependency time lag.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.LinkType">
      <summary>
        <para>Specifies the task relationship type (FinishToStart, FinishToFinish, etc.)</para>
      </summary>
      <value>A task relationship type.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.PredecessorTaskId">
      <summary>
        <para>Specifies the predecessor task’s id.</para>
      </summary>
      <value>A predecessor task’s id.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttPredecessorLink.Tag">
      <summary>
        <para>Specifies the connector’s additional info.</para>
      </summary>
      <value>A connector’s additional info.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttResource">
      <summary>
        <para>A Gantt <see href="https://docs.devexpress.com/WPF/401649/controls-and-libraries/gantt-control/resources">resource</see> data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttResource.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttResource"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResource.Color">
      <summary>
        <para>Gets or sets the resource’s color.</para>
      </summary>
      <value>The resource’s color.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResource.Id">
      <summary>
        <para>Gets or sets the resource’s ID.</para>
      </summary>
      <value>The resource’s ID.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResource.Name">
      <summary>
        <para>Gets or sets the resource’s name.</para>
      </summary>
      <value>The resource’s name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.GanttResourceLink">
      <summary>
        <para>A <see href="https://docs.devexpress.com/WPF/401649/controls-and-libraries/gantt-control/resources">resource</see> dependency data object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Gantt.GanttResourceLink.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Gantt.GanttResourceLink"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResourceLink.AllocationPercentage">
      <summary>
        <para>Gets or sets a percentage of time a resource spends on a task.</para>
      </summary>
      <value>A percentage of time a resource spends on a task.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Gantt.GanttResourceLink.ResourceId">
      <summary>
        <para>Gets or sets a <see href="https://docs.devexpress.com/WPF/401649/controls-and-libraries/gantt-control/resources">resource</see> ID.</para>
      </summary>
      <value>A resource ID.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Gantt.PredecessorLinkType">
      <summary>
        <para>Lists the values used to specify the Gantt task relationships.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.FinishToFinish">
      <summary>
        <para>The successor task may not finish before the predecessor task is finished.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.FinishToStart">
      <summary>
        <para>The successor task may not start before the predecessor task is finished.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.StartToFinish">
      <summary>
        <para>The successor task may not finish before the predecessor task is started.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Gantt.PredecessorLinkType.StartToStart">
      <summary>
        <para>The successor task may not start before the predecessor task is started.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.GlyphAlignment">
      <summary>
        <para>Lists image positions in the dialog button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.GlyphAlignment.Bottom">
      <summary>
        <para>The image is located below the button’s content.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.GlyphAlignment.Left">
      <summary>
        <para>The image is located on the left side of the dialog button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.GlyphAlignment.Right">
      <summary>
        <para>The image is located on the right side of the dialog button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.GlyphAlignment.Top">
      <summary>
        <para>The image is located above the button’s content.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.IApplicationJumpListService">
      <summary>
        <para>Provides methods to add jump tasks to the application’s Jump List.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IApplicationJumpListService.AddToRecentCategory(System.String)">
      <summary>
        <para>Adds the specified jump path to the Recent category of the Jump List.</para>
      </summary>
      <param name="jumpPath">The JumpPath to add to the Jump List.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IApplicationJumpListService.Apply">
      <summary>
        <para>Registers and adds the newly added jump tasks to its Jump List.</para>
      </summary>
      <returns>Returns the collection of rejected items.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IApplicationJumpListService.Items">
      <summary>
        <para>Gets the collection of <see cref="T:DevExpress.Mvvm.UI.ApplicationJumpTask"/> objects that are displayed in the Jump List.</para>
      </summary>
      <value>The collection of <see cref="T:DevExpress.Mvvm.UI.ApplicationJumpTask"/> objects displayed in the Jump List.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IApplicationJumpListService.ShowFrequentCategory">
      <summary>
        <para>Gets or sets a value that indicates whether frequently used items are displayed in the Jump List.</para>
      </summary>
      <value>true, if frequently used items are displayed in the Jump List; otherwise, false.The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IApplicationJumpListService.ShowRecentCategory">
      <summary>
        <para>Gets or sets a value that indicates whether recently used items are displayed in the Jump List.</para>
      </summary>
      <value>true, if recently used items are displayed in the Jump List; otherwise, false.The default is false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ICommand`1">
      <summary>
        <para>The interface that specifies a parameterized command.</para>
      </summary>
      <typeparam name="T">The command parameter.</typeparam>
    </member>
    <member name="M:DevExpress.Mvvm.ICommand`1.CanExecute(`0)">
      <summary>
        <para>Returns whether the command can be executed in its current state.</para>
      </summary>
      <param name="param">The command parameter.</param>
      <returns>true if the command can be executed; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.ICommand`1.Execute(`0)">
      <summary>
        <para>Defines the method called when the command is invoked.</para>
      </summary>
      <param name="param">The command parameter.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ICurrentWindowService">
      <summary>
        <para>Provides methods to work with a window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Activate">
      <summary>
        <para>Activates the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Close">
      <summary>
        <para>Closes the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Hide">
      <summary>
        <para>Hides the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ICurrentWindowService.Show">
      <summary>
        <para>Shows the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ICurrentWindowService.WindowState">
      <summary>
        <para>Gets or sets the specified state for the window associated with the <see cref="T:DevExpress.Mvvm.ICurrentWindowService"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Mvvm.DXWindowState"/> enumeration value</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDataErrorInfoHelper">
      <summary>
        <para>Provides extension methods to get an error based on defined DataAnnotation attributes or Fluent API</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDataErrorInfoHelper.GetErrorText(System.Object,System.String)">
      <summary>
        <para>Returns the error text for the specified property.</para>
      </summary>
      <param name="owner">A System.Object object, which is the owner of the specified property.</param>
      <param name="propertyName">A System.String value that specifies the name of the property.</param>
      <returns>A System.String value that describes the error of the specified property.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IDialogService">
      <summary>
        <para>Provides methods to show a dialog window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDialogService.ShowDialog(System.Collections.Generic.IEnumerable{DevExpress.Mvvm.UICommand},System.String,System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Shows a dialog window with specified parameters.</para>
      </summary>
      <param name="dialogCommands">A list of <see cref="T:DevExpress.Mvvm.UICommand"/> objects that are used to generate dialog buttons.</param>
      <param name="title">A dialog window caption.</param>
      <param name="documentType">A <see cref="T:System.String"/> value that specifies the name of a document type to be shown in the dialog window.</param>
      <param name="viewModel">An object that is the view model to be passed to the data context of the dialog view.</param>
      <param name="parameter">A parameter for passing data to the passed view model.</param>
      <param name="parentViewModel">The parent View model for building a parent-child view model relationship.</param>
      <returns>An object of the <see cref="T:DevExpress.Mvvm.UICommand"/> type that is the command corresponding to the button the end-user clicked.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IDispatcherService">
      <summary>
        <para>Contains methods that use the <see cref="T:System.Windows.Threading.Dispatcher"/> to perform actions in a ViewModel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDispatcherService.BeginInvoke(System.Action)">
      <summary>
        <para>Executes the specified delegate asynchronously.</para>
      </summary>
      <param name="action">The delegate to execute that takes no arguments and does not return a value.</param>
      <returns>The task of the execution process.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IDispatcherService.Invoke(System.Action)">
      <summary>
        <para>Executes the specified delegate.</para>
      </summary>
      <param name="action">The delegate to execute that takes no arguments and does not return a value.</param>
    </member>
    <member name="T:DevExpress.Mvvm.IDocument">
      <summary>
        <para>Provides methods to work with a document created with the IDocumentManagerService.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocument.Close(System.Boolean)">
      <summary>
        <para>Closes the document.</para>
      </summary>
      <param name="force">true, to disable the confirmation logic; otherwise, false.</param>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.Content">
      <summary>
        <para>Specifies the document content.</para>
      </summary>
      <value>The document content.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.DestroyOnClose">
      <summary>
        <para>Specifies whether to release the memory reserved for the document after it is closed.</para>
      </summary>
      <value>true, to release the memory reserved for the document after it is closed; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IDocument.Hide">
      <summary>
        <para>Hides the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.Id">
      <summary>
        <para>Specifies the document ID.</para>
      </summary>
      <value>The document ID.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IDocument.Show">
      <summary>
        <para>Shows the document.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocument.Title">
      <summary>
        <para>Specifies the document title.</para>
      </summary>
      <value>The document title.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentContent">
      <summary>
        <para>Provides a way to obtain the document’s properties at the document’s ViewModel level.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentContent.DocumentOwner">
      <summary>
        <para>Gets or sets the service which owns the current document.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Mvvm.IDocumentOwner"/> implementation that represents the service to which the current document belongs.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentContent.OnClose(System.ComponentModel.CancelEventArgs)">
      <summary>
        <para>Invoked before a document is closed (hidden), and allows you to prevent this action.</para>
      </summary>
      <param name="e">Provides data for the event handler that can be used to prevent specific operations on a document.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentContent.OnDestroy">
      <summary>
        <para>Invoked after a document has been closed (hidden).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentContent.Title">
      <summary>
        <para>Gets a value specifying the document title.</para>
      </summary>
      <value>The text displayed in the document title.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentManagerService">
      <summary>
        <para>Provides methods to create documents and operate with them.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentManagerService.ActiveDocument">
      <summary>
        <para>Specifies the active document.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface.</value>
    </member>
    <member name="E:DevExpress.Mvvm.IDocumentManagerService.ActiveDocumentChanged">
      <summary>
        <para>Invoked each time the active document is changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentManagerService.CreateDocument(System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Creates a document.</para>
      </summary>
      <param name="documentType">A <see cref="T:System.String"/> value that specifies the type of the view to be created by the <see cref="T:DevExpress.Mvvm.IDocumentManagerService"/>.</param>
      <param name="viewModel">An object that is assigned to the DataContext property of the created view.</param>
      <param name="parameter">A parameter to be passed to the view model.</param>
      <param name="parentViewModel">An object that is passed to the view model of the created view.</param>
      <returns>An object implementing the <see cref="T:DevExpress.Mvvm.IDocument"/> interface that is the created document.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IDocumentManagerService.Documents">
      <summary>
        <para>Specifies a set of created documents.</para>
      </summary>
      <value>A set of created documents.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IDocumentOwner">
      <summary>
        <para>Provides a way to obtain the document owner (service) at the document level.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IDocumentOwner.Close(DevExpress.Mvvm.IDocumentContent,System.Boolean)">
      <summary>
        <para>Closes the specified document.</para>
      </summary>
      <param name="documentContent">An <see cref="T:DevExpress.Mvvm.IDocument"/> implementation to be closed.</param>
      <param name="force">true, to disable the confirmation logic; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.Mvvm.IFileDialogServiceBase">
      <summary>
        <para>Provides methods and properties to configure the standard dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.AddExtension">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box automatically adds an extension to the file name if the user omits it.</para>
      </summary>
      <value>true, if the dialog box adds an extension to the file name; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.AutoUpgradeEnabled">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box automatically upgrades appearance and behavior.</para>
      </summary>
      <value>true, if the dialog box automatically upgrades appearance and behavior; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.CheckFileExists">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box displays a warning when the user specifies a file name that does not exist.</para>
      </summary>
      <value>true, if the dialog box displays a warning when the user specifies a file name that does not exist; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.CheckPathExists">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box displays a warning when the user specifies a path that does not exist.</para>
      </summary>
      <value>true, if the dialog box displays a warning when the user specifies a path that does not exist; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.DereferenceLinks">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box returns the location of the file referenced by the shortcut or it returns the location of the shortcut.</para>
      </summary>
      <value>true, if the dialog box returns the location of the file referenced by the shortcut; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.Filter">
      <summary>
        <para>Gets or sets a filter string which specifies options that are available in the “Files of type” box in the dialog box.</para>
      </summary>
      <value>A System.String value containing the file filtering options available in the dialog box. The default is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.FilterIndex">
      <summary>
        <para>Gets or sets the index of the filtering option currently selected in the dialog box.</para>
      </summary>
      <value>A System.Int32 value containing the index of the filtering option currently selected in the file dialog box. The default value is 1.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.InitialDirectory">
      <summary>
        <para>Gets or sets the initial folder shown by the dialog box.</para>
      </summary>
      <value>A System.String value specifying the initial folder shown by the dialog box. The default is an empty string.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileDialogServiceBase.Reset">
      <summary>
        <para>Resets all properties to the default values.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.RestoreDirectory">
      <summary>
        <para>Gets or sets whether the dialog should remember the previous directory and restore it when you show this dialog again.</para>
      </summary>
      <value>true, if the dialog box restores the current directory to the previously selected directory if the initial directory was changed while searching for files; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.ShowHelp">
      <summary>
        <para>Gets or sets a value indicating whether the Help button is displayed in the dialog box.</para>
      </summary>
      <value>true, if the Help button is shown in the dialog box; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.SupportMultiDottedExtensions">
      <summary>
        <para>Gets or sets a value whether the dialog box displays and saves files that have multiple file name extensions.</para>
      </summary>
      <value>true, if the dialog box supports multiple file name extensions; otherwise, false. The default is false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.Title">
      <summary>
        <para>Gets or sets the title displayed by the dialog box.</para>
      </summary>
      <value>A System.String value that specifies the title displayed by the dialog box.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileDialogServiceBase.ValidateNames">
      <summary>
        <para>Gets or sets a value indicating whether the dialog box accepts only valid Win32 file names.</para>
      </summary>
      <value>true, if the dialog box accepts only valid Win32 file names; otherwise, false. The default is true.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IFileSystemInfo">
      <summary>
        <para>Provides members common to <see cref="T:DevExpress.Mvvm.IFolderInfo"/> and <see cref="T:DevExpress.Mvvm.IFileInfo"/></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.Attributes">
      <summary>
        <para>Gets or sets file attributes.</para>
      </summary>
      <value>The file attributes.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileSystemInfo.Delete">
      <summary>
        <para>Deletes a file or folder.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.DirectoryName">
      <summary>
        <para>Gets a string representing the directory’s full path.</para>
      </summary>
      <value>A string that is the directory’s full path.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.Exists">
      <summary>
        <para>Determines whether the specified file exists.</para>
      </summary>
      <value>true, if the file exists; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFileSystemInfo.MoveTo(System.String)">
      <summary>
        <para>Moves a specified file to a new location and allows you to specify a new file name.</para>
      </summary>
      <param name="destinationFileName">The new path and name for the file.</param>
    </member>
    <member name="P:DevExpress.Mvvm.IFileSystemInfo.Name">
      <summary>
        <para>Gets a file or a folder name.</para>
      </summary>
      <value>A file or a folder name.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IFolderBrowserDialogService">
      <summary>
        <para>Provides methods to browse, create, and select folders in the File System by using the standard folder browser dialog.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IFolderBrowserDialogService.ResultPath">
      <summary>
        <para>Gets or sets the path of the selected directory.</para>
      </summary>
      <value>A Systems.Stringvalue specifying the path of the selected directory. The default is an empty string.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IFolderBrowserDialogService.ShowDialog">
      <summary>
        <para>Shows the dialog box.</para>
      </summary>
      <returns>true, if the user clicks OK in the dialog box; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IFolderBrowserDialogService.StartPath">
      <summary>
        <para>Gets or sets the path of the initially selected directory.</para>
      </summary>
      <value>A Systems.Stringvalue specifying the path of the initially selected directory. The default is an empty string.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ILayoutSerializationService">
      <summary>
        <para>Provides methods to save/restore the layout of serializable DevExpress WPF Controls.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.IMessageBoxService">
      <summary>
        <para>Provides methods to show dialog boxes.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IMessageBoxService.Show(System.String,System.String,DevExpress.Mvvm.MessageButton,DevExpress.Mvvm.MessageIcon,DevExpress.Mvvm.MessageResult)">
      <summary>
        <para>Shows a message box with specified parameters.</para>
      </summary>
      <param name="messageBoxText">A text shown within the message box.</param>
      <param name="caption">A caption of the message box.</param>
      <param name="button">An object of the DevExpress.Mvvm.MessageButton type that is the set of buttons shown within the message box.</param>
      <param name="icon">An object of the DevExpress.Mvvm.MessageIcon type that is the icon shown within the message box.</param>
      <param name="defaultResult">An object of the DevExpress.Mvvm.MessageResult type that specifies which message box button is default. A default button is highlighted when a message box is shown.</param>
      <returns>An object of the DevExpress.Mvvm.MessageResult type that is the button the end-user clicked.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IMessenger">
      <summary>
        <para>Provides methods to send messages and register message handlers.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Register``1(System.Object,System.Object,System.Boolean,System.Action{``0})">
      <summary>
        <para>Registers a handler of a specific message type.</para>
      </summary>
      <param name="recipient">An object that will receive messages.</param>
      <param name="token">An object (marker) that can be used to identify a specific message. null if you want to process messages without tokens.</param>
      <param name="receiveInheritedMessages">true to receive messages of the TMessage type and all derived types; false to receive messages of the TMessage type only.</param>
      <param name="action">An action that will be invoked when the specified message occurs.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Send``1(``0,System.Type,System.Object)">
      <summary>
        <para>Sends the specified message.</para>
      </summary>
      <param name="message">The message of the TMessage type to be sent.</param>
      <param name="messageTargetType">Only recipients of the messageTargetType or inherited type will receive the current message. Set this parameter to null to send a message without an addressee type.</param>
      <param name="token">An object (marker) that can be used to identify a specific message. null if you want to send regular messages (without tokens).When sending a message, a token can be assigned to the message via the current token parameter. Only message recipients that registered the same token via the Register method are invoked when this message occurs.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Unregister(System.Object)">
      <summary>
        <para>Unsubscribes the specified object from receiving any messages.</para>
      </summary>
      <param name="recipient">An object to be unsubscribed from receiving any messages.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IMessenger.Unregister``1(System.Object,System.Object,System.Action{``0})">
      <summary>
        <para>Unsubscribes the specified object’s action from being invoked when a specific message occurs.</para>
      </summary>
      <param name="recipient">An object containing an action to be unsubscribed from receiving the messages of TMessage type.</param>
      <param name="token">An object (marker) that was used to identify a specific message when subscribing to it via the Register method. null to unsubscribe from the message regardless of the token assigned to it.</param>
      <param name="action">An action to be unsubscribed from receiving messages.</param>
    </member>
    <member name="T:DevExpress.Mvvm.INavigationService">
      <summary>
        <para>Provides members to navigate between Views.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.CanGoBack">
      <summary>
        <para>Specifies whether it is possible to perform a navigation to the previous view.</para>
      </summary>
      <value>true, if a navigation to the previous view is allowed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.Mvvm.INavigationService.CanGoBackChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.Mvvm.INavigationService.CanGoBack">INavigationService.CanGoBack</see> property value has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.CanGoForward">
      <summary>
        <para>Specifies whether it is possible to perform a navigation to the next view.</para>
      </summary>
      <value>true, if a navigation to the next view is allowed; otherwise, false.</value>
    </member>
    <member name="E:DevExpress.Mvvm.INavigationService.CanGoForwardChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.Mvvm.INavigationService.CanGoForward">INavigationService.CanGoForward</see> property value has been changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.CanNavigate">
      <summary>
        <para>Gets a value indicating whether a <see cref="T:DevExpress.Xpf.WindowsUI.NavigationFrame"/> associated with the current service can navigate.</para>
      </summary>
      <value>true, a <see cref="T:DevExpress.Xpf.WindowsUI.NavigationFrame"/> associated with the current service can navigate; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.ClearCache">
      <summary>
        <para>Clears the navigation cache.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.ClearNavigationHistory">
      <summary>
        <para>Clears the navigation history.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.INavigationService.Current">
      <summary>
        <para>Returns the current view model.</para>
      </summary>
      <value>A current view model.</value>
    </member>
    <member name="E:DevExpress.Mvvm.INavigationService.CurrentChanged">
      <summary>
        <para>Fires after the <see cref="P:DevExpress.Mvvm.INavigationService.Current">INavigationService.Current</see> property value has been changed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoBack">
      <summary>
        <para>Performs a navigation to the previous view, if allowed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoBack(System.Object)">
      <summary>
        <para>Navigates back to the previously selected screen (view).</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoForward">
      <summary>
        <para>Performs a navigation to the next view, if allowed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.GoForward(System.Object)">
      <summary>
        <para>Navigates forward to the next screen (view).</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.INavigationService.Navigate(System.String,System.Object,System.Object,System.Object,System.Boolean)">
      <summary>
        <para>Performs navigation to the target View.</para>
      </summary>
      <param name="viewType">A System.String value that specifies the name of the target View type.</param>
      <param name="viewModel">An object specifying the ViewModel of the target View.</param>
      <param name="param">A parameter for passing data to the target ViewModel.</param>
      <param name="parentViewModel">The parent ViewModel for building a parent-child view model relationship.</param>
      <param name="saveToJournal">true, to log the navigation; otherwise, false.</param>
    </member>
    <member name="T:DevExpress.Mvvm.INotificationService">
      <summary>
        <para>Provides methods to display notifications in Windows 8 style.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.INotificationService.CreateCustomNotification(System.Object)">
      <summary>
        <para>Creates and returns a custom notification with the specified View Model.</para>
      </summary>
      <param name="viewModel">An object specifying the notification’s View Model.</param>
      <returns>An DevExpress.Mvvm.INotification descendant with the specified View Model.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IOpenDialogServiceBase">
      <summary>
        <para>Provides members common to <see cref="T:DevExpress.Mvvm.IOpenFileDialogService"/> and <see cref="T:DevExpress.Mvvm.IOpenFolderDialogService"/></para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenDialogServiceBase.Multiselect">
      <summary>
        <para>Gets or sets whether a dialog box allows users to select multiple files.</para>
      </summary>
      <value>true if a dialog box allows users to select multiple files; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IOpenDialogServiceBase.ShowDialog(System.Action{System.ComponentModel.CancelEventArgs},System.String)">
      <summary>
        <para>Shows the dialog box.</para>
      </summary>
      <param name="fileOK">A System.Action object that allows you to cancel file selection.</param>
      <param name="directoryName">A System.String object that specifies the initial directory.</param>
      <returns>true, if the user clicks OK in the dialog box; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IOpenFileDialogService">
      <summary>
        <para>Provides methods to browse and open files in the File System by using the standard dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenFileDialogService.File">
      <summary>
        <para>Gets an object specifying the file selected in the dialog box.</para>
      </summary>
      <value>A FileInfo implementation that specifies the file selected in the dialog box.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IOpenFileDialogService.Files">
      <summary>
        <para>Gets a collection specifying all the files selected in the dialog box.</para>
      </summary>
      <value>A collection that specifies all the files selected in the dialog box.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISaveFileDialogService">
      <summary>
        <para>Provides methods to save the data of a ViewModel to a file by using the standard dialog box.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISaveFileDialogService.DefaultExt">
      <summary>
        <para>Gets or sets the default file extension.</para>
      </summary>
      <value>A System.String value specifying the default file extension. The default value is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ISaveFileDialogService.DefaultFileName">
      <summary>
        <para>Gets or sets the default file name.</para>
      </summary>
      <value>A System.String value specifying the default file name. The default value is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ISaveFileDialogService.File">
      <summary>
        <para>Gets an object specifying the file selected in the dialog box.</para>
      </summary>
      <value>A FileInfo implementation that specifies the file selected in the dialog box.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ISaveFileDialogService.ShowDialog(System.Action{System.ComponentModel.CancelEventArgs},System.String,System.String)">
      <summary>
        <para>Shows the dialog box.</para>
      </summary>
      <param name="fileOK">A System.Action object that allows you to cancel file selection.</param>
      <param name="directoryName">A System.String object that specifies the initial directory.</param>
      <param name="fileName">A System.String object that specifies the file name.</param>
      <returns>true, if the user clicks OK in the dialog box; otherwise, false.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.IServiceContainer">
      <summary>
        <para>Provides methods to retrieve services and register them in a service container.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.Clear">
      <summary>
        <para>Clears services stored in the current service container.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService``1(DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface.</para>
      </summary>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService``1(System.String,DevExpress.Mvvm.ServiceSearchMode,System.Boolean@)">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.GetService``1(System.String,DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface with a specified key.</para>
      </summary>
      <param name="key">A service identifier.</param>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.RegisterService(System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true, to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.RegisterService(System.String,System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="key">The service identifier.</param>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IServiceContainer.UnregisterService(System.Object)">
      <summary>
        <para>Unregisters the specified service.</para>
      </summary>
      <param name="service">The service to unregister.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ISplashScreenManagerService">
      <summary>
        <para>Provides data and methods used by the <see href="https://docs.devexpress.com/WPF/401692/mvvm-framework/services/predefined-set/splashscreenmanagerservice">SplashScreenManagerService</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenManagerService.Close">
      <summary>
        <para>Hides the splash screen.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenManagerService.Show(System.String,System.Int32)">
      <summary>
        <para>Displays the splash screen.</para>
      </summary>
      <param name="documentType">The string value that identifies the document type.</param>
      <param name="timeout">A time interval for which the splash screen initialization process is prioritized over the main application, in milliseconds.</param>
    </member>
    <member name="P:DevExpress.Mvvm.ISplashScreenManagerService.State">
      <summary>
        <para>Specifies the current state of the splash screen.</para>
      </summary>
      <value>A DevExpress.Mvvm.SplashScreenState enumeration value.</value>
    </member>
    <member name="E:DevExpress.Mvvm.ISplashScreenManagerService.StateChanged">
      <summary>
        <para>Occurs when the <see cref="P:DevExpress.Mvvm.ISplashScreenManagerService.State">State</see> property value is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISplashScreenManagerService.ViewModel">
      <summary>
        <para>Provides access to the view model that stores the splash screen data and options.</para>
      </summary>
      <value>A DevExpress.Mvvm.DXSplashScreenViewModel object.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISplashScreenService">
      <summary>
        <para>Provides methods to display splash screens.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.HideSplashScreen">
      <summary>
        <para>Hides the splash screen.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISplashScreenService.IsSplashScreenActive">
      <summary>
        <para>Gets whether the splash screen is currently shown.</para>
      </summary>
      <value>true, if the splash screen is currently shown; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.SetSplashScreenProgress(System.Double,System.Double)">
      <summary>
        <para>Specifies the current progress of the splash screen.</para>
      </summary>
      <param name="progress">The current progress value.</param>
      <param name="maxProgress">The maximum progress value.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.SetSplashScreenState(System.Object)">
      <summary>
        <para>Specifies a data object to be passed to the splash screen service.</para>
      </summary>
      <param name="state">A data object to be passed to the splash screen service.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ISplashScreenService.ShowSplashScreen(System.String)">
      <summary>
        <para>Shows the splash screen.</para>
      </summary>
      <param name="documentType">A <see cref="T:System.String"/> value that specifies the name of a document type to be shown in the splash screen.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportNavigation">
      <summary>
        <para>Provides members that occur when navigating to an object and when navigating away from it.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportNavigation.OnNavigatedFrom">
      <summary>
        <para>This method is called when a navigation from the current view model is performed.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ISupportNavigation.OnNavigatedTo">
      <summary>
        <para>This method is called when navigation to the current view model is performed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportParameter">
      <summary>
        <para>Provides a member that allows data to be passed from a Main to a Detail ViewModel, when these ViewModels are loosely coupled.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportParameter.Parameter">
      <summary>
        <para>Specifies a parameter for passing data between view models.</para>
      </summary>
      <value>A parameter to be passed.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportParentViewModel">
      <summary>
        <para>Provides a member that allows a Main ViewModel to be passed to a Detail ViewModel. In addition, this allows the Main ViewModel’s services to be used within the Detail ViewModel.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportParentViewModel.ParentViewModel">
      <summary>
        <para>Specifies the parent View model for building a parent-child view model relationship.</para>
      </summary>
      <value>A parent view model.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ISupportServices">
      <summary>
        <para>Provides a member to access an object containing services.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportServices.ServiceContainer">
      <summary>
        <para>Returns an object implementing the <see cref="T:DevExpress.Mvvm.IServiceContainer"/> interface which is used to access services.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Mvvm.IServiceContainer"/> interface.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportWizardFinishCommand.CanFinish">
      <summary>
        <para>Indicates whether or not the Finish button is enabled on a wizard page.</para>
      </summary>
      <value>true, if the Finish button is enabled; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ISupportWizardNextCommand.CanGoForward">
      <summary>
        <para>Indicates whether or not the Next button is enabled on a wizard page.</para>
      </summary>
      <value>true, if the Next button is enabled; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ITaskbarButtonService">
      <summary>
        <para>Provides methods to display customizable application taskbar buttons.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.Description">
      <summary>
        <para>Gets or sets the text to display for the task-bar thumbnail tool-tip.</para>
      </summary>
      <value>A System.String value specifying the text to display for the thumbnail tool-tip.The default is an empty string.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.OverlayIcon">
      <summary>
        <para>Gets or sets the icon that is displayed over the taskbar button.</para>
      </summary>
      <value>The icon that is displayed over the taskbar button.The default is null.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ProgressState">
      <summary>
        <para>Gets or sets the taskbar button’s progress state.</para>
      </summary>
      <value>A System.Windows.Shell.TaskbarItemProgressState enumerator value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ProgressValue">
      <summary>
        <para>Gets or sets the taskbar button’s progress. This is a dependency property.</para>
      </summary>
      <value>A System.Double value specifing the taskbar button’s progress</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ThumbButtonInfos">
      <summary>
        <para>Gets or sets the TaskbarThumbButtonInfo collection.</para>
      </summary>
      <value>The TaskbarThumbButtonInfo collection that represents the taskbar thumbnail’s Thumb buttons.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ThumbnailClipMargin">
      <summary>
        <para>Gets or sets the thumbnail clipping margins</para>
      </summary>
      <value>A System.Windows.Thickness value.The default is 0.</value>
    </member>
    <member name="P:DevExpress.Mvvm.ITaskbarButtonService.ThumbnailClipMarginCallback">
      <summary>
        <para>Gets or sets the ThumbnailClipMargin property callback.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ITaskbarButtonService.UpdateThumbnailClipMargin">
      <summary>
        <para>Updates the thumbnail clipping margin.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.IUIObjectService">
      <summary>
        <para>Contains members that allow you to access a UI element from View Model code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IUIObjectService.Object">
      <summary>
        <para>Gets the UI element to which the service is attached.</para>
      </summary>
      <value>The UI element.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IViewInjectionManager">
      <summary>
        <para>Provides methods to inject and manipulate under ViewModels (and their Views)</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.Navigate(System.String,System.Object)">
      <summary>
        <para>Navigates to the specified ViewModel (and its View) inside the corresponding region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the key of the ViewModel (and its View).</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterNavigatedAwayEventHandler(System.Object,System.Action)">
      <summary>
        <para>Registers the NavigateAway event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterNavigatedEventHandler(System.Object,System.Action)">
      <summary>
        <para>Registers the Navigated event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.RegisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Register the service. For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.Remove(System.String,System.Object)">
      <summary>
        <para>Removes the ViewModel (and its View) from the specified region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterNavigatedAwayEventHandler(System.Object,System.Action)">
      <summary>
        <para>Unregisters the NavigateAway event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterNavigatedEventHandler(System.Object,System.Action)">
      <summary>
        <para>Unregisters the Navigated event handler for the specified ViewModel.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <param name="eventHandler">A System.Action object encapsulating the event handler.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionManager.UnregisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Unregister the service. For internal use.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.IViewInjectionService">
      <summary>
        <para>Provides methods and properties to inject VewModels (and their Views) and operate with them.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionService.GetKey(System.Object)">
      <summary>
        <para>Returns the key of an injected ViewModel (and its View).</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <returns>An object that specifies the key of the injected ViewModel (and its View).</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IViewInjectionService.RegionName">
      <summary>
        <para>Gets or sets the region name.</para>
      </summary>
      <value>A System.String that specifies the region name.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IViewInjectionService.Remove(System.Object)">
      <summary>
        <para>Removes the specified ViewModel (and its View) from the collection of the injected items.</para>
      </summary>
      <param name="viewModel">An object representing the ViewModel.</param>
      <returns>true if the item is successfully removed; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.IViewInjectionService.SelectedViewModel">
      <summary>
        <para>Gets or sets the selected ViewModel.</para>
      </summary>
      <value>An object representing the currently selected ViewModel.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IViewInjectionService.ViewModels">
      <summary>
        <para>Gets the collection of injected View Models (and their Views).</para>
      </summary>
      <value>A collection of injected View Models (and their Views).</value>
    </member>
    <member name="T:DevExpress.Mvvm.IWindowService">
      <summary>
        <para>Provides methods to display a view as a window and control it from the ViewModel.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Activate">
      <summary>
        <para>Brings the service’s window to the front and activates it.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Close">
      <summary>
        <para>Closes the service window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Hide">
      <summary>
        <para>Hides the service’s window.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IWindowService.IsWindowAlive">
      <summary>
        <para>Gets whether the service’s window is created and alive.</para>
      </summary>
      <value>true, if the service’s window is created and alive; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Restore">
      <summary>
        <para>Restores the service’s window.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.IWindowService.Show(System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Shows a window with specified View and ViewModel.</para>
      </summary>
      <param name="documentType">A <see cref="T:System.String">String</see> value that specifies the type of the view to be created by the <see href="https://docs.devexpress.com/WPF/401015/mvvm-framework/services/predefined-set/windowservice">WindowService</see>.</param>
      <param name="viewModel">An object that represents the ViewModel.</param>
      <param name="parameter">An object that represents the paramenter passed to the ViewModel</param>
      <param name="parentViewModel">An object that represents the parent ViewModel.</param>
    </member>
    <member name="P:DevExpress.Mvvm.IWindowService.Title">
      <summary>
        <para>Gets or sets the service window’s title.</para>
      </summary>
      <value>The service window’s title.</value>
    </member>
    <member name="P:DevExpress.Mvvm.IWindowService.WindowState">
      <summary>
        <para>Gets or sets a window state (restored, minimized, maximized).</para>
      </summary>
      <value>A window state.</value>
    </member>
    <member name="T:DevExpress.Mvvm.IWizardService">
      <summary>
        <para>Provides methods to use the <see cref="T:DevExpress.Xpf.Controls.Wizard"/> control in compliance with the MVVM pattern.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.IWizardService.Current">
      <summary>
        <para>Returns the currently displayed object.</para>
      </summary>
      <value>A System.Object value representing the currently displayed object.</value>
    </member>
    <member name="M:DevExpress.Mvvm.IWizardService.GoBack(System.Object)">
      <summary>
        <para>Navigates to the previous wizard page.</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IWizardService.GoForward(System.Object)">
      <summary>
        <para>Navigates to the next wizard page.</para>
      </summary>
      <param name="param">An object that specifies the navigation parameter for the current navigation.</param>
    </member>
    <member name="M:DevExpress.Mvvm.IWizardService.Navigate(System.String,System.Object,System.Object,System.Object)">
      <summary>
        <para>Performs navigation to the target View.</para>
      </summary>
      <param name="viewType">A System.String value that specifies the name of the target View type.</param>
      <param name="viewModel">An object specifying the ViewModel of the target View.</param>
      <param name="param">A parameter for passing data to the target ViewModel.</param>
    </member>
    <member name="T:DevExpress.Mvvm.MessageButton">
      <summary>
        <para>Lists buttons displayed in a message box.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageButton.OK">
      <summary>
        <para>The message box displays the OK button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageButton.OKCancel">
      <summary>
        <para>The message box displays OK and Cancel buttons.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageButton.YesNo">
      <summary>
        <para>The message box displays Yes and No buttons.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageButton.YesNoCancel">
      <summary>
        <para>The message box displays Yes, No, and Cancel buttons.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.MessageResult">
      <summary>
        <para>Lists message box results.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageResult.Cancel">
      <summary>
        <para>The message box result is Cancel.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageResult.No">
      <summary>
        <para>The message box result is No.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageResult.None">
      <summary>
        <para>The message box is closed without a result.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageResult.OK">
      <summary>
        <para>The message box result is OK.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.MessageResult.Yes">
      <summary>
        <para>The message box result is Yes.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Messenger">
      <summary>
        <para>Allows you to send messages and register handlers that will process these messages.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.#ctor">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.Mvvm.Messenger"/> instance.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.#ctor(System.Boolean,DevExpress.Mvvm.ActionReferenceType)">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.Mvvm.Messenger"/> instance.</para>
      </summary>
      <param name="isMultiThreadSafe">true, if the messenger can be used from multiple threads; otherwise, false.</param>
      <param name="actionReferenceType">An ActionReferenceType enumeration value.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.#ctor(System.Boolean,DevExpress.Mvvm.Native.IActionInvokerFactory)">
      <summary>
        <para>Initializes a <see cref="T:DevExpress.Mvvm.Messenger"/> instance.</para>
      </summary>
      <param name="isMultiThreadSafe">true, if the messenger can be used from multiple threads; otherwise, false.</param>
      <param name="actionInvokerFactory">An object implementing the IActionInvokerFactory interface.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Cleanup">
      <summary>
        <para>Removes all references to the actions that belong to non-existing recipients.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Messenger.Default">
      <summary>
        <para>Specifies the default messenger.</para>
      </summary>
      <value>An object implementing the <see cref="T:DevExpress.Mvvm.IMessenger"/> interface.</value>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Register``1(System.Object,System.Object,System.Boolean,System.Action{``0})">
      <summary>
        <para>Registers a handler of a specific message type.</para>
      </summary>
      <param name="recipient">An object that will receive messages.</param>
      <param name="token">An object (marker) that can be used to identify a specific message. null if you want to process messages without tokens.</param>
      <param name="receiveInheritedMessages">true to receive messages of the TMessage type and all derived types; false to receive messages of the TMessage type only.</param>
      <param name="action">An action that will be invoked when the specified message occurs.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.RequestCleanup">
      <summary>
        <para>Executes the <see cref="M:DevExpress.Mvvm.Messenger.Cleanup">Messenger.Cleanup</see> method asynchronously when the current application is idle.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Send``1(``0,System.Type,System.Object)">
      <summary>
        <para>Sends the specified message.</para>
      </summary>
      <param name="message">The message to send.</param>
      <param name="messageTargetType">The message target type.</param>
      <param name="token">An object that separates messages.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Unregister(System.Object)">
      <summary>
        <para>Unregisters the specified object from all registered message handlers.</para>
      </summary>
      <param name="recipient">An object to unregister.</param>
    </member>
    <member name="M:DevExpress.Mvvm.Messenger.Unregister``1(System.Object,System.Object,System.Action{``0})">
      <summary>
        <para>Unsubscribes the specified object’s action from being invoked when a specific message occurs.</para>
      </summary>
      <param name="recipient">An object containing an action to be unsubscribed from receiving the messages of TMessage type.</param>
      <param name="token">An object (marker) that was used to identify a specific message when subscribing to it via the Register method. null to unsubscribe from the message regardless of the token assigned to it.</param>
      <param name="action">An action to be unsubscribed from receiving messages.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModule">
      <summary>
        <para>A structure that binds a ViewModel to its View.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModuleManager">
      <summary>
        <para>Provides methods to control modules and regions.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IModuleManagerBase">
      <summary>
        <para>Provides methods to control modules and regions.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.IRegion">
      <summary>
        <para>Provides control of injected ViewModels.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.Module">
      <summary>
        <para>A structure that binds a ViewModel to its View.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ModuleInjection.ModuleManager">
      <summary>
        <para>Provides methods to control modules and regions.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManager.#ctor(DevExpress.Mvvm.IViewModelLocator,DevExpress.Mvvm.UI.IViewLocator,DevExpress.Mvvm.IStateSerializer,System.Boolean,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ModuleInjection.ModuleManager"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManager.#ctor(System.Boolean,System.Boolean,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ModuleInjection.ModuleManager"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ModuleInjection.ModuleManager.#ctor(System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ModuleInjection.ModuleManager"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.NavigationViewModelBase">
      <summary>
        <para>A View Model for Views between which navigation can be organized if they are placed within a <see cref="T:DevExpress.Xpf.WindowsUI.NavigationFrame"/>.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ServiceContainer">
      <summary>
        <para>An object that contains services.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ServiceContainer"/> class with the specified owner.</para>
      </summary>
      <param name="owner">An object that will be the owner of the created service container.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.Clear">
      <summary>
        <para>Clears services stored in the current service container.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ServiceContainer.Default">
      <summary>
        <para>Gets the default service contaner.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Mvvm.IServiceContainer"/> implementation.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.GetService``1(DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface.</para>
      </summary>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.GetService``1(System.String,DevExpress.Mvvm.ServiceSearchMode)">
      <summary>
        <para>Performs a search for a service implementing the specified service interface with a specified key.</para>
      </summary>
      <param name="key">A service identifier.</param>
      <param name="searchMode">A <see cref="T:DevExpress.Mvvm.ServiceSearchMode"/> enumeration value.</param>
      <returns>An object implementing the specified service interface.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.RegisterService(System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true, to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.RegisterService(System.String,System.Object,System.Boolean)">
      <summary>
        <para>Registers the specified service.</para>
      </summary>
      <param name="key">The service identifier.</param>
      <param name="service">The service to register.</param>
      <param name="yieldToParent">true, to allow access to the passed service from the parent service container; otherwise, false.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ServiceContainer.UnregisterService(System.Object)">
      <summary>
        <para>Unregisters the specified service.</para>
      </summary>
      <param name="service">The service to unregister.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ServiceSearchMode">
      <summary>
        <para>Lists values that specify whether a search for a service must be carried out within the current service container, or within the current and parent service containers.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ServiceSearchMode.LocalOnly">
      <summary>
        <para>A search for a service is carried out within the current service container.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ServiceSearchMode.PreferLocal">
      <summary>
        <para>A search for a service is carried out within both the current and parent service containers. A service found in the current container has a higher priority.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.ServiceSearchMode.PreferParents">
      <summary>
        <para>A search for a service is carried out within both the current and parent service containers. A service found in parent containers has a higher priority.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.SplashScreenState">
      <summary>
        <para>Lists values that specify the current state of the splash screen window.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Closed">
      <summary>
        <para>The splash screen is closed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Closing">
      <summary>
        <para>The splash screen is about to be closed (the Closing event fired).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Showing">
      <summary>
        <para>The splash screen is about to be shown (the Show method is called).</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.SplashScreenState.Shown">
      <summary>
        <para>The splash screen is shown (the Loaded event fired).</para>
      </summary>
    </member>
    <member name="N:DevExpress.Mvvm.UI">
      <summary>
        <para>Contains controls for building a UI using the MVVM design pattern.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.UI.IViewLocator">
      <summary>
        <para>Provides a method to retrieve a View by its type name.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UI.IViewLocator.ResolveView(System.String)">
      <summary>
        <para>Returns a View based on its short type name.</para>
      </summary>
      <param name="name">The short type name of the View to be returned.</param>
      <returns>A View of the specified type.</returns>
    </member>
    <member name="M:DevExpress.Mvvm.UI.IViewLocator.ResolveViewType(System.String)">
      <summary>
        <para>Returns a view type based on its short type name.</para>
      </summary>
      <param name="name">The short type name of the view which type is to be returned.</param>
      <returns>The type of the view.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.UICommand">
      <summary>
        <para>A ViewModel that generates a dialog button.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with the default settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor(System.Object,System.Object,DevExpress.Mvvm.ICommand{System.ComponentModel.CancelEventArgs},System.Boolean,System.Boolean,System.Object,System.Boolean,System.Object,DevExpress.Mvvm.DialogButtonAlignment,DevExpress.Mvvm.AsyncDisplayMode,System.Object,DevExpress.Mvvm.GlyphAlignment)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor(System.Object,System.Object,DevExpress.Mvvm.ICommand{System.ComponentModel.CancelEventArgs})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor(System.Object,System.Object,System.Windows.Input.ICommand,System.Boolean,System.Boolean,System.Object,System.Boolean,System.Object,DevExpress.Mvvm.DialogButtonAlignment,DevExpress.Mvvm.AsyncDisplayMode,System.Object,DevExpress.Mvvm.GlyphAlignment)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.#ctor(System.Object,System.Object,System.Windows.Input.ICommand)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.UICommand"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.ActualAlignment">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Alignment">
      <summary>
        <para>Gets or sets the dialog button’s horizontal alignment.</para>
      </summary>
      <value>Specifies the dialog button alignment.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.AllowCloseWindow">
      <summary>
        <para>Gets or sets a value that indicates whether the dialog button is the close button.</para>
      </summary>
      <value>true, if the button closes the current window; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.AsyncDisplayMode">
      <summary>
        <para>Gets or sets how the dialog button indicates executed asynchronous operations.</para>
      </summary>
      <value>The value that specifies how the dialog button indicates executed asynchronous operations.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Caption">
      <summary>
        <para>Gets or sets the text displayed within the dialog button.</para>
      </summary>
      <value>A string which specifies the text displayed within the dialog button.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Command">
      <summary>
        <para>Gets or sets the command to invoke when the dialog button is clicked.</para>
      </summary>
      <value>A command to invoke when the dialog button is clicked.</value>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.GenerateFromMessageBoxButton(System.Windows.MessageBoxButton,DevExpress.Mvvm.IMessageBoxButtonLocalizer,System.Nullable{System.Windows.MessageBoxResult},System.Nullable{System.Windows.MessageBoxResult})">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.GenerateFromMessageBoxButton(System.Windows.MessageBoxButton,DevExpress.Mvvm.IMessageButtonLocalizer,System.Nullable{System.Windows.MessageBoxResult},System.Nullable{System.Windows.MessageBoxResult})">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.UICommand.GenerateFromMessageButton(DevExpress.Mvvm.MessageButton,DevExpress.Mvvm.IMessageButtonLocalizer,System.Nullable{DevExpress.Mvvm.MessageResult},System.Nullable{DevExpress.Mvvm.MessageResult})">
      <summary>
        <para>For internal use.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Glyph">
      <summary>
        <para>Gets or sets the image displayed in the dialog button.</para>
      </summary>
      <value>The image displayed in the dialog button.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.GlyphAlignment">
      <summary>
        <para>Gets or sets the dialog button’s image alignment.</para>
      </summary>
      <value>The dialog button’s image alignment.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Id">
      <summary>
        <para>Gets or sets the identifier of the dialog button.</para>
      </summary>
      <value>An object that specifies the identifier the dialog button.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.IsCancel">
      <summary>
        <para>Gets or sets a value that indicates whether the dialog button is the cancel button.</para>
      </summary>
      <value>true, if the dialog button is the cancel button; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.IsDefault">
      <summary>
        <para>Gets or sets a value that indicates whether the dialog button is the default button.</para>
      </summary>
      <value>true, if the dialog button is the default button; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Placement">
      <summary>
        <para>Gets or sets the dialog button alignment.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.UICommand.Tag">
      <summary>
        <para>Gets or sets the data associated with the dialog button.</para>
      </summary>
      <value>An object that contains information associated with the dialog button.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ViewInjectionManager">
      <summary>
        <para>Allows you to perform injecting and manipulation under ViewModels (and their Views) in any section of the application’s code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ViewInjectionManager.Default">
      <summary>
        <para>Gets the default view injection manager.</para>
      </summary>
      <value>An <see cref="T:DevExpress.Mvvm.IViewInjectionManager"/> implementation.</value>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.Navigate(System.String,System.Object)">
      <summary>
        <para>Navigates to the specified ViewModel (and its View) inside the corresponding region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the key of the ViewModel (and its View).</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.RegisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Register the service. For internal use.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.Remove(System.String,System.Object)">
      <summary>
        <para>Removes the ViewModel (and its View) from the specified region.</para>
      </summary>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManager.UnregisterService(DevExpress.Mvvm.IViewInjectionService)">
      <summary>
        <para>Unregister the service. For internal use.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.ViewInjectionManagerExtensions">
      <summary>
        <para>Provides extension methods for the <see cref="T:DevExpress.Mvvm.ViewInjectionManager"/> to integrate and control ViewModels (with their Views).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManagerExtensions.Inject(DevExpress.Mvvm.IViewInjectionManager,System.String,System.Object,System.Func{System.Object},System.String)">
      <summary>
        <para>Static extension method that injects a ViewModel (and its View) with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
      <param name="viewModelFactory">A object encapsulating the method that returns the ViewModel.</param>
      <param name="viewName">A System.String value specifying the view that will be created using the <see cref="T:DevExpress.Mvvm.UI.ViewLocator"/>.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManagerExtensions.Inject(DevExpress.Mvvm.IViewInjectionManager,System.String,System.Object,System.Func{System.Object},System.Type)">
      <summary>
        <para>Static extension method that injects a ViewModel (and its View) with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
      <param name="viewModelFactory">A object encapsulating the method that returns the ViewModel.</param>
      <param name="viewType">A System.String value specifying the view that will be created using the <see cref="T:DevExpress.Mvvm.UI.ViewLocator"/>.</param>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionManagerExtensions.Inject(DevExpress.Mvvm.IViewInjectionManager,System.String,System.Object,System.Func{System.Object})">
      <summary>
        <para>Injects a ViewModel (and its View) with the specified parameters.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="regionName">A System.String value specifying the region name.</param>
      <param name="key">An object specifying the identifier (key) of the View and its ViewModel.</param>
      <param name="viewModelFactory">A object encapsulating the method that returns the ViewModel.</param>
    </member>
    <member name="T:DevExpress.Mvvm.ViewInjectionServiceExtensions">
      <summary>
        <para>Provides extension methods for the <see cref="T:DevExpress.Mvvm.UI.ViewInjectionService"/> to integrate and control ViewModels (with their Views).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewInjectionServiceExtensions.GetViewModel(DevExpress.Mvvm.IViewInjectionService,System.Object)">
      <summary>
        <para>Static extension method that finds and returns a ViewModel with the specified key.</para>
      </summary>
      <param name="service">The type the static extension method operates with.</param>
      <param name="key">An object representing the identifier (key) of the View and its ViewModel.</param>
      <returns>An object representing the ViewModel.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.ViewModelBase">
      <summary>
        <para>The base class for ViewModels.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewModelBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ViewModelBase"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.ViewModelBase.IsInDesignMode">
      <summary>
        <para>Gets whether design-time mode is active.</para>
      </summary>
      <value>true, if design-time mode is active; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.ViewModelClosingEventArgs">
      <summary>
        <para>Provides data for the ViewModelClosingEvent event.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.ViewModelClosingEventArgs.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.ViewModelClosingEventArgs"/> class.</para>
      </summary>
      <param name="viewModel">An object specifying the View Model that is about to be closed.</param>
    </member>
    <member name="P:DevExpress.Mvvm.ViewModelClosingEventArgs.ViewModel">
      <summary>
        <para>Gets or sets the View Model.</para>
      </summary>
      <value>An object specifying the View Model.</value>
    </member>
    <member name="N:DevExpress.Mvvm.Xpf">
      <summary>
        <para>Contains classes that allow you to work with virtual sources in an MVVM pattern.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.CellMergeArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.CellMergeCommand">TableView.CellMergeCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.CellMergeArgs.#ctor(System.String,System.Object,System.Object,System.Object,System.Object,System.Int32,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.CellMergeArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FieldName">
      <summary>
        <para>Gets the column’s field name.</para>
      </summary>
      <value>The column’s field name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstCellValue">
      <summary>
        <para>Gets the first compared value.</para>
      </summary>
      <value>The first compared value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstItem">
      <summary>
        <para>Gets a data source record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstCellValue"/>.</para>
      </summary>
      <value>A data source record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstCellValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstSourceIndex">
      <summary>
        <para>Gets a data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstCellValue"/>.</para>
      </summary>
      <value>A data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.FirstCellValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.Merge">
      <summary>
        <para>Gets or sets whether to merge processed cells.</para>
      </summary>
      <value>true to merge processed cells; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondCellValue">
      <summary>
        <para>Gets the second compared value.</para>
      </summary>
      <value>The second compared value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondItem">
      <summary>
        <para>Gets a data source record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondCellValue"/>.</para>
      </summary>
      <value>A data source record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondCellValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondSourceIndex">
      <summary>
        <para>Gets a data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondCellValue"/>.</para>
      </summary>
      <value>A data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.CellMergeArgs.SecondCellValue"/>.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.CellValueChangedArgs">
      <summary>
        <para>Contains data for the command bound to the CellValueChangedCommand or CellValueChangingCommand property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.CellValueChangedArgs.#ctor(System.Object,System.String,System.Object,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.CellValueChangedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellValueChangedArgs.OldValue">
      <summary>
        <para>Gets an old cell value.</para>
      </summary>
      <value>An old cell value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.CellValueChangedInEditFormArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.CellValueChangingCommand">GridViewBase.CellValueChangingCommand</see> or <see cref="P:DevExpress.Xpf.Grid.TreeListView.CellValueChangingCommand">TreeListView.CellValueChangingCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.CellValueChangedInEditFormArgs.#ctor(System.Object,System.String,System.Object,System.Object,DevExpress.Mvvm.CellEditorData[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.CellValueChangedInEditFormArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CellValueChangedInEditFormArgs.CellEditors">
      <summary>
        <para>Gets an array of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> objects that allow you to specify settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</para>
      </summary>
      <value>An array of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> objects that allow you to specify settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ClickArgs">
      <summary>
        <para>Contains data for commands executed when a user double-clicks a row within the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ClickArgs.#ctor(System.Object,DevExpress.Mvvm.Xpf.MouseButton)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.ClickArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ClickArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ClickArgs.MouseButton">
      <summary>
        <para>Gets a mouse button that a user clicks.</para>
      </summary>
      <value>A mouse button that a user clicks.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ColumnDisplayTextArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomColumnDisplayTextCommand">GridControl.CustomColumnDisplayTextCommand</see> and <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomColumnDisplayTextCommand">TreeListView.CustomColumnDisplayTextCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ColumnDisplayTextArgs.#ctor(System.Object,System.String,System.Int32,System.Object,System.String,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.ColumnDisplayTextArgs"/> class with specified settings.</para>
      </summary>
      <param name="item">A record in a data source.</param>
      <param name="fieldName">The current field name.</param>
      <param name="sourceIndex">The index of the processed record in a data source.</param>
      <param name="value">The processed cell’s value.</param>
      <param name="displayText">The text displayed in the processed cell.</param>
      <param name="showAsNullText">Specifies whether text corresponding to a null value appears faded.</param>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ColumnDisplayTextArgs.ShowAsNullText">
      <summary>
        <para>Specifies whether text corresponding to a null value appears faded.</para>
      </summary>
      <value>true to fade the null text; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ColumnDisplayTextArgs.SourceIndex">
      <summary>
        <para>Gets the index of a record in a data source that corresponds to the processed data row.</para>
      </summary>
      <value>The index of the processed record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs">
      <summary>
        <para>Contains data for the <see cref="E:DevExpress.Xpf.Grid.DialogEditFormBehavior.CreateEditItemViewModel"/> event and the command bound to the <see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.CreateEditItemViewModelCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs.GetViewModelAsync">
      <summary>
        <para>Gets or sets the task that returns the edit operation’s view model. The view model includes an item that you want to edit or add to the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>, the data context that describes the edit operation, and the dialog edit form‘s title.</para>
      </summary>
      <value>The task that returns the edit operation’s view model.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs.IsNewItem">
      <summary>
        <para>Gets whether a user invokes the dialog edit form to add a new item to the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>true if a user invokes the dialog edit form to add a new item to the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>; false if a user invokes the dialog edit form to update an existing item.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs.Key">
      <summary>
        <para>Gets the value of the field assigned to the <see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.KeyProperty">DialogEditFormBehavior.KeyProperty</see> property.</para>
      </summary>
      <value>The value of the field assigned to the <see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.KeyProperty">DialogEditFormBehavior.KeyProperty</see> property.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.CreateEditItemViewModelArgs.ViewModel">
      <summary>
        <para>Gets or sets the view model used in the edit operation. The view model includes an item that you want to edit or add to the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>, the data context that describes the edit operation, and the dialog edit form‘s title.</para>
      </summary>
      <value>The view model used in the edit operation.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.DataSourceRefreshArgs">
      <summary>
        <para>Populates commands bound to the <see cref="E:DevExpress.Xpf.Grid.DataViewBase.DataSourceRefresh">DataViewBase.DataSourceRefresh</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.DataSourceRefreshArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.DataSourceRefreshArgs"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DataSourceRefreshArgs.RefreshAsync">
      <summary>
        <para>Gets or sets a task that implements custom refresh logic.</para>
      </summary>
      <value>A task that implements custom refresh logic.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.DeleteValidationArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.ValidateRowDeletionCommand">DataViewBase.DataSourceRefresh</see> and <see cref="P:DevExpress.Xpf.Grid.TreeListView.ValidateNodeDeletionCommand"/> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.DeleteValidationArgs.#ctor(System.Object[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.DeleteValidationArgs"/> class with specified settings.</para>
      </summary>
      <param name="items">An array of rows that a user wants to delete.</param>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DeleteValidationArgs.DisplayErrorMode">
      <summary>
        <para>Gets whether to display errors in the message box.</para>
      </summary>
      <value>A mode that specifies whether to display errors.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DeleteValidationArgs.Items">
      <summary>
        <para>Gets an array of rows that a user wants to delete.</para>
      </summary>
      <value>An array of rows that a user wants to delete.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DeleteValidationArgs.Result">
      <summary>
        <para>Gets or sets information about the error (its type and description).</para>
      </summary>
      <value>Information about the error (its type and description).</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DeleteValidationArgs.ResultAsync">
      <summary>
        <para>Gets or sets a task that asynchronously deletes rows from the underlying data source (database).</para>
      </summary>
      <value>A task that asynchronously deletes rows from the underlying data source (database).</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.DisplayDeleteOperationError">
      <summary>
        <para>Lists the values that specify the <see cref="P:DevExpress.Xpf.Grid.GridValidateRowDeletionEventArgs.DisplayErrorMode">GridValidateRowDeletionEventArgs.DisplayErrorMode</see> property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.DisplayDeleteOperationError.Disabled">
      <summary>
        <para>An error is not displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.DisplayDeleteOperationError.ShowMessageBox">
      <summary>
        <para>An error is displayed in the message box.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.DisplayTextArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomColumnDisplayTextCommand">GridControl.CustomColumnDisplayTextCommand</see>, <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomColumnDisplayTextCommand">TreeListView.CustomColumnDisplayTextCommand</see>, and <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomGroupDisplayTextCommand">GridControl.CustomGroupDisplayTextCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.DisplayTextArgs.#ctor(System.Object,System.String,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.DisplayTextArgs"/> class with specified settings.</para>
      </summary>
      <param name="item">A record in a data source.</param>
      <param name="fieldName">The current field name.</param>
      <param name="value">The processed cell’s value.</param>
      <param name="displayText">The text displayed in the processed cell.</param>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DisplayTextArgs.DisplayText">
      <summary>
        <para>Gets or sets the text displayed in the processed cell.</para>
      </summary>
      <value>The text displayed in the processed cell.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DisplayTextArgs.FieldName">
      <summary>
        <para>Gets the current field name.</para>
      </summary>
      <value>The current field name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DisplayTextArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.DisplayTextArgs.Value">
      <summary>
        <para>Gets the processed cell’s value.</para>
      </summary>
      <value>The processed cell’s value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.EditArgs">
      <summary>
        <para>Contains data for the edit commands.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.EditArgs.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.EditArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs">
      <summary>
        <para>Contains data for the <see cref="E:DevExpress.Xpf.Grid.DialogEditFormBehavior.ValidateRow"/> event and the command bound to the <see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.ValidateRowCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs.#ctor(System.Object,System.Boolean,System.Object,System.Nullable{System.Threading.CancellationToken})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs.CancellationToken">
      <summary>
        <para>Gets a token that allows you to respond to a task cancellation request invoked by the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>A cancellation token.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs.EditOperationContext">
      <summary>
        <para>Gets the edit operation’s data context.</para>
      </summary>
      <value>The edit operation’s data context.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs.IsNewItem">
      <summary>
        <para>Gets whether the dialog edit form was invoked to add a new item to the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>true if the dialog edit form was invoked to add a new item to the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>; false if the dialog edit form was invoked to update an existing item.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs.Item">
      <summary>
        <para>Gets the processed data record.</para>
      </summary>
      <value>The processed data record.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormRowValidationArgs.ValidateAsync">
      <summary>
        <para>Gets or sets the task that validates changes and posts them to the data source.</para>
      </summary>
      <value>The task that validates changes and posts them to the data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.EditFormValidateRowDeletionArgs">
      <summary>
        <para>Contains data for the <see cref="E:DevExpress.Xpf.Grid.DialogEditFormBehavior.ValidateRowDeletion"/> event and the command bound to the <see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.ValidateRowDeletionCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.EditFormValidateRowDeletionArgs.#ctor(System.Object[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.EditFormValidateRowDeletionArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormValidateRowDeletionArgs.Keys">
      <summary>
        <para>Gets <see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.KeyProperty"/> values for deleted rows.</para>
      </summary>
      <value><see cref="P:DevExpress.Xpf.Grid.DialogEditFormBehavior.KeyProperty"/> values for deleted rows.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditFormValidateRowDeletionArgs.ValidateAsync">
      <summary>
        <para>Gets or sets the task that validates the delete row operation.</para>
      </summary>
      <value>The task that validates the delete row operation.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.EditItemViewModel">
      <summary>
        <para>A View Model for the edit operation in the <see cref="T:DevExpress.Xpf.Grid.DialogEditFormBehavior"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.EditItemViewModel.#ctor(System.Object,System.Object,System.Action,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.EditItemViewModel"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.EditItemViewModel.Dispose">
      <summary>
        <para>Disposes the View Model.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditItemViewModel.EditOperationContext">
      <summary>
        <para>Gets the data context that describes the edit operation.</para>
      </summary>
      <value>The data context that describes the edit operation.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditItemViewModel.Item">
      <summary>
        <para>Gets an item that you want to edit or add to the control.</para>
      </summary>
      <value>An item that you want to edit or add to the control.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.EditItemViewModel.Title">
      <summary>
        <para>Gets the dialog edit form‘s title.</para>
      </summary>
      <value>The dialog edit form‘s title.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ExceptionMode">
      <summary>
        <para>Lists values that specify how to handle an exception.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ExceptionMode.DisplayError">
      <summary>
        <para>Displays an error message box with text specified by the <see cref="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.ErrorText"/> property. Use the <see cref="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.WindowCaption"/> property to specify the message box’s caption.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ExceptionMode.Ignore">
      <summary>
        <para>Ignores an exception.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ExceptionMode.NoAction">
      <summary>
        <para>Displays an error icon and does not allow users to focus other rows until values are corrected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ExceptionMode.ThrowException">
      <summary>
        <para>Throws an exception. Use the <see cref="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.Exception"/> property to get this exception.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Data.InfiniteAsyncSource.FetchRowsCommand">InfiniteAsyncSource.FetchRowsCommand</see> / <see cref="P:DevExpress.Xpf.Data.PagedAsyncSource.FetchPageCommand">PagedAsyncSource.FetchPageCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.#ctor(System.Threading.CancellationToken,DevExpress.Xpf.Data.SortDefinition[],System.Object,System.Int32,System.Object[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.CancellationToken">
      <summary>
        <para>Gets a token that allows you to respond to a task cancellation request invoked by the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>A cancellation token.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.Filter">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s filter.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s filter.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.Keys">
      <summary>
        <para>Gets keys that you passed to the <see cref="M:DevExpress.Xpf.Data.PagedAsyncSource.ReloadRows(System.Object[])"/> method.</para>
      </summary>
      <value>Keys that you passed to the <see cref="M:DevExpress.Xpf.Data.PagedAsyncSource.ReloadRows(System.Object[])"/> method.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.Result">
      <summary>
        <para>Gets or sets the result of the fetch rows / fetch page operations.</para>
      </summary>
      <value>A task that returns the <see cref="T:DevExpress.Xpf.Data.FetchRowsResult"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.Skip">
      <summary>
        <para>Gets the number of rows to skip in a returned result set.</para>
      </summary>
      <value>The number of rows to skip in a returned result set.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchAsyncArgsBase.SortOrder">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s sort order.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s sort order.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.FetchPageAsyncArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Data.PagedAsyncSource.FetchPageCommand">PagedAsyncSource.FetchPageCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.FetchPageAsyncArgs.#ctor(System.Threading.CancellationToken,DevExpress.Xpf.Data.SortDefinition[],System.Object,System.Int32,System.Int32,System.Object[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.FetchPageAsyncArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchPageAsyncArgs.Take">
      <summary>
        <para>Gets the number of rows to take in a returned result set.</para>
      </summary>
      <value>The number of rows to take in a returned result set.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.FetchRowsAsyncArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Data.InfiniteAsyncSource.FetchRowsCommand">InfiniteAsyncSource.FetchRowsCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.FetchRowsAsyncArgs.#ctor(System.Threading.CancellationToken,DevExpress.Xpf.Data.SortDefinition[],System.Object,System.Int32,System.Nullable{System.Int32},System.Object[],System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.FetchRowsAsyncArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchRowsAsyncArgs.AllowRetry">
      <summary>
        <para>Gets or sets whether to allow the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> to re-request data.</para>
      </summary>
      <value>true to allow the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> to re-request data; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchRowsAsyncArgs.SkipToken">
      <summary>
        <para>Gets the skip token.</para>
      </summary>
      <value>The skip token.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FetchRowsAsyncArgs.Take">
      <summary>
        <para>Gets one of the following values:</para>
        <para />
        <para />
        <para />
      </summary>
      <value>The number of rows to be reloaded.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.FilterArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomRowFilterCommand">GridControl.CustomRowFilterCommand</see> or <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomNodeFilterCommand">TreeListView.CustomNodeFilterCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.FilterArgs.#ctor(System.Object,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.FilterArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FilterArgs.DefaultVisibility">
      <summary>
        <para>Gets the row’s visibility calculated by the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>true if the processed row is visible; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FilterArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.FilterArgs.Visible">
      <summary>
        <para>Gets or sets whether to display the processed row.</para>
      </summary>
      <value>true to display the processed row; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Data.InfiniteAsyncSource.GetTotalSummariesCommand">InfiniteAsyncSource.GetTotalSummariesCommand</see> / <see cref="P:DevExpress.Xpf.Data.PagedAsyncSource.GetTotalSummariesCommand">PagedAsyncSource.GetTotalSummariesCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs.#ctor(System.Threading.CancellationToken,DevExpress.Xpf.Data.SummaryDefinition[],System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs.CancellationToken">
      <summary>
        <para>Gets a token that allows you to respond to a task cancellation request invoked by the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>A cancellation token.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs.Filter">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s filter.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s filter.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs.Result">
      <summary>
        <para>Gets or sets the result of the get summaries operation.</para>
      </summary>
      <value>The result of the get summaries operation.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetSummariesAsyncArgs.Summaries">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s <see href="https://docs.devexpress.com/WPF/6128/controls-and-libraries/data-grid/data-summaries/total-summary">total summary items</see>.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s <see href="https://docs.devexpress.com/WPF/6128/controls-and-libraries/data-grid/data-summaries/total-summary">total summary items</see>.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Data.InfiniteAsyncSource.GetUniqueValuesCommand">InfiniteAsyncSource.GetUniqueValuesCommand</see> / <see cref="P:DevExpress.Xpf.Data.PagedAsyncSource.GetUniqueValuesCommand">PagedAsyncSource.GetUniqueValuesCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs.#ctor(System.Threading.CancellationToken,System.String,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs.CancellationToken">
      <summary>
        <para>Gets a token that allows you to respond to a task cancellation request invoked by the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
      <value>A cancellation token.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs.Filter">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s filter.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Grid.GridControl"/>‘s filter.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs.PropertyName">
      <summary>
        <para>Gets the field name for which the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> collects unique values.</para>
      </summary>
      <value>The field name for which the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> collects unique values.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs.Result">
      <summary>
        <para>Gets or sets the result of the get unique values operation (only values).</para>
      </summary>
      <value>A task that returns the result of the get unique values operation.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GetUniqueValuesAsyncArgs.ResultWithCounts">
      <summary>
        <para>Gets or sets the result of the get unique values operation (values and their counts).</para>
      </summary>
      <value>A task that returns the result of the get unique values operation.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.GroupDisplayTextArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomGroupDisplayTextCommand">GridControl.CustomGroupDisplayTextCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.GroupDisplayTextArgs.#ctor(System.Collections.Generic.IReadOnlyList{DevExpress.Mvvm.Xpf.GroupInfo},System.Object,System.String,System.Object,System.String)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.GroupDisplayTextArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.GroupDisplayTextArgs.GroupPath">
      <summary>
        <para>Gets a path to the processed group row. This path contains information about applied group operations.</para>
      </summary>
      <value>A path to the processed group row.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.InitNewItemArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.InitNewRowCommand">TableView.InitNewRowCommand</see> or <see cref="P:DevExpress.Xpf.Grid.TreeListView.InitNewNodeCommand">TreeListView.InitNewNodeCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.InitNewItemArgs.#ctor(System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.InitNewItemArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InitNewItemArgs.Item">
      <summary>
        <para>Gets the added record.</para>
      </summary>
      <value>The added record.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.InitNewNodeArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.InitNewNodeCommand">TreeListView.InitNewNodeCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.InitNewNodeArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.InitNewNodeArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InitNewNodeArgs.Path">
      <summary>
        <para>Gets a path to the added node. This path starts with this node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the added node. This path starts with this node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.InitNewRowArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.InitNewRowCommand">TableView.InitNewRowCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.InitNewRowArgs.#ctor(System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.InitNewRowArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InitNewRowArgs.SourceIndex">
      <summary>
        <para>Gets the added record’s data source index.</para>
      </summary>
      <value>The added record’s data source index.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.InvalidRowExceptionCommand">GridViewBase.InvalidRowExceptionCommand</see> or <see cref="P:DevExpress.Xpf.Grid.TreeListView.InvalidNodeExceptionCommand">TreeListView.InvalidNodeExceptionCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.#ctor(System.Object,System.Boolean,System.Exception,System.String,System.String,DevExpress.Mvvm.Xpf.ExceptionMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.ErrorText">
      <summary>
        <para>Gets or sets the error description.</para>
      </summary>
      <value>The error description.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.Exception">
      <summary>
        <para>Gets a thrown exception.</para>
      </summary>
      <value>A thrown exception.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.ExceptionMode">
      <summary>
        <para>Gets or sets how to handle an exception.</para>
      </summary>
      <value>A value that specifies how to handle an exception.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.IsNewItem">
      <summary>
        <para>Gets whether the validate operation processes a new data item.</para>
      </summary>
      <value>true if the validate operation processes a new data item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidItemExceptionArgs.WindowCaption">
      <summary>
        <para>Gets or sets the error window’s caption.</para>
      </summary>
      <value>The error window’s caption.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.InvalidNodeExceptionArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.InvalidNodeExceptionCommand">TreeListView.InvalidNodeExceptionCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.InvalidNodeExceptionArgs.#ctor(System.Object,System.Boolean,System.Collections.Generic.IEnumerable{System.Object},System.Exception,System.String,System.String,DevExpress.Mvvm.Xpf.ExceptionMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.InvalidNodeExceptionArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidNodeExceptionArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.InvalidRowExceptionArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.InvalidRowExceptionCommand">GridViewBase.InvalidRowExceptionCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.InvalidRowExceptionArgs.#ctor(System.Object,System.Boolean,System.Int32,System.Exception,System.String,System.String,DevExpress.Mvvm.Xpf.ExceptionMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.InvalidRowExceptionArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.InvalidRowExceptionArgs.SourceIndex">
      <summary>
        <para>Gets a record’s data source index.</para>
      </summary>
      <value>A record’s data source index.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.MouseButton">
      <summary>
        <para>Lists values that specify a mouse button that a user clicks.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.MouseButton.Left">
      <summary>
        <para>The left mouse button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.MouseButton.Middle">
      <summary>
        <para>The middle mouse button.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.MouseButton.Right">
      <summary>
        <para>The right mouse button.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NewItemArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.AddingNewRowCommand">GridViewBase.AddingNewRowCommand</see> or <see cref="P:DevExpress.Xpf.Grid.TreeListView.AddingNewNodeCommand">TreeListView.AddingNewNodeCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NewItemArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NewItemArgs"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NewItemArgs.Item">
      <summary>
        <para>Gets or sets the added record.</para>
      </summary>
      <value>The added record.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NewNodeArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.AddingNewNodeCommand">TreeListView.AddingNewNodeCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NewNodeArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NewNodeArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NewNodeArgs.ParentItem">
      <summary>
        <para>Gets a parent record.</para>
      </summary>
      <value>A parent record.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NewNodeArgs.ParentPath">
      <summary>
        <para>Gets a path to the parent node. This path starts with this node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the parent node. This path starts with this node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NewRowArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.AddingNewRowCommand">GridViewBase.AddingNewRowCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NewRowArgs.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NewRowArgs"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeCanceledArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.NodeCanceledCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeCanceledArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeCanceledArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeCanceledArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeCellValidationArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.ValidateCellCommand">TreeListView.ValidateCellCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeCellValidationArgs.#ctor(System.String,System.Object,System.Object,System.Object,System.Collections.Generic.IEnumerable{System.Object},System.Boolean,System.Threading.CancellationToken,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeCellValidationArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeCellValidationArgs.FieldName">
      <summary>
        <para>Gets the column’s field name.</para>
      </summary>
      <value>The column’s field name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeCellValidationArgs.NewValue">
      <summary>
        <para>Gets the editor’s value.</para>
      </summary>
      <value>The editor’s value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeCellValidationArgs.OldValue">
      <summary>
        <para>Gets the cell’s old valid value.</para>
      </summary>
      <value>The cell’s old valid value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeChangedArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.NodeChangedCommand">TreeListView.NodeChangedCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeChangedArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},DevExpress.Mvvm.Xpf.NodeChangeType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeChangedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeChangedArgs.ChangeType">
      <summary>
        <para>Gets the value that specifies how the node is changed.</para>
      </summary>
      <value>The value that specifies how the node is changed.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeChangedArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeChangedArgs.Path">
      <summary>
        <para>Gets a path to the changed node. This path starts with this node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the changed node. This path starts with this node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeChangeType">
      <summary>
        <para>Lists values that specify how the node is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.Add">
      <summary>
        <para>A node is added.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.CheckBox">
      <summary>
        <para>A node’s check box is checked/unchecked.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.Content">
      <summary>
        <para>A node’s <see cref="P:DevExpress.Xpf.Grid.TreeListNode.Content">content</see> is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.Expand">
      <summary>
        <para>A node is expanded/collapsed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.ExpandButtonVisibility">
      <summary>
        <para>A node’s expand button is shown/hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.Image">
      <summary>
        <para>A node’s <see cref="P:DevExpress.Xpf.Grid.TreeListNode.Image">image</see> is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.IsCheckBoxEnabled">
      <summary>
        <para>A node’s check box is enabled/disabled.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.NodeChangeType.Remove">
      <summary>
        <para>A node is removed.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeClickArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.RowDoubleClickCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeClickArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},DevExpress.Mvvm.Xpf.MouseButton)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeClickArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeClickArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeDisplayTextArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomColumnDisplayTextCommand">TreeListView.CustomColumnDisplayTextCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeDisplayTextArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.String,System.Object,System.String,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeDisplayTextArgs"/> class with specified settings.</para>
      </summary>
      <param name="item">A record in a data source.</param>
      <param name="path">A path to the processed node. This path starts with the processed node and includes all its parent nodes.</param>
      <param name="fieldName">The current field name.</param>
      <param name="value">The processed cell’s value.</param>
      <param name="displayText">The text displayed in the processed cell.</param>
      <param name="showAsNullText">Specifies whether text corresponding to a null value appears faded.</param>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeDisplayTextArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeDisplayTextArgs.ShowAsNullText">
      <summary>
        <para>Specifies whether text corresponding to a null value appears faded.</para>
      </summary>
      <value>true to fade the null text; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeEditArgs">
      <summary>
        <para>Contains information about the node that a user edits.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeEditArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeEditArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeEditArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeEditFinishedArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.NodeEditFinishedCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeEditFinishedArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.Nullable{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeEditFinishedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeEditFinishedArgs.Success">
      <summary>
        <para>Gets whether a user committed changes made in a node.</para>
      </summary>
      <value>true if a user committed changes made in a node, false if a user did not commit changes, or null if a user did not make changes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeEditStartedArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.NodeEditStartedCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeEditStartedArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeEditStartedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeEditStartingArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.NodeEditStartingCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeEditStartingArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.Boolean,DevExpress.Mvvm.CellEditorData[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeEditStartingArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeEditStartingArgs.Cancel">
      <summary>
        <para>Gets or sets whether to cancel the start of the edit operation.</para>
      </summary>
      <value>true to cancel the start of the edit operation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeEditStartingArgs.CellEditors">
      <summary>
        <para>Gets an array of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> objects that allow you to specify settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</para>
      </summary>
      <value>An array of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> objects that allow you to specify settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeEditStartingArgs.IsNewItem">
      <summary>
        <para>Gets whether the node that a user starts to edit is the <see href="https://docs.devexpress.com/WPF/6258/controls-and-libraries/data-grid/visual-elements/common-elements/new-item-row">New Item Row</see>.</para>
      </summary>
      <value>true if the node is the <see href="https://docs.devexpress.com/WPF/6258/controls-and-libraries/data-grid/visual-elements/common-elements/new-item-row">New Item Row</see>; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeFilterArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomNodeFilterCommand">TreeListView.CustomNodeFilterCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeFilterArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeFilterArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeFilterArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeSortArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomColumnSortCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeSortArgs.#ctor(System.Object,System.Object,System.String,System.Collections.Generic.IEnumerable{System.Object},System.Collections.Generic.IEnumerable{System.Object},System.Object,System.Object,DevExpress.Mvvm.Xpf.SortOrder)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeSortArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeSortArgs.FirstPath">
      <summary>
        <para>Gets a path to the node that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue"/>. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the node that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeSortArgs.SecondPath">
      <summary>
        <para>Gets a path to the node that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue"/>. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the node that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue"/>.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeSummaryArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomSummaryCommand">TreeListView.CustomSummaryCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeSummaryArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},DevExpress.Mvvm.Xpf.SummaryArgsItem,System.Object,System.Boolean,System.Object,DevExpress.Mvvm.Xpf.SummaryProcess)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeSummaryArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeSummaryArgs.IsNodeSummary">
      <summary>
        <para>Gets whether a node summary value is calculated.</para>
      </summary>
      <value>true if a node summary value is calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeSummaryArgs.IsTotalSummary">
      <summary>
        <para>Gets whether a total summary value is calculated.</para>
      </summary>
      <value>true if a total summary value is calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeSummaryArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeUpdatedArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.NodeUpdatedCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeUpdatedArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeUpdatedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeUpdatedArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.NodeValidationArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.ValidateNodeCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.NodeValidationArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.Boolean,System.Threading.CancellationToken,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.NodeValidationArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.NodeValidationArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowCanceledArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.RowCanceledCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowCanceledArgs.#ctor(System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowCanceledArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowCanceledArgs.SourceIndex">
      <summary>
        <para>Gets the index of the current record in a data source.</para>
      </summary>
      <value>The index of the current record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowCellValidationArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.ValidateCellCommand">GridViewBase.ValidateCellCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowCellValidationArgs.#ctor(System.String,System.Object,System.Object,System.Object,System.Int32,System.Boolean,System.Threading.CancellationToken,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowCellValidationArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowCellValidationArgs.FieldName">
      <summary>
        <para>Gets the column’s field name.</para>
      </summary>
      <value>The column’s field name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowCellValidationArgs.NewValue">
      <summary>
        <para>Gets the editor’s value.</para>
      </summary>
      <value>The editor’s value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowCellValidationArgs.OldValue">
      <summary>
        <para>Gets the cell’s old valid value.</para>
      </summary>
      <value>The cell’s old valid value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowClickArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.RowDoubleClickCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowClickArgs.#ctor(System.Object,System.Int32,DevExpress.Mvvm.Xpf.MouseButton)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowClickArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowClickArgs.SourceIndex">
      <summary>
        <para>Gets the index of the current record in a data source.</para>
      </summary>
      <value>The index of the current record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowEditArgs">
      <summary>
        <para>Contains information about the row that a user edits.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowEditArgs.#ctor(System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowEditArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowEditArgs.SourceIndex">
      <summary>
        <para>Gets the index of the current record in a data source.</para>
      </summary>
      <value>The index of the current record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowEditFinishedArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.RowEditFinishedCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowEditFinishedArgs.#ctor(System.Object,System.Int32,System.Nullable{System.Boolean})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowEditFinishedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowEditFinishedArgs.Success">
      <summary>
        <para>Gets whether a user committed changes made in a row.</para>
      </summary>
      <value>true if a user committed changes made in a row, false if a user did not commit changes, or null if a user did not make changes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowEditStartedArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.RowEditStartedCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowEditStartedArgs.#ctor(System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowEditStartedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowEditStartingArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TableView.RowEditStartingCommand"/> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowEditStartingArgs.#ctor(System.Object,System.Int32,System.Boolean,DevExpress.Mvvm.CellEditorData[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowEditStartingArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowEditStartingArgs.Cancel">
      <summary>
        <para>Gets or sets whether to cancel the start of the edit operation.</para>
      </summary>
      <value>true to cancel the start of the edit operation; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowEditStartingArgs.CellEditors">
      <summary>
        <para>Gets an array of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> objects that allow you to specify settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</para>
      </summary>
      <value>An array of the <see cref="T:DevExpress.Mvvm.CellEditorData"/> objects that allow you to specify settings in the <see href="https://docs.devexpress.com/WPF/403491/controls-and-libraries/data-grid/data-editing-and-validation/modify-cell-values/edit-form">Edit Form</see>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowEditStartingArgs.IsNewItem">
      <summary>
        <para>Gets whether the row that a user starts to edit is the <see href="https://docs.devexpress.com/WPF/6258/controls-and-libraries/data-grid/visual-elements/common-elements/new-item-row">New Item Row</see>.</para>
      </summary>
      <value>true if the row is the <see href="https://docs.devexpress.com/WPF/6258/controls-and-libraries/data-grid/visual-elements/common-elements/new-item-row">New Item Row</see>; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowFilterArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomRowFilterCommand">GridControl.CustomRowFilterCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowFilterArgs.#ctor(System.Object,System.Int32,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowFilterArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowFilterArgs.SourceIndex">
      <summary>
        <para>Gets the record’s index in a data source.</para>
      </summary>
      <value>The record’s index in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowSortArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomColumnSortCommand">GridControl.CustomColumnSortCommand</see> and <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomColumnGroupCommand">GridControl.CustomColumnGroupCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowSortArgs.#ctor(System.Object,System.Object,System.String,System.Int32,System.Int32,System.Object,System.Object,DevExpress.Mvvm.Xpf.SortOrder)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowSortArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSortArgs.FirstSourceIndex">
      <summary>
        <para>Gets a data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue"/>.</para>
      </summary>
      <value>A data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSortArgs.SecondSourceIndex">
      <summary>
        <para>Gets a data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue"/>.</para>
      </summary>
      <value>A data source index of the record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue"/>.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowSummaryArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomSummaryCommand">GridControl.CustomSummaryCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowSummaryArgs.#ctor(System.Object,System.Int32,DevExpress.Mvvm.Xpf.SummaryArgsItem,System.Object,System.Object,System.Collections.Generic.IReadOnlyList{DevExpress.Mvvm.Xpf.GroupInfo},DevExpress.Mvvm.Xpf.SummaryProcess,DevExpress.Mvvm.Xpf.SummaryMode)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowSummaryArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryArgs.GroupPath">
      <summary>
        <para>Gets a path to the processed group row. This path contains information about applied group operations.</para>
      </summary>
      <value>A path to the processed group row.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryArgs.IsGroupSummary">
      <summary>
        <para>Gets whether a group summary value is calculated.</para>
      </summary>
      <value>true if a group summary value is calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryArgs.IsTotalSummary">
      <summary>
        <para>Gets whether a total summary value is calculated.</para>
      </summary>
      <value>true if a total summary value is calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryArgs.Mode">
      <summary>
        <para>Gets whether the current summary is calculated against all rows or the selection.</para>
      </summary>
      <value>A value that specifies whether the current summary is calculated against all rows or the selection.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryArgs.SourceIndex">
      <summary>
        <para>Gets the record’s index in a data source.</para>
      </summary>
      <value>The record’s index in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomSummaryExistsCommand">GridControl.CustomSummaryExistsCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs.#ctor(DevExpress.Mvvm.Xpf.SummaryArgsItem,System.Collections.Generic.IReadOnlyList{DevExpress.Mvvm.Xpf.GroupInfo})">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs.Exists">
      <summary>
        <para>Gets or sets whether to calculate and display the summary value.</para>
      </summary>
      <value>true to calculate and display the summary value; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs.GroupPath">
      <summary>
        <para>Gets a path to the processed group row. This path contains information about applied group operations.</para>
      </summary>
      <value>A path to the processed group row.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs.IsGroupSummary">
      <summary>
        <para>Gets whether a group summary value is about to be calculated.</para>
      </summary>
      <value>true if a group summary value is about to be calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs.IsTotalSummary">
      <summary>
        <para>Gets whether a total summary value is about to be calculated.</para>
      </summary>
      <value>true if a total summary value is about to be calculated; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowSummaryExistsArgs.SummaryItem">
      <summary>
        <para>Gets the processed summary item.</para>
      </summary>
      <value>The processed summary item.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowUpdatedArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.RowUpdatedCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowUpdatedArgs.#ctor(System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowUpdatedArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowUpdatedArgs.SourceIndex">
      <summary>
        <para>Gets the index of the current record in a data source.</para>
      </summary>
      <value>The index of the current record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.RowValidationArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.ValidateRowCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.RowValidationArgs.#ctor(System.Object,System.Int32,System.Boolean,System.Threading.CancellationToken,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.RowValidationArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.RowValidationArgs.SourceIndex">
      <summary>
        <para>Gets the index of the current record in a data source.</para>
      </summary>
      <value>The index of the current record in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.SortArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomColumnSortCommand">GridControl.CustomColumnSortCommand</see>, <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomColumnSortCommand">TreeListView.CustomColumnSortCommand</see>, and <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomColumnGroupCommand">GridControl.CustomColumnGroupCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.SortArgs.#ctor(System.Object,System.Object,System.String,System.Object,System.Object,DevExpress.Mvvm.Xpf.SortOrder)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.SortArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.FieldName">
      <summary>
        <para>Gets the field name whose values are compared.</para>
      </summary>
      <value>The field name whose values are compared.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.FirstItem">
      <summary>
        <para>Gets a record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue"/>.</para>
      </summary>
      <value>A record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.FirstValue">
      <summary>
        <para>Gets the first compared value.</para>
      </summary>
      <value>The first compared value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.Result">
      <summary>
        <para>Gets or sets the result of a custom comparison.</para>
      </summary>
      <value>The result of a custom comparison.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.SecondItem">
      <summary>
        <para>Gets a record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue"/>.</para>
      </summary>
      <value>A record that contains the <see cref="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue"/>.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.SecondValue">
      <summary>
        <para>Gets the second compared value.</para>
      </summary>
      <value>The second compared value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SortArgs.SortOrder">
      <summary>
        <para>Gets the sort order applied to the column.</para>
      </summary>
      <value>The column’s sort order.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.SummaryArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomSummaryCommand">GridControl.CustomSummaryCommand</see> and <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomSummaryCommand">TreeListView.CustomSummaryCommand</see> properties with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.SummaryArgs.#ctor(System.Object,DevExpress.Mvvm.Xpf.SummaryArgsItem,System.Object,System.Object,DevExpress.Mvvm.Xpf.SummaryProcess)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.SummaryArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SummaryArgs.FieldValue">
      <summary>
        <para>Gets the processed field value.</para>
      </summary>
      <value>The processed field value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SummaryArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SummaryArgs.SummaryItem">
      <summary>
        <para>Gets the calculated summary item.</para>
      </summary>
      <value>The calculated summary item.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SummaryArgs.SummaryProcess">
      <summary>
        <para>Gets the calculation stage.</para>
      </summary>
      <value>The calculation stage.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SummaryArgs.TotalValue">
      <summary>
        <para>Gets or sets the total summary value.</para>
      </summary>
      <value>The total summary value.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.SummaryArgs.TotalValueReady">
      <summary>
        <para>Gets or sets whether to skip the Calculation stage of the custom summary calculation process.</para>
      </summary>
      <value>true to skip the Calculation stage; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomUnboundColumnDataCommand">GridControl.CustomUnboundColumnDataCommand</see> or <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomUnboundColumnDataCommand">TreeListView.CustomUnboundColumnDataCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs.#ctor(System.Object,System.String,System.Boolean,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs.FieldName">
      <summary>
        <para>Gets the unbound column’s field name.</para>
      </summary>
      <value>The unbound column’s field name.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs.IsGetData">
      <summary>
        <para>Gets whether you can populate unbound columns with data.</para>
      </summary>
      <value>true if you can populate unbound columns with data; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs.IsSetData">
      <summary>
        <para>Gets whether a user modifies an unbound cell’s value and you can save this value to a data source.</para>
      </summary>
      <value>true if a user modifies an unbound cell’s value and you can save this value to a data source; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnDataArgs.Value">
      <summary>
        <para>Gets or sets the processed cell’s value.</para>
      </summary>
      <value>The processed cell’s value.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.UnboundColumnNodeArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.CustomUnboundColumnDataCommand">TreeListView.CustomUnboundColumnDataCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.UnboundColumnNodeArgs.#ctor(System.Object,System.Collections.Generic.IEnumerable{System.Object},System.String,System.Boolean,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.UnboundColumnNodeArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnNodeArgs.Path">
      <summary>
        <para>Gets a path to the processed node. This path starts with the processed node and includes all its parent nodes.</para>
      </summary>
      <value>A path to the processed node. This path starts with the processed node and includes all its parent nodes.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.UnboundColumnRowArgs">
      <summary>
        <para>Contains data for the command bound to the <see cref="P:DevExpress.Xpf.Grid.GridControl.CustomUnboundColumnDataCommand">GridControl.CustomUnboundColumnDataCommand</see> property.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.UnboundColumnRowArgs.#ctor(System.Object,System.Int32,System.String,System.Boolean,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.UnboundColumnRowArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UnboundColumnRowArgs.SourceIndex">
      <summary>
        <para>Gets the record’s index in a data source.</para>
      </summary>
      <value>The record’s index in a data source.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.UniqueValuesArgs">
      <summary>
        <para>Populates the command bound to the <see cref="P:DevExpress.Xpf.Grid.DataControlBase.CustomUniqueValuesCommand">DataControlBase.CustomUniqueValuesCommand</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.UniqueValuesArgs.#ctor(System.String,System.Boolean,System.Boolean,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.UniqueValuesArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.FieldName">
      <summary>
        <para>Gets the field name for which the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> collects unique values.</para>
      </summary>
      <value>The field name for which the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> collects unique values.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.Filter">
      <summary>
        <para>Gets the current filter.</para>
      </summary>
      <value>The current filter.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.IncludeFilteredOut">
      <summary>
        <para>Gets whether the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> displays a drop-down filter’s values (including filtered out values).</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Grid.GridControl"/> displays a drop-down filter’s values (including filtered out values); otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.RoundDateTime">
      <summary>
        <para>Gets whether to ignore time values when you filter by DateTime values.</para>
      </summary>
      <value>true to ignore time values; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.UniqueValues">
      <summary>
        <para>Gets or sets unique values.</para>
      </summary>
      <value>Unique values.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.UniqueValuesAndCounts">
      <summary>
        <para>Gets or sets unique values and the number of values.</para>
      </summary>
      <value>Unique values and the number of values.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.UniqueValuesAndCountsAsync">
      <summary>
        <para>Gets or sets a task that allows you to asynchronously return unique values and the number of values.</para>
      </summary>
      <value>A task that allows you to asynchronously return unique values and the number of values.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.UniqueValuesArgs.UniqueValuesAsync">
      <summary>
        <para>Gets or sets a task that allows you to asynchronously return unique values.</para>
      </summary>
      <value>A task that allows you to asynchronously return unique values.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ValidateNodeDeletionArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.TreeListView.ValidateNodeDeletionCommand"/> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ValidateNodeDeletionArgs.#ctor(System.Object[],System.Collections.Generic.IEnumerable{System.Object}[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.ValidateNodeDeletionArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ValidateRowDeletionArgs">
      <summary>
        <para>Populates commands bound to the <see cref="P:DevExpress.Xpf.Grid.GridViewBase.ValidateRowDeletionCommand">DataViewBase.DataSourceRefresh</see> property with data.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ValidateRowDeletionArgs.#ctor(System.Object[],System.Int32[])">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.ValidateRowDeletionArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ValidationArgs">
      <summary>
        <para>Contains data for commands that validate row values within the <see cref="T:DevExpress.Xpf.Grid.GridControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ValidationArgs.#ctor(System.Object,System.Boolean,System.Threading.CancellationToken,System.Boolean)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.ValidationArgs"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationArgs.CancellationToken">
      <summary>
        <para>Gets an object that notifies that changes made within a row should be canceled.</para>
      </summary>
      <value>An object that notifies that changes made within a row should be canceled.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationArgs.IsNewItem">
      <summary>
        <para>Gets whether the validate operation processes a new data item.</para>
      </summary>
      <value>true if the validate operation processes a new data item; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationArgs.Item">
      <summary>
        <para>Gets a record in a data source.</para>
      </summary>
      <value>A record in a data source.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationArgs.Result">
      <summary>
        <para>Gets or sets an error description if row data are invalid. Null if data are valid.</para>
      </summary>
      <value>An error description.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationArgs.ResultAsync">
      <summary>
        <para>Gets or sets a task that asynchronously posts changes to an underlying data source (database).</para>
      </summary>
      <value>A task that asynchronously posts changes to an underlying data source (database).</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationArgs.UseCancellationToken">
      <summary>
        <para>Gets or sets whether changes made within a row can be canceled.</para>
      </summary>
      <value>true if changes made within a row can be canceled; otherwise, false.</value>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ValidationErrorInfo">
      <summary>
        <para>Contains information about the error (its type and description).</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ValidationErrorInfo.#ctor(System.String,DevExpress.Mvvm.Xpf.ValidationErrorType)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Mvvm.Xpf.ValidationErrorInfo"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ValidationErrorInfo.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current object.</para>
      </summary>
      <param name="obj">The object to compare with the current object.</param>
      <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationErrorInfo.ErrorContent">
      <summary>
        <para>Gets the validation error description.</para>
      </summary>
      <value>The validation error description.</value>
    </member>
    <member name="P:DevExpress.Mvvm.Xpf.ValidationErrorInfo.ErrorType">
      <summary>
        <para>Gets the error type.</para>
      </summary>
      <value>One of the <see cref="T:DevExpress.Mvvm.Xpf.ValidationErrorType"/> enumeration values.</value>
    </member>
    <member name="M:DevExpress.Mvvm.Xpf.ValidationErrorInfo.GetHashCode">
      <summary>
        <para>Serves as the default hash function.</para>
      </summary>
      <returns>A hash code for the current object.</returns>
    </member>
    <member name="T:DevExpress.Mvvm.Xpf.ValidationErrorType">
      <summary>
        <para>Enumerates error types.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ValidationErrorType.Critical">
      <summary>
        <para>A critical error.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ValidationErrorType.Default">
      <summary>
        <para>A default error.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ValidationErrorType.Information">
      <summary>
        <para>An information error.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Mvvm.Xpf.ValidationErrorType.Warning">
      <summary>
        <para>A warning.</para>
      </summary>
    </member>
    <member name="N:DevExpress.Xpf.Data">
      <summary>
        <para>Contains classes implementing the common data management functionality for DevExpress WPF controls (e.g. <see href="https://docs.devexpress.com/WPF/6084/controls-and-libraries/data-grid">DXGrid</see>).</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Data.DetachedObjectsHelper`1">
      <summary>
        <para>The helper class that contains detached objects. A detached object stores properties associated with the <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object’s</see> properties. Use this class to access the <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object’s</see> properties when the object is disposed of.</para>
      </summary>
      <typeparam name="T">The data item type.</typeparam>
    </member>
    <member name="M:DevExpress.Xpf.Data.DetachedObjectsHelper`1.ApplyProperties(`0,System.Object)">
      <summary>
        <para>Populates the specified <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object</see> with the detached object’s properties.</para>
      </summary>
      <param name="item">A <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object</see>.</param>
      <param name="detachedObject">A detached object.</param>
    </member>
    <member name="M:DevExpress.Xpf.Data.DetachedObjectsHelper`1.ConvertToDetachedObjects(System.Collections.Generic.IEnumerable{`0})">
      <summary>
        <para>Creates detached objects from the collection of <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Objects</see>. A detached object stores properties associated with the <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object’s</see> properties.</para>
      </summary>
      <param name="source">A collection of <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Objects</see>.</param>
      <returns>An array of detached objects.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Data.DetachedObjectsHelper`1.Create(System.String,System.String[])">
      <summary>
        <para>Creates the <see cref="T:DevExpress.Xpf.Data.DetachedObjectsHelper`1"/> instance with the specified parameters.</para>
      </summary>
      <param name="keyProperty">The key property that contains unique values.</param>
      <param name="properties">The <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object’s</see> properties.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Data.DetachedObjectsHelper`1"/> instance.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Data.DetachedObjectsHelper`1.GetKey(System.Object)">
      <summary>
        <para>Gets the key value of the specified detached object.</para>
      </summary>
      <param name="detachedObject">A detached object.</param>
      <returns>The key value of the specified detached object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Data.DetachedObjectsHelper`1.Properties">
      <summary>
        <para>Gets the detached object’s descriptors associated with the corresponding <see href="https://docs.devexpress.com/XPO/2077/create-a-data-model/create-a-persistent-object">Persistent Object’s</see> descriptors.</para>
      </summary>
      <value>A collection of the <see cref="T:System.ComponentModel.PropertyDescriptorCollection">PropertyDescriptor</see> objects.</value>
    </member>
    <member name="M:DevExpress.Xpf.Data.DetachedObjectsHelper`1.SetKey(System.Object,System.Object)">
      <summary>
        <para>Sets the key value of the specified detached object.</para>
      </summary>
      <param name="detachedObject">The detached object.</param>
      <param name="key">The key value.</param>
    </member>
    <member name="T:DevExpress.Xpf.Data.FetchRowsResult">
      <summary>
        <para>The result of the fetch rows operation.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Data.FetchRowsResult.#ctor(System.Object[],System.Boolean,System.Object)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Data.FetchRowsResult"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Data.FetchRowsResult.HasMoreRows">
      <summary>
        <para>Gets whether a data source has more rows.</para>
      </summary>
      <value>true, if a data source has more rows; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Data.FetchRowsResult.NextSkipToken">
      <summary>
        <para>Gets the next skip token.</para>
      </summary>
      <value>The next skip token.</value>
    </member>
    <member name="P:DevExpress.Xpf.Data.FetchRowsResult.Rows">
      <summary>
        <para>Gets fetched rows.</para>
      </summary>
      <value>Fetched rows.</value>
    </member>
    <member name="T:DevExpress.Xpf.Data.SummaryType">
      <summary>
        <para>Lists values specifying the aggregate function types.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Data.SummaryType.Avg">
      <summary>
        <para>The average value of a column.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Data.SummaryType.Count">
      <summary>
        <para>The record count.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Data.SummaryType.Max">
      <summary>
        <para>The maximum value in a column.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Data.SummaryType.Min">
      <summary>
        <para>The minimum value in a column.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Data.SummaryType.Sum">
      <summary>
        <para>The sum of all values in a column.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Data.ValueAndCount">
      <summary>
        <para>An object that contains a unique value and its count.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Data.ValueAndCount.#ctor(System.Object,System.Int32)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Data.ValueAndCount"/> class with specified settings.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Data.ValueAndCount.Count">
      <summary>
        <para>Gets the count of unique values.</para>
      </summary>
      <value>The count of unique values.</value>
    </member>
    <member name="M:DevExpress.Xpf.Data.ValueAndCount.Equals(DevExpress.Xpf.Data.ValueAndCount)">
      <summary>
        <para>Determines whether the specified object is equal to the current ValueAndCount instance.</para>
      </summary>
      <param name="other">The specified object.</param>
      <returns>true, if the specified object is equal to the current ValueAndCount instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Data.ValueAndCount.Equals(System.Object)">
      <summary>
        <para>Determines whether the specified object is equal to the current ValueAndCount instance.</para>
      </summary>
      <param name="obj">The specified object.</param>
      <returns>true, if the specified object is equal to the current ValueAndCount instance; otherwise, false.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Data.ValueAndCount.GetHashCode">
      <summary>
        <para>Gets the hash code (a number) that corresponds to the value of the current object.</para>
      </summary>
      <returns>The hash code (a number) that corresponds to the value of the current object.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Data.ValueAndCount.ToString">
      <summary>
        <para>Returns the textual representation of the ValueAndCount object.</para>
      </summary>
      <returns>The textual representation of the ValueAndCount object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Data.ValueAndCount.Value">
      <summary>
        <para>Gets a unique value.</para>
      </summary>
      <value>A unique value.</value>
    </member>
  </members>
</doc>