﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using DevExpress.Xpf.Core;
using omsnext.core.Models;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class KtforgevUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<KtforgevDto> _ktforgevData;
        private bool _isLoading;
        private bool _isLoadingMore;
        private int _currentPage = 0;
        private int _pageSize = 1000;
        private int _totalRecords = 0;
        private string? _currentSortField;
        private string? _currentSortDirection = "asc";
        private string? _currentFilterField;
        private string? _currentFilterValue;
        private bool _hasMoreData = true;

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<KtforgevDto> KtforgevData
        {
            get => _ktforgevData;
            set
            {
                _ktforgevData = value;
                OnPropertyChanged(nameof(KtforgevData));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
                UpdateStatusLabel();
            }
        }

        public KtforgevUserControl(ApiClient apiClient)
        {
            InitializeUserControl();
            
            _apiClient = apiClient;
            _ktforgevData = new ObservableCollection<KtforgevDto>();
            
            DataContext = this;
            
            Loaded += KtforgevUserControl_Loaded;
        }

        private void InitializeUserControl()
        {
            var grid = new Grid();
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
            grid.RowDefinitions.Add(new RowDefinition { Height = new GridLength(1, GridUnitType.Star) });
            grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

            // Fejléc
            var header = new TextBlock 
            { 
                Text = "Ktforgev adatok", 
                FontSize = 20, 
                FontWeight = FontWeights.Bold, 
                Margin = new Thickness(10) 
            };
            Grid.SetRow(header, 0);
            grid.Children.Add(header);

            // DataGrid
            var dataGrid = new DataGrid
            {
                Name = "KtforgevGrid",
                Margin = new Thickness(10),
                AutoGenerateColumns = false,
                CanUserAddRows = false,
                CanUserDeleteRows = false,
                IsReadOnly = true,
                GridLinesVisibility = DataGridGridLinesVisibility.Horizontal,
                HeadersVisibility = DataGridHeadersVisibility.Column,
                SelectionMode = DataGridSelectionMode.Single
            };

            // Oszlopok hozzáadása
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "ID", Binding = new System.Windows.Data.Binding("id"), Width = 80 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "Hivatkozási szám", Binding = new System.Windows.Data.Binding("hiv_szam"), Width = 150 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "T/K", Binding = new System.Windows.Data.Binding("tk"), Width = 50 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "Megnevezés", Binding = new System.Windows.Data.Binding("meg_nev"), Width = 200 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "Érték", Binding = new System.Windows.Data.Binding("ert_ek") { StringFormat = "N2" }, Width = 120 });
            dataGrid.Columns.Add(new DataGridTextColumn { Header = "Partner kód", Binding = new System.Windows.Data.Binding("par_kod"), Width = 100 });

            Grid.SetRow(dataGrid, 1);
            grid.Children.Add(dataGrid);

            // Egyszerű státusz sor
            var statusPanel = new StackPanel 
            { 
                Orientation = Orientation.Horizontal, 
                Margin = new Thickness(10, 5, 10, 5) 
            };
            
            var statusLabel = new TextBlock { Text = "Készen", Margin = new Thickness(0, 0, 20, 0) };
            var recordCountLabel = new TextBlock { Text = "Rekordok: 0" };
            
            statusPanel.Children.Add(statusLabel);
            statusPanel.Children.Add(recordCountLabel);

            Grid.SetRow(statusPanel, 2);
            grid.Children.Add(statusPanel);

            Content = grid;

            // Referenciák tárolása
            _dataGrid = dataGrid;
            _statusLabel = statusLabel;
            _recordCountLabel = recordCountLabel;
        }

        private DataGrid _dataGrid;
        private TextBlock _statusLabel;
        private TextBlock _recordCountLabel;

        private void UpdateStatusLabel()
        {
            if (_statusLabel != null)
            {
                _statusLabel.Text = _isLoading ? "Betöltés..." : "Készen";
            }
        }

        private async void KtforgevUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync(true);
            
            if (_dataGrid != null)
            {
                _dataGrid.ItemsSource = KtforgevData;
            }
        }

        private async Task LoadDataAsync(bool clearExisting = false)
        {
            if (_isLoading) return;

            IsLoading = true;
            try
            {
                if (clearExisting)
                {
                    _currentPage = 0;
                    KtforgevData.Clear();
                    _hasMoreData = true;
                }

                var skip = _currentPage * _pageSize;
                var result = await _apiClient.GetKtforgevDataAsync(
                    skip: skip,
                    take: _pageSize,
                    sortField: _currentSortField,
                    sortDirection: _currentSortDirection,
                    filterField: _currentFilterField,
                    filterValue: _currentFilterValue,
                    filterOperator: "contains");

                if (result != null)
                {
                    if (clearExisting)
                    {
                        KtforgevData.Clear();
                    }

                    foreach (var item in result.Data)
                    {
                        KtforgevData.Add(item);
                    }

                    _totalRecords = result.TotalCount;
                    _hasMoreData = KtforgevData.Count < _totalRecords;
                    
                    UpdateStatusLabels();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt az adatok betöltése során.", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateStatusLabels()
        {
            if (_recordCountLabel != null)
            {
                _recordCountLabel.Text = $"Betöltött rekordok: {KtforgevData.Count:N0} / {_totalRecords:N0}";
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}