/* DevExtreme Auth Card stílusok */
.authCard {
  display: flex;
  height: 100vh;
  background-color: var(--base-bg);
  font-family: "Inter", sans-serif;
}

.authCard .cardContent {
  margin: auto;
  padding: 24px;
  flex-grow: 0;
  max-width: 360px;
  width: auto;
  border-radius: 8px;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.04);
  background-color: white;
  border: none;
}

.authCard .header {
  margin-bottom: 24px;
  margin-top: 24px;
}

.authCard .title {
  color: var(--base-text-color);
  text-align: center;
  line-height: 24px;
  font-weight: 500;
  font-size: 24px;
}

.authCard .description {
  color: var(--base-text-color-alpha);
  line-height: 16px;
  font-size: 12px;
  margin-top: 32px;
  text-align: center;
}

.loginForm .formText {
  color: var(--base-text-color-alpha);
}

.errorMessage {
  color: #d32f2f;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
}

.successMessage {
  color: #388e3c;
  font-size: 14px;
  text-align: center;
  margin-top: 10px;
  margin-bottom: 10px;
}

/* Responsive design */
@media (max-width: 576px) {
  .authCard .cardContent {
    width: 100%;
    height: 100%;
    max-width: 100%;
    border-radius: 0;
    box-shadow: none;
    margin: 0;
    border: 0;
    flex-grow: 1;
  }
}

/* DevExtreme Form stílusok felülírása */
.loginForm :global(.dx-form) {
  background: transparent;
}

.loginForm :global(.dx-field-item) {
  margin-bottom: 16px;
}

.loginForm :global(.dx-field-item-label) {
  font-weight: 500;
  color: var(--base-text-color);
  margin-bottom: 8px;
}

.loginForm :global(.dx-texteditor) {
  border-radius: 4px;
}

.loginForm :global(.dx-texteditor.dx-editor-filled) {
  background-color: #f5f5f5;
  border: 1px solid var(--border-color);
}

.loginForm :global(.dx-texteditor.dx-state-focused.dx-editor-filled) {
  background-color: #ffffff;
  border-color: var(--accent-color);
  box-shadow: 0 0 0 2px rgba(25, 118, 210, 0.2);
}

.loginForm :global(.dx-button) {
  border-radius: 4px;
  font-weight: 500;
  text-transform: none;
}

.loginForm :global(.dx-button.dx-button-default) {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
  color: white;
}

.loginForm :global(.dx-button.dx-button-default:hover) {
  background-color: #1565c0;
  border-color: #1565c0;
}

.loginForm :global(.dx-button.dx-button-default.dx-state-disabled) {
  opacity: 0.6;
}

/* Dark mode támogatás */
.dark .authCard .cardContent {
  background-color: #2d2d2d;
  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.3);
}

.dark .loginForm :global(.dx-texteditor.dx-editor-filled) {
  background-color: #404040;
  border-color: #555555;
  color: var(--base-text-color);
}

.dark .loginForm :global(.dx-texteditor.dx-state-focused.dx-editor-filled) {
  background-color: #4a4a4a;
  border-color: var(--accent-color);
}
