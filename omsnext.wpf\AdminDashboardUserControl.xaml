<UserControl x:Class="omsnext.wpf.AdminDashboardUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Modern Ribbon Toolbar -->
        <Border Grid.Row="0" Background="#F8F9FA" BorderBrush="#E2E8F0" BorderThickness="0,0,0,1" Padding="15,10">
            <StackPanel>
                <!-- Címsor -->
                <TextBlock Text="Admin Dashboard - Jelszó Visszaállítási Kérelmek" 
                         FontSize="20" 
                         FontWeight="Bold" 
                         Foreground="#2D3748"
                         Margin="0,0,0,15"/>
                
                <!-- Toolbar -->
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <!-- Bal oldali műveletek -->
                    <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                        
                        <!-- Kérelem műveletek -->
                        <Border Background="White" CornerRadius="8" BorderBrush="#E2E8F0" BorderThickness="1" Margin="0,0,15,0" Padding="8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="Kérelem műveletek:" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                
                                <Button x:Name="ApproveButton" 
                                      Content="✅ Jóváhagy" 
                                      Click="ApproveButton_Click"
                                      Padding="12,6"
                                      Margin="2"
                                      Background="#10B981"
                                      Foreground="White"
                                      BorderThickness="0"
                                      IsEnabled="False">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}" 
                                                              CornerRadius="6" 
                                                              Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#059669"/>
                                                </Trigger>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="Background" Value="#9CA3AF"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                                
                                <Button x:Name="RejectButton" 
                                      Content="❌ Elutasít" 
                                      Click="RejectButton_Click"
                                      Padding="12,6"
                                      Margin="2"
                                      Background="#EF4444"
                                      Foreground="White"
                                      BorderThickness="0"
                                      IsEnabled="False">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}" 
                                                              CornerRadius="6" 
                                                              Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#DC2626"/>
                                                </Trigger>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="Background" Value="#9CA3AF"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                                
                                <Button x:Name="ViewButton" 
                                      Content="📝 Részletek" 
                                      Click="ViewButton_Click"
                                      Padding="12,6"
                                      Margin="2"
                                      Background="#6366F1"
                                      Foreground="White"
                                      BorderThickness="0"
                                      IsEnabled="False">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Setter Property="Template">
                                                <Setter.Value>
                                                    <ControlTemplate TargetType="Button">
                                                        <Border Background="{TemplateBinding Background}" 
                                                              CornerRadius="6" 
                                                              Padding="{TemplateBinding Padding}">
                                                            <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                        </Border>
                                                    </ControlTemplate>
                                                </Setter.Value>
                                            </Setter>
                                            <Style.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="#4F46E5"/>
                                                </Trigger>
                                                <Trigger Property="IsEnabled" Value="False">
                                                    <Setter Property="Background" Value="#9CA3AF"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                            </StackPanel>
                        </Border>
                        
                        <!-- Szűrés -->
                        <Border Background="White" CornerRadius="8" BorderBrush="#E2E8F0" BorderThickness="1" Margin="0,0,15,0" Padding="8">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="Szűrés:" FontWeight="SemiBold" VerticalAlignment="Center" Margin="0,0,10,0"/>
                                <ComboBox x:Name="StatusFilterComboBox" 
                                        Width="120" 
                                        VerticalAlignment="Center" 
                                        SelectionChanged="StatusFilter_SelectionChanged">
                                    <ComboBoxItem Content="Minden állapot" Tag=""/>
                                    <ComboBoxItem Content="Függőben" Tag="Pending" IsSelected="True"/>
                                    <ComboBoxItem Content="Jóváhagyott" Tag="Approved"/>
                                    <ComboBoxItem Content="Elutasított" Tag="Rejected"/>
                                    <ComboBoxItem Content="Befejezett" Tag="Completed"/>
                                </ComboBox>
                            </StackPanel>
                        </Border>
                        
                    </StackPanel>
                    
                    <!-- Jobb oldali műveletek -->
                    <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                        <Button x:Name="RefreshButton" 
                              Content="🔄 Frissítés" 
                              Click="RefreshButton_Click" 
                              Padding="12,8" 
                              Margin="5,0"
                              Background="#3B82F6"
                              Foreground="White"
                              BorderThickness="0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" 
                                                      CornerRadius="6" 
                                                      Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#2563EB"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                        
                        <Button x:Name="ExportButton" 
                              Content="📊 Export" 
                              Click="ExportButton_Click" 
                              Padding="12,8" 
                              Margin="5,0"
                              Background="#059669"
                              Foreground="White"
                              BorderThickness="0">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}" 
                                                      CornerRadius="6" 
                                                      Padding="{TemplateBinding Padding}">
                                                    <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                                </Border>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#047857"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Button.Style>
                        </Button>
                    </StackPanel>
                </Grid>
            </StackPanel>
        </Border>

        <!-- Grid Control -->
        <dxg:GridControl x:Name="RequestsGrid" Grid.Row="1" Margin="15">
            <dxg:GridControl.Columns>
                <dxg:GridColumn FieldName="Id" Header="ID" Width="60" AllowSorting="True"/>
                <dxg:GridColumn FieldName="UserEmail" Header="Felhasználó E-mail" Width="200" AllowSorting="True"/>
                <dxg:GridColumn FieldName="UserDisplayName" Header="Felhasználó Neve" Width="180" AllowSorting="True"/>
                <dxg:GridColumn FieldName="RequestDate" Header="Kérelem Dátuma" Width="140" AllowSorting="True"/>
                <dxg:GridColumn FieldName="Status" Header="Állapot" Width="120" AllowSorting="True">
                    <dxg:GridColumn.CellTemplate>
                        <DataTemplate>
                            <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Value}" Value="Pending">
                                                <Setter Property="Background" Value="#FEF3C7"/>
                                                <Setter Property="BorderBrush" Value="#F59E0B"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Approved">
                                                <Setter Property="Background" Value="#D1FAE5"/>
                                                <Setter Property="BorderBrush" Value="#10B981"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Rejected">
                                                <Setter Property="Background" Value="#FEE2E2"/>
                                                <Setter Property="BorderBrush" Value="#EF4444"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Completed">
                                                <Setter Property="Background" Value="#E0E7FF"/>
                                                <Setter Property="BorderBrush" Value="#6366F1"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="{Binding Value}" 
                                         FontWeight="SemiBold" 
                                         FontSize="11"
                                         HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </dxg:GridColumn.CellTemplate>
                </dxg:GridColumn>
                <dxg:GridColumn FieldName="ProcessedByAdminName" Header="Feldolgozó Admin" Width="150" AllowSorting="True"/>
                <dxg:GridColumn FieldName="ProcessedDate" Header="Feldolgozás Dátuma" Width="140" AllowSorting="True"/>
                <dxg:GridColumn FieldName="RequestSource" Header="Forrás" Width="80" AllowSorting="True"/>
            </dxg:GridControl.Columns>
            
            <dxg:GridControl.View>
                <dxg:TableView x:Name="TableView" 
                             AllowSorting="True" 
                             AllowGrouping="True" 
                             AllowColumnFiltering="True"
                             ShowGroupPanel="True"
                             AutoWidth="True"
                             NavigationStyle="Row"
                             ShowVerticalLines="False"
                             ShowHorizontalLines="True"
                             AllowPerPixelScrolling="True"
                             FocusedRowChanged="TableView_FocusedRowChanged"/>
            </dxg:GridControl.View>
        </dxg:GridControl>

        <!-- Enhanced Status Bar -->
        <StatusBar Grid.Row="2" Height="35" Background="#F8F9FA">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="StatusLabel" Text="Készen" FontWeight="Medium"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="RecordCountLabel" Text="Kérelmek: 0" FontWeight="Medium"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="15,0"/>
                    
                    <Border Background="#FEF3C7" CornerRadius="8" Padding="6,2" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8" Fill="#F59E0B" Margin="0,0,4,0"/>
                            <TextBlock Text="Függőben:" FontSize="11" FontWeight="Medium"/>
                            <TextBlock x:Name="PendingCountLabel" Text="0" FontWeight="Bold" Margin="4,0,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="#D1FAE5" CornerRadius="8" Padding="6,2" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8" Fill="#10B981" Margin="0,0,4,0"/>
                            <TextBlock Text="Befejezett:" FontSize="11" FontWeight="Medium"/>
                            <TextBlock x:Name="CompletedCountLabel" Text="0" FontWeight="Bold" Margin="4,0,0,0"/>
                        </StackPanel>
                    </Border>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>