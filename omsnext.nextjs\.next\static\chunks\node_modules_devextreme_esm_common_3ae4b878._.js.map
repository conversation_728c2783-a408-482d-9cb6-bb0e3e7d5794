{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/event_registrator_callbacks.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/event_registrator_callbacks.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport MemorizedCallbacks from \"../../../../core/memorized_callbacks\";\r\nexport default new MemorizedCallbacks;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCACe,IAAI,mKAAA,CAAA,UAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 22, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/hook_touch_props.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/hook_touch_props.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/core/m_hook_touch_props\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 43, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/utils/event_target.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/utils/event_target.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../../__internal/events/utils/m_event_target\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/config.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/config.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport configMethod from \"../__internal/core/m_config\";\r\nexport default configMethod;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCACe,yKAAA,CAAA,UAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 81, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/guid.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/guid.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    Guid\r\n} from \"../__internal/core/m_guid\";\r\nexport default Guid;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe,uKAAA,CAAA,OAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/set_template_engine.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/set_template_engine.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    setTemplateEngine\r\n} from \"../__internal/core/m_set_template_engine\";\r\nexport default setTemplateEngine;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;uCAGe,wMAAA,CAAA,oBAAiB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/events_engine.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/events_engine.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/core/m_events_engine\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 153, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/cldr-data/parent_locales.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/cldr-data/parent_locales.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\n// !!! AUTO-GENERATED FILE, DO NOT EDIT\r\nexport default {\r\n    \"en-150\": \"en-001\",\r\n    \"en-AG\": \"en-001\",\r\n    \"en-AI\": \"en-001\",\r\n    \"en-AU\": \"en-001\",\r\n    \"en-BB\": \"en-001\",\r\n    \"en-BM\": \"en-001\",\r\n    \"en-BS\": \"en-001\",\r\n    \"en-BW\": \"en-001\",\r\n    \"en-BZ\": \"en-001\",\r\n    \"en-CC\": \"en-001\",\r\n    \"en-CK\": \"en-001\",\r\n    \"en-CM\": \"en-001\",\r\n    \"en-CX\": \"en-001\",\r\n    \"en-CY\": \"en-001\",\r\n    \"en-DG\": \"en-001\",\r\n    \"en-DM\": \"en-001\",\r\n    \"en-ER\": \"en-001\",\r\n    \"en-FJ\": \"en-001\",\r\n    \"en-FK\": \"en-001\",\r\n    \"en-FM\": \"en-001\",\r\n    \"en-GB\": \"en-001\",\r\n    \"en-GD\": \"en-001\",\r\n    \"en-GG\": \"en-001\",\r\n    \"en-GH\": \"en-001\",\r\n    \"en-GI\": \"en-001\",\r\n    \"en-GM\": \"en-001\",\r\n    \"en-GY\": \"en-001\",\r\n    \"en-HK\": \"en-001\",\r\n    \"en-IE\": \"en-001\",\r\n    \"en-IL\": \"en-001\",\r\n    \"en-IM\": \"en-001\",\r\n    \"en-IN\": \"en-001\",\r\n    \"en-IO\": \"en-001\",\r\n    \"en-JE\": \"en-001\",\r\n    \"en-JM\": \"en-001\",\r\n    \"en-KE\": \"en-001\",\r\n    \"en-KI\": \"en-001\",\r\n    \"en-KN\": \"en-001\",\r\n    \"en-KY\": \"en-001\",\r\n    \"en-LC\": \"en-001\",\r\n    \"en-LR\": \"en-001\",\r\n    \"en-LS\": \"en-001\",\r\n    \"en-MG\": \"en-001\",\r\n    \"en-MO\": \"en-001\",\r\n    \"en-MS\": \"en-001\",\r\n    \"en-MT\": \"en-001\",\r\n    \"en-MU\": \"en-001\",\r\n    \"en-MV\": \"en-001\",\r\n    \"en-MW\": \"en-001\",\r\n    \"en-MY\": \"en-001\",\r\n    \"en-NA\": \"en-001\",\r\n    \"en-NF\": \"en-001\",\r\n    \"en-NG\": \"en-001\",\r\n    \"en-NR\": \"en-001\",\r\n    \"en-NU\": \"en-001\",\r\n    \"en-NZ\": \"en-001\",\r\n    \"en-PG\": \"en-001\",\r\n    \"en-PK\": \"en-001\",\r\n    \"en-PN\": \"en-001\",\r\n    \"en-PW\": \"en-001\",\r\n    \"en-RW\": \"en-001\",\r\n    \"en-SB\": \"en-001\",\r\n    \"en-SC\": \"en-001\",\r\n    \"en-SD\": \"en-001\",\r\n    \"en-SG\": \"en-001\",\r\n    \"en-SH\": \"en-001\",\r\n    \"en-SL\": \"en-001\",\r\n    \"en-SS\": \"en-001\",\r\n    \"en-SX\": \"en-001\",\r\n    \"en-SZ\": \"en-001\",\r\n    \"en-TC\": \"en-001\",\r\n    \"en-TK\": \"en-001\",\r\n    \"en-TO\": \"en-001\",\r\n    \"en-TT\": \"en-001\",\r\n    \"en-TV\": \"en-001\",\r\n    \"en-TZ\": \"en-001\",\r\n    \"en-UG\": \"en-001\",\r\n    \"en-VC\": \"en-001\",\r\n    \"en-VG\": \"en-001\",\r\n    \"en-VU\": \"en-001\",\r\n    \"en-WS\": \"en-001\",\r\n    \"en-ZA\": \"en-001\",\r\n    \"en-ZM\": \"en-001\",\r\n    \"en-ZW\": \"en-001\",\r\n    \"en-AT\": \"en-150\",\r\n    \"en-BE\": \"en-150\",\r\n    \"en-CH\": \"en-150\",\r\n    \"en-DE\": \"en-150\",\r\n    \"en-DK\": \"en-150\",\r\n    \"en-FI\": \"en-150\",\r\n    \"en-NL\": \"en-150\",\r\n    \"en-SE\": \"en-150\",\r\n    \"en-SI\": \"en-150\",\r\n    \"hi-Latn\": \"en-IN\",\r\n    \"es-AR\": \"es-419\",\r\n    \"es-BO\": \"es-419\",\r\n    \"es-BR\": \"es-419\",\r\n    \"es-BZ\": \"es-419\",\r\n    \"es-CL\": \"es-419\",\r\n    \"es-CO\": \"es-419\",\r\n    \"es-CR\": \"es-419\",\r\n    \"es-CU\": \"es-419\",\r\n    \"es-DO\": \"es-419\",\r\n    \"es-EC\": \"es-419\",\r\n    \"es-GT\": \"es-419\",\r\n    \"es-HN\": \"es-419\",\r\n    \"es-MX\": \"es-419\",\r\n    \"es-NI\": \"es-419\",\r\n    \"es-PA\": \"es-419\",\r\n    \"es-PE\": \"es-419\",\r\n    \"es-PR\": \"es-419\",\r\n    \"es-PY\": \"es-419\",\r\n    \"es-SV\": \"es-419\",\r\n    \"es-US\": \"es-419\",\r\n    \"es-UY\": \"es-419\",\r\n    \"es-VE\": \"es-419\",\r\n    nb: \"no\",\r\n    nn: \"no\",\r\n    \"pt-AO\": \"pt-PT\",\r\n    \"pt-CH\": \"pt-PT\",\r\n    \"pt-CV\": \"pt-PT\",\r\n    \"pt-FR\": \"pt-PT\",\r\n    \"pt-GQ\": \"pt-PT\",\r\n    \"pt-GW\": \"pt-PT\",\r\n    \"pt-LU\": \"pt-PT\",\r\n    \"pt-MO\": \"pt-PT\",\r\n    \"pt-MZ\": \"pt-PT\",\r\n    \"pt-ST\": \"pt-PT\",\r\n    \"pt-TL\": \"pt-PT\",\r\n    \"az-Arab\": \"und\",\r\n    \"az-Cyrl\": \"und\",\r\n    \"bal-Latn\": \"und\",\r\n    \"blt-Latn\": \"und\",\r\n    \"bm-Nkoo\": \"und\",\r\n    \"bs-Cyrl\": \"und\",\r\n    \"byn-Latn\": \"und\",\r\n    \"cu-Glag\": \"und\",\r\n    \"dje-Arab\": \"und\",\r\n    \"dyo-Arab\": \"und\",\r\n    \"en-Dsrt\": \"und\",\r\n    \"en-Shaw\": \"und\",\r\n    \"ff-Adlm\": \"und\",\r\n    \"ff-Arab\": \"und\",\r\n    \"ha-Arab\": \"und\",\r\n    \"iu-Latn\": \"und\",\r\n    \"kk-Arab\": \"und\",\r\n    \"ks-Deva\": \"und\",\r\n    \"ku-Arab\": \"und\",\r\n    \"ky-Arab\": \"und\",\r\n    \"ky-Latn\": \"und\",\r\n    \"ml-Arab\": \"und\",\r\n    \"mn-Mong\": \"und\",\r\n    \"mni-Mtei\": \"und\",\r\n    \"ms-Arab\": \"und\",\r\n    \"pa-Arab\": \"und\",\r\n    \"sat-Deva\": \"und\",\r\n    \"sd-Deva\": \"und\",\r\n    \"sd-Khoj\": \"und\",\r\n    \"sd-Sind\": \"und\",\r\n    \"shi-Latn\": \"und\",\r\n    \"so-Arab\": \"und\",\r\n    \"sr-Latn\": \"und\",\r\n    \"sw-Arab\": \"und\",\r\n    \"tg-Arab\": \"und\",\r\n    \"ug-Cyrl\": \"und\",\r\n    \"uz-Arab\": \"und\",\r\n    \"uz-Cyrl\": \"und\",\r\n    \"vai-Latn\": \"und\",\r\n    \"wo-Arab\": \"und\",\r\n    \"yo-Arab\": \"und\",\r\n    \"yue-Hans\": \"und\",\r\n    \"zh-Hant\": \"und\",\r\n    \"zh-Hant-MO\": \"zh-Hant-HK\"\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GACD,uCAAuC;;;;uCACxB;IACX,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,WAAW;IACX,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,WAAW;IACX,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,WAAW;IACX,YAAY;IACZ,WAAW;IACX,cAAc;AAClB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/parentLocale.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/parentLocale.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst PARENT_LOCALE_SEPARATOR = \"-\";\r\nexport default (parentLocales, locale) => {\r\n    const parentLocale = parentLocales[locale];\r\n    if (parentLocale) {\r\n        return \"root\" !== parentLocale && parentLocale\r\n    }\r\n    return locale.substr(0, locale.lastIndexOf(\"-\"))\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,0BAA0B;uCACjB,CAAC,eAAe;IAC3B,MAAM,eAAe,aAAa,CAAC,OAAO;IAC1C,IAAI,cAAc;QACd,OAAO,WAAW,gBAAgB;IACtC;IACA,OAAO,OAAO,MAAM,CAAC,GAAG,OAAO,WAAW,CAAC;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/core.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/core.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport dependencyInjector from \"../../../core/utils/dependency_injector\";\r\nimport parentLocales from \"./cldr-data/parent_locales\";\r\nimport getParentLocale from \"./parentLocale\";\r\nconst DEFAULT_LOCALE = \"en\";\r\nexport default dependencyInjector({\r\n    locale: (() => {\r\n        let currentLocale = \"en\";\r\n        return locale => {\r\n            if (!locale) {\r\n                return currentLocale\r\n            }\r\n            currentLocale = locale\r\n        }\r\n    })(),\r\n    getValueByClosestLocale: function(getter) {\r\n        let locale = this.locale();\r\n        let value = getter(locale);\r\n        let isRootLocale;\r\n        while (!value && !isRootLocale) {\r\n            locale = getParentLocale(parentLocales, locale);\r\n            if (locale) {\r\n                value = getter(locale)\r\n            } else {\r\n                isRootLocale = true\r\n            }\r\n        }\r\n        if (void 0 === value && \"en\" !== locale) {\r\n            return getter(\"en\")\r\n        }\r\n        return value\r\n    }\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;;;;AACA,MAAM,iBAAiB;uCACR,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;IAC9B,QAAQ,CAAC;QACL,IAAI,gBAAgB;QACpB,OAAO,CAAA;YACH,IAAI,CAAC,QAAQ;gBACT,OAAO;YACX;YACA,gBAAgB;QACpB;IACJ,CAAC;IACD,yBAAyB,SAAS,MAAM;QACpC,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,QAAQ,OAAO;QACnB,IAAI;QACJ,MAAO,CAAC,SAAS,CAAC,aAAc;YAC5B,SAAS,CAAA,GAAA,sLAAA,CAAA,UAAe,AAAD,EAAE,wMAAA,CAAA,UAAa,EAAE;YACxC,IAAI,QAAQ;gBACR,QAAQ,OAAO;YACnB,OAAO;gBACH,eAAe;YACnB;QACJ;QACA,IAAI,KAAK,MAAM,SAAS,SAAS,QAAQ;YACrC,OAAO,OAAO;QAClB;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 414, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/default_messages.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/default_messages.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\n// !!! AUTO-GENERATED FILE, DO NOT EDIT\r\nexport const defaultMessages = {\r\n    en: {\r\n        Yes: \"Yes\",\r\n        No: \"No\",\r\n        Cancel: \"Cancel\",\r\n        CheckState: \"Check state\",\r\n        Close: \"Close\",\r\n        Clear: \"Clear\",\r\n        Done: \"Done\",\r\n        Loading: \"Loading...\",\r\n        Select: \"Select...\",\r\n        Search: \"Search\",\r\n        Back: \"Back\",\r\n        OK: \"OK\",\r\n        Today: \"Today\",\r\n        Yesterday: \"Yesterday\",\r\n        \"dxCollectionWidget-noDataText\": \"No data to display\",\r\n        \"dxDropDownEditor-selectLabel\": \"Select\",\r\n        \"validation-required\": \"Required\",\r\n        \"validation-required-formatted\": \"{0} is required\",\r\n        \"validation-numeric\": \"Value must be a number\",\r\n        \"validation-numeric-formatted\": \"{0} must be a number\",\r\n        \"validation-range\": \"Value is out of range\",\r\n        \"validation-range-formatted\": \"{0} is out of range\",\r\n        \"validation-stringLength\": \"The length of the value is not correct\",\r\n        \"validation-stringLength-formatted\": \"The length of {0} is not correct\",\r\n        \"validation-custom\": \"Value is invalid\",\r\n        \"validation-custom-formatted\": \"{0} is invalid\",\r\n        \"validation-async\": \"Value is invalid\",\r\n        \"validation-async-formatted\": \"{0} is invalid\",\r\n        \"validation-compare\": \"Values do not match\",\r\n        \"validation-compare-formatted\": \"{0} does not match\",\r\n        \"validation-pattern\": \"Value does not match pattern\",\r\n        \"validation-pattern-formatted\": \"{0} does not match pattern\",\r\n        \"validation-email\": \"Email is invalid\",\r\n        \"validation-email-formatted\": \"{0} is invalid\",\r\n        \"validation-mask\": \"Value is invalid\",\r\n        \"dxLookup-searchPlaceholder\": \"Minimum character number: {0}\",\r\n        \"dxList-pullingDownText\": \"Pull down to refresh...\",\r\n        \"dxList-pulledDownText\": \"Release to refresh...\",\r\n        \"dxList-refreshingText\": \"Refreshing...\",\r\n        \"dxList-pageLoadingText\": \"Loading...\",\r\n        \"dxList-nextButtonText\": \"More\",\r\n        \"dxList-selectAll\": \"Select All\",\r\n        \"dxList-listAriaLabel\": \"Items\",\r\n        \"dxList-listAriaLabel-deletable\": \"Deletable items\",\r\n        \"dxListEditDecorator-delete\": \"Delete\",\r\n        \"dxListEditDecorator-more\": \"More\",\r\n        \"dxList-selectAll-indeterminate\": \"Half-checked\",\r\n        \"dxList-selectAll-checked\": \"Checked\",\r\n        \"dxList-selectAll-notChecked\": \"Not checked\",\r\n        \"dxList-ariaRoleDescription\": \"List\",\r\n        \"dxList-listAriaLabel-itemContent\": \"List item content\",\r\n        \"dxScrollView-pullingDownText\": \"Pull down to refresh...\",\r\n        \"dxScrollView-pulledDownText\": \"Release to refresh...\",\r\n        \"dxScrollView-refreshingText\": \"Refreshing...\",\r\n        \"dxScrollView-reachBottomText\": \"Loading...\",\r\n        \"dxDateBox-simulatedDataPickerTitleTime\": \"Select time\",\r\n        \"dxDateBox-simulatedDataPickerTitleDate\": \"Select date\",\r\n        \"dxDateBox-simulatedDataPickerTitleDateTime\": \"Select date and time\",\r\n        \"dxDateBox-validation-datetime\": \"Value must be a date or time\",\r\n        \"dxDateRangeBox-invalidStartDateMessage\": \"Start value must be a date\",\r\n        \"dxDateRangeBox-invalidEndDateMessage\": \"End value must be a date\",\r\n        \"dxDateRangeBox-startDateOutOfRangeMessage\": \"Start date is out of range\",\r\n        \"dxDateRangeBox-endDateOutOfRangeMessage\": \"End date is out of range\",\r\n        \"dxDateRangeBox-startDateLabel\": \"Start Date\",\r\n        \"dxDateRangeBox-endDateLabel\": \"End Date\",\r\n        \"dxFileUploader-selectFile\": \"Select a file\",\r\n        \"dxFileUploader-dropFile\": \"or Drop a file here\",\r\n        \"dxFileUploader-bytes\": \"bytes\",\r\n        \"dxFileUploader-kb\": \"KB\",\r\n        \"dxFileUploader-Mb\": \"MB\",\r\n        \"dxFileUploader-Gb\": \"GB\",\r\n        \"dxFileUploader-upload\": \"Upload\",\r\n        \"dxFileUploader-uploaded\": \"Uploaded\",\r\n        \"dxFileUploader-readyToUpload\": \"Ready to upload\",\r\n        \"dxFileUploader-uploadAbortedMessage\": \"Upload cancelled\",\r\n        \"dxFileUploader-uploadFailedMessage\": \"Upload failed\",\r\n        \"dxFileUploader-invalidFileExtension\": \"File type is not allowed\",\r\n        \"dxFileUploader-invalidMaxFileSize\": \"File is too large\",\r\n        \"dxFileUploader-invalidMinFileSize\": \"File is too small\",\r\n        \"dxRangeSlider-ariaFrom\": \"From\",\r\n        \"dxRangeSlider-ariaTill\": \"Till\",\r\n        \"dxSwitch-switchedOnText\": \"ON\",\r\n        \"dxSwitch-switchedOffText\": \"OFF\",\r\n        \"dxForm-optionalMark\": \"optional\",\r\n        \"dxForm-requiredMessage\": \"{0} is required\",\r\n        \"dxNumberBox-invalidValueMessage\": \"Value must be a number\",\r\n        \"dxNumberBox-noDataText\": \"No data\",\r\n        \"dxDataGrid-emptyHeaderWithColumnChooserText\": \"Use {0} to display columns\",\r\n        \"dxDataGrid-emptyHeaderWithGroupPanelText\": \"Drag a column from the group panel here\",\r\n        \"dxDataGrid-emptyHeaderWithColumnChooserAndGroupPanelText\": \"Use {0} or drag a column from the group panel\",\r\n        \"dxDataGrid-emptyHeaderColumnChooserText\": \"column chooser\",\r\n        \"dxDataGrid-columnChooserTitle\": \"Column Chooser\",\r\n        \"dxDataGrid-columnChooserEmptyText\": \"Drag a column here to hide it\",\r\n        \"dxDataGrid-groupContinuesMessage\": \"Continues on the next page\",\r\n        \"dxDataGrid-groupContinuedMessage\": \"Continued from the previous page\",\r\n        \"dxDataGrid-groupHeaderText\": \"Group by This Column\",\r\n        \"dxDataGrid-ungroupHeaderText\": \"Ungroup\",\r\n        \"dxDataGrid-ungroupAllText\": \"Ungroup All\",\r\n        \"dxDataGrid-editingEditRow\": \"Edit\",\r\n        \"dxDataGrid-editingSaveRowChanges\": \"Save\",\r\n        \"dxDataGrid-editingCancelRowChanges\": \"Cancel\",\r\n        \"dxDataGrid-editingDeleteRow\": \"Delete\",\r\n        \"dxDataGrid-editingUndeleteRow\": \"Undelete\",\r\n        \"dxDataGrid-editingConfirmDeleteMessage\": \"Are you sure you want to delete this record?\",\r\n        \"dxDataGrid-validationCancelChanges\": \"Cancel changes\",\r\n        \"dxDataGrid-groupPanelEmptyText\": \"Drag a column header here to group by that column\",\r\n        \"dxDataGrid-noDataText\": \"No data\",\r\n        \"dxDataGrid-searchPanelPlaceholder\": \"Search...\",\r\n        \"dxDataGrid-filterRowShowAllText\": \"(All)\",\r\n        \"dxDataGrid-filterRowResetOperationText\": \"Reset\",\r\n        \"dxDataGrid-filterRowOperationEquals\": \"Equals\",\r\n        \"dxDataGrid-filterRowOperationNotEquals\": \"Does not equal\",\r\n        \"dxDataGrid-filterRowOperationLess\": \"Less than\",\r\n        \"dxDataGrid-filterRowOperationLessOrEquals\": \"Less than or equal to\",\r\n        \"dxDataGrid-filterRowOperationGreater\": \"Greater than\",\r\n        \"dxDataGrid-filterRowOperationGreaterOrEquals\": \"Greater than or equal to\",\r\n        \"dxDataGrid-filterRowOperationStartsWith\": \"Starts with\",\r\n        \"dxDataGrid-filterRowOperationContains\": \"Contains\",\r\n        \"dxDataGrid-filterRowOperationNotContains\": \"Does not contain\",\r\n        \"dxDataGrid-filterRowOperationEndsWith\": \"Ends with\",\r\n        \"dxDataGrid-filterRowOperationBetween\": \"Between\",\r\n        \"dxDataGrid-filterRowOperationBetweenStartText\": \"Start\",\r\n        \"dxDataGrid-filterRowOperationBetweenEndText\": \"End\",\r\n        \"dxDataGrid-ariaSearchBox\": \"Search box\",\r\n        \"dxDataGrid-applyFilterText\": \"Apply filter\",\r\n        \"dxDataGrid-trueText\": \"true\",\r\n        \"dxDataGrid-falseText\": \"false\",\r\n        \"dxDataGrid-sortingAscendingText\": \"Sort Ascending\",\r\n        \"dxDataGrid-sortingDescendingText\": \"Sort Descending\",\r\n        \"dxDataGrid-sortingClearText\": \"Clear Sorting\",\r\n        \"dxDataGrid-ariaNotSortedColumn\": \"Not sorted column\",\r\n        \"dxDataGrid-ariaSortedAscendingColumn\": \"Column sorted in ascending order\",\r\n        \"dxDataGrid-ariaSortedDescendingColumn\": \"Column sorted in descending order\",\r\n        \"dxDataGrid-ariaSortIndex\": \"Sort index {0}\",\r\n        \"dxDataGrid-editingSaveAllChanges\": \"Save changes\",\r\n        \"dxDataGrid-editingCancelAllChanges\": \"Discard changes\",\r\n        \"dxDataGrid-editingAddRow\": \"Add a row\",\r\n        \"dxDataGrid-summaryMin\": \"Min: {0}\",\r\n        \"dxDataGrid-summaryMinOtherColumn\": \"Min of {1} is {0}\",\r\n        \"dxDataGrid-summaryMax\": \"Max: {0}\",\r\n        \"dxDataGrid-summaryMaxOtherColumn\": \"Max of {1} is {0}\",\r\n        \"dxDataGrid-summaryAvg\": \"Avg: {0}\",\r\n        \"dxDataGrid-summaryAvgOtherColumn\": \"Avg of {1} is {0}\",\r\n        \"dxDataGrid-summarySum\": \"Sum: {0}\",\r\n        \"dxDataGrid-summarySumOtherColumn\": \"Sum of {1} is {0}\",\r\n        \"dxDataGrid-summaryCount\": \"Count: {0}\",\r\n        \"dxDataGrid-columnFixingFix\": \"Set Fixed Position\",\r\n        \"dxDataGrid-columnFixingUnfix\": \"Unfix\",\r\n        \"dxDataGrid-columnFixingLeftPosition\": \"Left\",\r\n        \"dxDataGrid-columnFixingRightPosition\": \"Right\",\r\n        \"dxDataGrid-columnFixingStickyPosition\": \"Sticky\",\r\n        \"dxDataGrid-exportTo\": \"Export\",\r\n        \"dxDataGrid-exportToExcel\": \"Export to Excel file\",\r\n        \"dxDataGrid-exporting\": \"Exporting...\",\r\n        \"dxDataGrid-excelFormat\": \"Excel file\",\r\n        \"dxDataGrid-selectedRows\": \"Selected rows\",\r\n        \"dxDataGrid-exportSelectedRows\": \"Export selected rows to {0}\",\r\n        \"dxDataGrid-exportAll\": \"Export all data to {0}\",\r\n        \"dxDataGrid-headerFilterLabel\": \"Filter options\",\r\n        \"dxDataGrid-headerFilterIndicatorLabel\": \"Show filter options for column '{0}'\",\r\n        \"dxDataGrid-headerFilterEmptyValue\": \"(Blanks)\",\r\n        \"dxDataGrid-headerFilterOK\": \"OK\",\r\n        \"dxDataGrid-headerFilterCancel\": \"Cancel\",\r\n        \"dxDataGrid-ariaAdaptiveCollapse\": \"Hide additional data\",\r\n        \"dxDataGrid-ariaAdaptiveExpand\": \"Display additional data\",\r\n        \"dxDataGrid-ariaColumn\": \"Column\",\r\n        \"dxDataGrid-ariaColumnHeader\": \"Column header\",\r\n        \"dxDataGrid-ariaValue\": \"Value\",\r\n        \"dxDataGrid-ariaError\": \"Error\",\r\n        \"dxDataGrid-ariaRevertButton\": \"Press Escape to discard the changes\",\r\n        \"dxDataGrid-ariaFilterCell\": \"Filter cell\",\r\n        \"dxDataGrid-ariaCollapse\": \"Collapse\",\r\n        \"dxDataGrid-ariaModifiedCell\": \"Modified\",\r\n        \"dxDataGrid-ariaDeletedCell\": \"Deleted\",\r\n        \"dxDataGrid-ariaEditableCell\": \"Editable\",\r\n        \"dxDataGrid-ariaExpand\": \"Expand\",\r\n        \"dxDataGrid-ariaCollapsedRow\": \"Collapsed row\",\r\n        \"dxDataGrid-ariaExpandedRow\": \"Expanded row\",\r\n        \"dxDataGrid-ariaDataGrid\": \"Data grid with {0} rows and {1} columns\",\r\n        \"dxDataGrid-ariaSearchInGrid\": \"Search in the data grid\",\r\n        \"dxDataGrid-ariaSelectAll\": \"Select all\",\r\n        \"dxDataGrid-ariaSelectRow\": \"Select row\",\r\n        \"dxDataGrid-ariaToolbar\": \"Data grid toolbar\",\r\n        \"dxDataGrid-ariaEditForm\": \"Edit form\",\r\n        \"dxDataGrid-filterBuilderPopupTitle\": \"Filter Builder\",\r\n        \"dxDataGrid-filterPanelCreateFilter\": \"Create Filter\",\r\n        \"dxDataGrid-filterPanelClearFilter\": \"Clear\",\r\n        \"dxDataGrid-filterPanelFilterEnabledHint\": \"Enable the filter\",\r\n        \"dxDataGrid-masterDetail\": \"Cell with details\",\r\n        \"dxDataGrid-moveColumnToTheRight\": \"Move to the right\",\r\n        \"dxDataGrid-moveColumnToTheLeft\": \"Move to the left\",\r\n        \"dxTreeList-ariaTreeList\": \"Tree list with {0} rows and {1} columns\",\r\n        \"dxTreeList-ariaExpandableInstruction\": \"Press Ctrl + right arrow to expand the focused node and Ctrl + left arrow to collapse it\",\r\n        \"dxTreeList-ariaSearchInGrid\": \"Search in the tree list\",\r\n        \"dxTreeList-ariaToolbar\": \"Tree list toolbar\",\r\n        \"dxTreeList-editingAddRowToNode\": \"Add\",\r\n        \"dxPager-infoText\": \"Page {0} of {1} ({2} items)\",\r\n        \"dxPager-pagesCountText\": \"of\",\r\n        \"dxPager-pageSize\": \"Items per page: {0}\",\r\n        \"dxPager-pageSizesAllText\": \"All\",\r\n        \"dxPager-page\": \"Page {0}\",\r\n        \"dxPager-prevPage\": \"Previous page\",\r\n        \"dxPager-nextPage\": \"Next page\",\r\n        \"dxPager-ariaLabel\": \"Page navigation\",\r\n        \"dxPager-ariaPageSize\": \"Page size\",\r\n        \"dxPager-ariaPageNumber\": \"Page number\",\r\n        \"dxPagination-infoText\": \"Page {0} of {1} ({2} items)\",\r\n        \"dxPagination-pagesCountText\": \"of\",\r\n        \"dxPagination-pageSize\": \"Items per page: {0}\",\r\n        \"dxPagination-pageSizesAllText\": \"All\",\r\n        \"dxPagination-page\": \"Page {0}\",\r\n        \"dxPagination-prevPage\": \"Previous page\",\r\n        \"dxPagination-nextPage\": \"Next page\",\r\n        \"dxPagination-ariaLabel\": \"Page navigation\",\r\n        \"dxPagination-ariaPageSize\": \"Page size\",\r\n        \"dxPagination-ariaPageNumber\": \"Page number\",\r\n        \"dxPivotGrid-grandTotal\": \"Grand Total\",\r\n        \"dxPivotGrid-total\": \"{0} Total\",\r\n        \"dxPivotGrid-fieldChooserTitle\": \"Field Chooser\",\r\n        \"dxPivotGrid-showFieldChooser\": \"Show Field Chooser\",\r\n        \"dxPivotGrid-expandAll\": \"Expand All\",\r\n        \"dxPivotGrid-collapseAll\": \"Collapse All\",\r\n        \"dxPivotGrid-sortColumnBySummary\": 'Sort \"{0}\" by This Column',\r\n        \"dxPivotGrid-sortRowBySummary\": 'Sort \"{0}\" by This Row',\r\n        \"dxPivotGrid-removeAllSorting\": \"Remove All Sorting\",\r\n        \"dxPivotGrid-dataNotAvailable\": \"N/A\",\r\n        \"dxPivotGrid-rowFields\": \"Row Fields\",\r\n        \"dxPivotGrid-columnFields\": \"Column Fields\",\r\n        \"dxPivotGrid-dataFields\": \"Data Fields\",\r\n        \"dxPivotGrid-filterFields\": \"Filter Fields\",\r\n        \"dxPivotGrid-allFields\": \"All Fields\",\r\n        \"dxPivotGrid-columnFieldArea\": \"Drop Column Fields Here\",\r\n        \"dxPivotGrid-dataFieldArea\": \"Drop Data Fields Here\",\r\n        \"dxPivotGrid-rowFieldArea\": \"Drop Row Fields Here\",\r\n        \"dxPivotGrid-filterFieldArea\": \"Drop Filter Fields Here\",\r\n        \"dxScheduler-dateRange\": \"from {0} to {1}\",\r\n        \"dxScheduler-ariaLabel\": \"Scheduler. {0} view: {1} with {2} appointments\",\r\n        \"dxScheduler-ariaLabel-currentIndicator-present\": \"The current time indicator is visible in the view\",\r\n        \"dxScheduler-ariaLabel-currentIndicator-not-present\": \"The current time indicator is not visible on the screen\",\r\n        \"dxScheduler-appointmentAriaLabel-group\": \"Group: {0}\",\r\n        \"dxScheduler-appointmentAriaLabel-recurring\": \"Recurring appointment\",\r\n        \"dxScheduler-appointmentListAriaLabel\": \"Appointment list\",\r\n        \"dxScheduler-editorLabelTitle\": \"Subject\",\r\n        \"dxScheduler-editorLabelStartDate\": \"Start Date\",\r\n        \"dxScheduler-editorLabelEndDate\": \"End Date\",\r\n        \"dxScheduler-editorLabelDescription\": \"Description\",\r\n        \"dxScheduler-editorLabelRecurrence\": \"Repeat\",\r\n        \"dxScheduler-navigationToday\": \"Today\",\r\n        \"dxScheduler-navigationPrevious\": \"Previous page\",\r\n        \"dxScheduler-navigationNext\": \"Next page\",\r\n        \"dxScheduler-openAppointment\": \"Open appointment\",\r\n        \"dxScheduler-recurrenceNever\": \"Never\",\r\n        \"dxScheduler-recurrenceMinutely\": \"Every minute\",\r\n        \"dxScheduler-recurrenceHourly\": \"Hourly\",\r\n        \"dxScheduler-recurrenceDaily\": \"Daily\",\r\n        \"dxScheduler-recurrenceWeekly\": \"Weekly\",\r\n        \"dxScheduler-recurrenceMonthly\": \"Monthly\",\r\n        \"dxScheduler-recurrenceYearly\": \"Yearly\",\r\n        \"dxScheduler-recurrenceRepeatEvery\": \"Repeat Every\",\r\n        \"dxScheduler-recurrenceRepeatOn\": \"Repeat On\",\r\n        \"dxScheduler-recurrenceEnd\": \"End repeat\",\r\n        \"dxScheduler-recurrenceAfter\": \"After\",\r\n        \"dxScheduler-recurrenceOn\": \"On\",\r\n        \"dxScheduler-recurrenceUntilDateLabel\": \"Date when repeat ends\",\r\n        \"dxScheduler-recurrenceOccurrenceLabel\": \"Number of occurrences\",\r\n        \"dxScheduler-recurrenceRepeatMinutely\": \"minute(s)\",\r\n        \"dxScheduler-recurrenceRepeatHourly\": \"hour(s)\",\r\n        \"dxScheduler-recurrenceRepeatDaily\": \"day(s)\",\r\n        \"dxScheduler-recurrenceRepeatWeekly\": \"week(s)\",\r\n        \"dxScheduler-recurrenceRepeatMonthly\": \"month(s)\",\r\n        \"dxScheduler-recurrenceRepeatYearly\": \"year(s)\",\r\n        \"dxScheduler-switcherDay\": \"Day\",\r\n        \"dxScheduler-switcherWeek\": \"Week\",\r\n        \"dxScheduler-switcherWorkWeek\": \"Work Week\",\r\n        \"dxScheduler-switcherMonth\": \"Month\",\r\n        \"dxScheduler-switcherAgenda\": \"Agenda\",\r\n        \"dxScheduler-switcherTimelineDay\": \"Timeline Day\",\r\n        \"dxScheduler-switcherTimelineWeek\": \"Timeline Week\",\r\n        \"dxScheduler-switcherTimelineWorkWeek\": \"Timeline Work Week\",\r\n        \"dxScheduler-switcherTimelineMonth\": \"Timeline Month\",\r\n        \"dxScheduler-recurrenceRepeatOnDate\": \"on date\",\r\n        \"dxScheduler-recurrenceRepeatCount\": \"occurrence(s)\",\r\n        \"dxScheduler-allDay\": \"All day\",\r\n        \"dxScheduler-ariaEditForm\": \"Edit form\",\r\n        \"dxScheduler-confirmRecurrenceEditTitle\": \"Edit Recurring Appointment\",\r\n        \"dxScheduler-confirmRecurrenceDeleteTitle\": \"Delete Recurring Appointment\",\r\n        \"dxScheduler-confirmRecurrenceEditMessage\": \"Do you want to edit only this appointment or the whole series?\",\r\n        \"dxScheduler-confirmRecurrenceDeleteMessage\": \"Do you want to delete only this appointment or the whole series?\",\r\n        \"dxScheduler-confirmRecurrenceEditSeries\": \"Edit series\",\r\n        \"dxScheduler-confirmRecurrenceDeleteSeries\": \"Delete series\",\r\n        \"dxScheduler-confirmRecurrenceEditOccurrence\": \"Edit appointment\",\r\n        \"dxScheduler-confirmRecurrenceDeleteOccurrence\": \"Delete appointment\",\r\n        \"dxScheduler-noTimezoneTitle\": \"No timezone\",\r\n        \"dxScheduler-moreAppointments\": \"{0} more\",\r\n        \"dxCalendar-currentDay\": \"Today\",\r\n        \"dxCalendar-currentMonth\": \"Current month\",\r\n        \"dxCalendar-currentYear\": \"Current year\",\r\n        \"dxCalendar-currentYearRange\": \"Current year range\",\r\n        \"dxCalendar-todayButtonText\": \"Today\",\r\n        \"dxCalendar-ariaWidgetName\": \"Calendar\",\r\n        \"dxCalendar-previousMonthButtonLabel\": \"Previous month\",\r\n        \"dxCalendar-previousYearButtonLabel\": \"Previous year\",\r\n        \"dxCalendar-previousDecadeButtonLabel\": \"Previous decade\",\r\n        \"dxCalendar-previousCenturyButtonLabel\": \"Previous century\",\r\n        \"dxCalendar-nextMonthButtonLabel\": \"Next month\",\r\n        \"dxCalendar-nextYearButtonLabel\": \"Next year\",\r\n        \"dxCalendar-nextDecadeButtonLabel\": \"Next decade\",\r\n        \"dxCalendar-nextCenturyButtonLabel\": \"Next century\",\r\n        \"dxCalendar-captionMonthLabel\": \"Month selection\",\r\n        \"dxCalendar-captionYearLabel\": \"Year selection\",\r\n        \"dxCalendar-captionDecadeLabel\": \"Decade selection\",\r\n        \"dxCalendar-captionCenturyLabel\": \"Century selection\",\r\n        \"dxCalendar-selectedDate\": \"The selected date is {0}\",\r\n        \"dxCalendar-selectedDates\": \"The selected dates\",\r\n        \"dxCalendar-selectedDateRange\": \"The selected date range is from {0} to {1}\",\r\n        \"dxCalendar-selectedMultipleDateRange\": \"from {0} to {1}\",\r\n        \"dxCalendar-selectedDateRangeCount\": \"There are {0} selected date ranges\",\r\n        \"dxCalendar-readOnlyLabel\": \"Read-only calendar\",\r\n        \"dxCardView-ariaSearchInGrid\": \"Search in the card view\",\r\n        \"dxCardView-ariaHeaderItemLabel\": \"Field name {0}\",\r\n        \"dxCardView-ariaHeaderItemSortingAscendingLabel\": \"Sorted in ascending order\",\r\n        \"dxCardView-ariaHeaderItemSortingDescendingLabel\": \"Sorted in descending order\",\r\n        \"dxCardView-ariaHeaderItemSortingIndexLabel\": \"Sort index {0}\",\r\n        \"dxCardView-ariaHeaderHasHeaderFilterLabel\": \"Header filter applied\",\r\n        \"dxCardView-ariaSelectCard\": \"Select card\",\r\n        \"dxCardView-ariaCardView\": \"Card view with {0} cards. Each card has {1} fields\",\r\n        \"dxCardView-ariaCard\": \"Card\",\r\n        \"dxCardView-ariaEditableCard\": \"Editable card\",\r\n        \"dxCardView-ariaCardPosition\": \"Row {0}, column {1}\",\r\n        \"dxCardView-ariaSelectedCardState\": \"Selected\",\r\n        \"dxCardView-ariaNotSelectedCardState\": \"Not selected\",\r\n        \"dxCardView-selectAll\": \"Select all\",\r\n        \"dxCardView-clearSelection\": \"Clear selection\",\r\n        \"dxCardView-cardNoImageAriaLabel\": \"No image\",\r\n        \"dxCardView-headerItemDropZoneText\": \"Drop the header item here\",\r\n        \"dxCardView-emptyHeaderPanelText\": \"Use {0} to display columns\",\r\n        \"dxCardView-emptyHeaderPanelColumnChooserText\": \"column chooser\",\r\n        \"dxAvatar-defaultImageAlt\": \"Avatar\",\r\n        \"dxChat-elementAriaLabel\": \"Chat\",\r\n        \"dxChat-textareaPlaceholder\": \"Type a message\",\r\n        \"dxChat-sendButtonAriaLabel\": \"Send\",\r\n        \"dxChat-cancelEditingButtonAriaLabel\": \"Cancel\",\r\n        \"dxChat-editingMessageCaption\": \"Edit Message\",\r\n        \"dxChat-defaultUserName\": \"Unknown User\",\r\n        \"dxChat-messageListAriaLabel\": \"Message list\",\r\n        \"dxChat-alertListAriaLabel\": \"Error list\",\r\n        \"dxChat-emptyListMessage\": \"There are no messages in this chat\",\r\n        \"dxChat-emptyListPrompt\": \"Write your first message\",\r\n        \"dxChat-typingMessageSingleUser\": \"{0} is typing...\",\r\n        \"dxChat-typingMessageTwoUsers\": \"{0} and {1} are typing...\",\r\n        \"dxChat-typingMessageThreeUsers\": \"{0}, {1} and {2} are typing...\",\r\n        \"dxChat-typingMessageMultipleUsers\": \"{0} and others are typing...\",\r\n        \"dxChat-editedMessageText\": \"Edited\",\r\n        \"dxChat-editingEditMessage\": \"Edit\",\r\n        \"dxChat-editingDeleteMessage\": \"Delete\",\r\n        \"dxChat-editingDeleteConfirmText\": \"Are you sure you want to delete this message?\",\r\n        \"dxChat-deletedMessageText\": \"This message was deleted\",\r\n        \"dxChat-defaultImageAlt\": \"Image shared in chat\",\r\n        \"dxColorView-ariaRed\": \"Red\",\r\n        \"dxColorView-ariaGreen\": \"Green\",\r\n        \"dxColorView-ariaBlue\": \"Blue\",\r\n        \"dxColorView-ariaAlpha\": \"Transparency\",\r\n        \"dxColorView-ariaHex\": \"Color code\",\r\n        \"dxTagBox-selected\": \"{0} selected\",\r\n        \"dxTagBox-allSelected\": \"All selected ({0})\",\r\n        \"dxTagBox-moreSelected\": \"{0} more\",\r\n        \"dxTagBox-tagRoleDescription\": \"Tag. Press the delete button to remove this tag\",\r\n        \"dxTagBox-ariaRoleDescription\": \"Tag box\",\r\n        \"vizExport-printingButtonText\": \"Print\",\r\n        \"vizExport-titleMenuText\": \"Exporting/Printing\",\r\n        \"vizExport-exportButtonText\": \"{0} file\",\r\n        \"dxFilterBuilder-and\": \"And\",\r\n        \"dxFilterBuilder-or\": \"Or\",\r\n        \"dxFilterBuilder-notAnd\": \"Not And\",\r\n        \"dxFilterBuilder-notOr\": \"Not Or\",\r\n        \"dxFilterBuilder-addCondition\": \"Add Condition\",\r\n        \"dxFilterBuilder-addGroup\": \"Add Group\",\r\n        \"dxFilterBuilder-enterValueText\": \"<enter a value>\",\r\n        \"dxFilterBuilder-filterOperationEquals\": \"Equals\",\r\n        \"dxFilterBuilder-filterOperationNotEquals\": \"Does not equal\",\r\n        \"dxFilterBuilder-filterOperationLess\": \"Is less than\",\r\n        \"dxFilterBuilder-filterOperationLessOrEquals\": \"Is less than or equal to\",\r\n        \"dxFilterBuilder-filterOperationGreater\": \"Is greater than\",\r\n        \"dxFilterBuilder-filterOperationGreaterOrEquals\": \"Is greater than or equal to\",\r\n        \"dxFilterBuilder-filterOperationStartsWith\": \"Starts with\",\r\n        \"dxFilterBuilder-filterOperationContains\": \"Contains\",\r\n        \"dxFilterBuilder-filterOperationNotContains\": \"Does not contain\",\r\n        \"dxFilterBuilder-filterOperationEndsWith\": \"Ends with\",\r\n        \"dxFilterBuilder-filterOperationIsBlank\": \"Is blank\",\r\n        \"dxFilterBuilder-filterOperationIsNotBlank\": \"Is not blank\",\r\n        \"dxFilterBuilder-filterOperationBetween\": \"Is between\",\r\n        \"dxFilterBuilder-filterOperationAnyOf\": \"Is any of\",\r\n        \"dxFilterBuilder-filterOperationNoneOf\": \"Is none of\",\r\n        \"dxFilterBuilder-filterAriaRootElement\": \"Filter builder\",\r\n        \"dxFilterBuilder-filterAriaGroupLevel\": \"Level {0}\",\r\n        \"dxFilterBuilder-filterAriaGroupItem\": \"Group item\",\r\n        \"dxFilterBuilder-filterAriaOperationButton\": \"Operation\",\r\n        \"dxFilterBuilder-filterAriaAddButton\": \"Add\",\r\n        \"dxFilterBuilder-filterAriaRemoveButton\": \"Remove {0}\",\r\n        \"dxFilterBuilder-filterAriaItemField\": \"Item field\",\r\n        \"dxFilterBuilder-filterAriaItemOperation\": \"Item operation\",\r\n        \"dxFilterBuilder-filterAriaItemValue\": \"Item value\",\r\n        \"dxHtmlEditor-dialogColorCaption\": \"Change Font Color\",\r\n        \"dxHtmlEditor-dialogBackgroundCaption\": \"Change Background Color\",\r\n        \"dxHtmlEditor-dialogLinkCaption\": \"Add Link\",\r\n        \"dxHtmlEditor-dialogLinkUrlField\": \"URL\",\r\n        \"dxHtmlEditor-dialogLinkTextField\": \"Text\",\r\n        \"dxHtmlEditor-dialogLinkTargetField\": \"Open link in new window\",\r\n        \"dxHtmlEditor-dialogImageCaption\": \"Add Image\",\r\n        \"dxHtmlEditor-dialogImageUrlField\": \"URL\",\r\n        \"dxHtmlEditor-dialogImageAltField\": \"Alternate text\",\r\n        \"dxHtmlEditor-dialogImageWidthField\": \"Width (px)\",\r\n        \"dxHtmlEditor-dialogImageHeightField\": \"Height (px)\",\r\n        \"dxHtmlEditor-dialogInsertTableRowsField\": \"Rows\",\r\n        \"dxHtmlEditor-dialogInsertTableColumnsField\": \"Columns\",\r\n        \"dxHtmlEditor-dialogInsertTableCaption\": \"Insert Table\",\r\n        \"dxHtmlEditor-dialogUpdateImageCaption\": \"Update Image\",\r\n        \"dxHtmlEditor-dialogImageUpdateButton\": \"Update\",\r\n        \"dxHtmlEditor-dialogImageAddButton\": \"Add\",\r\n        \"dxHtmlEditor-dialogImageSpecifyUrl\": \"From the Web\",\r\n        \"dxHtmlEditor-dialogImageSelectFile\": \"From This Device\",\r\n        \"dxHtmlEditor-dialogImageKeepAspectRatio\": \"Keep Aspect Ratio\",\r\n        \"dxHtmlEditor-dialogImageEncodeToBase64\": \"Encode to Base64\",\r\n        \"dxHtmlEditor-heading\": \"Heading\",\r\n        \"dxHtmlEditor-normalText\": \"Normal text\",\r\n        \"dxHtmlEditor-background\": \"Background Color\",\r\n        \"dxHtmlEditor-bold\": \"Bold\",\r\n        \"dxHtmlEditor-color\": \"Font Color\",\r\n        \"dxHtmlEditor-font\": \"Font\",\r\n        \"dxHtmlEditor-italic\": \"Italic\",\r\n        \"dxHtmlEditor-link\": \"Add Link\",\r\n        \"dxHtmlEditor-image\": \"Add Image\",\r\n        \"dxHtmlEditor-size\": \"Size\",\r\n        \"dxHtmlEditor-strike\": \"Strikethrough\",\r\n        \"dxHtmlEditor-subscript\": \"Subscript\",\r\n        \"dxHtmlEditor-superscript\": \"Superscript\",\r\n        \"dxHtmlEditor-underline\": \"Underline\",\r\n        \"dxHtmlEditor-blockquote\": \"Blockquote\",\r\n        \"dxHtmlEditor-header\": \"Header\",\r\n        \"dxHtmlEditor-increaseIndent\": \"Increase Indent\",\r\n        \"dxHtmlEditor-decreaseIndent\": \"Decrease Indent\",\r\n        \"dxHtmlEditor-orderedList\": \"Ordered List\",\r\n        \"dxHtmlEditor-bulletList\": \"Bullet List\",\r\n        \"dxHtmlEditor-alignLeft\": \"Align Left\",\r\n        \"dxHtmlEditor-alignCenter\": \"Align Center\",\r\n        \"dxHtmlEditor-alignRight\": \"Align Right\",\r\n        \"dxHtmlEditor-alignJustify\": \"Align Justify\",\r\n        \"dxHtmlEditor-codeBlock\": \"Code Block\",\r\n        \"dxHtmlEditor-variable\": \"Add Variable\",\r\n        \"dxHtmlEditor-undo\": \"Undo\",\r\n        \"dxHtmlEditor-redo\": \"Redo\",\r\n        \"dxHtmlEditor-clear\": \"Clear Formatting\",\r\n        \"dxHtmlEditor-insertTable\": \"Insert Table\",\r\n        \"dxHtmlEditor-insertHeaderRow\": \"Insert Header Row\",\r\n        \"dxHtmlEditor-insertRowAbove\": \"Insert Row Above\",\r\n        \"dxHtmlEditor-insertRowBelow\": \"Insert Row Below\",\r\n        \"dxHtmlEditor-insertColumnLeft\": \"Insert Column Left\",\r\n        \"dxHtmlEditor-insertColumnRight\": \"Insert Column Right\",\r\n        \"dxHtmlEditor-deleteColumn\": \"Delete Column\",\r\n        \"dxHtmlEditor-deleteRow\": \"Delete Row\",\r\n        \"dxHtmlEditor-deleteTable\": \"Delete Table\",\r\n        \"dxHtmlEditor-cellProperties\": \"Cell Properties\",\r\n        \"dxHtmlEditor-tableProperties\": \"Table Properties\",\r\n        \"dxHtmlEditor-insert\": \"Insert\",\r\n        \"dxHtmlEditor-delete\": \"Delete\",\r\n        \"dxHtmlEditor-border\": \"Border\",\r\n        \"dxHtmlEditor-style\": \"Style\",\r\n        \"dxHtmlEditor-width\": \"Width\",\r\n        \"dxHtmlEditor-height\": \"Height\",\r\n        \"dxHtmlEditor-borderColor\": \"Color\",\r\n        \"dxHtmlEditor-borderWidth\": \"Border Width\",\r\n        \"dxHtmlEditor-tableBackground\": \"Background\",\r\n        \"dxHtmlEditor-dimensions\": \"Dimensions\",\r\n        \"dxHtmlEditor-alignment\": \"Alignment\",\r\n        \"dxHtmlEditor-horizontal\": \"Horizontal\",\r\n        \"dxHtmlEditor-vertical\": \"Vertical\",\r\n        \"dxHtmlEditor-paddingVertical\": \"Vertical Padding\",\r\n        \"dxHtmlEditor-paddingHorizontal\": \"Horizontal Padding\",\r\n        \"dxHtmlEditor-pixels\": \"Pixels\",\r\n        \"dxHtmlEditor-list\": \"List\",\r\n        \"dxHtmlEditor-ordered\": \"Ordered\",\r\n        \"dxHtmlEditor-bullet\": \"Bullet\",\r\n        \"dxHtmlEditor-align\": \"Align\",\r\n        \"dxHtmlEditor-center\": \"Center\",\r\n        \"dxHtmlEditor-left\": \"Left\",\r\n        \"dxHtmlEditor-right\": \"Right\",\r\n        \"dxHtmlEditor-indent\": \"Indent\",\r\n        \"dxHtmlEditor-justify\": \"Justify\",\r\n        \"dxHtmlEditor-borderStyleNone\": \"none\",\r\n        \"dxHtmlEditor-borderStyleHidden\": \"hidden\",\r\n        \"dxHtmlEditor-borderStyleDotted\": \"dotted\",\r\n        \"dxHtmlEditor-borderStyleDashed\": \"dashed\",\r\n        \"dxHtmlEditor-borderStyleSolid\": \"solid\",\r\n        \"dxHtmlEditor-borderStyleDouble\": \"double\",\r\n        \"dxHtmlEditor-borderStyleGroove\": \"groove\",\r\n        \"dxHtmlEditor-borderStyleRidge\": \"ridge\",\r\n        \"dxHtmlEditor-borderStyleInset\": \"inset\",\r\n        \"dxHtmlEditor-borderStyleOutset\": \"outset\",\r\n        \"dxHtmlEditor-aiDialogTitle\": \"AI Assistant\",\r\n        \"dxHtmlEditor-aiDialogError\": \"Something went wrong. Please try again.\",\r\n        \"dxHtmlEditor-aiDialogCanceled\": \"Generation canceled\",\r\n        \"dxHtmlEditor-aiReplace\": \"Replace\",\r\n        \"dxHtmlEditor-aiInsertAbove\": \"Insert above\",\r\n        \"dxHtmlEditor-aiInsertBelow\": \"Insert below\",\r\n        \"dxHtmlEditor-aiCopy\": \"Copy\",\r\n        \"dxHtmlEditor-aiRegenerate\": \"Regenerate\",\r\n        \"dxHtmlEditor-aiGenerate\": \"Generate\",\r\n        \"dxHtmlEditor-aiCancel\": \"Cancel\",\r\n        \"dxHtmlEditor-aiToolbarItemAriaLabel\": \"AI Assistant toolbar item\",\r\n        \"dxHtmlEditor-aiResultTextAreaAriaLabel\": \"AI Assistant result\",\r\n        \"dxHtmlEditor-aiAskPlaceholder\": \"Ask AI to modify text\",\r\n        \"dxFileManager-newDirectoryName\": \"Untitled directory\",\r\n        \"dxFileManager-rootDirectoryName\": \"Files\",\r\n        \"dxFileManager-errorNoAccess\": \"Access Denied. Operation could not be completed.\",\r\n        \"dxFileManager-errorDirectoryExistsFormat\": \"Directory '{0}' already exists.\",\r\n        \"dxFileManager-errorFileExistsFormat\": \"File '{0}' already exists.\",\r\n        \"dxFileManager-errorFileNotFoundFormat\": \"File '{0}' not found.\",\r\n        \"dxFileManager-errorDirectoryNotFoundFormat\": \"Directory '{0}' not found.\",\r\n        \"dxFileManager-errorWrongFileExtension\": \"File extension is not allowed.\",\r\n        \"dxFileManager-errorMaxFileSizeExceeded\": \"File size exceeds the maximum allowed size.\",\r\n        \"dxFileManager-errorInvalidSymbols\": \"This name contains invalid characters.\",\r\n        \"dxFileManager-errorDefault\": \"Unspecified error.\",\r\n        \"dxFileManager-errorDirectoryOpenFailed\": \"The directory cannot be opened\",\r\n        \"dxFileManager-commandCreate\": \"New directory\",\r\n        \"dxFileManager-commandRename\": \"Rename\",\r\n        \"dxFileManager-commandMove\": \"Move to\",\r\n        \"dxFileManager-commandCopy\": \"Copy to\",\r\n        \"dxFileManager-commandDelete\": \"Delete\",\r\n        \"dxFileManager-commandDownload\": \"Download\",\r\n        \"dxFileManager-commandUpload\": \"Upload files\",\r\n        \"dxFileManager-commandRefresh\": \"Refresh\",\r\n        \"dxFileManager-commandThumbnails\": \"Thumbnails View\",\r\n        \"dxFileManager-commandDetails\": \"Details View\",\r\n        \"dxFileManager-commandClearSelection\": \"Clear selection\",\r\n        \"dxFileManager-commandShowNavPane\": \"Toggle navigation pane\",\r\n        \"dxFileManager-dialogDirectoryChooserMoveTitle\": \"Move to\",\r\n        \"dxFileManager-dialogDirectoryChooserMoveButtonText\": \"Move\",\r\n        \"dxFileManager-dialogDirectoryChooserCopyTitle\": \"Copy to\",\r\n        \"dxFileManager-dialogDirectoryChooserCopyButtonText\": \"Copy\",\r\n        \"dxFileManager-dialogRenameItemTitle\": \"Rename\",\r\n        \"dxFileManager-dialogRenameItemButtonText\": \"Save\",\r\n        \"dxFileManager-dialogCreateDirectoryTitle\": \"New directory\",\r\n        \"dxFileManager-dialogCreateDirectoryButtonText\": \"Create\",\r\n        \"dxFileManager-dialogDeleteItemTitle\": \"Delete\",\r\n        \"dxFileManager-dialogDeleteItemButtonText\": \"Delete\",\r\n        \"dxFileManager-dialogDeleteItemSingleItemConfirmation\": \"Are you sure you want to delete {0}?\",\r\n        \"dxFileManager-dialogDeleteItemMultipleItemsConfirmation\": \"Are you sure you want to delete {0} items?\",\r\n        \"dxFileManager-dialogButtonCancel\": \"Cancel\",\r\n        \"dxFileManager-editingCreateSingleItemProcessingMessage\": \"Creating a directory inside {0}\",\r\n        \"dxFileManager-editingCreateSingleItemSuccessMessage\": \"Created a directory inside {0}\",\r\n        \"dxFileManager-editingCreateSingleItemErrorMessage\": \"Directory was not created\",\r\n        \"dxFileManager-editingCreateCommonErrorMessage\": \"Directory was not created\",\r\n        \"dxFileManager-editingRenameSingleItemProcessingMessage\": \"Renaming an item inside {0}\",\r\n        \"dxFileManager-editingRenameSingleItemSuccessMessage\": \"Renamed an item inside {0}\",\r\n        \"dxFileManager-editingRenameSingleItemErrorMessage\": \"Item was not renamed\",\r\n        \"dxFileManager-editingRenameCommonErrorMessage\": \"Item was not renamed\",\r\n        \"dxFileManager-editingDeleteSingleItemProcessingMessage\": \"Deleting an item from {0}\",\r\n        \"dxFileManager-editingDeleteMultipleItemsProcessingMessage\": \"Deleting {0} items from {1}\",\r\n        \"dxFileManager-editingDeleteSingleItemSuccessMessage\": \"Deleted an item from {0}\",\r\n        \"dxFileManager-editingDeleteMultipleItemsSuccessMessage\": \"Deleted {0} items from {1}\",\r\n        \"dxFileManager-editingDeleteSingleItemErrorMessage\": \"Item was not deleted\",\r\n        \"dxFileManager-editingDeleteMultipleItemsErrorMessage\": \"{0} items were not deleted\",\r\n        \"dxFileManager-editingDeleteCommonErrorMessage\": \"Some items were not deleted\",\r\n        \"dxFileManager-editingMoveSingleItemProcessingMessage\": \"Moving an item to {0}\",\r\n        \"dxFileManager-editingMoveMultipleItemsProcessingMessage\": \"Moving {0} items to {1}\",\r\n        \"dxFileManager-editingMoveSingleItemSuccessMessage\": \"Moved an item to {0}\",\r\n        \"dxFileManager-editingMoveMultipleItemsSuccessMessage\": \"Moved {0} items to {1}\",\r\n        \"dxFileManager-editingMoveSingleItemErrorMessage\": \"Item was not moved\",\r\n        \"dxFileManager-editingMoveMultipleItemsErrorMessage\": \"{0} items were not moved\",\r\n        \"dxFileManager-editingMoveCommonErrorMessage\": \"Some items were not moved\",\r\n        \"dxFileManager-editingCopySingleItemProcessingMessage\": \"Copying an item to {0}\",\r\n        \"dxFileManager-editingCopyMultipleItemsProcessingMessage\": \"Copying {0} items to {1}\",\r\n        \"dxFileManager-editingCopySingleItemSuccessMessage\": \"Copied an item to {0}\",\r\n        \"dxFileManager-editingCopyMultipleItemsSuccessMessage\": \"Copied {0} items to {1}\",\r\n        \"dxFileManager-editingCopySingleItemErrorMessage\": \"Item was not copied\",\r\n        \"dxFileManager-editingCopyMultipleItemsErrorMessage\": \"{0} items were not copied\",\r\n        \"dxFileManager-editingCopyCommonErrorMessage\": \"Some items were not copied\",\r\n        \"dxFileManager-editingUploadSingleItemProcessingMessage\": \"Uploading an item to {0}\",\r\n        \"dxFileManager-editingUploadMultipleItemsProcessingMessage\": \"Uploading {0} items to {1}\",\r\n        \"dxFileManager-editingUploadSingleItemSuccessMessage\": \"Uploaded an item to {0}\",\r\n        \"dxFileManager-editingUploadMultipleItemsSuccessMessage\": \"Uploaded {0} items to {1}\",\r\n        \"dxFileManager-editingUploadSingleItemErrorMessage\": \"Item was not uploaded\",\r\n        \"dxFileManager-editingUploadMultipleItemsErrorMessage\": \"{0} items were not uploaded\",\r\n        \"dxFileManager-editingUploadCanceledMessage\": \"Canceled\",\r\n        \"dxFileManager-editingDownloadSingleItemErrorMessage\": \"Item was not downloaded\",\r\n        \"dxFileManager-editingDownloadMultipleItemsErrorMessage\": \"{0} items were not downloaded\",\r\n        \"dxFileManager-listDetailsColumnCaptionName\": \"Name\",\r\n        \"dxFileManager-listDetailsColumnCaptionDateModified\": \"Date Modified\",\r\n        \"dxFileManager-listDetailsColumnCaptionFileSize\": \"File Size\",\r\n        \"dxFileManager-listThumbnailsTooltipTextSize\": \"Size\",\r\n        \"dxFileManager-listThumbnailsTooltipTextDateModified\": \"Date Modified\",\r\n        \"dxFileManager-notificationProgressPanelTitle\": \"Progress\",\r\n        \"dxFileManager-notificationProgressPanelEmptyListText\": \"No operations\",\r\n        \"dxFileManager-notificationProgressPanelOperationCanceled\": \"Canceled\",\r\n        \"dxDiagram-categoryGeneral\": \"General\",\r\n        \"dxDiagram-categoryFlowchart\": \"Flowchart\",\r\n        \"dxDiagram-categoryOrgChart\": \"Org Chart\",\r\n        \"dxDiagram-categoryContainers\": \"Containers\",\r\n        \"dxDiagram-categoryCustom\": \"Custom\",\r\n        \"dxDiagram-commandExportToSvg\": \"Export to SVG\",\r\n        \"dxDiagram-commandExportToPng\": \"Export to PNG\",\r\n        \"dxDiagram-commandExportToJpg\": \"Export to JPEG\",\r\n        \"dxDiagram-commandUndo\": \"Undo\",\r\n        \"dxDiagram-commandRedo\": \"Redo\",\r\n        \"dxDiagram-commandFontName\": \"Font Name\",\r\n        \"dxDiagram-commandFontSize\": \"Font Size\",\r\n        \"dxDiagram-commandBold\": \"Bold\",\r\n        \"dxDiagram-commandItalic\": \"Italic\",\r\n        \"dxDiagram-commandUnderline\": \"Underline\",\r\n        \"dxDiagram-commandTextColor\": \"Font Color\",\r\n        \"dxDiagram-commandLineColor\": \"Line Color\",\r\n        \"dxDiagram-commandLineWidth\": \"Line Width\",\r\n        \"dxDiagram-commandLineStyle\": \"Line Style\",\r\n        \"dxDiagram-commandLineStyleSolid\": \"Solid\",\r\n        \"dxDiagram-commandLineStyleDotted\": \"Dotted\",\r\n        \"dxDiagram-commandLineStyleDashed\": \"Dashed\",\r\n        \"dxDiagram-commandFillColor\": \"Fill Color\",\r\n        \"dxDiagram-commandAlignLeft\": \"Align Left\",\r\n        \"dxDiagram-commandAlignCenter\": \"Align Center\",\r\n        \"dxDiagram-commandAlignRight\": \"Align Right\",\r\n        \"dxDiagram-commandConnectorLineType\": \"Connector Line Type\",\r\n        \"dxDiagram-commandConnectorLineStraight\": \"Straight\",\r\n        \"dxDiagram-commandConnectorLineOrthogonal\": \"Orthogonal\",\r\n        \"dxDiagram-commandConnectorLineStart\": \"Connector Line Start\",\r\n        \"dxDiagram-commandConnectorLineEnd\": \"Connector Line End\",\r\n        \"dxDiagram-commandConnectorLineNone\": \"None\",\r\n        \"dxDiagram-commandConnectorLineArrow\": \"Arrow\",\r\n        \"dxDiagram-commandFullscreen\": \"Full Screen\",\r\n        \"dxDiagram-commandUnits\": \"Units\",\r\n        \"dxDiagram-commandPageSize\": \"Page Size\",\r\n        \"dxDiagram-commandPageOrientation\": \"Page Orientation\",\r\n        \"dxDiagram-commandPageOrientationLandscape\": \"Landscape\",\r\n        \"dxDiagram-commandPageOrientationPortrait\": \"Portrait\",\r\n        \"dxDiagram-commandPageColor\": \"Page Color\",\r\n        \"dxDiagram-commandShowGrid\": \"Show Grid\",\r\n        \"dxDiagram-commandSnapToGrid\": \"Snap to Grid\",\r\n        \"dxDiagram-commandGridSize\": \"Grid Size\",\r\n        \"dxDiagram-commandZoomLevel\": \"Zoom Level\",\r\n        \"dxDiagram-commandAutoZoom\": \"Auto Zoom\",\r\n        \"dxDiagram-commandFitToContent\": \"Fit to Content\",\r\n        \"dxDiagram-commandFitToWidth\": \"Fit to Width\",\r\n        \"dxDiagram-commandAutoZoomByContent\": \"Auto Zoom by Content\",\r\n        \"dxDiagram-commandAutoZoomByWidth\": \"Auto Zoom by Width\",\r\n        \"dxDiagram-commandSimpleView\": \"Simple View\",\r\n        \"dxDiagram-commandCut\": \"Cut\",\r\n        \"dxDiagram-commandCopy\": \"Copy\",\r\n        \"dxDiagram-commandPaste\": \"Paste\",\r\n        \"dxDiagram-commandSelectAll\": \"Select All\",\r\n        \"dxDiagram-commandDelete\": \"Delete\",\r\n        \"dxDiagram-commandBringToFront\": \"Bring to Front\",\r\n        \"dxDiagram-commandSendToBack\": \"Send to Back\",\r\n        \"dxDiagram-commandLock\": \"Lock\",\r\n        \"dxDiagram-commandUnlock\": \"Unlock\",\r\n        \"dxDiagram-commandInsertShapeImage\": \"Insert Image...\",\r\n        \"dxDiagram-commandEditShapeImage\": \"Change Image...\",\r\n        \"dxDiagram-commandDeleteShapeImage\": \"Delete Image\",\r\n        \"dxDiagram-commandLayoutLeftToRight\": \"Left-to-right\",\r\n        \"dxDiagram-commandLayoutRightToLeft\": \"Right-to-left\",\r\n        \"dxDiagram-commandLayoutTopToBottom\": \"Top-to-bottom\",\r\n        \"dxDiagram-commandLayoutBottomToTop\": \"Bottom-to-top\",\r\n        \"dxDiagram-unitIn\": \"in\",\r\n        \"dxDiagram-unitCm\": \"cm\",\r\n        \"dxDiagram-unitPx\": \"px\",\r\n        \"dxDiagram-dialogButtonOK\": \"OK\",\r\n        \"dxDiagram-dialogButtonCancel\": \"Cancel\",\r\n        \"dxDiagram-dialogInsertShapeImageTitle\": \"Insert Image\",\r\n        \"dxDiagram-dialogEditShapeImageTitle\": \"Change Image\",\r\n        \"dxDiagram-dialogEditShapeImageSelectButton\": \"Select image\",\r\n        \"dxDiagram-dialogEditShapeImageLabelText\": \"or drop a file here\",\r\n        \"dxDiagram-uiExport\": \"Export\",\r\n        \"dxDiagram-uiProperties\": \"Properties\",\r\n        \"dxDiagram-uiSettings\": \"Settings\",\r\n        \"dxDiagram-uiShowToolbox\": \"Show Toolbox\",\r\n        \"dxDiagram-uiSearch\": \"Search\",\r\n        \"dxDiagram-uiStyle\": \"Style\",\r\n        \"dxDiagram-uiLayout\": \"Layout\",\r\n        \"dxDiagram-uiLayoutTree\": \"Tree\",\r\n        \"dxDiagram-uiLayoutLayered\": \"Layered\",\r\n        \"dxDiagram-uiDiagram\": \"Diagram\",\r\n        \"dxDiagram-uiText\": \"Text\",\r\n        \"dxDiagram-uiObject\": \"Object\",\r\n        \"dxDiagram-uiConnector\": \"Connector\",\r\n        \"dxDiagram-uiPage\": \"Page\",\r\n        \"dxDiagram-shapeText\": \"Text\",\r\n        \"dxDiagram-shapeRectangle\": \"Rectangle\",\r\n        \"dxDiagram-shapeEllipse\": \"Ellipse\",\r\n        \"dxDiagram-shapeCross\": \"Cross\",\r\n        \"dxDiagram-shapeTriangle\": \"Triangle\",\r\n        \"dxDiagram-shapeDiamond\": \"Diamond\",\r\n        \"dxDiagram-shapeHeart\": \"Heart\",\r\n        \"dxDiagram-shapePentagon\": \"Pentagon\",\r\n        \"dxDiagram-shapeHexagon\": \"Hexagon\",\r\n        \"dxDiagram-shapeOctagon\": \"Octagon\",\r\n        \"dxDiagram-shapeStar\": \"Star\",\r\n        \"dxDiagram-shapeArrowLeft\": \"Left Arrow\",\r\n        \"dxDiagram-shapeArrowUp\": \"Up Arrow\",\r\n        \"dxDiagram-shapeArrowRight\": \"Right Arrow\",\r\n        \"dxDiagram-shapeArrowDown\": \"Down Arrow\",\r\n        \"dxDiagram-shapeArrowUpDown\": \"Up Down Arrow\",\r\n        \"dxDiagram-shapeArrowLeftRight\": \"Left Right Arrow\",\r\n        \"dxDiagram-shapeProcess\": \"Process\",\r\n        \"dxDiagram-shapeDecision\": \"Decision\",\r\n        \"dxDiagram-shapeTerminator\": \"Terminator\",\r\n        \"dxDiagram-shapePredefinedProcess\": \"Predefined Process\",\r\n        \"dxDiagram-shapeDocument\": \"Document\",\r\n        \"dxDiagram-shapeMultipleDocuments\": \"Multiple Documents\",\r\n        \"dxDiagram-shapeManualInput\": \"Manual Input\",\r\n        \"dxDiagram-shapePreparation\": \"Preparation\",\r\n        \"dxDiagram-shapeData\": \"Data\",\r\n        \"dxDiagram-shapeDatabase\": \"Database\",\r\n        \"dxDiagram-shapeHardDisk\": \"Hard Disk\",\r\n        \"dxDiagram-shapeInternalStorage\": \"Internal Storage\",\r\n        \"dxDiagram-shapePaperTape\": \"Paper Tape\",\r\n        \"dxDiagram-shapeManualOperation\": \"Manual Operation\",\r\n        \"dxDiagram-shapeDelay\": \"Delay\",\r\n        \"dxDiagram-shapeStoredData\": \"Stored Data\",\r\n        \"dxDiagram-shapeDisplay\": \"Display\",\r\n        \"dxDiagram-shapeMerge\": \"Merge\",\r\n        \"dxDiagram-shapeConnector\": \"Connector\",\r\n        \"dxDiagram-shapeOr\": \"Or\",\r\n        \"dxDiagram-shapeSummingJunction\": \"Summing Junction\",\r\n        \"dxDiagram-shapeContainerDefaultText\": \"Container\",\r\n        \"dxDiagram-shapeVerticalContainer\": \"Vertical Container\",\r\n        \"dxDiagram-shapeHorizontalContainer\": \"Horizontal Container\",\r\n        \"dxDiagram-shapeCardDefaultText\": \"Person's Name\",\r\n        \"dxDiagram-shapeCardWithImageOnLeft\": \"Card with Image on the Left\",\r\n        \"dxDiagram-shapeCardWithImageOnTop\": \"Card with Image on the Top\",\r\n        \"dxDiagram-shapeCardWithImageOnRight\": \"Card with Image on the Right\",\r\n        \"dxGantt-dialogTitle\": \"Title\",\r\n        \"dxGantt-dialogStartTitle\": \"Start\",\r\n        \"dxGantt-dialogEndTitle\": \"End\",\r\n        \"dxGantt-dialogProgressTitle\": \"Progress\",\r\n        \"dxGantt-dialogResourcesTitle\": \"Resources\",\r\n        \"dxGantt-dialogResourceManagerTitle\": \"Resource Manager\",\r\n        \"dxGantt-dialogTaskDetailsTitle\": \"Task Details\",\r\n        \"dxGantt-dialogEditResourceListHint\": \"Edit Resource List\",\r\n        \"dxGantt-dialogEditNoResources\": \"No resources\",\r\n        \"dxGantt-dialogButtonAdd\": \"Add\",\r\n        \"dxGantt-contextMenuNewTask\": \"New Task\",\r\n        \"dxGantt-contextMenuNewSubtask\": \"New Subtask\",\r\n        \"dxGantt-contextMenuDeleteTask\": \"Delete Task\",\r\n        \"dxGantt-contextMenuDeleteDependency\": \"Delete Dependency\",\r\n        \"dxGantt-dialogTaskDeleteConfirmation\": \"Deleting a task also deletes all its dependencies and subtasks. Are you sure you want to delete this task?\",\r\n        \"dxGantt-dialogDependencyDeleteConfirmation\": \"Are you sure you want to delete the dependency from the task?\",\r\n        \"dxGantt-dialogResourcesDeleteConfirmation\": \"Deleting a resource also deletes it from tasks to which this resource is assigned. Are you sure you want to delete these resources? Resources: {0}\",\r\n        \"dxGantt-dialogConstraintCriticalViolationMessage\": \"The task you are attempting to move is linked to a second task by a dependency relation. This change would conflict with dependency rules. How would you like to proceed?\",\r\n        \"dxGantt-dialogConstraintViolationMessage\": \"The task you are attempting to move is linked to a second task by a dependency relation. How would you like to proceed?\",\r\n        \"dxGantt-dialogCancelOperationMessage\": \"Cancel the operation\",\r\n        \"dxGantt-dialogDeleteDependencyMessage\": \"Delete the dependency\",\r\n        \"dxGantt-dialogMoveTaskAndKeepDependencyMessage\": \"Move the task and keep the dependency\",\r\n        \"dxGantt-dialogConstraintCriticalViolationSeveralTasksMessage\": \"The task you are attempting to move is linked to another tasks by dependency relations. This change would conflict with dependency rules. How would you like to proceed?\",\r\n        \"dxGantt-dialogConstraintViolationSeveralTasksMessage\": \"The task you are attempting to move is linked to another tasks by dependency relations. How would you like to proceed?\",\r\n        \"dxGantt-dialogDeleteDependenciesMessage\": \"Delete the dependency relations\",\r\n        \"dxGantt-dialogMoveTaskAndKeepDependenciesMessage\": \"Move the task and keep the dependencies\",\r\n        \"dxGantt-undo\": \"Undo\",\r\n        \"dxGantt-redo\": \"Redo\",\r\n        \"dxGantt-expandAll\": \"Expand All\",\r\n        \"dxGantt-collapseAll\": \"Collapse All\",\r\n        \"dxGantt-addNewTask\": \"Add New Task\",\r\n        \"dxGantt-deleteSelectedTask\": \"Delete Selected Task\",\r\n        \"dxGantt-zoomIn\": \"Zoom In\",\r\n        \"dxGantt-zoomOut\": \"Zoom Out\",\r\n        \"dxGantt-fullScreen\": \"Full Screen\",\r\n        \"dxGantt-quarter\": \"Q{0}\",\r\n        \"dxGantt-sortingAscendingText\": \"Sort Ascending\",\r\n        \"dxGantt-sortingDescendingText\": \"Sort Descending\",\r\n        \"dxGantt-sortingClearText\": \"Clear Sorting\",\r\n        \"dxGantt-showResources\": \"Show Resources\",\r\n        \"dxGantt-showDependencies\": \"Show Dependencies\",\r\n        \"dxGantt-dialogStartDateValidation\": \"Start date must be after {0}\",\r\n        \"dxGantt-dialogEndDateValidation\": \"End date must be after {0}\",\r\n        \"dxGallery-itemName\": \"Gallery item\",\r\n        \"dxMultiView-elementAriaRoleDescription\": \"MultiView\",\r\n        \"dxMultiView-elementAriaLabel\": \"Use the arrow keys or swipe to navigate between views\",\r\n        \"dxMultiView-itemAriaRoleDescription\": \"View\",\r\n        \"dxMultiView-itemAriaLabel\": \"{0} of {1}\",\r\n        \"dxSplitter-resizeHandleAriaLabel\": \"Split bar\",\r\n        \"dxSplitter-resizeHandleAriaRoleDescription\": \"Separator\",\r\n        \"dxStepper-optionalMark\": \"(Optional)\"\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GACD,uCAAuC;;;;AAChC,MAAM,kBAAkB;IAC3B,IAAI;QACA,KAAK;QACL,IAAI;QACJ,QAAQ;QACR,YAAY;QACZ,OAAO;QACP,OAAO;QACP,MAAM;QACN,SAAS;QACT,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,IAAI;QACJ,OAAO;QACP,WAAW;QACX,iCAAiC;QACjC,gCAAgC;QAChC,uBAAuB;QACvB,iCAAiC;QACjC,sBAAsB;QACtB,gCAAgC;QAChC,oBAAoB;QACpB,8BAA8B;QAC9B,2BAA2B;QAC3B,qCAAqC;QACrC,qBAAqB;QACrB,+BAA+B;QAC/B,oBAAoB;QACpB,8BAA8B;QAC9B,sBAAsB;QACtB,gCAAgC;QAChC,sBAAsB;QACtB,gCAAgC;QAChC,oBAAoB;QACpB,8BAA8B;QAC9B,mBAAmB;QACnB,8BAA8B;QAC9B,0BAA0B;QAC1B,yBAAyB;QACzB,yBAAyB;QACzB,0BAA0B;QAC1B,yBAAyB;QACzB,oBAAoB;QACpB,wBAAwB;QACxB,kCAAkC;QAClC,8BAA8B;QAC9B,4BAA4B;QAC5B,kCAAkC;QAClC,4BAA4B;QAC5B,+BAA+B;QAC/B,8BAA8B;QAC9B,oCAAoC;QACpC,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,gCAAgC;QAChC,0CAA0C;QAC1C,0CAA0C;QAC1C,8CAA8C;QAC9C,iCAAiC;QACjC,0CAA0C;QAC1C,wCAAwC;QACxC,6CAA6C;QAC7C,2CAA2C;QAC3C,iCAAiC;QACjC,+BAA+B;QAC/B,6BAA6B;QAC7B,2BAA2B;QAC3B,wBAAwB;QACxB,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,2BAA2B;QAC3B,gCAAgC;QAChC,uCAAuC;QACvC,sCAAsC;QACtC,uCAAuC;QACvC,qCAAqC;QACrC,qCAAqC;QACrC,0BAA0B;QAC1B,0BAA0B;QAC1B,2BAA2B;QAC3B,4BAA4B;QAC5B,uBAAuB;QACvB,0BAA0B;QAC1B,mCAAmC;QACnC,0BAA0B;QAC1B,+CAA+C;QAC/C,4CAA4C;QAC5C,4DAA4D;QAC5D,2CAA2C;QAC3C,iCAAiC;QACjC,qCAAqC;QACrC,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,gCAAgC;QAChC,6BAA6B;QAC7B,6BAA6B;QAC7B,oCAAoC;QACpC,sCAAsC;QACtC,+BAA+B;QAC/B,iCAAiC;QACjC,0CAA0C;QAC1C,sCAAsC;QACtC,kCAAkC;QAClC,yBAAyB;QACzB,qCAAqC;QACrC,mCAAmC;QACnC,0CAA0C;QAC1C,uCAAuC;QACvC,0CAA0C;QAC1C,qCAAqC;QACrC,6CAA6C;QAC7C,wCAAwC;QACxC,gDAAgD;QAChD,2CAA2C;QAC3C,yCAAyC;QACzC,4CAA4C;QAC5C,yCAAyC;QACzC,wCAAwC;QACxC,iDAAiD;QACjD,+CAA+C;QAC/C,4BAA4B;QAC5B,8BAA8B;QAC9B,uBAAuB;QACvB,wBAAwB;QACxB,mCAAmC;QACnC,oCAAoC;QACpC,+BAA+B;QAC/B,kCAAkC;QAClC,wCAAwC;QACxC,yCAAyC;QACzC,4BAA4B;QAC5B,oCAAoC;QACpC,sCAAsC;QACtC,4BAA4B;QAC5B,yBAAyB;QACzB,oCAAoC;QACpC,yBAAyB;QACzB,oCAAoC;QACpC,yBAAyB;QACzB,oCAAoC;QACpC,yBAAyB;QACzB,oCAAoC;QACpC,2BAA2B;QAC3B,8BAA8B;QAC9B,gCAAgC;QAChC,uCAAuC;QACvC,wCAAwC;QACxC,yCAAyC;QACzC,uBAAuB;QACvB,4BAA4B;QAC5B,wBAAwB;QACxB,0BAA0B;QAC1B,2BAA2B;QAC3B,iCAAiC;QACjC,wBAAwB;QACxB,gCAAgC;QAChC,yCAAyC;QACzC,qCAAqC;QACrC,6BAA6B;QAC7B,iCAAiC;QACjC,mCAAmC;QACnC,iCAAiC;QACjC,yBAAyB;QACzB,+BAA+B;QAC/B,wBAAwB;QACxB,wBAAwB;QACxB,+BAA+B;QAC/B,6BAA6B;QAC7B,2BAA2B;QAC3B,+BAA+B;QAC/B,8BAA8B;QAC9B,+BAA+B;QAC/B,yBAAyB;QACzB,+BAA+B;QAC/B,8BAA8B;QAC9B,2BAA2B;QAC3B,+BAA+B;QAC/B,4BAA4B;QAC5B,4BAA4B;QAC5B,0BAA0B;QAC1B,2BAA2B;QAC3B,sCAAsC;QACtC,sCAAsC;QACtC,qCAAqC;QACrC,2CAA2C;QAC3C,2BAA2B;QAC3B,mCAAmC;QACnC,kCAAkC;QAClC,2BAA2B;QAC3B,wCAAwC;QACxC,+BAA+B;QAC/B,0BAA0B;QAC1B,kCAAkC;QAClC,oBAAoB;QACpB,0BAA0B;QAC1B,oBAAoB;QACpB,4BAA4B;QAC5B,gBAAgB;QAChB,oBAAoB;QACpB,oBAAoB;QACpB,qBAAqB;QACrB,wBAAwB;QACxB,0BAA0B;QAC1B,yBAAyB;QACzB,+BAA+B;QAC/B,yBAAyB;QACzB,iCAAiC;QACjC,qBAAqB;QACrB,yBAAyB;QACzB,yBAAyB;QACzB,0BAA0B;QAC1B,6BAA6B;QAC7B,+BAA+B;QAC/B,0BAA0B;QAC1B,qBAAqB;QACrB,iCAAiC;QACjC,gCAAgC;QAChC,yBAAyB;QACzB,2BAA2B;QAC3B,mCAAmC;QACnC,gCAAgC;QAChC,gCAAgC;QAChC,gCAAgC;QAChC,yBAAyB;QACzB,4BAA4B;QAC5B,0BAA0B;QAC1B,4BAA4B;QAC5B,yBAAyB;QACzB,+BAA+B;QAC/B,6BAA6B;QAC7B,4BAA4B;QAC5B,+BAA+B;QAC/B,yBAAyB;QACzB,yBAAyB;QACzB,kDAAkD;QAClD,sDAAsD;QACtD,0CAA0C;QAC1C,8CAA8C;QAC9C,wCAAwC;QACxC,gCAAgC;QAChC,oCAAoC;QACpC,kCAAkC;QAClC,sCAAsC;QACtC,qCAAqC;QACrC,+BAA+B;QAC/B,kCAAkC;QAClC,8BAA8B;QAC9B,+BAA+B;QAC/B,+BAA+B;QAC/B,kCAAkC;QAClC,gCAAgC;QAChC,+BAA+B;QAC/B,gCAAgC;QAChC,iCAAiC;QACjC,gCAAgC;QAChC,qCAAqC;QACrC,kCAAkC;QAClC,6BAA6B;QAC7B,+BAA+B;QAC/B,4BAA4B;QAC5B,wCAAwC;QACxC,yCAAyC;QACzC,wCAAwC;QACxC,sCAAsC;QACtC,qCAAqC;QACrC,sCAAsC;QACtC,uCAAuC;QACvC,sCAAsC;QACtC,2BAA2B;QAC3B,4BAA4B;QAC5B,gCAAgC;QAChC,6BAA6B;QAC7B,8BAA8B;QAC9B,mCAAmC;QACnC,oCAAoC;QACpC,wCAAwC;QACxC,qCAAqC;QACrC,sCAAsC;QACtC,qCAAqC;QACrC,sBAAsB;QACtB,4BAA4B;QAC5B,0CAA0C;QAC1C,4CAA4C;QAC5C,4CAA4C;QAC5C,8CAA8C;QAC9C,2CAA2C;QAC3C,6CAA6C;QAC7C,+CAA+C;QAC/C,iDAAiD;QACjD,+BAA+B;QAC/B,gCAAgC;QAChC,yBAAyB;QACzB,2BAA2B;QAC3B,0BAA0B;QAC1B,+BAA+B;QAC/B,8BAA8B;QAC9B,6BAA6B;QAC7B,uCAAuC;QACvC,sCAAsC;QACtC,wCAAwC;QACxC,yCAAyC;QACzC,mCAAmC;QACnC,kCAAkC;QAClC,oCAAoC;QACpC,qCAAqC;QACrC,gCAAgC;QAChC,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,2BAA2B;QAC3B,4BAA4B;QAC5B,gCAAgC;QAChC,wCAAwC;QACxC,qCAAqC;QACrC,4BAA4B;QAC5B,+BAA+B;QAC/B,kCAAkC;QAClC,kDAAkD;QAClD,mDAAmD;QACnD,8CAA8C;QAC9C,6CAA6C;QAC7C,6BAA6B;QAC7B,2BAA2B;QAC3B,uBAAuB;QACvB,+BAA+B;QAC/B,+BAA+B;QAC/B,oCAAoC;QACpC,uCAAuC;QACvC,wBAAwB;QACxB,6BAA6B;QAC7B,mCAAmC;QACnC,qCAAqC;QACrC,mCAAmC;QACnC,gDAAgD;QAChD,4BAA4B;QAC5B,2BAA2B;QAC3B,8BAA8B;QAC9B,8BAA8B;QAC9B,uCAAuC;QACvC,gCAAgC;QAChC,0BAA0B;QAC1B,+BAA+B;QAC/B,6BAA6B;QAC7B,2BAA2B;QAC3B,0BAA0B;QAC1B,kCAAkC;QAClC,gCAAgC;QAChC,kCAAkC;QAClC,qCAAqC;QACrC,4BAA4B;QAC5B,6BAA6B;QAC7B,+BAA+B;QAC/B,mCAAmC;QACnC,6BAA6B;QAC7B,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QACzB,wBAAwB;QACxB,yBAAyB;QACzB,uBAAuB;QACvB,qBAAqB;QACrB,wBAAwB;QACxB,yBAAyB;QACzB,+BAA+B;QAC/B,gCAAgC;QAChC,gCAAgC;QAChC,2BAA2B;QAC3B,8BAA8B;QAC9B,uBAAuB;QACvB,sBAAsB;QACtB,0BAA0B;QAC1B,yBAAyB;QACzB,gCAAgC;QAChC,4BAA4B;QAC5B,kCAAkC;QAClC,yCAAyC;QACzC,4CAA4C;QAC5C,uCAAuC;QACvC,+CAA+C;QAC/C,0CAA0C;QAC1C,kDAAkD;QAClD,6CAA6C;QAC7C,2CAA2C;QAC3C,8CAA8C;QAC9C,2CAA2C;QAC3C,0CAA0C;QAC1C,6CAA6C;QAC7C,0CAA0C;QAC1C,wCAAwC;QACxC,yCAAyC;QACzC,yCAAyC;QACzC,wCAAwC;QACxC,uCAAuC;QACvC,6CAA6C;QAC7C,uCAAuC;QACvC,0CAA0C;QAC1C,uCAAuC;QACvC,2CAA2C;QAC3C,uCAAuC;QACvC,mCAAmC;QACnC,wCAAwC;QACxC,kCAAkC;QAClC,mCAAmC;QACnC,oCAAoC;QACpC,sCAAsC;QACtC,mCAAmC;QACnC,oCAAoC;QACpC,oCAAoC;QACpC,sCAAsC;QACtC,uCAAuC;QACvC,2CAA2C;QAC3C,8CAA8C;QAC9C,yCAAyC;QACzC,yCAAyC;QACzC,wCAAwC;QACxC,qCAAqC;QACrC,sCAAsC;QACtC,sCAAsC;QACtC,2CAA2C;QAC3C,0CAA0C;QAC1C,wBAAwB;QACxB,2BAA2B;QAC3B,2BAA2B;QAC3B,qBAAqB;QACrB,sBAAsB;QACtB,qBAAqB;QACrB,uBAAuB;QACvB,qBAAqB;QACrB,sBAAsB;QACtB,qBAAqB;QACrB,uBAAuB;QACvB,0BAA0B;QAC1B,4BAA4B;QAC5B,0BAA0B;QAC1B,2BAA2B;QAC3B,uBAAuB;QACvB,+BAA+B;QAC/B,+BAA+B;QAC/B,4BAA4B;QAC5B,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,2BAA2B;QAC3B,6BAA6B;QAC7B,0BAA0B;QAC1B,yBAAyB;QACzB,qBAAqB;QACrB,qBAAqB;QACrB,sBAAsB;QACtB,4BAA4B;QAC5B,gCAAgC;QAChC,+BAA+B;QAC/B,+BAA+B;QAC/B,iCAAiC;QACjC,kCAAkC;QAClC,6BAA6B;QAC7B,0BAA0B;QAC1B,4BAA4B;QAC5B,+BAA+B;QAC/B,gCAAgC;QAChC,uBAAuB;QACvB,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,sBAAsB;QACtB,uBAAuB;QACvB,4BAA4B;QAC5B,4BAA4B;QAC5B,gCAAgC;QAChC,2BAA2B;QAC3B,0BAA0B;QAC1B,2BAA2B;QAC3B,yBAAyB;QACzB,gCAAgC;QAChC,kCAAkC;QAClC,uBAAuB;QACvB,qBAAqB;QACrB,wBAAwB;QACxB,uBAAuB;QACvB,sBAAsB;QACtB,uBAAuB;QACvB,qBAAqB;QACrB,sBAAsB;QACtB,uBAAuB;QACvB,wBAAwB;QACxB,gCAAgC;QAChC,kCAAkC;QAClC,kCAAkC;QAClC,kCAAkC;QAClC,iCAAiC;QACjC,kCAAkC;QAClC,kCAAkC;QAClC,iCAAiC;QACjC,iCAAiC;QACjC,kCAAkC;QAClC,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,0BAA0B;QAC1B,8BAA8B;QAC9B,8BAA8B;QAC9B,uBAAuB;QACvB,6BAA6B;QAC7B,2BAA2B;QAC3B,yBAAyB;QACzB,uCAAuC;QACvC,0CAA0C;QAC1C,iCAAiC;QACjC,kCAAkC;QAClC,mCAAmC;QACnC,+BAA+B;QAC/B,4CAA4C;QAC5C,uCAAuC;QACvC,yCAAyC;QACzC,8CAA8C;QAC9C,yCAAyC;QACzC,0CAA0C;QAC1C,qCAAqC;QACrC,8BAA8B;QAC9B,0CAA0C;QAC1C,+BAA+B;QAC/B,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,+BAA+B;QAC/B,iCAAiC;QACjC,+BAA+B;QAC/B,gCAAgC;QAChC,mCAAmC;QACnC,gCAAgC;QAChC,uCAAuC;QACvC,oCAAoC;QACpC,iDAAiD;QACjD,sDAAsD;QACtD,iDAAiD;QACjD,sDAAsD;QACtD,uCAAuC;QACvC,4CAA4C;QAC5C,4CAA4C;QAC5C,iDAAiD;QACjD,uCAAuC;QACvC,4CAA4C;QAC5C,wDAAwD;QACxD,2DAA2D;QAC3D,oCAAoC;QACpC,0DAA0D;QAC1D,uDAAuD;QACvD,qDAAqD;QACrD,iDAAiD;QACjD,0DAA0D;QAC1D,uDAAuD;QACvD,qDAAqD;QACrD,iDAAiD;QACjD,0DAA0D;QAC1D,6DAA6D;QAC7D,uDAAuD;QACvD,0DAA0D;QAC1D,qDAAqD;QACrD,wDAAwD;QACxD,iDAAiD;QACjD,wDAAwD;QACxD,2DAA2D;QAC3D,qDAAqD;QACrD,wDAAwD;QACxD,mDAAmD;QACnD,sDAAsD;QACtD,+CAA+C;QAC/C,wDAAwD;QACxD,2DAA2D;QAC3D,qDAAqD;QACrD,wDAAwD;QACxD,mDAAmD;QACnD,sDAAsD;QACtD,+CAA+C;QAC/C,0DAA0D;QAC1D,6DAA6D;QAC7D,uDAAuD;QACvD,0DAA0D;QAC1D,qDAAqD;QACrD,wDAAwD;QACxD,8CAA8C;QAC9C,uDAAuD;QACvD,0DAA0D;QAC1D,8CAA8C;QAC9C,sDAAsD;QACtD,kDAAkD;QAClD,+CAA+C;QAC/C,uDAAuD;QACvD,gDAAgD;QAChD,wDAAwD;QACxD,4DAA4D;QAC5D,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,gCAAgC;QAChC,4BAA4B;QAC5B,gCAAgC;QAChC,gCAAgC;QAChC,gCAAgC;QAChC,yBAAyB;QACzB,yBAAyB;QACzB,6BAA6B;QAC7B,6BAA6B;QAC7B,yBAAyB;QACzB,2BAA2B;QAC3B,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,8BAA8B;QAC9B,mCAAmC;QACnC,oCAAoC;QACpC,oCAAoC;QACpC,8BAA8B;QAC9B,8BAA8B;QAC9B,gCAAgC;QAChC,+BAA+B;QAC/B,sCAAsC;QACtC,0CAA0C;QAC1C,4CAA4C;QAC5C,uCAAuC;QACvC,qCAAqC;QACrC,sCAAsC;QACtC,uCAAuC;QACvC,+BAA+B;QAC/B,0BAA0B;QAC1B,6BAA6B;QAC7B,oCAAoC;QACpC,6CAA6C;QAC7C,4CAA4C;QAC5C,8BAA8B;QAC9B,6BAA6B;QAC7B,+BAA+B;QAC/B,6BAA6B;QAC7B,8BAA8B;QAC9B,6BAA6B;QAC7B,iCAAiC;QACjC,+BAA+B;QAC/B,sCAAsC;QACtC,oCAAoC;QACpC,+BAA+B;QAC/B,wBAAwB;QACxB,yBAAyB;QACzB,0BAA0B;QAC1B,8BAA8B;QAC9B,2BAA2B;QAC3B,iCAAiC;QACjC,+BAA+B;QAC/B,yBAAyB;QACzB,2BAA2B;QAC3B,qCAAqC;QACrC,mCAAmC;QACnC,qCAAqC;QACrC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,sCAAsC;QACtC,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,4BAA4B;QAC5B,gCAAgC;QAChC,yCAAyC;QACzC,uCAAuC;QACvC,8CAA8C;QAC9C,2CAA2C;QAC3C,sBAAsB;QACtB,0BAA0B;QAC1B,wBAAwB;QACxB,2BAA2B;QAC3B,sBAAsB;QACtB,qBAAqB;QACrB,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,yBAAyB;QACzB,oBAAoB;QACpB,uBAAuB;QACvB,4BAA4B;QAC5B,0BAA0B;QAC1B,wBAAwB;QACxB,2BAA2B;QAC3B,0BAA0B;QAC1B,wBAAwB;QACxB,2BAA2B;QAC3B,0BAA0B;QAC1B,0BAA0B;QAC1B,uBAAuB;QACvB,4BAA4B;QAC5B,0BAA0B;QAC1B,6BAA6B;QAC7B,4BAA4B;QAC5B,8BAA8B;QAC9B,iCAAiC;QACjC,0BAA0B;QAC1B,2BAA2B;QAC3B,6BAA6B;QAC7B,oCAAoC;QACpC,2BAA2B;QAC3B,oCAAoC;QACpC,8BAA8B;QAC9B,8BAA8B;QAC9B,uBAAuB;QACvB,2BAA2B;QAC3B,2BAA2B;QAC3B,kCAAkC;QAClC,4BAA4B;QAC5B,kCAAkC;QAClC,wBAAwB;QACxB,6BAA6B;QAC7B,0BAA0B;QAC1B,wBAAwB;QACxB,4BAA4B;QAC5B,qBAAqB;QACrB,kCAAkC;QAClC,uCAAuC;QACvC,oCAAoC;QACpC,sCAAsC;QACtC,kCAAkC;QAClC,sCAAsC;QACtC,qCAAqC;QACrC,uCAAuC;QACvC,uBAAuB;QACvB,4BAA4B;QAC5B,0BAA0B;QAC1B,+BAA+B;QAC/B,gCAAgC;QAChC,sCAAsC;QACtC,kCAAkC;QAClC,sCAAsC;QACtC,iCAAiC;QACjC,2BAA2B;QAC3B,8BAA8B;QAC9B,iCAAiC;QACjC,iCAAiC;QACjC,uCAAuC;QACvC,wCAAwC;QACxC,8CAA8C;QAC9C,6CAA6C;QAC7C,oDAAoD;QACpD,4CAA4C;QAC5C,wCAAwC;QACxC,yCAAyC;QACzC,kDAAkD;QAClD,gEAAgE;QAChE,wDAAwD;QACxD,2CAA2C;QAC3C,oDAAoD;QACpD,gBAAgB;QAChB,gBAAgB;QAChB,qBAAqB;QACrB,uBAAuB;QACvB,sBAAsB;QACtB,8BAA8B;QAC9B,kBAAkB;QAClB,mBAAmB;QACnB,sBAAsB;QACtB,mBAAmB;QACnB,gCAAgC;QAChC,iCAAiC;QACjC,4BAA4B;QAC5B,yBAAyB;QACzB,4BAA4B;QAC5B,qCAAqC;QACrC,mCAAmC;QACnC,sBAAsB;QACtB,0CAA0C;QAC1C,gCAAgC;QAChC,uCAAuC;QACvC,6BAA6B;QAC7B,oCAAoC;QACpC,8CAA8C;QAC9C,0BAA0B;IAC9B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1212, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/message.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/message.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport dependencyInjector from \"../../../core/utils/dependency_injector\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    format as stringFormat\r\n} from \"../../../core/utils/string\";\r\nimport {\r\n    humanize\r\n} from \"../../../core/utils/inflector\";\r\nimport coreLocalization from \"./core\";\r\nimport {\r\n    defaultMessages\r\n} from \"./default_messages\";\r\nconst baseDictionary = extend(true, {}, defaultMessages);\r\nconst getDataByLocale = (localeData, locale) => {\r\n    var _Object$entries$find;\r\n    return localeData[locale] || (null === locale || void 0 === locale ? void 0 : locale.toLowerCase) && (null === (_Object$entries$find = Object.entries(localeData).find((_ref => {\r\n        let [key] = _ref;\r\n        return key.toLowerCase() === locale.toLowerCase()\r\n    }))) || void 0 === _Object$entries$find ? void 0 : _Object$entries$find[1]) || {}\r\n};\r\nconst newMessages = {};\r\nconst messageLocalization = dependencyInjector({\r\n    engine: function() {\r\n        return \"base\"\r\n    },\r\n    _dictionary: baseDictionary,\r\n    load: function(messages) {\r\n        extend(true, this._dictionary, messages)\r\n    },\r\n    _localizablePrefix: \"@\",\r\n    setup: function(localizablePrefix) {\r\n        this._localizablePrefix = localizablePrefix\r\n    },\r\n    localizeString: function(text) {\r\n        const that = this;\r\n        const regex = new RegExp(\"(^|[^a-zA-Z_0-9\" + that._localizablePrefix + \"-]+)(\" + that._localizablePrefix + \"{1,2})([a-zA-Z_0-9-]+)\", \"g\");\r\n        const escapeString = that._localizablePrefix + that._localizablePrefix;\r\n        return text.replace(regex, ((str, prefix, escape, localizationKey) => {\r\n            const defaultResult = that._localizablePrefix + localizationKey;\r\n            let result;\r\n            if (escape !== escapeString) {\r\n                result = that.format(localizationKey)\r\n            }\r\n            if (!result) {\r\n                newMessages[localizationKey] = humanize(localizationKey)\r\n            }\r\n            return prefix + (result || defaultResult)\r\n        }))\r\n    },\r\n    getMessagesByLocales: function() {\r\n        return this._dictionary\r\n    },\r\n    getDictionary: function(onlyNew) {\r\n        if (onlyNew) {\r\n            return newMessages\r\n        }\r\n        return extend({}, newMessages, this.getMessagesByLocales()[coreLocalization.locale()])\r\n    },\r\n    getFormatter: function(key) {\r\n        return this._getFormatterBase(key) || this._getFormatterBase(key, \"en\")\r\n    },\r\n    _getFormatterBase: function(key, locale) {\r\n        const message = coreLocalization.getValueByClosestLocale((locale => getDataByLocale(this._dictionary, locale)[key]));\r\n        if (message) {\r\n            return function() {\r\n                const args = 1 === arguments.length && Array.isArray(arguments[0]) ? arguments[0].slice(0) : Array.prototype.slice.call(arguments, 0);\r\n                args.unshift(message);\r\n                return stringFormat.apply(this, args)\r\n            }\r\n        }\r\n    },\r\n    format: function(key) {\r\n        const formatter = this.getFormatter(key);\r\n        const values = Array.prototype.slice.call(arguments, 1);\r\n        return formatter && formatter.apply(this, values) || \"\"\r\n    }\r\n});\r\nexport default messageLocalization;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AACA;;;;;;;AAGA,MAAM,iBAAiB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,0LAAA,CAAA,kBAAe;AACvD,MAAM,kBAAkB,CAAC,YAAY;IACjC,IAAI;IACJ,OAAO,UAAU,CAAC,OAAO,IAAI,CAAC,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,WAAW,KAAK,CAAC,SAAS,CAAC,uBAAuB,OAAO,OAAO,CAAC,YAAY,IAAI,CAAE,CAAA;QACpK,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI,WAAW,OAAO,OAAO,WAAW;IACnD,EAAG,KAAK,KAAK,MAAM,uBAAuB,KAAK,IAAI,oBAAoB,CAAC,EAAE,KAAK,CAAC;AACpF;AACA,MAAM,cAAc,CAAC;AACrB,MAAM,sBAAsB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;IAC3C,QAAQ;QACJ,OAAO;IACX;IACA,aAAa;IACb,MAAM,SAAS,QAAQ;QACnB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE;IACnC;IACA,oBAAoB;IACpB,OAAO,SAAS,iBAAiB;QAC7B,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA,gBAAgB,SAAS,IAAI;QACzB,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,IAAI,OAAO,oBAAoB,KAAK,kBAAkB,GAAG,UAAU,KAAK,kBAAkB,GAAG,0BAA0B;QACrI,MAAM,eAAe,KAAK,kBAAkB,GAAG,KAAK,kBAAkB;QACtE,OAAO,KAAK,OAAO,CAAC,OAAQ,CAAC,KAAK,QAAQ,QAAQ;YAC9C,MAAM,gBAAgB,KAAK,kBAAkB,GAAG;YAChD,IAAI;YACJ,IAAI,WAAW,cAAc;gBACzB,SAAS,KAAK,MAAM,CAAC;YACzB;YACA,IAAI,CAAC,QAAQ;gBACT,WAAW,CAAC,gBAAgB,GAAG,CAAA,GAAA,qLAAA,CAAA,WAAQ,AAAD,EAAE;YAC5C;YACA,OAAO,SAAS,CAAC,UAAU,aAAa;QAC5C;IACJ;IACA,sBAAsB;QAClB,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,eAAe,SAAS,OAAO;QAC3B,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,aAAa,IAAI,CAAC,oBAAoB,EAAE,CAAC,8KAAA,CAAA,UAAgB,CAAC,MAAM,GAAG;IACzF;IACA,cAAc,SAAS,GAAG;QACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK;IACtE;IACA,mBAAmB,SAAS,GAAG,EAAE,MAAM;QACnC,MAAM,UAAU,8KAAA,CAAA,UAAgB,CAAC,uBAAuB,CAAE,CAAA,SAAU,gBAAgB,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,IAAI;QAClH,IAAI,SAAS;YACT,OAAO;gBACH,MAAM,OAAO,MAAM,UAAU,MAAM,IAAI,MAAM,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,SAAS,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;gBACnI,KAAK,OAAO,CAAC;gBACb,OAAO,kLAAA,CAAA,SAAY,CAAC,KAAK,CAAC,IAAI,EAAE;YACpC;QACJ;IACJ;IACA,QAAQ,SAAS,GAAG;QAChB,MAAM,YAAY,IAAI,CAAC,YAAY,CAAC;QACpC,MAAM,SAAS,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW;QACrD,OAAO,aAAa,UAAU,KAAK,CAAC,IAAI,EAAE,WAAW;IACzD;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1307, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/utils/index.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/utils/index.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../../__internal/events/utils/index\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1328, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/frame.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/frame.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    hasWindow,\r\n    getWindow\r\n} from \"../../../core/utils/window\";\r\nconst window = hasWindow() ? getWindow() : {};\r\nimport callOnce from \"../../../core/utils/call_once\";\r\nconst FRAME_ANIMATION_STEP_TIME = 1e3 / 60;\r\nlet request = function(callback) {\r\n    return setTimeout(callback, 16.666666666666668)\r\n};\r\nlet cancel = function(requestID) {\r\n    clearTimeout(requestID)\r\n};\r\nconst setAnimationFrameMethods = callOnce((function() {\r\n    const nativeRequest = window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || window.oRequestAnimationFrame || window.msRequestAnimationFrame;\r\n    const nativeCancel = window.cancelAnimationFrame || window.webkitCancelAnimationFrame || window.mozCancelAnimationFrame || window.oCancelAnimationFrame || window.msCancelAnimationFrame;\r\n    if (nativeRequest && nativeCancel) {\r\n        request = nativeRequest;\r\n        cancel = nativeCancel\r\n    }\r\n}));\r\nexport function requestAnimationFrame() {\r\n    setAnimationFrameMethods();\r\n    return request.apply(window, arguments)\r\n}\r\nexport function cancelAnimationFrame() {\r\n    setAnimationFrameMethods();\r\n    cancel.apply(window, arguments)\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAKA;;AADA,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,MAAM,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,MAAM,CAAC;;AAE5C,MAAM,4BAA4B,MAAM;AACxC,IAAI,UAAU,SAAS,QAAQ;IAC3B,OAAO,WAAW,UAAU;AAChC;AACA,IAAI,SAAS,SAAS,SAAS;IAC3B,aAAa;AACjB;AACA,MAAM,2BAA2B,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAG;IACvC,MAAM,gBAAgB,OAAO,qBAAqB,IAAI,OAAO,2BAA2B,IAAI,OAAO,wBAAwB,IAAI,OAAO,sBAAsB,IAAI,OAAO,uBAAuB;IAC9L,MAAM,eAAe,OAAO,oBAAoB,IAAI,OAAO,0BAA0B,IAAI,OAAO,uBAAuB,IAAI,OAAO,qBAAqB,IAAI,OAAO,sBAAsB;IACxL,IAAI,iBAAiB,cAAc;QAC/B,UAAU;QACV,SAAS;IACb;AACJ;AACO,SAAS;IACZ;IACA,OAAO,QAAQ,KAAK,CAAC,QAAQ;AACjC;AACO,SAAS;IACZ;IACA,OAAO,KAAK,CAAC,QAAQ;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1372, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/translator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/translator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    data as elementData,\r\n    removeData\r\n} from \"../../../core/element_data\";\r\nimport {\r\n    type\r\n} from \"../../../core/utils/type\";\r\nconst TRANSLATOR_DATA_KEY = \"dxTranslator\";\r\nconst TRANSFORM_MATRIX_REGEX = /matrix(3d)?\\((.+?)\\)/;\r\nconst TRANSLATE_REGEX = /translate(?:3d)?\\((.+?)\\)/;\r\nexport const locate = function($element) {\r\n    $element = $($element);\r\n    const translate = getTranslate($element);\r\n    return {\r\n        left: translate.x,\r\n        top: translate.y\r\n    }\r\n};\r\n\r\nfunction isPercentValue(value) {\r\n    return \"string\" === type(value) && \"%\" === value[value.length - 1]\r\n}\r\n\r\nfunction cacheTranslate($element, translate) {\r\n    if ($element.length) {\r\n        elementData($element.get(0), \"dxTranslator\", translate)\r\n    }\r\n}\r\nexport const clearCache = function($element) {\r\n    if ($element.length) {\r\n        removeData($element.get(0), \"dxTranslator\")\r\n    }\r\n};\r\nexport const getTranslateCss = function(translate) {\r\n    translate.x = translate.x || 0;\r\n    translate.y = translate.y || 0;\r\n    const xValueString = isPercentValue(translate.x) ? translate.x : translate.x + \"px\";\r\n    const yValueString = isPercentValue(translate.y) ? translate.y : translate.y + \"px\";\r\n    return \"translate(\" + xValueString + \", \" + yValueString + \")\"\r\n};\r\nexport const getTranslate = function($element) {\r\n    let result = $element.length ? elementData($element.get(0), \"dxTranslator\") : null;\r\n    if (!result) {\r\n        const transformValue = $element.css(\"transform\") || getTranslateCss({\r\n            x: 0,\r\n            y: 0\r\n        });\r\n        let matrix = transformValue.match(TRANSFORM_MATRIX_REGEX);\r\n        const is3D = matrix && matrix[1];\r\n        if (matrix) {\r\n            matrix = matrix[2].split(\",\");\r\n            if (\"3d\" === is3D) {\r\n                matrix = matrix.slice(12, 15)\r\n            } else {\r\n                matrix.push(0);\r\n                matrix = matrix.slice(4, 7)\r\n            }\r\n        } else {\r\n            matrix = [0, 0, 0]\r\n        }\r\n        result = {\r\n            x: parseFloat(matrix[0]),\r\n            y: parseFloat(matrix[1]),\r\n            z: parseFloat(matrix[2])\r\n        };\r\n        cacheTranslate($element, result)\r\n    }\r\n    return result\r\n};\r\nexport const move = function($element, position) {\r\n    $element = $($element);\r\n    const left = position.left;\r\n    const top = position.top;\r\n    let translate;\r\n    if (void 0 === left) {\r\n        translate = getTranslate($element);\r\n        translate.y = top || 0\r\n    } else if (void 0 === top) {\r\n        translate = getTranslate($element);\r\n        translate.x = left || 0\r\n    } else {\r\n        translate = {\r\n            x: left || 0,\r\n            y: top || 0,\r\n            z: 0\r\n        };\r\n        cacheTranslate($element, translate)\r\n    }\r\n    $element.css({\r\n        transform: getTranslateCss(translate)\r\n    });\r\n    if (isPercentValue(left) || isPercentValue(top)) {\r\n        clearCache($element)\r\n    }\r\n};\r\nexport const resetPosition = function($element, finishTransition) {\r\n    $element = $($element);\r\n    let originalTransition;\r\n    const stylesConfig = {\r\n        left: 0,\r\n        top: 0,\r\n        transform: \"none\"\r\n    };\r\n    if (finishTransition) {\r\n        originalTransition = $element.css(\"transition\");\r\n        stylesConfig.transition = \"none\"\r\n    }\r\n    $element.css(stylesConfig);\r\n    clearCache($element);\r\n    if (finishTransition) {\r\n        $element.get(0).offsetHeight;\r\n        $element.css(\"transition\", originalTransition)\r\n    }\r\n};\r\nexport const parseTranslate = function(translateString) {\r\n    let result = translateString.match(TRANSLATE_REGEX);\r\n    if (!result || !result[1]) {\r\n        return\r\n    }\r\n    result = result[1].split(\",\");\r\n    result = {\r\n        x: parseFloat(result[0]),\r\n        y: parseFloat(result[1]),\r\n        z: parseFloat(result[2])\r\n    };\r\n    return result\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;AACD;AACA;AAAA;AAIA;AAAA;;;;AAGA,MAAM,sBAAsB;AAC5B,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB;AACjB,MAAM,SAAS,SAAS,QAAQ;IACnC,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACb,MAAM,YAAY,aAAa;IAC/B,OAAO;QACH,MAAM,UAAU,CAAC;QACjB,KAAK,UAAU,CAAC;IACpB;AACJ;AAEA,SAAS,eAAe,KAAK;IACzB,OAAO,aAAa,CAAA,GAAA,gLAAA,CAAA,OAAI,AAAD,EAAE,UAAU,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;AACtE;AAEA,SAAS,eAAe,QAAQ,EAAE,SAAS;IACvC,IAAI,SAAS,MAAM,EAAE;QACjB,CAAA,GAAA,+KAAA,CAAA,OAAW,AAAD,EAAE,SAAS,GAAG,CAAC,IAAI,gBAAgB;IACjD;AACJ;AACO,MAAM,aAAa,SAAS,QAAQ;IACvC,IAAI,SAAS,MAAM,EAAE;QACjB,CAAA,GAAA,+KAAA,CAAA,aAAU,AAAD,EAAE,SAAS,GAAG,CAAC,IAAI;IAChC;AACJ;AACO,MAAM,kBAAkB,SAAS,SAAS;IAC7C,UAAU,CAAC,GAAG,UAAU,CAAC,IAAI;IAC7B,UAAU,CAAC,GAAG,UAAU,CAAC,IAAI;IAC7B,MAAM,eAAe,eAAe,UAAU,CAAC,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG;IAC/E,MAAM,eAAe,eAAe,UAAU,CAAC,IAAI,UAAU,CAAC,GAAG,UAAU,CAAC,GAAG;IAC/E,OAAO,eAAe,eAAe,OAAO,eAAe;AAC/D;AACO,MAAM,eAAe,SAAS,QAAQ;IACzC,IAAI,SAAS,SAAS,MAAM,GAAG,CAAA,GAAA,+KAAA,CAAA,OAAW,AAAD,EAAE,SAAS,GAAG,CAAC,IAAI,kBAAkB;IAC9E,IAAI,CAAC,QAAQ;QACT,MAAM,iBAAiB,SAAS,GAAG,CAAC,gBAAgB,gBAAgB;YAChE,GAAG;YACH,GAAG;QACP;QACA,IAAI,SAAS,eAAe,KAAK,CAAC;QAClC,MAAM,OAAO,UAAU,MAAM,CAAC,EAAE;QAChC,IAAI,QAAQ;YACR,SAAS,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;YACzB,IAAI,SAAS,MAAM;gBACf,SAAS,OAAO,KAAK,CAAC,IAAI;YAC9B,OAAO;gBACH,OAAO,IAAI,CAAC;gBACZ,SAAS,OAAO,KAAK,CAAC,GAAG;YAC7B;QACJ,OAAO;YACH,SAAS;gBAAC;gBAAG;gBAAG;aAAE;QACtB;QACA,SAAS;YACL,GAAG,WAAW,MAAM,CAAC,EAAE;YACvB,GAAG,WAAW,MAAM,CAAC,EAAE;YACvB,GAAG,WAAW,MAAM,CAAC,EAAE;QAC3B;QACA,eAAe,UAAU;IAC7B;IACA,OAAO;AACX;AACO,MAAM,OAAO,SAAS,QAAQ,EAAE,QAAQ;IAC3C,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACb,MAAM,OAAO,SAAS,IAAI;IAC1B,MAAM,MAAM,SAAS,GAAG;IACxB,IAAI;IACJ,IAAI,KAAK,MAAM,MAAM;QACjB,YAAY,aAAa;QACzB,UAAU,CAAC,GAAG,OAAO;IACzB,OAAO,IAAI,KAAK,MAAM,KAAK;QACvB,YAAY,aAAa;QACzB,UAAU,CAAC,GAAG,QAAQ;IAC1B,OAAO;QACH,YAAY;YACR,GAAG,QAAQ;YACX,GAAG,OAAO;YACV,GAAG;QACP;QACA,eAAe,UAAU;IAC7B;IACA,SAAS,GAAG,CAAC;QACT,WAAW,gBAAgB;IAC/B;IACA,IAAI,eAAe,SAAS,eAAe,MAAM;QAC7C,WAAW;IACf;AACJ;AACO,MAAM,gBAAgB,SAAS,QAAQ,EAAE,gBAAgB;IAC5D,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACb,IAAI;IACJ,MAAM,eAAe;QACjB,MAAM;QACN,KAAK;QACL,WAAW;IACf;IACA,IAAI,kBAAkB;QAClB,qBAAqB,SAAS,GAAG,CAAC;QAClC,aAAa,UAAU,GAAG;IAC9B;IACA,SAAS,GAAG,CAAC;IACb,WAAW;IACX,IAAI,kBAAkB;QAClB,SAAS,GAAG,CAAC,GAAG,YAAY;QAC5B,SAAS,GAAG,CAAC,cAAc;IAC/B;AACJ;AACO,MAAM,iBAAiB,SAAS,eAAe;IAClD,IAAI,SAAS,gBAAgB,KAAK,CAAC;IACnC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,EAAE,EAAE;QACvB;IACJ;IACA,SAAS,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC;IACzB,SAAS;QACL,GAAG,WAAW,MAAM,CAAC,EAAE;QACvB,GAAG,WAAW,MAAM,CAAC,EAAE;QACvB,GAAG,WAAW,MAAM,CAAC,EAAE;IAC3B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1522, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/easing.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/easing.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isFunction\r\n} from \"../../../core/utils/type\";\r\nconst CSS_TRANSITION_EASING_REGEX = /cubic-bezier\\((\\d+(?:\\.\\d+)?)\\s*,\\s*(\\d+(?:\\.\\d+)?)\\s*,\\s*(\\d+(?:\\.\\d+)?)\\s*,\\s*(\\d+(?:\\.\\d+)?)\\)/;\r\nconst TransitionTimingFuncMap = {\r\n    linear: \"cubic-bezier(0, 0, 1, 1)\",\r\n    swing: \"cubic-bezier(0.445, 0.05, 0.55, 0.95)\",\r\n    ease: \"cubic-bezier(0.25, 0.1, 0.25, 1)\",\r\n    \"ease-in\": \"cubic-bezier(0.42, 0, 1, 1)\",\r\n    \"ease-out\": \"cubic-bezier(0, 0, 0.58, 1)\",\r\n    \"ease-in-out\": \"cubic-bezier(0.42, 0, 0.58, 1)\"\r\n};\r\nconst polynomBezier = function(x1, y1, x2, y2) {\r\n    const Cx = 3 * x1;\r\n    const Bx = 3 * (x2 - x1) - Cx;\r\n    const Ax = 1 - Cx - Bx;\r\n    const Cy = 3 * y1;\r\n    const By = 3 * (y2 - y1) - Cy;\r\n    const Ay = 1 - Cy - By;\r\n    const bezierX = function(t) {\r\n        return t * (Cx + t * (Bx + t * Ax))\r\n    };\r\n    const derivativeX = function(t) {\r\n        return Cx + t * (2 * Bx + 3 * t * Ax)\r\n    };\r\n    return function(t) {\r\n        return function(t) {\r\n            return t * (Cy + t * (By + t * Ay))\r\n        }(function(t) {\r\n            let x = t;\r\n            let i = 0;\r\n            let z;\r\n            while (i < 14) {\r\n                z = bezierX(x) - t;\r\n                if (Math.abs(z) < .001) {\r\n                    break\r\n                }\r\n                x -= z / derivativeX(x);\r\n                i++\r\n            }\r\n            return x\r\n        }(t))\r\n    }\r\n};\r\nlet easing = {};\r\nexport const convertTransitionTimingFuncToEasing = function(cssTransitionEasing) {\r\n    cssTransitionEasing = TransitionTimingFuncMap[cssTransitionEasing] || cssTransitionEasing;\r\n    let coeffs = cssTransitionEasing.match(CSS_TRANSITION_EASING_REGEX);\r\n    let forceName;\r\n    if (!coeffs) {\r\n        forceName = \"linear\";\r\n        coeffs = TransitionTimingFuncMap[forceName].match(CSS_TRANSITION_EASING_REGEX)\r\n    }\r\n    coeffs = coeffs.slice(1, 5);\r\n    for (let i = 0; i < coeffs.length; i++) {\r\n        coeffs[i] = parseFloat(coeffs[i])\r\n    }\r\n    const easingName = forceName || \"cubicbezier_\" + coeffs.join(\"_\").replace(/\\./g, \"p\");\r\n    if (!isFunction(easing[easingName])) {\r\n        easing[easingName] = function(x, t, b, c, d) {\r\n            return c * polynomBezier(coeffs[0], coeffs[1], coeffs[2], coeffs[3])(t / d) + b\r\n        }\r\n    }\r\n    return easingName\r\n};\r\nexport function setEasing(value) {\r\n    easing = value\r\n}\r\nexport function getEasing(name) {\r\n    return easing[name]\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;AAAA;;AAGA,MAAM,8BAA8B;AACpC,MAAM,0BAA0B;IAC5B,QAAQ;IACR,OAAO;IACP,MAAM;IACN,WAAW;IACX,YAAY;IACZ,eAAe;AACnB;AACA,MAAM,gBAAgB,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IACzC,MAAM,KAAK,IAAI;IACf,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI;IAC3B,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,KAAK,IAAI;IACf,MAAM,KAAK,IAAI,CAAC,KAAK,EAAE,IAAI;IAC3B,MAAM,KAAK,IAAI,KAAK;IACpB,MAAM,UAAU,SAAS,CAAC;QACtB,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;IACtC;IACA,MAAM,cAAc,SAAS,CAAC;QAC1B,OAAO,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,IAAI,EAAE;IACxC;IACA,OAAO,SAAS,CAAC;QACb,OAAO,SAAS,CAAC;YACb,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,CAAC;QACtC,EAAE,SAAS,CAAC;YACR,IAAI,IAAI;YACR,IAAI,IAAI;YACR,IAAI;YACJ,MAAO,IAAI,GAAI;gBACX,IAAI,QAAQ,KAAK;gBACjB,IAAI,KAAK,GAAG,CAAC,KAAK,MAAM;oBACpB;gBACJ;gBACA,KAAK,IAAI,YAAY;gBACrB;YACJ;YACA,OAAO;QACX,EAAE;IACN;AACJ;AACA,IAAI,SAAS,CAAC;AACP,MAAM,sCAAsC,SAAS,mBAAmB;IAC3E,sBAAsB,uBAAuB,CAAC,oBAAoB,IAAI;IACtE,IAAI,SAAS,oBAAoB,KAAK,CAAC;IACvC,IAAI;IACJ,IAAI,CAAC,QAAQ;QACT,YAAY;QACZ,SAAS,uBAAuB,CAAC,UAAU,CAAC,KAAK,CAAC;IACtD;IACA,SAAS,OAAO,KAAK,CAAC,GAAG;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,MAAM,CAAC,EAAE,GAAG,WAAW,MAAM,CAAC,EAAE;IACpC;IACA,MAAM,aAAa,aAAa,iBAAiB,OAAO,IAAI,CAAC,KAAK,OAAO,CAAC,OAAO;IACjF,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,MAAM,CAAC,WAAW,GAAG;QACjC,MAAM,CAAC,WAAW,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;YACvC,OAAO,IAAI,cAAc,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,MAAM,CAAC,EAAE,EAAE,IAAI,KAAK;QAClF;IACJ;IACA,OAAO;AACX;AACO,SAAS,UAAU,KAAK;IAC3B,SAAS;AACb;AACO,SAAS,UAAU,IAAI;IAC1B,OAAO,MAAM,CAAC,KAAK;AACvB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1609, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment/devices.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment/devices.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../__internal/core/m_devices\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1630, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment/hide_callback.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment/hide_callback.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport const hideCallback = function() {\r\n    let callbacks = [];\r\n    return {\r\n        add: function(callback) {\r\n            if (!callbacks.includes(callback)) {\r\n                callbacks.push(callback)\r\n            }\r\n        },\r\n        remove: function(callback) {\r\n            const indexOfCallback = callbacks.indexOf(callback);\r\n            if (-1 !== indexOfCallback) {\r\n                callbacks.splice(indexOfCallback, 1)\r\n            }\r\n        },\r\n        fire: function() {\r\n            const callback = callbacks.pop();\r\n            const result = !!callback;\r\n            if (result) {\r\n                callback()\r\n            }\r\n            return result\r\n        },\r\n        hasCallback: function() {\r\n            return callbacks.length > 0\r\n        }\r\n    }\r\n}();\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM,eAAe;IACxB,IAAI,YAAY,EAAE;IAClB,OAAO;QACH,KAAK,SAAS,QAAQ;YAClB,IAAI,CAAC,UAAU,QAAQ,CAAC,WAAW;gBAC/B,UAAU,IAAI,CAAC;YACnB;QACJ;QACA,QAAQ,SAAS,QAAQ;YACrB,MAAM,kBAAkB,UAAU,OAAO,CAAC;YAC1C,IAAI,CAAC,MAAM,iBAAiB;gBACxB,UAAU,MAAM,CAAC,iBAAiB;YACtC;QACJ;QACA,MAAM;YACF,MAAM,WAAW,UAAU,GAAG;YAC9B,MAAM,SAAS,CAAC,CAAC;YACjB,IAAI,QAAQ;gBACR;YACJ;YACA,OAAO;QACX;QACA,aAAa;YACT,OAAO,UAAU,MAAM,GAAG;QAC9B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1671, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment/hide_top_overlay.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment/hide_top_overlay.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    hideCallback\r\n} from \"./hide_callback\";\r\nexport default function() {\r\n    return hideCallback.fire()\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AAGe;IACX,OAAO,sLAAA,CAAA,eAAY,CAAC,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1690, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport/init_mobile_viewport.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment/init_mobile_viewport/init_mobile_viewport.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getWidth,\r\n    setWidth\r\n} from \"../../../../core/utils/size\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    getWindow\r\n} from \"../../../../core/utils/window\";\r\nconst window = getWindow();\r\nimport eventsEngine from \"../../events/core/events_engine\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport resizeCallbacks from \"../../../../core/utils/resize_callbacks\";\r\nimport {\r\n    styleProp\r\n} from \"../../../../core/utils/style\";\r\nimport devices from \"../../../../__internal/core/m_devices\";\r\nimport domAdapter from \"../../../../__internal/core/m_dom_adapter\";\r\nimport supportUtils from \"../../../../__internal/core/utils/m_support\";\r\nexport const initMobileViewport = function(options) {\r\n    options = extend({}, options);\r\n    let realDevice = devices.real();\r\n    const allowZoom = options.allowZoom;\r\n    const allowPan = options.allowPan;\r\n    const allowSelection = \"allowSelection\" in options ? options.allowSelection : \"generic\" === realDevice.platform;\r\n    if (!$(\"meta[name=viewport]\").length) {\r\n        $(\"<meta>\").attr(\"name\", \"viewport\").appendTo(\"head\")\r\n    }\r\n    const metaVerbs = [\"width=device-width\"];\r\n    const msTouchVerbs = [];\r\n    if (allowZoom) {\r\n        msTouchVerbs.push(\"pinch-zoom\")\r\n    } else {\r\n        metaVerbs.push(\"initial-scale=1.0\", \"maximum-scale=1.0, user-scalable=no\")\r\n    }\r\n    if (allowPan) {\r\n        msTouchVerbs.push(\"pan-x\", \"pan-y\")\r\n    }\r\n    if (!allowPan && !allowZoom) {\r\n        $(\"html, body\").css({\r\n            msContentZooming: \"none\",\r\n            msUserSelect: \"none\",\r\n            overflow: \"hidden\"\r\n        })\r\n    } else {\r\n        $(\"html\").css(\"msOverflowStyle\", \"-ms-autohiding-scrollbar\")\r\n    }\r\n    if (!allowSelection && supportUtils.supportProp(\"userSelect\")) {\r\n        $(\".dx-viewport\").css(styleProp(\"userSelect\"), \"none\")\r\n    }\r\n    $(\"meta[name=viewport]\").attr(\"content\", metaVerbs.join());\r\n    $(\"html\").css(\"msTouchAction\", msTouchVerbs.join(\" \") || \"none\");\r\n    realDevice = devices.real();\r\n    if (supportUtils.touch) {\r\n        eventsEngine.off(domAdapter.getDocument(), \".dxInitMobileViewport\");\r\n        eventsEngine.on(domAdapter.getDocument(), \"dxpointermove.dxInitMobileViewport\", (function(e) {\r\n            const count = e.pointers.length;\r\n            const isTouchEvent = \"touch\" === e.pointerType;\r\n            const zoomDisabled = !allowZoom && count > 1;\r\n            const panDisabled = !allowPan && 1 === count && !e.isScrollingEvent;\r\n            if (isTouchEvent && (zoomDisabled || panDisabled)) {\r\n                e.preventDefault()\r\n            }\r\n        }))\r\n    }\r\n    if (realDevice.ios) {\r\n        const isPhoneGap = \"file:\" === domAdapter.getLocation().protocol;\r\n        if (!isPhoneGap) {\r\n            resizeCallbacks.add((function() {\r\n                const windowWidth = getWidth(window);\r\n                setWidth($(\"body\"), windowWidth)\r\n            }))\r\n        }\r\n    }\r\n    if (realDevice.android) {\r\n        resizeCallbacks.add((function() {\r\n            setTimeout((function() {\r\n                const activeElement = domAdapter.getActiveElement();\r\n                activeElement.scrollIntoViewIfNeeded ? activeElement.scrollIntoViewIfNeeded() : activeElement.scrollIntoView(false)\r\n            }))\r\n        }))\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAIA;AACA;AAAA;AAIA;AAAA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;AACA;AACA;AAAA;;;;AAXA,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD;;;;;;;;AAYhB,MAAM,qBAAqB,SAAS,OAAO;IAC9C,UAAU,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;IACrB,IAAI,aAAa,0KAAA,CAAA,UAAO,CAAC,IAAI;IAC7B,MAAM,YAAY,QAAQ,SAAS;IACnC,MAAM,WAAW,QAAQ,QAAQ;IACjC,MAAM,iBAAiB,oBAAoB,UAAU,QAAQ,cAAc,GAAG,cAAc,WAAW,QAAQ;IAC/G,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,uBAAuB,MAAM,EAAE;QAClC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,IAAI,CAAC,QAAQ,YAAY,QAAQ,CAAC;IAClD;IACA,MAAM,YAAY;QAAC;KAAqB;IACxC,MAAM,eAAe,EAAE;IACvB,IAAI,WAAW;QACX,aAAa,IAAI,CAAC;IACtB,OAAO;QACH,UAAU,IAAI,CAAC,qBAAqB;IACxC;IACA,IAAI,UAAU;QACV,aAAa,IAAI,CAAC,SAAS;IAC/B;IACA,IAAI,CAAC,YAAY,CAAC,WAAW;QACzB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,cAAc,GAAG,CAAC;YAChB,kBAAkB;YAClB,cAAc;YACd,UAAU;QACd;IACJ,OAAO;QACH,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,GAAG,CAAC,mBAAmB;IACrC;IACA,IAAI,CAAC,kBAAkB,mMAAA,CAAA,UAAY,CAAC,WAAW,CAAC,eAAe;QAC3D,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,gBAAgB,GAAG,CAAC,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACnD;IACA,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,uBAAuB,IAAI,CAAC,WAAW,UAAU,IAAI;IACvD,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,GAAG,CAAC,iBAAiB,aAAa,IAAI,CAAC,QAAQ;IACzD,aAAa,0KAAA,CAAA,UAAO,CAAC,IAAI;IACzB,IAAI,mMAAA,CAAA,UAAY,CAAC,KAAK,EAAE;QACpB,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,8KAAA,CAAA,UAAU,CAAC,WAAW,IAAI;QAC3C,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,8KAAA,CAAA,UAAU,CAAC,WAAW,IAAI,sCAAuC,SAAS,CAAC;YACvF,MAAM,QAAQ,EAAE,QAAQ,CAAC,MAAM;YAC/B,MAAM,eAAe,YAAY,EAAE,WAAW;YAC9C,MAAM,eAAe,CAAC,aAAa,QAAQ;YAC3C,MAAM,cAAc,CAAC,YAAY,MAAM,SAAS,CAAC,EAAE,gBAAgB;YACnE,IAAI,gBAAgB,CAAC,gBAAgB,WAAW,GAAG;gBAC/C,EAAE,cAAc;YACpB;QACJ;IACJ;IACA,IAAI,WAAW,GAAG,EAAE;QAChB,MAAM,aAAa,YAAY,8KAAA,CAAA,UAAU,CAAC,WAAW,GAAG,QAAQ;QAChE,IAAI,CAAC,YAAY;YACb,yKAAA,CAAA,UAAe,CAAC,GAAG,CAAE;gBACjB,MAAM,cAAc,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;gBAC7B,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS;YACxB;QACJ;IACJ;IACA,IAAI,WAAW,OAAO,EAAE;QACpB,yKAAA,CAAA,UAAe,CAAC,GAAG,CAAE;YACjB,WAAY;gBACR,MAAM,gBAAgB,8KAAA,CAAA,UAAU,CAAC,gBAAgB;gBACjD,cAAc,sBAAsB,GAAG,cAAc,sBAAsB,KAAK,cAAc,cAAc,CAAC;YACjH;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1797, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment/init_mobile_viewport.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment/init_mobile_viewport.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    initMobileViewport\r\n} from \"./init_mobile_viewport/init_mobile_viewport\";\r\nexport default initMobileViewport;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe,qNAAA,CAAA,qBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1814, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/default_date_names.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/default_date_names.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    map\r\n} from \"../../../core/utils/iterator\";\r\nconst MONTHS = [\"January\", \"February\", \"March\", \"April\", \"May\", \"June\", \"July\", \"August\", \"September\", \"October\", \"November\", \"December\"];\r\nconst DAYS = [\"Sunday\", \"Monday\", \"Tuesday\", \"Wednesday\", \"Thursday\", \"Friday\", \"Saturday\"];\r\nconst PERIODS = [\"AM\", \"PM\"];\r\nconst QUARTERS = [\"Q1\", \"Q2\", \"Q3\", \"Q4\"];\r\nconst cutCaptions = (captions, format) => {\r\n    const lengthByFormat = {\r\n        abbreviated: 3,\r\n        short: 2,\r\n        narrow: 1\r\n    };\r\n    return map(captions, (caption => caption.substr(0, lengthByFormat[format])))\r\n};\r\nexport default {\r\n    getMonthNames: function(format) {\r\n        return cutCaptions(MONTHS, format)\r\n    },\r\n    getDayNames: function(format) {\r\n        return cutCaptions(DAYS, format)\r\n    },\r\n    getQuarterNames: function(format) {\r\n        return QUARTERS\r\n    },\r\n    getPeriodNames: function(format) {\r\n        return PERIODS\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;AAGA,MAAM,SAAS;IAAC;IAAW;IAAY;IAAS;IAAS;IAAO;IAAQ;IAAQ;IAAU;IAAa;IAAW;IAAY;CAAW;AACzI,MAAM,OAAO;IAAC;IAAU;IAAU;IAAW;IAAa;IAAY;IAAU;CAAW;AAC3F,MAAM,UAAU;IAAC;IAAM;CAAK;AAC5B,MAAM,WAAW;IAAC;IAAM;IAAM;IAAM;CAAK;AACzC,MAAM,cAAc,CAAC,UAAU;IAC3B,MAAM,iBAAiB;QACnB,aAAa;QACb,OAAO;QACP,QAAQ;IACZ;IACA,OAAO,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,UAAW,CAAA,UAAW,QAAQ,MAAM,CAAC,GAAG,cAAc,CAAC,OAAO;AAC7E;uCACe;IACX,eAAe,SAAS,MAAM;QAC1B,OAAO,YAAY,QAAQ;IAC/B;IACA,aAAa,SAAS,MAAM;QACxB,OAAO,YAAY,MAAM;IAC7B;IACA,iBAAiB,SAAS,MAAM;QAC5B,OAAO;IACX;IACA,gBAAgB,SAAS,MAAM;QAC3B,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1886, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/ldml/date.formatter.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/ldml/date.formatter.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nfunction leftPad(text, length) {\r\n    while (text.length < length) {\r\n        text = \"0\" + text\r\n    }\r\n    return text\r\n}\r\nconst FORMAT_TYPES = {\r\n    3: \"abbreviated\",\r\n    4: \"wide\",\r\n    5: \"narrow\"\r\n};\r\nconst LDML_FORMATTERS = {\r\n    y: function(date, count, useUtc) {\r\n        let year = date[useUtc ? \"getUTCFullYear\" : \"getFullYear\"]();\r\n        if (2 === count) {\r\n            year %= 100\r\n        }\r\n        return leftPad(year.toString(), count)\r\n    },\r\n    M: function(date, count, useUtc, dateParts) {\r\n        const month = date[useUtc ? \"getUTCMonth\" : \"getMonth\"]();\r\n        const formatType = FORMAT_TYPES[count];\r\n        if (formatType) {\r\n            return dateParts.getMonthNames(formatType, \"format\")[month]\r\n        }\r\n        return leftPad((month + 1).toString(), Math.min(count, 2))\r\n    },\r\n    L: function(date, count, useUtc, dateParts) {\r\n        const month = date[useUtc ? \"getUTCMonth\" : \"getMonth\"]();\r\n        const formatType = FORMAT_TYPES[count];\r\n        if (formatType) {\r\n            return dateParts.getMonthNames(formatType, \"standalone\")[month]\r\n        }\r\n        return leftPad((month + 1).toString(), Math.min(count, 2))\r\n    },\r\n    Q: function(date, count, useUtc, dateParts) {\r\n        const month = date[useUtc ? \"getUTCMonth\" : \"getMonth\"]();\r\n        const quarter = Math.floor(month / 3);\r\n        const formatType = FORMAT_TYPES[count];\r\n        if (formatType) {\r\n            return dateParts.getQuarterNames(formatType)[quarter]\r\n        }\r\n        return leftPad((quarter + 1).toString(), Math.min(count, 2))\r\n    },\r\n    E: function(date, count, useUtc, dateParts) {\r\n        const day = date[useUtc ? \"getUTCDay\" : \"getDay\"]();\r\n        const formatType = FORMAT_TYPES[count < 3 ? 3 : count];\r\n        return dateParts.getDayNames(formatType)[day]\r\n    },\r\n    a: function(date, count, useUtc, dateParts) {\r\n        const hours = date[useUtc ? \"getUTCHours\" : \"getHours\"]();\r\n        const period = hours < 12 ? 0 : 1;\r\n        const formatType = FORMAT_TYPES[count];\r\n        return dateParts.getPeriodNames(formatType)[period]\r\n    },\r\n    d: function(date, count, useUtc) {\r\n        return leftPad(date[useUtc ? \"getUTCDate\" : \"getDate\"]().toString(), Math.min(count, 2))\r\n    },\r\n    H: function(date, count, useUtc) {\r\n        return leftPad(date[useUtc ? \"getUTCHours\" : \"getHours\"]().toString(), Math.min(count, 2))\r\n    },\r\n    h: function(date, count, useUtc) {\r\n        const hours = date[useUtc ? \"getUTCHours\" : \"getHours\"]();\r\n        return leftPad((hours % 12 || 12).toString(), Math.min(count, 2))\r\n    },\r\n    m: function(date, count, useUtc) {\r\n        return leftPad(date[useUtc ? \"getUTCMinutes\" : \"getMinutes\"]().toString(), Math.min(count, 2))\r\n    },\r\n    s: function(date, count, useUtc) {\r\n        return leftPad(date[useUtc ? \"getUTCSeconds\" : \"getSeconds\"]().toString(), Math.min(count, 2))\r\n    },\r\n    S: function(date, count, useUtc) {\r\n        return leftPad(date[useUtc ? \"getUTCMilliseconds\" : \"getMilliseconds\"]().toString(), 3).substr(0, count)\r\n    },\r\n    x: function(date, count, useUtc) {\r\n        const timezoneOffset = useUtc ? 0 : date.getTimezoneOffset();\r\n        const signPart = timezoneOffset > 0 ? \"-\" : \"+\";\r\n        const timezoneOffsetAbs = Math.abs(timezoneOffset);\r\n        const hours = Math.floor(timezoneOffsetAbs / 60);\r\n        const minutes = timezoneOffsetAbs % 60;\r\n        const hoursPart = leftPad(hours.toString(), 2);\r\n        const minutesPart = leftPad(minutes.toString(), 2);\r\n        return signPart + hoursPart + (count >= 3 ? \":\" : \"\") + (count > 1 || minutes ? minutesPart : \"\")\r\n    },\r\n    X: function(date, count, useUtc) {\r\n        if (useUtc || !date.getTimezoneOffset()) {\r\n            return \"Z\"\r\n        }\r\n        return LDML_FORMATTERS.x(date, count, useUtc)\r\n    },\r\n    Z: function(date, count, useUtc) {\r\n        return LDML_FORMATTERS.X(date, count >= 5 ? 3 : 2, useUtc)\r\n    }\r\n};\r\nexport const getFormatter = function(format, dateParts) {\r\n    return function(date) {\r\n        let charIndex;\r\n        let formatter;\r\n        let char;\r\n        let charCount = 0;\r\n        let isEscaping = false;\r\n        let isCurrentCharEqualsNext;\r\n        let result = \"\";\r\n        if (!date) {\r\n            return null\r\n        }\r\n        if (!format) {\r\n            return date\r\n        }\r\n        const useUtc = \"Z\" === format[format.length - 1] || \"'Z'\" === format.slice(-3);\r\n        for (charIndex = 0; charIndex < format.length; charIndex++) {\r\n            char = format[charIndex];\r\n            formatter = LDML_FORMATTERS[char];\r\n            isCurrentCharEqualsNext = char === format[charIndex + 1];\r\n            charCount++;\r\n            if (!isCurrentCharEqualsNext) {\r\n                if (formatter && !isEscaping) {\r\n                    result += formatter(date, charCount, useUtc, dateParts)\r\n                }\r\n                charCount = 0\r\n            }\r\n            if (\"'\" === char && !isCurrentCharEqualsNext) {\r\n                isEscaping = !isEscaping\r\n            } else if (isEscaping || !formatter) {\r\n                result += char\r\n            }\r\n            if (\"'\" === char && isCurrentCharEqualsNext) {\r\n                charIndex++\r\n            }\r\n        }\r\n        return result\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,SAAS,QAAQ,IAAI,EAAE,MAAM;IACzB,MAAO,KAAK,MAAM,GAAG,OAAQ;QACzB,OAAO,MAAM;IACjB;IACA,OAAO;AACX;AACA,MAAM,eAAe;IACjB,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,kBAAkB;IACpB,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,IAAI,OAAO,IAAI,CAAC,SAAS,mBAAmB,cAAc;QAC1D,IAAI,MAAM,OAAO;YACb,QAAQ;QACZ;QACA,OAAO,QAAQ,KAAK,QAAQ,IAAI;IACpC;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACtC,MAAM,QAAQ,IAAI,CAAC,SAAS,gBAAgB,WAAW;QACvD,MAAM,aAAa,YAAY,CAAC,MAAM;QACtC,IAAI,YAAY;YACZ,OAAO,UAAU,aAAa,CAAC,YAAY,SAAS,CAAC,MAAM;QAC/D;QACA,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC3D;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACtC,MAAM,QAAQ,IAAI,CAAC,SAAS,gBAAgB,WAAW;QACvD,MAAM,aAAa,YAAY,CAAC,MAAM;QACtC,IAAI,YAAY;YACZ,OAAO,UAAU,aAAa,CAAC,YAAY,aAAa,CAAC,MAAM;QACnE;QACA,OAAO,QAAQ,CAAC,QAAQ,CAAC,EAAE,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC3D;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACtC,MAAM,QAAQ,IAAI,CAAC,SAAS,gBAAgB,WAAW;QACvD,MAAM,UAAU,KAAK,KAAK,CAAC,QAAQ;QACnC,MAAM,aAAa,YAAY,CAAC,MAAM;QACtC,IAAI,YAAY;YACZ,OAAO,UAAU,eAAe,CAAC,WAAW,CAAC,QAAQ;QACzD;QACA,OAAO,QAAQ,CAAC,UAAU,CAAC,EAAE,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC7D;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACtC,MAAM,MAAM,IAAI,CAAC,SAAS,cAAc,SAAS;QACjD,MAAM,aAAa,YAAY,CAAC,QAAQ,IAAI,IAAI,MAAM;QACtD,OAAO,UAAU,WAAW,CAAC,WAAW,CAAC,IAAI;IACjD;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS;QACtC,MAAM,QAAQ,IAAI,CAAC,SAAS,gBAAgB,WAAW;QACvD,MAAM,SAAS,QAAQ,KAAK,IAAI;QAChC,MAAM,aAAa,YAAY,CAAC,MAAM;QACtC,OAAO,UAAU,cAAc,CAAC,WAAW,CAAC,OAAO;IACvD;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,OAAO,QAAQ,IAAI,CAAC,SAAS,eAAe,UAAU,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IACzF;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,OAAO,QAAQ,IAAI,CAAC,SAAS,gBAAgB,WAAW,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC3F;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,MAAM,QAAQ,IAAI,CAAC,SAAS,gBAAgB,WAAW;QACvD,OAAO,QAAQ,CAAC,QAAQ,MAAM,EAAE,EAAE,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAClE;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,OAAO,QAAQ,IAAI,CAAC,SAAS,kBAAkB,aAAa,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC/F;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,OAAO,QAAQ,IAAI,CAAC,SAAS,kBAAkB,aAAa,GAAG,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC/F;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,OAAO,QAAQ,IAAI,CAAC,SAAS,uBAAuB,kBAAkB,GAAG,QAAQ,IAAI,GAAG,MAAM,CAAC,GAAG;IACtG;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,MAAM,iBAAiB,SAAS,IAAI,KAAK,iBAAiB;QAC1D,MAAM,WAAW,iBAAiB,IAAI,MAAM;QAC5C,MAAM,oBAAoB,KAAK,GAAG,CAAC;QACnC,MAAM,QAAQ,KAAK,KAAK,CAAC,oBAAoB;QAC7C,MAAM,UAAU,oBAAoB;QACpC,MAAM,YAAY,QAAQ,MAAM,QAAQ,IAAI;QAC5C,MAAM,cAAc,QAAQ,QAAQ,QAAQ,IAAI;QAChD,OAAO,WAAW,YAAY,CAAC,SAAS,IAAI,MAAM,EAAE,IAAI,CAAC,QAAQ,KAAK,UAAU,cAAc,EAAE;IACpG;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,IAAI,UAAU,CAAC,KAAK,iBAAiB,IAAI;YACrC,OAAO;QACX;QACA,OAAO,gBAAgB,CAAC,CAAC,MAAM,OAAO;IAC1C;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,MAAM;QAC3B,OAAO,gBAAgB,CAAC,CAAC,MAAM,SAAS,IAAI,IAAI,GAAG;IACvD;AACJ;AACO,MAAM,eAAe,SAAS,MAAM,EAAE,SAAS;IAClD,OAAO,SAAS,IAAI;QAChB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,IAAI;QACJ,IAAI,SAAS;QACb,IAAI,CAAC,MAAM;YACP,OAAO;QACX;QACA,IAAI,CAAC,QAAQ;YACT,OAAO;QACX;QACA,MAAM,SAAS,QAAQ,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,IAAI,UAAU,OAAO,KAAK,CAAC,CAAC;QAC5E,IAAK,YAAY,GAAG,YAAY,OAAO,MAAM,EAAE,YAAa;YACxD,OAAO,MAAM,CAAC,UAAU;YACxB,YAAY,eAAe,CAAC,KAAK;YACjC,0BAA0B,SAAS,MAAM,CAAC,YAAY,EAAE;YACxD;YACA,IAAI,CAAC,yBAAyB;gBAC1B,IAAI,aAAa,CAAC,YAAY;oBAC1B,UAAU,UAAU,MAAM,WAAW,QAAQ;gBACjD;gBACA,YAAY;YAChB;YACA,IAAI,QAAQ,QAAQ,CAAC,yBAAyB;gBAC1C,aAAa,CAAC;YAClB,OAAO,IAAI,cAAc,CAAC,WAAW;gBACjC,UAAU;YACd;YACA,IAAI,QAAQ,QAAQ,yBAAyB;gBACzC;YACJ;QACJ;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2033, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment/time_zone_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment/time_zone_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport timeZoneUtils from \"../../../__internal/scheduler/m_utils_time_zone\";\r\nexport const getTimeZones = timeZoneUtils.getTimeZones;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AACO,MAAM,eAAe,uLAAA,CAAA,UAAa,CAAC,YAAY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2050, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/environment.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/environment.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport devices from \"./environment/devices\";\r\nimport hideTopOverlay from \"./environment/hide_top_overlay\";\r\nimport initMobileViewport from \"./environment/init_mobile_viewport\";\r\nimport {\r\n    getTimeZones\r\n} from \"./environment/time_zone_utils\";\r\nexport {\r\n    devices,\r\n    hideTopOverlay,\r\n    initMobileViewport,\r\n    getTimeZones\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD;AACA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2081, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/position.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/position.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getOuterWidth,\r\n    getOuterHeight,\r\n    getWidth,\r\n    getHeight\r\n} from \"../../../core/utils/size\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    splitPair,\r\n    pairToObject\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getWindow\r\n} from \"../../../core/utils/window\";\r\nconst window = getWindow();\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport {\r\n    isWindow,\r\n    isDefined\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    getBoundingRect\r\n} from \"../../../core/utils/position\";\r\nimport browser from \"../../../core/utils/browser\";\r\nimport {\r\n    resetPosition,\r\n    move\r\n} from \"./translator\";\r\nimport {\r\n    touch\r\n} from \"../../../core/utils/support\";\r\nimport devices from \"../../../core/devices\";\r\nimport {\r\n    setStyle\r\n} from \"../../../core/utils/style\";\r\nconst horzRe = /left|right/;\r\nconst vertRe = /top|bottom/;\r\nconst collisionRe = /fit|flip|none/;\r\nconst scaleRe = /scale\\(.+?\\)/;\r\nconst IS_SAFARI = browser.safari;\r\nconst normalizeAlign = function(raw) {\r\n    const result = {\r\n        h: \"center\",\r\n        v: \"center\"\r\n    };\r\n    const pair = splitPair(raw);\r\n    if (pair) {\r\n        each(pair, (function() {\r\n            const w = String(this).toLowerCase();\r\n            if (horzRe.test(w)) {\r\n                result.h = w\r\n            } else if (vertRe.test(w)) {\r\n                result.v = w\r\n            }\r\n        }))\r\n    }\r\n    return result\r\n};\r\nconst normalizeOffset = function(raw, preventRound) {\r\n    return pairToObject(raw, preventRound)\r\n};\r\nconst normalizeCollision = function(raw) {\r\n    const pair = splitPair(raw);\r\n    let h = String(pair && pair[0]).toLowerCase();\r\n    let v = String(pair && pair[1]).toLowerCase();\r\n    if (!collisionRe.test(h)) {\r\n        h = \"none\"\r\n    }\r\n    if (!collisionRe.test(v)) {\r\n        v = h\r\n    }\r\n    return {\r\n        h: h,\r\n        v: v\r\n    }\r\n};\r\nconst getAlignFactor = function(align) {\r\n    switch (align) {\r\n        case \"center\":\r\n            return .5;\r\n        case \"right\":\r\n        case \"bottom\":\r\n            return 1;\r\n        default:\r\n            return 0\r\n    }\r\n};\r\nconst inverseAlign = function(align) {\r\n    switch (align) {\r\n        case \"left\":\r\n            return \"right\";\r\n        case \"right\":\r\n            return \"left\";\r\n        case \"top\":\r\n            return \"bottom\";\r\n        case \"bottom\":\r\n            return \"top\";\r\n        default:\r\n            return align\r\n    }\r\n};\r\nconst calculateOversize = function(data, bounds) {\r\n    let oversize = 0;\r\n    if (data.myLocation < bounds.min) {\r\n        oversize += bounds.min - data.myLocation\r\n    }\r\n    if (data.myLocation > bounds.max) {\r\n        oversize += data.myLocation - bounds.max\r\n    }\r\n    return oversize\r\n};\r\nconst collisionSide = function(direction, data, bounds) {\r\n    if (data.myLocation < bounds.min) {\r\n        return \"h\" === direction ? \"left\" : \"top\"\r\n    }\r\n    if (data.myLocation > bounds.max) {\r\n        return \"h\" === direction ? \"right\" : \"bottom\"\r\n    }\r\n    return \"none\"\r\n};\r\nconst initMyLocation = function(data) {\r\n    data.myLocation = data.atLocation + getAlignFactor(data.atAlign) * data.atSize - getAlignFactor(data.myAlign) * data.mySize + data.offset\r\n};\r\nconst collisionResolvers = {\r\n    fit: function(data, bounds) {\r\n        let result = false;\r\n        if (data.myLocation > bounds.max) {\r\n            data.myLocation = bounds.max;\r\n            result = true\r\n        }\r\n        if (data.myLocation < bounds.min) {\r\n            data.myLocation = bounds.min;\r\n            result = true\r\n        }\r\n        data.fit = result\r\n    },\r\n    flip: function(data, bounds) {\r\n        data.flip = false;\r\n        if (\"center\" === data.myAlign && \"center\" === data.atAlign) {\r\n            return\r\n        }\r\n        if (data.myLocation < bounds.min || data.myLocation > bounds.max) {\r\n            const inverseData = extend({}, data, {\r\n                myAlign: inverseAlign(data.myAlign),\r\n                atAlign: inverseAlign(data.atAlign),\r\n                offset: -data.offset\r\n            });\r\n            initMyLocation(inverseData);\r\n            inverseData.oversize = calculateOversize(inverseData, bounds);\r\n            if (inverseData.myLocation >= bounds.min && inverseData.myLocation <= bounds.max || data.oversize > inverseData.oversize) {\r\n                data.myLocation = inverseData.myLocation;\r\n                data.oversize = inverseData.oversize;\r\n                data.flip = true\r\n            }\r\n        }\r\n    },\r\n    flipfit: function(data, bounds) {\r\n        this.flip(data, bounds);\r\n        this.fit(data, bounds)\r\n    },\r\n    none: function(data) {\r\n        data.oversize = 0\r\n    }\r\n};\r\nlet scrollbarWidth;\r\nconst calculateScrollbarWidth = function() {\r\n    const $scrollDiv = $(\"<div>\").css({\r\n        width: 100,\r\n        height: 100,\r\n        overflow: \"scroll\",\r\n        position: \"absolute\",\r\n        top: -9999\r\n    }).appendTo($(\"body\"));\r\n    const result = $scrollDiv.get(0).offsetWidth - $scrollDiv.get(0).clientWidth;\r\n    $scrollDiv.remove();\r\n    scrollbarWidth = result\r\n};\r\nconst defaultPositionResult = {\r\n    h: {\r\n        location: 0,\r\n        flip: false,\r\n        fit: false,\r\n        oversize: 0\r\n    },\r\n    v: {\r\n        location: 0,\r\n        flip: false,\r\n        fit: false,\r\n        oversize: 0\r\n    }\r\n};\r\nconst calculatePosition = function(what, options) {\r\n    const $what = $(what);\r\n    const currentOffset = $what.offset();\r\n    const result = extend(true, {}, defaultPositionResult, {\r\n        h: {\r\n            location: currentOffset.left\r\n        },\r\n        v: {\r\n            location: currentOffset.top\r\n        }\r\n    });\r\n    if (!options) {\r\n        return result\r\n    }\r\n    const my = normalizeAlign(options.my);\r\n    const at = normalizeAlign(options.at);\r\n    let of = $(options.of).length && options.of || window;\r\n    const offset = normalizeOffset(options.offset, options.precise);\r\n    const collision = normalizeCollision(options.collision);\r\n    const boundary = options.boundary;\r\n    const boundaryOffset = normalizeOffset(options.boundaryOffset, options.precise);\r\n    const h = {\r\n        mySize: getOuterWidth($what),\r\n        myAlign: my.h,\r\n        atAlign: at.h,\r\n        offset: offset.h,\r\n        collision: collision.h,\r\n        boundaryOffset: boundaryOffset.h\r\n    };\r\n    const v = {\r\n        mySize: getOuterHeight($what),\r\n        myAlign: my.v,\r\n        atAlign: at.v,\r\n        offset: offset.v,\r\n        collision: collision.v,\r\n        boundaryOffset: boundaryOffset.v\r\n    };\r\n    if (of.preventDefault) {\r\n        h.atLocation = of.pageX;\r\n        v.atLocation = of.pageY;\r\n        h.atSize = 0;\r\n        v.atSize = 0\r\n    } else {\r\n        of = $(of);\r\n        if (isWindow(of [0])) {\r\n            h.atLocation = of.scrollLeft();\r\n            v.atLocation = of.scrollTop();\r\n            if (\"phone\" === devices.real().deviceType && of [0].visualViewport) {\r\n                h.atLocation = Math.max(h.atLocation, of [0].visualViewport.offsetLeft);\r\n                v.atLocation = Math.max(v.atLocation, of [0].visualViewport.offsetTop);\r\n                h.atSize = of [0].visualViewport.width;\r\n                v.atSize = of [0].visualViewport.height\r\n            } else {\r\n                h.atSize = of [0].innerWidth > of [0].outerWidth ? of [0].innerWidth : getWidth(of);\r\n                v.atSize = of [0].innerHeight > of [0].outerHeight || IS_SAFARI ? of [0].innerHeight : getHeight(of)\r\n            }\r\n        } else if (9 === of [0].nodeType) {\r\n            h.atLocation = 0;\r\n            v.atLocation = 0;\r\n            h.atSize = getWidth(of);\r\n            v.atSize = getHeight(of)\r\n        } else {\r\n            const ofRect = getBoundingRect(of.get(0));\r\n            const o = getOffsetWithoutScale(of);\r\n            h.atLocation = o.left;\r\n            v.atLocation = o.top;\r\n            h.atSize = Math.max(ofRect.width, getOuterWidth(of));\r\n            v.atSize = Math.max(ofRect.height, getOuterHeight(of))\r\n        }\r\n    }\r\n    initMyLocation(h);\r\n    initMyLocation(v);\r\n    const bounds = function() {\r\n        const win = $(window);\r\n        const windowWidth = getWidth(win);\r\n        const windowHeight = getHeight(win);\r\n        let left = win.scrollLeft();\r\n        let top = win.scrollTop();\r\n        const documentElement = domAdapter.getDocumentElement();\r\n        const hZoomLevel = touch ? documentElement.clientWidth / windowWidth : 1;\r\n        const vZoomLevel = touch ? documentElement.clientHeight / windowHeight : 1;\r\n        if (void 0 === scrollbarWidth) {\r\n            calculateScrollbarWidth()\r\n        }\r\n        let boundaryWidth = windowWidth;\r\n        let boundaryHeight = windowHeight;\r\n        if (boundary && !isWindow(boundary)) {\r\n            const $boundary = $(boundary);\r\n            const boundaryPosition = $boundary.offset();\r\n            left = boundaryPosition.left;\r\n            top = boundaryPosition.top;\r\n            boundaryWidth = getWidth($boundary);\r\n            boundaryHeight = getHeight($boundary)\r\n        }\r\n        return {\r\n            h: {\r\n                min: left + h.boundaryOffset,\r\n                max: left + boundaryWidth / hZoomLevel - h.mySize - h.boundaryOffset\r\n            },\r\n            v: {\r\n                min: top + v.boundaryOffset,\r\n                max: top + boundaryHeight / vZoomLevel - v.mySize - v.boundaryOffset\r\n            }\r\n        }\r\n    }();\r\n    h.oversize = calculateOversize(h, bounds.h);\r\n    v.oversize = calculateOversize(v, bounds.v);\r\n    h.collisionSide = collisionSide(\"h\", h, bounds.h);\r\n    v.collisionSide = collisionSide(\"v\", v, bounds.v);\r\n    if (collisionResolvers[h.collision]) {\r\n        collisionResolvers[h.collision](h, bounds.h)\r\n    }\r\n    if (collisionResolvers[v.collision]) {\r\n        collisionResolvers[v.collision](v, bounds.v)\r\n    }\r\n    const preciser = function(number) {\r\n        return options.precise ? number : Math.round(number)\r\n    };\r\n    extend(true, result, {\r\n        h: {\r\n            location: preciser(h.myLocation),\r\n            oversize: preciser(h.oversize),\r\n            fit: h.fit,\r\n            flip: h.flip,\r\n            collisionSide: h.collisionSide\r\n        },\r\n        v: {\r\n            location: preciser(v.myLocation),\r\n            oversize: preciser(v.oversize),\r\n            fit: v.fit,\r\n            flip: v.flip,\r\n            collisionSide: v.collisionSide\r\n        },\r\n        precise: options.precise\r\n    });\r\n    return result\r\n};\r\nconst setScaleProperty = function(element, scale, styleAttr, isEmpty) {\r\n    const stylePropIsValid = isDefined(element.style) && !domAdapter.isNode(element.style);\r\n    const newStyleValue = isEmpty ? styleAttr.replace(scale, \"\") : styleAttr;\r\n    if (stylePropIsValid) {\r\n        setStyle(element, newStyleValue, false)\r\n    } else {\r\n        const styleAttributeNode = domAdapter.createAttribute(\"style\");\r\n        styleAttributeNode.value = newStyleValue;\r\n        element.setAttributeNode(styleAttributeNode)\r\n    }\r\n};\r\nconst getOffsetWithoutScale = function($startElement) {\r\n    var _currentElement$getAt, _style$match;\r\n    let $currentElement = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : $startElement;\r\n    const currentElement = $currentElement.get(0);\r\n    if (!currentElement) {\r\n        return $startElement.offset()\r\n    }\r\n    const style = (null === (_currentElement$getAt = currentElement.getAttribute) || void 0 === _currentElement$getAt ? void 0 : _currentElement$getAt.call(currentElement, \"style\")) || \"\";\r\n    const scale = null === (_style$match = style.match(scaleRe)) || void 0 === _style$match ? void 0 : _style$match[0];\r\n    let offset;\r\n    if (scale) {\r\n        setScaleProperty(currentElement, scale, style, true);\r\n        offset = getOffsetWithoutScale($startElement, $currentElement.parent());\r\n        setScaleProperty(currentElement, scale, style, false)\r\n    } else {\r\n        offset = getOffsetWithoutScale($startElement, $currentElement.parent())\r\n    }\r\n    return offset\r\n};\r\nconst position = function(what, options) {\r\n    const $what = $(what);\r\n    if (!options) {\r\n        return $what.offset()\r\n    }\r\n    resetPosition($what, true);\r\n    const offset = getOffsetWithoutScale($what);\r\n    const targetPosition = options.h && options.v ? options : calculatePosition($what, options);\r\n    const preciser = function(number) {\r\n        return options.precise ? number : Math.round(number)\r\n    };\r\n    move($what, {\r\n        left: targetPosition.h.location - preciser(offset.left),\r\n        top: targetPosition.v.location - preciser(offset.top)\r\n    });\r\n    return targetPosition\r\n};\r\nconst offset = function(element) {\r\n    element = $(element).get(0);\r\n    if (isWindow(element)) {\r\n        return null\r\n    } else if (element && \"pageY\" in element && \"pageX\" in element) {\r\n        return {\r\n            top: element.pageY,\r\n            left: element.pageX\r\n        }\r\n    }\r\n    return $(element).offset()\r\n};\r\nif (!position.inverseAlign) {\r\n    position.inverseAlign = inverseAlign\r\n}\r\nif (!position.normalizeAlign) {\r\n    position.normalizeAlign = normalizeAlign\r\n}\r\nexport default {\r\n    calculateScrollbarWidth: calculateScrollbarWidth,\r\n    calculate: calculatePosition,\r\n    setup: position,\r\n    offset: offset\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAMA;AACA;AAAA;AAIA;AAAA;AAGA;AAAA;AAIA;AACA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AACA;AAIA;AAAA;AAGA;AACA;AAAA;;;;;;AArBA,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD;;;;;;;;;;AAwBvB,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,cAAc;AACpB,MAAM,UAAU;AAChB,MAAM,YAAY,gKAAA,CAAA,UAAO,CAAC,MAAM;AAChC,MAAM,iBAAiB,SAAS,GAAG;IAC/B,MAAM,SAAS;QACX,GAAG;QACH,GAAG;IACP;IACA,MAAM,OAAO,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAE;IACvB,IAAI,MAAM;QACN,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,MAAO;YACR,MAAM,IAAI,OAAO,IAAI,EAAE,WAAW;YAClC,IAAI,OAAO,IAAI,CAAC,IAAI;gBAChB,OAAO,CAAC,GAAG;YACf,OAAO,IAAI,OAAO,IAAI,CAAC,IAAI;gBACvB,OAAO,CAAC,GAAG;YACf;QACJ;IACJ;IACA,OAAO;AACX;AACA,MAAM,kBAAkB,SAAS,GAAG,EAAE,YAAY;IAC9C,OAAO,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,KAAK;AAC7B;AACA,MAAM,qBAAqB,SAAS,GAAG;IACnC,MAAM,OAAO,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,EAAE;IACvB,IAAI,IAAI,OAAO,QAAQ,IAAI,CAAC,EAAE,EAAE,WAAW;IAC3C,IAAI,IAAI,OAAO,QAAQ,IAAI,CAAC,EAAE,EAAE,WAAW;IAC3C,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI;QACtB,IAAI;IACR;IACA,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI;QACtB,IAAI;IACR;IACA,OAAO;QACH,GAAG;QACH,GAAG;IACP;AACJ;AACA,MAAM,iBAAiB,SAAS,KAAK;IACjC,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,eAAe,SAAS,KAAK;IAC/B,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,MAAM,oBAAoB,SAAS,IAAI,EAAE,MAAM;IAC3C,IAAI,WAAW;IACf,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;QAC9B,YAAY,OAAO,GAAG,GAAG,KAAK,UAAU;IAC5C;IACA,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;QAC9B,YAAY,KAAK,UAAU,GAAG,OAAO,GAAG;IAC5C;IACA,OAAO;AACX;AACA,MAAM,gBAAgB,SAAS,SAAS,EAAE,IAAI,EAAE,MAAM;IAClD,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;QAC9B,OAAO,QAAQ,YAAY,SAAS;IACxC;IACA,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;QAC9B,OAAO,QAAQ,YAAY,UAAU;IACzC;IACA,OAAO;AACX;AACA,MAAM,iBAAiB,SAAS,IAAI;IAChC,KAAK,UAAU,GAAG,KAAK,UAAU,GAAG,eAAe,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG,eAAe,KAAK,OAAO,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM;AAC7I;AACA,MAAM,qBAAqB;IACvB,KAAK,SAAS,IAAI,EAAE,MAAM;QACtB,IAAI,SAAS;QACb,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;YAC9B,KAAK,UAAU,GAAG,OAAO,GAAG;YAC5B,SAAS;QACb;QACA,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;YAC9B,KAAK,UAAU,GAAG,OAAO,GAAG;YAC5B,SAAS;QACb;QACA,KAAK,GAAG,GAAG;IACf;IACA,MAAM,SAAS,IAAI,EAAE,MAAM;QACvB,KAAK,IAAI,GAAG;QACZ,IAAI,aAAa,KAAK,OAAO,IAAI,aAAa,KAAK,OAAO,EAAE;YACxD;QACJ;QACA,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,IAAI,KAAK,UAAU,GAAG,OAAO,GAAG,EAAE;YAC9D,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,MAAM;gBACjC,SAAS,aAAa,KAAK,OAAO;gBAClC,SAAS,aAAa,KAAK,OAAO;gBAClC,QAAQ,CAAC,KAAK,MAAM;YACxB;YACA,eAAe;YACf,YAAY,QAAQ,GAAG,kBAAkB,aAAa;YACtD,IAAI,YAAY,UAAU,IAAI,OAAO,GAAG,IAAI,YAAY,UAAU,IAAI,OAAO,GAAG,IAAI,KAAK,QAAQ,GAAG,YAAY,QAAQ,EAAE;gBACtH,KAAK,UAAU,GAAG,YAAY,UAAU;gBACxC,KAAK,QAAQ,GAAG,YAAY,QAAQ;gBACpC,KAAK,IAAI,GAAG;YAChB;QACJ;IACJ;IACA,SAAS,SAAS,IAAI,EAAE,MAAM;QAC1B,IAAI,CAAC,IAAI,CAAC,MAAM;QAChB,IAAI,CAAC,GAAG,CAAC,MAAM;IACnB;IACA,MAAM,SAAS,IAAI;QACf,KAAK,QAAQ,GAAG;IACpB;AACJ;AACA,IAAI;AACJ,MAAM,0BAA0B;IAC5B,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,GAAG,CAAC;QAC9B,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,KAAK,CAAC;IACV,GAAG,QAAQ,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACd,MAAM,SAAS,WAAW,GAAG,CAAC,GAAG,WAAW,GAAG,WAAW,GAAG,CAAC,GAAG,WAAW;IAC5E,WAAW,MAAM;IACjB,iBAAiB;AACrB;AACA,MAAM,wBAAwB;IAC1B,GAAG;QACC,UAAU;QACV,MAAM;QACN,KAAK;QACL,UAAU;IACd;IACA,GAAG;QACC,UAAU;QACV,MAAM;QACN,KAAK;QACL,UAAU;IACd;AACJ;AACA,MAAM,oBAAoB,SAAS,IAAI,EAAE,OAAO;IAC5C,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IAChB,MAAM,gBAAgB,MAAM,MAAM;IAClC,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,uBAAuB;QACnD,GAAG;YACC,UAAU,cAAc,IAAI;QAChC;QACA,GAAG;YACC,UAAU,cAAc,GAAG;QAC/B;IACJ;IACA,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,MAAM,KAAK,eAAe,QAAQ,EAAE;IACpC,MAAM,KAAK,eAAe,QAAQ,EAAE;IACpC,IAAI,KAAK,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,EAAE,EAAE,MAAM,IAAI,QAAQ,EAAE,IAAI;IAC/C,MAAM,SAAS,gBAAgB,QAAQ,MAAM,EAAE,QAAQ,OAAO;IAC9D,MAAM,YAAY,mBAAmB,QAAQ,SAAS;IACtD,MAAM,WAAW,QAAQ,QAAQ;IACjC,MAAM,iBAAiB,gBAAgB,QAAQ,cAAc,EAAE,QAAQ,OAAO;IAC9E,MAAM,IAAI;QACN,QAAQ,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;QACtB,SAAS,GAAG,CAAC;QACb,SAAS,GAAG,CAAC;QACb,QAAQ,OAAO,CAAC;QAChB,WAAW,UAAU,CAAC;QACtB,gBAAgB,eAAe,CAAC;IACpC;IACA,MAAM,IAAI;QACN,QAAQ,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;QACvB,SAAS,GAAG,CAAC;QACb,SAAS,GAAG,CAAC;QACb,QAAQ,OAAO,CAAC;QAChB,WAAW,UAAU,CAAC;QACtB,gBAAgB,eAAe,CAAC;IACpC;IACA,IAAI,GAAG,cAAc,EAAE;QACnB,EAAE,UAAU,GAAG,GAAG,KAAK;QACvB,EAAE,UAAU,GAAG,GAAG,KAAK;QACvB,EAAE,MAAM,GAAG;QACX,EAAE,MAAM,GAAG;IACf,OAAO;QACH,KAAK,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACP,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,EAAG,CAAC,EAAE,GAAG;YAClB,EAAE,UAAU,GAAG,GAAG,UAAU;YAC5B,EAAE,UAAU,GAAG,GAAG,SAAS;YAC3B,IAAI,YAAY,uJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,UAAU,IAAI,EAAG,CAAC,EAAE,CAAC,cAAc,EAAE;gBAChE,EAAE,UAAU,GAAG,KAAK,GAAG,CAAC,EAAE,UAAU,EAAE,EAAG,CAAC,EAAE,CAAC,cAAc,CAAC,UAAU;gBACtE,EAAE,UAAU,GAAG,KAAK,GAAG,CAAC,EAAE,UAAU,EAAE,EAAG,CAAC,EAAE,CAAC,cAAc,CAAC,SAAS;gBACrE,EAAE,MAAM,GAAG,EAAG,CAAC,EAAE,CAAC,cAAc,CAAC,KAAK;gBACtC,EAAE,MAAM,GAAG,EAAG,CAAC,EAAE,CAAC,cAAc,CAAC,MAAM;YAC3C,OAAO;gBACH,EAAE,MAAM,GAAG,EAAG,CAAC,EAAE,CAAC,UAAU,GAAG,EAAG,CAAC,EAAE,CAAC,UAAU,GAAG,EAAG,CAAC,EAAE,CAAC,UAAU,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;gBAChF,EAAE,MAAM,GAAG,EAAG,CAAC,EAAE,CAAC,WAAW,GAAG,EAAG,CAAC,EAAE,CAAC,WAAW,IAAI,YAAY,EAAG,CAAC,EAAE,CAAC,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;YACrG;QACJ,OAAO,IAAI,MAAM,EAAG,CAAC,EAAE,CAAC,QAAQ,EAAE;YAC9B,EAAE,UAAU,GAAG;YACf,EAAE,UAAU,GAAG;YACf,EAAE,MAAM,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;YACpB,EAAE,MAAM,GAAG,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;QACzB,OAAO;YACH,MAAM,SAAS,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,GAAG,GAAG,CAAC;YACtC,MAAM,IAAI,sBAAsB;YAChC,EAAE,UAAU,GAAG,EAAE,IAAI;YACrB,EAAE,UAAU,GAAG,EAAE,GAAG;YACpB,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,OAAO,KAAK,EAAE,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;YAChD,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,OAAO,MAAM,EAAE,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;QACtD;IACJ;IACA,eAAe;IACf,eAAe;IACf,MAAM,SAAS;QACX,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACd,MAAM,cAAc,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;QAC7B,MAAM,eAAe,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;QAC/B,IAAI,OAAO,IAAI,UAAU;QACzB,IAAI,MAAM,IAAI,SAAS;QACvB,MAAM,kBAAkB,2JAAA,CAAA,UAAU,CAAC,kBAAkB;QACrD,MAAM,aAAa,mMAAA,CAAA,QAAK,GAAG,gBAAgB,WAAW,GAAG,cAAc;QACvE,MAAM,aAAa,mMAAA,CAAA,QAAK,GAAG,gBAAgB,YAAY,GAAG,eAAe;QACzE,IAAI,KAAK,MAAM,gBAAgB;YAC3B;QACJ;QACA,IAAI,gBAAgB;QACpB,IAAI,iBAAiB;QACrB,IAAI,YAAY,CAAC,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,WAAW;YACjC,MAAM,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;YACpB,MAAM,mBAAmB,UAAU,MAAM;YACzC,OAAO,iBAAiB,IAAI;YAC5B,MAAM,iBAAiB,GAAG;YAC1B,gBAAgB,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;YACzB,iBAAiB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;QAC/B;QACA,OAAO;YACH,GAAG;gBACC,KAAK,OAAO,EAAE,cAAc;gBAC5B,KAAK,OAAO,gBAAgB,aAAa,EAAE,MAAM,GAAG,EAAE,cAAc;YACxE;YACA,GAAG;gBACC,KAAK,MAAM,EAAE,cAAc;gBAC3B,KAAK,MAAM,iBAAiB,aAAa,EAAE,MAAM,GAAG,EAAE,cAAc;YACxE;QACJ;IACJ;IACA,EAAE,QAAQ,GAAG,kBAAkB,GAAG,OAAO,CAAC;IAC1C,EAAE,QAAQ,GAAG,kBAAkB,GAAG,OAAO,CAAC;IAC1C,EAAE,aAAa,GAAG,cAAc,KAAK,GAAG,OAAO,CAAC;IAChD,EAAE,aAAa,GAAG,cAAc,KAAK,GAAG,OAAO,CAAC;IAChD,IAAI,kBAAkB,CAAC,EAAE,SAAS,CAAC,EAAE;QACjC,kBAAkB,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC;IAC/C;IACA,IAAI,kBAAkB,CAAC,EAAE,SAAS,CAAC,EAAE;QACjC,kBAAkB,CAAC,EAAE,SAAS,CAAC,CAAC,GAAG,OAAO,CAAC;IAC/C;IACA,MAAM,WAAW,SAAS,MAAM;QAC5B,OAAO,QAAQ,OAAO,GAAG,SAAS,KAAK,KAAK,CAAC;IACjD;IACA,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,QAAQ;QACjB,GAAG;YACC,UAAU,SAAS,EAAE,UAAU;YAC/B,UAAU,SAAS,EAAE,QAAQ;YAC7B,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,aAAa;QAClC;QACA,GAAG;YACC,UAAU,SAAS,EAAE,UAAU;YAC/B,UAAU,SAAS,EAAE,QAAQ;YAC7B,KAAK,EAAE,GAAG;YACV,MAAM,EAAE,IAAI;YACZ,eAAe,EAAE,aAAa;QAClC;QACA,SAAS,QAAQ,OAAO;IAC5B;IACA,OAAO;AACX;AACA,MAAM,mBAAmB,SAAS,OAAO,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO;IAChE,MAAM,mBAAmB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,KAAK,KAAK,CAAC,2JAAA,CAAA,UAAU,CAAC,MAAM,CAAC,QAAQ,KAAK;IACrF,MAAM,gBAAgB,UAAU,UAAU,OAAO,CAAC,OAAO,MAAM;IAC/D,IAAI,kBAAkB;QAClB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,eAAe;IACrC,OAAO;QACH,MAAM,qBAAqB,2JAAA,CAAA,UAAU,CAAC,eAAe,CAAC;QACtD,mBAAmB,KAAK,GAAG;QAC3B,QAAQ,gBAAgB,CAAC;IAC7B;AACJ;AACA,MAAM,wBAAwB,SAAS,aAAa;IAChD,IAAI,uBAAuB;IAC3B,IAAI,kBAAkB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACvF,MAAM,iBAAiB,gBAAgB,GAAG,CAAC;IAC3C,IAAI,CAAC,gBAAgB;QACjB,OAAO,cAAc,MAAM;IAC/B;IACA,MAAM,QAAQ,CAAC,SAAS,CAAC,wBAAwB,eAAe,YAAY,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,CAAC,gBAAgB,QAAQ,KAAK;IACrL,MAAM,QAAQ,SAAS,CAAC,eAAe,MAAM,KAAK,CAAC,QAAQ,KAAK,KAAK,MAAM,eAAe,KAAK,IAAI,YAAY,CAAC,EAAE;IAClH,IAAI;IACJ,IAAI,OAAO;QACP,iBAAiB,gBAAgB,OAAO,OAAO;QAC/C,SAAS,sBAAsB,eAAe,gBAAgB,MAAM;QACpE,iBAAiB,gBAAgB,OAAO,OAAO;IACnD,OAAO;QACH,SAAS,sBAAsB,eAAe,gBAAgB,MAAM;IACxE;IACA,OAAO;AACX;AACA,MAAM,WAAW,SAAS,IAAI,EAAE,OAAO;IACnC,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IAChB,IAAI,CAAC,SAAS;QACV,OAAO,MAAM,MAAM;IACvB;IACA,CAAA,GAAA,iLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;IACrB,MAAM,SAAS,sBAAsB;IACrC,MAAM,iBAAiB,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG,UAAU,kBAAkB,OAAO;IACnF,MAAM,WAAW,SAAS,MAAM;QAC5B,OAAO,QAAQ,OAAO,GAAG,SAAS,KAAK,KAAK,CAAC;IACjD;IACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAO;QACR,MAAM,eAAe,CAAC,CAAC,QAAQ,GAAG,SAAS,OAAO,IAAI;QACtD,KAAK,eAAe,CAAC,CAAC,QAAQ,GAAG,SAAS,OAAO,GAAG;IACxD;IACA,OAAO;AACX;AACA,MAAM,SAAS,SAAS,OAAO;IAC3B,UAAU,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,GAAG,CAAC;IACzB,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;QACnB,OAAO;IACX,OAAO,IAAI,WAAW,WAAW,WAAW,WAAW,SAAS;QAC5D,OAAO;YACH,KAAK,QAAQ,KAAK;YAClB,MAAM,QAAQ,KAAK;QACvB;IACJ;IACA,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,MAAM;AAC5B;AACA,IAAI,CAAC,SAAS,YAAY,EAAE;IACxB,SAAS,YAAY,GAAG;AAC5B;AACA,IAAI,CAAC,SAAS,cAAc,EAAE;IAC1B,SAAS,cAAc,GAAG;AAC9B;uCACe;IACX,yBAAyB;IACzB,WAAW;IACX,OAAO;IACP,QAAQ;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2497, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/event_registrator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/event_registrator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/core/m_event_registrator\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2518, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/remove.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/remove.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_remove\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2539, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/fx.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/fx.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    getWindow\r\n} from \"../../../core/utils/window\";\r\nconst window = getWindow();\r\nimport eventsEngine from \"../events/core/events_engine\";\r\nimport errors from \"../../../core/errors\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../core/element\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    isFunction,\r\n    isPlainObject\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    each,\r\n    map\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getTranslateCss,\r\n    parseTranslate,\r\n    clearCache,\r\n    locate,\r\n    getTranslate\r\n} from \"./translator\";\r\nimport {\r\n    convertTransitionTimingFuncToEasing,\r\n    getEasing\r\n} from \"./easing\";\r\nimport {\r\n    requestAnimationFrame,\r\n    cancelAnimationFrame\r\n} from \"./frame\";\r\nimport supportUtils from \"../../../__internal/core/utils/m_support\";\r\nimport positionUtils from \"./position\";\r\nimport {\r\n    removeEvent\r\n} from \"../events/remove\";\r\nimport {\r\n    addNamespace\r\n} from \"../events/utils/index\";\r\nimport {\r\n    when,\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nconst removeEventName = addNamespace(removeEvent, \"dxFX\");\r\nimport {\r\n    noop\r\n} from \"../../../core/utils/common\";\r\nconst RELATIVE_VALUE_REGEX = /^([+-])=(.*)/i;\r\nconst ANIM_DATA_KEY = \"dxAnimData\";\r\nconst ANIM_QUEUE_KEY = \"dxAnimQueue\";\r\nconst TRANSFORM_PROP = \"transform\";\r\nconst TransitionAnimationStrategy = {\r\n    initAnimation: function($element, config) {\r\n        $element.css({\r\n            transitionProperty: \"none\"\r\n        });\r\n        if (\"string\" === typeof config.from) {\r\n            $element.addClass(config.from)\r\n        } else {\r\n            setProps($element, config.from)\r\n        }\r\n        const that = this;\r\n        const deferred = new Deferred;\r\n        const cleanupWhen = config.cleanupWhen;\r\n        config.transitionAnimation = {\r\n            deferred: deferred,\r\n            finish: function() {\r\n                that._finishTransition($element);\r\n                if (cleanupWhen) {\r\n                    when(deferred, cleanupWhen).always((function() {\r\n                        that._cleanup($element, config)\r\n                    }))\r\n                } else {\r\n                    that._cleanup($element, config)\r\n                }\r\n                deferred.resolveWith($element, [config, $element])\r\n            }\r\n        };\r\n        this._completeAnimationCallback($element, config).done((function() {\r\n            config.transitionAnimation.finish()\r\n        })).fail((function() {\r\n            deferred.rejectWith($element, [config, $element])\r\n        }));\r\n        if (!config.duration) {\r\n            config.transitionAnimation.finish()\r\n        }\r\n        $element.css(\"transform\")\r\n    },\r\n    animate: function($element, config) {\r\n        this._startAnimation($element, config);\r\n        return config.transitionAnimation.deferred.promise()\r\n    },\r\n    _completeAnimationCallback: function($element, config) {\r\n        const that = this;\r\n        const startTime = Date.now() + config.delay;\r\n        const deferred = new Deferred;\r\n        const transitionEndFired = new Deferred;\r\n        const simulatedTransitionEndFired = new Deferred;\r\n        let simulatedEndEventTimer;\r\n        const transitionEndEventFullName = supportUtils.transitionEndEventName() + \".dxFX\";\r\n        config.transitionAnimation.cleanup = function() {\r\n            clearTimeout(simulatedEndEventTimer);\r\n            clearTimeout(waitForJSCompleteTimer);\r\n            eventsEngine.off($element, transitionEndEventFullName);\r\n            eventsEngine.off($element, removeEventName)\r\n        };\r\n        eventsEngine.one($element, transitionEndEventFullName, (function() {\r\n            if (Date.now() - startTime >= config.duration) {\r\n                transitionEndFired.reject()\r\n            }\r\n        }));\r\n        eventsEngine.off($element, removeEventName);\r\n        eventsEngine.on($element, removeEventName, (function() {\r\n            that.stop($element, config);\r\n            deferred.reject()\r\n        }));\r\n        const waitForJSCompleteTimer = setTimeout((function() {\r\n            simulatedEndEventTimer = setTimeout((function() {\r\n                simulatedTransitionEndFired.reject()\r\n            }), config.duration + config.delay + fx._simulatedTransitionEndDelay);\r\n            when(transitionEndFired, simulatedTransitionEndFired).fail(function() {\r\n                deferred.resolve()\r\n            }.bind(this))\r\n        }));\r\n        return deferred.promise()\r\n    },\r\n    _startAnimation: function($element, config) {\r\n        $element.css({\r\n            transitionProperty: \"all\",\r\n            transitionDelay: config.delay + \"ms\",\r\n            transitionDuration: config.duration + \"ms\",\r\n            transitionTimingFunction: config.easing\r\n        });\r\n        if (\"string\" === typeof config.to) {\r\n            $element[0].className += \" \" + config.to\r\n        } else if (config.to) {\r\n            setProps($element, config.to)\r\n        }\r\n    },\r\n    _finishTransition: function($element) {\r\n        $element.css(\"transition\", \"none\")\r\n    },\r\n    _cleanup: function($element, config) {\r\n        config.transitionAnimation.cleanup();\r\n        if (\"string\" === typeof config.from) {\r\n            $element.removeClass(config.from);\r\n            $element.removeClass(config.to)\r\n        }\r\n    },\r\n    stop: function($element, config, jumpToEnd) {\r\n        if (!config) {\r\n            return\r\n        }\r\n        if (jumpToEnd) {\r\n            config.transitionAnimation.finish()\r\n        } else {\r\n            if (isPlainObject(config.to)) {\r\n                each(config.to, (function(key) {\r\n                    $element.css(key, $element.css(key))\r\n                }))\r\n            }\r\n            this._finishTransition($element);\r\n            this._cleanup($element, config)\r\n        }\r\n    }\r\n};\r\nconst FrameAnimationStrategy = {\r\n    initAnimation: function($element, config) {\r\n        setProps($element, config.from)\r\n    },\r\n    animate: function($element, config) {\r\n        const deferred = new Deferred;\r\n        const that = this;\r\n        if (!config) {\r\n            return deferred.reject().promise()\r\n        }\r\n        each(config.to, (function(prop) {\r\n            if (void 0 === config.from[prop]) {\r\n                config.from[prop] = that._normalizeValue($element.css(prop))\r\n            }\r\n        }));\r\n        if (config.to.transform) {\r\n            config.from.transform = that._parseTransform(config.from.transform);\r\n            config.to.transform = that._parseTransform(config.to.transform)\r\n        }\r\n        config.frameAnimation = {\r\n            to: config.to,\r\n            from: config.from,\r\n            currentValue: config.from,\r\n            easing: convertTransitionTimingFuncToEasing(config.easing),\r\n            duration: config.duration,\r\n            startTime: (new Date).valueOf(),\r\n            finish: function() {\r\n                this.currentValue = this.to;\r\n                this.draw();\r\n                cancelAnimationFrame(config.frameAnimation.animationFrameId);\r\n                deferred.resolve()\r\n            },\r\n            draw: function() {\r\n                if (config.draw) {\r\n                    config.draw(this.currentValue);\r\n                    return\r\n                }\r\n                const currentValue = extend({}, this.currentValue);\r\n                if (currentValue.transform) {\r\n                    currentValue.transform = map(currentValue.transform, (function(value, prop) {\r\n                        if (\"translate\" === prop) {\r\n                            return getTranslateCss(value)\r\n                        } else if (\"scale\" === prop) {\r\n                            return \"scale(\" + value + \")\"\r\n                        } else if (\"rotate\" === prop.substr(0, prop.length - 1)) {\r\n                            return prop + \"(\" + value + \"deg)\"\r\n                        }\r\n                    })).join(\" \")\r\n                }\r\n                $element.css(currentValue)\r\n            }\r\n        };\r\n        if (config.delay) {\r\n            config.frameAnimation.startTime += config.delay;\r\n            config.frameAnimation.delayTimeout = setTimeout((function() {\r\n                that._startAnimation($element, config)\r\n            }), config.delay)\r\n        } else {\r\n            that._startAnimation($element, config)\r\n        }\r\n        return deferred.promise()\r\n    },\r\n    _startAnimation: function($element, config) {\r\n        eventsEngine.off($element, removeEventName);\r\n        eventsEngine.on($element, removeEventName, (function() {\r\n            if (config.frameAnimation) {\r\n                cancelAnimationFrame(config.frameAnimation.animationFrameId)\r\n            }\r\n        }));\r\n        this._animationStep($element, config)\r\n    },\r\n    _parseTransform: function(transformString) {\r\n        const result = {};\r\n        each(transformString.match(/\\w+\\d*\\w*\\([^)]*\\)\\s*/g), (function(i, part) {\r\n            const translateData = parseTranslate(part);\r\n            const scaleData = part.match(/scale\\((.+?)\\)/);\r\n            const rotateData = part.match(/(rotate.)\\((.+)deg\\)/);\r\n            if (translateData) {\r\n                result.translate = translateData\r\n            }\r\n            if (scaleData && scaleData[1]) {\r\n                result.scale = parseFloat(scaleData[1])\r\n            }\r\n            if (rotateData && rotateData[1]) {\r\n                result[rotateData[1]] = parseFloat(rotateData[2])\r\n            }\r\n        }));\r\n        return result\r\n    },\r\n    stop: function($element, config, jumpToEnd) {\r\n        const frameAnimation = config && config.frameAnimation;\r\n        if (!frameAnimation) {\r\n            return\r\n        }\r\n        cancelAnimationFrame(frameAnimation.animationFrameId);\r\n        clearTimeout(frameAnimation.delayTimeout);\r\n        if (jumpToEnd) {\r\n            frameAnimation.finish()\r\n        }\r\n        delete config.frameAnimation\r\n    },\r\n    _animationStep: function($element, config) {\r\n        const frameAnimation = config && config.frameAnimation;\r\n        if (!frameAnimation) {\r\n            return\r\n        }\r\n        const now = (new Date).valueOf();\r\n        if (now >= frameAnimation.startTime + frameAnimation.duration) {\r\n            frameAnimation.finish();\r\n            return\r\n        }\r\n        frameAnimation.currentValue = this._calcStepValue(frameAnimation, now - frameAnimation.startTime);\r\n        frameAnimation.draw();\r\n        const that = this;\r\n        frameAnimation.animationFrameId = requestAnimationFrame((function() {\r\n            that._animationStep($element, config)\r\n        }))\r\n    },\r\n    _calcStepValue: function(frameAnimation, currentDuration) {\r\n        const calcValueRecursively = function(from, to) {\r\n            const result = Array.isArray(to) ? [] : {};\r\n            each(to, (function(propName, endPropValue) {\r\n                if (\"string\" === typeof endPropValue && false === parseFloat(endPropValue)) {\r\n                    return true\r\n                }\r\n                result[propName] = \"object\" === typeof endPropValue ? calcValueRecursively(from[propName], endPropValue) : function(propName) {\r\n                    const x = currentDuration / frameAnimation.duration;\r\n                    const t = currentDuration;\r\n                    const b = 1 * from[propName];\r\n                    const c = to[propName] - from[propName];\r\n                    const d = frameAnimation.duration;\r\n                    return getEasing(frameAnimation.easing)(x, t, b, c, d)\r\n                }(propName)\r\n            }));\r\n            return result\r\n        };\r\n        return calcValueRecursively(frameAnimation.from, frameAnimation.to)\r\n    },\r\n    _normalizeValue: function(value) {\r\n        const numericValue = parseFloat(value);\r\n        if (false === numericValue) {\r\n            return value\r\n        }\r\n        return numericValue\r\n    }\r\n};\r\nconst FallbackToNoAnimationStrategy = {\r\n    initAnimation: function() {},\r\n    animate: function() {\r\n        return (new Deferred).resolve().promise()\r\n    },\r\n    stop: noop,\r\n    isSynchronous: true\r\n};\r\nconst getAnimationStrategy = function(config) {\r\n    config = config || {};\r\n    const animationStrategies = {\r\n        transition: supportUtils.transition() ? TransitionAnimationStrategy : FrameAnimationStrategy,\r\n        frame: FrameAnimationStrategy,\r\n        noAnimation: FallbackToNoAnimationStrategy\r\n    };\r\n    let strategy = config.strategy || \"transition\";\r\n    if (\"css\" === config.type && !supportUtils.transition()) {\r\n        strategy = \"noAnimation\"\r\n    }\r\n    return animationStrategies[strategy]\r\n};\r\nconst baseConfigValidator = function(config, animationType, validate, typeMessage) {\r\n    each([\"from\", \"to\"], (function() {\r\n        if (!validate(config[this])) {\r\n            throw errors.Error(\"E0010\", animationType, this, typeMessage)\r\n        }\r\n    }))\r\n};\r\nconst isObjectConfigValidator = function(config, animationType) {\r\n    return baseConfigValidator(config, animationType, (function(target) {\r\n        return isPlainObject(target)\r\n    }), \"a plain object\")\r\n};\r\nconst isStringConfigValidator = function(config, animationType) {\r\n    return baseConfigValidator(config, animationType, (function(target) {\r\n        return \"string\" === typeof target\r\n    }), \"a string\")\r\n};\r\nconst CustomAnimationConfigurator = {\r\n    setup: function() {}\r\n};\r\nconst CssAnimationConfigurator = {\r\n    validateConfig: function(config) {\r\n        isStringConfigValidator(config, \"css\")\r\n    },\r\n    setup: function() {}\r\n};\r\nconst positionAliases = {\r\n    top: {\r\n        my: \"bottom center\",\r\n        at: \"top center\"\r\n    },\r\n    bottom: {\r\n        my: \"top center\",\r\n        at: \"bottom center\"\r\n    },\r\n    right: {\r\n        my: \"left center\",\r\n        at: \"right center\"\r\n    },\r\n    left: {\r\n        my: \"right center\",\r\n        at: \"left center\"\r\n    }\r\n};\r\nconst SlideAnimationConfigurator = {\r\n    validateConfig: function(config) {\r\n        isObjectConfigValidator(config, \"slide\")\r\n    },\r\n    setup: function($element, config) {\r\n        const location = locate($element);\r\n        if (\"slide\" !== config.type) {\r\n            const positioningConfig = \"slideIn\" === config.type ? config.from : config.to;\r\n            positioningConfig.position = extend({\r\n                of: window\r\n            }, positionAliases[config.direction]);\r\n            setupPosition($element, positioningConfig)\r\n        }\r\n        this._setUpConfig(location, config.from);\r\n        this._setUpConfig(location, config.to);\r\n        clearCache($element)\r\n    },\r\n    _setUpConfig: function(location, config) {\r\n        config.left = \"left\" in config ? config.left : \"+=0\";\r\n        config.top = \"top\" in config ? config.top : \"+=0\";\r\n        this._initNewPosition(location, config)\r\n    },\r\n    _initNewPosition: function(location, config) {\r\n        const position = {\r\n            left: config.left,\r\n            top: config.top\r\n        };\r\n        delete config.left;\r\n        delete config.top;\r\n        let relativeValue = this._getRelativeValue(position.left);\r\n        if (void 0 !== relativeValue) {\r\n            position.left = relativeValue + location.left\r\n        } else {\r\n            config.left = 0\r\n        }\r\n        relativeValue = this._getRelativeValue(position.top);\r\n        if (void 0 !== relativeValue) {\r\n            position.top = relativeValue + location.top\r\n        } else {\r\n            config.top = 0\r\n        }\r\n        config.transform = getTranslateCss({\r\n            x: position.left,\r\n            y: position.top\r\n        })\r\n    },\r\n    _getRelativeValue: function(value) {\r\n        let relativeValue;\r\n        if (\"string\" === typeof value && (relativeValue = RELATIVE_VALUE_REGEX.exec(value))) {\r\n            return parseInt(relativeValue[1] + \"1\") * relativeValue[2]\r\n        }\r\n    }\r\n};\r\nconst FadeAnimationConfigurator = {\r\n    setup: function($element, config) {\r\n        const from = config.from;\r\n        const to = config.to;\r\n        const defaultFromOpacity = \"fadeOut\" === config.type ? 1 : 0;\r\n        const defaultToOpacity = \"fadeOut\" === config.type ? 0 : 1;\r\n        let fromOpacity = isPlainObject(from) ? String(from.opacity ?? defaultFromOpacity) : String(from);\r\n        let toOpacity = isPlainObject(to) ? String(to.opacity ?? defaultToOpacity) : String(to);\r\n        if (!config.skipElementInitialStyles) {\r\n            fromOpacity = $element.css(\"opacity\")\r\n        }\r\n        switch (config.type) {\r\n            case \"fadeIn\":\r\n                toOpacity = 1;\r\n                break;\r\n            case \"fadeOut\":\r\n                toOpacity = 0\r\n        }\r\n        config.from = {\r\n            visibility: \"visible\",\r\n            opacity: fromOpacity\r\n        };\r\n        config.to = {\r\n            opacity: toOpacity\r\n        }\r\n    }\r\n};\r\nconst PopAnimationConfigurator = {\r\n    validateConfig: function(config) {\r\n        isObjectConfigValidator(config, \"pop\")\r\n    },\r\n    setup: function($element, config) {\r\n        const from = config.from;\r\n        const to = config.to;\r\n        const fromOpacity = \"opacity\" in from ? from.opacity : $element.css(\"opacity\");\r\n        const toOpacity = \"opacity\" in to ? to.opacity : 1;\r\n        const fromScale = \"scale\" in from ? from.scale : 0;\r\n        const toScale = \"scale\" in to ? to.scale : 1;\r\n        config.from = {\r\n            opacity: fromOpacity\r\n        };\r\n        const translate = getTranslate($element);\r\n        config.from.transform = this._getCssTransform(translate, fromScale);\r\n        config.to = {\r\n            opacity: toOpacity\r\n        };\r\n        config.to.transform = this._getCssTransform(translate, toScale)\r\n    },\r\n    _getCssTransform: function(translate, scale) {\r\n        return getTranslateCss(translate) + \"scale(\" + scale + \")\"\r\n    }\r\n};\r\nconst animationConfigurators = {\r\n    custom: CustomAnimationConfigurator,\r\n    slide: SlideAnimationConfigurator,\r\n    slideIn: SlideAnimationConfigurator,\r\n    slideOut: SlideAnimationConfigurator,\r\n    fade: FadeAnimationConfigurator,\r\n    fadeIn: FadeAnimationConfigurator,\r\n    fadeOut: FadeAnimationConfigurator,\r\n    pop: PopAnimationConfigurator,\r\n    css: CssAnimationConfigurator\r\n};\r\nconst getAnimationConfigurator = function(config) {\r\n    const result = animationConfigurators[config.type];\r\n    if (!result) {\r\n        throw errors.Error(\"E0011\", config.type)\r\n    }\r\n    return result\r\n};\r\nconst defaultJSConfig = {\r\n    type: \"custom\",\r\n    from: {},\r\n    to: {},\r\n    duration: 400,\r\n    start: noop,\r\n    complete: noop,\r\n    easing: \"ease\",\r\n    delay: 0\r\n};\r\nconst defaultCssConfig = {\r\n    duration: 400,\r\n    easing: \"ease\",\r\n    delay: 0\r\n};\r\n\r\nfunction setupAnimationOnElement() {\r\n    const $element = this.element;\r\n    const config = this.config;\r\n    setupPosition($element, config.from);\r\n    setupPosition($element, config.to);\r\n    this.configurator.setup($element, config);\r\n    $element.data(\"dxAnimData\", this);\r\n    if (fx.off) {\r\n        config.duration = 0;\r\n        config.delay = 0\r\n    }\r\n    this.strategy.initAnimation($element, config);\r\n    if (config.start) {\r\n        const element = getPublicElement($element);\r\n        config.start.apply(this, [element, config])\r\n    }\r\n}\r\nconst onElementAnimationComplete = function(animation) {\r\n    const $element = animation.element;\r\n    const config = animation.config;\r\n    $element.removeData(\"dxAnimData\");\r\n    if (config.complete) {\r\n        const element = getPublicElement($element);\r\n        config.complete.apply(this, [element, config])\r\n    }\r\n    animation.deferred.resolveWith(this, [$element, config])\r\n};\r\nconst startAnimationOnElement = function() {\r\n    const animation = this;\r\n    const $element = animation.element;\r\n    const config = animation.config;\r\n    animation.isStarted = true;\r\n    return animation.strategy.animate($element, config).done((function() {\r\n        onElementAnimationComplete(animation)\r\n    })).fail((function() {\r\n        animation.deferred.rejectWith(this, [$element, config])\r\n    }))\r\n};\r\nconst stopAnimationOnElement = function(jumpToEnd) {\r\n    const animation = this;\r\n    const $element = animation.element;\r\n    const config = animation.config;\r\n    clearTimeout(animation.startTimeout);\r\n    if (!animation.isStarted) {\r\n        animation.start()\r\n    }\r\n    animation.strategy.stop($element, config, jumpToEnd)\r\n};\r\nconst scopedRemoveEvent = addNamespace(removeEvent, \"dxFXStartAnimation\");\r\nconst subscribeToRemoveEvent = function(animation) {\r\n    eventsEngine.off(animation.element, scopedRemoveEvent);\r\n    eventsEngine.on(animation.element, scopedRemoveEvent, (function() {\r\n        fx.stop(animation.element)\r\n    }));\r\n    animation.deferred.always((function() {\r\n        eventsEngine.off(animation.element, scopedRemoveEvent)\r\n    }))\r\n};\r\nconst createAnimation = function(element, initialConfig) {\r\n    const defaultConfig = \"css\" === initialConfig.type ? defaultCssConfig : defaultJSConfig;\r\n    const config = extend(true, {}, defaultConfig, initialConfig);\r\n    const configurator = getAnimationConfigurator(config);\r\n    const strategy = getAnimationStrategy(config);\r\n    const animation = {\r\n        element: $(element),\r\n        config: config,\r\n        configurator: configurator,\r\n        strategy: strategy,\r\n        isSynchronous: strategy.isSynchronous,\r\n        setup: setupAnimationOnElement,\r\n        start: startAnimationOnElement,\r\n        stop: stopAnimationOnElement,\r\n        deferred: new Deferred\r\n    };\r\n    if (isFunction(configurator.validateConfig)) {\r\n        configurator.validateConfig(config)\r\n    }\r\n    subscribeToRemoveEvent(animation);\r\n    return animation\r\n};\r\nconst animate = function(element, config) {\r\n    const $element = $(element);\r\n    if (!$element.length) {\r\n        return (new Deferred).resolve().promise()\r\n    }\r\n    const animation = createAnimation($element, config);\r\n    pushInAnimationQueue($element, animation);\r\n    return animation.deferred.promise()\r\n};\r\n\r\nfunction pushInAnimationQueue($element, animation) {\r\n    const queueData = getAnimQueueData($element);\r\n    writeAnimQueueData($element, queueData);\r\n    queueData.push(animation);\r\n    if (!isAnimating($element)) {\r\n        shiftFromAnimationQueue($element, queueData)\r\n    }\r\n}\r\n\r\nfunction getAnimQueueData($element) {\r\n    return $element.data(\"dxAnimQueue\") || []\r\n}\r\n\r\nfunction writeAnimQueueData($element, queueData) {\r\n    $element.data(\"dxAnimQueue\", queueData)\r\n}\r\nconst destroyAnimQueueData = function($element) {\r\n    $element.removeData(\"dxAnimQueue\")\r\n};\r\n\r\nfunction isAnimating($element) {\r\n    return !!$element.data(\"dxAnimData\")\r\n}\r\n\r\nfunction shiftFromAnimationQueue($element, queueData) {\r\n    queueData = getAnimQueueData($element);\r\n    if (!queueData.length) {\r\n        return\r\n    }\r\n    const animation = queueData.shift();\r\n    if (0 === queueData.length) {\r\n        destroyAnimQueueData($element)\r\n    }\r\n    executeAnimation(animation).done((function() {\r\n        if (!isAnimating($element)) {\r\n            shiftFromAnimationQueue($element)\r\n        }\r\n    }))\r\n}\r\n\r\nfunction executeAnimation(animation) {\r\n    animation.setup();\r\n    if (fx.off || animation.isSynchronous) {\r\n        animation.start()\r\n    } else {\r\n        animation.startTimeout = setTimeout((function() {\r\n            animation.start()\r\n        }))\r\n    }\r\n    return animation.deferred.promise()\r\n}\r\n\r\nfunction setupPosition($element, config) {\r\n    if (!config || !config.position) {\r\n        return\r\n    }\r\n    const win = $(window);\r\n    let left = 0;\r\n    let top = 0;\r\n    const position = positionUtils.calculate($element, config.position);\r\n    const offset = $element.offset();\r\n    const currentPosition = $element.position();\r\n    if (currentPosition.top > offset.top) {\r\n        top = win.scrollTop()\r\n    }\r\n    if (currentPosition.left > offset.left) {\r\n        left = win.scrollLeft()\r\n    }\r\n    extend(config, {\r\n        left: position.h.location - offset.left + currentPosition.left - left,\r\n        top: position.v.location - offset.top + currentPosition.top - top\r\n    });\r\n    delete config.position\r\n}\r\n\r\nfunction setProps($element, props) {\r\n    each(props, (function(key, value) {\r\n        try {\r\n            $element.css(key, isFunction(value) ? value() : value)\r\n        } catch (e) {}\r\n    }))\r\n}\r\nconst stop = function(element, jumpToEnd) {\r\n    const $element = $(element);\r\n    const queueData = getAnimQueueData($element);\r\n    each(queueData, (function(_, animation) {\r\n        animation.config.delay = 0;\r\n        animation.config.duration = 0;\r\n        animation.isSynchronous = true\r\n    }));\r\n    if (!isAnimating($element)) {\r\n        shiftFromAnimationQueue($element, queueData)\r\n    }\r\n    const animation = $element.data(\"dxAnimData\");\r\n    if (animation) {\r\n        animation.stop(jumpToEnd)\r\n    }\r\n    $element.removeData(\"dxAnimData\");\r\n    destroyAnimQueueData($element)\r\n};\r\nconst fx = {\r\n    off: false,\r\n    animationTypes: animationConfigurators,\r\n    animate: animate,\r\n    createAnimation: createAnimation,\r\n    isAnimating: isAnimating,\r\n    stop: stop,\r\n    _simulatedTransitionEndDelay: 100\r\n};\r\nexport default fx;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAIA;AAAA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAIA;AAOA;AAIA;AAIA;AAAA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAKA;AAAA;;;AA7CA,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD;;;;;;;;;;;;;;;AA4CvB,MAAM,kBAAkB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,2KAAA,CAAA,cAAW,EAAE;;AAIlD,MAAM,uBAAuB;AAC7B,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,iBAAiB;AACvB,MAAM,8BAA8B;IAChC,eAAe,SAAS,QAAQ,EAAE,MAAM;QACpC,SAAS,GAAG,CAAC;YACT,oBAAoB;QACxB;QACA,IAAI,aAAa,OAAO,OAAO,IAAI,EAAE;YACjC,SAAS,QAAQ,CAAC,OAAO,IAAI;QACjC,OAAO;YACH,SAAS,UAAU,OAAO,IAAI;QAClC;QACA,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,MAAM,cAAc,OAAO,WAAW;QACtC,OAAO,mBAAmB,GAAG;YACzB,UAAU;YACV,QAAQ;gBACJ,KAAK,iBAAiB,CAAC;gBACvB,IAAI,aAAa;oBACb,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,UAAU,aAAa,MAAM,CAAE;wBAChC,KAAK,QAAQ,CAAC,UAAU;oBAC5B;gBACJ,OAAO;oBACH,KAAK,QAAQ,CAAC,UAAU;gBAC5B;gBACA,SAAS,WAAW,CAAC,UAAU;oBAAC;oBAAQ;iBAAS;YACrD;QACJ;QACA,IAAI,CAAC,0BAA0B,CAAC,UAAU,QAAQ,IAAI,CAAE;YACpD,OAAO,mBAAmB,CAAC,MAAM;QACrC,GAAI,IAAI,CAAE;YACN,SAAS,UAAU,CAAC,UAAU;gBAAC;gBAAQ;aAAS;QACpD;QACA,IAAI,CAAC,OAAO,QAAQ,EAAE;YAClB,OAAO,mBAAmB,CAAC,MAAM;QACrC;QACA,SAAS,GAAG,CAAC;IACjB;IACA,SAAS,SAAS,QAAQ,EAAE,MAAM;QAC9B,IAAI,CAAC,eAAe,CAAC,UAAU;QAC/B,OAAO,OAAO,mBAAmB,CAAC,QAAQ,CAAC,OAAO;IACtD;IACA,4BAA4B,SAAS,QAAQ,EAAE,MAAM;QACjD,MAAM,OAAO,IAAI;QACjB,MAAM,YAAY,KAAK,GAAG,KAAK,OAAO,KAAK;QAC3C,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,MAAM,qBAAqB,IAAI,oLAAA,CAAA,WAAQ;QACvC,MAAM,8BAA8B,IAAI,oLAAA,CAAA,WAAQ;QAChD,IAAI;QACJ,MAAM,6BAA6B,mMAAA,CAAA,UAAY,CAAC,sBAAsB,KAAK;QAC3E,OAAO,mBAAmB,CAAC,OAAO,GAAG;YACjC,aAAa;YACb,aAAa;YACb,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU;YAC3B,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU;QAC/B;QACA,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU,4BAA6B;YACpD,IAAI,KAAK,GAAG,KAAK,aAAa,OAAO,QAAQ,EAAE;gBAC3C,mBAAmB,MAAM;YAC7B;QACJ;QACA,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU;QAC3B,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,UAAU,iBAAkB;YACxC,KAAK,IAAI,CAAC,UAAU;YACpB,SAAS,MAAM;QACnB;QACA,MAAM,yBAAyB,WAAY;YACvC,yBAAyB,WAAY;gBACjC,4BAA4B,MAAM;YACtC,GAAI,OAAO,QAAQ,GAAG,OAAO,KAAK,GAAG,GAAG,4BAA4B;YACpE,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,oBAAoB,6BAA6B,IAAI,CAAC,CAAA;gBACvD,SAAS,OAAO;YACpB,CAAA,EAAE,IAAI,CAAC,IAAI;QACf;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,iBAAiB,SAAS,QAAQ,EAAE,MAAM;QACtC,SAAS,GAAG,CAAC;YACT,oBAAoB;YACpB,iBAAiB,OAAO,KAAK,GAAG;YAChC,oBAAoB,OAAO,QAAQ,GAAG;YACtC,0BAA0B,OAAO,MAAM;QAC3C;QACA,IAAI,aAAa,OAAO,OAAO,EAAE,EAAE;YAC/B,QAAQ,CAAC,EAAE,CAAC,SAAS,IAAI,MAAM,OAAO,EAAE;QAC5C,OAAO,IAAI,OAAO,EAAE,EAAE;YAClB,SAAS,UAAU,OAAO,EAAE;QAChC;IACJ;IACA,mBAAmB,SAAS,QAAQ;QAChC,SAAS,GAAG,CAAC,cAAc;IAC/B;IACA,UAAU,SAAS,QAAQ,EAAE,MAAM;QAC/B,OAAO,mBAAmB,CAAC,OAAO;QAClC,IAAI,aAAa,OAAO,OAAO,IAAI,EAAE;YACjC,SAAS,WAAW,CAAC,OAAO,IAAI;YAChC,SAAS,WAAW,CAAC,OAAO,EAAE;QAClC;IACJ;IACA,MAAM,SAAS,QAAQ,EAAE,MAAM,EAAE,SAAS;QACtC,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,IAAI,WAAW;YACX,OAAO,mBAAmB,CAAC,MAAM;QACrC,OAAO;YACH,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,EAAE,GAAG;gBAC1B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,OAAO,EAAE,EAAG,SAAS,GAAG;oBACzB,SAAS,GAAG,CAAC,KAAK,SAAS,GAAG,CAAC;gBACnC;YACJ;YACA,IAAI,CAAC,iBAAiB,CAAC;YACvB,IAAI,CAAC,QAAQ,CAAC,UAAU;QAC5B;IACJ;AACJ;AACA,MAAM,yBAAyB;IAC3B,eAAe,SAAS,QAAQ,EAAE,MAAM;QACpC,SAAS,UAAU,OAAO,IAAI;IAClC;IACA,SAAS,SAAS,QAAQ,EAAE,MAAM;QAC9B,MAAM,WAAW,IAAI,oLAAA,CAAA,WAAQ;QAC7B,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,QAAQ;YACT,OAAO,SAAS,MAAM,GAAG,OAAO;QACpC;QACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,OAAO,EAAE,EAAG,SAAS,IAAI;YAC1B,IAAI,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,EAAE;gBAC9B,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,eAAe,CAAC,SAAS,GAAG,CAAC;YAC1D;QACJ;QACA,IAAI,OAAO,EAAE,CAAC,SAAS,EAAE;YACrB,OAAO,IAAI,CAAC,SAAS,GAAG,KAAK,eAAe,CAAC,OAAO,IAAI,CAAC,SAAS;YAClE,OAAO,EAAE,CAAC,SAAS,GAAG,KAAK,eAAe,CAAC,OAAO,EAAE,CAAC,SAAS;QAClE;QACA,OAAO,cAAc,GAAG;YACpB,IAAI,OAAO,EAAE;YACb,MAAM,OAAO,IAAI;YACjB,cAAc,OAAO,IAAI;YACzB,QAAQ,CAAA,GAAA,6KAAA,CAAA,sCAAmC,AAAD,EAAE,OAAO,MAAM;YACzD,UAAU,OAAO,QAAQ;YACzB,WAAW,CAAC,IAAI,IAAI,EAAE,OAAO;YAC7B,QAAQ;gBACJ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE;gBAC3B,IAAI,CAAC,IAAI;gBACT,CAAA,GAAA,4KAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,cAAc,CAAC,gBAAgB;gBAC3D,SAAS,OAAO;YACpB;YACA,MAAM;gBACF,IAAI,OAAO,IAAI,EAAE;oBACb,OAAO,IAAI,CAAC,IAAI,CAAC,YAAY;oBAC7B;gBACJ;gBACA,MAAM,eAAe,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,YAAY;gBACjD,IAAI,aAAa,SAAS,EAAE;oBACxB,aAAa,SAAS,GAAG,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,aAAa,SAAS,EAAG,SAAS,KAAK,EAAE,IAAI;wBACtE,IAAI,gBAAgB,MAAM;4BACtB,OAAO,CAAA,GAAA,iLAAA,CAAA,kBAAe,AAAD,EAAE;wBAC3B,OAAO,IAAI,YAAY,MAAM;4BACzB,OAAO,WAAW,QAAQ;wBAC9B,OAAO,IAAI,aAAa,KAAK,MAAM,CAAC,GAAG,KAAK,MAAM,GAAG,IAAI;4BACrD,OAAO,OAAO,MAAM,QAAQ;wBAChC;oBACJ,GAAI,IAAI,CAAC;gBACb;gBACA,SAAS,GAAG,CAAC;YACjB;QACJ;QACA,IAAI,OAAO,KAAK,EAAE;YACd,OAAO,cAAc,CAAC,SAAS,IAAI,OAAO,KAAK;YAC/C,OAAO,cAAc,CAAC,YAAY,GAAG,WAAY;gBAC7C,KAAK,eAAe,CAAC,UAAU;YACnC,GAAI,OAAO,KAAK;QACpB,OAAO;YACH,KAAK,eAAe,CAAC,UAAU;QACnC;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,iBAAiB,SAAS,QAAQ,EAAE,MAAM;QACtC,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU;QAC3B,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,UAAU,iBAAkB;YACxC,IAAI,OAAO,cAAc,EAAE;gBACvB,CAAA,GAAA,4KAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,cAAc,CAAC,gBAAgB;YAC/D;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,UAAU;IAClC;IACA,iBAAiB,SAAS,eAAe;QACrC,MAAM,SAAS,CAAC;QAChB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,KAAK,CAAC,2BAA4B,SAAS,CAAC,EAAE,IAAI;YACnE,MAAM,gBAAgB,CAAA,GAAA,iLAAA,CAAA,iBAAc,AAAD,EAAE;YACrC,MAAM,YAAY,KAAK,KAAK,CAAC;YAC7B,MAAM,aAAa,KAAK,KAAK,CAAC;YAC9B,IAAI,eAAe;gBACf,OAAO,SAAS,GAAG;YACvB;YACA,IAAI,aAAa,SAAS,CAAC,EAAE,EAAE;gBAC3B,OAAO,KAAK,GAAG,WAAW,SAAS,CAAC,EAAE;YAC1C;YACA,IAAI,cAAc,UAAU,CAAC,EAAE,EAAE;gBAC7B,MAAM,CAAC,UAAU,CAAC,EAAE,CAAC,GAAG,WAAW,UAAU,CAAC,EAAE;YACpD;QACJ;QACA,OAAO;IACX;IACA,MAAM,SAAS,QAAQ,EAAE,MAAM,EAAE,SAAS;QACtC,MAAM,iBAAiB,UAAU,OAAO,cAAc;QACtD,IAAI,CAAC,gBAAgB;YACjB;QACJ;QACA,CAAA,GAAA,4KAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe,gBAAgB;QACpD,aAAa,eAAe,YAAY;QACxC,IAAI,WAAW;YACX,eAAe,MAAM;QACzB;QACA,OAAO,OAAO,cAAc;IAChC;IACA,gBAAgB,SAAS,QAAQ,EAAE,MAAM;QACrC,MAAM,iBAAiB,UAAU,OAAO,cAAc;QACtD,IAAI,CAAC,gBAAgB;YACjB;QACJ;QACA,MAAM,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO;QAC9B,IAAI,OAAO,eAAe,SAAS,GAAG,eAAe,QAAQ,EAAE;YAC3D,eAAe,MAAM;YACrB;QACJ;QACA,eAAe,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,gBAAgB,MAAM,eAAe,SAAS;QAChG,eAAe,IAAI;QACnB,MAAM,OAAO,IAAI;QACjB,eAAe,gBAAgB,GAAG,CAAA,GAAA,4KAAA,CAAA,wBAAqB,AAAD,EAAG;YACrD,KAAK,cAAc,CAAC,UAAU;QAClC;IACJ;IACA,gBAAgB,SAAS,cAAc,EAAE,eAAe;QACpD,MAAM,uBAAuB,SAAS,IAAI,EAAE,EAAE;YAC1C,MAAM,SAAS,MAAM,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;YACzC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAK,SAAS,QAAQ,EAAE,YAAY;gBACrC,IAAI,aAAa,OAAO,gBAAgB,UAAU,WAAW,eAAe;oBACxE,OAAO;gBACX;gBACA,MAAM,CAAC,SAAS,GAAG,aAAa,OAAO,eAAe,qBAAqB,IAAI,CAAC,SAAS,EAAE,gBAAgB,SAAS,QAAQ;oBACxH,MAAM,IAAI,kBAAkB,eAAe,QAAQ;oBACnD,MAAM,IAAI;oBACV,MAAM,IAAI,IAAI,IAAI,CAAC,SAAS;oBAC5B,MAAM,IAAI,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS;oBACvC,MAAM,IAAI,eAAe,QAAQ;oBACjC,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe,MAAM,EAAE,GAAG,GAAG,GAAG,GAAG;gBACxD,EAAE;YACN;YACA,OAAO;QACX;QACA,OAAO,qBAAqB,eAAe,IAAI,EAAE,eAAe,EAAE;IACtE;IACA,iBAAiB,SAAS,KAAK;QAC3B,MAAM,eAAe,WAAW;QAChC,IAAI,UAAU,cAAc;YACxB,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,MAAM,gCAAgC;IAClC,eAAe,YAAY;IAC3B,SAAS;QACL,OAAO,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO,GAAG,OAAO;IAC3C;IACA,MAAM,kLAAA,CAAA,OAAI;IACV,eAAe;AACnB;AACA,MAAM,uBAAuB,SAAS,MAAM;IACxC,SAAS,UAAU,CAAC;IACpB,MAAM,sBAAsB;QACxB,YAAY,mMAAA,CAAA,UAAY,CAAC,UAAU,KAAK,8BAA8B;QACtE,OAAO;QACP,aAAa;IACjB;IACA,IAAI,WAAW,OAAO,QAAQ,IAAI;IAClC,IAAI,UAAU,OAAO,IAAI,IAAI,CAAC,mMAAA,CAAA,UAAY,CAAC,UAAU,IAAI;QACrD,WAAW;IACf;IACA,OAAO,mBAAmB,CAAC,SAAS;AACxC;AACA,MAAM,sBAAsB,SAAS,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,WAAW;IAC7E,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAQ;KAAK,EAAG;QAClB,IAAI,CAAC,SAAS,MAAM,CAAC,IAAI,CAAC,GAAG;YACzB,MAAM,sJAAA,CAAA,UAAM,CAAC,KAAK,CAAC,SAAS,eAAe,IAAI,EAAE;QACrD;IACJ;AACJ;AACA,MAAM,0BAA0B,SAAS,MAAM,EAAE,aAAa;IAC1D,OAAO,oBAAoB,QAAQ,eAAgB,SAAS,MAAM;QAC9D,OAAO,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;IACzB,GAAI;AACR;AACA,MAAM,0BAA0B,SAAS,MAAM,EAAE,aAAa;IAC1D,OAAO,oBAAoB,QAAQ,eAAgB,SAAS,MAAM;QAC9D,OAAO,aAAa,OAAO;IAC/B,GAAI;AACR;AACA,MAAM,8BAA8B;IAChC,OAAO,YAAY;AACvB;AACA,MAAM,2BAA2B;IAC7B,gBAAgB,SAAS,MAAM;QAC3B,wBAAwB,QAAQ;IACpC;IACA,OAAO,YAAY;AACvB;AACA,MAAM,kBAAkB;IACpB,KAAK;QACD,IAAI;QACJ,IAAI;IACR;IACA,QAAQ;QACJ,IAAI;QACJ,IAAI;IACR;IACA,OAAO;QACH,IAAI;QACJ,IAAI;IACR;IACA,MAAM;QACF,IAAI;QACJ,IAAI;IACR;AACJ;AACA,MAAM,6BAA6B;IAC/B,gBAAgB,SAAS,MAAM;QAC3B,wBAAwB,QAAQ;IACpC;IACA,OAAO,SAAS,QAAQ,EAAE,MAAM;QAC5B,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,SAAM,AAAD,EAAE;QACxB,IAAI,YAAY,OAAO,IAAI,EAAE;YACzB,MAAM,oBAAoB,cAAc,OAAO,IAAI,GAAG,OAAO,IAAI,GAAG,OAAO,EAAE;YAC7E,kBAAkB,QAAQ,GAAG,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;gBAChC,IAAI;YACR,GAAG,eAAe,CAAC,OAAO,SAAS,CAAC;YACpC,cAAc,UAAU;QAC5B;QACA,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO,IAAI;QACvC,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO,EAAE;QACrC,CAAA,GAAA,iLAAA,CAAA,aAAU,AAAD,EAAE;IACf;IACA,cAAc,SAAS,QAAQ,EAAE,MAAM;QACnC,OAAO,IAAI,GAAG,UAAU,SAAS,OAAO,IAAI,GAAG;QAC/C,OAAO,GAAG,GAAG,SAAS,SAAS,OAAO,GAAG,GAAG;QAC5C,IAAI,CAAC,gBAAgB,CAAC,UAAU;IACpC;IACA,kBAAkB,SAAS,QAAQ,EAAE,MAAM;QACvC,MAAM,WAAW;YACb,MAAM,OAAO,IAAI;YACjB,KAAK,OAAO,GAAG;QACnB;QACA,OAAO,OAAO,IAAI;QAClB,OAAO,OAAO,GAAG;QACjB,IAAI,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,SAAS,IAAI;QACxD,IAAI,KAAK,MAAM,eAAe;YAC1B,SAAS,IAAI,GAAG,gBAAgB,SAAS,IAAI;QACjD,OAAO;YACH,OAAO,IAAI,GAAG;QAClB;QACA,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,SAAS,GAAG;QACnD,IAAI,KAAK,MAAM,eAAe;YAC1B,SAAS,GAAG,GAAG,gBAAgB,SAAS,GAAG;QAC/C,OAAO;YACH,OAAO,GAAG,GAAG;QACjB;QACA,OAAO,SAAS,GAAG,CAAA,GAAA,iLAAA,CAAA,kBAAe,AAAD,EAAE;YAC/B,GAAG,SAAS,IAAI;YAChB,GAAG,SAAS,GAAG;QACnB;IACJ;IACA,mBAAmB,SAAS,KAAK;QAC7B,IAAI;QACJ,IAAI,aAAa,OAAO,SAAS,CAAC,gBAAgB,qBAAqB,IAAI,CAAC,MAAM,GAAG;YACjF,OAAO,SAAS,aAAa,CAAC,EAAE,GAAG,OAAO,aAAa,CAAC,EAAE;QAC9D;IACJ;AACJ;AACA,MAAM,4BAA4B;IAC9B,OAAO,SAAS,QAAQ,EAAE,MAAM;QAC5B,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,KAAK,OAAO,EAAE;QACpB,MAAM,qBAAqB,cAAc,OAAO,IAAI,GAAG,IAAI;QAC3D,MAAM,mBAAmB,cAAc,OAAO,IAAI,GAAG,IAAI;YACV;QAA/C,IAAI,cAAc,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,OAAO,CAAA,gBAAA,KAAK,OAAO,cAAZ,2BAAA,gBAAgB,sBAAsB,OAAO;YACjD;QAA3C,IAAI,YAAY,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,OAAO,CAAA,cAAA,GAAG,OAAO,cAAV,yBAAA,cAAc,oBAAoB,OAAO;QACpF,IAAI,CAAC,OAAO,wBAAwB,EAAE;YAClC,cAAc,SAAS,GAAG,CAAC;QAC/B;QACA,OAAQ,OAAO,IAAI;YACf,KAAK;gBACD,YAAY;gBACZ;YACJ,KAAK;gBACD,YAAY;QACpB;QACA,OAAO,IAAI,GAAG;YACV,YAAY;YACZ,SAAS;QACb;QACA,OAAO,EAAE,GAAG;YACR,SAAS;QACb;IACJ;AACJ;AACA,MAAM,2BAA2B;IAC7B,gBAAgB,SAAS,MAAM;QAC3B,wBAAwB,QAAQ;IACpC;IACA,OAAO,SAAS,QAAQ,EAAE,MAAM;QAC5B,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,KAAK,OAAO,EAAE;QACpB,MAAM,cAAc,aAAa,OAAO,KAAK,OAAO,GAAG,SAAS,GAAG,CAAC;QACpE,MAAM,YAAY,aAAa,KAAK,GAAG,OAAO,GAAG;QACjD,MAAM,YAAY,WAAW,OAAO,KAAK,KAAK,GAAG;QACjD,MAAM,UAAU,WAAW,KAAK,GAAG,KAAK,GAAG;QAC3C,OAAO,IAAI,GAAG;YACV,SAAS;QACb;QACA,MAAM,YAAY,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE;QAC/B,OAAO,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW;QACzD,OAAO,EAAE,GAAG;YACR,SAAS;QACb;QACA,OAAO,EAAE,CAAC,SAAS,GAAG,IAAI,CAAC,gBAAgB,CAAC,WAAW;IAC3D;IACA,kBAAkB,SAAS,SAAS,EAAE,KAAK;QACvC,OAAO,CAAA,GAAA,iLAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,WAAW,QAAQ;IAC3D;AACJ;AACA,MAAM,yBAAyB;IAC3B,QAAQ;IACR,OAAO;IACP,SAAS;IACT,UAAU;IACV,MAAM;IACN,QAAQ;IACR,SAAS;IACT,KAAK;IACL,KAAK;AACT;AACA,MAAM,2BAA2B,SAAS,MAAM;IAC5C,MAAM,SAAS,sBAAsB,CAAC,OAAO,IAAI,CAAC;IAClD,IAAI,CAAC,QAAQ;QACT,MAAM,sJAAA,CAAA,UAAM,CAAC,KAAK,CAAC,SAAS,OAAO,IAAI;IAC3C;IACA,OAAO;AACX;AACA,MAAM,kBAAkB;IACpB,MAAM;IACN,MAAM,CAAC;IACP,IAAI,CAAC;IACL,UAAU;IACV,OAAO,kLAAA,CAAA,OAAI;IACX,UAAU,kLAAA,CAAA,OAAI;IACd,QAAQ;IACR,OAAO;AACX;AACA,MAAM,mBAAmB;IACrB,UAAU;IACV,QAAQ;IACR,OAAO;AACX;AAEA,SAAS;IACL,MAAM,WAAW,IAAI,CAAC,OAAO;IAC7B,MAAM,SAAS,IAAI,CAAC,MAAM;IAC1B,cAAc,UAAU,OAAO,IAAI;IACnC,cAAc,UAAU,OAAO,EAAE;IACjC,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,UAAU;IAClC,SAAS,IAAI,CAAC,cAAc,IAAI;IAChC,IAAI,GAAG,GAAG,EAAE;QACR,OAAO,QAAQ,GAAG;QAClB,OAAO,KAAK,GAAG;IACnB;IACA,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,UAAU;IACtC,IAAI,OAAO,KAAK,EAAE;QACd,MAAM,UAAU,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,OAAO,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;YAAC;YAAS;SAAO;IAC9C;AACJ;AACA,MAAM,6BAA6B,SAAS,SAAS;IACjD,MAAM,WAAW,UAAU,OAAO;IAClC,MAAM,SAAS,UAAU,MAAM;IAC/B,SAAS,UAAU,CAAC;IACpB,IAAI,OAAO,QAAQ,EAAE;QACjB,MAAM,UAAU,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;QACjC,OAAO,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;YAAC;YAAS;SAAO;IACjD;IACA,UAAU,QAAQ,CAAC,WAAW,CAAC,IAAI,EAAE;QAAC;QAAU;KAAO;AAC3D;AACA,MAAM,0BAA0B;IAC5B,MAAM,YAAY,IAAI;IACtB,MAAM,WAAW,UAAU,OAAO;IAClC,MAAM,SAAS,UAAU,MAAM;IAC/B,UAAU,SAAS,GAAG;IACtB,OAAO,UAAU,QAAQ,CAAC,OAAO,CAAC,UAAU,QAAQ,IAAI,CAAE;QACtD,2BAA2B;IAC/B,GAAI,IAAI,CAAE;QACN,UAAU,QAAQ,CAAC,UAAU,CAAC,IAAI,EAAE;YAAC;YAAU;SAAO;IAC1D;AACJ;AACA,MAAM,yBAAyB,SAAS,SAAS;IAC7C,MAAM,YAAY,IAAI;IACtB,MAAM,WAAW,UAAU,OAAO;IAClC,MAAM,SAAS,UAAU,MAAM;IAC/B,aAAa,UAAU,YAAY;IACnC,IAAI,CAAC,UAAU,SAAS,EAAE;QACtB,UAAU,KAAK;IACnB;IACA,UAAU,QAAQ,CAAC,IAAI,CAAC,UAAU,QAAQ;AAC9C;AACA,MAAM,oBAAoB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,2KAAA,CAAA,cAAW,EAAE;AACpD,MAAM,yBAAyB,SAAS,SAAS;IAC7C,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;IACpC,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,UAAU,OAAO,EAAE,mBAAoB;QACnD,GAAG,IAAI,CAAC,UAAU,OAAO;IAC7B;IACA,UAAU,QAAQ,CAAC,MAAM,CAAE;QACvB,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,UAAU,OAAO,EAAE;IACxC;AACJ;AACA,MAAM,kBAAkB,SAAS,OAAO,EAAE,aAAa;IACnD,MAAM,gBAAgB,UAAU,cAAc,IAAI,GAAG,mBAAmB;IACxE,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,eAAe;IAC/C,MAAM,eAAe,yBAAyB;IAC9C,MAAM,WAAW,qBAAqB;IACtC,MAAM,YAAY;QACd,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACX,QAAQ;QACR,cAAc;QACd,UAAU;QACV,eAAe,SAAS,aAAa;QACrC,OAAO;QACP,OAAO;QACP,MAAM;QACN,UAAU,IAAI,oLAAA,CAAA,WAAQ;IAC1B;IACA,IAAI,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,aAAa,cAAc,GAAG;QACzC,aAAa,cAAc,CAAC;IAChC;IACA,uBAAuB;IACvB,OAAO;AACX;AACA,MAAM,UAAU,SAAS,OAAO,EAAE,MAAM;IACpC,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACnB,IAAI,CAAC,SAAS,MAAM,EAAE;QAClB,OAAO,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO,GAAG,OAAO;IAC3C;IACA,MAAM,YAAY,gBAAgB,UAAU;IAC5C,qBAAqB,UAAU;IAC/B,OAAO,UAAU,QAAQ,CAAC,OAAO;AACrC;AAEA,SAAS,qBAAqB,QAAQ,EAAE,SAAS;IAC7C,MAAM,YAAY,iBAAiB;IACnC,mBAAmB,UAAU;IAC7B,UAAU,IAAI,CAAC;IACf,IAAI,CAAC,YAAY,WAAW;QACxB,wBAAwB,UAAU;IACtC;AACJ;AAEA,SAAS,iBAAiB,QAAQ;IAC9B,OAAO,SAAS,IAAI,CAAC,kBAAkB,EAAE;AAC7C;AAEA,SAAS,mBAAmB,QAAQ,EAAE,SAAS;IAC3C,SAAS,IAAI,CAAC,eAAe;AACjC;AACA,MAAM,uBAAuB,SAAS,QAAQ;IAC1C,SAAS,UAAU,CAAC;AACxB;AAEA,SAAS,YAAY,QAAQ;IACzB,OAAO,CAAC,CAAC,SAAS,IAAI,CAAC;AAC3B;AAEA,SAAS,wBAAwB,QAAQ,EAAE,SAAS;IAChD,YAAY,iBAAiB;IAC7B,IAAI,CAAC,UAAU,MAAM,EAAE;QACnB;IACJ;IACA,MAAM,YAAY,UAAU,KAAK;IACjC,IAAI,MAAM,UAAU,MAAM,EAAE;QACxB,qBAAqB;IACzB;IACA,iBAAiB,WAAW,IAAI,CAAE;QAC9B,IAAI,CAAC,YAAY,WAAW;YACxB,wBAAwB;QAC5B;IACJ;AACJ;AAEA,SAAS,iBAAiB,SAAS;IAC/B,UAAU,KAAK;IACf,IAAI,GAAG,GAAG,IAAI,UAAU,aAAa,EAAE;QACnC,UAAU,KAAK;IACnB,OAAO;QACH,UAAU,YAAY,GAAG,WAAY;YACjC,UAAU,KAAK;QACnB;IACJ;IACA,OAAO,UAAU,QAAQ,CAAC,OAAO;AACrC;AAEA,SAAS,cAAc,QAAQ,EAAE,MAAM;IACnC,IAAI,CAAC,UAAU,CAAC,OAAO,QAAQ,EAAE;QAC7B;IACJ;IACA,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACd,IAAI,OAAO;IACX,IAAI,MAAM;IACV,MAAM,WAAW,+KAAA,CAAA,UAAa,CAAC,SAAS,CAAC,UAAU,OAAO,QAAQ;IAClE,MAAM,SAAS,SAAS,MAAM;IAC9B,MAAM,kBAAkB,SAAS,QAAQ;IACzC,IAAI,gBAAgB,GAAG,GAAG,OAAO,GAAG,EAAE;QAClC,MAAM,IAAI,SAAS;IACvB;IACA,IAAI,gBAAgB,IAAI,GAAG,OAAO,IAAI,EAAE;QACpC,OAAO,IAAI,UAAU;IACzB;IACA,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;QACX,MAAM,SAAS,CAAC,CAAC,QAAQ,GAAG,OAAO,IAAI,GAAG,gBAAgB,IAAI,GAAG;QACjE,KAAK,SAAS,CAAC,CAAC,QAAQ,GAAG,OAAO,GAAG,GAAG,gBAAgB,GAAG,GAAG;IAClE;IACA,OAAO,OAAO,QAAQ;AAC1B;AAEA,SAAS,SAAS,QAAQ,EAAE,KAAK;IAC7B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,SAAS,GAAG,EAAE,KAAK;QAC5B,IAAI;YACA,SAAS,GAAG,CAAC,KAAK,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,SAAS,UAAU;QACpD,EAAE,OAAO,GAAG,CAAC;IACjB;AACJ;AACA,MAAM,OAAO,SAAS,OAAO,EAAE,SAAS;IACpC,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;IACnB,MAAM,YAAY,iBAAiB;IACnC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,WAAY,SAAS,CAAC,EAAE,SAAS;QAClC,UAAU,MAAM,CAAC,KAAK,GAAG;QACzB,UAAU,MAAM,CAAC,QAAQ,GAAG;QAC5B,UAAU,aAAa,GAAG;IAC9B;IACA,IAAI,CAAC,YAAY,WAAW;QACxB,wBAAwB,UAAU;IACtC;IACA,MAAM,YAAY,SAAS,IAAI,CAAC;IAChC,IAAI,WAAW;QACX,UAAU,IAAI,CAAC;IACnB;IACA,SAAS,UAAU,CAAC;IACpB,qBAAqB;AACzB;AACA,MAAM,KAAK;IACP,KAAK;IACL,gBAAgB;IAChB,SAAS;IACT,iBAAiB;IACjB,aAAa;IACb,MAAM;IACN,8BAA8B;AAClC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3283, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/presets/presets.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/presets/presets.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getWidth\r\n} from \"../../../../core/utils/size\";\r\nimport {\r\n    Component\r\n} from \"../../../../core/component\";\r\nimport {\r\n    each\r\n} from \"../../../../core/utils/iterator\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport devices from \"../../environment/devices\";\r\nimport fx from \"../fx\";\r\nconst directionPostfixes = {\r\n    forward: \" dx-forward\",\r\n    backward: \" dx-backward\",\r\n    none: \" dx-no-direction\",\r\n    undefined: \" dx-no-direction\"\r\n};\r\nconst optionPrefix = \"preset_\";\r\nconst AnimationPresetCollection = Component.inherit({\r\n    ctor: function() {\r\n        this.callBase.apply(this, arguments);\r\n        this._registeredPresets = [];\r\n        this.resetToDefaults()\r\n    },\r\n    _getDefaultOptions: function() {\r\n        return extend(this.callBase(), {\r\n            defaultAnimationDuration: 400,\r\n            defaultAnimationDelay: 0,\r\n            defaultStaggerAnimationDuration: 300,\r\n            defaultStaggerAnimationDelay: 40,\r\n            defaultStaggerAnimationStartDelay: 500\r\n        })\r\n    },\r\n    _defaultOptionsRules: function() {\r\n        return this.callBase().concat([{\r\n            device: function(device) {\r\n                return device.phone\r\n            },\r\n            options: {\r\n                defaultStaggerAnimationDuration: 350,\r\n                defaultStaggerAnimationDelay: 50,\r\n                defaultStaggerAnimationStartDelay: 0\r\n            }\r\n        }, {\r\n            device: function() {\r\n                return devices.current().android || devices.real.android\r\n            },\r\n            options: {\r\n                defaultAnimationDelay: 100\r\n            }\r\n        }])\r\n    },\r\n    _getPresetOptionName: function(animationName) {\r\n        return \"preset_\" + animationName\r\n    },\r\n    _createAndroidSlideAnimationConfig: function(throughOpacity, widthMultiplier) {\r\n        const that = this;\r\n        const createBaseConfig = function(configModifier) {\r\n            return {\r\n                type: \"slide\",\r\n                delay: void 0 === configModifier.delay ? that.option(\"defaultAnimationDelay\") : configModifier.delay,\r\n                duration: void 0 === configModifier.duration ? that.option(\"defaultAnimationDuration\") : configModifier.duration\r\n            }\r\n        };\r\n        return {\r\n            enter: function($element, configModifier) {\r\n                const width = getWidth($element.parent()) * widthMultiplier;\r\n                const direction = configModifier.direction;\r\n                const config = createBaseConfig(configModifier);\r\n                config.to = {\r\n                    left: 0,\r\n                    opacity: 1\r\n                };\r\n                if (\"forward\" === direction) {\r\n                    config.from = {\r\n                        left: width,\r\n                        opacity: throughOpacity\r\n                    }\r\n                } else if (\"backward\" === direction) {\r\n                    config.from = {\r\n                        left: -width,\r\n                        opacity: throughOpacity\r\n                    }\r\n                } else {\r\n                    config.from = {\r\n                        left: 0,\r\n                        opacity: 0\r\n                    }\r\n                }\r\n                return fx.createAnimation($element, config)\r\n            },\r\n            leave: function($element, configModifier) {\r\n                const width = getWidth($element.parent()) * widthMultiplier;\r\n                const direction = configModifier.direction;\r\n                const config = createBaseConfig(configModifier);\r\n                config.from = {\r\n                    left: 0,\r\n                    opacity: 1\r\n                };\r\n                if (\"forward\" === direction) {\r\n                    config.to = {\r\n                        left: -width,\r\n                        opacity: throughOpacity\r\n                    }\r\n                } else if (\"backward\" === direction) {\r\n                    config.to = {\r\n                        left: width,\r\n                        opacity: throughOpacity\r\n                    }\r\n                } else {\r\n                    config.to = {\r\n                        left: 0,\r\n                        opacity: 0\r\n                    }\r\n                }\r\n                return fx.createAnimation($element, config)\r\n            }\r\n        }\r\n    },\r\n    _createOpenDoorConfig: function() {\r\n        const that = this;\r\n        const createBaseConfig = function(configModifier) {\r\n            return {\r\n                type: \"css\",\r\n                extraCssClasses: \"dx-opendoor-animation\",\r\n                delay: void 0 === configModifier.delay ? that.option(\"defaultAnimationDelay\") : configModifier.delay,\r\n                duration: void 0 === configModifier.duration ? that.option(\"defaultAnimationDuration\") : configModifier.duration\r\n            }\r\n        };\r\n        return {\r\n            enter: function($element, configModifier) {\r\n                const direction = configModifier.direction;\r\n                const config = createBaseConfig(configModifier);\r\n                config.delay = \"none\" === direction ? config.delay : config.duration;\r\n                config.from = \"dx-enter dx-opendoor-animation\" + directionPostfixes[direction];\r\n                config.to = \"dx-enter-active\";\r\n                return fx.createAnimation($element, config)\r\n            },\r\n            leave: function($element, configModifier) {\r\n                const direction = configModifier.direction;\r\n                const config = createBaseConfig(configModifier);\r\n                config.from = \"dx-leave dx-opendoor-animation\" + directionPostfixes[direction];\r\n                config.to = \"dx-leave-active\";\r\n                return fx.createAnimation($element, config)\r\n            }\r\n        }\r\n    },\r\n    _createWinPopConfig: function() {\r\n        const that = this;\r\n        const baseConfig = {\r\n            type: \"css\",\r\n            extraCssClasses: \"dx-win-pop-animation\",\r\n            duration: that.option(\"defaultAnimationDuration\")\r\n        };\r\n        return {\r\n            enter: function($element, configModifier) {\r\n                const config = baseConfig;\r\n                const direction = configModifier.direction;\r\n                config.delay = \"none\" === direction ? that.option(\"defaultAnimationDelay\") : that.option(\"defaultAnimationDuration\") / 2;\r\n                config.from = \"dx-enter dx-win-pop-animation\" + directionPostfixes[direction];\r\n                config.to = \"dx-enter-active\";\r\n                return fx.createAnimation($element, config)\r\n            },\r\n            leave: function($element, configModifier) {\r\n                const config = baseConfig;\r\n                const direction = configModifier.direction;\r\n                config.delay = that.option(\"defaultAnimationDelay\");\r\n                config.from = \"dx-leave dx-win-pop-animation\" + directionPostfixes[direction];\r\n                config.to = \"dx-leave-active\";\r\n                return fx.createAnimation($element, config)\r\n            }\r\n        }\r\n    },\r\n    resetToDefaults: function() {\r\n        this.clear();\r\n        this.registerDefaultPresets();\r\n        this.applyChanges()\r\n    },\r\n    clear: function(name) {\r\n        const that = this;\r\n        const newRegisteredPresets = [];\r\n        each(this._registeredPresets, (function(index, preset) {\r\n            if (!name || name === preset.name) {\r\n                that.option(that._getPresetOptionName(preset.name), void 0)\r\n            } else {\r\n                newRegisteredPresets.push(preset)\r\n            }\r\n        }));\r\n        this._registeredPresets = newRegisteredPresets;\r\n        this.applyChanges()\r\n    },\r\n    registerPreset: function(name, config) {\r\n        this._registeredPresets.push({\r\n            name: name,\r\n            config: config\r\n        })\r\n    },\r\n    applyChanges: function() {\r\n        const that = this;\r\n        const customRules = [];\r\n        each(this._registeredPresets, (function(index, preset) {\r\n            const rule = {\r\n                device: preset.config.device,\r\n                options: {}\r\n            };\r\n            rule.options[that._getPresetOptionName(preset.name)] = preset.config.animation;\r\n            customRules.push(rule)\r\n        }));\r\n        this._setOptionsByDevice(customRules)\r\n    },\r\n    getPreset: function(name) {\r\n        let result = name;\r\n        while (\"string\" === typeof result) {\r\n            result = this.option(this._getPresetOptionName(result))\r\n        }\r\n        return result\r\n    },\r\n    registerDefaultPresets: function() {\r\n        this.registerPreset(\"pop\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-android-pop-animation\",\r\n                delay: this.option(\"defaultAnimationDelay\"),\r\n                duration: this.option(\"defaultAnimationDuration\")\r\n            }\r\n        });\r\n        this.registerPreset(\"openDoor\", {\r\n            animation: this._createOpenDoorConfig()\r\n        });\r\n        this.registerPreset(\"win-pop\", {\r\n            animation: this._createWinPopConfig()\r\n        });\r\n        this.registerPreset(\"fade\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-fade-animation\",\r\n                delay: this.option(\"defaultAnimationDelay\"),\r\n                duration: this.option(\"defaultAnimationDuration\")\r\n            }\r\n        });\r\n        this.registerPreset(\"slide\", {\r\n            device: function() {\r\n                return devices.current().android || devices.real.android\r\n            },\r\n            animation: this._createAndroidSlideAnimationConfig(1, 1)\r\n        });\r\n        this.registerPreset(\"slide\", {\r\n            device: function() {\r\n                return !devices.current().android && !devices.real.android\r\n            },\r\n            animation: {\r\n                extraCssClasses: \"dx-slide-animation\",\r\n                delay: this.option(\"defaultAnimationDelay\"),\r\n                duration: this.option(\"defaultAnimationDuration\")\r\n            }\r\n        });\r\n        this.registerPreset(\"ios7-slide\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-ios7-slide-animation\",\r\n                delay: this.option(\"defaultAnimationDelay\"),\r\n                duration: this.option(\"defaultAnimationDuration\")\r\n            }\r\n        });\r\n        this.registerPreset(\"overflow\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-overflow-animation\",\r\n                delay: this.option(\"defaultAnimationDelay\"),\r\n                duration: this.option(\"defaultAnimationDuration\")\r\n            }\r\n        });\r\n        this.registerPreset(\"ios7-toolbar\", {\r\n            device: function() {\r\n                return !devices.current().android && !devices.real.android\r\n            },\r\n            animation: {\r\n                extraCssClasses: \"dx-ios7-toolbar-animation\",\r\n                delay: this.option(\"defaultAnimationDelay\"),\r\n                duration: this.option(\"defaultAnimationDuration\")\r\n            }\r\n        });\r\n        this.registerPreset(\"ios7-toolbar\", {\r\n            device: function() {\r\n                return devices.current().android || devices.real.android\r\n            },\r\n            animation: this._createAndroidSlideAnimationConfig(0, .4)\r\n        });\r\n        this.registerPreset(\"stagger-fade\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-fade-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-slide\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-slide-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-fade-slide\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-fade-slide-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-drop\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-drop-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-fade-drop\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-fade-drop-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-fade-rise\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-fade-rise-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-3d-drop\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-3d-drop-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        });\r\n        this.registerPreset(\"stagger-fade-zoom\", {\r\n            animation: {\r\n                extraCssClasses: \"dx-fade-zoom-animation\",\r\n                staggerDelay: this.option(\"defaultStaggerAnimationDelay\"),\r\n                duration: this.option(\"defaultStaggerAnimationDuration\"),\r\n                delay: this.option(\"defaultStaggerAnimationStartDelay\")\r\n            }\r\n        })\r\n    }\r\n});\r\nconst animationPresets = new AnimationPresetCollection;\r\nexport {\r\n    animationPresets as presets, AnimationPresetCollection as PresetCollection\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AACA;;;;;;;AACA,MAAM,qBAAqB;IACvB,SAAS;IACT,UAAU;IACV,MAAM;IACN,WAAW;AACf;AACA,MAAM,eAAe;AACrB,MAAM,4BAA4B,oLAAA,CAAA,YAAS,CAAC,OAAO,CAAC;IAChD,MAAM;QACF,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,kBAAkB,GAAG,EAAE;QAC5B,IAAI,CAAC,eAAe;IACxB;IACA,oBAAoB;QAChB,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;YAC3B,0BAA0B;YAC1B,uBAAuB;YACvB,iCAAiC;YACjC,8BAA8B;YAC9B,mCAAmC;QACvC;IACJ;IACA,sBAAsB;QAClB,OAAO,IAAI,CAAC,QAAQ,GAAG,MAAM,CAAC;YAAC;gBAC3B,QAAQ,SAAS,MAAM;oBACnB,OAAO,OAAO,KAAK;gBACvB;gBACA,SAAS;oBACL,iCAAiC;oBACjC,8BAA8B;oBAC9B,mCAAmC;gBACvC;YACJ;YAAG;gBACC,QAAQ;oBACJ,OAAO,0KAAA,CAAA,UAAO,CAAC,OAAO,GAAG,OAAO,IAAI,0KAAA,CAAA,UAAO,CAAC,IAAI,CAAC,OAAO;gBAC5D;gBACA,SAAS;oBACL,uBAAuB;gBAC3B;YACJ;SAAE;IACN;IACA,sBAAsB,SAAS,aAAa;QACxC,OAAO,YAAY;IACvB;IACA,oCAAoC,SAAS,cAAc,EAAE,eAAe;QACxE,MAAM,OAAO,IAAI;QACjB,MAAM,mBAAmB,SAAS,cAAc;YAC5C,OAAO;gBACH,MAAM;gBACN,OAAO,KAAK,MAAM,eAAe,KAAK,GAAG,KAAK,MAAM,CAAC,2BAA2B,eAAe,KAAK;gBACpG,UAAU,KAAK,MAAM,eAAe,QAAQ,GAAG,KAAK,MAAM,CAAC,8BAA8B,eAAe,QAAQ;YACpH;QACJ;QACA,OAAO;YACH,OAAO,SAAS,QAAQ,EAAE,cAAc;gBACpC,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,MAAM,MAAM;gBAC5C,MAAM,YAAY,eAAe,SAAS;gBAC1C,MAAM,SAAS,iBAAiB;gBAChC,OAAO,EAAE,GAAG;oBACR,MAAM;oBACN,SAAS;gBACb;gBACA,IAAI,cAAc,WAAW;oBACzB,OAAO,IAAI,GAAG;wBACV,MAAM;wBACN,SAAS;oBACb;gBACJ,OAAO,IAAI,eAAe,WAAW;oBACjC,OAAO,IAAI,GAAG;wBACV,MAAM,CAAC;wBACP,SAAS;oBACb;gBACJ,OAAO;oBACH,OAAO,IAAI,GAAG;wBACV,MAAM;wBACN,SAAS;oBACb;gBACJ;gBACA,OAAO,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;YACxC;YACA,OAAO,SAAS,QAAQ,EAAE,cAAc;gBACpC,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,MAAM,MAAM;gBAC5C,MAAM,YAAY,eAAe,SAAS;gBAC1C,MAAM,SAAS,iBAAiB;gBAChC,OAAO,IAAI,GAAG;oBACV,MAAM;oBACN,SAAS;gBACb;gBACA,IAAI,cAAc,WAAW;oBACzB,OAAO,EAAE,GAAG;wBACR,MAAM,CAAC;wBACP,SAAS;oBACb;gBACJ,OAAO,IAAI,eAAe,WAAW;oBACjC,OAAO,EAAE,GAAG;wBACR,MAAM;wBACN,SAAS;oBACb;gBACJ,OAAO;oBACH,OAAO,EAAE,GAAG;wBACR,MAAM;wBACN,SAAS;oBACb;gBACJ;gBACA,OAAO,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;YACxC;QACJ;IACJ;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,MAAM,mBAAmB,SAAS,cAAc;YAC5C,OAAO;gBACH,MAAM;gBACN,iBAAiB;gBACjB,OAAO,KAAK,MAAM,eAAe,KAAK,GAAG,KAAK,MAAM,CAAC,2BAA2B,eAAe,KAAK;gBACpG,UAAU,KAAK,MAAM,eAAe,QAAQ,GAAG,KAAK,MAAM,CAAC,8BAA8B,eAAe,QAAQ;YACpH;QACJ;QACA,OAAO;YACH,OAAO,SAAS,QAAQ,EAAE,cAAc;gBACpC,MAAM,YAAY,eAAe,SAAS;gBAC1C,MAAM,SAAS,iBAAiB;gBAChC,OAAO,KAAK,GAAG,WAAW,YAAY,OAAO,KAAK,GAAG,OAAO,QAAQ;gBACpE,OAAO,IAAI,GAAG,mCAAmC,kBAAkB,CAAC,UAAU;gBAC9E,OAAO,EAAE,GAAG;gBACZ,OAAO,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;YACxC;YACA,OAAO,SAAS,QAAQ,EAAE,cAAc;gBACpC,MAAM,YAAY,eAAe,SAAS;gBAC1C,MAAM,SAAS,iBAAiB;gBAChC,OAAO,IAAI,GAAG,mCAAmC,kBAAkB,CAAC,UAAU;gBAC9E,OAAO,EAAE,GAAG;gBACZ,OAAO,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;YACxC;QACJ;IACJ;IACA,qBAAqB;QACjB,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa;YACf,MAAM;YACN,iBAAiB;YACjB,UAAU,KAAK,MAAM,CAAC;QAC1B;QACA,OAAO;YACH,OAAO,SAAS,QAAQ,EAAE,cAAc;gBACpC,MAAM,SAAS;gBACf,MAAM,YAAY,eAAe,SAAS;gBAC1C,OAAO,KAAK,GAAG,WAAW,YAAY,KAAK,MAAM,CAAC,2BAA2B,KAAK,MAAM,CAAC,8BAA8B;gBACvH,OAAO,IAAI,GAAG,kCAAkC,kBAAkB,CAAC,UAAU;gBAC7E,OAAO,EAAE,GAAG;gBACZ,OAAO,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;YACxC;YACA,OAAO,SAAS,QAAQ,EAAE,cAAc;gBACpC,MAAM,SAAS;gBACf,MAAM,YAAY,eAAe,SAAS;gBAC1C,OAAO,KAAK,GAAG,KAAK,MAAM,CAAC;gBAC3B,OAAO,IAAI,GAAG,kCAAkC,kBAAkB,CAAC,UAAU;gBAC7E,OAAO,EAAE,GAAG;gBACZ,OAAO,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;YACxC;QACJ;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,YAAY;IACrB;IACA,OAAO,SAAS,IAAI;QAChB,MAAM,OAAO,IAAI;QACjB,MAAM,uBAAuB,EAAE;QAC/B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,kBAAkB,EAAG,SAAS,KAAK,EAAE,MAAM;YACjD,IAAI,CAAC,QAAQ,SAAS,OAAO,IAAI,EAAE;gBAC/B,KAAK,MAAM,CAAC,KAAK,oBAAoB,CAAC,OAAO,IAAI,GAAG,KAAK;YAC7D,OAAO;gBACH,qBAAqB,IAAI,CAAC;YAC9B;QACJ;QACA,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,YAAY;IACrB;IACA,gBAAgB,SAAS,IAAI,EAAE,MAAM;QACjC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC;YACzB,MAAM;YACN,QAAQ;QACZ;IACJ;IACA,cAAc;QACV,MAAM,OAAO,IAAI;QACjB,MAAM,cAAc,EAAE;QACtB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,kBAAkB,EAAG,SAAS,KAAK,EAAE,MAAM;YACjD,MAAM,OAAO;gBACT,QAAQ,OAAO,MAAM,CAAC,MAAM;gBAC5B,SAAS,CAAC;YACd;YACA,KAAK,OAAO,CAAC,KAAK,oBAAoB,CAAC,OAAO,IAAI,EAAE,GAAG,OAAO,MAAM,CAAC,SAAS;YAC9E,YAAY,IAAI,CAAC;QACrB;QACA,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA,WAAW,SAAS,IAAI;QACpB,IAAI,SAAS;QACb,MAAO,aAAa,OAAO,OAAQ;YAC/B,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB,CAAC;QACnD;QACA,OAAO;IACX;IACA,wBAAwB;QACpB,IAAI,CAAC,cAAc,CAAC,OAAO;YACvB,WAAW;gBACP,iBAAiB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACnB,UAAU,IAAI,CAAC,MAAM,CAAC;YAC1B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,YAAY;YAC5B,WAAW,IAAI,CAAC,qBAAqB;QACzC;QACA,IAAI,CAAC,cAAc,CAAC,WAAW;YAC3B,WAAW,IAAI,CAAC,mBAAmB;QACvC;QACA,IAAI,CAAC,cAAc,CAAC,QAAQ;YACxB,WAAW;gBACP,iBAAiB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACnB,UAAU,IAAI,CAAC,MAAM,CAAC;YAC1B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,SAAS;YACzB,QAAQ;gBACJ,OAAO,0KAAA,CAAA,UAAO,CAAC,OAAO,GAAG,OAAO,IAAI,0KAAA,CAAA,UAAO,CAAC,IAAI,CAAC,OAAO;YAC5D;YACA,WAAW,IAAI,CAAC,kCAAkC,CAAC,GAAG;QAC1D;QACA,IAAI,CAAC,cAAc,CAAC,SAAS;YACzB,QAAQ;gBACJ,OAAO,CAAC,0KAAA,CAAA,UAAO,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,0KAAA,CAAA,UAAO,CAAC,IAAI,CAAC,OAAO;YAC9D;YACA,WAAW;gBACP,iBAAiB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACnB,UAAU,IAAI,CAAC,MAAM,CAAC;YAC1B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,cAAc;YAC9B,WAAW;gBACP,iBAAiB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACnB,UAAU,IAAI,CAAC,MAAM,CAAC;YAC1B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,YAAY;YAC5B,WAAW;gBACP,iBAAiB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACnB,UAAU,IAAI,CAAC,MAAM,CAAC;YAC1B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,gBAAgB;YAChC,QAAQ;gBACJ,OAAO,CAAC,0KAAA,CAAA,UAAO,CAAC,OAAO,GAAG,OAAO,IAAI,CAAC,0KAAA,CAAA,UAAO,CAAC,IAAI,CAAC,OAAO;YAC9D;YACA,WAAW;gBACP,iBAAiB;gBACjB,OAAO,IAAI,CAAC,MAAM,CAAC;gBACnB,UAAU,IAAI,CAAC,MAAM,CAAC;YAC1B;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,gBAAgB;YAChC,QAAQ;gBACJ,OAAO,0KAAA,CAAA,UAAO,CAAC,OAAO,GAAG,OAAO,IAAI,0KAAA,CAAA,UAAO,CAAC,IAAI,CAAC,OAAO;YAC5D;YACA,WAAW,IAAI,CAAC,kCAAkC,CAAC,GAAG;QAC1D;QACA,IAAI,CAAC,cAAc,CAAC,gBAAgB;YAChC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,iBAAiB;YACjC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,sBAAsB;YACtC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,gBAAgB;YAChC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,qBAAqB;YACrC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,qBAAqB;YACrC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,mBAAmB;YACnC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,qBAAqB;YACrC,WAAW;gBACP,iBAAiB;gBACjB,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,UAAU,IAAI,CAAC,MAAM,CAAC;gBACtB,OAAO,IAAI,CAAC,MAAM,CAAC;YACvB;QACJ;IACJ;AACJ;AACA,MAAM,mBAAmB,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3659, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/presets.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/presets.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    presets\r\n} from \"./presets/presets\";\r\nexport default presets;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe,yLAAA,CAAA,UAAO", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3676, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/transition_executor/transition_executor.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/transition_executor/transition_executor.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../../core/renderer\";\r\nimport Class from \"../../../../core/class\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport commonUtils from \"../../../../__internal/core/utils/m_common\";\r\nimport {\r\n    isFunction,\r\n    isPlainObject\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    map\r\n} from \"../../../../core/utils/iterator\";\r\nimport fx from \"../fx\";\r\nimport {\r\n    presets\r\n} from \"../presets/presets\";\r\nimport {\r\n    when,\r\n    Deferred\r\n} from \"../../../../core/utils/deferred\";\r\nconst directionPostfixes = {\r\n    forward: \" dx-forward\",\r\n    backward: \" dx-backward\",\r\n    none: \" dx-no-direction\",\r\n    undefined: \" dx-no-direction\"\r\n};\r\nconst DX_ANIMATING_CLASS = \"dx-animating\";\r\nexport const TransitionExecutor = Class.inherit({\r\n    ctor: function() {\r\n        this._accumulatedDelays = {\r\n            enter: 0,\r\n            leave: 0\r\n        };\r\n        this._animations = [];\r\n        this.reset()\r\n    },\r\n    _createAnimations: function($elements, initialConfig, configModifier, type) {\r\n        $elements = $($elements);\r\n        const that = this;\r\n        const result = [];\r\n        configModifier = configModifier || {};\r\n        const animationConfig = this._prepareElementAnimationConfig(initialConfig, configModifier, type);\r\n        if (animationConfig) {\r\n            $elements.each((function() {\r\n                const animation = that._createAnimation($(this), animationConfig, configModifier);\r\n                if (animation) {\r\n                    animation.element.addClass(\"dx-animating\");\r\n                    animation.setup();\r\n                    result.push(animation)\r\n                }\r\n            }))\r\n        }\r\n        return result\r\n    },\r\n    _prepareElementAnimationConfig: function(config, configModifier, type) {\r\n        let result;\r\n        if (\"string\" === typeof config) {\r\n            const presetName = config;\r\n            config = presets.getPreset(presetName)\r\n        }\r\n        if (!config) {\r\n            result = void 0\r\n        } else if (isFunction(config[type])) {\r\n            result = config[type]\r\n        } else {\r\n            result = extend({\r\n                skipElementInitialStyles: true,\r\n                cleanupWhen: this._completePromise\r\n            }, config, configModifier);\r\n            if (!result.type || \"css\" === result.type) {\r\n                const cssClass = \"dx-\" + type;\r\n                const extraCssClasses = (result.extraCssClasses ? \" \" + result.extraCssClasses : \"\") + directionPostfixes[result.direction];\r\n                result.type = \"css\";\r\n                result.from = (result.from || cssClass) + extraCssClasses;\r\n                result.to = result.to || cssClass + \"-active\"\r\n            }\r\n            result.staggerDelay = result.staggerDelay || 0;\r\n            result.delay = result.delay || 0;\r\n            if (result.staggerDelay) {\r\n                result.delay += this._accumulatedDelays[type];\r\n                this._accumulatedDelays[type] += result.staggerDelay\r\n            }\r\n        }\r\n        return result\r\n    },\r\n    _createAnimation: function($element, animationConfig, configModifier) {\r\n        let result;\r\n        if (isPlainObject(animationConfig)) {\r\n            result = fx.createAnimation($element, animationConfig)\r\n        } else if (isFunction(animationConfig)) {\r\n            result = animationConfig($element, configModifier)\r\n        }\r\n        return result\r\n    },\r\n    _startAnimations: function() {\r\n        const animations = this._animations;\r\n        for (let i = 0; i < animations.length; i++) {\r\n            animations[i].start()\r\n        }\r\n    },\r\n    _stopAnimations: function(jumpToEnd) {\r\n        const animations = this._animations;\r\n        for (let i = 0; i < animations.length; i++) {\r\n            animations[i].stop(jumpToEnd)\r\n        }\r\n    },\r\n    _clearAnimations: function() {\r\n        const animations = this._animations;\r\n        for (let i = 0; i < animations.length; i++) {\r\n            animations[i].element.removeClass(\"dx-animating\")\r\n        }\r\n        this._animations.length = 0\r\n    },\r\n    reset: function() {\r\n        this._accumulatedDelays.enter = 0;\r\n        this._accumulatedDelays.leave = 0;\r\n        this._clearAnimations();\r\n        this._completeDeferred = new Deferred;\r\n        this._completePromise = this._completeDeferred.promise()\r\n    },\r\n    enter: function($elements, animationConfig, configModifier) {\r\n        const animations = this._createAnimations($elements, animationConfig, configModifier, \"enter\");\r\n        this._animations.push.apply(this._animations, animations)\r\n    },\r\n    leave: function($elements, animationConfig, configModifier) {\r\n        const animations = this._createAnimations($elements, animationConfig, configModifier, \"leave\");\r\n        this._animations.push.apply(this._animations, animations)\r\n    },\r\n    start: function() {\r\n        const that = this;\r\n        let result;\r\n        if (!this._animations.length) {\r\n            that.reset();\r\n            result = (new Deferred).resolve().promise()\r\n        } else {\r\n            const animationDeferreds = map(this._animations, (function(animation) {\r\n                const result = new Deferred;\r\n                animation.deferred.always((function() {\r\n                    result.resolve()\r\n                }));\r\n                return result.promise()\r\n            }));\r\n            result = when.apply($, animationDeferreds).always((function() {\r\n                that._completeDeferred.resolve();\r\n                that.reset()\r\n            }));\r\n            commonUtils.executeAsync((function() {\r\n                that._startAnimations()\r\n            }))\r\n        }\r\n        return result\r\n    },\r\n    stop: function(jumpToEnd) {\r\n        this._stopAnimations(jumpToEnd)\r\n    }\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAIA;AAAA;AAGA;AACA;AAGA;AAAA;;;;;;;;;;AAIA,MAAM,qBAAqB;IACvB,SAAS;IACT,UAAU;IACV,MAAM;IACN,WAAW;AACf;AACA,MAAM,qBAAqB;AACpB,MAAM,qBAAqB,qJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAC5C,MAAM;QACF,IAAI,CAAC,kBAAkB,GAAG;YACtB,OAAO;YACP,OAAO;QACX;QACA,IAAI,CAAC,WAAW,GAAG,EAAE;QACrB,IAAI,CAAC,KAAK;IACd;IACA,mBAAmB,SAAS,SAAS,EAAE,aAAa,EAAE,cAAc,EAAE,IAAI;QACtE,YAAY,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACd,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,EAAE;QACjB,iBAAiB,kBAAkB,CAAC;QACpC,MAAM,kBAAkB,IAAI,CAAC,8BAA8B,CAAC,eAAe,gBAAgB;QAC3F,IAAI,iBAAiB;YACjB,UAAU,IAAI,CAAE;gBACZ,MAAM,YAAY,KAAK,gBAAgB,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,GAAG,iBAAiB;gBAClE,IAAI,WAAW;oBACX,UAAU,OAAO,CAAC,QAAQ,CAAC;oBAC3B,UAAU,KAAK;oBACf,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;QACA,OAAO;IACX;IACA,gCAAgC,SAAS,MAAM,EAAE,cAAc,EAAE,IAAI;QACjE,IAAI;QACJ,IAAI,aAAa,OAAO,QAAQ;YAC5B,MAAM,aAAa;YACnB,SAAS,yLAAA,CAAA,UAAO,CAAC,SAAS,CAAC;QAC/B;QACA,IAAI,CAAC,QAAQ;YACT,SAAS,KAAK;QAClB,OAAO,IAAI,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,MAAM,CAAC,KAAK,GAAG;YACjC,SAAS,MAAM,CAAC,KAAK;QACzB,OAAO;YACH,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;gBACZ,0BAA0B;gBAC1B,aAAa,IAAI,CAAC,gBAAgB;YACtC,GAAG,QAAQ;YACX,IAAI,CAAC,OAAO,IAAI,IAAI,UAAU,OAAO,IAAI,EAAE;gBACvC,MAAM,WAAW,QAAQ;gBACzB,MAAM,kBAAkB,CAAC,OAAO,eAAe,GAAG,MAAM,OAAO,eAAe,GAAG,EAAE,IAAI,kBAAkB,CAAC,OAAO,SAAS,CAAC;gBAC3H,OAAO,IAAI,GAAG;gBACd,OAAO,IAAI,GAAG,CAAC,OAAO,IAAI,IAAI,QAAQ,IAAI;gBAC1C,OAAO,EAAE,GAAG,OAAO,EAAE,IAAI,WAAW;YACxC;YACA,OAAO,YAAY,GAAG,OAAO,YAAY,IAAI;YAC7C,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI;YAC/B,IAAI,OAAO,YAAY,EAAE;gBACrB,OAAO,KAAK,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK;gBAC7C,IAAI,CAAC,kBAAkB,CAAC,KAAK,IAAI,OAAO,YAAY;YACxD;QACJ;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,QAAQ,EAAE,eAAe,EAAE,cAAc;QAChE,IAAI;QACJ,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB;YAChC,SAAS,yKAAA,CAAA,UAAE,CAAC,eAAe,CAAC,UAAU;QAC1C,OAAO,IAAI,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;YACpC,SAAS,gBAAgB,UAAU;QACvC;QACA,OAAO;IACX;IACA,kBAAkB;QACd,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,UAAU,CAAC,EAAE,CAAC,KAAK;QACvB;IACJ;IACA,iBAAiB,SAAS,SAAS;QAC/B,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,UAAU,CAAC,EAAE,CAAC,IAAI,CAAC;QACvB;IACJ;IACA,kBAAkB;QACd,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YACxC,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,WAAW,CAAC;QACtC;QACA,IAAI,CAAC,WAAW,CAAC,MAAM,GAAG;IAC9B;IACA,OAAO;QACH,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG;QAChC,IAAI,CAAC,kBAAkB,CAAC,KAAK,GAAG;QAChC,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,iBAAiB,GAAG,IAAI,oLAAA,CAAA,WAAQ;QACrC,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,iBAAiB,CAAC,OAAO;IAC1D;IACA,OAAO,SAAS,SAAS,EAAE,eAAe,EAAE,cAAc;QACtD,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,WAAW,iBAAiB,gBAAgB;QACtF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE;IAClD;IACA,OAAO,SAAS,SAAS,EAAE,eAAe,EAAE,cAAc;QACtD,MAAM,aAAa,IAAI,CAAC,iBAAiB,CAAC,WAAW,iBAAiB,gBAAgB;QACtF,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE;IAClD;IACA,OAAO;QACH,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,MAAM,EAAE;YAC1B,KAAK,KAAK;YACV,SAAS,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO,GAAG,OAAO;QAC7C,OAAO;YACH,MAAM,qBAAqB,CAAA,GAAA,oLAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,WAAW,EAAG,SAAS,SAAS;gBAChE,MAAM,SAAS,IAAI,oLAAA,CAAA,WAAQ;gBAC3B,UAAU,QAAQ,CAAC,MAAM,CAAE;oBACvB,OAAO,OAAO;gBAClB;gBACA,OAAO,OAAO,OAAO;YACzB;YACA,SAAS,oLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,wJAAA,CAAA,UAAC,EAAE,oBAAoB,MAAM,CAAE;gBAC/C,KAAK,iBAAiB,CAAC,OAAO;gBAC9B,KAAK,KAAK;YACd;YACA,kLAAA,CAAA,UAAW,CAAC,YAAY,CAAE;gBACtB,KAAK,gBAAgB;YACzB;QACJ;QACA,OAAO;IACX;IACA,MAAM,SAAS,SAAS;QACpB,IAAI,CAAC,eAAe,CAAC;IACzB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3848, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation/transition_executor.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation/transition_executor.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    TransitionExecutor\r\n} from \"./transition_executor/transition_executor\";\r\nexport default TransitionExecutor;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe,iNAAA,CAAA,qBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3865, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/animation.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/animation.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    requestAnimationFrame,\r\n    cancelAnimationFrame\r\n} from \"./animation/frame\";\r\nimport fx from \"./animation/fx\";\r\nimport animationPresets from \"./animation/presets\";\r\nimport TransitionExecutor from \"./animation/transition_executor\";\r\nexport {\r\n    requestAnimationFrame,\r\n    cancelAnimationFrame,\r\n    fx,\r\n    animationPresets,\r\n    TransitionExecutor\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD;AAIA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3896, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/emitter.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/emitter.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/core/m_emitter\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3917, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/wheel.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/wheel.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../../__internal/events/core/m_wheel\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3938, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/pointer/base.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/pointer/base.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/pointer/m_base\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3959, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/pointer/observer.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/pointer/observer.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/pointer/m_observer\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3980, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/pointer/mouse.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/pointer/mouse.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/pointer/m_mouse\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4001, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/pointer/touch.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/pointer/touch.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/pointer/m_touch\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4022, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/pointer/mouse_and_touch.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/pointer/mouse_and_touch.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/pointer/m_mouse_and_touch\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4043, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/pointer.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/pointer.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../__internal/events/m_pointer\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4064, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/emitter_registrator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/core/m_emitter_registrator\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4085, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/utils/event_nodes_disposing.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/utils/event_nodes_disposing.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../../__internal/events/utils/m_event_nodes_disposing\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4106, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/click.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/click.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_click\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4127, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/double_click.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/double_click.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    name,\r\n    dblClick\r\n} from \"../../../__internal/events/m_dblclick\";\r\nimport registerEvent from \"./core/event_registrator\";\r\nregisterEvent(name, dblClick);\r\nexport {\r\n    name\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD;AAIA;AAAA;;;AACA,CAAA,GAAA,8LAAA,CAAA,UAAa,AAAD,EAAE,6KAAA,CAAA,OAAI,EAAE,6KAAA,CAAA,WAAQ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4154, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/errors.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/errors.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../__internal/data/m_errors\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4175, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    compileGetter,\r\n    compileSetter\r\n} from \"../../core/utils/data\";\r\nexport * from \"../../__internal/data/m_utils\";\r\nexport {\r\n    compileGetter,\r\n    compileSetter\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD;AAIA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4200, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/array_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/array_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../__internal/data/m_array_utils\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4221, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/array_query.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/array_query.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../__internal/data/m_array_query\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4242, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/store_helper.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/store_helper.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../__internal/data/m_store_helper\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4263, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/abstract_store.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/abstract_store.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../__internal/data/m_abstract_store\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4284, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/custom_store.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/custom_store.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport CustomStore from \"../../__internal/data/m_custom_store\";\r\n\r\nfunction isGroupItem(item) {\r\n    if (void 0 === item || null === item || \"object\" !== typeof item) {\r\n        return false\r\n    }\r\n    return \"key\" in item && \"items\" in item\r\n}\r\n\r\nfunction isLoadResultObject(res) {\r\n    return !Array.isArray(res) && \"data\" in res\r\n}\r\n\r\nfunction isGroupItemsArray(res) {\r\n    return Array.isArray(res) && !!res.length && isGroupItem(res[0])\r\n}\r\n\r\nfunction isItemsArray(res) {\r\n    return Array.isArray(res) && !isGroupItem(res[0])\r\n}\r\nexport {\r\n    CustomStore,\r\n    isLoadResultObject,\r\n    isGroupItemsArray,\r\n    isItemsArray\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;;AAEA,SAAS,YAAY,IAAI;IACrB,IAAI,KAAK,MAAM,QAAQ,SAAS,QAAQ,aAAa,OAAO,MAAM;QAC9D,OAAO;IACX;IACA,OAAO,SAAS,QAAQ,WAAW;AACvC;AAEA,SAAS,mBAAmB,GAAG;IAC3B,OAAO,CAAC,MAAM,OAAO,CAAC,QAAQ,UAAU;AAC5C;AAEA,SAAS,kBAAkB,GAAG;IAC1B,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,CAAC,IAAI,MAAM,IAAI,YAAY,GAAG,CAAC,EAAE;AACnE;AAEA,SAAS,aAAa,GAAG;IACrB,OAAO,MAAM,OAAO,CAAC,QAAQ,CAAC,YAAY,GAAG,CAAC,EAAE;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4325, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/query_adapters.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/query_adapters.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport default {};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;uCACc,CAAC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4340, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/remote_query.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/remote_query.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../__internal/data/m_remote_query\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4361, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/query_implementation.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/query_implementation.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport arrayQueryImpl from \"./array_query\";\r\nimport remoteQueryImpl from \"./remote_query\";\r\nexport const queryImpl = {\r\n    array: arrayQueryImpl,\r\n    remote: remoteQueryImpl\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;;;AACO,MAAM,YAAY;IACrB,OAAO,8KAAA,CAAA,UAAc;IACrB,QAAQ,+KAAA,CAAA,UAAe;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4385, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/query.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/query.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../__internal/data/m_query\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4406, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/array_store.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/array_store.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../__internal/data/m_array_store\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4427, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/data_source/utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/data_source/utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/data/data_source/m_utils\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4448, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/data_source/operation_manager.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/data_source/operation_manager.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../__internal/data/data_source/m_operation_manager\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4469, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/data_source/data_source.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/data_source/data_source.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/data/data_source/m_data_source\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4490, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/data/data_source.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/data/data_source.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    DataSource\r\n} from \"./data_source/data_source\";\r\nexport default DataSource;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;uCAGe,6LAAA,CAAA,aAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4508, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    sign,\r\n    multiplyInExponentialForm\r\n} from \"../../../core/utils/math\";\r\nconst DECIMAL_BASE = 10;\r\n\r\nfunction roundByAbs(value) {\r\n    const valueSign = sign(value);\r\n    return valueSign * Math.round(Math.abs(value))\r\n}\r\n\r\nfunction adjustValue(value, precision) {\r\n    const precisionMultiplier = Math.pow(10, precision);\r\n    const intermediateValue = multiplyInExponentialForm(value, precision);\r\n    return roundByAbs(intermediateValue) / precisionMultiplier\r\n}\r\nexport function toFixed(value, precision) {\r\n    const valuePrecision = precision || 0;\r\n    const adjustedValue = valuePrecision > 0 ? adjustValue(...arguments) : value;\r\n    return adjustedValue.toFixed(valuePrecision)\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;AAIA,MAAM,eAAe;AAErB,SAAS,WAAW,KAAK;IACrB,MAAM,YAAY,CAAA,GAAA,gLAAA,CAAA,OAAI,AAAD,EAAE;IACvB,OAAO,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;AAC3C;AAEA,SAAS,YAAY,KAAK,EAAE,SAAS;IACjC,MAAM,sBAAsB,KAAK,GAAG,CAAC,IAAI;IACzC,MAAM,oBAAoB,CAAA,GAAA,gLAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;IAC3D,OAAO,WAAW,qBAAqB;AAC3C;AACO,SAAS,QAAQ,KAAK,EAAE,SAAS;IACpC,MAAM,iBAAiB,aAAa;IACpC,MAAM,gBAAgB,iBAAiB,IAAI,eAAe,aAAa;IACvE,OAAO,cAAc,OAAO,CAAC;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4540, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/ldml/number.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/ldml/number.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    fitIntoRange,\r\n    multiplyInExponentialForm\r\n} from \"../../../../core/utils/math\";\r\nimport {\r\n    toFixed\r\n} from \"../utils\";\r\nconst DEFAULT_CONFIG = {\r\n    thousandsSeparator: \",\",\r\n    decimalSeparator: \".\"\r\n};\r\nconst ESCAPING_CHAR = \"'\";\r\nconst MAXIMUM_NUMBER_LENGTH = 15;\r\nconst PERCENT_EXPONENT_SHIFT = 2;\r\n\r\nfunction getGroupSizes(formatString) {\r\n    return formatString.split(\",\").slice(1).map((function(str) {\r\n        let singleQuotesLeft = 0;\r\n        return str.split(\"\").filter((function(char, index) {\r\n            singleQuotesLeft += \"'\" === char;\r\n            const isDigit = \"#\" === char || \"0\" === char;\r\n            const isInStub = singleQuotesLeft % 2;\r\n            return isDigit && !isInStub\r\n        })).length\r\n    }))\r\n}\r\n\r\nfunction splitSignParts(format) {\r\n    let separatorChar = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : \";\";\r\n    let escapingChar = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : \"'\";\r\n    const parts = [];\r\n    let currentPart = \"\";\r\n    let state = \"searchingSeparator\";\r\n    for (let i = 0; i < format.length; i++) {\r\n        const char = format[i];\r\n        if (\"searchingSeparator\" === state && char === escapingChar) {\r\n            state = \"skippingSeparationInsideEscaping\"\r\n        } else if (\"skippingSeparationInsideEscaping\" === state && char === escapingChar) {\r\n            state = \"searchingSeparator\"\r\n        } else if (\"searchingSeparator\" === state && char === separatorChar) {\r\n            state = \"separating\";\r\n            parts.push(currentPart);\r\n            currentPart = \"\"\r\n        }\r\n        if (\"separating\" !== state) {\r\n            currentPart += char\r\n        } else {\r\n            state = \"searchingSeparator\"\r\n        }\r\n    }\r\n    parts.push(currentPart);\r\n    return parts\r\n}\r\n\r\nfunction getSignParts(format) {\r\n    const signParts = splitSignParts(format);\r\n    if (1 === signParts.length) {\r\n        signParts.push(\"-\" + signParts[0])\r\n    }\r\n    return signParts\r\n}\r\n\r\nfunction reverseString(str) {\r\n    return str.toString().split(\"\").reverse().join(\"\")\r\n}\r\n\r\nfunction isPercentFormat(format) {\r\n    return -1 !== format.indexOf(\"%\") && !format.match(/'[^']*%[^']*'/g)\r\n}\r\n\r\nfunction removeStubs(str) {\r\n    return str.replace(/'[^']*'/g, \"\")\r\n}\r\n\r\nfunction getNonRequiredDigitCount(floatFormat) {\r\n    if (!floatFormat) {\r\n        return 0\r\n    }\r\n    const format = removeStubs(floatFormat);\r\n    return format.length - format.replace(/[#]/g, \"\").length\r\n}\r\n\r\nfunction getRequiredDigitCount(floatFormat) {\r\n    if (!floatFormat) {\r\n        return 0\r\n    }\r\n    const format = removeStubs(floatFormat);\r\n    return format.length - format.replace(/[0]/g, \"\").length\r\n}\r\n\r\nfunction normalizeValueString(valuePart, minDigitCount, maxDigitCount) {\r\n    if (!valuePart) {\r\n        return \"\"\r\n    }\r\n    if (valuePart.length > maxDigitCount) {\r\n        valuePart = valuePart.substr(0, maxDigitCount)\r\n    }\r\n    while (valuePart.length > minDigitCount && \"0\" === valuePart.slice(-1)) {\r\n        valuePart = valuePart.substr(0, valuePart.length - 1)\r\n    }\r\n    while (valuePart.length < minDigitCount) {\r\n        valuePart += \"0\"\r\n    }\r\n    return valuePart\r\n}\r\n\r\nfunction applyGroups(valueString, groupSizes, thousandsSeparator) {\r\n    if (!groupSizes.length) {\r\n        return valueString\r\n    }\r\n    const groups = [];\r\n    let index = 0;\r\n    while (valueString) {\r\n        const groupSize = groupSizes[index];\r\n        if (!groupSize) {\r\n            break\r\n        }\r\n        groups.push(valueString.slice(0, groupSize));\r\n        valueString = valueString.slice(groupSize);\r\n        if (index < groupSizes.length - 1) {\r\n            index++\r\n        }\r\n    }\r\n    return groups.join(thousandsSeparator)\r\n}\r\n\r\nfunction formatNumberPart(format, valueString) {\r\n    return format.split(\"'\").map((function(formatPart, escapeIndex) {\r\n        const isEscape = escapeIndex % 2;\r\n        if (!formatPart && isEscape) {\r\n            return \"'\"\r\n        }\r\n        return isEscape ? formatPart : formatPart.replace(/[,#0]+/, valueString)\r\n    })).join(\"\")\r\n}\r\n\r\nfunction getFloatPointIndex(format) {\r\n    let isEscape = false;\r\n    for (let index = 0; index < format.length; index++) {\r\n        if (\"'\" === format[index]) {\r\n            isEscape = !isEscape\r\n        }\r\n        if (\".\" === format[index] && !isEscape) {\r\n            return index\r\n        }\r\n    }\r\n    return format.length\r\n}\r\nexport function getFormatter(format, config) {\r\n    config = config || DEFAULT_CONFIG;\r\n    return function(value) {\r\n        if (\"number\" !== typeof value || isNaN(value)) {\r\n            return \"\"\r\n        }\r\n        const signFormatParts = getSignParts(format);\r\n        const isPositiveZero = 1 / value === 1 / 0;\r\n        const isPositive = value > 0 || isPositiveZero;\r\n        const numberFormat = signFormatParts[isPositive ? 0 : 1];\r\n        const floatPointIndex = getFloatPointIndex(numberFormat);\r\n        const floatFormatParts = [numberFormat.substr(0, floatPointIndex), numberFormat.substr(floatPointIndex + 1)];\r\n        const minFloatPrecision = getRequiredDigitCount(floatFormatParts[1]);\r\n        const maxFloatPrecision = minFloatPrecision + getNonRequiredDigitCount(floatFormatParts[1]);\r\n        if (isPercentFormat(numberFormat)) {\r\n            value = multiplyInExponentialForm(value, 2)\r\n        }\r\n        if (!isPositive) {\r\n            value = -value\r\n        }\r\n        const minIntegerPrecision = getRequiredDigitCount(floatFormatParts[0]);\r\n        const maxIntegerPrecision = getNonRequiredDigitCount(floatFormatParts[0]) || config.unlimitedIntegerDigits ? void 0 : minIntegerPrecision;\r\n        const integerLength = Math.floor(value).toString().length;\r\n        const floatPrecision = fitIntoRange(maxFloatPrecision, 0, 15 - integerLength);\r\n        const groupSizes = getGroupSizes(floatFormatParts[0]).reverse();\r\n        const valueParts = toFixed(value, floatPrecision < 0 ? 0 : floatPrecision).split(\".\");\r\n        let valueIntegerPart = normalizeValueString(reverseString(valueParts[0]), minIntegerPrecision, maxIntegerPrecision);\r\n        const valueFloatPart = normalizeValueString(valueParts[1], minFloatPrecision, maxFloatPrecision);\r\n        valueIntegerPart = applyGroups(valueIntegerPart, groupSizes, config.thousandsSeparator);\r\n        const integerString = reverseString(formatNumberPart(reverseString(floatFormatParts[0]), valueIntegerPart));\r\n        const floatString = maxFloatPrecision ? formatNumberPart(floatFormatParts[1], valueFloatPart) : \"\";\r\n        const result = integerString + (floatString.match(/\\d/) ? config.decimalSeparator : \"\") + floatString;\r\n        return result\r\n    }\r\n}\r\n\r\nfunction parseValue(text, isPercent, isNegative) {\r\n    const value = (isPercent ? .01 : 1) * parseFloat(text) || 0;\r\n    return isNegative ? -value : value\r\n}\r\n\r\nfunction prepareValueText(valueText, formatter, isPercent, isIntegerPart) {\r\n    let nextValueText = valueText;\r\n    let char;\r\n    let text;\r\n    let nextText;\r\n    do {\r\n        if (nextText) {\r\n            char = text.length === nextText.length ? \"0\" : \"1\";\r\n            valueText = isIntegerPart ? char + valueText : valueText + char\r\n        }\r\n        text = nextText || formatter(parseValue(nextValueText, isPercent));\r\n        nextValueText = isIntegerPart ? \"1\" + nextValueText : nextValueText + \"1\";\r\n        nextText = formatter(parseValue(nextValueText, isPercent))\r\n    } while (text !== nextText && (isIntegerPart ? text.length === nextText.length : text.length <= nextText.length));\r\n    if (isIntegerPart && nextText.length > text.length) {\r\n        const hasGroups = -1 === formatter(12345).indexOf(\"12345\");\r\n        do {\r\n            valueText = \"1\" + valueText\r\n        } while (hasGroups && parseValue(valueText, isPercent) < 1e5)\r\n    }\r\n    return valueText\r\n}\r\n\r\nfunction getFormatByValueText(valueText, formatter, isPercent, isNegative) {\r\n    let format = formatter(parseValue(valueText, isPercent, isNegative));\r\n    const valueTextParts = valueText.split(\".\");\r\n    const valueTextWithModifiedFloat = valueTextParts[0] + \".3\" + valueTextParts[1].slice(1);\r\n    const valueWithModifiedFloat = parseValue(valueTextWithModifiedFloat, isPercent, isNegative);\r\n    const decimalSeparatorIndex = formatter(valueWithModifiedFloat).indexOf(\"3\") - 1;\r\n    format = format.replace(/(\\d)\\D(\\d)/g, \"$1,$2\");\r\n    if (decimalSeparatorIndex >= 0) {\r\n        format = format.slice(0, decimalSeparatorIndex) + \".\" + format.slice(decimalSeparatorIndex + 1)\r\n    }\r\n    format = format.replace(/1+/, \"1\").replace(/1/g, \"#\");\r\n    if (!isPercent) {\r\n        format = format.replace(/%/g, \"'%'\")\r\n    }\r\n    return format\r\n}\r\nexport function getFormat(formatter) {\r\n    let valueText = \".\";\r\n    const isPercent = formatter(1).indexOf(\"100\") >= 0;\r\n    valueText = prepareValueText(valueText, formatter, isPercent, true);\r\n    valueText = prepareValueText(valueText, formatter, isPercent, false);\r\n    const positiveFormat = getFormatByValueText(valueText, formatter, isPercent, false);\r\n    const negativeFormat = getFormatByValueText(valueText, formatter, isPercent, true);\r\n    return negativeFormat === \"-\" + positiveFormat ? positiveFormat : positiveFormat + \";\" + negativeFormat\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAIA;;;AAGA,MAAM,iBAAiB;IACnB,oBAAoB;IACpB,kBAAkB;AACtB;AACA,MAAM,gBAAgB;AACtB,MAAM,wBAAwB;AAC9B,MAAM,yBAAyB;AAE/B,SAAS,cAAc,YAAY;IAC/B,OAAO,aAAa,KAAK,CAAC,KAAK,KAAK,CAAC,GAAG,GAAG,CAAE,SAAS,GAAG;QACrD,IAAI,mBAAmB;QACvB,OAAO,IAAI,KAAK,CAAC,IAAI,MAAM,CAAE,SAAS,IAAI,EAAE,KAAK;YAC7C,oBAAoB,QAAQ;YAC5B,MAAM,UAAU,QAAQ,QAAQ,QAAQ;YACxC,MAAM,WAAW,mBAAmB;YACpC,OAAO,WAAW,CAAC;QACvB,GAAI,MAAM;IACd;AACJ;AAEA,SAAS,eAAe,MAAM;IAC1B,IAAI,gBAAgB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACrF,IAAI,eAAe,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACpF,MAAM,QAAQ,EAAE;IAChB,IAAI,cAAc;IAClB,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,IAAI,yBAAyB,SAAS,SAAS,cAAc;YACzD,QAAQ;QACZ,OAAO,IAAI,uCAAuC,SAAS,SAAS,cAAc;YAC9E,QAAQ;QACZ,OAAO,IAAI,yBAAyB,SAAS,SAAS,eAAe;YACjE,QAAQ;YACR,MAAM,IAAI,CAAC;YACX,cAAc;QAClB;QACA,IAAI,iBAAiB,OAAO;YACxB,eAAe;QACnB,OAAO;YACH,QAAQ;QACZ;IACJ;IACA,MAAM,IAAI,CAAC;IACX,OAAO;AACX;AAEA,SAAS,aAAa,MAAM;IACxB,MAAM,YAAY,eAAe;IACjC,IAAI,MAAM,UAAU,MAAM,EAAE;QACxB,UAAU,IAAI,CAAC,MAAM,SAAS,CAAC,EAAE;IACrC;IACA,OAAO;AACX;AAEA,SAAS,cAAc,GAAG;IACtB,OAAO,IAAI,QAAQ,GAAG,KAAK,CAAC,IAAI,OAAO,GAAG,IAAI,CAAC;AACnD;AAEA,SAAS,gBAAgB,MAAM;IAC3B,OAAO,CAAC,MAAM,OAAO,OAAO,CAAC,QAAQ,CAAC,OAAO,KAAK,CAAC;AACvD;AAEA,SAAS,YAAY,GAAG;IACpB,OAAO,IAAI,OAAO,CAAC,YAAY;AACnC;AAEA,SAAS,yBAAyB,WAAW;IACzC,IAAI,CAAC,aAAa;QACd,OAAO;IACX;IACA,MAAM,SAAS,YAAY;IAC3B,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,CAAC,QAAQ,IAAI,MAAM;AAC5D;AAEA,SAAS,sBAAsB,WAAW;IACtC,IAAI,CAAC,aAAa;QACd,OAAO;IACX;IACA,MAAM,SAAS,YAAY;IAC3B,OAAO,OAAO,MAAM,GAAG,OAAO,OAAO,CAAC,QAAQ,IAAI,MAAM;AAC5D;AAEA,SAAS,qBAAqB,SAAS,EAAE,aAAa,EAAE,aAAa;IACjE,IAAI,CAAC,WAAW;QACZ,OAAO;IACX;IACA,IAAI,UAAU,MAAM,GAAG,eAAe;QAClC,YAAY,UAAU,MAAM,CAAC,GAAG;IACpC;IACA,MAAO,UAAU,MAAM,GAAG,iBAAiB,QAAQ,UAAU,KAAK,CAAC,CAAC,GAAI;QACpE,YAAY,UAAU,MAAM,CAAC,GAAG,UAAU,MAAM,GAAG;IACvD;IACA,MAAO,UAAU,MAAM,GAAG,cAAe;QACrC,aAAa;IACjB;IACA,OAAO;AACX;AAEA,SAAS,YAAY,WAAW,EAAE,UAAU,EAAE,kBAAkB;IAC5D,IAAI,CAAC,WAAW,MAAM,EAAE;QACpB,OAAO;IACX;IACA,MAAM,SAAS,EAAE;IACjB,IAAI,QAAQ;IACZ,MAAO,YAAa;QAChB,MAAM,YAAY,UAAU,CAAC,MAAM;QACnC,IAAI,CAAC,WAAW;YACZ;QACJ;QACA,OAAO,IAAI,CAAC,YAAY,KAAK,CAAC,GAAG;QACjC,cAAc,YAAY,KAAK,CAAC;QAChC,IAAI,QAAQ,WAAW,MAAM,GAAG,GAAG;YAC/B;QACJ;IACJ;IACA,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA,SAAS,iBAAiB,MAAM,EAAE,WAAW;IACzC,OAAO,OAAO,KAAK,CAAC,KAAK,GAAG,CAAE,SAAS,UAAU,EAAE,WAAW;QAC1D,MAAM,WAAW,cAAc;QAC/B,IAAI,CAAC,cAAc,UAAU;YACzB,OAAO;QACX;QACA,OAAO,WAAW,aAAa,WAAW,OAAO,CAAC,UAAU;IAChE,GAAI,IAAI,CAAC;AACb;AAEA,SAAS,mBAAmB,MAAM;IAC9B,IAAI,WAAW;IACf,IAAK,IAAI,QAAQ,GAAG,QAAQ,OAAO,MAAM,EAAE,QAAS;QAChD,IAAI,QAAQ,MAAM,CAAC,MAAM,EAAE;YACvB,WAAW,CAAC;QAChB;QACA,IAAI,QAAQ,MAAM,CAAC,MAAM,IAAI,CAAC,UAAU;YACpC,OAAO;QACX;IACJ;IACA,OAAO,OAAO,MAAM;AACxB;AACO,SAAS,aAAa,MAAM,EAAE,MAAM;IACvC,SAAS,UAAU;IACnB,OAAO,SAAS,KAAK;QACjB,IAAI,aAAa,OAAO,SAAS,MAAM,QAAQ;YAC3C,OAAO;QACX;QACA,MAAM,kBAAkB,aAAa;QACrC,MAAM,iBAAiB,IAAI,UAAU,IAAI;QACzC,MAAM,aAAa,QAAQ,KAAK;QAChC,MAAM,eAAe,eAAe,CAAC,aAAa,IAAI,EAAE;QACxD,MAAM,kBAAkB,mBAAmB;QAC3C,MAAM,mBAAmB;YAAC,aAAa,MAAM,CAAC,GAAG;YAAkB,aAAa,MAAM,CAAC,kBAAkB;SAAG;QAC5G,MAAM,oBAAoB,sBAAsB,gBAAgB,CAAC,EAAE;QACnE,MAAM,oBAAoB,oBAAoB,yBAAyB,gBAAgB,CAAC,EAAE;QAC1F,IAAI,gBAAgB,eAAe;YAC/B,QAAQ,CAAA,GAAA,gLAAA,CAAA,4BAAyB,AAAD,EAAE,OAAO;QAC7C;QACA,IAAI,CAAC,YAAY;YACb,QAAQ,CAAC;QACb;QACA,MAAM,sBAAsB,sBAAsB,gBAAgB,CAAC,EAAE;QACrE,MAAM,sBAAsB,yBAAyB,gBAAgB,CAAC,EAAE,KAAK,OAAO,sBAAsB,GAAG,KAAK,IAAI;QACtH,MAAM,gBAAgB,KAAK,KAAK,CAAC,OAAO,QAAQ,GAAG,MAAM;QACzD,MAAM,iBAAiB,CAAA,GAAA,gLAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB,GAAG,KAAK;QAC/D,MAAM,aAAa,cAAc,gBAAgB,CAAC,EAAE,EAAE,OAAO;QAC7D,MAAM,aAAa,CAAA,GAAA,+KAAA,CAAA,UAAO,AAAD,EAAE,OAAO,iBAAiB,IAAI,IAAI,gBAAgB,KAAK,CAAC;QACjF,IAAI,mBAAmB,qBAAqB,cAAc,UAAU,CAAC,EAAE,GAAG,qBAAqB;QAC/F,MAAM,iBAAiB,qBAAqB,UAAU,CAAC,EAAE,EAAE,mBAAmB;QAC9E,mBAAmB,YAAY,kBAAkB,YAAY,OAAO,kBAAkB;QACtF,MAAM,gBAAgB,cAAc,iBAAiB,cAAc,gBAAgB,CAAC,EAAE,GAAG;QACzF,MAAM,cAAc,oBAAoB,iBAAiB,gBAAgB,CAAC,EAAE,EAAE,kBAAkB;QAChG,MAAM,SAAS,gBAAgB,CAAC,YAAY,KAAK,CAAC,QAAQ,OAAO,gBAAgB,GAAG,EAAE,IAAI;QAC1F,OAAO;IACX;AACJ;AAEA,SAAS,WAAW,IAAI,EAAE,SAAS,EAAE,UAAU;IAC3C,MAAM,QAAQ,CAAC,YAAY,MAAM,CAAC,IAAI,WAAW,SAAS;IAC1D,OAAO,aAAa,CAAC,QAAQ;AACjC;AAEA,SAAS,iBAAiB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa;IACpE,IAAI,gBAAgB;IACpB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,GAAG;QACC,IAAI,UAAU;YACV,OAAO,KAAK,MAAM,KAAK,SAAS,MAAM,GAAG,MAAM;YAC/C,YAAY,gBAAgB,OAAO,YAAY,YAAY;QAC/D;QACA,OAAO,YAAY,UAAU,WAAW,eAAe;QACvD,gBAAgB,gBAAgB,MAAM,gBAAgB,gBAAgB;QACtE,WAAW,UAAU,WAAW,eAAe;IACnD,QAAS,SAAS,YAAY,CAAC,gBAAgB,KAAK,MAAM,KAAK,SAAS,MAAM,GAAG,KAAK,MAAM,IAAI,SAAS,MAAM,EAAG;IAClH,IAAI,iBAAiB,SAAS,MAAM,GAAG,KAAK,MAAM,EAAE;QAChD,MAAM,YAAY,CAAC,MAAM,UAAU,OAAO,OAAO,CAAC;QAClD,GAAG;YACC,YAAY,MAAM;QACtB,QAAS,aAAa,WAAW,WAAW,aAAa,IAAI;IACjE;IACA,OAAO;AACX;AAEA,SAAS,qBAAqB,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU;IACrE,IAAI,SAAS,UAAU,WAAW,WAAW,WAAW;IACxD,MAAM,iBAAiB,UAAU,KAAK,CAAC;IACvC,MAAM,6BAA6B,cAAc,CAAC,EAAE,GAAG,OAAO,cAAc,CAAC,EAAE,CAAC,KAAK,CAAC;IACtF,MAAM,yBAAyB,WAAW,4BAA4B,WAAW;IACjF,MAAM,wBAAwB,UAAU,wBAAwB,OAAO,CAAC,OAAO;IAC/E,SAAS,OAAO,OAAO,CAAC,eAAe;IACvC,IAAI,yBAAyB,GAAG;QAC5B,SAAS,OAAO,KAAK,CAAC,GAAG,yBAAyB,MAAM,OAAO,KAAK,CAAC,wBAAwB;IACjG;IACA,SAAS,OAAO,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;IACjD,IAAI,CAAC,WAAW;QACZ,SAAS,OAAO,OAAO,CAAC,MAAM;IAClC;IACA,OAAO;AACX;AACO,SAAS,UAAU,SAAS;IAC/B,IAAI,YAAY;IAChB,MAAM,YAAY,UAAU,GAAG,OAAO,CAAC,UAAU;IACjD,YAAY,iBAAiB,WAAW,WAAW,WAAW;IAC9D,YAAY,iBAAiB,WAAW,WAAW,WAAW;IAC9D,MAAM,iBAAiB,qBAAqB,WAAW,WAAW,WAAW;IAC7E,MAAM,iBAAiB,qBAAqB,WAAW,WAAW,WAAW;IAC7E,OAAO,mBAAmB,MAAM,iBAAiB,iBAAiB,iBAAiB,MAAM;AAC7F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4778, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/currency.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/currency.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nexport default {\r\n    _formatNumberCore: function(value, format, formatConfig) {\r\n        if (\"currency\" === format) {\r\n            formatConfig.precision = formatConfig.precision || 0;\r\n            let result = this.format(value, extend({}, formatConfig, {\r\n                type: \"fixedpoint\"\r\n            }));\r\n            const currencyPart = this.getCurrencySymbol().symbol.replace(/\\$/g, \"$$$$\");\r\n            result = result.replace(/^(\\D*)(\\d.*)/, \"$1\" + currencyPart + \"$2\");\r\n            return result\r\n        }\r\n        return this.callBase.apply(this, arguments)\r\n    },\r\n    getCurrencySymbol: function() {\r\n        return {\r\n            symbol: \"$\"\r\n        }\r\n    },\r\n    getOpenXmlCurrencyFormat: function() {\r\n        return \"$#,##0{0}_);\\\\($#,##0{0}\\\\)\"\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;uCAGe;IACX,mBAAmB,SAAS,KAAK,EAAE,MAAM,EAAE,YAAY;QACnD,IAAI,eAAe,QAAQ;YACvB,aAAa,SAAS,GAAG,aAAa,SAAS,IAAI;YACnD,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,cAAc;gBACrD,MAAM;YACV;YACA,MAAM,eAAe,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,OAAO,CAAC,OAAO;YACpE,SAAS,OAAO,OAAO,CAAC,gBAAgB,OAAO,eAAe;YAC9D,OAAO;QACX;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;IACrC;IACA,mBAAmB;QACf,OAAO;YACH,QAAQ;QACZ;IACJ;IACA,0BAA0B;QACtB,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4817, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/open_xml_currency_format.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/open_xml_currency_format.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport default (currencySymbol, accountingFormat) => {\r\n    if (!accountingFormat) {\r\n        return\r\n    }\r\n    let encodedCurrencySymbol = currencySymbol;\r\n    if (\"string\" === typeof currencySymbol) {\r\n        encodedCurrencySymbol = \"\";\r\n        for (let i = 0; i < currencySymbol.length; i++) {\r\n            if (\"$\" !== currencySymbol[i]) {\r\n                encodedCurrencySymbol += \"\\\\\"\r\n            }\r\n            encodedCurrencySymbol += currencySymbol[i]\r\n        }\r\n    }\r\n    const encodeSymbols = {\r\n        \".00\": \"{0}\",\r\n        \"'\": \"\\\\'\",\r\n        \"\\\\(\": \"\\\\(\",\r\n        \"\\\\)\": \"\\\\)\",\r\n        \" \": \"\\\\ \",\r\n        '\"': \"&quot;\",\r\n        \"\\\\\\xa4\": encodedCurrencySymbol\r\n    };\r\n    const result = accountingFormat.split(\";\");\r\n    for (let i = 0; i < result.length; i++) {\r\n        for (const symbol in encodeSymbols) {\r\n            if (Object.prototype.hasOwnProperty.call(encodeSymbols, symbol)) {\r\n                result[i] = result[i].replace(new RegExp(symbol, \"g\"), encodeSymbols[symbol])\r\n            }\r\n        }\r\n    }\r\n    return 2 === result.length ? result[0] + \"_);\" + result[1] : result[0]\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;uCACc,CAAC,gBAAgB;IAC5B,IAAI,CAAC,kBAAkB;QACnB;IACJ;IACA,IAAI,wBAAwB;IAC5B,IAAI,aAAa,OAAO,gBAAgB;QACpC,wBAAwB;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,IAAI,QAAQ,cAAc,CAAC,EAAE,EAAE;gBAC3B,yBAAyB;YAC7B;YACA,yBAAyB,cAAc,CAAC,EAAE;QAC9C;IACJ;IACA,MAAM,gBAAgB;QAClB,OAAO;QACP,KAAK;QACL,OAAO;QACP,OAAO;QACP,KAAK;QACL,KAAK;QACL,UAAU;IACd;IACA,MAAM,SAAS,iBAAiB,KAAK,CAAC;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,IAAK,MAAM,UAAU,cAAe;YAChC,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,SAAS;gBAC7D,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,OAAO,QAAQ,MAAM,aAAa,CAAC,OAAO;YAChF;QACJ;IACJ;IACA,OAAO,MAAM,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,GAAG,QAAQ,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4864, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/cldr-data/accounting_formats.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/cldr-data/accounting_formats.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\n// !!! AUTO-GENERATED FILE, DO NOT EDIT\r\nexport default {\r\n    af: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"af-NA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    agq: \"#,##0.00\\xa4\",\r\n    ak: \"\\xa4#,##0.00\",\r\n    am: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ar: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-AE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-BH\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-DJ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-DZ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-EG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-EH\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-ER\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-IL\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-IQ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-JO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-KM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-KW\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-LB\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-LY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-MA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-MR\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-OM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-PS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-QA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-SA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-SD\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-SO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-SS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-SY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-TD\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-TN\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ar-YE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    as: \"\\xa4\\xa0#,##,##0.00\",\r\n    asa: \"#,##0.00\\xa0\\xa4\",\r\n    ast: \"#,##0.00\\xa0\\xa4\",\r\n    az: \"#,##0.00\\xa0\\xa4\",\r\n    \"az-Cyrl\": \"#,##0.00\\xa0\\xa4\",\r\n    \"az-Latn\": \"#,##0.00\\xa0\\xa4\",\r\n    bas: \"#,##0.00\\xa0\\xa4\",\r\n    be: \"#,##0.00\\xa0\\xa4\",\r\n    \"be-tarask\": \"#,##0.00\\xa0\\xa4\",\r\n    bem: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    bez: \"#,##0.00\\xa4\",\r\n    bg: \"0.00\\xa0\\xa4;(0.00\\xa0\\xa4)\",\r\n    bm: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    bn: \"#,##,##0.00\\xa4;(#,##,##0.00\\xa4)\",\r\n    \"bn-IN\": \"#,##,##0.00\\xa4;(#,##,##0.00\\xa4)\",\r\n    bo: \"\\xa4\\xa0#,##0.00\",\r\n    \"bo-IN\": \"\\xa4\\xa0#,##0.00\",\r\n    br: \"#,##0.00\\xa0\\xa4\",\r\n    brx: \"\\xa4\\xa0#,##,##0.00\",\r\n    bs: \"#,##0.00\\xa0\\xa4\",\r\n    \"bs-Cyrl\": \"#,##0.00\\xa0\\xa4\",\r\n    \"bs-Latn\": \"#,##0.00\\xa0\\xa4\",\r\n    ca: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"ca-AD\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"ca-ES-valencia\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"ca-FR\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"ca-IT\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    ccp: \"#,##,##0.00\\xa4;(#,##,##0.00\\xa4)\",\r\n    \"ccp-IN\": \"#,##,##0.00\\xa4;(#,##,##0.00\\xa4)\",\r\n    ce: \"#,##0.00\\xa0\\xa4\",\r\n    ceb: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    cgg: \"\\xa4#,##0.00\",\r\n    chr: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ckb: \"\\xa4\\xa0#,##0.00\",\r\n    \"ckb-IR\": \"\\xa4\\xa0#,##0.00\",\r\n    cs: \"#,##0.00\\xa0\\xa4\",\r\n    cy: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    da: \"#,##0.00\\xa0\\xa4\",\r\n    \"da-GL\": \"#,##0.00\\xa0\\xa4\",\r\n    dav: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    de: \"#,##0.00\\xa0\\xa4\",\r\n    \"de-AT\": \"#,##0.00\\xa0\\xa4\",\r\n    \"de-BE\": \"#,##0.00\\xa0\\xa4\",\r\n    \"de-CH\": \"#,##0.00\\xa0\\xa4\",\r\n    \"de-IT\": \"#,##0.00\\xa0\\xa4\",\r\n    \"de-LI\": \"#,##0.00\\xa0\\xa4\",\r\n    \"de-LU\": \"#,##0.00\\xa0\\xa4\",\r\n    dje: \"#,##0.00\\xa4\",\r\n    doi: \"\\xa4#,##0.00\",\r\n    dsb: \"#,##0.00\\xa0\\xa4\",\r\n    dua: \"#,##0.00\\xa0\\xa4\",\r\n    dyo: \"#,##0.00\\xa0\\xa4\",\r\n    dz: \"\\xa4#,##,##0.00\",\r\n    ebu: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ee: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ee-TG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    el: \"#,##0.00\\xa0\\xa4\",\r\n    \"el-CY\": \"#,##0.00\\xa0\\xa4\",\r\n    en: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-001\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-150\": \"#,##0.00\\xa0\\xa4\",\r\n    \"en-AE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-AG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-AI\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-AS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-AT\": \"\\xa4\\xa0#,##0.00\",\r\n    \"en-AU\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-BB\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-BE\": \"#,##0.00\\xa0\\xa4\",\r\n    \"en-BI\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-BM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-BS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-BW\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-BZ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-CA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-CC\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-CH\": \"\\xa4\\xa0#,##0.00;\\xa4-#,##0.00\",\r\n    \"en-CK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-CM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-CX\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-CY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-DE\": \"#,##0.00\\xa0\\xa4\",\r\n    \"en-DG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-DK\": \"#,##0.00\\xa0\\xa4\",\r\n    \"en-DM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-ER\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-FI\": \"#,##0.00\\xa0\\xa4\",\r\n    \"en-FJ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-FK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-FM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GB\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GD\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GH\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GI\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GU\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-GY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-HK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-IE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-IL\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-IM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-IN\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-IO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-JE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-JM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-KE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-KI\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-KN\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-KY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-LC\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-LR\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-LS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MH\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MP\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MT\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MU\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MV\": \"\\xa4\\xa0#,##0.00\",\r\n    \"en-MW\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-MY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-NA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-NF\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-NG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-NL\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"en-NR\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-NU\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-NZ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-PG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-PH\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-PK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-PN\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-PR\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-PW\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-RW\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SB\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SC\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SD\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SE\": \"#,##0.00\\xa0\\xa4\",\r\n    \"en-SG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SH\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SI\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"en-SL\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SX\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-SZ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-TC\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-TK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-TO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-TT\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-TV\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-TZ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-UG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-UM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-VC\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-VG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-VI\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-VU\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-WS\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-ZA\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-ZM\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"en-ZW\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    eo: \"\\xa4\\xa0#,##0.00\",\r\n    es: \"#,##0.00\\xa0\\xa4\",\r\n    \"es-419\": \"\\xa4#,##0.00\",\r\n    \"es-AR\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"es-BO\": \"\\xa4#,##0.00\",\r\n    \"es-BR\": \"\\xa4#,##0.00\",\r\n    \"es-BZ\": \"\\xa4#,##0.00\",\r\n    \"es-CL\": \"\\xa4#,##0.00\",\r\n    \"es-CO\": \"\\xa4#,##0.00\",\r\n    \"es-CR\": \"\\xa4#,##0.00\",\r\n    \"es-CU\": \"\\xa4#,##0.00\",\r\n    \"es-DO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"es-EA\": \"#,##0.00\\xa0\\xa4\",\r\n    \"es-EC\": \"\\xa4#,##0.00\",\r\n    \"es-GQ\": \"#,##0.00\\xa0\\xa4\",\r\n    \"es-GT\": \"\\xa4#,##0.00\",\r\n    \"es-HN\": \"\\xa4#,##0.00\",\r\n    \"es-IC\": \"#,##0.00\\xa0\\xa4\",\r\n    \"es-MX\": \"\\xa4#,##0.00\",\r\n    \"es-NI\": \"\\xa4#,##0.00\",\r\n    \"es-PA\": \"\\xa4#,##0.00\",\r\n    \"es-PE\": \"\\xa4#,##0.00\",\r\n    \"es-PH\": \"#,##0.00\\xa0\\xa4\",\r\n    \"es-PR\": \"\\xa4#,##0.00\",\r\n    \"es-PY\": \"\\xa4#,##0.00\",\r\n    \"es-SV\": \"\\xa4#,##0.00\",\r\n    \"es-US\": \"\\xa4#,##0.00\",\r\n    \"es-UY\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"es-VE\": \"\\xa4#,##0.00\",\r\n    et: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    eu: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    ewo: \"#,##0.00\\xa0\\xa4\",\r\n    fa: \"\\u200e\\xa4\\xa0#,##0.00;\\u200e(\\xa4\\xa0#,##0.00)\",\r\n    \"fa-AF\": \"\\xa4\\xa0#,##0.00;\\u200e(\\xa4\\xa0#,##0.00)\",\r\n    ff: \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Adlm\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-BF\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-CM\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-GH\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-GM\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-GW\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-LR\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-MR\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-NE\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-NG\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-SL\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Adlm-SN\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ff-Latn\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-BF\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-CM\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-GH\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-GM\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-GN\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-GW\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-LR\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-MR\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-NE\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-NG\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ff-Latn-SL\": \"#,##0.00\\xa0\\xa4\",\r\n    fi: \"#,##0.00\\xa0\\xa4\",\r\n    fil: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    fo: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fo-DK\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    fr: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-BE\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-BF\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-BI\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-BJ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-BL\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CA\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CD\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CF\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CG\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CH\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CI\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-CM\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-DJ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-DZ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-GA\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-GF\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-GN\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-GP\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-GQ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-HT\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-KM\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-LU\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MA\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MC\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MF\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MG\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-ML\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MQ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MR\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-MU\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-NC\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-NE\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-PF\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-PM\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-RE\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-RW\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-SC\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-SN\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-SY\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-TD\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-TG\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-TN\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-VU\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-WF\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"fr-YT\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    fur: \"\\xa4\\xa0#,##0.00\",\r\n    fy: \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    ga: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ga-GB\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    gd: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    gl: \"#,##0.00\\xa0\\xa4\",\r\n    gsw: \"#,##0.00\\xa0\\xa4\",\r\n    \"gsw-FR\": \"#,##0.00\\xa0\\xa4\",\r\n    \"gsw-LI\": \"#,##0.00\\xa0\\xa4\",\r\n    gu: \"\\xa4#,##,##0.00;(\\xa4#,##,##0.00)\",\r\n    guz: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    gv: \"\\xa4#,##0.00\",\r\n    ha: \"\\xa4\\xa0#,##0.00\",\r\n    \"ha-GH\": \"\\xa4\\xa0#,##0.00\",\r\n    \"ha-NE\": \"\\xa4\\xa0#,##0.00\",\r\n    haw: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    he: \"#,##0.00\\xa0\\xa4\",\r\n    hi: \"\\xa4#,##,##0.00\",\r\n    \"hi-Latn\": \"\\xa4#,##,##0.00\",\r\n    hr: \"#,##0.00\\xa0\\xa4\",\r\n    \"hr-BA\": \"#,##0.00\\xa0\\xa4\",\r\n    hsb: \"#,##0.00\\xa0\\xa4\",\r\n    hu: \"#,##0.00\\xa0\\xa4\",\r\n    hy: \"#,##0.00\\xa0\\xa4\",\r\n    ia: \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    id: \"\\xa4#,##0.00\",\r\n    ig: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ii: \"\\xa4\\xa0#,##0.00\",\r\n    is: \"#,##0.00\\xa0\\xa4\",\r\n    it: \"#,##0.00\\xa0\\xa4\",\r\n    \"it-CH\": \"#,##0.00\\xa0\\xa4\",\r\n    \"it-SM\": \"#,##0.00\\xa0\\xa4\",\r\n    \"it-VA\": \"#,##0.00\\xa0\\xa4\",\r\n    ja: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    jgo: \"\\xa4\\xa0#,##0.00\",\r\n    jmc: \"\\xa4#,##0.00\",\r\n    jv: \"\\xa4\\xa0#,##0.00\",\r\n    ka: \"#,##0.00\\xa0\\xa4\",\r\n    kab: \"#,##0.00\\xa4\",\r\n    kam: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    kde: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    kea: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    kgp: \"\\xa4\\xa0#,##0.00\",\r\n    khq: \"#,##0.00\\xa4\",\r\n    ki: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    kk: \"#,##0.00\\xa0\\xa4\",\r\n    kkj: \"\\xa4\\xa0#,##0.00\",\r\n    kl: \"\\xa4#,##0.00;\\xa4-#,##0.00\",\r\n    kln: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    km: \"#,##0.00\\xa4;(#,##0.00\\xa4)\",\r\n    kn: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ko: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ko-KP\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    kok: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ks: \"\\xa4#,##0.00\",\r\n    \"ks-Arab\": \"\\xa4#,##0.00\",\r\n    \"ks-Deva\": \"\\xa4\\xa0#,##0.00\",\r\n    ksb: \"#,##0.00\\xa4\",\r\n    ksf: \"#,##0.00\\xa0\\xa4\",\r\n    ksh: \"#,##0.00\\xa0\\xa4\",\r\n    ku: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    kw: \"\\xa4#,##0.00\",\r\n    ky: \"#,##0.00\\xa0\\xa4\",\r\n    lag: \"\\xa4\\xa0#,##0.00\",\r\n    lb: \"#,##0.00\\xa0\\xa4\",\r\n    lg: \"#,##0.00\\xa4\",\r\n    lkt: \"\\xa4\\xa0#,##0.00\",\r\n    ln: \"#,##0.00\\xa0\\xa4\",\r\n    \"ln-AO\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ln-CF\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ln-CG\": \"#,##0.00\\xa0\\xa4\",\r\n    lo: \"\\xa4#,##0.00;\\xa4-#,##0.00\",\r\n    lrc: \"\\xa4\\xa0#,##0.00\",\r\n    \"lrc-IQ\": \"\\xa4\\xa0#,##0.00\",\r\n    lt: \"#,##0.00\\xa0\\xa4\",\r\n    lu: \"#,##0.00\\xa4\",\r\n    luo: \"#,##0.00\\xa4\",\r\n    luy: \"\\xa4#,##0.00;\\xa4-\\xa0#,##0.00\",\r\n    lv: \"#,##0.00\\xa0\\xa4\",\r\n    mai: \"\\xa4\\xa0#,##0.00\",\r\n    mas: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"mas-TZ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    mer: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    mfe: \"\\xa4\\xa0#,##0.00\",\r\n    mg: \"\\xa4#,##0.00\",\r\n    mgh: \"\\xa4\\xa0#,##0.00\",\r\n    mgo: \"\\xa4\\xa0#,##0.00\",\r\n    mi: \"\\xa4\\xa0#,##0.00\",\r\n    mk: \"#,##0.00\\xa0\\xa4\",\r\n    ml: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    mn: \"\\xa4\\xa0#,##0.00\",\r\n    mni: \"\\xa4\\xa0#,##0.00\",\r\n    \"mni-Beng\": \"\\xa4\\xa0#,##0.00\",\r\n    mr: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ms: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ms-BN\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ms-ID\": \"\\xa4#,##0.00\",\r\n    \"ms-SG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    mt: \"\\xa4#,##0.00\",\r\n    mua: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    my: \"\\xa4\\xa0#,##0.00\",\r\n    mzn: \"\\xa4\\xa0#,##0.00\",\r\n    naq: \"\\xa4#,##0.00\",\r\n    nb: \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nb-SJ\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    nd: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    nds: \"\\xa4\\xa0#,##0.00\",\r\n    \"nds-NL\": \"\\xa4\\xa0#,##0.00\",\r\n    ne: \"\\xa4\\xa0#,##,##0.00\",\r\n    \"ne-IN\": \"\\xa4\\xa0#,##,##0.00\",\r\n    nl: \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nl-AW\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nl-BE\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nl-BQ\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nl-CW\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nl-SR\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    \"nl-SX\": \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    nmg: \"#,##0.00\\xa0\\xa4\",\r\n    nn: \"#,##0.00\\xa0\\xa4\",\r\n    nnh: \"\\xa4\\xa0#,##0.00\",\r\n    no: \"\\xa4\\xa0#,##0.00;(\\xa4\\xa0#,##0.00)\",\r\n    nus: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    nyn: \"\\xa4#,##0.00\",\r\n    om: \"\\xa4#,##0.00\",\r\n    \"om-KE\": \"\\xa4#,##0.00\",\r\n    or: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    os: \"\\xa4\\xa0#,##0.00\",\r\n    \"os-RU\": \"\\xa4\\xa0#,##0.00\",\r\n    pa: \"\\xa4\\xa0#,##0.00\",\r\n    \"pa-Arab\": \"\\xa4\\xa0#,##0.00\",\r\n    \"pa-Guru\": \"\\xa4\\xa0#,##0.00\",\r\n    pcm: \"\\xa4#,##0.00\",\r\n    pl: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    ps: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ps-PK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    pt: \"\\xa4\\xa0#,##0.00\",\r\n    \"pt-AO\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-CH\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-CV\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-GQ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-GW\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-LU\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-MO\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-MZ\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-PT\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-ST\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"pt-TL\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    qu: \"\\xa4\\xa0#,##0.00\",\r\n    \"qu-BO\": \"\\xa4\\xa0#,##0.00\",\r\n    \"qu-EC\": \"\\xa4\\xa0#,##0.00\",\r\n    rm: \"#,##0.00\\xa0\\xa4\",\r\n    rn: \"#,##0.00\\xa4\",\r\n    ro: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"ro-MD\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    rof: \"\\xa4#,##0.00\",\r\n    ru: \"#,##0.00\\xa0\\xa4\",\r\n    \"ru-BY\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ru-KG\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ru-KZ\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ru-MD\": \"#,##0.00\\xa0\\xa4\",\r\n    \"ru-UA\": \"#,##0.00\\xa0\\xa4\",\r\n    rw: \"\\xa4\\xa0#,##0.00\",\r\n    rwk: \"#,##0.00\\xa4\",\r\n    sa: \"\\xa4\\xa0#,##0.00\",\r\n    sah: \"#,##0.00\\xa0\\xa4\",\r\n    saq: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    sat: \"\\xa4\\xa0#,##0.00\",\r\n    \"sat-Olck\": \"\\xa4\\xa0#,##0.00\",\r\n    sbp: \"#,##0.00\\xa4\",\r\n    sc: \"#,##0.00\\xa0\\xa4\",\r\n    sd: \"\\xa4\\xa0#,##0.00\",\r\n    \"sd-Arab\": \"\\xa4\\xa0#,##0.00\",\r\n    \"sd-Deva\": \"\\xa4\\xa0#,##0.00\",\r\n    se: \"#,##0.00\\xa0\\xa4\",\r\n    \"se-FI\": \"#,##0.00\\xa0\\xa4\",\r\n    \"se-SE\": \"#,##0.00\\xa0\\xa4\",\r\n    seh: \"#,##0.00\\xa4\",\r\n    ses: \"#,##0.00\\xa4\",\r\n    sg: \"\\xa4#,##0.00;\\xa4-#,##0.00\",\r\n    shi: \"#,##0.00\\xa4\",\r\n    \"shi-Latn\": \"#,##0.00\\xa4\",\r\n    \"shi-Tfng\": \"#,##0.00\\xa4\",\r\n    si: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    sk: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    sl: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    smn: \"#,##0.00\\xa0\\xa4\",\r\n    sn: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    so: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"so-DJ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"so-ET\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"so-KE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    sq: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sq-MK\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sq-XK\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    sr: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Cyrl\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Cyrl-BA\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Cyrl-ME\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Cyrl-XK\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Latn\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Latn-BA\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Latn-ME\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    \"sr-Latn-XK\": \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    su: \"\\xa4#,##0.00\",\r\n    \"su-Latn\": \"\\xa4#,##0.00\",\r\n    sv: \"#,##0.00\\xa0\\xa4\",\r\n    \"sv-AX\": \"#,##0.00\\xa0\\xa4\",\r\n    \"sv-FI\": \"#,##0.00\\xa0\\xa4\",\r\n    sw: \"\\xa4\\xa0#,##0.00\",\r\n    \"sw-CD\": \"\\xa4\\xa0#,##0.00\",\r\n    \"sw-KE\": \"\\xa4\\xa0#,##0.00\",\r\n    \"sw-UG\": \"\\xa4\\xa0#,##0.00\",\r\n    ta: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ta-LK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ta-MY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ta-SG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    te: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    teo: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"teo-KE\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    tg: \"#,##0.00\\xa0\\xa4\",\r\n    th: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    ti: \"\\xa4#,##0.00\",\r\n    \"ti-ER\": \"\\xa4#,##0.00\",\r\n    tk: \"#,##0.00\\xa0\\xa4\",\r\n    to: \"\\xa4\\xa0#,##0.00\",\r\n    tr: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"tr-CY\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    tt: \"#,##0.00\\xa0\\xa4\",\r\n    twq: \"#,##0.00\\xa4\",\r\n    tzm: \"#,##0.00\\xa0\\xa4\",\r\n    ug: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    uk: \"#,##0.00\\xa0\\xa4\",\r\n    und: \"\\xa4\\xa0#,##0.00\",\r\n    ur: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"ur-IN\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    uz: \"#,##0.00\\xa0\\xa4\",\r\n    \"uz-Arab\": \"\\xa4\\xa0#,##0.00\",\r\n    \"uz-Cyrl\": \"#,##0.00\\xa0\\xa4\",\r\n    \"uz-Latn\": \"#,##0.00\\xa0\\xa4\",\r\n    vai: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"vai-Latn\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"vai-Vaii\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    vi: \"#,##0.00\\xa0\\xa4\",\r\n    vun: \"\\xa4#,##0.00\",\r\n    wae: \"\\xa4\\xa0#,##0.00\",\r\n    wo: \"\\xa4\\xa0#,##0.00\",\r\n    xh: \"\\xa4#,##0.00\",\r\n    xog: \"#,##0.00\\xa0\\xa4\",\r\n    yav: \"#,##0.00\\xa0\\xa4;(#,##0.00\\xa0\\xa4)\",\r\n    yi: \"\\xa4\\xa0#,##0.00\",\r\n    yo: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"yo-BJ\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    yrl: \"\\xa4\\xa0#,##0.00\",\r\n    \"yrl-CO\": \"\\xa4\\xa0#,##0.00\",\r\n    \"yrl-VE\": \"\\xa4\\xa0#,##0.00\",\r\n    yue: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"yue-Hans\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"yue-Hant\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    zgh: \"#,##0.00\\xa4\",\r\n    zh: \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hans\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hans-HK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hans-MO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hans-SG\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hant\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hant-HK\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    \"zh-Hant-MO\": \"\\xa4#,##0.00;(\\xa4#,##0.00)\",\r\n    zu: \"\\xa4#,##0.00;(\\xa4#,##0.00)\"\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GACD,uCAAuC;;;;uCACxB;IACX,IAAI;IACJ,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,WAAW;IACX,WAAW;IACX,KAAK;IACL,IAAI;IACJ,aAAa;IACb,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,WAAW;IACX,WAAW;IACX,IAAI;IACJ,SAAS;IACT,kBAAkB;IAClB,SAAS;IACT,SAAS;IACT,KAAK;IACL,UAAU;IACV,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,UAAU;IACV,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,KAAK;IACL,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,UAAU;IACV,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,WAAW;IACX,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,WAAW;IACX,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,cAAc;IACd,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,UAAU;IACV,UAAU;IACV,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,IAAI;IACJ,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,KAAK;IACL,IAAI;IACJ,WAAW;IACX,WAAW;IACX,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,KAAK;IACL,UAAU;IACV,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,UAAU;IACV,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,YAAY;IACZ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,UAAU;IACV,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,WAAW;IACX,WAAW;IACX,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,KAAK;IACL,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,YAAY;IACZ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,WAAW;IACX,IAAI;IACJ,SAAS;IACT,SAAS;IACT,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,YAAY;IACZ,YAAY;IACZ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,IAAI;IACJ,WAAW;IACX,cAAc;IACd,cAAc;IACd,cAAc;IACd,WAAW;IACX,cAAc;IACd,cAAc;IACd,cAAc;IACd,IAAI;IACJ,WAAW;IACX,IAAI;IACJ,SAAS;IACT,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,KAAK;IACL,UAAU;IACV,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,WAAW;IACX,WAAW;IACX,WAAW;IACX,KAAK;IACL,YAAY;IACZ,YAAY;IACZ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,KAAK;IACL,UAAU;IACV,UAAU;IACV,KAAK;IACL,YAAY;IACZ,YAAY;IACZ,KAAK;IACL,IAAI;IACJ,WAAW;IACX,cAAc;IACd,cAAc;IACd,cAAc;IACd,WAAW;IACX,cAAc;IACd,cAAc;IACd,IAAI;AACR", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5455, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/intl/number.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/intl/number.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    config as dxConfig\r\n} from \"../../../../common\";\r\nimport localizationCoreUtils from \"../core\";\r\nimport openXmlCurrencyFormat from \"../open_xml_currency_format\";\r\nimport accountingFormats from \"../cldr-data/accounting_formats\";\r\nconst CURRENCY_STYLES = [\"standard\", \"accounting\"];\r\nconst MAX_FRACTION_DIGITS = 20;\r\nconst detectCurrencySymbolRegex = /([^\\s0]+)?(\\s*)0*[.,]*0*(\\s*)([^\\s0]+)?/;\r\nconst formattersCache = {};\r\nconst getFormatter = format => {\r\n    const key = localizationCoreUtils.locale() + \"/\" + JSON.stringify(format);\r\n    if (!formattersCache[key]) {\r\n        formattersCache[key] = new Intl.NumberFormat(localizationCoreUtils.locale(), format).format\r\n    }\r\n    return formattersCache[key]\r\n};\r\nconst getCurrencyFormatter = currency => new Intl.NumberFormat(localizationCoreUtils.locale(), {\r\n    style: \"currency\",\r\n    currency: currency\r\n});\r\nexport default {\r\n    engine: function() {\r\n        return \"intl\"\r\n    },\r\n    _formatNumberCore: function(value, format, formatConfig) {\r\n        if (\"exponential\" === format) {\r\n            return this.callBase.apply(this, arguments)\r\n        }\r\n        return getFormatter(this._normalizeFormatConfig(format, formatConfig, value))(value)\r\n    },\r\n    _normalizeFormatConfig: function(format, formatConfig, value) {\r\n        let config;\r\n        if (\"decimal\" === format) {\r\n            const fractionDigits = String(value).split(\".\")[1];\r\n            config = {\r\n                minimumIntegerDigits: formatConfig.precision || void 0,\r\n                useGrouping: false,\r\n                maximumFractionDigits: fractionDigits && fractionDigits.length,\r\n                round: value < 0 ? \"ceil\" : \"floor\"\r\n            }\r\n        } else {\r\n            config = this._getPrecisionConfig(formatConfig.precision)\r\n        }\r\n        if (\"percent\" === format) {\r\n            config.style = \"percent\"\r\n        } else if (\"currency\" === format) {\r\n            const useAccountingStyle = formatConfig.useCurrencyAccountingStyle ?? dxConfig().defaultUseCurrencyAccountingStyle;\r\n            config.style = \"currency\";\r\n            config.currency = formatConfig.currency || dxConfig().defaultCurrency;\r\n            config.currencySign = CURRENCY_STYLES[+useAccountingStyle]\r\n        }\r\n        return config\r\n    },\r\n    _getPrecisionConfig: function(precision) {\r\n        let config;\r\n        if (null === precision) {\r\n            config = {\r\n                minimumFractionDigits: 0,\r\n                maximumFractionDigits: 20\r\n            }\r\n        } else {\r\n            config = {\r\n                minimumFractionDigits: precision || 0,\r\n                maximumFractionDigits: precision || 0\r\n            }\r\n        }\r\n        return config\r\n    },\r\n    format: function(value, format) {\r\n        if (\"number\" !== typeof value) {\r\n            return value\r\n        }\r\n        format = this._normalizeFormat(format);\r\n        if (\"default\" === format.currency) {\r\n            format.currency = dxConfig().defaultCurrency\r\n        }\r\n        if (!format || \"function\" !== typeof format && !format.type && !format.formatter) {\r\n            return getFormatter(format)(value)\r\n        }\r\n        const result = this.callBase.apply(this, arguments);\r\n        return result\r\n    },\r\n    _getCurrencySymbolInfo: function(currency) {\r\n        const formatter = getCurrencyFormatter(currency);\r\n        return this._extractCurrencySymbolInfo(formatter.format(0))\r\n    },\r\n    _extractCurrencySymbolInfo: function(currencyValueString) {\r\n        const match = detectCurrencySymbolRegex.exec(currencyValueString) || [];\r\n        const position = match[1] ? \"before\" : \"after\";\r\n        const symbol = match[1] || match[4] || \"\";\r\n        const delimiter = match[2] || match[3] || \"\";\r\n        return {\r\n            position: position,\r\n            symbol: symbol,\r\n            delimiter: delimiter\r\n        }\r\n    },\r\n    getCurrencySymbol: function(currency) {\r\n        if (!currency) {\r\n            currency = dxConfig().defaultCurrency\r\n        }\r\n        const symbolInfo = this._getCurrencySymbolInfo(currency);\r\n        return {\r\n            symbol: symbolInfo.symbol\r\n        }\r\n    },\r\n    getOpenXmlCurrencyFormat: function(currency) {\r\n        const targetCurrency = currency || dxConfig().defaultCurrency;\r\n        const currencySymbol = this._getCurrencySymbolInfo(targetCurrency).symbol;\r\n        const closestAccountingFormat = localizationCoreUtils.getValueByClosestLocale((locale => accountingFormats[locale]));\r\n        return openXmlCurrencyFormat(currencySymbol, closestAccountingFormat)\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AACA;AACA;;;;;AACA,MAAM,kBAAkB;IAAC;IAAY;CAAa;AAClD,MAAM,sBAAsB;AAC5B,MAAM,4BAA4B;AAClC,MAAM,kBAAkB,CAAC;AACzB,MAAM,eAAe,CAAA;IACjB,MAAM,MAAM,8KAAA,CAAA,UAAqB,CAAC,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC;IAClE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;QACvB,eAAe,CAAC,IAAI,GAAG,IAAI,KAAK,YAAY,CAAC,8KAAA,CAAA,UAAqB,CAAC,MAAM,IAAI,QAAQ,MAAM;IAC/F;IACA,OAAO,eAAe,CAAC,IAAI;AAC/B;AACA,MAAM,uBAAuB,CAAA,WAAY,IAAI,KAAK,YAAY,CAAC,8KAAA,CAAA,UAAqB,CAAC,MAAM,IAAI;QAC3F,OAAO;QACP,UAAU;IACd;uCACe;IACX,QAAQ;QACJ,OAAO;IACX;IACA,mBAAmB,SAAS,KAAK,EAAE,MAAM,EAAE,YAAY;QACnD,IAAI,kBAAkB,QAAQ;YAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QACrC;QACA,OAAO,aAAa,IAAI,CAAC,sBAAsB,CAAC,QAAQ,cAAc,QAAQ;IAClF;IACA,wBAAwB,SAAS,MAAM,EAAE,YAAY,EAAE,KAAK;QACxD,IAAI;QACJ,IAAI,cAAc,QAAQ;YACtB,MAAM,iBAAiB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;YAClD,SAAS;gBACL,sBAAsB,aAAa,SAAS,IAAI,KAAK;gBACrD,aAAa;gBACb,uBAAuB,kBAAkB,eAAe,MAAM;gBAC9D,OAAO,QAAQ,IAAI,SAAS;YAChC;QACJ,OAAO;YACH,SAAS,IAAI,CAAC,mBAAmB,CAAC,aAAa,SAAS;QAC5D;QACA,IAAI,cAAc,QAAQ;YACtB,OAAO,KAAK,GAAG;QACnB,OAAO,IAAI,eAAe,QAAQ;gBACH;YAA3B,MAAM,qBAAqB,CAAA,2CAAA,aAAa,0BAA0B,cAAvC,sDAAA,2CAA2C,CAAA,GAAA,6LAAA,CAAA,SAAQ,AAAD,IAAI,iCAAiC;YAClH,OAAO,KAAK,GAAG;YACf,OAAO,QAAQ,GAAG,aAAa,QAAQ,IAAI,CAAA,GAAA,6LAAA,CAAA,SAAQ,AAAD,IAAI,eAAe;YACrE,OAAO,YAAY,GAAG,eAAe,CAAC,CAAC,mBAAmB;QAC9D;QACA,OAAO;IACX;IACA,qBAAqB,SAAS,SAAS;QACnC,IAAI;QACJ,IAAI,SAAS,WAAW;YACpB,SAAS;gBACL,uBAAuB;gBACvB,uBAAuB;YAC3B;QACJ,OAAO;YACH,SAAS;gBACL,uBAAuB,aAAa;gBACpC,uBAAuB,aAAa;YACxC;QACJ;QACA,OAAO;IACX;IACA,QAAQ,SAAS,KAAK,EAAE,MAAM;QAC1B,IAAI,aAAa,OAAO,OAAO;YAC3B,OAAO;QACX;QACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QAC/B,IAAI,cAAc,OAAO,QAAQ,EAAE;YAC/B,OAAO,QAAQ,GAAG,CAAA,GAAA,6LAAA,CAAA,SAAQ,AAAD,IAAI,eAAe;QAChD;QACA,IAAI,CAAC,UAAU,eAAe,OAAO,UAAU,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,SAAS,EAAE;YAC9E,OAAO,aAAa,QAAQ;QAChC;QACA,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QACzC,OAAO;IACX;IACA,wBAAwB,SAAS,QAAQ;QACrC,MAAM,YAAY,qBAAqB;QACvC,OAAO,IAAI,CAAC,0BAA0B,CAAC,UAAU,MAAM,CAAC;IAC5D;IACA,4BAA4B,SAAS,mBAAmB;QACpD,MAAM,QAAQ,0BAA0B,IAAI,CAAC,wBAAwB,EAAE;QACvE,MAAM,WAAW,KAAK,CAAC,EAAE,GAAG,WAAW;QACvC,MAAM,SAAS,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;QACvC,MAAM,YAAY,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI;QAC1C,OAAO;YACH,UAAU;YACV,QAAQ;YACR,WAAW;QACf;IACJ;IACA,mBAAmB,SAAS,QAAQ;QAChC,IAAI,CAAC,UAAU;YACX,WAAW,CAAA,GAAA,6LAAA,CAAA,SAAQ,AAAD,IAAI,eAAe;QACzC;QACA,MAAM,aAAa,IAAI,CAAC,sBAAsB,CAAC;QAC/C,OAAO;YACH,QAAQ,WAAW,MAAM;QAC7B;IACJ;IACA,0BAA0B,SAAS,QAAQ;QACvC,MAAM,iBAAiB,YAAY,CAAA,GAAA,6LAAA,CAAA,SAAQ,AAAD,IAAI,eAAe;QAC7D,MAAM,iBAAiB,IAAI,CAAC,sBAAsB,CAAC,gBAAgB,MAAM;QACzE,MAAM,0BAA0B,8KAAA,CAAA,UAAqB,CAAC,uBAAuB,CAAE,CAAA,SAAU,4MAAA,CAAA,UAAiB,CAAC,OAAO;QAClH,OAAO,CAAA,GAAA,kMAAA,CAAA,UAAqB,AAAD,EAAE,gBAAgB;IACjD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5590, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/number.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/number.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport dependencyInjector from \"../../../core/utils/dependency_injector\";\r\nimport {\r\n    escapeRegExp\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isPlainObject\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    getFormatter\r\n} from \"./ldml/number\";\r\nimport config from \"../../../core/config\";\r\nimport errors from \"../../../core/errors\";\r\nimport {\r\n    toFixed\r\n} from \"./utils\";\r\nimport currencyLocalization from \"./currency\";\r\nimport intlNumberLocalization from \"./intl/number\";\r\nconst hasIntl = \"undefined\" !== typeof Intl;\r\nconst MAX_LARGE_NUMBER_POWER = 4;\r\nconst DECIMAL_BASE = 10;\r\nconst NUMERIC_FORMATS = [\"currency\", \"fixedpoint\", \"exponential\", \"percent\", \"decimal\"];\r\nconst LargeNumberFormatPostfixes = {\r\n    1: \"K\",\r\n    2: \"M\",\r\n    3: \"B\",\r\n    4: \"T\"\r\n};\r\nconst LargeNumberFormatPowers = {\r\n    largenumber: \"auto\",\r\n    thousands: 1,\r\n    millions: 2,\r\n    billions: 3,\r\n    trillions: 4\r\n};\r\nconst numberLocalization = dependencyInjector({\r\n    engine: function() {\r\n        return \"base\"\r\n    },\r\n    numericFormats: NUMERIC_FORMATS,\r\n    defaultLargeNumberFormatPostfixes: LargeNumberFormatPostfixes,\r\n    _parseNumberFormatString: function(formatType) {\r\n        const formatObject = {};\r\n        if (!formatType || \"string\" !== typeof formatType) {\r\n            return\r\n        }\r\n        const formatList = formatType.toLowerCase().split(\" \");\r\n        each(formatList, ((index, value) => {\r\n            if (NUMERIC_FORMATS.includes(value)) {\r\n                formatObject.formatType = value\r\n            } else if (value in LargeNumberFormatPowers) {\r\n                formatObject.power = LargeNumberFormatPowers[value]\r\n            }\r\n        }));\r\n        if (formatObject.power && !formatObject.formatType) {\r\n            formatObject.formatType = \"fixedpoint\"\r\n        }\r\n        if (formatObject.formatType) {\r\n            return formatObject\r\n        }\r\n    },\r\n    _calculateNumberPower: function(value, base, minPower, maxPower) {\r\n        let number = Math.abs(value);\r\n        let power = 0;\r\n        if (number > 1) {\r\n            while (number && number >= base && (void 0 === maxPower || power < maxPower)) {\r\n                power++;\r\n                number /= base\r\n            }\r\n        } else if (number > 0 && number < 1) {\r\n            while (number < 1 && (void 0 === minPower || power > minPower)) {\r\n                power--;\r\n                number *= base\r\n            }\r\n        }\r\n        return power\r\n    },\r\n    _getNumberByPower: function(number, power, base) {\r\n        let result = number;\r\n        while (power > 0) {\r\n            result /= base;\r\n            power--\r\n        }\r\n        while (power < 0) {\r\n            result *= base;\r\n            power++\r\n        }\r\n        return result\r\n    },\r\n    _formatNumber: function(value, formatObject, formatConfig) {\r\n        if (\"auto\" === formatObject.power) {\r\n            formatObject.power = this._calculateNumberPower(value, 1e3, 0, 4)\r\n        }\r\n        if (formatObject.power) {\r\n            value = this._getNumberByPower(value, formatObject.power, 1e3)\r\n        }\r\n        const powerPostfix = this.defaultLargeNumberFormatPostfixes[formatObject.power] || \"\";\r\n        let result = this._formatNumberCore(value, formatObject.formatType, formatConfig);\r\n        result = result.replace(/(\\d|.$)(\\D*)$/, \"$1\" + powerPostfix + \"$2\");\r\n        return result\r\n    },\r\n    _formatNumberExponential: function(value, formatConfig) {\r\n        let power = this._calculateNumberPower(value, 10);\r\n        let number = this._getNumberByPower(value, power, 10);\r\n        if (void 0 === formatConfig.precision) {\r\n            formatConfig.precision = 1\r\n        }\r\n        if (number.toFixed(formatConfig.precision || 0) >= 10) {\r\n            power++;\r\n            number /= 10\r\n        }\r\n        const powString = (power >= 0 ? \"+\" : \"\") + power.toString();\r\n        return this._formatNumberCore(number, \"fixedpoint\", formatConfig) + \"E\" + powString\r\n    },\r\n    _addZeroes: function(value, precision) {\r\n        const multiplier = Math.pow(10, precision);\r\n        const sign = value < 0 ? \"-\" : \"\";\r\n        value = (Math.abs(value) * multiplier >>> 0) / multiplier;\r\n        let result = value.toString();\r\n        while (result.length < precision) {\r\n            result = \"0\" + result\r\n        }\r\n        return sign + result\r\n    },\r\n    _addGroupSeparators: function(value) {\r\n        const parts = value.toString().split(\".\");\r\n        return parts[0].replace(/\\B(?=(\\d{3})+(?!\\d))/g, config().thousandsSeparator) + (parts[1] ? config().decimalSeparator + parts[1] : \"\")\r\n    },\r\n    _formatNumberCore: function(value, format, formatConfig) {\r\n        if (\"exponential\" === format) {\r\n            return this._formatNumberExponential(value, formatConfig)\r\n        }\r\n        if (\"decimal\" !== format && null !== formatConfig.precision) {\r\n            formatConfig.precision = formatConfig.precision || 0\r\n        }\r\n        if (\"percent\" === format) {\r\n            value *= 100\r\n        }\r\n        if (void 0 !== formatConfig.precision) {\r\n            if (\"decimal\" === format) {\r\n                value = this._addZeroes(value, formatConfig.precision)\r\n            } else {\r\n                value = null === formatConfig.precision ? value.toPrecision() : toFixed(value, formatConfig.precision)\r\n            }\r\n        }\r\n        if (\"decimal\" !== format) {\r\n            value = this._addGroupSeparators(value)\r\n        } else {\r\n            value = value.toString().replace(\".\", config().decimalSeparator)\r\n        }\r\n        if (\"percent\" === format) {\r\n            value += \"%\"\r\n        }\r\n        return value\r\n    },\r\n    _normalizeFormat: function(format) {\r\n        if (!format) {\r\n            return {}\r\n        }\r\n        if (\"function\" === typeof format) {\r\n            return format\r\n        }\r\n        if (!isPlainObject(format)) {\r\n            format = {\r\n                type: format\r\n            }\r\n        }\r\n        return format\r\n    },\r\n    _getSeparators: function() {\r\n        return {\r\n            decimalSeparator: this.getDecimalSeparator(),\r\n            thousandsSeparator: this.getThousandsSeparator()\r\n        }\r\n    },\r\n    getThousandsSeparator: function() {\r\n        return this.format(1e4, \"fixedPoint\")[2]\r\n    },\r\n    getDecimalSeparator: function() {\r\n        return this.format(1.2, {\r\n            type: \"fixedPoint\",\r\n            precision: 1\r\n        })[1]\r\n    },\r\n    convertDigits: function(value, toStandard) {\r\n        const digits = this.format(90, \"decimal\");\r\n        if (\"string\" !== typeof value || \"0\" === digits[1]) {\r\n            return value\r\n        }\r\n        const fromFirstDigit = toStandard ? digits[1] : \"0\";\r\n        const toFirstDigit = toStandard ? \"0\" : digits[1];\r\n        const fromLastDigit = toStandard ? digits[0] : \"9\";\r\n        const regExp = new RegExp(\"[\" + fromFirstDigit + \"-\" + fromLastDigit + \"]\", \"g\");\r\n        return value.replace(regExp, (char => String.fromCharCode(char.charCodeAt(0) + (toFirstDigit.charCodeAt(0) - fromFirstDigit.charCodeAt(0)))))\r\n    },\r\n    getNegativeEtalonRegExp: function(format) {\r\n        const separators = this._getSeparators();\r\n        const digitalRegExp = new RegExp(\"[0-9\" + escapeRegExp(separators.decimalSeparator + separators.thousandsSeparator) + \"]+\", \"g\");\r\n        let negativeEtalon = this.format(-1, format).replace(digitalRegExp, \"1\");\r\n        [\"\\\\\", \"(\", \")\", \"[\", \"]\", \"*\", \"+\", \"$\", \"^\", \"?\", \"|\", \"{\", \"}\"].forEach((char => {\r\n            negativeEtalon = negativeEtalon.replace(new RegExp(`\\\\${char}`, \"g\"), `\\\\${char}`)\r\n        }));\r\n        negativeEtalon = negativeEtalon.replace(/ /g, \"\\\\s\");\r\n        negativeEtalon = negativeEtalon.replace(/1/g, \".*\");\r\n        return new RegExp(negativeEtalon, \"g\")\r\n    },\r\n    getSign: function(text, format) {\r\n        if (!format) {\r\n            if (\"-\" === text.replace(/[^0-9-]/g, \"\").charAt(0)) {\r\n                return -1\r\n            }\r\n            return 1\r\n        }\r\n        const negativeEtalon = this.getNegativeEtalonRegExp(format);\r\n        return text.match(negativeEtalon) ? -1 : 1\r\n    },\r\n    format: function(value, format) {\r\n        if (\"number\" !== typeof value) {\r\n            return value\r\n        }\r\n        if (\"number\" === typeof format) {\r\n            return value\r\n        }\r\n        format = format && format.formatter || format;\r\n        if (\"function\" === typeof format) {\r\n            return format(value)\r\n        }\r\n        format = this._normalizeFormat(format);\r\n        if (!format.type) {\r\n            format.type = \"decimal\"\r\n        }\r\n        const numberConfig = this._parseNumberFormatString(format.type);\r\n        if (!numberConfig) {\r\n            const formatterConfig = this._getSeparators();\r\n            formatterConfig.unlimitedIntegerDigits = format.unlimitedIntegerDigits;\r\n            const formatter = getFormatter(format.type, formatterConfig)(value);\r\n            const result = this.convertDigits(formatter);\r\n            return result\r\n        }\r\n        return this._formatNumber(value, numberConfig, format)\r\n    },\r\n    parse: function(text, format) {\r\n        if (!text) {\r\n            return\r\n        }\r\n        if (format && format.parser) {\r\n            return format.parser(text)\r\n        }\r\n        text = this.convertDigits(text, true);\r\n        if (format && \"string\" !== typeof format) {\r\n            errors.log(\"W0011\")\r\n        }\r\n        const decimalSeparator = this.getDecimalSeparator();\r\n        const regExp = new RegExp(\"[^0-9\" + escapeRegExp(decimalSeparator) + \"]\", \"g\");\r\n        const cleanedText = text.replace(regExp, \"\").replace(decimalSeparator, \".\").replace(/\\.$/g, \"\");\r\n        if (\".\" === cleanedText || \"\" === cleanedText) {\r\n            return null\r\n        }\r\n        if (this._calcSignificantDigits(cleanedText) > 15) {\r\n            return NaN\r\n        }\r\n        let parsed = +cleanedText * this.getSign(text, format);\r\n        format = this._normalizeFormat(format);\r\n        const formatConfig = this._parseNumberFormatString(format.type);\r\n        let power = null === formatConfig || void 0 === formatConfig ? void 0 : formatConfig.power;\r\n        if (power) {\r\n            if (\"auto\" === power) {\r\n                const match = text.match(/\\d(K|M|B|T)/);\r\n                if (match) {\r\n                    power = Object.keys(LargeNumberFormatPostfixes).find((power => LargeNumberFormatPostfixes[power] === match[1]))\r\n                }\r\n            }\r\n            parsed *= Math.pow(10, 3 * power)\r\n        }\r\n        if (\"percent\" === (null === formatConfig || void 0 === formatConfig ? void 0 : formatConfig.formatType)) {\r\n            parsed /= 100\r\n        }\r\n        return parsed\r\n    },\r\n    _calcSignificantDigits: function(text) {\r\n        const [integer, fractional] = text.split(\".\");\r\n        const calcDigitsAfterLeadingZeros = digits => {\r\n            let index = -1;\r\n            for (let i = 0; i < digits.length; i++) {\r\n                if (\"0\" !== digits[i]) {\r\n                    index = i;\r\n                    break\r\n                }\r\n            }\r\n            return index > -1 ? digits.length - index : 0\r\n        };\r\n        let result = 0;\r\n        if (integer) {\r\n            result += calcDigitsAfterLeadingZeros(integer.split(\"\"))\r\n        }\r\n        if (fractional) {\r\n            result += calcDigitsAfterLeadingZeros(fractional.split(\"\").reverse())\r\n        }\r\n        return result\r\n    }\r\n});\r\nnumberLocalization.inject(currencyLocalization);\r\nif (hasIntl) {\r\n    numberLocalization.inject(intlNumberLocalization)\r\n}\r\nexport default numberLocalization;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAGA;AACA;AACA;AAGA;AACA;;;;;;;;;;;AACA,MAAM,UAAU,gBAAgB,OAAO;AACvC,MAAM,yBAAyB;AAC/B,MAAM,eAAe;AACrB,MAAM,kBAAkB;IAAC;IAAY;IAAc;IAAe;IAAW;CAAU;AACvF,MAAM,6BAA6B;IAC/B,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,0BAA0B;IAC5B,aAAa;IACb,WAAW;IACX,UAAU;IACV,UAAU;IACV,WAAW;AACf;AACA,MAAM,qBAAqB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;IAC1C,QAAQ;QACJ,OAAO;IACX;IACA,gBAAgB;IAChB,mCAAmC;IACnC,0BAA0B,SAAS,UAAU;QACzC,MAAM,eAAe,CAAC;QACtB,IAAI,CAAC,cAAc,aAAa,OAAO,YAAY;YAC/C;QACJ;QACA,MAAM,aAAa,WAAW,WAAW,GAAG,KAAK,CAAC;QAClD,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,YAAa,CAAC,OAAO;YACtB,IAAI,gBAAgB,QAAQ,CAAC,QAAQ;gBACjC,aAAa,UAAU,GAAG;YAC9B,OAAO,IAAI,SAAS,yBAAyB;gBACzC,aAAa,KAAK,GAAG,uBAAuB,CAAC,MAAM;YACvD;QACJ;QACA,IAAI,aAAa,KAAK,IAAI,CAAC,aAAa,UAAU,EAAE;YAChD,aAAa,UAAU,GAAG;QAC9B;QACA,IAAI,aAAa,UAAU,EAAE;YACzB,OAAO;QACX;IACJ;IACA,uBAAuB,SAAS,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ;QAC3D,IAAI,SAAS,KAAK,GAAG,CAAC;QACtB,IAAI,QAAQ;QACZ,IAAI,SAAS,GAAG;YACZ,MAAO,UAAU,UAAU,QAAQ,CAAC,KAAK,MAAM,YAAY,QAAQ,QAAQ,EAAG;gBAC1E;gBACA,UAAU;YACd;QACJ,OAAO,IAAI,SAAS,KAAK,SAAS,GAAG;YACjC,MAAO,SAAS,KAAK,CAAC,KAAK,MAAM,YAAY,QAAQ,QAAQ,EAAG;gBAC5D;gBACA,UAAU;YACd;QACJ;QACA,OAAO;IACX;IACA,mBAAmB,SAAS,MAAM,EAAE,KAAK,EAAE,IAAI;QAC3C,IAAI,SAAS;QACb,MAAO,QAAQ,EAAG;YACd,UAAU;YACV;QACJ;QACA,MAAO,QAAQ,EAAG;YACd,UAAU;YACV;QACJ;QACA,OAAO;IACX;IACA,eAAe,SAAS,KAAK,EAAE,YAAY,EAAE,YAAY;QACrD,IAAI,WAAW,aAAa,KAAK,EAAE;YAC/B,aAAa,KAAK,GAAG,IAAI,CAAC,qBAAqB,CAAC,OAAO,KAAK,GAAG;QACnE;QACA,IAAI,aAAa,KAAK,EAAE;YACpB,QAAQ,IAAI,CAAC,iBAAiB,CAAC,OAAO,aAAa,KAAK,EAAE;QAC9D;QACA,MAAM,eAAe,IAAI,CAAC,iCAAiC,CAAC,aAAa,KAAK,CAAC,IAAI;QACnF,IAAI,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,aAAa,UAAU,EAAE;QACpE,SAAS,OAAO,OAAO,CAAC,iBAAiB,OAAO,eAAe;QAC/D,OAAO;IACX;IACA,0BAA0B,SAAS,KAAK,EAAE,YAAY;QAClD,IAAI,QAAQ,IAAI,CAAC,qBAAqB,CAAC,OAAO;QAC9C,IAAI,SAAS,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;QAClD,IAAI,KAAK,MAAM,aAAa,SAAS,EAAE;YACnC,aAAa,SAAS,GAAG;QAC7B;QACA,IAAI,OAAO,OAAO,CAAC,aAAa,SAAS,IAAI,MAAM,IAAI;YACnD;YACA,UAAU;QACd;QACA,MAAM,YAAY,CAAC,SAAS,IAAI,MAAM,EAAE,IAAI,MAAM,QAAQ;QAC1D,OAAO,IAAI,CAAC,iBAAiB,CAAC,QAAQ,cAAc,gBAAgB,MAAM;IAC9E;IACA,YAAY,SAAS,KAAK,EAAE,SAAS;QACjC,MAAM,aAAa,KAAK,GAAG,CAAC,IAAI;QAChC,MAAM,OAAO,QAAQ,IAAI,MAAM;QAC/B,QAAQ,CAAC,KAAK,GAAG,CAAC,SAAS,eAAe,CAAC,IAAI;QAC/C,IAAI,SAAS,MAAM,QAAQ;QAC3B,MAAO,OAAO,MAAM,GAAG,UAAW;YAC9B,SAAS,MAAM;QACnB;QACA,OAAO,OAAO;IAClB;IACA,qBAAqB,SAAS,KAAK;QAC/B,MAAM,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC;QACrC,OAAO,KAAK,CAAC,EAAE,CAAC,OAAO,CAAC,yBAAyB,CAAA,GAAA,sJAAA,CAAA,UAAM,AAAD,IAAI,kBAAkB,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,CAAA,GAAA,sJAAA,CAAA,UAAM,AAAD,IAAI,gBAAgB,GAAG,KAAK,CAAC,EAAE,GAAG,EAAE;IACzI;IACA,mBAAmB,SAAS,KAAK,EAAE,MAAM,EAAE,YAAY;QACnD,IAAI,kBAAkB,QAAQ;YAC1B,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO;QAChD;QACA,IAAI,cAAc,UAAU,SAAS,aAAa,SAAS,EAAE;YACzD,aAAa,SAAS,GAAG,aAAa,SAAS,IAAI;QACvD;QACA,IAAI,cAAc,QAAQ;YACtB,SAAS;QACb;QACA,IAAI,KAAK,MAAM,aAAa,SAAS,EAAE;YACnC,IAAI,cAAc,QAAQ;gBACtB,QAAQ,IAAI,CAAC,UAAU,CAAC,OAAO,aAAa,SAAS;YACzD,OAAO;gBACH,QAAQ,SAAS,aAAa,SAAS,GAAG,MAAM,WAAW,KAAK,CAAA,GAAA,+KAAA,CAAA,UAAO,AAAD,EAAE,OAAO,aAAa,SAAS;YACzG;QACJ;QACA,IAAI,cAAc,QAAQ;YACtB,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACrC,OAAO;YACH,QAAQ,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAA,GAAA,sJAAA,CAAA,UAAM,AAAD,IAAI,gBAAgB;QACnE;QACA,IAAI,cAAc,QAAQ;YACtB,SAAS;QACb;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,MAAM;QAC7B,IAAI,CAAC,QAAQ;YACT,OAAO,CAAC;QACZ;QACA,IAAI,eAAe,OAAO,QAAQ;YAC9B,OAAO;QACX;QACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;YACxB,SAAS;gBACL,MAAM;YACV;QACJ;QACA,OAAO;IACX;IACA,gBAAgB;QACZ,OAAO;YACH,kBAAkB,IAAI,CAAC,mBAAmB;YAC1C,oBAAoB,IAAI,CAAC,qBAAqB;QAClD;IACJ;IACA,uBAAuB;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,aAAa,CAAC,EAAE;IAC5C;IACA,qBAAqB;QACjB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;YACpB,MAAM;YACN,WAAW;QACf,EAAE,CAAC,EAAE;IACT;IACA,eAAe,SAAS,KAAK,EAAE,UAAU;QACrC,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,IAAI;QAC/B,IAAI,aAAa,OAAO,SAAS,QAAQ,MAAM,CAAC,EAAE,EAAE;YAChD,OAAO;QACX;QACA,MAAM,iBAAiB,aAAa,MAAM,CAAC,EAAE,GAAG;QAChD,MAAM,eAAe,aAAa,MAAM,MAAM,CAAC,EAAE;QACjD,MAAM,gBAAgB,aAAa,MAAM,CAAC,EAAE,GAAG;QAC/C,MAAM,SAAS,IAAI,OAAO,MAAM,iBAAiB,MAAM,gBAAgB,KAAK;QAC5E,OAAO,MAAM,OAAO,CAAC,QAAS,CAAA,OAAQ,OAAO,YAAY,CAAC,KAAK,UAAU,CAAC,KAAK,CAAC,aAAa,UAAU,CAAC,KAAK,eAAe,UAAU,CAAC,EAAE;IAC7I;IACA,yBAAyB,SAAS,MAAM;QACpC,MAAM,aAAa,IAAI,CAAC,cAAc;QACtC,MAAM,gBAAgB,IAAI,OAAO,SAAS,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,WAAW,gBAAgB,GAAG,WAAW,kBAAkB,IAAI,MAAM;QAC5H,IAAI,iBAAiB,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,QAAQ,OAAO,CAAC,eAAe;QACpE;YAAC;YAAM;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;YAAK;SAAI,CAAC,OAAO,CAAE,CAAA;YACxE,iBAAiB,eAAe,OAAO,CAAC,IAAI,OAAO,AAAC,KAAS,OAAL,OAAQ,MAAM,AAAC,KAAS,OAAL;QAC/E;QACA,iBAAiB,eAAe,OAAO,CAAC,MAAM;QAC9C,iBAAiB,eAAe,OAAO,CAAC,MAAM;QAC9C,OAAO,IAAI,OAAO,gBAAgB;IACtC;IACA,SAAS,SAAS,IAAI,EAAE,MAAM;QAC1B,IAAI,CAAC,QAAQ;YACT,IAAI,QAAQ,KAAK,OAAO,CAAC,YAAY,IAAI,MAAM,CAAC,IAAI;gBAChD,OAAO,CAAC;YACZ;YACA,OAAO;QACX;QACA,MAAM,iBAAiB,IAAI,CAAC,uBAAuB,CAAC;QACpD,OAAO,KAAK,KAAK,CAAC,kBAAkB,CAAC,IAAI;IAC7C;IACA,QAAQ,SAAS,KAAK,EAAE,MAAM;QAC1B,IAAI,aAAa,OAAO,OAAO;YAC3B,OAAO;QACX;QACA,IAAI,aAAa,OAAO,QAAQ;YAC5B,OAAO;QACX;QACA,SAAS,UAAU,OAAO,SAAS,IAAI;QACvC,IAAI,eAAe,OAAO,QAAQ;YAC9B,OAAO,OAAO;QAClB;QACA,SAAS,IAAI,CAAC,gBAAgB,CAAC;QAC/B,IAAI,CAAC,OAAO,IAAI,EAAE;YACd,OAAO,IAAI,GAAG;QAClB;QACA,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC,OAAO,IAAI;QAC9D,IAAI,CAAC,cAAc;YACf,MAAM,kBAAkB,IAAI,CAAC,cAAc;YAC3C,gBAAgB,sBAAsB,GAAG,OAAO,sBAAsB;YACtE,MAAM,YAAY,CAAA,GAAA,wLAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI,EAAE,iBAAiB;YAC7D,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC;YAClC,OAAO;QACX;QACA,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,cAAc;IACnD;IACA,OAAO,SAAS,IAAI,EAAE,MAAM;QACxB,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,UAAU,OAAO,MAAM,EAAE;YACzB,OAAO,OAAO,MAAM,CAAC;QACzB;QACA,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;QAChC,IAAI,UAAU,aAAa,OAAO,QAAQ;YACtC,sJAAA,CAAA,UAAM,CAAC,GAAG,CAAC;QACf;QACA,MAAM,mBAAmB,IAAI,CAAC,mBAAmB;QACjD,MAAM,SAAS,IAAI,OAAO,UAAU,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,oBAAoB,KAAK;QAC1E,MAAM,cAAc,KAAK,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,kBAAkB,KAAK,OAAO,CAAC,QAAQ;QAC5F,IAAI,QAAQ,eAAe,OAAO,aAAa;YAC3C,OAAO;QACX;QACA,IAAI,IAAI,CAAC,sBAAsB,CAAC,eAAe,IAAI;YAC/C,OAAO;QACX;QACA,IAAI,SAAS,CAAC,cAAc,IAAI,CAAC,OAAO,CAAC,MAAM;QAC/C,SAAS,IAAI,CAAC,gBAAgB,CAAC;QAC/B,MAAM,eAAe,IAAI,CAAC,wBAAwB,CAAC,OAAO,IAAI;QAC9D,IAAI,QAAQ,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,KAAK;QAC1F,IAAI,OAAO;YACP,IAAI,WAAW,OAAO;gBAClB,MAAM,QAAQ,KAAK,KAAK,CAAC;gBACzB,IAAI,OAAO;oBACP,QAAQ,OAAO,IAAI,CAAC,4BAA4B,IAAI,CAAE,CAAA,QAAS,0BAA0B,CAAC,MAAM,KAAK,KAAK,CAAC,EAAE;gBACjH;YACJ;YACA,UAAU,KAAK,GAAG,CAAC,IAAI,IAAI;QAC/B;QACA,IAAI,cAAc,CAAC,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,UAAU,GAAG;YACrG,UAAU;QACd;QACA,OAAO;IACX;IACA,wBAAwB,SAAS,IAAI;QACjC,MAAM,CAAC,SAAS,WAAW,GAAG,KAAK,KAAK,CAAC;QACzC,MAAM,8BAA8B,CAAA;YAChC,IAAI,QAAQ,CAAC;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACpC,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE;oBACnB,QAAQ;oBACR;gBACJ;YACJ;YACA,OAAO,QAAQ,CAAC,IAAI,OAAO,MAAM,GAAG,QAAQ;QAChD;QACA,IAAI,SAAS;QACb,IAAI,SAAS;YACT,UAAU,4BAA4B,QAAQ,KAAK,CAAC;QACxD;QACA,IAAI,YAAY;YACZ,UAAU,4BAA4B,WAAW,KAAK,CAAC,IAAI,OAAO;QACtE;QACA,OAAO;IACX;AACJ;AACA,mBAAmB,MAAM,CAAC,kLAAA,CAAA,UAAoB;AAC9C,IAAI,SAAS;IACT,mBAAmB,MAAM,CAAC,wLAAA,CAAA,UAAsB;AACpD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5935, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/ldml/date.format.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/ldml/date.format.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport numberLocalization from \"../number\";\r\nconst ARABIC_COMMA = \"\\u060c\";\r\nconst FORMAT_SEPARATORS = \" .,:;/\\\\<>()-[]\\u060c\";\r\nconst AM_PM_PATTERN = \". m.\";\r\nconst checkDigit = function(char) {\r\n    const code = char && numberLocalization.convertDigits(char, false).charCodeAt(0);\r\n    const zeroCode = numberLocalization.convertDigits(\"0\", false).charCodeAt(0);\r\n    return zeroCode <= code && code < zeroCode + 10\r\n};\r\nconst checkPatternContinue = function(text, patterns, index, isDigit) {\r\n    const char = text[index];\r\n    const nextChar = text[index + 1];\r\n    if (!isDigit) {\r\n        if (\".\" === char || \" \" === char && \". m.\" === text.slice(index - 1, index + 3)) {\r\n            return true\r\n        }\r\n        if (\"-\" === char && !checkDigit(nextChar)) {\r\n            return true\r\n        }\r\n    }\r\n    const isDigitChanged = isDigit && patterns.some((pattern => text[index] !== pattern[index]));\r\n    return FORMAT_SEPARATORS.indexOf(char) < 0 && isDigit === checkDigit(char) && (!isDigit || isDigitChanged)\r\n};\r\nconst getPatternStartIndex = function(defaultPattern, index) {\r\n    if (!checkDigit(defaultPattern[index])) {\r\n        while (index > 0 && !checkDigit(defaultPattern[index - 1]) && (\".\" === defaultPattern[index - 1] || FORMAT_SEPARATORS.indexOf(defaultPattern[index - 1]) < 0)) {\r\n            index--\r\n        }\r\n    }\r\n    return index\r\n};\r\nconst getDifference = function(defaultPattern, patterns, processedIndexes, isDigit) {\r\n    let i = 0;\r\n    const result = [];\r\n    const patternsFilter = function(pattern) {\r\n        return defaultPattern[i] !== pattern[i] && (void 0 === isDigit || checkDigit(defaultPattern[i]) === isDigit)\r\n    };\r\n    if (!Array.isArray(patterns)) {\r\n        patterns = [patterns]\r\n    }\r\n    for (i = 0; i < defaultPattern.length; i++) {\r\n        if (processedIndexes.indexOf(i) < 0 && patterns.filter(patternsFilter).length) {\r\n            i = getPatternStartIndex(defaultPattern, i);\r\n            do {\r\n                isDigit = checkDigit(defaultPattern[i]);\r\n                if (!result.length && !isDigit && checkDigit(patterns[0][i])) {\r\n                    break\r\n                }\r\n                result.push(i);\r\n                processedIndexes.unshift(i);\r\n                i++\r\n            } while (defaultPattern[i] && checkPatternContinue(defaultPattern, patterns, i, isDigit));\r\n            break\r\n        }\r\n    }\r\n    if (1 === result.length && (\"0\" === defaultPattern[processedIndexes[0] - 1] || \"\\u0660\" === defaultPattern[processedIndexes[0] - 1])) {\r\n        processedIndexes.unshift(processedIndexes[0] - 1)\r\n    }\r\n    return result\r\n};\r\nconst replaceCharsCore = function(pattern, indexes, char, patternPositions) {\r\n    const baseCharIndex = indexes[0];\r\n    const patternIndex = baseCharIndex < patternPositions.length ? patternPositions[baseCharIndex] : baseCharIndex;\r\n    indexes.forEach((function(_, index) {\r\n        pattern = pattern.substr(0, patternIndex + index) + (char.length > 1 ? char[index] : char) + pattern.substr(patternIndex + index + 1)\r\n    }));\r\n    if (1 === indexes.length) {\r\n        pattern = pattern.replace(\"0\" + char, char + char);\r\n        pattern = pattern.replace(\"\\u0660\" + char, char + char)\r\n    }\r\n    return pattern\r\n};\r\nconst replaceChars = function(pattern, indexes, char, patternPositions) {\r\n    let i;\r\n    let index;\r\n    let patternIndex;\r\n    if (!checkDigit(pattern[indexes[0]] || \"0\")) {\r\n        const letterCount = Math.max(indexes.length <= 3 ? 3 : 4, char.length);\r\n        while (indexes.length > letterCount) {\r\n            index = indexes.pop();\r\n            patternIndex = patternPositions[index];\r\n            patternPositions[index] = -1;\r\n            for (i = index + 1; i < patternPositions.length; i++) {\r\n                patternPositions[i]--\r\n            }\r\n            pattern = pattern.substr(0, patternIndex) + pattern.substr(patternIndex + 1)\r\n        }\r\n        index = indexes[indexes.length - 1] + 1, patternIndex = index < patternPositions.length ? patternPositions[index] : index;\r\n        while (indexes.length < letterCount) {\r\n            indexes.push(indexes[indexes.length - 1] + 1);\r\n            for (i = index; i < patternPositions.length; i++) {\r\n                patternPositions[i]++\r\n            }\r\n            pattern = pattern.substr(0, patternIndex) + \" \" + pattern.substr(patternIndex)\r\n        }\r\n    }\r\n    pattern = replaceCharsCore(pattern, indexes, char, patternPositions);\r\n    return pattern\r\n};\r\nconst formatValue = function(value, formatter) {\r\n    if (Array.isArray(value)) {\r\n        return value.map((function(value) {\r\n            return (formatter(value) || \"\").toString()\r\n        }))\r\n    }\r\n    return (formatter(value) || \"\").toString()\r\n};\r\nconst ESCAPE_CHARS_REGEXP = /[a-zA-Z]/g;\r\nconst escapeChars = function(pattern, defaultPattern, processedIndexes, patternPositions) {\r\n    const escapeIndexes = defaultPattern.split(\"\").map((function(char, index) {\r\n        if (processedIndexes.indexOf(index) < 0 && (char.match(ESCAPE_CHARS_REGEXP) || \"'\" === char)) {\r\n            return patternPositions[index]\r\n        }\r\n        return -1\r\n    }));\r\n    pattern = pattern.split(\"\").map((function(char, index) {\r\n        let result = char;\r\n        const isCurrentCharEscaped = escapeIndexes.indexOf(index) >= 0;\r\n        const isPrevCharEscaped = index > 0 && escapeIndexes.indexOf(index - 1) >= 0;\r\n        const isNextCharEscaped = escapeIndexes.indexOf(index + 1) >= 0;\r\n        if (isCurrentCharEscaped) {\r\n            if (!isPrevCharEscaped) {\r\n                result = \"'\" + result\r\n            }\r\n            if (!isNextCharEscaped) {\r\n                result += \"'\"\r\n            }\r\n        }\r\n        return result\r\n    })).join(\"\");\r\n    return pattern\r\n};\r\nexport const getFormat = function(formatter) {\r\n    const processedIndexes = [];\r\n    const defaultPattern = formatValue(new Date(2009, 8, 8, 6, 5, 4), formatter);\r\n    const patternPositions = defaultPattern.split(\"\").map((function(_, index) {\r\n        return index\r\n    }));\r\n    let result = defaultPattern;\r\n    const replacedPatterns = {};\r\n    const datePatterns = [{\r\n        date: new Date(2009, 8, 8, 6, 5, 4, 111),\r\n        pattern: \"S\"\r\n    }, {\r\n        date: new Date(2009, 8, 8, 6, 5, 2),\r\n        pattern: \"s\"\r\n    }, {\r\n        date: new Date(2009, 8, 8, 6, 2, 4),\r\n        pattern: \"m\"\r\n    }, {\r\n        date: new Date(2009, 8, 8, 18, 5, 4),\r\n        pattern: \"H\",\r\n        isDigit: true\r\n    }, {\r\n        date: new Date(2009, 8, 8, 2, 5, 4),\r\n        pattern: \"h\",\r\n        isDigit: true\r\n    }, {\r\n        date: new Date(2009, 8, 8, 18, 5, 4),\r\n        pattern: \"a\",\r\n        isDigit: false\r\n    }, {\r\n        date: new Date(2009, 8, 1, 6, 5, 4),\r\n        pattern: \"d\"\r\n    }, {\r\n        date: [new Date(2009, 8, 2, 6, 5, 4), new Date(2009, 8, 3, 6, 5, 4), new Date(2009, 8, 4, 6, 5, 4)],\r\n        pattern: \"E\"\r\n    }, {\r\n        date: new Date(2009, 9, 6, 6, 5, 4),\r\n        pattern: \"M\"\r\n    }, {\r\n        date: new Date(1998, 8, 8, 6, 5, 4),\r\n        pattern: \"y\"\r\n    }];\r\n    if (!result) {\r\n        return\r\n    }\r\n    datePatterns.forEach((function(test) {\r\n        const diff = getDifference(defaultPattern, formatValue(test.date, formatter), processedIndexes, test.isDigit);\r\n        const pattern = \"M\" === test.pattern && !replacedPatterns.d ? \"L\" : test.pattern;\r\n        result = replaceChars(result, diff, pattern, patternPositions);\r\n        replacedPatterns[pattern] = diff.length\r\n    }));\r\n    result = escapeChars(result, defaultPattern, processedIndexes, patternPositions);\r\n    if (processedIndexes.length) {\r\n        return result\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AACA,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,gBAAgB;AACtB,MAAM,aAAa,SAAS,IAAI;IAC5B,MAAM,OAAO,QAAQ,gLAAA,CAAA,UAAkB,CAAC,aAAa,CAAC,MAAM,OAAO,UAAU,CAAC;IAC9E,MAAM,WAAW,gLAAA,CAAA,UAAkB,CAAC,aAAa,CAAC,KAAK,OAAO,UAAU,CAAC;IACzE,OAAO,YAAY,QAAQ,OAAO,WAAW;AACjD;AACA,MAAM,uBAAuB,SAAS,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,OAAO;IAChE,MAAM,OAAO,IAAI,CAAC,MAAM;IACxB,MAAM,WAAW,IAAI,CAAC,QAAQ,EAAE;IAChC,IAAI,CAAC,SAAS;QACV,IAAI,QAAQ,QAAQ,QAAQ,QAAQ,WAAW,KAAK,KAAK,CAAC,QAAQ,GAAG,QAAQ,IAAI;YAC7E,OAAO;QACX;QACA,IAAI,QAAQ,QAAQ,CAAC,WAAW,WAAW;YACvC,OAAO;QACX;IACJ;IACA,MAAM,iBAAiB,WAAW,SAAS,IAAI,CAAE,CAAA,UAAW,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,MAAM;IAC1F,OAAO,kBAAkB,OAAO,CAAC,QAAQ,KAAK,YAAY,WAAW,SAAS,CAAC,CAAC,WAAW,cAAc;AAC7G;AACA,MAAM,uBAAuB,SAAS,cAAc,EAAE,KAAK;IACvD,IAAI,CAAC,WAAW,cAAc,CAAC,MAAM,GAAG;QACpC,MAAO,QAAQ,KAAK,CAAC,WAAW,cAAc,CAAC,QAAQ,EAAE,KAAK,CAAC,QAAQ,cAAc,CAAC,QAAQ,EAAE,IAAI,kBAAkB,OAAO,CAAC,cAAc,CAAC,QAAQ,EAAE,IAAI,CAAC,EAAG;YAC3J;QACJ;IACJ;IACA,OAAO;AACX;AACA,MAAM,gBAAgB,SAAS,cAAc,EAAE,QAAQ,EAAE,gBAAgB,EAAE,OAAO;IAC9E,IAAI,IAAI;IACR,MAAM,SAAS,EAAE;IACjB,MAAM,iBAAiB,SAAS,OAAO;QACnC,OAAO,cAAc,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,IAAI,CAAC,KAAK,MAAM,WAAW,WAAW,cAAc,CAAC,EAAE,MAAM,OAAO;IAC/G;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW;QAC1B,WAAW;YAAC;SAAS;IACzB;IACA,IAAK,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;QACxC,IAAI,iBAAiB,OAAO,CAAC,KAAK,KAAK,SAAS,MAAM,CAAC,gBAAgB,MAAM,EAAE;YAC3E,IAAI,qBAAqB,gBAAgB;YACzC,GAAG;gBACC,UAAU,WAAW,cAAc,CAAC,EAAE;gBACtC,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,WAAW,WAAW,QAAQ,CAAC,EAAE,CAAC,EAAE,GAAG;oBAC1D;gBACJ;gBACA,OAAO,IAAI,CAAC;gBACZ,iBAAiB,OAAO,CAAC;gBACzB;YACJ,QAAS,cAAc,CAAC,EAAE,IAAI,qBAAqB,gBAAgB,UAAU,GAAG,SAAU;YAC1F;QACJ;IACJ;IACA,IAAI,MAAM,OAAO,MAAM,IAAI,CAAC,QAAQ,cAAc,CAAC,gBAAgB,CAAC,EAAE,GAAG,EAAE,IAAI,aAAa,cAAc,CAAC,gBAAgB,CAAC,EAAE,GAAG,EAAE,GAAG;QAClI,iBAAiB,OAAO,CAAC,gBAAgB,CAAC,EAAE,GAAG;IACnD;IACA,OAAO;AACX;AACA,MAAM,mBAAmB,SAAS,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB;IACtE,MAAM,gBAAgB,OAAO,CAAC,EAAE;IAChC,MAAM,eAAe,gBAAgB,iBAAiB,MAAM,GAAG,gBAAgB,CAAC,cAAc,GAAG;IACjG,QAAQ,OAAO,CAAE,SAAS,CAAC,EAAE,KAAK;QAC9B,UAAU,QAAQ,MAAM,CAAC,GAAG,eAAe,SAAS,CAAC,KAAK,MAAM,GAAG,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,QAAQ,MAAM,CAAC,eAAe,QAAQ;IACvI;IACA,IAAI,MAAM,QAAQ,MAAM,EAAE;QACtB,UAAU,QAAQ,OAAO,CAAC,MAAM,MAAM,OAAO;QAC7C,UAAU,QAAQ,OAAO,CAAC,WAAW,MAAM,OAAO;IACtD;IACA,OAAO;AACX;AACA,MAAM,eAAe,SAAS,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,gBAAgB;IAClE,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,WAAW,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,IAAI,MAAM;QACzC,MAAM,cAAc,KAAK,GAAG,CAAC,QAAQ,MAAM,IAAI,IAAI,IAAI,GAAG,KAAK,MAAM;QACrE,MAAO,QAAQ,MAAM,GAAG,YAAa;YACjC,QAAQ,QAAQ,GAAG;YACnB,eAAe,gBAAgB,CAAC,MAAM;YACtC,gBAAgB,CAAC,MAAM,GAAG,CAAC;YAC3B,IAAK,IAAI,QAAQ,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAClD,gBAAgB,CAAC,EAAE;YACvB;YACA,UAAU,QAAQ,MAAM,CAAC,GAAG,gBAAgB,QAAQ,MAAM,CAAC,eAAe;QAC9E;QACA,QAAQ,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,GAAG,GAAG,eAAe,QAAQ,iBAAiB,MAAM,GAAG,gBAAgB,CAAC,MAAM,GAAG;QACpH,MAAO,QAAQ,MAAM,GAAG,YAAa;YACjC,QAAQ,IAAI,CAAC,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE,GAAG;YAC3C,IAAK,IAAI,OAAO,IAAI,iBAAiB,MAAM,EAAE,IAAK;gBAC9C,gBAAgB,CAAC,EAAE;YACvB;YACA,UAAU,QAAQ,MAAM,CAAC,GAAG,gBAAgB,MAAM,QAAQ,MAAM,CAAC;QACrE;IACJ;IACA,UAAU,iBAAiB,SAAS,SAAS,MAAM;IACnD,OAAO;AACX;AACA,MAAM,cAAc,SAAS,KAAK,EAAE,SAAS;IACzC,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,OAAO,MAAM,GAAG,CAAE,SAAS,KAAK;YAC5B,OAAO,CAAC,UAAU,UAAU,EAAE,EAAE,QAAQ;QAC5C;IACJ;IACA,OAAO,CAAC,UAAU,UAAU,EAAE,EAAE,QAAQ;AAC5C;AACA,MAAM,sBAAsB;AAC5B,MAAM,cAAc,SAAS,OAAO,EAAE,cAAc,EAAE,gBAAgB,EAAE,gBAAgB;IACpF,MAAM,gBAAgB,eAAe,KAAK,CAAC,IAAI,GAAG,CAAE,SAAS,IAAI,EAAE,KAAK;QACpE,IAAI,iBAAiB,OAAO,CAAC,SAAS,KAAK,CAAC,KAAK,KAAK,CAAC,wBAAwB,QAAQ,IAAI,GAAG;YAC1F,OAAO,gBAAgB,CAAC,MAAM;QAClC;QACA,OAAO,CAAC;IACZ;IACA,UAAU,QAAQ,KAAK,CAAC,IAAI,GAAG,CAAE,SAAS,IAAI,EAAE,KAAK;QACjD,IAAI,SAAS;QACb,MAAM,uBAAuB,cAAc,OAAO,CAAC,UAAU;QAC7D,MAAM,oBAAoB,QAAQ,KAAK,cAAc,OAAO,CAAC,QAAQ,MAAM;QAC3E,MAAM,oBAAoB,cAAc,OAAO,CAAC,QAAQ,MAAM;QAC9D,IAAI,sBAAsB;YACtB,IAAI,CAAC,mBAAmB;gBACpB,SAAS,MAAM;YACnB;YACA,IAAI,CAAC,mBAAmB;gBACpB,UAAU;YACd;QACJ;QACA,OAAO;IACX,GAAI,IAAI,CAAC;IACT,OAAO;AACX;AACO,MAAM,YAAY,SAAS,SAAS;IACvC,MAAM,mBAAmB,EAAE;IAC3B,MAAM,iBAAiB,YAAY,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG,IAAI;IAClE,MAAM,mBAAmB,eAAe,KAAK,CAAC,IAAI,GAAG,CAAE,SAAS,CAAC,EAAE,KAAK;QACpE,OAAO;IACX;IACA,IAAI,SAAS;IACb,MAAM,mBAAmB,CAAC;IAC1B,MAAM,eAAe;QAAC;YAClB,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG,GAAG;YACpC,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;YACjC,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;YACjC,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,GAAG;YAClC,SAAS;YACT,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;YACjC,SAAS;YACT,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,GAAG;YAClC,SAAS;YACT,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;YACjC,SAAS;QACb;QAAG;YACC,MAAM;gBAAC,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;gBAAI,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;gBAAI,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;aAAG;YACnG,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;YACjC,SAAS;QACb;QAAG;YACC,MAAM,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;YACjC,SAAS;QACb;KAAE;IACF,IAAI,CAAC,QAAQ;QACT;IACJ;IACA,aAAa,OAAO,CAAE,SAAS,IAAI;QAC/B,MAAM,OAAO,cAAc,gBAAgB,YAAY,KAAK,IAAI,EAAE,YAAY,kBAAkB,KAAK,OAAO;QAC5G,MAAM,UAAU,QAAQ,KAAK,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,MAAM,KAAK,OAAO;QAChF,SAAS,aAAa,QAAQ,MAAM,SAAS;QAC7C,gBAAgB,CAAC,QAAQ,GAAG,KAAK,MAAM;IAC3C;IACA,SAAS,YAAY,QAAQ,gBAAgB,kBAAkB;IAC/D,IAAI,iBAAiB,MAAM,EAAE;QACzB,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6155, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/ldml/date.parser.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/ldml/date.parser.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    escapeRegExp\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    logger\r\n} from \"../../../../core/utils/console\";\r\nconst FORMAT_TYPES = {\r\n    3: \"abbreviated\",\r\n    4: \"wide\",\r\n    5: \"narrow\"\r\n};\r\nconst monthRegExpGenerator = function(count, dateParts) {\r\n    if (count > 2) {\r\n        return Object.keys(FORMAT_TYPES).map((function(count) {\r\n            return [\"format\", \"standalone\"].map((function(type) {\r\n                return dateParts.getMonthNames(FORMAT_TYPES[count], type).join(\"|\")\r\n            })).join(\"|\")\r\n        })).join(\"|\")\r\n    }\r\n    return 2 === count ? \"1[012]|0?[1-9]\" : \"0??[1-9]|1[012]\"\r\n};\r\nconst PATTERN_REGEXPS = {\r\n    \":\": function(count, dateParts) {\r\n        const countSuffix = count > 1 ? `{${count}}` : \"\";\r\n        let timeSeparator = escapeRegExp(dateParts.getTimeSeparator());\r\n        \":\" !== timeSeparator && (timeSeparator = `${timeSeparator}|:`);\r\n        return `${timeSeparator}${countSuffix}`\r\n    },\r\n    y: function(count) {\r\n        return 2 === count ? `[0-9]{${count}}` : \"[0-9]+?\"\r\n    },\r\n    M: monthRegExpGenerator,\r\n    L: monthRegExpGenerator,\r\n    Q: function(count, dateParts) {\r\n        if (count > 2) {\r\n            return dateParts.getQuarterNames(FORMAT_TYPES[count], \"format\").join(\"|\")\r\n        }\r\n        return \"0?[1-4]\"\r\n    },\r\n    E: function(count, dateParts) {\r\n        return \"\\\\D*\"\r\n    },\r\n    a: function(count, dateParts) {\r\n        return dateParts.getPeriodNames(FORMAT_TYPES[count < 3 ? 3 : count], \"format\").join(\"|\")\r\n    },\r\n    d: function(count) {\r\n        return 2 === count ? \"3[01]|[12][0-9]|0?[1-9]\" : \"0??[1-9]|[12][0-9]|3[01]\"\r\n    },\r\n    H: function(count) {\r\n        return 2 === count ? \"2[0-3]|1[0-9]|0?[0-9]\" : \"0??[0-9]|1[0-9]|2[0-3]\"\r\n    },\r\n    h: function(count) {\r\n        return 2 === count ? \"1[012]|0?[1-9]\" : \"0??[1-9]|1[012]\"\r\n    },\r\n    m: function(count) {\r\n        return 2 === count ? \"[1-5][0-9]|0?[0-9]\" : \"0??[0-9]|[1-5][0-9]\"\r\n    },\r\n    s: function(count) {\r\n        return 2 === count ? \"[1-5][0-9]|0?[0-9]\" : \"0??[0-9]|[1-5][0-9]\"\r\n    },\r\n    S: function(count) {\r\n        return `[0-9]{1,${count}}`\r\n    },\r\n    w: function(count) {\r\n        return 2 === count ? \"[1-5][0-9]|0?[0-9]\" : \"0??[0-9]|[1-5][0-9]\"\r\n    },\r\n    x: function(count) {\r\n        return 3 === count ? \"[+-](?:2[0-3]|[01][0-9]):(?:[0-5][0-9])|Z\" : \"[+-](?:2[0-3]|[01][0-9])(?:[0-5][0-9])|Z\"\r\n    }\r\n};\r\nconst parseNumber = Number;\r\nconst caseInsensitiveIndexOf = function(array, value) {\r\n    return array.map((item => item.toLowerCase())).indexOf(value.toLowerCase())\r\n};\r\nconst monthPatternParser = function(text, count, dateParts) {\r\n    if (count > 2) {\r\n        return [\"format\", \"standalone\"].map((function(type) {\r\n            return Object.keys(FORMAT_TYPES).map((function(count) {\r\n                const monthNames = dateParts.getMonthNames(FORMAT_TYPES[count], type);\r\n                return caseInsensitiveIndexOf(monthNames, text)\r\n            }))\r\n        })).reduce((function(a, b) {\r\n            return a.concat(b)\r\n        })).filter((function(index) {\r\n            return index >= 0\r\n        }))[0]\r\n    }\r\n    return parseNumber(text) - 1\r\n};\r\nconst PATTERN_PARSERS = {\r\n    y: function(text, count) {\r\n        const year = parseNumber(text);\r\n        if (2 === count) {\r\n            return year < 30 ? 2e3 + year : 1900 + year\r\n        }\r\n        return year\r\n    },\r\n    M: monthPatternParser,\r\n    L: monthPatternParser,\r\n    Q: function(text, count, dateParts) {\r\n        if (count > 2) {\r\n            return dateParts.getQuarterNames(FORMAT_TYPES[count], \"format\").indexOf(text)\r\n        }\r\n        return parseNumber(text) - 1\r\n    },\r\n    E: function(text, count, dateParts) {\r\n        const dayNames = dateParts.getDayNames(FORMAT_TYPES[count < 3 ? 3 : count], \"format\");\r\n        return caseInsensitiveIndexOf(dayNames, text)\r\n    },\r\n    a: function(text, count, dateParts) {\r\n        const periodNames = dateParts.getPeriodNames(FORMAT_TYPES[count < 3 ? 3 : count], \"format\");\r\n        return caseInsensitiveIndexOf(periodNames, text)\r\n    },\r\n    d: parseNumber,\r\n    H: parseNumber,\r\n    h: parseNumber,\r\n    m: parseNumber,\r\n    s: parseNumber,\r\n    S: function(text, count) {\r\n        count = Math.max(count, 3);\r\n        text = text.slice(0, 3);\r\n        while (count < 3) {\r\n            text += \"0\";\r\n            count++\r\n        }\r\n        return parseNumber(text)\r\n    }\r\n};\r\nconst ORDERED_PATTERNS = [\"y\", \"M\", \"d\", \"h\", \"m\", \"s\", \"S\"];\r\nconst PATTERN_SETTERS = {\r\n    y: \"setFullYear\",\r\n    M: \"setMonth\",\r\n    L: \"setMonth\",\r\n    a: function(date, value, datePartValues) {\r\n        let hours = date.getHours();\r\n        const hourPartValue = datePartValues.h;\r\n        if (void 0 !== hourPartValue && hourPartValue !== hours) {\r\n            hours--\r\n        }\r\n        if (!value && 12 === hours) {\r\n            hours = 0\r\n        } else if (value && 12 !== hours) {\r\n            hours += 12\r\n        }\r\n        date.setHours(hours)\r\n    },\r\n    d: \"setDate\",\r\n    H: \"setHours\",\r\n    h: \"setHours\",\r\n    m: \"setMinutes\",\r\n    s: \"setSeconds\",\r\n    S: \"setMilliseconds\"\r\n};\r\nconst getSameCharCount = function(text, index) {\r\n    const char = text[index];\r\n    if (!char) {\r\n        return 0\r\n    }\r\n    let count = 0;\r\n    do {\r\n        index++;\r\n        count++\r\n    } while (text[index] === char);\r\n    return count\r\n};\r\nconst createPattern = function(char, count) {\r\n    let result = \"\";\r\n    for (let i = 0; i < count; i++) {\r\n        result += char\r\n    }\r\n    return result\r\n};\r\nexport const getRegExpInfo = function(format, dateParts) {\r\n    let regexpText = \"\";\r\n    let stubText = \"\";\r\n    let isEscaping;\r\n    const patterns = [];\r\n    const addPreviousStub = function() {\r\n        if (stubText) {\r\n            patterns.push(`'${stubText}'`);\r\n            regexpText += `${escapeRegExp(stubText)})`;\r\n            stubText = \"\"\r\n        }\r\n    };\r\n    for (let i = 0; i < format.length; i++) {\r\n        const char = format[i];\r\n        const isEscapeChar = \"'\" === char;\r\n        const regexpPart = PATTERN_REGEXPS[char];\r\n        if (isEscapeChar) {\r\n            isEscaping = !isEscaping;\r\n            if (\"'\" !== format[i - 1]) {\r\n                continue\r\n            }\r\n        }\r\n        if (regexpPart && !isEscaping) {\r\n            const count = getSameCharCount(format, i);\r\n            const pattern = createPattern(char, count);\r\n            addPreviousStub();\r\n            patterns.push(pattern);\r\n            regexpText += `(${regexpPart(count,dateParts)})`;\r\n            i += count - 1\r\n        } else {\r\n            if (!stubText) {\r\n                regexpText += \"(\"\r\n            }\r\n            stubText += char\r\n        }\r\n    }\r\n    addPreviousStub();\r\n    if (!isPossibleForParsingFormat(patterns)) {\r\n        logger.warn(`The following format may be parsed incorrectly: ${format}.`)\r\n    }\r\n    return {\r\n        patterns: patterns,\r\n        regexp: new RegExp(`^${regexpText}$`, \"i\")\r\n    }\r\n};\r\nconst digitFieldSymbols = [\"d\", \"H\", \"h\", \"m\", \"s\", \"w\", \"M\", \"L\", \"Q\"];\r\nexport const isPossibleForParsingFormat = function(patterns) {\r\n    const isDigitPattern = pattern => {\r\n        if (!pattern) {\r\n            return false\r\n        }\r\n        const char = pattern[0];\r\n        return [\"y\", \"S\"].includes(char) || digitFieldSymbols.includes(char) && pattern.length < 3\r\n    };\r\n    let possibleForParsing = true;\r\n    let ambiguousDigitPatternsCount = 0;\r\n    return patterns.every(((pattern, index, patterns) => {\r\n        if (isDigitPattern(pattern)) {\r\n            if ((pattern => \"S\" !== pattern[0] && 2 !== pattern.length)(pattern)) {\r\n                possibleForParsing = ++ambiguousDigitPatternsCount < 2\r\n            }\r\n            if (!isDigitPattern(patterns[index + 1])) {\r\n                ambiguousDigitPatternsCount = 0\r\n            }\r\n        }\r\n        return possibleForParsing\r\n    }))\r\n};\r\nexport const getPatternSetters = function() {\r\n    return PATTERN_SETTERS\r\n};\r\nconst setPatternPart = function(date, pattern, text, dateParts, datePartValues) {\r\n    const patternChar = pattern[0];\r\n    const partSetter = PATTERN_SETTERS[patternChar];\r\n    const partParser = PATTERN_PARSERS[patternChar];\r\n    if (partSetter && partParser) {\r\n        const value = partParser(text, pattern.length, dateParts);\r\n        datePartValues[pattern] = value;\r\n        if (date[partSetter]) {\r\n            date[partSetter](value)\r\n        } else {\r\n            partSetter(date, value, datePartValues)\r\n        }\r\n    }\r\n};\r\nconst setPatternPartFromNow = function(date, pattern, now) {\r\n    const setterName = PATTERN_SETTERS[pattern];\r\n    const getterName = \"g\" + setterName.substr(1);\r\n    const value = now[getterName]();\r\n    date[setterName](value)\r\n};\r\nconst getShortPatterns = function(fullPatterns) {\r\n    return fullPatterns.map((function(pattern) {\r\n        if (\"'\" === pattern[0]) {\r\n            return \"\"\r\n        } else {\r\n            return \"H\" === pattern[0] ? \"h\" : pattern[0]\r\n        }\r\n    }))\r\n};\r\nconst getMaxOrderedPatternIndex = function(patterns) {\r\n    const indexes = patterns.map((function(pattern) {\r\n        return ORDERED_PATTERNS.indexOf(pattern)\r\n    }));\r\n    return Math.max.apply(Math, indexes)\r\n};\r\nconst getOrderedFormatPatterns = function(formatPatterns) {\r\n    const otherPatterns = formatPatterns.filter((function(pattern) {\r\n        return ORDERED_PATTERNS.indexOf(pattern) < 0\r\n    }));\r\n    return ORDERED_PATTERNS.concat(otherPatterns)\r\n};\r\nexport const getParser = function(format, dateParts) {\r\n    const regExpInfo = getRegExpInfo(format, dateParts);\r\n    return function(text) {\r\n        const regExpResult = regExpInfo.regexp.exec(text);\r\n        if (regExpResult) {\r\n            const now = new Date;\r\n            const date = new Date(now.getFullYear(), 0, 1);\r\n            const formatPatterns = getShortPatterns(regExpInfo.patterns);\r\n            const maxPatternIndex = getMaxOrderedPatternIndex(formatPatterns);\r\n            const orderedFormatPatterns = getOrderedFormatPatterns(formatPatterns);\r\n            const datePartValues = {};\r\n            orderedFormatPatterns.forEach((function(pattern, index) {\r\n                if (!pattern || index < ORDERED_PATTERNS.length && index > maxPatternIndex) {\r\n                    return\r\n                }\r\n                const patternIndex = formatPatterns.indexOf(pattern);\r\n                if (patternIndex >= 0) {\r\n                    const regExpPattern = regExpInfo.patterns[patternIndex];\r\n                    const regExpText = regExpResult[patternIndex + 1];\r\n                    setPatternPart(date, regExpPattern, regExpText, dateParts, datePartValues)\r\n                } else {\r\n                    setPatternPartFromNow(date, pattern, now)\r\n                }\r\n            }));\r\n            return date\r\n        }\r\n        return null\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AAAA;AAGA;AAAA;;;AAGA,MAAM,eAAe;IACjB,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,uBAAuB,SAAS,KAAK,EAAE,SAAS;IAClD,IAAI,QAAQ,GAAG;QACX,OAAO,OAAO,IAAI,CAAC,cAAc,GAAG,CAAE,SAAS,KAAK;YAChD,OAAO;gBAAC;gBAAU;aAAa,CAAC,GAAG,CAAE,SAAS,IAAI;gBAC9C,OAAO,UAAU,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE,MAAM,IAAI,CAAC;YACnE,GAAI,IAAI,CAAC;QACb,GAAI,IAAI,CAAC;IACb;IACA,OAAO,MAAM,QAAQ,mBAAmB;AAC5C;AACA,MAAM,kBAAkB;IACpB,KAAK,SAAS,KAAK,EAAE,SAAS;QAC1B,MAAM,cAAc,QAAQ,IAAI,AAAC,IAAS,OAAN,OAAM,OAAK;QAC/C,IAAI,gBAAgB,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,UAAU,gBAAgB;QAC3D,QAAQ,iBAAiB,CAAC,gBAAgB,AAAC,GAAgB,OAAd,eAAc,KAAG;QAC9D,OAAO,AAAC,GAAkB,OAAhB,eAA4B,OAAZ;IAC9B;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,AAAC,SAAc,OAAN,OAAM,OAAK;IAC7C;IACA,GAAG;IACH,GAAG;IACH,GAAG,SAAS,KAAK,EAAE,SAAS;QACxB,IAAI,QAAQ,GAAG;YACX,OAAO,UAAU,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,IAAI,CAAC;QACzE;QACA,OAAO;IACX;IACA,GAAG,SAAS,KAAK,EAAE,SAAS;QACxB,OAAO;IACX;IACA,GAAG,SAAS,KAAK,EAAE,SAAS;QACxB,OAAO,UAAU,cAAc,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE,UAAU,IAAI,CAAC;IACxF;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,4BAA4B;IACrD;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,0BAA0B;IACnD;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,mBAAmB;IAC5C;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,uBAAuB;IAChD;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,uBAAuB;IAChD;IACA,GAAG,SAAS,KAAK;QACb,OAAO,AAAC,WAAgB,OAAN,OAAM;IAC5B;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,uBAAuB;IAChD;IACA,GAAG,SAAS,KAAK;QACb,OAAO,MAAM,QAAQ,8CAA8C;IACvE;AACJ;AACA,MAAM,cAAc;AACpB,MAAM,yBAAyB,SAAS,KAAK,EAAE,KAAK;IAChD,OAAO,MAAM,GAAG,CAAE,CAAA,OAAQ,KAAK,WAAW,IAAK,OAAO,CAAC,MAAM,WAAW;AAC5E;AACA,MAAM,qBAAqB,SAAS,IAAI,EAAE,KAAK,EAAE,SAAS;IACtD,IAAI,QAAQ,GAAG;QACX,OAAO;YAAC;YAAU;SAAa,CAAC,GAAG,CAAE,SAAS,IAAI;YAC9C,OAAO,OAAO,IAAI,CAAC,cAAc,GAAG,CAAE,SAAS,KAAK;gBAChD,MAAM,aAAa,UAAU,aAAa,CAAC,YAAY,CAAC,MAAM,EAAE;gBAChE,OAAO,uBAAuB,YAAY;YAC9C;QACJ,GAAI,MAAM,CAAE,SAAS,CAAC,EAAE,CAAC;YACrB,OAAO,EAAE,MAAM,CAAC;QACpB,GAAI,MAAM,CAAE,SAAS,KAAK;YACtB,OAAO,SAAS;QACpB,EAAG,CAAC,EAAE;IACV;IACA,OAAO,YAAY,QAAQ;AAC/B;AACA,MAAM,kBAAkB;IACpB,GAAG,SAAS,IAAI,EAAE,KAAK;QACnB,MAAM,OAAO,YAAY;QACzB,IAAI,MAAM,OAAO;YACb,OAAO,OAAO,KAAK,MAAM,OAAO,OAAO;QAC3C;QACA,OAAO;IACX;IACA,GAAG;IACH,GAAG;IACH,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,SAAS;QAC9B,IAAI,QAAQ,GAAG;YACX,OAAO,UAAU,eAAe,CAAC,YAAY,CAAC,MAAM,EAAE,UAAU,OAAO,CAAC;QAC5E;QACA,OAAO,YAAY,QAAQ;IAC/B;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,SAAS;QAC9B,MAAM,WAAW,UAAU,WAAW,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;QAC5E,OAAO,uBAAuB,UAAU;IAC5C;IACA,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,SAAS;QAC9B,MAAM,cAAc,UAAU,cAAc,CAAC,YAAY,CAAC,QAAQ,IAAI,IAAI,MAAM,EAAE;QAClF,OAAO,uBAAuB,aAAa;IAC/C;IACA,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG,SAAS,IAAI,EAAE,KAAK;QACnB,QAAQ,KAAK,GAAG,CAAC,OAAO;QACxB,OAAO,KAAK,KAAK,CAAC,GAAG;QACrB,MAAO,QAAQ,EAAG;YACd,QAAQ;YACR;QACJ;QACA,OAAO,YAAY;IACvB;AACJ;AACA,MAAM,mBAAmB;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAC5D,MAAM,kBAAkB;IACpB,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG,SAAS,IAAI,EAAE,KAAK,EAAE,cAAc;QACnC,IAAI,QAAQ,KAAK,QAAQ;QACzB,MAAM,gBAAgB,eAAe,CAAC;QACtC,IAAI,KAAK,MAAM,iBAAiB,kBAAkB,OAAO;YACrD;QACJ;QACA,IAAI,CAAC,SAAS,OAAO,OAAO;YACxB,QAAQ;QACZ,OAAO,IAAI,SAAS,OAAO,OAAO;YAC9B,SAAS;QACb;QACA,KAAK,QAAQ,CAAC;IAClB;IACA,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;IACH,GAAG;AACP;AACA,MAAM,mBAAmB,SAAS,IAAI,EAAE,KAAK;IACzC,MAAM,OAAO,IAAI,CAAC,MAAM;IACxB,IAAI,CAAC,MAAM;QACP,OAAO;IACX;IACA,IAAI,QAAQ;IACZ,GAAG;QACC;QACA;IACJ,QAAS,IAAI,CAAC,MAAM,KAAK,KAAM;IAC/B,OAAO;AACX;AACA,MAAM,gBAAgB,SAAS,IAAI,EAAE,KAAK;IACtC,IAAI,SAAS;IACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;QAC5B,UAAU;IACd;IACA,OAAO;AACX;AACO,MAAM,gBAAgB,SAAS,MAAM,EAAE,SAAS;IACnD,IAAI,aAAa;IACjB,IAAI,WAAW;IACf,IAAI;IACJ,MAAM,WAAW,EAAE;IACnB,MAAM,kBAAkB;QACpB,IAAI,UAAU;YACV,SAAS,IAAI,CAAC,AAAC,IAAY,OAAT,UAAS;YAC3B,cAAc,AAAC,GAAyB,OAAvB,CAAA,GAAA,kLAAA,CAAA,eAAY,AAAD,EAAE,WAAU;YACxC,WAAW;QACf;IACJ;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,MAAM,OAAO,MAAM,CAAC,EAAE;QACtB,MAAM,eAAe,QAAQ;QAC7B,MAAM,aAAa,eAAe,CAAC,KAAK;QACxC,IAAI,cAAc;YACd,aAAa,CAAC;YACd,IAAI,QAAQ,MAAM,CAAC,IAAI,EAAE,EAAE;gBACvB;YACJ;QACJ;QACA,IAAI,cAAc,CAAC,YAAY;YAC3B,MAAM,QAAQ,iBAAiB,QAAQ;YACvC,MAAM,UAAU,cAAc,MAAM;YACpC;YACA,SAAS,IAAI,CAAC;YACd,cAAc,AAAC,IAA+B,OAA5B,WAAW,OAAM,YAAW;YAC9C,KAAK,QAAQ;QACjB,OAAO;YACH,IAAI,CAAC,UAAU;gBACX,cAAc;YAClB;YACA,YAAY;QAChB;IACJ;IACA;IACA,IAAI,CAAC,2BAA2B,WAAW;QACvC,mLAAA,CAAA,SAAM,CAAC,IAAI,CAAC,AAAC,mDAAyD,OAAP,QAAO;IAC1E;IACA,OAAO;QACH,UAAU;QACV,QAAQ,IAAI,OAAO,AAAC,IAAc,OAAX,YAAW,MAAI;IAC1C;AACJ;AACA,MAAM,oBAAoB;IAAC;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;IAAK;CAAI;AAChE,MAAM,6BAA6B,SAAS,QAAQ;IACvD,MAAM,iBAAiB,CAAA;QACnB,IAAI,CAAC,SAAS;YACV,OAAO;QACX;QACA,MAAM,OAAO,OAAO,CAAC,EAAE;QACvB,OAAO;YAAC;YAAK;SAAI,CAAC,QAAQ,CAAC,SAAS,kBAAkB,QAAQ,CAAC,SAAS,QAAQ,MAAM,GAAG;IAC7F;IACA,IAAI,qBAAqB;IACzB,IAAI,8BAA8B;IAClC,OAAO,SAAS,KAAK,CAAE,CAAC,SAAS,OAAO;QACpC,IAAI,eAAe,UAAU;YACzB,IAAI,CAAC,CAAA,UAAW,QAAQ,OAAO,CAAC,EAAE,IAAI,MAAM,QAAQ,MAAM,EAAE,UAAU;gBAClE,qBAAqB,EAAE,8BAA8B;YACzD;YACA,IAAI,CAAC,eAAe,QAAQ,CAAC,QAAQ,EAAE,GAAG;gBACtC,8BAA8B;YAClC;QACJ;QACA,OAAO;IACX;AACJ;AACO,MAAM,oBAAoB;IAC7B,OAAO;AACX;AACA,MAAM,iBAAiB,SAAS,IAAI,EAAE,OAAO,EAAE,IAAI,EAAE,SAAS,EAAE,cAAc;IAC1E,MAAM,cAAc,OAAO,CAAC,EAAE;IAC9B,MAAM,aAAa,eAAe,CAAC,YAAY;IAC/C,MAAM,aAAa,eAAe,CAAC,YAAY;IAC/C,IAAI,cAAc,YAAY;QAC1B,MAAM,QAAQ,WAAW,MAAM,QAAQ,MAAM,EAAE;QAC/C,cAAc,CAAC,QAAQ,GAAG;QAC1B,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,CAAC,WAAW,CAAC;QACrB,OAAO;YACH,WAAW,MAAM,OAAO;QAC5B;IACJ;AACJ;AACA,MAAM,wBAAwB,SAAS,IAAI,EAAE,OAAO,EAAE,GAAG;IACrD,MAAM,aAAa,eAAe,CAAC,QAAQ;IAC3C,MAAM,aAAa,MAAM,WAAW,MAAM,CAAC;IAC3C,MAAM,QAAQ,GAAG,CAAC,WAAW;IAC7B,IAAI,CAAC,WAAW,CAAC;AACrB;AACA,MAAM,mBAAmB,SAAS,YAAY;IAC1C,OAAO,aAAa,GAAG,CAAE,SAAS,OAAO;QACrC,IAAI,QAAQ,OAAO,CAAC,EAAE,EAAE;YACpB,OAAO;QACX,OAAO;YACH,OAAO,QAAQ,OAAO,CAAC,EAAE,GAAG,MAAM,OAAO,CAAC,EAAE;QAChD;IACJ;AACJ;AACA,MAAM,4BAA4B,SAAS,QAAQ;IAC/C,MAAM,UAAU,SAAS,GAAG,CAAE,SAAS,OAAO;QAC1C,OAAO,iBAAiB,OAAO,CAAC;IACpC;IACA,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM;AAChC;AACA,MAAM,2BAA2B,SAAS,cAAc;IACpD,MAAM,gBAAgB,eAAe,MAAM,CAAE,SAAS,OAAO;QACzD,OAAO,iBAAiB,OAAO,CAAC,WAAW;IAC/C;IACA,OAAO,iBAAiB,MAAM,CAAC;AACnC;AACO,MAAM,YAAY,SAAS,MAAM,EAAE,SAAS;IAC/C,MAAM,aAAa,cAAc,QAAQ;IACzC,OAAO,SAAS,IAAI;QAChB,MAAM,eAAe,WAAW,MAAM,CAAC,IAAI,CAAC;QAC5C,IAAI,cAAc;YACd,MAAM,MAAM,IAAI;YAChB,MAAM,OAAO,IAAI,KAAK,IAAI,WAAW,IAAI,GAAG;YAC5C,MAAM,iBAAiB,iBAAiB,WAAW,QAAQ;YAC3D,MAAM,kBAAkB,0BAA0B;YAClD,MAAM,wBAAwB,yBAAyB;YACvD,MAAM,iBAAiB,CAAC;YACxB,sBAAsB,OAAO,CAAE,SAAS,OAAO,EAAE,KAAK;gBAClD,IAAI,CAAC,WAAW,QAAQ,iBAAiB,MAAM,IAAI,QAAQ,iBAAiB;oBACxE;gBACJ;gBACA,MAAM,eAAe,eAAe,OAAO,CAAC;gBAC5C,IAAI,gBAAgB,GAAG;oBACnB,MAAM,gBAAgB,WAAW,QAAQ,CAAC,aAAa;oBACvD,MAAM,aAAa,YAAY,CAAC,eAAe,EAAE;oBACjD,eAAe,MAAM,eAAe,YAAY,WAAW;gBAC/D,OAAO;oBACH,sBAAsB,MAAM,SAAS;gBACzC;YACJ;YACA,OAAO;QACX;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6512, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/cldr-data/first_day_of_week_data.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/cldr-data/first_day_of_week_data.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\n// !!! AUTO-GENERATED FILE, DO NOT EDIT\r\nexport default {\r\n    \"af-NA\": 1,\r\n    agq: 1,\r\n    ak: 1,\r\n    ar: 6,\r\n    \"ar-EH\": 1,\r\n    \"ar-ER\": 1,\r\n    \"ar-KM\": 1,\r\n    \"ar-LB\": 1,\r\n    \"ar-MA\": 1,\r\n    \"ar-MR\": 1,\r\n    \"ar-PS\": 1,\r\n    \"ar-SO\": 1,\r\n    \"ar-SS\": 1,\r\n    \"ar-TD\": 1,\r\n    \"ar-TN\": 1,\r\n    asa: 1,\r\n    ast: 1,\r\n    az: 1,\r\n    \"az-Cyrl\": 1,\r\n    bas: 1,\r\n    be: 1,\r\n    bem: 1,\r\n    bez: 1,\r\n    bg: 1,\r\n    bm: 1,\r\n    br: 1,\r\n    bs: 1,\r\n    \"bs-Cyrl\": 1,\r\n    ca: 1,\r\n    ce: 1,\r\n    cgg: 1,\r\n    ckb: 6,\r\n    cs: 1,\r\n    cy: 1,\r\n    da: 1,\r\n    de: 1,\r\n    dje: 1,\r\n    dsb: 1,\r\n    dua: 1,\r\n    dyo: 1,\r\n    ee: 1,\r\n    el: 1,\r\n    \"en-001\": 1,\r\n    \"en-AE\": 6,\r\n    \"en-BI\": 1,\r\n    \"en-MP\": 1,\r\n    \"en-MV\": 5,\r\n    \"en-SD\": 6,\r\n    eo: 1,\r\n    es: 1,\r\n    et: 1,\r\n    eu: 1,\r\n    ewo: 1,\r\n    fa: 6,\r\n    ff: 1,\r\n    \"ff-Adlm\": 1,\r\n    fi: 1,\r\n    fo: 1,\r\n    fr: 1,\r\n    \"fr-DJ\": 6,\r\n    \"fr-DZ\": 6,\r\n    \"fr-SY\": 6,\r\n    fur: 1,\r\n    fy: 1,\r\n    ga: 1,\r\n    gd: 1,\r\n    gl: 1,\r\n    gsw: 1,\r\n    gv: 1,\r\n    ha: 1,\r\n    hr: 1,\r\n    hsb: 1,\r\n    hu: 1,\r\n    hy: 1,\r\n    ia: 1,\r\n    ig: 1,\r\n    is: 1,\r\n    it: 1,\r\n    jgo: 1,\r\n    jmc: 1,\r\n    ka: 1,\r\n    kab: 6,\r\n    kde: 1,\r\n    kea: 1,\r\n    khq: 1,\r\n    kk: 1,\r\n    kkj: 1,\r\n    kl: 1,\r\n    \"ko-KP\": 1,\r\n    ksb: 1,\r\n    ksf: 1,\r\n    ksh: 1,\r\n    ku: 1,\r\n    kw: 1,\r\n    ky: 1,\r\n    lag: 1,\r\n    lb: 1,\r\n    lg: 1,\r\n    ln: 1,\r\n    lrc: 6,\r\n    lt: 1,\r\n    lu: 1,\r\n    lv: 1,\r\n    \"mas-TZ\": 1,\r\n    mfe: 1,\r\n    mg: 1,\r\n    mgo: 1,\r\n    mi: 1,\r\n    mk: 1,\r\n    mn: 1,\r\n    ms: 1,\r\n    mua: 1,\r\n    mzn: 6,\r\n    naq: 1,\r\n    nds: 1,\r\n    nl: 1,\r\n    nmg: 1,\r\n    nnh: 1,\r\n    no: 1,\r\n    nus: 1,\r\n    nyn: 1,\r\n    os: 1,\r\n    pcm: 1,\r\n    pl: 1,\r\n    ps: 6,\r\n    \"pt-AO\": 1,\r\n    \"pt-CH\": 1,\r\n    \"pt-CV\": 1,\r\n    \"pt-GQ\": 1,\r\n    \"pt-GW\": 1,\r\n    \"pt-LU\": 1,\r\n    \"pt-ST\": 1,\r\n    \"pt-TL\": 1,\r\n    \"qu-BO\": 1,\r\n    \"qu-EC\": 1,\r\n    rm: 1,\r\n    rn: 1,\r\n    ro: 1,\r\n    rof: 1,\r\n    ru: 1,\r\n    rw: 1,\r\n    rwk: 1,\r\n    sah: 1,\r\n    sbp: 1,\r\n    sc: 1,\r\n    se: 1,\r\n    ses: 1,\r\n    sg: 1,\r\n    shi: 1,\r\n    \"shi-Latn\": 1,\r\n    si: 1,\r\n    sk: 1,\r\n    sl: 1,\r\n    smn: 1,\r\n    so: 1,\r\n    \"so-DJ\": 6,\r\n    sq: 1,\r\n    sr: 1,\r\n    \"sr-Latn\": 1,\r\n    sv: 1,\r\n    sw: 1,\r\n    \"ta-LK\": 1,\r\n    \"ta-MY\": 1,\r\n    teo: 1,\r\n    tg: 1,\r\n    \"ti-ER\": 1,\r\n    tk: 1,\r\n    to: 1,\r\n    tr: 1,\r\n    tt: 1,\r\n    twq: 1,\r\n    tzm: 1,\r\n    uk: 1,\r\n    uz: 1,\r\n    \"uz-Arab\": 6,\r\n    \"uz-Cyrl\": 1,\r\n    vai: 1,\r\n    \"vai-Latn\": 1,\r\n    vi: 1,\r\n    vun: 1,\r\n    wae: 1,\r\n    wo: 1,\r\n    xog: 1,\r\n    yav: 1,\r\n    yi: 1,\r\n    yo: 1,\r\n    zgh: 1\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC,GACD,uCAAuC;;;;uCACxB;IACX,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,KAAK;IACL,KAAK;IACL,IAAI;IACJ,WAAW;IACX,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,UAAU;IACV,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,KAAK;IACL,YAAY;IACZ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,IAAI;IACJ,IAAI;IACJ,SAAS;IACT,SAAS;IACT,KAAK;IACL,IAAI;IACJ,SAAS;IACT,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,WAAW;IACX,WAAW;IACX,KAAK;IACL,YAAY;IACZ,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,KAAK;IACL,KAAK;IACL,IAAI;IACJ,IAAI;IACJ,KAAK;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6716, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/intl/date.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/intl/date.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport localizationCoreUtils from \"../core\";\r\nconst SYMBOLS_TO_REMOVE_REGEX = /[\\u200E\\u200F]/g;\r\nconst NARROW_NO_BREAK_SPACE_REGEX = /[\\u202F]/g;\r\nconst getIntlFormatter = format => date => {\r\n    if (!format.timeZoneName) {\r\n        const year = date.getFullYear();\r\n        const recognizableAsTwentyCentury = String(year).length < 3;\r\n        const safeYearShift = 400;\r\n        const temporaryYearValue = recognizableAsTwentyCentury ? year + safeYearShift : year;\r\n        const utcDate = new Date(Date.UTC(temporaryYearValue, date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds(), date.getMilliseconds()));\r\n        if (recognizableAsTwentyCentury) {\r\n            utcDate.setFullYear(year)\r\n        }\r\n        const utcFormat = extend({\r\n            timeZone: \"UTC\"\r\n        }, format);\r\n        return formatDateTime(utcDate, utcFormat)\r\n    }\r\n    return formatDateTime(date, format)\r\n};\r\nconst formattersCache = {};\r\nconst getFormatter = format => {\r\n    const key = localizationCoreUtils.locale() + \"/\" + JSON.stringify(format);\r\n    if (!formattersCache[key]) {\r\n        formattersCache[key] = new Intl.DateTimeFormat(localizationCoreUtils.locale(), format).format\r\n    }\r\n    return formattersCache[key]\r\n};\r\n\r\nfunction formatDateTime(date, format) {\r\n    return getFormatter(format)(date).replace(SYMBOLS_TO_REMOVE_REGEX, \"\").replace(NARROW_NO_BREAK_SPACE_REGEX, \" \")\r\n}\r\nconst formatNumber = number => new Intl.NumberFormat(localizationCoreUtils.locale()).format(number);\r\nconst getAlternativeNumeralsMap = (() => {\r\n    const numeralsMapCache = {};\r\n    return locale => {\r\n        if (!(locale in numeralsMapCache)) {\r\n            if (\"0\" === formatNumber(0)) {\r\n                numeralsMapCache[locale] = false;\r\n                return false\r\n            }\r\n            numeralsMapCache[locale] = {};\r\n            for (let i = 0; i < 10; ++i) {\r\n                numeralsMapCache[locale][formatNumber(i)] = i\r\n            }\r\n        }\r\n        return numeralsMapCache[locale]\r\n    }\r\n})();\r\nconst normalizeNumerals = dateString => {\r\n    const alternativeNumeralsMap = getAlternativeNumeralsMap(localizationCoreUtils.locale());\r\n    if (!alternativeNumeralsMap) {\r\n        return dateString\r\n    }\r\n    return dateString.split(\"\").map((sign => sign in alternativeNumeralsMap ? String(alternativeNumeralsMap[sign]) : sign)).join(\"\")\r\n};\r\nconst removeLeadingZeroes = str => str.replace(/(\\D)0+(\\d)/g, \"$1$2\");\r\nconst dateStringEquals = (actual, expected) => removeLeadingZeroes(actual) === removeLeadingZeroes(expected);\r\nconst normalizeMonth = text => text.replace(\"d\\u2019\", \"de \");\r\nconst intlFormats = {\r\n    day: {\r\n        day: \"numeric\"\r\n    },\r\n    date: {\r\n        year: \"numeric\",\r\n        month: \"long\",\r\n        day: \"numeric\"\r\n    },\r\n    dayofweek: {\r\n        weekday: \"long\"\r\n    },\r\n    longdate: {\r\n        weekday: \"long\",\r\n        year: \"numeric\",\r\n        month: \"long\",\r\n        day: \"numeric\"\r\n    },\r\n    longdatelongtime: {\r\n        weekday: \"long\",\r\n        year: \"numeric\",\r\n        month: \"long\",\r\n        day: \"numeric\",\r\n        hour: \"numeric\",\r\n        minute: \"numeric\",\r\n        second: \"numeric\"\r\n    },\r\n    longtime: {\r\n        hour: \"numeric\",\r\n        minute: \"numeric\",\r\n        second: \"numeric\"\r\n    },\r\n    month: {\r\n        month: \"long\"\r\n    },\r\n    monthandday: {\r\n        month: \"long\",\r\n        day: \"numeric\"\r\n    },\r\n    monthandyear: {\r\n        year: \"numeric\",\r\n        month: \"long\"\r\n    },\r\n    shortdate: {},\r\n    shorttime: {\r\n        hour: \"numeric\",\r\n        minute: \"numeric\"\r\n    },\r\n    shortyear: {\r\n        year: \"2-digit\"\r\n    },\r\n    year: {\r\n        year: \"numeric\"\r\n    }\r\n};\r\nObject.defineProperty(intlFormats, \"shortdateshorttime\", {\r\n    get: function() {\r\n        const defaultOptions = Intl.DateTimeFormat(localizationCoreUtils.locale()).resolvedOptions();\r\n        return {\r\n            year: defaultOptions.year,\r\n            month: defaultOptions.month,\r\n            day: defaultOptions.day,\r\n            hour: \"numeric\",\r\n            minute: \"numeric\"\r\n        }\r\n    }\r\n});\r\nconst getIntlFormat = format => \"string\" === typeof format && intlFormats[format.toLowerCase()];\r\nconst monthNameStrategies = {\r\n    standalone: function(monthIndex, monthFormat) {\r\n        const date = new Date(1999, monthIndex, 13, 1);\r\n        const dateString = getIntlFormatter({\r\n            month: monthFormat\r\n        })(date);\r\n        return dateString\r\n    },\r\n    format: function(monthIndex, monthFormat) {\r\n        const date = new Date(0, monthIndex, 13, 1);\r\n        const dateString = normalizeMonth(getIntlFormatter({\r\n            day: \"numeric\",\r\n            month: monthFormat\r\n        })(date));\r\n        const parts = dateString.split(\" \").filter((part => part.indexOf(\"13\") < 0));\r\n        if (1 === parts.length) {\r\n            return parts[0]\r\n        } else if (2 === parts.length) {\r\n            return parts[0].length > parts[1].length ? parts[0] : parts[1]\r\n        }\r\n        return monthNameStrategies.standalone(monthIndex, monthFormat)\r\n    }\r\n};\r\nexport default {\r\n    engine: function() {\r\n        return \"intl\"\r\n    },\r\n    getMonthNames: function(format, type) {\r\n        const monthFormat = {\r\n            wide: \"long\",\r\n            abbreviated: \"short\",\r\n            narrow: \"narrow\"\r\n        } [format || \"wide\"];\r\n        type = \"format\" === type ? type : \"standalone\";\r\n        return Array.apply(null, new Array(12)).map(((_, monthIndex) => monthNameStrategies[type](monthIndex, monthFormat)))\r\n    },\r\n    getDayNames: function(format) {\r\n        const result = (format => Array.apply(null, new Array(7)).map(((_, dayIndex) => getIntlFormatter({\r\n            weekday: format\r\n        })(new Date(0, 0, dayIndex)))))({\r\n            wide: \"long\",\r\n            abbreviated: \"short\",\r\n            short: \"narrow\",\r\n            narrow: \"narrow\"\r\n        } [format || \"wide\"]);\r\n        return result\r\n    },\r\n    getPeriodNames: function() {\r\n        const hour12Formatter = getIntlFormatter({\r\n            hour: \"numeric\",\r\n            hour12: true\r\n        });\r\n        return [1, 13].map((hours => {\r\n            const hourNumberText = formatNumber(1);\r\n            const timeParts = hour12Formatter(new Date(0, 0, 1, hours)).split(hourNumberText);\r\n            if (2 !== timeParts.length) {\r\n                return \"\"\r\n            }\r\n            const biggerPart = timeParts[0].length > timeParts[1].length ? timeParts[0] : timeParts[1];\r\n            return biggerPart.trim()\r\n        }))\r\n    },\r\n    format: function(date, format) {\r\n        if (!date) {\r\n            return\r\n        }\r\n        if (!format) {\r\n            return date\r\n        }\r\n        if (\"function\" !== typeof format && !format.formatter) {\r\n            format = format.type || format\r\n        }\r\n        const intlFormat = getIntlFormat(format);\r\n        if (intlFormat) {\r\n            return getIntlFormatter(intlFormat)(date)\r\n        }\r\n        const formatType = typeof format;\r\n        if (format.formatter || \"function\" === formatType || \"string\" === formatType) {\r\n            return this.callBase.apply(this, arguments)\r\n        }\r\n        return getIntlFormatter(format)(date)\r\n    },\r\n    parse: function(dateString, format) {\r\n        let formatter;\r\n        if (format && !format.parser && \"string\" === typeof dateString) {\r\n            dateString = normalizeMonth(dateString);\r\n            formatter = date => normalizeMonth(this.format(date, format))\r\n        }\r\n        return this.callBase(dateString, formatter || format)\r\n    },\r\n    _parseDateBySimpleFormat: function(dateString, format) {\r\n        dateString = normalizeNumerals(dateString);\r\n        const formatParts = this.getFormatParts(format);\r\n        const dateParts = dateString.split(/\\D+/).filter((part => part.length > 0));\r\n        if (formatParts.length !== dateParts.length) {\r\n            return\r\n        }\r\n        const dateArgs = this._generateDateArgs(formatParts, dateParts);\r\n        const constructValidDate = ampmShift => {\r\n            const parsedDate = ((dateArgs, ampmShift) => {\r\n                const hoursShift = ampmShift ? 12 : 0;\r\n                return new Date(dateArgs.year, dateArgs.month, dateArgs.day, (dateArgs.hours + hoursShift) % 24, dateArgs.minutes, dateArgs.seconds)\r\n            })(dateArgs, ampmShift);\r\n            if (dateStringEquals(normalizeNumerals(this.format(parsedDate, format)), dateString)) {\r\n                return parsedDate\r\n            }\r\n        };\r\n        return constructValidDate(false) || constructValidDate(true)\r\n    },\r\n    _generateDateArgs: function(formatParts, dateParts) {\r\n        const currentDate = new Date;\r\n        const dateArgs = {\r\n            year: currentDate.getFullYear(),\r\n            month: currentDate.getMonth(),\r\n            day: currentDate.getDate(),\r\n            hours: 0,\r\n            minutes: 0,\r\n            seconds: 0\r\n        };\r\n        formatParts.forEach(((formatPart, index) => {\r\n            const datePart = dateParts[index];\r\n            let parsed = parseInt(datePart, 10);\r\n            if (\"month\" === formatPart) {\r\n                parsed -= 1\r\n            }\r\n            dateArgs[formatPart] = parsed\r\n        }));\r\n        return dateArgs\r\n    },\r\n    formatUsesMonthName: function(format) {\r\n        if (\"object\" === typeof format && !(format.type || format.format)) {\r\n            return \"long\" === format.month\r\n        }\r\n        return this.callBase.apply(this, arguments)\r\n    },\r\n    formatUsesDayName: function(format) {\r\n        if (\"object\" === typeof format && !(format.type || format.format)) {\r\n            return \"long\" === format.weekday\r\n        }\r\n        return this.callBase.apply(this, arguments)\r\n    },\r\n    getTimeSeparator: function() {\r\n        return normalizeNumerals(formatDateTime(new Date(2001, 1, 1, 11, 11), {\r\n            hour: \"numeric\",\r\n            minute: \"numeric\",\r\n            hour12: false\r\n        })).replace(/\\d/g, \"\")\r\n    },\r\n    getFormatParts: function(format) {\r\n        if (\"string\" === typeof format) {\r\n            return this.callBase(format)\r\n        }\r\n        const intlFormat = extend({}, intlFormats[format.toLowerCase()]);\r\n        const date = new Date(2001, 2, 4, 5, 6, 7);\r\n        let formattedDate = getIntlFormatter(intlFormat)(date);\r\n        formattedDate = normalizeNumerals(formattedDate);\r\n        return [{\r\n            name: \"year\",\r\n            value: 1\r\n        }, {\r\n            name: \"month\",\r\n            value: 3\r\n        }, {\r\n            name: \"day\",\r\n            value: 4\r\n        }, {\r\n            name: \"hours\",\r\n            value: 5\r\n        }, {\r\n            name: \"minutes\",\r\n            value: 6\r\n        }, {\r\n            name: \"seconds\",\r\n            value: 7\r\n        }].map((part => ({\r\n            name: part.name,\r\n            index: formattedDate.indexOf(part.value)\r\n        }))).filter((part => part.index > -1)).sort(((a, b) => a.index - b.index)).map((part => part.name))\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;;;AACA,MAAM,0BAA0B;AAChC,MAAM,8BAA8B;AACpC,MAAM,mBAAmB,CAAA,SAAU,CAAA;QAC/B,IAAI,CAAC,OAAO,YAAY,EAAE;YACtB,MAAM,OAAO,KAAK,WAAW;YAC7B,MAAM,8BAA8B,OAAO,MAAM,MAAM,GAAG;YAC1D,MAAM,gBAAgB;YACtB,MAAM,qBAAqB,8BAA8B,OAAO,gBAAgB;YAChF,MAAM,UAAU,IAAI,KAAK,KAAK,GAAG,CAAC,oBAAoB,KAAK,QAAQ,IAAI,KAAK,OAAO,IAAI,KAAK,QAAQ,IAAI,KAAK,UAAU,IAAI,KAAK,UAAU,IAAI,KAAK,eAAe;YAClK,IAAI,6BAA6B;gBAC7B,QAAQ,WAAW,CAAC;YACxB;YACA,MAAM,YAAY,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;gBACrB,UAAU;YACd,GAAG;YACH,OAAO,eAAe,SAAS;QACnC;QACA,OAAO,eAAe,MAAM;IAChC;AACA,MAAM,kBAAkB,CAAC;AACzB,MAAM,eAAe,CAAA;IACjB,MAAM,MAAM,8KAAA,CAAA,UAAqB,CAAC,MAAM,KAAK,MAAM,KAAK,SAAS,CAAC;IAClE,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE;QACvB,eAAe,CAAC,IAAI,GAAG,IAAI,KAAK,cAAc,CAAC,8KAAA,CAAA,UAAqB,CAAC,MAAM,IAAI,QAAQ,MAAM;IACjG;IACA,OAAO,eAAe,CAAC,IAAI;AAC/B;AAEA,SAAS,eAAe,IAAI,EAAE,MAAM;IAChC,OAAO,aAAa,QAAQ,MAAM,OAAO,CAAC,yBAAyB,IAAI,OAAO,CAAC,6BAA6B;AAChH;AACA,MAAM,eAAe,CAAA,SAAU,IAAI,KAAK,YAAY,CAAC,8KAAA,CAAA,UAAqB,CAAC,MAAM,IAAI,MAAM,CAAC;AAC5F,MAAM,4BAA4B,CAAC;IAC/B,MAAM,mBAAmB,CAAC;IAC1B,OAAO,CAAA;QACH,IAAI,CAAC,CAAC,UAAU,gBAAgB,GAAG;YAC/B,IAAI,QAAQ,aAAa,IAAI;gBACzB,gBAAgB,CAAC,OAAO,GAAG;gBAC3B,OAAO;YACX;YACA,gBAAgB,CAAC,OAAO,GAAG,CAAC;YAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;gBACzB,gBAAgB,CAAC,OAAO,CAAC,aAAa,GAAG,GAAG;YAChD;QACJ;QACA,OAAO,gBAAgB,CAAC,OAAO;IACnC;AACJ,CAAC;AACD,MAAM,oBAAoB,CAAA;IACtB,MAAM,yBAAyB,0BAA0B,8KAAA,CAAA,UAAqB,CAAC,MAAM;IACrF,IAAI,CAAC,wBAAwB;QACzB,OAAO;IACX;IACA,OAAO,WAAW,KAAK,CAAC,IAAI,GAAG,CAAE,CAAA,OAAQ,QAAQ,yBAAyB,OAAO,sBAAsB,CAAC,KAAK,IAAI,MAAO,IAAI,CAAC;AACjI;AACA,MAAM,sBAAsB,CAAA,MAAO,IAAI,OAAO,CAAC,eAAe;AAC9D,MAAM,mBAAmB,CAAC,QAAQ,WAAa,oBAAoB,YAAY,oBAAoB;AACnG,MAAM,iBAAiB,CAAA,OAAQ,KAAK,OAAO,CAAC,WAAW;AACvD,MAAM,cAAc;IAChB,KAAK;QACD,KAAK;IACT;IACA,MAAM;QACF,MAAM;QACN,OAAO;QACP,KAAK;IACT;IACA,WAAW;QACP,SAAS;IACb;IACA,UAAU;QACN,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;IACT;IACA,kBAAkB;QACd,SAAS;QACT,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;QACR,QAAQ;IACZ;IACA,UAAU;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;IACZ;IACA,OAAO;QACH,OAAO;IACX;IACA,aAAa;QACT,OAAO;QACP,KAAK;IACT;IACA,cAAc;QACV,MAAM;QACN,OAAO;IACX;IACA,WAAW,CAAC;IACZ,WAAW;QACP,MAAM;QACN,QAAQ;IACZ;IACA,WAAW;QACP,MAAM;IACV;IACA,MAAM;QACF,MAAM;IACV;AACJ;AACA,OAAO,cAAc,CAAC,aAAa,sBAAsB;IACrD,KAAK;QACD,MAAM,iBAAiB,KAAK,cAAc,CAAC,8KAAA,CAAA,UAAqB,CAAC,MAAM,IAAI,eAAe;QAC1F,OAAO;YACH,MAAM,eAAe,IAAI;YACzB,OAAO,eAAe,KAAK;YAC3B,KAAK,eAAe,GAAG;YACvB,MAAM;YACN,QAAQ;QACZ;IACJ;AACJ;AACA,MAAM,gBAAgB,CAAA,SAAU,aAAa,OAAO,UAAU,WAAW,CAAC,OAAO,WAAW,GAAG;AAC/F,MAAM,sBAAsB;IACxB,YAAY,SAAS,UAAU,EAAE,WAAW;QACxC,MAAM,OAAO,IAAI,KAAK,MAAM,YAAY,IAAI;QAC5C,MAAM,aAAa,iBAAiB;YAChC,OAAO;QACX,GAAG;QACH,OAAO;IACX;IACA,QAAQ,SAAS,UAAU,EAAE,WAAW;QACpC,MAAM,OAAO,IAAI,KAAK,GAAG,YAAY,IAAI;QACzC,MAAM,aAAa,eAAe,iBAAiB;YAC/C,KAAK;YACL,OAAO;QACX,GAAG;QACH,MAAM,QAAQ,WAAW,KAAK,CAAC,KAAK,MAAM,CAAE,CAAA,OAAQ,KAAK,OAAO,CAAC,QAAQ;QACzE,IAAI,MAAM,MAAM,MAAM,EAAE;YACpB,OAAO,KAAK,CAAC,EAAE;QACnB,OAAO,IAAI,MAAM,MAAM,MAAM,EAAE;YAC3B,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM,GAAG,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;QAClE;QACA,OAAO,oBAAoB,UAAU,CAAC,YAAY;IACtD;AACJ;uCACe;IACX,QAAQ;QACJ,OAAO;IACX;IACA,eAAe,SAAS,MAAM,EAAE,IAAI;QAChC,MAAM,cAAc;YAChB,MAAM;YACN,aAAa;YACb,QAAQ;QACZ,CAAE,CAAC,UAAU,OAAO;QACpB,OAAO,aAAa,OAAO,OAAO;QAClC,OAAO,MAAM,KAAK,CAAC,MAAM,IAAI,MAAM,KAAK,GAAG,CAAE,CAAC,GAAG,aAAe,mBAAmB,CAAC,KAAK,CAAC,YAAY;IAC1G;IACA,aAAa,SAAS,MAAM;QACxB,MAAM,SAAS,CAAC,CAAA,SAAU,MAAM,KAAK,CAAC,MAAM,IAAI,MAAM,IAAI,GAAG,CAAE,CAAC,GAAG,WAAa,iBAAiB;oBAC7F,SAAS;gBACb,GAAG,IAAI,KAAK,GAAG,GAAG,WAAY,EAAE;YAC5B,MAAM;YACN,aAAa;YACb,OAAO;YACP,QAAQ;QACZ,CAAE,CAAC,UAAU,OAAO;QACpB,OAAO;IACX;IACA,gBAAgB;QACZ,MAAM,kBAAkB,iBAAiB;YACrC,MAAM;YACN,QAAQ;QACZ;QACA,OAAO;YAAC;YAAG;SAAG,CAAC,GAAG,CAAE,CAAA;YAChB,MAAM,iBAAiB,aAAa;YACpC,MAAM,YAAY,gBAAgB,IAAI,KAAK,GAAG,GAAG,GAAG,QAAQ,KAAK,CAAC;YAClE,IAAI,MAAM,UAAU,MAAM,EAAE;gBACxB,OAAO;YACX;YACA,MAAM,aAAa,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,CAAC,MAAM,GAAG,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE;YAC1F,OAAO,WAAW,IAAI;QAC1B;IACJ;IACA,QAAQ,SAAS,IAAI,EAAE,MAAM;QACzB,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,CAAC,QAAQ;YACT,OAAO;QACX;QACA,IAAI,eAAe,OAAO,UAAU,CAAC,OAAO,SAAS,EAAE;YACnD,SAAS,OAAO,IAAI,IAAI;QAC5B;QACA,MAAM,aAAa,cAAc;QACjC,IAAI,YAAY;YACZ,OAAO,iBAAiB,YAAY;QACxC;QACA,MAAM,aAAa,OAAO;QAC1B,IAAI,OAAO,SAAS,IAAI,eAAe,cAAc,aAAa,YAAY;YAC1E,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QACrC;QACA,OAAO,iBAAiB,QAAQ;IACpC;IACA,OAAO,SAAS,UAAU,EAAE,MAAM;QAC9B,IAAI;QACJ,IAAI,UAAU,CAAC,OAAO,MAAM,IAAI,aAAa,OAAO,YAAY;YAC5D,aAAa,eAAe;YAC5B,YAAY,CAAA,OAAQ,eAAe,IAAI,CAAC,MAAM,CAAC,MAAM;QACzD;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,aAAa;IAClD;IACA,0BAA0B,SAAS,UAAU,EAAE,MAAM;QACjD,aAAa,kBAAkB;QAC/B,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC;QACxC,MAAM,YAAY,WAAW,KAAK,CAAC,OAAO,MAAM,CAAE,CAAA,OAAQ,KAAK,MAAM,GAAG;QACxE,IAAI,YAAY,MAAM,KAAK,UAAU,MAAM,EAAE;YACzC;QACJ;QACA,MAAM,WAAW,IAAI,CAAC,iBAAiB,CAAC,aAAa;QACrD,MAAM,qBAAqB,CAAA;YACvB,MAAM,aAAa,CAAC,CAAC,UAAU;gBAC3B,MAAM,aAAa,YAAY,KAAK;gBACpC,OAAO,IAAI,KAAK,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,GAAG,EAAE,CAAC,SAAS,KAAK,GAAG,UAAU,IAAI,IAAI,SAAS,OAAO,EAAE,SAAS,OAAO;YACvI,CAAC,EAAE,UAAU;YACb,IAAI,iBAAiB,kBAAkB,IAAI,CAAC,MAAM,CAAC,YAAY,UAAU,aAAa;gBAClF,OAAO;YACX;QACJ;QACA,OAAO,mBAAmB,UAAU,mBAAmB;IAC3D;IACA,mBAAmB,SAAS,WAAW,EAAE,SAAS;QAC9C,MAAM,cAAc,IAAI;QACxB,MAAM,WAAW;YACb,MAAM,YAAY,WAAW;YAC7B,OAAO,YAAY,QAAQ;YAC3B,KAAK,YAAY,OAAO;YACxB,OAAO;YACP,SAAS;YACT,SAAS;QACb;QACA,YAAY,OAAO,CAAE,CAAC,YAAY;YAC9B,MAAM,WAAW,SAAS,CAAC,MAAM;YACjC,IAAI,SAAS,SAAS,UAAU;YAChC,IAAI,YAAY,YAAY;gBACxB,UAAU;YACd;YACA,QAAQ,CAAC,WAAW,GAAG;QAC3B;QACA,OAAO;IACX;IACA,qBAAqB,SAAS,MAAM;QAChC,IAAI,aAAa,OAAO,UAAU,CAAC,CAAC,OAAO,IAAI,IAAI,OAAO,MAAM,GAAG;YAC/D,OAAO,WAAW,OAAO,KAAK;QAClC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;IACrC;IACA,mBAAmB,SAAS,MAAM;QAC9B,IAAI,aAAa,OAAO,UAAU,CAAC,CAAC,OAAO,IAAI,IAAI,OAAO,MAAM,GAAG;YAC/D,OAAO,WAAW,OAAO,OAAO;QACpC;QACA,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;IACrC;IACA,kBAAkB;QACd,OAAO,kBAAkB,eAAe,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,KAAK;YAClE,MAAM;YACN,QAAQ;YACR,QAAQ;QACZ,IAAI,OAAO,CAAC,OAAO;IACvB;IACA,gBAAgB,SAAS,MAAM;QAC3B,IAAI,aAAa,OAAO,QAAQ;YAC5B,OAAO,IAAI,CAAC,QAAQ,CAAC;QACzB;QACA,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,WAAW,CAAC,OAAO,WAAW,GAAG;QAC/D,MAAM,OAAO,IAAI,KAAK,MAAM,GAAG,GAAG,GAAG,GAAG;QACxC,IAAI,gBAAgB,iBAAiB,YAAY;QACjD,gBAAgB,kBAAkB;QAClC,OAAO;YAAC;gBACJ,MAAM;gBACN,OAAO;YACX;YAAG;gBACC,MAAM;gBACN,OAAO;YACX;YAAG;gBACC,MAAM;gBACN,OAAO;YACX;YAAG;gBACC,MAAM;gBACN,OAAO;YACX;YAAG;gBACC,MAAM;gBACN,OAAO;YACX;YAAG;gBACC,MAAM;gBACN,OAAO;YACX;SAAE,CAAC,GAAG,CAAE,CAAA,OAAQ,CAAC;gBACb,MAAM,KAAK,IAAI;gBACf,OAAO,cAAc,OAAO,CAAC,KAAK,KAAK;YAC3C,CAAC,GAAI,MAAM,CAAE,CAAA,OAAQ,KAAK,KAAK,GAAG,CAAC,GAAI,IAAI,CAAE,CAAC,GAAG,IAAM,EAAE,KAAK,GAAG,EAAE,KAAK,EAAG,GAAG,CAAE,CAAA,OAAQ,KAAK,IAAI;IACrG;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7050, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization/date.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization/date.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport dependencyInjector from \"../../../core/utils/dependency_injector\";\r\nimport {\r\n    isString\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport errors from \"../../../core/errors\";\r\nimport {\r\n    getFormatter as getLDMLDateFormatter\r\n} from \"./ldml/date.formatter\";\r\nimport {\r\n    getFormat as getLDMLDateFormat\r\n} from \"./ldml/date.format\";\r\nimport {\r\n    getParser as getLDMLDateParser\r\n} from \"./ldml/date.parser\";\r\nimport defaultDateNames from \"./default_date_names\";\r\nimport firstDayOfWeekData from \"./cldr-data/first_day_of_week_data\";\r\nimport localizationCore from \"./core\";\r\nimport numberLocalization from \"./number\";\r\nimport intlDateLocalization from \"./intl/date\";\r\nconst DEFAULT_DAY_OF_WEEK_INDEX = 0;\r\nconst hasIntl = \"undefined\" !== typeof Intl;\r\nconst FORMATS_TO_PATTERN_MAP = {\r\n    shortdate: \"M/d/y\",\r\n    shorttime: \"h:mm a\",\r\n    longdate: \"EEEE, MMMM d, y\",\r\n    longtime: \"h:mm:ss a\",\r\n    monthandday: \"MMMM d\",\r\n    monthandyear: \"MMMM y\",\r\n    quarterandyear: \"QQQ y\",\r\n    day: \"d\",\r\n    year: \"y\",\r\n    shortdateshorttime: \"M/d/y, h:mm a\",\r\n    longdatelongtime: \"EEEE, MMMM d, y, h:mm:ss a\",\r\n    month: \"LLLL\",\r\n    shortyear: \"yy\",\r\n    dayofweek: \"EEEE\",\r\n    quarter: \"QQQ\",\r\n    hour: \"HH\",\r\n    minute: \"mm\",\r\n    second: \"ss\",\r\n    millisecond: \"SSS\",\r\n    \"datetime-local\": \"yyyy-MM-ddTHH':'mm':'ss\"\r\n};\r\nconst possiblePartPatterns = {\r\n    year: [\"y\", \"yy\", \"yyyy\"],\r\n    day: [\"d\", \"dd\"],\r\n    month: [\"M\", \"MM\", \"MMM\", \"MMMM\"],\r\n    hours: [\"H\", \"HH\", \"h\", \"hh\", \"ah\"],\r\n    minutes: [\"m\", \"mm\"],\r\n    seconds: [\"s\", \"ss\"],\r\n    milliseconds: [\"S\", \"SS\", \"SSS\"]\r\n};\r\nconst dateLocalization = dependencyInjector({\r\n    engine: function() {\r\n        return \"base\"\r\n    },\r\n    _getPatternByFormat: function(format) {\r\n        return FORMATS_TO_PATTERN_MAP[format.toLowerCase()]\r\n    },\r\n    _expandPattern: function(pattern) {\r\n        return this._getPatternByFormat(pattern) || pattern\r\n    },\r\n    formatUsesMonthName: function(format) {\r\n        return -1 !== this._expandPattern(format).indexOf(\"MMMM\")\r\n    },\r\n    formatUsesDayName: function(format) {\r\n        return -1 !== this._expandPattern(format).indexOf(\"EEEE\")\r\n    },\r\n    getFormatParts: function(format) {\r\n        const pattern = this._getPatternByFormat(format) || format;\r\n        const result = [];\r\n        each(pattern.split(/\\W+/), ((_, formatPart) => {\r\n            each(possiblePartPatterns, ((partName, possiblePatterns) => {\r\n                if (possiblePatterns.includes(formatPart)) {\r\n                    result.push(partName)\r\n                }\r\n            }))\r\n        }));\r\n        return result\r\n    },\r\n    getMonthNames: function(format) {\r\n        return defaultDateNames.getMonthNames(format)\r\n    },\r\n    getDayNames: function(format) {\r\n        return defaultDateNames.getDayNames(format)\r\n    },\r\n    getQuarterNames: function(format) {\r\n        return defaultDateNames.getQuarterNames(format)\r\n    },\r\n    getPeriodNames: function(format) {\r\n        return defaultDateNames.getPeriodNames(format)\r\n    },\r\n    getTimeSeparator: function() {\r\n        return \":\"\r\n    },\r\n    is24HourFormat: function(format) {\r\n        const amTime = new Date(2017, 0, 20, 11, 0, 0, 0);\r\n        const pmTime = new Date(2017, 0, 20, 23, 0, 0, 0);\r\n        const amTimeFormatted = this.format(amTime, format);\r\n        const pmTimeFormatted = this.format(pmTime, format);\r\n        for (let i = 0; i < amTimeFormatted.length; i++) {\r\n            if (amTimeFormatted[i] !== pmTimeFormatted[i]) {\r\n                return !isNaN(parseInt(amTimeFormatted[i]))\r\n            }\r\n        }\r\n    },\r\n    format: function(date, format) {\r\n        if (!date) {\r\n            return\r\n        }\r\n        if (!format) {\r\n            return date\r\n        }\r\n        let formatter;\r\n        if (\"function\" === typeof format) {\r\n            formatter = format\r\n        } else if (format.formatter) {\r\n            formatter = format.formatter\r\n        } else {\r\n            format = format.type || format;\r\n            if (isString(format)) {\r\n                format = FORMATS_TO_PATTERN_MAP[format.toLowerCase()] || format;\r\n                return numberLocalization.convertDigits(getLDMLDateFormatter(format, this)(date))\r\n            }\r\n        }\r\n        if (!formatter) {\r\n            return\r\n        }\r\n        return formatter(date)\r\n    },\r\n    parse: function(text, format) {\r\n        const that = this;\r\n        let ldmlFormat;\r\n        let formatter;\r\n        if (!text) {\r\n            return\r\n        }\r\n        if (!format) {\r\n            return this.parse(text, \"shortdate\")\r\n        }\r\n        if (format.parser) {\r\n            return format.parser(text)\r\n        }\r\n        if (\"string\" === typeof format && !FORMATS_TO_PATTERN_MAP[format.toLowerCase()]) {\r\n            ldmlFormat = format\r\n        } else {\r\n            formatter = value => {\r\n                const text = that.format(value, format);\r\n                return numberLocalization.convertDigits(text, true)\r\n            };\r\n            try {\r\n                ldmlFormat = getLDMLDateFormat(formatter)\r\n            } catch (e) {}\r\n        }\r\n        if (ldmlFormat) {\r\n            text = numberLocalization.convertDigits(text, true);\r\n            return getLDMLDateParser(ldmlFormat, this)(text)\r\n        }\r\n        errors.log(\"W0012\");\r\n        const result = new Date(text);\r\n        if (!result || isNaN(result.getTime())) {\r\n            return\r\n        }\r\n        return result\r\n    },\r\n    firstDayOfWeekIndex: function() {\r\n        const index = localizationCore.getValueByClosestLocale((locale => firstDayOfWeekData[locale]));\r\n        return void 0 === index ? 0 : index\r\n    }\r\n});\r\nif (hasIntl) {\r\n    dateLocalization.inject(intlDateLocalization)\r\n}\r\nexport default dateLocalization;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AACA;AAGA;AAGA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACA,MAAM,4BAA4B;AAClC,MAAM,UAAU,gBAAgB,OAAO;AACvC,MAAM,yBAAyB;IAC3B,WAAW;IACX,WAAW;IACX,UAAU;IACV,UAAU;IACV,aAAa;IACb,cAAc;IACd,gBAAgB;IAChB,KAAK;IACL,MAAM;IACN,oBAAoB;IACpB,kBAAkB;IAClB,OAAO;IACP,WAAW;IACX,WAAW;IACX,SAAS;IACT,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,aAAa;IACb,kBAAkB;AACtB;AACA,MAAM,uBAAuB;IACzB,MAAM;QAAC;QAAK;QAAM;KAAO;IACzB,KAAK;QAAC;QAAK;KAAK;IAChB,OAAO;QAAC;QAAK;QAAM;QAAO;KAAO;IACjC,OAAO;QAAC;QAAK;QAAM;QAAK;QAAM;KAAK;IACnC,SAAS;QAAC;QAAK;KAAK;IACpB,SAAS;QAAC;QAAK;KAAK;IACpB,cAAc;QAAC;QAAK;QAAM;KAAM;AACpC;AACA,MAAM,mBAAmB,CAAA,GAAA,4KAAA,CAAA,UAAkB,AAAD,EAAE;IACxC,QAAQ;QACJ,OAAO;IACX;IACA,qBAAqB,SAAS,MAAM;QAChC,OAAO,sBAAsB,CAAC,OAAO,WAAW,GAAG;IACvD;IACA,gBAAgB,SAAS,OAAO;QAC5B,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY;IAChD;IACA,qBAAqB,SAAS,MAAM;QAChC,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,OAAO,CAAC;IACtD;IACA,mBAAmB,SAAS,MAAM;QAC9B,OAAO,CAAC,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,OAAO,CAAC;IACtD;IACA,gBAAgB,SAAS,MAAM;QAC3B,MAAM,UAAU,IAAI,CAAC,mBAAmB,CAAC,WAAW;QACpD,MAAM,SAAS,EAAE;QACjB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,KAAK,CAAC,QAAS,CAAC,GAAG;YAC5B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,sBAAuB,CAAC,UAAU;gBACnC,IAAI,iBAAiB,QAAQ,CAAC,aAAa;oBACvC,OAAO,IAAI,CAAC;gBAChB;YACJ;QACJ;QACA,OAAO;IACX;IACA,eAAe,SAAS,MAAM;QAC1B,OAAO,4LAAA,CAAA,UAAgB,CAAC,aAAa,CAAC;IAC1C;IACA,aAAa,SAAS,MAAM;QACxB,OAAO,4LAAA,CAAA,UAAgB,CAAC,WAAW,CAAC;IACxC;IACA,iBAAiB,SAAS,MAAM;QAC5B,OAAO,4LAAA,CAAA,UAAgB,CAAC,eAAe,CAAC;IAC5C;IACA,gBAAgB,SAAS,MAAM;QAC3B,OAAO,4LAAA,CAAA,UAAgB,CAAC,cAAc,CAAC;IAC3C;IACA,kBAAkB;QACd,OAAO;IACX;IACA,gBAAgB,SAAS,MAAM;QAC3B,MAAM,SAAS,IAAI,KAAK,MAAM,GAAG,IAAI,IAAI,GAAG,GAAG;QAC/C,MAAM,SAAS,IAAI,KAAK,MAAM,GAAG,IAAI,IAAI,GAAG,GAAG;QAC/C,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,QAAQ;QAC5C,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC,QAAQ;QAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC7C,IAAI,eAAe,CAAC,EAAE,KAAK,eAAe,CAAC,EAAE,EAAE;gBAC3C,OAAO,CAAC,MAAM,SAAS,eAAe,CAAC,EAAE;YAC7C;QACJ;IACJ;IACA,QAAQ,SAAS,IAAI,EAAE,MAAM;QACzB,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,CAAC,QAAQ;YACT,OAAO;QACX;QACA,IAAI;QACJ,IAAI,eAAe,OAAO,QAAQ;YAC9B,YAAY;QAChB,OAAO,IAAI,OAAO,SAAS,EAAE;YACzB,YAAY,OAAO,SAAS;QAChC,OAAO;YACH,SAAS,OAAO,IAAI,IAAI;YACxB,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;gBAClB,SAAS,sBAAsB,CAAC,OAAO,WAAW,GAAG,IAAI;gBACzD,OAAO,gLAAA,CAAA,UAAkB,CAAC,aAAa,CAAC,CAAA,GAAA,mMAAA,CAAA,eAAoB,AAAD,EAAE,QAAQ,IAAI,EAAE;YAC/E;QACJ;QACA,IAAI,CAAC,WAAW;YACZ;QACJ;QACA,OAAO,UAAU;IACrB;IACA,OAAO,SAAS,IAAI,EAAE,MAAM;QACxB,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,MAAM;YACP;QACJ;QACA,IAAI,CAAC,QAAQ;YACT,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;QAC5B;QACA,IAAI,OAAO,MAAM,EAAE;YACf,OAAO,OAAO,MAAM,CAAC;QACzB;QACA,IAAI,aAAa,OAAO,UAAU,CAAC,sBAAsB,CAAC,OAAO,WAAW,GAAG,EAAE;YAC7E,aAAa;QACjB,OAAO;YACH,YAAY,CAAA;gBACR,MAAM,OAAO,KAAK,MAAM,CAAC,OAAO;gBAChC,OAAO,gLAAA,CAAA,UAAkB,CAAC,aAAa,CAAC,MAAM;YAClD;YACA,IAAI;gBACA,aAAa,CAAA,GAAA,gMAAA,CAAA,YAAiB,AAAD,EAAE;YACnC,EAAE,OAAO,GAAG,CAAC;QACjB;QACA,IAAI,YAAY;YACZ,OAAO,gLAAA,CAAA,UAAkB,CAAC,aAAa,CAAC,MAAM;YAC9C,OAAO,CAAA,GAAA,gMAAA,CAAA,YAAiB,AAAD,EAAE,YAAY,IAAI,EAAE;QAC/C;QACA,sJAAA,CAAA,UAAM,CAAC,GAAG,CAAC;QACX,MAAM,SAAS,IAAI,KAAK;QACxB,IAAI,CAAC,UAAU,MAAM,OAAO,OAAO,KAAK;YACpC;QACJ;QACA,OAAO;IACX;IACA,qBAAqB;QACjB,MAAM,QAAQ,8KAAA,CAAA,UAAgB,CAAC,uBAAuB,CAAE,CAAA,SAAU,gNAAA,CAAA,UAAkB,CAAC,OAAO;QAC5F,OAAO,KAAK,MAAM,QAAQ,IAAI;IAClC;AACJ;AACA,IAAI,SAAS;IACT,iBAAiB,MAAM,CAAC,sLAAA,CAAA,UAAoB;AAChD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7273, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/emitter.feedback.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/emitter.feedback.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../../__internal/events/core/m_emitter.feedback\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7294, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/hover.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/hover.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_hover\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7315, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/core/keyboard_processor.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/core/keyboard_processor.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/core/m_keyboard_processor\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7336, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/short.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/short.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_short\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7357, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/visibility_change.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/visibility_change.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport VisibilityChangeModule from \"../../../__internal/events/m_visibility_change\";\r\nexport const triggerShownEvent = VisibilityChangeModule.triggerShownEvent;\r\nexport const triggerHidingEvent = VisibilityChangeModule.triggerHidingEvent;\r\nexport const triggerResizeEvent = VisibilityChangeModule.triggerResizeEvent;\r\nexport default VisibilityChangeModule;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;;AACO,MAAM,oBAAoB,sLAAA,CAAA,UAAsB,CAAC,iBAAiB;AAClE,MAAM,qBAAqB,sLAAA,CAAA,UAAsB,CAAC,kBAAkB;AACpE,MAAM,qBAAqB,sLAAA,CAAA,UAAsB,CAAC,kBAAkB;uCAC5D,sLAAA,CAAA,UAAsB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7388, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/gesture/emitter.gesture.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/gesture/m_emitter.gesture\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7409, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/drag.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/drag.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_drag\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7430, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.scroll.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/gesture/emitter.gesture.scroll.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/gesture/m_emitter.gesture.scroll\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7451, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/hold.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/hold.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../__internal/events/m_hold\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7472, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/contextmenu.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/contextmenu.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_contextmenu\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7493, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/swipe.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/swipe.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport * from \"../../../__internal/events/m_swipe\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7514, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/gesture/swipeable.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/gesture/swipeable.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport {\r\n    default\r\n}\r\nfrom \"../../../../__internal/events/gesture/m_swipeable\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;AACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7535, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport eventsEngine from \"./events/core/events_engine\";\r\nexport const on = eventsEngine.on;\r\nexport const one = eventsEngine.one;\r\nexport const off = eventsEngine.off;\r\nexport const trigger = eventsEngine.trigger;\r\nexport const Event = eventsEngine.Event;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AACD;AAAA;;AACO,MAAM,KAAK,0LAAA,CAAA,UAAY,CAAC,EAAE;AAC1B,MAAM,MAAM,0LAAA,CAAA,UAAY,CAAC,GAAG;AAC5B,MAAM,MAAM,0LAAA,CAAA,UAAY,CAAC,GAAG;AAC5B,MAAM,UAAU,0LAAA,CAAA,UAAY,CAAC,OAAO;AACpC,MAAM,QAAQ,0LAAA,CAAA,UAAY,CAAC,KAAK", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7561, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/events/transform.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/events/transform.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    exportNames\r\n} from \"../../../__internal/events/m_transform\";\r\nexport const {\r\n    transformstart: transformstart,\r\n    transform: transform,\r\n    transformend: transformend,\r\n    translatestart: translatestart,\r\n    translate: translate,\r\n    translateend: translateend,\r\n    zoomstart: zoomstart,\r\n    zoom: zoom,\r\n    zoomend: zoomend,\r\n    pinchstart: pinchstart,\r\n    pinch: pinch,\r\n    pinchend: pinchend,\r\n    rotatestart: rotatestart,\r\n    rotate: rotate,\r\n    rotateend: rotateend\r\n} = exportNames;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;AACD;;AAGO,MAAM,EACT,gBAAgB,cAAc,EAC9B,WAAW,SAAS,EACpB,cAAc,YAAY,EAC1B,gBAAgB,cAAc,EAC9B,WAAW,SAAS,EACpB,cAAc,YAAY,EAC1B,WAAW,SAAS,EACpB,MAAM,IAAI,EACV,SAAS,OAAO,EAChB,YAAY,UAAU,EACtB,OAAO,KAAK,EACZ,UAAU,QAAQ,EAClB,aAAa,WAAW,EACxB,QAAQ,MAAM,EACd,WAAW,SAAS,EACvB,GAAG,8KAAA,CAAA,cAAW", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7592, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/common/core/localization.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/common/core/localization.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport core from \"./localization/core\";\r\nimport message from \"./localization/message\";\r\nimport number from \"./localization/number\";\r\nimport date from \"./localization/date\";\r\nimport \"./localization/currency\";\r\nexport const locale = core.locale.bind(core);\r\nexport const loadMessages = message.load.bind(message);\r\nexport const formatMessage = message.format.bind(message);\r\nexport const formatNumber = number.format.bind(number);\r\nexport const parseNumber = number.parse.bind(number);\r\nexport const formatDate = date.format.bind(date);\r\nexport const parseDate = date.parse.bind(date);\r\nexport {\r\n    message,\r\n    number,\r\n    date\r\n};\r\nexport function disableIntl() {\r\n    if (\"intl\" === number.engine()) {\r\n        number.resetInjection()\r\n    }\r\n    if (\"intl\" === date.engine()) {\r\n        date.resetInjection()\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;AACD;AACA;AACA;AACA;AACA;;;;;;AACO,MAAM,SAAS,8KAAA,CAAA,UAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8KAAA,CAAA,UAAI;AACpC,MAAM,eAAe,iLAAA,CAAA,UAAO,CAAC,IAAI,CAAC,IAAI,CAAC,iLAAA,CAAA,UAAO;AAC9C,MAAM,gBAAgB,iLAAA,CAAA,UAAO,CAAC,MAAM,CAAC,IAAI,CAAC,iLAAA,CAAA,UAAO;AACjD,MAAM,eAAe,gLAAA,CAAA,UAAM,CAAC,MAAM,CAAC,IAAI,CAAC,gLAAA,CAAA,UAAM;AAC9C,MAAM,cAAc,gLAAA,CAAA,UAAM,CAAC,KAAK,CAAC,IAAI,CAAC,gLAAA,CAAA,UAAM;AAC5C,MAAM,aAAa,8KAAA,CAAA,UAAI,CAAC,MAAM,CAAC,IAAI,CAAC,8KAAA,CAAA,UAAI;AACxC,MAAM,YAAY,8KAAA,CAAA,UAAI,CAAC,KAAK,CAAC,IAAI,CAAC,8KAAA,CAAA,UAAI;;AAMtC,SAAS;IACZ,IAAI,WAAW,gLAAA,CAAA,UAAM,CAAC,MAAM,IAAI;QAC5B,gLAAA,CAAA,UAAM,CAAC,cAAc;IACzB;IACA,IAAI,WAAW,8KAAA,CAAA,UAAI,CAAC,MAAM,IAAI;QAC1B,8KAAA,CAAA,UAAI,CAAC,cAAc;IACvB;AACJ", "ignoreList": [0], "debugId": null}}]}