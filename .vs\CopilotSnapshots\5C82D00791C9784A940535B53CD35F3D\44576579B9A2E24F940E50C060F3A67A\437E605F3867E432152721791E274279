﻿using DevExpress.Xpo;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using omsnext.api.Helpers;
using omsnext.shared.DTO;
using omsnext.shared.Models;
using System.Linq;  
using System.Threading.Tasks;

namespace omsnext.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase 
    {
        private readonly UnitOfWork _uow;
        private readonly ILogger<AuthController> _logger;
        private readonly TokenHelper _tokenHelper;

        public AuthController(UnitOfWork uow, ILogger<AuthController> logger, TokenHelper tokenHelper)
        {
            _uow = uow;
            _logger = logger;
            _tokenHelper = tokenHelper;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { error = "Hibás adatok." });

            try
            {
                var user = await _uow.Query<User>()
                    .FirstOrDefaultAsync(u => u.Email == model.Email && u.IsActive);

                if (user == null || !BCrypt.Net.BCrypt.Verify(model.Password, user.PasswordHash))
                    return Unauthorized(new { error = "Hibás e-mail vagy jelszó." });

                var token = TokenHelper.GenerateToken(user);
                if (string.IsNullOrEmpty(token))
                    return StatusCode(500, new { error = "Token generálási hiba." });

                // Get roles and permissions for response
                var roles = user.Roles.Select(r => r.Name).ToList();
                var permissions = user.Roles
                    .SelectMany(r => r.Permissions)
                    .Select(p => p.Name)
                    .Distinct()
                    .ToList();

                return Ok(new
                {
                    token,
                    user = new { user.Oid, user.Email, user.DisplayName, roles, permissions }
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Hiba a bejelentkezés során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }

        [HttpPost("notify-admin-password-reset")]
        public async Task<IActionResult> NotifyAdminPasswordReset([FromBody] PasswordResetNotificationDto model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { error = "Hibás adatok." });

            try
            {
                // Ellenőrizze, hogy létezik-e a felhasználó
                var user = await _uow.Query<User>()
                    .FirstOrDefaultAsync(u => u.Email == model.Email && u.IsActive);

                if (user == null)
                    return BadRequest(new { error = "Nem található felhasználó ezzel az e-mail címmel." });

                // Jelszó visszaállítási kérelem mentése az adatbázisba
                var resetRequest = new PasswordResetRequest(_uow)
                {
                    UserEmail = user.Email,
                    UserDisplayName = user.DisplayName,
                    UserId = user.Oid,
                    RequestDate = DateTime.UtcNow,
                    Status = "Pending",
                    RequestSource = "Desktop",
                    UserIPAddress = HttpContext.Connection.RemoteIpAddress?.ToString()
                };

                await _uow.SaveAsync(resetRequest);
                await _uow.CommitChangesAsync();

                // Log bejegyzés létrehozása
                _logger.LogInformation($"Jelszó visszaállítási kérelem mentve - ID: {resetRequest.Id}, Email: {model.Email}, Dátum: {DateTime.UtcNow}");

                // TODO: Itt implementálhatja az admin e-mail értesítést
                // await SendAdminNotificationEmail(resetRequest);

                return Ok(new { 
                    message = "Adminisztrátor értesítése sikeres.", 
                    requestId = resetRequest.Id,
                    timestamp = DateTime.UtcNow 
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hiba az admin értesítése során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }

        [HttpGet("password-reset-requests")]
        public async Task<IActionResult> GetPasswordResetRequests([FromQuery] int skip = 0, [FromQuery] int take = 50)
        {
            try
            {
                var requests = await _uow.Query<PasswordResetRequest>()
                    .OrderByDescending(r => r.RequestDate)
                    .Skip(skip)
                    .Take(take)
                    .ToListAsync();

                var totalCount = await _uow.Query<PasswordResetRequest>().CountAsync();

                return Ok(new
                {
                    data = requests.Select(r => new
                    {
                        r.Id,
                        r.UserEmail,
                        r.UserDisplayName,
                        r.RequestDate,
                        r.Status,
                        r.ProcessedByAdminName,
                        r.ProcessedDate,
                        r.AdminNotes,
                        r.RequestSource
                    }),
                    totalCount,
                    skip,
                    take
                });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hiba a jelszó visszaállítási kérelmek lekérdezése során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }

        [HttpPost("process-password-reset/{requestId}")]
        public async Task<IActionResult> ProcessPasswordResetRequest(long requestId, [FromBody] ProcessPasswordResetDto model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { error = "Hibás adatok." });

            try
            {
                var request = await _uow.GetObjectByKeyAsync<PasswordResetRequest>(requestId);
                if (request == null)
                    return NotFound(new { error = "Nem található kérelem." });

                if (request.Status != "Pending")
                    return BadRequest(new { error = "Ez a kérelem már feldolgozásra került." });

                // TODO: Admin felhasználó azonosítása a token alapján
                var adminUser = await _uow.Query<User>()
                    .FirstOrDefaultAsync(u => u.Email == model.AdminEmail && u.IsActive);

                if (adminUser == null)
                    return Unauthorized(new { error = "Nincs jogosultsága a művelethez." });

                // Kérelem feldolgozása
                request.Status = model.Action; // "Approved", "Rejected", "Completed"
                request.ProcessedByAdminId = adminUser.Oid;
                request.ProcessedByAdminName = adminUser.DisplayName;
                request.ProcessedDate = DateTime.UtcNow;
                request.AdminNotes = model.Notes;

                // Ha jóváhagyott, generálunk új jelszót
                if (model.Action == "Approved")
                {
                    var user = await _uow.GetObjectByKeyAsync<User>(request.UserId);
                    if (user != null)
                    {
                        var newPassword = GenerateRandomPassword();
                        user.PasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword);
                        
                        // TODO: Küldjük el az új jelszót e-mailben
                        // await SendNewPasswordEmail(user.Email, newPassword);
                        
                        request.Status = "Completed";
                        
                        _logger.LogInformation($"Jelszó visszaállítva - User ID: {user.Oid}, Admin: {adminUser.DisplayName}");
                    }
                }

                await _uow.SaveAsync(request);
                await _uow.CommitChangesAsync();

                return Ok(new { message = "Kérelem sikeresen feldolgozva.", status = request.Status });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hiba a jelszó visszaállítási kérelem feldolgozása során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }

        private string GenerateRandomPassword(int length = 12)
        {
            const string chars = "ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnpqrstuvwxyz23456789";
            var random = new Random();
            return new string(Enumerable.Repeat(chars, length)
                .Select(s => s[random.Next(s.Length)]).ToArray());
        }
    }
}
