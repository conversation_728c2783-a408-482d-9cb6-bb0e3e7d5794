﻿using DevExpress.Xpo;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using omsnext.api.Helpers;
using omsnext.shared.DTO;
using omsnext.shared.Models;
using System.Threading.Tasks;

namespace omsnext.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase 
    {
        private readonly UnitOfWork _uow;
        private readonly ILogger<AuthController> _logger;
        private readonly TokenHelper _tokenHelper;

        public AuthController(UnitOfWork uow, ILogger<AuthController> logger, TokenHelper tokenHelper)
        {
            _uow = uow;
            _logger = logger;
            _tokenHelper = tokenHelper;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { error = "Hibás adatok." });

            try
            {
                var user = await _uow.Query<User>()
                    .FirstOrDefaultAsync(u => u.Email == model.Email && u.IsActive);

                if (user == null || !BCrypt.Net.BCrypt.Verify(model.Password, user.PasswordHash))
                    return Unauthorized(new { error = "Hibás e-mail vagy jelszó." });

                var token = TokenHelper.GenerateToken(user);
                if (string.IsNullOrEmpty(token))
                    return StatusCode(500, new { error = "Token generálási hiba." });

                return Ok(new
                {
                    token,
                    user = new { user.Oid, user.Email, user.DisplayName }
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Hiba a bejelentkezés során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }
    }
}
