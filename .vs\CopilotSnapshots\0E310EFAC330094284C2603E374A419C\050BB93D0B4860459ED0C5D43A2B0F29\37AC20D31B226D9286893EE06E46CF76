﻿using System.Windows;

namespace omsnext.wpf;

public partial class DashboardWindow : Window
{
    private readonly LoginResponse _loginData;

    public DashboardWindow(LoginResponse loginData)
    {
        InitializeComponent();
        _loginData = loginData;
        InitializeUser();
    }

    private void InitializeUser()
    {
        UserNameLabel.Text = $"Üdvözöljük, {_loginData.User.DisplayName}!";
        WelcomeLabel.Text = $"Üdvözöljük a rendszerben, {_loginData.User.DisplayName}!";
    }

    private void LogoutButton_Click(object sender, RoutedEventArgs e)
    {
        var result = MessageBox.Show("Biztosan ki szeretne jelent<PERSON>?", 
                                   "Kijelentkezés", 
                                   MessageBoxButton.YesNo, 
                                   MessageBoxImage.Question);
        
        if (result == MessageBoxResult.Yes)
        {
            var loginWindow = new LoginWindow();
            loginWindow.Show();
            this.Close();
        }
    }

    private void UsersButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("Felhasználók modul hamarosan elérhető!", "Információ", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void SettingsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("Beállítások modul hamarosan elérhető!", "Információ", MessageBoxButton.OK, MessageBoxImage.Information);
    }

    private void ReportsButton_Click(object sender, RoutedEventArgs e)
    {
        MessageBox.Show("Jelentések modul hamarosan elérhető!", "Információ", MessageBoxButton.OK, MessageBoxImage.Information);
    }
}