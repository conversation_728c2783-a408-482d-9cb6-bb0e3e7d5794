﻿using System.Net.Http;
using System.Text;
using System.Text.Json;
using omsnext.shared.DTO;
using omsnext.core.Models;

namespace omsnext.wpf.Services
{
    public class ApiClient
    {
        private readonly HttpClient _httpClient;
        public string? Token { get; private set; }
        public UserInfoDto? CurrentUser { get; private set; }
        public ApiClient(string baseUrl = "http://localhost:5292")
        {
            _httpClient = new HttpClient { BaseAddress = new Uri(baseUrl) };
        }

        public async Task<LoginResponseDto?> LoginAsync(string email, string password)
        {
            var loginDto = new LoginDto { Email = email, Password = password };
            var json = JsonSerializer.Serialize(loginDto);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("/api/Auth/login", content);
            if (!response.IsSuccessStatusCode)
                return null;

            var responseJson = await response.Content.ReadAsStringAsync();
            var loginResponse = JsonSerializer.Deserialize<LoginResponseDto>(responseJson, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            if (loginResponse != null)
            {
                Token = loginResponse.Token;
                CurrentUser = loginResponse.User;
                _httpClient.DefaultRequestHeaders.Authorization =
                    new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", Token);
            }

            return loginResponse;
        }

        public async Task<KtforgevDataResult?> GetKtforgevDataAsync(
            int skip = 0,
            int take = 100,
            string? sortField = null,
            string? sortDirection = "asc",
            string? filterField = null,
            string? filterValue = null,
            string? filterOperator = "contains")
        {
            try
            {
                var queryParams = new List<string>();
                queryParams.Add($"skip={skip}");
                queryParams.Add($"take={take}");
                
                if (!string.IsNullOrEmpty(sortField))
                    queryParams.Add($"sortField={Uri.EscapeDataString(sortField)}");
                
                if (!string.IsNullOrEmpty(sortDirection))
                    queryParams.Add($"sortDirection={Uri.EscapeDataString(sortDirection)}");
                
                if (!string.IsNullOrEmpty(filterField))
                    queryParams.Add($"filterField={Uri.EscapeDataString(filterField)}");
                
                if (!string.IsNullOrEmpty(filterValue))
                    queryParams.Add($"filterValue={Uri.EscapeDataString(filterValue)}");
                
                if (!string.IsNullOrEmpty(filterOperator))
                    queryParams.Add($"filterOperator={Uri.EscapeDataString(filterOperator)}");

                var queryString = string.Join("&", queryParams);
                var response = await _httpClient.GetAsync($"/api/KtforgevWpf?{queryString}");
                
                if (!response.IsSuccessStatusCode)
                    return null;

                var json = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<KtforgevDataResult>(json, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                });

                return result;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public void Logout()
        {
            Token = null;
            CurrentUser = null;
            _httpClient.DefaultRequestHeaders.Authorization = null;
        }

        public void Dispose()
        {
            _httpClient.Dispose();
        }
    }

    public class KtforgevDataResult
    {
        public List<KtforgevDto> Data { get; set; } = new();
        public int TotalCount { get; set; }
        public int Skip { get; set; }
        public int Take { get; set; }
    }
}