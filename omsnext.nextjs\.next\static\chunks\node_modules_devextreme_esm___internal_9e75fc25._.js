(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/dispatcher.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/utils/macro_task_array/dispatcher.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "macroTaskIdSet": ()=>macroTaskIdSet
});
const macroTaskIdSet = new Set;
const schedule = async (callback, macroTaskTimeoutMs)=>new Promise((resolve)=>{
        const taskId = setTimeout(()=>{
            callback();
            macroTaskIdSet.delete(taskId);
            resolve();
        }, macroTaskTimeoutMs);
        macroTaskIdSet.add(taskId);
    });
const dispose = ()=>{
    Array.from(macroTaskIdSet).forEach((id)=>{
        clearTimeout(id);
        macroTaskIdSet.delete(id);
    });
};
const __TURBOPACK__default__export__ = {
    schedule: schedule,
    dispose: dispose
};
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/methods.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/utils/macro_task_array/methods.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "DEFAULT_MACRO_TASK_TIMEOUT": ()=>DEFAULT_MACRO_TASK_TIMEOUT,
    "DEFAULT_STEPS_VALUE": ()=>DEFAULT_STEPS_VALUE,
    "macroTaskArrayForEach": ()=>macroTaskArrayForEach,
    "macroTaskArrayMap": ()=>macroTaskArrayMap
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$dispatcher$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/dispatcher.js [app-client] (ecmascript)");
;
const DEFAULT_STEPS_VALUE = 100;
const DEFAULT_MACRO_TASK_TIMEOUT = 0;
const macroTaskArrayForEach = async function(array, callback) {
    let step = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 100;
    let macroTaskTimeoutMs = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0;
    const promises = [];
    const batchesCount = Math.ceil(array.length / step);
    for(let batchIdx = 0; batchIdx < batchesCount; batchIdx += 1){
        const scheduledTask = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$dispatcher$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].schedule(()=>{
            const startIdx = batchIdx * step;
            const maxIdx = startIdx + step;
            for(let idx = startIdx; idx < maxIdx && void 0 !== array[idx]; idx += 1){
                callback(array[idx]);
            }
        }, macroTaskTimeoutMs);
        promises.push(scheduledTask);
    }
    await Promise.all(promises);
};
const macroTaskArrayMap = async function(array, callback) {
    let step = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 100;
    let macroTaskTimeoutMs = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0;
    const result = [];
    await macroTaskArrayForEach(array, (item)=>{
        result.push(callback(item));
    }, step, macroTaskTimeoutMs);
    return result;
};
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/utils/macro_task_array/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$dispatcher$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/dispatcher.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$methods$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/methods.js [app-client] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = {
    forEach: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$methods$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["macroTaskArrayForEach"],
    map: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$methods$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["macroTaskArrayMap"],
    dispose: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$dispatcher$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dispose
};
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/utils/index.js [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/utils/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/index.js [app-client] (ecmascript)");
;
;
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/utils/index.js [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/index.js [app-client] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/index.js [app-client] (ecmascript) <export default as macroTaskArray>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "macroTaskArray": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/index.js [app-client] (ecmascript)");
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/m_date_adapter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/m_date_adapter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
;
const toMs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds;
class DateAdapterCore {
    get source() {
        return this._source;
    }
    result() {
        return this._source;
    }
    getTimezoneOffset() {
        let format = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : void 0;
        const value = this._source.getTimezoneOffset();
        if ("minute" === format) {
            return value * toMs("minute");
        }
        return value;
    }
    getTime() {
        return this._source.getTime();
    }
    setTime(value) {
        this._source.setTime(value);
        return this;
    }
    addTime(value) {
        this._source.setTime(this._source.getTime() + value);
        return this;
    }
    setMinutes(value) {
        this._source.setMinutes(value);
        return this;
    }
    addMinutes(value) {
        this._source.setMinutes(this._source.getMinutes() + value);
        return this;
    }
    subtractMinutes(value) {
        this._source.setMinutes(this._source.getMinutes() - value);
        return this;
    }
    constructor(source){
        this._source = new Date(source.getTime ? source.getTime() : source);
    }
}
const DateAdapter = (date)=>new DateAdapterCore(date);
const __TURBOPACK__default__export__ = DateAdapter;
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/timezones/m_utils_timezones_data.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/timezones/m_utils_timezones_data.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/config.js [app-client] (ecmascript)");
;
;
const getConvertedUntils = (value)=>value.split("|").map((until)=>{
        if ("Infinity" === until) {
            return null;
        }
        return 1e3 * parseInt(until, 36);
    });
const parseTimezone = (timeZoneConfig)=>{
    const { offsets: offsets } = timeZoneConfig;
    const { offsetIndices: offsetIndices } = timeZoneConfig;
    const { untils: untils } = timeZoneConfig;
    const offsetList = offsets.split("|").map((value)=>parseInt(value));
    const offsetIndexList = offsetIndices.split("").map((value)=>parseInt(value));
    const dateList = getConvertedUntils(untils).map((accumulator = 0, (value)=>accumulator += value));
    var accumulator;
    return {
        offsetList: offsetList,
        offsetIndexList: offsetIndexList,
        dateList: dateList
    };
};
class TimeZoneCache {
    tryGet(id) {
        if (!this.map.get(id)) {
            const config = timeZoneDataUtils.getTimezoneById(id);
            if (!config) {
                return false;
            }
            const timeZoneInfo = parseTimezone(config);
            this.map.set(id, timeZoneInfo);
        }
        return this.map.get(id);
    }
    constructor(){
        this.map = new Map;
    }
}
const tzCache = new TimeZoneCache;
const timeZoneDataUtils = {
    _tzCache: tzCache,
    getTimeZonesOld: ()=>{
        var _GlobalConfig_timezones;
        return (_GlobalConfig_timezones = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().timezones) !== null && _GlobalConfig_timezones !== void 0 ? _GlobalConfig_timezones : [];
    },
    formatOffset (offset) {
        const hours = Math.floor(offset);
        const minutesInDecimal = offset - hours;
        const signString = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sign"])(offset) >= 0 ? "+" : "-";
        const hoursString = "0".concat(Math.abs(hours)).slice(-2);
        const minutesString = minutesInDecimal > 0 ? ":" + 60 * minutesInDecimal : ":00";
        return signString + hoursString + minutesString;
    },
    formatId: (id)=>id.split("/").join(" - ").split("_").join(" "),
    getTimezoneById (id) {
        if (!id) {
            return;
        }
        const tzList = this.getTimeZonesOld();
        for(let i = 0; i < tzList.length; i++){
            const currentId = tzList[i].id;
            if (currentId === id) {
                return tzList[i];
            }
        }
        return;
    },
    getTimeZoneOffsetById (id, timestamp) {
        const timeZoneInfo = tzCache.tryGet(id);
        return timeZoneInfo ? this.getUtcOffset(timeZoneInfo, timestamp) : void 0;
    },
    getTimeZoneDeclarationTuple (id, year) {
        const timeZoneInfo = tzCache.tryGet(id);
        return timeZoneInfo ? this.getTimeZoneDeclarationTupleCore(timeZoneInfo, year) : [];
    },
    getTimeZoneDeclarationTupleCore (timeZoneInfo, year) {
        const { offsetList: offsetList } = timeZoneInfo;
        const { offsetIndexList: offsetIndexList } = timeZoneInfo;
        const { dateList: dateList } = timeZoneInfo;
        const tupleResult = [];
        for(let i = 0; i < dateList.length; i++){
            const currentDate = dateList[i];
            const currentYear = new Date(currentDate).getFullYear();
            if (currentYear === year) {
                const offset = offsetList[offsetIndexList[i + 1]];
                tupleResult.push({
                    date: currentDate,
                    offset: -offset / 60
                });
            }
            if (currentYear > year) {
                break;
            }
        }
        return tupleResult;
    },
    getUtcOffset (timeZoneInfo, dateTimeStamp) {
        const { offsetList: offsetList } = timeZoneInfo;
        const { offsetIndexList: offsetIndexList } = timeZoneInfo;
        const { dateList: dateList } = timeZoneInfo;
        const lastIntervalStartIndex = dateList.length - 1 - 1;
        let index = lastIntervalStartIndex;
        while(index >= 0 && dateTimeStamp < dateList[index]){
            index--;
        }
        const offset = offsetList[offsetIndexList[index + 1]];
        return -offset / 60 || offset;
    }
};
const __TURBOPACK__default__export__ = timeZoneDataUtils;
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/timezones/timezone_list.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/timezones/timezone_list.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {
    value: [
        "Etc/GMT+12",
        "Etc/GMT+11",
        "Pacific/Midway",
        "Pacific/Niue",
        "Pacific/Pago_Pago",
        "Pacific/Samoa",
        "US/Samoa",
        "Etc/GMT+10",
        "HST",
        "Pacific/Honolulu",
        "Pacific/Johnston",
        "Pacific/Rarotonga",
        "Pacific/Tahiti",
        "US/Hawaii",
        "Pacific/Marquesas",
        "America/Adak",
        "America/Atka",
        "Etc/GMT+9",
        "Pacific/Gambier",
        "US/Aleutian",
        "America/Anchorage",
        "America/Juneau",
        "America/Metlakatla",
        "America/Nome",
        "America/Sitka",
        "America/Yakutat",
        "Etc/GMT+8",
        "Pacific/Pitcairn",
        "US/Alaska",
        "America/Creston",
        "America/Dawson_Creek",
        "America/Dawson",
        "America/Ensenada",
        "America/Fort_Nelson",
        "America/Hermosillo",
        "America/Los_Angeles",
        "America/Phoenix",
        "America/Santa_Isabel",
        "America/Tijuana",
        "America/Vancouver",
        "America/Whitehorse",
        "Canada/Pacific",
        "Canada/Yukon",
        "Etc/GMT+7",
        "Mexico/BajaNorte",
        "MST",
        "PST8PDT",
        "US/Arizona",
        "US/Pacific",
        "America/Belize",
        "America/Boise",
        "America/Cambridge_Bay",
        "America/Chihuahua",
        "America/Costa_Rica",
        "America/Denver",
        "America/Edmonton",
        "America/El_Salvador",
        "America/Guatemala",
        "America/Inuvik",
        "America/Managua",
        "America/Mazatlan",
        "America/Monterrey",
        "America/Ojinaga",
        "America/Regina",
        "America/Shiprock",
        "America/Swift_Current",
        "America/Tegucigalpa",
        "America/Yellowknife",
        "Canada/Mountain",
        "Canada/Saskatchewan",
        "Chile/EasterIsland",
        "Etc/GMT+6",
        "Mexico/BajaSur",
        "MST7MDT",
        "Navajo",
        "Pacific/Easter",
        "Pacific/Galapagos",
        "US/Mountain",
        "America/Atikokan",
        "America/Bahia_Banderas",
        "America/Bogota",
        "America/Cancun",
        "America/Cayman",
        "America/Chicago",
        "America/Coral_Harbour",
        "America/Eirunepe",
        "America/Guayaquil",
        "America/Indiana/Knox",
        "America/Indiana/Tell_City",
        "America/Jamaica",
        "America/Knox_IN",
        "America/Lima",
        "America/Matamoros",
        "America/Menominee",
        "America/Merida",
        "America/Mexico_City",
        "America/North_Dakota/Beulah",
        "America/North_Dakota/Center",
        "America/North_Dakota/New_Salem",
        "America/Panama",
        "America/Porto_Acre",
        "America/Rainy_River",
        "America/Rankin_Inlet",
        "America/Resolute",
        "America/Rio_Branco",
        "America/Winnipeg",
        "Brazil/Acre",
        "Canada/Central",
        "CST6CDT",
        "EST",
        "Etc/GMT+5",
        "Jamaica",
        "Mexico/General",
        "US/Central",
        "US/Indiana-Starke",
        "America/Anguilla",
        "America/Antigua",
        "America/Aruba",
        "America/Asuncion",
        "America/Barbados",
        "America/Blanc-Sablon",
        "America/Boa_Vista",
        "America/Campo_Grande",
        "America/Caracas",
        "America/Cuiaba",
        "America/Curacao",
        "America/Detroit",
        "America/Dominica",
        "America/Fort_Wayne",
        "America/Grand_Turk",
        "America/Grenada",
        "America/Guadeloupe",
        "America/Guyana",
        "America/Havana",
        "America/Indiana/Indianapolis",
        "America/Indiana/Marengo",
        "America/Indiana/Petersburg",
        "America/Indiana/Vevay",
        "America/Indiana/Vincennes",
        "America/Indiana/Winamac",
        "America/Indianapolis",
        "America/Iqaluit",
        "America/Kentucky/Louisville",
        "America/Kentucky/Monticello",
        "America/Kralendijk",
        "America/La_Paz",
        "America/Louisville",
        "America/Lower_Princes",
        "America/Manaus",
        "America/Marigot",
        "America/Martinique",
        "America/Montreal",
        "America/Montserrat",
        "America/Nassau",
        "America/New_York",
        "America/Nipigon",
        "America/Pangnirtung",
        "America/Port_of_Spain",
        "America/Port-au-Prince",
        "America/Porto_Velho",
        "America/Puerto_Rico",
        "America/Santiago",
        "America/Santo_Domingo",
        "America/St_Barthelemy",
        "America/St_Kitts",
        "America/St_Lucia",
        "America/St_Thomas",
        "America/St_Vincent",
        "America/Thunder_Bay",
        "America/Toronto",
        "America/Tortola",
        "America/Virgin",
        "Brazil/West",
        "Canada/Eastern",
        "Chile/Continental",
        "Cuba",
        "EST5EDT",
        "Etc/GMT+4",
        "US/East-Indiana",
        "US/Eastern",
        "US/Michigan",
        "America/Araguaina",
        "America/Argentina/Buenos_Aires",
        "America/Argentina/Catamarca",
        "America/Argentina/ComodRivadavia",
        "America/Argentina/Cordoba",
        "America/Argentina/Jujuy",
        "America/Argentina/La_Rioja",
        "America/Argentina/Mendoza",
        "America/Argentina/Rio_Gallegos",
        "America/Argentina/Salta",
        "America/Argentina/San_Juan",
        "America/Argentina/San_Luis",
        "America/Argentina/Tucuman",
        "America/Argentina/Ushuaia",
        "America/Bahia",
        "America/Belem",
        "America/Buenos_Aires",
        "America/Catamarca",
        "America/Cayenne",
        "America/Cordoba",
        "America/Fortaleza",
        "America/Glace_Bay",
        "America/Goose_Bay",
        "America/Halifax",
        "America/Jujuy",
        "America/Maceio",
        "America/Mendoza",
        "America/Moncton",
        "America/Montevideo",
        "America/Paramaribo",
        "America/Punta_Arenas",
        "America/Recife",
        "America/Rosario",
        "America/Santarem",
        "America/Sao_Paulo",
        "America/Thule",
        "Antarctica/Palmer",
        "Antarctica/Rothera",
        "Atlantic/Bermuda",
        "Atlantic/Stanley",
        "Brazil/East",
        "Canada/Atlantic",
        "Etc/GMT+3",
        "America/St_Johns",
        "Canada/Newfoundland",
        "America/Godthab",
        "America/Miquelon",
        "America/Noronha",
        "America/Nuuk",
        "Atlantic/South_Georgia",
        "Brazil/DeNoronha",
        "Etc/GMT+2",
        "Atlantic/Cape_Verde",
        "Etc/GMT+1",
        "Africa/Abidjan",
        "Africa/Accra",
        "Africa/Bamako",
        "Africa/Banjul",
        "Africa/Bissau",
        "Africa/Conakry",
        "Africa/Dakar",
        "Africa/Freetown",
        "Africa/Lome",
        "Africa/Monrovia",
        "Africa/Nouakchott",
        "Africa/Ouagadougou",
        "Africa/Sao_Tome",
        "Africa/Timbuktu",
        "America/Danmarkshavn",
        "America/Scoresbysund",
        "Atlantic/Azores",
        "Atlantic/Reykjavik",
        "Atlantic/St_Helena",
        "Etc/GMT-0",
        "Etc/GMT",
        "Etc/GMT+0",
        "Etc/GMT0",
        "Etc/Greenwich",
        "Etc/UCT",
        "Etc/Universal",
        "Etc/UTC",
        "Etc/Zulu",
        "GMT-0",
        "GMT",
        "GMT+0",
        "GMT0",
        "Greenwich",
        "Iceland",
        "UCT",
        "Universal",
        "UTC",
        "Zulu",
        "Africa/Algiers",
        "Africa/Bangui",
        "Africa/Brazzaville",
        "Africa/Casablanca",
        "Africa/Douala",
        "Africa/El_Aaiun",
        "Africa/Kinshasa",
        "Africa/Lagos",
        "Africa/Libreville",
        "Africa/Luanda",
        "Africa/Malabo",
        "Africa/Ndjamena",
        "Africa/Niamey",
        "Africa/Porto-Novo",
        "Africa/Tunis",
        "Atlantic/Canary",
        "Atlantic/Faeroe",
        "Atlantic/Faroe",
        "Atlantic/Madeira",
        "Eire",
        "Etc/GMT-1",
        "Europe/Belfast",
        "Europe/Dublin",
        "Europe/Guernsey",
        "Europe/Isle_of_Man",
        "Europe/Jersey",
        "Europe/Lisbon",
        "Europe/London",
        "GB-Eire",
        "GB",
        "Portugal",
        "WET",
        "Africa/Blantyre",
        "Africa/Bujumbura",
        "Africa/Cairo",
        "Africa/Ceuta",
        "Africa/Gaborone",
        "Africa/Harare",
        "Africa/Johannesburg",
        "Africa/Khartoum",
        "Africa/Kigali",
        "Africa/Lubumbashi",
        "Africa/Lusaka",
        "Africa/Maputo",
        "Africa/Maseru",
        "Africa/Mbabane",
        "Africa/Tripoli",
        "Africa/Windhoek",
        "Antarctica/Troll",
        "Arctic/Longyearbyen",
        "Atlantic/Jan_Mayen",
        "CET",
        "Egypt",
        "Etc/GMT-2",
        "Europe/Amsterdam",
        "Europe/Andorra",
        "Europe/Belgrade",
        "Europe/Berlin",
        "Europe/Bratislava",
        "Europe/Brussels",
        "Europe/Budapest",
        "Europe/Busingen",
        "Europe/Copenhagen",
        "Europe/Gibraltar",
        "Europe/Kaliningrad",
        "Europe/Ljubljana",
        "Europe/Luxembourg",
        "Europe/Madrid",
        "Europe/Malta",
        "Europe/Monaco",
        "Europe/Oslo",
        "Europe/Paris",
        "Europe/Podgorica",
        "Europe/Prague",
        "Europe/Rome",
        "Europe/San_Marino",
        "Europe/Sarajevo",
        "Europe/Skopje",
        "Europe/Stockholm",
        "Europe/Tirane",
        "Europe/Vaduz",
        "Europe/Vatican",
        "Europe/Vienna",
        "Europe/Warsaw",
        "Europe/Zagreb",
        "Europe/Zurich",
        "Libya",
        "MET",
        "Poland",
        "Africa/Addis_Ababa",
        "Africa/Asmara",
        "Africa/Asmera",
        "Africa/Dar_es_Salaam",
        "Africa/Djibouti",
        "Africa/Juba",
        "Africa/Kampala",
        "Africa/Mogadishu",
        "Africa/Nairobi",
        "Antarctica/Syowa",
        "Asia/Aden",
        "Asia/Amman",
        "Asia/Baghdad",
        "Asia/Bahrain",
        "Asia/Beirut",
        "Asia/Damascus",
        "Asia/Famagusta",
        "Asia/Gaza",
        "Asia/Hebron",
        "Asia/Istanbul",
        "Asia/Jerusalem",
        "Asia/Kuwait",
        "Asia/Nicosia",
        "Asia/Qatar",
        "Asia/Riyadh",
        "Asia/Tel_Aviv",
        "EET",
        "Etc/GMT-3",
        "Europe/Athens",
        "Europe/Bucharest",
        "Europe/Chisinau",
        "Europe/Helsinki",
        "Europe/Istanbul",
        "Europe/Kiev",
        "Europe/Kirov",
        "Europe/Mariehamn",
        "Europe/Minsk",
        "Europe/Moscow",
        "Europe/Nicosia",
        "Europe/Riga",
        "Europe/Simferopol",
        "Europe/Sofia",
        "Europe/Tallinn",
        "Europe/Tiraspol",
        "Europe/Uzhgorod",
        "Europe/Vilnius",
        "Europe/Zaporozhye",
        "Indian/Antananarivo",
        "Indian/Comoro",
        "Indian/Mayotte",
        "Israel",
        "Turkey",
        "W-SU",
        "Asia/Baku",
        "Asia/Dubai",
        "Asia/Muscat",
        "Asia/Tbilisi",
        "Asia/Yerevan",
        "Etc/GMT-4",
        "Europe/Astrakhan",
        "Europe/Samara",
        "Europe/Saratov",
        "Europe/Ulyanovsk",
        "Europe/Volgograd",
        "Indian/Mahe",
        "Indian/Mauritius",
        "Indian/Reunion",
        "Asia/Kabul",
        "Asia/Tehran",
        "Iran",
        "Antarctica/Mawson",
        "Asia/Aqtau",
        "Asia/Aqtobe",
        "Asia/Ashgabat",
        "Asia/Ashkhabad",
        "Asia/Atyrau",
        "Asia/Dushanbe",
        "Asia/Karachi",
        "Asia/Oral",
        "Asia/Qyzylorda",
        "Asia/Samarkand",
        "Asia/Tashkent",
        "Asia/Yekaterinburg",
        "Etc/GMT-5",
        "Indian/Kerguelen",
        "Indian/Maldives",
        "Asia/Calcutta",
        "Asia/Colombo",
        "Asia/Kolkata",
        "Asia/Kathmandu",
        "Asia/Katmandu",
        "Antarctica/Vostok",
        "Asia/Almaty",
        "Asia/Bishkek",
        "Asia/Dacca",
        "Asia/Dhaka",
        "Asia/Kashgar",
        "Asia/Omsk",
        "Asia/Qostanay",
        "Asia/Thimbu",
        "Asia/Thimphu",
        "Asia/Urumqi",
        "Etc/GMT-6",
        "Indian/Chagos",
        "Asia/Rangoon",
        "Asia/Yangon",
        "Indian/Cocos",
        "Antarctica/Davis",
        "Asia/Bangkok",
        "Asia/Barnaul",
        "Asia/Ho_Chi_Minh",
        "Asia/Hovd",
        "Asia/Jakarta",
        "Asia/Krasnoyarsk",
        "Asia/Novokuznetsk",
        "Asia/Novosibirsk",
        "Asia/Phnom_Penh",
        "Asia/Pontianak",
        "Asia/Saigon",
        "Asia/Tomsk",
        "Asia/Vientiane",
        "Etc/GMT-7",
        "Indian/Christmas",
        "Antarctica/Casey",
        "Asia/Brunei",
        "Asia/Choibalsan",
        "Asia/Chongqing",
        "Asia/Chungking",
        "Asia/Harbin",
        "Asia/Hong_Kong",
        "Asia/Irkutsk",
        "Asia/Kuala_Lumpur",
        "Asia/Kuching",
        "Asia/Macao",
        "Asia/Macau",
        "Asia/Makassar",
        "Asia/Manila",
        "Asia/Shanghai",
        "Asia/Singapore",
        "Asia/Taipei",
        "Asia/Ujung_Pandang",
        "Asia/Ulaanbaatar",
        "Asia/Ulan_Bator",
        "Australia/Perth",
        "Australia/West",
        "Etc/GMT-8",
        "Hongkong",
        "PRC",
        "ROC",
        "Singapore",
        "Australia/Eucla",
        "Asia/Chita",
        "Asia/Dili",
        "Asia/Jayapura",
        "Asia/Khandyga",
        "Asia/Pyongyang",
        "Asia/Seoul",
        "Asia/Tokyo",
        "Asia/Yakutsk",
        "Etc/GMT-9",
        "Japan",
        "Pacific/Palau",
        "ROK",
        "Australia/Adelaide",
        "Australia/Broken_Hill",
        "Australia/Darwin",
        "Australia/North",
        "Australia/South",
        "Australia/Yancowinna",
        "Antarctica/DumontDUrville",
        "Asia/Ust-Nera",
        "Asia/Vladivostok",
        "Australia/ACT",
        "Australia/Brisbane",
        "Australia/Canberra",
        "Australia/Currie",
        "Australia/Hobart",
        "Australia/Lindeman",
        "Australia/Melbourne",
        "Australia/NSW",
        "Australia/Queensland",
        "Australia/Sydney",
        "Australia/Tasmania",
        "Australia/Victoria",
        "Etc/GMT-10",
        "Pacific/Chuuk",
        "Pacific/Guam",
        "Pacific/Port_Moresby",
        "Pacific/Saipan",
        "Pacific/Truk",
        "Pacific/Yap",
        "Australia/LHI",
        "Australia/Lord_Howe",
        "Antarctica/Macquarie",
        "Asia/Magadan",
        "Asia/Sakhalin",
        "Asia/Srednekolymsk",
        "Etc/GMT-11",
        "Pacific/Bougainville",
        "Pacific/Efate",
        "Pacific/Guadalcanal",
        "Pacific/Kosrae",
        "Pacific/Norfolk",
        "Pacific/Noumea",
        "Pacific/Pohnpei",
        "Pacific/Ponape",
        "Antarctica/McMurdo",
        "Antarctica/South_Pole",
        "Asia/Anadyr",
        "Asia/Kamchatka",
        "Etc/GMT-12",
        "Kwajalein",
        "NZ",
        "Pacific/Auckland",
        "Pacific/Fiji",
        "Pacific/Funafuti",
        "Pacific/Kwajalein",
        "Pacific/Majuro",
        "Pacific/Nauru",
        "Pacific/Tarawa",
        "Pacific/Wake",
        "Pacific/Wallis",
        "NZ-CHAT",
        "Pacific/Chatham",
        "Etc/GMT-13",
        "Pacific/Apia",
        "Pacific/Enderbury",
        "Pacific/Fakaofo",
        "Pacific/Tongatapu",
        "Etc/GMT-14",
        "Pacific/Kiritimati"
    ]
};
}),
"[project]/node_modules/devextreme/esm/__internal/scheduler/m_utils_time_zone.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/scheduler/m_utils_time_zone.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__macroTaskArray$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/utils/macro_task_array/index.js [app-client] (ecmascript) <export default as macroTaskArray>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$m_date_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/m_date_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$m_utils_timezones_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/timezones/m_utils_timezones_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$timezone_list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/scheduler/timezones/timezone_list.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const toMs = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds;
const MINUTES_IN_HOUR = 60;
const MS_IN_MINUTE = 6e4;
const GMT = "GMT";
const offsetFormatRegexp = /^GMT(?:[+-]\d{2}:\d{2})?$/;
const createUTCDateWithLocalOffset = (date)=>{
    if (!date) {
        return null;
    }
    return new Date(Date.UTC(date.getFullYear(), date.getMonth(), date.getDate(), date.getHours(), date.getMinutes(), date.getSeconds()));
};
const createDateFromUTCWithLocalOffset = (date)=>{
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$m_date_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(date);
    const timezoneOffsetBeforeInMin = result.getTimezoneOffset();
    result.addTime(result.getTimezoneOffset("minute"));
    result.subtractMinutes(timezoneOffsetBeforeInMin - result.getTimezoneOffset());
    return result.source;
};
const createUTCDate = (date)=>new Date(Date.UTC(date.getUTCFullYear(), date.getUTCMonth(), date.getUTCDate(), date.getUTCHours(), date.getUTCMinutes()));
const getTimezoneOffsetChangeInMinutes = (startDate, endDate, updatedStartDate, updatedEndDate)=>getDaylightOffset(updatedStartDate, updatedEndDate) - getDaylightOffset(startDate, endDate);
const getTimezoneOffsetChangeInMs = (startDate, endDate, updatedStartDate, updatedEndDate)=>getTimezoneOffsetChangeInMinutes(startDate, endDate, updatedStartDate, updatedEndDate) * toMs("minute");
const getDaylightOffset = (startDate, endDate)=>new Date(startDate).getTimezoneOffset() - new Date(endDate).getTimezoneOffset();
const getDaylightOffsetInMs = (startDate, endDate)=>getDaylightOffset(startDate, endDate) * toMs("minute");
const calculateTimezoneByValueOld = function(timezone) {
    let date = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    const customTimezones = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$m_utils_timezones_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTimeZonesOld();
    if (0 === customTimezones.length) {
        return;
    }
    const dateUtc = createUTCDate(date);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$m_utils_timezones_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTimeZoneOffsetById(timezone, dateUtc.getTime());
};
const calculateTimezoneByValueCore = function(timeZone) {
    let date = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    const offset = getStringOffset(timeZone, date);
    if (void 0 === offset) {
        return;
    }
    if (offset === GMT) {
        return 0;
    }
    const isMinus = "-" === offset.substring(3, 4);
    const hours = offset.substring(4, 6);
    const minutes = offset.substring(7, 9);
    const result = parseInt(hours, 10) + parseInt(minutes, 10) / 60;
    return isMinus ? -result : result;
};
const calculateTimezoneByValue = function(timeZone) {
    let date = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    if (!timeZone) {
        return;
    }
    const isValidTimezone = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$timezone_list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].value.includes(timeZone);
    if (!isValidTimezone) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].log("W0009", timeZone);
        return;
    }
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dateUtilsTs"].isValidDate(date)) {
        return;
    }
    let result = calculateTimezoneByValueOld(timeZone, date);
    if (void 0 === result) {
        result = calculateTimezoneByValueCore(timeZone, date);
    }
    return result;
};
const getStringOffset = function(timeZone) {
    let date = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    let result = "";
    try {
        var _dateTimeFormat$forma;
        const dateTimeFormat = new Intl.DateTimeFormat("en-US", {
            timeZone: timeZone,
            timeZoneName: "longOffset"
        });
        var _ref;
        result = (_ref = null === (_dateTimeFormat$forma = dateTimeFormat.formatToParts(date).find((_ref)=>{
            let { type: type } = _ref;
            return "timeZoneName" === type;
        })) || void 0 === _dateTimeFormat$forma ? void 0 : _dateTimeFormat$forma.value) !== null && _ref !== void 0 ? _ref : "";
    } catch (e) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].log("W0009", timeZone);
        return;
    }
    const isSupportedFormat = offsetFormatRegexp.test(result);
    if (!isSupportedFormat) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].log("W0009", timeZone);
        return;
    }
    return result;
};
const getOffsetNamePart = (offset)=>{
    if (offset === GMT) {
        return "".concat(offset, " +00:00");
    }
    return offset.replace(GMT, "".concat(GMT, " "));
};
const getTimezoneTitle = function(timeZone) {
    let date = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dateUtilsTs"].isValidDate(date)) {
        return "";
    }
    const tzNamePart = timeZone.replace(/\//g, " - ").replace(/_/g, " ");
    const offset = getStringOffset(timeZone, date);
    if (void 0 === offset) {
        return;
    }
    const offsetNamePart = getOffsetNamePart(offset);
    return "(".concat(offsetNamePart, ") ").concat(tzNamePart);
};
const _getDaylightOffsetByTimezone = (startDate, endDate, timeZone)=>{
    const startDayOffset = calculateTimezoneByValue(timeZone, startDate);
    const endDayOffset = calculateTimezoneByValue(timeZone, endDate);
    if (void 0 === startDayOffset || void 0 === endDayOffset) {
        return 0;
    }
    return startDayOffset - endDayOffset;
};
const getCorrectedDateByDaylightOffsets = (convertedOriginalStartDate, convertedDate, date, timeZone, startDateTimezone)=>{
    const daylightOffsetByCommonTimezone = _getDaylightOffsetByTimezone(convertedOriginalStartDate, convertedDate, timeZone);
    const daylightOffsetByAppointmentTimezone = _getDaylightOffsetByTimezone(convertedOriginalStartDate, convertedDate, startDateTimezone);
    const diff = daylightOffsetByCommonTimezone - daylightOffsetByAppointmentTimezone;
    return new Date(date.getTime() - diff * toMs("hour"));
};
const correctRecurrenceExceptionByTimezone = function(exception, exceptionByStartDate, timeZone, startDateTimeZone) {
    let isBackConversion = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : false;
    let timezoneOffset = (exception.getTimezoneOffset() - exceptionByStartDate.getTimezoneOffset()) / 60;
    if (startDateTimeZone) {
        timezoneOffset = _getDaylightOffsetByTimezone(exceptionByStartDate, exception, startDateTimeZone);
    } else if (timeZone) {
        timezoneOffset = _getDaylightOffsetByTimezone(exceptionByStartDate, exception, timeZone);
    }
    return new Date(exception.getTime() + (isBackConversion ? -1 : 1) * timezoneOffset * toMs("hour"));
};
const isTimezoneChangeInDate = (date)=>{
    const startDayDate = new Date(new Date(date).setHours(0, 0, 0, 0));
    const endDayDate = new Date(new Date(date).setHours(23, 59, 59, 0));
    return startDayDate.getTimezoneOffset() - endDayDate.getTimezoneOffset() !== 0;
};
const getDateWithoutTimezoneChange = (date)=>{
    const clonedDate = new Date(date);
    if (isTimezoneChangeInDate(clonedDate)) {
        const result = new Date(clonedDate);
        return new Date(result.setDate(result.getDate() + 1));
    }
    return clonedDate;
};
const isSameAppointmentDates = (startDate, endDate)=>{
    endDate = new Date(endDate.getTime() - 1);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].sameDate(startDate, endDate);
};
const getClientTimezoneOffset = function() {
    let date = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : new Date;
    return 6e4 * date.getTimezoneOffset();
};
const getDiffBetweenClientTimezoneOffsets = function() {
    let firstDate = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : new Date;
    let secondDate = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    return getClientTimezoneOffset(firstDate) - getClientTimezoneOffset(secondDate);
};
const isEqualLocalTimeZone = function(timeZoneName) {
    let date = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : new Date;
    if (Intl) {
        const localTimeZoneName = Intl.DateTimeFormat().resolvedOptions().timeZone;
        if (localTimeZoneName === timeZoneName) {
            return true;
        }
    }
    return isEqualLocalTimeZoneByDeclaration(timeZoneName, date);
};
const hasDSTInLocalTimeZone = ()=>{
    const [startDate, endDate] = getExtremeDates();
    return startDate.getTimezoneOffset() !== endDate.getTimezoneOffset();
};
const getOffset = (date)=>-date.getTimezoneOffset() / 60;
const getDateAndMoveHourBack = (dateStamp)=>new Date(dateStamp - toMs("hour"));
const isEqualLocalTimeZoneByDeclarationOld = (timeZoneName, date)=>{
    const year = date.getFullYear();
    const configTuple = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$m_utils_timezones_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTimeZoneDeclarationTuple(timeZoneName, year);
    const [summerTime, winterTime] = configTuple;
    const noDSTInTargetTimeZone = configTuple.length < 2;
    if (noDSTInTargetTimeZone) {
        const targetTimeZoneOffset = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$m_utils_timezones_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTimeZoneOffsetById(timeZoneName, date);
        const localTimeZoneOffset = getOffset(date);
        if (targetTimeZoneOffset !== localTimeZoneOffset) {
            return false;
        }
        return !hasDSTInLocalTimeZone();
    }
    const localSummerOffset = getOffset(new Date(summerTime.date));
    const localWinterOffset = getOffset(new Date(winterTime.date));
    if (localSummerOffset !== summerTime.offset) {
        return false;
    }
    if (localSummerOffset === getOffset(getDateAndMoveHourBack(summerTime.date))) {
        return false;
    }
    if (localWinterOffset !== winterTime.offset) {
        return false;
    }
    if (localWinterOffset === getOffset(getDateAndMoveHourBack(winterTime.date))) {
        return false;
    }
    return true;
};
const isEqualLocalTimeZoneByDeclaration = (timeZoneName, date)=>{
    const customTimezones = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$m_utils_timezones_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTimeZonesOld();
    const targetTimezoneData = customTimezones.filter((tz)=>tz.id === timeZoneName);
    if (1 === targetTimezoneData.length) {
        return isEqualLocalTimeZoneByDeclarationOld(timeZoneName, date);
    }
    return false;
};
const getExtremeDates = ()=>{
    const nowDate = new Date(Date.now());
    const startDate = new Date;
    const endDate = new Date;
    startDate.setFullYear(nowDate.getFullYear(), 0, 1);
    endDate.setFullYear(nowDate.getFullYear(), 6, 1);
    return [
        startDate,
        endDate
    ];
};
const setOffsetsToDate = (targetDate, offsetsArray)=>{
    const newDateMs = offsetsArray.reduce((result, offset)=>result + offset, targetDate.getTime());
    return new Date(newDateMs);
};
const addOffsetsWithoutDST = function(date) {
    for(var _len = arguments.length, offsets = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++){
        offsets[_key - 1] = arguments[_key];
    }
    const newDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dateUtilsTs"].addOffsets(date, offsets);
    const daylightShift = getDaylightOffsetInMs(date, newDate);
    if (!daylightShift) {
        return newDate;
    }
    const correctLocalDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dateUtilsTs"].addOffsets(newDate, [
        -daylightShift
    ]);
    const daylightSecondShift = getDaylightOffsetInMs(newDate, correctLocalDate);
    return !daylightSecondShift ? correctLocalDate : newDate;
};
const getTimeZones = function() {
    let date = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : new Date;
    let timeZones = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$timezone_list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].value;
    return timeZones.map((timezoneId)=>({
            id: timezoneId,
            title: getTimezoneTitle(timezoneId, date),
            offset: calculateTimezoneByValue(timezoneId, date)
        }));
};
const GET_TIMEZONES_BATCH_SIZE = 10;
let timeZoneDataCache = [];
let timeZoneDataCachePromise;
const cacheTimeZones = async function() {
    let date = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : new Date;
    if (timeZoneDataCachePromise) {
        return timeZoneDataCachePromise;
    }
    timeZoneDataCachePromise = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$utils$2f$macro_task_array$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__macroTaskArray$3e$__["macroTaskArray"].map(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$scheduler$2f$timezones$2f$timezone_list$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].value, (timezoneId)=>({
            id: timezoneId,
            title: getTimezoneTitle(timezoneId, date)
        }), 10);
    timeZoneDataCache = await timeZoneDataCachePromise;
    return timeZoneDataCache;
};
const getTimeZonesCache = ()=>timeZoneDataCache;
const utils = {
    getDaylightOffset: getDaylightOffset,
    getDaylightOffsetInMs: getDaylightOffsetInMs,
    getTimezoneOffsetChangeInMinutes: getTimezoneOffsetChangeInMinutes,
    getTimezoneOffsetChangeInMs: getTimezoneOffsetChangeInMs,
    calculateTimezoneByValue: calculateTimezoneByValue,
    getCorrectedDateByDaylightOffsets: getCorrectedDateByDaylightOffsets,
    isSameAppointmentDates: isSameAppointmentDates,
    correctRecurrenceExceptionByTimezone: correctRecurrenceExceptionByTimezone,
    getClientTimezoneOffset: getClientTimezoneOffset,
    getDiffBetweenClientTimezoneOffsets: getDiffBetweenClientTimezoneOffsets,
    createUTCDateWithLocalOffset: createUTCDateWithLocalOffset,
    createDateFromUTCWithLocalOffset: createDateFromUTCWithLocalOffset,
    createUTCDate: createUTCDate,
    isTimezoneChangeInDate: isTimezoneChangeInDate,
    getDateWithoutTimezoneChange: getDateWithoutTimezoneChange,
    hasDSTInLocalTimeZone: hasDSTInLocalTimeZone,
    isEqualLocalTimeZone: isEqualLocalTimeZone,
    isEqualLocalTimeZoneByDeclaration: isEqualLocalTimeZoneByDeclaration,
    setOffsetsToDate: setOffsetsToDate,
    addOffsetsWithoutDST: addOffsetsWithoutDST,
    getTimeZones: getTimeZones,
    getTimeZonesCache: getTimeZonesCache,
    cacheTimeZones: cacheTimeZones
};
const __TURBOPACK__default__export__ = utils;
}),
"[project]/node_modules/devextreme/esm/__internal/utils/toMilliseconds.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/utils/toMilliseconds.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "toMilliseconds": ()=>toMilliseconds
});
const timeIntervals = {
    millisecond: 1,
    second: 1e3,
    minute: 6e4,
    hour: 36e5,
    day: 864e5,
    week: 6048e5,
    month: 2592e6,
    quarter: 7776e6,
    year: 31536e6
};
function toMilliseconds(value) {
    return timeIntervals[value];
}
}),
"[project]/node_modules/devextreme/esm/__internal/utils/version.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/utils/version.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "assertDevExtremeVersion": ()=>assertDevExtremeVersion,
    "assertedVersionsCompatible": ()=>assertedVersionsCompatible,
    "clearAssertedVersions": ()=>clearAssertedVersions,
    "getPreviousMajorVersion": ()=>getPreviousMajorVersion,
    "parseVersion": ()=>parseVersion,
    "stringifyVersion": ()=>stringifyVersion
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
;
const MAX_MINOR_VERSION = 2;
const MIN_MINOR_VERSION = 1;
const assertedVersions = [];
const VERSION_SPLITTER = ".";
function stringifyVersion(version) {
    const { major: major, minor: minor, patch: patch } = version;
    return [
        major,
        minor,
        patch
    ].join(".");
}
function parseVersion(version) {
    const [major, minor, patch] = version.split(".").map(Number);
    return {
        major: major,
        minor: minor,
        patch: patch
    };
}
function assertDevExtremeVersion(packageName, version) {
    assertedVersions.push({
        packageName: packageName,
        version: version
    });
}
function clearAssertedVersions() {}
function stringifyVersionList(assertedVersionList) {
    return assertedVersionList.map((assertedVersion)=>"".concat(assertedVersion.packageName, ": ").concat(assertedVersion.version)).join("\n");
}
function versionsEqual(versionA, versionB) {
    return versionA.major === versionB.major && versionA.minor === versionB.minor && versionA.patch === versionB.patch;
}
function getPreviousMajorVersion(_ref) {
    let { major: major, minor: minor, patch: patch } = _ref;
    const previousMajorVersion = 1 === minor ? {
        major: major - 1,
        minor: 2,
        patch: patch
    } : {
        major: major,
        minor: minor - 1,
        patch: patch
    };
    return previousMajorVersion;
}
function assertedVersionsCompatible(currentVersion) {
    const mismatchingVersions = assertedVersions.filter((assertedVersion)=>!versionsEqual(parseVersion(assertedVersion.version), currentVersion));
    if (mismatchingVersions.length) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].log("W0023", stringifyVersionList([
            {
                packageName: "devextreme",
                version: stringifyVersion(currentVersion)
            },
            ...mismatchingVersions
        ]));
        return false;
    }
    return true;
}
}),
"[project]/node_modules/devextreme/esm/__internal/utils/type_conversion.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/utils/type_conversion.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "toNumber": ()=>toNumber
});
function toNumber(attribute) {
    return attribute ? Number(attribute.replace("px", "")) : 0;
}
}),
"[project]/node_modules/devextreme/esm/__internal/utils/memoize.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/utils/memoize.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "memoize": ()=>memoize
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
const compareByReference = (args, lastArgs)=>args.length === lastArgs.length && !Object.keys(args).some((key)=>args[key] !== lastArgs[key]);
const compareByValue = (args, lastArgs)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["equalByValue"])(args, lastArgs, {
        maxDepth: 4
    });
const createCacheFunc = (firstArgs, firstResult, originFunc, compareFunc)=>{
    let lastArgs = firstArgs;
    let lastResult = firstResult;
    return function() {
        for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
            args[_key] = arguments[_key];
        }
        const argsEquals = compareFunc(args, lastArgs);
        if (argsEquals) {
            return lastResult;
        }
        lastArgs = args;
        lastResult = originFunc(...lastArgs);
        return lastResult;
    };
};
const MEMOIZE_DEFAULT_OPTIONS = {
    compareType: "reference"
};
const memoize = function(func) {
    let { compareType: compareType } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : MEMOIZE_DEFAULT_OPTIONS;
    let cachedFunc = null;
    return function() {
        for(var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++){
            args[_key2] = arguments[_key2];
        }
        if (!cachedFunc) {
            const firstResult = func(...args);
            cachedFunc = createCacheFunc(args, firstResult, func, "reference" === compareType ? compareByReference : compareByValue);
            return firstResult;
        }
        return cachedFunc(...args);
    };
};
}),
"[project]/node_modules/devextreme/esm/__internal/filter_builder/m_between.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/filter_builder/m_between.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getConfig": ()=>getConfig
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
;
const FILTER_BUILDER_RANGE_CLASS = "dx-filterbuilder-range";
const FILTER_BUILDER_RANGE_START_CLASS = "dx-filterbuilder-range-start";
const FILTER_BUILDER_RANGE_END_CLASS = "dx-filterbuilder-range-end";
const FILTER_BUILDER_RANGE_SEPARATOR_CLASS = "dx-filterbuilder-range-separator";
const SEPARATOR = "\u2013";
function editorTemplate(conditionInfo, container) {
    const $editorStart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-range-start");
    const $editorEnd = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-range-end");
    let values = conditionInfo.value || [];
    const getStartValue = function(values) {
        return values && values.length > 0 ? values[0] : null;
    };
    const getEndValue = function(values) {
        return values && 2 === values.length ? values[1] : null;
    };
    container.append($editorStart);
    container.append((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<span>").addClass("dx-filterbuilder-range-separator").text("\u2013"));
    container.append($editorEnd);
    container.addClass("dx-filterbuilder-range");
    this._editorFactory.createEditor.call(this, $editorStart, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, conditionInfo.field, conditionInfo, {
        value: getStartValue(values),
        parentType: "filterBuilder",
        setValue (value) {
            values = [
                value,
                getEndValue(values)
            ];
            conditionInfo.setValue(values);
        }
    }));
    this._editorFactory.createEditor.call(this, $editorEnd, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, conditionInfo.field, conditionInfo, {
        value: getEndValue(values),
        parentType: "filterBuilder",
        setValue (value) {
            values = [
                getStartValue(values),
                value
            ];
            conditionInfo.setValue(values);
        }
    }));
}
function getConfig(caption, context) {
    return {
        name: "between",
        caption: caption,
        icon: "range",
        valueSeparator: "\u2013",
        dataTypes: [
            "number",
            "date",
            "datetime"
        ],
        editorTemplate: editorTemplate.bind(context),
        notForLookup: true
    };
}
}),
"[project]/node_modules/devextreme/esm/__internal/filter_builder/m_filter_operations_dictionary.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/filter_builder/m_filter_operations_dictionary.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const OPERATION_ICONS = {
    "=": "equal",
    "<>": "notequal",
    "<": "less",
    "<=": "lessorequal",
    ">": "greater",
    ">=": "greaterorequal",
    notcontains: "doesnotcontain",
    contains: "contains",
    startswith: "startswith",
    endswith: "endswith",
    isblank: "isblank",
    isnotblank: "isnotblank"
};
const OPERATION_NAME = {
    "=": "equal",
    "<>": "notEqual",
    "<": "lessThan",
    "<=": "lessThanOrEqual",
    ">": "greaterThan",
    ">=": "greaterThanOrEqual",
    startswith: "startsWith",
    contains: "contains",
    notcontains: "notContains",
    endswith: "endsWith",
    isblank: "isBlank",
    isnotblank: "isNotBlank",
    between: "between"
};
const __TURBOPACK__default__export__ = {
    getIconByFilterOperation: (filterOperation)=>OPERATION_ICONS[filterOperation],
    getNameByFilterOperation: (filterOperation)=>OPERATION_NAME[filterOperation]
};
}),
"[project]/node_modules/devextreme/esm/__internal/filter_builder/m_utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/filter_builder/m_utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "addItem": ()=>addItem,
    "convertToInnerStructure": ()=>convertToInnerStructure,
    "createCondition": ()=>createCondition,
    "createEmptyGroup": ()=>createEmptyGroup,
    "filterHasField": ()=>filterHasField,
    "getAvailableOperations": ()=>getAvailableOperations,
    "getCaptionByOperation": ()=>getCaptionByOperation,
    "getCaptionWithParents": ()=>getCaptionWithParents,
    "getCurrentLookupValueText": ()=>getCurrentLookupValueText,
    "getCurrentValueText": ()=>getCurrentValueText,
    "getCustomOperation": ()=>getCustomOperation,
    "getDefaultOperation": ()=>getDefaultOperation,
    "getField": ()=>getField,
    "getFilterExpression": ()=>getFilterExpression,
    "getFilterOperations": ()=>getFilterOperations,
    "getGroupCriteria": ()=>getGroupCriteria,
    "getGroupMenuItem": ()=>getGroupMenuItem,
    "getGroupValue": ()=>getGroupValue,
    "getItems": ()=>getItems,
    "getMatchedConditions": ()=>getMatchedConditions,
    "getMergedOperations": ()=>getMergedOperations,
    "getNormalizedFields": ()=>getNormalizedFields,
    "getNormalizedFilter": ()=>getNormalizedFilter,
    "getOperationFromAvailable": ()=>getOperationFromAvailable,
    "getOperationValue": ()=>getOperationValue,
    "isCondition": ()=>isCondition,
    "isEmptyGroup": ()=>isEmptyGroup,
    "isGroup": ()=>isGroup,
    "isValidCondition": ()=>isValidCondition,
    "removeFieldConditionsFromFilter": ()=>removeFieldConditionsFromFilter,
    "removeItem": ()=>removeItem,
    "renderValueText": ()=>renderValueText,
    "setGroupValue": ()=>setGroupValue,
    "syncFilters": ()=>syncFilters,
    "updateConditionByOperation": ()=>updateConditionByOperation
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/message.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$data_source$2f$data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/data_source/data_source.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_source/m_data_source.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/errors.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/format_helper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$shared$2f$filtering$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/shared/filtering.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/widget/ui.errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_between$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/filter_builder/m_between.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_filter_operations_dictionary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/filter_builder/m_filter_operations_dictionary.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const DEFAULT_DATA_TYPE = "string";
const EMPTY_MENU_ICON = "icon-none";
const AND_GROUP_OPERATION = "and";
const EQUAL_OPERATION = "=";
const NOT_EQUAL_OPERATION = "<>";
const DATATYPE_OPERATIONS = {
    number: [
        "=",
        "<>",
        "<",
        ">",
        "<=",
        ">=",
        "isblank",
        "isnotblank"
    ],
    string: [
        "contains",
        "notcontains",
        "startswith",
        "endswith",
        "=",
        "<>",
        "isblank",
        "isnotblank"
    ],
    date: [
        "=",
        "<>",
        "<",
        ">",
        "<=",
        ">=",
        "isblank",
        "isnotblank"
    ],
    datetime: [
        "=",
        "<>",
        "<",
        ">",
        "<=",
        ">=",
        "isblank",
        "isnotblank"
    ],
    boolean: [
        "=",
        "<>",
        "isblank",
        "isnotblank"
    ],
    object: [
        "isblank",
        "isnotblank"
    ]
};
const DEFAULT_FORMAT = {
    date: "shortDate",
    datetime: "shortDateShortTime"
};
const LOOKUP_OPERATIONS = [
    "=",
    "<>",
    "isblank",
    "isnotblank"
];
const AVAILABLE_FIELD_PROPERTIES = [
    "caption",
    "customizeText",
    "dataField",
    "dataType",
    "editorTemplate",
    "falseText",
    "editorOptions",
    "filterOperations",
    "format",
    "lookup",
    "trueText",
    "calculateFilterExpression",
    "name"
];
const FILTER_BUILDER_CLASS = "dx-filterbuilder";
const FILTER_BUILDER_ITEM_TEXT_CLASS = "dx-filterbuilder-text";
const FILTER_BUILDER_ITEM_TEXT_PART_CLASS = "dx-filterbuilder-text-part";
const FILTER_BUILDER_ITEM_TEXT_SEPARATOR_CLASS = "dx-filterbuilder-text-separator";
const FILTER_BUILDER_ITEM_TEXT_SEPARATOR_EMPTY_CLASS = "dx-filterbuilder-text-separator-empty";
function getFormattedValueText(field, value) {
    const fieldFormat = field.format || DEFAULT_FORMAT[field.dataType];
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format(value, fieldFormat);
}
function isNegationGroup(group) {
    return group && group.length > 1 && "!" === group[0] && !isCondition(group);
}
function getGroupCriteria(group) {
    return isNegationGroup(group) ? group[1] : group;
}
function setGroupCriteria(group, criteria) {
    if (isNegationGroup(group)) {
        group[1] = criteria;
    } else {
        group = criteria;
    }
    return group;
}
function convertGroupToNewStructure(group, value) {
    if (function(value) {
        return -1 !== value.indexOf("!");
    }(value)) {
        if (!isNegationGroup(group)) {
            !function(group) {
                const criteria = group.slice(0);
                group.length = 0;
                group.push("!", criteria);
            }(group);
        }
    } else if (isNegationGroup(group)) {
        !function(group) {
            const criteria = getGroupCriteria(group);
            group.length = 0;
            [].push.apply(group, criteria);
        }(group);
    }
}
function setGroupValue(group, value) {
    convertGroupToNewStructure(group, value);
    const criteria = getGroupCriteria(group);
    let i;
    value = function(value) {
        return -1 === value.indexOf("!") ? value : value.substring(1);
    }(value);
    !function(criteria, value) {
        for(i = 0; i < criteria.length; i++){
            if (!Array.isArray(criteria[i])) {
                criteria[i] = value;
            }
        }
    }(criteria, value);
    return group;
}
function getGroupMenuItem(group, availableGroups) {
    const groupValue = getGroupValue(group);
    return availableGroups.filter((item)=>item.value === groupValue)[0];
}
function getCriteriaOperation(criteria) {
    if (isCondition(criteria)) {
        return "and";
    }
    let value = "";
    for(let i = 0; i < criteria.length; i++){
        const item = criteria[i];
        if (!Array.isArray(item)) {
            if (value && value !== item) {
                throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["errors"].Error("E4019");
            }
            if ("!" !== item) {
                value = item;
            }
        }
    }
    return value;
}
function getGroupValue(group) {
    const criteria = getGroupCriteria(group);
    let value = getCriteriaOperation(criteria);
    if (!value) {
        value = "and";
    }
    if (criteria !== group) {
        value = "!".concat(value);
    }
    return value;
}
function getDefaultFilterOperations(field) {
    return field.lookup && LOOKUP_OPERATIONS || DATATYPE_OPERATIONS[field.dataType || "string"];
}
function containItems(entity) {
    return Array.isArray(entity) && entity.length;
}
function getFilterOperations(field) {
    const result = containItems(field.filterOperations) ? field.filterOperations : getDefaultFilterOperations(field);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])([], result);
}
function getCaptionByOperation(operation, filterOperationDescriptions) {
    const operationName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_filter_operations_dictionary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getNameByFilterOperation(operation);
    return filterOperationDescriptions && filterOperationDescriptions[operationName] ? filterOperationDescriptions[operationName] : operationName;
}
function getOperationFromAvailable(operation, availableOperations) {
    for(let i = 0; i < availableOperations.length; i++){
        if (availableOperations[i].value === operation) {
            return availableOperations[i];
        }
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error("E1048", operation);
}
function getCustomOperation(customOperations, name) {
    const filteredOperations = customOperations.filter((item)=>item.name === name);
    return filteredOperations.length ? filteredOperations[0] : null;
}
function getAvailableOperations(field, filterOperationDescriptions, customOperations) {
    const filterOperations = getFilterOperations(field);
    const isLookupField = !!field.lookup;
    customOperations.forEach((customOperation)=>{
        if (!field.filterOperations && -1 === filterOperations.indexOf(customOperation.name)) {
            const dataTypes = customOperation && customOperation.dataTypes;
            const isOperationForbidden = isLookupField ? !!customOperation.notForLookup : false;
            if (!isOperationForbidden && dataTypes && dataTypes.indexOf(field.dataType || "string") >= 0) {
                filterOperations.push(customOperation.name);
            }
        }
    });
    return filterOperations.map((operation)=>{
        const customOperation = getCustomOperation(customOperations, operation);
        if (customOperation) {
            return {
                icon: customOperation.icon || "icon-none",
                text: customOperation.caption || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["captionize"])(customOperation.name),
                value: customOperation.name,
                isCustom: true
            };
        }
        return {
            icon: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_filter_operations_dictionary$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getIconByFilterOperation(operation) || "icon-none",
            text: getCaptionByOperation(operation, filterOperationDescriptions),
            value: operation
        };
    });
}
function getDefaultOperation(field) {
    return field.defaultFilterOperation || getFilterOperations(field)[0];
}
function createCondition(field, customOperations) {
    const condition = [
        field.dataField,
        "",
        ""
    ];
    const filterOperation = getDefaultOperation(field);
    updateConditionByOperation(condition, filterOperation, customOperations);
    return condition;
}
function removeItem(group, item) {
    const criteria = getGroupCriteria(group);
    const index = criteria.indexOf(item);
    criteria.splice(index, 1);
    if (1 !== criteria.length) {
        criteria.splice(index, 1);
    }
    return group;
}
function createEmptyGroup(value) {
    const isNegation = isNegationGroupOperation(value);
    const groupOperation = isNegation ? getGroupOperationFromNegationOperation(value) : value;
    return isNegation ? [
        "!",
        [
            groupOperation
        ]
    ] : [
        groupOperation
    ];
}
function isEmptyGroup(group) {
    const criteria = getGroupCriteria(group);
    if (isCondition(criteria)) {
        return false;
    }
    const hasConditions = criteria.some((item)=>isCondition(item));
    return !hasConditions;
}
function addItem(item, group) {
    const criteria = getGroupCriteria(group);
    const groupValue = getGroupValue(criteria);
    1 === criteria.length ? criteria.unshift(item) : criteria.push(item, groupValue);
    return group;
}
function getField(dataField, fields) {
    for(let i = 0; i < fields.length; i++){
        if (fields[i].name === dataField) {
            return fields[i];
        }
        if (fields[i].dataField.toLowerCase() === dataField.toLowerCase()) {
            return fields[i];
        }
    }
    const extendedFields = getItems(fields, true).filter((item)=>item.dataField.toLowerCase() === dataField.toLowerCase());
    if (extendedFields.length > 0) {
        return extendedFields[0];
    }
    throw new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error("E1047", dataField);
}
function isGroup(criteria) {
    if (!Array.isArray(criteria)) {
        return false;
    }
    return criteria.length < 2 || Array.isArray(criteria[0]) || Array.isArray(criteria[1]);
}
function isCondition(criteria) {
    if (!Array.isArray(criteria)) {
        return false;
    }
    return criteria.length > 1 && !Array.isArray(criteria[0]) && !Array.isArray(criteria[1]);
}
function convertToInnerGroup(group, customOperations, defaultGroupOperation) {
    defaultGroupOperation = defaultGroupOperation || "and";
    const groupOperation = getCriteriaOperation(group).toLowerCase() || defaultGroupOperation;
    let innerGroup = [];
    for(let i = 0; i < group.length; i++){
        if (isGroup(group[i])) {
            innerGroup.push(convertToInnerStructure(group[i], customOperations, defaultGroupOperation));
            innerGroup = appendGroupOperationToGroup(innerGroup, groupOperation);
        } else if (isCondition(group[i])) {
            innerGroup.push(convertToInnerCondition(group[i], customOperations));
            innerGroup = appendGroupOperationToGroup(innerGroup, groupOperation);
        }
    }
    if (0 === innerGroup.length) {
        innerGroup = appendGroupOperationToGroup(innerGroup, groupOperation);
    }
    return innerGroup;
}
function conditionHasCustomOperation(condition, customOperations) {
    const customOperation = getCustomOperation(customOperations, condition[1]);
    return customOperation && customOperation.name === condition[1];
}
function convertToInnerCondition(condition, customOperations) {
    if (conditionHasCustomOperation(condition, customOperations)) {
        return condition;
    }
    if (condition.length < 3) {
        condition[2] = condition[1];
        condition[1] = "=";
    }
    return condition;
}
function isNegationGroupOperation(operation) {
    return -1 !== operation.indexOf("not");
}
function getGroupOperationFromNegationOperation(operation) {
    return operation.substring(3).toLowerCase();
}
function appendGroupOperationToCriteria(criteria, groupOperation) {
    const isNegation = isNegationGroupOperation(groupOperation);
    groupOperation = isNegation ? getGroupOperationFromNegationOperation(groupOperation) : groupOperation;
    return isNegation ? [
        "!",
        criteria,
        groupOperation
    ] : [
        criteria,
        groupOperation
    ];
}
function appendGroupOperationToGroup(group, groupOperation) {
    const isNegation = isNegationGroupOperation(groupOperation);
    groupOperation = isNegation ? getGroupOperationFromNegationOperation(groupOperation) : groupOperation;
    group.push(groupOperation);
    let result = group;
    if (isNegation) {
        result = [
            "!",
            result
        ];
    }
    return result;
}
function convertToInnerStructure(value, customOperations, defaultGroupOperation) {
    defaultGroupOperation = defaultGroupOperation || "and";
    if (!value) {
        return createEmptyGroup(defaultGroupOperation);
    }
    value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, [], value);
    if (isCondition(value)) {
        return appendGroupOperationToCriteria(convertToInnerCondition(value, customOperations), defaultGroupOperation);
    }
    if (isNegationGroup(value)) {
        return [
            "!",
            isCondition(value[1]) ? appendGroupOperationToCriteria(convertToInnerCondition(value[1], customOperations), defaultGroupOperation) : isNegationGroup(value[1]) ? appendGroupOperationToCriteria(convertToInnerStructure(value[1], customOperations), defaultGroupOperation) : convertToInnerGroup(value[1], customOperations, defaultGroupOperation)
        ];
    }
    return convertToInnerGroup(value, customOperations, defaultGroupOperation);
}
function getNormalizedFields(fields) {
    return fields.reduce((result, field)=>{
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(field.dataField)) {
            const normalizedField = {};
            for(const key in field){
                if (field[key] && AVAILABLE_FIELD_PROPERTIES.includes(key)) {
                    normalizedField[key] = field[key];
                }
            }
            normalizedField.defaultCalculateFilterExpression = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$shared$2f$filtering$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].defaultCalculateFilterExpression;
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(normalizedField.dataType)) {
                normalizedField.dataType = "string";
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(normalizedField.trueText)) {
                normalizedField.trueText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxDataGrid-trueText");
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(normalizedField.falseText)) {
                normalizedField.falseText = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxDataGrid-falseText");
            }
            result.push(normalizedField);
        }
        return result;
    }, []);
}
function getConditionFilterExpression(condition, fields, customOperations, target) {
    const field = getField(condition[0], fields);
    const filterExpression = convertToInnerCondition(condition, customOperations);
    const customOperation = customOperations.length && getCustomOperation(customOperations, filterExpression[1]);
    if (customOperation && customOperation.calculateFilterExpression) {
        return customOperation.calculateFilterExpression.apply(customOperation, [
            filterExpression[2],
            field,
            fields
        ]);
    }
    if (field.createFilterExpression) {
        return field.createFilterExpression.apply(field, [
            filterExpression[2],
            filterExpression[1],
            target
        ]);
    }
    if (field.calculateFilterExpression) {
        return field.calculateFilterExpression.apply(field, [
            filterExpression[2],
            filterExpression[1],
            target
        ]);
    }
    return field.defaultCalculateFilterExpression.apply(field, [
        filterExpression[2],
        filterExpression[1],
        target
    ]);
}
function getFilterExpression(value, fields, customOperations, target) {
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value)) {
        return null;
    }
    if (isNegationGroup(value)) {
        const filterExpression = getFilterExpression(value[1], fields, customOperations, target);
        return [
            "!",
            filterExpression
        ];
    }
    const criteria = getGroupCriteria(value);
    if (isCondition(criteria)) {
        return getConditionFilterExpression(criteria, fields, customOperations, target) || null;
    }
    let result = [];
    let filterExpression;
    const groupValue = getGroupValue(criteria);
    for(let i = 0; i < criteria.length; i++){
        if (isGroup(criteria[i])) {
            filterExpression = getFilterExpression(criteria[i], fields, customOperations, target);
            if (filterExpression) {
                i && result.push(groupValue);
                result.push(filterExpression);
            }
        } else if (isCondition(criteria[i])) {
            filterExpression = getConditionFilterExpression(criteria[i], fields, customOperations, target);
            if (filterExpression) {
                result.length && result.push(groupValue);
                result.push(filterExpression);
            }
        }
    }
    if (1 === result.length) {
        result = result[0];
    }
    return result.length ? result : null;
}
function getNormalizedFilter(group) {
    const criteria = getGroupCriteria(group);
    let i;
    if (0 === criteria.length) {
        return null;
    }
    const itemsForRemove = [];
    for(i = 0; i < criteria.length; i++){
        if (isGroup(criteria[i])) {
            const normalizedGroupValue = getNormalizedFilter(criteria[i]);
            if (normalizedGroupValue) {
                criteria[i] = normalizedGroupValue;
            } else {
                itemsForRemove.push(criteria[i]);
            }
        } else if (isCondition(criteria[i])) {
            if (!isValidCondition(criteria[i])) {
                itemsForRemove.push(criteria[i]);
            }
        }
    }
    for(i = 0; i < itemsForRemove.length; i++){
        removeItem(criteria, itemsForRemove[i]);
    }
    if (1 === criteria.length) {
        return null;
    }
    criteria.splice(criteria.length - 1, 1);
    if (1 === criteria.length) {
        group = setGroupCriteria(group, criteria[0]);
    }
    if (0 === group.length) {
        return null;
    }
    return group;
}
function getCurrentLookupValueText(field, value, handler) {
    if ("" === value) {
        handler("");
        return;
    }
    const { lookup: lookup } = field;
    if (lookup.items) {
        handler(lookup.calculateCellValue(value) || "");
    } else {
        const lookupDataSource = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(lookup.dataSource) ? lookup.dataSource({}) : lookup.dataSource;
        const dataSource = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_source$2f$m_data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DataSource"](lookupDataSource);
        dataSource.loadSingle(lookup.valueExpr, value).done((result)=>{
            let valueText = "";
            if (result) {
                valueText = lookup.displayExpr ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["compileGetter"])(lookup.displayExpr)(result) : result;
            }
            if (field.customizeText) {
                valueText = field.customizeText({
                    value: value,
                    valueText: valueText
                });
            }
            handler(valueText);
        }).fail(()=>{
            handler("");
        });
    }
}
function getPrimitiveValueText(field, value, customOperation, target, options) {
    let valueText;
    if (true === value) {
        valueText = field.trueText || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxDataGrid-trueText");
    } else if (false === value) {
        valueText = field.falseText || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxDataGrid-falseText");
    } else {
        valueText = getFormattedValueText(field, value);
    }
    if (field.customizeText) {
        valueText = field.customizeText.call(field, {
            value: value,
            valueText: valueText,
            target: target
        });
    }
    if (customOperation && customOperation.customizeText) {
        valueText = customOperation.customizeText.call(customOperation, {
            value: value,
            valueText: valueText,
            field: field,
            target: target
        }, options);
    }
    return valueText;
}
function getArrayValueText(field, value, customOperation, target) {
    const options = {
        values: value
    };
    return value.map((v)=>getPrimitiveValueText(field, v, customOperation, target, options));
}
function checkDefaultValue(value) {
    return "" === value || null === value;
}
function getCurrentValueText(field, value, customOperation) {
    let target = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : "filterBuilder";
    if (checkDefaultValue(value)) {
        return "";
    }
    if (Array.isArray(value)) {
        const result = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"];
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"].apply(this, getArrayValueText(field, value, customOperation, target)).done(function() {
            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                args[_key] = arguments[_key];
            }
            const text = args.some((item)=>!checkDefaultValue(item)) ? args.map((item)=>!checkDefaultValue(item) ? item : "?") : "";
            result.resolve(text);
        });
        return result;
    }
    return getPrimitiveValueText(field, value, customOperation, target);
}
function itemExists(plainItems, parentId) {
    return plainItems.some((item)=>item.dataField === parentId);
}
function pushItemAndCheckParent(originalItems, plainItems, item) {
    const { dataField: dataField } = item;
    if (hasParent(dataField)) {
        item.parentId = getParentIdFromItemDataField(dataField);
        if (!itemExists(plainItems, item.parentId) && !itemExists(originalItems, item.parentId)) {
            pushItemAndCheckParent(originalItems, plainItems, {
                id: item.parentId,
                dataType: "object",
                dataField: item.parentId,
                caption: generateCaptionByDataField(item.parentId, true),
                filterOperations: [
                    "isblank",
                    "isnotblank"
                ],
                defaultCalculateFilterExpression: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$shared$2f$filtering$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].defaultCalculateFilterExpression
            });
        }
    }
    plainItems.push(item);
}
function generateCaptionByDataField(dataField, allowHierarchicalFields) {
    let caption = "";
    if (allowHierarchicalFields) {
        dataField = dataField.substring(dataField.lastIndexOf(".") + 1);
    } else if (hasParent(dataField)) {
        dataField.split(".").forEach((field, index, arr)=>{
            caption += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["captionize"])(field);
            if (index !== arr.length - 1) {
                caption += ".";
            }
        });
        return caption;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["captionize"])(dataField);
}
function getItems(fields, allowHierarchicalFields) {
    const items = [];
    for(let i = 0; i < fields.length; i++){
        const item = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {
            caption: generateCaptionByDataField(fields[i].dataField, allowHierarchicalFields)
        }, fields[i]);
        item.id = item.name || item.dataField;
        if (allowHierarchicalFields) {
            pushItemAndCheckParent(fields, items, item);
        } else {
            items.push(item);
        }
    }
    return items;
}
function hasParent(dataField) {
    return -1 !== dataField.lastIndexOf(".");
}
function getParentIdFromItemDataField(dataField) {
    return dataField.substring(0, dataField.lastIndexOf("."));
}
function getCaptionWithParents(item, plainItems) {
    if (hasParent(item.dataField)) {
        const parentId = getParentIdFromItemDataField(item.dataField);
        for(let i = 0; i < plainItems.length; i++){
            if (plainItems[i].dataField === parentId) {
                return "".concat(getCaptionWithParents(plainItems[i], plainItems), ".").concat(item.caption);
            }
        }
    }
    return item.caption;
}
function updateConditionByOperation(condition, operation, customOperations) {
    let customOperation = getCustomOperation(customOperations, operation);
    if (customOperation) {
        if (false === customOperation.hasValue) {
            condition[1] = operation;
            condition.length = 2;
        } else {
            condition[1] = operation;
            condition[2] = "";
        }
        return condition;
    }
    if ("isblank" === operation) {
        condition[1] = "=";
        condition[2] = null;
    } else if ("isnotblank" === operation) {
        condition[1] = "<>";
        condition[2] = null;
    } else {
        customOperation = getCustomOperation(customOperations, condition[1]);
        if (customOperation || 2 === condition.length || null === condition[2]) {
            condition[2] = "";
        }
        condition[1] = operation;
    }
    return condition;
}
function getOperationValue(condition) {
    let caption;
    if (null === condition[2]) {
        if ("=" === condition[1]) {
            caption = "isblank";
        } else {
            caption = "isnotblank";
        }
    } else {
        caption = condition[1];
    }
    return caption;
}
function isValidCondition(condition) {
    return "" !== condition[2];
}
function getMergedOperations(customOperations, betweenCaption, context) {
    const result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, [], customOperations);
    let betweenIndex = -1;
    result.some((customOperation, index)=>{
        if ("between" === customOperation.name) {
            betweenIndex = index;
            return true;
        }
        return;
    });
    if (-1 !== betweenIndex) {
        result[betweenIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_between$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])(betweenCaption, context), result[betweenIndex]);
    } else {
        result.unshift((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_between$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getConfig"])(betweenCaption, context));
    }
    return result;
}
function isMatchedCondition(filter, addedFilterDataField) {
    return filter[0] === addedFilterDataField;
}
function removeFieldConditionsFromFilter(filter, dataField) {
    if (!filter || 0 === filter.length) {
        return null;
    }
    if (isCondition(filter)) {
        const hasMatchedCondition = isMatchedCondition(filter, dataField);
        return !hasMatchedCondition ? filter : null;
    }
    return syncConditionIntoGroup(filter, [
        dataField
    ], false);
}
function syncConditionIntoGroup(filter, addedFilter, canPush) {
    const result = [];
    const isNegation = isNegationGroup(filter);
    filter.forEach((item)=>{
        if (isCondition(item)) {
            if (isMatchedCondition(item, addedFilter[0])) {
                if (canPush) {
                    result.push(addedFilter);
                    canPush = false;
                } else {
                    result.splice(result.length - 1, 1);
                }
            } else {
                result.push(item);
            }
        } else {
            (result.length || isGroup(item)) && result.push(item);
        }
    });
    if (0 === result.length) {
        return null;
    }
    if (canPush) {
        result.push("and");
        result.push(addedFilter);
    }
    if (isNegation) {
        return [
            "!",
            1 === result.length ? result[0] : result
        ];
    }
    return 1 === result.length ? result[0] : result;
}
function syncFilters(filter, addedFilter) {
    if (null === filter || 0 === filter.length) {
        return addedFilter;
    }
    if (isCondition(filter)) {
        if (isMatchedCondition(filter, addedFilter[0])) {
            return addedFilter;
        }
        return [
            filter,
            "and",
            addedFilter
        ];
    }
    const groupValue = getGroupValue(filter);
    if ("and" !== groupValue) {
        return [
            addedFilter,
            "and",
            filter
        ];
    }
    return syncConditionIntoGroup(filter, addedFilter, true);
}
function getMatchedConditions(filter, dataField) {
    if (null === filter || 0 === filter.length) {
        return [];
    }
    if (isCondition(filter)) {
        if (isMatchedCondition(filter, dataField)) {
            return [
                filter
            ];
        }
        return [];
    }
    const groupValue = getGroupValue(filter);
    if ("and" !== groupValue) {
        return [];
    }
    const result = filter.filter((item)=>isCondition(item) && isMatchedCondition(item, dataField));
    return result;
}
function filterHasField(filter, dataField) {
    if (null === filter || 0 === filter.length) {
        return false;
    }
    if (isCondition(filter)) {
        return filter[0] === dataField;
    }
    return filter.some((item)=>(isCondition(item) || isGroup(item)) && filterHasField(item, dataField));
}
const renderValueText = function($container, value, customOperation) {
    if (Array.isArray(value)) {
        const lastItemIndex = value.length - 1;
        $container.empty();
        value.forEach((t, i)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<span>").addClass("dx-filterbuilder-text-part").text(t).appendTo($container);
            if (i !== lastItemIndex) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<span>").addClass("dx-filterbuilder-text-separator").text(customOperation && customOperation.valueSeparator ? customOperation.valueSeparator : "|").addClass("dx-filterbuilder-text-separator-empty").appendTo($container);
            }
        });
    } else if (value) {
        $container.text(value);
    } else {
        $container.text(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-enterValueText"));
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/filter_builder/m_filter_builder.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/filter_builder/m_filter_builder.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/message.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/component_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/guid.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$popup$2f$ui$2e$popup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/popup/ui.popup.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$shared$2f$ui$2e$editor_factory_mixin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/shared/ui.editor_factory_mixin.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$tree_view$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/tree_view.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$widget$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/widget/ui.widget.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$ui$2f$overlay$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/ui/overlay/m_utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/filter_builder/m_utils.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const FILTER_BUILDER_CLASS = "dx-filterbuilder";
const FILTER_BUILDER_GROUP_CLASS = "dx-filterbuilder-group";
const FILTER_BUILDER_GROUP_ITEM_CLASS = "dx-filterbuilder-group-item";
const FILTER_BUILDER_GROUP_CONTENT_CLASS = "dx-filterbuilder-group-content";
const FILTER_BUILDER_GROUP_OPERATIONS_CLASS = "dx-filterbuilder-group-operations";
const FILTER_BUILDER_GROUP_OPERATION_CLASS = "dx-filterbuilder-group-operation";
const FILTER_BUILDER_ACTION_CLASS = "dx-filterbuilder-action";
const FILTER_BUILDER_IMAGE_CLASS = "dx-filterbuilder-action-icon";
const FILTER_BUILDER_IMAGE_ADD_CLASS = "dx-icon-plus";
const FILTER_BUILDER_IMAGE_REMOVE_CLASS = "dx-icon-remove";
const FILTER_BUILDER_ITEM_TEXT_CLASS = "dx-filterbuilder-text";
const FILTER_BUILDER_ITEM_FIELD_CLASS = "dx-filterbuilder-item-field";
const FILTER_BUILDER_ITEM_OPERATION_CLASS = "dx-filterbuilder-item-operation";
const FILTER_BUILDER_ITEM_VALUE_CLASS = "dx-filterbuilder-item-value";
const FILTER_BUILDER_ITEM_VALUE_TEXT_CLASS = "dx-filterbuilder-item-value-text";
const FILTER_BUILDER_OVERLAY_CLASS = "dx-filterbuilder-overlay";
const FILTER_BUILDER_FILTER_OPERATIONS_CLASS = "dx-filterbuilder-operations";
const FILTER_BUILDER_FIELDS_CLASS = "dx-filterbuilder-fields";
const FILTER_BUILDER_ADD_CONDITION_CLASS = "dx-filterbuilder-add-condition";
const ACTIVE_CLASS = "dx-state-active";
const FILTER_BUILDER_MENU_CUSTOM_OPERATION_CLASS = "dx-filterbuilder-menu-custom-operation";
const SOURCE = "filterBuilder";
const DISABLED_STATE_CLASS = "dx-state-disabled";
const OVERLAY_CONTENT_CLASS = "dx-overlay-content";
const TREEVIEW_NODE_CONTAINER = "dx-treeview-node-container";
const TAB_KEY = "tab";
const ENTER_KEY = "enter";
const ESCAPE_KEY = "escape";
const ACTIONS = [
    {
        name: "onEditorPreparing",
        config: {
            excludeValidators: [
                "disabled",
                "readOnly"
            ],
            category: "rendering"
        }
    },
    {
        name: "onEditorPrepared",
        config: {
            excludeValidators: [
                "disabled",
                "readOnly"
            ],
            category: "rendering"
        }
    },
    {
        name: "onValueChanged",
        config: {
            excludeValidators: [
                "disabled",
                "readOnly"
            ]
        }
    }
];
const OPERATORS = {
    and: "and",
    or: "or",
    notAnd: "!and",
    notOr: "!or"
};
const EditorFactory = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$shared$2f$ui$2e$editor_factory_mixin$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(class {
});
class FilterBuilder extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$widget$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    _getDefaultOptions() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(super._getDefaultOptions(), {
            onEditorPreparing: null,
            onEditorPrepared: null,
            onValueChanged: null,
            fields: [],
            groupOperations: [
                "and",
                "or",
                "notAnd",
                "notOr"
            ],
            maxGroupLevel: void 0,
            value: null,
            allowHierarchicalFields: false,
            groupOperationDescriptions: {
                and: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-and"),
                or: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-or"),
                notAnd: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-notAnd"),
                notOr: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-notOr")
            },
            customOperations: [],
            closePopupOnTargetScroll: true,
            filterOperationDescriptions: {
                between: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationBetween"),
                equal: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationEquals"),
                notEqual: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationNotEquals"),
                lessThan: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationLess"),
                lessThanOrEqual: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationLessOrEquals"),
                greaterThan: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationGreater"),
                greaterThanOrEqual: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationGreaterOrEquals"),
                startsWith: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationStartsWith"),
                contains: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationContains"),
                notContains: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationNotContains"),
                endsWith: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationEndsWith"),
                isBlank: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationIsBlank"),
                isNotBlank: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterOperationIsNotBlank")
            }
        });
    }
    _optionChanged(args) {
        switch(args.name){
            case "closePopupOnTargetScroll":
                break;
            case "onEditorPreparing":
            case "onEditorPrepared":
            case "onValueChanged":
                this._initActions();
                break;
            case "customOperations":
                this._initCustomOperations();
                this._invalidate();
                break;
            case "fields":
            case "maxGroupLevel":
            case "groupOperations":
            case "allowHierarchicalFields":
            case "groupOperationDescriptions":
            case "filterOperationDescriptions":
                this._invalidate();
                break;
            case "value":
                if (args.value !== args.previousValue) {
                    const disableInvalidateForValue = this._disableInvalidateForValue;
                    if (!disableInvalidateForValue) {
                        this._initModel();
                        this._invalidate();
                    }
                    this._disableInvalidateForValue = false;
                    this.executeAction("onValueChanged", {
                        value: args.value,
                        previousValue: args.previousValue
                    });
                    this._disableInvalidateForValue = disableInvalidateForValue;
                }
                break;
            default:
                super._optionChanged(args);
        }
    }
    getFilterExpression() {
        const fields = this._getNormalizedFields();
        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, [], this._model);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getFilterExpression"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNormalizedFilter"])(value), fields, this._customOperations, SOURCE);
    }
    _getNormalizedFields() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNormalizedFields"])(this.option("fields"));
    }
    _updateFilter() {
        this._disableInvalidateForValue = true;
        const value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, [], this._model);
        const normalizedValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNormalizedFilter"])(value);
        const oldValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNormalizedFilter"])(this._getModel(this.option("value")));
        if (JSON.stringify(oldValue) !== JSON.stringify(normalizedValue)) {
            this.option("value", normalizedValue);
        }
        this._disableInvalidateForValue = false;
        this._fireContentReadyAction();
    }
    _init() {
        this._initCustomOperations();
        this._initModel();
        this._initEditorFactory();
        this._initActions();
        super._init();
    }
    _initEditorFactory() {
        this._editorFactory = new EditorFactory;
    }
    _initCustomOperations() {
        this._customOperations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMergedOperations"])(this.option("customOperations"), this.option("filterOperationDescriptions.between"), this);
    }
    _getDefaultGroupOperation() {
        var _this$option;
        var _ref;
        return (_ref = null === (_this$option = this.option("groupOperations")) || void 0 === _this$option ? void 0 : _this$option[0]) !== null && _ref !== void 0 ? _ref : OPERATORS.and;
    }
    _getModel(value) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertToInnerStructure"])(value, this._customOperations, this._getDefaultGroupOperation());
    }
    _initModel() {
        this._model = this._getModel(this.option("value"));
    }
    _initActions() {
        const that = this;
        that._actions = {};
        ACTIONS.forEach((action)=>{
            const actionConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, action.config);
            that._actions[action.name] = that._createActionByOption(action.name, actionConfig);
        });
    }
    executeAction(actionName, options) {
        const action = this._actions[actionName];
        return action && action(options);
    }
    _initMarkup() {
        this.$element().addClass("dx-filterbuilder");
        super._initMarkup();
        this._addAriaAttributes(this.$element(), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaRootElement"), "group");
        this._createGroupElementByCriteria(this._model).appendTo(this.$element());
    }
    _addAriaAttributes($element, ariaLabel, role, hasPopup, hasExpanded, ariaLevel) {
        if (!$element || !$element.length) {
            return;
        }
        const attributes = {
            role: role
        };
        if (ariaLabel) {
            if ($element.text().length > 0) {
                attributes.title = ariaLabel;
            } else {
                attributes["aria-label"] = ariaLabel;
            }
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(hasPopup)) {
            attributes["aria-haspopup"] = "".concat(hasPopup);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(hasExpanded)) {
            attributes["aria-expanded"] = "".concat(hasExpanded);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(ariaLevel)) {
            attributes["aria-level"] = "".concat(ariaLevel);
        }
        $element.attr(attributes);
    }
    _createConditionElement(condition, parent, groupLevel) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-group").append(this._createConditionItem(condition, parent, groupLevel)).attr("role", "group");
    }
    _createGroupElementByCriteria(criteria, parent) {
        let groupLevel = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 0;
        const $group = this._createGroupElement(criteria, parent, groupLevel);
        const $groupContent = $group.find(".dx-filterbuilder-group-content");
        const groupCriteria = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getGroupCriteria"])(criteria);
        for(let i = 0; i < groupCriteria.length; i++){
            const innerCriteria = groupCriteria[i];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isGroup"])(innerCriteria)) {
                this._createGroupElementByCriteria(innerCriteria, criteria, groupLevel + 1).appendTo($groupContent);
            } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isCondition"])(innerCriteria)) {
                this._createConditionElement(innerCriteria, criteria, "".concat(groupLevel + 1)).appendTo($groupContent);
            }
        }
        return $group;
    }
    _createGroupElement(criteria, parent, groupLevel) {
        const $guid = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        const $groupItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-group-item");
        const $groupContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-group-content").attr("id", "".concat($guid));
        const $group = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-group").append($groupItem).append($groupContent);
        if (null != parent) {
            this._createRemoveButton(()=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItem"])(parent, criteria);
                $group.remove();
                this._updateFilter();
            }, "group").appendTo($groupItem);
        }
        let groupItemLevel = groupLevel;
        if (0 === groupLevel) {
            this._addAriaAttributes($group, "", "tree");
            groupItemLevel += 1;
        }
        this._addAriaAttributes($groupItem, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaGroupItem"), "treeitem", null, null, groupItemLevel);
        $groupItem.attr("aria-owns", "".concat($guid));
        this._createGroupOperationButton(criteria).appendTo($groupItem);
        this._createAddButton(()=>{
            const newGroup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createEmptyGroup"])(this._getDefaultGroupOperation());
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addItem"])(newGroup, criteria);
            this._createGroupElement(newGroup, criteria, groupLevel + 1).appendTo($groupContent);
            this._updateFilter();
        }, ()=>{
            const field = this.option("fields")[0];
            const newCondition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createCondition"])(field, this._customOperations);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addItem"])(newCondition, criteria);
            this._createConditionElement(newCondition, criteria, groupLevel + 1).appendTo($groupContent);
            this._updateFilter();
        }, groupLevel).appendTo($groupItem);
        return $group;
    }
    _createButton(caption) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").text(caption);
    }
    _createGroupOperationButton(criteria) {
        const groupOperations = this._getGroupOperations(criteria);
        let groupMenuItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getGroupMenuItem"])(criteria, groupOperations);
        const caption = groupMenuItem.text;
        const $operationButton = groupOperations && groupOperations.length < 2 ? this._createButton(caption).addClass("dx-state-disabled") : this._createButtonWithMenu({
            caption: caption,
            menu: {
                items: groupOperations,
                displayExpr: "text",
                keyExpr: "value",
                onItemClick: (e)=>{
                    if (groupMenuItem !== e.itemData) {
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setGroupValue"])(criteria, e.itemData.value);
                        $operationButton.text(e.itemData.text);
                        groupMenuItem = e.itemData;
                        this._updateFilter();
                    }
                },
                onContentReady (e) {
                    e.component.selectItem(groupMenuItem);
                },
                cssClass: "dx-filterbuilder-group-operations"
            }
        });
        this._addAriaAttributes($operationButton, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaOperationButton"), "combobox", true, false);
        return $operationButton.addClass("dx-filterbuilder-text").addClass("dx-filterbuilder-group-operation").attr("tabindex", 0);
    }
    _createButtonWithMenu(options) {
        const that = this;
        const removeMenu = function() {
            that.$element().find(".".concat(ACTIVE_CLASS)).removeClass(ACTIVE_CLASS).attr("aria-expanded", "false");
            that.$element().find(".dx-overlay .dx-treeview").remove();
            that.$element().find(".dx-overlay").remove();
        };
        const rtlEnabled = this.option("rtlEnabled");
        const position = rtlEnabled ? "right" : "left";
        const $button = this._createButton(options.caption);
        const $guid = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$guid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        $button.attr("aria-controls", "".concat($guid));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(options.menu, {
            id: $guid,
            focusStateEnabled: true,
            selectionMode: "single",
            onItemClick: (handler = options.menu.onItemClick, function(e) {
                handler(e);
                if ("dxclick" === e.event.type) {
                    removeMenu();
                }
            }),
            onHiding () {
                $button.removeClass(ACTIVE_CLASS).attr("aria-expanded", "false");
            },
            position: {
                my: "".concat(position, " top"),
                at: "".concat(position, " bottom"),
                offset: "0 1",
                of: $button,
                collision: "flip"
            },
            animation: null,
            onHidden () {
                removeMenu();
            },
            cssClass: "dx-filterbuilder-overlay ".concat(options.menu.cssClass),
            rtlEnabled: rtlEnabled
        });
        var handler;
        options.popup = {
            onShown (info) {
                const treeViewContentElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(info.component.content());
                const treeViewElement = treeViewContentElement.find(".dx-treeview");
                if (treeViewElement.length) {
                    that._applyAccessibilityAttributes(treeViewElement);
                }
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(treeViewElement, "keyup keydown", (e)=>{
                    const keyName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeKeyName"])(e);
                    if ("keydown" === e.type && "tab" === keyName || "keyup" === e.type && ("escape" === keyName || "enter" === keyName)) {
                        info.component.hide();
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger(options.menu.position.of, "focus");
                    }
                });
                const treeView = treeViewElement.dxTreeView("instance");
                treeView.focus();
                treeView.option("focusedElement", null);
            }
        };
        this._subscribeOnClickAndEnterKey($button, ()=>{
            removeMenu();
            that._createPopupWithTreeView(options, that.$element());
            $button.addClass(ACTIVE_CLASS).attr("aria-expanded", "true");
        });
        return $button;
    }
    _hasValueButton(condition) {
        const customOperation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCustomOperation"])(this._customOperations, condition[1]);
        return customOperation ? false !== customOperation.hasValue : null !== condition[2];
    }
    _createOperationButtonWithMenu(condition, field) {
        const that = this;
        const availableOperations = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAvailableOperations"])(field, this.option("filterOperationDescriptions"), this._customOperations);
        let currentOperation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOperationFromAvailable"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOperationValue"])(condition), availableOperations);
        const $operationButton = this._createButtonWithMenu({
            caption: currentOperation.text,
            menu: {
                items: availableOperations,
                displayExpr: "text",
                onItemRendered (e) {
                    e.itemData.isCustom && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e.itemElement).addClass("dx-filterbuilder-menu-custom-operation");
                },
                onContentReady (e) {
                    e.component.selectItem(currentOperation);
                },
                onItemClick: (e)=>{
                    if (currentOperation !== e.itemData) {
                        currentOperation = e.itemData;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateConditionByOperation"])(condition, currentOperation.value, that._customOperations);
                        const $valueButton = $operationButton.siblings().filter(".dx-filterbuilder-item-value");
                        if (that._hasValueButton(condition)) {
                            if (0 !== $valueButton.length) {
                                $valueButton.remove();
                            }
                            that._createValueButton(condition, field).appendTo($operationButton.parent());
                        } else {
                            $valueButton.remove();
                        }
                        $operationButton.text(currentOperation.text);
                        this._updateFilter();
                    }
                },
                cssClass: "dx-filterbuilder-operations"
            }
        }).addClass("dx-filterbuilder-text").addClass("dx-filterbuilder-item-operation").attr("tabindex", 0);
        this._addAriaAttributes($operationButton, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaItemOperation"), "combobox", true, false);
        return $operationButton;
    }
    _createOperationAndValueButtons(condition, field, $item) {
        this._createOperationButtonWithMenu(condition, field).appendTo($item);
        if (this._hasValueButton(condition)) {
            this._createValueButton(condition, field).appendTo($item);
        }
    }
    _createFieldButtonWithMenu(fields, condition, field) {
        const that = this;
        const allowHierarchicalFields = this.option("allowHierarchicalFields");
        const items = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getItems"])(fields, allowHierarchicalFields);
        let item = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getField"])(field.name || field.dataField, items);
        const getFullCaption = function(item, items) {
            return allowHierarchicalFields ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCaptionWithParents"])(item, items) : item.caption;
        };
        condition[0] = item.name || item.dataField;
        const $fieldButton = this._createButtonWithMenu({
            caption: getFullCaption(item, items),
            menu: {
                items: items,
                dataStructure: "plain",
                keyExpr: "id",
                parentId: "parentId",
                displayExpr: "caption",
                onItemClick: (e)=>{
                    if (item !== e.itemData) {
                        item = e.itemData;
                        condition[0] = item.name || item.dataField;
                        condition[2] = "object" === item.dataType ? null : "";
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updateConditionByOperation"])(condition, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDefaultOperation"])(item), that._customOperations);
                        $fieldButton.siblings().filter(".dx-filterbuilder-text").remove();
                        that._createOperationAndValueButtons(condition, item, $fieldButton.parent());
                        const caption = getFullCaption(item, e.component.option("items"));
                        $fieldButton.text(caption);
                        this._updateFilter();
                    }
                },
                onContentReady (e) {
                    e.component.selectItem(item);
                },
                cssClass: "dx-filterbuilder-fields"
            }
        }).addClass("dx-filterbuilder-text").addClass("dx-filterbuilder-item-field").attr("tabindex", 0);
        this._addAriaAttributes($fieldButton, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaItemField"), "combobox", true, false);
        return $fieldButton;
    }
    _createConditionItem(condition, parent, groupLevel) {
        const $item = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-group-item");
        const fields = this._getNormalizedFields();
        const field = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getField"])(condition[0], fields);
        this._addAriaAttributes($item, "", "treeitem", null, null, groupLevel);
        this._createRemoveButton(()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeItem"])(parent, condition);
            const isSingleChild = 1 === $item.parent().children().length;
            if (isSingleChild) {
                $item.parent().remove();
            } else {
                $item.remove();
            }
            this._updateFilter();
        }, "condition").appendTo($item);
        this._createFieldButtonWithMenu(fields, condition, field).appendTo($item);
        this._createOperationAndValueButtons(condition, field, $item);
        return $item;
    }
    _getGroupOperations(criteria) {
        let groupOperations = this.option("groupOperations");
        const groupOperationDescriptions = this.option("groupOperationDescriptions");
        if (!groupOperations || !groupOperations.length) {
            groupOperations = [
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getGroupValue"])(criteria).replace("!", "not")
            ];
        }
        return groupOperations.map((operation)=>({
                text: groupOperationDescriptions[operation],
                value: OPERATORS[operation]
            }));
    }
    _createRemoveButton(handler, type) {
        const $removeButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass(FILTER_BUILDER_IMAGE_CLASS).addClass("dx-icon-remove").addClass("dx-filterbuilder-action").attr("tabindex", 0);
        if (type) {
            const removeMessage = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaRemoveButton", type);
            this._addAriaAttributes($removeButton, removeMessage, "button");
        }
        this._subscribeOnClickAndEnterKey($removeButton, handler);
        return $removeButton;
    }
    _createAddButton(addGroupHandler, addConditionHandler, groupLevel) {
        let $button;
        const maxGroupLevel = this.option("maxGroupLevel");
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(maxGroupLevel) && groupLevel >= maxGroupLevel) {
            $button = this._createButton();
            this._subscribeOnClickAndEnterKey($button, addConditionHandler);
        } else {
            $button = this._createButtonWithMenu({
                menu: {
                    items: [
                        {
                            caption: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-addCondition"),
                            click: addConditionHandler
                        },
                        {
                            caption: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-addGroup"),
                            click: addGroupHandler
                        }
                    ],
                    displayExpr: "caption",
                    onItemClick (e) {
                        e.itemData.click();
                    },
                    cssClass: "dx-filterbuilder-add-condition"
                }
            });
        }
        this._addAriaAttributes($button, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaAddButton"), "combobox", true, false);
        return $button.addClass(FILTER_BUILDER_IMAGE_CLASS).addClass("dx-icon-plus").addClass("dx-filterbuilder-action").attr("tabindex", 0);
    }
    _createValueText(item, field, $container) {
        const that = this;
        const $text = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").html("&nbsp;").addClass("dx-filterbuilder-item-value-text").attr("tabindex", 0).appendTo($container);
        this._addAriaAttributes($text, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format("dxFilterBuilder-filterAriaItemValue"), "button", true);
        const value = item[2];
        const customOperation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCustomOperation"])(that._customOperations, item[1]);
        if (!customOperation && field.lookup) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentLookupValueText"])(field, value, (result)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["renderValueText"])($text, result);
            });
        } else {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCurrentValueText"])(field, value, customOperation)).done((result)=>{
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["renderValueText"])($text, result, customOperation);
            });
        }
        that._subscribeOnClickAndEnterKey($text, (e)=>{
            if ("keyup" === e.type) {
                e.stopPropagation();
            }
            that._createValueEditorWithEvents(item, field, $container);
        });
        return $text;
    }
    _updateConditionValue(item, value, callback) {
        const areValuesDifferent = item[2] !== value;
        if (areValuesDifferent) {
            item[2] = value;
        }
        callback();
        this._updateFilter();
    }
    _addDocumentKeyUp($editor, handler) {
        let isComposing = false;
        let hasCompositionJustEnded = false;
        const document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
        const documentKeyUpHandler = (e)=>{
            if (isComposing || hasCompositionJustEnded) {
                hasCompositionJustEnded = false;
                return;
            }
            handler(e);
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(document, "keyup", documentKeyUpHandler);
        const input = $editor.find("input");
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(input, "compositionstart", ()=>{
            isComposing = true;
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(input, "compositionend", ()=>{
            isComposing = false;
            hasCompositionJustEnded = true;
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(input, "keydown", (event)=>{
            if (229 !== event.which) {
                hasCompositionJustEnded = false;
            }
        });
        this._documentKeyUpHandler = documentKeyUpHandler;
    }
    _addDocumentClick($editor, closeEditorFunc) {
        const document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
        const documentClickHandler = (e)=>{
            if (!this._isFocusOnEditorParts($editor, e.target)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger($editor.find("input"), "change");
                closeEditorFunc();
            }
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(document, "dxpointerdown", documentClickHandler);
        this._documentClickHandler = documentClickHandler;
    }
    _isFocusOnEditorParts($editor, target) {
        const activeElement = target || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getActiveElement();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(activeElement).closest($editor.children()).length || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(activeElement).closest(".dx-dropdowneditor-overlay").length;
    }
    _removeEvents() {
        const document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this._documentKeyUpHandler) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(document, "keyup", this._documentKeyUpHandler);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this._documentClickHandler) && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(document, "dxpointerdown", this._documentClickHandler);
    }
    _dispose() {
        this._removeEvents();
        super._dispose();
    }
    _createValueEditorWithEvents(item, field, $container) {
        let value = item[2];
        const createValueText = ()=>{
            $container.empty();
            this._removeEvents();
            return this._createValueText(item, field, $container);
        };
        const closeEditor = ()=>{
            this._updateConditionValue(item, value, ()=>{
                createValueText();
            });
        };
        const options = {
            value: "" === value ? null : value,
            filterOperation: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOperationValue"])(item),
            setValue (data) {
                value = null === data ? "" : data;
            },
            closeEditor: closeEditor,
            text: $container.text()
        };
        $container.empty();
        const $editor = this._createValueEditor($container, field, options);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger($editor.find("input").not(":hidden").eq(0), "focus");
        this._removeEvents();
        this._addDocumentClick($editor, closeEditor);
        this._addDocumentKeyUp($editor, (e)=>{
            const keyName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeKeyName"])(e);
            if ("tab" === keyName) {
                if (this._isFocusOnEditorParts($editor)) {
                    return;
                }
                this._updateConditionValue(item, value, ()=>{
                    createValueText();
                    if (e.shiftKey) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger($container.prev(), "focus");
                    }
                });
            }
            if ("escape" === keyName) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger(createValueText(), "focus");
            }
            if ("enter" === keyName) {
                this._updateConditionValue(item, value, ()=>{
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger(createValueText(), "focus");
                });
            }
        });
        this._fireContentReadyAction();
    }
    _createValueButton(item, field) {
        const $valueButton = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-filterbuilder-text").addClass("dx-filterbuilder-item-value");
        this._createValueText(item, field, $valueButton);
        return $valueButton;
    }
    _createValueEditor($container, field, options) {
        const $editor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").attr("tabindex", 0).appendTo($container);
        const customOperation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$filter_builder$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCustomOperation"])(this._customOperations, options.filterOperation);
        const editorTemplate = customOperation && customOperation.editorTemplate ? customOperation.editorTemplate : field.editorTemplate;
        if (editorTemplate) {
            const template = this._getTemplate(editorTemplate);
            template.render({
                model: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                    field: field
                }, options),
                container: $editor
            });
        } else {
            this._editorFactory.createEditor.call(this, $editor, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, field, options, {
                parentType: SOURCE
            }));
        }
        return $editor;
    }
    _createPopupWithTreeView(options, $container) {
        const that = this;
        const $popup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass(options.menu.cssClass).appendTo($container);
        this._createComponent($popup, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$popup$2f$ui$2e$popup$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            onHiding: options.menu.onHiding,
            onHidden: options.menu.onHidden,
            rtlEnabled: options.menu.rtlEnabled,
            position: options.menu.position,
            animation: options.menu.animation,
            contentTemplate (contentElement) {
                const $menuContainer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").appendTo(contentElement);
                that._createComponent($menuContainer, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$tree_view$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], options.menu);
                $menuContainer.attr("id", "".concat(options.menu.id));
                this.repaint();
            },
            _ignoreFunctionValueDeprecation: true,
            maxHeight: ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$ui$2f$overlay$2f$m_utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getElementMaxHeightByWindow"])(options.menu.position.of),
            visible: true,
            focusStateEnabled: false,
            preventScrollEvents: false,
            container: $popup,
            hideOnOutsideClick: true,
            onShown: options.popup.onShown,
            shading: false,
            width: "auto",
            height: "auto",
            showTitle: false,
            _wrapperClassExternal: options.menu.cssClass,
            _ignorePreventScrollEventsDeprecation: true
        });
    }
    _subscribeOnClickAndEnterKey($button, handler) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($button, "dxclick", handler);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($button, "keyup", (e)=>{
            if ("enter" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeKeyName"])(e)) {
                handler(e);
            }
        });
    }
    _applyAccessibilityAttributes($element) {
        var _treeViewPopup$find;
        const treeViewPopup = $element.closest(".dx-overlay-content");
        null === treeViewPopup || void 0 === treeViewPopup || treeViewPopup.removeAttr("role");
        const treeViewNode = null === treeViewPopup || void 0 === treeViewPopup || null === (_treeViewPopup$find = treeViewPopup.find) || void 0 === _treeViewPopup$find ? void 0 : _treeViewPopup$find.call(treeViewPopup, ".".concat(TREEVIEW_NODE_CONTAINER));
        null === treeViewNode || void 0 === treeViewNode || treeViewNode.attr("role", "presentation");
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("dxFilterBuilder", FilterBuilder);
const __TURBOPACK__default__export__ = FilterBuilder;
}),
"[project]/node_modules/devextreme/esm/__internal/m_draggable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/m_draggable.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/position.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/translator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/component_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$empty_template$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/empty_template.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_empty_template$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_empty_template.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/position.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_position.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$string$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/string.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_string.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$view_port$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/view_port.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_view_port$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_view_port.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/widget/dom_component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$ui$2f$scroll_view$2f$m_animator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/ui/scroll_view/m_animator.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
const KEYDOWN_EVENT = "keydown";
const DRAGGABLE = "dxDraggable";
const DRAGSTART_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["start"], DRAGGABLE);
const DRAG_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["move"], DRAGGABLE);
const DRAGEND_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["end"], DRAGGABLE);
const DRAG_ENTER_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["enter"], DRAGGABLE);
const DRAGEND_LEAVE_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["leave"], DRAGGABLE);
const POINTERDOWN_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].down, DRAGGABLE);
const KEYDOWN_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("keydown", DRAGGABLE);
const CLONE_CLASS = "clone";
let targetDraggable;
let sourceDraggable;
const ANONYMOUS_TEMPLATE_NAME = "content";
const getMousePosition = (event)=>({
        x: event.pageX - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(window).scrollLeft(),
        y: event.pageY - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(window).scrollTop()
    });
const GESTURE_COVER_CLASS = "dx-gesture-cover";
const OVERLAY_WRAPPER_CLASS = "dx-overlay-wrapper";
const OVERLAY_CONTENT_CLASS = "dx-overlay-content";
class ScrollHelper {
    updateScrollable(elements, mousePosition) {
        let isScrollableFound = false;
        elements.some((element)=>{
            const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
            const isTargetOverOverlayWrapper = $element.hasClass("dx-overlay-wrapper");
            const isTargetOverOverlayContent = $element.hasClass("dx-overlay-content");
            if (isTargetOverOverlayWrapper || isTargetOverOverlayContent) {
                return true;
            }
            isScrollableFound = this._trySetScrollable(element, mousePosition);
            return isScrollableFound;
        });
        if (!isScrollableFound) {
            this._$scrollableAtPointer = null;
            this._scrollSpeed = 0;
        }
    }
    isScrolling() {
        return !!this._scrollSpeed;
    }
    isScrollable($element) {
        return ("auto" === $element.css(this._overFlowAttr) || $element.hasClass("dx-scrollable-container")) && $element.prop(this._scrollSizeProp) > Math.ceil("width" === this._sizeAttr ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])($element) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])($element));
    }
    _trySetScrollable(element, mousePosition) {
        const that = this;
        const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
        let distanceToBorders;
        const sensitivity = that._component.option("scrollSensitivity");
        let isScrollable = that.isScrollable($element);
        if (isScrollable) {
            distanceToBorders = that._calculateDistanceToBorders($element, mousePosition);
            if (sensitivity > distanceToBorders[that._limitProps.start]) {
                if (!that._preventScroll) {
                    that._scrollSpeed = -that._calculateScrollSpeed(distanceToBorders[that._limitProps.start]);
                    that._$scrollableAtPointer = $element;
                }
            } else if (sensitivity > distanceToBorders[that._limitProps.end]) {
                if (!that._preventScroll) {
                    that._scrollSpeed = that._calculateScrollSpeed(distanceToBorders[that._limitProps.end]);
                    that._$scrollableAtPointer = $element;
                }
            } else {
                isScrollable = false;
                that._preventScroll = false;
            }
        }
        return isScrollable;
    }
    _calculateDistanceToBorders($area, mousePosition) {
        const area = $area.get(0);
        let areaBoundingRect;
        if (area) {
            areaBoundingRect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBoundingRect"])(area);
            return {
                left: mousePosition.x - areaBoundingRect.left,
                top: mousePosition.y - areaBoundingRect.top,
                right: areaBoundingRect.right - mousePosition.x,
                bottom: areaBoundingRect.bottom - mousePosition.y
            };
        }
        return {};
    }
    _calculateScrollSpeed(distance) {
        const component = this._component;
        const sensitivity = component.option("scrollSensitivity");
        const maxSpeed = component.option("scrollSpeed");
        return Math.ceil(((sensitivity - distance) / sensitivity) ** 2 * maxSpeed);
    }
    scrollByStep() {
        const that = this;
        if (that._$scrollableAtPointer && that._scrollSpeed) {
            if (that._$scrollableAtPointer.hasClass("dx-scrollable-container")) {
                const $scrollable = that._$scrollableAtPointer.closest(".dx-scrollable");
                const scrollableInstance = $scrollable.data("dxScrollable") || $scrollable.data("dxScrollView");
                if (scrollableInstance) {
                    const nextScrollPosition = scrollableInstance.scrollOffset()[that._limitProps.start] + that._scrollSpeed;
                    scrollableInstance.scrollTo({
                        [that._limitProps.start]: nextScrollPosition
                    });
                }
            } else {
                const nextScrollPosition = that._$scrollableAtPointer[that._scrollValue]() + that._scrollSpeed;
                that._$scrollableAtPointer[that._scrollValue](nextScrollPosition);
            }
            const dragMoveArgs = that._component._dragMoveArgs;
            if (dragMoveArgs) {
                that._component._dragMoveHandler(dragMoveArgs);
            }
        }
    }
    reset() {
        this._$scrollableAtPointer = null;
        this._scrollSpeed = 0;
        this._preventScroll = true;
    }
    isOutsideScrollable($scrollable, event) {
        if (!$scrollable) {
            return false;
        }
        const scrollableSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBoundingRect"])($scrollable.get(0));
        const start = scrollableSize[this._limitProps.start];
        const size = scrollableSize[this._sizeAttr];
        const mousePosition = getMousePosition(event);
        const location = "width" === this._sizeAttr ? mousePosition.x : mousePosition.y;
        return location < start || location > start + size;
    }
    constructor(orientation, component){
        this._$scrollableAtPointer = null;
        this._preventScroll = true;
        this._component = component;
        if ("vertical" === orientation) {
            this._scrollValue = "scrollTop";
            this._overFlowAttr = "overflowY";
            this._sizeAttr = "height";
            this._scrollSizeProp = "scrollHeight";
            this._clientSizeProp = "clientHeight";
            this._limitProps = {
                start: "top",
                end: "bottom"
            };
        } else {
            this._scrollValue = "scrollLeft";
            this._overFlowAttr = "overflowX";
            this._sizeAttr = "width";
            this._scrollSizeProp = "scrollWidth";
            this._clientSizeProp = "clientWidth";
            this._limitProps = {
                start: "left",
                end: "right"
            };
        }
    }
}
class ScrollAnimator extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$ui$2f$scroll_view$2f$m_animator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    ctor(strategy) {
        super.ctor();
        this._strategy = strategy;
    }
    _step() {
        const horizontalScrollHelper = this._strategy._horizontalScrollHelper;
        const verticalScrollHelper = this._strategy._verticalScrollHelper;
        null === horizontalScrollHelper || void 0 === horizontalScrollHelper || horizontalScrollHelper.scrollByStep();
        null === verticalScrollHelper || void 0 === verticalScrollHelper || verticalScrollHelper.scrollByStep();
    }
}
class Draggable extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    reset() {}
    dragMove(e) {}
    dragEnter() {}
    dragLeave() {}
    dragEnd(sourceEvent) {
        const sourceDraggable = this._getSourceDraggable();
        sourceDraggable._fireRemoveEvent(sourceEvent);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"])().resolve();
    }
    _fireRemoveEvent(sourceEvent) {}
    _getDefaultOptions() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, super._getDefaultOptions(), {
            onDragStart: null,
            onDragMove: null,
            onDragEnd: null,
            onDragEnter: null,
            onDragLeave: null,
            onDragCancel: null,
            onCancelByEsc: false,
            onDrop: null,
            immediate: true,
            dragDirection: "both",
            boundOffset: 0,
            allowMoveByClick: false,
            itemData: null,
            contentTemplate: "content",
            handle: "",
            filter: "",
            clone: false,
            autoScroll: true,
            scrollSpeed: 30,
            scrollSensitivity: 60
        });
    }
    _setOptionsByReference() {
        super._setOptionsByReference.apply(this, arguments);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this._optionsByReference, {
            component: true,
            group: true,
            itemData: true,
            data: true
        });
    }
    _init() {
        super._init();
        this._attachEventHandlers();
        this._scrollAnimator = new ScrollAnimator(this);
        this._horizontalScrollHelper = new ScrollHelper("horizontal", this);
        this._verticalScrollHelper = new ScrollHelper("vertical", this);
        this._initScrollTop = 0;
        this._initScrollLeft = 0;
    }
    _normalizeCursorOffset(offset) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isObject"])(offset)) {
            offset = {
                h: offset.x,
                v: offset.y
            };
        }
        offset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["splitPair"])(offset).map((value)=>parseFloat(value));
        return {
            left: offset[0],
            top: 1 === offset.length ? offset[0] : offset[1]
        };
    }
    _getNormalizedCursorOffset(offset, options) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(offset)) {
            offset = offset.call(this, options);
        }
        return this._normalizeCursorOffset(offset);
    }
    _calculateElementOffset(options) {
        let elementOffset;
        let dragElementOffset;
        const { event: event } = options;
        const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options.itemElement);
        const $dragElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options.dragElement);
        const isCloned = this._dragElementIsCloned();
        const cursorOffset = this.option("cursorOffset");
        let normalizedCursorOffset = {
            left: 0,
            top: 0
        };
        const currentLocate = this._initialLocate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["locate"])($dragElement);
        if (isCloned || options.initialOffset || cursorOffset) {
            elementOffset = options.initialOffset || $element.offset();
            if (cursorOffset) {
                normalizedCursorOffset = this._getNormalizedCursorOffset(cursorOffset, options);
                if (isFinite(normalizedCursorOffset.left)) {
                    elementOffset.left = event.pageX;
                }
                if (isFinite(normalizedCursorOffset.top)) {
                    elementOffset.top = event.pageY;
                }
            }
            dragElementOffset = $dragElement.offset();
            elementOffset.top -= dragElementOffset.top + (normalizedCursorOffset.top || 0) - currentLocate.top;
            elementOffset.left -= dragElementOffset.left + (normalizedCursorOffset.left || 0) - currentLocate.left;
        }
        return elementOffset;
    }
    _initPosition(options) {
        const $dragElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options.dragElement);
        const elementOffset = this._calculateElementOffset(options);
        if (elementOffset) {
            this._move(elementOffset, $dragElement);
        }
        this._startPosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["locate"])($dragElement);
    }
    _startAnimator() {
        if (!this._scrollAnimator.inProgress()) {
            this._scrollAnimator.start();
        }
    }
    _stopAnimator() {
        this._scrollAnimator.stop();
    }
    _addWidgetPrefix(className) {
        const componentName = this.NAME;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["dasherize"])(componentName) + (className ? "-".concat(className) : "");
    }
    _getItemsSelector() {
        return this.option("filter") || "";
    }
    _$content() {
        const $element = this.$element();
        const $wrapper = $element.children(".dx-template-wrapper");
        return $wrapper.length ? $wrapper : $element;
    }
    _attachEventHandlers() {
        if (this.option("disabled")) {
            return;
        }
        let $element = this._$content();
        let itemsSelector = this._getItemsSelector();
        const allowMoveByClick = this.option("allowMoveByClick");
        const data = {
            direction: this.option("dragDirection"),
            immediate: this.option("immediate"),
            checkDropTarget: ($target, event)=>{
                const targetGroup = this.option("group");
                const sourceGroup = this._getSourceDraggable().option("group");
                const $scrollable = this._getScrollable($target);
                if (this._verticalScrollHelper.isOutsideScrollable($scrollable, event) || this._horizontalScrollHelper.isOutsideScrollable($scrollable, event)) {
                    return false;
                }
                return sourceGroup && sourceGroup === targetGroup;
            }
        };
        if (allowMoveByClick) {
            $element = this._getArea();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, POINTERDOWN_EVENT_NAME, data, this._pointerDownHandler.bind(this));
        }
        if (">" === itemsSelector[0]) {
            itemsSelector = itemsSelector.slice(1);
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, DRAGSTART_EVENT_NAME, itemsSelector, data, this._dragStartHandler.bind(this));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, DRAG_EVENT_NAME, data, this._dragMoveHandler.bind(this));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, DRAGEND_EVENT_NAME, data, this._dragEndHandler.bind(this));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, DRAG_ENTER_EVENT_NAME, data, this._dragEnterHandler.bind(this));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, DRAGEND_LEAVE_EVENT_NAME, data, this._dragLeaveHandler.bind(this));
        if (this.option("onCancelByEsc")) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, KEYDOWN_EVENT_NAME, this._keydownHandler.bind(this));
        }
    }
    _dragElementIsCloned() {
        var _this$_$dragElement;
        return null === (_this$_$dragElement = this._$dragElement) || void 0 === _this$_$dragElement ? void 0 : _this$_$dragElement.hasClass(this._addWidgetPrefix("clone"));
    }
    _getDragTemplateArgs($element, $container) {
        return {
            container: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])($container),
            model: {
                itemData: this.option("itemData"),
                itemElement: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])($element)
            }
        };
    }
    _createDragElement($element) {
        let result = $element;
        const clone = this.option("clone");
        const $container = this._getContainer();
        let template = this.option("dragTemplate");
        if (template) {
            template = this._getTemplate(template);
            result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").appendTo($container);
            template.render(this._getDragTemplateArgs($element, result));
        } else if (clone) {
            result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").appendTo($container);
            $element.clone().css({
                width: $element.css("width"),
                height: $element.css("height")
            }).appendTo(result);
        }
        return result.toggleClass(this._addWidgetPrefix("clone"), result.get(0) !== $element.get(0)).toggleClass("dx-rtl", this.option("rtlEnabled"));
    }
    _resetDragElement() {
        if (this._dragElementIsCloned()) {
            var _this$_$dragElement2;
            null === (_this$_$dragElement2 = this._$dragElement) || void 0 === _this$_$dragElement2 || _this$_$dragElement2.remove();
        } else {
            this._toggleDraggingClass(false);
        }
        this._$dragElement = null;
    }
    _resetSourceElement() {
        this._toggleDragSourceClass(false);
        this._$sourceElement = null;
    }
    _detachEventHandlers() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._$content(), ".".concat(DRAGGABLE));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._getArea(), ".".concat(DRAGGABLE));
    }
    _move(position, $element) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["move"])($element || this._$dragElement, position);
    }
    _getDraggableElement(e) {
        const $sourceElement = this._getSourceElement();
        if ($sourceElement) {
            return $sourceElement;
        }
        const allowMoveByClick = this.option("allowMoveByClick");
        if (allowMoveByClick) {
            return this.$element();
        }
        let $target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(null === e || void 0 === e ? void 0 : e.target);
        const itemsSelector = this._getItemsSelector();
        if (">" === itemsSelector[0]) {
            const $items = this._$content().find(itemsSelector);
            if (!$items.is($target)) {
                $target = $target.closest($items);
            }
        }
        return $target;
    }
    _getSourceElement() {
        const draggable = this._getSourceDraggable();
        return draggable._$sourceElement;
    }
    _pointerDownHandler(e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["needSkipEvent"])(e)) {
            return;
        }
        const position = {};
        const $element = this.$element();
        const { dragDirection: dragDirection } = this.option();
        if ("horizontal" === dragDirection || "both" === dragDirection) {
            position.left = e.pageX - $element.offset().left + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["locate"])($element).left - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])($element) / 2;
        }
        if ("vertical" === dragDirection || "both" === dragDirection) {
            position.top = e.pageY - $element.offset().top + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["locate"])($element).top - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])($element) / 2;
        }
        this._move(position, $element);
        this._getAction("onDragMove")(this._getEventArgs(e));
    }
    _isValidElement(event, $element) {
        var _event$originalEvent;
        const { handle: handle } = this.option();
        const $target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(null === (_event$originalEvent = event.originalEvent) || void 0 === _event$originalEvent ? void 0 : _event$originalEvent.target);
        if (handle && !$target.closest(handle).length) {
            return false;
        }
        if (!$element.length) {
            return false;
        }
        return !$element.is(".dx-state-disabled, .dx-state-disabled *");
    }
    _dragStartHandler(e) {
        const $element = this._getDraggableElement(e);
        this.dragInProgress = true;
        if (!this._isValidElement(e, $element)) {
            e.cancel = true;
            return;
        }
        if (this._$sourceElement) {
            return;
        }
        const dragStartArgs = this._getDragStartArgs(e, $element);
        this._getAction("onDragStart")(dragStartArgs);
        if (dragStartArgs.cancel) {
            e.cancel = true;
            return;
        }
        this.option("itemData", dragStartArgs.itemData);
        this._setSourceDraggable();
        this._$sourceElement = $element;
        let initialOffset = $element.offset();
        if (!this._hasClonedDraggable() && this.option("autoScroll")) {
            this._initScrollTop = this._getScrollableScrollTop();
            this._initScrollLeft = this._getScrollableScrollLeft();
            initialOffset = this._getDraggableElementOffset(initialOffset.left, initialOffset.top);
        }
        const $dragElement = this._$dragElement = this._createDragElement($element);
        this._toggleDraggingClass(true);
        this._toggleDragSourceClass(true);
        this._setGestureCoverCursor($dragElement.children());
        const isFixedPosition = "fixed" === $dragElement.css("position");
        this._initPosition((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, dragStartArgs, {
            dragElement: $dragElement.get(0),
            initialOffset: isFixedPosition && initialOffset
        }));
        this._getAction("onDraggableElementShown")((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, dragStartArgs, {
            dragElement: $dragElement
        }));
        const $area = this._getArea();
        const areaOffset = this._getAreaOffset($area);
        const boundOffset = this._getBoundOffset();
        const areaWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($area);
        const areaHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($area);
        const elementWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])($dragElement);
        const elementHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])($dragElement);
        const startOffset_left = $dragElement.offset().left - areaOffset.left, startOffset_top = $dragElement.offset().top - areaOffset.top;
        if ($area.length) {
            e.maxLeftOffset = startOffset_left - boundOffset.left;
            e.maxRightOffset = areaWidth - startOffset_left - elementWidth - boundOffset.right;
            e.maxTopOffset = startOffset_top - boundOffset.top;
            e.maxBottomOffset = areaHeight - startOffset_top - elementHeight - boundOffset.bottom;
        }
        if (this.option("autoScroll")) {
            this._startAnimator();
        }
    }
    _getAreaOffset($area) {
        const offset = $area && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].offset($area);
        return offset || {
            left: 0,
            top: 0
        };
    }
    _toggleDraggingClass(value) {
        var _this$_$dragElement3;
        null === (_this$_$dragElement3 = this._$dragElement) || void 0 === _this$_$dragElement3 || _this$_$dragElement3.toggleClass(this._addWidgetPrefix("dragging"), value);
    }
    _toggleDragSourceClass(value, $element) {
        const $sourceElement = $element || this._$sourceElement;
        null === $sourceElement || void 0 === $sourceElement || $sourceElement.toggleClass(this._addWidgetPrefix("source"), value);
    }
    _setGestureCoverCursor($element) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(".dx-gesture-cover").css("cursor", $element.css("cursor"));
    }
    _getBoundOffset() {
        let boundOffset = this.option("boundOffset");
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(boundOffset)) {
            boundOffset = boundOffset.call(this);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["quadToObject"])(boundOffset);
    }
    _getArea() {
        let area = this.option("boundary");
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(area)) {
            area = area.call(this);
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(area);
    }
    _getContainer() {
        let { container: container } = this.option();
        if (void 0 === container) {
            container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_view_port$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["value"])();
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(container);
    }
    _getDraggableElementOffset(initialOffsetX, initialOffsetY) {
        var _this$_startPosition, _this$_startPosition2;
        const initScrollTop = this._initScrollTop;
        const initScrollLeft = this._initScrollLeft;
        const scrollTop = this._getScrollableScrollTop();
        const scrollLeft = this._getScrollableScrollLeft();
        const elementPosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.element()).css("position");
        const isFixedPosition = "fixed" === elementPosition;
        var _ref, _ref1;
        const result = {
            left: ((_ref = null === (_this$_startPosition = this._startPosition) || void 0 === _this$_startPosition ? void 0 : _this$_startPosition.left) !== null && _ref !== void 0 ? _ref : 0) + initialOffsetX,
            top: ((_ref1 = null === (_this$_startPosition2 = this._startPosition) || void 0 === _this$_startPosition2 ? void 0 : _this$_startPosition2.top) !== null && _ref1 !== void 0 ? _ref1 : 0) + initialOffsetY
        };
        if (isFixedPosition || this._hasClonedDraggable()) {
            return result;
        }
        return {
            left: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumeric"])(scrollLeft) ? result.left + scrollLeft - initScrollLeft : result.left,
            top: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumeric"])(scrollTop) ? result.top + scrollTop - initScrollTop : result.top
        };
    }
    _hasClonedDraggable() {
        return this.option("clone") || this.option("dragTemplate");
    }
    _dragMoveHandler(e) {
        this._dragMoveArgs = e;
        if (!this._$dragElement) {
            e.cancel = true;
            return;
        }
        const offset = this._getDraggableElementOffset(e.offset.x, e.offset.y);
        this._move(offset);
        this._updateScrollable(e);
        const eventArgs = this._getEventArgs(e);
        this._getAction("onDragMove")(eventArgs);
        if (true === eventArgs.cancel) {
            return;
        }
        const targetDraggable = this._getTargetDraggable();
        targetDraggable.dragMove(e, scrollBy);
    }
    _updateScrollable(e) {
        const that = this;
        if (that.option("autoScroll")) {
            const mousePosition = getMousePosition(e);
            const allObjects = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].elementsFromPoint(mousePosition.x, mousePosition.y, this.$element().get(0));
            that._verticalScrollHelper.updateScrollable(allObjects, mousePosition);
            that._horizontalScrollHelper.updateScrollable(allObjects, mousePosition);
        }
    }
    _getScrollable($element) {
        let $scrollable;
        $element.parents().toArray().some((parent)=>{
            const $parent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(parent);
            if (this._horizontalScrollHelper.isScrollable($parent) || this._verticalScrollHelper.isScrollable($parent)) {
                $scrollable = $parent;
                return true;
            }
            return false;
        });
        return $scrollable;
    }
    _getScrollableScrollTop() {
        var _this$_getScrollable;
        var _ref;
        return (_ref = null === (_this$_getScrollable = this._getScrollable((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.element()))) || void 0 === _this$_getScrollable ? void 0 : _this$_getScrollable.scrollTop()) !== null && _ref !== void 0 ? _ref : 0;
    }
    _getScrollableScrollLeft() {
        var _this$_getScrollable2;
        var _ref;
        return (_ref = null === (_this$_getScrollable2 = this._getScrollable((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this.element()))) || void 0 === _this$_getScrollable2 ? void 0 : _this$_getScrollable2.scrollLeft()) !== null && _ref !== void 0 ? _ref : 0;
    }
    _defaultActionArgs() {
        const args = super._defaultActionArgs.apply(this, arguments);
        const component = this.option("component");
        if (component) {
            args.component = component;
            args.element = component.element();
        }
        return args;
    }
    _getEventArgs(e) {
        const sourceDraggable = this._getSourceDraggable();
        const targetDraggable = this._getTargetDraggable();
        return {
            event: e,
            itemData: sourceDraggable.option("itemData"),
            itemElement: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])(sourceDraggable._$sourceElement),
            fromComponent: sourceDraggable.option("component") || sourceDraggable,
            toComponent: targetDraggable.option("component") || targetDraggable,
            fromData: sourceDraggable.option("data"),
            toData: targetDraggable.option("data")
        };
    }
    _getDragStartArgs(e, $itemElement) {
        const args = this._getEventArgs(e);
        return {
            event: args.event,
            itemData: args.itemData,
            itemElement: $itemElement,
            fromData: args.fromData
        };
    }
    _revertItemToInitialPosition() {
        !this._dragElementIsCloned() && this._move(this._initialLocate, this._$sourceElement);
    }
    _dragEndHandler(e) {
        const d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"])();
        const dragEndEventArgs = this._getEventArgs(e);
        const dropEventArgs = this._getEventArgs(e);
        const targetDraggable = this._getTargetDraggable();
        let needRevertPosition = true;
        this.dragInProgress = false;
        try {
            this._getAction("onDragEnd")(dragEndEventArgs);
        } finally{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromPromise"])(dragEndEventArgs.cancel)).done((cancel)=>{
                if (!cancel) {
                    if (targetDraggable !== this) {
                        targetDraggable._getAction("onDrop")(dropEventArgs);
                    }
                    if (!dropEventArgs.cancel) {
                        needRevertPosition = false;
                        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fromPromise"])(targetDraggable.dragEnd(dragEndEventArgs))).always(d.resolve);
                        return;
                    }
                }
                d.resolve();
            }).fail(d.resolve);
            d.done(()=>{
                if (needRevertPosition) {
                    this._revertItemToInitialPosition();
                }
                this._resetDragOptions(targetDraggable);
            });
        }
    }
    _isTargetOverAnotherDraggable(e) {
        const sourceDraggable = this._getSourceDraggable();
        if (this === sourceDraggable) {
            return false;
        }
        const $dragElement = sourceDraggable._$dragElement;
        const $sourceDraggableElement = sourceDraggable.$element();
        const $targetDraggableElement = this.$element();
        const mousePosition = getMousePosition(e);
        const elements = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].elementsFromPoint(mousePosition.x, mousePosition.y, this.element());
        const firstWidgetElement = elements.filter((element)=>{
            const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
            if ($element.hasClass(this._addWidgetPrefix())) {
                return !$element.closest($dragElement).length;
            }
            return false;
        })[0];
        const $sourceElement = this._getSourceElement();
        const isTargetOverItself = firstWidgetElement === $sourceDraggableElement.get(0);
        const isTargetOverNestedDraggable = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(firstWidgetElement).closest($sourceElement).length;
        return !firstWidgetElement || firstWidgetElement === $targetDraggableElement.get(0) && !isTargetOverItself && !isTargetOverNestedDraggable;
    }
    _dragEnterHandler(e) {
        this._fireDragEnterEvent(e);
        if (this._isTargetOverAnotherDraggable(e)) {
            this._setTargetDraggable();
        }
        const sourceDraggable = this._getSourceDraggable();
        sourceDraggable.dragEnter(e);
    }
    _dragLeaveHandler(e) {
        this._fireDragLeaveEvent(e);
        this._resetTargetDraggable();
        if (this !== this._getSourceDraggable()) {
            this.reset();
        }
        const sourceDraggable = this._getSourceDraggable();
        sourceDraggable.dragLeave(e);
    }
    _keydownHandler(e) {
        if (this.dragInProgress && "Escape" === e.key) {
            this._keydownEscapeHandler(e);
        }
    }
    _keydownEscapeHandler(e) {
        var _sourceDraggable;
        const $sourceElement = this._getSourceElement();
        if (!$sourceElement) {
            return;
        }
        const dragCancelEventArgs = this._getEventArgs(e);
        this._getAction("onDragCancel")(dragCancelEventArgs);
        if (dragCancelEventArgs.cancel) {
            return;
        }
        this.dragInProgress = false;
        null === (_sourceDraggable = sourceDraggable) || void 0 === _sourceDraggable || _sourceDraggable._toggleDraggingClass(false);
        this._detachEventHandlers();
        this._revertItemToInitialPosition();
        const targetDraggable = this._getTargetDraggable();
        this._resetDragOptions(targetDraggable);
        this._attachEventHandlers();
    }
    _getAction(name) {
        return this["_".concat(name, "Action")] || this._createActionByOption(name);
    }
    _getAnonymousTemplateName() {
        return "content";
    }
    _initTemplates() {
        if (!this.option("contentTemplate")) {
            return;
        }
        this._templateManager.addDefaultTemplates({
            content: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_empty_template$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["EmptyTemplate"]
        });
        super._initTemplates.apply(this, arguments);
    }
    _render() {
        super._render();
        this.$element().addClass(this._addWidgetPrefix());
        const transclude = this._templateManager.anonymousTemplateName === this.option("contentTemplate");
        const template = this._getTemplateByOption("contentTemplate");
        if (template) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(template.render({
                container: this.element(),
                transclude: transclude
            }));
        }
    }
    _optionChanged(args) {
        const { name: name } = args;
        switch(name){
            case "onDragStart":
            case "onDragMove":
            case "onDragEnd":
            case "onDrop":
            case "onDragEnter":
            case "onDragLeave":
            case "onDragCancel":
            case "onDraggableElementShown":
                this["_".concat(name, "Action")] = this._createActionByOption(name);
                break;
            case "dragTemplate":
            case "contentTemplate":
            case "container":
            case "clone":
            case "scrollSensitivity":
            case "scrollSpeed":
            case "boundOffset":
            case "handle":
            case "group":
            case "data":
            case "itemData":
                break;
            case "allowMoveByClick":
            case "dragDirection":
            case "disabled":
            case "boundary":
            case "filter":
            case "immediate":
                this._resetDragElement();
                this._detachEventHandlers();
                this._attachEventHandlers();
                break;
            case "onCancelByEsc":
                this._keydownHandler();
                break;
            case "autoScroll":
                this._verticalScrollHelper.reset();
                this._horizontalScrollHelper.reset();
                break;
            default:
                super._optionChanged(args);
        }
    }
    _getTargetDraggable() {
        return targetDraggable || this;
    }
    _getSourceDraggable() {
        return sourceDraggable || this;
    }
    _setTargetDraggable() {
        const currentGroup = this.option("group");
        const sourceDraggable = this._getSourceDraggable();
        if (currentGroup && currentGroup === sourceDraggable.option("group")) {
            targetDraggable = this;
        }
    }
    _setSourceDraggable() {
        sourceDraggable = this;
    }
    _resetSourceDraggable() {
        sourceDraggable = null;
    }
    _resetTargetDraggable() {
        targetDraggable = null;
    }
    _resetDragOptions(targetDraggable) {
        this.reset();
        targetDraggable.reset();
        this._stopAnimator();
        this._horizontalScrollHelper.reset();
        this._verticalScrollHelper.reset();
        this._resetDragElement();
        this._resetSourceElement();
        this._resetTargetDraggable();
        this._resetSourceDraggable();
    }
    _dispose() {
        super._dispose();
        this._detachEventHandlers();
        this._resetDragElement();
        this._resetTargetDraggable();
        this._resetSourceDraggable();
        this._$sourceElement = null;
        this._stopAnimator();
    }
    _fireDragEnterEvent(sourceEvent) {
        const args = this._getEventArgs(sourceEvent);
        this._getAction("onDragEnter")(args);
    }
    _fireDragLeaveEvent(sourceEvent) {
        const args = this._getEventArgs(sourceEvent);
        this._getAction("onDragLeave")(args);
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(DRAGGABLE, Draggable);
const __TURBOPACK__default__export__ = Draggable;
}),
"[project]/node_modules/devextreme/esm/__internal/m_sortable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/m_sortable.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__fx$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/fx.js [app-client] (ecmascript) <export default as fx>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/translator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/component_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/position.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_position.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$m_draggable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/m_draggable.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
const SORTABLE = "dxSortable";
const PLACEHOLDER_CLASS = "placeholder";
const CLONE_CLASS = "clone";
const isElementVisible = (itemElement)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(itemElement).is(":visible");
const animate = (element, config)=>{
    var _config$to, _config$to2;
    if (!element) {
        return;
    }
    const left = (null === (_config$to = config.to) || void 0 === _config$to ? void 0 : _config$to.left) || 0;
    const top = (null === (_config$to2 = config.to) || void 0 === _config$to2 ? void 0 : _config$to2.top) || 0;
    element.style.transform = "translate(".concat(left, "px,").concat(top, "px)");
    element.style.transition = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$fx$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$export__default__as__fx$3e$__["fx"].off ? "" : "transform ".concat(config.duration, "ms ").concat(config.easing);
};
const stopAnimation = (element)=>{
    if (!element) {
        return;
    }
    element.style.transform = "";
    element.style.transition = "";
};
function getScrollableBoundary($scrollable) {
    const offset = $scrollable.offset();
    const { style: style } = $scrollable[0];
    const paddingLeft = parseFloat(style.paddingLeft) || 0;
    const paddingRight = parseFloat(style.paddingRight) || 0;
    const paddingTop = parseFloat(style.paddingTop) || 0;
    const width = $scrollable[0].clientWidth - (paddingLeft + paddingRight);
    const height = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])($scrollable);
    const left = offset.left + paddingLeft;
    const top = offset.top + paddingTop;
    return {
        left: left,
        right: left + width,
        top: top,
        bottom: top + height
    };
}
class Sortable extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$m_draggable$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    _init() {
        super._init();
        this._sourceScrollHandler = this._handleSourceScroll.bind(this);
        this._sourceScrollableInfo = null;
    }
    _getDefaultOptions() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, super._getDefaultOptions(), {
            clone: true,
            filter: "> *",
            itemOrientation: "vertical",
            dropFeedbackMode: "push",
            allowDropInsideItem: false,
            allowReordering: true,
            moveItemOnDrop: false,
            onDragChange: null,
            onAdd: null,
            onRemove: null,
            onReorder: null,
            onPlaceholderPrepared: null,
            placeholderClassName: "",
            animation: {
                type: "slide",
                duration: 300,
                easing: "ease"
            },
            fromIndex: null,
            toIndex: null,
            dropInsideItem: false,
            itemPoints: null,
            fromIndexOffset: 0,
            offset: 0,
            autoUpdate: false,
            draggableElementSize: 0
        });
    }
    reset() {
        this.option({
            dropInsideItem: false,
            toIndex: null,
            fromIndex: null,
            itemPoints: null,
            fromIndexOffset: 0,
            draggableElementSize: 0
        });
        if (this._$placeholderElement) {
            this._$placeholderElement.remove();
        }
        this._$placeholderElement = null;
        if (!this._isIndicateMode() && this._$modifiedItem) {
            this._$modifiedItem.css("marginBottom", this._modifiedItemMargin);
            this._$modifiedItem = null;
        }
    }
    _getPrevVisibleItem(items, index) {
        return items.slice(0, index).reverse().filter(isElementVisible)[0];
    }
    _dragStartHandler(e) {
        super._dragStartHandler.apply(this, arguments);
        if (true === e.cancel) {
            return;
        }
        const $sourceElement = this._getSourceElement();
        this._updateItemPoints();
        this._subscribeToSourceScroll(e);
        this.option("fromIndex", this._getElementIndex($sourceElement));
        this.option("fromIndexOffset", this.option("offset"));
    }
    _subscribeToSourceScroll(e) {
        const $scrollable = this._getScrollable((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e.target));
        if ($scrollable) {
            this._sourceScrollableInfo = {
                element: $scrollable,
                scrollLeft: $scrollable.scrollLeft(),
                scrollTop: $scrollable.scrollTop()
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($scrollable, "scroll", this._sourceScrollHandler);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($scrollable, "scroll", this._sourceScrollHandler);
        }
    }
    _unsubscribeFromSourceScroll() {
        if (this._sourceScrollableInfo) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._sourceScrollableInfo.element, "scroll", this._sourceScrollHandler);
            this._sourceScrollableInfo = null;
        }
    }
    _handleSourceScroll(e) {
        const sourceScrollableInfo = this._sourceScrollableInfo;
        if (sourceScrollableInfo) {
            [
                "scrollLeft",
                "scrollTop"
            ].forEach((scrollProp)=>{
                if (e.target[scrollProp] !== sourceScrollableInfo[scrollProp]) {
                    const scrollBy = e.target[scrollProp] - sourceScrollableInfo[scrollProp];
                    this._correctItemPoints(scrollBy);
                    this._movePlaceholder();
                    sourceScrollableInfo[scrollProp] = e.target[scrollProp];
                }
            });
        }
    }
    _dragEnterHandler(e) {
        super._dragEnterHandler.apply(this, arguments);
        if (this === this._getSourceDraggable()) {
            return;
        }
        this._subscribeToSourceScroll(e);
        this._updateItemPoints();
        this.option("fromIndex", -1);
        if (!this._isIndicateMode()) {
            const itemPoints = this.option("itemPoints");
            const lastItemPoint = itemPoints[itemPoints.length - 1];
            if (lastItemPoint) {
                const $element = this.$element();
                const $sourceElement = this._getSourceElement();
                const isVertical = this._isVerticalOrientation();
                const sourceElementSize = isVertical ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($sourceElement, true) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($sourceElement, true);
                const scrollSize = $element.get(0)[isVertical ? "scrollHeight" : "scrollWidth"];
                const scrollPosition = $element.get(0)[isVertical ? "scrollTop" : "scrollLeft"];
                const positionProp = isVertical ? "top" : "left";
                const lastPointPosition = lastItemPoint[positionProp];
                const elementPosition = $element.offset()[positionProp];
                const freeSize = elementPosition + scrollSize - scrollPosition - lastPointPosition;
                if (freeSize < sourceElementSize) {
                    if (isVertical) {
                        const items = this._getItems();
                        const $lastItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this._getPrevVisibleItem(items));
                        this._$modifiedItem = $lastItem;
                        this._modifiedItemMargin = $lastItem.get(0).style.marginBottom;
                        $lastItem.css("marginBottom", sourceElementSize - freeSize);
                        const $sortable = $lastItem.closest(".dx-sortable");
                        const sortable = $sortable.data("dxScrollable") || $sortable.data("dxScrollView");
                        null === sortable || void 0 === sortable || sortable.update();
                    }
                }
            }
        }
    }
    _dragLeaveHandler() {
        super._dragLeaveHandler.apply(this, arguments);
        if (this !== this._getSourceDraggable()) {
            this._unsubscribeFromSourceScroll();
        }
    }
    dragEnter() {
        if (this !== this._getTargetDraggable()) {
            this.option("toIndex", -1);
        }
    }
    dragLeave() {
        if (this !== this._getTargetDraggable()) {
            this.option("toIndex", this.option("fromIndex"));
        }
    }
    _allowDrop(event) {
        const targetDraggable = this._getTargetDraggable();
        const $targetDraggable = targetDraggable.$element();
        const $scrollable = this._getScrollable($targetDraggable);
        if ($scrollable) {
            const { left: left, right: right, top: top, bottom: bottom } = getScrollableBoundary($scrollable);
            const toIndex = this.option("toIndex");
            const itemPoints = this.option("itemPoints");
            const itemPoint = null === itemPoints || void 0 === itemPoints ? void 0 : itemPoints.filter((item)=>item.index === toIndex)[0];
            if (itemPoint && void 0 !== itemPoint.top) {
                const isVertical = this._isVerticalOrientation();
                if (isVertical) {
                    return top <= Math.ceil(itemPoint.top) && Math.floor(itemPoint.top) <= bottom;
                }
                return left <= Math.ceil(itemPoint.left) && Math.floor(itemPoint.left) <= right;
            }
        }
        return true;
    }
    dragEnd(sourceEvent) {
        this._unsubscribeFromSourceScroll();
        const $sourceElement = this._getSourceElement();
        const sourceDraggable = this._getSourceDraggable();
        const isSourceDraggable = sourceDraggable.NAME !== this.NAME;
        const toIndex = this.option("toIndex");
        const { event: event } = sourceEvent;
        const allowDrop = this._allowDrop(event);
        if (null !== toIndex && toIndex >= 0 && allowDrop) {
            let cancelAdd;
            let cancelRemove;
            if (sourceDraggable !== this) {
                cancelAdd = this._fireAddEvent(event);
                if (!cancelAdd) {
                    cancelRemove = this._fireRemoveEvent(event);
                }
            }
            if (isSourceDraggable) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$translator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["resetPosition"])($sourceElement);
            }
            if (this.option("moveItemOnDrop")) {
                !cancelAdd && this._moveItem($sourceElement, toIndex, cancelRemove);
            }
            if (sourceDraggable === this) {
                return this._fireReorderEvent(event);
            }
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"])().resolve();
    }
    dragMove(e) {
        const itemPoints = this.option("itemPoints");
        if (!itemPoints) {
            return;
        }
        const isVertical = this._isVerticalOrientation();
        const axisName = isVertical ? "top" : "left";
        const cursorPosition = isVertical ? e.pageY : e.pageX;
        const rtlEnabled = this.option("rtlEnabled");
        let itemPoint;
        for(let i = itemPoints.length - 1; i >= 0; i--){
            const centerPosition = itemPoints[i + 1] && (itemPoints[i][axisName] + itemPoints[i + 1][axisName]) / 2;
            if ((!isVertical && rtlEnabled ? cursorPosition > centerPosition : centerPosition > cursorPosition) || void 0 === centerPosition) {
                itemPoint = itemPoints[i];
            } else {
                break;
            }
        }
        if (itemPoint) {
            this._updatePlaceholderPosition(e, itemPoint);
            if (this._verticalScrollHelper.isScrolling() && this._isIndicateMode()) {
                this._movePlaceholder();
            }
        }
    }
    _isIndicateMode() {
        return "indicate" === this.option("dropFeedbackMode") || this.option("allowDropInsideItem");
    }
    _createPlaceholder() {
        if (!this._isIndicateMode()) {
            return;
        }
        const customCssClass = this.option("placeholderClassName");
        this._$placeholderElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass(this._addWidgetPrefix("placeholder")).addClass(customCssClass !== null && customCssClass !== void 0 ? customCssClass : "").insertBefore(this._getSourceDraggable()._$dragElement);
        return this._$placeholderElement;
    }
    _getItems() {
        const itemsSelector = this._getItemsSelector();
        return this._$content().find(itemsSelector).not(".".concat(this._addWidgetPrefix("placeholder"))).not(".".concat(this._addWidgetPrefix("clone"))).toArray();
    }
    _allowReordering() {
        const sourceDraggable = this._getSourceDraggable();
        const targetDraggable = this._getTargetDraggable();
        return sourceDraggable !== targetDraggable || this.option("allowReordering");
    }
    _isValidPoint(visibleIndex, draggableVisibleIndex, dropInsideItem) {
        const allowDropInsideItem = this.option("allowDropInsideItem");
        const allowReordering = dropInsideItem || this._allowReordering();
        if (!allowReordering && (0 !== visibleIndex || !allowDropInsideItem)) {
            return false;
        }
        if (!this._isIndicateMode()) {
            return true;
        }
        return -1 === draggableVisibleIndex || visibleIndex !== draggableVisibleIndex && (dropInsideItem || visibleIndex !== draggableVisibleIndex + 1);
    }
    _getItemPoints() {
        const that = this;
        let result = [];
        let $item;
        let offset;
        let itemWidth;
        const { rtlEnabled: rtlEnabled } = that.option();
        const isVertical = that._isVerticalOrientation();
        const itemElements = that._getItems();
        const visibleItemElements = itemElements.filter(isElementVisible);
        const visibleItemCount = visibleItemElements.length;
        const $draggableItem = this._getDraggableElement();
        const draggableVisibleIndex = visibleItemElements.indexOf($draggableItem.get(0));
        if (visibleItemCount) {
            for(let i = 0; i <= visibleItemCount; i++){
                const needCorrectLeftPosition = !isVertical && rtlEnabled ^ i === visibleItemCount;
                const needCorrectTopPosition = isVertical && i === visibleItemCount;
                if (i < visibleItemCount) {
                    $item = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(visibleItemElements[i]);
                    offset = $item.offset();
                    itemWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($item);
                }
                result.push({
                    dropInsideItem: false,
                    left: offset.left + (needCorrectLeftPosition ? itemWidth : 0),
                    top: offset.top + (needCorrectTopPosition ? result[i - 1].height : 0),
                    index: i === visibleItemCount ? itemElements.length : itemElements.indexOf($item.get(0)),
                    $item: $item,
                    width: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($item),
                    height: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($item),
                    isValid: that._isValidPoint(i, draggableVisibleIndex)
                });
            }
            if (this.option("allowDropInsideItem")) {
                const points = result;
                result = [];
                for(let i = 0; i < points.length; i++){
                    result.push(points[i]);
                    if (points[i + 1]) {
                        result.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, points[i], {
                            dropInsideItem: true,
                            top: Math.floor((points[i].top + points[i + 1].top) / 2),
                            left: Math.floor((points[i].left + points[i + 1].left) / 2),
                            isValid: this._isValidPoint(i, draggableVisibleIndex, true)
                        }));
                    }
                }
            }
        } else {
            result.push({
                dropInsideItem: false,
                index: 0,
                isValid: true
            });
        }
        return result;
    }
    _updateItemPoints(forceUpdate) {
        if (forceUpdate || this.option("autoUpdate") || !this.option("itemPoints")) {
            this.option("itemPoints", this._getItemPoints());
        }
    }
    _correctItemPoints(scrollBy) {
        const itemPoints = this.option("itemPoints");
        if (scrollBy && itemPoints && !this.option("autoUpdate")) {
            const isVertical = this._isVerticalOrientation();
            const positionPropName = isVertical ? "top" : "left";
            itemPoints.forEach((itemPoint)=>{
                itemPoint[positionPropName] -= scrollBy;
            });
        }
    }
    _getElementIndex($itemElement) {
        return this._getItems().indexOf($itemElement.get(0));
    }
    _getDragTemplateArgs($element) {
        const args = super._getDragTemplateArgs.apply(this, arguments);
        args.model.fromIndex = this._getElementIndex($element);
        return args;
    }
    _togglePlaceholder(value) {
        var _this$_$placeholderEl;
        null === (_this$_$placeholderEl = this._$placeholderElement) || void 0 === _this$_$placeholderEl || _this$_$placeholderEl.toggle(value);
    }
    _isVerticalOrientation() {
        const { itemOrientation: itemOrientation } = this.option();
        return "vertical" === itemOrientation;
    }
    _normalizeToIndex(toIndex, skipOffsetting) {
        const isAnotherDraggable = this._getSourceDraggable() !== this._getTargetDraggable();
        const fromIndex = this._getActualFromIndex();
        if (null === toIndex) {
            return fromIndex;
        }
        return Math.max(isAnotherDraggable || fromIndex >= toIndex || skipOffsetting ? toIndex : toIndex - 1, 0);
    }
    _updatePlaceholderPosition(e, itemPoint) {
        const sourceDraggable = this._getSourceDraggable();
        const toIndex = this._normalizeToIndex(itemPoint.index, itemPoint.dropInsideItem);
        const eventArgs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this._getEventArgs(e), {
            toIndex: toIndex,
            dropInsideItem: itemPoint.dropInsideItem
        });
        itemPoint.isValid && this._getAction("onDragChange")(eventArgs);
        if (eventArgs.cancel || !itemPoint.isValid) {
            if (!itemPoint.isValid) {
                this.option({
                    dropInsideItem: false,
                    toIndex: null
                });
            }
            return;
        }
        this.option({
            dropInsideItem: itemPoint.dropInsideItem,
            toIndex: itemPoint.index
        });
        this._getAction("onPlaceholderPrepared")((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this._getEventArgs(e), {
            placeholderElement: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])(this._$placeholderElement),
            dragElement: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPublicElement"])(sourceDraggable._$dragElement)
        }));
        this._updateItemPoints();
    }
    _makeWidthCorrection($item, width) {
        this._$scrollable = this._getScrollable($item);
        if (this._$scrollable) {
            const scrollableWidth = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(this._$scrollable);
            const overflowLeft = this._$scrollable.offset().left - $item.offset().left;
            const overflowRight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($item) - overflowLeft - scrollableWidth;
            if (overflowLeft > 0) {
                width -= overflowLeft;
            }
            if (overflowRight > 0) {
                width -= overflowRight;
            }
        }
        return width;
    }
    _updatePlaceholderSizes($placeholderElement, $itemElement) {
        const dropInsideItem = this.option("dropInsideItem");
        const isVertical = this._isVerticalOrientation();
        let width = "";
        let height = "";
        $placeholderElement.toggleClass(this._addWidgetPrefix("placeholder-inside"), dropInsideItem);
        if (isVertical || dropInsideItem) {
            width = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($itemElement);
        }
        if (!isVertical || dropInsideItem) {
            height = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($itemElement);
        }
        width = this._makeWidthCorrection($itemElement, width);
        $placeholderElement.css({
            width: width,
            height: height
        });
    }
    _moveItem($itemElement, index, cancelRemove) {
        let $prevTargetItemElement;
        const $itemElements = this._getItems();
        const $targetItemElement = $itemElements[index];
        const sourceDraggable = this._getSourceDraggable();
        if (cancelRemove) {
            $itemElement = $itemElement.clone();
            sourceDraggable._toggleDragSourceClass(false, $itemElement);
        }
        if (!$targetItemElement) {
            $prevTargetItemElement = $itemElements[index - 1];
        }
        this._moveItemCore($itemElement, $targetItemElement, $prevTargetItemElement);
    }
    _moveItemCore($targetItem, item, prevItem) {
        if (!item && !prevItem) {
            $targetItem.appendTo(this.$element());
        } else if (prevItem) {
            $targetItem.insertAfter((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prevItem));
        } else {
            $targetItem.insertBefore((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(item));
        }
    }
    _getDragStartArgs(e, $itemElement) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(super._getDragStartArgs.apply(this, arguments), {
            fromIndex: this._getElementIndex($itemElement)
        });
    }
    _getEventArgs(e) {
        const sourceDraggable = this._getSourceDraggable();
        const targetDraggable = this._getTargetDraggable();
        const dropInsideItem = targetDraggable.option("dropInsideItem");
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(super._getEventArgs.apply(this, arguments), {
            fromIndex: sourceDraggable.option("fromIndex"),
            toIndex: this._normalizeToIndex(targetDraggable.option("toIndex"), dropInsideItem),
            dropInsideItem: dropInsideItem
        });
    }
    _optionChanged(args) {
        const { name: name } = args;
        switch(name){
            case "onDragChange":
            case "onPlaceholderPrepared":
            case "onAdd":
            case "onRemove":
            case "onReorder":
                this["_".concat(name, "Action")] = this._createActionByOption(name);
                break;
            case "fromIndex":
                [
                    false,
                    true
                ].forEach((isDragSource)=>{
                    const fromIndex = isDragSource ? args.value : args.previousValue;
                    if (null !== fromIndex) {
                        const $fromElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this._getItems()[fromIndex]);
                        this._toggleDragSourceClass(isDragSource, $fromElement);
                    }
                });
                break;
            case "dropInsideItem":
                this._optionChangedDropInsideItem(args);
                break;
            case "toIndex":
                this._optionChangedToIndex(args);
                break;
            case "itemOrientation":
            case "allowDropInsideItem":
            case "moveItemOnDrop":
            case "dropFeedbackMode":
            case "itemPoints":
            case "animation":
            case "allowReordering":
            case "fromIndexOffset":
            case "offset":
            case "draggableElementSize":
            case "autoUpdate":
            case "placeholderClassName":
                break;
            default:
                super._optionChanged(args);
        }
    }
    _optionChangedDropInsideItem() {
        if (this._isIndicateMode() && this._$placeholderElement) {
            this._movePlaceholder();
        }
    }
    _isPositionVisible(position) {
        const $element = this.$element();
        let scrollContainer;
        if ("hidden" !== $element.css("overflow")) {
            scrollContainer = $element.get(0);
        } else {
            $element.parents().each(function() {
                if ("visible" !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this).css("overflow")) {
                    scrollContainer = this;
                    return false;
                }
                return;
            });
        }
        if (scrollContainer) {
            const clientRect = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getBoundingRect"])(scrollContainer);
            const isVerticalOrientation = this._isVerticalOrientation();
            const start = isVerticalOrientation ? "top" : "left";
            const end = isVerticalOrientation ? "bottom" : "right";
            const pageOffset = isVerticalOrientation ? window.pageYOffset : window.pageXOffset;
            if (position[start] < clientRect[start] + pageOffset || position[start] > clientRect[end] + pageOffset) {
                return false;
            }
        }
        return true;
    }
    _optionChangedToIndex(args) {
        const toIndex = args.value;
        if (this._isIndicateMode()) {
            const showPlaceholder = null !== toIndex && toIndex >= 0;
            this._togglePlaceholder(showPlaceholder);
            if (showPlaceholder) {
                this._movePlaceholder();
            }
        } else {
            this._moveItems(args.previousValue, args.value, args.fullUpdate);
        }
    }
    update() {
        if (null === this.option("fromIndex") && null === this.option("toIndex")) {
            return;
        }
        this._updateItemPoints(true);
        this._updateDragSourceClass();
        const toIndex = this.option("toIndex");
        this._optionChangedToIndex({
            value: toIndex,
            fullUpdate: true
        });
    }
    _updateDragSourceClass() {
        const fromIndex = this._getActualFromIndex();
        const $fromElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this._getItems()[fromIndex]);
        if ($fromElement.length) {
            this._$sourceElement = $fromElement;
            this._toggleDragSourceClass(true, $fromElement);
        }
    }
    _makeLeftCorrection(left) {
        const $scrollable = this._$scrollable;
        if ($scrollable && this._isVerticalOrientation()) {
            const overflowLeft = $scrollable.offset().left - left;
            if (overflowLeft > 0) {
                left += overflowLeft;
            }
        }
        return left;
    }
    _movePlaceholder() {
        const that = this;
        const $placeholderElement = that._$placeholderElement || that._createPlaceholder();
        if (!$placeholderElement) {
            return;
        }
        const items = that._getItems();
        const toIndex = that.option("toIndex");
        const isVerticalOrientation = that._isVerticalOrientation();
        const rtlEnabled = this.option("rtlEnabled");
        const dropInsideItem = that.option("dropInsideItem");
        let position = null;
        let itemElement = items[toIndex];
        if (itemElement) {
            const $itemElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(itemElement);
            position = $itemElement.offset();
            if (!isVerticalOrientation && rtlEnabled && !dropInsideItem) {
                position.left += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($itemElement, true);
            }
        } else {
            const prevVisibleItemElement = itemElement = this._getPrevVisibleItem(items, toIndex);
            if (prevVisibleItemElement) {
                position = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(prevVisibleItemElement).offset();
                if (isVerticalOrientation) {
                    position.top += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])(prevVisibleItemElement, true);
                } else if (!rtlEnabled) {
                    position.left += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])(prevVisibleItemElement, true);
                }
            }
        }
        that._updatePlaceholderSizes($placeholderElement, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(itemElement));
        if (position && !that._isPositionVisible(position)) {
            position = null;
        }
        if (position) {
            const isLastVerticalPosition = isVerticalOrientation && toIndex === items.length;
            const outerPlaceholderHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($placeholderElement);
            position.left = that._makeLeftCorrection(position.left);
            position.top = isLastVerticalPosition && position.top >= outerPlaceholderHeight ? position.top - outerPlaceholderHeight : position.top;
            that._move(position, $placeholderElement);
        }
        $placeholderElement.toggle(!!position);
    }
    _getPositions(items, elementSize, fromIndex, toIndex) {
        const positions = [];
        for(let i = 0; i < items.length; i++){
            let position = 0;
            if (null === toIndex || null === fromIndex) {
                positions.push(position);
                continue;
            }
            if (-1 === fromIndex) {
                if (i >= toIndex) {
                    position = elementSize;
                }
            } else if (-1 === toIndex) {
                if (i > fromIndex) {
                    position = -elementSize;
                }
            } else if (fromIndex < toIndex) {
                if (i > fromIndex && i < toIndex) {
                    position = -elementSize;
                }
            } else if (fromIndex > toIndex) {
                if (i >= toIndex && i < fromIndex) {
                    position = elementSize;
                }
            }
            positions.push(position);
        }
        return positions;
    }
    _getDraggableElementSize(isVerticalOrientation) {
        const $draggableItem = this._getDraggableElement();
        let size = this.option("draggableElementSize");
        if (!size) {
            size = isVerticalOrientation ? ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($draggableItem) + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterHeight"])($draggableItem, true)) / 2 : ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($draggableItem) + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOuterWidth"])($draggableItem, true)) / 2;
            if (!this.option("autoUpdate")) {
                this.option("draggableElementSize", size);
            }
        }
        return size;
    }
    _getActualFromIndex() {
        const { fromIndex: fromIndex, fromIndexOffset: fromIndexOffset, offset: offset } = this.option();
        return null == fromIndex ? null : fromIndex + fromIndexOffset - offset;
    }
    _moveItems(prevToIndex, toIndex, fullUpdate) {
        const fromIndex = this._getActualFromIndex();
        const isVerticalOrientation = this._isVerticalOrientation();
        const positionPropName = isVerticalOrientation ? "top" : "left";
        const elementSize = this._getDraggableElementSize(isVerticalOrientation);
        const items = this._getItems();
        const prevPositions = this._getPositions(items, elementSize, fromIndex, prevToIndex);
        const positions = this._getPositions(items, elementSize, fromIndex, toIndex);
        const animationConfig = this.option("animation");
        const rtlEnabled = this.option("rtlEnabled");
        for(let i = 0; i < items.length; i++){
            const itemElement = items[i];
            const prevPosition = prevPositions[i];
            const position = positions[i];
            if (null === toIndex || null === fromIndex) {
                stopAnimation(itemElement);
            } else if (prevPosition !== position || fullUpdate && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(position)) {
                animate(itemElement, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, animationConfig, {
                    to: {
                        [positionPropName]: !isVerticalOrientation && rtlEnabled ? -position : position
                    }
                }));
            }
        }
    }
    _toggleDragSourceClass(value, $element) {
        const $sourceElement = $element || this._$sourceElement;
        super._toggleDragSourceClass.apply(this, arguments);
        if (!this._isIndicateMode()) {
            null === $sourceElement || void 0 === $sourceElement || $sourceElement.toggleClass(this._addWidgetPrefix("source-hidden"), value);
        }
    }
    _dispose() {
        this.reset();
        super._dispose();
    }
    _fireAddEvent(sourceEvent) {
        const args = this._getEventArgs(sourceEvent);
        this._getAction("onAdd")(args);
        return args.cancel;
    }
    _fireRemoveEvent(sourceEvent) {
        const sourceDraggable = this._getSourceDraggable();
        const args = this._getEventArgs(sourceEvent);
        sourceDraggable._getAction("onRemove")(args);
        return args.cancel;
    }
    _fireReorderEvent(sourceEvent) {
        const args = this._getEventArgs(sourceEvent);
        this._getAction("onReorder")(args);
        return args.promise || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Deferred"])().resolve();
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(SORTABLE, Sortable);
const __TURBOPACK__default__export__ = Sortable;
}),
"[project]/node_modules/devextreme/esm/__internal/common/m_charts.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/common/m_charts.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "registerGradient": ()=>registerGradient,
    "registerPattern": ()=>registerPattern
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
;
;
const graphicObjects = {};
const registerPattern = (options)=>{
    const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
    graphicObjects[id] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        type: "pattern"
    }, options);
    return id;
};
const registerGradient = (type, options)=>{
    const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
    graphicObjects[id] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
        type: type
    }, options);
    return id;
};
const getGraphicObjects = ()=>graphicObjects;
const __TURBOPACK__default__export__ = {
    getGraphicObjects: getGraphicObjects
};
}),
}]);

//# sourceMappingURL=node_modules_devextreme_esm___internal_9e75fc25._.js.map