﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;
using DevExpress.Data.Linq;
using omsnext.core.Models;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class KtforgevUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<KtforgevDto> _ktforgevData;
        private bool _isLoading;
        private LinqServerModeSource _serverModeSource;
        private int _totalRecords = 0;

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<KtforgevDto> KtforgevData
        {
            get => _ktforgevData;
            set
            {
                _ktforgevData = value;
                OnPropertyChanged(nameof(KtforgevData));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
                if (StatusLabel != null)
                    StatusLabel.Text = value ? "Betöltés..." : "Készen";
            }
        }

        public KtforgevUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _ktforgevData = new ObservableCollection<KtforgevDto>();
            
            DataContext = this;
            
            // Server Mode inicializálása
            InitializeServerMode();
            
            Loaded += KtforgevUserControl_Loaded;
        }

        private void InitializeServerMode()
        {
            try
            {
                // LinqServerModeSource létrehozása
                _serverModeSource = new LinqServerModeSource();
                
                // DevExtreme API használata a szerveren keresztül
                _serverModeSource.QueryableSource = new KtforgevQueryable(_apiClient);
                _serverModeSource.ElementType = typeof(KtforgevDto);
                _serverModeSource.KeyExpression = "id";
                
                // GridControl kötése a server mode source-hoz
                KtforgevGrid.ItemsSource = _serverModeSource;
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Server Mode inicializálási hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                
                // Fallback: normál ObservableCollection használata
                KtforgevGrid.ItemsSource = KtforgevData;
            }
        }

        private async void KtforgevUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await RefreshDataAsync();
        }

        private async Task RefreshDataAsync()
        {
            IsLoading = true;
            try
            {
                if (_serverModeSource != null)
                {
                    // Server mode refresh - nincs Refresh metódus
                    _serverModeSource = new LinqServerModeSource();
                    _serverModeSource.QueryableSource = new KtforgevQueryable(_apiClient);
                    _serverModeSource.ElementType = typeof(KtforgevDto);
                    _serverModeSource.KeyExpression = "id";
                    KtforgevGrid.ItemsSource = _serverModeSource;
                    StatusLabel.Text = "Server Mode aktív - minden adat elérhető";
                }
                else
                {
                    // Fallback: első batch betöltése
                    await LoadInitialDataAsync();
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadInitialDataAsync()
        {
            var result = await _apiClient.GetKtforgevDataAsync(skip: 0, take: 1000);
            if (result != null)
            {
                KtforgevData.Clear();
                foreach (var item in result.Data)
                {
                    KtforgevData.Add(item);
                }
                _totalRecords = result.TotalCount;
                UpdateStatusLabels();
            }
        }

        private void UpdateStatusLabels()
        {
            if (RecordCountLabel != null)
            {
                if (_serverModeSource != null)
                {
                    RecordCountLabel.Text = "Server Mode - minden adat elérhető";
                    PageInfoLabel.Text = "Virtual scrolling, grouping, filtering aktív";
                }
                else
                {
                    RecordCountLabel.Text = $"Betöltött rekordok: {KtforgevData.Count:N0} / {_totalRecords:N0}";
                    PageInfoLabel.Text = "Fallback mode";
                }
            }
        }

        private async void ApplyFilterButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = FilterFieldComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null && !string.IsNullOrWhiteSpace(FilterValueTextBox.Text))
            {
                var filterField = selectedItem.Tag?.ToString();
                var filterValue = FilterValueTextBox.Text.Trim();
                
                // DevExpress Grid filtering
                var column = KtforgevGrid.Columns.FirstOrDefault(c => c.FieldName == filterField);
                if (column != null)
                {
                    column.FilterCriteria = new DevExpress.Data.Filtering.BinaryOperator(
                        filterField, 
                        filterValue, 
                        DevExpress.Data.Filtering.BinaryOperatorType.Like);
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük válasszon szűrő mezőt és adjon meg értéket!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            FilterFieldComboBox.SelectedIndex = -1;
            FilterValueTextBox.Clear();
            KtforgevGrid.ClearFilter();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // Custom IQueryable implementáció a DevExpress Server Mode-hoz
    public class KtforgevQueryable : IQueryable<KtforgevDto>
    {
        private readonly ApiClient _apiClient;
        private readonly KtforgevQueryProvider _provider;

        public KtforgevQueryable(ApiClient apiClient)
        {
            _apiClient = apiClient;
            _provider = new KtforgevQueryProvider(apiClient);
        }

        public Type ElementType => typeof(KtforgevDto);
        public System.Linq.Expressions.Expression Expression => System.Linq.Expressions.Expression.Constant(this);
        public IQueryProvider Provider => _provider;

        public IEnumerator<KtforgevDto> GetEnumerator()
        {
            // Ez a DevExpress által hívódik meg
            var result = _apiClient.GetKtforgevDataAsync(skip: 0, take: 10000).Result;
            return result?.Data?.GetEnumerator() ?? new List<KtforgevDto>().GetEnumerator();
        }

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }

    public class KtforgevQueryProvider : IQueryProvider
    {
        private readonly ApiClient _apiClient;

        public KtforgevQueryProvider(ApiClient apiClient)
        {
            _apiClient = apiClient;
        }

        public IQueryable CreateQuery(System.Linq.Expressions.Expression expression)
        {
            return new KtforgevQueryable(_apiClient);
        }

        public IQueryable<TElement> CreateQuery<TElement>(System.Linq.Expressions.Expression expression)
        {
            if (typeof(TElement) == typeof(KtforgevDto))
                return (IQueryable<TElement>)new KtforgevQueryable(_apiClient);
            throw new NotSupportedException();
        }

        public object Execute(System.Linq.Expressions.Expression expression)
        {
            throw new NotSupportedException();
        }

        public TResult Execute<TResult>(System.Linq.Expressions.Expression expression)
        {
            throw new NotSupportedException();
        }
    }
}