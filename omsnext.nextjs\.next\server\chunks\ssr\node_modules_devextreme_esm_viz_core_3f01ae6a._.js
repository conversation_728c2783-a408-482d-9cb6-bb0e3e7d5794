module.exports = {

"[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "PANE_PADDING": ()=>PANE_PADDING,
    "adjustVisualRange": ()=>adjustVisualRange,
    "convertAngleToRendererSpace": ()=>convertAngleToRendererSpace,
    "convertPolarToXY": ()=>convertPolarToXY,
    "convertVisualRangeObject": ()=>convertVisualRangeObject,
    "convertXYToPolar": ()=>convertXYToPolar,
    "decreaseGaps": ()=>decreaseGaps,
    "degreesToRadians": ()=>degreesToRadians,
    "enumParser": ()=>enumParser,
    "extractColor": ()=>extractColor,
    "getAddFunction": ()=>getAddFunction,
    "getAdjustedLog10": ()=>getAdjustedLog10,
    "getAppropriateFormat": ()=>getAppropriateFormat,
    "getCategoriesInfo": ()=>getCategoriesInfo,
    "getCosAndSin": ()=>getCosAndSin,
    "getDecimalOrder": ()=>getDecimalOrder,
    "getDistance": ()=>getDistance,
    "getLog": ()=>getLog,
    "getLogExt": ()=>getLogExt,
    "getNextDefsSvgId": ()=>getNextDefsSvgId,
    "getPower": ()=>getPower,
    "getVerticallyShiftedAngularCoords": ()=>getVerticallyShiftedAngularCoords,
    "getVizRangeObject": ()=>getVizRangeObject,
    "isRelativeHeightPane": ()=>isRelativeHeightPane,
    "map": ()=>map,
    "mergeMarginOptions": ()=>mergeMarginOptions,
    "normalizeAngle": ()=>normalizeAngle,
    "normalizeArcParams": ()=>normalizeArcParams,
    "normalizeBBox": ()=>normalizeBBox,
    "normalizeEnum": ()=>normalizeEnum,
    "normalizePanesHeight": ()=>normalizePanesHeight,
    "parseScalar": ()=>parseScalar,
    "patchFontOptions": ()=>patchFontOptions,
    "pointInCanvas": ()=>pointInCanvas,
    "processSeriesTemplate": ()=>processSeriesTemplate,
    "raiseTo": ()=>raiseTo,
    "raiseToExt": ()=>raiseToExt,
    "rangesAreEqual": ()=>rangesAreEqual,
    "rotateBBox": ()=>rotateBBox,
    "roundValue": ()=>roundValue,
    "setCanvasValues": ()=>setCanvasValues,
    "unique": ()=>unique,
    "updatePanesCanvases": ()=>updatePanesCanvases,
    "valueOf": ()=>valueOf
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/color.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const { PI: PI, LN10: LN10, abs: abs, log: log, floor: floor, ceil: ceil, pow: pow, sqrt: sqrt, atan2: atan2 } = Math;
const _min = Math.min;
const _max = Math.max;
const _cos = Math.cos;
const _sin = Math.sin;
const _round = Math.round;
const dateToMilliseconds = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds;
const MAX_PIXEL_COUNT = 1e10;
const PI_DIV_180 = PI / 180;
const _isNaN = isNaN;
const _Number = Number;
const _NaN = NaN;
let numDefsSvgElements = 1;
const PANE_PADDING = 10;
const getLog = function(value, base) {
    if (!value) {
        return NaN;
    }
    return log(value) / log(base);
};
const getAdjustedLog10 = function(value) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(getLog(value, 10));
};
const raiseTo = function(power, base) {
    return pow(base, power);
};
const normalizeAngle = function(angle) {
    return (angle % 360 + 360) % 360;
};
const convertAngleToRendererSpace = function(angle) {
    return 90 - angle;
};
const degreesToRadians = function(value) {
    return PI * value / 180;
};
const getCosAndSin = function(angle) {
    const angleInRadians = degreesToRadians(angle);
    return {
        cos: _cos(angleInRadians),
        sin: _sin(angleInRadians)
    };
};
const DECIMAL_ORDER_THRESHOLD = 1e-14;
const getDistance = function(x1, y1, x2, y2) {
    const diffX = x2 - x1;
    const diffY = y2 - y1;
    return sqrt(diffY * diffY + diffX * diffX);
};
const getDecimalOrder = function(number) {
    let n = abs(number);
    let cn;
    if (!_isNaN(n)) {
        if (n > 0) {
            n = log(n) / LN10;
            cn = ceil(n);
            return cn - n < 1e-14 ? cn : floor(n);
        }
        return 0;
    }
    return NaN;
};
const getAppropriateFormat = function(start, end, count) {
    const order = _max(getDecimalOrder(start), getDecimalOrder(end));
    let precision = -getDecimalOrder(abs(end - start) / count);
    let format;
    if (!_isNaN(order) && !_isNaN(precision)) {
        if (abs(order) <= 4) {
            format = "fixedPoint";
            precision < 0 && (precision = 0);
            precision > 4 && (precision = 4);
        } else {
            format = "exponential";
            precision += order - 1;
            precision > 3 && (precision = 3);
        }
        return {
            type: format,
            precision: precision
        };
    }
    return null;
};
const roundValue = function(value, precision) {
    if (precision > 20) {
        precision = 20;
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(value)) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isExponential"])(value)) {
            return _Number(value.toExponential(precision));
        } else {
            return _Number(value.toFixed(precision));
        }
    }
};
const getPower = function(value) {
    return value.toExponential().split("e")[1];
};
function map(array, callback) {
    let i = 0;
    const len = array.length;
    const result = [];
    let value;
    while(i < len){
        value = callback(array[i], i);
        if (null !== value) {
            result.push(value);
        }
        i++;
    }
    return result;
}
function selectByKeys(object, keys) {
    return map(keys, (key)=>object[key] ? object[key] : null);
}
function decreaseFields(object, keys, eachDecrease, decrease) {
    let dec = decrease;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(keys, (_, key)=>{
        if (object[key]) {
            object[key] -= eachDecrease;
            dec -= eachDecrease;
        }
    });
    return dec;
}
function normalizeEnum(value) {
    return String(value).toLowerCase();
}
function setCanvasValues(canvas) {
    if (canvas) {
        canvas.originalTop = canvas.top;
        canvas.originalBottom = canvas.bottom;
        canvas.originalLeft = canvas.left;
        canvas.originalRight = canvas.right;
    }
    return canvas;
}
function normalizeBBoxField(value) {
    return -1e10 < value && value < 1e10 ? value : 0;
}
function normalizeBBox(bBox) {
    const xl = normalizeBBoxField(floor(bBox.x));
    const yt = normalizeBBoxField(floor(bBox.y));
    const xr = normalizeBBoxField(ceil(bBox.width + bBox.x));
    const yb = normalizeBBoxField(ceil(bBox.height + bBox.y));
    const result = {
        x: xl,
        y: yt,
        width: xr - xl,
        height: yb - yt
    };
    result.isEmpty = !result.x && !result.y && !result.width && !result.height;
    return result;
}
function rotateBBox(bBox, center, angle) {
    const cos = _Number(_cos(angle * PI_DIV_180).toFixed(3));
    const sin = _Number(_sin(angle * PI_DIV_180).toFixed(3));
    const w2 = bBox.width / 2;
    const h2 = bBox.height / 2;
    const centerX = bBox.x + w2;
    const centerY = bBox.y + h2;
    const w2_ = abs(w2 * cos) + abs(h2 * sin);
    const h2_ = abs(w2 * sin) + abs(h2 * cos);
    const centerX_ = center[0] + (centerX - center[0]) * cos + (centerY - center[1]) * sin;
    const centerY_ = center[1] - (centerX - center[0]) * sin + (centerY - center[1]) * cos;
    return normalizeBBox({
        x: centerX_ - w2_,
        y: centerY_ - h2_,
        width: 2 * w2_,
        height: 2 * h2_
    });
}
const decreaseGaps = function(object, keys, decrease) {
    let arrayGaps;
    do {
        arrayGaps = selectByKeys(object, keys);
        arrayGaps.push(ceil(decrease / arrayGaps.length));
        decrease = decreaseFields(object, keys, _min.apply(null, arrayGaps), decrease);
    }while (decrease > 0 && arrayGaps.length > 1)
    return decrease;
};
const parseScalar = function(value, defaultValue) {
    return void 0 !== value ? value : defaultValue;
};
const enumParser = function(values) {
    const stored = {};
    let i;
    let ii;
    for(i = 0, ii = values.length; i < ii; ++i){
        stored[normalizeEnum(values[i])] = 1;
    }
    return function(value, defaultValue) {
        const _value = normalizeEnum(value);
        return stored[_value] ? _value : defaultValue;
    };
};
const patchFontOptions = function(options) {
    const fontOptions = {};
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(options || {}, function(key, value) {
        if (/^(cursor)$/i.test(key)) {} else if ("opacity" === key) {
            value = null;
        } else if ("color" === key) {
            key = "fill";
            if ("opacity" in options) {
                const color = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](value);
                value = `rgba(${color.r},${color.g},${color.b},${options.opacity})`;
            }
        } else {
            key = "font-" + key;
        }
        fontOptions[key] = value;
    });
    return fontOptions;
};
function convertPolarToXY(centerCoords, startAngle, angle, radius) {
    const normalizedRadius = radius > 0 ? radius : 0;
    angle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(angle) ? angle + startAngle - 90 : 0;
    const cosSin = getCosAndSin(angle);
    return {
        x: _round(centerCoords.x + normalizedRadius * cosSin.cos),
        y: _round(centerCoords.y + normalizedRadius * cosSin.sin)
    };
}
const convertXYToPolar = function(centerCoords, x, y) {
    const radius = getDistance(centerCoords.x, centerCoords.y, x, y);
    const angle = atan2(y - centerCoords.y, x - centerCoords.x);
    return {
        phi: _round(normalizeAngle(180 * angle / PI)),
        r: _round(radius)
    };
};
const processSeriesTemplate = function(seriesTemplate, items) {
    const customizeSeries = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(seriesTemplate.customizeSeries) ? seriesTemplate.customizeSeries : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
    const nameField = seriesTemplate.nameField;
    const generatedSeries = {};
    const seriesOrder = [];
    let series;
    let i = 0;
    let length;
    let data;
    items = items || [];
    for(length = items.length; i < length; i++){
        data = items[i];
        if (nameField in data) {
            series = generatedSeries[data[nameField]];
            if (!series) {
                series = generatedSeries[data[nameField]] = {
                    name: data[nameField],
                    nameFieldValue: data[nameField]
                };
                seriesOrder.push(series.name);
            }
        }
    }
    return map(seriesOrder, function(orderedName) {
        const group = generatedSeries[orderedName];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(group, customizeSeries.call(null, group.name));
    });
};
const getCategoriesInfo = function(categories, startValue, endValue) {
    if (0 === categories.length) {
        return {
            categories: []
        };
    }
    startValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(startValue) ? startValue : categories[0];
    endValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(endValue) ? endValue : categories[categories.length - 1];
    const categoriesValue = map(categories, (category)=>null === category || void 0 === category ? void 0 : category.valueOf());
    let indexStartValue = categoriesValue.indexOf(startValue.valueOf());
    let indexEndValue = categoriesValue.indexOf(endValue.valueOf());
    let swapBuf;
    let inverted = false;
    indexStartValue < 0 && (indexStartValue = 0);
    indexEndValue < 0 && (indexEndValue = categories.length - 1);
    if (indexEndValue < indexStartValue) {
        swapBuf = indexEndValue;
        indexEndValue = indexStartValue;
        indexStartValue = swapBuf;
        inverted = true;
    }
    const visibleCategories = categories.slice(indexStartValue, indexEndValue + 1);
    const lastIdx = visibleCategories.length - 1;
    return {
        categories: visibleCategories,
        start: visibleCategories[inverted ? lastIdx : 0],
        end: visibleCategories[inverted ? 0 : lastIdx],
        inverted: inverted
    };
};
function isRelativeHeightPane(pane) {
    return !(pane.unit % 2);
}
function normalizePanesHeight(panes) {
    panes.forEach((pane)=>{
        const height = pane.height;
        let unit = 0;
        let parsedHeight = parseFloat(height) || void 0;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(height) && height.indexOf("px") > -1 || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(height) && height > 1) {
            parsedHeight = _round(parsedHeight);
            unit = 1;
        }
        if (!unit && parsedHeight) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(height) && height.indexOf("%") > -1) {
                parsedHeight /= 100;
                unit = 2;
            } else if (parsedHeight < 0) {
                parsedHeight = parsedHeight < -1 ? 1 : abs(parsedHeight);
            }
        }
        pane.height = parsedHeight;
        pane.unit = unit;
    });
    const relativeHeightPanes = panes.filter(isRelativeHeightPane);
    const weightSum = relativeHeightPanes.reduce((prev, next)=>prev + (next.height || 0), 0);
    const weightHeightCount = relativeHeightPanes.length;
    const emptyHeightPanes = relativeHeightPanes.filter((pane)=>!pane.height);
    const emptyHeightCount = emptyHeightPanes.length;
    if (weightSum < 1 && emptyHeightCount) {
        emptyHeightPanes.forEach((pane)=>pane.height = (1 - weightSum) / emptyHeightCount);
    } else if (weightSum > 1 || weightSum < 1 && !emptyHeightCount || 1 === weightSum && emptyHeightCount) {
        if (emptyHeightCount) {
            const weightForEmpty = weightSum / weightHeightCount;
            const emptyWeightSum = emptyHeightCount * weightForEmpty;
            relativeHeightPanes.filter((pane)=>pane.height).forEach((pane)=>pane.height *= (weightSum - emptyWeightSum) / weightSum);
            emptyHeightPanes.forEach((pane)=>pane.height = weightForEmpty);
        }
        relativeHeightPanes.forEach((pane)=>pane.height *= 1 / weightSum);
    }
}
function updatePanesCanvases(panes, canvas, rotated) {
    let distributedSpace = 0;
    const paneSpace = rotated ? canvas.width - canvas.left - canvas.right : canvas.height - canvas.top - canvas.bottom;
    const totalCustomSpace = panes.reduce((prev, cur)=>prev + (!isRelativeHeightPane(cur) ? cur.height : 0), 0);
    const usefulSpace = paneSpace - 10 * (panes.length - 1) - totalCustomSpace;
    const startName = rotated ? "left" : "top";
    const endName = rotated ? "right" : "bottom";
    panes.forEach((pane)=>{
        const calcLength = !isRelativeHeightPane(pane) ? pane.height : _round(pane.height * usefulSpace);
        pane.canvas = pane.canvas || {};
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(pane.canvas, canvas);
        pane.canvas[startName] = canvas[startName] + distributedSpace;
        pane.canvas[endName] = canvas[endName] + (paneSpace - calcLength - distributedSpace);
        distributedSpace = distributedSpace + calcLength + 10;
        setCanvasValues(pane.canvas);
    });
}
const unique = function(array) {
    const values = {};
    return map(array, function(item) {
        const result = !values[item] ? item : null;
        values[item] = true;
        return result;
    });
};
const getVerticallyShiftedAngularCoords = function(bBox, dy, center) {
    const isPositive = bBox.x + bBox.width / 2 >= center.x;
    const horizontalOffset1 = (isPositive ? bBox.x : bBox.x + bBox.width) - center.x;
    const verticalOffset1 = bBox.y - center.y;
    const verticalOffset2 = verticalOffset1 + dy;
    const horizontalOffset2 = _round(sqrt(horizontalOffset1 * horizontalOffset1 + verticalOffset1 * verticalOffset1 - verticalOffset2 * verticalOffset2));
    const dx = (isPositive ? +horizontalOffset2 : -horizontalOffset2) || horizontalOffset1;
    return {
        x: center.x + (isPositive ? dx : dx - bBox.width),
        y: bBox.y + dy
    };
};
function mergeMarginOptions(opt1, opt2) {
    return {
        checkInterval: opt1.checkInterval || opt2.checkInterval,
        size: _max(opt1.size || 0, opt2.size || 0),
        percentStick: opt1.percentStick || opt2.percentStick,
        sizePointNormalState: _max(opt1.sizePointNormalState || 0, opt2.sizePointNormalState || 0)
    };
}
function getVizRangeObject(value) {
    if (Array.isArray(value)) {
        return {
            startValue: value[0],
            endValue: value[1]
        };
    } else {
        return value || {};
    }
}
function normalizeArcParams(x, y, innerRadius, outerRadius, startAngle, endAngle) {
    let isCircle;
    let noArc = true;
    const angleDiff = roundValue(endAngle, 3) - roundValue(startAngle, 3);
    if (angleDiff) {
        if (abs(angleDiff) % 360 === 0) {
            startAngle = 0;
            endAngle = 360;
            isCircle = true;
            endAngle -= .01;
        }
        if (startAngle > 360) {
            startAngle %= 360;
        }
        if (endAngle > 360) {
            endAngle %= 360;
        }
        if (startAngle > endAngle) {
            startAngle -= 360;
        }
        noArc = false;
    }
    startAngle *= PI_DIV_180;
    endAngle *= PI_DIV_180;
    return [
        x,
        y,
        Math.min(outerRadius, innerRadius),
        Math.max(outerRadius, innerRadius),
        Math.cos(startAngle),
        Math.sin(startAngle),
        Math.cos(endAngle),
        Math.sin(endAngle),
        isCircle,
        floor(abs(endAngle - startAngle) / PI) % 2 ? "1" : "0",
        noArc
    ];
}
function convertVisualRangeObject(visualRange, convertToVisualRange) {
    if (convertToVisualRange) {
        return visualRange;
    }
    return [
        visualRange.startValue,
        visualRange.endValue
    ];
}
function getAddFunction(range, correctZeroLevel) {
    if ("datetime" === range.dataType) {
        return function(rangeValue, marginValue) {
            let sign = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;
            return new Date(rangeValue.getTime() + sign * marginValue);
        };
    }
    if ("logarithmic" === range.axisType) {
        return function(rangeValue, marginValue) {
            let sign = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;
            const log = getLogExt(rangeValue, range.base) + sign * marginValue;
            return raiseToExt(log, range.base);
        };
    }
    return function(rangeValue, marginValue) {
        let sign = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;
        const newValue = rangeValue + sign * marginValue;
        return correctZeroLevel && newValue * rangeValue <= 0 ? 0 : newValue;
    };
}
function adjustVisualRange(options, visualRange, wholeRange, dataRange) {
    const minDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(visualRange.startValue);
    const maxDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(visualRange.endValue);
    const nonDiscrete = "discrete" !== options.axisType;
    dataRange = dataRange || wholeRange;
    const add = getAddFunction(options, false);
    let min = minDefined ? visualRange.startValue : dataRange.min;
    let max = maxDefined ? visualRange.endValue : dataRange.max;
    let rangeLength = visualRange.length;
    const categories = dataRange.categories;
    if (nonDiscrete && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(min) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(max)) {
        return {
            startValue: min,
            endValue: max
        };
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(rangeLength)) {
        if (nonDiscrete) {
            if ("datetime" === options.dataType && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(rangeLength)) {
                rangeLength = dateToMilliseconds(rangeLength);
            }
            if (maxDefined && !minDefined || !maxDefined && !minDefined) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(wholeRange.max) && (max = max > wholeRange.max ? wholeRange.max : max);
                min = add(max, rangeLength, -1);
            } else if (minDefined && !maxDefined) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(wholeRange.min) && (min = min < wholeRange.min ? wholeRange.min : min);
                max = add(min, rangeLength);
            }
        } else {
            rangeLength = parseInt(rangeLength);
            if (!isNaN(rangeLength) && isFinite(rangeLength)) {
                rangeLength--;
                if (!maxDefined && !minDefined) {
                    max = categories[categories.length - 1];
                    min = categories[categories.length - 1 - rangeLength];
                } else if (minDefined && !maxDefined) {
                    const categoriesInfo = getCategoriesInfo(categories, min, void 0);
                    max = categoriesInfo.categories[rangeLength];
                } else if (!minDefined && maxDefined) {
                    const categoriesInfo = getCategoriesInfo(categories, void 0, max);
                    min = categoriesInfo.categories[categoriesInfo.categories.length - 1 - rangeLength];
                }
            }
        }
    }
    if (nonDiscrete) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(wholeRange.max) && max > wholeRange.max) {
            max = wholeRange.max;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(wholeRange.min) && min < wholeRange.min) {
            min = wholeRange.min;
        }
    }
    return {
        startValue: min,
        endValue: max
    };
}
function getLogExt(value, base) {
    let allowNegatives = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : false;
    let linearThreshold = arguments.length > 3 ? arguments[3] : void 0;
    if (!allowNegatives) {
        return getLog(value, base);
    }
    if (0 === value) {
        return 0;
    }
    const transformValue = getLog(abs(value), base) - (linearThreshold - 1);
    if (transformValue < 0) {
        return 0;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sign"])(value) * transformValue, Number(pow(base, linearThreshold - 1).toFixed(abs(linearThreshold))));
}
function raiseToExt(value, base) {
    let allowNegatives = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : false;
    let linearThreshold = arguments.length > 3 ? arguments[3] : void 0;
    if (!allowNegatives) {
        return raiseTo(value, base);
    }
    if (0 === value) {
        return 0;
    }
    const transformValue = raiseTo(abs(value) + (linearThreshold - 1), base);
    if (transformValue < 0) {
        return 0;
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sign"])(value) * transformValue, Number(pow(base, linearThreshold).toFixed(abs(linearThreshold))));
}
function rangesAreEqual(range, rangeFromOptions) {
    if (Array.isArray(rangeFromOptions)) {
        return range.length === rangeFromOptions.length && range.every((item, i)=>valueOf(item) === valueOf(rangeFromOptions[i]));
    } else {
        return valueOf(range.startValue) === valueOf(rangeFromOptions.startValue) && valueOf(range.endValue) === valueOf(rangeFromOptions.endValue);
    }
}
function valueOf(value) {
    return value && value.valueOf();
}
function pointInCanvas(canvas, x, y) {
    return x >= canvas.left && x <= canvas.right && y >= canvas.top && y <= canvas.bottom;
}
const getNextDefsSvgId = ()=>"DevExpress_" + numDefsSvgElements++;
function extractColor(color, isBase) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(color) || !color) {
        return color;
    } else if (isBase) {
        return color.base;
    } else {
        return color.fillId || color.base;
    }
}
}),
"[project]/node_modules/devextreme/esm/viz/core/layout_element.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/layout_element.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "LayoutElement": ()=>LayoutElement,
    "WrapperLayoutElement": ()=>WrapperLayoutElement
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/object.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-ssr] (ecmascript)");
;
const _round = Math.round;
;
const defaultOffset = {
    horizontal: 0,
    vertical: 0
};
const alignFactors = {
    center: .5,
    right: 1,
    bottom: 1,
    left: 0,
    top: 0
};
function LayoutElement(options) {
    this._options = options;
}
LayoutElement.prototype = {
    constructor: LayoutElement,
    position: function(options) {
        const ofBBox = options.of.getLayoutOptions();
        const myBBox = this.getLayoutOptions();
        const at = options.at;
        const my = options.my;
        const offset = options.offset || defaultOffset;
        const shiftX = -alignFactors[my.horizontal] * myBBox.width + ofBBox.x + alignFactors[at.horizontal] * ofBBox.width + parseInt(offset.horizontal);
        const shiftY = -alignFactors[my.vertical] * myBBox.height + ofBBox.y + alignFactors[at.vertical] * ofBBox.height + parseInt(offset.vertical);
        this.shift(_round(shiftX), _round(shiftY));
    },
    getLayoutOptions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"]
};
function WrapperLayoutElement(renderElement, bBox) {
    this._renderElement = renderElement;
    this._cacheBBox = bBox;
}
const wrapperLayoutElementPrototype = WrapperLayoutElement.prototype = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clone"])(LayoutElement.prototype);
wrapperLayoutElementPrototype.constructor = WrapperLayoutElement;
wrapperLayoutElementPrototype.getLayoutOptions = function() {
    return this._cacheBBox || this._renderElement.getBBox();
};
wrapperLayoutElementPrototype.shift = function(shiftX, shiftY) {
    const bBox = this.getLayoutOptions();
    this._renderElement.move(_round(shiftX - bBox.x), _round(shiftY - bBox.y));
};
;
}),
"[project]/node_modules/devextreme/esm/viz/core/renderers/animation.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/renderers/animation.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "AnimationController": ()=>AnimationController,
    "animationSvgStep": ()=>animationSvgStep,
    "easingFunctions": ()=>easingFunctions
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-ssr] (ecmascript)");
;
const noop = function() {};
const easingFunctions = {
    easeOutCubic: function(pos, start, end) {
        return 1 === pos ? end : (1 - Math.pow(1 - pos, 3)) * (end - start) + +start;
    },
    linear: function(pos, start, end) {
        return 1 === pos ? end : pos * (end - start) + +start;
    }
};
const animationSvgStep = {
    segments: function(elem, params, progress, easing, currentParams) {
        const from = params.from;
        const to = params.to;
        let curSeg;
        let seg;
        let i;
        let j;
        const segments = [];
        for(i = 0; i < from.length; i++){
            curSeg = from[i];
            seg = [
                curSeg[0]
            ];
            if (curSeg.length > 1) {
                for(j = 1; j < curSeg.length; j++){
                    seg.push(easing(progress, curSeg[j], to[i][j]));
                }
            }
            segments.push(seg);
        }
        currentParams.segments = params.end && 1 === progress ? params.end : segments;
        elem.attr({
            segments: segments
        });
    },
    arc: function(elem, params, progress, easing) {
        const from = params.from;
        const to = params.to;
        const current = {};
        for(const i in from){
            current[i] = easing(progress, from[i], to[i]);
        }
        elem.attr(current);
    },
    transform: function(elem, params, progress, easing, currentParams) {
        const from = params.from;
        const to = params.to;
        const current = {};
        for(const i in from){
            current[i] = currentParams[i] = easing(progress, from[i], to[i]);
        }
        elem.attr(current);
    },
    base: function(elem, params, progress, easing, currentParams, attributeName) {
        const obj = {};
        obj[attributeName] = currentParams[attributeName] = easing(progress, params.from, params.to);
        elem.attr(obj);
    },
    _: noop,
    complete: function(element, currentSettings) {
        element.attr(currentSettings);
    }
};
function step(now) {
    const that = this;
    const animateStep = that._animateStep;
    let attrName;
    that._progress = that._calcProgress(now);
    for(attrName in that.params){
        const anim = animateStep[attrName] || animateStep.base;
        anim(that.element, that.params[attrName], that._progress, that._easing, that._currentParams, attrName);
    }
    that.options.step && that.options.step(that._easing(that._progress, 0, 1), that._progress);
    if (1 === that._progress) {
        return that.stop();
    }
    return true;
}
function delayTick(now) {
    if (now - this._startTime >= this.delay) {
        this.tick = step;
    }
    return true;
}
function start(now) {
    this._startTime = now;
    this.tick = this.delay ? delayTick : step;
    return true;
}
function Animation(element, params, options) {
    this._progress = 0;
    this.element = element;
    this.params = params;
    this.options = options;
    this.duration = options.partitionDuration ? options.duration * options.partitionDuration : options.duration;
    this.delay = options.delay && options.duration * options.delay || 0;
    this._animateStep = options.animateStep || animationSvgStep;
    this._easing = easingFunctions[options.easing] || easingFunctions.easeOutCubic;
    this._currentParams = {};
    this.tick = start;
}
Animation.prototype = {
    _calcProgress: function(now) {
        return Math.min(1, (now - this.delay - this._startTime) / this.duration);
    },
    stop: function(disableComplete) {
        const options = this.options;
        const animateStep = this._animateStep;
        this.stop = this.tick = noop;
        animateStep.complete && animateStep.complete(this.element, this._currentParams);
        options.complete && !disableComplete && options.complete();
    }
};
function AnimationController(element) {
    this._animationCount = 0;
    this._timerId = null;
    this._animations = {};
    this.element = element;
}
AnimationController.prototype = {
    _loop: function() {
        const that = this;
        const animations = that._animations;
        let activeAnimation = 0;
        const now = (new Date).getTime();
        let an;
        const endAnimation = that._endAnimation;
        for(an in animations){
            if (!animations[an].tick(now)) {
                delete animations[an];
            }
            activeAnimation++;
        }
        if (0 === activeAnimation) {
            that.stop();
            that._endAnimationTimer = endAnimation && setTimeout(function() {
                if (0 === that._animationCount) {
                    endAnimation();
                    that._endAnimation = null;
                }
            });
            return;
        }
        that._timerId = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["requestAnimationFrame"].call(null, function() {
            that._loop();
        }, that.element);
    },
    addAnimation: function(animation) {
        const that = this;
        that._animations[that._animationCount++] = animation;
        clearTimeout(that._endAnimationTimer);
        if (!that._timerId) {
            clearTimeout(that._startDelay);
            that._startDelay = setTimeout(function() {
                that._timerId = 1;
                that._loop();
            }, 0);
        }
    },
    animateElement: function(elem, params, options) {
        if (elem && params && options) {
            elem.animation && elem.animation.stop();
            this.addAnimation(elem.animation = new Animation(elem, params, options));
        }
    },
    onEndAnimation: function(endAnimation) {
        this._animationCount ? this._endAnimation = endAnimation : endAnimation();
    },
    dispose: function() {
        this.stop();
        this.element = null;
    },
    stop: function() {
        this._animations = {};
        this._animationCount = 0;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(this._timerId);
        clearTimeout(this._startDelay);
        clearTimeout(this._endAnimationTimer);
        this._timerId = null;
    },
    lock: function() {
        let an;
        const animations = this._animations;
        let unstoppable;
        let hasUnstoppableInAnimations;
        for(an in animations){
            unstoppable = animations[an].options.unstoppable;
            hasUnstoppableInAnimations = hasUnstoppableInAnimations || unstoppable;
            if (!unstoppable) {
                animations[an].stop(true);
                delete animations[an];
            }
        }
        !hasUnstoppableInAnimations && this.stop();
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/renderers/renderer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "ArcSvgElement": ()=>ArcSvgElement,
    "PathSvgElement": ()=>PathSvgElement,
    "RectSvgElement": ()=>RectSvgElement,
    "Renderer": ()=>Renderer,
    "SvgElement": ()=>SvgElement,
    "TextSvgElement": ()=>TextSvgElement,
    "getFuncIri": ()=>getFuncIri,
    "processHatchingAttrs": ()=>processHatchingAttrs,
    "refreshPaths": ()=>refreshPaths
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/call_once.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/svg.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_svg.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/animation.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])();
const { max: max, round: round } = Math;
const SHARPING_CORRECTION = .5;
const ARC_COORD_PREC = 5;
const LIGHTENING_HASH = "@filter::lightening";
const pxAddingExceptions = {
    "column-count": true,
    "fill-opacity": true,
    "flex-grow": true,
    "flex-shrink": true,
    "font-weight": true,
    "line-height": true,
    opacity: true,
    order: true,
    orphans: true,
    widows: true,
    "z-index": true,
    zoom: true
};
const KEY_TEXT = "text";
const KEY_STROKE = "stroke";
const KEY_STROKE_WIDTH = "stroke-width";
const KEY_STROKE_OPACITY = "stroke-opacity";
const KEY_FONT_SIZE = "font-size";
const KEY_FONT_STYLE = "font-style";
const KEY_FONT_WEIGHT = "font-weight";
const KEY_TEXT_DECORATION = "text-decoration";
const KEY_TEXTS_ALIGNMENT = "textsAlignment";
const NONE = "none";
const DEFAULT_FONT_SIZE = 12;
const ELLIPSIS = "...";
const objectCreate = function() {
    if (!Object.create) {
        return function(proto) {
            const F = function() {};
            F.prototype = proto;
            return new F;
        };
    } else {
        return function(proto) {
            return Object.create(proto);
        };
    }
}();
const DEFAULTS = {
    scaleX: 1,
    scaleY: 1,
    "pointer-events": null
};
const getBackup = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(function() {
    const backupContainer = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div");
    backupContainer.style.left = "-9999px";
    backupContainer.style.position = "absolute";
    return {
        backupContainer: backupContainer,
        backupCounter: 0
    };
});
function backupRoot(root) {
    if (0 === getBackup().backupCounter) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody().appendChild(getBackup().backupContainer);
    }
    ++getBackup().backupCounter;
    root.append({
        element: getBackup().backupContainer
    });
}
function restoreRoot(root, container) {
    root.append({
        element: container
    });
    --getBackup().backupCounter;
    if (0 === getBackup().backupCounter) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody().removeChild(getBackup().backupContainer);
    }
}
function isObjectArgument(value) {
    return value && "string" !== typeof value;
}
function createElement(tagName) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElementNS("http://www.w3.org/2000/svg", tagName);
}
function getFuncIri(id, pathModified) {
    return null !== id ? "url(" + (pathModified ? window.location.href.split("#")[0] : "") + "#" + id + ")" : id;
}
function extend(target, source) {
    let key;
    for(key in source){
        target[key] = source[key];
    }
    return target;
}
const preserveAspectRatioMap = {
    full: NONE,
    lefttop: "xMinYMin",
    leftcenter: "xMinYMid",
    leftbottom: "xMinYMax",
    centertop: "xMidYMin",
    center: "xMidYMid",
    centerbottom: "xMidYMax",
    righttop: "xMaxYMin",
    rightcenter: "xMaxYMid",
    rightbottom: "xMaxYMax"
};
function processHatchingAttrs(element, attrs) {
    if (attrs.hatching && "none" !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(attrs.hatching.direction)) {
        attrs = extend({}, attrs);
        attrs.fill = element._hatching = element.renderer.lockDefsElements({
            color: attrs.fill,
            hatching: attrs.hatching
        }, element._hatching, "pattern");
        delete attrs.filter;
    } else if (element._hatching) {
        element.renderer.releaseDefsElements(element._hatching);
        element._hatching = null;
        delete attrs.filter;
    } else if (attrs.filter) {
        attrs = extend({}, attrs);
        attrs.filter = element._filter = element.renderer.lockDefsElements({}, element._filter, "filter");
    } else if (element._filter) {
        element.renderer.releaseDefsElements(element._filter);
        element._filter = null;
    }
    delete attrs.hatching;
    return attrs;
}
const buildArcPath = function(x, y, innerR, outerR, startAngleCos, startAngleSin, endAngleCos, endAngleSin, isCircle, longFlag) {
    return [
        "M",
        (x + outerR * startAngleCos).toFixed(5),
        (y - outerR * startAngleSin).toFixed(5),
        "A",
        outerR.toFixed(5),
        outerR.toFixed(5),
        0,
        longFlag,
        0,
        (x + outerR * endAngleCos).toFixed(5),
        (y - outerR * endAngleSin).toFixed(5),
        isCircle ? "M" : "L",
        (x + innerR * endAngleCos).toFixed(5),
        (y - innerR * endAngleSin).toFixed(5),
        "A",
        innerR.toFixed(5),
        innerR.toFixed(5),
        0,
        longFlag,
        1,
        (x + innerR * startAngleCos).toFixed(5),
        (y - innerR * startAngleSin).toFixed(5),
        "Z"
    ].join(" ");
};
function buildPathSegments(points, type) {
    let list = [
        [
            "M",
            0,
            0
        ]
    ];
    switch(type){
        case "line":
            list = buildLineSegments(points);
            break;
        case "area":
            list = buildLineSegments(points, true);
            break;
        case "bezier":
            list = buildCurveSegments(points);
            break;
        case "bezierarea":
            list = buildCurveSegments(points, true);
    }
    return list;
}
function buildLineSegments(points, close) {
    return buildSegments(points, buildSimpleLineSegment, close);
}
function buildCurveSegments(points, close) {
    return buildSegments(points, buildSimpleCurveSegment, close);
}
function buildSegments(points, buildSimpleSegment, close) {
    var _points$;
    let i;
    let ii;
    const list = [];
    if (null !== (_points$ = points[0]) && void 0 !== _points$ && _points$.length) {
        for(i = 0, ii = points.length; i < ii; ++i){
            buildSimpleSegment(points[i], close, list);
        }
    } else {
        buildSimpleSegment(points, close, list);
    }
    return list;
}
function buildSimpleLineSegment(points, close, list) {
    let i = 0;
    const k0 = list.length;
    let k = k0;
    const ii = (points || []).length;
    if (ii) {
        if (void 0 !== points[0].x) {
            for(; i < ii;){
                list[k++] = [
                    "L",
                    points[i].x,
                    points[i++].y
                ];
            }
        } else {
            for(; i < ii;){
                list[k++] = [
                    "L",
                    points[i++],
                    points[i++]
                ];
            }
        }
        list[k0][0] = "M";
    } else {
        list[k] = [
            "M",
            0,
            0
        ];
    }
    close && list.push([
        "Z"
    ]);
    return list;
}
function buildSimpleCurveSegment(points, close, list) {
    let i;
    let k = list.length;
    const ii = (points || []).length;
    if (ii) {
        if (void 0 !== points[0].x) {
            list[k++] = [
                "M",
                points[0].x,
                points[0].y
            ];
            for(i = 1; i < ii;){
                list[k++] = [
                    "C",
                    points[i].x,
                    points[i++].y,
                    points[i].x,
                    points[i++].y,
                    points[i].x,
                    points[i++].y
                ];
            }
        } else {
            list[k++] = [
                "M",
                points[0],
                points[1]
            ];
            for(i = 2; i < ii;){
                list[k++] = [
                    "C",
                    points[i++],
                    points[i++],
                    points[i++],
                    points[i++],
                    points[i++],
                    points[i++]
                ];
            }
        }
    } else {
        list[k] = [
            "M",
            0,
            0
        ];
    }
    close && list.push([
        "Z"
    ]);
    return list;
}
function combinePathParam(segments) {
    const d = [];
    let k = 0;
    let i;
    const ii = segments.length;
    let segment;
    let j;
    let jj;
    for(i = 0; i < ii; ++i){
        segment = segments[i];
        for(j = 0, jj = segment.length; j < jj; ++j){
            d[k++] = segment[j];
        }
    }
    return d.join(" ");
}
function compensateSegments(oldSegments, newSegments, type) {
    const oldLength = oldSegments.length;
    const newLength = newSegments.length;
    let i;
    let originalNewSegments;
    const makeEqualSegments = -1 !== type.indexOf("area") ? makeEqualAreaSegments : makeEqualLineSegments;
    if (0 === oldLength) {
        for(i = 0; i < newLength; i++){
            oldSegments.push(newSegments[i].slice(0));
        }
    } else if (oldLength < newLength) {
        makeEqualSegments(oldSegments, newSegments, type);
    } else if (oldLength > newLength) {
        originalNewSegments = newSegments.slice(0);
        makeEqualSegments(newSegments, oldSegments, type);
    }
    return originalNewSegments;
}
function prepareConstSegment(constSeg, type) {
    const x = constSeg[constSeg.length - 2];
    const y = constSeg[constSeg.length - 1];
    switch(type){
        case "line":
        case "area":
            constSeg[0] = "L";
            break;
        case "bezier":
        case "bezierarea":
            constSeg[0] = "C";
            constSeg[1] = constSeg[3] = constSeg[5] = x;
            constSeg[2] = constSeg[4] = constSeg[6] = y;
    }
}
function makeEqualLineSegments(short, long, type) {
    const constSeg = short[short.length - 1].slice();
    let i = short.length;
    prepareConstSegment(constSeg, type);
    for(; i < long.length; i++){
        short[i] = constSeg.slice(0);
    }
}
function makeEqualAreaSegments(short, long, type) {
    let i;
    let head;
    const shortLength = short.length;
    const longLength = long.length;
    let constsSeg1;
    let constsSeg2;
    if ((shortLength - 1) % 2 === 0 && (longLength - 1) % 2 === 0) {
        i = (shortLength - 1) / 2 - 1;
        head = short.slice(0, i + 1);
        constsSeg1 = head[head.length - 1].slice(0);
        constsSeg2 = short.slice(i + 1)[0].slice(0);
        prepareConstSegment(constsSeg1, type);
        prepareConstSegment(constsSeg2, type);
        for(let j = i; j < (longLength - 1) / 2 - 1; j++){
            short.splice(j + 1, 0, constsSeg1);
            short.splice(j + 3, 0, constsSeg2);
        }
    }
}
function baseCss(that, styles) {
    const elemStyles = that._styles;
    let key;
    let value;
    styles = styles || {};
    for(key in styles){
        value = styles[key];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value)) {
            value += "number" === typeof value && !pxAddingExceptions[key] ? "px" : "";
            elemStyles[key] = "" !== value ? value : null;
        }
    }
    for(key in elemStyles){
        value = elemStyles[key];
        if (value) {
            that.element.style[key] = value;
        } else if (null === value) {
            that.element.style[key] = "";
        }
    }
    return that;
}
function fixFuncIri(wrapper, attribute) {
    const element = wrapper.element;
    const id = wrapper.attr(attribute);
    if (id && -1 !== id.indexOf("DevExpress")) {
        element.removeAttribute(attribute);
        element.setAttribute(attribute, getFuncIri(id, wrapper.renderer.pathModified));
    }
}
function baseAttr(that, attrs) {
    attrs = attrs || {};
    const settings = that._settings;
    const attributes = {};
    let key;
    let value;
    const elem = that.element;
    const renderer = that.renderer;
    const rtl = renderer.rtl;
    let hasTransformations;
    let recalculateDashStyle;
    let sw;
    let i;
    if (!isObjectArgument(attrs)) {
        if (attrs in settings) {
            return settings[attrs];
        }
        if (attrs in DEFAULTS) {
            return DEFAULTS[attrs];
        }
        return 0;
    }
    extend(attributes, attrs);
    for(key in attributes){
        value = attributes[key];
        if (void 0 === value) {
            continue;
        }
        settings[key] = value;
        if ("align" === key) {
            key = "text-anchor";
            value = ({
                left: rtl ? "end" : "start",
                center: "middle",
                right: rtl ? "start" : "end"
            })[value] || null;
        } else if ("dashStyle" === key) {
            recalculateDashStyle = true;
            continue;
        } else if ("stroke-width" === key) {
            recalculateDashStyle = true;
        } else if (value && ("fill" === key || "clip-path" === key || "filter" === key) && 0 === value.indexOf("DevExpress")) {
            that._addFixIRICallback();
            value = getFuncIri(value, renderer.pathModified);
        } else if (/^(translate(X|Y)|rotate[XY]?|scale(X|Y)|sharp|sharpDirection)$/i.test(key)) {
            hasTransformations = true;
            continue;
        } else if (/^(x|y|d)$/i.test(key)) {
            hasTransformations = true;
        }
        if (null === value) {
            elem.removeAttribute(key);
        } else {
            elem.setAttribute(key, value);
        }
    }
    if (recalculateDashStyle && "dashStyle" in settings) {
        value = settings.dashStyle;
        sw = ("_originalSW" in that ? that._originalSW : settings["stroke-width"]) || 1;
        key = "stroke-dasharray";
        value = null === value ? "" : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(value);
        if ("" === value || "solid" === value || value === NONE) {
            that.element.removeAttribute(key);
        } else {
            value = value.replace(/longdash/g, "8,3,").replace(/dash/g, "4,3,").replace(/dot/g, "1,3,").replace(/,$/, "").split(",");
            i = value.length;
            while(i--){
                value[i] = parseInt(value[i]) * sw;
            }
            that.element.setAttribute(key, value.join(","));
        }
    }
    if (hasTransformations) {
        that._applyTransformation();
    }
    return that;
}
function pathAttr(attrs) {
    const that = this;
    let segments;
    if (isObjectArgument(attrs)) {
        attrs = extend({}, attrs);
        segments = attrs.segments;
        if ("points" in attrs) {
            segments = buildPathSegments(attrs.points, that.type);
            delete attrs.points;
        }
        if (segments) {
            attrs.d = combinePathParam(segments);
            that.segments = segments;
            delete attrs.segments;
        }
    }
    return baseAttr(that, attrs);
}
function arcAttr(attrs) {
    const settings = this._settings;
    let x;
    let y;
    let innerRadius;
    let outerRadius;
    let startAngle;
    let endAngle;
    if (isObjectArgument(attrs)) {
        attrs = extend({}, attrs);
        if ("x" in attrs || "y" in attrs || "innerRadius" in attrs || "outerRadius" in attrs || "startAngle" in attrs || "endAngle" in attrs) {
            settings.x = x = "x" in attrs ? attrs.x : settings.x;
            delete attrs.x;
            settings.y = y = "y" in attrs ? attrs.y : settings.y;
            delete attrs.y;
            settings.innerRadius = innerRadius = "innerRadius" in attrs ? attrs.innerRadius : settings.innerRadius;
            delete attrs.innerRadius;
            settings.outerRadius = outerRadius = "outerRadius" in attrs ? attrs.outerRadius : settings.outerRadius;
            delete attrs.outerRadius;
            settings.startAngle = startAngle = "startAngle" in attrs ? attrs.startAngle : settings.startAngle;
            delete attrs.startAngle;
            settings.endAngle = endAngle = "endAngle" in attrs ? attrs.endAngle : settings.endAngle;
            delete attrs.endAngle;
            attrs.d = buildArcPath.apply(null, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeArcParams"])(x, y, innerRadius, outerRadius, startAngle, endAngle));
        }
    }
    return baseAttr(this, attrs);
}
function rectAttr(attrs) {
    const that = this;
    let x;
    let y;
    let width;
    let height;
    let sw;
    let maxSW;
    let newSW;
    if (isObjectArgument(attrs)) {
        attrs = extend({}, attrs);
        if (void 0 !== attrs.x || void 0 !== attrs.y || void 0 !== attrs.width || void 0 !== attrs.height || void 0 !== attrs["stroke-width"]) {
            void 0 !== attrs.x ? x = that._originalX = attrs.x : x = that._originalX || 0;
            void 0 !== attrs.y ? y = that._originalY = attrs.y : y = that._originalY || 0;
            void 0 !== attrs.width ? width = that._originalWidth = attrs.width : width = that._originalWidth || 0;
            void 0 !== attrs.height ? height = that._originalHeight = attrs.height : height = that._originalHeight || 0;
            void 0 !== attrs["stroke-width"] ? sw = that._originalSW = attrs["stroke-width"] : sw = that._originalSW;
            maxSW = ~~((width < height ? width : height) / 2);
            newSW = (sw || 0) < maxSW ? sw || 0 : maxSW;
            attrs.x = x + newSW / 2;
            attrs.y = y + newSW / 2;
            attrs.width = width - newSW;
            attrs.height = height - newSW;
            ((sw || 0) !== newSW || !(0 === newSW && void 0 === sw)) && (attrs["stroke-width"] = newSW);
        }
        if ("sharp" in attrs) {
            delete attrs.sharp;
        }
    }
    return baseAttr(that, attrs);
}
function textAttr(attrs) {
    const that = this;
    let isResetRequired;
    if (!isObjectArgument(attrs)) {
        return baseAttr(that, attrs);
    }
    attrs = extend({}, attrs);
    const settings = that._settings;
    const wasStroked = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(settings.stroke) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(settings["stroke-width"]);
    if (void 0 !== attrs.text) {
        settings.text = attrs.text;
        delete attrs.text;
        isResetRequired = true;
    }
    if (void 0 !== attrs.stroke) {
        settings.stroke = attrs.stroke;
        delete attrs.stroke;
    }
    if (void 0 !== attrs["stroke-width"]) {
        settings["stroke-width"] = attrs["stroke-width"];
        delete attrs["stroke-width"];
    }
    if (void 0 !== attrs["stroke-opacity"]) {
        settings["stroke-opacity"] = attrs["stroke-opacity"];
        delete attrs["stroke-opacity"];
    }
    if (void 0 !== attrs.textsAlignment) {
        alignTextNodes(that, attrs.textsAlignment);
        delete attrs.textsAlignment;
    }
    const isStroked = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(settings.stroke) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(settings["stroke-width"]);
    baseAttr(that, attrs);
    isResetRequired = isResetRequired || isStroked !== wasStroked && settings.text;
    if (isResetRequired) {
        createTextNodes(that, settings.text, isStroked);
        that._hasEllipsis = false;
    }
    if (isResetRequired || void 0 !== attrs.x || void 0 !== attrs.y) {
        locateTextNodes(that);
    }
    if (isStroked) {
        strokeTextNodes(that);
    }
    return that;
}
function textCss(styles) {
    styles = styles || {};
    baseCss(this, styles);
    if ("font-size" in styles) {
        locateTextNodes(this);
    }
    return this;
}
function orderHtmlTree(list, line, node, parentStyle, parentClassName) {
    let style;
    let realStyle;
    let i;
    let ii;
    let nodes;
    if (void 0 !== node.wholeText) {
        list.push({
            value: node.wholeText,
            style: parentStyle,
            className: parentClassName,
            line: line,
            height: parentStyle["font-size"] || 0
        });
    } else if ("BR" === node.tagName) {
        ++line;
    } else if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isElementNode(node)) {
        extend(style = {}, parentStyle);
        switch(node.tagName){
            case "B":
            case "STRONG":
                style["font-weight"] = "bold";
                break;
            case "I":
            case "EM":
                style["font-style"] = "italic";
                break;
            case "U":
                style["text-decoration"] = "underline";
        }
        realStyle = node.style;
        realStyle.color && (style.fill = realStyle.color);
        realStyle.fontSize && (style["font-size"] = realStyle.fontSize);
        realStyle.fontStyle && (style["font-style"] = realStyle.fontStyle);
        realStyle.fontWeight && (style["font-weight"] = realStyle.fontWeight);
        realStyle.textDecoration && (style["text-decoration"] = realStyle.textDecoration);
        for(i = 0, nodes = node.childNodes, ii = nodes.length; i < ii; ++i){
            line = orderHtmlTree(list, line, nodes[i], style, node.className || parentClassName);
        }
    }
    return line;
}
function adjustLineHeights(items) {
    let i;
    let ii;
    let currentItem = items[0];
    let item;
    for(i = 1, ii = items.length; i < ii; ++i){
        item = items[i];
        if (item.line === currentItem.line) {
            currentItem.height = maxLengthFontSize(currentItem.height, item.height);
            currentItem.inherits = currentItem.inherits || 0 === parseFloat(item.height);
            item.height = NaN;
        } else {
            currentItem = item;
        }
    }
}
function removeExtraAttrs(html) {
    const findStyleAndClassAttrs = /(style|class)\s*=\s*(["'])(?:(?!\2).)*\2\s?/gi;
    return html.replace(/(?:(<[a-z0-9]+\s*))([\s\S]*?)(>|\/>)/gi, function(allTagAttrs, p1, p2, p3) {
        p2 = (p2 && p2.match(findStyleAndClassAttrs) || []).map(function(str) {
            return str;
        }).join(" ");
        return p1 + p2 + p3;
    });
}
function parseHTML(text) {
    const items = [];
    const div = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div");
    div.innerHTML = text.replace(/\r/g, "").replace(/\n/g, "<br/>").replace(/style=/g, "data-style=");
    div.querySelectorAll("[data-style]").forEach((element)=>{
        element.style = element.getAttribute("data-style");
        element.removeAttribute("data-style");
    });
    orderHtmlTree(items, 0, div, {}, "");
    adjustLineHeights(items);
    return items;
}
function parseMultiline(text) {
    const texts = text.replace(/\r/g, "").split(/\n/g);
    let i = 0;
    const items = [];
    for(; i < texts.length; i++){
        items.push({
            value: texts[i].trim(),
            height: 0,
            line: i
        });
    }
    return items;
}
function createTspans(items, element, fieldName) {
    let i;
    let ii;
    let item;
    for(i = 0, ii = items.length; i < ii; ++i){
        item = items[i];
        item[fieldName] = createElement("tspan");
        item[fieldName].appendChild(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createTextNode(item.value));
        item.style && baseCss({
            element: item[fieldName],
            _styles: {}
        }, item.style);
        item.className && item[fieldName].setAttribute("class", item.className);
        element.appendChild(item[fieldName]);
    }
}
function restoreText() {
    if (this._hasEllipsis) {
        this.attr({
            text: this._settings.text
        });
    }
}
function applyEllipsis(maxWidth) {
    const that = this;
    let lines;
    let hasEllipsis = false;
    let i;
    let ii;
    let lineParts;
    let j;
    let jj;
    let text;
    restoreText.call(that);
    const ellipsis = that.renderer.text("...").attr(that._styles).append(that.renderer.root);
    const ellipsisWidth = ellipsis.getBBox().width;
    if (that._getElementBBox().width > maxWidth) {
        if (maxWidth - ellipsisWidth < 0) {
            maxWidth = 0;
        } else {
            maxWidth -= ellipsisWidth;
        }
        lines = prepareLines(that.element, that._texts, maxWidth);
        for(i = 0, ii = lines.length; i < ii; ++i){
            lineParts = lines[i].parts;
            if (1 === lines[i].commonLength) {
                continue;
            }
            for(j = 0, jj = lineParts.length; j < jj; ++j){
                text = lineParts[j];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(text.endIndex)) {
                    setNewText(text, text.endIndex);
                    hasEllipsis = true;
                } else if (text.startBox > maxWidth) {
                    removeTextSpan(text);
                }
            }
        }
    }
    ellipsis.remove();
    that._hasEllipsis = hasEllipsis;
    return hasEllipsis;
}
function cloneAndRemoveAttrs(node) {
    let clone;
    if (node) {
        clone = node.cloneNode();
        clone.removeAttribute("y");
        clone.removeAttribute("x");
    }
    return clone || node;
}
function detachTitleElements(element) {
    const titleElements = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].querySelectorAll(element, "title");
    for(let i = 0; i < titleElements.length; i++){
        element.removeChild(titleElements[i]);
    }
    return titleElements;
}
function detachAndStoreTitleElements(element) {
    const titleElements = detachTitleElements(element);
    return ()=>{
        for(let i = 0; i < titleElements.length; i++){
            element.appendChild(titleElements[i]);
        }
    };
}
function setMaxSize(maxWidth, maxHeight) {
    let options = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
    const that = this;
    let lines = [];
    let textChanged = false;
    let textIsEmpty = false;
    let ellipsisMaxWidth = maxWidth;
    restoreText.call(that);
    const restoreTitleElement = detachAndStoreTitleElements(this.element);
    const ellipsis = that.renderer.text("...").attr(that._styles).append(that.renderer.root);
    const ellipsisWidth = ellipsis.getBBox().width;
    const { width: width, height: height } = that._getElementBBox();
    if ((width || height) && (width > maxWidth || maxHeight && height > maxHeight)) {
        if (maxWidth - ellipsisWidth < 0) {
            ellipsisMaxWidth = 0;
        } else {
            ellipsisMaxWidth -= ellipsisWidth;
        }
        lines = applyOverflowRules(that.element, that._texts, maxWidth, ellipsisMaxWidth, options);
        lines = setMaxHeight(lines, ellipsisMaxWidth, options, maxHeight, parseFloat(this._getLineHeight()));
        this._texts = lines.reduce((texts, line)=>texts.concat(line.parts), []).filter((t)=>"" !== t.value).map((t)=>{
            t.stroke && t.tspan.parentNode.appendChild(t.stroke);
            return t;
        }).map((t)=>{
            t.tspan.parentNode.appendChild(t.tspan);
            return t;
        });
        !this._texts.length && (this._texts = null);
        textChanged = true;
        if (this._texts) {
            locateTextNodes(this);
        } else {
            this.element.textContent = "";
            textIsEmpty = true;
        }
    }
    ellipsis.remove();
    that._hasEllipsis = textChanged;
    restoreTitleElement();
    return {
        rowCount: lines.length,
        textChanged: textChanged,
        textIsEmpty: textIsEmpty
    };
}
function getIndexForEllipsis(text, maxWidth, startBox, endBox) {
    let k;
    let kk;
    if (startBox <= maxWidth && endBox > maxWidth) {
        for(k = 1, kk = text.value.length; k <= kk; ++k){
            if (startBox + text.tspan.getSubStringLength(0, k) > maxWidth) {
                return k - 1;
            }
        }
    }
}
function getTextWidth(text) {
    return text.value.length ? text.tspan.getSubStringLength(0, text.value.length) : 0;
}
function prepareLines(element, texts, maxWidth) {
    let lines = [];
    let i;
    let ii;
    let text;
    let startBox;
    let endBox;
    if (texts) {
        for(i = 0, ii = texts.length; i < ii; ++i){
            text = texts[i];
            if (!lines[text.line]) {
                text.startBox = startBox = 0;
                lines.push({
                    commonLength: text.value.length,
                    parts: [
                        text
                    ]
                });
            } else {
                text.startBox = startBox;
                lines[text.line].parts.push(text);
                lines[text.line].commonLength += text.value.length;
            }
            endBox = startBox + text.tspan.getSubStringLength(0, text.value.length);
            text.endIndex = getIndexForEllipsis(text, maxWidth, startBox, endBox);
            startBox = endBox;
        }
    } else {
        text = {
            value: element.textContent,
            tspan: element
        };
        text.startBox = startBox = 0;
        endBox = startBox + getTextWidth(text);
        text.endIndex = getIndexForEllipsis(text, maxWidth, startBox, endBox);
        lines = [
            {
                commonLength: element.textContent.length,
                parts: [
                    text
                ]
            }
        ];
    }
    return lines;
}
function getSpaceBreakIndex(text, maxWidth) {
    const initialIndices = text.startBox > 0 ? [
        0
    ] : [];
    const spaceIndices = text.value.split("").reduce((indices, char, index)=>{
        if (" " === char) {
            indices.push(index);
        }
        return indices;
    }, initialIndices);
    let spaceIndex = 0;
    while(void 0 !== spaceIndices[spaceIndex + 1] && text.startBox + text.tspan.getSubStringLength(0, spaceIndices[spaceIndex + 1]) < maxWidth){
        spaceIndex++;
    }
    return spaceIndices[spaceIndex];
}
function getWordBreakIndex(text, maxWidth) {
    for(let i = 0; i < text.value.length - 1; i++){
        if (text.startBox + text.tspan.getSubStringLength(0, i + 1) > maxWidth) {
            return i;
        }
    }
}
function getEllipsisString(ellipsisMaxWidth, _ref) {
    let { hideOverflowEllipsis: hideOverflowEllipsis } = _ref;
    return hideOverflowEllipsis && 0 === ellipsisMaxWidth ? "" : "...";
}
function setEllipsis(text, ellipsisMaxWidth, options) {
    const ellipsis = getEllipsisString(ellipsisMaxWidth, options);
    if (text.value.length && text.tspan.parentNode) {
        for(let i = text.value.length - 1; i >= 1; i--){
            if (text.startBox + text.tspan.getSubStringLength(0, i) < ellipsisMaxWidth) {
                setNewText(text, i, ellipsis);
                break;
            } else if (1 === i) {
                setNewText(text, 0, ellipsis);
            }
        }
    }
}
function wordWrap(text, maxWidth, ellipsisMaxWidth, options, lastStepBreakIndex) {
    const wholeText = text.value;
    let breakIndex;
    if ("none" !== options.wordWrap) {
        breakIndex = "normal" === options.wordWrap ? getSpaceBreakIndex(text, maxWidth) : getWordBreakIndex(text, maxWidth);
    }
    let restLines = [];
    let restText;
    if (isFinite(breakIndex) && !(0 === lastStepBreakIndex && 0 === breakIndex)) {
        setNewText(text, breakIndex, "");
        const newTextOffset = " " === wholeText[breakIndex] ? 1 : 0;
        const restString = wholeText.slice(breakIndex + newTextOffset);
        if (restString.length) {
            const restTspan = cloneAndRemoveAttrs(text.tspan);
            restTspan.textContent = restString;
            text.tspan.parentNode.appendChild(restTspan);
            restText = extend(extend({}, text), {
                value: restString,
                startBox: 0,
                height: 0,
                tspan: restTspan,
                stroke: cloneAndRemoveAttrs(text.stroke),
                endBox: restTspan.getSubStringLength(0, restString.length)
            });
            restText.stroke && (restText.stroke.textContent = restString);
            if (restText.endBox > maxWidth) {
                restLines = wordWrap(restText, maxWidth, ellipsisMaxWidth, options, breakIndex);
                if (!restLines.length) {
                    return [];
                }
            }
        }
    }
    if (text.value.length) {
        if ("ellipsis" === options.textOverflow && text.tspan.getSubStringLength(0, text.value.length) > maxWidth) {
            setEllipsis(text, ellipsisMaxWidth, options);
        }
        if ("hide" === options.textOverflow && text.tspan.getSubStringLength(0, text.value.length) > maxWidth) {
            return [];
        }
    } else {
        text.tspan.parentNode.removeChild(text.tspan);
    }
    const parts = [];
    if (restText) {
        parts.push(restText);
    }
    return [
        {
            commonLength: wholeText.length,
            parts: parts
        }
    ].concat(restLines);
}
function calculateLineHeight(line, lineHeight) {
    return line.parts.reduce((height, text)=>max(height, getItemLineHeight(text, lineHeight)), 0);
}
function setMaxHeight(lines, ellipsisMaxWidth, options, maxHeight, lineHeight) {
    const textOverflow = options.textOverflow;
    if (!isFinite(maxHeight) || 0 === Number(maxHeight) || "none" === textOverflow) {
        return lines;
    }
    const result = lines.reduce((_ref2, l, index, arr)=>{
        let [lines, commonHeight] = _ref2;
        const height = calculateLineHeight(l, lineHeight);
        commonHeight += height;
        if (commonHeight < maxHeight) {
            lines.push(l);
        } else {
            l.parts.forEach((item)=>{
                removeTextSpan(item);
            });
            if ("ellipsis" === textOverflow) {
                const prevLine = arr[index - 1];
                if (prevLine) {
                    const text = prevLine.parts[prevLine.parts.length - 1];
                    if (!text.hasEllipsis) {
                        if (0 === ellipsisMaxWidth || text.endBox < ellipsisMaxWidth) {
                            setNewText(text, text.value.length, getEllipsisString(ellipsisMaxWidth, options));
                        } else {
                            setEllipsis(text, ellipsisMaxWidth, options);
                        }
                    }
                }
            }
        }
        return [
            lines,
            commonHeight
        ];
    }, [
        [],
        0
    ]);
    if ("hide" === textOverflow && result[1] > maxHeight) {
        result[0].forEach((l)=>{
            l.parts.forEach((item)=>{
                removeTextSpan(item);
            });
        });
        return [];
    }
    return result[0];
}
function applyOverflowRules(element, texts, maxWidth, ellipsisMaxWidth, options) {
    if (!texts) {
        const textValue = element.textContent;
        const text = {
            value: textValue,
            height: 0,
            line: 0
        };
        element.textContent = "";
        createTspans([
            text
        ], element, "tspan");
        texts = [
            text
        ];
    }
    return texts.reduce((_ref3, text)=>{
        let [lines, startBox, endBox, stop, lineNumber] = _ref3;
        const line = lines[lines.length - 1];
        if (stop) {
            return [
                lines,
                startBox,
                endBox,
                stop
            ];
        }
        if (!line || text.line !== lineNumber) {
            text.startBox = startBox = 0;
            lines.push({
                commonLength: text.value.length,
                parts: [
                    text
                ]
            });
        } else {
            text.startBox = startBox;
            if (startBox > ellipsisMaxWidth && "none" === options.wordWrap && "ellipsis" === options.textOverflow) {
                removeTextSpan(text);
                return [
                    lines,
                    startBox,
                    endBox,
                    stop,
                    lineNumber
                ];
            }
            line.parts.push(text);
            line.commonLength += text.value.length;
        }
        text.endBox = endBox = startBox + getTextWidth(text);
        startBox = endBox;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(maxWidth) && endBox > maxWidth) {
            const wordWrapLines = wordWrap(text, maxWidth, ellipsisMaxWidth, options);
            if (!wordWrapLines.length) {
                lines = [];
                stop = true;
            } else {
                lines = lines.concat(wordWrapLines.filter((l)=>l.parts.length > 0));
            }
        }
        return [
            lines,
            startBox,
            endBox,
            stop,
            text.line
        ];
    }, [
        [],
        0,
        0,
        false,
        0
    ])[0];
}
function setNewText(text, index) {
    let insertString = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : "...";
    const newText = text.value.substr(0, index) + insertString;
    text.value = text.tspan.textContent = newText;
    text.stroke && (text.stroke.textContent = newText);
    if ("..." === insertString) {
        text.hasEllipsis = true;
    }
}
function removeTextSpan(text) {
    text.tspan.parentNode && text.tspan.parentNode.removeChild(text.tspan);
    text.stroke && text.stroke.parentNode && text.stroke.parentNode.removeChild(text.stroke);
}
function createTextNodes(wrapper, text, isStroked) {
    let items;
    let parsedHtml;
    wrapper._texts = null;
    wrapper.clear();
    if (null === text) {
        return;
    }
    text = "" + text;
    if (!wrapper.renderer.encodeHtml && (/<[a-z][\s\S]*>/i.test(text) || -1 !== text.indexOf("&"))) {
        parsedHtml = removeExtraAttrs(text);
        items = parseHTML(parsedHtml);
    } else if (/\n/g.test(text)) {
        items = parseMultiline(text);
    } else if (isStroked) {
        items = [
            {
                value: text.trim(),
                height: 0
            }
        ];
    }
    if (items) {
        if (items.length) {
            wrapper._texts = items;
            if (isStroked) {
                createTspans(items, wrapper.element, "stroke");
            }
            createTspans(items, wrapper.element, "tspan");
        }
    } else {
        wrapper.element.appendChild(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createTextNode(text));
    }
}
function setTextNodeAttribute(item, name, value) {
    item.tspan.setAttribute(name, value);
    item.stroke && item.stroke.setAttribute(name, value);
}
function getItemLineHeight(item, defaultValue) {
    return item.inherits ? maxLengthFontSize(item.height, defaultValue) : item.height || defaultValue;
}
function locateTextNodes(wrapper) {
    if (!wrapper._texts) {
        return;
    }
    const items = wrapper._texts;
    const x = wrapper._settings.x;
    const lineHeight = wrapper._getLineHeight();
    let i;
    let ii;
    let item = items[0];
    setTextNodeAttribute(item, "x", x);
    setTextNodeAttribute(item, "y", wrapper._settings.y);
    for(i = 1, ii = items.length; i < ii; ++i){
        item = items[i];
        if (parseFloat(item.height) >= 0) {
            setTextNodeAttribute(item, "x", x);
            const height = getItemLineHeight(item, lineHeight);
            setTextNodeAttribute(item, "dy", height);
        }
    }
}
function alignTextNodes(wrapper, alignment) {
    if (!wrapper._texts || "center" === alignment) {
        return;
    }
    const items = wrapper._texts;
    const direction = "left" === alignment ? -1 : 1;
    const maxTextWidth = Math.max.apply(Math, items.map((t)=>getTextWidth(t)));
    for(let i = 0; i < items.length; i++){
        const item = items[i];
        const textWidth = getTextWidth(item);
        if (0 !== maxTextWidth && maxTextWidth !== textWidth) {
            setTextNodeAttribute(item, "dx", direction * round((maxTextWidth - textWidth) / 2 * 10) / 10);
        }
    }
}
function maxLengthFontSize(fontSize1, fontSize2) {
    const parsedHeight1 = parseFloat(fontSize1);
    const parsedHeight2 = parseFloat(fontSize2);
    const height1 = parsedHeight1 || 12;
    const height2 = parsedHeight2 || 12;
    return height1 > height2 ? !isNaN(parsedHeight1) ? fontSize1 : height1 : !isNaN(parsedHeight2) ? fontSize2 : height2;
}
function strokeTextNodes(wrapper) {
    if (!wrapper._texts) {
        return;
    }
    const items = wrapper._texts;
    const stroke = wrapper._settings.stroke;
    const strokeWidth = wrapper._settings["stroke-width"];
    const strokeOpacity = wrapper._settings["stroke-opacity"] || 1;
    let tspan;
    let i;
    let ii;
    for(i = 0, ii = items.length; i < ii; ++i){
        tspan = items[i].stroke;
        tspan.setAttribute("stroke", stroke);
        tspan.setAttribute("stroke-width", strokeWidth);
        tspan.setAttribute("stroke-opacity", strokeOpacity);
        tspan.setAttribute("stroke-linejoin", "round");
    }
}
function baseAnimate(that, params, options, complete) {
    options = options || {};
    let key;
    let value;
    const renderer = that.renderer;
    const settings = that._settings;
    const animationParams = {};
    const defaults = {
        translateX: 0,
        translateY: 0,
        scaleX: 1,
        scaleY: 1,
        rotate: 0,
        rotateX: 0,
        rotateY: 0
    };
    if (complete) {
        options.complete = complete;
    }
    if (renderer.animationEnabled()) {
        for(key in params){
            value = params[key];
            if (/^(translate(X|Y)|rotate[XY]?|scale(X|Y))$/i.test(key)) {
                animationParams.transform = animationParams.transform || {
                    from: {},
                    to: {}
                };
                animationParams.transform.from[key] = key in settings ? Number(settings[key].toFixed(3)) : defaults[key];
                animationParams.transform.to[key] = value;
            } else if ("arc" === key || "segments" === key) {
                animationParams[key] = value;
            } else {
                animationParams[key] = {
                    from: key in settings ? settings[key] : parseFloat(that.element.getAttribute(key) || 0),
                    to: value
                };
            }
        }
        renderer.animateElement(that, animationParams, extend(extend({}, renderer._animation), options));
    } else {
        options.step && options.step.call(that, 1, 1);
        options.complete && options.complete.call(that);
        that.attr(params);
    }
    return that;
}
function pathAnimate(params, options, complete) {
    const that = this;
    const curSegments = that.segments || [];
    let newSegments;
    let endSegments;
    if (that.renderer.animationEnabled() && "points" in params) {
        newSegments = buildPathSegments(params.points, that.type);
        endSegments = compensateSegments(curSegments, newSegments, that.type);
        params.segments = {
            from: curSegments,
            to: newSegments,
            end: endSegments
        };
        delete params.points;
    }
    return baseAnimate(that, params, options, complete);
}
function arcAnimate(params, options, complete) {
    const settings = this._settings;
    const arcParams = {
        from: {},
        to: {}
    };
    if (this.renderer.animationEnabled() && ("x" in params || "y" in params || "innerRadius" in params || "outerRadius" in params || "startAngle" in params || "endAngle" in params)) {
        arcParams.from.x = settings.x || 0;
        arcParams.from.y = settings.y || 0;
        arcParams.from.innerRadius = settings.innerRadius || 0;
        arcParams.from.outerRadius = settings.outerRadius || 0;
        arcParams.from.startAngle = settings.startAngle || 0;
        arcParams.from.endAngle = settings.endAngle || 0;
        arcParams.to.x = "x" in params ? params.x : settings.x;
        delete params.x;
        arcParams.to.y = "y" in params ? params.y : settings.y;
        delete params.y;
        arcParams.to.innerRadius = "innerRadius" in params ? params.innerRadius : settings.innerRadius;
        delete params.innerRadius;
        arcParams.to.outerRadius = "outerRadius" in params ? params.outerRadius : settings.outerRadius;
        delete params.outerRadius;
        arcParams.to.startAngle = "startAngle" in params ? params.startAngle : settings.startAngle;
        delete params.startAngle;
        arcParams.to.endAngle = "endAngle" in params ? params.endAngle : settings.endAngle;
        delete params.endAngle;
        params.arc = arcParams;
    }
    return baseAnimate(this, params, options, complete);
}
function buildLink(target, parameters) {
    const obj = {
        is: false,
        name: parameters.name || parameters,
        after: parameters.after
    };
    if (target) {
        obj.to = target;
    } else {
        obj.virtual = true;
    }
    return obj;
}
let SvgElement = function(renderer, tagName, type) {
    const that = this;
    that.renderer = renderer;
    that.element = createElement(tagName);
    that._settings = {};
    that._styles = {};
    if ("path" === tagName) {
        that.type = type || "line";
    }
};
function removeFuncIriCallback(callback) {
    fixFuncIriCallbacks.remove(callback);
}
SvgElement.prototype = {
    constructor: SvgElement,
    _getJQElement: function() {
        return this._$element || (this._$element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this.element));
    },
    _addFixIRICallback: function() {
        const that = this;
        const fn = function() {
            fixFuncIri(that, "fill");
            fixFuncIri(that, "clip-path");
            fixFuncIri(that, "filter");
        };
        that.element._fixFuncIri = fn;
        fn.renderer = that.renderer;
        fixFuncIriCallbacks.add(fn);
        that._addFixIRICallback = function() {};
    },
    _clearChildrenFuncIri: function() {
        const clearChildren = function(element) {
            let i;
            for(i = 0; i < element.childNodes.length; i++){
                removeFuncIriCallback(element.childNodes[i]._fixFuncIri);
                clearChildren(element.childNodes[i]);
            }
        };
        clearChildren(this.element);
    },
    dispose: function() {
        removeFuncIriCallback(this.element._fixFuncIri);
        this._clearChildrenFuncIri();
        this._getJQElement().remove();
        return this;
    },
    append: function(parent) {
        (parent || this.renderer.root).element.appendChild(this.element);
        return this;
    },
    remove: function() {
        const element = this.element;
        element.parentNode && element.parentNode.removeChild(element);
        return this;
    },
    enableLinks: function() {
        this._links = [];
        return this;
    },
    virtualLink: function(parameters) {
        linkItem({
            _link: buildLink(null, parameters)
        }, this);
        return this;
    },
    linkAfter: function(name) {
        this._linkAfter = name;
        return this;
    },
    linkOn: function(target, parameters) {
        this._link = buildLink(target, parameters);
        linkItem(this, target);
        return this;
    },
    linkOff: function() {
        unlinkItem(this);
        this._link = null;
        return this;
    },
    linkAppend: function() {
        const link = this._link;
        const items = link.to._links;
        let i;
        let next;
        for(i = link.i + 1; (next = items[i]) && !next._link.is; ++i){}
        this._insert(link.to, next);
        link.is = true;
        return this;
    },
    _insert: function(parent, next) {
        parent.element.insertBefore(this.element, next ? next.element : null);
    },
    linkRemove: function() {
        this.remove();
        this._link.is = false;
        return this;
    },
    clear: function() {
        this._clearChildrenFuncIri();
        this._getJQElement().empty();
        return this;
    },
    toBackground: function() {
        const elem = this.element;
        const parent = elem.parentNode;
        parent && parent.insertBefore(elem, parent.firstChild);
        return this;
    },
    toForeground: function() {
        const elem = this.element;
        const parent = elem.parentNode;
        parent && parent.appendChild(elem);
        return this;
    },
    attr: function(attrs) {
        return baseAttr(this, attrs);
    },
    smartAttr: function(attrs) {
        return this.attr(processHatchingAttrs(this, attrs));
    },
    css: function(styles) {
        return baseCss(this, styles);
    },
    animate: function(params, options, complete) {
        return baseAnimate(this, params, options, complete);
    },
    sharp (pos, sharpDirection) {
        return this.attr({
            sharp: pos || true,
            sharpDirection: sharpDirection
        });
    },
    _applyTransformation () {
        const tr = this._settings;
        let rotateX;
        let rotateY;
        const transformations = [];
        const sharpMode = tr.sharp;
        const trDirection = tr.sharpDirection || 1;
        const strokeOdd = tr["stroke-width"] % 2;
        const correctionX = strokeOdd && ("h" === sharpMode || true === sharpMode) ? .5 * trDirection : 0;
        const correctionY = strokeOdd && ("v" === sharpMode || true === sharpMode) ? .5 * trDirection : 0;
        transformations.push("translate(" + ((tr.translateX || 0) + correctionX) + "," + ((tr.translateY || 0) + correctionY) + ")");
        if (tr.rotate) {
            if ("rotateX" in tr) {
                rotateX = tr.rotateX;
            } else {
                rotateX = tr.x;
            }
            if ("rotateY" in tr) {
                rotateY = tr.rotateY;
            } else {
                rotateY = tr.y;
            }
            transformations.push("rotate(" + tr.rotate + "," + (rotateX || 0) + "," + (rotateY || 0) + ")");
        }
        const scaleXDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(tr.scaleX);
        const scaleYDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(tr.scaleY);
        if (scaleXDefined || scaleYDefined) {
            transformations.push("scale(" + (scaleXDefined ? tr.scaleX : 1) + "," + (scaleYDefined ? tr.scaleY : 1) + ")");
        }
        if (transformations.length) {
            this.element.setAttribute("transform", transformations.join(" "));
        }
    },
    move: function(x, y, animate, animOptions) {
        const obj = {};
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(x) && (obj.translateX = x);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(y) && (obj.translateY = y);
        if (!animate) {
            this.attr(obj);
        } else {
            this.animate(obj, animOptions);
        }
        return this;
    },
    rotate: function(angle, x, y, animate, animOptions) {
        const obj = {
            rotate: angle || 0
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(x) && (obj.rotateX = x);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(y) && (obj.rotateY = y);
        if (!animate) {
            this.attr(obj);
        } else {
            this.animate(obj, animOptions);
        }
        return this;
    },
    _getElementBBox: function() {
        const elem = this.element;
        let bBox;
        try {
            bBox = elem.getBBox && elem.getBBox();
        } catch (e) {}
        return bBox || {
            x: 0,
            y: 0,
            width: elem.offsetWidth || 0,
            height: elem.offsetHeight || 0
        };
    },
    getBBox: function() {
        const transformation = this._settings;
        let bBox = this._getElementBBox();
        if (transformation.rotate) {
            bBox = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["rotateBBox"])(bBox, [
                ("rotateX" in transformation ? transformation.rotateX : transformation.x) || 0,
                ("rotateY" in transformation ? transformation.rotateY : transformation.y) || 0
            ], -transformation.rotate);
        } else {
            bBox = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeBBox"])(bBox);
        }
        return bBox;
    },
    markup: function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getSvgMarkup"])(this.element);
    },
    getOffset: function() {
        return this._getJQElement().offset();
    },
    stopAnimation: function(disableComplete) {
        const animation = this.animation;
        animation && animation.stop(disableComplete);
        return this;
    },
    setTitle: function(text) {
        const titleElem = createElement("title");
        titleElem.textContent = text || "";
        this.element.appendChild(titleElem);
    },
    removeTitle () {
        detachTitleElements(this.element);
    },
    data: function(obj, val) {
        const elem = this.element;
        let key;
        if (void 0 !== val) {
            elem[obj] = val;
        } else {
            for(key in obj){
                elem[key] = obj[key];
            }
        }
        return this;
    },
    on: function() {
        const args = [
            this._getJQElement()
        ];
        args.push.apply(args, arguments);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on.apply(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], args);
        return this;
    },
    off: function() {
        const args = [
            this._getJQElement()
        ];
        args.push.apply(args, arguments);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].off.apply(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], args);
        return this;
    },
    trigger: function() {
        const args = [
            this._getJQElement()
        ];
        args.push.apply(args, arguments);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].trigger.apply(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], args);
        return this;
    }
};
let PathSvgElement = function(renderer, type) {
    SvgElement.call(this, renderer, "path", type);
};
PathSvgElement.prototype = objectCreate(SvgElement.prototype);
extend(PathSvgElement.prototype, {
    constructor: PathSvgElement,
    attr: pathAttr,
    animate: pathAnimate
});
let ArcSvgElement = function(renderer) {
    SvgElement.call(this, renderer, "path", "arc");
};
ArcSvgElement.prototype = objectCreate(SvgElement.prototype);
extend(ArcSvgElement.prototype, {
    constructor: ArcSvgElement,
    attr: arcAttr,
    animate: arcAnimate
});
let RectSvgElement = function(renderer) {
    SvgElement.call(this, renderer, "rect");
};
RectSvgElement.prototype = objectCreate(SvgElement.prototype);
extend(RectSvgElement.prototype, {
    constructor: RectSvgElement,
    attr: rectAttr
});
let TextSvgElement = function(renderer) {
    SvgElement.call(this, renderer, "text");
    this.css({
        "white-space": "pre"
    });
};
TextSvgElement.prototype = objectCreate(SvgElement.prototype);
extend(TextSvgElement.prototype, {
    constructor: TextSvgElement,
    attr: textAttr,
    css: textCss,
    applyEllipsis: applyEllipsis,
    setMaxSize: setMaxSize,
    restoreText: restoreText,
    _getLineHeight () {
        return !isNaN(parseFloat(this._styles["font-size"])) ? this._styles["font-size"] : 12;
    }
});
function updateIndexes(items, k) {
    let i;
    let item;
    for(i = k; item = items[i]; ++i){
        item._link.i = i;
    }
}
function linkItem(target, container) {
    const items = container._links;
    const key = target._link.after = target._link.after || container._linkAfter;
    let i;
    let item;
    if (key) {
        for(i = 0; (item = items[i]) && item._link.name !== key; ++i){}
        if (item) {
            for(++i; (item = items[i]) && item._link.after === key; ++i){}
        }
    } else {
        i = items.length;
    }
    items.splice(i, 0, target);
    updateIndexes(items, i);
}
function unlinkItem(target) {
    let i;
    const items = target._link.to._links;
    for(i = 0; items[i] !== target; ++i){}
    items.splice(i, 1);
    updateIndexes(items, i);
}
function Renderer(options) {
    this.root = this._createElement("svg", {
        xmlns: "http://www.w3.org/2000/svg",
        version: "1.1",
        fill: NONE,
        stroke: NONE,
        "stroke-width": 0
    }).attr({
        class: options.cssClass
    }).css({
        "line-height": "normal",
        "-moz-user-select": NONE,
        "-webkit-user-select": NONE,
        "-webkit-tap-highlight-color": "rgba(0, 0, 0, 0)",
        display: "block",
        overflow: "hidden"
    });
    this._init();
    this.pathModified = !!options.pathModified;
    this._$container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(options.container);
    this.root.append({
        element: options.container
    });
    this._locker = 0;
    this._backed = false;
}
Renderer.prototype = {
    constructor: Renderer,
    _init: function() {
        this._defs = this._createElement("defs").append(this.root);
        this._animationController = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AnimationController"](this.root.element);
        this._animation = {
            enabled: true,
            duration: 1e3,
            easing: "easeOutCubic"
        };
    },
    setOptions: function(options) {
        this.rtl = !!options.rtl;
        this.encodeHtml = !!options.encodeHtml;
        this.updateAnimationOptions(options.animation || {});
        this.root.attr({
            direction: this.rtl ? "rtl" : "ltr"
        });
        return this;
    },
    _createElement: function(tagName, attr, type) {
        const elem = new SvgElement(this, tagName, type);
        attr && elem.attr(attr);
        return elem;
    },
    lock: function() {
        const that = this;
        if (0 === that._locker) {
            that._backed = !that._$container.is(":visible");
            if (that._backed) {
                backupRoot(that.root);
            }
        }
        ++that._locker;
        return that;
    },
    unlock: function() {
        const that = this;
        --that._locker;
        if (0 === that._locker) {
            if (that._backed) {
                restoreRoot(that.root, that._$container[0]);
            }
            that._backed = false;
        }
        return that;
    },
    resize: function(width, height) {
        if (width >= 0 && height >= 0) {
            this.root.attr({
                width: width,
                height: height
            });
        }
        return this;
    },
    dispose: function() {
        const that = this;
        let key;
        that.root.dispose();
        that._defs.dispose();
        that._animationController.dispose();
        fixFuncIriCallbacks.removeByRenderer(that);
        for(key in that){
            that[key] = null;
        }
        return that;
    },
    animationEnabled: function() {
        return !!this._animation.enabled;
    },
    updateAnimationOptions: function(newOptions) {
        extend(this._animation, newOptions);
        return this;
    },
    stopAllAnimations: function(lock) {
        this._animationController[lock ? "lock" : "stop"]();
        return this;
    },
    animateElement: function(element, params, options) {
        this._animationController.animateElement(element, params, options);
        return this;
    },
    svg: function() {
        return this.root.markup();
    },
    getRootOffset: function() {
        return this.root.getOffset();
    },
    onEndAnimation: function(endAnimation) {
        this._animationController.onEndAnimation(endAnimation);
    },
    rect: function(x, y, width, height) {
        const elem = new RectSvgElement(this);
        return elem.attr({
            x: x || 0,
            y: y || 0,
            width: width || 0,
            height: height || 0
        });
    },
    simpleRect: function() {
        return this._createElement("rect");
    },
    circle: function(x, y, r) {
        return this._createElement("circle", {
            cx: x || 0,
            cy: y || 0,
            r: r || 0
        });
    },
    g: function() {
        return this._createElement("g");
    },
    image: function(x, y, w, h, href, location) {
        const image = this._createElement("image", {
            x: x || 0,
            y: y || 0,
            width: w || 0,
            height: h || 0,
            preserveAspectRatio: preserveAspectRatioMap[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(location)] || NONE
        });
        image.element.setAttributeNS("http://www.w3.org/1999/xlink", "href", href || "");
        return image;
    },
    path: function(points, type) {
        const elem = new PathSvgElement(this, type);
        return elem.attr({
            points: points || []
        });
    },
    arc: function(x, y, innerRadius, outerRadius, startAngle, endAngle) {
        const elem = new ArcSvgElement(this);
        return elem.attr({
            x: x || 0,
            y: y || 0,
            innerRadius: innerRadius || 0,
            outerRadius: outerRadius || 0,
            startAngle: startAngle || 0,
            endAngle: endAngle || 0
        });
    },
    text: function(text, x, y) {
        const elem = new TextSvgElement(this);
        return elem.attr({
            text: text,
            x: x || 0,
            y: y || 0
        });
    },
    linearGradient: function(stops) {
        let id = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
        let rotationAngle = arguments.length > 2 ? arguments[2] : void 0;
        const gradient = this._createElement("linearGradient", {
            id: id,
            gradientTransform: `rotate(${rotationAngle || 0})`
        }).append(this._defs);
        gradient.id = id;
        this._createGradientStops(stops, gradient);
        return gradient;
    },
    radialGradient: function(stops, id) {
        const gradient = this._createElement("radialGradient", {
            id: id
        }).append(this._defs);
        this._createGradientStops(stops, gradient);
        return gradient;
    },
    _createGradientStops: function(stops, group) {
        stops.forEach((stop)=>{
            this._createElement("stop", {
                offset: stop.offset,
                "stop-color": stop["stop-color"] ?? stop.color,
                "stop-opacity": stop.opacity
            }).append(group);
        });
    },
    pattern: function(color, hatching, _id) {
        hatching = hatching || {};
        const step = hatching.step || 6;
        const stepTo2 = step / 2;
        const stepBy15 = 1.5 * step;
        const id = _id || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
        const d = "right" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(hatching.direction) ? "M " + stepTo2 + " " + -stepTo2 + " L " + -stepTo2 + " " + stepTo2 + " M 0 " + step + " L " + step + " 0 M " + stepBy15 + " " + stepTo2 + " L " + stepTo2 + " " + stepBy15 : "M 0 0 L " + step + " " + step + " M " + -stepTo2 + " " + stepTo2 + " L " + stepTo2 + " " + stepBy15 + " M " + stepTo2 + " " + -stepTo2 + " L " + stepBy15 + " " + stepTo2;
        const pattern = this._createElement("pattern", {
            id: id,
            width: step,
            height: step,
            patternUnits: "userSpaceOnUse"
        }).append(this._defs);
        pattern.id = id;
        this.rect(0, 0, step, step).attr({
            fill: color,
            opacity: hatching.opacity
        }).append(pattern);
        new PathSvgElement(this).attr({
            d: d,
            "stroke-width": hatching.width || 1,
            stroke: color
        }).append(pattern);
        return pattern;
    },
    customPattern: function(id, template, width, height) {
        const option = {
            id: id,
            width: width,
            height: height,
            patternContentUnits: "userSpaceOnUse",
            patternUnits: this._getPatternUnits(width, height)
        };
        const pattern = this._createElement("pattern", option).append(this._defs);
        template.render({
            container: pattern.element
        });
        return pattern;
    },
    _getPatternUnits: function(width, height) {
        if (Number(width) && Number(height)) {
            return "userSpaceOnUse";
        }
    },
    _getPointsWithYOffset: function(points, offset) {
        return points.map(function(point, index) {
            if (index % 2 !== 0) {
                return point + offset;
            }
            return point;
        });
    },
    clipShape: function(method, methodArgs) {
        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
        let clipPath = this._createElement("clipPath", {
            id: id
        }).append(this._defs);
        const shape = method.apply(this, methodArgs).append(clipPath);
        shape.id = id;
        shape.remove = function() {
            throw "Not implemented";
        };
        shape.dispose = function() {
            clipPath.dispose();
            clipPath = null;
            return this;
        };
        return shape;
    },
    clipRect (x, y, width, height) {
        return this.clipShape(this.rect, arguments);
    },
    clipCircle (x, y, radius) {
        return this.clipShape(this.circle, arguments);
    },
    shadowFilter: function(x, y, width, height, offsetX, offsetY, blur, color, opacity) {
        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
        const filter = this._createElement("filter", {
            id: id,
            x: x || 0,
            y: y || 0,
            width: width || 0,
            height: height || 0
        }).append(this._defs);
        const gaussianBlur = this._createElement("feGaussianBlur", {
            in: "SourceGraphic",
            result: "gaussianBlurResult",
            stdDeviation: blur || 0
        }).append(filter);
        const offset = this._createElement("feOffset", {
            in: "gaussianBlurResult",
            result: "offsetResult",
            dx: offsetX || 0,
            dy: offsetY || 0
        }).append(filter);
        const flood = this._createElement("feFlood", {
            result: "floodResult",
            "flood-color": color || "",
            "flood-opacity": opacity
        }).append(filter);
        const composite = this._createElement("feComposite", {
            in: "floodResult",
            in2: "offsetResult",
            operator: "in",
            result: "compositeResult"
        }).append(filter);
        const finalComposite = this._createElement("feComposite", {
            in: "SourceGraphic",
            in2: "compositeResult",
            operator: "over"
        }).append(filter);
        filter.id = id;
        filter.gaussianBlur = gaussianBlur;
        filter.offset = offset;
        filter.flood = flood;
        filter.composite = composite;
        filter.finalComposite = finalComposite;
        filter.attr = function(attrs) {
            const filterAttrs = {};
            const offsetAttrs = {};
            const floodAttrs = {};
            "x" in attrs && (filterAttrs.x = attrs.x);
            "y" in attrs && (filterAttrs.y = attrs.y);
            "width" in attrs && (filterAttrs.width = attrs.width);
            "height" in attrs && (filterAttrs.height = attrs.height);
            baseAttr(this, filterAttrs);
            "blur" in attrs && this.gaussianBlur.attr({
                stdDeviation: attrs.blur
            });
            "offsetX" in attrs && (offsetAttrs.dx = attrs.offsetX);
            "offsetY" in attrs && (offsetAttrs.dy = attrs.offsetY);
            this.offset.attr(offsetAttrs);
            "color" in attrs && (floodAttrs["flood-color"] = attrs.color);
            "opacity" in attrs && (floodAttrs["flood-opacity"] = attrs.opacity);
            this.flood.attr(floodAttrs);
            return this;
        };
        return filter;
    },
    brightFilter: function(type, slope) {
        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
        const filter = this._createElement("filter", {
            id: id
        }).append(this._defs);
        const componentTransferElement = this._createElement("feComponentTransfer").append(filter);
        const attrs = {
            type: type,
            slope: slope
        };
        filter.id = id;
        this._createElement("feFuncR", attrs).append(componentTransferElement);
        this._createElement("feFuncG", attrs).append(componentTransferElement);
        this._createElement("feFuncB", attrs).append(componentTransferElement);
        return filter;
    },
    getGrayScaleFilter: function() {
        if (this._grayScaleFilter) {
            return this._grayScaleFilter;
        }
        const id = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])();
        const filter = this._createElement("filter", {
            id: id
        }).append(this._defs);
        this._createElement("feColorMatrix").attr({
            type: "matrix",
            values: "0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 0.6 0"
        }).append(filter);
        filter.id = id;
        this._grayScaleFilter = filter;
        return filter;
    },
    lightenFilter: function(id) {
        const filter = this._createElement("filter", {
            id: id
        }).append(this._defs);
        this._createElement("feColorMatrix", {
            type: "matrix",
            values: "1.3 0 0 0 0 0 1.3 0 0 0 0 0 1.3 0 0 0 0 0 1 0"
        }).append(filter);
        filter.id = id;
        return filter;
    },
    initDefsElements: function() {
        const storage = this._defsElementsStorage = this._defsElementsStorage || {
            byHash: {},
            baseId: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNextDefsSvgId"])()
        };
        const byHash = storage.byHash;
        let name;
        for(name in byHash){
            byHash[name].pattern.dispose();
        }
        storage.byHash = {};
        storage.refToHash = {};
        storage.nextId = 0;
    },
    drawPattern: function(_ref4, storageId, nextId) {
        let { color: color, hatching: hatching } = _ref4;
        return this.pattern(color, hatching, `${storageId}-hatching-${nextId++}`);
    },
    drawFilter: function(_, storageId, nextId) {
        return this.lightenFilter(`${storageId}-lightening-${nextId++}`);
    },
    lockDefsElements: function(attrs, ref, type) {
        const storage = this._defsElementsStorage;
        let storageItem;
        const hash = "pattern" === type ? getHatchingHash(attrs) : LIGHTENING_HASH;
        const method = "pattern" === type ? this.drawPattern : this.drawFilter;
        let pattern;
        if (storage.refToHash[ref] !== hash) {
            if (ref) {
                this.releaseDefsElements(ref);
            }
            storageItem = storage.byHash[hash];
            if (!storageItem) {
                pattern = method.call(this, attrs, storage.baseId, storage.nextId++);
                storageItem = storage.byHash[hash] = {
                    pattern: pattern,
                    count: 0
                };
                storage.refToHash[pattern.id] = hash;
            }
            ++storageItem.count;
            ref = storageItem.pattern.id;
        }
        return ref;
    },
    releaseDefsElements: function(ref) {
        const storage = this._defsElementsStorage;
        const hash = storage.refToHash[ref];
        const storageItem = storage.byHash[hash];
        if (storageItem && 0 === --storageItem.count) {
            storageItem.pattern.dispose();
            delete storage.byHash[hash];
            delete storage.refToHash[ref];
        }
    }
};
function getHatchingHash(_ref5) {
    let { color: color, hatching: hatching } = _ref5;
    return "@" + color + "::" + hatching.step + ":" + hatching.width + ":" + hatching.opacity + ":" + hatching.direction;
}
const fixFuncIriCallbacks = function() {
    let callbacks = [];
    return {
        add: function(fn) {
            callbacks.push(fn);
        },
        remove: function(fn) {
            callbacks = callbacks.filter(function(el) {
                return el !== fn;
            });
        },
        removeByRenderer: function(renderer) {
            callbacks = callbacks.filter(function(el) {
                return el.renderer !== renderer;
            });
        },
        fire: function() {
            callbacks.forEach(function(fn) {
                fn();
            });
        }
    };
}();
const refreshPaths = function() {
    fixFuncIriCallbacks.fire();
};
}),
"[project]/node_modules/devextreme/esm/viz/core/plaque.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/plaque.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Plaque": ()=>Plaque
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/objectWithoutPropertiesLoose.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
;
;
const _excluded = [
    "x",
    "y",
    "canvas",
    "offsetX",
    "offsetY",
    "offset"
];
;
;
const math = Math;
const round = math.round;
const max = math.max;
const min = math.min;
const sin = math.sin;
const cos = math.cos;
const asin = math.asin;
const PI = math.PI;
const buildPath = function() {
    for(var _len = arguments.length, points = new Array(_len), _key = 0; _key < _len; _key++){
        points[_key] = arguments[_key];
    }
    return points.join("");
};
function getArc(cornerRadius, xDirection, yDirection) {
    return `a ${cornerRadius} ${cornerRadius} 0 0 1 ${xDirection * cornerRadius} ${yDirection * cornerRadius}`;
}
function getAbsoluteArc(cornerRadius, x, y) {
    return `A ${cornerRadius} ${cornerRadius} 0 0 1 ${x} ${y}`;
}
function rotateX(x, y, angle, x0, y0) {
    return (x - x0) * round(cos(angle)) + (y - y0) * round(sin(angle)) + x0;
}
function rotateY(x, y, angle, x0, y0) {
    return -(x - x0) * round(sin(angle)) + (y - y0) * round(cos(angle)) + y0;
}
function rotateSize(options, angle) {
    if (angle % 90 === 0 && angle % 180 !== 0) {
        return {
            width: options.height,
            height: options.width
        };
    }
    return options;
}
function getCloudAngle(_ref, x, y, anchorX, anchorY) {
    let { width: width, height: height } = _ref;
    const halfWidth = width / 2;
    const halfHeight = height / 2;
    const xr = Math.ceil(x + halfWidth);
    const xl = Math.floor(x - halfWidth);
    const yt = Math.floor(y - halfHeight);
    const yb = Math.ceil(y + halfHeight);
    if (anchorX < xl && anchorY < yt || anchorX >= xl && anchorX <= xr && anchorY < yt) {
        return 270;
    }
    if (anchorX > xr && anchorY > yb || anchorX >= xl && anchorX <= xr && anchorY > yb) {
        return 90;
    } else if (anchorX < xl && anchorY > yb || anchorX < xl && anchorY >= yt && anchorY <= yb) {
        return 180;
    }
    return 0;
}
function getCloudPoints(_ref2, x, y, anchorX, anchorY, _ref3, bounded) {
    let { width: width, height: height } = _ref2;
    let { arrowWidth: arrowWidth, cornerRadius: cornerRadius = 0 } = _ref3;
    const halfArrowWidth = arrowWidth / 2;
    const halfWidth = width / 2;
    const halfHeight = height / 2;
    const xr = Math.ceil(x + halfWidth);
    const xl = Math.floor(x - halfWidth);
    const yt = Math.floor(y - halfHeight);
    const yb = Math.ceil(y + halfHeight);
    const leftTopCorner = [
        xl,
        yt
    ];
    const rightTopCorner = [
        xr,
        yt
    ];
    const rightBottomCorner = [
        xr,
        yb
    ];
    const leftBottomCorner = [
        xl,
        yb
    ];
    const arrowX = anchorX <= xl ? xl : xr <= anchorX ? xr : anchorX;
    const arrowY = anchorY <= yt ? yt : yb <= anchorY ? yb : anchorY;
    const arrowBaseBottom = min(arrowY + halfArrowWidth, yb);
    const arrowBaseTop = max(arrowY - halfArrowWidth, yt);
    const arrowBaseLeft = max(arrowX - halfArrowWidth, xl);
    cornerRadius = Math.min(width / 2, height / 2, cornerRadius);
    let points;
    leftTopCorner[1] += cornerRadius;
    rightTopCorner[0] -= cornerRadius;
    rightBottomCorner[1] -= cornerRadius;
    leftBottomCorner[0] += cornerRadius;
    if (!bounded || xl <= anchorX && anchorX <= xr && yt <= anchorY && anchorY <= yb) {
        points = buildPath(leftTopCorner, getArc(cornerRadius, 1, -1), "L", rightTopCorner, getArc(cornerRadius, 1, 1), "L", rightBottomCorner, getArc(cornerRadius, -1, 1), "L", leftBottomCorner, getArc(cornerRadius, -1, -1));
    } else if (anchorX > xr && anchorY < yt) {
        const arrowAngle = arrowWidth / cornerRadius || 0;
        const angle = PI / 4 + arrowAngle / 2;
        const endAngle = PI / 4 - arrowAngle / 2;
        const arrowEndPointX = rightTopCorner[0] + cos(endAngle) * cornerRadius;
        const arrowEndPointY = rightTopCorner[1] + (1 - sin(endAngle)) * cornerRadius;
        let arrowArc = buildPath("L", rightTopCorner, getArc(cornerRadius, cos(angle), 1 - sin(angle)), "L", [
            anchorX,
            anchorY,
            arrowEndPointX,
            arrowEndPointY
        ], getAbsoluteArc(cornerRadius, rightTopCorner[0] + cornerRadius, rightTopCorner[1] + cornerRadius));
        if (Math.abs(angle) > PI / 2) {
            arrowArc = buildPath("L", [
                arrowBaseLeft,
                yt,
                anchorX,
                anchorY,
                xr,
                arrowBaseBottom
            ]);
        }
        points = buildPath(leftTopCorner, getArc(cornerRadius, 1, -1), arrowArc, "L", rightBottomCorner, getArc(cornerRadius, -1, 1), "L", leftBottomCorner, getArc(cornerRadius, -1, -1));
    } else if (anchorX > xr && anchorY >= yt && anchorY <= yb) {
        let arrowArc;
        if (arrowBaseTop >= rightTopCorner[1] + cornerRadius && arrowBaseBottom <= rightBottomCorner[1]) {
            arrowArc = buildPath(getArc(cornerRadius, 1, 1), "L", [
                xr,
                arrowBaseTop,
                anchorX,
                anchorY,
                xr,
                arrowBaseBottom
            ], "L", rightBottomCorner, getArc(cornerRadius, -1, 1));
        } else if (arrowBaseTop < rightTopCorner[1] + cornerRadius && arrowBaseBottom >= rightTopCorner[1] + cornerRadius && arrowBaseBottom <= rightBottomCorner[1]) {
            const arrowWidthRest = rightTopCorner[1] + cornerRadius - arrowBaseTop;
            const angle = arrowWidthRest / cornerRadius;
            const arrowBaseTopX = rightTopCorner[0] + cos(angle) * cornerRadius;
            const arrowBaseTopY = rightTopCorner[1] + (1 - sin(angle)) * cornerRadius;
            arrowArc = buildPath(getArc(cornerRadius, cos(angle), 1 - sin(angle)), "L", [
                arrowBaseTopX,
                arrowBaseTopY,
                anchorX,
                anchorY,
                xr,
                arrowBaseBottom
            ], "L", rightBottomCorner, getArc(cornerRadius, -1, 1));
        } else if (arrowBaseTop < rightTopCorner[1] + cornerRadius && arrowBaseBottom < rightTopCorner[1] + cornerRadius) {
            const arrowWidthRest = rightTopCorner[1] + cornerRadius - arrowBaseTop;
            const arrowAngle = arrowWidthRest / cornerRadius;
            const angle = arrowAngle;
            const arrowBaseTopX = rightTopCorner[0] + cos(angle) * cornerRadius;
            const arrowBaseTopY = rightTopCorner[1] + (1 - sin(angle)) * cornerRadius;
            const bottomAngle = Math.sin((rightTopCorner[1] + cornerRadius - arrowBaseBottom) / cornerRadius);
            const arrowBaseBottomX = rightTopCorner[0] + cornerRadius * cos(bottomAngle);
            const arrowBaseBottomY = rightTopCorner[1] + cornerRadius * (1 - sin(bottomAngle));
            arrowArc = buildPath(getArc(cornerRadius, cos(angle), 1 - sin(angle)), "L", [
                arrowBaseTopX,
                arrowBaseTopY,
                anchorX,
                anchorY,
                arrowBaseBottomX,
                arrowBaseBottomY
            ], getAbsoluteArc(cornerRadius, rightTopCorner[0] + cornerRadius, rightTopCorner[1] + cornerRadius), "L", rightBottomCorner, getArc(cornerRadius, -1, 1));
        } else if (arrowBaseTop <= rightTopCorner[1] + cornerRadius && arrowBaseBottom >= rightBottomCorner[1]) {
            const topAngle = asin((rightTopCorner[1] + cornerRadius - arrowBaseTop) / cornerRadius);
            const arrowBaseTopX = rightTopCorner[0] + cornerRadius * cos(topAngle);
            const arrowBaseTopY = rightTopCorner[1] + cornerRadius * (1 - sin(topAngle));
            const bottomAngle = asin((arrowBaseBottom - rightBottomCorner[1]) / cornerRadius);
            const arrowBaseBottomX = rightBottomCorner[0] + cornerRadius * (cos(bottomAngle) - 1);
            const arrowBaseBottomY = rightBottomCorner[1] + cornerRadius * sin(bottomAngle);
            arrowArc = buildPath(getArc(cornerRadius, cos(topAngle), 1 - sin(topAngle)), "L", [
                arrowBaseTopX,
                arrowBaseTopY,
                anchorX,
                anchorY,
                arrowBaseBottomX,
                arrowBaseBottomY
            ], getAbsoluteArc(cornerRadius, rightBottomCorner[0] - cornerRadius, rightBottomCorner[1] + cornerRadius));
        } else if (arrowBaseTop > rightTopCorner[1] + cornerRadius && arrowBaseTop <= rightBottomCorner[1] && arrowBaseBottom > rightBottomCorner[1]) {
            const bottomAngle = asin((arrowBaseBottom - rightBottomCorner[1]) / cornerRadius);
            const arrowBaseBottomX = rightBottomCorner[0] + cornerRadius * (cos(bottomAngle) - 1);
            const arrowBaseBottomY = rightBottomCorner[1] + cornerRadius * sin(bottomAngle);
            arrowArc = buildPath(getArc(cornerRadius, 1, 1), "L", [
                xr,
                arrowBaseTop,
                anchorX,
                anchorY,
                arrowBaseBottomX,
                arrowBaseBottomY
            ], getAbsoluteArc(cornerRadius, rightBottomCorner[0] - cornerRadius, rightBottomCorner[1] + cornerRadius));
        } else if (arrowBaseTop > rightTopCorner[1] + cornerRadius && arrowBaseBottom > rightBottomCorner[1]) {
            const bottomAngle = asin((arrowBaseBottom - rightBottomCorner[1]) / cornerRadius);
            const arrowBaseBottomX = rightBottomCorner[0] + cornerRadius * (cos(bottomAngle) - 1);
            const arrowBaseBottomY = rightBottomCorner[1] + cornerRadius * sin(bottomAngle);
            const topAngle = asin((arrowBaseTop - rightBottomCorner[1]) / cornerRadius);
            const arrowBaseTopX = rightBottomCorner[0] + cornerRadius * (cos(topAngle) - 1);
            const arrowBaseTopY = rightBottomCorner[1] + cornerRadius * sin(topAngle);
            arrowArc = buildPath(getArc(cornerRadius, 1, 1), "L", rightBottomCorner, getArc(cornerRadius, cos(topAngle) - 1, sin(topAngle)), "L", [
                arrowBaseTopX,
                arrowBaseTopY,
                anchorX,
                anchorY,
                arrowBaseBottomX,
                arrowBaseBottomY
            ], getAbsoluteArc(cornerRadius, rightBottomCorner[0] - cornerRadius, rightBottomCorner[1] + cornerRadius));
        }
        points = buildPath(leftTopCorner, getArc(cornerRadius, 1, -1), "L", rightTopCorner, arrowArc, "L", leftBottomCorner, getArc(cornerRadius, -1, -1));
    }
    return buildPath("M", points, "Z");
}
class Plaque {
    constructor(options, widget, root, contentTemplate){
        let bounded = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : true;
        let measureContent = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : (_, g)=>g.getBBox();
        let moveContentGroup = arguments.length > 6 && void 0 !== arguments[6] ? arguments[6] : (_, g, x, y)=>g.move(x, y);
        this.widget = widget;
        this.options = options;
        this.root = root;
        this.contentTemplate = contentTemplate;
        this.bonded = bounded;
        this.measureContent = measureContent;
        this.moveContentGroup = moveContentGroup;
    }
    draw(_ref4) {
        let { x: anchorX, y: anchorY, canvas: canvas = {}, offsetX: offsetX, offsetY: offsetY, offset: offset = 0 } = _ref4, restProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$objectWithoutPropertiesLoose$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(_ref4, _excluded);
        const options = this.options;
        let { x: x, y: y } = options;
        const bounds_xl = canvas.left, bounds_xr = canvas.width - canvas.right, bounds_width = canvas.width - canvas.right - canvas.left, bounds_yt = canvas.top, bounds_yb = canvas.height - canvas.bottom, bounds_height = canvas.height - canvas.bottom - canvas.top;
        if (!((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(anchorX) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(anchorY)) && !((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(x) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(y))) {
            return false;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(anchorX) && (anchorX < bounds_xl || bounds_xr < anchorX || anchorY < bounds_yt || bounds_yb < anchorY)) {
            return false;
        }
        if (!this._root) {
            this._draw();
        }
        const shadowSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({
            x: "-50%",
            y: "-50%",
            width: "200%",
            height: "200%"
        }, options.shadow);
        const contentWidth = options.width > 0 ? options.width : null;
        const contentHeight = options.height > 0 ? options.height : null;
        const onRender = ()=>{
            var _this$_root;
            const bBox = this._contentBBox = this.measureContent(this.widget, this._contentGroup);
            const size = this._size = {
                width: max(contentWidth, bBox.width) + 2 * options.paddingLeftRight,
                height: max(contentHeight, bBox.height) + 2 * options.paddingTopBottom,
                offset: offset
            };
            const xOff = shadowSettings.offsetX;
            const yOff = shadowSettings.offsetY;
            const blur = 2 * shadowSettings.blur + 1;
            const lm = max(blur - xOff, 0);
            const rm = max(blur + xOff, 0);
            const tm = max(blur - yOff, 0);
            const bm = max(blur + yOff, 0);
            this.margins = {
                lm: lm,
                rm: rm,
                tm: tm,
                bm: bm
            };
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(x)) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(offsetX)) {
                    x = anchorX + offsetX;
                } else if (bounds_width < size.width) {
                    x = round(bounds_xl + bounds_width / 2);
                } else {
                    x = min(max(anchorX, Math.ceil(bounds_xl + size.width / 2 + lm)), Math.floor(bounds_xr - size.width / 2 - rm));
                }
            } else {
                x += offsetX || 0;
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(anchorX)) {
                    anchorX = x;
                }
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(y)) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(offsetY)) {
                    y = anchorY + offsetY;
                } else {
                    const y_top = anchorY - options.arrowLength - size.height / 2 - offset;
                    const y_bottom = anchorY + options.arrowLength + size.height / 2 + offset;
                    if (bounds_height < size.height + options.arrowLength) {
                        y = round(bounds_yt + size.height / 2);
                    } else if (y_top - size.height / 2 - tm < bounds_yt) {
                        if (y_bottom + size.height / 2 + bm < bounds_yb) {
                            y = y_bottom;
                            anchorY += offset;
                        } else {
                            y = round(bounds_yt + size.height / 2);
                        }
                    } else {
                        y = y_top;
                        anchorY -= offset;
                    }
                }
            } else {
                y += offsetY || 0;
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(anchorY)) {
                    anchorY = y + size.height / 2;
                }
            }
            this.anchorX = anchorX;
            this.anchorY = anchorY;
            this.move(x, y);
            null === (_this$_root = this._root) || void 0 === _this$_root || _this$_root.append(this.root);
        };
        if (this.contentTemplate.render) {
            this.contentTemplate.render({
                model: options,
                container: this._contentGroup.element,
                onRendered: onRender
            });
        } else {
            this.contentTemplate((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
                group: this._contentGroup,
                onRender: onRender
            }, restProps));
        }
        return true;
    }
    _draw() {
        const renderer = this.widget._renderer;
        const options = this.options;
        const shadowSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({
            x: "-50%",
            y: "-50%",
            width: "200%",
            height: "200%"
        }, options.shadow);
        const shadow = this._shadow = renderer.shadowFilter().attr(shadowSettings);
        const cloudSettings = {
            opacity: options.opacity,
            "stroke-width": 0,
            fill: options.color
        };
        const borderOptions = options.border || {};
        if (borderOptions.visible) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(cloudSettings, {
                "stroke-width": borderOptions.width,
                stroke: borderOptions.color,
                "stroke-opacity": borderOptions.opacity,
                dashStyle: borderOptions.dashStyle
            });
        }
        const group = this._root = renderer.g().append(this.root);
        if (options.type) {
            group.attr({
                class: `dxc-${options.type}-annotation`
            });
        }
        const cloudGroup = renderer.g().attr({
            filter: shadow.id
        }).append(group);
        this._cloud = renderer.path([], "area").attr(cloudSettings).sharp().append(cloudGroup);
        this._contentGroup = renderer.g().append(group);
    }
    getBBox() {
        const size = this._size || {};
        const margins = this.margins || {};
        const rotationAngle = getCloudAngle(size, this.x, this.y, this.anchorX, this.anchorY);
        return {
            x: Math.floor(this.x - size.width / 2 - margins.lm),
            y: Math.floor(this.y - size.height / 2 - margins.tm - (270 === rotationAngle ? this.options.arrowLength : 0)),
            width: size.width + margins.lm + margins.rm,
            height: size.height + margins.tm + margins.bm + (90 === rotationAngle || 270 === rotationAngle ? this.options.arrowLength : 0)
        };
    }
    clear() {
        if (this._root) {
            this._root.remove();
            this._shadow.remove();
            this._root = null;
        }
        return this;
    }
    customizeCloud(attr) {
        if (this._cloud) {
            this._cloud.attr(attr);
        }
    }
    moveRoot(x, y) {
        if (this._root) {
            this._root.move(x, y);
        }
    }
    move(x, y) {
        x = round(x);
        y = round(y);
        this.x = x;
        this.y = y;
        const rotationAngle = getCloudAngle(this._size, x, y, this.anchorX, this.anchorY);
        const radRotationAngle = rotationAngle * PI / 180;
        this._cloud.attr({
            d: getCloudPoints(rotateSize(this._size, rotationAngle), x, y, rotateX(this.anchorX, this.anchorY, radRotationAngle, x, y), rotateY(this.anchorX, this.anchorY, radRotationAngle, x, y), this.options, this.bonded)
        }).rotate(rotationAngle, x, y);
        this.moveContentGroup(this.widget, this._contentGroup, x - this._contentBBox.x - this._contentBBox.width / 2, y - this._contentBBox.y - this._contentBBox.height / 2);
    }
    hitTest(x, y) {
        const { width: width, height: height } = this._size || {};
        return Math.abs(x - this.x) <= width / 2 && Math.abs(y - this.y) <= height / 2;
    }
}
}),
"[project]/node_modules/devextreme/esm/viz/core/tooltip.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/tooltip.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Tooltip": ()=>Tooltip,
    "plugin": ()=>plugin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/style.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dom.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/format_helper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$plaque$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/plaque.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
const format = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format;
const mathCeil = Math.ceil;
const mathMax = Math.max;
const mathMin = Math.min;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])();
const DEFAULT_HTML_GROUP_WIDTH = 3e3;
function hideElement($element) {
    $element.css({
        left: "-9999px"
    }).detach();
}
function getSpecialFormatOptions(options, specialFormat) {
    let result = options;
    switch(specialFormat){
        case "argument":
            result = {
                format: options.argumentFormat
            };
            break;
        case "percent":
            result = {
                format: {
                    type: "percent",
                    precision: options.format && options.format.percentPrecision
                }
            };
    }
    return result;
}
function createTextHtml() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("<div>").css({
        position: "relative",
        display: "inline-block",
        padding: 0,
        margin: 0,
        border: "0px solid transparent"
    });
}
function removeElements(elements) {
    elements.forEach((el)=>el.remove());
}
let Tooltip = function(params) {
    this._eventTrigger = params.eventTrigger;
    this._widgetRoot = params.widgetRoot;
    this._widget = params.widget;
    this._textHtmlContainers = [];
    this._wrapper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("<div>").css({
        position: "absolute",
        overflow: "hidden",
        pointerEvents: "none"
    }).addClass(params.cssClass);
    const renderer = this._renderer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Renderer"]({
        pathModified: params.pathModified,
        container: this._wrapper[0]
    });
    const root = renderer.root;
    root.attr({
        "pointer-events": "none"
    });
    this._text = renderer.text(void 0, 0, 0);
    this._textGroupHtml = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("<div>").css({
        position: "absolute",
        padding: 0,
        margin: 0,
        border: "0px solid transparent"
    }).appendTo(this._wrapper);
    this._textHtml = createTextHtml().appendTo(this._textGroupHtml);
};
Tooltip.prototype = {
    constructor: Tooltip,
    dispose: function() {
        this._wrapper.remove();
        this._renderer.dispose();
        this._options = this._widgetRoot = null;
    },
    _getContainer: function() {
        const options = this._options;
        let container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this._widgetRoot).closest(options.container);
        if (0 === container.length) {
            container = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(options.container);
        }
        return (container.length ? container : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("body")).get(0);
    },
    setTemplate (contentTemplate) {
        this._template = contentTemplate ? this._widget._getTemplate(contentTemplate) : null;
    },
    setOptions: function(options) {
        options = options || {};
        const that = this;
        that._options = options;
        that._textFontStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.font);
        that._textFontStyles.color = that._textFontStyles.fill;
        that._wrapper.css({
            zIndex: options.zIndex
        });
        that._customizeTooltip = options.customizeTooltip;
        const textGroupHtml = that._textGroupHtml;
        if (this.plaque) {
            this.plaque.clear();
        }
        this.setTemplate(options.contentTemplate);
        const pointerEvents = options.interactive ? "auto" : "none";
        if (options.interactive) {
            this._renderer.root.css({
                "-moz-user-select": "auto",
                "-webkit-user-select": "auto"
            });
        }
        this.plaque = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$plaque$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plaque"]({
            opacity: that._options.opacity,
            color: that._options.color,
            border: that._options.border,
            paddingLeftRight: that._options.paddingLeftRight,
            paddingTopBottom: that._options.paddingTopBottom,
            arrowLength: that._options.arrowLength,
            arrowWidth: 20,
            shadow: that._options.shadow,
            cornerRadius: that._options.cornerRadius
        }, that, that._renderer.root, (_ref)=>{
            let { group: group, onRender: onRender, eventData: eventData, isMoving: isMoving, templateCallback: templateCallback = ()=>{} } = _ref;
            const state = that._state;
            if (!isMoving) {
                const template = that._template;
                const useTemplate = template && !state.formatObject.skipTemplate;
                if (state.html || useTemplate) {
                    textGroupHtml.css({
                        color: state.textColor,
                        width: 3e3,
                        pointerEvents: pointerEvents
                    });
                    if (useTemplate) {
                        const htmlContainers = that._textHtmlContainers;
                        const containerToTemplateRender = createTextHtml().appendTo(that._textGroupHtml);
                        htmlContainers.push(containerToTemplateRender);
                        template.render({
                            model: state.formatObject,
                            container: containerToTemplateRender,
                            onRendered: ()=>{
                                removeElements(htmlContainers.splice(0, htmlContainers.length - 1));
                                that._textHtml = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["replaceWith"])(that._textHtml, containerToTemplateRender);
                                state.html = that._textHtml.html();
                                if (0 === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWidth"])(that._textHtml) && 0 === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getHeight"])(that._textHtml)) {
                                    this.plaque.clear();
                                    templateCallback(false);
                                    return;
                                }
                                onRender();
                                that._riseEvents(eventData);
                                that._moveWrapper();
                                that.plaque.customizeCloud({
                                    fill: state.color,
                                    stroke: state.borderColor,
                                    "pointer-events": pointerEvents
                                });
                                templateCallback(true);
                                that._textHtmlContainers = [];
                            }
                        });
                        return;
                    } else {
                        that._text.attr({
                            text: ""
                        });
                        that._textHtml.html(state.html);
                    }
                } else {
                    that._text.css({
                        fill: state.textColor
                    }).attr({
                        text: state.text,
                        class: options.cssClass,
                        "pointer-events": pointerEvents
                    }).append(group.attr({
                        align: options.textAlignment
                    }));
                }
                that._riseEvents(eventData);
                that.plaque.customizeCloud({
                    fill: state.color,
                    stroke: state.borderColor,
                    "pointer-events": pointerEvents
                });
            }
            onRender();
            that._moveWrapper();
            return true;
        }, true, (tooltip, g)=>{
            const state = tooltip._state;
            if (state.html) {
                let bBox = window.getComputedStyle(that._textHtml.get(0));
                bBox = {
                    x: 0,
                    y: 0,
                    width: mathCeil(parseFloat(bBox.width)),
                    height: mathCeil(parseFloat(bBox.height))
                };
                return bBox;
            }
            return g.getBBox();
        }, (tooltip, g, x, y)=>{
            const state = tooltip._state;
            if (state.html) {
                that._textGroupHtml.css({
                    left: x,
                    top: y
                });
            } else {
                g.move(x, y);
            }
        });
        return that;
    },
    _riseEvents: function(eventData) {
        this._eventData && this._eventTrigger("tooltipHidden", this._eventData);
        this._eventData = eventData;
        this._eventTrigger("tooltipShown", this._eventData);
    },
    setRendererOptions: function(options) {
        this._renderer.setOptions(options);
        this._textGroupHtml.css({
            direction: options.rtl ? "rtl" : "ltr"
        });
        return this;
    },
    update: function(options) {
        const that = this;
        that.setOptions(options);
        hideElement(that._wrapper);
        const normalizedCSS = {};
        for(const name in that._textFontStyles){
            const normalizedName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["camelize"])(name);
            normalizedCSS[normalizedName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeStyleProp"])(normalizedName, that._textFontStyles[name]);
        }
        that._textGroupHtml.css(normalizedCSS);
        that._text.css(that._textFontStyles);
        that._eventData = null;
        return that;
    },
    _prepare: function(formatObject, state) {
        let customizeTooltip = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : this._customizeTooltip;
        const options = this._options;
        let customize = {};
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(customizeTooltip)) {
            customize = customizeTooltip.call(formatObject, formatObject);
            customize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPlainObject"])(customize) ? customize : {};
            if ("text" in customize) {
                state.text = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(customize.text) ? String(customize.text) : "";
            }
            if ("html" in customize) {
                state.html = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(customize.html) ? String(customize.html) : "";
            }
        }
        if (!("text" in state) && !("html" in state)) {
            state.text = formatObject.valueText || formatObject.description || "";
        }
        state.color = customize.color || options.color;
        state.borderColor = customize.borderColor || (options.border || {}).color;
        state.textColor = customize.fontColor || (this._textFontStyles || {}).color;
        return !!state.text || !!state.html || !!this._template;
    },
    show: function(formatObject, params, eventData, customizeTooltip, templateCallback) {
        const that = this;
        if (that._options.forceEvents) {
            eventData.x = params.x;
            eventData.y = params.y - params.offset;
            that._riseEvents(eventData);
            return true;
        }
        const state = {
            formatObject: formatObject,
            eventData: eventData,
            templateCallback: templateCallback
        };
        if (!that._prepare(formatObject, state, customizeTooltip)) {
            return false;
        }
        that._state = state;
        that._wrapper.appendTo(that._getContainer());
        that._clear();
        const parameters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, that._options, {
            canvas: that._getCanvas()
        }, state, {
            x: params.x,
            y: params.y,
            offset: params.offset
        });
        return this.plaque.clear().draw(parameters);
    },
    isCursorOnTooltip: function(x, y) {
        if (this._options.interactive) {
            const box = this.plaque.getBBox();
            return x > box.x && x < box.x + box.width && y > box.y && y < box.y + box.height;
        }
        return false;
    },
    hide: function(isPointerOut) {
        const that = this;
        hideElement(that._wrapper);
        if (that._eventData) {
            that._eventTrigger("tooltipHidden", that._options.forceEvents ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({
                isPointerOut: isPointerOut
            }, that._eventData) : that._eventData);
            that._clear();
            that._eventData = null;
        }
    },
    _clear () {
        this._textHtml.empty();
    },
    move: function(x, y, offset) {
        this.plaque.draw({
            x: x,
            y: y,
            offset: offset,
            canvas: this._getCanvas(),
            isMoving: true
        });
    },
    _moveWrapper: function() {
        const that = this;
        const plaqueBBox = this.plaque.getBBox();
        that._renderer.resize(plaqueBBox.width, plaqueBBox.height);
        const offset = that._wrapper.css({
            left: 0,
            top: 0
        }).offset();
        const left = plaqueBBox.x;
        const top = plaqueBBox.y;
        that._wrapper.css({
            left: left - offset.left,
            top: top - offset.top
        });
        this.plaque.moveRoot(-left, -top);
        if (this._state.html) {
            that._textHtml.css({
                left: -left,
                top: -top
            });
            that._textGroupHtml.css({
                width: mathCeil((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWidth"])(that._textHtml))
            });
        }
    },
    formatValue: function(value, _specialFormat) {
        const options = _specialFormat ? getSpecialFormatOptions(this._options, _specialFormat) : this._options;
        return format(value, options.format);
    },
    getOptions () {
        return this._options;
    },
    getLocation: function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(this._options.location);
    },
    isEnabled: function() {
        return !!this._options.enabled || !!this._options.forceEvents;
    },
    isShared: function() {
        return !!this._options.shared;
    },
    _getCanvas: function() {
        const container = this._getContainer();
        const containerBox = container.getBoundingClientRect();
        const html = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDocumentElement();
        const document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDocument();
        let left = window.pageXOffset || html.scrollLeft || 0;
        let top = window.pageYOffset || html.scrollTop || 0;
        const box = {
            left: left,
            top: top,
            width: mathMax(html.clientWidth, document.body.clientWidth) + left,
            height: mathMax(document.body.scrollHeight, html.scrollHeight, document.body.offsetHeight, html.offsetHeight, document.body.clientHeight, html.clientHeight),
            right: 0,
            bottom: 0
        };
        if (container !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody()) {
            left = mathMax(box.left, box.left + containerBox.left);
            top = mathMax(box.top, box.top + containerBox.top);
            box.width = mathMin(containerBox.width, box.width) + left + box.left;
            box.height = mathMin(containerBox.height, box.height) + top + box.top;
            box.left = left;
            box.top = top;
        }
        return box;
    }
};
const plugin = {
    name: "tooltip",
    init: function() {
        this._initTooltip();
    },
    dispose: function() {
        this._disposeTooltip();
    },
    members: {
        _initTooltip: function() {
            this._tooltip = new Tooltip({
                cssClass: this._rootClassPrefix + "-tooltip",
                eventTrigger: this._eventTrigger,
                pathModified: this.option("pathModified"),
                widgetRoot: this.element(),
                widget: this
            });
        },
        _disposeTooltip: function() {
            this._tooltip.dispose();
            this._tooltip = null;
        },
        _setTooltipRendererOptions: function() {
            this._tooltip.setRendererOptions(this._getRendererOptions());
        },
        _setTooltipOptions: function() {
            this._tooltip.update(this._getOption("tooltip"));
        }
    },
    extenders: {
        _stopCurrentHandling () {
            this._tooltip && this._tooltip.hide();
        }
    },
    customize: function(constructor) {
        const proto = constructor.prototype;
        proto._eventsMap.onTooltipShown = {
            name: "tooltipShown"
        };
        proto._eventsMap.onTooltipHidden = {
            name: "tooltipHidden"
        };
        constructor.addChange({
            code: "TOOLTIP_RENDERER",
            handler: function() {
                this._setTooltipRendererOptions();
            },
            isThemeDependent: true,
            isOptionChange: true
        });
        constructor.addChange({
            code: "TOOLTIP",
            handler: function() {
                this._setTooltipOptions();
            },
            isThemeDependent: true,
            isOptionChange: true,
            option: "tooltip"
        });
    },
    fontFields: [
        "tooltip.font"
    ]
};
}),
"[project]/node_modules/devextreme/esm/viz/core/annotations.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/annotations.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "createAnnotations": ()=>createAnnotations,
    "plugins": ()=>plugins
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/tooltip.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$plaque$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/plaque.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/drag.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
const getDocument = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDocument;
const EVENT_NS = "annotations";
const DOT_EVENT_NS = "." + EVENT_NS;
const POINTER_ACTION = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])([
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].down,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].move
], EVENT_NS);
const POINTER_UP_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].up, EVENT_NS);
const DRAG_START_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["start"] + DOT_EVENT_NS;
const DRAG_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["move"] + DOT_EVENT_NS;
const DRAG_END_EVENT_NAME = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_drag$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["end"] + DOT_EVENT_NS;
function coreAnnotation(options, contentTemplate) {
    return {
        draw: function(widget, group) {
            const annotationGroup = widget._renderer.g().append(group).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.font));
            if (this.plaque) {
                this.plaque.clear();
            }
            this.plaque = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$plaque$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Plaque"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, options, {
                cornerRadius: (options.border || {}).cornerRadius
            }), widget, annotationGroup, contentTemplate, widget._isAnnotationBounded(options));
            this.plaque.draw(widget._getAnnotationCoords(this));
            if (options.allowDragging) {
                annotationGroup.on(DRAG_START_EVENT_NAME, {
                    immediate: true
                }, (e)=>{
                    this._dragOffsetX = this.plaque.x - e.pageX;
                    this._dragOffsetY = this.plaque.y - e.pageY;
                }).on(DRAG_EVENT_NAME, (e)=>{
                    this.plaque.move(e.pageX + this._dragOffsetX, e.pageY + this._dragOffsetY);
                }).on(DRAG_END_EVENT_NAME, (e)=>{
                    this.offsetX = (this.offsetX || 0) + e.offset.x;
                    this.offsetY = (this.offsetY || 0) + e.offset.y;
                });
            }
        },
        hitTest (x, y) {
            return this.plaque.hitTest(x, y);
        },
        showTooltip (tooltip, _ref) {
            let { x: x, y: y } = _ref;
            const that = this;
            const options = that.options;
            if (tooltip.annotation !== that) {
                tooltip.setTemplate(options.tooltipTemplate);
                const callback = (result)=>{
                    result && (tooltip.annotation = that);
                };
                callback(tooltip.show(options, {
                    x: x,
                    y: y
                }, {
                    target: options
                }, options.customizeTooltip, callback));
            } else if (!tooltip.isCursorOnTooltip(x, y)) {
                tooltip.move(x, y);
            }
        }
    };
}
function getTemplateFunction(options, widget) {
    let template;
    if ("text" === options.type) {
        template = function(item, groupElement) {
            const text = widget._renderer.text(item.text).attr({
                class: item.cssClass
            }).append({
                element: groupElement
            });
            if (item.width > 0 || item.height > 0) {
                text.setMaxSize(item.width, item.height, {
                    wordWrap: item.wordWrap,
                    textOverflow: item.textOverflow
                });
            }
        };
    } else if ("image" === options.type) {
        template = function(item, groupElement) {
            const { width: width, height: height, url: url, location: location } = item.image || {};
            const { width: outerWidth, height: outerHeight } = item;
            const imageWidth = outerWidth > 0 ? Math.min(width, outerWidth) : width;
            const imageHeight = outerHeight > 0 ? Math.min(height, outerHeight) : height;
            widget._renderer.image(0, 0, imageWidth, imageHeight, url, location || "center").append({
                element: groupElement
            });
        };
    } else if ("custom" === options.type) {
        template = options.template;
    }
    return template;
}
function getImageObject(image) {
    return "string" === typeof image ? {
        url: image
    } : image;
}
let createAnnotations = function(widget, items) {
    let commonAnnotationSettings = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
    let customizeAnnotation = arguments.length > 3 ? arguments[3] : void 0;
    let pullOptions = arguments.length > 4 ? arguments[4] : void 0;
    const commonImageOptions = getImageObject(commonAnnotationSettings.image);
    return items.reduce((arr, item)=>{
        const currentImageOptions = getImageObject(item.image);
        const customizedItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(customizeAnnotation) ? customizeAnnotation(item) : {};
        if (customizedItem) {
            customizedItem.image = getImageObject(customizedItem.image);
        }
        const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, commonAnnotationSettings, item, {
            image: commonImageOptions
        }, {
            image: currentImageOptions
        }, customizedItem);
        const templateFunction = getTemplateFunction(options, widget);
        const annotation = templateFunction && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, pullOptions(options), coreAnnotation(options, widget._getTemplate(templateFunction)));
        annotation && arr.push(annotation);
        return arr;
    }, []);
};
const chartPlugin = {
    name: "annotations_chart",
    init () {},
    dispose () {},
    members: {
        _getAnnotationCoords (annotation) {
            var _axis, _axis2;
            const coords = {
                offsetX: annotation.offsetX,
                offsetY: annotation.offsetY
            };
            const argCoordName = this._options.silent("rotated") ? "y" : "x";
            const valCoordName = this._options.silent("rotated") ? "x" : "y";
            const argAxis = this.getArgumentAxis();
            const argument = argAxis.validateUnit(annotation.argument);
            let axis = this.getValueAxis(annotation.axis);
            let series;
            let pane = null === (_axis = axis) || void 0 === _axis ? void 0 : _axis.pane;
            if (annotation.series) {
                var _series;
                series = this.series.filter((s)=>s.name === annotation.series)[0];
                axis = null === (_series = series) || void 0 === _series ? void 0 : _series.getValueAxis();
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(axis) && (pane = axis.pane);
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(argument)) {
                if (series) {
                    const center = series.getPointCenterByArg(argument);
                    center && (coords[argCoordName] = center[argCoordName]);
                } else {
                    coords[argCoordName] = argAxis.getTranslator().translate(argument);
                }
                !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(pane) && (pane = argAxis.pane);
            }
            const value = null === (_axis2 = axis) || void 0 === _axis2 ? void 0 : _axis2.validateUnit(annotation.value);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value)) {
                var _axis3;
                coords[valCoordName] = null === (_axis3 = axis) || void 0 === _axis3 ? void 0 : _axis3.getTranslator().translate(value);
                !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(pane) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(axis) && (pane = axis.pane);
            }
            coords.canvas = this._getCanvasForPane(pane);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(coords[argCoordName]) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value)) {
                var _series2;
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(axis) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(series)) {
                    coords[valCoordName] = argAxis.getAxisPosition();
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(axis) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(series)) {
                    coords[valCoordName] = this._argumentAxes.filter((a)=>a.pane === axis.pane)[0].getAxisPosition();
                } else if (null !== (_series2 = series) && void 0 !== _series2 && _series2.checkSeriesViewportCoord(argAxis, coords[argCoordName])) {
                    coords[valCoordName] = series.getSeriesPairCoord(coords[argCoordName], true);
                }
            }
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(argument) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(coords[valCoordName])) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(axis) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(series)) {
                    coords[argCoordName] = axis.getAxisPosition();
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(series)) {
                    if (series.checkSeriesViewportCoord(axis, coords[valCoordName])) {
                        coords[argCoordName] = series.getSeriesPairCoord(coords[valCoordName], false);
                    }
                }
            }
            return coords;
        },
        _annotationsPointerEventHandler (event) {
            if (this._disposed) {
                return;
            }
            const originalEvent = event.originalEvent || {};
            const touch = originalEvent.touches && originalEvent.touches[0] || {};
            const rootOffset = this._renderer.getRootOffset();
            const coords = {
                x: touch.pageX || originalEvent.pageX || event.pageX,
                y: touch.pageY || originalEvent.pageY || event.pageY
            };
            const annotation = this._annotations.items.filter((a)=>a.hitTest(coords.x - rootOffset.left, coords.y - rootOffset.top))[0];
            if (!annotation || !annotation.options.tooltipEnabled) {
                this._annotations.hideTooltip();
                return;
            }
            this._clear();
            if (annotation.options.allowDragging && event.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].down) {
                this._annotations._hideToolTipForDrag = true;
            }
            if (!this._annotations._hideToolTipForDrag) {
                annotation.showTooltip(this._annotations.tooltip, coords);
                event.stopPropagation();
            }
        },
        _isAnnotationBounded: (options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.argument),
        _pullOptions: (options)=>({
                type: options.type,
                name: options.name,
                x: options.x,
                y: options.y,
                value: options.value,
                argument: options.argument,
                axis: options.axis,
                series: options.series,
                options: options,
                offsetX: options.offsetX,
                offsetY: options.offsetY
            }),
        _forceAnnotationRender () {
            this._change([
                "FORCE_RENDER"
            ]);
        },
        _clear () {
            this.hideTooltip();
            this.clearHover();
        }
    }
};
const polarChartPlugin = {
    name: "annotations_polar_chart",
    init () {},
    dispose () {},
    members: {
        _getAnnotationCoords (annotation) {
            const coords = {
                offsetX: annotation.offsetX,
                offsetY: annotation.offsetY,
                canvas: this._calcCanvas()
            };
            const argAxis = this.getArgumentAxis();
            let argument = argAxis.validateUnit(annotation.argument);
            const value = this.getValueAxis().validateUnit(annotation.value);
            const radius = annotation.radius;
            const angle = annotation.angle;
            let pointCoords;
            let series;
            if (annotation.series) {
                series = this.series.filter((s)=>s.name === annotation.series)[0];
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, coords, this.getXYFromPolar(angle, radius, argument, value));
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(series)) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(coords.angle) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(radius)) {
                    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(argument)) {
                        argument = argAxis.getTranslator().from(isFinite(angle) ? this.getActualAngle(angle) : coords.angle);
                    }
                    pointCoords = series.getSeriesPairCoord({
                        argument: argument,
                        angle: -coords.angle
                    }, true);
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(coords.radius) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(argument) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(angle)) {
                    pointCoords = series.getSeriesPairCoord({
                        radius: coords.radius
                    }, false);
                }
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(pointCoords)) {
                    coords.x = pointCoords.x;
                    coords.y = pointCoords.y;
                }
            }
            if (annotation.series && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(pointCoords)) {
                coords.x = coords.y = void 0;
            }
            return coords;
        },
        _annotationsPointerEventHandler: chartPlugin.members._annotationsPointerEventHandler,
        _isAnnotationBounded: chartPlugin.members._isAnnotationBounded,
        _pullOptions (options) {
            const polarOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, {
                radius: options.radius,
                angle: options.angle
            }, chartPlugin.members._pullOptions(options));
            delete polarOptions.axis;
            return polarOptions;
        },
        _forceAnnotationRender: chartPlugin.members._forceAnnotationRender,
        _clear: chartPlugin.members._clear
    }
};
const vectorMapPlugin = {
    name: "annotations_vector_map",
    init () {},
    dispose () {
        this._annotations._offTracker();
        this._annotations._offTracker = null;
    },
    members: {
        _getAnnotationCoords (annotation) {
            const coords = {
                offsetX: annotation.offsetX,
                offsetY: annotation.offsetY
            };
            coords.canvas = this._projection.getCanvas();
            if (annotation.coordinates) {
                const data = this._projection.toScreenPoint(annotation.coordinates);
                coords.x = data[0];
                coords.y = data[1];
            }
            return coords;
        },
        _annotationsPointerEventHandler: chartPlugin.members._annotationsPointerEventHandler,
        _isAnnotationBounded: (options)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.coordinates),
        _pullOptions (options) {
            const vectorMapOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, {
                coordinates: options.coordinates
            }, chartPlugin.members._pullOptions(options));
            delete vectorMapOptions.axis;
            delete vectorMapOptions.series;
            delete vectorMapOptions.argument;
            delete vectorMapOptions.value;
            return vectorMapOptions;
        },
        _forceAnnotationRender () {
            this._change([
                "EXTRA_ELEMENTS"
            ]);
        },
        _getAnnotationStyles: ()=>({
                "text-anchor": "start"
            }),
        _clear () {}
    },
    extenders: {
        _prepareExtraElements () {
            const that = this;
            const renderElements = ()=>{
                that._renderExtraElements();
            };
            that._annotations._offTracker = that._tracker.on({
                move: renderElements,
                zoom: renderElements,
                end: renderElements
            });
        }
    }
};
const pieChartPlugin = {
    name: "annotations_pie_chart",
    init () {},
    dispose () {},
    members: {
        _getAnnotationCoords (annotation) {
            let series;
            const coords = {
                offsetX: annotation.offsetX,
                offsetY: annotation.offsetY,
                canvas: this._canvas
            };
            if (annotation.argument) {
                if (annotation.series) {
                    series = this.getSeriesByName(annotation.series);
                } else {
                    series = this.series[0];
                }
                const argument = series.getPointsByArg(annotation.argument)[0];
                const { x: x, y: y } = argument.getAnnotationCoords(annotation.location);
                coords.x = x;
                coords.y = y;
            }
            return coords;
        },
        _isAnnotationBounded: (options)=>options.argument,
        _annotationsPointerEventHandler: chartPlugin.members._annotationsPointerEventHandler,
        _pullOptions (options) {
            const pieChartOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, {
                location: options.location
            }, chartPlugin.members._pullOptions(options));
            delete pieChartOptions.axis;
            return pieChartOptions;
        },
        _clear: chartPlugin.members._clear,
        _forceAnnotationRender: chartPlugin.members._forceAnnotationRender
    }
};
const corePlugin = {
    name: "annotations_core",
    init () {
        this._annotations = {
            items: [],
            _hideToolTipForDrag: false,
            tooltip: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$tooltip$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Tooltip"]({
                cssClass: `${this._rootClassPrefix}-annotation-tooltip`,
                eventTrigger: this._eventTrigger,
                widgetRoot: this.element(),
                widget: this
            }),
            hideTooltip () {
                this.tooltip.annotation = null;
                this.tooltip.hide();
            },
            clearItems () {
                this.items.forEach((i)=>i.plaque.clear());
                this.items = [];
            }
        };
        this._annotations.tooltip.setRendererOptions(this._getRendererOptions());
    },
    dispose () {
        this._annotationsGroup.linkRemove().linkOff();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].off(getDocument(), DOT_EVENT_NS);
        this._annotationsGroup.off(DOT_EVENT_NS);
        this._annotations.tooltip && this._annotations.tooltip.dispose();
    },
    extenders: {
        _createHtmlStructure () {
            this._annotationsGroup = this._renderer.g().attr({
                class: `${this._rootClassPrefix}-annotations`
            }).css(this._getAnnotationStyles()).linkOn(this._renderer.root, "annotations").linkAppend();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on(getDocument(), POINTER_ACTION, (e)=>{
                if (this._disposed) {
                    return;
                }
                if (!this._annotations.tooltip.isCursorOnTooltip(e.pageX, e.pageY)) {
                    this._annotations.hideTooltip();
                }
            });
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].on(getDocument(), POINTER_UP_EVENT_NAME, (event)=>{
                this._annotations._hideToolTipForDrag = false;
                this._annotationsPointerEventHandler(event);
            });
            this._annotationsGroup.on(POINTER_ACTION, this._annotationsPointerEventHandler.bind(this));
        },
        _renderExtraElements () {
            this._annotationsGroup.clear();
            this._annotations.items.forEach((item)=>item.draw(this, this._annotationsGroup));
        },
        _stopCurrentHandling () {
            this._annotations.hideTooltip();
        }
    },
    members: {
        _buildAnnotations () {
            this._annotations.clearItems();
            const items = this._getOption("annotations", true);
            if (!(null !== items && void 0 !== items && items.length)) {
                return;
            }
            this._annotations.items = createAnnotations(this, items, this._getOption("commonAnnotationSettings"), this._getOption("customizeAnnotation", true), this._pullOptions);
        },
        _setAnnotationTooltipOptions () {
            const tooltipOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, this._getOption("tooltip"));
            tooltipOptions.contentTemplate = tooltipOptions.customizeTooltip = void 0;
            this._annotations.tooltip.update(tooltipOptions);
        },
        _getAnnotationCoords: ()=>({}),
        _pullOptions: ()=>({}),
        _getAnnotationStyles: ()=>({})
    },
    customize (constructor) {
        constructor.addChange({
            code: "ANNOTATIONITEMS",
            handler () {
                this._requestChange([
                    "ANNOTATIONS"
                ]);
            },
            isOptionChange: true,
            option: "annotations"
        });
        constructor.addChange({
            code: "ANNOTATIONSSETTINGS",
            handler () {
                this._requestChange([
                    "ANNOTATIONS"
                ]);
            },
            isOptionChange: true,
            option: "commonAnnotationSettings"
        });
        constructor.addChange({
            code: "ANNOTATIONS",
            handler () {
                this._buildAnnotations();
                this._setAnnotationTooltipOptions();
                this._forceAnnotationRender();
            },
            isThemeDependent: true,
            isOptionChange: true
        });
    },
    fontFields: [
        "commonAnnotationSettings.font"
    ]
};
const plugins = {
    core: corePlugin,
    chart: chartPlugin,
    polarChart: polarChartPlugin,
    vectorMap: vectorMapPlugin,
    pieChart: pieChartPlugin
};
}),
"[project]/node_modules/devextreme/esm/viz/core/series_family.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/series_family.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "SeriesFamily": ()=>SeriesFamily
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const { round: round, abs: abs, pow: pow, sqrt: sqrt } = Math;
const _min = Math.min;
const DEFAULT_BAR_GROUP_PADDING = .3;
function validateBarPadding(barPadding) {
    return barPadding < 0 || barPadding > 1 ? void 0 : barPadding;
}
function validateBarGroupPadding(barGroupPadding) {
    return barGroupPadding < 0 || barGroupPadding > 1 ? .3 : barGroupPadding;
}
function isStackExist(series, arg) {
    return series.some(function(s) {
        return !s.getOptions().ignoreEmptyPoints || s.getPointsByArg(arg, true).some(function(point) {
            return point.hasValue();
        });
    });
}
function correctStackCoordinates(series, currentStacks, arg, stack, parameters, barsArea, seriesStackIndexCallback) {
    series.forEach(function(series) {
        const stackIndex = seriesStackIndexCallback(currentStacks.indexOf(stack), currentStacks.length);
        const points = series.getPointsByArg(arg, true);
        const barPadding = validateBarPadding(series.getOptions().barPadding);
        const barWidth = series.getOptions().barWidth;
        let offset = getOffset(stackIndex, parameters);
        let width = parameters.width;
        let extraParameters;
        if (-1 === stackIndex) {
            return;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(barPadding) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(barWidth)) {
            extraParameters = calculateParams(barsArea, currentStacks.length, 1 - barPadding, barWidth);
            width = extraParameters.width;
            if (!series.getBarOverlapGroup()) {
                offset = getOffset(stackIndex, extraParameters);
            }
        }
        correctPointCoordinates(points, width, offset);
    });
}
function getStackName(series) {
    return series.getStackName() || series.getBarOverlapGroup();
}
function adjustBarSeriesDimensionsCore(series, options, seriesStackIndexCallback) {
    var _series$, _series$2;
    const commonStacks = [];
    const allArguments = [];
    const seriesInStacks = {};
    const barGroupWidth = options.barGroupWidth;
    const argumentAxis = null === (_series$ = series[0]) || void 0 === _series$ ? void 0 : _series$.getArgumentAxis();
    let interval;
    if (null !== (_series$2 = series[0]) && void 0 !== _series$2 && _series$2.useAggregation()) {
        var _series$3;
        const isDateArgAxis = "datetime" === (null === (_series$3 = series[0]) || void 0 === _series$3 ? void 0 : _series$3.argumentType);
        let tickInterval = argumentAxis.getTickInterval();
        let aggregationInterval = argumentAxis.getAggregationInterval();
        tickInterval = isDateArgAxis ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds(tickInterval) : tickInterval;
        aggregationInterval = isDateArgAxis ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds(aggregationInterval) : aggregationInterval;
        interval = aggregationInterval < tickInterval ? aggregationInterval : tickInterval;
    }
    interval = null === argumentAxis || void 0 === argumentAxis ? void 0 : argumentAxis.getTranslator().getInterval(interval);
    const barsArea = barGroupWidth ? interval > barGroupWidth ? barGroupWidth : interval : interval * (1 - validateBarGroupPadding(options.barGroupPadding));
    series.forEach(function(s, i) {
        const stackName = getStackName(s) || i.toString();
        let argument;
        for(argument in s.pointsByArgument){
            if (-1 === allArguments.indexOf(argument.valueOf())) {
                allArguments.push(argument.valueOf());
            }
        }
        if (-1 === commonStacks.indexOf(stackName)) {
            commonStacks.push(stackName);
            seriesInStacks[stackName] = [];
        }
        seriesInStacks[stackName].push(s);
    });
    allArguments.forEach(function(arg) {
        const currentStacks = commonStacks.reduce((stacks, stack)=>{
            if (isStackExist(seriesInStacks[stack], arg)) {
                stacks.push(stack);
            }
            return stacks;
        }, []);
        const parameters = calculateParams(barsArea, currentStacks.length);
        commonStacks.forEach((stack)=>{
            correctStackCoordinates(seriesInStacks[stack], currentStacks, arg, stack, parameters, barsArea, seriesStackIndexCallback);
        });
    });
}
function calculateParams(barsArea, count, percentWidth, fixedBarWidth) {
    let spacing;
    let width;
    if (fixedBarWidth) {
        width = _min(fixedBarWidth, barsArea / count);
        spacing = count > 1 ? round((barsArea - round(width) * count) / (count - 1)) : 0;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(percentWidth)) {
        width = barsArea * percentWidth / count;
        spacing = count > 1 ? round((barsArea - barsArea * percentWidth) / (count - 1)) : 0;
    } else {
        spacing = round(barsArea / count * .2);
        width = (barsArea - spacing * (count - 1)) / count;
    }
    return {
        width: width > 1 ? round(width) : 1,
        spacing: spacing,
        middleIndex: count / 2,
        rawWidth: width
    };
}
function getOffset(stackIndex, parameters) {
    const width = parameters.rawWidth < 1 ? parameters.rawWidth : parameters.width;
    return (stackIndex - parameters.middleIndex + .5) * width - (parameters.middleIndex - stackIndex - .5) * parameters.spacing;
}
function correctPointCoordinates(points, width, offset) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(points, function(_, point) {
        point.correctCoordinates({
            width: width,
            offset: offset
        });
    });
}
function getValueType(value) {
    return value >= 0 ? "positive" : "negative";
}
function getVisibleSeries(that) {
    return that.series.filter(function(s) {
        return s.isVisible();
    });
}
function getAbsStackSumByArg(stackKeepers, stackName, argument) {
    const positiveStackValue = (stackKeepers.positive[stackName] || {})[argument] || 0;
    const negativeStackValue = -(stackKeepers.negative[stackName] || {})[argument] || 0;
    return positiveStackValue + negativeStackValue;
}
function getStackSumByArg(stackKeepers, stackName, argument) {
    const positiveStackValue = (stackKeepers.positive[stackName] || {})[argument] || 0;
    const negativeStackValue = (stackKeepers.negative[stackName] || {})[argument] || 0;
    return positiveStackValue + negativeStackValue;
}
function getSeriesStackIndexCallback(inverted) {
    if (!inverted) {
        return function(index) {
            return index;
        };
    } else {
        return function(index, stackCount) {
            return stackCount - index - 1;
        };
    }
}
function isInverted(series) {
    return series[0] && series[0].getArgumentAxis().getTranslator().isInverted();
}
function adjustBarSeriesDimensions() {
    const series = getVisibleSeries(this);
    adjustBarSeriesDimensionsCore(series, this._options, getSeriesStackIndexCallback(isInverted(series)));
}
function getFirstValueSign(series) {
    const points = series.getPoints();
    let value;
    for(let i = 0; i < points.length; i++){
        const point = points[i];
        value = point.initialValue && point.initialValue.valueOf();
        if (abs(value) > 0) {
            break;
        }
    }
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sign"])(value);
}
function adjustStackedSeriesValues() {
    const negativesAsZeroes = this._options.negativesAsZeroes;
    const series = getVisibleSeries(this);
    const stackKeepers = {
        positive: {},
        negative: {}
    };
    const holesStack = {
        left: {},
        right: {}
    };
    const lastSeriesInPositiveStack = {};
    const lastSeriesInNegativeStack = {};
    series.forEach(function(singleSeries) {
        const stackName = getStackName(singleSeries);
        let hole = false;
        const stack = getFirstValueSign(singleSeries) < 0 ? lastSeriesInNegativeStack : lastSeriesInPositiveStack;
        singleSeries._prevSeries = stack[stackName];
        stack[stackName] = singleSeries;
        singleSeries.holes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, holesStack);
        singleSeries.getPoints().forEach(function(point, index, points) {
            let value = point.initialValue && point.initialValue.valueOf();
            let argument = point.argument.valueOf();
            let stacks = value >= 0 ? stackKeepers.positive : stackKeepers.negative;
            const isNotBarSeries = "bar" !== singleSeries.type;
            if (negativesAsZeroes && value < 0) {
                stacks = stackKeepers.positive;
                value = 0;
                point.resetValue();
            }
            stacks[stackName] = stacks[stackName] || {};
            const currentStack = stacks[stackName];
            if (currentStack[argument]) {
                if (isNotBarSeries) {
                    point.correctValue(currentStack[argument]);
                }
                currentStack[argument] += value;
            } else {
                currentStack[argument] = value;
                if (isNotBarSeries) {
                    point.resetCorrection();
                }
            }
            if (!point.hasValue()) {
                const prevPoint = points[index - 1];
                if (!hole && prevPoint && prevPoint.hasValue()) {
                    argument = prevPoint.argument.valueOf();
                    prevPoint._skipSetRightHole = true;
                    holesStack.right[argument] = (holesStack.right[argument] || 0) + (prevPoint.value.valueOf() - (isFinite(prevPoint.minValue) ? prevPoint.minValue.valueOf() : 0));
                }
                hole = true;
            } else if (hole) {
                hole = false;
                holesStack.left[argument] = (holesStack.left[argument] || 0) + (point.value.valueOf() - (isFinite(point.minValue) ? point.minValue.valueOf() : 0));
                point._skipSetLeftHole = true;
            }
        });
    });
    series.forEach(function(singleSeries) {
        const holes = singleSeries.holes;
        singleSeries.getPoints().forEach(function(point) {
            const argument = point.argument.valueOf();
            point.resetHoles();
            !point._skipSetLeftHole && point.setHole(holes.left[argument] || holesStack.left[argument] && 0, "left");
            !point._skipSetRightHole && point.setHole(holes.right[argument] || holesStack.right[argument] && 0, "right");
            point._skipSetLeftHole = null;
            point._skipSetRightHole = null;
        });
    });
    this._stackKeepers = stackKeepers;
    series.forEach(function(singleSeries) {
        singleSeries.getPoints().forEach(function(point) {
            const argument = point.argument.valueOf();
            const stackName = getStackName(singleSeries);
            const absTotal = getAbsStackSumByArg(stackKeepers, stackName, argument);
            const total = getStackSumByArg(stackKeepers, stackName, argument);
            point.setPercentValue(absTotal, total, holesStack.left[argument], holesStack.right[argument]);
        });
    });
}
function updateStackedSeriesValues() {
    const that = this;
    const series = getVisibleSeries(that);
    const stack = that._stackKeepers;
    const stackKeepers = {
        positive: {},
        negative: {}
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(series, function(_, singleSeries) {
        const minBarSize = singleSeries.getOptions().minBarSize;
        const valueAxisTranslator = singleSeries.getValueAxis().getTranslator();
        const minShownBusinessValue = minBarSize && valueAxisTranslator.getMinBarSize(minBarSize);
        const stackName = singleSeries.getStackName();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(singleSeries.getPoints(), function(index, point) {
            if (!point.hasValue()) {
                return;
            }
            let value = point.initialValue && point.initialValue.valueOf();
            const argument = point.argument.valueOf();
            if (that.fullStacked) {
                value = value / getAbsStackSumByArg(stack, stackName, argument) || 0;
            }
            const updateValue = valueAxisTranslator.checkMinBarSize(value, minShownBusinessValue, point.value);
            const valueType = getValueType(updateValue);
            const currentStack = stackKeepers[valueType][stackName] = stackKeepers[valueType][stackName] || {};
            if (currentStack[argument]) {
                point.minValue = currentStack[argument];
                currentStack[argument] += updateValue;
            } else {
                currentStack[argument] = updateValue;
            }
            point.value = currentStack[argument];
        });
    });
    if (that.fullStacked) {
        updateFullStackedSeriesValues(series, stackKeepers);
    }
}
function updateFullStackedSeriesValues(series, stackKeepers) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(series, function(_, singleSeries) {
        const stackName = singleSeries.getStackName ? singleSeries.getStackName() : "default";
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(singleSeries.getPoints(), function(index, point) {
            const stackSum = getAbsStackSumByArg(stackKeepers, stackName, point.argument.valueOf());
            if (0 !== stackSum) {
                point.value = point.value / stackSum;
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(point.minValue)) {
                    point.minValue = point.minValue / stackSum;
                }
            }
        });
    });
}
function updateRangeSeriesValues() {
    const series = getVisibleSeries(this);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(series, function(_, singleSeries) {
        const minBarSize = singleSeries.getOptions().minBarSize;
        const valueAxisTranslator = singleSeries.getValueAxis().getTranslator();
        const minShownBusinessValue = minBarSize && valueAxisTranslator.getMinBarSize(minBarSize);
        if (minShownBusinessValue) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(singleSeries.getPoints(), function(_, point) {
                if (!point.hasValue()) {
                    return;
                }
                if (point.value.valueOf() - point.minValue.valueOf() < minShownBusinessValue) {
                    point.value = valueAxisTranslator.toValue(point.value.valueOf() + minShownBusinessValue / 2);
                    point.minValue = valueAxisTranslator.toValue(point.minValue.valueOf() - minShownBusinessValue / 2);
                }
            });
        }
    });
}
function updateBarSeriesValues() {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(this.series, function(_, singleSeries) {
        const minBarSize = singleSeries.getOptions().minBarSize;
        const valueAxisTranslator = singleSeries.getValueAxis().getTranslator();
        const minShownBusinessValue = minBarSize && valueAxisTranslator.getMinBarSize(minBarSize);
        if (minShownBusinessValue) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(singleSeries.getPoints(), function(index, point) {
                if (point.hasValue()) {
                    point.value = valueAxisTranslator.checkMinBarSize(point.initialValue, minShownBusinessValue);
                }
            });
        }
    });
}
function adjustCandlestickSeriesDimensions() {
    const series = getVisibleSeries(this);
    adjustBarSeriesDimensionsCore(series, {
        barGroupPadding: .3
    }, getSeriesStackIndexCallback(isInverted(series)));
}
function adjustBubbleSeriesDimensions() {
    const series = getVisibleSeries(this);
    if (!series.length) {
        return;
    }
    const options = this._options;
    const visibleAreaX = series[0].getArgumentAxis().getVisibleArea();
    const visibleAreaY = series[0].getValueAxis().getVisibleArea();
    const min = _min(visibleAreaX[1] - visibleAreaX[0], visibleAreaY[1] - visibleAreaY[0]);
    const minBubbleArea = pow(options.minBubbleSize, 2);
    const maxBubbleArea = pow(min * options.maxBubbleSize, 2);
    const equalBubbleSize = (min * options.maxBubbleSize + options.minBubbleSize) / 2;
    let minPointSize = 1 / 0;
    let maxPointSize = -1 / 0;
    let pointSize;
    let bubbleArea;
    let sizeProportion;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(series, function(_, seriesItem) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(seriesItem.getPoints(), function(_, point) {
            maxPointSize = maxPointSize > point.size ? maxPointSize : point.size;
            minPointSize = minPointSize < point.size ? minPointSize : point.size;
        });
    });
    const sizeDispersion = maxPointSize - minPointSize;
    const areaDispersion = abs(maxBubbleArea - minBubbleArea);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(series, function(_, seriesItem) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(seriesItem.getPoints(), function(_, point) {
            if (maxPointSize === minPointSize) {
                pointSize = round(equalBubbleSize);
            } else {
                sizeProportion = abs(point.size - minPointSize) / sizeDispersion;
                bubbleArea = areaDispersion * sizeProportion + minBubbleArea;
                pointSize = round(sqrt(bubbleArea));
            }
            point.correctCoordinates(pointSize);
        });
    });
}
function SeriesFamily(options) {
    const that = this;
    that.type = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(options.type);
    that.pane = options.pane;
    that.series = [];
    that.updateOptions(options);
    switch(that.type){
        case "bar":
            that.adjustSeriesDimensions = adjustBarSeriesDimensions;
            that.updateSeriesValues = updateBarSeriesValues;
            that.adjustSeriesValues = adjustStackedSeriesValues;
            break;
        case "rangebar":
            that.adjustSeriesDimensions = adjustBarSeriesDimensions;
            that.updateSeriesValues = updateRangeSeriesValues;
            break;
        case "fullstackedbar":
            that.fullStacked = true;
            that.adjustSeriesDimensions = adjustBarSeriesDimensions;
            that.adjustSeriesValues = adjustStackedSeriesValues;
            that.updateSeriesValues = updateStackedSeriesValues;
            break;
        case "stackedbar":
            that.adjustSeriesDimensions = adjustBarSeriesDimensions;
            that.adjustSeriesValues = adjustStackedSeriesValues;
            that.updateSeriesValues = updateStackedSeriesValues;
            break;
        case "fullstackedarea":
        case "fullstackedline":
        case "fullstackedspline":
        case "fullstackedsplinearea":
            that.fullStacked = true;
            that.adjustSeriesValues = adjustStackedSeriesValues;
            break;
        case "stackedarea":
        case "stackedsplinearea":
        case "stackedline":
        case "stackedspline":
            that.adjustSeriesValues = adjustStackedSeriesValues;
            break;
        case "candlestick":
        case "stock":
            that.adjustSeriesDimensions = adjustCandlestickSeriesDimensions;
            break;
        case "bubble":
            that.adjustSeriesDimensions = adjustBubbleSeriesDimensions;
    }
}
SeriesFamily.prototype = {
    constructor: SeriesFamily,
    adjustSeriesDimensions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    adjustSeriesValues: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    updateSeriesValues: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    updateOptions: function(options) {
        this._options = options;
    },
    dispose: function() {
        this.series = null;
    },
    add: function(series) {
        const type = this.type;
        this.series = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["map"])(series, (singleSeries)=>singleSeries.type === type ? singleSeries : null);
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/core/base_theme_manager.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/base_theme_manager.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "BaseThemeManager": ()=>BaseThemeManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/palette.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/themes.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const _getTheme = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTheme"];
const _addCacheItem = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["addCacheItem"];
const _removeCacheItem = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeCacheItem"];
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"];
const _each = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"];
function getThemePart(theme, path) {
    let _theme = theme;
    path && _each(path.split("."), function(_, pathItem) {
        return _theme = _theme[pathItem];
    });
    return _theme;
}
const BaseThemeManager = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor: function(options) {
        this._themeSection = options.themeSection;
        this._fontFields = options.fontFields || [];
        _addCacheItem(this);
    },
    dispose: function() {
        _removeCacheItem(this);
        this._callback = this._theme = this._font = null;
        return this;
    },
    setCallback: function(callback) {
        this._callback = callback;
        return this;
    },
    setTheme: function(theme, rtl) {
        this._current = theme;
        this._rtl = rtl;
        return this.refresh();
    },
    refresh: function() {
        const that = this;
        const current = that._current || {};
        let theme = _getTheme(current.name || current);
        that._themeName = theme.name;
        that._defaultPalette = theme.defaultPalette;
        that._font = _extend({}, theme.font, current.font);
        that._themeSection && _each(that._themeSection.split("."), function(_, path) {
            theme = _extend(true, {}, theme[path]);
        });
        that._theme = _extend(true, {}, theme, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(current) ? {} : current);
        that._initializeTheme();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parseScalar"])(that._rtl, that._theme.rtlEnabled)) {
            _extend(true, that._theme, that._theme._rtl);
        }
        that._callback();
        return that;
    },
    theme: function(path) {
        return getThemePart(this._theme, path);
    },
    themeName: function() {
        return this._themeName;
    },
    createPalette: function(palette, options) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createPalette"])(palette, options, this._defaultPalette);
    },
    createDiscretePalette: function(palette, count) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDiscretePalette"])(palette, count, this._defaultPalette);
    },
    createGradientPalette: function(palette) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getGradientPalette"])(palette, this._defaultPalette);
    },
    getAccentColor: function(palette) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAccentColor"])(palette, this._defaultPalette);
    },
    _initializeTheme: function() {
        const that = this;
        _each(that._fontFields || [], function(_, path) {
            that._initializeFont(getThemePart(that._theme, path));
        });
    },
    _initializeFont: function(font) {
        _extend(font, this._font, _extend({}, font));
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/core/title.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/title.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Title": ()=>Title,
    "plugin": ()=>plugin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/layout_element.js [app-ssr] (ecmascript)");
;
;
;
;
const _Number = Number;
const parseHorizontalAlignment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enumParser"])([
    "left",
    "center",
    "right"
]);
const parseVerticalAlignment = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enumParser"])([
    "top",
    "bottom"
]);
const DEFAULT_MARGIN = 10;
function hasText(text) {
    return !!(text && String(text).length > 0);
}
function processTitleLength(elem, text, width, options, placeholderSize) {
    if (elem.attr({
        text: text
    }).setMaxSize(width, placeholderSize, options).textChanged) {
        elem.setTitle(text);
    }
}
function pickMarginValue(value) {
    return value >= 0 ? _Number(value) : 10;
}
function validateMargin(margin) {
    let result;
    if (margin >= 0) {
        result = {
            left: _Number(margin),
            top: _Number(margin),
            right: _Number(margin),
            bottom: _Number(margin)
        };
    } else {
        margin = margin || {};
        result = {
            left: pickMarginValue(margin.left),
            top: pickMarginValue(margin.top),
            right: pickMarginValue(margin.right),
            bottom: pickMarginValue(margin.bottom)
        };
    }
    return result;
}
function checkRect(rect, boundingRect) {
    return rect[2] - rect[0] < boundingRect.width || rect[3] - rect[1] < boundingRect.height;
}
let Title = function(params) {
    this._params = params;
    this._group = params.renderer.g().attr({
        class: params.cssClass
    }).linkOn(params.root || params.renderer.root, "title");
    this._hasText = false;
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(Title.prototype, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LayoutElement"].prototype, {
    dispose: function() {
        const that = this;
        that._group.linkRemove();
        that._group.linkOff();
        if (that._titleElement) {
            that._clipRect.dispose();
            that._titleElement = that._subtitleElement = that._clipRect = null;
        }
        that._params = that._group = that._options = null;
    },
    _updateOptions: function(options) {
        this._options = options;
        this._options.horizontalAlignment = parseHorizontalAlignment(options.horizontalAlignment, "center");
        this._options.verticalAlignment = parseVerticalAlignment(options.verticalAlignment, "top");
        this._options.margin = validateMargin(options.margin);
    },
    _updateStructure: function() {
        const that = this;
        const renderer = that._params.renderer;
        const group = that._group;
        const options = that._options;
        const align = options.horizontalAlignment;
        if (!that._titleElement) {
            that._titleElement = renderer.text().append(group);
            that._subtitleElement = renderer.text();
            that._clipRect = renderer.clipRect();
            group.attr({
                "clip-path": that._clipRect.id
            });
        }
        that._titleElement.attr({
            align: align,
            class: options.cssClass
        });
        that._subtitleElement.attr({
            align: align,
            class: options.subtitle.cssClass
        });
        group.linkAppend();
        hasText(options.subtitle.text) ? that._subtitleElement.append(group) : that._subtitleElement.remove();
    },
    _updateTexts: function() {
        const options = this._options;
        const subtitleOptions = options.subtitle;
        const titleElement = this._titleElement;
        const subtitleElement = this._subtitleElement;
        let titleBox;
        titleElement.attr({
            text: "A",
            y: 0
        }).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.font));
        titleBox = titleElement.getBBox();
        this._baseLineCorrection = titleBox.height + titleBox.y;
        titleElement.attr({
            text: options.text
        });
        titleBox = titleElement.getBBox();
        const y = -titleBox.y;
        titleElement.attr({
            y: y
        });
        if (hasText(subtitleOptions.text)) {
            subtitleElement.attr({
                text: subtitleOptions.text,
                y: 0
            }).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(subtitleOptions.font));
        }
    },
    _shiftSubtitle () {
        const titleBox = this._titleElement.getBBox();
        const element = this._subtitleElement;
        const offset = this._options.subtitle.offset;
        element.move(0, titleBox.y + titleBox.height - element.getBBox().y - offset);
    },
    _updateBoundingRectAlignment: function() {
        const boundingRect = this._boundingRect;
        const options = this._options;
        boundingRect.verticalAlignment = options.verticalAlignment;
        boundingRect.horizontalAlignment = options.horizontalAlignment;
        boundingRect.cutLayoutSide = options.verticalAlignment;
        boundingRect.cutSide = "vertical";
        boundingRect.position = {
            horizontal: options.horizontalAlignment,
            vertical: options.verticalAlignment
        };
    },
    hasText: function() {
        return this._hasText;
    },
    update: function(themeOptions, userOptions) {
        const that = this;
        const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, themeOptions, processTitleOptions(userOptions));
        const _hasText = hasText(options.text);
        const isLayoutChanged = _hasText || _hasText !== that._hasText;
        that._baseLineCorrection = 0;
        that._updateOptions(options);
        that._boundingRect = {};
        if (_hasText) {
            that._updateStructure();
            that._updateTexts();
        } else {
            that._group.linkRemove();
        }
        that._updateBoundingRect();
        that._updateBoundingRectAlignment();
        that._hasText = _hasText;
        return isLayoutChanged;
    },
    draw: function(width, height) {
        const that = this;
        if (that._hasText) {
            that._group.linkAppend();
            that._correctTitleLength(width);
            if (that._group.getBBox().height > height) {
                this.freeSpace();
            }
        }
        return that;
    },
    _correctTitleLength: function(width) {
        const that = this;
        const options = that._options;
        const margin = options.margin;
        const maxWidth = width - margin.left - margin.right;
        let placeholderSize = options.placeholderSize;
        processTitleLength(that._titleElement, options.text, maxWidth, options, placeholderSize);
        if (that._subtitleElement) {
            if (_Number(placeholderSize) > 0) {
                placeholderSize -= that._titleElement.getBBox().height;
            }
            processTitleLength(that._subtitleElement, options.subtitle.text, maxWidth, options.subtitle, placeholderSize);
            that._shiftSubtitle();
        }
        that._updateBoundingRect();
        const { x: x, y: y, height: height } = this.getCorrectedLayoutOptions();
        this._clipRect.attr({
            x: x,
            y: y,
            width: width,
            height: height
        });
    },
    getLayoutOptions: function() {
        return this._boundingRect || null;
    },
    shift: function(x, y) {
        const box = this.getLayoutOptions();
        this._group.move(x - box.x, y - box.y);
        return this;
    },
    _updateBoundingRect: function() {
        const that = this;
        const options = that._options;
        const margin = options.margin;
        const boundingRect = that._boundingRect;
        const box = that._hasText ? that._group.getBBox() : {
            width: 0,
            height: 0,
            x: 0,
            y: 0,
            isEmpty: true
        };
        if (!box.isEmpty) {
            box.height += margin.top + margin.bottom - that._baseLineCorrection;
            box.width += margin.left + margin.right;
            box.x -= margin.left;
            box.y += that._baseLineCorrection - margin.top;
        }
        if (options.placeholderSize > 0) {
            box.height = options.placeholderSize;
        }
        boundingRect.height = box.height;
        boundingRect.width = box.width;
        boundingRect.x = box.x;
        boundingRect.y = box.y;
    },
    getCorrectedLayoutOptions () {
        const srcBox = this.getLayoutOptions();
        const correction = this._baseLineCorrection;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, srcBox, {
            y: srcBox.y - correction,
            height: srcBox.height + correction
        });
    },
    layoutOptions: function() {
        if (!this._hasText) {
            return null;
        }
        return {
            horizontalAlignment: this._boundingRect.horizontalAlignment,
            verticalAlignment: this._boundingRect.verticalAlignment,
            priority: 0
        };
    },
    measure: function(size) {
        this.draw(size[0], size[1]);
        return [
            this._boundingRect.width,
            this._boundingRect.height
        ];
    },
    move: function(rect, fitRect) {
        const boundingRect = this._boundingRect;
        if (checkRect(rect, boundingRect)) {
            this.shift(fitRect[0], fitRect[1]);
        } else {
            this.shift(Math.round(rect[0]), Math.round(rect[1]));
        }
    },
    freeSpace: function() {
        this._params.incidentOccurred("W2103");
        this._group.linkRemove();
        this._boundingRect.width = this._boundingRect.height = 0;
    },
    getOptions: function() {
        return this._options;
    },
    changeLink: function(root) {
        this._group.linkRemove();
        this._group.linkOn(root, "title");
    }
});
function processTitleOptions(options) {
    const newOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(options) ? {
        text: options
    } : options || {};
    newOptions.subtitle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(newOptions.subtitle) ? {
        text: newOptions.subtitle
    } : newOptions.subtitle || {};
    return newOptions;
}
const plugin = {
    name: "title",
    init: function() {
        this._title = new Title({
            renderer: this._renderer,
            cssClass: this._rootClassPrefix + "-title",
            incidentOccurred: this._incidentOccurred
        });
        this._layout.add(this._title);
    },
    dispose: function() {
        this._title.dispose();
        this._title = null;
    },
    customize: function(constructor) {
        constructor.addChange({
            code: "TITLE",
            handler: function() {
                if (this._title.update(this._themeManager.theme("title"), this.option("title"))) {
                    this._change([
                        "LAYOUT"
                    ]);
                }
            },
            isThemeDependent: true,
            option: "title",
            isOptionChange: true
        });
    },
    fontFields: [
        "title.font",
        "title.subtitle.font"
    ]
};
}),
"[project]/node_modules/devextreme/esm/viz/core/data_source.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/data_source.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "plugin": ()=>plugin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$data_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/data_helper.js [app-ssr] (ecmascript)");
;
;
const postCtor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$data_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].postCtor;
let name;
const members = {
    _dataSourceLoadErrorHandler: function() {
        this._dataSourceChangedHandler();
    },
    _dataSourceOptions: function() {
        return {
            paginate: false
        };
    },
    _updateDataSource: function() {
        this._refreshDataSource();
        if (!this.option("dataSource")) {
            this._dataSourceChangedHandler();
        }
    },
    _dataIsLoaded: function() {
        return !this._dataSource || this._dataSource.isLoaded();
    },
    _dataSourceItems: function() {
        return this._dataSource && this._dataSource.items();
    }
};
for(name in __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$data_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]){
    if ("postCtor" === name) {
        continue;
    }
    members[name] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$data_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"][name];
}
const plugin = {
    name: "data_source",
    init: function() {
        postCtor.call(this);
    },
    dispose: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"],
    members: members
};
}),
"[project]/node_modules/devextreme/esm/viz/core/export.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/export.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "ExportMenu": ()=>ExportMenu,
    "combineMarkups": ()=>combineMarkups,
    "exportFromMarkup": ()=>exportFromMarkup,
    "exportWidgets": ()=>exportWidgets,
    "getMarkup": ()=>getMarkup,
    "plugin": ()=>plugin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/svg.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_svg.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/message.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/themes.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$hover$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/hover.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hover$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_hover.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/console.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_console.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const pointerActions = [
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].down,
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].move
].join(" ");
const BUTTON_SIZE = 35;
const ICON_COORDS = [
    [
        9,
        12,
        26,
        12,
        26,
        14,
        9,
        14
    ],
    [
        9,
        17,
        26,
        17,
        26,
        19,
        9,
        19
    ],
    [
        9,
        22,
        26,
        22,
        26,
        24,
        9,
        24
    ]
];
const LIST_PADDING_TOP = 4;
const LIST_WIDTH = 120;
const VERTICAL_TEXT_MARGIN = 8;
const HORIZONTAL_TEXT_MARGIN = 15;
const MENU_ITEM_HEIGHT = 30;
const LIST_STROKE_WIDTH = 1;
const MARGIN = 10;
const SHADOW_OFFSET = 2;
const SHADOW_BLUR = 3;
const DEFAULT_EXPORT_FORMAT = "PNG";
const ALLOWED_IMAGE_FORMATS = [
    "PNG",
    "JPEG",
    "GIF"
];
const ALLOWED_EXTRA_FORMATS = [
    "PDF",
    "SVG"
];
const EXPORT_CSS_CLASS = "dx-export-menu";
const A4WidthCm = "21cm";
const EXPORT_DATA_KEY = "export-element-type";
const FORMAT_DATA_KEY = "export-element-format";
const GET_COLOR_REGEX = /data-backgroundcolor="([^"]*)"/;
function getRendererWrapper(width, height, backgroundColor) {
    const rendererContainer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("<div>").get(0);
    const renderer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Renderer"]({
        container: rendererContainer
    });
    renderer.resize(width, height);
    renderer.root.element.setAttribute("data-backgroundcolor", backgroundColor);
    return {
        createGroup: ()=>renderer.g(),
        getRootContent: ()=>renderer.root.element.cloneNode(true),
        dispose () {
            renderer.dispose();
            rendererContainer.remove();
        }
    };
}
function getValidFormats() {
    const imageFormats = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["image"].testFormats(ALLOWED_IMAGE_FORMATS);
    return {
        unsupported: imageFormats.unsupported,
        supported: imageFormats.supported.concat(ALLOWED_EXTRA_FORMATS)
    };
}
function validateFormat(format, incidentOccurred, validFormats) {
    validFormats = validFormats || getValidFormats();
    format = String(format).toUpperCase();
    if (-1 !== validFormats.supported.indexOf(format)) {
        return format;
    }
    if (-1 !== validFormats.unsupported.indexOf(format)) {
        incidentOccurred && incidentOccurred("W2108", [
            format
        ]);
    }
}
function getCreatorFunc(format) {
    if ("SVG" === format) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["svg"].getData;
    } else if ("PDF" === format) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["pdf"].getData;
    } else {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["image"].getData;
    }
}
function print(imageSrc, options) {
    const document = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])().document;
    const iFrame = document.createElement("iframe");
    iFrame.onload = setPrint(imageSrc, options);
    iFrame.style.position = "fixed";
    iFrame.style.width = "0";
    iFrame.style.height = "0";
    iFrame.style.right = "0";
    iFrame.style.bottom = "0";
    document.body.appendChild(iFrame);
}
function calculatePrintPageWidth(iFrameBody) {
    iFrameBody.style.width = "21cm";
    const width = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWidth"])(iFrameBody);
    iFrameBody.style.width = "";
    return width;
}
function setPrint(imageSrc, options) {
    return function() {
        let window = this.contentWindow;
        const img = window.document.createElement("img");
        window.document.body.appendChild(img);
        const widthRatio = calculatePrintPageWidth(window.document.body) / options.width;
        if (widthRatio < 1) {
            window.document.body.style.transform = `scale(${widthRatio})`;
            window.document.body.style["transform-origin"] = "0 0";
        }
        const removeFrame = ()=>{
            this.parentElement.removeChild(this);
        };
        img.addEventListener("load", ()=>{
            window.focus();
            window.print();
        });
        img.addEventListener("error", removeFrame);
        window.addEventListener("afterprint", ()=>{
            setTimeout(removeFrame, 0);
        });
        img.src = imageSrc;
    };
}
function getItemAttributes(options, type, itemIndex) {
    const x = -85;
    const y = 40 + 30 * itemIndex;
    const attr = {
        rect: {
            width: 118,
            height: 30,
            x: -84,
            y: y
        },
        text: {
            x: x + (options.rtl ? 105 : 15),
            y: y + 30 - 8
        }
    };
    if ("printing" === type) {
        attr.separator = {
            stroke: options.button.default.borderColor,
            "stroke-width": 1,
            cursor: "pointer",
            sharp: "v",
            d: "M -85 " + (y + 30 - 1) + " L 35 " + (y + 30 - 1)
        };
    }
    return attr;
}
function createMenuItem(renderer, options, settings) {
    const itemData = {};
    const type = settings.type;
    const format = settings.format;
    const attr = getItemAttributes(options, type, settings.itemIndex);
    const fontStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.font);
    fontStyle["pointer-events"] = "none";
    const menuItem = renderer.g().attr({
        class: "dx-export-menu-list-item"
    });
    itemData[EXPORT_DATA_KEY] = type;
    if (format) {
        itemData[FORMAT_DATA_KEY] = format;
    }
    const rect = renderer.rect();
    rect.attr(attr.rect).css({
        cursor: "pointer",
        "pointer-events": "all"
    }).data(itemData);
    rect.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hover$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["start"] + ".export", ()=>rect.attr({
            fill: options.button.hover.backgroundColor
        })).on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hover$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["end"] + ".export", ()=>rect.attr({
            fill: null
        }));
    rect.append(menuItem);
    const text = renderer.text(settings.text).css(fontStyle).attr(attr.text).append(menuItem);
    if ("printing" === type) {
        renderer.path(null, "line").attr(attr.separator).append(menuItem);
    }
    return {
        g: menuItem,
        rect: rect,
        resetState: ()=>rect.attr({
                fill: null
            }),
        fixPosition: ()=>{
            const textBBox = text.getBBox();
            text.move(attr.text.x - textBBox.x - (options.rtl ? textBBox.width : 0));
        }
    };
}
function createMenuItems(renderer, options) {
    let items = [];
    if (options.printingEnabled) {
        items.push(createMenuItem(renderer, options, {
            type: "printing",
            text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format("vizExport-printingButtonText"),
            itemIndex: items.length
        }));
    }
    items = options.formats.reduce((r, format)=>{
        r.push(createMenuItem(renderer, options, {
            type: "exporting",
            text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getFormatter("vizExport-exportButtonText")(format),
            format: format,
            itemIndex: r.length
        }));
        return r;
    }, items);
    return items;
}
function getBackgroundColorFromMarkup(markup) {
    const parsedMarkup = GET_COLOR_REGEX.exec(markup);
    return null === parsedMarkup || void 0 === parsedMarkup ? void 0 : parsedMarkup[1];
}
const exportFromMarkup = function(markup, options) {
    options.format = validateFormat(options.format) || "PNG";
    options.fileName = options.fileName || "file";
    options.exportingAction = options.onExporting;
    options.exportedAction = options.onExported;
    options.fileSavingAction = options.onFileSaving;
    options.margin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.margin) ? options.margin : 10;
    options.backgroundColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.backgroundColor) ? options.backgroundColor : getBackgroundColorFromMarkup(markup) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTheme"])().backgroundColor;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["export"])(markup, options, getCreatorFunc(options.format));
};
const getMarkup = (widgets)=>combineMarkups(widgets).root.outerHTML;
const exportWidgets = function(widgets, options) {
    options = options || {};
    const markupInfo = combineMarkups(widgets, {
        gridLayout: options.gridLayout,
        verticalAlignment: options.verticalAlignment,
        horizontalAlignment: options.horizontalAlignment
    });
    options.width = markupInfo.width;
    options.height = markupInfo.height;
    exportFromMarkup(markupInfo.root, options);
};
let combineMarkups = function(widgets) {
    let options = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
    if (!Array.isArray(widgets)) {
        widgets = [
            [
                widgets
            ]
        ];
    } else if (!Array.isArray(widgets[0])) {
        widgets = widgets.map((item)=>[
                item
            ]);
    }
    const compactView = !options.gridLayout;
    const exportItems = widgets.reduce((r, row, rowIndex)=>{
        const rowInfo = row.reduce((r, item, colIndex)=>{
            const size = item.getSize();
            const backgroundColor = item.option("backgroundColor") || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getTheme"])(item.option("theme")).backgroundColor;
            const node = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(item.element()).find("svg").get(0).cloneNode(true);
            backgroundColor && -1 === r.backgroundColors.indexOf(backgroundColor) && r.backgroundColors.push(backgroundColor);
            r.hOffset = r.width;
            r.width += size.width;
            r.height = Math.max(r.height, size.height);
            r.itemWidth = Math.max(r.itemWidth, size.width);
            r.items.push({
                node: node,
                width: size.width,
                height: size.height,
                c: colIndex,
                r: rowIndex,
                hOffset: r.hOffset
            });
            return r;
        }, {
            items: [],
            height: 0,
            itemWidth: 0,
            hOffset: 0,
            width: 0,
            backgroundColors: r.backgroundColors
        });
        r.rowOffsets.push(r.totalHeight);
        r.rowHeights.push(rowInfo.height);
        r.totalHeight += rowInfo.height;
        r.items = r.items.concat(rowInfo.items);
        r.itemWidth = Math.max(r.itemWidth, rowInfo.itemWidth);
        r.maxItemLen = Math.max(r.maxItemLen, rowInfo.items.length);
        r.totalWidth = compactView ? Math.max(r.totalWidth, rowInfo.width) : r.maxItemLen * r.itemWidth;
        return r;
    }, {
        items: [],
        rowOffsets: [],
        rowHeights: [],
        itemWidth: 0,
        totalHeight: 0,
        maxItemLen: 0,
        totalWidth: 0,
        backgroundColors: []
    });
    const backgroundColor = `${1 === exportItems.backgroundColors.length ? exportItems.backgroundColors[0] : ""}`;
    const { totalWidth: totalWidth, totalHeight: totalHeight } = exportItems;
    const rootElement = wrapItemsToElement(totalWidth, totalHeight, backgroundColor, {
        options: options,
        exportItems: exportItems,
        compactView: compactView
    });
    return {
        root: rootElement,
        width: totalWidth,
        height: totalHeight
    };
};
function wrapItemsToElement(width, height, backgroundColor, _ref) {
    let { exportItems: exportItems, options: options, compactView: compactView } = _ref;
    const rendererWrapper = getRendererWrapper(width, height, backgroundColor);
    const getVOffset = (item)=>{
        const align = options.verticalAlignment;
        const dy = exportItems.rowHeights[item.r] - item.height;
        return exportItems.rowOffsets[item.r] + ("bottom" === align ? dy : "center" === align ? dy / 2 : 0);
    };
    const getHOffset = (item)=>{
        if (compactView) {
            return item.hOffset;
        }
        const align = options.horizontalAlignment;
        const colWidth = exportItems.itemWidth;
        const dx = colWidth - item.width;
        return item.c * colWidth + ("right" === align ? dx : "center" === align ? dx / 2 : 0);
    };
    exportItems.items.forEach((item)=>{
        const container = rendererWrapper.createGroup();
        container.attr({
            translateX: getHOffset(item),
            translateY: getVOffset(item)
        });
        container.element.appendChild(item.node);
        container.append();
    });
    const result = rendererWrapper.getRootContent();
    rendererWrapper.dispose();
    return result;
}
let ExportMenu = function(params) {
    const renderer = this._renderer = params.renderer;
    this._incidentOccurred = params.incidentOccurred;
    this._exportTo = params.exportTo;
    this._print = params.print;
    this._shadow = renderer.shadowFilter("-50%", "-50%", "200%", "200%", 2, 6, 3);
    this._shadow.attr({
        opacity: .8
    });
    this._group = renderer.g().attr({
        class: "dx-export-menu",
        [__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["HIDDEN_FOR_EXPORT"]]: true
    }).linkOn(renderer.root, {
        name: "export-menu",
        after: "peripheral"
    });
    this._buttonGroup = renderer.g().attr({
        class: "dx-export-menu-button"
    }).append(this._group);
    this._listGroup = renderer.g().attr({
        class: "dx-export-menu-list"
    }).append(this._group);
    this._overlay = renderer.rect(-85, 39, 120, 0);
    this._overlay.attr({
        "stroke-width": 1,
        cursor: "pointer",
        rx: 4,
        ry: 4,
        filter: this._shadow.id
    });
    this._overlay.data({
        "export-element-type": "list"
    });
    this.validFormats = getValidFormats();
    this._subscribeEvents();
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(ExportMenu.prototype, {
    getLayoutOptions () {
        if (this._hiddenDueToLayout) {
            return {
                width: 0,
                height: 0,
                cutSide: "vertical",
                cutLayoutSide: "top"
            };
        }
        const bBox = this._buttonGroup.getBBox();
        bBox.cutSide = "vertical";
        bBox.cutLayoutSide = "top";
        bBox.height += 10;
        bBox.position = {
            vertical: "top",
            horizontal: "right"
        };
        bBox.verticalAlignment = "top";
        bBox.horizontalAlignment = "right";
        return bBox;
    },
    shift (_, y) {
        this._group.attr({
            translateY: this._group.attr("translateY") + y
        });
    },
    draw (width, height, canvas) {
        this._group.move(width - 35 - 2 - 3 + canvas.left, Math.floor(height / 2 - 17.5));
        const layoutOptions = this.getLayoutOptions();
        if (layoutOptions.width > width || layoutOptions.height > height) {
            this.freeSpace();
        }
        return this;
    },
    show () {
        this._group.linkAppend();
    },
    hide () {
        this._group.linkRemove();
    },
    setOptions (options) {
        this._options = options;
        if (options.formats) {
            options.formats = options.formats.reduce((r, format)=>{
                format = validateFormat(format, this._incidentOccurred, this.validFormats);
                format && r.push(format);
                return r;
            }, []);
        } else {
            options.formats = this.validFormats.supported.slice();
        }
        options.printingEnabled = void 0 === options.printingEnabled ? true : options.printingEnabled;
        if (options.enabled && (options.formats.length || options.printingEnabled)) {
            this.show();
            this._updateButton();
            this._updateList();
            this._hideList();
        } else {
            this.hide();
        }
    },
    dispose () {
        this._unsubscribeEvents();
        this._group.linkRemove().linkOff();
        this._group.dispose();
        this._shadow.dispose();
    },
    layoutOptions () {
        return this._options.enabled && {
            horizontalAlignment: "right",
            verticalAlignment: "top",
            weak: true
        };
    },
    measure () {
        this._fillSpace();
        const margin = this._options.button.margin;
        return [
            35 + margin.left + margin.right,
            35 + margin.top + margin.bottom
        ];
    },
    move (rect) {
        const margin = this._options.button.margin;
        this._group.attr({
            translateX: Math.round(rect[0]) + margin.left,
            translateY: Math.round(rect[1]) + margin.top
        });
    },
    _fillSpace () {
        this._hiddenDueToLayout = false;
        this.show();
    },
    freeSpace () {
        this._incidentOccurred("W2107");
        this._hiddenDueToLayout = true;
        this.hide();
    },
    _hideList () {
        this._listGroup.remove();
        this._listShown = false;
        this._setButtonState("default");
        this._menuItems.forEach((item)=>item.resetState());
    },
    _showList () {
        this._listGroup.append(this._group);
        this._listShown = true;
        this._menuItems.forEach((item)=>item.fixPosition());
    },
    _setButtonState (state) {
        const style = this._options.button[state];
        this._button.attr({
            stroke: style.borderColor,
            fill: style.backgroundColor
        });
        this._icon.attr({
            fill: style.color
        });
    },
    _subscribeEvents () {
        this._renderer.root.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].up + ".export", (e)=>{
            const elementType = e.target[EXPORT_DATA_KEY];
            if (!elementType) {
                if (this._button) {
                    this._hideList();
                }
                return;
            }
            if ("button" === elementType) {
                if (this._listShown) {
                    this._setButtonState("default");
                    this._hideList();
                } else {
                    this._setButtonState("focus");
                    this._showList();
                }
            } else if ("printing" === elementType) {
                this._print();
                this._hideList();
            } else if ("exporting" === elementType) {
                this._exportTo(e.target[FORMAT_DATA_KEY]);
                this._hideList();
            }
        });
        this._listGroup.on(pointerActions, (e)=>e.stopPropagation());
        this._buttonGroup.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].enter, ()=>this._setButtonState("hover"));
        this._buttonGroup.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].leave, ()=>this._setButtonState(this._listShown ? "focus" : "default"));
        this._buttonGroup.on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].down + ".export", ()=>this._setButtonState("active"));
    },
    _unsubscribeEvents () {
        this._renderer.root.off(".export");
        this._listGroup.off();
        this._buttonGroup.off();
    },
    _updateButton () {
        const renderer = this._renderer;
        const options = this._options;
        const exportData = {
            "export-element-type": "button"
        };
        if (!this._button) {
            this._button = renderer.rect(0, 0, 35, 35).append(this._buttonGroup);
            this._button.attr({
                rx: 4,
                ry: 4,
                fill: options.button.default.backgroundColor,
                stroke: options.button.default.borderColor,
                "stroke-width": 1,
                cursor: "pointer"
            });
            this._button.data(exportData);
            this._icon = renderer.path(ICON_COORDS).append(this._buttonGroup);
            this._icon.attr({
                fill: options.button.default.color,
                cursor: "pointer"
            });
            this._icon.data(exportData);
            this._buttonGroup.setTitle(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$message$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format("vizExport-titleMenuText"));
        }
    },
    _updateList () {
        const options = this._options;
        const buttonDefault = options.button.default;
        const listGroup = this._listGroup;
        const items = createMenuItems(this._renderer, options);
        this._shadow.attr({
            color: options.shadowColor
        });
        this._overlay.attr({
            height: 30 * items.length + 2,
            fill: buttonDefault.backgroundColor,
            stroke: buttonDefault.borderColor
        });
        listGroup.clear();
        this._overlay.append(listGroup);
        items.forEach((item)=>item.g.append(listGroup));
        this._menuItems = items;
    }
});
function getExportOptions(widget, exportOptions, fileName, format) {
    if (format || exportOptions.format) {
        format = validateFormat(format || exportOptions.format, widget._incidentOccurred);
    }
    const { width: width, height: height } = widget.getSize();
    return {
        format: format || "PNG",
        fileName: fileName || exportOptions.fileName || "file",
        backgroundColor: exportOptions.backgroundColor,
        width: width,
        height: height,
        margin: exportOptions.margin,
        svgToCanvas: exportOptions.svgToCanvas,
        exportingAction: widget._createActionByOption("onExporting", {
            excludeValidators: [
                "disabled"
            ]
        }),
        exportedAction: widget._createActionByOption("onExported", {
            excludeValidators: [
                "disabled"
            ]
        }),
        fileSavingAction: widget._createActionByOption("onFileSaving", {
            excludeValidators: [
                "disabled"
            ]
        })
    };
}
const plugin = {
    name: "export",
    init () {
        this._exportMenu = new ExportMenu({
            renderer: this._renderer,
            incidentOccurred: this._incidentOccurred,
            print: ()=>this.print(),
            exportTo: (format)=>this.exportTo(void 0, format)
        });
        this._layout.add(this._exportMenu);
    },
    dispose () {
        this._exportMenu.dispose();
    },
    members: {
        _getExportMenuOptions () {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, this._getOption("export"), {
                rtl: this._getOption("rtlEnabled", true)
            });
        },
        _disablePointerEvents () {
            const pointerEventsValue = this._renderer.root.attr("pointer-events");
            this._renderer.root.attr({
                "pointer-events": "none"
            });
            return pointerEventsValue;
        },
        exportTo (fileName, format) {
            const menu = this._exportMenu;
            const options = getExportOptions(this, this._getOption("export") || {}, fileName, format);
            menu && menu.hide();
            const pointerEventsValue = this._disablePointerEvents();
            const promise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["export"])(this._renderer.root.element, options, getCreatorFunc(options.format)).fail(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logger"].error).always(()=>{
                this._renderer.root.attr({
                    "pointer-events": pointerEventsValue
                });
            });
            menu && menu.show();
            return promise;
        },
        print () {
            const menu = this._exportMenu;
            const options = getExportOptions(this, this._getOption("export") || {});
            options.exportingAction = null;
            options.exportedAction = null;
            options.margin = 0;
            options.format = "PNG";
            options.useBase64 = true;
            options.fileSavingAction = (eventArgs)=>{
                print(`data:image/png;base64,${eventArgs.data}`, {
                    width: options.width,
                    __test: options.__test
                });
                eventArgs.cancel = true;
            };
            const pointerEventsValue = this._disablePointerEvents();
            menu && menu.hide();
            const promise = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["export"])(this._renderer.root.element, options, getCreatorFunc(options.format)).fail(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logger"].error).always(()=>{
                this._renderer.root.attr({
                    "pointer-events": pointerEventsValue
                });
            });
            menu && menu.show();
            return promise;
        }
    },
    customize (constructor) {
        const proto = constructor.prototype;
        constructor.addChange({
            code: "EXPORT",
            handler () {
                this._exportMenu.setOptions(this._getExportMenuOptions());
                this._change([
                    "LAYOUT"
                ]);
            },
            isThemeDependent: true,
            isOptionChange: true,
            option: "export"
        });
        proto._optionChangesMap.onExporting = "EXPORT";
        proto._optionChangesMap.onExported = "EXPORT";
        proto._optionChangesMap.onFileSaving = "EXPORT";
    },
    fontFields: [
        "export.font"
    ]
};
}),
"[project]/node_modules/devextreme/esm/viz/core/loading_indicator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/loading_indicator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "LoadingIndicator": ()=>LoadingIndicator,
    "plugin": ()=>plugin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
;
const STATE_HIDDEN = 0;
const STATE_SHOWN = 1;
const ANIMATION_EASING = "linear";
const ANIMATION_DURATION = 400;
const LOADING_INDICATOR_READY = "loadingIndicatorReady";
let LoadingIndicator = function(parameters) {
    const renderer = parameters.renderer;
    this._group = renderer.g().attr({
        class: "dx-loading-indicator"
    }).linkOn(renderer.root, {
        name: "loading-indicator",
        after: "peripheral"
    });
    this._rect = renderer.rect().attr({
        opacity: 0
    }).append(this._group);
    this._text = renderer.text().attr({
        align: "center"
    }).append(this._group);
    this._createStates(parameters.eventTrigger, this._group, renderer.root, parameters.notify);
};
LoadingIndicator.prototype = {
    constructor: LoadingIndicator,
    _createStates: function(eventTrigger, group, root, notify) {
        this._states = [
            {
                opacity: 0,
                start: function() {
                    notify(false);
                },
                complete: function() {
                    group.linkRemove();
                    root.css({
                        "pointer-events": ""
                    });
                    eventTrigger("loadingIndicatorReady");
                }
            },
            {
                opacity: .85,
                start: function() {
                    group.linkAppend();
                    root.css({
                        "pointer-events": "none"
                    });
                    notify(true);
                },
                complete: function() {
                    eventTrigger("loadingIndicatorReady");
                }
            }
        ];
        this._state = 0;
    },
    setSize: function(size) {
        const width = size.width;
        const height = size.height;
        this._rect.attr({
            width: width,
            height: height
        });
        this._text.attr({
            x: width / 2,
            y: height / 2
        });
    },
    setOptions: function(options) {
        this._rect.attr({
            fill: options.backgroundColor
        });
        this._text.css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.font)).attr({
            text: options.text,
            class: options.cssClass
        });
        this[options.show ? "show" : "hide"]();
    },
    dispose: function() {
        this._group.linkRemove().linkOff();
        this._group = this._rect = this._text = this._states = null;
    },
    _transit: function(stateId) {
        const that = this;
        let state;
        if (that._state !== stateId) {
            that._state = stateId;
            that._isHiding = false;
            state = that._states[stateId];
            that._rect.stopAnimation().animate({
                opacity: state.opacity
            }, {
                complete: state.complete,
                easing: "linear",
                duration: 400,
                unstoppable: true
            });
            that._noHiding = true;
            state.start();
            that._noHiding = false;
        }
    },
    show: function() {
        this._transit(1);
    },
    hide: function() {
        this._transit(0);
    },
    scheduleHiding: function() {
        if (!this._noHiding) {
            this._isHiding = true;
        }
    },
    fulfillHiding: function() {
        if (this._isHiding) {
            this.hide();
        }
    }
};
const plugin = {
    name: "loading_indicator",
    init: function() {
        const that = this;
        that._loadingIndicator = new LoadingIndicator({
            eventTrigger: that._eventTrigger,
            renderer: that._renderer,
            notify: function(state) {
                that._skipLoadingIndicatorOptions = true;
                that.option("loadingIndicator", {
                    show: state
                });
                that._skipLoadingIndicatorOptions = false;
                if (state) {
                    that._stopCurrentHandling();
                }
            }
        });
        that._scheduleLoadingIndicatorHiding();
    },
    dispose: function() {
        this._loadingIndicator.dispose();
        this._loadingIndicator = null;
    },
    members: {
        _scheduleLoadingIndicatorHiding: function() {
            this._loadingIndicator.scheduleHiding();
        },
        _fulfillLoadingIndicatorHiding: function() {
            this._loadingIndicator.fulfillHiding();
        },
        showLoadingIndicator: function() {
            this._loadingIndicator.show();
        },
        hideLoadingIndicator: function() {
            this._loadingIndicator.hide();
        },
        _onBeginUpdate: function() {
            if (!this._optionChangedLocker) {
                this._scheduleLoadingIndicatorHiding();
            }
        }
    },
    extenders: {
        _dataSourceLoadingChangedHandler (isLoading) {
            if (isLoading && (this._options.silent("loadingIndicator") || {}).enabled) {
                this._loadingIndicator.show();
            }
        },
        _setContentSize () {
            this._loadingIndicator.setSize(this._canvas);
        },
        endUpdate () {
            if (this._initialized && this._dataIsReady()) {
                this._fulfillLoadingIndicatorHiding();
            }
        }
    },
    customize: function(constructor) {
        const proto = constructor.prototype;
        if (proto._dataSourceChangedHandler) {
            const _dataSourceChangedHandler = proto._dataSourceChangedHandler;
            proto._dataSourceChangedHandler = function() {
                this._scheduleLoadingIndicatorHiding();
                _dataSourceChangedHandler.apply(this, arguments);
            };
        }
        constructor.addChange({
            code: "LOADING_INDICATOR",
            handler: function() {
                if (!this._skipLoadingIndicatorOptions) {
                    this._loadingIndicator.setOptions(this._getOption("loadingIndicator"));
                }
                this._scheduleLoadingIndicatorHiding();
            },
            isThemeDependent: true,
            option: "loadingIndicator",
            isOptionChange: true
        });
        proto._eventsMap.onLoadingIndicatorReady = {
            name: "loadingIndicatorReady"
        };
        const _drawn = proto._drawn;
        proto._drawn = function() {
            _drawn.apply(this, arguments);
            if (this._dataIsReady()) {
                this._fulfillLoadingIndicatorHiding();
            }
        };
    },
    fontFields: [
        "loadingIndicator.font"
    ]
};
}),
"[project]/node_modules/devextreme/esm/viz/core/errors_warnings.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/errors_warnings.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$error$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/error.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-ssr] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$error$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ERROR_MESSAGES, {
    E2001: "Invalid data source",
    E2002: "Axis type and data type are incompatible",
    E2003: 'The "{0}" data source field contains data of unsupported type',
    E2004: 'The "{0}" data source field is inconsistent',
    E2005: 'The value field "{0}" is absent in the data source or all its values are negative',
    E2006: "A cycle is detected in provided data",
    E2007: 'The value field "{0}" is absent in the data source',
    E2008: 'The value field "{0}" must be a string',
    E2009: 'The value field "{0}" must be a positive numeric value',
    E2101: "Unknown series type: {0}",
    E2102: "Ambiguity occurred between two value axes with the same name",
    E2103: 'The "{0}" option is given an invalid value. Assign a function instead',
    E2104: "Invalid logarithm base",
    E2105: 'Invalid value of a "{0}"',
    E2202: "Invalid {0} scale value",
    E2203: "The range you are trying to set is invalid",
    W2002: "The {0} series cannot be drawn because the {1} data field is missing",
    W2003: "Tick interval is too small",
    W2101: 'The "{0}" pane does not exist; the last pane is used by default',
    W2102: 'A value axis with the "{0}" name was created automatically',
    W2103: "The chart title was hidden due to the container size",
    W2104: "The legend was hidden due to the container size",
    W2105: 'The title of the "{0}" axis was hidden due to the container size',
    W2106: 'The labels of the "{0}" axis were hidden due to the container size',
    W2107: "The export menu was hidden due to the container size",
    W2108: "The browser does not support exporting images to {0} format.",
    W2301: "Invalid value range"
});
}),
"[project]/node_modules/devextreme/esm/viz/core/base_widget.utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/base_widget.utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "createEventTrigger": ()=>createEventTrigger,
    "createIncidentOccurred": ()=>createIncidentOccurred,
    "createResizeHandler": ()=>createResizeHandler
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$string$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/string.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_string.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$errors_warnings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/errors_warnings.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$resize_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/resize_callbacks.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$resize_observer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/resize_observer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
const ERROR_MESSAGES = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$errors_warnings$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].ERROR_MESSAGES;
function createEventTrigger(eventsMap, callbackGetter) {
    let triggers = {};
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(eventsMap, function(name, info) {
        if (info.name) {
            createEvent(name);
        }
    });
    let changes;
    triggerEvent.change = function(name) {
        const eventInfo = eventsMap[name];
        if (eventInfo) {
            (changes = changes || {})[name] = eventInfo;
        }
        return !!eventInfo;
    };
    triggerEvent.applyChanges = function() {
        if (changes) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(changes, function(name, eventInfo) {
                createEvent(eventInfo.newName || name);
            });
            changes = null;
        }
    };
    triggerEvent.dispose = function() {
        eventsMap = callbackGetter = triggers = null;
    };
    return triggerEvent;
    //TURBOPACK unreachable
    ;
    function createEvent(name) {
        const eventInfo = eventsMap[name];
        triggers[eventInfo.name] = callbackGetter(name, eventInfo.actionSettings);
    }
    function triggerEvent(name, arg, complete) {
        triggers[name](arg);
        complete && complete();
    }
}
let createIncidentOccurred = function(widgetName, eventTrigger) {
    return function(id, args) {
        eventTrigger("incidentOccurred", {
            target: {
                id: id,
                type: "E" === id[0] ? "error" : "warning",
                args: args,
                text: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["format"].apply(null, [
                    ERROR_MESSAGES[id]
                ].concat(args || [])),
                widget: widgetName,
                version: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]
            }
        });
    };
};
function getResizeManager(resizeCallback) {
    return (observe, unsubscribe)=>{
        const { handler: handler, dispose: dispose } = createDeferredHandler(resizeCallback, unsubscribe);
        observe(handler);
        return dispose;
    };
}
function createDeferredHandler(callback, unsubscribe) {
    let timeout;
    const handler = function() {
        clearTimeout(timeout);
        timeout = setTimeout(callback, 100);
    };
    return {
        handler: handler,
        dispose () {
            clearTimeout(timeout);
            unsubscribe(handler);
        }
    };
}
function createResizeHandler(contentElement, redrawOnResize, resize) {
    let disposeHandler;
    const resizeManager = getResizeManager(resize);
    if ("windowonly" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(redrawOnResize)) {
        disposeHandler = resizeManager((handler)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$resize_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].add(handler), (handler)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$resize_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].remove(handler));
    } else if (true === redrawOnResize) {
        disposeHandler = resizeManager((handler)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$resize_observer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].observe(contentElement, handler), ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$resize_observer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].unobserve(contentElement));
    }
    return disposeHandler;
}
}),
"[project]/node_modules/devextreme/esm/viz/core/helpers.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/helpers.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "changes": ()=>changes,
    "expand": ()=>expand,
    "replaceInherit": ()=>replaceInherit
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
;
;
;
const isServerSide = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["hasWindow"])();
function Flags() {
    this.reset();
}
Flags.prototype = {
    constructor: Flags,
    add: function(codes) {
        let i;
        const ii = codes.length;
        const flags = this._flags;
        for(i = 0; i < ii; ++i){
            flags[codes[i]] = 1;
        }
    },
    has: function(code) {
        return this._flags[code] > 0;
    },
    count: function() {
        return Object.keys(this._flags).length;
    },
    reset: function() {
        this._flags = {};
    }
};
function combineMaps(baseMap, thisMap) {
    return baseMap !== thisMap ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, baseMap, thisMap) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, baseMap);
}
function combineLists(baseList, thisList) {
    return baseList !== thisList ? baseList.concat(thisList) : baseList.slice();
}
function buildTotalChanges(proto) {
    proto._totalChangesOrder = proto._optionChangesOrder.concat(proto._layoutChangesOrder, proto._customChangesOrder);
}
function addChange(settings) {
    const proto = this.prototype;
    const code = settings.code;
    proto["_change_" + code] = settings.handler;
    if (settings.isThemeDependent) {
        proto._themeDependentChanges.push(code);
    }
    if (settings.option) {
        proto._optionChangesMap[settings.option] = code;
    }
    (settings.isOptionChange ? proto._optionChangesOrder : proto._customChangesOrder).push(code);
    buildTotalChanges(proto);
}
function createChainExecutor() {
    const executeChain = function() {
        let i;
        const ii = executeChain._chain.length;
        let result;
        for(i = 0; i < ii; ++i){
            result = executeChain._chain[i].apply(this, arguments);
        }
        return result;
    };
    executeChain._chain = [];
    executeChain.add = function(item) {
        executeChain._chain.push(item);
    };
    executeChain.copy = function(executor) {
        executeChain._chain = executor._chain.slice();
    };
    return executeChain;
}
function expand(target, name, expander) {
    let current = target[name];
    if (!current) {
        current = expander;
    } else if (!current.add) {
        current = createChainExecutor();
        current.add(target[name]);
        current.add(expander);
    } else {
        if (false === Object.prototype.hasOwnProperty.call(target, name)) {
            current = createChainExecutor();
            current.copy(target[name]);
        }
        current.add(expander);
    }
    target[name] = current;
}
function addPlugin(plugin) {
    const proto = this.prototype;
    proto._plugins.push(plugin);
    plugin.fontFields && proto._fontFields.push.apply(proto._fontFields, plugin.fontFields);
    if (plugin.members) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(this.prototype, plugin.members);
    }
    if (plugin.customize) {
        plugin.customize(this);
    }
    if (plugin.extenders) {
        Object.keys(plugin.extenders).forEach(function(key) {
            const func = plugin.extenders[key];
            expand(proto, key, func);
        }, this);
    }
}
const replaceInherit = isServerSide ? function(widget) {
    const _inherit = widget.inherit;
    widget.inherit = function() {
        const result = _inherit.apply(this, arguments);
        const proto = result.prototype;
        [
            "_plugins",
            "_eventsMap",
            "_initialChanges",
            "_themeDependentChanges",
            "_optionChangesMap",
            "_optionChangesOrder",
            "_layoutChangesOrder",
            "_customChangesOrder",
            "_totalChangesOrder"
        ].forEach(function(key) {
            proto[key] = {};
        });
        result.addPlugin = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
        return result;
    };
    widget.addChange = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
    widget.addPlugin = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
} : function(widget) {
    const _inherit = widget.inherit;
    widget.inherit = function() {
        let proto = this.prototype;
        const plugins = proto._plugins;
        const fontFields = proto._fontFields;
        const eventsMap = proto._eventsMap;
        const initialChanges = proto._initialChanges;
        const themeDependentChanges = proto._themeDependentChanges;
        const optionChangesMap = proto._optionChangesMap;
        const partialOptionChangesMap = proto._partialOptionChangesMap;
        const partialOptionChangesPath = proto._partialOptionChangesPath;
        const optionChangesOrder = proto._optionChangesOrder;
        const layoutChangesOrder = proto._layoutChangesOrder;
        const customChangesOrder = proto._customChangesOrder;
        const result = _inherit.apply(this, arguments);
        proto = result.prototype;
        proto._plugins = combineLists(plugins, proto._plugins);
        proto._fontFields = combineLists(fontFields, proto._fontFields);
        proto._eventsMap = combineMaps(eventsMap, proto._eventsMap);
        proto._initialChanges = combineLists(initialChanges, proto._initialChanges);
        proto._themeDependentChanges = combineLists(themeDependentChanges, proto._themeDependentChanges);
        proto._optionChangesMap = combineMaps(optionChangesMap, proto._optionChangesMap);
        proto._partialOptionChangesMap = combineMaps(partialOptionChangesMap, proto._partialOptionChangesMap);
        proto._partialOptionChangesPath = combineMaps(partialOptionChangesPath, proto._partialOptionChangesPath);
        proto._optionChangesOrder = combineLists(optionChangesOrder, proto._optionChangesOrder);
        proto._layoutChangesOrder = combineLists(layoutChangesOrder, proto._layoutChangesOrder);
        proto._customChangesOrder = combineLists(customChangesOrder, proto._customChangesOrder);
        buildTotalChanges(proto);
        result.addPlugin = addPlugin;
        return result;
    };
    widget.prototype._plugins = [];
    widget.prototype._fontFields = [];
    widget.addChange = addChange;
    widget.addPlugin = addPlugin;
};
function changes() {
    return new Flags;
}
}),
"[project]/node_modules/devextreme/esm/viz/core/layout.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/core/layout.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
;
const _min = Math.min;
const _max = Math.max;
const _round = Math.round;
const ALIGN_START = 0;
const ALIGN_MIDDLE = 1;
const ALIGN_END = 2;
const horizontalAlignmentMap = {
    left: 0,
    center: 1,
    right: 2
};
const verticalAlignmentMap = {
    top: 0,
    center: 1,
    bottom: 2
};
const sideMap = {
    horizontal: 0,
    vertical: 1
};
const slicersMap = {};
const BBOX_CEIL_CORRECTION = 2;
slicersMap[0] = function(a, b, size) {
    return [
        a,
        _min(b, a + size)
    ];
};
slicersMap[1] = function(a, b, size) {
    return [
        _max(a, (a + b - size) / 2),
        _min(b, (a + b + size) / 2)
    ];
};
slicersMap[2] = function(a, b, size) {
    return [
        _max(a, b - size),
        b
    ];
};
function pickValue(value, map, defaultValue) {
    const val = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(value);
    return val in map ? map[val] : defaultValue;
}
function normalizeLayoutOptions(options) {
    const side = pickValue(options.side, sideMap, 1);
    const alignment = [
        pickValue(options.horizontalAlignment, horizontalAlignmentMap, 1),
        pickValue(options.verticalAlignment, verticalAlignmentMap, 0)
    ];
    return {
        side: side,
        primary: bringToEdge(alignment[side]),
        secondary: alignment[1 - side],
        weak: options.weak,
        priority: options.priority || 0,
        header: options.header,
        position: options.position
    };
}
function bringToEdge(primary) {
    return primary < 2 ? 0 : 2;
}
function getConjugateSide(side) {
    return 1 - side;
}
function getSlice(alignment, a, b, size) {
    return slicersMap[alignment](a, b, size);
}
function getShrink(alignment, size) {
    return (alignment > 0 ? -1 : 1) * size;
}
function processForward(item, rect, minSize) {
    const side = item.side;
    const size = item.element.measure([
        rect[2] - rect[0],
        rect[3] - rect[1]
    ]);
    const minSide = "indside" === item.position ? 0 : minSize[side];
    const isValid = size[side] < rect[2 + side] - rect[side] - minSide;
    if (isValid) {
        if ("inside" !== item.position) {
            rect[item.primary + side] += getShrink(item.primary, size[side]);
        }
        item.size = size;
    }
    return isValid;
}
function processRectBackward(item, rect, alignmentRect) {
    const primarySide = item.side;
    const secondarySide = getConjugateSide(primarySide);
    const itemRect = [];
    const secondary = getSlice(item.secondary, alignmentRect[secondarySide], alignmentRect[2 + secondarySide], item.size[secondarySide]);
    itemRect[primarySide] = _round(itemRect[2 + primarySide] = rect[item.primary + primarySide] + ("inside" === item.position ? getShrink(item.primary, item.size[primarySide]) : 0));
    itemRect[item.primary + primarySide] = _round(rect[item.primary + primarySide] - getShrink(item.primary, item.size[primarySide]));
    if ("inside" !== item.position) {
        rect[item.primary + primarySide] = itemRect[item.primary + primarySide];
    }
    itemRect[secondarySide] = _round(secondary[0]);
    itemRect[2 + secondarySide] = _round(secondary[1]);
    return itemRect;
}
function processBackward(item, rect, alignmentRect, fitRect, size, targetRect) {
    const itemRect = processRectBackward(item, rect, alignmentRect);
    const itemFitRect = processRectBackward(item, fitRect, fitRect);
    if (size[item.side] > 0) {
        size[item.side] -= item.size[item.side];
        targetRect[item.primary + item.side] = itemRect[item.primary + item.side];
        item.element.freeSpace();
    } else {
        item.element.move(itemRect, itemFitRect);
    }
}
function Layout() {
    this._targets = [];
}
Layout.prototype = {
    constructor: Layout,
    dispose: function() {
        this._targets = null;
    },
    add: function(target) {
        this._targets.push(target);
    },
    forward: function(targetRect, minSize) {
        const rect = targetRect.slice();
        const targets = createTargets(this._targets);
        let i;
        const ii = targets.length;
        const cache = [];
        for(i = 0; i < ii; ++i){
            if (processForward(targets[i], rect, minSize)) {
                cache.push(targets[i]);
            } else {
                targets[i].element.freeSpace();
            }
        }
        this._cache = cache.reverse();
        return rect;
    },
    backward: function(targetRect, alignmentRect) {
        let size = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : [
            0,
            0
        ];
        let backwardRect = targetRect.slice();
        const fitRect = targetRect.slice();
        const targets = this._cache;
        let targetSide = 0;
        let target;
        let i;
        const ii = targets.length;
        for(i = 0; i < ii; ++i){
            target = targets[i];
            if (target.side !== targetSide) {
                backwardRect = targetRect.slice();
            }
            processBackward(target, backwardRect, alignmentRect, fitRect, size, targetRect);
            targetSide = target.side;
        }
        return size;
    }
};
function createTargets(targets) {
    let i;
    const ii = targets.length;
    let collection = [];
    let layout;
    for(i = 0; i < ii; ++i){
        layout = targets[i].layoutOptions();
        if (layout) {
            layout = normalizeLayoutOptions(layout);
            layout.element = targets[i];
            collection.push(layout);
        }
    }
    collection.sort(function(a, b) {
        return b.side - a.side || a.priority - b.priority;
    });
    collection = processWeakItems(collection);
    return collection;
}
function processWeakItems(collection) {
    const weakItem = collection.filter(function(item) {
        return true === item.weak;
    })[0];
    let headerItem;
    if (weakItem) {
        headerItem = collection.filter(function(item) {
            return weakItem.primary === item.primary && item.side === weakItem.side && item !== weakItem;
        })[0];
    }
    if (weakItem && headerItem) {
        return [
            makeHeader(headerItem, weakItem)
        ].concat(collection.filter(function(item) {
            return !(item === headerItem || item === weakItem);
        }));
    }
    return collection;
}
function processBackwardHeaderRect(element, rect) {
    const rectCopy = rect.slice();
    const itemRect = processRectBackward(element, rectCopy, rectCopy);
    itemRect[element.side] = rect[element.side];
    itemRect[2 + element.side] = rect[2 + element.side];
    return itemRect;
}
function makeHeader(header, weakElement) {
    const side = header.side;
    const primary = header.primary;
    const secondary = header.secondary;
    return {
        side: side,
        primary: primary,
        secondary: secondary,
        priority: 0,
        element: {
            measure: function(targetSize) {
                const result = targetSize.slice();
                const weakSize = weakElement.element.measure(targetSize.slice());
                targetSize[primary] -= weakSize[primary];
                const headerSize = header.element.measure(targetSize.slice());
                result[side] = weakSize[side] = headerSize[side] = Math.max(headerSize[side], weakSize[side]);
                weakElement.size = weakSize;
                header.size = headerSize;
                return result;
            },
            move: function(rect, fitRect) {
                if (fitRect[2] - fitRect[0] < header.size[0] + weakElement.size[0] - 2) {
                    this.freeSpace();
                    return;
                }
                const weakRect = processBackwardHeaderRect(weakElement, fitRect);
                fitRect[2 + weakElement.primary] = weakRect[weakElement.primary];
                const headerFitReact = processBackwardHeaderRect(header, fitRect);
                if (fitRect[2 + weakElement.primary] < rect[2 + weakElement.primary] && header.size[header.primary] > rect[2 + header.primary] - rect[header.primary]) {
                    rect[2 + weakElement.primary] = fitRect[2 + weakElement.primary];
                }
                let headerRect = processBackwardHeaderRect(header, rect);
                if (headerRect[2 + weakElement.primary] > fitRect[2 + weakElement.primary]) {
                    rect[2 + weakElement.primary] = fitRect[2 + weakElement.primary];
                    headerRect = processBackwardHeaderRect(header, rect);
                }
                weakElement.element.move(weakRect);
                header.element.move(headerRect, headerFitReact);
            },
            freeSpace: function() {
                header.element.freeSpace();
                weakElement.element.freeSpace();
            }
        }
    };
}
const __TURBOPACK__default__export__ = Layout;
}),

};

//# sourceMappingURL=node_modules_devextreme_esm_viz_core_3f01ae6a._.js.map