module.exports = {

"[project]/node_modules/devextreme/esm/core/utils/callbacks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/callbacks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_callbacks.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Callbacks"];
}),
"[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/iterator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/memorized_callbacks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/memorized_callbacks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_memorized_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_memorized_callbacks.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_memorized_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["MemorizedCallbacks"];
}),
"[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/type.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/extend.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/string.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/string.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_string.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/string.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_string$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_string.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$string$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/string.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/version.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/version.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "fullVersion": ()=>fullVersion,
    "version": ()=>version
});
const version = "25.1.3";
const fullVersion = "25.1.3";
}),
"[project]/node_modules/devextreme/esm/core/utils/error.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/error.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_error$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_error.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_error$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["error"];
}),
"[project]/node_modules/devextreme/esm/core/errors.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/errors.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_errors.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/core/class.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/class.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_class$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_class.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_class$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/core/templates/template_engine_registry.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/template_engine_registry.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template_engine_registry$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template_engine_registry.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/template_engine_registry.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template_engine_registry$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template_engine_registry.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$template_engine_registry$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/template_engine_registry.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/config.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/config.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/config.js [app-ssr] (ecmascript) <export default as config>");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__config$3e$__["config"];
}),
"[project]/node_modules/devextreme/esm/core/guid.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/guid.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$guid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Guid$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/guid.js [app-ssr] (ecmascript) <export default as Guid>");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$guid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Guid$3e$__["Guid"];
}),
"[project]/node_modules/devextreme/esm/core/utils/console.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/console.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_console.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/console.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_console.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/console.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/dependency_injector.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dependency_injector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dependency_injector.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dependency_injector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["injector"];
}),
"[project]/node_modules/devextreme/esm/core/utils/variable_wrapper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/variable_wrapper.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_variable_wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_variable_wrapper.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_variable_wrapper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["variableWrapper"];
}),
"[project]/node_modules/devextreme/esm/core/utils/object.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/object.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/object.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/object.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/data.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/data.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_data$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_data.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/data.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_data$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_data.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$data$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/data.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/deferred.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/common.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/shadow_dom.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/shadow_dom.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_shadow_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_shadow_dom.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/shadow_dom.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_shadow_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_shadow_dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$shadow_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/shadow_dom.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/dom_adapter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_dom_adapter.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["domAdapter"];
}),
"[project]/node_modules/devextreme/esm/core/utils/call_once.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/call_once.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_call_once$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_call_once.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_call_once$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["callOnce"];
}),
"[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/window.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/inflector.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/element_data.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/element_data.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/element_data.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element_data$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element_data.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/html_parser.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/html_parser.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_html_parser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_html_parser.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/html_parser.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_html_parser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_html_parser.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$html_parser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/html_parser.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/size.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/size.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/size.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/style.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/style.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_style.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/style.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_style.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$style$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/style.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/renderer_base.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/renderer_base.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_renderer_base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_renderer_base.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/renderer_base.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_renderer_base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_renderer_base.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer_base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer_base.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/renderer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/renderer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_renderer.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["renderer"];
}),
"[project]/node_modules/devextreme/esm/core/element.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/element.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/element.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/events_strategy.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/events_strategy.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_events_strategy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_events_strategy.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/events_strategy.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_events_strategy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_events_strategy.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$events_strategy$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/events_strategy.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/ready_callbacks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/ready_callbacks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_ready_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_ready_callbacks.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_ready_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["readyCallbacksModule"];
}),
"[project]/node_modules/devextreme/esm/core/utils/resize_callbacks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/resize_callbacks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_resize_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_resize_callbacks.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_resize_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resizeCallbacks"];
}),
"[project]/node_modules/devextreme/esm/core/utils/storage.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/storage.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_storage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_storage.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/storage.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_storage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_storage.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$storage$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/storage.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/view_port.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/view_port.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_view_port$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_view_port.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/view_port.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_view_port$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_view_port.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$view_port$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/view_port.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/position.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/position.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_position.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/position.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_position$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_position.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$position$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/position.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/browser.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/browser.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_browser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_browser.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_browser$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["browser"];
}),
"[project]/node_modules/devextreme/esm/core/utils/support.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/support.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-ssr] (ecmascript) <module evaluation>");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/support.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$support$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/support.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/date_serialization.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/date_serialization.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_date_serialization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_date_serialization.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_date_serialization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dateSerialization"];
}),
"[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/math.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/date.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_date.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dateUtils"];
}),
"[project]/node_modules/devextreme/esm/core/devices.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/devices.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$environment$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/environment.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__devices$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-ssr] (ecmascript) <export default as devices>");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__devices$3e$__["devices"];
}),
"[project]/node_modules/devextreme/esm/core/action.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/action.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_action.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_action$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Action"];
}),
"[project]/node_modules/devextreme/esm/core/options/utils.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/options/utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$options$2f$m_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/options/m_utils.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/options/utils.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$options$2f$m_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/options/m_utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$options$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/options/utils.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/comparator.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/comparator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_comparator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_comparator.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/comparator.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_comparator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_comparator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$comparator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/comparator.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/options/option_manager.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/options/option_manager.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$options$2f$m_option_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/options/m_option_manager.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/options/option_manager.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$options$2f$m_option_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/options/m_option_manager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$options$2f$option_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/options/option_manager.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/options/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/options/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$options$2f$m_index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/options/m_index.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/options/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$options$2f$m_index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/options/m_index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$options$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/options/index.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/postponed_operations.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/postponed_operations.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_postponed_operations$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_postponed_operations.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/postponed_operations.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_postponed_operations$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_postponed_operations.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$postponed_operations$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/postponed_operations.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/public_component.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/public_component.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_public_component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_public_component.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/public_component.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_public_component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_public_component.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$public_component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/public_component.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/component.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/component.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/widget/component.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/component.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/widget/component.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/component.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/dom.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/dom.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/dom.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dom.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/http_request.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/http_request.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_http_request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_http_request.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_http_request$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["httpRequest"];
}),
"[project]/node_modules/devextreme/esm/core/utils/ajax_utils.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/ajax_utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_ajax_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_ajax_utils.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/ajax_utils.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_ajax_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_ajax_utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ajax_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/ajax_utils.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/ajax.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/ajax.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_ajax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_ajax.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_ajax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Ajax"];
}),
"[project]/node_modules/devextreme/esm/core/utils/queue.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/queue.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_queue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_queue.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/queue.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_queue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_queue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$queue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/queue.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/component_registrator_callbacks.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/component_registrator_callbacks.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_component_registrator_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_component_registrator_callbacks.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_component_registrator_callbacks$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["componentRegistratorCallbacks"];
}),
"[project]/node_modules/devextreme/esm/core/component_registrator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/component_registrator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_component_registrator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_component_registrator.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_component_registrator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["registerComponent"];
}),
"[project]/node_modules/devextreme/esm/core/utils/version.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/version.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_version.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/version.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/version.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/templates/template_base.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/template_base.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template_base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template_base.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/template_base.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template_base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template_base.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$template_base$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/template_base.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/templates/empty_template.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/empty_template.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_empty_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_empty_template.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/empty_template.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_empty_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_empty_template.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$empty_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/empty_template.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/templates/function_template.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/function_template.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_function_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_function_template.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/function_template.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_function_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_function_template.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$function_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/function_template.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/templates/child_default_template.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/child_default_template.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_child_default_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_child_default_template.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/child_default_template.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_child_default_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_child_default_template.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$child_default_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/child_default_template.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/templates/template.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/template.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/template.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_template.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/template.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/array.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/array.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_array$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_array.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/array.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_array$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_array.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$array$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/array.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/template_manager.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/template_manager.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_template_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_template_manager.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/template_manager.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_template_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_template_manager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$template_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/template_manager.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/array_compare.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/array_compare.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_array_compare$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_array_compare.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/array_compare.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_array_compare$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_array_compare.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$array_compare$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/array_compare.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/templates/bindable_template.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/templates/bindable_template.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_bindable_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_bindable_template.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/templates/bindable_template.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$templates$2f$m_bindable_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/templates/m_bindable_template.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$templates$2f$bindable_template$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/templates/bindable_template.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/icon.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/icon.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_icon.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/icon.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_icon.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$icon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/icon.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/selection_filter.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/selection_filter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_selection_filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_selection_filter.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/selection_filter.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_selection_filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_selection_filter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$selection_filter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/selection_filter.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/resize_observer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/resize_observer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_resize_observer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_resize_observer.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_resize_observer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["resizeObserverSingleton"];
}),
"[project]/node_modules/devextreme/esm/core/dom_component.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/dom_component.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$dom_component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/widget/dom_component.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$dom_component$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/core/inferno_renderer.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/inferno_renderer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_inferno_renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_inferno_renderer.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_inferno_renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["infernoRenderer"];
}),
"[project]/node_modules/devextreme/esm/core/utils/stubs.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/stubs.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_stubs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_stubs.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/stubs.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_stubs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_stubs.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$stubs$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/stubs.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/core/utils/svg.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/core/utils/svg.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_svg.js [app-ssr] (ecmascript)");
;
}),
"[project]/node_modules/devextreme/esm/core/utils/svg.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_svg.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/svg.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/common.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/common.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/config.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$guid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/guid.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$set_template_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/set_template_engine.js [app-ssr] (ecmascript)");
;
;
;
;
}),
"[project]/node_modules/devextreme/esm/common.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$config$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/config.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$guid$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/guid.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$set_template_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/set_template_engine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/animation/frame.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/animation/frame.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation.js [app-ssr] (ecmascript) <module evaluation>");
;
}),
"[project]/node_modules/devextreme/esm/animation/frame.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$animation$2f$frame$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/animation/frame.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/data/abstract_store.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/data/abstract_store.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$abstract_store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/abstract_store.js [app-ssr] (ecmascript) <module evaluation>");
;
}),
"[project]/node_modules/devextreme/esm/data/abstract_store.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$data$2f$abstract_store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/data/abstract_store.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$data$2f$abstract_store$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/data/abstract_store.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/format_helper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/format_helper.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/number.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/date.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$currency$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization/currency.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
    format: function(value, format) {
        const formatIsValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(format) && "" !== format || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPlainObject"])(format) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(format);
        const valueIsValid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(value) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(value);
        if (!formatIsValid || !valueIsValid) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value) ? value.toString() : "";
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(format)) {
            return format(value);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(format)) {
            format = {
                type: format
            };
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(value)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format(value, format);
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(value)) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format(value, format);
        }
    },
    getTimeFormat: function(showSecond) {
        return showSecond ? "longtime" : "shorttime";
    },
    _normalizeFormat: function(format) {
        if (!Array.isArray(format)) {
            return format;
        }
        if (1 === format.length) {
            return format[0];
        }
        return function(date) {
            return format.map(function(formatPart) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format(date, formatPart);
            }).join(" ");
        };
    },
    getDateFormatByDifferences: function(dateDifferences, intervalFormat) {
        const resultFormat = [];
        const needSpecialSecondFormatter = intervalFormat && dateDifferences.millisecond && !(dateDifferences.year || dateDifferences.month || dateDifferences.day);
        if (needSpecialSecondFormatter) {
            const secondFormatter = function(date) {
                return date.getSeconds() + date.getMilliseconds() / 1e3 + "s";
            };
            resultFormat.push(secondFormatter);
        } else if (dateDifferences.millisecond) {
            resultFormat.push("millisecond");
        }
        if (dateDifferences.hour || dateDifferences.minute || !needSpecialSecondFormatter && dateDifferences.second) {
            resultFormat.unshift(this.getTimeFormat(dateDifferences.second));
        }
        if (dateDifferences.year && dateDifferences.month && dateDifferences.day) {
            if (intervalFormat && "month" === intervalFormat) {
                return "monthandyear";
            } else {
                resultFormat.unshift("shortdate");
                return this._normalizeFormat(resultFormat);
            }
        }
        if (dateDifferences.year && dateDifferences.month) {
            return "monthandyear";
        }
        if (dateDifferences.year && dateDifferences.quarter) {
            return "quarterandyear";
        }
        if (dateDifferences.year) {
            return "year";
        }
        if (dateDifferences.quarter) {
            return "quarter";
        }
        if (dateDifferences.month && dateDifferences.day) {
            if (intervalFormat) {
                const monthDayFormatter = function(date) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getMonthNames("abbreviated")[date.getMonth()] + " " + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format(date, "day");
                };
                resultFormat.unshift(monthDayFormatter);
            } else {
                resultFormat.unshift("monthandday");
            }
            return this._normalizeFormat(resultFormat);
        }
        if (dateDifferences.month) {
            return "month";
        }
        if (dateDifferences.day) {
            if (intervalFormat) {
                resultFormat.unshift("day");
            } else {
                const dayFormatter = function(date) {
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format(date, "dayofweek") + ", " + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].format(date, "day");
                };
                resultFormat.unshift(dayFormatter);
            }
            return this._normalizeFormat(resultFormat);
        }
        return this._normalizeFormat(resultFormat);
    },
    getDateFormatByTicks: function(ticks) {
        let maxDiff;
        let currentDiff;
        let i;
        if (ticks.length > 1) {
            maxDiff = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDatesDifferences(ticks[0], ticks[1]);
            for(i = 1; i < ticks.length - 1; i++){
                currentDiff = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDatesDifferences(ticks[i], ticks[i + 1]);
                if (maxDiff.count < currentDiff.count) {
                    maxDiff = currentDiff;
                }
            }
        } else {
            maxDiff = {
                year: true,
                month: true,
                day: true,
                hour: ticks[0].getHours() > 0,
                minute: ticks[0].getMinutes() > 0,
                second: ticks[0].getSeconds() > 0,
                millisecond: ticks[0].getMilliseconds() > 0
            };
        }
        const resultFormat = this.getDateFormatByDifferences(maxDiff);
        return resultFormat;
    },
    getDateFormatByTickInterval: function(startValue, endValue, tickInterval) {
        let dateUnitInterval;
        const correctDateDifferences = function(dateDifferences, tickInterval, value) {
            switch(tickInterval){
                case "year":
                case "quarter":
                    dateDifferences.month = value;
                case "month":
                    dateDifferences.day = value;
                case "week":
                case "day":
                    dateDifferences.hour = value;
                case "hour":
                    dateDifferences.minute = value;
                case "minute":
                    dateDifferences.second = value;
                case "second":
                    dateDifferences.millisecond = value;
            }
        };
        tickInterval = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(tickInterval) ? tickInterval.toLowerCase() : tickInterval;
        const dateDifferences = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDatesDifferences(startValue, endValue);
        if (startValue !== endValue) {
            !function(differences, minDate, maxDate) {
                if (!maxDate.getMilliseconds() && maxDate.getSeconds()) {
                    if (maxDate.getSeconds() - minDate.getSeconds() === 1) {
                        differences.millisecond = true;
                        differences.second = false;
                    }
                } else if (!maxDate.getSeconds() && maxDate.getMinutes()) {
                    if (maxDate.getMinutes() - minDate.getMinutes() === 1) {
                        differences.second = true;
                        differences.minute = false;
                    }
                } else if (!maxDate.getMinutes() && maxDate.getHours()) {
                    if (maxDate.getHours() - minDate.getHours() === 1) {
                        differences.minute = true;
                        differences.hour = false;
                    }
                } else if (!maxDate.getHours() && maxDate.getDate() > 1) {
                    if (maxDate.getDate() - minDate.getDate() === 1) {
                        differences.hour = true;
                        differences.day = false;
                    }
                } else if (1 === maxDate.getDate() && maxDate.getMonth()) {
                    if (maxDate.getMonth() - minDate.getMonth() === 1) {
                        differences.day = true;
                        differences.month = false;
                    }
                } else if (!maxDate.getMonth() && maxDate.getFullYear()) {
                    if (maxDate.getFullYear() - minDate.getFullYear() === 1) {
                        differences.month = true;
                        differences.year = false;
                    }
                }
            }(dateDifferences, startValue > endValue ? endValue : startValue, startValue > endValue ? startValue : endValue);
        }
        dateUnitInterval = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDateUnitInterval(dateDifferences);
        correctDateDifferences(dateDifferences, dateUnitInterval, true);
        dateUnitInterval = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDateUnitInterval(tickInterval || "second");
        correctDateDifferences(dateDifferences, dateUnitInterval, false);
        dateDifferences[({
            week: "day"
        })[dateUnitInterval] || dateUnitInterval] = true;
        const resultFormat = this.getDateFormatByDifferences(dateDifferences);
        return resultFormat;
    }
});
}),
"[project]/node_modules/devextreme/esm/data_helper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/data_helper.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_data_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/m_data_helper.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$m_data_helper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["DataHelperMixin"];
}),
"[project]/node_modules/devextreme/esm/data_controller.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/data_controller.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_controller$2f$data_controller$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/data/data_controller/data_controller.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$data$2f$data_controller$2f$data_controller$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}),
"[project]/node_modules/devextreme/esm/events/events.types.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/events/events.types.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "triggerHandler": ()=>triggerHandler
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-ssr] (ecmascript)");
;
const triggerHandler = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].triggerHandler;
}),
"[project]/node_modules/devextreme/esm/events/index.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/events/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$events$2e$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/events.types.js [app-ssr] (ecmascript)");
;
;
}),
"[project]/node_modules/devextreme/esm/events/index.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$events$2e$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/events.types.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/index.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/color.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/color.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const standardColorNames = {
    aliceblue: "f0f8ff",
    antiquewhite: "faebd7",
    aqua: "00ffff",
    aquamarine: "7fffd4",
    azure: "f0ffff",
    beige: "f5f5dc",
    bisque: "ffe4c4",
    black: "000000",
    blanchedalmond: "ffebcd",
    blue: "0000ff",
    blueviolet: "8a2be2",
    brown: "a52a2a",
    burlywood: "deb887",
    cadetblue: "5f9ea0",
    chartreuse: "7fff00",
    chocolate: "d2691e",
    coral: "ff7f50",
    cornflowerblue: "6495ed",
    cornsilk: "fff8dc",
    crimson: "dc143c",
    cyan: "00ffff",
    darkblue: "00008b",
    darkcyan: "008b8b",
    darkgoldenrod: "b8860b",
    darkgray: "a9a9a9",
    darkgreen: "006400",
    darkgrey: "a9a9a9",
    darkkhaki: "bdb76b",
    darkmagenta: "8b008b",
    darkolivegreen: "556b2f",
    darkorange: "ff8c00",
    darkorchid: "9932cc",
    darkred: "8b0000",
    darksalmon: "e9967a",
    darkseagreen: "8fbc8f",
    darkslateblue: "483d8b",
    darkslategray: "2f4f4f",
    darkslategrey: "2f4f4f",
    darkturquoise: "00ced1",
    darkviolet: "9400d3",
    deeppink: "ff1493",
    deepskyblue: "00bfff",
    dimgray: "696969",
    dimgrey: "696969",
    dodgerblue: "1e90ff",
    feldspar: "d19275",
    firebrick: "b22222",
    floralwhite: "fffaf0",
    forestgreen: "228b22",
    fuchsia: "ff00ff",
    gainsboro: "dcdcdc",
    ghostwhite: "f8f8ff",
    gold: "ffd700",
    goldenrod: "daa520",
    gray: "808080",
    green: "008000",
    greenyellow: "adff2f",
    grey: "808080",
    honeydew: "f0fff0",
    hotpink: "ff69b4",
    indianred: "cd5c5c",
    indigo: "4b0082",
    ivory: "fffff0",
    khaki: "f0e68c",
    lavender: "e6e6fa",
    lavenderblush: "fff0f5",
    lawngreen: "7cfc00",
    lemonchiffon: "fffacd",
    lightblue: "add8e6",
    lightcoral: "f08080",
    lightcyan: "e0ffff",
    lightgoldenrodyellow: "fafad2",
    lightgray: "d3d3d3",
    lightgreen: "90ee90",
    lightgrey: "d3d3d3",
    lightpink: "ffb6c1",
    lightsalmon: "ffa07a",
    lightseagreen: "20b2aa",
    lightskyblue: "87cefa",
    lightslateblue: "8470ff",
    lightslategray: "778899",
    lightslategrey: "778899",
    lightsteelblue: "b0c4de",
    lightyellow: "ffffe0",
    lime: "00ff00",
    limegreen: "32cd32",
    linen: "faf0e6",
    magenta: "ff00ff",
    maroon: "800000",
    mediumaquamarine: "66cdaa",
    mediumblue: "0000cd",
    mediumorchid: "ba55d3",
    mediumpurple: "9370d8",
    mediumseagreen: "3cb371",
    mediumslateblue: "7b68ee",
    mediumspringgreen: "00fa9a",
    mediumturquoise: "48d1cc",
    mediumvioletred: "c71585",
    midnightblue: "191970",
    mintcream: "f5fffa",
    mistyrose: "ffe4e1",
    moccasin: "ffe4b5",
    navajowhite: "ffdead",
    navy: "000080",
    oldlace: "fdf5e6",
    olive: "808000",
    olivedrab: "6b8e23",
    orange: "ffa500",
    orangered: "ff4500",
    orchid: "da70d6",
    palegoldenrod: "eee8aa",
    palegreen: "98fb98",
    paleturquoise: "afeeee",
    palevioletred: "d87093",
    papayawhip: "ffefd5",
    peachpuff: "ffdab9",
    peru: "cd853f",
    pink: "ffc0cb",
    plum: "dda0dd",
    powderblue: "b0e0e6",
    purple: "800080",
    rebeccapurple: "663399",
    red: "ff0000",
    rosybrown: "bc8f8f",
    royalblue: "4169e1",
    saddlebrown: "8b4513",
    salmon: "fa8072",
    sandybrown: "f4a460",
    seagreen: "2e8b57",
    seashell: "fff5ee",
    sienna: "a0522d",
    silver: "c0c0c0",
    skyblue: "87ceeb",
    slateblue: "6a5acd",
    slategray: "708090",
    slategrey: "708090",
    snow: "fffafa",
    springgreen: "00ff7f",
    steelblue: "4682b4",
    tan: "d2b48c",
    teal: "008080",
    thistle: "d8bfd8",
    tomato: "ff6347",
    turquoise: "40e0d0",
    violet: "ee82ee",
    violetred: "d02090",
    wheat: "f5deb3",
    white: "ffffff",
    whitesmoke: "f5f5f5",
    yellow: "ffff00",
    yellowgreen: "9acd32"
};
const standardColorTypes = [
    {
        re: /^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,
        process: function(colorString) {
            return [
                parseInt(colorString[1], 10),
                parseInt(colorString[2], 10),
                parseInt(colorString[3], 10)
            ];
        }
    },
    {
        re: /^rgba\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3}),\s*(\d*\.*\d+)\)$/,
        process: function(colorString) {
            return [
                parseInt(colorString[1], 10),
                parseInt(colorString[2], 10),
                parseInt(colorString[3], 10),
                parseFloat(colorString[4])
            ];
        }
    },
    {
        re: /^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/,
        process: function(colorString) {
            return [
                parseInt(colorString[1], 16),
                parseInt(colorString[2], 16),
                parseInt(colorString[3], 16)
            ];
        }
    },
    {
        re: /^#([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})([a-f0-9]{2})$/,
        process: function(colorString) {
            return [
                parseInt(colorString[1], 16),
                parseInt(colorString[2], 16),
                parseInt(colorString[3], 16),
                Number((parseInt(colorString[4], 16) / 255).toFixed(2))
            ];
        }
    },
    {
        re: /^#([a-f0-9]{1})([a-f0-9]{1})([a-f0-9]{1})([a-f0-9]{1})$/,
        process: function(colorString) {
            return [
                parseInt(colorString[1] + colorString[1], 16),
                parseInt(colorString[2] + colorString[2], 16),
                parseInt(colorString[3] + colorString[3], 16),
                Number((parseInt(colorString[4] + colorString[4], 16) / 255).toFixed(2))
            ];
        }
    },
    {
        re: /^#([a-f0-9]{1})([a-f0-9]{1})([a-f0-9]{1})$/,
        process: function(colorString) {
            return [
                parseInt(colorString[1] + colorString[1], 16),
                parseInt(colorString[2] + colorString[2], 16),
                parseInt(colorString[3] + colorString[3], 16)
            ];
        }
    },
    {
        re: /^hsv\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,
        process: function(colorString) {
            const h = parseInt(colorString[1], 10);
            const s = parseInt(colorString[2], 10);
            const v = parseInt(colorString[3], 10);
            const rgb = hsvToRgb(h, s, v);
            return [
                rgb[0],
                rgb[1],
                rgb[2],
                1,
                [
                    h,
                    s,
                    v
                ]
            ];
        }
    },
    {
        re: /^hsl\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,
        process: function(colorString) {
            const h = parseInt(colorString[1], 10);
            const s = parseInt(colorString[2], 10);
            const l = parseInt(colorString[3], 10);
            const rgb = hslToRgb(h, s, l);
            return [
                rgb[0],
                rgb[1],
                rgb[2],
                1,
                null,
                [
                    h,
                    s,
                    l
                ]
            ];
        }
    }
];
const _round = Math.round;
function Color(value) {
    this.baseColor = value;
    let color;
    if (value) {
        color = String(value).toLowerCase().replace(/ /g, "");
        color = standardColorNames[color] ? "#" + standardColorNames[color] : color;
        color = parseColor(color);
    }
    if (!color) {
        this.colorIsInvalid = true;
    }
    color = color || {};
    this.r = normalize(color[0]);
    this.g = normalize(color[1]);
    this.b = normalize(color[2]);
    this.a = normalize(color[3], 1, 1);
    if (color[4]) {
        this.hsv = {
            h: color[4][0],
            s: color[4][1],
            v: color[4][2]
        };
    } else {
        this.hsv = toHsvFromRgb(this.r, this.g, this.b);
    }
    if (color[5]) {
        this.hsl = {
            h: color[5][0],
            s: color[5][1],
            l: color[5][2]
        };
    } else {
        this.hsl = toHslFromRgb(this.r, this.g, this.b);
    }
}
function parseColor(color) {
    if ("transparent" === color) {
        return [
            0,
            0,
            0,
            0
        ];
    }
    let i = 0;
    const ii = standardColorTypes.length;
    let str;
    for(; i < ii; ++i){
        str = standardColorTypes[i].re.exec(color);
        if (str) {
            return standardColorTypes[i].process(str);
        }
    }
    return null;
}
function normalize(colorComponent, def, max) {
    def = def || 0;
    max = max || 255;
    return colorComponent < 0 || isNaN(colorComponent) ? def : colorComponent > max ? max : colorComponent;
}
function toHexFromRgb(r, g, b) {
    return "#" + (16777216 | r << 16 | g << 8 | b).toString(16).slice(1);
}
function toHsvFromRgb(r, g, b) {
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const delta = max - min;
    let H;
    let S;
    let V = max;
    S = 0 === max ? 0 : 1 - min / max;
    if (max === min) {
        H = 0;
    } else {
        switch(max){
            case r:
                H = (g - b) / delta * 60;
                if (g < b) {
                    H += 360;
                }
                break;
            case g:
                H = (b - r) / delta * 60 + 120;
                break;
            case b:
                H = (r - g) / delta * 60 + 240;
        }
    }
    S *= 100;
    V *= 100 / 255;
    return {
        h: Math.round(H),
        s: Math.round(S),
        v: Math.round(V)
    };
}
function hsvToRgb(h, s, v) {
    const index = Math.floor(h % 360 / 60);
    const vMin = (100 - s) * v / 100;
    const a = h % 60 / 60 * (v - vMin);
    const vInc = vMin + a;
    const vDec = v - a;
    let r;
    let g;
    let b;
    switch(index){
        case 0:
            r = v;
            g = vInc;
            b = vMin;
            break;
        case 1:
            r = vDec;
            g = v;
            b = vMin;
            break;
        case 2:
            r = vMin;
            g = v;
            b = vInc;
            break;
        case 3:
            r = vMin;
            g = vDec;
            b = v;
            break;
        case 4:
            r = vInc;
            g = vMin;
            b = v;
            break;
        case 5:
            r = v;
            g = vMin;
            b = vDec;
    }
    return [
        Math.round(2.55 * r),
        Math.round(2.55 * g),
        Math.round(2.55 * b)
    ];
}
function calculateHue(r, g, b, delta) {
    const max = Math.max(r, g, b);
    switch(max){
        case r:
            return (g - b) / delta + (g < b ? 6 : 0);
        case g:
            return (b - r) / delta + 2;
        case b:
            return (r - g) / delta + 4;
    }
}
function toHslFromRgb(r, g, b) {
    r = convertTo01Bounds(r, 255);
    g = convertTo01Bounds(g, 255);
    b = convertTo01Bounds(b, 255);
    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    const maxMinSum = max + min;
    let h;
    let s;
    const l = maxMinSum / 2;
    if (max === min) {
        h = s = 0;
    } else {
        const delta = max - min;
        if (l > .5) {
            s = delta / (2 - maxMinSum);
        } else {
            s = delta / maxMinSum;
        }
        h = calculateHue(r, g, b, delta);
        h /= 6;
    }
    return {
        h: _round(360 * h),
        s: _round(100 * s),
        l: _round(100 * l)
    };
}
function makeColorTint(colorPart, h) {
    let colorTint = h;
    if ("r" === colorPart) {
        colorTint = h + 1 / 3;
    }
    if ("b" === colorPart) {
        colorTint = h - 1 / 3;
    }
    return colorTint;
}
function modifyColorTint(colorTint) {
    if (colorTint < 0) {
        colorTint += 1;
    }
    if (colorTint > 1) {
        colorTint -= 1;
    }
    return colorTint;
}
function hueToRgb(p, q, colorTint) {
    colorTint = modifyColorTint(colorTint);
    if (colorTint < 1 / 6) {
        return p + 6 * (q - p) * colorTint;
    }
    if (colorTint < .5) {
        return q;
    }
    if (colorTint < 2 / 3) {
        return p + (q - p) * (2 / 3 - colorTint) * 6;
    }
    return p;
}
function hslToRgb(h, s, l) {
    let r;
    let g;
    let b;
    h = convertTo01Bounds(h, 360);
    s = convertTo01Bounds(s, 100);
    l = convertTo01Bounds(l, 100);
    if (0 === s) {
        r = g = b = l;
    } else {
        const q = l < .5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        r = hueToRgb(p, q, makeColorTint("r", h));
        g = hueToRgb(p, q, makeColorTint("g", h));
        b = hueToRgb(p, q, makeColorTint("b", h));
    }
    return [
        _round(255 * r),
        _round(255 * g),
        _round(255 * b)
    ];
}
function convertTo01Bounds(n, max) {
    n = Math.min(max, Math.max(0, parseFloat(n)));
    if (Math.abs(n - max) < 1e-6) {
        return 1;
    }
    return n % max / parseFloat(max);
}
function isIntegerBetweenMinAndMax(number, min, max) {
    min = min || 0;
    max = max || 255;
    if (number % 1 !== 0 || number < min || number > max || "number" !== typeof number || isNaN(number)) {
        return false;
    }
    return true;
}
Color.prototype = {
    constructor: Color,
    highlight: function(step) {
        step = step || 10;
        return this.alter(step).toHex();
    },
    darken: function(step) {
        step = step || 10;
        return this.alter(-step).toHex();
    },
    alter: function(step) {
        const result = new Color;
        result.r = normalize(this.r + step);
        result.g = normalize(this.g + step);
        result.b = normalize(this.b + step);
        return result;
    },
    blend: function(blendColor, opacity) {
        const other = blendColor instanceof Color ? blendColor : new Color(blendColor);
        const result = new Color;
        result.r = normalize(_round(this.r * (1 - opacity) + other.r * opacity));
        result.g = normalize(_round(this.g * (1 - opacity) + other.g * opacity));
        result.b = normalize(_round(this.b * (1 - opacity) + other.b * opacity));
        return result;
    },
    toHex: function() {
        return toHexFromRgb(this.r, this.g, this.b);
    },
    getPureColor: function() {
        const rgb = hsvToRgb(this.hsv.h, 100, 100);
        return new Color("rgb(" + rgb.join(",") + ")");
    },
    isValidHex: function(hex) {
        return /(^#[0-9A-F]{6}$)|(^#[0-9A-F]{3}$)/i.test(hex);
    },
    isValidRGB: function(r, g, b) {
        if (!isIntegerBetweenMinAndMax(r) || !isIntegerBetweenMinAndMax(g) || !isIntegerBetweenMinAndMax(b)) {
            return false;
        }
        return true;
    },
    isValidAlpha: function(a) {
        if (isNaN(a) || a < 0 || a > 1 || "number" !== typeof a) {
            return false;
        }
        return true;
    },
    colorIsInvalid: false,
    fromHSL: function(hsl) {
        const color = new Color;
        const rgb = hslToRgb(hsl.h, hsl.s, hsl.l);
        color.r = rgb[0];
        color.g = rgb[1];
        color.b = rgb[2];
        return color;
    }
};
const __TURBOPACK__default__export__ = Color;
}),
"[project]/node_modules/devextreme/esm/exporter/file_saver.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/exporter/file_saver.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "MIME_TYPES": ()=>MIME_TYPES,
    "fileSaver": ()=>fileSaver
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/widget/ui.errors.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/console.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_console.js [app-ssr] (ecmascript)");
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])();
const navigator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getNavigator"])();
const FILE_EXTESIONS = {
    EXCEL: "xlsx",
    CSS: "css",
    PNG: "png",
    JPEG: "jpeg",
    GIF: "gif",
    SVG: "svg",
    PDF: "pdf"
};
const MIME_TYPES = {
    CSS: "text/css",
    EXCEL: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    PNG: "image/png",
    JPEG: "image/jpeg",
    GIF: "image/gif",
    SVG: "image/svg+xml",
    PDF: "application/pdf"
};
const fileSaver = {
    _revokeObjectURLTimeout: 3e4,
    _getDataUri: function(format, data) {
        const mimeType = this._getMimeType(format);
        return `data:${mimeType};base64,${data}`;
    },
    _getMimeType: function(format) {
        return MIME_TYPES[format] || "application/octet-stream";
    },
    _linkDownloader: function(fileName, href) {
        const exportLinkElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("a");
        exportLinkElement.download = fileName;
        exportLinkElement.href = href;
        exportLinkElement.target = "_blank";
        return exportLinkElement;
    },
    _winJSBlobSave: function(blob, fileName, format) {
        const savePicker = new Windows.Storage.Pickers.FileSavePicker;
        savePicker.suggestedStartLocation = Windows.Storage.Pickers.PickerLocationId.documentsLibrary;
        const fileExtension = FILE_EXTESIONS[format];
        if (fileExtension) {
            const mimeType = this._getMimeType(format);
            savePicker.fileTypeChoices.insert(mimeType, [
                "." + fileExtension
            ]);
        }
        savePicker.suggestedFileName = fileName;
        savePicker.pickSaveFileAsync().then(function(file) {
            if (file) {
                file.openAsync(Windows.Storage.FileAccessMode.readWrite).then(function(outputStream) {
                    const inputStream = blob.msDetachStream();
                    Windows.Storage.Streams.RandomAccessStream.copyAsync(inputStream, outputStream).then(function() {
                        outputStream.flushAsync().done(function() {
                            inputStream.close();
                            outputStream.close();
                        });
                    });
                });
            }
        });
    },
    _click: function(link) {
        try {
            link.dispatchEvent(new MouseEvent("click", {
                cancelable: true
            }));
        } catch (e) {
            const event = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getDocument().createEvent("MouseEvents");
            event.initMouseEvent("click", true, true, window, 0, 0, 0, 80, 20, false, false, false, false, 0, null);
            link.dispatchEvent(event);
        }
    },
    _saveBlobAs: function(fileName, format, data) {
        this._blobSaved = false;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(navigator.msSaveOrOpenBlob)) {
            navigator.msSaveOrOpenBlob(data, fileName);
            this._blobSaved = true;
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(window.WinJS)) {
            this._winJSBlobSave(data, fileName, format);
            this._blobSaved = true;
        } else {
            const URL = window.URL || window.webkitURL || window.mozURL || window.msURL || window.oURL;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(URL)) {
                const objectURL = URL.createObjectURL(data);
                const downloadLink = this._linkDownloader(fileName, objectURL);
                setTimeout(()=>{
                    URL.revokeObjectURL(objectURL);
                    this._objectUrlRevoked = true;
                }, this._revokeObjectURLTimeout);
                this._click(downloadLink);
            } else {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_console$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["logger"].warn("window.URL || window.webkitURL || window.mozURL || window.msURL || window.oURL is not defined");
            }
        }
    },
    saveAs: function(fileName, format, data) {
        const fileExtension = FILE_EXTESIONS[format];
        if (fileExtension) {
            fileName += "." + fileExtension;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(window.Blob)) {
            this._saveBlobAs(fileName, format, data);
        } else {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(navigator.userAgent.match(/iPad/i))) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$ui$2e$errors$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].log("E1034");
            }
            const downloadLink = this._linkDownloader(fileName, this._getDataUri(format, data));
            this._click(downloadLink);
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/exporter/image_creator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/exporter/image_creator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "calcScaledInfo": ()=>calcScaledInfo,
    "getData": ()=>getData,
    "imageCreator": ()=>imageCreator,
    "testFormats": ()=>testFormats
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/color.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_svg.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dom.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/inflector.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_inflector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])();
;
;
const _math = Math;
const PI = _math.PI;
const _min = _math.min;
const _abs = _math.abs;
const _sqrt = _math.sqrt;
const _pow = _math.pow;
const _atan2 = _math.atan2;
const _cos = _math.cos;
const _sin = _math.sin;
const _number = Number;
const IMAGE_QUALITY = 1;
const TEXT_DECORATION_LINE_WIDTH_COEFF = .05;
const DEFAULT_FONT_SIZE = "10px";
const DEFAULT_FONT_FAMILY = "sans-serif";
const DEFAULT_TEXT_COLOR = "#000";
let parseAttributes;
function getStringFromCanvas(canvas, mimeType) {
    const dataURL = canvas.toDataURL(mimeType, 1);
    const imageData = window.atob(dataURL.substring(("data:" + mimeType + ";base64,").length));
    return imageData;
}
function arcTo(x1, y1, x2, y2, radius, largeArcFlag, clockwise, context) {
    const cBx = (x1 + x2) / 2;
    const cBy = (y1 + y2) / 2;
    let aB = _atan2(y1 - y2, x1 - x2);
    const k = largeArcFlag ? 1 : -1;
    aB += PI / 180 * 90 * (clockwise ? 1 : -1);
    const opSide = _sqrt(_pow(x2 - x1, 2) + _pow(y2 - y1, 2)) / 2;
    const adjSide = _sqrt(_abs(_pow(radius, 2) - _pow(opSide, 2)));
    const centerX = cBx + k * (adjSide * _cos(aB));
    const centerY = cBy + k * (adjSide * _sin(aB));
    const startAngle = _atan2(y1 - centerY, x1 - centerX);
    const endAngle = _atan2(y2 - centerY, x2 - centerX);
    context.arc(centerX, centerY, radius, startAngle, endAngle, !clockwise);
}
function getElementOptions(element, rootAppended) {
    const attr = parseAttributes(element.attributes || {});
    const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, attr, {
        text: element.textContent.replace(/\s+/g, " "),
        textAlign: "middle" === attr["text-anchor"] ? "center" : attr["text-anchor"]
    });
    const transform = attr.transform;
    let coords;
    if (transform) {
        coords = transform.match(/translate\(-*\d+([.]\d+)*(,*\s*-*\d+([.]\d+)*)*/);
        if (coords) {
            coords = coords[0].match(/-*\d+([.]\d+)*/g);
            options.translateX = _number(coords[0]);
            options.translateY = coords[1] ? _number(coords[1]) : 0;
        }
        coords = transform.match(/rotate\(-*\d+([.]\d+)*(,*\s*-*\d+([.]\d+)*,*\s*-*\d+([.]\d+)*)*/);
        if (coords) {
            coords = coords[0].match(/-*\d+([.]\d+)*/g);
            options.rotationAngle = _number(coords[0]);
            options.rotationX = coords[1] && _number(coords[1]);
            options.rotationY = coords[2] && _number(coords[2]);
        }
        coords = transform.match(/scale\(-*\d+([.]\d+)*(,*\s*-*\d+([.]\d+)*)*/);
        if (coords) {
            coords = coords[0].match(/-*\d+([.]\d+)*/g);
            options.scaleX = _number(coords[0]);
            if (coords.length > 1) {
                options.scaleY = _number(coords[1]);
            } else {
                options.scaleY = options.scaleX;
            }
        }
    }
    parseStyles(element, options, rootAppended);
    return options;
}
function drawRect(context, options) {
    const x = options.x;
    const y = options.y;
    const width = options.width;
    const height = options.height;
    let cornerRadius = options.rx;
    if (!cornerRadius) {
        context.rect(x, y, width, height);
    } else {
        cornerRadius = _min(cornerRadius, width / 2, height / 2);
        context.save();
        context.translate(x, y);
        context.moveTo(width / 2, 0);
        context.arcTo(width, 0, width, height, cornerRadius);
        context.arcTo(width, height, 0, height, cornerRadius);
        context.arcTo(0, height, 0, 0, cornerRadius);
        context.arcTo(0, 0, cornerRadius, 0, cornerRadius);
        context.lineTo(width / 2, 0);
        context.restore();
    }
}
function drawImage(context, options, shared) {
    const d = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"];
    const image = new window.Image;
    image.onload = function() {
        context.save();
        context.globalAlpha = options.globalAlpha;
        transformElement(context, options);
        clipElement(context, options, shared);
        context.drawImage(image, options.x || 0, options.y || 0, options.width, options.height);
        context.restore();
        d.resolve();
    };
    image.onerror = function() {
        d.resolve();
    };
    image.setAttribute("crossOrigin", "anonymous");
    image.src = options.href || options["xlink:href"];
    return d;
}
function drawPath(context, dAttr) {
    const dArray = dAttr.replace(/,/g, " ").split(/([A-Z])/i).filter((item)=>"" !== item.trim());
    let i = 0;
    let params;
    let prevParams;
    let prevParamsLen;
    do {
        params = (dArray[i + 1] || "").trim().split(" ");
        switch(dArray[i]){
            case "M":
                context.moveTo(_number(params[0]), _number(params[1]));
                i += 2;
                break;
            case "L":
                for(let j = 0; j < params.length / 2; j++){
                    context.lineTo(_number(params[2 * j]), _number(params[2 * j + 1]));
                }
                i += 2;
                break;
            case "C":
                context.bezierCurveTo(_number(params[0]), _number(params[1]), _number(params[2]), _number(params[3]), _number(params[4]), _number(params[5]));
                i += 2;
                break;
            case "a":
                prevParams = dArray[i - 1].trim().split(" ");
                prevParamsLen = prevParams.length - 1;
                arcTo(_number(prevParams[prevParamsLen - 1]), _number(prevParams[prevParamsLen]), _number(prevParams[prevParamsLen - 1]) + _number(params[5]), _number(prevParams[prevParamsLen]) + _number(params[6]), _number(params[0]), _number(params[3]), _number(params[4]), context);
                i += 2;
                break;
            case "A":
                prevParams = dArray[i - 1].trim().split(" ");
                prevParamsLen = prevParams.length - 1;
                arcTo(_number(prevParams[prevParamsLen - 1]), _number(prevParams[prevParamsLen]), _number(params[5]), _number(params[6]), _number(params[0]), _number(params[3]), _number(params[4]), context);
                i += 2;
                break;
            case "Z":
                context.closePath();
                i += 1;
                break;
            default:
                i++;
        }
    }while (i < dArray.length)
}
function parseStyles(element, options, rootAppended) {
    let style = element.style || {};
    let field;
    for(field in style){
        if ("" !== style[field]) {
            options[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["camelize"])(field)] = style[field];
        }
    }
    if (rootAppended && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isElementNode(element)) {
        style = window.getComputedStyle(element);
        [
            "fill",
            "stroke",
            "stroke-width",
            "font-family",
            "font-size",
            "font-style",
            "font-weight"
        ].forEach(function(prop) {
            if (prop in style && "" !== style[prop]) {
                options[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_inflector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["camelize"])(prop)] = style[prop];
            }
        });
        [
            "opacity",
            "fill-opacity",
            "stroke-opacity"
        ].forEach(function(prop) {
            if (prop in style && "" !== style[prop] && "1" !== style[prop]) {
                options[prop] = _number(style[prop]);
            }
        });
    }
    options.textDecoration = options.textDecoration || options.textDecorationLine;
    options.globalAlpha = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(options.opacity) ? options.opacity : options.globalAlpha;
}
function parseUrl(urlString) {
    const matches = urlString && urlString.match(/url\(.*#(.*?)["']?\)/i);
    return matches && matches[1];
}
function setFontStyle(context, options) {
    const fontParams = [];
    options.fontSize = options.fontSize || "10px";
    options.fontFamily = options.fontFamily || "sans-serif";
    options.fill = options.fill || "#000";
    options.fontStyle && fontParams.push(options.fontStyle);
    options.fontWeight && fontParams.push(options.fontWeight);
    fontParams.push(options.fontSize);
    fontParams.push(options.fontFamily);
    context.font = fontParams.join(" ");
    context.textAlign = options.textAlign;
    context.fillStyle = options.fill;
    context.globalAlpha = options.globalAlpha;
}
function drawText(context, options, shared) {
    setFontStyle(context, options);
    applyFilter(context, options, shared);
    options.text && context.fillText(options.text, options.x || 0, options.y || 0);
    strokeElement(context, options, true);
    drawTextDecoration(context, options, shared);
}
function drawTextDecoration(context, options, shared) {
    if (!options.textDecoration || "none" === options.textDecoration) {
        return;
    }
    const x = options.x;
    const textWidth = context.measureText(options.text).width;
    const textHeight = parseInt(options.fontSize, 10);
    const lineHeight = .05 * textHeight < 1 ? 1 : .05 * textHeight;
    let y = options.y;
    switch(options.textDecoration){
        case "line-through":
            y -= textHeight / 3 + lineHeight / 2;
            break;
        case "overline":
            y -= textHeight - lineHeight;
            break;
        case "underline":
            y += lineHeight;
    }
    context.rect(x, y, textWidth, lineHeight);
    fillElement(context, options, shared);
    strokeElement(context, options);
}
function aggregateOpacity(options) {
    options.strokeOpacity = void 0 !== options["stroke-opacity"] ? options["stroke-opacity"] : 1;
    options.fillOpacity = void 0 !== options["fill-opacity"] ? options["fill-opacity"] : 1;
    if (void 0 !== options.opacity) {
        options.strokeOpacity *= options.opacity;
        options.fillOpacity *= options.opacity;
    }
}
function hasTspan(element) {
    const nodes = element.childNodes;
    for(let i = 0; i < nodes.length; i++){
        if ("tspan" === nodes[i].tagName) {
            return true;
        }
    }
    return false;
}
function drawTextElement(childNodes, context, options, shared) {
    const lines = [];
    let line;
    let offset = 0;
    for(let i = 0; i < childNodes.length; i++){
        const element = childNodes[i];
        if (void 0 === element.tagName) {
            drawElement(element, context, options, shared);
        } else if ("tspan" === element.tagName || "text" === element.tagName) {
            const elementOptions = getElementOptions(element, shared.rootAppended);
            const mergedOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, options, elementOptions);
            if ("tspan" === element.tagName && hasTspan(element)) {
                drawTextElement(element.childNodes, context, mergedOptions, shared);
                continue;
            }
            mergedOptions.textAlign = "start";
            if (!line || void 0 !== elementOptions.x) {
                line = {
                    elements: [],
                    options: [],
                    widths: [],
                    offsets: []
                };
                lines.push(line);
            }
            if (void 0 !== elementOptions.y) {
                offset = 0;
            }
            if (void 0 !== elementOptions.dy) {
                offset += parseFloat(elementOptions.dy);
            }
            line.elements.push(element);
            line.options.push(mergedOptions);
            line.offsets.push(offset);
            setFontStyle(context, mergedOptions);
            line.widths.push(context.measureText(mergedOptions.text).width);
        }
    }
    lines.forEach(function(line) {
        const commonWidth = line.widths.reduce(function(commonWidth, width) {
            return commonWidth + width;
        }, 0);
        let xDiff = 0;
        let currentOffset = 0;
        if ("center" === options.textAlign) {
            xDiff = commonWidth / 2;
        }
        if ("end" === options.textAlign) {
            xDiff = commonWidth;
        }
        line.options.forEach(function(o, index) {
            const width = line.widths[index];
            o.x = o.x - xDiff + currentOffset;
            o.y += line.offsets[index];
            currentOffset += width;
        });
        line.elements.forEach(function(element, index) {
            drawTextElement(element.childNodes, context, line.options[index], shared);
        });
    });
}
function drawElement(element, context, parentOptions, shared) {
    const tagName = element.tagName;
    const isText = "text" === tagName || "tspan" === tagName || void 0 === tagName;
    const isImage = "image" === tagName;
    const isComment = 8 === element.nodeType;
    const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, parentOptions, getElementOptions(element, shared.rootAppended));
    if ("hidden" === options.visibility || options[__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].HIDDEN_FOR_EXPORT] || isComment) {
        return;
    }
    context.save();
    !isImage && transformElement(context, options);
    clipElement(context, options, shared);
    aggregateOpacity(options);
    let promise;
    context.beginPath();
    switch(element.tagName){
        case void 0:
            drawText(context, options, shared);
            break;
        case "text":
        case "tspan":
            drawTextElement(element.childNodes, context, options, shared);
            break;
        case "image":
            promise = drawImage(context, options, shared);
            break;
        case "path":
            drawPath(context, options.d);
            break;
        case "rect":
            drawRect(context, options);
            context.closePath();
            break;
        case "circle":
            context.arc(options.cx, options.cy, options.r, 0, 2 * PI, 1);
    }
    if (!isText) {
        applyFilter(context, options, shared);
        if (!isImage) {
            promise = fillElement(context, options, shared);
        }
        strokeElement(context, options);
    }
    applyGradient(context, options, shared, element, "linear");
    applyGradient(context, options, shared, element, "radial");
    context.restore();
    return promise;
}
function applyGradient(context, options, _ref, element, type) {
    let { linearGradients: linearGradients, radialGradients: radialGradients } = _ref;
    const gradients = "linear" === type ? linearGradients : radialGradients;
    if (0 === Object.keys(gradients).length) {
        return;
    }
    const id = parseUrl(options.fill);
    if (id && gradients[id]) {
        const box = element.getBBox();
        const horizontalCenter = box.x + box.width / 2;
        const verticalCenter = box.y + box.height / 2;
        const maxRadius = Math.max(box.height / 2, box.width / 2);
        const gradient = "linear" === type ? context.createLinearGradient(box.x, 0, box.x + box.width, 0) : context.createRadialGradient(horizontalCenter, verticalCenter, 0, horizontalCenter, verticalCenter, maxRadius);
        gradients[id].colors.forEach((opt)=>{
            const offset = parseInt(opt.offset.replace(/%/, ""));
            gradient.addColorStop(offset / 100, opt.stopColor);
        });
        if ("linear" === type) {
            var _gradients$id$transfo;
            const angle = ((null === (_gradients$id$transfo = gradients[id].transform) || void 0 === _gradients$id$transfo ? void 0 : _gradients$id$transfo.replace(/\D/g, "")) || 0) * Math.PI / 180;
            context.translate(horizontalCenter, verticalCenter);
            context.rotate(angle);
            context.translate(-horizontalCenter, -verticalCenter);
        }
        context.globalAlpha = options.opacity;
        context.fillStyle = gradient;
        context.fill();
    }
}
function applyFilter(context, options, shared) {
    let filterOptions;
    const id = parseUrl(options.filter);
    if (id) {
        filterOptions = shared.filters[id];
        if (!filterOptions) {
            filterOptions = {
                offsetX: 0,
                offsetY: 0,
                blur: 0,
                color: "#000"
            };
        }
        context.shadowOffsetX = filterOptions.offsetX;
        context.shadowOffsetY = filterOptions.offsetY;
        context.shadowColor = filterOptions.color;
        context.shadowBlur = filterOptions.blur;
    }
}
function transformElement(context, options) {
    context.translate(options.translateX || 0, options.translateY || 0);
    options.translateX = void 0;
    options.translateY = void 0;
    if (options.rotationAngle) {
        context.translate(options.rotationX || 0, options.rotationY || 0);
        context.rotate(options.rotationAngle * PI / 180);
        context.translate(-(options.rotationX || 0), -(options.rotationY || 0));
        options.rotationAngle = void 0;
        options.rotationX = void 0;
        options.rotationY = void 0;
    }
    if (isFinite(options.scaleX)) {
        context.scale(options.scaleX, options.scaleY);
        options.scaleX = void 0;
        options.scaleY = void 0;
    }
}
function clipElement(context, options, shared) {
    if (options["clip-path"]) {
        drawElement(shared.clipPaths[parseUrl(options["clip-path"])], context, {}, shared);
        context.clip();
        options["clip-path"] = void 0;
    }
}
function hex2rgba(hexColor, alpha) {
    const color = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](hexColor);
    return "rgba(" + color.r + "," + color.g + "," + color.b + "," + alpha + ")";
}
function createGradient(element) {
    var _element$attributes$g;
    const options = {
        colors: [],
        transform: null === (_element$attributes$g = element.attributes.gradientTransform) || void 0 === _element$attributes$g ? void 0 : _element$attributes$g.textContent
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(element.childNodes, (_, _ref2)=>{
        let { attributes: attributes } = _ref2;
        options.colors.push({
            offset: attributes.offset.value,
            stopColor: attributes["stop-color"].value
        });
    });
    return options;
}
function createFilter(element) {
    let color;
    let opacity;
    const filterOptions = {};
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(element.childNodes, function(_, node) {
        const attr = node.attributes;
        if (!attr.result) {
            return;
        }
        switch(attr.result.value){
            case "gaussianBlurResult":
                filterOptions.blur = _number(attr.stdDeviation.value);
                break;
            case "offsetResult":
                filterOptions.offsetX = _number(attr.dx.value);
                filterOptions.offsetY = _number(attr.dy.value);
                break;
            case "floodResult":
                color = attr["flood-color"] ? attr["flood-color"].value : "#000";
                opacity = attr["flood-opacity"] ? attr["flood-opacity"].value : 1;
                filterOptions.color = hex2rgba(color, opacity);
        }
    });
    return filterOptions;
}
function asyncEach(array, callback) {
    let d = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"];
    let i = 0;
    for(; i < array.length; i++){
        const result = callback(array[i]);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPromise"])(result)) {
            result.then(()=>{
                asyncEach(Array.prototype.slice.call(array, i + 1), callback, d);
            });
            break;
        }
    }
    if (i === array.length) {
        d.resolve();
    }
    return d;
}
function drawCanvasElements(elements, context, parentOptions, shared) {
    return asyncEach(elements, function(element) {
        switch(element.tagName && element.tagName.toLowerCase()){
            case "g":
            case "svg":
                {
                    const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, parentOptions, getElementOptions(element, shared.rootAppended));
                    context.save();
                    transformElement(context, options);
                    clipElement(context, options, shared);
                    const onDone = ()=>{
                        context.restore();
                    };
                    const promise = drawCanvasElements(element.childNodes, context, options, shared);
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPromise"])(promise)) {
                        promise.then(onDone);
                    } else {
                        onDone();
                    }
                    return promise;
                }
            case "defs":
                return drawCanvasElements(element.childNodes, context, {}, shared);
            case "clippath":
                shared.clipPaths[element.attributes.id.textContent] = element.childNodes[0];
                break;
            case "pattern":
                shared.patterns[element.attributes.id.textContent] = element;
                break;
            case "filter":
                shared.filters[element.id] = createFilter(element);
                break;
            case "lineargradient":
                shared.linearGradients[element.attributes.id.textContent] = createGradient(element);
                break;
            case "radialgradient":
                shared.radialGradients[element.attributes.id.textContent] = createGradient(element);
                break;
            default:
                return drawElement(element, context, parentOptions, shared);
        }
    });
}
function setLineDash(context, options) {
    let matches = options["stroke-dasharray"] && options["stroke-dasharray"].match(/(\d+)/g);
    if (matches && matches.length) {
        matches = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["map"])(matches, function(item) {
            return _number(item);
        });
        context.setLineDash(matches);
    }
}
function strokeElement(context, options, isText) {
    const stroke = options.stroke;
    if (stroke && "none" !== stroke && 0 !== options["stroke-width"]) {
        setLineDash(context, options);
        context.lineJoin = options["stroke-linejoin"];
        context.lineWidth = options["stroke-width"];
        context.globalAlpha = options.strokeOpacity;
        context.strokeStyle = stroke;
        isText ? context.strokeText(options.text, options.x, options.y) : context.stroke();
        context.globalAlpha = 1;
    }
}
function getPattern(context, pattern, shared, parentOptions) {
    const options = getElementOptions(pattern, shared.rootAppended);
    const patternCanvas = imageCreator._createCanvas(options.width, options.height, 0);
    const patternContext = patternCanvas.getContext("2d");
    const promise = drawCanvasElements(pattern.childNodes, patternContext, options, shared);
    const onDone = ()=>{
        context.fillStyle = context.createPattern(patternCanvas, "repeat");
        context.globalAlpha = parentOptions.fillOpacity;
        context.fill();
        context.globalAlpha = 1;
    };
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPromise"])(promise)) {
        promise.then(onDone);
    } else {
        onDone();
    }
    return promise;
}
function fillElement(context, options, shared) {
    const fill = options.fill;
    let promise;
    if (fill && "none" !== fill) {
        if (-1 === fill.search(/url/)) {
            context.fillStyle = fill;
            context.globalAlpha = options.fillOpacity;
            context.fill();
            context.globalAlpha = 1;
        } else {
            const pattern = shared.patterns[parseUrl(fill)];
            if (!pattern) {
                return;
            }
            promise = getPattern(context, pattern, shared, options);
        }
    }
    return promise;
}
parseAttributes = function(attributes) {
    const newAttributes = {};
    let attr;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(attributes, function(index, item) {
        attr = item.textContent;
        if (isFinite(attr)) {
            attr = _number(attr);
        }
        newAttributes[item.name.toLowerCase()] = attr;
    });
    return newAttributes;
};
function drawBackground(context, width, height, backgroundColor, margin) {
    context.fillStyle = backgroundColor || "#ffffff";
    context.fillRect(-margin, -margin, width + 2 * margin, height + 2 * margin);
}
function createInvisibleDiv() {
    const invisibleDiv = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].createElement("div");
    invisibleDiv.style.left = "-9999px";
    invisibleDiv.style.position = "absolute";
    return invisibleDiv;
}
function convertSvgToCanvas(svg, canvas, rootAppended) {
    return drawCanvasElements(svg.childNodes, canvas.getContext("2d"), {}, {
        clipPaths: {},
        patterns: {},
        filters: {},
        linearGradients: {},
        radialGradients: {},
        rootAppended: rootAppended
    });
}
function getCanvasFromSvg(markup, _ref3) {
    let { width: width, height: height, backgroundColor: backgroundColor, margin: margin, svgToCanvas: svgToCanvas = convertSvgToCanvas } = _ref3;
    const scaledScreenInfo = calcScaledInfo(width, height);
    const canvas = imageCreator._createCanvas(scaledScreenInfo.width, scaledScreenInfo.height, margin);
    const context = canvas.getContext("2d");
    context.setTransform(scaledScreenInfo.pixelRatio, 0, 0, scaledScreenInfo.pixelRatio, 0, 0);
    const svgElem = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getSvgElement(markup);
    let invisibleDiv;
    const markupIsDomElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].isElementNode(markup) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isRenderer"])(markup);
    context.translate(margin, margin);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody().appendChild(canvas);
    if (!markupIsDomElement) {
        invisibleDiv = createInvisibleDiv();
        invisibleDiv.appendChild(svgElem);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody().appendChild(invisibleDiv);
    }
    if (svgElem.attributes.direction) {
        canvas.dir = svgElem.attributes.direction.textContent;
    }
    drawBackground(context, width, height, backgroundColor, margin);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fromPromise"])(svgToCanvas(svgElem, canvas, markupIsDomElement && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["contains"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody(), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(markup).get(0)))).then(()=>canvas).always(()=>{
        invisibleDiv && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody().removeChild(invisibleDiv);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getBody().removeChild(canvas);
    });
}
const imageCreator = {
    getImageData: function(markup, options) {
        const mimeType = "image/" + options.format;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(options.__parseAttributesFn)) {
            parseAttributes = options.__parseAttributesFn;
        }
        return getCanvasFromSvg(markup, options).then((canvas)=>getStringFromCanvas(canvas, mimeType));
    },
    getData: function(markup, options) {
        const that = this;
        return imageCreator.getImageData(markup, options).then((binaryData)=>{
            const mimeType = "image/" + options.format;
            const data = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(window.Blob) && !options.useBase64 ? that._getBlob(binaryData, mimeType) : that._getBase64(binaryData);
            return data;
        });
    },
    _getBlob: function(binaryData, mimeType) {
        let i;
        const dataArray = new Uint8Array(binaryData.length);
        for(i = 0; i < binaryData.length; i++){
            dataArray[i] = binaryData.charCodeAt(i);
        }
        return new window.Blob([
            dataArray.buffer
        ], {
            type: mimeType
        });
    },
    _getBase64: function(binaryData) {
        return window.btoa(binaryData);
    },
    _createCanvas (width, height, margin) {
        const canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])("<canvas>")[0];
        canvas.width = width + 2 * margin;
        canvas.height = height + 2 * margin;
        canvas.hidden = true;
        return canvas;
    }
};
function getData(data, options) {
    return imageCreator.getData(data, options);
}
function testFormats(formats) {
    const canvas = imageCreator._createCanvas(100, 100, 0);
    return formats.reduce(function(r, f) {
        const mimeType = ("image/" + f).toLowerCase();
        if (-1 !== canvas.toDataURL(mimeType).indexOf(mimeType)) {
            r.supported.push(f);
        } else {
            r.unsupported.push(f);
        }
        return r;
    }, {
        supported: [],
        unsupported: []
    });
}
function calcScaledInfo(width, height) {
    const pixelRatio = window.devicePixelRatio || 1;
    return {
        pixelRatio: pixelRatio,
        width: width * pixelRatio,
        height: height * pixelRatio
    };
}
}),
"[project]/node_modules/devextreme/esm/exporter/svg_creator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/exporter/svg_creator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getData": ()=>getData,
    "svgCreator": ()=>svgCreator
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ajax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/ajax.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_svg.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-ssr] (ecmascript)");
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])();
;
;
;
;
const svgCreator = {
    _markup: "",
    _imageArray: {},
    _imageDeferreds: [],
    _getBinaryFile: function(src, callback) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ajax$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].sendRequest({
            url: src,
            method: "GET",
            responseType: "arraybuffer"
        }).done(callback).fail(function() {
            callback(false);
        });
    },
    _loadImages: function() {
        const that = this;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(that._imageArray, function(src) {
            const deferred = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"];
            that._imageDeferreds.push(deferred);
            that._getBinaryFile(src, function(response) {
                if (!response) {
                    delete that._imageArray[src];
                    deferred.resolve();
                    return;
                }
                let i;
                let binary = "";
                const bytes = new Uint8Array(response);
                const length = bytes.byteLength;
                for(i = 0; i < length; i++){
                    binary += String.fromCharCode(bytes[i]);
                }
                that._imageArray[src] = "data:image/png;base64," + window.btoa(binary);
                deferred.resolve();
            });
        });
    },
    _parseImages: function(element) {
        let href;
        const that = this;
        if ("image" === element.tagName) {
            href = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(element).attr("href") || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(element).attr("xlink:href");
            if (!that._imageArray[href]) {
                that._imageArray[href] = "";
            }
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(element.childNodes, function(_, element) {
            that._parseImages(element);
        });
    },
    _prepareImages: function(svgElem) {
        this._parseImages(svgElem);
        this._loadImages();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["when"].apply(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], this._imageDeferreds);
    },
    getData: function(data, options) {
        let markup;
        const that = this;
        const svgElem = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getSvgElement(data);
        const $svgObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(svgElem);
        $svgObject.find(`[${__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].HIDDEN_FOR_EXPORT}]`).remove();
        markup = '<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>' + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_svg$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].getSvgMarkup($svgObject.get(0), options.backgroundColor);
        return that._prepareImages(svgElem).then(()=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(that._imageArray, function(href, dataURI) {
                const regexpString = `href=['|"]${href}['|"]`;
                markup = markup.replace(new RegExp(regexpString, "gi"), `href="${dataURI}"`);
            });
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(window.Blob) ? that._getBlob(markup) : that._getBase64(markup);
        });
    },
    _getBlob: function(markup) {
        return new window.Blob([
            markup
        ], {
            type: "image/svg+xml"
        });
    },
    _getBase64: function(markup) {
        return window.btoa(markup);
    }
};
function getData(data, options) {
    return svgCreator.getData(data, options);
}
}),
"[project]/node_modules/devextreme/esm/exporter/pdf_creator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/exporter/pdf_creator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getData": ()=>getData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/version.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/image_creator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getWindow"])();
;
;
;
const mainPageTemplate = "%PDF-1.3\r\n2 0 obj\r\n<</ProcSet[/PDF/ImageB/ImageC/ImageI]/XObject<</I0 5 0 R>>>>\r\nendobj\r\n4 0 obj\r\n<</Type/Pages/Kids[1 0 R]/Count 1>>\r\nendobj\r\n7 0 obj\r\n<</OpenAction[1 0 R /FitH null]/Type/Catalog/Pages 4 0 R/PageLayout/OneColumn>>\r\nendobj\r\n1 0 obj\r\n<</Type/Page/Resources 2 0 R/MediaBox[0 0 _width_ _height_]/Contents 3 0 R/Parent 4 0 R>>\r\nendobj\r\n";
const contentTemplate = "3 0 obj\r\n<</Length 52>>stream\r\n0.20 w\n0 G\nq _width_ 0 0 _height_ 0.00 0.00 cm /I0 Do Q\r\nendstream\r\nendobj\r\n";
const infoTemplate = "6 0 obj\r\n<</CreationDate _date_/Producer(DevExtreme _version_)>>\r\nendobj\r\n";
const imageStartTemplate = "5 0 obj\r\n<</Type/XObject/Subtype/Image/Width _width_/Height _height_/ColorSpace/DeviceRGB/BitsPerComponent 8/Filter/DCTDecode/Length _length_>>stream\r\n";
const imageEndTemplate = "\r\nendstream\r\nendobj\r\n";
const trailerTemplate = "trailer\r\n<<\r\n/Size 8\r\n/Root 7 0 R\r\n/Info 6 0 R\r\n>>\r\nstartxref\r\n_length_\r\n%%EOF";
const xrefTemplate = "xref\r\n0 8\r\n0000000000 65535 f\r\n0000000241 00000 n\r\n0000000010 00000 n\r\n_main_ 00000 n\r\n0000000089 00000 n\r\n_image_ 00000 n\r\n_info_ 00000 n\r\n0000000143 00000 n\r\n";
const pad = function(str, len) {
    return str.length < len ? pad("0" + str, len) : str;
};
let composePdfString = function(imageString, options, curDate) {
    const margin = 2 * (options.margin || 0);
    let { width: width, height: height } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calcScaledInfo"])(options.width, options.height);
    width += margin;
    height += margin;
    const widthPt = (.75 * width).toFixed(2);
    const heightPt = (.75 * height).toFixed(2);
    const flooredWidth = Math.floor(width);
    const flooredHeight = Math.floor(height);
    const mainPage = mainPageTemplate.replace("_width_", widthPt).replace("_height_", heightPt);
    const content = contentTemplate.replace("_width_", widthPt).replace("_height_", heightPt);
    const info = infoTemplate.replace("_date_", curDate).replace("_version_", __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$version$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["version"]);
    const image = imageStartTemplate.replace("_width_", flooredWidth).replace("_height_", flooredHeight).replace("_length_", imageString.length) + imageString + imageEndTemplate;
    const xref = getXref(mainPage.length, content.length, info.length);
    const mainContent = mainPage + content + info + image;
    const trailer = trailerTemplate.replace("_length_", mainContent.length);
    return mainContent + xref + trailer;
};
function getXref(mainPageLength, contentLength, infoLength) {
    return xrefTemplate.replace("_main_", pad(mainPageLength + "", 10)).replace("_info_", pad(mainPageLength + contentLength + "", 10)).replace("_image_", pad(mainPageLength + contentLength + infoLength + "", 10));
}
let getCurDate = function() {
    return new Date;
};
let getBlob = function(binaryData) {
    let i = 0;
    const dataArray = new Uint8Array(binaryData.length);
    for(; i < binaryData.length; i++){
        dataArray[i] = binaryData.charCodeAt(i);
    }
    return new window.Blob([
        dataArray.buffer
    ], {
        type: "application/pdf"
    });
};
let getBase64 = function(binaryData) {
    return window.btoa(binaryData);
};
function getTwoDigitValue(value) {
    const stringValue = value.toString();
    if (1 === stringValue.length) {
        return `0${value}`;
    }
    return value;
}
function convertToPdfDateFormat(date) {
    const dateUnits = [
        date.getUTCFullYear(),
        getTwoDigitValue(date.getUTCMonth()),
        getTwoDigitValue(date.getUTCDate()),
        getTwoDigitValue(date.getUTCHours()),
        getTwoDigitValue(date.getUTCMinutes()),
        getTwoDigitValue(date.getUTCSeconds())
    ];
    return `(D:${dateUnits.join("")}Z00'00')`;
}
function getData(data, options) {
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["imageCreator"].getImageData(data, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, options, {
        format: "JPEG"
    })).then((imageString)=>{
        const binaryData = composePdfString(imageString, options, convertToPdfDateFormat(getCurDate()));
        const pdfData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(window.Blob) ? getBlob(binaryData) : getBase64(binaryData);
        return pdfData;
    });
}
}),
"[project]/node_modules/devextreme/esm/exporter.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/exporter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "export": ()=>_export,
    "image": ()=>image,
    "pdf": ()=>pdf,
    "svg": ()=>svg
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$file_saver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/file_saver.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/image_creator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$svg_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/svg_creator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$pdf_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/pdf_creator.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
function _export(data, options, getData) {
    if (!data) {
        return (new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"]).resolve();
    }
    const exportingAction = options.exportingAction;
    const exportedAction = options.exportedAction;
    const fileSavingAction = options.fileSavingAction;
    const eventArgs = {
        fileName: options.fileName,
        format: options.format,
        cancel: false
    };
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isBoolean"])(options.selectedRowsOnly)) {
        eventArgs.selectedRowsOnly = options.selectedRowsOnly;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(exportingAction) && exportingAction(eventArgs);
    if (!eventArgs.cancel) {
        return getData(data, options).then((blob)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(exportedAction) && exportedAction();
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(fileSavingAction)) {
                eventArgs.data = blob;
                fileSavingAction(eventArgs);
            }
            if (!eventArgs.cancel) {
                const format = "xlsx" === options.format ? "EXCEL" : options.format;
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$file_saver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["fileSaver"].saveAs(eventArgs.fileName, format, blob);
            }
        });
    }
    return (new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"]).resolve();
}
;
const image = {
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["imageCreator"],
    getData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getData"],
    testFormats: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["testFormats"]
};
const pdf = {
    getData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$pdf_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getData"]
};
const svg = {
    creator: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$svg_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["svgCreator"],
    getData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$svg_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getData"]
};
}),
"[project]/node_modules/devextreme/esm/exporter.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$file_saver$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/file_saver.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$image_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/image_creator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$svg_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/svg_creator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2f$pdf_creator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter/pdf_creator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$exporter$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/exporter.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/localization.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/localization.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization.js [app-ssr] (ecmascript) <module evaluation>");
;
}),
"[project]/node_modules/devextreme/esm/localization.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$localization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/localization.js [app-ssr] (ecmascript) <locals>");
}),

};

//# sourceMappingURL=node_modules_devextreme_esm_e8e63d23._.js.map