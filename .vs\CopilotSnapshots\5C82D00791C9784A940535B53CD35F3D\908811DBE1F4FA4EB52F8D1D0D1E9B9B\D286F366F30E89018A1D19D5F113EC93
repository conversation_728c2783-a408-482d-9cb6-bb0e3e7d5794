﻿using System.Windows;
using DevExpress.Xpf.Core;

namespace omsnext.wpf
{
    public partial class AdminNotesDialog : ThemedWindow
    {
        public string Notes { get; private set; } = string.Empty;

        public AdminNotesDialog(string title, string description)
        {
            InitializeComponent();
            
            TitleLabel.Text = title;
            DescriptionLabel.Text = description;
            
            NotesTextEdit.Focus();
        }

        private void OkButton_Click(object sender, RoutedEventArgs e)
        {
            Notes = NotesTextEdit.Text?.Trim() ?? string.Empty;
            
            if (string.IsNullOrWhiteSpace(Notes))
            {
                DXMessageBox.Show("K<PERSON>rj<PERSON>k, adjon meg egy jegyzetet!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                NotesTextEdit.Focus();
                return;
            }
            
            DialogResult = true;
            Close();
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            DialogResult = false;
            Close();
        }

        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                CancelButton_Click(this, new RoutedEventArgs());
            }
            else if (e.Key == System.Windows.Input.Key.Enter && 
                     (System.Windows.Input.Keyboard.Modifiers & System.Windows.Input.ModifierKeys.Control) == System.Windows.Input.ModifierKeys.Control)
            {
                OkButton_Click(this, new RoutedEventArgs());
            }
            
            base.OnKeyDown(e);
        }
    }
}