{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/contants.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport const WHITE = \"#ffffff\";\r\nexport const BLACK = \"#000000\";\r\nexport const LIGHT_GREY = \"#d3d3d3\";\r\nexport const GREY_GREEN = \"#303030\";\r\nexport const SOME_GREY = \"#2b2b2b\";\r\nexport const RED = \"#ff0000\";\r\nexport const PRIMARY_TITLE_COLOR = \"#232323\";\r\nexport const SECONDARY_TITLE_COLOR = \"#767676\";\r\nexport const NONE = \"none\";\r\nexport const SOLID = \"solid\";\r\nexport const TOP = \"top\";\r\nexport const RIGHT = \"right\";\r\nexport const BOTTOM = \"bottom\";\r\nexport const LEFT = \"left\";\r\nexport const CENTER = \"center\";\r\nexport const INSIDE = \"inside\";\r\nexport const OUTSIDE = \"outside\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;;AACM,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,YAAY;AAClB,MAAM,MAAM;AACZ,MAAM,sBAAsB;AAC5B,MAAM,wBAAwB;AAC9B,MAAM,OAAO;AACb,MAAM,QAAQ;AACd,MAAM,MAAM;AACZ,MAAM,QAAQ;AACd,MAAM,SAAS;AACf,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,UAAU", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/bar_gauge.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/bar_gauge.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    BOTTOM,\r\n    CENTER\r\n} from \"./contants\";\r\nexport default {\r\n    barGauge: {\r\n        backgroundColor: \"#e0e0e0\",\r\n        relativeInnerRadius: .3,\r\n        barSpacing: 4,\r\n        resolveLabelOverlapping: \"hide\",\r\n        label: {\r\n            indent: 20,\r\n            connectorWidth: 2,\r\n            font: {\r\n                size: 16\r\n            }\r\n        },\r\n        legend: {\r\n            visible: false\r\n        },\r\n        indicator: {\r\n            hasPositiveMeaning: true,\r\n            layout: {\r\n                horizontalAlignment: CENTER,\r\n                verticalAlignment: BOTTOM\r\n            },\r\n            text: {\r\n                font: {\r\n                    size: 18\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAIe;IACX,UAAU;QACN,iBAAiB;QACjB,qBAAqB;QACrB,YAAY;QACZ,yBAAyB;QACzB,OAAO;YACH,QAAQ;YACR,gBAAgB;YAChB,MAAM;gBACF,MAAM;YACV;QACJ;QACA,QAAQ;YACJ,SAAS;QACb;QACA,WAAW;YACP,oBAAoB;YACpB,QAAQ;gBACJ,qBAAqB,2MAAA,CAAA,SAAM;gBAC3B,mBAAmB,2MAAA,CAAA,SAAM;YAC7B;YACA,MAAM;gBACF,MAAM;oBACF,MAAM;gBACV;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 98, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/bullet.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/bullet.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport default {\r\n    bullet: {\r\n        color: \"#e8c267\",\r\n        targetColor: \"#666666\",\r\n        targetWidth: 4,\r\n        showTarget: true,\r\n        showZeroLevel: true,\r\n        tooltip: {\r\n            enabled: true\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;uCACc;IACX,QAAQ;QACJ,OAAO;QACP,aAAa;QACb,aAAa;QACb,YAAY;QACZ,eAAe;QACf,SAAS;YACL,SAAS;QACb;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    BLACK,\r\n    BOTTOM,\r\n    CENTER,\r\n    INSIDE,\r\n    LEFT,\r\n    LIGHT_GREY,\r\n    NONE,\r\n    OUTSIDE,\r\n    RED,\r\n    RIGHT,\r\n    SOLID,\r\n    TOP,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    \"chart:common\": {\r\n        animation: {\r\n            enabled: true,\r\n            duration: 1e3,\r\n            easing: \"easeOutCubic\",\r\n            maxPointCountSupported: 300\r\n        },\r\n        commonSeriesSettings: {\r\n            border: {\r\n                visible: false,\r\n                width: 2\r\n            },\r\n            showInLegend: true,\r\n            visible: true,\r\n            hoverMode: \"nearestPoint\",\r\n            selectionMode: \"includePoints\",\r\n            hoverStyle: {\r\n                hatching: {\r\n                    direction: RIGHT,\r\n                    width: 2,\r\n                    step: 6,\r\n                    opacity: .75\r\n                },\r\n                highlight: true,\r\n                border: {\r\n                    visible: false,\r\n                    width: 3\r\n                }\r\n            },\r\n            selectionStyle: {\r\n                hatching: {\r\n                    direction: RIGHT,\r\n                    width: 2,\r\n                    step: 6,\r\n                    opacity: .5\r\n                },\r\n                highlight: true,\r\n                border: {\r\n                    visible: false,\r\n                    width: 3\r\n                }\r\n            },\r\n            valueErrorBar: {\r\n                displayMode: \"auto\",\r\n                value: 1,\r\n                color: BLACK,\r\n                lineWidth: 2,\r\n                edgeLength: 8\r\n            },\r\n            label: {\r\n                visible: false,\r\n                alignment: CENTER,\r\n                rotationAngle: 0,\r\n                horizontalOffset: 0,\r\n                verticalOffset: 0,\r\n                radialOffset: 0,\r\n                showForZeroValues: true,\r\n                customizeText: void 0,\r\n                maxLabelCount: void 0,\r\n                position: OUTSIDE,\r\n                font: {\r\n                    color: WHITE\r\n                },\r\n                border: {\r\n                    visible: false,\r\n                    width: 1,\r\n                    color: LIGHT_GREY,\r\n                    dashStyle: SOLID\r\n                },\r\n                connector: {\r\n                    visible: false,\r\n                    width: 1\r\n                }\r\n            }\r\n        },\r\n        seriesSelectionMode: \"single\",\r\n        pointSelectionMode: \"single\",\r\n        equalRowHeight: true,\r\n        dataPrepareSettings: {\r\n            checkTypeForAllData: false,\r\n            convertToAxisDataType: true,\r\n            sortingMethod: true\r\n        },\r\n        title: {\r\n            margin: 10\r\n        },\r\n        adaptiveLayout: {\r\n            width: 80,\r\n            height: 80,\r\n            keepLabels: true\r\n        },\r\n        _rtl: {\r\n            legend: {\r\n                itemTextPosition: LEFT\r\n            }\r\n        },\r\n        resolveLabelOverlapping: NONE\r\n    },\r\n    \"chart:common:axis\": {\r\n        visible: true,\r\n        valueMarginsEnabled: true,\r\n        placeholderSize: null,\r\n        logarithmBase: 10,\r\n        discreteAxisDivisionMode: \"betweenLabels\",\r\n        aggregatedPointsPosition: \"betweenTicks\",\r\n        width: 1,\r\n        label: {\r\n            visible: true\r\n        },\r\n        grid: {\r\n            visible: false,\r\n            width: 1\r\n        },\r\n        minorGrid: {\r\n            visible: false,\r\n            width: 1,\r\n            opacity: .3\r\n        },\r\n        tick: {\r\n            visible: true,\r\n            width: 1,\r\n            length: 7,\r\n            shift: 3\r\n        },\r\n        minorTick: {\r\n            visible: false,\r\n            width: 1,\r\n            opacity: .3,\r\n            length: 7,\r\n            shift: 3\r\n        },\r\n        stripStyle: {\r\n            paddingLeftRight: 10,\r\n            paddingTopBottom: 5\r\n        },\r\n        constantLineStyle: {\r\n            width: 1,\r\n            color: BLACK,\r\n            dashStyle: SOLID,\r\n            label: {\r\n                visible: true,\r\n                position: INSIDE\r\n            }\r\n        },\r\n        marker: {\r\n            label: {}\r\n        }\r\n    },\r\n    \"chart:common:annotation\": {\r\n        font: {\r\n            color: \"#333333\"\r\n        },\r\n        tooltipEnabled: true,\r\n        border: {\r\n            width: 1,\r\n            color: \"#dddddd\",\r\n            dashStyle: SOLID,\r\n            visible: true\r\n        },\r\n        color: WHITE,\r\n        opacity: .9,\r\n        arrowLength: 14,\r\n        arrowWidth: 14,\r\n        paddingLeftRight: 10,\r\n        paddingTopBottom: 10,\r\n        shadow: {\r\n            opacity: .15,\r\n            offsetX: 0,\r\n            offsetY: 1,\r\n            blur: 4,\r\n            color: BLACK\r\n        },\r\n        image: {\r\n            width: 30,\r\n            height: 30\r\n        },\r\n        wordWrap: \"normal\",\r\n        textOverflow: \"ellipsis\",\r\n        allowDragging: false\r\n    },\r\n    chart: {\r\n        commonSeriesSettings: {\r\n            type: \"line\",\r\n            stack: \"default\",\r\n            aggregation: {\r\n                enabled: void 0\r\n            },\r\n            point: {\r\n                visible: true,\r\n                symbol: \"circle\",\r\n                size: 12,\r\n                border: {\r\n                    visible: false,\r\n                    width: 1\r\n                },\r\n                hoverMode: \"onlyPoint\",\r\n                selectionMode: \"onlyPoint\",\r\n                hoverStyle: {\r\n                    border: {\r\n                        visible: true,\r\n                        width: 4\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    border: {\r\n                        visible: true,\r\n                        width: 4\r\n                    }\r\n                }\r\n            },\r\n            scatter: {},\r\n            line: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            stackedline: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            stackedspline: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            fullstackedline: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            fullstackedspline: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            stepline: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            area: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            stackedarea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            fullstackedarea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            fullstackedsplinearea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            steparea: {\r\n                border: {\r\n                    visible: true,\r\n                    width: 2\r\n                },\r\n                point: {\r\n                    visible: false\r\n                },\r\n                hoverStyle: {\r\n                    border: {\r\n                        visible: true,\r\n                        width: 3\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    border: {\r\n                        visible: true,\r\n                        width: 3\r\n                    }\r\n                },\r\n                opacity: .5\r\n            },\r\n            spline: {\r\n                width: 2,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            splinearea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            stackedsplinearea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            bar: {\r\n                cornerRadius: 0,\r\n                point: {\r\n                    hoverStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            stackedbar: {\r\n                cornerRadius: 0,\r\n                point: {\r\n                    hoverStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    }\r\n                },\r\n                label: {\r\n                    position: INSIDE\r\n                }\r\n            },\r\n            fullstackedbar: {\r\n                cornerRadius: 0,\r\n                point: {\r\n                    hoverStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    }\r\n                },\r\n                label: {\r\n                    position: INSIDE\r\n                }\r\n            },\r\n            rangebar: {\r\n                cornerRadius: 0,\r\n                point: {\r\n                    hoverStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            rangearea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            rangesplinearea: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            bubble: {\r\n                opacity: .5,\r\n                point: {\r\n                    hoverStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        border: {\r\n                            visible: false\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            candlestick: {\r\n                width: 1,\r\n                reduction: {\r\n                    color: RED\r\n                },\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3,\r\n                    highlight: false\r\n                },\r\n                point: {\r\n                    border: {\r\n                        visible: true\r\n                    }\r\n                }\r\n            },\r\n            stock: {\r\n                width: 1,\r\n                reduction: {\r\n                    color: RED\r\n                },\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    },\r\n                    highlight: false\r\n                },\r\n                selectionStyle: {\r\n                    width: 3,\r\n                    highlight: false\r\n                },\r\n                point: {\r\n                    border: {\r\n                        visible: true\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        crosshair: {\r\n            enabled: false,\r\n            color: \"#f05b41\",\r\n            width: 1,\r\n            dashStyle: SOLID,\r\n            label: {\r\n                visible: false,\r\n                font: {\r\n                    color: WHITE,\r\n                    size: 12\r\n                }\r\n            },\r\n            verticalLine: {\r\n                visible: true\r\n            },\r\n            horizontalLine: {\r\n                visible: true\r\n            }\r\n        },\r\n        commonAxisSettings: {\r\n            multipleAxesSpacing: 5,\r\n            forceUserTickInterval: false,\r\n            breakStyle: {\r\n                width: 5,\r\n                color: \"#ababab\",\r\n                line: \"waved\"\r\n            },\r\n            label: {\r\n                displayMode: \"standard\",\r\n                overlappingBehavior: \"hide\",\r\n                indentFromAxis: 10,\r\n                wordWrap: \"normal\",\r\n                textOverflow: \"none\"\r\n            },\r\n            title: {\r\n                font: {\r\n                    size: 16\r\n                },\r\n                margin: 6,\r\n                alignment: CENTER\r\n            },\r\n            constantLineStyle: {\r\n                paddingLeftRight: 10,\r\n                paddingTopBottom: 10\r\n            }\r\n        },\r\n        horizontalAxis: {\r\n            position: BOTTOM,\r\n            axisDivisionFactor: 70,\r\n            label: {\r\n                rotationAngle: 90,\r\n                staggeringSpacing: 5,\r\n                alignment: CENTER\r\n            },\r\n            stripStyle: {\r\n                label: {\r\n                    horizontalAlignment: CENTER,\r\n                    verticalAlignment: TOP\r\n                }\r\n            },\r\n            constantLineStyle: {\r\n                label: {\r\n                    horizontalAlignment: RIGHT,\r\n                    verticalAlignment: TOP\r\n                }\r\n            },\r\n            constantLines: []\r\n        },\r\n        verticalAxis: {\r\n            position: LEFT,\r\n            axisDivisionFactor: 40,\r\n            label: {\r\n                alignment: RIGHT\r\n            },\r\n            stripStyle: {\r\n                label: {\r\n                    horizontalAlignment: LEFT,\r\n                    verticalAlignment: CENTER\r\n                }\r\n            },\r\n            constantLineStyle: {\r\n                label: {\r\n                    horizontalAlignment: LEFT,\r\n                    verticalAlignment: TOP\r\n                }\r\n            },\r\n            constantLines: []\r\n        },\r\n        argumentAxis: {\r\n            endOnTick: false,\r\n            workWeek: [1, 2, 3, 4, 5]\r\n        },\r\n        valueAxis: {\r\n            grid: {\r\n                visible: true\r\n            },\r\n            autoBreaksEnabled: false,\r\n            maxAutoBreakCount: 4\r\n        },\r\n        commonPaneSettings: {\r\n            backgroundColor: NONE,\r\n            border: {\r\n                color: LIGHT_GREY,\r\n                width: 1,\r\n                visible: false,\r\n                top: true,\r\n                bottom: true,\r\n                left: true,\r\n                right: true,\r\n                dashStyle: SOLID\r\n            }\r\n        },\r\n        scrollBar: {\r\n            visible: false,\r\n            offset: 5,\r\n            color: \"gray\",\r\n            width: 10\r\n        },\r\n        adjustOnZoom: true,\r\n        autoHidePointMarkers: true,\r\n        rotated: false,\r\n        synchronizeMultiAxes: true,\r\n        stickyHovering: true,\r\n        barGroupPadding: .3,\r\n        minBubbleSize: 12,\r\n        maxBubbleSize: .2,\r\n        zoomAndPan: {\r\n            dragBoxStyle: {\r\n                color: \"#2a2a2a\",\r\n                opacity: .2\r\n            },\r\n            panKey: \"shift\",\r\n            allowMouseWheel: true,\r\n            allowTouchGestures: true\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAee;IACX,gBAAgB;QACZ,WAAW;YACP,SAAS;YACT,UAAU;YACV,QAAQ;YACR,wBAAwB;QAC5B;QACA,sBAAsB;YAClB,QAAQ;gBACJ,SAAS;gBACT,OAAO;YACX;YACA,cAAc;YACd,SAAS;YACT,WAAW;YACX,eAAe;YACf,YAAY;gBACR,UAAU;oBACN,WAAW,2MAAA,CAAA,QAAK;oBAChB,OAAO;oBACP,MAAM;oBACN,SAAS;gBACb;gBACA,WAAW;gBACX,QAAQ;oBACJ,SAAS;oBACT,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,UAAU;oBACN,WAAW,2MAAA,CAAA,QAAK;oBAChB,OAAO;oBACP,MAAM;oBACN,SAAS;gBACb;gBACA,WAAW;gBACX,QAAQ;oBACJ,SAAS;oBACT,OAAO;gBACX;YACJ;YACA,eAAe;gBACX,aAAa;gBACb,OAAO;gBACP,OAAO,2MAAA,CAAA,QAAK;gBACZ,WAAW;gBACX,YAAY;YAChB;YACA,OAAO;gBACH,SAAS;gBACT,WAAW,2MAAA,CAAA,SAAM;gBACjB,eAAe;gBACf,kBAAkB;gBAClB,gBAAgB;gBAChB,cAAc;gBACd,mBAAmB;gBACnB,eAAe,KAAK;gBACpB,eAAe,KAAK;gBACpB,UAAU,2MAAA,CAAA,UAAO;gBACjB,MAAM;oBACF,OAAO,2MAAA,CAAA,QAAK;gBAChB;gBACA,QAAQ;oBACJ,SAAS;oBACT,OAAO;oBACP,OAAO,2MAAA,CAAA,aAAU;oBACjB,WAAW,2MAAA,CAAA,QAAK;gBACpB;gBACA,WAAW;oBACP,SAAS;oBACT,OAAO;gBACX;YACJ;QACJ;QACA,qBAAqB;QACrB,oBAAoB;QACpB,gBAAgB;QAChB,qBAAqB;YACjB,qBAAqB;YACrB,uBAAuB;YACvB,eAAe;QACnB;QACA,OAAO;YACH,QAAQ;QACZ;QACA,gBAAgB;YACZ,OAAO;YACP,QAAQ;YACR,YAAY;QAChB;QACA,MAAM;YACF,QAAQ;gBACJ,kBAAkB,2MAAA,CAAA,OAAI;YAC1B;QACJ;QACA,yBAAyB,2MAAA,CAAA,OAAI;IACjC;IACA,qBAAqB;QACjB,SAAS;QACT,qBAAqB;QACrB,iBAAiB;QACjB,eAAe;QACf,0BAA0B;QAC1B,0BAA0B;QAC1B,OAAO;QACP,OAAO;YACH,SAAS;QACb;QACA,MAAM;YACF,SAAS;YACT,OAAO;QACX;QACA,WAAW;YACP,SAAS;YACT,OAAO;YACP,SAAS;QACb;QACA,MAAM;YACF,SAAS;YACT,OAAO;YACP,QAAQ;YACR,OAAO;QACX;QACA,WAAW;YACP,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;YACR,OAAO;QACX;QACA,YAAY;YACR,kBAAkB;YAClB,kBAAkB;QACtB;QACA,mBAAmB;YACf,OAAO;YACP,OAAO,2MAAA,CAAA,QAAK;YACZ,WAAW,2MAAA,CAAA,QAAK;YAChB,OAAO;gBACH,SAAS;gBACT,UAAU,2MAAA,CAAA,SAAM;YACpB;QACJ;QACA,QAAQ;YACJ,OAAO,CAAC;QACZ;IACJ;IACA,2BAA2B;QACvB,MAAM;YACF,OAAO;QACX;QACA,gBAAgB;QAChB,QAAQ;YACJ,OAAO;YACP,OAAO;YACP,WAAW,2MAAA,CAAA,QAAK;YAChB,SAAS;QACb;QACA,OAAO,2MAAA,CAAA,QAAK;QACZ,SAAS;QACT,aAAa;QACb,YAAY;QACZ,kBAAkB;QAClB,kBAAkB;QAClB,QAAQ;YACJ,SAAS;YACT,SAAS;YACT,SAAS;YACT,MAAM;YACN,OAAO,2MAAA,CAAA,QAAK;QAChB;QACA,OAAO;YACH,OAAO;YACP,QAAQ;QACZ;QACA,UAAU;QACV,cAAc;QACd,eAAe;IACnB;IACA,OAAO;QACH,sBAAsB;YAClB,MAAM;YACN,OAAO;YACP,aAAa;gBACT,SAAS,KAAK;YAClB;YACA,OAAO;gBACH,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,QAAQ;oBACJ,SAAS;oBACT,OAAO;gBACX;gBACA,WAAW;gBACX,eAAe;gBACf,YAAY;oBACR,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;gBACA,gBAAgB;oBACZ,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;YACJ;YACA,SAAS,CAAC;YACV,MAAM;gBACF,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,aAAa;gBACT,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,eAAe;gBACX,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,iBAAiB;gBACb,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,mBAAmB;gBACf,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,UAAU;gBACN,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,MAAM;gBACF,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,aAAa;gBACT,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,iBAAiB;gBACb,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,uBAAuB;gBACnB,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,UAAU;gBACN,QAAQ;oBACJ,SAAS;oBACT,OAAO;gBACX;gBACA,OAAO;oBACH,SAAS;gBACb;gBACA,YAAY;oBACR,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;gBACA,gBAAgB;oBACZ,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;gBACA,SAAS;YACb;YACA,QAAQ;gBACJ,OAAO;gBACP,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,YAAY;gBACR,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,mBAAmB;gBACf,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,KAAK;gBACD,cAAc;gBACd,OAAO;oBACH,YAAY;wBACR,QAAQ;4BACJ,SAAS;wBACb;oBACJ;oBACA,gBAAgB;wBACZ,QAAQ;4BACJ,SAAS;wBACb;oBACJ;gBACJ;YACJ;YACA,YAAY;gBACR,cAAc;gBACd,OAAO;oBACH,YAAY;wBACR,QAAQ;4BACJ,SAAS;wBACb;oBACJ;oBACA,gBAAgB;wBACZ,QAAQ;4BACJ,SAAS;wBACb;oBACJ;gBACJ;gBACA,OAAO;oBACH,UAAU,2MAAA,CAAA,SAAM;gBACpB;YACJ;YACA,gBAAgB;gBACZ,cAAc;gBACd,OAAO;oBACH,YAAY;wBACR,QAAQ;4BACJ,SAAS;wBACb;oBACJ;oBACA,gBAAgB;wBACZ,QAAQ;4BACJ,SAAS;wBACb;oBACJ;gBACJ;gBACA,OAAO;oBACH,UAAU,2MAAA,CAAA,SAAM;gBACpB;YACJ;YACA,UAAU;gBACN,cAAc;gBACd,OAAO;oBACH,YAAY;wBACR,QAAQ;4BACJ,SAAS;wBACb;oBACJ;oBACA,gBAAgB;wBACZ,QAAQ;4BACJ,SAAS;wBACb;oBACJ;gBACJ;YACJ;YACA,WAAW;gBACP,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,iBAAiB;gBACb,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,QAAQ;gBACJ,SAAS;gBACT,OAAO;oBACH,YAAY;wBACR,QAAQ;4BACJ,SAAS;wBACb;oBACJ;oBACA,gBAAgB;wBACZ,QAAQ;4BACJ,SAAS;wBACb;oBACJ;gBACJ;YACJ;YACA,aAAa;gBACT,OAAO;gBACP,WAAW;oBACP,OAAO,2MAAA,CAAA,MAAG;gBACd;gBACA,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;oBACP,WAAW;gBACf;gBACA,OAAO;oBACH,QAAQ;wBACJ,SAAS;oBACb;gBACJ;YACJ;YACA,OAAO;gBACH,OAAO;gBACP,WAAW;oBACP,OAAO,2MAAA,CAAA,MAAG;gBACd;gBACA,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;oBACA,WAAW;gBACf;gBACA,gBAAgB;oBACZ,OAAO;oBACP,WAAW;gBACf;gBACA,OAAO;oBACH,QAAQ;wBACJ,SAAS;oBACb;gBACJ;YACJ;QACJ;QACA,WAAW;YACP,SAAS;YACT,OAAO;YACP,OAAO;YACP,WAAW,2MAAA,CAAA,QAAK;YAChB,OAAO;gBACH,SAAS;gBACT,MAAM;oBACF,OAAO,2MAAA,CAAA,QAAK;oBACZ,MAAM;gBACV;YACJ;YACA,cAAc;gBACV,SAAS;YACb;YACA,gBAAgB;gBACZ,SAAS;YACb;QACJ;QACA,oBAAoB;YAChB,qBAAqB;YACrB,uBAAuB;YACvB,YAAY;gBACR,OAAO;gBACP,OAAO;gBACP,MAAM;YACV;YACA,OAAO;gBACH,aAAa;gBACb,qBAAqB;gBACrB,gBAAgB;gBAChB,UAAU;gBACV,cAAc;YAClB;YACA,OAAO;gBACH,MAAM;oBACF,MAAM;gBACV;gBACA,QAAQ;gBACR,WAAW,2MAAA,CAAA,SAAM;YACrB;YACA,mBAAmB;gBACf,kBAAkB;gBAClB,kBAAkB;YACtB;QACJ;QACA,gBAAgB;YACZ,UAAU,2MAAA,CAAA,SAAM;YAChB,oBAAoB;YACpB,OAAO;gBACH,eAAe;gBACf,mBAAmB;gBACnB,WAAW,2MAAA,CAAA,SAAM;YACrB;YACA,YAAY;gBACR,OAAO;oBACH,qBAAqB,2MAAA,CAAA,SAAM;oBAC3B,mBAAmB,2MAAA,CAAA,MAAG;gBAC1B;YACJ;YACA,mBAAmB;gBACf,OAAO;oBACH,qBAAqB,2MAAA,CAAA,QAAK;oBAC1B,mBAAmB,2MAAA,CAAA,MAAG;gBAC1B;YACJ;YACA,eAAe,EAAE;QACrB;QACA,cAAc;YACV,UAAU,2MAAA,CAAA,OAAI;YACd,oBAAoB;YACpB,OAAO;gBACH,WAAW,2MAAA,CAAA,QAAK;YACpB;YACA,YAAY;gBACR,OAAO;oBACH,qBAAqB,2MAAA,CAAA,OAAI;oBACzB,mBAAmB,2MAAA,CAAA,SAAM;gBAC7B;YACJ;YACA,mBAAmB;gBACf,OAAO;oBACH,qBAAqB,2MAAA,CAAA,OAAI;oBACzB,mBAAmB,2MAAA,CAAA,MAAG;gBAC1B;YACJ;YACA,eAAe,EAAE;QACrB;QACA,cAAc;YACV,WAAW;YACX,UAAU;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;aAAE;QAC7B;QACA,WAAW;YACP,MAAM;gBACF,SAAS;YACb;YACA,mBAAmB;YACnB,mBAAmB;QACvB;QACA,oBAAoB;YAChB,iBAAiB,2MAAA,CAAA,OAAI;YACrB,QAAQ;gBACJ,OAAO,2MAAA,CAAA,aAAU;gBACjB,OAAO;gBACP,SAAS;gBACT,KAAK;gBACL,QAAQ;gBACR,MAAM;gBACN,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;YACpB;QACJ;QACA,WAAW;YACP,SAAS;YACT,QAAQ;YACR,OAAO;YACP,OAAO;QACX;QACA,cAAc;QACd,sBAAsB;QACtB,SAAS;QACT,sBAAsB;QACtB,gBAAgB;QAChB,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,YAAY;YACR,cAAc;gBACV,OAAO;gBACP,SAAS;YACb;YACA,QAAQ;YACR,iBAAiB;YACjB,oBAAoB;QACxB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 788, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/funnel.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/funnel.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    LEFT,\r\n    LIGHT_GREY,\r\n    RIGHT,\r\n    SOLID,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    funnel: {\r\n        sortData: true,\r\n        valueField: \"val\",\r\n        colorField: \"color\",\r\n        argumentField: \"arg\",\r\n        hoverEnabled: true,\r\n        selectionMode: \"single\",\r\n        item: {\r\n            border: {\r\n                visible: false,\r\n                width: 2,\r\n                color: WHITE\r\n            },\r\n            hoverStyle: {\r\n                hatching: {\r\n                    opacity: .75,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: RIGHT\r\n                },\r\n                border: {}\r\n            },\r\n            selectionStyle: {\r\n                hatching: {\r\n                    opacity: .5,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: RIGHT\r\n                },\r\n                border: {}\r\n            }\r\n        },\r\n        title: {\r\n            margin: 10\r\n        },\r\n        adaptiveLayout: {\r\n            width: 80,\r\n            height: 80,\r\n            keepLabels: true\r\n        },\r\n        legend: {\r\n            visible: false\r\n        },\r\n        _rtl: {\r\n            legend: {\r\n                itemTextPosition: LEFT\r\n            }\r\n        },\r\n        tooltip: {\r\n            customizeTooltip: info => ({\r\n                text: `${info.item.argument} ${info.valueText}`\r\n            })\r\n        },\r\n        inverted: false,\r\n        algorithm: \"dynamicSlope\",\r\n        neckWidth: 0,\r\n        neckHeight: 0,\r\n        resolveLabelOverlapping: \"shift\",\r\n        label: {\r\n            textOverflow: \"ellipsis\",\r\n            wordWrap: \"normal\",\r\n            visible: true,\r\n            horizontalAlignment: RIGHT,\r\n            horizontalOffset: 0,\r\n            verticalOffset: 0,\r\n            showForZeroValues: false,\r\n            customizeText: info => `${info.item.argument} ${info.valueText}`,\r\n            position: \"columns\",\r\n            font: {\r\n                color: WHITE\r\n            },\r\n            border: {\r\n                visible: false,\r\n                width: 1,\r\n                color: LIGHT_GREY,\r\n                dashStyle: SOLID\r\n            },\r\n            connector: {\r\n                visible: true,\r\n                width: 1,\r\n                opacity: .5\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAOe;IACX,QAAQ;QACJ,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,eAAe;QACf,cAAc;QACd,eAAe;QACf,MAAM;YACF,QAAQ;gBACJ,SAAS;gBACT,OAAO;gBACP,OAAO,2MAAA,CAAA,QAAK;YAChB;YACA,YAAY;gBACR,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW,2MAAA,CAAA,QAAK;gBACpB;gBACA,QAAQ,CAAC;YACb;YACA,gBAAgB;gBACZ,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW,2MAAA,CAAA,QAAK;gBACpB;gBACA,QAAQ,CAAC;YACb;QACJ;QACA,OAAO;YACH,QAAQ;QACZ;QACA,gBAAgB;YACZ,OAAO;YACP,QAAQ;YACR,YAAY;QAChB;QACA,QAAQ;YACJ,SAAS;QACb;QACA,MAAM;YACF,QAAQ;gBACJ,kBAAkB,2MAAA,CAAA,OAAI;YAC1B;QACJ;QACA,SAAS;YACL,kBAAkB,CAAA,OAAQ,CAAC;oBACvB,MAAM,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;gBACnD,CAAC;QACL;QACA,UAAU;QACV,WAAW;QACX,WAAW;QACX,YAAY;QACZ,yBAAyB;QACzB,OAAO;YACH,cAAc;YACd,UAAU;YACV,SAAS;YACT,qBAAqB,2MAAA,CAAA,QAAK;YAC1B,kBAAkB;YAClB,gBAAgB;YAChB,mBAAmB;YACnB,eAAe,CAAA,OAAQ,GAAG,KAAK,IAAI,CAAC,QAAQ,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE;YAChE,UAAU;YACV,MAAM;gBACF,OAAO,2MAAA,CAAA,QAAK;YAChB;YACA,QAAQ;gBACJ,SAAS;gBACT,OAAO;gBACP,OAAO,2MAAA,CAAA,aAAU;gBACjB,WAAW,2MAAA,CAAA,QAAK;YACpB;YACA,WAAW;gBACP,SAAS;gBACT,OAAO;gBACP,SAAS;YACb;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/gauge.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/gauge.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    BOTTOM,\r\n    CENTER,\r\n    LEFT,\r\n    NONE,\r\n    OUTSIDE,\r\n    RIGHT,\r\n    TOP,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    gauge: {\r\n        scale: {\r\n            tick: {\r\n                visible: true,\r\n                length: 5,\r\n                width: 2,\r\n                opacity: 1\r\n            },\r\n            minorTick: {\r\n                visible: false,\r\n                length: 3,\r\n                width: 1,\r\n                opacity: 1\r\n            },\r\n            label: {\r\n                visible: true,\r\n                alignment: CENTER,\r\n                hideFirstOrLast: \"last\",\r\n                overlappingBehavior: \"hide\"\r\n            },\r\n            position: TOP,\r\n            endOnTick: false\r\n        },\r\n        rangeContainer: {\r\n            offset: 0,\r\n            width: 5,\r\n            backgroundColor: \"#808080\"\r\n        },\r\n        valueIndicators: {\r\n            _default: {\r\n                color: \"#c2c2c2\"\r\n            },\r\n            rangebar: {\r\n                space: 2,\r\n                size: 10,\r\n                color: \"#cbc5cf\",\r\n                backgroundColor: NONE,\r\n                text: {\r\n                    indent: 0,\r\n                    font: {\r\n                        size: 14,\r\n                        color: null\r\n                    }\r\n                }\r\n            },\r\n            twocolorneedle: {\r\n                secondColor: \"#e18e92\"\r\n            },\r\n            trianglemarker: {\r\n                space: 2,\r\n                length: 14,\r\n                width: 13,\r\n                color: \"#8798a5\"\r\n            },\r\n            textcloud: {\r\n                arrowLength: 5,\r\n                horizontalOffset: 6,\r\n                verticalOffset: 3,\r\n                color: \"#679ec5\",\r\n                text: {\r\n                    font: {\r\n                        color: WHITE,\r\n                        size: 18\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        indicator: {\r\n            hasPositiveMeaning: true,\r\n            layout: {\r\n                horizontalAlignment: CENTER,\r\n                verticalAlignment: BOTTOM\r\n            },\r\n            text: {\r\n                font: {\r\n                    size: 18\r\n                }\r\n            }\r\n        },\r\n        _circular: {\r\n            scale: {\r\n                scaleDivisionFactor: 17,\r\n                orientation: OUTSIDE,\r\n                label: {\r\n                    indentFromTick: 10\r\n                }\r\n            },\r\n            rangeContainer: {\r\n                orientation: OUTSIDE\r\n            },\r\n            valueIndicatorType: \"rectangleneedle\",\r\n            subvalueIndicatorType: \"trianglemarker\",\r\n            valueIndicators: {\r\n                _type: \"rectangleneedle\",\r\n                _default: {\r\n                    offset: 20,\r\n                    indentFromCenter: 0,\r\n                    width: 2,\r\n                    spindleSize: 14,\r\n                    spindleGapSize: 10,\r\n                    beginAdaptingAtRadius: 50\r\n                },\r\n                triangleneedle: {\r\n                    width: 4\r\n                },\r\n                twocolorneedle: {\r\n                    space: 2,\r\n                    secondFraction: .4\r\n                },\r\n                rangebar: {\r\n                    offset: 30\r\n                },\r\n                trianglemarker: {\r\n                    offset: 6\r\n                },\r\n                textcloud: {\r\n                    offset: -6\r\n                }\r\n            }\r\n        },\r\n        _linear: {\r\n            scale: {\r\n                scaleDivisionFactor: 25,\r\n                horizontalOrientation: RIGHT,\r\n                verticalOrientation: BOTTOM,\r\n                label: {\r\n                    indentFromTick: -10\r\n                }\r\n            },\r\n            rangeContainer: {\r\n                horizontalOrientation: RIGHT,\r\n                verticalOrientation: BOTTOM\r\n            },\r\n            valueIndicatorType: \"rangebar\",\r\n            subvalueIndicatorType: \"trianglemarker\",\r\n            valueIndicators: {\r\n                _type: \"rectangle\",\r\n                _default: {\r\n                    offset: 2.5,\r\n                    length: 15,\r\n                    width: 15\r\n                },\r\n                rectangle: {\r\n                    width: 10\r\n                },\r\n                rangebar: {\r\n                    offset: 10,\r\n                    horizontalOrientation: RIGHT,\r\n                    verticalOrientation: BOTTOM\r\n                },\r\n                trianglemarker: {\r\n                    offset: 10,\r\n                    horizontalOrientation: LEFT,\r\n                    verticalOrientation: TOP\r\n                },\r\n                textcloud: {\r\n                    offset: -1,\r\n                    horizontalOrientation: LEFT,\r\n                    verticalOrientation: TOP\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAUe;IACX,OAAO;QACH,OAAO;YACH,MAAM;gBACF,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,SAAS;YACb;YACA,WAAW;gBACP,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,SAAS;YACb;YACA,OAAO;gBACH,SAAS;gBACT,WAAW,2MAAA,CAAA,SAAM;gBACjB,iBAAiB;gBACjB,qBAAqB;YACzB;YACA,UAAU,2MAAA,CAAA,MAAG;YACb,WAAW;QACf;QACA,gBAAgB;YACZ,QAAQ;YACR,OAAO;YACP,iBAAiB;QACrB;QACA,iBAAiB;YACb,UAAU;gBACN,OAAO;YACX;YACA,UAAU;gBACN,OAAO;gBACP,MAAM;gBACN,OAAO;gBACP,iBAAiB,2MAAA,CAAA,OAAI;gBACrB,MAAM;oBACF,QAAQ;oBACR,MAAM;wBACF,MAAM;wBACN,OAAO;oBACX;gBACJ;YACJ;YACA,gBAAgB;gBACZ,aAAa;YACjB;YACA,gBAAgB;gBACZ,OAAO;gBACP,QAAQ;gBACR,OAAO;gBACP,OAAO;YACX;YACA,WAAW;gBACP,aAAa;gBACb,kBAAkB;gBAClB,gBAAgB;gBAChB,OAAO;gBACP,MAAM;oBACF,MAAM;wBACF,OAAO,2MAAA,CAAA,QAAK;wBACZ,MAAM;oBACV;gBACJ;YACJ;QACJ;QACA,WAAW;YACP,oBAAoB;YACpB,QAAQ;gBACJ,qBAAqB,2MAAA,CAAA,SAAM;gBAC3B,mBAAmB,2MAAA,CAAA,SAAM;YAC7B;YACA,MAAM;gBACF,MAAM;oBACF,MAAM;gBACV;YACJ;QACJ;QACA,WAAW;YACP,OAAO;gBACH,qBAAqB;gBACrB,aAAa,2MAAA,CAAA,UAAO;gBACpB,OAAO;oBACH,gBAAgB;gBACpB;YACJ;YACA,gBAAgB;gBACZ,aAAa,2MAAA,CAAA,UAAO;YACxB;YACA,oBAAoB;YACpB,uBAAuB;YACvB,iBAAiB;gBACb,OAAO;gBACP,UAAU;oBACN,QAAQ;oBACR,kBAAkB;oBAClB,OAAO;oBACP,aAAa;oBACb,gBAAgB;oBAChB,uBAAuB;gBAC3B;gBACA,gBAAgB;oBACZ,OAAO;gBACX;gBACA,gBAAgB;oBACZ,OAAO;oBACP,gBAAgB;gBACpB;gBACA,UAAU;oBACN,QAAQ;gBACZ;gBACA,gBAAgB;oBACZ,QAAQ;gBACZ;gBACA,WAAW;oBACP,QAAQ,CAAC;gBACb;YACJ;QACJ;QACA,SAAS;YACL,OAAO;gBACH,qBAAqB;gBACrB,uBAAuB,2MAAA,CAAA,QAAK;gBAC5B,qBAAqB,2MAAA,CAAA,SAAM;gBAC3B,OAAO;oBACH,gBAAgB,CAAC;gBACrB;YACJ;YACA,gBAAgB;gBACZ,uBAAuB,2MAAA,CAAA,QAAK;gBAC5B,qBAAqB,2MAAA,CAAA,SAAM;YAC/B;YACA,oBAAoB;YACpB,uBAAuB;YACvB,iBAAiB;gBACb,OAAO;gBACP,UAAU;oBACN,QAAQ;oBACR,QAAQ;oBACR,OAAO;gBACX;gBACA,WAAW;oBACP,OAAO;gBACX;gBACA,UAAU;oBACN,QAAQ;oBACR,uBAAuB,2MAAA,CAAA,QAAK;oBAC5B,qBAAqB,2MAAA,CAAA,SAAM;gBAC/B;gBACA,gBAAgB;oBACZ,QAAQ;oBACR,uBAAuB,2MAAA,CAAA,OAAI;oBAC3B,qBAAqB,2MAAA,CAAA,MAAG;gBAC5B;gBACA,WAAW;oBACP,QAAQ,CAAC;oBACT,uBAAuB,2MAAA,CAAA,OAAI;oBAC3B,qBAAqB,2MAAA,CAAA,MAAG;gBAC5B;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1071, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/pie_chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/pie_chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    NONE,\r\n    RIGHT,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    pie: {\r\n        innerRadius: .5,\r\n        minDiameter: .5,\r\n        type: \"pie\",\r\n        dataPrepareSettings: {\r\n            _skipArgumentSorting: true\r\n        },\r\n        commonSeriesSettings: {\r\n            pie: {\r\n                border: {\r\n                    visible: false,\r\n                    width: 2,\r\n                    color: WHITE\r\n                },\r\n                hoverStyle: {\r\n                    hatching: {\r\n                        direction: RIGHT,\r\n                        width: 4,\r\n                        step: 10,\r\n                        opacity: .75\r\n                    },\r\n                    highlight: true,\r\n                    border: {\r\n                        visible: false,\r\n                        width: 2\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    hatching: {\r\n                        direction: RIGHT,\r\n                        width: 4,\r\n                        step: 10,\r\n                        opacity: .5\r\n                    },\r\n                    highlight: true,\r\n                    border: {\r\n                        visible: false,\r\n                        width: 2\r\n                    }\r\n                }\r\n            },\r\n            doughnut: {\r\n                border: {\r\n                    visible: false,\r\n                    width: 2,\r\n                    color: WHITE\r\n                },\r\n                hoverStyle: {\r\n                    hatching: {\r\n                        direction: RIGHT,\r\n                        width: 4,\r\n                        step: 10,\r\n                        opacity: .75\r\n                    },\r\n                    highlight: true,\r\n                    border: {\r\n                        visible: false,\r\n                        width: 2\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    hatching: {\r\n                        direction: RIGHT,\r\n                        width: 4,\r\n                        step: 10,\r\n                        opacity: .5\r\n                    },\r\n                    highlight: true,\r\n                    border: {\r\n                        visible: false,\r\n                        width: 2\r\n                    }\r\n                }\r\n            },\r\n            donut: {\r\n                border: {\r\n                    visible: false,\r\n                    width: 2,\r\n                    color: WHITE\r\n                },\r\n                hoverStyle: {\r\n                    hatching: {\r\n                        direction: RIGHT,\r\n                        width: 4,\r\n                        step: 10,\r\n                        opacity: .75\r\n                    },\r\n                    highlight: true,\r\n                    border: {\r\n                        visible: false,\r\n                        width: 2\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    hatching: {\r\n                        direction: RIGHT,\r\n                        width: 4,\r\n                        step: 10,\r\n                        opacity: .5\r\n                    },\r\n                    highlight: true,\r\n                    border: {\r\n                        visible: false,\r\n                        width: 2\r\n                    }\r\n                }\r\n            },\r\n            label: {\r\n                textOverflow: \"ellipsis\",\r\n                wordWrap: \"normal\"\r\n            }\r\n        },\r\n        legend: {\r\n            hoverMode: \"allArgumentPoints\",\r\n            backgroundColor: NONE\r\n        },\r\n        adaptiveLayout: {\r\n            keepLabels: false\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAKe;IACX,KAAK;QACD,aAAa;QACb,aAAa;QACb,MAAM;QACN,qBAAqB;YACjB,sBAAsB;QAC1B;QACA,sBAAsB;YAClB,KAAK;gBACD,QAAQ;oBACJ,SAAS;oBACT,OAAO;oBACP,OAAO,2MAAA,CAAA,QAAK;gBAChB;gBACA,YAAY;oBACR,UAAU;wBACN,WAAW,2MAAA,CAAA,QAAK;wBAChB,OAAO;wBACP,MAAM;wBACN,SAAS;oBACb;oBACA,WAAW;oBACX,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;gBACA,gBAAgB;oBACZ,UAAU;wBACN,WAAW,2MAAA,CAAA,QAAK;wBAChB,OAAO;wBACP,MAAM;wBACN,SAAS;oBACb;oBACA,WAAW;oBACX,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;YACJ;YACA,UAAU;gBACN,QAAQ;oBACJ,SAAS;oBACT,OAAO;oBACP,OAAO,2MAAA,CAAA,QAAK;gBAChB;gBACA,YAAY;oBACR,UAAU;wBACN,WAAW,2MAAA,CAAA,QAAK;wBAChB,OAAO;wBACP,MAAM;wBACN,SAAS;oBACb;oBACA,WAAW;oBACX,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;gBACA,gBAAgB;oBACZ,UAAU;wBACN,WAAW,2MAAA,CAAA,QAAK;wBAChB,OAAO;wBACP,MAAM;wBACN,SAAS;oBACb;oBACA,WAAW;oBACX,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;YACJ;YACA,OAAO;gBACH,QAAQ;oBACJ,SAAS;oBACT,OAAO;oBACP,OAAO,2MAAA,CAAA,QAAK;gBAChB;gBACA,YAAY;oBACR,UAAU;wBACN,WAAW,2MAAA,CAAA,QAAK;wBAChB,OAAO;wBACP,MAAM;wBACN,SAAS;oBACb;oBACA,WAAW;oBACX,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;gBACA,gBAAgB;oBACZ,UAAU;wBACN,WAAW,2MAAA,CAAA,QAAK;wBAChB,OAAO;wBACP,MAAM;wBACN,SAAS;oBACb;oBACA,WAAW;oBACX,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;gBACJ;YACJ;YACA,OAAO;gBACH,cAAc;gBACd,UAAU;YACd;QACJ;QACA,QAAQ;YACJ,WAAW;YACX,iBAAiB,2MAAA,CAAA,OAAI;QACzB;QACA,gBAAgB;YACZ,YAAY;QAChB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/polar_chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/polar_chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    CENTER,\r\n    NONE,\r\n    RIGHT,\r\n    SOLID,\r\n    TOP\r\n} from \"./contants\";\r\nexport default {\r\n    polar: {\r\n        commonSeriesSettings: {\r\n            type: \"scatter\",\r\n            closed: true,\r\n            point: {\r\n                visible: true,\r\n                symbol: \"circle\",\r\n                size: 12,\r\n                border: {\r\n                    visible: false,\r\n                    width: 1\r\n                },\r\n                hoverMode: \"onlyPoint\",\r\n                selectionMode: \"onlyPoint\",\r\n                hoverStyle: {\r\n                    border: {\r\n                        visible: true,\r\n                        width: 4\r\n                    },\r\n                    size: 12\r\n                },\r\n                selectionStyle: {\r\n                    border: {\r\n                        visible: true,\r\n                        width: 4\r\n                    },\r\n                    size: 12\r\n                }\r\n            },\r\n            scatter: {},\r\n            line: {\r\n                width: 2,\r\n                dashStyle: SOLID,\r\n                hoverStyle: {\r\n                    width: 3,\r\n                    hatching: {\r\n                        direction: NONE\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    width: 3\r\n                }\r\n            },\r\n            area: {\r\n                point: {\r\n                    visible: false\r\n                },\r\n                opacity: .5\r\n            },\r\n            stackedline: {\r\n                width: 2\r\n            },\r\n            bar: {\r\n                opacity: .8\r\n            },\r\n            stackedbar: {\r\n                opacity: .8\r\n            }\r\n        },\r\n        adaptiveLayout: {\r\n            width: 80,\r\n            height: 80,\r\n            keepLabels: true\r\n        },\r\n        barGroupPadding: .3,\r\n        commonAxisSettings: {\r\n            visible: true,\r\n            forceUserTickInterval: false,\r\n            label: {\r\n                overlappingBehavior: \"hide\",\r\n                indentFromAxis: 5\r\n            },\r\n            grid: {\r\n                visible: true\r\n            },\r\n            minorGrid: {\r\n                visible: true\r\n            },\r\n            tick: {\r\n                visible: true\r\n            },\r\n            title: {\r\n                font: {\r\n                    size: 16\r\n                },\r\n                margin: 10\r\n            }\r\n        },\r\n        argumentAxis: {\r\n            startAngle: 0,\r\n            firstPointOnStartAngle: false,\r\n            period: void 0\r\n        },\r\n        valueAxis: {\r\n            endOnTick: false,\r\n            tick: {\r\n                visible: false\r\n            }\r\n        },\r\n        horizontalAxis: {\r\n            position: TOP,\r\n            axisDivisionFactor: 50,\r\n            label: {\r\n                alignment: CENTER\r\n            }\r\n        },\r\n        verticalAxis: {\r\n            position: TOP,\r\n            axisDivisionFactor: 30,\r\n            label: {\r\n                alignment: RIGHT\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAOe;IACX,OAAO;QACH,sBAAsB;YAClB,MAAM;YACN,QAAQ;YACR,OAAO;gBACH,SAAS;gBACT,QAAQ;gBACR,MAAM;gBACN,QAAQ;oBACJ,SAAS;oBACT,OAAO;gBACX;gBACA,WAAW;gBACX,eAAe;gBACf,YAAY;oBACR,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;oBACA,MAAM;gBACV;gBACA,gBAAgB;oBACZ,QAAQ;wBACJ,SAAS;wBACT,OAAO;oBACX;oBACA,MAAM;gBACV;YACJ;YACA,SAAS,CAAC;YACV,MAAM;gBACF,OAAO;gBACP,WAAW,2MAAA,CAAA,QAAK;gBAChB,YAAY;oBACR,OAAO;oBACP,UAAU;wBACN,WAAW,2MAAA,CAAA,OAAI;oBACnB;gBACJ;gBACA,gBAAgB;oBACZ,OAAO;gBACX;YACJ;YACA,MAAM;gBACF,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;YACb;YACA,aAAa;gBACT,OAAO;YACX;YACA,KAAK;gBACD,SAAS;YACb;YACA,YAAY;gBACR,SAAS;YACb;QACJ;QACA,gBAAgB;YACZ,OAAO;YACP,QAAQ;YACR,YAAY;QAChB;QACA,iBAAiB;QACjB,oBAAoB;YAChB,SAAS;YACT,uBAAuB;YACvB,OAAO;gBACH,qBAAqB;gBACrB,gBAAgB;YACpB;YACA,MAAM;gBACF,SAAS;YACb;YACA,WAAW;gBACP,SAAS;YACb;YACA,MAAM;gBACF,SAAS;YACb;YACA,OAAO;gBACH,MAAM;oBACF,MAAM;gBACV;gBACA,QAAQ;YACZ;QACJ;QACA,cAAc;YACV,YAAY;YACZ,wBAAwB;YACxB,QAAQ,KAAK;QACjB;QACA,WAAW;YACP,WAAW;YACX,MAAM;gBACF,SAAS;YACb;QACJ;QACA,gBAAgB;YACZ,UAAU,2MAAA,CAAA,MAAG;YACb,oBAAoB;YACpB,OAAO;gBACH,WAAW,2MAAA,CAAA,SAAM;YACrB;QACJ;QACA,cAAc;YACV,UAAU,2MAAA,CAAA,MAAG;YACb,oBAAoB;YACpB,OAAO;gBACH,WAAW,2MAAA,CAAA,QAAK;YACpB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1341, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/range_selector.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/range_selector.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    BLACK,\r\n    CENTER,\r\n    RED,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    rangeSelector: {\r\n        scale: {\r\n            valueMarginsEnabled: true,\r\n            width: 1,\r\n            color: BLACK,\r\n            opacity: .1,\r\n            showCustomBoundaryTicks: true,\r\n            label: {\r\n                overlappingBehavior: \"hide\",\r\n                alignment: CENTER,\r\n                visible: true,\r\n                topIndent: 7,\r\n                font: {\r\n                    size: 11\r\n                }\r\n            },\r\n            tick: {\r\n                width: 1,\r\n                color: BLACK,\r\n                opacity: .17,\r\n                visible: true,\r\n                length: 12\r\n            },\r\n            minorTick: {\r\n                width: 1,\r\n                color: BLACK,\r\n                opacity: .05,\r\n                visible: true,\r\n                length: 12\r\n            },\r\n            marker: {\r\n                width: 1,\r\n                color: \"#000000\",\r\n                opacity: .1,\r\n                visible: true,\r\n                separatorHeight: 33,\r\n                topIndent: 10,\r\n                textLeftIndent: 7,\r\n                textTopIndent: 11,\r\n                label: {}\r\n            },\r\n            logarithmBase: 10,\r\n            workWeek: [1, 2, 3, 4, 5],\r\n            breakStyle: {\r\n                width: 5,\r\n                color: \"#ababab\",\r\n                line: \"waved\"\r\n            },\r\n            endOnTick: false\r\n        },\r\n        selectedRangeColor: \"#606060\",\r\n        sliderMarker: {\r\n            visible: true,\r\n            paddingTopBottom: 2,\r\n            paddingLeftRight: 4,\r\n            color: \"#606060\",\r\n            invalidRangeColor: RED,\r\n            font: {\r\n                color: WHITE,\r\n                size: 11\r\n            }\r\n        },\r\n        sliderHandle: {\r\n            width: 1,\r\n            color: BLACK,\r\n            opacity: .2\r\n        },\r\n        shutter: {\r\n            opacity: .75\r\n        },\r\n        background: {\r\n            color: \"#c0bae1\",\r\n            visible: true,\r\n            image: {\r\n                location: \"full\"\r\n            }\r\n        },\r\n        behavior: {\r\n            snapToTicks: true,\r\n            animationEnabled: true,\r\n            moveSelectedRangeByClick: true,\r\n            manualRangeSelectionEnabled: true,\r\n            allowSlidersSwap: true,\r\n            valueChangeMode: \"onHandleRelease\"\r\n        },\r\n        redrawOnResize: true,\r\n        chart: {\r\n            barGroupPadding: .3,\r\n            minBubbleSize: 12,\r\n            maxBubbleSize: .2,\r\n            topIndent: .1,\r\n            bottomIndent: 0,\r\n            valueAxis: {\r\n                inverted: false,\r\n                logarithmBase: 10\r\n            },\r\n            commonSeriesSettings: {\r\n                type: \"area\",\r\n                aggregation: {\r\n                    enabled: void 0\r\n                },\r\n                point: {\r\n                    visible: false\r\n                },\r\n                scatter: {\r\n                    point: {\r\n                        visible: true\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAMe;IACX,eAAe;QACX,OAAO;YACH,qBAAqB;YACrB,OAAO;YACP,OAAO,2MAAA,CAAA,QAAK;YACZ,SAAS;YACT,yBAAyB;YACzB,OAAO;gBACH,qBAAqB;gBACrB,WAAW,2MAAA,CAAA,SAAM;gBACjB,SAAS;gBACT,WAAW;gBACX,MAAM;oBACF,MAAM;gBACV;YACJ;YACA,MAAM;gBACF,OAAO;gBACP,OAAO,2MAAA,CAAA,QAAK;gBACZ,SAAS;gBACT,SAAS;gBACT,QAAQ;YACZ;YACA,WAAW;gBACP,OAAO;gBACP,OAAO,2MAAA,CAAA,QAAK;gBACZ,SAAS;gBACT,SAAS;gBACT,QAAQ;YACZ;YACA,QAAQ;gBACJ,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,SAAS;gBACT,iBAAiB;gBACjB,WAAW;gBACX,gBAAgB;gBAChB,eAAe;gBACf,OAAO,CAAC;YACZ;YACA,eAAe;YACf,UAAU;gBAAC;gBAAG;gBAAG;gBAAG;gBAAG;aAAE;YACzB,YAAY;gBACR,OAAO;gBACP,OAAO;gBACP,MAAM;YACV;YACA,WAAW;QACf;QACA,oBAAoB;QACpB,cAAc;YACV,SAAS;YACT,kBAAkB;YAClB,kBAAkB;YAClB,OAAO;YACP,mBAAmB,2MAAA,CAAA,MAAG;YACtB,MAAM;gBACF,OAAO,2MAAA,CAAA,QAAK;gBACZ,MAAM;YACV;QACJ;QACA,cAAc;YACV,OAAO;YACP,OAAO,2MAAA,CAAA,QAAK;YACZ,SAAS;QACb;QACA,SAAS;YACL,SAAS;QACb;QACA,YAAY;YACR,OAAO;YACP,SAAS;YACT,OAAO;gBACH,UAAU;YACd;QACJ;QACA,UAAU;YACN,aAAa;YACb,kBAAkB;YAClB,0BAA0B;YAC1B,6BAA6B;YAC7B,kBAAkB;YAClB,iBAAiB;QACrB;QACA,gBAAgB;QAChB,OAAO;YACH,iBAAiB;YACjB,eAAe;YACf,eAAe;YACf,WAAW;YACX,cAAc;YACd,WAAW;gBACP,UAAU;gBACV,eAAe;YACnB;YACA,sBAAsB;gBAClB,MAAM;gBACN,aAAa;oBACT,SAAS,KAAK;gBAClB;gBACA,OAAO;oBACH,SAAS;gBACb;gBACA,SAAS;oBACL,OAAO;wBACH,SAAS;oBACb;gBACJ;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1477, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/sankey.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/sankey.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    BLACK,\r\n    CENTER,\r\n    RIGHT,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    sankey: {\r\n        sourceField: \"source\",\r\n        targetField: \"target\",\r\n        weightField: \"weight\",\r\n        hoverEnabled: true,\r\n        alignment: CENTER,\r\n        adaptiveLayout: {\r\n            width: 80,\r\n            height: 80,\r\n            keepLabels: true\r\n        },\r\n        label: {\r\n            visible: true,\r\n            horizontalOffset: 8,\r\n            verticalOffset: 0,\r\n            overlappingBehavior: \"ellipsis\",\r\n            useNodeColors: false,\r\n            font: {\r\n                color: BLACK,\r\n                weight: 500\r\n            },\r\n            border: {\r\n                visible: false,\r\n                width: 2,\r\n                color: WHITE\r\n            },\r\n            customizeText: info => info.label,\r\n            shadow: {\r\n                opacity: .2,\r\n                offsetX: 0,\r\n                offsetY: 1,\r\n                blur: 1,\r\n                color: WHITE\r\n            }\r\n        },\r\n        title: {\r\n            margin: 10,\r\n            font: {\r\n                size: 28,\r\n                weight: 200\r\n            },\r\n            subtitle: {\r\n                font: {\r\n                    size: 16\r\n                }\r\n            }\r\n        },\r\n        tooltip: {\r\n            enabled: true\r\n        },\r\n        node: {\r\n            padding: 30,\r\n            width: 8,\r\n            opacity: 1,\r\n            border: {\r\n                color: WHITE,\r\n                width: 1,\r\n                visible: false\r\n            },\r\n            hoverStyle: {\r\n                hatching: {\r\n                    opacity: .75,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: RIGHT\r\n                },\r\n                border: {}\r\n            }\r\n        },\r\n        link: {\r\n            color: \"#888888\",\r\n            colorMode: \"none\",\r\n            opacity: .3,\r\n            border: {\r\n                color: WHITE,\r\n                width: 1,\r\n                visible: false\r\n            },\r\n            hoverStyle: {\r\n                opacity: .5,\r\n                hatching: {\r\n                    opacity: .75,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: RIGHT\r\n                },\r\n                border: {}\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAMe;IACX,QAAQ;QACJ,aAAa;QACb,aAAa;QACb,aAAa;QACb,cAAc;QACd,WAAW,2MAAA,CAAA,SAAM;QACjB,gBAAgB;YACZ,OAAO;YACP,QAAQ;YACR,YAAY;QAChB;QACA,OAAO;YACH,SAAS;YACT,kBAAkB;YAClB,gBAAgB;YAChB,qBAAqB;YACrB,eAAe;YACf,MAAM;gBACF,OAAO,2MAAA,CAAA,QAAK;gBACZ,QAAQ;YACZ;YACA,QAAQ;gBACJ,SAAS;gBACT,OAAO;gBACP,OAAO,2MAAA,CAAA,QAAK;YAChB;YACA,eAAe,CAAA,OAAQ,KAAK,KAAK;YACjC,QAAQ;gBACJ,SAAS;gBACT,SAAS;gBACT,SAAS;gBACT,MAAM;gBACN,OAAO,2MAAA,CAAA,QAAK;YAChB;QACJ;QACA,OAAO;YACH,QAAQ;YACR,MAAM;gBACF,MAAM;gBACN,QAAQ;YACZ;YACA,UAAU;gBACN,MAAM;oBACF,MAAM;gBACV;YACJ;QACJ;QACA,SAAS;YACL,SAAS;QACb;QACA,MAAM;YACF,SAAS;YACT,OAAO;YACP,SAAS;YACT,QAAQ;gBACJ,OAAO,2MAAA,CAAA,QAAK;gBACZ,OAAO;gBACP,SAAS;YACb;YACA,YAAY;gBACR,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW,2MAAA,CAAA,QAAK;gBACpB;gBACA,QAAQ,CAAC;YACb;QACJ;QACA,MAAM;YACF,OAAO;YACP,WAAW;YACX,SAAS;YACT,QAAQ;gBACJ,OAAO,2MAAA,CAAA,QAAK;gBACZ,OAAO;gBACP,SAAS;YACb;YACA,YAAY;gBACR,SAAS;gBACT,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW,2MAAA,CAAA,QAAK;gBACpB;gBACA,QAAQ,CAAC;YACb;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/sparkline.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/sparkline.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    sparkline: {\r\n        lineColor: \"#666666\",\r\n        lineWidth: 2,\r\n        areaOpacity: .2,\r\n        minColor: \"#e8c267\",\r\n        maxColor: \"#e55253\",\r\n        barPositiveColor: \"#a9a9a9\",\r\n        barNegativeColor: \"#d7d7d7\",\r\n        winColor: \"#a9a9a9\",\r\n        lossColor: \"#d7d7d7\",\r\n        firstLastColor: \"#666666\",\r\n        pointSymbol: \"circle\",\r\n        pointColor: WHITE,\r\n        pointSize: 4,\r\n        type: \"line\",\r\n        argumentField: \"arg\",\r\n        valueField: \"val\",\r\n        winlossThreshold: 0,\r\n        showFirstLast: true,\r\n        showMinMax: false,\r\n        tooltip: {\r\n            enabled: true\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe;IACX,WAAW;QACP,WAAW;QACX,WAAW;QACX,aAAa;QACb,UAAU;QACV,UAAU;QACV,kBAAkB;QAClB,kBAAkB;QAClB,UAAU;QACV,WAAW;QACX,gBAAgB;QAChB,aAAa;QACb,YAAY,2MAAA,CAAA,QAAK;QACjB,WAAW;QACX,MAAM;QACN,eAAe;QACf,YAAY;QACZ,kBAAkB;QAClB,eAAe;QACf,YAAY;QACZ,SAAS;YACL,SAAS;QACb;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1627, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/tree_map.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/tree_map.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    SECONDARY_TITLE_COLOR\r\n} from \"./contants\";\r\nexport default {\r\n    treeMap: {\r\n        tile: {\r\n            border: {\r\n                width: 1,\r\n                opacity: .2,\r\n                color: \"#000000\"\r\n            },\r\n            color: \"#5f8b95\",\r\n            hoverStyle: {\r\n                hatching: {\r\n                    opacity: .75,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: \"right\"\r\n                },\r\n                border: {}\r\n            },\r\n            selectionStyle: {\r\n                hatching: {\r\n                    opacity: .5,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: \"right\"\r\n                },\r\n                border: {\r\n                    opacity: 1\r\n                }\r\n            },\r\n            label: {\r\n                visible: true,\r\n                paddingLeftRight: 5,\r\n                paddingTopBottom: 4,\r\n                font: {\r\n                    color: \"#ffffff\",\r\n                    weight: 600\r\n                },\r\n                shadow: {\r\n                    opacity: .6,\r\n                    offsetX: 0,\r\n                    offsetY: 1,\r\n                    blur: 2,\r\n                    color: \"#000000\"\r\n                },\r\n                wordWrap: \"normal\",\r\n                textOverflow: \"ellipsis\"\r\n            }\r\n        },\r\n        group: {\r\n            padding: 4,\r\n            border: {\r\n                width: 1\r\n            },\r\n            color: \"#eeeeee\",\r\n            hoverStyle: {\r\n                hatching: {\r\n                    opacity: 0,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: \"right\"\r\n                },\r\n                border: {}\r\n            },\r\n            selectionStyle: {\r\n                hatching: {\r\n                    opacity: 0,\r\n                    step: 6,\r\n                    width: 2,\r\n                    direction: \"right\"\r\n                },\r\n                border: {}\r\n            },\r\n            label: {\r\n                visible: true,\r\n                paddingLeftRight: 5,\r\n                paddingTopBottom: 4,\r\n                font: {\r\n                    color: SECONDARY_TITLE_COLOR,\r\n                    weight: 600\r\n                },\r\n                textOverflow: \"ellipsis\"\r\n            }\r\n        },\r\n        title: {\r\n            subtitle: {}\r\n        },\r\n        tooltip: {},\r\n        loadingIndicator: {}\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCAGe;IACX,SAAS;QACL,MAAM;YACF,QAAQ;gBACJ,OAAO;gBACP,SAAS;gBACT,OAAO;YACX;YACA,OAAO;YACP,YAAY;gBACR,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW;gBACf;gBACA,QAAQ,CAAC;YACb;YACA,gBAAgB;gBACZ,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW;gBACf;gBACA,QAAQ;oBACJ,SAAS;gBACb;YACJ;YACA,OAAO;gBACH,SAAS;gBACT,kBAAkB;gBAClB,kBAAkB;gBAClB,MAAM;oBACF,OAAO;oBACP,QAAQ;gBACZ;gBACA,QAAQ;oBACJ,SAAS;oBACT,SAAS;oBACT,SAAS;oBACT,MAAM;oBACN,OAAO;gBACX;gBACA,UAAU;gBACV,cAAc;YAClB;QACJ;QACA,OAAO;YACH,SAAS;YACT,QAAQ;gBACJ,OAAO;YACX;YACA,OAAO;YACP,YAAY;gBACR,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW;gBACf;gBACA,QAAQ,CAAC;YACb;YACA,gBAAgB;gBACZ,UAAU;oBACN,SAAS;oBACT,MAAM;oBACN,OAAO;oBACP,WAAW;gBACf;gBACA,QAAQ,CAAC;YACb;YACA,OAAO;gBACH,SAAS;gBACT,kBAAkB;gBAClB,kBAAkB;gBAClB,MAAM;oBACF,OAAO,2MAAA,CAAA,wBAAqB;oBAC5B,QAAQ;gBACZ;gBACA,cAAc;YAClB;QACJ;QACA,OAAO;YACH,UAAU,CAAC;QACf;QACA,SAAS,CAAC;QACV,kBAAkB,CAAC;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/vector_map.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/vector_map.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    BOTTOM,\r\n    GREY_GREEN,\r\n    INSIDE,\r\n    LEFT,\r\n    RIGHT,\r\n    SOME_GREY,\r\n    WHITE\r\n} from \"./contants\";\r\nexport default {\r\n    map: {\r\n        title: {\r\n            margin: 10\r\n        },\r\n        background: {\r\n            borderWidth: 1,\r\n            borderColor: \"#cacaca\"\r\n        },\r\n        layer: {\r\n            label: {\r\n                enabled: false,\r\n                stroke: WHITE,\r\n                \"stroke-width\": 1,\r\n                \"stroke-opacity\": .7,\r\n                font: {\r\n                    color: SOME_GREY,\r\n                    size: 12\r\n                }\r\n            }\r\n        },\r\n        \"layer:area\": {\r\n            borderWidth: 1,\r\n            borderColor: WHITE,\r\n            color: \"#d2d2d2\",\r\n            hoveredBorderColor: GREY_GREEN,\r\n            selectedBorderWidth: 2,\r\n            selectedBorderColor: <PERSON><PERSON><PERSON>_<PERSON>RE<PERSON>,\r\n            label: {\r\n                \"stroke-width\": 2,\r\n                font: {\r\n                    size: 16\r\n                }\r\n            }\r\n        },\r\n        \"layer:line\": {\r\n            borderWidth: 2,\r\n            color: \"#ba8365\",\r\n            hoveredColor: \"#a94813\",\r\n            selectedBorderWidth: 3,\r\n            selectedColor: \"#e55100\",\r\n            label: {\r\n                \"stroke-width\": 2,\r\n                font: {\r\n                    size: 16\r\n                }\r\n            }\r\n        },\r\n        \"layer:marker\": {\r\n            label: {\r\n                enabled: true,\r\n                \"stroke-width\": 1,\r\n                font: {\r\n                    size: 12\r\n                }\r\n            }\r\n        },\r\n        \"layer:marker:dot\": {\r\n            borderWidth: 2,\r\n            borderColor: WHITE,\r\n            size: 8,\r\n            selectedStep: 2,\r\n            backStep: 18,\r\n            backColor: WHITE,\r\n            backOpacity: .32,\r\n            shadow: true\r\n        },\r\n        \"layer:marker:bubble\": {\r\n            minSize: 20,\r\n            maxSize: 50,\r\n            hoveredBorderWidth: 1,\r\n            hoveredBorderColor: GREY_GREEN,\r\n            selectedBorderWidth: 2,\r\n            selectedBorderColor: GREY_GREEN\r\n        },\r\n        \"layer:marker:pie\": {\r\n            size: 50,\r\n            hoveredBorderWidth: 1,\r\n            hoveredBorderColor: GREY_GREEN,\r\n            selectedBorderWidth: 2,\r\n            selectedBorderColor: GREY_GREEN\r\n        },\r\n        \"layer:marker:image\": {\r\n            size: 20\r\n        },\r\n        legend: {\r\n            verticalAlignment: BOTTOM,\r\n            horizontalAlignment: RIGHT,\r\n            position: INSIDE,\r\n            backgroundOpacity: .65,\r\n            border: {\r\n                visible: true\r\n            },\r\n            paddingLeftRight: 16,\r\n            paddingTopBottom: 12\r\n        },\r\n        controlBar: {\r\n            borderColor: \"#5d5d5d\",\r\n            borderWidth: 3,\r\n            color: WHITE,\r\n            margin: 20,\r\n            opacity: .3\r\n        },\r\n        _rtl: {\r\n            legend: {\r\n                itemTextPosition: LEFT\r\n            }\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCASe;IACX,KAAK;QACD,OAAO;YACH,QAAQ;QACZ;QACA,YAAY;YACR,aAAa;YACb,aAAa;QACjB;QACA,OAAO;YACH,OAAO;gBACH,SAAS;gBACT,QAAQ,2MAAA,CAAA,QAAK;gBACb,gBAAgB;gBAChB,kBAAkB;gBAClB,MAAM;oBACF,OAAO,2MAAA,CAAA,YAAS;oBAChB,MAAM;gBACV;YACJ;QACJ;QACA,cAAc;YACV,aAAa;YACb,aAAa,2MAAA,CAAA,QAAK;YAClB,OAAO;YACP,oBAAoB,2MAAA,CAAA,aAAU;YAC9B,qBAAqB;YACrB,qBAAqB,2MAAA,CAAA,aAAU;YAC/B,OAAO;gBACH,gBAAgB;gBAChB,MAAM;oBACF,MAAM;gBACV;YACJ;QACJ;QACA,cAAc;YACV,aAAa;YACb,OAAO;YACP,cAAc;YACd,qBAAqB;YACrB,eAAe;YACf,OAAO;gBACH,gBAAgB;gBAChB,MAAM;oBACF,MAAM;gBACV;YACJ;QACJ;QACA,gBAAgB;YACZ,OAAO;gBACH,SAAS;gBACT,gBAAgB;gBAChB,MAAM;oBACF,MAAM;gBACV;YACJ;QACJ;QACA,oBAAoB;YAChB,aAAa;YACb,aAAa,2MAAA,CAAA,QAAK;YAClB,MAAM;YACN,cAAc;YACd,UAAU;YACV,WAAW,2MAAA,CAAA,QAAK;YAChB,aAAa;YACb,QAAQ;QACZ;QACA,uBAAuB;YACnB,SAAS;YACT,SAAS;YACT,oBAAoB;YACpB,oBAAoB,2MAAA,CAAA,aAAU;YAC9B,qBAAqB;YACrB,qBAAqB,2MAAA,CAAA,aAAU;QACnC;QACA,oBAAoB;YAChB,MAAM;YACN,oBAAoB;YACpB,oBAAoB,2MAAA,CAAA,aAAU;YAC9B,qBAAqB;YACrB,qBAAqB,2MAAA,CAAA,aAAU;QACnC;QACA,sBAAsB;YAClB,MAAM;QACV;QACA,QAAQ;YACJ,mBAAmB,2MAAA,CAAA,SAAM;YACzB,qBAAqB,2MAAA,CAAA,QAAK;YAC1B,UAAU,2MAAA,CAAA,SAAM;YAChB,mBAAmB;YACnB,QAAQ;gBACJ,SAAS;YACb;YACA,kBAAkB;YAClB,kBAAkB;QACtB;QACA,YAAY;YACR,aAAa;YACb,aAAa;YACb,OAAO,2MAAA,CAAA,QAAK;YACZ,QAAQ;YACR,SAAS;QACb;QACA,MAAM;YACF,QAAQ;gBACJ,kBAAkB,2MAAA,CAAA,OAAI;YAC1B;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1859, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/index.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/light/index.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport bar_gauge from \"./bar_gauge\";\r\nimport bullet from \"./bullet\";\r\nimport chart from \"./chart\";\r\nimport {\r\n    BLACK,\r\n    CENTER,\r\n    LIGHT_GREY,\r\n    OUTSIDE,\r\n    PRIMARY_TITLE_COLOR,\r\n    RIGHT,\r\n    SECONDARY_TITLE_COLOR,\r\n    SOLID,\r\n    TOP,\r\n    WHITE\r\n} from \"./contants\";\r\nimport funnel from \"./funnel\";\r\nimport gauge from \"./gauge\";\r\nimport pieChart from \"./pie_chart\";\r\nimport polarChart from \"./polar_chart\";\r\nimport rangeSelector from \"./range_selector\";\r\nimport sankey from \"./sankey\";\r\nimport sparkline from \"./sparkline\";\r\nimport treeMap from \"./tree_map\";\r\nimport vectorMap from \"./vector_map\";\r\nexport default [{\r\n    baseThemeName: void 0,\r\n    theme: _extends({\r\n        name: \"generic.light\",\r\n        isDefault: true,\r\n        font: {\r\n            color: SECONDARY_TITLE_COLOR,\r\n            family: \"'Segoe UI', 'Helvetica Neue', 'Trebuchet MS', Verdana, sans-serif\",\r\n            weight: 400,\r\n            size: 12,\r\n            cursor: \"default\"\r\n        },\r\n        redrawOnResize: true,\r\n        backgroundColor: WHITE,\r\n        primaryTitleColor: PRIMARY_TITLE_COLOR,\r\n        secondaryTitleColor: SECONDARY_TITLE_COLOR,\r\n        gridColor: LIGHT_GREY,\r\n        axisColor: SECONDARY_TITLE_COLOR,\r\n        title: {\r\n            backgroundColor: WHITE,\r\n            font: {\r\n                size: 28,\r\n                family: \"'Segoe UI Light', 'Helvetica Neue Light', 'Segoe UI', 'Helvetica Neue', 'Trebuchet MS', Verdana, sans-serif\",\r\n                weight: 200\r\n            },\r\n            subtitle: {\r\n                font: {\r\n                    size: 16\r\n                },\r\n                offset: 0,\r\n                wordWrap: \"normal\",\r\n                textOverflow: \"ellipsis\"\r\n            },\r\n            wordWrap: \"normal\",\r\n            textOverflow: \"ellipsis\"\r\n        },\r\n        loadingIndicator: {\r\n            text: \"Loading...\"\r\n        },\r\n        export: {\r\n            backgroundColor: WHITE,\r\n            margin: 10,\r\n            font: {\r\n                size: 14,\r\n                color: PRIMARY_TITLE_COLOR,\r\n                weight: 400\r\n            },\r\n            button: {\r\n                margin: {\r\n                    top: 8,\r\n                    left: 10,\r\n                    right: 10,\r\n                    bottom: 8\r\n                },\r\n                default: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#ddd\",\r\n                    backgroundColor: WHITE\r\n                },\r\n                hover: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#bebebe\",\r\n                    backgroundColor: \"#e6e6e6\"\r\n                },\r\n                focus: {\r\n                    color: BLACK,\r\n                    borderColor: \"#9d9d9d\",\r\n                    backgroundColor: \"#e6e6e6\"\r\n                },\r\n                active: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#9d9d9d\",\r\n                    backgroundColor: \"#d4d4d4\"\r\n                }\r\n            },\r\n            shadowColor: LIGHT_GREY\r\n        },\r\n        tooltip: {\r\n            enabled: false,\r\n            border: {\r\n                width: 1,\r\n                color: LIGHT_GREY,\r\n                dashStyle: SOLID,\r\n                visible: true\r\n            },\r\n            font: {\r\n                color: PRIMARY_TITLE_COLOR\r\n            },\r\n            color: WHITE,\r\n            arrowLength: 10,\r\n            paddingLeftRight: 18,\r\n            paddingTopBottom: 15,\r\n            textAlignment: \"center\",\r\n            shared: false,\r\n            location: CENTER,\r\n            shadow: {\r\n                opacity: .4,\r\n                offsetX: 0,\r\n                offsetY: 4,\r\n                blur: 2,\r\n                color: BLACK\r\n            },\r\n            interactive: false\r\n        },\r\n        legend: {\r\n            hoverMode: \"includePoints\",\r\n            verticalAlignment: TOP,\r\n            horizontalAlignment: RIGHT,\r\n            position: OUTSIDE,\r\n            visible: true,\r\n            margin: 10,\r\n            markerSize: 12,\r\n            border: {\r\n                visible: false,\r\n                width: 1,\r\n                cornerRadius: 0,\r\n                dashStyle: SOLID\r\n            },\r\n            paddingLeftRight: 20,\r\n            paddingTopBottom: 15,\r\n            columnCount: 0,\r\n            rowCount: 0,\r\n            columnItemSpacing: 20,\r\n            rowItemSpacing: 8,\r\n            title: {\r\n                backgroundColor: WHITE,\r\n                margin: {\r\n                    left: 0,\r\n                    bottom: 9,\r\n                    right: 0,\r\n                    top: 0\r\n                },\r\n                font: {\r\n                    size: 18,\r\n                    weight: 200\r\n                },\r\n                subtitle: {\r\n                    offset: 0,\r\n                    font: {\r\n                        size: 14\r\n                    },\r\n                    wordWrap: \"none\",\r\n                    textOverflow: \"ellipsis\"\r\n                },\r\n                wordWrap: \"none\",\r\n                textOverflow: \"ellipsis\"\r\n            }\r\n        }\r\n    }, chart, funnel, gauge, bar_gauge, vectorMap, pieChart, polarChart, rangeSelector, sankey, sparkline, bullet, treeMap)\r\n}, {\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"generic.light.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AACA;AACA;AAYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;uCACe;IAAC;QACZ,eAAe,KAAK;QACpB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;YACZ,MAAM;YACN,WAAW;YACX,MAAM;gBACF,OAAO,2MAAA,CAAA,wBAAqB;gBAC5B,QAAQ;gBACR,QAAQ;gBACR,MAAM;gBACN,QAAQ;YACZ;YACA,gBAAgB;YAChB,iBAAiB,2MAAA,CAAA,QAAK;YACtB,mBAAmB,2MAAA,CAAA,sBAAmB;YACtC,qBAAqB,2MAAA,CAAA,wBAAqB;YAC1C,WAAW,2MAAA,CAAA,aAAU;YACrB,WAAW,2MAAA,CAAA,wBAAqB;YAChC,OAAO;gBACH,iBAAiB,2MAAA,CAAA,QAAK;gBACtB,MAAM;oBACF,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACZ;gBACA,UAAU;oBACN,MAAM;wBACF,MAAM;oBACV;oBACA,QAAQ;oBACR,UAAU;oBACV,cAAc;gBAClB;gBACA,UAAU;gBACV,cAAc;YAClB;YACA,kBAAkB;gBACd,MAAM;YACV;YACA,QAAQ;gBACJ,iBAAiB,2MAAA,CAAA,QAAK;gBACtB,QAAQ;gBACR,MAAM;oBACF,MAAM;oBACN,OAAO,2MAAA,CAAA,sBAAmB;oBAC1B,QAAQ;gBACZ;gBACA,QAAQ;oBACJ,QAAQ;wBACJ,KAAK;wBACL,MAAM;wBACN,OAAO;wBACP,QAAQ;oBACZ;oBACA,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB,2MAAA,CAAA,QAAK;oBAC1B;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO,2MAAA,CAAA,QAAK;wBACZ,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;gBACA,aAAa,2MAAA,CAAA,aAAU;YAC3B;YACA,SAAS;gBACL,SAAS;gBACT,QAAQ;oBACJ,OAAO;oBACP,OAAO,2MAAA,CAAA,aAAU;oBACjB,WAAW,2MAAA,CAAA,QAAK;oBAChB,SAAS;gBACb;gBACA,MAAM;oBACF,OAAO,2MAAA,CAAA,sBAAmB;gBAC9B;gBACA,OAAO,2MAAA,CAAA,QAAK;gBACZ,aAAa;gBACb,kBAAkB;gBAClB,kBAAkB;gBAClB,eAAe;gBACf,QAAQ;gBACR,UAAU,2MAAA,CAAA,SAAM;gBAChB,QAAQ;oBACJ,SAAS;oBACT,SAAS;oBACT,SAAS;oBACT,MAAM;oBACN,OAAO,2MAAA,CAAA,QAAK;gBAChB;gBACA,aAAa;YACjB;YACA,QAAQ;gBACJ,WAAW;gBACX,mBAAmB,2MAAA,CAAA,MAAG;gBACtB,qBAAqB,2MAAA,CAAA,QAAK;gBAC1B,UAAU,2MAAA,CAAA,UAAO;gBACjB,SAAS;gBACT,QAAQ;gBACR,YAAY;gBACZ,QAAQ;oBACJ,SAAS;oBACT,OAAO;oBACP,cAAc;oBACd,WAAW,2MAAA,CAAA,QAAK;gBACpB;gBACA,kBAAkB;gBAClB,kBAAkB;gBAClB,aAAa;gBACb,UAAU;gBACV,mBAAmB;gBACnB,gBAAgB;gBAChB,OAAO;oBACH,iBAAiB,2MAAA,CAAA,QAAK;oBACtB,QAAQ;wBACJ,MAAM;wBACN,QAAQ;wBACR,OAAO;wBACP,KAAK;oBACT;oBACA,MAAM;wBACF,MAAM;wBACN,QAAQ;oBACZ;oBACA,UAAU;wBACN,QAAQ;wBACR,MAAM;4BACF,MAAM;wBACV;wBACA,UAAU;wBACV,cAAc;oBAClB;oBACA,UAAU;oBACV,cAAc;gBAClB;YACJ;QACJ,GAAG,wMAAA,CAAA,UAAK,EAAE,yMAAA,CAAA,UAAM,EAAE,wMAAA,CAAA,UAAK,EAAE,4MAAA,CAAA,UAAS,EAAE,6MAAA,CAAA,UAAS,EAAE,4MAAA,CAAA,UAAQ,EAAE,8MAAA,CAAA,UAAU,EAAE,iNAAA,CAAA,UAAa,EAAE,yMAAA,CAAA,UAAM,EAAE,4MAAA,CAAA,UAAS,EAAE,yMAAA,CAAA,UAAM,EAAE,2MAAA,CAAA,UAAO;IAC1H;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2059, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/carmine.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/carmine.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst ACCENT_COLOR = \"#f05b41\";\r\nconst BACKGROUND_COLOR = \"#fff\";\r\nconst TITLE_COLOR = \"#333\";\r\nconst SUBTITLE_COLOR = \"#8899a8\";\r\nconst TEXT_COLOR = \"#707070\";\r\nconst BORDER_COLOR = \"#dee1e3\";\r\nexport default [{\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"generic.carmine\",\r\n        defaultPalette: \"Carmine\",\r\n        backgroundColor: \"#fff\",\r\n        primaryTitleColor: \"#333\",\r\n        secondaryTitleColor: \"#8899a8\",\r\n        gridColor: \"#dee1e3\",\r\n        axisColor: \"#707070\",\r\n        export: {\r\n            backgroundColor: \"#fff\",\r\n            font: {\r\n                color: \"#333\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#b1b7bd\",\r\n                    backgroundColor: \"#fff\"\r\n                },\r\n                hover: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#b1b7bd\",\r\n                    backgroundColor: \"#faf2f0\"\r\n                },\r\n                focus: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#6d7781\",\r\n                    backgroundColor: \"#faf2f0\"\r\n                },\r\n                active: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#6d7781\",\r\n                    backgroundColor: \"#f5e7e4\"\r\n                }\r\n            }\r\n        },\r\n        legend: {\r\n            font: {\r\n                color: \"#707070\"\r\n            }\r\n        },\r\n        tooltip: {\r\n            color: \"#fff\",\r\n            border: {\r\n                color: \"#dee1e3\"\r\n            },\r\n            font: {\r\n                color: \"#333\"\r\n            }\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#dee1e3\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            font: {\r\n                color: \"#333\"\r\n            },\r\n            border: {\r\n                color: \"#dee1e3\"\r\n            },\r\n            color: \"#fff\"\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#dee1e3\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#c1c5c7\"\r\n                }\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            scale: {\r\n                breakStyle: {\r\n                    color: \"#c1c5c7\"\r\n                },\r\n                tick: {\r\n                    opacity: .12\r\n                }\r\n            },\r\n            selectedRangeColor: \"#f05b41\",\r\n            sliderMarker: {\r\n                color: \"#f05b41\"\r\n            },\r\n            sliderHandle: {\r\n                color: \"#f05b41\",\r\n                opacity: .5\r\n            }\r\n        },\r\n        sparkline: {\r\n            pointColor: \"#fff\",\r\n            minColor: \"#f0ad4e\",\r\n            maxColor: \"#f74d61\"\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#dee1e3\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#8899a8\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        bullet: {\r\n            color: \"#f05b41\"\r\n        },\r\n        gauge: {\r\n            valueIndicators: {\r\n                rangebar: {\r\n                    color: \"#f05b41\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#f05b41\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.carmine\",\r\n    theme: {\r\n        name: \"generic.carmine.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,eAAe;uCACN;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,2BAA2B;gBACvB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,OAAO;gBACX;gBACA,OAAO;YACX;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;YACJ;YACA,eAAe;gBACX,OAAO;oBACH,YAAY;wBACR,OAAO;oBACX;oBACA,MAAM;wBACF,SAAS;oBACb;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;gBACX;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,WAAW;gBACP,YAAY;gBACZ,UAAU;gBACV,UAAU;YACd;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,QAAQ;gBACJ,OAAO;YACX;YACA,OAAO;gBACH,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2217, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/dark.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/dark.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst WHITE = \"#ffffff\";\r\nconst BLACK = \"#000000\";\r\nconst SOME_GREY = \"#2b2b2b\";\r\nconst RANGE_COLOR = \"#b5b5b5\";\r\nconst GREY_GREEN = \"#303030\";\r\nconst AREA_LAYER_COLOR = \"#686868\";\r\nconst LINE_COLOR = \"#c7c7c7\";\r\nconst TARGET_COLOR = \"#8e8e8e\";\r\nconst POSITIVE_COLOR = \"#b8b8b8\";\r\nconst BORDER_COLOR = \"#494949\";\r\nexport default [{\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"generic.dark\",\r\n        font: {\r\n            color: \"#808080\"\r\n        },\r\n        backgroundColor: \"#2a2a2a\",\r\n        primaryTitleColor: \"#dedede\",\r\n        secondaryTitleColor: \"#a3a3a3\",\r\n        gridColor: \"#555555\",\r\n        axisColor: \"#a3a3a3\",\r\n        export: {\r\n            backgroundColor: \"#2a2a2a\",\r\n            font: {\r\n                color: \"#dbdbdb\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#4d4d4d\",\r\n                    backgroundColor: \"#2e2e2e\"\r\n                },\r\n                hover: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#6c6c6c\",\r\n                    backgroundColor: \"#444\"\r\n                },\r\n                focus: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#8d8d8d\",\r\n                    backgroundColor: \"#444444\"\r\n                },\r\n                active: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#8d8d8d\",\r\n                    backgroundColor: \"#555555\"\r\n                }\r\n            },\r\n            shadowColor: \"#292929\"\r\n        },\r\n        tooltip: {\r\n            color: \"#2b2b2b\",\r\n            border: {\r\n                color: \"#494949\"\r\n            },\r\n            font: {\r\n                color: \"#929292\"\r\n            }\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#494949\"\r\n                    }\r\n                },\r\n                valueErrorBar: {\r\n                    color: WHITE\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:axis\": {\r\n            constantLineStyle: {\r\n                color: WHITE\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            font: {\r\n                color: \"#929292\"\r\n            },\r\n            border: {\r\n                color: \"#494949\"\r\n            },\r\n            color: \"#2b2b2b\",\r\n            shadow: {\r\n                opacity: .008,\r\n                offsetY: 4,\r\n                blur: 8\r\n            }\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#494949\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#818181\"\r\n                }\r\n            },\r\n            zoomAndPan: {\r\n                dragBoxStyle: {\r\n                    color: WHITE\r\n                }\r\n            }\r\n        },\r\n        gauge: {\r\n            rangeContainer: {\r\n                backgroundColor: \"#b5b5b5\"\r\n            },\r\n            valueIndicators: {\r\n                _default: {\r\n                    color: \"#b5b5b5\"\r\n                },\r\n                rangebar: {\r\n                    color: \"#84788b\"\r\n                },\r\n                twocolorneedle: {\r\n                    secondColor: \"#ba544d\"\r\n                },\r\n                trianglemarker: {\r\n                    color: \"#b7918f\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#ba544d\"\r\n                }\r\n            }\r\n        },\r\n        barGauge: {\r\n            backgroundColor: \"#3c3c3c\"\r\n        },\r\n        rangeSelector: {\r\n            scale: {\r\n                tick: {\r\n                    color: WHITE,\r\n                    opacity: .32\r\n                },\r\n                minorTick: {\r\n                    color: WHITE,\r\n                    opacity: .1\r\n                },\r\n                breakStyle: {\r\n                    color: \"#818181\"\r\n                }\r\n            },\r\n            selectedRangeColor: \"#b5b5b5\",\r\n            sliderMarker: {\r\n                color: \"#b5b5b5\",\r\n                font: {\r\n                    color: \"#303030\"\r\n                }\r\n            },\r\n            sliderHandle: {\r\n                color: WHITE,\r\n                opacity: .2\r\n            },\r\n            shutter: {\r\n                color: \"#2b2b2b\",\r\n                opacity: .9\r\n            }\r\n        },\r\n        map: {\r\n            background: {\r\n                borderColor: \"#3f3f3f\"\r\n            },\r\n            layer: {\r\n                label: {\r\n                    stroke: BLACK,\r\n                    font: {\r\n                        color: WHITE\r\n                    }\r\n                }\r\n            },\r\n            \"layer:area\": {\r\n                borderColor: \"#303030\",\r\n                color: \"#686868\",\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            \"layer:line\": {\r\n                color: \"#c77244\",\r\n                hoveredColor: \"#ff5d04\",\r\n                selectedColor: \"#ff784f\"\r\n            },\r\n            \"layer:marker:bubble\": {\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            \"layer:marker:pie\": {\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            legend: {\r\n                border: {\r\n                    color: \"#3f3f3f\"\r\n                },\r\n                font: {\r\n                    color: WHITE\r\n                }\r\n            },\r\n            controlBar: {\r\n                borderColor: \"#c7c7c7\",\r\n                color: \"#303030\"\r\n            }\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#4c4c4c\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#a3a3a3\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        sparkline: {\r\n            lineColor: \"#c7c7c7\",\r\n            firstLastColor: \"#c7c7c7\",\r\n            barPositiveColor: \"#b8b8b8\",\r\n            barNegativeColor: \"#8e8e8e\",\r\n            winColor: \"#b8b8b8\",\r\n            lossColor: \"#8e8e8e\",\r\n            pointColor: \"#303030\"\r\n        },\r\n        bullet: {\r\n            targetColor: \"#8e8e8e\"\r\n        },\r\n        funnel: {\r\n            item: {\r\n                border: {\r\n                    color: \"#2a2a2a\"\r\n                }\r\n            }\r\n        },\r\n        sankey: {\r\n            label: {\r\n                font: {\r\n                    color: WHITE\r\n                },\r\n                shadow: {\r\n                    opacity: 0\r\n                }\r\n            },\r\n            node: {\r\n                border: {\r\n                    color: \"#2a2a2a\"\r\n                }\r\n            },\r\n            link: {\r\n                color: \"#888888\",\r\n                border: {\r\n                    color: \"#2a2a2a\"\r\n                },\r\n                hoverStyle: {\r\n                    color: \"#bbbbbb\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.dark\",\r\n    theme: {\r\n        name: \"generic.dark.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,YAAY;AAClB,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,eAAe;uCACN;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,MAAM;gBACF,OAAO;YACX;YACA,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;gBACA,aAAa;YACjB;YACA,SAAS;gBACL,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;oBACA,eAAe;wBACX,OAAO;oBACX;gBACJ;YACJ;YACA,qBAAqB;gBACjB,mBAAmB;oBACf,OAAO;gBACX;YACJ;YACA,2BAA2B;gBACvB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,OAAO;gBACX;gBACA,OAAO;gBACP,QAAQ;oBACJ,SAAS;oBACT,SAAS;oBACT,MAAM;gBACV;YACJ;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;gBACA,YAAY;oBACR,cAAc;wBACV,OAAO;oBACX;gBACJ;YACJ;YACA,OAAO;gBACH,gBAAgB;oBACZ,iBAAiB;gBACrB;gBACA,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,UAAU;wBACN,OAAO;oBACX;oBACA,gBAAgB;wBACZ,aAAa;oBACjB;oBACA,gBAAgB;wBACZ,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;YACA,UAAU;gBACN,iBAAiB;YACrB;YACA,eAAe;gBACX,OAAO;oBACH,MAAM;wBACF,OAAO;wBACP,SAAS;oBACb;oBACA,WAAW;wBACP,OAAO;wBACP,SAAS;oBACb;oBACA,YAAY;wBACR,OAAO;oBACX;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;oBACP,MAAM;wBACF,OAAO;oBACX;gBACJ;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;gBACA,SAAS;oBACL,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,KAAK;gBACD,YAAY;oBACR,aAAa;gBACjB;gBACA,OAAO;oBACH,OAAO;wBACH,QAAQ;wBACR,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;gBACA,cAAc;oBACV,aAAa;oBACb,OAAO;oBACP,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,cAAc;oBACV,OAAO;oBACP,cAAc;oBACd,eAAe;gBACnB;gBACA,uBAAuB;oBACnB,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,oBAAoB;oBAChB,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,QAAQ;oBACJ,QAAQ;wBACJ,OAAO;oBACX;oBACA,MAAM;wBACF,OAAO;oBACX;gBACJ;gBACA,YAAY;oBACR,aAAa;oBACb,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,WAAW;gBACP,WAAW;gBACX,gBAAgB;gBAChB,kBAAkB;gBAClB,kBAAkB;gBAClB,UAAU;gBACV,WAAW;gBACX,YAAY;YAChB;YACA,QAAQ;gBACJ,aAAa;YACjB;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;YACA,QAAQ;gBACJ,OAAO;oBACH,MAAM;wBACF,OAAO;oBACX;oBACA,QAAQ;wBACJ,SAAS;oBACb;gBACJ;gBACA,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,MAAM;oBACF,OAAO;oBACP,QAAQ;wBACJ,OAAO;oBACX;oBACA,YAAY;wBACR,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2501, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/contrast.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/contrast.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst WHITE = \"#ffffff\";\r\nconst BLACK = \"#000000\";\r\nconst CONTRAST_ACTIVE = \"#cf00da\";\r\nconst MARKER_COLOR = \"#f8ca00\";\r\nconst AREA_LAYER_COLOR = \"#686868\";\r\nexport default [{\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"generic.contrast\",\r\n        defaultPalette: \"Bright\",\r\n        font: {\r\n            color: WHITE\r\n        },\r\n        backgroundColor: BLACK,\r\n        primaryTitleColor: WHITE,\r\n        secondaryTitleColor: WHITE,\r\n        gridColor: WHITE,\r\n        axisColor: WHITE,\r\n        export: {\r\n            backgroundColor: BLACK,\r\n            font: {\r\n                color: WHITE\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: WHITE,\r\n                    borderColor: WHITE,\r\n                    backgroundColor: BLACK\r\n                },\r\n                hover: {\r\n                    color: WHITE,\r\n                    borderColor: WHITE,\r\n                    backgroundColor: \"#cf00d7\"\r\n                },\r\n                focus: {\r\n                    color: WHITE,\r\n                    borderColor: \"#cf00d7\",\r\n                    backgroundColor: BLACK\r\n                },\r\n                active: {\r\n                    color: BLACK,\r\n                    borderColor: WHITE,\r\n                    backgroundColor: WHITE\r\n                }\r\n            },\r\n            borderColor: WHITE,\r\n            menuButtonColor: BLACK,\r\n            activeBackgroundColor: WHITE,\r\n            activeColor: BLACK,\r\n            selectedBorderColor: \"#cf00da\",\r\n            selectedColor: \"#cf00da\",\r\n            shadowColor: \"none\"\r\n        },\r\n        tooltip: {\r\n            border: {\r\n                color: WHITE\r\n            },\r\n            font: {\r\n                color: WHITE\r\n            },\r\n            color: BLACK\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                valueErrorBar: {\r\n                    color: WHITE\r\n                },\r\n                hoverStyle: {\r\n                    hatching: {\r\n                        opacity: .5\r\n                    }\r\n                },\r\n                selectionStyle: {\r\n                    hatching: {\r\n                        opacity: .35\r\n                    }\r\n                },\r\n                label: {\r\n                    font: {\r\n                        color: WHITE\r\n                    },\r\n                    border: {\r\n                        color: WHITE\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:axis\": {\r\n            constantLineStyle: {\r\n                color: WHITE\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            font: {\r\n                color: WHITE\r\n            },\r\n            border: {\r\n                color: WHITE\r\n            },\r\n            color: BLACK\r\n        },\r\n        chart: {\r\n            commonSeriesSettings: {},\r\n            crosshair: {\r\n                color: \"#cf00d7\"\r\n            },\r\n            commonPaneSettings: {\r\n                backgroundColor: BLACK,\r\n                border: {\r\n                    color: WHITE\r\n                }\r\n            },\r\n            scrollBar: {\r\n                color: WHITE\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#cf00d7\"\r\n                }\r\n            },\r\n            zoomAndPan: {\r\n                dragBoxStyle: {\r\n                    color: WHITE,\r\n                    opacity: .7\r\n                }\r\n            }\r\n        },\r\n        pie: {\r\n            commonSeriesSettings: {\r\n                pie: {\r\n                    hoverStyle: {\r\n                        hatching: {\r\n                            opacity: .5\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        hatching: {\r\n                            opacity: .35\r\n                        }\r\n                    }\r\n                },\r\n                doughnut: {\r\n                    hoverStyle: {\r\n                        hatching: {\r\n                            opacity: .5\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        hatching: {\r\n                            opacity: .35\r\n                        }\r\n                    }\r\n                },\r\n                donut: {\r\n                    hoverStyle: {\r\n                        hatching: {\r\n                            opacity: .5\r\n                        }\r\n                    },\r\n                    selectionStyle: {\r\n                        hatching: {\r\n                            opacity: .35\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        gauge: {\r\n            rangeContainer: {\r\n                backgroundColor: WHITE\r\n            },\r\n            valueIndicators: {\r\n                _default: {\r\n                    color: WHITE\r\n                },\r\n                rangebar: {\r\n                    color: WHITE,\r\n                    backgroundColor: BLACK\r\n                },\r\n                twocolorneedle: {\r\n                    secondColor: WHITE\r\n                },\r\n                trianglemarker: {\r\n                    color: WHITE\r\n                },\r\n                textcloud: {\r\n                    color: WHITE,\r\n                    text: {\r\n                        font: {\r\n                            color: BLACK\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        barGauge: {\r\n            backgroundColor: \"#3c3c3c\"\r\n        },\r\n        rangeSelector: {\r\n            scale: {\r\n                tick: {\r\n                    color: WHITE,\r\n                    opacity: .4\r\n                },\r\n                minorTick: {\r\n                    color: WHITE,\r\n                    opacity: .12\r\n                },\r\n                breakStyle: {\r\n                    color: \"#cf00d7\"\r\n                }\r\n            },\r\n            selectedRangeColor: \"#cf00da\",\r\n            sliderMarker: {\r\n                color: \"#cf00da\"\r\n            },\r\n            sliderHandle: {\r\n                color: \"#cf00da\",\r\n                opacity: 1\r\n            },\r\n            shutter: {\r\n                opacity: .75\r\n            },\r\n            background: {\r\n                color: BLACK\r\n            }\r\n        },\r\n        map: {\r\n            background: {\r\n                borderColor: WHITE\r\n            },\r\n            layer: {\r\n                label: {\r\n                    stroke: BLACK,\r\n                    font: {\r\n                        color: WHITE\r\n                    }\r\n                }\r\n            },\r\n            \"layer:area\": {\r\n                borderColor: BLACK,\r\n                color: \"#686868\",\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE,\r\n                label: {\r\n                    font: {\r\n                        opacity: 1\r\n                    }\r\n                }\r\n            },\r\n            \"layer:line\": {\r\n                color: \"#267cff\",\r\n                hoveredColor: \"#f613ff\",\r\n                selectedColor: WHITE\r\n            },\r\n            \"layer:marker:dot\": {\r\n                borderColor: BLACK,\r\n                color: \"#f8ca00\",\r\n                backColor: BLACK,\r\n                backOpacity: .32\r\n            },\r\n            \"layer:marker:bubble\": {\r\n                color: \"#f8ca00\",\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            \"layer:marker:pie\": {\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            controlBar: {\r\n                borderColor: WHITE,\r\n                color: BLACK,\r\n                opacity: .3\r\n            }\r\n        },\r\n        treeMap: {\r\n            tile: {\r\n                color: \"#70c92f\"\r\n            },\r\n            group: {\r\n                color: \"#797979\",\r\n                label: {\r\n                    font: {\r\n                        color: WHITE\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        sparkline: {\r\n            pointColor: BLACK\r\n        },\r\n        bullet: {},\r\n        polar: {\r\n            commonSeriesSettings: {}\r\n        },\r\n        funnel: {\r\n            label: {\r\n                connector: {\r\n                    opacity: 1\r\n                }\r\n            }\r\n        },\r\n        sankey: {\r\n            label: {\r\n                font: {\r\n                    color: WHITE\r\n                },\r\n                shadow: {\r\n                    opacity: 0\r\n                }\r\n            },\r\n            node: {\r\n                border: {\r\n                    visible: true,\r\n                    width: 1,\r\n                    color: WHITE\r\n                }\r\n            },\r\n            link: {\r\n                opacity: .5,\r\n                border: {\r\n                    visible: true,\r\n                    width: 1,\r\n                    color: WHITE\r\n                },\r\n                hoverStyle: {\r\n                    opacity: .9\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.contrast\",\r\n    theme: {\r\n        name: \"generic.contrast.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,kBAAkB;AACxB,MAAM,eAAe;AACrB,MAAM,mBAAmB;uCACV;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,MAAM;gBACF,OAAO;YACX;YACA,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;gBACA,aAAa;gBACb,iBAAiB;gBACjB,uBAAuB;gBACvB,aAAa;gBACb,qBAAqB;gBACrB,eAAe;gBACf,aAAa;YACjB;YACA,SAAS;gBACL,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;gBACA,OAAO;YACX;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,eAAe;wBACX,OAAO;oBACX;oBACA,YAAY;wBACR,UAAU;4BACN,SAAS;wBACb;oBACJ;oBACA,gBAAgB;wBACZ,UAAU;4BACN,SAAS;wBACb;oBACJ;oBACA,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;wBACA,QAAQ;4BACJ,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,qBAAqB;gBACjB,mBAAmB;oBACf,OAAO;gBACX;YACJ;YACA,2BAA2B;gBACvB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,OAAO;gBACX;gBACA,OAAO;YACX;YACA,OAAO;gBACH,sBAAsB,CAAC;gBACvB,WAAW;oBACP,OAAO;gBACX;gBACA,oBAAoB;oBAChB,iBAAiB;oBACjB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,WAAW;oBACP,OAAO;gBACX;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;gBACA,YAAY;oBACR,cAAc;wBACV,OAAO;wBACP,SAAS;oBACb;gBACJ;YACJ;YACA,KAAK;gBACD,sBAAsB;oBAClB,KAAK;wBACD,YAAY;4BACR,UAAU;gCACN,SAAS;4BACb;wBACJ;wBACA,gBAAgB;4BACZ,UAAU;gCACN,SAAS;4BACb;wBACJ;oBACJ;oBACA,UAAU;wBACN,YAAY;4BACR,UAAU;gCACN,SAAS;4BACb;wBACJ;wBACA,gBAAgB;4BACZ,UAAU;gCACN,SAAS;4BACb;wBACJ;oBACJ;oBACA,OAAO;wBACH,YAAY;4BACR,UAAU;gCACN,SAAS;4BACb;wBACJ;wBACA,gBAAgB;4BACZ,UAAU;gCACN,SAAS;4BACb;wBACJ;oBACJ;gBACJ;YACJ;YACA,OAAO;gBACH,gBAAgB;oBACZ,iBAAiB;gBACrB;gBACA,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,UAAU;wBACN,OAAO;wBACP,iBAAiB;oBACrB;oBACA,gBAAgB;wBACZ,aAAa;oBACjB;oBACA,gBAAgB;wBACZ,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;wBACP,MAAM;4BACF,MAAM;gCACF,OAAO;4BACX;wBACJ;oBACJ;gBACJ;YACJ;YACA,UAAU;gBACN,iBAAiB;YACrB;YACA,eAAe;gBACX,OAAO;oBACH,MAAM;wBACF,OAAO;wBACP,SAAS;oBACb;oBACA,WAAW;wBACP,OAAO;wBACP,SAAS;oBACb;oBACA,YAAY;wBACR,OAAO;oBACX;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;gBACX;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;gBACA,SAAS;oBACL,SAAS;gBACb;gBACA,YAAY;oBACR,OAAO;gBACX;YACJ;YACA,KAAK;gBACD,YAAY;oBACR,aAAa;gBACjB;gBACA,OAAO;oBACH,OAAO;wBACH,QAAQ;wBACR,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;gBACA,cAAc;oBACV,aAAa;oBACb,OAAO;oBACP,oBAAoB;oBACpB,qBAAqB;oBACrB,OAAO;wBACH,MAAM;4BACF,SAAS;wBACb;oBACJ;gBACJ;gBACA,cAAc;oBACV,OAAO;oBACP,cAAc;oBACd,eAAe;gBACnB;gBACA,oBAAoB;oBAChB,aAAa;oBACb,OAAO;oBACP,WAAW;oBACX,aAAa;gBACjB;gBACA,uBAAuB;oBACnB,OAAO;oBACP,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,oBAAoB;oBAChB,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,YAAY;oBACR,aAAa;oBACb,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,SAAS;gBACL,MAAM;oBACF,OAAO;gBACX;gBACA,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,WAAW;gBACP,YAAY;YAChB;YACA,QAAQ,CAAC;YACT,OAAO;gBACH,sBAAsB,CAAC;YAC3B;YACA,QAAQ;gBACJ,OAAO;oBACH,WAAW;wBACP,SAAS;oBACb;gBACJ;YACJ;YACA,QAAQ;gBACJ,OAAO;oBACH,MAAM;wBACF,OAAO;oBACX;oBACA,QAAQ;wBACJ,SAAS;oBACb;gBACJ;gBACA,MAAM;oBACF,QAAQ;wBACJ,SAAS;wBACT,OAAO;wBACP,OAAO;oBACX;gBACJ;gBACA,MAAM;oBACF,SAAS;oBACT,QAAQ;wBACJ,SAAS;wBACT,OAAO;wBACP,OAAO;oBACX;oBACA,YAAY;wBACR,SAAS;oBACb;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2856, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/darkmoon.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/darkmoon.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst ACCENT_COLOR = \"#3debd3\";\r\nconst BACKGROUND_COLOR = \"#465672\";\r\nconst TITLE_COLOR = \"#fff\";\r\nconst SUBTITLE_COLOR = \"#919bac\";\r\nconst TEXT_COLOR = \"#c7ccd4\";\r\nconst BORDER_COLOR = \"#596980\";\r\nexport default [{\r\n    baseThemeName: \"generic.dark\",\r\n    theme: {\r\n        name: \"generic.darkmoon\",\r\n        defaultPalette: \"Dark Moon\",\r\n        backgroundColor: \"#465672\",\r\n        primaryTitleColor: \"#fff\",\r\n        secondaryTitleColor: \"#919bac\",\r\n        gridColor: \"#596980\",\r\n        axisColor: \"#c7ccd4\",\r\n        export: {\r\n            backgroundColor: \"#465672\",\r\n            font: {\r\n                color: \"#fff\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#fff\",\r\n                    borderColor: \"#7a889e\",\r\n                    backgroundColor: \"#465672\"\r\n                },\r\n                hover: {\r\n                    color: \"#fff\",\r\n                    borderColor: \"#9da8b8\",\r\n                    backgroundColor: \"#596e92\"\r\n                },\r\n                focus: {\r\n                    color: \"#fff\",\r\n                    borderColor: \"#c4cad4\",\r\n                    backgroundColor: \"#596e92\"\r\n                },\r\n                active: {\r\n                    color: \"#fff\",\r\n                    borderColor: \"#c4cad4\",\r\n                    backgroundColor: \"#6b80a4\"\r\n                }\r\n            }\r\n        },\r\n        legend: {\r\n            font: {\r\n                color: \"#c7ccd4\"\r\n            }\r\n        },\r\n        tooltip: {\r\n            color: \"#62789e\",\r\n            border: {\r\n                color: \"#596980\"\r\n            },\r\n            font: {\r\n                color: \"#fff\"\r\n            }\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#596980\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            font: {\r\n                color: \"#fff\"\r\n            },\r\n            border: {\r\n                color: \"#596980\"\r\n            },\r\n            color: \"#62789e\"\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#596980\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#73869e\"\r\n                }\r\n            }\r\n        },\r\n        gauge: {\r\n            valueIndicators: {\r\n                rangebar: {\r\n                    color: \"#3debd3\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#3debd3\",\r\n                    text: {\r\n                        font: {\r\n                            color: \"#465672\"\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        barGauge: {\r\n            backgroundColor: \"#526280\"\r\n        },\r\n        funnel: {\r\n            item: {\r\n                border: {\r\n                    color: \"#465672\"\r\n                }\r\n            }\r\n        },\r\n        sparkline: {\r\n            pointColor: \"#465672\",\r\n            minColor: \"#f0ad4e\",\r\n            maxColor: \"#f9517e\"\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#596980\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#fff\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        map: {\r\n            background: {\r\n                borderColor: \"#596980\"\r\n            },\r\n            \"layer:area\": {\r\n                color: \"#97a3b6\",\r\n                borderColor: \"#465672\"\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            shutter: {\r\n                color: \"#465672\"\r\n            },\r\n            scale: {\r\n                breakStyle: {\r\n                    color: \"#73869e\"\r\n                },\r\n                tick: {\r\n                    opacity: .2\r\n                }\r\n            },\r\n            selectedRangeColor: \"#3debd3\",\r\n            sliderMarker: {\r\n                color: \"#3debd3\",\r\n                font: {\r\n                    color: \"#000\"\r\n                }\r\n            },\r\n            sliderHandle: {\r\n                color: \"#3debd3\",\r\n                opacity: .5\r\n            }\r\n        },\r\n        bullet: {\r\n            color: \"#3debd3\"\r\n        },\r\n        sankey: {\r\n            link: {\r\n                border: {\r\n                    color: \"#465672\"\r\n                }\r\n            },\r\n            node: {\r\n                border: {\r\n                    color: \"#465672\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.darkmoon\",\r\n    theme: {\r\n        name: \"generic.darkmoon.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,eAAe;uCACN;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,2BAA2B;gBACvB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,OAAO;gBACX;gBACA,OAAO;YACX;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;YACJ;YACA,OAAO;gBACH,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;wBACP,MAAM;4BACF,MAAM;gCACF,OAAO;4BACX;wBACJ;oBACJ;gBACJ;YACJ;YACA,UAAU;gBACN,iBAAiB;YACrB;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;YACA,WAAW;gBACP,YAAY;gBACZ,UAAU;gBACV,UAAU;YACd;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,KAAK;gBACD,YAAY;oBACR,aAAa;gBACjB;gBACA,cAAc;oBACV,OAAO;oBACP,aAAa;gBACjB;YACJ;YACA,eAAe;gBACX,SAAS;oBACL,OAAO;gBACX;gBACA,OAAO;oBACH,YAAY;wBACR,OAAO;oBACX;oBACA,MAAM;wBACF,SAAS;oBACb;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;oBACP,MAAM;wBACF,OAAO;oBACX;gBACJ;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,QAAQ;gBACJ,OAAO;YACX;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3056, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/darkviolet.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/darkviolet.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst ACCENT_COLOR = \"#9c63ff\";\r\nconst BACKGROUND_COLOR = \"#17171f\";\r\nconst TITLE_COLOR = \"#f5f6f7\";\r\nconst SUBTITLE_COLOR = \"#fff\";\r\nconst TEXT_COLOR = \"#b2b2b6\";\r\nconst BORDER_COLOR = \"#343840\";\r\nexport default [{\r\n    baseThemeName: \"generic.dark\",\r\n    theme: {\r\n        name: \"generic.darkviolet\",\r\n        defaultPalette: \"Dark Violet\",\r\n        backgroundColor: \"#17171f\",\r\n        primaryTitleColor: \"#f5f6f7\",\r\n        secondaryTitleColor: \"#fff\",\r\n        gridColor: \"#343840\",\r\n        axisColor: \"#b2b2b6\",\r\n        export: {\r\n            backgroundColor: \"#17171f\",\r\n            font: {\r\n                color: \"#f5f6f7\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#f5f6f7\",\r\n                    borderColor: \"#414152\",\r\n                    backgroundColor: \"#17171f\"\r\n                },\r\n                hover: {\r\n                    color: \"#f5f6f7\",\r\n                    borderColor: \"#5c5c74\",\r\n                    backgroundColor: \"#2d2d3c\"\r\n                },\r\n                focus: {\r\n                    color: \"#f5f6f7\",\r\n                    borderColor: \"#7c7c97\",\r\n                    backgroundColor: \"#2d2d3c\"\r\n                },\r\n                active: {\r\n                    color: \"#f5f6f7\",\r\n                    borderColor: \"#7c7c97\",\r\n                    backgroundColor: \"#3c3c51\"\r\n                }\r\n            }\r\n        },\r\n        legend: {\r\n            font: {\r\n                color: \"#b2b2b6\"\r\n            }\r\n        },\r\n        tooltip: {\r\n            color: \"#17171f\",\r\n            border: {\r\n                color: \"#414152\"\r\n            },\r\n            font: {\r\n                color: \"#f5f6f7\"\r\n            }\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#343840\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            font: {\r\n                color: \"#f5f6f7\"\r\n            },\r\n            border: {\r\n                color: \"#414152\"\r\n            },\r\n            color: \"#17171f\"\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#343840\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#575e6b\"\r\n                }\r\n            }\r\n        },\r\n        funnel: {\r\n            item: {\r\n                border: {\r\n                    color: \"#17171f\"\r\n                }\r\n            }\r\n        },\r\n        sparkline: {\r\n            pointColor: \"#17171f\",\r\n            minColor: \"#f0ad4e\",\r\n            maxColor: \"#d9534f\"\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#343840\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#fff\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            shutter: {\r\n                color: \"#17171f\"\r\n            },\r\n            scale: {\r\n                breakStyle: {\r\n                    color: \"#575e6b\"\r\n                },\r\n                tick: {\r\n                    opacity: .2\r\n                }\r\n            },\r\n            selectedRangeColor: \"#9c63ff\",\r\n            sliderMarker: {\r\n                color: \"#9c63ff\",\r\n                font: {\r\n                    color: \"#fff\"\r\n                }\r\n            },\r\n            sliderHandle: {\r\n                color: \"#9c63ff\",\r\n                opacity: .5\r\n            }\r\n        },\r\n        bullet: {\r\n            color: \"#9c63ff\"\r\n        },\r\n        gauge: {\r\n            valueIndicators: {\r\n                rangebar: {\r\n                    color: \"#9c63ff\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#9c63ff\"\r\n                }\r\n            }\r\n        },\r\n        sankey: {\r\n            link: {\r\n                border: {\r\n                    color: \"#17171f\"\r\n                }\r\n            },\r\n            node: {\r\n                border: {\r\n                    color: \"#17171f\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.darkviolet\",\r\n    theme: {\r\n        name: \"generic.darkviolet.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,eAAe;uCACN;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,2BAA2B;gBACvB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,OAAO;gBACX;gBACA,OAAO;YACX;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;YACA,WAAW;gBACP,YAAY;gBACZ,UAAU;gBACV,UAAU;YACd;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,eAAe;gBACX,SAAS;oBACL,OAAO;gBACX;gBACA,OAAO;oBACH,YAAY;wBACR,OAAO;oBACX;oBACA,MAAM;wBACF,SAAS;oBACb;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;oBACP,MAAM;wBACF,OAAO;oBACX;gBACJ;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,QAAQ;gBACJ,OAAO;YACX;YACA,OAAO;gBACH,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3239, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/greenmist.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/greenmist.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst ACCENT_COLOR = \"#3cbab2\";\r\nconst BACKGROUND_COLOR = \"#f5f5f5\";\r\nconst TITLE_COLOR = \"#28484f\";\r\nconst SUBTITLE_COLOR = \"#7eb2be\";\r\nconst TEXT_COLOR = \"#657c80\";\r\nconst BORDER_COLOR = \"#dedede\";\r\nexport default [{\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"generic.greenmist\",\r\n        defaultPalette: \"Green Mist\",\r\n        backgroundColor: \"#f5f5f5\",\r\n        primaryTitleColor: \"#28484f\",\r\n        secondaryTitleColor: \"#7eb2be\",\r\n        gridColor: \"#dedede\",\r\n        axisColor: \"#657c80\",\r\n        export: {\r\n            backgroundColor: \"#f5f5f5\",\r\n            font: {\r\n                color: \"#28484f\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#28484f\",\r\n                    borderColor: \"#a2b4b8\",\r\n                    backgroundColor: \"#f5f5f5\"\r\n                },\r\n                hover: {\r\n                    color: \"#28484f\",\r\n                    borderColor: \"#7f989e\",\r\n                    backgroundColor: \"rgba(222, 222, 222, 0.4)\"\r\n                },\r\n                focus: {\r\n                    color: \"#28484f\",\r\n                    borderColor: \"#5f777c\",\r\n                    backgroundColor: \"rgba(222, 222, 222, 0.4)\"\r\n                },\r\n                active: {\r\n                    color: \"#28484f\",\r\n                    borderColor: \"#5f777c\",\r\n                    backgroundColor: \"rgba(222, 222, 222, 0.8)\"\r\n                }\r\n            }\r\n        },\r\n        legend: {\r\n            font: {\r\n                color: \"#657c80\"\r\n            }\r\n        },\r\n        tooltip: {\r\n            color: \"#fff\",\r\n            border: {\r\n                color: \"#dedede\"\r\n            },\r\n            font: {\r\n                color: \"#28484f\"\r\n            }\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#dedede\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            color: \"#fff\",\r\n            border: {\r\n                color: \"#dedede\"\r\n            },\r\n            font: {\r\n                color: \"#28484f\"\r\n            }\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#dedede\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#c1c1c1\"\r\n                }\r\n            }\r\n        },\r\n        funnel: {\r\n            item: {\r\n                border: {\r\n                    color: \"#f5f5f5\"\r\n                }\r\n            }\r\n        },\r\n        sparkline: {\r\n            pointColor: \"#f5f5f5\",\r\n            minColor: \"#ffc852\",\r\n            maxColor: \"#f74a5e\"\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#dedede\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#7eb2be\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            shutter: {\r\n                color: \"#f5f5f5\"\r\n            },\r\n            scale: {\r\n                breakStyle: {\r\n                    color: \"#c1c1c1\"\r\n                },\r\n                tick: {\r\n                    opacity: .12\r\n                }\r\n            },\r\n            selectedRangeColor: \"#3cbab2\",\r\n            sliderMarker: {\r\n                color: \"#3cbab2\"\r\n            },\r\n            sliderHandle: {\r\n                color: \"#3cbab2\",\r\n                opacity: .5\r\n            }\r\n        },\r\n        bullet: {\r\n            color: \"#3cbab2\"\r\n        },\r\n        gauge: {\r\n            valueIndicators: {\r\n                rangebar: {\r\n                    color: \"#3cbab2\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#3cbab2\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.greenmist\",\r\n    theme: {\r\n        name: \"generic.greenmist.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,eAAe;uCACN;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,2BAA2B;gBACvB,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;YACA,WAAW;gBACP,YAAY;gBACZ,UAAU;gBACV,UAAU;YACd;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,eAAe;gBACX,SAAS;oBACL,OAAO;gBACX;gBACA,OAAO;oBACH,YAAY;wBACR,OAAO;oBACX;oBACA,MAAM;wBACF,SAAS;oBACb;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;gBACX;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,QAAQ;gBACJ,OAAO;YACX;YACA,OAAO;gBACH,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3407, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/generic/softblue.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/generic/softblue.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst ACCENT_COLOR = \"#7ab8eb\";\r\nconst BACKGROUND_COLOR = \"#fff\";\r\nconst TITLE_COLOR = \"#333\";\r\nconst SUBTITLE_COLOR = \"#99a1a8\";\r\nconst TEXT_COLOR = \"#707070\";\r\nconst BORDER_COLOR = \"#e8eaeb\";\r\nexport default [{\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"generic.softblue\",\r\n        defaultPalette: \"Soft Blue\",\r\n        backgroundColor: \"#fff\",\r\n        primaryTitleColor: \"#333\",\r\n        secondaryTitleColor: \"#99a1a8\",\r\n        gridColor: \"#e8eaeb\",\r\n        axisColor: \"#707070\",\r\n        export: {\r\n            backgroundColor: \"#fff\",\r\n            font: {\r\n                color: \"#333\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#c9d0d4\",\r\n                    backgroundColor: \"#fff\"\r\n                },\r\n                hover: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#a7b2b9\",\r\n                    backgroundColor: \"#e6e6e6\"\r\n                },\r\n                focus: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#82929b\",\r\n                    backgroundColor: \"#e6e6e6\"\r\n                },\r\n                active: {\r\n                    color: \"#333\",\r\n                    borderColor: \"#82929b\",\r\n                    backgroundColor: \"#d4d4d4\"\r\n                }\r\n            }\r\n        },\r\n        legend: {\r\n            font: {\r\n                color: \"#707070\"\r\n            }\r\n        },\r\n        tooltip: {\r\n            color: \"#fff\",\r\n            border: {\r\n                color: \"#e8eaeb\"\r\n            },\r\n            font: {\r\n                color: \"#333\"\r\n            }\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#e8eaeb\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            color: \"#fff\",\r\n            border: {\r\n                color: \"#e8eaeb\"\r\n            },\r\n            font: {\r\n                color: \"#333\"\r\n            }\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#e8eaeb\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#cfd2d3\"\r\n                }\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            scale: {\r\n                breakStyle: {\r\n                    color: \"#cfd2d3\"\r\n                },\r\n                tick: {\r\n                    opacity: .12\r\n                }\r\n            },\r\n            selectedRangeColor: \"#7ab8eb\",\r\n            sliderMarker: {\r\n                color: \"#7ab8eb\"\r\n            },\r\n            sliderHandle: {\r\n                color: \"#7ab8eb\",\r\n                opacity: .5\r\n            }\r\n        },\r\n        sparkline: {\r\n            pointColor: \"#fff\",\r\n            minColor: \"#f0ad4e\",\r\n            maxColor: \"#d9534f\"\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#e8eaeb\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#99a1a8\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        bullet: {\r\n            color: \"#7ab8eb\"\r\n        },\r\n        gauge: {\r\n            valueIndicators: {\r\n                rangebar: {\r\n                    color: \"#7ab8eb\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#7ab8eb\"\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"generic.softblue\",\r\n    theme: {\r\n        name: \"generic.softblue.compact\"\r\n    }\r\n}];\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,eAAe;AACrB,MAAM,mBAAmB;AACzB,MAAM,cAAc;AACpB,MAAM,iBAAiB;AACvB,MAAM,aAAa;AACnB,MAAM,eAAe;uCACN;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,iBAAiB;YACjB,mBAAmB;YACnB,qBAAqB;YACrB,WAAW;YACX,WAAW;YACX,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;YACJ;YACA,QAAQ;gBACJ,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,2BAA2B;gBACvB,OAAO;gBACP,QAAQ;oBACJ,OAAO;gBACX;gBACA,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;YACJ;YACA,eAAe;gBACX,OAAO;oBACH,YAAY;wBACR,OAAO;oBACX;oBACA,MAAM;wBACF,SAAS;oBACb;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;gBACX;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,WAAW;gBACP,YAAY;gBACZ,UAAU;gBACV,UAAU;YACd;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,QAAQ;gBACJ,OAAO;YACX;YACA,OAAO;gBACH,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3565, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/material/index.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/material/index.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst FONT_FAMILY = \"'Roboto', 'RobotoFallback', 'Helvetica', 'Arial', sans-serif\";\r\nconst LIGHT_TITLE_COLOR = \"rgba(0,0,0,0.87)\";\r\nconst LIGHT_LABEL_COLOR = \"rgba(0,0,0,0.54)\";\r\nconst DARK_TITLE_COLOR = \"rgba(255,255,255,0.87)\";\r\nconst DARK_LABEL_COLOR = \"rgba(255,255,255,0.54)\";\r\nconst DARK_BACKGROUND_COLOR = \"#363640\";\r\nconst WHITE = \"#ffffff\";\r\nconst BLACK = \"#000000\";\r\nconst RANGE_COLOR = \"#b5b5b5\";\r\nconst AREA_LAYER_COLOR = \"#686868\";\r\nconst LINE_COLOR = \"#c7c7c7\";\r\nconst TARGET_COLOR = \"#8e8e8e\";\r\nconst POSITIVE_COLOR = \"#b8b8b8\";\r\nconst LABEL_BORDER_COLOR = \"#494949\";\r\nconst BREAK_STYLE_COLOR = \"#818181\";\r\nconst themes = [{\r\n    baseThemeName: \"generic.light\",\r\n    theme: {\r\n        name: \"material\",\r\n        defaultPalette: \"Material\",\r\n        font: {\r\n            family: FONT_FAMILY\r\n        },\r\n        title: {\r\n            margin: {\r\n                top: 20,\r\n                bottom: 20,\r\n                left: 0,\r\n                right: 0\r\n            },\r\n            font: {\r\n                size: 20,\r\n                family: FONT_FAMILY,\r\n                weight: 500\r\n            },\r\n            horizontalAlignment: \"left\",\r\n            subtitle: {\r\n                font: {\r\n                    size: 14\r\n                },\r\n                horizontalAlignment: \"left\"\r\n            }\r\n        },\r\n        tooltip: {\r\n            shadow: {\r\n                opacity: 0\r\n            },\r\n            border: {\r\n                visible: false\r\n            },\r\n            paddingLeftRight: 8,\r\n            paddingTopBottom: 6,\r\n            arrowLength: 0,\r\n            location: \"edge\",\r\n            color: \"#616161\",\r\n            font: {\r\n                color: WHITE\r\n            },\r\n            cornerRadius: 4\r\n        },\r\n        chart: {\r\n            commonAxisSettings: {\r\n                minorTick: {\r\n                    opacity: .5\r\n                },\r\n                label: {\r\n                    font: {\r\n                        size: 11\r\n                    }\r\n                }\r\n            },\r\n            commonAnnotationSettings: {\r\n                font: {\r\n                    color: WHITE\r\n                },\r\n                border: {\r\n                    color: \"#616161\"\r\n                },\r\n                color: \"#616161\",\r\n                arrowLength: 14,\r\n                arrowWidth: 0,\r\n                shadow: {\r\n                    opacity: .08,\r\n                    offsetY: 4,\r\n                    blur: 8\r\n                },\r\n                cornerRadius: 4\r\n            }\r\n        },\r\n        pie: {\r\n            title: {\r\n                horizontalAlignment: \"center\",\r\n                subtitle: {\r\n                    horizontalAlignment: \"center\"\r\n                }\r\n            }\r\n        },\r\n        polar: {\r\n            commonAxisSettings: {\r\n                minorTick: {\r\n                    opacity: .5\r\n                }\r\n            },\r\n            title: {\r\n                horizontalAlignment: \"center\",\r\n                subtitle: {\r\n                    horizontalAlignment: \"center\"\r\n                }\r\n            }\r\n        },\r\n        funnel: {\r\n            title: {\r\n                horizontalAlignment: \"center\",\r\n                subtitle: {\r\n                    horizontalAlignment: \"center\"\r\n                }\r\n            }\r\n        },\r\n        gauge: {\r\n            title: {\r\n                horizontalAlignment: \"center\",\r\n                subtitle: {\r\n                    horizontalAlignment: \"center\"\r\n                }\r\n            }\r\n        },\r\n        barGauge: {\r\n            title: {\r\n                horizontalAlignment: \"center\",\r\n                subtitle: {\r\n                    horizontalAlignment: \"center\"\r\n                }\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            sliderHandle: {\r\n                opacity: .5\r\n            }\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                label: {\r\n                    font: {\r\n                        weight: 500\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"material\",\r\n    theme: {\r\n        name: \"material.light\",\r\n        gridColor: \"#e0e0e0\",\r\n        axisColor: LIGHT_LABEL_COLOR,\r\n        primaryTitleColor: LIGHT_TITLE_COLOR,\r\n        legend: {\r\n            font: {\r\n                color: LIGHT_LABEL_COLOR\r\n            }\r\n        },\r\n        chart: {\r\n            scrollBar: {\r\n                color: \"#bfbfbf\",\r\n                opacity: .7\r\n            }\r\n        },\r\n        gauge: {\r\n            rangeContainer: {\r\n                backgroundColor: \"rgba(0,0,0,0.2)\"\r\n            }\r\n        },\r\n        barGauge: {\r\n            backgroundColor: \"#efefef\"\r\n        }\r\n    }\r\n}, {\r\n    baseThemeName: \"material\",\r\n    theme: {\r\n        name: \"material.dark\",\r\n        gridColor: \"#515159\",\r\n        backgroundColor: \"#363640\",\r\n        axisColor: DARK_LABEL_COLOR,\r\n        font: {\r\n            color: DARK_LABEL_COLOR\r\n        },\r\n        primaryTitleColor: DARK_TITLE_COLOR,\r\n        secondaryTitleColor: DARK_TITLE_COLOR,\r\n        tooltip: {\r\n            color: \"#000\"\r\n        },\r\n        export: {\r\n            backgroundColor: \"#363640\",\r\n            font: {\r\n                color: \"#dbdbdb\"\r\n            },\r\n            button: {\r\n                default: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#4d4d4d\",\r\n                    backgroundColor: \"#363640\"\r\n                },\r\n                hover: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#6c6c6c\",\r\n                    backgroundColor: \"#3f3f4b\"\r\n                },\r\n                focus: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#8d8d8d\",\r\n                    backgroundColor: \"#494956\"\r\n                },\r\n                active: {\r\n                    color: \"#dedede\",\r\n                    borderColor: \"#8d8d8d\",\r\n                    backgroundColor: \"#494956\"\r\n                }\r\n            },\r\n            shadowColor: \"#292929\"\r\n        },\r\n        \"chart:common\": {\r\n            commonSeriesSettings: {\r\n                label: {\r\n                    border: {\r\n                        color: \"#494949\"\r\n                    }\r\n                },\r\n                valueErrorBar: {\r\n                    color: WHITE\r\n                }\r\n            }\r\n        },\r\n        \"chart:common:axis\": {\r\n            constantLineStyle: {\r\n                color: WHITE\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            border: {\r\n                color: \"#000\"\r\n            },\r\n            color: \"#000\"\r\n        },\r\n        chart: {\r\n            commonPaneSettings: {\r\n                border: {\r\n                    color: \"#494949\"\r\n                }\r\n            },\r\n            commonAxisSettings: {\r\n                breakStyle: {\r\n                    color: \"#818181\"\r\n                }\r\n            },\r\n            zoomAndPan: {\r\n                dragBoxStyle: {\r\n                    color: WHITE\r\n                }\r\n            }\r\n        },\r\n        gauge: {\r\n            rangeContainer: {\r\n                backgroundColor: \"#b5b5b5\"\r\n            },\r\n            valueIndicators: {\r\n                _default: {\r\n                    color: \"#b5b5b5\"\r\n                },\r\n                rangebar: {\r\n                    color: \"#84788b\"\r\n                },\r\n                twocolorneedle: {\r\n                    secondColor: \"#ba544d\"\r\n                },\r\n                trianglemarker: {\r\n                    color: \"#b7918f\"\r\n                },\r\n                textcloud: {\r\n                    color: \"#ba544d\"\r\n                }\r\n            }\r\n        },\r\n        barGauge: {\r\n            backgroundColor: \"#3c3c3c\"\r\n        },\r\n        rangeSelector: {\r\n            scale: {\r\n                tick: {\r\n                    color: WHITE,\r\n                    opacity: .32\r\n                },\r\n                minorTick: {\r\n                    color: WHITE,\r\n                    opacity: .1\r\n                },\r\n                breakStyle: {\r\n                    color: \"#818181\"\r\n                }\r\n            },\r\n            selectedRangeColor: \"#b5b5b5\",\r\n            sliderMarker: {\r\n                color: \"#b5b5b5\",\r\n                font: {\r\n                    color: \"#363640\"\r\n                }\r\n            },\r\n            sliderHandle: {\r\n                color: WHITE,\r\n                opacity: .2\r\n            },\r\n            shutter: {\r\n                color: WHITE,\r\n                opacity: .1\r\n            }\r\n        },\r\n        map: {\r\n            background: {\r\n                borderColor: \"#3f3f3f\"\r\n            },\r\n            layer: {\r\n                label: {\r\n                    stroke: BLACK,\r\n                    font: {\r\n                        color: WHITE\r\n                    }\r\n                }\r\n            },\r\n            \"layer:area\": {\r\n                borderColor: \"#363640\",\r\n                color: \"#686868\",\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            \"layer:line\": {\r\n                color: \"#c77244\",\r\n                hoveredColor: \"#ff5d04\",\r\n                selectedColor: \"#ff784f\"\r\n            },\r\n            \"layer:marker:bubble\": {\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            \"layer:marker:pie\": {\r\n                hoveredBorderColor: WHITE,\r\n                selectedBorderColor: WHITE\r\n            },\r\n            legend: {\r\n                border: {\r\n                    color: \"#3f3f3f\"\r\n                },\r\n                font: {\r\n                    color: WHITE\r\n                }\r\n            },\r\n            controlBar: {\r\n                borderColor: \"#c7c7c7\",\r\n                color: \"#363640\"\r\n            }\r\n        },\r\n        treeMap: {\r\n            group: {\r\n                color: \"#4c4c4c\",\r\n                label: {\r\n                    font: {\r\n                        color: \"#a3a3a3\"\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        sparkline: {\r\n            lineColor: \"#c7c7c7\",\r\n            firstLastColor: \"#c7c7c7\",\r\n            barPositiveColor: \"#b8b8b8\",\r\n            barNegativeColor: \"#8e8e8e\",\r\n            winColor: \"#b8b8b8\",\r\n            lossColor: \"#8e8e8e\",\r\n            pointColor: \"#363640\"\r\n        },\r\n        bullet: {\r\n            targetColor: \"#8e8e8e\"\r\n        },\r\n        funnel: {\r\n            item: {\r\n                border: {\r\n                    color: \"#363640\"\r\n                }\r\n            }\r\n        },\r\n        sankey: {\r\n            label: {\r\n                font: {\r\n                    color: WHITE\r\n                }\r\n            }\r\n        }\r\n    }\r\n}];\r\n\r\nfunction getMaterialColorScheme(accentName, themeName, accentColor) {\r\n    return {\r\n        theme: {\r\n            name: `material.${accentName}.${themeName}`,\r\n            rangeSelector: {\r\n                selectedRangeColor: accentColor,\r\n                sliderMarker: {\r\n                    color: accentColor\r\n                },\r\n                sliderHandle: {\r\n                    color: accentColor\r\n                }\r\n            },\r\n            map: {\r\n                \"layer:marker:dot\": {\r\n                    color: accentColor\r\n                },\r\n                \"layer:marker:bubble\": {\r\n                    color: accentColor\r\n                },\r\n                legend: {\r\n                    markerColor: accentColor\r\n                }\r\n            },\r\n            bullet: {\r\n                color: accentColor\r\n            },\r\n            gauge: {\r\n                valueIndicators: {\r\n                    rangebar: {\r\n                        color: accentColor\r\n                    },\r\n                    textcloud: {\r\n                        color: accentColor\r\n                    }\r\n                }\r\n            }\r\n        },\r\n        baseThemeName: `material.${themeName}`\r\n    }\r\n}\r\nconst materialAccents = {\r\n    blue: \"#03a9f4\",\r\n    lime: \"#cddc39\",\r\n    orange: \"#ff5722\",\r\n    purple: \"#9c27b0\",\r\n    teal: \"#009688\"\r\n};\r\nObject.keys(materialAccents).forEach((accent => {\r\n    const color = materialAccents[accent];\r\n    themes.push(getMaterialColorScheme(accent, \"light\", color), getMaterialColorScheme(accent, \"dark\", color), {\r\n        theme: {\r\n            name: `material.${accent}.light.compact`\r\n        },\r\n        baseThemeName: `material.${accent}.light`\r\n    }, {\r\n        theme: {\r\n            name: `material.${accent}.dark.compact`\r\n        },\r\n        baseThemeName: `material.${accent}.dark`\r\n    })\r\n}));\r\nexport default themes;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,cAAc;AACpB,MAAM,oBAAoB;AAC1B,MAAM,oBAAoB;AAC1B,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AACzB,MAAM,wBAAwB;AAC9B,MAAM,QAAQ;AACd,MAAM,QAAQ;AACd,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,eAAe;AACrB,MAAM,iBAAiB;AACvB,MAAM,qBAAqB;AAC3B,MAAM,oBAAoB;AAC1B,MAAM,SAAS;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;YACN,gBAAgB;YAChB,MAAM;gBACF,QAAQ;YACZ;YACA,OAAO;gBACH,QAAQ;oBACJ,KAAK;oBACL,QAAQ;oBACR,MAAM;oBACN,OAAO;gBACX;gBACA,MAAM;oBACF,MAAM;oBACN,QAAQ;oBACR,QAAQ;gBACZ;gBACA,qBAAqB;gBACrB,UAAU;oBACN,MAAM;wBACF,MAAM;oBACV;oBACA,qBAAqB;gBACzB;YACJ;YACA,SAAS;gBACL,QAAQ;oBACJ,SAAS;gBACb;gBACA,QAAQ;oBACJ,SAAS;gBACb;gBACA,kBAAkB;gBAClB,kBAAkB;gBAClB,aAAa;gBACb,UAAU;gBACV,OAAO;gBACP,MAAM;oBACF,OAAO;gBACX;gBACA,cAAc;YAClB;YACA,OAAO;gBACH,oBAAoB;oBAChB,WAAW;wBACP,SAAS;oBACb;oBACA,OAAO;wBACH,MAAM;4BACF,MAAM;wBACV;oBACJ;gBACJ;gBACA,0BAA0B;oBACtB,MAAM;wBACF,OAAO;oBACX;oBACA,QAAQ;wBACJ,OAAO;oBACX;oBACA,OAAO;oBACP,aAAa;oBACb,YAAY;oBACZ,QAAQ;wBACJ,SAAS;wBACT,SAAS;wBACT,MAAM;oBACV;oBACA,cAAc;gBAClB;YACJ;YACA,KAAK;gBACD,OAAO;oBACH,qBAAqB;oBACrB,UAAU;wBACN,qBAAqB;oBACzB;gBACJ;YACJ;YACA,OAAO;gBACH,oBAAoB;oBAChB,WAAW;wBACP,SAAS;oBACb;gBACJ;gBACA,OAAO;oBACH,qBAAqB;oBACrB,UAAU;wBACN,qBAAqB;oBACzB;gBACJ;YACJ;YACA,QAAQ;gBACJ,OAAO;oBACH,qBAAqB;oBACrB,UAAU;wBACN,qBAAqB;oBACzB;gBACJ;YACJ;YACA,OAAO;gBACH,OAAO;oBACH,qBAAqB;oBACrB,UAAU;wBACN,qBAAqB;oBACzB;gBACJ;YACJ;YACA,UAAU;gBACN,OAAO;oBACH,qBAAqB;oBACrB,UAAU;wBACN,qBAAqB;oBACzB;gBACJ;YACJ;YACA,eAAe;gBACX,cAAc;oBACV,SAAS;gBACb;YACJ;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;wBACH,MAAM;4BACF,QAAQ;wBACZ;oBACJ;gBACJ;YACJ;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;YACN,WAAW;YACX,WAAW;YACX,mBAAmB;YACnB,QAAQ;gBACJ,MAAM;oBACF,OAAO;gBACX;YACJ;YACA,OAAO;gBACH,WAAW;oBACP,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,OAAO;gBACH,gBAAgB;oBACZ,iBAAiB;gBACrB;YACJ;YACA,UAAU;gBACN,iBAAiB;YACrB;QACJ;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;YACN,WAAW;YACX,iBAAiB;YACjB,WAAW;YACX,MAAM;gBACF,OAAO;YACX;YACA,mBAAmB;YACnB,qBAAqB;YACrB,SAAS;gBACL,OAAO;YACX;YACA,QAAQ;gBACJ,iBAAiB;gBACjB,MAAM;oBACF,OAAO;gBACX;gBACA,QAAQ;oBACJ,SAAS;wBACL,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,OAAO;wBACH,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;oBACA,QAAQ;wBACJ,OAAO;wBACP,aAAa;wBACb,iBAAiB;oBACrB;gBACJ;gBACA,aAAa;YACjB;YACA,gBAAgB;gBACZ,sBAAsB;oBAClB,OAAO;wBACH,QAAQ;4BACJ,OAAO;wBACX;oBACJ;oBACA,eAAe;wBACX,OAAO;oBACX;gBACJ;YACJ;YACA,qBAAqB;gBACjB,mBAAmB;oBACf,OAAO;gBACX;YACJ;YACA,2BAA2B;gBACvB,QAAQ;oBACJ,OAAO;gBACX;gBACA,OAAO;YACX;YACA,OAAO;gBACH,oBAAoB;oBAChB,QAAQ;wBACJ,OAAO;oBACX;gBACJ;gBACA,oBAAoB;oBAChB,YAAY;wBACR,OAAO;oBACX;gBACJ;gBACA,YAAY;oBACR,cAAc;wBACV,OAAO;oBACX;gBACJ;YACJ;YACA,OAAO;gBACH,gBAAgB;oBACZ,iBAAiB;gBACrB;gBACA,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,UAAU;wBACN,OAAO;oBACX;oBACA,gBAAgB;wBACZ,aAAa;oBACjB;oBACA,gBAAgB;wBACZ,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;YACA,UAAU;gBACN,iBAAiB;YACrB;YACA,eAAe;gBACX,OAAO;oBACH,MAAM;wBACF,OAAO;wBACP,SAAS;oBACb;oBACA,WAAW;wBACP,OAAO;wBACP,SAAS;oBACb;oBACA,YAAY;wBACR,OAAO;oBACX;gBACJ;gBACA,oBAAoB;gBACpB,cAAc;oBACV,OAAO;oBACP,MAAM;wBACF,OAAO;oBACX;gBACJ;gBACA,cAAc;oBACV,OAAO;oBACP,SAAS;gBACb;gBACA,SAAS;oBACL,OAAO;oBACP,SAAS;gBACb;YACJ;YACA,KAAK;gBACD,YAAY;oBACR,aAAa;gBACjB;gBACA,OAAO;oBACH,OAAO;wBACH,QAAQ;wBACR,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;gBACA,cAAc;oBACV,aAAa;oBACb,OAAO;oBACP,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,cAAc;oBACV,OAAO;oBACP,cAAc;oBACd,eAAe;gBACnB;gBACA,uBAAuB;oBACnB,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,oBAAoB;oBAChB,oBAAoB;oBACpB,qBAAqB;gBACzB;gBACA,QAAQ;oBACJ,QAAQ;wBACJ,OAAO;oBACX;oBACA,MAAM;wBACF,OAAO;oBACX;gBACJ;gBACA,YAAY;oBACR,aAAa;oBACb,OAAO;gBACX;YACJ;YACA,SAAS;gBACL,OAAO;oBACH,OAAO;oBACP,OAAO;wBACH,MAAM;4BACF,OAAO;wBACX;oBACJ;gBACJ;YACJ;YACA,WAAW;gBACP,WAAW;gBACX,gBAAgB;gBAChB,kBAAkB;gBAClB,kBAAkB;gBAClB,UAAU;gBACV,WAAW;gBACX,YAAY;YAChB;YACA,QAAQ;gBACJ,aAAa;YACjB;YACA,QAAQ;gBACJ,MAAM;oBACF,QAAQ;wBACJ,OAAO;oBACX;gBACJ;YACJ;YACA,QAAQ;gBACJ,OAAO;oBACH,MAAM;wBACF,OAAO;oBACX;gBACJ;YACJ;QACJ;IACJ;CAAE;AAEF,SAAS,uBAAuB,UAAU,EAAE,SAAS,EAAE,WAAW;IAC9D,OAAO;QACH,OAAO;YACH,MAAM,CAAC,SAAS,EAAE,WAAW,CAAC,EAAE,WAAW;YAC3C,eAAe;gBACX,oBAAoB;gBACpB,cAAc;oBACV,OAAO;gBACX;gBACA,cAAc;oBACV,OAAO;gBACX;YACJ;YACA,KAAK;gBACD,oBAAoB;oBAChB,OAAO;gBACX;gBACA,uBAAuB;oBACnB,OAAO;gBACX;gBACA,QAAQ;oBACJ,aAAa;gBACjB;YACJ;YACA,QAAQ;gBACJ,OAAO;YACX;YACA,OAAO;gBACH,iBAAiB;oBACb,UAAU;wBACN,OAAO;oBACX;oBACA,WAAW;wBACP,OAAO;oBACX;gBACJ;YACJ;QACJ;QACA,eAAe,CAAC,SAAS,EAAE,WAAW;IAC1C;AACJ;AACA,MAAM,kBAAkB;IACpB,MAAM;IACN,MAAM;IACN,QAAQ;IACR,QAAQ;IACR,MAAM;AACV;AACA,OAAO,IAAI,CAAC,iBAAiB,OAAO,CAAE,CAAA;IAClC,MAAM,QAAQ,eAAe,CAAC,OAAO;IACrC,OAAO,IAAI,CAAC,uBAAuB,QAAQ,SAAS,QAAQ,uBAAuB,QAAQ,QAAQ,QAAQ;QACvG,OAAO;YACH,MAAM,CAAC,SAAS,EAAE,OAAO,cAAc,CAAC;QAC5C;QACA,eAAe,CAAC,SAAS,EAAE,OAAO,MAAM,CAAC;IAC7C,GAAG;QACC,OAAO;YACH,MAAM,CAAC,SAAS,EAAE,OAAO,aAAa,CAAC;QAC3C;QACA,eAAe,CAAC,SAAS,EAAE,OAAO,KAAK,CAAC;IAC5C;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4043, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/themes/fluent/index.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/themes/fluent/index.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nconst themes = [{\r\n    baseThemeName: \"material.blue.light\",\r\n    theme: {\r\n        name: \"fluent.blue.light\"\r\n    }\r\n}, {\r\n    baseThemeName: \"fluent.blue.light\",\r\n    theme: {\r\n        name: \"fluent.blue.light.compact\"\r\n    }\r\n}, {\r\n    baseThemeName: \"fluent.blue.light\",\r\n    theme: {\r\n        name: \"fluent.saas.light\"\r\n    }\r\n}, {\r\n    baseThemeName: \"fluent.saas.light\",\r\n    theme: {\r\n        name: \"fluent.saas.light.compact\"\r\n    }\r\n}, {\r\n    baseThemeName: \"material.blue.dark\",\r\n    theme: {\r\n        name: \"fluent.blue.dark\"\r\n    }\r\n}, {\r\n    baseThemeName: \"fluent.blue.dark\",\r\n    theme: {\r\n        name: \"fluent.blue.dark.compact\"\r\n    }\r\n}, {\r\n    baseThemeName: \"fluent.blue.dark\",\r\n    theme: {\r\n        name: \"fluent.saas.dark\"\r\n    }\r\n}, {\r\n    baseThemeName: \"fluent.saas.dark\",\r\n    theme: {\r\n        name: \"fluent.saas.dark.compact\"\r\n    }\r\n}];\r\nexport default themes;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD,MAAM,SAAS;IAAC;QACZ,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;IAAG;QACC,eAAe;QACf,OAAO;YACH,MAAM;QACV;IACJ;CAAE;uCACa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4108, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/core/m_base_widget.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/core/m_base_widget.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport devices from \"../../../core/devices\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport DOMComponent from \"../../../core/dom_component\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    noop\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getHeight,\r\n    getWidth\r\n} from \"../../../core/utils/size\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isNumeric,\r\n    isObject as _isObject,\r\n    type\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    getWindow,\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport {\r\n    BaseThemeManager\r\n} from \"../../../viz/core/base_theme_manager\";\r\nimport {\r\n    createEventTrigger,\r\n    createIncidentOccurred,\r\n    createResizeHandler\r\n} from \"../../../viz/core/base_widget.utils\";\r\nimport warnings from \"../../../viz/core/errors_warnings\";\r\nimport {\r\n    changes,\r\n    replaceInherit\r\n} from \"../../../viz/core/helpers\";\r\nimport _Layout from \"../../../viz/core/layout\";\r\nimport {\r\n    Renderer\r\n} from \"../../../viz/core/renderers/renderer\";\r\nimport {\r\n    parseScalar as _parseScalar\r\n} from \"../../../viz/core/utils\";\r\nimport {\r\n    areCanvasesDifferent,\r\n    floorCanvasDimensions\r\n} from \"../../../viz/utils\";\r\nimport graphicObject from \"../../common/m_charts\";\r\nconst {\r\n    log: log\r\n} = warnings;\r\nconst OPTION_RTL_ENABLED = \"rtlEnabled\";\r\nconst SIZED_ELEMENT_CLASS = \"dx-sized-element\";\r\nconst baseOptionMethod = DOMComponent.prototype.option;\r\n\r\nfunction getTrue() {\r\n    return true\r\n}\r\n\r\nfunction getFalse() {\r\n    return false\r\n}\r\n\r\nfunction defaultOnIncidentOccurred(e) {\r\n    if (!e.component._eventsStrategy.hasEvent(\"incidentOccurred\")) {\r\n        log.apply(null, [e.target.id].concat(e.target.args || []))\r\n    }\r\n}\r\n\r\nfunction pickPositiveValue(values) {\r\n    return values.reduce(((result, value) => value > 0 && !result ? value : result), 0)\r\n}\r\nconst getEmptyComponent = function() {\r\n    const emptyComponentConfig = {\r\n        _initTemplates() {},\r\n        ctor(element, options) {\r\n            this.callBase(element, options);\r\n            const sizedElement = domAdapter.createElement(\"div\");\r\n            const width = options && isNumeric(options.width) ? `${options.width}px` : \"100%\";\r\n            const height = options && isNumeric(options.height) ? `${options.height}px` : `${this._getDefaultSize().height}px`;\r\n            domAdapter.setStyle(sizedElement, \"width\", width);\r\n            domAdapter.setStyle(sizedElement, \"height\", height);\r\n            domAdapter.setClass(sizedElement, \"dx-sized-element\", false);\r\n            domAdapter.insertElement(element, sizedElement)\r\n        }\r\n    };\r\n    const EmptyComponent = DOMComponent.inherit(emptyComponentConfig);\r\n    const originalInherit = EmptyComponent.inherit;\r\n    EmptyComponent.inherit = function(config) {\r\n        Object.keys(config).forEach((field => {\r\n            if (isFunction(config[field]) && \"_\" !== field.substr(0, 1) && \"option\" !== field || \"_dispose\" === field || \"_optionChanged\" === field) {\r\n                config[field] = noop\r\n            }\r\n        }));\r\n        return originalInherit.call(this, config)\r\n    };\r\n    return EmptyComponent\r\n};\r\n\r\nfunction callForEach(functions) {\r\n    functions.forEach((c => c()))\r\n}\r\nconst isServerSide = !hasWindow();\r\n\r\nfunction sizeIsValid(value) {\r\n    return isDefined(value) && value > 0\r\n}\r\nconst baseWidget = isServerSide ? getEmptyComponent() : DOMComponent.inherit({\r\n    _eventsMap: {\r\n        onIncidentOccurred: {\r\n            name: \"incidentOccurred\",\r\n            actionSettings: {\r\n                excludeValidators: [\"disabled\"]\r\n            }\r\n        },\r\n        onDrawn: {\r\n            name: \"drawn\",\r\n            actionSettings: {\r\n                excludeValidators: [\"disabled\"]\r\n            }\r\n        }\r\n    },\r\n    _getDefaultOptions() {\r\n        return extend(this.callBase(), {\r\n            onIncidentOccurred: defaultOnIncidentOccurred\r\n        })\r\n    },\r\n    _useLinks: true,\r\n    _init() {\r\n        this._$element.children(\".dx-sized-element\").remove();\r\n        this._graphicObjects = {};\r\n        this.callBase(...arguments);\r\n        this._changesLocker = 0;\r\n        this._optionChangedLocker = 0;\r\n        this._asyncFirstDrawing = true;\r\n        this._changes = changes();\r\n        this._suspendChanges();\r\n        this._themeManager = this._createThemeManager();\r\n        this._themeManager.setCallback((() => {\r\n            this._requestChange(this._themeDependentChanges)\r\n        }));\r\n        this._renderElementAttributes();\r\n        this._initRenderer();\r\n        const useLinks = this._useLinks;\r\n        if (useLinks) {\r\n            this._renderer.root.enableLinks().virtualLink(\"core\").virtualLink(\"peripheral\")\r\n        }\r\n        this._renderVisibilityChange();\r\n        this._attachVisibilityChangeHandlers();\r\n        this._toggleParentsScrollSubscription(this._isVisible());\r\n        this._initEventTrigger();\r\n        this._incidentOccurred = createIncidentOccurred(this.NAME, this._eventTrigger);\r\n        this._layout = new _Layout;\r\n        if (useLinks) {\r\n            this._renderer.root.linkAfter(\"core\")\r\n        }\r\n        this._initPlugins();\r\n        this._initCore();\r\n        if (useLinks) {\r\n            this._renderer.root.linkAfter()\r\n        }\r\n        this._change(this._initialChanges)\r\n    },\r\n    _createThemeManager() {\r\n        return new BaseThemeManager(this._getThemeManagerOptions())\r\n    },\r\n    _getThemeManagerOptions() {\r\n        return {\r\n            themeSection: this._themeSection,\r\n            fontFields: this._fontFields\r\n        }\r\n    },\r\n    _initialChanges: [\"LAYOUT\", \"RESIZE_HANDLER\", \"THEME\", \"DISABLED\"],\r\n    _initPlugins() {\r\n        each(this._plugins, ((_, plugin) => {\r\n            plugin.init.call(this)\r\n        }))\r\n    },\r\n    _disposePlugins() {\r\n        each(this._plugins.slice().reverse(), ((_, plugin) => {\r\n            plugin.dispose.call(this)\r\n        }))\r\n    },\r\n    _change(codes) {\r\n        this._changes.add(codes)\r\n    },\r\n    _suspendChanges() {\r\n        this._changesLocker += 1\r\n    },\r\n    _resumeChanges() {\r\n        if (0 === --this._changesLocker && this._changes.count() > 0 && !this._applyingChanges) {\r\n            this._renderer.lock();\r\n            this._applyingChanges = true;\r\n            this._applyChanges();\r\n            this._changes.reset();\r\n            this._applyingChanges = false;\r\n            this._changesApplied();\r\n            this._renderer.unlock();\r\n            if (this._optionsQueue) {\r\n                this._applyQueuedOptions()\r\n            }\r\n            this.resolveItemsDeferred(this._legend ? [this._legend] : []);\r\n            this._optionChangedLocker += 1;\r\n            this._notify();\r\n            this._optionChangedLocker -= 1\r\n        }\r\n    },\r\n    resolveItemsDeferred(items) {\r\n        this._resolveDeferred(this._getTemplatesItems(items))\r\n    },\r\n    _collectTemplatesFromItems: items => items.reduce(((prev, i) => ({\r\n        items: prev.items.concat(i.getTemplatesDef()),\r\n        groups: prev.groups.concat(i.getTemplatesGroups())\r\n    })), {\r\n        items: [],\r\n        groups: []\r\n    }),\r\n    _getTemplatesItems(items) {\r\n        const elements = this._collectTemplatesFromItems(items);\r\n        const extraItems = this._getExtraTemplatesItems();\r\n        return {\r\n            items: extraItems.items.concat(elements.items),\r\n            groups: extraItems.groups.concat(elements.groups),\r\n            launchRequest: [extraItems.launchRequest],\r\n            doneRequest: [extraItems.doneRequest]\r\n        }\r\n    },\r\n    _getExtraTemplatesItems: () => ({\r\n        items: [],\r\n        groups: [],\r\n        launchRequest: () => {},\r\n        doneRequest: () => {}\r\n    }),\r\n    _resolveDeferred(_ref) {\r\n        let {\r\n            items: items,\r\n            launchRequest: launchRequest,\r\n            doneRequest: doneRequest,\r\n            groups: groups\r\n        } = _ref;\r\n        this._setGroupsVisibility(groups, \"hidden\");\r\n        if (this._changesApplying) {\r\n            this._changesApplying = false;\r\n            callForEach(doneRequest);\r\n            return\r\n        }\r\n        let syncRendering = true;\r\n        when.apply(this, items).done((() => {\r\n            var _groups$;\r\n            const isGroupInDom = !(null !== (_groups$ = groups[0]) && void 0 !== _groups$ && _groups$.element) || !!$(groups[0].element.closest(\"svg\")).length;\r\n            if (!isGroupInDom) {\r\n                return\r\n            }\r\n            if (syncRendering) {\r\n                this._setGroupsVisibility(groups, \"visible\");\r\n                return\r\n            }\r\n            callForEach(launchRequest);\r\n            this._changesApplying = true;\r\n            const changes = [\"LAYOUT\", \"FULL_RENDER\"];\r\n            if (this._asyncFirstDrawing) {\r\n                changes.push(\"FORCE_FIRST_DRAWING\");\r\n                this._asyncFirstDrawing = false\r\n            } else {\r\n                changes.push(\"FORCE_DRAWING\")\r\n            }\r\n            this._requestChange(changes);\r\n            this._setGroupsVisibility(groups, \"visible\")\r\n        }));\r\n        syncRendering = false\r\n    },\r\n    _setGroupsVisibility(groups, visibility) {\r\n        groups.forEach((g => g.attr({\r\n            visibility: visibility\r\n        })))\r\n    },\r\n    _applyQueuedOptions() {\r\n        const queue = this._optionsQueue;\r\n        this._optionsQueue = null;\r\n        this.beginUpdate();\r\n        each(queue, ((_, action) => {\r\n            action()\r\n        }));\r\n        this.endUpdate()\r\n    },\r\n    _requestChange(codes) {\r\n        this._suspendChanges();\r\n        this._change(codes);\r\n        this._resumeChanges()\r\n    },\r\n    _applyChanges() {\r\n        const changes = this._changes;\r\n        const order = this._totalChangesOrder;\r\n        const changesOrderLength = order.length;\r\n        for (let i = 0; i < changesOrderLength; i += 1) {\r\n            if (changes.has(order[i])) {\r\n                this[`_change_${order[i]}`]()\r\n            }\r\n        }\r\n    },\r\n    _optionChangesOrder: [\"EVENTS\", \"THEME\", \"RENDERER\", \"RESIZE_HANDLER\"],\r\n    _layoutChangesOrder: [\"ELEMENT_ATTR\", \"CONTAINER_SIZE\", \"LAYOUT\"],\r\n    _customChangesOrder: [\"DISABLED\"],\r\n    _change_EVENTS() {\r\n        this._eventTrigger.applyChanges()\r\n    },\r\n    _change_THEME() {\r\n        this._setThemeAndRtl()\r\n    },\r\n    _change_RENDERER() {\r\n        this._setRendererOptions()\r\n    },\r\n    _change_RESIZE_HANDLER() {\r\n        this._setupResizeHandler()\r\n    },\r\n    _change_ELEMENT_ATTR() {\r\n        this._renderElementAttributes();\r\n        this._change([\"CONTAINER_SIZE\"])\r\n    },\r\n    _change_CONTAINER_SIZE() {\r\n        this._updateSize()\r\n    },\r\n    _change_LAYOUT() {\r\n        this._setContentSize()\r\n    },\r\n    _change_DISABLED() {\r\n        const renderer = this._renderer;\r\n        const {\r\n            root: root\r\n        } = renderer;\r\n        if (this.option(\"disabled\")) {\r\n            this._initDisabledState = root.attr(\"pointer-events\");\r\n            root.attr({\r\n                \"pointer-events\": \"none\",\r\n                filter: renderer.getGrayScaleFilter().id\r\n            })\r\n        } else if (\"none\" === root.attr(\"pointer-events\")) {\r\n            root.attr({\r\n                \"pointer-events\": isDefined(this._initDisabledState) ? this._initDisabledState : null,\r\n                filter: null\r\n            })\r\n        }\r\n    },\r\n    _themeDependentChanges: [\"RENDERER\"],\r\n    _initRenderer() {\r\n        const rawCanvas = this._calculateRawCanvas();\r\n        this._canvas = floorCanvasDimensions(rawCanvas);\r\n        this._renderer = new Renderer({\r\n            cssClass: `${this._rootClassPrefix} ${this._rootClass}`,\r\n            pathModified: this.option(\"pathModified\"),\r\n            container: this._$element[0]\r\n        });\r\n        this._renderer.resize(this._canvas.width, this._canvas.height)\r\n    },\r\n    _disposeRenderer() {\r\n        this._renderer.dispose()\r\n    },\r\n    _disposeGraphicObjects() {\r\n        Object.keys(this._graphicObjects).forEach((id => {\r\n            this._graphicObjects[id].dispose()\r\n        }));\r\n        this._graphicObjects = null\r\n    },\r\n    _getAnimationOptions: noop,\r\n    render() {\r\n        this._requestChange([\"CONTAINER_SIZE\"]);\r\n        const visible = this._isVisible();\r\n        this._toggleParentsScrollSubscription(visible);\r\n        !visible && this._stopCurrentHandling()\r\n    },\r\n    _toggleParentsScrollSubscription(subscribe) {\r\n        let $parents = $(this._renderer.root.element).parents();\r\n        if (\"generic\" === devices.real().platform) {\r\n            $parents = $parents.add(getWindow())\r\n        }\r\n        this._proxiedTargetParentsScrollHandler = this._proxiedTargetParentsScrollHandler || function() {\r\n            this._stopCurrentHandling()\r\n        }.bind(this);\r\n        eventsEngine.off($(\"\").add(this._$prevRootParents), \"scroll.viz_widgets\", this._proxiedTargetParentsScrollHandler);\r\n        if (subscribe) {\r\n            eventsEngine.on($parents, \"scroll.viz_widgets\", this._proxiedTargetParentsScrollHandler);\r\n            this._$prevRootParents = $parents\r\n        }\r\n    },\r\n    _stopCurrentHandling: noop,\r\n    _dispose() {\r\n        if (this._disposed) {\r\n            return\r\n        }\r\n        this.callBase(...arguments);\r\n        this._toggleParentsScrollSubscription(false);\r\n        this._removeResizeHandler();\r\n        this._layout.dispose();\r\n        this._eventTrigger.dispose();\r\n        this._disposeCore();\r\n        this._disposePlugins();\r\n        this._disposeGraphicObjects();\r\n        this._disposeRenderer();\r\n        this._themeManager.dispose();\r\n        this._themeManager = null;\r\n        this._renderer = null;\r\n        this._eventTrigger = null\r\n    },\r\n    _initEventTrigger() {\r\n        this._eventTrigger = createEventTrigger(this._eventsMap, ((name, actionSettings) => this._createActionByOption(name, actionSettings)))\r\n    },\r\n    _calculateRawCanvas() {\r\n        const size = this.option(\"size\") || {};\r\n        const margin = this.option(\"margin\") || {};\r\n        const defaultCanvas = this._getDefaultSize() || {};\r\n        const getSizeOfSide = (size, side, getter) => {\r\n            if (sizeIsValid(size[side]) || !hasWindow()) {\r\n                return 0\r\n            }\r\n            const elementSize = getter(this._$element);\r\n            return elementSize <= 1 ? 0 : elementSize\r\n        };\r\n        const elementWidth = getSizeOfSide(size, \"width\", (x => getWidth(x)));\r\n        const elementHeight = getSizeOfSide(size, \"height\", (x => getHeight(x)));\r\n        let canvas = {\r\n            width: size.width <= 0 ? 0 : pickPositiveValue([size.width, elementWidth, defaultCanvas.width]),\r\n            height: size.height <= 0 ? 0 : pickPositiveValue([size.height, elementHeight, defaultCanvas.height]),\r\n            left: pickPositiveValue([margin.left, defaultCanvas.left]),\r\n            top: pickPositiveValue([margin.top, defaultCanvas.top]),\r\n            right: pickPositiveValue([margin.right, defaultCanvas.right]),\r\n            bottom: pickPositiveValue([margin.bottom, defaultCanvas.bottom])\r\n        };\r\n        if (canvas.width - canvas.left - canvas.right <= 0 || canvas.height - canvas.top - canvas.bottom <= 0) {\r\n            canvas = {\r\n                width: 0,\r\n                height: 0\r\n            }\r\n        }\r\n        return canvas\r\n    },\r\n    _updateSize() {\r\n        const rawCanvas = this._calculateRawCanvas();\r\n        if (areCanvasesDifferent(this._canvas, rawCanvas) || this.__forceRender) {\r\n            this._canvas = floorCanvasDimensions(rawCanvas);\r\n            this._recreateSizeDependentObjects(true);\r\n            this._renderer.resize(this._canvas.width, this._canvas.height);\r\n            this._change([\"LAYOUT\"])\r\n        }\r\n    },\r\n    _recreateSizeDependentObjects: noop,\r\n    _getMinSize: () => [0, 0],\r\n    _getAlignmentRect: noop,\r\n    _setContentSize() {\r\n        const canvas = this._canvas;\r\n        const layout = this._layout;\r\n        let rect = canvas.width > 0 && canvas.height > 0 ? [canvas.left, canvas.top, canvas.width - canvas.right, canvas.height - canvas.bottom] : [0, 0, 0, 0];\r\n        rect = layout.forward(rect, this._getMinSize());\r\n        const nextRect = this._applySize(rect) || rect;\r\n        layout.backward(nextRect, this._getAlignmentRect() || nextRect)\r\n    },\r\n    _getOption(name, isScalar) {\r\n        const theme = this._themeManager.theme(name);\r\n        const option = this.option(name);\r\n        return isScalar ? void 0 !== option ? option : theme : extend(true, {}, theme, option)\r\n    },\r\n    _setupResizeHandler() {\r\n        const redrawOnResize = _parseScalar(this._getOption(\"redrawOnResize\", true), true);\r\n        if (this._disposeResizeHandler) {\r\n            this._removeResizeHandler()\r\n        }\r\n        this._disposeResizeHandler = createResizeHandler(this._$element[0], redrawOnResize, (() => this._requestChange([\"CONTAINER_SIZE\"])))\r\n    },\r\n    _removeResizeHandler() {\r\n        if (this._disposeResizeHandler) {\r\n            this._disposeResizeHandler();\r\n            this._disposeResizeHandler = null\r\n        }\r\n    },\r\n    _onBeginUpdate: noop,\r\n    beginUpdate() {\r\n        if (this._initialized && this._isUpdateAllowed()) {\r\n            this._onBeginUpdate();\r\n            this._suspendChanges()\r\n        }\r\n        this.callBase(...arguments);\r\n        return this\r\n    },\r\n    endUpdate() {\r\n        this.callBase();\r\n        this._isUpdateAllowed() && this._resumeChanges();\r\n        return this\r\n    },\r\n    option(name) {\r\n        if (this._initialized && this._applyingChanges && (arguments.length > 1 || _isObject(name))) {\r\n            this._optionsQueue = this._optionsQueue || [];\r\n            this._optionsQueue.push(this._getActionForUpdating(arguments))\r\n        } else {\r\n            return baseOptionMethod.apply(this, arguments)\r\n        }\r\n    },\r\n    _getActionForUpdating(args) {\r\n        return () => {\r\n            baseOptionMethod.apply(this, args)\r\n        }\r\n    },\r\n    _clean: noop,\r\n    _render: noop,\r\n    _optionChanged(arg) {\r\n        if (this._optionChangedLocker) {\r\n            return\r\n        }\r\n        const partialChanges = this.getPartialChangeOptionsName(arg);\r\n        let changes = [];\r\n        if (partialChanges.length > 0) {\r\n            partialChanges.forEach((pc => changes.push(this._partialOptionChangesMap[pc])))\r\n        } else {\r\n            changes.push(this._optionChangesMap[arg.name])\r\n        }\r\n        changes = changes.filter((c => !!c));\r\n        if (this._eventTrigger.change(arg.name)) {\r\n            this._change([\"EVENTS\"])\r\n        } else if (changes.length > 0) {\r\n            this._change(changes)\r\n        } else {\r\n            this.callBase.apply(this, arguments)\r\n        }\r\n    },\r\n    _notify: noop,\r\n    _changesApplied: noop,\r\n    _optionChangesMap: {\r\n        size: \"CONTAINER_SIZE\",\r\n        margin: \"CONTAINER_SIZE\",\r\n        redrawOnResize: \"RESIZE_HANDLER\",\r\n        theme: \"THEME\",\r\n        rtlEnabled: \"THEME\",\r\n        encodeHtml: \"THEME\",\r\n        elementAttr: \"ELEMENT_ATTR\",\r\n        disabled: \"DISABLED\"\r\n    },\r\n    _partialOptionChangesMap: {},\r\n    _partialOptionChangesPath: {},\r\n    getPartialChangeOptionsName(changedOption) {\r\n        const {\r\n            fullName: fullName\r\n        } = changedOption;\r\n        const sections = fullName.split(/[.]/);\r\n        const {\r\n            name: name\r\n        } = changedOption;\r\n        const {\r\n            value: value\r\n        } = changedOption;\r\n        const options = this._partialOptionChangesPath[name];\r\n        const partialChangeOptionsName = [];\r\n        if (options) {\r\n            if (true === options) {\r\n                partialChangeOptionsName.push(name)\r\n            } else {\r\n                options.forEach((op => {\r\n                    fullName.indexOf(op) >= 0 && partialChangeOptionsName.push(op)\r\n                }));\r\n                if (1 === sections.length) {\r\n                    if (\"object\" === type(value)) {\r\n                        this._addOptionsNameForPartialUpdate(value, options, partialChangeOptionsName)\r\n                    } else if (\"array\" === type(value)) {\r\n                        if (value.length > 0 && value.every((item => this._checkOptionsForPartialUpdate(item, options)))) {\r\n                            value.forEach((item => {\r\n                                this._addOptionsNameForPartialUpdate(item, options, partialChangeOptionsName)\r\n                            }))\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return partialChangeOptionsName.filter(((value, index, self) => self.indexOf(value) === index))\r\n    },\r\n    _checkOptionsForPartialUpdate: (optionObject, options) => !Object.keys(optionObject).some((key => -1 === options.indexOf(key))),\r\n    _addOptionsNameForPartialUpdate(optionObject, options, partialChangeOptionsName) {\r\n        const optionKeys = Object.keys(optionObject);\r\n        if (this._checkOptionsForPartialUpdate(optionObject, options)) {\r\n            optionKeys.forEach((key => options.indexOf(key) > -1 && partialChangeOptionsName.push(key)))\r\n        }\r\n    },\r\n    _visibilityChanged() {\r\n        this.render()\r\n    },\r\n    _setThemeAndRtl() {\r\n        this._themeManager.setTheme(this.option(\"theme\"), this.option(\"rtlEnabled\"))\r\n    },\r\n    _getRendererOptions() {\r\n        return {\r\n            rtl: this.option(\"rtlEnabled\"),\r\n            encodeHtml: this.option(\"encodeHtml\"),\r\n            animation: this._getAnimationOptions()\r\n        }\r\n    },\r\n    _setRendererOptions() {\r\n        this._renderer.setOptions(this._getRendererOptions())\r\n    },\r\n    svg() {\r\n        return this._renderer.svg()\r\n    },\r\n    getSize() {\r\n        const canvas = this._canvas || {};\r\n        return {\r\n            width: canvas.width,\r\n            height: canvas.height\r\n        }\r\n    },\r\n    isReady: getFalse,\r\n    _dataIsReady: getTrue,\r\n    _resetIsReady() {\r\n        this.isReady = getFalse\r\n    },\r\n    _renderGraphicObjects() {\r\n        const renderer = this._renderer;\r\n        const graphics = graphicObject.getGraphicObjects();\r\n        Object.keys(graphics).forEach((id => {\r\n            if (!this._graphicObjects[id]) {\r\n                const {\r\n                    type: type,\r\n                    colors: colors,\r\n                    rotationAngle: rotationAngle,\r\n                    template: template,\r\n                    width: width,\r\n                    height: height\r\n                } = graphics[id];\r\n                switch (type) {\r\n                    case \"linear\":\r\n                        this._graphicObjects[id] = renderer.linearGradient(colors, id, rotationAngle);\r\n                        break;\r\n                    case \"radial\":\r\n                        this._graphicObjects[id] = renderer.radialGradient(colors, id);\r\n                        break;\r\n                    case \"pattern\":\r\n                        this._graphicObjects[id] = renderer.customPattern(id, this._getTemplate(template), width, height)\r\n                }\r\n            }\r\n        }))\r\n    },\r\n    _drawn() {\r\n        this.isReady = getFalse;\r\n        if (this._dataIsReady()) {\r\n            this._renderer.onEndAnimation((() => {\r\n                this.isReady = getTrue\r\n            }))\r\n        }\r\n        this._eventTrigger(\"drawn\", {})\r\n    }\r\n});\r\nexport default baseWidget;\r\nreplaceInherit(baseWidget);\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAOA;AAAA;AAIA;AAGA;AAKA;AACA;AAIA;AACA;AAGA;AAGA;AAAA;AAIA;;;;;;;;;;;;;;;;;;;;;;AACA,MAAM,EACF,KAAK,GAAG,EACX,GAAG,mKAAA,CAAA,UAAQ;AACZ,MAAM,qBAAqB;AAC3B,MAAM,sBAAsB;AAC5B,MAAM,mBAAmB,0JAAA,CAAA,UAAY,CAAC,SAAS,CAAC,MAAM;AAEtD,SAAS;IACL,OAAO;AACX;AAEA,SAAS;IACL,OAAO;AACX;AAEA,SAAS,0BAA0B,CAAC;IAChC,IAAI,CAAC,EAAE,SAAS,CAAC,eAAe,CAAC,QAAQ,CAAC,qBAAqB;QAC3D,IAAI,KAAK,CAAC,MAAM;YAAC,EAAE,MAAM,CAAC,EAAE;SAAC,CAAC,MAAM,CAAC,EAAE,MAAM,CAAC,IAAI,IAAI,EAAE;IAC5D;AACJ;AAEA,SAAS,kBAAkB,MAAM;IAC7B,OAAO,OAAO,MAAM,CAAE,CAAC,QAAQ,QAAU,QAAQ,KAAK,CAAC,SAAS,QAAQ,QAAS;AACrF;AACA,MAAM,oBAAoB;IACtB,MAAM,uBAAuB;QACzB,mBAAkB;QAClB,MAAK,OAAO,EAAE,OAAO;YACjB,IAAI,CAAC,QAAQ,CAAC,SAAS;YACvB,MAAM,eAAe,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC;YAC9C,MAAM,QAAQ,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,KAAK,IAAI,GAAG,QAAQ,KAAK,CAAC,EAAE,CAAC,GAAG;YAC3E,MAAM,SAAS,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,IAAI,GAAG,QAAQ,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,eAAe,GAAG,MAAM,CAAC,EAAE,CAAC;YAClH,wJAAA,CAAA,UAAU,CAAC,QAAQ,CAAC,cAAc,SAAS;YAC3C,wJAAA,CAAA,UAAU,CAAC,QAAQ,CAAC,cAAc,UAAU;YAC5C,wJAAA,CAAA,UAAU,CAAC,QAAQ,CAAC,cAAc,oBAAoB;YACtD,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC,SAAS;QACtC;IACJ;IACA,MAAM,iBAAiB,0JAAA,CAAA,UAAY,CAAC,OAAO,CAAC;IAC5C,MAAM,kBAAkB,eAAe,OAAO;IAC9C,eAAe,OAAO,GAAG,SAAS,MAAM;QACpC,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAE,CAAA;YACzB,IAAI,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,MAAM,CAAC,MAAM,KAAK,QAAQ,MAAM,MAAM,CAAC,GAAG,MAAM,aAAa,SAAS,eAAe,SAAS,qBAAqB,OAAO;gBACrI,MAAM,CAAC,MAAM,GAAG,+KAAA,CAAA,OAAI;YACxB;QACJ;QACA,OAAO,gBAAgB,IAAI,CAAC,IAAI,EAAE;IACtC;IACA,OAAO;AACX;AAEA,SAAS,YAAY,SAAS;IAC1B,UAAU,OAAO,CAAE,CAAA,IAAK;AAC5B;AACA,MAAM,eAAe,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;AAE9B,SAAS,YAAY,KAAK;IACtB,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,QAAQ;AACvC;AACA,MAAM,aAAa,eAAe,sBAAsB,0JAAA,CAAA,UAAY,CAAC,OAAO,CAAC;IACzE,YAAY;QACR,oBAAoB;YAChB,MAAM;YACN,gBAAgB;gBACZ,mBAAmB;oBAAC;iBAAW;YACnC;QACJ;QACA,SAAS;YACL,MAAM;YACN,gBAAgB;gBACZ,mBAAmB;oBAAC;iBAAW;YACnC;QACJ;IACJ;IACA;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;YAC3B,oBAAoB;QACxB;IACJ;IACA,WAAW;IACX;QACI,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,qBAAqB,MAAM;QACnD,IAAI,CAAC,eAAe,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ,IAAI;QACjB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD;QACtB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,mBAAmB;QAC7C,IAAI,CAAC,aAAa,CAAC,WAAW,CAAE;YAC5B,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,sBAAsB;QACnD;QACA,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,aAAa;QAClB,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,IAAI,UAAU;YACV,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC,QAAQ,WAAW,CAAC;QACtE;QACA,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,+BAA+B;QACpC,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,UAAU;QACrD,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,wKAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,aAAa;QAC7E,IAAI,CAAC,OAAO,GAAG,IAAI,0JAAA,CAAA,UAAO;QAC1B,IAAI,UAAU;YACV,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC;QAClC;QACA,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,SAAS;QACd,IAAI,UAAU;YACV,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS;QACjC;QACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,eAAe;IACrC;IACA;QACI,OAAO,IAAI,sKAAA,CAAA,mBAAgB,CAAC,IAAI,CAAC,uBAAuB;IAC5D;IACA;QACI,OAAO;YACH,cAAc,IAAI,CAAC,aAAa;YAChC,YAAY,IAAI,CAAC,WAAW;QAChC;IACJ;IACA,iBAAiB;QAAC;QAAU;QAAkB;QAAS;KAAW;IAClE;QACI,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,QAAQ,EAAG,CAAC,GAAG;YACrB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QACzB;IACJ;IACA;QACI,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,OAAO,IAAK,CAAC,GAAG;YACvC,OAAO,OAAO,CAAC,IAAI,CAAC,IAAI;QAC5B;IACJ;IACA,SAAQ,KAAK;QACT,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;IACtB;IACA;QACI,IAAI,CAAC,cAAc,IAAI;IAC3B;IACA;QACI,IAAI,MAAM,EAAE,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,QAAQ,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACpF,IAAI,CAAC,SAAS,CAAC,IAAI;YACnB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,QAAQ,CAAC,KAAK;YACnB,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,SAAS,CAAC,MAAM;YACrB,IAAI,IAAI,CAAC,aAAa,EAAE;gBACpB,IAAI,CAAC,mBAAmB;YAC5B;YACA,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,OAAO,GAAG;gBAAC,IAAI,CAAC,OAAO;aAAC,GAAG,EAAE;YAC5D,IAAI,CAAC,oBAAoB,IAAI;YAC7B,IAAI,CAAC,OAAO;YACZ,IAAI,CAAC,oBAAoB,IAAI;QACjC;IACJ;IACA,sBAAqB,KAAK;QACtB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,kBAAkB,CAAC;IAClD;IACA,4BAA4B,CAAA,QAAS,MAAM,MAAM,CAAE,CAAC,MAAM,IAAM,CAAC;gBAC7D,OAAO,KAAK,KAAK,CAAC,MAAM,CAAC,EAAE,eAAe;gBAC1C,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,kBAAkB;YACnD,CAAC,GAAI;YACD,OAAO,EAAE;YACT,QAAQ,EAAE;QACd;IACA,oBAAmB,KAAK;QACpB,MAAM,WAAW,IAAI,CAAC,0BAA0B,CAAC;QACjD,MAAM,aAAa,IAAI,CAAC,uBAAuB;QAC/C,OAAO;YACH,OAAO,WAAW,KAAK,CAAC,MAAM,CAAC,SAAS,KAAK;YAC7C,QAAQ,WAAW,MAAM,CAAC,MAAM,CAAC,SAAS,MAAM;YAChD,eAAe;gBAAC,WAAW,aAAa;aAAC;YACzC,aAAa;gBAAC,WAAW,WAAW;aAAC;QACzC;IACJ;IACA,yBAAyB,IAAM,CAAC;YAC5B,OAAO,EAAE;YACT,QAAQ,EAAE;YACV,eAAe,KAAO;YACtB,aAAa,KAAO;QACxB,CAAC;IACD,kBAAiB,IAAI;QACjB,IAAI,EACA,OAAO,KAAK,EACZ,eAAe,aAAa,EAC5B,aAAa,WAAW,EACxB,QAAQ,MAAM,EACjB,GAAG;QACJ,IAAI,CAAC,oBAAoB,CAAC,QAAQ;QAClC,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,GAAG;YACxB,YAAY;YACZ;QACJ;QACA,IAAI,gBAAgB;QACpB,iLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,EAAE,OAAO,IAAI,CAAE;YAC1B,IAAI;YACJ,MAAM,eAAe,CAAC,CAAC,SAAS,CAAC,WAAW,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,YAAY,SAAS,OAAO,KAAK,CAAC,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,QAAQ,MAAM;YAClJ,IAAI,CAAC,cAAc;gBACf;YACJ;YACA,IAAI,eAAe;gBACf,IAAI,CAAC,oBAAoB,CAAC,QAAQ;gBAClC;YACJ;YACA,YAAY;YACZ,IAAI,CAAC,gBAAgB,GAAG;YACxB,MAAM,UAAU;gBAAC;gBAAU;aAAc;YACzC,IAAI,IAAI,CAAC,kBAAkB,EAAE;gBACzB,QAAQ,IAAI,CAAC;gBACb,IAAI,CAAC,kBAAkB,GAAG;YAC9B,OAAO;gBACH,QAAQ,IAAI,CAAC;YACjB;YACA,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,oBAAoB,CAAC,QAAQ;QACtC;QACA,gBAAgB;IACpB;IACA,sBAAqB,MAAM,EAAE,UAAU;QACnC,OAAO,OAAO,CAAE,CAAA,IAAK,EAAE,IAAI,CAAC;gBACxB,YAAY;YAChB;IACJ;IACA;QACI,MAAM,QAAQ,IAAI,CAAC,aAAa;QAChC,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,WAAW;QAChB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,GAAG;YACb;QACJ;QACA,IAAI,CAAC,SAAS;IAClB;IACA,gBAAe,KAAK;QAChB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,OAAO,CAAC;QACb,IAAI,CAAC,cAAc;IACvB;IACA;QACI,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,QAAQ,IAAI,CAAC,kBAAkB;QACrC,MAAM,qBAAqB,MAAM,MAAM;QACvC,IAAK,IAAI,IAAI,GAAG,IAAI,oBAAoB,KAAK,EAAG;YAC5C,IAAI,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE,GAAG;gBACvB,IAAI,CAAC,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC;YAC/B;QACJ;IACJ;IACA,qBAAqB;QAAC;QAAU;QAAS;QAAY;KAAiB;IACtE,qBAAqB;QAAC;QAAgB;QAAkB;KAAS;IACjE,qBAAqB;QAAC;KAAW;IACjC;QACI,IAAI,CAAC,aAAa,CAAC,YAAY;IACnC;IACA;QACI,IAAI,CAAC,eAAe;IACxB;IACA;QACI,IAAI,CAAC,mBAAmB;IAC5B;IACA;QACI,IAAI,CAAC,mBAAmB;IAC5B;IACA;QACI,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,OAAO,CAAC;YAAC;SAAiB;IACnC;IACA;QACI,IAAI,CAAC,WAAW;IACpB;IACA;QACI,IAAI,CAAC,eAAe;IACxB;IACA;QACI,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa;YACzB,IAAI,CAAC,kBAAkB,GAAG,KAAK,IAAI,CAAC;YACpC,KAAK,IAAI,CAAC;gBACN,kBAAkB;gBAClB,QAAQ,SAAS,kBAAkB,GAAG,EAAE;YAC5C;QACJ,OAAO,IAAI,WAAW,KAAK,IAAI,CAAC,mBAAmB;YAC/C,KAAK,IAAI,CAAC;gBACN,kBAAkB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,kBAAkB,GAAG;gBACjF,QAAQ;YACZ;QACJ;IACJ;IACA,wBAAwB;QAAC;KAAW;IACpC;QACI,MAAM,YAAY,IAAI,CAAC,mBAAmB;QAC1C,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE;QACrC,IAAI,CAAC,SAAS,GAAG,IAAI,yKAAA,CAAA,WAAQ,CAAC;YAC1B,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,EAAE;YACvD,cAAc,IAAI,CAAC,MAAM,CAAC;YAC1B,WAAW,IAAI,CAAC,SAAS,CAAC,EAAE;QAChC;QACA,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;IACjE;IACA;QACI,IAAI,CAAC,SAAS,CAAC,OAAO;IAC1B;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,OAAO,CAAE,CAAA;YACvC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,OAAO;QACpC;QACA,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,sBAAsB,+KAAA,CAAA,OAAI;IAC1B;QACI,IAAI,CAAC,cAAc,CAAC;YAAC;SAAiB;QACtC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,CAAC,gCAAgC,CAAC;QACtC,CAAC,WAAW,IAAI,CAAC,oBAAoB;IACzC;IACA,kCAAiC,SAAS;QACtC,IAAI,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO;QACrD,IAAI,cAAc,oJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,QAAQ,EAAE;YACvC,WAAW,SAAS,GAAG,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;QACpC;QACA,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,kCAAkC,IAAI,CAAA;YACjF,IAAI,CAAC,oBAAoB;QAC7B,CAAA,EAAE,IAAI,CAAC,IAAI;QACX,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,GAAG,CAAC,IAAI,CAAC,iBAAiB,GAAG,sBAAsB,IAAI,CAAC,kCAAkC;QACjH,IAAI,WAAW;YACX,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,UAAU,sBAAsB,IAAI,CAAC,kCAAkC;YACvF,IAAI,CAAC,iBAAiB,GAAG;QAC7B;IACJ;IACA,sBAAsB,+KAAA,CAAA,OAAI;IAC1B;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB;QACJ;QACA,IAAI,CAAC,QAAQ,IAAI;QACjB,IAAI,CAAC,gCAAgC,CAAC;QACtC,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,OAAO,CAAC,OAAO;QACpB,IAAI,CAAC,aAAa,CAAC,OAAO;QAC1B,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,aAAa,CAAC,OAAO;QAC1B,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,aAAa,GAAG;IACzB;IACA;QACI,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,CAAC,UAAU,EAAG,CAAC,MAAM,iBAAmB,IAAI,CAAC,qBAAqB,CAAC,MAAM;IACzH;IACA;QACI,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC;QACrC,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;QACzC,MAAM,gBAAgB,IAAI,CAAC,eAAe,MAAM,CAAC;QACjD,MAAM,gBAAgB,CAAC,MAAM,MAAM;YAC/B,IAAI,YAAY,IAAI,CAAC,KAAK,KAAK,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;gBACzC,OAAO;YACX;YACA,MAAM,cAAc,OAAO,IAAI,CAAC,SAAS;YACzC,OAAO,eAAe,IAAI,IAAI;QAClC;QACA,MAAM,eAAe,cAAc,MAAM,SAAU,CAAA,IAAK,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE;QACjE,MAAM,gBAAgB,cAAc,MAAM,UAAW,CAAA,IAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;QACpE,IAAI,SAAS;YACT,OAAO,KAAK,KAAK,IAAI,IAAI,IAAI,kBAAkB;gBAAC,KAAK,KAAK;gBAAE;gBAAc,cAAc,KAAK;aAAC;YAC9F,QAAQ,KAAK,MAAM,IAAI,IAAI,IAAI,kBAAkB;gBAAC,KAAK,MAAM;gBAAE;gBAAe,cAAc,MAAM;aAAC;YACnG,MAAM,kBAAkB;gBAAC,OAAO,IAAI;gBAAE,cAAc,IAAI;aAAC;YACzD,KAAK,kBAAkB;gBAAC,OAAO,GAAG;gBAAE,cAAc,GAAG;aAAC;YACtD,OAAO,kBAAkB;gBAAC,OAAO,KAAK;gBAAE,cAAc,KAAK;aAAC;YAC5D,QAAQ,kBAAkB;gBAAC,OAAO,MAAM;gBAAE,cAAc,MAAM;aAAC;QACnE;QACA,IAAI,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK,IAAI,KAAK,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM,IAAI,GAAG;YACnG,SAAS;gBACL,OAAO;gBACP,QAAQ;YACZ;QACJ;QACA,OAAO;IACX;IACA;QACI,MAAM,YAAY,IAAI,CAAC,mBAAmB;QAC1C,IAAI,CAAA,GAAA,iKAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,cAAc,IAAI,CAAC,aAAa,EAAE;YACrE,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE;YACrC,IAAI,CAAC,6BAA6B,CAAC;YACnC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;YAC7D,IAAI,CAAC,OAAO,CAAC;gBAAC;aAAS;QAC3B;IACJ;IACA,+BAA+B,+KAAA,CAAA,OAAI;IACnC,aAAa,IAAM;YAAC;YAAG;SAAE;IACzB,mBAAmB,+KAAA,CAAA,OAAI;IACvB;QACI,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI,OAAO,OAAO,KAAK,GAAG,KAAK,OAAO,MAAM,GAAG,IAAI;YAAC,OAAO,IAAI;YAAE,OAAO,GAAG;YAAE,OAAO,KAAK,GAAG,OAAO,KAAK;YAAE,OAAO,MAAM,GAAG,OAAO,MAAM;SAAC,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE;QACvJ,OAAO,OAAO,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW;QAC5C,MAAM,WAAW,IAAI,CAAC,UAAU,CAAC,SAAS;QAC1C,OAAO,QAAQ,CAAC,UAAU,IAAI,CAAC,iBAAiB,MAAM;IAC1D;IACA,YAAW,IAAI,EAAE,QAAQ;QACrB,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;QACvC,MAAM,SAAS,IAAI,CAAC,MAAM,CAAC;QAC3B,OAAO,WAAW,KAAK,MAAM,SAAS,SAAS,QAAQ,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,OAAO;IACnF;IACA;QACI,MAAM,iBAAiB,CAAA,GAAA,yJAAA,CAAA,cAAY,AAAD,EAAE,IAAI,CAAC,UAAU,CAAC,kBAAkB,OAAO;QAC7E,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,oBAAoB;QAC7B;QACA,IAAI,CAAC,qBAAqB,GAAG,CAAA,GAAA,wKAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE,gBAAiB,IAAM,IAAI,CAAC,cAAc,CAAC;gBAAC;aAAiB;IACrI;IACA;QACI,IAAI,IAAI,CAAC,qBAAqB,EAAE;YAC5B,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,qBAAqB,GAAG;QACjC;IACJ;IACA,gBAAgB,+KAAA,CAAA,OAAI;IACpB;QACI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,IAAI;YAC9C,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,eAAe;QACxB;QACA,IAAI,CAAC,QAAQ,IAAI;QACjB,OAAO,IAAI;IACf;IACA;QACI,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,gBAAgB,MAAM,IAAI,CAAC,cAAc;QAC9C,OAAO,IAAI;IACf;IACA,QAAO,IAAI;QACP,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,gBAAgB,IAAI,CAAC,UAAU,MAAM,GAAG,KAAK,CAAA,GAAA,6KAAA,CAAA,WAAS,AAAD,EAAE,KAAK,GAAG;YACzF,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI,EAAE;YAC7C,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC;QACvD,OAAO;YACH,OAAO,iBAAiB,KAAK,CAAC,IAAI,EAAE;QACxC;IACJ;IACA,uBAAsB,IAAI;QACtB,OAAO;YACH,iBAAiB,KAAK,CAAC,IAAI,EAAE;QACjC;IACJ;IACA,QAAQ,+KAAA,CAAA,OAAI;IACZ,SAAS,+KAAA,CAAA,OAAI;IACb,gBAAe,GAAG;QACd,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B;QACJ;QACA,MAAM,iBAAiB,IAAI,CAAC,2BAA2B,CAAC;QACxD,IAAI,UAAU,EAAE;QAChB,IAAI,eAAe,MAAM,GAAG,GAAG;YAC3B,eAAe,OAAO,CAAE,CAAA,KAAM,QAAQ,IAAI,CAAC,IAAI,CAAC,wBAAwB,CAAC,GAAG;QAChF,OAAO;YACH,QAAQ,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,IAAI,CAAC;QACjD;QACA,UAAU,QAAQ,MAAM,CAAE,CAAA,IAAK,CAAC,CAAC;QACjC,IAAI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,IAAI,GAAG;YACrC,IAAI,CAAC,OAAO,CAAC;gBAAC;aAAS;QAC3B,OAAO,IAAI,QAAQ,MAAM,GAAG,GAAG;YAC3B,IAAI,CAAC,OAAO,CAAC;QACjB,OAAO;YACH,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QAC9B;IACJ;IACA,SAAS,+KAAA,CAAA,OAAI;IACb,iBAAiB,+KAAA,CAAA,OAAI;IACrB,mBAAmB;QACf,MAAM;QACN,QAAQ;QACR,gBAAgB;QAChB,OAAO;QACP,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,UAAU;IACd;IACA,0BAA0B,CAAC;IAC3B,2BAA2B,CAAC;IAC5B,6BAA4B,aAAa;QACrC,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;QACJ,MAAM,WAAW,SAAS,KAAK,CAAC;QAChC,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,MAAM,EACF,OAAO,KAAK,EACf,GAAG;QACJ,MAAM,UAAU,IAAI,CAAC,yBAAyB,CAAC,KAAK;QACpD,MAAM,2BAA2B,EAAE;QACnC,IAAI,SAAS;YACT,IAAI,SAAS,SAAS;gBAClB,yBAAyB,IAAI,CAAC;YAClC,OAAO;gBACH,QAAQ,OAAO,CAAE,CAAA;oBACb,SAAS,OAAO,CAAC,OAAO,KAAK,yBAAyB,IAAI,CAAC;gBAC/D;gBACA,IAAI,MAAM,SAAS,MAAM,EAAE;oBACvB,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;wBAC1B,IAAI,CAAC,+BAA+B,CAAC,OAAO,SAAS;oBACzD,OAAO,IAAI,YAAY,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;wBAChC,IAAI,MAAM,MAAM,GAAG,KAAK,MAAM,KAAK,CAAE,CAAA,OAAQ,IAAI,CAAC,6BAA6B,CAAC,MAAM,WAAY;4BAC9F,MAAM,OAAO,CAAE,CAAA;gCACX,IAAI,CAAC,+BAA+B,CAAC,MAAM,SAAS;4BACxD;wBACJ;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO,yBAAyB,MAAM,CAAE,CAAC,OAAO,OAAO,OAAS,KAAK,OAAO,CAAC,WAAW;IAC5F;IACA,+BAA+B,CAAC,cAAc,UAAY,CAAC,OAAO,IAAI,CAAC,cAAc,IAAI,CAAE,CAAA,MAAO,CAAC,MAAM,QAAQ,OAAO,CAAC;IACzH,iCAAgC,YAAY,EAAE,OAAO,EAAE,wBAAwB;QAC3E,MAAM,aAAa,OAAO,IAAI,CAAC;QAC/B,IAAI,IAAI,CAAC,6BAA6B,CAAC,cAAc,UAAU;YAC3D,WAAW,OAAO,CAAE,CAAA,MAAO,QAAQ,OAAO,CAAC,OAAO,CAAC,KAAK,yBAAyB,IAAI,CAAC;QAC1F;IACJ;IACA;QACI,IAAI,CAAC,MAAM;IACf;IACA;QACI,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC;IAClE;IACA;QACI,OAAO;YACH,KAAK,IAAI,CAAC,MAAM,CAAC;YACjB,YAAY,IAAI,CAAC,MAAM,CAAC;YACxB,WAAW,IAAI,CAAC,oBAAoB;QACxC;IACJ;IACA;QACI,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB;IACtD;IACA;QACI,OAAO,IAAI,CAAC,SAAS,CAAC,GAAG;IAC7B;IACA;QACI,MAAM,SAAS,IAAI,CAAC,OAAO,IAAI,CAAC;QAChC,OAAO;YACH,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;QACzB;IACJ;IACA,SAAS;IACT,cAAc;IACd;QACI,IAAI,CAAC,OAAO,GAAG;IACnB;IACA;QACI,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,WAAW,wKAAA,CAAA,UAAa,CAAC,iBAAiB;QAChD,OAAO,IAAI,CAAC,UAAU,OAAO,CAAE,CAAA;YAC3B,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE;gBAC3B,MAAM,EACF,MAAM,IAAI,EACV,QAAQ,MAAM,EACd,eAAe,aAAa,EAC5B,UAAU,QAAQ,EAClB,OAAO,KAAK,EACZ,QAAQ,MAAM,EACjB,GAAG,QAAQ,CAAC,GAAG;gBAChB,OAAQ;oBACJ,KAAK;wBACD,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,SAAS,cAAc,CAAC,QAAQ,IAAI;wBAC/D;oBACJ,KAAK;wBACD,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,SAAS,cAAc,CAAC,QAAQ;wBAC3D;oBACJ,KAAK;wBACD,IAAI,CAAC,eAAe,CAAC,GAAG,GAAG,SAAS,aAAa,CAAC,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW,OAAO;gBAClG;YACJ;QACJ;IACJ;IACA;QACI,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,IAAI,CAAC,YAAY,IAAI;YACrB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAE;gBAC3B,IAAI,CAAC,OAAO,GAAG;YACnB;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,SAAS,CAAC;IACjC;AACJ;uCACe;AACf,CAAA,GAAA,2JAAA,CAAA,iBAAc,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4820, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/chart_components/rolling_stock.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/chart_components/rolling_stock.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport class RollingStock {\r\n    constructor(label, isRotated, shiftFunction) {\r\n        const bBox = label.getBoundingRect();\r\n        const {\r\n            x: x\r\n        } = bBox;\r\n        const {\r\n            y: y\r\n        } = bBox;\r\n        const endX = bBox.x + bBox.width;\r\n        const endY = bBox.y + bBox.height;\r\n        this.labels = [label];\r\n        this.shiftFunction = shiftFunction;\r\n        this.bBox = {\r\n            start: isRotated ? x : y,\r\n            width: isRotated ? bBox.width : bBox.height,\r\n            end: isRotated ? endX : endY,\r\n            oppositeStart: isRotated ? y : x,\r\n            oppositeEnd: isRotated ? endY : endX\r\n        };\r\n        this.initialPosition = isRotated ? bBox.x : bBox.y\r\n    }\r\n    toChain(nextRollingStock) {\r\n        const nextRollingStockBBox = nextRollingStock.getBoundingRect();\r\n        nextRollingStock.shift(nextRollingStockBBox.start - this.bBox.end);\r\n        this.changeBoxWidth(nextRollingStockBBox.width);\r\n        this.labels = this.labels.concat(nextRollingStock.labels)\r\n    }\r\n    getBoundingRect() {\r\n        return this.bBox\r\n    }\r\n    shift(shiftLength) {\r\n        this.labels.forEach((label => {\r\n            const bBox = label.getBoundingRect();\r\n            const coords = this.shiftFunction(bBox, shiftLength);\r\n            if (!label.hideInsideLabel(coords)) {\r\n                label.shift(coords.x, coords.y)\r\n            }\r\n        }));\r\n        this.bBox.end -= shiftLength;\r\n        this.bBox.start -= shiftLength\r\n    }\r\n    setRollingStockInCanvas(canvas) {\r\n        if (this.bBox.end > canvas.end) {\r\n            this.shift(this.bBox.end - canvas.end)\r\n        }\r\n    }\r\n    getLabels() {\r\n        return this.labels\r\n    }\r\n    value() {\r\n        return this.labels[0].getData().value\r\n    }\r\n    getInitialPosition() {\r\n        return this.initialPosition\r\n    }\r\n    changeBoxWidth(width) {\r\n        this.bBox.end += width;\r\n        this.bBox.width += width\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,MAAM;IACT,YAAY,KAAK,EAAE,SAAS,EAAE,aAAa,CAAE;QACzC,MAAM,OAAO,MAAM,eAAe;QAClC,MAAM,EACF,GAAG,CAAC,EACP,GAAG;QACJ,MAAM,EACF,GAAG,CAAC,EACP,GAAG;QACJ,MAAM,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK;QAChC,MAAM,OAAO,KAAK,CAAC,GAAG,KAAK,MAAM;QACjC,IAAI,CAAC,MAAM,GAAG;YAAC;SAAM;QACrB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,IAAI,GAAG;YACR,OAAO,YAAY,IAAI;YACvB,OAAO,YAAY,KAAK,KAAK,GAAG,KAAK,MAAM;YAC3C,KAAK,YAAY,OAAO;YACxB,eAAe,YAAY,IAAI;YAC/B,aAAa,YAAY,OAAO;QACpC;QACA,IAAI,CAAC,eAAe,GAAG,YAAY,KAAK,CAAC,GAAG,KAAK,CAAC;IACtD;IACA,QAAQ,gBAAgB,EAAE;QACtB,MAAM,uBAAuB,iBAAiB,eAAe;QAC7D,iBAAiB,KAAK,CAAC,qBAAqB,KAAK,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG;QACjE,IAAI,CAAC,cAAc,CAAC,qBAAqB,KAAK;QAC9C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,iBAAiB,MAAM;IAC5D;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,MAAM,WAAW,EAAE;QACf,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,MAAM,OAAO,MAAM,eAAe;YAClC,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,MAAM;YACxC,IAAI,CAAC,MAAM,eAAe,CAAC,SAAS;gBAChC,MAAM,KAAK,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC;YAClC;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;IACvB;IACA,wBAAwB,MAAM,EAAE;QAC5B,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG,EAAE;YAC5B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,OAAO,GAAG;QACzC;IACJ;IACA,YAAY;QACR,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,OAAO,GAAG,KAAK;IACzC;IACA,qBAAqB;QACjB,OAAO,IAAI,CAAC,eAAe;IAC/B;IACA,eAAe,KAAK,EAAE;QAClB,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,IAAI;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4893, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/chart_components/m_base_chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/chart_components/m_base_chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    isPointerEvent,\r\n    isTouchEvent\r\n} from \"../../../common/core/events/utils/index\";\r\nimport {\r\n    grep,\r\n    noop\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    reverseEach as _reverseEach\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isDefined as _isDefined,\r\n    isFunction\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    LayoutManager\r\n} from \"../../../viz/chart_components/layout_manager\";\r\nimport * as trackerModule from \"../../../viz/chart_components/tracker\";\r\nimport {\r\n    ThemeManager\r\n} from \"../../../viz/components/chart_theme_manager\";\r\nimport {\r\n    validateData\r\n} from \"../../../viz/components/data_validator\";\r\nimport {\r\n    Legend\r\n} from \"../../../viz/components/legend\";\r\nimport {\r\n    plugin as dataSourcePlugin\r\n} from \"../../../viz/core/data_source\";\r\nimport {\r\n    plugin as exportPlugin\r\n} from \"../../../viz/core/export\";\r\nimport {\r\n    plugin as loadingIndicatorPlugin\r\n} from \"../../../viz/core/loading_indicator\";\r\nimport {\r\n    plugin as titlePlugin\r\n} from \"../../../viz/core/title\";\r\nimport {\r\n    plugin as tooltipPlugin\r\n} from \"../../../viz/core/tooltip\";\r\nimport {\r\n    map as _map,\r\n    processSeriesTemplate,\r\n    setCanvasValues as _setCanvasValues\r\n} from \"../../../viz/core/utils\";\r\nimport {\r\n    Series\r\n} from \"../../../viz/series/base_series\";\r\nimport BaseWidget from \"../core/m_base_widget\";\r\nimport {\r\n    RollingStock\r\n} from \"./rolling_stock\";\r\nconst {\r\n    isArray: isArray\r\n} = Array;\r\nconst REINIT_REFRESH_ACTION = \"_reinit\";\r\nconst REINIT_DATA_SOURCE_REFRESH_ACTION = \"_updateDataSource\";\r\nconst DATA_INIT_REFRESH_ACTION = \"_dataInit\";\r\nconst FORCE_RENDER_REFRESH_ACTION = \"_forceRender\";\r\nconst RESIZE_REFRESH_ACTION = \"_resize\";\r\nconst ACTIONS_BY_PRIORITY = [\"_reinit\", \"_updateDataSource\", \"_dataInit\", \"_forceRender\", \"_resize\"];\r\nconst DEFAULT_OPACITY = .3;\r\nconst REFRESH_SERIES_DATA_INIT_ACTION_OPTIONS = [\"series\", \"commonSeriesSettings\", \"dataPrepareSettings\", \"seriesSelectionMode\", \"pointSelectionMode\", \"synchronizeMultiAxes\", \"resolveLabelsOverlapping\"];\r\nconst REFRESH_SERIES_FAMILIES_ACTION_OPTIONS = [\"minBubbleSize\", \"maxBubbleSize\", \"barGroupPadding\", \"barGroupWidth\", \"negativesAsZeroes\", \"negativesAsZeros\"];\r\nconst FORCE_RENDER_REFRESH_ACTION_OPTIONS = [\"adaptiveLayout\", \"crosshair\", \"resolveLabelOverlapping\", \"adjustOnZoom\", \"stickyHovering\"];\r\nconst FONT = \"font\";\r\n\r\nfunction checkHeightRollingStock(rollingStocks, stubCanvas) {\r\n    const canvasSize = stubCanvas.end - stubCanvas.start;\r\n    let size = 0;\r\n    rollingStocks.forEach((rollingStock => {\r\n        size += rollingStock.getBoundingRect().width\r\n    }));\r\n    while (canvasSize < size) {\r\n        size -= findAndKillSmallValue(rollingStocks)\r\n    }\r\n}\r\n\r\nfunction findAndKillSmallValue(rollingStocks) {\r\n    const smallestObject = rollingStocks.reduce(((prev, rollingStock, index) => {\r\n        if (!rollingStock) {\r\n            return prev\r\n        }\r\n        const value = rollingStock.value();\r\n        return value < prev.value ? {\r\n            value: value,\r\n            rollingStock: rollingStock,\r\n            index: index\r\n        } : prev\r\n    }), {\r\n        rollingStock: void 0,\r\n        value: 1 / 0,\r\n        index: void 0\r\n    });\r\n    smallestObject.rollingStock.getLabels()[0].draw(false);\r\n    const {\r\n        width: width\r\n    } = smallestObject.rollingStock.getBoundingRect();\r\n    rollingStocks[smallestObject.index] = null;\r\n    return width\r\n}\r\n\r\nfunction checkStackOverlap(rollingStocks) {\r\n    let i;\r\n    let j;\r\n    let iLength;\r\n    let jLength;\r\n    let overlap = false;\r\n    for (i = 0, iLength = rollingStocks.length - 1; i < iLength; i++) {\r\n        for (j = i + 1, jLength = rollingStocks.length; j < jLength; j++) {\r\n            if (i !== j && checkStacksOverlapping(rollingStocks[i], rollingStocks[j], true)) {\r\n                overlap = true;\r\n                break\r\n            }\r\n        }\r\n        if (overlap) {\r\n            break\r\n        }\r\n    }\r\n    return overlap\r\n}\r\n\r\nfunction resolveLabelOverlappingInOneDirection(points, canvas, isRotated, isInverted, shiftFunction) {\r\n    let customSorting = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : () => 0;\r\n    const rollingStocks = [];\r\n    const stubCanvas = {\r\n        start: isRotated ? canvas.left : canvas.top,\r\n        end: isRotated ? canvas.width - canvas.right : canvas.height - canvas.bottom\r\n    };\r\n    let hasStackedSeries = false;\r\n    let sortRollingStocks;\r\n    points.forEach((p => {\r\n        if (!p) {\r\n            return\r\n        }\r\n        hasStackedSeries = hasStackedSeries || p.series.isStackedSeries() || p.series.isFullStackedSeries();\r\n        p.getLabels().forEach((l => {\r\n            if (l.isVisible()) {\r\n                rollingStocks.push(new RollingStock(l, isRotated, shiftFunction))\r\n            }\r\n        }))\r\n    }));\r\n    if (hasStackedSeries) {\r\n        if (Number(!isRotated) ^ Number(isInverted)) {\r\n            rollingStocks.reverse()\r\n        }\r\n        sortRollingStocks = isInverted ? rollingStocks : sortRollingStocksByValue(rollingStocks)\r\n    } else {\r\n        const rollingStocksTmp = rollingStocks.slice();\r\n        sortRollingStocks = rollingStocks.sort(((a, b) => customSorting(a, b) || a.getInitialPosition() - b.getInitialPosition() || rollingStocksTmp.indexOf(a) - rollingStocksTmp.indexOf(b)))\r\n    }\r\n    if (!checkStackOverlap(sortRollingStocks)) {\r\n        return false\r\n    }\r\n    checkHeightRollingStock(sortRollingStocks, stubCanvas);\r\n    prepareOverlapStacks(sortRollingStocks);\r\n    sortRollingStocks.reverse();\r\n    moveRollingStock(sortRollingStocks, stubCanvas);\r\n    return true\r\n}\r\n\r\nfunction checkStacksOverlapping(firstRolling, secondRolling, inTwoSides) {\r\n    if (!firstRolling || !secondRolling) {\r\n        return\r\n    }\r\n    const firstRect = firstRolling.getBoundingRect();\r\n    const secondRect = secondRolling.getBoundingRect();\r\n    const oppositeOverlapping = inTwoSides ? firstRect.oppositeStart <= secondRect.oppositeStart && firstRect.oppositeEnd > secondRect.oppositeStart || secondRect.oppositeStart <= firstRect.oppositeStart && secondRect.oppositeEnd > firstRect.oppositeStart : true;\r\n    return firstRect.end > secondRect.start && oppositeOverlapping\r\n}\r\n\r\nfunction sortRollingStocksByValue(rollingStocks) {\r\n    const positiveRollingStocks = [];\r\n    const negativeRollingStocks = [];\r\n    rollingStocks.forEach((stock => {\r\n        if (stock.value() > 0) {\r\n            positiveRollingStocks.push(stock)\r\n        } else {\r\n            negativeRollingStocks.unshift(stock)\r\n        }\r\n    }));\r\n    return positiveRollingStocks.concat(negativeRollingStocks)\r\n}\r\n\r\nfunction prepareOverlapStacks(rollingStocks) {\r\n    let root;\r\n    for (let i = 0; i < rollingStocks.length - 1; i += 1) {\r\n        const currentRollingStock = root || rollingStocks[i];\r\n        if (checkStacksOverlapping(currentRollingStock, rollingStocks[i + 1])) {\r\n            currentRollingStock.toChain(rollingStocks[i + 1]);\r\n            rollingStocks[i + 1] = null;\r\n            root = currentRollingStock\r\n        } else {\r\n            root = rollingStocks[i + 1] || currentRollingStock\r\n        }\r\n    }\r\n}\r\n\r\nfunction rollingStocksIsOut(rollingStock, canvas) {\r\n    return rollingStock.getBoundingRect().end > canvas.end\r\n}\r\n\r\nfunction moveRollingStock(rollingStocks, canvas) {\r\n    for (let i = 0; i < rollingStocks.length; i += 1) {\r\n        const currentRollingStock = rollingStocks[i];\r\n        let shouldSetCanvas = true;\r\n        if (null !== currentRollingStock && rollingStocksIsOut(currentRollingStock, canvas)) {\r\n            const currentBBox = currentRollingStock.getBoundingRect();\r\n            for (let j = i + 1; j < rollingStocks.length; j += 1) {\r\n                const nextRollingStock = rollingStocks[j];\r\n                if (nextRollingStock) {\r\n                    const nextBBox = nextRollingStock.getBoundingRect();\r\n                    if (nextBBox.end > currentBBox.start - (currentBBox.end - canvas.end)) {\r\n                        nextRollingStock.toChain(currentRollingStock);\r\n                        shouldSetCanvas = false;\r\n                        break\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        if (shouldSetCanvas) {\r\n            null === currentRollingStock || void 0 === currentRollingStock || currentRollingStock.setRollingStockInCanvas(canvas)\r\n        }\r\n    }\r\n}\r\n\r\nfunction getLegendFields(name) {\r\n    return {\r\n        nameField: `${name}Name`,\r\n        colorField: `${name}Color`,\r\n        indexField: `${name}Index`\r\n    }\r\n}\r\n\r\nfunction getLegendSettings(legendDataField) {\r\n    const formatObjectFields = getLegendFields(legendDataField);\r\n    return {\r\n        getFormatObject(data) {\r\n            const res = {};\r\n            res[formatObjectFields.indexField] = data.id;\r\n            res[formatObjectFields.colorField] = data.states.normal.fill;\r\n            res[formatObjectFields.nameField] = data.text;\r\n            return res\r\n        },\r\n        textField: formatObjectFields.nameField\r\n    }\r\n}\r\n\r\nfunction checkOverlapping(firstRect, secondRect) {\r\n    return (firstRect.x <= secondRect.x && secondRect.x <= firstRect.x + firstRect.width || firstRect.x >= secondRect.x && firstRect.x <= secondRect.x + secondRect.width) && (firstRect.y <= secondRect.y && secondRect.y <= firstRect.y + firstRect.height || firstRect.y >= secondRect.y && firstRect.y <= secondRect.y + secondRect.height)\r\n}\r\nexport const overlapping = {\r\n    resolveLabelOverlappingInOneDirection: resolveLabelOverlappingInOneDirection\r\n};\r\nexport const BaseChart = BaseWidget.inherit({\r\n    _eventsMap: {\r\n        onSeriesClick: {\r\n            name: \"seriesClick\"\r\n        },\r\n        onPointClick: {\r\n            name: \"pointClick\"\r\n        },\r\n        onArgumentAxisClick: {\r\n            name: \"argumentAxisClick\"\r\n        },\r\n        onLegendClick: {\r\n            name: \"legendClick\"\r\n        },\r\n        onSeriesSelectionChanged: {\r\n            name: \"seriesSelectionChanged\"\r\n        },\r\n        onPointSelectionChanged: {\r\n            name: \"pointSelectionChanged\"\r\n        },\r\n        onSeriesHoverChanged: {\r\n            name: \"seriesHoverChanged\"\r\n        },\r\n        onPointHoverChanged: {\r\n            name: \"pointHoverChanged\"\r\n        },\r\n        onDone: {\r\n            name: \"done\",\r\n            actionSettings: {\r\n                excludeValidators: [\"disabled\"]\r\n            }\r\n        },\r\n        onZoomStart: {\r\n            name: \"zoomStart\"\r\n        },\r\n        onZoomEnd: {\r\n            name: \"zoomEnd\"\r\n        }\r\n    },\r\n    _fontFields: [`legend.${FONT}`, `legend.title.${FONT}`, `legend.title.subtitle.${FONT}`, `commonSeriesSettings.label.${FONT}`],\r\n    _rootClassPrefix: \"dxc\",\r\n    _rootClass: \"dxc-chart\",\r\n    _initialChanges: [\"INIT\"],\r\n    _themeDependentChanges: [\"REFRESH_SERIES_REINIT\"],\r\n    _getThemeManagerOptions() {\r\n        const themeOptions = this.callBase.apply(this, arguments);\r\n        themeOptions.options = this.option();\r\n        return themeOptions\r\n    },\r\n    _createThemeManager() {\r\n        const chartOption = this.option();\r\n        const themeManager = new ThemeManager(this._getThemeManagerOptions());\r\n        themeManager.setTheme(chartOption.theme, chartOption.rtlEnabled);\r\n        return themeManager\r\n    },\r\n    _initCore() {\r\n        this._canvasClipRect = this._renderer.clipRect();\r\n        this._createHtmlStructure();\r\n        this._createLegend();\r\n        this._createTracker();\r\n        this._needHandleRenderComplete = true;\r\n        this.layoutManager = new LayoutManager;\r\n        this._createScrollBar();\r\n        eventsEngine.on(this._$element, \"contextmenu\", (event => {\r\n            if (isTouchEvent(event) || isPointerEvent(event)) {\r\n                event.preventDefault()\r\n            }\r\n        }));\r\n        eventsEngine.on(this._$element, \"MSHoldVisual\", (event => {\r\n            event.preventDefault()\r\n        }))\r\n    },\r\n    _getLayoutItems: noop,\r\n    _layoutManagerOptions() {\r\n        return this._themeManager.getOptions(\"adaptiveLayout\")\r\n    },\r\n    _reinit() {\r\n        _setCanvasValues(this._canvas);\r\n        this._reinitAxes();\r\n        this._requestChange([\"DATA_SOURCE\", \"DATA_INIT\", \"CORRECT_AXIS\", \"FULL_RENDER\"])\r\n    },\r\n    _correctAxes: noop,\r\n    _createHtmlStructure() {\r\n        const renderer = this._renderer;\r\n        const {\r\n            root: root\r\n        } = renderer;\r\n        const createConstantLinesGroup = function() {\r\n            return renderer.g().attr({\r\n                class: \"dxc-constant-lines-group\"\r\n            }).linkOn(root, \"constant-lines\")\r\n        };\r\n        this._constantLinesGroup = {\r\n            dispose() {\r\n                this.under.dispose();\r\n                this.above.dispose()\r\n            },\r\n            linkOff() {\r\n                this.under.linkOff();\r\n                this.above.linkOff()\r\n            },\r\n            clear() {\r\n                this.under.linkRemove().clear();\r\n                this.above.linkRemove().clear()\r\n            },\r\n            linkAppend() {\r\n                this.under.linkAppend();\r\n                this.above.linkAppend()\r\n            }\r\n        };\r\n        this._labelsAxesGroup = renderer.g().attr({\r\n            class: \"dxc-elements-axes-group\"\r\n        });\r\n        const appendLabelsAxesGroup = () => {\r\n            this._labelsAxesGroup.linkOn(root, \"elements\")\r\n        };\r\n        this._backgroundRect = renderer.rect().attr({\r\n            fill: \"gray\",\r\n            opacity: 1e-4\r\n        }).append(root);\r\n        this._panesBackgroundGroup = renderer.g().attr({\r\n            class: \"dxc-background\"\r\n        }).append(root);\r\n        this._stripsGroup = renderer.g().attr({\r\n            class: \"dxc-strips-group\"\r\n        }).linkOn(root, \"strips\");\r\n        this._gridGroup = renderer.g().attr({\r\n            class: \"dxc-grids-group\"\r\n        }).linkOn(root, \"grids\");\r\n        this._panesBorderGroup = renderer.g().attr({\r\n            class: \"dxc-border\"\r\n        }).linkOn(root, \"border\");\r\n        this._axesGroup = renderer.g().attr({\r\n            class: \"dxc-axes-group\"\r\n        }).linkOn(root, \"axes\");\r\n        this._executeAppendBeforeSeries(appendLabelsAxesGroup);\r\n        this._stripLabelAxesGroup = renderer.g().attr({\r\n            class: \"dxc-strips-labels-group\"\r\n        }).linkOn(root, \"strips-labels\");\r\n        this._constantLinesGroup.under = createConstantLinesGroup();\r\n        this._seriesGroup = renderer.g().attr({\r\n            class: \"dxc-series-group\"\r\n        }).linkOn(root, \"series\");\r\n        this._executeAppendAfterSeries(appendLabelsAxesGroup);\r\n        this._constantLinesGroup.above = createConstantLinesGroup();\r\n        this._scaleBreaksGroup = renderer.g().attr({\r\n            class: \"dxc-scale-breaks\"\r\n        }).linkOn(root, \"scale-breaks\");\r\n        this._labelsGroup = renderer.g().attr({\r\n            class: \"dxc-labels-group\"\r\n        }).linkOn(root, \"labels\");\r\n        this._crosshairCursorGroup = renderer.g().attr({\r\n            class: \"dxc-crosshair-cursor\"\r\n        }).linkOn(root, \"crosshair\");\r\n        this._legendGroup = renderer.g().attr({\r\n            class: \"dxc-legend\",\r\n            \"clip-path\": this._getCanvasClipRectID()\r\n        }).linkOn(root, \"legend\").linkAppend(root).enableLinks();\r\n        this._scrollBarGroup = renderer.g().attr({\r\n            class: \"dxc-scroll-bar\"\r\n        }).linkOn(root, \"scroll-bar\")\r\n    },\r\n    _executeAppendBeforeSeries() {},\r\n    _executeAppendAfterSeries() {},\r\n    _disposeObjectsInArray(propName, fieldNames) {\r\n        (this[propName] || []).forEach((item => {\r\n            if (fieldNames && item) {\r\n                fieldNames.forEach((field => {\r\n                    var _item$field;\r\n                    null === (_item$field = item[field]) || void 0 === _item$field || _item$field.dispose()\r\n                }))\r\n            } else {\r\n                null === item || void 0 === item || item.dispose()\r\n            }\r\n        }));\r\n        this[propName] = null\r\n    },\r\n    _disposeCore() {\r\n        const disposeObject = propName => {\r\n            if (this[propName]) {\r\n                this[propName].dispose();\r\n                this[propName] = null\r\n            }\r\n        };\r\n        const unlinkGroup = name => {\r\n            this[name].linkOff()\r\n        };\r\n        const disposeObjectsInArray = this._disposeObjectsInArray;\r\n        this._renderer.stopAllAnimations();\r\n        disposeObjectsInArray.call(this, \"series\");\r\n        disposeObject(\"_tracker\");\r\n        disposeObject(\"_crosshair\");\r\n        this.layoutManager = this._userOptions = this._canvas = this._groupsData = null;\r\n        unlinkGroup(\"_stripsGroup\");\r\n        unlinkGroup(\"_gridGroup\");\r\n        unlinkGroup(\"_axesGroup\");\r\n        unlinkGroup(\"_constantLinesGroup\");\r\n        unlinkGroup(\"_stripLabelAxesGroup\");\r\n        unlinkGroup(\"_panesBorderGroup\");\r\n        unlinkGroup(\"_seriesGroup\");\r\n        unlinkGroup(\"_labelsGroup\");\r\n        unlinkGroup(\"_crosshairCursorGroup\");\r\n        unlinkGroup(\"_legendGroup\");\r\n        unlinkGroup(\"_scrollBarGroup\");\r\n        unlinkGroup(\"_scaleBreaksGroup\");\r\n        disposeObject(\"_canvasClipRect\");\r\n        disposeObject(\"_panesBackgroundGroup\");\r\n        disposeObject(\"_backgroundRect\");\r\n        disposeObject(\"_stripsGroup\");\r\n        disposeObject(\"_gridGroup\");\r\n        disposeObject(\"_axesGroup\");\r\n        disposeObject(\"_constantLinesGroup\");\r\n        disposeObject(\"_stripLabelAxesGroup\");\r\n        disposeObject(\"_panesBorderGroup\");\r\n        disposeObject(\"_seriesGroup\");\r\n        disposeObject(\"_labelsGroup\");\r\n        disposeObject(\"_crosshairCursorGroup\");\r\n        disposeObject(\"_legendGroup\");\r\n        disposeObject(\"_scrollBarGroup\");\r\n        disposeObject(\"_scaleBreaksGroup\")\r\n    },\r\n    _getAnimationOptions() {\r\n        return this._themeManager.getOptions(\"animation\")\r\n    },\r\n    _getDefaultSize: () => ({\r\n        width: 400,\r\n        height: 400\r\n    }),\r\n    _getOption(name) {\r\n        return this._themeManager.getOptions(name)\r\n    },\r\n    _applySize(rect) {\r\n        this._rect = rect.slice();\r\n        if (!this._changes.has(\"FULL_RENDER\")) {\r\n            this._processRefreshData(\"_resize\")\r\n        }\r\n    },\r\n    _resize() {\r\n        this._doRender(this.__renderOptions || {\r\n            animate: false,\r\n            isResize: true\r\n        })\r\n    },\r\n    _trackerType: \"ChartTracker\",\r\n    _createTracker() {\r\n        this._tracker = new trackerModule[this._trackerType]({\r\n            seriesGroup: this._seriesGroup,\r\n            renderer: this._renderer,\r\n            tooltip: this._tooltip,\r\n            legend: this._legend,\r\n            eventTrigger: this._eventTrigger\r\n        })\r\n    },\r\n    _getTrackerSettings() {\r\n        return extend({\r\n            chart: this\r\n        }, this._getSelectionModes())\r\n    },\r\n    _getSelectionModes() {\r\n        const themeManager = this._themeManager;\r\n        return {\r\n            seriesSelectionMode: themeManager.getOptions(\"seriesSelectionMode\"),\r\n            pointSelectionMode: themeManager.getOptions(\"pointSelectionMode\")\r\n        }\r\n    },\r\n    _updateTracker(trackerCanvases) {\r\n        this._tracker.update(this._getTrackerSettings());\r\n        this._tracker.setCanvases({\r\n            left: 0,\r\n            right: this._canvas.width,\r\n            top: 0,\r\n            bottom: this._canvas.height\r\n        }, trackerCanvases)\r\n    },\r\n    _createCanvasFromRect(rect) {\r\n        const currentCanvas = this._canvas;\r\n        return _setCanvasValues({\r\n            left: rect[0],\r\n            top: rect[1],\r\n            right: currentCanvas.width - rect[2],\r\n            bottom: currentCanvas.height - rect[3],\r\n            width: currentCanvas.width,\r\n            height: currentCanvas.height\r\n        })\r\n    },\r\n    _doRender(_options) {\r\n        if (0 === this._canvas.width && 0 === this._canvas.height) {\r\n            return\r\n        }\r\n        this._resetIsReady();\r\n        const drawOptions = this._prepareDrawOptions(_options);\r\n        const {\r\n            recreateCanvas: recreateCanvas\r\n        } = drawOptions;\r\n        this._preserveOriginalCanvas();\r\n        if (recreateCanvas) {\r\n            this.__currentCanvas = this._canvas\r\n        } else {\r\n            this._canvas = this.__currentCanvas\r\n        }\r\n        recreateCanvas && this._updateCanvasClipRect(this._canvas);\r\n        this._canvas = this._createCanvasFromRect(this._rect);\r\n        this._renderer.stopAllAnimations(true);\r\n        this._cleanGroups();\r\n        const startTime = new Date;\r\n        this._renderElements(drawOptions);\r\n        this._lastRenderingTime = Number(new Date) - Number(startTime)\r\n    },\r\n    _preserveOriginalCanvas() {\r\n        this.__originalCanvas = this._canvas;\r\n        this._canvas = extend({}, this._canvas)\r\n    },\r\n    _layoutAxes: noop,\r\n    _renderElements(drawOptions) {\r\n        const preparedOptions = this._prepareToRender(drawOptions);\r\n        const isRotated = this._isRotated();\r\n        const isLegendInside = this._isLegendInside();\r\n        const trackerCanvases = [];\r\n        extend({}, this._canvas);\r\n        let argBusinessRange;\r\n        let zoomMinArg;\r\n        let zoomMaxArg;\r\n        this._renderer.lock();\r\n        if (drawOptions.drawLegend && this._legend) {\r\n            this._legendGroup.linkAppend()\r\n        }\r\n        this.layoutManager.setOptions(this._layoutManagerOptions());\r\n        const layoutTargets = this._getLayoutTargets();\r\n        this._layoutAxes((needSpace => {\r\n            const axisDrawOptions = needSpace ? extend({}, drawOptions, {\r\n                animate: false,\r\n                recreateCanvas: true\r\n            }) : drawOptions;\r\n            const canvas = this._renderAxes(axisDrawOptions, preparedOptions);\r\n            this._shrinkAxes(needSpace, canvas)\r\n        }));\r\n        this._applyClipRects(preparedOptions);\r\n        this._appendSeriesGroups();\r\n        this._createCrosshairCursor();\r\n        layoutTargets.forEach((_ref => {\r\n            let {\r\n                canvas: canvas\r\n            } = _ref;\r\n            trackerCanvases.push({\r\n                left: canvas.left,\r\n                right: canvas.width - canvas.right,\r\n                top: canvas.top,\r\n                bottom: canvas.height - canvas.bottom\r\n            })\r\n        }));\r\n        if (this._scrollBar) {\r\n            argBusinessRange = this._argumentAxes[0].getTranslator().getBusinessRange();\r\n            if (\"discrete\" === argBusinessRange.axisType && argBusinessRange.categories && argBusinessRange.categories.length <= 1 || \"discrete\" !== argBusinessRange.axisType && argBusinessRange.min === argBusinessRange.max) {\r\n                zoomMinArg = zoomMaxArg = void 0\r\n            } else {\r\n                zoomMinArg = argBusinessRange.minVisible;\r\n                zoomMaxArg = argBusinessRange.maxVisible\r\n            }\r\n            this._scrollBar.init(argBusinessRange, !this._argumentAxes[0].getOptions().valueMarginsEnabled).setPosition(zoomMinArg, zoomMaxArg)\r\n        }\r\n        this._updateTracker(trackerCanvases);\r\n        this._updateLegendPosition(drawOptions, isLegendInside);\r\n        this._applyPointMarkersAutoHiding();\r\n        this._renderSeries(drawOptions, isRotated, isLegendInside);\r\n        this._renderGraphicObjects();\r\n        this._renderer.unlock()\r\n    },\r\n    _updateLegendPosition: noop,\r\n    _createCrosshairCursor: noop,\r\n    _appendSeriesGroups() {\r\n        this._seriesGroup.linkAppend();\r\n        this._labelsGroup.linkAppend();\r\n        this._appendAdditionalSeriesGroups()\r\n    },\r\n    _renderSeries(drawOptions, isRotated, isLegendInside) {\r\n        this._calculateSeriesLayout(drawOptions, isRotated);\r\n        this._renderSeriesElements(drawOptions, isLegendInside)\r\n    },\r\n    _calculateSeriesLayout(drawOptions, isRotated) {\r\n        drawOptions.hideLayoutLabels = this.layoutManager.needMoreSpaceForPanesCanvas(this._getLayoutTargets(), isRotated) && !this._themeManager.getOptions(\"adaptiveLayout\").keepLabels;\r\n        this._updateSeriesDimensions(drawOptions)\r\n    },\r\n    _getArgFilter: () => () => true,\r\n    _getValFilter: () => () => true,\r\n    _getPointsToAnimation(series) {\r\n        const argViewPortFilter = this._getArgFilter();\r\n        return series.map((s => {\r\n            const valViewPortFilter = this._getValFilter(s);\r\n            return s.getPoints().filter((p => p.getOptions().visible && argViewPortFilter(p.argument) && (valViewPortFilter(p.getMinValue(true)) || valViewPortFilter(p.getMaxValue(true))))).length\r\n        }))\r\n    },\r\n    _renderSeriesElements(drawOptions, isLegendInside) {\r\n        const {\r\n            series: series\r\n        } = this;\r\n        const resolveLabelOverlapping = this._themeManager.getOptions(\"resolveLabelOverlapping\");\r\n        const pointsToAnimation = this._getPointsToAnimation(series);\r\n        series.forEach(((singleSeries, index) => {\r\n            this._applyExtraSettings(singleSeries, drawOptions);\r\n            const animationEnabled = drawOptions.animate && pointsToAnimation[index] <= drawOptions.animationPointsLimit && this._renderer.animationEnabled();\r\n            singleSeries.draw(animationEnabled, drawOptions.hideLayoutLabels, this._getLegendCallBack(singleSeries))\r\n        }));\r\n        if (\"none\" === resolveLabelOverlapping) {\r\n            this._adjustSeriesLabels(false)\r\n        } else {\r\n            this._locateLabels(resolveLabelOverlapping)\r\n        }\r\n        this._renderTrackers(isLegendInside);\r\n        this._tracker.repairTooltip();\r\n        this._renderExtraElements();\r\n        this._clearCanvas();\r\n        this._seriesElementsDrawn = true\r\n    },\r\n    _changesApplied() {\r\n        if (this._seriesElementsDrawn) {\r\n            this._seriesElementsDrawn = false;\r\n            this._drawn();\r\n            this._renderCompleteHandler()\r\n        }\r\n    },\r\n    _locateLabels(resolveLabelOverlapping) {\r\n        this._resolveLabelOverlapping(resolveLabelOverlapping)\r\n    },\r\n    _renderExtraElements() {},\r\n    _clearCanvas() {\r\n        this._canvas = this.__originalCanvas\r\n    },\r\n    _resolveLabelOverlapping(resolveLabelOverlapping) {\r\n        let func;\r\n        switch (resolveLabelOverlapping) {\r\n            case \"stack\":\r\n                func = this._resolveLabelOverlappingStack;\r\n                break;\r\n            case \"hide\":\r\n                func = this._resolveLabelOverlappingHide;\r\n                break;\r\n            case \"shift\":\r\n                func = this._resolveLabelOverlappingShift\r\n        }\r\n        return isFunction(func) && func.call(this)\r\n    },\r\n    _getVisibleSeries() {\r\n        return grep(this.getAllSeries(), (series => series.isVisible()))\r\n    },\r\n    _resolveLabelOverlappingHide() {\r\n        const labels = [];\r\n        let currentLabel;\r\n        let nextLabel;\r\n        let currentLabelRect;\r\n        let nextLabelRect;\r\n        let i;\r\n        let j;\r\n        let points;\r\n        const series = this._getVisibleSeries();\r\n        for (i = 0; i < series.length; i++) {\r\n            points = series[i].getVisiblePoints();\r\n            for (j = 0; j < points.length; j++) {\r\n                labels.push.apply(labels, points[j].getLabels())\r\n            }\r\n        }\r\n        for (i = 0; i < labels.length; i++) {\r\n            currentLabel = labels[i];\r\n            if (!currentLabel.isVisible()) {\r\n                continue\r\n            }\r\n            currentLabelRect = currentLabel.getBoundingRect();\r\n            for (j = i + 1; j < labels.length; j++) {\r\n                nextLabel = labels[j];\r\n                nextLabelRect = nextLabel.getBoundingRect();\r\n                if (checkOverlapping(currentLabelRect, nextLabelRect)) {\r\n                    nextLabel.draw(false)\r\n                }\r\n            }\r\n        }\r\n    },\r\n    _cleanGroups() {\r\n        this._stripsGroup.linkRemove().clear();\r\n        this._gridGroup.linkRemove().clear();\r\n        this._axesGroup.linkRemove().clear();\r\n        this._constantLinesGroup.clear();\r\n        this._stripLabelAxesGroup.linkRemove().clear();\r\n        this._labelsGroup.linkRemove().clear();\r\n        this._crosshairCursorGroup.linkRemove().clear();\r\n        this._scaleBreaksGroup.linkRemove().clear()\r\n    },\r\n    _allowLegendInsidePosition: () => false,\r\n    _createLegend() {\r\n        const legendSettings = getLegendSettings(this._legendDataField);\r\n        this._legend = new Legend({\r\n            renderer: this._renderer,\r\n            widget: this,\r\n            group: this._legendGroup,\r\n            backgroundClass: \"dxc-border\",\r\n            itemGroupClass: \"dxc-item\",\r\n            titleGroupClass: \"dxc-title\",\r\n            textField: legendSettings.textField,\r\n            getFormatObject: legendSettings.getFormatObject,\r\n            allowInsidePosition: this._allowLegendInsidePosition()\r\n        });\r\n        this._updateLegend();\r\n        this._layout.add(this._legend)\r\n    },\r\n    _updateLegend() {\r\n        const themeManager = this._themeManager;\r\n        const legendOptions = themeManager.getOptions(\"legend\");\r\n        const legendData = this._getLegendData();\r\n        legendOptions.containerBackgroundColor = themeManager.getOptions(\"containerBackgroundColor\");\r\n        legendOptions._incidentOccurred = this._incidentOccurred;\r\n        this._legend.update(legendData, legendOptions, themeManager.theme(\"legend\").title);\r\n        this._change([\"LAYOUT\"])\r\n    },\r\n    _prepareDrawOptions(drawOptions) {\r\n        const animationOptions = this._getAnimationOptions();\r\n        const options = extend({}, {\r\n            force: false,\r\n            adjustAxes: true,\r\n            drawLegend: true,\r\n            drawTitle: true,\r\n            animate: animationOptions.enabled,\r\n            animationPointsLimit: animationOptions.maxPointCountSupported\r\n        }, drawOptions, this.__renderOptions);\r\n        if (!_isDefined(options.recreateCanvas)) {\r\n            options.recreateCanvas = options.adjustAxes && options.drawLegend && options.drawTitle\r\n        }\r\n        return options\r\n    },\r\n    _processRefreshData(newRefreshAction) {\r\n        const currentRefreshActionPosition = ACTIONS_BY_PRIORITY.indexOf(this._currentRefreshData);\r\n        const newRefreshActionPosition = ACTIONS_BY_PRIORITY.indexOf(newRefreshAction);\r\n        if (!this._currentRefreshData || currentRefreshActionPosition >= 0 && newRefreshActionPosition < currentRefreshActionPosition) {\r\n            this._currentRefreshData = newRefreshAction\r\n        }\r\n        this._requestChange([\"REFRESH\"])\r\n    },\r\n    _getLegendData() {\r\n        return _map(this._getLegendTargets(), (item => {\r\n            const {\r\n                legendData: legendData\r\n            } = item;\r\n            const style = item.getLegendStyles;\r\n            let {\r\n                opacity: opacity\r\n            } = style.normal;\r\n            if (!item.visible) {\r\n                if (!_isDefined(opacity) || opacity > .3) {\r\n                    opacity = .3\r\n                }\r\n                legendData.textOpacity = .3\r\n            }\r\n            const opacityStyle = {\r\n                opacity: opacity\r\n            };\r\n            legendData.states = {\r\n                hover: extend({}, style.hover, opacityStyle),\r\n                selection: extend({}, style.selection, opacityStyle),\r\n                normal: extend({}, style.normal, opacityStyle)\r\n            };\r\n            return legendData\r\n        }))\r\n    },\r\n    _getLegendOptions(item) {\r\n        return {\r\n            legendData: {\r\n                text: item[this._legendItemTextField],\r\n                id: item.index,\r\n                visible: true\r\n            },\r\n            getLegendStyles: item.getLegendStyles(),\r\n            visible: item.isVisible()\r\n        }\r\n    },\r\n    _disposeSeries(seriesIndex) {\r\n        var _this$series;\r\n        if (this.series) {\r\n            if (_isDefined(seriesIndex)) {\r\n                this.series[seriesIndex].dispose();\r\n                this.series.splice(seriesIndex, 1)\r\n            } else {\r\n                this.series.forEach((s => s.dispose()));\r\n                this.series.length = 0\r\n            }\r\n        }\r\n        if (!(null !== (_this$series = this.series) && void 0 !== _this$series && _this$series.length)) {\r\n            this.series = []\r\n        }\r\n    },\r\n    _disposeSeriesFamilies() {\r\n        (this.seriesFamilies || []).forEach((family => {\r\n            family.dispose()\r\n        }));\r\n        this.seriesFamilies = null;\r\n        this._needHandleRenderComplete = true\r\n    },\r\n    _optionChanged(arg) {\r\n        this._themeManager.resetOptions(arg.name);\r\n        this.callBase.apply(this, arguments)\r\n    },\r\n    _applyChanges() {\r\n        this._themeManager.update(this._options.silent());\r\n        this.callBase(...arguments)\r\n    },\r\n    _optionChangesMap: {\r\n        animation: \"ANIMATION\",\r\n        dataSource: \"DATA_SOURCE\",\r\n        palette: \"PALETTE\",\r\n        paletteExtensionMode: \"PALETTE\",\r\n        legend: \"FORCE_DATA_INIT\",\r\n        seriesTemplate: \"FORCE_DATA_INIT\",\r\n        export: \"FORCE_RENDER\",\r\n        valueAxis: \"AXES_AND_PANES\",\r\n        argumentAxis: \"AXES_AND_PANES\",\r\n        commonAxisSettings: \"AXES_AND_PANES\",\r\n        panes: \"AXES_AND_PANES\",\r\n        commonPaneSettings: \"AXES_AND_PANES\",\r\n        defaultPane: \"AXES_AND_PANES\",\r\n        containerBackgroundColor: \"AXES_AND_PANES\",\r\n        rotated: \"ROTATED\",\r\n        autoHidePointMarkers: \"REFRESH_SERIES_REINIT\",\r\n        customizePoint: \"REFRESH_SERIES_REINIT\",\r\n        customizeLabel: \"REFRESH_SERIES_REINIT\",\r\n        scrollBar: \"SCROLL_BAR\"\r\n    },\r\n    _optionChangesOrder: [\"ROTATED\", \"PALETTE\", \"REFRESH_SERIES_REINIT\", \"USE_SPIDER_WEB\", \"AXES_AND_PANES\", \"INIT\", \"REINIT\", \"DATA_SOURCE\", \"REFRESH_SERIES_DATA_INIT\", \"DATA_INIT\", \"FORCE_DATA_INIT\", \"REFRESH_AXES\", \"CORRECT_AXIS\"],\r\n    _customChangesOrder: [\"ANIMATION\", \"REFRESH_SERIES_FAMILIES\", \"FORCE_FIRST_DRAWING\", \"FORCE_DRAWING\", \"FORCE_RENDER\", \"VISUAL_RANGE\", \"SCROLL_BAR\", \"REINIT\", \"REFRESH\", \"FULL_RENDER\"],\r\n    _change_ANIMATION() {\r\n        this._renderer.updateAnimationOptions(this._getAnimationOptions())\r\n    },\r\n    _change_DATA_SOURCE() {\r\n        this._needHandleRenderComplete = true;\r\n        this._updateDataSource()\r\n    },\r\n    _change_PALETTE() {\r\n        this._themeManager.updatePalette();\r\n        this._refreshSeries(\"DATA_INIT\")\r\n    },\r\n    _change_REFRESH_SERIES_DATA_INIT() {\r\n        this._refreshSeries(\"DATA_INIT\")\r\n    },\r\n    _change_DATA_INIT() {\r\n        if ((!this.series || this.needToPopulateSeries) && !this._changes.has(\"FORCE_DATA_INIT\")) {\r\n            this._dataInit()\r\n        }\r\n    },\r\n    _change_FORCE_DATA_INIT() {\r\n        this._dataInit()\r\n    },\r\n    _change_REFRESH_SERIES_FAMILIES() {\r\n        this._processSeriesFamilies();\r\n        this._populateBusinessRange();\r\n        this._processRefreshData(\"_forceRender\")\r\n    },\r\n    _change_FORCE_RENDER() {\r\n        this._processRefreshData(\"_forceRender\")\r\n    },\r\n    _change_AXES_AND_PANES() {\r\n        this._refreshSeries(\"INIT\")\r\n    },\r\n    _change_ROTATED() {\r\n        this._createScrollBar();\r\n        this._refreshSeries(\"INIT\")\r\n    },\r\n    _change_REFRESH_SERIES_REINIT() {\r\n        this._refreshSeries(\"INIT\")\r\n    },\r\n    _change_REFRESH_AXES() {\r\n        _setCanvasValues(this._canvas);\r\n        this._reinitAxes();\r\n        this._requestChange([\"CORRECT_AXIS\", \"FULL_RENDER\"])\r\n    },\r\n    _change_SCROLL_BAR() {\r\n        this._createScrollBar();\r\n        this._processRefreshData(\"_forceRender\")\r\n    },\r\n    _change_REINIT() {\r\n        this._processRefreshData(\"_reinit\")\r\n    },\r\n    _change_FORCE_DRAWING() {\r\n        this._resetComponentsAnimation()\r\n    },\r\n    _change_FORCE_FIRST_DRAWING() {\r\n        this._resetComponentsAnimation(true)\r\n    },\r\n    _resetComponentsAnimation(isFirstDrawing) {\r\n        this.series.forEach((s => {\r\n            s.resetApplyingAnimation(isFirstDrawing)\r\n        }));\r\n        this._resetAxesAnimation(isFirstDrawing)\r\n    },\r\n    _resetAxesAnimation: noop,\r\n    _refreshSeries(actionName) {\r\n        this.needToPopulateSeries = true;\r\n        this._requestChange([actionName])\r\n    },\r\n    _change_CORRECT_AXIS() {\r\n        this._correctAxes()\r\n    },\r\n    _doRefresh() {\r\n        const methodName = this._currentRefreshData;\r\n        if (methodName) {\r\n            this._currentRefreshData = null;\r\n            this._renderer.stopAllAnimations(true);\r\n            this[methodName]()\r\n        }\r\n    },\r\n    _updateCanvasClipRect(canvas) {\r\n        const width = Math.max(canvas.width - canvas.left - canvas.right, 0);\r\n        const height = Math.max(canvas.height - canvas.top - canvas.bottom, 0);\r\n        this._canvasClipRect.attr({\r\n            x: canvas.left,\r\n            y: canvas.top,\r\n            width: width,\r\n            height: height\r\n        });\r\n        this._backgroundRect.attr({\r\n            x: canvas.left,\r\n            y: canvas.top,\r\n            width: width,\r\n            height: height\r\n        })\r\n    },\r\n    _getCanvasClipRectID() {\r\n        return this._canvasClipRect.id\r\n    },\r\n    _dataSourceChangedHandler() {\r\n        if (this._changes.has(\"INIT\")) {\r\n            this._requestChange([\"DATA_INIT\"])\r\n        } else {\r\n            this._requestChange([\"FORCE_DATA_INIT\"])\r\n        }\r\n    },\r\n    _dataInit() {\r\n        this._dataSpecificInit(true)\r\n    },\r\n    _processSingleSeries(singleSeries) {\r\n        singleSeries.createPoints(false)\r\n    },\r\n    _handleSeriesDataUpdated() {\r\n        if (this._getVisibleSeries().some((s => s.useAggregation()))) {\r\n            this._populateMarginOptions()\r\n        }\r\n        this.series.forEach((s => this._processSingleSeries(s)), this)\r\n    },\r\n    _dataSpecificInit(needRedraw) {\r\n        if (!this.series || this.needToPopulateSeries) {\r\n            this.series = this._populateSeries()\r\n        }\r\n        this._repopulateSeries();\r\n        this._seriesPopulatedHandlerCore();\r\n        this._populateBusinessRange();\r\n        this._tracker.updateSeries(this.series, this._changes.has(\"INIT\"));\r\n        this._updateLegend();\r\n        if (needRedraw) {\r\n            this._requestChange([\"FULL_RENDER\"])\r\n        }\r\n    },\r\n    _forceRender() {\r\n        this._doRender({\r\n            force: true\r\n        })\r\n    },\r\n    _repopulateSeries() {\r\n        const themeManager = this._themeManager;\r\n        const data = this._dataSourceItems();\r\n        const dataValidatorOptions = themeManager.getOptions(\"dataPrepareSettings\");\r\n        const seriesTemplate = themeManager.getOptions(\"seriesTemplate\");\r\n        if (seriesTemplate) {\r\n            this._populateSeries(data)\r\n        }\r\n        this._groupSeries();\r\n        const parsedData = validateData(data, this._groupsData, this._incidentOccurred, dataValidatorOptions);\r\n        themeManager.resetPalette();\r\n        this.series.forEach((singleSeries => {\r\n            singleSeries.updateData(parsedData[singleSeries.getArgumentField()])\r\n        }));\r\n        this._handleSeriesDataUpdated()\r\n    },\r\n    _renderCompleteHandler() {\r\n        let allSeriesInited = true;\r\n        if (this._needHandleRenderComplete) {\r\n            this.series.forEach((s => {\r\n                allSeriesInited = allSeriesInited && s.canRenderCompleteHandle()\r\n            }));\r\n            if (allSeriesInited) {\r\n                this._needHandleRenderComplete = false;\r\n                this._eventTrigger(\"done\", {\r\n                    target: this\r\n                })\r\n            }\r\n        }\r\n    },\r\n    _dataIsReady() {\r\n        return _isDefined(this.option(\"dataSource\")) && this._dataIsLoaded()\r\n    },\r\n    _populateSeriesOptions(data) {\r\n        const themeManager = this._themeManager;\r\n        const seriesTemplate = themeManager.getOptions(\"seriesTemplate\");\r\n        const seriesOptions = seriesTemplate ? processSeriesTemplate(seriesTemplate, data || []) : this.option(\"series\");\r\n        const allSeriesOptions = isArray(seriesOptions) ? seriesOptions : seriesOptions ? [seriesOptions] : [];\r\n        const extraOptions = this._getExtraOptions();\r\n        let particularSeriesOptions;\r\n        let seriesTheme;\r\n        const seriesThemes = [];\r\n        const seriesVisibilityChanged = target => {\r\n            this._specialProcessSeries();\r\n            this._populateBusinessRange(target && target.getValueAxis(), true);\r\n            this._renderer.stopAllAnimations(true);\r\n            this._updateLegend();\r\n            this._requestChange([\"FULL_RENDER\"])\r\n        };\r\n        for (let i = 0; i < allSeriesOptions.length; i++) {\r\n            particularSeriesOptions = extend(true, {}, allSeriesOptions[i], extraOptions);\r\n            if (!_isDefined(particularSeriesOptions.name) || \"\" === particularSeriesOptions.name) {\r\n                particularSeriesOptions.name = `Series ${(i+1).toString()}`\r\n            }\r\n            particularSeriesOptions.rotated = this._isRotated();\r\n            particularSeriesOptions.customizePoint = themeManager.getOptions(\"customizePoint\");\r\n            particularSeriesOptions.customizeLabel = themeManager.getOptions(\"customizeLabel\");\r\n            particularSeriesOptions.visibilityChanged = seriesVisibilityChanged;\r\n            particularSeriesOptions.incidentOccurred = this._incidentOccurred;\r\n            seriesTheme = themeManager.getOptions(\"series\", particularSeriesOptions, allSeriesOptions.length);\r\n            if (this._checkPaneName(seriesTheme)) {\r\n                seriesThemes.push(seriesTheme)\r\n            }\r\n        }\r\n        return seriesThemes\r\n    },\r\n    _populateSeries(data) {\r\n        var _this$series3;\r\n        const seriesBasis = [];\r\n        const incidentOccurred = this._incidentOccurred;\r\n        const seriesThemes = this._populateSeriesOptions(data);\r\n        let particularSeries;\r\n        let disposeSeriesFamilies = false;\r\n        this.needToPopulateSeries = false;\r\n        seriesThemes.forEach((theme => {\r\n            var _this$series2;\r\n            const curSeries = null === (_this$series2 = this.series) || void 0 === _this$series2 ? void 0 : _this$series2.find((s => s.name === theme.name && !seriesBasis.map((sb => sb.series)).includes(s)));\r\n            if (curSeries && curSeries.type === theme.type) {\r\n                seriesBasis.push({\r\n                    series: curSeries,\r\n                    options: theme\r\n                })\r\n            } else {\r\n                seriesBasis.push({\r\n                    options: theme\r\n                });\r\n                disposeSeriesFamilies = true\r\n            }\r\n        }));\r\n        0 !== (null === (_this$series3 = this.series) || void 0 === _this$series3 ? void 0 : _this$series3.length) && this._tracker.clearHover();\r\n        _reverseEach(this.series, ((index, series) => {\r\n            if (!seriesBasis.some((s => series === s.series))) {\r\n                this._disposeSeries(index);\r\n                disposeSeriesFamilies = true\r\n            }\r\n        }));\r\n        !disposeSeriesFamilies && (disposeSeriesFamilies = seriesBasis.some((sb => sb.series.name !== seriesThemes[sb.series.index].name)));\r\n        this.series = [];\r\n        disposeSeriesFamilies && this._disposeSeriesFamilies();\r\n        this._themeManager.resetPalette();\r\n        const eventPipe = data => {\r\n            this.series.forEach((currentSeries => {\r\n                currentSeries.notify(data)\r\n            }))\r\n        };\r\n        seriesBasis.forEach((basis => {\r\n            var _this$_argumentAxes;\r\n            const seriesTheme = basis.options;\r\n            const argumentAxis = (null === (_this$_argumentAxes = this._argumentAxes) || void 0 === _this$_argumentAxes ? void 0 : _this$_argumentAxes.filter((a => a.pane === seriesTheme.pane))[0]) ?? this.getArgumentAxis();\r\n            const renderSettings = {\r\n                commonSeriesModes: this._getSelectionModes(),\r\n                argumentAxis: argumentAxis,\r\n                valueAxis: this._getValueAxis(seriesTheme.pane, seriesTheme.axis)\r\n            };\r\n            if (basis.series) {\r\n                particularSeries = basis.series;\r\n                particularSeries.updateOptions(seriesTheme, renderSettings)\r\n            } else {\r\n                particularSeries = new Series(extend({\r\n                    renderer: this._renderer,\r\n                    seriesGroup: this._seriesGroup,\r\n                    labelsGroup: this._labelsGroup,\r\n                    eventTrigger: this._eventTrigger,\r\n                    eventPipe: eventPipe,\r\n                    incidentOccurred: incidentOccurred\r\n                }, renderSettings), seriesTheme)\r\n            }\r\n            if (!particularSeries.isUpdated) {\r\n                incidentOccurred(\"E2101\", [seriesTheme.type])\r\n            } else {\r\n                particularSeries.index = this.series.length;\r\n                this.series.push(particularSeries)\r\n            }\r\n        }));\r\n        return this.series\r\n    },\r\n    getStackedPoints(point) {\r\n        const stackName = point.series.getStackName();\r\n        return this._getVisibleSeries().reduce(((stackPoints, series) => {\r\n            if (!_isDefined(series.getStackName()) || !_isDefined(stackName) || stackName === series.getStackName()) {\r\n                stackPoints = stackPoints.concat(series.getPointsByArg(point.argument))\r\n            }\r\n            return stackPoints\r\n        }), [])\r\n    },\r\n    getAllSeries: function() {\r\n        return (this.series || []).slice()\r\n    },\r\n    getSeriesByName: function(name) {\r\n        const found = (this.series || []).find((singleSeries => singleSeries.name === name));\r\n        return found || null\r\n    },\r\n    getSeriesByPos: function(pos) {\r\n        return (this.series || [])[pos]\r\n    },\r\n    clearSelection: function() {\r\n        this._tracker.clearSelection()\r\n    },\r\n    hideTooltip() {\r\n        this._tracker._hideTooltip()\r\n    },\r\n    clearHover() {\r\n        this._tracker.clearHover()\r\n    },\r\n    render(renderOptions) {\r\n        this.__renderOptions = renderOptions;\r\n        this.__forceRender = renderOptions && renderOptions.force;\r\n        this.callBase.apply(this, arguments);\r\n        this.__renderOptions = this.__forceRender = null;\r\n        return this\r\n    },\r\n    refresh() {\r\n        this._disposeSeries();\r\n        this._disposeSeriesFamilies();\r\n        this._requestChange([\"CONTAINER_SIZE\", \"REFRESH_SERIES_REINIT\"])\r\n    },\r\n    _getMinSize() {\r\n        const adaptiveLayout = this._layoutManagerOptions();\r\n        return [adaptiveLayout.width, adaptiveLayout.height]\r\n    },\r\n    _change_REFRESH() {\r\n        if (!this._changes.has(\"INIT\")) {\r\n            this._doRefresh()\r\n        } else {\r\n            this._currentRefreshData = null\r\n        }\r\n    },\r\n    _change_FULL_RENDER() {\r\n        this._forceRender()\r\n    },\r\n    _change_INIT() {\r\n        this._reinit()\r\n    },\r\n    _stopCurrentHandling() {\r\n        if (this._disposed) {\r\n            return\r\n        }\r\n        this._tracker.stopCurrentHandling()\r\n    }\r\n});\r\nREFRESH_SERIES_DATA_INIT_ACTION_OPTIONS.forEach((name => {\r\n    BaseChart.prototype._optionChangesMap[name] = \"REFRESH_SERIES_DATA_INIT\"\r\n}));\r\nFORCE_RENDER_REFRESH_ACTION_OPTIONS.forEach((name => {\r\n    BaseChart.prototype._optionChangesMap[name] = \"FORCE_RENDER\"\r\n}));\r\nREFRESH_SERIES_FAMILIES_ACTION_OPTIONS.forEach((name => {\r\n    BaseChart.prototype._optionChangesMap[name] = \"REFRESH_SERIES_FAMILIES\"\r\n}));\r\nBaseChart.addPlugin(exportPlugin);\r\nBaseChart.addPlugin(titlePlugin);\r\nBaseChart.addPlugin(dataSourcePlugin);\r\nBaseChart.addPlugin(tooltipPlugin);\r\nBaseChart.addPlugin(loadingIndicatorPlugin);\r\nconst {\r\n    _change_TITLE: _change_TITLE\r\n} = BaseChart.prototype;\r\nBaseChart.prototype._change_TITLE = function() {\r\n    _change_TITLE.apply(this, arguments);\r\n    this._change([\"FORCE_RENDER\"])\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AACA;AAAA;AAIA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAGA;AACA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAGA;AAKA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;AACJ,MAAM,wBAAwB;AAC9B,MAAM,oCAAoC;AAC1C,MAAM,2BAA2B;AACjC,MAAM,8BAA8B;AACpC,MAAM,wBAAwB;AAC9B,MAAM,sBAAsB;IAAC;IAAW;IAAqB;IAAa;IAAgB;CAAU;AACpG,MAAM,kBAAkB;AACxB,MAAM,0CAA0C;IAAC;IAAU;IAAwB;IAAuB;IAAuB;IAAsB;IAAwB;CAA2B;AAC1M,MAAM,yCAAyC;IAAC;IAAiB;IAAiB;IAAmB;IAAiB;IAAqB;CAAmB;AAC9J,MAAM,sCAAsC;IAAC;IAAkB;IAAa;IAA2B;IAAgB;CAAiB;AACxI,MAAM,OAAO;AAEb,SAAS,wBAAwB,aAAa,EAAE,UAAU;IACtD,MAAM,aAAa,WAAW,GAAG,GAAG,WAAW,KAAK;IACpD,IAAI,OAAO;IACX,cAAc,OAAO,CAAE,CAAA;QACnB,QAAQ,aAAa,eAAe,GAAG,KAAK;IAChD;IACA,MAAO,aAAa,KAAM;QACtB,QAAQ,sBAAsB;IAClC;AACJ;AAEA,SAAS,sBAAsB,aAAa;IACxC,MAAM,iBAAiB,cAAc,MAAM,CAAE,CAAC,MAAM,cAAc;QAC9D,IAAI,CAAC,cAAc;YACf,OAAO;QACX;QACA,MAAM,QAAQ,aAAa,KAAK;QAChC,OAAO,QAAQ,KAAK,KAAK,GAAG;YACxB,OAAO;YACP,cAAc;YACd,OAAO;QACX,IAAI;IACR,GAAI;QACA,cAAc,KAAK;QACnB,OAAO,IAAI;QACX,OAAO,KAAK;IAChB;IACA,eAAe,YAAY,CAAC,SAAS,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC;IAChD,MAAM,EACF,OAAO,KAAK,EACf,GAAG,eAAe,YAAY,CAAC,eAAe;IAC/C,aAAa,CAAC,eAAe,KAAK,CAAC,GAAG;IACtC,OAAO;AACX;AAEA,SAAS,kBAAkB,aAAa;IACpC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,UAAU;IACd,IAAK,IAAI,GAAG,UAAU,cAAc,MAAM,GAAG,GAAG,IAAI,SAAS,IAAK;QAC9D,IAAK,IAAI,IAAI,GAAG,UAAU,cAAc,MAAM,EAAE,IAAI,SAAS,IAAK;YAC9D,IAAI,MAAM,KAAK,uBAAuB,aAAa,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,EAAE,OAAO;gBAC7E,UAAU;gBACV;YACJ;QACJ;QACA,IAAI,SAAS;YACT;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,sCAAsC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa;IAC/F,IAAI,gBAAgB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,IAAM;IAC3F,MAAM,gBAAgB,EAAE;IACxB,MAAM,aAAa;QACf,OAAO,YAAY,OAAO,IAAI,GAAG,OAAO,GAAG;QAC3C,KAAK,YAAY,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,MAAM,GAAG,OAAO,MAAM;IAChF;IACA,IAAI,mBAAmB;IACvB,IAAI;IACJ,OAAO,OAAO,CAAE,CAAA;QACZ,IAAI,CAAC,GAAG;YACJ;QACJ;QACA,mBAAmB,oBAAoB,EAAE,MAAM,CAAC,eAAe,MAAM,EAAE,MAAM,CAAC,mBAAmB;QACjG,EAAE,SAAS,GAAG,OAAO,CAAE,CAAA;YACnB,IAAI,EAAE,SAAS,IAAI;gBACf,cAAc,IAAI,CAAC,IAAI,8LAAA,CAAA,eAAY,CAAC,GAAG,WAAW;YACtD;QACJ;IACJ;IACA,IAAI,kBAAkB;QAClB,IAAI,OAAO,CAAC,aAAa,OAAO,aAAa;YACzC,cAAc,OAAO;QACzB;QACA,oBAAoB,aAAa,gBAAgB,yBAAyB;IAC9E,OAAO;QACH,MAAM,mBAAmB,cAAc,KAAK;QAC5C,oBAAoB,cAAc,IAAI,CAAE,CAAC,GAAG,IAAM,cAAc,GAAG,MAAM,EAAE,kBAAkB,KAAK,EAAE,kBAAkB,MAAM,iBAAiB,OAAO,CAAC,KAAK,iBAAiB,OAAO,CAAC;IACvL;IACA,IAAI,CAAC,kBAAkB,oBAAoB;QACvC,OAAO;IACX;IACA,wBAAwB,mBAAmB;IAC3C,qBAAqB;IACrB,kBAAkB,OAAO;IACzB,iBAAiB,mBAAmB;IACpC,OAAO;AACX;AAEA,SAAS,uBAAuB,YAAY,EAAE,aAAa,EAAE,UAAU;IACnE,IAAI,CAAC,gBAAgB,CAAC,eAAe;QACjC;IACJ;IACA,MAAM,YAAY,aAAa,eAAe;IAC9C,MAAM,aAAa,cAAc,eAAe;IAChD,MAAM,sBAAsB,aAAa,UAAU,aAAa,IAAI,WAAW,aAAa,IAAI,UAAU,WAAW,GAAG,WAAW,aAAa,IAAI,WAAW,aAAa,IAAI,UAAU,aAAa,IAAI,WAAW,WAAW,GAAG,UAAU,aAAa,GAAG;IAC9P,OAAO,UAAU,GAAG,GAAG,WAAW,KAAK,IAAI;AAC/C;AAEA,SAAS,yBAAyB,aAAa;IAC3C,MAAM,wBAAwB,EAAE;IAChC,MAAM,wBAAwB,EAAE;IAChC,cAAc,OAAO,CAAE,CAAA;QACnB,IAAI,MAAM,KAAK,KAAK,GAAG;YACnB,sBAAsB,IAAI,CAAC;QAC/B,OAAO;YACH,sBAAsB,OAAO,CAAC;QAClC;IACJ;IACA,OAAO,sBAAsB,MAAM,CAAC;AACxC;AAEA,SAAS,qBAAqB,aAAa;IACvC,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,GAAG,GAAG,KAAK,EAAG;QAClD,MAAM,sBAAsB,QAAQ,aAAa,CAAC,EAAE;QACpD,IAAI,uBAAuB,qBAAqB,aAAa,CAAC,IAAI,EAAE,GAAG;YACnE,oBAAoB,OAAO,CAAC,aAAa,CAAC,IAAI,EAAE;YAChD,aAAa,CAAC,IAAI,EAAE,GAAG;YACvB,OAAO;QACX,OAAO;YACH,OAAO,aAAa,CAAC,IAAI,EAAE,IAAI;QACnC;IACJ;AACJ;AAEA,SAAS,mBAAmB,YAAY,EAAE,MAAM;IAC5C,OAAO,aAAa,eAAe,GAAG,GAAG,GAAG,OAAO,GAAG;AAC1D;AAEA,SAAS,iBAAiB,aAAa,EAAE,MAAM;IAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;QAC9C,MAAM,sBAAsB,aAAa,CAAC,EAAE;QAC5C,IAAI,kBAAkB;QACtB,IAAI,SAAS,uBAAuB,mBAAmB,qBAAqB,SAAS;YACjF,MAAM,cAAc,oBAAoB,eAAe;YACvD,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;gBAClD,MAAM,mBAAmB,aAAa,CAAC,EAAE;gBACzC,IAAI,kBAAkB;oBAClB,MAAM,WAAW,iBAAiB,eAAe;oBACjD,IAAI,SAAS,GAAG,GAAG,YAAY,KAAK,GAAG,CAAC,YAAY,GAAG,GAAG,OAAO,GAAG,GAAG;wBACnE,iBAAiB,OAAO,CAAC;wBACzB,kBAAkB;wBAClB;oBACJ;gBACJ;YACJ;QACJ;QACA,IAAI,iBAAiB;YACjB,SAAS,uBAAuB,KAAK,MAAM,uBAAuB,oBAAoB,uBAAuB,CAAC;QAClH;IACJ;AACJ;AAEA,SAAS,gBAAgB,IAAI;IACzB,OAAO;QACH,WAAW,GAAG,KAAK,IAAI,CAAC;QACxB,YAAY,GAAG,KAAK,KAAK,CAAC;QAC1B,YAAY,GAAG,KAAK,KAAK,CAAC;IAC9B;AACJ;AAEA,SAAS,kBAAkB,eAAe;IACtC,MAAM,qBAAqB,gBAAgB;IAC3C,OAAO;QACH,iBAAgB,IAAI;YAChB,MAAM,MAAM,CAAC;YACb,GAAG,CAAC,mBAAmB,UAAU,CAAC,GAAG,KAAK,EAAE;YAC5C,GAAG,CAAC,mBAAmB,UAAU,CAAC,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC,IAAI;YAC5D,GAAG,CAAC,mBAAmB,SAAS,CAAC,GAAG,KAAK,IAAI;YAC7C,OAAO;QACX;QACA,WAAW,mBAAmB,SAAS;IAC3C;AACJ;AAEA,SAAS,iBAAiB,SAAS,EAAE,UAAU;IAC3C,OAAO,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,UAAU,CAAC,GAAG,UAAU,KAAK,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,GAAG,WAAW,KAAK,KAAK,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,IAAI,WAAW,CAAC,IAAI,UAAU,CAAC,GAAG,UAAU,MAAM,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,IAAI,UAAU,CAAC,IAAI,WAAW,CAAC,GAAG,WAAW,MAAM;AAC9U;AACO,MAAM,cAAc;IACvB,uCAAuC;AAC3C;AACO,MAAM,YAAY,kLAAA,CAAA,UAAU,CAAC,OAAO,CAAC;IACxC,YAAY;QACR,eAAe;YACX,MAAM;QACV;QACA,cAAc;YACV,MAAM;QACV;QACA,qBAAqB;YACjB,MAAM;QACV;QACA,eAAe;YACX,MAAM;QACV;QACA,0BAA0B;YACtB,MAAM;QACV;QACA,yBAAyB;YACrB,MAAM;QACV;QACA,sBAAsB;YAClB,MAAM;QACV;QACA,qBAAqB;YACjB,MAAM;QACV;QACA,QAAQ;YACJ,MAAM;YACN,gBAAgB;gBACZ,mBAAmB;oBAAC;iBAAW;YACnC;QACJ;QACA,aAAa;YACT,MAAM;QACV;QACA,WAAW;YACP,MAAM;QACV;IACJ;IACA,aAAa;QAAC,CAAC,OAAO,EAAE,MAAM;QAAE,CAAC,aAAa,EAAE,MAAM;QAAE,CAAC,sBAAsB,EAAE,MAAM;QAAE,CAAC,2BAA2B,EAAE,MAAM;KAAC;IAC9H,kBAAkB;IAClB,YAAY;IACZ,iBAAiB;QAAC;KAAO;IACzB,wBAAwB;QAAC;KAAwB;IACjD;QACI,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QAC/C,aAAa,OAAO,GAAG,IAAI,CAAC,MAAM;QAClC,OAAO;IACX;IACA;QACI,MAAM,cAAc,IAAI,CAAC,MAAM;QAC/B,MAAM,eAAe,IAAI,6KAAA,CAAA,eAAY,CAAC,IAAI,CAAC,uBAAuB;QAClE,aAAa,QAAQ,CAAC,YAAY,KAAK,EAAE,YAAY,UAAU;QAC/D,OAAO;IACX;IACA;QACI,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ;QAC9C,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,yBAAyB,GAAG;QACjC,IAAI,CAAC,aAAa,GAAG,IAAI,8KAAA,CAAA,gBAAa;QACtC,IAAI,CAAC,gBAAgB;QACrB,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,eAAgB,CAAA;YAC5C,IAAI,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,UAAU,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;gBAC9C,MAAM,cAAc;YACxB;QACJ;QACA,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,gBAAiB,CAAA;YAC7C,MAAM,cAAc;QACxB;IACJ;IACA,iBAAiB,+KAAA,CAAA,OAAI;IACrB;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;IACzC;IACA;QACI,CAAA,GAAA,yJAAA,CAAA,kBAAgB,AAAD,EAAE,IAAI,CAAC,OAAO;QAC7B,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC;YAAC;YAAe;YAAa;YAAgB;SAAc;IACnF;IACA,cAAc,+KAAA,CAAA,OAAI;IAClB;QACI,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,MAAM,2BAA2B;YAC7B,OAAO,SAAS,CAAC,GAAG,IAAI,CAAC;gBACrB,OAAO;YACX,GAAG,MAAM,CAAC,MAAM;QACpB;QACA,IAAI,CAAC,mBAAmB,GAAG;YACvB;gBACI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAClB,IAAI,CAAC,KAAK,CAAC,OAAO;YACtB;YACA;gBACI,IAAI,CAAC,KAAK,CAAC,OAAO;gBAClB,IAAI,CAAC,KAAK,CAAC,OAAO;YACtB;YACA;gBACI,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK;gBAC7B,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG,KAAK;YACjC;YACA;gBACI,IAAI,CAAC,KAAK,CAAC,UAAU;gBACrB,IAAI,CAAC,KAAK,CAAC,UAAU;YACzB;QACJ;QACA,IAAI,CAAC,gBAAgB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACtC,OAAO;QACX;QACA,MAAM,wBAAwB;YAC1B,IAAI,CAAC,gBAAgB,CAAC,MAAM,CAAC,MAAM;QACvC;QACA,IAAI,CAAC,eAAe,GAAG,SAAS,IAAI,GAAG,IAAI,CAAC;YACxC,MAAM;YACN,SAAS;QACb,GAAG,MAAM,CAAC;QACV,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAC3C,OAAO;QACX,GAAG,MAAM,CAAC;QACV,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAClC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAChC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACvC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAChC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,0BAA0B,CAAC;QAChC,IAAI,CAAC,oBAAoB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAC1C,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG;QACjC,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAClC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,yBAAyB,CAAC;QAC/B,IAAI,CAAC,mBAAmB,CAAC,KAAK,GAAG;QACjC,IAAI,CAAC,iBAAiB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACvC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAClC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,qBAAqB,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAC3C,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;QAChB,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YAClC,OAAO;YACP,aAAa,IAAI,CAAC,oBAAoB;QAC1C,GAAG,MAAM,CAAC,MAAM,UAAU,UAAU,CAAC,MAAM,WAAW;QACtD,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;YACrC,OAAO;QACX,GAAG,MAAM,CAAC,MAAM;IACpB;IACA,+BAA8B;IAC9B,8BAA6B;IAC7B,wBAAuB,QAAQ,EAAE,UAAU;QACvC,CAAC,IAAI,CAAC,SAAS,IAAI,EAAE,EAAE,OAAO,CAAE,CAAA;YAC5B,IAAI,cAAc,MAAM;gBACpB,WAAW,OAAO,CAAE,CAAA;oBAChB,IAAI;oBACJ,SAAS,CAAC,cAAc,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,eAAe,YAAY,OAAO;gBACzF;YACJ,OAAO;gBACH,SAAS,QAAQ,KAAK,MAAM,QAAQ,KAAK,OAAO;YACpD;QACJ;QACA,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;QACI,MAAM,gBAAgB,CAAA;YAClB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,IAAI,CAAC,SAAS,CAAC,OAAO;gBACtB,IAAI,CAAC,SAAS,GAAG;YACrB;QACJ;QACA,MAAM,cAAc,CAAA;YAChB,IAAI,CAAC,KAAK,CAAC,OAAO;QACtB;QACA,MAAM,wBAAwB,IAAI,CAAC,sBAAsB;QACzD,IAAI,CAAC,SAAS,CAAC,iBAAiB;QAChC,sBAAsB,IAAI,CAAC,IAAI,EAAE;QACjC,cAAc;QACd,cAAc;QACd,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG;QAC3E,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,YAAY;QACZ,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;QACd,cAAc;IAClB;IACA;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;IACzC;IACA,iBAAiB,IAAM,CAAC;YACpB,OAAO;YACP,QAAQ;QACZ,CAAC;IACD,YAAW,IAAI;QACX,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;IACzC;IACA,YAAW,IAAI;QACX,IAAI,CAAC,KAAK,GAAG,KAAK,KAAK;QACvB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB;YACnC,IAAI,CAAC,mBAAmB,CAAC;QAC7B;IACJ;IACA;QACI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,eAAe,IAAI;YACnC,SAAS;YACT,UAAU;QACd;IACJ;IACA,cAAc;IACd;QACI,IAAI,CAAC,QAAQ,GAAG,IAAI,uKAAa,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;YACjD,aAAa,IAAI,CAAC,YAAY;YAC9B,UAAU,IAAI,CAAC,SAAS;YACxB,SAAS,IAAI,CAAC,QAAQ;YACtB,QAAQ,IAAI,CAAC,OAAO;YACpB,cAAc,IAAI,CAAC,aAAa;QACpC;IACJ;IACA;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YACV,OAAO,IAAI;QACf,GAAG,IAAI,CAAC,kBAAkB;IAC9B;IACA;QACI,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,OAAO;YACH,qBAAqB,aAAa,UAAU,CAAC;YAC7C,oBAAoB,aAAa,UAAU,CAAC;QAChD;IACJ;IACA,gBAAe,eAAe;QAC1B,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,mBAAmB;QAC7C,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;YACtB,MAAM;YACN,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK;YACzB,KAAK;YACL,QAAQ,IAAI,CAAC,OAAO,CAAC,MAAM;QAC/B,GAAG;IACP;IACA,uBAAsB,IAAI;QACtB,MAAM,gBAAgB,IAAI,CAAC,OAAO;QAClC,OAAO,CAAA,GAAA,yJAAA,CAAA,kBAAgB,AAAD,EAAE;YACpB,MAAM,IAAI,CAAC,EAAE;YACb,KAAK,IAAI,CAAC,EAAE;YACZ,OAAO,cAAc,KAAK,GAAG,IAAI,CAAC,EAAE;YACpC,QAAQ,cAAc,MAAM,GAAG,IAAI,CAAC,EAAE;YACtC,OAAO,cAAc,KAAK;YAC1B,QAAQ,cAAc,MAAM;QAChC;IACJ;IACA,WAAU,QAAQ;QACd,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,IAAI,CAAC,OAAO,CAAC,MAAM,EAAE;YACvD;QACJ;QACA,IAAI,CAAC,aAAa;QAClB,MAAM,cAAc,IAAI,CAAC,mBAAmB,CAAC;QAC7C,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG;QACJ,IAAI,CAAC,uBAAuB;QAC5B,IAAI,gBAAgB;YAChB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,OAAO;QACvC,OAAO;YACH,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,eAAe;QACvC;QACA,kBAAkB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO;QACzD,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,KAAK;QACpD,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QACjC,IAAI,CAAC,YAAY;QACjB,MAAM,YAAY,IAAI;QACtB,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,kBAAkB,GAAG,OAAO,IAAI,QAAQ,OAAO;IACxD;IACA;QACI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,OAAO;QACpC,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO;IAC1C;IACA,aAAa,+KAAA,CAAA,OAAI;IACjB,iBAAgB,WAAW;QACvB,MAAM,kBAAkB,IAAI,CAAC,gBAAgB,CAAC;QAC9C,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,kBAAkB,EAAE;QAC1B,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,OAAO;QACvB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,SAAS,CAAC,IAAI;QACnB,IAAI,YAAY,UAAU,IAAI,IAAI,CAAC,OAAO,EAAE;YACxC,IAAI,CAAC,YAAY,CAAC,UAAU;QAChC;QACA,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB;QACxD,MAAM,gBAAgB,IAAI,CAAC,iBAAiB;QAC5C,IAAI,CAAC,WAAW,CAAE,CAAA;YACd,MAAM,kBAAkB,YAAY,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,aAAa;gBACxD,SAAS;gBACT,gBAAgB;YACpB,KAAK;YACL,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC,iBAAiB;YACjD,IAAI,CAAC,WAAW,CAAC,WAAW;QAChC;QACA,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,sBAAsB;QAC3B,cAAc,OAAO,CAAE,CAAA;YACnB,IAAI,EACA,QAAQ,MAAM,EACjB,GAAG;YACJ,gBAAgB,IAAI,CAAC;gBACjB,MAAM,OAAO,IAAI;gBACjB,OAAO,OAAO,KAAK,GAAG,OAAO,KAAK;gBAClC,KAAK,OAAO,GAAG;gBACf,QAAQ,OAAO,MAAM,GAAG,OAAO,MAAM;YACzC;QACJ;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,mBAAmB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa,GAAG,gBAAgB;YACzE,IAAI,eAAe,iBAAiB,QAAQ,IAAI,iBAAiB,UAAU,IAAI,iBAAiB,UAAU,CAAC,MAAM,IAAI,KAAK,eAAe,iBAAiB,QAAQ,IAAI,iBAAiB,GAAG,KAAK,iBAAiB,GAAG,EAAE;gBACjN,aAAa,aAAa,KAAK;YACnC,OAAO;gBACH,aAAa,iBAAiB,UAAU;gBACxC,aAAa,iBAAiB,UAAU;YAC5C;YACA,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU,GAAG,mBAAmB,EAAE,WAAW,CAAC,YAAY;QAC5H;QACA,IAAI,CAAC,cAAc,CAAC;QACpB,IAAI,CAAC,qBAAqB,CAAC,aAAa;QACxC,IAAI,CAAC,4BAA4B;QACjC,IAAI,CAAC,aAAa,CAAC,aAAa,WAAW;QAC3C,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,SAAS,CAAC,MAAM;IACzB;IACA,uBAAuB,+KAAA,CAAA,OAAI;IAC3B,wBAAwB,+KAAA,CAAA,OAAI;IAC5B;QACI,IAAI,CAAC,YAAY,CAAC,UAAU;QAC5B,IAAI,CAAC,YAAY,CAAC,UAAU;QAC5B,IAAI,CAAC,6BAA6B;IACtC;IACA,eAAc,WAAW,EAAE,SAAS,EAAE,cAAc;QAChD,IAAI,CAAC,sBAAsB,CAAC,aAAa;QACzC,IAAI,CAAC,qBAAqB,CAAC,aAAa;IAC5C;IACA,wBAAuB,WAAW,EAAE,SAAS;QACzC,YAAY,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,IAAI,cAAc,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,kBAAkB,UAAU;QACjL,IAAI,CAAC,uBAAuB,CAAC;IACjC;IACA,eAAe,IAAM,IAAM;IAC3B,eAAe,IAAM,IAAM;IAC3B,uBAAsB,MAAM;QACxB,MAAM,oBAAoB,IAAI,CAAC,aAAa;QAC5C,OAAO,OAAO,GAAG,CAAE,CAAA;YACf,MAAM,oBAAoB,IAAI,CAAC,aAAa,CAAC;YAC7C,OAAO,EAAE,SAAS,GAAG,MAAM,CAAE,CAAA,IAAK,EAAE,UAAU,GAAG,OAAO,IAAI,kBAAkB,EAAE,QAAQ,KAAK,CAAC,kBAAkB,EAAE,WAAW,CAAC,UAAU,kBAAkB,EAAE,WAAW,CAAC,MAAM,GAAI,MAAM;QAC5L;IACJ;IACA,uBAAsB,WAAW,EAAE,cAAc;QAC7C,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG,IAAI;QACR,MAAM,0BAA0B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAC9D,MAAM,oBAAoB,IAAI,CAAC,qBAAqB,CAAC;QACrD,OAAO,OAAO,CAAE,CAAC,cAAc;YAC3B,IAAI,CAAC,mBAAmB,CAAC,cAAc;YACvC,MAAM,mBAAmB,YAAY,OAAO,IAAI,iBAAiB,CAAC,MAAM,IAAI,YAAY,oBAAoB,IAAI,IAAI,CAAC,SAAS,CAAC,gBAAgB;YAC/I,aAAa,IAAI,CAAC,kBAAkB,YAAY,gBAAgB,EAAE,IAAI,CAAC,kBAAkB,CAAC;QAC9F;QACA,IAAI,WAAW,yBAAyB;YACpC,IAAI,CAAC,mBAAmB,CAAC;QAC7B,OAAO;YACH,IAAI,CAAC,aAAa,CAAC;QACvB;QACA,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,QAAQ,CAAC,aAAa;QAC3B,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,oBAAoB,GAAG;IAChC;IACA;QACI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,GAAG;YAC5B,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,sBAAsB;QAC/B;IACJ;IACA,eAAc,uBAAuB;QACjC,IAAI,CAAC,wBAAwB,CAAC;IAClC;IACA,yBAAwB;IACxB;QACI,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB;IACxC;IACA,0BAAyB,uBAAuB;QAC5C,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,OAAO,IAAI,CAAC,6BAA6B;gBACzC;YACJ,KAAK;gBACD,OAAO,IAAI,CAAC,4BAA4B;gBACxC;YACJ,KAAK;gBACD,OAAO,IAAI,CAAC,6BAA6B;QACjD;QACA,OAAO,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,SAAS,KAAK,IAAI,CAAC,IAAI;IAC7C;IACA;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,YAAY,IAAK,CAAA,SAAU,OAAO,SAAS;IAChE;IACA;QACI,MAAM,SAAS,EAAE;QACjB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,SAAS,IAAI,CAAC,iBAAiB;QACrC,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YAChC,SAAS,MAAM,CAAC,EAAE,CAAC,gBAAgB;YACnC,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBAChC,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,MAAM,CAAC,EAAE,CAAC,SAAS;YACjD;QACJ;QACA,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YAChC,eAAe,MAAM,CAAC,EAAE;YACxB,IAAI,CAAC,aAAa,SAAS,IAAI;gBAC3B;YACJ;YACA,mBAAmB,aAAa,eAAe;YAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;gBACpC,YAAY,MAAM,CAAC,EAAE;gBACrB,gBAAgB,UAAU,eAAe;gBACzC,IAAI,iBAAiB,kBAAkB,gBAAgB;oBACnD,UAAU,IAAI,CAAC;gBACnB;YACJ;QACJ;IACJ;IACA;QACI,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,KAAK;QACpC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,KAAK;QAClC,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,KAAK;QAClC,IAAI,CAAC,mBAAmB,CAAC,KAAK;QAC9B,IAAI,CAAC,oBAAoB,CAAC,UAAU,GAAG,KAAK;QAC5C,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,KAAK;QACpC,IAAI,CAAC,qBAAqB,CAAC,UAAU,GAAG,KAAK;QAC7C,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,KAAK;IAC7C;IACA,4BAA4B,IAAM;IAClC;QACI,MAAM,iBAAiB,kBAAkB,IAAI,CAAC,gBAAgB;QAC9D,IAAI,CAAC,OAAO,GAAG,IAAI,gKAAA,CAAA,SAAM,CAAC;YACtB,UAAU,IAAI,CAAC,SAAS;YACxB,QAAQ,IAAI;YACZ,OAAO,IAAI,CAAC,YAAY;YACxB,iBAAiB;YACjB,gBAAgB;YAChB,iBAAiB;YACjB,WAAW,eAAe,SAAS;YACnC,iBAAiB,eAAe,eAAe;YAC/C,qBAAqB,IAAI,CAAC,0BAA0B;QACxD;QACA,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO;IACjC;IACA;QACI,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,gBAAgB,aAAa,UAAU,CAAC;QAC9C,MAAM,aAAa,IAAI,CAAC,cAAc;QACtC,cAAc,wBAAwB,GAAG,aAAa,UAAU,CAAC;QACjE,cAAc,iBAAiB,GAAG,IAAI,CAAC,iBAAiB;QACxD,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,YAAY,eAAe,aAAa,KAAK,CAAC,UAAU,KAAK;QACjF,IAAI,CAAC,OAAO,CAAC;YAAC;SAAS;IAC3B;IACA,qBAAoB,WAAW;QAC3B,MAAM,mBAAmB,IAAI,CAAC,oBAAoB;QAClD,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;YACvB,OAAO;YACP,YAAY;YACZ,YAAY;YACZ,WAAW;YACX,SAAS,iBAAiB,OAAO;YACjC,sBAAsB,iBAAiB,sBAAsB;QACjE,GAAG,aAAa,IAAI,CAAC,eAAe;QACpC,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,QAAQ,cAAc,GAAG;YACrC,QAAQ,cAAc,GAAG,QAAQ,UAAU,IAAI,QAAQ,UAAU,IAAI,QAAQ,SAAS;QAC1F;QACA,OAAO;IACX;IACA,qBAAoB,gBAAgB;QAChC,MAAM,+BAA+B,oBAAoB,OAAO,CAAC,IAAI,CAAC,mBAAmB;QACzF,MAAM,2BAA2B,oBAAoB,OAAO,CAAC;QAC7D,IAAI,CAAC,IAAI,CAAC,mBAAmB,IAAI,gCAAgC,KAAK,2BAA2B,8BAA8B;YAC3H,IAAI,CAAC,mBAAmB,GAAG;QAC/B;QACA,IAAI,CAAC,cAAc,CAAC;YAAC;SAAU;IACnC;IACA;QACI,OAAO,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,IAAI,CAAC,iBAAiB,IAAK,CAAA;YACnC,MAAM,EACF,YAAY,UAAU,EACzB,GAAG;YACJ,MAAM,QAAQ,KAAK,eAAe;YAClC,IAAI,EACA,SAAS,OAAO,EACnB,GAAG,MAAM,MAAM;YAChB,IAAI,CAAC,KAAK,OAAO,EAAE;gBACf,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,YAAY,UAAU,IAAI;oBACtC,UAAU;gBACd;gBACA,WAAW,WAAW,GAAG;YAC7B;YACA,MAAM,eAAe;gBACjB,SAAS;YACb;YACA,WAAW,MAAM,GAAG;gBAChB,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,MAAM,KAAK,EAAE;gBAC/B,WAAW,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,MAAM,SAAS,EAAE;gBACvC,QAAQ,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,MAAM,MAAM,EAAE;YACrC;YACA,OAAO;QACX;IACJ;IACA,mBAAkB,IAAI;QAClB,OAAO;YACH,YAAY;gBACR,MAAM,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC;gBACrC,IAAI,KAAK,KAAK;gBACd,SAAS;YACb;YACA,iBAAiB,KAAK,eAAe;YACrC,SAAS,KAAK,SAAS;QAC3B;IACJ;IACA,gBAAe,WAAW;QACtB,IAAI;QACJ,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,cAAc;gBACzB,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO;gBAChC,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,aAAa;YACpC,OAAO;gBACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA,IAAK,EAAE,OAAO;gBACnC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;YACzB;QACJ;QACA,IAAI,CAAC,CAAC,SAAS,CAAC,eAAe,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,gBAAgB,aAAa,MAAM,GAAG;YAC5F,IAAI,CAAC,MAAM,GAAG,EAAE;QACpB;IACJ;IACA;QACI,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,OAAO,CAAE,CAAA;YACjC,OAAO,OAAO;QAClB;QACA,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,yBAAyB,GAAG;IACrC;IACA,gBAAe,GAAG;QACd,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,IAAI,IAAI;QACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;IAC9B;IACA;QACI,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;QAC9C,IAAI,CAAC,QAAQ,IAAI;IACrB;IACA,mBAAmB;QACf,WAAW;QACX,YAAY;QACZ,SAAS;QACT,sBAAsB;QACtB,QAAQ;QACR,gBAAgB;QAChB,QAAQ;QACR,WAAW;QACX,cAAc;QACd,oBAAoB;QACpB,OAAO;QACP,oBAAoB;QACpB,aAAa;QACb,0BAA0B;QAC1B,SAAS;QACT,sBAAsB;QACtB,gBAAgB;QAChB,gBAAgB;QAChB,WAAW;IACf;IACA,qBAAqB;QAAC;QAAW;QAAW;QAAyB;QAAkB;QAAkB;QAAQ;QAAU;QAAe;QAA4B;QAAa;QAAmB;QAAgB;KAAe;IACrO,qBAAqB;QAAC;QAAa;QAA2B;QAAuB;QAAiB;QAAgB;QAAgB;QAAc;QAAU;QAAW;KAAc;IACvL;QACI,IAAI,CAAC,SAAS,CAAC,sBAAsB,CAAC,IAAI,CAAC,oBAAoB;IACnE;IACA;QACI,IAAI,CAAC,yBAAyB,GAAG;QACjC,IAAI,CAAC,iBAAiB;IAC1B;IACA;QACI,IAAI,CAAC,aAAa,CAAC,aAAa;QAChC,IAAI,CAAC,cAAc,CAAC;IACxB;IACA;QACI,IAAI,CAAC,cAAc,CAAC;IACxB;IACA;QACI,IAAI,CAAC,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,oBAAoB;YACtF,IAAI,CAAC,SAAS;QAClB;IACJ;IACA;QACI,IAAI,CAAC,SAAS;IAClB;IACA;QACI,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA;QACI,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA;QACI,IAAI,CAAC,cAAc,CAAC;IACxB;IACA;QACI,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,cAAc,CAAC;IACxB;IACA;QACI,IAAI,CAAC,cAAc,CAAC;IACxB;IACA;QACI,CAAA,GAAA,yJAAA,CAAA,kBAAgB,AAAD,EAAE,IAAI,CAAC,OAAO;QAC7B,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC;YAAC;YAAgB;SAAc;IACvD;IACA;QACI,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA;QACI,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA;QACI,IAAI,CAAC,yBAAyB;IAClC;IACA;QACI,IAAI,CAAC,yBAAyB,CAAC;IACnC;IACA,2BAA0B,cAAc;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,EAAE,sBAAsB,CAAC;QAC7B;QACA,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA,qBAAqB,+KAAA,CAAA,OAAI;IACzB,gBAAe,UAAU;QACrB,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,cAAc,CAAC;YAAC;SAAW;IACpC;IACA;QACI,IAAI,CAAC,YAAY;IACrB;IACA;QACI,MAAM,aAAa,IAAI,CAAC,mBAAmB;QAC3C,IAAI,YAAY;YACZ,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;YACjC,IAAI,CAAC,WAAW;QACpB;IACJ;IACA,uBAAsB,MAAM;QACxB,MAAM,QAAQ,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK,EAAE;QAClE,MAAM,SAAS,KAAK,GAAG,CAAC,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM,EAAE;QACpE,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtB,GAAG,OAAO,IAAI;YACd,GAAG,OAAO,GAAG;YACb,OAAO;YACP,QAAQ;QACZ;QACA,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtB,GAAG,OAAO,IAAI;YACd,GAAG,OAAO,GAAG;YACb,OAAO;YACP,QAAQ;QACZ;IACJ;IACA;QACI,OAAO,IAAI,CAAC,eAAe,CAAC,EAAE;IAClC;IACA;QACI,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS;YAC3B,IAAI,CAAC,cAAc,CAAC;gBAAC;aAAY;QACrC,OAAO;YACH,IAAI,CAAC,cAAc,CAAC;gBAAC;aAAkB;QAC3C;IACJ;IACA;QACI,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IACA,sBAAqB,YAAY;QAC7B,aAAa,YAAY,CAAC;IAC9B;IACA;QACI,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAE,CAAA,IAAK,EAAE,cAAc,KAAM;YAC1D,IAAI,CAAC,sBAAsB;QAC/B;QACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA,IAAK,IAAI,CAAC,oBAAoB,CAAC,IAAK,IAAI;IACjE;IACA,mBAAkB,UAAU;QACxB,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3C,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,eAAe;QACtC;QACA,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,2BAA2B;QAChC,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;QAC1D,IAAI,CAAC,aAAa;QAClB,IAAI,YAAY;YACZ,IAAI,CAAC,cAAc,CAAC;gBAAC;aAAc;QACvC;IACJ;IACA;QACI,IAAI,CAAC,SAAS,CAAC;YACX,OAAO;QACX;IACJ;IACA;QACI,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,OAAO,IAAI,CAAC,gBAAgB;QAClC,MAAM,uBAAuB,aAAa,UAAU,CAAC;QACrD,MAAM,iBAAiB,aAAa,UAAU,CAAC;QAC/C,IAAI,gBAAgB;YAChB,IAAI,CAAC,eAAe,CAAC;QACzB;QACA,IAAI,CAAC,YAAY;QACjB,MAAM,aAAa,CAAA,GAAA,wKAAA,CAAA,eAAY,AAAD,EAAE,MAAM,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,iBAAiB,EAAE;QAChF,aAAa,YAAY;QACzB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,aAAa,UAAU,CAAC,UAAU,CAAC,aAAa,gBAAgB,GAAG;QACvE;QACA,IAAI,CAAC,wBAAwB;IACjC;IACA;QACI,IAAI,kBAAkB;QACtB,IAAI,IAAI,CAAC,yBAAyB,EAAE;YAChC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;gBACjB,kBAAkB,mBAAmB,EAAE,uBAAuB;YAClE;YACA,IAAI,iBAAiB;gBACjB,IAAI,CAAC,yBAAyB,GAAG;gBACjC,IAAI,CAAC,aAAa,CAAC,QAAQ;oBACvB,QAAQ,IAAI;gBAChB;YACJ;QACJ;IACJ;IACA;QACI,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,kBAAkB,IAAI,CAAC,aAAa;IACtE;IACA,wBAAuB,IAAI;QACvB,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,iBAAiB,aAAa,UAAU,CAAC;QAC/C,MAAM,gBAAgB,iBAAiB,CAAA,GAAA,yJAAA,CAAA,wBAAqB,AAAD,EAAE,gBAAgB,QAAQ,EAAE,IAAI,IAAI,CAAC,MAAM,CAAC;QACvG,MAAM,mBAAmB,QAAQ,iBAAiB,gBAAgB,gBAAgB;YAAC;SAAc,GAAG,EAAE;QACtG,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,IAAI;QACJ,IAAI;QACJ,MAAM,eAAe,EAAE;QACvB,MAAM,0BAA0B,CAAA;YAC5B,IAAI,CAAC,qBAAqB;YAC1B,IAAI,CAAC,sBAAsB,CAAC,UAAU,OAAO,YAAY,IAAI;YAC7D,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;YACjC,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,cAAc,CAAC;gBAAC;aAAc;QACvC;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAC9C,0BAA0B,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,gBAAgB,CAAC,EAAE,EAAE;YAChE,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,wBAAwB,IAAI,KAAK,OAAO,wBAAwB,IAAI,EAAE;gBAClF,wBAAwB,IAAI,GAAG,CAAC,OAAO,EAAE,CAAC,IAAE,CAAC,EAAE,QAAQ,IAAI;YAC/D;YACA,wBAAwB,OAAO,GAAG,IAAI,CAAC,UAAU;YACjD,wBAAwB,cAAc,GAAG,aAAa,UAAU,CAAC;YACjE,wBAAwB,cAAc,GAAG,aAAa,UAAU,CAAC;YACjE,wBAAwB,iBAAiB,GAAG;YAC5C,wBAAwB,gBAAgB,GAAG,IAAI,CAAC,iBAAiB;YACjE,cAAc,aAAa,UAAU,CAAC,UAAU,yBAAyB,iBAAiB,MAAM;YAChG,IAAI,IAAI,CAAC,cAAc,CAAC,cAAc;gBAClC,aAAa,IAAI,CAAC;YACtB;QACJ;QACA,OAAO;IACX;IACA,iBAAgB,IAAI;QAChB,IAAI;QACJ,MAAM,cAAc,EAAE;QACtB,MAAM,mBAAmB,IAAI,CAAC,iBAAiB;QAC/C,MAAM,eAAe,IAAI,CAAC,sBAAsB,CAAC;QACjD,IAAI;QACJ,IAAI,wBAAwB;QAC5B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,aAAa,OAAO,CAAE,CAAA;YAClB,IAAI;YACJ,MAAM,YAAY,SAAS,CAAC,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,gBAAgB,KAAK,IAAI,cAAc,IAAI,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,YAAY,GAAG,CAAE,CAAA,KAAM,GAAG,MAAM,EAAG,QAAQ,CAAC;YAC/L,IAAI,aAAa,UAAU,IAAI,KAAK,MAAM,IAAI,EAAE;gBAC5C,YAAY,IAAI,CAAC;oBACb,QAAQ;oBACR,SAAS;gBACb;YACJ,OAAO;gBACH,YAAY,IAAI,CAAC;oBACb,SAAS;gBACb;gBACA,wBAAwB;YAC5B;QACJ;QACA,MAAM,CAAC,SAAS,CAAC,gBAAgB,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,gBAAgB,KAAK,IAAI,cAAc,MAAM,KAAK,IAAI,CAAC,QAAQ,CAAC,UAAU;QACtI,CAAA,GAAA,iLAAA,CAAA,cAAY,AAAD,EAAE,IAAI,CAAC,MAAM,EAAG,CAAC,OAAO;YAC/B,IAAI,CAAC,YAAY,IAAI,CAAE,CAAA,IAAK,WAAW,EAAE,MAAM,GAAI;gBAC/C,IAAI,CAAC,cAAc,CAAC;gBACpB,wBAAwB;YAC5B;QACJ;QACA,CAAC,yBAAyB,CAAC,wBAAwB,YAAY,IAAI,CAAE,CAAA,KAAM,GAAG,MAAM,CAAC,IAAI,KAAK,YAAY,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,CAAE;QAClI,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,yBAAyB,IAAI,CAAC,sBAAsB;QACpD,IAAI,CAAC,aAAa,CAAC,YAAY;QAC/B,MAAM,YAAY,CAAA;YACd,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;gBACjB,cAAc,MAAM,CAAC;YACzB;QACJ;QACA,YAAY,OAAO,CAAE,CAAA;YACjB,IAAI;YACJ,MAAM,cAAc,MAAM,OAAO;YACjC,MAAM,eAAe,CAAC,SAAS,CAAC,sBAAsB,IAAI,CAAC,aAAa,KAAK,KAAK,MAAM,sBAAsB,KAAK,IAAI,oBAAoB,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,IAAI,CAAE,CAAC,EAAE,KAAK,IAAI,CAAC,eAAe;YACjN,MAAM,iBAAiB;gBACnB,mBAAmB,IAAI,CAAC,kBAAkB;gBAC1C,cAAc;gBACd,WAAW,IAAI,CAAC,aAAa,CAAC,YAAY,IAAI,EAAE,YAAY,IAAI;YACpE;YACA,IAAI,MAAM,MAAM,EAAE;gBACd,mBAAmB,MAAM,MAAM;gBAC/B,iBAAiB,aAAa,CAAC,aAAa;YAChD,OAAO;gBACH,mBAAmB,IAAI,iKAAA,CAAA,SAAM,CAAC,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;oBACjC,UAAU,IAAI,CAAC,SAAS;oBACxB,aAAa,IAAI,CAAC,YAAY;oBAC9B,aAAa,IAAI,CAAC,YAAY;oBAC9B,cAAc,IAAI,CAAC,aAAa;oBAChC,WAAW;oBACX,kBAAkB;gBACtB,GAAG,iBAAiB;YACxB;YACA,IAAI,CAAC,iBAAiB,SAAS,EAAE;gBAC7B,iBAAiB,SAAS;oBAAC,YAAY,IAAI;iBAAC;YAChD,OAAO;gBACH,iBAAiB,KAAK,GAAG,IAAI,CAAC,MAAM,CAAC,MAAM;gBAC3C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACrB;QACJ;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,kBAAiB,KAAK;QAClB,MAAM,YAAY,MAAM,MAAM,CAAC,YAAY;QAC3C,OAAO,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAE,CAAC,aAAa;YAClD,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,OAAO,YAAY,OAAO,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,cAAc,cAAc,OAAO,YAAY,IAAI;gBACrG,cAAc,YAAY,MAAM,CAAC,OAAO,cAAc,CAAC,MAAM,QAAQ;YACzE;YACA,OAAO;QACX,GAAI,EAAE;IACV;IACA,cAAc;QACV,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE,KAAK;IACpC;IACA,iBAAiB,SAAS,IAAI;QAC1B,MAAM,QAAQ,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE,IAAI,CAAE,CAAA,eAAgB,aAAa,IAAI,KAAK;QAC9E,OAAO,SAAS;IACpB;IACA,gBAAgB,SAAS,GAAG;QACxB,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC,CAAC,IAAI;IACnC;IACA,gBAAgB;QACZ,IAAI,CAAC,QAAQ,CAAC,cAAc;IAChC;IACA;QACI,IAAI,CAAC,QAAQ,CAAC,YAAY;IAC9B;IACA;QACI,IAAI,CAAC,QAAQ,CAAC,UAAU;IAC5B;IACA,QAAO,aAAa;QAChB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,aAAa,GAAG,iBAAiB,cAAc,KAAK;QACzD,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,GAAG;QAC5C,OAAO,IAAI;IACf;IACA;QACI,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,cAAc,CAAC;YAAC;YAAkB;SAAwB;IACnE;IACA;QACI,MAAM,iBAAiB,IAAI,CAAC,qBAAqB;QACjD,OAAO;YAAC,eAAe,KAAK;YAAE,eAAe,MAAM;SAAC;IACxD;IACA;QACI,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS;YAC5B,IAAI,CAAC,UAAU;QACnB,OAAO;YACH,IAAI,CAAC,mBAAmB,GAAG;QAC/B;IACJ;IACA;QACI,IAAI,CAAC,YAAY;IACrB;IACA;QACI,IAAI,CAAC,OAAO;IAChB;IACA;QACI,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB;QACJ;QACA,IAAI,CAAC,QAAQ,CAAC,mBAAmB;IACrC;AACJ;AACA,wCAAwC,OAAO,CAAE,CAAA;IAC7C,UAAU,SAAS,CAAC,iBAAiB,CAAC,KAAK,GAAG;AAClD;AACA,oCAAoC,OAAO,CAAE,CAAA;IACzC,UAAU,SAAS,CAAC,iBAAiB,CAAC,KAAK,GAAG;AAClD;AACA,uCAAuC,OAAO,CAAE,CAAA;IAC5C,UAAU,SAAS,CAAC,iBAAiB,CAAC,KAAK,GAAG;AAClD;AACA,UAAU,SAAS,CAAC,0JAAA,CAAA,SAAY;AAChC,UAAU,SAAS,CAAC,yJAAA,CAAA,SAAW;AAC/B,UAAU,SAAS,CAAC,+JAAA,CAAA,SAAgB;AACpC,UAAU,SAAS,CAAC,2JAAA,CAAA,SAAa;AACjC,UAAU,SAAS,CAAC,qKAAA,CAAA,SAAsB;AAC1C,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,UAAU,SAAS;AACvB,UAAU,SAAS,CAAC,aAAa,GAAG;IAChC,cAAc,KAAK,CAAC,IAAI,EAAE;IAC1B,IAAI,CAAC,OAAO,CAAC;QAAC;KAAe;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6204, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/chart_components/m_advanced_chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/chart_components/m_advanced_chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    noop as _noop\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    extend as _extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    reverseEach as _reverseEach\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isDefined as _isDefined,\r\n    type\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    Axis\r\n} from \"../../../viz/axes/base_axis\";\r\nimport {\r\n    SeriesFamily\r\n} from \"../../../viz/core/series_family\";\r\nimport {\r\n    convertVisualRangeObject,\r\n    map as _map,\r\n    mergeMarginOptions,\r\n    rangesAreEqual,\r\n    setCanvasValues,\r\n    unique\r\n} from \"../../../viz/core/utils\";\r\nimport rangeDataCalculator from \"../../../viz/series/helpers/range_data_calculator\";\r\nimport {\r\n    Range\r\n} from \"../../../viz/translators/range\";\r\nimport {\r\n    areCanvasesDifferent,\r\n    floorCanvasDimensions\r\n} from \"../../../viz/utils\";\r\nimport {\r\n    BaseChart\r\n} from \"./m_base_chart\";\r\nconst {\r\n    isArray: isArray\r\n} = Array;\r\nconst DEFAULT_AXIS_NAME = \"defaultAxisName\";\r\nconst FONT = \"font\";\r\nconst COMMON_AXIS_SETTINGS = \"commonAxisSettings\";\r\nconst DEFAULT_PANE_NAME = \"default\";\r\nconst VISUAL_RANGE = \"VISUAL_RANGE\";\r\n\r\nfunction prepareAxis(axisOptions) {\r\n    if (isArray(axisOptions)) {\r\n        return 0 === axisOptions.length ? [{}] : axisOptions\r\n    }\r\n    return [axisOptions]\r\n}\r\n\r\nfunction processBubbleMargin(marginOptions, bubbleSize) {\r\n    if (marginOptions.processBubbleSize) {\r\n        marginOptions.size = bubbleSize\r\n    }\r\n    return marginOptions\r\n}\r\n\r\nfunction estimateBubbleSize(size, panesCount, maxSize, rotated) {\r\n    const width = rotated ? size.width / panesCount : size.width;\r\n    const height = rotated ? size.height : size.height / panesCount;\r\n    return Math.min(width, height) * maxSize\r\n}\r\n\r\nfunction setAxisVisualRangeByOption(arg, axis, isDirectOption, index) {\r\n    let options;\r\n    let visualRange;\r\n    if (isDirectOption) {\r\n        visualRange = arg.value;\r\n        options = {\r\n            skipEventRising: true\r\n        };\r\n        const wrappedVisualRange = wrapVisualRange(arg.fullName, visualRange);\r\n        if (wrappedVisualRange) {\r\n            options = {\r\n                allowPartialUpdate: true\r\n            };\r\n            visualRange = wrappedVisualRange\r\n        }\r\n    } else {\r\n        visualRange = (_isDefined(index) ? arg.value[index] : arg.value).visualRange\r\n    }\r\n    axis.visualRange(visualRange, options)\r\n}\r\n\r\nfunction getAxisTypes(groupsData, axis, isArgumentAxes) {\r\n    if (isArgumentAxes) {\r\n        return {\r\n            argumentAxisType: groupsData.argumentAxisType,\r\n            argumentType: groupsData.argumentType\r\n        }\r\n    }\r\n    const {\r\n        valueAxisType: valueAxisType,\r\n        valueType: valueType\r\n    } = groupsData.groups.find((g => g.valueAxis === axis));\r\n    return {\r\n        valueAxisType: valueAxisType,\r\n        valueType: valueType\r\n    }\r\n}\r\n\r\nfunction wrapVisualRange(fullName, value) {\r\n    const pathElements = fullName.split(\".\");\r\n    const destElem = pathElements.at(-1);\r\n    if (\"endValue\" === destElem || \"startValue\" === destElem) {\r\n        return {\r\n            [destElem]: value\r\n        }\r\n    }\r\n    return\r\n}\r\nexport const AdvancedChart = BaseChart.inherit({\r\n    _fontFields: [`commonAxisSettings.label.${FONT}`, `commonAxisSettings.title.${FONT}`],\r\n    _partialOptionChangesMap: {\r\n        visualRange: VISUAL_RANGE,\r\n        _customVisualRange: VISUAL_RANGE,\r\n        strips: \"REFRESH_AXES\",\r\n        constantLines: \"REFRESH_AXES\"\r\n    },\r\n    _partialOptionChangesPath: {\r\n        argumentAxis: [\"strips\", \"constantLines\", \"visualRange\", \"_customVisualRange\"],\r\n        valueAxis: [\"strips\", \"constantLines\", \"visualRange\", \"_customVisualRange\"]\r\n    },\r\n    _initCore() {\r\n        this._panesClipRects = {};\r\n        this.callBase()\r\n    },\r\n    _disposeCore() {\r\n        const disposeObjectsInArray = this._disposeObjectsInArray;\r\n        const panesClipRects = this._panesClipRects;\r\n        this.callBase();\r\n        disposeObjectsInArray.call(panesClipRects, \"fixed\");\r\n        disposeObjectsInArray.call(panesClipRects, \"base\");\r\n        disposeObjectsInArray.call(panesClipRects, \"wide\");\r\n        this._panesClipRects = null;\r\n        this._labelsAxesGroup.linkOff();\r\n        this._labelsAxesGroup.dispose();\r\n        this._labelsAxesGroup = null\r\n    },\r\n    _dispose() {\r\n        const disposeObjectsInArray = this._disposeObjectsInArray;\r\n        this.callBase();\r\n        this.panes = null;\r\n        if (this._legend) {\r\n            this._legend.dispose();\r\n            this._legend = null\r\n        }\r\n        disposeObjectsInArray.call(this, \"panesBackground\");\r\n        disposeObjectsInArray.call(this, \"seriesFamilies\");\r\n        this._disposeAxes()\r\n    },\r\n    _createPanes() {\r\n        this._cleanPanesClipRects(\"fixed\");\r\n        this._cleanPanesClipRects(\"base\");\r\n        this._cleanPanesClipRects(\"wide\")\r\n    },\r\n    _cleanPanesClipRects(clipArrayName) {\r\n        const clipArray = this._panesClipRects[clipArrayName];\r\n        (clipArray || []).forEach((clipRect => {\r\n            null === clipRect || void 0 === clipRect || clipRect.dispose()\r\n        }));\r\n        this._panesClipRects[clipArrayName] = []\r\n    },\r\n    _getElementsClipRectID(paneName) {\r\n        const clipShape = this._panesClipRects.fixed[this._getPaneIndex(paneName)];\r\n        return null === clipShape || void 0 === clipShape ? void 0 : clipShape.id\r\n    },\r\n    _getPaneIndex(paneName) {\r\n        const name = paneName || \"default\";\r\n        return this.panes.findIndex((pane => pane.name === name))\r\n    },\r\n    _updateSize(forceUpdateCanvas) {\r\n        this.callBase();\r\n        if (forceUpdateCanvas && areCanvasesDifferent(this.__currentCanvas, this._canvas)) {\r\n            this.__currentCanvas = floorCanvasDimensions(this._canvas)\r\n        }\r\n        setCanvasValues(this._canvas)\r\n    },\r\n    _reinitAxes() {\r\n        this.panes = this._createPanes();\r\n        this._populateAxes();\r\n        this._axesReinitialized = true\r\n    },\r\n    _populateAxes() {\r\n        const {\r\n            panes: panes\r\n        } = this;\r\n        const rotated = this._isRotated();\r\n        const argumentAxesOptions = prepareAxis(this.option(\"argumentAxis\") || {})[0];\r\n        const valueAxisOption = this.option(\"valueAxis\");\r\n        const valueAxesOptions = prepareAxis(valueAxisOption || {});\r\n        let argumentAxesPopulatedOptions = [];\r\n        const valueAxesPopulatedOptions = [];\r\n        const axisNames = [];\r\n        let valueAxesCounter = 0;\r\n        let paneWithNonVirtualAxis;\r\n        const crosshairMargins = this._getCrosshairMargins();\r\n\r\n        function getNextAxisName() {\r\n            const name = \"defaultAxisName\" + String(valueAxesCounter);\r\n            valueAxesCounter += 1;\r\n            return name\r\n        }\r\n        if (rotated) {\r\n            paneWithNonVirtualAxis = \"right\" === argumentAxesOptions.position ? panes[panes.length - 1].name : panes[0].name\r\n        } else {\r\n            paneWithNonVirtualAxis = \"top\" === argumentAxesOptions.position ? panes[0].name : panes[panes.length - 1].name\r\n        }\r\n        argumentAxesPopulatedOptions = _map(panes, (pane => {\r\n            const virtual = pane.name !== paneWithNonVirtualAxis;\r\n            return this._populateAxesOptions(\"argumentAxis\", argumentAxesOptions, {\r\n                pane: pane.name,\r\n                name: null,\r\n                optionPath: \"argumentAxis\",\r\n                crosshairMargin: rotated ? crosshairMargins.x : crosshairMargins.y\r\n            }, rotated, virtual)\r\n        }));\r\n        valueAxesOptions.forEach(((axisOptions, priority) => {\r\n            var _axisOptions$panes;\r\n            let axisPanes = [];\r\n            const {\r\n                name: name\r\n            } = axisOptions;\r\n            if (name && axisNames.includes(name)) {\r\n                this._incidentOccurred(\"E2102\");\r\n                return\r\n            }\r\n            if (name) {\r\n                axisNames.push(name)\r\n            }\r\n            if (axisOptions.pane) {\r\n                axisPanes.push(axisOptions.pane)\r\n            }\r\n            if (null !== (_axisOptions$panes = axisOptions.panes) && void 0 !== _axisOptions$panes && _axisOptions$panes.length) {\r\n                axisPanes = axisPanes.concat(axisOptions.panes.slice(0))\r\n            }\r\n            axisPanes = unique(axisPanes);\r\n            if (!axisPanes.length) {\r\n                axisPanes.push(void 0)\r\n            }\r\n            axisPanes.forEach((pane => {\r\n                const optionPath = isArray(valueAxisOption) ? `valueAxis[${String(priority)}]` : \"valueAxis\";\r\n                valueAxesPopulatedOptions.push(this._populateAxesOptions(\"valueAxis\", axisOptions, {\r\n                    name: name || getNextAxisName(),\r\n                    pane: pane,\r\n                    priority: priority,\r\n                    optionPath: optionPath,\r\n                    crosshairMargin: rotated ? crosshairMargins.y : crosshairMargins.x\r\n                }, rotated))\r\n            }))\r\n        }));\r\n        this._redesignAxes(argumentAxesPopulatedOptions, true, paneWithNonVirtualAxis);\r\n        this._redesignAxes(valueAxesPopulatedOptions, false)\r\n    },\r\n    _redesignAxes(options, isArgumentAxes, paneWithNonVirtualAxis) {\r\n        const axesBasis = [];\r\n        let axes = isArgumentAxes ? this._argumentAxes : this._valueAxes;\r\n        options.forEach((opt => {\r\n            var _axes;\r\n            const curAxes = null === (_axes = axes) || void 0 === _axes ? void 0 : _axes.filter((a => a.name === opt.name && (!_isDefined(opt.pane) && this.panes.some((p => p.name === a.pane)) || a.pane === opt.pane)));\r\n            if (null !== curAxes && void 0 !== curAxes && curAxes.length) {\r\n                curAxes.forEach((axis => {\r\n                    const axisTypes = getAxisTypes(this._groupsData, axis, isArgumentAxes);\r\n                    axis.updateOptions(opt);\r\n                    if (isArgumentAxes) {\r\n                        axis.setTypes(axisTypes.argumentAxisType, axisTypes.argumentType, \"argumentType\")\r\n                    } else {\r\n                        axis.setTypes(axisTypes.valueAxisType, axisTypes.valueType, \"valueType\")\r\n                    }\r\n                    axis.validate();\r\n                    axesBasis.push({\r\n                        axis: axis\r\n                    })\r\n                }))\r\n            } else {\r\n                axesBasis.push({\r\n                    options: opt\r\n                })\r\n            }\r\n        }));\r\n        if (axes) {\r\n            _reverseEach(axes, ((index, axis) => {\r\n                if (!axesBasis.some((basis => basis.axis && basis.axis === axis))) {\r\n                    this._disposeAxis(index, isArgumentAxes)\r\n                }\r\n            }))\r\n        } else if (isArgumentAxes) {\r\n            axes = this._argumentAxes = []\r\n        } else {\r\n            axes = this._valueAxes = []\r\n        }\r\n        axesBasis.forEach((basis => {\r\n            let {\r\n                axis: axis\r\n            } = basis;\r\n            if (basis.axis && isArgumentAxes) {\r\n                basis.axis.isVirtual = basis.axis.pane !== paneWithNonVirtualAxis\r\n            } else if (basis.options) {\r\n                axis = this._createAxis(isArgumentAxes, basis.options, isArgumentAxes ? basis.options.pane !== paneWithNonVirtualAxis : void 0);\r\n                axes.push(axis)\r\n            }\r\n            axis.applyVisualRangeSetter(this._getVisualRangeSetter())\r\n        }))\r\n    },\r\n    _disposeAxis(index, isArgumentAxis) {\r\n        const axes = isArgumentAxis ? this._argumentAxes : this._valueAxes;\r\n        const axis = axes[index];\r\n        if (!axis) {\r\n            return\r\n        }\r\n        axis.dispose();\r\n        axes.splice(index, 1)\r\n    },\r\n    _disposeAxes() {\r\n        const disposeObjectsInArray = this._disposeObjectsInArray;\r\n        disposeObjectsInArray.call(this, \"_argumentAxes\");\r\n        disposeObjectsInArray.call(this, \"_valueAxes\")\r\n    },\r\n    _appendAdditionalSeriesGroups() {\r\n        this._crosshairCursorGroup.linkAppend();\r\n        if (this._scrollBar) {\r\n            this._scrollBarGroup.linkAppend()\r\n        }\r\n    },\r\n    _getLegendTargets() {\r\n        return (this.series || []).map((s => {\r\n            const item = this._getLegendOptions(s);\r\n            item.legendData.series = s;\r\n            if (!s.getOptions().showInLegend) {\r\n                item.legendData.visible = false\r\n            }\r\n            return item\r\n        }))\r\n    },\r\n    _legendItemTextField: \"name\",\r\n    _seriesPopulatedHandlerCore() {\r\n        this._processSeriesFamilies();\r\n        this._processValueAxisFormat()\r\n    },\r\n    _renderTrackers() {\r\n        for (let i = 0; i < this.series.length; i += 1) {\r\n            this.series[i].drawTrackers()\r\n        }\r\n    },\r\n    _specialProcessSeries() {\r\n        this._processSeriesFamilies()\r\n    },\r\n    _processSeriesFamilies() {\r\n        var _this$seriesFamilies;\r\n        const types = [];\r\n        const families = [];\r\n        let paneSeries;\r\n        const themeManager = this._themeManager;\r\n        const negativesAsZeroes = themeManager.getOptions(\"negativesAsZeroes\");\r\n        const negativesAsZeros = themeManager.getOptions(\"negativesAsZeros\");\r\n        const familyOptions = {\r\n            minBubbleSize: themeManager.getOptions(\"minBubbleSize\"),\r\n            maxBubbleSize: themeManager.getOptions(\"maxBubbleSize\"),\r\n            barGroupPadding: themeManager.getOptions(\"barGroupPadding\"),\r\n            barGroupWidth: themeManager.getOptions(\"barGroupWidth\"),\r\n            negativesAsZeroes: _isDefined(negativesAsZeroes) ? negativesAsZeroes : negativesAsZeros\r\n        };\r\n        if (null !== (_this$seriesFamilies = this.seriesFamilies) && void 0 !== _this$seriesFamilies && _this$seriesFamilies.length) {\r\n            this.seriesFamilies.forEach((family => {\r\n                family.updateOptions(familyOptions);\r\n                family.adjustSeriesValues()\r\n            }));\r\n            return\r\n        }\r\n        this.series.forEach((item => {\r\n            if (!types.includes(item.type)) {\r\n                types.push(item.type)\r\n            }\r\n        }));\r\n        this._getLayoutTargets().forEach((pane => {\r\n            paneSeries = this._getSeriesForPane(pane.name);\r\n            types.forEach((type => {\r\n                const family = new SeriesFamily({\r\n                    type: type,\r\n                    pane: pane.name,\r\n                    minBubbleSize: familyOptions.minBubbleSize,\r\n                    maxBubbleSize: familyOptions.maxBubbleSize,\r\n                    barGroupPadding: familyOptions.barGroupPadding,\r\n                    barGroupWidth: familyOptions.barGroupWidth,\r\n                    negativesAsZeroes: familyOptions.negativesAsZeroes,\r\n                    rotated: this._isRotated()\r\n                });\r\n                family.add(paneSeries);\r\n                family.adjustSeriesValues();\r\n                families.push(family)\r\n            }))\r\n        }));\r\n        this.seriesFamilies = families\r\n    },\r\n    _updateSeriesDimensions() {\r\n        const seriesFamilies = this.seriesFamilies || [];\r\n        for (let i = 0; i < seriesFamilies.length; i += 1) {\r\n            const family = seriesFamilies[i];\r\n            family.updateSeriesValues();\r\n            family.adjustSeriesDimensions()\r\n        }\r\n    },\r\n    _getLegendCallBack(series) {\r\n        var _this$_legend;\r\n        return null === (_this$_legend = this._legend) || void 0 === _this$_legend ? void 0 : _this$_legend.getActionCallback(series)\r\n    },\r\n    _appendAxesGroups() {\r\n        this._stripsGroup.linkAppend();\r\n        this._gridGroup.linkAppend();\r\n        this._axesGroup.linkAppend();\r\n        this._labelsAxesGroup.linkAppend();\r\n        this._constantLinesGroup.linkAppend();\r\n        this._stripLabelAxesGroup.linkAppend();\r\n        this._scaleBreaksGroup.linkAppend()\r\n    },\r\n    _populateMarginOptions() {\r\n        const bubbleSize = estimateBubbleSize(this.getSize(), this.panes.length, this._themeManager.getOptions(\"maxBubbleSize\"), this._isRotated());\r\n        let argumentMarginOptions = {};\r\n        this._valueAxes.forEach((valueAxis => {\r\n            const groupSeries = this.series.filter((series => series.getValueAxis() === valueAxis));\r\n            let marginOptions = {};\r\n            groupSeries.forEach((series => {\r\n                if (series.isVisible()) {\r\n                    const seriesMarginOptions = processBubbleMargin(series.getMarginOptions(), bubbleSize);\r\n                    marginOptions = mergeMarginOptions(marginOptions, seriesMarginOptions);\r\n                    argumentMarginOptions = mergeMarginOptions(argumentMarginOptions, seriesMarginOptions)\r\n                }\r\n            }));\r\n            valueAxis.setMarginOptions(marginOptions)\r\n        }));\r\n        this._argumentAxes.forEach((a => a.setMarginOptions(argumentMarginOptions)))\r\n    },\r\n    _populateBusinessRange(updatedAxis, keepRange) {\r\n        const rotated = this._isRotated();\r\n        const series = this._getVisibleSeries();\r\n        const argRanges = {};\r\n        const commonArgRange = new Range({\r\n            rotated: !!rotated\r\n        });\r\n        const getPaneName = axis => axis.pane || \"default\";\r\n        this.panes.forEach((p => {\r\n            argRanges[p.name] = new Range({\r\n                rotated: !!rotated\r\n            })\r\n        }));\r\n        this._valueAxes.forEach((valueAxis => {\r\n            const groupRange = new Range({\r\n                rotated: !!rotated,\r\n                pane: valueAxis.pane,\r\n                axis: valueAxis.name\r\n            });\r\n            const groupSeries = series.filter((series => series.getValueAxis() === valueAxis));\r\n            groupSeries.forEach((series => {\r\n                const seriesRange = series.getRangeData();\r\n                groupRange.addRange(seriesRange.val);\r\n                argRanges[getPaneName(valueAxis)].addRange(seriesRange.arg)\r\n            }));\r\n            if (!updatedAxis || updatedAxis && groupSeries.length && valueAxis === updatedAxis) {\r\n                valueAxis.setGroupSeries(groupSeries);\r\n                valueAxis.setBusinessRange(groupRange, this._axesReinitialized || keepRange, this._argumentAxes[0]._lastVisualRangeUpdateMode)\r\n            }\r\n        }));\r\n        if (!updatedAxis || updatedAxis && series.length) {\r\n            Object.keys(argRanges).forEach((p => commonArgRange.addRange(argRanges[p])));\r\n            const commonInterval = commonArgRange.interval;\r\n            this._argumentAxes.forEach((a => {\r\n                const currentInterval = argRanges[getPaneName(a)].interval ?? commonInterval;\r\n                a.setBusinessRange(new Range(_extends({}, commonArgRange, {\r\n                    interval: currentInterval\r\n                })), this._axesReinitialized, void 0, this._groupsData.categories)\r\n            }))\r\n        }\r\n        this._populateMarginOptions()\r\n    },\r\n    getArgumentAxis() {\r\n        return (this._argumentAxes || []).find((a => !a.isVirtual))\r\n    },\r\n    getValueAxis(name) {\r\n        return (this._valueAxes || []).find(_isDefined(name) ? a => a.name === name : a => a.pane === this.defaultPane)\r\n    },\r\n    _getGroupsData() {\r\n        const groups = [];\r\n        this._valueAxes.forEach((axis => {\r\n            groups.push({\r\n                series: this.series.filter((series => series.getValueAxis() === axis)),\r\n                valueAxis: axis,\r\n                valueOptions: axis.getOptions()\r\n            })\r\n        }));\r\n        return {\r\n            groups: groups,\r\n            argumentAxes: this._argumentAxes,\r\n            argumentOptions: this._argumentAxes[0].getOptions()\r\n        }\r\n    },\r\n    _groupSeries() {\r\n        this._correctValueAxes(false);\r\n        this._groupsData = this._getGroupsData()\r\n    },\r\n    _processValueAxisFormat() {\r\n        const axesWithFullStackedFormat = [];\r\n        this.series.forEach((series => {\r\n            const axis = series.getValueAxis();\r\n            if (series.isFullStackedSeries()) {\r\n                axis.setPercentLabelFormat();\r\n                axesWithFullStackedFormat.push(axis)\r\n            }\r\n        }));\r\n        this._valueAxes.forEach((axis => {\r\n            if (!axesWithFullStackedFormat.includes(axis)) {\r\n                axis.resetAutoLabelFormat()\r\n            }\r\n        }))\r\n    },\r\n    _populateAxesOptions(typeSelector, userOptions, axisOptions, rotated, virtual) {\r\n        const preparedUserOptions = this._prepareStripsAndConstantLines(typeSelector, userOptions, rotated);\r\n        const options = _extend(true, {}, preparedUserOptions, axisOptions, this._prepareAxisOptions(typeSelector, preparedUserOptions, rotated));\r\n        if (virtual) {\r\n            options.visible = false;\r\n            options.tick.visible = false;\r\n            options.minorTick.visible = false;\r\n            options.label.visible = false;\r\n            options.title = {}\r\n        }\r\n        return options\r\n    },\r\n    _getValFilter: series => rangeDataCalculator.getViewPortFilter(series.getValueAxis().visualRange() || {}),\r\n    _createAxis(isArgumentAxes, options, virtual) {\r\n        const typeSelector = isArgumentAxes ? \"argumentAxis\" : \"valueAxis\";\r\n        const renderingSettings = _extend({\r\n            renderer: this._renderer,\r\n            incidentOccurred: this._incidentOccurred,\r\n            eventTrigger: this._eventTrigger,\r\n            axisClass: isArgumentAxes ? \"arg\" : \"val\",\r\n            widgetClass: \"dxc\",\r\n            stripsGroup: this._stripsGroup,\r\n            stripLabelAxesGroup: this._stripLabelAxesGroup,\r\n            constantLinesGroup: this._constantLinesGroup,\r\n            scaleBreaksGroup: this._scaleBreaksGroup,\r\n            axesContainerGroup: this._axesGroup,\r\n            labelsAxesGroup: this._labelsAxesGroup,\r\n            gridGroup: this._gridGroup,\r\n            isArgumentAxis: isArgumentAxes,\r\n            getTemplate: template => this._getTemplate(template)\r\n        }, this._getAxisRenderingOptions(typeSelector));\r\n        const axis = new Axis(renderingSettings);\r\n        axis.updateOptions(options);\r\n        axis.isVirtual = virtual;\r\n        return axis\r\n    },\r\n    _applyVisualRangeByVirtualAxes: () => false,\r\n    _applyCustomVisualRangeOption(axis, range) {\r\n        if (axis.getOptions().optionPath) {\r\n            this._parseVisualRangeOption(`${axis.getOptions().optionPath}.visualRange`, range)\r\n        }\r\n    },\r\n    _getVisualRangeSetter() {\r\n        return (axis, _ref) => {\r\n            let {\r\n                skipEventRising: skipEventRising,\r\n                range: range\r\n            } = _ref;\r\n            this._applyCustomVisualRangeOption(axis, range);\r\n            axis.setCustomVisualRange(range);\r\n            axis.skipEventRising = skipEventRising;\r\n            if (!this._applyVisualRangeByVirtualAxes(axis, range)) {\r\n                if (this._applyingChanges) {\r\n                    this._change_VISUAL_RANGE()\r\n                } else {\r\n                    this._requestChange([VISUAL_RANGE])\r\n                }\r\n            }\r\n        }\r\n    },\r\n    _getTrackerSettings() {\r\n        return _extend(this.callBase(), {\r\n            argumentAxis: this.getArgumentAxis()\r\n        })\r\n    },\r\n    _prepareStripsAndConstantLines(typeSelector, userOptions, rotated) {\r\n        userOptions = this._themeManager.getOptions(typeSelector, userOptions, rotated);\r\n        if (userOptions.strips) {\r\n            userOptions.strips.forEach(((line, i) => {\r\n                userOptions.strips[i] = _extend(true, {}, userOptions.stripStyle, line)\r\n            }))\r\n        }\r\n        if (userOptions.constantLines) {\r\n            userOptions.constantLines.forEach(((line, i) => {\r\n                userOptions.constantLines[i] = _extend(true, {}, userOptions.constantLineStyle, line)\r\n            }))\r\n        }\r\n        return userOptions\r\n    },\r\n    refresh() {\r\n        this._disposeAxes();\r\n        this.callBase()\r\n    },\r\n    _layoutAxes(drawAxes) {\r\n        drawAxes();\r\n        const needSpace = this.checkForMoreSpaceForPanesCanvas();\r\n        if (needSpace) {\r\n            const rect = this._rect.slice();\r\n            const size = this._layout.backward(rect, rect, [needSpace.width, needSpace.height]);\r\n            needSpace.width = Math.max(0, size[0]);\r\n            needSpace.height = Math.max(0, size[1]);\r\n            this._canvas = this._createCanvasFromRect(rect);\r\n            drawAxes(needSpace)\r\n        }\r\n    },\r\n    checkForMoreSpaceForPanesCanvas() {\r\n        return this.layoutManager.needMoreSpaceForPanesCanvas(this._getLayoutTargets(), this._isRotated())\r\n    },\r\n    _parseVisualRangeOption(fullName, value) {\r\n        const name = fullName.split(/[.[]/)[0];\r\n        let index = fullName.match(/\\d+/g);\r\n        index = _isDefined(index) ? parseInt(index[0], 10) : index;\r\n        if (fullName.indexOf(\"visualRange\") > 0) {\r\n            if (\"object\" !== type(value)) {\r\n                value = wrapVisualRange(fullName, value) ?? value\r\n            }\r\n            this._setCustomVisualRange(name, index, value)\r\n        } else if ((\"object\" === type(value) || isArray(value)) && name.indexOf(\"Axis\") > 0 && JSON.stringify(value).indexOf(\"visualRange\") > 0) {\r\n            if (_isDefined(value.visualRange)) {\r\n                this._setCustomVisualRange(name, index, value.visualRange)\r\n            } else if (isArray(value)) {\r\n                value.forEach(((a, i) => {\r\n                    if (_isDefined(a.visualRange)) {\r\n                        this._setCustomVisualRange(name, i, a.visualRange)\r\n                    }\r\n                }))\r\n            }\r\n        }\r\n    },\r\n    _setCustomVisualRange(axesName, index, value) {\r\n        const options = this._options.silent(axesName);\r\n        if (!options) {\r\n            return\r\n        }\r\n        if (!_isDefined(index)) {\r\n            options._customVisualRange = value\r\n        } else {\r\n            options[index]._customVisualRange = value\r\n        }\r\n        this._axesReinitialized = true\r\n    },\r\n    _raiseZoomEndHandlers() {\r\n        this._valueAxes.forEach((axis => axis.handleZoomEnd()))\r\n    },\r\n    _setOptionsByReference() {\r\n        this.callBase();\r\n        _extend(this._optionsByReference, {\r\n            \"valueAxis.visualRange\": true\r\n        })\r\n    },\r\n    _notifyOptionChanged(option, value) {\r\n        this.callBase.apply(this, arguments);\r\n        if (!this._optionChangedLocker) {\r\n            this._parseVisualRangeOption(option, value)\r\n        }\r\n    },\r\n    _notifyVisualRange() {\r\n        this._valueAxes.forEach((axis => {\r\n            const axisPath = axis.getOptions().optionPath;\r\n            if (axisPath) {\r\n                const path = `${axisPath}.visualRange`;\r\n                const visualRange = convertVisualRangeObject(axis.visualRange(), !isArray(this.option(path)));\r\n                if (!axis.skipEventRising || !rangesAreEqual(visualRange, this.option(path))) {\r\n                    if (!this.option(axisPath) && \"valueAxis\" !== axisPath) {\r\n                        this.option(axisPath, {\r\n                            name: axis.name,\r\n                            visualRange: visualRange\r\n                        })\r\n                    } else {\r\n                        this.option(path, visualRange)\r\n                    }\r\n                } else {\r\n                    axis.skipEventRising = null\r\n                }\r\n            }\r\n        }))\r\n    },\r\n    _notify() {\r\n        this.callBase();\r\n        this._axesReinitialized = false;\r\n        if (true !== this.option(\"disableTwoWayBinding\")) {\r\n            this.skipOptionsRollBack = true;\r\n            this._notifyVisualRange();\r\n            this.skipOptionsRollBack = false\r\n        }\r\n    },\r\n    _getAxesForScaling() {\r\n        return this._valueAxes\r\n    },\r\n    _getAxesByOptionPath(arg, isDirectOption, optionName) {\r\n        const sourceAxes = this._getAxesForScaling();\r\n        let axes = [];\r\n        if (isDirectOption) {\r\n            let axisPath;\r\n            if (arg.fullName) {\r\n                axisPath = arg.fullName.slice(0, arg.fullName.indexOf(\".\"))\r\n            }\r\n            axes = sourceAxes.filter((a => a.getOptions().optionPath === axisPath))\r\n        } else if (\"object\" === type(arg.value)) {\r\n            axes = sourceAxes.filter((a => a.getOptions().optionPath === arg.name))\r\n        } else if (isArray(arg.value)) {\r\n            arg.value.forEach(((v, index) => {\r\n                const axis = sourceAxes.filter((a => a.getOptions().optionPath === `${arg.name}[${index}]`))[0];\r\n                if (_isDefined(v[optionName]) && _isDefined(axis)) {\r\n                    axes[index] = axis\r\n                }\r\n            }))\r\n        }\r\n        return axes\r\n    },\r\n    _optionChanged(arg) {\r\n        if (!this._optionChangedLocker) {\r\n            const optionName = \"visualRange\";\r\n            let axes;\r\n            const isDirectOption = arg.fullName.indexOf(optionName) > 0 ? true : this.getPartialChangeOptionsName(arg).indexOf(optionName) > -1 ? false : void 0;\r\n            if (_isDefined(isDirectOption)) {\r\n                axes = this._getAxesByOptionPath(arg, isDirectOption, optionName);\r\n                if (axes) {\r\n                    if (axes.length > 1 || isArray(arg.value)) {\r\n                        axes.forEach(((a, index) => setAxisVisualRangeByOption(arg, a, isDirectOption, index)))\r\n                    } else if (1 === axes.length) {\r\n                        setAxisVisualRangeByOption(arg, axes[0], isDirectOption)\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        this.callBase(arg)\r\n    },\r\n    _change_VISUAL_RANGE() {\r\n        this._recreateSizeDependentObjects(false);\r\n        if (!this._changes.has(\"FULL_RENDER\")) {\r\n            const resizePanesOnZoom = this.option(\"resizePanesOnZoom\");\r\n            this._doRender({\r\n                force: true,\r\n                drawTitle: false,\r\n                drawLegend: false,\r\n                adjustAxes: resizePanesOnZoom ?? (this.option(\"adjustAxesOnZoom\") || false),\r\n                animate: false\r\n            });\r\n            this._raiseZoomEndHandlers()\r\n        }\r\n    },\r\n    resetVisualRange() {\r\n        this._valueAxes.forEach((axis => {\r\n            axis.resetVisualRange(false);\r\n            this._applyCustomVisualRangeOption(axis)\r\n        }));\r\n        this._requestChange([VISUAL_RANGE])\r\n    },\r\n    _getCrosshairMargins: () => ({\r\n        x: 0,\r\n        y: 0\r\n    }),\r\n    _legendDataField: \"series\",\r\n    _adjustSeriesLabels: _noop,\r\n    _correctValueAxes: _noop\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAGA;AAGA;AAQA;AACA;AAGA;AAAA;AAIA;;;;;;;;;;;;;AAGA,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;AACJ,MAAM,oBAAoB;AAC1B,MAAM,OAAO;AACb,MAAM,uBAAuB;AAC7B,MAAM,oBAAoB;AAC1B,MAAM,eAAe;AAErB,SAAS,YAAY,WAAW;IAC5B,IAAI,QAAQ,cAAc;QACtB,OAAO,MAAM,YAAY,MAAM,GAAG;YAAC,CAAC;SAAE,GAAG;IAC7C;IACA,OAAO;QAAC;KAAY;AACxB;AAEA,SAAS,oBAAoB,aAAa,EAAE,UAAU;IAClD,IAAI,cAAc,iBAAiB,EAAE;QACjC,cAAc,IAAI,GAAG;IACzB;IACA,OAAO;AACX;AAEA,SAAS,mBAAmB,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO;IAC1D,MAAM,QAAQ,UAAU,KAAK,KAAK,GAAG,aAAa,KAAK,KAAK;IAC5D,MAAM,SAAS,UAAU,KAAK,MAAM,GAAG,KAAK,MAAM,GAAG;IACrD,OAAO,KAAK,GAAG,CAAC,OAAO,UAAU;AACrC;AAEA,SAAS,2BAA2B,GAAG,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK;IAChE,IAAI;IACJ,IAAI;IACJ,IAAI,gBAAgB;QAChB,cAAc,IAAI,KAAK;QACvB,UAAU;YACN,iBAAiB;QACrB;QACA,MAAM,qBAAqB,gBAAgB,IAAI,QAAQ,EAAE;QACzD,IAAI,oBAAoB;YACpB,UAAU;gBACN,oBAAoB;YACxB;YACA,cAAc;QAClB;IACJ,OAAO;QACH,cAAc,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,SAAS,IAAI,KAAK,CAAC,MAAM,GAAG,IAAI,KAAK,EAAE,WAAW;IAChF;IACA,KAAK,WAAW,CAAC,aAAa;AAClC;AAEA,SAAS,aAAa,UAAU,EAAE,IAAI,EAAE,cAAc;IAClD,IAAI,gBAAgB;QAChB,OAAO;YACH,kBAAkB,WAAW,gBAAgB;YAC7C,cAAc,WAAW,YAAY;QACzC;IACJ;IACA,MAAM,EACF,eAAe,aAAa,EAC5B,WAAW,SAAS,EACvB,GAAG,WAAW,MAAM,CAAC,IAAI,CAAE,CAAA,IAAK,EAAE,SAAS,KAAK;IACjD,OAAO;QACH,eAAe;QACf,WAAW;IACf;AACJ;AAEA,SAAS,gBAAgB,QAAQ,EAAE,KAAK;IACpC,MAAM,eAAe,SAAS,KAAK,CAAC;IACpC,MAAM,WAAW,aAAa,EAAE,CAAC,CAAC;IAClC,IAAI,eAAe,YAAY,iBAAiB,UAAU;QACtD,OAAO;YACH,CAAC,SAAS,EAAE;QAChB;IACJ;IACA;AACJ;AACO,MAAM,gBAAgB,6LAAA,CAAA,YAAS,CAAC,OAAO,CAAC;IAC3C,aAAa;QAAC,CAAC,yBAAyB,EAAE,MAAM;QAAE,CAAC,yBAAyB,EAAE,MAAM;KAAC;IACrF,0BAA0B;QACtB,aAAa;QACb,oBAAoB;QACpB,QAAQ;QACR,eAAe;IACnB;IACA,2BAA2B;QACvB,cAAc;YAAC;YAAU;YAAiB;YAAe;SAAqB;QAC9E,WAAW;YAAC;YAAU;YAAiB;YAAe;SAAqB;IAC/E;IACA;QACI,IAAI,CAAC,eAAe,GAAG,CAAC;QACxB,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,MAAM,wBAAwB,IAAI,CAAC,sBAAsB;QACzD,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,IAAI,CAAC,QAAQ;QACb,sBAAsB,IAAI,CAAC,gBAAgB;QAC3C,sBAAsB,IAAI,CAAC,gBAAgB;QAC3C,sBAAsB,IAAI,CAAC,gBAAgB;QAC3C,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,gBAAgB,CAAC,OAAO;QAC7B,IAAI,CAAC,gBAAgB,CAAC,OAAO;QAC7B,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA;QACI,MAAM,wBAAwB,IAAI,CAAC,sBAAsB;QACzD,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,OAAO;YACpB,IAAI,CAAC,OAAO,GAAG;QACnB;QACA,sBAAsB,IAAI,CAAC,IAAI,EAAE;QACjC,sBAAsB,IAAI,CAAC,IAAI,EAAE;QACjC,IAAI,CAAC,YAAY;IACrB;IACA;QACI,IAAI,CAAC,oBAAoB,CAAC;QAC1B,IAAI,CAAC,oBAAoB,CAAC;QAC1B,IAAI,CAAC,oBAAoB,CAAC;IAC9B;IACA,sBAAqB,aAAa;QAC9B,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,cAAc;QACrD,CAAC,aAAa,EAAE,EAAE,OAAO,CAAE,CAAA;YACvB,SAAS,YAAY,KAAK,MAAM,YAAY,SAAS,OAAO;QAChE;QACA,IAAI,CAAC,eAAe,CAAC,cAAc,GAAG,EAAE;IAC5C;IACA,wBAAuB,QAAQ;QAC3B,MAAM,YAAY,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU;QAC1E,OAAO,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,EAAE;IAC7E;IACA,eAAc,QAAQ;QAClB,MAAM,OAAO,YAAY;QACzB,OAAO,IAAI,CAAC,KAAK,CAAC,SAAS,CAAE,CAAA,OAAQ,KAAK,IAAI,KAAK;IACvD;IACA,aAAY,iBAAiB;QACzB,IAAI,CAAC,QAAQ;QACb,IAAI,qBAAqB,CAAA,GAAA,iKAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,GAAG;YAC/E,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,OAAO;QAC7D;QACA,CAAA,GAAA,yJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,CAAC,OAAO;IAChC;IACA;QACI,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY;QAC9B,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA;QACI,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI;QACR,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,sBAAsB,YAAY,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,EAAE,CAAC,EAAE;QAC7E,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM,mBAAmB,YAAY,mBAAmB,CAAC;QACzD,IAAI,+BAA+B,EAAE;QACrC,MAAM,4BAA4B,EAAE;QACpC,MAAM,YAAY,EAAE;QACpB,IAAI,mBAAmB;QACvB,IAAI;QACJ,MAAM,mBAAmB,IAAI,CAAC,oBAAoB;QAElD,SAAS;YACL,MAAM,OAAO,oBAAoB,OAAO;YACxC,oBAAoB;YACpB,OAAO;QACX;QACA,IAAI,SAAS;YACT,yBAAyB,YAAY,oBAAoB,QAAQ,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI;QACpH,OAAO;YACH,yBAAyB,UAAU,oBAAoB,QAAQ,GAAG,KAAK,CAAC,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI;QAClH;QACA,+BAA+B,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,OAAQ,CAAA;YACxC,MAAM,UAAU,KAAK,IAAI,KAAK;YAC9B,OAAO,IAAI,CAAC,oBAAoB,CAAC,gBAAgB,qBAAqB;gBAClE,MAAM,KAAK,IAAI;gBACf,MAAM;gBACN,YAAY;gBACZ,iBAAiB,UAAU,iBAAiB,CAAC,GAAG,iBAAiB,CAAC;YACtE,GAAG,SAAS;QAChB;QACA,iBAAiB,OAAO,CAAE,CAAC,aAAa;YACpC,IAAI;YACJ,IAAI,YAAY,EAAE;YAClB,MAAM,EACF,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,QAAQ,UAAU,QAAQ,CAAC,OAAO;gBAClC,IAAI,CAAC,iBAAiB,CAAC;gBACvB;YACJ;YACA,IAAI,MAAM;gBACN,UAAU,IAAI,CAAC;YACnB;YACA,IAAI,YAAY,IAAI,EAAE;gBAClB,UAAU,IAAI,CAAC,YAAY,IAAI;YACnC;YACA,IAAI,SAAS,CAAC,qBAAqB,YAAY,KAAK,KAAK,KAAK,MAAM,sBAAsB,mBAAmB,MAAM,EAAE;gBACjH,YAAY,UAAU,MAAM,CAAC,YAAY,KAAK,CAAC,KAAK,CAAC;YACzD;YACA,YAAY,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE;YACnB,IAAI,CAAC,UAAU,MAAM,EAAE;gBACnB,UAAU,IAAI,CAAC,KAAK;YACxB;YACA,UAAU,OAAO,CAAE,CAAA;gBACf,MAAM,aAAa,QAAQ,mBAAmB,CAAC,UAAU,EAAE,OAAO,UAAU,CAAC,CAAC,GAAG;gBACjF,0BAA0B,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa,aAAa;oBAC/E,MAAM,QAAQ;oBACd,MAAM;oBACN,UAAU;oBACV,YAAY;oBACZ,iBAAiB,UAAU,iBAAiB,CAAC,GAAG,iBAAiB,CAAC;gBACtE,GAAG;YACP;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,8BAA8B,MAAM;QACvD,IAAI,CAAC,aAAa,CAAC,2BAA2B;IAClD;IACA,eAAc,OAAO,EAAE,cAAc,EAAE,sBAAsB;QACzD,MAAM,YAAY,EAAE;QACpB,IAAI,OAAO,iBAAiB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU;QAChE,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI;YACJ,MAAM,UAAU,SAAS,CAAC,QAAQ,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,KAAK,CAAC,IAAI,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,EAAE,IAAI,KAAM,EAAE,IAAI,KAAK,IAAI,IAAI;YAC3M,IAAI,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,MAAM,EAAE;gBAC1D,QAAQ,OAAO,CAAE,CAAA;oBACb,MAAM,YAAY,aAAa,IAAI,CAAC,WAAW,EAAE,MAAM;oBACvD,KAAK,aAAa,CAAC;oBACnB,IAAI,gBAAgB;wBAChB,KAAK,QAAQ,CAAC,UAAU,gBAAgB,EAAE,UAAU,YAAY,EAAE;oBACtE,OAAO;wBACH,KAAK,QAAQ,CAAC,UAAU,aAAa,EAAE,UAAU,SAAS,EAAE;oBAChE;oBACA,KAAK,QAAQ;oBACb,UAAU,IAAI,CAAC;wBACX,MAAM;oBACV;gBACJ;YACJ,OAAO;gBACH,UAAU,IAAI,CAAC;oBACX,SAAS;gBACb;YACJ;QACJ;QACA,IAAI,MAAM;YACN,CAAA,GAAA,iLAAA,CAAA,cAAY,AAAD,EAAE,MAAO,CAAC,OAAO;gBACxB,IAAI,CAAC,UAAU,IAAI,CAAE,CAAA,QAAS,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,OAAQ;oBAC/D,IAAI,CAAC,YAAY,CAAC,OAAO;gBAC7B;YACJ;QACJ,OAAO,IAAI,gBAAgB;YACvB,OAAO,IAAI,CAAC,aAAa,GAAG,EAAE;QAClC,OAAO;YACH,OAAO,IAAI,CAAC,UAAU,GAAG,EAAE;QAC/B;QACA,UAAU,OAAO,CAAE,CAAA;YACf,IAAI,EACA,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,MAAM,IAAI,IAAI,gBAAgB;gBAC9B,MAAM,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,IAAI,KAAK;YAC/C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACtB,OAAO,IAAI,CAAC,WAAW,CAAC,gBAAgB,MAAM,OAAO,EAAE,iBAAiB,MAAM,OAAO,CAAC,IAAI,KAAK,yBAAyB,KAAK;gBAC7H,KAAK,IAAI,CAAC;YACd;YACA,KAAK,sBAAsB,CAAC,IAAI,CAAC,qBAAqB;QAC1D;IACJ;IACA,cAAa,KAAK,EAAE,cAAc;QAC9B,MAAM,OAAO,iBAAiB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU;QAClE,MAAM,OAAO,IAAI,CAAC,MAAM;QACxB,IAAI,CAAC,MAAM;YACP;QACJ;QACA,KAAK,OAAO;QACZ,KAAK,MAAM,CAAC,OAAO;IACvB;IACA;QACI,MAAM,wBAAwB,IAAI,CAAC,sBAAsB;QACzD,sBAAsB,IAAI,CAAC,IAAI,EAAE;QACjC,sBAAsB,IAAI,CAAC,IAAI,EAAE;IACrC;IACA;QACI,IAAI,CAAC,qBAAqB,CAAC,UAAU;QACrC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,eAAe,CAAC,UAAU;QACnC;IACJ;IACA;QACI,OAAO,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,EAAE,GAAG,CAAE,CAAA;YAC5B,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC;YACpC,KAAK,UAAU,CAAC,MAAM,GAAG;YACzB,IAAI,CAAC,EAAE,UAAU,GAAG,YAAY,EAAE;gBAC9B,KAAK,UAAU,CAAC,OAAO,GAAG;YAC9B;YACA,OAAO;QACX;IACJ;IACA,sBAAsB;IACtB;QACI,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,uBAAuB;IAChC;IACA;QACI,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,KAAK,EAAG;YAC5C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,YAAY;QAC/B;IACJ;IACA;QACI,IAAI,CAAC,sBAAsB;IAC/B;IACA;QACI,IAAI;QACJ,MAAM,QAAQ,EAAE;QAChB,MAAM,WAAW,EAAE;QACnB,IAAI;QACJ,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,oBAAoB,aAAa,UAAU,CAAC;QAClD,MAAM,mBAAmB,aAAa,UAAU,CAAC;QACjD,MAAM,gBAAgB;YAClB,eAAe,aAAa,UAAU,CAAC;YACvC,eAAe,aAAa,UAAU,CAAC;YACvC,iBAAiB,aAAa,UAAU,CAAC;YACzC,eAAe,aAAa,UAAU,CAAC;YACvC,mBAAmB,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,qBAAqB,oBAAoB;QAC3E;QACA,IAAI,SAAS,CAAC,uBAAuB,IAAI,CAAC,cAAc,KAAK,KAAK,MAAM,wBAAwB,qBAAqB,MAAM,EAAE;YACzH,IAAI,CAAC,cAAc,CAAC,OAAO,CAAE,CAAA;gBACzB,OAAO,aAAa,CAAC;gBACrB,OAAO,kBAAkB;YAC7B;YACA;QACJ;QACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,IAAI,CAAC,MAAM,QAAQ,CAAC,KAAK,IAAI,GAAG;gBAC5B,MAAM,IAAI,CAAC,KAAK,IAAI;YACxB;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG,OAAO,CAAE,CAAA;YAC9B,aAAa,IAAI,CAAC,iBAAiB,CAAC,KAAK,IAAI;YAC7C,MAAM,OAAO,CAAE,CAAA;gBACX,MAAM,SAAS,IAAI,iKAAA,CAAA,eAAY,CAAC;oBAC5B,MAAM;oBACN,MAAM,KAAK,IAAI;oBACf,eAAe,cAAc,aAAa;oBAC1C,eAAe,cAAc,aAAa;oBAC1C,iBAAiB,cAAc,eAAe;oBAC9C,eAAe,cAAc,aAAa;oBAC1C,mBAAmB,cAAc,iBAAiB;oBAClD,SAAS,IAAI,CAAC,UAAU;gBAC5B;gBACA,OAAO,GAAG,CAAC;gBACX,OAAO,kBAAkB;gBACzB,SAAS,IAAI,CAAC;YAClB;QACJ;QACA,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA;QACI,MAAM,iBAAiB,IAAI,CAAC,cAAc,IAAI,EAAE;QAChD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,KAAK,EAAG;YAC/C,MAAM,SAAS,cAAc,CAAC,EAAE;YAChC,OAAO,kBAAkB;YACzB,OAAO,sBAAsB;QACjC;IACJ;IACA,oBAAmB,MAAM;QACrB,IAAI;QACJ,OAAO,SAAS,CAAC,gBAAgB,IAAI,CAAC,OAAO,KAAK,KAAK,MAAM,gBAAgB,KAAK,IAAI,cAAc,iBAAiB,CAAC;IAC1H;IACA;QACI,IAAI,CAAC,YAAY,CAAC,UAAU;QAC5B,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,IAAI,CAAC,UAAU,CAAC,UAAU;QAC1B,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAChC,IAAI,CAAC,mBAAmB,CAAC,UAAU;QACnC,IAAI,CAAC,oBAAoB,CAAC,UAAU;QACpC,IAAI,CAAC,iBAAiB,CAAC,UAAU;IACrC;IACA;QACI,MAAM,aAAa,mBAAmB,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,kBAAkB,IAAI,CAAC,UAAU;QACxI,IAAI,wBAAwB,CAAC;QAC7B,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE,CAAA,SAAU,OAAO,YAAY,OAAO;YAC5E,IAAI,gBAAgB,CAAC;YACrB,YAAY,OAAO,CAAE,CAAA;gBACjB,IAAI,OAAO,SAAS,IAAI;oBACpB,MAAM,sBAAsB,oBAAoB,OAAO,gBAAgB,IAAI;oBAC3E,gBAAgB,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;oBAClD,wBAAwB,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE,uBAAuB;gBACtE;YACJ;YACA,UAAU,gBAAgB,CAAC;QAC/B;QACA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA,IAAK,EAAE,gBAAgB,CAAC;IACxD;IACA,wBAAuB,WAAW,EAAE,SAAS;QACzC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,SAAS,IAAI,CAAC,iBAAiB;QACrC,MAAM,YAAY,CAAC;QACnB,MAAM,iBAAiB,IAAI,gKAAA,CAAA,QAAK,CAAC;YAC7B,SAAS,CAAC,CAAC;QACf;QACA,MAAM,cAAc,CAAA,OAAQ,KAAK,IAAI,IAAI;QACzC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;YAChB,SAAS,CAAC,EAAE,IAAI,CAAC,GAAG,IAAI,gKAAA,CAAA,QAAK,CAAC;gBAC1B,SAAS,CAAC,CAAC;YACf;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,MAAM,aAAa,IAAI,gKAAA,CAAA,QAAK,CAAC;gBACzB,SAAS,CAAC,CAAC;gBACX,MAAM,UAAU,IAAI;gBACpB,MAAM,UAAU,IAAI;YACxB;YACA,MAAM,cAAc,OAAO,MAAM,CAAE,CAAA,SAAU,OAAO,YAAY,OAAO;YACvE,YAAY,OAAO,CAAE,CAAA;gBACjB,MAAM,cAAc,OAAO,YAAY;gBACvC,WAAW,QAAQ,CAAC,YAAY,GAAG;gBACnC,SAAS,CAAC,YAAY,WAAW,CAAC,QAAQ,CAAC,YAAY,GAAG;YAC9D;YACA,IAAI,CAAC,eAAe,eAAe,YAAY,MAAM,IAAI,cAAc,aAAa;gBAChF,UAAU,cAAc,CAAC;gBACzB,UAAU,gBAAgB,CAAC,YAAY,IAAI,CAAC,kBAAkB,IAAI,WAAW,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,0BAA0B;YACjI;QACJ;QACA,IAAI,CAAC,eAAe,eAAe,OAAO,MAAM,EAAE;YAC9C,OAAO,IAAI,CAAC,WAAW,OAAO,CAAE,CAAA,IAAK,eAAe,QAAQ,CAAC,SAAS,CAAC,EAAE;YACzE,MAAM,iBAAiB,eAAe,QAAQ;YAC9C,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA;gBACxB,MAAM,kBAAkB,SAAS,CAAC,YAAY,GAAG,CAAC,QAAQ,IAAI;gBAC9D,EAAE,gBAAgB,CAAC,IAAI,gKAAA,CAAA,QAAK,CAAC,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;oBACtD,UAAU;gBACd,KAAK,IAAI,CAAC,kBAAkB,EAAE,KAAK,GAAG,IAAI,CAAC,WAAW,CAAC,UAAU;YACrE;QACJ;QACA,IAAI,CAAC,sBAAsB;IAC/B;IACA;QACI,OAAO,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,EAAE,IAAI,CAAE,CAAA,IAAK,CAAC,EAAE,SAAS;IAC7D;IACA,cAAa,IAAI;QACb,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE,EAAE,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,QAAQ,CAAA,IAAK,EAAE,IAAI,KAAK,OAAO,CAAA,IAAK,EAAE,IAAI,KAAK,IAAI,CAAC,WAAW;IAClH;IACA;QACI,MAAM,SAAS,EAAE;QACjB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,OAAO,IAAI,CAAC;gBACR,QAAQ,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE,CAAA,SAAU,OAAO,YAAY,OAAO;gBAChE,WAAW;gBACX,cAAc,KAAK,UAAU;YACjC;QACJ;QACA,OAAO;YACH,QAAQ;YACR,cAAc,IAAI,CAAC,aAAa;YAChC,iBAAiB,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,UAAU;QACrD;IACJ;IACA;QACI,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc;IAC1C;IACA;QACI,MAAM,4BAA4B,EAAE;QACpC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,MAAM,OAAO,OAAO,YAAY;YAChC,IAAI,OAAO,mBAAmB,IAAI;gBAC9B,KAAK,qBAAqB;gBAC1B,0BAA0B,IAAI,CAAC;YACnC;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,IAAI,CAAC,0BAA0B,QAAQ,CAAC,OAAO;gBAC3C,KAAK,oBAAoB;YAC7B;QACJ;IACJ;IACA,sBAAqB,YAAY,EAAE,WAAW,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO;QACzE,MAAM,sBAAsB,IAAI,CAAC,8BAA8B,CAAC,cAAc,aAAa;QAC3F,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG,qBAAqB,aAAa,IAAI,CAAC,mBAAmB,CAAC,cAAc,qBAAqB;QAChI,IAAI,SAAS;YACT,QAAQ,OAAO,GAAG;YAClB,QAAQ,IAAI,CAAC,OAAO,GAAG;YACvB,QAAQ,SAAS,CAAC,OAAO,GAAG;YAC5B,QAAQ,KAAK,CAAC,OAAO,GAAG;YACxB,QAAQ,KAAK,GAAG,CAAC;QACrB;QACA,OAAO;IACX;IACA,eAAe,CAAA,SAAU,sLAAA,CAAA,UAAmB,CAAC,iBAAiB,CAAC,OAAO,YAAY,GAAG,WAAW,MAAM,CAAC;IACvG,aAAY,cAAc,EAAE,OAAO,EAAE,OAAO;QACxC,MAAM,eAAe,iBAAiB,iBAAiB;QACvD,MAAM,oBAAoB,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE;YAC9B,UAAU,IAAI,CAAC,SAAS;YACxB,kBAAkB,IAAI,CAAC,iBAAiB;YACxC,cAAc,IAAI,CAAC,aAAa;YAChC,WAAW,iBAAiB,QAAQ;YACpC,aAAa;YACb,aAAa,IAAI,CAAC,YAAY;YAC9B,qBAAqB,IAAI,CAAC,oBAAoB;YAC9C,oBAAoB,IAAI,CAAC,mBAAmB;YAC5C,kBAAkB,IAAI,CAAC,iBAAiB;YACxC,oBAAoB,IAAI,CAAC,UAAU;YACnC,iBAAiB,IAAI,CAAC,gBAAgB;YACtC,WAAW,IAAI,CAAC,UAAU;YAC1B,gBAAgB;YAChB,aAAa,CAAA,WAAY,IAAI,CAAC,YAAY,CAAC;QAC/C,GAAG,IAAI,CAAC,wBAAwB,CAAC;QACjC,MAAM,OAAO,IAAI,6JAAA,CAAA,OAAI,CAAC;QACtB,KAAK,aAAa,CAAC;QACnB,KAAK,SAAS,GAAG;QACjB,OAAO;IACX;IACA,gCAAgC,IAAM;IACtC,+BAA8B,IAAI,EAAE,KAAK;QACrC,IAAI,KAAK,UAAU,GAAG,UAAU,EAAE;YAC9B,IAAI,CAAC,uBAAuB,CAAC,GAAG,KAAK,UAAU,GAAG,UAAU,CAAC,YAAY,CAAC,EAAE;QAChF;IACJ;IACA;QACI,OAAO,CAAC,MAAM;YACV,IAAI,EACA,iBAAiB,eAAe,EAChC,OAAO,KAAK,EACf,GAAG;YACJ,IAAI,CAAC,6BAA6B,CAAC,MAAM;YACzC,KAAK,oBAAoB,CAAC;YAC1B,KAAK,eAAe,GAAG;YACvB,IAAI,CAAC,IAAI,CAAC,8BAA8B,CAAC,MAAM,QAAQ;gBACnD,IAAI,IAAI,CAAC,gBAAgB,EAAE;oBACvB,IAAI,CAAC,oBAAoB;gBAC7B,OAAO;oBACH,IAAI,CAAC,cAAc,CAAC;wBAAC;qBAAa;gBACtC;YACJ;QACJ;IACJ;IACA;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;YAC5B,cAAc,IAAI,CAAC,eAAe;QACtC;IACJ;IACA,gCAA+B,YAAY,EAAE,WAAW,EAAE,OAAO;QAC7D,cAAc,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,cAAc,aAAa;QACvE,IAAI,YAAY,MAAM,EAAE;YACpB,YAAY,MAAM,CAAC,OAAO,CAAE,CAAC,MAAM;gBAC/B,YAAY,MAAM,CAAC,EAAE,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG,YAAY,UAAU,EAAE;YACtE;QACJ;QACA,IAAI,YAAY,aAAa,EAAE;YAC3B,YAAY,aAAa,CAAC,OAAO,CAAE,CAAC,MAAM;gBACtC,YAAY,aAAa,CAAC,EAAE,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG,YAAY,iBAAiB,EAAE;YACpF;QACJ;QACA,OAAO;IACX;IACA;QACI,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,QAAQ;IACjB;IACA,aAAY,QAAQ;QAChB;QACA,MAAM,YAAY,IAAI,CAAC,+BAA+B;QACtD,IAAI,WAAW;YACX,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK;YAC7B,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM;gBAAC,UAAU,KAAK;gBAAE,UAAU,MAAM;aAAC;YAClF,UAAU,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE;YACrC,UAAU,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE;YACtC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC;YAC1C,SAAS;QACb;IACJ;IACA;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,UAAU;IACnG;IACA,yBAAwB,QAAQ,EAAE,KAAK;QACnC,MAAM,OAAO,SAAS,KAAK,CAAC,OAAO,CAAC,EAAE;QACtC,IAAI,QAAQ,SAAS,KAAK,CAAC;QAC3B,QAAQ,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,SAAS,SAAS,KAAK,CAAC,EAAE,EAAE,MAAM;QACrD,IAAI,SAAS,OAAO,CAAC,iBAAiB,GAAG;YACrC,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,QAAQ;gBAC1B,QAAQ,gBAAgB,UAAU,UAAU;YAChD;YACA,IAAI,CAAC,qBAAqB,CAAC,MAAM,OAAO;QAC5C,OAAO,IAAI,CAAC,aAAa,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,UAAU,QAAQ,MAAM,KAAK,KAAK,OAAO,CAAC,UAAU,KAAK,KAAK,SAAS,CAAC,OAAO,OAAO,CAAC,iBAAiB,GAAG;YACrI,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,MAAM,WAAW,GAAG;gBAC/B,IAAI,CAAC,qBAAqB,CAAC,MAAM,OAAO,MAAM,WAAW;YAC7D,OAAO,IAAI,QAAQ,QAAQ;gBACvB,MAAM,OAAO,CAAE,CAAC,GAAG;oBACf,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,EAAE,WAAW,GAAG;wBAC3B,IAAI,CAAC,qBAAqB,CAAC,MAAM,GAAG,EAAE,WAAW;oBACrD;gBACJ;YACJ;QACJ;IACJ;IACA,uBAAsB,QAAQ,EAAE,KAAK,EAAE,KAAK;QACxC,MAAM,UAAU,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,SAAS;YACV;QACJ;QACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,QAAQ;YACpB,QAAQ,kBAAkB,GAAG;QACjC,OAAO;YACH,OAAO,CAAC,MAAM,CAAC,kBAAkB,GAAG;QACxC;QACA,IAAI,CAAC,kBAAkB,GAAG;IAC9B;IACA;QACI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,aAAa;IACvD;IACA;QACI,IAAI,CAAC,QAAQ;QACb,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC9B,yBAAyB;QAC7B;IACJ;IACA,sBAAqB,MAAM,EAAE,KAAK;QAC9B,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QAC1B,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,uBAAuB,CAAC,QAAQ;QACzC;IACJ;IACA;QACI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,MAAM,WAAW,KAAK,UAAU,GAAG,UAAU;YAC7C,IAAI,UAAU;gBACV,MAAM,OAAO,GAAG,SAAS,YAAY,CAAC;gBACtC,MAAM,cAAc,CAAA,GAAA,yJAAA,CAAA,2BAAwB,AAAD,EAAE,KAAK,WAAW,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC;gBACtF,IAAI,CAAC,KAAK,eAAe,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,IAAI,CAAC,MAAM,CAAC,QAAQ;oBAC1E,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa,gBAAgB,UAAU;wBACpD,IAAI,CAAC,MAAM,CAAC,UAAU;4BAClB,MAAM,KAAK,IAAI;4BACf,aAAa;wBACjB;oBACJ,OAAO;wBACH,IAAI,CAAC,MAAM,CAAC,MAAM;oBACtB;gBACJ,OAAO;oBACH,KAAK,eAAe,GAAG;gBAC3B;YACJ;QACJ;IACJ;IACA;QACI,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,yBAAyB;YAC9C,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,mBAAmB,GAAG;QAC/B;IACJ;IACA;QACI,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,sBAAqB,GAAG,EAAE,cAAc,EAAE,UAAU;QAChD,MAAM,aAAa,IAAI,CAAC,kBAAkB;QAC1C,IAAI,OAAO,EAAE;QACb,IAAI,gBAAgB;YAChB,IAAI;YACJ,IAAI,IAAI,QAAQ,EAAE;gBACd,WAAW,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,IAAI,QAAQ,CAAC,OAAO,CAAC;YAC1D;YACA,OAAO,WAAW,MAAM,CAAE,CAAA,IAAK,EAAE,UAAU,GAAG,UAAU,KAAK;QACjE,OAAO,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,KAAK,GAAG;YACrC,OAAO,WAAW,MAAM,CAAE,CAAA,IAAK,EAAE,UAAU,GAAG,UAAU,KAAK,IAAI,IAAI;QACzE,OAAO,IAAI,QAAQ,IAAI,KAAK,GAAG;YAC3B,IAAI,KAAK,CAAC,OAAO,CAAE,CAAC,GAAG;gBACnB,MAAM,OAAO,WAAW,MAAM,CAAE,CAAA,IAAK,EAAE,UAAU,GAAG,UAAU,KAAK,GAAG,IAAI,IAAI,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAE,CAAC,EAAE;gBAC/F,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,CAAC,CAAC,WAAW,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,OAAO;oBAC/C,IAAI,CAAC,MAAM,GAAG;gBAClB;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAe,GAAG;QACd,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,MAAM,aAAa;YACnB,IAAI;YACJ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,OAAO,CAAC,cAAc,IAAI,OAAO,IAAI,CAAC,2BAA2B,CAAC,KAAK,OAAO,CAAC,cAAc,CAAC,IAAI,QAAQ,KAAK;YACnJ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,iBAAiB;gBAC5B,OAAO,IAAI,CAAC,oBAAoB,CAAC,KAAK,gBAAgB;gBACtD,IAAI,MAAM;oBACN,IAAI,KAAK,MAAM,GAAG,KAAK,QAAQ,IAAI,KAAK,GAAG;wBACvC,KAAK,OAAO,CAAE,CAAC,GAAG,QAAU,2BAA2B,KAAK,GAAG,gBAAgB;oBACnF,OAAO,IAAI,MAAM,KAAK,MAAM,EAAE;wBAC1B,2BAA2B,KAAK,IAAI,CAAC,EAAE,EAAE;oBAC7C;gBACJ;YACJ;QACJ;QACA,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA;QACI,IAAI,CAAC,6BAA6B,CAAC;QACnC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,gBAAgB;YACnC,MAAM,oBAAoB,IAAI,CAAC,MAAM,CAAC;YACtC,IAAI,CAAC,SAAS,CAAC;gBACX,OAAO;gBACP,WAAW;gBACX,YAAY;gBACZ,YAAY,qBAAqB,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB,KAAK;gBAC1E,SAAS;YACb;YACA,IAAI,CAAC,qBAAqB;QAC9B;IACJ;IACA;QACI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,KAAK,gBAAgB,CAAC;YACtB,IAAI,CAAC,6BAA6B,CAAC;QACvC;QACA,IAAI,CAAC,cAAc,CAAC;YAAC;SAAa;IACtC;IACA,sBAAsB,IAAM,CAAC;YACzB,GAAG;YACH,GAAG;QACP,CAAC;IACD,kBAAkB;IAClB,qBAAqB,+KAAA,CAAA,OAAK;IAC1B,mBAAmB,+KAAA,CAAA,OAAK;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6977, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/viz/m_chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/viz/m_chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport registerComponent from \"../../core/component_registrator\";\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    extend as _extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    each as _each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    getPrecision\r\n} from \"../../core/utils/math\";\r\nimport {\r\n    getHeight\r\n} from \"../../core/utils/size\";\r\nimport {\r\n    isDefined as _isDefined,\r\n    type\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    hasWindow\r\n} from \"../../core/utils/window\";\r\nimport {\r\n    Crosshair,\r\n    getMargins\r\n} from \"../../viz/chart_components/crosshair\";\r\nimport {\r\n    LayoutManager\r\n} from \"../../viz/chart_components/layout_manager\";\r\nimport multiAxesSynchronizer from \"../../viz/chart_components/multi_axes_synchronizer\";\r\nimport {\r\n    ScrollBar\r\n} from \"../../viz/chart_components/scroll_bar\";\r\nimport shutterZoom from \"../../viz/chart_components/shutter_zoom\";\r\nimport zoomAndPan from \"../../viz/chart_components/zoom_and_pan\";\r\nimport {\r\n    plugins\r\n} from \"../../viz/core/annotations\";\r\nimport {\r\n    convertVisualRangeObject,\r\n    extractColor,\r\n    getCategoriesInfo,\r\n    getLog,\r\n    isRelativeHeightPane,\r\n    map as _map,\r\n    normalizePanesHeight,\r\n    PANE_PADDING,\r\n    rangesAreEqual,\r\n    updatePanesCanvases\r\n} from \"../../viz/core/utils\";\r\nimport rangeDataCalculator from \"../../viz/series/helpers/range_data_calculator\";\r\nimport {\r\n    Range\r\n} from \"../../viz/translators/range\";\r\nimport {\r\n    prepareSegmentRectPoints\r\n} from \"../../viz/utils\";\r\nimport {\r\n    AdvancedChart\r\n} from \"./chart_components/m_advanced_chart\";\r\nimport {\r\n    overlapping\r\n} from \"./chart_components/m_base_chart\";\r\nconst DEFAULT_PANE_NAME = \"default\";\r\nconst VISUAL_RANGE = \"VISUAL_RANGE\";\r\nconst DEFAULT_PANES = [{\r\n    name: \"default\",\r\n    border: {}\r\n}];\r\nconst DISCRETE = \"discrete\";\r\nconst {\r\n    isArray: isArray\r\n} = Array;\r\n\r\nfunction getFirstAxisNameForPane(axes, paneName, defaultPane) {\r\n    let result;\r\n    for (let i = 0; i < axes.length; i += 1) {\r\n        if (axes[i].pane === paneName || void 0 === axes[i].pane && paneName === defaultPane) {\r\n            result = axes[i].name;\r\n            break\r\n        }\r\n    }\r\n    if (!result) {\r\n        result = axes[0].name\r\n    }\r\n    return result\r\n}\r\n\r\nfunction changeVisibilityAxisGrids(axis, gridVisibility, minorGridVisibility) {\r\n    const gridOpt = axis.getOptions().grid;\r\n    const minorGridOpt = axis.getOptions().minorGrid;\r\n    gridOpt.visible = gridVisibility;\r\n    minorGridOpt && (minorGridOpt.visible = minorGridVisibility)\r\n}\r\n\r\nfunction hideGridsOnNonFirstValueAxisForPane(axesForPane) {\r\n    let axisShown = false;\r\n    const hiddenStubAxis = [];\r\n    const minorGridVisibility = axesForPane.some((axis => {\r\n        const minorGridOptions = axis.getOptions().minorGrid;\r\n        return null === minorGridOptions || void 0 === minorGridOptions ? void 0 : minorGridOptions.visible\r\n    }));\r\n    const gridVisibility = axesForPane.some((axis => {\r\n        const gridOptions = axis.getOptions().grid;\r\n        return null === gridOptions || void 0 === gridOptions ? void 0 : gridOptions.visible\r\n    }));\r\n    if (axesForPane.length > 1) {\r\n        axesForPane.forEach((axis => {\r\n            const gridOpt = axis.getOptions().grid;\r\n            if (axisShown) {\r\n                changeVisibilityAxisGrids(axis, false, false)\r\n            } else if (null !== gridOpt && void 0 !== gridOpt && gridOpt.visible) {\r\n                if (axis.getTranslator().getBusinessRange().isEmpty()) {\r\n                    changeVisibilityAxisGrids(axis, false, false);\r\n                    hiddenStubAxis.push(axis)\r\n                } else {\r\n                    axisShown = true;\r\n                    changeVisibilityAxisGrids(axis, gridVisibility, minorGridVisibility)\r\n                }\r\n            }\r\n        }));\r\n        if (!axisShown && hiddenStubAxis.length) {\r\n            changeVisibilityAxisGrids(hiddenStubAxis[0], gridVisibility, minorGridVisibility)\r\n        }\r\n    }\r\n}\r\n\r\nfunction findAxisOptions(valueAxes, valueAxesOptions, axisName) {\r\n    let result;\r\n    let axInd;\r\n    for (axInd = 0; axInd < valueAxesOptions.length; axInd += 1) {\r\n        if (valueAxesOptions[axInd].name === axisName) {\r\n            result = valueAxesOptions[axInd];\r\n            result.priority = axInd;\r\n            break\r\n        }\r\n    }\r\n    if (!result) {\r\n        for (axInd = 0; axInd < valueAxes.length; axInd += 1) {\r\n            if (valueAxes[axInd].name === axisName) {\r\n                result = valueAxes[axInd].getOptions();\r\n                result.priority = valueAxes[axInd].priority;\r\n                break\r\n            }\r\n        }\r\n    }\r\n    return result\r\n}\r\n\r\nfunction findAxis(paneName, axisName, axes) {\r\n    const axisByName = axes.find((axis => axis.name === axisName && axis.pane === paneName));\r\n    if (axisByName) {\r\n        return axisByName\r\n    }\r\n    if (paneName) {\r\n        return findAxis(void 0, axisName, axes)\r\n    }\r\n}\r\n\r\nfunction compareAxes(a, b) {\r\n    return a.priority - b.priority\r\n}\r\n\r\nfunction doesPaneExist(panes, paneName) {\r\n    let found = false;\r\n    _each(panes, ((_, pane) => {\r\n        if (pane.name === paneName) {\r\n            found = true;\r\n            return false\r\n        }\r\n        return\r\n    }));\r\n    return found\r\n}\r\n\r\nfunction accumulate(field, src1, src2, auxSpacing) {\r\n    const val1 = src1[field] || 0;\r\n    const val2 = src2[field] || 0;\r\n    return val1 + val2 + (val1 && val2 ? auxSpacing : 0)\r\n}\r\n\r\nfunction pickMax(field, src1, src2) {\r\n    return pickMaxValue(src1[field], src2[field])\r\n}\r\n\r\nfunction pickMaxValue(val1, val2) {\r\n    return Math.max(val1 || 0, val2 || 0)\r\n}\r\n\r\nfunction getAxisMargins(axis) {\r\n    return axis.getMargins()\r\n}\r\n\r\nfunction getHorizontalAxesMargins(axes, getMarginsFunc) {\r\n    return axes.reduce(((margins, axis) => {\r\n        var _axis$getOrthogonalAx;\r\n        const axisMargins = getMarginsFunc(axis);\r\n        const paneMargins = margins.panes[axis.pane] = margins.panes[axis.pane] || {};\r\n        const spacing = axis.getMultipleAxesSpacing();\r\n        paneMargins.top = accumulate(\"top\", paneMargins, axisMargins, spacing);\r\n        paneMargins.bottom = accumulate(\"bottom\", paneMargins, axisMargins, spacing);\r\n        paneMargins.left = pickMax(\"left\", paneMargins, axisMargins);\r\n        paneMargins.right = pickMax(\"right\", paneMargins, axisMargins);\r\n        margins.top = pickMax(\"top\", paneMargins, margins);\r\n        margins.bottom = pickMax(\"bottom\", paneMargins, margins);\r\n        margins.left = pickMax(\"left\", paneMargins, margins);\r\n        margins.right = pickMax(\"right\", paneMargins, margins);\r\n        const orthogonalAxis = null === (_axis$getOrthogonalAx = axis.getOrthogonalAxis) || void 0 === _axis$getOrthogonalAx ? void 0 : _axis$getOrthogonalAx.call(axis);\r\n        const shouldResetPositionMargin = (null === orthogonalAxis || void 0 === orthogonalAxis ? void 0 : orthogonalAxis.customPositionIsAvailable()) && (!axis.customPositionIsBoundaryOrthogonalAxis() || !orthogonalAxis.customPositionEqualsToPredefined());\r\n        if (shouldResetPositionMargin) {\r\n            margins[orthogonalAxis.getResolvedBoundaryPosition()] = 0\r\n        }\r\n        return margins\r\n    }), {\r\n        panes: {}\r\n    })\r\n}\r\n\r\nfunction getVerticalAxesMargins(axes) {\r\n    return axes.reduce(((margins, axis) => {\r\n        const axisMargins = axis.getMargins();\r\n        const paneMargins = margins.panes[axis.pane] = margins.panes[axis.pane] || {};\r\n        const spacing = axis.getMultipleAxesSpacing();\r\n        paneMargins.top = pickMax(\"top\", paneMargins, axisMargins);\r\n        paneMargins.bottom = pickMax(\"bottom\", paneMargins, axisMargins);\r\n        paneMargins.left = accumulate(\"left\", paneMargins, axisMargins, spacing);\r\n        paneMargins.right = accumulate(\"right\", paneMargins, axisMargins, spacing);\r\n        margins.top = pickMax(\"top\", paneMargins, margins);\r\n        margins.bottom = pickMax(\"bottom\", paneMargins, margins);\r\n        margins.left = pickMax(\"left\", paneMargins, margins);\r\n        margins.right = pickMax(\"right\", paneMargins, margins);\r\n        return margins\r\n    }), {\r\n        panes: {}\r\n    })\r\n}\r\n\r\nfunction performActionOnAxes(axes, action, actionArgument1, actionArgument2, actionArgument3) {\r\n    axes.forEach((axis => {\r\n        axis[action](null === actionArgument1 || void 0 === actionArgument1 ? void 0 : actionArgument1[axis.pane], (null === actionArgument2 || void 0 === actionArgument2 ? void 0 : actionArgument2[axis.pane]) || actionArgument2, actionArgument3)\r\n    }))\r\n}\r\n\r\nfunction shrinkCanvases(isRotated, canvases, sizes, verticalMargins, horizontalMargins) {\r\n    function getMargin(side, margins, pane) {\r\n        const m = !(isRotated ? [\"left\", \"right\"] : [\"top\", \"bottom\"]).includes(side) ? margins : margins.panes[pane] || {};\r\n        return m[side]\r\n    }\r\n\r\n    function getMaxMargin(side, margins1, margins2, pane) {\r\n        return pickMaxValue(getMargin(side, margins1, pane), getMargin(side, margins2, pane))\r\n    }\r\n    const getOriginalField = field => `original${field[0].toUpperCase()}${field.slice(1)}`;\r\n\r\n    function shrink(canvases, paneNames, sizeField, startMargin, endMargin, oppositeMargins) {\r\n        paneNames = paneNames.sort(((p1, p2) => canvases[p2][startMargin] - canvases[p1][startMargin]));\r\n        paneNames.forEach((pane => {\r\n            const canvas = canvases[pane];\r\n            oppositeMargins.forEach((margin => {\r\n                canvas[margin] = canvas[getOriginalField(margin)] + getMaxMargin(margin, verticalMargins, horizontalMargins, pane)\r\n            }))\r\n        }));\r\n        const firstPane = canvases[paneNames[0]];\r\n        const initialEmptySpace = firstPane[sizeField] - firstPane[getOriginalField(endMargin)] - canvases[paneNames.at(-1)][getOriginalField(startMargin)];\r\n        let emptySpace = paneNames.reduce(((space, paneName) => {\r\n            const maxStartMargin = getMaxMargin(startMargin, verticalMargins, horizontalMargins, paneName);\r\n            const maxEndMargin = getMaxMargin(endMargin, verticalMargins, horizontalMargins, paneName);\r\n            return space - maxStartMargin - maxEndMargin\r\n        }), initialEmptySpace) - PANE_PADDING * (paneNames.length - 1);\r\n        emptySpace -= Object.keys(sizes).reduce(((prev, key) => {\r\n            const currentHeight = !isRelativeHeightPane(sizes[key]) ? sizes[key].height : 0;\r\n            return prev + currentHeight\r\n        }), 0);\r\n        const initialOffset = firstPane[sizeField] - firstPane[getOriginalField(endMargin)] - (emptySpace < 0 ? emptySpace : 0);\r\n        paneNames.reduce(((offset, pane) => {\r\n            const canvas = canvases[pane];\r\n            const paneSize = sizes[pane];\r\n            offset -= getMaxMargin(endMargin, verticalMargins, horizontalMargins, pane);\r\n            canvas[endMargin] = firstPane[sizeField] - offset;\r\n            offset -= !isRelativeHeightPane(paneSize) ? paneSize.height : Math.floor(emptySpace * paneSize.height);\r\n            canvas[startMargin] = offset;\r\n            offset -= getMaxMargin(startMargin, verticalMargins, horizontalMargins, pane) + PANE_PADDING;\r\n            return offset\r\n        }), initialOffset)\r\n    }\r\n    const paneNames = Object.keys(canvases);\r\n    if (!isRotated) {\r\n        shrink(canvases, paneNames, \"height\", \"top\", \"bottom\", [\"left\", \"right\"])\r\n    } else {\r\n        shrink(canvases, paneNames, \"width\", \"left\", \"right\", [\"top\", \"bottom\"])\r\n    }\r\n    return canvases\r\n}\r\n\r\nfunction drawAxesWithTicks(axes, condition, canvases, panesBorderOptions) {\r\n    if (condition) {\r\n        performActionOnAxes(axes, \"createTicks\", canvases);\r\n        multiAxesSynchronizer.synchronize(axes)\r\n    }\r\n    performActionOnAxes(axes, \"draw\", !condition && canvases, panesBorderOptions)\r\n}\r\n\r\nfunction shiftAxis(side1, side2) {\r\n    const shifts = {};\r\n    return function(axis) {\r\n        if (!axis.customPositionIsAvailable() || axis.customPositionEqualsToPredefined()) {\r\n            const shift = shifts[axis.pane] = shifts[axis.pane] || {\r\n                top: 0,\r\n                left: 0,\r\n                bottom: 0,\r\n                right: 0\r\n            };\r\n            const spacing = axis.getMultipleAxesSpacing();\r\n            const margins = axis.getMargins();\r\n            axis.shift(shift);\r\n            shift[side1] = accumulate(side1, shift, margins, spacing);\r\n            shift[side2] = accumulate(side2, shift, margins, spacing)\r\n        } else {\r\n            axis.shift({\r\n                top: 0,\r\n                left: 0,\r\n                bottom: 0,\r\n                right: 0\r\n            })\r\n        }\r\n    }\r\n}\r\n\r\nfunction getCommonSize(side, margins) {\r\n    let size = 0;\r\n    let paneMargins;\r\n    Object.keys(margins.panes).forEach((pane => {\r\n        paneMargins = margins.panes[pane];\r\n        size += \"height\" === side ? paneMargins.top + paneMargins.bottom : paneMargins.left + paneMargins.right\r\n    }));\r\n    return size\r\n}\r\n\r\nfunction checkUsedSpace(sizeShortage, side, axes, getMarginFunc) {\r\n    let size = 0;\r\n    if (sizeShortage[side] > 0) {\r\n        size = getCommonSize(side, getMarginFunc(axes, getAxisMargins));\r\n        performActionOnAxes(axes, \"hideTitle\");\r\n        sizeShortage[side] -= size - getCommonSize(side, getMarginFunc(axes, getAxisMargins))\r\n    }\r\n    if (sizeShortage[side] > 0) {\r\n        performActionOnAxes(axes, \"hideOuterElements\")\r\n    }\r\n}\r\n\r\nfunction axisAnimationEnabled(drawOptions, pointsToAnimation) {\r\n    const pointsCount = pointsToAnimation.reduce(((sum, count) => sum + count), 0) / pointsToAnimation.length;\r\n    return drawOptions.animate && pointsCount <= drawOptions.animationPointsLimit\r\n}\r\n\r\nfunction collectMarkersInfoBySeries(allSeries, filteredSeries, argAxis) {\r\n    const series = [];\r\n    const overloadedSeries = {};\r\n    const argVisualRange = argAxis.visualRange();\r\n    const argTranslator = argAxis.getTranslator();\r\n    const argViewPortFilter = rangeDataCalculator.getViewPortFilter(argVisualRange || {});\r\n    filteredSeries.forEach((s => {\r\n        const valAxis = s.getValueAxis();\r\n        const valVisualRange = valAxis.getCanvasRange();\r\n        const valTranslator = valAxis.getTranslator();\r\n        const seriesIndex = allSeries.indexOf(s);\r\n        const valViewPortFilter = rangeDataCalculator.getViewPortFilter(valVisualRange || {});\r\n        overloadedSeries[seriesIndex] = {};\r\n        filteredSeries.forEach((sr => {\r\n            overloadedSeries[seriesIndex][allSeries.indexOf(sr)] = 0\r\n        }));\r\n        const seriesPoints = [];\r\n        const pointsInViewport = s.getPoints().filter((p => p.getOptions().visible && argViewPortFilter(p.argument) && (valViewPortFilter(p.getMinValue(true)) || valViewPortFilter(p.getMaxValue(true)))));\r\n        pointsInViewport.forEach((p => {\r\n            const tp = {\r\n                seriesIndex: seriesIndex,\r\n                argument: p.argument,\r\n                value: p.getMaxValue(true),\r\n                size: p.bubbleSize || p.getOptions().size,\r\n                x: void 0,\r\n                y: void 0\r\n            };\r\n            if (p.getMinValue(true) !== p.getMaxValue(true)) {\r\n                const mp = _extend({}, tp);\r\n                mp.value = p.getMinValue(true);\r\n                mp.x = argTranslator.to(mp.argument, 1);\r\n                mp.y = valTranslator.to(mp.value, 1);\r\n                seriesPoints.push(mp)\r\n            }\r\n            tp.x = argTranslator.to(tp.argument, 1);\r\n            tp.y = valTranslator.to(tp.value, 1);\r\n            seriesPoints.push(tp)\r\n        }));\r\n        overloadedSeries[seriesIndex].pointsCount = seriesPoints.length;\r\n        overloadedSeries[seriesIndex].total = 0;\r\n        overloadedSeries[seriesIndex].continuousSeries = 0;\r\n        series.push({\r\n            name: s.name,\r\n            index: seriesIndex,\r\n            points: seriesPoints\r\n        })\r\n    }));\r\n    return {\r\n        series: series,\r\n        overloadedSeries: overloadedSeries\r\n    }\r\n}\r\nconst isOverlay = (currentPoint, overlayPoint, pointRadius) => {\r\n    const pointHitsLeftBorder = overlayPoint.x - pointRadius <= currentPoint.x;\r\n    const pointHitsRightBorder = overlayPoint.x + pointRadius >= currentPoint.x;\r\n    const pointHitsTopBorder = overlayPoint.y - pointRadius <= currentPoint.y;\r\n    const pointHitsBottomBorder = overlayPoint.y + pointRadius >= currentPoint.y;\r\n    const isPointOverlappedHorizontally = pointHitsLeftBorder && pointHitsRightBorder;\r\n    const isPointOverlappedVertically = pointHitsTopBorder && pointHitsBottomBorder;\r\n    return isPointOverlappedHorizontally && isPointOverlappedVertically\r\n};\r\nconst isPointOverlapped = (currentPoint, points, skipSamePointsComparing) => {\r\n    const radiusPoint = currentPoint.getOptions().size / 2;\r\n    for (let i = 0; i < points.length; i += 1) {\r\n        if (!skipSamePointsComparing) {\r\n            const isXCoordinateSame = points[i].x === currentPoint.x;\r\n            const isYCoordinateSame = points[i].y === currentPoint.y;\r\n            if (isXCoordinateSame && isYCoordinateSame) {\r\n                continue\r\n            }\r\n        }\r\n        if (isOverlay(currentPoint, points[i], radiusPoint)) {\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n};\r\n\r\nfunction fastHidingPointMarkersByArea(canvas, markersInfo, series) {\r\n    const area = canvas.width * canvas.height;\r\n    const seriesPoints = markersInfo.series;\r\n    for (let i = seriesPoints.length - 1; i >= 0; i -= 1) {\r\n        const currentSeries = series.filter((s => s.name === seriesPoints[i].name))[0];\r\n        const {\r\n            points: points\r\n        } = seriesPoints[i];\r\n        const pointSize = points.length ? points[0].size : 0;\r\n        const pointsArea = pointSize * pointSize * points.length;\r\n        if (currentSeries.autoHidePointMarkersEnabled() && pointsArea >= area / seriesPoints.length) {\r\n            const {\r\n                index: index\r\n            } = seriesPoints[i];\r\n            currentSeries.autoHidePointMarkers = true;\r\n            seriesPoints.splice(i, 1);\r\n            series.splice(series.indexOf(currentSeries), 1);\r\n            markersInfo.overloadedSeries[index] = null\r\n        }\r\n    }\r\n}\r\n\r\nfunction updateMarkersInfo(points, overloadedSeries) {\r\n    let isContinuousSeries = false;\r\n    for (let i = 0; i < points.length - 1; i += 1) {\r\n        const curPoint = points[i];\r\n        const {\r\n            size: size\r\n        } = curPoint;\r\n        if (_isDefined(curPoint.x) && _isDefined(curPoint.y)) {\r\n            for (let j = i + 1; j < points.length; j += 1) {\r\n                const nextPoint = points[j];\r\n                const nextX = null === nextPoint || void 0 === nextPoint ? void 0 : nextPoint.x;\r\n                const nextY = null === nextPoint || void 0 === nextPoint ? void 0 : nextPoint.y;\r\n                if (!_isDefined(nextX) || Math.abs(curPoint.x - nextX) >= size) {\r\n                    isContinuousSeries = isContinuousSeries && j !== i + 1;\r\n                    break\r\n                } else {\r\n                    const distance = _isDefined(nextX) && _isDefined(nextY) && Math.sqrt((curPoint.x - nextX) ** 2 + (curPoint.y - nextY) ** 2);\r\n                    if (distance && distance < size) {\r\n                        overloadedSeries[curPoint.seriesIndex][nextPoint.seriesIndex] += 1;\r\n                        overloadedSeries[curPoint.seriesIndex].total += 1;\r\n                        if (!isContinuousSeries) {\r\n                            overloadedSeries[curPoint.seriesIndex].continuousSeries += 1;\r\n                            isContinuousSeries = true\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\nconst dxChart = AdvancedChart.inherit({\r\n    _themeSection: \"chart\",\r\n    _fontFields: [\"crosshair.label.font\"],\r\n    _initCore() {\r\n        this.paneAxis = {};\r\n        this.callBase()\r\n    },\r\n    _init() {\r\n        this._containerInitialHeight = hasWindow() ? getHeight(this._$element) : 0;\r\n        this.callBase()\r\n    },\r\n    _correctAxes() {\r\n        this._correctValueAxes(true)\r\n    },\r\n    _getExtraOptions: noop,\r\n    _createPanes() {\r\n        let panes = this.option(\"panes\");\r\n        let panesNameCounter = 0;\r\n        let defaultPane;\r\n        if (!panes || isArray(panes) && !panes.length) {\r\n            panes = DEFAULT_PANES\r\n        }\r\n        this.callBase();\r\n        defaultPane = this.option(\"defaultPane\");\r\n        panes = _extend(true, [], isArray(panes) ? panes : [panes]);\r\n        _each(panes, ((_, pane) => {\r\n            pane.name = !_isDefined(pane.name) ? \"default\" + panesNameCounter++ : pane.name\r\n        }));\r\n        if (_isDefined(defaultPane)) {\r\n            if (!doesPaneExist(panes, defaultPane)) {\r\n                this._incidentOccurred(\"W2101\", [defaultPane]);\r\n                defaultPane = panes[panes.length - 1].name\r\n            }\r\n        } else {\r\n            defaultPane = panes[panes.length - 1].name\r\n        }\r\n        this.defaultPane = defaultPane;\r\n        panes = this._isRotated() ? panes.reverse() : panes;\r\n        return panes\r\n    },\r\n    _getAxisRenderingOptions: () => ({\r\n        axisType: \"xyAxes\",\r\n        drawingType: \"linear\"\r\n    }),\r\n    _prepareAxisOptions(typeSelector, userOptions, rotated) {\r\n        return {\r\n            isHorizontal: \"argumentAxis\" === typeSelector !== rotated,\r\n            containerColor: this._themeManager.getOptions(\"containerBackgroundColor\")\r\n        }\r\n    },\r\n    _checkPaneName(seriesTheme) {\r\n        const paneList = _map(this.panes, (pane => pane.name));\r\n        seriesTheme.pane = seriesTheme.pane || this.defaultPane;\r\n        return paneList.includes(seriesTheme.pane)\r\n    },\r\n    _initCustomPositioningAxes() {\r\n        const argumentAxis = this.getArgumentAxis();\r\n        const valueAxisName = argumentAxis.getOptions().customPositionAxis;\r\n        const valueAxis = this._valueAxes.find((v => v.pane === argumentAxis.pane && (!valueAxisName || valueAxisName === v.name)));\r\n        this._valueAxes.forEach((v => {\r\n            if (argumentAxis !== v.getOrthogonalAxis()) {\r\n                v.getOrthogonalAxis = () => argumentAxis;\r\n                v.customPositionIsBoundaryOrthogonalAxis = () => argumentAxis.customPositionIsBoundary()\r\n            }\r\n        }));\r\n        if (_isDefined(valueAxis) && valueAxis !== argumentAxis.getOrthogonalAxis()) {\r\n            argumentAxis.getOrthogonalAxis = () => valueAxis;\r\n            argumentAxis.customPositionIsBoundaryOrthogonalAxis = () => this._valueAxes.some((v => v.customPositionIsBoundary()))\r\n        } else if (_isDefined(argumentAxis.getOrthogonalAxis()) && !_isDefined(valueAxis)) {\r\n            argumentAxis.getOrthogonalAxis = noop\r\n        }\r\n    },\r\n    _getAllAxes() {\r\n        return this._argumentAxes.concat(this._valueAxes)\r\n    },\r\n    _resetAxesAnimation(isFirstDrawing, isHorizontal) {\r\n        let axes;\r\n        if (_isDefined(isHorizontal)) {\r\n            axes = isHorizontal ^ this._isRotated() ? this._argumentAxes : this._valueAxes\r\n        } else {\r\n            axes = this._getAllAxes()\r\n        }\r\n        axes.forEach((a => {\r\n            a.resetApplyingAnimation(isFirstDrawing)\r\n        }))\r\n    },\r\n    _axesBoundaryPositioning() {\r\n        const allAxes = this._getAllAxes();\r\n        let boundaryStateChanged = false;\r\n        allAxes.forEach((a => {\r\n            if (!a.customPositionIsAvailable()) {\r\n                return\r\n            }\r\n            const prevBoundaryState = a.customPositionIsBoundary();\r\n            a._customBoundaryPosition = a.getCustomBoundaryPosition();\r\n            boundaryStateChanged = boundaryStateChanged || prevBoundaryState !== a.customPositionIsBoundary()\r\n        }));\r\n        return boundaryStateChanged\r\n    },\r\n    _getCrosshairMargins() {\r\n        const crosshairOptions = this._getCrosshairOptions() || {};\r\n        const crosshairEnabled = crosshairOptions.enabled;\r\n        const margins = getMargins();\r\n        const horizontalLabel = _extend(true, {}, crosshairOptions.label, crosshairOptions.horizontalLine.label);\r\n        const verticalLabel = _extend(true, {}, crosshairOptions.label, crosshairOptions.verticalLine.label);\r\n        return {\r\n            x: crosshairEnabled && crosshairOptions.horizontalLine.visible && horizontalLabel.visible ? margins.x : 0,\r\n            y: crosshairEnabled && crosshairOptions.verticalLine.visible && verticalLabel.visible ? margins.y : 0\r\n        }\r\n    },\r\n    _getValueAxis(paneName, axisName) {\r\n        const valueAxes = this._valueAxes;\r\n        const valueAxisOptions = this.option(\"valueAxis\") || {};\r\n        const valueAxesOptions = isArray(valueAxisOptions) ? valueAxisOptions : [valueAxisOptions];\r\n        const rotated = this._isRotated();\r\n        const crosshairMargins = this._getCrosshairMargins();\r\n        let axisOptions;\r\n        let axis;\r\n        axisName = axisName || getFirstAxisNameForPane(valueAxes, paneName, this.defaultPane);\r\n        axis = findAxis(paneName, axisName, valueAxes);\r\n        if (!axis) {\r\n            axisOptions = findAxisOptions(valueAxes, valueAxesOptions, axisName);\r\n            if (!axisOptions) {\r\n                this._incidentOccurred(\"W2102\", [axisName]);\r\n                axisOptions = {\r\n                    name: axisName,\r\n                    priority: valueAxes.length\r\n                }\r\n            }\r\n            axis = this._createAxis(false, this._populateAxesOptions(\"valueAxis\", axisOptions, {\r\n                pane: paneName,\r\n                name: axisName,\r\n                optionPath: isArray(valueAxisOptions) ? `valueAxis[${axisOptions.priority}]` : \"valueAxis\",\r\n                crosshairMargin: rotated ? crosshairMargins.y : crosshairMargins.x\r\n            }, rotated));\r\n            axis.applyVisualRangeSetter(this._getVisualRangeSetter());\r\n            valueAxes.push(axis)\r\n        }\r\n        axis.setPane(paneName);\r\n        return axis\r\n    },\r\n    _correctValueAxes(needHideGrids) {\r\n        const synchronizeMultiAxes = this._themeManager.getOptions(\"synchronizeMultiAxes\");\r\n        const valueAxes = this._valueAxes;\r\n        const paneWithAxis = {};\r\n        this.series.forEach((series => {\r\n            const axis = series.getValueAxis();\r\n            paneWithAxis[axis.pane] = true\r\n        }));\r\n        this.panes.forEach((pane => {\r\n            const paneName = pane.name;\r\n            if (!paneWithAxis[paneName]) {\r\n                this._getValueAxis(paneName)\r\n            }\r\n            if (needHideGrids && synchronizeMultiAxes) {\r\n                hideGridsOnNonFirstValueAxisForPane(valueAxes.filter((axis => axis.pane === paneName)))\r\n            }\r\n        }));\r\n        this._valueAxes = valueAxes.filter((axis => {\r\n            if (!axis.pane) {\r\n                axis.setPane(this.defaultPane)\r\n            }\r\n            const paneExists = doesPaneExist(this.panes, axis.pane);\r\n            if (!paneExists) {\r\n                axis.dispose();\r\n                axis = null\r\n            }\r\n            return paneExists\r\n        })).sort(compareAxes);\r\n        const defaultAxis = this.getValueAxis();\r\n        this._valueAxes.forEach((axis => {\r\n            const {\r\n                optionPath: optionPath\r\n            } = axis.getOptions();\r\n            if (optionPath) {\r\n                const axesWithSamePath = this._valueAxes.filter((a => a.getOptions().optionPath === optionPath));\r\n                if (axesWithSamePath.length > 1) {\r\n                    if (axesWithSamePath.some((a => a === defaultAxis))) {\r\n                        axesWithSamePath.forEach((a => {\r\n                            if (a !== defaultAxis) {\r\n                                a.getOptions().optionPath = null\r\n                            }\r\n                        }))\r\n                    } else {\r\n                        axesWithSamePath.forEach(((a, i) => {\r\n                            if (0 !== i) {\r\n                                a.getOptions().optionPath = null\r\n                            }\r\n                        }))\r\n                    }\r\n                }\r\n            }\r\n        }))\r\n    },\r\n    _getSeriesForPane(paneName) {\r\n        const paneSeries = [];\r\n        _each(this.series, ((_, oneSeries) => {\r\n            if (oneSeries.pane === paneName) {\r\n                paneSeries.push(oneSeries)\r\n            }\r\n        }));\r\n        return paneSeries\r\n    },\r\n    _createPanesBorderOptions() {\r\n        const commonBorderOptions = this._themeManager.getOptions(\"commonPaneSettings\").border;\r\n        const panesBorderOptions = {};\r\n        this.panes.forEach((pane => {\r\n            panesBorderOptions[pane.name] = _extend(true, {}, commonBorderOptions, pane.border)\r\n        }));\r\n        return panesBorderOptions\r\n    },\r\n    _createScrollBar() {\r\n        const scrollBarOptions = this._themeManager.getOptions(\"scrollBar\") || {};\r\n        const scrollBarGroup = this._scrollBarGroup;\r\n        if (scrollBarOptions.visible) {\r\n            scrollBarOptions.rotated = this._isRotated();\r\n            this._scrollBar = (this._scrollBar || new ScrollBar(this._renderer, scrollBarGroup)).update(scrollBarOptions)\r\n        } else {\r\n            var _this$_scrollBar;\r\n            scrollBarGroup.linkRemove();\r\n            null === (_this$_scrollBar = this._scrollBar) || void 0 === _this$_scrollBar || _this$_scrollBar.dispose();\r\n            this._scrollBar = null\r\n        }\r\n    },\r\n    _executeAppendAfterSeries(append) {\r\n        append()\r\n    },\r\n    _prepareToRender() {\r\n        const panesBorderOptions = this._createPanesBorderOptions();\r\n        this._createPanesBackground();\r\n        this._appendAxesGroups();\r\n        this._adjustViewport();\r\n        return panesBorderOptions\r\n    },\r\n    _adjustViewport() {\r\n        const adjustOnZoom = this._themeManager.getOptions(\"adjustOnZoom\");\r\n        if (!adjustOnZoom) {\r\n            return\r\n        }\r\n        this._valueAxes.forEach((axis => axis.adjust()))\r\n    },\r\n    _recreateSizeDependentObjects(isCanvasChanged) {\r\n        const series = this._getVisibleSeries();\r\n        const useAggregation = series.some((s => s.useAggregation()));\r\n        const zoomChanged = this._isZooming();\r\n        if (!useAggregation) {\r\n            return\r\n        }\r\n        this._argumentAxes.forEach((axis => {\r\n            axis.updateCanvas(this._canvas, true)\r\n        }));\r\n        series.forEach((series => {\r\n            if (series.useAggregation() && (isCanvasChanged || zoomChanged || !series._useAllAggregatedPoints)) {\r\n                series.createPoints()\r\n            }\r\n        }));\r\n        this._processSeriesFamilies()\r\n    },\r\n    _isZooming() {\r\n        const argumentAxis = this.getArgumentAxis();\r\n        if (!(null !== argumentAxis && void 0 !== argumentAxis && argumentAxis.getTranslator())) {\r\n            return false\r\n        }\r\n        const businessRange = argumentAxis.getTranslator().getBusinessRange();\r\n        const zoomRange = argumentAxis.getViewport();\r\n        let min = zoomRange ? zoomRange.min : 0;\r\n        let max = zoomRange ? zoomRange.max : 0;\r\n        if (\"logarithmic\" === businessRange.axisType) {\r\n            min = getLog(min, businessRange.base);\r\n            max = getLog(max, businessRange.base)\r\n        }\r\n        const viewportDistance = businessRange.axisType === DISCRETE ? getCategoriesInfo(businessRange.categories, min, max).categories.length : Math.abs(max - min);\r\n        let precision = getPrecision(viewportDistance);\r\n        precision = precision > 1 ? 10 ** (precision - 2) : 1;\r\n        const zoomChanged = Math.round((this._zoomLength - viewportDistance) * precision) / precision !== 0;\r\n        this._zoomLength = viewportDistance;\r\n        return zoomChanged\r\n    },\r\n    _handleSeriesDataUpdated() {\r\n        const viewport = new Range;\r\n        this.series.forEach((s => {\r\n            viewport.addRange(s.getArgumentRange())\r\n        }));\r\n        this._argumentAxes.forEach((axis => {\r\n            axis.updateCanvas(this._canvas, true);\r\n            axis.setBusinessRange(viewport, this._axesReinitialized)\r\n        }));\r\n        this.callBase()\r\n    },\r\n    _isLegendInside() {\r\n        return this._legend && \"inside\" === this._legend.getPosition()\r\n    },\r\n    _isRotated() {\r\n        return this._themeManager.getOptions(\"rotated\")\r\n    },\r\n    _getLayoutTargets() {\r\n        return this.panes\r\n    },\r\n    _applyClipRects(panesBorderOptions) {\r\n        this._drawPanesBorders(panesBorderOptions);\r\n        this._createClipRectsForPanes();\r\n        this._applyClipRectsForAxes();\r\n        this._fillPanesBackground()\r\n    },\r\n    _updateLegendPosition(drawOptions, legendHasInsidePosition) {\r\n        if (drawOptions.drawLegend && this._legend && legendHasInsidePosition) {\r\n            const {\r\n                panes: panes\r\n            } = this;\r\n            const newCanvas = _extend({}, panes[0].canvas);\r\n            const layoutManager = new LayoutManager;\r\n            newCanvas.right = panes[panes.length - 1].canvas.right;\r\n            newCanvas.bottom = panes[panes.length - 1].canvas.bottom;\r\n            layoutManager.layoutInsideLegend(this._legend, newCanvas)\r\n        }\r\n    },\r\n    _allowLegendInsidePosition: () => true,\r\n    _applyExtraSettings(series) {\r\n        const paneIndex = this._getPaneIndex(series.pane);\r\n        const panesClipRects = this._panesClipRects;\r\n        const wideClipRect = panesClipRects.wide[paneIndex];\r\n        series.setClippingParams(panesClipRects.base[paneIndex].id, null === wideClipRect || void 0 === wideClipRect ? void 0 : wideClipRect.id, this._getPaneBorderVisibility(paneIndex))\r\n    },\r\n    _updatePanesCanvases(drawOptions) {\r\n        if (!drawOptions.recreateCanvas) {\r\n            return\r\n        }\r\n        updatePanesCanvases(this.panes, this._canvas, this._isRotated())\r\n    },\r\n    _normalizePanesHeight() {\r\n        normalizePanesHeight(this.panes)\r\n    },\r\n    _renderScaleBreaks() {\r\n        this._valueAxes.concat(this._argumentAxes).forEach((axis => {\r\n            axis.drawScaleBreaks()\r\n        }))\r\n    },\r\n    _getArgFilter() {\r\n        return rangeDataCalculator.getViewPortFilter(this.getArgumentAxis().visualRange() || {})\r\n    },\r\n    _hidePointsForSingleSeriesIfNeeded(series) {\r\n        const seriesPoints = series.getPoints();\r\n        let overlappedPointsCount = 0;\r\n        for (let i = 0; i < seriesPoints.length; i += 1) {\r\n            const currentPoint = seriesPoints[i];\r\n            const overlappingPoints = seriesPoints.slice(i + 1);\r\n            overlappedPointsCount += Number(isPointOverlapped(currentPoint, overlappingPoints));\r\n            if (overlappedPointsCount > seriesPoints.length / 2) {\r\n                series.autoHidePointMarkers = true;\r\n                break\r\n            }\r\n        }\r\n    },\r\n    _applyAutoHidePointMarkers(filteredSeries) {\r\n        let overlappingPoints = [];\r\n        const overlappedPointsCalculator = (pointsCount, currentPoint) => pointsCount + isPointOverlapped(currentPoint, overlappingPoints, true);\r\n        for (let i = filteredSeries.length - 1; i >= 0; i -= 1) {\r\n            const currentSeries = filteredSeries[i];\r\n            if (!currentSeries.autoHidePointMarkersEnabled()) {\r\n                continue\r\n            }\r\n            currentSeries.autoHidePointMarkers = false;\r\n            this._hidePointsForSingleSeriesIfNeeded(currentSeries);\r\n            if (!currentSeries.autoHidePointMarkers) {\r\n                const seriesPoints = currentSeries.getPoints();\r\n                const overlappingPointsCount = seriesPoints.reduce(overlappedPointsCalculator, 0);\r\n                if (overlappingPointsCount < seriesPoints.length) {\r\n                    overlappingPoints = overlappingPoints.concat(seriesPoints)\r\n                } else {\r\n                    currentSeries.autoHidePointMarkers = true\r\n                }\r\n            }\r\n        }\r\n    },\r\n    _applyPointMarkersAutoHiding() {\r\n        const allSeries = this.series;\r\n        if (!this._themeManager.getOptions(\"autoHidePointMarkers\")) {\r\n            allSeries.forEach((s => {\r\n                s.autoHidePointMarkers = false\r\n            }));\r\n            return\r\n        }\r\n        this.panes.forEach((_ref => {\r\n            let {\r\n                borderCoords: borderCoords,\r\n                name: name\r\n            } = _ref;\r\n            const series = allSeries.filter((s => s.pane === name && s.usePointsToDefineAutoHiding()));\r\n            series.forEach((singleSeries => {\r\n                singleSeries.prepareCoordinatesForPoints()\r\n            }));\r\n            const argAxis = this.getArgumentAxis();\r\n            const markersInfo = collectMarkersInfoBySeries(allSeries, series, argAxis);\r\n            fastHidingPointMarkersByArea(borderCoords, markersInfo, series);\r\n            if (markersInfo.series.length) {\r\n                const argVisualRange = argAxis.visualRange();\r\n                const argAxisIsDiscrete = argAxis.getOptions().type === DISCRETE;\r\n                const sortingCallback = argAxisIsDiscrete ? (p1, p2) => argVisualRange.categories.indexOf(p1.argument) - argVisualRange.categories.indexOf(p2.argument) : (p1, p2) => p1.argument - p2.argument;\r\n                let points = [];\r\n                markersInfo.series.forEach((s => {\r\n                    points = points.concat(s.points)\r\n                }));\r\n                points.sort(sortingCallback);\r\n                updateMarkersInfo(points, markersInfo.overloadedSeries);\r\n                this._applyAutoHidePointMarkers(series)\r\n            }\r\n        }))\r\n    },\r\n    _renderAxes(drawOptions, panesBorderOptions) {\r\n        function calculateTitlesWidth(axes) {\r\n            return axes.map((axis => {\r\n                if (!axis.getTitle) {\r\n                    return 0\r\n                }\r\n                const title = axis.getTitle();\r\n                return title ? title.bBox.width : 0\r\n            }))\r\n        }\r\n        const rotated = this._isRotated();\r\n        const synchronizeMultiAxes = this._themeManager.getOptions(\"synchronizeMultiAxes\");\r\n        const scrollBar = this._scrollBar ? [this._scrollBar] : [];\r\n        const extendedArgAxes = this._isArgumentAxisBeforeScrollBar() ? this._argumentAxes.concat(scrollBar) : scrollBar.concat(this._argumentAxes);\r\n        const verticalAxes = rotated ? this._argumentAxes : this._valueAxes;\r\n        const verticalElements = rotated ? extendedArgAxes : this._valueAxes;\r\n        const horizontalAxes = rotated ? this._valueAxes : this._argumentAxes;\r\n        const horizontalElements = rotated ? this._valueAxes : extendedArgAxes;\r\n        const allAxes = verticalAxes.concat(horizontalAxes);\r\n        const allElements = allAxes.concat(scrollBar);\r\n        const verticalAxesFirstDrawing = verticalAxes.some((v => v.isFirstDrawing()));\r\n        this._normalizePanesHeight();\r\n        this._updatePanesCanvases(drawOptions);\r\n        let panesCanvases = this.panes.reduce(((canvases, pane) => {\r\n            canvases[pane.name] = _extend({}, pane.canvas);\r\n            return canvases\r\n        }), {});\r\n        const paneSizes = this.panes.reduce(((sizes, pane) => {\r\n            sizes[pane.name] = {\r\n                height: pane.height,\r\n                unit: pane.unit\r\n            };\r\n            return sizes\r\n        }), {});\r\n        const cleanPanesCanvases = _extend(true, {}, panesCanvases);\r\n        this._initCustomPositioningAxes();\r\n        const needCustomAdjustAxes = this._axesBoundaryPositioning();\r\n        if (!drawOptions.adjustAxes && !needCustomAdjustAxes) {\r\n            drawAxesWithTicks(verticalAxes, !rotated && synchronizeMultiAxes, panesCanvases, panesBorderOptions);\r\n            drawAxesWithTicks(horizontalAxes, rotated && synchronizeMultiAxes, panesCanvases, panesBorderOptions);\r\n            performActionOnAxes(allAxes, \"prepareAnimation\");\r\n            this._renderScaleBreaks();\r\n            horizontalAxes.forEach((a => a.resolveOverlappingForCustomPositioning(verticalAxes)));\r\n            verticalAxes.forEach((a => a.resolveOverlappingForCustomPositioning(horizontalAxes)));\r\n            return false\r\n        }\r\n        if (needCustomAdjustAxes) {\r\n            allAxes.forEach((a => a.customPositionIsAvailable() && a.shift({\r\n                top: 0,\r\n                left: 0,\r\n                bottom: 0,\r\n                right: 0\r\n            })))\r\n        }\r\n        if (this._scrollBar) {\r\n            this._scrollBar.setPane(this.panes)\r\n        }\r\n        let vAxesMargins = {\r\n            panes: {},\r\n            left: 0,\r\n            right: 0\r\n        };\r\n        let hAxesMargins = getHorizontalAxesMargins(horizontalElements, (axis => axis.estimateMargins(panesCanvases[axis.pane])));\r\n        panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, vAxesMargins, hAxesMargins);\r\n        const drawAxesAndSetCanvases = isHorizontal => {\r\n            const axes = isHorizontal ? horizontalAxes : verticalAxes;\r\n            const condition = (isHorizontal ? rotated : !rotated) && synchronizeMultiAxes;\r\n            drawAxesWithTicks(axes, condition, panesCanvases, panesBorderOptions);\r\n            if (isHorizontal) {\r\n                hAxesMargins = getHorizontalAxesMargins(horizontalElements, getAxisMargins)\r\n            } else {\r\n                vAxesMargins = getVerticalAxesMargins(verticalElements)\r\n            }\r\n            panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, vAxesMargins, hAxesMargins)\r\n        };\r\n        drawAxesAndSetCanvases(false);\r\n        drawAxesAndSetCanvases(true);\r\n        if (!this._changesApplying && this._estimateTickIntervals(verticalAxes, panesCanvases)) {\r\n            drawAxesAndSetCanvases(false)\r\n        }\r\n        let oldTitlesWidth = calculateTitlesWidth(verticalAxes);\r\n        const visibleSeries = this._getVisibleSeries();\r\n        const pointsToAnimation = this._getPointsToAnimation(visibleSeries);\r\n        const axesIsAnimated = axisAnimationEnabled(drawOptions, pointsToAnimation);\r\n        performActionOnAxes(allElements, \"updateSize\", panesCanvases, axesIsAnimated);\r\n        horizontalElements.forEach(shiftAxis(\"top\", \"bottom\"));\r\n        verticalElements.forEach(shiftAxis(\"left\", \"right\"));\r\n        this._renderScaleBreaks();\r\n        this.panes.forEach((pane => {\r\n            _extend(pane.canvas, panesCanvases[pane.name])\r\n        }));\r\n        this._valueAxes.forEach((axis => {\r\n            axis.setInitRange()\r\n        }));\r\n        verticalAxes.forEach(((axis, i) => {\r\n            var _axis$hasWrap;\r\n            if (null !== (_axis$hasWrap = axis.hasWrap) && void 0 !== _axis$hasWrap && _axis$hasWrap.call(axis)) {\r\n                const title = axis.getTitle();\r\n                const newTitleWidth = title ? title.bBox.width : 0;\r\n                const offset = newTitleWidth - oldTitlesWidth[i];\r\n                if (\"right\" === axis.getOptions().position) {\r\n                    vAxesMargins.right += offset\r\n                } else {\r\n                    vAxesMargins.left += offset;\r\n                    this.panes.forEach((_ref2 => {\r\n                        let {\r\n                            name: name\r\n                        } = _ref2;\r\n                        vAxesMargins.panes[name].left += offset\r\n                    }))\r\n                }\r\n                panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, vAxesMargins, hAxesMargins);\r\n                performActionOnAxes(allElements, \"updateSize\", panesCanvases, false, false);\r\n                oldTitlesWidth = calculateTitlesWidth(verticalAxes)\r\n            }\r\n        }));\r\n        if (verticalAxes.some((v => v.customPositionIsAvailable() && v.getCustomPosition() !== v._axisPosition))) {\r\n            axesIsAnimated && this._resetAxesAnimation(verticalAxesFirstDrawing, false);\r\n            performActionOnAxes(verticalAxes, \"updateSize\", panesCanvases, axesIsAnimated)\r\n        }\r\n        horizontalAxes.forEach((a => a.resolveOverlappingForCustomPositioning(verticalAxes)));\r\n        verticalAxes.forEach((a => a.resolveOverlappingForCustomPositioning(horizontalAxes)));\r\n        return cleanPanesCanvases\r\n    },\r\n    _getExtraTemplatesItems() {\r\n        const allAxes = (this._argumentAxes || []).concat(this._valueAxes || []);\r\n        const elements = this._collectTemplatesFromItems(allAxes);\r\n        return {\r\n            items: elements.items,\r\n            groups: elements.groups,\r\n            launchRequest() {\r\n                allAxes.forEach((a => {\r\n                    a.setRenderedState(true)\r\n                }))\r\n            },\r\n            doneRequest() {\r\n                allAxes.forEach((a => {\r\n                    a.setRenderedState(false)\r\n                }))\r\n            }\r\n        }\r\n    },\r\n    _estimateTickIntervals: (axes, canvases) => axes.some((axis => axis.estimateTickInterval(canvases[axis.pane]))),\r\n    checkForMoreSpaceForPanesCanvas() {\r\n        const rotated = this._isRotated();\r\n        const panesAreCustomSized = this.panes.filter((p => p.unit)).length === this.panes.length;\r\n        let needSpace = false;\r\n        if (panesAreCustomSized) {\r\n            let needHorizontalSpace = 0;\r\n            let needVerticalSpace = 0;\r\n            if (rotated) {\r\n                const argAxisRightMargin = this.getArgumentAxis().getMargins().right;\r\n                const rightPanesIndent = Math.min(...this.panes.map((p => p.canvas.right)));\r\n                needHorizontalSpace = this._canvas.right + argAxisRightMargin - rightPanesIndent\r\n            } else {\r\n                const argAxisBottomMargin = this.getArgumentAxis().getMargins().bottom;\r\n                const bottomPanesIndent = Math.min(...this.panes.map((p => p.canvas.bottom)));\r\n                needVerticalSpace = this._canvas.bottom + argAxisBottomMargin - bottomPanesIndent\r\n            }\r\n            needSpace = needHorizontalSpace > 0 || needVerticalSpace > 0 ? {\r\n                width: needHorizontalSpace,\r\n                height: needVerticalSpace\r\n            } : false;\r\n            if (0 !== needVerticalSpace) {\r\n                const realSize = this.getSize();\r\n                const customSize = this.option(\"size\");\r\n                const container = this._$element[0];\r\n                const containerHasStyledHeight = !!parseInt(container.style.height, 10) || 0 !== this._containerInitialHeight;\r\n                if (!rotated && !(null !== customSize && void 0 !== customSize && customSize.height) && !containerHasStyledHeight) {\r\n                    this._forceResize(realSize.width, realSize.height + needVerticalSpace);\r\n                    needSpace = false\r\n                }\r\n            }\r\n        } else {\r\n            needSpace = this.layoutManager.needMoreSpaceForPanesCanvas(this._getLayoutTargets(), rotated, (pane => ({\r\n                width: rotated && !!pane.unit,\r\n                height: !rotated && !!pane.unit\r\n            })))\r\n        }\r\n        return needSpace\r\n    },\r\n    _forceResize(width, height) {\r\n        this._renderer.resize(width, height);\r\n        this._updateSize(true);\r\n        this._setContentSize();\r\n        this._preserveOriginalCanvas();\r\n        this._updateCanvasClipRect(this._canvas)\r\n    },\r\n    _shrinkAxes(sizeShortage, panesCanvases) {\r\n        if (!sizeShortage || !panesCanvases) {\r\n            return\r\n        }\r\n        this._renderer.stopAllAnimations(true);\r\n        const rotated = this._isRotated();\r\n        const scrollBar = this._scrollBar ? [this._scrollBar] : [];\r\n        const extendedArgAxes = this._isArgumentAxisBeforeScrollBar() ? this._argumentAxes.concat(scrollBar) : scrollBar.concat(this._argumentAxes);\r\n        const verticalAxes = rotated ? extendedArgAxes : this._valueAxes;\r\n        const horizontalAxes = rotated ? this._valueAxes : extendedArgAxes;\r\n        const allAxes = verticalAxes.concat(horizontalAxes);\r\n        if (sizeShortage.width || sizeShortage.height) {\r\n            checkUsedSpace(sizeShortage, \"height\", horizontalAxes, getHorizontalAxesMargins);\r\n            checkUsedSpace(sizeShortage, \"width\", verticalAxes, getVerticalAxesMargins);\r\n            performActionOnAxes(allAxes, \"updateSize\", panesCanvases);\r\n            const paneSizes = this.panes.reduce(((sizes, pane) => {\r\n                sizes[pane.name] = {\r\n                    height: pane.height,\r\n                    unit: pane.unit\r\n                };\r\n                return sizes\r\n            }), {});\r\n            panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, getVerticalAxesMargins(verticalAxes), getHorizontalAxesMargins(horizontalAxes, getAxisMargins));\r\n            performActionOnAxes(allAxes, \"updateSize\", panesCanvases);\r\n            horizontalAxes.forEach(shiftAxis(\"top\", \"bottom\"));\r\n            verticalAxes.forEach(shiftAxis(\"left\", \"right\"));\r\n            this.panes.forEach((pane => _extend(pane.canvas, panesCanvases[pane.name])))\r\n        }\r\n    },\r\n    _isArgumentAxisBeforeScrollBar() {\r\n        const argumentAxis = this.getArgumentAxis();\r\n        if (this._scrollBar) {\r\n            var _argumentAxis$getOpti;\r\n            const argAxisPosition = argumentAxis.getResolvedBoundaryPosition();\r\n            const argAxisLabelPosition = null === (_argumentAxis$getOpti = argumentAxis.getOptions().label) || void 0 === _argumentAxis$getOpti ? void 0 : _argumentAxis$getOpti.position;\r\n            const scrollBarPosition = this._scrollBar.getOptions().position;\r\n            return argumentAxis.hasNonBoundaryPosition() || scrollBarPosition === argAxisPosition && argAxisLabelPosition !== scrollBarPosition\r\n        }\r\n        return false\r\n    },\r\n    _getPanesParameters() {\r\n        const {\r\n            panes: panes\r\n        } = this;\r\n        const params = [];\r\n        for (let i = 0; i < panes.length; i += 1) {\r\n            if (this._getPaneBorderVisibility(i)) {\r\n                params.push({\r\n                    coords: panes[i].borderCoords,\r\n                    clipRect: this._panesClipRects.fixed[i]\r\n                })\r\n            }\r\n        }\r\n        return params\r\n    },\r\n    _createCrosshairCursor() {\r\n        const options = this._themeManager.getOptions(\"crosshair\") || {};\r\n        const argumentAxis = this.getArgumentAxis();\r\n        const axes = this._isRotated() ? [this._valueAxes, [argumentAxis]] : [\r\n            [argumentAxis], this._valueAxes\r\n        ];\r\n        const parameters = {\r\n            canvas: this._getCommonCanvas(),\r\n            panes: this._getPanesParameters(),\r\n            axes: axes\r\n        };\r\n        if (!(null !== options && void 0 !== options && options.enabled)) {\r\n            return\r\n        }\r\n        if (this._crosshair) {\r\n            this._crosshair.update(options, parameters)\r\n        } else {\r\n            this._crosshair = new Crosshair(this._renderer, options, parameters, this._crosshairCursorGroup)\r\n        }\r\n        this._crosshair.render()\r\n    },\r\n    _getCommonCanvas() {\r\n        let commonCanvas;\r\n        const {\r\n            panes: panes\r\n        } = this;\r\n        for (let i = 0; i < panes.length; i += 1) {\r\n            const {\r\n                canvas: canvas\r\n            } = panes[i];\r\n            if (!commonCanvas) {\r\n                commonCanvas = _extend({}, canvas)\r\n            } else {\r\n                commonCanvas.right = canvas.right;\r\n                commonCanvas.bottom = canvas.bottom\r\n            }\r\n        }\r\n        return commonCanvas\r\n    },\r\n    _createPanesBackground() {\r\n        const defaultBackgroundColor = this._themeManager.getOptions(\"commonPaneSettings\").backgroundColor;\r\n        const renderer = this._renderer;\r\n        const rects = [];\r\n        this._panesBackgroundGroup.clear();\r\n        for (let i = 0; i < this.panes.length; i += 1) {\r\n            const backgroundColor = this.panes[i].backgroundColor || defaultBackgroundColor;\r\n            if (!backgroundColor || \"none\" === backgroundColor) {\r\n                rects.push(null);\r\n                continue\r\n            }\r\n            const rect = renderer.rect(0, 0, 0, 0).attr({\r\n                fill: extractColor(backgroundColor),\r\n                \"stroke-width\": 0\r\n            }).append(this._panesBackgroundGroup);\r\n            rects.push(rect)\r\n        }\r\n        this.panesBackground = rects\r\n    },\r\n    _fillPanesBackground() {\r\n        _each(this.panes, ((i, pane) => {\r\n            const bc = pane.borderCoords;\r\n            if (null !== this.panesBackground[i]) {\r\n                this.panesBackground[i].attr({\r\n                    x: bc.left,\r\n                    y: bc.top,\r\n                    width: bc.width,\r\n                    height: bc.height\r\n                })\r\n            }\r\n        }))\r\n    },\r\n    _calcPaneBorderCoords(pane) {\r\n        const {\r\n            canvas: canvas\r\n        } = pane;\r\n        const bc = pane.borderCoords = pane.borderCoords || {};\r\n        bc.left = canvas.left;\r\n        bc.top = canvas.top;\r\n        bc.right = canvas.width - canvas.right;\r\n        bc.bottom = canvas.height - canvas.bottom;\r\n        bc.width = Math.max(bc.right - bc.left, 0);\r\n        bc.height = Math.max(bc.bottom - bc.top, 0)\r\n    },\r\n    _drawPanesBorders(panesBorderOptions) {\r\n        const rotated = this._isRotated();\r\n        this._panesBorderGroup.linkRemove().clear();\r\n        _each(this.panes, ((i, pane) => {\r\n            const borderOptions = panesBorderOptions[pane.name];\r\n            const attr = {\r\n                fill: \"none\",\r\n                stroke: borderOptions.color,\r\n                \"stroke-opacity\": borderOptions.opacity,\r\n                \"stroke-width\": borderOptions.width,\r\n                dashStyle: borderOptions.dashStyle,\r\n                \"stroke-linecap\": \"square\"\r\n            };\r\n            this._calcPaneBorderCoords(pane, rotated);\r\n            if (!borderOptions.visible) {\r\n                return\r\n            }\r\n            const bc = pane.borderCoords;\r\n            const segmentRectParams = prepareSegmentRectPoints(bc.left, bc.top, bc.width, bc.height, borderOptions);\r\n            this._renderer.path(segmentRectParams.points, segmentRectParams.pathType).attr(attr).append(this._panesBorderGroup)\r\n        }));\r\n        this._panesBorderGroup.linkAppend()\r\n    },\r\n    _createClipRect(clipArray, index, left, top, width, height) {\r\n        let clipRect = clipArray[index];\r\n        if (!clipRect) {\r\n            clipRect = this._renderer.clipRect(left, top, width, height);\r\n            clipArray[index] = clipRect\r\n        } else {\r\n            clipRect.attr({\r\n                x: left,\r\n                y: top,\r\n                width: width,\r\n                height: height\r\n            })\r\n        }\r\n    },\r\n    _createClipRectsForPanes() {\r\n        const canvas = this._canvas;\r\n        _each(this.panes, ((i, pane) => {\r\n            let needWideClipRect = false;\r\n            const bc = pane.borderCoords;\r\n            let {\r\n                left: left\r\n            } = bc;\r\n            let {\r\n                top: top\r\n            } = bc;\r\n            let {\r\n                width: width\r\n            } = bc;\r\n            let {\r\n                height: height\r\n            } = bc;\r\n            const panesClipRects = this._panesClipRects;\r\n            this._createClipRect(panesClipRects.fixed, i, left, top, width, height);\r\n            this._createClipRect(panesClipRects.base, i, left, top, width, height);\r\n            _each(this.series, ((_, series) => {\r\n                if (series.pane === pane.name && (series.isFinancialSeries() || series.areErrorBarsVisible())) {\r\n                    needWideClipRect = true\r\n                }\r\n            }));\r\n            if (needWideClipRect) {\r\n                if (this._isRotated()) {\r\n                    top = 0;\r\n                    height = canvas.height\r\n                } else {\r\n                    left = 0;\r\n                    width = canvas.width\r\n                }\r\n                this._createClipRect(panesClipRects.wide, i, left, top, width, height)\r\n            } else {\r\n                panesClipRects.wide[i] = null\r\n            }\r\n        }))\r\n    },\r\n    _applyClipRectsForAxes() {\r\n        const axes = this._getAllAxes();\r\n        const chartCanvasClipRectID = this._getCanvasClipRectID();\r\n        for (let i = 0; i < axes.length; i += 1) {\r\n            const elementsClipRectID = this._getElementsClipRectID(axes[i].pane);\r\n            axes[i].applyClipRects(elementsClipRectID, chartCanvasClipRectID)\r\n        }\r\n    },\r\n    _getPaneBorderVisibility(paneIndex) {\r\n        var _pane$border;\r\n        const commonPaneBorderVisible = this._themeManager.getOptions(\"commonPaneSettings\").border.visible;\r\n        const pane = this.panes[paneIndex];\r\n        const paneVisibility = null === pane || void 0 === pane || null === (_pane$border = pane.border) || void 0 === _pane$border ? void 0 : _pane$border.visible;\r\n        return void 0 === paneVisibility ? commonPaneBorderVisible : paneVisibility\r\n    },\r\n    _getCanvasForPane(paneName) {\r\n        var _this$panes$find;\r\n        return null === (_this$panes$find = this.panes.find((pane => pane.name === paneName))) || void 0 === _this$panes$find ? void 0 : _this$panes$find.canvas\r\n    },\r\n    _getTrackerSettings() {\r\n        return _extend(this.callBase(), {\r\n            chart: this,\r\n            rotated: this._isRotated(),\r\n            crosshair: this._getCrosshairOptions().enabled ? this._crosshair : null,\r\n            stickyHovering: this._themeManager.getOptions(\"stickyHovering\")\r\n        })\r\n    },\r\n    _resolveLabelOverlappingStack() {\r\n        const isRotated = this._isRotated();\r\n        const shiftDirection = isRotated ? (box, length) => ({\r\n            x: box.x - length,\r\n            y: box.y\r\n        }) : (box, length) => ({\r\n            x: box.x,\r\n            y: box.y - length\r\n        });\r\n        const processor = (a, b) => {\r\n            const coordPosition = isRotated ? 1 : 0;\r\n            const figureCenter1 = a.labels[0].getFigureCenter()[coordPosition];\r\n            const figureCenter12 = b.labels[0].getFigureCenter()[coordPosition];\r\n            if (figureCenter1 - figureCenter12 === 0) {\r\n                const translator = a.labels[0].getPoint().series.getValueAxis().getTranslator();\r\n                const direction = translator.isInverted() ? -1 : 1;\r\n                return (a.value() - b.value()) * direction\r\n            }\r\n            return 0\r\n        };\r\n        _each(this._getStackPoints(), ((_, stacks) => {\r\n            _each(stacks, ((_, points) => {\r\n                const isInverted = points[0].series.getValueAxis().getOptions().inverted;\r\n                overlapping.resolveLabelOverlappingInOneDirection(points, this._getCommonCanvas(), isRotated, isInverted, shiftDirection, processor)\r\n            }))\r\n        }))\r\n    },\r\n    _getStackPoints() {\r\n        const stackPoints = {};\r\n        const visibleSeries = this._getVisibleSeries();\r\n        _each(visibleSeries, ((_, singleSeries) => {\r\n            const points = singleSeries.getPoints();\r\n            const stackName = singleSeries.getStackName() || null;\r\n            _each(points, ((_, point) => {\r\n                const {\r\n                    argument: argument\r\n                } = point;\r\n                if (!stackPoints[argument]) {\r\n                    stackPoints[argument] = {}\r\n                }\r\n                if (!stackPoints[argument][stackName]) {\r\n                    stackPoints[argument][stackName] = []\r\n                }\r\n                stackPoints[argument][stackName].push(point)\r\n            }))\r\n        }));\r\n        return stackPoints\r\n    },\r\n    _getCrosshairOptions() {\r\n        return this._getOption(\"crosshair\")\r\n    },\r\n    zoomArgument(min, max) {\r\n        if (!this._initialized || !_isDefined(min) && !_isDefined(max)) {\r\n            return\r\n        }\r\n        this.getArgumentAxis().visualRange([min, max])\r\n    },\r\n    resetVisualRange() {\r\n        const axes = this._argumentAxes;\r\n        const nonVirtualArgumentAxis = this.getArgumentAxis();\r\n        axes.forEach((axis => {\r\n            axis.resetVisualRange(nonVirtualArgumentAxis !== axis);\r\n            this._applyCustomVisualRangeOption(axis)\r\n        }));\r\n        this.callBase()\r\n    },\r\n    getVisibleArgumentBounds() {\r\n        const translator = this._argumentAxes[0].getTranslator();\r\n        const range = translator.getBusinessRange();\r\n        const isDiscrete = range.axisType === DISCRETE;\r\n        const {\r\n            categories: categories\r\n        } = range;\r\n        return {\r\n            minVisible: isDiscrete ? range.minVisible || categories[0] : range.minVisible,\r\n            maxVisible: isDiscrete ? range.maxVisible || categories[categories.length - 1] : range.maxVisible\r\n        }\r\n    },\r\n    _change_FULL_RENDER() {\r\n        this.callBase();\r\n        if (this._changes.has(VISUAL_RANGE)) {\r\n            this._raiseZoomEndHandlers()\r\n        }\r\n    },\r\n    _getAxesForScaling() {\r\n        return [this.getArgumentAxis()].concat(this._valueAxes)\r\n    },\r\n    _applyVisualRangeByVirtualAxes(axis, range) {\r\n        if (axis.isArgumentAxis) {\r\n            if (axis !== this.getArgumentAxis()) {\r\n                return true\r\n            }\r\n            this._argumentAxes.filter((a => a !== axis)).forEach((a => a.visualRange(range, {\r\n                start: true,\r\n                end: true\r\n            })))\r\n        }\r\n        return false\r\n    },\r\n    _raiseZoomEndHandlers() {\r\n        this._argumentAxes.forEach((axis => axis.handleZoomEnd()));\r\n        this.callBase()\r\n    },\r\n    _setOptionsByReference() {\r\n        this.callBase();\r\n        _extend(this._optionsByReference, {\r\n            \"argumentAxis.visualRange\": true\r\n        })\r\n    },\r\n    option() {\r\n        const option = this.callBase(...arguments);\r\n        const valueAxis = this._options.silent(\"valueAxis\");\r\n        if (\"array\" === type(valueAxis)) {\r\n            for (let i = 0; i < valueAxis.length; i += 1) {\r\n                const optionPath = `valueAxis[${i}].visualRange`;\r\n                this._optionsByReference[optionPath] = true\r\n            }\r\n        }\r\n        return option\r\n    },\r\n    _notifyVisualRange() {\r\n        const argAxis = this._argumentAxes[0];\r\n        const argumentVisualRange = convertVisualRangeObject(argAxis.visualRange(), !isArray(this.option(\"argumentAxis.visualRange\")));\r\n        if (!argAxis.skipEventRising || !rangesAreEqual(argumentVisualRange, this.option(\"argumentAxis.visualRange\"))) {\r\n            this.option(\"argumentAxis.visualRange\", argumentVisualRange)\r\n        } else {\r\n            argAxis.skipEventRising = null\r\n        }\r\n        this.callBase()\r\n    }\r\n});\r\ndxChart.addPlugin(shutterZoom);\r\ndxChart.addPlugin(zoomAndPan);\r\ndxChart.addPlugin(plugins.core);\r\ndxChart.addPlugin(plugins.chart);\r\nregisterComponent(\"dxChart\", dxChart);\r\nexport default dxChart;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAIA;AAGA;AACA;AAGA;AACA;AACA;AAGA;AAYA;AACA;AAGA;AAAA;AAGA;AAGA;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,oBAAoB;AAC1B,MAAM,eAAe;AACrB,MAAM,gBAAgB;IAAC;QACnB,MAAM;QACN,QAAQ,CAAC;IACb;CAAE;AACF,MAAM,WAAW;AACjB,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;AAEJ,SAAS,wBAAwB,IAAI,EAAE,QAAQ,EAAE,WAAW;IACxD,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;QACrC,IAAI,IAAI,CAAC,EAAE,CAAC,IAAI,KAAK,YAAY,KAAK,MAAM,IAAI,CAAC,EAAE,CAAC,IAAI,IAAI,aAAa,aAAa;YAClF,SAAS,IAAI,CAAC,EAAE,CAAC,IAAI;YACrB;QACJ;IACJ;IACA,IAAI,CAAC,QAAQ;QACT,SAAS,IAAI,CAAC,EAAE,CAAC,IAAI;IACzB;IACA,OAAO;AACX;AAEA,SAAS,0BAA0B,IAAI,EAAE,cAAc,EAAE,mBAAmB;IACxE,MAAM,UAAU,KAAK,UAAU,GAAG,IAAI;IACtC,MAAM,eAAe,KAAK,UAAU,GAAG,SAAS;IAChD,QAAQ,OAAO,GAAG;IAClB,gBAAgB,CAAC,aAAa,OAAO,GAAG,mBAAmB;AAC/D;AAEA,SAAS,oCAAoC,WAAW;IACpD,IAAI,YAAY;IAChB,MAAM,iBAAiB,EAAE;IACzB,MAAM,sBAAsB,YAAY,IAAI,CAAE,CAAA;QAC1C,MAAM,mBAAmB,KAAK,UAAU,GAAG,SAAS;QACpD,OAAO,SAAS,oBAAoB,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,OAAO;IACvG;IACA,MAAM,iBAAiB,YAAY,IAAI,CAAE,CAAA;QACrC,MAAM,cAAc,KAAK,UAAU,GAAG,IAAI;QAC1C,OAAO,SAAS,eAAe,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,OAAO;IACxF;IACA,IAAI,YAAY,MAAM,GAAG,GAAG;QACxB,YAAY,OAAO,CAAE,CAAA;YACjB,MAAM,UAAU,KAAK,UAAU,GAAG,IAAI;YACtC,IAAI,WAAW;gBACX,0BAA0B,MAAM,OAAO;YAC3C,OAAO,IAAI,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,OAAO,EAAE;gBAClE,IAAI,KAAK,aAAa,GAAG,gBAAgB,GAAG,OAAO,IAAI;oBACnD,0BAA0B,MAAM,OAAO;oBACvC,eAAe,IAAI,CAAC;gBACxB,OAAO;oBACH,YAAY;oBACZ,0BAA0B,MAAM,gBAAgB;gBACpD;YACJ;QACJ;QACA,IAAI,CAAC,aAAa,eAAe,MAAM,EAAE;YACrC,0BAA0B,cAAc,CAAC,EAAE,EAAE,gBAAgB;QACjE;IACJ;AACJ;AAEA,SAAS,gBAAgB,SAAS,EAAE,gBAAgB,EAAE,QAAQ;IAC1D,IAAI;IACJ,IAAI;IACJ,IAAK,QAAQ,GAAG,QAAQ,iBAAiB,MAAM,EAAE,SAAS,EAAG;QACzD,IAAI,gBAAgB,CAAC,MAAM,CAAC,IAAI,KAAK,UAAU;YAC3C,SAAS,gBAAgB,CAAC,MAAM;YAChC,OAAO,QAAQ,GAAG;YAClB;QACJ;IACJ;IACA,IAAI,CAAC,QAAQ;QACT,IAAK,QAAQ,GAAG,QAAQ,UAAU,MAAM,EAAE,SAAS,EAAG;YAClD,IAAI,SAAS,CAAC,MAAM,CAAC,IAAI,KAAK,UAAU;gBACpC,SAAS,SAAS,CAAC,MAAM,CAAC,UAAU;gBACpC,OAAO,QAAQ,GAAG,SAAS,CAAC,MAAM,CAAC,QAAQ;gBAC3C;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,SAAS,QAAQ,EAAE,QAAQ,EAAE,IAAI;IACtC,MAAM,aAAa,KAAK,IAAI,CAAE,CAAA,OAAQ,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,KAAK;IAC9E,IAAI,YAAY;QACZ,OAAO;IACX;IACA,IAAI,UAAU;QACV,OAAO,SAAS,KAAK,GAAG,UAAU;IACtC;AACJ;AAEA,SAAS,YAAY,CAAC,EAAE,CAAC;IACrB,OAAO,EAAE,QAAQ,GAAG,EAAE,QAAQ;AAClC;AAEA,SAAS,cAAc,KAAK,EAAE,QAAQ;IAClC,IAAI,QAAQ;IACZ,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,OAAQ,CAAC,GAAG;QACd,IAAI,KAAK,IAAI,KAAK,UAAU;YACxB,QAAQ;YACR,OAAO;QACX;QACA;IACJ;IACA,OAAO;AACX;AAEA,SAAS,WAAW,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,UAAU;IAC7C,MAAM,OAAO,IAAI,CAAC,MAAM,IAAI;IAC5B,MAAM,OAAO,IAAI,CAAC,MAAM,IAAI;IAC5B,OAAO,OAAO,OAAO,CAAC,QAAQ,OAAO,aAAa,CAAC;AACvD;AAEA,SAAS,QAAQ,KAAK,EAAE,IAAI,EAAE,IAAI;IAC9B,OAAO,aAAa,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM;AAChD;AAEA,SAAS,aAAa,IAAI,EAAE,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,QAAQ,GAAG,QAAQ;AACvC;AAEA,SAAS,eAAe,IAAI;IACxB,OAAO,KAAK,UAAU;AAC1B;AAEA,SAAS,yBAAyB,IAAI,EAAE,cAAc;IAClD,OAAO,KAAK,MAAM,CAAE,CAAC,SAAS;QAC1B,IAAI;QACJ,MAAM,cAAc,eAAe;QACnC,MAAM,cAAc,QAAQ,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC;QAC5E,MAAM,UAAU,KAAK,sBAAsB;QAC3C,YAAY,GAAG,GAAG,WAAW,OAAO,aAAa,aAAa;QAC9D,YAAY,MAAM,GAAG,WAAW,UAAU,aAAa,aAAa;QACpE,YAAY,IAAI,GAAG,QAAQ,QAAQ,aAAa;QAChD,YAAY,KAAK,GAAG,QAAQ,SAAS,aAAa;QAClD,QAAQ,GAAG,GAAG,QAAQ,OAAO,aAAa;QAC1C,QAAQ,MAAM,GAAG,QAAQ,UAAU,aAAa;QAChD,QAAQ,IAAI,GAAG,QAAQ,QAAQ,aAAa;QAC5C,QAAQ,KAAK,GAAG,QAAQ,SAAS,aAAa;QAC9C,MAAM,iBAAiB,SAAS,CAAC,wBAAwB,KAAK,iBAAiB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,CAAC;QAC3J,MAAM,4BAA4B,CAAC,SAAS,kBAAkB,KAAK,MAAM,iBAAiB,KAAK,IAAI,eAAe,yBAAyB,EAAE,KAAK,CAAC,CAAC,KAAK,sCAAsC,MAAM,CAAC,eAAe,gCAAgC,EAAE;QACvP,IAAI,2BAA2B;YAC3B,OAAO,CAAC,eAAe,2BAA2B,GAAG,GAAG;QAC5D;QACA,OAAO;IACX,GAAI;QACA,OAAO,CAAC;IACZ;AACJ;AAEA,SAAS,uBAAuB,IAAI;IAChC,OAAO,KAAK,MAAM,CAAE,CAAC,SAAS;QAC1B,MAAM,cAAc,KAAK,UAAU;QACnC,MAAM,cAAc,QAAQ,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG,QAAQ,KAAK,CAAC,KAAK,IAAI,CAAC,IAAI,CAAC;QAC5E,MAAM,UAAU,KAAK,sBAAsB;QAC3C,YAAY,GAAG,GAAG,QAAQ,OAAO,aAAa;QAC9C,YAAY,MAAM,GAAG,QAAQ,UAAU,aAAa;QACpD,YAAY,IAAI,GAAG,WAAW,QAAQ,aAAa,aAAa;QAChE,YAAY,KAAK,GAAG,WAAW,SAAS,aAAa,aAAa;QAClE,QAAQ,GAAG,GAAG,QAAQ,OAAO,aAAa;QAC1C,QAAQ,MAAM,GAAG,QAAQ,UAAU,aAAa;QAChD,QAAQ,IAAI,GAAG,QAAQ,QAAQ,aAAa;QAC5C,QAAQ,KAAK,GAAG,QAAQ,SAAS,aAAa;QAC9C,OAAO;IACX,GAAI;QACA,OAAO,CAAC;IACZ;AACJ;AAEA,SAAS,oBAAoB,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,eAAe,EAAE,eAAe;IACxF,KAAK,OAAO,CAAE,CAAA;QACV,IAAI,CAAC,OAAO,CAAC,SAAS,mBAAmB,KAAK,MAAM,kBAAkB,KAAK,IAAI,eAAe,CAAC,KAAK,IAAI,CAAC,EAAE,CAAC,SAAS,mBAAmB,KAAK,MAAM,kBAAkB,KAAK,IAAI,eAAe,CAAC,KAAK,IAAI,CAAC,KAAK,iBAAiB;IAClO;AACJ;AAEA,SAAS,eAAe,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,eAAe,EAAE,iBAAiB;IAClF,SAAS,UAAU,IAAI,EAAE,OAAO,EAAE,IAAI;QAClC,MAAM,IAAI,CAAC,CAAC,YAAY;YAAC;YAAQ;SAAQ,GAAG;YAAC;YAAO;SAAS,EAAE,QAAQ,CAAC,QAAQ,UAAU,QAAQ,KAAK,CAAC,KAAK,IAAI,CAAC;QAClH,OAAO,CAAC,CAAC,KAAK;IAClB;IAEA,SAAS,aAAa,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,IAAI;QAChD,OAAO,aAAa,UAAU,MAAM,UAAU,OAAO,UAAU,MAAM,UAAU;IACnF;IACA,MAAM,mBAAmB,CAAA,QAAS,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,CAAC,WAAW,KAAK,MAAM,KAAK,CAAC,IAAI;IAEtF,SAAS,OAAO,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,eAAe;QACnF,YAAY,UAAU,IAAI,CAAE,CAAC,IAAI,KAAO,QAAQ,CAAC,GAAG,CAAC,YAAY,GAAG,QAAQ,CAAC,GAAG,CAAC,YAAY;QAC7F,UAAU,OAAO,CAAE,CAAA;YACf,MAAM,SAAS,QAAQ,CAAC,KAAK;YAC7B,gBAAgB,OAAO,CAAE,CAAA;gBACrB,MAAM,CAAC,OAAO,GAAG,MAAM,CAAC,iBAAiB,QAAQ,GAAG,aAAa,QAAQ,iBAAiB,mBAAmB;YACjH;QACJ;QACA,MAAM,YAAY,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;QACxC,MAAM,oBAAoB,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,iBAAiB,WAAW,GAAG,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC,GAAG,CAAC,iBAAiB,aAAa;QACnJ,IAAI,aAAa,UAAU,MAAM,CAAE,CAAC,OAAO;YACvC,MAAM,iBAAiB,aAAa,aAAa,iBAAiB,mBAAmB;YACrF,MAAM,eAAe,aAAa,WAAW,iBAAiB,mBAAmB;YACjF,OAAO,QAAQ,iBAAiB;QACpC,GAAI,qBAAqB,yJAAA,CAAA,eAAY,GAAG,CAAC,UAAU,MAAM,GAAG,CAAC;QAC7D,cAAc,OAAO,IAAI,CAAC,OAAO,MAAM,CAAE,CAAC,MAAM;YAC5C,MAAM,gBAAgB,CAAC,CAAA,GAAA,yJAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK,CAAC,IAAI,IAAI,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAC9E,OAAO,OAAO;QAClB,GAAI;QACJ,MAAM,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAS,CAAC,iBAAiB,WAAW,GAAG,CAAC,aAAa,IAAI,aAAa,CAAC;QACtH,UAAU,MAAM,CAAE,CAAC,QAAQ;YACvB,MAAM,SAAS,QAAQ,CAAC,KAAK;YAC7B,MAAM,WAAW,KAAK,CAAC,KAAK;YAC5B,UAAU,aAAa,WAAW,iBAAiB,mBAAmB;YACtE,MAAM,CAAC,UAAU,GAAG,SAAS,CAAC,UAAU,GAAG;YAC3C,UAAU,CAAC,CAAA,GAAA,yJAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY,SAAS,MAAM,GAAG,KAAK,KAAK,CAAC,aAAa,SAAS,MAAM;YACrG,MAAM,CAAC,YAAY,GAAG;YACtB,UAAU,aAAa,aAAa,iBAAiB,mBAAmB,QAAQ,yJAAA,CAAA,eAAY;YAC5F,OAAO;QACX,GAAI;IACR;IACA,MAAM,YAAY,OAAO,IAAI,CAAC;IAC9B,IAAI,CAAC,WAAW;QACZ,OAAO,UAAU,WAAW,UAAU,OAAO,UAAU;YAAC;YAAQ;SAAQ;IAC5E,OAAO;QACH,OAAO,UAAU,WAAW,SAAS,QAAQ,SAAS;YAAC;YAAO;SAAS;IAC3E;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,IAAI,EAAE,SAAS,EAAE,QAAQ,EAAE,kBAAkB;IACpE,IAAI,WAAW;QACX,oBAAoB,MAAM,eAAe;QACzC,uLAAA,CAAA,UAAqB,CAAC,WAAW,CAAC;IACtC;IACA,oBAAoB,MAAM,QAAQ,CAAC,aAAa,UAAU;AAC9D;AAEA,SAAS,UAAU,KAAK,EAAE,KAAK;IAC3B,MAAM,SAAS,CAAC;IAChB,OAAO,SAAS,IAAI;QAChB,IAAI,CAAC,KAAK,yBAAyB,MAAM,KAAK,gCAAgC,IAAI;YAC9E,MAAM,QAAQ,MAAM,CAAC,KAAK,IAAI,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI,CAAC,IAAI;gBACnD,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,OAAO;YACX;YACA,MAAM,UAAU,KAAK,sBAAsB;YAC3C,MAAM,UAAU,KAAK,UAAU;YAC/B,KAAK,KAAK,CAAC;YACX,KAAK,CAAC,MAAM,GAAG,WAAW,OAAO,OAAO,SAAS;YACjD,KAAK,CAAC,MAAM,GAAG,WAAW,OAAO,OAAO,SAAS;QACrD,OAAO;YACH,KAAK,KAAK,CAAC;gBACP,KAAK;gBACL,MAAM;gBACN,QAAQ;gBACR,OAAO;YACX;QACJ;IACJ;AACJ;AAEA,SAAS,cAAc,IAAI,EAAE,OAAO;IAChC,IAAI,OAAO;IACX,IAAI;IACJ,OAAO,IAAI,CAAC,QAAQ,KAAK,EAAE,OAAO,CAAE,CAAA;QAChC,cAAc,QAAQ,KAAK,CAAC,KAAK;QACjC,QAAQ,aAAa,OAAO,YAAY,GAAG,GAAG,YAAY,MAAM,GAAG,YAAY,IAAI,GAAG,YAAY,KAAK;IAC3G;IACA,OAAO;AACX;AAEA,SAAS,eAAe,YAAY,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa;IAC3D,IAAI,OAAO;IACX,IAAI,YAAY,CAAC,KAAK,GAAG,GAAG;QACxB,OAAO,cAAc,MAAM,cAAc,MAAM;QAC/C,oBAAoB,MAAM;QAC1B,YAAY,CAAC,KAAK,IAAI,OAAO,cAAc,MAAM,cAAc,MAAM;IACzE;IACA,IAAI,YAAY,CAAC,KAAK,GAAG,GAAG;QACxB,oBAAoB,MAAM;IAC9B;AACJ;AAEA,SAAS,qBAAqB,WAAW,EAAE,iBAAiB;IACxD,MAAM,cAAc,kBAAkB,MAAM,CAAE,CAAC,KAAK,QAAU,MAAM,OAAQ,KAAK,kBAAkB,MAAM;IACzG,OAAO,YAAY,OAAO,IAAI,eAAe,YAAY,oBAAoB;AACjF;AAEA,SAAS,2BAA2B,SAAS,EAAE,cAAc,EAAE,OAAO;IAClE,MAAM,SAAS,EAAE;IACjB,MAAM,mBAAmB,CAAC;IAC1B,MAAM,iBAAiB,QAAQ,WAAW;IAC1C,MAAM,gBAAgB,QAAQ,aAAa;IAC3C,MAAM,oBAAoB,sLAAA,CAAA,UAAmB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;IACnF,eAAe,OAAO,CAAE,CAAA;QACpB,MAAM,UAAU,EAAE,YAAY;QAC9B,MAAM,iBAAiB,QAAQ,cAAc;QAC7C,MAAM,gBAAgB,QAAQ,aAAa;QAC3C,MAAM,cAAc,UAAU,OAAO,CAAC;QACtC,MAAM,oBAAoB,sLAAA,CAAA,UAAmB,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;QACnF,gBAAgB,CAAC,YAAY,GAAG,CAAC;QACjC,eAAe,OAAO,CAAE,CAAA;YACpB,gBAAgB,CAAC,YAAY,CAAC,UAAU,OAAO,CAAC,IAAI,GAAG;QAC3D;QACA,MAAM,eAAe,EAAE;QACvB,MAAM,mBAAmB,EAAE,SAAS,GAAG,MAAM,CAAE,CAAA,IAAK,EAAE,UAAU,GAAG,OAAO,IAAI,kBAAkB,EAAE,QAAQ,KAAK,CAAC,kBAAkB,EAAE,WAAW,CAAC,UAAU,kBAAkB,EAAE,WAAW,CAAC,MAAM;QAChM,iBAAiB,OAAO,CAAE,CAAA;YACtB,MAAM,KAAK;gBACP,aAAa;gBACb,UAAU,EAAE,QAAQ;gBACpB,OAAO,EAAE,WAAW,CAAC;gBACrB,MAAM,EAAE,UAAU,IAAI,EAAE,UAAU,GAAG,IAAI;gBACzC,GAAG,KAAK;gBACR,GAAG,KAAK;YACZ;YACA,IAAI,EAAE,WAAW,CAAC,UAAU,EAAE,WAAW,CAAC,OAAO;gBAC7C,MAAM,KAAK,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,CAAC,GAAG;gBACvB,GAAG,KAAK,GAAG,EAAE,WAAW,CAAC;gBACzB,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,QAAQ,EAAE;gBACrC,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,KAAK,EAAE;gBAClC,aAAa,IAAI,CAAC;YACtB;YACA,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,QAAQ,EAAE;YACrC,GAAG,CAAC,GAAG,cAAc,EAAE,CAAC,GAAG,KAAK,EAAE;YAClC,aAAa,IAAI,CAAC;QACtB;QACA,gBAAgB,CAAC,YAAY,CAAC,WAAW,GAAG,aAAa,MAAM;QAC/D,gBAAgB,CAAC,YAAY,CAAC,KAAK,GAAG;QACtC,gBAAgB,CAAC,YAAY,CAAC,gBAAgB,GAAG;QACjD,OAAO,IAAI,CAAC;YACR,MAAM,EAAE,IAAI;YACZ,OAAO;YACP,QAAQ;QACZ;IACJ;IACA,OAAO;QACH,QAAQ;QACR,kBAAkB;IACtB;AACJ;AACA,MAAM,YAAY,CAAC,cAAc,cAAc;IAC3C,MAAM,sBAAsB,aAAa,CAAC,GAAG,eAAe,aAAa,CAAC;IAC1E,MAAM,uBAAuB,aAAa,CAAC,GAAG,eAAe,aAAa,CAAC;IAC3E,MAAM,qBAAqB,aAAa,CAAC,GAAG,eAAe,aAAa,CAAC;IACzE,MAAM,wBAAwB,aAAa,CAAC,GAAG,eAAe,aAAa,CAAC;IAC5E,MAAM,gCAAgC,uBAAuB;IAC7D,MAAM,8BAA8B,sBAAsB;IAC1D,OAAO,iCAAiC;AAC5C;AACA,MAAM,oBAAoB,CAAC,cAAc,QAAQ;IAC7C,MAAM,cAAc,aAAa,UAAU,GAAG,IAAI,GAAG;IACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;QACvC,IAAI,CAAC,yBAAyB;YAC1B,MAAM,oBAAoB,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,aAAa,CAAC;YACxD,MAAM,oBAAoB,MAAM,CAAC,EAAE,CAAC,CAAC,KAAK,aAAa,CAAC;YACxD,IAAI,qBAAqB,mBAAmB;gBACxC;YACJ;QACJ;QACA,IAAI,UAAU,cAAc,MAAM,CAAC,EAAE,EAAE,cAAc;YACjD,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,SAAS,6BAA6B,MAAM,EAAE,WAAW,EAAE,MAAM;IAC7D,MAAM,OAAO,OAAO,KAAK,GAAG,OAAO,MAAM;IACzC,MAAM,eAAe,YAAY,MAAM;IACvC,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAG;QAClD,MAAM,gBAAgB,OAAO,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,YAAY,CAAC,EAAE,CAAC,IAAI,CAAE,CAAC,EAAE;QAC9E,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG,YAAY,CAAC,EAAE;QACnB,MAAM,YAAY,OAAO,MAAM,GAAG,MAAM,CAAC,EAAE,CAAC,IAAI,GAAG;QACnD,MAAM,aAAa,YAAY,YAAY,OAAO,MAAM;QACxD,IAAI,cAAc,2BAA2B,MAAM,cAAc,OAAO,aAAa,MAAM,EAAE;YACzF,MAAM,EACF,OAAO,KAAK,EACf,GAAG,YAAY,CAAC,EAAE;YACnB,cAAc,oBAAoB,GAAG;YACrC,aAAa,MAAM,CAAC,GAAG;YACvB,OAAO,MAAM,CAAC,OAAO,OAAO,CAAC,gBAAgB;YAC7C,YAAY,gBAAgB,CAAC,MAAM,GAAG;QAC1C;IACJ;AACJ;AAEA,SAAS,kBAAkB,MAAM,EAAE,gBAAgB;IAC/C,IAAI,qBAAqB;IACzB,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,GAAG,GAAG,KAAK,EAAG;QAC3C,MAAM,WAAW,MAAM,CAAC,EAAE;QAC1B,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,SAAS,CAAC,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,SAAS,CAAC,GAAG;YAClD,IAAK,IAAI,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;gBAC3C,MAAM,YAAY,MAAM,CAAC,EAAE;gBAC3B,MAAM,QAAQ,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,CAAC;gBAC/E,MAAM,QAAQ,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,CAAC;gBAC/E,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,UAAU,KAAK,GAAG,CAAC,SAAS,CAAC,GAAG,UAAU,MAAM;oBAC5D,qBAAqB,sBAAsB,MAAM,IAAI;oBACrD;gBACJ,OAAO;oBACH,MAAM,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,UAAU,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,UAAU,KAAK,IAAI,CAAC,CAAC,SAAS,CAAC,GAAG,KAAK,KAAK,IAAI,CAAC,SAAS,CAAC,GAAG,KAAK,KAAK;oBACzH,IAAI,YAAY,WAAW,MAAM;wBAC7B,gBAAgB,CAAC,SAAS,WAAW,CAAC,CAAC,UAAU,WAAW,CAAC,IAAI;wBACjE,gBAAgB,CAAC,SAAS,WAAW,CAAC,CAAC,KAAK,IAAI;wBAChD,IAAI,CAAC,oBAAoB;4BACrB,gBAAgB,CAAC,SAAS,WAAW,CAAC,CAAC,gBAAgB,IAAI;4BAC3D,qBAAqB;wBACzB;oBACJ;gBACJ;YACJ;QACJ;IACJ;AACJ;AACA,MAAM,UAAU,iMAAA,CAAA,gBAAa,CAAC,OAAO,CAAC;IAClC,eAAe;IACf,aAAa;QAAC;KAAuB;IACrC;QACI,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,IAAI,CAAC,uBAAuB,GAAG,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,MAAM,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,SAAS,IAAI;QACzE,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IACA,kBAAkB,+KAAA,CAAA,OAAI;IACtB;QACI,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC;QACxB,IAAI,mBAAmB;QACvB,IAAI;QACJ,IAAI,CAAC,SAAS,QAAQ,UAAU,CAAC,MAAM,MAAM,EAAE;YAC3C,QAAQ;QACZ;QACA,IAAI,CAAC,QAAQ;QACb,cAAc,IAAI,CAAC,MAAM,CAAC;QAC1B,QAAQ,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,EAAE,EAAE,QAAQ,SAAS,QAAQ;YAAC;SAAM;QAC1D,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,OAAQ,CAAC,GAAG;YACd,KAAK,IAAI,GAAG,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,KAAK,IAAI,IAAI,YAAY,qBAAqB,KAAK,IAAI;QACnF;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,cAAc;YACzB,IAAI,CAAC,cAAc,OAAO,cAAc;gBACpC,IAAI,CAAC,iBAAiB,CAAC,SAAS;oBAAC;iBAAY;gBAC7C,cAAc,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI;YAC9C;QACJ,OAAO;YACH,cAAc,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,IAAI;QAC9C;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,QAAQ,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,KAAK;QAC9C,OAAO;IACX;IACA,0BAA0B,IAAM,CAAC;YAC7B,UAAU;YACV,aAAa;QACjB,CAAC;IACD,qBAAoB,YAAY,EAAE,WAAW,EAAE,OAAO;QAClD,OAAO;YACH,cAAc,mBAAmB,iBAAiB;YAClD,gBAAgB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAClD;IACJ;IACA,gBAAe,WAAW;QACtB,MAAM,WAAW,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,IAAI,CAAC,KAAK,EAAG,CAAA,OAAQ,KAAK,IAAI;QACpD,YAAY,IAAI,GAAG,YAAY,IAAI,IAAI,IAAI,CAAC,WAAW;QACvD,OAAO,SAAS,QAAQ,CAAC,YAAY,IAAI;IAC7C;IACA;QACI,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,MAAM,gBAAgB,aAAa,UAAU,GAAG,kBAAkB;QAClE,MAAM,YAAY,IAAI,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,aAAa,IAAI,IAAI,CAAC,CAAC,iBAAiB,kBAAkB,EAAE,IAAI;QACxH,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,IAAI,iBAAiB,EAAE,iBAAiB,IAAI;gBACxC,EAAE,iBAAiB,GAAG,IAAM;gBAC5B,EAAE,sCAAsC,GAAG,IAAM,aAAa,wBAAwB;YAC1F;QACJ;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,cAAc,cAAc,aAAa,iBAAiB,IAAI;YACzE,aAAa,iBAAiB,GAAG,IAAM;YACvC,aAAa,sCAAsC,GAAG,IAAM,IAAI,CAAC,UAAU,CAAC,IAAI,CAAE,CAAA,IAAK,EAAE,wBAAwB;QACrH,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,aAAa,iBAAiB,OAAO,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,YAAY;YAC/E,aAAa,iBAAiB,GAAG,+KAAA,CAAA,OAAI;QACzC;IACJ;IACA;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;IACpD;IACA,qBAAoB,cAAc,EAAE,YAAY;QAC5C,IAAI;QACJ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,eAAe;YAC1B,OAAO,eAAe,IAAI,CAAC,UAAU,KAAK,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU;QAClF,OAAO;YACH,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,KAAK,OAAO,CAAE,CAAA;YACV,EAAE,sBAAsB,CAAC;QAC7B;IACJ;IACA;QACI,MAAM,UAAU,IAAI,CAAC,WAAW;QAChC,IAAI,uBAAuB;QAC3B,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI,CAAC,EAAE,yBAAyB,IAAI;gBAChC;YACJ;YACA,MAAM,oBAAoB,EAAE,wBAAwB;YACpD,EAAE,uBAAuB,GAAG,EAAE,yBAAyB;YACvD,uBAAuB,wBAAwB,sBAAsB,EAAE,wBAAwB;QACnG;QACA,OAAO;IACX;IACA;QACI,MAAM,mBAAmB,IAAI,CAAC,oBAAoB,MAAM,CAAC;QACzD,MAAM,mBAAmB,iBAAiB,OAAO;QACjD,MAAM,UAAU,CAAA,GAAA,yKAAA,CAAA,aAAU,AAAD;QACzB,MAAM,kBAAkB,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG,iBAAiB,KAAK,EAAE,iBAAiB,cAAc,CAAC,KAAK;QACvG,MAAM,gBAAgB,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG,iBAAiB,KAAK,EAAE,iBAAiB,YAAY,CAAC,KAAK;QACnG,OAAO;YACH,GAAG,oBAAoB,iBAAiB,cAAc,CAAC,OAAO,IAAI,gBAAgB,OAAO,GAAG,QAAQ,CAAC,GAAG;YACxG,GAAG,oBAAoB,iBAAiB,YAAY,CAAC,OAAO,IAAI,cAAc,OAAO,GAAG,QAAQ,CAAC,GAAG;QACxG;IACJ;IACA,eAAc,QAAQ,EAAE,QAAQ;QAC5B,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC,gBAAgB,CAAC;QACtD,MAAM,mBAAmB,QAAQ,oBAAoB,mBAAmB;YAAC;SAAiB;QAC1F,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,mBAAmB,IAAI,CAAC,oBAAoB;QAClD,IAAI;QACJ,IAAI;QACJ,WAAW,YAAY,wBAAwB,WAAW,UAAU,IAAI,CAAC,WAAW;QACpF,OAAO,SAAS,UAAU,UAAU;QACpC,IAAI,CAAC,MAAM;YACP,cAAc,gBAAgB,WAAW,kBAAkB;YAC3D,IAAI,CAAC,aAAa;gBACd,IAAI,CAAC,iBAAiB,CAAC,SAAS;oBAAC;iBAAS;gBAC1C,cAAc;oBACV,MAAM;oBACN,UAAU,UAAU,MAAM;gBAC9B;YACJ;YACA,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,aAAa;gBAC/E,MAAM;gBACN,MAAM;gBACN,YAAY,QAAQ,oBAAoB,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,CAAC,CAAC,GAAG;gBAC/E,iBAAiB,UAAU,iBAAiB,CAAC,GAAG,iBAAiB,CAAC;YACtE,GAAG;YACH,KAAK,sBAAsB,CAAC,IAAI,CAAC,qBAAqB;YACtD,UAAU,IAAI,CAAC;QACnB;QACA,KAAK,OAAO,CAAC;QACb,OAAO;IACX;IACA,mBAAkB,aAAa;QAC3B,MAAM,uBAAuB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAC3D,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,MAAM,eAAe,CAAC;QACtB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,MAAM,OAAO,OAAO,YAAY;YAChC,YAAY,CAAC,KAAK,IAAI,CAAC,GAAG;QAC9B;QACA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;YAChB,MAAM,WAAW,KAAK,IAAI;YAC1B,IAAI,CAAC,YAAY,CAAC,SAAS,EAAE;gBACzB,IAAI,CAAC,aAAa,CAAC;YACvB;YACA,IAAI,iBAAiB,sBAAsB;gBACvC,oCAAoC,UAAU,MAAM,CAAE,CAAA,OAAQ,KAAK,IAAI,KAAK;YAChF;QACJ;QACA,IAAI,CAAC,UAAU,GAAG,UAAU,MAAM,CAAE,CAAA;YAChC,IAAI,CAAC,KAAK,IAAI,EAAE;gBACZ,KAAK,OAAO,CAAC,IAAI,CAAC,WAAW;YACjC;YACA,MAAM,aAAa,cAAc,IAAI,CAAC,KAAK,EAAE,KAAK,IAAI;YACtD,IAAI,CAAC,YAAY;gBACb,KAAK,OAAO;gBACZ,OAAO;YACX;YACA,OAAO;QACX,GAAI,IAAI,CAAC;QACT,MAAM,cAAc,IAAI,CAAC,YAAY;QACrC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,MAAM,EACF,YAAY,UAAU,EACzB,GAAG,KAAK,UAAU;YACnB,IAAI,YAAY;gBACZ,MAAM,mBAAmB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,UAAU,GAAG,UAAU,KAAK;gBACpF,IAAI,iBAAiB,MAAM,GAAG,GAAG;oBAC7B,IAAI,iBAAiB,IAAI,CAAE,CAAA,IAAK,MAAM,cAAe;wBACjD,iBAAiB,OAAO,CAAE,CAAA;4BACtB,IAAI,MAAM,aAAa;gCACnB,EAAE,UAAU,GAAG,UAAU,GAAG;4BAChC;wBACJ;oBACJ,OAAO;wBACH,iBAAiB,OAAO,CAAE,CAAC,GAAG;4BAC1B,IAAI,MAAM,GAAG;gCACT,EAAE,UAAU,GAAG,UAAU,GAAG;4BAChC;wBACJ;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,mBAAkB,QAAQ;QACtB,MAAM,aAAa,EAAE;QACrB,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAG,CAAC,GAAG;YACpB,IAAI,UAAU,IAAI,KAAK,UAAU;gBAC7B,WAAW,IAAI,CAAC;YACpB;QACJ;QACA,OAAO;IACX;IACA;QACI,MAAM,sBAAsB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,sBAAsB,MAAM;QACtF,MAAM,qBAAqB,CAAC;QAC5B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;YAChB,kBAAkB,CAAC,KAAK,IAAI,CAAC,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG,qBAAqB,KAAK,MAAM;QACtF;QACA,OAAO;IACX;IACA;QACI,MAAM,mBAAmB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC;QACxE,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,IAAI,iBAAiB,OAAO,EAAE;YAC1B,iBAAiB,OAAO,GAAG,IAAI,CAAC,UAAU;YAC1C,IAAI,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,0KAAA,CAAA,YAAS,CAAC,IAAI,CAAC,SAAS,EAAE,eAAe,EAAE,MAAM,CAAC;QAChG,OAAO;YACH,IAAI;YACJ,eAAe,UAAU;YACzB,SAAS,CAAC,mBAAmB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,OAAO;YACxG,IAAI,CAAC,UAAU,GAAG;QACtB;IACJ;IACA,2BAA0B,MAAM;QAC5B;IACJ;IACA;QACI,MAAM,qBAAqB,IAAI,CAAC,yBAAyB;QACzD,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,eAAe;QACpB,OAAO;IACX;IACA;QACI,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QACnD,IAAI,CAAC,cAAc;YACf;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,MAAM;IAChD;IACA,+BAA8B,eAAe;QACzC,MAAM,SAAS,IAAI,CAAC,iBAAiB;QACrC,MAAM,iBAAiB,OAAO,IAAI,CAAE,CAAA,IAAK,EAAE,cAAc;QACzD,MAAM,cAAc,IAAI,CAAC,UAAU;QACnC,IAAI,CAAC,gBAAgB;YACjB;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA;YACxB,KAAK,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE;QACpC;QACA,OAAO,OAAO,CAAE,CAAA;YACZ,IAAI,OAAO,cAAc,MAAM,CAAC,mBAAmB,eAAe,CAAC,OAAO,uBAAuB,GAAG;gBAChG,OAAO,YAAY;YACvB;QACJ;QACA,IAAI,CAAC,sBAAsB;IAC/B;IACA;QACI,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,CAAC,CAAC,SAAS,gBAAgB,KAAK,MAAM,gBAAgB,aAAa,aAAa,EAAE,GAAG;YACrF,OAAO;QACX;QACA,MAAM,gBAAgB,aAAa,aAAa,GAAG,gBAAgB;QACnE,MAAM,YAAY,aAAa,WAAW;QAC1C,IAAI,MAAM,YAAY,UAAU,GAAG,GAAG;QACtC,IAAI,MAAM,YAAY,UAAU,GAAG,GAAG;QACtC,IAAI,kBAAkB,cAAc,QAAQ,EAAE;YAC1C,MAAM,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,cAAc,IAAI;YACpC,MAAM,CAAA,GAAA,yJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,cAAc,IAAI;QACxC;QACA,MAAM,mBAAmB,cAAc,QAAQ,KAAK,WAAW,CAAA,GAAA,yJAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc,UAAU,EAAE,KAAK,KAAK,UAAU,CAAC,MAAM,GAAG,KAAK,GAAG,CAAC,MAAM;QACxJ,IAAI,YAAY,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE;QAC7B,YAAY,YAAY,IAAI,MAAM,CAAC,YAAY,CAAC,IAAI;QACpD,MAAM,cAAc,KAAK,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,GAAG,gBAAgB,IAAI,aAAa,cAAc;QAClG,IAAI,CAAC,WAAW,GAAG;QACnB,OAAO;IACX;IACA;QACI,MAAM,WAAW,IAAI,gKAAA,CAAA,QAAK;QAC1B,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,SAAS,QAAQ,CAAC,EAAE,gBAAgB;QACxC;QACA,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA;YACxB,KAAK,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE;YAChC,KAAK,gBAAgB,CAAC,UAAU,IAAI,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,OAAO,IAAI,CAAC,OAAO,IAAI,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;IAChE;IACA;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;IACzC;IACA;QACI,OAAO,IAAI,CAAC,KAAK;IACrB;IACA,iBAAgB,kBAAkB;QAC9B,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,oBAAoB;IAC7B;IACA,uBAAsB,WAAW,EAAE,uBAAuB;QACtD,IAAI,YAAY,UAAU,IAAI,IAAI,CAAC,OAAO,IAAI,yBAAyB;YACnE,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI;YACR,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE,CAAC,MAAM;YAC7C,MAAM,gBAAgB,IAAI,8KAAA,CAAA,gBAAa;YACvC,UAAU,KAAK,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,KAAK;YACtD,UAAU,MAAM,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM,CAAC,MAAM;YACxD,cAAc,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE;QACnD;IACJ;IACA,4BAA4B,IAAM;IAClC,qBAAoB,MAAM;QACtB,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,OAAO,IAAI;QAChD,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,eAAe,eAAe,IAAI,CAAC,UAAU;QACnD,OAAO,iBAAiB,CAAC,eAAe,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,EAAE,EAAE,IAAI,CAAC,wBAAwB,CAAC;IAC3K;IACA,sBAAqB,WAAW;QAC5B,IAAI,CAAC,YAAY,cAAc,EAAE;YAC7B;QACJ;QACA,CAAA,GAAA,yJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU;IACjE;IACA;QACI,CAAA,GAAA,yJAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,KAAK;IACnC;IACA;QACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO,CAAE,CAAA;YAChD,KAAK,eAAe;QACxB;IACJ;IACA;QACI,OAAO,sLAAA,CAAA,UAAmB,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,GAAG,WAAW,MAAM,CAAC;IAC1F;IACA,oCAAmC,MAAM;QACrC,MAAM,eAAe,OAAO,SAAS;QACrC,IAAI,wBAAwB;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;YAC7C,MAAM,eAAe,YAAY,CAAC,EAAE;YACpC,MAAM,oBAAoB,aAAa,KAAK,CAAC,IAAI;YACjD,yBAAyB,OAAO,kBAAkB,cAAc;YAChE,IAAI,wBAAwB,aAAa,MAAM,GAAG,GAAG;gBACjD,OAAO,oBAAoB,GAAG;gBAC9B;YACJ;QACJ;IACJ;IACA,4BAA2B,cAAc;QACrC,IAAI,oBAAoB,EAAE;QAC1B,MAAM,6BAA6B,CAAC,aAAa,eAAiB,cAAc,kBAAkB,cAAc,mBAAmB;QACnI,IAAK,IAAI,IAAI,eAAe,MAAM,GAAG,GAAG,KAAK,GAAG,KAAK,EAAG;YACpD,MAAM,gBAAgB,cAAc,CAAC,EAAE;YACvC,IAAI,CAAC,cAAc,2BAA2B,IAAI;gBAC9C;YACJ;YACA,cAAc,oBAAoB,GAAG;YACrC,IAAI,CAAC,kCAAkC,CAAC;YACxC,IAAI,CAAC,cAAc,oBAAoB,EAAE;gBACrC,MAAM,eAAe,cAAc,SAAS;gBAC5C,MAAM,yBAAyB,aAAa,MAAM,CAAC,4BAA4B;gBAC/E,IAAI,yBAAyB,aAAa,MAAM,EAAE;oBAC9C,oBAAoB,kBAAkB,MAAM,CAAC;gBACjD,OAAO;oBACH,cAAc,oBAAoB,GAAG;gBACzC;YACJ;QACJ;IACJ;IACA;QACI,MAAM,YAAY,IAAI,CAAC,MAAM;QAC7B,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,yBAAyB;YACxD,UAAU,OAAO,CAAE,CAAA;gBACf,EAAE,oBAAoB,GAAG;YAC7B;YACA;QACJ;QACA,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;YAChB,IAAI,EACA,cAAc,YAAY,EAC1B,MAAM,IAAI,EACb,GAAG;YACJ,MAAM,SAAS,UAAU,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,QAAQ,EAAE,2BAA2B;YACtF,OAAO,OAAO,CAAE,CAAA;gBACZ,aAAa,2BAA2B;YAC5C;YACA,MAAM,UAAU,IAAI,CAAC,eAAe;YACpC,MAAM,cAAc,2BAA2B,WAAW,QAAQ;YAClE,6BAA6B,cAAc,aAAa;YACxD,IAAI,YAAY,MAAM,CAAC,MAAM,EAAE;gBAC3B,MAAM,iBAAiB,QAAQ,WAAW;gBAC1C,MAAM,oBAAoB,QAAQ,UAAU,GAAG,IAAI,KAAK;gBACxD,MAAM,kBAAkB,oBAAoB,CAAC,IAAI,KAAO,eAAe,UAAU,CAAC,OAAO,CAAC,GAAG,QAAQ,IAAI,eAAe,UAAU,CAAC,OAAO,CAAC,GAAG,QAAQ,IAAI,CAAC,IAAI,KAAO,GAAG,QAAQ,GAAG,GAAG,QAAQ;gBAC/L,IAAI,SAAS,EAAE;gBACf,YAAY,MAAM,CAAC,OAAO,CAAE,CAAA;oBACxB,SAAS,OAAO,MAAM,CAAC,EAAE,MAAM;gBACnC;gBACA,OAAO,IAAI,CAAC;gBACZ,kBAAkB,QAAQ,YAAY,gBAAgB;gBACtD,IAAI,CAAC,0BAA0B,CAAC;YACpC;QACJ;IACJ;IACA,aAAY,WAAW,EAAE,kBAAkB;QACvC,SAAS,qBAAqB,IAAI;YAC9B,OAAO,KAAK,GAAG,CAAE,CAAA;gBACb,IAAI,CAAC,KAAK,QAAQ,EAAE;oBAChB,OAAO;gBACX;gBACA,MAAM,QAAQ,KAAK,QAAQ;gBAC3B,OAAO,QAAQ,MAAM,IAAI,CAAC,KAAK,GAAG;YACtC;QACJ;QACA,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,uBAAuB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAC3D,MAAM,YAAY,IAAI,CAAC,UAAU,GAAG;YAAC,IAAI,CAAC,UAAU;SAAC,GAAG,EAAE;QAC1D,MAAM,kBAAkB,IAAI,CAAC,8BAA8B,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,UAAU,MAAM,CAAC,IAAI,CAAC,aAAa;QAC1I,MAAM,eAAe,UAAU,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU;QACnE,MAAM,mBAAmB,UAAU,kBAAkB,IAAI,CAAC,UAAU;QACpE,MAAM,iBAAiB,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa;QACrE,MAAM,qBAAqB,UAAU,IAAI,CAAC,UAAU,GAAG;QACvD,MAAM,UAAU,aAAa,MAAM,CAAC;QACpC,MAAM,cAAc,QAAQ,MAAM,CAAC;QACnC,MAAM,2BAA2B,aAAa,IAAI,CAAE,CAAA,IAAK,EAAE,cAAc;QACzE,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,oBAAoB,CAAC;QAC1B,IAAI,gBAAgB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAE,CAAC,UAAU;YAC9C,QAAQ,CAAC,KAAK,IAAI,CAAC,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,CAAC,GAAG,KAAK,MAAM;YAC7C,OAAO;QACX,GAAI,CAAC;QACL,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,CAAE,CAAC,OAAO;YACzC,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;gBACf,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;YACnB;YACA,OAAO;QACX,GAAI,CAAC;QACL,MAAM,qBAAqB,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,MAAM,CAAC,GAAG;QAC7C,IAAI,CAAC,0BAA0B;QAC/B,MAAM,uBAAuB,IAAI,CAAC,wBAAwB;QAC1D,IAAI,CAAC,YAAY,UAAU,IAAI,CAAC,sBAAsB;YAClD,kBAAkB,cAAc,CAAC,WAAW,sBAAsB,eAAe;YACjF,kBAAkB,gBAAgB,WAAW,sBAAsB,eAAe;YAClF,oBAAoB,SAAS;YAC7B,IAAI,CAAC,kBAAkB;YACvB,eAAe,OAAO,CAAE,CAAA,IAAK,EAAE,sCAAsC,CAAC;YACtE,aAAa,OAAO,CAAE,CAAA,IAAK,EAAE,sCAAsC,CAAC;YACpE,OAAO;QACX;QACA,IAAI,sBAAsB;YACtB,QAAQ,OAAO,CAAE,CAAA,IAAK,EAAE,yBAAyB,MAAM,EAAE,KAAK,CAAC;oBAC3D,KAAK;oBACL,MAAM;oBACN,QAAQ;oBACR,OAAO;gBACX;QACJ;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK;QACtC;QACA,IAAI,eAAe;YACf,OAAO,CAAC;YACR,MAAM;YACN,OAAO;QACX;QACA,IAAI,eAAe,yBAAyB,oBAAqB,CAAA,OAAQ,KAAK,eAAe,CAAC,aAAa,CAAC,KAAK,IAAI,CAAC;QACtH,gBAAgB,eAAe,SAAS,eAAe,WAAW,cAAc;QAChF,MAAM,yBAAyB,CAAA;YAC3B,MAAM,OAAO,eAAe,iBAAiB;YAC7C,MAAM,YAAY,CAAC,eAAe,UAAU,CAAC,OAAO,KAAK;YACzD,kBAAkB,MAAM,WAAW,eAAe;YAClD,IAAI,cAAc;gBACd,eAAe,yBAAyB,oBAAoB;YAChE,OAAO;gBACH,eAAe,uBAAuB;YAC1C;YACA,gBAAgB,eAAe,SAAS,eAAe,WAAW,cAAc;QACpF;QACA,uBAAuB;QACvB,uBAAuB;QACvB,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,sBAAsB,CAAC,cAAc,gBAAgB;YACpF,uBAAuB;QAC3B;QACA,IAAI,iBAAiB,qBAAqB;QAC1C,MAAM,gBAAgB,IAAI,CAAC,iBAAiB;QAC5C,MAAM,oBAAoB,IAAI,CAAC,qBAAqB,CAAC;QACrD,MAAM,iBAAiB,qBAAqB,aAAa;QACzD,oBAAoB,aAAa,cAAc,eAAe;QAC9D,mBAAmB,OAAO,CAAC,UAAU,OAAO;QAC5C,iBAAiB,OAAO,CAAC,UAAU,QAAQ;QAC3C,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;YAChB,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,KAAK,MAAM,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC;QACjD;QACA,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA;YACrB,KAAK,YAAY;QACrB;QACA,aAAa,OAAO,CAAE,CAAC,MAAM;YACzB,IAAI;YACJ,IAAI,SAAS,CAAC,gBAAgB,KAAK,OAAO,KAAK,KAAK,MAAM,iBAAiB,cAAc,IAAI,CAAC,OAAO;gBACjG,MAAM,QAAQ,KAAK,QAAQ;gBAC3B,MAAM,gBAAgB,QAAQ,MAAM,IAAI,CAAC,KAAK,GAAG;gBACjD,MAAM,SAAS,gBAAgB,cAAc,CAAC,EAAE;gBAChD,IAAI,YAAY,KAAK,UAAU,GAAG,QAAQ,EAAE;oBACxC,aAAa,KAAK,IAAI;gBAC1B,OAAO;oBACH,aAAa,IAAI,IAAI;oBACrB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA;wBAChB,IAAI,EACA,MAAM,IAAI,EACb,GAAG;wBACJ,aAAa,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI;oBACrC;gBACJ;gBACA,gBAAgB,eAAe,SAAS,eAAe,WAAW,cAAc;gBAChF,oBAAoB,aAAa,cAAc,eAAe,OAAO;gBACrE,iBAAiB,qBAAqB;YAC1C;QACJ;QACA,IAAI,aAAa,IAAI,CAAE,CAAA,IAAK,EAAE,yBAAyB,MAAM,EAAE,iBAAiB,OAAO,EAAE,aAAa,GAAI;YACtG,kBAAkB,IAAI,CAAC,mBAAmB,CAAC,0BAA0B;YACrE,oBAAoB,cAAc,cAAc,eAAe;QACnE;QACA,eAAe,OAAO,CAAE,CAAA,IAAK,EAAE,sCAAsC,CAAC;QACtE,aAAa,OAAO,CAAE,CAAA,IAAK,EAAE,sCAAsC,CAAC;QACpE,OAAO;IACX;IACA;QACI,MAAM,UAAU,CAAC,IAAI,CAAC,aAAa,IAAI,EAAE,EAAE,MAAM,CAAC,IAAI,CAAC,UAAU,IAAI,EAAE;QACvE,MAAM,WAAW,IAAI,CAAC,0BAA0B,CAAC;QACjD,OAAO;YACH,OAAO,SAAS,KAAK;YACrB,QAAQ,SAAS,MAAM;YACvB;gBACI,QAAQ,OAAO,CAAE,CAAA;oBACb,EAAE,gBAAgB,CAAC;gBACvB;YACJ;YACA;gBACI,QAAQ,OAAO,CAAE,CAAA;oBACb,EAAE,gBAAgB,CAAC;gBACvB;YACJ;QACJ;IACJ;IACA,wBAAwB,CAAC,MAAM,WAAa,KAAK,IAAI,CAAE,CAAA,OAAQ,KAAK,oBAAoB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC;IAC5G;QACI,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,sBAAsB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,EAAG,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACzF,IAAI,YAAY;QAChB,IAAI,qBAAqB;YACrB,IAAI,sBAAsB;YAC1B,IAAI,oBAAoB;YACxB,IAAI,SAAS;gBACT,MAAM,qBAAqB,IAAI,CAAC,eAAe,GAAG,UAAU,GAAG,KAAK;gBACpE,MAAM,mBAAmB,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAE,CAAA,IAAK,EAAE,MAAM,CAAC,KAAK;gBACxE,sBAAsB,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG,qBAAqB;YACpE,OAAO;gBACH,MAAM,sBAAsB,IAAI,CAAC,eAAe,GAAG,UAAU,GAAG,MAAM;gBACtE,MAAM,oBAAoB,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,GAAG,CAAE,CAAA,IAAK,EAAE,MAAM,CAAC,MAAM;gBAC1E,oBAAoB,IAAI,CAAC,OAAO,CAAC,MAAM,GAAG,sBAAsB;YACpE;YACA,YAAY,sBAAsB,KAAK,oBAAoB,IAAI;gBAC3D,OAAO;gBACP,QAAQ;YACZ,IAAI;YACJ,IAAI,MAAM,mBAAmB;gBACzB,MAAM,WAAW,IAAI,CAAC,OAAO;gBAC7B,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;gBAC/B,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,EAAE;gBACnC,MAAM,2BAA2B,CAAC,CAAC,SAAS,UAAU,KAAK,CAAC,MAAM,EAAE,OAAO,MAAM,IAAI,CAAC,uBAAuB;gBAC7G,IAAI,CAAC,WAAW,CAAC,CAAC,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,MAAM,KAAK,CAAC,0BAA0B;oBAC/G,IAAI,CAAC,YAAY,CAAC,SAAS,KAAK,EAAE,SAAS,MAAM,GAAG;oBACpD,YAAY;gBAChB;YACJ;QACJ,OAAO;YACH,YAAY,IAAI,CAAC,aAAa,CAAC,2BAA2B,CAAC,IAAI,CAAC,iBAAiB,IAAI,SAAU,CAAA,OAAQ,CAAC;oBACpG,OAAO,WAAW,CAAC,CAAC,KAAK,IAAI;oBAC7B,QAAQ,CAAC,WAAW,CAAC,CAAC,KAAK,IAAI;gBACnC,CAAC;QACL;QACA,OAAO;IACX;IACA,cAAa,KAAK,EAAE,MAAM;QACtB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO;QAC7B,IAAI,CAAC,WAAW,CAAC;QACjB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,uBAAuB;QAC5B,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,OAAO;IAC3C;IACA,aAAY,YAAY,EAAE,aAAa;QACnC,IAAI,CAAC,gBAAgB,CAAC,eAAe;YACjC;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;QACjC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,YAAY,IAAI,CAAC,UAAU,GAAG;YAAC,IAAI,CAAC,UAAU;SAAC,GAAG,EAAE;QAC1D,MAAM,kBAAkB,IAAI,CAAC,8BAA8B,KAAK,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,aAAa,UAAU,MAAM,CAAC,IAAI,CAAC,aAAa;QAC1I,MAAM,eAAe,UAAU,kBAAkB,IAAI,CAAC,UAAU;QAChE,MAAM,iBAAiB,UAAU,IAAI,CAAC,UAAU,GAAG;QACnD,MAAM,UAAU,aAAa,MAAM,CAAC;QACpC,IAAI,aAAa,KAAK,IAAI,aAAa,MAAM,EAAE;YAC3C,eAAe,cAAc,UAAU,gBAAgB;YACvD,eAAe,cAAc,SAAS,cAAc;YACpD,oBAAoB,SAAS,cAAc;YAC3C,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,MAAM,CAAE,CAAC,OAAO;gBACzC,KAAK,CAAC,KAAK,IAAI,CAAC,GAAG;oBACf,QAAQ,KAAK,MAAM;oBACnB,MAAM,KAAK,IAAI;gBACnB;gBACA,OAAO;YACX,GAAI,CAAC;YACL,gBAAgB,eAAe,SAAS,eAAe,WAAW,uBAAuB,eAAe,yBAAyB,gBAAgB;YACjJ,oBAAoB,SAAS,cAAc;YAC3C,eAAe,OAAO,CAAC,UAAU,OAAO;YACxC,aAAa,OAAO,CAAC,UAAU,QAAQ;YACvC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA,OAAQ,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,KAAK,MAAM,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC;QAC7E;IACJ;IACA;QACI,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI;YACJ,MAAM,kBAAkB,aAAa,2BAA2B;YAChE,MAAM,uBAAuB,SAAS,CAAC,wBAAwB,aAAa,UAAU,GAAG,KAAK,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,QAAQ;YAC7K,MAAM,oBAAoB,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,QAAQ;YAC/D,OAAO,aAAa,sBAAsB,MAAM,sBAAsB,mBAAmB,yBAAyB;QACtH;QACA,OAAO;IACX;IACA;QACI,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI;QACR,MAAM,SAAS,EAAE;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;YACtC,IAAI,IAAI,CAAC,wBAAwB,CAAC,IAAI;gBAClC,OAAO,IAAI,CAAC;oBACR,QAAQ,KAAK,CAAC,EAAE,CAAC,YAAY;oBAC7B,UAAU,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,EAAE;gBAC3C;YACJ;QACJ;QACA,OAAO;IACX;IACA;QACI,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,gBAAgB,CAAC;QAC/D,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,MAAM,OAAO,IAAI,CAAC,UAAU,KAAK;YAAC,IAAI,CAAC,UAAU;YAAE;gBAAC;aAAa;SAAC,GAAG;YACjE;gBAAC;aAAa;YAAE,IAAI,CAAC,UAAU;SAClC;QACD,MAAM,aAAa;YACf,QAAQ,IAAI,CAAC,gBAAgB;YAC7B,OAAO,IAAI,CAAC,mBAAmB;YAC/B,MAAM;QACV;QACA,IAAI,CAAC,CAAC,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,OAAO,GAAG;YAC9D;QACJ;QACA,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,SAAS;QACpC,OAAO;YACH,IAAI,CAAC,UAAU,GAAG,IAAI,yKAAA,CAAA,YAAS,CAAC,IAAI,CAAC,SAAS,EAAE,SAAS,YAAY,IAAI,CAAC,qBAAqB;QACnG;QACA,IAAI,CAAC,UAAU,CAAC,MAAM;IAC1B;IACA;QACI,IAAI;QACJ,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI;QACR,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;YACtC,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG,KAAK,CAAC,EAAE;YACZ,IAAI,CAAC,cAAc;gBACf,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,CAAC,GAAG;YAC/B,OAAO;gBACH,aAAa,KAAK,GAAG,OAAO,KAAK;gBACjC,aAAa,MAAM,GAAG,OAAO,MAAM;YACvC;QACJ;QACA,OAAO;IACX;IACA;QACI,MAAM,yBAAyB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,sBAAsB,eAAe;QAClG,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,QAAQ,EAAE;QAChB,IAAI,CAAC,qBAAqB,CAAC,KAAK;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,KAAK,EAAG;YAC3C,MAAM,kBAAkB,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,eAAe,IAAI;YACzD,IAAI,CAAC,mBAAmB,WAAW,iBAAiB;gBAChD,MAAM,IAAI,CAAC;gBACX;YACJ;YACA,MAAM,OAAO,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;gBACxC,MAAM,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD,EAAE;gBACnB,gBAAgB;YACpB,GAAG,MAAM,CAAC,IAAI,CAAC,qBAAqB;YACpC,MAAM,IAAI,CAAC;QACf;QACA,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA;QACI,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,KAAK,EAAG,CAAC,GAAG;YACnB,MAAM,KAAK,KAAK,YAAY;YAC5B,IAAI,SAAS,IAAI,CAAC,eAAe,CAAC,EAAE,EAAE;gBAClC,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,IAAI,CAAC;oBACzB,GAAG,GAAG,IAAI;oBACV,GAAG,GAAG,GAAG;oBACT,OAAO,GAAG,KAAK;oBACf,QAAQ,GAAG,MAAM;gBACrB;YACJ;QACJ;IACJ;IACA,uBAAsB,IAAI;QACtB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,MAAM,KAAK,KAAK,YAAY,GAAG,KAAK,YAAY,IAAI,CAAC;QACrD,GAAG,IAAI,GAAG,OAAO,IAAI;QACrB,GAAG,GAAG,GAAG,OAAO,GAAG;QACnB,GAAG,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK;QACtC,GAAG,MAAM,GAAG,OAAO,MAAM,GAAG,OAAO,MAAM;QACzC,GAAG,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,GAAG,IAAI,EAAE;QACxC,GAAG,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,GAAG,GAAG,GAAG,EAAE;IAC7C;IACA,mBAAkB,kBAAkB;QAChC,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,KAAK;QACzC,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,KAAK,EAAG,CAAC,GAAG;YACnB,MAAM,gBAAgB,kBAAkB,CAAC,KAAK,IAAI,CAAC;YACnD,MAAM,OAAO;gBACT,MAAM;gBACN,QAAQ,cAAc,KAAK;gBAC3B,kBAAkB,cAAc,OAAO;gBACvC,gBAAgB,cAAc,KAAK;gBACnC,WAAW,cAAc,SAAS;gBAClC,kBAAkB;YACtB;YACA,IAAI,CAAC,qBAAqB,CAAC,MAAM;YACjC,IAAI,CAAC,cAAc,OAAO,EAAE;gBACxB;YACJ;YACA,MAAM,KAAK,KAAK,YAAY;YAC5B,MAAM,oBAAoB,CAAA,GAAA,iKAAA,CAAA,2BAAwB,AAAD,EAAE,GAAG,IAAI,EAAE,GAAG,GAAG,EAAE,GAAG,KAAK,EAAE,GAAG,MAAM,EAAE;YACzF,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,kBAAkB,MAAM,EAAE,kBAAkB,QAAQ,EAAE,IAAI,CAAC,MAAM,MAAM,CAAC,IAAI,CAAC,iBAAiB;QACtH;QACA,IAAI,CAAC,iBAAiB,CAAC,UAAU;IACrC;IACA,iBAAgB,SAAS,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM;QACtD,IAAI,WAAW,SAAS,CAAC,MAAM;QAC/B,IAAI,CAAC,UAAU;YACX,WAAW,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,KAAK,OAAO;YACrD,SAAS,CAAC,MAAM,GAAG;QACvB,OAAO;YACH,SAAS,IAAI,CAAC;gBACV,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,QAAQ;YACZ;QACJ;IACJ;IACA;QACI,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,KAAK,EAAG,CAAC,GAAG;YACnB,IAAI,mBAAmB;YACvB,MAAM,KAAK,KAAK,YAAY;YAC5B,IAAI,EACA,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,EACA,KAAK,GAAG,EACX,GAAG;YACJ,IAAI,EACA,OAAO,KAAK,EACf,GAAG;YACJ,IAAI,EACA,QAAQ,MAAM,EACjB,GAAG;YACJ,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,IAAI,CAAC,eAAe,CAAC,eAAe,KAAK,EAAE,GAAG,MAAM,KAAK,OAAO;YAChE,IAAI,CAAC,eAAe,CAAC,eAAe,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YAC/D,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAG,CAAC,GAAG;gBACpB,IAAI,OAAO,IAAI,KAAK,KAAK,IAAI,IAAI,CAAC,OAAO,iBAAiB,MAAM,OAAO,mBAAmB,EAAE,GAAG;oBAC3F,mBAAmB;gBACvB;YACJ;YACA,IAAI,kBAAkB;gBAClB,IAAI,IAAI,CAAC,UAAU,IAAI;oBACnB,MAAM;oBACN,SAAS,OAAO,MAAM;gBAC1B,OAAO;oBACH,OAAO;oBACP,QAAQ,OAAO,KAAK;gBACxB;gBACA,IAAI,CAAC,eAAe,CAAC,eAAe,IAAI,EAAE,GAAG,MAAM,KAAK,OAAO;YACnE,OAAO;gBACH,eAAe,IAAI,CAAC,EAAE,GAAG;YAC7B;QACJ;IACJ;IACA;QACI,MAAM,OAAO,IAAI,CAAC,WAAW;QAC7B,MAAM,wBAAwB,IAAI,CAAC,oBAAoB;QACvD,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,KAAK,EAAG;YACrC,MAAM,qBAAqB,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI;YACnE,IAAI,CAAC,EAAE,CAAC,cAAc,CAAC,oBAAoB;QAC/C;IACJ;IACA,0BAAyB,SAAS;QAC9B,IAAI;QACJ,MAAM,0BAA0B,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC,sBAAsB,MAAM,CAAC,OAAO;QAClG,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,UAAU;QAClC,MAAM,iBAAiB,SAAS,QAAQ,KAAK,MAAM,QAAQ,SAAS,CAAC,eAAe,KAAK,MAAM,KAAK,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,OAAO;QAC3J,OAAO,KAAK,MAAM,iBAAiB,0BAA0B;IACjE;IACA,mBAAkB,QAAQ;QACtB,IAAI;QACJ,OAAO,SAAS,CAAC,mBAAmB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAE,CAAA,OAAQ,KAAK,IAAI,KAAK,SAAU,KAAK,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,MAAM;IAC5J;IACA;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;YAC5B,OAAO,IAAI;YACX,SAAS,IAAI,CAAC,UAAU;YACxB,WAAW,IAAI,CAAC,oBAAoB,GAAG,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG;YACnE,gBAAgB,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QAClD;IACJ;IACA;QACI,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,MAAM,iBAAiB,YAAY,CAAC,KAAK,SAAW,CAAC;gBACjD,GAAG,IAAI,CAAC,GAAG;gBACX,GAAG,IAAI,CAAC;YACZ,CAAC,IAAI,CAAC,KAAK,SAAW,CAAC;gBACnB,GAAG,IAAI,CAAC;gBACR,GAAG,IAAI,CAAC,GAAG;YACf,CAAC;QACD,MAAM,YAAY,CAAC,GAAG;YAClB,MAAM,gBAAgB,YAAY,IAAI;YACtC,MAAM,gBAAgB,EAAE,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,cAAc;YAClE,MAAM,iBAAiB,EAAE,MAAM,CAAC,EAAE,CAAC,eAAe,EAAE,CAAC,cAAc;YACnE,IAAI,gBAAgB,mBAAmB,GAAG;gBACtC,MAAM,aAAa,EAAE,MAAM,CAAC,EAAE,CAAC,QAAQ,GAAG,MAAM,CAAC,YAAY,GAAG,aAAa;gBAC7E,MAAM,YAAY,WAAW,UAAU,KAAK,CAAC,IAAI;gBACjD,OAAO,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,EAAE,IAAI;YACrC;YACA,OAAO;QACX;QACA,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,eAAe,IAAK,CAAC,GAAG;YAC/B,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,CAAC,GAAG;gBACf,MAAM,aAAa,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,GAAG,UAAU,GAAG,QAAQ;gBACxE,6LAAA,CAAA,cAAW,CAAC,qCAAqC,CAAC,QAAQ,IAAI,CAAC,gBAAgB,IAAI,WAAW,YAAY,gBAAgB;YAC9H;QACJ;IACJ;IACA;QACI,MAAM,cAAc,CAAC;QACrB,MAAM,gBAAgB,IAAI,CAAC,iBAAiB;QAC5C,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,eAAgB,CAAC,GAAG;YACtB,MAAM,SAAS,aAAa,SAAS;YACrC,MAAM,YAAY,aAAa,YAAY,MAAM;YACjD,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,CAAC,GAAG;gBACf,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;gBACJ,IAAI,CAAC,WAAW,CAAC,SAAS,EAAE;oBACxB,WAAW,CAAC,SAAS,GAAG,CAAC;gBAC7B;gBACA,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,UAAU,EAAE;oBACnC,WAAW,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE;gBACzC;gBACA,WAAW,CAAC,SAAS,CAAC,UAAU,CAAC,IAAI,CAAC;YAC1C;QACJ;QACA,OAAO;IACX;IACA;QACI,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B;IACA,cAAa,GAAG,EAAE,GAAG;QACjB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,QAAQ,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAU,AAAD,EAAE,MAAM;YAC5D;QACJ;QACA,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC;YAAC;YAAK;SAAI;IACjD;IACA;QACI,MAAM,OAAO,IAAI,CAAC,aAAa;QAC/B,MAAM,yBAAyB,IAAI,CAAC,eAAe;QACnD,KAAK,OAAO,CAAE,CAAA;YACV,KAAK,gBAAgB,CAAC,2BAA2B;YACjD,IAAI,CAAC,6BAA6B,CAAC;QACvC;QACA,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,MAAM,aAAa,IAAI,CAAC,aAAa,CAAC,EAAE,CAAC,aAAa;QACtD,MAAM,QAAQ,WAAW,gBAAgB;QACzC,MAAM,aAAa,MAAM,QAAQ,KAAK;QACtC,MAAM,EACF,YAAY,UAAU,EACzB,GAAG;QACJ,OAAO;YACH,YAAY,aAAa,MAAM,UAAU,IAAI,UAAU,CAAC,EAAE,GAAG,MAAM,UAAU;YAC7E,YAAY,aAAa,MAAM,UAAU,IAAI,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,GAAG,MAAM,UAAU;QACrG;IACJ;IACA;QACI,IAAI,CAAC,QAAQ;QACb,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,eAAe;YACjC,IAAI,CAAC,qBAAqB;QAC9B;IACJ;IACA;QACI,OAAO;YAAC,IAAI,CAAC,eAAe;SAAG,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;IAC1D;IACA,gCAA+B,IAAI,EAAE,KAAK;QACtC,IAAI,KAAK,cAAc,EAAE;YACrB,IAAI,SAAS,IAAI,CAAC,eAAe,IAAI;gBACjC,OAAO;YACX;YACA,IAAI,CAAC,aAAa,CAAC,MAAM,CAAE,CAAA,IAAK,MAAM,MAAO,OAAO,CAAE,CAAA,IAAK,EAAE,WAAW,CAAC,OAAO;oBAC5E,OAAO;oBACP,KAAK;gBACT;QACJ;QACA,OAAO;IACX;IACA;QACI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,aAAa;QACtD,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,IAAI,CAAC,QAAQ;QACb,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC9B,4BAA4B;QAChC;IACJ;IACA;QACI,MAAM,SAAS,IAAI,CAAC,QAAQ,IAAI;QAChC,MAAM,YAAY,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACvC,IAAI,YAAY,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,YAAY;YAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,KAAK,EAAG;gBAC1C,MAAM,aAAa,CAAC,UAAU,EAAE,EAAE,aAAa,CAAC;gBAChD,IAAI,CAAC,mBAAmB,CAAC,WAAW,GAAG;YAC3C;QACJ;QACA,OAAO;IACX;IACA;QACI,MAAM,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;QACrC,MAAM,sBAAsB,CAAA,GAAA,yJAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ,WAAW,IAAI,CAAC,QAAQ,IAAI,CAAC,MAAM,CAAC;QACjG,IAAI,CAAC,QAAQ,eAAe,IAAI,CAAC,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD,EAAE,qBAAqB,IAAI,CAAC,MAAM,CAAC,8BAA8B;YAC3G,IAAI,CAAC,MAAM,CAAC,4BAA4B;QAC5C,OAAO;YACH,QAAQ,eAAe,GAAG;QAC9B;QACA,IAAI,CAAC,QAAQ;IACjB;AACJ;AACA,QAAQ,SAAS,CAAC,4KAAA,CAAA,UAAW;AAC7B,QAAQ,SAAS,CAAC,4KAAA,CAAA,UAAU;AAC5B,QAAQ,SAAS,CAAC,+JAAA,CAAA,UAAO,CAAC,IAAI;AAC9B,QAAQ,SAAS,CAAC,+JAAA,CAAA,UAAO,CAAC,KAAK;AAC/B,CAAA,GAAA,kKAAA,CAAA,UAAiB,AAAD,EAAE,WAAW;uCACd", "ignoreList": [0], "debugId": null}}]}