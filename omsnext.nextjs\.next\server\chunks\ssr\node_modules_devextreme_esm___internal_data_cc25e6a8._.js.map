{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_errors.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_errors.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport coreErrors from \"../../core/errors\";\r\nimport errorUtils from \"../../core/utils/error\";\r\nexport const errors = errorUtils(coreErrors.ERROR_MESSAGES, {\r\n    E4000: \"[DevExpress.data]: {0}\",\r\n    E4001: \"Unknown aggregating function is detected: '{0}'\",\r\n    E4002: \"Unsupported OData protocol version is used\",\r\n    E4003: \"Unknown filter operation is used: {0}\",\r\n    E4004: \"The thenby() method is called before the sortby() method\",\r\n    E4005: \"Store requires a key expression for this operation\",\r\n    E4006: \"ArrayStore 'data' option must be an array\",\r\n    E4007: \"Compound keys cannot be auto-generated\",\r\n    E4008: \"Attempt to insert an item with a duplicated key\",\r\n    E4009: \"Data item cannot be found\",\r\n    E4010: \"CustomStore does not support creating queries\",\r\n    E4011: \"Custom Store method is not implemented or is not a function: {0}\",\r\n    E4012: \"Custom Store method returns an invalid value: {0}\",\r\n    E4013: \"Local Store requires the 'name' configuration option is specified\",\r\n    E4014: \"Unknown data type is specified for ODataStore: {0}\",\r\n    E4015: \"Unknown entity name or alias is used: {0}\",\r\n    E4016: \"The compileSetter(expr) method is called with 'self' passed as a parameter\",\r\n    E4017: \"Keys cannot be modified\",\r\n    E4018: \"The server has returned a non-numeric value in a response to an item count request\",\r\n    E4019: \"Mixing of group operators inside a single group of filter expression is not allowed\",\r\n    E4020: \"Unknown store type is detected: {0}\",\r\n    E4021: \"The server response does not provide the totalCount value\",\r\n    E4022: \"The server response does not provide the groupCount value\",\r\n    E4023: \"Could not parse the following XML: {0}\",\r\n    E4024: \"String function {0} cannot be used with the data field {1} of type {2}.\",\r\n    W4000: \"Data returned from the server has an incorrect structure\",\r\n    W4001: 'The {0} field is listed in both \"keyType\" and \"fieldTypes\". The value of \"fieldTypes\" is used.',\r\n    W4002: \"Data loading has failed for some cells due to the following error: {0}\"\r\n});\r\nexport let errorHandler = null;\r\nexport const handleError = function(error) {\r\n    var _errorHandler;\r\n    null === (_errorHandler = errorHandler) || void 0 === _errorHandler || _errorHandler(error)\r\n};\r\nexport const setErrorHandler = handler => errorHandler = handler;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AACA;;;AACO,MAAM,SAAS,CAAA,GAAA,2JAAA,CAAA,UAAU,AAAD,EAAE,mJAAA,CAAA,UAAU,CAAC,cAAc,EAAE;IACxD,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACX;AACO,IAAI,eAAe;AACnB,MAAM,cAAc,SAAS,KAAK;IACrC,IAAI;IACJ,SAAS,CAAC,gBAAgB,YAAY,KAAK,KAAK,MAAM,iBAAiB,cAAc;AACzF;AACO,MAAM,kBAAkB,CAAA,UAAW,eAAe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport domAdapter from \"../../core/dom_adapter\";\r\nimport {\r\n    equalByValue\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    Deferred\r\n} from \"../../core/utils/deferred\";\r\nimport {\r\n    map\r\n} from \"../../core/utils/iterator\";\r\nimport readyCallbacks from \"../../core/utils/ready_callbacks\";\r\nimport {\r\n    isFunction\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    getWindow\r\n} from \"../../core/utils/window\";\r\nconst ready = readyCallbacks.add;\r\nexport const XHR_ERROR_UNLOAD = \"DEVEXTREME_XHR_ERROR_UNLOAD\";\r\nexport const normalizeBinaryCriterion = function(crit) {\r\n    return [crit[0], crit.length < 3 ? \"=\" : String(crit[1]).toLowerCase(), crit.length < 2 ? true : crit[crit.length - 1]]\r\n};\r\nexport const normalizeSortingInfo = function(info) {\r\n    if (!Array.isArray(info)) {\r\n        info = [info]\r\n    }\r\n    return map(info, (i => {\r\n        const result = {\r\n            selector: isFunction(i) || \"string\" === typeof i ? i : i.getter || i.field || i.selector,\r\n            desc: !!(i.desc || \"d\" === String(i.dir).charAt(0).toLowerCase())\r\n        };\r\n        if (i.compare) {\r\n            result.compare = i.compare\r\n        }\r\n        return result\r\n    }))\r\n};\r\nexport const errorMessageFromXhr = function() {\r\n    const textStatusMessages = {\r\n        timeout: \"Network connection timeout\",\r\n        error: \"Unspecified network error\",\r\n        parsererror: \"Unexpected server response\"\r\n    };\r\n    let unloading;\r\n    ready((() => {\r\n        const window = getWindow();\r\n        domAdapter.listen(window, \"beforeunload\", (() => {\r\n            unloading = true\r\n        }))\r\n    }));\r\n    return function(xhr, textStatus) {\r\n        if (unloading) {\r\n            return XHR_ERROR_UNLOAD\r\n        }\r\n        if (xhr.status < 400) {\r\n            return function(textStatus) {\r\n                let result = textStatusMessages[textStatus];\r\n                if (!result) {\r\n                    return textStatus\r\n                }\r\n                return result\r\n            }(textStatus)\r\n        }\r\n        return xhr.statusText\r\n    }\r\n}();\r\nexport const aggregators = {\r\n    count: {\r\n        seed: 0,\r\n        step: count => 1 + count\r\n    },\r\n    sum: {\r\n        seed: 0,\r\n        step: (sum, item) => sum + item\r\n    },\r\n    min: {\r\n        step: (min, item) => item < min ? item : min\r\n    },\r\n    max: {\r\n        step: (max, item) => item > max ? item : max\r\n    },\r\n    avg: {\r\n        seed: [0, 0],\r\n        step: (pair, value) => [pair[0] + value, pair[1] + 1],\r\n        finalize: pair => pair[1] ? pair[0] / pair[1] : NaN\r\n    }\r\n};\r\nexport const processRequestResultLock = function() {\r\n    let lockCount = 0;\r\n    let lockDeferred;\r\n    return {\r\n        obtain: function() {\r\n            if (0 === lockCount) {\r\n                lockDeferred = new Deferred\r\n            }\r\n            lockCount++\r\n        },\r\n        release: function() {\r\n            lockCount--;\r\n            if (lockCount < 1) {\r\n                lockDeferred.resolve()\r\n            }\r\n        },\r\n        promise: function() {\r\n            const deferred = 0 === lockCount ? (new Deferred).resolve() : lockDeferred;\r\n            return deferred.promise()\r\n        },\r\n        reset: function() {\r\n            lockCount = 0;\r\n            if (lockDeferred) {\r\n                lockDeferred.resolve()\r\n            }\r\n        }\r\n    }\r\n}();\r\nexport function isDisjunctiveOperator(condition) {\r\n    return /^(or|\\|\\||\\|)$/i.test(condition)\r\n}\r\nexport function isConjunctiveOperator(condition) {\r\n    return /^(and|&&|&)$/i.test(condition)\r\n}\r\nexport const keysEqual = function(keyExpr, key1, key2) {\r\n    if (Array.isArray(keyExpr)) {\r\n        const names = map(key1, ((v, k) => k));\r\n        let name;\r\n        for (let i = 0; i < names.length; i++) {\r\n            name = names[i];\r\n            if (!equalByValue(key1[name], key2[name], {\r\n                    strict: false\r\n                })) {\r\n                return false\r\n            }\r\n        }\r\n        return true\r\n    }\r\n    return equalByValue(key1, key2, {\r\n        strict: false\r\n    })\r\n};\r\nconst BASE64_CHARS = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=\";\r\nexport const base64_encode = function(input) {\r\n    if (!Array.isArray(input)) {\r\n        input = stringToByteArray(String(input))\r\n    }\r\n    let result = \"\";\r\n\r\n    function getBase64Char(index) {\r\n        return BASE64_CHARS.charAt(index)\r\n    }\r\n    for (let i = 0; i < input.length; i += 3) {\r\n        const octet1 = input[i];\r\n        const octet2 = input[i + 1];\r\n        const octet3 = input[i + 2];\r\n        result += map([octet1 >> 2, (3 & octet1) << 4 | octet2 >> 4, isNaN(octet2) ? 64 : (15 & octet2) << 2 | octet3 >> 6, isNaN(octet3) ? 64 : 63 & octet3], getBase64Char).join(\"\")\r\n    }\r\n    return result\r\n};\r\n\r\nfunction stringToByteArray(str) {\r\n    const bytes = [];\r\n    let code;\r\n    let i;\r\n    for (i = 0; i < str.length; i++) {\r\n        code = str.charCodeAt(i);\r\n        if (code < 128) {\r\n            bytes.push(code)\r\n        } else if (code < 2048) {\r\n            bytes.push(192 + (code >> 6), 128 + (63 & code))\r\n        } else if (code < 65536) {\r\n            bytes.push(224 + (code >> 12), 128 + (code >> 6 & 63), 128 + (63 & code))\r\n        } else if (code < 2097152) {\r\n            bytes.push(240 + (code >> 18), 128 + (code >> 12 & 63), 128 + (code >> 6 & 63), 128 + (63 & code))\r\n        }\r\n    }\r\n    return bytes\r\n}\r\nexport const isUnaryOperation = function(crit) {\r\n    return \"!\" === crit[0] && Array.isArray(crit[1])\r\n};\r\nconst isGroupOperator = function(value) {\r\n    return \"and\" === value || \"or\" === value\r\n};\r\nexport const isUniformEqualsByOr = function(crit) {\r\n    if (crit.length > 2 && Array.isArray(crit[0]) && \"or\" === crit[1] && \"string\" === typeof crit[0][0] && \"=\" === crit[0][1]) {\r\n        const [prop] = crit[0];\r\n        return !crit.find(((el, i) => i % 2 !== 0 ? \"or\" !== el : !Array.isArray(el) || 3 !== el.length || el[0] !== prop || \"=\" !== el[1]))\r\n    }\r\n    return false\r\n};\r\nexport const isGroupCriterion = function(crit) {\r\n    const first = crit[0];\r\n    const second = crit[1];\r\n    if (Array.isArray(first)) {\r\n        return true\r\n    }\r\n    if (isFunction(first)) {\r\n        if (Array.isArray(second) || isFunction(second) || isGroupOperator(second)) {\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n};\r\nexport const trivialPromise = function() {\r\n    const d = new Deferred;\r\n    return d.resolve.apply(d, arguments).promise()\r\n};\r\nexport const rejectedPromise = function() {\r\n    const d = new Deferred;\r\n    return d.reject.apply(d, arguments).promise()\r\n};\r\n\r\nfunction throttle(func, timeout) {\r\n    let timeoutId;\r\n    return function() {\r\n        if (!timeoutId) {\r\n            timeoutId = setTimeout((() => {\r\n                timeoutId = void 0;\r\n                func.call(this)\r\n            }), isFunction(timeout) ? timeout() : timeout)\r\n        }\r\n        return timeoutId\r\n    }\r\n}\r\nexport function throttleChanges(func, timeout) {\r\n    let cache = [];\r\n    const throttled = throttle((function() {\r\n        func.call(this, cache);\r\n        cache = []\r\n    }), timeout);\r\n    return function(changes) {\r\n        if (Array.isArray(changes)) {\r\n            cache.push(...changes)\r\n        }\r\n        return throttled.call(this, cache)\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;;;;;;;;AAGA,MAAM,QAAQ,qKAAA,CAAA,UAAc,CAAC,GAAG;AACzB,MAAM,mBAAmB;AACzB,MAAM,2BAA2B,SAAS,IAAI;IACjD,OAAO;QAAC,IAAI,CAAC,EAAE;QAAE,KAAK,MAAM,GAAG,IAAI,MAAM,OAAO,IAAI,CAAC,EAAE,EAAE,WAAW;QAAI,KAAK,MAAM,GAAG,IAAI,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;KAAC;AAC3H;AACO,MAAM,uBAAuB,SAAS,IAAI;IAC7C,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QACtB,OAAO;YAAC;SAAK;IACjB;IACA,OAAO,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE,MAAO,CAAA;QACd,MAAM,SAAS;YACX,UAAU,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,MAAM,aAAa,OAAO,IAAI,IAAI,EAAE,MAAM,IAAI,EAAE,KAAK,IAAI,EAAE,QAAQ;YACxF,MAAM,CAAC,CAAC,CAAC,EAAE,IAAI,IAAI,QAAQ,OAAO,EAAE,GAAG,EAAE,MAAM,CAAC,GAAG,WAAW,EAAE;QACpE;QACA,IAAI,EAAE,OAAO,EAAE;YACX,OAAO,OAAO,GAAG,EAAE,OAAO;QAC9B;QACA,OAAO;IACX;AACJ;AACO,MAAM,sBAAsB;IAC/B,MAAM,qBAAqB;QACvB,SAAS;QACT,OAAO;QACP,aAAa;IACjB;IACA,IAAI;IACJ,MAAO;QACH,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;QACvB,wJAAA,CAAA,UAAU,CAAC,MAAM,CAAC,QAAQ,gBAAiB;YACvC,YAAY;QAChB;IACJ;IACA,OAAO,SAAS,GAAG,EAAE,UAAU;QAC3B,IAAI,WAAW;YACX,OAAO;QACX;QACA,IAAI,IAAI,MAAM,GAAG,KAAK;YAClB,OAAO,SAAS,UAAU;gBACtB,IAAI,SAAS,kBAAkB,CAAC,WAAW;gBAC3C,IAAI,CAAC,QAAQ;oBACT,OAAO;gBACX;gBACA,OAAO;YACX,EAAE;QACN;QACA,OAAO,IAAI,UAAU;IACzB;AACJ;AACO,MAAM,cAAc;IACvB,OAAO;QACH,MAAM;QACN,MAAM,CAAA,QAAS,IAAI;IACvB;IACA,KAAK;QACD,MAAM;QACN,MAAM,CAAC,KAAK,OAAS,MAAM;IAC/B;IACA,KAAK;QACD,MAAM,CAAC,KAAK,OAAS,OAAO,MAAM,OAAO;IAC7C;IACA,KAAK;QACD,MAAM,CAAC,KAAK,OAAS,OAAO,MAAM,OAAO;IAC7C;IACA,KAAK;QACD,MAAM;YAAC;YAAG;SAAE;QACZ,MAAM,CAAC,MAAM,QAAU;gBAAC,IAAI,CAAC,EAAE,GAAG;gBAAO,IAAI,CAAC,EAAE,GAAG;aAAE;QACrD,UAAU,CAAA,OAAQ,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG;IACpD;AACJ;AACO,MAAM,2BAA2B;IACpC,IAAI,YAAY;IAChB,IAAI;IACJ,OAAO;QACH,QAAQ;YACJ,IAAI,MAAM,WAAW;gBACjB,eAAe,IAAI,iLAAA,CAAA,WAAQ;YAC/B;YACA;QACJ;QACA,SAAS;YACL;YACA,IAAI,YAAY,GAAG;gBACf,aAAa,OAAO;YACxB;QACJ;QACA,SAAS;YACL,MAAM,WAAW,MAAM,YAAY,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,OAAO,KAAK;YAC9D,OAAO,SAAS,OAAO;QAC3B;QACA,OAAO;YACH,YAAY;YACZ,IAAI,cAAc;gBACd,aAAa,OAAO;YACxB;QACJ;IACJ;AACJ;AACO,SAAS,sBAAsB,SAAS;IAC3C,OAAO,kBAAkB,IAAI,CAAC;AAClC;AACO,SAAS,sBAAsB,SAAS;IAC3C,OAAO,gBAAgB,IAAI,CAAC;AAChC;AACO,MAAM,YAAY,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI;IACjD,IAAI,MAAM,OAAO,CAAC,UAAU;QACxB,MAAM,QAAQ,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE,MAAO,CAAC,GAAG,IAAM;QACnC,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,OAAO,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,KAAK,EAAE;gBAClC,QAAQ;YACZ,IAAI;gBACJ,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,OAAO,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,MAAM,MAAM;QAC5B,QAAQ;IACZ;AACJ;AACA,MAAM,eAAe;AACd,MAAM,gBAAgB,SAAS,KAAK;IACvC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;QACvB,QAAQ,kBAAkB,OAAO;IACrC;IACA,IAAI,SAAS;IAEb,SAAS,cAAc,KAAK;QACxB,OAAO,aAAa,MAAM,CAAC;IAC/B;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;QACtC,MAAM,SAAS,KAAK,CAAC,EAAE;QACvB,MAAM,SAAS,KAAK,CAAC,IAAI,EAAE;QAC3B,MAAM,SAAS,KAAK,CAAC,IAAI,EAAE;QAC3B,UAAU,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE;YAAC,UAAU;YAAG,CAAC,IAAI,MAAM,KAAK,IAAI,UAAU;YAAG,MAAM,UAAU,KAAK,CAAC,KAAK,MAAM,KAAK,IAAI,UAAU;YAAG,MAAM,UAAU,KAAK,KAAK;SAAO,EAAE,eAAe,IAAI,CAAC;IAC/K;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,GAAG;IAC1B,MAAM,QAAQ,EAAE;IAChB,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;QAC7B,OAAO,IAAI,UAAU,CAAC;QACtB,IAAI,OAAO,KAAK;YACZ,MAAM,IAAI,CAAC;QACf,OAAO,IAAI,OAAO,MAAM;YACpB,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,KAAK,IAAI;QAClD,OAAO,IAAI,OAAO,OAAO;YACrB,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI;QAC3E,OAAO,IAAI,OAAO,SAAS;YACvB,MAAM,IAAI,CAAC,MAAM,CAAC,QAAQ,EAAE,GAAG,MAAM,CAAC,QAAQ,KAAK,EAAE,GAAG,MAAM,CAAC,QAAQ,IAAI,EAAE,GAAG,MAAM,CAAC,KAAK,IAAI;QACpG;IACJ;IACA,OAAO;AACX;AACO,MAAM,mBAAmB,SAAS,IAAI;IACzC,OAAO,QAAQ,IAAI,CAAC,EAAE,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE;AACnD;AACA,MAAM,kBAAkB,SAAS,KAAK;IAClC,OAAO,UAAU,SAAS,SAAS;AACvC;AACO,MAAM,sBAAsB,SAAS,IAAI;IAC5C,IAAI,KAAK,MAAM,GAAG,KAAK,MAAM,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK,SAAS,IAAI,CAAC,EAAE,IAAI,aAAa,OAAO,IAAI,CAAC,EAAE,CAAC,EAAE,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;QACvH,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC,EAAE;QACtB,OAAO,CAAC,KAAK,IAAI,CAAE,CAAC,IAAI,IAAM,IAAI,MAAM,IAAI,SAAS,KAAK,CAAC,MAAM,OAAO,CAAC,OAAO,MAAM,GAAG,MAAM,IAAI,EAAE,CAAC,EAAE,KAAK,QAAQ,QAAQ,EAAE,CAAC,EAAE;IACtI;IACA,OAAO;AACX;AACO,MAAM,mBAAmB,SAAS,IAAI;IACzC,MAAM,QAAQ,IAAI,CAAC,EAAE;IACrB,MAAM,SAAS,IAAI,CAAC,EAAE;IACtB,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,OAAO;IACX;IACA,IAAI,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;QACnB,IAAI,MAAM,OAAO,CAAC,WAAW,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,WAAW,gBAAgB,SAAS;YACxE,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACO,MAAM,iBAAiB;IAC1B,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;IACtB,OAAO,EAAE,OAAO,CAAC,KAAK,CAAC,GAAG,WAAW,OAAO;AAChD;AACO,MAAM,kBAAkB;IAC3B,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;IACtB,OAAO,EAAE,MAAM,CAAC,KAAK,CAAC,GAAG,WAAW,OAAO;AAC/C;AAEA,SAAS,SAAS,IAAI,EAAE,OAAO;IAC3B,IAAI;IACJ,OAAO;QACH,IAAI,CAAC,WAAW;YACZ,YAAY,WAAY;gBACpB,YAAY,KAAK;gBACjB,KAAK,IAAI,CAAC,IAAI;YAClB,GAAI,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,WAAW,YAAY;QAC1C;QACA,OAAO;IACX;AACJ;AACO,SAAS,gBAAgB,IAAI,EAAE,OAAO;IACzC,IAAI,QAAQ,EAAE;IACd,MAAM,YAAY,SAAU;QACxB,KAAK,IAAI,CAAC,IAAI,EAAE;QAChB,QAAQ,EAAE;IACd,GAAI;IACJ,OAAO,SAAS,OAAO;QACnB,IAAI,MAAM,OAAO,CAAC,UAAU;YACxB,MAAM,IAAI,IAAI;QAClB;QACA,OAAO,UAAU,IAAI,CAAC,IAAI,EAAE;IAChC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 343, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_array_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_array_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    errors\r\n} from \"../../common/data/errors\";\r\nimport {\r\n    keysEqual,\r\n    rejectedPromise,\r\n    trivialPromise\r\n} from \"../../common/data/utils\";\r\nimport config from \"../../core/config\";\r\nimport Guid from \"../../core/guid\";\r\nimport {\r\n    compileGetter\r\n} from \"../../core/utils/data\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    deepExtendArraySafe\r\n} from \"../../core/utils/object\";\r\nimport {\r\n    isDefined,\r\n    isEmptyObject,\r\n    isObject,\r\n    isPlainObject\r\n} from \"../../core/utils/type\";\r\n\r\nfunction hasKey(target, keyOrKeys) {\r\n    let key;\r\n    const keys = \"string\" === typeof keyOrKeys ? keyOrKeys.split() : keyOrKeys.slice();\r\n    while (keys.length) {\r\n        key = keys.shift();\r\n        if (key in target) {\r\n            return true\r\n        }\r\n    }\r\n    return false\r\n}\r\n\r\nfunction findItems(keyInfo, items, key, groupCount) {\r\n    let childItems;\r\n    let result;\r\n    if (groupCount) {\r\n        for (let i = 0; i < items.length; i++) {\r\n            childItems = items[i].items || items[i].collapsedItems || [];\r\n            result = findItems(keyInfo, childItems || [], key, groupCount - 1);\r\n            if (result) {\r\n                return result\r\n            }\r\n        }\r\n    } else if (indexByKey(keyInfo, items, key) >= 0) {\r\n        return items\r\n    }\r\n}\r\n\r\nfunction getItems(keyInfo, items, key, groupCount) {\r\n    if (groupCount) {\r\n        return findItems(keyInfo, items, key, groupCount) || []\r\n    }\r\n    return items\r\n}\r\n\r\nfunction generateDataByKeyMap(keyInfo, array) {\r\n    if (keyInfo.key() && (!array._dataByKeyMap || array._dataByKeyMapLength !== array.length)) {\r\n        const dataByKeyMap = {};\r\n        const arrayLength = array.length;\r\n        for (let i = 0; i < arrayLength; i++) {\r\n            dataByKeyMap[JSON.stringify(keyInfo.keyOf(array[i]))] = array[i]\r\n        }\r\n        array._dataByKeyMap = dataByKeyMap;\r\n        array._dataByKeyMapLength = arrayLength\r\n    }\r\n}\r\n\r\nfunction getCacheValue(array, key) {\r\n    if (array._dataByKeyMap) {\r\n        return array._dataByKeyMap[JSON.stringify(key)]\r\n    }\r\n}\r\n\r\nfunction getHasKeyCacheValue(array, key) {\r\n    if (array._dataByKeyMap) {\r\n        return array._dataByKeyMap[JSON.stringify(key)]\r\n    }\r\n    return true\r\n}\r\n\r\nfunction setDataByKeyMapValue(array, key, data) {\r\n    if (array._dataByKeyMap) {\r\n        array._dataByKeyMap[JSON.stringify(key)] = data;\r\n        array._dataByKeyMapLength += data ? 1 : -1\r\n    }\r\n}\r\n\r\nfunction cloneInstanceWithChangedPaths(instance, changes, clonedInstances) {\r\n    clonedInstances = clonedInstances || new WeakMap;\r\n    const result = instance ? Object.create(Object.getPrototypeOf(instance)) : {};\r\n    if (instance) {\r\n        clonedInstances.set(instance, result)\r\n    }\r\n    const instanceWithoutPrototype = _extends({}, instance);\r\n    deepExtendArraySafe(result, instanceWithoutPrototype, true, true, true);\r\n    for (const name in instanceWithoutPrototype) {\r\n        const value = instanceWithoutPrototype[name];\r\n        const change = null === changes || void 0 === changes ? void 0 : changes[name];\r\n        if (isObject(value) && !isPlainObject(value) && isObject(change) && !clonedInstances.has(value)) {\r\n            result[name] = cloneInstanceWithChangedPaths(value, change, clonedInstances)\r\n        }\r\n    }\r\n    for (const name in result) {\r\n        const prop = result[name];\r\n        if (isObject(prop) && clonedInstances.has(prop)) {\r\n            result[name] = clonedInstances.get(prop)\r\n        }\r\n    }\r\n    return result\r\n}\r\n\r\nfunction createObjectWithChanges(target, changes) {\r\n    const result = cloneInstanceWithChangedPaths(target, changes);\r\n    return deepExtendArraySafe(result, changes, true, true, true)\r\n}\r\n\r\nfunction applyBatch(_ref) {\r\n    let {\r\n        keyInfo: keyInfo,\r\n        data: data,\r\n        changes: changes,\r\n        groupCount: groupCount,\r\n        useInsertIndex: useInsertIndex,\r\n        immutable: immutable,\r\n        disableCache: disableCache,\r\n        logError: logError,\r\n        skipCopying: skipCopying\r\n    } = _ref;\r\n    const resultItems = true === immutable ? [...data] : data;\r\n    changes.forEach((item => {\r\n        const items = \"insert\" === item.type ? resultItems : getItems(keyInfo, resultItems, item.key, groupCount);\r\n        !disableCache && generateDataByKeyMap(keyInfo, items);\r\n        switch (item.type) {\r\n            case \"update\":\r\n                update(keyInfo, items, item.key, item.data, true, immutable, logError);\r\n                break;\r\n            case \"insert\":\r\n                insert(keyInfo, items, item.data, useInsertIndex && isDefined(item.index) ? item.index : -1, true, logError, skipCopying);\r\n                break;\r\n            case \"remove\":\r\n                remove(keyInfo, items, item.key, true, logError)\r\n        }\r\n    }));\r\n    return resultItems\r\n}\r\n\r\nfunction getErrorResult(isBatch, logError, errorCode) {\r\n    return !isBatch ? rejectedPromise(errors.Error(errorCode)) : logError && errors.log(errorCode)\r\n}\r\n\r\nfunction applyChanges(data, changes) {\r\n    let options = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};\r\n    const {\r\n        keyExpr: keyExpr = \"id\",\r\n        immutable: immutable = true\r\n    } = options;\r\n    const keyGetter = compileGetter(keyExpr);\r\n    const keyInfo = {\r\n        key: () => keyExpr,\r\n        keyOf: obj => keyGetter(obj)\r\n    };\r\n    return applyBatch({\r\n        keyInfo: keyInfo,\r\n        data: data,\r\n        changes: changes,\r\n        immutable: immutable,\r\n        disableCache: true,\r\n        logError: true\r\n    })\r\n}\r\n\r\nfunction update(keyInfo, array, key, data, isBatch, immutable, logError) {\r\n    let target;\r\n    const keyExpr = keyInfo.key();\r\n    if (keyExpr) {\r\n        if (hasKey(data, keyExpr) && !keysEqual(keyExpr, key, keyInfo.keyOf(data))) {\r\n            return getErrorResult(isBatch, logError, \"E4017\")\r\n        }\r\n        target = getCacheValue(array, key);\r\n        if (!target) {\r\n            const index = indexByKey(keyInfo, array, key);\r\n            if (index < 0) {\r\n                return getErrorResult(isBatch, logError, \"E4009\")\r\n            }\r\n            target = array[index];\r\n            if (true === immutable && isDefined(target)) {\r\n                const newTarget = createObjectWithChanges(target, data);\r\n                array[index] = newTarget;\r\n                return !isBatch && trivialPromise(newTarget, key)\r\n            }\r\n        }\r\n    } else {\r\n        target = key\r\n    }\r\n    deepExtendArraySafe(target, data, true, false, true, true);\r\n    if (!isBatch) {\r\n        if (config().useLegacyStoreResult) {\r\n            return trivialPromise(key, data)\r\n        }\r\n        return trivialPromise(target, key)\r\n    }\r\n}\r\n\r\nfunction insert(keyInfo, array, data, index, isBatch, logError, skipCopying) {\r\n    let keyValue;\r\n    const keyExpr = keyInfo.key();\r\n    const obj = isPlainObject(data) && !skipCopying ? extend({}, data) : data;\r\n    if (keyExpr) {\r\n        keyValue = keyInfo.keyOf(obj);\r\n        if (void 0 === keyValue || \"object\" === typeof keyValue && isEmptyObject(keyValue)) {\r\n            if (Array.isArray(keyExpr)) {\r\n                throw errors.Error(\"E4007\")\r\n            }\r\n            keyValue = obj[keyExpr] = String(new Guid)\r\n        } else if (void 0 !== array[indexByKey(keyInfo, array, keyValue)]) {\r\n            return getErrorResult(isBatch, logError, \"E4008\")\r\n        }\r\n    } else {\r\n        keyValue = obj\r\n    }\r\n    if (index >= 0) {\r\n        array.splice(index, 0, obj)\r\n    } else {\r\n        array.push(obj)\r\n    }\r\n    setDataByKeyMapValue(array, keyValue, obj);\r\n    if (!isBatch) {\r\n        return trivialPromise(config().useLegacyStoreResult ? data : obj, keyValue)\r\n    }\r\n}\r\n\r\nfunction remove(keyInfo, array, key, isBatch, logError) {\r\n    const index = indexByKey(keyInfo, array, key);\r\n    if (index > -1) {\r\n        array.splice(index, 1);\r\n        setDataByKeyMapValue(array, key, null)\r\n    }\r\n    if (!isBatch) {\r\n        return trivialPromise(key)\r\n    }\r\n    if (index < 0) {\r\n        return getErrorResult(isBatch, logError, \"E4009\")\r\n    }\r\n}\r\n\r\nfunction indexByKey(keyInfo, array, key) {\r\n    const keyExpr = keyInfo.key();\r\n    if (!getHasKeyCacheValue(array, key)) {\r\n        return -1\r\n    }\r\n    for (let i = 0, arrayLength = array.length; i < arrayLength; i++) {\r\n        if (keysEqual(keyExpr, keyInfo.keyOf(array[i]), key)) {\r\n            return i\r\n        }\r\n    }\r\n    return -1\r\n}\r\nexport {\r\n    applyBatch,\r\n    applyChanges,\r\n    createObjectWithChanges,\r\n    indexByKey,\r\n    insert,\r\n    remove,\r\n    update\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;AACD;AACA;AAAA;AAGA;AAAA;AAKA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;;;;;;AAOA,SAAS,OAAO,MAAM,EAAE,SAAS;IAC7B,IAAI;IACJ,MAAM,OAAO,aAAa,OAAO,YAAY,UAAU,KAAK,KAAK,UAAU,KAAK;IAChF,MAAO,KAAK,MAAM,CAAE;QAChB,MAAM,KAAK,KAAK;QAChB,IAAI,OAAO,QAAQ;YACf,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,SAAS,UAAU,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU;IAC9C,IAAI;IACJ,IAAI;IACJ,IAAI,YAAY;QACZ,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,aAAa,KAAK,CAAC,EAAE,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE,CAAC,cAAc,IAAI,EAAE;YAC5D,SAAS,UAAU,SAAS,cAAc,EAAE,EAAE,KAAK,aAAa;YAChE,IAAI,QAAQ;gBACR,OAAO;YACX;QACJ;IACJ,OAAO,IAAI,WAAW,SAAS,OAAO,QAAQ,GAAG;QAC7C,OAAO;IACX;AACJ;AAEA,SAAS,SAAS,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,UAAU;IAC7C,IAAI,YAAY;QACZ,OAAO,UAAU,SAAS,OAAO,KAAK,eAAe,EAAE;IAC3D;IACA,OAAO;AACX;AAEA,SAAS,qBAAqB,OAAO,EAAE,KAAK;IACxC,IAAI,QAAQ,GAAG,MAAM,CAAC,CAAC,MAAM,aAAa,IAAI,MAAM,mBAAmB,KAAK,MAAM,MAAM,GAAG;QACvF,MAAM,eAAe,CAAC;QACtB,MAAM,cAAc,MAAM,MAAM;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YAClC,YAAY,CAAC,KAAK,SAAS,CAAC,QAAQ,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,GAAG,KAAK,CAAC,EAAE;QACpE;QACA,MAAM,aAAa,GAAG;QACtB,MAAM,mBAAmB,GAAG;IAChC;AACJ;AAEA,SAAS,cAAc,KAAK,EAAE,GAAG;IAC7B,IAAI,MAAM,aAAa,EAAE;QACrB,OAAO,MAAM,aAAa,CAAC,KAAK,SAAS,CAAC,KAAK;IACnD;AACJ;AAEA,SAAS,oBAAoB,KAAK,EAAE,GAAG;IACnC,IAAI,MAAM,aAAa,EAAE;QACrB,OAAO,MAAM,aAAa,CAAC,KAAK,SAAS,CAAC,KAAK;IACnD;IACA,OAAO;AACX;AAEA,SAAS,qBAAqB,KAAK,EAAE,GAAG,EAAE,IAAI;IAC1C,IAAI,MAAM,aAAa,EAAE;QACrB,MAAM,aAAa,CAAC,KAAK,SAAS,CAAC,KAAK,GAAG;QAC3C,MAAM,mBAAmB,IAAI,OAAO,IAAI,CAAC;IAC7C;AACJ;AAEA,SAAS,8BAA8B,QAAQ,EAAE,OAAO,EAAE,eAAe;IACrE,kBAAkB,mBAAmB,IAAI;IACzC,MAAM,SAAS,WAAW,OAAO,MAAM,CAAC,OAAO,cAAc,CAAC,aAAa,CAAC;IAC5E,IAAI,UAAU;QACV,gBAAgB,GAAG,CAAC,UAAU;IAClC;IACA,MAAM,2BAA2B,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;IAC9C,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,0BAA0B,MAAM,MAAM;IAClE,IAAK,MAAM,QAAQ,yBAA0B;QACzC,MAAM,QAAQ,wBAAwB,CAAC,KAAK;QAC5C,MAAM,SAAS,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,OAAO,CAAC,KAAK;QAC9E,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,CAAC,gBAAgB,GAAG,CAAC,QAAQ;YAC7F,MAAM,CAAC,KAAK,GAAG,8BAA8B,OAAO,QAAQ;QAChE;IACJ;IACA,IAAK,MAAM,QAAQ,OAAQ;QACvB,MAAM,OAAO,MAAM,CAAC,KAAK;QACzB,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,gBAAgB,GAAG,CAAC,OAAO;YAC7C,MAAM,CAAC,KAAK,GAAG,gBAAgB,GAAG,CAAC;QACvC;IACJ;IACA,OAAO;AACX;AAEA,SAAS,wBAAwB,MAAM,EAAE,OAAO;IAC5C,MAAM,SAAS,8BAA8B,QAAQ;IACrD,OAAO,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,SAAS,MAAM,MAAM;AAC5D;AAEA,SAAS,WAAW,IAAI;IACpB,IAAI,EACA,SAAS,OAAO,EAChB,MAAM,IAAI,EACV,SAAS,OAAO,EAChB,YAAY,UAAU,EACtB,gBAAgB,cAAc,EAC9B,WAAW,SAAS,EACpB,cAAc,YAAY,EAC1B,UAAU,QAAQ,EAClB,aAAa,WAAW,EAC3B,GAAG;IACJ,MAAM,cAAc,SAAS,YAAY;WAAI;KAAK,GAAG;IACrD,QAAQ,OAAO,CAAE,CAAA;QACb,MAAM,QAAQ,aAAa,KAAK,IAAI,GAAG,cAAc,SAAS,SAAS,aAAa,KAAK,GAAG,EAAE;QAC9F,CAAC,gBAAgB,qBAAqB,SAAS;QAC/C,OAAQ,KAAK,IAAI;YACb,KAAK;gBACD,OAAO,SAAS,OAAO,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,MAAM,WAAW;gBAC7D;YACJ,KAAK;gBACD,OAAO,SAAS,OAAO,KAAK,IAAI,EAAE,kBAAkB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,CAAC,GAAG,MAAM,UAAU;gBAC7G;YACJ,KAAK;gBACD,OAAO,SAAS,OAAO,KAAK,GAAG,EAAE,MAAM;QAC/C;IACJ;IACA,OAAO;AACX;AAEA,SAAS,eAAe,OAAO,EAAE,QAAQ,EAAE,SAAS;IAChD,OAAO,CAAC,UAAU,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD,EAAE,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC,cAAc,YAAY,sKAAA,CAAA,SAAM,CAAC,GAAG,CAAC;AACxF;AAEA,SAAS,aAAa,IAAI,EAAE,OAAO;IAC/B,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;IAChF,MAAM,EACF,SAAS,UAAU,IAAI,EACvB,WAAW,YAAY,IAAI,EAC9B,GAAG;IACJ,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;IAChC,MAAM,UAAU;QACZ,KAAK,IAAM;QACX,OAAO,CAAA,MAAO,UAAU;IAC5B;IACA,OAAO,WAAW;QACd,SAAS;QACT,MAAM;QACN,SAAS;QACT,WAAW;QACX,cAAc;QACd,UAAU;IACd;AACJ;AAEA,SAAS,OAAO,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;IACnE,IAAI;IACJ,MAAM,UAAU,QAAQ,GAAG;IAC3B,IAAI,SAAS;QACT,IAAI,OAAO,MAAM,YAAY,CAAC,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,SAAS,KAAK,QAAQ,KAAK,CAAC,QAAQ;YACxE,OAAO,eAAe,SAAS,UAAU;QAC7C;QACA,SAAS,cAAc,OAAO;QAC9B,IAAI,CAAC,QAAQ;YACT,MAAM,QAAQ,WAAW,SAAS,OAAO;YACzC,IAAI,QAAQ,GAAG;gBACX,OAAO,eAAe,SAAS,UAAU;YAC7C;YACA,SAAS,KAAK,CAAC,MAAM;YACrB,IAAI,SAAS,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;gBACzC,MAAM,YAAY,wBAAwB,QAAQ;gBAClD,KAAK,CAAC,MAAM,GAAG;gBACf,OAAO,CAAC,WAAW,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,WAAW;YACjD;QACJ;IACJ,OAAO;QACH,SAAS;IACb;IACA,CAAA,GAAA,+KAAA,CAAA,sBAAmB,AAAD,EAAE,QAAQ,MAAM,MAAM,OAAO,MAAM;IACrD,IAAI,CAAC,SAAS;QACV,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAM,AAAD,IAAI,oBAAoB,EAAE;YAC/B,OAAO,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,KAAK;QAC/B;QACA,OAAO,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAClC;AACJ;AAEA,SAAS,OAAO,OAAO,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,EAAE,WAAW;IACvE,IAAI;IACJ,MAAM,UAAU,QAAQ,GAAG;IAC3B,MAAM,MAAM,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAC,cAAc,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ;IACrE,IAAI,SAAS;QACT,WAAW,QAAQ,KAAK,CAAC;QACzB,IAAI,KAAK,MAAM,YAAY,aAAa,OAAO,YAAY,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YAChF,IAAI,MAAM,OAAO,CAAC,UAAU;gBACxB,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;YACvB;YACA,WAAW,GAAG,CAAC,QAAQ,GAAG,OAAO,IAAI,iJAAA,CAAA,UAAI;QAC7C,OAAO,IAAI,KAAK,MAAM,KAAK,CAAC,WAAW,SAAS,OAAO,UAAU,EAAE;YAC/D,OAAO,eAAe,SAAS,UAAU;QAC7C;IACJ,OAAO;QACH,WAAW;IACf;IACA,IAAI,SAAS,GAAG;QACZ,MAAM,MAAM,CAAC,OAAO,GAAG;IAC3B,OAAO;QACH,MAAM,IAAI,CAAC;IACf;IACA,qBAAqB,OAAO,UAAU;IACtC,IAAI,CAAC,SAAS;QACV,OAAO,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,CAAA,GAAA,mJAAA,CAAA,UAAM,AAAD,IAAI,oBAAoB,GAAG,OAAO,KAAK;IACtE;AACJ;AAEA,SAAS,OAAO,OAAO,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO,EAAE,QAAQ;IAClD,MAAM,QAAQ,WAAW,SAAS,OAAO;IACzC,IAAI,QAAQ,CAAC,GAAG;QACZ,MAAM,MAAM,CAAC,OAAO;QACpB,qBAAqB,OAAO,KAAK;IACrC;IACA,IAAI,CAAC,SAAS;QACV,OAAO,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE;IAC1B;IACA,IAAI,QAAQ,GAAG;QACX,OAAO,eAAe,SAAS,UAAU;IAC7C;AACJ;AAEA,SAAS,WAAW,OAAO,EAAE,KAAK,EAAE,GAAG;IACnC,MAAM,UAAU,QAAQ,GAAG;IAC3B,IAAI,CAAC,oBAAoB,OAAO,MAAM;QAClC,OAAO,CAAC;IACZ;IACA,IAAK,IAAI,IAAI,GAAG,cAAc,MAAM,MAAM,EAAE,IAAI,aAAa,IAAK;QAC9D,IAAI,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,KAAK,CAAC,KAAK,CAAC,EAAE,GAAG,MAAM;YAClD,OAAO;QACX;IACJ;IACA,OAAO,CAAC;AACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_array_query.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_array_query.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    errors,\r\n    handleError as handleDataError\r\n} from \"../../common/data/errors\";\r\nimport {\r\n    aggregators,\r\n    isConjunctiveOperator as isConjunctiveOperator<PERSON><PERSON><PERSON>,\r\n    isGroupCriterion,\r\n    isUnaryOperation,\r\n    isUniformEqualsByOr,\r\n    normalizeBinaryCriterion\r\n} from \"../../common/data/utils\";\r\nimport Class from \"../../core/class\";\r\nimport {\r\n    compileGetter,\r\n    toComparable\r\n} from \"../../core/utils/data\";\r\nimport {\r\n    Deferred\r\n} from \"../../core/utils/deferred\";\r\nimport {\r\n    each,\r\n    map\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isString\r\n} from \"../../core/utils/type\";\r\nconst Iterator = Class.inherit({\r\n    toArray() {\r\n        const result = [];\r\n        this.reset();\r\n        while (this.next()) {\r\n            result.push(this.current())\r\n        }\r\n        return result\r\n    },\r\n    countable: () => false\r\n});\r\nconst ArrayIterator = Iterator.inherit({\r\n    ctor(array) {\r\n        this.array = array;\r\n        this.index = -1\r\n    },\r\n    next() {\r\n        if (this.index + 1 < this.array.length) {\r\n            this.index++;\r\n            return true\r\n        }\r\n        return false\r\n    },\r\n    current() {\r\n        return this.array[this.index]\r\n    },\r\n    reset() {\r\n        this.index = -1\r\n    },\r\n    toArray() {\r\n        return this.array.slice(0)\r\n    },\r\n    countable: () => true,\r\n    count() {\r\n        return this.array.length\r\n    }\r\n});\r\nconst WrappedIterator = Iterator.inherit({\r\n    ctor(iter) {\r\n        this.iter = iter\r\n    },\r\n    next() {\r\n        return this.iter.next()\r\n    },\r\n    current() {\r\n        return this.iter.current()\r\n    },\r\n    reset() {\r\n        return this.iter.reset()\r\n    }\r\n});\r\nconst MapIterator = WrappedIterator.inherit({\r\n    ctor(iter, mapper) {\r\n        this.callBase(iter);\r\n        this.index = -1;\r\n        this.mapper = mapper\r\n    },\r\n    current() {\r\n        return this.mapper(this.callBase(), this.index)\r\n    },\r\n    next() {\r\n        const hasNext = this.callBase();\r\n        if (hasNext) {\r\n            this.index++\r\n        }\r\n        return hasNext\r\n    }\r\n});\r\nconst defaultCompare = function(xValue, yValue, options) {\r\n    if (isString(xValue) && isString(yValue) && (null !== options && void 0 !== options && options.locale || null !== options && void 0 !== options && options.collatorOptions)) {\r\n        return new Intl.Collator((null === options || void 0 === options ? void 0 : options.locale) || void 0, (null === options || void 0 === options ? void 0 : options.collatorOptions) || void 0).compare(xValue, yValue)\r\n    }\r\n    xValue = toComparable(xValue, false, options);\r\n    yValue = toComparable(yValue, false, options);\r\n    if (null === xValue && null !== yValue) {\r\n        return -1\r\n    }\r\n    if (null !== xValue && null === yValue) {\r\n        return 1\r\n    }\r\n    if (void 0 === xValue && void 0 !== yValue) {\r\n        return 1\r\n    }\r\n    if (void 0 !== xValue && void 0 === yValue) {\r\n        return -1\r\n    }\r\n    if (xValue < yValue) {\r\n        return -1\r\n    }\r\n    if (xValue > yValue) {\r\n        return 1\r\n    }\r\n    return 0\r\n};\r\nconst SortIterator = Iterator.inherit({\r\n    ctor(iter, getter, desc, compare) {\r\n        this.langParams = iter.langParams;\r\n        if (!(iter instanceof MapIterator)) {\r\n            iter = new MapIterator(iter, this._wrap);\r\n            iter.langParams = this.langParams\r\n        }\r\n        this.iter = iter;\r\n        this.rules = [{\r\n            getter: getter,\r\n            desc: desc,\r\n            compare: compare,\r\n            langParams: this.langParams\r\n        }]\r\n    },\r\n    thenBy(getter, desc, compare) {\r\n        const result = new SortIterator(this.sortedIter || this.iter, getter, desc, compare);\r\n        if (!this.sortedIter) {\r\n            result.rules = this.rules.concat(result.rules)\r\n        }\r\n        return result\r\n    },\r\n    next() {\r\n        this._ensureSorted();\r\n        return this.sortedIter.next()\r\n    },\r\n    current() {\r\n        this._ensureSorted();\r\n        return this.sortedIter.current()\r\n    },\r\n    reset() {\r\n        delete this.sortedIter\r\n    },\r\n    countable() {\r\n        return this.sortedIter || this.iter.countable()\r\n    },\r\n    count() {\r\n        if (this.sortedIter) {\r\n            return this.sortedIter.count()\r\n        }\r\n        return this.iter.count()\r\n    },\r\n    _ensureSorted() {\r\n        const that = this;\r\n        if (that.sortedIter) {\r\n            return\r\n        }\r\n        each(that.rules, (function() {\r\n            this.getter = compileGetter(this.getter)\r\n        }));\r\n        that.sortedIter = new MapIterator(new ArrayIterator(this.iter.toArray().sort(((x, y) => that._compare(x, y)))), that._unwrap)\r\n    },\r\n    _wrap: (record, index) => ({\r\n        index: index,\r\n        value: record\r\n    }),\r\n    _unwrap: wrappedItem => wrappedItem.value,\r\n    _getDefaultCompare: langParams => (xValue, yValue) => defaultCompare(xValue, yValue, langParams),\r\n    _compare(x, y) {\r\n        const xIndex = x.index;\r\n        const yIndex = y.index;\r\n        x = x.value;\r\n        y = y.value;\r\n        if (x === y) {\r\n            return xIndex - yIndex\r\n        }\r\n        for (let i = 0, rulesCount = this.rules.length; i < rulesCount; i++) {\r\n            const rule = this.rules[i];\r\n            const xValue = rule.getter(x);\r\n            const yValue = rule.getter(y);\r\n            const compare = rule.compare || this._getDefaultCompare(rule.langParams);\r\n            const compareResult = compare(xValue, yValue);\r\n            if (compareResult) {\r\n                return rule.desc ? -compareResult : compareResult\r\n            }\r\n        }\r\n        return xIndex - yIndex\r\n    }\r\n});\r\nconst compileCriteria = function() {\r\n    let langParams = {};\r\n    const _toComparable = value => toComparable(value, false, langParams);\r\n    const compileGroup = function(crit) {\r\n        if (isUniformEqualsByOr(crit)) {\r\n            return (crit => {\r\n                const getter = compileGetter(crit[0][0]);\r\n                const filterValues = crit.reduce(((acc, item, i) => {\r\n                    if (i % 2 === 0) {\r\n                        acc.push(_toComparable(item[2]))\r\n                    }\r\n                    return acc\r\n                }), []);\r\n                return obj => {\r\n                    const value = _toComparable(getter(obj));\r\n                    return filterValues.some((filterValue => useStrictComparison(filterValue) ? value === filterValue : value == filterValue))\r\n                }\r\n            })(crit)\r\n        }\r\n        const ops = [];\r\n        let isConjunctiveOperator = false;\r\n        let isConjunctiveNextOperator = false;\r\n        each(crit, (function() {\r\n            if (Array.isArray(this) || isFunction(this)) {\r\n                if (ops.length > 1 && isConjunctiveOperator !== isConjunctiveNextOperator) {\r\n                    throw errors.Error(\"E4019\")\r\n                }\r\n                ops.push(compileCriteria(this, langParams));\r\n                isConjunctiveOperator = isConjunctiveNextOperator;\r\n                isConjunctiveNextOperator = true\r\n            } else {\r\n                isConjunctiveNextOperator = isConjunctiveOperatorChecker(this)\r\n            }\r\n        }));\r\n        return function(d) {\r\n            let result = isConjunctiveOperator;\r\n            for (let i = 0; i < ops.length; i++) {\r\n                if (ops[i](d) !== isConjunctiveOperator) {\r\n                    result = !isConjunctiveOperator;\r\n                    break\r\n                }\r\n            }\r\n            return result\r\n        }\r\n    };\r\n    const toString = function(value) {\r\n        var _langParams;\r\n        return isDefined(value) ? null !== (_langParams = langParams) && void 0 !== _langParams && _langParams.locale ? value.toLocaleString(langParams.locale) : value.toString() : \"\"\r\n    };\r\n\r\n    function compileEquals(getter, value, negate) {\r\n        return function(obj) {\r\n            obj = _toComparable(getter(obj));\r\n            let result = useStrictComparison(value) ? obj === value : obj == value;\r\n            if (negate) {\r\n                result = !result\r\n            }\r\n            return result\r\n        }\r\n    }\r\n\r\n    function useStrictComparison(value) {\r\n        return \"\" === value || 0 === value || false === value\r\n    }\r\n    return function(crit, options) {\r\n        langParams = options || {};\r\n        if (isFunction(crit)) {\r\n            return crit\r\n        }\r\n        if (isGroupCriterion(crit)) {\r\n            return compileGroup(crit)\r\n        }\r\n        if (isUnaryOperation(crit)) {\r\n            return function(crit) {\r\n                const op = crit[0];\r\n                const criteria = compileCriteria(crit[1], langParams);\r\n                if (\"!\" === op) {\r\n                    return function(obj) {\r\n                        return !criteria(obj)\r\n                    }\r\n                }\r\n                throw errors.Error(\"E4003\", op)\r\n            }(crit)\r\n        }\r\n        return function(crit) {\r\n            crit = normalizeBinaryCriterion(crit);\r\n            const getter = compileGetter(crit[0]);\r\n            const op = crit[1];\r\n            let value = crit[2];\r\n            value = _toComparable(value);\r\n            const compare = (obj, operatorFn) => {\r\n                obj = _toComparable(getter(obj));\r\n                return (null == value || null == obj) && value !== obj ? false : operatorFn(obj, value)\r\n            };\r\n            switch (op.toLowerCase()) {\r\n                case \"=\":\r\n                    return compileEquals(getter, value);\r\n                case \"<>\":\r\n                    return compileEquals(getter, value, true);\r\n                case \">\":\r\n                    return obj => compare(obj, ((a, b) => a > b));\r\n                case \"<\":\r\n                    return obj => compare(obj, ((a, b) => a < b));\r\n                case \">=\":\r\n                    return obj => compare(obj, ((a, b) => a >= b));\r\n                case \"<=\":\r\n                    return obj => compare(obj, ((a, b) => a <= b));\r\n                case \"startswith\":\r\n                    return obj => _toComparable(toString(getter(obj))).startsWith(value);\r\n                case \"endswith\":\r\n                    return obj => _toComparable(toString(getter(obj))).endsWith(value);\r\n                case \"contains\":\r\n                    return obj => _toComparable(toString(getter(obj))).includes(value);\r\n                case \"notcontains\":\r\n                    return obj => !_toComparable(toString(getter(obj))).includes(value)\r\n            }\r\n            throw errors.Error(\"E4003\", op)\r\n        }(crit)\r\n    }\r\n}();\r\nconst FilterIterator = WrappedIterator.inherit({\r\n    ctor(iter, criteria) {\r\n        this.callBase(iter);\r\n        this.langParams = iter.langParams;\r\n        this.criteria = compileCriteria(criteria, this.langParams)\r\n    },\r\n    next() {\r\n        while (this.iter.next()) {\r\n            if (this.criteria(this.current())) {\r\n                return true\r\n            }\r\n        }\r\n        return false\r\n    }\r\n});\r\nconst GroupIterator = Iterator.inherit({\r\n    ctor(iter, getter) {\r\n        this.iter = iter;\r\n        this.getter = getter\r\n    },\r\n    next() {\r\n        this._ensureGrouped();\r\n        return this.groupedIter.next()\r\n    },\r\n    current() {\r\n        this._ensureGrouped();\r\n        return this.groupedIter.current()\r\n    },\r\n    reset() {\r\n        delete this.groupedIter\r\n    },\r\n    countable() {\r\n        return !!this.groupedIter\r\n    },\r\n    count() {\r\n        return this.groupedIter.count()\r\n    },\r\n    _ensureGrouped() {\r\n        if (this.groupedIter) {\r\n            return\r\n        }\r\n        const hash = {};\r\n        const keys = [];\r\n        const {\r\n            iter: iter\r\n        } = this;\r\n        const getter = compileGetter(this.getter);\r\n        iter.reset();\r\n        while (iter.next()) {\r\n            const current = iter.current();\r\n            const key = getter(current);\r\n            if (key in hash) {\r\n                hash[key].push(current)\r\n            } else {\r\n                hash[key] = [current];\r\n                keys.push(key)\r\n            }\r\n        }\r\n        this.groupedIter = new ArrayIterator(map(keys, (key => ({\r\n            key: key,\r\n            items: hash[key]\r\n        }))))\r\n    }\r\n});\r\nconst SelectIterator = WrappedIterator.inherit({\r\n    ctor(iter, getter) {\r\n        this.callBase(iter);\r\n        this.getter = compileGetter(getter)\r\n    },\r\n    current() {\r\n        return this.getter(this.callBase())\r\n    },\r\n    countable() {\r\n        return this.iter.countable()\r\n    },\r\n    count() {\r\n        return this.iter.count()\r\n    }\r\n});\r\nconst SliceIterator = WrappedIterator.inherit({\r\n    ctor(iter, skip, take) {\r\n        this.callBase(iter);\r\n        this.skip = Math.max(0, skip);\r\n        this.take = Math.max(0, take);\r\n        this.pos = 0\r\n    },\r\n    next() {\r\n        if (this.pos >= this.skip + this.take) {\r\n            return false\r\n        }\r\n        while (this.pos < this.skip && this.iter.next()) {\r\n            this.pos++\r\n        }\r\n        this.pos++;\r\n        return this.iter.next()\r\n    },\r\n    reset() {\r\n        this.callBase();\r\n        this.pos = 0\r\n    },\r\n    countable() {\r\n        return this.iter.countable()\r\n    },\r\n    count() {\r\n        return Math.min(this.iter.count() - this.skip, this.take)\r\n    }\r\n});\r\nconst arrayQueryImpl = function(iter, queryOptions) {\r\n    queryOptions = queryOptions || {};\r\n    if (!(iter instanceof Iterator)) {\r\n        iter = new ArrayIterator(iter)\r\n    }\r\n    if (queryOptions.langParams) {\r\n        iter.langParams = queryOptions.langParams\r\n    }\r\n    const handleError = function(error) {\r\n        const handler = queryOptions.errorHandler;\r\n        if (handler) {\r\n            handler(error)\r\n        }\r\n        handleDataError(error)\r\n    };\r\n    const aggregateCore = function(aggregator) {\r\n        const d = (new Deferred).fail(handleError);\r\n        let seed;\r\n        const {\r\n            step: step\r\n        } = aggregator;\r\n        const {\r\n            finalize: finalize\r\n        } = aggregator;\r\n        try {\r\n            iter.reset();\r\n            if (\"seed\" in aggregator) {\r\n                seed = aggregator.seed\r\n            } else {\r\n                seed = iter.next() ? iter.current() : NaN\r\n            }\r\n            let accumulator = seed;\r\n            while (iter.next()) {\r\n                accumulator = step(accumulator, iter.current())\r\n            }\r\n            d.resolve(finalize ? finalize(accumulator) : accumulator)\r\n        } catch (x) {\r\n            d.reject(x)\r\n        }\r\n        return d.promise()\r\n    };\r\n    const standardAggregate = function(name) {\r\n        return aggregateCore(aggregators[name])\r\n    };\r\n    const select = function(getter) {\r\n        if (!isFunction(getter) && !Array.isArray(getter)) {\r\n            getter = [].slice.call(arguments)\r\n        }\r\n        return chainQuery(new SelectIterator(iter, getter))\r\n    };\r\n    const selectProp = function(name) {\r\n        return select(compileGetter(name))\r\n    };\r\n\r\n    function chainQuery(iter) {\r\n        return arrayQueryImpl(iter, queryOptions)\r\n    }\r\n    return {\r\n        toArray: () => iter.toArray(),\r\n        enumerate() {\r\n            const d = (new Deferred).fail(handleError);\r\n            try {\r\n                d.resolve(iter.toArray())\r\n            } catch (x) {\r\n                d.reject(x)\r\n            }\r\n            return d.promise()\r\n        },\r\n        setLangParams(options) {\r\n            iter.langParams = options\r\n        },\r\n        sortBy: (getter, desc, compare) => chainQuery(new SortIterator(iter, getter, desc, compare)),\r\n        thenBy(getter, desc, compare) {\r\n            if (iter instanceof SortIterator) {\r\n                return chainQuery(iter.thenBy(getter, desc, compare))\r\n            }\r\n            throw errors.Error(\"E4004\")\r\n        },\r\n        filter(criteria) {\r\n            if (!Array.isArray(criteria)) {\r\n                criteria = [].slice.call(arguments)\r\n            }\r\n            return chainQuery(new FilterIterator(iter, criteria))\r\n        },\r\n        slice(skip, take) {\r\n            if (void 0 === take) {\r\n                take = Number.MAX_VALUE\r\n            }\r\n            return chainQuery(new SliceIterator(iter, skip, take))\r\n        },\r\n        select: select,\r\n        groupBy: getter => chainQuery(new GroupIterator(iter, getter)),\r\n        aggregate: function(seed, step, finalize) {\r\n            if (arguments.length < 2) {\r\n                return aggregateCore({\r\n                    step: arguments[0]\r\n                })\r\n            }\r\n            return aggregateCore({\r\n                seed: seed,\r\n                step: step,\r\n                finalize: finalize\r\n            })\r\n        },\r\n        count() {\r\n            if (iter.countable()) {\r\n                const d = (new Deferred).fail(handleError);\r\n                try {\r\n                    d.resolve(iter.count())\r\n                } catch (x) {\r\n                    d.reject(x)\r\n                }\r\n                return d.promise()\r\n            }\r\n            return standardAggregate(\"count\")\r\n        },\r\n        sum(getter) {\r\n            if (getter) {\r\n                return selectProp(getter).sum()\r\n            }\r\n            return standardAggregate(\"sum\")\r\n        },\r\n        min(getter) {\r\n            if (getter) {\r\n                return selectProp(getter).min()\r\n            }\r\n            return standardAggregate(\"min\")\r\n        },\r\n        max(getter) {\r\n            if (getter) {\r\n                return selectProp(getter).max()\r\n            }\r\n            return standardAggregate(\"max\")\r\n        },\r\n        avg(getter) {\r\n            if (getter) {\r\n                return selectProp(getter).avg()\r\n            }\r\n            return standardAggregate(\"avg\")\r\n        }\r\n    }\r\n};\r\nexport default arrayQueryImpl;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAIA;AAAA;AAQA;AACA;AAAA;AAIA;AAAA;AAGA;AAAA;AAIA;AAAA;;;;;;;;AAKA,MAAM,WAAW,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAC3B;QACI,MAAM,SAAS,EAAE;QACjB,IAAI,CAAC,KAAK;QACV,MAAO,IAAI,CAAC,IAAI,GAAI;YAChB,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;QAC5B;QACA,OAAO;IACX;IACA,WAAW,IAAM;AACrB;AACA,MAAM,gBAAgB,SAAS,OAAO,CAAC;IACnC,MAAK,KAAK;QACN,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,KAAK,GAAG,CAAC;IAClB;IACA;QACI,IAAI,IAAI,CAAC,KAAK,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;YACpC,IAAI,CAAC,KAAK;YACV,OAAO;QACX;QACA,OAAO;IACX;IACA;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;IACjC;IACA;QACI,IAAI,CAAC,KAAK,GAAG,CAAC;IAClB;IACA;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC;IAC5B;IACA,WAAW,IAAM;IACjB;QACI,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM;IAC5B;AACJ;AACA,MAAM,kBAAkB,SAAS,OAAO,CAAC;IACrC,MAAK,IAAI;QACL,IAAI,CAAC,IAAI,GAAG;IAChB;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACzB;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO;IAC5B;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;IAC1B;AACJ;AACA,MAAM,cAAc,gBAAgB,OAAO,CAAC;IACxC,MAAK,IAAI,EAAE,MAAM;QACb,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,KAAK,GAAG,CAAC;QACd,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,KAAK;IAClD;IACA;QACI,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,SAAS;YACT,IAAI,CAAC,KAAK;QACd;QACA,OAAO;IACX;AACJ;AACA,MAAM,iBAAiB,SAAS,MAAM,EAAE,MAAM,EAAE,OAAO;IACnD,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,CAAC,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,MAAM,IAAI,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,eAAe,GAAG;QACzK,OAAO,IAAI,KAAK,QAAQ,CAAC,CAAC,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,MAAM,KAAK,KAAK,GAAG,CAAC,SAAS,WAAW,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,eAAe,KAAK,KAAK,GAAG,OAAO,CAAC,QAAQ;IAClN;IACA,SAAS,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;IACrC,SAAS,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,OAAO;IACrC,IAAI,SAAS,UAAU,SAAS,QAAQ;QACpC,OAAO,CAAC;IACZ;IACA,IAAI,SAAS,UAAU,SAAS,QAAQ;QACpC,OAAO;IACX;IACA,IAAI,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ;QACxC,OAAO;IACX;IACA,IAAI,KAAK,MAAM,UAAU,KAAK,MAAM,QAAQ;QACxC,OAAO,CAAC;IACZ;IACA,IAAI,SAAS,QAAQ;QACjB,OAAO,CAAC;IACZ;IACA,IAAI,SAAS,QAAQ;QACjB,OAAO;IACX;IACA,OAAO;AACX;AACA,MAAM,eAAe,SAAS,OAAO,CAAC;IAClC,MAAK,IAAI,EAAE,MAAM,EAAE,IAAI,EAAE,OAAO;QAC5B,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,CAAC,gBAAgB,WAAW,GAAG;YAChC,OAAO,IAAI,YAAY,MAAM,IAAI,CAAC,KAAK;YACvC,KAAK,UAAU,GAAG,IAAI,CAAC,UAAU;QACrC;QACA,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;YAAC;gBACV,QAAQ;gBACR,MAAM;gBACN,SAAS;gBACT,YAAY,IAAI,CAAC,UAAU;YAC/B;SAAE;IACN;IACA,QAAO,MAAM,EAAE,IAAI,EAAE,OAAO;QACxB,MAAM,SAAS,IAAI,aAAa,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,EAAE,QAAQ,MAAM;QAC5E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,OAAO,KAAK;QACjD;QACA,OAAO;IACX;IACA;QACI,IAAI,CAAC,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI;IAC/B;IACA;QACI,IAAI,CAAC,aAAa;QAClB,OAAO,IAAI,CAAC,UAAU,CAAC,OAAO;IAClC;IACA;QACI,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA;QACI,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,IAAI,CAAC,SAAS;IACjD;IACA;QACI,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC,UAAU,CAAC,KAAK;QAChC;QACA,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;IAC1B;IACA;QACI,MAAM,OAAO,IAAI;QACjB,IAAI,KAAK,UAAU,EAAE;YACjB;QACJ;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,KAAK,KAAK,EAAG;YACd,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,MAAM;QAC3C;QACA,KAAK,UAAU,GAAG,IAAI,YAAY,IAAI,cAAc,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAE,CAAC,GAAG,IAAM,KAAK,QAAQ,CAAC,GAAG,MAAO,KAAK,OAAO;IAChI;IACA,OAAO,CAAC,QAAQ,QAAU,CAAC;YACvB,OAAO;YACP,OAAO;QACX,CAAC;IACD,SAAS,CAAA,cAAe,YAAY,KAAK;IACzC,oBAAoB,CAAA,aAAc,CAAC,QAAQ,SAAW,eAAe,QAAQ,QAAQ;IACrF,UAAS,CAAC,EAAE,CAAC;QACT,MAAM,SAAS,EAAE,KAAK;QACtB,MAAM,SAAS,EAAE,KAAK;QACtB,IAAI,EAAE,KAAK;QACX,IAAI,EAAE,KAAK;QACX,IAAI,MAAM,GAAG;YACT,OAAO,SAAS;QACpB;QACA,IAAK,IAAI,IAAI,GAAG,aAAa,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,IAAI,YAAY,IAAK;YACjE,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B,MAAM,SAAS,KAAK,MAAM,CAAC;YAC3B,MAAM,SAAS,KAAK,MAAM,CAAC;YAC3B,MAAM,UAAU,KAAK,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,KAAK,UAAU;YACvE,MAAM,gBAAgB,QAAQ,QAAQ;YACtC,IAAI,eAAe;gBACf,OAAO,KAAK,IAAI,GAAG,CAAC,gBAAgB;YACxC;QACJ;QACA,OAAO,SAAS;IACpB;AACJ;AACA,MAAM,kBAAkB;IACpB,IAAI,aAAa,CAAC;IAClB,MAAM,gBAAgB,CAAA,QAAS,CAAA,GAAA,6KAAA,CAAA,eAAY,AAAD,EAAE,OAAO,OAAO;IAC1D,MAAM,eAAe,SAAS,IAAI;QAC9B,IAAI,CAAA,GAAA,qKAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO;YAC3B,OAAO,CAAC,CAAA;gBACJ,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,EAAE,CAAC,EAAE;gBACvC,MAAM,eAAe,KAAK,MAAM,CAAE,CAAC,KAAK,MAAM;oBAC1C,IAAI,IAAI,MAAM,GAAG;wBACb,IAAI,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE;oBAClC;oBACA,OAAO;gBACX,GAAI,EAAE;gBACN,OAAO,CAAA;oBACH,MAAM,QAAQ,cAAc,OAAO;oBACnC,OAAO,aAAa,IAAI,CAAE,CAAA,cAAe,oBAAoB,eAAe,UAAU,cAAc,SAAS;gBACjH;YACJ,CAAC,EAAE;QACP;QACA,MAAM,MAAM,EAAE;QACd,IAAI,wBAAwB;QAC5B,IAAI,4BAA4B;QAChC,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,MAAO;YACR,IAAI,MAAM,OAAO,CAAC,IAAI,KAAK,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,IAAI,GAAG;gBACzC,IAAI,IAAI,MAAM,GAAG,KAAK,0BAA0B,2BAA2B;oBACvE,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;gBACvB;gBACA,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE;gBAC/B,wBAAwB;gBACxB,4BAA4B;YAChC,OAAO;gBACH,4BAA4B,CAAA,GAAA,qKAAA,CAAA,wBAA4B,AAAD,EAAE,IAAI;YACjE;QACJ;QACA,OAAO,SAAS,CAAC;YACb,IAAI,SAAS;YACb,IAAK,IAAI,IAAI,GAAG,IAAI,IAAI,MAAM,EAAE,IAAK;gBACjC,IAAI,GAAG,CAAC,EAAE,CAAC,OAAO,uBAAuB;oBACrC,SAAS,CAAC;oBACV;gBACJ;YACJ;YACA,OAAO;QACX;IACJ;IACA,MAAM,WAAW,SAAS,KAAK;QAC3B,IAAI;QACJ,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,SAAS,CAAC,cAAc,UAAU,KAAK,KAAK,MAAM,eAAe,YAAY,MAAM,GAAG,MAAM,cAAc,CAAC,WAAW,MAAM,IAAI,MAAM,QAAQ,KAAK;IACjL;IAEA,SAAS,cAAc,MAAM,EAAE,KAAK,EAAE,MAAM;QACxC,OAAO,SAAS,GAAG;YACf,MAAM,cAAc,OAAO;YAC3B,IAAI,SAAS,oBAAoB,SAAS,QAAQ,QAAQ,OAAO;YACjE,IAAI,QAAQ;gBACR,SAAS,CAAC;YACd;YACA,OAAO;QACX;IACJ;IAEA,SAAS,oBAAoB,KAAK;QAC9B,OAAO,OAAO,SAAS,MAAM,SAAS,UAAU;IACpD;IACA,OAAO,SAAS,IAAI,EAAE,OAAO;QACzB,aAAa,WAAW,CAAC;QACzB,IAAI,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,OAAO;YAClB,OAAO;QACX;QACA,IAAI,CAAA,GAAA,qKAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YACxB,OAAO,aAAa;QACxB;QACA,IAAI,CAAA,GAAA,qKAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO;YACxB,OAAO,SAAS,IAAI;gBAChB,MAAM,KAAK,IAAI,CAAC,EAAE;gBAClB,MAAM,WAAW,gBAAgB,IAAI,CAAC,EAAE,EAAE;gBAC1C,IAAI,QAAQ,IAAI;oBACZ,OAAO,SAAS,GAAG;wBACf,OAAO,CAAC,SAAS;oBACrB;gBACJ;gBACA,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS;YAChC,EAAE;QACN;QACA,OAAO,SAAS,IAAI;YAChB,OAAO,CAAA,GAAA,qKAAA,CAAA,2BAAwB,AAAD,EAAE;YAChC,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,EAAE;YACpC,MAAM,KAAK,IAAI,CAAC,EAAE;YAClB,IAAI,QAAQ,IAAI,CAAC,EAAE;YACnB,QAAQ,cAAc;YACtB,MAAM,UAAU,CAAC,KAAK;gBAClB,MAAM,cAAc,OAAO;gBAC3B,OAAO,CAAC,QAAQ,SAAS,QAAQ,GAAG,KAAK,UAAU,MAAM,QAAQ,WAAW,KAAK;YACrF;YACA,OAAQ,GAAG,WAAW;gBAClB,KAAK;oBACD,OAAO,cAAc,QAAQ;gBACjC,KAAK;oBACD,OAAO,cAAc,QAAQ,OAAO;gBACxC,KAAK;oBACD,OAAO,CAAA,MAAO,QAAQ,KAAM,CAAC,GAAG,IAAM,IAAI;gBAC9C,KAAK;oBACD,OAAO,CAAA,MAAO,QAAQ,KAAM,CAAC,GAAG,IAAM,IAAI;gBAC9C,KAAK;oBACD,OAAO,CAAA,MAAO,QAAQ,KAAM,CAAC,GAAG,IAAM,KAAK;gBAC/C,KAAK;oBACD,OAAO,CAAA,MAAO,QAAQ,KAAM,CAAC,GAAG,IAAM,KAAK;gBAC/C,KAAK;oBACD,OAAO,CAAA,MAAO,cAAc,SAAS,OAAO,OAAO,UAAU,CAAC;gBAClE,KAAK;oBACD,OAAO,CAAA,MAAO,cAAc,SAAS,OAAO,OAAO,QAAQ,CAAC;gBAChE,KAAK;oBACD,OAAO,CAAA,MAAO,cAAc,SAAS,OAAO,OAAO,QAAQ,CAAC;gBAChE,KAAK;oBACD,OAAO,CAAA,MAAO,CAAC,cAAc,SAAS,OAAO,OAAO,QAAQ,CAAC;YACrE;YACA,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS;QAChC,EAAE;IACN;AACJ;AACA,MAAM,iBAAiB,gBAAgB,OAAO,CAAC;IAC3C,MAAK,IAAI,EAAE,QAAQ;QACf,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,UAAU,GAAG,KAAK,UAAU;QACjC,IAAI,CAAC,QAAQ,GAAG,gBAAgB,UAAU,IAAI,CAAC,UAAU;IAC7D;IACA;QACI,MAAO,IAAI,CAAC,IAAI,CAAC,IAAI,GAAI;YACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO,KAAK;gBAC/B,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM,gBAAgB,SAAS,OAAO,CAAC;IACnC,MAAK,IAAI,EAAE,MAAM;QACb,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;QACI,IAAI,CAAC,cAAc;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAChC;IACA;QACI,IAAI,CAAC,cAAc;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO;IACnC;IACA;QACI,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA;QACI,OAAO,CAAC,CAAC,IAAI,CAAC,WAAW;IAC7B;IACA;QACI,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IACjC;IACA;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB;QACJ;QACA,MAAM,OAAO,CAAC;QACd,MAAM,OAAO,EAAE;QACf,MAAM,EACF,MAAM,IAAI,EACb,GAAG,IAAI;QACR,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,MAAM;QACxC,KAAK,KAAK;QACV,MAAO,KAAK,IAAI,GAAI;YAChB,MAAM,UAAU,KAAK,OAAO;YAC5B,MAAM,MAAM,OAAO;YACnB,IAAI,OAAO,MAAM;gBACb,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACnB,OAAO;gBACH,IAAI,CAAC,IAAI,GAAG;oBAAC;iBAAQ;gBACrB,KAAK,IAAI,CAAC;YACd;QACJ;QACA,IAAI,CAAC,WAAW,GAAG,IAAI,cAAc,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE,MAAO,CAAA,MAAO,CAAC;gBACpD,KAAK;gBACL,OAAO,IAAI,CAAC,IAAI;YACpB,CAAC;IACL;AACJ;AACA,MAAM,iBAAiB,gBAAgB,OAAO,CAAC;IAC3C,MAAK,IAAI,EAAE,MAAM;QACb,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;IAChC;IACA;QACI,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;IACpC;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;IAC9B;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,KAAK;IAC1B;AACJ;AACA,MAAM,gBAAgB,gBAAgB,OAAO,CAAC;IAC1C,MAAK,IAAI,EAAE,IAAI,EAAE,IAAI;QACjB,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG;QACxB,IAAI,CAAC,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG;QACxB,IAAI,CAAC,GAAG,GAAG;IACf;IACA;QACI,IAAI,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,EAAE;YACnC,OAAO;QACX;QACA,MAAO,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,GAAI;YAC7C,IAAI,CAAC,GAAG;QACZ;QACA,IAAI,CAAC,GAAG;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACzB;IACA;QACI,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,GAAG,GAAG;IACf;IACA;QACI,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;IAC9B;IACA;QACI,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;IAC5D;AACJ;AACA,MAAM,iBAAiB,SAAS,IAAI,EAAE,YAAY;IAC9C,eAAe,gBAAgB,CAAC;IAChC,IAAI,CAAC,CAAC,gBAAgB,QAAQ,GAAG;QAC7B,OAAO,IAAI,cAAc;IAC7B;IACA,IAAI,aAAa,UAAU,EAAE;QACzB,KAAK,UAAU,GAAG,aAAa,UAAU;IAC7C;IACA,MAAM,cAAc,SAAS,KAAK;QAC9B,MAAM,UAAU,aAAa,YAAY;QACzC,IAAI,SAAS;YACT,QAAQ;QACZ;QACA,CAAA,GAAA,sKAAA,CAAA,cAAe,AAAD,EAAE;IACpB;IACA,MAAM,gBAAgB,SAAS,UAAU;QACrC,MAAM,IAAI,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,IAAI,CAAC;QAC9B,IAAI;QACJ,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;QACJ,IAAI;YACA,KAAK,KAAK;YACV,IAAI,UAAU,YAAY;gBACtB,OAAO,WAAW,IAAI;YAC1B,OAAO;gBACH,OAAO,KAAK,IAAI,KAAK,KAAK,OAAO,KAAK;YAC1C;YACA,IAAI,cAAc;YAClB,MAAO,KAAK,IAAI,GAAI;gBAChB,cAAc,KAAK,aAAa,KAAK,OAAO;YAChD;YACA,EAAE,OAAO,CAAC,WAAW,SAAS,eAAe;QACjD,EAAE,OAAO,GAAG;YACR,EAAE,MAAM,CAAC;QACb;QACA,OAAO,EAAE,OAAO;IACpB;IACA,MAAM,oBAAoB,SAAS,IAAI;QACnC,OAAO,cAAc,qKAAA,CAAA,cAAW,CAAC,KAAK;IAC1C;IACA,MAAM,SAAS,SAAS,MAAM;QAC1B,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,WAAW,CAAC,MAAM,OAAO,CAAC,SAAS;YAC/C,SAAS,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B;QACA,OAAO,WAAW,IAAI,eAAe,MAAM;IAC/C;IACA,MAAM,aAAa,SAAS,IAAI;QAC5B,OAAO,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;IAChC;IAEA,SAAS,WAAW,IAAI;QACpB,OAAO,eAAe,MAAM;IAChC;IACA,OAAO;QACH,SAAS,IAAM,KAAK,OAAO;QAC3B;YACI,MAAM,IAAI,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,IAAI,CAAC;YAC9B,IAAI;gBACA,EAAE,OAAO,CAAC,KAAK,OAAO;YAC1B,EAAE,OAAO,GAAG;gBACR,EAAE,MAAM,CAAC;YACb;YACA,OAAO,EAAE,OAAO;QACpB;QACA,eAAc,OAAO;YACjB,KAAK,UAAU,GAAG;QACtB;QACA,QAAQ,CAAC,QAAQ,MAAM,UAAY,WAAW,IAAI,aAAa,MAAM,QAAQ,MAAM;QACnF,QAAO,MAAM,EAAE,IAAI,EAAE,OAAO;YACxB,IAAI,gBAAgB,cAAc;gBAC9B,OAAO,WAAW,KAAK,MAAM,CAAC,QAAQ,MAAM;YAChD;YACA,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;QACvB;QACA,QAAO,QAAQ;YACX,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW;gBAC1B,WAAW,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;YAC7B;YACA,OAAO,WAAW,IAAI,eAAe,MAAM;QAC/C;QACA,OAAM,IAAI,EAAE,IAAI;YACZ,IAAI,KAAK,MAAM,MAAM;gBACjB,OAAO,OAAO,SAAS;YAC3B;YACA,OAAO,WAAW,IAAI,cAAc,MAAM,MAAM;QACpD;QACA,QAAQ;QACR,SAAS,CAAA,SAAU,WAAW,IAAI,cAAc,MAAM;QACtD,WAAW,SAAS,IAAI,EAAE,IAAI,EAAE,QAAQ;YACpC,IAAI,UAAU,MAAM,GAAG,GAAG;gBACtB,OAAO,cAAc;oBACjB,MAAM,SAAS,CAAC,EAAE;gBACtB;YACJ;YACA,OAAO,cAAc;gBACjB,MAAM;gBACN,MAAM;gBACN,UAAU;YACd;QACJ;QACA;YACI,IAAI,KAAK,SAAS,IAAI;gBAClB,MAAM,IAAI,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,IAAI,CAAC;gBAC9B,IAAI;oBACA,EAAE,OAAO,CAAC,KAAK,KAAK;gBACxB,EAAE,OAAO,GAAG;oBACR,EAAE,MAAM,CAAC;gBACb;gBACA,OAAO,EAAE,OAAO;YACpB;YACA,OAAO,kBAAkB;QAC7B;QACA,KAAI,MAAM;YACN,IAAI,QAAQ;gBACR,OAAO,WAAW,QAAQ,GAAG;YACjC;YACA,OAAO,kBAAkB;QAC7B;QACA,KAAI,MAAM;YACN,IAAI,QAAQ;gBACR,OAAO,WAAW,QAAQ,GAAG;YACjC;YACA,OAAO,kBAAkB;QAC7B;QACA,KAAI,MAAM;YACN,IAAI,QAAQ;gBACR,OAAO,WAAW,QAAQ,GAAG;YACjC;YACA,OAAO,kBAAkB;QAC7B;QACA,KAAI,MAAM;YACN,IAAI,QAAQ;gBACR,OAAO,WAAW,QAAQ,GAAG;YACjC;YACA,OAAO,kBAAkB;QAC7B;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_store_helper.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_store_helper.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport arrayQuery from \"../../common/data/array_query\";\r\nimport {\r\n    normalizeSortingInfo\r\n} from \"../../common/data/utils\";\r\nimport {\r\n    grep\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\n\r\nfunction multiLevelGroup(query, groupInfo) {\r\n    query = query.groupBy(groupInfo[0].selector);\r\n    if (groupInfo.length > 1) {\r\n        query = query.select((g => extend({}, g, {\r\n            items: multiLevelGroup(arrayQuery(g.items), groupInfo.slice(1)).toArray()\r\n        })))\r\n    }\r\n    return query\r\n}\r\n\r\nfunction arrangeSortingInfo(groupInfo, sortInfo) {\r\n    const filteredGroup = [];\r\n    each(groupInfo, ((_, group) => {\r\n        const collision = grep(sortInfo, (sort => group.selector === sort.selector));\r\n        if (collision.length < 1) {\r\n            filteredGroup.push(group)\r\n        }\r\n    }));\r\n    return filteredGroup.concat(sortInfo)\r\n}\r\n\r\nfunction queryByOptions(query, options, isCountQuery) {\r\n    var _options;\r\n    options = options || {};\r\n    const {\r\n        filter: filter\r\n    } = options;\r\n    if (null !== (_options = options) && void 0 !== _options && _options.langParams) {\r\n        var _query$setLangParams, _query;\r\n        null === (_query$setLangParams = (_query = query).setLangParams) || void 0 === _query$setLangParams || _query$setLangParams.call(_query, options.langParams)\r\n    }\r\n    if (filter) {\r\n        query = query.filter(filter)\r\n    }\r\n    if (isCountQuery) {\r\n        return query\r\n    }\r\n    let {\r\n        sort: sort\r\n    } = options;\r\n    const {\r\n        select: select\r\n    } = options;\r\n    let {\r\n        group: group\r\n    } = options;\r\n    const {\r\n        skip: skip\r\n    } = options;\r\n    const {\r\n        take: take\r\n    } = options;\r\n    if (group) {\r\n        group = normalizeSortingInfo(group);\r\n        group.keepInitialKeyOrder = !!options.group.keepInitialKeyOrder\r\n    }\r\n    if (sort || group) {\r\n        sort = normalizeSortingInfo(sort || []);\r\n        if (group && !group.keepInitialKeyOrder) {\r\n            sort = arrangeSortingInfo(group, sort)\r\n        }\r\n        each(sort, (function(index) {\r\n            query = query[index ? \"thenBy\" : \"sortBy\"](this.selector, this.desc, this.compare)\r\n        }))\r\n    }\r\n    if (select) {\r\n        query = query.select(select)\r\n    }\r\n    if (group) {\r\n        query = multiLevelGroup(query, group)\r\n    }\r\n    if (take || skip) {\r\n        query = query.slice(skip || 0, take)\r\n    }\r\n    return query\r\n}\r\nexport default {\r\n    multiLevelGroup: multiLevelGroup,\r\n    arrangeSortingInfo: arrangeSortingInfo,\r\n    queryByOptions: queryByOptions\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;;AAIA,SAAS,gBAAgB,KAAK,EAAE,SAAS;IACrC,QAAQ,MAAM,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,QAAQ;IAC3C,IAAI,UAAU,MAAM,GAAG,GAAG;QACtB,QAAQ,MAAM,MAAM,CAAE,CAAA,IAAK,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,GAAG;gBACrC,OAAO,gBAAgB,CAAA,GAAA,2KAAA,CAAA,UAAU,AAAD,EAAE,EAAE,KAAK,GAAG,UAAU,KAAK,CAAC,IAAI,OAAO;YAC3E;IACJ;IACA,OAAO;AACX;AAEA,SAAS,mBAAmB,SAAS,EAAE,QAAQ;IAC3C,MAAM,gBAAgB,EAAE;IACxB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,WAAY,CAAC,GAAG;QACjB,MAAM,YAAY,CAAA,GAAA,+KAAA,CAAA,OAAI,AAAD,EAAE,UAAW,CAAA,OAAQ,MAAM,QAAQ,KAAK,KAAK,QAAQ;QAC1E,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,cAAc,IAAI,CAAC;QACvB;IACJ;IACA,OAAO,cAAc,MAAM,CAAC;AAChC;AAEA,SAAS,eAAe,KAAK,EAAE,OAAO,EAAE,YAAY;IAChD,IAAI;IACJ,UAAU,WAAW,CAAC;IACtB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;IACJ,IAAI,SAAS,CAAC,WAAW,OAAO,KAAK,KAAK,MAAM,YAAY,SAAS,UAAU,EAAE;QAC7E,IAAI,sBAAsB;QAC1B,SAAS,CAAC,uBAAuB,CAAC,SAAS,KAAK,EAAE,aAAa,KAAK,KAAK,MAAM,wBAAwB,qBAAqB,IAAI,CAAC,QAAQ,QAAQ,UAAU;IAC/J;IACA,IAAI,QAAQ;QACR,QAAQ,MAAM,MAAM,CAAC;IACzB;IACA,IAAI,cAAc;QACd,OAAO;IACX;IACA,IAAI,EACA,MAAM,IAAI,EACb,GAAG;IACJ,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;IACJ,IAAI,EACA,OAAO,KAAK,EACf,GAAG;IACJ,MAAM,EACF,MAAM,IAAI,EACb,GAAG;IACJ,MAAM,EACF,MAAM,IAAI,EACb,GAAG;IACJ,IAAI,OAAO;QACP,QAAQ,CAAA,GAAA,qKAAA,CAAA,uBAAoB,AAAD,EAAE;QAC7B,MAAM,mBAAmB,GAAG,CAAC,CAAC,QAAQ,KAAK,CAAC,mBAAmB;IACnE;IACA,IAAI,QAAQ,OAAO;QACf,OAAO,CAAA,GAAA,qKAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ,EAAE;QACtC,IAAI,SAAS,CAAC,MAAM,mBAAmB,EAAE;YACrC,OAAO,mBAAmB,OAAO;QACrC;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,MAAO,SAAS,KAAK;YACtB,QAAQ,KAAK,CAAC,QAAQ,WAAW,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,OAAO;QACrF;IACJ;IACA,IAAI,QAAQ;QACR,QAAQ,MAAM,MAAM,CAAC;IACzB;IACA,IAAI,OAAO;QACP,QAAQ,gBAAgB,OAAO;IACnC;IACA,IAAI,QAAQ,MAAM;QACd,QAAQ,MAAM,KAAK,CAAC,QAAQ,GAAG;IACnC;IACA,OAAO;AACX;uCACe;IACX,iBAAiB;IACjB,oBAAoB;IACpB,gBAAgB;AACpB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1267, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_abstract_store.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_abstract_store.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    errors,\r\n    handleError\r\n} from \"../../common/data/errors\";\r\nimport storeHelper from \"../../common/data/store_helper\";\r\nimport {\r\n    processRequestResultLock\r\n} from \"../../common/data/utils\";\r\nimport Class from \"../../core/class\";\r\nimport {\r\n    EventsStrategy\r\n} from \"../../core/events_strategy\";\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    compileGetter\r\n} from \"../../core/utils/data\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../core/utils/deferred\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    isEmptyObject\r\n} from \"../../core/utils/type\";\r\nconst {\r\n    abstract: abstract\r\n} = Class;\r\nconst {\r\n    queryByOptions: queryByOptions\r\n} = storeHelper;\r\nconst storeImpl = {};\r\nconst Store = Class.inherit({\r\n    _langParams: {},\r\n    ctor(options) {\r\n        const that = this;\r\n        options = options || {};\r\n        this._eventsStrategy = new EventsStrategy(this);\r\n        each([\"onLoaded\", \"onLoading\", \"onInserted\", \"onInserting\", \"onUpdated\", \"onUpdating\", \"onPush\", \"onRemoved\", \"onRemoving\", \"onModified\", \"onModifying\"], ((_, optionName) => {\r\n            if (optionName in options) {\r\n                that.on(optionName.slice(2).toLowerCase(), options[optionName])\r\n            }\r\n        }));\r\n        this._key = options.key;\r\n        this._errorHandler = options.errorHandler;\r\n        this._useDefaultSearch = true\r\n    },\r\n    _clearCache: noop,\r\n    _customLoadOptions: () => null,\r\n    key() {\r\n        return this._key\r\n    },\r\n    keyOf(obj) {\r\n        if (!this._keyGetter) {\r\n            this._keyGetter = compileGetter(this.key())\r\n        }\r\n        return this._keyGetter(obj)\r\n    },\r\n    _requireKey() {\r\n        if (!this.key()) {\r\n            throw errors.Error(\"E4005\")\r\n        }\r\n    },\r\n    load(options) {\r\n        const that = this;\r\n        options = options || {};\r\n        this._eventsStrategy.fireEvent(\"loading\", [options]);\r\n        return this._withLock(this._loadImpl(options)).done((result => {\r\n            that._eventsStrategy.fireEvent(\"loaded\", [result, options])\r\n        }))\r\n    },\r\n    _loadImpl(options) {\r\n        if (!isEmptyObject(this._langParams)) {\r\n            options = options || {};\r\n            options._langParams = _extends({}, this._langParams, options._langParams)\r\n        }\r\n        return queryByOptions(this.createQuery(options), options).enumerate()\r\n    },\r\n    _withLock(task) {\r\n        const result = new Deferred;\r\n        task.done((function() {\r\n            const that = this;\r\n            const args = arguments;\r\n            processRequestResultLock.promise().done((() => {\r\n                result.resolveWith(that, args)\r\n            }))\r\n        })).fail((function() {\r\n            result.rejectWith(this, arguments)\r\n        }));\r\n        return result\r\n    },\r\n    createQuery: abstract,\r\n    totalCount(options) {\r\n        return this._totalCountImpl(options)\r\n    },\r\n    _totalCountImpl(options) {\r\n        return queryByOptions(this.createQuery(options), options, true).count()\r\n    },\r\n    byKey(key, extraOptions) {\r\n        return this._addFailHandlers(this._withLock(this._byKeyImpl(key, extraOptions)))\r\n    },\r\n    _byKeyImpl: abstract,\r\n    insert(values) {\r\n        const that = this;\r\n        that._eventsStrategy.fireEvent(\"modifying\");\r\n        that._eventsStrategy.fireEvent(\"inserting\", [values]);\r\n        return that._addFailHandlers(that._insertImpl(values).done(((callbackValues, callbackKey) => {\r\n            that._eventsStrategy.fireEvent(\"inserted\", [callbackValues, callbackKey]);\r\n            that._eventsStrategy.fireEvent(\"modified\")\r\n        })))\r\n    },\r\n    _insertImpl: abstract,\r\n    update(key, values) {\r\n        const that = this;\r\n        that._eventsStrategy.fireEvent(\"modifying\");\r\n        that._eventsStrategy.fireEvent(\"updating\", [key, values]);\r\n        return that._addFailHandlers(that._updateImpl(key, values).done((() => {\r\n            that._eventsStrategy.fireEvent(\"updated\", [key, values]);\r\n            that._eventsStrategy.fireEvent(\"modified\")\r\n        })))\r\n    },\r\n    _updateImpl: abstract,\r\n    push(changes) {\r\n        const beforePushArgs = {\r\n            changes: changes,\r\n            waitFor: []\r\n        };\r\n        this._eventsStrategy.fireEvent(\"beforePushAggregation\", [beforePushArgs]);\r\n        when(...beforePushArgs.waitFor).done((() => {\r\n            this._pushImpl(changes);\r\n            this._eventsStrategy.fireEvent(\"beforePush\", [{\r\n                changes: changes\r\n            }]);\r\n            this._eventsStrategy.fireEvent(\"push\", [changes])\r\n        }))\r\n    },\r\n    _pushImpl: noop,\r\n    remove(key) {\r\n        const that = this;\r\n        that._eventsStrategy.fireEvent(\"modifying\");\r\n        that._eventsStrategy.fireEvent(\"removing\", [key]);\r\n        return that._addFailHandlers(that._removeImpl(key).done((callbackKey => {\r\n            that._eventsStrategy.fireEvent(\"removed\", [callbackKey]);\r\n            that._eventsStrategy.fireEvent(\"modified\")\r\n        })))\r\n    },\r\n    _removeImpl: abstract,\r\n    _addFailHandlers(deferred) {\r\n        return deferred.fail(this._errorHandler).fail(handleError)\r\n    },\r\n    on(eventName, eventHandler) {\r\n        this._eventsStrategy.on(eventName, eventHandler);\r\n        return this\r\n    },\r\n    off(eventName, eventHandler) {\r\n        this._eventsStrategy.off(eventName, eventHandler);\r\n        return this\r\n    }\r\n});\r\nStore.create = function(alias, options) {\r\n    if (!(alias in storeImpl)) {\r\n        throw errors.Error(\"E4020\", alias)\r\n    }\r\n    return new storeImpl[alias](options)\r\n};\r\nStore.registerClass = function(type, alias) {\r\n    if (alias) {\r\n        storeImpl[alias] = type\r\n    }\r\n    return type\r\n};\r\nStore.inherit = function(inheritor) {\r\n    return function(members, alias) {\r\n        const type = inheritor.apply(this, [members]);\r\n        Store.registerClass(type, alias);\r\n        return type\r\n    }\r\n}(Store.inherit);\r\nexport default Store;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAIA;AAAA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;;;;;;;;;;;;AAGA,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,kJAAA,CAAA,UAAK;AACT,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,4KAAA,CAAA,UAAW;AACf,MAAM,YAAY,CAAC;AACnB,MAAM,QAAQ,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IACxB,aAAa,CAAC;IACd,MAAK,OAAO;QACR,MAAM,OAAO,IAAI;QACjB,UAAU,WAAW,CAAC;QACtB,IAAI,CAAC,eAAe,GAAG,IAAI,+KAAA,CAAA,iBAAc,CAAC,IAAI;QAC9C,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE;YAAC;YAAY;YAAa;YAAc;YAAe;YAAa;YAAc;YAAU;YAAa;YAAc;YAAc;SAAc,EAAG,CAAC,GAAG;YAC3J,IAAI,cAAc,SAAS;gBACvB,KAAK,EAAE,CAAC,WAAW,KAAK,CAAC,GAAG,WAAW,IAAI,OAAO,CAAC,WAAW;YAClE;QACJ;QACA,IAAI,CAAC,IAAI,GAAG,QAAQ,GAAG;QACvB,IAAI,CAAC,aAAa,GAAG,QAAQ,YAAY;QACzC,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,aAAa,+KAAA,CAAA,OAAI;IACjB,oBAAoB,IAAM;IAC1B;QACI,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,OAAM,GAAG;QACL,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,GAAG;QAC5C;QACA,OAAO,IAAI,CAAC,UAAU,CAAC;IAC3B;IACA;QACI,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;YACb,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;QACvB;IACJ;IACA,MAAK,OAAO;QACR,MAAM,OAAO,IAAI;QACjB,UAAU,WAAW,CAAC;QACtB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,WAAW;YAAC;SAAQ;QACnD,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAE,CAAA;YACjD,KAAK,eAAe,CAAC,SAAS,CAAC,UAAU;gBAAC;gBAAQ;aAAQ;QAC9D;IACJ;IACA,WAAU,OAAO;QACb,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,WAAW,GAAG;YAClC,UAAU,WAAW,CAAC;YACtB,QAAQ,WAAW,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,WAAW,EAAE,QAAQ,WAAW;QAC5E;QACA,OAAO,eAAe,IAAI,CAAC,WAAW,CAAC,UAAU,SAAS,SAAS;IACvE;IACA,WAAU,IAAI;QACV,MAAM,SAAS,IAAI,iLAAA,CAAA,WAAQ;QAC3B,KAAK,IAAI,CAAE;YACP,MAAM,OAAO,IAAI;YACjB,MAAM,OAAO;YACb,qKAAA,CAAA,2BAAwB,CAAC,OAAO,GAAG,IAAI,CAAE;gBACrC,OAAO,WAAW,CAAC,MAAM;YAC7B;QACJ,GAAI,IAAI,CAAE;YACN,OAAO,UAAU,CAAC,IAAI,EAAE;QAC5B;QACA,OAAO;IACX;IACA,aAAa;IACb,YAAW,OAAO;QACd,OAAO,IAAI,CAAC,eAAe,CAAC;IAChC;IACA,iBAAgB,OAAO;QACnB,OAAO,eAAe,IAAI,CAAC,WAAW,CAAC,UAAU,SAAS,MAAM,KAAK;IACzE;IACA,OAAM,GAAG,EAAE,YAAY;QACnB,OAAO,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK;IACrE;IACA,YAAY;IACZ,QAAO,MAAM;QACT,MAAM,OAAO,IAAI;QACjB,KAAK,eAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,eAAe,CAAC,SAAS,CAAC,aAAa;YAAC;SAAO;QACpD,OAAO,KAAK,gBAAgB,CAAC,KAAK,WAAW,CAAC,QAAQ,IAAI,CAAE,CAAC,gBAAgB;YACzE,KAAK,eAAe,CAAC,SAAS,CAAC,YAAY;gBAAC;gBAAgB;aAAY;YACxE,KAAK,eAAe,CAAC,SAAS,CAAC;QACnC;IACJ;IACA,aAAa;IACb,QAAO,GAAG,EAAE,MAAM;QACd,MAAM,OAAO,IAAI;QACjB,KAAK,eAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,eAAe,CAAC,SAAS,CAAC,YAAY;YAAC;YAAK;SAAO;QACxD,OAAO,KAAK,gBAAgB,CAAC,KAAK,WAAW,CAAC,KAAK,QAAQ,IAAI,CAAE;YAC7D,KAAK,eAAe,CAAC,SAAS,CAAC,WAAW;gBAAC;gBAAK;aAAO;YACvD,KAAK,eAAe,CAAC,SAAS,CAAC;QACnC;IACJ;IACA,aAAa;IACb,MAAK,OAAO;QACR,MAAM,iBAAiB;YACnB,SAAS;YACT,SAAS,EAAE;QACf;QACA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,yBAAyB;YAAC;SAAe;QACxE,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,KAAK,eAAe,OAAO,EAAE,IAAI,CAAE;YAClC,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,cAAc;gBAAC;oBAC1C,SAAS;gBACb;aAAE;YACF,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,QAAQ;gBAAC;aAAQ;QACpD;IACJ;IACA,WAAW,+KAAA,CAAA,OAAI;IACf,QAAO,GAAG;QACN,MAAM,OAAO,IAAI;QACjB,KAAK,eAAe,CAAC,SAAS,CAAC;QAC/B,KAAK,eAAe,CAAC,SAAS,CAAC,YAAY;YAAC;SAAI;QAChD,OAAO,KAAK,gBAAgB,CAAC,KAAK,WAAW,CAAC,KAAK,IAAI,CAAE,CAAA;YACrD,KAAK,eAAe,CAAC,SAAS,CAAC,WAAW;gBAAC;aAAY;YACvD,KAAK,eAAe,CAAC,SAAS,CAAC;QACnC;IACJ;IACA,aAAa;IACb,kBAAiB,QAAQ;QACrB,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,IAAI,CAAC,sKAAA,CAAA,cAAW;IAC7D;IACA,IAAG,SAAS,EAAE,YAAY;QACtB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW;QACnC,OAAO,IAAI;IACf;IACA,KAAI,SAAS,EAAE,YAAY;QACvB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW;QACpC,OAAO,IAAI;IACf;AACJ;AACA,MAAM,MAAM,GAAG,SAAS,KAAK,EAAE,OAAO;IAClC,IAAI,CAAC,CAAC,SAAS,SAAS,GAAG;QACvB,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS;IAChC;IACA,OAAO,IAAI,SAAS,CAAC,MAAM,CAAC;AAChC;AACA,MAAM,aAAa,GAAG,SAAS,IAAI,EAAE,KAAK;IACtC,IAAI,OAAO;QACP,SAAS,CAAC,MAAM,GAAG;IACvB;IACA,OAAO;AACX;AACA,MAAM,OAAO,GAAG,SAAS,SAAS;IAC9B,OAAO,SAAS,OAAO,EAAE,KAAK;QAC1B,MAAM,OAAO,UAAU,KAAK,CAAC,IAAI,EAAE;YAAC;SAAQ;QAC5C,MAAM,aAAa,CAAC,MAAM;QAC1B,OAAO;IACX;AACJ,EAAE,MAAM,OAAO;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_custom_store.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_custom_store.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport arrayQuery from \"../../common/data/array_query\";\r\nimport {\r\n    applyBatch\r\n} from \"../../common/data/array_utils\";\r\nimport {\r\n    errors\r\n} from \"../../common/data/errors\";\r\nimport storeHelper from \"../../common/data/store_helper\";\r\nimport {\r\n    errorMessageFromXhr as errorMessageFromXhrUtility,\r\n    keysEqual,\r\n    XHR_ERROR_UNLOAD\r\n} from \"../../common/data/utils\";\r\nimport config from \"../../core/config\";\r\nimport $ from \"../../core/renderer\";\r\nimport {\r\n    Deferred,\r\n    fromPromise,\r\n    when\r\n} from \"../../core/utils/deferred\";\r\nimport {\r\n    isFunction\r\n} from \"../../core/utils/type\";\r\nimport Store from \"../../data/abstract_store\";\r\nconst TOTAL_COUNT = \"totalCount\";\r\nconst LOAD = \"load\";\r\nconst BY_KEY = \"byKey\";\r\nconst INSERT = \"insert\";\r\nconst UPDATE = \"update\";\r\nconst REMOVE = \"remove\";\r\n\r\nfunction isPromise(obj) {\r\n    return obj && isFunction(obj.then)\r\n}\r\n\r\nfunction trivialPromise(value) {\r\n    return (new Deferred).resolve(value).promise()\r\n}\r\n\r\nfunction ensureRequiredFuncOption(name, obj) {\r\n    if (!isFunction(obj)) {\r\n        throw errors.Error(\"E4011\", name)\r\n    }\r\n}\r\n\r\nfunction throwInvalidUserFuncResult(name) {\r\n    throw errors.Error(\"E4012\", name)\r\n}\r\n\r\nfunction createUserFuncFailureHandler(pendingDeferred) {\r\n    return function(arg) {\r\n        let error;\r\n        if (arg instanceof Error) {\r\n            error = arg\r\n        } else {\r\n            error = new Error(function(promiseArguments) {\r\n                const xhr = promiseArguments[0];\r\n                const textStatus = promiseArguments[1];\r\n                if (!xhr || !xhr.getResponseHeader) {\r\n                    return null\r\n                }\r\n                return errorMessageFromXhrUtility(xhr, textStatus)\r\n            }(arguments) || arg && String(arg) || \"Unknown error\")\r\n        }\r\n        if (error.message !== XHR_ERROR_UNLOAD) {\r\n            pendingDeferred.reject(error)\r\n        }\r\n    }\r\n}\r\n\r\nfunction invokeUserLoad(store, options) {\r\n    const userFunc = store._loadFunc;\r\n    let userResult;\r\n    ensureRequiredFuncOption(LOAD, userFunc);\r\n    userResult = userFunc.apply(store, [options]);\r\n    if (Array.isArray(userResult)) {\r\n        userResult = trivialPromise(userResult)\r\n    } else if (null === userResult || void 0 === userResult) {\r\n        userResult = trivialPromise([])\r\n    } else if (!isPromise(userResult)) {\r\n        throwInvalidUserFuncResult(LOAD)\r\n    }\r\n    return fromPromise(userResult)\r\n}\r\n\r\nfunction invokeUserTotalCountFunc(store, options) {\r\n    const userFunc = store._totalCountFunc;\r\n    let userResult;\r\n    if (!isFunction(userFunc)) {\r\n        throw errors.Error(\"E4021\")\r\n    }\r\n    userResult = userFunc.apply(store, [options]);\r\n    if (!isPromise(userResult)) {\r\n        userResult = Number(userResult);\r\n        if (!isFinite(userResult)) {\r\n            throwInvalidUserFuncResult(TOTAL_COUNT)\r\n        }\r\n        userResult = trivialPromise(userResult)\r\n    }\r\n    return fromPromise(userResult)\r\n}\r\n\r\nfunction invokeUserByKeyFunc(store, key, extraOptions) {\r\n    const userFunc = store._byKeyFunc;\r\n    let userResult;\r\n    ensureRequiredFuncOption(BY_KEY, userFunc);\r\n    userResult = userFunc.apply(store, [key, extraOptions]);\r\n    if (!isPromise(userResult)) {\r\n        userResult = trivialPromise(userResult)\r\n    }\r\n    return fromPromise(userResult)\r\n}\r\n\r\nfunction runRawLoad(pendingDeferred, store, userFuncOptions, continuation) {\r\n    if (store.__rawData) {\r\n        continuation(store.__rawData)\r\n    } else {\r\n        const loadPromise = store.__rawDataPromise || invokeUserLoad(store, userFuncOptions);\r\n        if (store._cacheRawData) {\r\n            store.__rawDataPromise = loadPromise\r\n        }\r\n        loadPromise.always((() => {\r\n            delete store.__rawDataPromise\r\n        })).done((rawData => {\r\n            if (store._cacheRawData) {\r\n                store.__rawData = rawData\r\n            }\r\n            continuation(rawData)\r\n        })).fail((error => {\r\n            var _store$_errorHandler;\r\n            const userFuncFailureHandler = createUserFuncFailureHandler(pendingDeferred);\r\n            null === (_store$_errorHandler = store._errorHandler) || void 0 === _store$_errorHandler || _store$_errorHandler.call(store, error);\r\n            userFuncFailureHandler(error)\r\n        }))\r\n    }\r\n}\r\n\r\nfunction runRawLoadWithQuery(pendingDeferred, store, options, countOnly) {\r\n    options = options || {};\r\n    const userFuncOptions = {};\r\n    if (\"userData\" in options) {\r\n        userFuncOptions.userData = options.userData\r\n    }\r\n    runRawLoad(pendingDeferred, store, userFuncOptions, (rawData => {\r\n        const rawDataQuery = arrayQuery(rawData, {\r\n            errorHandler: store._errorHandler\r\n        });\r\n        let itemsQuery;\r\n        let totalCountQuery;\r\n        const waitList = [];\r\n        let items;\r\n        let totalCount;\r\n        if (!countOnly) {\r\n            itemsQuery = storeHelper.queryByOptions(rawDataQuery, options);\r\n            if (itemsQuery === rawDataQuery) {\r\n                items = rawData.slice(0)\r\n            } else {\r\n                waitList.push(itemsQuery.enumerate().done((asyncResult => {\r\n                    items = asyncResult\r\n                })))\r\n            }\r\n        }\r\n        if (options.requireTotalCount || countOnly) {\r\n            totalCountQuery = storeHelper.queryByOptions(rawDataQuery, options, true);\r\n            if (totalCountQuery === rawDataQuery) {\r\n                totalCount = rawData.length\r\n            } else {\r\n                waitList.push(totalCountQuery.count().done((asyncResult => {\r\n                    totalCount = asyncResult\r\n                })))\r\n            }\r\n        }\r\n        when.apply($, waitList).done((() => {\r\n            if (countOnly) {\r\n                pendingDeferred.resolve(totalCount)\r\n            } else if (options.requireTotalCount) {\r\n                pendingDeferred.resolve(items, {\r\n                    totalCount: totalCount\r\n                })\r\n            } else {\r\n                pendingDeferred.resolve(items)\r\n            }\r\n        })).fail((x => {\r\n            pendingDeferred.reject(x)\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction runRawLoadWithKey(pendingDeferred, store, key) {\r\n    runRawLoad(pendingDeferred, store, {}, (rawData => {\r\n        const keyExpr = store.key();\r\n        let item;\r\n        for (let i = 0, len = rawData.length; i < len; i++) {\r\n            item = rawData[i];\r\n            if (keysEqual(keyExpr, store.keyOf(rawData[i]), key)) {\r\n                pendingDeferred.resolve(item);\r\n                return\r\n            }\r\n        }\r\n        pendingDeferred.reject(errors.Error(\"E4009\"))\r\n    }))\r\n}\r\nconst CustomStore = Store.inherit({\r\n    ctor(options) {\r\n        options = options || {};\r\n        this.callBase(options);\r\n        this._useDefaultSearch = !!options.useDefaultSearch || \"raw\" === options.loadMode;\r\n        this._loadMode = options.loadMode;\r\n        this._cacheRawData = false !== options.cacheRawData;\r\n        this._loadFunc = options[LOAD];\r\n        this._totalCountFunc = options[TOTAL_COUNT];\r\n        this._byKeyFunc = options[BY_KEY];\r\n        this._insertFunc = options[INSERT];\r\n        this._updateFunc = options[UPDATE];\r\n        this._removeFunc = options[REMOVE]\r\n    },\r\n    _clearCache() {\r\n        delete this.__rawData\r\n    },\r\n    createQuery() {\r\n        throw errors.Error(\"E4010\")\r\n    },\r\n    clearRawDataCache() {\r\n        this._clearCache()\r\n    },\r\n    _totalCountImpl(options) {\r\n        let d = new Deferred;\r\n        if (\"raw\" === this._loadMode && !this._totalCountFunc) {\r\n            runRawLoadWithQuery(d, this, options, true)\r\n        } else {\r\n            invokeUserTotalCountFunc(this, options).done((count => {\r\n                d.resolve(Number(count))\r\n            })).fail(createUserFuncFailureHandler(d));\r\n            d = this._addFailHandlers(d)\r\n        }\r\n        return d.promise()\r\n    },\r\n    _pushImpl(changes) {\r\n        if (this.__rawData) {\r\n            applyBatch({\r\n                keyInfo: this,\r\n                data: this.__rawData,\r\n                changes: changes\r\n            })\r\n        }\r\n    },\r\n    _loadImpl(options) {\r\n        let d = new Deferred;\r\n        if (\"raw\" === this._loadMode) {\r\n            runRawLoadWithQuery(d, this, options, false)\r\n        } else {\r\n            invokeUserLoad(this, options).done(((data, extra) => {\r\n                d.resolve(data, extra)\r\n            })).fail(createUserFuncFailureHandler(d));\r\n            d = this._addFailHandlers(d)\r\n        }\r\n        return d.promise()\r\n    },\r\n    _byKeyImpl(key, extraOptions) {\r\n        const d = new Deferred;\r\n        if (this._byKeyViaLoad()) {\r\n            this._requireKey();\r\n            runRawLoadWithKey(d, this, key)\r\n        } else {\r\n            invokeUserByKeyFunc(this, key, extraOptions).done((obj => {\r\n                d.resolve(obj)\r\n            })).fail(createUserFuncFailureHandler(d))\r\n        }\r\n        return d.promise()\r\n    },\r\n    _byKeyViaLoad() {\r\n        return \"raw\" === this._loadMode && !this._byKeyFunc\r\n    },\r\n    _insertImpl(values) {\r\n        const that = this;\r\n        const userFunc = that._insertFunc;\r\n        let userResult;\r\n        const d = new Deferred;\r\n        ensureRequiredFuncOption(INSERT, userFunc);\r\n        userResult = userFunc.apply(that, [values]);\r\n        if (!isPromise(userResult)) {\r\n            userResult = trivialPromise(userResult)\r\n        }\r\n        fromPromise(userResult).done((serverResponse => {\r\n            if (config().useLegacyStoreResult) {\r\n                d.resolve(values, serverResponse)\r\n            } else {\r\n                d.resolve(serverResponse || values, that.keyOf(serverResponse))\r\n            }\r\n        })).fail(createUserFuncFailureHandler(d));\r\n        return d.promise()\r\n    },\r\n    _updateImpl(key, values) {\r\n        const userFunc = this._updateFunc;\r\n        let userResult;\r\n        const d = new Deferred;\r\n        ensureRequiredFuncOption(UPDATE, userFunc);\r\n        userResult = userFunc.apply(this, [key, values]);\r\n        if (!isPromise(userResult)) {\r\n            userResult = trivialPromise(userResult)\r\n        }\r\n        fromPromise(userResult).done((serverResponse => {\r\n            if (config().useLegacyStoreResult) {\r\n                d.resolve(key, values)\r\n            } else {\r\n                d.resolve(serverResponse || values, key)\r\n            }\r\n        })).fail(createUserFuncFailureHandler(d));\r\n        return d.promise()\r\n    },\r\n    _removeImpl(key) {\r\n        const userFunc = this._removeFunc;\r\n        let userResult;\r\n        const d = new Deferred;\r\n        ensureRequiredFuncOption(REMOVE, userFunc);\r\n        userResult = userFunc.apply(this, [key]);\r\n        if (!isPromise(userResult)) {\r\n            userResult = trivialPromise()\r\n        }\r\n        fromPromise(userResult).done((() => {\r\n            d.resolve(key)\r\n        })).fail(createUserFuncFailureHandler(d));\r\n        return d.promise()\r\n    }\r\n});\r\nexport default CustomStore;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AACA;AAAA;AAKA;AACA;AACA;AAAA;AAKA;AAAA;AAGA;AAAA;;;;;;;;;;;AACA,MAAM,cAAc;AACpB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AACf,MAAM,SAAS;AAEf,SAAS,UAAU,GAAG;IAClB,OAAO,OAAO,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,IAAI,IAAI;AACrC;AAEA,SAAS,eAAe,KAAK;IACzB,OAAO,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,OAAO,CAAC,OAAO,OAAO;AAChD;AAEA,SAAS,yBAAyB,IAAI,EAAE,GAAG;IACvC,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,MAAM;QAClB,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS;IAChC;AACJ;AAEA,SAAS,2BAA2B,IAAI;IACpC,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS;AAChC;AAEA,SAAS,6BAA6B,eAAe;IACjD,OAAO,SAAS,GAAG;QACf,IAAI;QACJ,IAAI,eAAe,OAAO;YACtB,QAAQ;QACZ,OAAO;YACH,QAAQ,IAAI,MAAM,SAAS,gBAAgB;gBACvC,MAAM,MAAM,gBAAgB,CAAC,EAAE;gBAC/B,MAAM,aAAa,gBAAgB,CAAC,EAAE;gBACtC,IAAI,CAAC,OAAO,CAAC,IAAI,iBAAiB,EAAE;oBAChC,OAAO;gBACX;gBACA,OAAO,CAAA,GAAA,qKAAA,CAAA,sBAA0B,AAAD,EAAE,KAAK;YAC3C,EAAE,cAAc,OAAO,OAAO,QAAQ;QAC1C;QACA,IAAI,MAAM,OAAO,KAAK,qKAAA,CAAA,mBAAgB,EAAE;YACpC,gBAAgB,MAAM,CAAC;QAC3B;IACJ;AACJ;AAEA,SAAS,eAAe,KAAK,EAAE,OAAO;IAClC,MAAM,WAAW,MAAM,SAAS;IAChC,IAAI;IACJ,yBAAyB,MAAM;IAC/B,aAAa,SAAS,KAAK,CAAC,OAAO;QAAC;KAAQ;IAC5C,IAAI,MAAM,OAAO,CAAC,aAAa;QAC3B,aAAa,eAAe;IAChC,OAAO,IAAI,SAAS,cAAc,KAAK,MAAM,YAAY;QACrD,aAAa,eAAe,EAAE;IAClC,OAAO,IAAI,CAAC,UAAU,aAAa;QAC/B,2BAA2B;IAC/B;IACA,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;AACvB;AAEA,SAAS,yBAAyB,KAAK,EAAE,OAAO;IAC5C,MAAM,WAAW,MAAM,eAAe;IACtC,IAAI;IACJ,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,WAAW;QACvB,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;IACvB;IACA,aAAa,SAAS,KAAK,CAAC,OAAO;QAAC;KAAQ;IAC5C,IAAI,CAAC,UAAU,aAAa;QACxB,aAAa,OAAO;QACpB,IAAI,CAAC,SAAS,aAAa;YACvB,2BAA2B;QAC/B;QACA,aAAa,eAAe;IAChC;IACA,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;AACvB;AAEA,SAAS,oBAAoB,KAAK,EAAE,GAAG,EAAE,YAAY;IACjD,MAAM,WAAW,MAAM,UAAU;IACjC,IAAI;IACJ,yBAAyB,QAAQ;IACjC,aAAa,SAAS,KAAK,CAAC,OAAO;QAAC;QAAK;KAAa;IACtD,IAAI,CAAC,UAAU,aAAa;QACxB,aAAa,eAAe;IAChC;IACA,OAAO,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE;AACvB;AAEA,SAAS,WAAW,eAAe,EAAE,KAAK,EAAE,eAAe,EAAE,YAAY;IACrE,IAAI,MAAM,SAAS,EAAE;QACjB,aAAa,MAAM,SAAS;IAChC,OAAO;QACH,MAAM,cAAc,MAAM,gBAAgB,IAAI,eAAe,OAAO;QACpE,IAAI,MAAM,aAAa,EAAE;YACrB,MAAM,gBAAgB,GAAG;QAC7B;QACA,YAAY,MAAM,CAAE;YAChB,OAAO,MAAM,gBAAgB;QACjC,GAAI,IAAI,CAAE,CAAA;YACN,IAAI,MAAM,aAAa,EAAE;gBACrB,MAAM,SAAS,GAAG;YACtB;YACA,aAAa;QACjB,GAAI,IAAI,CAAE,CAAA;YACN,IAAI;YACJ,MAAM,yBAAyB,6BAA6B;YAC5D,SAAS,CAAC,uBAAuB,MAAM,aAAa,KAAK,KAAK,MAAM,wBAAwB,qBAAqB,IAAI,CAAC,OAAO;YAC7H,uBAAuB;QAC3B;IACJ;AACJ;AAEA,SAAS,oBAAoB,eAAe,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS;IACnE,UAAU,WAAW,CAAC;IACtB,MAAM,kBAAkB,CAAC;IACzB,IAAI,cAAc,SAAS;QACvB,gBAAgB,QAAQ,GAAG,QAAQ,QAAQ;IAC/C;IACA,WAAW,iBAAiB,OAAO,iBAAkB,CAAA;QACjD,MAAM,eAAe,CAAA,GAAA,2KAAA,CAAA,UAAU,AAAD,EAAE,SAAS;YACrC,cAAc,MAAM,aAAa;QACrC;QACA,IAAI;QACJ,IAAI;QACJ,MAAM,WAAW,EAAE;QACnB,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,WAAW;YACZ,aAAa,4KAAA,CAAA,UAAW,CAAC,cAAc,CAAC,cAAc;YACtD,IAAI,eAAe,cAAc;gBAC7B,QAAQ,QAAQ,KAAK,CAAC;YAC1B,OAAO;gBACH,SAAS,IAAI,CAAC,WAAW,SAAS,GAAG,IAAI,CAAE,CAAA;oBACvC,QAAQ;gBACZ;YACJ;QACJ;QACA,IAAI,QAAQ,iBAAiB,IAAI,WAAW;YACxC,kBAAkB,4KAAA,CAAA,UAAW,CAAC,cAAc,CAAC,cAAc,SAAS;YACpE,IAAI,oBAAoB,cAAc;gBAClC,aAAa,QAAQ,MAAM;YAC/B,OAAO;gBACH,SAAS,IAAI,CAAC,gBAAgB,KAAK,GAAG,IAAI,CAAE,CAAA;oBACxC,aAAa;gBACjB;YACJ;QACJ;QACA,iLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,qJAAA,CAAA,UAAC,EAAE,UAAU,IAAI,CAAE;YAC1B,IAAI,WAAW;gBACX,gBAAgB,OAAO,CAAC;YAC5B,OAAO,IAAI,QAAQ,iBAAiB,EAAE;gBAClC,gBAAgB,OAAO,CAAC,OAAO;oBAC3B,YAAY;gBAChB;YACJ,OAAO;gBACH,gBAAgB,OAAO,CAAC;YAC5B;QACJ,GAAI,IAAI,CAAE,CAAA;YACN,gBAAgB,MAAM,CAAC;QAC3B;IACJ;AACJ;AAEA,SAAS,kBAAkB,eAAe,EAAE,KAAK,EAAE,GAAG;IAClD,WAAW,iBAAiB,OAAO,CAAC,GAAI,CAAA;QACpC,MAAM,UAAU,MAAM,GAAG;QACzB,IAAI;QACJ,IAAK,IAAI,IAAI,GAAG,MAAM,QAAQ,MAAM,EAAE,IAAI,KAAK,IAAK;YAChD,OAAO,OAAO,CAAC,EAAE;YACjB,IAAI,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,KAAK,CAAC,OAAO,CAAC,EAAE,GAAG,MAAM;gBAClD,gBAAgB,OAAO,CAAC;gBACxB;YACJ;QACJ;QACA,gBAAgB,MAAM,CAAC,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;IACxC;AACJ;AACA,MAAM,cAAc,8KAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAC9B,MAAK,OAAO;QACR,UAAU,WAAW,CAAC;QACtB,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,CAAC,iBAAiB,GAAG,CAAC,CAAC,QAAQ,gBAAgB,IAAI,UAAU,QAAQ,QAAQ;QACjF,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;QACjC,IAAI,CAAC,aAAa,GAAG,UAAU,QAAQ,YAAY;QACnD,IAAI,CAAC,SAAS,GAAG,OAAO,CAAC,KAAK;QAC9B,IAAI,CAAC,eAAe,GAAG,OAAO,CAAC,YAAY;QAC3C,IAAI,CAAC,UAAU,GAAG,OAAO,CAAC,OAAO;QACjC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO;QAClC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO;QAClC,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,OAAO;IACtC;IACA;QACI,OAAO,IAAI,CAAC,SAAS;IACzB;IACA;QACI,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;IACvB;IACA;QACI,IAAI,CAAC,WAAW;IACpB;IACA,iBAAgB,OAAO;QACnB,IAAI,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACpB,IAAI,UAAU,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACnD,oBAAoB,GAAG,IAAI,EAAE,SAAS;QAC1C,OAAO;YACH,yBAAyB,IAAI,EAAE,SAAS,IAAI,CAAE,CAAA;gBAC1C,EAAE,OAAO,CAAC,OAAO;YACrB,GAAI,IAAI,CAAC,6BAA6B;YACtC,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC9B;QACA,OAAO,EAAE,OAAO;IACpB;IACA,WAAU,OAAO;QACb,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,CAAA,GAAA,2KAAA,CAAA,aAAU,AAAD,EAAE;gBACP,SAAS,IAAI;gBACb,MAAM,IAAI,CAAC,SAAS;gBACpB,SAAS;YACb;QACJ;IACJ;IACA,WAAU,OAAO;QACb,IAAI,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACpB,IAAI,UAAU,IAAI,CAAC,SAAS,EAAE;YAC1B,oBAAoB,GAAG,IAAI,EAAE,SAAS;QAC1C,OAAO;YACH,eAAe,IAAI,EAAE,SAAS,IAAI,CAAE,CAAC,MAAM;gBACvC,EAAE,OAAO,CAAC,MAAM;YACpB,GAAI,IAAI,CAAC,6BAA6B;YACtC,IAAI,IAAI,CAAC,gBAAgB,CAAC;QAC9B;QACA,OAAO,EAAE,OAAO;IACpB;IACA,YAAW,GAAG,EAAE,YAAY;QACxB,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,IAAI,IAAI,CAAC,aAAa,IAAI;YACtB,IAAI,CAAC,WAAW;YAChB,kBAAkB,GAAG,IAAI,EAAE;QAC/B,OAAO;YACH,oBAAoB,IAAI,EAAE,KAAK,cAAc,IAAI,CAAE,CAAA;gBAC/C,EAAE,OAAO,CAAC;YACd,GAAI,IAAI,CAAC,6BAA6B;QAC1C;QACA,OAAO,EAAE,OAAO;IACpB;IACA;QACI,OAAO,UAAU,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU;IACvD;IACA,aAAY,MAAM;QACd,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,WAAW;QACjC,IAAI;QACJ,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,yBAAyB,QAAQ;QACjC,aAAa,SAAS,KAAK,CAAC,MAAM;YAAC;SAAO;QAC1C,IAAI,CAAC,UAAU,aAAa;YACxB,aAAa,eAAe;QAChC;QACA,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,YAAY,IAAI,CAAE,CAAA;YAC1B,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAM,AAAD,IAAI,oBAAoB,EAAE;gBAC/B,EAAE,OAAO,CAAC,QAAQ;YACtB,OAAO;gBACH,EAAE,OAAO,CAAC,kBAAkB,QAAQ,KAAK,KAAK,CAAC;YACnD;QACJ,GAAI,IAAI,CAAC,6BAA6B;QACtC,OAAO,EAAE,OAAO;IACpB;IACA,aAAY,GAAG,EAAE,MAAM;QACnB,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,IAAI;QACJ,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,yBAAyB,QAAQ;QACjC,aAAa,SAAS,KAAK,CAAC,IAAI,EAAE;YAAC;YAAK;SAAO;QAC/C,IAAI,CAAC,UAAU,aAAa;YACxB,aAAa,eAAe;QAChC;QACA,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,YAAY,IAAI,CAAE,CAAA;YAC1B,IAAI,CAAA,GAAA,mJAAA,CAAA,UAAM,AAAD,IAAI,oBAAoB,EAAE;gBAC/B,EAAE,OAAO,CAAC,KAAK;YACnB,OAAO;gBACH,EAAE,OAAO,CAAC,kBAAkB,QAAQ;YACxC;QACJ,GAAI,IAAI,CAAC,6BAA6B;QACtC,OAAO,EAAE,OAAO;IACpB;IACA,aAAY,GAAG;QACX,MAAM,WAAW,IAAI,CAAC,WAAW;QACjC,IAAI;QACJ,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,yBAAyB,QAAQ;QACjC,aAAa,SAAS,KAAK,CAAC,IAAI,EAAE;YAAC;SAAI;QACvC,IAAI,CAAC,UAAU,aAAa;YACxB,aAAa;QACjB;QACA,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,YAAY,IAAI,CAAE;YAC1B,EAAE,OAAO,CAAC;QACd,GAAI,IAAI,CAAC,6BAA6B;QACtC,OAAO,EAAE,OAAO;IACpB;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_remote_query.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_remote_query.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport arrayQueryImpl from \"../../common/data/array_query\";\r\nimport {\r\n    errors,\r\n    handleError\r\n} from \"../../common/data/errors\";\r\nimport queryAdapters from \"../../common/data/query_adapters\";\r\nimport {\r\n    Deferred\r\n} from \"../../core/utils/deferred\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    isFunction\r\n} from \"../../core/utils/type\";\r\nconst remoteQueryImpl = function(url, queryOptions, tasks) {\r\n    tasks = tasks || [];\r\n    queryOptions = queryOptions || {};\r\n    const createTask = function(name, args) {\r\n        return {\r\n            name: name,\r\n            args: args\r\n        }\r\n    };\r\n    const exec = function(executorTask) {\r\n        const d = new Deferred;\r\n        let _adapterFactory;\r\n        let _adapter;\r\n        let _taskQueue;\r\n        let _currentTask;\r\n        let _mergedSortArgs;\r\n        const rejectWithNotify = function(error) {\r\n            const handler = queryOptions.errorHandler;\r\n            if (handler) {\r\n                handler(error)\r\n            }\r\n            handleError(error);\r\n            d.reject(error)\r\n        };\r\n\r\n        function mergeSortTask(task) {\r\n            switch (task.name) {\r\n                case \"sortBy\":\r\n                    _mergedSortArgs = [task.args];\r\n                    return true;\r\n                case \"thenBy\":\r\n                    if (!_mergedSortArgs) {\r\n                        throw errors.Error(\"E4004\")\r\n                    }\r\n                    _mergedSortArgs.push(task.args);\r\n                    return true\r\n            }\r\n            return false\r\n        }\r\n        try {\r\n            _adapterFactory = queryOptions.adapter;\r\n            if (!isFunction(_adapterFactory)) {\r\n                _adapterFactory = queryAdapters[_adapterFactory]\r\n            }\r\n            _adapter = _adapterFactory(queryOptions);\r\n            _taskQueue = [].concat(tasks).concat(executorTask);\r\n            const {\r\n                optimize: optimize\r\n            } = _adapter;\r\n            if (optimize) {\r\n                optimize(_taskQueue)\r\n            }\r\n            while (_taskQueue.length) {\r\n                _currentTask = _taskQueue[0];\r\n                if (!mergeSortTask(_currentTask)) {\r\n                    if (_mergedSortArgs) {\r\n                        _taskQueue.unshift(createTask(\"multiSort\", [_mergedSortArgs]));\r\n                        _mergedSortArgs = null;\r\n                        continue\r\n                    }\r\n                    if (\"enumerate\" !== String(_currentTask.name)) {\r\n                        if (!_adapter[_currentTask.name] || false === _adapter[_currentTask.name].apply(_adapter, _currentTask.args)) {\r\n                            break\r\n                        }\r\n                    }\r\n                }\r\n                _taskQueue.shift()\r\n            }! function() {\r\n                const head = _taskQueue[0];\r\n                const unmergedTasks = [];\r\n                if (head && \"multiSort\" === head.name) {\r\n                    _taskQueue.shift();\r\n                    each(head.args[0], (function() {\r\n                        unmergedTasks.push(createTask(unmergedTasks.length ? \"thenBy\" : \"sortBy\", this))\r\n                    }))\r\n                }\r\n                _taskQueue = unmergedTasks.concat(_taskQueue)\r\n            }();\r\n            _adapter.exec(url).done(((result, extra) => {\r\n                if (!_taskQueue.length) {\r\n                    d.resolve(result, extra)\r\n                } else {\r\n                    let clientChain = arrayQueryImpl(result, {\r\n                        errorHandler: queryOptions.errorHandler\r\n                    });\r\n                    each(_taskQueue, (function() {\r\n                        clientChain = clientChain[this.name].apply(clientChain, this.args)\r\n                    }));\r\n                    clientChain.done(d.resolve).fail(d.reject)\r\n                }\r\n            })).fail(rejectWithNotify)\r\n        } catch (x) {\r\n            rejectWithNotify(x)\r\n        }\r\n        return d.promise()\r\n    };\r\n    const query = {};\r\n    each([\"sortBy\", \"thenBy\", \"filter\", \"slice\", \"select\", \"groupBy\"], (function() {\r\n        const name = String(this);\r\n        query[name] = function() {\r\n            return remoteQueryImpl(url, queryOptions, tasks.concat(createTask(name, arguments)))\r\n        }\r\n    }));\r\n    each([\"count\", \"min\", \"max\", \"sum\", \"avg\", \"aggregate\", \"enumerate\"], (function() {\r\n        const name = String(this);\r\n        query[name] = function() {\r\n            return exec.call(this, createTask(name, arguments))\r\n        }\r\n    }));\r\n    return query\r\n};\r\nexport default remoteQueryImpl;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;AAIA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;;;AAGA,MAAM,kBAAkB,SAAS,GAAG,EAAE,YAAY,EAAE,KAAK;IACrD,QAAQ,SAAS,EAAE;IACnB,eAAe,gBAAgB,CAAC;IAChC,MAAM,aAAa,SAAS,IAAI,EAAE,IAAI;QAClC,OAAO;YACH,MAAM;YACN,MAAM;QACV;IACJ;IACA,MAAM,OAAO,SAAS,YAAY;QAC9B,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,mBAAmB,SAAS,KAAK;YACnC,MAAM,UAAU,aAAa,YAAY;YACzC,IAAI,SAAS;gBACT,QAAQ;YACZ;YACA,CAAA,GAAA,sKAAA,CAAA,cAAW,AAAD,EAAE;YACZ,EAAE,MAAM,CAAC;QACb;QAEA,SAAS,cAAc,IAAI;YACvB,OAAQ,KAAK,IAAI;gBACb,KAAK;oBACD,kBAAkB;wBAAC,KAAK,IAAI;qBAAC;oBAC7B,OAAO;gBACX,KAAK;oBACD,IAAI,CAAC,iBAAiB;wBAClB,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;oBACvB;oBACA,gBAAgB,IAAI,CAAC,KAAK,IAAI;oBAC9B,OAAO;YACf;YACA,OAAO;QACX;QACA,IAAI;YACA,kBAAkB,aAAa,OAAO;YACtC,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;gBAC9B,kBAAkB,qKAAA,CAAA,UAAa,CAAC,gBAAgB;YACpD;YACA,WAAW,gBAAgB;YAC3B,aAAa,EAAE,CAAC,MAAM,CAAC,OAAO,MAAM,CAAC;YACrC,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;YACJ,IAAI,UAAU;gBACV,SAAS;YACb;YACA,MAAO,WAAW,MAAM,CAAE;gBACtB,eAAe,UAAU,CAAC,EAAE;gBAC5B,IAAI,CAAC,cAAc,eAAe;oBAC9B,IAAI,iBAAiB;wBACjB,WAAW,OAAO,CAAC,WAAW,aAAa;4BAAC;yBAAgB;wBAC5D,kBAAkB;wBAClB;oBACJ;oBACA,IAAI,gBAAgB,OAAO,aAAa,IAAI,GAAG;wBAC3C,IAAI,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC,IAAI,UAAU,QAAQ,CAAC,aAAa,IAAI,CAAC,CAAC,KAAK,CAAC,UAAU,aAAa,IAAI,GAAG;4BAC1G;wBACJ;oBACJ;gBACJ;gBACA,WAAW,KAAK;YACpB;YAAC,CAAE;gBACC,MAAM,OAAO,UAAU,CAAC,EAAE;gBAC1B,MAAM,gBAAgB,EAAE;gBACxB,IAAI,QAAQ,gBAAgB,KAAK,IAAI,EAAE;oBACnC,WAAW,KAAK;oBAChB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,KAAK,IAAI,CAAC,EAAE,EAAG;wBAChB,cAAc,IAAI,CAAC,WAAW,cAAc,MAAM,GAAG,WAAW,UAAU,IAAI;oBAClF;gBACJ;gBACA,aAAa,cAAc,MAAM,CAAC;YACtC;YACA,SAAS,IAAI,CAAC,KAAK,IAAI,CAAE,CAAC,QAAQ;gBAC9B,IAAI,CAAC,WAAW,MAAM,EAAE;oBACpB,EAAE,OAAO,CAAC,QAAQ;gBACtB,OAAO;oBACH,IAAI,cAAc,CAAA,GAAA,2KAAA,CAAA,UAAc,AAAD,EAAE,QAAQ;wBACrC,cAAc,aAAa,YAAY;oBAC3C;oBACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,YAAa;wBACd,cAAc,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,KAAK,CAAC,aAAa,IAAI,CAAC,IAAI;oBACrE;oBACA,YAAY,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,MAAM;gBAC7C;YACJ,GAAI,IAAI,CAAC;QACb,EAAE,OAAO,GAAG;YACR,iBAAiB;QACrB;QACA,OAAO,EAAE,OAAO;IACpB;IACA,MAAM,QAAQ,CAAC;IACf,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAU;QAAU;QAAU;QAAS;QAAU;KAAU,EAAG;QAChE,MAAM,OAAO,OAAO,IAAI;QACxB,KAAK,CAAC,KAAK,GAAG;YACV,OAAO,gBAAgB,KAAK,cAAc,MAAM,MAAM,CAAC,WAAW,MAAM;QAC5E;IACJ;IACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAS;QAAO;QAAO;QAAO;QAAO;QAAa;KAAY,EAAG;QACnE,MAAM,OAAO,OAAO,IAAI;QACxB,KAAK,CAAC,KAAK,GAAG;YACV,OAAO,KAAK,IAAI,CAAC,IAAI,EAAE,WAAW,MAAM;QAC5C;IACJ;IACA,OAAO;AACX;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2017, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_query.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_query.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    queryImpl\r\n} from \"../../common/data/query_implementation\";\r\nconst query = function() {\r\n    const impl = Array.isArray(arguments[0]) ? \"array\" : \"remote\";\r\n    return queryImpl[impl].apply(this, arguments)\r\n};\r\nexport default query;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AAGA,MAAM,QAAQ;IACV,MAAM,OAAO,MAAM,OAAO,CAAC,SAAS,CAAC,EAAE,IAAI,UAAU;IACrD,OAAO,2KAAA,CAAA,YAAS,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE;AACvC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2038, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_array_store.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_array_store.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    applyBatch,\r\n    indexByKey,\r\n    insert,\r\n    remove,\r\n    update\r\n} from \"../../common/data/array_utils\";\r\nimport {\r\n    errors\r\n} from \"../../common/data/errors\";\r\nimport Query from \"../../common/data/query\";\r\nimport {\r\n    rejectedPromise,\r\n    trivialPromise\r\n} from \"../../common/data/utils\";\r\nimport Store from \"../../data/abstract_store\";\r\nconst ArrayStore = Store.inherit({\r\n    ctor(options) {\r\n        if (Array.isArray(options)) {\r\n            options = {\r\n                data: options\r\n            }\r\n        } else {\r\n            options = options || {}\r\n        }\r\n        this.callBase(options);\r\n        const initialArray = options.data;\r\n        if (initialArray && !Array.isArray(initialArray)) {\r\n            throw errors.Error(\"E4006\")\r\n        }\r\n        this._array = initialArray || []\r\n    },\r\n    createQuery() {\r\n        return Query(this._array, {\r\n            errorHandler: this._errorHandler\r\n        })\r\n    },\r\n    _byKeyImpl(key) {\r\n        const index = indexByKey(this, this._array, key);\r\n        if (-1 === index) {\r\n            return rejectedPromise(errors.Error(\"E4009\"))\r\n        }\r\n        return trivialPromise(this._array[index])\r\n    },\r\n    _insertImpl(values) {\r\n        return insert(this, this._array, values)\r\n    },\r\n    _pushImpl(changes) {\r\n        applyBatch({\r\n            keyInfo: this,\r\n            data: this._array,\r\n            changes: changes\r\n        })\r\n    },\r\n    _updateImpl(key, values) {\r\n        return update(this, this._array, key, values)\r\n    },\r\n    _removeImpl(key) {\r\n        return remove(this, this._array, key)\r\n    },\r\n    clear() {\r\n        this._eventsStrategy.fireEvent(\"modifying\");\r\n        this._array = [];\r\n        this._eventsStrategy.fireEvent(\"modified\")\r\n    }\r\n}, \"array\");\r\nexport default ArrayStore;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAOA;AAAA;AAGA;AAAA;AACA;AAAA;AAIA;AAAA;;;;;;AACA,MAAM,aAAa,8KAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAC7B,MAAK,OAAO;QACR,IAAI,MAAM,OAAO,CAAC,UAAU;YACxB,UAAU;gBACN,MAAM;YACV;QACJ,OAAO;YACH,UAAU,WAAW,CAAC;QAC1B;QACA,IAAI,CAAC,QAAQ,CAAC;QACd,MAAM,eAAe,QAAQ,IAAI;QACjC,IAAI,gBAAgB,CAAC,MAAM,OAAO,CAAC,eAAe;YAC9C,MAAM,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;QACvB;QACA,IAAI,CAAC,MAAM,GAAG,gBAAgB,EAAE;IACpC;IACA;QACI,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE;YACtB,cAAc,IAAI,CAAC,aAAa;QACpC;IACJ;IACA,YAAW,GAAG;QACV,MAAM,QAAQ,CAAA,GAAA,2KAAA,CAAA,aAAU,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE;QAC5C,IAAI,CAAC,MAAM,OAAO;YACd,OAAO,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD,EAAE,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;QACxC;QACA,OAAO,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;IAC5C;IACA,aAAY,MAAM;QACd,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE;IACrC;IACA,WAAU,OAAO;QACb,CAAA,GAAA,2KAAA,CAAA,aAAU,AAAD,EAAE;YACP,SAAS,IAAI;YACb,MAAM,IAAI,CAAC,MAAM;YACjB,SAAS;QACb;IACJ;IACA,aAAY,GAAG,EAAE,MAAM;QACnB,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,KAAK;IAC1C;IACA,aAAY,GAAG;QACX,OAAO,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE;IACrC;IACA;QACI,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;QAC/B,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;IACnC;AACJ,GAAG;uCACY", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2118, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/data_source/m_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/data_source/m_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nconst _excluded = [\"items\"];\r\nimport ArrayStore from \"../../../common/data/array_store\";\r\nimport {\r\n    CustomStore\r\n} from \"../../../common/data/custom_store\";\r\nimport {\r\n    normalizeSortingInfo\r\n} from \"../../../common/data/utils\";\r\nimport ajaxUtils from \"../../../core/utils/ajax\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each,\r\n    map\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isPlainObject\r\n} from \"../../../core/utils/type\";\r\nimport Store from \"../../../data/abstract_store\";\r\nexport const CANCELED_TOKEN = \"canceled\";\r\nexport const isPending = deferred => \"pending\" === deferred.state();\r\nexport const normalizeStoreLoadOptionAccessorArguments = originalArguments => {\r\n    switch (originalArguments.length) {\r\n        case 0:\r\n            return;\r\n        case 1:\r\n            return originalArguments[0]\r\n    }\r\n    return [].slice.call(originalArguments)\r\n};\r\nconst mapGroup = (group, level, mapper) => map(group, (item => {\r\n    const restItem = _objectWithoutPropertiesLoose(item, _excluded);\r\n    return _extends({}, restItem, {\r\n        items: mapRecursive(item.items, level - 1, mapper)\r\n    })\r\n}));\r\nconst mapRecursive = (items, level, mapper) => {\r\n    if (!Array.isArray(items)) {\r\n        return items\r\n    }\r\n    return level ? mapGroup(items, level, mapper) : map(items, mapper)\r\n};\r\nexport const mapDataRespectingGrouping = (items, mapper, groupInfo) => {\r\n    const level = groupInfo ? normalizeSortingInfo(groupInfo).length : 0;\r\n    return mapRecursive(items, level, mapper)\r\n};\r\nexport const normalizeLoadResult = (data, extra) => {\r\n    var _data;\r\n    if (null !== (_data = data) && void 0 !== _data && _data.data) {\r\n        extra = data;\r\n        data = data.data\r\n    }\r\n    if (!Array.isArray(data)) {\r\n        data = [data]\r\n    }\r\n    return {\r\n        data: data,\r\n        extra: extra\r\n    }\r\n};\r\nconst createCustomStoreFromLoadFunc = options => {\r\n    const storeConfig = {};\r\n    each([\"useDefaultSearch\", \"key\", \"load\", \"loadMode\", \"cacheRawData\", \"byKey\", \"lookup\", \"totalCount\", \"insert\", \"update\", \"remove\"], (function() {\r\n        storeConfig[this] = options[this];\r\n        delete options[this]\r\n    }));\r\n    return new CustomStore(storeConfig)\r\n};\r\nconst createStoreFromConfig = storeConfig => {\r\n    const alias = storeConfig.type;\r\n    delete storeConfig.type;\r\n    return Store.create(alias, storeConfig)\r\n};\r\nconst createCustomStoreFromUrl = (url, normalizationOptions) => new CustomStore({\r\n    load: () => ajaxUtils.sendRequest({\r\n        url: url,\r\n        dataType: \"json\"\r\n    }),\r\n    loadMode: null === normalizationOptions || void 0 === normalizationOptions ? void 0 : normalizationOptions.fromUrlLoadMode\r\n});\r\nexport const normalizeDataSourceOptions = (options, normalizationOptions) => {\r\n    let store;\r\n    if (\"string\" === typeof options) {\r\n        options = {\r\n            paginate: false,\r\n            store: createCustomStoreFromUrl(options, normalizationOptions)\r\n        }\r\n    }\r\n    if (void 0 === options) {\r\n        options = []\r\n    }\r\n    if (Array.isArray(options) || options instanceof Store) {\r\n        options = {\r\n            store: options\r\n        }\r\n    } else {\r\n        options = extend({}, options)\r\n    }\r\n    if (void 0 === options.store) {\r\n        options.store = []\r\n    }\r\n    store = options.store;\r\n    if (\"load\" in options) {\r\n        store = createCustomStoreFromLoadFunc(options)\r\n    } else if (Array.isArray(store)) {\r\n        store = new ArrayStore(store)\r\n    } else if (isPlainObject(store)) {\r\n        store = createStoreFromConfig(extend({}, store))\r\n    }\r\n    options.store = store;\r\n    return options\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;AACD;AACA;AAEA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;;;AAnBA,MAAM,YAAY;IAAC;CAAQ;;;;;;;;;AAoBpB,MAAM,iBAAiB;AACvB,MAAM,YAAY,CAAA,WAAY,cAAc,SAAS,KAAK;AAC1D,MAAM,4CAA4C,CAAA;IACrD,OAAQ,kBAAkB,MAAM;QAC5B,KAAK;YACD;QACJ,KAAK;YACD,OAAO,iBAAiB,CAAC,EAAE;IACnC;IACA,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;AACzB;AACA,MAAM,WAAW,CAAC,OAAO,OAAO,SAAW,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE,OAAQ,CAAA;QACnD,MAAM,WAAW,CAAA,GAAA,oLAAA,CAAA,UAA6B,AAAD,EAAE,MAAM;QACrD,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,UAAU;YAC1B,OAAO,aAAa,KAAK,KAAK,EAAE,QAAQ,GAAG;QAC/C;IACJ;AACA,MAAM,eAAe,CAAC,OAAO,OAAO;IAChC,IAAI,CAAC,MAAM,OAAO,CAAC,QAAQ;QACvB,OAAO;IACX;IACA,OAAO,QAAQ,SAAS,OAAO,OAAO,UAAU,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE,OAAO;AAC/D;AACO,MAAM,4BAA4B,CAAC,OAAO,QAAQ;IACrD,MAAM,QAAQ,YAAY,CAAA,GAAA,qKAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,MAAM,GAAG;IACnE,OAAO,aAAa,OAAO,OAAO;AACtC;AACO,MAAM,sBAAsB,CAAC,MAAM;IACtC,IAAI;IACJ,IAAI,SAAS,CAAC,QAAQ,IAAI,KAAK,KAAK,MAAM,SAAS,MAAM,IAAI,EAAE;QAC3D,QAAQ;QACR,OAAO,KAAK,IAAI;IACpB;IACA,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;QACtB,OAAO;YAAC;SAAK;IACjB;IACA,OAAO;QACH,MAAM;QACN,OAAO;IACX;AACJ;AACA,MAAM,gCAAgC,CAAA;IAClC,MAAM,cAAc,CAAC;IACrB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAoB;QAAO;QAAQ;QAAY;QAAgB;QAAS;QAAU;QAAc;QAAU;QAAU;KAAS,EAAG;QAClI,WAAW,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;QACjC,OAAO,OAAO,CAAC,IAAI,CAAC;IACxB;IACA,OAAO,IAAI,sNAAA,CAAA,cAAW,CAAC;AAC3B;AACA,MAAM,wBAAwB,CAAA;IAC1B,MAAM,QAAQ,YAAY,IAAI;IAC9B,OAAO,YAAY,IAAI;IACvB,OAAO,8KAAA,CAAA,UAAK,CAAC,MAAM,CAAC,OAAO;AAC/B;AACA,MAAM,2BAA2B,CAAC,KAAK,uBAAyB,IAAI,sNAAA,CAAA,cAAW,CAAC;QAC5E,MAAM,IAAM,0JAAA,CAAA,UAAS,CAAC,WAAW,CAAC;gBAC9B,KAAK;gBACL,UAAU;YACd;QACA,UAAU,SAAS,wBAAwB,KAAK,MAAM,uBAAuB,KAAK,IAAI,qBAAqB,eAAe;IAC9H;AACO,MAAM,6BAA6B,CAAC,SAAS;IAChD,IAAI;IACJ,IAAI,aAAa,OAAO,SAAS;QAC7B,UAAU;YACN,UAAU;YACV,OAAO,yBAAyB,SAAS;QAC7C;IACJ;IACA,IAAI,KAAK,MAAM,SAAS;QACpB,UAAU,EAAE;IAChB;IACA,IAAI,MAAM,OAAO,CAAC,YAAY,mBAAmB,8KAAA,CAAA,UAAK,EAAE;QACpD,UAAU;YACN,OAAO;QACX;IACJ,OAAO;QACH,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;IACzB;IACA,IAAI,KAAK,MAAM,QAAQ,KAAK,EAAE;QAC1B,QAAQ,KAAK,GAAG,EAAE;IACtB;IACA,QAAQ,QAAQ,KAAK;IACrB,IAAI,UAAU,SAAS;QACnB,QAAQ,8BAA8B;IAC1C,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ;QAC7B,QAAQ,IAAI,2KAAA,CAAA,UAAU,CAAC;IAC3B,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;QAC7B,QAAQ,sBAAsB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;IAC7C;IACA,QAAQ,KAAK,GAAG;IAChB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2274, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/data_source/m_operation_manager.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/data_source/m_operation_manager.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    CANCELED_TOKEN\r\n} from \"../../../common/data/data_source/utils\";\r\nexport default class OperationManager {\r\n    constructor() {\r\n        this._counter = -1;\r\n        this._deferreds = {}\r\n    }\r\n    add(deferred) {\r\n        this._counter++;\r\n        this._deferreds[this._counter] = deferred;\r\n        return this._counter\r\n    }\r\n    remove(operationId) {\r\n        return delete this._deferreds[operationId]\r\n    }\r\n    cancel(operationId) {\r\n        if (operationId in this._deferreds) {\r\n            this._deferreds[operationId].reject(CANCELED_TOKEN);\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n    cancelAll() {\r\n        while (this._counter > -1) {\r\n            this.cancel(this._counter);\r\n            this._counter--\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;AAGe,MAAM;IACjB,aAAc;QACV,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,UAAU,GAAG,CAAC;IACvB;IACA,IAAI,QAAQ,EAAE;QACV,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;QACjC,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,OAAO,WAAW,EAAE;QAChB,OAAO,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY;IAC9C;IACA,OAAO,WAAW,EAAE;QAChB,IAAI,eAAe,IAAI,CAAC,UAAU,EAAE;YAChC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,MAAM,CAAC,oLAAA,CAAA,iBAAc;YAClD,OAAO;QACX;QACA,OAAO;IACX;IACA,YAAY;QACR,MAAO,IAAI,CAAC,QAAQ,GAAG,CAAC,EAAG;YACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ;YACzB,IAAI,CAAC,QAAQ;QACjB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2318, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/data_source/m_data_source.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/data_source/m_data_source.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    applyBatch\r\n} from \"../../../common/data/array_utils\";\r\nimport {\r\n    CustomStore\r\n} from \"../../../common/data/custom_store\";\r\nimport OperationManager from \"../../../common/data/data_source/operation_manager\";\r\nimport {\r\n    CANCELED_TOKEN,\r\n    isPending,\r\n    mapDataRespectingGrouping,\r\n    normalizeDataSourceOptions,\r\n    normalizeLoadResult,\r\n    normalizeStoreLoadOptionAccessorArguments\r\n} from \"../../../common/data/data_source/utils\";\r\nimport {\r\n    errors\r\n} from \"../../../common/data/errors\";\r\nimport {\r\n    throttleChanges\r\n} from \"../../../common/data/utils\";\r\nimport Class from \"../../../core/class\";\r\nimport {\r\n    EventsStrategy\r\n} from \"../../../core/events_strategy\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    create\r\n} from \"../../../core/utils/queue\";\r\nimport {\r\n    isBoolean,\r\n    isDefined,\r\n    isEmptyObject,\r\n    isNumeric,\r\n    isObject,\r\n    isString\r\n} from \"../../../core/utils/type\";\r\nimport commonUtils from \"../../core/utils/m_common\";\r\nexport const DataSource = Class.inherit({\r\n    ctor(options) {\r\n        options = normalizeDataSourceOptions(options);\r\n        this._eventsStrategy = new EventsStrategy(this, {\r\n            syncStrategy: true\r\n        });\r\n        this._store = options.store;\r\n        this._changedTime = 0;\r\n        const needThrottling = 0 !== options.pushAggregationTimeout;\r\n        if (needThrottling) {\r\n            const throttlingTimeout = void 0 === options.pushAggregationTimeout ? () => 5 * this._changedTime : options.pushAggregationTimeout;\r\n            let pushDeferred;\r\n            let lastPushWaiters;\r\n            const throttlingPushHandler = throttleChanges((changes => {\r\n                pushDeferred.resolve();\r\n                const storePushPending = when(...lastPushWaiters);\r\n                storePushPending.done((() => this._onPush(changes)));\r\n                lastPushWaiters = void 0;\r\n                pushDeferred = void 0\r\n            }), throttlingTimeout);\r\n            this._onPushHandler = args => {\r\n                this._aggregationTimeoutId = throttlingPushHandler(args.changes);\r\n                if (!pushDeferred) {\r\n                    pushDeferred = new Deferred\r\n                }\r\n                lastPushWaiters = args.waitFor;\r\n                args.waitFor.push(pushDeferred.promise())\r\n            };\r\n            this._store.on(\"beforePushAggregation\", this._onPushHandler)\r\n        } else {\r\n            this._onPushHandler = changes => this._onPush(changes);\r\n            this._store.on(\"push\", this._onPushHandler)\r\n        }\r\n        this._storeLoadOptions = this._extractLoadOptions(options);\r\n        this._mapFunc = options.map;\r\n        this._postProcessFunc = options.postProcess;\r\n        this._pageIndex = void 0 !== options.pageIndex ? options.pageIndex : 0;\r\n        this._pageSize = void 0 !== options.pageSize ? options.pageSize : 20;\r\n        this._loadingCount = 0;\r\n        this._loadQueue = this._createLoadQueue();\r\n        this._searchValue = \"searchValue\" in options ? options.searchValue : null;\r\n        this._searchOperation = options.searchOperation || \"contains\";\r\n        this._searchExpr = options.searchExpr;\r\n        this._paginate = options.paginate;\r\n        this._reshapeOnPush = options.reshapeOnPush ?? false;\r\n        each([\"onChanged\", \"onLoadError\", \"onLoadingChanged\", \"onCustomizeLoadResult\", \"onCustomizeStoreLoadOptions\"], ((_, optionName) => {\r\n            if (optionName in options) {\r\n                this.on(optionName.substr(2, 1).toLowerCase() + optionName.substr(3), options[optionName])\r\n            }\r\n        }));\r\n        this._operationManager = new OperationManager;\r\n        this._init()\r\n    },\r\n    _init() {\r\n        this._items = [];\r\n        this._userData = {};\r\n        this._totalCount = -1;\r\n        this._isLoaded = false;\r\n        if (!isDefined(this._paginate)) {\r\n            this._paginate = !this.group()\r\n        }\r\n        this._isLastPage = !this._paginate\r\n    },\r\n    dispose() {\r\n        var _this$_delayedLoadTas;\r\n        this._store.off(\"beforePushAggregation\", this._onPushHandler);\r\n        this._store.off(\"push\", this._onPushHandler);\r\n        this._eventsStrategy.dispose();\r\n        clearTimeout(this._aggregationTimeoutId);\r\n        null === (_this$_delayedLoadTas = this._delayedLoadTask) || void 0 === _this$_delayedLoadTas || _this$_delayedLoadTas.abort();\r\n        this._operationManager.cancelAll();\r\n        delete this._store;\r\n        delete this._items;\r\n        delete this._delayedLoadTask;\r\n        this._disposed = true\r\n    },\r\n    _extractLoadOptions(options) {\r\n        const result = {};\r\n        let names = [\"sort\", \"filter\", \"langParams\", \"select\", \"group\", \"requireTotalCount\"];\r\n        const customNames = this._store._customLoadOptions();\r\n        if (customNames) {\r\n            names = names.concat(customNames)\r\n        }\r\n        each(names, (function() {\r\n            result[this] = options[this]\r\n        }));\r\n        return result\r\n    },\r\n    loadOptions() {\r\n        return this._storeLoadOptions\r\n    },\r\n    items() {\r\n        return this._items\r\n    },\r\n    pageIndex(newIndex) {\r\n        if (!isNumeric(newIndex)) {\r\n            return this._pageIndex\r\n        }\r\n        this._pageIndex = newIndex;\r\n        this._isLastPage = !this._paginate\r\n    },\r\n    paginate(value) {\r\n        if (!isBoolean(value)) {\r\n            return this._paginate\r\n        }\r\n        if (this._paginate !== value) {\r\n            this._paginate = value;\r\n            this.pageIndex(0)\r\n        }\r\n    },\r\n    pageSize(value) {\r\n        if (!isNumeric(value)) {\r\n            return this._pageSize\r\n        }\r\n        this._pageSize = value\r\n    },\r\n    isLastPage() {\r\n        return this._isLastPage\r\n    },\r\n    generateStoreLoadOptionAccessor(optionName) {\r\n        return args => {\r\n            const normalizedArgs = normalizeStoreLoadOptionAccessorArguments(args);\r\n            if (void 0 === normalizedArgs) {\r\n                return this._storeLoadOptions[optionName]\r\n            }\r\n            this._storeLoadOptions[optionName] = normalizedArgs\r\n        }\r\n    },\r\n    sort() {\r\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n            args[_key] = arguments[_key]\r\n        }\r\n        return this.generateStoreLoadOptionAccessor(\"sort\")(args)\r\n    },\r\n    filter() {\r\n        const newFilter = normalizeStoreLoadOptionAccessorArguments(arguments);\r\n        if (void 0 === newFilter) {\r\n            return this._storeLoadOptions.filter\r\n        }\r\n        this._storeLoadOptions.filter = newFilter;\r\n        this.pageIndex(0)\r\n    },\r\n    group() {\r\n        for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\r\n            args[_key2] = arguments[_key2]\r\n        }\r\n        return this.generateStoreLoadOptionAccessor(\"group\")(args)\r\n    },\r\n    select() {\r\n        for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\r\n            args[_key3] = arguments[_key3]\r\n        }\r\n        return this.generateStoreLoadOptionAccessor(\"select\")(args)\r\n    },\r\n    requireTotalCount(value) {\r\n        if (!isBoolean(value)) {\r\n            return this._storeLoadOptions.requireTotalCount\r\n        }\r\n        this._storeLoadOptions.requireTotalCount = value\r\n    },\r\n    searchValue(value) {\r\n        if (arguments.length < 1) {\r\n            return this._searchValue\r\n        }\r\n        this._searchValue = value;\r\n        this.pageIndex(0)\r\n    },\r\n    searchOperation(op) {\r\n        if (!isString(op)) {\r\n            return this._searchOperation\r\n        }\r\n        this._searchOperation = op;\r\n        this.pageIndex(0)\r\n    },\r\n    searchExpr(expr) {\r\n        const argc = arguments.length;\r\n        if (0 === argc) {\r\n            return this._searchExpr\r\n        }\r\n        if (argc > 1) {\r\n            expr = [].slice.call(arguments)\r\n        }\r\n        this._searchExpr = expr;\r\n        this.pageIndex(0)\r\n    },\r\n    store() {\r\n        return this._store\r\n    },\r\n    key() {\r\n        var _this$_store;\r\n        return null === (_this$_store = this._store) || void 0 === _this$_store ? void 0 : _this$_store.key()\r\n    },\r\n    totalCount() {\r\n        return this._totalCount\r\n    },\r\n    isLoaded() {\r\n        return this._isLoaded\r\n    },\r\n    isLoading() {\r\n        return this._loadingCount > 0\r\n    },\r\n    beginLoading() {\r\n        this._changeLoadingCount(1)\r\n    },\r\n    endLoading() {\r\n        this._changeLoadingCount(-1)\r\n    },\r\n    _createLoadQueue: () => create(),\r\n    _changeLoadingCount(increment) {\r\n        const oldLoading = this.isLoading();\r\n        this._loadingCount += increment;\r\n        const newLoading = this.isLoading();\r\n        if (oldLoading ^ newLoading) {\r\n            this._eventsStrategy.fireEvent(\"loadingChanged\", [newLoading])\r\n        }\r\n    },\r\n    _scheduleLoadCallbacks(deferred) {\r\n        this.beginLoading();\r\n        deferred.always((() => {\r\n            this.endLoading()\r\n        }))\r\n    },\r\n    _scheduleFailCallbacks(deferred) {\r\n        var _this = this;\r\n        deferred.fail((function() {\r\n            for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\r\n                args[_key4] = arguments[_key4]\r\n            }\r\n            if (args[0] === CANCELED_TOKEN) {\r\n                return\r\n            }\r\n            _this._eventsStrategy.fireEvent(\"loadError\", args)\r\n        }))\r\n    },\r\n    _fireChanged(args) {\r\n        const date = new Date;\r\n        this._eventsStrategy.fireEvent(\"changed\", args);\r\n        this._changedTime = new Date - date\r\n    },\r\n    _scheduleChangedCallbacks(deferred) {\r\n        deferred.done((() => this._fireChanged()))\r\n    },\r\n    loadSingle(propName, propValue) {\r\n        const d = new Deferred;\r\n        const key = this.key();\r\n        const store = this._store;\r\n        const options = this._createStoreLoadOptions();\r\n        this._scheduleFailCallbacks(d);\r\n        if (arguments.length < 2) {\r\n            propValue = propName;\r\n            propName = key\r\n        }\r\n        delete options.skip;\r\n        delete options.group;\r\n        delete options.refresh;\r\n        delete options.pageIndex;\r\n        delete options.searchString;\r\n        (() => {\r\n            if (propName === key || store instanceof CustomStore && !store._byKeyViaLoad()) {\r\n                return store.byKey(propValue, options)\r\n            }\r\n            options.take = 1;\r\n            options.filter = options.filter ? [options.filter, [propName, propValue]] : [propName, propValue];\r\n            return store.load(options)\r\n        })().fail(d.reject).done((data => {\r\n            const isEmptyArray = Array.isArray(data) && !data.length;\r\n            if (!isDefined(data) || isEmptyArray) {\r\n                d.reject(errors.Error(\"E4009\"))\r\n            } else {\r\n                if (!Array.isArray(data)) {\r\n                    data = [data]\r\n                }\r\n                d.resolve(this._applyMapFunction(data)[0])\r\n            }\r\n        }));\r\n        return d.promise()\r\n    },\r\n    load() {\r\n        const d = new Deferred;\r\n        const loadTask = () => {\r\n            if (this._disposed) {\r\n                return\r\n            }\r\n            if (!isPending(d)) {\r\n                return\r\n            }\r\n            return this._loadFromStore(loadOperation, d)\r\n        };\r\n        this._scheduleLoadCallbacks(d);\r\n        this._scheduleFailCallbacks(d);\r\n        this._scheduleChangedCallbacks(d);\r\n        const loadOperation = this._createLoadOperation(d);\r\n        this._eventsStrategy.fireEvent(\"customizeStoreLoadOptions\", [loadOperation]);\r\n        this._loadQueue.add((() => {\r\n            if (\"number\" === typeof loadOperation.delay) {\r\n                this._delayedLoadTask = commonUtils.executeAsync(loadTask, loadOperation.delay)\r\n            } else {\r\n                loadTask()\r\n            }\r\n            return d.promise()\r\n        }));\r\n        return d.promise({\r\n            operationId: loadOperation.operationId\r\n        })\r\n    },\r\n    _onPush(changes) {\r\n        if (this._reshapeOnPush) {\r\n            this.load()\r\n        } else {\r\n            const changingArgs = {\r\n                changes: changes\r\n            };\r\n            this._eventsStrategy.fireEvent(\"changing\", [changingArgs]);\r\n            const group = this.group();\r\n            const items = this.items();\r\n            let groupLevel = 0;\r\n            let dataSourceChanges = this.paginate() || group ? changes.filter((item => \"update\" === item.type)) : changes;\r\n            if (group) {\r\n                groupLevel = Array.isArray(group) ? group.length : 1\r\n            }\r\n            if (this._mapFunc) {\r\n                dataSourceChanges.forEach((item => {\r\n                    if (\"insert\" === item.type) {\r\n                        item.data = this._mapFunc(item.data)\r\n                    }\r\n                }))\r\n            }\r\n            if (changingArgs.postProcessChanges) {\r\n                dataSourceChanges = changingArgs.postProcessChanges(dataSourceChanges)\r\n            }\r\n            applyBatch({\r\n                keyInfo: this.store(),\r\n                data: items,\r\n                changes: dataSourceChanges,\r\n                groupCount: groupLevel,\r\n                useInsertIndex: true\r\n            });\r\n            this._fireChanged([{\r\n                changes: changes\r\n            }])\r\n        }\r\n    },\r\n    _createLoadOperation(deferred) {\r\n        const operationId = this._operationManager.add(deferred);\r\n        const storeLoadOptions = this._createStoreLoadOptions();\r\n        if (this._store && !isEmptyObject(null === storeLoadOptions || void 0 === storeLoadOptions ? void 0 : storeLoadOptions.langParams)) {\r\n            this._store._langParams = _extends({}, this._store._langParams, storeLoadOptions.langParams)\r\n        }\r\n        deferred.always((() => this._operationManager.remove(operationId)));\r\n        return {\r\n            operationId: operationId,\r\n            storeLoadOptions: storeLoadOptions\r\n        }\r\n    },\r\n    reload() {\r\n        const store = this.store();\r\n        store._clearCache();\r\n        this._init();\r\n        return this.load()\r\n    },\r\n    cancel(operationId) {\r\n        return this._operationManager.cancel(operationId)\r\n    },\r\n    cancelAll() {\r\n        return this._operationManager.cancelAll()\r\n    },\r\n    _addSearchOptions(storeLoadOptions) {\r\n        if (this._disposed) {\r\n            return\r\n        }\r\n        if (this.store()._useDefaultSearch) {\r\n            this._addSearchFilter(storeLoadOptions)\r\n        } else {\r\n            storeLoadOptions.searchOperation = this._searchOperation;\r\n            storeLoadOptions.searchValue = this._searchValue;\r\n            storeLoadOptions.searchExpr = this._searchExpr\r\n        }\r\n    },\r\n    _createStoreLoadOptions() {\r\n        const result = extend({}, this._storeLoadOptions);\r\n        this._addSearchOptions(result);\r\n        if (this._paginate) {\r\n            if (this._pageSize) {\r\n                result.skip = this._pageIndex * this._pageSize;\r\n                result.take = this._pageSize\r\n            }\r\n        }\r\n        result.userData = this._userData;\r\n        return result\r\n    },\r\n    _addSearchFilter(storeLoadOptions) {\r\n        const value = this._searchValue;\r\n        const op = this._searchOperation;\r\n        let selector = this._searchExpr;\r\n        const searchFilter = [];\r\n        if (!value) {\r\n            return\r\n        }\r\n        if (!selector) {\r\n            selector = \"this\"\r\n        }\r\n        if (!Array.isArray(selector)) {\r\n            selector = [selector]\r\n        }\r\n        each(selector, ((i, item) => {\r\n            if (searchFilter.length) {\r\n                searchFilter.push(\"or\")\r\n            }\r\n            searchFilter.push([item, op, value])\r\n        }));\r\n        if (storeLoadOptions.filter) {\r\n            storeLoadOptions.filter = [searchFilter, storeLoadOptions.filter]\r\n        } else {\r\n            storeLoadOptions.filter = searchFilter\r\n        }\r\n    },\r\n    _loadFromStore(loadOptions, pendingDeferred) {\r\n        const handleSuccess = (data, extra) => {\r\n            if (this._disposed) {\r\n                return\r\n            }\r\n            if (!isPending(pendingDeferred)) {\r\n                return\r\n            }\r\n            const loadResult = extend(normalizeLoadResult(data, extra), loadOptions);\r\n            this._eventsStrategy.fireEvent(\"customizeLoadResult\", [loadResult]);\r\n            when(loadResult.data).done((data => {\r\n                loadResult.data = data;\r\n                this._processStoreLoadResult(loadResult, pendingDeferred)\r\n            })).fail(pendingDeferred.reject)\r\n        };\r\n        if (loadOptions.data) {\r\n            return (new Deferred).resolve(loadOptions.data).done(handleSuccess)\r\n        }\r\n        return this.store().load(loadOptions.storeLoadOptions).done(handleSuccess).fail(pendingDeferred.reject)\r\n    },\r\n    _processStoreLoadResult(loadResult, pendingDeferred) {\r\n        let {\r\n            data: data\r\n        } = loadResult;\r\n        let {\r\n            extra: extra\r\n        } = loadResult;\r\n        const {\r\n            storeLoadOptions: storeLoadOptions\r\n        } = loadResult;\r\n        const resolvePendingDeferred = () => {\r\n            this._isLoaded = true;\r\n            this._totalCount = isFinite(extra.totalCount) ? extra.totalCount : -1;\r\n            return pendingDeferred.resolve(data, extra)\r\n        };\r\n        const proceedLoadingTotalCount = () => {\r\n            this.store().totalCount(storeLoadOptions).done((count => {\r\n                extra.totalCount = count;\r\n                resolvePendingDeferred()\r\n            })).fail(pendingDeferred.reject)\r\n        };\r\n        if (this._disposed) {\r\n            return\r\n        }\r\n        data = this._applyPostProcessFunction(this._applyMapFunction(data));\r\n        if (!isObject(extra)) {\r\n            extra = {}\r\n        }\r\n        this._items = data;\r\n        if (!data.length || !this._paginate || this._pageSize && data.length < this._pageSize) {\r\n            this._isLastPage = true\r\n        }\r\n        if (storeLoadOptions.requireTotalCount && !isFinite(extra.totalCount)) {\r\n            proceedLoadingTotalCount()\r\n        } else {\r\n            resolvePendingDeferred()\r\n        }\r\n    },\r\n    _applyMapFunction(data) {\r\n        if (this._mapFunc) {\r\n            return mapDataRespectingGrouping(data, this._mapFunc, this.group())\r\n        }\r\n        return data\r\n    },\r\n    _applyPostProcessFunction(data) {\r\n        if (this._postProcessFunc) {\r\n            return this._postProcessFunc(data)\r\n        }\r\n        return data\r\n    },\r\n    on(eventName, eventHandler) {\r\n        this._eventsStrategy.on(eventName, eventHandler);\r\n        return this\r\n    },\r\n    off(eventName, eventHandler) {\r\n        this._eventsStrategy.off(eventName, eventHandler);\r\n        return this\r\n    }\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AACA;AAAA;AAQA;AAAA;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAQA;;;;;;;;;;;;;;;;AACO,MAAM,aAAa,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IACpC,MAAK,OAAO;QACR,UAAU,CAAA,GAAA,oLAAA,CAAA,6BAA0B,AAAD,EAAE;QACrC,IAAI,CAAC,eAAe,GAAG,IAAI,+KAAA,CAAA,iBAAc,CAAC,IAAI,EAAE;YAC5C,cAAc;QAClB;QACA,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;QAC3B,IAAI,CAAC,YAAY,GAAG;QACpB,MAAM,iBAAiB,MAAM,QAAQ,sBAAsB;QAC3D,IAAI,gBAAgB;YAChB,MAAM,oBAAoB,KAAK,MAAM,QAAQ,sBAAsB,GAAG,IAAM,IAAI,IAAI,CAAC,YAAY,GAAG,QAAQ,sBAAsB;YAClI,IAAI;YACJ,IAAI;YACJ,MAAM,wBAAwB,CAAA,GAAA,qKAAA,CAAA,kBAAe,AAAD,EAAG,CAAA;gBAC3C,aAAa,OAAO;gBACpB,MAAM,mBAAmB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,KAAK;gBACjC,iBAAiB,IAAI,CAAE,IAAM,IAAI,CAAC,OAAO,CAAC;gBAC1C,kBAAkB,KAAK;gBACvB,eAAe,KAAK;YACxB,GAAI;YACJ,IAAI,CAAC,cAAc,GAAG,CAAA;gBAClB,IAAI,CAAC,qBAAqB,GAAG,sBAAsB,KAAK,OAAO;gBAC/D,IAAI,CAAC,cAAc;oBACf,eAAe,IAAI,iLAAA,CAAA,WAAQ;gBAC/B;gBACA,kBAAkB,KAAK,OAAO;gBAC9B,KAAK,OAAO,CAAC,IAAI,CAAC,aAAa,OAAO;YAC1C;YACA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,yBAAyB,IAAI,CAAC,cAAc;QAC/D,OAAO;YACH,IAAI,CAAC,cAAc,GAAG,CAAA,UAAW,IAAI,CAAC,OAAO,CAAC;YAC9C,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,QAAQ,IAAI,CAAC,cAAc;QAC9C;QACA,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,mBAAmB,CAAC;QAClD,IAAI,CAAC,QAAQ,GAAG,QAAQ,GAAG;QAC3B,IAAI,CAAC,gBAAgB,GAAG,QAAQ,WAAW;QAC3C,IAAI,CAAC,UAAU,GAAG,KAAK,MAAM,QAAQ,SAAS,GAAG,QAAQ,SAAS,GAAG;QACrE,IAAI,CAAC,SAAS,GAAG,KAAK,MAAM,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,GAAG;QAClE,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB;QACvC,IAAI,CAAC,YAAY,GAAG,iBAAiB,UAAU,QAAQ,WAAW,GAAG;QACrE,IAAI,CAAC,gBAAgB,GAAG,QAAQ,eAAe,IAAI;QACnD,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU;QACrC,IAAI,CAAC,SAAS,GAAG,QAAQ,QAAQ;QACjC,IAAI,CAAC,cAAc,GAAG,QAAQ,aAAa,IAAI;QAC/C,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE;YAAC;YAAa;YAAe;YAAoB;YAAyB;SAA8B,EAAG,CAAC,GAAG;YAChH,IAAI,cAAc,SAAS;gBACvB,IAAI,CAAC,EAAE,CAAC,WAAW,MAAM,CAAC,GAAG,GAAG,WAAW,KAAK,WAAW,MAAM,CAAC,IAAI,OAAO,CAAC,WAAW;YAC7F;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG,IAAI,gMAAA,CAAA,UAAgB;QAC7C,IAAI,CAAC,KAAK;IACd;IACA;QACI,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,SAAS,GAAG;YAC5B,IAAI,CAAC,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK;QAChC;QACA,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,SAAS;IACtC;IACA;QACI,IAAI;QACJ,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,cAAc;QAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,QAAQ,IAAI,CAAC,cAAc;QAC3C,IAAI,CAAC,eAAe,CAAC,OAAO;QAC5B,aAAa,IAAI,CAAC,qBAAqB;QACvC,SAAS,CAAC,wBAAwB,IAAI,CAAC,gBAAgB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,KAAK;QAC3H,IAAI,CAAC,iBAAiB,CAAC,SAAS;QAChC,OAAO,IAAI,CAAC,MAAM;QAClB,OAAO,IAAI,CAAC,MAAM;QAClB,OAAO,IAAI,CAAC,gBAAgB;QAC5B,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,qBAAoB,OAAO;QACvB,MAAM,SAAS,CAAC;QAChB,IAAI,QAAQ;YAAC;YAAQ;YAAU;YAAc;YAAU;YAAS;SAAoB;QACpF,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAClD,IAAI,aAAa;YACb,QAAQ,MAAM,MAAM,CAAC;QACzB;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ;YACT,MAAM,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC;QAChC;QACA,OAAO;IACX;IACA;QACI,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA;QACI,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,WAAU,QAAQ;QACd,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YACtB,OAAO,IAAI,CAAC,UAAU;QAC1B;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG,CAAC,IAAI,CAAC,SAAS;IACtC;IACA,UAAS,KAAK;QACV,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACnB,OAAO,IAAI,CAAC,SAAS;QACzB;QACA,IAAI,IAAI,CAAC,SAAS,KAAK,OAAO;YAC1B,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,SAAS,CAAC;QACnB;IACJ;IACA,UAAS,KAAK;QACV,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACnB,OAAO,IAAI,CAAC,SAAS;QACzB;QACA,IAAI,CAAC,SAAS,GAAG;IACrB;IACA;QACI,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,iCAAgC,UAAU;QACtC,OAAO,CAAA;YACH,MAAM,iBAAiB,CAAA,GAAA,oLAAA,CAAA,4CAAyC,AAAD,EAAE;YACjE,IAAI,KAAK,MAAM,gBAAgB;gBAC3B,OAAO,IAAI,CAAC,iBAAiB,CAAC,WAAW;YAC7C;YACA,IAAI,CAAC,iBAAiB,CAAC,WAAW,GAAG;QACzC;IACJ;IACA;QACI,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACrF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAChC;QACA,OAAO,IAAI,CAAC,+BAA+B,CAAC,QAAQ;IACxD;IACA;QACI,MAAM,YAAY,CAAA,GAAA,oLAAA,CAAA,4CAAyC,AAAD,EAAE;QAC5D,IAAI,KAAK,MAAM,WAAW;YACtB,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM;QACxC;QACA,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG;QAChC,IAAI,CAAC,SAAS,CAAC;IACnB;IACA;QACI,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC3F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAClC;QACA,OAAO,IAAI,CAAC,+BAA+B,CAAC,SAAS;IACzD;IACA;QACI,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;YAC3F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;QAClC;QACA,OAAO,IAAI,CAAC,+BAA+B,CAAC,UAAU;IAC1D;IACA,mBAAkB,KAAK;QACnB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACnB,OAAO,IAAI,CAAC,iBAAiB,CAAC,iBAAiB;QACnD;QACA,IAAI,CAAC,iBAAiB,CAAC,iBAAiB,GAAG;IAC/C;IACA,aAAY,KAAK;QACb,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,OAAO,IAAI,CAAC,YAAY;QAC5B;QACA,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,SAAS,CAAC;IACnB;IACA,iBAAgB,EAAE;QACd,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;YACf,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,SAAS,CAAC;IACnB;IACA,YAAW,IAAI;QACX,MAAM,OAAO,UAAU,MAAM;QAC7B,IAAI,MAAM,MAAM;YACZ,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,OAAO,GAAG;YACV,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QACzB;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,CAAC;IACnB;IACA;QACI,OAAO,IAAI,CAAC,MAAM;IACtB;IACA;QACI,IAAI;QACJ,OAAO,SAAS,CAAC,eAAe,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,GAAG;IACvG;IACA;QACI,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA;QACI,OAAO,IAAI,CAAC,SAAS;IACzB;IACA;QACI,OAAO,IAAI,CAAC,aAAa,GAAG;IAChC;IACA;QACI,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA;QACI,IAAI,CAAC,mBAAmB,CAAC,CAAC;IAC9B;IACA,kBAAkB,IAAM,CAAA,GAAA,8KAAA,CAAA,SAAM,AAAD;IAC7B,qBAAoB,SAAS;QACzB,MAAM,aAAa,IAAI,CAAC,SAAS;QACjC,IAAI,CAAC,aAAa,IAAI;QACtB,MAAM,aAAa,IAAI,CAAC,SAAS;QACjC,IAAI,aAAa,YAAY;YACzB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,kBAAkB;gBAAC;aAAW;QACjE;IACJ;IACA,wBAAuB,QAAQ;QAC3B,IAAI,CAAC,YAAY;QACjB,SAAS,MAAM,CAAE;YACb,IAAI,CAAC,UAAU;QACnB;IACJ;IACA,wBAAuB,QAAQ;QAC3B,IAAI,QAAQ,IAAI;QAChB,SAAS,IAAI,CAAE;YACX,IAAK,IAAI,QAAQ,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,QAAQ,QAAQ,GAAG,QAAQ,OAAO,QAAS;gBAC3F,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,MAAM;YAClC;YACA,IAAI,IAAI,CAAC,EAAE,KAAK,oLAAA,CAAA,iBAAc,EAAE;gBAC5B;YACJ;YACA,MAAM,eAAe,CAAC,SAAS,CAAC,aAAa;QACjD;IACJ;IACA,cAAa,IAAI;QACb,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,WAAW;QAC1C,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO;IACnC;IACA,2BAA0B,QAAQ;QAC9B,SAAS,IAAI,CAAE,IAAM,IAAI,CAAC,YAAY;IAC1C;IACA,YAAW,QAAQ,EAAE,SAAS;QAC1B,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,MAAM,MAAM,IAAI,CAAC,GAAG;QACpB,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,UAAU,IAAI,CAAC,uBAAuB;QAC5C,IAAI,CAAC,sBAAsB,CAAC;QAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,YAAY;YACZ,WAAW;QACf;QACA,OAAO,QAAQ,IAAI;QACnB,OAAO,QAAQ,KAAK;QACpB,OAAO,QAAQ,OAAO;QACtB,OAAO,QAAQ,SAAS;QACxB,OAAO,QAAQ,YAAY;QAC3B,CAAC;YACG,IAAI,aAAa,OAAO,iBAAiB,sNAAA,CAAA,cAAW,IAAI,CAAC,MAAM,aAAa,IAAI;gBAC5E,OAAO,MAAM,KAAK,CAAC,WAAW;YAClC;YACA,QAAQ,IAAI,GAAG;YACf,QAAQ,MAAM,GAAG,QAAQ,MAAM,GAAG;gBAAC,QAAQ,MAAM;gBAAE;oBAAC;oBAAU;iBAAU;aAAC,GAAG;gBAAC;gBAAU;aAAU;YACjG,OAAO,MAAM,IAAI,CAAC;QACtB,CAAC,IAAI,IAAI,CAAC,EAAE,MAAM,EAAE,IAAI,CAAE,CAAA;YACtB,MAAM,eAAe,MAAM,OAAO,CAAC,SAAS,CAAC,KAAK,MAAM;YACxD,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,cAAc;gBAClC,EAAE,MAAM,CAAC,sKAAA,CAAA,SAAM,CAAC,KAAK,CAAC;YAC1B,OAAO;gBACH,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;oBACtB,OAAO;wBAAC;qBAAK;gBACjB;gBACA,EAAE,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,EAAE;YAC7C;QACJ;QACA,OAAO,EAAE,OAAO;IACpB;IACA;QACI,MAAM,IAAI,IAAI,iLAAA,CAAA,WAAQ;QACtB,MAAM,WAAW;YACb,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB;YACJ;YACA,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,IAAI;gBACf;YACJ;YACA,OAAO,IAAI,CAAC,cAAc,CAAC,eAAe;QAC9C;QACA,IAAI,CAAC,sBAAsB,CAAC;QAC5B,IAAI,CAAC,sBAAsB,CAAC;QAC5B,IAAI,CAAC,yBAAyB,CAAC;QAC/B,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC;QAChD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,6BAA6B;YAAC;SAAc;QAC3E,IAAI,CAAC,UAAU,CAAC,GAAG,CAAE;YACjB,IAAI,aAAa,OAAO,cAAc,KAAK,EAAE;gBACzC,IAAI,CAAC,gBAAgB,GAAG,+KAAA,CAAA,UAAW,CAAC,YAAY,CAAC,UAAU,cAAc,KAAK;YAClF,OAAO;gBACH;YACJ;YACA,OAAO,EAAE,OAAO;QACpB;QACA,OAAO,EAAE,OAAO,CAAC;YACb,aAAa,cAAc,WAAW;QAC1C;IACJ;IACA,SAAQ,OAAO;QACX,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,IAAI,CAAC,IAAI;QACb,OAAO;YACH,MAAM,eAAe;gBACjB,SAAS;YACb;YACA,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,YAAY;gBAAC;aAAa;YACzD,MAAM,QAAQ,IAAI,CAAC,KAAK;YACxB,MAAM,QAAQ,IAAI,CAAC,KAAK;YACxB,IAAI,aAAa;YACjB,IAAI,oBAAoB,IAAI,CAAC,QAAQ,MAAM,QAAQ,QAAQ,MAAM,CAAE,CAAA,OAAQ,aAAa,KAAK,IAAI,IAAK;YACtG,IAAI,OAAO;gBACP,aAAa,MAAM,OAAO,CAAC,SAAS,MAAM,MAAM,GAAG;YACvD;YACA,IAAI,IAAI,CAAC,QAAQ,EAAE;gBACf,kBAAkB,OAAO,CAAE,CAAA;oBACvB,IAAI,aAAa,KAAK,IAAI,EAAE;wBACxB,KAAK,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;oBACvC;gBACJ;YACJ;YACA,IAAI,aAAa,kBAAkB,EAAE;gBACjC,oBAAoB,aAAa,kBAAkB,CAAC;YACxD;YACA,CAAA,GAAA,2KAAA,CAAA,aAAU,AAAD,EAAE;gBACP,SAAS,IAAI,CAAC,KAAK;gBACnB,MAAM;gBACN,SAAS;gBACT,YAAY;gBACZ,gBAAgB;YACpB;YACA,IAAI,CAAC,YAAY,CAAC;gBAAC;oBACf,SAAS;gBACb;aAAE;QACN;IACJ;IACA,sBAAqB,QAAQ;QACzB,MAAM,cAAc,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAC/C,MAAM,mBAAmB,IAAI,CAAC,uBAAuB;QACrD,IAAI,IAAI,CAAC,MAAM,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,oBAAoB,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,UAAU,GAAG;YAChI,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE,iBAAiB,UAAU;QAC/F;QACA,SAAS,MAAM,CAAE,IAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;QACrD,OAAO;YACH,aAAa;YACb,kBAAkB;QACtB;IACJ;IACA;QACI,MAAM,QAAQ,IAAI,CAAC,KAAK;QACxB,MAAM,WAAW;QACjB,IAAI,CAAC,KAAK;QACV,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,QAAO,WAAW;QACd,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;IACzC;IACA;QACI,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAS;IAC3C;IACA,mBAAkB,gBAAgB;QAC9B,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB;QACJ;QACA,IAAI,IAAI,CAAC,KAAK,GAAG,iBAAiB,EAAE;YAChC,IAAI,CAAC,gBAAgB,CAAC;QAC1B,OAAO;YACH,iBAAiB,eAAe,GAAG,IAAI,CAAC,gBAAgB;YACxD,iBAAiB,WAAW,GAAG,IAAI,CAAC,YAAY;YAChD,iBAAiB,UAAU,GAAG,IAAI,CAAC,WAAW;QAClD;IACJ;IACA;QACI,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,iBAAiB;QAChD,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB,OAAO,IAAI,GAAG,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,SAAS;gBAC9C,OAAO,IAAI,GAAG,IAAI,CAAC,SAAS;YAChC;QACJ;QACA,OAAO,QAAQ,GAAG,IAAI,CAAC,SAAS;QAChC,OAAO;IACX;IACA,kBAAiB,gBAAgB;QAC7B,MAAM,QAAQ,IAAI,CAAC,YAAY;QAC/B,MAAM,KAAK,IAAI,CAAC,gBAAgB;QAChC,IAAI,WAAW,IAAI,CAAC,WAAW;QAC/B,MAAM,eAAe,EAAE;QACvB,IAAI,CAAC,OAAO;YACR;QACJ;QACA,IAAI,CAAC,UAAU;YACX,WAAW;QACf;QACA,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW;YAC1B,WAAW;gBAAC;aAAS;QACzB;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,UAAW,CAAC,GAAG;YAChB,IAAI,aAAa,MAAM,EAAE;gBACrB,aAAa,IAAI,CAAC;YACtB;YACA,aAAa,IAAI,CAAC;gBAAC;gBAAM;gBAAI;aAAM;QACvC;QACA,IAAI,iBAAiB,MAAM,EAAE;YACzB,iBAAiB,MAAM,GAAG;gBAAC;gBAAc,iBAAiB,MAAM;aAAC;QACrE,OAAO;YACH,iBAAiB,MAAM,GAAG;QAC9B;IACJ;IACA,gBAAe,WAAW,EAAE,eAAe;QACvC,MAAM,gBAAgB,CAAC,MAAM;YACzB,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB;YACJ;YACA,IAAI,CAAC,CAAA,GAAA,oLAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;gBAC7B;YACJ;YACA,MAAM,aAAa,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,QAAQ;YAC5D,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC,uBAAuB;gBAAC;aAAW;YAClE,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,WAAW,IAAI,EAAE,IAAI,CAAE,CAAA;gBACxB,WAAW,IAAI,GAAG;gBAClB,IAAI,CAAC,uBAAuB,CAAC,YAAY;YAC7C,GAAI,IAAI,CAAC,gBAAgB,MAAM;QACnC;QACA,IAAI,YAAY,IAAI,EAAE;YAClB,OAAO,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,OAAO,CAAC,YAAY,IAAI,EAAE,IAAI,CAAC;QACzD;QACA,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,YAAY,gBAAgB,EAAE,IAAI,CAAC,eAAe,IAAI,CAAC,gBAAgB,MAAM;IAC1G;IACA,yBAAwB,UAAU,EAAE,eAAe;QAC/C,IAAI,EACA,MAAM,IAAI,EACb,GAAG;QACJ,IAAI,EACA,OAAO,KAAK,EACf,GAAG;QACJ,MAAM,EACF,kBAAkB,gBAAgB,EACrC,GAAG;QACJ,MAAM,yBAAyB;YAC3B,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,WAAW,GAAG,SAAS,MAAM,UAAU,IAAI,MAAM,UAAU,GAAG,CAAC;YACpE,OAAO,gBAAgB,OAAO,CAAC,MAAM;QACzC;QACA,MAAM,2BAA2B;YAC7B,IAAI,CAAC,KAAK,GAAG,UAAU,CAAC,kBAAkB,IAAI,CAAE,CAAA;gBAC5C,MAAM,UAAU,GAAG;gBACnB;YACJ,GAAI,IAAI,CAAC,gBAAgB,MAAM;QACnC;QACA,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB;QACJ;QACA,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAC7D,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YAClB,QAAQ,CAAC;QACb;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,KAAK,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,SAAS,IAAI,KAAK,MAAM,GAAG,IAAI,CAAC,SAAS,EAAE;YACnF,IAAI,CAAC,WAAW,GAAG;QACvB;QACA,IAAI,iBAAiB,iBAAiB,IAAI,CAAC,SAAS,MAAM,UAAU,GAAG;YACnE;QACJ,OAAO;YACH;QACJ;IACJ;IACA,mBAAkB,IAAI;QAClB,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO,CAAA,GAAA,oLAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK;QACpE;QACA,OAAO;IACX;IACA,2BAA0B,IAAI;QAC1B,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACjC;QACA,OAAO;IACX;IACA,IAAG,SAAS,EAAE,YAAY;QACtB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,WAAW;QACnC,OAAO,IAAI;IACf;IACA,KAAI,SAAS,EAAE,YAAY;QACvB,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,WAAW;QACpC,OAAO,IAAI;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2906, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/m_data_helper.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/m_data_helper.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    DataSource\r\n} from \"../../common/data/data_source/data_source\";\r\nimport {\r\n    normalizeDataSourceOptions\r\n} from \"../../common/data/data_source/utils\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport DataController from \"../ui/collection/m_data_controller\";\r\nconst DATA_SOURCE_OPTIONS_METHOD = \"_dataSourceOptions\";\r\nconst DATA_SOURCE_CHANGED_METHOD = \"_dataSourceChangedHandler\";\r\nconst DATA_SOURCE_LOAD_ERROR_METHOD = \"_dataSourceLoadErrorHandler\";\r\nconst DATA_SOURCE_LOADING_CHANGED_METHOD = \"_dataSourceLoadingChangedHandler\";\r\nconst DATA_SOURCE_FROM_URL_LOAD_MODE_METHOD = \"_dataSourceFromUrlLoadMode\";\r\nconst SPECIFIC_DATA_SOURCE_OPTION = \"_getSpecificDataSourceOption\";\r\nconst NORMALIZE_DATA_SOURCE = \"_normalizeDataSource\";\r\nexport const DataHelperMixin = {\r\n    postCtor() {\r\n        this.on(\"disposing\", (() => {\r\n            this._disposeDataSource()\r\n        }))\r\n    },\r\n    _refreshDataSource() {\r\n        this._initDataSource();\r\n        this._loadDataSource()\r\n    },\r\n    _initDataSource() {\r\n        let dataSourceOptions = SPECIFIC_DATA_SOURCE_OPTION in this ? this[SPECIFIC_DATA_SOURCE_OPTION]() : this.option(\"dataSource\");\r\n        let widgetDataSourceOptions;\r\n        let dataSourceType;\r\n        this._disposeDataSource();\r\n        if (dataSourceOptions) {\r\n            if (dataSourceOptions instanceof DataSource) {\r\n                this._isSharedDataSource = true;\r\n                this._dataSource = dataSourceOptions\r\n            } else {\r\n                widgetDataSourceOptions = \"_dataSourceOptions\" in this ? this._dataSourceOptions() : {};\r\n                dataSourceType = this._dataSourceType ? this._dataSourceType() : DataSource;\r\n                dataSourceOptions = normalizeDataSourceOptions(dataSourceOptions, {\r\n                    fromUrlLoadMode: \"_dataSourceFromUrlLoadMode\" in this && this._dataSourceFromUrlLoadMode()\r\n                });\r\n                this._dataSource = new dataSourceType(extend(true, {}, widgetDataSourceOptions, dataSourceOptions))\r\n            }\r\n            if (NORMALIZE_DATA_SOURCE in this) {\r\n                this._dataSource = this[NORMALIZE_DATA_SOURCE](this._dataSource)\r\n            }\r\n            this._addDataSourceHandlers();\r\n            this._initDataController()\r\n        }\r\n    },\r\n    _initDataController() {\r\n        var _this$option;\r\n        const dataController = null === (_this$option = this.option) || void 0 === _this$option ? void 0 : _this$option.call(this, \"_dataController\");\r\n        const dataSource = this._dataSource;\r\n        if (dataController) {\r\n            this._dataController = dataController\r\n        } else {\r\n            this._dataController = new DataController(dataSource)\r\n        }\r\n    },\r\n    _addDataSourceHandlers() {\r\n        if (DATA_SOURCE_CHANGED_METHOD in this) {\r\n            this._addDataSourceChangeHandler()\r\n        }\r\n        if (\"_dataSourceLoadErrorHandler\" in this) {\r\n            this._addDataSourceLoadErrorHandler()\r\n        }\r\n        if (\"_dataSourceLoadingChangedHandler\" in this) {\r\n            this._addDataSourceLoadingChangedHandler()\r\n        }\r\n        this._addReadyWatcher()\r\n    },\r\n    _addReadyWatcher() {\r\n        this.readyWatcher = function(isLoading) {\r\n            this._ready && this._ready(!isLoading)\r\n        }.bind(this);\r\n        this._dataSource.on(\"loadingChanged\", this.readyWatcher)\r\n    },\r\n    _addDataSourceChangeHandler() {\r\n        const dataSource = this._dataSource;\r\n        this._proxiedDataSourceChangedHandler = function(e) {\r\n            this[DATA_SOURCE_CHANGED_METHOD](dataSource.items(), e)\r\n        }.bind(this);\r\n        dataSource.on(\"changed\", this._proxiedDataSourceChangedHandler)\r\n    },\r\n    _addDataSourceLoadErrorHandler() {\r\n        this._proxiedDataSourceLoadErrorHandler = this._dataSourceLoadErrorHandler.bind(this);\r\n        this._dataSource.on(\"loadError\", this._proxiedDataSourceLoadErrorHandler)\r\n    },\r\n    _addDataSourceLoadingChangedHandler() {\r\n        this._proxiedDataSourceLoadingChangedHandler = this._dataSourceLoadingChangedHandler.bind(this);\r\n        this._dataSource.on(\"loadingChanged\", this._proxiedDataSourceLoadingChangedHandler)\r\n    },\r\n    _loadDataSource() {\r\n        const dataSource = this._dataSource;\r\n        if (dataSource) {\r\n            if (dataSource.isLoaded()) {\r\n                this._proxiedDataSourceChangedHandler && this._proxiedDataSourceChangedHandler()\r\n            } else {\r\n                dataSource.load()\r\n            }\r\n        }\r\n    },\r\n    _loadSingle(key, value) {\r\n        key = \"this\" === key ? this._dataSource.key() || \"this\" : key;\r\n        return this._dataSource.loadSingle(key, value)\r\n    },\r\n    _isLastPage() {\r\n        return !this._dataSource || this._dataSource.isLastPage() || !this._dataSource._pageSize\r\n    },\r\n    _isDataSourceLoading() {\r\n        return this._dataSource && this._dataSource.isLoading()\r\n    },\r\n    _disposeDataSource() {\r\n        if (this._dataSource) {\r\n            if (this._isSharedDataSource) {\r\n                delete this._isSharedDataSource;\r\n                this._proxiedDataSourceChangedHandler && this._dataSource.off(\"changed\", this._proxiedDataSourceChangedHandler);\r\n                this._proxiedDataSourceLoadErrorHandler && this._dataSource.off(\"loadError\", this._proxiedDataSourceLoadErrorHandler);\r\n                this._proxiedDataSourceLoadingChangedHandler && this._dataSource.off(\"loadingChanged\", this._proxiedDataSourceLoadingChangedHandler);\r\n                if (this._dataSource._eventsStrategy) {\r\n                    this._dataSource._eventsStrategy.off(\"loadingChanged\", this.readyWatcher)\r\n                }\r\n            } else {\r\n                this._dataSource.dispose()\r\n            }\r\n            delete this._dataSource;\r\n            delete this._proxiedDataSourceChangedHandler;\r\n            delete this._proxiedDataSourceLoadErrorHandler;\r\n            delete this._proxiedDataSourceLoadingChangedHandler\r\n        }\r\n    },\r\n    getDataSource() {\r\n        return this._dataSource || null\r\n    }\r\n};\r\nexport default DataHelperMixin;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;;;;;AACA,MAAM,6BAA6B;AACnC,MAAM,6BAA6B;AACnC,MAAM,gCAAgC;AACtC,MAAM,qCAAqC;AAC3C,MAAM,wCAAwC;AAC9C,MAAM,8BAA8B;AACpC,MAAM,wBAAwB;AACvB,MAAM,kBAAkB;IAC3B;QACI,IAAI,CAAC,EAAE,CAAC,aAAc;YAClB,IAAI,CAAC,kBAAkB;QAC3B;IACJ;IACA;QACI,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,eAAe;IACxB;IACA;QACI,IAAI,oBAAoB,+BAA+B,IAAI,GAAG,IAAI,CAAC,4BAA4B,KAAK,IAAI,CAAC,MAAM,CAAC;QAChH,IAAI;QACJ,IAAI;QACJ,IAAI,CAAC,kBAAkB;QACvB,IAAI,mBAAmB;YACnB,IAAI,6BAA6B,0LAAA,CAAA,aAAU,EAAE;gBACzC,IAAI,CAAC,mBAAmB,GAAG;gBAC3B,IAAI,CAAC,WAAW,GAAG;YACvB,OAAO;gBACH,0BAA0B,wBAAwB,IAAI,GAAG,IAAI,CAAC,kBAAkB,KAAK,CAAC;gBACtF,iBAAiB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,KAAK,0LAAA,CAAA,aAAU;gBAC3E,oBAAoB,CAAA,GAAA,oLAAA,CAAA,6BAA0B,AAAD,EAAE,mBAAmB;oBAC9D,iBAAiB,gCAAgC,IAAI,IAAI,IAAI,CAAC,0BAA0B;gBAC5F;gBACA,IAAI,CAAC,WAAW,GAAG,IAAI,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,yBAAyB;YACpF;YACA,IAAI,yBAAyB,IAAI,EAAE;gBAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW;YACnE;YACA,IAAI,CAAC,sBAAsB;YAC3B,IAAI,CAAC,mBAAmB;QAC5B;IACJ;IACA;QACI,IAAI;QACJ,MAAM,iBAAiB,SAAS,CAAC,eAAe,IAAI,CAAC,MAAM,KAAK,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,IAAI,CAAC,IAAI,EAAE;QAC3H,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI,gBAAgB;YAChB,IAAI,CAAC,eAAe,GAAG;QAC3B,OAAO;YACH,IAAI,CAAC,eAAe,GAAG,IAAI,2LAAA,CAAA,UAAc,CAAC;QAC9C;IACJ;IACA;QACI,IAAI,8BAA8B,IAAI,EAAE;YACpC,IAAI,CAAC,2BAA2B;QACpC;QACA,IAAI,iCAAiC,IAAI,EAAE;YACvC,IAAI,CAAC,8BAA8B;QACvC;QACA,IAAI,sCAAsC,IAAI,EAAE;YAC5C,IAAI,CAAC,mCAAmC;QAC5C;QACA,IAAI,CAAC,gBAAgB;IACzB;IACA;QACI,IAAI,CAAC,YAAY,GAAG,CAAA,SAAS,SAAS;YAClC,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,CAAC;QAChC,CAAA,EAAE,IAAI,CAAC,IAAI;QACX,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,kBAAkB,IAAI,CAAC,YAAY;IAC3D;IACA;QACI,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI,CAAC,gCAAgC,GAAG,CAAA,SAAS,CAAC;YAC9C,IAAI,CAAC,2BAA2B,CAAC,WAAW,KAAK,IAAI;QACzD,CAAA,EAAE,IAAI,CAAC,IAAI;QACX,WAAW,EAAE,CAAC,WAAW,IAAI,CAAC,gCAAgC;IAClE;IACA;QACI,IAAI,CAAC,kCAAkC,GAAG,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,IAAI;QACpF,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,aAAa,IAAI,CAAC,kCAAkC;IAC5E;IACA;QACI,IAAI,CAAC,uCAAuC,GAAG,IAAI,CAAC,gCAAgC,CAAC,IAAI,CAAC,IAAI;QAC9F,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,kBAAkB,IAAI,CAAC,uCAAuC;IACtF;IACA;QACI,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI,YAAY;YACZ,IAAI,WAAW,QAAQ,IAAI;gBACvB,IAAI,CAAC,gCAAgC,IAAI,IAAI,CAAC,gCAAgC;YAClF,OAAO;gBACH,WAAW,IAAI;YACnB;QACJ;IACJ;IACA,aAAY,GAAG,EAAE,KAAK;QAClB,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,MAAM,SAAS;QAC1D,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK;IAC5C;IACA;QACI,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS;IAC5F;IACA;QACI,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS;IACzD;IACA;QACI,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,OAAO,IAAI,CAAC,mBAAmB;gBAC/B,IAAI,CAAC,gCAAgC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,gCAAgC;gBAC9G,IAAI,CAAC,kCAAkC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,aAAa,IAAI,CAAC,kCAAkC;gBACpH,IAAI,CAAC,uCAAuC,IAAI,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,uCAAuC;gBACnI,IAAI,IAAI,CAAC,WAAW,CAAC,eAAe,EAAE;oBAClC,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,GAAG,CAAC,kBAAkB,IAAI,CAAC,YAAY;gBAC5E;YACJ,OAAO;gBACH,IAAI,CAAC,WAAW,CAAC,OAAO;YAC5B;YACA,OAAO,IAAI,CAAC,WAAW;YACvB,OAAO,IAAI,CAAC,gCAAgC;YAC5C,OAAO,IAAI,CAAC,kCAAkC;YAC9C,OAAO,IAAI,CAAC,uCAAuC;QACvD;IACJ;IACA;QACI,OAAO,IAAI,CAAC,WAAW,IAAI;IAC/B;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3060, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/data/data_controller/data_controller.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/data/data_controller/data_controller.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nimport ArrayStore from \"../../../common/data/array_store\";\r\nimport {\r\n    DataSource\r\n} from \"../../../common/data/data_source/data_source\";\r\nimport {\r\n    normalizeDataSourceOptions\r\n} from \"../../../common/data/data_source/utils\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    isDefined\r\n} from \"../../../core/utils/type\";\r\nclass DataController {\r\n    constructor(dataSourceOptions, _ref) {\r\n        let {\r\n            key: key\r\n        } = _ref;\r\n        this._isSharedDataSource = false;\r\n        this._keyExpr = key;\r\n        this.updateDataSource(dataSourceOptions)\r\n    }\r\n    _updateDataSource(dataSourceOptions) {\r\n        if (!dataSourceOptions) {\r\n            return\r\n        }\r\n        if (dataSourceOptions instanceof DataSource) {\r\n            this._isSharedDataSource = true;\r\n            this._dataSource = dataSourceOptions\r\n        } else {\r\n            const normalizedDataSourceOptions = normalizeDataSourceOptions(dataSourceOptions);\r\n            this._dataSource = new DataSource(extend(true, {}, {}, normalizedDataSourceOptions))\r\n        }\r\n    }\r\n    _updateDataSourceByItems(items) {\r\n        this._dataSource = new DataSource({\r\n            store: new ArrayStore({\r\n                key: this.key(),\r\n                data: items\r\n            }),\r\n            pageSize: 0\r\n        })\r\n    }\r\n    _disposeDataSource() {\r\n        if (this._dataSource) {\r\n            if (this._isSharedDataSource) {\r\n                this._isSharedDataSource = false\r\n            } else {\r\n                this._dataSource.dispose()\r\n            }\r\n            delete this._dataSource\r\n        }\r\n    }\r\n    load() {\r\n        return this._dataSource.load()\r\n    }\r\n    loadSingle(propName, propValue) {\r\n        if (!this._dataSource) {\r\n            return (new Deferred).reject()\r\n        }\r\n        let pName = propName;\r\n        let pValue = propValue;\r\n        if (arguments.length < 2) {\r\n            pValue = propName;\r\n            pName = this.key()\r\n        }\r\n        return this._dataSource.loadSingle(pName, pValue)\r\n    }\r\n    loadFromStore(loadOptions) {\r\n        return this.store().load(loadOptions)\r\n    }\r\n    loadNextPage() {\r\n        this.pageIndex(1 + this.pageIndex());\r\n        return this.load()\r\n    }\r\n    loadOptions() {\r\n        return this._dataSource.loadOptions()\r\n    }\r\n    userData() {\r\n        return this._dataSource._userData\r\n    }\r\n    cancel(operationId) {\r\n        this._dataSource.cancel(operationId)\r\n    }\r\n    cancelAll() {\r\n        this._dataSource.cancelAll()\r\n    }\r\n    filter(filter) {\r\n        return this._dataSource.filter(filter)\r\n    }\r\n    addSearchFilter(storeLoadOptions) {\r\n        this._dataSource._addSearchFilter(storeLoadOptions)\r\n    }\r\n    group(group) {\r\n        return this._dataSource.group(group)\r\n    }\r\n    paginate() {\r\n        return this._dataSource.paginate()\r\n    }\r\n    pageSize() {\r\n        return this._dataSource._pageSize\r\n    }\r\n    pageIndex(pageIndex) {\r\n        if (void 0 === pageIndex) {\r\n            return this._dataSource.pageIndex(void 0)\r\n        }\r\n        return this._dataSource.pageIndex(pageIndex)\r\n    }\r\n    resetDataSource() {\r\n        this._disposeDataSource()\r\n    }\r\n    resetDataSourcePageIndex() {\r\n        if (this.pageIndex()) {\r\n            this.pageIndex(0);\r\n            this.load()\r\n        }\r\n    }\r\n    updateDataSource(items, key) {\r\n        const dataSourceOptions = items ?? this.items();\r\n        if (key) {\r\n            this._keyExpr = key\r\n        }\r\n        this._disposeDataSource();\r\n        if (Array.isArray(dataSourceOptions)) {\r\n            this._updateDataSourceByItems(dataSourceOptions)\r\n        } else {\r\n            this._updateDataSource(dataSourceOptions)\r\n        }\r\n    }\r\n    totalCount() {\r\n        return this._dataSource.totalCount()\r\n    }\r\n    isLastPage() {\r\n        return this._dataSource.isLastPage() || !this._dataSource._pageSize\r\n    }\r\n    isLoading() {\r\n        return this._dataSource.isLoading()\r\n    }\r\n    isLoaded() {\r\n        return this._dataSource.isLoaded()\r\n    }\r\n    searchValue(value) {\r\n        return this._dataSource.searchValue(value)\r\n    }\r\n    searchOperation(operation) {\r\n        return this._dataSource.searchOperation(operation)\r\n    }\r\n    searchExpr(expr) {\r\n        return this._dataSource.searchExpr(expr)\r\n    }\r\n    select() {\r\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n            args[_key] = arguments[_key]\r\n        }\r\n        return this._dataSource.select(args)\r\n    }\r\n    key() {\r\n        var _this$_dataSource;\r\n        const storeKey = null === (_this$_dataSource = this._dataSource) || void 0 === _this$_dataSource ? void 0 : _this$_dataSource.key();\r\n        return isDefined(storeKey) && \"this\" === this._keyExpr ? storeKey : this._keyExpr\r\n    }\r\n    keyOf(item) {\r\n        return this.store().keyOf(item)\r\n    }\r\n    store() {\r\n        return this._dataSource.store()\r\n    }\r\n    items() {\r\n        var _this$_dataSource2;\r\n        return null === (_this$_dataSource2 = this._dataSource) || void 0 === _this$_dataSource2 ? void 0 : _this$_dataSource2.items()\r\n    }\r\n    applyMapFunction(data) {\r\n        return this._dataSource._applyMapFunction(data)\r\n    }\r\n    getDataSource() {\r\n        return this._dataSource ?? null\r\n    }\r\n    reload() {\r\n        return this._dataSource.reload()\r\n    }\r\n    on(event, handler) {\r\n        this._dataSource.on(event, handler)\r\n    }\r\n    off(event, handler) {\r\n        this._dataSource.off(event, handler)\r\n    }\r\n}\r\nexport default DataController;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;;;AAGA,MAAM;IACF,YAAY,iBAAiB,EAAE,IAAI,CAAE;QACjC,IAAI,EACA,KAAK,GAAG,EACX,GAAG;QACJ,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,gBAAgB,CAAC;IAC1B;IACA,kBAAkB,iBAAiB,EAAE;QACjC,IAAI,CAAC,mBAAmB;YACpB;QACJ;QACA,IAAI,6BAA6B,0LAAA,CAAA,aAAU,EAAE;YACzC,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,CAAC,WAAW,GAAG;QACvB,OAAO;YACH,MAAM,8BAA8B,CAAA,GAAA,oLAAA,CAAA,6BAA0B,AAAD,EAAE;YAC/D,IAAI,CAAC,WAAW,GAAG,IAAI,0LAAA,CAAA,aAAU,CAAC,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,CAAC,GAAG;QAC3D;IACJ;IACA,yBAAyB,KAAK,EAAE;QAC5B,IAAI,CAAC,WAAW,GAAG,IAAI,0LAAA,CAAA,aAAU,CAAC;YAC9B,OAAO,IAAI,2KAAA,CAAA,UAAU,CAAC;gBAClB,KAAK,IAAI,CAAC,GAAG;gBACb,MAAM;YACV;YACA,UAAU;QACd;IACJ;IACA,qBAAqB;QACjB,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB,IAAI,IAAI,CAAC,mBAAmB,EAAE;gBAC1B,IAAI,CAAC,mBAAmB,GAAG;YAC/B,OAAO;gBACH,IAAI,CAAC,WAAW,CAAC,OAAO;YAC5B;YACA,OAAO,IAAI,CAAC,WAAW;QAC3B;IACJ;IACA,OAAO;QACH,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAChC;IACA,WAAW,QAAQ,EAAE,SAAS,EAAE;QAC5B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB,OAAO,CAAC,IAAI,iLAAA,CAAA,WAAQ,EAAE,MAAM;QAChC;QACA,IAAI,QAAQ;QACZ,IAAI,SAAS;QACb,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,SAAS;YACT,QAAQ,IAAI,CAAC,GAAG;QACpB;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,OAAO;IAC9C;IACA,cAAc,WAAW,EAAE;QACvB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAC7B;IACA,eAAe;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS;QACjC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,cAAc;QACV,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW;IACvC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;IACrC;IACA,OAAO,WAAW,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAC5B;IACA,YAAY;QACR,IAAI,CAAC,WAAW,CAAC,SAAS;IAC9B;IACA,OAAO,MAAM,EAAE;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACnC;IACA,gBAAgB,gBAAgB,EAAE;QAC9B,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;IACtC;IACA,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAClC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IACpC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;IACrC;IACA,UAAU,SAAS,EAAE;QACjB,IAAI,KAAK,MAAM,WAAW;YACtB,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK;QAC3C;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;IACtC;IACA,kBAAkB;QACd,IAAI,CAAC,kBAAkB;IAC3B;IACA,2BAA2B;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,IAAI;QACb;IACJ;IACA,iBAAiB,KAAK,EAAE,GAAG,EAAE;QACzB,MAAM,oBAAoB,SAAS,IAAI,CAAC,KAAK;QAC7C,IAAI,KAAK;YACL,IAAI,CAAC,QAAQ,GAAG;QACpB;QACA,IAAI,CAAC,kBAAkB;QACvB,IAAI,MAAM,OAAO,CAAC,oBAAoB;YAClC,IAAI,CAAC,wBAAwB,CAAC;QAClC,OAAO;YACH,IAAI,CAAC,iBAAiB,CAAC;QAC3B;IACJ;IACA,aAAa;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;IACtC;IACA,aAAa;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS;IACvE;IACA,YAAY;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;IACrC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IACpC;IACA,YAAY,KAAK,EAAE;QACf,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;IACxC;IACA,gBAAgB,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;IAC5C;IACA,WAAW,IAAI,EAAE;QACb,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IACvC;IACA,SAAS;QACL,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACrF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAChC;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACnC;IACA,MAAM;QACF,IAAI;QACJ,MAAM,WAAW,SAAS,CAAC,oBAAoB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,GAAG;QACjI,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa,WAAW,IAAI,CAAC,QAAQ,GAAG,WAAW,IAAI,CAAC,QAAQ;IACrF;IACA,MAAM,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IACjC;IACA,QAAQ;QACJ,IAAI;QACJ,OAAO,SAAS,CAAC,qBAAqB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,qBAAqB,KAAK,IAAI,mBAAmB,KAAK;IAChI;IACA,iBAAiB,IAAI,EAAE;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC;IAC9C;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW,IAAI;IAC/B;IACA,SAAS;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;IAClC;IACA,GAAG,KAAK,EAAE,OAAO,EAAE;QACf,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO;IAC/B;IACA,IAAI,KAAK,EAAE,OAAO,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO;IAChC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}]}