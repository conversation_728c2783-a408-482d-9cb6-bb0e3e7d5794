﻿using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Microsoft.IdentityModel.Tokens;
using omsnext.shared.Models;
using Microsoft.Extensions.Configuration;

namespace omsnext.api.Helpers
{
    public class TokenHelper
    {
        private static IConfiguration _configuration;

        public static void Initialize(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        private static string Secret<PERSON>ey
        {
            get
            {
                if (_configuration == null)
                    throw new InvalidOperationException("TokenHelper is not initialized. Call TokenHelper.Initialize with a valid IConfiguration instance.");
                return _configuration["Jwt:Key"];
            }
        }

        public static string GenerateToken(User user, int expireMinutes = 60)
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(SecretKey);

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(new[]
                {
                        new Claim(ClaimTypes.NameIdentifier, user.Oid.ToString()),
                        new Claim(ClaimTypes.Name, user.DisplayName ?? ""),
                        new Claim(ClaimTypes.Email, user.Email ?? "")
                    }),
                Expires = DateTime.UtcNow.AddMinutes(expireMinutes),
                SigningCredentials = new SigningCredentials(new SymmetricSecurityKey(key), SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }
    }
}