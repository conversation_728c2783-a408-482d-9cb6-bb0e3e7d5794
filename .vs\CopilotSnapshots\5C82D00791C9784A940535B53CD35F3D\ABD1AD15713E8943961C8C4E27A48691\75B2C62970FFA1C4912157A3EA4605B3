﻿using DevExpress.Xpo;
using Microsoft.AspNetCore.Mvc;
using omsnext.core.Models;

namespace omsnext.api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class KtforgevWpfController : ControllerBase
{
    private readonly Session _session;

    public KtforgevWpfController(Session session)
    {
        _session = session;
    }

    [HttpGet]
    public async Task<IActionResult> Get(
        [FromQuery] int skip = 0,
        [FromQuery] int take = 100,
        [FromQuery] string? sortField = null,
        [FromQuery] string? sortDirection = "asc",
        [FromQuery] string? filterField = null,
        [FromQuery] string? filterValue = null,
        [FromQuery] string? filterOperator = "contains")
    {
        try
        {
            var query = new XPQuery<Ktforgev>(_session);

            // Simple filtering without complex switch statements
            if (!string.IsNullOrEmpty(filterField) && !string.IsNullOrEmpty(filterValue))
            {
                if (filterField == "hiv_szam")
                    query = new XPQuery<Ktforgev>(_session).Where(x => x.hiv_szam.Contains(filterValue));
                else if (filterField == "tk")
                    query = new XPQuery<Ktforgev>(_session).Where(x => x.tk == filterValue);
                else if (filterField == "meg_nev")
                    query = new XPQuery<Ktforgev>(_session).Where(x => x.meg_nev.Contains(filterValue));
                else if (filterField == "par_kod")
                    query = new XPQuery<Ktforgev>(_session).Where(x => x.par_kod.Contains(filterValue));
            }

            // Get total count
            var totalCount = await Task.Run(() => query.Count());

            // Simple sorting
            IOrderedQueryable<Ktforgev> orderedQuery;
            if (sortField == "id")
                orderedQuery = sortDirection == "desc" ? query.OrderByDescending(x => x.id) : query.OrderBy(x => x.id);
            else if (sortField == "hiv_szam")
                orderedQuery = sortDirection == "desc" ? query.OrderByDescending(x => x.hiv_szam) : query.OrderBy(x => x.hiv_szam);
            else if (sortField == "meg_nev")
                orderedQuery = sortDirection == "desc" ? query.OrderByDescending(x => x.meg_nev) : query.OrderBy(x => x.meg_nev);
            else if (sortField == "ert_ek")
                orderedQuery = sortDirection == "desc" ? query.OrderByDescending(x => x.ert_ek) : query.OrderBy(x => x.ert_ek);
            else
                orderedQuery = query.OrderBy(x => x.id);

            // Get paginated data
            var items = await Task.Run(() =>
                orderedQuery
                    .Skip(skip)
                    .Take(take)
                    .Select(x => new KtforgevDto
                    {
                        id = x.id,
                        hiv_szam = x.hiv_szam,
                        tk = x.tk,
                        fkv_1 = x.fkv_1,
                        dev_nem = x.dev_nem,
                        dev_ert = x.dev_ert,
                        fkv_2 = x.fkv_2,
                        fkv_3 = x.fkv_3,
                        mnk_szam = x.mnk_szam,
                        fda_t = x.fda_t,
                        rog_zito = x.rog_zito,
                        rog_dat = x.rog_dat,
                        meg_nev = x.meg_nev,
                        rel_azon = x.rel_azon,
                        nap_lo = x.nap_lo,
                        fej = x.fej,
                        ert_ek = x.ert_ek,
                        biz_dat = x.biz_dat,
                        par_kod = x.par_kod,
                        dol_kod = x.dol_kod,
                        cos_tcenter = x.cos_tcenter,
                        rel_azon2 = x.rel_azon2,
                        ber_kat = x.ber_kat,
                        men_nyi = x.men_nyi,
                        pro_jekt = x.pro_jekt,
                        afa_kod = x.afa_kod,
                        ert_ek_signed = x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek),
                        created_at = x.created_at,
                        updated_at = x.updated_at
                    })
                    .ToList());

            return Ok(new
            {
                data = items,
                totalCount = totalCount,
                skip = skip,
                take = take
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }
}
