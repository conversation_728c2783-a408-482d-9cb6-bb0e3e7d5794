﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;
using DevExpress.Xpf.Bars;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class AdminDashboardUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<PasswordResetRequestDto> _requests;
        private bool _isLoading;
        private string _currentStatusFilter = "Pending";
        private DispatcherTimer? _autoRefreshTimer;
        private PasswordResetRequestDto? _selectedRequest;

        public event PropertyChangedEventHandler? PropertyChanged;

        public AdminDashboardUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _requests = new ObservableCollection<PasswordResetRequestDto>();
            
            DataContext = this;
            RequestsGrid.ItemsSource = _requests;
            
            InitializeRibbonDefaults();
            Loaded += AdminDashboardUserControl_Loaded;
        }

        private void InitializeRibbonDefaults()
        {
            // Alapértelmezett értékek beállítása
            StatusFilterRibbonEdit.EditValue = "Pending";
            RefreshIntervalRibbonEdit.EditValue = 30;
            EmailNotificationsRibbonCheck.IsChecked = true;
            
            // Event kezelők hozzáadása
            StatusFilterRibbonEdit.EditValueChanged += StatusFilterRibbonEdit_EditValueChanged;
            RefreshIntervalRibbonEdit.EditValueChanged += RefreshIntervalRibbonEdit_EditValueChanged;
        }

        private async void AdminDashboardUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
            UpdateLastRefreshTime();
        }

        #region Ribbon Event Handlers

        private async void ApproveRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            if (_selectedRequest?.Status == "Pending")
            {
                var result = DXMessageBox.Show(
                    $"Biztosan jóváhagyja a jelszó visszaállítási kérelmet?\n\nFelhasználó: {_selectedRequest.UserDisplayName}\nE-mail: {_selectedRequest.UserEmail}", 
                    "Kérelem jóváhagyása", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await ProcessRequestAsync(_selectedRequest.Id, "Approved", "Adminisztrátor által jóváhagyva");
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük, válasszon ki egy függő állapotú kérelmet!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void RejectRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            if (_selectedRequest?.Status == "Pending")
            {
                var inputWindow = CreateNotesInputDialog("Kérelem elutasítása", "Kérjük, adja meg az elutasítás okát:");
                if (inputWindow.ShowDialog() == true && inputWindow.Tag is string notes && !string.IsNullOrWhiteSpace(notes))
                {
                    await ProcessRequestAsync(_selectedRequest.Id, "Rejected", notes);
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük, válasszon ki egy függő állapotú kérelmet!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ViewDetailsRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            if (_selectedRequest != null)
            {
                try
                {
                    var detailDialog = new RequestDetailDialog(_selectedRequest);
                    detailDialog.ShowDialog();
                }
                catch
                {
                    // Fallback egyszerű megjelenítésre
                    ShowRequestDetailsSimple(_selectedRequest);
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük, válasszon ki egy kérelmet a részletek megtekintéséhez!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void RefreshRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            await LoadRequestsAsync();
            UpdateLastRefreshTime();
        }

        private void ExportRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            try
            {
                RequestsGrid.ExportToXlsx("password_reset_requests.xlsx");
                DXMessageBox.Show("Adatok sikeresen exportálva!", "Export", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba az export során: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void BulkApproveRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            var pendingRequests = _requests.Where(r => r.Status == "Pending").ToList();
            if (pendingRequests.Any())
            {
                var result = DXMessageBox.Show(
                    $"Biztosan jóváhagyja az összes függő kérelmet? ({pendingRequests.Count} db)", 
                    "Tömeges jóváhagyás", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    foreach (var request in pendingRequests)
                    {
                        await ProcessRequestAsync(request.Id, "Approved", "Tömeges jóváhagyás");
                    }
                }
            }
            else
            {
                DXMessageBox.Show("Nincs függő állapotú kérelem!", "Információ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void BulkRejectRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            var pendingRequests = _requests.Where(r => r.Status == "Pending").ToList();
            if (pendingRequests.Any())
            {
                var inputWindow = CreateNotesInputDialog("Tömeges elutasítás", "Adja meg az elutasítás okát az összes kérelemhez:");
                if (inputWindow.ShowDialog() == true && inputWindow.Tag is string notes && !string.IsNullOrWhiteSpace(notes))
                {
                    foreach (var request in pendingRequests)
                    {
                        await ProcessRequestAsync(request.Id, "Rejected", notes);
                    }
                }
            }
            else
            {
                DXMessageBox.Show("Nincs függő állapotú kérelem!", "Információ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async void StatusFilterRibbonEdit_EditValueChanged(object sender, DevExpress.Xpf.Editors.EditValueChangedEventArgs e)
        {
            _currentStatusFilter = e.NewValue?.ToString() ?? "";
            await LoadRequestsAsync();
        }

        private void ClearFilterRibbonButton_Click(object sender, ItemClickEventArgs e)
        {
            StatusFilterRibbonEdit.EditValue = "";
            RequestsGrid.ClearSorting();
            RequestsGrid.ClearGrouping();
        }

        private void AutoRefreshRibbonCheck_CheckedChanged(object sender, ItemClickEventArgs e)
        {
            if (AutoRefreshRibbonCheck.IsChecked == true)
            {
                StartAutoRefresh();
            }
            else
            {
                StopAutoRefresh();
            }
        }

        private void RefreshIntervalRibbonEdit_EditValueChanged(object sender, DevExpress.Xpf.Editors.EditValueChangedEventArgs e)
        {
            if (AutoRefreshRibbonCheck.IsChecked == true)
            {
                StartAutoRefresh(); // Újraindítás új időközzel
            }
        }

        #endregion

        #region Grid Event Handlers

        private void TableView_FocusedRowChanged(object sender, DevExpress.Xpf.Grid.FocusedRowChangedEventArgs e)
        {
            _selectedRequest = TableView.GetFocusedRow() as PasswordResetRequestDto;
            UpdateRibbonButtonStates();
        }

        #endregion

        #region Private Methods

        private void UpdateRibbonButtonStates()
        {
            bool hasSelection = _selectedRequest != null;
            bool isPending = _selectedRequest?.Status == "Pending";

            ApproveRibbonButton.IsEnabled = hasSelection && isPending;
            RejectRibbonButton.IsEnabled = hasSelection && isPending;
            ViewDetailsRibbonButton.IsEnabled = hasSelection;
        }

        private void StartAutoRefresh()
        {
            StopAutoRefresh();
            
            if (RefreshIntervalRibbonEdit.EditValue is int interval && interval >= 5)
            {
                _autoRefreshTimer = new DispatcherTimer();
                _autoRefreshTimer.Interval = TimeSpan.FromSeconds(interval);
                _autoRefreshTimer.Tick += async (s, e) => await LoadRequestsAsync();
                _autoRefreshTimer.Start();
            }
        }

        private void StopAutoRefresh()
        {
            _autoRefreshTimer?.Stop();
            _autoRefreshTimer = null;
        }

        private Window CreateNotesInputDialog(string title, string description)
        {
            try
            {
                return new AdminNotesDialog(title, description);
            }
            catch
            {
                // Fallback egyszerű input dialog
                return CreateSimpleInputDialog(title, description);
            }
        }

        private Window CreateSimpleInputDialog(string title, string description)
        {
            var inputWindow = new Window
            {
                Title = title,
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Window.GetWindow(this)
            };

            var panel = new StackPanel { Margin = new Thickness(20) };
            panel.Children.Add(new TextBlock { Text = description, Margin = new Thickness(0, 0, 0, 10) });
            
            var textBox = new TextBox { Height = 80, TextWrapping = TextWrapping.Wrap, AcceptsReturn = true };
            panel.Children.Add(textBox);

            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Right, Margin = new Thickness(0, 15, 0, 0) };
            var okButton = new Button { Content = "OK", Width = 75, Height = 30, Margin = new Thickness(0, 0, 10, 0) };
            var cancelButton = new Button { Content = "Mégse", Width = 75, Height = 30 };

            bool? dialogResult = null;
            okButton.Click += (s, args) => { 
                inputWindow.Tag = textBox.Text.Trim();
                dialogResult = true; 
                inputWindow.Close(); 
            };
            cancelButton.Click += (s, args) => { dialogResult = false; inputWindow.Close(); };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            panel.Children.Add(buttonPanel);

            inputWindow.Content = panel;
            inputWindow.Tag = "";
            
            return inputWindow;
        }

        private void ShowRequestDetailsSimple(PasswordResetRequestDto request)
        {
            var details = $"Kérelem részletei:\n\n" +
                         $"ID: {request.Id}\n" +
                         $"Felhasználó: {request.UserDisplayName}\n" +
                         $"E-mail: {request.UserEmail}\n" +
                         $"Kérelem dátuma: {request.RequestDate:yyyy.MM.dd HH:mm}\n" +
                         $"Állapot: {request.Status}\n" +
                         $"Forrás: {request.RequestSource}\n";

            if (!string.IsNullOrEmpty(request.ProcessedByAdminName))
            {
                details += $"Feldolgozta: {request.ProcessedByAdminName}\n";
                details += $"Feldolgozás dátuma: {request.ProcessedDate:yyyy.MM.dd HH:mm}\n";
            }

            if (!string.IsNullOrEmpty(request.AdminNotes))
            {
                details += $"Admin jegyzet: {request.AdminNotes}";
            }

            DXMessageBox.Show(details, "Kérelem részletei", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void UpdateLastRefreshTime()
        {
            LastRefreshLabel.Text = $"Utolsó frissítés: {DateTime.Now:HH:mm:ss}";
        }

        private async Task LoadRequestsAsync()
        {
            if (_isLoading) return;

            _isLoading = true;
            LoadingIndicator.Visibility = Visibility.Visible;
            StatusLabel.Text = "Betöltés...";
            
            try
            {
                // API hívás vagy szimulált adatok
                var sampleRequests = new List<PasswordResetRequestDto>
                {
                    new PasswordResetRequestDto
                    {
                        Id = 1,
                        UserEmail = "<EMAIL>",
                        UserDisplayName = "Teszt Felhasználó",
                        RequestDate = DateTime.Now.AddHours(-2),
                        Status = "Pending",
                        RequestSource = "Desktop"
                    },
                    new PasswordResetRequestDto
                    {
                        Id = 2,
                        UserEmail = "<EMAIL>",
                        UserDisplayName = "Másik Felhasználó",
                        RequestDate = DateTime.Now.AddDays(-1),
                        Status = "Completed",
                        ProcessedByAdminName = "Admin User",
                        ProcessedDate = DateTime.Now.AddHours(-1),
                        AdminNotes = "Sikeresen feldolgozva",
                        RequestSource = "Desktop"
                    }
                };

                _requests.Clear();
                
                var filteredRequests = string.IsNullOrEmpty(_currentStatusFilter) 
                    ? sampleRequests 
                    : sampleRequests.Where(r => r.Status == _currentStatusFilter);
                
                foreach (var request in filteredRequests)
                {
                    _requests.Add(request);
                }
                
                UpdateStatusLabels(sampleRequests);
                UpdateRibbonButtonStates();
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
                LoadingIndicator.Visibility = Visibility.Collapsed;
                StatusLabel.Text = "Készen";
            }
        }

        private void UpdateStatusLabels(List<PasswordResetRequestDto> allRequests)
        {
            RecordCountLabel.Text = $"Kérelmek: {_requests.Count}";
            PendingCountLabel.Text = allRequests.Count(r => r.Status == "Pending").ToString();
            CompletedCountLabel.Text = allRequests.Count(r => r.Status == "Completed").ToString();
        }

        private async Task ProcessRequestAsync(long requestId, string action, string notes)
        {
            _isLoading = true;
            LoadingIndicator.Visibility = Visibility.Visible;
            StatusLabel.Text = "Feldolgozás...");
            
            try
            {
                // TODO: API hívás implementálása
                // var success = await _apiClient.ProcessPasswordResetRequestAsync(requestId, action, notes);
                
                // Szimulált siker
                await Task.Delay(1000);
                var success = true;
                
                if (success)
                {
                    DXMessageBox.Show("Kérelem sikeresen feldolgozva!", "Siker", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadRequestsAsync();
                    UpdateLastRefreshTime();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt a kérelem feldolgozása során!", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
                LoadingIndicator.Visibility = Visibility.Collapsed;
                StatusLabel.Text = "Készen";
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}