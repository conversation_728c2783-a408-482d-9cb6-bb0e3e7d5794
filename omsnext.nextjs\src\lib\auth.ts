// src/lib/auth.ts
import { jwtVerify } from "jose";

// Secret key for JWT verification (should be stored in environment variables)
const JWT_SECRET = new TextEncoder().encode(
  process.env.JWT_SECRET || "your-secret-key-here"
);

export interface UserJwtPayload {
  jti: string;
  iat: number;
  exp: number;
  userId: string;
  email: string;
  roles: string[];
}

/**
 * Verifies the JWT token and returns the payload if valid
 */
export async function verifyToken(
  token: string
): Promise<UserJwtPayload | null> {
  try {
    const verified = await jwtVerify(token, JWT_SECRET);
    return verified.payload as unknown as UserJwtPayload;
  } catch (error) {
    console.error("Token verification failed:", error);
    return null;
  }
}

/**
 * Gets the token from cookies (client-side)
 */
export function getTokenFromCookies() {
  if (typeof document !== "undefined") {
    const cookies = document.cookie.split(";");
    for (const cookie of cookies) {
      const [name, value] = cookie.trim().split("=");
      if (name === "oms_token") {
        return decodeURIComponent(value);
      }
    }
  }
  return null;
}

/**
 * Checks if the user is authenticated (client-side)
 */
export async function isAuthenticated() {
  const token = getTokenFromCookies();
  if (!token) return false;

  const payload = await verifyToken(token);
  return payload !== null;
}

/**
 * Gets the user payload from the token (client-side)
 */
export async function getUserPayload() {
  const token = getTokenFromCookies();
  if (!token) return null;

  return await verifyToken(token);
}
