﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;
using omsnext.wpf.Services;
using omsnext.shared.DTO;

namespace omsnext.wpf
{
    public partial class AdminDashboardUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<PasswordResetRequestDto> _requests;
        private bool _isLoading;
        private string _currentStatusFilter = "Pending";
        private PasswordResetRequestDto? _selectedRequest;

        public event PropertyChangedEventHandler? PropertyChanged;

        public AdminDashboardUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _requests = new ObservableCollection<PasswordResetRequestDto>();
            
            DataContext = this;
            RequestsGrid.ItemsSource = _requests;
            
            Loaded += AdminDashboardUserControl_Loaded;
        }

        private async void AdminDashboardUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
        }

        #region Event Handlers

        private async void ApproveButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRequest?.Status == "Pending")
            {
                var result = DXMessageBox.Show(
                    $"Biztosan jóváhagyja a jelszó visszaállítási kérelmet?\n\nFelhasználó: {_selectedRequest.UserDisplayName}\nE-mail: {_selectedRequest.UserEmail}", 
                    "Kérelem jóváhagyása", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await ProcessRequestAsync(_selectedRequest.Id, "Approved", "Adminisztrátor által jóváhagyva");
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük, válasszon ki egy függő állapotú kérelmet!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void RejectButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRequest?.Status == "Pending")
            {
                var inputWindow = CreateSimpleInputDialog("Kérelem elutasítása", "Kérjük, adja meg az elutasítás okát:");
                if (inputWindow.ShowDialog() == true && inputWindow.Tag is string notes && !string.IsNullOrWhiteSpace(notes))
                {
                    await ProcessRequestAsync(_selectedRequest.Id, "Rejected", notes);
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük, válasszon ki egy függő állapotú kérelmet!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            if (_selectedRequest != null)
            {
                ShowRequestDetailsSimple(_selectedRequest);
            }
            else
            {
                DXMessageBox.Show("Kérjük, válasszon ki egy kérelmet a részletek megtekintéséhez!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
        }

        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Egyszerűsített export - TODO: implementálni
                DXMessageBox.Show("Export funkció hamarosan elérhető!", "Információ", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba az export során: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _currentStatusFilter = selectedItem.Tag?.ToString() ?? "";
                await LoadRequestsAsync();
            }
        }

        private void TableView_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            _selectedRequest = RequestsGrid.GetFocusedRow() as PasswordResetRequestDto;
            UpdateButtonStates();
        }

        #endregion

        #region Private Methods

        private void UpdateButtonStates()
        {
            bool hasSelection = _selectedRequest != null;
            bool isPending = _selectedRequest?.Status == "Pending";

            ApproveButton.IsEnabled = hasSelection && isPending;
            RejectButton.IsEnabled = hasSelection && isPending;
            ViewButton.IsEnabled = hasSelection;
        }

        private Window CreateSimpleInputDialog(string title, string description)
        {
            var inputWindow = new Window
            {
                Title = title,
                Width = 400,
                Height = 200,
                WindowStartupLocation = WindowStartupLocation.CenterOwner,
                Owner = Window.GetWindow(this)
            };

            var panel = new StackPanel { Margin = new Thickness(20) };
            panel.Children.Add(new TextBlock { Text = description, Margin = new Thickness(0, 0, 0, 10) });
            
            var textBox = new TextBox { Height = 80, TextWrapping = TextWrapping.Wrap, AcceptsReturn = true };
            panel.Children.Add(textBox);

            var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Right, Margin = new Thickness(0, 15, 0, 0) };
            var okButton = new Button { Content = "OK", Width = 75, Height = 30, Margin = new Thickness(0, 0, 10, 0) };
            var cancelButton = new Button { Content = "Mégse", Width = 75, Height = 30 };

            bool? dialogResult = null;
            okButton.Click += (s, args) => { 
                inputWindow.Tag = textBox.Text.Trim();
                dialogResult = true; 
                inputWindow.Close(); 
            };
            cancelButton.Click += (s, args) => { dialogResult = false; inputWindow.Close(); };

            buttonPanel.Children.Add(okButton);
            buttonPanel.Children.Add(cancelButton);
            panel.Children.Add(buttonPanel);

            inputWindow.Content = panel;
            inputWindow.Tag = "";
            
            return inputWindow;
        }

        private void ShowRequestDetailsSimple(PasswordResetRequestDto request)
        {
            var details = $"Kérelem részletei:\n\n" +
                         $"ID: {request.Id}\n" +
                         $"Felhasználó: {request.UserDisplayName}\n" +
                         $"E-mail: {request.UserEmail}\n" +
                         $"Kérelem dátuma: {request.RequestDate:yyyy.MM.dd HH:mm}\n" +
                         $"Állapot: {request.Status}\n" +
                         $"Forrás: {request.RequestSource}\n";

            if (!string.IsNullOrEmpty(request.ProcessedByAdminName))
            {
                details += $"Feldolgozta: {request.ProcessedByAdminName}\n";
                details += $"Feldolgozás dátuma: {request.ProcessedDate:yyyy.MM.dd HH:mm}\n";
            }

            if (!string.IsNullOrEmpty(request.AdminNotes))
            {
                details += $"Admin jegyzet: {request.AdminNotes}";
            }

            DXMessageBox.Show(details, "Kérelem részletei", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private async Task LoadRequestsAsync()
        {
            if (_isLoading) return;

            _isLoading = true;
            StatusLabel.Text = "Betöltés...";
            
            try
            {
                // Szimulált adatok egyenlőre
                var sampleRequests = new List<PasswordResetRequestDto>
                {
                    new PasswordResetRequestDto
                    {
                        Id = 1,
                        UserEmail = "<EMAIL>",
                        UserDisplayName = "Teszt Felhasználó",
                        RequestDate = DateTime.Now.AddHours(-2),
                        Status = "Pending",
                        RequestSource = "Desktop"
                    },
                    new PasswordResetRequestDto
                    {
                        Id = 2,
                        UserEmail = "<EMAIL>",
                        UserDisplayName = "Másik Felhasználó",
                        RequestDate = DateTime.Now.AddDays(-1),
                        Status = "Completed",
                        ProcessedByAdminName = "Admin User",
                        ProcessedDate = DateTime.Now.AddHours(-1),
                        AdminNotes = "Sikeresen feldolgozva",
                        RequestSource = "Desktop"
                    }
                };

                _requests.Clear();
                
                var filteredRequests = string.IsNullOrEmpty(_currentStatusFilter) 
                    ? sampleRequests 
                    : sampleRequests.Where(r => r.Status == _currentStatusFilter);
                
                foreach (var request in filteredRequests)
                {
                    _requests.Add(request);
                }
                
                UpdateStatusLabels(sampleRequests);
                UpdateButtonStates();
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
                StatusLabel.Text = "Készen";
            }
        }

        private void UpdateStatusLabels(List<PasswordResetRequestDto> allRequests)
        {
            RecordCountLabel.Text = $"Kérelmek: {_requests.Count}";
            PendingCountLabel.Text = allRequests.Count(r => r.Status == "Pending").ToString();
            CompletedCountLabel.Text = allRequests.Count(r => r.Status == "Completed").ToString();
        }

        private async Task ProcessRequestAsync(long requestId, string action, string notes)
        {
            _isLoading = true;
            StatusLabel.Text = "Feldolgozás...";
            
            try
            {
                // TODO: API hívás implementálása
                // var success = await _apiClient.ProcessPasswordResetRequestAsync(requestId, action, notes);
                
                // Szimulált siker
                await Task.Delay(1000);
                var success = true;
                
                if (success)
                {
                    DXMessageBox.Show("Kérelem sikeresen feldolgozva!", "Siker", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadRequestsAsync();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt a kérelem feldolgozása során!", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
                StatusLabel.Text = "Készen";
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #endregion
    }
}