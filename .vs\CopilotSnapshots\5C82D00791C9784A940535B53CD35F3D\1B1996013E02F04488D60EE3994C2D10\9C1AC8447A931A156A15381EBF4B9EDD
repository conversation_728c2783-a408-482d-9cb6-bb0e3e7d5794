﻿using System.Windows;
using DevExpress.Xpf.Core;
using omsnext.wpf.Services;
using System.Text.RegularExpressions;

namespace omsnext.wpf;

public partial class LoginWindow : ThemedWindow
{
    private readonly ApiClient _apiClient;

    public LoginWindow()
    {
        InitializeComponent();
        _apiClient = new ApiClient();
        
        // Enter key támogatás
        KeyDown += LoginWindow_KeyDown;
        EmailTextEdit.KeyDown += Input_KeyDown;
        PasswordBoxEdit.KeyDown += Input_KeyDown;
        ResetEmailTextEdit.KeyDown += ResetInput_KeyDown;
    }

    private void LoginWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == System.Windows.Input.Key.Escape)
        {
            Close();
        }
    }

    private void Input_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == System.Windows.Input.Key.Enter && LoginPanel.Visibility == Visibility.Visible)
        {
            LoginButton_Click(sender, new RoutedEventArgs());
        }
    }

    private void ResetInput_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == System.Windows.Input.Key.Enter && ForgotPasswordPanel.Visibility == Visibility.Visible)
        {
            ResetPasswordButton_Click(sender, new RoutedEventArgs());
        }
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        HideMessages();
        SetLoadingState(true);

        try
        {
            var email = EmailTextEdit.Text?.Trim();
            var password = PasswordBoxEdit.Password;

            // Validálás
            if (string.IsNullOrEmpty(email))
            {
                ShowError("Kérjük, adja meg az e-mail címét!");
                return;
            }

            if (!IsValidEmail(email))
            {
                ShowError("Kérjük, adjon meg egy érvényes e-mail címet!");
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowError("Kérjük, adja meg a jelszavát!");
                return;
            }

            // Bejelentkezés
            var loginResponse = await _apiClient.LoginAsync(email, password);

            if (loginResponse != null && !string.IsNullOrEmpty(loginResponse.Token))
            {
                var mainWindow = new MainWindow(loginResponse, _apiClient);
                mainWindow.Show();
                this.Close();
            }
            else
            {
                ShowError("Hibás e-mail cím vagy jelszó!");
            }
        }
        catch (Exception ex)
        {
            ShowError($"Kapcsolódási hiba: {ex.Message}");
        }
        finally
        {
            SetLoadingState(false);
        }
    }

    private void SetLoadingState(bool isLoading)
    {
        LoginButton.IsEnabled = !isLoading;
        EmailTextEdit.IsEnabled = !isLoading;
        PasswordBoxEdit.IsEnabled = !isLoading;
        
        LoadingIndicator.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
        LoadingIndicator.DeferedVisibility = isLoading;
    }

    private void ShowError(string message)
    {
        ErrorMessage.Text = message;
        ErrorPanel.Visibility = Visibility.Visible;
    }

    private void HideMessages()
    {
        ErrorPanel.Visibility = Visibility.Collapsed;
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var emailRegex = new System.Text.RegularExpressions.Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }

    private void ForgotPasswordLink_Click(object sender, RoutedEventArgs e)
    {
        LoginPanel.Visibility = Visibility.Collapsed;
        ForgotPasswordPanel.Visibility = Visibility.Visible;
        HideMessages();
        
        if (!string.IsNullOrEmpty(EmailTextEdit.Text))
        {
            ResetEmailTextEdit.Text = EmailTextEdit.Text;
        }
    }

    private void BackButton_Click(object sender, RoutedEventArgs e)
    {
        ForgotPasswordPanel.Visibility = Visibility.Collapsed;
        LoginPanel.Visibility = Visibility.Visible;
        HideMessages();
    }

    private async void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
    {
        HideMessages();

        var email = ResetEmailTextEdit.Text?.Trim();

        if (string.IsNullOrEmpty(email) || !IsValidEmail(email))
        {
            ShowError("Kérjük, adjon meg egy érvényes e-mail címet!");
            return;
        }

        try
        {
            // Szimulált jelszó visszaállítás
            await Task.Delay(2000);
            ShowSuccess($"Jelszó visszaállítási linket küldtünk a {email} címre!");
            
            await Task.Delay(3000);
            BackButton_Click(sender, e);
        }
        catch (Exception ex)
        {
            ShowError($"Hiba történt: {ex.Message}");
        }
    }

    private void ShowSuccess(string message)
    {
        SuccessMessage.Text = message;
        SuccessPanel.Visibility = Visibility.Visible;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }
}