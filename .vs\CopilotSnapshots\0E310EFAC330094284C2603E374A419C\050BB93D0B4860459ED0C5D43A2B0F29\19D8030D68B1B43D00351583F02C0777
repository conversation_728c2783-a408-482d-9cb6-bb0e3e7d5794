﻿<Window x:Class="omsnext.wpf.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="OmsNext - Bejelentkezés" Height="400" Width="350"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Grid>
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                <GradientStop Color="#f8f9fa" Offset="0"/>
                <GradientStop Color="#e9ecef" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>
        
        <Border Background="White" 
                CornerRadius="10" 
                Margin="30"
                Effect="{StaticResource {x:Static SystemParameters.DropShadowKey}}">
            <StackPanel Margin="40,30">
                <!-- Logo/Title -->
                <TextBlock Text="OmsNext" 
                           FontSize="28" 
                           FontWeight="Bold" 
                           HorizontalAlignment="Center" 
                           Foreground="#2c3e50" 
                           Margin="0,0,0,30"/>
                
                <!-- Email -->
                <Label Content="E-mail cím:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <TextBox x:Name="EmailTextBox" 
                         Height="35" 
                         Padding="10,0" 
                         FontSize="14"
                         Margin="0,0,0,15"/>
                
                <!-- Password -->
                <Label Content="Jelszó:" FontWeight="SemiBold" Margin="0,0,0,5"/>
                <PasswordBox x:Name="PasswordBox" 
                             Height="35" 
                             Padding="10,0" 
                             FontSize="14"
                             Margin="0,0,0,20"/>
                
                <!-- Login Button -->
                <Button x:Name="LoginButton" 
                        Content="Bejelentkezés" 
                        Height="40" 
                        FontSize="16" 
                        FontWeight="SemiBold"
                        Background="#007bff" 
                        Foreground="White" 
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="LoginButton_Click"/>
                
                <!-- Error Message -->
                <TextBlock x:Name="ErrorMessage" 
                           Foreground="Red" 
                           HorizontalAlignment="Center" 
                           Margin="0,15,0,0" 
                           TextWrapping="Wrap"
                           Visibility="Collapsed"/>
                
                <!-- Loading Indicator -->
                <ProgressBar x:Name="LoadingProgressBar" 
                             Height="4" 
                             IsIndeterminate="True" 
                             Margin="0,15,0,0"
                             Visibility="Collapsed"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>