﻿using DevExpress.Xpo;
using Microsoft.AspNetCore.Mvc;
using omsnext.core.Models;

namespace omsnext.api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class KtforgevWpfController : ControllerBase
{
    private readonly Session _session;

    public KtforgevWpfController(Session session)
    {
        _session = session;
    }

    [HttpGet]
    public async Task<IActionResult> Get(
        [FromQuery] int skip = 0,
        [FromQuery] int take = 100,
        [FromQuery] string? sortField = null,
        [FromQuery] string? sortDirection = "asc",
        [FromQuery] string? filterField = null,
        [FromQuery] string? filterValue = null,
        [FromQuery] string? filterOperator = "contains")
    {
        try
        {
            IQueryable<Ktforgev> query = new XPQuery<Ktforgev>(_session);

            // Szűrés alkalmazása
            if (!string.IsNullOrEmpty(filterField) && !string.IsNullOrEmpty(filterValue))
            {
                query = ApplyFilter(query, filterField, filterValue, filterOperator);
            }

            // Rendezés alkalmazása
            if (!string.IsNullOrEmpty(sortField))
            {
                query = ApplySort(query, sortField, sortDirection);
            }
            else
            {
                query = query.OrderBy(x => x.id);
            }

            // Teljes rekordszám lekérdezése a szűrés után
            var totalCount = await Task.Run(() => query.Count());

            // Lapozás és projekció
            var items = await Task.Run(() =>
                query
                    .Skip(skip)
                    .Take(take)
                    .Select(x => new KtforgevDto
                    {
                        id = x.id,
                        hiv_szam = x.hiv_szam,
                        tk = x.tk,
                        fkv_1 = x.fkv_1,
                        dev_nem = x.dev_nem,
                        dev_ert = x.dev_ert,
                        fkv_2 = x.fkv_2,
                        fkv_3 = x.fkv_3,
                        mnk_szam = x.mnk_szam,
                        fda_t = x.fda_t,
                        rog_zito = x.rog_zito,
                        rog_dat = x.rog_dat,
                        meg_nev = x.meg_nev,
                        rel_azon = x.rel_azon,
                        nap_lo = x.nap_lo,
                        fej = x.fej,
                        ert_ek = x.ert_ek,
                        biz_dat = x.biz_dat,
                        par_kod = x.par_kod,
                        dol_kod = x.dol_kod,
                        cos_tcenter = x.cos_tcenter,
                        rel_azon2 = x.rel_azon2,
                        ber_kat = x.ber_kat,
                        men_nyi = x.men_nyi,
                        pro_jekt = x.pro_jekt,
                        afa_kod = x.afa_kod,
                        ert_ek_signed = x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek),
                        created_at = x.created_at,
                        updated_at = x.updated_at
                    })
                    .ToList());

            return Ok(new
            {
                data = items,
                totalCount = totalCount,
                skip = skip,
                take = take
            });
        }
        catch (Exception ex)
        {
            return BadRequest(new { error = ex.Message });
        }
    }

    private XPQuery<Ktforgev> ApplyFilter(XPQuery<Ktforgev> query, string field, string value, string op)
    {
        switch (field.ToLower())
        {
            case "hiv_szam":
                switch (op)
                {
                    case "contains":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.hiv_szam.Contains(value));
                    case "equals":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.hiv_szam == value);
                    case "startswith":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.hiv_szam.StartsWith(value));
                    default:
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.hiv_szam.Contains(value));
                }
            case "tk":
                return new XPQuery<Ktforgev>(query.Session).Where(x => x.tk == value);
            case "fkv_1":
                switch (op)
                {
                    case "contains":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.fkv_1.Contains(value));
                    case "equals":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.fkv_1 == value);
                    default:
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.fkv_1.Contains(value));
                }
            case "meg_nev":
                switch (op)
                {
                    case "contains":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.meg_nev.Contains(value));
                    case "equals":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.meg_nev == value);
                    default:
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.meg_nev.Contains(value));
                }
            case "par_kod":
                switch (op)
                {
                    case "contains":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.par_kod.Contains(value));
                    case "equals":
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.par_kod == value);
                    default:
                        return new XPQuery<Ktforgev>(query.Session).Where(x => x.par_kod.Contains(value));
                }
            case "ert_ek":
                if (decimal.TryParse(value, out var ertEkValue))
                {
                    switch (op)
                    {
                        case "equals":
                            return new XPQuery<Ktforgev>(query.Session).Where(x => x.ert_ek == ertEkValue);
                        case "greater":
                            return new XPQuery<Ktforgev>(query.Session).Where(x => x.ert_ek > ertEkValue);
                        case "less":
                            return new XPQuery<Ktforgev>(query.Session).Where(x => x.ert_ek < ertEkValue);
                        default:
                            return new XPQuery<Ktforgev>(query.Session).Where(x => x.ert_ek == ertEkValue);
                    }
                }
                return query;
            default:
                return query;
        }
    }

    private IOrderedQueryable<Ktforgev> ApplySort(XPQuery<Ktforgev> query, string field, string direction)
    {
        var isAscending = direction.ToLower() != "desc";
        
        return field.ToLower() switch
        {
            "id" => isAscending ? query.OrderBy(x => x.id) : query.OrderByDescending(x => x.id),
            "hiv_szam" => isAscending ? query.OrderBy(x => x.hiv_szam) : query.OrderByDescending(x => x.hiv_szam),
            "tk" => isAscending ? query.OrderBy(x => x.tk) : query.OrderByDescending(x => x.tk),
            "fkv_1" => isAscending ? query.OrderBy(x => x.fkv_1) : query.OrderByDescending(x => x.fkv_1),
            "meg_nev" => isAscending ? query.OrderBy(x => x.meg_nev) : query.OrderByDescending(x => x.meg_nev),
            "ert_ek" => isAscending ? query.OrderBy(x => x.ert_ek) : query.OrderByDescending(x => x.ert_ek),
            "ert_ek_signed" => isAscending ? 
                query.OrderBy(x => x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek)) : 
                query.OrderByDescending(x => x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek)),
            "created_at" => isAscending ? query.OrderBy(x => x.created_at) : query.OrderByDescending(x => x.created_at),
            _ => isAscending ? query.OrderBy(x => x.id) : query.OrderByDescending(x => x.id)
        };
    }
}