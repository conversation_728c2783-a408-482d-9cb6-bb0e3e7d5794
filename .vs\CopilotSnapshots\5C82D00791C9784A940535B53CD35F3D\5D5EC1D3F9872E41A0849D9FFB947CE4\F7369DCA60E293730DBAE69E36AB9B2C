﻿<UserControl x:Class="omsnext.wpf.KtforgevUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Fejléc és szűrő rész -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" VerticalAlignment="Center">
            <TextBlock Text="Ktforgev adatok" FontSize="20" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
            
            <TextBlock Text="Szűrő mező:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <ComboBox x:Name="FilterFieldComboBox" Width="120" VerticalAlignment="Center" Margin="0,0,10,0">
                <ComboBoxItem Content="hiv_szam" Tag="hiv_szam"/>
                <ComboBoxItem Content="tk" Tag="tk"/>
                <ComboBoxItem Content="fkv_1" Tag="fkv_1"/>
                <ComboBoxItem Content="meg_nev" Tag="meg_nev"/>
                <ComboBoxItem Content="par_kod" Tag="par_kod"/>
                <ComboBoxItem Content="ert_ek" Tag="ert_ek"/>
            </ComboBox>
            
            <TextBlock Text="Érték:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <TextBox x:Name="FilterValueTextBox" Width="150" VerticalAlignment="Center" Margin="0,0,10,0"/>
            
            <Button x:Name="ApplyFilterButton" Content="Szűrés" Click="ApplyFilterButton_Click" Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="ClearFilterButton" Content="Törlés" Click="ClearFilterButton_Click" Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="RefreshButton" Content="Frissítés" Click="RefreshButton_Click" Padding="10,5"/>
        </StackPanel>

        <!-- DevExpress GridControl Server Mode-dal -->
        <dxg:GridControl x:Name="KtforgevGrid" Grid.Row="1" Margin="10">
            <dxg:GridControl.Columns>
                <dxg:GridColumn FieldName="id" Header="ID" Width="80" AllowSorting="True"/>
                <dxg:GridColumn FieldName="hiv_szam" Header="Hivatkozási szám" Width="150" AllowSorting="True"/>
                <dxg:GridColumn FieldName="tk" Header="T/K" Width="50" AllowSorting="True"/>
                <dxg:GridColumn FieldName="fkv_1" Header="FKV 1" Width="100" AllowSorting="True"/>
                <dxg:GridColumn FieldName="dev_nem" Header="Deviza nem" Width="80" AllowSorting="True"/>
                <dxg:GridColumn FieldName="dev_ert" Header="Deviza érték" Width="100" AllowSorting="True"/>
                <dxg:GridColumn FieldName="fkv_2" Header="FKV 2" Width="100" AllowSorting="True"/>
                <dxg:GridColumn FieldName="fkv_3" Header="FKV 3" Width="100" AllowSorting="True"/>
                <dxg:GridColumn FieldName="mnk_szam" Header="Munkaügyi szám" Width="120" AllowSorting="True"/>
                <dxg:GridColumn FieldName="meg_nev" Header="Megnevezés" Width="200" AllowSorting="True"/>
                <dxg:GridColumn FieldName="ert_ek" Header="Érték" Width="120" AllowSorting="True"/>
                <dxg:GridColumn FieldName="ert_ek_signed" Header="Előjeles érték" Width="120" AllowSorting="True"/>
                <dxg:GridColumn FieldName="par_kod" Header="Partner kód" Width="100" AllowSorting="True"/>
                <dxg:GridColumn FieldName="created_at" Header="Létrehozva" Width="120" AllowSorting="True"/>
            </dxg:GridControl.Columns>
            
            <dxg:GridControl.View>
                <dxg:TableView x:Name="TableView" 
                             AllowSorting="True" 
                             AllowGrouping="True" 
                             AllowColumnFiltering="True"
                             ShowGroupPanel="True"
                             AutoWidth="True"
                             NavigationStyle="Row"
                             ShowVerticalLines="False"
                             ShowHorizontalLines="True"
                             AllowPerPixelScrolling="True"
                             PageSize="1000"/>
            </dxg:GridControl.View>
        </dxg:GridControl>

        <!-- Státusz sor -->
        <StatusBar Grid.Row="2" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="StatusLabel" Text="Készen"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="RecordCountLabel" Text="Rekordok: 0"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="10,0"/>
                    <TextBlock x:Name="PageInfoLabel" Text=""/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
