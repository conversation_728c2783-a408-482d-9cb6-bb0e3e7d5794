﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>DevExpress.Xpf.Ribbon.v25.1</name>
  </assembly>
  <members>
    <member name="N:DevExpress.Xpf.Ribbon">
      <summary>
        <para>Contains classes that implement the Ribbon UI. To use these classes in XAML code, add the xmlns:dxr=”“ namespace reference.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.ApplicationMenu">
      <summary>
        <para>The Windows Explorer style popup menu.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.ApplicationMenu.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.BottomPane">
      <summary>
        <para>Gets or sets the control displayed along the bottom edge of the ApplicationMenu.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The control displayed along the bottom edge of the ApplicationMenu.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenu.BottomPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenu.BottomPane">ApplicationMenu.BottomPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.IsMenuEmpty">
      <summary>
        <para>Gets whether the ApplicationMenu contains visible items.</para>
      </summary>
      <value>true if the application menu contains visible items; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.ApplicationMenu.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.Ribbon">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> to which the current <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu"/> object belongs.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> to which the current <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu"/> object belongs.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.RibbonStyle">
      <summary>
        <para>Gets the ApplicationMenu’s paint style.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonStyle"/> value that specifies the ApplicationMenu’s paint style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenu.RibbonStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenu.RibbonStyle">ApplicationMenu.RibbonStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.RightPane">
      <summary>
        <para>Gets or sets the control displayed within the ApplicationMenu’s right pane.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The control displayed within the ApplicationMenu’s right pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenu.RightPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenu.RightPane">ApplicationMenu.RightPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.RightPaneWidth">
      <summary>
        <para>Gets or sets the right pane’s width.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An integer value which specifies the right pane’s width, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenu.RightPaneWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenu.RightPaneWidth">ApplicationMenu.RightPaneWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenu.ShowRightPane">
      <summary>
        <para>Gets or sets whether the ApplicationMenu’s right pane is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the ApplicationMenu’s right pane is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenu.ShowRightPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenu.ShowRightPane">ApplicationMenu.ShowRightPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.ApplicationMenuInfo">
      <summary>
        <para>This class is obsolete. Use the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu"/> class instead.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenuInfo"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.BottomPane">
      <summary>
        <para>Gets or sets the control displayed along the bottom edge of the ApplicationMenu.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The control displayed along the bottom edge of the ApplicationMenu.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.BottomPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.BottomPane">ApplicationMenuInfo.BottomPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.RightPane">
      <summary>
        <para>Gets or sets the control displayed within the ApplicationMenu’s right pane.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The control displayed within the ApplicationMenu’s right pane.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.RightPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.RightPane">ApplicationMenuInfo.RightPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.RightPaneWidth">
      <summary>
        <para>Gets or sets the right pane’s width.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An integer value which specifies the right pane’s width, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.RightPaneWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.RightPaneWidth">ApplicationMenuInfo.RightPaneWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.ShowRightPane">
      <summary>
        <para>Gets or sets whether the ApplicationMenu’s right pane is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the ApplicationMenu’s right pane is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.ShowRightPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.ApplicationMenuInfo.ShowRightPane">ApplicationMenuInfo.ShowRightPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageButtonItem">
      <summary>
        <para>A button item within a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageButtonItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageButtonItem"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.Command">
      <summary>
        <para>Gets or sets a command fired when the button item is clicked.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Input.ICommand"/> object, fired when the button item is clicked.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandParameter">
      <summary>
        <para>Gets or sets additional parameters for a <see cref="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.Command">BackstageButtonItem.Command</see>.</para>
      </summary>
      <value>An object defining command parameters.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandParameterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandParameter">BackstageButtonItem.CommandParameter</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.Command">BackstageButtonItem.Command</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandTarget">
      <summary>
        <para>Gets or sets a target control on which a command is executed.</para>
      </summary>
      <value>An <see cref="T:System.Windows.IInputElement"/> object .</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandTargetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.CommandTarget">BackstageButtonItem.CommandTarget</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.KeyGesture">
      <summary>
        <para>Gets or sets a keyboard combination that can be used to invoke the current button’s functionality.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A KeyGesture object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageButtonItem.KeyGestureProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageButtonItem.KeyGesture">BackstageButtonItem.KeyGesture</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageButtonItem.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageItem">
      <summary>
        <para>Provides a base class for backstage clickable items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageItem"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItem.Content">
      <summary>
        <para>Gets or sets an item’s caption.</para>
      </summary>
      <value>An <see cref="T:System.Object"/> that specifies an item’s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItem.ContentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItem.Content">BackstageItem.Content</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItem.ContentTemplate">
      <summary>
        <para>Gets or sets a template for an object assigned to the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItem.Content">BackstageItem.Content</see>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> for an object assigned to the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItem.Content">BackstageItem.Content</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItem.ContentTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItem.ContentTemplate">BackstageItem.ContentTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItem.KeyTip">
      <summary>
        <para>Gets or sets an item’s key tip.</para>
      </summary>
      <value>A string object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItem.KeyTipProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItem.KeyTip">BackstageItem.KeyTip</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItem.RibbonStyle">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Ribbon.BackstageItem"/>‘s paint style. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonStyle"/> enumeration value that specifies the paint style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItem.RibbonStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItem.RibbonStyle">BackstageItem.RibbonStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageItemBase">
      <summary>
        <para>A base for classes that represent backstage items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageItemBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageItemBase"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualIsEnabled">
      <summary>
        <para>Defines whether an item is enabled or not. This is a dependency property.</para>
      </summary>
      <value>true if an item is enabled ; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualIsEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualIsEnabled">BackstageItemBase.ActualIsEnabled</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualIsFocused">
      <summary>
        <para>Indicates if a backstage item is focused. This is a dependency property.</para>
      </summary>
      <value>true if the item is focused; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualIsFocusedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualIsFocused">BackstageItemBase.ActualIsFocused</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualTextStyle">
      <summary>
        <para>Gets a style, applied to a backstage item’s caption. This is a dependency property.</para>
      </summary>
      <value>A Style object, applied to a backstage item’s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ActualTextStyle">BackstageItemBase.ActualTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.Backstage">
      <summary>
        <para>Gets or sets a BackstageViewControl that owns the current item. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> object that owns the current item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.BackstageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.Backstage">BackstageItemBase.Backstage</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.BorderStyle">
      <summary>
        <para>Gets or sets a style for backstage item borders. This is a dependency property.</para>
      </summary>
      <value>A Style object that is applied to item borders.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.BorderStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.BorderStyle">BackstageItemBase.BorderStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.BackstageItemBase.Click">
      <summary>
        <para>Fires when a backstage item is clicked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ContentStyle">
      <summary>
        <para>Gets or sets a style applied to the item’s contents. This is a dependency property.</para>
      </summary>
      <value>A Style object applied to the item’s contents.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.ContentStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.ContentStyle">BackstageItemBase.ContentStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.HoverTextStyle">
      <summary>
        <para>Gets or sets style settings for the item’s caption when the item is hovered over. This is a dependency property.</para>
      </summary>
      <value>A Style object applied to the item’s caption when the item is hovered over.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.HoverTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.HoverTextStyle">BackstageItemBase.HoverTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.MergeOrder">
      <summary>
        <para>Gets or sets the position of this backstage item within the merged <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>. This is a dependency property.</para>
      </summary>
      <value>An Int32 value that specifies the position of this backstage item within the merged <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.MergeOrderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.MergeOrder">BackstageItemBase.MergeOrder</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.MergeType">
      <summary>
        <para>Gets or sets how backstage controls should merge this item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemMergeType"/> enumerator value that specifies how backstage controls should merge this item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.MergeTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.MergeType">BackstageItemBase.MergeType</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.NormalTextStyle">
      <summary>
        <para>Gets or sets a style for the item’s caption in the normal state.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object for the item’s caption in the normal state.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.NormalTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.NormalTextStyle">BackstageItemBase.NormalTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemBase.Placement">
      <summary>
        <para>Gets or sets the backstage item’s postition. This is a dependency property.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Ribbon.BackstageItemPlacement"/> value that specifies the backstage item’s postition.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemBase.PlacementProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemBase.Placement">BackstageItemBase.Placement</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageItemPlacement">
      <summary>
        <para>Lists values that specify where the backstage item is displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemPlacement.Bottom">
      <summary>
        <para>The backstage item is displayed at the bottom.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemPlacement.Top">
      <summary>
        <para>The backstage item is displayed at the top.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageItemWithImage">
      <summary>
        <para>A base class for items with glyphs.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageItemWithImage.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageItemWithImage"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.Glyph">
      <summary>
        <para>Gets or sets the item’s image. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.ImageSource"/> object that is the item’s glyph.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphContainerStyle">
      <summary>
        <para>Gets or set’s a style applied to a container displaying a glyph within the current item. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to a container displaying a glyph.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphContainerStyle">BackstageItemWithImage.GlyphContainerStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.Glyph">BackstageItemWithImage.Glyph</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphStyle">
      <summary>
        <para>Gets or sets a style applied to the item’s glyph (<see cref="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.Glyph">BackstageItemWithImage.Glyph</see>). This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to the item’s glyph.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageItemWithImage.GlyphStyle">BackstageItemWithImage.GlyphStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageSeparatorItem">
      <summary>
        <para>A line that separates neighboring backstage items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageSeparatorItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageSeparatorItem"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageTabItem">
      <summary>
        <para>A tab item within a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageTabItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageTabItem.ControlPane">
      <summary>
        <para>Gets or sets a control displayed in the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s right area when the tab item is selected.</para>
      </summary>
      <value>A control displayed in the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s right area when the tab item is selected.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageTabItem.ControlPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.ControlPane">BackstageTabItem.ControlPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageTabItem.IsSelected">
      <summary>
        <para>Gets whether the current <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> is selected.</para>
      </summary>
      <value>Returns true if the current <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> is selected; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageTabItem.IsSelectedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.IsSelected">BackstageTabItem.IsSelected</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageTabItem.KeyGesture">
      <summary>
        <para>Gets or sets a keyboard combination that can be used to select the current tab.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A KeyGesture object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageTabItem.KeyGestureProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.KeyGesture">BackstageTabItem.KeyGesture</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageTabItem.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageTabItem.SelectedTextStyle">
      <summary>
        <para>Gets or sets a style applied to a tab item when it’s selected. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to a tab item when it’s selected.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageTabItem.SelectedTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.SelectedTextStyle">BackstageTabItem.SelectedTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageViewControl">
      <summary>
        <para>The MS Office style full-screen menu.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ActualBackgroundGlyph">
      <summary>
        <para>Gets an actual background image for the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>. This is a dependency property.</para>
      </summary>
      <value>An ImageSource object, displayed in the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s right bottom corner.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ActualBackgroundGlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ActualBackgroundGlyph">BackstageViewControl.ActualBackgroundGlyph</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ActualControlPane">
      <summary>
        <para>Gets <see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.ControlPane">BackstageTabItem.ControlPane</see> of the currently selected <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/>. This is a dependency property.</para>
      </summary>
      <value>An Object that specifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.ControlPane">BackstageTabItem.ControlPane</see> of the currently selected <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ActualControlPaneProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ActualControlPane">BackstageViewControl.ActualControlPane</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundGlyph">
      <summary>
        <para>Gets or sets a custom background image for the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.ImageSource"/> object that specifies a custom background image for the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundGlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundGlyph">BackstageViewControl.BackgroundGlyph</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundGlyphStyle">
      <summary>
        <para>Gets or sets a style applied to the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s background image. This is a dependency property.</para>
      </summary>
      <value>A style object, applied to the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s background image.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundGlyphStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundGlyphStyle">BackstageViewControl.BackgroundGlyphStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundStyle">
      <summary>
        <para>Gets or sets a style applied to the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s background. This is a dependency property.</para>
      </summary>
      <value>A style object, applied to the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s background.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.BackgroundStyle">BackstageViewControl.BackgroundStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.Close">
      <summary>
        <para>Closes a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.CloseButtonVisibility">
      <summary>
        <para>Gets or sets whether the button that closes the control is visible.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Visibility"/> enumeration value, such as Visible, Hidden or Collapsed, that specifies whether the button that closes the control is visible. The default is Visible.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.CloseButtonVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.CloseButtonVisibility">BackstageViewControl.CloseButtonVisibility</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ComplexLayoutState">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.BackstageViewControl.ComplexLayoutStateChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ComplexLayoutState">BackstageViewControl.ComplexLayoutState</see> is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ControlPaneStyle">
      <summary>
        <para>Gets or sets a style applied to all tab item control panes (<see cref="P:DevExpress.Xpf.Ribbon.BackstageTabItem.ControlPane">BackstageTabItem.ControlPane</see>). This is a dependency property.</para>
      </summary>
      <value>A Style object applied to all tab item control panes.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ControlPaneStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ControlPaneStyle">BackstageViewControl.ControlPaneStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.DisableDefaultBackgroundGlyph">
      <summary>
        <para>Gets or sets whether the default background glyph is visible. This is a dependency property.</para>
      </summary>
      <value>true if the default background glyph is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.DisableDefaultBackgroundGlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.DisableDefaultBackgroundGlyph">BackstageViewControl.DisableDefaultBackgroundGlyph</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.EnableWindowTitleShrink">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/>‘s title bar is overlapped by the content area of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> displayed in full-screen mode. This is a dependency property.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/>‘s title bar is overlapped by the content area of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> displayed in full-screen mode; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.EnableWindowTitleShrinkProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.EnableWindowTitleShrink">BackstageViewControl.EnableWindowTitleShrink</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.GetTabFromIndex(System.Int32)">
      <summary>
        <para>Gets a <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> by its index.</para>
      </summary>
      <param name="tabIndex">An integer value that specifies the required tab item’s index.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> at the specified index.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.GetTabIndex(DevExpress.Xpf.Ribbon.BackstageTabItem)">
      <summary>
        <para>Gets the index of a <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> in the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Tabs">BackstageViewControl.Tabs</see> collection.</para>
      </summary>
      <param name="tabItem">A <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> whose index is to be obtained.</param>
      <returns>An integer value that specifies the index of a <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> in the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Tabs">BackstageViewControl.Tabs</see> collection.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.HasItems">
      <summary>
        <para>Returns whether this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> currently contains any items.</para>
      </summary>
      <value>true, if this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> currently contains any items; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.HasItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.HasItems">BackstageViewControl.HasItems</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.IsBackgroundGlyphVisible">
      <summary>
        <para>Gets whether the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s background image is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s background image is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.IsBackgroundGlyphVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.IsBackgroundGlyphVisible">BackstageViewControl.IsBackgroundGlyphVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.IsFullScreen">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> occupies the entire client area of the application window. This is a dependency property.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> occupies the entire client area of the application window; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.IsFullScreenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.IsFullScreen">BackstageViewControl.IsFullScreen</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.IsOpen">
      <summary>
        <para>Gets or sets whether the specified <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> is currently opened.</para>
        <para>This is dependency property.</para>
      </summary>
      <value>true if the specified <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> is currently opened; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.IsOpenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.IsOpen">BackstageViewControl.IsOpen</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerGenerator">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerStyle">
      <summary>
        <para>Gets or sets the style that should be applied to the container element generated for each <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> item.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> that should be applied to the container element generated for each <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> item.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerStyle">BackstageViewControl.ItemContainerStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerStyleSelector">
      <summary>
        <para>Gets or sets the style selector for this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>. A style selector utilizes custom logic to select a style that should be applied to the container element generated for each <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> item.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> object associated with this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemContainerStyleSelector">BackstageViewControl.ItemContainerStyleSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemMergeType">
      <summary>
        <para>Gets or sets the merge type common to all items owned by this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemMergeType"/> enumerator value that is the merge type for all items owned by this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemMergeTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemMergeType">BackstageViewControl.ItemMergeType</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Items">
      <summary>
        <para>Provides access to <see cref="T:DevExpress.Xpf.Ribbon.BackstageButtonItem"/>, <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/> and <see cref="T:DevExpress.Xpf.Ribbon.BackstageSeparatorItem"/> objects owned by this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsPanel">
      <summary>
        <para>Provides access to this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s items panel.</para>
      </summary>
      <value>An ItemsPanelTemplate object that represents the items panel of this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsPanelProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsPanel">BackstageViewControl.ItemsPanel</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsPresenterStyle">
      <summary>
        <para>Gets or sets a style applied to the items presenter. This is a dependency property.</para>
      </summary>
      <value>A Style object applied to the items presenter.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsPresenterStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsPresenterStyle">BackstageViewControl.ItemsPresenterStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Items">BackstageViewControl.Items</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsSource">
      <summary>
        <para>Gets or sets the source of backstage items with which this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> should be populated.</para>
      </summary>
      <value>A <see cref="T:System.Collections.IEnumerable"/> object that stores backstage items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemsSource">BackstageViewControl.ItemsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemTemplate">
      <summary>
        <para>Gets or sets the template that will be applied to all backstage items owned by this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.DataTemplate"/> object that is the template that will be applied to all backstage items owned by this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemTemplate">BackstageViewControl.ItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that uses custom logic to select a required template that should be applied to child items of the current <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.DataTemplateSelector"/> object that implements custom logic to select a required template that should be applied to child items of the current <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.ItemTemplateSelector">BackstageViewControl.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.Merge(DevExpress.Xpf.Ribbon.BackstageViewControl)">
      <summary>
        <para>Merges items from the target <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> into this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
      <param name="second">A  whose items will be merged into this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.MergedParent">
      <summary>
        <para>Gets a parent <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> to which items of the current <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> have been merged.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> with which this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> has been merged.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.MergeOrder">
      <summary>
        <para>Gets or sets the order in which items that belong to this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> will be arranged within the merged <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>. This is a dependency property.</para>
      </summary>
      <value>An Int32 value that specifies the order in which items that belong to this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> will be arranged within the merged <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.MergeOrderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.MergeOrder">BackstageViewControl.MergeOrder</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Placement">
      <summary>
        <para>Gets or sets a position where the backstage view is displayed: in an AdornerLayer or in a Window. This is a dependency property.</para>
      </summary>
      <value>Any of the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewPlacement"/> enumeration values.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.PlacementProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Placement">BackstageViewControl.Placement</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Ribbon">
      <summary>
        <para>Gets the item’s parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> object that specifies the item’s parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.RibbonStyle">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s paint style. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonStyle"/> enumeration value that specifies the paint style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.RibbonStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.RibbonStyle">BackstageViewControl.RibbonStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTab">
      <summary>
        <para>Gets or sets the currently selected tab. This is a dependency property.</para>
      </summary>
      <value>The currently selected <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/>.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTabChanged">
      <summary>
        <para>Allows you to respond to tab item selection.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTabIndex">
      <summary>
        <para>Gets or sets the index of the currently selected tab item among all tab items. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value that specifies the index of the currently selected tab item within the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Tabs">BackstageViewControl.Tabs</see> list.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTabIndexProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTabIndex">BackstageViewControl.SelectedTabIndex</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTabProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.SelectedTab">BackstageViewControl.SelectedTab</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.TabCount">
      <summary>
        <para>Gets the number of <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/>s within a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
      <value>A <see cref="T:System.Int32"/> value, indicating the number of <see cref="T:DevExpress.Xpf.Ribbon.BackstageTabItem"/>s within a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/></value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.TabPaneMinWidth">
      <summary>
        <para>Gets or sets the minimum width of the area where item captions are displayed. This is a dependency property.</para>
      </summary>
      <value>A double value that specifies the minimum width of the area where item captions are displayed.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.TabPaneMinWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.TabPaneMinWidth">BackstageViewControl.TabPaneMinWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.TabPaneStyle">
      <summary>
        <para>Gets or sets a style applied to the area where item captions are displayed. This is a dependency property.</para>
      </summary>
      <value>A Style object, applied to the area where item captions are displayed.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewControl.TabPaneStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BackstageViewControl.TabPaneStyle">BackstageViewControl.TabPaneStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BackstageViewControl.Tabs">
      <summary>
        <para>Gets a list of tab items.</para>
      </summary>
      <value>A List object, containing all tab items in the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BackstageViewControl.Unmerge(DevExpress.Xpf.Ribbon.BackstageViewControl)">
      <summary>
        <para>Unmerges items of the target <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> from this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</para>
      </summary>
      <param name="second">A  whose items will be removed from this <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>.</param>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BackstageViewPlacement">
      <summary>
        <para>Lists values that specify where the backstage view is rendered.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewPlacement.Adorner">
      <summary>
        <para>The <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> is placed in an AdornerLayer.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BackstageViewPlacement.Window">
      <summary>
        <para>The <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> is placed in a borderless window.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BarButtonGroup">
      <summary>
        <para>Represents a group of items.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BarButtonGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroup"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemLinks">
      <summary>
        <para>Provides access to links that are owned by the current group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that contains links owned by the container item.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.Items">
      <summary>
        <para>Provides access to <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items and bar item links</see> displayed in this <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroup"/>.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection collection that contains bar items and bar item links displayed in this <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroup"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize bar item links for the current <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroup"/> container.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as bar item links.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemsSource">BarButtonGroup.ItemsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplate">BarButtonGroup.ItemTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemStyle">BarButtonGroup.ItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemsSource">BarButtonGroup.ItemsSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemsSource">BarButtonGroup.ItemsSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplate">BarButtonGroup.ItemTemplate</see> attached property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemsSource">BarButtonGroup.ItemsSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.BarButtonGroup.ItemTemplateSelector">BarButtonGroup.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.BarButtonGroupLink">
      <summary>
        <para>Represents a link to the <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroup"/> item.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.BarButtonGroupLink.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroupLink"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.BarButtonGroupLink.ButtonGroup">
      <summary>
        <para>Gets the button group to which the current link corresponds.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroup"/> object that represents the button group to which the current link corresponds</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.DropDownGalleryEventArgs">
      <summary>
        <para>Provides data for the <see cref="E:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryInit">RibbonGalleryBarItem.DropDownGalleryInit</see> and<see cref="E:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryClosed">RibbonGalleryBarItem.DropDownGalleryClosed</see> events.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DropDownGalleryEventArgs.#ctor(DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.DropDownGalleryEventArgs"/> class with the specified settings.</para>
      </summary>
      <param name="dropDownGallery">A GalleryDropDownPopupMenu object.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.DropDownGalleryEventArgs.DropDownGallery">
      <summary>
        <para>Gets or sets the dropdown window displaying a gallery.</para>
      </summary>
      <value>A GalleryDropDownPopupMenu object that represents the dropdown window displaying a gallery.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.DropDownGalleryEventHandler">
      <summary>
        <para>Represents the method that will handle events relating to dropdown galleries.</para>
      </summary>
      <param name="sender">An object that fires the event.</param>
      <param name="e">A <see cref="T:DevExpress.Xpf.Ribbon.DropDownGalleryEventArgs"/> object that provides the event’s data.</param>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.DXRibbonWindow">
      <summary>
        <para>A window that is designed to be used together with a <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>, to implement the Ribbon UI in your application.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DXRibbonWindow.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.ActualRibbon">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> integrated into the current <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/>. This is a dependency property.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> integrated into the current <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.ActualRibbonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.ActualRibbon">DXRibbonWindow.ActualRibbon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.AeroContentMarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.AeroContentMargin">DXRibbonWindow.AeroContentMargin</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.ContentPresenterMarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.ContentPresenterMargin">DXRibbonWindow.ContentPresenterMargin</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.DisplayShowModeSelector">
      <summary>
        <para>Gets or sets whether the button that allows an end-user to select the ribbon show mode is displayed in the upper-right corner of the <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/>. This is a dependency property.</para>
      </summary>
      <value>true, if the button is displayed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.DisplayShowModeSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.DisplayShowModeSelector">DXRibbonWindow.DisplayShowModeSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DXRibbonWindow.DragOrMaximizeWindow(System.Windows.Input.MouseButtonEventArgs)">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DXRibbonWindow.GetAeroContentMargin(System.Windows.DependencyObject)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DXRibbonWindow.GetContentPresenterMargin(System.Windows.DependencyObject)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DXRibbonWindow.GetRibbonHeaderBorderHeight(System.Windows.DependencyObject)">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.IsCaptionVisible">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.IsCaptionVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.IsCaptionVisible">DXRibbonWindow.IsCaptionVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.IsRibbonCaptionVisible">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.IsRibbonCaptionVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.IsRibbonCaptionVisible">DXRibbonWindow.IsRibbonCaptionVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.DXRibbonWindow.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.RibbonAutoHideMode">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> integrated into the current <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/> is automatically hidden on an outside click.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> is automatically hidden on an outside click; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.RibbonAutoHideModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.RibbonAutoHideMode">DXRibbonWindow.RibbonAutoHideMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.DXRibbonWindow.RibbonHeaderBorderHeightProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.DXRibbonWindow.RibbonHeaderBorderHeight">DXRibbonWindow.RibbonHeaderBorderHeight</see> dependency property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu">
      <summary>
        <para>A popup menu capable of displaying a gallery.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu"/> class.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.#ctor(DevExpress.Xpf.Bars.BarItemLinkCollection,DevExpress.Xpf.Ribbon.RibbonGalleryBarItemLinkControl)">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu"/> class with the specified settings.</para>
      </summary>
      <param name="itemLinks">A BarItemLinkCollection object that stores bar item links used within the newly created <see cref="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu"/>.</param>
      <param name="ownerLinkControl">A RibbonGalleryBarItemLinkControl object that owns the newly created <see cref="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu"/>.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.Gallery">
      <summary>
        <para>Gets or sets the gallery displayed by the <see cref="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu"/>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.Gallery"/> object displayed by the current <see cref="T:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.GalleryProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.Gallery">GalleryDropDownPopupMenu.Gallery</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.InitialVisibleColCount">
      <summary>
        <para>Gets the number of initially visible columns in the gallery.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An integer value that specifies the number of initially visible columns in the gallery.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.InitialVisibleColCountProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.InitialVisibleColCount">GalleryDropDownPopupMenu.InitialVisibleColCount</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.InitialVisibleRowCount">
      <summary>
        <para>Gets the number of initially visible rows in the gallery.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An integer value that specifies the number of initially visible rows in the gallery.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.InitialVisibleRowCountProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.GalleryDropDownPopupMenu.InitialVisibleRowCount">GalleryDropDownPopupMenu.InitialVisibleRowCount</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.MinimizationButtonPosition">
      <summary>
        <para>Provides members that specify Ribbon Minimize button’s position.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.MinimizationButtonPosition.PageContent">
      <summary>
        <para>The Minimize button is located in the Page Content section.</para>
        <para />
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.MinimizationButtonPosition.PageHeader">
      <summary>
        <para>The Minimize button is located in the Page Header section.</para>
        <para />
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonControl">
      <summary>
        <para>The <see href="https://docs.devexpress.com/WPF/7954/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-control">Ribbon Control</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonLargeIcon">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonLargeIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonLargeIcon">RibbonControl.ActualApplicationButtonLargeIcon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonSmallIcon">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonSmallIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonSmallIcon">RibbonControl.ActualApplicationButtonSmallIcon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonStyle">
      <summary>
        <para>Gets the actual style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see></para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonStyle">RibbonControl.ActualApplicationButtonStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonWidth">
      <summary>
        <para>Gets the actual width of the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the actual width of the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationButtonWidth">RibbonControl.ActualApplicationButtonWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationIcon">
      <summary>
        <para>Gets or sets the actual application icon of the <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/> in which the RibbonControl is displayed.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that specifies the actual application icon.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualApplicationIcon">RibbonControl.ActualApplicationIcon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualCategories">
      <summary>
        <para>Returns categories whose pages are currently visible.</para>
      </summary>
      <value>A collection of <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/> objects.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualCategoriesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualCategories">RibbonControl.ActualCategories</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualHeaderBorderTemplate">
      <summary>
        <para>Gets or sets the actual template applied to the RibbonControl’s header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A ControlTemplate object that specifies the actual template applied to the RibbonControl’s header.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualHeaderBorderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualHeaderBorderTemplate">RibbonControl.ActualHeaderBorderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualHeaderQuickAccessToolbarContainerStyle">
      <summary>
        <para>Gets the style of the container that displays the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object applied to the container that displays the Ribbon Quick Access Toolbar.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualHeaderQuickAccessToolbarContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualHeaderQuickAccessToolbarContainerStyle">RibbonControl.ActualHeaderQuickAccessToolbarContainerStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualIsApplicationButtonTextVisible">
      <summary>
        <para>Gets whether a non-empty string is assigned to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonText">RibbonControl.ApplicationButtonText</see> property.</para>
      </summary>
      <value>true if a non-empty string is assigned to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonText">RibbonControl.ApplicationButtonText</see> property; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualIsApplicationButtonTextVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualIsApplicationButtonTextVisible">RibbonControl.ActualIsApplicationButtonTextVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualMergedParent">
      <summary>
        <para>Gets an actual parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> with which the current child <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualWindowTitle">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ActualWindowTitleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ActualWindowTitle">RibbonControl.ActualWindowTitle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AeroBorderRibbonStyle">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s border style within the Aero Window.</para>
      </summary>
      <value>The RibbonStyle enumerator value that specifies the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s border style within the Aero Window.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AeroBorderRibbonStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AeroBorderRibbonStyle">RibbonControl.AeroBorderRibbonStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AeroBorderTopOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AeroBorderTopOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AeroBorderTopOffset">RibbonControl.AeroBorderTopOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AeroTemplate">
      <summary>
        <para>Gets or sets a template applied to the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> when hosted within an Aero window.</para>
      </summary>
      <value>A ControlTemplate object applied to the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> when hosted within an Aero window.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AeroTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AeroTemplate">RibbonControl.AeroTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowAddingToToolbarProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowAddingToToolbar">RibbonControl.AllowAddingToToolbar</see> attached property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowCustomization">
      <summary>
        <para>Gets or sets whether ribbon <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">customization</see> is supported at runtime. This is a dependency property.</para>
      </summary>
      <value>true if ribbon customization is supported at runtime; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowCustomizationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowCustomization">RibbonControl.AllowCustomization</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowCustomizingDefaultGroups">
      <summary>
        <para>Gets or sets if default groups can be customized at runtime. This is a dependency property.</para>
      </summary>
      <value>true if default groups can be customized at runtime; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowCustomizingDefaultGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowCustomizingDefaultGroups">RibbonControl.AllowCustomizingDefaultGroups</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowKeyTips">
      <summary>
        <para>Gets or sets whether Key Tips are enabled. This is a dependency property.</para>
      </summary>
      <value>true if Key Tips are enabled; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowKeyTipsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowKeyTips">RibbonControl.AllowKeyTips</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowMinimizeRibbon">
      <summary>
        <para>Gets or sets whether the Ribbon Control’s context menu displays the Minimize the Ribbon command, allowing an end-user to <see href="https://docs.devexpress.com/WPF/8356/controls-and-libraries/ribbon-bars-and-menu/ribbon/features/minimizing-ribbon">minimize the Ribbon Control</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the RibbonControl can be minimized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowMinimizeRibbonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowMinimizeRibbon">RibbonControl.AllowMinimizeRibbon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowRibbonNavigationFromEditorOnTabPress">
      <summary>
        <para>Gets or sets whether the end-users are allowed to navigate away from the <see cref="T:DevExpress.Xpf.Bars.BarEditItem"/>s by pressing the TAB key at runtime.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true, if the end-users are allowed to navigate away from the <see cref="T:DevExpress.Xpf.Bars.BarEditItem"/> by pressing the TAB key at runtime; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowRibbonNavigationFromEditorOnTabPressProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowRibbonNavigationFromEditorOnTabPress">RibbonControl.AllowRibbonNavigationFromEditorOnTabPress</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowSimplifiedRibbon">
      <summary>
        <para>Gets or sets whether the Ribbon Control’s <see href="https://docs.devexpress.com/WPF/7954/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-control#simplified-mode">Simplified mode</see> is allowed.</para>
      </summary>
      <value>true, if the Ribbon Control’s Simplified mode is allowed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AllowSimplifiedRibbonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowSimplifiedRibbon">RibbonControl.AllowSimplifiedRibbon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007Style">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office 2007 style.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInAeroWindow">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInAeroWindow">RibbonControl.ApplicationButton2007StyleInAeroWindow</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInAeroWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInAeroWindow">RibbonControl.ApplicationButton2007StyleInAeroWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInPopup">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office 2007 style, when the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu">Application Menu</see> is opened, displaying the button in the menu.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInPopupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleInPopup">RibbonControl.ApplicationButton2007StyleInPopup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007StyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2007Style">RibbonControl.ApplicationButton2007Style</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010PopupHorizontalOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010PopupHorizontalOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010PopupHorizontalOffset">RibbonControl.ApplicationButton2010PopupHorizontalOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010PopupVerticalOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010PopupVerticalOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010PopupVerticalOffset">RibbonControl.ApplicationButton2010PopupVerticalOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010Style">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office 2010 style.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010StyleInPopup">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office 2010 style, when the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu">Application Menu</see> is opened, displaying the button in the menu.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010StyleInPopupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010StyleInPopup">RibbonControl.ApplicationButton2010StyleInPopup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010StyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButton2010Style">RibbonControl.ApplicationButton2010Style</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonLargeIcon">
      <summary>
        <para>Gets or sets the image displayed within the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office2007 paint style.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An ImageSource value that specifies the image displayed within the Application Button in the Office2007 paint style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonLargeIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonLargeIcon">RibbonControl.ApplicationButtonLargeIcon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonOfficeSlimStyle">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office Slim style. This is a dependency property.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonOfficeSlimStyleInPopup">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office Slim style when the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu">Application Menu</see> is opened, displaying the button in the menu.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonOfficeSlimStyleInPopupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonOfficeSlimStyleInPopup">RibbonControl.ApplicationButtonOfficeSlimStyleInPopup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonOfficeSlimStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonOfficeSlimStyle">RibbonControl.ApplicationButtonOfficeSlimStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonPopupHorizontalOffset">
      <summary>
        <para>Gets or sets the horizontal offset of the control assigned to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenu">RibbonControl.ApplicationMenu</see> property, relative to the top left corner of the RibbonControl.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the popup control’s horizontal offset.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonPopupHorizontalOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonPopupHorizontalOffset">RibbonControl.ApplicationButtonPopupHorizontalOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonPopupVerticalOffset">
      <summary>
        <para>Gets or sets the vertical offset of the control assigned to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenu">RibbonControl.ApplicationMenu</see> property, relative to the top left corner of the RibbonControl.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the popup control’s vertical offset.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonPopupVerticalOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonPopupVerticalOffset">RibbonControl.ApplicationButtonPopupVerticalOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonSmallIcon">
      <summary>
        <para>Gets or sets the image displayed within the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office2010 and OfficeSlim paint styles.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An ImageSource value that specifies the image displayed within the Application Button in the Office2010 paint style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonSmallIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonSmallIcon">RibbonControl.ApplicationButtonSmallIcon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTabletOfficeStyle">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Tablet Office style. This is a dependency property.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTabletOfficeStyleInPopup">
      <summary>
        <para>Gets or sets the style applied to the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Tablet Office style when the <see cref="T:DevExpress.Xpf.Ribbon.ApplicationMenu">Application Menu</see> is opened, displaying the button in the menu.</para>
      </summary>
      <value>A Style object that represents the corresponding style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTabletOfficeStyleInPopupProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTabletOfficeStyleInPopup">RibbonControl.ApplicationButtonTabletOfficeStyleInPopup</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTabletOfficeStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTabletOfficeStyle">RibbonControl.ApplicationButtonTabletOfficeStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonText">
      <summary>
        <para>Gets or sets the text displayed within the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> in the Office2010 and OfficeSlim paint styles.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies text displayed within the Application Button in the Office2010  and OfficeSlim paint styles.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonTextProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationButtonText">RibbonControl.ApplicationButtonText</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenu">
      <summary>
        <para>Gets or sets the menu or control that is invoked when clicking on the RibbonControl’s <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see>. This is a dependency property.</para>
      </summary>
      <value>An object that is invoked when clicking on the RibbonControl’s <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see>.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuChanged">
      <summary>
        <para>Fires when the value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenu">RibbonControl.ApplicationMenu</see> property changes.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuChangedEvent">
      <summary>
        <para>Identifies the <see cref="E:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuChanged">RibbonControl.ApplicationMenuChanged</see> routed event.</para>
      </summary>
      <value>A routed event identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuHorizontalOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuHorizontalOffsetInAeroWindow">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuHorizontalOffsetInAeroWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuHorizontalOffsetInAeroWindow">RibbonControl.ApplicationMenuHorizontalOffsetInAeroWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuHorizontalOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuHorizontalOffset">RibbonControl.ApplicationMenuHorizontalOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenu">RibbonControl.ApplicationMenu</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuVerticalOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuVerticalOffsetInAeroWindow">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuVerticalOffsetInAeroWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuVerticalOffsetInAeroWindow">RibbonControl.ApplicationMenuVerticalOffsetInAeroWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuVerticalOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenuVerticalOffset">RibbonControl.ApplicationMenuVerticalOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AsyncMergingEnabled">
      <summary>
        <para>Gets or sets whether <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>s asynchronous merging is enabled. This is a dependency property.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s asynchronous merging is enabled; otherwise, false. The default is true.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AsyncMergingEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AsyncMergingEnabled">RibbonControl.AsyncMergingEnabled</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.AutoHideMode">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>, when integrated into the <see cref="T:DevExpress.Xpf.Ribbon.DXRibbonWindow"/>, is automatically hidden on an outside click. This is a dependency property.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> is automatically hidden on an outside click; otherwise, false. The default is false.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.AutoHideModeChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AutoHideMode">RibbonControl.AutoHideMode</see> property is changed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.AutoHideModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AutoHideMode">RibbonControl.AutoHideMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.BackstageClosed">
      <summary>
        <para>Allows you to respond to a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/>‘s closure.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.BackstageOpened">
      <summary>
        <para>Allows you to respond to a <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> being invoked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ButtonGroupsIndent">
      <summary>
        <para>Gets or sets the indent between <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroupLink"/>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The indent between button groups, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ButtonGroupsIndentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ButtonGroupsIndent">RibbonControl.ButtonGroupsIndent</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ButtonGroupVertAlign">
      <summary>
        <para>Gets or sets the vertical alignment of <see cref="T:DevExpress.Xpf.Ribbon.BarButtonGroupLink"/> objects within <see href="https://docs.devexpress.com/WPF/7956/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-group">Ribbon Page Groups</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A VerticalAlignment value that specifies the vertical alignment of button groups within Ribbon page groups.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ButtonGroupVertAlignProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ButtonGroupVertAlign">RibbonControl.ButtonGroupVertAlign</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.Categories">
      <summary>
        <para>A collection of categories used within the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>A DevExpress.Xpf.Ribbon.RibbonPageCategoryCollection object that contains categories used within the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Categories">RibbonControl.Categories</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize categories for the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The source of objects to be visualized as ribbon categories.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesSource">RibbonControl.CategoriesSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> or <see cref="T:DevExpress.Xpf.Ribbon.RibbonDefaultPageCategory"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplate">RibbonControl.CategoryTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CategoryStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryStyle">RibbonControl.CategoryStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesSource">RibbonControl.CategoriesSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CategoryStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesSource">RibbonControl.CategoriesSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplate">RibbonControl.CategoryTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoriesSource">RibbonControl.CategoriesSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CategoryTemplateSelector">RibbonControl.CategoryTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.CloseApplicationMenu">
      <summary>
        <para>Closes the currently opened control assigned to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ApplicationMenu">RibbonControl.ApplicationMenu</see> property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CollapsedRibbonAeroBorderTopOffset">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CollapsedRibbonAeroBorderTopOffsetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CollapsedRibbonAeroBorderTopOffset">RibbonControl.CollapsedRibbonAeroBorderTopOffset</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.CollapseMinimizedRibbon">
      <summary>
        <para>Collapses the expanded ribbon, when it’s in the minimized state.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ColumnIndent">
      <summary>
        <para>Gets or sets the horizontal indent between <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar item links</see> within the Ribbon Control.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The horizontal indent between <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar item links</see> within the Ribbon Control, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ColumnIndentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ColumnIndent">RibbonControl.ColumnIndent</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.Controllers">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CornerRadius">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl">RibbonControl</see>‘s corner radius. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.CornerRadius"/> value that specifies the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl">RibbonControl</see>‘s corner radius.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.CornerRadiusProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.CornerRadius">RibbonControl.CornerRadius</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.CreateToolTipControl(System.String)">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.CustomizationHelper">
      <summary>
        <para>Provides access to the object that provides methods to work with the Customization Window and customization menus.</para>
      </summary>
      <value>An instance of the RibbonCustomizationHelper class object.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.DefaultTemplate">
      <summary>
        <para>Gets or sets the template applied to the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> in non-Aero mode.</para>
      </summary>
      <value>A ControlTemplate object applied to the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> in non-Aero mode.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.DefaultTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.DefaultTemplate">RibbonControl.DefaultTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.ExpandMinimizedRibbon">
      <summary>
        <para>Shows the contents of the currently selected page, when the RibbonControl is in the minimized state.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.GetAllowAddingToToolbar(System.Windows.DependencyObject)">
      <summary>
        <para>Returns the current effective value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowAddingToToolbar">RibbonControl.AllowAddingToToolbar</see> dependency property on the specified DependencyObject.</para>
      </summary>
      <param name="obj">A DependencyObject whose dependency property value should be returned.</param>
      <returns>The current effective value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowAddingToToolbar">RibbonControl.AllowAddingToToolbar</see> dependency property.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.GetFirstSelectablePage">
      <summary>
        <para>Returns the first selectable <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> contained within the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object that is the first selectable page within the parent ribbon control.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.GetMostAppropriateScreenRect">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.GetRibbon(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> attached property for the specified object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> property’s value is to be returned.</param>
      <returns>The value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> attached property for the specified object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.GroupCaptionTextStyle">
      <summary>
        <para>Gets or sets the style applied to captions (<see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Caption">RibbonPageGroup.Caption</see>) of Ribbon groups.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to Ribbon groups’ captions.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.GroupCaptionTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.GroupCaptionTextStyle">RibbonControl.GroupCaptionTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderBorderTemplateInRibbonWindow">
      <summary>
        <para>Gets or sets the template applied to the RibbonControl’s header, when the RibbonControl is used within a <see href="https://docs.devexpress.com/WPF/7980/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/dxribbonwindow">DXRibbonWindow</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A ControlTemplate object that specifies the template applied to the RibbonControl’s header, when the RibbonControl is used within a <see href="https://docs.devexpress.com/WPF/7980/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/dxribbonwindow">DXRibbonWindow</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderBorderTemplateInRibbonWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderBorderTemplateInRibbonWindow">RibbonControl.HeaderBorderTemplateInRibbonWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyle">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInAeroWindow">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInAeroWindow">RibbonControl.HeaderQAT2007ContainerStyleInAeroWindow</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInAeroWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInAeroWindow">RibbonControl.HeaderQAT2007ContainerStyleInAeroWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInRibbonWindow">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInRibbonWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleInRibbonWindow">RibbonControl.HeaderQAT2007ContainerStyleInRibbonWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2007ContainerStyle">RibbonControl.HeaderQAT2007ContainerStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyle">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInAeroWindow">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInAeroWindow">RibbonControl.HeaderQAT2010ContainerStyleInAeroWindow</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInAeroWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInAeroWindow">RibbonControl.HeaderQAT2010ContainerStyleInAeroWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInRibbonWindow">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInRibbonWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleInRibbonWindow">RibbonControl.HeaderQAT2010ContainerStyleInRibbonWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQAT2010ContainerStyle">RibbonControl.HeaderQAT2010ContainerStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutAppIconInAeroWindow">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutAppIconInAeroWindow">RibbonControl.HeaderQATContainerStyleWithoutAppIconInAeroWindow</see> dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutAppIconInAeroWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutAppIconInAeroWindow">RibbonControl.HeaderQATContainerStyleWithoutAppIconInAeroWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutApplicationIcon">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutApplicationIconProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HeaderQATContainerStyleWithoutApplicationIcon">RibbonControl.HeaderQATContainerStyleWithoutApplicationIcon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HideEmptyGroups">
      <summary>
        <para>Gets or sets whether any <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> within this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> should be hidden, if does not contain any items.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true, if any <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> within this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> should be hidden, if does not contain any items; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HideEmptyGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HideEmptyGroups">RibbonControl.HideEmptyGroups</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.HoverPageCaptionTextStyle">
      <summary>
        <para>Gets or sets a style used to paint a hovered <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>‘s caption.</para>
      </summary>
      <value>A Style object used to paint a hovered <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>‘s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.HoverPageCaptionTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.HoverPageCaptionTextStyle">RibbonControl.HoverPageCaptionTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsAeroMode">
      <summary>
        <para>Gets whether this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> is currently in Aero mode.</para>
      </summary>
      <value>true, if this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> is currently in Aero mode; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsAeroModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsAeroMode">RibbonControl.IsAeroMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsApplicationIconVisible">
      <summary>
        <para>Gets whether the application icon is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the application icon is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsApplicationIconVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsApplicationIconVisible">RibbonControl.IsApplicationIconVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsBackStageViewOpen">
      <summary>
        <para>Gets if the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> is opened.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Ribbon.BackstageViewControl"/> is opened; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsBackStageViewOpenProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsBackStageViewOpen">RibbonControl.IsBackStageViewOpen</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsChild">
      <summary>
        <para>Gets if the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged with any other <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged with any other <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsContextualCategoryExist">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsHeaderBorderVisible">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsHeaderBorderVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsHeaderBorderVisible">RibbonControl.IsHeaderBorderVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsHiddenRibbonCollapsed">
      <summary>
        <para>Gets or sets whether the ribbon displayed in the auto-hide mode is actually hidden. This is a dependency property.</para>
      </summary>
      <value>true, if the ribbon displayed in the auto-hide mode is actually hidden; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsHiddenRibbonCollapsedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsHiddenRibbonCollapsed">RibbonControl.IsHiddenRibbonCollapsed</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsInRibbonWindow">
      <summary>
        <para>Gets whether the RibbonControl is displayed within a <see href="https://docs.devexpress.com/WPF/7980/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/dxribbonwindow">DXRibbonWindow</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the RibbonControl is displayed within a DXRibbonWindow; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsInRibbonWindowProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsInRibbonWindow">RibbonControl.IsInRibbonWindow</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMerged">
      <summary>
        <para>Gets if any <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>true if any <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>; otherwise, false.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.IsMergedChild(DevExpress.Xpf.Ribbon.RibbonControl)">
      <summary>
        <para>Returns if the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged with the current one.</para>
      </summary>
      <param name="item">A child <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> object to be tested.</param>
      <returns>true if the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> has been merged with the current one; otherwise, false.</returns>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsMergedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMerged">RibbonControl.IsMerged</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizationButtonVisible">
      <summary>
        <para>Gets the actual visibility state of the Ribbon Control’s Minimize button.</para>
      </summary>
      <value>true if the Minimize button is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizationButtonVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizationButtonVisible">RibbonControl.IsMinimizationButtonVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimized">
      <summary>
        <para>Gets or sets whether the Ribbon Control is <see href="https://docs.devexpress.com/WPF/8356/controls-and-libraries/ribbon-bars-and-menu/ribbon/features/minimizing-ribbon">minimized</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true, if the Ribbon Control is minimized; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimized">RibbonControl.IsMinimized</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizedRibbonCollapsed">
      <summary>
        <para>Gets whether the minimized Ribbon is collapsed.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the minimized Ribbon is collapsed; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizedRibbonCollapsedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsMinimizedRibbonCollapsed">RibbonControl.IsMinimizedRibbonCollapsed</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsOffice2010Style">
      <summary>
        <para>Gets whether the RibbonControl is painted in the Office 2010 style.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the RibbonControl is painted in the Office 2010 style; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsOffice2010StyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsOffice2010Style">RibbonControl.IsOffice2010Style</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsRibbonTitleBarActualVisible">
      <summary>
        <para>Gets whether the ribbon title bar is visible.</para>
      </summary>
      <value>true if the ribbon title bar is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsRibbonTitleBarActualVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsRibbonTitleBarActualVisible">RibbonControl.IsRibbonTitleBarActualVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.IsSimplified">
      <summary>
        <para>Gets or sets whether the Ribbon Control’s layout is currently in the <see href="https://docs.devexpress.com/WPF/7954/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-control#simplified-mode">Simplified mode</see>.</para>
      </summary>
      <value>true, if the Ribbon Control’s layout is currently in the <see href="https://docs.devexpress.com/WPF/7954/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-control#simplified-mode">Simplified mode</see>; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.IsSimplifiedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.IsSimplified">RibbonControl.IsSimplified</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemLinksSourceElementGeneratesUniqueBarItem">
      <summary>
        <para>Gets or sets whether each reference to a data object in an ItemLinksSource for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> should generate a unique <see cref="T:DevExpress.Xpf.Bars.BarItem"/>, whether this data object was previously referenced. This is a dependency property.</para>
      </summary>
      <value>true if each reference to a data object in an ItemLinksSource for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> should generate a unique <see cref="T:DevExpress.Xpf.Bars.BarItem"/>; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemLinksSourceElementGeneratesUniqueBarItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemLinksSourceElementGeneratesUniqueBarItem">RibbonControl.ItemLinksSourceElementGeneratesUniqueBarItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.Items">
      <summary>
        <para>Provides access to <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">categories and pages</see> displayed in this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>A DevExpress.Xpf.Ribbon.Internal.RibbonItemCollection collection that stores categories and pages displayed in this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Items">RibbonControl.Items</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize categories and pages for the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>. This is a dependency property.</para>
      </summary>
      <value>The source of objects to be visualized as ribbon categories (or pages).</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemsSource">RibbonControl.ItemsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplate">RibbonControl.ItemTemplate</see>‘s content. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemStyle">RibbonControl.ItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemsSource">RibbonControl.ItemsSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemsSource">RibbonControl.ItemsSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplate">RibbonControl.ItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemsSource">RibbonControl.ItemsSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ItemTemplateSelector">RibbonControl.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.KeyTipApplicationButton">
      <summary>
        <para>Gets or sets the Key Tip used to invoke the Application Button’s functionality.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies the Key Tip used to invoke the Application Button’s functionality.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.KeyTipApplicationButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.KeyTipApplicationButton">RibbonControl.KeyTipApplicationButton</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.LoadPagesInBackground">
      <summary>
        <para>Gets or sets whether <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>s within the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> should load their content in background.</para>
      </summary>
      <value>true, if <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>s within the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> should load their content in background; otherwise, false. The default is true.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.Manager">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Bars.BarManager"/> that manages <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items</see> displayed by the RibbonControl.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarManager"/> object.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.MaxPageCaptionTextIndent">
      <summary>
        <para>Gets or sets the maximum distance between a ribbon page’s caption and vertical tab header borders.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Double value that specifies the maximum distance between a ribbon page’s caption and vertical tab header borders.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.MaxPageCaptionTextIndentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.MaxPageCaptionTextIndent">RibbonControl.MaxPageCaptionTextIndent</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.MDIMergeStyle">
      <summary>
        <para>Gets or sets if the RibbonControl can be merged.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.MDIMergeStyle"/> value specifying if the RibbonControl can be merged.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.MDIMergeStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.MDIMergeStyle">RibbonControl.MDIMergeStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.MenuIconStyle">
      <summary>
        <para>Gets or sets the style of icons in the drop-down menu that allows the ribbon show mode to be specified.</para>
      </summary>
      <value>A DevExpress.Xpf.Ribbon.RibbonMenuIconStyle enumeration value that specifies the style of icons in the drop-down menu that allows the ribbon show mode to be specified.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.MenuIconStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.MenuIconStyle">RibbonControl.MenuIconStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.Merge(DevExpress.Xpf.Ribbon.RibbonControl)">
      <summary>
        <para>Merges the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <param name="childRibbon">A <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> whose elements are to be merged with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.MergedParent">
      <summary>
        <para>Gets the parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> merged with this child <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>The parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> merged with this child <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.MinimizationButtonPosition">
      <summary>
        <para>Gets or sets the Ribbon Control’s Minimize button position.</para>
      </summary>
      <value>A RibbonMinimizationButtonPosition value that specifies the Minimize button’s position.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.MinimizationButtonPositionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.MinimizationButtonPosition">RibbonControl.MinimizationButtonPosition</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.MinimizationButtonVisibility">
      <summary>
        <para>Gets or sets the visibility of the Ribbon Control’s Minimize button.</para>
      </summary>
      <value>A RibbonMinimizationButtonVisibility value that specifies the Minimize button’s visibility.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.MinimizationButtonVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.MinimizationButtonVisibility">RibbonControl.MinimizationButtonVisibility</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.NormalPageCaptionTextStyle">
      <summary>
        <para>Gets or sets the style applied to a Ribbon page’s caption (<see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Caption">RibbonPage.Caption</see>), when the page is not selected.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to non-selected Ribbon page captions.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.NormalPageCaptionTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.NormalPageCaptionTextStyle">RibbonControl.NormalPageCaptionTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.OnActualCategoriesChanged">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageCaptionMinWidth">
      <summary>
        <para>Gets or sets the minimum allowed width of page headers.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The minimum allowed width of page headers.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageCaptionMinWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageCaptionMinWidth">RibbonControl.PageCaptionMinWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageCategoryAlignment">
      <summary>
        <para>Gets or sets the alignment of <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">custom page categories</see>, which display contextual tab pages.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryCaptionAlignment"/> value that specifies the alignment of custom page categories.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageCategoryAlignmentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageCategoryAlignment">RibbonControl.PageCategoryAlignment</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinks">
      <summary>
        <para>Gets a collection of <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar item links</see> displayed at the right edge of the Ribbon Control in the same row with tab headers.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object representing the collection of bar item links.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinksSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize bar item links for the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> container.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as page header items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinksSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinksSource">RibbonControl.PageHeaderItemLinksSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItems">
      <summary>
        <para>Provides access to the collection of <see href="https://docs.devexpress.com/WPF/8174/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon/items-and-links">bar items and bar item links</see> displayed at the right edge of the Ribbon Control, in the same row with tab headers.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection collection that stores elements displayed at the right edge of the Ribbon Control, in the same row with tab headers.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplate">RibbonControl.PageHeaderItemTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemStyle">RibbonControl.PageHeaderItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinksSource">RibbonControl.PageHeaderItemLinksSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the  <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinksSource">RibbonControl.PageHeaderItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplate">RibbonControl.PageHeaderItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemLinksSource">RibbonControl.PageHeaderItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderItemTemplateSelector">RibbonControl.PageHeaderItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageHeaderLinksControl">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.PageToCategoryBinder">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.RequestReMerge">
      <summary>
        <para>Updates the info about ribbon items and then re-merges the ribbon.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.RestoreDefaultLayout">
      <summary>
        <para>Discards all the layout customizations and restores the default <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.RestoreLayout(System.Object)">
      <summary>
        <para>Restores the previously saved layout of a <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <param name="path">A path to the layout configuration file or a Stream from which a custom layout is to be restored.</param>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.RestoreLayoutException">
      <summary>
        <para>Occurs when the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> fails to restore its layout.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.RibbonHeaderVisibility">
      <summary>
        <para>Gets or sets whether to show the Ribbon’s page headers and the region above the headers.</para>
      </summary>
      <value>A RibbonHeaderVisibility value that specifies the visibility of the Ribbon’s header region.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.RibbonHeaderVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.RibbonHeaderVisibility">RibbonControl.RibbonHeaderVisibility</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.RibbonPopupMenuClosed">
      <summary>
        <para>Fires after the Ribbon’s context menu is closed.</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.RibbonPopupMenuShowing">
      <summary>
        <para>Fires before the Ribbon’s context menu is displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.RibbonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.RibbonStyle">
      <summary>
        <para>Gets or sets the style of the RibbonControl.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonStyle"/> enumeration value that specifies the paint style.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.RibbonStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.RibbonStyle">RibbonControl.RibbonStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.RibbonTitleBarVisibility">
      <summary>
        <para>Gets or sets whether to show a bar that displays the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>.</para>
      </summary>
      <value>A RibbonTitleBarVisibility value that specifies the visibility of the bar displaying the Quick Access Toolbar.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.RibbonTitleBarVisibilityProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.RibbonTitleBarVisibility">RibbonControl.RibbonTitleBarVisibility</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.RowIndent">
      <summary>
        <para>Gets or sets the vertical indent between <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar item links</see> within the Ribbon Control.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The vertical indent between <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar item links</see> within the Ribbon Control, in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.RowIndentProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.RowIndent">RibbonControl.RowIndent</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.RuntimeCustomizations">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.SaveDefaultLayout">
      <summary>
        <para>Saves the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> layout as the default layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.SaveLayout(System.Object)">
      <summary>
        <para>Saves a custom <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s layout into an XML file or a stream.</para>
      </summary>
      <param name="path">A path to the layout configuration file or a stream to which a custom layout is to be saved.</param>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.SaveLayoutException">
      <summary>
        <para>Occurs when the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> fails to save its layout.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPage">
      <summary>
        <para>Gets or sets the RibbonControl’s selected <see href="https://docs.devexpress.com/WPF/7955/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page">page</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object that specifies the selected page.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageCaptionTextStyle">
      <summary>
        <para>Gets or sets the style applied to a Ribbon page’s caption (<see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Caption">RibbonPage.Caption</see>), when the page is selected.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to the selected Ribbon page’s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageCaptionTextStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageCaptionTextStyle">RibbonControl.SelectedPageCaptionTextStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageChanged">
      <summary>
        <para>Fires when a page is selected.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageName">
      <summary>
        <para>Specifies the selected page by its name.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies the selected page’s name.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageNameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageName">RibbonControl.SelectedPageName</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageOnMerging">
      <summary>
        <para>Gets or sets which <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s selected page will be selected after ribbon controls are merged.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.SelectedPageOnMerging"/> enumerator value indicating which <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s selected page will be selected after ribbon controls are merged.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageOnMergingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageOnMerging">RibbonControl.SelectedPageOnMerging</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPagePopupMargin">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPagePopupMarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPagePopupMargin">RibbonControl.SelectedPagePopupMargin</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SelectedPage">RibbonControl.SelectedPage</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SelfCategories">
      <summary>
        <para>Gets page categories defined in <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Items">RibbonControl.Items</see> and <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Categories">RibbonControl.Categories</see> collections. This is a dependency property.</para>
      </summary>
      <value>A DevExpress.Xpf.Ribbon.Internal.SelfCategoriesCollection that contains page categories defined in <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Items">RibbonControl.Items</see> and <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Categories">RibbonControl.Categories</see> collections.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SelfCategoriesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SelfCategories">RibbonControl.SelfCategories</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.SetAllowAddingToToolbar(System.Windows.DependencyObject,System.Boolean)">
      <summary>
        <para>Sets the local value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.AllowAddingToToolbar">RibbonControl.AllowAddingToToolbar</see> dependency property on the specified DependencyObject.</para>
      </summary>
      <param name="obj">A DependencyObject whose dependency property should be set.</param>
      <param name="value">The new local value.</param>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.SetRibbon(System.Windows.DependencyObject,DevExpress.Xpf.Ribbon.RibbonControl)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> attached property for the specified object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> attached property is to be set.</param>
      <param name="value">The new value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.Ribbon">RibbonControl.Ribbon</see> attached property.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ShowApplicationButton">
      <summary>
        <para>Gets or sets whether the <see href="https://docs.devexpress.com/WPF/115363/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-menu#application-button">Application Button</see> is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the Application Button is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ShowApplicationButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ShowApplicationButton">RibbonControl.ShowApplicationButton</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.StandaloneHeaderBorderTemplate">
      <summary>
        <para>Gets or sets the template applied to the RibbonControl’s header, when the RibbonControl is used as a standalone control.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A ControlTemplate object that specifies the template applied to the RibbonControl’s header, when the RibbonControl is used as a standalone control.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.StandaloneHeaderBorderTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.StandaloneHeaderBorderTemplate">RibbonControl.StandaloneHeaderBorderTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.SupportSidePanels">
      <summary>
        <para>Gets or sets whether the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> can be integrated into the window header when there are side panels in the layout. This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> can be integrated into the window header when side panels are in the layout; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.SupportSidePanelsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.SupportSidePanels"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.Toolbar">
      <summary>
        <para>Gets a <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see> assigned to this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
      <value>A <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see> assigned to this <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarCustomizationMenuClosed">
      <summary>
        <para>Fires when the Customization menu is closed (This menu is available when clicking the Customization button displayed next to the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>).</para>
      </summary>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarCustomizationMenuShowing">
      <summary>
        <para>Fires when a Customization menu is about to be invoked (when clicking the Customization button displayed next to the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>).</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarHideSeparators">
      <summary>
        <para>This member supports the internal infrastructure and is not intended to be used directly from your code.</para>
      </summary>
      <value>A <see cref="T:System.Boolean"/> value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarHideSeparatorsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarHideSeparators">RibbonControl.ToolbarHideSeparators</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinks">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/WPF/8174/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon/items-and-links">bar item links</see> displayed within the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that stores bar item links displayed within the Ribbon Quick Access Toolbar.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinksSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize bar item links displayed within the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as toolbar items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinksSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinksSource">RibbonControl.ToolbarItemLinksSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItems">
      <summary>
        <para>Provides access to the collection of <see href="https://docs.devexpress.com/WPF/8174/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon/items-and-links">bar items and bar item links</see> displayed in the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Quick Access Toolbar</see>.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection collection that stores elements that populate this ribbon’s <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Quick Access Toolbar</see>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplate">RibbonControl.ToolbarItemTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemStyle">RibbonControl.ToolbarItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinksSource">RibbonControl.ToolbarItemLinksSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinksSource">RibbonControl.ToolbarItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplate">RibbonControl.ToolbarItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemLinksSource">RibbonControl.ToolbarItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarItemTemplateSelector">RibbonControl.ToolbarItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarModeChanged">
      <summary>
        <para>Fires when the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowMode">RibbonControl.ToolbarShowMode</see> property is changed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowCustomizationButton">
      <summary>
        <para>Gets or sets whether a Customization button is displayed to the right of the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see>.</para>
      </summary>
      <value>true if the Customization button is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowCustomizationButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowCustomizationButton">RibbonControl.ToolbarShowCustomizationButton</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowMode">
      <summary>
        <para>Gets or sets where and whether the <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see> is displayed.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode"/> value that specifies the Toolbar’s display mode</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowModeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonControl.ToolbarShowMode">RibbonControl.ToolbarShowMode</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.UnMerge">
      <summary>
        <para>Restores the original <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s layout by removing all the elements that have been added via the <see cref="M:DevExpress.Xpf.Ribbon.RibbonControl.Merge(DevExpress.Xpf.Ribbon.RibbonControl)">RibbonControl.Merge</see> method.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonControl.UnMerge(DevExpress.Xpf.Ribbon.RibbonControl)">
      <summary>
        <para>Removes the elements that belong to the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> from the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>. This method is in effect when these two ribbon controls have been previously merged.</para>
      </summary>
      <param name="childRibbon">A <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>, whose elements are to be removed from the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonControl.UseCaptionToIdentifyPagesOnMerging">
      <summary>
        <para>Gets or sets whether the parent ribbon control should try to keep its selected page when merging with another child ribbon.</para>
      </summary>
      <value>true, if the parent ribbon control should try to keep its selected page when merging with another child ribbon; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonControl.UseOldNavigationManager">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonDefaultPageCategory">
      <summary>
        <para>Represents the Ribbon default <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">page category</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonDefaultPageCategory.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonDefaultPageCategory"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem">
      <summary>
        <para>A bar item that displays a gallery.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGallery">
      <summary>
        <para>Gets or sets the item’s dropdown gallery, which is invoked when clicking the RibbonGalleryBarItem’s dropdown button.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.Gallery"/> object that represents the dropdown gallery.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryClosed">
      <summary>
        <para>Fires when a popup window containing the dropdown gallery is closed.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryEnabled">
      <summary>
        <para>Gets or sets if a drop-down behavior is enabled for the current gallery.</para>
      </summary>
      <value>true if a drop-down behavior is enabled for the current gallery; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryEnabled">RibbonGalleryBarItem.DropDownGalleryEnabled</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryInit">
      <summary>
        <para>Allows you to customize the dropdown gallery.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryMenuCustomItemsGlyphSize">
      <summary>
        <para>Gets or sets the size of the glyph for the bar items within the current dropdown gallery. This property affects the glyph size if the bar item’s GlyphSize property is set to Custom. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Size"/> structure that is the size of the glyph.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryMenuCustomItemsGlyphSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryMenuCustomItemsGlyphSize">RibbonGalleryBarItem.DropDownGalleryMenuCustomItemsGlyphSize</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryMenuItemsGlyphSize">
      <summary>
        <para>Gets or sets the size of the images displayed for the menu items within the dropdown gallery.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.GlyphSize"/> value that specifies the size of the corresponding images.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryMenuItemsGlyphSizeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryMenuItemsGlyphSize">RibbonGalleryBarItem.DropDownGalleryMenuItemsGlyphSize</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGalleryProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownGallery">RibbonGalleryBarItem.DropDownGallery</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownMenuItemLinks">
      <summary>
        <para>Gets the bar item links displayed below the dropdown gallery.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that contains the corresponding items.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.DropDownMenuItems">
      <summary>
        <para>Provides access to a collection of <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items</see> whose links are displayed below the dropdown gallery.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection object that owns <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items</see> whose links are displayed below the dropdown gallery.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.Gallery">
      <summary>
        <para>Gets or sets the in-ribbon gallery associated with the item.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.Gallery"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.GalleryProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.Gallery">RibbonGalleryBarItem.Gallery</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.GetDefaultItemsGlyphSize(DevExpress.Xpf.Bars.LinkContainerType)">
      <summary>
        <para>Implements the <see cref="M:DevExpress.Xpf.Bars.ILinksHolder.GetDefaultItemsGlyphSize(DevExpress.Xpf.Bars.LinkContainerType)">ILinksHolder.GetDefaultItemsGlyphSize</see> method, and is not supposed to be used directly from your code.</para>
      </summary>
      <param name="linkContainerType">A LinkContainerType value that specifies the type of bar item container.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Bars.GlyphSize"/> value.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.GetLogicalChildrenEnumerator">
      <summary>
        <para>Gets an enumerator used to iterate through bar item links.</para>
      </summary>
      <returns>An IEnumerator object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.ItemsGlyphSize">
      <summary>
        <para>Gets the size of images used by bar item links owned by the current object.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.GlyphSize"/> value.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.Links">
      <summary>
        <para>Gets or sets the bar item links displayed below the dropdown gallery.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that contains the corresponding bar item links.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.Merge(DevExpress.Xpf.Bars.ILinksHolder)">
      <summary>
        <para>Adds the visible links from the specified link container onto the current link container.</para>
      </summary>
      <param name="holder">A <see cref="T:DevExpress.Xpf.Bars.ILinksHolder"/> object whose visible links are merged with the current link container.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.MergedLinks">
      <summary>
        <para>Gets the links currently displayed by the link container, including the links that have been merged with the container.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that contains the links currently displayed by the link container.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.OnLinkAdded(DevExpress.Xpf.Bars.BarItemLinkBase)">
      <summary>
        <para>This method is called before a bar item link is added to the current link holder.</para>
      </summary>
      <param name="link">A <see cref="T:DevExpress.Xpf.Bars.BarItemLink"/> object being added.</param>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.OnLinkRemoved(DevExpress.Xpf.Bars.BarItemLinkBase)">
      <summary>
        <para>This method is called before a bar item link is removed from the current link holder.</para>
      </summary>
      <param name="link">A <see cref="T:DevExpress.Xpf.Bars.BarItemLink"/> object being removed.</param>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.UnMerge">
      <summary>
        <para>Removes the links of other link containers from the current link container. This method is in effect when other link containers have been merged with the current container.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem.UnMerge(DevExpress.Xpf.Bars.ILinksHolder)">
      <summary>
        <para>Removes the links of the specified link container from the current link container. This method is in effect when the specified link container has been merged with the current container.</para>
      </summary>
      <param name="holder">A <see cref="T:DevExpress.Xpf.Bars.ILinksHolder"/> object whose links must be removed from the current link container.</param>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItemLink">
      <summary>
        <para>Represents a link to a <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem"/> object.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryBarItemLink.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItemLink"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemePaletteSelectorBehavior">
      <summary>
        <para>Populates the associated <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem"/> with the available palettes and allows you to choose the application theme’s palette.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemePaletteSelectorBehavior.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemePaletteSelectorBehavior"/> class.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemeSelectorBehavior">
      <summary>
        <para>Populates the associated <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryBarItem">RibbonGalleryBarItem</see> with available themes and allows you to choose the application’s theme.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemeSelectorBehavior.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemeSelectorBehavior"/> class.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemeSelectorBehavior.DropDownGalleryProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonGalleryItemThemeSelectorBehavior.DropDownGallery"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonMergeType">
      <summary>
        <para>Specifies merging options used by the <see cref="M:DevExpress.Xpf.Ribbon.RibbonControl.Merge(DevExpress.Xpf.Ribbon.RibbonControl)">RibbonControl.Merge</see> method.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMergeType.Add">
      <summary>
        <para>Adds a child ribbon element to a parent ribbon even if an element with the same caption already exists in the parent ribbon.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMergeType.MergeItems">
      <summary>
        <para>The default merging mechanism. Sub-items of the current child ribbon element are merged into the parent ribbon element that has the same caption. If no parent element with the same caption exists, the current child ribbon element is appended according to its MergeOrder.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMergeType.Remove">
      <summary>
        <para>Prevents the current child ribbon element from being merged with a parent ribbon object.</para>
        <para />
        <para>If the MergeType for a parent element is set to Remove, this element is hidden after the merge is complete.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMergeType.Replace">
      <summary>
        <para>Replaces a parent ribbon element with the current child ribbon element if they have the same caption.</para>
        <para>If a parent element with the same caption is not found, the current child ribbon element is added to the parent ribbon.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonMinimizationButtonVisibility">
      <summary>
        <para>Provides members that specify Ribbon’s Minimize button visibility.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMinimizationButtonVisibility.Auto">
      <summary>
        <para>The Minimize button is visible in each Ribbon style except the Office 2007 style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMinimizationButtonVisibility.Collapsed">
      <summary>
        <para>The Minimize button is hidden in all Ribbon styles.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonMinimizationButtonVisibility.Visible">
      <summary>
        <para>The Minimize button is visible in all Ribbon styles.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonPage">
      <summary>
        <para>Represents a <see href="https://docs.devexpress.com/WPF/7955/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page">Ribbon Page</see> within a <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPage.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.ActualColor">
      <summary>
        <para>Gets the color that is used to paint the page caption.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Color"/> object used to paint the page caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.ActualColorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.ActualColor">RibbonPage.ActualColor</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.ActualGroups">
      <summary>
        <para>Provides access to a collection of <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>s used by the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>.</para>
      </summary>
      <value>A ReadOnlyObservableCollection object, containing <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>s used by the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.ActualIsVisible">
      <summary>
        <para>Gets whether the page is displayed on-screen.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the page is displayed on-screen; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.ActualIsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.ActualIsVisible">RibbonPage.ActualIsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.AllowRemoveFromParentDuringCustomization">
      <summary>
        <para>Gets or sets if an end-user can remove the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> during the <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">Runtime Customization</see>. This is a dependency property.</para>
      </summary>
      <value>true, if an end-user can remove the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> during the <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">Runtime Customization</see>; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.AllowRemoveFromParentDuringCustomizationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.AllowRemoveFromParentDuringCustomization">RibbonPage.AllowRemoveFromParentDuringCustomization</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Altitude">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code. This is a dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.AltitudeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Altitude">RibbonPage.Altitude</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Background">
      <summary>
        <para>Gets or sets the brush used to paint the RibbonPage’s background. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object used to paint the RibbonPage’s background.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.BackgroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Background"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Badge">
      <summary>
        <para>Gets or sets the RibbonPage’s <see cref="T:DevExpress.Xpf.Core.Badge">BadgeControl</see>. This is a dependency property.</para>
      </summary>
      <value>The badge.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.BadgeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Badge"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.BorderBrush">
      <summary>
        <para>Gets or sets the brush used to paint the borders of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.BorderBrushProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.BorderBrush"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.BorderThickness">
      <summary>
        <para>Gets or sets the thickness of a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>‘s border. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value which specifies a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>‘s border thickness in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.BorderThicknessProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.BorderThickness"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Caption">
      <summary>
        <para>Gets or sets the object displayed within the page’s header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An object displayed within the page’s header.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.CaptionMinWidth">
      <summary>
        <para>Gets or sets the minimum allowed width of the current page header.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The minimum allowed width of the current page header</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CaptionMinWidthProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.CaptionMinWidth">RibbonPage.CaptionMinWidth</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Caption">RibbonPage.Caption</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.CaptionTemplate">
      <summary>
        <para>Gets or sets the template that presents the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Caption">RibbonPage.Caption</see> in a custom manner.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that presents the Ribbon Page’s caption in a custom manner.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CaptionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.CaptionTemplate">RibbonPage.CaptionTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.CategoryName">
      <summary>
        <para>Gets or sets the page’s category name. This is a dependency property.</para>
      </summary>
      <value>A string value that is a page category name.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CategoryNameProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.CategoryName">RibbonPage.CategoryName</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Commands">
      <summary>
        <para>Gets or sets a RibbonPage item’s commands. You should populate this collection when you use deferred ribbon page content loading. This is a dependency property.</para>
      </summary>
      <value>A collection of RibbonPage item commands.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CommandsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Commands"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.ContentBorderStyle">
      <summary>
        <para>Gets or sets a style applied to the RibbonPage’s contents. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to the RibbonPage’s contents.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.ContentBorderStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.ContentBorderStyle">RibbonPage.ContentBorderStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.CornerRadius">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>‘s corner radius. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.CornerRadius"/> value that specifies the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>‘s corner radius.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CornerRadiusProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.CornerRadius"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.CustomGroups">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.CustomGroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.CustomGroups">RibbonPage.CustomGroups</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Foreground">
      <summary>
        <para>Gets or sets the brush used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>‘s foreground. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>‘s foreground.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.ForegroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Foreground"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPage.GetRibbonPage(System.Windows.DependencyObject)">
      <summary>
        <para>Get the value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.RibbonPage">RibbonPage.RibbonPage</see> attached property to a specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupCollectionTemplate">
      <summary>
        <para>Gets or sets the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Groups">RibbonPage.Group</see> collection in RibbonPage that RibbonControl should load only when a user opens the page. This is a dependency property.</para>
      </summary>
      <value>A template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupCollectionTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupCollectionTemplate"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Groups">
      <summary>
        <para>Gets the collection of <see href="https://docs.devexpress.com/WPF/7956/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-group">page groups</see> displayed by the current page.</para>
      </summary>
      <value>A collection of ribbon page groups.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupsProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Groups">RibbonPage.Groups</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupsSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>s for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The source of objects to be visualized as ribbon groups.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupsSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupsSource">RibbonPage.GroupsSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplate">RibbonPage.GroupTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupStyle">RibbonPage.GroupStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupsSource">RibbonPage.GroupsSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupsSource">RibbonPage.GroupsSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplate">RibbonPage.GroupTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupsSource">RibbonPage.GroupsSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.GroupTemplateSelector">RibbonPage.GroupTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.HideWhenEmpty">
      <summary>
        <para>Gets or sets whether the current page is automatically hidden if it does not contain any items. This is a dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.HideWhenEmptyProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.HideWhenEmpty">RibbonPage.HideWhenEmpty</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.IsRemoved">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
        <para>This is a dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.IsRemovedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.IsRemoved">RibbonPage.IsRemoved</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.IsSelectable">
      <summary>
        <para>Gets if the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> is selectable at the moment.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> is selectable at the moment; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.IsSelected">
      <summary>
        <para>Gets or sets whether the current page is selected.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the current page is selected; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.IsSelectedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.IsSelected">RibbonPage.IsSelected</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.IsVisible">
      <summary>
        <para>Gets or sets the page’s visibility.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the page is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.IsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.IsVisible">RibbonPage.IsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.KeyTip">
      <summary>
        <para>Gets or sets the Key Tip used to display Key Tips for the page’s child elements.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies the Key Tip associated with the ribbon page.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.KeyTipProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.KeyTip">RibbonPage.KeyTip</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Margin">
      <summary>
        <para>Gets or sets the outer indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> that represents the outer indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.MarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Margin"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.MergeOrder">
      <summary>
        <para>Gets or sets the position of this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> within a merged Ribbon. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Nullable`1"/>&lt;<see cref="T:System.Int32"/>,&gt; value that is the position of this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> within a merged Ribbon.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.MergeOrderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.MergeOrder">RibbonPage.MergeOrder</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.MergeType">
      <summary>
        <para>Gets or sets the way ribbon pages merge.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonMergeType"/> value indicating how ribbon pages merge.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.MergeTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.MergeType">RibbonPage.MergeType</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Padding">
      <summary>
        <para>Gets or sets the inner indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that represents the inner indents (in pixels) around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage">RibbonPage</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.PaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.Padding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.PageCategory">
      <summary>
        <para>Gets the category that owns the current page.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/> object that owns the current page.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.PageCategoryProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.PageCategory">RibbonPage.PageCategory</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPage.Ribbon">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> to which the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object belongs.</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> to which the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object belongs</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPage.RibbonPageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPage.RibbonPage"/> attached property.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonPageCategory">
      <summary>
        <para>Represents a Ribbon custom <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">page category</see>, used to implement contextual Ribbon pages.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPageCategory.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategory.SelectedPageOnCategoryShowing">
      <summary>
        <para>Gets or sets which <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> will be initially selected on the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> display. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object that will be initially selected on the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> display.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategory.SelectedPageOnCategoryShowingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategory.SelectedPageOnCategoryShowing">RibbonPageCategory.SelectedPageOnCategoryShowing</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase">
      <summary>
        <para>Represents the base class for Ribbon <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">categories</see>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/> class.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.ActualIsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.ActualIsVisible"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.ActualPages">
      <summary>
        <para>Provides access to a collection of <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>s used by the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> or <see cref="T:DevExpress.Xpf.Ribbon.RibbonDefaultPageCategory"/>.</para>
      </summary>
      <value>A ReadOnlyObservableCollection object, containing <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>s used by the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> or <see cref="T:DevExpress.Xpf.Ribbon.RibbonDefaultPageCategory"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.AllowRemoveFromParentDuringCustomization">
      <summary>
        <para>Gets or sets if an end-user can remove Pages that belong to the current Category during <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">Runtime Customization</see>. This is a dependency property.</para>
      </summary>
      <value>true, if an end-user can remove Pages that belong to the current Category during <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">Runtime Customization</see>; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.AllowRemoveFromParentDuringCustomizationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.AllowRemoveFromParentDuringCustomization">RibbonPageCategoryBase.AllowRemoveFromParentDuringCustomization</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Altitude">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code. This is a dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.AltitudeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Altitude">RibbonPageCategoryBase.Altitude</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Background">
      <summary>
        <para>Gets or sets the brush used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s background. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s background.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BackgroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Background"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BorderBrush">
      <summary>
        <para>Gets or sets the brush used to paint the borders of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase">RibbonPageCategoryBase</see>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BorderBrushProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BorderBrush"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BorderThickness">
      <summary>
        <para>Gets or sets the thickness of a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s border. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value which specifies a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s border thickness in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BorderThicknessProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.BorderThickness"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Caption">
      <summary>
        <para>Gets or sets the category’s caption. This property is not in effect for the default page category.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies the category’s caption.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Caption">RibbonPageCategoryBase.Caption</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Color">
      <summary>
        <para>Gets or sets the color that is used to paint the category’s caption and its pages. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Drawing.Color"/> value that specifies the category’s color.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.ColorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Color">RibbonPageCategoryBase.Color</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CornerRadius">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s corner radius. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.CornerRadius"/> value that specifies the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s corner radius.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CornerRadiusProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CornerRadius"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CustomPages">
      <summary>
        <para>For internal use only.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CustomPagesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.CustomPages">RibbonPageCategoryBase.CustomPages</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Foreground">
      <summary>
        <para>Gets or sets the brush used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s foreground. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>‘s foreground.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.ForegroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Foreground"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.GetFirstSelectablePage">
      <summary>
        <para>Returns the first selectable <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> contained within the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/>.</para>
      </summary>
      <returns>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object that is the first selectable page within the current page category.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsDefault">
      <summary>
        <para>Gets whether the current category is default.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the current category is default; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsDefaultProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsDefault">RibbonPageCategoryBase.IsDefault</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsSelected">
      <summary>
        <para>Gets whether the current category contains a selected page.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the current category contains a selected page; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsSelectedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsSelected">RibbonPageCategoryBase.IsSelected</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsVisible">
      <summary>
        <para>Gets or sets whether the current category is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the current category is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.IsVisible">RibbonPageCategoryBase.IsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Margin">
      <summary>
        <para>Gets or sets the outer indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> that represents the outer indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Margin"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MergeOrder">
      <summary>
        <para>Gets or sets the position of ribbon pages that belong to this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/> within a merged Ribbon.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Nullable`1"/>&lt;<see cref="T:System.Int32"/>,&gt; value that is the position of ribbon pages that belong to this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/> within a merged Ribbon.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MergeOrderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MergeOrder">RibbonPageCategoryBase.MergeOrder</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MergeType">
      <summary>
        <para>Gets or sets the way ribbon categories merge. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonMergeType"/> value indicating how ribbon categories merge.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MergeTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.MergeType">RibbonPageCategoryBase.MergeType</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Padding">
      <summary>
        <para>Gets or sets the inner indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that represents the inner indents (in pixels) around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Padding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Pages">
      <summary>
        <para>Provides access to the collection of <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">pages</see> owned by the category.</para>
      </summary>
      <value>A RibbonPageCollection object that specifies the collection of pages owned by the category.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Pages">RibbonPageCategoryBase.Pages</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>s for the current ribbon category.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The source of objects to be visualized as ribbon pages.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesSource">RibbonPageCategoryBase.PagesSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplate">RibbonPageCategoryBase.PageTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageStyle">RibbonPageCategoryBase.PageStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the RibbonPageCategoryBase.PagesSource](xref:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesSource) collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesSource">RibbonPageCategoryBase.PagesSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplate">RibbonPageCategoryBase.PageTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PagesSource">RibbonPageCategoryBase.PagesSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.PageTemplateSelector">RibbonPageCategoryBase.PageTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Ribbon">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> that owns the current object.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> that owns the current object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.RibbonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageCategoryBase.Ribbon">RibbonPageCategoryBase.Ribbon</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonPageCategoryCaptionAlignment">
      <summary>
        <para>Enumerates values that specify how <see href="https://docs.devexpress.com/WPF/7960/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-categories-and-contextual-pages">custom page categories</see> are aligned.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryCaptionAlignment.Default">
      <summary>
        <para>The same as the <see cref="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryCaptionAlignment.Left">RibbonPageCategoryCaptionAlignment.Left</see> option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryCaptionAlignment.Left">
      <summary>
        <para>Headers of contextual page categories are aligned to a Ribbon Control’s left edge.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageCategoryCaptionAlignment.Right">
      <summary>
        <para>Headers of contextual page categories are aligned to a Ribbon Control’s right edge.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonPageGroup">
      <summary>
        <para>Represents a set of bar items and bar item links within a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPageGroup.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ActualHideWhenEmpty">
      <summary>
        <para>Gets the actual value that specifies whether the current group is automatically hidden if it does not contain any items.</para>
      </summary>
      <value>true, if the current page is hidden if it does not contain any items; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ActualIsVisible">
      <summary>
        <para>Returns whether the page group is actually visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the page group is actually visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ActualIsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ActualIsVisible">RibbonPageGroup.ActualIsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.AllowCollapse">
      <summary>
        <para>Gets or sets whether the page group can be collapsed.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the page group automatically collapses when its size is reduced, so it can no longer display all its links simultaneously; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.AllowCollapseProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.AllowCollapse">RibbonPageGroup.AllowCollapse</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.AllowRemoveFromParentDuringCustomization">
      <summary>
        <para>Gets or sets if an end-user can remove the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> during <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">Runtime Customization</see>. This is a dependency property.</para>
      </summary>
      <value>true, if an end-user can remove the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> during <see href="https://docs.devexpress.com/WPF/10626/controls-and-libraries/ribbon-bars-and-menu/ribbon/runtime-customization">Runtime Customization</see>; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.AllowRemoveFromParentDuringCustomizationProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.AllowRemoveFromParentDuringCustomization">RibbonPageGroup.AllowRemoveFromParentDuringCustomization</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Altitude">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code. This is a dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.AltitudeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Altitude">RibbonPageGroup.Altitude</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Background">
      <summary>
        <para>Gets or sets the brush used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s background. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s background.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.BackgroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Background"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.BorderBrush">
      <summary>
        <para>Gets or sets the brush used to paint the borders of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> value.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.BorderBrushProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.BorderBrush"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.BorderThickness">
      <summary>
        <para>Gets or sets the thickness of a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s border. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value which specifies a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s border thickness in pixels.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.BorderThicknessProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.BorderThickness"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Caption">
      <summary>
        <para>Gets or sets the text displayed within the group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string which specifies the text displayed within the group.</value>
    </member>
    <member name="E:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonClick">
      <summary>
        <para>Occurs when the group’s Caption Button is clicked.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommand">
      <summary>
        <para>Gets or sets the command to invoke when the group’s Caption Button is clicked.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The command to invoke when the group’s Caption Button is clicked.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandParameter">
      <summary>
        <para>Gets or sets the parameter to pass to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommand">RibbonPageGroup.CaptionButtonCommand</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A parameter to pass to the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommand">RibbonPageGroup.CaptionButtonCommand</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandParameterProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandParameter">RibbonPageGroup.CaptionButtonCommandParameter</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommand">RibbonPageGroup.CaptionButtonCommand</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandTarget">
      <summary>
        <para>Gets or sets the element on which to raise the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommand">RibbonPageGroup.CaptionButtonCommand</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The element on which to raise the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommand">RibbonPageGroup.CaptionButtonCommand</see>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandTargetProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionButtonCommandTarget">RibbonPageGroup.CaptionButtonCommandTarget</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.CaptionProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Caption">RibbonPageGroup.Caption</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CornerRadius">
      <summary>
        <para>Gets or sets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s corner radius. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.CornerRadius"/> value that specifies the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s corner radius.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.CornerRadiusProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.CornerRadius"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Foreground">
      <summary>
        <para>Gets or sets the brush used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s foreground. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Media.Brush"/> object used to paint the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>‘s foreground.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ForegroundProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Foreground"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPageGroup.GetDefaultItemsGlyphSize(DevExpress.Xpf.Bars.LinkContainerType)">
      <summary>
        <para>Implements the <see cref="M:DevExpress.Xpf.Bars.ILinksHolder.GetDefaultItemsGlyphSize(DevExpress.Xpf.Bars.LinkContainerType)">ILinksHolder.GetDefaultItemsGlyphSize</see> method.</para>
      </summary>
      <param name="linkContainerType">A LinkContainerType value that specifies the type of bar item container.</param>
      <returns>A <see cref="T:DevExpress.Xpf.Bars.GlyphSize"/> value.</returns>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonPageGroup.GetLogicalChildrenEnumerator">
      <summary>
        <para>Gets an enumerator used to iterate through bar item links.</para>
      </summary>
      <returns>An IEnumerator object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Glyph">
      <summary>
        <para>Gets or sets the image displayed within the group in the minimized state.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that represents the image displayed within the group in the minimized state.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.GlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Glyph"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.HeaderBorderStyle">
      <summary>
        <para>Gets or sets a style applied to the RibbonPageGroup’s header. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Style"/> object applied to the RibbonPageGroup’s header.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.HeaderBorderStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.HeaderBorderStyle"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.HideWhenEmpty">
      <summary>
        <para>Gets or sets whether the current page is automatically hidden if it does not contain any items. This is a dependency property.</para>
      </summary>
      <value>The <see cref="T:System.Nullable`1"/>&lt;<see cref="T:System.Boolean"/>,&gt; value that specifies whether the current page is automatically hidden if it does not contain any items. The default is null.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.HideWhenEmptyProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.HideWhenEmpty">RibbonPageGroup.HideWhenEmpty</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsCaptionButtonEnabled">
      <summary>
        <para>Gets or sets whether the Caption Button is enabled. This is a dependency property.</para>
      </summary>
      <value>true if the Caption Button is enabled; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsCaptionButtonEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsCaptionButtonEnabled">RibbonPageGroup.IsCaptionButtonEnabled</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsRemoved">
      <summary>
        <para>This member supports the internal infrastructure, and is not intended to be used directly from your code.</para>
        <para>This is a dependency property.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsRemovedProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsRemoved">RibbonPageGroup.IsRemoved</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsVisible">
      <summary>
        <para>Gets or sets whether the current group is visible. This is a dependency property.</para>
      </summary>
      <value>true if the current group is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.IsVisible">RibbonPageGroup.IsVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinks">
      <summary>
        <para>Gets the collection of <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar item links</see> displayed by the current <see href="https://docs.devexpress.com/WPF/7956/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page-group">page group</see>.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that stores bar item links displayed by the group.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize bar item links for the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> container.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>The source of objects to be visualized as bar item links.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSourceElementGeneratesUniqueBarItem">
      <summary>
        <para>Gets or sets whether each reference to a data object in an <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">RibbonPageGroup.ItemLinksSource</see> for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> should generate a unique <see cref="T:DevExpress.Xpf.Bars.BarItem"/>, whether this data object was previously referenced. This is a dependency property.</para>
      </summary>
      <value>true if each reference to a data object in an <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">RibbonPageGroup.ItemLinksSource</see> for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> should generate a unique <see cref="T:DevExpress.Xpf.Bars.BarItem"/>; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSourceElementGeneratesUniqueBarItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSourceElementGeneratesUniqueBarItem">RibbonPageGroup.ItemLinksSourceElementGeneratesUniqueBarItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">RibbonPageGroup.ItemLinksSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Items">
      <summary>
        <para>Provides access to <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items and bar item links</see> displayed in this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection collection that stores bar items and bar item links displayed in this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemsGlyphSize">
      <summary>
        <para>Gets the size of images used by bar item links owned by the current object.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.GlyphSize"/> value.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplate">RibbonPageGroup.ItemTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemStyle">RibbonPageGroup.ItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">RibbonPageGroup.ItemLinksSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">RibbonPageGroup.ItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplate">RibbonPageGroup.ItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemLinksSource">RibbonPageGroup.ItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ItemTemplateSelector">RibbonPageGroup.ItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.KeyTip">
      <summary>
        <para>Gets or sets the Key Tip used to mimic a click on a ribbon page group’s button.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies the Key Tip associated with page group’s button.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.KeyTipGroupExpanding">
      <summary>
        <para>Gets or sets the Key Tip used to expand a collapsed ribbon page group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A string that specifies the Key Tip used to expand a collapsed ribbon page group.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.KeyTipGroupExpandingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.KeyTipGroupExpanding">RibbonPageGroup.KeyTipGroupExpanding</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.KeyTipProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.KeyTip">RibbonPageGroup.KeyTip</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Links">
      <summary>
        <para>Gets the collection of links owned by the current page group.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that contains links owned by the current page group.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Margin">
      <summary>
        <para>Gets or sets the outer indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> that represents the outer indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.MarginProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Margin"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.MergeOrder">
      <summary>
        <para>Gets or sets the position of this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> within a merged ribbon page.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Nullable`1"/>&lt;<see cref="T:System.Int32"/>,&gt; object that is the position of this <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> within a merged ribbon page.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.MergeOrderProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.MergeOrder">RibbonPageGroup.MergeOrder</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.MergeType">
      <summary>
        <para>Gets or sets the way ribbon page groups merge.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonMergeType"/> value indicating how ribbon page groups merge.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.MergeTypeProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.MergeType">RibbonPageGroup.MergeType</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Padding">
      <summary>
        <para>Gets or sets the inner indents around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Thickness"/> value that represents the inner indents (in pixels) around the <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/>.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.PaddingProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Padding"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Page">
      <summary>
        <para>Gets the <see href="https://docs.devexpress.com/WPF/7955/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-page">Ribbon Page</see> that owns the current page group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> object that owns the current page group.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.PageProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Page">RibbonPageGroup.Page</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.Ribbon">
      <summary>
        <para>Gets the <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> to which the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> object belongs</para>
      </summary>
      <value>The <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/> to which the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageGroup"/> object belongs</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ShowCaptionButton">
      <summary>
        <para>Gets or sets whether the Caption Button is visible.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the Caption Button is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.ShowCaptionButtonProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.ShowCaptionButton">RibbonPageGroup.ShowCaptionButton</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.SmallGlyph">
      <summary>
        <para>Gets or sets the glyph of the drop-down button displayed in the collapsed group. This property is in effect for the TabletOffice and OfficeSlim ribbon styles. This is a dependency property.</para>
      </summary>
      <value>An ImageSource object that represents an image displayed in the drop-down button of the collapsed group.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.SmallGlyphProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.SmallGlyph">RibbonPageGroup.SmallGlyph</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.SuperTip">
      <summary>
        <para>Gets or sets a SuperTip associated with the ribbon page group.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Core.SuperTip"/> object.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonPageGroup.SuperTipProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonPageGroup.SuperTip">RibbonPageGroup.SuperTip</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode">
      <summary>
        <para>Contains values that specify where a <see href="https://docs.devexpress.com/WPF/7957/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-quick-access-toolbar">Ribbon Quick Access Toolbar</see> is displayed within a <see href="https://docs.devexpress.com/WPF/7954/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-control">Ribbon Control</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode.Default">
      <summary>
        <para>The same as the <see cref="F:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode.ShowAbove">RibbonQuickAccessToolbarShowMode.ShowAbove</see> option.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode.Hide">
      <summary>
        <para>The Ribbon Quick Access Toolbar is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode.ShowAbove">
      <summary>
        <para>The Ribbon Quick Access Toolbar is displayed above the Ribbon Control.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonQuickAccessToolbarShowMode.ShowBelow">
      <summary>
        <para>The Ribbon Quick Access Toolbar is displayed below the Ribbon Control.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl">
      <summary>
        <para>A Ribbon UI status bar.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.#ctor">
      <summary>
        <para>Initializes a new instance of the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> class.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.ActualIsSizeGripVisible">
      <summary>
        <para>Gets if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s size grip element is visible. This is a dependency property.</para>
      </summary>
      <value>true if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s size grip element is visible; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.ActualIsSizeGripVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.ActualIsSizeGripVisible">RibbonStatusBarControl.ActualIsSizeGripVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.AsyncMergingEnabled">
      <summary>
        <para>Gets or sets whether <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> asynchronous merging is enabled.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true, if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> asynchronous merging is enabled; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.AsyncMergingEnabledProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.AsyncMergingEnabled">RibbonStatusBarControl.AsyncMergingEnabled</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.GetRibbonStatusBar(System.Windows.DependencyObject)">
      <summary>
        <para>Gets the value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> attached property for the specified object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> property’s value is to be returned.</param>
      <returns>The value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> attached property for the specified object.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.IsChild">
      <summary>
        <para>Gets if the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged with any other <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>.</para>
      </summary>
      <value>true if the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged with any other <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>; otherwise, false.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.IsMerged">
      <summary>
        <para>Gets if any <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>.</para>
      </summary>
      <value>true if any <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>; otherwise, false .</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.IsMergedChild(DevExpress.Xpf.Ribbon.RibbonStatusBarControl)">
      <summary>
        <para>Indicates whether the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> is merged with the current one.</para>
      </summary>
      <param name="item">A child <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> object to be tested.</param>
      <returns>true if the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged with the current one; otherwise, false.</returns>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.IsSizeGripVisible">
      <summary>
        <para>Gets or sets whether the size grip is displayed within the status bar.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>true if the size grip is displayed within the status bar; otherwise, false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.IsSizeGripVisibleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.IsSizeGripVisible">RibbonStatusBarControl.IsSizeGripVisible</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.ItemLinksSourceElementGeneratesUniqueBarItem">
      <summary>
        <para>Gets or sets whether each reference to a data object in an ItemLinksSource for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> should generate a unique <see cref="T:DevExpress.Xpf.Bars.BarItem"/>, whether this data object was previously referenced. This is a dependency property.</para>
      </summary>
      <value>true if each reference to a data object in an ItemLinksSource for this <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> should generate a unique <see cref="T:DevExpress.Xpf.Bars.BarItem"/>; otherwise, false. The default is false.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.ItemLinksSourceElementGeneratesUniqueBarItemProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.ItemLinksSourceElementGeneratesUniqueBarItem">RibbonStatusBarControl.ItemLinksSourceElementGeneratesUniqueBarItem</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinks">
      <summary>
        <para>Gets the collection of links displayed on the left of the RibbonStatusBar.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that stores the links displayed on the left of the RibbonStatusBar.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinksSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize bar item links displayed at the left part of the <see href="https://docs.devexpress.com/WPF/7958/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-status-bar">Ribbon Status Bar</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as status bar items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinksSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinksSource">RibbonStatusBarControl.LeftItemLinksSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItems">
      <summary>
        <para>Provides access to <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items and bar item links</see> displayed at the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s left.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection collection that stores bar items and bar item links displayed at the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s left.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplate">RibbonStatusBarControl.LeftItemTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemStyle">RibbonStatusBarControl.LeftItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinksSource">RibbonStatusBarControl.LeftItemLinksSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the  <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinksSource">RibbonStatusBarControl.LeftItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplate">RibbonStatusBarControl.LeftItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemLinksSource">RibbonStatusBarControl.LeftItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.LeftItemTemplateSelector">RibbonStatusBarControl.LeftItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.MDIMergeStyle">
      <summary>
        <para>Gets or sets if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> can be merged.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.MDIMergeStyle"/> value specifying if the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> can be merged.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.MDIMergeStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.MDIMergeStyle">RibbonStatusBarControl.MDIMergeStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.Merge(DevExpress.Xpf.Ribbon.RibbonStatusBarControl)">
      <summary>
        <para>Merges the specified ribbon status bar with the current bar.</para>
      </summary>
      <param name="childStatusBar">A <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> whose visible item links must be merged with the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>.</param>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.MergedParent">
      <summary>
        <para>Gets a parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> with which the current child <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged.</para>
      </summary>
      <value>A parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> object, with which the current child <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> has been merged.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.OnApplyTemplate">
      <summary>
        <para>Called after the template is completely generated and attached to the visual tree.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBarProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> dependency property.</para>
      </summary>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinks">
      <summary>
        <para>Gets the collection of links displayed on the right of the RibbonStatusBar.</para>
      </summary>
      <value>A <see cref="T:DevExpress.Xpf.Bars.BarItemLinkCollection"/> object that stores the links displayed on the right of the RibbonStatusBar.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinksSource">
      <summary>
        <para>Gets or sets a collection of objects providing information to generate and initialize bar item links at the right part of the <see href="https://docs.devexpress.com/WPF/7958/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-status-bar">Ribbon Status Bar</see>.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A source of objects to be visualized as status bar items.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinksSourceProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinksSource">RibbonStatusBarControl.RightItemLinksSource</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItems">
      <summary>
        <para>Provides access to <see href="https://docs.devexpress.com/WPF/7983/controls-and-libraries/ribbon-bars-and-menu/ribbon/populating-ribbon">bar items and bar item links</see> displayed at the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s right.</para>
      </summary>
      <value>A DevExpress.Xpf.Bars.CommonBarItemCollection collection that stores bar items and bar item links displayed at the <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s right.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemStyle">
      <summary>
        <para>Gets or sets the style applied to a <see cref="T:DevExpress.Xpf.Bars.BarItem"/> object defined as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplate">RibbonStatusBarControl.RightItemTemplate</see>‘s content.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A Style object providing corresponding style settings.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemStyleProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemStyle">RibbonStatusBarControl.RightItemStyle</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemStyleSelector">
      <summary>
        <para>Gets or sets an object that chooses a style applied to objects generated with the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinksSource">RibbonStatusBarControl.RightItemLinksSource</see> collection. This is a dependency property.</para>
      </summary>
      <value>A <see cref="T:System.Windows.Controls.StyleSelector"/> descendant that applies a style based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemStyleSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemStyleSelector"/> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplate">
      <summary>
        <para>Gets or sets the template used to visualize objects stored as elements in the  <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinksSource">RibbonStatusBarControl.RightItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A DataTemplate object that specifies the corresponding template.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplateProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplate">RibbonStatusBarControl.RightItemTemplate</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplateSelector">
      <summary>
        <para>Gets or sets an object that chooses a template used to visualize objects stored as elements in the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemLinksSource">RibbonStatusBarControl.RightItemLinksSource</see> collection.</para>
        <para>This is a dependency property.</para>
      </summary>
      <value>A System.Windows.Controls.DataTemplateSelector descendant that applies a template based on custom logic.</value>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplateSelectorProperty">
      <summary>
        <para>Identifies the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RightItemTemplateSelector">RibbonStatusBarControl.RightItemTemplateSelector</see> dependency property.</para>
      </summary>
      <value>A dependency property identifier.</value>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.SetRibbonStatusBar(System.Windows.DependencyObject,DevExpress.Xpf.Ribbon.RibbonStatusBarControl)">
      <summary>
        <para>Sets the value of the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> attached property for the specified object.</para>
      </summary>
      <param name="obj">A DependencyObject whose <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> property’s value is to be set.</param>
      <param name="value">The <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> class object set as the <see cref="P:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.RibbonStatusBar">RibbonStatusBarControl.RibbonStatusBar</see> attached property value.</param>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.UnMerge">
      <summary>
        <para>Restores the original <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>‘s layout.</para>
      </summary>
    </member>
    <member name="M:DevExpress.Xpf.Ribbon.RibbonStatusBarControl.UnMerge(DevExpress.Xpf.Ribbon.RibbonStatusBarControl)">
      <summary>
        <para>Removes the links of the specified <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> from the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>. This method is in effect when these two status bars have been previously merged.</para>
      </summary>
      <param name="childStatusBar">A <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/> object whose links are to be removed from the current <see cref="T:DevExpress.Xpf.Ribbon.RibbonStatusBarControl"/>.</param>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonStyle">
      <summary>
        <para>Enumerates available paint styles for a <see href="https://docs.devexpress.com/WPF/7954/controls-and-libraries/ribbon-bars-and-menu/ribbon/ribbon-structure/ribbon-control">Ribbon Control</see>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStyle.Office2007">
      <summary>
        <para>A RibbonControl’s elements are painted in the Office2007 paint style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStyle.Office2010">
      <summary>
        <para>A RibbonControl’s elements are painted in the Office2010 paint style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStyle.Office2019">
      <summary>
        <para>A RibbonControl’s elements are painted in the Office2017 paint style.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStyle.OfficeSlim">
      <summary>
        <para>The Ribbon style inspired by the ribbon UI introduced in Microsoft “Office Universal” apps.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonStyle.TabletOffice">
      <summary>
        <para>Style inspired by the Ribbon control in MS Office for iPad.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.RibbonTitleBarVisibility">
      <summary>
        <para>Lists values that specify the visibility of the bar displaying the Quick Access Toolbar.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonTitleBarVisibility.Auto">
      <summary>
        <para>The bar is hidden if the following conditions are met:</para>
        <para />
        <para />
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonTitleBarVisibility.Collapsed">
      <summary>
        <para>The bar is hidden.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.RibbonTitleBarVisibility.Visible">
      <summary>
        <para>The bar is visible.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.SelectedPageOnCategoryShowing">
      <summary>
        <para>Provides members that specify which page within a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> will be selected when this category is displayed.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.SelectedPageOnCategoryShowing.FirstPage">
      <summary>
        <para>The first <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> within a <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> will be selected.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.SelectedPageOnCategoryShowing.None">
      <summary>
        <para>The target <see cref="T:DevExpress.Xpf.Ribbon.RibbonPageCategory"/> will be displayed with all its <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/>s unselected.</para>
      </summary>
    </member>
    <member name="T:DevExpress.Xpf.Ribbon.SelectedPageOnMerging">
      <summary>
        <para>Provides members that specify which <see cref="T:DevExpress.Xpf.Ribbon.RibbonPage"/> will be selected after ribbon controls are merged.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.SelectedPageOnMerging.ParentSelectedPage">
      <summary>
        <para>Specifies that the parent <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s currently selected page should be selected within a resulting <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
    </member>
    <member name="F:DevExpress.Xpf.Ribbon.SelectedPageOnMerging.SelectedPage">
      <summary>
        <para>Specifies that the child <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>‘s currently selected page should be selected within a resulting <see cref="T:DevExpress.Xpf.Ribbon.RibbonControl"/>.</para>
      </summary>
    </member>
  </members>
</doc>