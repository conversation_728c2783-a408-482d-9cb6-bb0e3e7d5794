{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    isDefined,\r\n    isNumeric,\r\n    isExponential,\r\n    isFunction,\r\n    isString\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    adjust,\r\n    sign\r\n} from \"../../core/utils/math\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nimport Color from \"../../color\";\r\nconst {\r\n    PI: PI,\r\n    LN10: LN10,\r\n    abs: abs,\r\n    log: log,\r\n    floor: floor,\r\n    ceil: ceil,\r\n    pow: pow,\r\n    sqrt: sqrt,\r\n    atan2: atan2\r\n} = Math;\r\nconst _min = Math.min;\r\nconst _max = Math.max;\r\nconst _cos = Math.cos;\r\nconst _sin = Math.sin;\r\nconst _round = Math.round;\r\nconst dateToMilliseconds = dateUtils.dateToMilliseconds;\r\nconst MAX_PIXEL_COUNT = 1e10;\r\nconst PI_DIV_180 = PI / 180;\r\nconst _isNaN = isNaN;\r\nconst _Number = Number;\r\nconst _NaN = NaN;\r\nlet numDefsSvgElements = 1;\r\nexport const PANE_PADDING = 10;\r\nexport const getLog = function(value, base) {\r\n    if (!value) {\r\n        return NaN\r\n    }\r\n    return log(value) / log(base)\r\n};\r\nexport const getAdjustedLog10 = function(value) {\r\n    return adjust(getLog(value, 10))\r\n};\r\nexport const raiseTo = function(power, base) {\r\n    return pow(base, power)\r\n};\r\nexport const normalizeAngle = function(angle) {\r\n    return (angle % 360 + 360) % 360\r\n};\r\nexport const convertAngleToRendererSpace = function(angle) {\r\n    return 90 - angle\r\n};\r\nexport const degreesToRadians = function(value) {\r\n    return PI * value / 180\r\n};\r\nexport const getCosAndSin = function(angle) {\r\n    const angleInRadians = degreesToRadians(angle);\r\n    return {\r\n        cos: _cos(angleInRadians),\r\n        sin: _sin(angleInRadians)\r\n    }\r\n};\r\nconst DECIMAL_ORDER_THRESHOLD = 1e-14;\r\nexport const getDistance = function(x1, y1, x2, y2) {\r\n    const diffX = x2 - x1;\r\n    const diffY = y2 - y1;\r\n    return sqrt(diffY * diffY + diffX * diffX)\r\n};\r\nexport const getDecimalOrder = function(number) {\r\n    let n = abs(number);\r\n    let cn;\r\n    if (!_isNaN(n)) {\r\n        if (n > 0) {\r\n            n = log(n) / LN10;\r\n            cn = ceil(n);\r\n            return cn - n < 1e-14 ? cn : floor(n)\r\n        }\r\n        return 0\r\n    }\r\n    return NaN\r\n};\r\nexport const getAppropriateFormat = function(start, end, count) {\r\n    const order = _max(getDecimalOrder(start), getDecimalOrder(end));\r\n    let precision = -getDecimalOrder(abs(end - start) / count);\r\n    let format;\r\n    if (!_isNaN(order) && !_isNaN(precision)) {\r\n        if (abs(order) <= 4) {\r\n            format = \"fixedPoint\";\r\n            precision < 0 && (precision = 0);\r\n            precision > 4 && (precision = 4)\r\n        } else {\r\n            format = \"exponential\";\r\n            precision += order - 1;\r\n            precision > 3 && (precision = 3)\r\n        }\r\n        return {\r\n            type: format,\r\n            precision: precision\r\n        }\r\n    }\r\n    return null\r\n};\r\nexport const roundValue = function(value, precision) {\r\n    if (precision > 20) {\r\n        precision = 20\r\n    }\r\n    if (isNumeric(value)) {\r\n        if (isExponential(value)) {\r\n            return _Number(value.toExponential(precision))\r\n        } else {\r\n            return _Number(value.toFixed(precision))\r\n        }\r\n    }\r\n};\r\nexport const getPower = function(value) {\r\n    return value.toExponential().split(\"e\")[1]\r\n};\r\nexport function map(array, callback) {\r\n    let i = 0;\r\n    const len = array.length;\r\n    const result = [];\r\n    let value;\r\n    while (i < len) {\r\n        value = callback(array[i], i);\r\n        if (null !== value) {\r\n            result.push(value)\r\n        }\r\n        i++\r\n    }\r\n    return result\r\n}\r\n\r\nfunction selectByKeys(object, keys) {\r\n    return map(keys, (key => object[key] ? object[key] : null))\r\n}\r\n\r\nfunction decreaseFields(object, keys, eachDecrease, decrease) {\r\n    let dec = decrease;\r\n    each(keys, ((_, key) => {\r\n        if (object[key]) {\r\n            object[key] -= eachDecrease;\r\n            dec -= eachDecrease\r\n        }\r\n    }));\r\n    return dec\r\n}\r\nexport function normalizeEnum(value) {\r\n    return String(value).toLowerCase()\r\n}\r\nexport function setCanvasValues(canvas) {\r\n    if (canvas) {\r\n        canvas.originalTop = canvas.top;\r\n        canvas.originalBottom = canvas.bottom;\r\n        canvas.originalLeft = canvas.left;\r\n        canvas.originalRight = canvas.right\r\n    }\r\n    return canvas\r\n}\r\n\r\nfunction normalizeBBoxField(value) {\r\n    return -1e10 < value && value < 1e10 ? value : 0\r\n}\r\nexport function normalizeBBox(bBox) {\r\n    const xl = normalizeBBoxField(floor(bBox.x));\r\n    const yt = normalizeBBoxField(floor(bBox.y));\r\n    const xr = normalizeBBoxField(ceil(bBox.width + bBox.x));\r\n    const yb = normalizeBBoxField(ceil(bBox.height + bBox.y));\r\n    const result = {\r\n        x: xl,\r\n        y: yt,\r\n        width: xr - xl,\r\n        height: yb - yt\r\n    };\r\n    result.isEmpty = !result.x && !result.y && !result.width && !result.height;\r\n    return result\r\n}\r\nexport function rotateBBox(bBox, center, angle) {\r\n    const cos = _Number(_cos(angle * PI_DIV_180).toFixed(3));\r\n    const sin = _Number(_sin(angle * PI_DIV_180).toFixed(3));\r\n    const w2 = bBox.width / 2;\r\n    const h2 = bBox.height / 2;\r\n    const centerX = bBox.x + w2;\r\n    const centerY = bBox.y + h2;\r\n    const w2_ = abs(w2 * cos) + abs(h2 * sin);\r\n    const h2_ = abs(w2 * sin) + abs(h2 * cos);\r\n    const centerX_ = center[0] + (centerX - center[0]) * cos + (centerY - center[1]) * sin;\r\n    const centerY_ = center[1] - (centerX - center[0]) * sin + (centerY - center[1]) * cos;\r\n    return normalizeBBox({\r\n        x: centerX_ - w2_,\r\n        y: centerY_ - h2_,\r\n        width: 2 * w2_,\r\n        height: 2 * h2_\r\n    })\r\n}\r\nexport const decreaseGaps = function(object, keys, decrease) {\r\n    let arrayGaps;\r\n    do {\r\n        arrayGaps = selectByKeys(object, keys);\r\n        arrayGaps.push(ceil(decrease / arrayGaps.length));\r\n        decrease = decreaseFields(object, keys, _min.apply(null, arrayGaps), decrease)\r\n    } while (decrease > 0 && arrayGaps.length > 1);\r\n    return decrease\r\n};\r\nexport const parseScalar = function(value, defaultValue) {\r\n    return void 0 !== value ? value : defaultValue\r\n};\r\nexport const enumParser = function(values) {\r\n    const stored = {};\r\n    let i;\r\n    let ii;\r\n    for (i = 0, ii = values.length; i < ii; ++i) {\r\n        stored[normalizeEnum(values[i])] = 1\r\n    }\r\n    return function(value, defaultValue) {\r\n        const _value = normalizeEnum(value);\r\n        return stored[_value] ? _value : defaultValue\r\n    }\r\n};\r\nexport const patchFontOptions = function(options) {\r\n    const fontOptions = {};\r\n    each(options || {}, (function(key, value) {\r\n        if (/^(cursor)$/i.test(key)) {} else if (\"opacity\" === key) {\r\n            value = null\r\n        } else if (\"color\" === key) {\r\n            key = \"fill\";\r\n            if (\"opacity\" in options) {\r\n                const color = new Color(value);\r\n                value = `rgba(${color.r},${color.g},${color.b},${options.opacity})`\r\n            }\r\n        } else {\r\n            key = \"font-\" + key\r\n        }\r\n        fontOptions[key] = value\r\n    }));\r\n    return fontOptions\r\n};\r\nexport function convertPolarToXY(centerCoords, startAngle, angle, radius) {\r\n    const normalizedRadius = radius > 0 ? radius : 0;\r\n    angle = isDefined(angle) ? angle + startAngle - 90 : 0;\r\n    const cosSin = getCosAndSin(angle);\r\n    return {\r\n        x: _round(centerCoords.x + normalizedRadius * cosSin.cos),\r\n        y: _round(centerCoords.y + normalizedRadius * cosSin.sin)\r\n    }\r\n}\r\nexport const convertXYToPolar = function(centerCoords, x, y) {\r\n    const radius = getDistance(centerCoords.x, centerCoords.y, x, y);\r\n    const angle = atan2(y - centerCoords.y, x - centerCoords.x);\r\n    return {\r\n        phi: _round(normalizeAngle(180 * angle / PI)),\r\n        r: _round(radius)\r\n    }\r\n};\r\nexport const processSeriesTemplate = function(seriesTemplate, items) {\r\n    const customizeSeries = isFunction(seriesTemplate.customizeSeries) ? seriesTemplate.customizeSeries : noop;\r\n    const nameField = seriesTemplate.nameField;\r\n    const generatedSeries = {};\r\n    const seriesOrder = [];\r\n    let series;\r\n    let i = 0;\r\n    let length;\r\n    let data;\r\n    items = items || [];\r\n    for (length = items.length; i < length; i++) {\r\n        data = items[i];\r\n        if (nameField in data) {\r\n            series = generatedSeries[data[nameField]];\r\n            if (!series) {\r\n                series = generatedSeries[data[nameField]] = {\r\n                    name: data[nameField],\r\n                    nameFieldValue: data[nameField]\r\n                };\r\n                seriesOrder.push(series.name)\r\n            }\r\n        }\r\n    }\r\n    return map(seriesOrder, (function(orderedName) {\r\n        const group = generatedSeries[orderedName];\r\n        return extend(group, customizeSeries.call(null, group.name))\r\n    }))\r\n};\r\nexport const getCategoriesInfo = function(categories, startValue, endValue) {\r\n    if (0 === categories.length) {\r\n        return {\r\n            categories: []\r\n        }\r\n    }\r\n    startValue = isDefined(startValue) ? startValue : categories[0];\r\n    endValue = isDefined(endValue) ? endValue : categories[categories.length - 1];\r\n    const categoriesValue = map(categories, (category => null === category || void 0 === category ? void 0 : category.valueOf()));\r\n    let indexStartValue = categoriesValue.indexOf(startValue.valueOf());\r\n    let indexEndValue = categoriesValue.indexOf(endValue.valueOf());\r\n    let swapBuf;\r\n    let inverted = false;\r\n    indexStartValue < 0 && (indexStartValue = 0);\r\n    indexEndValue < 0 && (indexEndValue = categories.length - 1);\r\n    if (indexEndValue < indexStartValue) {\r\n        swapBuf = indexEndValue;\r\n        indexEndValue = indexStartValue;\r\n        indexStartValue = swapBuf;\r\n        inverted = true\r\n    }\r\n    const visibleCategories = categories.slice(indexStartValue, indexEndValue + 1);\r\n    const lastIdx = visibleCategories.length - 1;\r\n    return {\r\n        categories: visibleCategories,\r\n        start: visibleCategories[inverted ? lastIdx : 0],\r\n        end: visibleCategories[inverted ? 0 : lastIdx],\r\n        inverted: inverted\r\n    }\r\n};\r\nexport function isRelativeHeightPane(pane) {\r\n    return !(pane.unit % 2)\r\n}\r\nexport function normalizePanesHeight(panes) {\r\n    panes.forEach((pane => {\r\n        const height = pane.height;\r\n        let unit = 0;\r\n        let parsedHeight = parseFloat(height) || void 0;\r\n        if (isString(height) && height.indexOf(\"px\") > -1 || isNumeric(height) && height > 1) {\r\n            parsedHeight = _round(parsedHeight);\r\n            unit = 1\r\n        }\r\n        if (!unit && parsedHeight) {\r\n            if (isString(height) && height.indexOf(\"%\") > -1) {\r\n                parsedHeight /= 100;\r\n                unit = 2\r\n            } else if (parsedHeight < 0) {\r\n                parsedHeight = parsedHeight < -1 ? 1 : abs(parsedHeight)\r\n            }\r\n        }\r\n        pane.height = parsedHeight;\r\n        pane.unit = unit\r\n    }));\r\n    const relativeHeightPanes = panes.filter(isRelativeHeightPane);\r\n    const weightSum = relativeHeightPanes.reduce(((prev, next) => prev + (next.height || 0)), 0);\r\n    const weightHeightCount = relativeHeightPanes.length;\r\n    const emptyHeightPanes = relativeHeightPanes.filter((pane => !pane.height));\r\n    const emptyHeightCount = emptyHeightPanes.length;\r\n    if (weightSum < 1 && emptyHeightCount) {\r\n        emptyHeightPanes.forEach((pane => pane.height = (1 - weightSum) / emptyHeightCount))\r\n    } else if (weightSum > 1 || weightSum < 1 && !emptyHeightCount || 1 === weightSum && emptyHeightCount) {\r\n        if (emptyHeightCount) {\r\n            const weightForEmpty = weightSum / weightHeightCount;\r\n            const emptyWeightSum = emptyHeightCount * weightForEmpty;\r\n            relativeHeightPanes.filter((pane => pane.height)).forEach((pane => pane.height *= (weightSum - emptyWeightSum) / weightSum));\r\n            emptyHeightPanes.forEach((pane => pane.height = weightForEmpty))\r\n        }\r\n        relativeHeightPanes.forEach((pane => pane.height *= 1 / weightSum))\r\n    }\r\n}\r\nexport function updatePanesCanvases(panes, canvas, rotated) {\r\n    let distributedSpace = 0;\r\n    const paneSpace = rotated ? canvas.width - canvas.left - canvas.right : canvas.height - canvas.top - canvas.bottom;\r\n    const totalCustomSpace = panes.reduce(((prev, cur) => prev + (!isRelativeHeightPane(cur) ? cur.height : 0)), 0);\r\n    const usefulSpace = paneSpace - 10 * (panes.length - 1) - totalCustomSpace;\r\n    const startName = rotated ? \"left\" : \"top\";\r\n    const endName = rotated ? \"right\" : \"bottom\";\r\n    panes.forEach((pane => {\r\n        const calcLength = !isRelativeHeightPane(pane) ? pane.height : _round(pane.height * usefulSpace);\r\n        pane.canvas = pane.canvas || {};\r\n        extend(pane.canvas, canvas);\r\n        pane.canvas[startName] = canvas[startName] + distributedSpace;\r\n        pane.canvas[endName] = canvas[endName] + (paneSpace - calcLength - distributedSpace);\r\n        distributedSpace = distributedSpace + calcLength + 10;\r\n        setCanvasValues(pane.canvas)\r\n    }))\r\n}\r\nexport const unique = function(array) {\r\n    const values = {};\r\n    return map(array, (function(item) {\r\n        const result = !values[item] ? item : null;\r\n        values[item] = true;\r\n        return result\r\n    }))\r\n};\r\nexport const getVerticallyShiftedAngularCoords = function(bBox, dy, center) {\r\n    const isPositive = bBox.x + bBox.width / 2 >= center.x;\r\n    const horizontalOffset1 = (isPositive ? bBox.x : bBox.x + bBox.width) - center.x;\r\n    const verticalOffset1 = bBox.y - center.y;\r\n    const verticalOffset2 = verticalOffset1 + dy;\r\n    const horizontalOffset2 = _round(sqrt(horizontalOffset1 * horizontalOffset1 + verticalOffset1 * verticalOffset1 - verticalOffset2 * verticalOffset2));\r\n    const dx = (isPositive ? +horizontalOffset2 : -horizontalOffset2) || horizontalOffset1;\r\n    return {\r\n        x: center.x + (isPositive ? dx : dx - bBox.width),\r\n        y: bBox.y + dy\r\n    }\r\n};\r\nexport function mergeMarginOptions(opt1, opt2) {\r\n    return {\r\n        checkInterval: opt1.checkInterval || opt2.checkInterval,\r\n        size: _max(opt1.size || 0, opt2.size || 0),\r\n        percentStick: opt1.percentStick || opt2.percentStick,\r\n        sizePointNormalState: _max(opt1.sizePointNormalState || 0, opt2.sizePointNormalState || 0)\r\n    }\r\n}\r\nexport function getVizRangeObject(value) {\r\n    if (Array.isArray(value)) {\r\n        return {\r\n            startValue: value[0],\r\n            endValue: value[1]\r\n        }\r\n    } else {\r\n        return value || {}\r\n    }\r\n}\r\nexport function normalizeArcParams(x, y, innerRadius, outerRadius, startAngle, endAngle) {\r\n    let isCircle;\r\n    let noArc = true;\r\n    const angleDiff = roundValue(endAngle, 3) - roundValue(startAngle, 3);\r\n    if (angleDiff) {\r\n        if (abs(angleDiff) % 360 === 0) {\r\n            startAngle = 0;\r\n            endAngle = 360;\r\n            isCircle = true;\r\n            endAngle -= .01\r\n        }\r\n        if (startAngle > 360) {\r\n            startAngle %= 360\r\n        }\r\n        if (endAngle > 360) {\r\n            endAngle %= 360\r\n        }\r\n        if (startAngle > endAngle) {\r\n            startAngle -= 360\r\n        }\r\n        noArc = false\r\n    }\r\n    startAngle *= PI_DIV_180;\r\n    endAngle *= PI_DIV_180;\r\n    return [x, y, Math.min(outerRadius, innerRadius), Math.max(outerRadius, innerRadius), Math.cos(startAngle), Math.sin(startAngle), Math.cos(endAngle), Math.sin(endAngle), isCircle, floor(abs(endAngle - startAngle) / PI) % 2 ? \"1\" : \"0\", noArc]\r\n}\r\nexport function convertVisualRangeObject(visualRange, convertToVisualRange) {\r\n    if (convertToVisualRange) {\r\n        return visualRange\r\n    }\r\n    return [visualRange.startValue, visualRange.endValue]\r\n}\r\nexport function getAddFunction(range, correctZeroLevel) {\r\n    if (\"datetime\" === range.dataType) {\r\n        return function(rangeValue, marginValue) {\r\n            let sign = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;\r\n            return new Date(rangeValue.getTime() + sign * marginValue)\r\n        }\r\n    }\r\n    if (\"logarithmic\" === range.axisType) {\r\n        return function(rangeValue, marginValue) {\r\n            let sign = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;\r\n            const log = getLogExt(rangeValue, range.base) + sign * marginValue;\r\n            return raiseToExt(log, range.base)\r\n        }\r\n    }\r\n    return function(rangeValue, marginValue) {\r\n        let sign = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : 1;\r\n        const newValue = rangeValue + sign * marginValue;\r\n        return correctZeroLevel && newValue * rangeValue <= 0 ? 0 : newValue\r\n    }\r\n}\r\nexport function adjustVisualRange(options, visualRange, wholeRange, dataRange) {\r\n    const minDefined = isDefined(visualRange.startValue);\r\n    const maxDefined = isDefined(visualRange.endValue);\r\n    const nonDiscrete = \"discrete\" !== options.axisType;\r\n    dataRange = dataRange || wholeRange;\r\n    const add = getAddFunction(options, false);\r\n    let min = minDefined ? visualRange.startValue : dataRange.min;\r\n    let max = maxDefined ? visualRange.endValue : dataRange.max;\r\n    let rangeLength = visualRange.length;\r\n    const categories = dataRange.categories;\r\n    if (nonDiscrete && !isDefined(min) && !isDefined(max)) {\r\n        return {\r\n            startValue: min,\r\n            endValue: max\r\n        }\r\n    }\r\n    if (isDefined(rangeLength)) {\r\n        if (nonDiscrete) {\r\n            if (\"datetime\" === options.dataType && !isNumeric(rangeLength)) {\r\n                rangeLength = dateToMilliseconds(rangeLength)\r\n            }\r\n            if (maxDefined && !minDefined || !maxDefined && !minDefined) {\r\n                isDefined(wholeRange.max) && (max = max > wholeRange.max ? wholeRange.max : max);\r\n                min = add(max, rangeLength, -1)\r\n            } else if (minDefined && !maxDefined) {\r\n                isDefined(wholeRange.min) && (min = min < wholeRange.min ? wholeRange.min : min);\r\n                max = add(min, rangeLength)\r\n            }\r\n        } else {\r\n            rangeLength = parseInt(rangeLength);\r\n            if (!isNaN(rangeLength) && isFinite(rangeLength)) {\r\n                rangeLength--;\r\n                if (!maxDefined && !minDefined) {\r\n                    max = categories[categories.length - 1];\r\n                    min = categories[categories.length - 1 - rangeLength]\r\n                } else if (minDefined && !maxDefined) {\r\n                    const categoriesInfo = getCategoriesInfo(categories, min, void 0);\r\n                    max = categoriesInfo.categories[rangeLength]\r\n                } else if (!minDefined && maxDefined) {\r\n                    const categoriesInfo = getCategoriesInfo(categories, void 0, max);\r\n                    min = categoriesInfo.categories[categoriesInfo.categories.length - 1 - rangeLength]\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (nonDiscrete) {\r\n        if (isDefined(wholeRange.max) && max > wholeRange.max) {\r\n            max = wholeRange.max\r\n        }\r\n        if (isDefined(wholeRange.min) && min < wholeRange.min) {\r\n            min = wholeRange.min\r\n        }\r\n    }\r\n    return {\r\n        startValue: min,\r\n        endValue: max\r\n    }\r\n}\r\nexport function getLogExt(value, base) {\r\n    let allowNegatives = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : false;\r\n    let linearThreshold = arguments.length > 3 ? arguments[3] : void 0;\r\n    if (!allowNegatives) {\r\n        return getLog(value, base)\r\n    }\r\n    if (0 === value) {\r\n        return 0\r\n    }\r\n    const transformValue = getLog(abs(value), base) - (linearThreshold - 1);\r\n    if (transformValue < 0) {\r\n        return 0\r\n    }\r\n    return adjust(sign(value) * transformValue, Number(pow(base, linearThreshold - 1).toFixed(abs(linearThreshold))))\r\n}\r\nexport function raiseToExt(value, base) {\r\n    let allowNegatives = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : false;\r\n    let linearThreshold = arguments.length > 3 ? arguments[3] : void 0;\r\n    if (!allowNegatives) {\r\n        return raiseTo(value, base)\r\n    }\r\n    if (0 === value) {\r\n        return 0\r\n    }\r\n    const transformValue = raiseTo(abs(value) + (linearThreshold - 1), base);\r\n    if (transformValue < 0) {\r\n        return 0\r\n    }\r\n    return adjust(sign(value) * transformValue, Number(pow(base, linearThreshold).toFixed(abs(linearThreshold))))\r\n}\r\nexport function rangesAreEqual(range, rangeFromOptions) {\r\n    if (Array.isArray(rangeFromOptions)) {\r\n        return range.length === rangeFromOptions.length && range.every(((item, i) => valueOf(item) === valueOf(rangeFromOptions[i])))\r\n    } else {\r\n        return valueOf(range.startValue) === valueOf(rangeFromOptions.startValue) && valueOf(range.endValue) === valueOf(rangeFromOptions.endValue)\r\n    }\r\n}\r\nexport function valueOf(value) {\r\n    return value && value.valueOf()\r\n}\r\nexport function pointInCanvas(canvas, x, y) {\r\n    return x >= canvas.left && x <= canvas.right && y >= canvas.top && y <= canvas.bottom\r\n}\r\nexport const getNextDefsSvgId = () => \"DevExpress_\" + numDefsSvgElements++;\r\nexport function extractColor(color, isBase) {\r\n    if (isString(color) || !color) {\r\n        return color\r\n    } else if (isBase) {\r\n        return color.base\r\n    } else {\r\n        return color.fillId || color.base\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACD;AAAA;AAGA;AAAA;AAOA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AACA;;;;;;;;AACA,MAAM,EACF,IAAI,EAAE,EACN,MAAM,IAAI,EACV,KAAK,GAAG,EACR,KAAK,GAAG,EACR,OAAO,KAAK,EACZ,MAAM,IAAI,EACV,KAAK,GAAG,EACR,MAAM,IAAI,EACV,OAAO,KAAK,EACf,GAAG;AACJ,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,SAAS,KAAK,KAAK;AACzB,MAAM,qBAAqB,0JAAA,CAAA,UAAS,CAAC,kBAAkB;AACvD,MAAM,kBAAkB;AACxB,MAAM,aAAa,KAAK;AACxB,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,IAAI,qBAAqB;AAClB,MAAM,eAAe;AACrB,MAAM,SAAS,SAAS,KAAK,EAAE,IAAI;IACtC,IAAI,CAAC,OAAO;QACR,OAAO;IACX;IACA,OAAO,IAAI,SAAS,IAAI;AAC5B;AACO,MAAM,mBAAmB,SAAS,KAAK;IAC1C,OAAO,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,OAAO,OAAO;AAChC;AACO,MAAM,UAAU,SAAS,KAAK,EAAE,IAAI;IACvC,OAAO,IAAI,MAAM;AACrB;AACO,MAAM,iBAAiB,SAAS,KAAK;IACxC,OAAO,CAAC,QAAQ,MAAM,GAAG,IAAI;AACjC;AACO,MAAM,8BAA8B,SAAS,KAAK;IACrD,OAAO,KAAK;AAChB;AACO,MAAM,mBAAmB,SAAS,KAAK;IAC1C,OAAO,KAAK,QAAQ;AACxB;AACO,MAAM,eAAe,SAAS,KAAK;IACtC,MAAM,iBAAiB,iBAAiB;IACxC,OAAO;QACH,KAAK,KAAK;QACV,KAAK,KAAK;IACd;AACJ;AACA,MAAM,0BAA0B;AACzB,MAAM,cAAc,SAAS,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;IAC9C,MAAM,QAAQ,KAAK;IACnB,MAAM,QAAQ,KAAK;IACnB,OAAO,KAAK,QAAQ,QAAQ,QAAQ;AACxC;AACO,MAAM,kBAAkB,SAAS,MAAM;IAC1C,IAAI,IAAI,IAAI;IACZ,IAAI;IACJ,IAAI,CAAC,OAAO,IAAI;QACZ,IAAI,IAAI,GAAG;YACP,IAAI,IAAI,KAAK;YACb,KAAK,KAAK;YACV,OAAO,KAAK,IAAI,QAAQ,KAAK,MAAM;QACvC;QACA,OAAO;IACX;IACA,OAAO;AACX;AACO,MAAM,uBAAuB,SAAS,KAAK,EAAE,GAAG,EAAE,KAAK;IAC1D,MAAM,QAAQ,KAAK,gBAAgB,QAAQ,gBAAgB;IAC3D,IAAI,YAAY,CAAC,gBAAgB,IAAI,MAAM,SAAS;IACpD,IAAI;IACJ,IAAI,CAAC,OAAO,UAAU,CAAC,OAAO,YAAY;QACtC,IAAI,IAAI,UAAU,GAAG;YACjB,SAAS;YACT,YAAY,KAAK,CAAC,YAAY,CAAC;YAC/B,YAAY,KAAK,CAAC,YAAY,CAAC;QACnC,OAAO;YACH,SAAS;YACT,aAAa,QAAQ;YACrB,YAAY,KAAK,CAAC,YAAY,CAAC;QACnC;QACA,OAAO;YACH,MAAM;YACN,WAAW;QACf;IACJ;IACA,OAAO;AACX;AACO,MAAM,aAAa,SAAS,KAAK,EAAE,SAAS;IAC/C,IAAI,YAAY,IAAI;QAChB,YAAY;IAChB;IACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;QAClB,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ;YACtB,OAAO,QAAQ,MAAM,aAAa,CAAC;QACvC,OAAO;YACH,OAAO,QAAQ,MAAM,OAAO,CAAC;QACjC;IACJ;AACJ;AACO,MAAM,WAAW,SAAS,KAAK;IAClC,OAAO,MAAM,aAAa,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;AAC9C;AACO,SAAS,IAAI,KAAK,EAAE,QAAQ;IAC/B,IAAI,IAAI;IACR,MAAM,MAAM,MAAM,MAAM;IACxB,MAAM,SAAS,EAAE;IACjB,IAAI;IACJ,MAAO,IAAI,IAAK;QACZ,QAAQ,SAAS,KAAK,CAAC,EAAE,EAAE;QAC3B,IAAI,SAAS,OAAO;YAChB,OAAO,IAAI,CAAC;QAChB;QACA;IACJ;IACA,OAAO;AACX;AAEA,SAAS,aAAa,MAAM,EAAE,IAAI;IAC9B,OAAO,IAAI,MAAO,CAAA,MAAO,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI,GAAG;AACzD;AAEA,SAAS,eAAe,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ;IACxD,IAAI,MAAM;IACV,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,MAAO,CAAC,GAAG;QACZ,IAAI,MAAM,CAAC,IAAI,EAAE;YACb,MAAM,CAAC,IAAI,IAAI;YACf,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACO,SAAS,cAAc,KAAK;IAC/B,OAAO,OAAO,OAAO,WAAW;AACpC;AACO,SAAS,gBAAgB,MAAM;IAClC,IAAI,QAAQ;QACR,OAAO,WAAW,GAAG,OAAO,GAAG;QAC/B,OAAO,cAAc,GAAG,OAAO,MAAM;QACrC,OAAO,YAAY,GAAG,OAAO,IAAI;QACjC,OAAO,aAAa,GAAG,OAAO,KAAK;IACvC;IACA,OAAO;AACX;AAEA,SAAS,mBAAmB,KAAK;IAC7B,OAAO,CAAC,OAAO,SAAS,QAAQ,OAAO,QAAQ;AACnD;AACO,SAAS,cAAc,IAAI;IAC9B,MAAM,KAAK,mBAAmB,MAAM,KAAK,CAAC;IAC1C,MAAM,KAAK,mBAAmB,MAAM,KAAK,CAAC;IAC1C,MAAM,KAAK,mBAAmB,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC;IACtD,MAAM,KAAK,mBAAmB,KAAK,KAAK,MAAM,GAAG,KAAK,CAAC;IACvD,MAAM,SAAS;QACX,GAAG;QACH,GAAG;QACH,OAAO,KAAK;QACZ,QAAQ,KAAK;IACjB;IACA,OAAO,OAAO,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,KAAK,IAAI,CAAC,OAAO,MAAM;IAC1E,OAAO;AACX;AACO,SAAS,WAAW,IAAI,EAAE,MAAM,EAAE,KAAK;IAC1C,MAAM,MAAM,QAAQ,KAAK,QAAQ,YAAY,OAAO,CAAC;IACrD,MAAM,MAAM,QAAQ,KAAK,QAAQ,YAAY,OAAO,CAAC;IACrD,MAAM,KAAK,KAAK,KAAK,GAAG;IACxB,MAAM,KAAK,KAAK,MAAM,GAAG;IACzB,MAAM,UAAU,KAAK,CAAC,GAAG;IACzB,MAAM,UAAU,KAAK,CAAC,GAAG;IACzB,MAAM,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK;IACrC,MAAM,MAAM,IAAI,KAAK,OAAO,IAAI,KAAK;IACrC,MAAM,WAAW,MAAM,CAAC,EAAE,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,UAAU,MAAM,CAAC,EAAE,IAAI;IACnF,MAAM,WAAW,MAAM,CAAC,EAAE,GAAG,CAAC,UAAU,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,UAAU,MAAM,CAAC,EAAE,IAAI;IACnF,OAAO,cAAc;QACjB,GAAG,WAAW;QACd,GAAG,WAAW;QACd,OAAO,IAAI;QACX,QAAQ,IAAI;IAChB;AACJ;AACO,MAAM,eAAe,SAAS,MAAM,EAAE,IAAI,EAAE,QAAQ;IACvD,IAAI;IACJ,GAAG;QACC,YAAY,aAAa,QAAQ;QACjC,UAAU,IAAI,CAAC,KAAK,WAAW,UAAU,MAAM;QAC/C,WAAW,eAAe,QAAQ,MAAM,KAAK,KAAK,CAAC,MAAM,YAAY;IACzE,QAAS,WAAW,KAAK,UAAU,MAAM,GAAG,EAAG;IAC/C,OAAO;AACX;AACO,MAAM,cAAc,SAAS,KAAK,EAAE,YAAY;IACnD,OAAO,KAAK,MAAM,QAAQ,QAAQ;AACtC;AACO,MAAM,aAAa,SAAS,MAAM;IACrC,MAAM,SAAS,CAAC;IAChB,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;QACzC,MAAM,CAAC,cAAc,MAAM,CAAC,EAAE,EAAE,GAAG;IACvC;IACA,OAAO,SAAS,KAAK,EAAE,YAAY;QAC/B,MAAM,SAAS,cAAc;QAC7B,OAAO,MAAM,CAAC,OAAO,GAAG,SAAS;IACrC;AACJ;AACO,MAAM,mBAAmB,SAAS,OAAO;IAC5C,MAAM,cAAc,CAAC;IACrB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,WAAW,CAAC,GAAI,SAAS,GAAG,EAAE,KAAK;QACpC,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,OAAO,IAAI,cAAc,KAAK;YACxD,QAAQ;QACZ,OAAO,IAAI,YAAY,KAAK;YACxB,MAAM;YACN,IAAI,aAAa,SAAS;gBACtB,MAAM,QAAQ,IAAI,0IAAA,CAAA,UAAK,CAAC;gBACxB,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,CAAC,CAAC;YACvE;QACJ,OAAO;YACH,MAAM,UAAU;QACpB;QACA,WAAW,CAAC,IAAI,GAAG;IACvB;IACA,OAAO;AACX;AACO,SAAS,iBAAiB,YAAY,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM;IACpE,MAAM,mBAAmB,SAAS,IAAI,SAAS;IAC/C,QAAQ,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,aAAa,KAAK;IACrD,MAAM,SAAS,aAAa;IAC5B,OAAO;QACH,GAAG,OAAO,aAAa,CAAC,GAAG,mBAAmB,OAAO,GAAG;QACxD,GAAG,OAAO,aAAa,CAAC,GAAG,mBAAmB,OAAO,GAAG;IAC5D;AACJ;AACO,MAAM,mBAAmB,SAAS,YAAY,EAAE,CAAC,EAAE,CAAC;IACvD,MAAM,SAAS,YAAY,aAAa,CAAC,EAAE,aAAa,CAAC,EAAE,GAAG;IAC9D,MAAM,QAAQ,MAAM,IAAI,aAAa,CAAC,EAAE,IAAI,aAAa,CAAC;IAC1D,OAAO;QACH,KAAK,OAAO,eAAe,MAAM,QAAQ;QACzC,GAAG,OAAO;IACd;AACJ;AACO,MAAM,wBAAwB,SAAS,cAAc,EAAE,KAAK;IAC/D,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,eAAe,eAAe,IAAI,eAAe,eAAe,GAAG,+KAAA,CAAA,OAAI;IAC1G,MAAM,YAAY,eAAe,SAAS;IAC1C,MAAM,kBAAkB,CAAC;IACzB,MAAM,cAAc,EAAE;IACtB,IAAI;IACJ,IAAI,IAAI;IACR,IAAI;IACJ,IAAI;IACJ,QAAQ,SAAS,EAAE;IACnB,IAAK,SAAS,MAAM,MAAM,EAAE,IAAI,QAAQ,IAAK;QACzC,OAAO,KAAK,CAAC,EAAE;QACf,IAAI,aAAa,MAAM;YACnB,SAAS,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC;YACzC,IAAI,CAAC,QAAQ;gBACT,SAAS,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG;oBACxC,MAAM,IAAI,CAAC,UAAU;oBACrB,gBAAgB,IAAI,CAAC,UAAU;gBACnC;gBACA,YAAY,IAAI,CAAC,OAAO,IAAI;YAChC;QACJ;IACJ;IACA,OAAO,IAAI,aAAc,SAAS,WAAW;QACzC,MAAM,QAAQ,eAAe,CAAC,YAAY;QAC1C,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,OAAO,gBAAgB,IAAI,CAAC,MAAM,MAAM,IAAI;IAC9D;AACJ;AACO,MAAM,oBAAoB,SAAS,UAAU,EAAE,UAAU,EAAE,QAAQ;IACtE,IAAI,MAAM,WAAW,MAAM,EAAE;QACzB,OAAO;YACH,YAAY,EAAE;QAClB;IACJ;IACA,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,aAAa,UAAU,CAAC,EAAE;IAC/D,WAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,WAAW,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;IAC7E,MAAM,kBAAkB,IAAI,YAAa,CAAA,WAAY,SAAS,YAAY,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,OAAO;IACzH,IAAI,kBAAkB,gBAAgB,OAAO,CAAC,WAAW,OAAO;IAChE,IAAI,gBAAgB,gBAAgB,OAAO,CAAC,SAAS,OAAO;IAC5D,IAAI;IACJ,IAAI,WAAW;IACf,kBAAkB,KAAK,CAAC,kBAAkB,CAAC;IAC3C,gBAAgB,KAAK,CAAC,gBAAgB,WAAW,MAAM,GAAG,CAAC;IAC3D,IAAI,gBAAgB,iBAAiB;QACjC,UAAU;QACV,gBAAgB;QAChB,kBAAkB;QAClB,WAAW;IACf;IACA,MAAM,oBAAoB,WAAW,KAAK,CAAC,iBAAiB,gBAAgB;IAC5E,MAAM,UAAU,kBAAkB,MAAM,GAAG;IAC3C,OAAO;QACH,YAAY;QACZ,OAAO,iBAAiB,CAAC,WAAW,UAAU,EAAE;QAChD,KAAK,iBAAiB,CAAC,WAAW,IAAI,QAAQ;QAC9C,UAAU;IACd;AACJ;AACO,SAAS,qBAAqB,IAAI;IACrC,OAAO,CAAC,CAAC,KAAK,IAAI,GAAG,CAAC;AAC1B;AACO,SAAS,qBAAqB,KAAK;IACtC,MAAM,OAAO,CAAE,CAAA;QACX,MAAM,SAAS,KAAK,MAAM;QAC1B,IAAI,OAAO;QACX,IAAI,eAAe,WAAW,WAAW,KAAK;QAC9C,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,SAAS,GAAG;YAClF,eAAe,OAAO;YACtB,OAAO;QACX;QACA,IAAI,CAAC,QAAQ,cAAc;YACvB,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,WAAW,OAAO,OAAO,CAAC,OAAO,CAAC,GAAG;gBAC9C,gBAAgB;gBAChB,OAAO;YACX,OAAO,IAAI,eAAe,GAAG;gBACzB,eAAe,eAAe,CAAC,IAAI,IAAI,IAAI;YAC/C;QACJ;QACA,KAAK,MAAM,GAAG;QACd,KAAK,IAAI,GAAG;IAChB;IACA,MAAM,sBAAsB,MAAM,MAAM,CAAC;IACzC,MAAM,YAAY,oBAAoB,MAAM,CAAE,CAAC,MAAM,OAAS,OAAO,CAAC,KAAK,MAAM,IAAI,CAAC,GAAI;IAC1F,MAAM,oBAAoB,oBAAoB,MAAM;IACpD,MAAM,mBAAmB,oBAAoB,MAAM,CAAE,CAAA,OAAQ,CAAC,KAAK,MAAM;IACzE,MAAM,mBAAmB,iBAAiB,MAAM;IAChD,IAAI,YAAY,KAAK,kBAAkB;QACnC,iBAAiB,OAAO,CAAE,CAAA,OAAQ,KAAK,MAAM,GAAG,CAAC,IAAI,SAAS,IAAI;IACtE,OAAO,IAAI,YAAY,KAAK,YAAY,KAAK,CAAC,oBAAoB,MAAM,aAAa,kBAAkB;QACnG,IAAI,kBAAkB;YAClB,MAAM,iBAAiB,YAAY;YACnC,MAAM,iBAAiB,mBAAmB;YAC1C,oBAAoB,MAAM,CAAE,CAAA,OAAQ,KAAK,MAAM,EAAG,OAAO,CAAE,CAAA,OAAQ,KAAK,MAAM,IAAI,CAAC,YAAY,cAAc,IAAI;YACjH,iBAAiB,OAAO,CAAE,CAAA,OAAQ,KAAK,MAAM,GAAG;QACpD;QACA,oBAAoB,OAAO,CAAE,CAAA,OAAQ,KAAK,MAAM,IAAI,IAAI;IAC5D;AACJ;AACO,SAAS,oBAAoB,KAAK,EAAE,MAAM,EAAE,OAAO;IACtD,IAAI,mBAAmB;IACvB,MAAM,YAAY,UAAU,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK,GAAG,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM;IAClH,MAAM,mBAAmB,MAAM,MAAM,CAAE,CAAC,MAAM,MAAQ,OAAO,CAAC,CAAC,qBAAqB,OAAO,IAAI,MAAM,GAAG,CAAC,GAAI;IAC7G,MAAM,cAAc,YAAY,KAAK,CAAC,MAAM,MAAM,GAAG,CAAC,IAAI;IAC1D,MAAM,YAAY,UAAU,SAAS;IACrC,MAAM,UAAU,UAAU,UAAU;IACpC,MAAM,OAAO,CAAE,CAAA;QACX,MAAM,aAAa,CAAC,qBAAqB,QAAQ,KAAK,MAAM,GAAG,OAAO,KAAK,MAAM,GAAG;QACpF,KAAK,MAAM,GAAG,KAAK,MAAM,IAAI,CAAC;QAC9B,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,KAAK,MAAM,EAAE;QACpB,KAAK,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,GAAG;QAC7C,KAAK,MAAM,CAAC,QAAQ,GAAG,MAAM,CAAC,QAAQ,GAAG,CAAC,YAAY,aAAa,gBAAgB;QACnF,mBAAmB,mBAAmB,aAAa;QACnD,gBAAgB,KAAK,MAAM;IAC/B;AACJ;AACO,MAAM,SAAS,SAAS,KAAK;IAChC,MAAM,SAAS,CAAC;IAChB,OAAO,IAAI,OAAQ,SAAS,IAAI;QAC5B,MAAM,SAAS,CAAC,MAAM,CAAC,KAAK,GAAG,OAAO;QACtC,MAAM,CAAC,KAAK,GAAG;QACf,OAAO;IACX;AACJ;AACO,MAAM,oCAAoC,SAAS,IAAI,EAAE,EAAE,EAAE,MAAM;IACtE,MAAM,aAAa,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,OAAO,CAAC;IACtD,MAAM,oBAAoB,CAAC,aAAa,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,KAAK,IAAI,OAAO,CAAC;IAChF,MAAM,kBAAkB,KAAK,CAAC,GAAG,OAAO,CAAC;IACzC,MAAM,kBAAkB,kBAAkB;IAC1C,MAAM,oBAAoB,OAAO,KAAK,oBAAoB,oBAAoB,kBAAkB,kBAAkB,kBAAkB;IACpI,MAAM,KAAK,CAAC,aAAa,CAAC,oBAAoB,CAAC,iBAAiB,KAAK;IACrE,OAAO;QACH,GAAG,OAAO,CAAC,GAAG,CAAC,aAAa,KAAK,KAAK,KAAK,KAAK;QAChD,GAAG,KAAK,CAAC,GAAG;IAChB;AACJ;AACO,SAAS,mBAAmB,IAAI,EAAE,IAAI;IACzC,OAAO;QACH,eAAe,KAAK,aAAa,IAAI,KAAK,aAAa;QACvD,MAAM,KAAK,KAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IAAI;QACxC,cAAc,KAAK,YAAY,IAAI,KAAK,YAAY;QACpD,sBAAsB,KAAK,KAAK,oBAAoB,IAAI,GAAG,KAAK,oBAAoB,IAAI;IAC5F;AACJ;AACO,SAAS,kBAAkB,KAAK;IACnC,IAAI,MAAM,OAAO,CAAC,QAAQ;QACtB,OAAO;YACH,YAAY,KAAK,CAAC,EAAE;YACpB,UAAU,KAAK,CAAC,EAAE;QACtB;IACJ,OAAO;QACH,OAAO,SAAS,CAAC;IACrB;AACJ;AACO,SAAS,mBAAmB,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ;IACnF,IAAI;IACJ,IAAI,QAAQ;IACZ,MAAM,YAAY,WAAW,UAAU,KAAK,WAAW,YAAY;IACnE,IAAI,WAAW;QACX,IAAI,IAAI,aAAa,QAAQ,GAAG;YAC5B,aAAa;YACb,WAAW;YACX,WAAW;YACX,YAAY;QAChB;QACA,IAAI,aAAa,KAAK;YAClB,cAAc;QAClB;QACA,IAAI,WAAW,KAAK;YAChB,YAAY;QAChB;QACA,IAAI,aAAa,UAAU;YACvB,cAAc;QAClB;QACA,QAAQ;IACZ;IACA,cAAc;IACd,YAAY;IACZ,OAAO;QAAC;QAAG;QAAG,KAAK,GAAG,CAAC,aAAa;QAAc,KAAK,GAAG,CAAC,aAAa;QAAc,KAAK,GAAG,CAAC;QAAa,KAAK,GAAG,CAAC;QAAa,KAAK,GAAG,CAAC;QAAW,KAAK,GAAG,CAAC;QAAW;QAAU,MAAM,IAAI,WAAW,cAAc,MAAM,IAAI,MAAM;QAAK;KAAM;AACtP;AACO,SAAS,yBAAyB,WAAW,EAAE,oBAAoB;IACtE,IAAI,sBAAsB;QACtB,OAAO;IACX;IACA,OAAO;QAAC,YAAY,UAAU;QAAE,YAAY,QAAQ;KAAC;AACzD;AACO,SAAS,eAAe,KAAK,EAAE,gBAAgB;IAClD,IAAI,eAAe,MAAM,QAAQ,EAAE;QAC/B,OAAO,SAAS,UAAU,EAAE,WAAW;YACnC,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;YAC5E,OAAO,IAAI,KAAK,WAAW,OAAO,KAAK,OAAO;QAClD;IACJ;IACA,IAAI,kBAAkB,MAAM,QAAQ,EAAE;QAClC,OAAO,SAAS,UAAU,EAAE,WAAW;YACnC,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;YAC5E,MAAM,MAAM,UAAU,YAAY,MAAM,IAAI,IAAI,OAAO;YACvD,OAAO,WAAW,KAAK,MAAM,IAAI;QACrC;IACJ;IACA,OAAO,SAAS,UAAU,EAAE,WAAW;QACnC,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QAC5E,MAAM,WAAW,aAAa,OAAO;QACrC,OAAO,oBAAoB,WAAW,cAAc,IAAI,IAAI;IAChE;AACJ;AACO,SAAS,kBAAkB,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,SAAS;IACzE,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,UAAU;IACnD,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,QAAQ;IACjD,MAAM,cAAc,eAAe,QAAQ,QAAQ;IACnD,YAAY,aAAa;IACzB,MAAM,MAAM,eAAe,SAAS;IACpC,IAAI,MAAM,aAAa,YAAY,UAAU,GAAG,UAAU,GAAG;IAC7D,IAAI,MAAM,aAAa,YAAY,QAAQ,GAAG,UAAU,GAAG;IAC3D,IAAI,cAAc,YAAY,MAAM;IACpC,MAAM,aAAa,UAAU,UAAU;IACvC,IAAI,eAAe,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM;QACnD,OAAO;YACH,YAAY;YACZ,UAAU;QACd;IACJ;IACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;QACxB,IAAI,aAAa;YACb,IAAI,eAAe,QAAQ,QAAQ,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;gBAC5D,cAAc,mBAAmB;YACrC;YACA,IAAI,cAAc,CAAC,cAAc,CAAC,cAAc,CAAC,YAAY;gBACzD,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,KAAK,CAAC,MAAM,MAAM,WAAW,GAAG,GAAG,WAAW,GAAG,GAAG,GAAG;gBAC/E,MAAM,IAAI,KAAK,aAAa,CAAC;YACjC,OAAO,IAAI,cAAc,CAAC,YAAY;gBAClC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,KAAK,CAAC,MAAM,MAAM,WAAW,GAAG,GAAG,WAAW,GAAG,GAAG,GAAG;gBAC/E,MAAM,IAAI,KAAK;YACnB;QACJ,OAAO;YACH,cAAc,SAAS;YACvB,IAAI,CAAC,MAAM,gBAAgB,SAAS,cAAc;gBAC9C;gBACA,IAAI,CAAC,cAAc,CAAC,YAAY;oBAC5B,MAAM,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;oBACvC,MAAM,UAAU,CAAC,WAAW,MAAM,GAAG,IAAI,YAAY;gBACzD,OAAO,IAAI,cAAc,CAAC,YAAY;oBAClC,MAAM,iBAAiB,kBAAkB,YAAY,KAAK,KAAK;oBAC/D,MAAM,eAAe,UAAU,CAAC,YAAY;gBAChD,OAAO,IAAI,CAAC,cAAc,YAAY;oBAClC,MAAM,iBAAiB,kBAAkB,YAAY,KAAK,GAAG;oBAC7D,MAAM,eAAe,UAAU,CAAC,eAAe,UAAU,CAAC,MAAM,GAAG,IAAI,YAAY;gBACvF;YACJ;QACJ;IACJ;IACA,IAAI,aAAa;QACb,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,KAAK,MAAM,WAAW,GAAG,EAAE;YACnD,MAAM,WAAW,GAAG;QACxB;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW,GAAG,KAAK,MAAM,WAAW,GAAG,EAAE;YACnD,MAAM,WAAW,GAAG;QACxB;IACJ;IACA,OAAO;QACH,YAAY;QACZ,UAAU;IACd;AACJ;AACO,SAAS,UAAU,KAAK,EAAE,IAAI;IACjC,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACtF,IAAI,kBAAkB,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;IACjE,IAAI,CAAC,gBAAgB;QACjB,OAAO,OAAO,OAAO;IACzB;IACA,IAAI,MAAM,OAAO;QACb,OAAO;IACX;IACA,MAAM,iBAAiB,OAAO,IAAI,QAAQ,QAAQ,CAAC,kBAAkB,CAAC;IACtE,IAAI,iBAAiB,GAAG;QACpB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,SAAS,gBAAgB,OAAO,IAAI,MAAM,kBAAkB,GAAG,OAAO,CAAC,IAAI;AAClG;AACO,SAAS,WAAW,KAAK,EAAE,IAAI;IAClC,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACtF,IAAI,kBAAkB,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;IACjE,IAAI,CAAC,gBAAgB;QACjB,OAAO,QAAQ,OAAO;IAC1B;IACA,IAAI,MAAM,OAAO;QACb,OAAO;IACX;IACA,MAAM,iBAAiB,QAAQ,IAAI,SAAS,CAAC,kBAAkB,CAAC,GAAG;IACnE,IAAI,iBAAiB,GAAG;QACpB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,SAAS,gBAAgB,OAAO,IAAI,MAAM,iBAAiB,OAAO,CAAC,IAAI;AAC9F;AACO,SAAS,eAAe,KAAK,EAAE,gBAAgB;IAClD,IAAI,MAAM,OAAO,CAAC,mBAAmB;QACjC,OAAO,MAAM,MAAM,KAAK,iBAAiB,MAAM,IAAI,MAAM,KAAK,CAAE,CAAC,MAAM,IAAM,QAAQ,UAAU,QAAQ,gBAAgB,CAAC,EAAE;IAC9H,OAAO;QACH,OAAO,QAAQ,MAAM,UAAU,MAAM,QAAQ,iBAAiB,UAAU,KAAK,QAAQ,MAAM,QAAQ,MAAM,QAAQ,iBAAiB,QAAQ;IAC9I;AACJ;AACO,SAAS,QAAQ,KAAK;IACzB,OAAO,SAAS,MAAM,OAAO;AACjC;AACO,SAAS,cAAc,MAAM,EAAE,CAAC,EAAE,CAAC;IACtC,OAAO,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,OAAO,MAAM;AACzF;AACO,MAAM,mBAAmB,IAAM,gBAAgB;AAC/C,SAAS,aAAa,KAAK,EAAE,MAAM;IACtC,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,CAAC,OAAO;QAC3B,OAAO;IACX,OAAO,IAAI,QAAQ;QACf,OAAO,MAAM,IAAI;IACrB,OAAO;QACH,OAAO,MAAM,MAAM,IAAI,MAAM,IAAI;IACrC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 642, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/layout_element.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/layout_element.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nconst _round = Math.round;\r\nimport {\r\n    clone\r\n} from \"../../core/utils/object\";\r\nconst defaultOffset = {\r\n    horizontal: 0,\r\n    vertical: 0\r\n};\r\nconst alignFactors = {\r\n    center: .5,\r\n    right: 1,\r\n    bottom: 1,\r\n    left: 0,\r\n    top: 0\r\n};\r\n\r\nfunction LayoutElement(options) {\r\n    this._options = options\r\n}\r\nLayoutElement.prototype = {\r\n    constructor: LayoutElement,\r\n    position: function(options) {\r\n        const ofBBox = options.of.getLayoutOptions();\r\n        const myBBox = this.getLayoutOptions();\r\n        const at = options.at;\r\n        const my = options.my;\r\n        const offset = options.offset || defaultOffset;\r\n        const shiftX = -alignFactors[my.horizontal] * myBBox.width + ofBBox.x + alignFactors[at.horizontal] * ofBBox.width + parseInt(offset.horizontal);\r\n        const shiftY = -alignFactors[my.vertical] * myBBox.height + ofBBox.y + alignFactors[at.vertical] * ofBBox.height + parseInt(offset.vertical);\r\n        this.shift(_round(shiftX), _round(shiftY))\r\n    },\r\n    getLayoutOptions: noop\r\n};\r\n\r\nfunction WrapperLayoutElement(renderElement, bBox) {\r\n    this._renderElement = renderElement;\r\n    this._cacheBBox = bBox\r\n}\r\nconst wrapperLayoutElementPrototype = WrapperLayoutElement.prototype = clone(LayoutElement.prototype);\r\nwrapperLayoutElementPrototype.constructor = WrapperLayoutElement;\r\nwrapperLayoutElementPrototype.getLayoutOptions = function() {\r\n    return this._cacheBBox || this._renderElement.getBBox()\r\n};\r\nwrapperLayoutElementPrototype.shift = function(shiftX, shiftY) {\r\n    const bBox = this.getLayoutOptions();\r\n    this._renderElement.move(_round(shiftX - bBox.x), _round(shiftY - bBox.y))\r\n};\r\nexport {\r\n    LayoutElement,\r\n    WrapperLayoutElement\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAIA;AAAA;;AADA,MAAM,SAAS,KAAK,KAAK;;AAIzB,MAAM,gBAAgB;IAClB,YAAY;IACZ,UAAU;AACd;AACA,MAAM,eAAe;IACjB,QAAQ;IACR,OAAO;IACP,QAAQ;IACR,MAAM;IACN,KAAK;AACT;AAEA,SAAS,cAAc,OAAO;IAC1B,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA,cAAc,SAAS,GAAG;IACtB,aAAa;IACb,UAAU,SAAS,OAAO;QACtB,MAAM,SAAS,QAAQ,EAAE,CAAC,gBAAgB;QAC1C,MAAM,SAAS,IAAI,CAAC,gBAAgB;QACpC,MAAM,KAAK,QAAQ,EAAE;QACrB,MAAM,KAAK,QAAQ,EAAE;QACrB,MAAM,SAAS,QAAQ,MAAM,IAAI;QACjC,MAAM,SAAS,CAAC,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,KAAK,GAAG,OAAO,CAAC,GAAG,YAAY,CAAC,GAAG,UAAU,CAAC,GAAG,OAAO,KAAK,GAAG,SAAS,OAAO,UAAU;QAC/I,MAAM,SAAS,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,OAAO,MAAM,GAAG,OAAO,CAAC,GAAG,YAAY,CAAC,GAAG,QAAQ,CAAC,GAAG,OAAO,MAAM,GAAG,SAAS,OAAO,QAAQ;QAC3I,IAAI,CAAC,KAAK,CAAC,OAAO,SAAS,OAAO;IACtC;IACA,kBAAkB,+KAAA,CAAA,OAAI;AAC1B;AAEA,SAAS,qBAAqB,aAAa,EAAE,IAAI;IAC7C,IAAI,CAAC,cAAc,GAAG;IACtB,IAAI,CAAC,UAAU,GAAG;AACtB;AACA,MAAM,gCAAgC,qBAAqB,SAAS,GAAG,CAAA,GAAA,+KAAA,CAAA,QAAK,AAAD,EAAE,cAAc,SAAS;AACpG,8BAA8B,WAAW,GAAG;AAC5C,8BAA8B,gBAAgB,GAAG;IAC7C,OAAO,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,OAAO;AACzD;AACA,8BAA8B,KAAK,GAAG,SAAS,MAAM,EAAE,MAAM;IACzD,MAAM,OAAO,IAAI,CAAC,gBAAgB;IAClC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,SAAS,KAAK,CAAC,GAAG,OAAO,SAAS,KAAK,CAAC;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/renderers/animation.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/renderers/animation.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    requestAnimationFrame,\r\n    cancelAnimationFrame\r\n} from \"../../../common/core/animation/frame\";\r\nconst noop = function() {};\r\nexport const easingFunctions = {\r\n    easeOutCubic: function(pos, start, end) {\r\n        return 1 === pos ? end : (1 - Math.pow(1 - pos, 3)) * (end - start) + +start\r\n    },\r\n    linear: function(pos, start, end) {\r\n        return 1 === pos ? end : pos * (end - start) + +start\r\n    }\r\n};\r\nexport const animationSvgStep = {\r\n    segments: function(elem, params, progress, easing, currentParams) {\r\n        const from = params.from;\r\n        const to = params.to;\r\n        let curSeg;\r\n        let seg;\r\n        let i;\r\n        let j;\r\n        const segments = [];\r\n        for (i = 0; i < from.length; i++) {\r\n            curSeg = from[i];\r\n            seg = [curSeg[0]];\r\n            if (curSeg.length > 1) {\r\n                for (j = 1; j < curSeg.length; j++) {\r\n                    seg.push(easing(progress, curSeg[j], to[i][j]))\r\n                }\r\n            }\r\n            segments.push(seg)\r\n        }\r\n        currentParams.segments = params.end && 1 === progress ? params.end : segments;\r\n        elem.attr({\r\n            segments: segments\r\n        })\r\n    },\r\n    arc: function(elem, params, progress, easing) {\r\n        const from = params.from;\r\n        const to = params.to;\r\n        const current = {};\r\n        for (const i in from) {\r\n            current[i] = easing(progress, from[i], to[i])\r\n        }\r\n        elem.attr(current)\r\n    },\r\n    transform: function(elem, params, progress, easing, currentParams) {\r\n        const from = params.from;\r\n        const to = params.to;\r\n        const current = {};\r\n        for (const i in from) {\r\n            current[i] = currentParams[i] = easing(progress, from[i], to[i])\r\n        }\r\n        elem.attr(current)\r\n    },\r\n    base: function(elem, params, progress, easing, currentParams, attributeName) {\r\n        const obj = {};\r\n        obj[attributeName] = currentParams[attributeName] = easing(progress, params.from, params.to);\r\n        elem.attr(obj)\r\n    },\r\n    _: noop,\r\n    complete: function(element, currentSettings) {\r\n        element.attr(currentSettings)\r\n    }\r\n};\r\n\r\nfunction step(now) {\r\n    const that = this;\r\n    const animateStep = that._animateStep;\r\n    let attrName;\r\n    that._progress = that._calcProgress(now);\r\n    for (attrName in that.params) {\r\n        const anim = animateStep[attrName] || animateStep.base;\r\n        anim(that.element, that.params[attrName], that._progress, that._easing, that._currentParams, attrName)\r\n    }\r\n    that.options.step && that.options.step(that._easing(that._progress, 0, 1), that._progress);\r\n    if (1 === that._progress) {\r\n        return that.stop()\r\n    }\r\n    return true\r\n}\r\n\r\nfunction delayTick(now) {\r\n    if (now - this._startTime >= this.delay) {\r\n        this.tick = step\r\n    }\r\n    return true\r\n}\r\n\r\nfunction start(now) {\r\n    this._startTime = now;\r\n    this.tick = this.delay ? delayTick : step;\r\n    return true\r\n}\r\n\r\nfunction Animation(element, params, options) {\r\n    this._progress = 0;\r\n    this.element = element;\r\n    this.params = params;\r\n    this.options = options;\r\n    this.duration = options.partitionDuration ? options.duration * options.partitionDuration : options.duration;\r\n    this.delay = options.delay && options.duration * options.delay || 0;\r\n    this._animateStep = options.animateStep || animationSvgStep;\r\n    this._easing = easingFunctions[options.easing] || easingFunctions.easeOutCubic;\r\n    this._currentParams = {};\r\n    this.tick = start\r\n}\r\nAnimation.prototype = {\r\n    _calcProgress: function(now) {\r\n        return Math.min(1, (now - this.delay - this._startTime) / this.duration)\r\n    },\r\n    stop: function(disableComplete) {\r\n        const options = this.options;\r\n        const animateStep = this._animateStep;\r\n        this.stop = this.tick = noop;\r\n        animateStep.complete && animateStep.complete(this.element, this._currentParams);\r\n        options.complete && !disableComplete && options.complete()\r\n    }\r\n};\r\nexport function AnimationController(element) {\r\n    this._animationCount = 0;\r\n    this._timerId = null;\r\n    this._animations = {};\r\n    this.element = element\r\n}\r\nAnimationController.prototype = {\r\n    _loop: function() {\r\n        const that = this;\r\n        const animations = that._animations;\r\n        let activeAnimation = 0;\r\n        const now = (new Date).getTime();\r\n        let an;\r\n        const endAnimation = that._endAnimation;\r\n        for (an in animations) {\r\n            if (!animations[an].tick(now)) {\r\n                delete animations[an]\r\n            }\r\n            activeAnimation++\r\n        }\r\n        if (0 === activeAnimation) {\r\n            that.stop();\r\n            that._endAnimationTimer = endAnimation && setTimeout((function() {\r\n                if (0 === that._animationCount) {\r\n                    endAnimation();\r\n                    that._endAnimation = null\r\n                }\r\n            }));\r\n            return\r\n        }\r\n        that._timerId = requestAnimationFrame.call(null, (function() {\r\n            that._loop()\r\n        }), that.element)\r\n    },\r\n    addAnimation: function(animation) {\r\n        const that = this;\r\n        that._animations[that._animationCount++] = animation;\r\n        clearTimeout(that._endAnimationTimer);\r\n        if (!that._timerId) {\r\n            clearTimeout(that._startDelay);\r\n            that._startDelay = setTimeout((function() {\r\n                that._timerId = 1;\r\n                that._loop()\r\n            }), 0)\r\n        }\r\n    },\r\n    animateElement: function(elem, params, options) {\r\n        if (elem && params && options) {\r\n            elem.animation && elem.animation.stop();\r\n            this.addAnimation(elem.animation = new Animation(elem, params, options))\r\n        }\r\n    },\r\n    onEndAnimation: function(endAnimation) {\r\n        this._animationCount ? this._endAnimation = endAnimation : endAnimation()\r\n    },\r\n    dispose: function() {\r\n        this.stop();\r\n        this.element = null\r\n    },\r\n    stop: function() {\r\n        this._animations = {};\r\n        this._animationCount = 0;\r\n        cancelAnimationFrame(this._timerId);\r\n        clearTimeout(this._startDelay);\r\n        clearTimeout(this._endAnimationTimer);\r\n        this._timerId = null\r\n    },\r\n    lock: function() {\r\n        let an;\r\n        const animations = this._animations;\r\n        let unstoppable;\r\n        let hasUnstoppableInAnimations;\r\n        for (an in animations) {\r\n            unstoppable = animations[an].options.unstoppable;\r\n            hasUnstoppableInAnimations = hasUnstoppableInAnimations || unstoppable;\r\n            if (!unstoppable) {\r\n                animations[an].stop(true);\r\n                delete animations[an]\r\n            }\r\n        }!hasUnstoppableInAnimations && this.stop()\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;;AAIA,MAAM,OAAO,YAAY;AAClB,MAAM,kBAAkB;IAC3B,cAAc,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;QAClC,OAAO,MAAM,MAAM,MAAM,CAAC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,EAAE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC;IAC3E;IACA,QAAQ,SAAS,GAAG,EAAE,KAAK,EAAE,GAAG;QAC5B,OAAO,MAAM,MAAM,MAAM,MAAM,CAAC,MAAM,KAAK,IAAI,CAAC;IACpD;AACJ;AACO,MAAM,mBAAmB;IAC5B,UAAU,SAAS,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa;QAC5D,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,KAAK,OAAO,EAAE;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,WAAW,EAAE;QACnB,IAAK,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAC9B,SAAS,IAAI,CAAC,EAAE;YAChB,MAAM;gBAAC,MAAM,CAAC,EAAE;aAAC;YACjB,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;oBAChC,IAAI,IAAI,CAAC,OAAO,UAAU,MAAM,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE;gBACjD;YACJ;YACA,SAAS,IAAI,CAAC;QAClB;QACA,cAAc,QAAQ,GAAG,OAAO,GAAG,IAAI,MAAM,WAAW,OAAO,GAAG,GAAG;QACrE,KAAK,IAAI,CAAC;YACN,UAAU;QACd;IACJ;IACA,KAAK,SAAS,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM;QACxC,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,KAAK,OAAO,EAAE;QACpB,MAAM,UAAU,CAAC;QACjB,IAAK,MAAM,KAAK,KAAM;YAClB,OAAO,CAAC,EAAE,GAAG,OAAO,UAAU,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QAChD;QACA,KAAK,IAAI,CAAC;IACd;IACA,WAAW,SAAS,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa;QAC7D,MAAM,OAAO,OAAO,IAAI;QACxB,MAAM,KAAK,OAAO,EAAE;QACpB,MAAM,UAAU,CAAC;QACjB,IAAK,MAAM,KAAK,KAAM;YAClB,OAAO,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,GAAG,OAAO,UAAU,IAAI,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE;QACnE;QACA,KAAK,IAAI,CAAC;IACd;IACA,MAAM,SAAS,IAAI,EAAE,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa;QACvE,MAAM,MAAM,CAAC;QACb,GAAG,CAAC,cAAc,GAAG,aAAa,CAAC,cAAc,GAAG,OAAO,UAAU,OAAO,IAAI,EAAE,OAAO,EAAE;QAC3F,KAAK,IAAI,CAAC;IACd;IACA,GAAG;IACH,UAAU,SAAS,OAAO,EAAE,eAAe;QACvC,QAAQ,IAAI,CAAC;IACjB;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,MAAM,OAAO,IAAI;IACjB,MAAM,cAAc,KAAK,YAAY;IACrC,IAAI;IACJ,KAAK,SAAS,GAAG,KAAK,aAAa,CAAC;IACpC,IAAK,YAAY,KAAK,MAAM,CAAE;QAC1B,MAAM,OAAO,WAAW,CAAC,SAAS,IAAI,YAAY,IAAI;QACtD,KAAK,KAAK,OAAO,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE,KAAK,SAAS,EAAE,KAAK,OAAO,EAAE,KAAK,cAAc,EAAE;IACjG;IACA,KAAK,OAAO,CAAC,IAAI,IAAI,KAAK,OAAO,CAAC,IAAI,CAAC,KAAK,OAAO,CAAC,KAAK,SAAS,EAAE,GAAG,IAAI,KAAK,SAAS;IACzF,IAAI,MAAM,KAAK,SAAS,EAAE;QACtB,OAAO,KAAK,IAAI;IACpB;IACA,OAAO;AACX;AAEA,SAAS,UAAU,GAAG;IAClB,IAAI,MAAM,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,KAAK,EAAE;QACrC,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,OAAO;AACX;AAEA,SAAS,MAAM,GAAG;IACd,IAAI,CAAC,UAAU,GAAG;IAClB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,KAAK,GAAG,YAAY;IACrC,OAAO;AACX;AAEA,SAAS,UAAU,OAAO,EAAE,MAAM,EAAE,OAAO;IACvC,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,MAAM,GAAG;IACd,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,QAAQ,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,QAAQ,GAAG,QAAQ,iBAAiB,GAAG,QAAQ,QAAQ;IAC3G,IAAI,CAAC,KAAK,GAAG,QAAQ,KAAK,IAAI,QAAQ,QAAQ,GAAG,QAAQ,KAAK,IAAI;IAClE,IAAI,CAAC,YAAY,GAAG,QAAQ,WAAW,IAAI;IAC3C,IAAI,CAAC,OAAO,GAAG,eAAe,CAAC,QAAQ,MAAM,CAAC,IAAI,gBAAgB,YAAY;IAC9E,IAAI,CAAC,cAAc,GAAG,CAAC;IACvB,IAAI,CAAC,IAAI,GAAG;AAChB;AACA,UAAU,SAAS,GAAG;IAClB,eAAe,SAAS,GAAG;QACvB,OAAO,KAAK,GAAG,CAAC,GAAG,CAAC,MAAM,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,QAAQ;IAC3E;IACA,MAAM,SAAS,eAAe;QAC1B,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,cAAc,IAAI,CAAC,YAAY;QACrC,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,IAAI,GAAG;QACxB,YAAY,QAAQ,IAAI,YAAY,QAAQ,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,cAAc;QAC9E,QAAQ,QAAQ,IAAI,CAAC,mBAAmB,QAAQ,QAAQ;IAC5D;AACJ;AACO,SAAS,oBAAoB,OAAO;IACvC,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,QAAQ,GAAG;IAChB,IAAI,CAAC,WAAW,GAAG,CAAC;IACpB,IAAI,CAAC,OAAO,GAAG;AACnB;AACA,oBAAoB,SAAS,GAAG;IAC5B,OAAO;QACH,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,KAAK,WAAW;QACnC,IAAI,kBAAkB;QACtB,MAAM,MAAM,CAAC,IAAI,IAAI,EAAE,OAAO;QAC9B,IAAI;QACJ,MAAM,eAAe,KAAK,aAAa;QACvC,IAAK,MAAM,WAAY;YACnB,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;gBAC3B,OAAO,UAAU,CAAC,GAAG;YACzB;YACA;QACJ;QACA,IAAI,MAAM,iBAAiB;YACvB,KAAK,IAAI;YACT,KAAK,kBAAkB,GAAG,gBAAgB,WAAY;gBAClD,IAAI,MAAM,KAAK,eAAe,EAAE;oBAC5B;oBACA,KAAK,aAAa,GAAG;gBACzB;YACJ;YACA;QACJ;QACA,KAAK,QAAQ,GAAG,yKAAA,CAAA,wBAAqB,CAAC,IAAI,CAAC,MAAO;YAC9C,KAAK,KAAK;QACd,GAAI,KAAK,OAAO;IACpB;IACA,cAAc,SAAS,SAAS;QAC5B,MAAM,OAAO,IAAI;QACjB,KAAK,WAAW,CAAC,KAAK,eAAe,GAAG,GAAG;QAC3C,aAAa,KAAK,kBAAkB;QACpC,IAAI,CAAC,KAAK,QAAQ,EAAE;YAChB,aAAa,KAAK,WAAW;YAC7B,KAAK,WAAW,GAAG,WAAY;gBAC3B,KAAK,QAAQ,GAAG;gBAChB,KAAK,KAAK;YACd,GAAI;QACR;IACJ;IACA,gBAAgB,SAAS,IAAI,EAAE,MAAM,EAAE,OAAO;QAC1C,IAAI,QAAQ,UAAU,SAAS;YAC3B,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,IAAI;YACrC,IAAI,CAAC,YAAY,CAAC,KAAK,SAAS,GAAG,IAAI,UAAU,MAAM,QAAQ;QACnE;IACJ;IACA,gBAAgB,SAAS,YAAY;QACjC,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,GAAG,eAAe;IAC/D;IACA,SAAS;QACL,IAAI,CAAC,IAAI;QACT,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,MAAM;QACF,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,eAAe,GAAG;QACvB,CAAA,GAAA,yKAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,QAAQ;QAClC,aAAa,IAAI,CAAC,WAAW;QAC7B,aAAa,IAAI,CAAC,kBAAkB;QACpC,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,MAAM;QACF,IAAI;QACJ,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI;QACJ,IAAI;QACJ,IAAK,MAAM,WAAY;YACnB,cAAc,UAAU,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW;YAChD,6BAA6B,8BAA8B;YAC3D,IAAI,CAAC,aAAa;gBACd,UAAU,CAAC,GAAG,CAAC,IAAI,CAAC;gBACpB,OAAO,UAAU,CAAC,GAAG;YACzB;QACJ;QAAC,CAAC,8BAA8B,IAAI,CAAC,IAAI;IAC7C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 920, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/renderers/renderer.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/renderers/renderer.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../core/renderer\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport {\r\n    getWindow\r\n} from \"../../../core/utils/window\";\r\nimport callOnce from \"../../../core/utils/call_once\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    getSvgMarkup\r\n} from \"../../../core/utils/svg\";\r\nimport {\r\n    AnimationController\r\n} from \"./animation\";\r\nimport {\r\n    normalizeBBox,\r\n    rotateBBox,\r\n    normalizeEnum,\r\n    normalizeArcParams,\r\n    getNextDefsSvgId\r\n} from \"../utils\";\r\nimport {\r\n    isDefined\r\n} from \"../../../core/utils/type\";\r\nconst window = getWindow();\r\nconst {\r\n    max: max,\r\n    round: round\r\n} = Math;\r\nconst SHARPING_CORRECTION = .5;\r\nconst ARC_COORD_PREC = 5;\r\nconst LIGHTENING_HASH = \"@filter::lightening\";\r\nconst pxAddingExceptions = {\r\n    \"column-count\": true,\r\n    \"fill-opacity\": true,\r\n    \"flex-grow\": true,\r\n    \"flex-shrink\": true,\r\n    \"font-weight\": true,\r\n    \"line-height\": true,\r\n    opacity: true,\r\n    order: true,\r\n    orphans: true,\r\n    widows: true,\r\n    \"z-index\": true,\r\n    zoom: true\r\n};\r\nconst KEY_TEXT = \"text\";\r\nconst KEY_STROKE = \"stroke\";\r\nconst KEY_STROKE_WIDTH = \"stroke-width\";\r\nconst KEY_STROKE_OPACITY = \"stroke-opacity\";\r\nconst KEY_FONT_SIZE = \"font-size\";\r\nconst KEY_FONT_STYLE = \"font-style\";\r\nconst KEY_FONT_WEIGHT = \"font-weight\";\r\nconst KEY_TEXT_DECORATION = \"text-decoration\";\r\nconst KEY_TEXTS_ALIGNMENT = \"textsAlignment\";\r\nconst NONE = \"none\";\r\nconst DEFAULT_FONT_SIZE = 12;\r\nconst ELLIPSIS = \"...\";\r\nconst objectCreate = function() {\r\n    if (!Object.create) {\r\n        return function(proto) {\r\n            const F = function() {};\r\n            F.prototype = proto;\r\n            return new F\r\n        }\r\n    } else {\r\n        return function(proto) {\r\n            return Object.create(proto)\r\n        }\r\n    }\r\n}();\r\nconst DEFAULTS = {\r\n    scaleX: 1,\r\n    scaleY: 1,\r\n    \"pointer-events\": null\r\n};\r\nconst getBackup = callOnce((function() {\r\n    const backupContainer = domAdapter.createElement(\"div\");\r\n    backupContainer.style.left = \"-9999px\";\r\n    backupContainer.style.position = \"absolute\";\r\n    return {\r\n        backupContainer: backupContainer,\r\n        backupCounter: 0\r\n    }\r\n}));\r\n\r\nfunction backupRoot(root) {\r\n    if (0 === getBackup().backupCounter) {\r\n        domAdapter.getBody().appendChild(getBackup().backupContainer)\r\n    }++getBackup().backupCounter;\r\n    root.append({\r\n        element: getBackup().backupContainer\r\n    })\r\n}\r\n\r\nfunction restoreRoot(root, container) {\r\n    root.append({\r\n        element: container\r\n    });\r\n    --getBackup().backupCounter;\r\n    if (0 === getBackup().backupCounter) {\r\n        domAdapter.getBody().removeChild(getBackup().backupContainer)\r\n    }\r\n}\r\n\r\nfunction isObjectArgument(value) {\r\n    return value && \"string\" !== typeof value\r\n}\r\n\r\nfunction createElement(tagName) {\r\n    return domAdapter.createElementNS(\"http://www.w3.org/2000/svg\", tagName)\r\n}\r\nexport function getFuncIri(id, pathModified) {\r\n    return null !== id ? \"url(\" + (pathModified ? window.location.href.split(\"#\")[0] : \"\") + \"#\" + id + \")\" : id\r\n}\r\n\r\nfunction extend(target, source) {\r\n    let key;\r\n    for (key in source) {\r\n        target[key] = source[key]\r\n    }\r\n    return target\r\n}\r\nconst preserveAspectRatioMap = {\r\n    full: NONE,\r\n    lefttop: \"xMinYMin\",\r\n    leftcenter: \"xMinYMid\",\r\n    leftbottom: \"xMinYMax\",\r\n    centertop: \"xMidYMin\",\r\n    center: \"xMidYMid\",\r\n    centerbottom: \"xMidYMax\",\r\n    righttop: \"xMaxYMin\",\r\n    rightcenter: \"xMaxYMid\",\r\n    rightbottom: \"xMaxYMax\"\r\n};\r\nexport function processHatchingAttrs(element, attrs) {\r\n    if (attrs.hatching && \"none\" !== normalizeEnum(attrs.hatching.direction)) {\r\n        attrs = extend({}, attrs);\r\n        attrs.fill = element._hatching = element.renderer.lockDefsElements({\r\n            color: attrs.fill,\r\n            hatching: attrs.hatching\r\n        }, element._hatching, \"pattern\");\r\n        delete attrs.filter\r\n    } else if (element._hatching) {\r\n        element.renderer.releaseDefsElements(element._hatching);\r\n        element._hatching = null;\r\n        delete attrs.filter\r\n    } else if (attrs.filter) {\r\n        attrs = extend({}, attrs);\r\n        attrs.filter = element._filter = element.renderer.lockDefsElements({}, element._filter, \"filter\")\r\n    } else if (element._filter) {\r\n        element.renderer.releaseDefsElements(element._filter);\r\n        element._filter = null\r\n    }\r\n    delete attrs.hatching;\r\n    return attrs\r\n}\r\nconst buildArcPath = function(x, y, innerR, outerR, startAngleCos, startAngleSin, endAngleCos, endAngleSin, isCircle, longFlag) {\r\n    return [\"M\", (x + outerR * startAngleCos).toFixed(5), (y - outerR * startAngleSin).toFixed(5), \"A\", outerR.toFixed(5), outerR.toFixed(5), 0, longFlag, 0, (x + outerR * endAngleCos).toFixed(5), (y - outerR * endAngleSin).toFixed(5), isCircle ? \"M\" : \"L\", (x + innerR * endAngleCos).toFixed(5), (y - innerR * endAngleSin).toFixed(5), \"A\", innerR.toFixed(5), innerR.toFixed(5), 0, longFlag, 1, (x + innerR * startAngleCos).toFixed(5), (y - innerR * startAngleSin).toFixed(5), \"Z\"].join(\" \")\r\n};\r\n\r\nfunction buildPathSegments(points, type) {\r\n    let list = [\r\n        [\"M\", 0, 0]\r\n    ];\r\n    switch (type) {\r\n        case \"line\":\r\n            list = buildLineSegments(points);\r\n            break;\r\n        case \"area\":\r\n            list = buildLineSegments(points, true);\r\n            break;\r\n        case \"bezier\":\r\n            list = buildCurveSegments(points);\r\n            break;\r\n        case \"bezierarea\":\r\n            list = buildCurveSegments(points, true)\r\n    }\r\n    return list\r\n}\r\n\r\nfunction buildLineSegments(points, close) {\r\n    return buildSegments(points, buildSimpleLineSegment, close)\r\n}\r\n\r\nfunction buildCurveSegments(points, close) {\r\n    return buildSegments(points, buildSimpleCurveSegment, close)\r\n}\r\n\r\nfunction buildSegments(points, buildSimpleSegment, close) {\r\n    var _points$;\r\n    let i;\r\n    let ii;\r\n    const list = [];\r\n    if (null !== (_points$ = points[0]) && void 0 !== _points$ && _points$.length) {\r\n        for (i = 0, ii = points.length; i < ii; ++i) {\r\n            buildSimpleSegment(points[i], close, list)\r\n        }\r\n    } else {\r\n        buildSimpleSegment(points, close, list)\r\n    }\r\n    return list\r\n}\r\n\r\nfunction buildSimpleLineSegment(points, close, list) {\r\n    let i = 0;\r\n    const k0 = list.length;\r\n    let k = k0;\r\n    const ii = (points || []).length;\r\n    if (ii) {\r\n        if (void 0 !== points[0].x) {\r\n            for (; i < ii;) {\r\n                list[k++] = [\"L\", points[i].x, points[i++].y]\r\n            }\r\n        } else {\r\n            for (; i < ii;) {\r\n                list[k++] = [\"L\", points[i++], points[i++]]\r\n            }\r\n        }\r\n        list[k0][0] = \"M\"\r\n    } else {\r\n        list[k] = [\"M\", 0, 0]\r\n    }\r\n    close && list.push([\"Z\"]);\r\n    return list\r\n}\r\n\r\nfunction buildSimpleCurveSegment(points, close, list) {\r\n    let i;\r\n    let k = list.length;\r\n    const ii = (points || []).length;\r\n    if (ii) {\r\n        if (void 0 !== points[0].x) {\r\n            list[k++] = [\"M\", points[0].x, points[0].y];\r\n            for (i = 1; i < ii;) {\r\n                list[k++] = [\"C\", points[i].x, points[i++].y, points[i].x, points[i++].y, points[i].x, points[i++].y]\r\n            }\r\n        } else {\r\n            list[k++] = [\"M\", points[0], points[1]];\r\n            for (i = 2; i < ii;) {\r\n                list[k++] = [\"C\", points[i++], points[i++], points[i++], points[i++], points[i++], points[i++]]\r\n            }\r\n        }\r\n    } else {\r\n        list[k] = [\"M\", 0, 0]\r\n    }\r\n    close && list.push([\"Z\"]);\r\n    return list\r\n}\r\n\r\nfunction combinePathParam(segments) {\r\n    const d = [];\r\n    let k = 0;\r\n    let i;\r\n    const ii = segments.length;\r\n    let segment;\r\n    let j;\r\n    let jj;\r\n    for (i = 0; i < ii; ++i) {\r\n        segment = segments[i];\r\n        for (j = 0, jj = segment.length; j < jj; ++j) {\r\n            d[k++] = segment[j]\r\n        }\r\n    }\r\n    return d.join(\" \")\r\n}\r\n\r\nfunction compensateSegments(oldSegments, newSegments, type) {\r\n    const oldLength = oldSegments.length;\r\n    const newLength = newSegments.length;\r\n    let i;\r\n    let originalNewSegments;\r\n    const makeEqualSegments = -1 !== type.indexOf(\"area\") ? makeEqualAreaSegments : makeEqualLineSegments;\r\n    if (0 === oldLength) {\r\n        for (i = 0; i < newLength; i++) {\r\n            oldSegments.push(newSegments[i].slice(0))\r\n        }\r\n    } else if (oldLength < newLength) {\r\n        makeEqualSegments(oldSegments, newSegments, type)\r\n    } else if (oldLength > newLength) {\r\n        originalNewSegments = newSegments.slice(0);\r\n        makeEqualSegments(newSegments, oldSegments, type)\r\n    }\r\n    return originalNewSegments\r\n}\r\n\r\nfunction prepareConstSegment(constSeg, type) {\r\n    const x = constSeg[constSeg.length - 2];\r\n    const y = constSeg[constSeg.length - 1];\r\n    switch (type) {\r\n        case \"line\":\r\n        case \"area\":\r\n            constSeg[0] = \"L\";\r\n            break;\r\n        case \"bezier\":\r\n        case \"bezierarea\":\r\n            constSeg[0] = \"C\";\r\n            constSeg[1] = constSeg[3] = constSeg[5] = x;\r\n            constSeg[2] = constSeg[4] = constSeg[6] = y\r\n    }\r\n}\r\n\r\nfunction makeEqualLineSegments(short, long, type) {\r\n    const constSeg = short[short.length - 1].slice();\r\n    let i = short.length;\r\n    prepareConstSegment(constSeg, type);\r\n    for (; i < long.length; i++) {\r\n        short[i] = constSeg.slice(0)\r\n    }\r\n}\r\n\r\nfunction makeEqualAreaSegments(short, long, type) {\r\n    let i;\r\n    let head;\r\n    const shortLength = short.length;\r\n    const longLength = long.length;\r\n    let constsSeg1;\r\n    let constsSeg2;\r\n    if ((shortLength - 1) % 2 === 0 && (longLength - 1) % 2 === 0) {\r\n        i = (shortLength - 1) / 2 - 1;\r\n        head = short.slice(0, i + 1);\r\n        constsSeg1 = head[head.length - 1].slice(0);\r\n        constsSeg2 = short.slice(i + 1)[0].slice(0);\r\n        prepareConstSegment(constsSeg1, type);\r\n        prepareConstSegment(constsSeg2, type);\r\n        for (let j = i; j < (longLength - 1) / 2 - 1; j++) {\r\n            short.splice(j + 1, 0, constsSeg1);\r\n            short.splice(j + 3, 0, constsSeg2)\r\n        }\r\n    }\r\n}\r\n\r\nfunction baseCss(that, styles) {\r\n    const elemStyles = that._styles;\r\n    let key;\r\n    let value;\r\n    styles = styles || {};\r\n    for (key in styles) {\r\n        value = styles[key];\r\n        if (isDefined(value)) {\r\n            value += \"number\" === typeof value && !pxAddingExceptions[key] ? \"px\" : \"\";\r\n            elemStyles[key] = \"\" !== value ? value : null\r\n        }\r\n    }\r\n    for (key in elemStyles) {\r\n        value = elemStyles[key];\r\n        if (value) {\r\n            that.element.style[key] = value\r\n        } else if (null === value) {\r\n            that.element.style[key] = \"\"\r\n        }\r\n    }\r\n    return that\r\n}\r\n\r\nfunction fixFuncIri(wrapper, attribute) {\r\n    const element = wrapper.element;\r\n    const id = wrapper.attr(attribute);\r\n    if (id && -1 !== id.indexOf(\"DevExpress\")) {\r\n        element.removeAttribute(attribute);\r\n        element.setAttribute(attribute, getFuncIri(id, wrapper.renderer.pathModified))\r\n    }\r\n}\r\n\r\nfunction baseAttr(that, attrs) {\r\n    attrs = attrs || {};\r\n    const settings = that._settings;\r\n    const attributes = {};\r\n    let key;\r\n    let value;\r\n    const elem = that.element;\r\n    const renderer = that.renderer;\r\n    const rtl = renderer.rtl;\r\n    let hasTransformations;\r\n    let recalculateDashStyle;\r\n    let sw;\r\n    let i;\r\n    if (!isObjectArgument(attrs)) {\r\n        if (attrs in settings) {\r\n            return settings[attrs]\r\n        }\r\n        if (attrs in DEFAULTS) {\r\n            return DEFAULTS[attrs]\r\n        }\r\n        return 0\r\n    }\r\n    extend(attributes, attrs);\r\n    for (key in attributes) {\r\n        value = attributes[key];\r\n        if (void 0 === value) {\r\n            continue\r\n        }\r\n        settings[key] = value;\r\n        if (\"align\" === key) {\r\n            key = \"text-anchor\";\r\n            value = {\r\n                left: rtl ? \"end\" : \"start\",\r\n                center: \"middle\",\r\n                right: rtl ? \"start\" : \"end\"\r\n            } [value] || null\r\n        } else if (\"dashStyle\" === key) {\r\n            recalculateDashStyle = true;\r\n            continue\r\n        } else if (\"stroke-width\" === key) {\r\n            recalculateDashStyle = true\r\n        } else if (value && (\"fill\" === key || \"clip-path\" === key || \"filter\" === key) && 0 === value.indexOf(\"DevExpress\")) {\r\n            that._addFixIRICallback();\r\n            value = getFuncIri(value, renderer.pathModified)\r\n        } else if (/^(translate(X|Y)|rotate[XY]?|scale(X|Y)|sharp|sharpDirection)$/i.test(key)) {\r\n            hasTransformations = true;\r\n            continue\r\n        } else if (/^(x|y|d)$/i.test(key)) {\r\n            hasTransformations = true\r\n        }\r\n        if (null === value) {\r\n            elem.removeAttribute(key)\r\n        } else {\r\n            elem.setAttribute(key, value)\r\n        }\r\n    }\r\n    if (recalculateDashStyle && \"dashStyle\" in settings) {\r\n        value = settings.dashStyle;\r\n        sw = (\"_originalSW\" in that ? that._originalSW : settings[\"stroke-width\"]) || 1;\r\n        key = \"stroke-dasharray\";\r\n        value = null === value ? \"\" : normalizeEnum(value);\r\n        if (\"\" === value || \"solid\" === value || value === NONE) {\r\n            that.element.removeAttribute(key)\r\n        } else {\r\n            value = value.replace(/longdash/g, \"8,3,\").replace(/dash/g, \"4,3,\").replace(/dot/g, \"1,3,\").replace(/,$/, \"\").split(\",\");\r\n            i = value.length;\r\n            while (i--) {\r\n                value[i] = parseInt(value[i]) * sw\r\n            }\r\n            that.element.setAttribute(key, value.join(\",\"))\r\n        }\r\n    }\r\n    if (hasTransformations) {\r\n        that._applyTransformation()\r\n    }\r\n    return that\r\n}\r\n\r\nfunction pathAttr(attrs) {\r\n    const that = this;\r\n    let segments;\r\n    if (isObjectArgument(attrs)) {\r\n        attrs = extend({}, attrs);\r\n        segments = attrs.segments;\r\n        if (\"points\" in attrs) {\r\n            segments = buildPathSegments(attrs.points, that.type);\r\n            delete attrs.points\r\n        }\r\n        if (segments) {\r\n            attrs.d = combinePathParam(segments);\r\n            that.segments = segments;\r\n            delete attrs.segments\r\n        }\r\n    }\r\n    return baseAttr(that, attrs)\r\n}\r\n\r\nfunction arcAttr(attrs) {\r\n    const settings = this._settings;\r\n    let x;\r\n    let y;\r\n    let innerRadius;\r\n    let outerRadius;\r\n    let startAngle;\r\n    let endAngle;\r\n    if (isObjectArgument(attrs)) {\r\n        attrs = extend({}, attrs);\r\n        if (\"x\" in attrs || \"y\" in attrs || \"innerRadius\" in attrs || \"outerRadius\" in attrs || \"startAngle\" in attrs || \"endAngle\" in attrs) {\r\n            settings.x = x = \"x\" in attrs ? attrs.x : settings.x;\r\n            delete attrs.x;\r\n            settings.y = y = \"y\" in attrs ? attrs.y : settings.y;\r\n            delete attrs.y;\r\n            settings.innerRadius = innerRadius = \"innerRadius\" in attrs ? attrs.innerRadius : settings.innerRadius;\r\n            delete attrs.innerRadius;\r\n            settings.outerRadius = outerRadius = \"outerRadius\" in attrs ? attrs.outerRadius : settings.outerRadius;\r\n            delete attrs.outerRadius;\r\n            settings.startAngle = startAngle = \"startAngle\" in attrs ? attrs.startAngle : settings.startAngle;\r\n            delete attrs.startAngle;\r\n            settings.endAngle = endAngle = \"endAngle\" in attrs ? attrs.endAngle : settings.endAngle;\r\n            delete attrs.endAngle;\r\n            attrs.d = buildArcPath.apply(null, normalizeArcParams(x, y, innerRadius, outerRadius, startAngle, endAngle))\r\n        }\r\n    }\r\n    return baseAttr(this, attrs)\r\n}\r\n\r\nfunction rectAttr(attrs) {\r\n    const that = this;\r\n    let x;\r\n    let y;\r\n    let width;\r\n    let height;\r\n    let sw;\r\n    let maxSW;\r\n    let newSW;\r\n    if (isObjectArgument(attrs)) {\r\n        attrs = extend({}, attrs);\r\n        if (void 0 !== attrs.x || void 0 !== attrs.y || void 0 !== attrs.width || void 0 !== attrs.height || void 0 !== attrs[\"stroke-width\"]) {\r\n            void 0 !== attrs.x ? x = that._originalX = attrs.x : x = that._originalX || 0;\r\n            void 0 !== attrs.y ? y = that._originalY = attrs.y : y = that._originalY || 0;\r\n            void 0 !== attrs.width ? width = that._originalWidth = attrs.width : width = that._originalWidth || 0;\r\n            void 0 !== attrs.height ? height = that._originalHeight = attrs.height : height = that._originalHeight || 0;\r\n            void 0 !== attrs[\"stroke-width\"] ? sw = that._originalSW = attrs[\"stroke-width\"] : sw = that._originalSW;\r\n            maxSW = ~~((width < height ? width : height) / 2);\r\n            newSW = (sw || 0) < maxSW ? sw || 0 : maxSW;\r\n            attrs.x = x + newSW / 2;\r\n            attrs.y = y + newSW / 2;\r\n            attrs.width = width - newSW;\r\n            attrs.height = height - newSW;\r\n            ((sw || 0) !== newSW || !(0 === newSW && void 0 === sw)) && (attrs[\"stroke-width\"] = newSW)\r\n        }\r\n        if (\"sharp\" in attrs) {\r\n            delete attrs.sharp\r\n        }\r\n    }\r\n    return baseAttr(that, attrs)\r\n}\r\n\r\nfunction textAttr(attrs) {\r\n    const that = this;\r\n    let isResetRequired;\r\n    if (!isObjectArgument(attrs)) {\r\n        return baseAttr(that, attrs)\r\n    }\r\n    attrs = extend({}, attrs);\r\n    const settings = that._settings;\r\n    const wasStroked = isDefined(settings.stroke) && isDefined(settings[\"stroke-width\"]);\r\n    if (void 0 !== attrs.text) {\r\n        settings.text = attrs.text;\r\n        delete attrs.text;\r\n        isResetRequired = true\r\n    }\r\n    if (void 0 !== attrs.stroke) {\r\n        settings.stroke = attrs.stroke;\r\n        delete attrs.stroke\r\n    }\r\n    if (void 0 !== attrs[\"stroke-width\"]) {\r\n        settings[\"stroke-width\"] = attrs[\"stroke-width\"];\r\n        delete attrs[\"stroke-width\"]\r\n    }\r\n    if (void 0 !== attrs[\"stroke-opacity\"]) {\r\n        settings[\"stroke-opacity\"] = attrs[\"stroke-opacity\"];\r\n        delete attrs[\"stroke-opacity\"]\r\n    }\r\n    if (void 0 !== attrs.textsAlignment) {\r\n        alignTextNodes(that, attrs.textsAlignment);\r\n        delete attrs.textsAlignment\r\n    }\r\n    const isStroked = isDefined(settings.stroke) && isDefined(settings[\"stroke-width\"]);\r\n    baseAttr(that, attrs);\r\n    isResetRequired = isResetRequired || isStroked !== wasStroked && settings.text;\r\n    if (isResetRequired) {\r\n        createTextNodes(that, settings.text, isStroked);\r\n        that._hasEllipsis = false\r\n    }\r\n    if (isResetRequired || void 0 !== attrs.x || void 0 !== attrs.y) {\r\n        locateTextNodes(that)\r\n    }\r\n    if (isStroked) {\r\n        strokeTextNodes(that)\r\n    }\r\n    return that\r\n}\r\n\r\nfunction textCss(styles) {\r\n    styles = styles || {};\r\n    baseCss(this, styles);\r\n    if (\"font-size\" in styles) {\r\n        locateTextNodes(this)\r\n    }\r\n    return this\r\n}\r\n\r\nfunction orderHtmlTree(list, line, node, parentStyle, parentClassName) {\r\n    let style;\r\n    let realStyle;\r\n    let i;\r\n    let ii;\r\n    let nodes;\r\n    if (void 0 !== node.wholeText) {\r\n        list.push({\r\n            value: node.wholeText,\r\n            style: parentStyle,\r\n            className: parentClassName,\r\n            line: line,\r\n            height: parentStyle[\"font-size\"] || 0\r\n        })\r\n    } else if (\"BR\" === node.tagName) {\r\n        ++line\r\n    } else if (domAdapter.isElementNode(node)) {\r\n        extend(style = {}, parentStyle);\r\n        switch (node.tagName) {\r\n            case \"B\":\r\n            case \"STRONG\":\r\n                style[\"font-weight\"] = \"bold\";\r\n                break;\r\n            case \"I\":\r\n            case \"EM\":\r\n                style[\"font-style\"] = \"italic\";\r\n                break;\r\n            case \"U\":\r\n                style[\"text-decoration\"] = \"underline\"\r\n        }\r\n        realStyle = node.style;\r\n        realStyle.color && (style.fill = realStyle.color);\r\n        realStyle.fontSize && (style[\"font-size\"] = realStyle.fontSize);\r\n        realStyle.fontStyle && (style[\"font-style\"] = realStyle.fontStyle);\r\n        realStyle.fontWeight && (style[\"font-weight\"] = realStyle.fontWeight);\r\n        realStyle.textDecoration && (style[\"text-decoration\"] = realStyle.textDecoration);\r\n        for (i = 0, nodes = node.childNodes, ii = nodes.length; i < ii; ++i) {\r\n            line = orderHtmlTree(list, line, nodes[i], style, node.className || parentClassName)\r\n        }\r\n    }\r\n    return line\r\n}\r\n\r\nfunction adjustLineHeights(items) {\r\n    let i;\r\n    let ii;\r\n    let currentItem = items[0];\r\n    let item;\r\n    for (i = 1, ii = items.length; i < ii; ++i) {\r\n        item = items[i];\r\n        if (item.line === currentItem.line) {\r\n            currentItem.height = maxLengthFontSize(currentItem.height, item.height);\r\n            currentItem.inherits = currentItem.inherits || 0 === parseFloat(item.height);\r\n            item.height = NaN\r\n        } else {\r\n            currentItem = item\r\n        }\r\n    }\r\n}\r\n\r\nfunction removeExtraAttrs(html) {\r\n    const findStyleAndClassAttrs = /(style|class)\\s*=\\s*([\"'])(?:(?!\\2).)*\\2\\s?/gi;\r\n    return html.replace(/(?:(<[a-z0-9]+\\s*))([\\s\\S]*?)(>|\\/>)/gi, (function(allTagAttrs, p1, p2, p3) {\r\n        p2 = (p2 && p2.match(findStyleAndClassAttrs) || []).map((function(str) {\r\n            return str\r\n        })).join(\" \");\r\n        return p1 + p2 + p3\r\n    }))\r\n}\r\n\r\nfunction parseHTML(text) {\r\n    const items = [];\r\n    const div = domAdapter.createElement(\"div\");\r\n    div.innerHTML = text.replace(/\\r/g, \"\").replace(/\\n/g, \"<br/>\").replace(/style=/g, \"data-style=\");\r\n    div.querySelectorAll(\"[data-style]\").forEach((element => {\r\n        element.style = element.getAttribute(\"data-style\");\r\n        element.removeAttribute(\"data-style\")\r\n    }));\r\n    orderHtmlTree(items, 0, div, {}, \"\");\r\n    adjustLineHeights(items);\r\n    return items\r\n}\r\n\r\nfunction parseMultiline(text) {\r\n    const texts = text.replace(/\\r/g, \"\").split(/\\n/g);\r\n    let i = 0;\r\n    const items = [];\r\n    for (; i < texts.length; i++) {\r\n        items.push({\r\n            value: texts[i].trim(),\r\n            height: 0,\r\n            line: i\r\n        })\r\n    }\r\n    return items\r\n}\r\n\r\nfunction createTspans(items, element, fieldName) {\r\n    let i;\r\n    let ii;\r\n    let item;\r\n    for (i = 0, ii = items.length; i < ii; ++i) {\r\n        item = items[i];\r\n        item[fieldName] = createElement(\"tspan\");\r\n        item[fieldName].appendChild(domAdapter.createTextNode(item.value));\r\n        item.style && baseCss({\r\n            element: item[fieldName],\r\n            _styles: {}\r\n        }, item.style);\r\n        item.className && item[fieldName].setAttribute(\"class\", item.className);\r\n        element.appendChild(item[fieldName])\r\n    }\r\n}\r\n\r\nfunction restoreText() {\r\n    if (this._hasEllipsis) {\r\n        this.attr({\r\n            text: this._settings.text\r\n        })\r\n    }\r\n}\r\n\r\nfunction applyEllipsis(maxWidth) {\r\n    const that = this;\r\n    let lines;\r\n    let hasEllipsis = false;\r\n    let i;\r\n    let ii;\r\n    let lineParts;\r\n    let j;\r\n    let jj;\r\n    let text;\r\n    restoreText.call(that);\r\n    const ellipsis = that.renderer.text(\"...\").attr(that._styles).append(that.renderer.root);\r\n    const ellipsisWidth = ellipsis.getBBox().width;\r\n    if (that._getElementBBox().width > maxWidth) {\r\n        if (maxWidth - ellipsisWidth < 0) {\r\n            maxWidth = 0\r\n        } else {\r\n            maxWidth -= ellipsisWidth\r\n        }\r\n        lines = prepareLines(that.element, that._texts, maxWidth);\r\n        for (i = 0, ii = lines.length; i < ii; ++i) {\r\n            lineParts = lines[i].parts;\r\n            if (1 === lines[i].commonLength) {\r\n                continue\r\n            }\r\n            for (j = 0, jj = lineParts.length; j < jj; ++j) {\r\n                text = lineParts[j];\r\n                if (isDefined(text.endIndex)) {\r\n                    setNewText(text, text.endIndex);\r\n                    hasEllipsis = true\r\n                } else if (text.startBox > maxWidth) {\r\n                    removeTextSpan(text)\r\n                }\r\n            }\r\n        }\r\n    }\r\n    ellipsis.remove();\r\n    that._hasEllipsis = hasEllipsis;\r\n    return hasEllipsis\r\n}\r\n\r\nfunction cloneAndRemoveAttrs(node) {\r\n    let clone;\r\n    if (node) {\r\n        clone = node.cloneNode();\r\n        clone.removeAttribute(\"y\");\r\n        clone.removeAttribute(\"x\")\r\n    }\r\n    return clone || node\r\n}\r\n\r\nfunction detachTitleElements(element) {\r\n    const titleElements = domAdapter.querySelectorAll(element, \"title\");\r\n    for (let i = 0; i < titleElements.length; i++) {\r\n        element.removeChild(titleElements[i])\r\n    }\r\n    return titleElements\r\n}\r\n\r\nfunction detachAndStoreTitleElements(element) {\r\n    const titleElements = detachTitleElements(element);\r\n    return () => {\r\n        for (let i = 0; i < titleElements.length; i++) {\r\n            element.appendChild(titleElements[i])\r\n        }\r\n    }\r\n}\r\n\r\nfunction setMaxSize(maxWidth, maxHeight) {\r\n    let options = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};\r\n    const that = this;\r\n    let lines = [];\r\n    let textChanged = false;\r\n    let textIsEmpty = false;\r\n    let ellipsisMaxWidth = maxWidth;\r\n    restoreText.call(that);\r\n    const restoreTitleElement = detachAndStoreTitleElements(this.element);\r\n    const ellipsis = that.renderer.text(\"...\").attr(that._styles).append(that.renderer.root);\r\n    const ellipsisWidth = ellipsis.getBBox().width;\r\n    const {\r\n        width: width,\r\n        height: height\r\n    } = that._getElementBBox();\r\n    if ((width || height) && (width > maxWidth || maxHeight && height > maxHeight)) {\r\n        if (maxWidth - ellipsisWidth < 0) {\r\n            ellipsisMaxWidth = 0\r\n        } else {\r\n            ellipsisMaxWidth -= ellipsisWidth\r\n        }\r\n        lines = applyOverflowRules(that.element, that._texts, maxWidth, ellipsisMaxWidth, options);\r\n        lines = setMaxHeight(lines, ellipsisMaxWidth, options, maxHeight, parseFloat(this._getLineHeight()));\r\n        this._texts = lines.reduce(((texts, line) => texts.concat(line.parts)), []).filter((t => \"\" !== t.value)).map((t => {\r\n            t.stroke && t.tspan.parentNode.appendChild(t.stroke);\r\n            return t\r\n        })).map((t => {\r\n            t.tspan.parentNode.appendChild(t.tspan);\r\n            return t\r\n        }));\r\n        !this._texts.length && (this._texts = null);\r\n        textChanged = true;\r\n        if (this._texts) {\r\n            locateTextNodes(this)\r\n        } else {\r\n            this.element.textContent = \"\";\r\n            textIsEmpty = true\r\n        }\r\n    }\r\n    ellipsis.remove();\r\n    that._hasEllipsis = textChanged;\r\n    restoreTitleElement();\r\n    return {\r\n        rowCount: lines.length,\r\n        textChanged: textChanged,\r\n        textIsEmpty: textIsEmpty\r\n    }\r\n}\r\n\r\nfunction getIndexForEllipsis(text, maxWidth, startBox, endBox) {\r\n    let k;\r\n    let kk;\r\n    if (startBox <= maxWidth && endBox > maxWidth) {\r\n        for (k = 1, kk = text.value.length; k <= kk; ++k) {\r\n            if (startBox + text.tspan.getSubStringLength(0, k) > maxWidth) {\r\n                return k - 1\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nfunction getTextWidth(text) {\r\n    return text.value.length ? text.tspan.getSubStringLength(0, text.value.length) : 0\r\n}\r\n\r\nfunction prepareLines(element, texts, maxWidth) {\r\n    let lines = [];\r\n    let i;\r\n    let ii;\r\n    let text;\r\n    let startBox;\r\n    let endBox;\r\n    if (texts) {\r\n        for (i = 0, ii = texts.length; i < ii; ++i) {\r\n            text = texts[i];\r\n            if (!lines[text.line]) {\r\n                text.startBox = startBox = 0;\r\n                lines.push({\r\n                    commonLength: text.value.length,\r\n                    parts: [text]\r\n                })\r\n            } else {\r\n                text.startBox = startBox;\r\n                lines[text.line].parts.push(text);\r\n                lines[text.line].commonLength += text.value.length\r\n            }\r\n            endBox = startBox + text.tspan.getSubStringLength(0, text.value.length);\r\n            text.endIndex = getIndexForEllipsis(text, maxWidth, startBox, endBox);\r\n            startBox = endBox\r\n        }\r\n    } else {\r\n        text = {\r\n            value: element.textContent,\r\n            tspan: element\r\n        };\r\n        text.startBox = startBox = 0;\r\n        endBox = startBox + getTextWidth(text);\r\n        text.endIndex = getIndexForEllipsis(text, maxWidth, startBox, endBox);\r\n        lines = [{\r\n            commonLength: element.textContent.length,\r\n            parts: [text]\r\n        }]\r\n    }\r\n    return lines\r\n}\r\n\r\nfunction getSpaceBreakIndex(text, maxWidth) {\r\n    const initialIndices = text.startBox > 0 ? [0] : [];\r\n    const spaceIndices = text.value.split(\"\").reduce(((indices, char, index) => {\r\n        if (\" \" === char) {\r\n            indices.push(index)\r\n        }\r\n        return indices\r\n    }), initialIndices);\r\n    let spaceIndex = 0;\r\n    while (void 0 !== spaceIndices[spaceIndex + 1] && text.startBox + text.tspan.getSubStringLength(0, spaceIndices[spaceIndex + 1]) < maxWidth) {\r\n        spaceIndex++\r\n    }\r\n    return spaceIndices[spaceIndex]\r\n}\r\n\r\nfunction getWordBreakIndex(text, maxWidth) {\r\n    for (let i = 0; i < text.value.length - 1; i++) {\r\n        if (text.startBox + text.tspan.getSubStringLength(0, i + 1) > maxWidth) {\r\n            return i\r\n        }\r\n    }\r\n}\r\n\r\nfunction getEllipsisString(ellipsisMaxWidth, _ref) {\r\n    let {\r\n        hideOverflowEllipsis: hideOverflowEllipsis\r\n    } = _ref;\r\n    return hideOverflowEllipsis && 0 === ellipsisMaxWidth ? \"\" : \"...\"\r\n}\r\n\r\nfunction setEllipsis(text, ellipsisMaxWidth, options) {\r\n    const ellipsis = getEllipsisString(ellipsisMaxWidth, options);\r\n    if (text.value.length && text.tspan.parentNode) {\r\n        for (let i = text.value.length - 1; i >= 1; i--) {\r\n            if (text.startBox + text.tspan.getSubStringLength(0, i) < ellipsisMaxWidth) {\r\n                setNewText(text, i, ellipsis);\r\n                break\r\n            } else if (1 === i) {\r\n                setNewText(text, 0, ellipsis)\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nfunction wordWrap(text, maxWidth, ellipsisMaxWidth, options, lastStepBreakIndex) {\r\n    const wholeText = text.value;\r\n    let breakIndex;\r\n    if (\"none\" !== options.wordWrap) {\r\n        breakIndex = \"normal\" === options.wordWrap ? getSpaceBreakIndex(text, maxWidth) : getWordBreakIndex(text, maxWidth)\r\n    }\r\n    let restLines = [];\r\n    let restText;\r\n    if (isFinite(breakIndex) && !(0 === lastStepBreakIndex && 0 === breakIndex)) {\r\n        setNewText(text, breakIndex, \"\");\r\n        const newTextOffset = \" \" === wholeText[breakIndex] ? 1 : 0;\r\n        const restString = wholeText.slice(breakIndex + newTextOffset);\r\n        if (restString.length) {\r\n            const restTspan = cloneAndRemoveAttrs(text.tspan);\r\n            restTspan.textContent = restString;\r\n            text.tspan.parentNode.appendChild(restTspan);\r\n            restText = extend(extend({}, text), {\r\n                value: restString,\r\n                startBox: 0,\r\n                height: 0,\r\n                tspan: restTspan,\r\n                stroke: cloneAndRemoveAttrs(text.stroke),\r\n                endBox: restTspan.getSubStringLength(0, restString.length)\r\n            });\r\n            restText.stroke && (restText.stroke.textContent = restString);\r\n            if (restText.endBox > maxWidth) {\r\n                restLines = wordWrap(restText, maxWidth, ellipsisMaxWidth, options, breakIndex);\r\n                if (!restLines.length) {\r\n                    return []\r\n                }\r\n            }\r\n        }\r\n    }\r\n    if (text.value.length) {\r\n        if (\"ellipsis\" === options.textOverflow && text.tspan.getSubStringLength(0, text.value.length) > maxWidth) {\r\n            setEllipsis(text, ellipsisMaxWidth, options)\r\n        }\r\n        if (\"hide\" === options.textOverflow && text.tspan.getSubStringLength(0, text.value.length) > maxWidth) {\r\n            return []\r\n        }\r\n    } else {\r\n        text.tspan.parentNode.removeChild(text.tspan)\r\n    }\r\n    const parts = [];\r\n    if (restText) {\r\n        parts.push(restText)\r\n    }\r\n    return [{\r\n        commonLength: wholeText.length,\r\n        parts: parts\r\n    }].concat(restLines)\r\n}\r\n\r\nfunction calculateLineHeight(line, lineHeight) {\r\n    return line.parts.reduce(((height, text) => max(height, getItemLineHeight(text, lineHeight))), 0)\r\n}\r\n\r\nfunction setMaxHeight(lines, ellipsisMaxWidth, options, maxHeight, lineHeight) {\r\n    const textOverflow = options.textOverflow;\r\n    if (!isFinite(maxHeight) || 0 === Number(maxHeight) || \"none\" === textOverflow) {\r\n        return lines\r\n    }\r\n    const result = lines.reduce(((_ref2, l, index, arr) => {\r\n        let [lines, commonHeight] = _ref2;\r\n        const height = calculateLineHeight(l, lineHeight);\r\n        commonHeight += height;\r\n        if (commonHeight < maxHeight) {\r\n            lines.push(l)\r\n        } else {\r\n            l.parts.forEach((item => {\r\n                removeTextSpan(item)\r\n            }));\r\n            if (\"ellipsis\" === textOverflow) {\r\n                const prevLine = arr[index - 1];\r\n                if (prevLine) {\r\n                    const text = prevLine.parts[prevLine.parts.length - 1];\r\n                    if (!text.hasEllipsis) {\r\n                        if (0 === ellipsisMaxWidth || text.endBox < ellipsisMaxWidth) {\r\n                            setNewText(text, text.value.length, getEllipsisString(ellipsisMaxWidth, options))\r\n                        } else {\r\n                            setEllipsis(text, ellipsisMaxWidth, options)\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return [lines, commonHeight]\r\n    }), [\r\n        [], 0\r\n    ]);\r\n    if (\"hide\" === textOverflow && result[1] > maxHeight) {\r\n        result[0].forEach((l => {\r\n            l.parts.forEach((item => {\r\n                removeTextSpan(item)\r\n            }))\r\n        }));\r\n        return []\r\n    }\r\n    return result[0]\r\n}\r\n\r\nfunction applyOverflowRules(element, texts, maxWidth, ellipsisMaxWidth, options) {\r\n    if (!texts) {\r\n        const textValue = element.textContent;\r\n        const text = {\r\n            value: textValue,\r\n            height: 0,\r\n            line: 0\r\n        };\r\n        element.textContent = \"\";\r\n        createTspans([text], element, \"tspan\");\r\n        texts = [text]\r\n    }\r\n    return texts.reduce(((_ref3, text) => {\r\n        let [lines, startBox, endBox, stop, lineNumber] = _ref3;\r\n        const line = lines[lines.length - 1];\r\n        if (stop) {\r\n            return [lines, startBox, endBox, stop]\r\n        }\r\n        if (!line || text.line !== lineNumber) {\r\n            text.startBox = startBox = 0;\r\n            lines.push({\r\n                commonLength: text.value.length,\r\n                parts: [text]\r\n            })\r\n        } else {\r\n            text.startBox = startBox;\r\n            if (startBox > ellipsisMaxWidth && \"none\" === options.wordWrap && \"ellipsis\" === options.textOverflow) {\r\n                removeTextSpan(text);\r\n                return [lines, startBox, endBox, stop, lineNumber]\r\n            }\r\n            line.parts.push(text);\r\n            line.commonLength += text.value.length\r\n        }\r\n        text.endBox = endBox = startBox + getTextWidth(text);\r\n        startBox = endBox;\r\n        if (isDefined(maxWidth) && endBox > maxWidth) {\r\n            const wordWrapLines = wordWrap(text, maxWidth, ellipsisMaxWidth, options);\r\n            if (!wordWrapLines.length) {\r\n                lines = [];\r\n                stop = true\r\n            } else {\r\n                lines = lines.concat(wordWrapLines.filter((l => l.parts.length > 0)))\r\n            }\r\n        }\r\n        return [lines, startBox, endBox, stop, text.line]\r\n    }), [\r\n        [], 0, 0, false, 0\r\n    ])[0]\r\n}\r\n\r\nfunction setNewText(text, index) {\r\n    let insertString = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : \"...\";\r\n    const newText = text.value.substr(0, index) + insertString;\r\n    text.value = text.tspan.textContent = newText;\r\n    text.stroke && (text.stroke.textContent = newText);\r\n    if (\"...\" === insertString) {\r\n        text.hasEllipsis = true\r\n    }\r\n}\r\n\r\nfunction removeTextSpan(text) {\r\n    text.tspan.parentNode && text.tspan.parentNode.removeChild(text.tspan);\r\n    text.stroke && text.stroke.parentNode && text.stroke.parentNode.removeChild(text.stroke)\r\n}\r\n\r\nfunction createTextNodes(wrapper, text, isStroked) {\r\n    let items;\r\n    let parsedHtml;\r\n    wrapper._texts = null;\r\n    wrapper.clear();\r\n    if (null === text) {\r\n        return\r\n    }\r\n    text = \"\" + text;\r\n    if (!wrapper.renderer.encodeHtml && (/<[a-z][\\s\\S]*>/i.test(text) || -1 !== text.indexOf(\"&\"))) {\r\n        parsedHtml = removeExtraAttrs(text);\r\n        items = parseHTML(parsedHtml)\r\n    } else if (/\\n/g.test(text)) {\r\n        items = parseMultiline(text)\r\n    } else if (isStroked) {\r\n        items = [{\r\n            value: text.trim(),\r\n            height: 0\r\n        }]\r\n    }\r\n    if (items) {\r\n        if (items.length) {\r\n            wrapper._texts = items;\r\n            if (isStroked) {\r\n                createTspans(items, wrapper.element, \"stroke\")\r\n            }\r\n            createTspans(items, wrapper.element, \"tspan\")\r\n        }\r\n    } else {\r\n        wrapper.element.appendChild(domAdapter.createTextNode(text))\r\n    }\r\n}\r\n\r\nfunction setTextNodeAttribute(item, name, value) {\r\n    item.tspan.setAttribute(name, value);\r\n    item.stroke && item.stroke.setAttribute(name, value)\r\n}\r\n\r\nfunction getItemLineHeight(item, defaultValue) {\r\n    return item.inherits ? maxLengthFontSize(item.height, defaultValue) : item.height || defaultValue\r\n}\r\n\r\nfunction locateTextNodes(wrapper) {\r\n    if (!wrapper._texts) {\r\n        return\r\n    }\r\n    const items = wrapper._texts;\r\n    const x = wrapper._settings.x;\r\n    const lineHeight = wrapper._getLineHeight();\r\n    let i;\r\n    let ii;\r\n    let item = items[0];\r\n    setTextNodeAttribute(item, \"x\", x);\r\n    setTextNodeAttribute(item, \"y\", wrapper._settings.y);\r\n    for (i = 1, ii = items.length; i < ii; ++i) {\r\n        item = items[i];\r\n        if (parseFloat(item.height) >= 0) {\r\n            setTextNodeAttribute(item, \"x\", x);\r\n            const height = getItemLineHeight(item, lineHeight);\r\n            setTextNodeAttribute(item, \"dy\", height)\r\n        }\r\n    }\r\n}\r\n\r\nfunction alignTextNodes(wrapper, alignment) {\r\n    if (!wrapper._texts || \"center\" === alignment) {\r\n        return\r\n    }\r\n    const items = wrapper._texts;\r\n    const direction = \"left\" === alignment ? -1 : 1;\r\n    const maxTextWidth = Math.max.apply(Math, items.map((t => getTextWidth(t))));\r\n    for (let i = 0; i < items.length; i++) {\r\n        const item = items[i];\r\n        const textWidth = getTextWidth(item);\r\n        if (0 !== maxTextWidth && maxTextWidth !== textWidth) {\r\n            setTextNodeAttribute(item, \"dx\", direction * round((maxTextWidth - textWidth) / 2 * 10) / 10)\r\n        }\r\n    }\r\n}\r\n\r\nfunction maxLengthFontSize(fontSize1, fontSize2) {\r\n    const parsedHeight1 = parseFloat(fontSize1);\r\n    const parsedHeight2 = parseFloat(fontSize2);\r\n    const height1 = parsedHeight1 || 12;\r\n    const height2 = parsedHeight2 || 12;\r\n    return height1 > height2 ? !isNaN(parsedHeight1) ? fontSize1 : height1 : !isNaN(parsedHeight2) ? fontSize2 : height2\r\n}\r\n\r\nfunction strokeTextNodes(wrapper) {\r\n    if (!wrapper._texts) {\r\n        return\r\n    }\r\n    const items = wrapper._texts;\r\n    const stroke = wrapper._settings.stroke;\r\n    const strokeWidth = wrapper._settings[\"stroke-width\"];\r\n    const strokeOpacity = wrapper._settings[\"stroke-opacity\"] || 1;\r\n    let tspan;\r\n    let i;\r\n    let ii;\r\n    for (i = 0, ii = items.length; i < ii; ++i) {\r\n        tspan = items[i].stroke;\r\n        tspan.setAttribute(\"stroke\", stroke);\r\n        tspan.setAttribute(\"stroke-width\", strokeWidth);\r\n        tspan.setAttribute(\"stroke-opacity\", strokeOpacity);\r\n        tspan.setAttribute(\"stroke-linejoin\", \"round\")\r\n    }\r\n}\r\n\r\nfunction baseAnimate(that, params, options, complete) {\r\n    options = options || {};\r\n    let key;\r\n    let value;\r\n    const renderer = that.renderer;\r\n    const settings = that._settings;\r\n    const animationParams = {};\r\n    const defaults = {\r\n        translateX: 0,\r\n        translateY: 0,\r\n        scaleX: 1,\r\n        scaleY: 1,\r\n        rotate: 0,\r\n        rotateX: 0,\r\n        rotateY: 0\r\n    };\r\n    if (complete) {\r\n        options.complete = complete\r\n    }\r\n    if (renderer.animationEnabled()) {\r\n        for (key in params) {\r\n            value = params[key];\r\n            if (/^(translate(X|Y)|rotate[XY]?|scale(X|Y))$/i.test(key)) {\r\n                animationParams.transform = animationParams.transform || {\r\n                    from: {},\r\n                    to: {}\r\n                };\r\n                animationParams.transform.from[key] = key in settings ? Number(settings[key].toFixed(3)) : defaults[key];\r\n                animationParams.transform.to[key] = value\r\n            } else if (\"arc\" === key || \"segments\" === key) {\r\n                animationParams[key] = value\r\n            } else {\r\n                animationParams[key] = {\r\n                    from: key in settings ? settings[key] : parseFloat(that.element.getAttribute(key) || 0),\r\n                    to: value\r\n                }\r\n            }\r\n        }\r\n        renderer.animateElement(that, animationParams, extend(extend({}, renderer._animation), options))\r\n    } else {\r\n        options.step && options.step.call(that, 1, 1);\r\n        options.complete && options.complete.call(that);\r\n        that.attr(params)\r\n    }\r\n    return that\r\n}\r\n\r\nfunction pathAnimate(params, options, complete) {\r\n    const that = this;\r\n    const curSegments = that.segments || [];\r\n    let newSegments;\r\n    let endSegments;\r\n    if (that.renderer.animationEnabled() && \"points\" in params) {\r\n        newSegments = buildPathSegments(params.points, that.type);\r\n        endSegments = compensateSegments(curSegments, newSegments, that.type);\r\n        params.segments = {\r\n            from: curSegments,\r\n            to: newSegments,\r\n            end: endSegments\r\n        };\r\n        delete params.points\r\n    }\r\n    return baseAnimate(that, params, options, complete)\r\n}\r\n\r\nfunction arcAnimate(params, options, complete) {\r\n    const settings = this._settings;\r\n    const arcParams = {\r\n        from: {},\r\n        to: {}\r\n    };\r\n    if (this.renderer.animationEnabled() && (\"x\" in params || \"y\" in params || \"innerRadius\" in params || \"outerRadius\" in params || \"startAngle\" in params || \"endAngle\" in params)) {\r\n        arcParams.from.x = settings.x || 0;\r\n        arcParams.from.y = settings.y || 0;\r\n        arcParams.from.innerRadius = settings.innerRadius || 0;\r\n        arcParams.from.outerRadius = settings.outerRadius || 0;\r\n        arcParams.from.startAngle = settings.startAngle || 0;\r\n        arcParams.from.endAngle = settings.endAngle || 0;\r\n        arcParams.to.x = \"x\" in params ? params.x : settings.x;\r\n        delete params.x;\r\n        arcParams.to.y = \"y\" in params ? params.y : settings.y;\r\n        delete params.y;\r\n        arcParams.to.innerRadius = \"innerRadius\" in params ? params.innerRadius : settings.innerRadius;\r\n        delete params.innerRadius;\r\n        arcParams.to.outerRadius = \"outerRadius\" in params ? params.outerRadius : settings.outerRadius;\r\n        delete params.outerRadius;\r\n        arcParams.to.startAngle = \"startAngle\" in params ? params.startAngle : settings.startAngle;\r\n        delete params.startAngle;\r\n        arcParams.to.endAngle = \"endAngle\" in params ? params.endAngle : settings.endAngle;\r\n        delete params.endAngle;\r\n        params.arc = arcParams\r\n    }\r\n    return baseAnimate(this, params, options, complete)\r\n}\r\n\r\nfunction buildLink(target, parameters) {\r\n    const obj = {\r\n        is: false,\r\n        name: parameters.name || parameters,\r\n        after: parameters.after\r\n    };\r\n    if (target) {\r\n        obj.to = target\r\n    } else {\r\n        obj.virtual = true\r\n    }\r\n    return obj\r\n}\r\nexport let SvgElement = function(renderer, tagName, type) {\r\n    const that = this;\r\n    that.renderer = renderer;\r\n    that.element = createElement(tagName);\r\n    that._settings = {};\r\n    that._styles = {};\r\n    if (\"path\" === tagName) {\r\n        that.type = type || \"line\"\r\n    }\r\n};\r\n\r\nfunction removeFuncIriCallback(callback) {\r\n    fixFuncIriCallbacks.remove(callback)\r\n}\r\nSvgElement.prototype = {\r\n    constructor: SvgElement,\r\n    _getJQElement: function() {\r\n        return this._$element || (this._$element = $(this.element))\r\n    },\r\n    _addFixIRICallback: function() {\r\n        const that = this;\r\n        const fn = function() {\r\n            fixFuncIri(that, \"fill\");\r\n            fixFuncIri(that, \"clip-path\");\r\n            fixFuncIri(that, \"filter\")\r\n        };\r\n        that.element._fixFuncIri = fn;\r\n        fn.renderer = that.renderer;\r\n        fixFuncIriCallbacks.add(fn);\r\n        that._addFixIRICallback = function() {}\r\n    },\r\n    _clearChildrenFuncIri: function() {\r\n        const clearChildren = function(element) {\r\n            let i;\r\n            for (i = 0; i < element.childNodes.length; i++) {\r\n                removeFuncIriCallback(element.childNodes[i]._fixFuncIri);\r\n                clearChildren(element.childNodes[i])\r\n            }\r\n        };\r\n        clearChildren(this.element)\r\n    },\r\n    dispose: function() {\r\n        removeFuncIriCallback(this.element._fixFuncIri);\r\n        this._clearChildrenFuncIri();\r\n        this._getJQElement().remove();\r\n        return this\r\n    },\r\n    append: function(parent) {\r\n        (parent || this.renderer.root).element.appendChild(this.element);\r\n        return this\r\n    },\r\n    remove: function() {\r\n        const element = this.element;\r\n        element.parentNode && element.parentNode.removeChild(element);\r\n        return this\r\n    },\r\n    enableLinks: function() {\r\n        this._links = [];\r\n        return this\r\n    },\r\n    virtualLink: function(parameters) {\r\n        linkItem({\r\n            _link: buildLink(null, parameters)\r\n        }, this);\r\n        return this\r\n    },\r\n    linkAfter: function(name) {\r\n        this._linkAfter = name;\r\n        return this\r\n    },\r\n    linkOn: function(target, parameters) {\r\n        this._link = buildLink(target, parameters);\r\n        linkItem(this, target);\r\n        return this\r\n    },\r\n    linkOff: function() {\r\n        unlinkItem(this);\r\n        this._link = null;\r\n        return this\r\n    },\r\n    linkAppend: function() {\r\n        const link = this._link;\r\n        const items = link.to._links;\r\n        let i;\r\n        let next;\r\n        for (i = link.i + 1;\r\n            (next = items[i]) && !next._link.is; ++i) {}\r\n        this._insert(link.to, next);\r\n        link.is = true;\r\n        return this\r\n    },\r\n    _insert: function(parent, next) {\r\n        parent.element.insertBefore(this.element, next ? next.element : null)\r\n    },\r\n    linkRemove: function() {\r\n        this.remove();\r\n        this._link.is = false;\r\n        return this\r\n    },\r\n    clear: function() {\r\n        this._clearChildrenFuncIri();\r\n        this._getJQElement().empty();\r\n        return this\r\n    },\r\n    toBackground: function() {\r\n        const elem = this.element;\r\n        const parent = elem.parentNode;\r\n        parent && parent.insertBefore(elem, parent.firstChild);\r\n        return this\r\n    },\r\n    toForeground: function() {\r\n        const elem = this.element;\r\n        const parent = elem.parentNode;\r\n        parent && parent.appendChild(elem);\r\n        return this\r\n    },\r\n    attr: function(attrs) {\r\n        return baseAttr(this, attrs)\r\n    },\r\n    smartAttr: function(attrs) {\r\n        return this.attr(processHatchingAttrs(this, attrs))\r\n    },\r\n    css: function(styles) {\r\n        return baseCss(this, styles)\r\n    },\r\n    animate: function(params, options, complete) {\r\n        return baseAnimate(this, params, options, complete)\r\n    },\r\n    sharp(pos, sharpDirection) {\r\n        return this.attr({\r\n            sharp: pos || true,\r\n            sharpDirection: sharpDirection\r\n        })\r\n    },\r\n    _applyTransformation() {\r\n        const tr = this._settings;\r\n        let rotateX;\r\n        let rotateY;\r\n        const transformations = [];\r\n        const sharpMode = tr.sharp;\r\n        const trDirection = tr.sharpDirection || 1;\r\n        const strokeOdd = tr[\"stroke-width\"] % 2;\r\n        const correctionX = strokeOdd && (\"h\" === sharpMode || true === sharpMode) ? .5 * trDirection : 0;\r\n        const correctionY = strokeOdd && (\"v\" === sharpMode || true === sharpMode) ? .5 * trDirection : 0;\r\n        transformations.push(\"translate(\" + ((tr.translateX || 0) + correctionX) + \",\" + ((tr.translateY || 0) + correctionY) + \")\");\r\n        if (tr.rotate) {\r\n            if (\"rotateX\" in tr) {\r\n                rotateX = tr.rotateX\r\n            } else {\r\n                rotateX = tr.x\r\n            }\r\n            if (\"rotateY\" in tr) {\r\n                rotateY = tr.rotateY\r\n            } else {\r\n                rotateY = tr.y\r\n            }\r\n            transformations.push(\"rotate(\" + tr.rotate + \",\" + (rotateX || 0) + \",\" + (rotateY || 0) + \")\")\r\n        }\r\n        const scaleXDefined = isDefined(tr.scaleX);\r\n        const scaleYDefined = isDefined(tr.scaleY);\r\n        if (scaleXDefined || scaleYDefined) {\r\n            transformations.push(\"scale(\" + (scaleXDefined ? tr.scaleX : 1) + \",\" + (scaleYDefined ? tr.scaleY : 1) + \")\")\r\n        }\r\n        if (transformations.length) {\r\n            this.element.setAttribute(\"transform\", transformations.join(\" \"))\r\n        }\r\n    },\r\n    move: function(x, y, animate, animOptions) {\r\n        const obj = {};\r\n        isDefined(x) && (obj.translateX = x);\r\n        isDefined(y) && (obj.translateY = y);\r\n        if (!animate) {\r\n            this.attr(obj)\r\n        } else {\r\n            this.animate(obj, animOptions)\r\n        }\r\n        return this\r\n    },\r\n    rotate: function(angle, x, y, animate, animOptions) {\r\n        const obj = {\r\n            rotate: angle || 0\r\n        };\r\n        isDefined(x) && (obj.rotateX = x);\r\n        isDefined(y) && (obj.rotateY = y);\r\n        if (!animate) {\r\n            this.attr(obj)\r\n        } else {\r\n            this.animate(obj, animOptions)\r\n        }\r\n        return this\r\n    },\r\n    _getElementBBox: function() {\r\n        const elem = this.element;\r\n        let bBox;\r\n        try {\r\n            bBox = elem.getBBox && elem.getBBox()\r\n        } catch (e) {}\r\n        return bBox || {\r\n            x: 0,\r\n            y: 0,\r\n            width: elem.offsetWidth || 0,\r\n            height: elem.offsetHeight || 0\r\n        }\r\n    },\r\n    getBBox: function() {\r\n        const transformation = this._settings;\r\n        let bBox = this._getElementBBox();\r\n        if (transformation.rotate) {\r\n            bBox = rotateBBox(bBox, [(\"rotateX\" in transformation ? transformation.rotateX : transformation.x) || 0, (\"rotateY\" in transformation ? transformation.rotateY : transformation.y) || 0], -transformation.rotate)\r\n        } else {\r\n            bBox = normalizeBBox(bBox)\r\n        }\r\n        return bBox\r\n    },\r\n    markup: function() {\r\n        return getSvgMarkup(this.element)\r\n    },\r\n    getOffset: function() {\r\n        return this._getJQElement().offset()\r\n    },\r\n    stopAnimation: function(disableComplete) {\r\n        const animation = this.animation;\r\n        animation && animation.stop(disableComplete);\r\n        return this\r\n    },\r\n    setTitle: function(text) {\r\n        const titleElem = createElement(\"title\");\r\n        titleElem.textContent = text || \"\";\r\n        this.element.appendChild(titleElem)\r\n    },\r\n    removeTitle() {\r\n        detachTitleElements(this.element)\r\n    },\r\n    data: function(obj, val) {\r\n        const elem = this.element;\r\n        let key;\r\n        if (void 0 !== val) {\r\n            elem[obj] = val\r\n        } else {\r\n            for (key in obj) {\r\n                elem[key] = obj[key]\r\n            }\r\n        }\r\n        return this\r\n    },\r\n    on: function() {\r\n        const args = [this._getJQElement()];\r\n        args.push.apply(args, arguments);\r\n        eventsEngine.on.apply(eventsEngine, args);\r\n        return this\r\n    },\r\n    off: function() {\r\n        const args = [this._getJQElement()];\r\n        args.push.apply(args, arguments);\r\n        eventsEngine.off.apply(eventsEngine, args);\r\n        return this\r\n    },\r\n    trigger: function() {\r\n        const args = [this._getJQElement()];\r\n        args.push.apply(args, arguments);\r\n        eventsEngine.trigger.apply(eventsEngine, args);\r\n        return this\r\n    }\r\n};\r\nexport let PathSvgElement = function(renderer, type) {\r\n    SvgElement.call(this, renderer, \"path\", type)\r\n};\r\nPathSvgElement.prototype = objectCreate(SvgElement.prototype);\r\nextend(PathSvgElement.prototype, {\r\n    constructor: PathSvgElement,\r\n    attr: pathAttr,\r\n    animate: pathAnimate\r\n});\r\nexport let ArcSvgElement = function(renderer) {\r\n    SvgElement.call(this, renderer, \"path\", \"arc\")\r\n};\r\nArcSvgElement.prototype = objectCreate(SvgElement.prototype);\r\nextend(ArcSvgElement.prototype, {\r\n    constructor: ArcSvgElement,\r\n    attr: arcAttr,\r\n    animate: arcAnimate\r\n});\r\nexport let RectSvgElement = function(renderer) {\r\n    SvgElement.call(this, renderer, \"rect\")\r\n};\r\nRectSvgElement.prototype = objectCreate(SvgElement.prototype);\r\nextend(RectSvgElement.prototype, {\r\n    constructor: RectSvgElement,\r\n    attr: rectAttr\r\n});\r\nexport let TextSvgElement = function(renderer) {\r\n    SvgElement.call(this, renderer, \"text\");\r\n    this.css({\r\n        \"white-space\": \"pre\"\r\n    })\r\n};\r\nTextSvgElement.prototype = objectCreate(SvgElement.prototype);\r\nextend(TextSvgElement.prototype, {\r\n    constructor: TextSvgElement,\r\n    attr: textAttr,\r\n    css: textCss,\r\n    applyEllipsis: applyEllipsis,\r\n    setMaxSize: setMaxSize,\r\n    restoreText: restoreText,\r\n    _getLineHeight() {\r\n        return !isNaN(parseFloat(this._styles[\"font-size\"])) ? this._styles[\"font-size\"] : 12\r\n    }\r\n});\r\n\r\nfunction updateIndexes(items, k) {\r\n    let i;\r\n    let item;\r\n    for (i = k; item = items[i]; ++i) {\r\n        item._link.i = i\r\n    }\r\n}\r\n\r\nfunction linkItem(target, container) {\r\n    const items = container._links;\r\n    const key = target._link.after = target._link.after || container._linkAfter;\r\n    let i;\r\n    let item;\r\n    if (key) {\r\n        for (i = 0;\r\n            (item = items[i]) && item._link.name !== key; ++i) {}\r\n        if (item) {\r\n            for (++i;\r\n                (item = items[i]) && item._link.after === key; ++i) {}\r\n        }\r\n    } else {\r\n        i = items.length\r\n    }\r\n    items.splice(i, 0, target);\r\n    updateIndexes(items, i)\r\n}\r\n\r\nfunction unlinkItem(target) {\r\n    let i;\r\n    const items = target._link.to._links;\r\n    for (i = 0; items[i] !== target; ++i) {}\r\n    items.splice(i, 1);\r\n    updateIndexes(items, i)\r\n}\r\nexport function Renderer(options) {\r\n    this.root = this._createElement(\"svg\", {\r\n        xmlns: \"http://www.w3.org/2000/svg\",\r\n        version: \"1.1\",\r\n        fill: NONE,\r\n        stroke: NONE,\r\n        \"stroke-width\": 0\r\n    }).attr({\r\n        class: options.cssClass\r\n    }).css({\r\n        \"line-height\": \"normal\",\r\n        \"-moz-user-select\": NONE,\r\n        \"-webkit-user-select\": NONE,\r\n        \"-webkit-tap-highlight-color\": \"rgba(0, 0, 0, 0)\",\r\n        display: \"block\",\r\n        overflow: \"hidden\"\r\n    });\r\n    this._init();\r\n    this.pathModified = !!options.pathModified;\r\n    this._$container = $(options.container);\r\n    this.root.append({\r\n        element: options.container\r\n    });\r\n    this._locker = 0;\r\n    this._backed = false\r\n}\r\nRenderer.prototype = {\r\n    constructor: Renderer,\r\n    _init: function() {\r\n        this._defs = this._createElement(\"defs\").append(this.root);\r\n        this._animationController = new AnimationController(this.root.element);\r\n        this._animation = {\r\n            enabled: true,\r\n            duration: 1e3,\r\n            easing: \"easeOutCubic\"\r\n        }\r\n    },\r\n    setOptions: function(options) {\r\n        this.rtl = !!options.rtl;\r\n        this.encodeHtml = !!options.encodeHtml;\r\n        this.updateAnimationOptions(options.animation || {});\r\n        this.root.attr({\r\n            direction: this.rtl ? \"rtl\" : \"ltr\"\r\n        });\r\n        return this\r\n    },\r\n    _createElement: function(tagName, attr, type) {\r\n        const elem = new SvgElement(this, tagName, type);\r\n        attr && elem.attr(attr);\r\n        return elem\r\n    },\r\n    lock: function() {\r\n        const that = this;\r\n        if (0 === that._locker) {\r\n            that._backed = !that._$container.is(\":visible\");\r\n            if (that._backed) {\r\n                backupRoot(that.root)\r\n            }\r\n        }++that._locker;\r\n        return that\r\n    },\r\n    unlock: function() {\r\n        const that = this;\r\n        --that._locker;\r\n        if (0 === that._locker) {\r\n            if (that._backed) {\r\n                restoreRoot(that.root, that._$container[0])\r\n            }\r\n            that._backed = false\r\n        }\r\n        return that\r\n    },\r\n    resize: function(width, height) {\r\n        if (width >= 0 && height >= 0) {\r\n            this.root.attr({\r\n                width: width,\r\n                height: height\r\n            })\r\n        }\r\n        return this\r\n    },\r\n    dispose: function() {\r\n        const that = this;\r\n        let key;\r\n        that.root.dispose();\r\n        that._defs.dispose();\r\n        that._animationController.dispose();\r\n        fixFuncIriCallbacks.removeByRenderer(that);\r\n        for (key in that) {\r\n            that[key] = null\r\n        }\r\n        return that\r\n    },\r\n    animationEnabled: function() {\r\n        return !!this._animation.enabled\r\n    },\r\n    updateAnimationOptions: function(newOptions) {\r\n        extend(this._animation, newOptions);\r\n        return this\r\n    },\r\n    stopAllAnimations: function(lock) {\r\n        this._animationController[lock ? \"lock\" : \"stop\"]();\r\n        return this\r\n    },\r\n    animateElement: function(element, params, options) {\r\n        this._animationController.animateElement(element, params, options);\r\n        return this\r\n    },\r\n    svg: function() {\r\n        return this.root.markup()\r\n    },\r\n    getRootOffset: function() {\r\n        return this.root.getOffset()\r\n    },\r\n    onEndAnimation: function(endAnimation) {\r\n        this._animationController.onEndAnimation(endAnimation)\r\n    },\r\n    rect: function(x, y, width, height) {\r\n        const elem = new RectSvgElement(this);\r\n        return elem.attr({\r\n            x: x || 0,\r\n            y: y || 0,\r\n            width: width || 0,\r\n            height: height || 0\r\n        })\r\n    },\r\n    simpleRect: function() {\r\n        return this._createElement(\"rect\")\r\n    },\r\n    circle: function(x, y, r) {\r\n        return this._createElement(\"circle\", {\r\n            cx: x || 0,\r\n            cy: y || 0,\r\n            r: r || 0\r\n        })\r\n    },\r\n    g: function() {\r\n        return this._createElement(\"g\")\r\n    },\r\n    image: function(x, y, w, h, href, location) {\r\n        const image = this._createElement(\"image\", {\r\n            x: x || 0,\r\n            y: y || 0,\r\n            width: w || 0,\r\n            height: h || 0,\r\n            preserveAspectRatio: preserveAspectRatioMap[normalizeEnum(location)] || NONE\r\n        });\r\n        image.element.setAttributeNS(\"http://www.w3.org/1999/xlink\", \"href\", href || \"\");\r\n        return image\r\n    },\r\n    path: function(points, type) {\r\n        const elem = new PathSvgElement(this, type);\r\n        return elem.attr({\r\n            points: points || []\r\n        })\r\n    },\r\n    arc: function(x, y, innerRadius, outerRadius, startAngle, endAngle) {\r\n        const elem = new ArcSvgElement(this);\r\n        return elem.attr({\r\n            x: x || 0,\r\n            y: y || 0,\r\n            innerRadius: innerRadius || 0,\r\n            outerRadius: outerRadius || 0,\r\n            startAngle: startAngle || 0,\r\n            endAngle: endAngle || 0\r\n        })\r\n    },\r\n    text: function(text, x, y) {\r\n        const elem = new TextSvgElement(this);\r\n        return elem.attr({\r\n            text: text,\r\n            x: x || 0,\r\n            y: y || 0\r\n        })\r\n    },\r\n    linearGradient: function(stops) {\r\n        let id = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : getNextDefsSvgId();\r\n        let rotationAngle = arguments.length > 2 ? arguments[2] : void 0;\r\n        const gradient = this._createElement(\"linearGradient\", {\r\n            id: id,\r\n            gradientTransform: `rotate(${rotationAngle||0})`\r\n        }).append(this._defs);\r\n        gradient.id = id;\r\n        this._createGradientStops(stops, gradient);\r\n        return gradient\r\n    },\r\n    radialGradient: function(stops, id) {\r\n        const gradient = this._createElement(\"radialGradient\", {\r\n            id: id\r\n        }).append(this._defs);\r\n        this._createGradientStops(stops, gradient);\r\n        return gradient\r\n    },\r\n    _createGradientStops: function(stops, group) {\r\n        stops.forEach((stop => {\r\n            this._createElement(\"stop\", {\r\n                offset: stop.offset,\r\n                \"stop-color\": stop[\"stop-color\"] ?? stop.color,\r\n                \"stop-opacity\": stop.opacity\r\n            }).append(group)\r\n        }))\r\n    },\r\n    pattern: function(color, hatching, _id) {\r\n        hatching = hatching || {};\r\n        const step = hatching.step || 6;\r\n        const stepTo2 = step / 2;\r\n        const stepBy15 = 1.5 * step;\r\n        const id = _id || getNextDefsSvgId();\r\n        const d = \"right\" === normalizeEnum(hatching.direction) ? \"M \" + stepTo2 + \" \" + -stepTo2 + \" L \" + -stepTo2 + \" \" + stepTo2 + \" M 0 \" + step + \" L \" + step + \" 0 M \" + stepBy15 + \" \" + stepTo2 + \" L \" + stepTo2 + \" \" + stepBy15 : \"M 0 0 L \" + step + \" \" + step + \" M \" + -stepTo2 + \" \" + stepTo2 + \" L \" + stepTo2 + \" \" + stepBy15 + \" M \" + stepTo2 + \" \" + -stepTo2 + \" L \" + stepBy15 + \" \" + stepTo2;\r\n        const pattern = this._createElement(\"pattern\", {\r\n            id: id,\r\n            width: step,\r\n            height: step,\r\n            patternUnits: \"userSpaceOnUse\"\r\n        }).append(this._defs);\r\n        pattern.id = id;\r\n        this.rect(0, 0, step, step).attr({\r\n            fill: color,\r\n            opacity: hatching.opacity\r\n        }).append(pattern);\r\n        new PathSvgElement(this).attr({\r\n            d: d,\r\n            \"stroke-width\": hatching.width || 1,\r\n            stroke: color\r\n        }).append(pattern);\r\n        return pattern\r\n    },\r\n    customPattern: function(id, template, width, height) {\r\n        const option = {\r\n            id: id,\r\n            width: width,\r\n            height: height,\r\n            patternContentUnits: \"userSpaceOnUse\",\r\n            patternUnits: this._getPatternUnits(width, height)\r\n        };\r\n        const pattern = this._createElement(\"pattern\", option).append(this._defs);\r\n        template.render({\r\n            container: pattern.element\r\n        });\r\n        return pattern\r\n    },\r\n    _getPatternUnits: function(width, height) {\r\n        if (Number(width) && Number(height)) {\r\n            return \"userSpaceOnUse\"\r\n        }\r\n    },\r\n    _getPointsWithYOffset: function(points, offset) {\r\n        return points.map((function(point, index) {\r\n            if (index % 2 !== 0) {\r\n                return point + offset\r\n            }\r\n            return point\r\n        }))\r\n    },\r\n    clipShape: function(method, methodArgs) {\r\n        const id = getNextDefsSvgId();\r\n        let clipPath = this._createElement(\"clipPath\", {\r\n            id: id\r\n        }).append(this._defs);\r\n        const shape = method.apply(this, methodArgs).append(clipPath);\r\n        shape.id = id;\r\n        shape.remove = function() {\r\n            throw \"Not implemented\"\r\n        };\r\n        shape.dispose = function() {\r\n            clipPath.dispose();\r\n            clipPath = null;\r\n            return this\r\n        };\r\n        return shape\r\n    },\r\n    clipRect(x, y, width, height) {\r\n        return this.clipShape(this.rect, arguments)\r\n    },\r\n    clipCircle(x, y, radius) {\r\n        return this.clipShape(this.circle, arguments)\r\n    },\r\n    shadowFilter: function(x, y, width, height, offsetX, offsetY, blur, color, opacity) {\r\n        const id = getNextDefsSvgId();\r\n        const filter = this._createElement(\"filter\", {\r\n            id: id,\r\n            x: x || 0,\r\n            y: y || 0,\r\n            width: width || 0,\r\n            height: height || 0\r\n        }).append(this._defs);\r\n        const gaussianBlur = this._createElement(\"feGaussianBlur\", {\r\n            in: \"SourceGraphic\",\r\n            result: \"gaussianBlurResult\",\r\n            stdDeviation: blur || 0\r\n        }).append(filter);\r\n        const offset = this._createElement(\"feOffset\", {\r\n            in: \"gaussianBlurResult\",\r\n            result: \"offsetResult\",\r\n            dx: offsetX || 0,\r\n            dy: offsetY || 0\r\n        }).append(filter);\r\n        const flood = this._createElement(\"feFlood\", {\r\n            result: \"floodResult\",\r\n            \"flood-color\": color || \"\",\r\n            \"flood-opacity\": opacity\r\n        }).append(filter);\r\n        const composite = this._createElement(\"feComposite\", {\r\n            in: \"floodResult\",\r\n            in2: \"offsetResult\",\r\n            operator: \"in\",\r\n            result: \"compositeResult\"\r\n        }).append(filter);\r\n        const finalComposite = this._createElement(\"feComposite\", {\r\n            in: \"SourceGraphic\",\r\n            in2: \"compositeResult\",\r\n            operator: \"over\"\r\n        }).append(filter);\r\n        filter.id = id;\r\n        filter.gaussianBlur = gaussianBlur;\r\n        filter.offset = offset;\r\n        filter.flood = flood;\r\n        filter.composite = composite;\r\n        filter.finalComposite = finalComposite;\r\n        filter.attr = function(attrs) {\r\n            const filterAttrs = {};\r\n            const offsetAttrs = {};\r\n            const floodAttrs = {};\r\n            \"x\" in attrs && (filterAttrs.x = attrs.x);\r\n            \"y\" in attrs && (filterAttrs.y = attrs.y);\r\n            \"width\" in attrs && (filterAttrs.width = attrs.width);\r\n            \"height\" in attrs && (filterAttrs.height = attrs.height);\r\n            baseAttr(this, filterAttrs);\r\n            \"blur\" in attrs && this.gaussianBlur.attr({\r\n                stdDeviation: attrs.blur\r\n            });\r\n            \"offsetX\" in attrs && (offsetAttrs.dx = attrs.offsetX);\r\n            \"offsetY\" in attrs && (offsetAttrs.dy = attrs.offsetY);\r\n            this.offset.attr(offsetAttrs);\r\n            \"color\" in attrs && (floodAttrs[\"flood-color\"] = attrs.color);\r\n            \"opacity\" in attrs && (floodAttrs[\"flood-opacity\"] = attrs.opacity);\r\n            this.flood.attr(floodAttrs);\r\n            return this\r\n        };\r\n        return filter\r\n    },\r\n    brightFilter: function(type, slope) {\r\n        const id = getNextDefsSvgId();\r\n        const filter = this._createElement(\"filter\", {\r\n            id: id\r\n        }).append(this._defs);\r\n        const componentTransferElement = this._createElement(\"feComponentTransfer\").append(filter);\r\n        const attrs = {\r\n            type: type,\r\n            slope: slope\r\n        };\r\n        filter.id = id;\r\n        this._createElement(\"feFuncR\", attrs).append(componentTransferElement);\r\n        this._createElement(\"feFuncG\", attrs).append(componentTransferElement);\r\n        this._createElement(\"feFuncB\", attrs).append(componentTransferElement);\r\n        return filter\r\n    },\r\n    getGrayScaleFilter: function() {\r\n        if (this._grayScaleFilter) {\r\n            return this._grayScaleFilter\r\n        }\r\n        const id = getNextDefsSvgId();\r\n        const filter = this._createElement(\"filter\", {\r\n            id: id\r\n        }).append(this._defs);\r\n        this._createElement(\"feColorMatrix\").attr({\r\n            type: \"matrix\",\r\n            values: \"0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 0.6 0\"\r\n        }).append(filter);\r\n        filter.id = id;\r\n        this._grayScaleFilter = filter;\r\n        return filter\r\n    },\r\n    lightenFilter: function(id) {\r\n        const filter = this._createElement(\"filter\", {\r\n            id: id\r\n        }).append(this._defs);\r\n        this._createElement(\"feColorMatrix\", {\r\n            type: \"matrix\",\r\n            values: \"1.3 0 0 0 0 0 1.3 0 0 0 0 0 1.3 0 0 0 0 0 1 0\"\r\n        }).append(filter);\r\n        filter.id = id;\r\n        return filter\r\n    },\r\n    initDefsElements: function() {\r\n        const storage = this._defsElementsStorage = this._defsElementsStorage || {\r\n            byHash: {},\r\n            baseId: getNextDefsSvgId()\r\n        };\r\n        const byHash = storage.byHash;\r\n        let name;\r\n        for (name in byHash) {\r\n            byHash[name].pattern.dispose()\r\n        }\r\n        storage.byHash = {};\r\n        storage.refToHash = {};\r\n        storage.nextId = 0\r\n    },\r\n    drawPattern: function(_ref4, storageId, nextId) {\r\n        let {\r\n            color: color,\r\n            hatching: hatching\r\n        } = _ref4;\r\n        return this.pattern(color, hatching, `${storageId}-hatching-${nextId++}`)\r\n    },\r\n    drawFilter: function(_, storageId, nextId) {\r\n        return this.lightenFilter(`${storageId}-lightening-${nextId++}`)\r\n    },\r\n    lockDefsElements: function(attrs, ref, type) {\r\n        const storage = this._defsElementsStorage;\r\n        let storageItem;\r\n        const hash = \"pattern\" === type ? getHatchingHash(attrs) : LIGHTENING_HASH;\r\n        const method = \"pattern\" === type ? this.drawPattern : this.drawFilter;\r\n        let pattern;\r\n        if (storage.refToHash[ref] !== hash) {\r\n            if (ref) {\r\n                this.releaseDefsElements(ref)\r\n            }\r\n            storageItem = storage.byHash[hash];\r\n            if (!storageItem) {\r\n                pattern = method.call(this, attrs, storage.baseId, storage.nextId++);\r\n                storageItem = storage.byHash[hash] = {\r\n                    pattern: pattern,\r\n                    count: 0\r\n                };\r\n                storage.refToHash[pattern.id] = hash\r\n            }++storageItem.count;\r\n            ref = storageItem.pattern.id\r\n        }\r\n        return ref\r\n    },\r\n    releaseDefsElements: function(ref) {\r\n        const storage = this._defsElementsStorage;\r\n        const hash = storage.refToHash[ref];\r\n        const storageItem = storage.byHash[hash];\r\n        if (storageItem && 0 === --storageItem.count) {\r\n            storageItem.pattern.dispose();\r\n            delete storage.byHash[hash];\r\n            delete storage.refToHash[ref]\r\n        }\r\n    }\r\n};\r\n\r\nfunction getHatchingHash(_ref5) {\r\n    let {\r\n        color: color,\r\n        hatching: hatching\r\n    } = _ref5;\r\n    return \"@\" + color + \"::\" + hatching.step + \":\" + hatching.width + \":\" + hatching.opacity + \":\" + hatching.direction\r\n}\r\nconst fixFuncIriCallbacks = function() {\r\n    let callbacks = [];\r\n    return {\r\n        add: function(fn) {\r\n            callbacks.push(fn)\r\n        },\r\n        remove: function(fn) {\r\n            callbacks = callbacks.filter((function(el) {\r\n                return el !== fn\r\n            }))\r\n        },\r\n        removeByRenderer: function(renderer) {\r\n            callbacks = callbacks.filter((function(el) {\r\n                return el.renderer !== renderer\r\n            }))\r\n        },\r\n        fire: function() {\r\n            callbacks.forEach((function(fn) {\r\n                fn()\r\n            }))\r\n        }\r\n    }\r\n}();\r\nexport const refreshPaths = function() {\r\n    fixFuncIriCallbacks.fire()\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;AACD;AACA;AACA;AAAA;AAGA;AACA;AAAA;AACA;AAAA;AAGA;AAGA;AAOA;AAAA;;;;;;;;;;AAGA,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;AACvB,MAAM,EACF,KAAK,GAAG,EACR,OAAO,KAAK,EACf,GAAG;AACJ,MAAM,sBAAsB;AAC5B,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,qBAAqB;IACvB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,eAAe;IACf,eAAe;IACf,eAAe;IACf,SAAS;IACT,OAAO;IACP,SAAS;IACT,QAAQ;IACR,WAAW;IACX,MAAM;AACV;AACA,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,qBAAqB;AAC3B,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,sBAAsB;AAC5B,MAAM,sBAAsB;AAC5B,MAAM,OAAO;AACb,MAAM,oBAAoB;AAC1B,MAAM,WAAW;AACjB,MAAM,eAAe;IACjB,IAAI,CAAC,OAAO,MAAM,EAAE;QAChB,OAAO,SAAS,KAAK;YACjB,MAAM,IAAI,YAAY;YACtB,EAAE,SAAS,GAAG;YACd,OAAO,IAAI;QACf;IACJ,OAAO;QACH,OAAO,SAAS,KAAK;YACjB,OAAO,OAAO,MAAM,CAAC;QACzB;IACJ;AACJ;AACA,MAAM,WAAW;IACb,QAAQ;IACR,QAAQ;IACR,kBAAkB;AACtB;AACA,MAAM,YAAY,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAG;IACxB,MAAM,kBAAkB,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC;IACjD,gBAAgB,KAAK,CAAC,IAAI,GAAG;IAC7B,gBAAgB,KAAK,CAAC,QAAQ,GAAG;IACjC,OAAO;QACH,iBAAiB;QACjB,eAAe;IACnB;AACJ;AAEA,SAAS,WAAW,IAAI;IACpB,IAAI,MAAM,YAAY,aAAa,EAAE;QACjC,wJAAA,CAAA,UAAU,CAAC,OAAO,GAAG,WAAW,CAAC,YAAY,eAAe;IAChE;IAAC,EAAE,YAAY,aAAa;IAC5B,KAAK,MAAM,CAAC;QACR,SAAS,YAAY,eAAe;IACxC;AACJ;AAEA,SAAS,YAAY,IAAI,EAAE,SAAS;IAChC,KAAK,MAAM,CAAC;QACR,SAAS;IACb;IACA,EAAE,YAAY,aAAa;IAC3B,IAAI,MAAM,YAAY,aAAa,EAAE;QACjC,wJAAA,CAAA,UAAU,CAAC,OAAO,GAAG,WAAW,CAAC,YAAY,eAAe;IAChE;AACJ;AAEA,SAAS,iBAAiB,KAAK;IAC3B,OAAO,SAAS,aAAa,OAAO;AACxC;AAEA,SAAS,cAAc,OAAO;IAC1B,OAAO,wJAAA,CAAA,UAAU,CAAC,eAAe,CAAC,8BAA8B;AACpE;AACO,SAAS,WAAW,EAAE,EAAE,YAAY;IACvC,OAAO,SAAS,KAAK,SAAS,CAAC,eAAe,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,EAAE,IAAI,MAAM,KAAK,MAAM;AAC9G;AAEA,SAAS,OAAO,MAAM,EAAE,MAAM;IAC1B,IAAI;IACJ,IAAK,OAAO,OAAQ;QAChB,MAAM,CAAC,IAAI,GAAG,MAAM,CAAC,IAAI;IAC7B;IACA,OAAO;AACX;AACA,MAAM,yBAAyB;IAC3B,MAAM;IACN,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,WAAW;IACX,QAAQ;IACR,cAAc;IACd,UAAU;IACV,aAAa;IACb,aAAa;AACjB;AACO,SAAS,qBAAqB,OAAO,EAAE,KAAK;IAC/C,IAAI,MAAM,QAAQ,IAAI,WAAW,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,QAAQ,CAAC,SAAS,GAAG;QACtE,QAAQ,OAAO,CAAC,GAAG;QACnB,MAAM,IAAI,GAAG,QAAQ,SAAS,GAAG,QAAQ,QAAQ,CAAC,gBAAgB,CAAC;YAC/D,OAAO,MAAM,IAAI;YACjB,UAAU,MAAM,QAAQ;QAC5B,GAAG,QAAQ,SAAS,EAAE;QACtB,OAAO,MAAM,MAAM;IACvB,OAAO,IAAI,QAAQ,SAAS,EAAE;QAC1B,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,SAAS;QACtD,QAAQ,SAAS,GAAG;QACpB,OAAO,MAAM,MAAM;IACvB,OAAO,IAAI,MAAM,MAAM,EAAE;QACrB,QAAQ,OAAO,CAAC,GAAG;QACnB,MAAM,MAAM,GAAG,QAAQ,OAAO,GAAG,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,CAAC,GAAG,QAAQ,OAAO,EAAE;IAC5F,OAAO,IAAI,QAAQ,OAAO,EAAE;QACxB,QAAQ,QAAQ,CAAC,mBAAmB,CAAC,QAAQ,OAAO;QACpD,QAAQ,OAAO,GAAG;IACtB;IACA,OAAO,MAAM,QAAQ;IACrB,OAAO;AACX;AACA,MAAM,eAAe,SAAS,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ;IAC1H,OAAO;QAAC;QAAK,CAAC,IAAI,SAAS,aAAa,EAAE,OAAO,CAAC;QAAI,CAAC,IAAI,SAAS,aAAa,EAAE,OAAO,CAAC;QAAI;QAAK,OAAO,OAAO,CAAC;QAAI,OAAO,OAAO,CAAC;QAAI;QAAG;QAAU;QAAG,CAAC,IAAI,SAAS,WAAW,EAAE,OAAO,CAAC;QAAI,CAAC,IAAI,SAAS,WAAW,EAAE,OAAO,CAAC;QAAI,WAAW,MAAM;QAAK,CAAC,IAAI,SAAS,WAAW,EAAE,OAAO,CAAC;QAAI,CAAC,IAAI,SAAS,WAAW,EAAE,OAAO,CAAC;QAAI;QAAK,OAAO,OAAO,CAAC;QAAI,OAAO,OAAO,CAAC;QAAI;QAAG;QAAU;QAAG,CAAC,IAAI,SAAS,aAAa,EAAE,OAAO,CAAC;QAAI,CAAC,IAAI,SAAS,aAAa,EAAE,OAAO,CAAC;QAAI;KAAI,CAAC,IAAI,CAAC;AACve;AAEA,SAAS,kBAAkB,MAAM,EAAE,IAAI;IACnC,IAAI,OAAO;QACP;YAAC;YAAK;YAAG;SAAE;KACd;IACD,OAAQ;QACJ,KAAK;YACD,OAAO,kBAAkB;YACzB;QACJ,KAAK;YACD,OAAO,kBAAkB,QAAQ;YACjC;QACJ,KAAK;YACD,OAAO,mBAAmB;YAC1B;QACJ,KAAK;YACD,OAAO,mBAAmB,QAAQ;IAC1C;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACpC,OAAO,cAAc,QAAQ,wBAAwB;AACzD;AAEA,SAAS,mBAAmB,MAAM,EAAE,KAAK;IACrC,OAAO,cAAc,QAAQ,yBAAyB;AAC1D;AAEA,SAAS,cAAc,MAAM,EAAE,kBAAkB,EAAE,KAAK;IACpD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM,OAAO,EAAE;IACf,IAAI,SAAS,CAAC,WAAW,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,YAAY,SAAS,MAAM,EAAE;QAC3E,IAAK,IAAI,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;YACzC,mBAAmB,MAAM,CAAC,EAAE,EAAE,OAAO;QACzC;IACJ,OAAO;QACH,mBAAmB,QAAQ,OAAO;IACtC;IACA,OAAO;AACX;AAEA,SAAS,uBAAuB,MAAM,EAAE,KAAK,EAAE,IAAI;IAC/C,IAAI,IAAI;IACR,MAAM,KAAK,KAAK,MAAM;IACtB,IAAI,IAAI;IACR,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;IAChC,IAAI,IAAI;QACJ,IAAI,KAAK,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YACxB,MAAO,IAAI,IAAK;gBACZ,IAAI,CAAC,IAAI,GAAG;oBAAC;oBAAK,MAAM,CAAC,EAAE,CAAC,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,CAAC;iBAAC;YACjD;QACJ,OAAO;YACH,MAAO,IAAI,IAAK;gBACZ,IAAI,CAAC,IAAI,GAAG;oBAAC;oBAAK,MAAM,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI;iBAAC;YAC/C;QACJ;QACA,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG;IAClB,OAAO;QACH,IAAI,CAAC,EAAE,GAAG;YAAC;YAAK;YAAG;SAAE;IACzB;IACA,SAAS,KAAK,IAAI,CAAC;QAAC;KAAI;IACxB,OAAO;AACX;AAEA,SAAS,wBAAwB,MAAM,EAAE,KAAK,EAAE,IAAI;IAChD,IAAI;IACJ,IAAI,IAAI,KAAK,MAAM;IACnB,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM;IAChC,IAAI,IAAI;QACJ,IAAI,KAAK,MAAM,MAAM,CAAC,EAAE,CAAC,CAAC,EAAE;YACxB,IAAI,CAAC,IAAI,GAAG;gBAAC;gBAAK,MAAM,CAAC,EAAE,CAAC,CAAC;gBAAE,MAAM,CAAC,EAAE,CAAC,CAAC;aAAC;YAC3C,IAAK,IAAI,GAAG,IAAI,IAAK;gBACjB,IAAI,CAAC,IAAI,GAAG;oBAAC;oBAAK,MAAM,CAAC,EAAE,CAAC,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,CAAC;oBAAE,MAAM,CAAC,EAAE,CAAC,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,CAAC;oBAAE,MAAM,CAAC,EAAE,CAAC,CAAC;oBAAE,MAAM,CAAC,IAAI,CAAC,CAAC;iBAAC;YACzG;QACJ,OAAO;YACH,IAAI,CAAC,IAAI,GAAG;gBAAC;gBAAK,MAAM,CAAC,EAAE;gBAAE,MAAM,CAAC,EAAE;aAAC;YACvC,IAAK,IAAI,GAAG,IAAI,IAAK;gBACjB,IAAI,CAAC,IAAI,GAAG;oBAAC;oBAAK,MAAM,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI;oBAAE,MAAM,CAAC,IAAI;iBAAC;YACnG;QACJ;IACJ,OAAO;QACH,IAAI,CAAC,EAAE,GAAG;YAAC;YAAK;YAAG;SAAE;IACzB;IACA,SAAS,KAAK,IAAI,CAAC;QAAC;KAAI;IACxB,OAAO;AACX;AAEA,SAAS,iBAAiB,QAAQ;IAC9B,MAAM,IAAI,EAAE;IACZ,IAAI,IAAI;IACR,IAAI;IACJ,MAAM,KAAK,SAAS,MAAM;IAC1B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,UAAU,QAAQ,CAAC,EAAE;QACrB,IAAK,IAAI,GAAG,KAAK,QAAQ,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;YAC1C,CAAC,CAAC,IAAI,GAAG,OAAO,CAAC,EAAE;QACvB;IACJ;IACA,OAAO,EAAE,IAAI,CAAC;AAClB;AAEA,SAAS,mBAAmB,WAAW,EAAE,WAAW,EAAE,IAAI;IACtD,MAAM,YAAY,YAAY,MAAM;IACpC,MAAM,YAAY,YAAY,MAAM;IACpC,IAAI;IACJ,IAAI;IACJ,MAAM,oBAAoB,CAAC,MAAM,KAAK,OAAO,CAAC,UAAU,wBAAwB;IAChF,IAAI,MAAM,WAAW;QACjB,IAAK,IAAI,GAAG,IAAI,WAAW,IAAK;YAC5B,YAAY,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,CAAC;QAC1C;IACJ,OAAO,IAAI,YAAY,WAAW;QAC9B,kBAAkB,aAAa,aAAa;IAChD,OAAO,IAAI,YAAY,WAAW;QAC9B,sBAAsB,YAAY,KAAK,CAAC;QACxC,kBAAkB,aAAa,aAAa;IAChD;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,QAAQ,EAAE,IAAI;IACvC,MAAM,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IACvC,MAAM,IAAI,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IACvC,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,QAAQ,CAAC,EAAE,GAAG;YACd;QACJ,KAAK;QACL,KAAK;YACD,QAAQ,CAAC,EAAE,GAAG;YACd,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG;YAC1C,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,GAAG;IAClD;AACJ;AAEA,SAAS,sBAAsB,KAAK,EAAE,IAAI,EAAE,IAAI;IAC5C,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK;IAC9C,IAAI,IAAI,MAAM,MAAM;IACpB,oBAAoB,UAAU;IAC9B,MAAO,IAAI,KAAK,MAAM,EAAE,IAAK;QACzB,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC;IAC9B;AACJ;AAEA,SAAS,sBAAsB,KAAK,EAAE,IAAI,EAAE,IAAI;IAC5C,IAAI;IACJ,IAAI;IACJ,MAAM,cAAc,MAAM,MAAM;IAChC,MAAM,aAAa,KAAK,MAAM;IAC9B,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,cAAc,CAAC,IAAI,MAAM,KAAK,CAAC,aAAa,CAAC,IAAI,MAAM,GAAG;QAC3D,IAAI,CAAC,cAAc,CAAC,IAAI,IAAI;QAC5B,OAAO,MAAM,KAAK,CAAC,GAAG,IAAI;QAC1B,aAAa,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC;QACzC,aAAa,MAAM,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,KAAK,CAAC;QACzC,oBAAoB,YAAY;QAChC,oBAAoB,YAAY;QAChC,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,IAAI,GAAG,IAAK;YAC/C,MAAM,MAAM,CAAC,IAAI,GAAG,GAAG;YACvB,MAAM,MAAM,CAAC,IAAI,GAAG,GAAG;QAC3B;IACJ;AACJ;AAEA,SAAS,QAAQ,IAAI,EAAE,MAAM;IACzB,MAAM,aAAa,KAAK,OAAO;IAC/B,IAAI;IACJ,IAAI;IACJ,SAAS,UAAU,CAAC;IACpB,IAAK,OAAO,OAAQ;QAChB,QAAQ,MAAM,CAAC,IAAI;QACnB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YAClB,SAAS,aAAa,OAAO,SAAS,CAAC,kBAAkB,CAAC,IAAI,GAAG,OAAO;YACxE,UAAU,CAAC,IAAI,GAAG,OAAO,QAAQ,QAAQ;QAC7C;IACJ;IACA,IAAK,OAAO,WAAY;QACpB,QAAQ,UAAU,CAAC,IAAI;QACvB,IAAI,OAAO;YACP,KAAK,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG;QAC9B,OAAO,IAAI,SAAS,OAAO;YACvB,KAAK,OAAO,CAAC,KAAK,CAAC,IAAI,GAAG;QAC9B;IACJ;IACA,OAAO;AACX;AAEA,SAAS,WAAW,OAAO,EAAE,SAAS;IAClC,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,KAAK,QAAQ,IAAI,CAAC;IACxB,IAAI,MAAM,CAAC,MAAM,GAAG,OAAO,CAAC,eAAe;QACvC,QAAQ,eAAe,CAAC;QACxB,QAAQ,YAAY,CAAC,WAAW,WAAW,IAAI,QAAQ,QAAQ,CAAC,YAAY;IAChF;AACJ;AAEA,SAAS,SAAS,IAAI,EAAE,KAAK;IACzB,QAAQ,SAAS,CAAC;IAClB,MAAM,WAAW,KAAK,SAAS;IAC/B,MAAM,aAAa,CAAC;IACpB,IAAI;IACJ,IAAI;IACJ,MAAM,OAAO,KAAK,OAAO;IACzB,MAAM,WAAW,KAAK,QAAQ;IAC9B,MAAM,MAAM,SAAS,GAAG;IACxB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,CAAC,iBAAiB,QAAQ;QAC1B,IAAI,SAAS,UAAU;YACnB,OAAO,QAAQ,CAAC,MAAM;QAC1B;QACA,IAAI,SAAS,UAAU;YACnB,OAAO,QAAQ,CAAC,MAAM;QAC1B;QACA,OAAO;IACX;IACA,OAAO,YAAY;IACnB,IAAK,OAAO,WAAY;QACpB,QAAQ,UAAU,CAAC,IAAI;QACvB,IAAI,KAAK,MAAM,OAAO;YAClB;QACJ;QACA,QAAQ,CAAC,IAAI,GAAG;QAChB,IAAI,YAAY,KAAK;YACjB,MAAM;YACN,QAAQ,CAAA;gBACJ,MAAM,MAAM,QAAQ;gBACpB,QAAQ;gBACR,OAAO,MAAM,UAAU;YAC3B,CAAA,CAAE,CAAC,MAAM,IAAI;QACjB,OAAO,IAAI,gBAAgB,KAAK;YAC5B,uBAAuB;YACvB;QACJ,OAAO,IAAI,mBAAmB,KAAK;YAC/B,uBAAuB;QAC3B,OAAO,IAAI,SAAS,CAAC,WAAW,OAAO,gBAAgB,OAAO,aAAa,GAAG,KAAK,MAAM,MAAM,OAAO,CAAC,eAAe;YAClH,KAAK,kBAAkB;YACvB,QAAQ,WAAW,OAAO,SAAS,YAAY;QACnD,OAAO,IAAI,kEAAkE,IAAI,CAAC,MAAM;YACpF,qBAAqB;YACrB;QACJ,OAAO,IAAI,aAAa,IAAI,CAAC,MAAM;YAC/B,qBAAqB;QACzB;QACA,IAAI,SAAS,OAAO;YAChB,KAAK,eAAe,CAAC;QACzB,OAAO;YACH,KAAK,YAAY,CAAC,KAAK;QAC3B;IACJ;IACA,IAAI,wBAAwB,eAAe,UAAU;QACjD,QAAQ,SAAS,SAAS;QAC1B,KAAK,CAAC,iBAAiB,OAAO,KAAK,WAAW,GAAG,QAAQ,CAAC,eAAe,KAAK;QAC9E,MAAM;QACN,QAAQ,SAAS,QAAQ,KAAK,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE;QAC5C,IAAI,OAAO,SAAS,YAAY,SAAS,UAAU,MAAM;YACrD,KAAK,OAAO,CAAC,eAAe,CAAC;QACjC,OAAO;YACH,QAAQ,MAAM,OAAO,CAAC,aAAa,QAAQ,OAAO,CAAC,SAAS,QAAQ,OAAO,CAAC,QAAQ,QAAQ,OAAO,CAAC,MAAM,IAAI,KAAK,CAAC;YACpH,IAAI,MAAM,MAAM;YAChB,MAAO,IAAK;gBACR,KAAK,CAAC,EAAE,GAAG,SAAS,KAAK,CAAC,EAAE,IAAI;YACpC;YACA,KAAK,OAAO,CAAC,YAAY,CAAC,KAAK,MAAM,IAAI,CAAC;QAC9C;IACJ;IACA,IAAI,oBAAoB;QACpB,KAAK,oBAAoB;IAC7B;IACA,OAAO;AACX;AAEA,SAAS,SAAS,KAAK;IACnB,MAAM,OAAO,IAAI;IACjB,IAAI;IACJ,IAAI,iBAAiB,QAAQ;QACzB,QAAQ,OAAO,CAAC,GAAG;QACnB,WAAW,MAAM,QAAQ;QACzB,IAAI,YAAY,OAAO;YACnB,WAAW,kBAAkB,MAAM,MAAM,EAAE,KAAK,IAAI;YACpD,OAAO,MAAM,MAAM;QACvB;QACA,IAAI,UAAU;YACV,MAAM,CAAC,GAAG,iBAAiB;YAC3B,KAAK,QAAQ,GAAG;YAChB,OAAO,MAAM,QAAQ;QACzB;IACJ;IACA,OAAO,SAAS,MAAM;AAC1B;AAEA,SAAS,QAAQ,KAAK;IAClB,MAAM,WAAW,IAAI,CAAC,SAAS;IAC/B,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,iBAAiB,QAAQ;QACzB,QAAQ,OAAO,CAAC,GAAG;QACnB,IAAI,OAAO,SAAS,OAAO,SAAS,iBAAiB,SAAS,iBAAiB,SAAS,gBAAgB,SAAS,cAAc,OAAO;YAClI,SAAS,CAAC,GAAG,IAAI,OAAO,QAAQ,MAAM,CAAC,GAAG,SAAS,CAAC;YACpD,OAAO,MAAM,CAAC;YACd,SAAS,CAAC,GAAG,IAAI,OAAO,QAAQ,MAAM,CAAC,GAAG,SAAS,CAAC;YACpD,OAAO,MAAM,CAAC;YACd,SAAS,WAAW,GAAG,cAAc,iBAAiB,QAAQ,MAAM,WAAW,GAAG,SAAS,WAAW;YACtG,OAAO,MAAM,WAAW;YACxB,SAAS,WAAW,GAAG,cAAc,iBAAiB,QAAQ,MAAM,WAAW,GAAG,SAAS,WAAW;YACtG,OAAO,MAAM,WAAW;YACxB,SAAS,UAAU,GAAG,aAAa,gBAAgB,QAAQ,MAAM,UAAU,GAAG,SAAS,UAAU;YACjG,OAAO,MAAM,UAAU;YACvB,SAAS,QAAQ,GAAG,WAAW,cAAc,QAAQ,MAAM,QAAQ,GAAG,SAAS,QAAQ;YACvF,OAAO,MAAM,QAAQ;YACrB,MAAM,CAAC,GAAG,aAAa,KAAK,CAAC,MAAM,CAAA,GAAA,yJAAA,CAAA,qBAAkB,AAAD,EAAE,GAAG,GAAG,aAAa,aAAa,YAAY;QACtG;IACJ;IACA,OAAO,SAAS,IAAI,EAAE;AAC1B;AAEA,SAAS,SAAS,KAAK;IACnB,MAAM,OAAO,IAAI;IACjB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,iBAAiB,QAAQ;QACzB,QAAQ,OAAO,CAAC,GAAG;QACnB,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM,KAAK,IAAI,KAAK,MAAM,MAAM,MAAM,IAAI,KAAK,MAAM,KAAK,CAAC,eAAe,EAAE;YACnI,KAAK,MAAM,MAAM,CAAC,GAAG,IAAI,KAAK,UAAU,GAAG,MAAM,CAAC,GAAG,IAAI,KAAK,UAAU,IAAI;YAC5E,KAAK,MAAM,MAAM,CAAC,GAAG,IAAI,KAAK,UAAU,GAAG,MAAM,CAAC,GAAG,IAAI,KAAK,UAAU,IAAI;YAC5E,KAAK,MAAM,MAAM,KAAK,GAAG,QAAQ,KAAK,cAAc,GAAG,MAAM,KAAK,GAAG,QAAQ,KAAK,cAAc,IAAI;YACpG,KAAK,MAAM,MAAM,MAAM,GAAG,SAAS,KAAK,eAAe,GAAG,MAAM,MAAM,GAAG,SAAS,KAAK,eAAe,IAAI;YAC1G,KAAK,MAAM,KAAK,CAAC,eAAe,GAAG,KAAK,KAAK,WAAW,GAAG,KAAK,CAAC,eAAe,GAAG,KAAK,KAAK,WAAW;YACxG,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,SAAS,QAAQ,MAAM,IAAI,CAAC;YAChD,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,MAAM,IAAI;YACtC,MAAM,CAAC,GAAG,IAAI,QAAQ;YACtB,MAAM,CAAC,GAAG,IAAI,QAAQ;YACtB,MAAM,KAAK,GAAG,QAAQ;YACtB,MAAM,MAAM,GAAG,SAAS;YACxB,CAAC,CAAC,MAAM,CAAC,MAAM,SAAS,CAAC,CAAC,MAAM,SAAS,KAAK,MAAM,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,eAAe,GAAG,KAAK;QAC9F;QACA,IAAI,WAAW,OAAO;YAClB,OAAO,MAAM,KAAK;QACtB;IACJ;IACA,OAAO,SAAS,MAAM;AAC1B;AAEA,SAAS,SAAS,KAAK;IACnB,MAAM,OAAO,IAAI;IACjB,IAAI;IACJ,IAAI,CAAC,iBAAiB,QAAQ;QAC1B,OAAO,SAAS,MAAM;IAC1B;IACA,QAAQ,OAAO,CAAC,GAAG;IACnB,MAAM,WAAW,KAAK,SAAS;IAC/B,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,eAAe;IACnF,IAAI,KAAK,MAAM,MAAM,IAAI,EAAE;QACvB,SAAS,IAAI,GAAG,MAAM,IAAI;QAC1B,OAAO,MAAM,IAAI;QACjB,kBAAkB;IACtB;IACA,IAAI,KAAK,MAAM,MAAM,MAAM,EAAE;QACzB,SAAS,MAAM,GAAG,MAAM,MAAM;QAC9B,OAAO,MAAM,MAAM;IACvB;IACA,IAAI,KAAK,MAAM,KAAK,CAAC,eAAe,EAAE;QAClC,QAAQ,CAAC,eAAe,GAAG,KAAK,CAAC,eAAe;QAChD,OAAO,KAAK,CAAC,eAAe;IAChC;IACA,IAAI,KAAK,MAAM,KAAK,CAAC,iBAAiB,EAAE;QACpC,QAAQ,CAAC,iBAAiB,GAAG,KAAK,CAAC,iBAAiB;QACpD,OAAO,KAAK,CAAC,iBAAiB;IAClC;IACA,IAAI,KAAK,MAAM,MAAM,cAAc,EAAE;QACjC,eAAe,MAAM,MAAM,cAAc;QACzC,OAAO,MAAM,cAAc;IAC/B;IACA,MAAM,YAAY,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,MAAM,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,eAAe;IAClF,SAAS,MAAM;IACf,kBAAkB,mBAAmB,cAAc,cAAc,SAAS,IAAI;IAC9E,IAAI,iBAAiB;QACjB,gBAAgB,MAAM,SAAS,IAAI,EAAE;QACrC,KAAK,YAAY,GAAG;IACxB;IACA,IAAI,mBAAmB,KAAK,MAAM,MAAM,CAAC,IAAI,KAAK,MAAM,MAAM,CAAC,EAAE;QAC7D,gBAAgB;IACpB;IACA,IAAI,WAAW;QACX,gBAAgB;IACpB;IACA,OAAO;AACX;AAEA,SAAS,QAAQ,MAAM;IACnB,SAAS,UAAU,CAAC;IACpB,QAAQ,IAAI,EAAE;IACd,IAAI,eAAe,QAAQ;QACvB,gBAAgB,IAAI;IACxB;IACA,OAAO,IAAI;AACf;AAEA,SAAS,cAAc,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,WAAW,EAAE,eAAe;IACjE,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK,MAAM,KAAK,SAAS,EAAE;QAC3B,KAAK,IAAI,CAAC;YACN,OAAO,KAAK,SAAS;YACrB,OAAO;YACP,WAAW;YACX,MAAM;YACN,QAAQ,WAAW,CAAC,YAAY,IAAI;QACxC;IACJ,OAAO,IAAI,SAAS,KAAK,OAAO,EAAE;QAC9B,EAAE;IACN,OAAO,IAAI,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC,OAAO;QACvC,OAAO,QAAQ,CAAC,GAAG;QACnB,OAAQ,KAAK,OAAO;YAChB,KAAK;YACL,KAAK;gBACD,KAAK,CAAC,cAAc,GAAG;gBACvB;YACJ,KAAK;YACL,KAAK;gBACD,KAAK,CAAC,aAAa,GAAG;gBACtB;YACJ,KAAK;gBACD,KAAK,CAAC,kBAAkB,GAAG;QACnC;QACA,YAAY,KAAK,KAAK;QACtB,UAAU,KAAK,IAAI,CAAC,MAAM,IAAI,GAAG,UAAU,KAAK;QAChD,UAAU,QAAQ,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,UAAU,QAAQ;QAC9D,UAAU,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG,UAAU,SAAS;QACjE,UAAU,UAAU,IAAI,CAAC,KAAK,CAAC,cAAc,GAAG,UAAU,UAAU;QACpE,UAAU,cAAc,IAAI,CAAC,KAAK,CAAC,kBAAkB,GAAG,UAAU,cAAc;QAChF,IAAK,IAAI,GAAG,QAAQ,KAAK,UAAU,EAAE,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;YACjE,OAAO,cAAc,MAAM,MAAM,KAAK,CAAC,EAAE,EAAE,OAAO,KAAK,SAAS,IAAI;QACxE;IACJ;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,KAAK;IAC5B,IAAI;IACJ,IAAI;IACJ,IAAI,cAAc,KAAK,CAAC,EAAE;IAC1B,IAAI;IACJ,IAAK,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;QACxC,OAAO,KAAK,CAAC,EAAE;QACf,IAAI,KAAK,IAAI,KAAK,YAAY,IAAI,EAAE;YAChC,YAAY,MAAM,GAAG,kBAAkB,YAAY,MAAM,EAAE,KAAK,MAAM;YACtE,YAAY,QAAQ,GAAG,YAAY,QAAQ,IAAI,MAAM,WAAW,KAAK,MAAM;YAC3E,KAAK,MAAM,GAAG;QAClB,OAAO;YACH,cAAc;QAClB;IACJ;AACJ;AAEA,SAAS,iBAAiB,IAAI;IAC1B,MAAM,yBAAyB;IAC/B,OAAO,KAAK,OAAO,CAAC,0CAA2C,SAAS,WAAW,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;QAC3F,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC,2BAA2B,EAAE,EAAE,GAAG,CAAE,SAAS,GAAG;YACjE,OAAO;QACX,GAAI,IAAI,CAAC;QACT,OAAO,KAAK,KAAK;IACrB;AACJ;AAEA,SAAS,UAAU,IAAI;IACnB,MAAM,QAAQ,EAAE;IAChB,MAAM,MAAM,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC;IACrC,IAAI,SAAS,GAAG,KAAK,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,OAAO,SAAS,OAAO,CAAC,WAAW;IACnF,IAAI,gBAAgB,CAAC,gBAAgB,OAAO,CAAE,CAAA;QAC1C,QAAQ,KAAK,GAAG,QAAQ,YAAY,CAAC;QACrC,QAAQ,eAAe,CAAC;IAC5B;IACA,cAAc,OAAO,GAAG,KAAK,CAAC,GAAG;IACjC,kBAAkB;IAClB,OAAO;AACX;AAEA,SAAS,eAAe,IAAI;IACxB,MAAM,QAAQ,KAAK,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC;IAC5C,IAAI,IAAI;IACR,MAAM,QAAQ,EAAE;IAChB,MAAO,IAAI,MAAM,MAAM,EAAE,IAAK;QAC1B,MAAM,IAAI,CAAC;YACP,OAAO,KAAK,CAAC,EAAE,CAAC,IAAI;YACpB,QAAQ;YACR,MAAM;QACV;IACJ;IACA,OAAO;AACX;AAEA,SAAS,aAAa,KAAK,EAAE,OAAO,EAAE,SAAS;IAC3C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;QACxC,OAAO,KAAK,CAAC,EAAE;QACf,IAAI,CAAC,UAAU,GAAG,cAAc;QAChC,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,wJAAA,CAAA,UAAU,CAAC,cAAc,CAAC,KAAK,KAAK;QAChE,KAAK,KAAK,IAAI,QAAQ;YAClB,SAAS,IAAI,CAAC,UAAU;YACxB,SAAS,CAAC;QACd,GAAG,KAAK,KAAK;QACb,KAAK,SAAS,IAAI,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,SAAS,KAAK,SAAS;QACtE,QAAQ,WAAW,CAAC,IAAI,CAAC,UAAU;IACvC;AACJ;AAEA,SAAS;IACL,IAAI,IAAI,CAAC,YAAY,EAAE;QACnB,IAAI,CAAC,IAAI,CAAC;YACN,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI;QAC7B;IACJ;AACJ;AAEA,SAAS,cAAc,QAAQ;IAC3B,MAAM,OAAO,IAAI;IACjB,IAAI;IACJ,IAAI,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,YAAY,IAAI,CAAC;IACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,QAAQ,CAAC,IAAI;IACvF,MAAM,gBAAgB,SAAS,OAAO,GAAG,KAAK;IAC9C,IAAI,KAAK,eAAe,GAAG,KAAK,GAAG,UAAU;QACzC,IAAI,WAAW,gBAAgB,GAAG;YAC9B,WAAW;QACf,OAAO;YACH,YAAY;QAChB;QACA,QAAQ,aAAa,KAAK,OAAO,EAAE,KAAK,MAAM,EAAE;QAChD,IAAK,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;YACxC,YAAY,KAAK,CAAC,EAAE,CAAC,KAAK;YAC1B,IAAI,MAAM,KAAK,CAAC,EAAE,CAAC,YAAY,EAAE;gBAC7B;YACJ;YACA,IAAK,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;gBAC5C,OAAO,SAAS,CAAC,EAAE;gBACnB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,QAAQ,GAAG;oBAC1B,WAAW,MAAM,KAAK,QAAQ;oBAC9B,cAAc;gBAClB,OAAO,IAAI,KAAK,QAAQ,GAAG,UAAU;oBACjC,eAAe;gBACnB;YACJ;QACJ;IACJ;IACA,SAAS,MAAM;IACf,KAAK,YAAY,GAAG;IACpB,OAAO;AACX;AAEA,SAAS,oBAAoB,IAAI;IAC7B,IAAI;IACJ,IAAI,MAAM;QACN,QAAQ,KAAK,SAAS;QACtB,MAAM,eAAe,CAAC;QACtB,MAAM,eAAe,CAAC;IAC1B;IACA,OAAO,SAAS;AACpB;AAEA,SAAS,oBAAoB,OAAO;IAChC,MAAM,gBAAgB,wJAAA,CAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS;IAC3D,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QAC3C,QAAQ,WAAW,CAAC,aAAa,CAAC,EAAE;IACxC;IACA,OAAO;AACX;AAEA,SAAS,4BAA4B,OAAO;IACxC,MAAM,gBAAgB,oBAAoB;IAC1C,OAAO;QACH,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC3C,QAAQ,WAAW,CAAC,aAAa,CAAC,EAAE;QACxC;IACJ;AACJ;AAEA,SAAS,WAAW,QAAQ,EAAE,SAAS;IACnC,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;IAChF,MAAM,OAAO,IAAI;IACjB,IAAI,QAAQ,EAAE;IACd,IAAI,cAAc;IAClB,IAAI,cAAc;IAClB,IAAI,mBAAmB;IACvB,YAAY,IAAI,CAAC;IACjB,MAAM,sBAAsB,4BAA4B,IAAI,CAAC,OAAO;IACpE,MAAM,WAAW,KAAK,QAAQ,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,MAAM,CAAC,KAAK,QAAQ,CAAC,IAAI;IACvF,MAAM,gBAAgB,SAAS,OAAO,GAAG,KAAK;IAC9C,MAAM,EACF,OAAO,KAAK,EACZ,QAAQ,MAAM,EACjB,GAAG,KAAK,eAAe;IACxB,IAAI,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ,YAAY,aAAa,SAAS,SAAS,GAAG;QAC5E,IAAI,WAAW,gBAAgB,GAAG;YAC9B,mBAAmB;QACvB,OAAO;YACH,oBAAoB;QACxB;QACA,QAAQ,mBAAmB,KAAK,OAAO,EAAE,KAAK,MAAM,EAAE,UAAU,kBAAkB;QAClF,QAAQ,aAAa,OAAO,kBAAkB,SAAS,WAAW,WAAW,IAAI,CAAC,cAAc;QAChG,IAAI,CAAC,MAAM,GAAG,MAAM,MAAM,CAAE,CAAC,OAAO,OAAS,MAAM,MAAM,CAAC,KAAK,KAAK,GAAI,EAAE,EAAE,MAAM,CAAE,CAAA,IAAK,OAAO,EAAE,KAAK,EAAG,GAAG,CAAE,CAAA;YAC3G,EAAE,MAAM,IAAI,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,MAAM;YACnD,OAAO;QACX,GAAI,GAAG,CAAE,CAAA;YACL,EAAE,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,EAAE,KAAK;YACtC,OAAO;QACX;QACA,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,IAAI;QAC1C,cAAc;QACd,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,gBAAgB,IAAI;QACxB,OAAO;YACH,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG;YAC3B,cAAc;QAClB;IACJ;IACA,SAAS,MAAM;IACf,KAAK,YAAY,GAAG;IACpB;IACA,OAAO;QACH,UAAU,MAAM,MAAM;QACtB,aAAa;QACb,aAAa;IACjB;AACJ;AAEA,SAAS,oBAAoB,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;IACzD,IAAI;IACJ,IAAI;IACJ,IAAI,YAAY,YAAY,SAAS,UAAU;QAC3C,IAAK,IAAI,GAAG,KAAK,KAAK,KAAK,CAAC,MAAM,EAAE,KAAK,IAAI,EAAE,EAAG;YAC9C,IAAI,WAAW,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,UAAU;gBAC3D,OAAO,IAAI;YACf;QACJ;IACJ;AACJ;AAEA,SAAS,aAAa,IAAI;IACtB,OAAO,KAAK,KAAK,CAAC,MAAM,GAAG,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI;AACrF;AAEA,SAAS,aAAa,OAAO,EAAE,KAAK,EAAE,QAAQ;IAC1C,IAAI,QAAQ,EAAE;IACd,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO;QACP,IAAK,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;YACxC,OAAO,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,KAAK,CAAC,KAAK,IAAI,CAAC,EAAE;gBACnB,KAAK,QAAQ,GAAG,WAAW;gBAC3B,MAAM,IAAI,CAAC;oBACP,cAAc,KAAK,KAAK,CAAC,MAAM;oBAC/B,OAAO;wBAAC;qBAAK;gBACjB;YACJ,OAAO;gBACH,KAAK,QAAQ,GAAG;gBAChB,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC;gBAC5B,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC,YAAY,IAAI,KAAK,KAAK,CAAC,MAAM;YACtD;YACA,SAAS,WAAW,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM;YACtE,KAAK,QAAQ,GAAG,oBAAoB,MAAM,UAAU,UAAU;YAC9D,WAAW;QACf;IACJ,OAAO;QACH,OAAO;YACH,OAAO,QAAQ,WAAW;YAC1B,OAAO;QACX;QACA,KAAK,QAAQ,GAAG,WAAW;QAC3B,SAAS,WAAW,aAAa;QACjC,KAAK,QAAQ,GAAG,oBAAoB,MAAM,UAAU,UAAU;QAC9D,QAAQ;YAAC;gBACL,cAAc,QAAQ,WAAW,CAAC,MAAM;gBACxC,OAAO;oBAAC;iBAAK;YACjB;SAAE;IACN;IACA,OAAO;AACX;AAEA,SAAS,mBAAmB,IAAI,EAAE,QAAQ;IACtC,MAAM,iBAAiB,KAAK,QAAQ,GAAG,IAAI;QAAC;KAAE,GAAG,EAAE;IACnD,MAAM,eAAe,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,MAAM,CAAE,CAAC,SAAS,MAAM;QAC9D,IAAI,QAAQ,MAAM;YACd,QAAQ,IAAI,CAAC;QACjB;QACA,OAAO;IACX,GAAI;IACJ,IAAI,aAAa;IACjB,MAAO,KAAK,MAAM,YAAY,CAAC,aAAa,EAAE,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,YAAY,CAAC,aAAa,EAAE,IAAI,SAAU;QACzI;IACJ;IACA,OAAO,YAAY,CAAC,WAAW;AACnC;AAEA,SAAS,kBAAkB,IAAI,EAAE,QAAQ;IACrC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG,IAAK;QAC5C,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,KAAK,UAAU;YACpE,OAAO;QACX;IACJ;AACJ;AAEA,SAAS,kBAAkB,gBAAgB,EAAE,IAAI;IAC7C,IAAI,EACA,sBAAsB,oBAAoB,EAC7C,GAAG;IACJ,OAAO,wBAAwB,MAAM,mBAAmB,KAAK;AACjE;AAEA,SAAS,YAAY,IAAI,EAAE,gBAAgB,EAAE,OAAO;IAChD,MAAM,WAAW,kBAAkB,kBAAkB;IACrD,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,KAAK,CAAC,UAAU,EAAE;QAC5C,IAAK,IAAI,IAAI,KAAK,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC7C,IAAI,KAAK,QAAQ,GAAG,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,kBAAkB;gBACxE,WAAW,MAAM,GAAG;gBACpB;YACJ,OAAO,IAAI,MAAM,GAAG;gBAChB,WAAW,MAAM,GAAG;YACxB;QACJ;IACJ;AACJ;AAEA,SAAS,SAAS,IAAI,EAAE,QAAQ,EAAE,gBAAgB,EAAE,OAAO,EAAE,kBAAkB;IAC3E,MAAM,YAAY,KAAK,KAAK;IAC5B,IAAI;IACJ,IAAI,WAAW,QAAQ,QAAQ,EAAE;QAC7B,aAAa,aAAa,QAAQ,QAAQ,GAAG,mBAAmB,MAAM,YAAY,kBAAkB,MAAM;IAC9G;IACA,IAAI,YAAY,EAAE;IAClB,IAAI;IACJ,IAAI,SAAS,eAAe,CAAC,CAAC,MAAM,sBAAsB,MAAM,UAAU,GAAG;QACzE,WAAW,MAAM,YAAY;QAC7B,MAAM,gBAAgB,QAAQ,SAAS,CAAC,WAAW,GAAG,IAAI;QAC1D,MAAM,aAAa,UAAU,KAAK,CAAC,aAAa;QAChD,IAAI,WAAW,MAAM,EAAE;YACnB,MAAM,YAAY,oBAAoB,KAAK,KAAK;YAChD,UAAU,WAAW,GAAG;YACxB,KAAK,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC;YAClC,WAAW,OAAO,OAAO,CAAC,GAAG,OAAO;gBAChC,OAAO;gBACP,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,QAAQ,oBAAoB,KAAK,MAAM;gBACvC,QAAQ,UAAU,kBAAkB,CAAC,GAAG,WAAW,MAAM;YAC7D;YACA,SAAS,MAAM,IAAI,CAAC,SAAS,MAAM,CAAC,WAAW,GAAG,UAAU;YAC5D,IAAI,SAAS,MAAM,GAAG,UAAU;gBAC5B,YAAY,SAAS,UAAU,UAAU,kBAAkB,SAAS;gBACpE,IAAI,CAAC,UAAU,MAAM,EAAE;oBACnB,OAAO,EAAE;gBACb;YACJ;QACJ;IACJ;IACA,IAAI,KAAK,KAAK,CAAC,MAAM,EAAE;QACnB,IAAI,eAAe,QAAQ,YAAY,IAAI,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,UAAU;YACvG,YAAY,MAAM,kBAAkB;QACxC;QACA,IAAI,WAAW,QAAQ,YAAY,IAAI,KAAK,KAAK,CAAC,kBAAkB,CAAC,GAAG,KAAK,KAAK,CAAC,MAAM,IAAI,UAAU;YACnG,OAAO,EAAE;QACb;IACJ,OAAO;QACH,KAAK,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,KAAK;IAChD;IACA,MAAM,QAAQ,EAAE;IAChB,IAAI,UAAU;QACV,MAAM,IAAI,CAAC;IACf;IACA,OAAO;QAAC;YACJ,cAAc,UAAU,MAAM;YAC9B,OAAO;QACX;KAAE,CAAC,MAAM,CAAC;AACd;AAEA,SAAS,oBAAoB,IAAI,EAAE,UAAU;IACzC,OAAO,KAAK,KAAK,CAAC,MAAM,CAAE,CAAC,QAAQ,OAAS,IAAI,QAAQ,kBAAkB,MAAM,cAAe;AACnG;AAEA,SAAS,aAAa,KAAK,EAAE,gBAAgB,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU;IACzE,MAAM,eAAe,QAAQ,YAAY;IACzC,IAAI,CAAC,SAAS,cAAc,MAAM,OAAO,cAAc,WAAW,cAAc;QAC5E,OAAO;IACX;IACA,MAAM,SAAS,MAAM,MAAM,CAAE,CAAC,OAAO,GAAG,OAAO;QAC3C,IAAI,CAAC,OAAO,aAAa,GAAG;QAC5B,MAAM,SAAS,oBAAoB,GAAG;QACtC,gBAAgB;QAChB,IAAI,eAAe,WAAW;YAC1B,MAAM,IAAI,CAAC;QACf,OAAO;YACH,EAAE,KAAK,CAAC,OAAO,CAAE,CAAA;gBACb,eAAe;YACnB;YACA,IAAI,eAAe,cAAc;gBAC7B,MAAM,WAAW,GAAG,CAAC,QAAQ,EAAE;gBAC/B,IAAI,UAAU;oBACV,MAAM,OAAO,SAAS,KAAK,CAAC,SAAS,KAAK,CAAC,MAAM,GAAG,EAAE;oBACtD,IAAI,CAAC,KAAK,WAAW,EAAE;wBACnB,IAAI,MAAM,oBAAoB,KAAK,MAAM,GAAG,kBAAkB;4BAC1D,WAAW,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE,kBAAkB,kBAAkB;wBAC5E,OAAO;4BACH,YAAY,MAAM,kBAAkB;wBACxC;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO;YAAC;YAAO;SAAa;IAChC,GAAI;QACA,EAAE;QAAE;KACP;IACD,IAAI,WAAW,gBAAgB,MAAM,CAAC,EAAE,GAAG,WAAW;QAClD,MAAM,CAAC,EAAE,CAAC,OAAO,CAAE,CAAA;YACf,EAAE,KAAK,CAAC,OAAO,CAAE,CAAA;gBACb,eAAe;YACnB;QACJ;QACA,OAAO,EAAE;IACb;IACA,OAAO,MAAM,CAAC,EAAE;AACpB;AAEA,SAAS,mBAAmB,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE,gBAAgB,EAAE,OAAO;IAC3E,IAAI,CAAC,OAAO;QACR,MAAM,YAAY,QAAQ,WAAW;QACrC,MAAM,OAAO;YACT,OAAO;YACP,QAAQ;YACR,MAAM;QACV;QACA,QAAQ,WAAW,GAAG;QACtB,aAAa;YAAC;SAAK,EAAE,SAAS;QAC9B,QAAQ;YAAC;SAAK;IAClB;IACA,OAAO,MAAM,MAAM,CAAE,CAAC,OAAO;QACzB,IAAI,CAAC,OAAO,UAAU,QAAQ,MAAM,WAAW,GAAG;QAClD,MAAM,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QACpC,IAAI,MAAM;YACN,OAAO;gBAAC;gBAAO;gBAAU;gBAAQ;aAAK;QAC1C;QACA,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,YAAY;YACnC,KAAK,QAAQ,GAAG,WAAW;YAC3B,MAAM,IAAI,CAAC;gBACP,cAAc,KAAK,KAAK,CAAC,MAAM;gBAC/B,OAAO;oBAAC;iBAAK;YACjB;QACJ,OAAO;YACH,KAAK,QAAQ,GAAG;YAChB,IAAI,WAAW,oBAAoB,WAAW,QAAQ,QAAQ,IAAI,eAAe,QAAQ,YAAY,EAAE;gBACnG,eAAe;gBACf,OAAO;oBAAC;oBAAO;oBAAU;oBAAQ;oBAAM;iBAAW;YACtD;YACA,KAAK,KAAK,CAAC,IAAI,CAAC;YAChB,KAAK,YAAY,IAAI,KAAK,KAAK,CAAC,MAAM;QAC1C;QACA,KAAK,MAAM,GAAG,SAAS,WAAW,aAAa;QAC/C,WAAW;QACX,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa,SAAS,UAAU;YAC1C,MAAM,gBAAgB,SAAS,MAAM,UAAU,kBAAkB;YACjE,IAAI,CAAC,cAAc,MAAM,EAAE;gBACvB,QAAQ,EAAE;gBACV,OAAO;YACX,OAAO;gBACH,QAAQ,MAAM,MAAM,CAAC,cAAc,MAAM,CAAE,CAAA,IAAK,EAAE,KAAK,CAAC,MAAM,GAAG;YACrE;QACJ;QACA,OAAO;YAAC;YAAO;YAAU;YAAQ;YAAM,KAAK,IAAI;SAAC;IACrD,GAAI;QACA,EAAE;QAAE;QAAG;QAAG;QAAO;KACpB,CAAC,CAAC,EAAE;AACT;AAEA,SAAS,WAAW,IAAI,EAAE,KAAK;IAC3B,IAAI,eAAe,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACpF,MAAM,UAAU,KAAK,KAAK,CAAC,MAAM,CAAC,GAAG,SAAS;IAC9C,KAAK,KAAK,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG;IACtC,KAAK,MAAM,IAAI,CAAC,KAAK,MAAM,CAAC,WAAW,GAAG,OAAO;IACjD,IAAI,UAAU,cAAc;QACxB,KAAK,WAAW,GAAG;IACvB;AACJ;AAEA,SAAS,eAAe,IAAI;IACxB,KAAK,KAAK,CAAC,UAAU,IAAI,KAAK,KAAK,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,KAAK;IACrE,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,UAAU,IAAI,KAAK,MAAM,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK,MAAM;AAC3F;AAEA,SAAS,gBAAgB,OAAO,EAAE,IAAI,EAAE,SAAS;IAC7C,IAAI;IACJ,IAAI;IACJ,QAAQ,MAAM,GAAG;IACjB,QAAQ,KAAK;IACb,IAAI,SAAS,MAAM;QACf;IACJ;IACA,OAAO,KAAK;IACZ,IAAI,CAAC,QAAQ,QAAQ,CAAC,UAAU,IAAI,CAAC,kBAAkB,IAAI,CAAC,SAAS,CAAC,MAAM,KAAK,OAAO,CAAC,IAAI,GAAG;QAC5F,aAAa,iBAAiB;QAC9B,QAAQ,UAAU;IACtB,OAAO,IAAI,MAAM,IAAI,CAAC,OAAO;QACzB,QAAQ,eAAe;IAC3B,OAAO,IAAI,WAAW;QAClB,QAAQ;YAAC;gBACL,OAAO,KAAK,IAAI;gBAChB,QAAQ;YACZ;SAAE;IACN;IACA,IAAI,OAAO;QACP,IAAI,MAAM,MAAM,EAAE;YACd,QAAQ,MAAM,GAAG;YACjB,IAAI,WAAW;gBACX,aAAa,OAAO,QAAQ,OAAO,EAAE;YACzC;YACA,aAAa,OAAO,QAAQ,OAAO,EAAE;QACzC;IACJ,OAAO;QACH,QAAQ,OAAO,CAAC,WAAW,CAAC,wJAAA,CAAA,UAAU,CAAC,cAAc,CAAC;IAC1D;AACJ;AAEA,SAAS,qBAAqB,IAAI,EAAE,IAAI,EAAE,KAAK;IAC3C,KAAK,KAAK,CAAC,YAAY,CAAC,MAAM;IAC9B,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,YAAY,CAAC,MAAM;AAClD;AAEA,SAAS,kBAAkB,IAAI,EAAE,YAAY;IACzC,OAAO,KAAK,QAAQ,GAAG,kBAAkB,KAAK,MAAM,EAAE,gBAAgB,KAAK,MAAM,IAAI;AACzF;AAEA,SAAS,gBAAgB,OAAO;IAC5B,IAAI,CAAC,QAAQ,MAAM,EAAE;QACjB;IACJ;IACA,MAAM,QAAQ,QAAQ,MAAM;IAC5B,MAAM,IAAI,QAAQ,SAAS,CAAC,CAAC;IAC7B,MAAM,aAAa,QAAQ,cAAc;IACzC,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO,KAAK,CAAC,EAAE;IACnB,qBAAqB,MAAM,KAAK;IAChC,qBAAqB,MAAM,KAAK,QAAQ,SAAS,CAAC,CAAC;IACnD,IAAK,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;QACxC,OAAO,KAAK,CAAC,EAAE;QACf,IAAI,WAAW,KAAK,MAAM,KAAK,GAAG;YAC9B,qBAAqB,MAAM,KAAK;YAChC,MAAM,SAAS,kBAAkB,MAAM;YACvC,qBAAqB,MAAM,MAAM;QACrC;IACJ;AACJ;AAEA,SAAS,eAAe,OAAO,EAAE,SAAS;IACtC,IAAI,CAAC,QAAQ,MAAM,IAAI,aAAa,WAAW;QAC3C;IACJ;IACA,MAAM,QAAQ,QAAQ,MAAM;IAC5B,MAAM,YAAY,WAAW,YAAY,CAAC,IAAI;IAC9C,MAAM,eAAe,KAAK,GAAG,CAAC,KAAK,CAAC,MAAM,MAAM,GAAG,CAAE,CAAA,IAAK,aAAa;IACvE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,MAAM,OAAO,KAAK,CAAC,EAAE;QACrB,MAAM,YAAY,aAAa;QAC/B,IAAI,MAAM,gBAAgB,iBAAiB,WAAW;YAClD,qBAAqB,MAAM,MAAM,YAAY,MAAM,CAAC,eAAe,SAAS,IAAI,IAAI,MAAM;QAC9F;IACJ;AACJ;AAEA,SAAS,kBAAkB,SAAS,EAAE,SAAS;IAC3C,MAAM,gBAAgB,WAAW;IACjC,MAAM,gBAAgB,WAAW;IACjC,MAAM,UAAU,iBAAiB;IACjC,MAAM,UAAU,iBAAiB;IACjC,OAAO,UAAU,UAAU,CAAC,MAAM,iBAAiB,YAAY,UAAU,CAAC,MAAM,iBAAiB,YAAY;AACjH;AAEA,SAAS,gBAAgB,OAAO;IAC5B,IAAI,CAAC,QAAQ,MAAM,EAAE;QACjB;IACJ;IACA,MAAM,QAAQ,QAAQ,MAAM;IAC5B,MAAM,SAAS,QAAQ,SAAS,CAAC,MAAM;IACvC,MAAM,cAAc,QAAQ,SAAS,CAAC,eAAe;IACrD,MAAM,gBAAgB,QAAQ,SAAS,CAAC,iBAAiB,IAAI;IAC7D,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,KAAK,MAAM,MAAM,EAAE,IAAI,IAAI,EAAE,EAAG;QACxC,QAAQ,KAAK,CAAC,EAAE,CAAC,MAAM;QACvB,MAAM,YAAY,CAAC,UAAU;QAC7B,MAAM,YAAY,CAAC,gBAAgB;QACnC,MAAM,YAAY,CAAC,kBAAkB;QACrC,MAAM,YAAY,CAAC,mBAAmB;IAC1C;AACJ;AAEA,SAAS,YAAY,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ;IAChD,UAAU,WAAW,CAAC;IACtB,IAAI;IACJ,IAAI;IACJ,MAAM,WAAW,KAAK,QAAQ;IAC9B,MAAM,WAAW,KAAK,SAAS;IAC/B,MAAM,kBAAkB,CAAC;IACzB,MAAM,WAAW;QACb,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,SAAS;IACb;IACA,IAAI,UAAU;QACV,QAAQ,QAAQ,GAAG;IACvB;IACA,IAAI,SAAS,gBAAgB,IAAI;QAC7B,IAAK,OAAO,OAAQ;YAChB,QAAQ,MAAM,CAAC,IAAI;YACnB,IAAI,6CAA6C,IAAI,CAAC,MAAM;gBACxD,gBAAgB,SAAS,GAAG,gBAAgB,SAAS,IAAI;oBACrD,MAAM,CAAC;oBACP,IAAI,CAAC;gBACT;gBACA,gBAAgB,SAAS,CAAC,IAAI,CAAC,IAAI,GAAG,OAAO,WAAW,OAAO,QAAQ,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,QAAQ,CAAC,IAAI;gBACxG,gBAAgB,SAAS,CAAC,EAAE,CAAC,IAAI,GAAG;YACxC,OAAO,IAAI,UAAU,OAAO,eAAe,KAAK;gBAC5C,eAAe,CAAC,IAAI,GAAG;YAC3B,OAAO;gBACH,eAAe,CAAC,IAAI,GAAG;oBACnB,MAAM,OAAO,WAAW,QAAQ,CAAC,IAAI,GAAG,WAAW,KAAK,OAAO,CAAC,YAAY,CAAC,QAAQ;oBACrF,IAAI;gBACR;YACJ;QACJ;QACA,SAAS,cAAc,CAAC,MAAM,iBAAiB,OAAO,OAAO,CAAC,GAAG,SAAS,UAAU,GAAG;IAC3F,OAAO;QACH,QAAQ,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG;QAC3C,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,IAAI,CAAC;QAC1C,KAAK,IAAI,CAAC;IACd;IACA,OAAO;AACX;AAEA,SAAS,YAAY,MAAM,EAAE,OAAO,EAAE,QAAQ;IAC1C,MAAM,OAAO,IAAI;IACjB,MAAM,cAAc,KAAK,QAAQ,IAAI,EAAE;IACvC,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK,QAAQ,CAAC,gBAAgB,MAAM,YAAY,QAAQ;QACxD,cAAc,kBAAkB,OAAO,MAAM,EAAE,KAAK,IAAI;QACxD,cAAc,mBAAmB,aAAa,aAAa,KAAK,IAAI;QACpE,OAAO,QAAQ,GAAG;YACd,MAAM;YACN,IAAI;YACJ,KAAK;QACT;QACA,OAAO,OAAO,MAAM;IACxB;IACA,OAAO,YAAY,MAAM,QAAQ,SAAS;AAC9C;AAEA,SAAS,WAAW,MAAM,EAAE,OAAO,EAAE,QAAQ;IACzC,MAAM,WAAW,IAAI,CAAC,SAAS;IAC/B,MAAM,YAAY;QACd,MAAM,CAAC;QACP,IAAI,CAAC;IACT;IACA,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,MAAM,CAAC,OAAO,UAAU,OAAO,UAAU,iBAAiB,UAAU,iBAAiB,UAAU,gBAAgB,UAAU,cAAc,MAAM,GAAG;QAC9K,UAAU,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI;QACjC,UAAU,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,IAAI;QACjC,UAAU,IAAI,CAAC,WAAW,GAAG,SAAS,WAAW,IAAI;QACrD,UAAU,IAAI,CAAC,WAAW,GAAG,SAAS,WAAW,IAAI;QACrD,UAAU,IAAI,CAAC,UAAU,GAAG,SAAS,UAAU,IAAI;QACnD,UAAU,IAAI,CAAC,QAAQ,GAAG,SAAS,QAAQ,IAAI;QAC/C,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,SAAS,OAAO,CAAC,GAAG,SAAS,CAAC;QACtD,OAAO,OAAO,CAAC;QACf,UAAU,EAAE,CAAC,CAAC,GAAG,OAAO,SAAS,OAAO,CAAC,GAAG,SAAS,CAAC;QACtD,OAAO,OAAO,CAAC;QACf,UAAU,EAAE,CAAC,WAAW,GAAG,iBAAiB,SAAS,OAAO,WAAW,GAAG,SAAS,WAAW;QAC9F,OAAO,OAAO,WAAW;QACzB,UAAU,EAAE,CAAC,WAAW,GAAG,iBAAiB,SAAS,OAAO,WAAW,GAAG,SAAS,WAAW;QAC9F,OAAO,OAAO,WAAW;QACzB,UAAU,EAAE,CAAC,UAAU,GAAG,gBAAgB,SAAS,OAAO,UAAU,GAAG,SAAS,UAAU;QAC1F,OAAO,OAAO,UAAU;QACxB,UAAU,EAAE,CAAC,QAAQ,GAAG,cAAc,SAAS,OAAO,QAAQ,GAAG,SAAS,QAAQ;QAClF,OAAO,OAAO,QAAQ;QACtB,OAAO,GAAG,GAAG;IACjB;IACA,OAAO,YAAY,IAAI,EAAE,QAAQ,SAAS;AAC9C;AAEA,SAAS,UAAU,MAAM,EAAE,UAAU;IACjC,MAAM,MAAM;QACR,IAAI;QACJ,MAAM,WAAW,IAAI,IAAI;QACzB,OAAO,WAAW,KAAK;IAC3B;IACA,IAAI,QAAQ;QACR,IAAI,EAAE,GAAG;IACb,OAAO;QACH,IAAI,OAAO,GAAG;IAClB;IACA,OAAO;AACX;AACO,IAAI,aAAa,SAAS,QAAQ,EAAE,OAAO,EAAE,IAAI;IACpD,MAAM,OAAO,IAAI;IACjB,KAAK,QAAQ,GAAG;IAChB,KAAK,OAAO,GAAG,cAAc;IAC7B,KAAK,SAAS,GAAG,CAAC;IAClB,KAAK,OAAO,GAAG,CAAC;IAChB,IAAI,WAAW,SAAS;QACpB,KAAK,IAAI,GAAG,QAAQ;IACxB;AACJ;AAEA,SAAS,sBAAsB,QAAQ;IACnC,oBAAoB,MAAM,CAAC;AAC/B;AACA,WAAW,SAAS,GAAG;IACnB,aAAa;IACb,eAAe;QACX,OAAO,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,OAAO,CAAC;IAC9D;IACA,oBAAoB;QAChB,MAAM,OAAO,IAAI;QACjB,MAAM,KAAK;YACP,WAAW,MAAM;YACjB,WAAW,MAAM;YACjB,WAAW,MAAM;QACrB;QACA,KAAK,OAAO,CAAC,WAAW,GAAG;QAC3B,GAAG,QAAQ,GAAG,KAAK,QAAQ;QAC3B,oBAAoB,GAAG,CAAC;QACxB,KAAK,kBAAkB,GAAG,YAAY;IAC1C;IACA,uBAAuB;QACnB,MAAM,gBAAgB,SAAS,OAAO;YAClC,IAAI;YACJ,IAAK,IAAI,GAAG,IAAI,QAAQ,UAAU,CAAC,MAAM,EAAE,IAAK;gBAC5C,sBAAsB,QAAQ,UAAU,CAAC,EAAE,CAAC,WAAW;gBACvD,cAAc,QAAQ,UAAU,CAAC,EAAE;YACvC;QACJ;QACA,cAAc,IAAI,CAAC,OAAO;IAC9B;IACA,SAAS;QACL,sBAAsB,IAAI,CAAC,OAAO,CAAC,WAAW;QAC9C,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,aAAa,GAAG,MAAM;QAC3B,OAAO,IAAI;IACf;IACA,QAAQ,SAAS,MAAM;QACnB,CAAC,UAAU,IAAI,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO;QAC/D,OAAO,IAAI;IACf;IACA,QAAQ;QACJ,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,WAAW,CAAC;QACrD,OAAO,IAAI;IACf;IACA,aAAa;QACT,IAAI,CAAC,MAAM,GAAG,EAAE;QAChB,OAAO,IAAI;IACf;IACA,aAAa,SAAS,UAAU;QAC5B,SAAS;YACL,OAAO,UAAU,MAAM;QAC3B,GAAG,IAAI;QACP,OAAO,IAAI;IACf;IACA,WAAW,SAAS,IAAI;QACpB,IAAI,CAAC,UAAU,GAAG;QAClB,OAAO,IAAI;IACf;IACA,QAAQ,SAAS,MAAM,EAAE,UAAU;QAC/B,IAAI,CAAC,KAAK,GAAG,UAAU,QAAQ;QAC/B,SAAS,IAAI,EAAE;QACf,OAAO,IAAI;IACf;IACA,SAAS;QACL,WAAW,IAAI;QACf,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,IAAI;IACf;IACA,YAAY;QACR,MAAM,OAAO,IAAI,CAAC,KAAK;QACvB,MAAM,QAAQ,KAAK,EAAE,CAAC,MAAM;QAC5B,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,KAAK,CAAC,GAAG,GACd,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,EAAE,EAAG,CAAC;QAC/C,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE;QACtB,KAAK,EAAE,GAAG;QACV,OAAO,IAAI;IACf;IACA,SAAS,SAAS,MAAM,EAAE,IAAI;QAC1B,OAAO,OAAO,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,OAAO,GAAG;IACpE;IACA,YAAY;QACR,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG;QAChB,OAAO,IAAI;IACf;IACA,OAAO;QACH,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,aAAa,GAAG,KAAK;QAC1B,OAAO,IAAI;IACf;IACA,cAAc;QACV,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,MAAM,SAAS,KAAK,UAAU;QAC9B,UAAU,OAAO,YAAY,CAAC,MAAM,OAAO,UAAU;QACrD,OAAO,IAAI;IACf;IACA,cAAc;QACV,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,MAAM,SAAS,KAAK,UAAU;QAC9B,UAAU,OAAO,WAAW,CAAC;QAC7B,OAAO,IAAI;IACf;IACA,MAAM,SAAS,KAAK;QAChB,OAAO,SAAS,IAAI,EAAE;IAC1B;IACA,WAAW,SAAS,KAAK;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC,qBAAqB,IAAI,EAAE;IAChD;IACA,KAAK,SAAS,MAAM;QAChB,OAAO,QAAQ,IAAI,EAAE;IACzB;IACA,SAAS,SAAS,MAAM,EAAE,OAAO,EAAE,QAAQ;QACvC,OAAO,YAAY,IAAI,EAAE,QAAQ,SAAS;IAC9C;IACA,OAAM,GAAG,EAAE,cAAc;QACrB,OAAO,IAAI,CAAC,IAAI,CAAC;YACb,OAAO,OAAO;YACd,gBAAgB;QACpB;IACJ;IACA;QACI,MAAM,KAAK,IAAI,CAAC,SAAS;QACzB,IAAI;QACJ,IAAI;QACJ,MAAM,kBAAkB,EAAE;QAC1B,MAAM,YAAY,GAAG,KAAK;QAC1B,MAAM,cAAc,GAAG,cAAc,IAAI;QACzC,MAAM,YAAY,EAAE,CAAC,eAAe,GAAG;QACvC,MAAM,cAAc,aAAa,CAAC,QAAQ,aAAa,SAAS,SAAS,IAAI,KAAK,cAAc;QAChG,MAAM,cAAc,aAAa,CAAC,QAAQ,aAAa,SAAS,SAAS,IAAI,KAAK,cAAc;QAChG,gBAAgB,IAAI,CAAC,eAAe,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,IAAI,WAAW,IAAI,MAAM,CAAC,CAAC,GAAG,UAAU,IAAI,CAAC,IAAI,WAAW,IAAI;QACxH,IAAI,GAAG,MAAM,EAAE;YACX,IAAI,aAAa,IAAI;gBACjB,UAAU,GAAG,OAAO;YACxB,OAAO;gBACH,UAAU,GAAG,CAAC;YAClB;YACA,IAAI,aAAa,IAAI;gBACjB,UAAU,GAAG,OAAO;YACxB,OAAO;gBACH,UAAU,GAAG,CAAC;YAClB;YACA,gBAAgB,IAAI,CAAC,YAAY,GAAG,MAAM,GAAG,MAAM,CAAC,WAAW,CAAC,IAAI,MAAM,CAAC,WAAW,CAAC,IAAI;QAC/F;QACA,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,GAAG,MAAM;QACzC,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,GAAG,MAAM;QACzC,IAAI,iBAAiB,eAAe;YAChC,gBAAgB,IAAI,CAAC,WAAW,CAAC,gBAAgB,GAAG,MAAM,GAAG,CAAC,IAAI,MAAM,CAAC,gBAAgB,GAAG,MAAM,GAAG,CAAC,IAAI;QAC9G;QACA,IAAI,gBAAgB,MAAM,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,aAAa,gBAAgB,IAAI,CAAC;QAChE;IACJ;IACA,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW;QACrC,MAAM,MAAM,CAAC;QACb,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,IAAI,UAAU,GAAG,CAAC;QACnC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,IAAI,UAAU,GAAG,CAAC;QACnC,IAAI,CAAC,SAAS;YACV,IAAI,CAAC,IAAI,CAAC;QACd,OAAO;YACH,IAAI,CAAC,OAAO,CAAC,KAAK;QACtB;QACA,OAAO,IAAI;IACf;IACA,QAAQ,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,WAAW;QAC9C,MAAM,MAAM;YACR,QAAQ,SAAS;QACrB;QACA,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,IAAI,OAAO,GAAG,CAAC;QAChC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,IAAI,OAAO,GAAG,CAAC;QAChC,IAAI,CAAC,SAAS;YACV,IAAI,CAAC,IAAI,CAAC;QACd,OAAO;YACH,IAAI,CAAC,OAAO,CAAC,KAAK;QACtB;QACA,OAAO,IAAI;IACf;IACA,iBAAiB;QACb,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,IAAI;QACJ,IAAI;YACA,OAAO,KAAK,OAAO,IAAI,KAAK,OAAO;QACvC,EAAE,OAAO,GAAG,CAAC;QACb,OAAO,QAAQ;YACX,GAAG;YACH,GAAG;YACH,OAAO,KAAK,WAAW,IAAI;YAC3B,QAAQ,KAAK,YAAY,IAAI;QACjC;IACJ;IACA,SAAS;QACL,MAAM,iBAAiB,IAAI,CAAC,SAAS;QACrC,IAAI,OAAO,IAAI,CAAC,eAAe;QAC/B,IAAI,eAAe,MAAM,EAAE;YACvB,OAAO,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE,MAAM;gBAAC,CAAC,aAAa,iBAAiB,eAAe,OAAO,GAAG,eAAe,CAAC,KAAK;gBAAG,CAAC,aAAa,iBAAiB,eAAe,OAAO,GAAG,eAAe,CAAC,KAAK;aAAE,EAAE,CAAC,eAAe,MAAM;QACpN,OAAO;YACH,OAAO,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE;QACzB;QACA,OAAO;IACX;IACA,QAAQ;QACJ,OAAO,CAAA,GAAA,4KAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,OAAO;IACpC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,aAAa,GAAG,MAAM;IACtC;IACA,eAAe,SAAS,eAAe;QACnC,MAAM,YAAY,IAAI,CAAC,SAAS;QAChC,aAAa,UAAU,IAAI,CAAC;QAC5B,OAAO,IAAI;IACf;IACA,UAAU,SAAS,IAAI;QACnB,MAAM,YAAY,cAAc;QAChC,UAAU,WAAW,GAAG,QAAQ;QAChC,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;IAC7B;IACA;QACI,oBAAoB,IAAI,CAAC,OAAO;IACpC;IACA,MAAM,SAAS,GAAG,EAAE,GAAG;QACnB,MAAM,OAAO,IAAI,CAAC,OAAO;QACzB,IAAI;QACJ,IAAI,KAAK,MAAM,KAAK;YAChB,IAAI,CAAC,IAAI,GAAG;QAChB,OAAO;YACH,IAAK,OAAO,IAAK;gBACb,IAAI,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;YACxB;QACJ;QACA,OAAO,IAAI;IACf;IACA,IAAI;QACA,MAAM,OAAO;YAAC,IAAI,CAAC,aAAa;SAAG;QACnC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACtB,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,KAAK,CAAC,uLAAA,CAAA,UAAY,EAAE;QACpC,OAAO,IAAI;IACf;IACA,KAAK;QACD,MAAM,OAAO;YAAC,IAAI,CAAC,aAAa;SAAG;QACnC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACtB,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,KAAK,CAAC,uLAAA,CAAA,UAAY,EAAE;QACrC,OAAO,IAAI;IACf;IACA,SAAS;QACL,MAAM,OAAO;YAAC,IAAI,CAAC,aAAa;SAAG;QACnC,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM;QACtB,uLAAA,CAAA,UAAY,CAAC,OAAO,CAAC,KAAK,CAAC,uLAAA,CAAA,UAAY,EAAE;QACzC,OAAO,IAAI;IACf;AACJ;AACO,IAAI,iBAAiB,SAAS,QAAQ,EAAE,IAAI;IAC/C,WAAW,IAAI,CAAC,IAAI,EAAE,UAAU,QAAQ;AAC5C;AACA,eAAe,SAAS,GAAG,aAAa,WAAW,SAAS;AAC5D,OAAO,eAAe,SAAS,EAAE;IAC7B,aAAa;IACb,MAAM;IACN,SAAS;AACb;AACO,IAAI,gBAAgB,SAAS,QAAQ;IACxC,WAAW,IAAI,CAAC,IAAI,EAAE,UAAU,QAAQ;AAC5C;AACA,cAAc,SAAS,GAAG,aAAa,WAAW,SAAS;AAC3D,OAAO,cAAc,SAAS,EAAE;IAC5B,aAAa;IACb,MAAM;IACN,SAAS;AACb;AACO,IAAI,iBAAiB,SAAS,QAAQ;IACzC,WAAW,IAAI,CAAC,IAAI,EAAE,UAAU;AACpC;AACA,eAAe,SAAS,GAAG,aAAa,WAAW,SAAS;AAC5D,OAAO,eAAe,SAAS,EAAE;IAC7B,aAAa;IACb,MAAM;AACV;AACO,IAAI,iBAAiB,SAAS,QAAQ;IACzC,WAAW,IAAI,CAAC,IAAI,EAAE,UAAU;IAChC,IAAI,CAAC,GAAG,CAAC;QACL,eAAe;IACnB;AACJ;AACA,eAAe,SAAS,GAAG,aAAa,WAAW,SAAS;AAC5D,OAAO,eAAe,SAAS,EAAE;IAC7B,aAAa;IACb,MAAM;IACN,KAAK;IACL,eAAe;IACf,YAAY;IACZ,aAAa;IACb;QACI,OAAO,CAAC,MAAM,WAAW,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,IAAI,CAAC,OAAO,CAAC,YAAY,GAAG;IACvF;AACJ;AAEA,SAAS,cAAc,KAAK,EAAE,CAAC;IAC3B,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,OAAO,KAAK,CAAC,EAAE,EAAE,EAAE,EAAG;QAC9B,KAAK,KAAK,CAAC,CAAC,GAAG;IACnB;AACJ;AAEA,SAAS,SAAS,MAAM,EAAE,SAAS;IAC/B,MAAM,QAAQ,UAAU,MAAM;IAC9B,MAAM,MAAM,OAAO,KAAK,CAAC,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK,IAAI,UAAU,UAAU;IAC3E,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK;QACL,IAAK,IAAI,GACL,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,EAAE,EAAG,CAAC;QACxD,IAAI,MAAM;YACN,IAAK,EAAE,GACH,CAAC,OAAO,KAAK,CAAC,EAAE,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,KAAK,EAAE,EAAG,CAAC;QAC7D;IACJ,OAAO;QACH,IAAI,MAAM,MAAM;IACpB;IACA,MAAM,MAAM,CAAC,GAAG,GAAG;IACnB,cAAc,OAAO;AACzB;AAEA,SAAS,WAAW,MAAM;IACtB,IAAI;IACJ,MAAM,QAAQ,OAAO,KAAK,CAAC,EAAE,CAAC,MAAM;IACpC,IAAK,IAAI,GAAG,KAAK,CAAC,EAAE,KAAK,QAAQ,EAAE,EAAG,CAAC;IACvC,MAAM,MAAM,CAAC,GAAG;IAChB,cAAc,OAAO;AACzB;AACO,SAAS,SAAS,OAAO;IAC5B,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO;QACnC,OAAO;QACP,SAAS;QACT,MAAM;QACN,QAAQ;QACR,gBAAgB;IACpB,GAAG,IAAI,CAAC;QACJ,OAAO,QAAQ,QAAQ;IAC3B,GAAG,GAAG,CAAC;QACH,eAAe;QACf,oBAAoB;QACpB,uBAAuB;QACvB,+BAA+B;QAC/B,SAAS;QACT,UAAU;IACd;IACA,IAAI,CAAC,KAAK;IACV,IAAI,CAAC,YAAY,GAAG,CAAC,CAAC,QAAQ,YAAY;IAC1C,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,SAAS;IACtC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QACb,SAAS,QAAQ,SAAS;IAC9B;IACA,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,OAAO,GAAG;AACnB;AACA,SAAS,SAAS,GAAG;IACjB,aAAa;IACb,OAAO;QACH,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,MAAM,CAAC,IAAI,CAAC,IAAI;QACzD,IAAI,CAAC,oBAAoB,GAAG,IAAI,0KAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;QACrE,IAAI,CAAC,UAAU,GAAG;YACd,SAAS;YACT,UAAU;YACV,QAAQ;QACZ;IACJ;IACA,YAAY,SAAS,OAAO;QACxB,IAAI,CAAC,GAAG,GAAG,CAAC,CAAC,QAAQ,GAAG;QACxB,IAAI,CAAC,UAAU,GAAG,CAAC,CAAC,QAAQ,UAAU;QACtC,IAAI,CAAC,sBAAsB,CAAC,QAAQ,SAAS,IAAI,CAAC;QAClD,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;YACX,WAAW,IAAI,CAAC,GAAG,GAAG,QAAQ;QAClC;QACA,OAAO,IAAI;IACf;IACA,gBAAgB,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI;QACxC,MAAM,OAAO,IAAI,WAAW,IAAI,EAAE,SAAS;QAC3C,QAAQ,KAAK,IAAI,CAAC;QAClB,OAAO;IACX;IACA,MAAM;QACF,MAAM,OAAO,IAAI;QACjB,IAAI,MAAM,KAAK,OAAO,EAAE;YACpB,KAAK,OAAO,GAAG,CAAC,KAAK,WAAW,CAAC,EAAE,CAAC;YACpC,IAAI,KAAK,OAAO,EAAE;gBACd,WAAW,KAAK,IAAI;YACxB;QACJ;QAAC,EAAE,KAAK,OAAO;QACf,OAAO;IACX;IACA,QAAQ;QACJ,MAAM,OAAO,IAAI;QACjB,EAAE,KAAK,OAAO;QACd,IAAI,MAAM,KAAK,OAAO,EAAE;YACpB,IAAI,KAAK,OAAO,EAAE;gBACd,YAAY,KAAK,IAAI,EAAE,KAAK,WAAW,CAAC,EAAE;YAC9C;YACA,KAAK,OAAO,GAAG;QACnB;QACA,OAAO;IACX;IACA,QAAQ,SAAS,KAAK,EAAE,MAAM;QAC1B,IAAI,SAAS,KAAK,UAAU,GAAG;YAC3B,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;gBACX,OAAO;gBACP,QAAQ;YACZ;QACJ;QACA,OAAO,IAAI;IACf;IACA,SAAS;QACL,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,KAAK,IAAI,CAAC,OAAO;QACjB,KAAK,KAAK,CAAC,OAAO;QAClB,KAAK,oBAAoB,CAAC,OAAO;QACjC,oBAAoB,gBAAgB,CAAC;QACrC,IAAK,OAAO,KAAM;YACd,IAAI,CAAC,IAAI,GAAG;QAChB;QACA,OAAO;IACX;IACA,kBAAkB;QACd,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO;IACpC;IACA,wBAAwB,SAAS,UAAU;QACvC,OAAO,IAAI,CAAC,UAAU,EAAE;QACxB,OAAO,IAAI;IACf;IACA,mBAAmB,SAAS,IAAI;QAC5B,IAAI,CAAC,oBAAoB,CAAC,OAAO,SAAS,OAAO;QACjD,OAAO,IAAI;IACf;IACA,gBAAgB,SAAS,OAAO,EAAE,MAAM,EAAE,OAAO;QAC7C,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC,SAAS,QAAQ;QAC1D,OAAO,IAAI;IACf;IACA,KAAK;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM;IAC3B;IACA,eAAe;QACX,OAAO,IAAI,CAAC,IAAI,CAAC,SAAS;IAC9B;IACA,gBAAgB,SAAS,YAAY;QACjC,IAAI,CAAC,oBAAoB,CAAC,cAAc,CAAC;IAC7C;IACA,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;QAC9B,MAAM,OAAO,IAAI,eAAe,IAAI;QACpC,OAAO,KAAK,IAAI,CAAC;YACb,GAAG,KAAK;YACR,GAAG,KAAK;YACR,OAAO,SAAS;YAChB,QAAQ,UAAU;QACtB;IACJ;IACA,YAAY;QACR,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA,QAAQ,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;QACpB,OAAO,IAAI,CAAC,cAAc,CAAC,UAAU;YACjC,IAAI,KAAK;YACT,IAAI,KAAK;YACT,GAAG,KAAK;QACZ;IACJ;IACA,GAAG;QACC,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA,OAAO,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,QAAQ;QACtC,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC,SAAS;YACvC,GAAG,KAAK;YACR,GAAG,KAAK;YACR,OAAO,KAAK;YACZ,QAAQ,KAAK;YACb,qBAAqB,sBAAsB,CAAC,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,IAAI;QAC5E;QACA,MAAM,OAAO,CAAC,cAAc,CAAC,gCAAgC,QAAQ,QAAQ;QAC7E,OAAO;IACX;IACA,MAAM,SAAS,MAAM,EAAE,IAAI;QACvB,MAAM,OAAO,IAAI,eAAe,IAAI,EAAE;QACtC,OAAO,KAAK,IAAI,CAAC;YACb,QAAQ,UAAU,EAAE;QACxB;IACJ;IACA,KAAK,SAAS,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,QAAQ;QAC9D,MAAM,OAAO,IAAI,cAAc,IAAI;QACnC,OAAO,KAAK,IAAI,CAAC;YACb,GAAG,KAAK;YACR,GAAG,KAAK;YACR,aAAa,eAAe;YAC5B,aAAa,eAAe;YAC5B,YAAY,cAAc;YAC1B,UAAU,YAAY;QAC1B;IACJ;IACA,MAAM,SAAS,IAAI,EAAE,CAAC,EAAE,CAAC;QACrB,MAAM,OAAO,IAAI,eAAe,IAAI;QACpC,OAAO,KAAK,IAAI,CAAC;YACb,MAAM;YACN,GAAG,KAAK;YACR,GAAG,KAAK;QACZ;IACJ;IACA,gBAAgB,SAAS,KAAK;QAC1B,IAAI,KAAK,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QACzF,IAAI,gBAAgB,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;QAC/D,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,kBAAkB;YACnD,IAAI;YACJ,mBAAmB,CAAC,OAAO,EAAE,iBAAe,EAAE,CAAC,CAAC;QACpD,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,SAAS,EAAE,GAAG;QACd,IAAI,CAAC,oBAAoB,CAAC,OAAO;QACjC,OAAO;IACX;IACA,gBAAgB,SAAS,KAAK,EAAE,EAAE;QAC9B,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,kBAAkB;YACnD,IAAI;QACR,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,IAAI,CAAC,oBAAoB,CAAC,OAAO;QACjC,OAAO;IACX;IACA,sBAAsB,SAAS,KAAK,EAAE,KAAK;QACvC,MAAM,OAAO,CAAE,CAAA;YACX,IAAI,CAAC,cAAc,CAAC,QAAQ;gBACxB,QAAQ,KAAK,MAAM;gBACnB,cAAc,IAAI,CAAC,aAAa,IAAI,KAAK,KAAK;gBAC9C,gBAAgB,KAAK,OAAO;YAChC,GAAG,MAAM,CAAC;QACd;IACJ;IACA,SAAS,SAAS,KAAK,EAAE,QAAQ,EAAE,GAAG;QAClC,WAAW,YAAY,CAAC;QACxB,MAAM,OAAO,SAAS,IAAI,IAAI;QAC9B,MAAM,UAAU,OAAO;QACvB,MAAM,WAAW,MAAM;QACvB,MAAM,KAAK,OAAO,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QACjC,MAAM,IAAI,YAAY,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,SAAS,IAAI,OAAO,UAAU,MAAM,CAAC,UAAU,QAAQ,CAAC,UAAU,MAAM,UAAU,UAAU,OAAO,QAAQ,OAAO,UAAU,WAAW,MAAM,UAAU,QAAQ,UAAU,MAAM,WAAW,aAAa,OAAO,MAAM,OAAO,QAAQ,CAAC,UAAU,MAAM,UAAU,QAAQ,UAAU,MAAM,WAAW,QAAQ,UAAU,MAAM,CAAC,UAAU,QAAQ,WAAW,MAAM;QAC1Y,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW;YAC3C,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,cAAc;QAClB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,QAAQ,EAAE,GAAG;QACb,IAAI,CAAC,IAAI,CAAC,GAAG,GAAG,MAAM,MAAM,IAAI,CAAC;YAC7B,MAAM;YACN,SAAS,SAAS,OAAO;QAC7B,GAAG,MAAM,CAAC;QACV,IAAI,eAAe,IAAI,EAAE,IAAI,CAAC;YAC1B,GAAG;YACH,gBAAgB,SAAS,KAAK,IAAI;YAClC,QAAQ;QACZ,GAAG,MAAM,CAAC;QACV,OAAO;IACX;IACA,eAAe,SAAS,EAAE,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM;QAC/C,MAAM,SAAS;YACX,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,qBAAqB;YACrB,cAAc,IAAI,CAAC,gBAAgB,CAAC,OAAO;QAC/C;QACA,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK;QACxE,SAAS,MAAM,CAAC;YACZ,WAAW,QAAQ,OAAO;QAC9B;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,KAAK,EAAE,MAAM;QACpC,IAAI,OAAO,UAAU,OAAO,SAAS;YACjC,OAAO;QACX;IACJ;IACA,uBAAuB,SAAS,MAAM,EAAE,MAAM;QAC1C,OAAO,OAAO,GAAG,CAAE,SAAS,KAAK,EAAE,KAAK;YACpC,IAAI,QAAQ,MAAM,GAAG;gBACjB,OAAO,QAAQ;YACnB;YACA,OAAO;QACX;IACJ;IACA,WAAW,SAAS,MAAM,EAAE,UAAU;QAClC,MAAM,KAAK,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QAC1B,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,YAAY;YAC3C,IAAI;QACR,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,MAAM,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,YAAY,MAAM,CAAC;QACpD,MAAM,EAAE,GAAG;QACX,MAAM,MAAM,GAAG;YACX,MAAM;QACV;QACA,MAAM,OAAO,GAAG;YACZ,SAAS,OAAO;YAChB,WAAW;YACX,OAAO,IAAI;QACf;QACA,OAAO;IACX;IACA,UAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,EAAE;IACrC;IACA,YAAW,CAAC,EAAE,CAAC,EAAE,MAAM;QACnB,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE;IACvC;IACA,cAAc,SAAS,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO;QAC9E,MAAM,KAAK,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QAC1B,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,UAAU;YACzC,IAAI;YACJ,GAAG,KAAK;YACR,GAAG,KAAK;YACR,OAAO,SAAS;YAChB,QAAQ,UAAU;QACtB,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,MAAM,eAAe,IAAI,CAAC,cAAc,CAAC,kBAAkB;YACvD,IAAI;YACJ,QAAQ;YACR,cAAc,QAAQ;QAC1B,GAAG,MAAM,CAAC;QACV,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,YAAY;YAC3C,IAAI;YACJ,QAAQ;YACR,IAAI,WAAW;YACf,IAAI,WAAW;QACnB,GAAG,MAAM,CAAC;QACV,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC,WAAW;YACzC,QAAQ;YACR,eAAe,SAAS;YACxB,iBAAiB;QACrB,GAAG,MAAM,CAAC;QACV,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,eAAe;YACjD,IAAI;YACJ,KAAK;YACL,UAAU;YACV,QAAQ;QACZ,GAAG,MAAM,CAAC;QACV,MAAM,iBAAiB,IAAI,CAAC,cAAc,CAAC,eAAe;YACtD,IAAI;YACJ,KAAK;YACL,UAAU;QACd,GAAG,MAAM,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,OAAO,YAAY,GAAG;QACtB,OAAO,MAAM,GAAG;QAChB,OAAO,KAAK,GAAG;QACf,OAAO,SAAS,GAAG;QACnB,OAAO,cAAc,GAAG;QACxB,OAAO,IAAI,GAAG,SAAS,KAAK;YACxB,MAAM,cAAc,CAAC;YACrB,MAAM,cAAc,CAAC;YACrB,MAAM,aAAa,CAAC;YACpB,OAAO,SAAS,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;YACxC,OAAO,SAAS,CAAC,YAAY,CAAC,GAAG,MAAM,CAAC;YACxC,WAAW,SAAS,CAAC,YAAY,KAAK,GAAG,MAAM,KAAK;YACpD,YAAY,SAAS,CAAC,YAAY,MAAM,GAAG,MAAM,MAAM;YACvD,SAAS,IAAI,EAAE;YACf,UAAU,SAAS,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;gBACtC,cAAc,MAAM,IAAI;YAC5B;YACA,aAAa,SAAS,CAAC,YAAY,EAAE,GAAG,MAAM,OAAO;YACrD,aAAa,SAAS,CAAC,YAAY,EAAE,GAAG,MAAM,OAAO;YACrD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACjB,WAAW,SAAS,CAAC,UAAU,CAAC,cAAc,GAAG,MAAM,KAAK;YAC5D,aAAa,SAAS,CAAC,UAAU,CAAC,gBAAgB,GAAG,MAAM,OAAO;YAClE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAChB,OAAO,IAAI;QACf;QACA,OAAO;IACX;IACA,cAAc,SAAS,IAAI,EAAE,KAAK;QAC9B,MAAM,KAAK,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QAC1B,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,UAAU;YACzC,IAAI;QACR,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,MAAM,2BAA2B,IAAI,CAAC,cAAc,CAAC,uBAAuB,MAAM,CAAC;QACnF,MAAM,QAAQ;YACV,MAAM;YACN,OAAO;QACX;QACA,OAAO,EAAE,GAAG;QACZ,IAAI,CAAC,cAAc,CAAC,WAAW,OAAO,MAAM,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,WAAW,OAAO,MAAM,CAAC;QAC7C,IAAI,CAAC,cAAc,CAAC,WAAW,OAAO,MAAM,CAAC;QAC7C,OAAO;IACX;IACA,oBAAoB;QAChB,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,MAAM,KAAK,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QAC1B,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,UAAU;YACzC,IAAI;QACR,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,IAAI,CAAC,cAAc,CAAC,iBAAiB,IAAI,CAAC;YACtC,MAAM;YACN,QAAQ;QACZ,GAAG,MAAM,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,IAAI,CAAC,gBAAgB,GAAG;QACxB,OAAO;IACX;IACA,eAAe,SAAS,EAAE;QACtB,MAAM,SAAS,IAAI,CAAC,cAAc,CAAC,UAAU;YACzC,IAAI;QACR,GAAG,MAAM,CAAC,IAAI,CAAC,KAAK;QACpB,IAAI,CAAC,cAAc,CAAC,iBAAiB;YACjC,MAAM;YACN,QAAQ;QACZ,GAAG,MAAM,CAAC;QACV,OAAO,EAAE,GAAG;QACZ,OAAO;IACX;IACA,kBAAkB;QACd,MAAM,UAAU,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,IAAI;YACrE,QAAQ,CAAC;YACT,QAAQ,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD;QAC3B;QACA,MAAM,SAAS,QAAQ,MAAM;QAC7B,IAAI;QACJ,IAAK,QAAQ,OAAQ;YACjB,MAAM,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAChC;QACA,QAAQ,MAAM,GAAG,CAAC;QAClB,QAAQ,SAAS,GAAG,CAAC;QACrB,QAAQ,MAAM,GAAG;IACrB;IACA,aAAa,SAAS,KAAK,EAAE,SAAS,EAAE,MAAM;QAC1C,IAAI,EACA,OAAO,KAAK,EACZ,UAAU,QAAQ,EACrB,GAAG;QACJ,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO,UAAU,GAAG,UAAU,UAAU,EAAE,UAAU;IAC5E;IACA,YAAY,SAAS,CAAC,EAAE,SAAS,EAAE,MAAM;QACrC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,UAAU,YAAY,EAAE,UAAU;IACnE;IACA,kBAAkB,SAAS,KAAK,EAAE,GAAG,EAAE,IAAI;QACvC,MAAM,UAAU,IAAI,CAAC,oBAAoB;QACzC,IAAI;QACJ,MAAM,OAAO,cAAc,OAAO,gBAAgB,SAAS;QAC3D,MAAM,SAAS,cAAc,OAAO,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU;QACtE,IAAI;QACJ,IAAI,QAAQ,SAAS,CAAC,IAAI,KAAK,MAAM;YACjC,IAAI,KAAK;gBACL,IAAI,CAAC,mBAAmB,CAAC;YAC7B;YACA,cAAc,QAAQ,MAAM,CAAC,KAAK;YAClC,IAAI,CAAC,aAAa;gBACd,UAAU,OAAO,IAAI,CAAC,IAAI,EAAE,OAAO,QAAQ,MAAM,EAAE,QAAQ,MAAM;gBACjE,cAAc,QAAQ,MAAM,CAAC,KAAK,GAAG;oBACjC,SAAS;oBACT,OAAO;gBACX;gBACA,QAAQ,SAAS,CAAC,QAAQ,EAAE,CAAC,GAAG;YACpC;YAAC,EAAE,YAAY,KAAK;YACpB,MAAM,YAAY,OAAO,CAAC,EAAE;QAChC;QACA,OAAO;IACX;IACA,qBAAqB,SAAS,GAAG;QAC7B,MAAM,UAAU,IAAI,CAAC,oBAAoB;QACzC,MAAM,OAAO,QAAQ,SAAS,CAAC,IAAI;QACnC,MAAM,cAAc,QAAQ,MAAM,CAAC,KAAK;QACxC,IAAI,eAAe,MAAM,EAAE,YAAY,KAAK,EAAE;YAC1C,YAAY,OAAO,CAAC,OAAO;YAC3B,OAAO,QAAQ,MAAM,CAAC,KAAK;YAC3B,OAAO,QAAQ,SAAS,CAAC,IAAI;QACjC;IACJ;AACJ;AAEA,SAAS,gBAAgB,KAAK;IAC1B,IAAI,EACA,OAAO,KAAK,EACZ,UAAU,QAAQ,EACrB,GAAG;IACJ,OAAO,MAAM,QAAQ,OAAO,SAAS,IAAI,GAAG,MAAM,SAAS,KAAK,GAAG,MAAM,SAAS,OAAO,GAAG,MAAM,SAAS,SAAS;AACxH;AACA,MAAM,sBAAsB;IACxB,IAAI,YAAY,EAAE;IAClB,OAAO;QACH,KAAK,SAAS,EAAE;YACZ,UAAU,IAAI,CAAC;QACnB;QACA,QAAQ,SAAS,EAAE;YACf,YAAY,UAAU,MAAM,CAAE,SAAS,EAAE;gBACrC,OAAO,OAAO;YAClB;QACJ;QACA,kBAAkB,SAAS,QAAQ;YAC/B,YAAY,UAAU,MAAM,CAAE,SAAS,EAAE;gBACrC,OAAO,GAAG,QAAQ,KAAK;YAC3B;QACJ;QACA,MAAM;YACF,UAAU,OAAO,CAAE,SAAS,EAAE;gBAC1B;YACJ;QACJ;IACJ;AACJ;AACO,MAAM,eAAe;IACxB,oBAAoB,IAAI;AAC5B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3103, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/plaque.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/plaque.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nconst _excluded = [\"x\", \"y\", \"canvas\", \"offsetX\", \"offsetY\", \"offset\"];\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nconst math = Math;\r\nconst round = math.round;\r\nconst max = math.max;\r\nconst min = math.min;\r\nconst sin = math.sin;\r\nconst cos = math.cos;\r\nconst asin = math.asin;\r\nconst PI = math.PI;\r\nconst buildPath = function() {\r\n    for (var _len = arguments.length, points = new Array(_len), _key = 0; _key < _len; _key++) {\r\n        points[_key] = arguments[_key]\r\n    }\r\n    return points.join(\"\")\r\n};\r\n\r\nfunction getArc(cornerRadius, xDirection, yDirection) {\r\n    return `a ${cornerRadius} ${cornerRadius} 0 0 1 ${xDirection*cornerRadius} ${yDirection*cornerRadius}`\r\n}\r\n\r\nfunction getAbsoluteArc(cornerRadius, x, y) {\r\n    return `A ${cornerRadius} ${cornerRadius} 0 0 1 ${x} ${y}`\r\n}\r\n\r\nfunction rotateX(x, y, angle, x0, y0) {\r\n    return (x - x0) * round(cos(angle)) + (y - y0) * round(sin(angle)) + x0\r\n}\r\n\r\nfunction rotateY(x, y, angle, x0, y0) {\r\n    return -(x - x0) * round(sin(angle)) + (y - y0) * round(cos(angle)) + y0\r\n}\r\n\r\nfunction rotateSize(options, angle) {\r\n    if (angle % 90 === 0 && angle % 180 !== 0) {\r\n        return {\r\n            width: options.height,\r\n            height: options.width\r\n        }\r\n    }\r\n    return options\r\n}\r\n\r\nfunction getCloudAngle(_ref, x, y, anchorX, anchorY) {\r\n    let {\r\n        width: width,\r\n        height: height\r\n    } = _ref;\r\n    const halfWidth = width / 2;\r\n    const halfHeight = height / 2;\r\n    const xr = Math.ceil(x + halfWidth);\r\n    const xl = Math.floor(x - halfWidth);\r\n    const yt = Math.floor(y - halfHeight);\r\n    const yb = Math.ceil(y + halfHeight);\r\n    if (anchorX < xl && anchorY < yt || anchorX >= xl && anchorX <= xr && anchorY < yt) {\r\n        return 270\r\n    }\r\n    if (anchorX > xr && anchorY > yb || anchorX >= xl && anchorX <= xr && anchorY > yb) {\r\n        return 90\r\n    } else if (anchorX < xl && anchorY > yb || anchorX < xl && anchorY >= yt && anchorY <= yb) {\r\n        return 180\r\n    }\r\n    return 0\r\n}\r\n\r\nfunction getCloudPoints(_ref2, x, y, anchorX, anchorY, _ref3, bounded) {\r\n    let {\r\n        width: width,\r\n        height: height\r\n    } = _ref2;\r\n    let {\r\n        arrowWidth: arrowWidth,\r\n        cornerRadius: cornerRadius = 0\r\n    } = _ref3;\r\n    const halfArrowWidth = arrowWidth / 2;\r\n    const halfWidth = width / 2;\r\n    const halfHeight = height / 2;\r\n    const xr = Math.ceil(x + halfWidth);\r\n    const xl = Math.floor(x - halfWidth);\r\n    const yt = Math.floor(y - halfHeight);\r\n    const yb = Math.ceil(y + halfHeight);\r\n    const leftTopCorner = [xl, yt];\r\n    const rightTopCorner = [xr, yt];\r\n    const rightBottomCorner = [xr, yb];\r\n    const leftBottomCorner = [xl, yb];\r\n    const arrowX = anchorX <= xl ? xl : xr <= anchorX ? xr : anchorX;\r\n    const arrowY = anchorY <= yt ? yt : yb <= anchorY ? yb : anchorY;\r\n    const arrowBaseBottom = min(arrowY + halfArrowWidth, yb);\r\n    const arrowBaseTop = max(arrowY - halfArrowWidth, yt);\r\n    const arrowBaseLeft = max(arrowX - halfArrowWidth, xl);\r\n    cornerRadius = Math.min(width / 2, height / 2, cornerRadius);\r\n    let points;\r\n    leftTopCorner[1] += cornerRadius;\r\n    rightTopCorner[0] -= cornerRadius;\r\n    rightBottomCorner[1] -= cornerRadius;\r\n    leftBottomCorner[0] += cornerRadius;\r\n    if (!bounded || xl <= anchorX && anchorX <= xr && yt <= anchorY && anchorY <= yb) {\r\n        points = buildPath(leftTopCorner, getArc(cornerRadius, 1, -1), \"L\", rightTopCorner, getArc(cornerRadius, 1, 1), \"L\", rightBottomCorner, getArc(cornerRadius, -1, 1), \"L\", leftBottomCorner, getArc(cornerRadius, -1, -1))\r\n    } else if (anchorX > xr && anchorY < yt) {\r\n        const arrowAngle = arrowWidth / cornerRadius || 0;\r\n        const angle = PI / 4 + arrowAngle / 2;\r\n        const endAngle = PI / 4 - arrowAngle / 2;\r\n        const arrowEndPointX = rightTopCorner[0] + cos(endAngle) * cornerRadius;\r\n        const arrowEndPointY = rightTopCorner[1] + (1 - sin(endAngle)) * cornerRadius;\r\n        let arrowArc = buildPath(\"L\", rightTopCorner, getArc(cornerRadius, cos(angle), 1 - sin(angle)), \"L\", [anchorX, anchorY, arrowEndPointX, arrowEndPointY], getAbsoluteArc(cornerRadius, rightTopCorner[0] + cornerRadius, rightTopCorner[1] + cornerRadius));\r\n        if (Math.abs(angle) > PI / 2) {\r\n            arrowArc = buildPath(\"L\", [arrowBaseLeft, yt, anchorX, anchorY, xr, arrowBaseBottom])\r\n        }\r\n        points = buildPath(leftTopCorner, getArc(cornerRadius, 1, -1), arrowArc, \"L\", rightBottomCorner, getArc(cornerRadius, -1, 1), \"L\", leftBottomCorner, getArc(cornerRadius, -1, -1))\r\n    } else if (anchorX > xr && anchorY >= yt && anchorY <= yb) {\r\n        let arrowArc;\r\n        if (arrowBaseTop >= rightTopCorner[1] + cornerRadius && arrowBaseBottom <= rightBottomCorner[1]) {\r\n            arrowArc = buildPath(getArc(cornerRadius, 1, 1), \"L\", [xr, arrowBaseTop, anchorX, anchorY, xr, arrowBaseBottom], \"L\", rightBottomCorner, getArc(cornerRadius, -1, 1))\r\n        } else if (arrowBaseTop < rightTopCorner[1] + cornerRadius && arrowBaseBottom >= rightTopCorner[1] + cornerRadius && arrowBaseBottom <= rightBottomCorner[1]) {\r\n            const arrowWidthRest = rightTopCorner[1] + cornerRadius - arrowBaseTop;\r\n            const angle = arrowWidthRest / cornerRadius;\r\n            const arrowBaseTopX = rightTopCorner[0] + cos(angle) * cornerRadius;\r\n            const arrowBaseTopY = rightTopCorner[1] + (1 - sin(angle)) * cornerRadius;\r\n            arrowArc = buildPath(getArc(cornerRadius, cos(angle), 1 - sin(angle)), \"L\", [arrowBaseTopX, arrowBaseTopY, anchorX, anchorY, xr, arrowBaseBottom], \"L\", rightBottomCorner, getArc(cornerRadius, -1, 1))\r\n        } else if (arrowBaseTop < rightTopCorner[1] + cornerRadius && arrowBaseBottom < rightTopCorner[1] + cornerRadius) {\r\n            const arrowWidthRest = rightTopCorner[1] + cornerRadius - arrowBaseTop;\r\n            const arrowAngle = arrowWidthRest / cornerRadius;\r\n            const angle = arrowAngle;\r\n            const arrowBaseTopX = rightTopCorner[0] + cos(angle) * cornerRadius;\r\n            const arrowBaseTopY = rightTopCorner[1] + (1 - sin(angle)) * cornerRadius;\r\n            const bottomAngle = Math.sin((rightTopCorner[1] + cornerRadius - arrowBaseBottom) / cornerRadius);\r\n            const arrowBaseBottomX = rightTopCorner[0] + cornerRadius * cos(bottomAngle);\r\n            const arrowBaseBottomY = rightTopCorner[1] + cornerRadius * (1 - sin(bottomAngle));\r\n            arrowArc = buildPath(getArc(cornerRadius, cos(angle), 1 - sin(angle)), \"L\", [arrowBaseTopX, arrowBaseTopY, anchorX, anchorY, arrowBaseBottomX, arrowBaseBottomY], getAbsoluteArc(cornerRadius, rightTopCorner[0] + cornerRadius, rightTopCorner[1] + cornerRadius), \"L\", rightBottomCorner, getArc(cornerRadius, -1, 1))\r\n        } else if (arrowBaseTop <= rightTopCorner[1] + cornerRadius && arrowBaseBottom >= rightBottomCorner[1]) {\r\n            const topAngle = asin((rightTopCorner[1] + cornerRadius - arrowBaseTop) / cornerRadius);\r\n            const arrowBaseTopX = rightTopCorner[0] + cornerRadius * cos(topAngle);\r\n            const arrowBaseTopY = rightTopCorner[1] + cornerRadius * (1 - sin(topAngle));\r\n            const bottomAngle = asin((arrowBaseBottom - rightBottomCorner[1]) / cornerRadius);\r\n            const arrowBaseBottomX = rightBottomCorner[0] + cornerRadius * (cos(bottomAngle) - 1);\r\n            const arrowBaseBottomY = rightBottomCorner[1] + cornerRadius * sin(bottomAngle);\r\n            arrowArc = buildPath(getArc(cornerRadius, cos(topAngle), 1 - sin(topAngle)), \"L\", [arrowBaseTopX, arrowBaseTopY, anchorX, anchorY, arrowBaseBottomX, arrowBaseBottomY], getAbsoluteArc(cornerRadius, rightBottomCorner[0] - cornerRadius, rightBottomCorner[1] + cornerRadius))\r\n        } else if (arrowBaseTop > rightTopCorner[1] + cornerRadius && arrowBaseTop <= rightBottomCorner[1] && arrowBaseBottom > rightBottomCorner[1]) {\r\n            const bottomAngle = asin((arrowBaseBottom - rightBottomCorner[1]) / cornerRadius);\r\n            const arrowBaseBottomX = rightBottomCorner[0] + cornerRadius * (cos(bottomAngle) - 1);\r\n            const arrowBaseBottomY = rightBottomCorner[1] + cornerRadius * sin(bottomAngle);\r\n            arrowArc = buildPath(getArc(cornerRadius, 1, 1), \"L\", [xr, arrowBaseTop, anchorX, anchorY, arrowBaseBottomX, arrowBaseBottomY], getAbsoluteArc(cornerRadius, rightBottomCorner[0] - cornerRadius, rightBottomCorner[1] + cornerRadius))\r\n        } else if (arrowBaseTop > rightTopCorner[1] + cornerRadius && arrowBaseBottom > rightBottomCorner[1]) {\r\n            const bottomAngle = asin((arrowBaseBottom - rightBottomCorner[1]) / cornerRadius);\r\n            const arrowBaseBottomX = rightBottomCorner[0] + cornerRadius * (cos(bottomAngle) - 1);\r\n            const arrowBaseBottomY = rightBottomCorner[1] + cornerRadius * sin(bottomAngle);\r\n            const topAngle = asin((arrowBaseTop - rightBottomCorner[1]) / cornerRadius);\r\n            const arrowBaseTopX = rightBottomCorner[0] + cornerRadius * (cos(topAngle) - 1);\r\n            const arrowBaseTopY = rightBottomCorner[1] + cornerRadius * sin(topAngle);\r\n            arrowArc = buildPath(getArc(cornerRadius, 1, 1), \"L\", rightBottomCorner, getArc(cornerRadius, cos(topAngle) - 1, sin(topAngle)), \"L\", [arrowBaseTopX, arrowBaseTopY, anchorX, anchorY, arrowBaseBottomX, arrowBaseBottomY], getAbsoluteArc(cornerRadius, rightBottomCorner[0] - cornerRadius, rightBottomCorner[1] + cornerRadius))\r\n        }\r\n        points = buildPath(leftTopCorner, getArc(cornerRadius, 1, -1), \"L\", rightTopCorner, arrowArc, \"L\", leftBottomCorner, getArc(cornerRadius, -1, -1))\r\n    }\r\n    return buildPath(\"M\", points, \"Z\")\r\n}\r\nexport class Plaque {\r\n    constructor(options, widget, root, contentTemplate) {\r\n        let bounded = arguments.length > 4 && void 0 !== arguments[4] ? arguments[4] : true;\r\n        let measureContent = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : (_, g) => g.getBBox();\r\n        let moveContentGroup = arguments.length > 6 && void 0 !== arguments[6] ? arguments[6] : (_, g, x, y) => g.move(x, y);\r\n        this.widget = widget;\r\n        this.options = options;\r\n        this.root = root;\r\n        this.contentTemplate = contentTemplate;\r\n        this.bonded = bounded;\r\n        this.measureContent = measureContent;\r\n        this.moveContentGroup = moveContentGroup\r\n    }\r\n    draw(_ref4) {\r\n        let {\r\n            x: anchorX,\r\n            y: anchorY,\r\n            canvas: canvas = {},\r\n            offsetX: offsetX,\r\n            offsetY: offsetY,\r\n            offset: offset = 0\r\n        } = _ref4, restProps = _objectWithoutPropertiesLoose(_ref4, _excluded);\r\n        const options = this.options;\r\n        let {\r\n            x: x,\r\n            y: y\r\n        } = options;\r\n        const bounds_xl = canvas.left,\r\n            bounds_xr = canvas.width - canvas.right,\r\n            bounds_width = canvas.width - canvas.right - canvas.left,\r\n            bounds_yt = canvas.top,\r\n            bounds_yb = canvas.height - canvas.bottom,\r\n            bounds_height = canvas.height - canvas.bottom - canvas.top;\r\n        if (!(isDefined(anchorX) && isDefined(anchorY)) && !(isDefined(x) && isDefined(y))) {\r\n            return false\r\n        }\r\n        if (isDefined(anchorX) && (anchorX < bounds_xl || bounds_xr < anchorX || anchorY < bounds_yt || bounds_yb < anchorY)) {\r\n            return false\r\n        }\r\n        if (!this._root) {\r\n            this._draw()\r\n        }\r\n        const shadowSettings = extend({\r\n            x: \"-50%\",\r\n            y: \"-50%\",\r\n            width: \"200%\",\r\n            height: \"200%\"\r\n        }, options.shadow);\r\n        const contentWidth = options.width > 0 ? options.width : null;\r\n        const contentHeight = options.height > 0 ? options.height : null;\r\n        const onRender = () => {\r\n            var _this$_root;\r\n            const bBox = this._contentBBox = this.measureContent(this.widget, this._contentGroup);\r\n            const size = this._size = {\r\n                width: max(contentWidth, bBox.width) + 2 * options.paddingLeftRight,\r\n                height: max(contentHeight, bBox.height) + 2 * options.paddingTopBottom,\r\n                offset: offset\r\n            };\r\n            const xOff = shadowSettings.offsetX;\r\n            const yOff = shadowSettings.offsetY;\r\n            const blur = 2 * shadowSettings.blur + 1;\r\n            const lm = max(blur - xOff, 0);\r\n            const rm = max(blur + xOff, 0);\r\n            const tm = max(blur - yOff, 0);\r\n            const bm = max(blur + yOff, 0);\r\n            this.margins = {\r\n                lm: lm,\r\n                rm: rm,\r\n                tm: tm,\r\n                bm: bm\r\n            };\r\n            if (!isDefined(x)) {\r\n                if (isDefined(offsetX)) {\r\n                    x = anchorX + offsetX\r\n                } else if (bounds_width < size.width) {\r\n                    x = round(bounds_xl + bounds_width / 2)\r\n                } else {\r\n                    x = min(max(anchorX, Math.ceil(bounds_xl + size.width / 2 + lm)), Math.floor(bounds_xr - size.width / 2 - rm))\r\n                }\r\n            } else {\r\n                x += offsetX || 0;\r\n                if (!isDefined(anchorX)) {\r\n                    anchorX = x\r\n                }\r\n            }\r\n            if (!isDefined(y)) {\r\n                if (isDefined(offsetY)) {\r\n                    y = anchorY + offsetY\r\n                } else {\r\n                    const y_top = anchorY - options.arrowLength - size.height / 2 - offset;\r\n                    const y_bottom = anchorY + options.arrowLength + size.height / 2 + offset;\r\n                    if (bounds_height < size.height + options.arrowLength) {\r\n                        y = round(bounds_yt + size.height / 2)\r\n                    } else if (y_top - size.height / 2 - tm < bounds_yt) {\r\n                        if (y_bottom + size.height / 2 + bm < bounds_yb) {\r\n                            y = y_bottom;\r\n                            anchorY += offset\r\n                        } else {\r\n                            y = round(bounds_yt + size.height / 2)\r\n                        }\r\n                    } else {\r\n                        y = y_top;\r\n                        anchorY -= offset\r\n                    }\r\n                }\r\n            } else {\r\n                y += offsetY || 0;\r\n                if (!isDefined(anchorY)) {\r\n                    anchorY = y + size.height / 2\r\n                }\r\n            }\r\n            this.anchorX = anchorX;\r\n            this.anchorY = anchorY;\r\n            this.move(x, y);\r\n            null === (_this$_root = this._root) || void 0 === _this$_root || _this$_root.append(this.root)\r\n        };\r\n        if (this.contentTemplate.render) {\r\n            this.contentTemplate.render({\r\n                model: options,\r\n                container: this._contentGroup.element,\r\n                onRendered: onRender\r\n            })\r\n        } else {\r\n            this.contentTemplate(_extends({\r\n                group: this._contentGroup,\r\n                onRender: onRender\r\n            }, restProps))\r\n        }\r\n        return true\r\n    }\r\n    _draw() {\r\n        const renderer = this.widget._renderer;\r\n        const options = this.options;\r\n        const shadowSettings = extend({\r\n            x: \"-50%\",\r\n            y: \"-50%\",\r\n            width: \"200%\",\r\n            height: \"200%\"\r\n        }, options.shadow);\r\n        const shadow = this._shadow = renderer.shadowFilter().attr(shadowSettings);\r\n        const cloudSettings = {\r\n            opacity: options.opacity,\r\n            \"stroke-width\": 0,\r\n            fill: options.color\r\n        };\r\n        const borderOptions = options.border || {};\r\n        if (borderOptions.visible) {\r\n            extend(cloudSettings, {\r\n                \"stroke-width\": borderOptions.width,\r\n                stroke: borderOptions.color,\r\n                \"stroke-opacity\": borderOptions.opacity,\r\n                dashStyle: borderOptions.dashStyle\r\n            })\r\n        }\r\n        const group = this._root = renderer.g().append(this.root);\r\n        if (options.type) {\r\n            group.attr({\r\n                class: `dxc-${options.type}-annotation`\r\n            })\r\n        }\r\n        const cloudGroup = renderer.g().attr({\r\n            filter: shadow.id\r\n        }).append(group);\r\n        this._cloud = renderer.path([], \"area\").attr(cloudSettings).sharp().append(cloudGroup);\r\n        this._contentGroup = renderer.g().append(group)\r\n    }\r\n    getBBox() {\r\n        const size = this._size || {};\r\n        const margins = this.margins || {};\r\n        const rotationAngle = getCloudAngle(size, this.x, this.y, this.anchorX, this.anchorY);\r\n        return {\r\n            x: Math.floor(this.x - size.width / 2 - margins.lm),\r\n            y: Math.floor(this.y - size.height / 2 - margins.tm - (270 === rotationAngle ? this.options.arrowLength : 0)),\r\n            width: size.width + margins.lm + margins.rm,\r\n            height: size.height + margins.tm + margins.bm + (90 === rotationAngle || 270 === rotationAngle ? this.options.arrowLength : 0)\r\n        }\r\n    }\r\n    clear() {\r\n        if (this._root) {\r\n            this._root.remove();\r\n            this._shadow.remove();\r\n            this._root = null\r\n        }\r\n        return this\r\n    }\r\n    customizeCloud(attr) {\r\n        if (this._cloud) {\r\n            this._cloud.attr(attr)\r\n        }\r\n    }\r\n    moveRoot(x, y) {\r\n        if (this._root) {\r\n            this._root.move(x, y)\r\n        }\r\n    }\r\n    move(x, y) {\r\n        x = round(x);\r\n        y = round(y);\r\n        this.x = x;\r\n        this.y = y;\r\n        const rotationAngle = getCloudAngle(this._size, x, y, this.anchorX, this.anchorY);\r\n        const radRotationAngle = rotationAngle * PI / 180;\r\n        this._cloud.attr({\r\n            d: getCloudPoints(rotateSize(this._size, rotationAngle), x, y, rotateX(this.anchorX, this.anchorY, radRotationAngle, x, y), rotateY(this.anchorX, this.anchorY, radRotationAngle, x, y), this.options, this.bonded)\r\n        }).rotate(rotationAngle, x, y);\r\n        this.moveContentGroup(this.widget, this._contentGroup, x - this._contentBBox.x - this._contentBBox.width / 2, y - this._contentBBox.y - this._contentBBox.height / 2)\r\n    }\r\n    hitTest(x, y) {\r\n        const {\r\n            width: width,\r\n            height: height\r\n        } = this._size || {};\r\n        return Math.abs(x - this.x) <= width / 2 && Math.abs(y - this.y) <= height / 2\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAEA;AAAA;AAGA;AAAA;;;AAJA,MAAM,YAAY;IAAC;IAAK;IAAK;IAAU;IAAW;IAAW;CAAS;;;AAOtE,MAAM,OAAO;AACb,MAAM,QAAQ,KAAK,KAAK;AACxB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,MAAM,KAAK,GAAG;AACpB,MAAM,OAAO,KAAK,IAAI;AACtB,MAAM,KAAK,KAAK,EAAE;AAClB,MAAM,YAAY;IACd,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,SAAS,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;QACvF,MAAM,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;IAClC;IACA,OAAO,OAAO,IAAI,CAAC;AACvB;AAEA,SAAS,OAAO,YAAY,EAAE,UAAU,EAAE,UAAU;IAChD,OAAO,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,aAAa,OAAO,EAAE,aAAW,aAAa,CAAC,EAAE,aAAW,cAAc;AAC1G;AAEA,SAAS,eAAe,YAAY,EAAE,CAAC,EAAE,CAAC;IACtC,OAAO,CAAC,EAAE,EAAE,aAAa,CAAC,EAAE,aAAa,OAAO,EAAE,EAAE,CAAC,EAAE,GAAG;AAC9D;AAEA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;IAChC,OAAO,CAAC,IAAI,EAAE,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,MAAM,IAAI,UAAU;AACzE;AAEA,SAAS,QAAQ,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,EAAE,EAAE,EAAE;IAChC,OAAO,CAAC,CAAC,IAAI,EAAE,IAAI,MAAM,IAAI,UAAU,CAAC,IAAI,EAAE,IAAI,MAAM,IAAI,UAAU;AAC1E;AAEA,SAAS,WAAW,OAAO,EAAE,KAAK;IAC9B,IAAI,QAAQ,OAAO,KAAK,QAAQ,QAAQ,GAAG;QACvC,OAAO;YACH,OAAO,QAAQ,MAAM;YACrB,QAAQ,QAAQ,KAAK;QACzB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,cAAc,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO;IAC/C,IAAI,EACA,OAAO,KAAK,EACZ,QAAQ,MAAM,EACjB,GAAG;IACJ,MAAM,YAAY,QAAQ;IAC1B,MAAM,aAAa,SAAS;IAC5B,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;IACzB,MAAM,KAAK,KAAK,KAAK,CAAC,IAAI;IAC1B,MAAM,KAAK,KAAK,KAAK,CAAC,IAAI;IAC1B,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;IACzB,IAAI,UAAU,MAAM,UAAU,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,IAAI;QAChF,OAAO;IACX;IACA,IAAI,UAAU,MAAM,UAAU,MAAM,WAAW,MAAM,WAAW,MAAM,UAAU,IAAI;QAChF,OAAO;IACX,OAAO,IAAI,UAAU,MAAM,UAAU,MAAM,UAAU,MAAM,WAAW,MAAM,WAAW,IAAI;QACvF,OAAO;IACX;IACA,OAAO;AACX;AAEA,SAAS,eAAe,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO;IACjE,IAAI,EACA,OAAO,KAAK,EACZ,QAAQ,MAAM,EACjB,GAAG;IACJ,IAAI,EACA,YAAY,UAAU,EACtB,cAAc,eAAe,CAAC,EACjC,GAAG;IACJ,MAAM,iBAAiB,aAAa;IACpC,MAAM,YAAY,QAAQ;IAC1B,MAAM,aAAa,SAAS;IAC5B,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;IACzB,MAAM,KAAK,KAAK,KAAK,CAAC,IAAI;IAC1B,MAAM,KAAK,KAAK,KAAK,CAAC,IAAI;IAC1B,MAAM,KAAK,KAAK,IAAI,CAAC,IAAI;IACzB,MAAM,gBAAgB;QAAC;QAAI;KAAG;IAC9B,MAAM,iBAAiB;QAAC;QAAI;KAAG;IAC/B,MAAM,oBAAoB;QAAC;QAAI;KAAG;IAClC,MAAM,mBAAmB;QAAC;QAAI;KAAG;IACjC,MAAM,SAAS,WAAW,KAAK,KAAK,MAAM,UAAU,KAAK;IACzD,MAAM,SAAS,WAAW,KAAK,KAAK,MAAM,UAAU,KAAK;IACzD,MAAM,kBAAkB,IAAI,SAAS,gBAAgB;IACrD,MAAM,eAAe,IAAI,SAAS,gBAAgB;IAClD,MAAM,gBAAgB,IAAI,SAAS,gBAAgB;IACnD,eAAe,KAAK,GAAG,CAAC,QAAQ,GAAG,SAAS,GAAG;IAC/C,IAAI;IACJ,aAAa,CAAC,EAAE,IAAI;IACpB,cAAc,CAAC,EAAE,IAAI;IACrB,iBAAiB,CAAC,EAAE,IAAI;IACxB,gBAAgB,CAAC,EAAE,IAAI;IACvB,IAAI,CAAC,WAAW,MAAM,WAAW,WAAW,MAAM,MAAM,WAAW,WAAW,IAAI;QAC9E,SAAS,UAAU,eAAe,OAAO,cAAc,GAAG,CAAC,IAAI,KAAK,gBAAgB,OAAO,cAAc,GAAG,IAAI,KAAK,mBAAmB,OAAO,cAAc,CAAC,GAAG,IAAI,KAAK,kBAAkB,OAAO,cAAc,CAAC,GAAG,CAAC;IAC1N,OAAO,IAAI,UAAU,MAAM,UAAU,IAAI;QACrC,MAAM,aAAa,aAAa,gBAAgB;QAChD,MAAM,QAAQ,KAAK,IAAI,aAAa;QACpC,MAAM,WAAW,KAAK,IAAI,aAAa;QACvC,MAAM,iBAAiB,cAAc,CAAC,EAAE,GAAG,IAAI,YAAY;QAC3D,MAAM,iBAAiB,cAAc,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,SAAS,IAAI;QACjE,IAAI,WAAW,UAAU,KAAK,gBAAgB,OAAO,cAAc,IAAI,QAAQ,IAAI,IAAI,SAAS,KAAK;YAAC;YAAS;YAAS;YAAgB;SAAe,EAAE,eAAe,cAAc,cAAc,CAAC,EAAE,GAAG,cAAc,cAAc,CAAC,EAAE,GAAG;QAC5O,IAAI,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG;YAC1B,WAAW,UAAU,KAAK;gBAAC;gBAAe;gBAAI;gBAAS;gBAAS;gBAAI;aAAgB;QACxF;QACA,SAAS,UAAU,eAAe,OAAO,cAAc,GAAG,CAAC,IAAI,UAAU,KAAK,mBAAmB,OAAO,cAAc,CAAC,GAAG,IAAI,KAAK,kBAAkB,OAAO,cAAc,CAAC,GAAG,CAAC;IACnL,OAAO,IAAI,UAAU,MAAM,WAAW,MAAM,WAAW,IAAI;QACvD,IAAI;QACJ,IAAI,gBAAgB,cAAc,CAAC,EAAE,GAAG,gBAAgB,mBAAmB,iBAAiB,CAAC,EAAE,EAAE;YAC7F,WAAW,UAAU,OAAO,cAAc,GAAG,IAAI,KAAK;gBAAC;gBAAI;gBAAc;gBAAS;gBAAS;gBAAI;aAAgB,EAAE,KAAK,mBAAmB,OAAO,cAAc,CAAC,GAAG;QACtK,OAAO,IAAI,eAAe,cAAc,CAAC,EAAE,GAAG,gBAAgB,mBAAmB,cAAc,CAAC,EAAE,GAAG,gBAAgB,mBAAmB,iBAAiB,CAAC,EAAE,EAAE;YAC1J,MAAM,iBAAiB,cAAc,CAAC,EAAE,GAAG,eAAe;YAC1D,MAAM,QAAQ,iBAAiB;YAC/B,MAAM,gBAAgB,cAAc,CAAC,EAAE,GAAG,IAAI,SAAS;YACvD,MAAM,gBAAgB,cAAc,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,IAAI;YAC7D,WAAW,UAAU,OAAO,cAAc,IAAI,QAAQ,IAAI,IAAI,SAAS,KAAK;gBAAC;gBAAe;gBAAe;gBAAS;gBAAS;gBAAI;aAAgB,EAAE,KAAK,mBAAmB,OAAO,cAAc,CAAC,GAAG;QACxM,OAAO,IAAI,eAAe,cAAc,CAAC,EAAE,GAAG,gBAAgB,kBAAkB,cAAc,CAAC,EAAE,GAAG,cAAc;YAC9G,MAAM,iBAAiB,cAAc,CAAC,EAAE,GAAG,eAAe;YAC1D,MAAM,aAAa,iBAAiB;YACpC,MAAM,QAAQ;YACd,MAAM,gBAAgB,cAAc,CAAC,EAAE,GAAG,IAAI,SAAS;YACvD,MAAM,gBAAgB,cAAc,CAAC,EAAE,GAAG,CAAC,IAAI,IAAI,MAAM,IAAI;YAC7D,MAAM,cAAc,KAAK,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE,GAAG,eAAe,eAAe,IAAI;YACpF,MAAM,mBAAmB,cAAc,CAAC,EAAE,GAAG,eAAe,IAAI;YAChE,MAAM,mBAAmB,cAAc,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,IAAI,YAAY;YACjF,WAAW,UAAU,OAAO,cAAc,IAAI,QAAQ,IAAI,IAAI,SAAS,KAAK;gBAAC;gBAAe;gBAAe;gBAAS;gBAAS;gBAAkB;aAAiB,EAAE,eAAe,cAAc,cAAc,CAAC,EAAE,GAAG,cAAc,cAAc,CAAC,EAAE,GAAG,eAAe,KAAK,mBAAmB,OAAO,cAAc,CAAC,GAAG;QACzT,OAAO,IAAI,gBAAgB,cAAc,CAAC,EAAE,GAAG,gBAAgB,mBAAmB,iBAAiB,CAAC,EAAE,EAAE;YACpG,MAAM,WAAW,KAAK,CAAC,cAAc,CAAC,EAAE,GAAG,eAAe,YAAY,IAAI;YAC1E,MAAM,gBAAgB,cAAc,CAAC,EAAE,GAAG,eAAe,IAAI;YAC7D,MAAM,gBAAgB,cAAc,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,IAAI,SAAS;YAC3E,MAAM,cAAc,KAAK,CAAC,kBAAkB,iBAAiB,CAAC,EAAE,IAAI;YACpE,MAAM,mBAAmB,iBAAiB,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,eAAe,CAAC;YACpF,MAAM,mBAAmB,iBAAiB,CAAC,EAAE,GAAG,eAAe,IAAI;YACnE,WAAW,UAAU,OAAO,cAAc,IAAI,WAAW,IAAI,IAAI,YAAY,KAAK;gBAAC;gBAAe;gBAAe;gBAAS;gBAAS;gBAAkB;aAAiB,EAAE,eAAe,cAAc,iBAAiB,CAAC,EAAE,GAAG,cAAc,iBAAiB,CAAC,EAAE,GAAG;QACrQ,OAAO,IAAI,eAAe,cAAc,CAAC,EAAE,GAAG,gBAAgB,gBAAgB,iBAAiB,CAAC,EAAE,IAAI,kBAAkB,iBAAiB,CAAC,EAAE,EAAE;YAC1I,MAAM,cAAc,KAAK,CAAC,kBAAkB,iBAAiB,CAAC,EAAE,IAAI;YACpE,MAAM,mBAAmB,iBAAiB,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,eAAe,CAAC;YACpF,MAAM,mBAAmB,iBAAiB,CAAC,EAAE,GAAG,eAAe,IAAI;YACnE,WAAW,UAAU,OAAO,cAAc,GAAG,IAAI,KAAK;gBAAC;gBAAI;gBAAc;gBAAS;gBAAS;gBAAkB;aAAiB,EAAE,eAAe,cAAc,iBAAiB,CAAC,EAAE,GAAG,cAAc,iBAAiB,CAAC,EAAE,GAAG;QAC7N,OAAO,IAAI,eAAe,cAAc,CAAC,EAAE,GAAG,gBAAgB,kBAAkB,iBAAiB,CAAC,EAAE,EAAE;YAClG,MAAM,cAAc,KAAK,CAAC,kBAAkB,iBAAiB,CAAC,EAAE,IAAI;YACpE,MAAM,mBAAmB,iBAAiB,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,eAAe,CAAC;YACpF,MAAM,mBAAmB,iBAAiB,CAAC,EAAE,GAAG,eAAe,IAAI;YACnE,MAAM,WAAW,KAAK,CAAC,eAAe,iBAAiB,CAAC,EAAE,IAAI;YAC9D,MAAM,gBAAgB,iBAAiB,CAAC,EAAE,GAAG,eAAe,CAAC,IAAI,YAAY,CAAC;YAC9E,MAAM,gBAAgB,iBAAiB,CAAC,EAAE,GAAG,eAAe,IAAI;YAChE,WAAW,UAAU,OAAO,cAAc,GAAG,IAAI,KAAK,mBAAmB,OAAO,cAAc,IAAI,YAAY,GAAG,IAAI,YAAY,KAAK;gBAAC;gBAAe;gBAAe;gBAAS;gBAAS;gBAAkB;aAAiB,EAAE,eAAe,cAAc,iBAAiB,CAAC,EAAE,GAAG,cAAc,iBAAiB,CAAC,EAAE,GAAG;QACzT;QACA,SAAS,UAAU,eAAe,OAAO,cAAc,GAAG,CAAC,IAAI,KAAK,gBAAgB,UAAU,KAAK,kBAAkB,OAAO,cAAc,CAAC,GAAG,CAAC;IACnJ;IACA,OAAO,UAAU,KAAK,QAAQ;AAClC;AACO,MAAM;IACT,YAAY,OAAO,EAAE,MAAM,EAAE,IAAI,EAAE,eAAe,CAAE;QAChD,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QAC/E,IAAI,iBAAiB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC,GAAG,IAAM,EAAE,OAAO;QACzG,IAAI,mBAAmB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,IAAM,EAAE,IAAI,CAAC,GAAG;QAClH,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA,KAAK,KAAK,EAAE;QACR,IAAI,EACA,GAAG,OAAO,EACV,GAAG,OAAO,EACV,QAAQ,SAAS,CAAC,CAAC,EACnB,SAAS,OAAO,EAChB,SAAS,OAAO,EAChB,QAAQ,SAAS,CAAC,EACrB,GAAG,OAAO,YAAY,CAAA,GAAA,oLAAA,CAAA,UAA6B,AAAD,EAAE,OAAO;QAC5D,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,IAAI,EACA,GAAG,CAAC,EACJ,GAAG,CAAC,EACP,GAAG;QACJ,MAAM,YAAY,OAAO,IAAI,EACzB,YAAY,OAAO,KAAK,GAAG,OAAO,KAAK,EACvC,eAAe,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,OAAO,IAAI,EACxD,YAAY,OAAO,GAAG,EACtB,YAAY,OAAO,MAAM,GAAG,OAAO,MAAM,EACzC,gBAAgB,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG,OAAO,GAAG;QAC9D,IAAI,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,KAAK,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,EAAE,GAAG;YAChF,OAAO;QACX;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,UAAU,aAAa,YAAY,WAAW,UAAU,aAAa,YAAY,OAAO,GAAG;YAClH,OAAO;QACX;QACA,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,IAAI,CAAC,KAAK;QACd;QACA,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YAC1B,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;QACZ,GAAG,QAAQ,MAAM;QACjB,MAAM,eAAe,QAAQ,KAAK,GAAG,IAAI,QAAQ,KAAK,GAAG;QACzD,MAAM,gBAAgB,QAAQ,MAAM,GAAG,IAAI,QAAQ,MAAM,GAAG;QAC5D,MAAM,WAAW;YACb,IAAI;YACJ,MAAM,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa;YACpF,MAAM,OAAO,IAAI,CAAC,KAAK,GAAG;gBACtB,OAAO,IAAI,cAAc,KAAK,KAAK,IAAI,IAAI,QAAQ,gBAAgB;gBACnE,QAAQ,IAAI,eAAe,KAAK,MAAM,IAAI,IAAI,QAAQ,gBAAgB;gBACtE,QAAQ;YACZ;YACA,MAAM,OAAO,eAAe,OAAO;YACnC,MAAM,OAAO,eAAe,OAAO;YACnC,MAAM,OAAO,IAAI,eAAe,IAAI,GAAG;YACvC,MAAM,KAAK,IAAI,OAAO,MAAM;YAC5B,MAAM,KAAK,IAAI,OAAO,MAAM;YAC5B,MAAM,KAAK,IAAI,OAAO,MAAM;YAC5B,MAAM,KAAK,IAAI,OAAO,MAAM;YAC5B,IAAI,CAAC,OAAO,GAAG;gBACX,IAAI;gBACJ,IAAI;gBACJ,IAAI;gBACJ,IAAI;YACR;YACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI;gBACf,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU;oBACpB,IAAI,UAAU;gBAClB,OAAO,IAAI,eAAe,KAAK,KAAK,EAAE;oBAClC,IAAI,MAAM,YAAY,eAAe;gBACzC,OAAO;oBACH,IAAI,IAAI,IAAI,SAAS,KAAK,IAAI,CAAC,YAAY,KAAK,KAAK,GAAG,IAAI,MAAM,KAAK,KAAK,CAAC,YAAY,KAAK,KAAK,GAAG,IAAI;gBAC9G;YACJ,OAAO;gBACH,KAAK,WAAW;gBAChB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU;oBACrB,UAAU;gBACd;YACJ;YACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI;gBACf,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU;oBACpB,IAAI,UAAU;gBAClB,OAAO;oBACH,MAAM,QAAQ,UAAU,QAAQ,WAAW,GAAG,KAAK,MAAM,GAAG,IAAI;oBAChE,MAAM,WAAW,UAAU,QAAQ,WAAW,GAAG,KAAK,MAAM,GAAG,IAAI;oBACnE,IAAI,gBAAgB,KAAK,MAAM,GAAG,QAAQ,WAAW,EAAE;wBACnD,IAAI,MAAM,YAAY,KAAK,MAAM,GAAG;oBACxC,OAAO,IAAI,QAAQ,KAAK,MAAM,GAAG,IAAI,KAAK,WAAW;wBACjD,IAAI,WAAW,KAAK,MAAM,GAAG,IAAI,KAAK,WAAW;4BAC7C,IAAI;4BACJ,WAAW;wBACf,OAAO;4BACH,IAAI,MAAM,YAAY,KAAK,MAAM,GAAG;wBACxC;oBACJ,OAAO;wBACH,IAAI;wBACJ,WAAW;oBACf;gBACJ;YACJ,OAAO;gBACH,KAAK,WAAW;gBAChB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU;oBACrB,UAAU,IAAI,KAAK,MAAM,GAAG;gBAChC;YACJ;YACA,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,IAAI,CAAC,GAAG;YACb,SAAS,CAAC,cAAc,IAAI,CAAC,KAAK,KAAK,KAAK,MAAM,eAAe,YAAY,MAAM,CAAC,IAAI,CAAC,IAAI;QACjG;QACA,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE;YAC7B,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;gBACxB,OAAO;gBACP,WAAW,IAAI,CAAC,aAAa,CAAC,OAAO;gBACrC,YAAY;YAChB;QACJ,OAAO;YACH,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;gBAC1B,OAAO,IAAI,CAAC,aAAa;gBACzB,UAAU;YACd,GAAG;QACP;QACA,OAAO;IACX;IACA,QAAQ;QACJ,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,SAAS;QACtC,MAAM,UAAU,IAAI,CAAC,OAAO;QAC5B,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YAC1B,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;QACZ,GAAG,QAAQ,MAAM;QACjB,MAAM,SAAS,IAAI,CAAC,OAAO,GAAG,SAAS,YAAY,GAAG,IAAI,CAAC;QAC3D,MAAM,gBAAgB;YAClB,SAAS,QAAQ,OAAO;YACxB,gBAAgB;YAChB,MAAM,QAAQ,KAAK;QACvB;QACA,MAAM,gBAAgB,QAAQ,MAAM,IAAI,CAAC;QACzC,IAAI,cAAc,OAAO,EAAE;YACvB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,eAAe;gBAClB,gBAAgB,cAAc,KAAK;gBACnC,QAAQ,cAAc,KAAK;gBAC3B,kBAAkB,cAAc,OAAO;gBACvC,WAAW,cAAc,SAAS;YACtC;QACJ;QACA,MAAM,QAAQ,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC,IAAI,CAAC,IAAI;QACxD,IAAI,QAAQ,IAAI,EAAE;YACd,MAAM,IAAI,CAAC;gBACP,OAAO,CAAC,IAAI,EAAE,QAAQ,IAAI,CAAC,WAAW,CAAC;YAC3C;QACJ;QACA,MAAM,aAAa,SAAS,CAAC,GAAG,IAAI,CAAC;YACjC,QAAQ,OAAO,EAAE;QACrB,GAAG,MAAM,CAAC;QACV,IAAI,CAAC,MAAM,GAAG,SAAS,IAAI,CAAC,EAAE,EAAE,QAAQ,IAAI,CAAC,eAAe,KAAK,GAAG,MAAM,CAAC;QAC3E,IAAI,CAAC,aAAa,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC;IAC7C;IACA,UAAU;QACN,MAAM,OAAO,IAAI,CAAC,KAAK,IAAI,CAAC;QAC5B,MAAM,UAAU,IAAI,CAAC,OAAO,IAAI,CAAC;QACjC,MAAM,gBAAgB,cAAc,MAAM,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO;QACpF,OAAO;YACH,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,KAAK,GAAG,IAAI,QAAQ,EAAE;YAClD,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,CAAC,GAAG,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE,GAAG,CAAC,QAAQ,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;YAC3G,OAAO,KAAK,KAAK,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE;YAC3C,QAAQ,KAAK,MAAM,GAAG,QAAQ,EAAE,GAAG,QAAQ,EAAE,GAAG,CAAC,OAAO,iBAAiB,QAAQ,gBAAgB,IAAI,CAAC,OAAO,CAAC,WAAW,GAAG,CAAC;QACjI;IACJ;IACA,QAAQ;QACJ,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,MAAM;YACjB,IAAI,CAAC,OAAO,CAAC,MAAM;YACnB,IAAI,CAAC,KAAK,GAAG;QACjB;QACA,OAAO,IAAI;IACf;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACrB;IACJ;IACA,SAAS,CAAC,EAAE,CAAC,EAAE;QACX,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;QACvB;IACJ;IACA,KAAK,CAAC,EAAE,CAAC,EAAE;QACP,IAAI,MAAM;QACV,IAAI,MAAM;QACV,IAAI,CAAC,CAAC,GAAG;QACT,IAAI,CAAC,CAAC,GAAG;QACT,MAAM,gBAAgB,cAAc,IAAI,CAAC,KAAK,EAAE,GAAG,GAAG,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO;QAChF,MAAM,mBAAmB,gBAAgB,KAAK;QAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,GAAG,eAAe,WAAW,IAAI,CAAC,KAAK,EAAE,gBAAgB,GAAG,GAAG,QAAQ,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,kBAAkB,GAAG,IAAI,QAAQ,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE,kBAAkB,GAAG,IAAI,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM;QACtN,GAAG,MAAM,CAAC,eAAe,GAAG;QAC5B,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,aAAa,EAAE,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG;IACvK;IACA,QAAQ,CAAC,EAAE,CAAC,EAAE;QACV,MAAM,EACF,OAAO,KAAK,EACZ,QAAQ,MAAM,EACjB,GAAG,IAAI,CAAC,KAAK,IAAI,CAAC;QACnB,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,QAAQ,KAAK,KAAK,GAAG,CAAC,IAAI,IAAI,CAAC,CAAC,KAAK,SAAS;IACjF;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3535, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/tooltip.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/tooltip.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getWidth,\r\n    getHeight\r\n} from \"../../core/utils/size\";\r\nimport {\r\n    normalizeStyleProp\r\n} from \"../../core/utils/style\";\r\nimport domAdapter from \"../../core/dom_adapter\";\r\nimport {\r\n    getWindow\r\n} from \"../../core/utils/window\";\r\nimport {\r\n    replaceWith\r\n} from \"../../core/utils/dom\";\r\nimport {\r\n    camelize\r\n} from \"../../core/utils/inflector\";\r\nimport $ from \"../../core/renderer\";\r\nimport {\r\n    Renderer\r\n} from \"./renderers/renderer\";\r\nimport {\r\n    isFunction,\r\n    isPlainObject,\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    patchFontOptions,\r\n    normalizeEnum\r\n} from \"./utils\";\r\nimport formatHelper from \"../../format_helper\";\r\nimport {\r\n    Plaque\r\n} from \"./plaque\";\r\nconst format = formatHelper.format;\r\nconst mathCeil = Math.ceil;\r\nconst mathMax = Math.max;\r\nconst mathMin = Math.min;\r\nconst window = getWindow();\r\nconst DEFAULT_HTML_GROUP_WIDTH = 3e3;\r\n\r\nfunction hideElement($element) {\r\n    $element.css({\r\n        left: \"-9999px\"\r\n    }).detach()\r\n}\r\n\r\nfunction getSpecialFormatOptions(options, specialFormat) {\r\n    let result = options;\r\n    switch (specialFormat) {\r\n        case \"argument\":\r\n            result = {\r\n                format: options.argumentFormat\r\n            };\r\n            break;\r\n        case \"percent\":\r\n            result = {\r\n                format: {\r\n                    type: \"percent\",\r\n                    precision: options.format && options.format.percentPrecision\r\n                }\r\n            }\r\n    }\r\n    return result\r\n}\r\n\r\nfunction createTextHtml() {\r\n    return $(\"<div>\").css({\r\n        position: \"relative\",\r\n        display: \"inline-block\",\r\n        padding: 0,\r\n        margin: 0,\r\n        border: \"0px solid transparent\"\r\n    })\r\n}\r\n\r\nfunction removeElements(elements) {\r\n    elements.forEach((el => el.remove()))\r\n}\r\nexport let Tooltip = function(params) {\r\n    this._eventTrigger = params.eventTrigger;\r\n    this._widgetRoot = params.widgetRoot;\r\n    this._widget = params.widget;\r\n    this._textHtmlContainers = [];\r\n    this._wrapper = $(\"<div>\").css({\r\n        position: \"absolute\",\r\n        overflow: \"hidden\",\r\n        pointerEvents: \"none\"\r\n    }).addClass(params.cssClass);\r\n    const renderer = this._renderer = new Renderer({\r\n        pathModified: params.pathModified,\r\n        container: this._wrapper[0]\r\n    });\r\n    const root = renderer.root;\r\n    root.attr({\r\n        \"pointer-events\": \"none\"\r\n    });\r\n    this._text = renderer.text(void 0, 0, 0);\r\n    this._textGroupHtml = $(\"<div>\").css({\r\n        position: \"absolute\",\r\n        padding: 0,\r\n        margin: 0,\r\n        border: \"0px solid transparent\"\r\n    }).appendTo(this._wrapper);\r\n    this._textHtml = createTextHtml().appendTo(this._textGroupHtml)\r\n};\r\nTooltip.prototype = {\r\n    constructor: Tooltip,\r\n    dispose: function() {\r\n        this._wrapper.remove();\r\n        this._renderer.dispose();\r\n        this._options = this._widgetRoot = null\r\n    },\r\n    _getContainer: function() {\r\n        const options = this._options;\r\n        let container = $(this._widgetRoot).closest(options.container);\r\n        if (0 === container.length) {\r\n            container = $(options.container)\r\n        }\r\n        return (container.length ? container : $(\"body\")).get(0)\r\n    },\r\n    setTemplate(contentTemplate) {\r\n        this._template = contentTemplate ? this._widget._getTemplate(contentTemplate) : null\r\n    },\r\n    setOptions: function(options) {\r\n        options = options || {};\r\n        const that = this;\r\n        that._options = options;\r\n        that._textFontStyles = patchFontOptions(options.font);\r\n        that._textFontStyles.color = that._textFontStyles.fill;\r\n        that._wrapper.css({\r\n            zIndex: options.zIndex\r\n        });\r\n        that._customizeTooltip = options.customizeTooltip;\r\n        const textGroupHtml = that._textGroupHtml;\r\n        if (this.plaque) {\r\n            this.plaque.clear()\r\n        }\r\n        this.setTemplate(options.contentTemplate);\r\n        const pointerEvents = options.interactive ? \"auto\" : \"none\";\r\n        if (options.interactive) {\r\n            this._renderer.root.css({\r\n                \"-moz-user-select\": \"auto\",\r\n                \"-webkit-user-select\": \"auto\"\r\n            })\r\n        }\r\n        this.plaque = new Plaque({\r\n            opacity: that._options.opacity,\r\n            color: that._options.color,\r\n            border: that._options.border,\r\n            paddingLeftRight: that._options.paddingLeftRight,\r\n            paddingTopBottom: that._options.paddingTopBottom,\r\n            arrowLength: that._options.arrowLength,\r\n            arrowWidth: 20,\r\n            shadow: that._options.shadow,\r\n            cornerRadius: that._options.cornerRadius\r\n        }, that, that._renderer.root, (_ref => {\r\n            let {\r\n                group: group,\r\n                onRender: onRender,\r\n                eventData: eventData,\r\n                isMoving: isMoving,\r\n                templateCallback: templateCallback = () => {}\r\n            } = _ref;\r\n            const state = that._state;\r\n            if (!isMoving) {\r\n                const template = that._template;\r\n                const useTemplate = template && !state.formatObject.skipTemplate;\r\n                if (state.html || useTemplate) {\r\n                    textGroupHtml.css({\r\n                        color: state.textColor,\r\n                        width: 3e3,\r\n                        pointerEvents: pointerEvents\r\n                    });\r\n                    if (useTemplate) {\r\n                        const htmlContainers = that._textHtmlContainers;\r\n                        const containerToTemplateRender = createTextHtml().appendTo(that._textGroupHtml);\r\n                        htmlContainers.push(containerToTemplateRender);\r\n                        template.render({\r\n                            model: state.formatObject,\r\n                            container: containerToTemplateRender,\r\n                            onRendered: () => {\r\n                                removeElements(htmlContainers.splice(0, htmlContainers.length - 1));\r\n                                that._textHtml = replaceWith(that._textHtml, containerToTemplateRender);\r\n                                state.html = that._textHtml.html();\r\n                                if (0 === getWidth(that._textHtml) && 0 === getHeight(that._textHtml)) {\r\n                                    this.plaque.clear();\r\n                                    templateCallback(false);\r\n                                    return\r\n                                }\r\n                                onRender();\r\n                                that._riseEvents(eventData);\r\n                                that._moveWrapper();\r\n                                that.plaque.customizeCloud({\r\n                                    fill: state.color,\r\n                                    stroke: state.borderColor,\r\n                                    \"pointer-events\": pointerEvents\r\n                                });\r\n                                templateCallback(true);\r\n                                that._textHtmlContainers = []\r\n                            }\r\n                        });\r\n                        return\r\n                    } else {\r\n                        that._text.attr({\r\n                            text: \"\"\r\n                        });\r\n                        that._textHtml.html(state.html)\r\n                    }\r\n                } else {\r\n                    that._text.css({\r\n                        fill: state.textColor\r\n                    }).attr({\r\n                        text: state.text,\r\n                        class: options.cssClass,\r\n                        \"pointer-events\": pointerEvents\r\n                    }).append(group.attr({\r\n                        align: options.textAlignment\r\n                    }))\r\n                }\r\n                that._riseEvents(eventData);\r\n                that.plaque.customizeCloud({\r\n                    fill: state.color,\r\n                    stroke: state.borderColor,\r\n                    \"pointer-events\": pointerEvents\r\n                })\r\n            }\r\n            onRender();\r\n            that._moveWrapper();\r\n            return true\r\n        }), true, ((tooltip, g) => {\r\n            const state = tooltip._state;\r\n            if (state.html) {\r\n                let bBox = window.getComputedStyle(that._textHtml.get(0));\r\n                bBox = {\r\n                    x: 0,\r\n                    y: 0,\r\n                    width: mathCeil(parseFloat(bBox.width)),\r\n                    height: mathCeil(parseFloat(bBox.height))\r\n                };\r\n                return bBox\r\n            }\r\n            return g.getBBox()\r\n        }), ((tooltip, g, x, y) => {\r\n            const state = tooltip._state;\r\n            if (state.html) {\r\n                that._textGroupHtml.css({\r\n                    left: x,\r\n                    top: y\r\n                })\r\n            } else {\r\n                g.move(x, y)\r\n            }\r\n        }));\r\n        return that\r\n    },\r\n    _riseEvents: function(eventData) {\r\n        this._eventData && this._eventTrigger(\"tooltipHidden\", this._eventData);\r\n        this._eventData = eventData;\r\n        this._eventTrigger(\"tooltipShown\", this._eventData)\r\n    },\r\n    setRendererOptions: function(options) {\r\n        this._renderer.setOptions(options);\r\n        this._textGroupHtml.css({\r\n            direction: options.rtl ? \"rtl\" : \"ltr\"\r\n        });\r\n        return this\r\n    },\r\n    update: function(options) {\r\n        const that = this;\r\n        that.setOptions(options);\r\n        hideElement(that._wrapper);\r\n        const normalizedCSS = {};\r\n        for (const name in that._textFontStyles) {\r\n            const normalizedName = camelize(name);\r\n            normalizedCSS[normalizedName] = normalizeStyleProp(normalizedName, that._textFontStyles[name])\r\n        }\r\n        that._textGroupHtml.css(normalizedCSS);\r\n        that._text.css(that._textFontStyles);\r\n        that._eventData = null;\r\n        return that\r\n    },\r\n    _prepare: function(formatObject, state) {\r\n        let customizeTooltip = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : this._customizeTooltip;\r\n        const options = this._options;\r\n        let customize = {};\r\n        if (isFunction(customizeTooltip)) {\r\n            customize = customizeTooltip.call(formatObject, formatObject);\r\n            customize = isPlainObject(customize) ? customize : {};\r\n            if (\"text\" in customize) {\r\n                state.text = isDefined(customize.text) ? String(customize.text) : \"\"\r\n            }\r\n            if (\"html\" in customize) {\r\n                state.html = isDefined(customize.html) ? String(customize.html) : \"\"\r\n            }\r\n        }\r\n        if (!(\"text\" in state) && !(\"html\" in state)) {\r\n            state.text = formatObject.valueText || formatObject.description || \"\"\r\n        }\r\n        state.color = customize.color || options.color;\r\n        state.borderColor = customize.borderColor || (options.border || {}).color;\r\n        state.textColor = customize.fontColor || (this._textFontStyles || {}).color;\r\n        return !!state.text || !!state.html || !!this._template\r\n    },\r\n    show: function(formatObject, params, eventData, customizeTooltip, templateCallback) {\r\n        const that = this;\r\n        if (that._options.forceEvents) {\r\n            eventData.x = params.x;\r\n            eventData.y = params.y - params.offset;\r\n            that._riseEvents(eventData);\r\n            return true\r\n        }\r\n        const state = {\r\n            formatObject: formatObject,\r\n            eventData: eventData,\r\n            templateCallback: templateCallback\r\n        };\r\n        if (!that._prepare(formatObject, state, customizeTooltip)) {\r\n            return false\r\n        }\r\n        that._state = state;\r\n        that._wrapper.appendTo(that._getContainer());\r\n        that._clear();\r\n        const parameters = extend({}, that._options, {\r\n            canvas: that._getCanvas()\r\n        }, state, {\r\n            x: params.x,\r\n            y: params.y,\r\n            offset: params.offset\r\n        });\r\n        return this.plaque.clear().draw(parameters)\r\n    },\r\n    isCursorOnTooltip: function(x, y) {\r\n        if (this._options.interactive) {\r\n            const box = this.plaque.getBBox();\r\n            return x > box.x && x < box.x + box.width && y > box.y && y < box.y + box.height\r\n        }\r\n        return false\r\n    },\r\n    hide: function(isPointerOut) {\r\n        const that = this;\r\n        hideElement(that._wrapper);\r\n        if (that._eventData) {\r\n            that._eventTrigger(\"tooltipHidden\", that._options.forceEvents ? extend({\r\n                isPointerOut: isPointerOut\r\n            }, that._eventData) : that._eventData);\r\n            that._clear();\r\n            that._eventData = null\r\n        }\r\n    },\r\n    _clear() {\r\n        this._textHtml.empty()\r\n    },\r\n    move: function(x, y, offset) {\r\n        this.plaque.draw({\r\n            x: x,\r\n            y: y,\r\n            offset: offset,\r\n            canvas: this._getCanvas(),\r\n            isMoving: true\r\n        })\r\n    },\r\n    _moveWrapper: function() {\r\n        const that = this;\r\n        const plaqueBBox = this.plaque.getBBox();\r\n        that._renderer.resize(plaqueBBox.width, plaqueBBox.height);\r\n        const offset = that._wrapper.css({\r\n            left: 0,\r\n            top: 0\r\n        }).offset();\r\n        const left = plaqueBBox.x;\r\n        const top = plaqueBBox.y;\r\n        that._wrapper.css({\r\n            left: left - offset.left,\r\n            top: top - offset.top\r\n        });\r\n        this.plaque.moveRoot(-left, -top);\r\n        if (this._state.html) {\r\n            that._textHtml.css({\r\n                left: -left,\r\n                top: -top\r\n            });\r\n            that._textGroupHtml.css({\r\n                width: mathCeil(getWidth(that._textHtml))\r\n            })\r\n        }\r\n    },\r\n    formatValue: function(value, _specialFormat) {\r\n        const options = _specialFormat ? getSpecialFormatOptions(this._options, _specialFormat) : this._options;\r\n        return format(value, options.format)\r\n    },\r\n    getOptions() {\r\n        return this._options\r\n    },\r\n    getLocation: function() {\r\n        return normalizeEnum(this._options.location)\r\n    },\r\n    isEnabled: function() {\r\n        return !!this._options.enabled || !!this._options.forceEvents\r\n    },\r\n    isShared: function() {\r\n        return !!this._options.shared\r\n    },\r\n    _getCanvas: function() {\r\n        const container = this._getContainer();\r\n        const containerBox = container.getBoundingClientRect();\r\n        const html = domAdapter.getDocumentElement();\r\n        const document = domAdapter.getDocument();\r\n        let left = window.pageXOffset || html.scrollLeft || 0;\r\n        let top = window.pageYOffset || html.scrollTop || 0;\r\n        const box = {\r\n            left: left,\r\n            top: top,\r\n            width: mathMax(html.clientWidth, document.body.clientWidth) + left,\r\n            height: mathMax(document.body.scrollHeight, html.scrollHeight, document.body.offsetHeight, html.offsetHeight, document.body.clientHeight, html.clientHeight),\r\n            right: 0,\r\n            bottom: 0\r\n        };\r\n        if (container !== domAdapter.getBody()) {\r\n            left = mathMax(box.left, box.left + containerBox.left);\r\n            top = mathMax(box.top, box.top + containerBox.top);\r\n            box.width = mathMin(containerBox.width, box.width) + left + box.left;\r\n            box.height = mathMin(containerBox.height, box.height) + top + box.top;\r\n            box.left = left;\r\n            box.top = top\r\n        }\r\n        return box\r\n    }\r\n};\r\nexport const plugin = {\r\n    name: \"tooltip\",\r\n    init: function() {\r\n        this._initTooltip()\r\n    },\r\n    dispose: function() {\r\n        this._disposeTooltip()\r\n    },\r\n    members: {\r\n        _initTooltip: function() {\r\n            this._tooltip = new Tooltip({\r\n                cssClass: this._rootClassPrefix + \"-tooltip\",\r\n                eventTrigger: this._eventTrigger,\r\n                pathModified: this.option(\"pathModified\"),\r\n                widgetRoot: this.element(),\r\n                widget: this\r\n            })\r\n        },\r\n        _disposeTooltip: function() {\r\n            this._tooltip.dispose();\r\n            this._tooltip = null\r\n        },\r\n        _setTooltipRendererOptions: function() {\r\n            this._tooltip.setRendererOptions(this._getRendererOptions())\r\n        },\r\n        _setTooltipOptions: function() {\r\n            this._tooltip.update(this._getOption(\"tooltip\"))\r\n        }\r\n    },\r\n    extenders: {\r\n        _stopCurrentHandling() {\r\n            this._tooltip && this._tooltip.hide()\r\n        }\r\n    },\r\n    customize: function(constructor) {\r\n        const proto = constructor.prototype;\r\n        proto._eventsMap.onTooltipShown = {\r\n            name: \"tooltipShown\"\r\n        };\r\n        proto._eventsMap.onTooltipHidden = {\r\n            name: \"tooltipHidden\"\r\n        };\r\n        constructor.addChange({\r\n            code: \"TOOLTIP_RENDERER\",\r\n            handler: function() {\r\n                this._setTooltipRendererOptions()\r\n            },\r\n            isThemeDependent: true,\r\n            isOptionChange: true\r\n        });\r\n        constructor.addChange({\r\n            code: \"TOOLTIP\",\r\n            handler: function() {\r\n                this._setTooltipOptions()\r\n            },\r\n            isThemeDependent: true,\r\n            isOptionChange: true,\r\n            option: \"tooltip\"\r\n        })\r\n    },\r\n    fontFields: [\"tooltip.font\"]\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAIA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AACA;AAGA;AAAA;AAKA;AAAA;AAGA;AAIA;AACA;;;;;;;;;;;;;;AAGA,MAAM,SAAS,kJAAA,CAAA,UAAY,CAAC,MAAM;AAClC,MAAM,WAAW,KAAK,IAAI;AAC1B,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;AACvB,MAAM,2BAA2B;AAEjC,SAAS,YAAY,QAAQ;IACzB,SAAS,GAAG,CAAC;QACT,MAAM;IACV,GAAG,MAAM;AACb;AAEA,SAAS,wBAAwB,OAAO,EAAE,aAAa;IACnD,IAAI,SAAS;IACb,OAAQ;QACJ,KAAK;YACD,SAAS;gBACL,QAAQ,QAAQ,cAAc;YAClC;YACA;QACJ,KAAK;YACD,SAAS;gBACL,QAAQ;oBACJ,MAAM;oBACN,WAAW,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,gBAAgB;gBAChE;YACJ;IACR;IACA,OAAO;AACX;AAEA,SAAS;IACL,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,GAAG,CAAC;QAClB,UAAU;QACV,SAAS;QACT,SAAS;QACT,QAAQ;QACR,QAAQ;IACZ;AACJ;AAEA,SAAS,eAAe,QAAQ;IAC5B,SAAS,OAAO,CAAE,CAAA,KAAM,GAAG,MAAM;AACrC;AACO,IAAI,UAAU,SAAS,MAAM;IAChC,IAAI,CAAC,aAAa,GAAG,OAAO,YAAY;IACxC,IAAI,CAAC,WAAW,GAAG,OAAO,UAAU;IACpC,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM;IAC5B,IAAI,CAAC,mBAAmB,GAAG,EAAE;IAC7B,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,GAAG,CAAC;QAC3B,UAAU;QACV,UAAU;QACV,eAAe;IACnB,GAAG,QAAQ,CAAC,OAAO,QAAQ;IAC3B,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,IAAI,yKAAA,CAAA,WAAQ,CAAC;QAC3C,cAAc,OAAO,YAAY;QACjC,WAAW,IAAI,CAAC,QAAQ,CAAC,EAAE;IAC/B;IACA,MAAM,OAAO,SAAS,IAAI;IAC1B,KAAK,IAAI,CAAC;QACN,kBAAkB;IACtB;IACA,IAAI,CAAC,KAAK,GAAG,SAAS,IAAI,CAAC,KAAK,GAAG,GAAG;IACtC,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,GAAG,CAAC;QACjC,UAAU;QACV,SAAS;QACT,QAAQ;QACR,QAAQ;IACZ,GAAG,QAAQ,CAAC,IAAI,CAAC,QAAQ;IACzB,IAAI,CAAC,SAAS,GAAG,iBAAiB,QAAQ,CAAC,IAAI,CAAC,cAAc;AAClE;AACA,QAAQ,SAAS,GAAG;IAChB,aAAa;IACb,SAAS;QACL,IAAI,CAAC,QAAQ,CAAC,MAAM;QACpB,IAAI,CAAC,SAAS,CAAC,OAAO;QACtB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,WAAW,GAAG;IACvC;IACA,eAAe;QACX,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,QAAQ,SAAS;QAC7D,IAAI,MAAM,UAAU,MAAM,EAAE;YACxB,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,SAAS;QACnC;QACA,OAAO,CAAC,UAAU,MAAM,GAAG,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,OAAO,EAAE,GAAG,CAAC;IAC1D;IACA,aAAY,eAAe;QACvB,IAAI,CAAC,SAAS,GAAG,kBAAkB,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,mBAAmB;IACpF;IACA,YAAY,SAAS,OAAO;QACxB,UAAU,WAAW,CAAC;QACtB,MAAM,OAAO,IAAI;QACjB,KAAK,QAAQ,GAAG;QAChB,KAAK,eAAe,GAAG,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI;QACpD,KAAK,eAAe,CAAC,KAAK,GAAG,KAAK,eAAe,CAAC,IAAI;QACtD,KAAK,QAAQ,CAAC,GAAG,CAAC;YACd,QAAQ,QAAQ,MAAM;QAC1B;QACA,KAAK,iBAAiB,GAAG,QAAQ,gBAAgB;QACjD,MAAM,gBAAgB,KAAK,cAAc;QACzC,IAAI,IAAI,CAAC,MAAM,EAAE;YACb,IAAI,CAAC,MAAM,CAAC,KAAK;QACrB;QACA,IAAI,CAAC,WAAW,CAAC,QAAQ,eAAe;QACxC,MAAM,gBAAgB,QAAQ,WAAW,GAAG,SAAS;QACrD,IAAI,QAAQ,WAAW,EAAE;YACrB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;gBACpB,oBAAoB;gBACpB,uBAAuB;YAC3B;QACJ;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,0JAAA,CAAA,SAAM,CAAC;YACrB,SAAS,KAAK,QAAQ,CAAC,OAAO;YAC9B,OAAO,KAAK,QAAQ,CAAC,KAAK;YAC1B,QAAQ,KAAK,QAAQ,CAAC,MAAM;YAC5B,kBAAkB,KAAK,QAAQ,CAAC,gBAAgB;YAChD,kBAAkB,KAAK,QAAQ,CAAC,gBAAgB;YAChD,aAAa,KAAK,QAAQ,CAAC,WAAW;YACtC,YAAY;YACZ,QAAQ,KAAK,QAAQ,CAAC,MAAM;YAC5B,cAAc,KAAK,QAAQ,CAAC,YAAY;QAC5C,GAAG,MAAM,KAAK,SAAS,CAAC,IAAI,EAAG,CAAA;YAC3B,IAAI,EACA,OAAO,KAAK,EACZ,UAAU,QAAQ,EAClB,WAAW,SAAS,EACpB,UAAU,QAAQ,EAClB,kBAAkB,mBAAmB,KAAO,CAAC,EAChD,GAAG;YACJ,MAAM,QAAQ,KAAK,MAAM;YACzB,IAAI,CAAC,UAAU;gBACX,MAAM,WAAW,KAAK,SAAS;gBAC/B,MAAM,cAAc,YAAY,CAAC,MAAM,YAAY,CAAC,YAAY;gBAChE,IAAI,MAAM,IAAI,IAAI,aAAa;oBAC3B,cAAc,GAAG,CAAC;wBACd,OAAO,MAAM,SAAS;wBACtB,OAAO;wBACP,eAAe;oBACnB;oBACA,IAAI,aAAa;wBACb,MAAM,iBAAiB,KAAK,mBAAmB;wBAC/C,MAAM,4BAA4B,iBAAiB,QAAQ,CAAC,KAAK,cAAc;wBAC/E,eAAe,IAAI,CAAC;wBACpB,SAAS,MAAM,CAAC;4BACZ,OAAO,MAAM,YAAY;4BACzB,WAAW;4BACX,YAAY;gCACR,eAAe,eAAe,MAAM,CAAC,GAAG,eAAe,MAAM,GAAG;gCAChE,KAAK,SAAS,GAAG,CAAA,GAAA,4KAAA,CAAA,cAAW,AAAD,EAAE,KAAK,SAAS,EAAE;gCAC7C,MAAM,IAAI,GAAG,KAAK,SAAS,CAAC,IAAI;gCAChC,IAAI,MAAM,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS,KAAK,MAAM,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS,GAAG;oCACnE,IAAI,CAAC,MAAM,CAAC,KAAK;oCACjB,iBAAiB;oCACjB;gCACJ;gCACA;gCACA,KAAK,WAAW,CAAC;gCACjB,KAAK,YAAY;gCACjB,KAAK,MAAM,CAAC,cAAc,CAAC;oCACvB,MAAM,MAAM,KAAK;oCACjB,QAAQ,MAAM,WAAW;oCACzB,kBAAkB;gCACtB;gCACA,iBAAiB;gCACjB,KAAK,mBAAmB,GAAG,EAAE;4BACjC;wBACJ;wBACA;oBACJ,OAAO;wBACH,KAAK,KAAK,CAAC,IAAI,CAAC;4BACZ,MAAM;wBACV;wBACA,KAAK,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI;oBAClC;gBACJ,OAAO;oBACH,KAAK,KAAK,CAAC,GAAG,CAAC;wBACX,MAAM,MAAM,SAAS;oBACzB,GAAG,IAAI,CAAC;wBACJ,MAAM,MAAM,IAAI;wBAChB,OAAO,QAAQ,QAAQ;wBACvB,kBAAkB;oBACtB,GAAG,MAAM,CAAC,MAAM,IAAI,CAAC;wBACjB,OAAO,QAAQ,aAAa;oBAChC;gBACJ;gBACA,KAAK,WAAW,CAAC;gBACjB,KAAK,MAAM,CAAC,cAAc,CAAC;oBACvB,MAAM,MAAM,KAAK;oBACjB,QAAQ,MAAM,WAAW;oBACzB,kBAAkB;gBACtB;YACJ;YACA;YACA,KAAK,YAAY;YACjB,OAAO;QACX,GAAI,MAAO,CAAC,SAAS;YACjB,MAAM,QAAQ,QAAQ,MAAM;YAC5B,IAAI,MAAM,IAAI,EAAE;gBACZ,IAAI,OAAO,OAAO,gBAAgB,CAAC,KAAK,SAAS,CAAC,GAAG,CAAC;gBACtD,OAAO;oBACH,GAAG;oBACH,GAAG;oBACH,OAAO,SAAS,WAAW,KAAK,KAAK;oBACrC,QAAQ,SAAS,WAAW,KAAK,MAAM;gBAC3C;gBACA,OAAO;YACX;YACA,OAAO,EAAE,OAAO;QACpB,GAAK,CAAC,SAAS,GAAG,GAAG;YACjB,MAAM,QAAQ,QAAQ,MAAM;YAC5B,IAAI,MAAM,IAAI,EAAE;gBACZ,KAAK,cAAc,CAAC,GAAG,CAAC;oBACpB,MAAM;oBACN,KAAK;gBACT;YACJ,OAAO;gBACH,EAAE,IAAI,CAAC,GAAG;YACd;QACJ;QACA,OAAO;IACX;IACA,aAAa,SAAS,SAAS;QAC3B,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,aAAa,CAAC,iBAAiB,IAAI,CAAC,UAAU;QACtE,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,aAAa,CAAC,gBAAgB,IAAI,CAAC,UAAU;IACtD;IACA,oBAAoB,SAAS,OAAO;QAChC,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;QAC1B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;YACpB,WAAW,QAAQ,GAAG,GAAG,QAAQ;QACrC;QACA,OAAO,IAAI;IACf;IACA,QAAQ,SAAS,OAAO;QACpB,MAAM,OAAO,IAAI;QACjB,KAAK,UAAU,CAAC;QAChB,YAAY,KAAK,QAAQ;QACzB,MAAM,gBAAgB,CAAC;QACvB,IAAK,MAAM,QAAQ,KAAK,eAAe,CAAE;YACrC,MAAM,iBAAiB,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE;YAChC,aAAa,CAAC,eAAe,GAAG,CAAA,GAAA,8KAAA,CAAA,qBAAkB,AAAD,EAAE,gBAAgB,KAAK,eAAe,CAAC,KAAK;QACjG;QACA,KAAK,cAAc,CAAC,GAAG,CAAC;QACxB,KAAK,KAAK,CAAC,GAAG,CAAC,KAAK,eAAe;QACnC,KAAK,UAAU,GAAG;QAClB,OAAO;IACX;IACA,UAAU,SAAS,YAAY,EAAE,KAAK;QAClC,IAAI,mBAAmB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,IAAI,CAAC,iBAAiB;QAC9G,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,YAAY,CAAC;QACjB,IAAI,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,mBAAmB;YAC9B,YAAY,iBAAiB,IAAI,CAAC,cAAc;YAChD,YAAY,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,YAAY,CAAC;YACpD,IAAI,UAAU,WAAW;gBACrB,MAAM,IAAI,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,IAAI,IAAI,OAAO,UAAU,IAAI,IAAI;YACtE;YACA,IAAI,UAAU,WAAW;gBACrB,MAAM,IAAI,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,IAAI,IAAI,OAAO,UAAU,IAAI,IAAI;YACtE;QACJ;QACA,IAAI,CAAC,CAAC,UAAU,KAAK,KAAK,CAAC,CAAC,UAAU,KAAK,GAAG;YAC1C,MAAM,IAAI,GAAG,aAAa,SAAS,IAAI,aAAa,WAAW,IAAI;QACvE;QACA,MAAM,KAAK,GAAG,UAAU,KAAK,IAAI,QAAQ,KAAK;QAC9C,MAAM,WAAW,GAAG,UAAU,WAAW,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,CAAC,EAAE,KAAK;QACzE,MAAM,SAAS,GAAG,UAAU,SAAS,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,CAAC,CAAC,EAAE,KAAK;QAC3E,OAAO,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,IAAI,CAAC,SAAS;IAC3D;IACA,MAAM,SAAS,YAAY,EAAE,MAAM,EAAE,SAAS,EAAE,gBAAgB,EAAE,gBAAgB;QAC9E,MAAM,OAAO,IAAI;QACjB,IAAI,KAAK,QAAQ,CAAC,WAAW,EAAE;YAC3B,UAAU,CAAC,GAAG,OAAO,CAAC;YACtB,UAAU,CAAC,GAAG,OAAO,CAAC,GAAG,OAAO,MAAM;YACtC,KAAK,WAAW,CAAC;YACjB,OAAO;QACX;QACA,MAAM,QAAQ;YACV,cAAc;YACd,WAAW;YACX,kBAAkB;QACtB;QACA,IAAI,CAAC,KAAK,QAAQ,CAAC,cAAc,OAAO,mBAAmB;YACvD,OAAO;QACX;QACA,KAAK,MAAM,GAAG;QACd,KAAK,QAAQ,CAAC,QAAQ,CAAC,KAAK,aAAa;QACzC,KAAK,MAAM;QACX,MAAM,aAAa,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,KAAK,QAAQ,EAAE;YACzC,QAAQ,KAAK,UAAU;QAC3B,GAAG,OAAO;YACN,GAAG,OAAO,CAAC;YACX,GAAG,OAAO,CAAC;YACX,QAAQ,OAAO,MAAM;QACzB;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,IAAI,CAAC;IACpC;IACA,mBAAmB,SAAS,CAAC,EAAE,CAAC;QAC5B,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE;YAC3B,MAAM,MAAM,IAAI,CAAC,MAAM,CAAC,OAAO;YAC/B,OAAO,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,GAAG,IAAI,MAAM;QACpF;QACA,OAAO;IACX;IACA,MAAM,SAAS,YAAY;QACvB,MAAM,OAAO,IAAI;QACjB,YAAY,KAAK,QAAQ;QACzB,IAAI,KAAK,UAAU,EAAE;YACjB,KAAK,aAAa,CAAC,iBAAiB,KAAK,QAAQ,CAAC,WAAW,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;gBACnE,cAAc;YAClB,GAAG,KAAK,UAAU,IAAI,KAAK,UAAU;YACrC,KAAK,MAAM;YACX,KAAK,UAAU,GAAG;QACtB;IACJ;IACA;QACI,IAAI,CAAC,SAAS,CAAC,KAAK;IACxB;IACA,MAAM,SAAS,CAAC,EAAE,CAAC,EAAE,MAAM;QACvB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,GAAG;YACH,GAAG;YACH,QAAQ;YACR,QAAQ,IAAI,CAAC,UAAU;YACvB,UAAU;QACd;IACJ;IACA,cAAc;QACV,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,OAAO;QACtC,KAAK,SAAS,CAAC,MAAM,CAAC,WAAW,KAAK,EAAE,WAAW,MAAM;QACzD,MAAM,SAAS,KAAK,QAAQ,CAAC,GAAG,CAAC;YAC7B,MAAM;YACN,KAAK;QACT,GAAG,MAAM;QACT,MAAM,OAAO,WAAW,CAAC;QACzB,MAAM,MAAM,WAAW,CAAC;QACxB,KAAK,QAAQ,CAAC,GAAG,CAAC;YACd,MAAM,OAAO,OAAO,IAAI;YACxB,KAAK,MAAM,OAAO,GAAG;QACzB;QACA,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC;QAC7B,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE;YAClB,KAAK,SAAS,CAAC,GAAG,CAAC;gBACf,MAAM,CAAC;gBACP,KAAK,CAAC;YACV;YACA,KAAK,cAAc,CAAC,GAAG,CAAC;gBACpB,OAAO,SAAS,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,SAAS;YAC3C;QACJ;IACJ;IACA,aAAa,SAAS,KAAK,EAAE,cAAc;QACvC,MAAM,UAAU,iBAAiB,wBAAwB,IAAI,CAAC,QAAQ,EAAE,kBAAkB,IAAI,CAAC,QAAQ;QACvG,OAAO,OAAO,OAAO,QAAQ,MAAM;IACvC;IACA;QACI,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,aAAa;QACT,OAAO,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,QAAQ;IAC/C;IACA,WAAW;QACP,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW;IACjE;IACA,UAAU;QACN,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM;IACjC;IACA,YAAY;QACR,MAAM,YAAY,IAAI,CAAC,aAAa;QACpC,MAAM,eAAe,UAAU,qBAAqB;QACpD,MAAM,OAAO,wJAAA,CAAA,UAAU,CAAC,kBAAkB;QAC1C,MAAM,WAAW,wJAAA,CAAA,UAAU,CAAC,WAAW;QACvC,IAAI,OAAO,OAAO,WAAW,IAAI,KAAK,UAAU,IAAI;QACpD,IAAI,MAAM,OAAO,WAAW,IAAI,KAAK,SAAS,IAAI;QAClD,MAAM,MAAM;YACR,MAAM;YACN,KAAK;YACL,OAAO,QAAQ,KAAK,WAAW,EAAE,SAAS,IAAI,CAAC,WAAW,IAAI;YAC9D,QAAQ,QAAQ,SAAS,IAAI,CAAC,YAAY,EAAE,KAAK,YAAY,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,KAAK,YAAY,EAAE,SAAS,IAAI,CAAC,YAAY,EAAE,KAAK,YAAY;YAC3J,OAAO;YACP,QAAQ;QACZ;QACA,IAAI,cAAc,wJAAA,CAAA,UAAU,CAAC,OAAO,IAAI;YACpC,OAAO,QAAQ,IAAI,IAAI,EAAE,IAAI,IAAI,GAAG,aAAa,IAAI;YACrD,MAAM,QAAQ,IAAI,GAAG,EAAE,IAAI,GAAG,GAAG,aAAa,GAAG;YACjD,IAAI,KAAK,GAAG,QAAQ,aAAa,KAAK,EAAE,IAAI,KAAK,IAAI,OAAO,IAAI,IAAI;YACpE,IAAI,MAAM,GAAG,QAAQ,aAAa,MAAM,EAAE,IAAI,MAAM,IAAI,MAAM,IAAI,GAAG;YACrE,IAAI,IAAI,GAAG;YACX,IAAI,GAAG,GAAG;QACd;QACA,OAAO;IACX;AACJ;AACO,MAAM,SAAS;IAClB,MAAM;IACN,MAAM;QACF,IAAI,CAAC,YAAY;IACrB;IACA,SAAS;QACL,IAAI,CAAC,eAAe;IACxB;IACA,SAAS;QACL,cAAc;YACV,IAAI,CAAC,QAAQ,GAAG,IAAI,QAAQ;gBACxB,UAAU,IAAI,CAAC,gBAAgB,GAAG;gBAClC,cAAc,IAAI,CAAC,aAAa;gBAChC,cAAc,IAAI,CAAC,MAAM,CAAC;gBAC1B,YAAY,IAAI,CAAC,OAAO;gBACxB,QAAQ,IAAI;YAChB;QACJ;QACA,iBAAiB;YACb,IAAI,CAAC,QAAQ,CAAC,OAAO;YACrB,IAAI,CAAC,QAAQ,GAAG;QACpB;QACA,4BAA4B;YACxB,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB;QAC7D;QACA,oBAAoB;YAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC;QACzC;IACJ;IACA,WAAW;QACP;YACI,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI;QACvC;IACJ;IACA,WAAW,SAAS,WAAW;QAC3B,MAAM,QAAQ,YAAY,SAAS;QACnC,MAAM,UAAU,CAAC,cAAc,GAAG;YAC9B,MAAM;QACV;QACA,MAAM,UAAU,CAAC,eAAe,GAAG;YAC/B,MAAM;QACV;QACA,YAAY,SAAS,CAAC;YAClB,MAAM;YACN,SAAS;gBACL,IAAI,CAAC,0BAA0B;YACnC;YACA,kBAAkB;YAClB,gBAAgB;QACpB;QACA,YAAY,SAAS,CAAC;YAClB,MAAM;YACN,SAAS;gBACL,IAAI,CAAC,kBAAkB;YAC3B;YACA,kBAAkB;YAClB,gBAAgB;YAChB,QAAQ;QACZ;IACJ;IACA,YAAY;QAAC;KAAe;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4032, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/annotations.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/annotations.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport domAdapter from \"../../core/dom_adapter\";\r\nimport {\r\n    isDefined,\r\n    isFunction\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    Tooltip\r\n} from \"../core/tooltip\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    patchFontOptions\r\n} from \"./utils\";\r\nimport {\r\n    Plaque\r\n} from \"./plaque\";\r\nimport pointerEvents from \"../../common/core/events/pointer\";\r\nimport {\r\n    start as dragEventStart,\r\n    move as dragEventMove,\r\n    end as dragEventEnd\r\n} from \"../../common/core/events/drag\";\r\nimport {\r\n    addNamespace\r\n} from \"../../common/core/events/utils/index\";\r\nimport eventsEngine from \"../../common/core/events/core/events_engine\";\r\nconst getDocument = domAdapter.getDocument;\r\nconst EVENT_NS = \"annotations\";\r\nconst DOT_EVENT_NS = \".\" + EVENT_NS;\r\nconst POINTER_ACTION = addNamespace([pointerEvents.down, pointerEvents.move], EVENT_NS);\r\nconst POINTER_UP_EVENT_NAME = addNamespace(pointerEvents.up, EVENT_NS);\r\nconst DRAG_START_EVENT_NAME = dragEventStart + DOT_EVENT_NS;\r\nconst DRAG_EVENT_NAME = dragEventMove + DOT_EVENT_NS;\r\nconst DRAG_END_EVENT_NAME = dragEventEnd + DOT_EVENT_NS;\r\n\r\nfunction coreAnnotation(options, contentTemplate) {\r\n    return {\r\n        draw: function(widget, group) {\r\n            const annotationGroup = widget._renderer.g().append(group).css(patchFontOptions(options.font));\r\n            if (this.plaque) {\r\n                this.plaque.clear()\r\n            }\r\n            this.plaque = new Plaque(extend(true, {}, options, {\r\n                cornerRadius: (options.border || {}).cornerRadius\r\n            }), widget, annotationGroup, contentTemplate, widget._isAnnotationBounded(options));\r\n            this.plaque.draw(widget._getAnnotationCoords(this));\r\n            if (options.allowDragging) {\r\n                annotationGroup.on(DRAG_START_EVENT_NAME, {\r\n                    immediate: true\r\n                }, (e => {\r\n                    this._dragOffsetX = this.plaque.x - e.pageX;\r\n                    this._dragOffsetY = this.plaque.y - e.pageY\r\n                })).on(DRAG_EVENT_NAME, (e => {\r\n                    this.plaque.move(e.pageX + this._dragOffsetX, e.pageY + this._dragOffsetY)\r\n                })).on(DRAG_END_EVENT_NAME, (e => {\r\n                    this.offsetX = (this.offsetX || 0) + e.offset.x;\r\n                    this.offsetY = (this.offsetY || 0) + e.offset.y\r\n                }))\r\n            }\r\n        },\r\n        hitTest(x, y) {\r\n            return this.plaque.hitTest(x, y)\r\n        },\r\n        showTooltip(tooltip, _ref) {\r\n            let {\r\n                x: x,\r\n                y: y\r\n            } = _ref;\r\n            const that = this;\r\n            const options = that.options;\r\n            if (tooltip.annotation !== that) {\r\n                tooltip.setTemplate(options.tooltipTemplate);\r\n                const callback = result => {\r\n                    result && (tooltip.annotation = that)\r\n                };\r\n                callback(tooltip.show(options, {\r\n                    x: x,\r\n                    y: y\r\n                }, {\r\n                    target: options\r\n                }, options.customizeTooltip, callback))\r\n            } else if (!tooltip.isCursorOnTooltip(x, y)) {\r\n                tooltip.move(x, y)\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nfunction getTemplateFunction(options, widget) {\r\n    let template;\r\n    if (\"text\" === options.type) {\r\n        template = function(item, groupElement) {\r\n            const text = widget._renderer.text(item.text).attr({\r\n                class: item.cssClass\r\n            }).append({\r\n                element: groupElement\r\n            });\r\n            if (item.width > 0 || item.height > 0) {\r\n                text.setMaxSize(item.width, item.height, {\r\n                    wordWrap: item.wordWrap,\r\n                    textOverflow: item.textOverflow\r\n                })\r\n            }\r\n        }\r\n    } else if (\"image\" === options.type) {\r\n        template = function(item, groupElement) {\r\n            const {\r\n                width: width,\r\n                height: height,\r\n                url: url,\r\n                location: location\r\n            } = item.image || {};\r\n            const {\r\n                width: outerWidth,\r\n                height: outerHeight\r\n            } = item;\r\n            const imageWidth = outerWidth > 0 ? Math.min(width, outerWidth) : width;\r\n            const imageHeight = outerHeight > 0 ? Math.min(height, outerHeight) : height;\r\n            widget._renderer.image(0, 0, imageWidth, imageHeight, url, location || \"center\").append({\r\n                element: groupElement\r\n            })\r\n        }\r\n    } else if (\"custom\" === options.type) {\r\n        template = options.template\r\n    }\r\n    return template\r\n}\r\n\r\nfunction getImageObject(image) {\r\n    return \"string\" === typeof image ? {\r\n        url: image\r\n    } : image\r\n}\r\nexport let createAnnotations = function(widget, items) {\r\n    let commonAnnotationSettings = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};\r\n    let customizeAnnotation = arguments.length > 3 ? arguments[3] : void 0;\r\n    let pullOptions = arguments.length > 4 ? arguments[4] : void 0;\r\n    const commonImageOptions = getImageObject(commonAnnotationSettings.image);\r\n    return items.reduce(((arr, item) => {\r\n        const currentImageOptions = getImageObject(item.image);\r\n        const customizedItem = isFunction(customizeAnnotation) ? customizeAnnotation(item) : {};\r\n        if (customizedItem) {\r\n            customizedItem.image = getImageObject(customizedItem.image)\r\n        }\r\n        const options = extend(true, {}, commonAnnotationSettings, item, {\r\n            image: commonImageOptions\r\n        }, {\r\n            image: currentImageOptions\r\n        }, customizedItem);\r\n        const templateFunction = getTemplateFunction(options, widget);\r\n        const annotation = templateFunction && extend(true, pullOptions(options), coreAnnotation(options, widget._getTemplate(templateFunction)));\r\n        annotation && arr.push(annotation);\r\n        return arr\r\n    }), [])\r\n};\r\nconst chartPlugin = {\r\n    name: \"annotations_chart\",\r\n    init() {},\r\n    dispose() {},\r\n    members: {\r\n        _getAnnotationCoords(annotation) {\r\n            var _axis, _axis2;\r\n            const coords = {\r\n                offsetX: annotation.offsetX,\r\n                offsetY: annotation.offsetY\r\n            };\r\n            const argCoordName = this._options.silent(\"rotated\") ? \"y\" : \"x\";\r\n            const valCoordName = this._options.silent(\"rotated\") ? \"x\" : \"y\";\r\n            const argAxis = this.getArgumentAxis();\r\n            const argument = argAxis.validateUnit(annotation.argument);\r\n            let axis = this.getValueAxis(annotation.axis);\r\n            let series;\r\n            let pane = null === (_axis = axis) || void 0 === _axis ? void 0 : _axis.pane;\r\n            if (annotation.series) {\r\n                var _series;\r\n                series = this.series.filter((s => s.name === annotation.series))[0];\r\n                axis = null === (_series = series) || void 0 === _series ? void 0 : _series.getValueAxis();\r\n                isDefined(axis) && (pane = axis.pane)\r\n            }\r\n            if (isDefined(argument)) {\r\n                if (series) {\r\n                    const center = series.getPointCenterByArg(argument);\r\n                    center && (coords[argCoordName] = center[argCoordName])\r\n                } else {\r\n                    coords[argCoordName] = argAxis.getTranslator().translate(argument)\r\n                }!isDefined(pane) && (pane = argAxis.pane)\r\n            }\r\n            const value = null === (_axis2 = axis) || void 0 === _axis2 ? void 0 : _axis2.validateUnit(annotation.value);\r\n            if (isDefined(value)) {\r\n                var _axis3;\r\n                coords[valCoordName] = null === (_axis3 = axis) || void 0 === _axis3 ? void 0 : _axis3.getTranslator().translate(value);\r\n                !isDefined(pane) && isDefined(axis) && (pane = axis.pane)\r\n            }\r\n            coords.canvas = this._getCanvasForPane(pane);\r\n            if (isDefined(coords[argCoordName]) && !isDefined(value)) {\r\n                var _series2;\r\n                if (!isDefined(axis) && !isDefined(series)) {\r\n                    coords[valCoordName] = argAxis.getAxisPosition()\r\n                } else if (isDefined(axis) && !isDefined(series)) {\r\n                    coords[valCoordName] = this._argumentAxes.filter((a => a.pane === axis.pane))[0].getAxisPosition()\r\n                } else if (null !== (_series2 = series) && void 0 !== _series2 && _series2.checkSeriesViewportCoord(argAxis, coords[argCoordName])) {\r\n                    coords[valCoordName] = series.getSeriesPairCoord(coords[argCoordName], true)\r\n                }\r\n            }\r\n            if (!isDefined(argument) && isDefined(coords[valCoordName])) {\r\n                if (isDefined(axis) && !isDefined(series)) {\r\n                    coords[argCoordName] = axis.getAxisPosition()\r\n                } else if (isDefined(series)) {\r\n                    if (series.checkSeriesViewportCoord(axis, coords[valCoordName])) {\r\n                        coords[argCoordName] = series.getSeriesPairCoord(coords[valCoordName], false)\r\n                    }\r\n                }\r\n            }\r\n            return coords\r\n        },\r\n        _annotationsPointerEventHandler(event) {\r\n            if (this._disposed) {\r\n                return\r\n            }\r\n            const originalEvent = event.originalEvent || {};\r\n            const touch = originalEvent.touches && originalEvent.touches[0] || {};\r\n            const rootOffset = this._renderer.getRootOffset();\r\n            const coords = {\r\n                x: touch.pageX || originalEvent.pageX || event.pageX,\r\n                y: touch.pageY || originalEvent.pageY || event.pageY\r\n            };\r\n            const annotation = this._annotations.items.filter((a => a.hitTest(coords.x - rootOffset.left, coords.y - rootOffset.top)))[0];\r\n            if (!annotation || !annotation.options.tooltipEnabled) {\r\n                this._annotations.hideTooltip();\r\n                return\r\n            }\r\n            this._clear();\r\n            if (annotation.options.allowDragging && event.type === pointerEvents.down) {\r\n                this._annotations._hideToolTipForDrag = true\r\n            }\r\n            if (!this._annotations._hideToolTipForDrag) {\r\n                annotation.showTooltip(this._annotations.tooltip, coords);\r\n                event.stopPropagation()\r\n            }\r\n        },\r\n        _isAnnotationBounded: options => isDefined(options.value) || isDefined(options.argument),\r\n        _pullOptions: options => ({\r\n            type: options.type,\r\n            name: options.name,\r\n            x: options.x,\r\n            y: options.y,\r\n            value: options.value,\r\n            argument: options.argument,\r\n            axis: options.axis,\r\n            series: options.series,\r\n            options: options,\r\n            offsetX: options.offsetX,\r\n            offsetY: options.offsetY\r\n        }),\r\n        _forceAnnotationRender() {\r\n            this._change([\"FORCE_RENDER\"])\r\n        },\r\n        _clear() {\r\n            this.hideTooltip();\r\n            this.clearHover()\r\n        }\r\n    }\r\n};\r\nconst polarChartPlugin = {\r\n    name: \"annotations_polar_chart\",\r\n    init() {},\r\n    dispose() {},\r\n    members: {\r\n        _getAnnotationCoords(annotation) {\r\n            const coords = {\r\n                offsetX: annotation.offsetX,\r\n                offsetY: annotation.offsetY,\r\n                canvas: this._calcCanvas()\r\n            };\r\n            const argAxis = this.getArgumentAxis();\r\n            let argument = argAxis.validateUnit(annotation.argument);\r\n            const value = this.getValueAxis().validateUnit(annotation.value);\r\n            const radius = annotation.radius;\r\n            const angle = annotation.angle;\r\n            let pointCoords;\r\n            let series;\r\n            if (annotation.series) {\r\n                series = this.series.filter((s => s.name === annotation.series))[0]\r\n            }\r\n            extend(true, coords, this.getXYFromPolar(angle, radius, argument, value));\r\n            if (isDefined(series)) {\r\n                if (isDefined(coords.angle) && !isDefined(value) && !isDefined(radius)) {\r\n                    if (!isDefined(argument)) {\r\n                        argument = argAxis.getTranslator().from(isFinite(angle) ? this.getActualAngle(angle) : coords.angle)\r\n                    }\r\n                    pointCoords = series.getSeriesPairCoord({\r\n                        argument: argument,\r\n                        angle: -coords.angle\r\n                    }, true)\r\n                } else if (isDefined(coords.radius) && !isDefined(argument) && !isDefined(angle)) {\r\n                    pointCoords = series.getSeriesPairCoord({\r\n                        radius: coords.radius\r\n                    }, false)\r\n                }\r\n                if (isDefined(pointCoords)) {\r\n                    coords.x = pointCoords.x;\r\n                    coords.y = pointCoords.y\r\n                }\r\n            }\r\n            if (annotation.series && !isDefined(pointCoords)) {\r\n                coords.x = coords.y = void 0\r\n            }\r\n            return coords\r\n        },\r\n        _annotationsPointerEventHandler: chartPlugin.members._annotationsPointerEventHandler,\r\n        _isAnnotationBounded: chartPlugin.members._isAnnotationBounded,\r\n        _pullOptions(options) {\r\n            const polarOptions = extend({}, {\r\n                radius: options.radius,\r\n                angle: options.angle\r\n            }, chartPlugin.members._pullOptions(options));\r\n            delete polarOptions.axis;\r\n            return polarOptions\r\n        },\r\n        _forceAnnotationRender: chartPlugin.members._forceAnnotationRender,\r\n        _clear: chartPlugin.members._clear\r\n    }\r\n};\r\nconst vectorMapPlugin = {\r\n    name: \"annotations_vector_map\",\r\n    init() {},\r\n    dispose() {\r\n        this._annotations._offTracker();\r\n        this._annotations._offTracker = null\r\n    },\r\n    members: {\r\n        _getAnnotationCoords(annotation) {\r\n            const coords = {\r\n                offsetX: annotation.offsetX,\r\n                offsetY: annotation.offsetY\r\n            };\r\n            coords.canvas = this._projection.getCanvas();\r\n            if (annotation.coordinates) {\r\n                const data = this._projection.toScreenPoint(annotation.coordinates);\r\n                coords.x = data[0];\r\n                coords.y = data[1]\r\n            }\r\n            return coords\r\n        },\r\n        _annotationsPointerEventHandler: chartPlugin.members._annotationsPointerEventHandler,\r\n        _isAnnotationBounded: options => isDefined(options.coordinates),\r\n        _pullOptions(options) {\r\n            const vectorMapOptions = extend({}, {\r\n                coordinates: options.coordinates\r\n            }, chartPlugin.members._pullOptions(options));\r\n            delete vectorMapOptions.axis;\r\n            delete vectorMapOptions.series;\r\n            delete vectorMapOptions.argument;\r\n            delete vectorMapOptions.value;\r\n            return vectorMapOptions\r\n        },\r\n        _forceAnnotationRender() {\r\n            this._change([\"EXTRA_ELEMENTS\"])\r\n        },\r\n        _getAnnotationStyles: () => ({\r\n            \"text-anchor\": \"start\"\r\n        }),\r\n        _clear() {}\r\n    },\r\n    extenders: {\r\n        _prepareExtraElements() {\r\n            const that = this;\r\n            const renderElements = () => {\r\n                that._renderExtraElements()\r\n            };\r\n            that._annotations._offTracker = that._tracker.on({\r\n                move: renderElements,\r\n                zoom: renderElements,\r\n                end: renderElements\r\n            })\r\n        }\r\n    }\r\n};\r\nconst pieChartPlugin = {\r\n    name: \"annotations_pie_chart\",\r\n    init() {},\r\n    dispose() {},\r\n    members: {\r\n        _getAnnotationCoords(annotation) {\r\n            let series;\r\n            const coords = {\r\n                offsetX: annotation.offsetX,\r\n                offsetY: annotation.offsetY,\r\n                canvas: this._canvas\r\n            };\r\n            if (annotation.argument) {\r\n                if (annotation.series) {\r\n                    series = this.getSeriesByName(annotation.series)\r\n                } else {\r\n                    series = this.series[0]\r\n                }\r\n                const argument = series.getPointsByArg(annotation.argument)[0];\r\n                const {\r\n                    x: x,\r\n                    y: y\r\n                } = argument.getAnnotationCoords(annotation.location);\r\n                coords.x = x;\r\n                coords.y = y\r\n            }\r\n            return coords\r\n        },\r\n        _isAnnotationBounded: options => options.argument,\r\n        _annotationsPointerEventHandler: chartPlugin.members._annotationsPointerEventHandler,\r\n        _pullOptions(options) {\r\n            const pieChartOptions = extend({}, {\r\n                location: options.location\r\n            }, chartPlugin.members._pullOptions(options));\r\n            delete pieChartOptions.axis;\r\n            return pieChartOptions\r\n        },\r\n        _clear: chartPlugin.members._clear,\r\n        _forceAnnotationRender: chartPlugin.members._forceAnnotationRender\r\n    }\r\n};\r\nconst corePlugin = {\r\n    name: \"annotations_core\",\r\n    init() {\r\n        this._annotations = {\r\n            items: [],\r\n            _hideToolTipForDrag: false,\r\n            tooltip: new Tooltip({\r\n                cssClass: `${this._rootClassPrefix}-annotation-tooltip`,\r\n                eventTrigger: this._eventTrigger,\r\n                widgetRoot: this.element(),\r\n                widget: this\r\n            }),\r\n            hideTooltip() {\r\n                this.tooltip.annotation = null;\r\n                this.tooltip.hide()\r\n            },\r\n            clearItems() {\r\n                this.items.forEach((i => i.plaque.clear()));\r\n                this.items = []\r\n            }\r\n        };\r\n        this._annotations.tooltip.setRendererOptions(this._getRendererOptions())\r\n    },\r\n    dispose() {\r\n        this._annotationsGroup.linkRemove().linkOff();\r\n        eventsEngine.off(getDocument(), DOT_EVENT_NS);\r\n        this._annotationsGroup.off(DOT_EVENT_NS);\r\n        this._annotations.tooltip && this._annotations.tooltip.dispose()\r\n    },\r\n    extenders: {\r\n        _createHtmlStructure() {\r\n            this._annotationsGroup = this._renderer.g().attr({\r\n                class: `${this._rootClassPrefix}-annotations`\r\n            }).css(this._getAnnotationStyles()).linkOn(this._renderer.root, \"annotations\").linkAppend();\r\n            eventsEngine.on(getDocument(), POINTER_ACTION, (e => {\r\n                if (this._disposed) {\r\n                    return\r\n                }\r\n                if (!this._annotations.tooltip.isCursorOnTooltip(e.pageX, e.pageY)) {\r\n                    this._annotations.hideTooltip()\r\n                }\r\n            }));\r\n            eventsEngine.on(getDocument(), POINTER_UP_EVENT_NAME, (event => {\r\n                this._annotations._hideToolTipForDrag = false;\r\n                this._annotationsPointerEventHandler(event)\r\n            }));\r\n            this._annotationsGroup.on(POINTER_ACTION, this._annotationsPointerEventHandler.bind(this))\r\n        },\r\n        _renderExtraElements() {\r\n            this._annotationsGroup.clear();\r\n            this._annotations.items.forEach((item => item.draw(this, this._annotationsGroup)))\r\n        },\r\n        _stopCurrentHandling() {\r\n            this._annotations.hideTooltip()\r\n        }\r\n    },\r\n    members: {\r\n        _buildAnnotations() {\r\n            this._annotations.clearItems();\r\n            const items = this._getOption(\"annotations\", true);\r\n            if (!(null !== items && void 0 !== items && items.length)) {\r\n                return\r\n            }\r\n            this._annotations.items = createAnnotations(this, items, this._getOption(\"commonAnnotationSettings\"), this._getOption(\"customizeAnnotation\", true), this._pullOptions)\r\n        },\r\n        _setAnnotationTooltipOptions() {\r\n            const tooltipOptions = extend({}, this._getOption(\"tooltip\"));\r\n            tooltipOptions.contentTemplate = tooltipOptions.customizeTooltip = void 0;\r\n            this._annotations.tooltip.update(tooltipOptions)\r\n        },\r\n        _getAnnotationCoords: () => ({}),\r\n        _pullOptions: () => ({}),\r\n        _getAnnotationStyles: () => ({})\r\n    },\r\n    customize(constructor) {\r\n        constructor.addChange({\r\n            code: \"ANNOTATIONITEMS\",\r\n            handler() {\r\n                this._requestChange([\"ANNOTATIONS\"])\r\n            },\r\n            isOptionChange: true,\r\n            option: \"annotations\"\r\n        });\r\n        constructor.addChange({\r\n            code: \"ANNOTATIONSSETTINGS\",\r\n            handler() {\r\n                this._requestChange([\"ANNOTATIONS\"])\r\n            },\r\n            isOptionChange: true,\r\n            option: \"commonAnnotationSettings\"\r\n        });\r\n        constructor.addChange({\r\n            code: \"ANNOTATIONS\",\r\n            handler() {\r\n                this._buildAnnotations();\r\n                this._setAnnotationTooltipOptions();\r\n                this._forceAnnotationRender()\r\n            },\r\n            isThemeDependent: true,\r\n            isOptionChange: true\r\n        })\r\n    },\r\n    fontFields: [\"commonAnnotationSettings.font\"]\r\n};\r\nexport const plugins = {\r\n    core: corePlugin,\r\n    chart: chartPlugin,\r\n    polarChart: polarChartPlugin,\r\n    vectorMap: vectorMapPlugin,\r\n    pieChart: pieChartPlugin\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AACA;AAAA;AAIA;AAGA;AAAA;AAGA;AAGA;AAGA;AAAA;AACA;AAAA;AAKA;AAAA;AAGA;AAAA;;;;;;;;;;;AACA,MAAM,cAAc,wJAAA,CAAA,UAAU,CAAC,WAAW;AAC1C,MAAM,WAAW;AACjB,MAAM,eAAe,MAAM;AAC3B,MAAM,iBAAiB,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE;IAAC,yKAAA,CAAA,UAAa,CAAC,IAAI;IAAE,yKAAA,CAAA,UAAa,CAAC,IAAI;CAAC,EAAE;AAC9E,MAAM,wBAAwB,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yKAAA,CAAA,UAAa,CAAC,EAAE,EAAE;AAC7D,MAAM,wBAAwB,sKAAA,CAAA,QAAc,GAAG;AAC/C,MAAM,kBAAkB,sKAAA,CAAA,OAAa,GAAG;AACxC,MAAM,sBAAsB,sKAAA,CAAA,MAAY,GAAG;AAE3C,SAAS,eAAe,OAAO,EAAE,eAAe;IAC5C,OAAO;QACH,MAAM,SAAS,MAAM,EAAE,KAAK;YACxB,MAAM,kBAAkB,OAAO,SAAS,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI;YAC5F,IAAI,IAAI,CAAC,MAAM,EAAE;gBACb,IAAI,CAAC,MAAM,CAAC,KAAK;YACrB;YACA,IAAI,CAAC,MAAM,GAAG,IAAI,0JAAA,CAAA,SAAM,CAAC,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,SAAS;gBAC/C,cAAc,CAAC,QAAQ,MAAM,IAAI,CAAC,CAAC,EAAE,YAAY;YACrD,IAAI,QAAQ,iBAAiB,iBAAiB,OAAO,oBAAoB,CAAC;YAC1E,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,oBAAoB,CAAC,IAAI;YACjD,IAAI,QAAQ,aAAa,EAAE;gBACvB,gBAAgB,EAAE,CAAC,uBAAuB;oBACtC,WAAW;gBACf,GAAI,CAAA;oBACA,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK;oBAC3C,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,KAAK;gBAC/C,GAAI,EAAE,CAAC,iBAAkB,CAAA;oBACrB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY,EAAE,EAAE,KAAK,GAAG,IAAI,CAAC,YAAY;gBAC7E,GAAI,EAAE,CAAC,qBAAsB,CAAA;oBACzB,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;oBAC/C,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,OAAO,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;gBACnD;YACJ;QACJ;QACA,SAAQ,CAAC,EAAE,CAAC;YACR,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC,GAAG;QAClC;QACA,aAAY,OAAO,EAAE,IAAI;YACrB,IAAI,EACA,GAAG,CAAC,EACJ,GAAG,CAAC,EACP,GAAG;YACJ,MAAM,OAAO,IAAI;YACjB,MAAM,UAAU,KAAK,OAAO;YAC5B,IAAI,QAAQ,UAAU,KAAK,MAAM;gBAC7B,QAAQ,WAAW,CAAC,QAAQ,eAAe;gBAC3C,MAAM,WAAW,CAAA;oBACb,UAAU,CAAC,QAAQ,UAAU,GAAG,IAAI;gBACxC;gBACA,SAAS,QAAQ,IAAI,CAAC,SAAS;oBAC3B,GAAG;oBACH,GAAG;gBACP,GAAG;oBACC,QAAQ;gBACZ,GAAG,QAAQ,gBAAgB,EAAE;YACjC,OAAO,IAAI,CAAC,QAAQ,iBAAiB,CAAC,GAAG,IAAI;gBACzC,QAAQ,IAAI,CAAC,GAAG;YACpB;QACJ;IACJ;AACJ;AAEA,SAAS,oBAAoB,OAAO,EAAE,MAAM;IACxC,IAAI;IACJ,IAAI,WAAW,QAAQ,IAAI,EAAE;QACzB,WAAW,SAAS,IAAI,EAAE,YAAY;YAClC,MAAM,OAAO,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,IAAI,EAAE,IAAI,CAAC;gBAC/C,OAAO,KAAK,QAAQ;YACxB,GAAG,MAAM,CAAC;gBACN,SAAS;YACb;YACA,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,GAAG,GAAG;gBACnC,KAAK,UAAU,CAAC,KAAK,KAAK,EAAE,KAAK,MAAM,EAAE;oBACrC,UAAU,KAAK,QAAQ;oBACvB,cAAc,KAAK,YAAY;gBACnC;YACJ;QACJ;IACJ,OAAO,IAAI,YAAY,QAAQ,IAAI,EAAE;QACjC,WAAW,SAAS,IAAI,EAAE,YAAY;YAClC,MAAM,EACF,OAAO,KAAK,EACZ,QAAQ,MAAM,EACd,KAAK,GAAG,EACR,UAAU,QAAQ,EACrB,GAAG,KAAK,KAAK,IAAI,CAAC;YACnB,MAAM,EACF,OAAO,UAAU,EACjB,QAAQ,WAAW,EACtB,GAAG;YACJ,MAAM,aAAa,aAAa,IAAI,KAAK,GAAG,CAAC,OAAO,cAAc;YAClE,MAAM,cAAc,cAAc,IAAI,KAAK,GAAG,CAAC,QAAQ,eAAe;YACtE,OAAO,SAAS,CAAC,KAAK,CAAC,GAAG,GAAG,YAAY,aAAa,KAAK,YAAY,UAAU,MAAM,CAAC;gBACpF,SAAS;YACb;QACJ;IACJ,OAAO,IAAI,aAAa,QAAQ,IAAI,EAAE;QAClC,WAAW,QAAQ,QAAQ;IAC/B;IACA,OAAO;AACX;AAEA,SAAS,eAAe,KAAK;IACzB,OAAO,aAAa,OAAO,QAAQ;QAC/B,KAAK;IACT,IAAI;AACR;AACO,IAAI,oBAAoB,SAAS,MAAM,EAAE,KAAK;IACjD,IAAI,2BAA2B,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;IACjG,IAAI,sBAAsB,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;IACrE,IAAI,cAAc,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;IAC7D,MAAM,qBAAqB,eAAe,yBAAyB,KAAK;IACxE,OAAO,MAAM,MAAM,CAAE,CAAC,KAAK;QACvB,MAAM,sBAAsB,eAAe,KAAK,KAAK;QACrD,MAAM,iBAAiB,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,uBAAuB,oBAAoB,QAAQ,CAAC;QACtF,IAAI,gBAAgB;YAChB,eAAe,KAAK,GAAG,eAAe,eAAe,KAAK;QAC9D;QACA,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,0BAA0B,MAAM;YAC7D,OAAO;QACX,GAAG;YACC,OAAO;QACX,GAAG;QACH,MAAM,mBAAmB,oBAAoB,SAAS;QACtD,MAAM,aAAa,oBAAoB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,YAAY,UAAU,eAAe,SAAS,OAAO,YAAY,CAAC;QACtH,cAAc,IAAI,IAAI,CAAC;QACvB,OAAO;IACX,GAAI,EAAE;AACV;AACA,MAAM,cAAc;IAChB,MAAM;IACN,SAAQ;IACR,YAAW;IACX,SAAS;QACL,sBAAqB,UAAU;YAC3B,IAAI,OAAO;YACX,MAAM,SAAS;gBACX,SAAS,WAAW,OAAO;gBAC3B,SAAS,WAAW,OAAO;YAC/B;YACA,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,MAAM;YAC7D,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,aAAa,MAAM;YAC7D,MAAM,UAAU,IAAI,CAAC,eAAe;YACpC,MAAM,WAAW,QAAQ,YAAY,CAAC,WAAW,QAAQ;YACzD,IAAI,OAAO,IAAI,CAAC,YAAY,CAAC,WAAW,IAAI;YAC5C,IAAI;YACJ,IAAI,OAAO,SAAS,CAAC,QAAQ,IAAI,KAAK,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI;YAC5E,IAAI,WAAW,MAAM,EAAE;gBACnB,IAAI;gBACJ,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM,CAAE,CAAC,EAAE;gBACnE,OAAO,SAAS,CAAC,UAAU,MAAM,KAAK,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,YAAY;gBACxF,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,OAAO,KAAK,IAAI;YACxC;YACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;gBACrB,IAAI,QAAQ;oBACR,MAAM,SAAS,OAAO,mBAAmB,CAAC;oBAC1C,UAAU,CAAC,MAAM,CAAC,aAAa,GAAG,MAAM,CAAC,aAAa;gBAC1D,OAAO;oBACH,MAAM,CAAC,aAAa,GAAG,QAAQ,aAAa,GAAG,SAAS,CAAC;gBAC7D;gBAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,OAAO,QAAQ,IAAI;YAC7C;YACA,MAAM,QAAQ,SAAS,CAAC,SAAS,IAAI,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,YAAY,CAAC,WAAW,KAAK;YAC3G,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;gBAClB,IAAI;gBACJ,MAAM,CAAC,aAAa,GAAG,SAAS,CAAC,SAAS,IAAI,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,aAAa,GAAG,SAAS,CAAC;gBACjH,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,OAAO,KAAK,IAAI;YAC5D;YACA,OAAO,MAAM,GAAG,IAAI,CAAC,iBAAiB,CAAC;YACvC,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,aAAa,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;gBACtD,IAAI;gBACJ,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;oBACxC,MAAM,CAAC,aAAa,GAAG,QAAQ,eAAe;gBAClD,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;oBAC9C,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,KAAK,IAAI,CAAE,CAAC,EAAE,CAAC,eAAe;gBACpG,OAAO,IAAI,SAAS,CAAC,WAAW,MAAM,KAAK,KAAK,MAAM,YAAY,SAAS,wBAAwB,CAAC,SAAS,MAAM,CAAC,aAAa,GAAG;oBAChI,MAAM,CAAC,aAAa,GAAG,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,EAAE;gBAC3E;YACJ;YACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAC,aAAa,GAAG;gBACzD,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;oBACvC,MAAM,CAAC,aAAa,GAAG,KAAK,eAAe;gBAC/C,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;oBAC1B,IAAI,OAAO,wBAAwB,CAAC,MAAM,MAAM,CAAC,aAAa,GAAG;wBAC7D,MAAM,CAAC,aAAa,GAAG,OAAO,kBAAkB,CAAC,MAAM,CAAC,aAAa,EAAE;oBAC3E;gBACJ;YACJ;YACA,OAAO;QACX;QACA,iCAAgC,KAAK;YACjC,IAAI,IAAI,CAAC,SAAS,EAAE;gBAChB;YACJ;YACA,MAAM,gBAAgB,MAAM,aAAa,IAAI,CAAC;YAC9C,MAAM,QAAQ,cAAc,OAAO,IAAI,cAAc,OAAO,CAAC,EAAE,IAAI,CAAC;YACpE,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa;YAC/C,MAAM,SAAS;gBACX,GAAG,MAAM,KAAK,IAAI,cAAc,KAAK,IAAI,MAAM,KAAK;gBACpD,GAAG,MAAM,KAAK,IAAI,cAAc,KAAK,IAAI,MAAM,KAAK;YACxD;YACA,MAAM,aAAa,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,OAAO,CAAC,OAAO,CAAC,GAAG,WAAW,IAAI,EAAE,OAAO,CAAC,GAAG,WAAW,GAAG,EAAG,CAAC,EAAE;YAC7H,IAAI,CAAC,cAAc,CAAC,WAAW,OAAO,CAAC,cAAc,EAAE;gBACnD,IAAI,CAAC,YAAY,CAAC,WAAW;gBAC7B;YACJ;YACA,IAAI,CAAC,MAAM;YACX,IAAI,WAAW,OAAO,CAAC,aAAa,IAAI,MAAM,IAAI,KAAK,yKAAA,CAAA,UAAa,CAAC,IAAI,EAAE;gBACvE,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG;YAC5C;YACA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,mBAAmB,EAAE;gBACxC,WAAW,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBAClD,MAAM,eAAe;YACzB;QACJ;QACA,sBAAsB,CAAA,UAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,KAAK,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ;QACvF,cAAc,CAAA,UAAW,CAAC;gBACtB,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,GAAG,QAAQ,CAAC;gBACZ,GAAG,QAAQ,CAAC;gBACZ,OAAO,QAAQ,KAAK;gBACpB,UAAU,QAAQ,QAAQ;gBAC1B,MAAM,QAAQ,IAAI;gBAClB,QAAQ,QAAQ,MAAM;gBACtB,SAAS;gBACT,SAAS,QAAQ,OAAO;gBACxB,SAAS,QAAQ,OAAO;YAC5B,CAAC;QACD;YACI,IAAI,CAAC,OAAO,CAAC;gBAAC;aAAe;QACjC;QACA;YACI,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,UAAU;QACnB;IACJ;AACJ;AACA,MAAM,mBAAmB;IACrB,MAAM;IACN,SAAQ;IACR,YAAW;IACX,SAAS;QACL,sBAAqB,UAAU;YAC3B,MAAM,SAAS;gBACX,SAAS,WAAW,OAAO;gBAC3B,SAAS,WAAW,OAAO;gBAC3B,QAAQ,IAAI,CAAC,WAAW;YAC5B;YACA,MAAM,UAAU,IAAI,CAAC,eAAe;YACpC,IAAI,WAAW,QAAQ,YAAY,CAAC,WAAW,QAAQ;YACvD,MAAM,QAAQ,IAAI,CAAC,YAAY,GAAG,YAAY,CAAC,WAAW,KAAK;YAC/D,MAAM,SAAS,WAAW,MAAM;YAChC,MAAM,QAAQ,WAAW,KAAK;YAC9B,IAAI;YACJ,IAAI;YACJ,IAAI,WAAW,MAAM,EAAE;gBACnB,SAAS,IAAI,CAAC,MAAM,CAAC,MAAM,CAAE,CAAA,IAAK,EAAE,IAAI,KAAK,WAAW,MAAM,CAAE,CAAC,EAAE;YACvE;YACA,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC,OAAO,QAAQ,UAAU;YAClE,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;gBACnB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS;oBACpE,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;wBACtB,WAAW,QAAQ,aAAa,GAAG,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,cAAc,CAAC,SAAS,OAAO,KAAK;oBACvG;oBACA,cAAc,OAAO,kBAAkB,CAAC;wBACpC,UAAU;wBACV,OAAO,CAAC,OAAO,KAAK;oBACxB,GAAG;gBACP,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;oBAC9E,cAAc,OAAO,kBAAkB,CAAC;wBACpC,QAAQ,OAAO,MAAM;oBACzB,GAAG;gBACP;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;oBACxB,OAAO,CAAC,GAAG,YAAY,CAAC;oBACxB,OAAO,CAAC,GAAG,YAAY,CAAC;gBAC5B;YACJ;YACA,IAAI,WAAW,MAAM,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc;gBAC9C,OAAO,CAAC,GAAG,OAAO,CAAC,GAAG,KAAK;YAC/B;YACA,OAAO;QACX;QACA,iCAAiC,YAAY,OAAO,CAAC,+BAA+B;QACpF,sBAAsB,YAAY,OAAO,CAAC,oBAAoB;QAC9D,cAAa,OAAO;YAChB,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;gBAC5B,QAAQ,QAAQ,MAAM;gBACtB,OAAO,QAAQ,KAAK;YACxB,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC;YACpC,OAAO,aAAa,IAAI;YACxB,OAAO;QACX;QACA,wBAAwB,YAAY,OAAO,CAAC,sBAAsB;QAClE,QAAQ,YAAY,OAAO,CAAC,MAAM;IACtC;AACJ;AACA,MAAM,kBAAkB;IACpB,MAAM;IACN,SAAQ;IACR;QACI,IAAI,CAAC,YAAY,CAAC,WAAW;QAC7B,IAAI,CAAC,YAAY,CAAC,WAAW,GAAG;IACpC;IACA,SAAS;QACL,sBAAqB,UAAU;YAC3B,MAAM,SAAS;gBACX,SAAS,WAAW,OAAO;gBAC3B,SAAS,WAAW,OAAO;YAC/B;YACA,OAAO,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,SAAS;YAC1C,IAAI,WAAW,WAAW,EAAE;gBACxB,MAAM,OAAO,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,WAAW,WAAW;gBAClE,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE;gBAClB,OAAO,CAAC,GAAG,IAAI,CAAC,EAAE;YACtB;YACA,OAAO;QACX;QACA,iCAAiC,YAAY,OAAO,CAAC,+BAA+B;QACpF,sBAAsB,CAAA,UAAW,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,WAAW;QAC9D,cAAa,OAAO;YAChB,MAAM,mBAAmB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;gBAChC,aAAa,QAAQ,WAAW;YACpC,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC;YACpC,OAAO,iBAAiB,IAAI;YAC5B,OAAO,iBAAiB,MAAM;YAC9B,OAAO,iBAAiB,QAAQ;YAChC,OAAO,iBAAiB,KAAK;YAC7B,OAAO;QACX;QACA;YACI,IAAI,CAAC,OAAO,CAAC;gBAAC;aAAiB;QACnC;QACA,sBAAsB,IAAM,CAAC;gBACzB,eAAe;YACnB,CAAC;QACD,WAAU;IACd;IACA,WAAW;QACP;YACI,MAAM,OAAO,IAAI;YACjB,MAAM,iBAAiB;gBACnB,KAAK,oBAAoB;YAC7B;YACA,KAAK,YAAY,CAAC,WAAW,GAAG,KAAK,QAAQ,CAAC,EAAE,CAAC;gBAC7C,MAAM;gBACN,MAAM;gBACN,KAAK;YACT;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB;IACnB,MAAM;IACN,SAAQ;IACR,YAAW;IACX,SAAS;QACL,sBAAqB,UAAU;YAC3B,IAAI;YACJ,MAAM,SAAS;gBACX,SAAS,WAAW,OAAO;gBAC3B,SAAS,WAAW,OAAO;gBAC3B,QAAQ,IAAI,CAAC,OAAO;YACxB;YACA,IAAI,WAAW,QAAQ,EAAE;gBACrB,IAAI,WAAW,MAAM,EAAE;oBACnB,SAAS,IAAI,CAAC,eAAe,CAAC,WAAW,MAAM;gBACnD,OAAO;oBACH,SAAS,IAAI,CAAC,MAAM,CAAC,EAAE;gBAC3B;gBACA,MAAM,WAAW,OAAO,cAAc,CAAC,WAAW,QAAQ,CAAC,CAAC,EAAE;gBAC9D,MAAM,EACF,GAAG,CAAC,EACJ,GAAG,CAAC,EACP,GAAG,SAAS,mBAAmB,CAAC,WAAW,QAAQ;gBACpD,OAAO,CAAC,GAAG;gBACX,OAAO,CAAC,GAAG;YACf;YACA,OAAO;QACX;QACA,sBAAsB,CAAA,UAAW,QAAQ,QAAQ;QACjD,iCAAiC,YAAY,OAAO,CAAC,+BAA+B;QACpF,cAAa,OAAO;YAChB,MAAM,kBAAkB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;gBAC/B,UAAU,QAAQ,QAAQ;YAC9B,GAAG,YAAY,OAAO,CAAC,YAAY,CAAC;YACpC,OAAO,gBAAgB,IAAI;YAC3B,OAAO;QACX;QACA,QAAQ,YAAY,OAAO,CAAC,MAAM;QAClC,wBAAwB,YAAY,OAAO,CAAC,sBAAsB;IACtE;AACJ;AACA,MAAM,aAAa;IACf,MAAM;IACN;QACI,IAAI,CAAC,YAAY,GAAG;YAChB,OAAO,EAAE;YACT,qBAAqB;YACrB,SAAS,IAAI,2JAAA,CAAA,UAAO,CAAC;gBACjB,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;gBACvD,cAAc,IAAI,CAAC,aAAa;gBAChC,YAAY,IAAI,CAAC,OAAO;gBACxB,QAAQ,IAAI;YAChB;YACA;gBACI,IAAI,CAAC,OAAO,CAAC,UAAU,GAAG;gBAC1B,IAAI,CAAC,OAAO,CAAC,IAAI;YACrB;YACA;gBACI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA,IAAK,EAAE,MAAM,CAAC,KAAK;gBACvC,IAAI,CAAC,KAAK,GAAG,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,kBAAkB,CAAC,IAAI,CAAC,mBAAmB;IACzE;IACA;QACI,IAAI,CAAC,iBAAiB,CAAC,UAAU,GAAG,OAAO;QAC3C,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,eAAe;QAChC,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAC3B,IAAI,CAAC,YAAY,CAAC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,OAAO;IAClE;IACA,WAAW;QACP;YACI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;gBAC7C,OAAO,GAAG,IAAI,CAAC,gBAAgB,CAAC,YAAY,CAAC;YACjD,GAAG,GAAG,CAAC,IAAI,CAAC,oBAAoB,IAAI,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,eAAe,UAAU;YACzF,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,eAAe,gBAAiB,CAAA;gBAC5C,IAAI,IAAI,CAAC,SAAS,EAAE;oBAChB;gBACJ;gBACA,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK,GAAG;oBAChE,IAAI,CAAC,YAAY,CAAC,WAAW;gBACjC;YACJ;YACA,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,eAAe,uBAAwB,CAAA;gBACnD,IAAI,CAAC,YAAY,CAAC,mBAAmB,GAAG;gBACxC,IAAI,CAAC,+BAA+B,CAAC;YACzC;YACA,IAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,gBAAgB,IAAI,CAAC,+BAA+B,CAAC,IAAI,CAAC,IAAI;QAC5F;QACA;YACI,IAAI,CAAC,iBAAiB,CAAC,KAAK;YAC5B,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB;QACnF;QACA;YACI,IAAI,CAAC,YAAY,CAAC,WAAW;QACjC;IACJ;IACA,SAAS;QACL;YACI,IAAI,CAAC,YAAY,CAAC,UAAU;YAC5B,MAAM,QAAQ,IAAI,CAAC,UAAU,CAAC,eAAe;YAC7C,IAAI,CAAC,CAAC,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,MAAM,GAAG;gBACvD;YACJ;YACA,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,kBAAkB,IAAI,EAAE,OAAO,IAAI,CAAC,UAAU,CAAC,6BAA6B,IAAI,CAAC,UAAU,CAAC,uBAAuB,OAAO,IAAI,CAAC,YAAY;QACzK;QACA;YACI,MAAM,iBAAiB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC;YAClD,eAAe,eAAe,GAAG,eAAe,gBAAgB,GAAG,KAAK;YACxE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,CAAC;QACrC;QACA,sBAAsB,IAAM,CAAC,CAAC,CAAC;QAC/B,cAAc,IAAM,CAAC,CAAC,CAAC;QACvB,sBAAsB,IAAM,CAAC,CAAC,CAAC;IACnC;IACA,WAAU,WAAW;QACjB,YAAY,SAAS,CAAC;YAClB,MAAM;YACN;gBACI,IAAI,CAAC,cAAc,CAAC;oBAAC;iBAAc;YACvC;YACA,gBAAgB;YAChB,QAAQ;QACZ;QACA,YAAY,SAAS,CAAC;YAClB,MAAM;YACN;gBACI,IAAI,CAAC,cAAc,CAAC;oBAAC;iBAAc;YACvC;YACA,gBAAgB;YAChB,QAAQ;QACZ;QACA,YAAY,SAAS,CAAC;YAClB,MAAM;YACN;gBACI,IAAI,CAAC,iBAAiB;gBACtB,IAAI,CAAC,4BAA4B;gBACjC,IAAI,CAAC,sBAAsB;YAC/B;YACA,kBAAkB;YAClB,gBAAgB;QACpB;IACJ;IACA,YAAY;QAAC;KAAgC;AACjD;AACO,MAAM,UAAU;IACnB,MAAM;IACN,OAAO;IACP,YAAY;IACZ,WAAW;IACX,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4574, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/series_family.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/series_family.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isNumeric,\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    each as _each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    sign\r\n} from \"../../core/utils/math\";\r\nimport {\r\n    noop as _noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    map as _map,\r\n    normalizeEnum as _normalizeEnum\r\n} from \"./utils\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nconst {\r\n    round: round,\r\n    abs: abs,\r\n    pow: pow,\r\n    sqrt: sqrt\r\n} = Math;\r\nconst _min = Math.min;\r\nconst DEFAULT_BAR_GROUP_PADDING = .3;\r\n\r\nfunction validateBarPadding(barPadding) {\r\n    return barPadding < 0 || barPadding > 1 ? void 0 : barPadding\r\n}\r\n\r\nfunction validateBarGroupPadding(barGroupPadding) {\r\n    return barGroupPadding < 0 || barGroupPadding > 1 ? .3 : barGroupPadding\r\n}\r\n\r\nfunction isStackExist(series, arg) {\r\n    return series.some((function(s) {\r\n        return !s.getOptions().ignoreEmptyPoints || s.getPointsByArg(arg, true).some((function(point) {\r\n            return point.hasValue()\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction correctStackCoordinates(series, currentStacks, arg, stack, parameters, barsArea, seriesStackIndexCallback) {\r\n    series.forEach((function(series) {\r\n        const stackIndex = seriesStackIndexCallback(currentStacks.indexOf(stack), currentStacks.length);\r\n        const points = series.getPointsByArg(arg, true);\r\n        const barPadding = validateBarPadding(series.getOptions().barPadding);\r\n        const barWidth = series.getOptions().barWidth;\r\n        let offset = getOffset(stackIndex, parameters);\r\n        let width = parameters.width;\r\n        let extraParameters;\r\n        if (-1 === stackIndex) {\r\n            return\r\n        }\r\n        if (isDefined(barPadding) || isDefined(barWidth)) {\r\n            extraParameters = calculateParams(barsArea, currentStacks.length, 1 - barPadding, barWidth);\r\n            width = extraParameters.width;\r\n            if (!series.getBarOverlapGroup()) {\r\n                offset = getOffset(stackIndex, extraParameters)\r\n            }\r\n        }\r\n        correctPointCoordinates(points, width, offset)\r\n    }))\r\n}\r\n\r\nfunction getStackName(series) {\r\n    return series.getStackName() || series.getBarOverlapGroup()\r\n}\r\n\r\nfunction adjustBarSeriesDimensionsCore(series, options, seriesStackIndexCallback) {\r\n    var _series$, _series$2;\r\n    const commonStacks = [];\r\n    const allArguments = [];\r\n    const seriesInStacks = {};\r\n    const barGroupWidth = options.barGroupWidth;\r\n    const argumentAxis = null === (_series$ = series[0]) || void 0 === _series$ ? void 0 : _series$.getArgumentAxis();\r\n    let interval;\r\n    if (null !== (_series$2 = series[0]) && void 0 !== _series$2 && _series$2.useAggregation()) {\r\n        var _series$3;\r\n        const isDateArgAxis = \"datetime\" === (null === (_series$3 = series[0]) || void 0 === _series$3 ? void 0 : _series$3.argumentType);\r\n        let tickInterval = argumentAxis.getTickInterval();\r\n        let aggregationInterval = argumentAxis.getAggregationInterval();\r\n        tickInterval = isDateArgAxis ? dateUtils.dateToMilliseconds(tickInterval) : tickInterval;\r\n        aggregationInterval = isDateArgAxis ? dateUtils.dateToMilliseconds(aggregationInterval) : aggregationInterval;\r\n        interval = aggregationInterval < tickInterval ? aggregationInterval : tickInterval\r\n    }\r\n    interval = null === argumentAxis || void 0 === argumentAxis ? void 0 : argumentAxis.getTranslator().getInterval(interval);\r\n    const barsArea = barGroupWidth ? interval > barGroupWidth ? barGroupWidth : interval : interval * (1 - validateBarGroupPadding(options.barGroupPadding));\r\n    series.forEach((function(s, i) {\r\n        const stackName = getStackName(s) || i.toString();\r\n        let argument;\r\n        for (argument in s.pointsByArgument) {\r\n            if (-1 === allArguments.indexOf(argument.valueOf())) {\r\n                allArguments.push(argument.valueOf())\r\n            }\r\n        }\r\n        if (-1 === commonStacks.indexOf(stackName)) {\r\n            commonStacks.push(stackName);\r\n            seriesInStacks[stackName] = []\r\n        }\r\n        seriesInStacks[stackName].push(s)\r\n    }));\r\n    allArguments.forEach((function(arg) {\r\n        const currentStacks = commonStacks.reduce(((stacks, stack) => {\r\n            if (isStackExist(seriesInStacks[stack], arg)) {\r\n                stacks.push(stack)\r\n            }\r\n            return stacks\r\n        }), []);\r\n        const parameters = calculateParams(barsArea, currentStacks.length);\r\n        commonStacks.forEach((stack => {\r\n            correctStackCoordinates(seriesInStacks[stack], currentStacks, arg, stack, parameters, barsArea, seriesStackIndexCallback)\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction calculateParams(barsArea, count, percentWidth, fixedBarWidth) {\r\n    let spacing;\r\n    let width;\r\n    if (fixedBarWidth) {\r\n        width = _min(fixedBarWidth, barsArea / count);\r\n        spacing = count > 1 ? round((barsArea - round(width) * count) / (count - 1)) : 0\r\n    } else if (isDefined(percentWidth)) {\r\n        width = barsArea * percentWidth / count;\r\n        spacing = count > 1 ? round((barsArea - barsArea * percentWidth) / (count - 1)) : 0\r\n    } else {\r\n        spacing = round(barsArea / count * .2);\r\n        width = (barsArea - spacing * (count - 1)) / count\r\n    }\r\n    return {\r\n        width: width > 1 ? round(width) : 1,\r\n        spacing: spacing,\r\n        middleIndex: count / 2,\r\n        rawWidth: width\r\n    }\r\n}\r\n\r\nfunction getOffset(stackIndex, parameters) {\r\n    const width = parameters.rawWidth < 1 ? parameters.rawWidth : parameters.width;\r\n    return (stackIndex - parameters.middleIndex + .5) * width - (parameters.middleIndex - stackIndex - .5) * parameters.spacing\r\n}\r\n\r\nfunction correctPointCoordinates(points, width, offset) {\r\n    _each(points, (function(_, point) {\r\n        point.correctCoordinates({\r\n            width: width,\r\n            offset: offset\r\n        })\r\n    }))\r\n}\r\n\r\nfunction getValueType(value) {\r\n    return value >= 0 ? \"positive\" : \"negative\"\r\n}\r\n\r\nfunction getVisibleSeries(that) {\r\n    return that.series.filter((function(s) {\r\n        return s.isVisible()\r\n    }))\r\n}\r\n\r\nfunction getAbsStackSumByArg(stackKeepers, stackName, argument) {\r\n    const positiveStackValue = (stackKeepers.positive[stackName] || {})[argument] || 0;\r\n    const negativeStackValue = -(stackKeepers.negative[stackName] || {})[argument] || 0;\r\n    return positiveStackValue + negativeStackValue\r\n}\r\n\r\nfunction getStackSumByArg(stackKeepers, stackName, argument) {\r\n    const positiveStackValue = (stackKeepers.positive[stackName] || {})[argument] || 0;\r\n    const negativeStackValue = (stackKeepers.negative[stackName] || {})[argument] || 0;\r\n    return positiveStackValue + negativeStackValue\r\n}\r\n\r\nfunction getSeriesStackIndexCallback(inverted) {\r\n    if (!inverted) {\r\n        return function(index) {\r\n            return index\r\n        }\r\n    } else {\r\n        return function(index, stackCount) {\r\n            return stackCount - index - 1\r\n        }\r\n    }\r\n}\r\n\r\nfunction isInverted(series) {\r\n    return series[0] && series[0].getArgumentAxis().getTranslator().isInverted()\r\n}\r\n\r\nfunction adjustBarSeriesDimensions() {\r\n    const series = getVisibleSeries(this);\r\n    adjustBarSeriesDimensionsCore(series, this._options, getSeriesStackIndexCallback(isInverted(series)))\r\n}\r\n\r\nfunction getFirstValueSign(series) {\r\n    const points = series.getPoints();\r\n    let value;\r\n    for (let i = 0; i < points.length; i++) {\r\n        const point = points[i];\r\n        value = point.initialValue && point.initialValue.valueOf();\r\n        if (abs(value) > 0) {\r\n            break\r\n        }\r\n    }\r\n    return sign(value)\r\n}\r\n\r\nfunction adjustStackedSeriesValues() {\r\n    const negativesAsZeroes = this._options.negativesAsZeroes;\r\n    const series = getVisibleSeries(this);\r\n    const stackKeepers = {\r\n        positive: {},\r\n        negative: {}\r\n    };\r\n    const holesStack = {\r\n        left: {},\r\n        right: {}\r\n    };\r\n    const lastSeriesInPositiveStack = {};\r\n    const lastSeriesInNegativeStack = {};\r\n    series.forEach((function(singleSeries) {\r\n        const stackName = getStackName(singleSeries);\r\n        let hole = false;\r\n        const stack = getFirstValueSign(singleSeries) < 0 ? lastSeriesInNegativeStack : lastSeriesInPositiveStack;\r\n        singleSeries._prevSeries = stack[stackName];\r\n        stack[stackName] = singleSeries;\r\n        singleSeries.holes = extend(true, {}, holesStack);\r\n        singleSeries.getPoints().forEach((function(point, index, points) {\r\n            let value = point.initialValue && point.initialValue.valueOf();\r\n            let argument = point.argument.valueOf();\r\n            let stacks = value >= 0 ? stackKeepers.positive : stackKeepers.negative;\r\n            const isNotBarSeries = \"bar\" !== singleSeries.type;\r\n            if (negativesAsZeroes && value < 0) {\r\n                stacks = stackKeepers.positive;\r\n                value = 0;\r\n                point.resetValue()\r\n            }\r\n            stacks[stackName] = stacks[stackName] || {};\r\n            const currentStack = stacks[stackName];\r\n            if (currentStack[argument]) {\r\n                if (isNotBarSeries) {\r\n                    point.correctValue(currentStack[argument])\r\n                }\r\n                currentStack[argument] += value\r\n            } else {\r\n                currentStack[argument] = value;\r\n                if (isNotBarSeries) {\r\n                    point.resetCorrection()\r\n                }\r\n            }\r\n            if (!point.hasValue()) {\r\n                const prevPoint = points[index - 1];\r\n                if (!hole && prevPoint && prevPoint.hasValue()) {\r\n                    argument = prevPoint.argument.valueOf();\r\n                    prevPoint._skipSetRightHole = true;\r\n                    holesStack.right[argument] = (holesStack.right[argument] || 0) + (prevPoint.value.valueOf() - (isFinite(prevPoint.minValue) ? prevPoint.minValue.valueOf() : 0))\r\n                }\r\n                hole = true\r\n            } else if (hole) {\r\n                hole = false;\r\n                holesStack.left[argument] = (holesStack.left[argument] || 0) + (point.value.valueOf() - (isFinite(point.minValue) ? point.minValue.valueOf() : 0));\r\n                point._skipSetLeftHole = true\r\n            }\r\n        }))\r\n    }));\r\n    series.forEach((function(singleSeries) {\r\n        const holes = singleSeries.holes;\r\n        singleSeries.getPoints().forEach((function(point) {\r\n            const argument = point.argument.valueOf();\r\n            point.resetHoles();\r\n            !point._skipSetLeftHole && point.setHole(holes.left[argument] || holesStack.left[argument] && 0, \"left\");\r\n            !point._skipSetRightHole && point.setHole(holes.right[argument] || holesStack.right[argument] && 0, \"right\");\r\n            point._skipSetLeftHole = null;\r\n            point._skipSetRightHole = null\r\n        }))\r\n    }));\r\n    this._stackKeepers = stackKeepers;\r\n    series.forEach((function(singleSeries) {\r\n        singleSeries.getPoints().forEach((function(point) {\r\n            const argument = point.argument.valueOf();\r\n            const stackName = getStackName(singleSeries);\r\n            const absTotal = getAbsStackSumByArg(stackKeepers, stackName, argument);\r\n            const total = getStackSumByArg(stackKeepers, stackName, argument);\r\n            point.setPercentValue(absTotal, total, holesStack.left[argument], holesStack.right[argument])\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction updateStackedSeriesValues() {\r\n    const that = this;\r\n    const series = getVisibleSeries(that);\r\n    const stack = that._stackKeepers;\r\n    const stackKeepers = {\r\n        positive: {},\r\n        negative: {}\r\n    };\r\n    _each(series, (function(_, singleSeries) {\r\n        const minBarSize = singleSeries.getOptions().minBarSize;\r\n        const valueAxisTranslator = singleSeries.getValueAxis().getTranslator();\r\n        const minShownBusinessValue = minBarSize && valueAxisTranslator.getMinBarSize(minBarSize);\r\n        const stackName = singleSeries.getStackName();\r\n        _each(singleSeries.getPoints(), (function(index, point) {\r\n            if (!point.hasValue()) {\r\n                return\r\n            }\r\n            let value = point.initialValue && point.initialValue.valueOf();\r\n            const argument = point.argument.valueOf();\r\n            if (that.fullStacked) {\r\n                value = value / getAbsStackSumByArg(stack, stackName, argument) || 0\r\n            }\r\n            const updateValue = valueAxisTranslator.checkMinBarSize(value, minShownBusinessValue, point.value);\r\n            const valueType = getValueType(updateValue);\r\n            const currentStack = stackKeepers[valueType][stackName] = stackKeepers[valueType][stackName] || {};\r\n            if (currentStack[argument]) {\r\n                point.minValue = currentStack[argument];\r\n                currentStack[argument] += updateValue\r\n            } else {\r\n                currentStack[argument] = updateValue\r\n            }\r\n            point.value = currentStack[argument]\r\n        }))\r\n    }));\r\n    if (that.fullStacked) {\r\n        updateFullStackedSeriesValues(series, stackKeepers)\r\n    }\r\n}\r\n\r\nfunction updateFullStackedSeriesValues(series, stackKeepers) {\r\n    _each(series, (function(_, singleSeries) {\r\n        const stackName = singleSeries.getStackName ? singleSeries.getStackName() : \"default\";\r\n        _each(singleSeries.getPoints(), (function(index, point) {\r\n            const stackSum = getAbsStackSumByArg(stackKeepers, stackName, point.argument.valueOf());\r\n            if (0 !== stackSum) {\r\n                point.value = point.value / stackSum;\r\n                if (isNumeric(point.minValue)) {\r\n                    point.minValue = point.minValue / stackSum\r\n                }\r\n            }\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction updateRangeSeriesValues() {\r\n    const series = getVisibleSeries(this);\r\n    _each(series, (function(_, singleSeries) {\r\n        const minBarSize = singleSeries.getOptions().minBarSize;\r\n        const valueAxisTranslator = singleSeries.getValueAxis().getTranslator();\r\n        const minShownBusinessValue = minBarSize && valueAxisTranslator.getMinBarSize(minBarSize);\r\n        if (minShownBusinessValue) {\r\n            _each(singleSeries.getPoints(), (function(_, point) {\r\n                if (!point.hasValue()) {\r\n                    return\r\n                }\r\n                if (point.value.valueOf() - point.minValue.valueOf() < minShownBusinessValue) {\r\n                    point.value = valueAxisTranslator.toValue(point.value.valueOf() + minShownBusinessValue / 2);\r\n                    point.minValue = valueAxisTranslator.toValue(point.minValue.valueOf() - minShownBusinessValue / 2)\r\n                }\r\n            }))\r\n        }\r\n    }))\r\n}\r\n\r\nfunction updateBarSeriesValues() {\r\n    _each(this.series, (function(_, singleSeries) {\r\n        const minBarSize = singleSeries.getOptions().minBarSize;\r\n        const valueAxisTranslator = singleSeries.getValueAxis().getTranslator();\r\n        const minShownBusinessValue = minBarSize && valueAxisTranslator.getMinBarSize(minBarSize);\r\n        if (minShownBusinessValue) {\r\n            _each(singleSeries.getPoints(), (function(index, point) {\r\n                if (point.hasValue()) {\r\n                    point.value = valueAxisTranslator.checkMinBarSize(point.initialValue, minShownBusinessValue)\r\n                }\r\n            }))\r\n        }\r\n    }))\r\n}\r\n\r\nfunction adjustCandlestickSeriesDimensions() {\r\n    const series = getVisibleSeries(this);\r\n    adjustBarSeriesDimensionsCore(series, {\r\n        barGroupPadding: .3\r\n    }, getSeriesStackIndexCallback(isInverted(series)))\r\n}\r\n\r\nfunction adjustBubbleSeriesDimensions() {\r\n    const series = getVisibleSeries(this);\r\n    if (!series.length) {\r\n        return\r\n    }\r\n    const options = this._options;\r\n    const visibleAreaX = series[0].getArgumentAxis().getVisibleArea();\r\n    const visibleAreaY = series[0].getValueAxis().getVisibleArea();\r\n    const min = _min(visibleAreaX[1] - visibleAreaX[0], visibleAreaY[1] - visibleAreaY[0]);\r\n    const minBubbleArea = pow(options.minBubbleSize, 2);\r\n    const maxBubbleArea = pow(min * options.maxBubbleSize, 2);\r\n    const equalBubbleSize = (min * options.maxBubbleSize + options.minBubbleSize) / 2;\r\n    let minPointSize = 1 / 0;\r\n    let maxPointSize = -1 / 0;\r\n    let pointSize;\r\n    let bubbleArea;\r\n    let sizeProportion;\r\n    _each(series, (function(_, seriesItem) {\r\n        _each(seriesItem.getPoints(), (function(_, point) {\r\n            maxPointSize = maxPointSize > point.size ? maxPointSize : point.size;\r\n            minPointSize = minPointSize < point.size ? minPointSize : point.size\r\n        }))\r\n    }));\r\n    const sizeDispersion = maxPointSize - minPointSize;\r\n    const areaDispersion = abs(maxBubbleArea - minBubbleArea);\r\n    _each(series, (function(_, seriesItem) {\r\n        _each(seriesItem.getPoints(), (function(_, point) {\r\n            if (maxPointSize === minPointSize) {\r\n                pointSize = round(equalBubbleSize)\r\n            } else {\r\n                sizeProportion = abs(point.size - minPointSize) / sizeDispersion;\r\n                bubbleArea = areaDispersion * sizeProportion + minBubbleArea;\r\n                pointSize = round(sqrt(bubbleArea))\r\n            }\r\n            point.correctCoordinates(pointSize)\r\n        }))\r\n    }))\r\n}\r\nexport function SeriesFamily(options) {\r\n    const that = this;\r\n    that.type = _normalizeEnum(options.type);\r\n    that.pane = options.pane;\r\n    that.series = [];\r\n    that.updateOptions(options);\r\n    switch (that.type) {\r\n        case \"bar\":\r\n            that.adjustSeriesDimensions = adjustBarSeriesDimensions;\r\n            that.updateSeriesValues = updateBarSeriesValues;\r\n            that.adjustSeriesValues = adjustStackedSeriesValues;\r\n            break;\r\n        case \"rangebar\":\r\n            that.adjustSeriesDimensions = adjustBarSeriesDimensions;\r\n            that.updateSeriesValues = updateRangeSeriesValues;\r\n            break;\r\n        case \"fullstackedbar\":\r\n            that.fullStacked = true;\r\n            that.adjustSeriesDimensions = adjustBarSeriesDimensions;\r\n            that.adjustSeriesValues = adjustStackedSeriesValues;\r\n            that.updateSeriesValues = updateStackedSeriesValues;\r\n            break;\r\n        case \"stackedbar\":\r\n            that.adjustSeriesDimensions = adjustBarSeriesDimensions;\r\n            that.adjustSeriesValues = adjustStackedSeriesValues;\r\n            that.updateSeriesValues = updateStackedSeriesValues;\r\n            break;\r\n        case \"fullstackedarea\":\r\n        case \"fullstackedline\":\r\n        case \"fullstackedspline\":\r\n        case \"fullstackedsplinearea\":\r\n            that.fullStacked = true;\r\n            that.adjustSeriesValues = adjustStackedSeriesValues;\r\n            break;\r\n        case \"stackedarea\":\r\n        case \"stackedsplinearea\":\r\n        case \"stackedline\":\r\n        case \"stackedspline\":\r\n            that.adjustSeriesValues = adjustStackedSeriesValues;\r\n            break;\r\n        case \"candlestick\":\r\n        case \"stock\":\r\n            that.adjustSeriesDimensions = adjustCandlestickSeriesDimensions;\r\n            break;\r\n        case \"bubble\":\r\n            that.adjustSeriesDimensions = adjustBubbleSeriesDimensions\r\n    }\r\n}\r\nSeriesFamily.prototype = {\r\n    constructor: SeriesFamily,\r\n    adjustSeriesDimensions: _noop,\r\n    adjustSeriesValues: _noop,\r\n    updateSeriesValues: _noop,\r\n    updateOptions: function(options) {\r\n        this._options = options\r\n    },\r\n    dispose: function() {\r\n        this.series = null\r\n    },\r\n    add: function(series) {\r\n        const type = this.type;\r\n        this.series = _map(series, (singleSeries => singleSeries.type === type ? singleSeries : null))\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAIA;;;;;;;;AACA,MAAM,EACF,OAAO,KAAK,EACZ,KAAK,GAAG,EACR,KAAK,GAAG,EACR,MAAM,IAAI,EACb,GAAG;AACJ,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,4BAA4B;AAElC,SAAS,mBAAmB,UAAU;IAClC,OAAO,aAAa,KAAK,aAAa,IAAI,KAAK,IAAI;AACvD;AAEA,SAAS,wBAAwB,eAAe;IAC5C,OAAO,kBAAkB,KAAK,kBAAkB,IAAI,KAAK;AAC7D;AAEA,SAAS,aAAa,MAAM,EAAE,GAAG;IAC7B,OAAO,OAAO,IAAI,CAAE,SAAS,CAAC;QAC1B,OAAO,CAAC,EAAE,UAAU,GAAG,iBAAiB,IAAI,EAAE,cAAc,CAAC,KAAK,MAAM,IAAI,CAAE,SAAS,KAAK;YACxF,OAAO,MAAM,QAAQ;QACzB;IACJ;AACJ;AAEA,SAAS,wBAAwB,MAAM,EAAE,aAAa,EAAE,GAAG,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,EAAE,wBAAwB;IAC9G,OAAO,OAAO,CAAE,SAAS,MAAM;QAC3B,MAAM,aAAa,yBAAyB,cAAc,OAAO,CAAC,QAAQ,cAAc,MAAM;QAC9F,MAAM,SAAS,OAAO,cAAc,CAAC,KAAK;QAC1C,MAAM,aAAa,mBAAmB,OAAO,UAAU,GAAG,UAAU;QACpE,MAAM,WAAW,OAAO,UAAU,GAAG,QAAQ;QAC7C,IAAI,SAAS,UAAU,YAAY;QACnC,IAAI,QAAQ,WAAW,KAAK;QAC5B,IAAI;QACJ,IAAI,CAAC,MAAM,YAAY;YACnB;QACJ;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YAC9C,kBAAkB,gBAAgB,UAAU,cAAc,MAAM,EAAE,IAAI,YAAY;YAClF,QAAQ,gBAAgB,KAAK;YAC7B,IAAI,CAAC,OAAO,kBAAkB,IAAI;gBAC9B,SAAS,UAAU,YAAY;YACnC;QACJ;QACA,wBAAwB,QAAQ,OAAO;IAC3C;AACJ;AAEA,SAAS,aAAa,MAAM;IACxB,OAAO,OAAO,YAAY,MAAM,OAAO,kBAAkB;AAC7D;AAEA,SAAS,8BAA8B,MAAM,EAAE,OAAO,EAAE,wBAAwB;IAC5E,IAAI,UAAU;IACd,MAAM,eAAe,EAAE;IACvB,MAAM,eAAe,EAAE;IACvB,MAAM,iBAAiB,CAAC;IACxB,MAAM,gBAAgB,QAAQ,aAAa;IAC3C,MAAM,eAAe,SAAS,CAAC,WAAW,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,eAAe;IAC/G,IAAI;IACJ,IAAI,SAAS,CAAC,YAAY,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,aAAa,UAAU,cAAc,IAAI;QACxF,IAAI;QACJ,MAAM,gBAAgB,eAAe,CAAC,SAAS,CAAC,YAAY,MAAM,CAAC,EAAE,KAAK,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,YAAY;QAChI,IAAI,eAAe,aAAa,eAAe;QAC/C,IAAI,sBAAsB,aAAa,sBAAsB;QAC7D,eAAe,gBAAgB,0JAAA,CAAA,UAAS,CAAC,kBAAkB,CAAC,gBAAgB;QAC5E,sBAAsB,gBAAgB,0JAAA,CAAA,UAAS,CAAC,kBAAkB,CAAC,uBAAuB;QAC1F,WAAW,sBAAsB,eAAe,sBAAsB;IAC1E;IACA,WAAW,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,aAAa,GAAG,WAAW,CAAC;IAChH,MAAM,WAAW,gBAAgB,WAAW,gBAAgB,gBAAgB,WAAW,WAAW,CAAC,IAAI,wBAAwB,QAAQ,eAAe,CAAC;IACvJ,OAAO,OAAO,CAAE,SAAS,CAAC,EAAE,CAAC;QACzB,MAAM,YAAY,aAAa,MAAM,EAAE,QAAQ;QAC/C,IAAI;QACJ,IAAK,YAAY,EAAE,gBAAgB,CAAE;YACjC,IAAI,CAAC,MAAM,aAAa,OAAO,CAAC,SAAS,OAAO,KAAK;gBACjD,aAAa,IAAI,CAAC,SAAS,OAAO;YACtC;QACJ;QACA,IAAI,CAAC,MAAM,aAAa,OAAO,CAAC,YAAY;YACxC,aAAa,IAAI,CAAC;YAClB,cAAc,CAAC,UAAU,GAAG,EAAE;QAClC;QACA,cAAc,CAAC,UAAU,CAAC,IAAI,CAAC;IACnC;IACA,aAAa,OAAO,CAAE,SAAS,GAAG;QAC9B,MAAM,gBAAgB,aAAa,MAAM,CAAE,CAAC,QAAQ;YAChD,IAAI,aAAa,cAAc,CAAC,MAAM,EAAE,MAAM;gBAC1C,OAAO,IAAI,CAAC;YAChB;YACA,OAAO;QACX,GAAI,EAAE;QACN,MAAM,aAAa,gBAAgB,UAAU,cAAc,MAAM;QACjE,aAAa,OAAO,CAAE,CAAA;YAClB,wBAAwB,cAAc,CAAC,MAAM,EAAE,eAAe,KAAK,OAAO,YAAY,UAAU;QACpG;IACJ;AACJ;AAEA,SAAS,gBAAgB,QAAQ,EAAE,KAAK,EAAE,YAAY,EAAE,aAAa;IACjE,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe;QACf,QAAQ,KAAK,eAAe,WAAW;QACvC,UAAU,QAAQ,IAAI,MAAM,CAAC,WAAW,MAAM,SAAS,KAAK,IAAI,CAAC,QAAQ,CAAC,KAAK;IACnF,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;QAChC,QAAQ,WAAW,eAAe;QAClC,UAAU,QAAQ,IAAI,MAAM,CAAC,WAAW,WAAW,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK;IACtF,OAAO;QACH,UAAU,MAAM,WAAW,QAAQ;QACnC,QAAQ,CAAC,WAAW,UAAU,CAAC,QAAQ,CAAC,CAAC,IAAI;IACjD;IACA,OAAO;QACH,OAAO,QAAQ,IAAI,MAAM,SAAS;QAClC,SAAS;QACT,aAAa,QAAQ;QACrB,UAAU;IACd;AACJ;AAEA,SAAS,UAAU,UAAU,EAAE,UAAU;IACrC,MAAM,QAAQ,WAAW,QAAQ,GAAG,IAAI,WAAW,QAAQ,GAAG,WAAW,KAAK;IAC9E,OAAO,CAAC,aAAa,WAAW,WAAW,GAAG,EAAE,IAAI,QAAQ,CAAC,WAAW,WAAW,GAAG,aAAa,EAAE,IAAI,WAAW,OAAO;AAC/H;AAEA,SAAS,wBAAwB,MAAM,EAAE,KAAK,EAAE,MAAM;IAClD,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,KAAK;QAC5B,MAAM,kBAAkB,CAAC;YACrB,OAAO;YACP,QAAQ;QACZ;IACJ;AACJ;AAEA,SAAS,aAAa,KAAK;IACvB,OAAO,SAAS,IAAI,aAAa;AACrC;AAEA,SAAS,iBAAiB,IAAI;IAC1B,OAAO,KAAK,MAAM,CAAC,MAAM,CAAE,SAAS,CAAC;QACjC,OAAO,EAAE,SAAS;IACtB;AACJ;AAEA,SAAS,oBAAoB,YAAY,EAAE,SAAS,EAAE,QAAQ;IAC1D,MAAM,qBAAqB,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI;IACjF,MAAM,qBAAqB,CAAC,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI;IAClF,OAAO,qBAAqB;AAChC;AAEA,SAAS,iBAAiB,YAAY,EAAE,SAAS,EAAE,QAAQ;IACvD,MAAM,qBAAqB,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI;IACjF,MAAM,qBAAqB,CAAC,aAAa,QAAQ,CAAC,UAAU,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,IAAI;IACjF,OAAO,qBAAqB;AAChC;AAEA,SAAS,4BAA4B,QAAQ;IACzC,IAAI,CAAC,UAAU;QACX,OAAO,SAAS,KAAK;YACjB,OAAO;QACX;IACJ,OAAO;QACH,OAAO,SAAS,KAAK,EAAE,UAAU;YAC7B,OAAO,aAAa,QAAQ;QAChC;IACJ;AACJ;AAEA,SAAS,WAAW,MAAM;IACtB,OAAO,MAAM,CAAC,EAAE,IAAI,MAAM,CAAC,EAAE,CAAC,eAAe,GAAG,aAAa,GAAG,UAAU;AAC9E;AAEA,SAAS;IACL,MAAM,SAAS,iBAAiB,IAAI;IACpC,8BAA8B,QAAQ,IAAI,CAAC,QAAQ,EAAE,4BAA4B,WAAW;AAChG;AAEA,SAAS,kBAAkB,MAAM;IAC7B,MAAM,SAAS,OAAO,SAAS;IAC/B,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QACpC,MAAM,QAAQ,MAAM,CAAC,EAAE;QACvB,QAAQ,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,OAAO;QACxD,IAAI,IAAI,SAAS,GAAG;YAChB;QACJ;IACJ;IACA,OAAO,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE;AAChB;AAEA,SAAS;IACL,MAAM,oBAAoB,IAAI,CAAC,QAAQ,CAAC,iBAAiB;IACzD,MAAM,SAAS,iBAAiB,IAAI;IACpC,MAAM,eAAe;QACjB,UAAU,CAAC;QACX,UAAU,CAAC;IACf;IACA,MAAM,aAAa;QACf,MAAM,CAAC;QACP,OAAO,CAAC;IACZ;IACA,MAAM,4BAA4B,CAAC;IACnC,MAAM,4BAA4B,CAAC;IACnC,OAAO,OAAO,CAAE,SAAS,YAAY;QACjC,MAAM,YAAY,aAAa;QAC/B,IAAI,OAAO;QACX,MAAM,QAAQ,kBAAkB,gBAAgB,IAAI,4BAA4B;QAChF,aAAa,WAAW,GAAG,KAAK,CAAC,UAAU;QAC3C,KAAK,CAAC,UAAU,GAAG;QACnB,aAAa,KAAK,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG;QACtC,aAAa,SAAS,GAAG,OAAO,CAAE,SAAS,KAAK,EAAE,KAAK,EAAE,MAAM;YAC3D,IAAI,QAAQ,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,OAAO;YAC5D,IAAI,WAAW,MAAM,QAAQ,CAAC,OAAO;YACrC,IAAI,SAAS,SAAS,IAAI,aAAa,QAAQ,GAAG,aAAa,QAAQ;YACvE,MAAM,iBAAiB,UAAU,aAAa,IAAI;YAClD,IAAI,qBAAqB,QAAQ,GAAG;gBAChC,SAAS,aAAa,QAAQ;gBAC9B,QAAQ;gBACR,MAAM,UAAU;YACpB;YACA,MAAM,CAAC,UAAU,GAAG,MAAM,CAAC,UAAU,IAAI,CAAC;YAC1C,MAAM,eAAe,MAAM,CAAC,UAAU;YACtC,IAAI,YAAY,CAAC,SAAS,EAAE;gBACxB,IAAI,gBAAgB;oBAChB,MAAM,YAAY,CAAC,YAAY,CAAC,SAAS;gBAC7C;gBACA,YAAY,CAAC,SAAS,IAAI;YAC9B,OAAO;gBACH,YAAY,CAAC,SAAS,GAAG;gBACzB,IAAI,gBAAgB;oBAChB,MAAM,eAAe;gBACzB;YACJ;YACA,IAAI,CAAC,MAAM,QAAQ,IAAI;gBACnB,MAAM,YAAY,MAAM,CAAC,QAAQ,EAAE;gBACnC,IAAI,CAAC,QAAQ,aAAa,UAAU,QAAQ,IAAI;oBAC5C,WAAW,UAAU,QAAQ,CAAC,OAAO;oBACrC,UAAU,iBAAiB,GAAG;oBAC9B,WAAW,KAAK,CAAC,SAAS,GAAG,CAAC,WAAW,KAAK,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,KAAK,CAAC,OAAO,KAAK,CAAC,SAAS,UAAU,QAAQ,IAAI,UAAU,QAAQ,CAAC,OAAO,KAAK,CAAC,CAAC;gBACnK;gBACA,OAAO;YACX,OAAO,IAAI,MAAM;gBACb,OAAO;gBACP,WAAW,IAAI,CAAC,SAAS,GAAG,CAAC,WAAW,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,CAAC,OAAO,KAAK,CAAC,SAAS,MAAM,QAAQ,IAAI,MAAM,QAAQ,CAAC,OAAO,KAAK,CAAC,CAAC;gBACjJ,MAAM,gBAAgB,GAAG;YAC7B;QACJ;IACJ;IACA,OAAO,OAAO,CAAE,SAAS,YAAY;QACjC,MAAM,QAAQ,aAAa,KAAK;QAChC,aAAa,SAAS,GAAG,OAAO,CAAE,SAAS,KAAK;YAC5C,MAAM,WAAW,MAAM,QAAQ,CAAC,OAAO;YACvC,MAAM,UAAU;YAChB,CAAC,MAAM,gBAAgB,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,CAAC,SAAS,IAAI,WAAW,IAAI,CAAC,SAAS,IAAI,GAAG;YACjG,CAAC,MAAM,iBAAiB,IAAI,MAAM,OAAO,CAAC,MAAM,KAAK,CAAC,SAAS,IAAI,WAAW,KAAK,CAAC,SAAS,IAAI,GAAG;YACpG,MAAM,gBAAgB,GAAG;YACzB,MAAM,iBAAiB,GAAG;QAC9B;IACJ;IACA,IAAI,CAAC,aAAa,GAAG;IACrB,OAAO,OAAO,CAAE,SAAS,YAAY;QACjC,aAAa,SAAS,GAAG,OAAO,CAAE,SAAS,KAAK;YAC5C,MAAM,WAAW,MAAM,QAAQ,CAAC,OAAO;YACvC,MAAM,YAAY,aAAa;YAC/B,MAAM,WAAW,oBAAoB,cAAc,WAAW;YAC9D,MAAM,QAAQ,iBAAiB,cAAc,WAAW;YACxD,MAAM,eAAe,CAAC,UAAU,OAAO,WAAW,IAAI,CAAC,SAAS,EAAE,WAAW,KAAK,CAAC,SAAS;QAChG;IACJ;AACJ;AAEA,SAAS;IACL,MAAM,OAAO,IAAI;IACjB,MAAM,SAAS,iBAAiB;IAChC,MAAM,QAAQ,KAAK,aAAa;IAChC,MAAM,eAAe;QACjB,UAAU,CAAC;QACX,UAAU,CAAC;IACf;IACA,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,YAAY;QACnC,MAAM,aAAa,aAAa,UAAU,GAAG,UAAU;QACvD,MAAM,sBAAsB,aAAa,YAAY,GAAG,aAAa;QACrE,MAAM,wBAAwB,cAAc,oBAAoB,aAAa,CAAC;QAC9E,MAAM,YAAY,aAAa,YAAY;QAC3C,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,aAAa,SAAS,IAAK,SAAS,KAAK,EAAE,KAAK;YAClD,IAAI,CAAC,MAAM,QAAQ,IAAI;gBACnB;YACJ;YACA,IAAI,QAAQ,MAAM,YAAY,IAAI,MAAM,YAAY,CAAC,OAAO;YAC5D,MAAM,WAAW,MAAM,QAAQ,CAAC,OAAO;YACvC,IAAI,KAAK,WAAW,EAAE;gBAClB,QAAQ,QAAQ,oBAAoB,OAAO,WAAW,aAAa;YACvE;YACA,MAAM,cAAc,oBAAoB,eAAe,CAAC,OAAO,uBAAuB,MAAM,KAAK;YACjG,MAAM,YAAY,aAAa;YAC/B,MAAM,eAAe,YAAY,CAAC,UAAU,CAAC,UAAU,GAAG,YAAY,CAAC,UAAU,CAAC,UAAU,IAAI,CAAC;YACjG,IAAI,YAAY,CAAC,SAAS,EAAE;gBACxB,MAAM,QAAQ,GAAG,YAAY,CAAC,SAAS;gBACvC,YAAY,CAAC,SAAS,IAAI;YAC9B,OAAO;gBACH,YAAY,CAAC,SAAS,GAAG;YAC7B;YACA,MAAM,KAAK,GAAG,YAAY,CAAC,SAAS;QACxC;IACJ;IACA,IAAI,KAAK,WAAW,EAAE;QAClB,8BAA8B,QAAQ;IAC1C;AACJ;AAEA,SAAS,8BAA8B,MAAM,EAAE,YAAY;IACvD,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,YAAY;QACnC,MAAM,YAAY,aAAa,YAAY,GAAG,aAAa,YAAY,KAAK;QAC5E,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,aAAa,SAAS,IAAK,SAAS,KAAK,EAAE,KAAK;YAClD,MAAM,WAAW,oBAAoB,cAAc,WAAW,MAAM,QAAQ,CAAC,OAAO;YACpF,IAAI,MAAM,UAAU;gBAChB,MAAM,KAAK,GAAG,MAAM,KAAK,GAAG;gBAC5B,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ,GAAG;oBAC3B,MAAM,QAAQ,GAAG,MAAM,QAAQ,GAAG;gBACtC;YACJ;QACJ;IACJ;AACJ;AAEA,SAAS;IACL,MAAM,SAAS,iBAAiB,IAAI;IACpC,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,YAAY;QACnC,MAAM,aAAa,aAAa,UAAU,GAAG,UAAU;QACvD,MAAM,sBAAsB,aAAa,YAAY,GAAG,aAAa;QACrE,MAAM,wBAAwB,cAAc,oBAAoB,aAAa,CAAC;QAC9E,IAAI,uBAAuB;YACvB,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,aAAa,SAAS,IAAK,SAAS,CAAC,EAAE,KAAK;gBAC9C,IAAI,CAAC,MAAM,QAAQ,IAAI;oBACnB;gBACJ;gBACA,IAAI,MAAM,KAAK,CAAC,OAAO,KAAK,MAAM,QAAQ,CAAC,OAAO,KAAK,uBAAuB;oBAC1E,MAAM,KAAK,GAAG,oBAAoB,OAAO,CAAC,MAAM,KAAK,CAAC,OAAO,KAAK,wBAAwB;oBAC1F,MAAM,QAAQ,GAAG,oBAAoB,OAAO,CAAC,MAAM,QAAQ,CAAC,OAAO,KAAK,wBAAwB;gBACpG;YACJ;QACJ;IACJ;AACJ;AAEA,SAAS;IACL,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAG,SAAS,CAAC,EAAE,YAAY;QACxC,MAAM,aAAa,aAAa,UAAU,GAAG,UAAU;QACvD,MAAM,sBAAsB,aAAa,YAAY,GAAG,aAAa;QACrE,MAAM,wBAAwB,cAAc,oBAAoB,aAAa,CAAC;QAC9E,IAAI,uBAAuB;YACvB,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,aAAa,SAAS,IAAK,SAAS,KAAK,EAAE,KAAK;gBAClD,IAAI,MAAM,QAAQ,IAAI;oBAClB,MAAM,KAAK,GAAG,oBAAoB,eAAe,CAAC,MAAM,YAAY,EAAE;gBAC1E;YACJ;QACJ;IACJ;AACJ;AAEA,SAAS;IACL,MAAM,SAAS,iBAAiB,IAAI;IACpC,8BAA8B,QAAQ;QAClC,iBAAiB;IACrB,GAAG,4BAA4B,WAAW;AAC9C;AAEA,SAAS;IACL,MAAM,SAAS,iBAAiB,IAAI;IACpC,IAAI,CAAC,OAAO,MAAM,EAAE;QAChB;IACJ;IACA,MAAM,UAAU,IAAI,CAAC,QAAQ;IAC7B,MAAM,eAAe,MAAM,CAAC,EAAE,CAAC,eAAe,GAAG,cAAc;IAC/D,MAAM,eAAe,MAAM,CAAC,EAAE,CAAC,YAAY,GAAG,cAAc;IAC5D,MAAM,MAAM,KAAK,YAAY,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE;IACrF,MAAM,gBAAgB,IAAI,QAAQ,aAAa,EAAE;IACjD,MAAM,gBAAgB,IAAI,MAAM,QAAQ,aAAa,EAAE;IACvD,MAAM,kBAAkB,CAAC,MAAM,QAAQ,aAAa,GAAG,QAAQ,aAAa,IAAI;IAChF,IAAI,eAAe,IAAI;IACvB,IAAI,eAAe,CAAC,IAAI;IACxB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,UAAU;QACjC,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,WAAW,SAAS,IAAK,SAAS,CAAC,EAAE,KAAK;YAC5C,eAAe,eAAe,MAAM,IAAI,GAAG,eAAe,MAAM,IAAI;YACpE,eAAe,eAAe,MAAM,IAAI,GAAG,eAAe,MAAM,IAAI;QACxE;IACJ;IACA,MAAM,iBAAiB,eAAe;IACtC,MAAM,iBAAiB,IAAI,gBAAgB;IAC3C,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,UAAU;QACjC,CAAA,GAAA,iLAAA,CAAA,OAAK,AAAD,EAAE,WAAW,SAAS,IAAK,SAAS,CAAC,EAAE,KAAK;YAC5C,IAAI,iBAAiB,cAAc;gBAC/B,YAAY,MAAM;YACtB,OAAO;gBACH,iBAAiB,IAAI,MAAM,IAAI,GAAG,gBAAgB;gBAClD,aAAa,iBAAiB,iBAAiB;gBAC/C,YAAY,MAAM,KAAK;YAC3B;YACA,MAAM,kBAAkB,CAAC;QAC7B;IACJ;AACJ;AACO,SAAS,aAAa,OAAO;IAChC,MAAM,OAAO,IAAI;IACjB,KAAK,IAAI,GAAG,CAAA,GAAA,yJAAA,CAAA,gBAAc,AAAD,EAAE,QAAQ,IAAI;IACvC,KAAK,IAAI,GAAG,QAAQ,IAAI;IACxB,KAAK,MAAM,GAAG,EAAE;IAChB,KAAK,aAAa,CAAC;IACnB,OAAQ,KAAK,IAAI;QACb,KAAK;YACD,KAAK,sBAAsB,GAAG;YAC9B,KAAK,kBAAkB,GAAG;YAC1B,KAAK,kBAAkB,GAAG;YAC1B;QACJ,KAAK;YACD,KAAK,sBAAsB,GAAG;YAC9B,KAAK,kBAAkB,GAAG;YAC1B;QACJ,KAAK;YACD,KAAK,WAAW,GAAG;YACnB,KAAK,sBAAsB,GAAG;YAC9B,KAAK,kBAAkB,GAAG;YAC1B,KAAK,kBAAkB,GAAG;YAC1B;QACJ,KAAK;YACD,KAAK,sBAAsB,GAAG;YAC9B,KAAK,kBAAkB,GAAG;YAC1B,KAAK,kBAAkB,GAAG;YAC1B;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,KAAK,WAAW,GAAG;YACnB,KAAK,kBAAkB,GAAG;YAC1B;QACJ,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,KAAK,kBAAkB,GAAG;YAC1B;QACJ,KAAK;QACL,KAAK;YACD,KAAK,sBAAsB,GAAG;YAC9B;QACJ,KAAK;YACD,KAAK,sBAAsB,GAAG;IACtC;AACJ;AACA,aAAa,SAAS,GAAG;IACrB,aAAa;IACb,wBAAwB,+KAAA,CAAA,OAAK;IAC7B,oBAAoB,+KAAA,CAAA,OAAK;IACzB,oBAAoB,+KAAA,CAAA,OAAK;IACzB,eAAe,SAAS,OAAO;QAC3B,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,SAAS;QACL,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,KAAK,SAAS,MAAM;QAChB,MAAM,OAAO,IAAI,CAAC,IAAI;QACtB,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,yJAAA,CAAA,MAAI,AAAD,EAAE,QAAS,CAAA,eAAgB,aAAa,IAAI,KAAK,OAAO,eAAe;IAC5F;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5047, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/base_theme_manager.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/base_theme_manager.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Class from \"../../core/class\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    isString as _isString\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    createPalette as getPalette,\r\n    getDiscretePalette,\r\n    getGradientPalette,\r\n    getAccentColor as accentColor\r\n} from \"../palette\";\r\nimport {\r\n    parseScalar as _parseScalar\r\n} from \"./utils\";\r\nimport {\r\n    getTheme,\r\n    addCacheItem,\r\n    removeCacheItem\r\n} from \"../themes\";\r\nconst _getTheme = getTheme;\r\nconst _addCacheItem = addCacheItem;\r\nconst _removeCacheItem = removeCacheItem;\r\nconst _extend = extend;\r\nconst _each = each;\r\n\r\nfunction getThemePart(theme, path) {\r\n    let _theme = theme;\r\n    path && _each(path.split(\".\"), (function(_, pathItem) {\r\n        return _theme = _theme[pathItem]\r\n    }));\r\n    return _theme\r\n}\r\nexport const BaseThemeManager = Class.inherit({\r\n    ctor: function(options) {\r\n        this._themeSection = options.themeSection;\r\n        this._fontFields = options.fontFields || [];\r\n        _addCacheItem(this)\r\n    },\r\n    dispose: function() {\r\n        _removeCacheItem(this);\r\n        this._callback = this._theme = this._font = null;\r\n        return this\r\n    },\r\n    setCallback: function(callback) {\r\n        this._callback = callback;\r\n        return this\r\n    },\r\n    setTheme: function(theme, rtl) {\r\n        this._current = theme;\r\n        this._rtl = rtl;\r\n        return this.refresh()\r\n    },\r\n    refresh: function() {\r\n        const that = this;\r\n        const current = that._current || {};\r\n        let theme = _getTheme(current.name || current);\r\n        that._themeName = theme.name;\r\n        that._defaultPalette = theme.defaultPalette;\r\n        that._font = _extend({}, theme.font, current.font);\r\n        that._themeSection && _each(that._themeSection.split(\".\"), (function(_, path) {\r\n            theme = _extend(true, {}, theme[path])\r\n        }));\r\n        that._theme = _extend(true, {}, theme, _isString(current) ? {} : current);\r\n        that._initializeTheme();\r\n        if (_parseScalar(that._rtl, that._theme.rtlEnabled)) {\r\n            _extend(true, that._theme, that._theme._rtl)\r\n        }\r\n        that._callback();\r\n        return that\r\n    },\r\n    theme: function(path) {\r\n        return getThemePart(this._theme, path)\r\n    },\r\n    themeName: function() {\r\n        return this._themeName\r\n    },\r\n    createPalette: function(palette, options) {\r\n        return getPalette(palette, options, this._defaultPalette)\r\n    },\r\n    createDiscretePalette: function(palette, count) {\r\n        return getDiscretePalette(palette, count, this._defaultPalette)\r\n    },\r\n    createGradientPalette: function(palette) {\r\n        return getGradientPalette(palette, this._defaultPalette)\r\n    },\r\n    getAccentColor: function(palette) {\r\n        return accentColor(palette, this._defaultPalette)\r\n    },\r\n    _initializeTheme: function() {\r\n        const that = this;\r\n        _each(that._fontFields || [], (function(_, path) {\r\n            that._initializeFont(getThemePart(that._theme, path))\r\n        }))\r\n    },\r\n    _initializeFont: function(font) {\r\n        _extend(font, this._font, _extend({}, font))\r\n    }\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAMA;AAGA;;;;;;;;AAKA,MAAM,YAAY,kJAAA,CAAA,WAAQ;AAC1B,MAAM,gBAAgB,kJAAA,CAAA,eAAY;AAClC,MAAM,mBAAmB,kJAAA,CAAA,kBAAe;AACxC,MAAM,UAAU,+KAAA,CAAA,SAAM;AACtB,MAAM,QAAQ,iLAAA,CAAA,OAAI;AAElB,SAAS,aAAa,KAAK,EAAE,IAAI;IAC7B,IAAI,SAAS;IACb,QAAQ,MAAM,KAAK,KAAK,CAAC,MAAO,SAAS,CAAC,EAAE,QAAQ;QAChD,OAAO,SAAS,MAAM,CAAC,SAAS;IACpC;IACA,OAAO;AACX;AACO,MAAM,mBAAmB,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC;IAC1C,MAAM,SAAS,OAAO;QAClB,IAAI,CAAC,aAAa,GAAG,QAAQ,YAAY;QACzC,IAAI,CAAC,WAAW,GAAG,QAAQ,UAAU,IAAI,EAAE;QAC3C,cAAc,IAAI;IACtB;IACA,SAAS;QACL,iBAAiB,IAAI;QACrB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG;QAC5C,OAAO,IAAI;IACf;IACA,aAAa,SAAS,QAAQ;QAC1B,IAAI,CAAC,SAAS,GAAG;QACjB,OAAO,IAAI;IACf;IACA,UAAU,SAAS,KAAK,EAAE,GAAG;QACzB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,SAAS;QACL,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ,IAAI,CAAC;QAClC,IAAI,QAAQ,UAAU,QAAQ,IAAI,IAAI;QACtC,KAAK,UAAU,GAAG,MAAM,IAAI;QAC5B,KAAK,eAAe,GAAG,MAAM,cAAc;QAC3C,KAAK,KAAK,GAAG,QAAQ,CAAC,GAAG,MAAM,IAAI,EAAE,QAAQ,IAAI;QACjD,KAAK,aAAa,IAAI,MAAM,KAAK,aAAa,CAAC,KAAK,CAAC,MAAO,SAAS,CAAC,EAAE,IAAI;YACxE,QAAQ,QAAQ,MAAM,CAAC,GAAG,KAAK,CAAC,KAAK;QACzC;QACA,KAAK,MAAM,GAAG,QAAQ,MAAM,CAAC,GAAG,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAS,AAAD,EAAE,WAAW,CAAC,IAAI;QACjE,KAAK,gBAAgB;QACrB,IAAI,CAAA,GAAA,yJAAA,CAAA,cAAY,AAAD,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,CAAC,UAAU,GAAG;YACjD,QAAQ,MAAM,KAAK,MAAM,EAAE,KAAK,MAAM,CAAC,IAAI;QAC/C;QACA,KAAK,SAAS;QACd,OAAO;IACX;IACA,OAAO,SAAS,IAAI;QAChB,OAAO,aAAa,IAAI,CAAC,MAAM,EAAE;IACrC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,eAAe,SAAS,OAAO,EAAE,OAAO;QACpC,OAAO,CAAA,GAAA,mJAAA,CAAA,gBAAU,AAAD,EAAE,SAAS,SAAS,IAAI,CAAC,eAAe;IAC5D;IACA,uBAAuB,SAAS,OAAO,EAAE,KAAK;QAC1C,OAAO,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,OAAO,IAAI,CAAC,eAAe;IAClE;IACA,uBAAuB,SAAS,OAAO;QACnC,OAAO,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,SAAS,IAAI,CAAC,eAAe;IAC3D;IACA,gBAAgB,SAAS,OAAO;QAC5B,OAAO,CAAA,GAAA,mJAAA,CAAA,iBAAW,AAAD,EAAE,SAAS,IAAI,CAAC,eAAe;IACpD;IACA,kBAAkB;QACd,MAAM,OAAO,IAAI;QACjB,MAAM,KAAK,WAAW,IAAI,EAAE,EAAG,SAAS,CAAC,EAAE,IAAI;YAC3C,KAAK,eAAe,CAAC,aAAa,KAAK,MAAM,EAAE;QACnD;IACJ;IACA,iBAAiB,SAAS,IAAI;QAC1B,QAAQ,MAAM,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG;IAC1C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5156, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/title.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/title.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isString as _isString\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    patchFontOptions as _patchFontOptions,\r\n    enumParser\r\n} from \"./utils\";\r\nimport {\r\n    LayoutElement\r\n} from \"./layout_element\";\r\nconst _Number = Number;\r\nconst parseHorizontalAlignment = enumParser([\"left\", \"center\", \"right\"]);\r\nconst parseVerticalAlignment = enumParser([\"top\", \"bottom\"]);\r\nconst DEFAULT_MARGIN = 10;\r\n\r\nfunction hasText(text) {\r\n    return !!(text && String(text).length > 0)\r\n}\r\n\r\nfunction processTitleLength(elem, text, width, options, placeholderSize) {\r\n    if (elem.attr({\r\n            text: text\r\n        }).setMaxSize(width, placeholderSize, options).textChanged) {\r\n        elem.setTitle(text)\r\n    }\r\n}\r\n\r\nfunction pickMarginValue(value) {\r\n    return value >= 0 ? _Number(value) : 10\r\n}\r\n\r\nfunction validateMargin(margin) {\r\n    let result;\r\n    if (margin >= 0) {\r\n        result = {\r\n            left: _Number(margin),\r\n            top: _Number(margin),\r\n            right: _Number(margin),\r\n            bottom: _Number(margin)\r\n        }\r\n    } else {\r\n        margin = margin || {};\r\n        result = {\r\n            left: pickMarginValue(margin.left),\r\n            top: pickMarginValue(margin.top),\r\n            right: pickMarginValue(margin.right),\r\n            bottom: pickMarginValue(margin.bottom)\r\n        }\r\n    }\r\n    return result\r\n}\r\n\r\nfunction checkRect(rect, boundingRect) {\r\n    return rect[2] - rect[0] < boundingRect.width || rect[3] - rect[1] < boundingRect.height\r\n}\r\nexport let Title = function(params) {\r\n    this._params = params;\r\n    this._group = params.renderer.g().attr({\r\n        class: params.cssClass\r\n    }).linkOn(params.root || params.renderer.root, \"title\");\r\n    this._hasText = false\r\n};\r\nextend(Title.prototype, LayoutElement.prototype, {\r\n    dispose: function() {\r\n        const that = this;\r\n        that._group.linkRemove();\r\n        that._group.linkOff();\r\n        if (that._titleElement) {\r\n            that._clipRect.dispose();\r\n            that._titleElement = that._subtitleElement = that._clipRect = null\r\n        }\r\n        that._params = that._group = that._options = null\r\n    },\r\n    _updateOptions: function(options) {\r\n        this._options = options;\r\n        this._options.horizontalAlignment = parseHorizontalAlignment(options.horizontalAlignment, \"center\");\r\n        this._options.verticalAlignment = parseVerticalAlignment(options.verticalAlignment, \"top\");\r\n        this._options.margin = validateMargin(options.margin)\r\n    },\r\n    _updateStructure: function() {\r\n        const that = this;\r\n        const renderer = that._params.renderer;\r\n        const group = that._group;\r\n        const options = that._options;\r\n        const align = options.horizontalAlignment;\r\n        if (!that._titleElement) {\r\n            that._titleElement = renderer.text().append(group);\r\n            that._subtitleElement = renderer.text();\r\n            that._clipRect = renderer.clipRect();\r\n            group.attr({\r\n                \"clip-path\": that._clipRect.id\r\n            })\r\n        }\r\n        that._titleElement.attr({\r\n            align: align,\r\n            class: options.cssClass\r\n        });\r\n        that._subtitleElement.attr({\r\n            align: align,\r\n            class: options.subtitle.cssClass\r\n        });\r\n        group.linkAppend();\r\n        hasText(options.subtitle.text) ? that._subtitleElement.append(group) : that._subtitleElement.remove()\r\n    },\r\n    _updateTexts: function() {\r\n        const options = this._options;\r\n        const subtitleOptions = options.subtitle;\r\n        const titleElement = this._titleElement;\r\n        const subtitleElement = this._subtitleElement;\r\n        let titleBox;\r\n        titleElement.attr({\r\n            text: \"A\",\r\n            y: 0\r\n        }).css(_patchFontOptions(options.font));\r\n        titleBox = titleElement.getBBox();\r\n        this._baseLineCorrection = titleBox.height + titleBox.y;\r\n        titleElement.attr({\r\n            text: options.text\r\n        });\r\n        titleBox = titleElement.getBBox();\r\n        const y = -titleBox.y;\r\n        titleElement.attr({\r\n            y: y\r\n        });\r\n        if (hasText(subtitleOptions.text)) {\r\n            subtitleElement.attr({\r\n                text: subtitleOptions.text,\r\n                y: 0\r\n            }).css(_patchFontOptions(subtitleOptions.font))\r\n        }\r\n    },\r\n    _shiftSubtitle() {\r\n        const titleBox = this._titleElement.getBBox();\r\n        const element = this._subtitleElement;\r\n        const offset = this._options.subtitle.offset;\r\n        element.move(0, titleBox.y + titleBox.height - element.getBBox().y - offset)\r\n    },\r\n    _updateBoundingRectAlignment: function() {\r\n        const boundingRect = this._boundingRect;\r\n        const options = this._options;\r\n        boundingRect.verticalAlignment = options.verticalAlignment;\r\n        boundingRect.horizontalAlignment = options.horizontalAlignment;\r\n        boundingRect.cutLayoutSide = options.verticalAlignment;\r\n        boundingRect.cutSide = \"vertical\";\r\n        boundingRect.position = {\r\n            horizontal: options.horizontalAlignment,\r\n            vertical: options.verticalAlignment\r\n        }\r\n    },\r\n    hasText: function() {\r\n        return this._hasText\r\n    },\r\n    update: function(themeOptions, userOptions) {\r\n        const that = this;\r\n        const options = extend(true, {}, themeOptions, processTitleOptions(userOptions));\r\n        const _hasText = hasText(options.text);\r\n        const isLayoutChanged = _hasText || _hasText !== that._hasText;\r\n        that._baseLineCorrection = 0;\r\n        that._updateOptions(options);\r\n        that._boundingRect = {};\r\n        if (_hasText) {\r\n            that._updateStructure();\r\n            that._updateTexts()\r\n        } else {\r\n            that._group.linkRemove()\r\n        }\r\n        that._updateBoundingRect();\r\n        that._updateBoundingRectAlignment();\r\n        that._hasText = _hasText;\r\n        return isLayoutChanged\r\n    },\r\n    draw: function(width, height) {\r\n        const that = this;\r\n        if (that._hasText) {\r\n            that._group.linkAppend();\r\n            that._correctTitleLength(width);\r\n            if (that._group.getBBox().height > height) {\r\n                this.freeSpace()\r\n            }\r\n        }\r\n        return that\r\n    },\r\n    _correctTitleLength: function(width) {\r\n        const that = this;\r\n        const options = that._options;\r\n        const margin = options.margin;\r\n        const maxWidth = width - margin.left - margin.right;\r\n        let placeholderSize = options.placeholderSize;\r\n        processTitleLength(that._titleElement, options.text, maxWidth, options, placeholderSize);\r\n        if (that._subtitleElement) {\r\n            if (_Number(placeholderSize) > 0) {\r\n                placeholderSize -= that._titleElement.getBBox().height\r\n            }\r\n            processTitleLength(that._subtitleElement, options.subtitle.text, maxWidth, options.subtitle, placeholderSize);\r\n            that._shiftSubtitle()\r\n        }\r\n        that._updateBoundingRect();\r\n        const {\r\n            x: x,\r\n            y: y,\r\n            height: height\r\n        } = this.getCorrectedLayoutOptions();\r\n        this._clipRect.attr({\r\n            x: x,\r\n            y: y,\r\n            width: width,\r\n            height: height\r\n        })\r\n    },\r\n    getLayoutOptions: function() {\r\n        return this._boundingRect || null\r\n    },\r\n    shift: function(x, y) {\r\n        const box = this.getLayoutOptions();\r\n        this._group.move(x - box.x, y - box.y);\r\n        return this\r\n    },\r\n    _updateBoundingRect: function() {\r\n        const that = this;\r\n        const options = that._options;\r\n        const margin = options.margin;\r\n        const boundingRect = that._boundingRect;\r\n        const box = that._hasText ? that._group.getBBox() : {\r\n            width: 0,\r\n            height: 0,\r\n            x: 0,\r\n            y: 0,\r\n            isEmpty: true\r\n        };\r\n        if (!box.isEmpty) {\r\n            box.height += margin.top + margin.bottom - that._baseLineCorrection;\r\n            box.width += margin.left + margin.right;\r\n            box.x -= margin.left;\r\n            box.y += that._baseLineCorrection - margin.top\r\n        }\r\n        if (options.placeholderSize > 0) {\r\n            box.height = options.placeholderSize\r\n        }\r\n        boundingRect.height = box.height;\r\n        boundingRect.width = box.width;\r\n        boundingRect.x = box.x;\r\n        boundingRect.y = box.y\r\n    },\r\n    getCorrectedLayoutOptions() {\r\n        const srcBox = this.getLayoutOptions();\r\n        const correction = this._baseLineCorrection;\r\n        return extend({}, srcBox, {\r\n            y: srcBox.y - correction,\r\n            height: srcBox.height + correction\r\n        })\r\n    },\r\n    layoutOptions: function() {\r\n        if (!this._hasText) {\r\n            return null\r\n        }\r\n        return {\r\n            horizontalAlignment: this._boundingRect.horizontalAlignment,\r\n            verticalAlignment: this._boundingRect.verticalAlignment,\r\n            priority: 0\r\n        }\r\n    },\r\n    measure: function(size) {\r\n        this.draw(size[0], size[1]);\r\n        return [this._boundingRect.width, this._boundingRect.height]\r\n    },\r\n    move: function(rect, fitRect) {\r\n        const boundingRect = this._boundingRect;\r\n        if (checkRect(rect, boundingRect)) {\r\n            this.shift(fitRect[0], fitRect[1])\r\n        } else {\r\n            this.shift(Math.round(rect[0]), Math.round(rect[1]))\r\n        }\r\n    },\r\n    freeSpace: function() {\r\n        this._params.incidentOccurred(\"W2103\");\r\n        this._group.linkRemove();\r\n        this._boundingRect.width = this._boundingRect.height = 0\r\n    },\r\n    getOptions: function() {\r\n        return this._options\r\n    },\r\n    changeLink: function(root) {\r\n        this._group.linkRemove();\r\n        this._group.linkOn(root, \"title\")\r\n    }\r\n});\r\n\r\nfunction processTitleOptions(options) {\r\n    const newOptions = _isString(options) ? {\r\n        text: options\r\n    } : options || {};\r\n    newOptions.subtitle = _isString(newOptions.subtitle) ? {\r\n        text: newOptions.subtitle\r\n    } : newOptions.subtitle || {};\r\n    return newOptions\r\n}\r\nexport const plugin = {\r\n    name: \"title\",\r\n    init: function() {\r\n        this._title = new Title({\r\n            renderer: this._renderer,\r\n            cssClass: this._rootClassPrefix + \"-title\",\r\n            incidentOccurred: this._incidentOccurred\r\n        });\r\n        this._layout.add(this._title)\r\n    },\r\n    dispose: function() {\r\n        this._title.dispose();\r\n        this._title = null\r\n    },\r\n    customize: function(constructor) {\r\n        constructor.addChange({\r\n            code: \"TITLE\",\r\n            handler: function() {\r\n                if (this._title.update(this._themeManager.theme(\"title\"), this.option(\"title\"))) {\r\n                    this._change([\"LAYOUT\"])\r\n                }\r\n            },\r\n            isThemeDependent: true,\r\n            option: \"title\",\r\n            isOptionChange: true\r\n        })\r\n    },\r\n    fontFields: [\"title.font\", \"title.subtitle.font\"]\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAGA;AAAA;AAGA;AAIA;;;;;AAGA,MAAM,UAAU;AAChB,MAAM,2BAA2B,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE;IAAC;IAAQ;IAAU;CAAQ;AACvE,MAAM,yBAAyB,CAAA,GAAA,yJAAA,CAAA,aAAU,AAAD,EAAE;IAAC;IAAO;CAAS;AAC3D,MAAM,iBAAiB;AAEvB,SAAS,QAAQ,IAAI;IACjB,OAAO,CAAC,CAAC,CAAC,QAAQ,OAAO,MAAM,MAAM,GAAG,CAAC;AAC7C;AAEA,SAAS,mBAAmB,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,eAAe;IACnE,IAAI,KAAK,IAAI,CAAC;QACN,MAAM;IACV,GAAG,UAAU,CAAC,OAAO,iBAAiB,SAAS,WAAW,EAAE;QAC5D,KAAK,QAAQ,CAAC;IAClB;AACJ;AAEA,SAAS,gBAAgB,KAAK;IAC1B,OAAO,SAAS,IAAI,QAAQ,SAAS;AACzC;AAEA,SAAS,eAAe,MAAM;IAC1B,IAAI;IACJ,IAAI,UAAU,GAAG;QACb,SAAS;YACL,MAAM,QAAQ;YACd,KAAK,QAAQ;YACb,OAAO,QAAQ;YACf,QAAQ,QAAQ;QACpB;IACJ,OAAO;QACH,SAAS,UAAU,CAAC;QACpB,SAAS;YACL,MAAM,gBAAgB,OAAO,IAAI;YACjC,KAAK,gBAAgB,OAAO,GAAG;YAC/B,OAAO,gBAAgB,OAAO,KAAK;YACnC,QAAQ,gBAAgB,OAAO,MAAM;QACzC;IACJ;IACA,OAAO;AACX;AAEA,SAAS,UAAU,IAAI,EAAE,YAAY;IACjC,OAAO,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,aAAa,KAAK,IAAI,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,GAAG,aAAa,MAAM;AAC5F;AACO,IAAI,QAAQ,SAAS,MAAM;IAC9B,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,CAAC,MAAM,GAAG,OAAO,QAAQ,CAAC,CAAC,GAAG,IAAI,CAAC;QACnC,OAAO,OAAO,QAAQ;IAC1B,GAAG,MAAM,CAAC,OAAO,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI,EAAE;IAC/C,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,SAAS,EAAE,kKAAA,CAAA,gBAAa,CAAC,SAAS,EAAE;IAC7C,SAAS;QACL,MAAM,OAAO,IAAI;QACjB,KAAK,MAAM,CAAC,UAAU;QACtB,KAAK,MAAM,CAAC,OAAO;QACnB,IAAI,KAAK,aAAa,EAAE;YACpB,KAAK,SAAS,CAAC,OAAO;YACtB,KAAK,aAAa,GAAG,KAAK,gBAAgB,GAAG,KAAK,SAAS,GAAG;QAClE;QACA,KAAK,OAAO,GAAG,KAAK,MAAM,GAAG,KAAK,QAAQ,GAAG;IACjD;IACA,gBAAgB,SAAS,OAAO;QAC5B,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,GAAG,yBAAyB,QAAQ,mBAAmB,EAAE;QAC1F,IAAI,CAAC,QAAQ,CAAC,iBAAiB,GAAG,uBAAuB,QAAQ,iBAAiB,EAAE;QACpF,IAAI,CAAC,QAAQ,CAAC,MAAM,GAAG,eAAe,QAAQ,MAAM;IACxD;IACA,kBAAkB;QACd,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,OAAO,CAAC,QAAQ;QACtC,MAAM,QAAQ,KAAK,MAAM;QACzB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,QAAQ,QAAQ,mBAAmB;QACzC,IAAI,CAAC,KAAK,aAAa,EAAE;YACrB,KAAK,aAAa,GAAG,SAAS,IAAI,GAAG,MAAM,CAAC;YAC5C,KAAK,gBAAgB,GAAG,SAAS,IAAI;YACrC,KAAK,SAAS,GAAG,SAAS,QAAQ;YAClC,MAAM,IAAI,CAAC;gBACP,aAAa,KAAK,SAAS,CAAC,EAAE;YAClC;QACJ;QACA,KAAK,aAAa,CAAC,IAAI,CAAC;YACpB,OAAO;YACP,OAAO,QAAQ,QAAQ;QAC3B;QACA,KAAK,gBAAgB,CAAC,IAAI,CAAC;YACvB,OAAO;YACP,OAAO,QAAQ,QAAQ,CAAC,QAAQ;QACpC;QACA,MAAM,UAAU;QAChB,QAAQ,QAAQ,QAAQ,CAAC,IAAI,IAAI,KAAK,gBAAgB,CAAC,MAAM,CAAC,SAAS,KAAK,gBAAgB,CAAC,MAAM;IACvG;IACA,cAAc;QACV,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,kBAAkB,QAAQ,QAAQ;QACxC,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,kBAAkB,IAAI,CAAC,gBAAgB;QAC7C,IAAI;QACJ,aAAa,IAAI,CAAC;YACd,MAAM;YACN,GAAG;QACP,GAAG,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAiB,AAAD,EAAE,QAAQ,IAAI;QACrC,WAAW,aAAa,OAAO;QAC/B,IAAI,CAAC,mBAAmB,GAAG,SAAS,MAAM,GAAG,SAAS,CAAC;QACvD,aAAa,IAAI,CAAC;YACd,MAAM,QAAQ,IAAI;QACtB;QACA,WAAW,aAAa,OAAO;QAC/B,MAAM,IAAI,CAAC,SAAS,CAAC;QACrB,aAAa,IAAI,CAAC;YACd,GAAG;QACP;QACA,IAAI,QAAQ,gBAAgB,IAAI,GAAG;YAC/B,gBAAgB,IAAI,CAAC;gBACjB,MAAM,gBAAgB,IAAI;gBAC1B,GAAG;YACP,GAAG,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAiB,AAAD,EAAE,gBAAgB,IAAI;QACjD;IACJ;IACA;QACI,MAAM,WAAW,IAAI,CAAC,aAAa,CAAC,OAAO;QAC3C,MAAM,UAAU,IAAI,CAAC,gBAAgB;QACrC,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM;QAC5C,QAAQ,IAAI,CAAC,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM,GAAG,QAAQ,OAAO,GAAG,CAAC,GAAG;IACzE;IACA,8BAA8B;QAC1B,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,aAAa,iBAAiB,GAAG,QAAQ,iBAAiB;QAC1D,aAAa,mBAAmB,GAAG,QAAQ,mBAAmB;QAC9D,aAAa,aAAa,GAAG,QAAQ,iBAAiB;QACtD,aAAa,OAAO,GAAG;QACvB,aAAa,QAAQ,GAAG;YACpB,YAAY,QAAQ,mBAAmB;YACvC,UAAU,QAAQ,iBAAiB;QACvC;IACJ;IACA,SAAS;QACL,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,QAAQ,SAAS,YAAY,EAAE,WAAW;QACtC,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,cAAc,oBAAoB;QACnE,MAAM,WAAW,QAAQ,QAAQ,IAAI;QACrC,MAAM,kBAAkB,YAAY,aAAa,KAAK,QAAQ;QAC9D,KAAK,mBAAmB,GAAG;QAC3B,KAAK,cAAc,CAAC;QACpB,KAAK,aAAa,GAAG,CAAC;QACtB,IAAI,UAAU;YACV,KAAK,gBAAgB;YACrB,KAAK,YAAY;QACrB,OAAO;YACH,KAAK,MAAM,CAAC,UAAU;QAC1B;QACA,KAAK,mBAAmB;QACxB,KAAK,4BAA4B;QACjC,KAAK,QAAQ,GAAG;QAChB,OAAO;IACX;IACA,MAAM,SAAS,KAAK,EAAE,MAAM;QACxB,MAAM,OAAO,IAAI;QACjB,IAAI,KAAK,QAAQ,EAAE;YACf,KAAK,MAAM,CAAC,UAAU;YACtB,KAAK,mBAAmB,CAAC;YACzB,IAAI,KAAK,MAAM,CAAC,OAAO,GAAG,MAAM,GAAG,QAAQ;gBACvC,IAAI,CAAC,SAAS;YAClB;QACJ;QACA,OAAO;IACX;IACA,qBAAqB,SAAS,KAAK;QAC/B,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,SAAS,QAAQ,MAAM;QAC7B,MAAM,WAAW,QAAQ,OAAO,IAAI,GAAG,OAAO,KAAK;QACnD,IAAI,kBAAkB,QAAQ,eAAe;QAC7C,mBAAmB,KAAK,aAAa,EAAE,QAAQ,IAAI,EAAE,UAAU,SAAS;QACxE,IAAI,KAAK,gBAAgB,EAAE;YACvB,IAAI,QAAQ,mBAAmB,GAAG;gBAC9B,mBAAmB,KAAK,aAAa,CAAC,OAAO,GAAG,MAAM;YAC1D;YACA,mBAAmB,KAAK,gBAAgB,EAAE,QAAQ,QAAQ,CAAC,IAAI,EAAE,UAAU,QAAQ,QAAQ,EAAE;YAC7F,KAAK,cAAc;QACvB;QACA,KAAK,mBAAmB;QACxB,MAAM,EACF,GAAG,CAAC,EACJ,GAAG,CAAC,EACJ,QAAQ,MAAM,EACjB,GAAG,IAAI,CAAC,yBAAyB;QAClC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAChB,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;QACZ;IACJ;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,aAAa,IAAI;IACjC;IACA,OAAO,SAAS,CAAC,EAAE,CAAC;QAChB,MAAM,MAAM,IAAI,CAAC,gBAAgB;QACjC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC;QACrC,OAAO,IAAI;IACf;IACA,qBAAqB;QACjB,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,SAAS,QAAQ,MAAM;QAC7B,MAAM,eAAe,KAAK,aAAa;QACvC,MAAM,MAAM,KAAK,QAAQ,GAAG,KAAK,MAAM,CAAC,OAAO,KAAK;YAChD,OAAO;YACP,QAAQ;YACR,GAAG;YACH,GAAG;YACH,SAAS;QACb;QACA,IAAI,CAAC,IAAI,OAAO,EAAE;YACd,IAAI,MAAM,IAAI,OAAO,GAAG,GAAG,OAAO,MAAM,GAAG,KAAK,mBAAmB;YACnE,IAAI,KAAK,IAAI,OAAO,IAAI,GAAG,OAAO,KAAK;YACvC,IAAI,CAAC,IAAI,OAAO,IAAI;YACpB,IAAI,CAAC,IAAI,KAAK,mBAAmB,GAAG,OAAO,GAAG;QAClD;QACA,IAAI,QAAQ,eAAe,GAAG,GAAG;YAC7B,IAAI,MAAM,GAAG,QAAQ,eAAe;QACxC;QACA,aAAa,MAAM,GAAG,IAAI,MAAM;QAChC,aAAa,KAAK,GAAG,IAAI,KAAK;QAC9B,aAAa,CAAC,GAAG,IAAI,CAAC;QACtB,aAAa,CAAC,GAAG,IAAI,CAAC;IAC1B;IACA;QACI,MAAM,SAAS,IAAI,CAAC,gBAAgB;QACpC,MAAM,aAAa,IAAI,CAAC,mBAAmB;QAC3C,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ;YACtB,GAAG,OAAO,CAAC,GAAG;YACd,QAAQ,OAAO,MAAM,GAAG;QAC5B;IACJ;IACA,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;QACX;QACA,OAAO;YACH,qBAAqB,IAAI,CAAC,aAAa,CAAC,mBAAmB;YAC3D,mBAAmB,IAAI,CAAC,aAAa,CAAC,iBAAiB;YACvD,UAAU;QACd;IACJ;IACA,SAAS,SAAS,IAAI;QAClB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QAC1B,OAAO;YAAC,IAAI,CAAC,aAAa,CAAC,KAAK;YAAE,IAAI,CAAC,aAAa,CAAC,MAAM;SAAC;IAChE;IACA,MAAM,SAAS,IAAI,EAAE,OAAO;QACxB,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,IAAI,UAAU,MAAM,eAAe;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,EAAE,EAAE,OAAO,CAAC,EAAE;QACrC,OAAO;YACH,IAAI,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE;QACtD;IACJ;IACA,WAAW;QACP,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;QAC9B,IAAI,CAAC,MAAM,CAAC,UAAU;QACtB,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,MAAM,GAAG;IAC3D;IACA,YAAY;QACR,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,YAAY,SAAS,IAAI;QACrB,IAAI,CAAC,MAAM,CAAC,UAAU;QACtB,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM;IAC7B;AACJ;AAEA,SAAS,oBAAoB,OAAO;IAChC,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,WAAS,AAAD,EAAE,WAAW;QACpC,MAAM;IACV,IAAI,WAAW,CAAC;IAChB,WAAW,QAAQ,GAAG,CAAA,GAAA,6KAAA,CAAA,WAAS,AAAD,EAAE,WAAW,QAAQ,IAAI;QACnD,MAAM,WAAW,QAAQ;IAC7B,IAAI,WAAW,QAAQ,IAAI,CAAC;IAC5B,OAAO;AACX;AACO,MAAM,SAAS;IAClB,MAAM;IACN,MAAM;QACF,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;YACpB,UAAU,IAAI,CAAC,SAAS;YACxB,UAAU,IAAI,CAAC,gBAAgB,GAAG;YAClC,kBAAkB,IAAI,CAAC,iBAAiB;QAC5C;QACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM;IAChC;IACA,SAAS;QACL,IAAI,CAAC,MAAM,CAAC,OAAO;QACnB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,WAAW,SAAS,WAAW;QAC3B,YAAY,SAAS,CAAC;YAClB,MAAM;YACN,SAAS;gBACL,IAAI,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,MAAM,CAAC,WAAW;oBAC7E,IAAI,CAAC,OAAO,CAAC;wBAAC;qBAAS;gBAC3B;YACJ;YACA,kBAAkB;YAClB,QAAQ;YACR,gBAAgB;QACpB;IACJ;IACA,YAAY;QAAC;QAAc;KAAsB;AACrD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5501, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/data_source.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/data_source.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport DataHelperMixin from \"../../data_helper\";\r\nconst postCtor = DataHelperMixin.postCtor;\r\nlet name;\r\nconst members = {\r\n    _dataSourceLoadErrorHandler: function() {\r\n        this._dataSourceChangedHandler()\r\n    },\r\n    _dataSourceOptions: function() {\r\n        return {\r\n            paginate: false\r\n        }\r\n    },\r\n    _updateDataSource: function() {\r\n        this._refreshDataSource();\r\n        if (!this.option(\"dataSource\")) {\r\n            this._dataSourceChangedHandler()\r\n        }\r\n    },\r\n    _dataIsLoaded: function() {\r\n        return !this._dataSource || this._dataSource.isLoaded()\r\n    },\r\n    _dataSourceItems: function() {\r\n        return this._dataSource && this._dataSource.items()\r\n    }\r\n};\r\nfor (name in DataHelperMixin) {\r\n    if (\"postCtor\" === name) {\r\n        continue\r\n    }\r\n    members[name] = DataHelperMixin[name]\r\n}\r\nexport const plugin = {\r\n    name: \"data_source\",\r\n    init: function() {\r\n        postCtor.call(this)\r\n    },\r\n    dispose: noop,\r\n    members: members\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;;;AACA,MAAM,WAAW,gJAAA,CAAA,UAAe,CAAC,QAAQ;AACzC,IAAI;AACJ,MAAM,UAAU;IACZ,6BAA6B;QACzB,IAAI,CAAC,yBAAyB;IAClC;IACA,oBAAoB;QAChB,OAAO;YACH,UAAU;QACd;IACJ;IACA,mBAAmB;QACf,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe;YAC5B,IAAI,CAAC,yBAAyB;QAClC;IACJ;IACA,eAAe;QACX,OAAO,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;IACzD;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,KAAK;IACrD;AACJ;AACA,IAAK,QAAQ,gJAAA,CAAA,UAAe,CAAE;IAC1B,IAAI,eAAe,MAAM;QACrB;IACJ;IACA,OAAO,CAAC,KAAK,GAAG,gJAAA,CAAA,UAAe,CAAC,KAAK;AACzC;AACO,MAAM,SAAS;IAClB,MAAM;IACN,MAAM;QACF,SAAS,IAAI,CAAC,IAAI;IACtB;IACA,SAAS,+KAAA,CAAA,OAAI;IACb,SAAS;AACb", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5558, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/export.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/export.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    getWindow\r\n} from \"../../core/utils/window\";\r\nimport {\r\n    patchFontOptions\r\n} from \"./utils\";\r\nimport {\r\n    HIDDEN_FOR_EXPORT\r\n} from \"../../core/utils/svg\";\r\nimport {\r\n    export as _export,\r\n    image as imageExporter,\r\n    svg as svgExporter,\r\n    pdf as pdfExporter\r\n} from \"../../exporter\";\r\nimport messageLocalization from \"../../common/core/localization/message\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    getTheme\r\n} from \"../themes\";\r\nimport {\r\n    start as hoverEventStart,\r\n    end as hoverEventEnd\r\n} from \"../../common/core/events/hover\";\r\nimport pointerEvents from \"../../common/core/events/pointer\";\r\nimport {\r\n    logger\r\n} from \"../../core/utils/console\";\r\nimport {\r\n    getWidth\r\n} from \"../../core/utils/size\";\r\nimport {\r\n    Renderer\r\n} from \"./renderers/renderer\";\r\nimport $ from \"../../core/renderer\";\r\nconst pointerActions = [pointerEvents.down, pointerEvents.move].join(\" \");\r\nconst BUTTON_SIZE = 35;\r\nconst ICON_COORDS = [\r\n    [9, 12, 26, 12, 26, 14, 9, 14],\r\n    [9, 17, 26, 17, 26, 19, 9, 19],\r\n    [9, 22, 26, 22, 26, 24, 9, 24]\r\n];\r\nconst LIST_PADDING_TOP = 4;\r\nconst LIST_WIDTH = 120;\r\nconst VERTICAL_TEXT_MARGIN = 8;\r\nconst HORIZONTAL_TEXT_MARGIN = 15;\r\nconst MENU_ITEM_HEIGHT = 30;\r\nconst LIST_STROKE_WIDTH = 1;\r\nconst MARGIN = 10;\r\nconst SHADOW_OFFSET = 2;\r\nconst SHADOW_BLUR = 3;\r\nconst DEFAULT_EXPORT_FORMAT = \"PNG\";\r\nconst ALLOWED_IMAGE_FORMATS = [\"PNG\", \"JPEG\", \"GIF\"];\r\nconst ALLOWED_EXTRA_FORMATS = [\"PDF\", \"SVG\"];\r\nconst EXPORT_CSS_CLASS = \"dx-export-menu\";\r\nconst A4WidthCm = \"21cm\";\r\nconst EXPORT_DATA_KEY = \"export-element-type\";\r\nconst FORMAT_DATA_KEY = \"export-element-format\";\r\nconst GET_COLOR_REGEX = /data-backgroundcolor=\"([^\"]*)\"/;\r\n\r\nfunction getRendererWrapper(width, height, backgroundColor) {\r\n    const rendererContainer = $(\"<div>\").get(0);\r\n    const renderer = new Renderer({\r\n        container: rendererContainer\r\n    });\r\n    renderer.resize(width, height);\r\n    renderer.root.element.setAttribute(\"data-backgroundcolor\", backgroundColor);\r\n    return {\r\n        createGroup: () => renderer.g(),\r\n        getRootContent: () => renderer.root.element.cloneNode(true),\r\n        dispose() {\r\n            renderer.dispose();\r\n            rendererContainer.remove()\r\n        }\r\n    }\r\n}\r\n\r\nfunction getValidFormats() {\r\n    const imageFormats = imageExporter.testFormats(ALLOWED_IMAGE_FORMATS);\r\n    return {\r\n        unsupported: imageFormats.unsupported,\r\n        supported: imageFormats.supported.concat(ALLOWED_EXTRA_FORMATS)\r\n    }\r\n}\r\n\r\nfunction validateFormat(format, incidentOccurred, validFormats) {\r\n    validFormats = validFormats || getValidFormats();\r\n    format = String(format).toUpperCase();\r\n    if (-1 !== validFormats.supported.indexOf(format)) {\r\n        return format\r\n    }\r\n    if (-1 !== validFormats.unsupported.indexOf(format)) {\r\n        incidentOccurred && incidentOccurred(\"W2108\", [format])\r\n    }\r\n}\r\n\r\nfunction getCreatorFunc(format) {\r\n    if (\"SVG\" === format) {\r\n        return svgExporter.getData\r\n    } else if (\"PDF\" === format) {\r\n        return pdfExporter.getData\r\n    } else {\r\n        return imageExporter.getData\r\n    }\r\n}\r\n\r\nfunction print(imageSrc, options) {\r\n    const document = getWindow().document;\r\n    const iFrame = document.createElement(\"iframe\");\r\n    iFrame.onload = setPrint(imageSrc, options);\r\n    iFrame.style.position = \"fixed\";\r\n    iFrame.style.width = \"0\";\r\n    iFrame.style.height = \"0\";\r\n    iFrame.style.right = \"0\";\r\n    iFrame.style.bottom = \"0\";\r\n    document.body.appendChild(iFrame)\r\n}\r\n\r\nfunction calculatePrintPageWidth(iFrameBody) {\r\n    iFrameBody.style.width = \"21cm\";\r\n    const width = getWidth(iFrameBody);\r\n    iFrameBody.style.width = \"\";\r\n    return width\r\n}\r\n\r\nfunction setPrint(imageSrc, options) {\r\n    return function() {\r\n        let window = this.contentWindow;\r\n        const img = window.document.createElement(\"img\");\r\n        window.document.body.appendChild(img);\r\n        const widthRatio = calculatePrintPageWidth(window.document.body) / options.width;\r\n        if (widthRatio < 1) {\r\n            window.document.body.style.transform = `scale(${widthRatio})`;\r\n            window.document.body.style[\"transform-origin\"] = \"0 0\"\r\n        }\r\n        const removeFrame = () => {\r\n            this.parentElement.removeChild(this)\r\n        };\r\n        img.addEventListener(\"load\", (() => {\r\n            window.focus();\r\n            window.print()\r\n        }));\r\n        img.addEventListener(\"error\", removeFrame);\r\n        window.addEventListener(\"afterprint\", (() => {\r\n            setTimeout(removeFrame, 0)\r\n        }));\r\n        img.src = imageSrc\r\n    }\r\n}\r\n\r\nfunction getItemAttributes(options, type, itemIndex) {\r\n    const x = -85;\r\n    const y = 40 + 30 * itemIndex;\r\n    const attr = {\r\n        rect: {\r\n            width: 118,\r\n            height: 30,\r\n            x: -84,\r\n            y: y\r\n        },\r\n        text: {\r\n            x: x + (options.rtl ? 105 : 15),\r\n            y: y + 30 - 8\r\n        }\r\n    };\r\n    if (\"printing\" === type) {\r\n        attr.separator = {\r\n            stroke: options.button.default.borderColor,\r\n            \"stroke-width\": 1,\r\n            cursor: \"pointer\",\r\n            sharp: \"v\",\r\n            d: \"M -85 \" + (y + 30 - 1) + \" L 35 \" + (y + 30 - 1)\r\n        }\r\n    }\r\n    return attr\r\n}\r\n\r\nfunction createMenuItem(renderer, options, settings) {\r\n    const itemData = {};\r\n    const type = settings.type;\r\n    const format = settings.format;\r\n    const attr = getItemAttributes(options, type, settings.itemIndex);\r\n    const fontStyle = patchFontOptions(options.font);\r\n    fontStyle[\"pointer-events\"] = \"none\";\r\n    const menuItem = renderer.g().attr({\r\n        class: \"dx-export-menu-list-item\"\r\n    });\r\n    itemData[EXPORT_DATA_KEY] = type;\r\n    if (format) {\r\n        itemData[FORMAT_DATA_KEY] = format\r\n    }\r\n    const rect = renderer.rect();\r\n    rect.attr(attr.rect).css({\r\n        cursor: \"pointer\",\r\n        \"pointer-events\": \"all\"\r\n    }).data(itemData);\r\n    rect.on(hoverEventStart + \".export\", (() => rect.attr({\r\n        fill: options.button.hover.backgroundColor\r\n    }))).on(hoverEventEnd + \".export\", (() => rect.attr({\r\n        fill: null\r\n    })));\r\n    rect.append(menuItem);\r\n    const text = renderer.text(settings.text).css(fontStyle).attr(attr.text).append(menuItem);\r\n    if (\"printing\" === type) {\r\n        renderer.path(null, \"line\").attr(attr.separator).append(menuItem)\r\n    }\r\n    return {\r\n        g: menuItem,\r\n        rect: rect,\r\n        resetState: () => rect.attr({\r\n            fill: null\r\n        }),\r\n        fixPosition: () => {\r\n            const textBBox = text.getBBox();\r\n            text.move(attr.text.x - textBBox.x - (options.rtl ? textBBox.width : 0))\r\n        }\r\n    }\r\n}\r\n\r\nfunction createMenuItems(renderer, options) {\r\n    let items = [];\r\n    if (options.printingEnabled) {\r\n        items.push(createMenuItem(renderer, options, {\r\n            type: \"printing\",\r\n            text: messageLocalization.format(\"vizExport-printingButtonText\"),\r\n            itemIndex: items.length\r\n        }))\r\n    }\r\n    items = options.formats.reduce(((r, format) => {\r\n        r.push(createMenuItem(renderer, options, {\r\n            type: \"exporting\",\r\n            text: messageLocalization.getFormatter(\"vizExport-exportButtonText\")(format),\r\n            format: format,\r\n            itemIndex: r.length\r\n        }));\r\n        return r\r\n    }), items);\r\n    return items\r\n}\r\n\r\nfunction getBackgroundColorFromMarkup(markup) {\r\n    const parsedMarkup = GET_COLOR_REGEX.exec(markup);\r\n    return null === parsedMarkup || void 0 === parsedMarkup ? void 0 : parsedMarkup[1]\r\n}\r\nexport const exportFromMarkup = function(markup, options) {\r\n    options.format = validateFormat(options.format) || \"PNG\";\r\n    options.fileName = options.fileName || \"file\";\r\n    options.exportingAction = options.onExporting;\r\n    options.exportedAction = options.onExported;\r\n    options.fileSavingAction = options.onFileSaving;\r\n    options.margin = isDefined(options.margin) ? options.margin : 10;\r\n    options.backgroundColor = isDefined(options.backgroundColor) ? options.backgroundColor : getBackgroundColorFromMarkup(markup) || getTheme().backgroundColor;\r\n    _export(markup, options, getCreatorFunc(options.format))\r\n};\r\nexport const getMarkup = widgets => combineMarkups(widgets).root.outerHTML;\r\nexport const exportWidgets = function(widgets, options) {\r\n    options = options || {};\r\n    const markupInfo = combineMarkups(widgets, {\r\n        gridLayout: options.gridLayout,\r\n        verticalAlignment: options.verticalAlignment,\r\n        horizontalAlignment: options.horizontalAlignment\r\n    });\r\n    options.width = markupInfo.width;\r\n    options.height = markupInfo.height;\r\n    exportFromMarkup(markupInfo.root, options)\r\n};\r\nexport let combineMarkups = function(widgets) {\r\n    let options = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};\r\n    if (!Array.isArray(widgets)) {\r\n        widgets = [\r\n            [widgets]\r\n        ]\r\n    } else if (!Array.isArray(widgets[0])) {\r\n        widgets = widgets.map((item => [item]))\r\n    }\r\n    const compactView = !options.gridLayout;\r\n    const exportItems = widgets.reduce(((r, row, rowIndex) => {\r\n        const rowInfo = row.reduce(((r, item, colIndex) => {\r\n            const size = item.getSize();\r\n            const backgroundColor = item.option(\"backgroundColor\") || getTheme(item.option(\"theme\")).backgroundColor;\r\n            const node = $(item.element()).find(\"svg\").get(0).cloneNode(true);\r\n            backgroundColor && -1 === r.backgroundColors.indexOf(backgroundColor) && r.backgroundColors.push(backgroundColor);\r\n            r.hOffset = r.width;\r\n            r.width += size.width;\r\n            r.height = Math.max(r.height, size.height);\r\n            r.itemWidth = Math.max(r.itemWidth, size.width);\r\n            r.items.push({\r\n                node: node,\r\n                width: size.width,\r\n                height: size.height,\r\n                c: colIndex,\r\n                r: rowIndex,\r\n                hOffset: r.hOffset\r\n            });\r\n            return r\r\n        }), {\r\n            items: [],\r\n            height: 0,\r\n            itemWidth: 0,\r\n            hOffset: 0,\r\n            width: 0,\r\n            backgroundColors: r.backgroundColors\r\n        });\r\n        r.rowOffsets.push(r.totalHeight);\r\n        r.rowHeights.push(rowInfo.height);\r\n        r.totalHeight += rowInfo.height;\r\n        r.items = r.items.concat(rowInfo.items);\r\n        r.itemWidth = Math.max(r.itemWidth, rowInfo.itemWidth);\r\n        r.maxItemLen = Math.max(r.maxItemLen, rowInfo.items.length);\r\n        r.totalWidth = compactView ? Math.max(r.totalWidth, rowInfo.width) : r.maxItemLen * r.itemWidth;\r\n        return r\r\n    }), {\r\n        items: [],\r\n        rowOffsets: [],\r\n        rowHeights: [],\r\n        itemWidth: 0,\r\n        totalHeight: 0,\r\n        maxItemLen: 0,\r\n        totalWidth: 0,\r\n        backgroundColors: []\r\n    });\r\n    const backgroundColor = `${1===exportItems.backgroundColors.length?exportItems.backgroundColors[0]:\"\"}`;\r\n    const {\r\n        totalWidth: totalWidth,\r\n        totalHeight: totalHeight\r\n    } = exportItems;\r\n    const rootElement = wrapItemsToElement(totalWidth, totalHeight, backgroundColor, {\r\n        options: options,\r\n        exportItems: exportItems,\r\n        compactView: compactView\r\n    });\r\n    return {\r\n        root: rootElement,\r\n        width: totalWidth,\r\n        height: totalHeight\r\n    }\r\n};\r\n\r\nfunction wrapItemsToElement(width, height, backgroundColor, _ref) {\r\n    let {\r\n        exportItems: exportItems,\r\n        options: options,\r\n        compactView: compactView\r\n    } = _ref;\r\n    const rendererWrapper = getRendererWrapper(width, height, backgroundColor);\r\n    const getVOffset = item => {\r\n        const align = options.verticalAlignment;\r\n        const dy = exportItems.rowHeights[item.r] - item.height;\r\n        return exportItems.rowOffsets[item.r] + (\"bottom\" === align ? dy : \"center\" === align ? dy / 2 : 0)\r\n    };\r\n    const getHOffset = item => {\r\n        if (compactView) {\r\n            return item.hOffset\r\n        }\r\n        const align = options.horizontalAlignment;\r\n        const colWidth = exportItems.itemWidth;\r\n        const dx = colWidth - item.width;\r\n        return item.c * colWidth + (\"right\" === align ? dx : \"center\" === align ? dx / 2 : 0)\r\n    };\r\n    exportItems.items.forEach((item => {\r\n        const container = rendererWrapper.createGroup();\r\n        container.attr({\r\n            translateX: getHOffset(item),\r\n            translateY: getVOffset(item)\r\n        });\r\n        container.element.appendChild(item.node);\r\n        container.append()\r\n    }));\r\n    const result = rendererWrapper.getRootContent();\r\n    rendererWrapper.dispose();\r\n    return result\r\n}\r\nexport let ExportMenu = function(params) {\r\n    const renderer = this._renderer = params.renderer;\r\n    this._incidentOccurred = params.incidentOccurred;\r\n    this._exportTo = params.exportTo;\r\n    this._print = params.print;\r\n    this._shadow = renderer.shadowFilter(\"-50%\", \"-50%\", \"200%\", \"200%\", 2, 6, 3);\r\n    this._shadow.attr({\r\n        opacity: .8\r\n    });\r\n    this._group = renderer.g().attr({\r\n        class: \"dx-export-menu\",\r\n        [HIDDEN_FOR_EXPORT]: true\r\n    }).linkOn(renderer.root, {\r\n        name: \"export-menu\",\r\n        after: \"peripheral\"\r\n    });\r\n    this._buttonGroup = renderer.g().attr({\r\n        class: \"dx-export-menu-button\"\r\n    }).append(this._group);\r\n    this._listGroup = renderer.g().attr({\r\n        class: \"dx-export-menu-list\"\r\n    }).append(this._group);\r\n    this._overlay = renderer.rect(-85, 39, 120, 0);\r\n    this._overlay.attr({\r\n        \"stroke-width\": 1,\r\n        cursor: \"pointer\",\r\n        rx: 4,\r\n        ry: 4,\r\n        filter: this._shadow.id\r\n    });\r\n    this._overlay.data({\r\n        \"export-element-type\": \"list\"\r\n    });\r\n    this.validFormats = getValidFormats();\r\n    this._subscribeEvents()\r\n};\r\nextend(ExportMenu.prototype, {\r\n    getLayoutOptions() {\r\n        if (this._hiddenDueToLayout) {\r\n            return {\r\n                width: 0,\r\n                height: 0,\r\n                cutSide: \"vertical\",\r\n                cutLayoutSide: \"top\"\r\n            }\r\n        }\r\n        const bBox = this._buttonGroup.getBBox();\r\n        bBox.cutSide = \"vertical\";\r\n        bBox.cutLayoutSide = \"top\";\r\n        bBox.height += 10;\r\n        bBox.position = {\r\n            vertical: \"top\",\r\n            horizontal: \"right\"\r\n        };\r\n        bBox.verticalAlignment = \"top\";\r\n        bBox.horizontalAlignment = \"right\";\r\n        return bBox\r\n    },\r\n    shift(_, y) {\r\n        this._group.attr({\r\n            translateY: this._group.attr(\"translateY\") + y\r\n        })\r\n    },\r\n    draw(width, height, canvas) {\r\n        this._group.move(width - 35 - 2 - 3 + canvas.left, Math.floor(height / 2 - 17.5));\r\n        const layoutOptions = this.getLayoutOptions();\r\n        if (layoutOptions.width > width || layoutOptions.height > height) {\r\n            this.freeSpace()\r\n        }\r\n        return this\r\n    },\r\n    show() {\r\n        this._group.linkAppend()\r\n    },\r\n    hide() {\r\n        this._group.linkRemove()\r\n    },\r\n    setOptions(options) {\r\n        this._options = options;\r\n        if (options.formats) {\r\n            options.formats = options.formats.reduce(((r, format) => {\r\n                format = validateFormat(format, this._incidentOccurred, this.validFormats);\r\n                format && r.push(format);\r\n                return r\r\n            }), [])\r\n        } else {\r\n            options.formats = this.validFormats.supported.slice()\r\n        }\r\n        options.printingEnabled = void 0 === options.printingEnabled ? true : options.printingEnabled;\r\n        if (options.enabled && (options.formats.length || options.printingEnabled)) {\r\n            this.show();\r\n            this._updateButton();\r\n            this._updateList();\r\n            this._hideList()\r\n        } else {\r\n            this.hide()\r\n        }\r\n    },\r\n    dispose() {\r\n        this._unsubscribeEvents();\r\n        this._group.linkRemove().linkOff();\r\n        this._group.dispose();\r\n        this._shadow.dispose()\r\n    },\r\n    layoutOptions() {\r\n        return this._options.enabled && {\r\n            horizontalAlignment: \"right\",\r\n            verticalAlignment: \"top\",\r\n            weak: true\r\n        }\r\n    },\r\n    measure() {\r\n        this._fillSpace();\r\n        const margin = this._options.button.margin;\r\n        return [35 + margin.left + margin.right, 35 + margin.top + margin.bottom]\r\n    },\r\n    move(rect) {\r\n        const margin = this._options.button.margin;\r\n        this._group.attr({\r\n            translateX: Math.round(rect[0]) + margin.left,\r\n            translateY: Math.round(rect[1]) + margin.top\r\n        })\r\n    },\r\n    _fillSpace() {\r\n        this._hiddenDueToLayout = false;\r\n        this.show()\r\n    },\r\n    freeSpace() {\r\n        this._incidentOccurred(\"W2107\");\r\n        this._hiddenDueToLayout = true;\r\n        this.hide()\r\n    },\r\n    _hideList() {\r\n        this._listGroup.remove();\r\n        this._listShown = false;\r\n        this._setButtonState(\"default\");\r\n        this._menuItems.forEach((item => item.resetState()))\r\n    },\r\n    _showList() {\r\n        this._listGroup.append(this._group);\r\n        this._listShown = true;\r\n        this._menuItems.forEach((item => item.fixPosition()))\r\n    },\r\n    _setButtonState(state) {\r\n        const style = this._options.button[state];\r\n        this._button.attr({\r\n            stroke: style.borderColor,\r\n            fill: style.backgroundColor\r\n        });\r\n        this._icon.attr({\r\n            fill: style.color\r\n        })\r\n    },\r\n    _subscribeEvents() {\r\n        this._renderer.root.on(pointerEvents.up + \".export\", (e => {\r\n            const elementType = e.target[EXPORT_DATA_KEY];\r\n            if (!elementType) {\r\n                if (this._button) {\r\n                    this._hideList()\r\n                }\r\n                return\r\n            }\r\n            if (\"button\" === elementType) {\r\n                if (this._listShown) {\r\n                    this._setButtonState(\"default\");\r\n                    this._hideList()\r\n                } else {\r\n                    this._setButtonState(\"focus\");\r\n                    this._showList()\r\n                }\r\n            } else if (\"printing\" === elementType) {\r\n                this._print();\r\n                this._hideList()\r\n            } else if (\"exporting\" === elementType) {\r\n                this._exportTo(e.target[FORMAT_DATA_KEY]);\r\n                this._hideList()\r\n            }\r\n        }));\r\n        this._listGroup.on(pointerActions, (e => e.stopPropagation()));\r\n        this._buttonGroup.on(pointerEvents.enter, (() => this._setButtonState(\"hover\")));\r\n        this._buttonGroup.on(pointerEvents.leave, (() => this._setButtonState(this._listShown ? \"focus\" : \"default\")));\r\n        this._buttonGroup.on(pointerEvents.down + \".export\", (() => this._setButtonState(\"active\")))\r\n    },\r\n    _unsubscribeEvents() {\r\n        this._renderer.root.off(\".export\");\r\n        this._listGroup.off();\r\n        this._buttonGroup.off()\r\n    },\r\n    _updateButton() {\r\n        const renderer = this._renderer;\r\n        const options = this._options;\r\n        const exportData = {\r\n            \"export-element-type\": \"button\"\r\n        };\r\n        if (!this._button) {\r\n            this._button = renderer.rect(0, 0, 35, 35).append(this._buttonGroup);\r\n            this._button.attr({\r\n                rx: 4,\r\n                ry: 4,\r\n                fill: options.button.default.backgroundColor,\r\n                stroke: options.button.default.borderColor,\r\n                \"stroke-width\": 1,\r\n                cursor: \"pointer\"\r\n            });\r\n            this._button.data(exportData);\r\n            this._icon = renderer.path(ICON_COORDS).append(this._buttonGroup);\r\n            this._icon.attr({\r\n                fill: options.button.default.color,\r\n                cursor: \"pointer\"\r\n            });\r\n            this._icon.data(exportData);\r\n            this._buttonGroup.setTitle(messageLocalization.format(\"vizExport-titleMenuText\"))\r\n        }\r\n    },\r\n    _updateList() {\r\n        const options = this._options;\r\n        const buttonDefault = options.button.default;\r\n        const listGroup = this._listGroup;\r\n        const items = createMenuItems(this._renderer, options);\r\n        this._shadow.attr({\r\n            color: options.shadowColor\r\n        });\r\n        this._overlay.attr({\r\n            height: 30 * items.length + 2,\r\n            fill: buttonDefault.backgroundColor,\r\n            stroke: buttonDefault.borderColor\r\n        });\r\n        listGroup.clear();\r\n        this._overlay.append(listGroup);\r\n        items.forEach((item => item.g.append(listGroup)));\r\n        this._menuItems = items\r\n    }\r\n});\r\n\r\nfunction getExportOptions(widget, exportOptions, fileName, format) {\r\n    if (format || exportOptions.format) {\r\n        format = validateFormat(format || exportOptions.format, widget._incidentOccurred)\r\n    }\r\n    const {\r\n        width: width,\r\n        height: height\r\n    } = widget.getSize();\r\n    return {\r\n        format: format || \"PNG\",\r\n        fileName: fileName || exportOptions.fileName || \"file\",\r\n        backgroundColor: exportOptions.backgroundColor,\r\n        width: width,\r\n        height: height,\r\n        margin: exportOptions.margin,\r\n        svgToCanvas: exportOptions.svgToCanvas,\r\n        exportingAction: widget._createActionByOption(\"onExporting\", {\r\n            excludeValidators: [\"disabled\"]\r\n        }),\r\n        exportedAction: widget._createActionByOption(\"onExported\", {\r\n            excludeValidators: [\"disabled\"]\r\n        }),\r\n        fileSavingAction: widget._createActionByOption(\"onFileSaving\", {\r\n            excludeValidators: [\"disabled\"]\r\n        })\r\n    }\r\n}\r\nexport const plugin = {\r\n    name: \"export\",\r\n    init() {\r\n        this._exportMenu = new ExportMenu({\r\n            renderer: this._renderer,\r\n            incidentOccurred: this._incidentOccurred,\r\n            print: () => this.print(),\r\n            exportTo: format => this.exportTo(void 0, format)\r\n        });\r\n        this._layout.add(this._exportMenu)\r\n    },\r\n    dispose() {\r\n        this._exportMenu.dispose()\r\n    },\r\n    members: {\r\n        _getExportMenuOptions() {\r\n            return extend({}, this._getOption(\"export\"), {\r\n                rtl: this._getOption(\"rtlEnabled\", true)\r\n            })\r\n        },\r\n        _disablePointerEvents() {\r\n            const pointerEventsValue = this._renderer.root.attr(\"pointer-events\");\r\n            this._renderer.root.attr({\r\n                \"pointer-events\": \"none\"\r\n            });\r\n            return pointerEventsValue\r\n        },\r\n        exportTo(fileName, format) {\r\n            const menu = this._exportMenu;\r\n            const options = getExportOptions(this, this._getOption(\"export\") || {}, fileName, format);\r\n            menu && menu.hide();\r\n            const pointerEventsValue = this._disablePointerEvents();\r\n            const promise = _export(this._renderer.root.element, options, getCreatorFunc(options.format)).fail(logger.error).always((() => {\r\n                this._renderer.root.attr({\r\n                    \"pointer-events\": pointerEventsValue\r\n                })\r\n            }));\r\n            menu && menu.show();\r\n            return promise\r\n        },\r\n        print() {\r\n            const menu = this._exportMenu;\r\n            const options = getExportOptions(this, this._getOption(\"export\") || {});\r\n            options.exportingAction = null;\r\n            options.exportedAction = null;\r\n            options.margin = 0;\r\n            options.format = \"PNG\";\r\n            options.useBase64 = true;\r\n            options.fileSavingAction = eventArgs => {\r\n                print(`data:image/png;base64,${eventArgs.data}`, {\r\n                    width: options.width,\r\n                    __test: options.__test\r\n                });\r\n                eventArgs.cancel = true\r\n            };\r\n            const pointerEventsValue = this._disablePointerEvents();\r\n            menu && menu.hide();\r\n            const promise = _export(this._renderer.root.element, options, getCreatorFunc(options.format)).fail(logger.error).always((() => {\r\n                this._renderer.root.attr({\r\n                    \"pointer-events\": pointerEventsValue\r\n                })\r\n            }));\r\n            menu && menu.show();\r\n            return promise\r\n        }\r\n    },\r\n    customize(constructor) {\r\n        const proto = constructor.prototype;\r\n        constructor.addChange({\r\n            code: \"EXPORT\",\r\n            handler() {\r\n                this._exportMenu.setOptions(this._getExportMenuOptions());\r\n                this._change([\"LAYOUT\"])\r\n            },\r\n            isThemeDependent: true,\r\n            isOptionChange: true,\r\n            option: \"export\"\r\n        });\r\n        proto._optionChangesMap.onExporting = \"EXPORT\";\r\n        proto._optionChangesMap.onExported = \"EXPORT\";\r\n        proto._optionChangesMap.onFileSaving = \"EXPORT\"\r\n    },\r\n    fontFields: [\"export.font\"]\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;AACD;AAAA;AAGA;AAAA;AAGA;AAGA;AAAA;AAGA;AAAA;AAMA;AACA;AAAA;AAGA;AAGA;AAAA;AAIA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAGA;;;;;;;;;;;;;;;AACA,MAAM,iBAAiB;IAAC,yKAAA,CAAA,UAAa,CAAC,IAAI;IAAE,yKAAA,CAAA,UAAa,CAAC,IAAI;CAAC,CAAC,IAAI,CAAC;AACrE,MAAM,cAAc;AACpB,MAAM,cAAc;IAChB;QAAC;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAG;KAAG;IAC9B;QAAC;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAG;KAAG;IAC9B;QAAC;QAAG;QAAI;QAAI;QAAI;QAAI;QAAI;QAAG;KAAG;CACjC;AACD,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,uBAAuB;AAC7B,MAAM,yBAAyB;AAC/B,MAAM,mBAAmB;AACzB,MAAM,oBAAoB;AAC1B,MAAM,SAAS;AACf,MAAM,gBAAgB;AACtB,MAAM,cAAc;AACpB,MAAM,wBAAwB;AAC9B,MAAM,wBAAwB;IAAC;IAAO;IAAQ;CAAM;AACpD,MAAM,wBAAwB;IAAC;IAAO;CAAM;AAC5C,MAAM,mBAAmB;AACzB,MAAM,YAAY;AAClB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AACxB,MAAM,kBAAkB;AAExB,SAAS,mBAAmB,KAAK,EAAE,MAAM,EAAE,eAAe;IACtD,MAAM,oBAAoB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,GAAG,CAAC;IACzC,MAAM,WAAW,IAAI,yKAAA,CAAA,WAAQ,CAAC;QAC1B,WAAW;IACf;IACA,SAAS,MAAM,CAAC,OAAO;IACvB,SAAS,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,wBAAwB;IAC3D,OAAO;QACH,aAAa,IAAM,SAAS,CAAC;QAC7B,gBAAgB,IAAM,SAAS,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC;QACtD;YACI,SAAS,OAAO;YAChB,kBAAkB,MAAM;QAC5B;IACJ;AACJ;AAEA,SAAS;IACL,MAAM,eAAe,6JAAA,CAAA,QAAa,CAAC,WAAW,CAAC;IAC/C,OAAO;QACH,aAAa,aAAa,WAAW;QACrC,WAAW,aAAa,SAAS,CAAC,MAAM,CAAC;IAC7C;AACJ;AAEA,SAAS,eAAe,MAAM,EAAE,gBAAgB,EAAE,YAAY;IAC1D,eAAe,gBAAgB;IAC/B,SAAS,OAAO,QAAQ,WAAW;IACnC,IAAI,CAAC,MAAM,aAAa,SAAS,CAAC,OAAO,CAAC,SAAS;QAC/C,OAAO;IACX;IACA,IAAI,CAAC,MAAM,aAAa,WAAW,CAAC,OAAO,CAAC,SAAS;QACjD,oBAAoB,iBAAiB,SAAS;YAAC;SAAO;IAC1D;AACJ;AAEA,SAAS,eAAe,MAAM;IAC1B,IAAI,UAAU,QAAQ;QAClB,OAAO,6JAAA,CAAA,MAAW,CAAC,OAAO;IAC9B,OAAO,IAAI,UAAU,QAAQ;QACzB,OAAO,6JAAA,CAAA,MAAW,CAAC,OAAO;IAC9B,OAAO;QACH,OAAO,6JAAA,CAAA,QAAa,CAAC,OAAO;IAChC;AACJ;AAEA,SAAS,MAAM,QAAQ,EAAE,OAAO;IAC5B,MAAM,WAAW,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,IAAI,QAAQ;IACrC,MAAM,SAAS,SAAS,aAAa,CAAC;IACtC,OAAO,MAAM,GAAG,SAAS,UAAU;IACnC,OAAO,KAAK,CAAC,QAAQ,GAAG;IACxB,OAAO,KAAK,CAAC,KAAK,GAAG;IACrB,OAAO,KAAK,CAAC,MAAM,GAAG;IACtB,OAAO,KAAK,CAAC,KAAK,GAAG;IACrB,OAAO,KAAK,CAAC,MAAM,GAAG;IACtB,SAAS,IAAI,CAAC,WAAW,CAAC;AAC9B;AAEA,SAAS,wBAAwB,UAAU;IACvC,WAAW,KAAK,CAAC,KAAK,GAAG;IACzB,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE;IACvB,WAAW,KAAK,CAAC,KAAK,GAAG;IACzB,OAAO;AACX;AAEA,SAAS,SAAS,QAAQ,EAAE,OAAO;IAC/B,OAAO;QACH,IAAI,SAAS,IAAI,CAAC,aAAa;QAC/B,MAAM,MAAM,OAAO,QAAQ,CAAC,aAAa,CAAC;QAC1C,OAAO,QAAQ,CAAC,IAAI,CAAC,WAAW,CAAC;QACjC,MAAM,aAAa,wBAAwB,OAAO,QAAQ,CAAC,IAAI,IAAI,QAAQ,KAAK;QAChF,IAAI,aAAa,GAAG;YAChB,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC,CAAC;YAC7D,OAAO,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,mBAAmB,GAAG;QACrD;QACA,MAAM,cAAc;YAChB,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,IAAI;QACvC;QACA,IAAI,gBAAgB,CAAC,QAAS;YAC1B,OAAO,KAAK;YACZ,OAAO,KAAK;QAChB;QACA,IAAI,gBAAgB,CAAC,SAAS;QAC9B,OAAO,gBAAgB,CAAC,cAAe;YACnC,WAAW,aAAa;QAC5B;QACA,IAAI,GAAG,GAAG;IACd;AACJ;AAEA,SAAS,kBAAkB,OAAO,EAAE,IAAI,EAAE,SAAS;IAC/C,MAAM,IAAI,CAAC;IACX,MAAM,IAAI,KAAK,KAAK;IACpB,MAAM,OAAO;QACT,MAAM;YACF,OAAO;YACP,QAAQ;YACR,GAAG,CAAC;YACJ,GAAG;QACP;QACA,MAAM;YACF,GAAG,IAAI,CAAC,QAAQ,GAAG,GAAG,MAAM,EAAE;YAC9B,GAAG,IAAI,KAAK;QAChB;IACJ;IACA,IAAI,eAAe,MAAM;QACrB,KAAK,SAAS,GAAG;YACb,QAAQ,QAAQ,MAAM,CAAC,OAAO,CAAC,WAAW;YAC1C,gBAAgB;YAChB,QAAQ;YACR,OAAO;YACP,GAAG,WAAW,CAAC,IAAI,KAAK,CAAC,IAAI,WAAW,CAAC,IAAI,KAAK,CAAC;QACvD;IACJ;IACA,OAAO;AACX;AAEA,SAAS,eAAe,QAAQ,EAAE,OAAO,EAAE,QAAQ;IAC/C,MAAM,WAAW,CAAC;IAClB,MAAM,OAAO,SAAS,IAAI;IAC1B,MAAM,SAAS,SAAS,MAAM;IAC9B,MAAM,OAAO,kBAAkB,SAAS,MAAM,SAAS,SAAS;IAChE,MAAM,YAAY,CAAA,GAAA,yJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI;IAC/C,SAAS,CAAC,iBAAiB,GAAG;IAC9B,MAAM,WAAW,SAAS,CAAC,GAAG,IAAI,CAAC;QAC/B,OAAO;IACX;IACA,QAAQ,CAAC,gBAAgB,GAAG;IAC5B,IAAI,QAAQ;QACR,QAAQ,CAAC,gBAAgB,GAAG;IAChC;IACA,MAAM,OAAO,SAAS,IAAI;IAC1B,KAAK,IAAI,CAAC,KAAK,IAAI,EAAE,GAAG,CAAC;QACrB,QAAQ;QACR,kBAAkB;IACtB,GAAG,IAAI,CAAC;IACR,KAAK,EAAE,CAAC,uKAAA,CAAA,QAAe,GAAG,WAAY,IAAM,KAAK,IAAI,CAAC;YAClD,MAAM,QAAQ,MAAM,CAAC,KAAK,CAAC,eAAe;QAC9C,IAAK,EAAE,CAAC,uKAAA,CAAA,MAAa,GAAG,WAAY,IAAM,KAAK,IAAI,CAAC;YAChD,MAAM;QACV;IACA,KAAK,MAAM,CAAC;IACZ,MAAM,OAAO,SAAS,IAAI,CAAC,SAAS,IAAI,EAAE,GAAG,CAAC,WAAW,IAAI,CAAC,KAAK,IAAI,EAAE,MAAM,CAAC;IAChF,IAAI,eAAe,MAAM;QACrB,SAAS,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC,KAAK,SAAS,EAAE,MAAM,CAAC;IAC5D;IACA,OAAO;QACH,GAAG;QACH,MAAM;QACN,YAAY,IAAM,KAAK,IAAI,CAAC;gBACxB,MAAM;YACV;QACA,aAAa;YACT,MAAM,WAAW,KAAK,OAAO;YAC7B,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,SAAS,CAAC,GAAG,CAAC,QAAQ,GAAG,GAAG,SAAS,KAAK,GAAG,CAAC;QAC1E;IACJ;AACJ;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,QAAQ,EAAE;IACd,IAAI,QAAQ,eAAe,EAAE;QACzB,MAAM,IAAI,CAAC,eAAe,UAAU,SAAS;YACzC,MAAM;YACN,MAAM,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YACjC,WAAW,MAAM,MAAM;QAC3B;IACJ;IACA,QAAQ,QAAQ,OAAO,CAAC,MAAM,CAAE,CAAC,GAAG;QAChC,EAAE,IAAI,CAAC,eAAe,UAAU,SAAS;YACrC,MAAM;YACN,MAAM,8KAAA,CAAA,UAAmB,CAAC,YAAY,CAAC,8BAA8B;YACrE,QAAQ;YACR,WAAW,EAAE,MAAM;QACvB;QACA,OAAO;IACX,GAAI;IACJ,OAAO;AACX;AAEA,SAAS,6BAA6B,MAAM;IACxC,MAAM,eAAe,gBAAgB,IAAI,CAAC;IAC1C,OAAO,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,YAAY,CAAC,EAAE;AACtF;AACO,MAAM,mBAAmB,SAAS,MAAM,EAAE,OAAO;IACpD,QAAQ,MAAM,GAAG,eAAe,QAAQ,MAAM,KAAK;IACnD,QAAQ,QAAQ,GAAG,QAAQ,QAAQ,IAAI;IACvC,QAAQ,eAAe,GAAG,QAAQ,WAAW;IAC7C,QAAQ,cAAc,GAAG,QAAQ,UAAU;IAC3C,QAAQ,gBAAgB,GAAG,QAAQ,YAAY;IAC/C,QAAQ,MAAM,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,IAAI,QAAQ,MAAM,GAAG;IAC9D,QAAQ,eAAe,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,eAAe,IAAI,QAAQ,eAAe,GAAG,6BAA6B,WAAW,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,IAAI,eAAe;IAC3J,CAAA,GAAA,6JAAA,CAAA,SAAO,AAAD,EAAE,QAAQ,SAAS,eAAe,QAAQ,MAAM;AAC1D;AACO,MAAM,YAAY,CAAA,UAAW,eAAe,SAAS,IAAI,CAAC,SAAS;AACnE,MAAM,gBAAgB,SAAS,OAAO,EAAE,OAAO;IAClD,UAAU,WAAW,CAAC;IACtB,MAAM,aAAa,eAAe,SAAS;QACvC,YAAY,QAAQ,UAAU;QAC9B,mBAAmB,QAAQ,iBAAiB;QAC5C,qBAAqB,QAAQ,mBAAmB;IACpD;IACA,QAAQ,KAAK,GAAG,WAAW,KAAK;IAChC,QAAQ,MAAM,GAAG,WAAW,MAAM;IAClC,iBAAiB,WAAW,IAAI,EAAE;AACtC;AACO,IAAI,iBAAiB,SAAS,OAAO;IACxC,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;IAChF,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU;QACzB,UAAU;YACN;gBAAC;aAAQ;SACZ;IACL,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO,CAAC,EAAE,GAAG;QACnC,UAAU,QAAQ,GAAG,CAAE,CAAA,OAAQ;gBAAC;aAAK;IACzC;IACA,MAAM,cAAc,CAAC,QAAQ,UAAU;IACvC,MAAM,cAAc,QAAQ,MAAM,CAAE,CAAC,GAAG,KAAK;QACzC,MAAM,UAAU,IAAI,MAAM,CAAE,CAAC,GAAG,MAAM;YAClC,MAAM,OAAO,KAAK,OAAO;YACzB,MAAM,kBAAkB,KAAK,MAAM,CAAC,sBAAsB,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,MAAM,CAAC,UAAU,eAAe;YACxG,MAAM,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,KAAK,OAAO,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,GAAG,SAAS,CAAC;YAC5D,mBAAmB,CAAC,MAAM,EAAE,gBAAgB,CAAC,OAAO,CAAC,oBAAoB,EAAE,gBAAgB,CAAC,IAAI,CAAC;YACjG,EAAE,OAAO,GAAG,EAAE,KAAK;YACnB,EAAE,KAAK,IAAI,KAAK,KAAK;YACrB,EAAE,MAAM,GAAG,KAAK,GAAG,CAAC,EAAE,MAAM,EAAE,KAAK,MAAM;YACzC,EAAE,SAAS,GAAG,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,KAAK,KAAK;YAC9C,EAAE,KAAK,CAAC,IAAI,CAAC;gBACT,MAAM;gBACN,OAAO,KAAK,KAAK;gBACjB,QAAQ,KAAK,MAAM;gBACnB,GAAG;gBACH,GAAG;gBACH,SAAS,EAAE,OAAO;YACtB;YACA,OAAO;QACX,GAAI;YACA,OAAO,EAAE;YACT,QAAQ;YACR,WAAW;YACX,SAAS;YACT,OAAO;YACP,kBAAkB,EAAE,gBAAgB;QACxC;QACA,EAAE,UAAU,CAAC,IAAI,CAAC,EAAE,WAAW;QAC/B,EAAE,UAAU,CAAC,IAAI,CAAC,QAAQ,MAAM;QAChC,EAAE,WAAW,IAAI,QAAQ,MAAM;QAC/B,EAAE,KAAK,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,QAAQ,KAAK;QACtC,EAAE,SAAS,GAAG,KAAK,GAAG,CAAC,EAAE,SAAS,EAAE,QAAQ,SAAS;QACrD,EAAE,UAAU,GAAG,KAAK,GAAG,CAAC,EAAE,UAAU,EAAE,QAAQ,KAAK,CAAC,MAAM;QAC1D,EAAE,UAAU,GAAG,cAAc,KAAK,GAAG,CAAC,EAAE,UAAU,EAAE,QAAQ,KAAK,IAAI,EAAE,UAAU,GAAG,EAAE,SAAS;QAC/F,OAAO;IACX,GAAI;QACA,OAAO,EAAE;QACT,YAAY,EAAE;QACd,YAAY,EAAE;QACd,WAAW;QACX,aAAa;QACb,YAAY;QACZ,YAAY;QACZ,kBAAkB,EAAE;IACxB;IACA,MAAM,kBAAkB,GAAG,MAAI,YAAY,gBAAgB,CAAC,MAAM,GAAC,YAAY,gBAAgB,CAAC,EAAE,GAAC,IAAI;IACvG,MAAM,EACF,YAAY,UAAU,EACtB,aAAa,WAAW,EAC3B,GAAG;IACJ,MAAM,cAAc,mBAAmB,YAAY,aAAa,iBAAiB;QAC7E,SAAS;QACT,aAAa;QACb,aAAa;IACjB;IACA,OAAO;QACH,MAAM;QACN,OAAO;QACP,QAAQ;IACZ;AACJ;AAEA,SAAS,mBAAmB,KAAK,EAAE,MAAM,EAAE,eAAe,EAAE,IAAI;IAC5D,IAAI,EACA,aAAa,WAAW,EACxB,SAAS,OAAO,EAChB,aAAa,WAAW,EAC3B,GAAG;IACJ,MAAM,kBAAkB,mBAAmB,OAAO,QAAQ;IAC1D,MAAM,aAAa,CAAA;QACf,MAAM,QAAQ,QAAQ,iBAAiB;QACvC,MAAM,KAAK,YAAY,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,KAAK,MAAM;QACvD,OAAO,YAAY,UAAU,CAAC,KAAK,CAAC,CAAC,GAAG,CAAC,aAAa,QAAQ,KAAK,aAAa,QAAQ,KAAK,IAAI,CAAC;IACtG;IACA,MAAM,aAAa,CAAA;QACf,IAAI,aAAa;YACb,OAAO,KAAK,OAAO;QACvB;QACA,MAAM,QAAQ,QAAQ,mBAAmB;QACzC,MAAM,WAAW,YAAY,SAAS;QACtC,MAAM,KAAK,WAAW,KAAK,KAAK;QAChC,OAAO,KAAK,CAAC,GAAG,WAAW,CAAC,YAAY,QAAQ,KAAK,aAAa,QAAQ,KAAK,IAAI,CAAC;IACxF;IACA,YAAY,KAAK,CAAC,OAAO,CAAE,CAAA;QACvB,MAAM,YAAY,gBAAgB,WAAW;QAC7C,UAAU,IAAI,CAAC;YACX,YAAY,WAAW;YACvB,YAAY,WAAW;QAC3B;QACA,UAAU,OAAO,CAAC,WAAW,CAAC,KAAK,IAAI;QACvC,UAAU,MAAM;IACpB;IACA,MAAM,SAAS,gBAAgB,cAAc;IAC7C,gBAAgB,OAAO;IACvB,OAAO;AACX;AACO,IAAI,aAAa,SAAS,MAAM;IACnC,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,OAAO,QAAQ;IACjD,IAAI,CAAC,iBAAiB,GAAG,OAAO,gBAAgB;IAChD,IAAI,CAAC,SAAS,GAAG,OAAO,QAAQ;IAChC,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK;IAC1B,IAAI,CAAC,OAAO,GAAG,SAAS,YAAY,CAAC,QAAQ,QAAQ,QAAQ,QAAQ,GAAG,GAAG;IAC3E,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACd,SAAS;IACb;IACA,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;QAC5B,OAAO;QACP,CAAC,4KAAA,CAAA,oBAAiB,CAAC,EAAE;IACzB,GAAG,MAAM,CAAC,SAAS,IAAI,EAAE;QACrB,MAAM;QACN,OAAO;IACX;IACA,IAAI,CAAC,YAAY,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;QAClC,OAAO;IACX,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM;IACrB,IAAI,CAAC,UAAU,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;QAChC,OAAO;IACX,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM;IACrB,IAAI,CAAC,QAAQ,GAAG,SAAS,IAAI,CAAC,CAAC,IAAI,IAAI,KAAK;IAC5C,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACf,gBAAgB;QAChB,QAAQ;QACR,IAAI;QACJ,IAAI;QACJ,QAAQ,IAAI,CAAC,OAAO,CAAC,EAAE;IAC3B;IACA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;QACf,uBAAuB;IAC3B;IACA,IAAI,CAAC,YAAY,GAAG;IACpB,IAAI,CAAC,gBAAgB;AACzB;AACA,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,WAAW,SAAS,EAAE;IACzB;QACI,IAAI,IAAI,CAAC,kBAAkB,EAAE;YACzB,OAAO;gBACH,OAAO;gBACP,QAAQ;gBACR,SAAS;gBACT,eAAe;YACnB;QACJ;QACA,MAAM,OAAO,IAAI,CAAC,YAAY,CAAC,OAAO;QACtC,KAAK,OAAO,GAAG;QACf,KAAK,aAAa,GAAG;QACrB,KAAK,MAAM,IAAI;QACf,KAAK,QAAQ,GAAG;YACZ,UAAU;YACV,YAAY;QAChB;QACA,KAAK,iBAAiB,GAAG;QACzB,KAAK,mBAAmB,GAAG;QAC3B,OAAO;IACX;IACA,OAAM,CAAC,EAAE,CAAC;QACN,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;QACjD;IACJ;IACA,MAAK,KAAK,EAAE,MAAM,EAAE,MAAM;QACtB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,QAAQ,KAAK,IAAI,IAAI,OAAO,IAAI,EAAE,KAAK,KAAK,CAAC,SAAS,IAAI;QAC3E,MAAM,gBAAgB,IAAI,CAAC,gBAAgB;QAC3C,IAAI,cAAc,KAAK,GAAG,SAAS,cAAc,MAAM,GAAG,QAAQ;YAC9D,IAAI,CAAC,SAAS;QAClB;QACA,OAAO,IAAI;IACf;IACA;QACI,IAAI,CAAC,MAAM,CAAC,UAAU;IAC1B;IACA;QACI,IAAI,CAAC,MAAM,CAAC,UAAU;IAC1B;IACA,YAAW,OAAO;QACd,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,QAAQ,OAAO,EAAE;YACjB,QAAQ,OAAO,GAAG,QAAQ,OAAO,CAAC,MAAM,CAAE,CAAC,GAAG;gBAC1C,SAAS,eAAe,QAAQ,IAAI,CAAC,iBAAiB,EAAE,IAAI,CAAC,YAAY;gBACzE,UAAU,EAAE,IAAI,CAAC;gBACjB,OAAO;YACX,GAAI,EAAE;QACV,OAAO;YACH,QAAQ,OAAO,GAAG,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,KAAK;QACvD;QACA,QAAQ,eAAe,GAAG,KAAK,MAAM,QAAQ,eAAe,GAAG,OAAO,QAAQ,eAAe;QAC7F,IAAI,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,MAAM,IAAI,QAAQ,eAAe,GAAG;YACxE,IAAI,CAAC,IAAI;YACT,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,WAAW;YAChB,IAAI,CAAC,SAAS;QAClB,OAAO;YACH,IAAI,CAAC,IAAI;QACb;IACJ;IACA;QACI,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,OAAO;QAChC,IAAI,CAAC,MAAM,CAAC,OAAO;QACnB,IAAI,CAAC,OAAO,CAAC,OAAO;IACxB;IACA;QACI,OAAO,IAAI,CAAC,QAAQ,CAAC,OAAO,IAAI;YAC5B,qBAAqB;YACrB,mBAAmB;YACnB,MAAM;QACV;IACJ;IACA;QACI,IAAI,CAAC,UAAU;QACf,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM;QAC1C,OAAO;YAAC,KAAK,OAAO,IAAI,GAAG,OAAO,KAAK;YAAE,KAAK,OAAO,GAAG,GAAG,OAAO,MAAM;SAAC;IAC7E;IACA,MAAK,IAAI;QACL,MAAM,SAAS,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM;QAC1C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACb,YAAY,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI;YAC7C,YAAY,KAAK,KAAK,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,GAAG;QAChD;IACJ;IACA;QACI,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,IAAI;IACb;IACA;QACI,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,IAAI;IACb;IACA;QACI,IAAI,CAAC,UAAU,CAAC,MAAM;QACtB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,UAAU;IACpD;IACA;QACI,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;QAClC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,WAAW;IACrD;IACA,iBAAgB,KAAK;QACjB,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM;QACzC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,QAAQ,MAAM,WAAW;YACzB,MAAM,MAAM,eAAe;QAC/B;QACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACZ,MAAM,MAAM,KAAK;QACrB;IACJ;IACA;QACI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,CAAC,yKAAA,CAAA,UAAa,CAAC,EAAE,GAAG,WAAY,CAAA;YAClD,MAAM,cAAc,EAAE,MAAM,CAAC,gBAAgB;YAC7C,IAAI,CAAC,aAAa;gBACd,IAAI,IAAI,CAAC,OAAO,EAAE;oBACd,IAAI,CAAC,SAAS;gBAClB;gBACA;YACJ;YACA,IAAI,aAAa,aAAa;gBAC1B,IAAI,IAAI,CAAC,UAAU,EAAE;oBACjB,IAAI,CAAC,eAAe,CAAC;oBACrB,IAAI,CAAC,SAAS;gBAClB,OAAO;oBACH,IAAI,CAAC,eAAe,CAAC;oBACrB,IAAI,CAAC,SAAS;gBAClB;YACJ,OAAO,IAAI,eAAe,aAAa;gBACnC,IAAI,CAAC,MAAM;gBACX,IAAI,CAAC,SAAS;YAClB,OAAO,IAAI,gBAAgB,aAAa;gBACpC,IAAI,CAAC,SAAS,CAAC,EAAE,MAAM,CAAC,gBAAgB;gBACxC,IAAI,CAAC,SAAS;YAClB;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,gBAAiB,CAAA,IAAK,EAAE,eAAe;QAC1D,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,yKAAA,CAAA,UAAa,CAAC,KAAK,EAAG,IAAM,IAAI,CAAC,eAAe,CAAC;QACtE,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,yKAAA,CAAA,UAAa,CAAC,KAAK,EAAG,IAAM,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,GAAG,UAAU;QAClG,IAAI,CAAC,YAAY,CAAC,EAAE,CAAC,yKAAA,CAAA,UAAa,CAAC,IAAI,GAAG,WAAY,IAAM,IAAI,CAAC,eAAe,CAAC;IACrF;IACA;QACI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;QACxB,IAAI,CAAC,UAAU,CAAC,GAAG;QACnB,IAAI,CAAC,YAAY,CAAC,GAAG;IACzB;IACA;QACI,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,aAAa;YACf,uBAAuB;QAC3B;QACA,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACf,IAAI,CAAC,OAAO,GAAG,SAAS,IAAI,CAAC,GAAG,GAAG,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,YAAY;YACnE,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;gBACd,IAAI;gBACJ,IAAI;gBACJ,MAAM,QAAQ,MAAM,CAAC,OAAO,CAAC,eAAe;gBAC5C,QAAQ,QAAQ,MAAM,CAAC,OAAO,CAAC,WAAW;gBAC1C,gBAAgB;gBAChB,QAAQ;YACZ;YACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YAClB,IAAI,CAAC,KAAK,GAAG,SAAS,IAAI,CAAC,aAAa,MAAM,CAAC,IAAI,CAAC,YAAY;YAChE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;gBACZ,MAAM,QAAQ,MAAM,CAAC,OAAO,CAAC,KAAK;gBAClC,QAAQ;YACZ;YACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YAChB,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;QAC1D;IACJ;IACA;QACI,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,gBAAgB,QAAQ,MAAM,CAAC,OAAO;QAC5C,MAAM,YAAY,IAAI,CAAC,UAAU;QACjC,MAAM,QAAQ,gBAAgB,IAAI,CAAC,SAAS,EAAE;QAC9C,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,OAAO,QAAQ,WAAW;QAC9B;QACA,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACf,QAAQ,KAAK,MAAM,MAAM,GAAG;YAC5B,MAAM,cAAc,eAAe;YACnC,QAAQ,cAAc,WAAW;QACrC;QACA,UAAU,KAAK;QACf,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC;QACrB,MAAM,OAAO,CAAE,CAAA,OAAQ,KAAK,CAAC,CAAC,MAAM,CAAC;QACrC,IAAI,CAAC,UAAU,GAAG;IACtB;AACJ;AAEA,SAAS,iBAAiB,MAAM,EAAE,aAAa,EAAE,QAAQ,EAAE,MAAM;IAC7D,IAAI,UAAU,cAAc,MAAM,EAAE;QAChC,SAAS,eAAe,UAAU,cAAc,MAAM,EAAE,OAAO,iBAAiB;IACpF;IACA,MAAM,EACF,OAAO,KAAK,EACZ,QAAQ,MAAM,EACjB,GAAG,OAAO,OAAO;IAClB,OAAO;QACH,QAAQ,UAAU;QAClB,UAAU,YAAY,cAAc,QAAQ,IAAI;QAChD,iBAAiB,cAAc,eAAe;QAC9C,OAAO;QACP,QAAQ;QACR,QAAQ,cAAc,MAAM;QAC5B,aAAa,cAAc,WAAW;QACtC,iBAAiB,OAAO,qBAAqB,CAAC,eAAe;YACzD,mBAAmB;gBAAC;aAAW;QACnC;QACA,gBAAgB,OAAO,qBAAqB,CAAC,cAAc;YACvD,mBAAmB;gBAAC;aAAW;QACnC;QACA,kBAAkB,OAAO,qBAAqB,CAAC,gBAAgB;YAC3D,mBAAmB;gBAAC;aAAW;QACnC;IACJ;AACJ;AACO,MAAM,SAAS;IAClB,MAAM;IACN;QACI,IAAI,CAAC,WAAW,GAAG,IAAI,WAAW;YAC9B,UAAU,IAAI,CAAC,SAAS;YACxB,kBAAkB,IAAI,CAAC,iBAAiB;YACxC,OAAO,IAAM,IAAI,CAAC,KAAK;YACvB,UAAU,CAAA,SAAU,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG;QAC9C;QACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW;IACrC;IACA;QACI,IAAI,CAAC,WAAW,CAAC,OAAO;IAC5B;IACA,SAAS;QACL;YACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;gBACzC,KAAK,IAAI,CAAC,UAAU,CAAC,cAAc;YACvC;QACJ;QACA;YACI,MAAM,qBAAqB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;YACpD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;gBACrB,kBAAkB;YACtB;YACA,OAAO;QACX;QACA,UAAS,QAAQ,EAAE,MAAM;YACrB,MAAM,OAAO,IAAI,CAAC,WAAW;YAC7B,MAAM,UAAU,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC,GAAG,UAAU;YAClF,QAAQ,KAAK,IAAI;YACjB,MAAM,qBAAqB,IAAI,CAAC,qBAAqB;YACrD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,eAAe,QAAQ,MAAM,GAAG,IAAI,CAAC,gLAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAE;gBACrH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;oBACrB,kBAAkB;gBACtB;YACJ;YACA,QAAQ,KAAK,IAAI;YACjB,OAAO;QACX;QACA;YACI,MAAM,OAAO,IAAI,CAAC,WAAW;YAC7B,MAAM,UAAU,iBAAiB,IAAI,EAAE,IAAI,CAAC,UAAU,CAAC,aAAa,CAAC;YACrE,QAAQ,eAAe,GAAG;YAC1B,QAAQ,cAAc,GAAG;YACzB,QAAQ,MAAM,GAAG;YACjB,QAAQ,MAAM,GAAG;YACjB,QAAQ,SAAS,GAAG;YACpB,QAAQ,gBAAgB,GAAG,CAAA;gBACvB,MAAM,CAAC,sBAAsB,EAAE,UAAU,IAAI,EAAE,EAAE;oBAC7C,OAAO,QAAQ,KAAK;oBACpB,QAAQ,QAAQ,MAAM;gBAC1B;gBACA,UAAU,MAAM,GAAG;YACvB;YACA,MAAM,qBAAqB,IAAI,CAAC,qBAAqB;YACrD,QAAQ,KAAK,IAAI;YACjB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,SAAS,eAAe,QAAQ,MAAM,GAAG,IAAI,CAAC,gLAAA,CAAA,SAAM,CAAC,KAAK,EAAE,MAAM,CAAE;gBACrH,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC;oBACrB,kBAAkB;gBACtB;YACJ;YACA,QAAQ,KAAK,IAAI;YACjB,OAAO;QACX;IACJ;IACA,WAAU,WAAW;QACjB,MAAM,QAAQ,YAAY,SAAS;QACnC,YAAY,SAAS,CAAC;YAClB,MAAM;YACN;gBACI,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,IAAI,CAAC,qBAAqB;gBACtD,IAAI,CAAC,OAAO,CAAC;oBAAC;iBAAS;YAC3B;YACA,kBAAkB;YAClB,gBAAgB;YAChB,QAAQ;QACZ;QACA,MAAM,iBAAiB,CAAC,WAAW,GAAG;QACtC,MAAM,iBAAiB,CAAC,UAAU,GAAG;QACrC,MAAM,iBAAiB,CAAC,YAAY,GAAG;IAC3C;IACA,YAAY;QAAC;KAAc;AAC/B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6329, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/loading_indicator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/loading_indicator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    patchFontOptions as _patchFontOptions\r\n} from \"./utils\";\r\nconst STATE_HIDDEN = 0;\r\nconst STATE_SHOWN = 1;\r\nconst ANIMATION_EASING = \"linear\";\r\nconst ANIMATION_DURATION = 400;\r\nconst LOADING_INDICATOR_READY = \"loadingIndicatorReady\";\r\nexport let LoadingIndicator = function(parameters) {\r\n    const renderer = parameters.renderer;\r\n    this._group = renderer.g().attr({\r\n        class: \"dx-loading-indicator\"\r\n    }).linkOn(renderer.root, {\r\n        name: \"loading-indicator\",\r\n        after: \"peripheral\"\r\n    });\r\n    this._rect = renderer.rect().attr({\r\n        opacity: 0\r\n    }).append(this._group);\r\n    this._text = renderer.text().attr({\r\n        align: \"center\"\r\n    }).append(this._group);\r\n    this._createStates(parameters.eventTrigger, this._group, renderer.root, parameters.notify)\r\n};\r\nLoadingIndicator.prototype = {\r\n    constructor: LoadingIndicator,\r\n    _createStates: function(eventTrigger, group, root, notify) {\r\n        this._states = [{\r\n            opacity: 0,\r\n            start: function() {\r\n                notify(false)\r\n            },\r\n            complete: function() {\r\n                group.linkRemove();\r\n                root.css({\r\n                    \"pointer-events\": \"\"\r\n                });\r\n                eventTrigger(\"loadingIndicatorReady\")\r\n            }\r\n        }, {\r\n            opacity: .85,\r\n            start: function() {\r\n                group.linkAppend();\r\n                root.css({\r\n                    \"pointer-events\": \"none\"\r\n                });\r\n                notify(true)\r\n            },\r\n            complete: function() {\r\n                eventTrigger(\"loadingIndicatorReady\")\r\n            }\r\n        }];\r\n        this._state = 0\r\n    },\r\n    setSize: function(size) {\r\n        const width = size.width;\r\n        const height = size.height;\r\n        this._rect.attr({\r\n            width: width,\r\n            height: height\r\n        });\r\n        this._text.attr({\r\n            x: width / 2,\r\n            y: height / 2\r\n        })\r\n    },\r\n    setOptions: function(options) {\r\n        this._rect.attr({\r\n            fill: options.backgroundColor\r\n        });\r\n        this._text.css(_patchFontOptions(options.font)).attr({\r\n            text: options.text,\r\n            class: options.cssClass\r\n        });\r\n        this[options.show ? \"show\" : \"hide\"]()\r\n    },\r\n    dispose: function() {\r\n        this._group.linkRemove().linkOff();\r\n        this._group = this._rect = this._text = this._states = null\r\n    },\r\n    _transit: function(stateId) {\r\n        const that = this;\r\n        let state;\r\n        if (that._state !== stateId) {\r\n            that._state = stateId;\r\n            that._isHiding = false;\r\n            state = that._states[stateId];\r\n            that._rect.stopAnimation().animate({\r\n                opacity: state.opacity\r\n            }, {\r\n                complete: state.complete,\r\n                easing: \"linear\",\r\n                duration: 400,\r\n                unstoppable: true\r\n            });\r\n            that._noHiding = true;\r\n            state.start();\r\n            that._noHiding = false\r\n        }\r\n    },\r\n    show: function() {\r\n        this._transit(1)\r\n    },\r\n    hide: function() {\r\n        this._transit(0)\r\n    },\r\n    scheduleHiding: function() {\r\n        if (!this._noHiding) {\r\n            this._isHiding = true\r\n        }\r\n    },\r\n    fulfillHiding: function() {\r\n        if (this._isHiding) {\r\n            this.hide()\r\n        }\r\n    }\r\n};\r\nexport const plugin = {\r\n    name: \"loading_indicator\",\r\n    init: function() {\r\n        const that = this;\r\n        that._loadingIndicator = new LoadingIndicator({\r\n            eventTrigger: that._eventTrigger,\r\n            renderer: that._renderer,\r\n            notify: function(state) {\r\n                that._skipLoadingIndicatorOptions = true;\r\n                that.option(\"loadingIndicator\", {\r\n                    show: state\r\n                });\r\n                that._skipLoadingIndicatorOptions = false;\r\n                if (state) {\r\n                    that._stopCurrentHandling()\r\n                }\r\n            }\r\n        });\r\n        that._scheduleLoadingIndicatorHiding()\r\n    },\r\n    dispose: function() {\r\n        this._loadingIndicator.dispose();\r\n        this._loadingIndicator = null\r\n    },\r\n    members: {\r\n        _scheduleLoadingIndicatorHiding: function() {\r\n            this._loadingIndicator.scheduleHiding()\r\n        },\r\n        _fulfillLoadingIndicatorHiding: function() {\r\n            this._loadingIndicator.fulfillHiding()\r\n        },\r\n        showLoadingIndicator: function() {\r\n            this._loadingIndicator.show()\r\n        },\r\n        hideLoadingIndicator: function() {\r\n            this._loadingIndicator.hide()\r\n        },\r\n        _onBeginUpdate: function() {\r\n            if (!this._optionChangedLocker) {\r\n                this._scheduleLoadingIndicatorHiding()\r\n            }\r\n        }\r\n    },\r\n    extenders: {\r\n        _dataSourceLoadingChangedHandler(isLoading) {\r\n            if (isLoading && (this._options.silent(\"loadingIndicator\") || {}).enabled) {\r\n                this._loadingIndicator.show()\r\n            }\r\n        },\r\n        _setContentSize() {\r\n            this._loadingIndicator.setSize(this._canvas)\r\n        },\r\n        endUpdate() {\r\n            if (this._initialized && this._dataIsReady()) {\r\n                this._fulfillLoadingIndicatorHiding()\r\n            }\r\n        }\r\n    },\r\n    customize: function(constructor) {\r\n        const proto = constructor.prototype;\r\n        if (proto._dataSourceChangedHandler) {\r\n            const _dataSourceChangedHandler = proto._dataSourceChangedHandler;\r\n            proto._dataSourceChangedHandler = function() {\r\n                this._scheduleLoadingIndicatorHiding();\r\n                _dataSourceChangedHandler.apply(this, arguments)\r\n            }\r\n        }\r\n        constructor.addChange({\r\n            code: \"LOADING_INDICATOR\",\r\n            handler: function() {\r\n                if (!this._skipLoadingIndicatorOptions) {\r\n                    this._loadingIndicator.setOptions(this._getOption(\"loadingIndicator\"))\r\n                }\r\n                this._scheduleLoadingIndicatorHiding()\r\n            },\r\n            isThemeDependent: true,\r\n            option: \"loadingIndicator\",\r\n            isOptionChange: true\r\n        });\r\n        proto._eventsMap.onLoadingIndicatorReady = {\r\n            name: \"loadingIndicatorReady\"\r\n        };\r\n        const _drawn = proto._drawn;\r\n        proto._drawn = function() {\r\n            _drawn.apply(this, arguments);\r\n            if (this._dataIsReady()) {\r\n                this._fulfillLoadingIndicatorHiding()\r\n            }\r\n        }\r\n    },\r\n    fontFields: [\"loadingIndicator.font\"]\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;;AAGA,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,qBAAqB;AAC3B,MAAM,0BAA0B;AACzB,IAAI,mBAAmB,SAAS,UAAU;IAC7C,MAAM,WAAW,WAAW,QAAQ;IACpC,IAAI,CAAC,MAAM,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC;QAC5B,OAAO;IACX,GAAG,MAAM,CAAC,SAAS,IAAI,EAAE;QACrB,MAAM;QACN,OAAO;IACX;IACA,IAAI,CAAC,KAAK,GAAG,SAAS,IAAI,GAAG,IAAI,CAAC;QAC9B,SAAS;IACb,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM;IACrB,IAAI,CAAC,KAAK,GAAG,SAAS,IAAI,GAAG,IAAI,CAAC;QAC9B,OAAO;IACX,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM;IACrB,IAAI,CAAC,aAAa,CAAC,WAAW,YAAY,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,IAAI,EAAE,WAAW,MAAM;AAC7F;AACA,iBAAiB,SAAS,GAAG;IACzB,aAAa;IACb,eAAe,SAAS,YAAY,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM;QACrD,IAAI,CAAC,OAAO,GAAG;YAAC;gBACZ,SAAS;gBACT,OAAO;oBACH,OAAO;gBACX;gBACA,UAAU;oBACN,MAAM,UAAU;oBAChB,KAAK,GAAG,CAAC;wBACL,kBAAkB;oBACtB;oBACA,aAAa;gBACjB;YACJ;YAAG;gBACC,SAAS;gBACT,OAAO;oBACH,MAAM,UAAU;oBAChB,KAAK,GAAG,CAAC;wBACL,kBAAkB;oBACtB;oBACA,OAAO;gBACX;gBACA,UAAU;oBACN,aAAa;gBACjB;YACJ;SAAE;QACF,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,SAAS,SAAS,IAAI;QAClB,MAAM,QAAQ,KAAK,KAAK;QACxB,MAAM,SAAS,KAAK,MAAM;QAC1B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACZ,OAAO;YACP,QAAQ;QACZ;QACA,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACZ,GAAG,QAAQ;YACX,GAAG,SAAS;QAChB;IACJ;IACA,YAAY,SAAS,OAAO;QACxB,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC;YACZ,MAAM,QAAQ,eAAe;QACjC;QACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAA,GAAA,yJAAA,CAAA,mBAAiB,AAAD,EAAE,QAAQ,IAAI,GAAG,IAAI,CAAC;YACjD,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,QAAQ;QAC3B;QACA,IAAI,CAAC,QAAQ,IAAI,GAAG,SAAS,OAAO;IACxC;IACA,SAAS;QACL,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,OAAO;QAChC,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG;IAC3D;IACA,UAAU,SAAS,OAAO;QACtB,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI,KAAK,MAAM,KAAK,SAAS;YACzB,KAAK,MAAM,GAAG;YACd,KAAK,SAAS,GAAG;YACjB,QAAQ,KAAK,OAAO,CAAC,QAAQ;YAC7B,KAAK,KAAK,CAAC,aAAa,GAAG,OAAO,CAAC;gBAC/B,SAAS,MAAM,OAAO;YAC1B,GAAG;gBACC,UAAU,MAAM,QAAQ;gBACxB,QAAQ;gBACR,UAAU;gBACV,aAAa;YACjB;YACA,KAAK,SAAS,GAAG;YACjB,MAAM,KAAK;YACX,KAAK,SAAS,GAAG;QACrB;IACJ;IACA,MAAM;QACF,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA,MAAM;QACF,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA,gBAAgB;QACZ,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,IAAI,CAAC,SAAS,GAAG;QACrB;IACJ;IACA,eAAe;QACX,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAI,CAAC,IAAI;QACb;IACJ;AACJ;AACO,MAAM,SAAS;IAClB,MAAM;IACN,MAAM;QACF,MAAM,OAAO,IAAI;QACjB,KAAK,iBAAiB,GAAG,IAAI,iBAAiB;YAC1C,cAAc,KAAK,aAAa;YAChC,UAAU,KAAK,SAAS;YACxB,QAAQ,SAAS,KAAK;gBAClB,KAAK,4BAA4B,GAAG;gBACpC,KAAK,MAAM,CAAC,oBAAoB;oBAC5B,MAAM;gBACV;gBACA,KAAK,4BAA4B,GAAG;gBACpC,IAAI,OAAO;oBACP,KAAK,oBAAoB;gBAC7B;YACJ;QACJ;QACA,KAAK,+BAA+B;IACxC;IACA,SAAS;QACL,IAAI,CAAC,iBAAiB,CAAC,OAAO;QAC9B,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,SAAS;QACL,iCAAiC;YAC7B,IAAI,CAAC,iBAAiB,CAAC,cAAc;QACzC;QACA,gCAAgC;YAC5B,IAAI,CAAC,iBAAiB,CAAC,aAAa;QACxC;QACA,sBAAsB;YAClB,IAAI,CAAC,iBAAiB,CAAC,IAAI;QAC/B;QACA,sBAAsB;YAClB,IAAI,CAAC,iBAAiB,CAAC,IAAI;QAC/B;QACA,gBAAgB;YACZ,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;gBAC5B,IAAI,CAAC,+BAA+B;YACxC;QACJ;IACJ;IACA,WAAW;QACP,kCAAiC,SAAS;YACtC,IAAI,aAAa,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,uBAAuB,CAAC,CAAC,EAAE,OAAO,EAAE;gBACvE,IAAI,CAAC,iBAAiB,CAAC,IAAI;YAC/B;QACJ;QACA;YACI,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO;QAC/C;QACA;YACI,IAAI,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,IAAI;gBAC1C,IAAI,CAAC,8BAA8B;YACvC;QACJ;IACJ;IACA,WAAW,SAAS,WAAW;QAC3B,MAAM,QAAQ,YAAY,SAAS;QACnC,IAAI,MAAM,yBAAyB,EAAE;YACjC,MAAM,4BAA4B,MAAM,yBAAyB;YACjE,MAAM,yBAAyB,GAAG;gBAC9B,IAAI,CAAC,+BAA+B;gBACpC,0BAA0B,KAAK,CAAC,IAAI,EAAE;YAC1C;QACJ;QACA,YAAY,SAAS,CAAC;YAClB,MAAM;YACN,SAAS;gBACL,IAAI,CAAC,IAAI,CAAC,4BAA4B,EAAE;oBACpC,IAAI,CAAC,iBAAiB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC;gBACtD;gBACA,IAAI,CAAC,+BAA+B;YACxC;YACA,kBAAkB;YAClB,QAAQ;YACR,gBAAgB;QACpB;QACA,MAAM,UAAU,CAAC,uBAAuB,GAAG;YACvC,MAAM;QACV;QACA,MAAM,SAAS,MAAM,MAAM;QAC3B,MAAM,MAAM,GAAG;YACX,OAAO,KAAK,CAAC,IAAI,EAAE;YACnB,IAAI,IAAI,CAAC,YAAY,IAAI;gBACrB,IAAI,CAAC,8BAA8B;YACvC;QACJ;IACJ;IACA,YAAY;QAAC;KAAwB;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6557, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/errors_warnings.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/errors_warnings.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport errorUtils from \"../../core/utils/error\";\r\nimport errors from \"../../core/errors\";\r\nexport default errorUtils(errors.ERROR_MESSAGES, {\r\n    E2001: \"Invalid data source\",\r\n    E2002: \"Axis type and data type are incompatible\",\r\n    E2003: 'The \"{0}\" data source field contains data of unsupported type',\r\n    E2004: 'The \"{0}\" data source field is inconsistent',\r\n    E2005: 'The value field \"{0}\" is absent in the data source or all its values are negative',\r\n    E2006: \"A cycle is detected in provided data\",\r\n    E2007: 'The value field \"{0}\" is absent in the data source',\r\n    E2008: 'The value field \"{0}\" must be a string',\r\n    E2009: 'The value field \"{0}\" must be a positive numeric value',\r\n    E2101: \"Unknown series type: {0}\",\r\n    E2102: \"Ambiguity occurred between two value axes with the same name\",\r\n    E2103: 'The \"{0}\" option is given an invalid value. Assign a function instead',\r\n    E2104: \"Invalid logarithm base\",\r\n    E2105: 'Invalid value of a \"{0}\"',\r\n    E2202: \"Invalid {0} scale value\",\r\n    E2203: \"The range you are trying to set is invalid\",\r\n    W2002: \"The {0} series cannot be drawn because the {1} data field is missing\",\r\n    W2003: \"Tick interval is too small\",\r\n    W2101: 'The \"{0}\" pane does not exist; the last pane is used by default',\r\n    W2102: 'A value axis with the \"{0}\" name was created automatically',\r\n    W2103: \"The chart title was hidden due to the container size\",\r\n    W2104: \"The legend was hidden due to the container size\",\r\n    W2105: 'The title of the \"{0}\" axis was hidden due to the container size',\r\n    W2106: 'The labels of the \"{0}\" axis were hidden due to the container size',\r\n    W2107: \"The export menu was hidden due to the container size\",\r\n    W2108: \"The browser does not support exporting images to {0} format.\",\r\n    W2301: \"Invalid value range\"\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;;;uCACe,CAAA,GAAA,2JAAA,CAAA,UAAU,AAAD,EAAE,mJAAA,CAAA,UAAM,CAAC,cAAc,EAAE;IAC7C,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6604, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/base_widget.utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/base_widget.utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    version\r\n} from \"../../core/version\";\r\nimport {\r\n    format as _stringFormat\r\n} from \"../../core/utils/string\";\r\nimport warnings from \"./errors_warnings\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport _windowResizeCallbacks from \"../../core/utils/resize_callbacks\";\r\nimport resizeObserverSingleton from \"../../core/resize_observer\";\r\nimport {\r\n    normalizeEnum\r\n} from \"./utils\";\r\nconst ERROR_MESSAGES = warnings.ERROR_MESSAGES;\r\nexport function createEventTrigger(eventsMap, callbackGetter) {\r\n    let triggers = {};\r\n    each(eventsMap, (function(name, info) {\r\n        if (info.name) {\r\n            createEvent(name)\r\n        }\r\n    }));\r\n    let changes;\r\n    triggerEvent.change = function(name) {\r\n        const eventInfo = eventsMap[name];\r\n        if (eventInfo) {\r\n            (changes = changes || {})[name] = eventInfo\r\n        }\r\n        return !!eventInfo\r\n    };\r\n    triggerEvent.applyChanges = function() {\r\n        if (changes) {\r\n            each(changes, (function(name, eventInfo) {\r\n                createEvent(eventInfo.newName || name)\r\n            }));\r\n            changes = null\r\n        }\r\n    };\r\n    triggerEvent.dispose = function() {\r\n        eventsMap = callbackGetter = triggers = null\r\n    };\r\n    return triggerEvent;\r\n\r\n    function createEvent(name) {\r\n        const eventInfo = eventsMap[name];\r\n        triggers[eventInfo.name] = callbackGetter(name, eventInfo.actionSettings)\r\n    }\r\n\r\n    function triggerEvent(name, arg, complete) {\r\n        triggers[name](arg);\r\n        complete && complete()\r\n    }\r\n}\r\nexport let createIncidentOccurred = function(widgetName, eventTrigger) {\r\n    return function(id, args) {\r\n        eventTrigger(\"incidentOccurred\", {\r\n            target: {\r\n                id: id,\r\n                type: \"E\" === id[0] ? \"error\" : \"warning\",\r\n                args: args,\r\n                text: _stringFormat.apply(null, [ERROR_MESSAGES[id]].concat(args || [])),\r\n                widget: widgetName,\r\n                version: version\r\n            }\r\n        })\r\n    }\r\n};\r\n\r\nfunction getResizeManager(resizeCallback) {\r\n    return (observe, unsubscribe) => {\r\n        const {\r\n            handler: handler,\r\n            dispose: dispose\r\n        } = createDeferredHandler(resizeCallback, unsubscribe);\r\n        observe(handler);\r\n        return dispose\r\n    }\r\n}\r\n\r\nfunction createDeferredHandler(callback, unsubscribe) {\r\n    let timeout;\r\n    const handler = function() {\r\n        clearTimeout(timeout);\r\n        timeout = setTimeout(callback, 100)\r\n    };\r\n    return {\r\n        handler: handler,\r\n        dispose() {\r\n            clearTimeout(timeout);\r\n            unsubscribe(handler)\r\n        }\r\n    }\r\n}\r\nexport function createResizeHandler(contentElement, redrawOnResize, resize) {\r\n    let disposeHandler;\r\n    const resizeManager = getResizeManager(resize);\r\n    if (\"windowonly\" === normalizeEnum(redrawOnResize)) {\r\n        disposeHandler = resizeManager((handler => _windowResizeCallbacks.add(handler)), (handler => _windowResizeCallbacks.remove(handler)))\r\n    } else if (true === redrawOnResize) {\r\n        disposeHandler = resizeManager((handler => resizeObserverSingleton.observe(contentElement, handler)), (() => resizeObserverSingleton.unobserve(contentElement)))\r\n    }\r\n    return disposeHandler\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AACA;AACA;;;;;;;;AAGA,MAAM,iBAAiB,mKAAA,CAAA,UAAQ,CAAC,cAAc;AACvC,SAAS,mBAAmB,SAAS,EAAE,cAAc;IACxD,IAAI,WAAW,CAAC;IAChB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,WAAY,SAAS,IAAI,EAAE,IAAI;QAChC,IAAI,KAAK,IAAI,EAAE;YACX,YAAY;QAChB;IACJ;IACA,IAAI;IACJ,aAAa,MAAM,GAAG,SAAS,IAAI;QAC/B,MAAM,YAAY,SAAS,CAAC,KAAK;QACjC,IAAI,WAAW;YACX,CAAC,UAAU,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;QACtC;QACA,OAAO,CAAC,CAAC;IACb;IACA,aAAa,YAAY,GAAG;QACxB,IAAI,SAAS;YACT,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,SAAS,IAAI,EAAE,SAAS;gBACnC,YAAY,UAAU,OAAO,IAAI;YACrC;YACA,UAAU;QACd;IACJ;IACA,aAAa,OAAO,GAAG;QACnB,YAAY,iBAAiB,WAAW;IAC5C;IACA,OAAO;;;IAEP,SAAS,YAAY,IAAI;QACrB,MAAM,YAAY,SAAS,CAAC,KAAK;QACjC,QAAQ,CAAC,UAAU,IAAI,CAAC,GAAG,eAAe,MAAM,UAAU,cAAc;IAC5E;IAEA,SAAS,aAAa,IAAI,EAAE,GAAG,EAAE,QAAQ;QACrC,QAAQ,CAAC,KAAK,CAAC;QACf,YAAY;IAChB;AACJ;AACO,IAAI,yBAAyB,SAAS,UAAU,EAAE,YAAY;IACjE,OAAO,SAAS,EAAE,EAAE,IAAI;QACpB,aAAa,oBAAoB;YAC7B,QAAQ;gBACJ,IAAI;gBACJ,MAAM,QAAQ,EAAE,CAAC,EAAE,GAAG,UAAU;gBAChC,MAAM;gBACN,MAAM,+KAAA,CAAA,SAAa,CAAC,KAAK,CAAC,MAAM;oBAAC,cAAc,CAAC,GAAG;iBAAC,CAAC,MAAM,CAAC,QAAQ,EAAE;gBACtE,QAAQ;gBACR,SAAS,oJAAA,CAAA,UAAO;YACpB;QACJ;IACJ;AACJ;AAEA,SAAS,iBAAiB,cAAc;IACpC,OAAO,CAAC,SAAS;QACb,MAAM,EACF,SAAS,OAAO,EAChB,SAAS,OAAO,EACnB,GAAG,sBAAsB,gBAAgB;QAC1C,QAAQ;QACR,OAAO;IACX;AACJ;AAEA,SAAS,sBAAsB,QAAQ,EAAE,WAAW;IAChD,IAAI;IACJ,MAAM,UAAU;QACZ,aAAa;QACb,UAAU,WAAW,UAAU;IACnC;IACA,OAAO;QACH,SAAS;QACT;YACI,aAAa;YACb,YAAY;QAChB;IACJ;AACJ;AACO,SAAS,oBAAoB,cAAc,EAAE,cAAc,EAAE,MAAM;IACtE,IAAI;IACJ,MAAM,gBAAgB,iBAAiB;IACvC,IAAI,iBAAiB,CAAA,GAAA,yJAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB;QAChD,iBAAiB,cAAe,CAAA,UAAW,sKAAA,CAAA,UAAsB,CAAC,GAAG,CAAC,UAAY,CAAA,UAAW,sKAAA,CAAA,UAAsB,CAAC,MAAM,CAAC;IAC/H,OAAO,IAAI,SAAS,gBAAgB;QAChC,iBAAiB,cAAe,CAAA,UAAW,4JAAA,CAAA,UAAuB,CAAC,OAAO,CAAC,gBAAgB,UAAY,IAAM,4JAAA,CAAA,UAAuB,CAAC,SAAS,CAAC;IACnJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6722, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/helpers.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/helpers.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend as _extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    hasWindow\r\n} from \"../../core/utils/window\";\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nconst isServerSide = !hasWindow();\r\n\r\nfunction Flags() {\r\n    this.reset()\r\n}\r\nFlags.prototype = {\r\n    constructor: Flags,\r\n    add: function(codes) {\r\n        let i;\r\n        const ii = codes.length;\r\n        const flags = this._flags;\r\n        for (i = 0; i < ii; ++i) {\r\n            flags[codes[i]] = 1\r\n        }\r\n    },\r\n    has: function(code) {\r\n        return this._flags[code] > 0\r\n    },\r\n    count: function() {\r\n        return Object.keys(this._flags).length\r\n    },\r\n    reset: function() {\r\n        this._flags = {}\r\n    }\r\n};\r\n\r\nfunction combineMaps(baseMap, thisMap) {\r\n    return baseMap !== thisMap ? _extend({}, baseMap, thisMap) : _extend({}, baseMap)\r\n}\r\n\r\nfunction combineLists(baseList, thisList) {\r\n    return baseList !== thisList ? baseList.concat(thisList) : baseList.slice()\r\n}\r\n\r\nfunction buildTotalChanges(proto) {\r\n    proto._totalChangesOrder = proto._optionChangesOrder.concat(proto._layoutChangesOrder, proto._customChangesOrder)\r\n}\r\n\r\nfunction addChange(settings) {\r\n    const proto = this.prototype;\r\n    const code = settings.code;\r\n    proto[\"_change_\" + code] = settings.handler;\r\n    if (settings.isThemeDependent) {\r\n        proto._themeDependentChanges.push(code)\r\n    }\r\n    if (settings.option) {\r\n        proto._optionChangesMap[settings.option] = code\r\n    }(settings.isOptionChange ? proto._optionChangesOrder : proto._customChangesOrder).push(code);\r\n    buildTotalChanges(proto)\r\n}\r\n\r\nfunction createChainExecutor() {\r\n    const executeChain = function() {\r\n        let i;\r\n        const ii = executeChain._chain.length;\r\n        let result;\r\n        for (i = 0; i < ii; ++i) {\r\n            result = executeChain._chain[i].apply(this, arguments)\r\n        }\r\n        return result\r\n    };\r\n    executeChain._chain = [];\r\n    executeChain.add = function(item) {\r\n        executeChain._chain.push(item)\r\n    };\r\n    executeChain.copy = function(executor) {\r\n        executeChain._chain = executor._chain.slice()\r\n    };\r\n    return executeChain\r\n}\r\nexport function expand(target, name, expander) {\r\n    let current = target[name];\r\n    if (!current) {\r\n        current = expander\r\n    } else if (!current.add) {\r\n        current = createChainExecutor();\r\n        current.add(target[name]);\r\n        current.add(expander)\r\n    } else {\r\n        if (false === Object.prototype.hasOwnProperty.call(target, name)) {\r\n            current = createChainExecutor();\r\n            current.copy(target[name])\r\n        }\r\n        current.add(expander)\r\n    }\r\n    target[name] = current\r\n}\r\n\r\nfunction addPlugin(plugin) {\r\n    const proto = this.prototype;\r\n    proto._plugins.push(plugin);\r\n    plugin.fontFields && proto._fontFields.push.apply(proto._fontFields, plugin.fontFields);\r\n    if (plugin.members) {\r\n        _extend(this.prototype, plugin.members)\r\n    }\r\n    if (plugin.customize) {\r\n        plugin.customize(this)\r\n    }\r\n    if (plugin.extenders) {\r\n        Object.keys(plugin.extenders).forEach((function(key) {\r\n            const func = plugin.extenders[key];\r\n            expand(proto, key, func)\r\n        }), this)\r\n    }\r\n}\r\nexport const replaceInherit = isServerSide ? function(widget) {\r\n    const _inherit = widget.inherit;\r\n    widget.inherit = function() {\r\n        const result = _inherit.apply(this, arguments);\r\n        const proto = result.prototype;\r\n        [\"_plugins\", \"_eventsMap\", \"_initialChanges\", \"_themeDependentChanges\", \"_optionChangesMap\", \"_optionChangesOrder\", \"_layoutChangesOrder\", \"_customChangesOrder\", \"_totalChangesOrder\"].forEach((function(key) {\r\n            proto[key] = {}\r\n        }));\r\n        result.addPlugin = noop;\r\n        return result\r\n    };\r\n    widget.addChange = noop;\r\n    widget.addPlugin = noop\r\n} : function(widget) {\r\n    const _inherit = widget.inherit;\r\n    widget.inherit = function() {\r\n        let proto = this.prototype;\r\n        const plugins = proto._plugins;\r\n        const fontFields = proto._fontFields;\r\n        const eventsMap = proto._eventsMap;\r\n        const initialChanges = proto._initialChanges;\r\n        const themeDependentChanges = proto._themeDependentChanges;\r\n        const optionChangesMap = proto._optionChangesMap;\r\n        const partialOptionChangesMap = proto._partialOptionChangesMap;\r\n        const partialOptionChangesPath = proto._partialOptionChangesPath;\r\n        const optionChangesOrder = proto._optionChangesOrder;\r\n        const layoutChangesOrder = proto._layoutChangesOrder;\r\n        const customChangesOrder = proto._customChangesOrder;\r\n        const result = _inherit.apply(this, arguments);\r\n        proto = result.prototype;\r\n        proto._plugins = combineLists(plugins, proto._plugins);\r\n        proto._fontFields = combineLists(fontFields, proto._fontFields);\r\n        proto._eventsMap = combineMaps(eventsMap, proto._eventsMap);\r\n        proto._initialChanges = combineLists(initialChanges, proto._initialChanges);\r\n        proto._themeDependentChanges = combineLists(themeDependentChanges, proto._themeDependentChanges);\r\n        proto._optionChangesMap = combineMaps(optionChangesMap, proto._optionChangesMap);\r\n        proto._partialOptionChangesMap = combineMaps(partialOptionChangesMap, proto._partialOptionChangesMap);\r\n        proto._partialOptionChangesPath = combineMaps(partialOptionChangesPath, proto._partialOptionChangesPath);\r\n        proto._optionChangesOrder = combineLists(optionChangesOrder, proto._optionChangesOrder);\r\n        proto._layoutChangesOrder = combineLists(layoutChangesOrder, proto._layoutChangesOrder);\r\n        proto._customChangesOrder = combineLists(customChangesOrder, proto._customChangesOrder);\r\n        buildTotalChanges(proto);\r\n        result.addPlugin = addPlugin;\r\n        return result\r\n    };\r\n    widget.prototype._plugins = [];\r\n    widget.prototype._fontFields = [];\r\n    widget.addChange = addChange;\r\n    widget.addPlugin = addPlugin\r\n};\r\nexport function changes() {\r\n    return new Flags\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;AAAA;AAGA;AAAA;AAGA;AAAA;;;;AAGA,MAAM,eAAe,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;AAE9B,SAAS;IACL,IAAI,CAAC,KAAK;AACd;AACA,MAAM,SAAS,GAAG;IACd,aAAa;IACb,KAAK,SAAS,KAAK;QACf,IAAI;QACJ,MAAM,KAAK,MAAM,MAAM;QACvB,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACrB,KAAK,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;QACtB;IACJ;IACA,KAAK,SAAS,IAAI;QACd,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;IAC/B;IACA,OAAO;QACH,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM;IAC1C;IACA,OAAO;QACH,IAAI,CAAC,MAAM,GAAG,CAAC;IACnB;AACJ;AAEA,SAAS,YAAY,OAAO,EAAE,OAAO;IACjC,OAAO,YAAY,UAAU,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,CAAC,GAAG,SAAS,WAAW,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,CAAC,GAAG;AAC7E;AAEA,SAAS,aAAa,QAAQ,EAAE,QAAQ;IACpC,OAAO,aAAa,WAAW,SAAS,MAAM,CAAC,YAAY,SAAS,KAAK;AAC7E;AAEA,SAAS,kBAAkB,KAAK;IAC5B,MAAM,kBAAkB,GAAG,MAAM,mBAAmB,CAAC,MAAM,CAAC,MAAM,mBAAmB,EAAE,MAAM,mBAAmB;AACpH;AAEA,SAAS,UAAU,QAAQ;IACvB,MAAM,QAAQ,IAAI,CAAC,SAAS;IAC5B,MAAM,OAAO,SAAS,IAAI;IAC1B,KAAK,CAAC,aAAa,KAAK,GAAG,SAAS,OAAO;IAC3C,IAAI,SAAS,gBAAgB,EAAE;QAC3B,MAAM,sBAAsB,CAAC,IAAI,CAAC;IACtC;IACA,IAAI,SAAS,MAAM,EAAE;QACjB,MAAM,iBAAiB,CAAC,SAAS,MAAM,CAAC,GAAG;IAC/C;IAAC,CAAC,SAAS,cAAc,GAAG,MAAM,mBAAmB,GAAG,MAAM,mBAAmB,EAAE,IAAI,CAAC;IACxF,kBAAkB;AACtB;AAEA,SAAS;IACL,MAAM,eAAe;QACjB,IAAI;QACJ,MAAM,KAAK,aAAa,MAAM,CAAC,MAAM;QACrC,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACrB,SAAS,aAAa,MAAM,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,EAAE;QAChD;QACA,OAAO;IACX;IACA,aAAa,MAAM,GAAG,EAAE;IACxB,aAAa,GAAG,GAAG,SAAS,IAAI;QAC5B,aAAa,MAAM,CAAC,IAAI,CAAC;IAC7B;IACA,aAAa,IAAI,GAAG,SAAS,QAAQ;QACjC,aAAa,MAAM,GAAG,SAAS,MAAM,CAAC,KAAK;IAC/C;IACA,OAAO;AACX;AACO,SAAS,OAAO,MAAM,EAAE,IAAI,EAAE,QAAQ;IACzC,IAAI,UAAU,MAAM,CAAC,KAAK;IAC1B,IAAI,CAAC,SAAS;QACV,UAAU;IACd,OAAO,IAAI,CAAC,QAAQ,GAAG,EAAE;QACrB,UAAU;QACV,QAAQ,GAAG,CAAC,MAAM,CAAC,KAAK;QACxB,QAAQ,GAAG,CAAC;IAChB,OAAO;QACH,IAAI,UAAU,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,QAAQ,OAAO;YAC9D,UAAU;YACV,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;QAC7B;QACA,QAAQ,GAAG,CAAC;IAChB;IACA,MAAM,CAAC,KAAK,GAAG;AACnB;AAEA,SAAS,UAAU,MAAM;IACrB,MAAM,QAAQ,IAAI,CAAC,SAAS;IAC5B,MAAM,QAAQ,CAAC,IAAI,CAAC;IACpB,OAAO,UAAU,IAAI,MAAM,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,WAAW,EAAE,OAAO,UAAU;IACtF,IAAI,OAAO,OAAO,EAAE;QAChB,CAAA,GAAA,+KAAA,CAAA,SAAO,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,OAAO,OAAO;IAC1C;IACA,IAAI,OAAO,SAAS,EAAE;QAClB,OAAO,SAAS,CAAC,IAAI;IACzB;IACA,IAAI,OAAO,SAAS,EAAE;QAClB,OAAO,IAAI,CAAC,OAAO,SAAS,EAAE,OAAO,CAAE,SAAS,GAAG;YAC/C,MAAM,OAAO,OAAO,SAAS,CAAC,IAAI;YAClC,OAAO,OAAO,KAAK;QACvB,GAAI,IAAI;IACZ;AACJ;AACO,MAAM,iBAAiB,eAAe,SAAS,MAAM;IACxD,MAAM,WAAW,OAAO,OAAO;IAC/B,OAAO,OAAO,GAAG;QACb,MAAM,SAAS,SAAS,KAAK,CAAC,IAAI,EAAE;QACpC,MAAM,QAAQ,OAAO,SAAS;QAC9B;YAAC;YAAY;YAAc;YAAmB;YAA0B;YAAqB;YAAuB;YAAuB;YAAuB;SAAqB,CAAC,OAAO,CAAE,SAAS,GAAG;YACzM,KAAK,CAAC,IAAI,GAAG,CAAC;QAClB;QACA,OAAO,SAAS,GAAG,+KAAA,CAAA,OAAI;QACvB,OAAO;IACX;IACA,OAAO,SAAS,GAAG,+KAAA,CAAA,OAAI;IACvB,OAAO,SAAS,GAAG,+KAAA,CAAA,OAAI;AAC3B,IAAI,SAAS,MAAM;IACf,MAAM,WAAW,OAAO,OAAO;IAC/B,OAAO,OAAO,GAAG;QACb,IAAI,QAAQ,IAAI,CAAC,SAAS;QAC1B,MAAM,UAAU,MAAM,QAAQ;QAC9B,MAAM,aAAa,MAAM,WAAW;QACpC,MAAM,YAAY,MAAM,UAAU;QAClC,MAAM,iBAAiB,MAAM,eAAe;QAC5C,MAAM,wBAAwB,MAAM,sBAAsB;QAC1D,MAAM,mBAAmB,MAAM,iBAAiB;QAChD,MAAM,0BAA0B,MAAM,wBAAwB;QAC9D,MAAM,2BAA2B,MAAM,yBAAyB;QAChE,MAAM,qBAAqB,MAAM,mBAAmB;QACpD,MAAM,qBAAqB,MAAM,mBAAmB;QACpD,MAAM,qBAAqB,MAAM,mBAAmB;QACpD,MAAM,SAAS,SAAS,KAAK,CAAC,IAAI,EAAE;QACpC,QAAQ,OAAO,SAAS;QACxB,MAAM,QAAQ,GAAG,aAAa,SAAS,MAAM,QAAQ;QACrD,MAAM,WAAW,GAAG,aAAa,YAAY,MAAM,WAAW;QAC9D,MAAM,UAAU,GAAG,YAAY,WAAW,MAAM,UAAU;QAC1D,MAAM,eAAe,GAAG,aAAa,gBAAgB,MAAM,eAAe;QAC1E,MAAM,sBAAsB,GAAG,aAAa,uBAAuB,MAAM,sBAAsB;QAC/F,MAAM,iBAAiB,GAAG,YAAY,kBAAkB,MAAM,iBAAiB;QAC/E,MAAM,wBAAwB,GAAG,YAAY,yBAAyB,MAAM,wBAAwB;QACpG,MAAM,yBAAyB,GAAG,YAAY,0BAA0B,MAAM,yBAAyB;QACvG,MAAM,mBAAmB,GAAG,aAAa,oBAAoB,MAAM,mBAAmB;QACtF,MAAM,mBAAmB,GAAG,aAAa,oBAAoB,MAAM,mBAAmB;QACtF,MAAM,mBAAmB,GAAG,aAAa,oBAAoB,MAAM,mBAAmB;QACtF,kBAAkB;QAClB,OAAO,SAAS,GAAG;QACnB,OAAO;IACX;IACA,OAAO,SAAS,CAAC,QAAQ,GAAG,EAAE;IAC9B,OAAO,SAAS,CAAC,WAAW,GAAG,EAAE;IACjC,OAAO,SAAS,GAAG;IACnB,OAAO,SAAS,GAAG;AACvB;AACO,SAAS;IACZ,OAAO,IAAI;AACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6909, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/core/layout.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/core/layout.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    normalizeEnum as _normalizeEnum\r\n} from \"./utils\";\r\nconst _min = Math.min;\r\nconst _max = Math.max;\r\nconst _round = Math.round;\r\nconst ALIGN_START = 0;\r\nconst ALIGN_MIDDLE = 1;\r\nconst ALIGN_END = 2;\r\nconst horizontalAlignmentMap = {\r\n    left: 0,\r\n    center: 1,\r\n    right: 2\r\n};\r\nconst verticalAlignmentMap = {\r\n    top: 0,\r\n    center: 1,\r\n    bottom: 2\r\n};\r\nconst sideMap = {\r\n    horizontal: 0,\r\n    vertical: 1\r\n};\r\nconst slicersMap = {};\r\nconst BBOX_CEIL_CORRECTION = 2;\r\nslicersMap[0] = function(a, b, size) {\r\n    return [a, _min(b, a + size)]\r\n};\r\nslicersMap[1] = function(a, b, size) {\r\n    return [_max(a, (a + b - size) / 2), _min(b, (a + b + size) / 2)]\r\n};\r\nslicersMap[2] = function(a, b, size) {\r\n    return [_max(a, b - size), b]\r\n};\r\n\r\nfunction pickValue(value, map, defaultValue) {\r\n    const val = _normalizeEnum(value);\r\n    return val in map ? map[val] : defaultValue\r\n}\r\n\r\nfunction normalizeLayoutOptions(options) {\r\n    const side = pickValue(options.side, sideMap, 1);\r\n    const alignment = [pickValue(options.horizontalAlignment, horizontalAlignmentMap, 1), pickValue(options.verticalAlignment, verticalAlignmentMap, 0)];\r\n    return {\r\n        side: side,\r\n        primary: bringToEdge(alignment[side]),\r\n        secondary: alignment[1 - side],\r\n        weak: options.weak,\r\n        priority: options.priority || 0,\r\n        header: options.header,\r\n        position: options.position\r\n    }\r\n}\r\n\r\nfunction bringToEdge(primary) {\r\n    return primary < 2 ? 0 : 2\r\n}\r\n\r\nfunction getConjugateSide(side) {\r\n    return 1 - side\r\n}\r\n\r\nfunction getSlice(alignment, a, b, size) {\r\n    return slicersMap[alignment](a, b, size)\r\n}\r\n\r\nfunction getShrink(alignment, size) {\r\n    return (alignment > 0 ? -1 : 1) * size\r\n}\r\n\r\nfunction processForward(item, rect, minSize) {\r\n    const side = item.side;\r\n    const size = item.element.measure([rect[2] - rect[0], rect[3] - rect[1]]);\r\n    const minSide = \"indside\" === item.position ? 0 : minSize[side];\r\n    const isValid = size[side] < rect[2 + side] - rect[side] - minSide;\r\n    if (isValid) {\r\n        if (\"inside\" !== item.position) {\r\n            rect[item.primary + side] += getShrink(item.primary, size[side])\r\n        }\r\n        item.size = size\r\n    }\r\n    return isValid\r\n}\r\n\r\nfunction processRectBackward(item, rect, alignmentRect) {\r\n    const primarySide = item.side;\r\n    const secondarySide = getConjugateSide(primarySide);\r\n    const itemRect = [];\r\n    const secondary = getSlice(item.secondary, alignmentRect[secondarySide], alignmentRect[2 + secondarySide], item.size[secondarySide]);\r\n    itemRect[primarySide] = _round(itemRect[2 + primarySide] = rect[item.primary + primarySide] + (\"inside\" === item.position ? getShrink(item.primary, item.size[primarySide]) : 0));\r\n    itemRect[item.primary + primarySide] = _round(rect[item.primary + primarySide] - getShrink(item.primary, item.size[primarySide]));\r\n    if (\"inside\" !== item.position) {\r\n        rect[item.primary + primarySide] = itemRect[item.primary + primarySide]\r\n    }\r\n    itemRect[secondarySide] = _round(secondary[0]);\r\n    itemRect[2 + secondarySide] = _round(secondary[1]);\r\n    return itemRect\r\n}\r\n\r\nfunction processBackward(item, rect, alignmentRect, fitRect, size, targetRect) {\r\n    const itemRect = processRectBackward(item, rect, alignmentRect);\r\n    const itemFitRect = processRectBackward(item, fitRect, fitRect);\r\n    if (size[item.side] > 0) {\r\n        size[item.side] -= item.size[item.side];\r\n        targetRect[item.primary + item.side] = itemRect[item.primary + item.side];\r\n        item.element.freeSpace()\r\n    } else {\r\n        item.element.move(itemRect, itemFitRect)\r\n    }\r\n}\r\n\r\nfunction Layout() {\r\n    this._targets = []\r\n}\r\nLayout.prototype = {\r\n    constructor: Layout,\r\n    dispose: function() {\r\n        this._targets = null\r\n    },\r\n    add: function(target) {\r\n        this._targets.push(target)\r\n    },\r\n    forward: function(targetRect, minSize) {\r\n        const rect = targetRect.slice();\r\n        const targets = createTargets(this._targets);\r\n        let i;\r\n        const ii = targets.length;\r\n        const cache = [];\r\n        for (i = 0; i < ii; ++i) {\r\n            if (processForward(targets[i], rect, minSize)) {\r\n                cache.push(targets[i])\r\n            } else {\r\n                targets[i].element.freeSpace()\r\n            }\r\n        }\r\n        this._cache = cache.reverse();\r\n        return rect\r\n    },\r\n    backward: function(targetRect, alignmentRect) {\r\n        let size = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : [0, 0];\r\n        let backwardRect = targetRect.slice();\r\n        const fitRect = targetRect.slice();\r\n        const targets = this._cache;\r\n        let targetSide = 0;\r\n        let target;\r\n        let i;\r\n        const ii = targets.length;\r\n        for (i = 0; i < ii; ++i) {\r\n            target = targets[i];\r\n            if (target.side !== targetSide) {\r\n                backwardRect = targetRect.slice()\r\n            }\r\n            processBackward(target, backwardRect, alignmentRect, fitRect, size, targetRect);\r\n            targetSide = target.side\r\n        }\r\n        return size\r\n    }\r\n};\r\n\r\nfunction createTargets(targets) {\r\n    let i;\r\n    const ii = targets.length;\r\n    let collection = [];\r\n    let layout;\r\n    for (i = 0; i < ii; ++i) {\r\n        layout = targets[i].layoutOptions();\r\n        if (layout) {\r\n            layout = normalizeLayoutOptions(layout);\r\n            layout.element = targets[i];\r\n            collection.push(layout)\r\n        }\r\n    }\r\n    collection.sort((function(a, b) {\r\n        return b.side - a.side || a.priority - b.priority\r\n    }));\r\n    collection = processWeakItems(collection);\r\n    return collection\r\n}\r\n\r\nfunction processWeakItems(collection) {\r\n    const weakItem = collection.filter((function(item) {\r\n        return true === item.weak\r\n    }))[0];\r\n    let headerItem;\r\n    if (weakItem) {\r\n        headerItem = collection.filter((function(item) {\r\n            return weakItem.primary === item.primary && item.side === weakItem.side && item !== weakItem\r\n        }))[0]\r\n    }\r\n    if (weakItem && headerItem) {\r\n        return [makeHeader(headerItem, weakItem)].concat(collection.filter((function(item) {\r\n            return !(item === headerItem || item === weakItem)\r\n        })))\r\n    }\r\n    return collection\r\n}\r\n\r\nfunction processBackwardHeaderRect(element, rect) {\r\n    const rectCopy = rect.slice();\r\n    const itemRect = processRectBackward(element, rectCopy, rectCopy);\r\n    itemRect[element.side] = rect[element.side];\r\n    itemRect[2 + element.side] = rect[2 + element.side];\r\n    return itemRect\r\n}\r\n\r\nfunction makeHeader(header, weakElement) {\r\n    const side = header.side;\r\n    const primary = header.primary;\r\n    const secondary = header.secondary;\r\n    return {\r\n        side: side,\r\n        primary: primary,\r\n        secondary: secondary,\r\n        priority: 0,\r\n        element: {\r\n            measure: function(targetSize) {\r\n                const result = targetSize.slice();\r\n                const weakSize = weakElement.element.measure(targetSize.slice());\r\n                targetSize[primary] -= weakSize[primary];\r\n                const headerSize = header.element.measure(targetSize.slice());\r\n                result[side] = weakSize[side] = headerSize[side] = Math.max(headerSize[side], weakSize[side]);\r\n                weakElement.size = weakSize;\r\n                header.size = headerSize;\r\n                return result\r\n            },\r\n            move: function(rect, fitRect) {\r\n                if (fitRect[2] - fitRect[0] < header.size[0] + weakElement.size[0] - 2) {\r\n                    this.freeSpace();\r\n                    return\r\n                }\r\n                const weakRect = processBackwardHeaderRect(weakElement, fitRect);\r\n                fitRect[2 + weakElement.primary] = weakRect[weakElement.primary];\r\n                const headerFitReact = processBackwardHeaderRect(header, fitRect);\r\n                if (fitRect[2 + weakElement.primary] < rect[2 + weakElement.primary] && header.size[header.primary] > rect[2 + header.primary] - rect[header.primary]) {\r\n                    rect[2 + weakElement.primary] = fitRect[2 + weakElement.primary]\r\n                }\r\n                let headerRect = processBackwardHeaderRect(header, rect);\r\n                if (headerRect[2 + weakElement.primary] > fitRect[2 + weakElement.primary]) {\r\n                    rect[2 + weakElement.primary] = fitRect[2 + weakElement.primary];\r\n                    headerRect = processBackwardHeaderRect(header, rect)\r\n                }\r\n                weakElement.element.move(weakRect);\r\n                header.element.move(headerRect, headerFitReact)\r\n            },\r\n            freeSpace: function() {\r\n                header.element.freeSpace();\r\n                weakElement.element.freeSpace()\r\n            }\r\n        }\r\n    }\r\n}\r\nexport default Layout;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AAGA,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,SAAS,KAAK,KAAK;AACzB,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,YAAY;AAClB,MAAM,yBAAyB;IAC3B,MAAM;IACN,QAAQ;IACR,OAAO;AACX;AACA,MAAM,uBAAuB;IACzB,KAAK;IACL,QAAQ;IACR,QAAQ;AACZ;AACA,MAAM,UAAU;IACZ,YAAY;IACZ,UAAU;AACd;AACA,MAAM,aAAa,CAAC;AACpB,MAAM,uBAAuB;AAC7B,UAAU,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI;IAC/B,OAAO;QAAC;QAAG,KAAK,GAAG,IAAI;KAAM;AACjC;AACA,UAAU,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI;IAC/B,OAAO;QAAC,KAAK,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI;QAAI,KAAK,GAAG,CAAC,IAAI,IAAI,IAAI,IAAI;KAAG;AACrE;AACA,UAAU,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,CAAC,EAAE,IAAI;IAC/B,OAAO;QAAC,KAAK,GAAG,IAAI;QAAO;KAAE;AACjC;AAEA,SAAS,UAAU,KAAK,EAAE,GAAG,EAAE,YAAY;IACvC,MAAM,MAAM,CAAA,GAAA,yJAAA,CAAA,gBAAc,AAAD,EAAE;IAC3B,OAAO,OAAO,MAAM,GAAG,CAAC,IAAI,GAAG;AACnC;AAEA,SAAS,uBAAuB,OAAO;IACnC,MAAM,OAAO,UAAU,QAAQ,IAAI,EAAE,SAAS;IAC9C,MAAM,YAAY;QAAC,UAAU,QAAQ,mBAAmB,EAAE,wBAAwB;QAAI,UAAU,QAAQ,iBAAiB,EAAE,sBAAsB;KAAG;IACpJ,OAAO;QACH,MAAM;QACN,SAAS,YAAY,SAAS,CAAC,KAAK;QACpC,WAAW,SAAS,CAAC,IAAI,KAAK;QAC9B,MAAM,QAAQ,IAAI;QAClB,UAAU,QAAQ,QAAQ,IAAI;QAC9B,QAAQ,QAAQ,MAAM;QACtB,UAAU,QAAQ,QAAQ;IAC9B;AACJ;AAEA,SAAS,YAAY,OAAO;IACxB,OAAO,UAAU,IAAI,IAAI;AAC7B;AAEA,SAAS,iBAAiB,IAAI;IAC1B,OAAO,IAAI;AACf;AAEA,SAAS,SAAS,SAAS,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI;IACnC,OAAO,UAAU,CAAC,UAAU,CAAC,GAAG,GAAG;AACvC;AAEA,SAAS,UAAU,SAAS,EAAE,IAAI;IAC9B,OAAO,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,IAAI;AACtC;AAEA,SAAS,eAAe,IAAI,EAAE,IAAI,EAAE,OAAO;IACvC,MAAM,OAAO,KAAK,IAAI;IACtB,MAAM,OAAO,KAAK,OAAO,CAAC,OAAO,CAAC;QAAC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QAAE,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;KAAC;IACxE,MAAM,UAAU,cAAc,KAAK,QAAQ,GAAG,IAAI,OAAO,CAAC,KAAK;IAC/D,MAAM,UAAU,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,CAAC,KAAK,GAAG;IAC3D,IAAI,SAAS;QACT,IAAI,aAAa,KAAK,QAAQ,EAAE;YAC5B,IAAI,CAAC,KAAK,OAAO,GAAG,KAAK,IAAI,UAAU,KAAK,OAAO,EAAE,IAAI,CAAC,KAAK;QACnE;QACA,KAAK,IAAI,GAAG;IAChB;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,IAAI,EAAE,IAAI,EAAE,aAAa;IAClD,MAAM,cAAc,KAAK,IAAI;IAC7B,MAAM,gBAAgB,iBAAiB;IACvC,MAAM,WAAW,EAAE;IACnB,MAAM,YAAY,SAAS,KAAK,SAAS,EAAE,aAAa,CAAC,cAAc,EAAE,aAAa,CAAC,IAAI,cAAc,EAAE,KAAK,IAAI,CAAC,cAAc;IACnI,QAAQ,CAAC,YAAY,GAAG,OAAO,QAAQ,CAAC,IAAI,YAAY,GAAG,IAAI,CAAC,KAAK,OAAO,GAAG,YAAY,GAAG,CAAC,aAAa,KAAK,QAAQ,GAAG,UAAU,KAAK,OAAO,EAAE,KAAK,IAAI,CAAC,YAAY,IAAI,CAAC;IAC/K,QAAQ,CAAC,KAAK,OAAO,GAAG,YAAY,GAAG,OAAO,IAAI,CAAC,KAAK,OAAO,GAAG,YAAY,GAAG,UAAU,KAAK,OAAO,EAAE,KAAK,IAAI,CAAC,YAAY;IAC/H,IAAI,aAAa,KAAK,QAAQ,EAAE;QAC5B,IAAI,CAAC,KAAK,OAAO,GAAG,YAAY,GAAG,QAAQ,CAAC,KAAK,OAAO,GAAG,YAAY;IAC3E;IACA,QAAQ,CAAC,cAAc,GAAG,OAAO,SAAS,CAAC,EAAE;IAC7C,QAAQ,CAAC,IAAI,cAAc,GAAG,OAAO,SAAS,CAAC,EAAE;IACjD,OAAO;AACX;AAEA,SAAS,gBAAgB,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU;IACzE,MAAM,WAAW,oBAAoB,MAAM,MAAM;IACjD,MAAM,cAAc,oBAAoB,MAAM,SAAS;IACvD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,GAAG,GAAG;QACrB,IAAI,CAAC,KAAK,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,KAAK,IAAI,CAAC;QACvC,UAAU,CAAC,KAAK,OAAO,GAAG,KAAK,IAAI,CAAC,GAAG,QAAQ,CAAC,KAAK,OAAO,GAAG,KAAK,IAAI,CAAC;QACzE,KAAK,OAAO,CAAC,SAAS;IAC1B,OAAO;QACH,KAAK,OAAO,CAAC,IAAI,CAAC,UAAU;IAChC;AACJ;AAEA,SAAS;IACL,IAAI,CAAC,QAAQ,GAAG,EAAE;AACtB;AACA,OAAO,SAAS,GAAG;IACf,aAAa;IACb,SAAS;QACL,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,KAAK,SAAS,MAAM;QAChB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;IACvB;IACA,SAAS,SAAS,UAAU,EAAE,OAAO;QACjC,MAAM,OAAO,WAAW,KAAK;QAC7B,MAAM,UAAU,cAAc,IAAI,CAAC,QAAQ;QAC3C,IAAI;QACJ,MAAM,KAAK,QAAQ,MAAM;QACzB,MAAM,QAAQ,EAAE;QAChB,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACrB,IAAI,eAAe,OAAO,CAAC,EAAE,EAAE,MAAM,UAAU;gBAC3C,MAAM,IAAI,CAAC,OAAO,CAAC,EAAE;YACzB,OAAO;gBACH,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,SAAS;YAChC;QACJ;QACA,IAAI,CAAC,MAAM,GAAG,MAAM,OAAO;QAC3B,OAAO;IACX;IACA,UAAU,SAAS,UAAU,EAAE,aAAa;QACxC,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;YAAC;YAAG;SAAE;QAClF,IAAI,eAAe,WAAW,KAAK;QACnC,MAAM,UAAU,WAAW,KAAK;QAChC,MAAM,UAAU,IAAI,CAAC,MAAM;QAC3B,IAAI,aAAa;QACjB,IAAI;QACJ,IAAI;QACJ,MAAM,KAAK,QAAQ,MAAM;QACzB,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YACrB,SAAS,OAAO,CAAC,EAAE;YACnB,IAAI,OAAO,IAAI,KAAK,YAAY;gBAC5B,eAAe,WAAW,KAAK;YACnC;YACA,gBAAgB,QAAQ,cAAc,eAAe,SAAS,MAAM;YACpE,aAAa,OAAO,IAAI;QAC5B;QACA,OAAO;IACX;AACJ;AAEA,SAAS,cAAc,OAAO;IAC1B,IAAI;IACJ,MAAM,KAAK,QAAQ,MAAM;IACzB,IAAI,aAAa,EAAE;IACnB,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,SAAS,OAAO,CAAC,EAAE,CAAC,aAAa;QACjC,IAAI,QAAQ;YACR,SAAS,uBAAuB;YAChC,OAAO,OAAO,GAAG,OAAO,CAAC,EAAE;YAC3B,WAAW,IAAI,CAAC;QACpB;IACJ;IACA,WAAW,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;QAC1B,OAAO,EAAE,IAAI,GAAG,EAAE,IAAI,IAAI,EAAE,QAAQ,GAAG,EAAE,QAAQ;IACrD;IACA,aAAa,iBAAiB;IAC9B,OAAO;AACX;AAEA,SAAS,iBAAiB,UAAU;IAChC,MAAM,WAAW,WAAW,MAAM,CAAE,SAAS,IAAI;QAC7C,OAAO,SAAS,KAAK,IAAI;IAC7B,EAAG,CAAC,EAAE;IACN,IAAI;IACJ,IAAI,UAAU;QACV,aAAa,WAAW,MAAM,CAAE,SAAS,IAAI;YACzC,OAAO,SAAS,OAAO,KAAK,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,SAAS,IAAI,IAAI,SAAS;QACxF,EAAG,CAAC,EAAE;IACV;IACA,IAAI,YAAY,YAAY;QACxB,OAAO;YAAC,WAAW,YAAY;SAAU,CAAC,MAAM,CAAC,WAAW,MAAM,CAAE,SAAS,IAAI;YAC7E,OAAO,CAAC,CAAC,SAAS,cAAc,SAAS,QAAQ;QACrD;IACJ;IACA,OAAO;AACX;AAEA,SAAS,0BAA0B,OAAO,EAAE,IAAI;IAC5C,MAAM,WAAW,KAAK,KAAK;IAC3B,MAAM,WAAW,oBAAoB,SAAS,UAAU;IACxD,QAAQ,CAAC,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAC;IAC3C,QAAQ,CAAC,IAAI,QAAQ,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,QAAQ,IAAI,CAAC;IACnD,OAAO;AACX;AAEA,SAAS,WAAW,MAAM,EAAE,WAAW;IACnC,MAAM,OAAO,OAAO,IAAI;IACxB,MAAM,UAAU,OAAO,OAAO;IAC9B,MAAM,YAAY,OAAO,SAAS;IAClC,OAAO;QACH,MAAM;QACN,SAAS;QACT,WAAW;QACX,UAAU;QACV,SAAS;YACL,SAAS,SAAS,UAAU;gBACxB,MAAM,SAAS,WAAW,KAAK;gBAC/B,MAAM,WAAW,YAAY,OAAO,CAAC,OAAO,CAAC,WAAW,KAAK;gBAC7D,UAAU,CAAC,QAAQ,IAAI,QAAQ,CAAC,QAAQ;gBACxC,MAAM,aAAa,OAAO,OAAO,CAAC,OAAO,CAAC,WAAW,KAAK;gBAC1D,MAAM,CAAC,KAAK,GAAG,QAAQ,CAAC,KAAK,GAAG,UAAU,CAAC,KAAK,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,KAAK,EAAE,QAAQ,CAAC,KAAK;gBAC5F,YAAY,IAAI,GAAG;gBACnB,OAAO,IAAI,GAAG;gBACd,OAAO;YACX;YACA,MAAM,SAAS,IAAI,EAAE,OAAO;gBACxB,IAAI,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,IAAI,CAAC,EAAE,GAAG,YAAY,IAAI,CAAC,EAAE,GAAG,GAAG;oBACpE,IAAI,CAAC,SAAS;oBACd;gBACJ;gBACA,MAAM,WAAW,0BAA0B,aAAa;gBACxD,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,QAAQ,CAAC,YAAY,OAAO,CAAC;gBAChE,MAAM,iBAAiB,0BAA0B,QAAQ;gBACzD,IAAI,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,YAAY,OAAO,CAAC,IAAI,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,OAAO,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO,OAAO,CAAC,EAAE;oBACnJ,IAAI,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC;gBACpE;gBACA,IAAI,aAAa,0BAA0B,QAAQ;gBACnD,IAAI,UAAU,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC,EAAE;oBACxE,IAAI,CAAC,IAAI,YAAY,OAAO,CAAC,GAAG,OAAO,CAAC,IAAI,YAAY,OAAO,CAAC;oBAChE,aAAa,0BAA0B,QAAQ;gBACnD;gBACA,YAAY,OAAO,CAAC,IAAI,CAAC;gBACzB,OAAO,OAAO,CAAC,IAAI,CAAC,YAAY;YACpC;YACA,WAAW;gBACP,OAAO,OAAO,CAAC,SAAS;gBACxB,YAAY,OAAO,CAAC,SAAS;YACjC;QACJ;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}]}