"use client";
import { useEffect, useState } from "react";
import DataGrid, { Column } from "devextreme-react/data-grid";
import Chart, {
  CommonSeriesSettings,
  Series,
  Export,
  Legend,
  Margin,
  Tooltip,
  Title,
  Subtitle,
} from "devextreme-react/chart";
import { useRouter } from "next/navigation";
import AppLayout from "../../../components/AppLayout";
import LoadIndicator from "devextreme-react/load-indicator";
import { useAuth } from "@/hooks/useAuth";
import PermissionGuard from "@/components/PermissionGuard";
import { hasPermission } from "@/lib/permissions";

export default function Dashboard() {
  const router = useRouter();
  const { user, loading: authLoading, authenticated } = useAuth();
  const [data, setData] = useState<any[]>([]);
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Auth check is handled by useAuth hook
    if (!authLoading && !authenticated) {
      router.push("/login");
      return;
    }

    // Mock data for demonstration
    setTimeout(() => {
      setData([
        { id: 1, name: "Teszt Felhasználó 1", balance: 15000 },
        { id: 2, name: "Teszt Felhasználó 2", balance: 25000 },
        { id: 3, name: "Teszt Felhasználó 3", balance: 8500 },
        { id: 4, name: "Teszt Felhasználó 4", balance: 32000 },
        { id: 5, name: "Teszt Felhasználó 5", balance: 12500 },
      ]);

      setChartData([
        { month: "Jan", revenue: 10000, expense: 8000 },
        { month: "Feb", revenue: 12000, expense: 9000 },
        { month: "Már", revenue: 15000, expense: 10000 },
        { month: "Ápr", revenue: 18000, expense: 12000 },
        { month: "Máj", revenue: 20000, expense: 15000 },
        { month: "Jún", revenue: 22000, expense: 17000 },
      ]);

      setLoading(false);
    }, 1000);
  }, [authLoading, authenticated, router]);

  if (authLoading || loading) {
    return (
      <AppLayout title="Dashboard">
        <div
          style={{
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            height: "200px",
          }}
        >
          <LoadIndicator width={40} height={40} />
        </div>
      </AppLayout>
    );
  }

  return (
    <PermissionGuard permission="view_dashboard">
      <AppLayout title="Dashboard">
        <div
          className="dx-card"
          style={{ padding: "24px", marginBottom: "20px" }}
        >
          <h2
            style={{
              fontSize: "24px",
              fontWeight: 500,
              marginBottom: "24px",
              color: "var(--base-text-color)",
            }}
          >
            Pénzügyi Áttekintés
          </h2>
          <Chart id="chart" dataSource={chartData} palette="Material">
            <CommonSeriesSettings argumentField="month" type="line" />
            <Series valueField="revenue" name="Bevétel" />
            <Series valueField="expense" name="Kiadás" />
            <Margin bottom={20} />
            <Title text="Bevétel és Kiadás">
              <Subtitle text="(ezer Ft)" />
            </Title>
            <Export enabled={true} />
            <Legend verticalAlignment="bottom" horizontalAlignment="center" />
            <Tooltip enabled={true} />
          </Chart>
        </div>

        <div className="dx-card" style={{ padding: "24px" }}>
          <h2
            style={{
              fontSize: "24px",
              fontWeight: 500,
              marginBottom: "24px",
              color: "var(--base-text-color)",
            }}
          >
            Felhasználói Adatok
          </h2>
          <DataGrid
            dataSource={data}
            height={400}
            showBorders={true}
            rowAlternationEnabled={true}
            columnAutoWidth={true}
          >
            <Column dataField="id" caption="ID" width={80} />
            <Column dataField="name" caption="Név" />
            <Column
              dataField="balance"
              caption="Egyenleg"
              dataType="number"
              format="currency"
            />
          </DataGrid>
        </div>
      </AppLayout>
    </PermissionGuard>
  );
}
