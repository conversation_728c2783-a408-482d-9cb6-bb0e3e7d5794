// src/hooks/useAuth.ts
"use client";

import { useState, useEffect } from "react";
import { getUserPayload, isAuthenticated } from "@/lib/auth-client";

export interface User {
  userId: string;
  email: string;
  roles: string[];
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [authenticated, setAuthenticated] = useState(false);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authStatus = await isAuthenticated();
        setAuthenticated(authStatus);

        if (authStatus) {
          const payload = await getUserPayload();
          if (payload) {
            setUser({
              userId: payload.userId,
              email: payload.email,
              roles: payload.roles,
            });
          }
        }
      } catch (error) {
        console.error("Authentication check failed:", error);
      } finally {
        setLoading(false);
      }
    };

    checkAuth();
  }, []);

  return { user, loading, authenticated };
}
