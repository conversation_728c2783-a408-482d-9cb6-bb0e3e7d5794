﻿using DevExpress.Xpf.Accordion;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Docking;
using omsnext.shared.DTO;
using omsnext.wpf.Services;
using System.Windows;
using System.Windows.Controls;


namespace omsnext.wpf;

public partial class MainWindow : ThemedWindow
{
    private readonly ApiClient _apiClient;
    private readonly LoginResponseDto _loginResponse;

    public MainWindow(LoginResponseDto loginResponse, ApiClient apiClient)
    {
        InitializeComponent();
        _loginResponse = loginResponse;
        _apiClient = apiClient;
        this.Title = $"OmsNext - Bejelentkezve: {_loginResponse.User.DisplayName}";
        OpenDashboard();
    }

    private void DashboardButton_Click(object sender, DevExpress.Xpf.Bars.ItemClickEventArgs e)
    {
        OpenDashboard();
    }

    private void LogoutButton_Click(object sender, DevExpress.Xpf.Bars.ItemClickEventArgs e)
    {
        var result = DXMessageBox.Show("Biztosan ki szeretne jelent<PERSON>?", "Kijelentkezés", MessageBoxButton.YesNo, MessageBoxImage.Question);
        if (result == MessageBoxResult.Yes)
        {
            var loginWindow = new LoginWindow();
            loginWindow.Show();
            this.Close();
        }
    }

    private void NavigationTree_SelectedItemChanged(object sender, RoutedPropertyChangedEventArgs<object> e)
    {
        if (e.NewValue is TreeViewItem item && item.Tag is string tag)
        {
            Navigate(tag);
        }
    }

    private void AccordionControl_SelectedItemChanged(object sender, AccordionSelectedItemChangedEventArgs e)
    {
        var accordion = sender as AccordionControl;
        var item = accordion?.SelectedItem as AccordionItem;
        var tag = item?.Tag as string;
        if (!string.IsNullOrEmpty(tag))
        {
            Navigate(tag);
        }
    }

    private void Navigate(string tag)
    {
        switch (tag)
        {
            case "Dashboard":
                OpenDashboard();
                break;
            case "UsersList":
                OpenTab("Felhasználó lista", "UsersList", new TextBlock { Text = "Felhasználók lista nézet", Margin = new Thickness(10) });
                break;
            case "Roles":
                OpenTab("Szerepkörök", "Roles", new TextBlock { Text = "Szerepkörök nézet", Margin = new Thickness(10) });
                break;
            case "Settings":
                OpenTab("Beállítások", "Settings", new TextBlock { Text = "Beállítások nézet", Margin = new Thickness(10) });
                break;
            case "Ktforgev":
                OpenKtforgevTab();
                break;
        }
    }

    private void OpenDashboard()
    {
        OpenTab("Dashboard", "Dashboard", new DashboardUserControl(_loginResponse));
    }

    private void OpenTab(string caption, string key, FrameworkElement content)
    {
        var existingDocument = DocumentGroup.Items.OfType<DocumentPanel>().FirstOrDefault(d => d.Tag as string == key);
        if (existingDocument != null)
        {
            existingDocument.IsActive = true;
            return;
        }

        var document = new DocumentPanel
        {
            Caption = caption,
            Content = content,
            Tag = key,
            AllowClose = key != "Dashboard"
        };
        DocumentGroup.Items.Add(document);
        document.IsActive = true;
    }

    private void OpenKtforgevTab()
    {
        OpenTab("Ktforgev", "Ktforgev", new KtforgevUserControl(_apiClient));
    }
}