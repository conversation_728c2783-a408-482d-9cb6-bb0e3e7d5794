﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class AdminDashboardUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<PasswordResetRequestDto> _requests;
        private bool _isLoading;
        private string _currentStatusFilter = "Pending";

        public event PropertyChangedEventHandler? PropertyChanged;

        public AdminDashboardUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _requests = new ObservableCollection<PasswordResetRequestDto>();
            
            DataContext = this;
            RequestsGrid.ItemsSource = _requests;
            
            Loaded += AdminDashboardUserControl_Loaded;
        }

        private async void AdminDashboardUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
        }

        private async Task LoadRequestsAsync()
        {
            if (_isLoading) return;

            _isLoading = true;
            StatusLabel.Text = "Betöltés...";
            
            try
            {
                // Szimulált adatok egyelőre
                var sampleRequests = new List<PasswordResetRequestDto>
                {
                    new PasswordResetRequestDto
                    {
                        Id = 1,
                        UserEmail = "<EMAIL>",
                        UserDisplayName = "Teszt Felhasználó",
                        RequestDate = DateTime.Now.AddHours(-2),
                        Status = "Pending",
                        RequestSource = "Desktop"
                    },
                    new PasswordResetRequestDto
                    {
                        Id = 2,
                        UserEmail = "<EMAIL>",
                        UserDisplayName = "Másik Felhasználó",
                        RequestDate = DateTime.Now.AddDays(-1),
                        Status = "Completed",
                        ProcessedByAdminName = "Admin User",
                        ProcessedDate = DateTime.Now.AddHours(-1),
                        AdminNotes = "Sikeresen feldolgozva",
                        RequestSource = "Desktop"
                    }
                };

                _requests.Clear();
                
                var filteredRequests = string.IsNullOrEmpty(_currentStatusFilter) 
                    ? sampleRequests 
                    : sampleRequests.Where(r => r.Status == _currentStatusFilter);
                
                foreach (var request in filteredRequests)
                {
                    _requests.Add(request);
                }
                
                UpdateStatusLabels(sampleRequests);
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
                StatusLabel.Text = "Készen";
            }
        }

        private void UpdateStatusLabels(List<PasswordResetRequestDto> allRequests)
        {
            RecordCountLabel.Text = $"Kérelmek: {_requests.Count}";
            PendingCountLabel.Text = allRequests.Count(r => r.Status == "Pending").ToString();
            CompletedCountLabel.Text = allRequests.Count(r => r.Status == "Completed").ToString();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
        }

        private async void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _currentStatusFilter = selectedItem.Tag?.ToString() ?? "";
                await LoadRequestsAsync();
            }
        }

        private async void ApproveButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PasswordResetRequestDto request)
            {
                var result = DXMessageBox.Show(
                    $"Biztosan jóváhagyja a jelszó visszaállítási kérelmet?\n\nFelhasználó: {request.UserDisplayName}\nE-mail: {request.UserEmail}", 
                    "Kérelem jóváhagyása", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await ProcessRequestAsync(request.Id, "Approved", "Adminisztrátor által jóváhagyva");
                }
            }
        }

        private async void RejectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PasswordResetRequestDto request)
            {
                // Egyszerű szöveges input
                var inputWindow = new Window
                {
                    Title = "Kérelem elutasítása",
                    Width = 400,
                    Height = 200,
                    WindowStartupLocation = WindowStartupLocation.CenterOwner,
                    Owner = Window.GetWindow(this)
                };

                var panel = new StackPanel { Margin = new Thickness(20) };
                panel.Children.Add(new TextBlock { Text = "Kérjük, adja meg az elutasítás okát:", Margin = new Thickness(0, 0, 0, 10) });
                
                var textBox = new TextBox { Height = 80, TextWrapping = TextWrapping.Wrap, AcceptsReturn = true };
                panel.Children.Add(textBox);

                var buttonPanel = new StackPanel { Orientation = Orientation.Horizontal, HorizontalAlignment = HorizontalAlignment.Right, Margin = new Thickness(0, 15, 0, 0) };
                var okButton = new Button { Content = "OK", Width = 75, Height = 30, Margin = new Thickness(0, 0, 10, 0) };
                var cancelButton = new Button { Content = "Mégse", Width = 75, Height = 30 };

                bool? dialogResult = null;
                okButton.Click += (s, args) => { dialogResult = true; inputWindow.Close(); };
                cancelButton.Click += (s, args) => { dialogResult = false; inputWindow.Close(); };

                buttonPanel.Children.Add(okButton);
                buttonPanel.Children.Add(cancelButton);
                panel.Children.Add(buttonPanel);

                inputWindow.Content = panel;
                inputWindow.ShowDialog();

                if (dialogResult == true && !string.IsNullOrWhiteSpace(textBox.Text))
                {
                    await ProcessRequestAsync(request.Id, "Rejected", textBox.Text.Trim());
                }
            }
        }

        private void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PasswordResetRequestDto request)
            {
                var details = $"Kérelem részletei:\n\n" +
                             $"ID: {request.Id}\n" +
                             $"Felhasználó: {request.UserDisplayName}\n" +
                             $"E-mail: {request.UserEmail}\n" +
                             $"Kérelem dátuma: {request.RequestDate:yyyy.MM.dd HH:mm}\n" +
                             $"Állapot: {request.Status}\n" +
                             $"Forrás: {request.RequestSource}\n";

                if (!string.IsNullOrEmpty(request.ProcessedByAdminName))
                {
                    details += $"Feldolgozta: {request.ProcessedByAdminName}\n";
                    details += $"Feldolgozás dátuma: {request.ProcessedDate:yyyy.MM.dd HH:mm}\n";
                }

                if (!string.IsNullOrEmpty(request.AdminNotes))
                {
                    details += $"Admin jegyzet: {request.AdminNotes}";
                }

                DXMessageBox.Show(details, "Kérelem részletei", MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        private async Task ProcessRequestAsync(long requestId, string action, string notes)
        {
            _isLoading = true;
            StatusLabel.Text = "Feldolgozás...";
            
            try
            {
                // TODO: API hívás implementálása
                // var success = await _apiClient.ProcessPasswordResetRequestAsync(requestId, action, notes);
                
                // Szimulált siker
                await Task.Delay(1000);
                var success = true;
                
                if (success)
                {
                    DXMessageBox.Show("Kérelem sikeresen feldolgozva!", "Siker", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadRequestsAsync();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt a kérelem feldolgozása során!", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoading = false;
                StatusLabel.Text = "Készen";
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    public class PasswordResetRequestDto
    {
        public long Id { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserDisplayName { get; set; } = string.Empty;
        public DateTime RequestDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ProcessedByAdminName { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public string? AdminNotes { get; set; }
        public string RequestSource { get; set; } = string.Empty;
    }
}