<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DevExtreme.AspNet.Core" Version="25.1.3" />
    <PackageReference Include="DevExtreme.AspNet.Data" Version="5.1.0" />
    <PackageReference Include="BCrypt.Net-Next" Version="4.0.3" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.7" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="6.0.2" />
    <PackageReference Include="Microsoft.IdentityModel.Tokens" Version="8.13.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.3" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="8.13.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\omsnext.shared\omsnext.shared.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="DevExpress.Data.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Data.v25.1.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Xpo.v25.1">
      <HintPath>..\..\..\..\..\..\Program Files\DevExpress 25.1\Components\Bin\NetCore\DevExpress.Xpo.v25.1.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
