﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;
using omsnext.core.Models;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class KtforgevUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<KtforgevDto> _ktforgevData;
        private bool _isLoading;
        private int _currentPage = 0;
        private int _pageSize = 100;
        private int _totalRecords = 0;
        private string? _currentSortField;
        private string? _currentSortDirection = "asc";
        private string? _currentFilterField;
        private string? _currentFilterValue;

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<KtforgevDto> KtforgevData
        {
            get => _ktforgevData;
            set
            {
                _ktforgevData = value;
                OnPropertyChanged(nameof(KtforgevData));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
                StatusLabel.Text = value ? "Betöltés..." : "Készen";
            }
        }

        public KtforgevUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _ktforgevData = new ObservableCollection<KtforgevDto>();
            
            DataContext = this;
            KtforgevGrid.ItemsSource = KtforgevData;
            
            // Grid események
            //TableView.CustomColumnSort += TableView_CustomColumnSort;
            
            Loaded += KtforgevUserControl_Loaded;
        }

        private async void KtforgevUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async Task LoadDataAsync()
        {
            if (IsLoading) return;

            IsLoading = true;
            try
            {
                var skip = _currentPage * _pageSize;
                var result = await _apiClient.GetKtforgevDataAsync(
                    skip: skip,
                    take: _pageSize,
                    sortField: _currentSortField,
                    sortDirection: _currentSortDirection,
                    filterField: _currentFilterField,
                    filterValue: _currentFilterValue,
                    filterOperator: "contains");

                if (result != null)
                {
                    KtforgevData.Clear();
                    foreach (var item in result.Data)
                    {
                        KtforgevData.Add(item);
                    }

                    _totalRecords = result.TotalCount;
                    UpdateStatusLabels();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt az adatok betöltése során.", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateStatusLabels()
        {
            RecordCountLabel.Text = $"Rekordok: {_totalRecords:N0}";
            var startRecord = _currentPage * _pageSize + 1;
            var endRecord = Math.Min((_currentPage + 1) * _pageSize, _totalRecords);
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            PageInfoLabel.Text = $"Oldal: {_currentPage + 1}/{totalPages} | Rekordok: {startRecord}-{endRecord}";
        }

        private async void ApplyFilterButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = FilterFieldComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null && !string.IsNullOrWhiteSpace(FilterValueTextBox.Text))
            {
                _currentFilterField = selectedItem.Tag?.ToString();
                _currentFilterValue = FilterValueTextBox.Text.Trim();
                _currentPage = 0; // Reset to first page
                await LoadDataAsync();
            }
            else
            {
                DXMessageBox.Show("Kérjük válasszon szűrő mezőt és adjon meg értéket!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            FilterFieldComboBox.SelectedIndex = -1;
            FilterValueTextBox.Clear();
            _currentFilterField = null;
            _currentFilterValue = null;
            _currentPage = 0;
            await LoadDataAsync();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync();
        }

        private async void TableView_CustomColumnSort(object sender, CustomColumnSortEventArgs e)
        {
            // Disable default sorting as we handle it server-side
            e.Handled = true;
            
            _currentSortField = e.Column.FieldName;
            //_currentSortDirection = e.SortOrder == ColumnSortOrder.Ascending ? "asc" : "desc";
            _currentPage = 0;
            
            await LoadDataAsync();
        }

        // Pagination methods (optional, for future use)
        public async Task GoToNextPageAsync()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            if (_currentPage < totalPages - 1)
            {
                _currentPage++;
                await LoadDataAsync();
            }
        }

        public async Task GoToPreviousPageAsync()
        {
            if (_currentPage > 0)
            {
                _currentPage--;
                await LoadDataAsync();
            }
        }

        public async Task GoToPageAsync(int pageNumber)
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
            if (pageNumber >= 0 && pageNumber < totalPages)
            {
                _currentPage = pageNumber;
                await LoadDataAsync();
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}