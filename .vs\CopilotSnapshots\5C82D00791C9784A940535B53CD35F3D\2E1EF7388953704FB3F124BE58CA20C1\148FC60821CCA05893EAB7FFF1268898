﻿<UserControl x:Class="omsnext.wpf.KtforgevUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Fejléc és szűrő rész -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="10" VerticalAlignment="Center">
            <TextBlock Text="Ktforgev adatok" FontSize="20" FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,20,0"/>
            
            <TextBlock Text="Szűrő mező:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <ComboBox x:Name="FilterFieldComboBox" Width="120" VerticalAlignment="Center" Margin="0,0,10,0">
                <ComboBoxItem Content="hiv_szam" Tag="hiv_szam"/>
                <ComboBoxItem Content="tk" Tag="tk"/>
                <ComboBoxItem Content="fkv_1" Tag="fkv_1"/>
                <ComboBoxItem Content="meg_nev" Tag="meg_nev"/>
                <ComboBoxItem Content="par_kod" Tag="par_kod"/>
                <ComboBoxItem Content="ert_ek" Tag="ert_ek"/>
            </ComboBox>
            
            <TextBlock Text="Érték:" VerticalAlignment="Center" Margin="10,0,5,0"/>
            <TextBox x:Name="FilterValueTextBox" Width="150" VerticalAlignment="Center" Margin="0,0,10,0"/>
            
            <Button x:Name="ApplyFilterButton" Content="Szűrés" Click="ApplyFilterButton_Click" Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="ClearFilterButton" Content="Törlés" Click="ClearFilterButton_Click" Margin="0,0,10,0" Padding="10,5"/>
            <Button x:Name="RefreshButton" Content="Frissítés" Click="RefreshButton_Click" Padding="10,5"/>
        </StackPanel>

        <!-- Standard WPF DataGrid -->
        <DataGrid x:Name="KtforgevGrid" Grid.Row="1" Margin="10"
                  AutoGenerateColumns="False"
                  CanUserAddRows="False"
                  CanUserDeleteRows="False"
                  IsReadOnly="True"
                  GridLinesVisibility="Horizontal"
                  HeadersVisibility="Column"
                  SelectionMode="Single"
                  VirtualizingPanel.IsVirtualizing="True"
                  VirtualizingPanel.VirtualizationMode="Recycling"
                  EnableRowVirtualization="True"
                  ScrollViewer.CanContentScroll="True">
            <DataGrid.Columns>
                <DataGridTextColumn Header="ID" Binding="{Binding id}" Width="80"/>
                <DataGridTextColumn Header="Hivatkozási szám" Binding="{Binding hiv_szam}" Width="150"/>
                <DataGridTextColumn Header="T/K" Binding="{Binding tk}" Width="50"/>
                <DataGridTextColumn Header="FKV 1" Binding="{Binding fkv_1}" Width="100"/>
                <DataGridTextColumn Header="Deviza nem" Binding="{Binding dev_nem}" Width="80"/>
                <DataGridTextColumn Header="Deviza érték" Binding="{Binding dev_ert, StringFormat=N2}" Width="100"/>
                <DataGridTextColumn Header="Megnevezés" Binding="{Binding meg_nev}" Width="200"/>
                <DataGridTextColumn Header="Érték" Binding="{Binding ert_ek, StringFormat=N2}" Width="120"/>
                <DataGridTextColumn Header="Előjeles érték" Binding="{Binding ert_ek_signed, StringFormat=N2}" Width="120"/>
                <DataGridTextColumn Header="Partner kód" Binding="{Binding par_kod}" Width="100"/>
                <DataGridTextColumn Header="Létrehozva" Binding="{Binding created_at, StringFormat=yyyy-MM-dd}" Width="120"/>
            </DataGrid.Columns>
        </DataGrid>

        <!-- Státusz sor -->
        <StatusBar Grid.Row="2" Height="25">
            <StatusBarItem>
                <TextBlock x:Name="StatusLabel" Text="Készen"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="RecordCountLabel" Text="Rekordok: 0"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="10,0"/>
                    <TextBlock x:Name="PageInfoLabel" Text=""/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>
