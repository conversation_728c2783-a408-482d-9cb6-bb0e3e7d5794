﻿<UserControl x:Class="omsnext.wpf.AdminDashboardUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
             xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Ribbon Control -->
        <dxr:RibbonControl Grid.Row="0" RibbonStyle="Office2019">
            <dxr:RibbonDefaultPageCategory>
                <dxr:RibbonPage Caption="Admin Dashboard" Glyph="{dx:DXImage SvgImages/Business Objects/BO_User.svg}">
                    
                    <!-- Kérelem műveletek -->
                    <dxr:RibbonPageGroup Caption="Kérelem Műveletek">
                        <dxb:BarButtonItem x:Name="ApproveRibbonButton" 
                                         Content="Jóváhagyás" 
                                         LargeGlyph="{dx:DXImage SvgImages/Icon Builder/Actions_Check.svg}"
                                         ItemClick="ApproveRibbonButton_Click"
                                         Hint="Kiválasztott kérelem jóváhagyása"/>
                        
                        <dxb:BarButtonItem x:Name="RejectRibbonButton" 
                                         Content="Elutasítás" 
                                         LargeGlyph="{dx:DXImage SvgImages/Icon Builder/Actions_Delete.svg}"
                                         ItemClick="RejectRibbonButton_Click"
                                         Hint="Kiválasztott kérelem elutasítása"/>
                        
                        <dxb:BarButtonItem x:Name="ViewDetailsRibbonButton" 
                                         Content="Részletek" 
                                         LargeGlyph="{dx:DXImage SvgImages/Outlook Inspired/Show.svg}"
                                         ItemClick="ViewDetailsRibbonButton_Click"
                                         Hint="Kérelem részletek megtekintése"/>
                    </dxr:RibbonPageGroup>
                    
                    <!-- Adatok kezelése -->
                    <dxr:RibbonPageGroup Caption="Adatok">
                        <dxb:BarButtonItem x:Name="RefreshRibbonButton" 
                                         Content="Frissítés" 
                                         LargeGlyph="{dx:DXImage SvgImages/Icon Builder/Actions_Refresh.svg}"
                                         ItemClick="RefreshRibbonButton_Click"
                                         Hint="Adatok frissítése"/>
                        
                        <dxb:BarButtonItem x:Name="ExportRibbonButton" 
                                         Content="Export" 
                                         LargeGlyph="{dx:DXImage SvgImages/Export/ExportToXLSX.svg}"
                                         ItemClick="ExportRibbonButton_Click"
                                         Hint="Adatok exportálása Excel-be"/>
                    </dxr:RibbonPageGroup>
                    
                    <!-- Szűrés és keresés -->
                    <dxr:RibbonPageGroup Caption="Szűrés">
                        <dxb:BarEditItem x:Name="StatusFilterRibbonEdit" 
                                       Content="Állapot szűrő:" 
                                       EditWidth="120">
                            <dxb:BarEditItem.EditSettings>
                                <dx:ComboBoxEditSettings>
                                    <dx:ComboBoxEditSettings.Items>
                                        <dx:ComboBoxEditItem DisplayMember="Minden állapot" Value=""/>
                                        <dx:ComboBoxEditItem DisplayMember="Függőben" Value="Pending"/>
                                        <dx:ComboBoxEditItem DisplayMember="Jóváhagyott" Value="Approved"/>
                                        <dx:ComboBoxEditItem DisplayMember="Elutasított" Value="Rejected"/>
                                        <dx:ComboBoxEditItem DisplayMember="Befejezett" Value="Completed"/>
                                    </dx:ComboBoxEditSettings.Items>
                                </dx:ComboBoxEditSettings>
                            </dxb:BarEditItem.EditSettings>
                        </dxb:BarEditItem>
                        
                        <dxb:BarButtonItem x:Name="ClearFilterRibbonButton" 
                                         Content="Szűrő törlése" 
                                         Glyph="{dx:DXImage SvgImages/Icon Builder/Actions_Clear.svg}"
                                         ItemClick="ClearFilterRibbonButton_Click"
                                         Hint="Összes szűrő törlése"/>
                    </dxr:RibbonPageGroup>
                    
                    <!-- Tömeges műveletek -->
                    <dxr:RibbonPageGroup Caption="Tömeges Műveletek">
                        <dxb:BarButtonItem x:Name="BulkApproveRibbonButton" 
                                         Content="Összes jóváhagyása" 
                                         LargeGlyph="{dx:DXImage SvgImages/Icon Builder/Actions_CheckCircled.svg}"
                                         ItemClick="BulkApproveRibbonButton_Click"
                                         Hint="Összes függő kérelem jóváhagyása"/>
                        
                        <dxb:BarButtonItem x:Name="BulkRejectRibbonButton" 
                                         Content="Összes elutasítása" 
                                         LargeGlyph="{dx:DXImage SvgImages/Icon Builder/Actions_DeleteCircled.svg}"
                                         ItemClick="BulkRejectRibbonButton_Click"
                                         Hint="Összes függő kérelem elutasítása"/>
                    </dxr:RibbonPageGroup>
                    
                </dxr:RibbonPage>
                
                <!-- Beállítások lap -->
                <dxr:RibbonPage Caption="Beállítások" Glyph="{dx:DXImage SvgImages/Hybrid/Settings.svg}">
                    <dxr:RibbonPageGroup Caption="Megjelenítés">
                        <dxb:BarCheckItem x:Name="AutoRefreshRibbonCheck" 
                                        Content="Auto-frissítés" 
                                        Glyph="{dx:DXImage SvgImages/Icon Builder/Actions_Refresh2.svg}"
                                        CheckedChanged="AutoRefreshRibbonCheck_CheckedChanged"
                                        Hint="Automatikus adatfrissítés be/kikapcsolása"/>
                        
                        <dxb:BarEditItem x:Name="RefreshIntervalRibbonEdit" 
                                       Content="Frissítési időköz (mp):" 
                                       EditWidth="80">
                            <dxb:BarEditItem.EditSettings>
                                <dx:SpinEditSettings Minimum="5" Maximum="300" Increment="5"/>
                            </dxb:BarEditItem.EditSettings>
                        </dxb:BarEditItem>
                    </dxr:RibbonPageGroup>
                    
                    <dxr:RibbonPageGroup Caption="Email Értesítések">
                        <dxb:BarCheckItem x:Name="EmailNotificationsRibbonCheck" 
                                        Content="Email értesítések" 
                                        Glyph="{dx:DXImage SvgImages/Business Objects/BO_Email.svg}"
                                        Hint="Admin email értesítések be/kikapcsolása"/>
                    </dxr:RibbonPageGroup>
                </dxr:RibbonPage>
            </dxr:RibbonDefaultPageCategory>
        </dxr:RibbonControl>

        <!-- Grid Control -->
        <dxg:GridControl x:Name="RequestsGrid" Grid.Row="1" Margin="10">
            <dxg:GridControl.Columns>
                <dxg:GridColumn FieldName="Id" Header="ID" Width="60" AllowSorting="True"/>
                <dxg:GridColumn FieldName="UserEmail" Header="Felhasználó E-mail" Width="200" AllowSorting="True"/>
                <dxg:GridColumn FieldName="UserDisplayName" Header="Felhasználó Neve" Width="180" AllowSorting="True"/>
                <dxg:GridColumn FieldName="RequestDate" Header="Kérelem Dátuma" Width="140" AllowSorting="True">
                    <dxg:GridColumn.EditSettings>
                        <dx:DateEditSettings DisplayFormat="yyyy.MM.dd HH:mm"/>
                    </dxg:GridColumn.EditSettings>
                </dxg:GridColumn>
                <dxg:GridColumn FieldName="Status" Header="Állapot" Width="120" AllowSorting="True">
                    <dxg:GridColumn.CellTemplate>
                        <DataTemplate>
                            <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Value}" Value="Pending">
                                                <Setter Property="Background" Value="#FEF3C7"/>
                                                <Setter Property="BorderBrush" Value="#F59E0B"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Approved">
                                                <Setter Property="Background" Value="#D1FAE5"/>
                                                <Setter Property="BorderBrush" Value="#10B981"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Rejected">
                                                <Setter Property="Background" Value="#FEE2E2"/>
                                                <Setter Property="BorderBrush" Value="#EF4444"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Completed">
                                                <Setter Property="Background" Value="#E0E7FF"/>
                                                <Setter Property="BorderBrush" Value="#6366F1"/>
                                                <Setter Property="BorderThickness" Value="1"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="{Binding Value}" 
                                         FontWeight="SemiBold" 
                                         FontSize="11"
                                         HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </dxg:GridColumn.CellTemplate>
                </dxg:GridColumn>
                <dxg:GridColumn FieldName="ProcessedByAdminName" Header="Feldolgozó Admin" Width="150" AllowSorting="True"/>
                <dxg:GridColumn FieldName="ProcessedDate" Header="Feldolgozás Dátuma" Width="140" AllowSorting="True">
                    <dxg:GridColumn.EditSettings>
                        <dx:DateEditSettings DisplayFormat="yyyy.MM.dd HH:mm"/>
                    </dxg:GridColumn.EditSettings>
                </dxg:GridColumn>
                <dxg:GridColumn FieldName="RequestSource" Header="Forrás" Width="80" AllowSorting="True"/>
            </dxg:GridControl.Columns>
            
            <dxg:GridControl.View>
                <dxg:TableView x:Name="TableView" 
                             AllowSorting="True" 
                             AllowGrouping="True" 
                             AllowColumnFiltering="True"
                             ShowGroupPanel="True"
                             AutoWidth="True"
                             NavigationStyle="Row"
                             ShowVerticalLines="False"
                             ShowHorizontalLines="True"
                             AllowPerPixelScrolling="True"
                             FocusedRowChanged="TableView_FocusedRowChanged"/>
            </dxg:GridControl.View>
        </dxg:GridControl>

        <!-- Enhanced Status Bar -->
        <StatusBar Grid.Row="2" Height="35" Background="#F8F9FA">
            <StatusBarItem>
                <StackPanel Orientation="Horizontal">
                    <dx:LoadingIndicator x:Name="LoadingIndicator" 
                                       Width="16" Height="16" 
                                       Margin="0,0,8,0"
                                       Visibility="Collapsed"/>
                    <TextBlock x:Name="StatusLabel" Text="Készen" FontWeight="Medium"/>
                </StackPanel>
            </StatusBarItem>
            
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="RecordCountLabel" Text="Kérelmek: 0" FontWeight="Medium"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="15,0"/>
                    
                    <Border Background="#FEF3C7" CornerRadius="8" Padding="6,2" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8" Fill="#F59E0B" Margin="0,0,4,0"/>
                            <TextBlock Text="Függőben:" FontSize="11" FontWeight="Medium"/>
                            <TextBlock x:Name="PendingCountLabel" Text="0" FontWeight="Bold" Margin="4,0,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <Border Background="#D1FAE5" CornerRadius="8" Padding="6,2" Margin="5,0">
                        <StackPanel Orientation="Horizontal">
                            <Ellipse Width="8" Height="8" Fill="#10B981" Margin="0,0,4,0"/>
                            <TextBlock Text="Befejezett:" FontSize="11" FontWeight="Medium"/>
                            <TextBlock x:Name="CompletedCountLabel" Text="0" FontWeight="Bold" Margin="4,0,0,0"/>
                        </StackPanel>
                    </Border>
                    
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="15,0"/>
                    <TextBlock x:Name="LastRefreshLabel" Text="Utolsó frissítés: -" FontSize="11" Foreground="Gray"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>