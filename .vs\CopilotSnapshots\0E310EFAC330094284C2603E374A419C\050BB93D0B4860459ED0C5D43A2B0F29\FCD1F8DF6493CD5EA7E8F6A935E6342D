﻿using System.Windows;

namespace omsnext.wpf;

public partial class LoginWindow : Window
{
    private readonly ApiClient _apiClient;

    public LoginWindow()
    {
        InitializeComponent();
        _apiClient = new ApiClient();
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        // Hide error message and show loading
        ErrorMessage.Visibility = Visibility.Collapsed;
        LoadingProgressBar.Visibility = Visibility.Visible;
        LoginButton.IsEnabled = false;

        try
        {
            var email = EmailTextBox.Text.Trim();
            var password = PasswordBox.Password;

            if (string.IsNullOrEmpty(email) || string.IsNullOrEmpty(password))
            {
                ShowError("<PERSON><PERSON>rj<PERSON><PERSON>, töltse ki az összes mezőt!");
                return;
            }

            var result = await _apiClient.LoginAsync(email, password);

            if (result != null && !string.IsNullOrEmpty(result.Token))
            {
                // Successful login - open dashboard
                var dashboard = new DashboardWindow(result);
                dashboard.Show();
                this.Close();
            }
            else
            {
                ShowError("Hibás e-mail cím vagy jelsz<PERSON>!");
            }
        }
        catch (Exception ex)
        {
            ShowError($"Hiba történt: {ex.Message}");
        }
        finally
        {
            LoadingProgressBar.Visibility = Visibility.Collapsed;
            LoginButton.IsEnabled = true;
        }
    }

    private void ShowError(string message)
    {
        ErrorMessage.Text = message;
        ErrorMessage.Visibility = Visibility.Visible;
    }
}