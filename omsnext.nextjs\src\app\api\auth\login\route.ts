import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

export async function POST(request: NextRequest) {
  const { email, password } = await request.json();

  // Fontos: Az API_URL környezeti változót be kell <PERSON>ll<PERSON>tani a .env.local fájlban.
  // Pl: API_URL=http://localhost:5292
  if (!process.env.API_URL) {
    return NextResponse.json(
      { error: "API URL is not configured." },
      { status: 500 }
    );
  }

  try {
    const apiResponse = await fetch(process.env.API_URL + "/api/auth/login", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ email, password }),
    });

    if (!apiResponse.ok) {
      const errorData = await apiResponse.json().catch(() => ({
        error: "Hiba történt a bejelentkezés során.",
      }));
      return NextResponse.json(
        { error: errorData.error || "Ismeretlen hiba" },
        { status: apiResponse.status }
      );
    }

    const data = await apiResponse.json();
    const { token, user } = data;

    // Cookie beállítása (httpOnly, secure, sameSite)
    const response = NextResponse.json({ success: true, user });
    response.cookies.set("oms_token", token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      path: "/",
      maxAge: 60 * 60 * 8, // 8 óra
    });

    return response;
  } catch (error) {
    console.error("Login API call failed:", error);
    return NextResponse.json({ error: "Szerver hiba." }, { status: 500 });
  }
}
