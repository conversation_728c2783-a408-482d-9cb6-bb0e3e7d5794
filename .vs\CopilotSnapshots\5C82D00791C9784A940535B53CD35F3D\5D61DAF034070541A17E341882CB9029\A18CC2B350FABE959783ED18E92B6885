﻿using DevExpress.Xpo;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using omsnext.api.Helpers;
using omsnext.shared.DTO;
using omsnext.shared.Models;
using System.Linq;  
using System.Threading.Tasks;

namespace omsnext.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AuthController : ControllerBase 
    {
        private readonly UnitOfWork _uow;
        private readonly ILogger<AuthController> _logger;
        private readonly TokenHelper _tokenHelper;

        public AuthController(UnitOfWork uow, ILogger<AuthController> logger, TokenHelper tokenHelper)
        {
            _uow = uow;
            _logger = logger;
            _tokenHelper = tokenHelper;
        }

        [HttpPost("login")]
        public async Task<IActionResult> Login([FromBody] LoginDto model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { error = "Hibás adatok." });

            try
            {
                var user = await _uow.Query<User>()
                    .FirstOrDefaultAsync(u => u.Email == model.Email && u.IsActive);

                if (user == null || !BCrypt.Net.BCrypt.Verify(model.Password, user.PasswordHash))
                    return Unauthorized(new { error = "Hibás e-mail vagy jelszó." });

                var token = TokenHelper.GenerateToken(user);
                if (string.IsNullOrEmpty(token))
                    return StatusCode(500, new { error = "Token generálási hiba." });

                // Get roles and permissions for response
                var roles = user.Roles.Select(r => r.Name).ToList();
                var permissions = user.Roles
                    .SelectMany(r => r.Permissions)
                    .Select(p => p.Name)
                    .Distinct()
                    .ToList();

                return Ok(new
                {
                    token,
                    user = new { user.Oid, user.Email, user.DisplayName, roles, permissions }
                });
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, "Hiba a bejelentkezés során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }

        [HttpPost("notify-admin-password-reset")]
        public async Task<IActionResult> NotifyAdminPasswordReset([FromBody] PasswordResetNotificationDto model)
        {
            if (!ModelState.IsValid)
                return BadRequest(new { error = "Hibás adatok." });

            try
            {
                // Ellenőrizze, hogy létezik-e a felhasználó
                var user = await _uow.Query<User>()
                    .FirstOrDefaultAsync(u => u.Email == model.Email && u.IsActive);

                if (user == null)
                    return BadRequest(new { error = "Nem található felhasználó ezzel az e-mail címmel." });

                // Itt implementálhatja az admin értesítés logikáját:
                // 1. E-mail küldés az adminisztrátornak
                // 2. Jelszó visszaállítási kérelem mentése az adatbázisba
                // 3. Log bejegyzés létrehozása

                _logger.LogInformation($"Jelszó visszaállítási kérelem: {model.Email} - {DateTime.UtcNow}");

                // Példa: egyszerű admin értesítés szimulálása
                await Task.Delay(1000); // Szimulált hálózati késleltetés

                return Ok(new { message = "Adminisztrátor értesítése sikeres.", timestamp = DateTime.UtcNow });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Hiba az admin értesítése során.");
                return StatusCode(500, new { error = "Váratlan hiba történt." });
            }
        }
    }
}
