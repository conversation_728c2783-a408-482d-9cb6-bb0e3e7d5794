{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_data_controller.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_data_controller.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    noop\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nconst DataControllerMock = {\r\n    load: () => Deferred().reject(),\r\n    loadSingle: () => Deferred().reject(),\r\n    loadFromStore: () => Deferred().reject(),\r\n    loadNextPage: () => Deferred().reject(),\r\n    loadOptions: noop,\r\n    userData: noop,\r\n    cancel: noop,\r\n    cancelAll: noop,\r\n    filter: noop,\r\n    addSearchFilter: noop,\r\n    group: noop,\r\n    paginate: noop,\r\n    pageSize: noop,\r\n    pageIndex: noop,\r\n    resetDataSourcePageIndex: noop,\r\n    totalCount: noop,\r\n    isLastPage: noop,\r\n    isLoading: noop,\r\n    isLoaded: noop,\r\n    searchValue: noop,\r\n    searchOperation: noop,\r\n    searchExpr: noop,\r\n    select: noop,\r\n    key: noop,\r\n    keyOf: noop,\r\n    store: noop,\r\n    items: noop,\r\n    applyMapFunction: noop,\r\n    getDataSource: noop,\r\n    reload: noop,\r\n    on: noop,\r\n    off: noop\r\n};\r\nclass DataController {\r\n    constructor(dataSource) {\r\n        if (!dataSource) {\r\n            return DataControllerMock\r\n        }\r\n        this._dataSource = dataSource\r\n    }\r\n    load() {\r\n        return this._dataSource.load()\r\n    }\r\n    loadSingle(propName, propValue) {\r\n        if (arguments.length < 2) {\r\n            propValue = propName;\r\n            propName = this.key()\r\n        }\r\n        return this._dataSource.loadSingle(propName, propValue)\r\n    }\r\n    loadFromStore(loadOptions) {\r\n        return this.store().load(loadOptions)\r\n    }\r\n    loadNextPage() {\r\n        this.pageIndex(1 + this.pageIndex());\r\n        return this.load()\r\n    }\r\n    loadOptions() {\r\n        return this._dataSource.loadOptions()\r\n    }\r\n    userData() {\r\n        return this._dataSource._userData\r\n    }\r\n    cancel(operationId) {\r\n        this._dataSource.cancel(operationId)\r\n    }\r\n    cancelAll() {\r\n        this._dataSource.cancelAll()\r\n    }\r\n    filter(filter) {\r\n        return this._dataSource.filter(filter)\r\n    }\r\n    addSearchFilter(storeLoadOptions) {\r\n        this._dataSource._addSearchFilter(storeLoadOptions)\r\n    }\r\n    group(group) {\r\n        return this._dataSource.group(group)\r\n    }\r\n    paginate() {\r\n        return this._dataSource.paginate()\r\n    }\r\n    pageSize() {\r\n        return this._dataSource._pageSize\r\n    }\r\n    pageIndex(pageIndex) {\r\n        return this._dataSource.pageIndex(pageIndex)\r\n    }\r\n    resetDataSourcePageIndex() {\r\n        if (this.pageIndex()) {\r\n            this.pageIndex(0);\r\n            this.load()\r\n        }\r\n    }\r\n    totalCount() {\r\n        return this._dataSource.totalCount()\r\n    }\r\n    isLastPage() {\r\n        return this._dataSource.isLastPage() || !this._dataSource._pageSize\r\n    }\r\n    isLoading() {\r\n        return this._dataSource.isLoading()\r\n    }\r\n    isLoaded() {\r\n        return this._dataSource.isLoaded()\r\n    }\r\n    searchValue(value) {\r\n        if (!arguments.length) {\r\n            return this._dataSource.searchValue()\r\n        }\r\n        return this._dataSource.searchValue(value)\r\n    }\r\n    searchOperation(operation) {\r\n        return this._dataSource.searchOperation(operation)\r\n    }\r\n    searchExpr(expr) {\r\n        if (!arguments.length) {\r\n            return this._dataSource.searchExpr()\r\n        }\r\n        return this._dataSource.searchExpr(expr)\r\n    }\r\n    select() {\r\n        return this._dataSource.select(...arguments)\r\n    }\r\n    key() {\r\n        return this._dataSource.key()\r\n    }\r\n    keyOf(item) {\r\n        return this.store().keyOf(item)\r\n    }\r\n    store() {\r\n        return this._dataSource.store()\r\n    }\r\n    items() {\r\n        return this._dataSource.items()\r\n    }\r\n    applyMapFunction(data) {\r\n        return this._dataSource._applyMapFunction(data)\r\n    }\r\n    getDataSource() {\r\n        return this._dataSource || null\r\n    }\r\n    reload() {\r\n        return this._dataSource.reload()\r\n    }\r\n    on(event, handler) {\r\n        this._dataSource.on(event, handler)\r\n    }\r\n    off(event, handler) {\r\n        this._dataSource.off(event, handler)\r\n    }\r\n}\r\nexport default DataController;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;;;AAGA,MAAM,qBAAqB;IACvB,MAAM,IAAM,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM;IAC7B,YAAY,IAAM,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM;IACnC,eAAe,IAAM,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM;IACtC,cAAc,IAAM,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,MAAM;IACrC,aAAa,+KAAA,CAAA,OAAI;IACjB,UAAU,+KAAA,CAAA,OAAI;IACd,QAAQ,+KAAA,CAAA,OAAI;IACZ,WAAW,+KAAA,CAAA,OAAI;IACf,QAAQ,+KAAA,CAAA,OAAI;IACZ,iBAAiB,+KAAA,CAAA,OAAI;IACrB,OAAO,+KAAA,CAAA,OAAI;IACX,UAAU,+KAAA,CAAA,OAAI;IACd,UAAU,+KAAA,CAAA,OAAI;IACd,WAAW,+KAAA,CAAA,OAAI;IACf,0BAA0B,+KAAA,CAAA,OAAI;IAC9B,YAAY,+KAAA,CAAA,OAAI;IAChB,YAAY,+KAAA,CAAA,OAAI;IAChB,WAAW,+KAAA,CAAA,OAAI;IACf,UAAU,+KAAA,CAAA,OAAI;IACd,aAAa,+KAAA,CAAA,OAAI;IACjB,iBAAiB,+KAAA,CAAA,OAAI;IACrB,YAAY,+KAAA,CAAA,OAAI;IAChB,QAAQ,+KAAA,CAAA,OAAI;IACZ,KAAK,+KAAA,CAAA,OAAI;IACT,OAAO,+KAAA,CAAA,OAAI;IACX,OAAO,+KAAA,CAAA,OAAI;IACX,OAAO,+KAAA,CAAA,OAAI;IACX,kBAAkB,+KAAA,CAAA,OAAI;IACtB,eAAe,+KAAA,CAAA,OAAI;IACnB,QAAQ,+KAAA,CAAA,OAAI;IACZ,IAAI,+KAAA,CAAA,OAAI;IACR,KAAK,+KAAA,CAAA,OAAI;AACb;AACA,MAAM;IACF,YAAY,UAAU,CAAE;QACpB,IAAI,CAAC,YAAY;YACb,OAAO;QACX;QACA,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,OAAO;QACH,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI;IAChC;IACA,WAAW,QAAQ,EAAE,SAAS,EAAE;QAC5B,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,YAAY;YACZ,WAAW,IAAI,CAAC,GAAG;QACvB;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,UAAU;IACjD;IACA,cAAc,WAAW,EAAE;QACvB,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC;IAC7B;IACA,eAAe;QACX,IAAI,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC,SAAS;QACjC,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,cAAc;QACV,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW;IACvC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;IACrC;IACA,OAAO,WAAW,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IAC5B;IACA,YAAY;QACR,IAAI,CAAC,WAAW,CAAC,SAAS;IAC9B;IACA,OAAO,MAAM,EAAE;QACX,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;IACnC;IACA,gBAAgB,gBAAgB,EAAE;QAC9B,IAAI,CAAC,WAAW,CAAC,gBAAgB,CAAC;IACtC;IACA,MAAM,KAAK,EAAE;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC;IAClC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IACpC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;IACrC;IACA,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC;IACtC;IACA,2BAA2B;QACvB,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,IAAI;QACb;IACJ;IACA,aAAa;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;IACtC;IACA,aAAa;QACT,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS;IACvE;IACA,YAAY;QACR,OAAO,IAAI,CAAC,WAAW,CAAC,SAAS;IACrC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;IACpC;IACA,YAAY,KAAK,EAAE;QACf,IAAI,CAAC,UAAU,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW;QACvC;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC;IACxC;IACA,gBAAgB,SAAS,EAAE;QACvB,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC;IAC5C;IACA,WAAW,IAAI,EAAE;QACb,IAAI,CAAC,UAAU,MAAM,EAAE;YACnB,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU;QACtC;QACA,OAAO,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC;IACvC;IACA,SAAS;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI;IACtC;IACA,MAAM;QACF,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG;IAC/B;IACA,MAAM,IAAI,EAAE;QACR,OAAO,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC;IAC9B;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IACjC;IACA,QAAQ;QACJ,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IACjC;IACA,iBAAiB,IAAI,EAAE;QACnB,OAAO,IAAI,CAAC,WAAW,CAAC,iBAAiB,CAAC;IAC9C;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW,IAAI;IAC/B;IACA,SAAS;QACL,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM;IAClC;IACA,GAAG,KAAK,EAAE,OAAO,EAAE;QACf,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,OAAO;IAC/B;IACA,IAAI,KAAK,EAAE,OAAO,EAAE;QAChB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,OAAO;IAChC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/item.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/item.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Class from \"../../../core/class\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    attachInstanceToElement,\r\n    getInstanceByElement\r\n} from \"../../../core/utils/public_component\";\r\nconst INVISIBLE_STATE_CLASS = \"dx-state-invisible\";\r\nconst DISABLED_STATE_CLASS = \"dx-state-disabled\";\r\nconst ITEM_CONTENT_PLACEHOLDER_CLASS = \"dx-item-content-placeholder\";\r\nconst forcibleWatcher = (watchMethod, fn, callback) => {\r\n    const filteredCallback = (() => {\r\n        let oldValue;\r\n        return value => {\r\n            if (oldValue !== value) {\r\n                callback(value, oldValue);\r\n                oldValue = value\r\n            }\r\n        }\r\n    })();\r\n    return {\r\n        dispose: watchMethod(fn, filteredCallback),\r\n        force() {\r\n            filteredCallback(fn())\r\n        }\r\n    }\r\n};\r\nclass CollectionItem extends(Class.inherit({})) {\r\n    ctor($element, options, rawData) {\r\n        this._$element = $element;\r\n        this._options = options;\r\n        this._rawData = rawData;\r\n        attachInstanceToElement($element, this, this._dispose);\r\n        this._render()\r\n    }\r\n    _render() {\r\n        const $placeholder = $(\"<div>\").addClass(\"dx-item-content-placeholder\");\r\n        this._$element.append($placeholder);\r\n        this._watchers = [];\r\n        this._renderWatchers()\r\n    }\r\n    _renderWatchers() {\r\n        this._startWatcher(\"disabled\", this._renderDisabled.bind(this));\r\n        this._startWatcher(\"visible\", this._renderVisible.bind(this))\r\n    }\r\n    _startWatcher(field, render) {\r\n        const rawData = this._rawData;\r\n        const exprGetter = this._options.fieldGetter(field);\r\n        const watcher = forcibleWatcher(this._options.watchMethod(), (() => exprGetter(rawData)), ((value, oldValue) => {\r\n            this._dirty = true;\r\n            render(value, oldValue)\r\n        }));\r\n        this._watchers.push(watcher)\r\n    }\r\n    setDataField() {\r\n        this._dirty = false;\r\n        each(this._watchers, ((_, watcher) => {\r\n            watcher.force()\r\n        }));\r\n        return this._dirty\r\n    }\r\n    _renderDisabled(value, oldValue) {\r\n        this._$element.toggleClass(\"dx-state-disabled\", !!value);\r\n        this._$element.attr(\"aria-disabled\", !!value);\r\n        this._updateOwnerFocus(value)\r\n    }\r\n    _updateOwnerFocus(isDisabled) {\r\n        const ownerComponent = this._options.owner;\r\n        if (ownerComponent && isDisabled) {\r\n            ownerComponent._resetItemFocus(this._$element)\r\n        }\r\n    }\r\n    _renderVisible(value, oldValue) {\r\n        this._$element.toggleClass(\"dx-state-invisible\", void 0 !== value && !value)\r\n    }\r\n    _dispose() {\r\n        each(this._watchers, ((_, watcher) => {\r\n            watcher.dispose()\r\n        }))\r\n    }\r\n    static getInstance($element) {\r\n        return getInstanceByElement($element, this)\r\n    }\r\n}\r\nexport default CollectionItem;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AAAA;AAGA;AAAA;;;;;AAIA,MAAM,wBAAwB;AAC9B,MAAM,uBAAuB;AAC7B,MAAM,iCAAiC;AACvC,MAAM,kBAAkB,CAAC,aAAa,IAAI;IACtC,MAAM,mBAAmB,CAAC;QACtB,IAAI;QACJ,OAAO,CAAA;YACH,IAAI,aAAa,OAAO;gBACpB,SAAS,OAAO;gBAChB,WAAW;YACf;QACJ;IACJ,CAAC;IACD,OAAO;QACH,SAAS,YAAY,IAAI;QACzB;YACI,iBAAiB;QACrB;IACJ;AACJ;AACA,MAAM,uBAAuB,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC;IACxC,KAAK,QAAQ,EAAE,OAAO,EAAE,OAAO,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,yLAAA,CAAA,0BAAuB,AAAD,EAAE,UAAU,IAAI,EAAE,IAAI,CAAC,QAAQ;QACrD,IAAI,CAAC,OAAO;IAChB;IACA,UAAU;QACN,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACzC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;QACtB,IAAI,CAAC,SAAS,GAAG,EAAE;QACnB,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,IAAI,CAAC,aAAa,CAAC,YAAY,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;QAC7D,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI;IAC/D;IACA,cAAc,KAAK,EAAE,MAAM,EAAE;QACzB,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,aAAa,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC7C,MAAM,UAAU,gBAAgB,IAAI,CAAC,QAAQ,CAAC,WAAW,IAAK,IAAM,WAAW,UAAY,CAAC,OAAO;YAC/F,IAAI,CAAC,MAAM,GAAG;YACd,OAAO,OAAO;QAClB;QACA,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IACxB;IACA,eAAe;QACX,IAAI,CAAC,MAAM,GAAG;QACd,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,SAAS,EAAG,CAAC,GAAG;YACtB,QAAQ,KAAK;QACjB;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,gBAAgB,KAAK,EAAE,QAAQ,EAAE;QAC7B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,qBAAqB,CAAC,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;QACvC,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IACA,kBAAkB,UAAU,EAAE;QAC1B,MAAM,iBAAiB,IAAI,CAAC,QAAQ,CAAC,KAAK;QAC1C,IAAI,kBAAkB,YAAY;YAC9B,eAAe,eAAe,CAAC,IAAI,CAAC,SAAS;QACjD;IACJ;IACA,eAAe,KAAK,EAAE,QAAQ,EAAE;QAC5B,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,sBAAsB,KAAK,MAAM,SAAS,CAAC;IAC1E;IACA,WAAW;QACP,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,SAAS,EAAG,CAAC,GAAG;YACtB,QAAQ,OAAO;QACnB;IACJ;IACA,OAAO,YAAY,QAAQ,EAAE;QACzB,OAAO,CAAA,GAAA,yLAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,IAAI;IAC9C;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 280, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_collection_widget.edit.strategy.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_collection_widget.edit.strategy.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Class from \"../../../core/class\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    equalByValue\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    isRenderer\r\n} from \"../../../core/utils/type\";\r\nclass EditStrategy extends(Class.inherit({})) {\r\n    constructor(collectionWidget) {\r\n        super();\r\n        this._collectionWidget = collectionWidget\r\n    }\r\n    getIndexByItemData(value) {\r\n        return Class.abstract()\r\n    }\r\n    getItemDataByIndex(index) {\r\n        Class.abstract()\r\n    }\r\n    getKeysByItems(items) {\r\n        Class.abstract()\r\n    }\r\n    getItemsByKeys(keys, items) {\r\n        Class.abstract()\r\n    }\r\n    itemsGetter() {\r\n        Class.abstract()\r\n    }\r\n    getKeyByIndex(index) {\r\n        const resultIndex = this._denormalizeItemIndex(index);\r\n        return this.getKeysByItems([this.getItemDataByIndex(resultIndex)])[0]\r\n    }\r\n    _equalKeys(key1, key2) {\r\n        if (this._collectionWidget._isKeySpecified()) {\r\n            return equalByValue(key1, key2)\r\n        }\r\n        return key1 === key2\r\n    }\r\n    beginCache() {\r\n        this._cache = {}\r\n    }\r\n    endCache() {\r\n        this._cache = null\r\n    }\r\n    getIndexByKey(key) {\r\n        return Class.abstract()\r\n    }\r\n    getNormalizedIndex(value) {\r\n        if (this._isNormalizedItemIndex(value)) {\r\n            return value\r\n        }\r\n        if (this._isItemIndex(value)) {\r\n            return this._normalizeItemIndex(value)\r\n        }\r\n        if (this._isNode(value)) {\r\n            return this._getNormalizedItemIndex(value)\r\n        }\r\n        return this._normalizeItemIndex(this.getIndexByItemData(value))\r\n    }\r\n    getIndex(value) {\r\n        if (this._isNormalizedItemIndex(value)) {\r\n            return this._denormalizeItemIndex(value)\r\n        }\r\n        if (this._isItemIndex(value)) {\r\n            return value\r\n        }\r\n        if (this._isNode(value)) {\r\n            return this._denormalizeItemIndex(this._getNormalizedItemIndex(value))\r\n        }\r\n        return this.getIndexByItemData(value)\r\n    }\r\n    getItemElement(value) {\r\n        if (this._isNormalizedItemIndex(value)) {\r\n            return this._getItemByNormalizedIndex(value)\r\n        }\r\n        if (this._isItemIndex(value)) {\r\n            return this._getItemByNormalizedIndex(this._normalizeItemIndex(value))\r\n        }\r\n        if (this._isNode(value)) {\r\n            return $(value)\r\n        }\r\n        const normalizedItemIndex = this._normalizeItemIndex(this.getIndexByItemData(value));\r\n        return this._getItemByNormalizedIndex(normalizedItemIndex)\r\n    }\r\n    _isNode(el) {\r\n        return domAdapter.isNode(el && isRenderer(el) ? el.get(0) : el)\r\n    }\r\n    deleteItemAtIndex(index) {\r\n        Class.abstract()\r\n    }\r\n    itemPlacementFunc(movingIndex, destinationIndex) {\r\n        return this._itemsFromSameParent(movingIndex, destinationIndex) && movingIndex < destinationIndex ? \"after\" : \"before\"\r\n    }\r\n    moveItemAtIndexToIndex(movingIndex, destinationIndex) {\r\n        Class.abstract()\r\n    }\r\n    _isNormalizedItemIndex(index) {\r\n        return \"number\" === typeof index && Math.round(index) === index\r\n    }\r\n    _isItemIndex(index) {\r\n        return Class.abstract()\r\n    }\r\n    _getNormalizedItemIndex(value) {\r\n        return Class.abstract()\r\n    }\r\n    _normalizeItemIndex(index) {\r\n        return Class.abstract()\r\n    }\r\n    _denormalizeItemIndex(index) {\r\n        return Class.abstract()\r\n    }\r\n    _getItemByNormalizedIndex(value) {\r\n        return Class.abstract()\r\n    }\r\n    _itemsFromSameParent(movingIndex, destinationIndex) {\r\n        return Class.abstract()\r\n    }\r\n}\r\nexport default EditStrategy;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AACA;AAAA;AAGA;AAAA;;;;;;AAGA,MAAM,qBAAqB,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC;IACtC,YAAY,gBAAgB,CAAE;QAC1B,KAAK;QACL,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,mBAAmB,KAAK,EAAE;QACtB,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,mBAAmB,KAAK,EAAE;QACtB,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,eAAe,KAAK,EAAE;QAClB,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,eAAe,IAAI,EAAE,KAAK,EAAE;QACxB,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,cAAc;QACV,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,cAAc,KAAK,EAAE;QACjB,MAAM,cAAc,IAAI,CAAC,qBAAqB,CAAC;QAC/C,OAAO,IAAI,CAAC,cAAc,CAAC;YAAC,IAAI,CAAC,kBAAkB,CAAC;SAAa,CAAC,CAAC,EAAE;IACzE;IACA,WAAW,IAAI,EAAE,IAAI,EAAE;QACnB,IAAI,IAAI,CAAC,iBAAiB,CAAC,eAAe,IAAI;YAC1C,OAAO,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,MAAM;QAC9B;QACA,OAAO,SAAS;IACpB;IACA,aAAa;QACT,IAAI,CAAC,MAAM,GAAG,CAAC;IACnB;IACA,WAAW;QACP,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,cAAc,GAAG,EAAE;QACf,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,mBAAmB,KAAK,EAAE;QACtB,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YACpC,OAAO;QACX;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC;QACpC;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;YACrB,OAAO,IAAI,CAAC,uBAAuB,CAAC;QACxC;QACA,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC;IAC5D;IACA,SAAS,KAAK,EAAE;QACZ,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YACpC,OAAO,IAAI,CAAC,qBAAqB,CAAC;QACtC;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC1B,OAAO;QACX;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;YACrB,OAAO,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,uBAAuB,CAAC;QACnE;QACA,OAAO,IAAI,CAAC,kBAAkB,CAAC;IACnC;IACA,eAAe,KAAK,EAAE;QAClB,IAAI,IAAI,CAAC,sBAAsB,CAAC,QAAQ;YACpC,OAAO,IAAI,CAAC,yBAAyB,CAAC;QAC1C;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,QAAQ;YAC1B,OAAO,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,mBAAmB,CAAC;QACnE;QACA,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ;YACrB,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACb;QACA,MAAM,sBAAsB,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,kBAAkB,CAAC;QAC7E,OAAO,IAAI,CAAC,yBAAyB,CAAC;IAC1C;IACA,QAAQ,EAAE,EAAE;QACR,OAAO,wJAAA,CAAA,UAAU,CAAC,MAAM,CAAC,MAAM,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,MAAM,GAAG,GAAG,CAAC,KAAK;IAChE;IACA,kBAAkB,KAAK,EAAE;QACrB,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,kBAAkB,WAAW,EAAE,gBAAgB,EAAE;QAC7C,OAAO,IAAI,CAAC,oBAAoB,CAAC,aAAa,qBAAqB,cAAc,mBAAmB,UAAU;IAClH;IACA,uBAAuB,WAAW,EAAE,gBAAgB,EAAE;QAClD,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,uBAAuB,KAAK,EAAE;QAC1B,OAAO,aAAa,OAAO,SAAS,KAAK,KAAK,CAAC,WAAW;IAC9D;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,wBAAwB,KAAK,EAAE;QAC3B,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,oBAAoB,KAAK,EAAE;QACvB,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,sBAAsB,KAAK,EAAE;QACzB,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,0BAA0B,KAAK,EAAE;QAC7B,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;IACA,qBAAqB,WAAW,EAAE,gBAAgB,EAAE;QAChD,OAAO,kJAAA,CAAA,UAAK,CAAC,QAAQ;IACzB;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_collection_widget.edit.strategy.plain.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_collection_widget.edit.strategy.plain.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport EditStrategy from \"./m_collection_widget.edit.strategy\";\r\nclass PlainEditStrategy extends EditStrategy {\r\n    _getPlainItems() {\r\n        return this._collectionWidget.option(\"items\") || []\r\n    }\r\n    getIndexByItemData(itemData) {\r\n        const keyOf = this._collectionWidget.keyOf.bind(this._collectionWidget);\r\n        if (keyOf) {\r\n            return this.getIndexByKey(keyOf(itemData))\r\n        }\r\n        return this._getPlainItems().indexOf(itemData)\r\n    }\r\n    getItemDataByIndex(index) {\r\n        return this._getPlainItems()[index]\r\n    }\r\n    deleteItemAtIndex(index) {\r\n        this._getPlainItems().splice(index, 1)\r\n    }\r\n    itemsGetter() {\r\n        return this._getPlainItems()\r\n    }\r\n    getKeysByItems(items) {\r\n        const keyOf = this._collectionWidget.keyOf.bind(this._collectionWidget);\r\n        let result = items;\r\n        if (keyOf) {\r\n            result = [];\r\n            for (let i = 0; i < items.length; i++) {\r\n                result.push(keyOf(items[i]))\r\n            }\r\n        }\r\n        return result\r\n    }\r\n    getIndexByKey(key) {\r\n        const cache = this._cache;\r\n        const keys = cache && cache.keys || this.getKeysByItems(this._getPlainItems());\r\n        if (cache && !cache.keys) {\r\n            cache.keys = keys\r\n        }\r\n        if (\"object\" === typeof key) {\r\n            for (let i = 0, {\r\n                    length: length\r\n                } = keys; i < length; i++) {\r\n                if (this._equalKeys(key, keys[i])) {\r\n                    return i\r\n                }\r\n            }\r\n        } else {\r\n            return keys.indexOf(key)\r\n        }\r\n        return -1\r\n    }\r\n    getItemsByKeys(keys, items) {\r\n        return (items || keys).slice()\r\n    }\r\n    moveItemAtIndexToIndex(movingIndex, destinationIndex) {\r\n        const items = this._getPlainItems();\r\n        const movedItemData = items[movingIndex];\r\n        items.splice(movingIndex, 1);\r\n        items.splice(destinationIndex, 0, movedItemData)\r\n    }\r\n    _isItemIndex(index) {\r\n        return \"number\" === typeof index && Math.round(index) === index\r\n    }\r\n    _getNormalizedItemIndex(itemElement) {\r\n        return this._collectionWidget._itemElements().index(itemElement)\r\n    }\r\n    _normalizeItemIndex(index) {\r\n        return index\r\n    }\r\n    _denormalizeItemIndex(index) {\r\n        return index\r\n    }\r\n    _getItemByNormalizedIndex(index) {\r\n        return index > -1 ? this._collectionWidget._itemElements().eq(index) : null\r\n    }\r\n    _itemsFromSameParent(firstIndex, secondIndex) {\r\n        return true\r\n    }\r\n}\r\nexport default PlainEditStrategy;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AACA,MAAM,0BAA0B,iNAAA,CAAA,UAAY;IACxC,iBAAiB;QACb,OAAO,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,YAAY,EAAE;IACvD;IACA,mBAAmB,QAAQ,EAAE;QACzB,MAAM,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB;QACtE,IAAI,OAAO;YACP,OAAO,IAAI,CAAC,aAAa,CAAC,MAAM;QACpC;QACA,OAAO,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC;IACzC;IACA,mBAAmB,KAAK,EAAE;QACtB,OAAO,IAAI,CAAC,cAAc,EAAE,CAAC,MAAM;IACvC;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,CAAC,cAAc,GAAG,MAAM,CAAC,OAAO;IACxC;IACA,cAAc;QACV,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,eAAe,KAAK,EAAE;QAClB,MAAM,QAAQ,IAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB;QACtE,IAAI,SAAS;QACb,IAAI,OAAO;YACP,SAAS,EAAE;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE;YAC9B;QACJ;QACA,OAAO;IACX;IACA,cAAc,GAAG,EAAE;QACf,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,OAAO,SAAS,MAAM,IAAI,IAAI,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,cAAc;QAC3E,IAAI,SAAS,CAAC,MAAM,IAAI,EAAE;YACtB,MAAM,IAAI,GAAG;QACjB;QACA,IAAI,aAAa,OAAO,KAAK;YACzB,IAAK,IAAI,IAAI,GAAG,EACR,QAAQ,MAAM,EACjB,GAAG,MAAM,IAAI,QAAQ,IAAK;gBAC3B,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,IAAI,CAAC,EAAE,GAAG;oBAC/B,OAAO;gBACX;YACJ;QACJ,OAAO;YACH,OAAO,KAAK,OAAO,CAAC;QACxB;QACA,OAAO,CAAC;IACZ;IACA,eAAe,IAAI,EAAE,KAAK,EAAE;QACxB,OAAO,CAAC,SAAS,IAAI,EAAE,KAAK;IAChC;IACA,uBAAuB,WAAW,EAAE,gBAAgB,EAAE;QAClD,MAAM,QAAQ,IAAI,CAAC,cAAc;QACjC,MAAM,gBAAgB,KAAK,CAAC,YAAY;QACxC,MAAM,MAAM,CAAC,aAAa;QAC1B,MAAM,MAAM,CAAC,kBAAkB,GAAG;IACtC;IACA,aAAa,KAAK,EAAE;QAChB,OAAO,aAAa,OAAO,SAAS,KAAK,KAAK,CAAC,WAAW;IAC9D;IACA,wBAAwB,WAAW,EAAE;QACjC,OAAO,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG,KAAK,CAAC;IACxD;IACA,oBAAoB,KAAK,EAAE;QACvB,OAAO;IACX;IACA,sBAAsB,KAAK,EAAE;QACzB,OAAO;IACX;IACA,0BAA0B,KAAK,EAAE;QAC7B,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,iBAAiB,CAAC,aAAa,GAAG,EAAE,CAAC,SAAS;IAC3E;IACA,qBAAqB,UAAU,EAAE,WAAW,EAAE;QAC1C,OAAO;IACX;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/collection_widget.base.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/collection_widget.base.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    name as clickEventName\r\n} from \"../../../common/core/events/click\";\r\nimport {\r\n    name as contextMenuEventName\r\n} from \"../../../common/core/events/contextmenu\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport holdEvent from \"../../../common/core/events/hold\";\r\nimport pointerEvents from \"../../../common/core/events/pointer\";\r\nimport {\r\n    addNamespace,\r\n    isCommandKeyPressed\r\n} from \"../../../common/core/events/utils/index\";\r\nimport messageLocalization from \"../../../common/core/localization/message\";\r\nimport Action from \"../../../core/action\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport Guid from \"../../../core/guid\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    BindableTemplate\r\n} from \"../../../core/templates/bindable_template\";\r\nimport {\r\n    deferRenderer,\r\n    ensureDefined\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    compileGetter\r\n} from \"../../../core/utils/data\";\r\nimport {\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getOuterHeight,\r\n    getOuterWidth\r\n} from \"../../../core/utils/size\";\r\nimport {\r\n    findTemplates\r\n} from \"../../../core/utils/template_manager\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isPlainObject\r\n} from \"../../../core/utils/type\";\r\nimport DataHelperMixin from \"../../../data_helper\";\r\nimport {\r\n    focusable\r\n} from \"../../../ui/widget/selectors\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../core/m_element\";\r\nimport Widget from \"../../core/widget/widget\";\r\nimport CollectionWidgetItem from \"../../ui/collection/item\";\r\nconst COLLECTION_CLASS = \"dx-collection\";\r\nconst ITEM_CLASS = \"dx-item\";\r\nconst CONTENT_CLASS_POSTFIX = \"-content\";\r\nconst ITEM_CONTENT_PLACEHOLDER_CLASS = \"dx-item-content-placeholder\";\r\nconst ITEM_DATA_KEY = \"dxItemData\";\r\nconst ITEM_INDEX_KEY = \"dxItemIndex\";\r\nconst ITEM_TEMPLATE_ID_PREFIX = \"tmpl-\";\r\nconst ITEMS_OPTIONS_NAME = \"dxItem\";\r\nconst ITEM_RESPONSE_WAIT_CLASS = \"dx-item-response-wait\";\r\nconst EMPTY_COLLECTION = \"dx-empty-collection\";\r\nconst TEMPLATE_WRAPPER_CLASS = \"dx-template-wrapper\";\r\nconst ITEM_PATH_REGEX = /^([^.]+\\[\\d+\\]\\.)+([\\w.]+)$/;\r\nconst ANONYMOUS_TEMPLATE_NAME = \"item\";\r\nconst FOCUS_UP = \"up\";\r\nconst FOCUS_DOWN = \"down\";\r\nconst FOCUS_LEFT = \"left\";\r\nconst FOCUS_RIGHT = \"right\";\r\nconst FOCUS_PAGE_UP = \"pageup\";\r\nconst FOCUS_PAGE_DOWN = \"pagedown\";\r\nconst FOCUS_LAST = \"last\";\r\nconst FOCUS_FIRST = \"first\";\r\nclass CollectionWidget extends Widget {\r\n    _supportedKeys() {\r\n        const move = (location, e) => {\r\n            if (!isCommandKeyPressed(e)) {\r\n                e.preventDefault();\r\n                e.stopPropagation();\r\n                this._moveFocus(location, e)\r\n            }\r\n        };\r\n        return _extends({}, super._supportedKeys(), {\r\n            space: e => {\r\n                e.preventDefault();\r\n                this._enterKeyHandler(e)\r\n            },\r\n            enter: this._enterKeyHandler,\r\n            leftArrow: move.bind(this, \"left\"),\r\n            rightArrow: move.bind(this, \"right\"),\r\n            upArrow: move.bind(this, \"up\"),\r\n            downArrow: move.bind(this, \"down\"),\r\n            pageUp: move.bind(this, \"up\"),\r\n            pageDown: move.bind(this, \"down\"),\r\n            home: move.bind(this, \"first\"),\r\n            end: move.bind(this, \"last\")\r\n        })\r\n    }\r\n    _getHandlerExtendedParams(e, $target) {\r\n        const params = extend({}, e, {\r\n            target: $target.get(0),\r\n            currentTarget: $target.get(0)\r\n        });\r\n        return params\r\n    }\r\n    _enterKeyHandler(e) {\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        const $itemElement = $(focusedElement);\r\n        if (!$itemElement.length) {\r\n            return\r\n        }\r\n        const itemData = this._getItemData($itemElement);\r\n        if (null !== itemData && void 0 !== itemData && itemData.onClick) {\r\n            this._itemEventHandlerByHandler($itemElement, itemData.onClick, {\r\n                event: e\r\n            })\r\n        }\r\n        this._itemClickHandler(this._getHandlerExtendedParams(e, $itemElement))\r\n    }\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            selectOnFocus: false,\r\n            loopItemFocus: true,\r\n            items: [],\r\n            itemTemplate: \"item\",\r\n            onItemRendered: null,\r\n            onItemClick: null,\r\n            onItemHold: null,\r\n            itemHoldTimeout: 750,\r\n            onItemContextMenu: null,\r\n            onFocusedItemChanged: null,\r\n            noDataText: messageLocalization.format(\"dxCollectionWidget-noDataText\"),\r\n            encodeNoDataText: false,\r\n            dataSource: null,\r\n            _dataController: null,\r\n            _itemAttributes: {},\r\n            itemTemplateProperty: \"template\",\r\n            focusedElement: null,\r\n            displayExpr: void 0,\r\n            disabledExpr: data => data ? data.disabled : void 0,\r\n            visibleExpr: data => data ? data.visible : void 0\r\n        })\r\n    }\r\n    _init() {\r\n        this._compileDisplayGetter();\r\n        this._initDataController();\r\n        super._init();\r\n        this._activeStateUnit = `.${ITEM_CLASS}`;\r\n        this._cleanRenderedItems();\r\n        this._refreshDataSource()\r\n    }\r\n    _compileDisplayGetter() {\r\n        const {\r\n            displayExpr: displayExpr\r\n        } = this.option();\r\n        this._displayGetter = displayExpr ? compileGetter(displayExpr) : void 0\r\n    }\r\n    _initTemplates() {\r\n        this._initItemsFromMarkup();\r\n        this._initDefaultItemTemplate();\r\n        super._initTemplates()\r\n    }\r\n    _getAnonymousTemplateName() {\r\n        return \"item\"\r\n    }\r\n    _initDefaultItemTemplate() {\r\n        const fieldsMap = this._getFieldsMap();\r\n        this._templateManager.addDefaultTemplates({\r\n            item: new BindableTemplate((($container, data) => {\r\n                if (isPlainObject(data)) {\r\n                    this._prepareDefaultItemTemplate(data, $container)\r\n                } else {\r\n                    if (fieldsMap && isFunction(fieldsMap.text)) {\r\n                        data = fieldsMap.text(data)\r\n                    }\r\n                    $container.text(String(ensureDefined(data, \"\")))\r\n                }\r\n            }), this._getBindableFields(), this.option(\"integrationOptions.watchMethod\"), fieldsMap)\r\n        })\r\n    }\r\n    _getBindableFields() {\r\n        return [\"text\", \"html\"]\r\n    }\r\n    _getFieldsMap() {\r\n        if (this._displayGetter) {\r\n            return {\r\n                text: this._displayGetter\r\n            }\r\n        }\r\n        return\r\n    }\r\n    _prepareDefaultItemTemplate(data, $container) {\r\n        const {\r\n            text: text,\r\n            html: html\r\n        } = data;\r\n        if (isDefined(text)) {\r\n            $container.text(text)\r\n        }\r\n        if (isDefined(html)) {\r\n            $container.html(html)\r\n        }\r\n    }\r\n    _initItemsFromMarkup() {\r\n        const rawItems = findTemplates(this.$element(), \"dxItem\");\r\n        if (!rawItems.length || this.option(\"items\").length) {\r\n            return\r\n        }\r\n        const items = rawItems.map((_ref => {\r\n            let {\r\n                element: element,\r\n                options: options\r\n            } = _ref;\r\n            const isTemplateRequired = /\\S/.test(element.innerHTML) && !options.template;\r\n            if (isTemplateRequired) {\r\n                options.template = this._prepareItemTemplate(element)\r\n            } else {\r\n                $(element).remove()\r\n            }\r\n            return options\r\n        }));\r\n        this.option(\"items\", items)\r\n    }\r\n    _prepareItemTemplate(item) {\r\n        const templateId = `tmpl-${new Guid}`;\r\n        const $template = $(item).detach().clone().removeAttr(\"data-options\").addClass(\"dx-template-wrapper\");\r\n        this._saveTemplate(templateId, $template);\r\n        return templateId\r\n    }\r\n    _dataSourceOptions() {\r\n        return {\r\n            paginate: false\r\n        }\r\n    }\r\n    _cleanRenderedItems() {\r\n        this._renderedItemsCount = 0\r\n    }\r\n    _focusTarget() {\r\n        return this.$element()\r\n    }\r\n    _focusInHandler(e) {\r\n        super._focusInHandler(e);\r\n        if (!this._isFocusTarget(e.target)) {\r\n            return\r\n        }\r\n        const $focusedElement = $(this.option(\"focusedElement\"));\r\n        if ($focusedElement.length) {\r\n            this._shouldSkipSelectOnFocus = true;\r\n            this._setFocusedItem($focusedElement);\r\n            this._shouldSkipSelectOnFocus = false\r\n        } else {\r\n            const $activeItem = this._getActiveItem();\r\n            if ($activeItem.length) {\r\n                this.option(\"focusedElement\", getPublicElement($activeItem))\r\n            }\r\n        }\r\n    }\r\n    _focusOutHandler(e) {\r\n        super._focusOutHandler(e);\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        const $target = $(focusedElement);\r\n        this._updateFocusedItemState($target, false)\r\n    }\r\n    _findActiveTarget($element) {\r\n        return $element.find(this._activeStateUnit)\r\n    }\r\n    _getActiveItem(last) {\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        const $focusedElement = $(focusedElement);\r\n        if ($focusedElement.length) {\r\n            return $focusedElement\r\n        }\r\n        return this._determineFocusedElement(last)\r\n    }\r\n    _determineFocusedElement(last) {\r\n        let index = this._getFocusedElementIndex();\r\n        const activeElements = this._getActiveElement();\r\n        const lastIndex = activeElements.length - 1;\r\n        if (index < 0) {\r\n            index = last ? lastIndex : 0\r\n        }\r\n        return activeElements.eq(index)\r\n    }\r\n    _getFocusedElementIndex() {\r\n        return 0\r\n    }\r\n    _moveFocus(location, e) {\r\n        const $items = this._getAvailableItems();\r\n        let $newTarget = $();\r\n        switch (location) {\r\n            case \"pageup\":\r\n            case \"up\":\r\n                $newTarget = this._prevItem($items);\r\n                break;\r\n            case \"pagedown\":\r\n            case \"down\":\r\n                $newTarget = this._nextItem($items);\r\n                break;\r\n            case \"right\":\r\n                $newTarget = this.option(\"rtlEnabled\") ? this._prevItem($items) : this._nextItem($items);\r\n                break;\r\n            case \"left\":\r\n                $newTarget = this.option(\"rtlEnabled\") ? this._nextItem($items) : this._prevItem($items);\r\n                break;\r\n            case \"first\":\r\n                $newTarget = $items.first();\r\n                break;\r\n            case \"last\":\r\n                $newTarget = $items.last();\r\n                break;\r\n            default:\r\n                return false\r\n        }\r\n        if (0 !== $newTarget.length) {\r\n            this.option(\"focusedElement\", getPublicElement($newTarget))\r\n        }\r\n    }\r\n    _getVisibleItems($itemElements) {\r\n        const $items = $itemElements ?? this._itemElements();\r\n        return $items.filter(\":visible\")\r\n    }\r\n    _getAvailableItems($itemElements) {\r\n        return this._getVisibleItems($itemElements)\r\n    }\r\n    _prevItem($items) {\r\n        const $target = this._getActiveItem();\r\n        const targetIndex = $items.index($target);\r\n        const $last = $items.last();\r\n        let $item = $($items[targetIndex - 1]);\r\n        const loop = this.option(\"loopItemFocus\");\r\n        if (0 === $item.length && loop) {\r\n            $item = $last\r\n        }\r\n        return $item\r\n    }\r\n    _nextItem($items) {\r\n        const $target = this._getActiveItem(true);\r\n        const targetIndex = $items.index($target);\r\n        const $first = $items.first();\r\n        let $item = $($items[targetIndex + 1]);\r\n        const loop = this.option(\"loopItemFocus\");\r\n        if (0 === $item.length && loop) {\r\n            $item = $first\r\n        }\r\n        return $item\r\n    }\r\n    _selectFocusedItem($target) {\r\n        this.selectItem($target)\r\n    }\r\n    _updateFocusedItemState(target, isFocused, needCleanItemId) {\r\n        const $target = $(target);\r\n        if ($target.length) {\r\n            this._refreshActiveDescendant();\r\n            this._refreshItemId($target, needCleanItemId);\r\n            this._toggleFocusClass(isFocused, $target)\r\n        }\r\n        this._updateParentActiveDescendant()\r\n    }\r\n    _getElementClassToSkipRefreshId() {\r\n        return \"\"\r\n    }\r\n    _shouldSkipRefreshId(target) {\r\n        const elementClass = this._getElementClassToSkipRefreshId();\r\n        const shouldSkipRefreshId = $(target).hasClass(elementClass);\r\n        return shouldSkipRefreshId\r\n    }\r\n    _refreshActiveDescendant($target) {\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        if (isDefined(focusedElement)) {\r\n            const shouldSetExistingId = this._shouldSkipRefreshId(focusedElement);\r\n            const id = shouldSetExistingId ? $(focusedElement).attr(\"id\") : this.getFocusedItemId();\r\n            this.setAria(\"activedescendant\", id, $target);\r\n            return\r\n        }\r\n        this.setAria(\"activedescendant\", null, $target)\r\n    }\r\n    _refreshItemId($target, needCleanItemId) {\r\n        const {\r\n            focusedElement: focusedElement\r\n        } = this.option();\r\n        const shouldSkipRefreshId = this._shouldSkipRefreshId($target);\r\n        if (shouldSkipRefreshId) {\r\n            return\r\n        }\r\n        if (!needCleanItemId && focusedElement) {\r\n            this.setAria(\"id\", this.getFocusedItemId(), $target)\r\n        } else {\r\n            this.setAria(\"id\", null, $target)\r\n        }\r\n    }\r\n    _isDisabled($element) {\r\n        return $element && \"true\" === $($element).attr(\"aria-disabled\")\r\n    }\r\n    _setFocusedItem($target) {\r\n        if (!$target || !$target.length) {\r\n            return\r\n        }\r\n        this._updateFocusedItemState($target, true);\r\n        this.onFocusedItemChanged(this.getFocusedItemId());\r\n        const {\r\n            selectOnFocus: selectOnFocus\r\n        } = this.option();\r\n        const isTargetDisabled = this._isDisabled($target);\r\n        if (selectOnFocus && !isTargetDisabled && !this._shouldSkipSelectOnFocus) {\r\n            this._selectFocusedItem($target)\r\n        }\r\n    }\r\n    _findItemElementByItem(item) {\r\n        let result = $();\r\n        const itemDataKey = this._itemDataKey();\r\n        this.itemElements().each(((index, itemElement) => {\r\n            const $item = $(itemElement);\r\n            if ($item.data(itemDataKey) === item) {\r\n                result = $item;\r\n                return false\r\n            }\r\n            return true\r\n        }));\r\n        return result\r\n    }\r\n    _getIndexByItem(item) {\r\n        const {\r\n            items: items\r\n        } = this.option();\r\n        return items.indexOf(item)\r\n    }\r\n    _itemOptionChanged(item, property, value, prevValue) {\r\n        const $item = this._findItemElementByItem(item);\r\n        if (!$item.length) {\r\n            return\r\n        }\r\n        if (!this.constructor.ItemClass.getInstance($item).setDataField(property, value)) {\r\n            this._refreshItem($item, item)\r\n        }\r\n        const isDisabling = \"disabled\" === property && value;\r\n        if (isDisabling) {\r\n            this._resetItemFocus($item)\r\n        }\r\n    }\r\n    _resetItemFocus($item) {\r\n        if ($item.is(this.option(\"focusedElement\"))) {\r\n            this._resetFocusedElement()\r\n        }\r\n    }\r\n    _resetFocusedElement() {\r\n        this.option(\"focusedElement\", null)\r\n    }\r\n    _refreshItem($item, item) {\r\n        const itemData = this._getItemData($item);\r\n        const index = $item.data(this._itemIndexKey());\r\n        this._renderItem(this._renderedItemsCount + index, itemData, null, $item)\r\n    }\r\n    _updateParentActiveDescendant() {}\r\n    _optionChanged(args) {\r\n        const {\r\n            name: name,\r\n            value: value,\r\n            previousValue: previousValue,\r\n            fullName: fullName\r\n        } = args;\r\n        if (\"items\" === name) {\r\n            const matches = fullName.match(ITEM_PATH_REGEX);\r\n            if (null !== matches && void 0 !== matches && matches.length) {\r\n                const property = matches[matches.length - 1];\r\n                const itemPath = fullName.replace(`.${property}`, \"\");\r\n                const item = this.option(itemPath);\r\n                this._itemOptionChanged(item, property, value, previousValue);\r\n                return\r\n            }\r\n        }\r\n        switch (name) {\r\n            case \"items\":\r\n            case \"_itemAttributes\":\r\n            case \"itemTemplateProperty\":\r\n            case \"useItemTextAsTitle\":\r\n                this._cleanRenderedItems();\r\n                this._invalidate();\r\n                break;\r\n            case \"dataSource\":\r\n                this._refreshDataSource();\r\n                this._renderEmptyMessage();\r\n                break;\r\n            case \"noDataText\":\r\n            case \"encodeNoDataText\":\r\n                this._renderEmptyMessage();\r\n                break;\r\n            case \"itemTemplate\":\r\n            case \"visibleExpr\":\r\n            case \"disabledExpr\":\r\n                this._invalidate();\r\n                break;\r\n            case \"onItemRendered\":\r\n                this._createItemRenderAction();\r\n                break;\r\n            case \"onItemClick\":\r\n            case \"selectOnFocus\":\r\n            case \"loopItemFocus\":\r\n                break;\r\n            case \"onItemHold\":\r\n            case \"itemHoldTimeout\":\r\n                this._attachHoldEvent();\r\n                break;\r\n            case \"onItemContextMenu\":\r\n                this._attachContextMenuEvent();\r\n                break;\r\n            case \"onFocusedItemChanged\":\r\n                this.onFocusedItemChanged = this._createActionByOption(\"onFocusedItemChanged\");\r\n                break;\r\n            case \"focusedElement\":\r\n                this._updateFocusedItemState(previousValue, false, true);\r\n                this._setFocusedItem($(value));\r\n                break;\r\n            case \"displayExpr\":\r\n                this._compileDisplayGetter();\r\n                this._initDefaultItemTemplate();\r\n                this._invalidate();\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    _invalidate() {\r\n        this._resetFocusedElement();\r\n        super._invalidate()\r\n    }\r\n    _loadNextPage() {\r\n        this._expectNextPageLoading();\r\n        return this._dataController.loadNextPage()\r\n    }\r\n    _expectNextPageLoading() {\r\n        this._startIndexForAppendedItems = 0\r\n    }\r\n    _expectLastItemLoading() {\r\n        this._startIndexForAppendedItems = -1\r\n    }\r\n    _forgetNextPageLoading() {\r\n        this._startIndexForAppendedItems = null\r\n    }\r\n    _dataSourceChangedHandler(newItems, e) {\r\n        const items = this.option(\"items\");\r\n        if (this._initialized && items && this._shouldAppendItems()) {\r\n            this._renderedItemsCount = items.length;\r\n            if (!this._isLastPage() || -1 !== this._startIndexForAppendedItems) {\r\n                this.option().items = items.concat(newItems.slice(this._startIndexForAppendedItems))\r\n            }\r\n            this._forgetNextPageLoading();\r\n            this._refreshContent()\r\n        } else {\r\n            this.option(\"items\", newItems.slice())\r\n        }\r\n    }\r\n    _refreshContent() {\r\n        this._prepareContent();\r\n        this._renderContent()\r\n    }\r\n    _dataSourceLoadErrorHandler() {\r\n        this._forgetNextPageLoading();\r\n        this.option(\"items\", this.option(\"items\"))\r\n    }\r\n    _shouldAppendItems() {\r\n        return null != this._startIndexForAppendedItems && this._allowDynamicItemsAppend()\r\n    }\r\n    _allowDynamicItemsAppend() {\r\n        return false\r\n    }\r\n    _clean() {\r\n        this._cleanFocusState();\r\n        this._cleanItemContainer();\r\n        if (this._inkRipple) {\r\n            delete this._inkRipple\r\n        }\r\n        this._resetActiveState()\r\n    }\r\n    _cleanItemContainer() {\r\n        $(this._itemContainer()).empty()\r\n    }\r\n    _dispose() {\r\n        super._dispose();\r\n        clearTimeout(this._itemFocusTimeout)\r\n    }\r\n    _refresh() {\r\n        this._cleanRenderedItems();\r\n        super._refresh()\r\n    }\r\n    _itemContainer(searchEnabled, previousSelectAllEnabled) {\r\n        return this.$element()\r\n    }\r\n    _itemClass() {\r\n        return ITEM_CLASS\r\n    }\r\n    _itemContentClass() {\r\n        return `${this._itemClass()}-content`\r\n    }\r\n    _itemResponseWaitClass() {\r\n        return \"dx-item-response-wait\"\r\n    }\r\n    _itemSelector() {\r\n        return `.${this._itemClass()}`\r\n    }\r\n    _itemDataKey() {\r\n        return \"dxItemData\"\r\n    }\r\n    _itemIndexKey() {\r\n        return \"dxItemIndex\"\r\n    }\r\n    _itemElements() {\r\n        return this._itemContainer().find(this._itemSelector())\r\n    }\r\n    _initMarkup() {\r\n        super._initMarkup();\r\n        this.onFocusedItemChanged = this._createActionByOption(\"onFocusedItemChanged\");\r\n        this.$element().addClass(\"dx-collection\");\r\n        this._prepareContent()\r\n    }\r\n    _prepareContent() {\r\n        deferRenderer((() => {\r\n            this._renderContentImpl()\r\n        }))()\r\n    }\r\n    _renderContent() {\r\n        this._fireContentReadyAction()\r\n    }\r\n    _render() {\r\n        super._render();\r\n        this._attachClickEvent();\r\n        this._attachHoldEvent();\r\n        this._attachContextMenuEvent()\r\n    }\r\n    _getPointerEvent() {\r\n        return pointerEvents.down\r\n    }\r\n    _attachClickEvent() {\r\n        const itemSelector = this._itemSelector();\r\n        const pointerEvent = this._getPointerEvent();\r\n        const clickEventNamespace = addNamespace(clickEventName, this.NAME);\r\n        const pointerEventNamespace = addNamespace(pointerEvent, this.NAME);\r\n        const pointerAction = new Action((args => {\r\n            const {\r\n                event: event\r\n            } = args;\r\n            this._itemPointerDownHandler(event)\r\n        }));\r\n        eventsEngine.off(this._itemContainer(), clickEventNamespace, itemSelector);\r\n        eventsEngine.off(this._itemContainer(), pointerEventNamespace, itemSelector);\r\n        eventsEngine.on(this._itemContainer(), clickEventNamespace, itemSelector, (e => this._itemClickHandler(e)));\r\n        eventsEngine.on(this._itemContainer(), pointerEventNamespace, itemSelector, (e => {\r\n            pointerAction.execute({\r\n                element: $(e.target),\r\n                event: e\r\n            })\r\n        }))\r\n    }\r\n    _itemClickHandler(e, args, config) {\r\n        this._itemDXEventHandler(e, \"onItemClick\", args, config)\r\n    }\r\n    _itemPointerDownHandler(e) {\r\n        if (!this.option(\"focusStateEnabled\")) {\r\n            return\r\n        }\r\n        this._itemFocusHandler = () => {\r\n            clearTimeout(this._itemFocusTimeout);\r\n            this._itemFocusHandler = void 0;\r\n            if (e.isDefaultPrevented()) {\r\n                return\r\n            }\r\n            const $target = $(e.target);\r\n            const $closestItem = $target.closest(this._itemElements());\r\n            const $closestFocusable = this._closestFocusable($target);\r\n            if ($closestItem.length && this._isFocusTarget(null === $closestFocusable || void 0 === $closestFocusable ? void 0 : $closestFocusable.get(0))) {\r\n                this._shouldSkipSelectOnFocus = true;\r\n                this.option(\"focusedElement\", getPublicElement($closestItem));\r\n                this._shouldSkipSelectOnFocus = false\r\n            }\r\n        };\r\n        this._itemFocusTimeout = setTimeout(this._forcePointerDownFocus.bind(this))\r\n    }\r\n    _closestFocusable($target) {\r\n        if ($target.is(focusable)) {\r\n            return $target\r\n        }\r\n        let $nextTarget = $target.parent();\r\n        while ($nextTarget.length && !domAdapter.isDocument($nextTarget.get(0)) && !domAdapter.isDocumentFragment($nextTarget.get(0))) {\r\n            if ($nextTarget.is(focusable)) {\r\n                return $nextTarget\r\n            }\r\n            $nextTarget = $nextTarget.parent()\r\n        }\r\n        return\r\n    }\r\n    _forcePointerDownFocus() {\r\n        if (this._itemFocusHandler) {\r\n            this._itemFocusHandler()\r\n        }\r\n    }\r\n    _updateFocusState(e, isFocused) {\r\n        super._updateFocusState(e, isFocused);\r\n        this._forcePointerDownFocus()\r\n    }\r\n    _attachHoldEvent() {\r\n        const $itemContainer = this._itemContainer();\r\n        const itemSelector = this._itemSelector();\r\n        const eventName = addNamespace(holdEvent.name, this.NAME);\r\n        eventsEngine.off($itemContainer, eventName, itemSelector);\r\n        eventsEngine.on($itemContainer, eventName, itemSelector, {\r\n            timeout: this._getHoldTimeout()\r\n        }, this._itemHoldHandler.bind(this))\r\n    }\r\n    _getHoldTimeout() {\r\n        const {\r\n            itemHoldTimeout: itemHoldTimeout\r\n        } = this.option();\r\n        return itemHoldTimeout\r\n    }\r\n    _shouldFireHoldEvent() {\r\n        return this.hasActionSubscription(\"onItemHold\")\r\n    }\r\n    _itemHoldHandler(e) {\r\n        if (this._shouldFireHoldEvent()) {\r\n            this._itemDXEventHandler(e, \"onItemHold\")\r\n        } else {\r\n            e.cancel = true\r\n        }\r\n    }\r\n    _attachContextMenuEvent() {\r\n        const $itemContainer = this._itemContainer();\r\n        const itemSelector = this._itemSelector();\r\n        const eventName = addNamespace(contextMenuEventName, this.NAME);\r\n        eventsEngine.off($itemContainer, eventName, itemSelector);\r\n        eventsEngine.on($itemContainer, eventName, itemSelector, this._itemContextMenuHandler.bind(this))\r\n    }\r\n    _shouldFireContextMenuEvent() {\r\n        return this.hasActionSubscription(\"onItemContextMenu\")\r\n    }\r\n    _itemContextMenuHandler(e) {\r\n        if (this._shouldFireContextMenuEvent()) {\r\n            this._itemDXEventHandler(e, \"onItemContextMenu\")\r\n        } else {\r\n            e.cancel = true\r\n        }\r\n    }\r\n    _renderContentImpl() {\r\n        const {\r\n            items: items\r\n        } = this.option();\r\n        const itemsToRender = items ?? [];\r\n        if (this._renderedItemsCount) {\r\n            this._renderItems(itemsToRender.slice(this._renderedItemsCount))\r\n        } else {\r\n            this._renderItems(itemsToRender)\r\n        }\r\n    }\r\n    _renderItems(items) {\r\n        if (items.length) {\r\n            each(items, ((index, itemData) => {\r\n                this._renderItem(this._renderedItemsCount + index, itemData)\r\n            }))\r\n        }\r\n        this._renderEmptyMessage()\r\n    }\r\n    _getItemsContainer() {\r\n        return this._itemContainer()\r\n    }\r\n    _setAttributes($element) {\r\n        const attributes = _extends({}, this.option(\"_itemAttributes\"));\r\n        const {\r\n            class: customClassValue\r\n        } = attributes;\r\n        if (customClassValue) {\r\n            const currentClassValue = $element.get(0).className;\r\n            attributes.class = [currentClassValue, customClassValue].join(\" \")\r\n        }\r\n        $element.attr(attributes)\r\n    }\r\n    _renderItem(index, itemData, $container, $itemToReplace) {\r\n        const itemIndex = (null === index || void 0 === index ? void 0 : index.item) ?? index;\r\n        const $containerToRender = $container ?? this._getItemsContainer();\r\n        const $itemFrame = this._renderItemFrame(itemIndex, itemData, $containerToRender, $itemToReplace);\r\n        this._setElementData($itemFrame, itemData, itemIndex);\r\n        this._setAttributes($itemFrame);\r\n        this._attachItemClickEvent(itemData, $itemFrame);\r\n        const $itemContent = this._getItemContent($itemFrame);\r\n        const {\r\n            itemTemplate: itemTemplate\r\n        } = this.option();\r\n        const renderContentPromise = this._renderItemContent({\r\n            index: itemIndex,\r\n            itemData: itemData,\r\n            container: getPublicElement($itemContent),\r\n            contentClass: this._itemContentClass(),\r\n            defaultTemplateName: itemTemplate\r\n        });\r\n        when(renderContentPromise).done(($content => {\r\n            this._postprocessRenderItem({\r\n                itemElement: $itemFrame,\r\n                itemContent: $content,\r\n                itemData: itemData,\r\n                itemIndex: itemIndex\r\n            });\r\n            this._executeItemRenderAction(index, itemData, getPublicElement($itemFrame))\r\n        }));\r\n        return $itemFrame\r\n    }\r\n    _getItemContent($itemFrame) {\r\n        const $itemContent = $itemFrame.find(\".dx-item-content-placeholder\");\r\n        $itemContent.removeClass(\"dx-item-content-placeholder\");\r\n        return $itemContent\r\n    }\r\n    _attachItemClickEvent(itemData, $itemElement) {\r\n        if (!itemData || !itemData.onClick) {\r\n            return\r\n        }\r\n        eventsEngine.on($itemElement, clickEventName, (e => {\r\n            this._itemEventHandlerByHandler($itemElement, itemData.onClick, {\r\n                event: e\r\n            })\r\n        }))\r\n    }\r\n    _renderItemContent(args) {\r\n        const itemTemplateName = this._getItemTemplateName(args);\r\n        const itemTemplate = this._getTemplate(itemTemplateName);\r\n        this._addItemContentClasses(args);\r\n        const $templateResult = $(this._createItemByTemplate(itemTemplate, args));\r\n        if (!$templateResult.hasClass(\"dx-template-wrapper\")) {\r\n            return args.container\r\n        }\r\n        return this._renderItemContentByNode(args, $templateResult)\r\n    }\r\n    _renderItemContentByNode(args, $node) {\r\n        $(args.container).replaceWith($node);\r\n        args.container = getPublicElement($node);\r\n        this._addItemContentClasses(args);\r\n        return $node\r\n    }\r\n    _addItemContentClasses(args) {\r\n        const classes = [ITEM_CLASS + \"-content\", args.contentClass];\r\n        $(args.container).addClass(classes.join(\" \"))\r\n    }\r\n    _appendItemToContainer($container, $itemFrame, index) {\r\n        $itemFrame.appendTo($container)\r\n    }\r\n    _renderItemFrame(index, itemData, $container, $itemToReplace) {\r\n        const $itemFrame = $(\"<div>\");\r\n        new this.constructor.ItemClass($itemFrame, this._itemOptions(), itemData || {});\r\n        if (null !== $itemToReplace && void 0 !== $itemToReplace && $itemToReplace.length) {\r\n            $itemToReplace.replaceWith($itemFrame)\r\n        } else {\r\n            this._appendItemToContainer.call(this, $container, $itemFrame, index)\r\n        }\r\n        if (this.option(\"useItemTextAsTitle\")) {\r\n            const displayValue = this._displayGetter ? this._displayGetter(itemData) : itemData;\r\n            $itemFrame.attr(\"title\", displayValue)\r\n        }\r\n        return $itemFrame\r\n    }\r\n    _itemOptions() {\r\n        return {\r\n            watchMethod: () => this.option(\"integrationOptions.watchMethod\"),\r\n            owner: this,\r\n            fieldGetter: field => {\r\n                const expr = this.option(`${field}Expr`);\r\n                const getter = compileGetter(expr);\r\n                return getter\r\n            }\r\n        }\r\n    }\r\n    _postprocessRenderItem(args) {}\r\n    _executeItemRenderAction(index, itemData, itemElement) {\r\n        this._getItemRenderAction()({\r\n            itemElement: itemElement,\r\n            itemIndex: index,\r\n            itemData: itemData\r\n        })\r\n    }\r\n    _setElementData(element, data, index) {\r\n        element.addClass([ITEM_CLASS, this._itemClass()].join(\" \")).data(this._itemDataKey(), data).data(this._itemIndexKey(), index)\r\n    }\r\n    _createItemRenderAction() {\r\n        this._itemRenderAction = this._createActionByOption(\"onItemRendered\", {\r\n            element: this.element(),\r\n            excludeValidators: [\"disabled\", \"readOnly\"],\r\n            category: \"rendering\"\r\n        });\r\n        return this._itemRenderAction\r\n    }\r\n    _getItemRenderAction() {\r\n        return this._itemRenderAction ?? this._createItemRenderAction()\r\n    }\r\n    _getItemTemplateName(args) {\r\n        const data = args.itemData;\r\n        const templateProperty = args.templateProperty || this.option(\"itemTemplateProperty\");\r\n        const template = data && data[templateProperty];\r\n        return template || args.defaultTemplateName\r\n    }\r\n    _createItemByTemplate(itemTemplate, renderArgs) {\r\n        const {\r\n            itemData: itemData,\r\n            container: container,\r\n            index: index\r\n        } = renderArgs;\r\n        return itemTemplate.render({\r\n            model: itemData,\r\n            container: container,\r\n            index: index,\r\n            onRendered: this._onItemTemplateRendered(itemTemplate, renderArgs)\r\n        })\r\n    }\r\n    _onItemTemplateRendered(itemTemplate, renderArgs) {\r\n        return () => {}\r\n    }\r\n    _emptyMessageContainer() {\r\n        return this._itemContainer()\r\n    }\r\n    _renderEmptyMessage(rootNodes) {\r\n        const items = rootNodes ?? this.option(\"items\");\r\n        const noDataText = this.option(\"noDataText\");\r\n        const hideNoData = !noDataText || items && items.length || this._dataController.isLoading();\r\n        if (hideNoData && this._$noData) {\r\n            this._$noData.remove();\r\n            this._$noData = null;\r\n            this.setAria(\"label\", void 0)\r\n        }\r\n        if (!hideNoData) {\r\n            this._$noData = this._$noData ?? $(\"<div>\").addClass(\"dx-empty-message\");\r\n            this._$noData.appendTo(this._emptyMessageContainer());\r\n            if (this.option(\"encodeNoDataText\")) {\r\n                this._$noData.text(noDataText)\r\n            } else {\r\n                this._$noData.html(noDataText)\r\n            }\r\n        }\r\n        this.$element().toggleClass(EMPTY_COLLECTION, !hideNoData)\r\n    }\r\n    _itemDXEventHandler(dxEvent, handlerOptionName, actionArgs, actionConfig) {\r\n        this._itemEventHandler(dxEvent.target, handlerOptionName, extend(actionArgs, {\r\n            event: dxEvent\r\n        }), actionConfig)\r\n    }\r\n    _itemEventHandler(initiator, handlerOptionName, actionArgs, actionConfig) {\r\n        const action = this._createActionByOption(handlerOptionName, extend({\r\n            validatingTargetName: \"itemElement\"\r\n        }, actionConfig));\r\n        return this._itemEventHandlerImpl(initiator, action, actionArgs)\r\n    }\r\n    _itemEventHandlerByHandler(initiator, handler, actionArgs, actionConfig) {\r\n        const action = this._createAction(handler, extend({\r\n            validatingTargetName: \"itemElement\"\r\n        }, actionConfig));\r\n        return this._itemEventHandlerImpl(initiator, action, actionArgs)\r\n    }\r\n    _itemEventHandlerImpl(initiator, action, actionArgs) {\r\n        const $itemElement = this._closestItemElement($(initiator));\r\n        const args = extend({}, actionArgs);\r\n        return action(extend(actionArgs, this._extendActionArgs($itemElement), args))\r\n    }\r\n    _extendActionArgs($itemElement) {\r\n        return {\r\n            itemElement: getPublicElement($itemElement),\r\n            itemIndex: this._itemElements().index($itemElement),\r\n            itemData: this._getItemData($itemElement)\r\n        }\r\n    }\r\n    _closestItemElement($element) {\r\n        return $($element).closest(this._itemSelector())\r\n    }\r\n    _getItemData(itemElement) {\r\n        return $(itemElement).data(this._itemDataKey())\r\n    }\r\n    _getSummaryItemsSize(dimension, items, includeMargin) {\r\n        let result = 0;\r\n        if (items) {\r\n            each(items, ((_, item) => {\r\n                if (\"width\" === dimension) {\r\n                    result += getOuterWidth(item, includeMargin ?? false)\r\n                } else if (\"height\" === dimension) {\r\n                    result += getOuterHeight(item, includeMargin ?? false)\r\n                }\r\n            }))\r\n        }\r\n        return result\r\n    }\r\n    getFocusedItemId() {\r\n        if (!this._focusedItemId) {\r\n            this._focusedItemId = `dx-${new Guid}`\r\n        }\r\n        return this._focusedItemId\r\n    }\r\n    itemElements() {\r\n        return this._itemElements()\r\n    }\r\n    itemsContainer() {\r\n        return this._itemContainer()\r\n    }\r\n}\r\nCollectionWidget.include(DataHelperMixin);\r\nCollectionWidget.ItemClass = CollectionWidgetItem;\r\nexport default CollectionWidget;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAIA;AACA;AACA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AAKA;AACA;AAGA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,MAAM,mBAAmB;AACzB,MAAM,aAAa;AACnB,MAAM,wBAAwB;AAC9B,MAAM,iCAAiC;AACvC,MAAM,gBAAgB;AACtB,MAAM,iBAAiB;AACvB,MAAM,0BAA0B;AAChC,MAAM,qBAAqB;AAC3B,MAAM,2BAA2B;AACjC,MAAM,mBAAmB;AACzB,MAAM,yBAAyB;AAC/B,MAAM,kBAAkB;AACxB,MAAM,0BAA0B;AAChC,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,gBAAgB;AACtB,MAAM,kBAAkB;AACxB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,yBAAyB,8KAAA,CAAA,UAAM;IACjC,iBAAiB;QACb,MAAM,OAAO,CAAC,UAAU;YACpB,IAAI,CAAC,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI;gBACzB,EAAE,cAAc;gBAChB,EAAE,eAAe;gBACjB,IAAI,CAAC,UAAU,CAAC,UAAU;YAC9B;QACJ;QACA,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,kBAAkB;YACxC,OAAO,CAAA;gBACH,EAAE,cAAc;gBAChB,IAAI,CAAC,gBAAgB,CAAC;YAC1B;YACA,OAAO,IAAI,CAAC,gBAAgB;YAC5B,WAAW,KAAK,IAAI,CAAC,IAAI,EAAE;YAC3B,YAAY,KAAK,IAAI,CAAC,IAAI,EAAE;YAC5B,SAAS,KAAK,IAAI,CAAC,IAAI,EAAE;YACzB,WAAW,KAAK,IAAI,CAAC,IAAI,EAAE;YAC3B,QAAQ,KAAK,IAAI,CAAC,IAAI,EAAE;YACxB,UAAU,KAAK,IAAI,CAAC,IAAI,EAAE;YAC1B,MAAM,KAAK,IAAI,CAAC,IAAI,EAAE;YACtB,KAAK,KAAK,IAAI,CAAC,IAAI,EAAE;QACzB;IACJ;IACA,0BAA0B,CAAC,EAAE,OAAO,EAAE;QAClC,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,GAAG;YACzB,QAAQ,QAAQ,GAAG,CAAC;YACpB,eAAe,QAAQ,GAAG,CAAC;QAC/B;QACA,OAAO;IACX;IACA,iBAAiB,CAAC,EAAE;QAChB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACvB,IAAI,CAAC,aAAa,MAAM,EAAE;YACtB;QACJ;QACA,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;QACnC,IAAI,SAAS,YAAY,KAAK,MAAM,YAAY,SAAS,OAAO,EAAE;YAC9D,IAAI,CAAC,0BAA0B,CAAC,cAAc,SAAS,OAAO,EAAE;gBAC5D,OAAO;YACX;QACJ;QACA,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,yBAAyB,CAAC,GAAG;IAC7D;IACA,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,eAAe;YACf,eAAe;YACf,OAAO,EAAE;YACT,cAAc;YACd,gBAAgB;YAChB,aAAa;YACb,YAAY;YACZ,iBAAiB;YACjB,mBAAmB;YACnB,sBAAsB;YACtB,YAAY,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YACvC,kBAAkB;YAClB,YAAY;YACZ,iBAAiB;YACjB,iBAAiB,CAAC;YAClB,sBAAsB;YACtB,gBAAgB;YAChB,aAAa,KAAK;YAClB,cAAc,CAAA,OAAQ,OAAO,KAAK,QAAQ,GAAG,KAAK;YAClD,aAAa,CAAA,OAAQ,OAAO,KAAK,OAAO,GAAG,KAAK;QACpD;IACJ;IACA,QAAQ;QACJ,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,mBAAmB;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,gBAAgB,GAAG,CAAC,CAAC,EAAE,YAAY;QACxC,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,kBAAkB;IAC3B;IACA,wBAAwB;QACpB,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,cAAc,GAAG,cAAc,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,KAAK;IAC1E;IACA,iBAAiB;QACb,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,wBAAwB;QAC7B,KAAK,CAAC;IACV;IACA,4BAA4B;QACxB,OAAO;IACX;IACA,2BAA2B;QACvB,MAAM,YAAY,IAAI,CAAC,aAAa;QACpC,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,CAAC;YACtC,MAAM,IAAI,8LAAA,CAAA,mBAAgB,CAAE,CAAC,YAAY;gBACrC,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO;oBACrB,IAAI,CAAC,2BAA2B,CAAC,MAAM;gBAC3C,OAAO;oBACH,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,UAAU,IAAI,GAAG;wBACzC,OAAO,UAAU,IAAI,CAAC;oBAC1B;oBACA,WAAW,IAAI,CAAC,OAAO,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,MAAM;gBAC/C;YACJ,GAAI,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,MAAM,CAAC,mCAAmC;QAClF;IACJ;IACA,qBAAqB;QACjB,OAAO;YAAC;YAAQ;SAAO;IAC3B;IACA,gBAAgB;QACZ,IAAI,IAAI,CAAC,cAAc,EAAE;YACrB,OAAO;gBACH,MAAM,IAAI,CAAC,cAAc;YAC7B;QACJ;QACA;IACJ;IACA,4BAA4B,IAAI,EAAE,UAAU,EAAE;QAC1C,MAAM,EACF,MAAM,IAAI,EACV,MAAM,IAAI,EACb,GAAG;QACJ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO;YACjB,WAAW,IAAI,CAAC;QACpB;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO;YACjB,WAAW,IAAI,CAAC;QACpB;IACJ;IACA,uBAAuB;QACnB,MAAM,WAAW,CAAA,GAAA,yLAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;QAChD,IAAI,CAAC,SAAS,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,MAAM,EAAE;YACjD;QACJ;QACA,MAAM,QAAQ,SAAS,GAAG,CAAE,CAAA;YACxB,IAAI,EACA,SAAS,OAAO,EAChB,SAAS,OAAO,EACnB,GAAG;YACJ,MAAM,qBAAqB,KAAK,IAAI,CAAC,QAAQ,SAAS,KAAK,CAAC,QAAQ,QAAQ;YAC5E,IAAI,oBAAoB;gBACpB,QAAQ,QAAQ,GAAG,IAAI,CAAC,oBAAoB,CAAC;YACjD,OAAO;gBACH,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,MAAM;YACrB;YACA,OAAO;QACX;QACA,IAAI,CAAC,MAAM,CAAC,SAAS;IACzB;IACA,qBAAqB,IAAI,EAAE;QACvB,MAAM,aAAa,CAAC,KAAK,EAAE,IAAI,iJAAA,CAAA,UAAI,EAAE;QACrC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,MAAM,GAAG,KAAK,GAAG,UAAU,CAAC,gBAAgB,QAAQ,CAAC;QAC/E,IAAI,CAAC,aAAa,CAAC,YAAY;QAC/B,OAAO;IACX;IACA,qBAAqB;QACjB,OAAO;YACH,UAAU;QACd;IACJ;IACA,sBAAsB;QAClB,IAAI,CAAC,mBAAmB,GAAG;IAC/B;IACA,eAAe;QACX,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,gBAAgB,CAAC,EAAE;QACf,KAAK,CAAC,gBAAgB;QACtB,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC,EAAE,MAAM,GAAG;YAChC;QACJ;QACA,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC;QACtC,IAAI,gBAAgB,MAAM,EAAE;YACxB,IAAI,CAAC,wBAAwB,GAAG;YAChC,IAAI,CAAC,eAAe,CAAC;YACrB,IAAI,CAAC,wBAAwB,GAAG;QACpC,OAAO;YACH,MAAM,cAAc,IAAI,CAAC,cAAc;YACvC,IAAI,YAAY,MAAM,EAAE;gBACpB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YACnD;QACJ;IACJ;IACA,iBAAiB,CAAC,EAAE;QAChB,KAAK,CAAC,iBAAiB;QACvB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QAClB,IAAI,CAAC,uBAAuB,CAAC,SAAS;IAC1C;IACA,kBAAkB,QAAQ,EAAE;QACxB,OAAO,SAAS,IAAI,CAAC,IAAI,CAAC,gBAAgB;IAC9C;IACA,eAAe,IAAI,EAAE;QACjB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QAC1B,IAAI,gBAAgB,MAAM,EAAE;YACxB,OAAO;QACX;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC;IACzC;IACA,yBAAyB,IAAI,EAAE;QAC3B,IAAI,QAAQ,IAAI,CAAC,uBAAuB;QACxC,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAC7C,MAAM,YAAY,eAAe,MAAM,GAAG;QAC1C,IAAI,QAAQ,GAAG;YACX,QAAQ,OAAO,YAAY;QAC/B;QACA,OAAO,eAAe,EAAE,CAAC;IAC7B;IACA,0BAA0B;QACtB,OAAO;IACX;IACA,WAAW,QAAQ,EAAE,CAAC,EAAE;QACpB,MAAM,SAAS,IAAI,CAAC,kBAAkB;QACtC,IAAI,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD;QACjB,OAAQ;YACJ,KAAK;YACL,KAAK;gBACD,aAAa,IAAI,CAAC,SAAS,CAAC;gBAC5B;YACJ,KAAK;YACL,KAAK;gBACD,aAAa,IAAI,CAAC,SAAS,CAAC;gBAC5B;YACJ,KAAK;gBACD,aAAa,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC;gBACjF;YACJ,KAAK;gBACD,aAAa,IAAI,CAAC,MAAM,CAAC,gBAAgB,IAAI,CAAC,SAAS,CAAC,UAAU,IAAI,CAAC,SAAS,CAAC;gBACjF;YACJ,KAAK;gBACD,aAAa,OAAO,KAAK;gBACzB;YACJ,KAAK;gBACD,aAAa,OAAO,IAAI;gBACxB;YACJ;gBACI,OAAO;QACf;QACA,IAAI,MAAM,WAAW,MAAM,EAAE;YACzB,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;QACnD;IACJ;IACA,iBAAiB,aAAa,EAAE;QAC5B,MAAM,SAAS,iBAAiB,IAAI,CAAC,aAAa;QAClD,OAAO,OAAO,MAAM,CAAC;IACzB;IACA,mBAAmB,aAAa,EAAE;QAC9B,OAAO,IAAI,CAAC,gBAAgB,CAAC;IACjC;IACA,UAAU,MAAM,EAAE;QACd,MAAM,UAAU,IAAI,CAAC,cAAc;QACnC,MAAM,cAAc,OAAO,KAAK,CAAC;QACjC,MAAM,QAAQ,OAAO,IAAI;QACzB,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,CAAC,cAAc,EAAE;QACrC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM;YAC5B,QAAQ;QACZ;QACA,OAAO;IACX;IACA,UAAU,MAAM,EAAE;QACd,MAAM,UAAU,IAAI,CAAC,cAAc,CAAC;QACpC,MAAM,cAAc,OAAO,KAAK,CAAC;QACjC,MAAM,SAAS,OAAO,KAAK;QAC3B,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,CAAC,cAAc,EAAE;QACrC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;QACzB,IAAI,MAAM,MAAM,MAAM,IAAI,MAAM;YAC5B,QAAQ;QACZ;QACA,OAAO;IACX;IACA,mBAAmB,OAAO,EAAE;QACxB,IAAI,CAAC,UAAU,CAAC;IACpB;IACA,wBAAwB,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE;QACxD,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QAClB,IAAI,QAAQ,MAAM,EAAE;YAChB,IAAI,CAAC,wBAAwB;YAC7B,IAAI,CAAC,cAAc,CAAC,SAAS;YAC7B,IAAI,CAAC,iBAAiB,CAAC,WAAW;QACtC;QACA,IAAI,CAAC,6BAA6B;IACtC;IACA,kCAAkC;QAC9B,OAAO;IACX;IACA,qBAAqB,MAAM,EAAE;QACzB,MAAM,eAAe,IAAI,CAAC,+BAA+B;QACzD,MAAM,sBAAsB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,QAAQ,CAAC;QAC/C,OAAO;IACX;IACA,yBAAyB,OAAO,EAAE;QAC9B,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;YAC3B,MAAM,sBAAsB,IAAI,CAAC,oBAAoB,CAAC;YACtD,MAAM,KAAK,sBAAsB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,gBAAgB,IAAI,CAAC,QAAQ,IAAI,CAAC,gBAAgB;YACrF,IAAI,CAAC,OAAO,CAAC,oBAAoB,IAAI;YACrC;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,oBAAoB,MAAM;IAC3C;IACA,eAAe,OAAO,EAAE,eAAe,EAAE;QACrC,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,sBAAsB,IAAI,CAAC,oBAAoB,CAAC;QACtD,IAAI,qBAAqB;YACrB;QACJ;QACA,IAAI,CAAC,mBAAmB,gBAAgB;YACpC,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,gBAAgB,IAAI;QAChD,OAAO;YACH,IAAI,CAAC,OAAO,CAAC,MAAM,MAAM;QAC7B;IACJ;IACA,YAAY,QAAQ,EAAE;QAClB,OAAO,YAAY,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,IAAI,CAAC;IACnD;IACA,gBAAgB,OAAO,EAAE;QACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE;YAC7B;QACJ;QACA,IAAI,CAAC,uBAAuB,CAAC,SAAS;QACtC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,gBAAgB;QAC/C,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,mBAAmB,IAAI,CAAC,WAAW,CAAC;QAC1C,IAAI,iBAAiB,CAAC,oBAAoB,CAAC,IAAI,CAAC,wBAAwB,EAAE;YACtE,IAAI,CAAC,kBAAkB,CAAC;QAC5B;IACJ;IACA,uBAAuB,IAAI,EAAE;QACzB,IAAI,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD;QACb,MAAM,cAAc,IAAI,CAAC,YAAY;QACrC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAE,CAAC,OAAO;YAC9B,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;YAChB,IAAI,MAAM,IAAI,CAAC,iBAAiB,MAAM;gBAClC,SAAS;gBACT,OAAO;YACX;YACA,OAAO;QACX;QACA,OAAO;IACX;IACA,gBAAgB,IAAI,EAAE;QAClB,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,MAAM,OAAO,CAAC;IACzB;IACA,mBAAmB,IAAI,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE;QACjD,MAAM,QAAQ,IAAI,CAAC,sBAAsB,CAAC;QAC1C,IAAI,CAAC,MAAM,MAAM,EAAE;YACf;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,WAAW,CAAC,OAAO,YAAY,CAAC,UAAU,QAAQ;YAC9E,IAAI,CAAC,YAAY,CAAC,OAAO;QAC7B;QACA,MAAM,cAAc,eAAe,YAAY;QAC/C,IAAI,aAAa;YACb,IAAI,CAAC,eAAe,CAAC;QACzB;IACJ;IACA,gBAAgB,KAAK,EAAE;QACnB,IAAI,MAAM,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB;YACzC,IAAI,CAAC,oBAAoB;QAC7B;IACJ;IACA,uBAAuB;QACnB,IAAI,CAAC,MAAM,CAAC,kBAAkB;IAClC;IACA,aAAa,KAAK,EAAE,IAAI,EAAE;QACtB,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;QACnC,MAAM,QAAQ,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa;QAC3C,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,GAAG,OAAO,UAAU,MAAM;IACvE;IACA,gCAAgC,CAAC;IACjC,eAAe,IAAI,EAAE;QACjB,MAAM,EACF,MAAM,IAAI,EACV,OAAO,KAAK,EACZ,eAAe,aAAa,EAC5B,UAAU,QAAQ,EACrB,GAAG;QACJ,IAAI,YAAY,MAAM;YAClB,MAAM,UAAU,SAAS,KAAK,CAAC;YAC/B,IAAI,SAAS,WAAW,KAAK,MAAM,WAAW,QAAQ,MAAM,EAAE;gBAC1D,MAAM,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;gBAC5C,MAAM,WAAW,SAAS,OAAO,CAAC,CAAC,CAAC,EAAE,UAAU,EAAE;gBAClD,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;gBACzB,IAAI,CAAC,kBAAkB,CAAC,MAAM,UAAU,OAAO;gBAC/C;YACJ;QACJ;QACA,OAAQ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,mBAAmB;gBACxB,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,mBAAmB;gBACxB;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,mBAAmB;gBACxB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,uBAAuB;gBAC5B;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,gBAAgB;gBACrB;YACJ,KAAK;gBACD,IAAI,CAAC,uBAAuB;gBAC5B;YACJ,KAAK;gBACD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;gBACvD;YACJ,KAAK;gBACD,IAAI,CAAC,uBAAuB,CAAC,eAAe,OAAO;gBACnD,IAAI,CAAC,eAAe,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;gBACvB;YACJ,KAAK;gBACD,IAAI,CAAC,qBAAqB;gBAC1B,IAAI,CAAC,wBAAwB;gBAC7B,IAAI,CAAC,WAAW;gBAChB;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,cAAc;QACV,IAAI,CAAC,oBAAoB;QACzB,KAAK,CAAC;IACV;IACA,gBAAgB;QACZ,IAAI,CAAC,sBAAsB;QAC3B,OAAO,IAAI,CAAC,eAAe,CAAC,YAAY;IAC5C;IACA,yBAAyB;QACrB,IAAI,CAAC,2BAA2B,GAAG;IACvC;IACA,yBAAyB;QACrB,IAAI,CAAC,2BAA2B,GAAG,CAAC;IACxC;IACA,yBAAyB;QACrB,IAAI,CAAC,2BAA2B,GAAG;IACvC;IACA,0BAA0B,QAAQ,EAAE,CAAC,EAAE;QACnC,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,IAAI,CAAC,YAAY,IAAI,SAAS,IAAI,CAAC,kBAAkB,IAAI;YACzD,IAAI,CAAC,mBAAmB,GAAG,MAAM,MAAM;YACvC,IAAI,CAAC,IAAI,CAAC,WAAW,MAAM,CAAC,MAAM,IAAI,CAAC,2BAA2B,EAAE;gBAChE,IAAI,CAAC,MAAM,GAAG,KAAK,GAAG,MAAM,MAAM,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,2BAA2B;YACtF;YACA,IAAI,CAAC,sBAAsB;YAC3B,IAAI,CAAC,eAAe;QACxB,OAAO;YACH,IAAI,CAAC,MAAM,CAAC,SAAS,SAAS,KAAK;QACvC;IACJ;IACA,kBAAkB;QACd,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,cAAc;IACvB;IACA,8BAA8B;QAC1B,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,MAAM,CAAC;IACrC;IACA,qBAAqB;QACjB,OAAO,QAAQ,IAAI,CAAC,2BAA2B,IAAI,IAAI,CAAC,wBAAwB;IACpF;IACA,2BAA2B;QACvB,OAAO;IACX;IACA,SAAS;QACL,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,mBAAmB;QACxB,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,OAAO,IAAI,CAAC,UAAU;QAC1B;QACA,IAAI,CAAC,iBAAiB;IAC1B;IACA,sBAAsB;QAClB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,cAAc,IAAI,KAAK;IAClC;IACA,WAAW;QACP,KAAK,CAAC;QACN,aAAa,IAAI,CAAC,iBAAiB;IACvC;IACA,WAAW;QACP,IAAI,CAAC,mBAAmB;QACxB,KAAK,CAAC;IACV;IACA,eAAe,aAAa,EAAE,wBAAwB,EAAE;QACpD,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,aAAa;QACT,OAAO;IACX;IACA,oBAAoB;QAChB,OAAO,GAAG,IAAI,CAAC,UAAU,GAAG,QAAQ,CAAC;IACzC;IACA,yBAAyB;QACrB,OAAO;IACX;IACA,gBAAgB;QACZ,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,UAAU,IAAI;IAClC;IACA,eAAe;QACX,OAAO;IACX;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,IAAI,CAAC,aAAa;IACxD;IACA,cAAc;QACV,KAAK,CAAC;QACN,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACvD,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAG;YACX,IAAI,CAAC,kBAAkB;QAC3B;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,uBAAuB;IAChC;IACA,UAAU;QACN,KAAK,CAAC;QACN,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,uBAAuB;IAChC;IACA,mBAAmB;QACf,OAAO,yKAAA,CAAA,UAAa,CAAC,IAAI;IAC7B;IACA,oBAAoB;QAChB,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,MAAM,sBAAsB,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,uKAAA,CAAA,OAAc,EAAE,IAAI,CAAC,IAAI;QAClE,MAAM,wBAAwB,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,cAAc,IAAI,CAAC,IAAI;QAClE,MAAM,gBAAgB,IAAI,mJAAA,CAAA,UAAM,CAAE,CAAA;YAC9B,MAAM,EACF,OAAO,KAAK,EACf,GAAG;YACJ,IAAI,CAAC,uBAAuB,CAAC;QACjC;QACA,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,qBAAqB;QAC7D,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc,IAAI,uBAAuB;QAC/D,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,qBAAqB,cAAe,CAAA,IAAK,IAAI,CAAC,iBAAiB,CAAC;QACvG,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,IAAI,uBAAuB,cAAe,CAAA;YACzE,cAAc,OAAO,CAAC;gBAClB,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM;gBACnB,OAAO;YACX;QACJ;IACJ;IACA,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;QAC/B,IAAI,CAAC,mBAAmB,CAAC,GAAG,eAAe,MAAM;IACrD;IACA,wBAAwB,CAAC,EAAE;QACvB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB;YACnC;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG;YACrB,aAAa,IAAI,CAAC,iBAAiB;YACnC,IAAI,CAAC,iBAAiB,GAAG,KAAK;YAC9B,IAAI,EAAE,kBAAkB,IAAI;gBACxB;YACJ;YACA,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM;YAC1B,MAAM,eAAe,QAAQ,OAAO,CAAC,IAAI,CAAC,aAAa;YACvD,MAAM,oBAAoB,IAAI,CAAC,iBAAiB,CAAC;YACjD,IAAI,aAAa,MAAM,IAAI,IAAI,CAAC,cAAc,CAAC,SAAS,qBAAqB,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,GAAG,CAAC,KAAK;gBAC5I,IAAI,CAAC,wBAAwB,GAAG;gBAChC,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC/C,IAAI,CAAC,wBAAwB,GAAG;YACpC;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG,WAAW,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI;IAC7E;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,QAAQ,EAAE,CAAC,8JAAA,CAAA,YAAS,GAAG;YACvB,OAAO;QACX;QACA,IAAI,cAAc,QAAQ,MAAM;QAChC,MAAO,YAAY,MAAM,IAAI,CAAC,wJAAA,CAAA,UAAU,CAAC,UAAU,CAAC,YAAY,GAAG,CAAC,OAAO,CAAC,wJAAA,CAAA,UAAU,CAAC,kBAAkB,CAAC,YAAY,GAAG,CAAC,IAAK;YAC3H,IAAI,YAAY,EAAE,CAAC,8JAAA,CAAA,YAAS,GAAG;gBAC3B,OAAO;YACX;YACA,cAAc,YAAY,MAAM;QACpC;QACA;IACJ;IACA,yBAAyB;QACrB,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,iBAAiB;QAC1B;IACJ;IACA,kBAAkB,CAAC,EAAE,SAAS,EAAE;QAC5B,KAAK,CAAC,kBAAkB,GAAG;QAC3B,IAAI,CAAC,sBAAsB;IAC/B;IACA,mBAAmB;QACf,MAAM,iBAAiB,IAAI,CAAC,cAAc;QAC1C,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,YAAY,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,sKAAA,CAAA,UAAS,CAAC,IAAI,EAAE,IAAI,CAAC,IAAI;QACxD,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,gBAAgB,WAAW;QAC5C,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,gBAAgB,WAAW,cAAc;YACrD,SAAS,IAAI,CAAC,eAAe;QACjC,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI;IACtC;IACA,kBAAkB;QACd,MAAM,EACF,iBAAiB,eAAe,EACnC,GAAG,IAAI,CAAC,MAAM;QACf,OAAO;IACX;IACA,uBAAuB;QACnB,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC;IACA,iBAAiB,CAAC,EAAE;QAChB,IAAI,IAAI,CAAC,oBAAoB,IAAI;YAC7B,IAAI,CAAC,mBAAmB,CAAC,GAAG;QAChC,OAAO;YACH,EAAE,MAAM,GAAG;QACf;IACJ;IACA,0BAA0B;QACtB,MAAM,iBAAiB,IAAI,CAAC,cAAc;QAC1C,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,YAAY,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,6KAAA,CAAA,OAAoB,EAAE,IAAI,CAAC,IAAI;QAC9D,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,gBAAgB,WAAW;QAC5C,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,gBAAgB,WAAW,cAAc,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,IAAI;IACnG;IACA,8BAA8B;QAC1B,OAAO,IAAI,CAAC,qBAAqB,CAAC;IACtC;IACA,wBAAwB,CAAC,EAAE;QACvB,IAAI,IAAI,CAAC,2BAA2B,IAAI;YACpC,IAAI,CAAC,mBAAmB,CAAC,GAAG;QAChC,OAAO;YACH,EAAE,MAAM,GAAG;QACf;IACJ;IACA,qBAAqB;QACjB,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,gBAAgB,SAAS,EAAE;QACjC,IAAI,IAAI,CAAC,mBAAmB,EAAE;YAC1B,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,CAAC,IAAI,CAAC,mBAAmB;QAClE,OAAO;YACH,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,MAAM,MAAM,EAAE;YACd,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;gBACjB,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB,GAAG,OAAO;YACvD;QACJ;QACA,IAAI,CAAC,mBAAmB;IAC5B;IACA,qBAAqB;QACjB,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,eAAe,QAAQ,EAAE;QACrB,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC;QAC5C,MAAM,EACF,OAAO,gBAAgB,EAC1B,GAAG;QACJ,IAAI,kBAAkB;YAClB,MAAM,oBAAoB,SAAS,GAAG,CAAC,GAAG,SAAS;YACnD,WAAW,KAAK,GAAG;gBAAC;gBAAmB;aAAiB,CAAC,IAAI,CAAC;QAClE;QACA,SAAS,IAAI,CAAC;IAClB;IACA,YAAY,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE;QACrD,MAAM,YAAY,CAAC,SAAS,SAAS,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,IAAI,KAAK;QAChF,MAAM,qBAAqB,cAAc,IAAI,CAAC,kBAAkB;QAChE,MAAM,aAAa,IAAI,CAAC,gBAAgB,CAAC,WAAW,UAAU,oBAAoB;QAClF,IAAI,CAAC,eAAe,CAAC,YAAY,UAAU;QAC3C,IAAI,CAAC,cAAc,CAAC;QACpB,IAAI,CAAC,qBAAqB,CAAC,UAAU;QACrC,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;QAC1C,MAAM,EACF,cAAc,YAAY,EAC7B,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,uBAAuB,IAAI,CAAC,kBAAkB,CAAC;YACjD,OAAO;YACP,UAAU;YACV,WAAW,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YAC5B,cAAc,IAAI,CAAC,iBAAiB;YACpC,qBAAqB;QACzB;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,sBAAsB,IAAI,CAAE,CAAA;YAC7B,IAAI,CAAC,sBAAsB,CAAC;gBACxB,aAAa;gBACb,aAAa;gBACb,UAAU;gBACV,WAAW;YACf;YACA,IAAI,CAAC,wBAAwB,CAAC,OAAO,UAAU,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;QACpE;QACA,OAAO;IACX;IACA,gBAAgB,UAAU,EAAE;QACxB,MAAM,eAAe,WAAW,IAAI,CAAC;QACrC,aAAa,WAAW,CAAC;QACzB,OAAO;IACX;IACA,sBAAsB,QAAQ,EAAE,YAAY,EAAE;QAC1C,IAAI,CAAC,YAAY,CAAC,SAAS,OAAO,EAAE;YAChC;QACJ;QACA,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,cAAc,uKAAA,CAAA,OAAc,EAAG,CAAA;YAC3C,IAAI,CAAC,0BAA0B,CAAC,cAAc,SAAS,OAAO,EAAE;gBAC5D,OAAO;YACX;QACJ;IACJ;IACA,mBAAmB,IAAI,EAAE;QACrB,MAAM,mBAAmB,IAAI,CAAC,oBAAoB,CAAC;QACnD,MAAM,eAAe,IAAI,CAAC,YAAY,CAAC;QACvC,IAAI,CAAC,sBAAsB,CAAC;QAC5B,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,qBAAqB,CAAC,cAAc;QACnE,IAAI,CAAC,gBAAgB,QAAQ,CAAC,wBAAwB;YAClD,OAAO,KAAK,SAAS;QACzB;QACA,OAAO,IAAI,CAAC,wBAAwB,CAAC,MAAM;IAC/C;IACA,yBAAyB,IAAI,EAAE,KAAK,EAAE;QAClC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,KAAK,SAAS,EAAE,WAAW,CAAC;QAC9B,KAAK,SAAS,GAAG,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;QAClC,IAAI,CAAC,sBAAsB,CAAC;QAC5B,OAAO;IACX;IACA,uBAAuB,IAAI,EAAE;QACzB,MAAM,UAAU;YAAC,aAAa;YAAY,KAAK,YAAY;SAAC;QAC5D,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,KAAK,SAAS,EAAE,QAAQ,CAAC,QAAQ,IAAI,CAAC;IAC5C;IACA,uBAAuB,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;QAClD,WAAW,QAAQ,CAAC;IACxB;IACA,iBAAiB,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,cAAc,EAAE;QAC1D,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACrB,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,YAAY,IAAI,CAAC,YAAY,IAAI,YAAY,CAAC;QAC7E,IAAI,SAAS,kBAAkB,KAAK,MAAM,kBAAkB,eAAe,MAAM,EAAE;YAC/E,eAAe,WAAW,CAAC;QAC/B,OAAO;YACH,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,IAAI,EAAE,YAAY,YAAY;QACnE;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB;YACnC,MAAM,eAAe,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,cAAc,CAAC,YAAY;YAC3E,WAAW,IAAI,CAAC,SAAS;QAC7B;QACA,OAAO;IACX;IACA,eAAe;QACX,OAAO;YACH,aAAa,IAAM,IAAI,CAAC,MAAM,CAAC;YAC/B,OAAO,IAAI;YACX,aAAa,CAAA;gBACT,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC,GAAG,MAAM,IAAI,CAAC;gBACvC,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;gBAC7B,OAAO;YACX;QACJ;IACJ;IACA,uBAAuB,IAAI,EAAE,CAAC;IAC9B,yBAAyB,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;QACnD,IAAI,CAAC,oBAAoB,GAAG;YACxB,aAAa;YACb,WAAW;YACX,UAAU;QACd;IACJ;IACA,gBAAgB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE;QAClC,QAAQ,QAAQ,CAAC;YAAC;YAAY,IAAI,CAAC,UAAU;SAAG,CAAC,IAAI,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI;IAC3H;IACA,0BAA0B;QACtB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,qBAAqB,CAAC,kBAAkB;YAClE,SAAS,IAAI,CAAC,OAAO;YACrB,mBAAmB;gBAAC;gBAAY;aAAW;YAC3C,UAAU;QACd;QACA,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,uBAAuB;QACnB,OAAO,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,uBAAuB;IACjE;IACA,qBAAqB,IAAI,EAAE;QACvB,MAAM,OAAO,KAAK,QAAQ;QAC1B,MAAM,mBAAmB,KAAK,gBAAgB,IAAI,IAAI,CAAC,MAAM,CAAC;QAC9D,MAAM,WAAW,QAAQ,IAAI,CAAC,iBAAiB;QAC/C,OAAO,YAAY,KAAK,mBAAmB;IAC/C;IACA,sBAAsB,YAAY,EAAE,UAAU,EAAE;QAC5C,MAAM,EACF,UAAU,QAAQ,EAClB,WAAW,SAAS,EACpB,OAAO,KAAK,EACf,GAAG;QACJ,OAAO,aAAa,MAAM,CAAC;YACvB,OAAO;YACP,WAAW;YACX,OAAO;YACP,YAAY,IAAI,CAAC,uBAAuB,CAAC,cAAc;QAC3D;IACJ;IACA,wBAAwB,YAAY,EAAE,UAAU,EAAE;QAC9C,OAAO,KAAO;IAClB;IACA,yBAAyB;QACrB,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,oBAAoB,SAAS,EAAE;QAC3B,MAAM,QAAQ,aAAa,IAAI,CAAC,MAAM,CAAC;QACvC,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,MAAM,aAAa,CAAC,cAAc,SAAS,MAAM,MAAM,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS;QACzF,IAAI,cAAc,IAAI,CAAC,QAAQ,EAAE;YAC7B,IAAI,CAAC,QAAQ,CAAC,MAAM;YACpB,IAAI,CAAC,QAAQ,GAAG;YAChB,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK;QAC/B;QACA,IAAI,CAAC,YAAY;YACb,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,QAAQ,IAAI,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;YACrD,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,sBAAsB;YAClD,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB;gBACjC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACvB,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC;YACvB;QACJ;QACA,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,kBAAkB,CAAC;IACnD;IACA,oBAAoB,OAAO,EAAE,iBAAiB,EAAE,UAAU,EAAE,YAAY,EAAE;QACtE,IAAI,CAAC,iBAAiB,CAAC,QAAQ,MAAM,EAAE,mBAAmB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,YAAY;YACzE,OAAO;QACX,IAAI;IACR;IACA,kBAAkB,SAAS,EAAE,iBAAiB,EAAE,UAAU,EAAE,YAAY,EAAE;QACtE,MAAM,SAAS,IAAI,CAAC,qBAAqB,CAAC,mBAAmB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YAChE,sBAAsB;QAC1B,GAAG;QACH,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,QAAQ;IACzD;IACA,2BAA2B,SAAS,EAAE,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE;QACrE,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YAC9C,sBAAsB;QAC1B,GAAG;QACH,OAAO,IAAI,CAAC,qBAAqB,CAAC,WAAW,QAAQ;IACzD;IACA,sBAAsB,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE;QACjD,MAAM,eAAe,IAAI,CAAC,mBAAmB,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QAChD,MAAM,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;QACxB,OAAO,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,YAAY,IAAI,CAAC,iBAAiB,CAAC,eAAe;IAC3E;IACA,kBAAkB,YAAY,EAAE;QAC5B,OAAO;YACH,aAAa,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YAC9B,WAAW,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;YACtC,UAAU,IAAI,CAAC,YAAY,CAAC;QAChC;IACJ;IACA,oBAAoB,QAAQ,EAAE;QAC1B,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,OAAO,CAAC,IAAI,CAAC,aAAa;IACjD;IACA,aAAa,WAAW,EAAE;QACtB,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,aAAa,IAAI,CAAC,IAAI,CAAC,YAAY;IAChD;IACA,qBAAqB,SAAS,EAAE,KAAK,EAAE,aAAa,EAAE;QAClD,IAAI,SAAS;QACb,IAAI,OAAO;YACP,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,GAAG;gBACb,IAAI,YAAY,WAAW;oBACvB,UAAU,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,MAAM,iBAAiB;gBACnD,OAAO,IAAI,aAAa,WAAW;oBAC/B,UAAU,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;gBACpD;YACJ;QACJ;QACA,OAAO;IACX;IACA,mBAAmB;QACf,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACtB,IAAI,CAAC,cAAc,GAAG,CAAC,GAAG,EAAE,IAAI,iJAAA,CAAA,UAAI,EAAE;QAC1C;QACA,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,eAAe;QACX,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,cAAc;IAC9B;AACJ;AACA,iBAAiB,OAAO,CAAC,gJAAA,CAAA,UAAe;AACxC,iBAAiB,SAAS,GAAG,8KAAA,CAAA,UAAoB;uCAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1519, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_collection_widget.edit.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_collection_widget.edit.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    DataSource\r\n} from \"../../../common/data/data_source/data_source\";\r\nimport {\r\n    normalizeLoadResult\r\n} from \"../../../common/data/data_source/utils\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    noop\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    compileGetter\r\n} from \"../../../core/utils/data\";\r\nimport {\r\n    Deferred,\r\n    fromPromise,\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isDefined\r\n} from \"../../../core/utils/type\";\r\nimport errors from \"../../../ui/widget/ui.errors\";\r\nimport BaseCollectionWidget from \"../../ui/collection/collection_widget.base\";\r\nimport PlainEditStrategy from \"../../ui/collection/m_collection_widget.edit.strategy.plain\";\r\nimport Selection from \"../../ui/selection/m_selection\";\r\nconst ITEM_DELETING_DATA_KEY = \"dxItemDeleting\";\r\nconst SELECTED_ITEM_CLASS = \"dx-item-selected\";\r\nexport const NOT_EXISTING_INDEX = -1;\r\nconst indexExists = index => index !== NOT_EXISTING_INDEX;\r\nclass CollectionWidget extends BaseCollectionWidget {\r\n    _setOptionsByReference() {\r\n        super._setOptionsByReference();\r\n        extend(this._optionsByReference, {\r\n            selectedItem: true\r\n        })\r\n    }\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            selectionMode: \"none\",\r\n            selectionRequired: false,\r\n            selectByClick: true,\r\n            selectedItems: [],\r\n            selectedItemKeys: [],\r\n            maxFilterLengthInRequest: 1500,\r\n            keyExpr: null,\r\n            selectedIndex: NOT_EXISTING_INDEX,\r\n            focusOnSelectedItem: true,\r\n            selectedItem: null,\r\n            onSelectionChanging: null,\r\n            onSelectionChanged: null,\r\n            onItemReordered: null,\r\n            onItemDeleting: null,\r\n            onItemDeleted: null\r\n        })\r\n    }\r\n    ctor(element, options) {\r\n        this._userOptions = options || {};\r\n        super.ctor(element, options)\r\n    }\r\n    _init() {\r\n        this._initEditStrategy();\r\n        super._init();\r\n        this._initKeyGetter();\r\n        this._initActions();\r\n        this._initSelectionModule()\r\n    }\r\n    _initKeyGetter() {\r\n        this._keyGetter = compileGetter(this.option(\"keyExpr\"))\r\n    }\r\n    _selectedItemClass() {\r\n        return \"dx-item-selected\"\r\n    }\r\n    _getActionsList() {\r\n        return [\"onSelectionChanging\", \"onSelectionChanged\"]\r\n    }\r\n    _initActions() {\r\n        this._actions = {};\r\n        const actions = this._getActionsList();\r\n        actions.forEach((action => {\r\n            this._actions[action] = this._createActionByOption(action, {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            }) ?? noop\r\n        }))\r\n    }\r\n    _getKeysByItems(selectedItems) {\r\n        return this._editStrategy.getKeysByItems(selectedItems)\r\n    }\r\n    _getItemsByKeys(selectedItemKeys, selectedItems) {\r\n        return this._editStrategy.getItemsByKeys(selectedItemKeys, selectedItems)\r\n    }\r\n    _getKeyByIndex(index) {\r\n        return this._editStrategy.getKeyByIndex(index)\r\n    }\r\n    _getIndexByKey(key) {\r\n        return this._editStrategy.getIndexByKey(key)\r\n    }\r\n    _getIndexByItemData(itemData) {\r\n        return this._editStrategy.getIndexByItemData(itemData)\r\n    }\r\n    _isKeySpecified() {\r\n        return !!this._dataController.key()\r\n    }\r\n    _getCombinedFilter() {\r\n        return this._dataController.filter()\r\n    }\r\n    key() {\r\n        const {\r\n            keyExpr: keyExpr\r\n        } = this.option();\r\n        if (keyExpr) {\r\n            return keyExpr\r\n        }\r\n        return this._dataController.key()\r\n    }\r\n    keyOf(item) {\r\n        let key = item;\r\n        if (this.option(\"keyExpr\")) {\r\n            key = this._keyGetter(item)\r\n        } else if (this._dataController.store()) {\r\n            key = this._dataController.keyOf(item)\r\n        }\r\n        return key\r\n    }\r\n    _nullValueSelectionSupported() {\r\n        return false\r\n    }\r\n    _initSelectionModule() {\r\n        const that = this;\r\n        const {\r\n            itemsGetter: itemsGetter\r\n        } = this._editStrategy;\r\n        this._selection = new Selection({\r\n            allowNullValue: this._nullValueSelectionSupported(),\r\n            mode: this.option(\"selectionMode\"),\r\n            maxFilterLengthInRequest: this.option(\"maxFilterLengthInRequest\"),\r\n            equalByReference: !this._isKeySpecified(),\r\n            onSelectionChanging: args => {\r\n                var _this$_actions$onSele, _this$_actions;\r\n                const isSelectionChanged = args.addedItemKeys.length || args.removedItemKeys.length;\r\n                if (!this._rendered || !isSelectionChanged) {\r\n                    return\r\n                }\r\n                const selectionChangingArgs = {\r\n                    removedItems: args.removedItems,\r\n                    addedItems: args.addedItems,\r\n                    cancel: false\r\n                };\r\n                null === (_this$_actions$onSele = (_this$_actions = this._actions).onSelectionChanging) || void 0 === _this$_actions$onSele || _this$_actions$onSele.call(_this$_actions, selectionChangingArgs);\r\n                args.cancel = selectionChangingArgs.cancel\r\n            },\r\n            onSelectionChanged: args => {\r\n                if (args.addedItemKeys.length || args.removedItemKeys.length) {\r\n                    this.option(\"selectedItems\", this._getItemsByKeys(args.selectedItemKeys, args.selectedItems));\r\n                    this._updateSelectedItems(args)\r\n                }\r\n            },\r\n            filter: this._getCombinedFilter.bind(this),\r\n            totalCount: () => {\r\n                const {\r\n                    items: items\r\n                } = this.option();\r\n                const totalCount = this._dataController.totalCount();\r\n                return totalCount >= 0 ? totalCount : this._getItemsCount(items)\r\n            },\r\n            key: this.key.bind(this),\r\n            keyOf: this.keyOf.bind(this),\r\n            load(options) {\r\n                var _dataController$loadO;\r\n                const dataController = that._dataController;\r\n                options.customQueryParams = null === (_dataController$loadO = dataController.loadOptions()) || void 0 === _dataController$loadO ? void 0 : _dataController$loadO.customQueryParams;\r\n                options.userData = dataController.userData();\r\n                if (dataController.store()) {\r\n                    return dataController.loadFromStore(options).done((loadResult => {\r\n                        if (that._disposed) {\r\n                            return\r\n                        }\r\n                        const items = normalizeLoadResult(loadResult).data;\r\n                        dataController.applyMapFunction(items)\r\n                    }))\r\n                }\r\n                return Deferred().resolve(this.plainItems())\r\n            },\r\n            dataFields: () => this._dataController.select(),\r\n            plainItems: itemsGetter.bind(this._editStrategy)\r\n        })\r\n    }\r\n    _getItemsCount(items) {\r\n        return items.reduce(((itemsCount, item) => itemsCount + (item.items ? this._getItemsCount(item.items) : 1)), 0)\r\n    }\r\n    _initEditStrategy() {\r\n        this._editStrategy = new PlainEditStrategy(this)\r\n    }\r\n    _getSelectedItemIndices(keys) {\r\n        const indices = [];\r\n        keys = keys || this._selection.getSelectedItemKeys();\r\n        this._editStrategy.beginCache();\r\n        each(keys, ((_, key) => {\r\n            const selectedIndex = this._getIndexByKey(key);\r\n            if (indexExists(selectedIndex)) {\r\n                indices.push(selectedIndex)\r\n            }\r\n        }));\r\n        this._editStrategy.endCache();\r\n        return indices\r\n    }\r\n    _initMarkup() {\r\n        this._rendering = true;\r\n        if (!this._dataController.isLoading()) {\r\n            this._syncSelectionOptions().done((() => this._normalizeSelectedItems()))\r\n        }\r\n        super._initMarkup()\r\n    }\r\n    _render() {\r\n        super._render();\r\n        this._rendering = false\r\n    }\r\n    _fireContentReadyAction() {\r\n        this._rendering = false;\r\n        this._rendered = true;\r\n        super._fireContentReadyAction()\r\n    }\r\n    _syncSelectionOptions(byOption) {\r\n        byOption = byOption ?? this._chooseSelectOption();\r\n        let selectedItem;\r\n        let selectedIndex;\r\n        let selectedItemKeys;\r\n        let selectedItems;\r\n        switch (byOption) {\r\n            case \"selectedIndex\":\r\n                selectedItem = this._editStrategy.getItemDataByIndex(this.option(\"selectedIndex\"));\r\n                if (isDefined(selectedItem)) {\r\n                    this._setOptionWithoutOptionChange(\"selectedItems\", [selectedItem]);\r\n                    this._setOptionWithoutOptionChange(\"selectedItem\", selectedItem);\r\n                    this._setOptionWithoutOptionChange(\"selectedItemKeys\", this._editStrategy.getKeysByItems([selectedItem]))\r\n                } else {\r\n                    this._setOptionWithoutOptionChange(\"selectedItems\", []);\r\n                    this._setOptionWithoutOptionChange(\"selectedItemKeys\", []);\r\n                    this._setOptionWithoutOptionChange(\"selectedItem\", null)\r\n                }\r\n                break;\r\n            case \"selectedItems\":\r\n                selectedItems = this.option(\"selectedItems\") || [];\r\n                selectedIndex = selectedItems.length ? this._editStrategy.getIndexByItemData(selectedItems[0]) : NOT_EXISTING_INDEX;\r\n                if (this.option(\"selectionRequired\") && !indexExists(selectedIndex)) {\r\n                    return this._syncSelectionOptions(\"selectedIndex\")\r\n                }\r\n                this._setOptionWithoutOptionChange(\"selectedItem\", selectedItems[0]);\r\n                this._setOptionWithoutOptionChange(\"selectedIndex\", selectedIndex);\r\n                this._setOptionWithoutOptionChange(\"selectedItemKeys\", this._editStrategy.getKeysByItems(selectedItems));\r\n                break;\r\n            case \"selectedItem\":\r\n                selectedItem = this.option(\"selectedItem\");\r\n                selectedIndex = this._editStrategy.getIndexByItemData(selectedItem);\r\n                if (this.option(\"selectionRequired\") && !indexExists(selectedIndex)) {\r\n                    return this._syncSelectionOptions(\"selectedIndex\")\r\n                }\r\n                if (isDefined(selectedItem)) {\r\n                    this._setOptionWithoutOptionChange(\"selectedItems\", [selectedItem]);\r\n                    this._setOptionWithoutOptionChange(\"selectedIndex\", selectedIndex);\r\n                    this._setOptionWithoutOptionChange(\"selectedItemKeys\", this._editStrategy.getKeysByItems([selectedItem]))\r\n                } else {\r\n                    this._setOptionWithoutOptionChange(\"selectedItems\", []);\r\n                    this._setOptionWithoutOptionChange(\"selectedItemKeys\", []);\r\n                    this._setOptionWithoutOptionChange(\"selectedIndex\", NOT_EXISTING_INDEX)\r\n                }\r\n                break;\r\n            case \"selectedItemKeys\":\r\n                selectedItemKeys = this.option(\"selectedItemKeys\");\r\n                if (this.option(\"selectionRequired\")) {\r\n                    const selectedItemIndex = this._getIndexByKey(selectedItemKeys[0]);\r\n                    if (!indexExists(selectedItemIndex)) {\r\n                        return this._syncSelectionOptions(\"selectedIndex\")\r\n                    }\r\n                }\r\n                return this._selection.setSelection(selectedItemKeys)\r\n        }\r\n        return Deferred().resolve().promise()\r\n    }\r\n    _chooseSelectOption() {\r\n        let optionName = \"selectedIndex\";\r\n        const isOptionDefined = name => {\r\n            const optionValue = this.option(name);\r\n            const length = isDefined(optionValue) && optionValue.length;\r\n            return length || name in this._userOptions\r\n        };\r\n        if (isOptionDefined(\"selectedItems\")) {\r\n            optionName = \"selectedItems\"\r\n        } else if (isOptionDefined(\"selectedItem\")) {\r\n            optionName = \"selectedItem\"\r\n        } else if (isOptionDefined(\"selectedItemKeys\")) {\r\n            optionName = \"selectedItemKeys\"\r\n        }\r\n        return optionName\r\n    }\r\n    _compareKeys(oldKeys, newKeys) {\r\n        if (oldKeys.length !== newKeys.length) {\r\n            return false\r\n        }\r\n        for (let i = 0; i < newKeys.length; i++) {\r\n            if (oldKeys[i] !== newKeys[i]) {\r\n                return false\r\n            }\r\n        }\r\n        return true\r\n    }\r\n    _normalizeSelectedItems() {\r\n        const {\r\n            selectionMode: selectionMode,\r\n            selectedItems: selectedItems,\r\n            items: items\r\n        } = this.option();\r\n        if (\"none\" === selectionMode) {\r\n            this._setOptionWithoutOptionChange(\"selectedItems\", []);\r\n            this._syncSelectionOptions(\"selectedItems\")\r\n        } else if (\"single\" === selectionMode) {\r\n            const newSelection = selectedItems ?? [];\r\n            if (newSelection.length > 1 || !newSelection.length && this.option(\"selectionRequired\") && null !== items && void 0 !== items && items.length) {\r\n                var _normalizedSelection;\r\n                const currentSelection = this._selection.getSelectedItems();\r\n                let normalizedSelection = void 0 === newSelection[0] ? currentSelection[0] : newSelection[0];\r\n                if (void 0 === normalizedSelection) {\r\n                    normalizedSelection = this._editStrategy.itemsGetter()[0]\r\n                }\r\n                if (this.option(\"grouped\") && null !== (_normalizedSelection = normalizedSelection) && void 0 !== _normalizedSelection && _normalizedSelection.items) {\r\n                    normalizedSelection.items = [normalizedSelection.items[0]]\r\n                }\r\n                this._selection.setSelection(this._getKeysByItems([normalizedSelection]));\r\n                this._setOptionWithoutOptionChange(\"selectedItems\", [normalizedSelection]);\r\n                return this._syncSelectionOptions(\"selectedItems\")\r\n            }\r\n            this._selection.setSelection(this._getKeysByItems(newSelection))\r\n        } else {\r\n            const newKeys = this._getKeysByItems(this.option(\"selectedItems\"));\r\n            const oldKeys = this._selection.getSelectedItemKeys();\r\n            if (!this._compareKeys(oldKeys, newKeys)) {\r\n                this._selection.setSelection(newKeys)\r\n            }\r\n        }\r\n        return Deferred().resolve().promise()\r\n    }\r\n    _itemClickHandler(e, args, config) {\r\n        let itemSelectPromise = Deferred().resolve();\r\n        this._createAction((e => {\r\n            itemSelectPromise = this._itemSelectHandler(e.event) ?? itemSelectPromise\r\n        }), {\r\n            validatingTargetName: \"itemElement\"\r\n        })({\r\n            itemElement: $(e.currentTarget),\r\n            event: e\r\n        });\r\n        itemSelectPromise.always((() => {\r\n            super._itemClickHandler(e, args, config)\r\n        }))\r\n    }\r\n    _itemSelectHandler(e, shouldIgnoreSelectByClick) {\r\n        if (!shouldIgnoreSelectByClick && !this.option(\"selectByClick\")) {\r\n            return\r\n        }\r\n        const $itemElement = e.currentTarget;\r\n        if (this.isItemSelected($itemElement)) {\r\n            this.unselectItem(e.currentTarget)\r\n        } else {\r\n            const itemSelectPromise = this.selectItem(e.currentTarget);\r\n            return null === itemSelectPromise || void 0 === itemSelectPromise ? void 0 : itemSelectPromise.promise()\r\n        }\r\n    }\r\n    _selectedItemElement(index) {\r\n        return this._itemElements().eq(index)\r\n    }\r\n    _postprocessRenderItem(args) {\r\n        const {\r\n            selectionMode: selectionMode\r\n        } = this.option();\r\n        if (\"none\" !== selectionMode) {\r\n            const $itemElement = $(args.itemElement);\r\n            const normalizedItemIndex = this._editStrategy.getNormalizedIndex($itemElement);\r\n            const isItemSelected = this._isItemSelected(normalizedItemIndex);\r\n            this._processSelectableItem($itemElement, isItemSelected)\r\n        }\r\n    }\r\n    _processSelectableItem($itemElement, isSelected) {\r\n        $itemElement.toggleClass(this._selectedItemClass(), isSelected);\r\n        this._setAriaSelectionAttribute($itemElement, String(isSelected))\r\n    }\r\n    _updateSelectedItems(args) {\r\n        const {\r\n            addedItemKeys: addedItemKeys,\r\n            removedItemKeys: removedItemKeys\r\n        } = args;\r\n        if (this._rendered && (addedItemKeys.length || removedItemKeys.length)) {\r\n            if (!this._rendering) {\r\n                const addedSelection = [];\r\n                const removedSelection = [];\r\n                this._editStrategy.beginCache();\r\n                for (let i = 0; i < addedItemKeys.length; i += 1) {\r\n                    const normalizedIndex = this._getIndexByKey(addedItemKeys[i]);\r\n                    addedSelection.push(normalizedIndex);\r\n                    this._addSelection(normalizedIndex)\r\n                }\r\n                for (let i = 0; i < removedItemKeys.length; i += 1) {\r\n                    const normalizedIndex = this._getIndexByKey(removedItemKeys[i]);\r\n                    removedSelection.push(normalizedIndex);\r\n                    this._removeSelection(normalizedIndex)\r\n                }\r\n                this._editStrategy.endCache();\r\n                this._updateSelection(addedSelection, removedSelection)\r\n            }\r\n            this._actions.onSelectionChanged({\r\n                addedItems: args.addedItems,\r\n                removedItems: args.removedItems\r\n            })\r\n        }\r\n    }\r\n    _updateSelection(addedSelection, removedSelection) {}\r\n    _setAriaSelectionAttribute($target, value) {\r\n        this.setAria(\"selected\", value, $target)\r\n    }\r\n    _getFocusedElementIndex() {\r\n        const {\r\n            focusOnSelectedItem: focusOnSelectedItem\r\n        } = this.option();\r\n        return focusOnSelectedItem ? this._getFlatIndex() : super._getFocusedElementIndex()\r\n    }\r\n    _getFlatIndex() {\r\n        const {\r\n            selectedIndex: selectedIndex = NOT_EXISTING_INDEX\r\n        } = this.option();\r\n        return selectedIndex\r\n    }\r\n    _removeSelection(normalizedIndex) {\r\n        const $itemElement = this._editStrategy.getItemElement(normalizedIndex);\r\n        if (indexExists(normalizedIndex)) {\r\n            this._processSelectableItem($itemElement, false);\r\n            eventsEngine.triggerHandler($itemElement, \"stateChanged\", false)\r\n        }\r\n    }\r\n    _addSelection(normalizedIndex) {\r\n        const $itemElement = this._editStrategy.getItemElement(normalizedIndex);\r\n        if (indexExists(normalizedIndex)) {\r\n            this._processSelectableItem($itemElement, true);\r\n            eventsEngine.triggerHandler($itemElement, \"stateChanged\", true)\r\n        }\r\n    }\r\n    _isItemSelected(index) {\r\n        const key = this._getKeyByIndex(index);\r\n        return this._selection.isItemSelected(key, {\r\n            checkPending: true\r\n        })\r\n    }\r\n    _optionChanged(args) {\r\n        switch (args.name) {\r\n            case \"selectionMode\":\r\n                this._invalidate();\r\n                break;\r\n            case \"dataSource\":\r\n                if (!args.value || Array.isArray(args.value) && !args.value.length) {\r\n                    this.option(\"selectedItemKeys\", [])\r\n                }\r\n                super._optionChanged(args);\r\n                break;\r\n            case \"selectedIndex\":\r\n            case \"selectedItem\":\r\n            case \"selectedItems\":\r\n            case \"selectedItemKeys\":\r\n                this._syncSelectionOptions(args.name).done((() => this._normalizeSelectedItems()));\r\n                break;\r\n            case \"keyExpr\":\r\n                this._initKeyGetter();\r\n                break;\r\n            case \"selectionRequired\":\r\n                this._normalizeSelectedItems();\r\n                break;\r\n            case \"onSelectionChanging\":\r\n            case \"onSelectionChanged\":\r\n                this._initActions();\r\n                break;\r\n            case \"selectByClick\":\r\n            case \"onItemDeleting\":\r\n            case \"onItemDeleted\":\r\n            case \"onItemReordered\":\r\n            case \"maxFilterLengthInRequest\":\r\n            case \"focusOnSelectedItem\":\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    _clearSelectedItems() {\r\n        this._setOptionWithoutOptionChange(\"selectedItems\", []);\r\n        this._syncSelectionOptions(\"selectedItems\")\r\n    }\r\n    _waitDeletingPrepare($itemElement) {\r\n        if ($itemElement.data(\"dxItemDeleting\")) {\r\n            return Deferred().resolve().promise()\r\n        }\r\n        $itemElement.data(\"dxItemDeleting\", true);\r\n        const deferred = Deferred();\r\n        const deletingActionArgs = {\r\n            cancel: false\r\n        };\r\n        const deletePromise = this._itemEventHandler($itemElement, \"onItemDeleting\", deletingActionArgs, {\r\n            excludeValidators: [\"disabled\", \"readOnly\"]\r\n        });\r\n        when(deletePromise).always((function(value) {\r\n            const deletePromiseExists = !deletePromise;\r\n            const deletePromiseResolved = !deletePromiseExists && \"resolved\" === deletePromise.state();\r\n            const argumentsSpecified = !!arguments.length;\r\n            const shouldDelete = deletePromiseExists || deletePromiseResolved && !argumentsSpecified || deletePromiseResolved && value;\r\n            when(fromPromise(deletingActionArgs.cancel)).always((() => {\r\n                $itemElement.data(\"dxItemDeleting\", false)\r\n            })).done((cancel => {\r\n                if (shouldDelete && !cancel) {\r\n                    deferred.resolve()\r\n                } else {\r\n                    deferred.reject()\r\n                }\r\n            })).fail(deferred.reject)\r\n        }));\r\n        return deferred.promise()\r\n    }\r\n    _deleteItemFromDS($item) {\r\n        const dataController = this._dataController;\r\n        const deferred = Deferred();\r\n        const disabledState = this.option(\"disabled\");\r\n        const dataStore = dataController.store();\r\n        if (!dataStore) {\r\n            return Deferred().resolve().promise()\r\n        }\r\n        if (!dataStore.remove) {\r\n            throw errors.Error(\"E1011\")\r\n        }\r\n        this.option(\"disabled\", true);\r\n        dataStore.remove(dataController.keyOf(this._getItemData($item))).done((key => {\r\n            if (void 0 !== key) {\r\n                deferred.resolve()\r\n            } else {\r\n                deferred.reject()\r\n            }\r\n        })).fail((() => {\r\n            deferred.reject()\r\n        }));\r\n        deferred.always((() => {\r\n            this.option(\"disabled\", disabledState)\r\n        }));\r\n        return deferred\r\n    }\r\n    _tryRefreshLastPage() {\r\n        const deferred = Deferred();\r\n        if (this._isLastPage() || this.option(\"grouped\")) {\r\n            deferred.resolve()\r\n        } else {\r\n            this._refreshLastPage().done((() => {\r\n                deferred.resolve()\r\n            }))\r\n        }\r\n        return deferred.promise()\r\n    }\r\n    _refreshLastPage() {\r\n        this._expectLastItemLoading();\r\n        return this._dataController.load()\r\n    }\r\n    _updateSelectionAfterDelete(index) {\r\n        const key = this._getKeyByIndex(index);\r\n        this._selection.deselect([key])\r\n    }\r\n    _updateIndicesAfterIndex(index) {\r\n        const itemElements = this._itemElements();\r\n        for (let i = index + 1; i < itemElements.length; i += 1) {\r\n            $(itemElements[i]).data(this._itemIndexKey(), i - 1)\r\n        }\r\n    }\r\n    _simulateOptionChange(optionName) {\r\n        var _this$_optionChangedA;\r\n        const optionValue = this.option(optionName);\r\n        if (optionValue instanceof DataSource) {\r\n            return\r\n        }\r\n        null === (_this$_optionChangedA = this._optionChangedAction) || void 0 === _this$_optionChangedA || _this$_optionChangedA.call(this, {\r\n            name: optionName,\r\n            fullName: optionName,\r\n            value: optionValue\r\n        })\r\n    }\r\n    isItemSelected(itemElement) {\r\n        return this._isItemSelected(this._editStrategy.getNormalizedIndex(itemElement))\r\n    }\r\n    selectItem(itemElement) {\r\n        const {\r\n            selectionMode: selectionMode\r\n        } = this.option();\r\n        if (\"none\" === selectionMode) {\r\n            return Deferred().resolve()\r\n        }\r\n        const itemIndex = this._editStrategy.getNormalizedIndex(itemElement);\r\n        if (!indexExists(itemIndex)) {\r\n            return Deferred().resolve()\r\n        }\r\n        const key = this._getKeyByIndex(itemIndex);\r\n        if (this._selection.isItemSelected(key)) {\r\n            return Deferred().resolve()\r\n        }\r\n        if (\"single\" === selectionMode) {\r\n            return this._selection.setSelection([key])\r\n        }\r\n        const {\r\n            selectedItemKeys: selectedItemKeys\r\n        } = this.option();\r\n        return this._selection.setSelection([...selectedItemKeys ?? [], key], [key])\r\n    }\r\n    unselectItem(itemElement) {\r\n        const itemIndex = this._editStrategy.getNormalizedIndex(itemElement);\r\n        if (!indexExists(itemIndex)) {\r\n            return\r\n        }\r\n        const selectedItemKeys = this._selection.getSelectedItemKeys();\r\n        if (this.option(\"selectionRequired\") && selectedItemKeys.length <= 1) {\r\n            return\r\n        }\r\n        const key = this._getKeyByIndex(itemIndex);\r\n        if (!this._selection.isItemSelected(key, {\r\n                checkPending: true\r\n            })) {\r\n            return\r\n        }\r\n        this._selection.deselect([key])\r\n    }\r\n    _deleteItemElementByIndex(index) {\r\n        this._updateSelectionAfterDelete(index);\r\n        this._updateIndicesAfterIndex(index);\r\n        this._editStrategy.deleteItemAtIndex(index)\r\n    }\r\n    _afterItemElementDeleted($item, deletedActionArgs) {\r\n        const changingOption = this._dataController.getDataSource() ? \"dataSource\" : \"items\";\r\n        this._simulateOptionChange(changingOption);\r\n        this._itemEventHandler($item, \"onItemDeleted\", deletedActionArgs, {\r\n            beforeExecute() {\r\n                $item.remove()\r\n            },\r\n            excludeValidators: [\"disabled\", \"readOnly\"]\r\n        });\r\n        this._renderEmptyMessage()\r\n    }\r\n    deleteItem(itemElement) {\r\n        const deferred = Deferred();\r\n        const $item = this._editStrategy.getItemElement(itemElement);\r\n        const index = this._editStrategy.getNormalizedIndex(itemElement);\r\n        const itemResponseWaitClass = this._itemResponseWaitClass();\r\n        if (indexExists(index)) {\r\n            this._waitDeletingPrepare($item).done((() => {\r\n                $item.addClass(itemResponseWaitClass);\r\n                const deletedActionArgs = this._extendActionArgs($item);\r\n                this._deleteItemFromDS($item).done((() => {\r\n                    this._deleteItemElementByIndex(index);\r\n                    this._afterItemElementDeleted($item, deletedActionArgs);\r\n                    this._tryRefreshLastPage().done((() => {\r\n                        deferred.resolveWith(this)\r\n                    }))\r\n                })).fail((() => {\r\n                    $item.removeClass(itemResponseWaitClass);\r\n                    deferred.rejectWith(this)\r\n                }))\r\n            })).fail((() => {\r\n                deferred.rejectWith(this)\r\n            }))\r\n        } else {\r\n            deferred.rejectWith(this)\r\n        }\r\n        return deferred.promise()\r\n    }\r\n    reorderItem(itemElement, toItemElement) {\r\n        const deferred = Deferred();\r\n        const strategy = this._editStrategy;\r\n        const $movingItem = strategy.getItemElement(itemElement);\r\n        const $destinationItem = strategy.getItemElement(toItemElement);\r\n        const movingIndex = strategy.getNormalizedIndex(itemElement);\r\n        const destinationIndex = strategy.getNormalizedIndex(toItemElement);\r\n        const changingOption = this._dataController.getDataSource() ? \"dataSource\" : \"items\";\r\n        const canMoveItems = indexExists(movingIndex) && indexExists(destinationIndex) && movingIndex !== destinationIndex;\r\n        if (canMoveItems) {\r\n            deferred.resolveWith(this)\r\n        } else {\r\n            deferred.rejectWith(this)\r\n        }\r\n        return deferred.promise().done((() => {\r\n            $destinationItem[strategy.itemPlacementFunc(movingIndex, destinationIndex)]($movingItem);\r\n            strategy.moveItemAtIndexToIndex(movingIndex, destinationIndex);\r\n            this._updateIndicesAfterIndex(movingIndex);\r\n            this.option(\"selectedItems\", this._getItemsByKeys(this._selection.getSelectedItemKeys(), this._selection.getSelectedItems()));\r\n            if (\"items\" === changingOption) {\r\n                this._simulateOptionChange(changingOption)\r\n            }\r\n            this._itemEventHandler($movingItem, \"onItemReordered\", {\r\n                fromIndex: strategy.getIndex(movingIndex),\r\n                toIndex: strategy.getIndex(destinationIndex)\r\n            }, {\r\n                excludeValidators: [\"disabled\", \"readOnly\"]\r\n            })\r\n        }))\r\n    }\r\n}\r\nexport default CollectionWidget;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AACA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAKA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AACA;AACA;AACA;;;;;;;;;;;;;;;;AACA,MAAM,yBAAyB;AAC/B,MAAM,sBAAsB;AACrB,MAAM,qBAAqB,CAAC;AACnC,MAAM,cAAc,CAAA,QAAS,UAAU;AACvC,MAAM,yBAAyB,mMAAA,CAAA,UAAoB;IAC/C,yBAAyB;QACrB,KAAK,CAAC;QACN,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC7B,cAAc;QAClB;IACJ;IACA,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,eAAe;YACf,mBAAmB;YACnB,eAAe;YACf,eAAe,EAAE;YACjB,kBAAkB,EAAE;YACpB,0BAA0B;YAC1B,SAAS;YACT,eAAe;YACf,qBAAqB;YACrB,cAAc;YACd,qBAAqB;YACrB,oBAAoB;YACpB,iBAAiB;YACjB,gBAAgB;YAChB,eAAe;QACnB;IACJ;IACA,KAAK,OAAO,EAAE,OAAO,EAAE;QACnB,IAAI,CAAC,YAAY,GAAG,WAAW,CAAC;QAChC,KAAK,CAAC,KAAK,SAAS;IACxB;IACA,QAAQ;QACJ,IAAI,CAAC,iBAAiB;QACtB,KAAK,CAAC;QACN,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,oBAAoB;IAC7B;IACA,iBAAiB;QACb,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC;IAChD;IACA,qBAAqB;QACjB,OAAO;IACX;IACA,kBAAkB;QACd,OAAO;YAAC;YAAuB;SAAqB;IACxD;IACA,eAAe;QACX,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,MAAM,UAAU,IAAI,CAAC,eAAe;QACpC,QAAQ,OAAO,CAAE,CAAA;YACb,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ;gBACvD,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C,MAAM,+KAAA,CAAA,OAAI;QACd;IACJ;IACA,gBAAgB,aAAa,EAAE;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;IAC7C;IACA,gBAAgB,gBAAgB,EAAE,aAAa,EAAE;QAC7C,OAAO,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,kBAAkB;IAC/D;IACA,eAAe,KAAK,EAAE;QAClB,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;IAC5C;IACA,eAAe,GAAG,EAAE;QAChB,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;IAC5C;IACA,oBAAoB,QAAQ,EAAE;QAC1B,OAAO,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;IACjD;IACA,kBAAkB;QACd,OAAO,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG;IACrC;IACA,qBAAqB;QACjB,OAAO,IAAI,CAAC,eAAe,CAAC,MAAM;IACtC;IACA,MAAM;QACF,MAAM,EACF,SAAS,OAAO,EACnB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,SAAS;YACT,OAAO;QACX;QACA,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG;IACnC;IACA,MAAM,IAAI,EAAE;QACR,IAAI,MAAM;QACV,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;YACxB,MAAM,IAAI,CAAC,UAAU,CAAC;QAC1B,OAAO,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI;YACrC,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;QACrC;QACA,OAAO;IACX;IACA,+BAA+B;QAC3B,OAAO;IACX;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG,IAAI,CAAC,aAAa;QACtB,IAAI,CAAC,UAAU,GAAG,IAAI,oLAAA,CAAA,UAAS,CAAC;YAC5B,gBAAgB,IAAI,CAAC,4BAA4B;YACjD,MAAM,IAAI,CAAC,MAAM,CAAC;YAClB,0BAA0B,IAAI,CAAC,MAAM,CAAC;YACtC,kBAAkB,CAAC,IAAI,CAAC,eAAe;YACvC,qBAAqB,CAAA;gBACjB,IAAI,uBAAuB;gBAC3B,MAAM,qBAAqB,KAAK,aAAa,CAAC,MAAM,IAAI,KAAK,eAAe,CAAC,MAAM;gBACnF,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,oBAAoB;oBACxC;gBACJ;gBACA,MAAM,wBAAwB;oBAC1B,cAAc,KAAK,YAAY;oBAC/B,YAAY,KAAK,UAAU;oBAC3B,QAAQ;gBACZ;gBACA,SAAS,CAAC,wBAAwB,CAAC,iBAAiB,IAAI,CAAC,QAAQ,EAAE,mBAAmB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,gBAAgB;gBAC1K,KAAK,MAAM,GAAG,sBAAsB,MAAM;YAC9C;YACA,oBAAoB,CAAA;gBAChB,IAAI,KAAK,aAAa,CAAC,MAAM,IAAI,KAAK,eAAe,CAAC,MAAM,EAAE;oBAC1D,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,eAAe,CAAC,KAAK,gBAAgB,EAAE,KAAK,aAAa;oBAC3F,IAAI,CAAC,oBAAoB,CAAC;gBAC9B;YACJ;YACA,QAAQ,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;YACzC,YAAY;gBACR,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,MAAM;gBACf,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,UAAU;gBAClD,OAAO,cAAc,IAAI,aAAa,IAAI,CAAC,cAAc,CAAC;YAC9D;YACA,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;YAC3B,MAAK,OAAO;gBACR,IAAI;gBACJ,MAAM,iBAAiB,KAAK,eAAe;gBAC3C,QAAQ,iBAAiB,GAAG,SAAS,CAAC,wBAAwB,eAAe,WAAW,EAAE,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,iBAAiB;gBAClL,QAAQ,QAAQ,GAAG,eAAe,QAAQ;gBAC1C,IAAI,eAAe,KAAK,IAAI;oBACxB,OAAO,eAAe,aAAa,CAAC,SAAS,IAAI,CAAE,CAAA;wBAC/C,IAAI,KAAK,SAAS,EAAE;4BAChB;wBACJ;wBACA,MAAM,QAAQ,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE,YAAY,IAAI;wBAClD,eAAe,gBAAgB,CAAC;oBACpC;gBACJ;gBACA,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,CAAC,IAAI,CAAC,UAAU;YAC7C;YACA,YAAY,IAAM,IAAI,CAAC,eAAe,CAAC,MAAM;YAC7C,YAAY,YAAY,IAAI,CAAC,IAAI,CAAC,aAAa;QACnD;IACJ;IACA,eAAe,KAAK,EAAE;QAClB,OAAO,MAAM,MAAM,CAAE,CAAC,YAAY,OAAS,aAAa,CAAC,KAAK,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK,IAAI,CAAC,GAAI;IACjH;IACA,oBAAoB;QAChB,IAAI,CAAC,aAAa,GAAG,IAAI,0NAAA,CAAA,UAAiB,CAAC,IAAI;IACnD;IACA,wBAAwB,IAAI,EAAE;QAC1B,MAAM,UAAU,EAAE;QAClB,OAAO,QAAQ,IAAI,CAAC,UAAU,CAAC,mBAAmB;QAClD,IAAI,CAAC,aAAa,CAAC,UAAU;QAC7B,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,MAAO,CAAC,GAAG;YACZ,MAAM,gBAAgB,IAAI,CAAC,cAAc,CAAC;YAC1C,IAAI,YAAY,gBAAgB;gBAC5B,QAAQ,IAAI,CAAC;YACjB;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,QAAQ;QAC3B,OAAO;IACX;IACA,cAAc;QACV,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI;YACnC,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAE,IAAM,IAAI,CAAC,uBAAuB;QACzE;QACA,KAAK,CAAC;IACV;IACA,UAAU;QACN,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,0BAA0B;QACtB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,KAAK,CAAC;IACV;IACA,sBAAsB,QAAQ,EAAE;QAC5B,WAAW,YAAY,IAAI,CAAC,mBAAmB;QAC/C,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAQ;YACJ,KAAK;gBACD,eAAe,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,IAAI,CAAC,MAAM,CAAC;gBACjE,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;oBACzB,IAAI,CAAC,6BAA6B,CAAC,iBAAiB;wBAAC;qBAAa;oBAClE,IAAI,CAAC,6BAA6B,CAAC,gBAAgB;oBACnD,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;wBAAC;qBAAa;gBAC3G,OAAO;oBACH,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE;oBACtD,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,EAAE;oBACzD,IAAI,CAAC,6BAA6B,CAAC,gBAAgB;gBACvD;gBACA;YACJ,KAAK;gBACD,gBAAgB,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBAClD,gBAAgB,cAAc,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC,aAAa,CAAC,EAAE,IAAI;gBACjG,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,YAAY,gBAAgB;oBACjE,OAAO,IAAI,CAAC,qBAAqB,CAAC;gBACtC;gBACA,IAAI,CAAC,6BAA6B,CAAC,gBAAgB,aAAa,CAAC,EAAE;gBACnE,IAAI,CAAC,6BAA6B,CAAC,iBAAiB;gBACpD,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;gBACzF;YACJ,KAAK;gBACD,eAAe,IAAI,CAAC,MAAM,CAAC;gBAC3B,gBAAgB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;gBACtD,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,CAAC,YAAY,gBAAgB;oBACjE,OAAO,IAAI,CAAC,qBAAqB,CAAC;gBACtC;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,eAAe;oBACzB,IAAI,CAAC,6BAA6B,CAAC,iBAAiB;wBAAC;qBAAa;oBAClE,IAAI,CAAC,6BAA6B,CAAC,iBAAiB;oBACpD,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;wBAAC;qBAAa;gBAC3G,OAAO;oBACH,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE;oBACtD,IAAI,CAAC,6BAA6B,CAAC,oBAAoB,EAAE;oBACzD,IAAI,CAAC,6BAA6B,CAAC,iBAAiB;gBACxD;gBACA;YACJ,KAAK;gBACD,mBAAmB,IAAI,CAAC,MAAM,CAAC;gBAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB;oBAClC,MAAM,oBAAoB,IAAI,CAAC,cAAc,CAAC,gBAAgB,CAAC,EAAE;oBACjE,IAAI,CAAC,YAAY,oBAAoB;wBACjC,OAAO,IAAI,CAAC,qBAAqB,CAAC;oBACtC;gBACJ;gBACA,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;QAC5C;QACA,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;IACvC;IACA,sBAAsB;QAClB,IAAI,aAAa;QACjB,MAAM,kBAAkB,CAAA;YACpB,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC;YAChC,MAAM,SAAS,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,YAAY,MAAM;YAC3D,OAAO,UAAU,QAAQ,IAAI,CAAC,YAAY;QAC9C;QACA,IAAI,gBAAgB,kBAAkB;YAClC,aAAa;QACjB,OAAO,IAAI,gBAAgB,iBAAiB;YACxC,aAAa;QACjB,OAAO,IAAI,gBAAgB,qBAAqB;YAC5C,aAAa;QACjB;QACA,OAAO;IACX;IACA,aAAa,OAAO,EAAE,OAAO,EAAE;QAC3B,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;YACnC,OAAO;QACX;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,IAAI,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,EAAE;gBAC3B,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,0BAA0B;QACtB,MAAM,EACF,eAAe,aAAa,EAC5B,eAAe,aAAa,EAC5B,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,WAAW,eAAe;YAC1B,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE;YACtD,IAAI,CAAC,qBAAqB,CAAC;QAC/B,OAAO,IAAI,aAAa,eAAe;YACnC,MAAM,eAAe,iBAAiB,EAAE;YACxC,IAAI,aAAa,MAAM,GAAG,KAAK,CAAC,aAAa,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,MAAM,EAAE;gBAC3I,IAAI;gBACJ,MAAM,mBAAmB,IAAI,CAAC,UAAU,CAAC,gBAAgB;gBACzD,IAAI,sBAAsB,KAAK,MAAM,YAAY,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE;gBAC5F,IAAI,KAAK,MAAM,qBAAqB;oBAChC,sBAAsB,IAAI,CAAC,aAAa,CAAC,WAAW,EAAE,CAAC,EAAE;gBAC7D;gBACA,IAAI,IAAI,CAAC,MAAM,CAAC,cAAc,SAAS,CAAC,uBAAuB,mBAAmB,KAAK,KAAK,MAAM,wBAAwB,qBAAqB,KAAK,EAAE;oBAClJ,oBAAoB,KAAK,GAAG;wBAAC,oBAAoB,KAAK,CAAC,EAAE;qBAAC;gBAC9D;gBACA,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;oBAAC;iBAAoB;gBACvE,IAAI,CAAC,6BAA6B,CAAC,iBAAiB;oBAAC;iBAAoB;gBACzE,OAAO,IAAI,CAAC,qBAAqB,CAAC;YACtC;YACA,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,eAAe,CAAC;QACtD,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC;YACjD,MAAM,UAAU,IAAI,CAAC,UAAU,CAAC,mBAAmB;YACnD,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,UAAU;gBACtC,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;YACjC;QACJ;QACA,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;IACvC;IACA,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;QAC/B,IAAI,oBAAoB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO;QAC1C,IAAI,CAAC,aAAa,CAAE,CAAA;YAChB,oBAAoB,IAAI,CAAC,kBAAkB,CAAC,EAAE,KAAK,KAAK;QAC5D,GAAI;YACA,sBAAsB;QAC1B,GAAG;YACC,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa;YAC9B,OAAO;QACX;QACA,kBAAkB,MAAM,CAAE;YACtB,KAAK,CAAC,kBAAkB,GAAG,MAAM;QACrC;IACJ;IACA,mBAAmB,CAAC,EAAE,yBAAyB,EAAE;QAC7C,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC7D;QACJ;QACA,MAAM,eAAe,EAAE,aAAa;QACpC,IAAI,IAAI,CAAC,cAAc,CAAC,eAAe;YACnC,IAAI,CAAC,YAAY,CAAC,EAAE,aAAa;QACrC,OAAO;YACH,MAAM,oBAAoB,IAAI,CAAC,UAAU,CAAC,EAAE,aAAa;YACzD,OAAO,SAAS,qBAAqB,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,OAAO;QAC1G;IACJ;IACA,qBAAqB,KAAK,EAAE;QACxB,OAAO,IAAI,CAAC,aAAa,GAAG,EAAE,CAAC;IACnC;IACA,uBAAuB,IAAI,EAAE;QACzB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,WAAW,eAAe;YAC1B,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,KAAK,WAAW;YACvC,MAAM,sBAAsB,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;YAClE,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC;YAC5C,IAAI,CAAC,sBAAsB,CAAC,cAAc;QAC9C;IACJ;IACA,uBAAuB,YAAY,EAAE,UAAU,EAAE;QAC7C,aAAa,WAAW,CAAC,IAAI,CAAC,kBAAkB,IAAI;QACpD,IAAI,CAAC,0BAA0B,CAAC,cAAc,OAAO;IACzD;IACA,qBAAqB,IAAI,EAAE;QACvB,MAAM,EACF,eAAe,aAAa,EAC5B,iBAAiB,eAAe,EACnC,GAAG;QACJ,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,cAAc,MAAM,IAAI,gBAAgB,MAAM,GAAG;YACpE,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;gBAClB,MAAM,iBAAiB,EAAE;gBACzB,MAAM,mBAAmB,EAAE;gBAC3B,IAAI,CAAC,aAAa,CAAC,UAAU;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,KAAK,EAAG;oBAC9C,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,EAAE;oBAC5D,eAAe,IAAI,CAAC;oBACpB,IAAI,CAAC,aAAa,CAAC;gBACvB;gBACA,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,KAAK,EAAG;oBAChD,MAAM,kBAAkB,IAAI,CAAC,cAAc,CAAC,eAAe,CAAC,EAAE;oBAC9D,iBAAiB,IAAI,CAAC;oBACtB,IAAI,CAAC,gBAAgB,CAAC;gBAC1B;gBACA,IAAI,CAAC,aAAa,CAAC,QAAQ;gBAC3B,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;YAC1C;YACA,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC;gBAC7B,YAAY,KAAK,UAAU;gBAC3B,cAAc,KAAK,YAAY;YACnC;QACJ;IACJ;IACA,iBAAiB,cAAc,EAAE,gBAAgB,EAAE,CAAC;IACpD,2BAA2B,OAAO,EAAE,KAAK,EAAE;QACvC,IAAI,CAAC,OAAO,CAAC,YAAY,OAAO;IACpC;IACA,0BAA0B;QACtB,MAAM,EACF,qBAAqB,mBAAmB,EAC3C,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,sBAAsB,IAAI,CAAC,aAAa,KAAK,KAAK,CAAC;IAC9D;IACA,gBAAgB;QACZ,MAAM,EACF,eAAe,gBAAgB,kBAAkB,EACpD,GAAG,IAAI,CAAC,MAAM;QACf,OAAO;IACX;IACA,iBAAiB,eAAe,EAAE;QAC9B,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QACvD,IAAI,YAAY,kBAAkB;YAC9B,IAAI,CAAC,sBAAsB,CAAC,cAAc;YAC1C,uLAAA,CAAA,UAAY,CAAC,cAAc,CAAC,cAAc,gBAAgB;QAC9D;IACJ;IACA,cAAc,eAAe,EAAE;QAC3B,MAAM,eAAe,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QACvD,IAAI,YAAY,kBAAkB;YAC9B,IAAI,CAAC,sBAAsB,CAAC,cAAc;YAC1C,uLAAA,CAAA,UAAY,CAAC,cAAc,CAAC,cAAc,gBAAgB;QAC9D;IACJ;IACA,gBAAgB,KAAK,EAAE;QACnB,MAAM,MAAM,IAAI,CAAC,cAAc,CAAC;QAChC,OAAO,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK;YACvC,cAAc;QAClB;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,OAAQ,KAAK,IAAI;YACb,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,KAAK,KAAK,IAAI,MAAM,OAAO,CAAC,KAAK,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,MAAM,EAAE;oBAChE,IAAI,CAAC,MAAM,CAAC,oBAAoB,EAAE;gBACtC;gBACA,KAAK,CAAC,eAAe;gBACrB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,qBAAqB,CAAC,KAAK,IAAI,EAAE,IAAI,CAAE,IAAM,IAAI,CAAC,uBAAuB;gBAC9E;YACJ,KAAK;gBACD,IAAI,CAAC,cAAc;gBACnB;YACJ,KAAK;gBACD,IAAI,CAAC,uBAAuB;gBAC5B;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,YAAY;gBACjB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,sBAAsB;QAClB,IAAI,CAAC,6BAA6B,CAAC,iBAAiB,EAAE;QACtD,IAAI,CAAC,qBAAqB,CAAC;IAC/B;IACA,qBAAqB,YAAY,EAAE;QAC/B,IAAI,aAAa,IAAI,CAAC,mBAAmB;YACrC,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;QACvC;QACA,aAAa,IAAI,CAAC,kBAAkB;QACpC,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,MAAM,qBAAqB;YACvB,QAAQ;QACZ;QACA,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,cAAc,kBAAkB,oBAAoB;YAC7F,mBAAmB;gBAAC;gBAAY;aAAW;QAC/C;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,eAAe,MAAM,CAAE,SAAS,KAAK;YACtC,MAAM,sBAAsB,CAAC;YAC7B,MAAM,wBAAwB,CAAC,uBAAuB,eAAe,cAAc,KAAK;YACxF,MAAM,qBAAqB,CAAC,CAAC,UAAU,MAAM;YAC7C,MAAM,eAAe,uBAAuB,yBAAyB,CAAC,sBAAsB,yBAAyB;YACrH,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,iLAAA,CAAA,cAAW,AAAD,EAAE,mBAAmB,MAAM,GAAG,MAAM,CAAE;gBACjD,aAAa,IAAI,CAAC,kBAAkB;YACxC,GAAI,IAAI,CAAE,CAAA;gBACN,IAAI,gBAAgB,CAAC,QAAQ;oBACzB,SAAS,OAAO;gBACpB,OAAO;oBACH,SAAS,MAAM;gBACnB;YACJ,GAAI,IAAI,CAAC,SAAS,MAAM;QAC5B;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,kBAAkB,KAAK,EAAE;QACrB,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;QAClC,MAAM,YAAY,eAAe,KAAK;QACtC,IAAI,CAAC,WAAW;YACZ,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO,GAAG,OAAO;QACvC;QACA,IAAI,CAAC,UAAU,MAAM,EAAE;YACnB,MAAM,iKAAA,CAAA,UAAM,CAAC,KAAK,CAAC;QACvB;QACA,IAAI,CAAC,MAAM,CAAC,YAAY;QACxB,UAAU,MAAM,CAAC,eAAe,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,SAAS,IAAI,CAAE,CAAA;YACnE,IAAI,KAAK,MAAM,KAAK;gBAChB,SAAS,OAAO;YACpB,OAAO;gBACH,SAAS,MAAM;YACnB;QACJ,GAAI,IAAI,CAAE;YACN,SAAS,MAAM;QACnB;QACA,SAAS,MAAM,CAAE;YACb,IAAI,CAAC,MAAM,CAAC,YAAY;QAC5B;QACA,OAAO;IACX;IACA,sBAAsB;QAClB,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,IAAI,IAAI,CAAC,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,YAAY;YAC9C,SAAS,OAAO;QACpB,OAAO;YACH,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAE;gBAC1B,SAAS,OAAO;YACpB;QACJ;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,mBAAmB;QACf,IAAI,CAAC,sBAAsB;QAC3B,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI;IACpC;IACA,4BAA4B,KAAK,EAAE;QAC/B,MAAM,MAAM,IAAI,CAAC,cAAc,CAAC;QAChC,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAC;SAAI;IAClC;IACA,yBAAyB,KAAK,EAAE;QAC5B,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,IAAK,IAAI,IAAI,QAAQ,GAAG,IAAI,aAAa,MAAM,EAAE,KAAK,EAAG;YACrD,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,IAAI;QACtD;IACJ;IACA,sBAAsB,UAAU,EAAE;QAC9B,IAAI;QACJ,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC;QAChC,IAAI,uBAAuB,0LAAA,CAAA,aAAU,EAAE;YACnC;QACJ;QACA,SAAS,CAAC,wBAAwB,IAAI,CAAC,oBAAoB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,IAAI,EAAE;YACjI,MAAM;YACN,UAAU;YACV,OAAO;QACX;IACJ;IACA,eAAe,WAAW,EAAE;QACxB,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;IACtE;IACA,WAAW,WAAW,EAAE;QACpB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,WAAW,eAAe;YAC1B,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO;QAC7B;QACA,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACxD,IAAI,CAAC,YAAY,YAAY;YACzB,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO;QAC7B;QACA,MAAM,MAAM,IAAI,CAAC,cAAc,CAAC;QAChC,IAAI,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,MAAM;YACrC,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO;QAC7B;QACA,IAAI,aAAa,eAAe;YAC5B,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;gBAAC;aAAI;QAC7C;QACA,MAAM,EACF,kBAAkB,gBAAgB,EACrC,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC;eAAI,oBAAoB,EAAE;YAAE;SAAI,EAAE;YAAC;SAAI;IAC/E;IACA,aAAa,WAAW,EAAE;QACtB,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACxD,IAAI,CAAC,YAAY,YAAY;YACzB;QACJ;QACA,MAAM,mBAAmB,IAAI,CAAC,UAAU,CAAC,mBAAmB;QAC5D,IAAI,IAAI,CAAC,MAAM,CAAC,wBAAwB,iBAAiB,MAAM,IAAI,GAAG;YAClE;QACJ;QACA,MAAM,MAAM,IAAI,CAAC,cAAc,CAAC;QAChC,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,cAAc,CAAC,KAAK;YACjC,cAAc;QAClB,IAAI;YACJ;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;YAAC;SAAI;IAClC;IACA,0BAA0B,KAAK,EAAE;QAC7B,IAAI,CAAC,2BAA2B,CAAC;QACjC,IAAI,CAAC,wBAAwB,CAAC;QAC9B,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAC;IACzC;IACA,yBAAyB,KAAK,EAAE,iBAAiB,EAAE;QAC/C,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC,aAAa,KAAK,eAAe;QAC7E,IAAI,CAAC,qBAAqB,CAAC;QAC3B,IAAI,CAAC,iBAAiB,CAAC,OAAO,iBAAiB,mBAAmB;YAC9D;gBACI,MAAM,MAAM;YAChB;YACA,mBAAmB;gBAAC;gBAAY;aAAW;QAC/C;QACA,IAAI,CAAC,mBAAmB;IAC5B;IACA,WAAW,WAAW,EAAE;QACpB,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC;QAChD,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,kBAAkB,CAAC;QACpD,MAAM,wBAAwB,IAAI,CAAC,sBAAsB;QACzD,IAAI,YAAY,QAAQ;YACpB,IAAI,CAAC,oBAAoB,CAAC,OAAO,IAAI,CAAE;gBACnC,MAAM,QAAQ,CAAC;gBACf,MAAM,oBAAoB,IAAI,CAAC,iBAAiB,CAAC;gBACjD,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI,CAAE;oBAChC,IAAI,CAAC,yBAAyB,CAAC;oBAC/B,IAAI,CAAC,wBAAwB,CAAC,OAAO;oBACrC,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAE;wBAC7B,SAAS,WAAW,CAAC,IAAI;oBAC7B;gBACJ,GAAI,IAAI,CAAE;oBACN,MAAM,WAAW,CAAC;oBAClB,SAAS,UAAU,CAAC,IAAI;gBAC5B;YACJ,GAAI,IAAI,CAAE;gBACN,SAAS,UAAU,CAAC,IAAI;YAC5B;QACJ,OAAO;YACH,SAAS,UAAU,CAAC,IAAI;QAC5B;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,YAAY,WAAW,EAAE,aAAa,EAAE;QACpC,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,MAAM,WAAW,IAAI,CAAC,aAAa;QACnC,MAAM,cAAc,SAAS,cAAc,CAAC;QAC5C,MAAM,mBAAmB,SAAS,cAAc,CAAC;QACjD,MAAM,cAAc,SAAS,kBAAkB,CAAC;QAChD,MAAM,mBAAmB,SAAS,kBAAkB,CAAC;QACrD,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC,aAAa,KAAK,eAAe;QAC7E,MAAM,eAAe,YAAY,gBAAgB,YAAY,qBAAqB,gBAAgB;QAClG,IAAI,cAAc;YACd,SAAS,WAAW,CAAC,IAAI;QAC7B,OAAO;YACH,SAAS,UAAU,CAAC,IAAI;QAC5B;QACA,OAAO,SAAS,OAAO,GAAG,IAAI,CAAE;YAC5B,gBAAgB,CAAC,SAAS,iBAAiB,CAAC,aAAa,kBAAkB,CAAC;YAC5E,SAAS,sBAAsB,CAAC,aAAa;YAC7C,IAAI,CAAC,wBAAwB,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,UAAU,CAAC,mBAAmB,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB;YACzH,IAAI,YAAY,gBAAgB;gBAC5B,IAAI,CAAC,qBAAqB,CAAC;YAC/B;YACA,IAAI,CAAC,iBAAiB,CAAC,aAAa,mBAAmB;gBACnD,WAAW,SAAS,QAAQ,CAAC;gBAC7B,SAAS,SAAS,QAAQ,CAAC;YAC/B,GAAG;gBACC,mBAAmB;oBAAC;oBAAY;iBAAW;YAC/C;QACJ;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2266, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_collection_widget.async.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_collection_widget.async.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport Guid from \"../../../core/guid\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport CollectionWidgetEdit from \"../../ui/collection/m_collection_widget.edit\";\r\nclass CollectionWidgetAsync extends CollectionWidgetEdit {\r\n    _initMarkup() {\r\n        this._asyncTemplateItemsMap = {};\r\n        super._initMarkup()\r\n    }\r\n    _render() {\r\n        super._render();\r\n        this._planPostRenderActions()\r\n    }\r\n    _renderItemContent(args) {\r\n        const renderContentDeferred = Deferred();\r\n        const itemDeferred = Deferred();\r\n        const uniqueKey = `dx${new Guid}`;\r\n        this._asyncTemplateItemsMap[uniqueKey] = itemDeferred;\r\n        const $itemContent = super._renderItemContent(_extends({}, args, {\r\n            uniqueKey: uniqueKey\r\n        }));\r\n        itemDeferred.done((() => {\r\n            renderContentDeferred.resolve($itemContent)\r\n        }));\r\n        return renderContentDeferred.promise()\r\n    }\r\n    _onItemTemplateRendered(itemTemplate, renderArgs) {\r\n        return () => {\r\n            const {\r\n                uniqueKey: uniqueKey\r\n            } = renderArgs;\r\n            if (uniqueKey) {\r\n                var _this$_asyncTemplateI;\r\n                null === (_this$_asyncTemplateI = this._asyncTemplateItemsMap[uniqueKey]) || void 0 === _this$_asyncTemplateI || _this$_asyncTemplateI.resolve()\r\n            }\r\n        }\r\n    }\r\n    _postProcessRenderItems() {}\r\n    _planPostRenderActions() {\r\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n            args[_key] = arguments[_key]\r\n        }\r\n        const d = Deferred();\r\n        const asyncTemplateItems = Object.values(this._asyncTemplateItemsMap);\r\n        when.apply(this, asyncTemplateItems).done((() => {\r\n            this._postProcessRenderItems(...args);\r\n            d.resolve().done((() => {\r\n                this._asyncTemplateItemsMap = {}\r\n            }))\r\n        }));\r\n        return d.promise()\r\n    }\r\n    _clean() {\r\n        super._clean();\r\n        const asyncTemplateItems = Object.values(this._asyncTemplateItemsMap);\r\n        asyncTemplateItems.forEach((item => {\r\n            item.reject()\r\n        }));\r\n        this._asyncTemplateItemsMap = {}\r\n    }\r\n}\r\nexport default CollectionWidgetAsync;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AAAA;AAIA;;;;;AACA,MAAM,8BAA8B,qMAAA,CAAA,UAAoB;IACpD,cAAc;QACV,IAAI,CAAC,sBAAsB,GAAG,CAAC;QAC/B,KAAK,CAAC;IACV;IACA,UAAU;QACN,KAAK,CAAC;QACN,IAAI,CAAC,sBAAsB;IAC/B;IACA,mBAAmB,IAAI,EAAE;QACrB,MAAM,wBAAwB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACrC,MAAM,eAAe,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QAC5B,MAAM,YAAY,CAAC,EAAE,EAAE,IAAI,iJAAA,CAAA,UAAI,EAAE;QACjC,IAAI,CAAC,sBAAsB,CAAC,UAAU,GAAG;QACzC,MAAM,eAAe,KAAK,CAAC,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,MAAM;YAC7D,WAAW;QACf;QACA,aAAa,IAAI,CAAE;YACf,sBAAsB,OAAO,CAAC;QAClC;QACA,OAAO,sBAAsB,OAAO;IACxC;IACA,wBAAwB,YAAY,EAAE,UAAU,EAAE;QAC9C,OAAO;YACH,MAAM,EACF,WAAW,SAAS,EACvB,GAAG;YACJ,IAAI,WAAW;gBACX,IAAI;gBACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,sBAAsB,CAAC,UAAU,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,OAAO;YAClJ;QACJ;IACJ;IACA,0BAA0B,CAAC;IAC3B,yBAAyB;QACrB,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACrF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAChC;QACA,MAAM,IAAI,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACjB,MAAM,qBAAqB,OAAO,MAAM,CAAC,IAAI,CAAC,sBAAsB;QACpE,iLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,EAAE,oBAAoB,IAAI,CAAE;YACvC,IAAI,CAAC,uBAAuB,IAAI;YAChC,EAAE,OAAO,GAAG,IAAI,CAAE;gBACd,IAAI,CAAC,sBAAsB,GAAG,CAAC;YACnC;QACJ;QACA,OAAO,EAAE,OAAO;IACpB;IACA,SAAS;QACL,KAAK,CAAC;QACN,MAAM,qBAAqB,OAAO,MAAM,CAAC,IAAI,CAAC,sBAAsB;QACpE,mBAAmB,OAAO,CAAE,CAAA;YACxB,KAAK,MAAM;QACf;QACA,IAAI,CAAC,sBAAsB,GAAG,CAAC;IACnC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2345, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_search_box_mixin.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_search_box_mixin.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport messageLocalization from \"../../../common/core/localization/message\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    stubComponent\r\n} from \"../../../core/utils/stubs\";\r\nimport errors from \"../../../ui/widget/ui.errors\";\r\nlet EditorClass = stubComponent(\"TextBox\");\r\nexport default {\r\n    _getDefaultOptions() {\r\n        return extend(this.callBase(), {\r\n            searchMode: \"\",\r\n            searchExpr: null,\r\n            searchValue: \"\",\r\n            searchEnabled: false,\r\n            searchEditorOptions: {}\r\n        })\r\n    },\r\n    _initMarkup() {\r\n        this._renderSearch();\r\n        this.callBase()\r\n    },\r\n    _renderSearch() {\r\n        const $element = this.$element();\r\n        const searchEnabled = this.option(\"searchEnabled\");\r\n        const searchBoxClassName = this._addWidgetPrefix(\"search\");\r\n        const rootElementClassName = this._addWidgetPrefix(\"with-search\");\r\n        if (!searchEnabled) {\r\n            $element.removeClass(rootElementClassName);\r\n            this._removeSearchBox();\r\n            return\r\n        }\r\n        const editorOptions = this._getSearchEditorOptions();\r\n        if (this._searchEditor) {\r\n            this._searchEditor.option(editorOptions)\r\n        } else {\r\n            $element.addClass(rootElementClassName);\r\n            this._$searchEditorElement = $(\"<div>\").addClass(searchBoxClassName).prependTo($element);\r\n            this._searchEditor = this._createComponent(this._$searchEditorElement, EditorClass, editorOptions)\r\n        }\r\n    },\r\n    _removeSearchBox() {\r\n        this._$searchEditorElement && this._$searchEditorElement.remove();\r\n        delete this._$searchEditorElement;\r\n        delete this._searchEditor\r\n    },\r\n    _getSearchEditorOptions() {\r\n        const that = this;\r\n        const userEditorOptions = that.option(\"searchEditorOptions\");\r\n        const searchText = messageLocalization.format(\"Search\");\r\n        return extend({\r\n            mode: \"search\",\r\n            placeholder: searchText,\r\n            tabIndex: that.option(\"tabIndex\"),\r\n            value: that.option(\"searchValue\"),\r\n            valueChangeEvent: \"input\",\r\n            inputAttr: {\r\n                \"aria-label\": searchText\r\n            },\r\n            onValueChanged(e) {\r\n                const searchTimeout = that.option(\"searchTimeout\");\r\n                that._valueChangeDeferred = Deferred();\r\n                clearTimeout(that._valueChangeTimeout);\r\n                that._valueChangeDeferred.done(function() {\r\n                    this.option(\"searchValue\", e.value)\r\n                }.bind(that));\r\n                if (e.event && \"input\" === e.event.type && searchTimeout) {\r\n                    that._valueChangeTimeout = setTimeout((() => {\r\n                        that._valueChangeDeferred.resolve()\r\n                    }), searchTimeout)\r\n                } else {\r\n                    that._valueChangeDeferred.resolve()\r\n                }\r\n            }\r\n        }, userEditorOptions)\r\n    },\r\n    _getAriaTarget() {\r\n        if (this.option(\"searchEnabled\")) {\r\n            return this._itemContainer(true)\r\n        }\r\n        return this.callBase()\r\n    },\r\n    _focusTarget() {\r\n        if (this.option(\"searchEnabled\")) {\r\n            return this._itemContainer(true)\r\n        }\r\n        return this.callBase()\r\n    },\r\n    _updateFocusState(e, isFocused) {\r\n        if (this.option(\"searchEnabled\")) {\r\n            this._toggleFocusClass(isFocused, this.$element())\r\n        }\r\n        this.callBase(e, isFocused)\r\n    },\r\n    getOperationBySearchMode: searchMode => \"equals\" === searchMode ? \"=\" : searchMode,\r\n    _optionChanged(args) {\r\n        switch (args.name) {\r\n            case \"searchEnabled\":\r\n            case \"searchEditorOptions\":\r\n                this._invalidate();\r\n                break;\r\n            case \"searchExpr\":\r\n            case \"searchMode\":\r\n            case \"searchValue\":\r\n                if (!this._dataSource) {\r\n                    errors.log(\"W1009\");\r\n                    return\r\n                }\r\n                if (\"searchMode\" === args.name) {\r\n                    this._dataSource.searchOperation(this.getOperationBySearchMode(args.value))\r\n                } else {\r\n                    this._dataSource[args.name](args.value)\r\n                }\r\n                this._dataSource.load();\r\n                break;\r\n            case \"searchTimeout\":\r\n                break;\r\n            default:\r\n                this.callBase(args)\r\n        }\r\n    },\r\n    focus() {\r\n        if (!this.option(\"focusedElement\") && this.option(\"searchEnabled\")) {\r\n            this._searchEditor && this._searchEditor.focus();\r\n            return\r\n        }\r\n        this.callBase()\r\n    },\r\n    _cleanAria() {\r\n        const $element = this.$element();\r\n        this.setAria({\r\n            role: null,\r\n            activedescendant: null\r\n        }, $element);\r\n        $element.attr(\"tabIndex\", null)\r\n    },\r\n    _clean() {\r\n        this.callBase();\r\n        this._cleanAria()\r\n    },\r\n    _refresh() {\r\n        if (this._valueChangeDeferred) {\r\n            this._valueChangeDeferred.resolve()\r\n        }\r\n        this.callBase()\r\n    },\r\n    setEditorClass(value) {\r\n        EditorClass = value\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;;;;;;;AACA,IAAI,cAAc,CAAA,GAAA,8KAAA,CAAA,gBAAa,AAAD,EAAE;uCACjB;IACX;QACI,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI;YAC3B,YAAY;YACZ,YAAY;YACZ,aAAa;YACb,eAAe;YACf,qBAAqB,CAAC;QAC1B;IACJ;IACA;QACI,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;QAClC,MAAM,qBAAqB,IAAI,CAAC,gBAAgB,CAAC;QACjD,MAAM,uBAAuB,IAAI,CAAC,gBAAgB,CAAC;QACnD,IAAI,CAAC,eAAe;YAChB,SAAS,WAAW,CAAC;YACrB,IAAI,CAAC,gBAAgB;YACrB;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,uBAAuB;QAClD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC9B,OAAO;YACH,SAAS,QAAQ,CAAC;YAClB,IAAI,CAAC,qBAAqB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,oBAAoB,SAAS,CAAC;YAC/E,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,qBAAqB,EAAE,aAAa;QACxF;IACJ;IACA;QACI,IAAI,CAAC,qBAAqB,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM;QAC/D,OAAO,IAAI,CAAC,qBAAqB;QACjC,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA;QACI,MAAM,OAAO,IAAI;QACjB,MAAM,oBAAoB,KAAK,MAAM,CAAC;QACtC,MAAM,aAAa,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;QAC9C,OAAO,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YACV,MAAM;YACN,aAAa;YACb,UAAU,KAAK,MAAM,CAAC;YACtB,OAAO,KAAK,MAAM,CAAC;YACnB,kBAAkB;YAClB,WAAW;gBACP,cAAc;YAClB;YACA,gBAAe,CAAC;gBACZ,MAAM,gBAAgB,KAAK,MAAM,CAAC;gBAClC,KAAK,oBAAoB,GAAG,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;gBACnC,aAAa,KAAK,mBAAmB;gBACrC,KAAK,oBAAoB,CAAC,IAAI,CAAC,CAAA;oBAC3B,IAAI,CAAC,MAAM,CAAC,eAAe,EAAE,KAAK;gBACtC,CAAA,EAAE,IAAI,CAAC;gBACP,IAAI,EAAE,KAAK,IAAI,YAAY,EAAE,KAAK,CAAC,IAAI,IAAI,eAAe;oBACtD,KAAK,mBAAmB,GAAG,WAAY;wBACnC,KAAK,oBAAoB,CAAC,OAAO;oBACrC,GAAI;gBACR,OAAO;oBACH,KAAK,oBAAoB,CAAC,OAAO;gBACrC;YACJ;QACJ,GAAG;IACP;IACA;QACI,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B;QACA,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA;QACI,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,OAAO,IAAI,CAAC,cAAc,CAAC;QAC/B;QACA,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,mBAAkB,CAAC,EAAE,SAAS;QAC1B,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,IAAI,CAAC,iBAAiB,CAAC,WAAW,IAAI,CAAC,QAAQ;QACnD;QACA,IAAI,CAAC,QAAQ,CAAC,GAAG;IACrB;IACA,0BAA0B,CAAA,aAAc,aAAa,aAAa,MAAM;IACxE,gBAAe,IAAI;QACf,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACnB,iKAAA,CAAA,UAAM,CAAC,GAAG,CAAC;oBACX;gBACJ;gBACA,IAAI,iBAAiB,KAAK,IAAI,EAAE;oBAC5B,IAAI,CAAC,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,wBAAwB,CAAC,KAAK,KAAK;gBAC7E,OAAO;oBACH,IAAI,CAAC,WAAW,CAAC,KAAK,IAAI,CAAC,CAAC,KAAK,KAAK;gBAC1C;gBACA,IAAI,CAAC,WAAW,CAAC,IAAI;gBACrB;YACJ,KAAK;gBACD;YACJ;gBACI,IAAI,CAAC,QAAQ,CAAC;QACtB;IACJ;IACA;QACI,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,qBAAqB,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAChE,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK;YAC9C;QACJ;QACA,IAAI,CAAC,QAAQ;IACjB;IACA;QACI,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,IAAI,CAAC,OAAO,CAAC;YACT,MAAM;YACN,kBAAkB;QACtB,GAAG;QACH,SAAS,IAAI,CAAC,YAAY;IAC9B;IACA;QACI,IAAI,CAAC,QAAQ;QACb,IAAI,CAAC,UAAU;IACnB;IACA;QACI,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B,IAAI,CAAC,oBAAoB,CAAC,OAAO;QACrC;QACA,IAAI,CAAC,QAAQ;IACjB;IACA,gBAAe,KAAK;QAChB,cAAc;IAClB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2517, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/collection/m_collection_widget.live_update.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/collection/m_collection_widget.live_update.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    indexByKey,\r\n    insert,\r\n    update\r\n} from \"../../../common/data/array_utils\";\r\nimport {\r\n    keysEqual\r\n} from \"../../../common/data/utils\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    findChanges\r\n} from \"../../../core/utils/array_compare\";\r\nimport {\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport CollectionWidgetAsync from \"../../ui/collection/m_collection_widget.async\";\r\nconst PRIVATE_KEY_FIELD = \"__dx_key__\";\r\nclass CollectionWidgetLiveUpdate extends CollectionWidgetAsync {\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            repaintChangesOnly: false\r\n        })\r\n    }\r\n    _customizeStoreLoadOptions(e) {\r\n        const dataController = this._dataController;\r\n        if (dataController.getDataSource() && !this._dataController.isLoaded()) {\r\n            this._correctionIndex = 0\r\n        }\r\n        if (this._correctionIndex && e.storeLoadOptions) {\r\n            e.storeLoadOptions.skip += this._correctionIndex\r\n        }\r\n    }\r\n    reload() {\r\n        this._correctionIndex = 0\r\n    }\r\n    _init() {\r\n        super._init();\r\n        this._refreshItemsCache();\r\n        this._correctionIndex = 0;\r\n        this._subscribeLoadOptionsCustomization(true)\r\n    }\r\n    _findItemElementByKey(key) {\r\n        let result = $();\r\n        const keyExpr = this.key();\r\n        this.itemElements().each(((_, item) => {\r\n            const $item = $(item);\r\n            const itemData = this._getItemData($item);\r\n            if (keyExpr ? keysEqual(keyExpr, this.keyOf(itemData), key) : this._isItemEquals(itemData, key)) {\r\n                result = $item;\r\n                return false\r\n            }\r\n        }));\r\n        return result\r\n    }\r\n    _dataSourceChangedHandler(newItems, e) {\r\n        if (null !== e && void 0 !== e && e.changes) {\r\n            this._modifyByChanges(e.changes)\r\n        } else {\r\n            super._dataSourceChangedHandler(newItems, e);\r\n            this._refreshItemsCache()\r\n        }\r\n    }\r\n    _isItemEquals(item1, item2) {\r\n        if (item1 && item1.__dx_key__) {\r\n            item1 = item1.data\r\n        }\r\n        try {\r\n            return JSON.stringify(item1) === JSON.stringify(item2)\r\n        } catch (e) {\r\n            return item1 === item2\r\n        }\r\n    }\r\n    _isItemStrictEquals(item1, item2) {\r\n        return this._isItemEquals(item1, item2)\r\n    }\r\n    _shouldAddNewGroup(changes, items) {\r\n        let result = false;\r\n        if (this.option(\"grouped\")) {\r\n            if (!changes.length) {\r\n                result = true\r\n            }\r\n            each(changes, ((i, change) => {\r\n                if (\"insert\" === change.type) {\r\n                    result = true;\r\n                    each(items, ((_, item) => {\r\n                        if (void 0 !== change.data.key && change.data.key === item.key) {\r\n                            result = false;\r\n                            return false\r\n                        }\r\n                    }))\r\n                }\r\n            }))\r\n        }\r\n        return result\r\n    }\r\n    _partialRefresh() {\r\n        if (this.option(\"repaintChangesOnly\")) {\r\n            const keyOf = data => {\r\n                if (data && void 0 !== data.__dx_key__) {\r\n                    return data.__dx_key__\r\n                }\r\n                return this.keyOf(data)\r\n            };\r\n            const result = findChanges(this._itemsCache, this._editStrategy.itemsGetter(), keyOf, this._isItemStrictEquals.bind(this));\r\n            if (result && this._itemsCache.length && !this._shouldAddNewGroup(result, this._itemsCache)) {\r\n                this._modifyByChanges(result, true);\r\n                this._renderEmptyMessage();\r\n                return true\r\n            }\r\n            this._refreshItemsCache()\r\n        }\r\n        return false\r\n    }\r\n    _refreshItemsCache() {\r\n        if (this.option(\"repaintChangesOnly\")) {\r\n            const items = this._editStrategy.itemsGetter();\r\n            try {\r\n                this._itemsCache = extend(true, [], items);\r\n                if (!this.key()) {\r\n                    this._itemsCache = this._itemsCache.map(((itemCache, index) => ({\r\n                        [PRIVATE_KEY_FIELD]: items[index],\r\n                        data: itemCache\r\n                    })))\r\n                }\r\n            } catch (e) {\r\n                this._itemsCache = extend([], items)\r\n            }\r\n        }\r\n    }\r\n    _dispose() {\r\n        this._subscribeLoadOptionsCustomization(false);\r\n        super._dispose()\r\n    }\r\n    _updateByChange(keyInfo, items, change, isPartialRefresh) {\r\n        if (isPartialRefresh) {\r\n            this._renderItem(change.index, change.data, null, this._findItemElementByKey(change.key))\r\n        } else {\r\n            const changedItem = items[indexByKey(keyInfo, items, change.key)];\r\n            if (changedItem) {\r\n                update(keyInfo, items, change.key, change.data).done((() => {\r\n                    this._renderItem(items.indexOf(changedItem), changedItem, null, this._findItemElementByKey(change.key))\r\n                }))\r\n            }\r\n        }\r\n    }\r\n    _insertByChange(keyInfo, items, change, isPartialRefresh) {\r\n        when(isPartialRefresh || insert(keyInfo, items, change.data, change.index)).done((() => {\r\n            this._beforeItemElementInserted(change);\r\n            this._renderItem(change.index ?? items.length, change.data);\r\n            this._afterItemElementInserted();\r\n            this._correctionIndex++\r\n        }))\r\n    }\r\n    _updateSelectionAfterRemoveByChange(removeIndex) {\r\n        const {\r\n            selectedIndex: selectedIndex,\r\n            selectedItems: selectedItems\r\n        } = this.option();\r\n        if (selectedIndex > removeIndex) {\r\n            this.option(\"selectedIndex\", selectedIndex - 1)\r\n        } else if (selectedIndex === removeIndex && 1 === selectedItems.length) {\r\n            this.option(\"selectedItems\", [])\r\n        } else {\r\n            this._normalizeSelectedItems()\r\n        }\r\n    }\r\n    _beforeItemElementInserted(change) {\r\n        const {\r\n            selectedIndex: selectedIndex\r\n        } = this.option();\r\n        if (change.index <= selectedIndex) {\r\n            this.option(\"selectedIndex\", selectedIndex + 1)\r\n        }\r\n    }\r\n    _afterItemElementInserted() {\r\n        this._renderEmptyMessage()\r\n    }\r\n    _removeByChange(keyInfo, items, change, isPartialRefresh) {\r\n        const index = isPartialRefresh ? change.index : indexByKey(keyInfo, items, change.key);\r\n        const removedItem = isPartialRefresh ? change.oldItem : items[index];\r\n        if (removedItem) {\r\n            const $removedItemElement = this._findItemElementByKey(change.key);\r\n            const deletedActionArgs = this._extendActionArgs($removedItemElement);\r\n            this._waitDeletingPrepare($removedItemElement).done((() => {\r\n                if (isPartialRefresh) {\r\n                    this._updateIndicesAfterIndex(index - 1);\r\n                    this._afterItemElementDeleted($removedItemElement, deletedActionArgs);\r\n                    this._updateSelectionAfterRemoveByChange(index)\r\n                } else {\r\n                    this._deleteItemElementByIndex(index);\r\n                    this._afterItemElementDeleted($removedItemElement, deletedActionArgs)\r\n                }\r\n            }));\r\n            this._correctionIndex--\r\n        }\r\n    }\r\n    _modifyByChanges(changes, isPartialRefresh) {\r\n        const items = this._editStrategy.itemsGetter();\r\n        const keyInfo = {\r\n            key: this.key.bind(this),\r\n            keyOf: this.keyOf.bind(this)\r\n        };\r\n        const dataController = this._dataController;\r\n        const paginate = dataController.paginate();\r\n        const group = dataController.group();\r\n        if (paginate || group) {\r\n            changes = changes.filter((item => \"insert\" !== item.type || void 0 !== item.index))\r\n        }\r\n        changes.forEach((change => this[`_${change.type}ByChange`](keyInfo, items, change, isPartialRefresh)));\r\n        this._renderedItemsCount = items.length;\r\n        this._refreshItemsCache();\r\n        this._fireContentReadyAction()\r\n    }\r\n    _appendItemToContainer($container, $itemFrame, index) {\r\n        const nextSiblingElement = $container.children(this._itemSelector()).get(index);\r\n        domAdapter.insertElement($container.get(0), $itemFrame.get(0), nextSiblingElement)\r\n    }\r\n    _subscribeLoadOptionsCustomization(enable) {\r\n        if (!this._dataController) {\r\n            return\r\n        }\r\n        if (enable) {\r\n            this._correctionIndex = 0;\r\n            this._dataController.on(\"customizeStoreLoadOptions\", this._customizeStoreLoadOptions.bind(this))\r\n        } else {\r\n            this._dataController.off(\"customizeStoreLoadOptions\", this._customizeStoreLoadOptions.bind(this))\r\n        }\r\n    }\r\n    _optionChanged(args) {\r\n        switch (args.name) {\r\n            case \"items\": {\r\n                const isItemsUpdated = this._partialRefresh(args.value);\r\n                if (!isItemsUpdated) {\r\n                    super._optionChanged(args)\r\n                }\r\n                break\r\n            }\r\n            case \"dataSource\":\r\n                if (!this.option(\"repaintChangesOnly\") || !args.value) {\r\n                    this.option(\"items\", [])\r\n                }\r\n                this._subscribeLoadOptionsCustomization(false);\r\n                super._optionChanged(args);\r\n                this._subscribeLoadOptionsCustomization(true);\r\n                break;\r\n            case \"repaintChangesOnly\":\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n}\r\nexport default CollectionWidgetLiveUpdate;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAKA;AAAA;AAGA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;;;;;;;;;;;AACA,MAAM,oBAAoB;AAC1B,MAAM,mCAAmC,sMAAA,CAAA,UAAqB;IAC1D,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,oBAAoB;QACxB;IACJ;IACA,2BAA2B,CAAC,EAAE;QAC1B,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,IAAI,eAAe,aAAa,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,IAAI;YACpE,IAAI,CAAC,gBAAgB,GAAG;QAC5B;QACA,IAAI,IAAI,CAAC,gBAAgB,IAAI,EAAE,gBAAgB,EAAE;YAC7C,EAAE,gBAAgB,CAAC,IAAI,IAAI,IAAI,CAAC,gBAAgB;QACpD;IACJ;IACA,SAAS;QACL,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA,QAAQ;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,kCAAkC,CAAC;IAC5C;IACA,sBAAsB,GAAG,EAAE;QACvB,IAAI,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD;QACb,MAAM,UAAU,IAAI,CAAC,GAAG;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAE,CAAC,GAAG;YAC1B,MAAM,QAAQ,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;YAChB,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;YACnC,IAAI,UAAU,CAAA,GAAA,qKAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,OAAO,IAAI,CAAC,aAAa,CAAC,UAAU,MAAM;gBAC7F,SAAS;gBACT,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,0BAA0B,QAAQ,EAAE,CAAC,EAAE;QACnC,IAAI,SAAS,KAAK,KAAK,MAAM,KAAK,EAAE,OAAO,EAAE;YACzC,IAAI,CAAC,gBAAgB,CAAC,EAAE,OAAO;QACnC,OAAO;YACH,KAAK,CAAC,0BAA0B,UAAU;YAC1C,IAAI,CAAC,kBAAkB;QAC3B;IACJ;IACA,cAAc,KAAK,EAAE,KAAK,EAAE;QACxB,IAAI,SAAS,MAAM,UAAU,EAAE;YAC3B,QAAQ,MAAM,IAAI;QACtB;QACA,IAAI;YACA,OAAO,KAAK,SAAS,CAAC,WAAW,KAAK,SAAS,CAAC;QACpD,EAAE,OAAO,GAAG;YACR,OAAO,UAAU;QACrB;IACJ;IACA,oBAAoB,KAAK,EAAE,KAAK,EAAE;QAC9B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO;IACrC;IACA,mBAAmB,OAAO,EAAE,KAAK,EAAE;QAC/B,IAAI,SAAS;QACb,IAAI,IAAI,CAAC,MAAM,CAAC,YAAY;YACxB,IAAI,CAAC,QAAQ,MAAM,EAAE;gBACjB,SAAS;YACb;YACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,CAAC,GAAG;gBACf,IAAI,aAAa,OAAO,IAAI,EAAE;oBAC1B,SAAS;oBACT,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,GAAG;wBACb,IAAI,KAAK,MAAM,OAAO,IAAI,CAAC,GAAG,IAAI,OAAO,IAAI,CAAC,GAAG,KAAK,KAAK,GAAG,EAAE;4BAC5D,SAAS;4BACT,OAAO;wBACX;oBACJ;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB;YACnC,MAAM,QAAQ,CAAA;gBACV,IAAI,QAAQ,KAAK,MAAM,KAAK,UAAU,EAAE;oBACpC,OAAO,KAAK,UAAU;gBAC1B;gBACA,OAAO,IAAI,CAAC,KAAK,CAAC;YACtB;YACA,MAAM,SAAS,CAAA,GAAA,sLAAA,CAAA,cAAW,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,OAAO,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;YACxH,IAAI,UAAU,IAAI,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,QAAQ,IAAI,CAAC,WAAW,GAAG;gBACzF,IAAI,CAAC,gBAAgB,CAAC,QAAQ;gBAC9B,IAAI,CAAC,mBAAmB;gBACxB,OAAO;YACX;YACA,IAAI,CAAC,kBAAkB;QAC3B;QACA,OAAO;IACX;IACA,qBAAqB;QACjB,IAAI,IAAI,CAAC,MAAM,CAAC,uBAAuB;YACnC,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,WAAW;YAC5C,IAAI;gBACA,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,EAAE,EAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI;oBACb,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAE,CAAC,WAAW,QAAU,CAAC;4BAC5D,CAAC,kBAAkB,EAAE,KAAK,CAAC,MAAM;4BACjC,MAAM;wBACV,CAAC;gBACL;YACJ,EAAE,OAAO,GAAG;gBACR,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,EAAE,EAAE;YAClC;QACJ;IACJ;IACA,WAAW;QACP,IAAI,CAAC,kCAAkC,CAAC;QACxC,KAAK,CAAC;IACV;IACA,gBAAgB,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE;QACtD,IAAI,kBAAkB;YAClB,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,EAAE,OAAO,IAAI,EAAE,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG;QAC3F,OAAO;YACH,MAAM,cAAc,KAAK,CAAC,CAAA,GAAA,2KAAA,CAAA,aAAU,AAAD,EAAE,SAAS,OAAO,OAAO,GAAG,EAAE;YACjE,IAAI,aAAa;gBACb,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE,SAAS,OAAO,OAAO,GAAG,EAAE,OAAO,IAAI,EAAE,IAAI,CAAE;oBAClD,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,cAAc,aAAa,MAAM,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG;gBACzG;YACJ;QACJ;IACJ;IACA,gBAAgB,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE;QACtD,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,oBAAoB,CAAA,GAAA,2KAAA,CAAA,SAAM,AAAD,EAAE,SAAS,OAAO,OAAO,IAAI,EAAE,OAAO,KAAK,GAAG,IAAI,CAAE;YAC9E,IAAI,CAAC,0BAA0B,CAAC;YAChC,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,IAAI,MAAM,MAAM,EAAE,OAAO,IAAI;YAC1D,IAAI,CAAC,yBAAyB;YAC9B,IAAI,CAAC,gBAAgB;QACzB;IACJ;IACA,oCAAoC,WAAW,EAAE;QAC7C,MAAM,EACF,eAAe,aAAa,EAC5B,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,gBAAgB,aAAa;YAC7B,IAAI,CAAC,MAAM,CAAC,iBAAiB,gBAAgB;QACjD,OAAO,IAAI,kBAAkB,eAAe,MAAM,cAAc,MAAM,EAAE;YACpE,IAAI,CAAC,MAAM,CAAC,iBAAiB,EAAE;QACnC,OAAO;YACH,IAAI,CAAC,uBAAuB;QAChC;IACJ;IACA,2BAA2B,MAAM,EAAE;QAC/B,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,OAAO,KAAK,IAAI,eAAe;YAC/B,IAAI,CAAC,MAAM,CAAC,iBAAiB,gBAAgB;QACjD;IACJ;IACA,4BAA4B;QACxB,IAAI,CAAC,mBAAmB;IAC5B;IACA,gBAAgB,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,gBAAgB,EAAE;QACtD,MAAM,QAAQ,mBAAmB,OAAO,KAAK,GAAG,CAAA,GAAA,2KAAA,CAAA,aAAU,AAAD,EAAE,SAAS,OAAO,OAAO,GAAG;QACrF,MAAM,cAAc,mBAAmB,OAAO,OAAO,GAAG,KAAK,CAAC,MAAM;QACpE,IAAI,aAAa;YACb,MAAM,sBAAsB,IAAI,CAAC,qBAAqB,CAAC,OAAO,GAAG;YACjE,MAAM,oBAAoB,IAAI,CAAC,iBAAiB,CAAC;YACjD,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,IAAI,CAAE;gBACjD,IAAI,kBAAkB;oBAClB,IAAI,CAAC,wBAAwB,CAAC,QAAQ;oBACtC,IAAI,CAAC,wBAAwB,CAAC,qBAAqB;oBACnD,IAAI,CAAC,mCAAmC,CAAC;gBAC7C,OAAO;oBACH,IAAI,CAAC,yBAAyB,CAAC;oBAC/B,IAAI,CAAC,wBAAwB,CAAC,qBAAqB;gBACvD;YACJ;YACA,IAAI,CAAC,gBAAgB;QACzB;IACJ;IACA,iBAAiB,OAAO,EAAE,gBAAgB,EAAE;QACxC,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,WAAW;QAC5C,MAAM,UAAU;YACZ,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI;YACvB,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI;QAC/B;QACA,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,WAAW,eAAe,QAAQ;QACxC,MAAM,QAAQ,eAAe,KAAK;QAClC,IAAI,YAAY,OAAO;YACnB,UAAU,QAAQ,MAAM,CAAE,CAAA,OAAQ,aAAa,KAAK,IAAI,IAAI,KAAK,MAAM,KAAK,KAAK;QACrF;QACA,QAAQ,OAAO,CAAE,CAAA,SAAU,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,OAAO,QAAQ;QACnF,IAAI,CAAC,mBAAmB,GAAG,MAAM,MAAM;QACvC,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,uBAAuB;IAChC;IACA,uBAAuB,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;QAClD,MAAM,qBAAqB,WAAW,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI,GAAG,CAAC;QACzE,wJAAA,CAAA,UAAU,CAAC,aAAa,CAAC,WAAW,GAAG,CAAC,IAAI,WAAW,GAAG,CAAC,IAAI;IACnE;IACA,mCAAmC,MAAM,EAAE;QACvC,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;YACvB;QACJ;QACA,IAAI,QAAQ;YACR,IAAI,CAAC,gBAAgB,GAAG;YACxB,IAAI,CAAC,eAAe,CAAC,EAAE,CAAC,6BAA6B,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI;QAClG,OAAO;YACH,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,6BAA6B,IAAI,CAAC,0BAA0B,CAAC,IAAI,CAAC,IAAI;QACnG;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,OAAQ,KAAK,IAAI;YACb,KAAK;gBAAS;oBACV,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC,KAAK,KAAK;oBACtD,IAAI,CAAC,gBAAgB;wBACjB,KAAK,CAAC,eAAe;oBACzB;oBACA;gBACJ;YACA,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,KAAK,KAAK,EAAE;oBACnD,IAAI,CAAC,MAAM,CAAC,SAAS,EAAE;gBAC3B;gBACA,IAAI,CAAC,kCAAkC,CAAC;gBACxC,KAAK,CAAC,eAAe;gBACrB,IAAI,CAAC,kCAAkC,CAAC;gBACxC;YACJ,KAAK;gBACD;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}]}