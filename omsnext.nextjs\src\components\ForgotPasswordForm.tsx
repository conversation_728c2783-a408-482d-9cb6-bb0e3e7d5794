"use client";
import { useState, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import Form, {
  Item,
  Label,
  ButtonItem,
  RequiredRule,
  EmailRule,
} from "devextreme-react/form";
import LoadIndicator from "devextreme-react/load-indicator";
import styles from "./LoginForm.module.css"; // Reuse the same styles as LoginForm

// CardAuth komponens (reusing the same component from LoginForm)
const CardAuth = ({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) => {
  return (
    <div className={styles.authCard}>
      <div className={`dx-card ${styles.cardContent}`}>
        <div className={styles.header}>
          <div className={styles.title}>{title}</div>
          <div className={styles.description}>{description}</div>
        </div>
        {children}
      </div>
    </div>
  );
};

export default function ForgotPasswordForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const formData = useRef({ email: "" });

  const onSubmit = useCallback(async (e: any) => {
    e.preventDefault();
    const { email } = formData.current;
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // In a real app, this would call your API endpoint
      // For now, we'll simulate the request
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Mock success response
      setSuccess(
        "Jelszó visszaállító e-mail elküldve! Kérjük ellenőrizze a postaládáját."
      );
    } catch (err: any) {
      setError(err.message || "Hiba történt az e-mail küldése során.");
    } finally {
      setLoading(false);
    }
  }, []);

  const emailEditorOptions = {
    stylingMode: "filled" as const,
    mode: "email" as const,
    placeholder: "pl. <EMAIL>",
    onValueChanged: (e: any) => {
      formData.current.email = e.value;
    },
  };

  const submitButtonOptions = {
    width: "100%",
    type: "default" as const,
    useSubmitBehavior: true,
    text: loading ? undefined : "Jelszó visszaállítás",
    template: loading
      ? () => <LoadIndicator width={24} height={24} />
      : undefined,
    disabled: loading,
  };

  const handleBackToLogin = () => {
    router.push("/login");
  };

  return (
    <CardAuth
      title="Jelszó visszaállítás"
      description="Adja meg e-mail címét, és küldünk egy jelszó visszaállító linket."
    >
      <form onSubmit={onSubmit} className={styles.loginForm}>
        <Form formData={formData.current}>
          <Item
            dataField="email"
            editorType="dxTextBox"
            editorOptions={emailEditorOptions}
          >
            <RequiredRule message="E-mail cím megadása kötelező" />
            <EmailRule message="Érvényes e-mail címet adjon meg" />
            <Label text="E-mail" />
          </Item>

          {error && (
            <Item>
              <div className={styles.errorMessage}>{error}</div>
            </Item>
          )}

          {success && (
            <Item>
              <div className={styles.successMessage}>{success}</div>
            </Item>
          )}

          <ButtonItem
            buttonOptions={submitButtonOptions}
            cssClass="login-button"
          />
        </Form>

        <div
          className={styles.formText}
          style={{ marginTop: "20px", textAlign: "center" }}
        >
          <a
            onClick={handleBackToLogin}
            style={{ cursor: "pointer", color: "var(--accent-color)" }}
          >
            Vissza a bejelentkezéshez
          </a>
        </div>
      </form>
    </CardAuth>
  );
}
