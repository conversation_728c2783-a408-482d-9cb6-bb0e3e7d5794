﻿using omsnext.shared.Models;
using DevExpress.Xpo;
using BCrypt.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;

namespace omsnext.api.Data
{
    public class SeedData
    {
        public static async Task EnsureSeededAsync(UnitOfWork uow, IConfiguration config, ILogger logger)
        {
            try
            {
                string adminEmail = config["Seed:AdminEmail"] ?? "<EMAIL>";
                string adminPassword = config["Seed:AdminPassword"] ?? "OmsNext123!";
                string adminDisplayName = config["Seed:AdminDisplayName"] ?? "Rendszergazda";

                if (!uow.Query<User>().Any(u => u.Email == adminEmail))
                {
                    var admin = new User(uow)
                    {
                        Email = adminEmail,
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword(adminPassword),
                        DisplayName = adminDisplayName,
                        IsActive = true,
                    };
                    admin.Save();
                    await uow.CommitChangesAsync();
                    logger.LogInformation("Admin user seeded: {Email}", adminEmail);
                }
                else
                {
                    logger.LogInformation("Admin user already exists: {Email}", adminEmail);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while seeding admin user.");
                throw;
            }
        }
    }
}
