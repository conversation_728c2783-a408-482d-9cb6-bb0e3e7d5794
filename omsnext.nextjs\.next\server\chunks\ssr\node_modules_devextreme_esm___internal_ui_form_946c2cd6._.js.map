{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/components/m_button_item.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/components/m_button_item.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport {\r\n    isDefined\r\n} from \"../../../../core/utils/type\";\r\nconst FIELD_BUTTON_ITEM_CLASS = \"dx-field-button-item\";\r\nexport function renderButtonItem(_ref) {\r\n    let {\r\n        item: item,\r\n        $parent: $parent,\r\n        rootElementCssClassList: rootElementCssClassList,\r\n        validationGroup: validationGroup,\r\n        createComponentCallback: createComponentCallback\r\n    } = _ref;\r\n    const $rootElement = $(\"<div>\").appendTo($parent).addClass(rootElementCssClassList.join(\" \")).addClass(\"dx-field-button-item\").css(\"textAlign\", convertAlignmentToTextAlign(item.horizontalAlignment));\r\n    $parent.css(\"justifyContent\", convertAlignmentToJustifyContent(item.verticalAlignment));\r\n    const $button = $(\"<div>\").appendTo($rootElement);\r\n    return {\r\n        $rootElement: $rootElement,\r\n        buttonInstance: createComponentCallback($button, \"dxButton\", extend({\r\n            validationGroup: validationGroup\r\n        }, item.buttonOptions))\r\n    }\r\n}\r\n\r\nfunction convertAlignmentToTextAlign(horizontalAlignment) {\r\n    return isDefined(horizontalAlignment) ? horizontalAlignment : \"right\"\r\n}\r\n\r\nfunction convertAlignmentToJustifyContent(verticalAlignment) {\r\n    switch (verticalAlignment) {\r\n        case \"center\":\r\n            return \"center\";\r\n        case \"bottom\":\r\n            return \"flex-end\";\r\n        default:\r\n            return \"flex-start\"\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;;;;AAGA,MAAM,0BAA0B;AACzB,SAAS,iBAAiB,IAAI;IACjC,IAAI,EACA,MAAM,IAAI,EACV,SAAS,OAAO,EAChB,yBAAyB,uBAAuB,EAChD,iBAAiB,eAAe,EAChC,yBAAyB,uBAAuB,EACnD,GAAG;IACJ,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,SAAS,QAAQ,CAAC,wBAAwB,IAAI,CAAC,MAAM,QAAQ,CAAC,wBAAwB,GAAG,CAAC,aAAa,4BAA4B,KAAK,mBAAmB;IACpM,QAAQ,GAAG,CAAC,kBAAkB,iCAAiC,KAAK,iBAAiB;IACrF,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;IACpC,OAAO;QACH,cAAc;QACd,gBAAgB,wBAAwB,SAAS,YAAY,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;YAChE,iBAAiB;QACrB,GAAG,KAAK,aAAa;IACzB;AACJ;AAEA,SAAS,4BAA4B,mBAAmB;IACpD,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,uBAAuB,sBAAsB;AAClE;AAEA,SAAS,iCAAiC,iBAAiB;IACvD,OAAQ;QACJ,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 53, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/components/m_empty_item.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/components/m_empty_item.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../../core/renderer\";\r\nexport const FIELD_EMPTY_ITEM_CLASS = \"dx-field-empty-item\";\r\nexport function renderEmptyItem(_ref) {\r\n    let {\r\n        $parent: $parent,\r\n        rootElementCssClassList: rootElementCssClassList\r\n    } = _ref;\r\n    return $(\"<div>\").addClass(\"dx-field-empty-item\").html(\"&nbsp;\").addClass(rootElementCssClassList.join(\" \")).appendTo($parent)\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;;AACO,MAAM,yBAAyB;AAC/B,SAAS,gBAAgB,IAAI;IAChC,IAAI,EACA,SAAS,OAAO,EAChB,yBAAyB,uBAAuB,EACnD,GAAG;IACJ,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,uBAAuB,IAAI,CAAC,UAAU,QAAQ,CAAC,wBAAwB,IAAI,CAAC,MAAM,QAAQ,CAAC;AAC1H", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/constants.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/constants.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport const WIDGET_CLASS = \"dx-widget\";\r\nexport const FORM_CLASS = \"dx-form\";\r\nexport const FORM_GROUP_CLASS = \"dx-form-group\";\r\nexport const FORM_GROUP_CAPTION_CLASS = \"dx-form-group-caption\";\r\nexport const FORM_GROUP_CUSTOM_CAPTION_CLASS = \"dx-form-group-custom-caption\";\r\nexport const FORM_FIELD_ITEM_COL_CLASS = \"dx-col-\";\r\nexport const FIELD_ITEM_CLASS = \"dx-field-item\";\r\nexport const LAYOUT_MANAGER_ONE_COLUMN = \"dx-layout-manager-one-col\";\r\nexport const FIELD_ITEM_LABEL_CONTENT_CLASS = \"dx-field-item-label-content\";\r\nexport const FORM_LAYOUT_MANAGER_CLASS = \"dx-layout-manager\";\r\nexport const FIELD_ITEM_LABEL_CLASS = \"dx-field-item-label\";\r\nexport const FIELD_ITEM_CONTENT_CLASS = \"dx-field-item-content\";\r\nexport const SINGLE_COLUMN_ITEM_CONTENT = \"dx-single-column-item-content\";\r\nexport const ROOT_SIMPLE_ITEM_CLASS = \"dx-root-simple-item\";\r\nexport const FORM_GROUP_CONTENT_CLASS = \"dx-form-group-content\";\r\nexport const FIELD_ITEM_CONTENT_HAS_GROUP_CLASS = \"dx-field-item-has-group\";\r\nexport const FIELD_ITEM_CONTENT_HAS_TABS_CLASS = \"dx-field-item-has-tabs\";\r\nexport const FORM_GROUP_WITH_CAPTION_CLASS = \"dx-form-group-with-caption\";\r\nexport const FIELD_ITEM_TAB_CLASS = \"dx-field-item-tab\";\r\nexport const GROUP_COL_COUNT_CLASS = \"dx-group-colcount-\";\r\nexport const GROUP_COL_COUNT_ATTR = \"group-col-count\";\r\nexport const FORM_VALIDATION_SUMMARY = \"dx-form-validation-summary\";\r\nexport const FORM_UNDERLINED_CLASS = \"dx-form-styling-mode-underlined\";\r\nexport const SIMPLE_ITEM_TYPE = \"simple\";\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,eAAe;AACrB,MAAM,aAAa;AACnB,MAAM,mBAAmB;AACzB,MAAM,2BAA2B;AACjC,MAAM,kCAAkC;AACxC,MAAM,4BAA4B;AAClC,MAAM,mBAAmB;AACzB,MAAM,4BAA4B;AAClC,MAAM,iCAAiC;AACvC,MAAM,4BAA4B;AAClC,MAAM,yBAAyB;AAC/B,MAAM,2BAA2B;AACjC,MAAM,6BAA6B;AACnC,MAAM,yBAAyB;AAC/B,MAAM,2BAA2B;AACjC,MAAM,qCAAqC;AAC3C,MAAM,oCAAoC;AAC1C,MAAM,gCAAgC;AACtC,MAAM,uBAAuB;AAC7B,MAAM,wBAAwB;AAC9B,MAAM,uBAAuB;AAC7B,MAAM,0BAA0B;AAChC,MAAM,wBAAwB;AAC9B,MAAM,mBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.layout_manager.utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.layout_manager.utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Guid from \"../../../core/guid\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    captionize\r\n} from \"../../../core/utils/inflector\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isDefined,\r\n    isFunction\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    SIMPLE_ITEM_TYPE\r\n} from \"./constants\";\r\nconst EDITORS_WITH_ARRAY_VALUE = [\"dxTagBox\", \"dxRangeSlider\", \"dxDateRangeBox\"];\r\nconst EDITORS_WITH_MULTIPLE_INPUT_FIELDS = [\"dxRangeSlider\", \"dxDateRangeBox\"];\r\nconst EDITORS_WITH_SPECIFIC_LABELS = [\"dxRangeSlider\", \"dxSlider\"];\r\nexport const EDITORS_WITHOUT_LABELS = [\"dxCalendar\", \"dxCheckBox\", \"dxHtmlEditor\", \"dxRadioGroup\", \"dxRangeSlider\", \"dxSlider\", \"dxSwitch\"];\r\nconst DROP_DOWN_EDITORS = [\"dxSelectBox\", \"dxDropDownBox\", \"dxTagBox\", \"dxLookup\", \"dxAutocomplete\", \"dxColorBox\", \"dxDateBox\", \"dxDateRangeBox\"];\r\nexport function convertToRenderFieldItemOptions(_ref) {\r\n    let {\r\n        $parent: $parent,\r\n        rootElementCssClassList: rootElementCssClassList,\r\n        formOrLayoutManager: formOrLayoutManager,\r\n        createComponentCallback: createComponentCallback,\r\n        item: item,\r\n        template: template,\r\n        labelTemplate: labelTemplate,\r\n        name: name,\r\n        formLabelLocation: formLabelLocation,\r\n        requiredMessageTemplate: requiredMessageTemplate,\r\n        validationGroup: validationGroup,\r\n        editorValue: editorValue,\r\n        canAssignUndefinedValueToEditor: canAssignUndefinedValueToEditor,\r\n        editorValidationBoundary: editorValidationBoundary,\r\n        editorStylingMode: editorStylingMode,\r\n        showColonAfterLabel: showColonAfterLabel,\r\n        managerLabelLocation: managerLabelLocation,\r\n        itemId: itemId,\r\n        managerMarkOptions: managerMarkOptions,\r\n        labelMode: labelMode,\r\n        onLabelTemplateRendered: onLabelTemplateRendered\r\n    } = _ref;\r\n    const isRequired = isDefined(item.isRequired) ? item.isRequired : !!_hasRequiredRuleInSet(item.validationRules);\r\n    const isSimpleItem = item.itemType === SIMPLE_ITEM_TYPE;\r\n    const helpID = item.helpText ? `dx-${new Guid}` : null;\r\n    const labelOptions = _convertToLabelOptions({\r\n        item: item,\r\n        id: itemId,\r\n        isRequired: isRequired,\r\n        managerMarkOptions: managerMarkOptions,\r\n        showColonAfterLabel: showColonAfterLabel,\r\n        labelLocation: managerLabelLocation,\r\n        formLabelMode: labelMode,\r\n        labelTemplate: labelTemplate,\r\n        onLabelTemplateRendered: onLabelTemplateRendered\r\n    });\r\n    const needRenderLabel = labelOptions.visible && (labelOptions.text || labelOptions.labelTemplate && isSimpleItem);\r\n    const {\r\n        location: labelLocation,\r\n        labelID: labelID\r\n    } = labelOptions;\r\n    const labelNeedBaselineAlign = \"top\" !== labelLocation && [\"dxTextArea\", \"dxRadioGroup\", \"dxCalendar\", \"dxHtmlEditor\"].includes(item.editorType);\r\n    const editorOptions = _convertToEditorOptions({\r\n        $parent: $parent,\r\n        editorType: item.editorType,\r\n        editorValue: editorValue,\r\n        defaultEditorName: item.dataField,\r\n        canAssignUndefinedValueToEditor: canAssignUndefinedValueToEditor,\r\n        externalEditorOptions: item.editorOptions,\r\n        editorInputId: itemId,\r\n        editorValidationBoundary: editorValidationBoundary,\r\n        editorStylingMode: editorStylingMode,\r\n        formLabelMode: labelMode,\r\n        labelText: labelOptions.textWithoutColon,\r\n        labelMark: labelOptions.markOptions.showRequiredMark ? String.fromCharCode(160) + labelOptions.markOptions.requiredMark : \"\"\r\n    });\r\n    const needRenderOptionalMarkAsHelpText = labelOptions.markOptions.showOptionalMark && !labelOptions.visible && \"hidden\" !== editorOptions.labelMode && !isDefined(item.helpText);\r\n    const helpText = needRenderOptionalMarkAsHelpText ? labelOptions.markOptions.optionalMark : item.helpText;\r\n    return {\r\n        $parent: $parent,\r\n        rootElementCssClassList: rootElementCssClassList,\r\n        formOrLayoutManager: formOrLayoutManager,\r\n        createComponentCallback: createComponentCallback,\r\n        labelOptions: labelOptions,\r\n        labelNeedBaselineAlign: labelNeedBaselineAlign,\r\n        labelLocation: labelLocation,\r\n        needRenderLabel: needRenderLabel,\r\n        item: item,\r\n        isSimpleItem: isSimpleItem,\r\n        isRequired: isRequired,\r\n        template: template,\r\n        helpID: helpID,\r\n        labelID: labelID,\r\n        name: name,\r\n        helpText: helpText,\r\n        formLabelLocation: formLabelLocation,\r\n        requiredMessageTemplate: requiredMessageTemplate,\r\n        validationGroup: validationGroup,\r\n        editorOptions: editorOptions\r\n    }\r\n}\r\nexport function getLabelMarkText(_ref2) {\r\n    let {\r\n        showRequiredMark: showRequiredMark,\r\n        requiredMark: requiredMark,\r\n        showOptionalMark: showOptionalMark,\r\n        optionalMark: optionalMark\r\n    } = _ref2;\r\n    if (!showRequiredMark && !showOptionalMark) {\r\n        return \"\"\r\n    }\r\n    return String.fromCharCode(160) + (showRequiredMark ? requiredMark : optionalMark)\r\n}\r\nexport function convertToLabelMarkOptions(_ref3, isRequired) {\r\n    let {\r\n        showRequiredMark: showRequiredMark,\r\n        requiredMark: requiredMark,\r\n        showOptionalMark: showOptionalMark,\r\n        optionalMark: optionalMark\r\n    } = _ref3;\r\n    return {\r\n        showRequiredMark: showRequiredMark && isRequired,\r\n        requiredMark: requiredMark,\r\n        showOptionalMark: showOptionalMark && !isRequired,\r\n        optionalMark: optionalMark\r\n    }\r\n}\r\n\r\nfunction getDropDownEditorOptions($parent, editorType, editorInputId) {\r\n    const isDropDownEditor = DROP_DOWN_EDITORS.includes(editorType);\r\n    if (!isDropDownEditor) {\r\n        return {}\r\n    }\r\n    return {\r\n        onPopupInitialized: _ref4 => {\r\n            let {\r\n                component: component,\r\n                popup: popup\r\n            } = _ref4;\r\n            const openOnFieldClick = component.option(\"openOnFieldClick\");\r\n            const initialHideOnOutsideClick = popup.option(\"hideOnOutsideClick\");\r\n            if (openOnFieldClick && isFunction(initialHideOnOutsideClick)) {\r\n                const hideOnOutsideClick = e => {\r\n                    const $target = $(e.target);\r\n                    const $label = $parent.find(`label[for=\"${editorInputId}\"]`);\r\n                    const isLabelClicked = !!$target.closest($label).length;\r\n                    return !isLabelClicked && initialHideOnOutsideClick(e)\r\n                };\r\n                component.option(\"dropDownOptions\", {\r\n                    hideOnOutsideClick: hideOnOutsideClick\r\n                });\r\n                popup.option({\r\n                    hideOnOutsideClick: hideOnOutsideClick\r\n                })\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\nfunction _convertToEditorOptions(_ref5) {\r\n    let {\r\n        $parent: $parent,\r\n        editorType: editorType,\r\n        defaultEditorName: defaultEditorName,\r\n        editorValue: editorValue,\r\n        canAssignUndefinedValueToEditor: canAssignUndefinedValueToEditor,\r\n        externalEditorOptions: externalEditorOptions,\r\n        editorInputId: editorInputId,\r\n        editorValidationBoundary: editorValidationBoundary,\r\n        editorStylingMode: editorStylingMode,\r\n        formLabelMode: formLabelMode,\r\n        labelText: labelText,\r\n        labelMark: labelMark\r\n    } = _ref5;\r\n    const editorOptionsWithValue = {};\r\n    if (void 0 !== editorValue || canAssignUndefinedValueToEditor) {\r\n        editorOptionsWithValue.value = editorValue\r\n    }\r\n    if (EDITORS_WITH_ARRAY_VALUE.includes(editorType)) {\r\n        editorOptionsWithValue.value = editorOptionsWithValue.value || []\r\n    }\r\n    let labelMode = null === externalEditorOptions || void 0 === externalEditorOptions ? void 0 : externalEditorOptions.labelMode;\r\n    if (!isDefined(labelMode)) {\r\n        labelMode = \"outside\" === formLabelMode ? \"hidden\" : formLabelMode\r\n    }\r\n    const stylingMode = (null === externalEditorOptions || void 0 === externalEditorOptions ? void 0 : externalEditorOptions.stylingMode) || editorStylingMode;\r\n    const useSpecificLabelOptions = EDITORS_WITH_SPECIFIC_LABELS.includes(editorType);\r\n    const dropDownEditorOptions = getDropDownEditorOptions($parent, editorType, editorInputId);\r\n    const result = extend(true, editorOptionsWithValue, externalEditorOptions, dropDownEditorOptions, {\r\n        inputAttr: {\r\n            id: editorInputId\r\n        },\r\n        validationBoundary: editorValidationBoundary,\r\n        stylingMode: stylingMode,\r\n        label: useSpecificLabelOptions ? null === externalEditorOptions || void 0 === externalEditorOptions ? void 0 : externalEditorOptions.label : labelText,\r\n        labelMode: labelMode,\r\n        labelMark: labelMark\r\n    });\r\n    if (externalEditorOptions) {\r\n        if (result.dataSource) {\r\n            result.dataSource = externalEditorOptions.dataSource\r\n        }\r\n        if (result.items) {\r\n            result.items = externalEditorOptions.items\r\n        }\r\n    }\r\n    if (defaultEditorName) {\r\n        if (EDITORS_WITH_MULTIPLE_INPUT_FIELDS.includes(editorType)) {\r\n            if (\"dxRangeSlider\" === editorType) {\r\n                if (!result.startName) {\r\n                    result.startName = `${defaultEditorName}Start`\r\n                }\r\n                if (!result.endName) {\r\n                    result.endName = `${defaultEditorName}End`\r\n                }\r\n            }\r\n            if (\"dxDateRangeBox\" === editorType) {\r\n                if (!result.startDateName) {\r\n                    result.startDateName = `${defaultEditorName}Start`\r\n                }\r\n                if (!result.endDateName) {\r\n                    result.endDateName = `${defaultEditorName}End`\r\n                }\r\n            }\r\n            return result\r\n        }\r\n        if (!result.name) {\r\n            result.name = defaultEditorName\r\n        }\r\n    }\r\n    return result\r\n}\r\n\r\nfunction _hasRequiredRuleInSet(rules) {\r\n    let hasRequiredRule;\r\n    if (null !== rules && void 0 !== rules && rules.length) {\r\n        each(rules, ((index, rule) => {\r\n            if (\"required\" === rule.type) {\r\n                hasRequiredRule = true;\r\n                return false\r\n            }\r\n        }))\r\n    }\r\n    return hasRequiredRule\r\n}\r\n\r\nfunction _convertToLabelOptions(_ref6) {\r\n    let {\r\n        item: item,\r\n        id: id,\r\n        isRequired: isRequired,\r\n        managerMarkOptions: managerMarkOptions,\r\n        showColonAfterLabel: showColonAfterLabel,\r\n        labelLocation: labelLocation,\r\n        labelTemplate: labelTemplate,\r\n        formLabelMode: formLabelMode,\r\n        onLabelTemplateRendered: onLabelTemplateRendered\r\n    } = _ref6;\r\n    const isEditorWithoutLabels = EDITORS_WITHOUT_LABELS.includes(item.editorType);\r\n    const labelOptions = extend({\r\n        showColon: showColonAfterLabel,\r\n        location: labelLocation,\r\n        id: id,\r\n        visible: \"outside\" === formLabelMode || isEditorWithoutLabels && \"hidden\" !== formLabelMode,\r\n        isRequired: isRequired\r\n    }, item ? item.label : {}, {\r\n        markOptions: convertToLabelMarkOptions(managerMarkOptions, isRequired),\r\n        labelTemplate: labelTemplate,\r\n        onLabelTemplateRendered: onLabelTemplateRendered\r\n    });\r\n    if ([\"dxRadioGroup\", \"dxCheckBox\", \"dxLookup\", \"dxSlider\", \"dxRangeSlider\", \"dxSwitch\", \"dxHtmlEditor\", \"dxDateRangeBox\"].includes(item.editorType)) {\r\n        labelOptions.labelID = `dx-label-${new Guid}`\r\n    }\r\n    if (!labelOptions.text && item.dataField) {\r\n        labelOptions.text = captionize(item.dataField)\r\n    }\r\n    if (labelOptions.text) {\r\n        labelOptions.textWithoutColon = labelOptions.text;\r\n        labelOptions.text += labelOptions.showColon ? \":\" : \"\"\r\n    }\r\n    return labelOptions\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;;;;;;;;AAGA,MAAM,2BAA2B;IAAC;IAAY;IAAiB;CAAiB;AAChF,MAAM,qCAAqC;IAAC;IAAiB;CAAiB;AAC9E,MAAM,+BAA+B;IAAC;IAAiB;CAAW;AAC3D,MAAM,yBAAyB;IAAC;IAAc;IAAc;IAAgB;IAAgB;IAAiB;IAAY;CAAW;AAC3I,MAAM,oBAAoB;IAAC;IAAe;IAAiB;IAAY;IAAY;IAAkB;IAAc;IAAa;CAAiB;AAC1I,SAAS,gCAAgC,IAAI;IAChD,IAAI,EACA,SAAS,OAAO,EAChB,yBAAyB,uBAAuB,EAChD,qBAAqB,mBAAmB,EACxC,yBAAyB,uBAAuB,EAChD,MAAM,IAAI,EACV,UAAU,QAAQ,EAClB,eAAe,aAAa,EAC5B,MAAM,IAAI,EACV,mBAAmB,iBAAiB,EACpC,yBAAyB,uBAAuB,EAChD,iBAAiB,eAAe,EAChC,aAAa,WAAW,EACxB,iCAAiC,+BAA+B,EAChE,0BAA0B,wBAAwB,EAClD,mBAAmB,iBAAiB,EACpC,qBAAqB,mBAAmB,EACxC,sBAAsB,oBAAoB,EAC1C,QAAQ,MAAM,EACd,oBAAoB,kBAAkB,EACtC,WAAW,SAAS,EACpB,yBAAyB,uBAAuB,EACnD,GAAG;IACJ,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG,CAAC,CAAC,sBAAsB,KAAK,eAAe;IAC9G,MAAM,eAAe,KAAK,QAAQ,KAAK,6KAAA,CAAA,mBAAgB;IACvD,MAAM,SAAS,KAAK,QAAQ,GAAG,CAAC,GAAG,EAAE,IAAI,iJAAA,CAAA,UAAI,EAAE,GAAG;IAClD,MAAM,eAAe,uBAAuB;QACxC,MAAM;QACN,IAAI;QACJ,YAAY;QACZ,oBAAoB;QACpB,qBAAqB;QACrB,eAAe;QACf,eAAe;QACf,eAAe;QACf,yBAAyB;IAC7B;IACA,MAAM,kBAAkB,aAAa,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,aAAa,aAAa,IAAI,YAAY;IAChH,MAAM,EACF,UAAU,aAAa,EACvB,SAAS,OAAO,EACnB,GAAG;IACJ,MAAM,yBAAyB,UAAU,iBAAiB;QAAC;QAAc;QAAgB;QAAc;KAAe,CAAC,QAAQ,CAAC,KAAK,UAAU;IAC/I,MAAM,gBAAgB,wBAAwB;QAC1C,SAAS;QACT,YAAY,KAAK,UAAU;QAC3B,aAAa;QACb,mBAAmB,KAAK,SAAS;QACjC,iCAAiC;QACjC,uBAAuB,KAAK,aAAa;QACzC,eAAe;QACf,0BAA0B;QAC1B,mBAAmB;QACnB,eAAe;QACf,WAAW,aAAa,gBAAgB;QACxC,WAAW,aAAa,WAAW,CAAC,gBAAgB,GAAG,OAAO,YAAY,CAAC,OAAO,aAAa,WAAW,CAAC,YAAY,GAAG;IAC9H;IACA,MAAM,mCAAmC,aAAa,WAAW,CAAC,gBAAgB,IAAI,CAAC,aAAa,OAAO,IAAI,aAAa,cAAc,SAAS,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,QAAQ;IAC/K,MAAM,WAAW,mCAAmC,aAAa,WAAW,CAAC,YAAY,GAAG,KAAK,QAAQ;IACzG,OAAO;QACH,SAAS;QACT,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,cAAc;QACd,wBAAwB;QACxB,eAAe;QACf,iBAAiB;QACjB,MAAM;QACN,cAAc;QACd,YAAY;QACZ,UAAU;QACV,QAAQ;QACR,SAAS;QACT,MAAM;QACN,UAAU;QACV,mBAAmB;QACnB,yBAAyB;QACzB,iBAAiB;QACjB,eAAe;IACnB;AACJ;AACO,SAAS,iBAAiB,KAAK;IAClC,IAAI,EACA,kBAAkB,gBAAgB,EAClC,cAAc,YAAY,EAC1B,kBAAkB,gBAAgB,EAClC,cAAc,YAAY,EAC7B,GAAG;IACJ,IAAI,CAAC,oBAAoB,CAAC,kBAAkB;QACxC,OAAO;IACX;IACA,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC,mBAAmB,eAAe,YAAY;AACrF;AACO,SAAS,0BAA0B,KAAK,EAAE,UAAU;IACvD,IAAI,EACA,kBAAkB,gBAAgB,EAClC,cAAc,YAAY,EAC1B,kBAAkB,gBAAgB,EAClC,cAAc,YAAY,EAC7B,GAAG;IACJ,OAAO;QACH,kBAAkB,oBAAoB;QACtC,cAAc;QACd,kBAAkB,oBAAoB,CAAC;QACvC,cAAc;IAClB;AACJ;AAEA,SAAS,yBAAyB,OAAO,EAAE,UAAU,EAAE,aAAa;IAChE,MAAM,mBAAmB,kBAAkB,QAAQ,CAAC;IACpD,IAAI,CAAC,kBAAkB;QACnB,OAAO,CAAC;IACZ;IACA,OAAO;QACH,oBAAoB,CAAA;YAChB,IAAI,EACA,WAAW,SAAS,EACpB,OAAO,KAAK,EACf,GAAG;YACJ,MAAM,mBAAmB,UAAU,MAAM,CAAC;YAC1C,MAAM,4BAA4B,MAAM,MAAM,CAAC;YAC/C,IAAI,oBAAoB,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,4BAA4B;gBAC3D,MAAM,qBAAqB,CAAA;oBACvB,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM;oBAC1B,MAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,WAAW,EAAE,cAAc,EAAE,CAAC;oBAC3D,MAAM,iBAAiB,CAAC,CAAC,QAAQ,OAAO,CAAC,QAAQ,MAAM;oBACvD,OAAO,CAAC,kBAAkB,0BAA0B;gBACxD;gBACA,UAAU,MAAM,CAAC,mBAAmB;oBAChC,oBAAoB;gBACxB;gBACA,MAAM,MAAM,CAAC;oBACT,oBAAoB;gBACxB;YACJ;QACJ;IACJ;AACJ;AAEA,SAAS,wBAAwB,KAAK;IAClC,IAAI,EACA,SAAS,OAAO,EAChB,YAAY,UAAU,EACtB,mBAAmB,iBAAiB,EACpC,aAAa,WAAW,EACxB,iCAAiC,+BAA+B,EAChE,uBAAuB,qBAAqB,EAC5C,eAAe,aAAa,EAC5B,0BAA0B,wBAAwB,EAClD,mBAAmB,iBAAiB,EACpC,eAAe,aAAa,EAC5B,WAAW,SAAS,EACpB,WAAW,SAAS,EACvB,GAAG;IACJ,MAAM,yBAAyB,CAAC;IAChC,IAAI,KAAK,MAAM,eAAe,iCAAiC;QAC3D,uBAAuB,KAAK,GAAG;IACnC;IACA,IAAI,yBAAyB,QAAQ,CAAC,aAAa;QAC/C,uBAAuB,KAAK,GAAG,uBAAuB,KAAK,IAAI,EAAE;IACrE;IACA,IAAI,YAAY,SAAS,yBAAyB,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,SAAS;IAC7H,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QACvB,YAAY,cAAc,gBAAgB,WAAW;IACzD;IACA,MAAM,cAAc,CAAC,SAAS,yBAAyB,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,WAAW,KAAK;IACzI,MAAM,0BAA0B,6BAA6B,QAAQ,CAAC;IACtE,MAAM,wBAAwB,yBAAyB,SAAS,YAAY;IAC5E,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM,wBAAwB,uBAAuB,uBAAuB;QAC9F,WAAW;YACP,IAAI;QACR;QACA,oBAAoB;QACpB,aAAa;QACb,OAAO,0BAA0B,SAAS,yBAAyB,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,KAAK,GAAG;QAC7I,WAAW;QACX,WAAW;IACf;IACA,IAAI,uBAAuB;QACvB,IAAI,OAAO,UAAU,EAAE;YACnB,OAAO,UAAU,GAAG,sBAAsB,UAAU;QACxD;QACA,IAAI,OAAO,KAAK,EAAE;YACd,OAAO,KAAK,GAAG,sBAAsB,KAAK;QAC9C;IACJ;IACA,IAAI,mBAAmB;QACnB,IAAI,mCAAmC,QAAQ,CAAC,aAAa;YACzD,IAAI,oBAAoB,YAAY;gBAChC,IAAI,CAAC,OAAO,SAAS,EAAE;oBACnB,OAAO,SAAS,GAAG,GAAG,kBAAkB,KAAK,CAAC;gBAClD;gBACA,IAAI,CAAC,OAAO,OAAO,EAAE;oBACjB,OAAO,OAAO,GAAG,GAAG,kBAAkB,GAAG,CAAC;gBAC9C;YACJ;YACA,IAAI,qBAAqB,YAAY;gBACjC,IAAI,CAAC,OAAO,aAAa,EAAE;oBACvB,OAAO,aAAa,GAAG,GAAG,kBAAkB,KAAK,CAAC;gBACtD;gBACA,IAAI,CAAC,OAAO,WAAW,EAAE;oBACrB,OAAO,WAAW,GAAG,GAAG,kBAAkB,GAAG,CAAC;gBAClD;YACJ;YACA,OAAO;QACX;QACA,IAAI,CAAC,OAAO,IAAI,EAAE;YACd,OAAO,IAAI,GAAG;QAClB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,sBAAsB,KAAK;IAChC,IAAI;IACJ,IAAI,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,MAAM,EAAE;QACpD,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;YACjB,IAAI,eAAe,KAAK,IAAI,EAAE;gBAC1B,kBAAkB;gBAClB,OAAO;YACX;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,uBAAuB,KAAK;IACjC,IAAI,EACA,MAAM,IAAI,EACV,IAAI,EAAE,EACN,YAAY,UAAU,EACtB,oBAAoB,kBAAkB,EACtC,qBAAqB,mBAAmB,EACxC,eAAe,aAAa,EAC5B,eAAe,aAAa,EAC5B,eAAe,aAAa,EAC5B,yBAAyB,uBAAuB,EACnD,GAAG;IACJ,MAAM,wBAAwB,uBAAuB,QAAQ,CAAC,KAAK,UAAU;IAC7E,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE;QACxB,WAAW;QACX,UAAU;QACV,IAAI;QACJ,SAAS,cAAc,iBAAiB,yBAAyB,aAAa;QAC9E,YAAY;IAChB,GAAG,OAAO,KAAK,KAAK,GAAG,CAAC,GAAG;QACvB,aAAa,0BAA0B,oBAAoB;QAC3D,eAAe;QACf,yBAAyB;IAC7B;IACA,IAAI;QAAC;QAAgB;QAAc;QAAY;QAAY;QAAiB;QAAY;QAAgB;KAAiB,CAAC,QAAQ,CAAC,KAAK,UAAU,GAAG;QACjJ,aAAa,OAAO,GAAG,CAAC,SAAS,EAAE,IAAI,iJAAA,CAAA,UAAI,EAAE;IACjD;IACA,IAAI,CAAC,aAAa,IAAI,IAAI,KAAK,SAAS,EAAE;QACtC,aAAa,IAAI,GAAG,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;IACjD;IACA,IAAI,aAAa,IAAI,EAAE;QACnB,aAAa,gBAAgB,GAAG,aAAa,IAAI;QACjD,aAAa,IAAI,IAAI,aAAa,SAAS,GAAG,MAAM;IACxD;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/components/m_label.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/components/m_label.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getPublicElement\r\n} from \"../../../../core/element\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    isDefined\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    FIELD_ITEM_LABEL_CLASS,\r\n    FIELD_ITEM_LABEL_CONTENT_CLASS\r\n} from \"../constants\";\r\nimport {\r\n    getLabelMarkText\r\n} from \"../m_form.layout_manager.utils\";\r\nexport const GET_LABEL_WIDTH_BY_TEXT_CLASS = \"dx-layout-manager-hidden-label\";\r\nexport const FIELD_ITEM_REQUIRED_MARK_CLASS = \"dx-field-item-required-mark\";\r\nexport const FIELD_ITEM_LABEL_LOCATION_CLASS = \"dx-field-item-label-location-\";\r\nexport const FIELD_ITEM_OPTIONAL_MARK_CLASS = \"dx-field-item-optional-mark\";\r\nexport const FIELD_ITEM_LABEL_TEXT_CLASS = \"dx-field-item-label-text\";\r\nexport function renderLabel(_ref) {\r\n    let {\r\n        text: text,\r\n        id: id,\r\n        location: location,\r\n        alignment: alignment,\r\n        labelID: labelID = null,\r\n        markOptions: markOptions = {},\r\n        labelTemplate: labelTemplate,\r\n        labelTemplateData: labelTemplateData,\r\n        onLabelTemplateRendered: onLabelTemplateRendered\r\n    } = _ref;\r\n    if ((!isDefined(text) || text.length <= 0) && !isDefined(labelTemplate)) {\r\n        return null\r\n    }\r\n    const $label = $(\"<label>\").addClass(`${FIELD_ITEM_LABEL_CLASS} dx-field-item-label-location-${location}`).attr(\"for\", id).attr(\"id\", labelID).css(\"textAlign\", alignment);\r\n    const $labelContainer = $(\"<span>\").addClass(FIELD_ITEM_LABEL_CONTENT_CLASS);\r\n    let $labelContent = $(\"<span>\").addClass(\"dx-field-item-label-text\").text(text);\r\n    if (labelTemplate) {\r\n        $labelContent = $(\"<div>\").addClass(\"dx-field-item-custom-label-content\");\r\n        labelTemplateData.text = text;\r\n        labelTemplate.render({\r\n            container: getPublicElement($labelContent),\r\n            model: labelTemplateData,\r\n            onRendered() {\r\n                null === onLabelTemplateRendered || void 0 === onLabelTemplateRendered || onLabelTemplateRendered()\r\n            }\r\n        })\r\n    }\r\n    return $label.append($labelContainer.append($labelContent, _renderLabelMark(markOptions)))\r\n}\r\n\r\nfunction _renderLabelMark(markOptions) {\r\n    const markText = getLabelMarkText(markOptions);\r\n    if (\"\" === markText) {\r\n        return null\r\n    }\r\n    return $(\"<span>\").addClass(markOptions.showRequiredMark ? \"dx-field-item-required-mark\" : \"dx-field-item-optional-mark\").text(markText)\r\n}\r\nexport function setLabelWidthByMaxLabelWidth($targetContainer, labelsSelector, labelMarkOptions) {\r\n    const FIELD_ITEM_LABEL_CONTENT_CLASS_Selector = `${labelsSelector} > .${FIELD_ITEM_LABEL_CLASS}:not(.dx-field-item-label-location-top) > .${FIELD_ITEM_LABEL_CONTENT_CLASS}`;\r\n    const $FIELD_ITEM_LABEL_CONTENT_CLASS_Items = $targetContainer.find(FIELD_ITEM_LABEL_CONTENT_CLASS_Selector);\r\n    const FIELD_ITEM_LABEL_CONTENT_CLASS_Length = $FIELD_ITEM_LABEL_CONTENT_CLASS_Items.length;\r\n    let labelWidth;\r\n    let i;\r\n    let maxWidth = 0;\r\n    for (i = 0; i < FIELD_ITEM_LABEL_CONTENT_CLASS_Length; i++) {\r\n        labelWidth = getLabelWidthByHTML($FIELD_ITEM_LABEL_CONTENT_CLASS_Items[i]);\r\n        if (labelWidth > maxWidth) {\r\n            maxWidth = labelWidth\r\n        }\r\n    }\r\n    for (i = 0; i < FIELD_ITEM_LABEL_CONTENT_CLASS_Length; i++) {\r\n        $FIELD_ITEM_LABEL_CONTENT_CLASS_Items[i].style.width = `${maxWidth}px`\r\n    }\r\n}\r\n\r\nfunction getLabelWidthByHTML(labelContent) {\r\n    let result = 0;\r\n    const itemsCount = labelContent.children.length;\r\n    for (let i = 0; i < itemsCount; i++) {\r\n        const child = labelContent.children[i];\r\n        result += child.offsetWidth\r\n    }\r\n    return result\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;AACD;AAAA;AAGA;AACA;AAAA;AAGA;AAIA;;;;;;AAGO,MAAM,gCAAgC;AACtC,MAAM,iCAAiC;AACvC,MAAM,kCAAkC;AACxC,MAAM,iCAAiC;AACvC,MAAM,8BAA8B;AACpC,SAAS,YAAY,IAAI;IAC5B,IAAI,EACA,MAAM,IAAI,EACV,IAAI,EAAE,EACN,UAAU,QAAQ,EAClB,WAAW,SAAS,EACpB,SAAS,UAAU,IAAI,EACvB,aAAa,cAAc,CAAC,CAAC,EAC7B,eAAe,aAAa,EAC5B,mBAAmB,iBAAiB,EACpC,yBAAyB,uBAAuB,EACnD,GAAG;IACJ,IAAI,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,KAAK,MAAM,IAAI,CAAC,KAAK,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;QACrE,OAAO;IACX;IACA,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,QAAQ,CAAC,GAAG,6KAAA,CAAA,yBAAsB,CAAC,8BAA8B,EAAE,UAAU,EAAE,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,MAAM,SAAS,GAAG,CAAC,aAAa;IAChK,MAAM,kBAAkB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ,CAAC,6KAAA,CAAA,iCAA8B;IAC3E,IAAI,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ,CAAC,4BAA4B,IAAI,CAAC;IAC1E,IAAI,eAAe;QACf,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACpC,kBAAkB,IAAI,GAAG;QACzB,cAAc,MAAM,CAAC;YACjB,WAAW,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YAC5B,OAAO;YACP;gBACI,SAAS,2BAA2B,KAAK,MAAM,2BAA2B;YAC9E;QACJ;IACJ;IACA,OAAO,OAAO,MAAM,CAAC,gBAAgB,MAAM,CAAC,eAAe,iBAAiB;AAChF;AAEA,SAAS,iBAAiB,WAAW;IACjC,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,mBAAgB,AAAD,EAAE;IAClC,IAAI,OAAO,UAAU;QACjB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ,CAAC,YAAY,gBAAgB,GAAG,gCAAgC,+BAA+B,IAAI,CAAC;AACnI;AACO,SAAS,6BAA6B,gBAAgB,EAAE,cAAc,EAAE,gBAAgB;IAC3F,MAAM,0CAA0C,GAAG,eAAe,IAAI,EAAE,6KAAA,CAAA,yBAAsB,CAAC,2CAA2C,EAAE,6KAAA,CAAA,iCAA8B,EAAE;IAC5K,MAAM,wCAAwC,iBAAiB,IAAI,CAAC;IACpE,MAAM,wCAAwC,sCAAsC,MAAM;IAC1F,IAAI;IACJ,IAAI;IACJ,IAAI,WAAW;IACf,IAAK,IAAI,GAAG,IAAI,uCAAuC,IAAK;QACxD,aAAa,oBAAoB,qCAAqC,CAAC,EAAE;QACzE,IAAI,aAAa,UAAU;YACvB,WAAW;QACf;IACJ;IACA,IAAK,IAAI,GAAG,IAAI,uCAAuC,IAAK;QACxD,qCAAqC,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,GAAG,GAAG,SAAS,EAAE,CAAC;IAC1E;AACJ;AAEA,SAAS,oBAAoB,YAAY;IACrC,IAAI,SAAS;IACb,MAAM,aAAa,aAAa,QAAQ,CAAC,MAAM;IAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACjC,MAAM,QAAQ,aAAa,QAAQ,CAAC,EAAE;QACtC,UAAU,MAAM,WAAW;IAC/B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 507, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/components/m_field_item.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/components/m_field_item.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    name as clickEventName\r\n} from \"../../../../common/core/events/click\";\r\nimport eventsEngine from \"../../../../common/core/events/core/events_engine\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../../core/element\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport {\r\n    captionize\r\n} from \"../../../../core/utils/inflector\";\r\nimport {\r\n    format\r\n} from \"../../../../core/utils/string\";\r\nimport {\r\n    isMaterialBased\r\n} from \"../../../../ui/themes\";\r\nimport Validator from \"../../../../ui/validator\";\r\nimport errors from \"../../../../ui/widget/ui.errors\";\r\nimport {\r\n    FIELD_ITEM_CONTENT_CLASS\r\n} from \"../constants\";\r\nimport {\r\n    renderLabel\r\n} from \"./m_label\";\r\nexport const FLEX_LAYOUT_CLASS = \"dx-flex-layout\";\r\nexport const FIELD_ITEM_OPTIONAL_CLASS = \"dx-field-item-optional\";\r\nexport const FIELD_ITEM_REQUIRED_CLASS = \"dx-field-item-required\";\r\nexport const FIELD_ITEM_CONTENT_WRAPPER_CLASS = \"dx-field-item-content-wrapper\";\r\nexport const FIELD_ITEM_CONTENT_LOCATION_CLASS = \"dx-field-item-content-location-\";\r\nexport const FIELD_ITEM_LABEL_ALIGN_CLASS = \"dx-field-item-label-align\";\r\nexport const FIELD_ITEM_HELP_TEXT_CLASS = \"dx-field-item-help-text\";\r\nexport const LABEL_VERTICAL_ALIGNMENT_CLASS = \"dx-label-v-align\";\r\nexport const LABEL_HORIZONTAL_ALIGNMENT_CLASS = \"dx-label-h-align\";\r\nexport const TOGGLE_CONTROLS_PADDING_CLASS = \"dx-toggle-controls-paddings\";\r\nconst TEMPLATE_WRAPPER_CLASS = \"dx-template-wrapper\";\r\nconst VALIDATION_TARGET_CLASS = \"dx-validation-target\";\r\nconst INVALID_CLASS = \"dx-invalid\";\r\nexport function renderFieldItem(_ref) {\r\n    let {\r\n        $parent: $parent,\r\n        rootElementCssClassList: rootElementCssClassList,\r\n        formOrLayoutManager: formOrLayoutManager,\r\n        createComponentCallback: createComponentCallback,\r\n        labelOptions: labelOptions,\r\n        labelNeedBaselineAlign: labelNeedBaselineAlign,\r\n        labelLocation: labelLocation,\r\n        needRenderLabel: needRenderLabel,\r\n        formLabelLocation: formLabelLocation,\r\n        item: item,\r\n        editorOptions: editorOptions,\r\n        isSimpleItem: isSimpleItem,\r\n        isRequired: isRequired,\r\n        template: template,\r\n        helpID: helpID,\r\n        labelID: labelID,\r\n        name: name,\r\n        helpText: helpText,\r\n        requiredMessageTemplate: requiredMessageTemplate,\r\n        validationGroup: validationGroup\r\n    } = _ref;\r\n    const $rootElement = $(\"<div>\").addClass(rootElementCssClassList.join(\" \")).appendTo($parent);\r\n    $rootElement.addClass(isRequired ? \"dx-field-item-required\" : \"dx-field-item-optional\");\r\n    if (isSimpleItem) {\r\n        $rootElement.addClass(\"dx-flex-layout\")\r\n    }\r\n    if (isSimpleItem && labelNeedBaselineAlign) {\r\n        $rootElement.addClass(\"dx-field-item-label-align\")\r\n    }\r\n    const $fieldEditorContainer = $(\"<div>\");\r\n    $fieldEditorContainer.data(\"dx-form-item\", item);\r\n    $fieldEditorContainer.addClass(FIELD_ITEM_CONTENT_CLASS).addClass(\"dx-field-item-content-location-\" + {\r\n        right: \"left\",\r\n        left: \"right\",\r\n        top: \"bottom\"\r\n    } [formLabelLocation]);\r\n    let $label = null;\r\n    if (needRenderLabel) {\r\n        if (labelOptions.labelTemplate) {\r\n            labelOptions.labelTemplateData = getTemplateData(item, editorOptions, formOrLayoutManager)\r\n        }\r\n        $label = renderLabel(labelOptions)\r\n    }\r\n    if ($label) {\r\n        const {\r\n            editorType: editorType\r\n        } = item;\r\n        $rootElement.append($label);\r\n        if (\"top\" === labelLocation || \"left\" === labelLocation) {\r\n            $rootElement.append($fieldEditorContainer)\r\n        }\r\n        if (\"right\" === labelLocation) {\r\n            $rootElement.prepend($fieldEditorContainer)\r\n        }\r\n        if (\"top\" === labelLocation) {\r\n            $rootElement.addClass(\"dx-label-v-align\")\r\n        } else {\r\n            $rootElement.addClass(\"dx-label-h-align\")\r\n        }\r\n        if (\"dxCheckBox\" === editorType || \"dxSwitch\" === editorType) {\r\n            eventsEngine.on($label, clickEventName, (() => {\r\n                eventsEngine.trigger($fieldEditorContainer.children(), clickEventName)\r\n            }))\r\n        }\r\n        const toggleControls = [\"dxCheckBox\", \"dxSwitch\", \"dxRadioGroup\"];\r\n        const isToggleControls = toggleControls.includes(editorType);\r\n        const labelAlignment = labelOptions.alignment;\r\n        const isLabelAlignmentLeft = \"left\" === labelAlignment || !labelAlignment;\r\n        const hasNotTemplate = !template;\r\n        const isLabelOnTop = \"top\" === labelLocation;\r\n        if (hasNotTemplate && isToggleControls && isLabelOnTop && isLabelAlignmentLeft) {\r\n            $fieldEditorContainer.addClass(\"dx-toggle-controls-paddings\")\r\n        }\r\n    } else {\r\n        $rootElement.append($fieldEditorContainer)\r\n    }\r\n    let widgetInstance;\r\n    if (template) {\r\n        template.render({\r\n            container: getPublicElement($fieldEditorContainer),\r\n            model: getTemplateData(item, editorOptions, formOrLayoutManager),\r\n            onRendered() {\r\n                const $validationTarget = getValidationTarget($fieldEditorContainer);\r\n                const validationTargetInstance = tryGetValidationTargetInstance($validationTarget);\r\n                subscribeWrapperInvalidClassToggle(validationTargetInstance)\r\n            }\r\n        })\r\n    } else {\r\n        const $div = $(\"<div>\").appendTo($fieldEditorContainer);\r\n        try {\r\n            widgetInstance = createComponentCallback($div, item.editorType, editorOptions);\r\n            widgetInstance.setAria(\"describedby\", helpID);\r\n            if (labelID) {\r\n                widgetInstance.setAria(\"labelledby\", labelID)\r\n            }\r\n            widgetInstance.setAria(\"required\", isRequired)\r\n        } catch (e) {\r\n            errors.log(\"E1035\", e.message)\r\n        }\r\n    }\r\n    const $validationTarget = getValidationTarget($fieldEditorContainer);\r\n    const validationTargetInstance = $validationTarget && $validationTarget.data(\"dx-validation-target\");\r\n    if (validationTargetInstance) {\r\n        const isItemHaveCustomLabel = item.label && item.label.text;\r\n        const itemName = isItemHaveCustomLabel ? null : name;\r\n        const fieldName = isItemHaveCustomLabel ? item.label.text : itemName && captionize(itemName);\r\n        let validationRules;\r\n        if (isSimpleItem) {\r\n            if (item.validationRules) {\r\n                validationRules = item.validationRules\r\n            } else {\r\n                const requiredMessage = format(requiredMessageTemplate, fieldName || \"\");\r\n                validationRules = item.isRequired ? [{\r\n                    type: \"required\",\r\n                    message: requiredMessage\r\n                }] : null\r\n            }\r\n        }\r\n        if (Array.isArray(validationRules) && validationRules.length) {\r\n            createComponentCallback($validationTarget, Validator, {\r\n                validationRules: validationRules,\r\n                validationGroup: validationGroup,\r\n                dataGetter: () => ({\r\n                    formItem: item\r\n                })\r\n            })\r\n        }\r\n        subscribeWrapperInvalidClassToggle(validationTargetInstance)\r\n    }\r\n    if (helpText && isSimpleItem) {\r\n        const $editorParent = $fieldEditorContainer.parent();\r\n        $editorParent.append($(\"<div>\").addClass(\"dx-field-item-content-wrapper\").append($fieldEditorContainer).append($(\"<div>\").addClass(\"dx-field-item-help-text\").attr(\"id\", helpID).text(helpText)))\r\n    }\r\n    return {\r\n        $fieldEditorContainer: $fieldEditorContainer,\r\n        $rootElement: $rootElement,\r\n        widgetInstance: widgetInstance\r\n    }\r\n}\r\n\r\nfunction getValidationTarget($fieldEditorContainer) {\r\n    const $editor = $fieldEditorContainer.children().first();\r\n    return $editor.hasClass(\"dx-template-wrapper\") ? $editor.children().first() : $editor\r\n}\r\n\r\nfunction tryGetValidationTargetInstance($validationTarget) {\r\n    var _$validationTarget$pa;\r\n    return (null === $validationTarget || void 0 === $validationTarget ? void 0 : $validationTarget.data(\"dx-validation-target\")) || (null === $validationTarget || void 0 === $validationTarget || null === (_$validationTarget$pa = $validationTarget.parent) || void 0 === _$validationTarget$pa || null === (_$validationTarget$pa = _$validationTarget$pa.call($validationTarget)) || void 0 === _$validationTarget$pa ? void 0 : _$validationTarget$pa.data(\"dx-validation-target\"))\r\n}\r\n\r\nfunction subscribeWrapperInvalidClassToggle(validationTargetInstance) {\r\n    if (validationTargetInstance && isMaterialBased()) {\r\n        const wrapperClass = \".dx-field-item-content-wrapper\";\r\n        const toggleInvalidClass = _ref2 => {\r\n            let {\r\n                element: element,\r\n                component: component\r\n            } = _ref2;\r\n            const {\r\n                isValid: isValid,\r\n                validationMessageMode: validationMessageMode\r\n            } = component.option();\r\n            $(element).parents(wrapperClass).toggleClass(\"dx-invalid\", false === isValid && (component._isFocused() || \"always\" === validationMessageMode))\r\n        };\r\n        validationTargetInstance.on(\"optionChanged\", (e => {\r\n            if (\"isValid\" !== e.name) {\r\n                return\r\n            }\r\n            toggleInvalidClass(e)\r\n        }));\r\n        validationTargetInstance.on(\"focusIn\", toggleInvalidClass).on(\"focusOut\", toggleInvalidClass).on(\"enterKey\", toggleInvalidClass)\r\n    }\r\n}\r\n\r\nfunction getTemplateData(item, editorOptions, formOrLayoutManager) {\r\n    return {\r\n        dataField: item.dataField,\r\n        editorType: item.editorType,\r\n        editorOptions: editorOptions,\r\n        component: formOrLayoutManager,\r\n        name: item.name\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;AACD;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAGA;AAGA;AACA;AACA;AAGA;;;;;;;;;;;;AAGO,MAAM,oBAAoB;AAC1B,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAClC,MAAM,mCAAmC;AACzC,MAAM,oCAAoC;AAC1C,MAAM,+BAA+B;AACrC,MAAM,6BAA6B;AACnC,MAAM,iCAAiC;AACvC,MAAM,mCAAmC;AACzC,MAAM,gCAAgC;AAC7C,MAAM,yBAAyB;AAC/B,MAAM,0BAA0B;AAChC,MAAM,gBAAgB;AACf,SAAS,gBAAgB,IAAI;IAChC,IAAI,EACA,SAAS,OAAO,EAChB,yBAAyB,uBAAuB,EAChD,qBAAqB,mBAAmB,EACxC,yBAAyB,uBAAuB,EAChD,cAAc,YAAY,EAC1B,wBAAwB,sBAAsB,EAC9C,eAAe,aAAa,EAC5B,iBAAiB,eAAe,EAChC,mBAAmB,iBAAiB,EACpC,MAAM,IAAI,EACV,eAAe,aAAa,EAC5B,cAAc,YAAY,EAC1B,YAAY,UAAU,EACtB,UAAU,QAAQ,EAClB,QAAQ,MAAM,EACd,SAAS,OAAO,EAChB,MAAM,IAAI,EACV,UAAU,QAAQ,EAClB,yBAAyB,uBAAuB,EAChD,iBAAiB,eAAe,EACnC,GAAG;IACJ,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,wBAAwB,IAAI,CAAC,MAAM,QAAQ,CAAC;IACrF,aAAa,QAAQ,CAAC,aAAa,2BAA2B;IAC9D,IAAI,cAAc;QACd,aAAa,QAAQ,CAAC;IAC1B;IACA,IAAI,gBAAgB,wBAAwB;QACxC,aAAa,QAAQ,CAAC;IAC1B;IACA,MAAM,wBAAwB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;IAChC,sBAAsB,IAAI,CAAC,gBAAgB;IAC3C,sBAAsB,QAAQ,CAAC,6KAAA,CAAA,2BAAwB,EAAE,QAAQ,CAAC,oCAAoC,CAAA;QAClG,OAAO;QACP,MAAM;QACN,KAAK;IACT,CAAA,CAAE,CAAC,kBAAkB;IACrB,IAAI,SAAS;IACb,IAAI,iBAAiB;QACjB,IAAI,aAAa,aAAa,EAAE;YAC5B,aAAa,iBAAiB,GAAG,gBAAgB,MAAM,eAAe;QAC1E;QACA,SAAS,CAAA,GAAA,yLAAA,CAAA,cAAW,AAAD,EAAE;IACzB;IACA,IAAI,QAAQ;QACR,MAAM,EACF,YAAY,UAAU,EACzB,GAAG;QACJ,aAAa,MAAM,CAAC;QACpB,IAAI,UAAU,iBAAiB,WAAW,eAAe;YACrD,aAAa,MAAM,CAAC;QACxB;QACA,IAAI,YAAY,eAAe;YAC3B,aAAa,OAAO,CAAC;QACzB;QACA,IAAI,UAAU,eAAe;YACzB,aAAa,QAAQ,CAAC;QAC1B,OAAO;YACH,aAAa,QAAQ,CAAC;QAC1B;QACA,IAAI,iBAAiB,cAAc,eAAe,YAAY;YAC1D,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,uKAAA,CAAA,OAAc,EAAG;gBACrC,uLAAA,CAAA,UAAY,CAAC,OAAO,CAAC,sBAAsB,QAAQ,IAAI,uKAAA,CAAA,OAAc;YACzE;QACJ;QACA,MAAM,iBAAiB;YAAC;YAAc;YAAY;SAAe;QACjE,MAAM,mBAAmB,eAAe,QAAQ,CAAC;QACjD,MAAM,iBAAiB,aAAa,SAAS;QAC7C,MAAM,uBAAuB,WAAW,kBAAkB,CAAC;QAC3D,MAAM,iBAAiB,CAAC;QACxB,MAAM,eAAe,UAAU;QAC/B,IAAI,kBAAkB,oBAAoB,gBAAgB,sBAAsB;YAC5E,sBAAsB,QAAQ,CAAC;QACnC;IACJ,OAAO;QACH,aAAa,MAAM,CAAC;IACxB;IACA,IAAI;IACJ,IAAI,UAAU;QACV,SAAS,MAAM,CAAC;YACZ,WAAW,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;YAC5B,OAAO,gBAAgB,MAAM,eAAe;YAC5C;gBACI,MAAM,oBAAoB,oBAAoB;gBAC9C,MAAM,2BAA2B,+BAA+B;gBAChE,mCAAmC;YACvC;QACJ;IACJ,OAAO;QACH,MAAM,OAAO,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACjC,IAAI;YACA,iBAAiB,wBAAwB,MAAM,KAAK,UAAU,EAAE;YAChE,eAAe,OAAO,CAAC,eAAe;YACtC,IAAI,SAAS;gBACT,eAAe,OAAO,CAAC,cAAc;YACzC;YACA,eAAe,OAAO,CAAC,YAAY;QACvC,EAAE,OAAO,GAAG;YACR,iKAAA,CAAA,UAAM,CAAC,GAAG,CAAC,SAAS,EAAE,OAAO;QACjC;IACJ;IACA,MAAM,oBAAoB,oBAAoB;IAC9C,MAAM,2BAA2B,qBAAqB,kBAAkB,IAAI,CAAC;IAC7E,IAAI,0BAA0B;QAC1B,MAAM,wBAAwB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,IAAI;QAC3D,MAAM,WAAW,wBAAwB,OAAO;QAChD,MAAM,YAAY,wBAAwB,KAAK,KAAK,CAAC,IAAI,GAAG,YAAY,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAE;QACnF,IAAI;QACJ,IAAI,cAAc;YACd,IAAI,KAAK,eAAe,EAAE;gBACtB,kBAAkB,KAAK,eAAe;YAC1C,OAAO;gBACH,MAAM,kBAAkB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,yBAAyB,aAAa;gBACrE,kBAAkB,KAAK,UAAU,GAAG;oBAAC;wBACjC,MAAM;wBACN,SAAS;oBACb;iBAAE,GAAG;YACT;QACJ;QACA,IAAI,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,EAAE;YAC1D,wBAAwB,mBAAmB,oJAAA,CAAA,UAAS,EAAE;gBAClD,iBAAiB;gBACjB,iBAAiB;gBACjB,YAAY,IAAM,CAAC;wBACf,UAAU;oBACd,CAAC;YACL;QACJ;QACA,mCAAmC;IACvC;IACA,IAAI,YAAY,cAAc;QAC1B,MAAM,gBAAgB,sBAAsB,MAAM;QAClD,cAAc,MAAM,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,iCAAiC,MAAM,CAAC,uBAAuB,MAAM,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,2BAA2B,IAAI,CAAC,MAAM,QAAQ,IAAI,CAAC;IAC1L;IACA,OAAO;QACH,uBAAuB;QACvB,cAAc;QACd,gBAAgB;IACpB;AACJ;AAEA,SAAS,oBAAoB,qBAAqB;IAC9C,MAAM,UAAU,sBAAsB,QAAQ,GAAG,KAAK;IACtD,OAAO,QAAQ,QAAQ,CAAC,yBAAyB,QAAQ,QAAQ,GAAG,KAAK,KAAK;AAClF;AAEA,SAAS,+BAA+B,iBAAiB;IACrD,IAAI;IACJ,OAAO,CAAC,SAAS,qBAAqB,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,IAAI,CAAC,uBAAuB,KAAK,CAAC,SAAS,qBAAqB,KAAK,MAAM,qBAAqB,SAAS,CAAC,wBAAwB,kBAAkB,MAAM,KAAK,KAAK,MAAM,yBAAyB,SAAS,CAAC,wBAAwB,sBAAsB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,CAAC,uBAAuB;AACzd;AAEA,SAAS,mCAAmC,wBAAwB;IAChE,IAAI,4BAA4B,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD,KAAK;QAC/C,MAAM,eAAe;QACrB,MAAM,qBAAqB,CAAA;YACvB,IAAI,EACA,SAAS,OAAO,EAChB,WAAW,SAAS,EACvB,GAAG;YACJ,MAAM,EACF,SAAS,OAAO,EAChB,uBAAuB,qBAAqB,EAC/C,GAAG,UAAU,MAAM;YACpB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,OAAO,CAAC,cAAc,WAAW,CAAC,cAAc,UAAU,WAAW,CAAC,UAAU,UAAU,MAAM,aAAa,qBAAqB;QACjJ;QACA,yBAAyB,EAAE,CAAC,iBAAkB,CAAA;YAC1C,IAAI,cAAc,EAAE,IAAI,EAAE;gBACtB;YACJ;YACA,mBAAmB;QACvB;QACA,yBAAyB,EAAE,CAAC,WAAW,oBAAoB,EAAE,CAAC,YAAY,oBAAoB,EAAE,CAAC,YAAY;IACjH;AACJ;AAEA,SAAS,gBAAgB,IAAI,EAAE,aAAa,EAAE,mBAAmB;IAC7D,OAAO;QACH,WAAW,KAAK,SAAS;QACzB,YAAY,KAAK,UAAU;QAC3B,eAAe;QACf,WAAW;QACX,MAAM,KAAK,IAAI;IACnB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.items_runtime_info.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.items_runtime_info.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Guid from \"../../../core/guid\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isString\r\n} from \"../../../core/utils/type\";\r\nexport default class FormItemsRunTimeInfo {\r\n    constructor() {\r\n        this._map = {}\r\n    }\r\n    _findWidgetInstance(condition) {\r\n        let result;\r\n        each(this._map, ((guid, _ref) => {\r\n            let {\r\n                widgetInstance: widgetInstance,\r\n                item: item\r\n            } = _ref;\r\n            if (condition(item)) {\r\n                result = widgetInstance;\r\n                return false\r\n            }\r\n        }));\r\n        return result\r\n    }\r\n    _findFieldByCondition(callback, valueExpr) {\r\n        let result;\r\n        each(this._map, ((key, value) => {\r\n            if (callback(value)) {\r\n                result = \"guid\" === valueExpr ? key : value[valueExpr];\r\n                return false\r\n            }\r\n        }));\r\n        return result\r\n    }\r\n    clear() {\r\n        this._map = {}\r\n    }\r\n    removeItemsByItems(itemsRunTimeInfo) {\r\n        each(itemsRunTimeInfo.getItems(), (guid => this.removeItemByKey(guid)))\r\n    }\r\n    removeItemByKey(key) {\r\n        delete this._map[key]\r\n    }\r\n    add(options) {\r\n        const key = options.guid || new Guid;\r\n        this._map[key] = options;\r\n        return key\r\n    }\r\n    addItemsOrExtendFrom(itemsRunTimeInfo) {\r\n        itemsRunTimeInfo.each(((key, itemRunTimeInfo) => {\r\n            if (this._map[key]) {\r\n                if (itemRunTimeInfo.widgetInstance) {\r\n                    this._map[key].widgetInstance = itemRunTimeInfo.widgetInstance\r\n                }\r\n                this._map[key].$itemContainer = itemRunTimeInfo.$itemContainer\r\n            } else {\r\n                this.add({\r\n                    item: itemRunTimeInfo.item,\r\n                    widgetInstance: itemRunTimeInfo.widgetInstance,\r\n                    guid: key,\r\n                    $itemContainer: itemRunTimeInfo.$itemContainer\r\n                })\r\n            }\r\n        }))\r\n    }\r\n    extendRunTimeItemInfoByKey(key, options) {\r\n        if (this._map[key]) {\r\n            this._map[key] = extend(this._map[key], options)\r\n        }\r\n    }\r\n    findWidgetInstanceByItem(item) {\r\n        return this._findWidgetInstance((storedItem => storedItem === item))\r\n    }\r\n    findGroupOrTabLayoutManagerByPath(targetPath) {\r\n        return this._findFieldByCondition((_ref2 => {\r\n            let {\r\n                path: path\r\n            } = _ref2;\r\n            return path === targetPath\r\n        }), \"layoutManager\")\r\n    }\r\n    findKeyByPath(targetPath) {\r\n        return this._findFieldByCondition((_ref3 => {\r\n            let {\r\n                path: path\r\n            } = _ref3;\r\n            return path === targetPath\r\n        }), \"guid\")\r\n    }\r\n    findWidgetInstanceByName(name) {\r\n        return this._findWidgetInstance((item => name === item.name))\r\n    }\r\n    findWidgetInstanceByDataField(dataField) {\r\n        return this._findWidgetInstance((item => dataField === (isString(item) ? item : item.dataField)))\r\n    }\r\n    findItemContainerByItem(item) {\r\n        for (const key in this._map) {\r\n            if (this._map[key].item === item) {\r\n                return this._map[key].$itemContainer\r\n            }\r\n        }\r\n        return null\r\n    }\r\n    findItemIndexByItem(targetItem) {\r\n        return this._findFieldByCondition((_ref4 => {\r\n            let {\r\n                item: item\r\n            } = _ref4;\r\n            return item === targetItem\r\n        }), \"itemIndex\")\r\n    }\r\n    findPreparedItemByItem(item) {\r\n        return this._findFieldByCondition((_ref5 => {\r\n            let {\r\n                item: currentItem\r\n            } = _ref5;\r\n            return currentItem === item\r\n        }), \"preparedItem\")\r\n    }\r\n    getItems() {\r\n        return this._map\r\n    }\r\n    each(handler) {\r\n        each(this._map, ((key, itemRunTimeInfo) => {\r\n            handler(key, itemRunTimeInfo)\r\n        }))\r\n    }\r\n    removeItemsByPathStartWith(path) {\r\n        const keys = Object.keys(this._map);\r\n        const filteredKeys = keys.filter((key => {\r\n            if (this._map[key].path) {\r\n                return this._map[key].path.indexOf(path, 0) > -1\r\n            }\r\n            return false\r\n        }));\r\n        filteredKeys.forEach((key => this.removeItemByKey(key)))\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;AAGe,MAAM;IACjB,aAAc;QACV,IAAI,CAAC,IAAI,GAAG,CAAC;IACjB;IACA,oBAAoB,SAAS,EAAE;QAC3B,IAAI;QACJ,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAG,CAAC,MAAM;YACpB,IAAI,EACA,gBAAgB,cAAc,EAC9B,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,UAAU,OAAO;gBACjB,SAAS;gBACT,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,sBAAsB,QAAQ,EAAE,SAAS,EAAE;QACvC,IAAI;QACJ,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAG,CAAC,KAAK;YACnB,IAAI,SAAS,QAAQ;gBACjB,SAAS,WAAW,YAAY,MAAM,KAAK,CAAC,UAAU;gBACtD,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,QAAQ;QACJ,IAAI,CAAC,IAAI,GAAG,CAAC;IACjB;IACA,mBAAmB,gBAAgB,EAAE;QACjC,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,QAAQ,IAAK,CAAA,OAAQ,IAAI,CAAC,eAAe,CAAC;IACpE;IACA,gBAAgB,GAAG,EAAE;QACjB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACzB;IACA,IAAI,OAAO,EAAE;QACT,MAAM,MAAM,QAAQ,IAAI,IAAI,IAAI,iJAAA,CAAA,UAAI;QACpC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;QACjB,OAAO;IACX;IACA,qBAAqB,gBAAgB,EAAE;QACnC,iBAAiB,IAAI,CAAE,CAAC,KAAK;YACzB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBAChB,IAAI,gBAAgB,cAAc,EAAE;oBAChC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,gBAAgB,cAAc;gBAClE;gBACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc,GAAG,gBAAgB,cAAc;YAClE,OAAO;gBACH,IAAI,CAAC,GAAG,CAAC;oBACL,MAAM,gBAAgB,IAAI;oBAC1B,gBAAgB,gBAAgB,cAAc;oBAC9C,MAAM;oBACN,gBAAgB,gBAAgB,cAAc;gBAClD;YACJ;QACJ;IACJ;IACA,2BAA2B,GAAG,EAAE,OAAO,EAAE;QACrC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;YAChB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QAC5C;IACJ;IACA,yBAAyB,IAAI,EAAE;QAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAE,CAAA,aAAc,eAAe;IAClE;IACA,kCAAkC,UAAU,EAAE;QAC1C,OAAO,IAAI,CAAC,qBAAqB,CAAE,CAAA;YAC/B,IAAI,EACA,MAAM,IAAI,EACb,GAAG;YACJ,OAAO,SAAS;QACpB,GAAI;IACR;IACA,cAAc,UAAU,EAAE;QACtB,OAAO,IAAI,CAAC,qBAAqB,CAAE,CAAA;YAC/B,IAAI,EACA,MAAM,IAAI,EACb,GAAG;YACJ,OAAO,SAAS;QACpB,GAAI;IACR;IACA,yBAAyB,IAAI,EAAE;QAC3B,OAAO,IAAI,CAAC,mBAAmB,CAAE,CAAA,OAAQ,SAAS,KAAK,IAAI;IAC/D;IACA,8BAA8B,SAAS,EAAE;QACrC,OAAO,IAAI,CAAC,mBAAmB,CAAE,CAAA,OAAQ,cAAc,CAAC,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ,OAAO,KAAK,SAAS;IAClG;IACA,wBAAwB,IAAI,EAAE;QAC1B,IAAK,MAAM,OAAO,IAAI,CAAC,IAAI,CAAE;YACzB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK,MAAM;gBAC9B,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,cAAc;YACxC;QACJ;QACA,OAAO;IACX;IACA,oBAAoB,UAAU,EAAE;QAC5B,OAAO,IAAI,CAAC,qBAAqB,CAAE,CAAA;YAC/B,IAAI,EACA,MAAM,IAAI,EACb,GAAG;YACJ,OAAO,SAAS;QACpB,GAAI;IACR;IACA,uBAAuB,IAAI,EAAE;QACzB,OAAO,IAAI,CAAC,qBAAqB,CAAE,CAAA;YAC/B,IAAI,EACA,MAAM,WAAW,EACpB,GAAG;YACJ,OAAO,gBAAgB;QAC3B,GAAI;IACR;IACA,WAAW;QACP,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,KAAK,OAAO,EAAE;QACV,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAG,CAAC,KAAK;YACnB,QAAQ,KAAK;QACjB;IACJ;IACA,2BAA2B,IAAI,EAAE;QAC7B,MAAM,OAAO,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QAClC,MAAM,eAAe,KAAK,MAAM,CAAE,CAAA;YAC9B,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;gBACrB,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC;YACnD;YACA,OAAO;QACX;QACA,aAAa,OAAO,CAAE,CAAA,MAAO,IAAI,CAAC,eAAe,CAAC;IACtD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.layout_manager.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.layout_manager.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport \"../../../ui/text_box\";\r\nimport \"../../../ui/number_box\";\r\nimport \"../../../ui/check_box\";\r\nimport \"../../../ui/date_box\";\r\nimport \"../../../ui/button\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    removeEvent\r\n} from \"../../../common/core/events/remove\";\r\nimport messageLocalization from \"../../../common/core/localization/message\";\r\nimport registerComponent from \"../../../core/component_registrator\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    normalizeIndexes\r\n} from \"../../../core/utils/array\";\r\nimport {\r\n    compileGetter\r\n} from \"../../../core/utils/data\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getWidth\r\n} from \"../../../core/utils/size\";\r\nimport {\r\n    isDefined,\r\n    isEmptyObject,\r\n    isFunction,\r\n    isObject,\r\n    type\r\n} from \"../../../core/utils/type\";\r\nimport variableWrapper from \"../../../core/utils/variable_wrapper\";\r\nimport {\r\n    getCurrentScreenFactor,\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport Widget from \"../../core/widget/widget\";\r\nimport ResponsiveBox from \"../../ui/m_responsive_box\";\r\nimport {\r\n    renderButtonItem\r\n} from \"./components/m_button_item\";\r\nimport {\r\n    renderEmptyItem\r\n} from \"./components/m_empty_item\";\r\nimport {\r\n    renderFieldItem\r\n} from \"./components/m_field_item\";\r\nimport {\r\n    FIELD_ITEM_CLASS,\r\n    FORM_LAYOUT_MANAGER_CLASS,\r\n    LAYOUT_MANAGER_ONE_COLUMN,\r\n    ROOT_SIMPLE_ITEM_CLASS,\r\n    SIMPLE_ITEM_TYPE,\r\n    SINGLE_COLUMN_ITEM_CONTENT\r\n} from \"./constants\";\r\nimport {\r\n    default as FormItemsRunTimeInfo\r\n} from \"./m_form.items_runtime_info\";\r\nimport {\r\n    convertToRenderFieldItemOptions\r\n} from \"./m_form.layout_manager.utils\";\r\nconst FORM_EDITOR_BY_DEFAULT = \"dxTextBox\";\r\nconst LAYOUT_MANAGER_FIRST_ROW_CLASS = \"dx-first-row\";\r\nconst LAYOUT_MANAGER_LAST_ROW_CLASS = \"dx-last-row\";\r\nconst LAYOUT_MANAGER_FIRST_COL_CLASS = \"dx-first-col\";\r\nconst LAYOUT_MANAGER_LAST_COL_CLASS = \"dx-last-col\";\r\nclass LayoutManager extends Widget {\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            layoutData: {},\r\n            readOnly: false,\r\n            colCount: 1,\r\n            labelLocation: \"left\",\r\n            onFieldDataChanged: null,\r\n            onEditorEnterKey: null,\r\n            customizeItem: null,\r\n            alignItemLabels: true,\r\n            minColWidth: 200,\r\n            showRequiredMark: true,\r\n            screenByWidth: null,\r\n            showOptionalMark: false,\r\n            requiredMark: \"*\",\r\n            labelMode: \"outside\",\r\n            optionalMark: messageLocalization.format(\"dxForm-optionalMark\"),\r\n            requiredMessage: messageLocalization.getFormatter(\"dxForm-requiredMessage\")\r\n        })\r\n    }\r\n    _setOptionsByReference() {\r\n        super._setOptionsByReference();\r\n        extend(this._optionsByReference, {\r\n            layoutData: true,\r\n            validationGroup: true\r\n        })\r\n    }\r\n    _init() {\r\n        const layoutData = this.option(\"layoutData\");\r\n        super._init();\r\n        this._itemWatchers = [];\r\n        this._itemsRunTimeInfo = new FormItemsRunTimeInfo;\r\n        this._updateReferencedOptions(layoutData);\r\n        this._initDataAndItems(layoutData)\r\n    }\r\n    _dispose() {\r\n        super._dispose();\r\n        this._cleanItemWatchers()\r\n    }\r\n    _initDataAndItems(initialData) {\r\n        this._syncDataWithItems();\r\n        this._updateItems(initialData)\r\n    }\r\n    _syncDataWithItems() {\r\n        const layoutData = this.option(\"layoutData\");\r\n        const userItems = this.option(\"items\");\r\n        if (isDefined(userItems)) {\r\n            userItems.forEach((item => {\r\n                if (item.dataField && void 0 === this._getDataByField(item.dataField)) {\r\n                    let value;\r\n                    if (item.editorOptions) {\r\n                        value = item.editorOptions.value\r\n                    }\r\n                    if (isDefined(value) || item.dataField in layoutData) {\r\n                        this._updateFieldValue(item.dataField, value)\r\n                    }\r\n                }\r\n            }))\r\n        }\r\n    }\r\n    _getDataByField(dataField) {\r\n        return dataField ? this.option(`layoutData.${dataField}`) : null\r\n    }\r\n    _isCheckboxUndefinedStateEnabled(_ref) {\r\n        let {\r\n            allowIndeterminateState: allowIndeterminateState,\r\n            editorType: editorType,\r\n            dataField: dataField\r\n        } = _ref;\r\n        if (true === allowIndeterminateState && \"dxCheckBox\" === editorType) {\r\n            const nameParts = [\"layoutData\", ...dataField.split(\".\")];\r\n            const propertyName = nameParts.pop();\r\n            const layoutData = this.option(nameParts.join(\".\"));\r\n            return layoutData && propertyName in layoutData\r\n        }\r\n        return false\r\n    }\r\n    _updateFieldValue(dataField, value) {\r\n        const layoutData = this.option(\"layoutData\");\r\n        let newValue = value;\r\n        if (!variableWrapper.isWrapped(layoutData[dataField]) && isDefined(dataField)) {\r\n            this.option(`layoutData.${dataField}`, newValue)\r\n        } else if (variableWrapper.isWritableWrapped(layoutData[dataField])) {\r\n            newValue = isFunction(newValue) ? newValue() : newValue;\r\n            layoutData[dataField](newValue)\r\n        }\r\n        this._triggerOnFieldDataChanged({\r\n            dataField: dataField,\r\n            value: newValue\r\n        })\r\n    }\r\n    _triggerOnFieldDataChanged(args) {\r\n        this._createActionByOption(\"onFieldDataChanged\")(args)\r\n    }\r\n    _updateItems(layoutData) {\r\n        const that = this;\r\n        const userItems = this.option(\"items\");\r\n        const isUserItemsExist = isDefined(userItems);\r\n        const {\r\n            customizeItem: customizeItem\r\n        } = this.option();\r\n        const items = isUserItemsExist ? userItems : this._generateItemsByData(layoutData);\r\n        if (isDefined(items)) {\r\n            const processedItems = [];\r\n            each(items, ((index, item) => {\r\n                if (that._isAcceptableItem(item)) {\r\n                    item = that._processItem(item);\r\n                    customizeItem && customizeItem(item);\r\n                    if (isObject(item) && false !== variableWrapper.unwrap(item.visible)) {\r\n                        processedItems.push(item)\r\n                    }\r\n                }\r\n            }));\r\n            if (!that._itemWatchers.length || !isUserItemsExist) {\r\n                that._updateItemWatchers(items)\r\n            }\r\n            this._setItems(processedItems);\r\n            this._sortItems()\r\n        }\r\n    }\r\n    _cleanItemWatchers() {\r\n        this._itemWatchers.forEach((dispose => {\r\n            dispose()\r\n        }));\r\n        this._itemWatchers = []\r\n    }\r\n    _updateItemWatchers(items) {\r\n        const that = this;\r\n        const watch = that._getWatch();\r\n        items.forEach((item => {\r\n            if (isObject(item) && isDefined(item.visible) && isFunction(watch)) {\r\n                that._itemWatchers.push(watch((() => variableWrapper.unwrap(item.visible)), (() => {\r\n                    that._updateItems(that.option(\"layoutData\"));\r\n                    that.repaint()\r\n                }), {\r\n                    skipImmediate: true\r\n                }))\r\n            }\r\n        }))\r\n    }\r\n    _generateItemsByData(layoutData) {\r\n        const result = [];\r\n        if (isDefined(layoutData)) {\r\n            each(layoutData, (dataField => {\r\n                result.push({\r\n                    dataField: dataField\r\n                })\r\n            }))\r\n        }\r\n        return result\r\n    }\r\n    _isAcceptableItem(item) {\r\n        const itemField = item.dataField || item;\r\n        const itemData = this._getDataByField(itemField);\r\n        return !(isFunction(itemData) && !variableWrapper.isWrapped(itemData))\r\n    }\r\n    _processItem(item) {\r\n        if (\"string\" === typeof item) {\r\n            item = {\r\n                dataField: item\r\n            }\r\n        }\r\n        if (\"object\" === typeof item && !item.itemType) {\r\n            item.itemType = SIMPLE_ITEM_TYPE\r\n        }\r\n        if (!isDefined(item.editorType) && isDefined(item.dataField)) {\r\n            const value = this._getDataByField(item.dataField);\r\n            item.editorType = isDefined(value) ? this._getEditorTypeByDataType(type(value)) : \"dxTextBox\"\r\n        }\r\n        if (\"dxCheckBox\" === item.editorType) {\r\n            item.allowIndeterminateState = item.allowIndeterminateState ?? true\r\n        }\r\n        return item\r\n    }\r\n    _getEditorTypeByDataType(dataType) {\r\n        switch (dataType) {\r\n            case \"number\":\r\n                return \"dxNumberBox\";\r\n            case \"date\":\r\n                return \"dxDateBox\";\r\n            case \"boolean\":\r\n                return \"dxCheckBox\";\r\n            default:\r\n                return \"dxTextBox\"\r\n        }\r\n    }\r\n    _sortItems() {\r\n        normalizeIndexes(this._items, \"visibleIndex\");\r\n        this._sortIndexes()\r\n    }\r\n    _sortIndexes() {\r\n        this._items.sort(((itemA, itemB) => {\r\n            const indexA = itemA.visibleIndex;\r\n            const indexB = itemB.visibleIndex;\r\n            let result;\r\n            if (indexA > indexB) {\r\n                result = 1\r\n            } else if (indexA < indexB) {\r\n                result = -1\r\n            } else {\r\n                result = 0\r\n            }\r\n            return result\r\n        }))\r\n    }\r\n    _initMarkup() {\r\n        this._itemsRunTimeInfo.clear();\r\n        this.$element().addClass(FORM_LAYOUT_MANAGER_CLASS);\r\n        super._initMarkup();\r\n        this._renderResponsiveBox()\r\n    }\r\n    _renderResponsiveBox() {\r\n        const that = this;\r\n        const templatesInfo = [];\r\n        if (that._items && that._items.length) {\r\n            const colCount = that._getColCount();\r\n            const $container = $(\"<div>\").appendTo(that.$element());\r\n            that._prepareItemsWithMerging(colCount);\r\n            const layoutItems = that._generateLayoutItems();\r\n            that._responsiveBox = that._createComponent($container, ResponsiveBox, that._getResponsiveBoxConfig(layoutItems, colCount, templatesInfo));\r\n            if (!hasWindow()) {\r\n                that._renderTemplates(templatesInfo)\r\n            }\r\n        }\r\n    }\r\n    _itemStateChangedHandler(e) {\r\n        this._refresh()\r\n    }\r\n    _renderTemplates(templatesInfo) {\r\n        const that = this;\r\n        let itemsWithLabelTemplateCount = 0;\r\n        templatesInfo.forEach((_ref2 => {\r\n            var _item$label;\r\n            let {\r\n                item: item\r\n            } = _ref2;\r\n            if (null !== item && void 0 !== item && null !== (_item$label = item.label) && void 0 !== _item$label && _item$label.template) {\r\n                itemsWithLabelTemplateCount++\r\n            }\r\n        }));\r\n        each(templatesInfo, ((index, info) => {\r\n            switch (info.itemType) {\r\n                case \"empty\":\r\n                    renderEmptyItem(info);\r\n                    break;\r\n                case \"button\":\r\n                    that._renderButtonItem(info);\r\n                    break;\r\n                default:\r\n                    that._renderFieldItem(info, itemsWithLabelTemplateCount)\r\n            }\r\n        }))\r\n    }\r\n    _getResponsiveBoxConfig(layoutItems, colCount, templatesInfo) {\r\n        const that = this;\r\n        const colCountByScreen = that.option(\"colCountByScreen\");\r\n        const xsColCount = colCountByScreen && colCountByScreen.xs;\r\n        return {\r\n            onItemStateChanged: this._itemStateChangedHandler.bind(this),\r\n            onLayoutChanged() {\r\n                const {\r\n                    onLayoutChanged: onLayoutChanged\r\n                } = that.option();\r\n                const isSingleColumnMode = that.isSingleColumnMode();\r\n                if (onLayoutChanged) {\r\n                    that.$element().toggleClass(LAYOUT_MANAGER_ONE_COLUMN, isSingleColumnMode);\r\n                    onLayoutChanged(isSingleColumnMode)\r\n                }\r\n            },\r\n            onContentReady(e) {\r\n                if (hasWindow()) {\r\n                    that._renderTemplates(templatesInfo)\r\n                }\r\n                if (that.option(\"onLayoutChanged\")) {\r\n                    that.$element().toggleClass(LAYOUT_MANAGER_ONE_COLUMN, that.isSingleColumnMode(e.component))\r\n                }\r\n            },\r\n            itemTemplate(e, itemData, itemElement) {\r\n                if (!e.location) {\r\n                    return\r\n                }\r\n                const $itemElement = $(itemElement);\r\n                const itemRenderedCountInPreviousRows = e.location.row * colCount;\r\n                const item = that._items[e.location.col + itemRenderedCountInPreviousRows];\r\n                if (!item) {\r\n                    return\r\n                }\r\n                const itemCssClassList = [item.cssClass];\r\n                $itemElement.toggleClass(SINGLE_COLUMN_ITEM_CONTENT, that.isSingleColumnMode(this));\r\n                if (0 === e.location.row) {\r\n                    itemCssClassList.push(\"dx-first-row\")\r\n                }\r\n                if (0 === e.location.col) {\r\n                    itemCssClassList.push(\"dx-first-col\")\r\n                }\r\n                if (item.itemType === SIMPLE_ITEM_TYPE && that.option(\"isRoot\")) {\r\n                    $itemElement.addClass(ROOT_SIMPLE_ITEM_CLASS)\r\n                }\r\n                const isLastColumn = e.location.col === colCount - 1 || e.location.col + e.location.colspan === colCount;\r\n                const rowsCount = that._getRowsCount();\r\n                const isLastRow = e.location.row === rowsCount - 1;\r\n                if (isLastColumn) {\r\n                    itemCssClassList.push(\"dx-last-col\")\r\n                }\r\n                if (isLastRow) {\r\n                    itemCssClassList.push(\"dx-last-row\")\r\n                }\r\n                if (\"empty\" !== item.itemType) {\r\n                    itemCssClassList.push(FIELD_ITEM_CLASS);\r\n                    itemCssClassList.push(that.option(\"cssItemClass\"));\r\n                    if (isDefined(item.col)) {\r\n                        itemCssClassList.push(`dx-col-${item.col}`)\r\n                    }\r\n                }\r\n                templatesInfo.push({\r\n                    itemType: item.itemType,\r\n                    item: item,\r\n                    $parent: $itemElement,\r\n                    rootElementCssClassList: itemCssClassList\r\n                })\r\n            },\r\n            cols: that._generateRatio(colCount),\r\n            rows: that._generateRatio(that._getRowsCount(), true),\r\n            dataSource: layoutItems,\r\n            screenByWidth: that.option(\"screenByWidth\"),\r\n            singleColumnScreen: xsColCount ? false : \"xs\"\r\n        }\r\n    }\r\n    _getColCount() {\r\n        let {\r\n            colCount: colCount\r\n        } = this.option();\r\n        const colCountByScreen = this.option(\"colCountByScreen\");\r\n        if (colCountByScreen) {\r\n            const {\r\n                form: form\r\n            } = this.option();\r\n            let screenFactor = null === form || void 0 === form ? void 0 : form.getTargetScreenFactor();\r\n            if (!screenFactor) {\r\n                screenFactor = hasWindow() ? getCurrentScreenFactor(this.option(\"screenByWidth\")) : \"lg\"\r\n            }\r\n            colCount = colCountByScreen[screenFactor] || colCount\r\n        }\r\n        if (\"auto\" === colCount) {\r\n            if (this._cashedColCount) {\r\n                return this._cashedColCount\r\n            }\r\n            this._cashedColCount = colCount = this._getMaxColCount()\r\n        }\r\n        return colCount < 1 ? 1 : colCount\r\n    }\r\n    _getMaxColCount() {\r\n        if (!hasWindow()) {\r\n            return 1\r\n        }\r\n        const minColWidth = this.option(\"minColWidth\");\r\n        const width = getWidth(this.$element());\r\n        const itemsCount = this._items.length;\r\n        const maxColCount = Math.floor(width / minColWidth) || 1;\r\n        return itemsCount < maxColCount ? itemsCount : maxColCount\r\n    }\r\n    isCachedColCountObsolete() {\r\n        return this._cashedColCount && this._getMaxColCount() !== this._cashedColCount\r\n    }\r\n    _prepareItemsWithMerging(colCount) {\r\n        const items = this._items.slice(0);\r\n        let item;\r\n        let itemsMergedByCol;\r\n        let result = [];\r\n        let j;\r\n        let i;\r\n        for (i = 0; i < items.length; i++) {\r\n            item = items[i];\r\n            result.push(item);\r\n            if (this.option(\"alignItemLabels\") || item.alignItemLabels || item.colSpan) {\r\n                item.col = this._getColByIndex(result.length - 1, colCount)\r\n            }\r\n            if (item.colSpan > 1 && item.col + item.colSpan <= colCount) {\r\n                itemsMergedByCol = [];\r\n                for (j = 0; j < item.colSpan - 1; j++) {\r\n                    itemsMergedByCol.push({\r\n                        merged: true\r\n                    })\r\n                }\r\n                result = result.concat(itemsMergedByCol)\r\n            } else {\r\n                delete item.colSpan\r\n            }\r\n        }\r\n        this._setItems(result)\r\n    }\r\n    _getColByIndex(index, colCount) {\r\n        return index % colCount\r\n    }\r\n    _setItems(items) {\r\n        this._items = items;\r\n        this._cashedColCount = null\r\n    }\r\n    _generateLayoutItems() {\r\n        const items = this._items;\r\n        const colCount = this._getColCount();\r\n        const result = [];\r\n        let item;\r\n        let i;\r\n        for (i = 0; i < items.length; i++) {\r\n            item = items[i];\r\n            if (!item.merged) {\r\n                const generatedItem = {\r\n                    location: {\r\n                        row: parseInt(i / colCount),\r\n                        col: this._getColByIndex(i, colCount)\r\n                    }\r\n                };\r\n                if (isDefined(item.disabled)) {\r\n                    generatedItem.disabled = item.disabled\r\n                }\r\n                if (isDefined(item.visible)) {\r\n                    generatedItem.visible = item.visible\r\n                }\r\n                if (isDefined(item.colSpan)) {\r\n                    generatedItem.location.colspan = item.colSpan\r\n                }\r\n                if (isDefined(item.rowSpan)) {\r\n                    generatedItem.location.rowspan = item.rowSpan\r\n                }\r\n                result.push(generatedItem)\r\n            }\r\n        }\r\n        return result\r\n    }\r\n    _renderEmptyItem($container) {\r\n        renderEmptyItem({\r\n            $container: $container\r\n        })\r\n    }\r\n    _renderButtonItem(_ref3) {\r\n        let {\r\n            item: item,\r\n            $parent: $parent,\r\n            rootElementCssClassList: rootElementCssClassList\r\n        } = _ref3;\r\n        const {\r\n            $rootElement: $rootElement,\r\n            buttonInstance: buttonInstance\r\n        } = renderButtonItem({\r\n            item: item,\r\n            $parent: $parent,\r\n            rootElementCssClassList: rootElementCssClassList,\r\n            validationGroup: this.option(\"validationGroup\"),\r\n            createComponentCallback: this._createComponent.bind(this)\r\n        });\r\n        this._itemsRunTimeInfo.add({\r\n            item: item,\r\n            widgetInstance: buttonInstance,\r\n            guid: item.guid,\r\n            $itemContainer: $rootElement\r\n        })\r\n    }\r\n    _renderFieldItem(_ref4, itemsWithLabelTemplateCount) {\r\n        var _item$label2;\r\n        let {\r\n            item: item,\r\n            $parent: $parent,\r\n            rootElementCssClassList: rootElementCssClassList\r\n        } = _ref4;\r\n        const editorValue = this._getDataByField(item.dataField);\r\n        let canAssignUndefinedValueToEditor = false;\r\n        if (void 0 === editorValue) {\r\n            const {\r\n                allowIndeterminateState: allowIndeterminateState,\r\n                editorType: editorType,\r\n                dataField: dataField\r\n            } = item;\r\n            canAssignUndefinedValueToEditor = this._isCheckboxUndefinedStateEnabled({\r\n                allowIndeterminateState: allowIndeterminateState,\r\n                editorType: editorType,\r\n                dataField: dataField\r\n            })\r\n        }\r\n        const name = item.dataField || item.name;\r\n        const formOrLayoutManager = this._getFormOrThis();\r\n        const {\r\n            form: form\r\n        } = this.option();\r\n        const {\r\n            $fieldEditorContainer: $fieldEditorContainer,\r\n            widgetInstance: widgetInstance,\r\n            $rootElement: $rootElement\r\n        } = renderFieldItem(convertToRenderFieldItemOptions({\r\n            $parent: $parent,\r\n            rootElementCssClassList: rootElementCssClassList,\r\n            item: item,\r\n            name: name,\r\n            editorValue: editorValue,\r\n            canAssignUndefinedValueToEditor: canAssignUndefinedValueToEditor,\r\n            formOrLayoutManager: this._getFormOrThis(),\r\n            createComponentCallback: this._createComponent.bind(this),\r\n            formLabelLocation: this.option(\"labelLocation\"),\r\n            requiredMessageTemplate: this.option(\"requiredMessage\"),\r\n            validationGroup: this.option(\"validationGroup\"),\r\n            editorValidationBoundary: this.option(\"validationBoundary\"),\r\n            editorStylingMode: null === form || void 0 === form ? void 0 : form.option(\"stylingMode\"),\r\n            showColonAfterLabel: this.option(\"showColonAfterLabel\"),\r\n            managerLabelLocation: this.option(\"labelLocation\"),\r\n            template: item.template ? this._getTemplate(item.template) : null,\r\n            labelTemplate: null !== (_item$label2 = item.label) && void 0 !== _item$label2 && _item$label2.template ? this._getTemplate(item.label.template) : null,\r\n            itemId: null === form || void 0 === form ? void 0 : form.getItemID(name),\r\n            managerMarkOptions: this._getMarkOptions(),\r\n            labelMode: this.option(\"labelMode\"),\r\n            onLabelTemplateRendered: () => {\r\n                this._incTemplateRenderedCallCount();\r\n                if (this._shouldAlignLabelsOnTemplateRendered(formOrLayoutManager, itemsWithLabelTemplateCount)) {\r\n                    formOrLayoutManager._alignLabels(this, this.isSingleColumnMode(formOrLayoutManager))\r\n                }\r\n            }\r\n        }));\r\n        const {\r\n            onFieldItemRendered: onFieldItemRendered\r\n        } = this.option();\r\n        null === onFieldItemRendered || void 0 === onFieldItemRendered || onFieldItemRendered();\r\n        if (widgetInstance && item.dataField) {\r\n            this._bindDataField(widgetInstance, item.dataField, item.editorType, $fieldEditorContainer)\r\n        }\r\n        this._itemsRunTimeInfo.add({\r\n            item: item,\r\n            widgetInstance: widgetInstance,\r\n            guid: item.guid,\r\n            $itemContainer: $rootElement\r\n        })\r\n    }\r\n    _incTemplateRenderedCallCount() {\r\n        this._labelTemplateRenderedCallCount = (this._labelTemplateRenderedCallCount ?? 0) + 1\r\n    }\r\n    _shouldAlignLabelsOnTemplateRendered(formOrLayoutManager, totalItemsWithLabelTemplate) {\r\n        return formOrLayoutManager.option(\"templatesRenderAsynchronously\") && this._labelTemplateRenderedCallCount === totalItemsWithLabelTemplate\r\n    }\r\n    _getMarkOptions() {\r\n        return {\r\n            showRequiredMark: this.option(\"showRequiredMark\"),\r\n            requiredMark: this.option(\"requiredMark\"),\r\n            showOptionalMark: this.option(\"showOptionalMark\"),\r\n            optionalMark: this.option(\"optionalMark\")\r\n        }\r\n    }\r\n    _getFormOrThis() {\r\n        const {\r\n            form: form\r\n        } = this.option();\r\n        return form || this\r\n    }\r\n    _bindDataField(editorInstance, dataField, editorType, $container) {\r\n        const formOrThis = this._getFormOrThis();\r\n        editorInstance.on(\"enterKey\", (args => {\r\n            formOrThis._createActionByOption(\"onEditorEnterKey\")(extend(args, {\r\n                dataField: dataField\r\n            }))\r\n        }));\r\n        this._createWatcher(editorInstance, $container, dataField);\r\n        this.linkEditorToDataField(editorInstance, dataField)\r\n    }\r\n    _createWatcher(editorInstance, $container, dataField) {\r\n        const that = this;\r\n        const watch = that._getWatch();\r\n        if (!isFunction(watch)) {\r\n            return\r\n        }\r\n        const dispose = watch((() => that._getDataByField(dataField)), (() => {\r\n            const fieldValue = that._getDataByField(dataField);\r\n            if (\"dxTagBox\" === editorInstance.NAME) {\r\n                const editorValue = editorInstance.option(\"value\");\r\n                if (fieldValue !== editorValue && function(array1, array2) {\r\n                        if (!Array.isArray(array1) || !Array.isArray(array2) || array1.length !== array2.length) {\r\n                            return false\r\n                        }\r\n                        for (let i = 0; i < array1.length; i++) {\r\n                            if (array1[i] !== array2[i]) {\r\n                                return false\r\n                            }\r\n                        }\r\n                        return true\r\n                    }(fieldValue, editorValue)) {\r\n                    return\r\n                }\r\n            }\r\n            editorInstance.option(\"value\", fieldValue)\r\n        }), {\r\n            deep: true,\r\n            skipImmediate: true\r\n        }, {\r\n            createWatcherDataField: dataField\r\n        });\r\n        eventsEngine.on($container, removeEvent, dispose)\r\n    }\r\n    _getWatch() {\r\n        if (!isDefined(this._watch)) {\r\n            const {\r\n                form: formInstance\r\n            } = this.option();\r\n            this._watch = formInstance && formInstance.option(\"integrationOptions.watchMethod\")\r\n        }\r\n        return this._watch\r\n    }\r\n    _createComponent($editor, type, editorOptions) {\r\n        const readOnlyState = this.option(\"readOnly\");\r\n        let hasEditorReadOnly = Object.hasOwn(editorOptions, \"readOnly\");\r\n        const instance = super._createComponent($editor, type, _extends({}, editorOptions, {\r\n            readOnly: !hasEditorReadOnly ? readOnlyState : editorOptions.readOnly\r\n        }));\r\n        let isChangeByForm = false;\r\n        instance.on(\"optionChanged\", (args => {\r\n            if (\"readOnly\" === args.name && !isChangeByForm) {\r\n                hasEditorReadOnly = true\r\n            }\r\n        }));\r\n        this.on(\"optionChanged\", (args => {\r\n            if (\"readOnly\" === args.name && !hasEditorReadOnly) {\r\n                isChangeByForm = true;\r\n                instance.option(args.name, args.value);\r\n                isChangeByForm = false\r\n            }\r\n        }));\r\n        return instance\r\n    }\r\n    _generateRatio(count, isAutoSize) {\r\n        const result = [];\r\n        let ratio;\r\n        let i;\r\n        for (i = 0; i < count; i++) {\r\n            ratio = {\r\n                ratio: 1\r\n            };\r\n            if (isAutoSize) {\r\n                ratio.baseSize = \"auto\"\r\n            }\r\n            result.push(ratio)\r\n        }\r\n        return result\r\n    }\r\n    _getRowsCount() {\r\n        return Math.ceil(this._items.length / this._getColCount())\r\n    }\r\n    _updateReferencedOptions(newLayoutData) {\r\n        const layoutData = this.option(\"layoutData\");\r\n        if (isObject(layoutData)) {\r\n            Object.getOwnPropertyNames(layoutData).forEach((property => delete this._optionsByReference[`layoutData.${property}`]))\r\n        }\r\n        if (isObject(newLayoutData)) {\r\n            Object.getOwnPropertyNames(newLayoutData).forEach((property => this._optionsByReference[`layoutData.${property}`] = true))\r\n        }\r\n    }\r\n    _clearWidget(instance) {\r\n        this._disableEditorValueChangedHandler = true;\r\n        instance.clear();\r\n        this._disableEditorValueChangedHandler = false;\r\n        instance.option(\"isValid\", true)\r\n    }\r\n    _optionChanged(args) {\r\n        if (0 === args.fullName.search(\"layoutData.\")) {\r\n            return\r\n        }\r\n        switch (args.name) {\r\n            case \"showRequiredMark\":\r\n            case \"showOptionalMark\":\r\n            case \"requiredMark\":\r\n            case \"optionalMark\":\r\n            case \"alignItemLabels\":\r\n            case \"labelLocation\":\r\n            case \"labelMode\":\r\n            case \"requiredMessage\":\r\n                this._invalidate();\r\n                break;\r\n            case \"layoutData\":\r\n                this._updateReferencedOptions(args.value);\r\n                if (this.option(\"items\")) {\r\n                    if (!isEmptyObject(args.value)) {\r\n                        this._itemsRunTimeInfo.each(((_, itemRunTimeInfo) => {\r\n                            if (isDefined(itemRunTimeInfo.item)) {\r\n                                const {\r\n                                    dataField: dataField\r\n                                } = itemRunTimeInfo.item;\r\n                                if (dataField && isDefined(itemRunTimeInfo.widgetInstance)) {\r\n                                    const valueGetter = compileGetter(dataField);\r\n                                    const dataValue = valueGetter(args.value);\r\n                                    const {\r\n                                        allowIndeterminateState: allowIndeterminateState,\r\n                                        editorType: editorType\r\n                                    } = itemRunTimeInfo.item;\r\n                                    if (void 0 !== dataValue || this._isCheckboxUndefinedStateEnabled({\r\n                                            allowIndeterminateState: allowIndeterminateState,\r\n                                            editorType: editorType,\r\n                                            dataField: dataField\r\n                                        })) {\r\n                                        itemRunTimeInfo.widgetInstance.option(\"value\", dataValue)\r\n                                    } else {\r\n                                        this._clearWidget(itemRunTimeInfo.widgetInstance)\r\n                                    }\r\n                                }\r\n                            }\r\n                        }))\r\n                    }\r\n                } else {\r\n                    this._initDataAndItems(args.value);\r\n                    this._invalidate()\r\n                }\r\n                break;\r\n            case \"items\":\r\n                this._cleanItemWatchers();\r\n                this._initDataAndItems(args.value);\r\n                this._invalidate();\r\n                break;\r\n            case \"customizeItem\":\r\n                this._updateItems(this.option(\"layoutData\"));\r\n                this._invalidate();\r\n                break;\r\n            case \"colCount\":\r\n            case \"colCountByScreen\":\r\n                this._resetColCount();\r\n                break;\r\n            case \"minColWidth\": {\r\n                const {\r\n                    colCount: colCount\r\n                } = this.option();\r\n                if (\"auto\" === colCount) {\r\n                    this._resetColCount()\r\n                }\r\n                break\r\n            }\r\n            case \"readOnly\":\r\n            case \"onFieldDataChanged\":\r\n                break;\r\n            case \"width\": {\r\n                super._optionChanged(args);\r\n                const {\r\n                    colCount: colCount\r\n                } = this.option();\r\n                if (\"auto\" === colCount) {\r\n                    this._resetColCount()\r\n                }\r\n                break\r\n            }\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    _resetColCount() {\r\n        this._cashedColCount = null;\r\n        this._invalidate()\r\n    }\r\n    linkEditorToDataField(editorInstance, dataField) {\r\n        this.on(\"optionChanged\", (args => {\r\n            if (args.fullName === `layoutData.${dataField}`) {\r\n                editorInstance._setOptionWithoutOptionChange(\"value\", args.value)\r\n            }\r\n        }));\r\n        editorInstance.on(\"valueChanged\", (args => {\r\n            const isValueReferenceType = isObject(args.value) || Array.isArray(args.value);\r\n            if (!this._disableEditorValueChangedHandler && !(isValueReferenceType && args.value === args.previousValue)) {\r\n                this._updateFieldValue(dataField, args.value)\r\n            }\r\n        }))\r\n    }\r\n    _dimensionChanged() {\r\n        const {\r\n            colCount: colCount\r\n        } = this.option();\r\n        if (\"auto\" === colCount && this.isCachedColCountObsolete()) {\r\n            this._eventsStrategy.fireEvent(\"autoColCountChanged\")\r\n        }\r\n    }\r\n    updateData(data, value) {\r\n        const that = this;\r\n        if (isObject(data)) {\r\n            each(data, ((dataField, fieldValue) => {\r\n                that._updateFieldValue(dataField, fieldValue)\r\n            }))\r\n        } else if (\"string\" === typeof data) {\r\n            that._updateFieldValue(data, value)\r\n        }\r\n    }\r\n    getEditor(field) {\r\n        return this._itemsRunTimeInfo.findWidgetInstanceByDataField(field) || this._itemsRunTimeInfo.findWidgetInstanceByName(field)\r\n    }\r\n    isSingleColumnMode(component) {\r\n        const responsiveBox = this._responsiveBox || component;\r\n        if (responsiveBox) {\r\n            return responsiveBox.option(\"currentScreenFactor\") === responsiveBox.option(\"singleColumnScreen\")\r\n        }\r\n    }\r\n    getItemsRunTimeInfo() {\r\n        return this._itemsRunTimeInfo\r\n    }\r\n}\r\nregisterComponent(\"dxLayoutManager\", LayoutManager);\r\nexport default LayoutManager;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAOA;AACA;AAAA;AAIA;AACA;AACA;AAGA;AAGA;AAGA;AAQA;AAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,yBAAyB;AAC/B,MAAM,iCAAiC;AACvC,MAAM,gCAAgC;AACtC,MAAM,iCAAiC;AACvC,MAAM,gCAAgC;AACtC,MAAM,sBAAsB,8KAAA,CAAA,UAAM;IAC9B,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,YAAY,CAAC;YACb,UAAU;YACV,UAAU;YACV,eAAe;YACf,oBAAoB;YACpB,kBAAkB;YAClB,eAAe;YACf,iBAAiB;YACjB,aAAa;YACb,kBAAkB;YAClB,eAAe;YACf,kBAAkB;YAClB,cAAc;YACd,WAAW;YACX,cAAc,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YACzC,iBAAiB,8KAAA,CAAA,UAAmB,CAAC,YAAY,CAAC;QACtD;IACJ;IACA,yBAAyB;QACrB,KAAK,CAAC;QACN,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC7B,YAAY;YACZ,iBAAiB;QACrB;IACJ;IACA,QAAQ;QACJ,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,KAAK,CAAC;QACN,IAAI,CAAC,aAAa,GAAG,EAAE;QACvB,IAAI,CAAC,iBAAiB,GAAG,IAAI,gMAAA,CAAA,UAAoB;QACjD,IAAI,CAAC,wBAAwB,CAAC;QAC9B,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IACA,WAAW;QACP,KAAK,CAAC;QACN,IAAI,CAAC,kBAAkB;IAC3B;IACA,kBAAkB,WAAW,EAAE;QAC3B,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,YAAY,CAAC;IACtB;IACA,qBAAqB;QACjB,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;YACtB,UAAU,OAAO,CAAE,CAAA;gBACf,IAAI,KAAK,SAAS,IAAI,KAAK,MAAM,IAAI,CAAC,eAAe,CAAC,KAAK,SAAS,GAAG;oBACnE,IAAI;oBACJ,IAAI,KAAK,aAAa,EAAE;wBACpB,QAAQ,KAAK,aAAa,CAAC,KAAK;oBACpC;oBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,KAAK,SAAS,IAAI,YAAY;wBAClD,IAAI,CAAC,iBAAiB,CAAC,KAAK,SAAS,EAAE;oBAC3C;gBACJ;YACJ;QACJ;IACJ;IACA,gBAAgB,SAAS,EAAE;QACvB,OAAO,YAAY,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,WAAW,IAAI;IAChE;IACA,iCAAiC,IAAI,EAAE;QACnC,IAAI,EACA,yBAAyB,uBAAuB,EAChD,YAAY,UAAU,EACtB,WAAW,SAAS,EACvB,GAAG;QACJ,IAAI,SAAS,2BAA2B,iBAAiB,YAAY;YACjE,MAAM,YAAY;gBAAC;mBAAiB,UAAU,KAAK,CAAC;aAAK;YACzD,MAAM,eAAe,UAAU,GAAG;YAClC,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,IAAI,CAAC;YAC9C,OAAO,cAAc,gBAAgB;QACzC;QACA,OAAO;IACX;IACA,kBAAkB,SAAS,EAAE,KAAK,EAAE;QAChC,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,WAAW;QACf,IAAI,CAAC,sKAAA,CAAA,UAAe,CAAC,SAAS,CAAC,UAAU,CAAC,UAAU,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY;YAC3E,IAAI,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,WAAW,EAAE;QAC3C,OAAO,IAAI,sKAAA,CAAA,UAAe,CAAC,iBAAiB,CAAC,UAAU,CAAC,UAAU,GAAG;YACjE,WAAW,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,YAAY,aAAa;YAC/C,UAAU,CAAC,UAAU,CAAC;QAC1B;QACA,IAAI,CAAC,0BAA0B,CAAC;YAC5B,WAAW;YACX,OAAO;QACX;IACJ;IACA,2BAA2B,IAAI,EAAE;QAC7B,IAAI,CAAC,qBAAqB,CAAC,sBAAsB;IACrD;IACA,aAAa,UAAU,EAAE;QACrB,MAAM,OAAO,IAAI;QACjB,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC;QAC9B,MAAM,mBAAmB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;QACnC,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,QAAQ,mBAAmB,YAAY,IAAI,CAAC,oBAAoB,CAAC;QACvE,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YAClB,MAAM,iBAAiB,EAAE;YACzB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;gBACjB,IAAI,KAAK,iBAAiB,CAAC,OAAO;oBAC9B,OAAO,KAAK,YAAY,CAAC;oBACzB,iBAAiB,cAAc;oBAC/B,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,UAAU,sKAAA,CAAA,UAAe,CAAC,MAAM,CAAC,KAAK,OAAO,GAAG;wBAClE,eAAe,IAAI,CAAC;oBACxB;gBACJ;YACJ;YACA,IAAI,CAAC,KAAK,aAAa,CAAC,MAAM,IAAI,CAAC,kBAAkB;gBACjD,KAAK,mBAAmB,CAAC;YAC7B;YACA,IAAI,CAAC,SAAS,CAAC;YACf,IAAI,CAAC,UAAU;QACnB;IACJ;IACA,qBAAqB;QACjB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA;YACxB;QACJ;QACA,IAAI,CAAC,aAAa,GAAG,EAAE;IAC3B;IACA,oBAAoB,KAAK,EAAE;QACvB,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,KAAK,SAAS;QAC5B,MAAM,OAAO,CAAE,CAAA;YACX,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,KAAK,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;gBAChE,KAAK,aAAa,CAAC,IAAI,CAAC,MAAO,IAAM,sKAAA,CAAA,UAAe,CAAC,MAAM,CAAC,KAAK,OAAO,GAAK;oBACzE,KAAK,YAAY,CAAC,KAAK,MAAM,CAAC;oBAC9B,KAAK,OAAO;gBAChB,GAAI;oBACA,eAAe;gBACnB;YACJ;QACJ;IACJ;IACA,qBAAqB,UAAU,EAAE;QAC7B,MAAM,SAAS,EAAE;QACjB,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,aAAa;YACvB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,YAAa,CAAA;gBACd,OAAO,IAAI,CAAC;oBACR,WAAW;gBACf;YACJ;QACJ;QACA,OAAO;IACX;IACA,kBAAkB,IAAI,EAAE;QACpB,MAAM,YAAY,KAAK,SAAS,IAAI;QACpC,MAAM,WAAW,IAAI,CAAC,eAAe,CAAC;QACtC,OAAO,CAAC,CAAC,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,aAAa,CAAC,sKAAA,CAAA,UAAe,CAAC,SAAS,CAAC,SAAS;IACzE;IACA,aAAa,IAAI,EAAE;QACf,IAAI,aAAa,OAAO,MAAM;YAC1B,OAAO;gBACH,WAAW;YACf;QACJ;QACA,IAAI,aAAa,OAAO,QAAQ,CAAC,KAAK,QAAQ,EAAE;YAC5C,KAAK,QAAQ,GAAG,6KAAA,CAAA,mBAAgB;QACpC;QACA,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS,GAAG;YAC1D,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK,SAAS;YACjD,KAAK,UAAU,GAAG,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,wBAAwB,CAAC,CAAA,GAAA,6KAAA,CAAA,OAAI,AAAD,EAAE,UAAU;QACtF;QACA,IAAI,iBAAiB,KAAK,UAAU,EAAE;YAClC,KAAK,uBAAuB,GAAG,KAAK,uBAAuB,IAAI;QACnE;QACA,OAAO;IACX;IACA,yBAAyB,QAAQ,EAAE;QAC/B,OAAQ;YACJ,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO;QACf;IACJ;IACA,aAAa;QACT,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE;QAC9B,IAAI,CAAC,YAAY;IACrB;IACA,eAAe;QACX,IAAI,CAAC,MAAM,CAAC,IAAI,CAAE,CAAC,OAAO;YACtB,MAAM,SAAS,MAAM,YAAY;YACjC,MAAM,SAAS,MAAM,YAAY;YACjC,IAAI;YACJ,IAAI,SAAS,QAAQ;gBACjB,SAAS;YACb,OAAO,IAAI,SAAS,QAAQ;gBACxB,SAAS,CAAC;YACd,OAAO;gBACH,SAAS;YACb;YACA,OAAO;QACX;IACJ;IACA,cAAc;QACV,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAC5B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,6KAAA,CAAA,4BAAyB;QAClD,KAAK,CAAC;QACN,IAAI,CAAC,oBAAoB;IAC7B;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,MAAM,gBAAgB,EAAE;QACxB,IAAI,KAAK,MAAM,IAAI,KAAK,MAAM,CAAC,MAAM,EAAE;YACnC,MAAM,WAAW,KAAK,YAAY;YAClC,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,KAAK,QAAQ;YACpD,KAAK,wBAAwB,CAAC;YAC9B,MAAM,cAAc,KAAK,oBAAoB;YAC7C,KAAK,cAAc,GAAG,KAAK,gBAAgB,CAAC,YAAY,4KAAA,CAAA,UAAa,EAAE,KAAK,uBAAuB,CAAC,aAAa,UAAU;YAC3H,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;gBACd,KAAK,gBAAgB,CAAC;YAC1B;QACJ;IACJ;IACA,yBAAyB,CAAC,EAAE;QACxB,IAAI,CAAC,QAAQ;IACjB;IACA,iBAAiB,aAAa,EAAE;QAC5B,MAAM,OAAO,IAAI;QACjB,IAAI,8BAA8B;QAClC,cAAc,OAAO,CAAE,CAAA;YACnB,IAAI;YACJ,IAAI,EACA,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,SAAS,CAAC,cAAc,KAAK,KAAK,KAAK,KAAK,MAAM,eAAe,YAAY,QAAQ,EAAE;gBAC3H;YACJ;QACJ;QACA,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,eAAgB,CAAC,OAAO;YACzB,OAAQ,KAAK,QAAQ;gBACjB,KAAK;oBACD,CAAA,GAAA,8LAAA,CAAA,kBAAe,AAAD,EAAE;oBAChB;gBACJ,KAAK;oBACD,KAAK,iBAAiB,CAAC;oBACvB;gBACJ;oBACI,KAAK,gBAAgB,CAAC,MAAM;YACpC;QACJ;IACJ;IACA,wBAAwB,WAAW,EAAE,QAAQ,EAAE,aAAa,EAAE;QAC1D,MAAM,OAAO,IAAI;QACjB,MAAM,mBAAmB,KAAK,MAAM,CAAC;QACrC,MAAM,aAAa,oBAAoB,iBAAiB,EAAE;QAC1D,OAAO;YACH,oBAAoB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,IAAI;YAC3D;gBACI,MAAM,EACF,iBAAiB,eAAe,EACnC,GAAG,KAAK,MAAM;gBACf,MAAM,qBAAqB,KAAK,kBAAkB;gBAClD,IAAI,iBAAiB;oBACjB,KAAK,QAAQ,GAAG,WAAW,CAAC,6KAAA,CAAA,4BAAyB,EAAE;oBACvD,gBAAgB;gBACpB;YACJ;YACA,gBAAe,CAAC;gBACZ,IAAI,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;oBACb,KAAK,gBAAgB,CAAC;gBAC1B;gBACA,IAAI,KAAK,MAAM,CAAC,oBAAoB;oBAChC,KAAK,QAAQ,GAAG,WAAW,CAAC,6KAAA,CAAA,4BAAyB,EAAE,KAAK,kBAAkB,CAAC,EAAE,SAAS;gBAC9F;YACJ;YACA,cAAa,CAAC,EAAE,QAAQ,EAAE,WAAW;gBACjC,IAAI,CAAC,EAAE,QAAQ,EAAE;oBACb;gBACJ;gBACA,MAAM,eAAe,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;gBACvB,MAAM,kCAAkC,EAAE,QAAQ,CAAC,GAAG,GAAG;gBACzD,MAAM,OAAO,KAAK,MAAM,CAAC,EAAE,QAAQ,CAAC,GAAG,GAAG,gCAAgC;gBAC1E,IAAI,CAAC,MAAM;oBACP;gBACJ;gBACA,MAAM,mBAAmB;oBAAC,KAAK,QAAQ;iBAAC;gBACxC,aAAa,WAAW,CAAC,6KAAA,CAAA,6BAA0B,EAAE,KAAK,kBAAkB,CAAC,IAAI;gBACjF,IAAI,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAE;oBACtB,iBAAiB,IAAI,CAAC;gBAC1B;gBACA,IAAI,MAAM,EAAE,QAAQ,CAAC,GAAG,EAAE;oBACtB,iBAAiB,IAAI,CAAC;gBAC1B;gBACA,IAAI,KAAK,QAAQ,KAAK,6KAAA,CAAA,mBAAgB,IAAI,KAAK,MAAM,CAAC,WAAW;oBAC7D,aAAa,QAAQ,CAAC,6KAAA,CAAA,yBAAsB;gBAChD;gBACA,MAAM,eAAe,EAAE,QAAQ,CAAC,GAAG,KAAK,WAAW,KAAK,EAAE,QAAQ,CAAC,GAAG,GAAG,EAAE,QAAQ,CAAC,OAAO,KAAK;gBAChG,MAAM,YAAY,KAAK,aAAa;gBACpC,MAAM,YAAY,EAAE,QAAQ,CAAC,GAAG,KAAK,YAAY;gBACjD,IAAI,cAAc;oBACd,iBAAiB,IAAI,CAAC;gBAC1B;gBACA,IAAI,WAAW;oBACX,iBAAiB,IAAI,CAAC;gBAC1B;gBACA,IAAI,YAAY,KAAK,QAAQ,EAAE;oBAC3B,iBAAiB,IAAI,CAAC,6KAAA,CAAA,mBAAgB;oBACtC,iBAAiB,IAAI,CAAC,KAAK,MAAM,CAAC;oBAClC,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,GAAG,GAAG;wBACrB,iBAAiB,IAAI,CAAC,CAAC,OAAO,EAAE,KAAK,GAAG,EAAE;oBAC9C;gBACJ;gBACA,cAAc,IAAI,CAAC;oBACf,UAAU,KAAK,QAAQ;oBACvB,MAAM;oBACN,SAAS;oBACT,yBAAyB;gBAC7B;YACJ;YACA,MAAM,KAAK,cAAc,CAAC;YAC1B,MAAM,KAAK,cAAc,CAAC,KAAK,aAAa,IAAI;YAChD,YAAY;YACZ,eAAe,KAAK,MAAM,CAAC;YAC3B,oBAAoB,aAAa,QAAQ;QAC7C;IACJ;IACA,eAAe;QACX,IAAI,EACA,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC;QACrC,IAAI,kBAAkB;YAClB,MAAM,EACF,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,MAAM;YACf,IAAI,eAAe,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,qBAAqB;YACzF,IAAI,CAAC,cAAc;gBACf,eAAe,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,MAAM,CAAA,GAAA,+KAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;YACxF;YACA,WAAW,gBAAgB,CAAC,aAAa,IAAI;QACjD;QACA,IAAI,WAAW,UAAU;YACrB,IAAI,IAAI,CAAC,eAAe,EAAE;gBACtB,OAAO,IAAI,CAAC,eAAe;YAC/B;YACA,IAAI,CAAC,eAAe,GAAG,WAAW,IAAI,CAAC,eAAe;QAC1D;QACA,OAAO,WAAW,IAAI,IAAI;IAC9B;IACA,kBAAkB;QACd,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YACd,OAAO;QACX;QACA,MAAM,cAAc,IAAI,CAAC,MAAM,CAAC;QAChC,MAAM,QAAQ,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,QAAQ;QACpC,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC,MAAM;QACrC,MAAM,cAAc,KAAK,KAAK,CAAC,QAAQ,gBAAgB;QACvD,OAAO,aAAa,cAAc,aAAa;IACnD;IACA,2BAA2B;QACvB,OAAO,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,OAAO,IAAI,CAAC,eAAe;IAClF;IACA,yBAAyB,QAAQ,EAAE;QAC/B,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC;QAChC,IAAI;QACJ,IAAI;QACJ,IAAI,SAAS,EAAE;QACf,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YAC/B,OAAO,KAAK,CAAC,EAAE;YACf,OAAO,IAAI,CAAC;YACZ,IAAI,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK,eAAe,IAAI,KAAK,OAAO,EAAE;gBACxE,KAAK,GAAG,GAAG,IAAI,CAAC,cAAc,CAAC,OAAO,MAAM,GAAG,GAAG;YACtD;YACA,IAAI,KAAK,OAAO,GAAG,KAAK,KAAK,GAAG,GAAG,KAAK,OAAO,IAAI,UAAU;gBACzD,mBAAmB,EAAE;gBACrB,IAAK,IAAI,GAAG,IAAI,KAAK,OAAO,GAAG,GAAG,IAAK;oBACnC,iBAAiB,IAAI,CAAC;wBAClB,QAAQ;oBACZ;gBACJ;gBACA,SAAS,OAAO,MAAM,CAAC;YAC3B,OAAO;gBACH,OAAO,KAAK,OAAO;YACvB;QACJ;QACA,IAAI,CAAC,SAAS,CAAC;IACnB;IACA,eAAe,KAAK,EAAE,QAAQ,EAAE;QAC5B,OAAO,QAAQ;IACnB;IACA,UAAU,KAAK,EAAE;QACb,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,eAAe,GAAG;IAC3B;IACA,uBAAuB;QACnB,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,WAAW,IAAI,CAAC,YAAY;QAClC,MAAM,SAAS,EAAE;QACjB,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YAC/B,OAAO,KAAK,CAAC,EAAE;YACf,IAAI,CAAC,KAAK,MAAM,EAAE;gBACd,MAAM,gBAAgB;oBAClB,UAAU;wBACN,KAAK,SAAS,IAAI;wBAClB,KAAK,IAAI,CAAC,cAAc,CAAC,GAAG;oBAChC;gBACJ;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,QAAQ,GAAG;oBAC1B,cAAc,QAAQ,GAAG,KAAK,QAAQ;gBAC1C;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,GAAG;oBACzB,cAAc,OAAO,GAAG,KAAK,OAAO;gBACxC;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,GAAG;oBACzB,cAAc,QAAQ,CAAC,OAAO,GAAG,KAAK,OAAO;gBACjD;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,GAAG;oBACzB,cAAc,QAAQ,CAAC,OAAO,GAAG,KAAK,OAAO;gBACjD;gBACA,OAAO,IAAI,CAAC;YAChB;QACJ;QACA,OAAO;IACX;IACA,iBAAiB,UAAU,EAAE;QACzB,CAAA,GAAA,8LAAA,CAAA,kBAAe,AAAD,EAAE;YACZ,YAAY;QAChB;IACJ;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,EACA,MAAM,IAAI,EACV,SAAS,OAAO,EAChB,yBAAyB,uBAAuB,EACnD,GAAG;QACJ,MAAM,EACF,cAAc,YAAY,EAC1B,gBAAgB,cAAc,EACjC,GAAG,CAAA,GAAA,+LAAA,CAAA,mBAAgB,AAAD,EAAE;YACjB,MAAM;YACN,SAAS;YACT,yBAAyB;YACzB,iBAAiB,IAAI,CAAC,MAAM,CAAC;YAC7B,yBAAyB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI;QAC5D;QACA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;YACvB,MAAM;YACN,gBAAgB;YAChB,MAAM,KAAK,IAAI;YACf,gBAAgB;QACpB;IACJ;IACA,iBAAiB,KAAK,EAAE,2BAA2B,EAAE;QACjD,IAAI;QACJ,IAAI,EACA,MAAM,IAAI,EACV,SAAS,OAAO,EAChB,yBAAyB,uBAAuB,EACnD,GAAG;QACJ,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC,KAAK,SAAS;QACvD,IAAI,kCAAkC;QACtC,IAAI,KAAK,MAAM,aAAa;YACxB,MAAM,EACF,yBAAyB,uBAAuB,EAChD,YAAY,UAAU,EACtB,WAAW,SAAS,EACvB,GAAG;YACJ,kCAAkC,IAAI,CAAC,gCAAgC,CAAC;gBACpE,yBAAyB;gBACzB,YAAY;gBACZ,WAAW;YACf;QACJ;QACA,MAAM,OAAO,KAAK,SAAS,IAAI,KAAK,IAAI;QACxC,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAC/C,MAAM,EACF,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,EACF,uBAAuB,qBAAqB,EAC5C,gBAAgB,cAAc,EAC9B,cAAc,YAAY,EAC7B,GAAG,CAAA,GAAA,8LAAA,CAAA,kBAAe,AAAD,EAAE,CAAA,GAAA,qMAAA,CAAA,kCAA+B,AAAD,EAAE;YAChD,SAAS;YACT,yBAAyB;YACzB,MAAM;YACN,MAAM;YACN,aAAa;YACb,iCAAiC;YACjC,qBAAqB,IAAI,CAAC,cAAc;YACxC,yBAAyB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI;YACxD,mBAAmB,IAAI,CAAC,MAAM,CAAC;YAC/B,yBAAyB,IAAI,CAAC,MAAM,CAAC;YACrC,iBAAiB,IAAI,CAAC,MAAM,CAAC;YAC7B,0BAA0B,IAAI,CAAC,MAAM,CAAC;YACtC,mBAAmB,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,MAAM,CAAC;YAC3E,qBAAqB,IAAI,CAAC,MAAM,CAAC;YACjC,sBAAsB,IAAI,CAAC,MAAM,CAAC;YAClC,UAAU,KAAK,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,QAAQ,IAAI;YAC7D,eAAe,SAAS,CAAC,eAAe,KAAK,KAAK,KAAK,KAAK,MAAM,gBAAgB,aAAa,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,CAAC,QAAQ,IAAI;YACnJ,QAAQ,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,SAAS,CAAC;YACnE,oBAAoB,IAAI,CAAC,eAAe;YACxC,WAAW,IAAI,CAAC,MAAM,CAAC;YACvB,yBAAyB;gBACrB,IAAI,CAAC,6BAA6B;gBAClC,IAAI,IAAI,CAAC,oCAAoC,CAAC,qBAAqB,8BAA8B;oBAC7F,oBAAoB,YAAY,CAAC,IAAI,EAAE,IAAI,CAAC,kBAAkB,CAAC;gBACnE;YACJ;QACJ;QACA,MAAM,EACF,qBAAqB,mBAAmB,EAC3C,GAAG,IAAI,CAAC,MAAM;QACf,SAAS,uBAAuB,KAAK,MAAM,uBAAuB;QAClE,IAAI,kBAAkB,KAAK,SAAS,EAAE;YAClC,IAAI,CAAC,cAAc,CAAC,gBAAgB,KAAK,SAAS,EAAE,KAAK,UAAU,EAAE;QACzE;QACA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;YACvB,MAAM;YACN,gBAAgB;YAChB,MAAM,KAAK,IAAI;YACf,gBAAgB;QACpB;IACJ;IACA,gCAAgC;QAC5B,IAAI,CAAC,+BAA+B,GAAG,CAAC,IAAI,CAAC,+BAA+B,IAAI,CAAC,IAAI;IACzF;IACA,qCAAqC,mBAAmB,EAAE,2BAA2B,EAAE;QACnF,OAAO,oBAAoB,MAAM,CAAC,oCAAoC,IAAI,CAAC,+BAA+B,KAAK;IACnH;IACA,kBAAkB;QACd,OAAO;YACH,kBAAkB,IAAI,CAAC,MAAM,CAAC;YAC9B,cAAc,IAAI,CAAC,MAAM,CAAC;YAC1B,kBAAkB,IAAI,CAAC,MAAM,CAAC;YAC9B,cAAc,IAAI,CAAC,MAAM,CAAC;QAC9B;IACJ;IACA,iBAAiB;QACb,MAAM,EACF,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,QAAQ,IAAI;IACvB;IACA,eAAe,cAAc,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,EAAE;QAC9D,MAAM,aAAa,IAAI,CAAC,cAAc;QACtC,eAAe,EAAE,CAAC,YAAa,CAAA;YAC3B,WAAW,qBAAqB,CAAC,oBAAoB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,MAAM;gBAC9D,WAAW;YACf;QACJ;QACA,IAAI,CAAC,cAAc,CAAC,gBAAgB,YAAY;QAChD,IAAI,CAAC,qBAAqB,CAAC,gBAAgB;IAC/C;IACA,eAAe,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE;QAClD,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,KAAK,SAAS;QAC5B,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;YACpB;QACJ;QACA,MAAM,UAAU,MAAO,IAAM,KAAK,eAAe,CAAC,YAAc;YAC5D,MAAM,aAAa,KAAK,eAAe,CAAC;YACxC,IAAI,eAAe,eAAe,IAAI,EAAE;gBACpC,MAAM,cAAc,eAAe,MAAM,CAAC;gBAC1C,IAAI,eAAe,eAAe,SAAS,MAAM,EAAE,MAAM;oBACjD,IAAI,CAAC,MAAM,OAAO,CAAC,WAAW,CAAC,MAAM,OAAO,CAAC,WAAW,OAAO,MAAM,KAAK,OAAO,MAAM,EAAE;wBACrF,OAAO;oBACX;oBACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;wBACpC,IAAI,MAAM,CAAC,EAAE,KAAK,MAAM,CAAC,EAAE,EAAE;4BACzB,OAAO;wBACX;oBACJ;oBACA,OAAO;gBACX,EAAE,YAAY,cAAc;oBAC5B;gBACJ;YACJ;YACA,eAAe,MAAM,CAAC,SAAS;QACnC,GAAI;YACA,MAAM;YACN,eAAe;QACnB,GAAG;YACC,wBAAwB;QAC5B;QACA,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,YAAY,wKAAA,CAAA,cAAW,EAAE;IAC7C;IACA,YAAY;QACR,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,GAAG;YACzB,MAAM,EACF,MAAM,YAAY,EACrB,GAAG,IAAI,CAAC,MAAM;YACf,IAAI,CAAC,MAAM,GAAG,gBAAgB,aAAa,MAAM,CAAC;QACtD;QACA,OAAO,IAAI,CAAC,MAAM;IACtB;IACA,iBAAiB,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE;QAC3C,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;QAClC,IAAI,oBAAoB,OAAO,MAAM,CAAC,eAAe;QACrD,MAAM,WAAW,KAAK,CAAC,iBAAiB,SAAS,MAAM,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,eAAe;YAC/E,UAAU,CAAC,oBAAoB,gBAAgB,cAAc,QAAQ;QACzE;QACA,IAAI,iBAAiB;QACrB,SAAS,EAAE,CAAC,iBAAkB,CAAA;YAC1B,IAAI,eAAe,KAAK,IAAI,IAAI,CAAC,gBAAgB;gBAC7C,oBAAoB;YACxB;QACJ;QACA,IAAI,CAAC,EAAE,CAAC,iBAAkB,CAAA;YACtB,IAAI,eAAe,KAAK,IAAI,IAAI,CAAC,mBAAmB;gBAChD,iBAAiB;gBACjB,SAAS,MAAM,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK;gBACrC,iBAAiB;YACrB;QACJ;QACA,OAAO;IACX;IACA,eAAe,KAAK,EAAE,UAAU,EAAE;QAC9B,MAAM,SAAS,EAAE;QACjB,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,OAAO,IAAK;YACxB,QAAQ;gBACJ,OAAO;YACX;YACA,IAAI,YAAY;gBACZ,MAAM,QAAQ,GAAG;YACrB;YACA,OAAO,IAAI,CAAC;QAChB;QACA,OAAO;IACX;IACA,gBAAgB;QACZ,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC,YAAY;IAC3D;IACA,yBAAyB,aAAa,EAAE;QACpC,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YACtB,OAAO,mBAAmB,CAAC,YAAY,OAAO,CAAE,CAAA,WAAY,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC;QACzH;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,gBAAgB;YACzB,OAAO,mBAAmB,CAAC,eAAe,OAAO,CAAE,CAAA,WAAY,IAAI,CAAC,mBAAmB,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,GAAG;QACxH;IACJ;IACA,aAAa,QAAQ,EAAE;QACnB,IAAI,CAAC,iCAAiC,GAAG;QACzC,SAAS,KAAK;QACd,IAAI,CAAC,iCAAiC,GAAG;QACzC,SAAS,MAAM,CAAC,WAAW;IAC/B;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,MAAM,KAAK,QAAQ,CAAC,MAAM,CAAC,gBAAgB;YAC3C;QACJ;QACA,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,wBAAwB,CAAC,KAAK,KAAK;gBACxC,IAAI,IAAI,CAAC,MAAM,CAAC,UAAU;oBACtB,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,GAAG;wBAC5B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAE,CAAC,GAAG;4BAC7B,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,IAAI,GAAG;gCACjC,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,gBAAgB,IAAI;gCACxB,IAAI,aAAa,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,cAAc,GAAG;oCACxD,MAAM,cAAc,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE;oCAClC,MAAM,YAAY,YAAY,KAAK,KAAK;oCACxC,MAAM,EACF,yBAAyB,uBAAuB,EAChD,YAAY,UAAU,EACzB,GAAG,gBAAgB,IAAI;oCACxB,IAAI,KAAK,MAAM,aAAa,IAAI,CAAC,gCAAgC,CAAC;wCAC1D,yBAAyB;wCACzB,YAAY;wCACZ,WAAW;oCACf,IAAI;wCACJ,gBAAgB,cAAc,CAAC,MAAM,CAAC,SAAS;oCACnD,OAAO;wCACH,IAAI,CAAC,YAAY,CAAC,gBAAgB,cAAc;oCACpD;gCACJ;4BACJ;wBACJ;oBACJ;gBACJ,OAAO;oBACH,IAAI,CAAC,iBAAiB,CAAC,KAAK,KAAK;oBACjC,IAAI,CAAC,WAAW;gBACpB;gBACA;YACJ,KAAK;gBACD,IAAI,CAAC,kBAAkB;gBACvB,IAAI,CAAC,iBAAiB,CAAC,KAAK,KAAK;gBACjC,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,cAAc;gBACnB;YACJ,KAAK;gBAAe;oBAChB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;oBACf,IAAI,WAAW,UAAU;wBACrB,IAAI,CAAC,cAAc;oBACvB;oBACA;gBACJ;YACA,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;gBAAS;oBACV,KAAK,CAAC,eAAe;oBACrB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;oBACf,IAAI,WAAW,UAAU;wBACrB,IAAI,CAAC,cAAc;oBACvB;oBACA;gBACJ;YACA;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,WAAW;IACpB;IACA,sBAAsB,cAAc,EAAE,SAAS,EAAE;QAC7C,IAAI,CAAC,EAAE,CAAC,iBAAkB,CAAA;YACtB,IAAI,KAAK,QAAQ,KAAK,CAAC,WAAW,EAAE,WAAW,EAAE;gBAC7C,eAAe,6BAA6B,CAAC,SAAS,KAAK,KAAK;YACpE;QACJ;QACA,eAAe,EAAE,CAAC,gBAAiB,CAAA;YAC/B,MAAM,uBAAuB,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,KAAK,MAAM,OAAO,CAAC,KAAK,KAAK;YAC7E,IAAI,CAAC,IAAI,CAAC,iCAAiC,IAAI,CAAC,CAAC,wBAAwB,KAAK,KAAK,KAAK,KAAK,aAAa,GAAG;gBACzG,IAAI,CAAC,iBAAiB,CAAC,WAAW,KAAK,KAAK;YAChD;QACJ;IACJ;IACA,oBAAoB;QAChB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,WAAW,YAAY,IAAI,CAAC,wBAAwB,IAAI;YACxD,IAAI,CAAC,eAAe,CAAC,SAAS,CAAC;QACnC;IACJ;IACA,WAAW,IAAI,EAAE,KAAK,EAAE;QACpB,MAAM,OAAO,IAAI;QACjB,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YAChB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,MAAO,CAAC,WAAW;gBACpB,KAAK,iBAAiB,CAAC,WAAW;YACtC;QACJ,OAAO,IAAI,aAAa,OAAO,MAAM;YACjC,KAAK,iBAAiB,CAAC,MAAM;QACjC;IACJ;IACA,UAAU,KAAK,EAAE;QACb,OAAO,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,UAAU,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;IAC1H;IACA,mBAAmB,SAAS,EAAE;QAC1B,MAAM,gBAAgB,IAAI,CAAC,cAAc,IAAI;QAC7C,IAAI,eAAe;YACf,OAAO,cAAc,MAAM,CAAC,2BAA2B,cAAc,MAAM,CAAC;QAChF;IACJ;IACA,sBAAsB;QAClB,OAAO,IAAI,CAAC,iBAAiB;IACjC;AACJ;AACA,CAAA,GAAA,kKAAA,CAAA,UAAiB,AAAD,EAAE,mBAAmB;uCACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1707, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.item_option_action.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.item_option_action.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Class from \"../../../core/class\";\r\nexport default class ItemOptionAction {\r\n    constructor(options) {\r\n        this._options = options;\r\n        this._itemsRunTimeInfo = this._options.itemsRunTimeInfo\r\n    }\r\n    findInstance() {\r\n        return this._itemsRunTimeInfo.findWidgetInstanceByItem(this._options.item)\r\n    }\r\n    findItemContainer() {\r\n        return this._itemsRunTimeInfo.findItemContainerByItem(this._options.item)\r\n    }\r\n    findPreparedItem() {\r\n        return this._itemsRunTimeInfo.findPreparedItemByItem(this._options.item)\r\n    }\r\n    tryExecute() {\r\n        Class.abstract()\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AACe,MAAM;IACjB,YAAY,OAAO,CAAE;QACjB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,QAAQ,CAAC,gBAAgB;IAC3D;IACA,eAAe;QACX,OAAO,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;IAC7E;IACA,oBAAoB;QAChB,OAAO,IAAI,CAAC,iBAAiB,CAAC,uBAAuB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;IAC5E;IACA,mBAAmB;QACf,OAAO,IAAI,CAAC,iBAAiB,CAAC,sBAAsB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI;IAC3E;IACA,aAAa;QACT,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1741, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    isDefined\r\n} from \"../../../core/utils/type\";\r\nexport const createItemPathByIndex = (index, isTabs) => `${isTabs?\"tabs\":\"items\"}[${index}]`;\r\nexport const concatPaths = (path1, path2) => {\r\n    if (isDefined(path1) && isDefined(path2)) {\r\n        return `${path1}.${path2}`\r\n    }\r\n    return path1 || path2\r\n};\r\nexport const getTextWithoutSpaces = text => text ? text.replace(/\\s/g, \"\") : void 0;\r\nexport const isEqualToDataFieldOrNameOrTitleOrCaption = (item, fieldName) => {\r\n    if (item) {\r\n        return item.dataField === fieldName || item.name === fieldName || getTextWithoutSpaces(item.title) === fieldName || \"group\" === item.itemType && getTextWithoutSpaces(item.caption) === fieldName\r\n    }\r\n    return false\r\n};\r\nexport const getFullOptionName = (path, optionName) => `${path}.${optionName}`;\r\nexport const getOptionNameFromFullName = fullName => {\r\n    const parts = fullName.split(\".\");\r\n    return parts[parts.length - 1].replace(/\\[\\d+]/, \"\")\r\n};\r\nexport const tryGetTabPath = fullPath => {\r\n    const pathParts = fullPath.split(\".\");\r\n    const resultPathParts = [...pathParts];\r\n    for (let i = pathParts.length - 1; i >= 0; i--) {\r\n        if (isFullPathContainsTabs(pathParts[i])) {\r\n            return resultPathParts.join(\".\")\r\n        }\r\n        resultPathParts.splice(i, 1)\r\n    }\r\n    return \"\"\r\n};\r\nexport const isFullPathContainsTabs = fullPath => fullPath.indexOf(\"tabs\") > -1;\r\nexport const getItemPath = (items, item, isTabs) => {\r\n    const index = items.indexOf(item);\r\n    if (index > -1) {\r\n        return createItemPathByIndex(index, isTabs)\r\n    }\r\n    for (let i = 0; i < items.length; i++) {\r\n        const targetItem = items[i];\r\n        const tabOrGroupItems = targetItem.tabs || targetItem.items;\r\n        if (tabOrGroupItems) {\r\n            const itemPath = getItemPath(tabOrGroupItems, item, targetItem.tabs);\r\n            if (itemPath) {\r\n                return concatPaths(createItemPathByIndex(i, isTabs), itemPath)\r\n            }\r\n        }\r\n    }\r\n};\r\nexport function convertToLayoutManagerOptions(_ref) {\r\n    let {\r\n        form: form,\r\n        $formElement: $formElement,\r\n        formOptions: formOptions,\r\n        items: items,\r\n        validationGroup: validationGroup,\r\n        extendedLayoutManagerOptions: extendedLayoutManagerOptions,\r\n        onFieldDataChanged: onFieldDataChanged,\r\n        onContentReady: onContentReady,\r\n        onDisposing: onDisposing,\r\n        onFieldItemRendered: onFieldItemRendered\r\n    } = _ref;\r\n    const baseOptions = {\r\n        form: form,\r\n        items: items,\r\n        $formElement: $formElement,\r\n        validationGroup: validationGroup,\r\n        onFieldDataChanged: onFieldDataChanged,\r\n        onContentReady: onContentReady,\r\n        onDisposing: onDisposing,\r\n        onFieldItemRendered: onFieldItemRendered,\r\n        validationBoundary: formOptions.scrollingEnabled ? $formElement : void 0,\r\n        scrollingEnabled: formOptions.scrollingEnabled,\r\n        showRequiredMark: formOptions.showRequiredMark,\r\n        showOptionalMark: formOptions.showOptionalMark,\r\n        requiredMark: formOptions.requiredMark,\r\n        optionalMark: formOptions.optionalMark,\r\n        requiredMessage: formOptions.requiredMessage,\r\n        screenByWidth: formOptions.screenByWidth,\r\n        layoutData: formOptions.formData,\r\n        labelLocation: formOptions.labelLocation,\r\n        customizeItem: formOptions.customizeItem,\r\n        minColWidth: formOptions.minColWidth,\r\n        showColonAfterLabel: formOptions.showColonAfterLabel,\r\n        onEditorEnterKey: formOptions.onEditorEnterKey,\r\n        labelMode: formOptions.labelMode\r\n    };\r\n    const result = extend(baseOptions, {\r\n        isRoot: extendedLayoutManagerOptions.isRoot,\r\n        colCount: extendedLayoutManagerOptions.colCount,\r\n        alignItemLabels: extendedLayoutManagerOptions.alignItemLabels,\r\n        cssItemClass: extendedLayoutManagerOptions.cssItemClass,\r\n        colCountByScreen: extendedLayoutManagerOptions.colCountByScreen,\r\n        onLayoutChanged: extendedLayoutManagerOptions.onLayoutChanged,\r\n        width: extendedLayoutManagerOptions.width\r\n    });\r\n    return result\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;AACD;AAAA;AAGA;AAAA;;;AAGO,MAAM,wBAAwB,CAAC,OAAO,SAAW,GAAG,SAAO,SAAO,QAAQ,CAAC,EAAE,MAAM,CAAC,CAAC;AACrF,MAAM,cAAc,CAAC,OAAO;IAC/B,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;QACtC,OAAO,GAAG,MAAM,CAAC,EAAE,OAAO;IAC9B;IACA,OAAO,SAAS;AACpB;AACO,MAAM,uBAAuB,CAAA,OAAQ,OAAO,KAAK,OAAO,CAAC,OAAO,MAAM,KAAK;AAC3E,MAAM,2CAA2C,CAAC,MAAM;IAC3D,IAAI,MAAM;QACN,OAAO,KAAK,SAAS,KAAK,aAAa,KAAK,IAAI,KAAK,aAAa,qBAAqB,KAAK,KAAK,MAAM,aAAa,YAAY,KAAK,QAAQ,IAAI,qBAAqB,KAAK,OAAO,MAAM;IAC5L;IACA,OAAO;AACX;AACO,MAAM,oBAAoB,CAAC,MAAM,aAAe,GAAG,KAAK,CAAC,EAAE,YAAY;AACvE,MAAM,4BAA4B,CAAA;IACrC,MAAM,QAAQ,SAAS,KAAK,CAAC;IAC7B,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,OAAO,CAAC,UAAU;AACrD;AACO,MAAM,gBAAgB,CAAA;IACzB,MAAM,YAAY,SAAS,KAAK,CAAC;IACjC,MAAM,kBAAkB;WAAI;KAAU;IACtC,IAAK,IAAI,IAAI,UAAU,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC5C,IAAI,uBAAuB,SAAS,CAAC,EAAE,GAAG;YACtC,OAAO,gBAAgB,IAAI,CAAC;QAChC;QACA,gBAAgB,MAAM,CAAC,GAAG;IAC9B;IACA,OAAO;AACX;AACO,MAAM,yBAAyB,CAAA,WAAY,SAAS,OAAO,CAAC,UAAU,CAAC;AACvE,MAAM,cAAc,CAAC,OAAO,MAAM;IACrC,MAAM,QAAQ,MAAM,OAAO,CAAC;IAC5B,IAAI,QAAQ,CAAC,GAAG;QACZ,OAAO,sBAAsB,OAAO;IACxC;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACnC,MAAM,aAAa,KAAK,CAAC,EAAE;QAC3B,MAAM,kBAAkB,WAAW,IAAI,IAAI,WAAW,KAAK;QAC3D,IAAI,iBAAiB;YACjB,MAAM,WAAW,YAAY,iBAAiB,MAAM,WAAW,IAAI;YACnE,IAAI,UAAU;gBACV,OAAO,YAAY,sBAAsB,GAAG,SAAS;YACzD;QACJ;IACJ;AACJ;AACO,SAAS,8BAA8B,IAAI;IAC9C,IAAI,EACA,MAAM,IAAI,EACV,cAAc,YAAY,EAC1B,aAAa,WAAW,EACxB,OAAO,KAAK,EACZ,iBAAiB,eAAe,EAChC,8BAA8B,4BAA4B,EAC1D,oBAAoB,kBAAkB,EACtC,gBAAgB,cAAc,EAC9B,aAAa,WAAW,EACxB,qBAAqB,mBAAmB,EAC3C,GAAG;IACJ,MAAM,cAAc;QAChB,MAAM;QACN,OAAO;QACP,cAAc;QACd,iBAAiB;QACjB,oBAAoB;QACpB,gBAAgB;QAChB,aAAa;QACb,qBAAqB;QACrB,oBAAoB,YAAY,gBAAgB,GAAG,eAAe,KAAK;QACvE,kBAAkB,YAAY,gBAAgB;QAC9C,kBAAkB,YAAY,gBAAgB;QAC9C,kBAAkB,YAAY,gBAAgB;QAC9C,cAAc,YAAY,YAAY;QACtC,cAAc,YAAY,YAAY;QACtC,iBAAiB,YAAY,eAAe;QAC5C,eAAe,YAAY,aAAa;QACxC,YAAY,YAAY,QAAQ;QAChC,eAAe,YAAY,aAAa;QACxC,eAAe,YAAY,aAAa;QACxC,aAAa,YAAY,WAAW;QACpC,qBAAqB,YAAY,mBAAmB;QACpD,kBAAkB,YAAY,gBAAgB;QAC9C,WAAW,YAAY,SAAS;IACpC;IACA,MAAM,SAAS,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,aAAa;QAC/B,QAAQ,6BAA6B,MAAM;QAC3C,UAAU,6BAA6B,QAAQ;QAC/C,iBAAiB,6BAA6B,eAAe;QAC7D,cAAc,6BAA6B,YAAY;QACvD,kBAAkB,6BAA6B,gBAAgB;QAC/D,iBAAiB,6BAA6B,eAAe;QAC7D,OAAO,6BAA6B,KAAK;IAC7C;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1857, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.item_options_actions.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.item_options_actions.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    data\r\n} from \"../../../core/element_data\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport ItemOptionAction from \"./m_form.item_option_action\";\r\nimport {\r\n    getFullOptionName\r\n} from \"./m_form.utils\";\r\nclass WidgetOptionItemOptionAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        const {\r\n            value: value\r\n        } = this._options;\r\n        const instance = this.findInstance();\r\n        if (instance) {\r\n            instance.option(value);\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n}\r\nclass TabOptionItemOptionAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        const tabPanel = this.findInstance();\r\n        if (tabPanel) {\r\n            const {\r\n                optionName: optionName,\r\n                item: item,\r\n                value: value\r\n            } = this._options;\r\n            const itemIndex = this._itemsRunTimeInfo.findItemIndexByItem(item);\r\n            if (itemIndex >= 0) {\r\n                tabPanel.option(getFullOptionName(`items[${itemIndex}]`, optionName), value);\r\n                return true\r\n            }\r\n        }\r\n        return false\r\n    }\r\n}\r\nclass SimpleItemTemplateChangedAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        return false\r\n    }\r\n}\r\nclass GroupItemTemplateChangedAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        const preparedItem = this.findPreparedItem();\r\n        if (null != preparedItem && preparedItem._prepareGroupItemTemplate && preparedItem._renderGroupContentTemplate) {\r\n            preparedItem._prepareGroupItemTemplate(this._options.item.template);\r\n            preparedItem._renderGroupContentTemplate();\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n}\r\nclass TabsOptionItemOptionAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        const tabPanel = this.findInstance();\r\n        if (tabPanel) {\r\n            const {\r\n                value: value\r\n            } = this._options;\r\n            tabPanel.option(\"dataSource\", value);\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n}\r\nclass ValidationRulesItemOptionAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        const {\r\n            item: item\r\n        } = this._options;\r\n        const instance = this.findInstance();\r\n        const validator = instance && data(instance.$element()[0], \"dxValidator\");\r\n        if (validator && item) {\r\n            const filterRequired = item => \"required\" === item.type;\r\n            const oldContainsRequired = (validator.option(\"validationRules\") || []).some(filterRequired);\r\n            const newContainsRequired = (item.validationRules || []).some(filterRequired);\r\n            if (!oldContainsRequired && !newContainsRequired || oldContainsRequired && newContainsRequired) {\r\n                validator.option(\"validationRules\", item.validationRules);\r\n                return true\r\n            }\r\n        }\r\n        return false\r\n    }\r\n}\r\nclass CssClassItemOptionAction extends ItemOptionAction {\r\n    tryExecute() {\r\n        const $itemContainer = this.findItemContainer();\r\n        const {\r\n            previousValue: previousValue,\r\n            value: value\r\n        } = this._options;\r\n        if ($itemContainer) {\r\n            $itemContainer.removeClass(previousValue).addClass(value);\r\n            return true\r\n        }\r\n        return false\r\n    }\r\n}\r\nconst tryCreateItemOptionAction = (optionName, itemActionOptions) => {\r\n    switch (optionName) {\r\n        case \"editorOptions\":\r\n        case \"buttonOptions\":\r\n            return new WidgetOptionItemOptionAction(itemActionOptions);\r\n        case \"validationRules\":\r\n            return new ValidationRulesItemOptionAction(itemActionOptions);\r\n        case \"cssClass\":\r\n            return new CssClassItemOptionAction(itemActionOptions);\r\n        case \"badge\":\r\n        case \"disabled\":\r\n        case \"icon\":\r\n        case \"tabTemplate\":\r\n        case \"title\":\r\n            return new TabOptionItemOptionAction(extend(itemActionOptions, {\r\n                optionName: optionName\r\n            }));\r\n        case \"tabs\":\r\n            return new TabsOptionItemOptionAction(itemActionOptions);\r\n        case \"template\": {\r\n            var _itemActionOptions$it, _itemActionOptions$it2;\r\n            const itemType = (null === itemActionOptions || void 0 === itemActionOptions || null === (_itemActionOptions$it = itemActionOptions.item) || void 0 === _itemActionOptions$it ? void 0 : _itemActionOptions$it.itemType) ?? (null === (_itemActionOptions$it2 = itemActionOptions.itemsRunTimeInfo.findPreparedItemByItem(null === itemActionOptions || void 0 === itemActionOptions ? void 0 : itemActionOptions.item)) || void 0 === _itemActionOptions$it2 ? void 0 : _itemActionOptions$it2.itemType);\r\n            if (\"simple\" === itemType) {\r\n                return new SimpleItemTemplateChangedAction(itemActionOptions)\r\n            }\r\n            if (\"group\" === itemType) {\r\n                return new GroupItemTemplateChangedAction(itemActionOptions)\r\n            }\r\n            return new TabOptionItemOptionAction(extend(itemActionOptions, {\r\n                optionName: optionName\r\n            }))\r\n        }\r\n        default:\r\n            return null\r\n    }\r\n};\r\nexport default tryCreateItemOptionAction;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AAGA;AACA;;;;;AAGA,MAAM,qCAAqC,gMAAA,CAAA,UAAgB;IACvD,aAAa;QACT,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,QAAQ;QACjB,MAAM,WAAW,IAAI,CAAC,YAAY;QAClC,IAAI,UAAU;YACV,SAAS,MAAM,CAAC;YAChB,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,MAAM,kCAAkC,gMAAA,CAAA,UAAgB;IACpD,aAAa;QACT,MAAM,WAAW,IAAI,CAAC,YAAY;QAClC,IAAI,UAAU;YACV,MAAM,EACF,YAAY,UAAU,EACtB,MAAM,IAAI,EACV,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,QAAQ;YACjB,MAAM,YAAY,IAAI,CAAC,iBAAiB,CAAC,mBAAmB,CAAC;YAC7D,IAAI,aAAa,GAAG;gBAChB,SAAS,MAAM,CAAC,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,CAAC,MAAM,EAAE,UAAU,CAAC,CAAC,EAAE,aAAa;gBACtE,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM,wCAAwC,gMAAA,CAAA,UAAgB;IAC1D,aAAa;QACT,OAAO;IACX;AACJ;AACA,MAAM,uCAAuC,gMAAA,CAAA,UAAgB;IACzD,aAAa;QACT,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,IAAI,QAAQ,gBAAgB,aAAa,yBAAyB,IAAI,aAAa,2BAA2B,EAAE;YAC5G,aAAa,yBAAyB,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ;YAClE,aAAa,2BAA2B;YACxC,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,MAAM,mCAAmC,gMAAA,CAAA,UAAgB;IACrD,aAAa;QACT,MAAM,WAAW,IAAI,CAAC,YAAY;QAClC,IAAI,UAAU;YACV,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,QAAQ;YACjB,SAAS,MAAM,CAAC,cAAc;YAC9B,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,MAAM,wCAAwC,gMAAA,CAAA,UAAgB;IAC1D,aAAa;QACT,MAAM,EACF,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,QAAQ;QACjB,MAAM,WAAW,IAAI,CAAC,YAAY;QAClC,MAAM,YAAY,YAAY,CAAA,GAAA,4KAAA,CAAA,OAAI,AAAD,EAAE,SAAS,QAAQ,EAAE,CAAC,EAAE,EAAE;QAC3D,IAAI,aAAa,MAAM;YACnB,MAAM,iBAAiB,CAAA,OAAQ,eAAe,KAAK,IAAI;YACvD,MAAM,sBAAsB,CAAC,UAAU,MAAM,CAAC,sBAAsB,EAAE,EAAE,IAAI,CAAC;YAC7E,MAAM,sBAAsB,CAAC,KAAK,eAAe,IAAI,EAAE,EAAE,IAAI,CAAC;YAC9D,IAAI,CAAC,uBAAuB,CAAC,uBAAuB,uBAAuB,qBAAqB;gBAC5F,UAAU,MAAM,CAAC,mBAAmB,KAAK,eAAe;gBACxD,OAAO;YACX;QACJ;QACA,OAAO;IACX;AACJ;AACA,MAAM,iCAAiC,gMAAA,CAAA,UAAgB;IACnD,aAAa;QACT,MAAM,iBAAiB,IAAI,CAAC,iBAAiB;QAC7C,MAAM,EACF,eAAe,aAAa,EAC5B,OAAO,KAAK,EACf,GAAG,IAAI,CAAC,QAAQ;QACjB,IAAI,gBAAgB;YAChB,eAAe,WAAW,CAAC,eAAe,QAAQ,CAAC;YACnD,OAAO;QACX;QACA,OAAO;IACX;AACJ;AACA,MAAM,4BAA4B,CAAC,YAAY;IAC3C,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO,IAAI,6BAA6B;QAC5C,KAAK;YACD,OAAO,IAAI,gCAAgC;QAC/C,KAAK;YACD,OAAO,IAAI,yBAAyB;QACxC,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YACD,OAAO,IAAI,0BAA0B,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,mBAAmB;gBAC3D,YAAY;YAChB;QACJ,KAAK;YACD,OAAO,IAAI,2BAA2B;QAC1C,KAAK;YAAY;gBACb,IAAI,uBAAuB;gBAC3B,MAAM,WAAW,CAAC,SAAS,qBAAqB,KAAK,MAAM,qBAAqB,SAAS,CAAC,wBAAwB,kBAAkB,IAAI,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,QAAQ,KAAK,CAAC,SAAS,CAAC,yBAAyB,kBAAkB,gBAAgB,CAAC,sBAAsB,CAAC,SAAS,qBAAqB,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,IAAI,CAAC,KAAK,KAAK,MAAM,yBAAyB,KAAK,IAAI,uBAAuB,QAAQ;gBACxe,IAAI,aAAa,UAAU;oBACvB,OAAO,IAAI,gCAAgC;gBAC/C;gBACA,IAAI,YAAY,UAAU;oBACtB,OAAO,IAAI,+BAA+B;gBAC9C;gBACA,OAAO,IAAI,0BAA0B,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,mBAAmB;oBAC3D,YAAY;gBAChB;YACJ;QACA;YACI,OAAO;IACf;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1999, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/form/m_form.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/form/m_form.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport \"../../../ui/validation_summary\";\r\nimport \"../../../ui/validation_group\";\r\nimport \"../../ui/form/m_form.layout_manager\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    triggerResizeEvent,\r\n    triggerShownEvent\r\n} from \"../../../common/core/events/visibility_change\";\r\nimport messageLocalization from \"../../../common/core/localization/message\";\r\nimport registerComponent from \"../../../core/component_registrator\";\r\nimport config from \"../../../core/config\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../core/element\";\r\nimport Guid from \"../../../core/guid\";\r\nimport $ from \"../../../core/renderer\";\r\nimport resizeObserverSingleton from \"../../../core/resize_observer\";\r\nimport {\r\n    ensureDefined\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    isDefined,\r\n    isEmptyObject,\r\n    isObject,\r\n    isString\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    defaultScreenFactorFunc,\r\n    getCurrentScreenFactor,\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport Editor from \"../../../ui/editor/editor\";\r\nimport TabPanel from \"../../../ui/tab_panel\";\r\nimport {\r\n    isMaterial,\r\n    isMaterialBased\r\n} from \"../../../ui/themes\";\r\nimport ValidationEngine from \"../../../ui/validation_engine\";\r\nimport Widget, {\r\n    FOCUSED_STATE_CLASS\r\n} from \"../../core/widget/widget\";\r\nimport {\r\n    DROP_DOWN_EDITOR_CLASS\r\n} from \"../../ui/drop_down_editor/m_drop_down_editor\";\r\nimport {\r\n    setLabelWidthByMaxLabelWidth\r\n} from \"../../ui/form/components/m_label\";\r\nimport {\r\n    FIELD_ITEM_CLASS,\r\n    FIELD_ITEM_CONTENT_CLASS,\r\n    FIELD_ITEM_CONTENT_HAS_GROUP_CLASS,\r\n    FIELD_ITEM_CONTENT_HAS_TABS_CLASS,\r\n    FIELD_ITEM_TAB_CLASS,\r\n    FORM_CLASS,\r\n    FORM_FIELD_ITEM_COL_CLASS,\r\n    FORM_GROUP_CAPTION_CLASS,\r\n    FORM_GROUP_CLASS,\r\n    FORM_GROUP_CONTENT_CLASS,\r\n    FORM_GROUP_CUSTOM_CAPTION_CLASS,\r\n    FORM_GROUP_WITH_CAPTION_CLASS,\r\n    FORM_UNDERLINED_CLASS,\r\n    FORM_VALIDATION_SUMMARY,\r\n    GROUP_COL_COUNT_ATTR,\r\n    GROUP_COL_COUNT_CLASS,\r\n    ROOT_SIMPLE_ITEM_CLASS\r\n} from \"../../ui/form/constants\";\r\nimport tryCreateItemOptionAction from \"../../ui/form/m_form.item_options_actions\";\r\nimport {\r\n    default as FormItemsRunTimeInfo\r\n} from \"../../ui/form/m_form.items_runtime_info\";\r\nimport {\r\n    convertToLabelMarkOptions\r\n} from \"../../ui/form/m_form.layout_manager.utils\";\r\nimport {\r\n    concatPaths,\r\n    convertToLayoutManagerOptions,\r\n    createItemPathByIndex,\r\n    getFullOptionName,\r\n    getItemPath,\r\n    getOptionNameFromFullName,\r\n    getTextWithoutSpaces,\r\n    isEqualToDataFieldOrNameOrTitleOrCaption,\r\n    isFullPathContainsTabs,\r\n    tryGetTabPath\r\n} from \"../../ui/form/m_form.utils\";\r\nimport Scrollable from \"../../ui/scroll_view/m_scrollable\";\r\nimport {\r\n    TEXTEDITOR_CLASS,\r\n    TEXTEDITOR_INPUT_CLASS\r\n} from \"../../ui/text_box/m_text_editor.base\";\r\nimport {\r\n    TOOLBAR_CLASS\r\n} from \"../../ui/toolbar/m_constants\";\r\nconst ITEM_OPTIONS_FOR_VALIDATION_UPDATING = [\"items\", \"isRequired\", \"validationRules\", \"visible\"];\r\nclass Form extends Widget {\r\n    _init() {\r\n        super._init();\r\n        this._dirtyFields = new Set;\r\n        this._cachedColCountOptions = [];\r\n        this._itemsRunTimeInfo = new FormItemsRunTimeInfo;\r\n        this._groupsColCount = [];\r\n        this._attachSyncSubscriptions()\r\n    }\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            formID: `dx-${new Guid}`,\r\n            formData: {},\r\n            colCount: 1,\r\n            screenByWidth: defaultScreenFactorFunc,\r\n            labelLocation: \"left\",\r\n            readOnly: false,\r\n            onFieldDataChanged: null,\r\n            customizeItem: null,\r\n            onEditorEnterKey: null,\r\n            minColWidth: 200,\r\n            alignItemLabels: true,\r\n            alignItemLabelsInAllGroups: true,\r\n            alignRootItemLabels: true,\r\n            showColonAfterLabel: true,\r\n            showRequiredMark: true,\r\n            showOptionalMark: false,\r\n            requiredMark: \"*\",\r\n            optionalMark: messageLocalization.format(\"dxForm-optionalMark\"),\r\n            requiredMessage: messageLocalization.getFormatter(\"dxForm-requiredMessage\"),\r\n            showValidationSummary: false,\r\n            scrollingEnabled: false,\r\n            stylingMode: config().editorStylingMode,\r\n            labelMode: \"outside\",\r\n            isDirty: false\r\n        })\r\n    }\r\n    _defaultOptionsRules() {\r\n        return super._defaultOptionsRules().concat([{\r\n            device: () => isMaterialBased(),\r\n            options: {\r\n                labelLocation: \"top\"\r\n            }\r\n        }, {\r\n            device: () => isMaterial(),\r\n            options: {\r\n                showColonAfterLabel: false\r\n            }\r\n        }])\r\n    }\r\n    _setOptionsByReference() {\r\n        super._setOptionsByReference();\r\n        extend(this._optionsByReference, {\r\n            formData: true,\r\n            validationGroup: true\r\n        })\r\n    }\r\n    _getGroupColCount($element) {\r\n        return parseInt($element.attr(GROUP_COL_COUNT_ATTR))\r\n    }\r\n    _applyLabelsWidthByCol($container, index) {\r\n        let options = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};\r\n        let labelMarkOptions = arguments.length > 3 ? arguments[3] : void 0;\r\n        const fieldItemClass = options.inOneColumn ? FIELD_ITEM_CLASS : FORM_FIELD_ITEM_COL_CLASS + index;\r\n        const cssExcludeTabbedSelector = options.excludeTabbed ? `:not(.${FIELD_ITEM_TAB_CLASS})` : \"\";\r\n        setLabelWidthByMaxLabelWidth($container, `.${fieldItemClass}${cssExcludeTabbedSelector}`, labelMarkOptions)\r\n    }\r\n    _applyLabelsWidth($container, excludeTabbed, inOneColumn, colCount, labelMarkOptions) {\r\n        colCount = inOneColumn ? 1 : colCount || this._getGroupColCount($container);\r\n        const applyLabelsOptions = {\r\n            excludeTabbed: excludeTabbed,\r\n            inOneColumn: inOneColumn\r\n        };\r\n        let i;\r\n        for (i = 0; i < colCount; i++) {\r\n            this._applyLabelsWidthByCol($container, i, applyLabelsOptions, labelMarkOptions)\r\n        }\r\n    }\r\n    _getGroupElementsInColumn($container, columnIndex, colCount) {\r\n        const cssColCountSelector = isDefined(colCount) ? `.${GROUP_COL_COUNT_CLASS}${colCount}` : \"\";\r\n        const groupSelector = `.${FORM_FIELD_ITEM_COL_CLASS}${columnIndex} > .${FIELD_ITEM_CONTENT_CLASS} > .${FORM_GROUP_CLASS}${cssColCountSelector}`;\r\n        return $container.find(groupSelector)\r\n    }\r\n    _applyLabelsWidthWithGroups($container, colCount, excludeTabbed, labelMarkOptions) {\r\n        const {\r\n            alignRootItemLabels: alignRootItemLabels\r\n        } = this.option();\r\n        if (true === alignRootItemLabels) {\r\n            const $rootSimpleItems = $container.find(`.${ROOT_SIMPLE_ITEM_CLASS}`);\r\n            for (let colIndex = 0; colIndex < colCount; colIndex++) {\r\n                this._applyLabelsWidthByCol($rootSimpleItems, colIndex, excludeTabbed, labelMarkOptions)\r\n            }\r\n        }\r\n        const alignItemLabelsInAllGroups = this.option(\"alignItemLabelsInAllGroups\");\r\n        if (alignItemLabelsInAllGroups) {\r\n            this._applyLabelsWidthWithNestedGroups($container, colCount, excludeTabbed, labelMarkOptions)\r\n        } else {\r\n            const $groups = this.$element().find(`.${FORM_GROUP_CLASS}`);\r\n            let i;\r\n            for (i = 0; i < $groups.length; i++) {\r\n                this._applyLabelsWidth($groups.eq(i), excludeTabbed, void 0, void 0, labelMarkOptions)\r\n            }\r\n        }\r\n    }\r\n    _applyLabelsWidthWithNestedGroups($container, colCount, excludeTabbed, labelMarkOptions) {\r\n        const applyLabelsOptions = {\r\n            excludeTabbed: excludeTabbed\r\n        };\r\n        let colIndex;\r\n        let groupsColIndex;\r\n        let groupColIndex;\r\n        let $groupsByCol;\r\n        for (colIndex = 0; colIndex < colCount; colIndex++) {\r\n            $groupsByCol = this._getGroupElementsInColumn($container, colIndex);\r\n            this._applyLabelsWidthByCol($groupsByCol, 0, applyLabelsOptions, labelMarkOptions);\r\n            for (groupsColIndex = 0; groupsColIndex < this._groupsColCount.length; groupsColIndex++) {\r\n                $groupsByCol = this._getGroupElementsInColumn($container, colIndex, this._groupsColCount[groupsColIndex]);\r\n                const groupColCount = this._getGroupColCount($groupsByCol);\r\n                for (groupColIndex = 1; groupColIndex < groupColCount; groupColIndex++) {\r\n                    this._applyLabelsWidthByCol($groupsByCol, groupColIndex, applyLabelsOptions, labelMarkOptions)\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _labelLocation() {\r\n        const {\r\n            labelLocation: labelLocation\r\n        } = this.option();\r\n        return labelLocation\r\n    }\r\n    _alignLabelsInColumn(_ref) {\r\n        let {\r\n            layoutManager: layoutManager,\r\n            inOneColumn: inOneColumn,\r\n            $container: $container,\r\n            excludeTabbed: excludeTabbed,\r\n            items: items\r\n        } = _ref;\r\n        if (!hasWindow() || \"top\" === this._labelLocation()) {\r\n            return\r\n        }\r\n        const labelMarkOptions = convertToLabelMarkOptions(layoutManager._getMarkOptions());\r\n        if (inOneColumn) {\r\n            this._applyLabelsWidth($container, excludeTabbed, true, void 0, labelMarkOptions)\r\n        } else if (this._checkGrouping(items)) {\r\n            this._applyLabelsWidthWithGroups($container, layoutManager._getColCount(), excludeTabbed, labelMarkOptions)\r\n        } else {\r\n            this._applyLabelsWidth($container, excludeTabbed, false, layoutManager._getColCount(), labelMarkOptions)\r\n        }\r\n    }\r\n    _prepareFormData() {\r\n        if (!isDefined(this.option(\"formData\"))) {\r\n            this.option(\"formData\", {})\r\n        }\r\n    }\r\n    _setStylingModeClass() {\r\n        const {\r\n            stylingMode: stylingMode\r\n        } = this.option();\r\n        if (\"underlined\" === stylingMode) {\r\n            this.$element().addClass(FORM_UNDERLINED_CLASS)\r\n        }\r\n    }\r\n    _initMarkup() {\r\n        ValidationEngine.addGroup(this._getValidationGroup(), false);\r\n        this._clearCachedInstances();\r\n        this._prepareFormData();\r\n        this.$element().addClass(FORM_CLASS);\r\n        this._setStylingModeClass();\r\n        super._initMarkup();\r\n        this.setAria(\"role\", \"form\", this.$element());\r\n        if (this.option(\"scrollingEnabled\")) {\r\n            this._renderScrollable()\r\n        }\r\n        this._renderLayout();\r\n        this._renderValidationSummary();\r\n        this._lastMarkupScreenFactor = this._targetScreenFactor || this._getCurrentScreenFactor();\r\n        this._attachResizeObserverSubscription()\r\n    }\r\n    _attachResizeObserverSubscription() {\r\n        if (hasWindow()) {\r\n            const formRootElement = this.$element().get(0);\r\n            resizeObserverSingleton.unobserve(formRootElement);\r\n            resizeObserverSingleton.observe(formRootElement, (() => {\r\n                this._resizeHandler()\r\n            }))\r\n        }\r\n    }\r\n    _resizeHandler() {\r\n        if (this._cachedLayoutManagers.length) {\r\n            each(this._cachedLayoutManagers, ((_, layoutManager) => {\r\n                var _layoutManager$option;\r\n                null === (_layoutManager$option = layoutManager.option(\"onLayoutChanged\")) || void 0 === _layoutManager$option || _layoutManager$option(layoutManager.isSingleColumnMode())\r\n            }))\r\n        }\r\n    }\r\n    _getCurrentScreenFactor() {\r\n        return hasWindow() ? getCurrentScreenFactor(this.option(\"screenByWidth\")) : \"lg\"\r\n    }\r\n    _clearCachedInstances() {\r\n        this._itemsRunTimeInfo.clear();\r\n        this._cachedLayoutManagers = []\r\n    }\r\n    _alignLabels(layoutManager, inOneColumn) {\r\n        this._alignLabelsInColumn({\r\n            $container: this.$element(),\r\n            layoutManager: layoutManager,\r\n            excludeTabbed: true,\r\n            items: this.option(\"items\"),\r\n            inOneColumn: inOneColumn\r\n        });\r\n        triggerResizeEvent(this.$element().find(`.${TOOLBAR_CLASS}`))\r\n    }\r\n    _clean() {\r\n        this._clearValidationSummary();\r\n        super._clean();\r\n        this._groupsColCount = [];\r\n        this._cachedColCountOptions = [];\r\n        this._lastMarkupScreenFactor = void 0;\r\n        resizeObserverSingleton.unobserve(this.$element().get(0))\r\n    }\r\n    _renderScrollable() {\r\n        const useNativeScrolling = this.option(\"useNativeScrolling\");\r\n        this._scrollable = new Scrollable(this.$element(), {\r\n            useNative: !!useNativeScrolling,\r\n            useSimulatedScrollbar: !useNativeScrolling,\r\n            useKeyboard: false,\r\n            direction: \"both\",\r\n            bounceEnabled: false\r\n        })\r\n    }\r\n    _getContent() {\r\n        var _this$_scrollable;\r\n        return this.option(\"scrollingEnabled\") ? $(null === (_this$_scrollable = this._scrollable) || void 0 === _this$_scrollable ? void 0 : _this$_scrollable.content()) : this.$element()\r\n    }\r\n    _clearValidationSummary() {\r\n        var _this$_$validationSum;\r\n        null === (_this$_$validationSum = this._$validationSummary) || void 0 === _this$_$validationSum || _this$_$validationSum.remove();\r\n        this._$validationSummary = void 0;\r\n        this._validationSummary = void 0\r\n    }\r\n    _renderValidationSummary() {\r\n        this._clearValidationSummary();\r\n        if (this.option(\"showValidationSummary\")) {\r\n            this._$validationSummary = $(\"<div>\").addClass(FORM_VALIDATION_SUMMARY).appendTo(this._getContent());\r\n            this._validationSummary = this._$validationSummary.dxValidationSummary({\r\n                validationGroup: this._getValidationGroup()\r\n            }).dxValidationSummary(\"instance\")\r\n        }\r\n    }\r\n    _prepareItems(items, parentIsTabbedItem, currentPath, isTabs) {\r\n        if (items) {\r\n            const result = [];\r\n            for (let i = 0; i < items.length; i++) {\r\n                let item = items[i];\r\n                const path = concatPaths(currentPath, createItemPathByIndex(i, isTabs));\r\n                const itemRunTimeInfo = {\r\n                    item: item,\r\n                    itemIndex: i,\r\n                    path: path\r\n                };\r\n                const guid = this._itemsRunTimeInfo.add(itemRunTimeInfo);\r\n                if (isString(item)) {\r\n                    item = {\r\n                        dataField: item\r\n                    }\r\n                }\r\n                if (isObject(item)) {\r\n                    const preparedItem = _extends({}, item);\r\n                    itemRunTimeInfo.preparedItem = preparedItem;\r\n                    preparedItem.guid = guid;\r\n                    this._tryPrepareGroupItemCaption(preparedItem);\r\n                    this._tryPrepareGroupItem(preparedItem);\r\n                    this._tryPrepareTabbedItem(preparedItem, path);\r\n                    this._tryPrepareItemTemplate(preparedItem);\r\n                    if (parentIsTabbedItem) {\r\n                        preparedItem.cssItemClass = FIELD_ITEM_TAB_CLASS\r\n                    }\r\n                    if (preparedItem.items) {\r\n                        preparedItem.items = this._prepareItems(preparedItem.items, parentIsTabbedItem, path)\r\n                    }\r\n                    result.push(preparedItem)\r\n                } else {\r\n                    result.push(item)\r\n                }\r\n            }\r\n            return result\r\n        }\r\n    }\r\n    _tryPrepareGroupItemCaption(item) {\r\n        if (\"group\" === item.itemType) {\r\n            item._prepareGroupCaptionTemplate = captionTemplate => {\r\n                if (item.captionTemplate) {\r\n                    item.groupCaptionTemplate = this._getTemplate(captionTemplate)\r\n                }\r\n                item.captionTemplate = this._itemGroupTemplate.bind(this, item)\r\n            };\r\n            item._prepareGroupCaptionTemplate(item.captionTemplate)\r\n        }\r\n    }\r\n    _tryPrepareGroupItem(item) {\r\n        if (\"group\" === item.itemType) {\r\n            item.alignItemLabels = ensureDefined(item.alignItemLabels, true);\r\n            item._prepareGroupItemTemplate = itemTemplate => {\r\n                if (item.template) {\r\n                    item.groupContentTemplate = this._getTemplate(itemTemplate)\r\n                }\r\n                item.template = this._itemGroupTemplate.bind(this, item)\r\n            };\r\n            item._prepareGroupItemTemplate(item.template)\r\n        }\r\n    }\r\n    _tryPrepareTabbedItem(item, path) {\r\n        if (\"tabbed\" === item.itemType) {\r\n            item.template = this._itemTabbedTemplate.bind(this, item);\r\n            item.tabs = this._prepareItems(item.tabs, true, path, true)\r\n        }\r\n    }\r\n    _tryPrepareItemTemplate(item) {\r\n        if (item.template) {\r\n            item.template = this._getTemplate(item.template)\r\n        }\r\n    }\r\n    _checkGrouping(items) {\r\n        if (items) {\r\n            for (let i = 0; i < items.length; i++) {\r\n                const item = items[i];\r\n                if (\"group\" === item.itemType) {\r\n                    return true\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _renderLayout() {\r\n        const that = this;\r\n        let items = that.option(\"items\");\r\n        const $content = that._getContent();\r\n        items = that._prepareItems(items);\r\n        that._rootLayoutManager = that._renderLayoutManager($content, this._createLayoutManagerOptions(items, {\r\n            isRoot: true,\r\n            colCount: that.option(\"colCount\"),\r\n            alignItemLabels: that.option(\"alignItemLabels\"),\r\n            screenByWidth: this.option(\"screenByWidth\"),\r\n            colCountByScreen: this.option(\"colCountByScreen\"),\r\n            onLayoutChanged(inOneColumn) {\r\n                that._alignLabels.bind(that)(that._rootLayoutManager, inOneColumn)\r\n            },\r\n            onContentReady(e) {\r\n                that._alignLabels(e.component, e.component.isSingleColumnMode())\r\n            }\r\n        }))\r\n    }\r\n    _tryGetItemsForTemplate(item) {\r\n        return item.items || []\r\n    }\r\n    _itemTabbedTemplate(item, e, $container) {\r\n        const $tabPanel = $(\"<div>\").appendTo($container);\r\n        const tabPanelOptions = extend({}, item.tabPanelOptions, {\r\n            dataSource: item.tabs,\r\n            onItemRendered: args => {\r\n                var _item$tabPanelOptions, _item$tabPanelOptions2;\r\n                null === (_item$tabPanelOptions = item.tabPanelOptions) || void 0 === _item$tabPanelOptions || null === (_item$tabPanelOptions2 = _item$tabPanelOptions.onItemRendered) || void 0 === _item$tabPanelOptions2 || _item$tabPanelOptions2.call(_item$tabPanelOptions, args);\r\n                triggerShownEvent(args.itemElement)\r\n            },\r\n            itemTemplate: (itemData, e, container) => {\r\n                const $container = $(container);\r\n                const alignItemLabels = ensureDefined(itemData.alignItemLabels, true);\r\n                const layoutManager = this._renderLayoutManager($container, this._createLayoutManagerOptions(this._tryGetItemsForTemplate(itemData), {\r\n                    colCount: itemData.colCount,\r\n                    alignItemLabels: alignItemLabels,\r\n                    screenByWidth: this.option(\"screenByWidth\"),\r\n                    colCountByScreen: itemData.colCountByScreen,\r\n                    cssItemClass: itemData.cssItemClass,\r\n                    onLayoutChanged: inOneColumn => {\r\n                        this._alignLabelsInColumn({\r\n                            $container: $container,\r\n                            layoutManager: layoutManager,\r\n                            items: itemData.items,\r\n                            inOneColumn: inOneColumn\r\n                        })\r\n                    }\r\n                }));\r\n                if (this._itemsRunTimeInfo) {\r\n                    this._itemsRunTimeInfo.extendRunTimeItemInfoByKey(itemData.guid, {\r\n                        layoutManager: layoutManager\r\n                    })\r\n                }\r\n                if (alignItemLabels) {\r\n                    this._alignLabelsInColumn({\r\n                        $container: $container,\r\n                        layoutManager: layoutManager,\r\n                        items: itemData.items,\r\n                        inOneColumn: layoutManager.isSingleColumnMode()\r\n                    })\r\n                }\r\n            }\r\n        });\r\n        const tryUpdateTabPanelInstance = (items, instance) => {\r\n            if (Array.isArray(items)) {\r\n                items.forEach((item => this._itemsRunTimeInfo.extendRunTimeItemInfoByKey(item.guid, {\r\n                    widgetInstance: instance\r\n                })))\r\n            }\r\n        };\r\n        const tabPanel = this._createComponent($tabPanel, TabPanel, tabPanelOptions);\r\n        $($container).parent().addClass(FIELD_ITEM_CONTENT_HAS_TABS_CLASS);\r\n        tabPanel.on(\"optionChanged\", (e => {\r\n            if (\"dataSource\" === e.fullName) {\r\n                tryUpdateTabPanelInstance(e.value, e.component)\r\n            }\r\n        }));\r\n        tryUpdateTabPanelInstance([{\r\n            guid: item.guid\r\n        }, ...item.tabs ?? []], tabPanel)\r\n    }\r\n    _itemGroupCaptionTemplate(item, $group, id) {\r\n        if (item.groupCaptionTemplate) {\r\n            const $captionTemplate = $(\"<div>\").addClass(FORM_GROUP_CUSTOM_CAPTION_CLASS).attr(\"id\", id).appendTo($group);\r\n            item._renderGroupCaptionTemplate = () => {\r\n                const data = {\r\n                    component: this,\r\n                    caption: item.caption,\r\n                    name: item.name\r\n                };\r\n                item.groupCaptionTemplate.render({\r\n                    model: data,\r\n                    container: getPublicElement($captionTemplate)\r\n                })\r\n            };\r\n            item._renderGroupCaptionTemplate();\r\n            return\r\n        }\r\n        if (item.caption) {\r\n            $(\"<span>\").addClass(FORM_GROUP_CAPTION_CLASS).text(item.caption).attr(\"id\", id).appendTo($group)\r\n        }\r\n    }\r\n    _itemGroupContentTemplate(item, $group) {\r\n        const $groupContent = $(\"<div>\").addClass(FORM_GROUP_CONTENT_CLASS).appendTo($group);\r\n        if (item.groupContentTemplate) {\r\n            item._renderGroupContentTemplate = () => {\r\n                $groupContent.empty();\r\n                const data = {\r\n                    formData: this.option(\"formData\"),\r\n                    component: this\r\n                };\r\n                item.groupContentTemplate.render({\r\n                    model: data,\r\n                    container: getPublicElement($groupContent)\r\n                })\r\n            };\r\n            item._renderGroupContentTemplate()\r\n        } else {\r\n            var _this$_itemsRunTimeIn;\r\n            const layoutManager = this._renderLayoutManager($groupContent, this._createLayoutManagerOptions(this._tryGetItemsForTemplate(item), {\r\n                colCount: item.colCount,\r\n                colCountByScreen: item.colCountByScreen,\r\n                alignItemLabels: item.alignItemLabels,\r\n                cssItemClass: item.cssItemClass\r\n            }));\r\n            null === (_this$_itemsRunTimeIn = this._itemsRunTimeInfo) || void 0 === _this$_itemsRunTimeIn || _this$_itemsRunTimeIn.extendRunTimeItemInfoByKey(item.guid, {\r\n                layoutManager: layoutManager\r\n            });\r\n            const colCount = layoutManager._getColCount();\r\n            if (!this._groupsColCount.includes(colCount)) {\r\n                this._groupsColCount.push(colCount)\r\n            }\r\n            $group.addClass(GROUP_COL_COUNT_CLASS + colCount);\r\n            $group.attr(GROUP_COL_COUNT_ATTR, colCount)\r\n        }\r\n    }\r\n    _itemGroupTemplate(item, options, $container) {\r\n        const {\r\n            id: id\r\n        } = options.editorOptions.inputAttr;\r\n        const $group = $(\"<div>\").toggleClass(FORM_GROUP_WITH_CAPTION_CLASS, isDefined(item.caption) && item.caption.length).addClass(FORM_GROUP_CLASS).appendTo($container);\r\n        const groupAria = {\r\n            role: \"group\",\r\n            labelledby: id\r\n        };\r\n        this.setAria(groupAria, $group);\r\n        $($container).parent().addClass(FIELD_ITEM_CONTENT_HAS_GROUP_CLASS);\r\n        this._itemGroupCaptionTemplate(item, $group, id);\r\n        this._itemGroupContentTemplate(item, $group)\r\n    }\r\n    _createLayoutManagerOptions(items, extendedLayoutManagerOptions) {\r\n        return convertToLayoutManagerOptions({\r\n            form: this,\r\n            formOptions: this.option(),\r\n            $formElement: this.$element(),\r\n            items: items,\r\n            validationGroup: this._getValidationGroup(),\r\n            extendedLayoutManagerOptions: extendedLayoutManagerOptions,\r\n            onFieldDataChanged: args => {\r\n                if (!this._isDataUpdating) {\r\n                    this._triggerOnFieldDataChanged(args)\r\n                }\r\n            },\r\n            onContentReady: args => {\r\n                var _extendedLayoutManage;\r\n                this._itemsRunTimeInfo.addItemsOrExtendFrom(args.component._itemsRunTimeInfo);\r\n                null === (_extendedLayoutManage = extendedLayoutManagerOptions.onContentReady) || void 0 === _extendedLayoutManage || _extendedLayoutManage.call(extendedLayoutManagerOptions, args)\r\n            },\r\n            onDisposing: _ref2 => {\r\n                let {\r\n                    component: component\r\n                } = _ref2;\r\n                const nestedItemsRunTimeInfo = component.getItemsRunTimeInfo();\r\n                this._itemsRunTimeInfo.removeItemsByItems(nestedItemsRunTimeInfo)\r\n            },\r\n            onFieldItemRendered: () => {\r\n                var _this$_validationSumm;\r\n                null === (_this$_validationSumm = this._validationSummary) || void 0 === _this$_validationSumm || _this$_validationSumm.refreshValidationGroup()\r\n            }\r\n        })\r\n    }\r\n    _renderLayoutManager($parent, layoutManagerOptions) {\r\n        const baseColCountByScreen = {\r\n            lg: layoutManagerOptions.colCount,\r\n            md: layoutManagerOptions.colCount,\r\n            sm: layoutManagerOptions.colCount,\r\n            xs: 1\r\n        };\r\n        this._cachedColCountOptions.push({\r\n            colCountByScreen: extend(baseColCountByScreen, layoutManagerOptions.colCountByScreen)\r\n        });\r\n        const $element = $(\"<div>\");\r\n        $element.appendTo($parent);\r\n        const instance = this._createComponent($element, \"dxLayoutManager\", layoutManagerOptions);\r\n        instance.on(\"autoColCountChanged\", (() => {\r\n            this._clearAutoColCountChangedTimeout();\r\n            this.autoColCountChangedTimeoutId = setTimeout((() => !this._disposed && this._refresh()), 0)\r\n        }));\r\n        this._cachedLayoutManagers.push(instance);\r\n        return instance\r\n    }\r\n    _getValidationGroup() {\r\n        return this.option(\"validationGroup\") || this\r\n    }\r\n    _createComponent($element, type, config) {\r\n        config = config || {};\r\n        this._extendConfig(config, {\r\n            readOnly: this.option(\"readOnly\")\r\n        });\r\n        return super._createComponent($element, type, config)\r\n    }\r\n    _attachSyncSubscriptions() {\r\n        const that = this;\r\n        that.on(\"optionChanged\", (args => {\r\n            const optionFullName = args.fullName;\r\n            if (\"formData\" === optionFullName) {\r\n                if (!isDefined(args.value)) {\r\n                    that._options.silent(\"formData\", args.value = {})\r\n                }\r\n                that._triggerOnFieldDataChangedByDataSet(args.value)\r\n            }\r\n            if (that._cachedLayoutManagers.length) {\r\n                each(that._cachedLayoutManagers, ((index, layoutManager) => {\r\n                    if (\"formData\" === optionFullName) {\r\n                        that._isDataUpdating = true;\r\n                        layoutManager.option(\"layoutData\", args.value);\r\n                        that._isDataUpdating = false\r\n                    }\r\n                    if (\"readOnly\" === args.name || \"disabled\" === args.name) {\r\n                        layoutManager.option(optionFullName, args.value)\r\n                    }\r\n                }))\r\n            }\r\n        }))\r\n    }\r\n    _optionChanged(args) {\r\n        const splitFullName = args.fullName.split(\".\");\r\n        if (splitFullName.length > 1 && -1 !== splitFullName[0].search(\"items\") && this._itemsOptionChangedHandler(args)) {\r\n            return\r\n        }\r\n        if (splitFullName.length > 1 && -1 !== splitFullName[0].search(\"formData\") && this._formDataOptionChangedHandler(args)) {\r\n            return\r\n        }\r\n        this._defaultOptionChangedHandler(args)\r\n    }\r\n    _defaultOptionChangedHandler(args) {\r\n        switch (args.name) {\r\n            case \"formData\":\r\n                if (!this.option(\"items\")) {\r\n                    this._invalidate()\r\n                } else if (isEmptyObject(args.value)) {\r\n                    this._clear()\r\n                }\r\n                break;\r\n            case \"onFieldDataChanged\":\r\n            case \"alignRootItemLabels\":\r\n            case \"readOnly\":\r\n            case \"isDirty\":\r\n                break;\r\n            case \"items\":\r\n            case \"colCount\":\r\n            case \"onEditorEnterKey\":\r\n            case \"labelLocation\":\r\n            case \"labelMode\":\r\n            case \"alignItemLabels\":\r\n            case \"showColonAfterLabel\":\r\n            case \"customizeItem\":\r\n            case \"alignItemLabelsInAllGroups\":\r\n            case \"showRequiredMark\":\r\n            case \"showOptionalMark\":\r\n            case \"requiredMark\":\r\n            case \"optionalMark\":\r\n            case \"requiredMessage\":\r\n            case \"scrollingEnabled\":\r\n            case \"formID\":\r\n            case \"colCountByScreen\":\r\n            case \"screenByWidth\":\r\n            case \"stylingMode\":\r\n                this._invalidate();\r\n                break;\r\n            case \"showValidationSummary\":\r\n                this._renderValidationSummary();\r\n                break;\r\n            case \"minColWidth\": {\r\n                const {\r\n                    colCount: colCount\r\n                } = this.option();\r\n                if (\"auto\" === colCount) {\r\n                    this._invalidate()\r\n                }\r\n                break\r\n            }\r\n            case \"width\":\r\n                super._optionChanged(args);\r\n                this._rootLayoutManager.option(args.name, args.value);\r\n                this._alignLabels(this._rootLayoutManager, this._rootLayoutManager.isSingleColumnMode());\r\n                break;\r\n            case \"validationGroup\":\r\n                ValidationEngine.removeGroup(args.previousValue || this);\r\n                this._invalidate();\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    _itemsOptionChangedHandler(args) {\r\n        const nameParts = args.fullName.split(\".\");\r\n        const {\r\n            value: value\r\n        } = args;\r\n        const itemPath = this._getItemPath(nameParts);\r\n        const item = this.option(itemPath);\r\n        const optionNameWithoutPath = args.fullName.replace(`${itemPath}.`, \"\");\r\n        const simpleOptionName = optionNameWithoutPath.split(\".\")[0].replace(/\\[\\d+]/, \"\");\r\n        const itemAction = this._tryCreateItemOptionAction(simpleOptionName, item, item[simpleOptionName], args.previousValue, itemPath);\r\n        let result = this._tryExecuteItemOptionAction(itemAction) || this._tryChangeLayoutManagerItemOption(args.fullName, value);\r\n        if (!result && item) {\r\n            this._changeItemOption(item, optionNameWithoutPath, value);\r\n            const items = this._generateItemsFromData(this.option(\"items\"));\r\n            this.option(\"items\", items);\r\n            result = true\r\n        }\r\n        return result\r\n    }\r\n    _formDataOptionChangedHandler(args) {\r\n        const nameParts = args.fullName.split(\".\");\r\n        const {\r\n            value: value\r\n        } = args;\r\n        const dataField = nameParts.slice(1).join(\".\");\r\n        const editor = this.getEditor(dataField);\r\n        if (editor) {\r\n            editor.option(\"value\", value)\r\n        } else {\r\n            this._triggerOnFieldDataChanged({\r\n                dataField: dataField,\r\n                value: value\r\n            })\r\n        }\r\n        return true\r\n    }\r\n    _tryCreateItemOptionAction(optionName, item, value, previousValue, itemPath) {\r\n        if (\"tabs\" === optionName) {\r\n            this._itemsRunTimeInfo.removeItemsByPathStartWith(`${itemPath}.tabs`);\r\n            value = this._prepareItems(value, true, itemPath, true)\r\n        }\r\n        return tryCreateItemOptionAction(optionName, {\r\n            item: item,\r\n            value: value,\r\n            previousValue: previousValue,\r\n            itemsRunTimeInfo: this._itemsRunTimeInfo\r\n        })\r\n    }\r\n    _tryExecuteItemOptionAction(action) {\r\n        return null === action || void 0 === action ? void 0 : action.tryExecute()\r\n    }\r\n    _updateValidationGroupAndSummaryIfNeeded(fullName) {\r\n        const optionName = getOptionNameFromFullName(fullName);\r\n        if (ITEM_OPTIONS_FOR_VALIDATION_UPDATING.includes(optionName)) {\r\n            ValidationEngine.addGroup(this._getValidationGroup(), false);\r\n            if (this.option(\"showValidationSummary\")) {\r\n                var _this$_validationSumm2;\r\n                null === (_this$_validationSumm2 = this._validationSummary) || void 0 === _this$_validationSumm2 || _this$_validationSumm2.refreshValidationGroup()\r\n            }\r\n        }\r\n    }\r\n    _setLayoutManagerItemOption(layoutManager, optionName, value, path) {\r\n        if (this._updateLockCount > 0) {\r\n            !layoutManager._updateLockCount && layoutManager.beginUpdate();\r\n            const key = this._itemsRunTimeInfo.findKeyByPath(path);\r\n            this.postponedOperations.add(key, (() => {\r\n                !layoutManager._disposed && layoutManager.endUpdate();\r\n                return Deferred().resolve()\r\n            }))\r\n        }\r\n        const contentReadyHandler = e => {\r\n            e.component.off(\"contentReady\", contentReadyHandler);\r\n            if (isFullPathContainsTabs(path)) {\r\n                const tabPath = tryGetTabPath(path);\r\n                const tabLayoutManager = this._itemsRunTimeInfo.findGroupOrTabLayoutManagerByPath(tabPath);\r\n                if (tabLayoutManager) {\r\n                    this._alignLabelsInColumn({\r\n                        items: tabLayoutManager.option(\"items\"),\r\n                        layoutManager: tabLayoutManager,\r\n                        $container: tabLayoutManager.$element(),\r\n                        inOneColumn: tabLayoutManager.isSingleColumnMode()\r\n                    })\r\n                }\r\n            } else {\r\n                this._alignLabels(this._rootLayoutManager, this._rootLayoutManager.isSingleColumnMode())\r\n            }\r\n        };\r\n        layoutManager.on(\"contentReady\", contentReadyHandler);\r\n        layoutManager.option(optionName, value);\r\n        this._updateValidationGroupAndSummaryIfNeeded(optionName)\r\n    }\r\n    _tryChangeLayoutManagerItemOption(fullName, value) {\r\n        const nameParts = fullName.split(\".\");\r\n        const optionName = getOptionNameFromFullName(fullName);\r\n        if (\"items\" === optionName && nameParts.length > 1) {\r\n            const itemPath = this._getItemPath(nameParts);\r\n            const layoutManager = this._itemsRunTimeInfo.findGroupOrTabLayoutManagerByPath(itemPath);\r\n            if (layoutManager) {\r\n                this._itemsRunTimeInfo.removeItemsByItems(layoutManager.getItemsRunTimeInfo());\r\n                const items = this._prepareItems(value, false, itemPath);\r\n                this._setLayoutManagerItemOption(layoutManager, optionName, items, itemPath);\r\n                return true\r\n            }\r\n        } else if (nameParts.length > 2) {\r\n            const endPartIndex = nameParts.length - 2;\r\n            const itemPath = this._getItemPath(nameParts.slice(0, endPartIndex));\r\n            const layoutManager = this._itemsRunTimeInfo.findGroupOrTabLayoutManagerByPath(itemPath);\r\n            if (layoutManager) {\r\n                const fullOptionName = getFullOptionName(nameParts[endPartIndex], optionName);\r\n                if (\"editorType\" === optionName) {\r\n                    if (layoutManager.option(fullOptionName) !== value) {\r\n                        return false\r\n                    }\r\n                }\r\n                if (\"visible\" === optionName) {\r\n                    const formItems = this.option(getFullOptionName(itemPath, \"items\"));\r\n                    if (null !== formItems && void 0 !== formItems && formItems.length) {\r\n                        const layoutManagerItems = layoutManager.option(\"items\");\r\n                        formItems.forEach(((item, index) => {\r\n                            const layoutItem = layoutManagerItems[index];\r\n                            layoutItem.visibleIndex = item.visibleIndex\r\n                        }))\r\n                    }\r\n                }\r\n                this._setLayoutManagerItemOption(layoutManager, fullOptionName, value, itemPath);\r\n                return true\r\n            }\r\n        }\r\n        return false\r\n    }\r\n    _tryChangeLayoutManagerItemOptions(itemPath, options) {\r\n        let result;\r\n        this.beginUpdate();\r\n        each(options, ((optionName, optionValue) => {\r\n            result = this._tryChangeLayoutManagerItemOption(getFullOptionName(itemPath, optionName), optionValue);\r\n            if (!result) {\r\n                return false\r\n            }\r\n        }));\r\n        this.endUpdate();\r\n        return result\r\n    }\r\n    _getItemPath(nameParts) {\r\n        let itemPath = nameParts[0];\r\n        let i;\r\n        for (i = 1; i < nameParts.length; i++) {\r\n            if (-1 !== nameParts[i].search(/items\\[\\d+]|tabs\\[\\d+]/)) {\r\n                itemPath += `.${nameParts[i]}`\r\n            } else {\r\n                break\r\n            }\r\n        }\r\n        return itemPath\r\n    }\r\n    _triggerOnFieldDataChanged(args) {\r\n        this._updateIsDirty(args.dataField);\r\n        this._createActionByOption(\"onFieldDataChanged\")(args)\r\n    }\r\n    _triggerOnFieldDataChangedByDataSet(data) {\r\n        if (data && isObject(data)) {\r\n            Object.keys(data).forEach((key => {\r\n                this._triggerOnFieldDataChanged({\r\n                    dataField: key,\r\n                    value: data[key]\r\n                })\r\n            }))\r\n        }\r\n    }\r\n    _updateFieldValue(dataField, value) {\r\n        if (isDefined(this.option(\"formData\"))) {\r\n            const editor = this.getEditor(dataField);\r\n            this.option(`formData.${dataField}`, value);\r\n            if (editor) {\r\n                const editorValue = editor.option(\"value\");\r\n                if (editorValue !== value) {\r\n                    editor.option(\"value\", value)\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _generateItemsFromData(items) {\r\n        const formData = this.option(\"formData\");\r\n        const result = [];\r\n        if (!items && isDefined(formData)) {\r\n            each(formData, (dataField => {\r\n                result.push({\r\n                    dataField: dataField\r\n                })\r\n            }))\r\n        }\r\n        if (items) {\r\n            each(items, ((index, item) => {\r\n                if (isObject(item)) {\r\n                    result.push(item)\r\n                } else {\r\n                    result.push({\r\n                        dataField: item\r\n                    })\r\n                }\r\n            }))\r\n        }\r\n        return result\r\n    }\r\n    _getItemByField(field, items) {\r\n        const that = this;\r\n        const fieldParts = isObject(field) ? field : that._getFieldParts(field);\r\n        const {\r\n            fieldName: fieldName\r\n        } = fieldParts;\r\n        const {\r\n            fieldPath: fieldPath\r\n        } = fieldParts;\r\n        let resultItem;\r\n        if (items.length) {\r\n            each(items, ((index, item) => {\r\n                const {\r\n                    itemType: itemType\r\n                } = item;\r\n                if (fieldPath.length) {\r\n                    const path = fieldPath.slice();\r\n                    item = that._getItemByFieldPath(path, fieldName, item)\r\n                } else if (\"group\" === itemType && !(item.caption || item.name) || \"tabbed\" === itemType && !item.name) {\r\n                    const subItemsField = that._getSubItemField(itemType);\r\n                    item.items = that._generateItemsFromData(item.items);\r\n                    item = that._getItemByField({\r\n                        fieldName: fieldName,\r\n                        fieldPath: fieldPath\r\n                    }, item[subItemsField])\r\n                }\r\n                if (isEqualToDataFieldOrNameOrTitleOrCaption(item, fieldName)) {\r\n                    resultItem = item;\r\n                    return false\r\n                }\r\n            }))\r\n        }\r\n        return resultItem\r\n    }\r\n    _getFieldParts(field) {\r\n        let fieldName = field;\r\n        let separatorIndex = fieldName.indexOf(\".\");\r\n        const resultPath = [];\r\n        while (-1 !== separatorIndex) {\r\n            resultPath.push(fieldName.substr(0, separatorIndex));\r\n            fieldName = fieldName.substr(separatorIndex + 1);\r\n            separatorIndex = fieldName.indexOf(\".\")\r\n        }\r\n        return {\r\n            fieldName: fieldName,\r\n            fieldPath: resultPath.reverse()\r\n        }\r\n    }\r\n    _getItemByFieldPath(path, fieldName, item) {\r\n        const that = this;\r\n        const {\r\n            itemType: itemType\r\n        } = item;\r\n        const subItemsField = that._getSubItemField(itemType);\r\n        const isItemWithSubItems = \"group\" === itemType || \"tabbed\" === itemType || item.title;\r\n        let result;\r\n        do {\r\n            if (isItemWithSubItems) {\r\n                const name = item.name || item.caption || item.title;\r\n                const isGroupWithName = isDefined(name);\r\n                const nameWithoutSpaces = getTextWithoutSpaces(name);\r\n                let pathNode;\r\n                item[subItemsField] = that._generateItemsFromData(item[subItemsField]);\r\n                if (isGroupWithName) {\r\n                    pathNode = path.pop()\r\n                }\r\n                if (!path.length) {\r\n                    result = that._getItemByField(fieldName, item[subItemsField]);\r\n                    if (result) {\r\n                        break\r\n                    }\r\n                }\r\n                if (!isGroupWithName || isGroupWithName && nameWithoutSpaces === pathNode) {\r\n                    if (path.length) {\r\n                        result = that._searchItemInEverySubItem(path, fieldName, item[subItemsField])\r\n                    }\r\n                }\r\n            } else {\r\n                break\r\n            }\r\n        } while (path.length && !isDefined(result));\r\n        return result\r\n    }\r\n    _getSubItemField(itemType) {\r\n        return \"tabbed\" === itemType ? \"tabs\" : \"items\"\r\n    }\r\n    _searchItemInEverySubItem(path, fieldName, items) {\r\n        const that = this;\r\n        let result;\r\n        each(items, ((index, groupItem) => {\r\n            result = that._getItemByFieldPath(path.slice(), fieldName, groupItem);\r\n            if (result) {\r\n                return false\r\n            }\r\n        }));\r\n        if (!result) {\r\n            result = false\r\n        }\r\n        return result\r\n    }\r\n    _changeItemOption(item, option, value) {\r\n        if (isObject(item)) {\r\n            item[option] = value\r\n        }\r\n    }\r\n    _dimensionChanged() {\r\n        const currentScreenFactor = this._getCurrentScreenFactor();\r\n        if (this._lastMarkupScreenFactor !== currentScreenFactor) {\r\n            if (this._isColCountChanged(this._lastMarkupScreenFactor, currentScreenFactor)) {\r\n                this._targetScreenFactor = currentScreenFactor;\r\n                this._refresh();\r\n                this._targetScreenFactor = void 0\r\n            }\r\n            this._lastMarkupScreenFactor = currentScreenFactor\r\n        }\r\n    }\r\n    _isColCountChanged(oldScreenSize, newScreenSize) {\r\n        let isChanged = false;\r\n        each(this._cachedColCountOptions, ((index, item) => {\r\n            if (item.colCountByScreen[oldScreenSize] !== item.colCountByScreen[newScreenSize]) {\r\n                isChanged = true;\r\n                return false\r\n            }\r\n        }));\r\n        return isChanged\r\n    }\r\n    _refresh() {\r\n        const editorSelector = `.${TEXTEDITOR_CLASS}.${FOCUSED_STATE_CLASS}:not(.${DROP_DOWN_EDITOR_CLASS}) .${TEXTEDITOR_INPUT_CLASS}`;\r\n        eventsEngine.trigger(this.$element().find(editorSelector), \"change\");\r\n        super._refresh()\r\n    }\r\n    _updateIsDirty(dataField) {\r\n        const editor = this.getEditor(dataField);\r\n        if (!editor) {\r\n            return\r\n        }\r\n        if (editor.option(\"isDirty\")) {\r\n            this._dirtyFields.add(dataField)\r\n        } else {\r\n            this._dirtyFields.delete(dataField)\r\n        }\r\n        this.option(\"isDirty\", !!this._dirtyFields.size)\r\n    }\r\n    updateRunTimeInfoForEachEditor(editorAction) {\r\n        this._itemsRunTimeInfo.each(((_, itemRunTimeInfo) => {\r\n            const {\r\n                widgetInstance: widgetInstance\r\n            } = itemRunTimeInfo;\r\n            if (isDefined(widgetInstance) && Editor.isEditor(widgetInstance)) {\r\n                editorAction(widgetInstance)\r\n            }\r\n        }))\r\n    }\r\n    _clear() {\r\n        this.updateRunTimeInfoForEachEditor((editor => {\r\n            editor.clear();\r\n            editor.option(\"isValid\", true)\r\n        }));\r\n        ValidationEngine.resetGroup(this._getValidationGroup())\r\n    }\r\n    _updateData(data, value, isComplexData) {\r\n        const that = this;\r\n        const _data = isComplexData ? value : data;\r\n        if (isObject(_data)) {\r\n            each(_data, ((dataField, fieldValue) => {\r\n                that._updateData(isComplexData ? `${data}.${dataField}` : dataField, fieldValue, isObject(fieldValue))\r\n            }))\r\n        } else if (isString(data)) {\r\n            that._updateFieldValue(data, value)\r\n        }\r\n    }\r\n    registerKeyHandler(key, handler) {\r\n        super.registerKeyHandler(key, handler);\r\n        this._itemsRunTimeInfo.each(((_, itemRunTimeInfo) => {\r\n            if (isDefined(itemRunTimeInfo.widgetInstance)) {\r\n                itemRunTimeInfo.widgetInstance.registerKeyHandler(key, handler)\r\n            }\r\n        }))\r\n    }\r\n    _focusTarget() {\r\n        return this.$element().find(`.${FIELD_ITEM_CONTENT_CLASS} [tabindex]`).first()\r\n    }\r\n    _visibilityChanged() {\r\n        this._alignLabels(this._rootLayoutManager, this._rootLayoutManager.isSingleColumnMode())\r\n    }\r\n    _clearAutoColCountChangedTimeout() {\r\n        if (this.autoColCountChangedTimeoutId) {\r\n            clearTimeout(this.autoColCountChangedTimeoutId);\r\n            this.autoColCountChangedTimeoutId = void 0\r\n        }\r\n    }\r\n    _dispose() {\r\n        this._clearAutoColCountChangedTimeout();\r\n        ValidationEngine.removeGroup(this._getValidationGroup());\r\n        super._dispose()\r\n    }\r\n    clear() {\r\n        this._clear()\r\n    }\r\n    resetValues() {\r\n        this._clear()\r\n    }\r\n    reset(editorsData) {\r\n        this.updateRunTimeInfoForEachEditor((editor => {\r\n            const editorName = editor.option(\"name\");\r\n            if (editorsData && editorName in editorsData) {\r\n                editor.reset(editorsData[editorName]);\r\n                this._updateIsDirty(editorName)\r\n            } else {\r\n                editor.reset()\r\n            }\r\n        }));\r\n        this._renderValidationSummary()\r\n    }\r\n    updateData(data, value) {\r\n        this._updateData(data, value)\r\n    }\r\n    getEditor(dataField) {\r\n        return this._itemsRunTimeInfo.findWidgetInstanceByDataField(dataField) || this._itemsRunTimeInfo.findWidgetInstanceByName(dataField)\r\n    }\r\n    getButton(name) {\r\n        return this._itemsRunTimeInfo.findWidgetInstanceByName(name)\r\n    }\r\n    updateDimensions() {\r\n        const that = this;\r\n        const deferred = Deferred();\r\n        if (that._scrollable) {\r\n            that._scrollable.update().done((() => {\r\n                deferred.resolveWith(that)\r\n            }))\r\n        } else {\r\n            deferred.resolveWith(that)\r\n        }\r\n        return deferred.promise()\r\n    }\r\n    itemOption(id, option, value) {\r\n        const items = this._generateItemsFromData(this.option(\"items\"));\r\n        const item = this._getItemByField(id, items);\r\n        const path = getItemPath(items, item);\r\n        if (!item) {\r\n            return\r\n        }\r\n        switch (arguments.length) {\r\n            case 1:\r\n                return item;\r\n            case 3: {\r\n                const itemAction = this._tryCreateItemOptionAction(option, item, value, item[option], path);\r\n                this._changeItemOption(item, option, value);\r\n                const fullName = getFullOptionName(path, option);\r\n                if (!this._tryExecuteItemOptionAction(itemAction) && !this._tryChangeLayoutManagerItemOption(fullName, value)) {\r\n                    this.option(\"items\", items)\r\n                }\r\n                break\r\n            }\r\n            default:\r\n                if (isObject(option)) {\r\n                    if (!this._tryChangeLayoutManagerItemOptions(path, option)) {\r\n                        let allowUpdateItems;\r\n                        each(option, ((optionName, optionValue) => {\r\n                            const itemAction = this._tryCreateItemOptionAction(optionName, item, optionValue, item[optionName], path);\r\n                            this._changeItemOption(item, optionName, optionValue);\r\n                            if (!allowUpdateItems && !this._tryExecuteItemOptionAction(itemAction)) {\r\n                                allowUpdateItems = true\r\n                            }\r\n                        }));\r\n                        allowUpdateItems && this.option(\"items\", items)\r\n                    }\r\n                }\r\n        }\r\n    }\r\n    validate() {\r\n        return ValidationEngine.validateGroup(this._getValidationGroup())\r\n    }\r\n    getItemID(name) {\r\n        const {\r\n            formID: formID\r\n        } = this.option();\r\n        return `dx_${formID}_${name||new Guid}`\r\n    }\r\n    getTargetScreenFactor() {\r\n        return this._targetScreenFactor\r\n    }\r\n}\r\nregisterComponent(\"dxForm\", Form);\r\nexport default Form;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AACA;AACA;AACA;AAAA;AACA;AAIA;AACA;AACA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAMA;AAAA;AAKA;AACA;AACA;AAIA;AACA;AAGA;AAGA;AAGA;AAmBA;AACA;AAGA;AAGA;AAYA;AACA;AAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,uCAAuC;IAAC;IAAS;IAAc;IAAmB;CAAU;AAClG,MAAM,aAAa,8KAAA,CAAA,UAAM;IACrB,QAAQ;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,YAAY,GAAG,IAAI;QACxB,IAAI,CAAC,sBAAsB,GAAG,EAAE;QAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,gMAAA,CAAA,UAAoB;QACjD,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC,wBAAwB;IACjC;IACA,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,QAAQ,CAAC,GAAG,EAAE,IAAI,iJAAA,CAAA,UAAI,EAAE;YACxB,UAAU,CAAC;YACX,UAAU;YACV,eAAe,+KAAA,CAAA,0BAAuB;YACtC,eAAe;YACf,UAAU;YACV,oBAAoB;YACpB,eAAe;YACf,kBAAkB;YAClB,aAAa;YACb,iBAAiB;YACjB,4BAA4B;YAC5B,qBAAqB;YACrB,qBAAqB;YACrB,kBAAkB;YAClB,kBAAkB;YAClB,cAAc;YACd,cAAc,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YACzC,iBAAiB,8KAAA,CAAA,UAAmB,CAAC,YAAY,CAAC;YAClD,uBAAuB;YACvB,kBAAkB;YAClB,aAAa,CAAA,GAAA,mJAAA,CAAA,UAAM,AAAD,IAAI,iBAAiB;YACvC,WAAW;YACX,SAAS;QACb;IACJ;IACA,uBAAuB;QACnB,OAAO,KAAK,CAAC,uBAAuB,MAAM,CAAC;YAAC;gBACxC,QAAQ,IAAM,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD;gBAC5B,SAAS;oBACL,eAAe;gBACnB;YACJ;YAAG;gBACC,QAAQ,IAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD;gBACvB,SAAS;oBACL,qBAAqB;gBACzB;YACJ;SAAE;IACN;IACA,yBAAyB;QACrB,KAAK,CAAC;QACN,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC7B,UAAU;YACV,iBAAiB;QACrB;IACJ;IACA,kBAAkB,QAAQ,EAAE;QACxB,OAAO,SAAS,SAAS,IAAI,CAAC,6KAAA,CAAA,uBAAoB;IACtD;IACA,uBAAuB,UAAU,EAAE,KAAK,EAAE;QACtC,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QAChF,IAAI,mBAAmB,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;QAClE,MAAM,iBAAiB,QAAQ,WAAW,GAAG,6KAAA,CAAA,mBAAgB,GAAG,6KAAA,CAAA,4BAAyB,GAAG;QAC5F,MAAM,2BAA2B,QAAQ,aAAa,GAAG,CAAC,MAAM,EAAE,6KAAA,CAAA,uBAAoB,CAAC,CAAC,CAAC,GAAG;QAC5F,CAAA,GAAA,yLAAA,CAAA,+BAA4B,AAAD,EAAE,YAAY,CAAC,CAAC,EAAE,iBAAiB,0BAA0B,EAAE;IAC9F;IACA,kBAAkB,UAAU,EAAE,aAAa,EAAE,WAAW,EAAE,QAAQ,EAAE,gBAAgB,EAAE;QAClF,WAAW,cAAc,IAAI,YAAY,IAAI,CAAC,iBAAiB,CAAC;QAChE,MAAM,qBAAqB;YACvB,eAAe;YACf,aAAa;QACjB;QACA,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,UAAU,IAAK;YAC3B,IAAI,CAAC,sBAAsB,CAAC,YAAY,GAAG,oBAAoB;QACnE;IACJ;IACA,0BAA0B,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE;QACzD,MAAM,sBAAsB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,CAAC,CAAC,EAAE,6KAAA,CAAA,wBAAqB,GAAG,UAAU,GAAG;QAC3F,MAAM,gBAAgB,CAAC,CAAC,EAAE,6KAAA,CAAA,4BAAyB,GAAG,YAAY,IAAI,EAAE,6KAAA,CAAA,2BAAwB,CAAC,IAAI,EAAE,6KAAA,CAAA,mBAAgB,GAAG,qBAAqB;QAC/I,OAAO,WAAW,IAAI,CAAC;IAC3B;IACA,4BAA4B,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAE;QAC/E,MAAM,EACF,qBAAqB,mBAAmB,EAC3C,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,SAAS,qBAAqB;YAC9B,MAAM,mBAAmB,WAAW,IAAI,CAAC,CAAC,CAAC,EAAE,6KAAA,CAAA,yBAAsB,EAAE;YACrE,IAAK,IAAI,WAAW,GAAG,WAAW,UAAU,WAAY;gBACpD,IAAI,CAAC,sBAAsB,CAAC,kBAAkB,UAAU,eAAe;YAC3E;QACJ;QACA,MAAM,6BAA6B,IAAI,CAAC,MAAM,CAAC;QAC/C,IAAI,4BAA4B;YAC5B,IAAI,CAAC,iCAAiC,CAAC,YAAY,UAAU,eAAe;QAChF,OAAO;YACH,MAAM,UAAU,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,6KAAA,CAAA,mBAAgB,EAAE;YAC3D,IAAI;YACJ,IAAK,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACjC,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,CAAC,IAAI,eAAe,KAAK,GAAG,KAAK,GAAG;YACzE;QACJ;IACJ;IACA,kCAAkC,UAAU,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB,EAAE;QACrF,MAAM,qBAAqB;YACvB,eAAe;QACnB;QACA,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAK,WAAW,GAAG,WAAW,UAAU,WAAY;YAChD,eAAe,IAAI,CAAC,yBAAyB,CAAC,YAAY;YAC1D,IAAI,CAAC,sBAAsB,CAAC,cAAc,GAAG,oBAAoB;YACjE,IAAK,iBAAiB,GAAG,iBAAiB,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,iBAAkB;gBACrF,eAAe,IAAI,CAAC,yBAAyB,CAAC,YAAY,UAAU,IAAI,CAAC,eAAe,CAAC,eAAe;gBACxG,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC;gBAC7C,IAAK,gBAAgB,GAAG,gBAAgB,eAAe,gBAAiB;oBACpE,IAAI,CAAC,sBAAsB,CAAC,cAAc,eAAe,oBAAoB;gBACjF;YACJ;QACJ;IACJ;IACA,iBAAiB;QACb,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,OAAO;IACX;IACA,qBAAqB,IAAI,EAAE;QACvB,IAAI,EACA,eAAe,aAAa,EAC5B,aAAa,WAAW,EACxB,YAAY,UAAU,EACtB,eAAe,aAAa,EAC5B,OAAO,KAAK,EACf,GAAG;QACJ,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,OAAO,UAAU,IAAI,CAAC,cAAc,IAAI;YACjD;QACJ;QACA,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,4BAAyB,AAAD,EAAE,cAAc,eAAe;QAChF,IAAI,aAAa;YACb,IAAI,CAAC,iBAAiB,CAAC,YAAY,eAAe,MAAM,KAAK,GAAG;QACpE,OAAO,IAAI,IAAI,CAAC,cAAc,CAAC,QAAQ;YACnC,IAAI,CAAC,2BAA2B,CAAC,YAAY,cAAc,YAAY,IAAI,eAAe;QAC9F,OAAO;YACH,IAAI,CAAC,iBAAiB,CAAC,YAAY,eAAe,OAAO,cAAc,YAAY,IAAI;QAC3F;IACJ;IACA,mBAAmB;QACf,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YACrC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC;QAC7B;IACJ;IACA,uBAAuB;QACnB,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,iBAAiB,aAAa;YAC9B,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,6KAAA,CAAA,wBAAqB;QAClD;IACJ;IACA,cAAc;QACV,4JAAA,CAAA,UAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,IAAI;QACtD,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,6KAAA,CAAA,aAAU;QACnC,IAAI,CAAC,oBAAoB;QACzB,KAAK,CAAC;QACN,IAAI,CAAC,OAAO,CAAC,QAAQ,QAAQ,IAAI,CAAC,QAAQ;QAC1C,IAAI,IAAI,CAAC,MAAM,CAAC,qBAAqB;YACjC,IAAI,CAAC,iBAAiB;QAC1B;QACA,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,uBAAuB,GAAG,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,uBAAuB;QACvF,IAAI,CAAC,iCAAiC;IAC1C;IACA,oCAAoC;QAChC,IAAI,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YACb,MAAM,kBAAkB,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;YAC5C,4JAAA,CAAA,UAAuB,CAAC,SAAS,CAAC;YAClC,4JAAA,CAAA,UAAuB,CAAC,OAAO,CAAC,iBAAkB;gBAC9C,IAAI,CAAC,cAAc;YACvB;QACJ;IACJ;IACA,iBAAiB;QACb,IAAI,IAAI,CAAC,qBAAqB,CAAC,MAAM,EAAE;YACnC,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,qBAAqB,EAAG,CAAC,GAAG;gBAClC,IAAI;gBACJ,SAAS,CAAC,wBAAwB,cAAc,MAAM,CAAC,kBAAkB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,cAAc,kBAAkB;YAC5K;QACJ;IACJ;IACA,0BAA0B;QACtB,OAAO,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,MAAM,CAAA,GAAA,+KAAA,CAAA,yBAAsB,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,oBAAoB;IAChF;IACA,wBAAwB;QACpB,IAAI,CAAC,iBAAiB,CAAC,KAAK;QAC5B,IAAI,CAAC,qBAAqB,GAAG,EAAE;IACnC;IACA,aAAa,aAAa,EAAE,WAAW,EAAE;QACrC,IAAI,CAAC,oBAAoB,CAAC;YACtB,YAAY,IAAI,CAAC,QAAQ;YACzB,eAAe;YACf,eAAe;YACf,OAAO,IAAI,CAAC,MAAM,CAAC;YACnB,aAAa;QACjB;QACA,CAAA,GAAA,kLAAA,CAAA,qBAAkB,AAAD,EAAE,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,kLAAA,CAAA,gBAAa,EAAE;IAC/D;IACA,SAAS;QACL,IAAI,CAAC,uBAAuB;QAC5B,KAAK,CAAC;QACN,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC,sBAAsB,GAAG,EAAE;QAChC,IAAI,CAAC,uBAAuB,GAAG,KAAK;QACpC,4JAAA,CAAA,UAAuB,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC;IAC1D;IACA,oBAAoB;QAChB,MAAM,qBAAqB,IAAI,CAAC,MAAM,CAAC;QACvC,IAAI,CAAC,WAAW,GAAG,IAAI,uLAAA,CAAA,UAAU,CAAC,IAAI,CAAC,QAAQ,IAAI;YAC/C,WAAW,CAAC,CAAC;YACb,uBAAuB,CAAC;YACxB,aAAa;YACb,WAAW;YACX,eAAe;QACnB;IACJ;IACA,cAAc;QACV,IAAI;QACJ,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,CAAC,oBAAoB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,OAAO,MAAM,IAAI,CAAC,QAAQ;IACtL;IACA,0BAA0B;QACtB,IAAI;QACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,mBAAmB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,MAAM;QAC/H,IAAI,CAAC,mBAAmB,GAAG,KAAK;QAChC,IAAI,CAAC,kBAAkB,GAAG,KAAK;IACnC;IACA,2BAA2B;QACvB,IAAI,CAAC,uBAAuB;QAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B;YACtC,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,6KAAA,CAAA,0BAAuB,EAAE,QAAQ,CAAC,IAAI,CAAC,WAAW;YACjG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,CAAC;gBACnE,iBAAiB,IAAI,CAAC,mBAAmB;YAC7C,GAAG,mBAAmB,CAAC;QAC3B;IACJ;IACA,cAAc,KAAK,EAAE,kBAAkB,EAAE,WAAW,EAAE,MAAM,EAAE;QAC1D,IAAI,OAAO;YACP,MAAM,SAAS,EAAE;YACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,IAAI,OAAO,KAAK,CAAC,EAAE;gBACnB,MAAM,OAAO,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD,EAAE,aAAa,CAAA,GAAA,mLAAA,CAAA,wBAAqB,AAAD,EAAE,GAAG;gBAC/D,MAAM,kBAAkB;oBACpB,MAAM;oBACN,WAAW;oBACX,MAAM;gBACV;gBACA,MAAM,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;gBACxC,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;oBAChB,OAAO;wBACH,WAAW;oBACf;gBACJ;gBACA,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;oBAChB,MAAM,eAAe,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG;oBAClC,gBAAgB,YAAY,GAAG;oBAC/B,aAAa,IAAI,GAAG;oBACpB,IAAI,CAAC,2BAA2B,CAAC;oBACjC,IAAI,CAAC,oBAAoB,CAAC;oBAC1B,IAAI,CAAC,qBAAqB,CAAC,cAAc;oBACzC,IAAI,CAAC,uBAAuB,CAAC;oBAC7B,IAAI,oBAAoB;wBACpB,aAAa,YAAY,GAAG,6KAAA,CAAA,uBAAoB;oBACpD;oBACA,IAAI,aAAa,KAAK,EAAE;wBACpB,aAAa,KAAK,GAAG,IAAI,CAAC,aAAa,CAAC,aAAa,KAAK,EAAE,oBAAoB;oBACpF;oBACA,OAAO,IAAI,CAAC;gBAChB,OAAO;oBACH,OAAO,IAAI,CAAC;gBAChB;YACJ;YACA,OAAO;QACX;IACJ;IACA,4BAA4B,IAAI,EAAE;QAC9B,IAAI,YAAY,KAAK,QAAQ,EAAE;YAC3B,KAAK,4BAA4B,GAAG,CAAA;gBAChC,IAAI,KAAK,eAAe,EAAE;oBACtB,KAAK,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC;gBAClD;gBACA,KAAK,eAAe,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE;YAC9D;YACA,KAAK,4BAA4B,CAAC,KAAK,eAAe;QAC1D;IACJ;IACA,qBAAqB,IAAI,EAAE;QACvB,IAAI,YAAY,KAAK,QAAQ,EAAE;YAC3B,KAAK,eAAe,GAAG,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,eAAe,EAAE;YAC3D,KAAK,yBAAyB,GAAG,CAAA;gBAC7B,IAAI,KAAK,QAAQ,EAAE;oBACf,KAAK,oBAAoB,GAAG,IAAI,CAAC,YAAY,CAAC;gBAClD;gBACA,KAAK,QAAQ,GAAG,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI,EAAE;YACvD;YACA,KAAK,yBAAyB,CAAC,KAAK,QAAQ;QAChD;IACJ;IACA,sBAAsB,IAAI,EAAE,IAAI,EAAE;QAC9B,IAAI,aAAa,KAAK,QAAQ,EAAE;YAC5B,KAAK,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE;YACpD,KAAK,IAAI,GAAG,IAAI,CAAC,aAAa,CAAC,KAAK,IAAI,EAAE,MAAM,MAAM;QAC1D;IACJ;IACA,wBAAwB,IAAI,EAAE;QAC1B,IAAI,KAAK,QAAQ,EAAE;YACf,KAAK,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,QAAQ;QACnD;IACJ;IACA,eAAe,KAAK,EAAE;QAClB,IAAI,OAAO;YACP,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,MAAM,OAAO,KAAK,CAAC,EAAE;gBACrB,IAAI,YAAY,KAAK,QAAQ,EAAE;oBAC3B,OAAO;gBACX;YACJ;QACJ;IACJ;IACA,gBAAgB;QACZ,MAAM,OAAO,IAAI;QACjB,IAAI,QAAQ,KAAK,MAAM,CAAC;QACxB,MAAM,WAAW,KAAK,WAAW;QACjC,QAAQ,KAAK,aAAa,CAAC;QAC3B,KAAK,kBAAkB,GAAG,KAAK,oBAAoB,CAAC,UAAU,IAAI,CAAC,2BAA2B,CAAC,OAAO;YAClG,QAAQ;YACR,UAAU,KAAK,MAAM,CAAC;YACtB,iBAAiB,KAAK,MAAM,CAAC;YAC7B,eAAe,IAAI,CAAC,MAAM,CAAC;YAC3B,kBAAkB,IAAI,CAAC,MAAM,CAAC;YAC9B,iBAAgB,WAAW;gBACvB,KAAK,YAAY,CAAC,IAAI,CAAC,MAAM,KAAK,kBAAkB,EAAE;YAC1D;YACA,gBAAe,CAAC;gBACZ,KAAK,YAAY,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,CAAC,kBAAkB;YACjE;QACJ;IACJ;IACA,wBAAwB,IAAI,EAAE;QAC1B,OAAO,KAAK,KAAK,IAAI,EAAE;IAC3B;IACA,oBAAoB,IAAI,EAAE,CAAC,EAAE,UAAU,EAAE;QACrC,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACtC,MAAM,kBAAkB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,KAAK,eAAe,EAAE;YACrD,YAAY,KAAK,IAAI;YACrB,gBAAgB,CAAA;gBACZ,IAAI,uBAAuB;gBAC3B,SAAS,CAAC,wBAAwB,KAAK,eAAe,KAAK,KAAK,MAAM,yBAAyB,SAAS,CAAC,yBAAyB,sBAAsB,cAAc,KAAK,KAAK,MAAM,0BAA0B,uBAAuB,IAAI,CAAC,uBAAuB;gBACnQ,CAAA,GAAA,kLAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,WAAW;YACtC;YACA,cAAc,CAAC,UAAU,GAAG;gBACxB,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;gBACrB,MAAM,kBAAkB,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,eAAe,EAAE;gBAChE,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,YAAY,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,uBAAuB,CAAC,WAAW;oBACjI,UAAU,SAAS,QAAQ;oBAC3B,iBAAiB;oBACjB,eAAe,IAAI,CAAC,MAAM,CAAC;oBAC3B,kBAAkB,SAAS,gBAAgB;oBAC3C,cAAc,SAAS,YAAY;oBACnC,iBAAiB,CAAA;wBACb,IAAI,CAAC,oBAAoB,CAAC;4BACtB,YAAY;4BACZ,eAAe;4BACf,OAAO,SAAS,KAAK;4BACrB,aAAa;wBACjB;oBACJ;gBACJ;gBACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;oBACxB,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,SAAS,IAAI,EAAE;wBAC7D,eAAe;oBACnB;gBACJ;gBACA,IAAI,iBAAiB;oBACjB,IAAI,CAAC,oBAAoB,CAAC;wBACtB,YAAY;wBACZ,eAAe;wBACf,OAAO,SAAS,KAAK;wBACrB,aAAa,cAAc,kBAAkB;oBACjD;gBACJ;YACJ;QACJ;QACA,MAAM,4BAA4B,CAAC,OAAO;YACtC,IAAI,MAAM,OAAO,CAAC,QAAQ;gBACtB,MAAM,OAAO,CAAE,CAAA,OAAQ,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,KAAK,IAAI,EAAE;wBAChF,gBAAgB;oBACpB;YACJ;QACJ;QACA,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,WAAW,oJAAA,CAAA,UAAQ,EAAE;QAC5D,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,YAAY,MAAM,GAAG,QAAQ,CAAC,6KAAA,CAAA,oCAAiC;QACjE,SAAS,EAAE,CAAC,iBAAkB,CAAA;YAC1B,IAAI,iBAAiB,EAAE,QAAQ,EAAE;gBAC7B,0BAA0B,EAAE,KAAK,EAAE,EAAE,SAAS;YAClD;QACJ;QACA,0BAA0B;YAAC;gBACvB,MAAM,KAAK,IAAI;YACnB;eAAM,KAAK,IAAI,IAAI,EAAE;SAAC,EAAE;IAC5B;IACA,0BAA0B,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE;QACxC,IAAI,KAAK,oBAAoB,EAAE;YAC3B,MAAM,mBAAmB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,6KAAA,CAAA,kCAA+B,EAAE,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC;YACtG,KAAK,2BAA2B,GAAG;gBAC/B,MAAM,OAAO;oBACT,WAAW,IAAI;oBACf,SAAS,KAAK,OAAO;oBACrB,MAAM,KAAK,IAAI;gBACnB;gBACA,KAAK,oBAAoB,CAAC,MAAM,CAAC;oBAC7B,OAAO;oBACP,WAAW,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;gBAChC;YACJ;YACA,KAAK,2BAA2B;YAChC;QACJ;QACA,IAAI,KAAK,OAAO,EAAE;YACd,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ,CAAC,6KAAA,CAAA,2BAAwB,EAAE,IAAI,CAAC,KAAK,OAAO,EAAE,IAAI,CAAC,MAAM,IAAI,QAAQ,CAAC;QAC9F;IACJ;IACA,0BAA0B,IAAI,EAAE,MAAM,EAAE;QACpC,MAAM,gBAAgB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,6KAAA,CAAA,2BAAwB,EAAE,QAAQ,CAAC;QAC7E,IAAI,KAAK,oBAAoB,EAAE;YAC3B,KAAK,2BAA2B,GAAG;gBAC/B,cAAc,KAAK;gBACnB,MAAM,OAAO;oBACT,UAAU,IAAI,CAAC,MAAM,CAAC;oBACtB,WAAW,IAAI;gBACnB;gBACA,KAAK,oBAAoB,CAAC,MAAM,CAAC;oBAC7B,OAAO;oBACP,WAAW,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE;gBAChC;YACJ;YACA,KAAK,2BAA2B;QACpC,OAAO;YACH,IAAI;YACJ,MAAM,gBAAgB,IAAI,CAAC,oBAAoB,CAAC,eAAe,IAAI,CAAC,2BAA2B,CAAC,IAAI,CAAC,uBAAuB,CAAC,OAAO;gBAChI,UAAU,KAAK,QAAQ;gBACvB,kBAAkB,KAAK,gBAAgB;gBACvC,iBAAiB,KAAK,eAAe;gBACrC,cAAc,KAAK,YAAY;YACnC;YACA,SAAS,CAAC,wBAAwB,IAAI,CAAC,iBAAiB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,0BAA0B,CAAC,KAAK,IAAI,EAAE;gBACzJ,eAAe;YACnB;YACA,MAAM,WAAW,cAAc,YAAY;YAC3C,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,QAAQ,CAAC,WAAW;gBAC1C,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YAC9B;YACA,OAAO,QAAQ,CAAC,6KAAA,CAAA,wBAAqB,GAAG;YACxC,OAAO,IAAI,CAAC,6KAAA,CAAA,uBAAoB,EAAE;QACtC;IACJ;IACA,mBAAmB,IAAI,EAAE,OAAO,EAAE,UAAU,EAAE;QAC1C,MAAM,EACF,IAAI,EAAE,EACT,GAAG,QAAQ,aAAa,CAAC,SAAS;QACnC,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,WAAW,CAAC,6KAAA,CAAA,gCAA6B,EAAE,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,KAAK,KAAK,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,6KAAA,CAAA,mBAAgB,EAAE,QAAQ,CAAC;QACzJ,MAAM,YAAY;YACd,MAAM;YACN,YAAY;QAChB;QACA,IAAI,CAAC,OAAO,CAAC,WAAW;QACxB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,YAAY,MAAM,GAAG,QAAQ,CAAC,6KAAA,CAAA,qCAAkC;QAClE,IAAI,CAAC,yBAAyB,CAAC,MAAM,QAAQ;QAC7C,IAAI,CAAC,yBAAyB,CAAC,MAAM;IACzC;IACA,4BAA4B,KAAK,EAAE,4BAA4B,EAAE;QAC7D,OAAO,CAAA,GAAA,mLAAA,CAAA,gCAA6B,AAAD,EAAE;YACjC,MAAM,IAAI;YACV,aAAa,IAAI,CAAC,MAAM;YACxB,cAAc,IAAI,CAAC,QAAQ;YAC3B,OAAO;YACP,iBAAiB,IAAI,CAAC,mBAAmB;YACzC,8BAA8B;YAC9B,oBAAoB,CAAA;gBAChB,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;oBACvB,IAAI,CAAC,0BAA0B,CAAC;gBACpC;YACJ;YACA,gBAAgB,CAAA;gBACZ,IAAI;gBACJ,IAAI,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,KAAK,SAAS,CAAC,iBAAiB;gBAC5E,SAAS,CAAC,wBAAwB,6BAA6B,cAAc,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,8BAA8B;YACnL;YACA,aAAa,CAAA;gBACT,IAAI,EACA,WAAW,SAAS,EACvB,GAAG;gBACJ,MAAM,yBAAyB,UAAU,mBAAmB;gBAC5D,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC;YAC9C;YACA,qBAAqB;gBACjB,IAAI;gBACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,sBAAsB;YAClJ;QACJ;IACJ;IACA,qBAAqB,OAAO,EAAE,oBAAoB,EAAE;QAChD,MAAM,uBAAuB;YACzB,IAAI,qBAAqB,QAAQ;YACjC,IAAI,qBAAqB,QAAQ;YACjC,IAAI,qBAAqB,QAAQ;YACjC,IAAI;QACR;QACA,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC7B,kBAAkB,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,sBAAsB,qBAAqB,gBAAgB;QACxF;QACA,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACnB,SAAS,QAAQ,CAAC;QAClB,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC,UAAU,mBAAmB;QACpE,SAAS,EAAE,CAAC,uBAAwB;YAChC,IAAI,CAAC,gCAAgC;YACrC,IAAI,CAAC,4BAA4B,GAAG,WAAY,IAAM,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,QAAQ,IAAK;QAC/F;QACA,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC;QAChC,OAAO;IACX;IACA,sBAAsB;QAClB,OAAO,IAAI,CAAC,MAAM,CAAC,sBAAsB,IAAI;IACjD;IACA,iBAAiB,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE;QACrC,SAAS,UAAU,CAAC;QACpB,IAAI,CAAC,aAAa,CAAC,QAAQ;YACvB,UAAU,IAAI,CAAC,MAAM,CAAC;QAC1B;QACA,OAAO,KAAK,CAAC,iBAAiB,UAAU,MAAM;IAClD;IACA,2BAA2B;QACvB,MAAM,OAAO,IAAI;QACjB,KAAK,EAAE,CAAC,iBAAkB,CAAA;YACtB,MAAM,iBAAiB,KAAK,QAAQ;YACpC,IAAI,eAAe,gBAAgB;gBAC/B,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,KAAK,KAAK,GAAG;oBACxB,KAAK,QAAQ,CAAC,MAAM,CAAC,YAAY,KAAK,KAAK,GAAG,CAAC;gBACnD;gBACA,KAAK,mCAAmC,CAAC,KAAK,KAAK;YACvD;YACA,IAAI,KAAK,qBAAqB,CAAC,MAAM,EAAE;gBACnC,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,KAAK,qBAAqB,EAAG,CAAC,OAAO;oBACtC,IAAI,eAAe,gBAAgB;wBAC/B,KAAK,eAAe,GAAG;wBACvB,cAAc,MAAM,CAAC,cAAc,KAAK,KAAK;wBAC7C,KAAK,eAAe,GAAG;oBAC3B;oBACA,IAAI,eAAe,KAAK,IAAI,IAAI,eAAe,KAAK,IAAI,EAAE;wBACtD,cAAc,MAAM,CAAC,gBAAgB,KAAK,KAAK;oBACnD;gBACJ;YACJ;QACJ;IACJ;IACA,eAAe,IAAI,EAAE;QACjB,MAAM,gBAAgB,KAAK,QAAQ,CAAC,KAAK,CAAC;QAC1C,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,MAAM,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,YAAY,IAAI,CAAC,0BAA0B,CAAC,OAAO;YAC9G;QACJ;QACA,IAAI,cAAc,MAAM,GAAG,KAAK,CAAC,MAAM,aAAa,CAAC,EAAE,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,6BAA6B,CAAC,OAAO;YACpH;QACJ;QACA,IAAI,CAAC,4BAA4B,CAAC;IACtC;IACA,6BAA6B,IAAI,EAAE;QAC/B,OAAQ,KAAK,IAAI;YACb,KAAK;gBACD,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;oBACvB,IAAI,CAAC,WAAW;gBACpB,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,GAAG;oBAClC,IAAI,CAAC,MAAM;gBACf;gBACA;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,wBAAwB;gBAC7B;YACJ,KAAK;gBAAe;oBAChB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;oBACf,IAAI,WAAW,UAAU;wBACrB,IAAI,CAAC,WAAW;oBACpB;oBACA;gBACJ;YACA,KAAK;gBACD,KAAK,CAAC,eAAe;gBACrB,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC,KAAK,IAAI,EAAE,KAAK,KAAK;gBACpD,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;gBACrF;YACJ,KAAK;gBACD,4JAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,KAAK,aAAa,IAAI,IAAI;gBACvD,IAAI,CAAC,WAAW;gBAChB;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,2BAA2B,IAAI,EAAE;QAC7B,MAAM,YAAY,KAAK,QAAQ,CAAC,KAAK,CAAC;QACtC,MAAM,EACF,OAAO,KAAK,EACf,GAAG;QACJ,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;QACnC,MAAM,OAAO,IAAI,CAAC,MAAM,CAAC;QACzB,MAAM,wBAAwB,KAAK,QAAQ,CAAC,OAAO,CAAC,GAAG,SAAS,CAAC,CAAC,EAAE;QACpE,MAAM,mBAAmB,sBAAsB,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,UAAU;QAC/E,MAAM,aAAa,IAAI,CAAC,0BAA0B,CAAC,kBAAkB,MAAM,IAAI,CAAC,iBAAiB,EAAE,KAAK,aAAa,EAAE;QACvH,IAAI,SAAS,IAAI,CAAC,2BAA2B,CAAC,eAAe,IAAI,CAAC,iCAAiC,CAAC,KAAK,QAAQ,EAAE;QACnH,IAAI,CAAC,UAAU,MAAM;YACjB,IAAI,CAAC,iBAAiB,CAAC,MAAM,uBAAuB;YACpD,MAAM,QAAQ,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC;YACtD,IAAI,CAAC,MAAM,CAAC,SAAS;YACrB,SAAS;QACb;QACA,OAAO;IACX;IACA,8BAA8B,IAAI,EAAE;QAChC,MAAM,YAAY,KAAK,QAAQ,CAAC,KAAK,CAAC;QACtC,MAAM,EACF,OAAO,KAAK,EACf,GAAG;QACJ,MAAM,YAAY,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC;QAC1C,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,QAAQ;YACR,OAAO,MAAM,CAAC,SAAS;QAC3B,OAAO;YACH,IAAI,CAAC,0BAA0B,CAAC;gBAC5B,WAAW;gBACX,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,2BAA2B,UAAU,EAAE,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE;QACzE,IAAI,WAAW,YAAY;YACvB,IAAI,CAAC,iBAAiB,CAAC,0BAA0B,CAAC,GAAG,SAAS,KAAK,CAAC;YACpE,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,MAAM,UAAU;QACtD;QACA,OAAO,CAAA,GAAA,kMAAA,CAAA,UAAyB,AAAD,EAAE,YAAY;YACzC,MAAM;YACN,OAAO;YACP,eAAe;YACf,kBAAkB,IAAI,CAAC,iBAAiB;QAC5C;IACJ;IACA,4BAA4B,MAAM,EAAE;QAChC,OAAO,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,UAAU;IAC5E;IACA,yCAAyC,QAAQ,EAAE;QAC/C,MAAM,aAAa,CAAA,GAAA,mLAAA,CAAA,4BAAyB,AAAD,EAAE;QAC7C,IAAI,qCAAqC,QAAQ,CAAC,aAAa;YAC3D,4JAAA,CAAA,UAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,mBAAmB,IAAI;YACtD,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B;gBACtC,IAAI;gBACJ,SAAS,CAAC,yBAAyB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,0BAA0B,uBAAuB,sBAAsB;YACrJ;QACJ;IACJ;IACA,4BAA4B,aAAa,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE;QAChE,IAAI,IAAI,CAAC,gBAAgB,GAAG,GAAG;YAC3B,CAAC,cAAc,gBAAgB,IAAI,cAAc,WAAW;YAC5D,MAAM,MAAM,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAC;YACjD,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC,KAAM;gBAC/B,CAAC,cAAc,SAAS,IAAI,cAAc,SAAS;gBACnD,OAAO,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,IAAI,OAAO;YAC7B;QACJ;QACA,MAAM,sBAAsB,CAAA;YACxB,EAAE,SAAS,CAAC,GAAG,CAAC,gBAAgB;YAChC,IAAI,CAAA,GAAA,mLAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO;gBAC9B,MAAM,UAAU,CAAA,GAAA,mLAAA,CAAA,gBAAa,AAAD,EAAE;gBAC9B,MAAM,mBAAmB,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,CAAC;gBAClF,IAAI,kBAAkB;oBAClB,IAAI,CAAC,oBAAoB,CAAC;wBACtB,OAAO,iBAAiB,MAAM,CAAC;wBAC/B,eAAe;wBACf,YAAY,iBAAiB,QAAQ;wBACrC,aAAa,iBAAiB,kBAAkB;oBACpD;gBACJ;YACJ,OAAO;gBACH,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;YACzF;QACJ;QACA,cAAc,EAAE,CAAC,gBAAgB;QACjC,cAAc,MAAM,CAAC,YAAY;QACjC,IAAI,CAAC,wCAAwC,CAAC;IAClD;IACA,kCAAkC,QAAQ,EAAE,KAAK,EAAE;QAC/C,MAAM,YAAY,SAAS,KAAK,CAAC;QACjC,MAAM,aAAa,CAAA,GAAA,mLAAA,CAAA,4BAAyB,AAAD,EAAE;QAC7C,IAAI,YAAY,cAAc,UAAU,MAAM,GAAG,GAAG;YAChD,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC;YACnC,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,CAAC;YAC/E,IAAI,eAAe;gBACf,IAAI,CAAC,iBAAiB,CAAC,kBAAkB,CAAC,cAAc,mBAAmB;gBAC3E,MAAM,QAAQ,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;gBAC/C,IAAI,CAAC,2BAA2B,CAAC,eAAe,YAAY,OAAO;gBACnE,OAAO;YACX;QACJ,OAAO,IAAI,UAAU,MAAM,GAAG,GAAG;YAC7B,MAAM,eAAe,UAAU,MAAM,GAAG;YACxC,MAAM,WAAW,IAAI,CAAC,YAAY,CAAC,UAAU,KAAK,CAAC,GAAG;YACtD,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,iCAAiC,CAAC;YAC/E,IAAI,eAAe;gBACf,MAAM,iBAAiB,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,CAAC,aAAa,EAAE;gBAClE,IAAI,iBAAiB,YAAY;oBAC7B,IAAI,cAAc,MAAM,CAAC,oBAAoB,OAAO;wBAChD,OAAO;oBACX;gBACJ;gBACA,IAAI,cAAc,YAAY;oBAC1B,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU;oBAC1D,IAAI,SAAS,aAAa,KAAK,MAAM,aAAa,UAAU,MAAM,EAAE;wBAChE,MAAM,qBAAqB,cAAc,MAAM,CAAC;wBAChD,UAAU,OAAO,CAAE,CAAC,MAAM;4BACtB,MAAM,aAAa,kBAAkB,CAAC,MAAM;4BAC5C,WAAW,YAAY,GAAG,KAAK,YAAY;wBAC/C;oBACJ;gBACJ;gBACA,IAAI,CAAC,2BAA2B,CAAC,eAAe,gBAAgB,OAAO;gBACvE,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,mCAAmC,QAAQ,EAAE,OAAO,EAAE;QAClD,IAAI;QACJ,IAAI,CAAC,WAAW;QAChB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,CAAC,YAAY;YACxB,SAAS,IAAI,CAAC,iCAAiC,CAAC,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,aAAa;YACzF,IAAI,CAAC,QAAQ;gBACT,OAAO;YACX;QACJ;QACA,IAAI,CAAC,SAAS;QACd,OAAO;IACX;IACA,aAAa,SAAS,EAAE;QACpB,IAAI,WAAW,SAAS,CAAC,EAAE;QAC3B,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACnC,IAAI,CAAC,MAAM,SAAS,CAAC,EAAE,CAAC,MAAM,CAAC,2BAA2B;gBACtD,YAAY,CAAC,CAAC,EAAE,SAAS,CAAC,EAAE,EAAE;YAClC,OAAO;gBACH;YACJ;QACJ;QACA,OAAO;IACX;IACA,2BAA2B,IAAI,EAAE;QAC7B,IAAI,CAAC,cAAc,CAAC,KAAK,SAAS;QAClC,IAAI,CAAC,qBAAqB,CAAC,sBAAsB;IACrD;IACA,oCAAoC,IAAI,EAAE;QACtC,IAAI,QAAQ,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YACxB,OAAO,IAAI,CAAC,MAAM,OAAO,CAAE,CAAA;gBACvB,IAAI,CAAC,0BAA0B,CAAC;oBAC5B,WAAW;oBACX,OAAO,IAAI,CAAC,IAAI;gBACpB;YACJ;QACJ;IACJ;IACA,kBAAkB,SAAS,EAAE,KAAK,EAAE;QAChC,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,cAAc;YACpC,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC;YAC9B,IAAI,CAAC,MAAM,CAAC,CAAC,SAAS,EAAE,WAAW,EAAE;YACrC,IAAI,QAAQ;gBACR,MAAM,cAAc,OAAO,MAAM,CAAC;gBAClC,IAAI,gBAAgB,OAAO;oBACvB,OAAO,MAAM,CAAC,SAAS;gBAC3B;YACJ;QACJ;IACJ;IACA,uBAAuB,KAAK,EAAE;QAC1B,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC;QAC7B,MAAM,SAAS,EAAE;QACjB,IAAI,CAAC,SAAS,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YAC/B,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,UAAW,CAAA;gBACZ,OAAO,IAAI,CAAC;oBACR,WAAW;gBACf;YACJ;QACJ;QACA,IAAI,OAAO;YACP,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;gBACjB,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;oBAChB,OAAO,IAAI,CAAC;gBAChB,OAAO;oBACH,OAAO,IAAI,CAAC;wBACR,WAAW;oBACf;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,KAAK,EAAE,KAAK,EAAE;QAC1B,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS,QAAQ,KAAK,cAAc,CAAC;QACjE,MAAM,EACF,WAAW,SAAS,EACvB,GAAG;QACJ,MAAM,EACF,WAAW,SAAS,EACvB,GAAG;QACJ,IAAI;QACJ,IAAI,MAAM,MAAM,EAAE;YACd,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;gBACjB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;gBACJ,IAAI,UAAU,MAAM,EAAE;oBAClB,MAAM,OAAO,UAAU,KAAK;oBAC5B,OAAO,KAAK,mBAAmB,CAAC,MAAM,WAAW;gBACrD,OAAO,IAAI,YAAY,YAAY,CAAC,CAAC,KAAK,OAAO,IAAI,KAAK,IAAI,KAAK,aAAa,YAAY,CAAC,KAAK,IAAI,EAAE;oBACpG,MAAM,gBAAgB,KAAK,gBAAgB,CAAC;oBAC5C,KAAK,KAAK,GAAG,KAAK,sBAAsB,CAAC,KAAK,KAAK;oBACnD,OAAO,KAAK,eAAe,CAAC;wBACxB,WAAW;wBACX,WAAW;oBACf,GAAG,IAAI,CAAC,cAAc;gBAC1B;gBACA,IAAI,CAAA,GAAA,mLAAA,CAAA,2CAAwC,AAAD,EAAE,MAAM,YAAY;oBAC3D,aAAa;oBACb,OAAO;gBACX;YACJ;QACJ;QACA,OAAO;IACX;IACA,eAAe,KAAK,EAAE;QAClB,IAAI,YAAY;QAChB,IAAI,iBAAiB,UAAU,OAAO,CAAC;QACvC,MAAM,aAAa,EAAE;QACrB,MAAO,CAAC,MAAM,eAAgB;YAC1B,WAAW,IAAI,CAAC,UAAU,MAAM,CAAC,GAAG;YACpC,YAAY,UAAU,MAAM,CAAC,iBAAiB;YAC9C,iBAAiB,UAAU,OAAO,CAAC;QACvC;QACA,OAAO;YACH,WAAW;YACX,WAAW,WAAW,OAAO;QACjC;IACJ;IACA,oBAAoB,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;QACvC,MAAM,OAAO,IAAI;QACjB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;QACJ,MAAM,gBAAgB,KAAK,gBAAgB,CAAC;QAC5C,MAAM,qBAAqB,YAAY,YAAY,aAAa,YAAY,KAAK,KAAK;QACtF,IAAI;QACJ,GAAG;YACC,IAAI,oBAAoB;gBACpB,MAAM,OAAO,KAAK,IAAI,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK;gBACpD,MAAM,kBAAkB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE;gBAClC,MAAM,oBAAoB,CAAA,GAAA,mLAAA,CAAA,uBAAoB,AAAD,EAAE;gBAC/C,IAAI;gBACJ,IAAI,CAAC,cAAc,GAAG,KAAK,sBAAsB,CAAC,IAAI,CAAC,cAAc;gBACrE,IAAI,iBAAiB;oBACjB,WAAW,KAAK,GAAG;gBACvB;gBACA,IAAI,CAAC,KAAK,MAAM,EAAE;oBACd,SAAS,KAAK,eAAe,CAAC,WAAW,IAAI,CAAC,cAAc;oBAC5D,IAAI,QAAQ;wBACR;oBACJ;gBACJ;gBACA,IAAI,CAAC,mBAAmB,mBAAmB,sBAAsB,UAAU;oBACvE,IAAI,KAAK,MAAM,EAAE;wBACb,SAAS,KAAK,yBAAyB,CAAC,MAAM,WAAW,IAAI,CAAC,cAAc;oBAChF;gBACJ;YACJ,OAAO;gBACH;YACJ;QACJ,QAAS,KAAK,MAAM,IAAI,CAAC,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAS;QAC5C,OAAO;IACX;IACA,iBAAiB,QAAQ,EAAE;QACvB,OAAO,aAAa,WAAW,SAAS;IAC5C;IACA,0BAA0B,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE;QAC9C,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,OAAO;YACjB,SAAS,KAAK,mBAAmB,CAAC,KAAK,KAAK,IAAI,WAAW;YAC3D,IAAI,QAAQ;gBACR,OAAO;YACX;QACJ;QACA,IAAI,CAAC,QAAQ;YACT,SAAS;QACb;QACA,OAAO;IACX;IACA,kBAAkB,IAAI,EAAE,MAAM,EAAE,KAAK,EAAE;QACnC,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YAChB,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,oBAAoB;QAChB,MAAM,sBAAsB,IAAI,CAAC,uBAAuB;QACxD,IAAI,IAAI,CAAC,uBAAuB,KAAK,qBAAqB;YACtD,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,uBAAuB,EAAE,sBAAsB;gBAC5E,IAAI,CAAC,mBAAmB,GAAG;gBAC3B,IAAI,CAAC,QAAQ;gBACb,IAAI,CAAC,mBAAmB,GAAG,KAAK;YACpC;YACA,IAAI,CAAC,uBAAuB,GAAG;QACnC;IACJ;IACA,mBAAmB,aAAa,EAAE,aAAa,EAAE;QAC7C,IAAI,YAAY;QAChB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,sBAAsB,EAAG,CAAC,OAAO;YACvC,IAAI,KAAK,gBAAgB,CAAC,cAAc,KAAK,KAAK,gBAAgB,CAAC,cAAc,EAAE;gBAC/E,YAAY;gBACZ,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,WAAW;QACP,MAAM,iBAAiB,CAAC,CAAC,EAAE,6LAAA,CAAA,mBAAgB,CAAC,CAAC,EAAE,8KAAA,CAAA,sBAAmB,CAAC,MAAM,EAAE,kMAAA,CAAA,yBAAsB,CAAC,GAAG,EAAE,6LAAA,CAAA,yBAAsB,EAAE;QAC/H,uLAAA,CAAA,UAAY,CAAC,OAAO,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,iBAAiB;QAC3D,KAAK,CAAC;IACV;IACA,eAAe,SAAS,EAAE;QACtB,MAAM,SAAS,IAAI,CAAC,SAAS,CAAC;QAC9B,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,IAAI,OAAO,MAAM,CAAC,YAAY;YAC1B,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;QAC1B,OAAO;YACH,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC;QAC7B;QACA,IAAI,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI;IACnD;IACA,+BAA+B,YAAY,EAAE;QACzC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAE,CAAC,GAAG;YAC7B,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG;YACJ,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB,2JAAA,CAAA,UAAM,CAAC,QAAQ,CAAC,iBAAiB;gBAC9D,aAAa;YACjB;QACJ;IACJ;IACA,SAAS;QACL,IAAI,CAAC,8BAA8B,CAAE,CAAA;YACjC,OAAO,KAAK;YACZ,OAAO,MAAM,CAAC,WAAW;QAC7B;QACA,4JAAA,CAAA,UAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,mBAAmB;IACxD;IACA,YAAY,IAAI,EAAE,KAAK,EAAE,aAAa,EAAE;QACpC,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,gBAAgB,QAAQ;QACtC,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;YACjB,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,WAAW;gBACrB,KAAK,WAAW,CAAC,gBAAgB,GAAG,KAAK,CAAC,EAAE,WAAW,GAAG,WAAW,YAAY,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE;YAC9F;QACJ,OAAO,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;YACvB,KAAK,iBAAiB,CAAC,MAAM;QACjC;IACJ;IACA,mBAAmB,GAAG,EAAE,OAAO,EAAE;QAC7B,KAAK,CAAC,mBAAmB,KAAK;QAC9B,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAE,CAAC,GAAG;YAC7B,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB,cAAc,GAAG;gBAC3C,gBAAgB,cAAc,CAAC,kBAAkB,CAAC,KAAK;YAC3D;QACJ;IACJ;IACA,eAAe;QACX,OAAO,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,EAAE,6KAAA,CAAA,2BAAwB,CAAC,WAAW,CAAC,EAAE,KAAK;IAChF;IACA,qBAAqB;QACjB,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,kBAAkB,CAAC,kBAAkB;IACzF;IACA,mCAAmC;QAC/B,IAAI,IAAI,CAAC,4BAA4B,EAAE;YACnC,aAAa,IAAI,CAAC,4BAA4B;YAC9C,IAAI,CAAC,4BAA4B,GAAG,KAAK;QAC7C;IACJ;IACA,WAAW;QACP,IAAI,CAAC,gCAAgC;QACrC,4JAAA,CAAA,UAAgB,CAAC,WAAW,CAAC,IAAI,CAAC,mBAAmB;QACrD,KAAK,CAAC;IACV;IACA,QAAQ;QACJ,IAAI,CAAC,MAAM;IACf;IACA,cAAc;QACV,IAAI,CAAC,MAAM;IACf;IACA,MAAM,WAAW,EAAE;QACf,IAAI,CAAC,8BAA8B,CAAE,CAAA;YACjC,MAAM,aAAa,OAAO,MAAM,CAAC;YACjC,IAAI,eAAe,cAAc,aAAa;gBAC1C,OAAO,KAAK,CAAC,WAAW,CAAC,WAAW;gBACpC,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;gBACH,OAAO,KAAK;YAChB;QACJ;QACA,IAAI,CAAC,wBAAwB;IACjC;IACA,WAAW,IAAI,EAAE,KAAK,EAAE;QACpB,IAAI,CAAC,WAAW,CAAC,MAAM;IAC3B;IACA,UAAU,SAAS,EAAE;QACjB,OAAO,IAAI,CAAC,iBAAiB,CAAC,6BAA6B,CAAC,cAAc,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;IAC9H;IACA,UAAU,IAAI,EAAE;QACZ,OAAO,IAAI,CAAC,iBAAiB,CAAC,wBAAwB,CAAC;IAC3D;IACA,mBAAmB;QACf,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,IAAI,KAAK,WAAW,EAAE;YAClB,KAAK,WAAW,CAAC,MAAM,GAAG,IAAI,CAAE;gBAC5B,SAAS,WAAW,CAAC;YACzB;QACJ,OAAO;YACH,SAAS,WAAW,CAAC;QACzB;QACA,OAAO,SAAS,OAAO;IAC3B;IACA,WAAW,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE;QAC1B,MAAM,QAAQ,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,MAAM,CAAC;QACtD,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,IAAI;QACtC,MAAM,OAAO,CAAA,GAAA,mLAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAChC,IAAI,CAAC,MAAM;YACP;QACJ;QACA,OAAQ,UAAU,MAAM;YACpB,KAAK;gBACD,OAAO;YACX,KAAK;gBAAG;oBACJ,MAAM,aAAa,IAAI,CAAC,0BAA0B,CAAC,QAAQ,MAAM,OAAO,IAAI,CAAC,OAAO,EAAE;oBACtF,IAAI,CAAC,iBAAiB,CAAC,MAAM,QAAQ;oBACrC,MAAM,WAAW,CAAA,GAAA,mLAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;oBACzC,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC,eAAe,CAAC,IAAI,CAAC,iCAAiC,CAAC,UAAU,QAAQ;wBAC3G,IAAI,CAAC,MAAM,CAAC,SAAS;oBACzB;oBACA;gBACJ;YACA;gBACI,IAAI,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;oBAClB,IAAI,CAAC,IAAI,CAAC,kCAAkC,CAAC,MAAM,SAAS;wBACxD,IAAI;wBACJ,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,QAAS,CAAC,YAAY;4BACvB,MAAM,aAAa,IAAI,CAAC,0BAA0B,CAAC,YAAY,MAAM,aAAa,IAAI,CAAC,WAAW,EAAE;4BACpG,IAAI,CAAC,iBAAiB,CAAC,MAAM,YAAY;4BACzC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,2BAA2B,CAAC,aAAa;gCACpE,mBAAmB;4BACvB;wBACJ;wBACA,oBAAoB,IAAI,CAAC,MAAM,CAAC,SAAS;oBAC7C;gBACJ;QACR;IACJ;IACA,WAAW;QACP,OAAO,4JAAA,CAAA,UAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB;IAClE;IACA,UAAU,IAAI,EAAE;QACZ,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,CAAC,GAAG,EAAE,OAAO,CAAC,EAAE,QAAM,IAAI,iJAAA,CAAA,UAAI,EAAE;IAC3C;IACA,wBAAwB;QACpB,OAAO,IAAI,CAAC,mBAAmB;IACnC;AACJ;AACA,CAAA,GAAA,kKAAA,CAAA,UAAiB,AAAD,EAAE,UAAU;uCACb", "ignoreList": [0], "debugId": null}}]}