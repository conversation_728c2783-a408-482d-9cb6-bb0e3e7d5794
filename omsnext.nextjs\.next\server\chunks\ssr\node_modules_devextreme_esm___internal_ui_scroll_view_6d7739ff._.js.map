{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/consts.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/consts.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport const SCROLL_LINE_HEIGHT = 40;\r\nexport const DIRECTION_VERTICAL = \"vertical\";\r\nexport const DIRECTION_HORIZONTAL = \"horizontal\";\r\nexport const DIRECTION_BOTH = \"both\";\r\nexport const SCROLLABLE_SIMULATED_CLASS = \"dx-scrollable-simulated\";\r\nexport const SCROLLABLE_CONTENT_CLASS = \"dx-scrollable-content\";\r\nexport const SCROLLABLE_WRAPPER_CLASS = \"dx-scrollable-wrapper\";\r\nexport const SCROLLABLE_CONTAINER_CLASS = \"dx-scrollable-container\";\r\nexport const SCROLLABLE_DISABLED_CLASS = \"dx-scrollable-disabled\";\r\nexport const SCROLLABLE_SCROLLBAR_SIMULATED = \"dx-scrollable-scrollbar-simulated\";\r\nexport const SCROLL<PERSON>LE_SCROLLBARS_HIDDEN = \"dx-scrollable-scrollbars-hidden\";\r\nexport const SCROLLABLE_SCROLLBARS_ALWAYSVISIBLE = \"dx-scrollable-scrollbars-alwaysvisible\";\r\nexport const SCROLLABLE_SCROLLBAR_CLASS = \"dx-scrollable-scrollbar\";\r\nexport const SCROLLABLE_SCROLLBAR_ACTIVE_CLASS = \"dx-scrollable-scrollbar-active\";\r\nexport const SCROLLABLE_SCROLL_CLASS = \"dx-scrollable-scroll\";\r\nexport const SCROLLABLE_SCROLL_CONTENT_CLASS = \"dx-scrollable-scroll-content\";\r\nexport const HOVER_ENABLED_STATE = \"dx-scrollbar-hoverable\";\r\nexport const SCROLLVIEW_CONTENT_CLASS = \"dx-scrollview-content\";\r\nexport const SCROLLVIEW_TOP_POCKET_CLASS = \"dx-scrollview-top-pocket\";\r\nexport const SCROLLVIEW_PULLDOWN = \"dx-scrollview-pull-down\";\r\nexport const SCROLLVIEW_PULLDOWN_LOADING_CLASS = \"dx-scrollview-pull-down-loading\";\r\nexport const SCROLLVIEW_PULLDOWN_READY_CLASS = \"dx-scrollview-pull-down-ready\";\r\nexport const SCROLLVIEW_PULLDOWN_IMAGE_CLASS = \"dx-scrollview-pull-down-image\";\r\nexport const SCROLLVIEW_PULLDOWN_INDICATOR_CLASS = \"dx-scrollview-pull-down-indicator\";\r\nexport const SCROLLVIEW_PULLDOWN_TEXT_CLASS = \"dx-scrollview-pull-down-text\";\r\nexport const SCROLLVIEW_PULLDOWN_VISIBLE_TEXT_CLASS = \"dx-scrollview-pull-down-text-visible\";\r\nexport const PULLDOWN_ICON_CLASS = \"dx-icon-pulldown\";\r\nexport const SCROLLVIEW_BOTTOM_POCKET_CLASS = \"dx-scrollview-bottom-pocket\";\r\nexport const SCROLLVIEW_REACHBOTTOM_CLASS = \"dx-scrollview-scrollbottom\";\r\nexport const SCROLLVIEW_REACHBOTTOM_INDICATOR_CLASS = \"dx-scrollview-scrollbottom-indicator\";\r\nexport const SCROLLVIEW_REACHBOTTOM_TEXT_CLASS = \"dx-scrollview-scrollbottom-text\";\r\nexport const TopPocketState = {\r\n    STATE_RELEASED: 0,\r\n    STATE_READY: 1,\r\n    STATE_REFRESHING: 2,\r\n    STATE_LOADING: 3,\r\n    STATE_TOUCHED: 4,\r\n    STATE_PULLED: 5\r\n};\r\nexport const ShowScrollbarMode = {\r\n    HOVER: \"onHover\",\r\n    ALWAYS: \"always\",\r\n    NEVER: \"never\",\r\n    SCROLL: \"onScroll\"\r\n};\r\nexport const KEY_CODES = {\r\n    PAGE_UP: \"pageUp\",\r\n    PAGE_DOWN: \"pageDown\",\r\n    END: \"end\",\r\n    HOME: \"home\",\r\n    LEFT: \"leftArrow\",\r\n    UP: \"upArrow\",\r\n    RIGHT: \"rightArrow\",\r\n    DOWN: \"downArrow\"\r\n};\r\nexport const VALIDATE_WHEEL_TIMEOUT = 500;\r\nexport const HIDE_SCROLLBAR_TIMEOUT = 500;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACM,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,MAAM,uBAAuB;AAC7B,MAAM,iBAAiB;AACvB,MAAM,6BAA6B;AACnC,MAAM,2BAA2B;AACjC,MAAM,2BAA2B;AACjC,MAAM,6BAA6B;AACnC,MAAM,4BAA4B;AAClC,MAAM,iCAAiC;AACvC,MAAM,+BAA+B;AACrC,MAAM,sCAAsC;AAC5C,MAAM,6BAA6B;AACnC,MAAM,oCAAoC;AAC1C,MAAM,0BAA0B;AAChC,MAAM,kCAAkC;AACxC,MAAM,sBAAsB;AAC5B,MAAM,2BAA2B;AACjC,MAAM,8BAA8B;AACpC,MAAM,sBAAsB;AAC5B,MAAM,oCAAoC;AAC1C,MAAM,kCAAkC;AACxC,MAAM,kCAAkC;AACxC,MAAM,sCAAsC;AAC5C,MAAM,iCAAiC;AACvC,MAAM,yCAAyC;AAC/C,MAAM,sBAAsB;AAC5B,MAAM,iCAAiC;AACvC,MAAM,+BAA+B;AACrC,MAAM,yCAAyC;AAC/C,MAAM,oCAAoC;AAC1C,MAAM,iBAAiB;IAC1B,gBAAgB;IAChB,aAAa;IACb,kBAAkB;IAClB,eAAe;IACf,eAAe;IACf,cAAc;AAClB;AACO,MAAM,oBAAoB;IAC7B,OAAO;IACP,QAAQ;IACR,OAAO;IACP,QAAQ;AACZ;AACO,MAAM,YAAY;IACrB,SAAS;IACT,WAAW;IACX,KAAK;IACL,MAAM;IACN,MAAM;IACN,IAAI;IACJ,OAAO;IACP,MAAM;AACV;AACO,MAAM,yBAAyB;AAC/B,MAAM,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/get_relative_offset.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/get_relative_offset.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport function getRelativeOffset(targetElementClass, sourceElement) {\r\n    const offset = {\r\n        left: 0,\r\n        top: 0\r\n    };\r\n    let element = sourceElement;\r\n    while (null !== (_element = element) && void 0 !== _element && _element.offsetParent && !element.classList.contains(targetElementClass)) {\r\n        var _element;\r\n        const parentElement = element.offsetParent;\r\n        const elementRect = element.getBoundingClientRect();\r\n        const parentElementRect = parentElement.getBoundingClientRect();\r\n        offset.left += elementRect.left - parentElementRect.left;\r\n        offset.top += elementRect.top - parentElementRect.top;\r\n        element = element.offsetParent\r\n    }\r\n    return offset\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,kBAAkB,kBAAkB,EAAE,aAAa;IAC/D,MAAM,SAAS;QACX,MAAM;QACN,KAAK;IACT;IACA,IAAI,UAAU;IACd,MAAO,SAAS,CAAC,WAAW,OAAO,KAAK,KAAK,MAAM,YAAY,SAAS,YAAY,IAAI,CAAC,QAAQ,SAAS,CAAC,QAAQ,CAAC,oBAAqB;QACrI,IAAI;QACJ,MAAM,gBAAgB,QAAQ,YAAY;QAC1C,MAAM,cAAc,QAAQ,qBAAqB;QACjD,MAAM,oBAAoB,cAAc,qBAAqB;QAC7D,OAAO,IAAI,IAAI,YAAY,IAAI,GAAG,kBAAkB,IAAI;QACxD,OAAO,GAAG,IAAI,YAAY,GAAG,GAAG,kBAAkB,GAAG;QACrD,UAAU,QAAQ,YAAY;IAClC;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/get_element_location_internal.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/get_element_location_internal.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    titleize\r\n} from \"../../../../core/utils/inflector\";\r\nimport {\r\n    DIRECTION_VERTICAL,\r\n    SCROLLABLE_CONTENT_CLASS\r\n} from \"../consts\";\r\nimport {\r\n    getRelativeOffset\r\n} from \"./get_relative_offset\";\r\nexport function getElementLocationInternal(targetElement, direction, containerElement, scrollOffset, offset) {\r\n    let scrollableContentClass = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : SCROLLABLE_CONTENT_CLASS;\r\n    const additionalOffset = _extends({\r\n        top: 0,\r\n        left: 0,\r\n        right: 0,\r\n        bottom: 0\r\n    }, offset);\r\n    const isVertical = direction === DIRECTION_VERTICAL;\r\n    const prop = isVertical ? \"top\" : \"left\";\r\n    const inverseProp = isVertical ? \"bottom\" : \"right\";\r\n    const dimension = isVertical ? \"height\" : \"width\";\r\n    const containerOffsetSize = containerElement[`offset${titleize(dimension)}`];\r\n    const containerClientSize = containerElement[`client${titleize(dimension)}`];\r\n    const containerSize = containerElement.getBoundingClientRect()[dimension];\r\n    const elementSize = targetElement.getBoundingClientRect()[dimension];\r\n    let scale = 1;\r\n    if (Math.abs(containerSize - containerOffsetSize) > 1) {\r\n        scale = containerSize / containerOffsetSize\r\n    }\r\n    const relativeElementOffset = getRelativeOffset(scrollableContentClass, targetElement)[prop] / scale;\r\n    const containerScrollOffset = scrollOffset[prop];\r\n    const relativeStartOffset = containerScrollOffset - relativeElementOffset + additionalOffset[prop];\r\n    const relativeEndOffset = containerScrollOffset - relativeElementOffset - elementSize / scale + containerClientSize - additionalOffset[inverseProp];\r\n    if (relativeStartOffset <= 0 && relativeEndOffset >= 0) {\r\n        return containerScrollOffset\r\n    }\r\n    return containerScrollOffset - (Math.abs(relativeStartOffset) > Math.abs(relativeEndOffset) ? relativeEndOffset : relativeStartOffset)\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAIA;;;;;AAGO,SAAS,2BAA2B,aAAa,EAAE,SAAS,EAAE,gBAAgB,EAAE,YAAY,EAAE,MAAM;IACvG,IAAI,yBAAyB,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,iLAAA,CAAA,2BAAwB;IACtH,MAAM,mBAAmB,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE;QAC9B,KAAK;QACL,MAAM;QACN,OAAO;QACP,QAAQ;IACZ,GAAG;IACH,MAAM,aAAa,cAAc,iLAAA,CAAA,qBAAkB;IACnD,MAAM,OAAO,aAAa,QAAQ;IAClC,MAAM,cAAc,aAAa,WAAW;IAC5C,MAAM,YAAY,aAAa,WAAW;IAC1C,MAAM,sBAAsB,gBAAgB,CAAC,CAAC,MAAM,EAAE,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,CAAC;IAC5E,MAAM,sBAAsB,gBAAgB,CAAC,CAAC,MAAM,EAAE,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,CAAC;IAC5E,MAAM,gBAAgB,iBAAiB,qBAAqB,EAAE,CAAC,UAAU;IACzE,MAAM,cAAc,cAAc,qBAAqB,EAAE,CAAC,UAAU;IACpE,IAAI,QAAQ;IACZ,IAAI,KAAK,GAAG,CAAC,gBAAgB,uBAAuB,GAAG;QACnD,QAAQ,gBAAgB;IAC5B;IACA,MAAM,wBAAwB,CAAA,GAAA,uMAAA,CAAA,oBAAiB,AAAD,EAAE,wBAAwB,cAAc,CAAC,KAAK,GAAG;IAC/F,MAAM,wBAAwB,YAAY,CAAC,KAAK;IAChD,MAAM,sBAAsB,wBAAwB,wBAAwB,gBAAgB,CAAC,KAAK;IAClG,MAAM,oBAAoB,wBAAwB,wBAAwB,cAAc,QAAQ,sBAAsB,gBAAgB,CAAC,YAAY;IACnJ,IAAI,uBAAuB,KAAK,qBAAqB,GAAG;QACpD,OAAO;IACX;IACA,OAAO,wBAAwB,CAAC,KAAK,GAAG,CAAC,uBAAuB,KAAK,GAAG,CAAC,qBAAqB,oBAAoB,mBAAmB;AACzI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 194, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scrollable.device.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scrollable.device.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport devices from \"../../../core/devices\";\r\nimport supportUtils from \"../../core/utils/m_support\";\r\nexport const deviceDependentOptions = function() {\r\n    return [{\r\n        device: () => !supportUtils.nativeScrolling,\r\n        options: {\r\n            useNative: false\r\n        }\r\n    }, {\r\n        device: device => !devices.isSimulator() && \"desktop\" === devices.real().deviceType && \"generic\" === device.platform,\r\n        options: {\r\n            bounceEnabled: false,\r\n            scrollByThumb: true,\r\n            scrollByContent: supportUtils.touch,\r\n            showScrollbar: \"onHover\"\r\n        }\r\n    }]\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;;;AACO,MAAM,yBAAyB;IAClC,OAAO;QAAC;YACJ,QAAQ,IAAM,CAAC,gMAAA,CAAA,UAAY,CAAC,eAAe;YAC3C,SAAS;gBACL,WAAW;YACf;QACJ;QAAG;YACC,QAAQ,CAAA,SAAU,CAAC,oJAAA,CAAA,UAAO,CAAC,WAAW,MAAM,cAAc,oJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,UAAU,IAAI,cAAc,OAAO,QAAQ;YACpH,SAAS;gBACL,eAAe;gBACf,eAAe;gBACf,iBAAiB,gMAAA,CAAA,UAAY,CAAC,KAAK;gBACnC,eAAe;YACnB;QACJ;KAAE;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 232, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scrollbar.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scrollbar.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    move\r\n} from \"../../../common/core/animation/translator\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport pointerEvents from \"../../../common/core/events/pointer\";\r\nimport {\r\n    addNamespace\r\n} from \"../../../common/core/events/utils/index\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    deferRenderer\r\n} from \"../../../core/utils/common\";\r\nimport readyCallback from \"../../../core/utils/ready_callbacks\";\r\nimport {\r\n    isPlainObject\r\n} from \"../../../core/utils/type\";\r\nimport Widget from \"../../core/widget/widget\";\r\nconst SCROLLBAR = \"dxScrollbar\";\r\nconst SCROLLABLE_SCROLLBAR_CLASS = \"dx-scrollable-scrollbar\";\r\nconst SCROLLABLE_SCROLLBAR_ACTIVE_CLASS = \"dx-scrollable-scrollbar-active\";\r\nconst SCROLLABLE_SCROLL_CLASS = \"dx-scrollable-scroll\";\r\nconst SCROLLABLE_SCROLL_CONTENT_CLASS = \"dx-scrollable-scroll-content\";\r\nconst HOVER_ENABLED_STATE = \"dx-scrollbar-hoverable\";\r\nconst HORIZONTAL = \"horizontal\";\r\nconst THUMB_MIN_SIZE = 15;\r\nconst SCROLLBAR_VISIBLE = {\r\n    onScroll: \"onScroll\",\r\n    onHover: \"onHover\",\r\n    always: \"always\",\r\n    never: \"never\"\r\n};\r\nlet activeScrollbar = null;\r\nclass Scrollbar extends Widget {\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            direction: null,\r\n            visible: false,\r\n            activeStateEnabled: false,\r\n            visibilityMode: SCROLLBAR_VISIBLE.onScroll,\r\n            containerSize: 0,\r\n            contentSize: 0,\r\n            expandable: true,\r\n            scaleRatio: 1\r\n        })\r\n    }\r\n    _init() {\r\n        super._init();\r\n        this._isHovered = false\r\n    }\r\n    _initMarkup() {\r\n        this._renderThumb();\r\n        super._initMarkup()\r\n    }\r\n    _render() {\r\n        super._render();\r\n        this._renderDirection();\r\n        this._update();\r\n        this._attachPointerDownHandler();\r\n        this.option(\"hoverStateEnabled\", this._isHoverMode());\r\n        const {\r\n            hoverStateEnabled: hoverStateEnabled\r\n        } = this.option();\r\n        this.$element().toggleClass(HOVER_ENABLED_STATE, hoverStateEnabled)\r\n    }\r\n    _renderThumb() {\r\n        this._$thumb = $(\"<div>\").addClass(\"dx-scrollable-scroll\");\r\n        $(\"<div>\").addClass(\"dx-scrollable-scroll-content\").appendTo(this._$thumb);\r\n        this.$element().addClass(\"dx-scrollable-scrollbar\").append(this._$thumb)\r\n    }\r\n    isThumb($element) {\r\n        return !!this.$element().find($element).length\r\n    }\r\n    _isHoverMode() {\r\n        const {\r\n            visibilityMode: visibilityMode,\r\n            expandable: expandable\r\n        } = this.option();\r\n        return (visibilityMode === SCROLLBAR_VISIBLE.onHover || visibilityMode === SCROLLBAR_VISIBLE.always) && expandable\r\n    }\r\n    _renderDirection() {\r\n        const {\r\n            direction: direction\r\n        } = this.option();\r\n        this.$element().addClass(`dx-scrollbar-${direction}`);\r\n        this._dimension = direction === HORIZONTAL ? \"width\" : \"height\";\r\n        this._prop = direction === HORIZONTAL ? \"left\" : \"top\"\r\n    }\r\n    _attachPointerDownHandler() {\r\n        eventsEngine.on(this._$thumb, addNamespace(pointerEvents.down, SCROLLBAR), this.feedbackOn.bind(this))\r\n    }\r\n    feedbackOn(e) {\r\n        null === e || void 0 === e || e.preventDefault();\r\n        this.$element().addClass(\"dx-scrollable-scrollbar-active\");\r\n        activeScrollbar = this\r\n    }\r\n    feedbackOff() {\r\n        this.$element().removeClass(\"dx-scrollable-scrollbar-active\");\r\n        activeScrollbar = null\r\n    }\r\n    cursorEnter() {\r\n        this._isHovered = true;\r\n        if (this._needScrollbar()) {\r\n            this.option(\"visible\", true)\r\n        }\r\n    }\r\n    cursorLeave() {\r\n        this._isHovered = false;\r\n        this.option(\"visible\", false)\r\n    }\r\n    _renderDimensions() {\r\n        this._$thumb.css({\r\n            width: this.option(\"width\"),\r\n            height: this.option(\"height\")\r\n        })\r\n    }\r\n    _toggleVisibility(visible) {\r\n        const {\r\n            visibilityMode: visibilityMode\r\n        } = this.option();\r\n        if (visibilityMode === SCROLLBAR_VISIBLE.onScroll) {\r\n            this._$thumb.css(\"opacity\")\r\n        }\r\n        visible = this._adjustVisibility(visible);\r\n        this.option().visible = visible;\r\n        this._$thumb.toggleClass(\"dx-state-invisible\", !visible)\r\n    }\r\n    _adjustVisibility(visible) {\r\n        if (this._baseContainerToContentRatio && !this._needScrollbar()) {\r\n            return false\r\n        }\r\n        const {\r\n            visibilityMode: visibilityMode\r\n        } = this.option();\r\n        switch (visibilityMode) {\r\n            case SCROLLBAR_VISIBLE.onScroll:\r\n                break;\r\n            case SCROLLBAR_VISIBLE.onHover:\r\n                visible = visible || !!this._isHovered;\r\n                break;\r\n            case SCROLLBAR_VISIBLE.never:\r\n                visible = false;\r\n                break;\r\n            case SCROLLBAR_VISIBLE.always:\r\n                visible = true\r\n        }\r\n        return visible\r\n    }\r\n    moveTo(location) {\r\n        if (this._isHidden()) {\r\n            return\r\n        }\r\n        if (isPlainObject(location)) {\r\n            location = location[this._prop] || 0\r\n        }\r\n        const scrollBarLocation = {};\r\n        scrollBarLocation[this._prop] = this._calculateScrollBarPosition(location);\r\n        move(this._$thumb, scrollBarLocation)\r\n    }\r\n    _calculateScrollBarPosition(location) {\r\n        return -location * this._thumbRatio\r\n    }\r\n    _update() {\r\n        const containerSize = Math.round(this.option(\"containerSize\"));\r\n        const contentSize = Math.round(this.option(\"contentSize\"));\r\n        let baseContainerSize = Math.round(this.option(\"baseContainerSize\"));\r\n        let baseContentSize = Math.round(this.option(\"baseContentSize\"));\r\n        if (isNaN(baseContainerSize)) {\r\n            baseContainerSize = containerSize;\r\n            baseContentSize = contentSize\r\n        }\r\n        const {\r\n            scaleRatio: scaleRatio\r\n        } = this.option();\r\n        this._baseContainerToContentRatio = baseContentSize ? baseContainerSize / baseContentSize : baseContainerSize;\r\n        this._realContainerToContentRatio = contentSize ? containerSize / contentSize : containerSize;\r\n        const thumbSize = Math.round(Math.max(Math.round(containerSize * this._realContainerToContentRatio), 15));\r\n        this._thumbRatio = (containerSize - thumbSize) / (scaleRatio * (contentSize - containerSize));\r\n        this.option(this._dimension, thumbSize / scaleRatio);\r\n        this.$element().css(\"display\", this._needScrollbar() ? \"\" : \"none\")\r\n    }\r\n    _isHidden() {\r\n        const {\r\n            visibilityMode: visibilityMode\r\n        } = this.option();\r\n        return visibilityMode === SCROLLBAR_VISIBLE.never\r\n    }\r\n    _needScrollbar() {\r\n        return !this._isHidden() && this._baseContainerToContentRatio < 1\r\n    }\r\n    containerToContentRatio() {\r\n        return this._realContainerToContentRatio\r\n    }\r\n    _normalizeSize(size) {\r\n        return isPlainObject(size) ? size[this._dimension] || 0 : size\r\n    }\r\n    _clean() {\r\n        super._clean();\r\n        if (this === activeScrollbar) {\r\n            activeScrollbar = null\r\n        }\r\n        eventsEngine.off(this._$thumb, `.${SCROLLBAR}`)\r\n    }\r\n    _optionChanged(args) {\r\n        if (this._isHidden()) {\r\n            return\r\n        }\r\n        switch (args.name) {\r\n            case \"containerSize\":\r\n            case \"contentSize\":\r\n                this.option()[args.name] = this._normalizeSize(args.value);\r\n                this._update();\r\n                break;\r\n            case \"baseContentSize\":\r\n            case \"baseContainerSize\":\r\n            case \"scaleRatio\":\r\n                this._update();\r\n                break;\r\n            case \"visibilityMode\":\r\n            case \"direction\":\r\n                this._invalidate();\r\n                break;\r\n            default:\r\n                super._optionChanged.apply(this, arguments)\r\n        }\r\n    }\r\n    update() {\r\n        deferRenderer((() => {\r\n            this._adjustVisibility() && this.option(\"visible\", true)\r\n        }))()\r\n    }\r\n}\r\nreadyCallback.add((() => {\r\n    eventsEngine.subscribeGlobal(domAdapter.getDocument(), addNamespace(pointerEvents.up, SCROLLBAR), (() => {\r\n        if (activeScrollbar) {\r\n            activeScrollbar.feedbackOff()\r\n        }\r\n    }))\r\n}));\r\nexport default Scrollbar;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAGA;AAAA;AACA;AAAA;AACA;AAAA;AAGA;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;;;;;;;;;;;;AACA,MAAM,YAAY;AAClB,MAAM,6BAA6B;AACnC,MAAM,oCAAoC;AAC1C,MAAM,0BAA0B;AAChC,MAAM,kCAAkC;AACxC,MAAM,sBAAsB;AAC5B,MAAM,aAAa;AACnB,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;IACtB,UAAU;IACV,SAAS;IACT,QAAQ;IACR,OAAO;AACX;AACA,IAAI,kBAAkB;AACtB,MAAM,kBAAkB,8KAAA,CAAA,UAAM;IAC1B,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,WAAW;YACX,SAAS;YACT,oBAAoB;YACpB,gBAAgB,kBAAkB,QAAQ;YAC1C,eAAe;YACf,aAAa;YACb,YAAY;YACZ,YAAY;QAChB;IACJ;IACA,QAAQ;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,UAAU,GAAG;IACtB;IACA,cAAc;QACV,IAAI,CAAC,YAAY;QACjB,KAAK,CAAC;IACV;IACA,UAAU;QACN,KAAK,CAAC;QACN,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,MAAM,CAAC,qBAAqB,IAAI,CAAC,YAAY;QAClD,MAAM,EACF,mBAAmB,iBAAiB,EACvC,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,qBAAqB;IACrD;IACA,eAAe;QACX,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACnC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,gCAAgC,QAAQ,CAAC,IAAI,CAAC,OAAO;QACzE,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,2BAA2B,MAAM,CAAC,IAAI,CAAC,OAAO;IAC3E;IACA,QAAQ,QAAQ,EAAE;QACd,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,UAAU,MAAM;IAClD;IACA,eAAe;QACX,MAAM,EACF,gBAAgB,cAAc,EAC9B,YAAY,UAAU,EACzB,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,CAAC,mBAAmB,kBAAkB,OAAO,IAAI,mBAAmB,kBAAkB,MAAM,KAAK;IAC5G;IACA,mBAAmB;QACf,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC,CAAC,aAAa,EAAE,WAAW;QACpD,IAAI,CAAC,UAAU,GAAG,cAAc,aAAa,UAAU;QACvD,IAAI,CAAC,KAAK,GAAG,cAAc,aAAa,SAAS;IACrD;IACA,4BAA4B;QACxB,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,OAAO,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yKAAA,CAAA,UAAa,CAAC,IAAI,EAAE,YAAY,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,IAAI;IACxG;IACA,WAAW,CAAC,EAAE;QACV,SAAS,KAAK,KAAK,MAAM,KAAK,EAAE,cAAc;QAC9C,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,kBAAkB,IAAI;IAC1B;IACA,cAAc;QACV,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC;QAC5B,kBAAkB;IACtB;IACA,cAAc;QACV,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,IAAI,CAAC,cAAc,IAAI;YACvB,IAAI,CAAC,MAAM,CAAC,WAAW;QAC3B;IACJ;IACA,cAAc;QACV,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,MAAM,CAAC,WAAW;IAC3B;IACA,oBAAoB;QAChB,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;YACb,OAAO,IAAI,CAAC,MAAM,CAAC;YACnB,QAAQ,IAAI,CAAC,MAAM,CAAC;QACxB;IACJ;IACA,kBAAkB,OAAO,EAAE;QACvB,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,mBAAmB,kBAAkB,QAAQ,EAAE;YAC/C,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC;QACrB;QACA,UAAU,IAAI,CAAC,iBAAiB,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG,OAAO,GAAG;QACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,sBAAsB,CAAC;IACpD;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,IAAI,CAAC,4BAA4B,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI;YAC7D,OAAO;QACX;QACA,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,OAAQ;YACJ,KAAK,kBAAkB,QAAQ;gBAC3B;YACJ,KAAK,kBAAkB,OAAO;gBAC1B,UAAU,WAAW,CAAC,CAAC,IAAI,CAAC,UAAU;gBACtC;YACJ,KAAK,kBAAkB,KAAK;gBACxB,UAAU;gBACV;YACJ,KAAK,kBAAkB,MAAM;gBACzB,UAAU;QAClB;QACA,OAAO;IACX;IACA,OAAO,QAAQ,EAAE;QACb,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB;QACJ;QACA,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YACzB,WAAW,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QACvC;QACA,MAAM,oBAAoB,CAAC;QAC3B,iBAAiB,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,2BAA2B,CAAC;QACjE,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE;IACvB;IACA,4BAA4B,QAAQ,EAAE;QAClC,OAAO,CAAC,WAAW,IAAI,CAAC,WAAW;IACvC;IACA,UAAU;QACN,MAAM,gBAAgB,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7C,MAAM,cAAc,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAC3C,IAAI,oBAAoB,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAC/C,IAAI,kBAAkB,KAAK,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7C,IAAI,MAAM,oBAAoB;YAC1B,oBAAoB;YACpB,kBAAkB;QACtB;QACA,MAAM,EACF,YAAY,UAAU,EACzB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,4BAA4B,GAAG,kBAAkB,oBAAoB,kBAAkB;QAC5F,IAAI,CAAC,4BAA4B,GAAG,cAAc,gBAAgB,cAAc;QAChF,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,gBAAgB,IAAI,CAAC,4BAA4B,GAAG;QACrG,IAAI,CAAC,WAAW,GAAG,CAAC,gBAAgB,SAAS,IAAI,CAAC,aAAa,CAAC,cAAc,aAAa,CAAC;QAC5F,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU,EAAE,YAAY;QACzC,IAAI,CAAC,QAAQ,GAAG,GAAG,CAAC,WAAW,IAAI,CAAC,cAAc,KAAK,KAAK;IAChE;IACA,YAAY;QACR,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,mBAAmB,kBAAkB,KAAK;IACrD;IACA,iBAAiB;QACb,OAAO,CAAC,IAAI,CAAC,SAAS,MAAM,IAAI,CAAC,4BAA4B,GAAG;IACpE;IACA,0BAA0B;QACtB,OAAO,IAAI,CAAC,4BAA4B;IAC5C;IACA,eAAe,IAAI,EAAE;QACjB,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,IAAI,IAAI;IAC9D;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,IAAI,KAAK,iBAAiB;YAC1B,kBAAkB;QACtB;QACA,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,WAAW;IAClD;IACA,eAAe,IAAI,EAAE;QACjB,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB;QACJ;QACA,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,MAAM,EAAE,CAAC,KAAK,IAAI,CAAC,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK,KAAK;gBACzD,IAAI,CAAC,OAAO;gBACZ;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,OAAO;gBACZ;YACJ,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ;gBACI,KAAK,CAAC,eAAe,KAAK,CAAC,IAAI,EAAE;QACzC;IACJ;IACA,SAAS;QACL,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAG;YACX,IAAI,CAAC,iBAAiB,MAAM,IAAI,CAAC,MAAM,CAAC,WAAW;QACvD;IACJ;AACJ;AACA,qKAAA,CAAA,UAAa,CAAC,GAAG,CAAE;IACf,uLAAA,CAAA,UAAY,CAAC,eAAe,CAAC,wJAAA,CAAA,UAAU,CAAC,WAAW,IAAI,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yKAAA,CAAA,UAAa,CAAC,EAAE,EAAE,YAAa;QAC/F,IAAI,iBAAiB;YACjB,gBAAgB,WAAW;QAC/B;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 480, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scrollable.native.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scrollable.native.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    isDxMouseWheelEvent\r\n} from \"../../../common/core/events/utils/index\";\r\nimport Class from \"../../../core/class\";\r\nimport devices from \"../../../core/devices\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getHeight,\r\n    getWidth\r\n} from \"../../../core/utils/size\";\r\nimport Scrollbar from \"./m_scrollbar\";\r\nconst SCROLLABLE_NATIVE = \"dxNativeScrollable\";\r\nconst SCROLLABLE_NATIVE_CLASS = \"dx-scrollable-native\";\r\nconst SCROLLABLE_SCROLLBAR_SIMULATED = \"dx-scrollable-scrollbar-simulated\";\r\nconst SCROLLABLE_SCROLLBARS_HIDDEN = \"dx-scrollable-scrollbars-hidden\";\r\nconst VERTICAL = \"vertical\";\r\nconst HORIZONTAL = \"horizontal\";\r\nconst HIDE_SCROLLBAR_TIMEOUT = 500;\r\nclass NativeStrategy extends(Class.inherit({})) {\r\n    ctor(scrollable) {\r\n        this._init(scrollable)\r\n    }\r\n    _init(scrollable) {\r\n        this._component = scrollable;\r\n        this._$element = scrollable.$element();\r\n        this._$container = $(scrollable.container());\r\n        this._$content = scrollable.$content();\r\n        const {\r\n            direction: direction,\r\n            useSimulatedScrollbar: useSimulatedScrollbar\r\n        } = scrollable.option();\r\n        this._direction = direction;\r\n        this._useSimulatedScrollbar = useSimulatedScrollbar;\r\n        this.option = scrollable.option.bind(scrollable);\r\n        this._createActionByOption = scrollable._createActionByOption.bind(scrollable);\r\n        this._isLocked = scrollable._isLocked.bind(scrollable);\r\n        this._isDirection = scrollable._isDirection.bind(scrollable);\r\n        this._allowedDirection = scrollable._allowedDirection.bind(scrollable);\r\n        this._getMaxOffset = scrollable._getMaxOffset.bind(scrollable);\r\n        this._isRtlNativeStrategy = scrollable._isRtlNativeStrategy.bind(scrollable)\r\n    }\r\n    render() {\r\n        const device = devices.real();\r\n        const deviceType = device.platform;\r\n        this._$element.addClass(\"dx-scrollable-native\").addClass(`dx-scrollable-native-${deviceType}`).toggleClass(SCROLLABLE_SCROLLBARS_HIDDEN, !this._isScrollbarVisible());\r\n        if (this._isScrollbarVisible() && this._useSimulatedScrollbar) {\r\n            this._renderScrollbars()\r\n        }\r\n    }\r\n    updateRtlPosition(isFirstRender) {\r\n        if (isFirstRender && this.option(\"rtlEnabled\")) {\r\n            if (this._isScrollbarVisible() && this._useSimulatedScrollbar) {\r\n                this._moveScrollbars()\r\n            }\r\n        }\r\n    }\r\n    _renderScrollbars() {\r\n        this._scrollbars = {};\r\n        this._hideScrollbarTimeout = 0;\r\n        this._$element.addClass(SCROLLABLE_SCROLLBAR_SIMULATED);\r\n        this._renderScrollbar(VERTICAL);\r\n        this._renderScrollbar(HORIZONTAL)\r\n    }\r\n    _renderScrollbar(direction) {\r\n        if (!this._isDirection(direction)) {\r\n            return\r\n        }\r\n        this._scrollbars[direction] = new Scrollbar($(\"<div>\").appendTo(this._$element), {\r\n            direction: direction,\r\n            expandable: this._component.option(\"scrollByThumb\")\r\n        })\r\n    }\r\n    handleInit(e) {}\r\n    handleStart() {}\r\n    handleMove(e) {\r\n        if (this._isLocked()) {\r\n            e.cancel = true;\r\n            return\r\n        }\r\n        if (this._allowedDirection()) {\r\n            e.originalEvent.isScrollingEvent = true\r\n        }\r\n    }\r\n    handleEnd() {}\r\n    handleCancel() {}\r\n    handleStop() {}\r\n    _eachScrollbar(callback) {\r\n        callback = callback.bind(this);\r\n        each(this._scrollbars || {}, ((direction, scrollbar) => {\r\n            callback(scrollbar, direction)\r\n        }))\r\n    }\r\n    createActions() {\r\n        this._scrollAction = this._createActionByOption(\"onScroll\");\r\n        this._updateAction = this._createActionByOption(\"onUpdated\")\r\n    }\r\n    _createActionArgs() {\r\n        const {\r\n            left: left,\r\n            top: top\r\n        } = this.location();\r\n        return {\r\n            event: this._eventForUserAction,\r\n            scrollOffset: this._getScrollOffset(),\r\n            reachedLeft: this._isRtlNativeStrategy() ? this._isReachedRight(-left) : this._isReachedLeft(left),\r\n            reachedRight: this._isRtlNativeStrategy() ? this._isReachedLeft(-Math.abs(left)) : this._isReachedRight(left),\r\n            reachedTop: this._isDirection(VERTICAL) ? Math.round(top) >= 0 : void 0,\r\n            reachedBottom: this._isDirection(VERTICAL) ? Math.round(Math.abs(top) - this._getMaxOffset().top) >= 0 : void 0\r\n        }\r\n    }\r\n    _getScrollOffset() {\r\n        const {\r\n            top: top,\r\n            left: left\r\n        } = this.location();\r\n        return {\r\n            top: -top,\r\n            left: this._normalizeOffsetLeft(-left)\r\n        }\r\n    }\r\n    _normalizeOffsetLeft(scrollLeft) {\r\n        if (this._isRtlNativeStrategy()) {\r\n            return this._getMaxOffset().left + scrollLeft\r\n        }\r\n        return scrollLeft\r\n    }\r\n    _isReachedLeft(left) {\r\n        return this._isDirection(HORIZONTAL) ? Math.round(left) >= 0 : void 0\r\n    }\r\n    _isReachedRight(left) {\r\n        return this._isDirection(HORIZONTAL) ? Math.round(Math.abs(left) - this._getMaxOffset().left) >= 0 : void 0\r\n    }\r\n    _isScrollbarVisible() {\r\n        const {\r\n            showScrollbar: showScrollbar\r\n        } = this.option();\r\n        return \"never\" !== showScrollbar && false !== showScrollbar\r\n    }\r\n    handleScroll(e) {\r\n        var _this$_scrollAction;\r\n        this._eventForUserAction = e;\r\n        this._moveScrollbars();\r\n        null === (_this$_scrollAction = this._scrollAction) || void 0 === _this$_scrollAction || _this$_scrollAction.call(this, this._createActionArgs())\r\n    }\r\n    _moveScrollbars() {\r\n        const {\r\n            top: top,\r\n            left: left\r\n        } = this._getScrollOffset();\r\n        this._eachScrollbar((scrollbar => {\r\n            scrollbar.moveTo({\r\n                top: -top,\r\n                left: -left\r\n            });\r\n            scrollbar.option(\"visible\", true)\r\n        }));\r\n        this._hideScrollbars()\r\n    }\r\n    _hideScrollbars() {\r\n        clearTimeout(this._hideScrollbarTimeout);\r\n        this._hideScrollbarTimeout = setTimeout((() => {\r\n            this._eachScrollbar((scrollbar => {\r\n                scrollbar.option(\"visible\", false)\r\n            }))\r\n        }), 500)\r\n    }\r\n    location() {\r\n        return {\r\n            left: -this._$container.scrollLeft(),\r\n            top: -this._$container.scrollTop()\r\n        }\r\n    }\r\n    disabledChanged() {}\r\n    update() {\r\n        this._update();\r\n        this._updateAction(this._createActionArgs())\r\n    }\r\n    _update() {\r\n        this._updateDimensions();\r\n        this._updateScrollbars()\r\n    }\r\n    _updateDimensions() {\r\n        this._containerSize = {\r\n            height: getHeight(this._$container),\r\n            width: getWidth(this._$container)\r\n        };\r\n        this._componentContentSize = {\r\n            height: getHeight(this._component.$content()),\r\n            width: getWidth(this._component.$content())\r\n        };\r\n        this._contentSize = {\r\n            height: getHeight(this._$content),\r\n            width: getWidth(this._$content)\r\n        }\r\n    }\r\n    _updateScrollbars() {\r\n        this._eachScrollbar((function(scrollbar, direction) {\r\n            const dimension = direction === VERTICAL ? \"height\" : \"width\";\r\n            scrollbar.option({\r\n                containerSize: this._containerSize[dimension],\r\n                contentSize: this._componentContentSize[dimension]\r\n            });\r\n            scrollbar.update()\r\n        }))\r\n    }\r\n    _allowedDirections() {\r\n        return {\r\n            vertical: this._isDirection(VERTICAL) && this._contentSize.height > this._containerSize.height,\r\n            horizontal: this._isDirection(HORIZONTAL) && this._contentSize.width > this._containerSize.width\r\n        }\r\n    }\r\n    dispose() {\r\n        const {\r\n            className: className\r\n        } = this._$element.get(0);\r\n        const scrollableNativeRegexp = new RegExp(\"dx-scrollable-native\\\\S*\", \"g\");\r\n        if (scrollableNativeRegexp.test(className)) {\r\n            this._$element.removeClass(className.match(scrollableNativeRegexp).join(\" \"))\r\n        }\r\n        eventsEngine.off(this._$element, `.${SCROLLABLE_NATIVE}`);\r\n        eventsEngine.off(this._$container, `.${SCROLLABLE_NATIVE}`);\r\n        this._removeScrollbars();\r\n        clearTimeout(this._hideScrollbarTimeout)\r\n    }\r\n    _removeScrollbars() {\r\n        this._eachScrollbar((scrollbar => {\r\n            scrollbar.$element().remove()\r\n        }))\r\n    }\r\n    scrollBy(distance) {\r\n        const location = this.location();\r\n        this._$container.scrollTop(Math.round(-location.top - distance.top));\r\n        this._$container.scrollLeft(Math.round(-location.left - distance.left))\r\n    }\r\n    validate(e) {\r\n        const {\r\n            disabled: disabled\r\n        } = this.option();\r\n        if (disabled) {\r\n            return false\r\n        }\r\n        if (isDxMouseWheelEvent(e) && this._isScrolledInMaxDirection(e)) {\r\n            return false\r\n        }\r\n        return !!this._allowedDirection()\r\n    }\r\n    _isScrolledInMaxDirection(e) {\r\n        const container = this._$container.get(0);\r\n        let result;\r\n        if (e.delta > 0) {\r\n            result = e.shiftKey ? !container.scrollLeft : !container.scrollTop\r\n        } else if (e.shiftKey) {\r\n            result = container.scrollLeft >= this._getMaxOffset().left\r\n        } else {\r\n            result = container.scrollTop >= this._getMaxOffset().top\r\n        }\r\n        return result\r\n    }\r\n    getDirection() {\r\n        return this._allowedDirection()\r\n    }\r\n}\r\nexport default NativeStrategy;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAIA;;;;;;;;;AACA,MAAM,oBAAoB;AAC1B,MAAM,0BAA0B;AAChC,MAAM,iCAAiC;AACvC,MAAM,+BAA+B;AACrC,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,yBAAyB;AAC/B,MAAM,uBAAuB,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC;IACxC,KAAK,UAAU,EAAE;QACb,IAAI,CAAC,KAAK,CAAC;IACf;IACA,MAAM,UAAU,EAAE;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG,WAAW,QAAQ;QACpC,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,SAAS;QACzC,IAAI,CAAC,SAAS,GAAG,WAAW,QAAQ;QACpC,MAAM,EACF,WAAW,SAAS,EACpB,uBAAuB,qBAAqB,EAC/C,GAAG,WAAW,MAAM;QACrB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,sBAAsB,GAAG;QAC9B,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,qBAAqB,GAAG,WAAW,qBAAqB,CAAC,IAAI,CAAC;QACnE,IAAI,CAAC,SAAS,GAAG,WAAW,SAAS,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,WAAW,YAAY,CAAC,IAAI,CAAC;QACjD,IAAI,CAAC,iBAAiB,GAAG,WAAW,iBAAiB,CAAC,IAAI,CAAC;QAC3D,IAAI,CAAC,aAAa,GAAG,WAAW,aAAa,CAAC,IAAI,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,WAAW,oBAAoB,CAAC,IAAI,CAAC;IACrE;IACA,SAAS;QACL,MAAM,SAAS,oJAAA,CAAA,UAAO,CAAC,IAAI;QAC3B,MAAM,aAAa,OAAO,QAAQ;QAClC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,wBAAwB,QAAQ,CAAC,CAAC,qBAAqB,EAAE,YAAY,EAAE,WAAW,CAAC,8BAA8B,CAAC,IAAI,CAAC,mBAAmB;QAClK,IAAI,IAAI,CAAC,mBAAmB,MAAM,IAAI,CAAC,sBAAsB,EAAE;YAC3D,IAAI,CAAC,iBAAiB;QAC1B;IACJ;IACA,kBAAkB,aAAa,EAAE;QAC7B,IAAI,iBAAiB,IAAI,CAAC,MAAM,CAAC,eAAe;YAC5C,IAAI,IAAI,CAAC,mBAAmB,MAAM,IAAI,CAAC,sBAAsB,EAAE;gBAC3D,IAAI,CAAC,eAAe;YACxB;QACJ;IACJ;IACA,oBAAoB;QAChB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACxB,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,gBAAgB,CAAC;IAC1B;IACA,iBAAiB,SAAS,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,YAAY;YAC/B;QACJ;QACA,IAAI,CAAC,WAAW,CAAC,UAAU,GAAG,IAAI,sLAAA,CAAA,UAAS,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,SAAS,GAAG;YAC7E,WAAW;YACX,YAAY,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;QACvC;IACJ;IACA,WAAW,CAAC,EAAE,CAAC;IACf,cAAc,CAAC;IACf,WAAW,CAAC,EAAE;QACV,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,EAAE,MAAM,GAAG;YACX;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,IAAI;YAC1B,EAAE,aAAa,CAAC,gBAAgB,GAAG;QACvC;IACJ;IACA,YAAY,CAAC;IACb,eAAe,CAAC;IAChB,aAAa,CAAC;IACd,eAAe,QAAQ,EAAE;QACrB,WAAW,SAAS,IAAI,CAAC,IAAI;QAC7B,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,WAAW,IAAI,CAAC,GAAI,CAAC,WAAW;YACtC,SAAS,WAAW;QACxB;IACJ;IACA,gBAAgB;QACZ,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAChD,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC;IACpD;IACA,oBAAoB;QAChB,MAAM,EACF,MAAM,IAAI,EACV,KAAK,GAAG,EACX,GAAG,IAAI,CAAC,QAAQ;QACjB,OAAO;YACH,OAAO,IAAI,CAAC,mBAAmB;YAC/B,cAAc,IAAI,CAAC,gBAAgB;YACnC,aAAa,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,IAAI,CAAC,cAAc,CAAC;YAC7F,cAAc,IAAI,CAAC,oBAAoB,KAAK,IAAI,CAAC,cAAc,CAAC,CAAC,KAAK,GAAG,CAAC,SAAS,IAAI,CAAC,eAAe,CAAC;YACxG,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,KAAK,KAAK,CAAC,QAAQ,IAAI,KAAK;YACtE,eAAe,IAAI,CAAC,YAAY,CAAC,YAAY,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,OAAO,IAAI,CAAC,aAAa,GAAG,GAAG,KAAK,IAAI,KAAK;QAClH;IACJ;IACA,mBAAmB;QACf,MAAM,EACF,KAAK,GAAG,EACR,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,QAAQ;QACjB,OAAO;YACH,KAAK,CAAC;YACN,MAAM,IAAI,CAAC,oBAAoB,CAAC,CAAC;QACrC;IACJ;IACA,qBAAqB,UAAU,EAAE;QAC7B,IAAI,IAAI,CAAC,oBAAoB,IAAI;YAC7B,OAAO,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG;QACvC;QACA,OAAO;IACX;IACA,eAAe,IAAI,EAAE;QACjB,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,KAAK,CAAC,SAAS,IAAI,KAAK;IACxE;IACA,gBAAgB,IAAI,EAAE;QAClB,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,QAAQ,IAAI,CAAC,aAAa,GAAG,IAAI,KAAK,IAAI,KAAK;IAC9G;IACA,sBAAsB;QAClB,MAAM,EACF,eAAe,aAAa,EAC/B,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,YAAY,iBAAiB,UAAU;IAClD;IACA,aAAa,CAAC,EAAE;QACZ,IAAI;QACJ,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,eAAe;QACpB,SAAS,CAAC,sBAAsB,IAAI,CAAC,aAAa,KAAK,KAAK,MAAM,uBAAuB,oBAAoB,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,iBAAiB;IAClJ;IACA,kBAAkB;QACd,MAAM,EACF,KAAK,GAAG,EACR,MAAM,IAAI,EACb,GAAG,IAAI,CAAC,gBAAgB;QACzB,IAAI,CAAC,cAAc,CAAE,CAAA;YACjB,UAAU,MAAM,CAAC;gBACb,KAAK,CAAC;gBACN,MAAM,CAAC;YACX;YACA,UAAU,MAAM,CAAC,WAAW;QAChC;QACA,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,aAAa,IAAI,CAAC,qBAAqB;QACvC,IAAI,CAAC,qBAAqB,GAAG,WAAY;YACrC,IAAI,CAAC,cAAc,CAAE,CAAA;gBACjB,UAAU,MAAM,CAAC,WAAW;YAChC;QACJ,GAAI;IACR;IACA,WAAW;QACP,OAAO;YACH,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,UAAU;YAClC,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,SAAS;QACpC;IACJ;IACA,kBAAkB,CAAC;IACnB,SAAS;QACL,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iBAAiB;IAC7C;IACA,UAAU;QACN,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,iBAAiB;IAC1B;IACA,oBAAoB;QAChB,IAAI,CAAC,cAAc,GAAG;YAClB,QAAQ,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,WAAW;YAClC,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,WAAW;QACpC;QACA,IAAI,CAAC,qBAAqB,GAAG;YACzB,QAAQ,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ;YAC1C,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,UAAU,CAAC,QAAQ;QAC5C;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,QAAQ,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,SAAS;YAChC,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,SAAS;QAClC;IACJ;IACA,oBAAoB;QAChB,IAAI,CAAC,cAAc,CAAE,SAAS,SAAS,EAAE,SAAS;YAC9C,MAAM,YAAY,cAAc,WAAW,WAAW;YACtD,UAAU,MAAM,CAAC;gBACb,eAAe,IAAI,CAAC,cAAc,CAAC,UAAU;gBAC7C,aAAa,IAAI,CAAC,qBAAqB,CAAC,UAAU;YACtD;YACA,UAAU,MAAM;QACpB;IACJ;IACA,qBAAqB;QACjB,OAAO;YACH,UAAU,IAAI,CAAC,YAAY,CAAC,aAAa,IAAI,CAAC,YAAY,CAAC,MAAM,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM;YAC9F,YAAY,IAAI,CAAC,YAAY,CAAC,eAAe,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc,CAAC,KAAK;QACpG;IACJ;IACA,UAAU;QACN,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;QACvB,MAAM,yBAAyB,IAAI,OAAO,4BAA4B;QACtE,IAAI,uBAAuB,IAAI,CAAC,YAAY;YACxC,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,UAAU,KAAK,CAAC,wBAAwB,IAAI,CAAC;QAC5E;QACA,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,mBAAmB;QACxD,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,mBAAmB;QAC1D,IAAI,CAAC,iBAAiB;QACtB,aAAa,IAAI,CAAC,qBAAqB;IAC3C;IACA,oBAAoB;QAChB,IAAI,CAAC,cAAc,CAAE,CAAA;YACjB,UAAU,QAAQ,GAAG,MAAM;QAC/B;IACJ;IACA,SAAS,QAAQ,EAAE;QACf,MAAM,WAAW,IAAI,CAAC,QAAQ;QAC9B,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,KAAK,KAAK,CAAC,CAAC,SAAS,GAAG,GAAG,SAAS,GAAG;QAClE,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,KAAK,KAAK,CAAC,CAAC,SAAS,IAAI,GAAG,SAAS,IAAI;IACzE;IACA,SAAS,CAAC,EAAE;QACR,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,UAAU;YACV,OAAO;QACX;QACA,IAAI,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,IAAI,CAAC,yBAAyB,CAAC,IAAI;YAC7D,OAAO;QACX;QACA,OAAO,CAAC,CAAC,IAAI,CAAC,iBAAiB;IACnC;IACA,0BAA0B,CAAC,EAAE;QACzB,MAAM,YAAY,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACvC,IAAI;QACJ,IAAI,EAAE,KAAK,GAAG,GAAG;YACb,SAAS,EAAE,QAAQ,GAAG,CAAC,UAAU,UAAU,GAAG,CAAC,UAAU,SAAS;QACtE,OAAO,IAAI,EAAE,QAAQ,EAAE;YACnB,SAAS,UAAU,UAAU,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI;QAC9D,OAAO;YACH,SAAS,UAAU,SAAS,IAAI,IAAI,CAAC,aAAa,GAAG,GAAG;QAC5D;QACA,OAAO;IACX;IACA,eAAe;QACX,OAAO,IAAI,CAAC,iBAAiB;IACjC;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 748, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_animator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_animator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    cancelAnimationFrame,\r\n    requestAnimationFrame\r\n} from \"../../../common/core/animation/frame\";\r\nimport Class from \"../../../core/class\";\r\nclass Animator extends(Class.inherit({})) {\r\n    ctor(strategy) {\r\n        this._finished = true;\r\n        this._stopped = false;\r\n        this._proxiedStepCore = this._stepCore.bind(this)\r\n    }\r\n    start() {\r\n        this._stopped = false;\r\n        this._finished = false;\r\n        this._stepCore()\r\n    }\r\n    stop() {\r\n        this._stopped = true;\r\n        cancelAnimationFrame(this._stepAnimationFrame)\r\n    }\r\n    _stepCore() {\r\n        if (this._isStopped()) {\r\n            this._stop();\r\n            return\r\n        }\r\n        if (this._isFinished()) {\r\n            this._finished = true;\r\n            this._complete();\r\n            return\r\n        }\r\n        this._step();\r\n        this._stepAnimationFrame = requestAnimationFrame(this._proxiedStepCore)\r\n    }\r\n    _step() {\r\n        Class.abstract()\r\n    }\r\n    _isFinished() {}\r\n    _stop() {}\r\n    _complete() {}\r\n    _isStopped() {\r\n        return this._stopped\r\n    }\r\n    inProgress() {\r\n        return !(this._stopped || this._finished)\r\n    }\r\n}\r\nexport default Animator;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAIA;;;AACA,MAAM,iBAAiB,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC;IAClC,KAAK,QAAQ,EAAE;QACX,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;IACpD;IACA,QAAQ;QACJ,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS;IAClB;IACA,OAAO;QACH,IAAI,CAAC,QAAQ,GAAG;QAChB,CAAA,GAAA,yKAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI,CAAC,mBAAmB;IACjD;IACA,YAAY;QACR,IAAI,IAAI,CAAC,UAAU,IAAI;YACnB,IAAI,CAAC,KAAK;YACV;QACJ;QACA,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,SAAS;YACd;QACJ;QACA,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,yKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,CAAC,gBAAgB;IAC1E;IACA,QAAQ;QACJ,kJAAA,CAAA,UAAK,CAAC,QAAQ;IAClB;IACA,cAAc,CAAC;IACf,QAAQ,CAAC;IACT,YAAY,CAAC;IACb,aAAa;QACT,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,aAAa;QACT,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,SAAS;IAC5C;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 808, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scrollable.simulated.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scrollable.simulated.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    locate,\r\n    move,\r\n    resetPosition\r\n} from \"../../../common/core/animation/translator\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport {\r\n    addNamespace as addEventNamespace,\r\n    isCommandKeyPressed,\r\n    isDxMouseWheelEvent,\r\n    normalizeKeyName\r\n} from \"../../../common/core/events/utils/index\";\r\nimport Class from \"../../../core/class\";\r\nimport domAdapter from \"../../../core/dom_adapter\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    deferRender,\r\n    deferRenderer,\r\n    deferUpdate,\r\n    deferUpdater\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../core/utils/extend\";\r\nimport {\r\n    titleize\r\n} from \"../../../core/utils/inflector\";\r\nimport {\r\n    each,\r\n    map\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getBoundingRect\r\n} from \"../../../core/utils/position\";\r\nimport {\r\n    getHeight,\r\n    getWidth\r\n} from \"../../../core/utils/size\";\r\nimport {\r\n    isDefined\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    getWindow,\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport Animator from \"./m_animator\";\r\nimport Scrollbar from \"./m_scrollbar\";\r\nconst SCROLLABLE_SIMULATED = \"dxSimulatedScrollable\";\r\nconst SCROLLABLE_STRATEGY = \"dxScrollableStrategy\";\r\nconst SCROLLABLE_SIMULATED_CURSOR = `${SCROLLABLE_SIMULATED}Cursor`;\r\nconst SCROLLABLE_SIMULATED_KEYBOARD = `${SCROLLABLE_SIMULATED}Keyboard`;\r\nconst SCROLLABLE_SIMULATED_CLASS = \"dx-scrollable-simulated\";\r\nconst SCROLLABLE_SCROLLBARS_ALWAYSVISIBLE = \"dx-scrollable-scrollbars-alwaysvisible\";\r\nconst SCROLLABLE_SCROLLBAR_CLASS = \"dx-scrollable-scrollbar\";\r\nconst VERTICAL = \"vertical\";\r\nconst HORIZONTAL = \"horizontal\";\r\nconst ACCELERATION = .92;\r\nconst OUT_BOUNDS_ACCELERATION = .5;\r\nconst MIN_VELOCITY_LIMIT = 1;\r\nconst FRAME_DURATION = Math.round(1e3 / 60);\r\nconst SCROLL_LINE_HEIGHT = 40;\r\nconst VALIDATE_WHEEL_TIMEOUT = 500;\r\nconst BOUNCE_MIN_VELOCITY_LIMIT = .2;\r\nconst BOUNCE_DURATION = 400;\r\nconst BOUNCE_FRAMES = 400 / FRAME_DURATION;\r\nconst BOUNCE_ACCELERATION_SUM = (1 - .92 ** BOUNCE_FRAMES) / (1 - .92);\r\nconst KEY_CODES = {\r\n    PAGE_UP: \"pageUp\",\r\n    PAGE_DOWN: \"pageDown\",\r\n    END: \"end\",\r\n    HOME: \"home\",\r\n    LEFT: \"leftArrow\",\r\n    UP: \"upArrow\",\r\n    RIGHT: \"rightArrow\",\r\n    DOWN: \"downArrow\",\r\n    TAB: \"tab\"\r\n};\r\nclass InertiaAnimator extends Animator {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.VELOCITY_LIMIT = 1\r\n    }\r\n    ctor(scroller) {\r\n        super.ctor();\r\n        this.scroller = scroller\r\n    }\r\n    _isFinished() {\r\n        return Math.abs(this.scroller._velocity) <= this.VELOCITY_LIMIT\r\n    }\r\n    _step() {\r\n        this.scroller._scrollStep(this.scroller._velocity);\r\n        this.scroller._velocity *= this._acceleration()\r\n    }\r\n    _acceleration() {\r\n        return this.scroller._inBounds() ? .92 : .5\r\n    }\r\n    _complete() {\r\n        this.scroller._scrollComplete()\r\n    }\r\n}\r\nclass BounceAnimator extends InertiaAnimator {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.VELOCITY_LIMIT = .2\r\n    }\r\n    _isFinished() {\r\n        return this.scroller._crossBoundOnNextStep() || super._isFinished()\r\n    }\r\n    _acceleration() {\r\n        return .92\r\n    }\r\n    _complete() {\r\n        this.scroller._move(this.scroller._bounceLocation);\r\n        super._complete()\r\n    }\r\n}\r\nexport class Scroller extends(Class.inherit({})) {\r\n    ctor(options) {\r\n        this._initOptions(options);\r\n        this._initAnimators();\r\n        this._initScrollbar()\r\n    }\r\n    _initOptions(options) {\r\n        this._location = 0;\r\n        this._topReached = false;\r\n        this._bottomReached = false;\r\n        this._axis = options.direction === HORIZONTAL ? \"x\" : \"y\";\r\n        this._prop = options.direction === HORIZONTAL ? \"left\" : \"top\";\r\n        this._dimension = options.direction === HORIZONTAL ? \"width\" : \"height\";\r\n        this._scrollProp = options.direction === HORIZONTAL ? \"scrollLeft\" : \"scrollTop\";\r\n        each(options, ((optionName, optionValue) => {\r\n            this[`_${optionName}`] = optionValue\r\n        }))\r\n    }\r\n    _initAnimators() {\r\n        this._inertiaAnimator = new InertiaAnimator(this);\r\n        this._bounceAnimator = new BounceAnimator(this)\r\n    }\r\n    _initScrollbar() {\r\n        this._scrollbar = new Scrollbar($(\"<div>\").appendTo(this._$container), {\r\n            direction: this._direction,\r\n            visible: this._scrollByThumb,\r\n            visibilityMode: this._visibilityModeNormalize(this._scrollbarVisible),\r\n            expandable: this._scrollByThumb\r\n        });\r\n        this._$scrollbar = this._scrollbar.$element()\r\n    }\r\n    _visibilityModeNormalize(mode) {\r\n        return true === mode ? \"onScroll\" : false === mode ? \"never\" : mode\r\n    }\r\n    _scrollStep(delta) {\r\n        const prevLocation = this._location;\r\n        this._location += delta;\r\n        this._suppressBounce();\r\n        this._move();\r\n        if (Math.abs(prevLocation - this._location) < 1) {\r\n            return\r\n        }\r\n        eventsEngine.triggerHandler(this._$container, {\r\n            type: \"scroll\"\r\n        })\r\n    }\r\n    _suppressBounce() {\r\n        if (this._bounceEnabled || this._inBounds(this._location)) {\r\n            return\r\n        }\r\n        this._velocity = 0;\r\n        this._location = this._boundLocation()\r\n    }\r\n    _boundLocation(location) {\r\n        location = void 0 !== location ? location : this._location;\r\n        return Math.max(Math.min(location, this._maxOffset), this._minOffset)\r\n    }\r\n    _move(location) {\r\n        this._location = void 0 !== location ? location * this._getScaleRatio() : this._location;\r\n        this._moveContent();\r\n        this._moveScrollbar()\r\n    }\r\n    _moveContent() {\r\n        const location = this._location;\r\n        this._$container[this._scrollProp](-location / this._getScaleRatio());\r\n        this._moveContentByTranslator(location)\r\n    }\r\n    _getScaleRatio() {\r\n        if (hasWindow() && !this._scaleRatio) {\r\n            const element = this._$element.get(0);\r\n            const realDimension = this._getRealDimension(element, this._dimension);\r\n            const baseDimension = this._getBaseDimension(element, this._dimension);\r\n            this._scaleRatio = Math.round(realDimension / baseDimension * 100) / 100\r\n        }\r\n        return this._scaleRatio || 1\r\n    }\r\n    _getRealDimension(element, dimension) {\r\n        return Math.round(getBoundingRect(element)[dimension])\r\n    }\r\n    _getBaseDimension(element, dimension) {\r\n        const dimensionName = `offset${titleize(dimension)}`;\r\n        return element[dimensionName]\r\n    }\r\n    _moveContentByTranslator(location) {\r\n        let translateOffset;\r\n        const minOffset = -this._maxScrollPropValue;\r\n        if (location > 0) {\r\n            translateOffset = location\r\n        }\r\n        if (location <= minOffset) {\r\n            translateOffset = location - minOffset\r\n        }\r\n        if (this._translateOffset === translateOffset) {\r\n            return\r\n        }\r\n        const targetLocation = {};\r\n        targetLocation[this._prop] = translateOffset;\r\n        this._translateOffset = translateOffset;\r\n        if (!translateOffset) {\r\n            resetPosition(this._$content);\r\n            return\r\n        }\r\n        move(this._$content, targetLocation)\r\n    }\r\n    _moveScrollbar() {\r\n        this._scrollbar.moveTo(this._location)\r\n    }\r\n    _scrollComplete() {\r\n        if (this._inBounds()) {\r\n            this._hideScrollbar();\r\n            if (this._completeDeferred) {\r\n                this._completeDeferred.resolve()\r\n            }\r\n        }\r\n        this._scrollToBounds()\r\n    }\r\n    _scrollToBounds() {\r\n        var _this$_bounceAction;\r\n        if (this._inBounds()) {\r\n            return\r\n        }\r\n        null === (_this$_bounceAction = this._bounceAction) || void 0 === _this$_bounceAction || _this$_bounceAction.call(this);\r\n        this._setupBounce();\r\n        this._bounceAnimator.start()\r\n    }\r\n    _setupBounce() {\r\n        const boundLocation = this._bounceLocation = this._boundLocation();\r\n        const bounceDistance = boundLocation - this._location;\r\n        this._velocity = bounceDistance / BOUNCE_ACCELERATION_SUM\r\n    }\r\n    _inBounds(location) {\r\n        location = void 0 !== location ? location : this._location;\r\n        return this._boundLocation(location) === location\r\n    }\r\n    _crossBoundOnNextStep() {\r\n        const location = this._location;\r\n        const nextLocation = location + this._velocity;\r\n        return location < this._minOffset && nextLocation >= this._minOffset || location > this._maxOffset && nextLocation <= this._maxOffset\r\n    }\r\n    _initHandler(e) {\r\n        this._stopScrolling();\r\n        this._prepareThumbScrolling(e)\r\n    }\r\n    _stopScrolling() {\r\n        deferRenderer((() => {\r\n            this._hideScrollbar();\r\n            this._inertiaAnimator.stop();\r\n            this._bounceAnimator.stop()\r\n        }))()\r\n    }\r\n    _prepareThumbScrolling(e) {\r\n        if (isDxMouseWheelEvent(e.originalEvent)) {\r\n            return\r\n        }\r\n        const $target = $(e.originalEvent.target);\r\n        const scrollbarClicked = this._isScrollbar($target);\r\n        if (scrollbarClicked) {\r\n            this._moveToMouseLocation(e)\r\n        }\r\n        this._thumbScrolling = scrollbarClicked || this._isThumb($target);\r\n        this._crossThumbScrolling = !this._thumbScrolling && this._isAnyThumbScrolling($target);\r\n        if (this._thumbScrolling) {\r\n            this._scrollbar.feedbackOn()\r\n        }\r\n    }\r\n    _isThumbScrollingHandler($target) {\r\n        return this._isThumb($target)\r\n    }\r\n    _moveToMouseLocation(e) {\r\n        const mouseLocation = e[`page${this._axis.toUpperCase()}`] - this._$element.offset()[this._prop];\r\n        const location = this._location + mouseLocation / this._containerToContentRatio() - getHeight(this._$container) / 2;\r\n        this._scrollStep(-Math.round(location))\r\n    }\r\n    _startHandler() {\r\n        this._showScrollbar()\r\n    }\r\n    _moveHandler(delta) {\r\n        if (this._crossThumbScrolling) {\r\n            return\r\n        }\r\n        if (this._thumbScrolling) {\r\n            delta[this._axis] = -Math.round(delta[this._axis] / this._containerToContentRatio())\r\n        }\r\n        this._scrollBy(delta)\r\n    }\r\n    _scrollBy(delta) {\r\n        delta = delta[this._axis];\r\n        if (!this._inBounds()) {\r\n            delta *= .5\r\n        }\r\n        this._scrollStep(delta)\r\n    }\r\n    _scrollByHandler(delta) {\r\n        if (!delta.x && !delta.y) {\r\n            return\r\n        }\r\n        this._scrollBy(delta);\r\n        this._scrollComplete()\r\n    }\r\n    _containerToContentRatio() {\r\n        return this._scrollbar.containerToContentRatio()\r\n    }\r\n    _endHandler(velocity) {\r\n        this._completeDeferred = Deferred();\r\n        this._velocity = velocity[this._axis];\r\n        this._inertiaHandler();\r\n        this._resetThumbScrolling();\r\n        return this._completeDeferred.promise()\r\n    }\r\n    _inertiaHandler() {\r\n        this._suppressInertia();\r\n        this._inertiaAnimator.start()\r\n    }\r\n    _suppressInertia() {\r\n        if (!this._inertiaEnabled || this._thumbScrolling) {\r\n            this._velocity = 0\r\n        }\r\n    }\r\n    _resetThumbScrolling() {\r\n        this._thumbScrolling = false;\r\n        this._crossThumbScrolling = false\r\n    }\r\n    _stopHandler() {\r\n        if (this._thumbScrolling) {\r\n            this._scrollComplete()\r\n        }\r\n        this._resetThumbScrolling();\r\n        this._scrollToBounds()\r\n    }\r\n    _disposeHandler() {\r\n        this._stopScrolling();\r\n        this._$scrollbar.remove()\r\n    }\r\n    _updateHandler() {\r\n        this._update();\r\n        this._moveToBounds()\r\n    }\r\n    _update() {\r\n        this._stopScrolling();\r\n        return deferUpdate((() => {\r\n            this._resetScaleRatio();\r\n            this._updateLocation();\r\n            this._updateBounds();\r\n            this._updateScrollbar();\r\n            deferRender((() => {\r\n                this._moveScrollbar();\r\n                this._scrollbar.update()\r\n            }))\r\n        }))\r\n    }\r\n    _resetScaleRatio() {\r\n        this._scaleRatio = null\r\n    }\r\n    _updateLocation() {\r\n        this._location = (locate(this._$content)[this._prop] - this._$container[this._scrollProp]()) * this._getScaleRatio()\r\n    }\r\n    _updateBounds() {\r\n        this._maxOffset = this._getMaxOffset();\r\n        this._minOffset = this._getMinOffset()\r\n    }\r\n    _getMaxOffset() {\r\n        return 0\r\n    }\r\n    _getMinOffset() {\r\n        this._maxScrollPropValue = Math.max(this._contentSize() - this._containerSize(), 0);\r\n        return -this._maxScrollPropValue\r\n    }\r\n    _updateScrollbar() {\r\n        deferUpdater((() => {\r\n            const containerSize = this._containerSize();\r\n            const contentSize = this._contentSize();\r\n            const baseContainerSize = this._getBaseDimension(this._$container.get(0), this._dimension);\r\n            const baseContentSize = this._getBaseDimension(this._$content.get(0), this._dimension);\r\n            deferRender((() => {\r\n                this._scrollbar.option({\r\n                    containerSize: containerSize,\r\n                    contentSize: contentSize,\r\n                    baseContainerSize: baseContainerSize,\r\n                    baseContentSize: baseContentSize,\r\n                    scaleRatio: this._getScaleRatio()\r\n                })\r\n            }))\r\n        }))()\r\n    }\r\n    _moveToBounds() {\r\n        deferRenderer(deferUpdater(deferRenderer((() => {\r\n            const location = this._boundLocation();\r\n            const locationChanged = location !== this._location;\r\n            this._location = location;\r\n            this._move();\r\n            if (locationChanged) {\r\n                var _this$_scrollAction;\r\n                null === (_this$_scrollAction = this._scrollAction) || void 0 === _this$_scrollAction || _this$_scrollAction.call(this)\r\n            }\r\n        }))))()\r\n    }\r\n    _createActionsHandler(actions) {\r\n        this._scrollAction = actions.scroll;\r\n        this._bounceAction = actions.bounce\r\n    }\r\n    _showScrollbar() {\r\n        this._scrollbar.option(\"visible\", true)\r\n    }\r\n    _hideScrollbar() {\r\n        this._scrollbar.option(\"visible\", false)\r\n    }\r\n    _containerSize() {\r\n        return this._getRealDimension(this._$container.get(0), this._dimension)\r\n    }\r\n    _contentSize() {\r\n        const isOverflowHidden = \"hidden\" === this._$content.css(`overflow${this._axis.toUpperCase()}`);\r\n        let contentSize = this._getRealDimension(this._$content.get(0), this._dimension);\r\n        if (!isOverflowHidden) {\r\n            const containerScrollSize = this._$content[0][`scroll${titleize(this._dimension)}`] * this._getScaleRatio();\r\n            contentSize = Math.max(containerScrollSize, contentSize)\r\n        }\r\n        return contentSize\r\n    }\r\n    _validateEvent(e) {\r\n        const $target = $(e.originalEvent.target);\r\n        return this._isThumb($target) || this._isScrollbar($target)\r\n    }\r\n    _isThumb($element) {\r\n        return this._scrollByThumb && this._scrollbar.isThumb($element)\r\n    }\r\n    _isScrollbar($element) {\r\n        return this._scrollByThumb && (null === $element || void 0 === $element ? void 0 : $element.is(this._$scrollbar))\r\n    }\r\n    _reachedMin() {\r\n        return Math.round(this._location - this._minOffset) <= 0\r\n    }\r\n    _reachedMax() {\r\n        return Math.round(this._location - this._maxOffset) >= 0\r\n    }\r\n    _cursorEnterHandler() {\r\n        this._resetScaleRatio();\r\n        this._updateScrollbar();\r\n        this._scrollbar.cursorEnter()\r\n    }\r\n    _cursorLeaveHandler() {\r\n        this._scrollbar.cursorLeave()\r\n    }\r\n    dispose() {}\r\n}\r\nlet hoveredScrollable;\r\nlet activeScrollable;\r\nexport class SimulatedStrategy extends(Class.inherit({})) {\r\n    ctor(scrollable) {\r\n        this._init(scrollable)\r\n    }\r\n    _init(scrollable) {\r\n        this._component = scrollable;\r\n        this._$element = scrollable.$element();\r\n        this._$container = $(scrollable.container());\r\n        this._$wrapper = scrollable._$wrapper;\r\n        this._$content = scrollable.$content();\r\n        this.option = scrollable.option.bind(scrollable);\r\n        this._createActionByOption = scrollable._createActionByOption.bind(scrollable);\r\n        this._isLocked = scrollable._isLocked.bind(scrollable);\r\n        this._isDirection = scrollable._isDirection.bind(scrollable);\r\n        this._allowedDirection = scrollable._allowedDirection.bind(scrollable);\r\n        this._getMaxOffset = scrollable._getMaxOffset.bind(scrollable)\r\n    }\r\n    render() {\r\n        this._$element.addClass(\"dx-scrollable-simulated\");\r\n        this._createScrollers();\r\n        if (this.option(\"useKeyboard\")) {\r\n            this._$container.prop(\"tabIndex\", 0)\r\n        }\r\n        this._attachKeyboardHandler();\r\n        this._attachCursorHandlers()\r\n    }\r\n    _createScrollers() {\r\n        this._scrollers = {};\r\n        if (this._isDirection(HORIZONTAL)) {\r\n            this._createScroller(HORIZONTAL)\r\n        }\r\n        if (this._isDirection(VERTICAL)) {\r\n            this._createScroller(VERTICAL)\r\n        }\r\n        this._$element.toggleClass(SCROLLABLE_SCROLLBARS_ALWAYSVISIBLE, \"always\" === this.option(\"showScrollbar\"))\r\n    }\r\n    _createScroller(direction) {\r\n        this._scrollers[direction] = new Scroller(this._scrollerOptions(direction))\r\n    }\r\n    _scrollerOptions(direction) {\r\n        return {\r\n            direction: direction,\r\n            $content: this._$content,\r\n            $container: this._$container,\r\n            $wrapper: this._$wrapper,\r\n            $element: this._$element,\r\n            scrollByThumb: this.option(\"scrollByThumb\"),\r\n            scrollbarVisible: this.option(\"showScrollbar\"),\r\n            bounceEnabled: this.option(\"bounceEnabled\"),\r\n            inertiaEnabled: this.option(\"inertiaEnabled\"),\r\n            isAnyThumbScrolling: this._isAnyThumbScrolling.bind(this)\r\n        }\r\n    }\r\n    _applyScaleRatio(targetLocation) {\r\n        for (const direction in this._scrollers) {\r\n            const prop = this._getPropByDirection(direction);\r\n            if (isDefined(targetLocation[prop])) {\r\n                const scroller = this._scrollers[direction];\r\n                targetLocation[prop] *= scroller._getScaleRatio()\r\n            }\r\n        }\r\n        return targetLocation\r\n    }\r\n    _isAnyThumbScrolling($target) {\r\n        let result = false;\r\n        this._eventHandler(\"isThumbScrolling\", $target).done(((isThumbScrollingVertical, isThumbScrollingHorizontal) => {\r\n            result = isThumbScrollingVertical || isThumbScrollingHorizontal\r\n        }));\r\n        return result\r\n    }\r\n    handleInit(e) {\r\n        this._suppressDirections(e);\r\n        this._eventForUserAction = e;\r\n        this._eventHandler(\"init\", e)\r\n    }\r\n    _suppressDirections(e) {\r\n        if (isDxMouseWheelEvent(e.originalEvent)) {\r\n            this._prepareDirections(true);\r\n            return\r\n        }\r\n        this._prepareDirections();\r\n        this._eachScroller((function(scroller, direction) {\r\n            const $target = $(e.originalEvent.target);\r\n            const isValid = scroller._validateEvent(e) || this.option(\"scrollByContent\") && this._isContent($target);\r\n            this._validDirections[direction] = isValid\r\n        }))\r\n    }\r\n    _isContent($element) {\r\n        return !!$element.closest(this._$element).length\r\n    }\r\n    _prepareDirections(value) {\r\n        value = value || false;\r\n        this._validDirections = {};\r\n        this._validDirections[HORIZONTAL] = value;\r\n        this._validDirections[VERTICAL] = value\r\n    }\r\n    _eachScroller(callback) {\r\n        callback = callback.bind(this);\r\n        each(this._scrollers, ((direction, scroller) => {\r\n            callback(scroller, direction)\r\n        }))\r\n    }\r\n    handleStart(e) {\r\n        this._eventForUserAction = e;\r\n        this._eventHandler(\"start\").done(this._startAction)\r\n    }\r\n    _saveActive() {\r\n        activeScrollable = this\r\n    }\r\n    _resetActive() {\r\n        if (activeScrollable === this) {\r\n            activeScrollable = null\r\n        }\r\n    }\r\n    handleMove(e) {\r\n        var _e$preventDefault;\r\n        if (this._isLocked()) {\r\n            e.cancel = true;\r\n            this._resetActive();\r\n            return\r\n        }\r\n        this._saveActive();\r\n        null === (_e$preventDefault = e.preventDefault) || void 0 === _e$preventDefault || _e$preventDefault.call(e);\r\n        this._adjustDistance(e, e.delta);\r\n        this._eventForUserAction = e;\r\n        this._eventHandler(\"move\", e.delta)\r\n    }\r\n    _adjustDistance(e, distance) {\r\n        distance.x *= this._validDirections[HORIZONTAL];\r\n        distance.y *= this._validDirections[VERTICAL];\r\n        const devicePixelRatio = this._tryGetDevicePixelRatio();\r\n        if (devicePixelRatio && isDxMouseWheelEvent(e.originalEvent)) {\r\n            distance.x = Math.round(distance.x / devicePixelRatio * 100) / 100;\r\n            distance.y = Math.round(distance.y / devicePixelRatio * 100) / 100\r\n        }\r\n    }\r\n    _tryGetDevicePixelRatio() {\r\n        if (hasWindow()) {\r\n            return getWindow().devicePixelRatio\r\n        }\r\n    }\r\n    handleEnd(e) {\r\n        var _e$originalEvent;\r\n        this._resetActive();\r\n        this._refreshCursorState(null === (_e$originalEvent = e.originalEvent) || void 0 === _e$originalEvent ? void 0 : _e$originalEvent.target);\r\n        this._adjustDistance(e, e.velocity);\r\n        this._eventForUserAction = e;\r\n        return this._eventHandler(\"end\", e.velocity).done(this._endAction)\r\n    }\r\n    handleCancel(e) {\r\n        this._resetActive();\r\n        this._eventForUserAction = e;\r\n        return this._eventHandler(\"end\", {\r\n            x: 0,\r\n            y: 0\r\n        })\r\n    }\r\n    handleStop() {\r\n        this._resetActive();\r\n        this._eventHandler(\"stop\")\r\n    }\r\n    handleScroll() {\r\n        var _this$_scrollAction2;\r\n        this._updateRtlConfig();\r\n        null === (_this$_scrollAction2 = this._scrollAction) || void 0 === _this$_scrollAction2 || _this$_scrollAction2.call(this)\r\n    }\r\n    _attachKeyboardHandler() {\r\n        eventsEngine.off(this._$element, `.${SCROLLABLE_SIMULATED_KEYBOARD}`);\r\n        if (!this.option(\"disabled\") && this.option(\"useKeyboard\")) {\r\n            eventsEngine.on(this._$element, addEventNamespace(\"keydown\", SCROLLABLE_SIMULATED_KEYBOARD), this._keyDownHandler.bind(this))\r\n        }\r\n    }\r\n    _keyDownHandler(e) {\r\n        clearTimeout(this._updateHandlerTimeout);\r\n        this._updateHandlerTimeout = setTimeout((() => {\r\n            if (normalizeKeyName(e) === KEY_CODES.TAB) {\r\n                this._eachScroller((scroller => {\r\n                    scroller._updateHandler()\r\n                }))\r\n            }\r\n        }));\r\n        if (!this._$container.is(domAdapter.getActiveElement(this._$container.get(0)))) {\r\n            return\r\n        }\r\n        let handled = true;\r\n        switch (normalizeKeyName(e)) {\r\n            case KEY_CODES.DOWN:\r\n                this._scrollByLine({\r\n                    y: 1\r\n                });\r\n                break;\r\n            case KEY_CODES.UP:\r\n                this._scrollByLine({\r\n                    y: -1\r\n                });\r\n                break;\r\n            case KEY_CODES.RIGHT:\r\n                this._scrollByLine({\r\n                    x: 1\r\n                });\r\n                break;\r\n            case KEY_CODES.LEFT:\r\n                this._scrollByLine({\r\n                    x: -1\r\n                });\r\n                break;\r\n            case KEY_CODES.PAGE_DOWN:\r\n                this._scrollByPage(1);\r\n                break;\r\n            case KEY_CODES.PAGE_UP:\r\n                this._scrollByPage(-1);\r\n                break;\r\n            case KEY_CODES.HOME:\r\n                this._scrollToHome();\r\n                break;\r\n            case KEY_CODES.END:\r\n                this._scrollToEnd();\r\n                break;\r\n            default:\r\n                handled = false\r\n        }\r\n        if (handled) {\r\n            e.stopPropagation();\r\n            e.preventDefault()\r\n        }\r\n    }\r\n    _scrollByLine(lines) {\r\n        const devicePixelRatio = this._tryGetDevicePixelRatio();\r\n        let scrollOffset = 40;\r\n        if (devicePixelRatio) {\r\n            scrollOffset = Math.abs(scrollOffset / devicePixelRatio * 100) / 100\r\n        }\r\n        this.scrollBy({\r\n            top: (lines.y || 0) * -scrollOffset,\r\n            left: (lines.x || 0) * -scrollOffset\r\n        })\r\n    }\r\n    _scrollByPage(page) {\r\n        const prop = this._wheelProp();\r\n        const dimension = this._dimensionByProp(prop);\r\n        const distance = {};\r\n        const getter = \"width\" === dimension ? getWidth : getHeight;\r\n        distance[prop] = page * -getter(this._$container);\r\n        this.scrollBy(distance)\r\n    }\r\n    _dimensionByProp(prop) {\r\n        return \"left\" === prop ? \"width\" : \"height\"\r\n    }\r\n    _getPropByDirection(direction) {\r\n        return direction === HORIZONTAL ? \"left\" : \"top\"\r\n    }\r\n    _scrollToHome() {\r\n        const prop = this._wheelProp();\r\n        const distance = {};\r\n        distance[prop] = 0;\r\n        this._component.scrollTo(distance)\r\n    }\r\n    _scrollToEnd() {\r\n        const prop = this._wheelProp();\r\n        const dimension = this._dimensionByProp(prop);\r\n        const distance = {};\r\n        const getter = \"width\" === dimension ? getWidth : getHeight;\r\n        distance[prop] = getter(this._$content) - getter(this._$container);\r\n        this._component.scrollTo(distance)\r\n    }\r\n    createActions() {\r\n        this._startAction = this._createActionHandler(\"onStart\");\r\n        this._endAction = this._createActionHandler(\"onEnd\");\r\n        this._updateAction = this._createActionHandler(\"onUpdated\");\r\n        this._createScrollerActions()\r\n    }\r\n    _createScrollerActions() {\r\n        this._scrollAction = this._createActionHandler(\"onScroll\");\r\n        this._bounceAction = this._createActionHandler(\"onBounce\");\r\n        this._eventHandler(\"createActions\", {\r\n            scroll: this._scrollAction,\r\n            bounce: this._bounceAction\r\n        })\r\n    }\r\n    _createActionHandler(optionName) {\r\n        const actionHandler = this._createActionByOption(optionName);\r\n        return () => {\r\n            actionHandler(extend(this._createActionArgs(), arguments))\r\n        }\r\n    }\r\n    _createActionArgs() {\r\n        const {\r\n            horizontal: scrollerX,\r\n            vertical: scrollerY\r\n        } = this._scrollers;\r\n        const offset = this._getScrollOffset();\r\n        this._scrollOffset = {\r\n            top: scrollerY && offset.top,\r\n            left: scrollerX && offset.left\r\n        };\r\n        return {\r\n            event: this._eventForUserAction,\r\n            scrollOffset: this._scrollOffset,\r\n            reachedLeft: null === scrollerX || void 0 === scrollerX ? void 0 : scrollerX._reachedMax(),\r\n            reachedRight: null === scrollerX || void 0 === scrollerX ? void 0 : scrollerX._reachedMin(),\r\n            reachedTop: null === scrollerY || void 0 === scrollerY ? void 0 : scrollerY._reachedMax(),\r\n            reachedBottom: null === scrollerY || void 0 === scrollerY ? void 0 : scrollerY._reachedMin()\r\n        }\r\n    }\r\n    _getScrollOffset() {\r\n        return {\r\n            top: -this.location().top,\r\n            left: -this.location().left\r\n        }\r\n    }\r\n    _eventHandler(eventName, location) {\r\n        const args = [].slice.call(arguments).slice(1);\r\n        const deferreds = map(this._scrollers, (scroller => scroller[`_${eventName}Handler`].apply(scroller, args)));\r\n        return when.apply($, deferreds).promise()\r\n    }\r\n    location() {\r\n        const location = locate(this._$content);\r\n        location.top -= this._$container.scrollTop();\r\n        location.left -= this._$container.scrollLeft();\r\n        return location\r\n    }\r\n    disabledChanged() {\r\n        this._attachCursorHandlers()\r\n    }\r\n    _attachCursorHandlers() {\r\n        eventsEngine.off(this._$element, `.${SCROLLABLE_SIMULATED_CURSOR}`);\r\n        if (!this.option(\"disabled\") && this._isHoverMode()) {\r\n            eventsEngine.on(this._$element, addEventNamespace(\"mouseenter\", SCROLLABLE_SIMULATED_CURSOR), this._cursorEnterHandler.bind(this));\r\n            eventsEngine.on(this._$element, addEventNamespace(\"mouseleave\", SCROLLABLE_SIMULATED_CURSOR), this._cursorLeaveHandler.bind(this))\r\n        }\r\n    }\r\n    _isHoverMode() {\r\n        return \"onHover\" === this.option(\"showScrollbar\")\r\n    }\r\n    _cursorEnterHandler(e) {\r\n        e = e || {};\r\n        e.originalEvent = e.originalEvent || {};\r\n        if (activeScrollable || e.originalEvent._hoverHandled) {\r\n            return\r\n        }\r\n        if (hoveredScrollable) {\r\n            hoveredScrollable._cursorLeaveHandler()\r\n        }\r\n        hoveredScrollable = this;\r\n        this._eventHandler(\"cursorEnter\");\r\n        e.originalEvent._hoverHandled = true\r\n    }\r\n    _cursorLeaveHandler(e) {\r\n        if (hoveredScrollable !== this || activeScrollable === hoveredScrollable) {\r\n            return\r\n        }\r\n        this._eventHandler(\"cursorLeave\");\r\n        hoveredScrollable = null;\r\n        this._refreshCursorState(null === e || void 0 === e ? void 0 : e.relatedTarget)\r\n    }\r\n    _refreshCursorState(target) {\r\n        if (!this._isHoverMode() && (!target || activeScrollable)) {\r\n            return\r\n        }\r\n        const $target = $(target);\r\n        const $scrollable = $target.closest(\".dx-scrollable-simulated:not(.dx-state-disabled)\");\r\n        const targetScrollable = $scrollable.length && $scrollable.data(SCROLLABLE_STRATEGY);\r\n        if (hoveredScrollable && hoveredScrollable !== targetScrollable) {\r\n            hoveredScrollable._cursorLeaveHandler()\r\n        }\r\n        if (targetScrollable) {\r\n            targetScrollable._cursorEnterHandler()\r\n        }\r\n    }\r\n    update() {\r\n        const result = this._eventHandler(\"update\").done(this._updateAction);\r\n        return when(result, deferUpdate((() => {\r\n            const allowedDirections = this._allowedDirections();\r\n            deferRender((() => {\r\n                let touchDirection = allowedDirections.vertical ? \"pan-x\" : \"\";\r\n                touchDirection = allowedDirections.horizontal ? \"pan-y\" : touchDirection;\r\n                touchDirection = allowedDirections.vertical && allowedDirections.horizontal ? \"none\" : touchDirection;\r\n                this._$container.css(\"touchAction\", touchDirection)\r\n            }));\r\n            return when().promise()\r\n        })))\r\n    }\r\n    _allowedDirections() {\r\n        const bounceEnabled = this.option(\"bounceEnabled\");\r\n        const verticalScroller = this._scrollers[VERTICAL];\r\n        const horizontalScroller = this._scrollers[HORIZONTAL];\r\n        return {\r\n            vertical: verticalScroller && (verticalScroller._minOffset < 0 || bounceEnabled),\r\n            horizontal: horizontalScroller && (horizontalScroller._minOffset < 0 || bounceEnabled)\r\n        }\r\n    }\r\n    _updateBounds() {\r\n        var _this$_scrollers$HORI;\r\n        null === (_this$_scrollers$HORI = this._scrollers[HORIZONTAL]) || void 0 === _this$_scrollers$HORI || _this$_scrollers$HORI._updateBounds()\r\n    }\r\n    _isHorizontalAndRtlEnabled() {\r\n        return this.option(\"rtlEnabled\") && this.option(\"direction\") !== VERTICAL\r\n    }\r\n    updateRtlPosition(needInitializeRtlConfig) {\r\n        if (needInitializeRtlConfig) {\r\n            this._rtlConfig = {\r\n                scrollRight: 0,\r\n                clientWidth: this._$container.get(0).clientWidth,\r\n                windowPixelRatio: this._getWindowDevicePixelRatio()\r\n            }\r\n        }\r\n        this._updateBounds();\r\n        if (this._isHorizontalAndRtlEnabled()) {\r\n            let scrollLeft = this._getMaxOffset().left - this._rtlConfig.scrollRight;\r\n            if (scrollLeft <= 0) {\r\n                scrollLeft = 0;\r\n                this._rtlConfig.scrollRight = this._getMaxOffset().left\r\n            }\r\n            if (this._getScrollOffset().left !== scrollLeft) {\r\n                this._rtlConfig.skipUpdating = true;\r\n                this._component.scrollTo({\r\n                    left: scrollLeft\r\n                });\r\n                this._rtlConfig.skipUpdating = false\r\n            }\r\n        }\r\n    }\r\n    _updateRtlConfig() {\r\n        if (this._isHorizontalAndRtlEnabled() && !this._rtlConfig.skipUpdating) {\r\n            const {\r\n                clientWidth: clientWidth,\r\n                scrollLeft: scrollLeft\r\n            } = this._$container.get(0);\r\n            const windowPixelRatio = this._getWindowDevicePixelRatio();\r\n            if (this._rtlConfig.windowPixelRatio === windowPixelRatio && this._rtlConfig.clientWidth === clientWidth) {\r\n                this._rtlConfig.scrollRight = this._getMaxOffset().left - scrollLeft\r\n            }\r\n            this._rtlConfig.clientWidth = clientWidth;\r\n            this._rtlConfig.windowPixelRatio = windowPixelRatio\r\n        }\r\n    }\r\n    _getWindowDevicePixelRatio() {\r\n        return hasWindow() ? getWindow().devicePixelRatio : 1\r\n    }\r\n    scrollBy(distance) {\r\n        var _this$_startAction, _this$_endAction;\r\n        const verticalScroller = this._scrollers[VERTICAL];\r\n        const horizontalScroller = this._scrollers[HORIZONTAL];\r\n        if (verticalScroller) {\r\n            distance.top = verticalScroller._boundLocation(distance.top + verticalScroller._location) - verticalScroller._location\r\n        }\r\n        if (horizontalScroller) {\r\n            distance.left = horizontalScroller._boundLocation(distance.left + horizontalScroller._location) - horizontalScroller._location\r\n        }\r\n        this._prepareDirections(true);\r\n        null === (_this$_startAction = this._startAction) || void 0 === _this$_startAction || _this$_startAction.call(this);\r\n        this._eventHandler(\"scrollBy\", {\r\n            x: distance.left,\r\n            y: distance.top\r\n        });\r\n        null === (_this$_endAction = this._endAction) || void 0 === _this$_endAction || _this$_endAction.call(this);\r\n        this._updateRtlConfig()\r\n    }\r\n    validate(e) {\r\n        if (isDxMouseWheelEvent(e) && isCommandKeyPressed(e)) {\r\n            return false\r\n        }\r\n        if (this.option(\"disabled\")) {\r\n            return false\r\n        }\r\n        if (this.option(\"bounceEnabled\")) {\r\n            return true\r\n        }\r\n        return isDxMouseWheelEvent(e) ? this._validateWheel(e) : this._validateMove(e)\r\n    }\r\n    _validateWheel(e) {\r\n        const scroller = this._scrollers[this._wheelDirection(e)];\r\n        const reachedMin = scroller._reachedMin();\r\n        const reachedMax = scroller._reachedMax();\r\n        const contentGreaterThanContainer = !reachedMin || !reachedMax;\r\n        const locatedNotAtBound = !reachedMin && !reachedMax;\r\n        const scrollFromMin = reachedMin && e.delta > 0;\r\n        const scrollFromMax = reachedMax && e.delta < 0;\r\n        let validated = contentGreaterThanContainer && (locatedNotAtBound || scrollFromMin || scrollFromMax);\r\n        validated = validated || void 0 !== this._validateWheelTimer;\r\n        if (validated) {\r\n            clearTimeout(this._validateWheelTimer);\r\n            this._validateWheelTimer = setTimeout((() => {\r\n                this._validateWheelTimer = void 0\r\n            }), 500)\r\n        }\r\n        return validated\r\n    }\r\n    _validateMove(e) {\r\n        if (!this.option(\"scrollByContent\") && !$(e.target).closest(\".dx-scrollable-scrollbar\").length) {\r\n            return false\r\n        }\r\n        return this._allowedDirection()\r\n    }\r\n    getDirection(e) {\r\n        return isDxMouseWheelEvent(e) ? this._wheelDirection(e) : this._allowedDirection()\r\n    }\r\n    _wheelProp() {\r\n        return this._wheelDirection() === HORIZONTAL ? \"left\" : \"top\"\r\n    }\r\n    _wheelDirection(e) {\r\n        switch (this.option(\"direction\")) {\r\n            case HORIZONTAL:\r\n                return HORIZONTAL;\r\n            case VERTICAL:\r\n                return VERTICAL;\r\n            default:\r\n                return null !== e && void 0 !== e && e.shiftKey ? HORIZONTAL : VERTICAL\r\n        }\r\n    }\r\n    dispose() {\r\n        this._resetActive();\r\n        if (hoveredScrollable === this) {\r\n            hoveredScrollable = null\r\n        }\r\n        this._eventHandler(\"dispose\");\r\n        this._detachEventHandlers();\r\n        this._$element.removeClass(\"dx-scrollable-simulated\");\r\n        this._eventForUserAction = null;\r\n        clearTimeout(this._validateWheelTimer);\r\n        clearTimeout(this._updateHandlerTimeout)\r\n    }\r\n    _detachEventHandlers() {\r\n        eventsEngine.off(this._$element, `.${SCROLLABLE_SIMULATED_CURSOR}`);\r\n        eventsEngine.off(this._$container, `.${SCROLLABLE_SIMULATED_KEYBOARD}`)\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAKA;AAAA;AACA;AAAA;AAMA;AACA;AACA;AACA;AAAA;AAMA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AAIA;AACA;;;;;;;;;;;;;;;;;;AACA,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB;AAC5B,MAAM,8BAA8B,GAAG,qBAAqB,MAAM,CAAC;AACnE,MAAM,gCAAgC,GAAG,qBAAqB,QAAQ,CAAC;AACvE,MAAM,6BAA6B;AACnC,MAAM,sCAAsC;AAC5C,MAAM,6BAA6B;AACnC,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,eAAe;AACrB,MAAM,0BAA0B;AAChC,MAAM,qBAAqB;AAC3B,MAAM,iBAAiB,KAAK,KAAK,CAAC,MAAM;AACxC,MAAM,qBAAqB;AAC3B,MAAM,yBAAyB;AAC/B,MAAM,4BAA4B;AAClC,MAAM,kBAAkB;AACxB,MAAM,gBAAgB,MAAM;AAC5B,MAAM,0BAA0B,CAAC,IAAI,OAAO,aAAa,IAAI,CAAC,IAAI,GAAG;AACrE,MAAM,YAAY;IACd,SAAS;IACT,WAAW;IACX,KAAK;IACL,MAAM;IACN,MAAM;IACN,IAAI;IACJ,OAAO;IACP,MAAM;IACN,KAAK;AACT;AACA,MAAM,wBAAwB,qLAAA,CAAA,UAAQ;IAClC,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,KAAK,QAAQ,EAAE;QACX,KAAK,CAAC;QACN,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,cAAc;QACV,OAAO,KAAK,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC,cAAc;IACnE;IACA,QAAQ;QACJ,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS;QACjD,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa;IACjD;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,KAAK,MAAM;IAC7C;IACA,YAAY;QACR,IAAI,CAAC,QAAQ,CAAC,eAAe;IACjC;AACJ;AACA,MAAM,uBAAuB;IACzB,aAAc;QACV,KAAK,IAAI;QACT,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,cAAc;QACV,OAAO,IAAI,CAAC,QAAQ,CAAC,qBAAqB,MAAM,KAAK,CAAC;IAC1D;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,YAAY;QACR,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe;QACjD,KAAK,CAAC;IACV;AACJ;AACO,MAAM,iBAAiB,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC;IACzC,KAAK,OAAO,EAAE;QACV,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,cAAc;IACvB;IACA,aAAa,OAAO,EAAE;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,KAAK,aAAa,MAAM;QACtD,IAAI,CAAC,KAAK,GAAG,QAAQ,SAAS,KAAK,aAAa,SAAS;QACzD,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS,KAAK,aAAa,UAAU;QAC/D,IAAI,CAAC,WAAW,GAAG,QAAQ,SAAS,KAAK,aAAa,eAAe;QACrE,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,SAAU,CAAC,YAAY;YACxB,IAAI,CAAC,CAAC,CAAC,EAAE,YAAY,CAAC,GAAG;QAC7B;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,gBAAgB,GAAG,IAAI,gBAAgB,IAAI;QAChD,IAAI,CAAC,eAAe,GAAG,IAAI,eAAe,IAAI;IAClD;IACA,iBAAiB;QACb,IAAI,CAAC,UAAU,GAAG,IAAI,sLAAA,CAAA,UAAS,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,WAAW,GAAG;YACnE,WAAW,IAAI,CAAC,UAAU;YAC1B,SAAS,IAAI,CAAC,cAAc;YAC5B,gBAAgB,IAAI,CAAC,wBAAwB,CAAC,IAAI,CAAC,iBAAiB;YACpE,YAAY,IAAI,CAAC,cAAc;QACnC;QACA,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,UAAU,CAAC,QAAQ;IAC/C;IACA,yBAAyB,IAAI,EAAE;QAC3B,OAAO,SAAS,OAAO,aAAa,UAAU,OAAO,UAAU;IACnE;IACA,YAAY,KAAK,EAAE;QACf,MAAM,eAAe,IAAI,CAAC,SAAS;QACnC,IAAI,CAAC,SAAS,IAAI;QAClB,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,KAAK;QACV,IAAI,KAAK,GAAG,CAAC,eAAe,IAAI,CAAC,SAAS,IAAI,GAAG;YAC7C;QACJ;QACA,uLAAA,CAAA,UAAY,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,EAAE;YAC1C,MAAM;QACV;IACJ;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,SAAS,GAAG;YACvD;QACJ;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,cAAc;IACxC;IACA,eAAe,QAAQ,EAAE;QACrB,WAAW,KAAK,MAAM,WAAW,WAAW,IAAI,CAAC,SAAS;QAC1D,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU;IACxE;IACA,MAAM,QAAQ,EAAE;QACZ,IAAI,CAAC,SAAS,GAAG,KAAK,MAAM,WAAW,WAAW,IAAI,CAAC,cAAc,KAAK,IAAI,CAAC,SAAS;QACxF,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,cAAc;IACvB;IACA,eAAe;QACX,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,WAAW,IAAI,CAAC,cAAc;QAClE,IAAI,CAAC,wBAAwB,CAAC;IAClC;IACA,iBAAiB;QACb,IAAI,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,OAAO,CAAC,IAAI,CAAC,WAAW,EAAE;YAClC,MAAM,UAAU,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACnC,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,SAAS,IAAI,CAAC,UAAU;YACrE,MAAM,gBAAgB,IAAI,CAAC,iBAAiB,CAAC,SAAS,IAAI,CAAC,UAAU;YACrE,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,gBAAgB,gBAAgB,OAAO;QACzE;QACA,OAAO,IAAI,CAAC,WAAW,IAAI;IAC/B;IACA,kBAAkB,OAAO,EAAE,SAAS,EAAE;QAClC,OAAO,KAAK,KAAK,CAAC,CAAA,GAAA,iLAAA,CAAA,kBAAe,AAAD,EAAE,QAAQ,CAAC,UAAU;IACzD;IACA,kBAAkB,OAAO,EAAE,SAAS,EAAE;QAClC,MAAM,gBAAgB,CAAC,MAAM,EAAE,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;QACpD,OAAO,OAAO,CAAC,cAAc;IACjC;IACA,yBAAyB,QAAQ,EAAE;QAC/B,IAAI;QACJ,MAAM,YAAY,CAAC,IAAI,CAAC,mBAAmB;QAC3C,IAAI,WAAW,GAAG;YACd,kBAAkB;QACtB;QACA,IAAI,YAAY,WAAW;YACvB,kBAAkB,WAAW;QACjC;QACA,IAAI,IAAI,CAAC,gBAAgB,KAAK,iBAAiB;YAC3C;QACJ;QACA,MAAM,iBAAiB,CAAC;QACxB,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG;QAC7B,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,iBAAiB;YAClB,CAAA,GAAA,8KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,SAAS;YAC5B;QACJ;QACA,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE;IACzB;IACA,iBAAiB;QACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS;IACzC;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,IAAI,CAAC,cAAc;YACnB,IAAI,IAAI,CAAC,iBAAiB,EAAE;gBACxB,IAAI,CAAC,iBAAiB,CAAC,OAAO;YAClC;QACJ;QACA,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB;QACJ;QACA,SAAS,CAAC,sBAAsB,IAAI,CAAC,aAAa,KAAK,KAAK,MAAM,uBAAuB,oBAAoB,IAAI,CAAC,IAAI;QACtH,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,eAAe,CAAC,KAAK;IAC9B;IACA,eAAe;QACX,MAAM,gBAAgB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,cAAc;QAChE,MAAM,iBAAiB,gBAAgB,IAAI,CAAC,SAAS;QACrD,IAAI,CAAC,SAAS,GAAG,iBAAiB;IACtC;IACA,UAAU,QAAQ,EAAE;QAChB,WAAW,KAAK,MAAM,WAAW,WAAW,IAAI,CAAC,SAAS;QAC1D,OAAO,IAAI,CAAC,cAAc,CAAC,cAAc;IAC7C;IACA,wBAAwB;QACpB,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,eAAe,WAAW,IAAI,CAAC,SAAS;QAC9C,OAAO,WAAW,IAAI,CAAC,UAAU,IAAI,gBAAgB,IAAI,CAAC,UAAU,IAAI,WAAW,IAAI,CAAC,UAAU,IAAI,gBAAgB,IAAI,CAAC,UAAU;IACzI;IACA,aAAa,CAAC,EAAE;QACZ,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,sBAAsB,CAAC;IAChC;IACA,iBAAiB;QACb,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAG;YACX,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC1B,IAAI,CAAC,eAAe,CAAC,IAAI;QAC7B;IACJ;IACA,uBAAuB,CAAC,EAAE;QACtB,IAAI,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,aAAa,GAAG;YACtC;QACJ;QACA,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa,CAAC,MAAM;QACxC,MAAM,mBAAmB,IAAI,CAAC,YAAY,CAAC;QAC3C,IAAI,kBAAkB;YAClB,IAAI,CAAC,oBAAoB,CAAC;QAC9B;QACA,IAAI,CAAC,eAAe,GAAG,oBAAoB,IAAI,CAAC,QAAQ,CAAC;QACzD,IAAI,CAAC,oBAAoB,GAAG,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,oBAAoB,CAAC;QAC/E,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,UAAU,CAAC,UAAU;QAC9B;IACJ;IACA,yBAAyB,OAAO,EAAE;QAC9B,OAAO,IAAI,CAAC,QAAQ,CAAC;IACzB;IACA,qBAAqB,CAAC,EAAE;QACpB,MAAM,gBAAgB,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,KAAK,CAAC;QAChG,MAAM,WAAW,IAAI,CAAC,SAAS,GAAG,gBAAgB,IAAI,CAAC,wBAAwB,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,WAAW,IAAI;QAClH,IAAI,CAAC,WAAW,CAAC,CAAC,KAAK,KAAK,CAAC;IACjC;IACA,gBAAgB;QACZ,IAAI,CAAC,cAAc;IACvB;IACA,aAAa,KAAK,EAAE;QAChB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC3B;QACJ;QACA,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,wBAAwB;QACrF;QACA,IAAI,CAAC,SAAS,CAAC;IACnB;IACA,UAAU,KAAK,EAAE;QACb,QAAQ,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC;QACzB,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI;YACnB,SAAS;QACb;QACA,IAAI,CAAC,WAAW,CAAC;IACrB;IACA,iBAAiB,KAAK,EAAE;QACpB,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;YACtB;QACJ;QACA,IAAI,CAAC,SAAS,CAAC;QACf,IAAI,CAAC,eAAe;IACxB;IACA,2BAA2B;QACvB,OAAO,IAAI,CAAC,UAAU,CAAC,uBAAuB;IAClD;IACA,YAAY,QAAQ,EAAE;QAClB,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QAChC,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,oBAAoB;QACzB,OAAO,IAAI,CAAC,iBAAiB,CAAC,OAAO;IACzC;IACA,kBAAkB;QACd,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,gBAAgB,CAAC,KAAK;IAC/B;IACA,mBAAmB;QACf,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/C,IAAI,CAAC,SAAS,GAAG;QACrB;IACJ;IACA,uBAAuB;QACnB,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,CAAC,oBAAoB,GAAG;IAChC;IACA,eAAe;QACX,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,IAAI,CAAC,eAAe;QACxB;QACA,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,eAAe;IACxB;IACA,kBAAkB;QACd,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,WAAW,CAAC,MAAM;IAC3B;IACA,iBAAiB;QACb,IAAI,CAAC,OAAO;QACZ,IAAI,CAAC,aAAa;IACtB;IACA,UAAU;QACN,IAAI,CAAC,cAAc;QACnB,OAAO,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAG;YAChB,IAAI,CAAC,gBAAgB;YACrB,IAAI,CAAC,eAAe;YACpB,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,gBAAgB;YACrB,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAG;gBACT,IAAI,CAAC,cAAc;gBACnB,IAAI,CAAC,UAAU,CAAC,MAAM;YAC1B;QACJ;IACJ;IACA,mBAAmB;QACf,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,kBAAkB;QACd,IAAI,CAAC,SAAS,GAAG,CAAC,CAAA,GAAA,8KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,IAAI,IAAI,CAAC,cAAc;IACtH;IACA,gBAAgB;QACZ,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa;QACpC,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa;IACxC;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,gBAAgB;QACZ,IAAI,CAAC,mBAAmB,GAAG,KAAK,GAAG,CAAC,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,cAAc,IAAI;QACjF,OAAO,CAAC,IAAI,CAAC,mBAAmB;IACpC;IACA,mBAAmB;QACf,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAG;YACV,MAAM,gBAAgB,IAAI,CAAC,cAAc;YACzC,MAAM,cAAc,IAAI,CAAC,YAAY;YACrC,MAAM,oBAAoB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU;YACzF,MAAM,kBAAkB,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU;YACrF,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAG;gBACT,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;oBACnB,eAAe;oBACf,aAAa;oBACb,mBAAmB;oBACnB,iBAAiB;oBACjB,YAAY,IAAI,CAAC,cAAc;gBACnC;YACJ;QACJ;IACJ;IACA,gBAAgB;QACZ,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAG;YACtC,MAAM,WAAW,IAAI,CAAC,cAAc;YACpC,MAAM,kBAAkB,aAAa,IAAI,CAAC,SAAS;YACnD,IAAI,CAAC,SAAS,GAAG;YACjB,IAAI,CAAC,KAAK;YACV,IAAI,iBAAiB;gBACjB,IAAI;gBACJ,SAAS,CAAC,sBAAsB,IAAI,CAAC,aAAa,KAAK,KAAK,MAAM,uBAAuB,oBAAoB,IAAI,CAAC,IAAI;YAC1H;QACJ;IACJ;IACA,sBAAsB,OAAO,EAAE;QAC3B,IAAI,CAAC,aAAa,GAAG,QAAQ,MAAM;QACnC,IAAI,CAAC,aAAa,GAAG,QAAQ,MAAM;IACvC;IACA,iBAAiB;QACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW;IACtC;IACA,iBAAiB;QACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,WAAW;IACtC;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU;IAC1E;IACA,eAAe;QACX,MAAM,mBAAmB,aAAa,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,KAAK,CAAC,WAAW,IAAI;QAC9F,IAAI,cAAc,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,UAAU;QAC/E,IAAI,CAAC,kBAAkB;YACnB,MAAM,sBAAsB,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,MAAM,EAAE,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,UAAU,GAAG,CAAC,GAAG,IAAI,CAAC,cAAc;YACzG,cAAc,KAAK,GAAG,CAAC,qBAAqB;QAChD;QACA,OAAO;IACX;IACA,eAAe,CAAC,EAAE;QACd,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa,CAAC,MAAM;QACxC,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,IAAI,CAAC,YAAY,CAAC;IACvD;IACA,SAAS,QAAQ,EAAE;QACf,OAAO,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC;IAC1D;IACA,aAAa,QAAQ,EAAE;QACnB,OAAO,IAAI,CAAC,cAAc,IAAI,CAAC,SAAS,YAAY,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,EAAE,CAAC,IAAI,CAAC,WAAW,CAAC;IACpH;IACA,cAAc;QACV,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,KAAK;IAC3D;IACA,cAAc;QACV,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,UAAU,KAAK;IAC3D;IACA,sBAAsB;QAClB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,UAAU,CAAC,WAAW;IAC/B;IACA,sBAAsB;QAClB,IAAI,CAAC,UAAU,CAAC,WAAW;IAC/B;IACA,UAAU,CAAC;AACf;AACA,IAAI;AACJ,IAAI;AACG,MAAM,0BAA0B,kJAAA,CAAA,UAAK,CAAC,OAAO,CAAC,CAAC;IAClD,KAAK,UAAU,EAAE;QACb,IAAI,CAAC,KAAK,CAAC;IACf;IACA,MAAM,UAAU,EAAE;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG,WAAW,QAAQ;QACpC,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,SAAS;QACzC,IAAI,CAAC,SAAS,GAAG,WAAW,SAAS;QACrC,IAAI,CAAC,SAAS,GAAG,WAAW,QAAQ;QACpC,IAAI,CAAC,MAAM,GAAG,WAAW,MAAM,CAAC,IAAI,CAAC;QACrC,IAAI,CAAC,qBAAqB,GAAG,WAAW,qBAAqB,CAAC,IAAI,CAAC;QACnE,IAAI,CAAC,SAAS,GAAG,WAAW,SAAS,CAAC,IAAI,CAAC;QAC3C,IAAI,CAAC,YAAY,GAAG,WAAW,YAAY,CAAC,IAAI,CAAC;QACjD,IAAI,CAAC,iBAAiB,GAAG,WAAW,iBAAiB,CAAC,IAAI,CAAC;QAC3D,IAAI,CAAC,aAAa,GAAG,WAAW,aAAa,CAAC,IAAI,CAAC;IACvD;IACA,SAAS;QACL,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACxB,IAAI,CAAC,gBAAgB;QACrB,IAAI,IAAI,CAAC,MAAM,CAAC,gBAAgB;YAC5B,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY;QACtC;QACA,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,qBAAqB;IAC9B;IACA,mBAAmB;QACf,IAAI,CAAC,UAAU,GAAG,CAAC;QACnB,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa;YAC/B,IAAI,CAAC,eAAe,CAAC;QACzB;QACA,IAAI,IAAI,CAAC,YAAY,CAAC,WAAW;YAC7B,IAAI,CAAC,eAAe,CAAC;QACzB;QACA,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC,qCAAqC,aAAa,IAAI,CAAC,MAAM,CAAC;IAC7F;IACA,gBAAgB,SAAS,EAAE;QACvB,IAAI,CAAC,UAAU,CAAC,UAAU,GAAG,IAAI,SAAS,IAAI,CAAC,gBAAgB,CAAC;IACpE;IACA,iBAAiB,SAAS,EAAE;QACxB,OAAO;YACH,WAAW;YACX,UAAU,IAAI,CAAC,SAAS;YACxB,YAAY,IAAI,CAAC,WAAW;YAC5B,UAAU,IAAI,CAAC,SAAS;YACxB,UAAU,IAAI,CAAC,SAAS;YACxB,eAAe,IAAI,CAAC,MAAM,CAAC;YAC3B,kBAAkB,IAAI,CAAC,MAAM,CAAC;YAC9B,eAAe,IAAI,CAAC,MAAM,CAAC;YAC3B,gBAAgB,IAAI,CAAC,MAAM,CAAC;YAC5B,qBAAqB,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI;QAC5D;IACJ;IACA,iBAAiB,cAAc,EAAE;QAC7B,IAAK,MAAM,aAAa,IAAI,CAAC,UAAU,CAAE;YACrC,MAAM,OAAO,IAAI,CAAC,mBAAmB,CAAC;YACtC,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,cAAc,CAAC,KAAK,GAAG;gBACjC,MAAM,WAAW,IAAI,CAAC,UAAU,CAAC,UAAU;gBAC3C,cAAc,CAAC,KAAK,IAAI,SAAS,cAAc;YACnD;QACJ;QACA,OAAO;IACX;IACA,qBAAqB,OAAO,EAAE;QAC1B,IAAI,SAAS;QACb,IAAI,CAAC,aAAa,CAAC,oBAAoB,SAAS,IAAI,CAAE,CAAC,0BAA0B;YAC7E,SAAS,4BAA4B;QACzC;QACA,OAAO;IACX;IACA,WAAW,CAAC,EAAE;QACV,IAAI,CAAC,mBAAmB,CAAC;QACzB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ;IAC/B;IACA,oBAAoB,CAAC,EAAE;QACnB,IAAI,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,aAAa,GAAG;YACtC,IAAI,CAAC,kBAAkB,CAAC;YACxB;QACJ;QACA,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,aAAa,CAAE,SAAS,QAAQ,EAAE,SAAS;YAC5C,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa,CAAC,MAAM;YACxC,MAAM,UAAU,SAAS,cAAc,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,sBAAsB,IAAI,CAAC,UAAU,CAAC;YAChG,IAAI,CAAC,gBAAgB,CAAC,UAAU,GAAG;QACvC;IACJ;IACA,WAAW,QAAQ,EAAE;QACjB,OAAO,CAAC,CAAC,SAAS,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,MAAM;IACpD;IACA,mBAAmB,KAAK,EAAE;QACtB,QAAQ,SAAS;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;QACzB,IAAI,CAAC,gBAAgB,CAAC,WAAW,GAAG;QACpC,IAAI,CAAC,gBAAgB,CAAC,SAAS,GAAG;IACtC;IACA,cAAc,QAAQ,EAAE;QACpB,WAAW,SAAS,IAAI,CAAC,IAAI;QAC7B,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,UAAU,EAAG,CAAC,WAAW;YAC/B,SAAS,UAAU;QACvB;IACJ;IACA,YAAY,CAAC,EAAE;QACX,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,aAAa,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,YAAY;IACtD;IACA,cAAc;QACV,mBAAmB,IAAI;IAC3B;IACA,eAAe;QACX,IAAI,qBAAqB,IAAI,EAAE;YAC3B,mBAAmB;QACvB;IACJ;IACA,WAAW,CAAC,EAAE;QACV,IAAI;QACJ,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,EAAE,MAAM,GAAG;YACX,IAAI,CAAC,YAAY;YACjB;QACJ;QACA,IAAI,CAAC,WAAW;QAChB,SAAS,CAAC,oBAAoB,EAAE,cAAc,KAAK,KAAK,MAAM,qBAAqB,kBAAkB,IAAI,CAAC;QAC1G,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,KAAK;QAC/B,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,aAAa,CAAC,QAAQ,EAAE,KAAK;IACtC;IACA,gBAAgB,CAAC,EAAE,QAAQ,EAAE;QACzB,SAAS,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,WAAW;QAC/C,SAAS,CAAC,IAAI,IAAI,CAAC,gBAAgB,CAAC,SAAS;QAC7C,MAAM,mBAAmB,IAAI,CAAC,uBAAuB;QACrD,IAAI,oBAAoB,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,aAAa,GAAG;YAC1D,SAAS,CAAC,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,mBAAmB,OAAO;YAC/D,SAAS,CAAC,GAAG,KAAK,KAAK,CAAC,SAAS,CAAC,GAAG,mBAAmB,OAAO;QACnE;IACJ;IACA,0BAA0B;QACtB,IAAI,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YACb,OAAO,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,IAAI,gBAAgB;QACvC;IACJ;IACA,UAAU,CAAC,EAAE;QACT,IAAI;QACJ,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,mBAAmB,CAAC,SAAS,CAAC,mBAAmB,EAAE,aAAa,KAAK,KAAK,MAAM,mBAAmB,KAAK,IAAI,iBAAiB,MAAM;QACxI,IAAI,CAAC,eAAe,CAAC,GAAG,EAAE,QAAQ;QAClC,IAAI,CAAC,mBAAmB,GAAG;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;IACrE;IACA,aAAa,CAAC,EAAE;QACZ,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,mBAAmB,GAAG;QAC3B,OAAO,IAAI,CAAC,aAAa,CAAC,OAAO;YAC7B,GAAG;YACH,GAAG;QACP;IACJ;IACA,aAAa;QACT,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,aAAa,CAAC;IACvB;IACA,eAAe;QACX,IAAI;QACJ,IAAI,CAAC,gBAAgB;QACrB,SAAS,CAAC,uBAAuB,IAAI,CAAC,aAAa,KAAK,KAAK,MAAM,wBAAwB,qBAAqB,IAAI,CAAC,IAAI;IAC7H;IACA,yBAAyB;QACrB,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,+BAA+B;QACpE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,MAAM,CAAC,gBAAgB;YACxD,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAiB,AAAD,EAAE,WAAW,gCAAgC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;QAC/H;IACJ;IACA,gBAAgB,CAAC,EAAE;QACf,aAAa,IAAI,CAAC,qBAAqB;QACvC,IAAI,CAAC,qBAAqB,GAAG,WAAY;YACrC,IAAI,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,UAAU,GAAG,EAAE;gBACvC,IAAI,CAAC,aAAa,CAAE,CAAA;oBAChB,SAAS,cAAc;gBAC3B;YACJ;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,CAAC,wJAAA,CAAA,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,MAAM;YAC5E;QACJ;QACA,IAAI,UAAU;QACd,OAAQ,CAAA,GAAA,8KAAA,CAAA,mBAAgB,AAAD,EAAE;YACrB,KAAK,UAAU,IAAI;gBACf,IAAI,CAAC,aAAa,CAAC;oBACf,GAAG;gBACP;gBACA;YACJ,KAAK,UAAU,EAAE;gBACb,IAAI,CAAC,aAAa,CAAC;oBACf,GAAG,CAAC;gBACR;gBACA;YACJ,KAAK,UAAU,KAAK;gBAChB,IAAI,CAAC,aAAa,CAAC;oBACf,GAAG;gBACP;gBACA;YACJ,KAAK,UAAU,IAAI;gBACf,IAAI,CAAC,aAAa,CAAC;oBACf,GAAG,CAAC;gBACR;gBACA;YACJ,KAAK,UAAU,SAAS;gBACpB,IAAI,CAAC,aAAa,CAAC;gBACnB;YACJ,KAAK,UAAU,OAAO;gBAClB,IAAI,CAAC,aAAa,CAAC,CAAC;gBACpB;YACJ,KAAK,UAAU,IAAI;gBACf,IAAI,CAAC,aAAa;gBAClB;YACJ,KAAK,UAAU,GAAG;gBACd,IAAI,CAAC,YAAY;gBACjB;YACJ;gBACI,UAAU;QAClB;QACA,IAAI,SAAS;YACT,EAAE,eAAe;YACjB,EAAE,cAAc;QACpB;IACJ;IACA,cAAc,KAAK,EAAE;QACjB,MAAM,mBAAmB,IAAI,CAAC,uBAAuB;QACrD,IAAI,eAAe;QACnB,IAAI,kBAAkB;YAClB,eAAe,KAAK,GAAG,CAAC,eAAe,mBAAmB,OAAO;QACrE;QACA,IAAI,CAAC,QAAQ,CAAC;YACV,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;YACvB,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC;QAC5B;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,WAAW,CAAC;QAClB,MAAM,SAAS,YAAY,YAAY,6KAAA,CAAA,WAAQ,GAAG,6KAAA,CAAA,YAAS;QAC3D,QAAQ,CAAC,KAAK,GAAG,OAAO,CAAC,OAAO,IAAI,CAAC,WAAW;QAChD,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA,iBAAiB,IAAI,EAAE;QACnB,OAAO,WAAW,OAAO,UAAU;IACvC;IACA,oBAAoB,SAAS,EAAE;QAC3B,OAAO,cAAc,aAAa,SAAS;IAC/C;IACA,gBAAgB;QACZ,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,MAAM,WAAW,CAAC;QAClB,QAAQ,CAAC,KAAK,GAAG;QACjB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IAC7B;IACA,eAAe;QACX,MAAM,OAAO,IAAI,CAAC,UAAU;QAC5B,MAAM,YAAY,IAAI,CAAC,gBAAgB,CAAC;QACxC,MAAM,WAAW,CAAC;QAClB,MAAM,SAAS,YAAY,YAAY,6KAAA,CAAA,WAAQ,GAAG,6KAAA,CAAA,YAAS;QAC3D,QAAQ,CAAC,KAAK,GAAG,OAAO,IAAI,CAAC,SAAS,IAAI,OAAO,IAAI,CAAC,WAAW;QACjE,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;IAC7B;IACA,gBAAgB;QACZ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC9C,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC5C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC/C,IAAI,CAAC,sBAAsB;IAC/B;IACA,yBAAyB;QACrB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC/C,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC/C,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAChC,QAAQ,IAAI,CAAC,aAAa;YAC1B,QAAQ,IAAI,CAAC,aAAa;QAC9B;IACJ;IACA,qBAAqB,UAAU,EAAE;QAC7B,MAAM,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;QACjD,OAAO;YACH,cAAc,CAAA,GAAA,+KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,iBAAiB,IAAI;QACnD;IACJ;IACA,oBAAoB;QAChB,MAAM,EACF,YAAY,SAAS,EACrB,UAAU,SAAS,EACtB,GAAG,IAAI,CAAC,UAAU;QACnB,MAAM,SAAS,IAAI,CAAC,gBAAgB;QACpC,IAAI,CAAC,aAAa,GAAG;YACjB,KAAK,aAAa,OAAO,GAAG;YAC5B,MAAM,aAAa,OAAO,IAAI;QAClC;QACA,OAAO;YACH,OAAO,IAAI,CAAC,mBAAmB;YAC/B,cAAc,IAAI,CAAC,aAAa;YAChC,aAAa,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,WAAW;YACxF,cAAc,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,WAAW;YACzF,YAAY,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,WAAW;YACvF,eAAe,SAAS,aAAa,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,WAAW;QAC9F;IACJ;IACA,mBAAmB;QACf,OAAO;YACH,KAAK,CAAC,IAAI,CAAC,QAAQ,GAAG,GAAG;YACzB,MAAM,CAAC,IAAI,CAAC,QAAQ,GAAG,IAAI;QAC/B;IACJ;IACA,cAAc,SAAS,EAAE,QAAQ,EAAE;QAC/B,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,WAAW,KAAK,CAAC;QAC5C,MAAM,YAAY,CAAA,GAAA,iLAAA,CAAA,MAAG,AAAD,EAAE,IAAI,CAAC,UAAU,EAAG,CAAA,WAAY,QAAQ,CAAC,CAAC,CAAC,EAAE,UAAU,OAAO,CAAC,CAAC,CAAC,KAAK,CAAC,UAAU;QACrG,OAAO,iLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,qJAAA,CAAA,UAAC,EAAE,WAAW,OAAO;IAC3C;IACA,WAAW;QACP,MAAM,WAAW,CAAA,GAAA,8KAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,SAAS;QACtC,SAAS,GAAG,IAAI,IAAI,CAAC,WAAW,CAAC,SAAS;QAC1C,SAAS,IAAI,IAAI,IAAI,CAAC,WAAW,CAAC,UAAU;QAC5C,OAAO;IACX;IACA,kBAAkB;QACd,IAAI,CAAC,qBAAqB;IAC9B;IACA,wBAAwB;QACpB,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,6BAA6B;QAClE,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,CAAC,YAAY,IAAI;YACjD,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAiB,AAAD,EAAE,cAAc,8BAA8B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;YAChI,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAiB,AAAD,EAAE,cAAc,8BAA8B,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;QACpI;IACJ;IACA,eAAe;QACX,OAAO,cAAc,IAAI,CAAC,MAAM,CAAC;IACrC;IACA,oBAAoB,CAAC,EAAE;QACnB,IAAI,KAAK,CAAC;QACV,EAAE,aAAa,GAAG,EAAE,aAAa,IAAI,CAAC;QACtC,IAAI,oBAAoB,EAAE,aAAa,CAAC,aAAa,EAAE;YACnD;QACJ;QACA,IAAI,mBAAmB;YACnB,kBAAkB,mBAAmB;QACzC;QACA,oBAAoB,IAAI;QACxB,IAAI,CAAC,aAAa,CAAC;QACnB,EAAE,aAAa,CAAC,aAAa,GAAG;IACpC;IACA,oBAAoB,CAAC,EAAE;QACnB,IAAI,sBAAsB,IAAI,IAAI,qBAAqB,mBAAmB;YACtE;QACJ;QACA,IAAI,CAAC,aAAa,CAAC;QACnB,oBAAoB;QACpB,IAAI,CAAC,mBAAmB,CAAC,SAAS,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,aAAa;IAClF;IACA,oBAAoB,MAAM,EAAE;QACxB,IAAI,CAAC,IAAI,CAAC,YAAY,MAAM,CAAC,CAAC,UAAU,gBAAgB,GAAG;YACvD;QACJ;QACA,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QAClB,MAAM,cAAc,QAAQ,OAAO,CAAC;QACpC,MAAM,mBAAmB,YAAY,MAAM,IAAI,YAAY,IAAI,CAAC;QAChE,IAAI,qBAAqB,sBAAsB,kBAAkB;YAC7D,kBAAkB,mBAAmB;QACzC;QACA,IAAI,kBAAkB;YAClB,iBAAiB,mBAAmB;QACxC;IACJ;IACA,SAAS;QACL,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,aAAa;QACnE,OAAO,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAG;YAC7B,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;YACjD,CAAA,GAAA,+KAAA,CAAA,cAAW,AAAD,EAAG;gBACT,IAAI,iBAAiB,kBAAkB,QAAQ,GAAG,UAAU;gBAC5D,iBAAiB,kBAAkB,UAAU,GAAG,UAAU;gBAC1D,iBAAiB,kBAAkB,QAAQ,IAAI,kBAAkB,UAAU,GAAG,SAAS;gBACvF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,eAAe;YACxC;YACA,OAAO,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,IAAI,OAAO;QACzB;IACJ;IACA,qBAAqB;QACjB,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;QAClC,MAAM,mBAAmB,IAAI,CAAC,UAAU,CAAC,SAAS;QAClD,MAAM,qBAAqB,IAAI,CAAC,UAAU,CAAC,WAAW;QACtD,OAAO;YACH,UAAU,oBAAoB,CAAC,iBAAiB,UAAU,GAAG,KAAK,aAAa;YAC/E,YAAY,sBAAsB,CAAC,mBAAmB,UAAU,GAAG,KAAK,aAAa;QACzF;IACJ;IACA,gBAAgB;QACZ,IAAI;QACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,aAAa;IAC7I;IACA,6BAA6B;QACzB,OAAO,IAAI,CAAC,MAAM,CAAC,iBAAiB,IAAI,CAAC,MAAM,CAAC,iBAAiB;IACrE;IACA,kBAAkB,uBAAuB,EAAE;QACvC,IAAI,yBAAyB;YACzB,IAAI,CAAC,UAAU,GAAG;gBACd,aAAa;gBACb,aAAa,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,WAAW;gBAChD,kBAAkB,IAAI,CAAC,0BAA0B;YACrD;QACJ;QACA,IAAI,CAAC,aAAa;QAClB,IAAI,IAAI,CAAC,0BAA0B,IAAI;YACnC,IAAI,aAAa,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW;YACxE,IAAI,cAAc,GAAG;gBACjB,aAAa;gBACb,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI;YAC3D;YACA,IAAI,IAAI,CAAC,gBAAgB,GAAG,IAAI,KAAK,YAAY;gBAC7C,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG;gBAC/B,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;oBACrB,MAAM;gBACV;gBACA,IAAI,CAAC,UAAU,CAAC,YAAY,GAAG;YACnC;QACJ;IACJ;IACA,mBAAmB;QACf,IAAI,IAAI,CAAC,0BAA0B,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE;YACpE,MAAM,EACF,aAAa,WAAW,EACxB,YAAY,UAAU,EACzB,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YACzB,MAAM,mBAAmB,IAAI,CAAC,0BAA0B;YACxD,IAAI,IAAI,CAAC,UAAU,CAAC,gBAAgB,KAAK,oBAAoB,IAAI,CAAC,UAAU,CAAC,WAAW,KAAK,aAAa;gBACtG,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,GAAG;YAC9D;YACA,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG;YAC9B,IAAI,CAAC,UAAU,CAAC,gBAAgB,GAAG;QACvC;IACJ;IACA,6BAA6B;QACzB,OAAO,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,MAAM,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,IAAI,gBAAgB,GAAG;IACxD;IACA,SAAS,QAAQ,EAAE;QACf,IAAI,oBAAoB;QACxB,MAAM,mBAAmB,IAAI,CAAC,UAAU,CAAC,SAAS;QAClD,MAAM,qBAAqB,IAAI,CAAC,UAAU,CAAC,WAAW;QACtD,IAAI,kBAAkB;YAClB,SAAS,GAAG,GAAG,iBAAiB,cAAc,CAAC,SAAS,GAAG,GAAG,iBAAiB,SAAS,IAAI,iBAAiB,SAAS;QAC1H;QACA,IAAI,oBAAoB;YACpB,SAAS,IAAI,GAAG,mBAAmB,cAAc,CAAC,SAAS,IAAI,GAAG,mBAAmB,SAAS,IAAI,mBAAmB,SAAS;QAClI;QACA,IAAI,CAAC,kBAAkB,CAAC;QACxB,SAAS,CAAC,qBAAqB,IAAI,CAAC,YAAY,KAAK,KAAK,MAAM,sBAAsB,mBAAmB,IAAI,CAAC,IAAI;QAClH,IAAI,CAAC,aAAa,CAAC,YAAY;YAC3B,GAAG,SAAS,IAAI;YAChB,GAAG,SAAS,GAAG;QACnB;QACA,SAAS,CAAC,mBAAmB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,IAAI,CAAC,IAAI;QAC1G,IAAI,CAAC,gBAAgB;IACzB;IACA,SAAS,CAAC,EAAE;QACR,IAAI,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI;YAClD,OAAO;QACX;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa;YACzB,OAAO;QACX;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB;YAC9B,OAAO;QACX;QACA,OAAO,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAI,CAAC,cAAc,CAAC,KAAK,IAAI,CAAC,aAAa,CAAC;IAChF;IACA,eAAe,CAAC,EAAE;QACd,MAAM,WAAW,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG;QACzD,MAAM,aAAa,SAAS,WAAW;QACvC,MAAM,aAAa,SAAS,WAAW;QACvC,MAAM,8BAA8B,CAAC,cAAc,CAAC;QACpD,MAAM,oBAAoB,CAAC,cAAc,CAAC;QAC1C,MAAM,gBAAgB,cAAc,EAAE,KAAK,GAAG;QAC9C,MAAM,gBAAgB,cAAc,EAAE,KAAK,GAAG;QAC9C,IAAI,YAAY,+BAA+B,CAAC,qBAAqB,iBAAiB,aAAa;QACnG,YAAY,aAAa,KAAK,MAAM,IAAI,CAAC,mBAAmB;QAC5D,IAAI,WAAW;YACX,aAAa,IAAI,CAAC,mBAAmB;YACrC,IAAI,CAAC,mBAAmB,GAAG,WAAY;gBACnC,IAAI,CAAC,mBAAmB,GAAG,KAAK;YACpC,GAAI;QACR;QACA,OAAO;IACX;IACA,cAAc,CAAC,EAAE;QACb,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,sBAAsB,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM,EAAE,OAAO,CAAC,4BAA4B,MAAM,EAAE;YAC5F,OAAO;QACX;QACA,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,aAAa,CAAC,EAAE;QACZ,OAAO,CAAA,GAAA,8KAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,IAAI,CAAC,eAAe,CAAC,KAAK,IAAI,CAAC,iBAAiB;IACpF;IACA,aAAa;QACT,OAAO,IAAI,CAAC,eAAe,OAAO,aAAa,SAAS;IAC5D;IACA,gBAAgB,CAAC,EAAE;QACf,OAAQ,IAAI,CAAC,MAAM,CAAC;YAChB,KAAK;gBACD,OAAO;YACX,KAAK;gBACD,OAAO;YACX;gBACI,OAAO,SAAS,KAAK,KAAK,MAAM,KAAK,EAAE,QAAQ,GAAG,aAAa;QACvE;IACJ;IACA,UAAU;QACN,IAAI,CAAC,YAAY;QACjB,IAAI,sBAAsB,IAAI,EAAE;YAC5B,oBAAoB;QACxB;QACA,IAAI,CAAC,aAAa,CAAC;QACnB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,SAAS,CAAC,WAAW,CAAC;QAC3B,IAAI,CAAC,mBAAmB,GAAG;QAC3B,aAAa,IAAI,CAAC,mBAAmB;QACrC,aAAa,IAAI,CAAC,qBAAqB;IAC3C;IACA,uBAAuB;QACnB,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,6BAA6B;QAClE,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,+BAA+B;IAC1E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1804, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scrollable.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scrollable.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport eventsEngine from \"../../../common/core/events/core/events_engine\";\r\nimport scrollEvents from \"../../../common/core/events/gesture/emitter.gesture.scroll\";\r\nimport {\r\n    addNamespace\r\n} from \"../../../common/core/events/utils/index\";\r\nimport registerComponent from \"../../../core/component_registrator\";\r\nimport devices from \"../../../core/devices\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../core/element\";\r\nimport $ from \"../../../core/renderer\";\r\nimport browser from \"../../../core/utils/browser\";\r\nimport {\r\n    ensureDefined,\r\n    noop\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    when\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    getHeight,\r\n    getOuterHeight,\r\n    getOuterWidth,\r\n    getWidth\r\n} from \"../../../core/utils/size\";\r\nimport {\r\n    isDefined,\r\n    isPlainObject\r\n} from \"../../../core/utils/type\";\r\nimport {\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport DOMComponent from \"../../core/widget/dom_component\";\r\nimport {\r\n    getElementLocationInternal\r\n} from \"../../ui/scroll_view/utils/get_element_location_internal\";\r\nimport supportUtils from \"../../core/utils/m_support\";\r\nimport {\r\n    deviceDependentOptions\r\n} from \"./m_scrollable.device\";\r\nimport NativeStrategy from \"./m_scrollable.native\";\r\nimport {\r\n    SimulatedStrategy\r\n} from \"./m_scrollable.simulated\";\r\nconst SCROLLABLE = \"dxScrollable\";\r\nconst SCROLLABLE_STRATEGY = \"dxScrollableStrategy\";\r\nconst SCROLLABLE_CLASS = \"dx-scrollable\";\r\nconst SCROLLABLE_DISABLED_CLASS = \"dx-scrollable-disabled\";\r\nconst SCROLLABLE_CONTAINER_CLASS = \"dx-scrollable-container\";\r\nconst SCROLLABLE_WRAPPER_CLASS = \"dx-scrollable-wrapper\";\r\nconst SCROLLABLE_CONTENT_CLASS = \"dx-scrollable-content\";\r\nconst VERTICAL = \"vertical\";\r\nconst HORIZONTAL = \"horizontal\";\r\nconst BOTH = \"both\";\r\nclass Scrollable extends DOMComponent {\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            disabled: false,\r\n            onScroll: null,\r\n            direction: VERTICAL,\r\n            showScrollbar: \"onScroll\",\r\n            useNative: true,\r\n            bounceEnabled: true,\r\n            scrollByContent: true,\r\n            scrollByThumb: false,\r\n            onUpdated: null,\r\n            onStart: null,\r\n            onEnd: null,\r\n            onBounce: null,\r\n            useSimulatedScrollbar: false,\r\n            useKeyboard: true,\r\n            inertiaEnabled: true,\r\n            updateManually: false,\r\n            _onVisibilityChanged: noop\r\n        })\r\n    }\r\n    _defaultOptionsRules() {\r\n        return super._defaultOptionsRules().concat(deviceDependentOptions(), [{\r\n            device: () => supportUtils.nativeScrolling && \"android\" === devices.real().platform && !browser.mozilla,\r\n            options: {\r\n                useSimulatedScrollbar: true\r\n            }\r\n        }])\r\n    }\r\n    _initOptions(options) {\r\n        super._initOptions(options);\r\n        if (!(\"useSimulatedScrollbar\" in options)) {\r\n            this._setUseSimulatedScrollbar()\r\n        }\r\n    }\r\n    _setUseSimulatedScrollbar() {\r\n        if (!this.initialOption(\"useSimulatedScrollbar\")) {\r\n            this.option(\"useSimulatedScrollbar\", !this.option(\"useNative\"))\r\n        }\r\n    }\r\n    _init() {\r\n        super._init();\r\n        this._initScrollableMarkup();\r\n        this._locked = false\r\n    }\r\n    _visibilityChanged(visible) {\r\n        if (visible) {\r\n            this.update();\r\n            this._updateRtlPosition();\r\n            this._savedScrollOffset && this.scrollTo(this._savedScrollOffset);\r\n            delete this._savedScrollOffset;\r\n            const {\r\n                _onVisibilityChanged: onVisibilityChanged\r\n            } = this.option();\r\n            null === onVisibilityChanged || void 0 === onVisibilityChanged || onVisibilityChanged(this)\r\n        } else {\r\n            this._savedScrollOffset = this.scrollOffset()\r\n        }\r\n    }\r\n    _initScrollableMarkup() {\r\n        const $element = this.$element().addClass(\"dx-scrollable\");\r\n        const $container = $(\"<div>\").addClass(\"dx-scrollable-container\");\r\n        const $wrapper = $(\"<div>\").addClass(\"dx-scrollable-wrapper\");\r\n        const $content = $(\"<div>\").addClass(\"dx-scrollable-content\");\r\n        this._$container = $container;\r\n        this._$wrapper = $wrapper;\r\n        this._$content = $content;\r\n        $content.append($element.contents()).appendTo($container);\r\n        $container.appendTo($wrapper);\r\n        $wrapper.appendTo($element)\r\n    }\r\n    _dimensionChanged() {\r\n        this.update();\r\n        this._updateRtlPosition()\r\n    }\r\n    _initMarkup() {\r\n        super._initMarkup();\r\n        this._renderDirection()\r\n    }\r\n    _render() {\r\n        this._renderStrategy();\r\n        this._attachEventHandlers();\r\n        this._renderDisabledState();\r\n        this._createActions();\r\n        this.update();\r\n        super._render();\r\n        this._updateRtlPosition(true)\r\n    }\r\n    _updateRtlPosition(needInitializeRtlConfig) {\r\n        this._strategy.updateRtlPosition(needInitializeRtlConfig)\r\n    }\r\n    _getMaxOffset() {\r\n        const {\r\n            scrollWidth: scrollWidth,\r\n            clientWidth: clientWidth,\r\n            scrollHeight: scrollHeight,\r\n            clientHeight: clientHeight\r\n        } = $(this.container()).get(0);\r\n        return {\r\n            left: scrollWidth - clientWidth,\r\n            top: scrollHeight - clientHeight\r\n        }\r\n    }\r\n    _attachEventHandlers() {\r\n        const strategy = this._strategy;\r\n        const initEventData = {\r\n            getDirection: strategy.getDirection.bind(strategy),\r\n            validate: this._validate.bind(this),\r\n            isNative: this.option(\"useNative\"),\r\n            scrollTarget: this._$container\r\n        };\r\n        eventsEngine.off(this._$wrapper, `.${SCROLLABLE}`);\r\n        eventsEngine.on(this._$wrapper, addNamespace(scrollEvents.init, SCROLLABLE), initEventData, this._initHandler.bind(this));\r\n        eventsEngine.on(this._$wrapper, addNamespace(scrollEvents.start, SCROLLABLE), strategy.handleStart.bind(strategy));\r\n        eventsEngine.on(this._$wrapper, addNamespace(scrollEvents.move, SCROLLABLE), strategy.handleMove.bind(strategy));\r\n        eventsEngine.on(this._$wrapper, addNamespace(scrollEvents.end, SCROLLABLE), strategy.handleEnd.bind(strategy));\r\n        eventsEngine.on(this._$wrapper, addNamespace(scrollEvents.cancel, SCROLLABLE), strategy.handleCancel.bind(strategy));\r\n        eventsEngine.on(this._$wrapper, addNamespace(scrollEvents.stop, SCROLLABLE), strategy.handleStop.bind(strategy));\r\n        eventsEngine.off(this._$container, `.${SCROLLABLE}`);\r\n        eventsEngine.on(this._$container, addNamespace(\"scroll\", SCROLLABLE), strategy.handleScroll.bind(strategy))\r\n    }\r\n    _validate(e) {\r\n        if (this._isLocked()) {\r\n            return false\r\n        }\r\n        this._updateIfNeed();\r\n        return this._moveIsAllowed(e)\r\n    }\r\n    _moveIsAllowed(e) {\r\n        return this._strategy.validate(e)\r\n    }\r\n    handleMove(e) {\r\n        this._strategy.handleMove(e)\r\n    }\r\n    _prepareDirections(value) {\r\n        this._strategy._prepareDirections(value)\r\n    }\r\n    _initHandler() {\r\n        const strategy = this._strategy;\r\n        strategy.handleInit.apply(strategy, arguments)\r\n    }\r\n    _renderDisabledState() {\r\n        const {\r\n            disabled: disabled\r\n        } = this.option();\r\n        this.$element().toggleClass(\"dx-scrollable-disabled\", disabled);\r\n        if (this.option(\"disabled\")) {\r\n            this._lock()\r\n        } else {\r\n            this._unlock()\r\n        }\r\n    }\r\n    _renderDirection() {\r\n        const {\r\n            direction: direction\r\n        } = this.option();\r\n        this.$element().removeClass(`dx-scrollable-${HORIZONTAL}`).removeClass(`dx-scrollable-${VERTICAL}`).removeClass(`dx-scrollable-${BOTH}`).addClass(`dx-scrollable-${direction}`)\r\n    }\r\n    _renderStrategy() {\r\n        this._createStrategy();\r\n        this._strategy.render();\r\n        this.$element().data(SCROLLABLE_STRATEGY, this._strategy)\r\n    }\r\n    _createStrategy() {\r\n        this._strategy = this.option(\"useNative\") ? new NativeStrategy(this) : new SimulatedStrategy(this)\r\n    }\r\n    _createActions() {\r\n        var _this$_strategy;\r\n        null === (_this$_strategy = this._strategy) || void 0 === _this$_strategy || _this$_strategy.createActions()\r\n    }\r\n    _clean() {\r\n        var _this$_strategy2;\r\n        null === (_this$_strategy2 = this._strategy) || void 0 === _this$_strategy2 || _this$_strategy2.dispose()\r\n    }\r\n    _optionChanged(args) {\r\n        var _this$_strategy3;\r\n        switch (args.name) {\r\n            case \"onStart\":\r\n            case \"onEnd\":\r\n            case \"onUpdated\":\r\n            case \"onScroll\":\r\n            case \"onBounce\":\r\n                this._createActions();\r\n                break;\r\n            case \"direction\":\r\n                this._resetInactiveDirection();\r\n                this._invalidate();\r\n                break;\r\n            case \"useNative\":\r\n                this._setUseSimulatedScrollbar();\r\n                this._invalidate();\r\n                break;\r\n            case \"inertiaEnabled\":\r\n            case \"scrollByThumb\":\r\n            case \"bounceEnabled\":\r\n            case \"useKeyboard\":\r\n            case \"showScrollbar\":\r\n            case \"useSimulatedScrollbar\":\r\n                this._invalidate();\r\n                break;\r\n            case \"disabled\":\r\n                this._renderDisabledState();\r\n                null === (_this$_strategy3 = this._strategy) || void 0 === _this$_strategy3 || _this$_strategy3.disabledChanged();\r\n                break;\r\n            case \"updateManually\":\r\n            case \"scrollByContent\":\r\n            case \"_onVisibilityChanged\":\r\n                break;\r\n            case \"width\":\r\n                super._optionChanged(args);\r\n                this._updateRtlPosition();\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    _resetInactiveDirection() {\r\n        const inactiveProp = this._getInactiveProp();\r\n        if (!inactiveProp || !hasWindow()) {\r\n            return\r\n        }\r\n        const scrollOffset = this.scrollOffset();\r\n        scrollOffset[inactiveProp] = 0;\r\n        this.scrollTo(scrollOffset)\r\n    }\r\n    _getInactiveProp() {\r\n        const {\r\n            direction: direction\r\n        } = this.option();\r\n        if (direction === VERTICAL) {\r\n            return \"left\"\r\n        }\r\n        if (direction === HORIZONTAL) {\r\n            return \"top\"\r\n        }\r\n    }\r\n    _location() {\r\n        return this._strategy.location()\r\n    }\r\n    _normalizeLocation(location) {\r\n        if (isPlainObject(location)) {\r\n            const left = ensureDefined(location.left, location.x);\r\n            const top = ensureDefined(location.top, location.y);\r\n            return {\r\n                left: isDefined(left) ? -left : void 0,\r\n                top: isDefined(top) ? -top : void 0\r\n            }\r\n        }\r\n        const {\r\n            direction: direction\r\n        } = this.option();\r\n        return {\r\n            left: direction !== VERTICAL ? -location : void 0,\r\n            top: direction !== HORIZONTAL ? -location : void 0\r\n        }\r\n    }\r\n    _isLocked() {\r\n        return this._locked\r\n    }\r\n    _lock() {\r\n        this._locked = true\r\n    }\r\n    _unlock() {\r\n        if (!this.option(\"disabled\")) {\r\n            this._locked = false\r\n        }\r\n    }\r\n    _isDirection(direction) {\r\n        const {\r\n            direction: current\r\n        } = this.option();\r\n        if (direction === VERTICAL) {\r\n            return current !== HORIZONTAL\r\n        }\r\n        if (direction === HORIZONTAL) {\r\n            return current !== VERTICAL\r\n        }\r\n        return current === direction\r\n    }\r\n    _updateAllowedDirection() {\r\n        const allowedDirections = this._strategy._allowedDirections();\r\n        if (this._isDirection(BOTH) && allowedDirections.vertical && allowedDirections.horizontal) {\r\n            this._allowedDirectionValue = BOTH\r\n        } else if (this._isDirection(HORIZONTAL) && allowedDirections.horizontal) {\r\n            this._allowedDirectionValue = HORIZONTAL\r\n        } else if (this._isDirection(VERTICAL) && allowedDirections.vertical) {\r\n            this._allowedDirectionValue = VERTICAL\r\n        } else {\r\n            this._allowedDirectionValue = null\r\n        }\r\n    }\r\n    _allowedDirection() {\r\n        return this._allowedDirectionValue\r\n    }\r\n    $content() {\r\n        return this._$content\r\n    }\r\n    content() {\r\n        return getPublicElement(this._$content)\r\n    }\r\n    container() {\r\n        return getPublicElement(this._$container)\r\n    }\r\n    scrollOffset() {\r\n        return this._strategy._getScrollOffset()\r\n    }\r\n    _isRtlNativeStrategy() {\r\n        const {\r\n            useNative: useNative,\r\n            rtlEnabled: rtlEnabled\r\n        } = this.option();\r\n        return useNative && rtlEnabled\r\n    }\r\n    scrollTop() {\r\n        return this.scrollOffset().top\r\n    }\r\n    scrollLeft() {\r\n        return this.scrollOffset().left\r\n    }\r\n    clientHeight() {\r\n        return getHeight(this._$container)\r\n    }\r\n    scrollHeight() {\r\n        return getOuterHeight(this.$content())\r\n    }\r\n    clientWidth() {\r\n        return getWidth(this._$container)\r\n    }\r\n    scrollWidth() {\r\n        return getOuterWidth(this.$content())\r\n    }\r\n    update() {\r\n        if (!this._strategy) {\r\n            return\r\n        }\r\n        return when(this._strategy.update()).done((() => {\r\n            this._updateAllowedDirection()\r\n        }))\r\n    }\r\n    scrollBy(distance) {\r\n        distance = this._normalizeLocation(distance);\r\n        if (!distance.top && !distance.left) {\r\n            return\r\n        }\r\n        this._updateIfNeed();\r\n        this._strategy.scrollBy(distance)\r\n    }\r\n    scrollTo(targetLocation) {\r\n        if (!hasWindow()) {\r\n            return\r\n        }\r\n        targetLocation = this._normalizeLocation(targetLocation);\r\n        this._updateIfNeed();\r\n        let location = this._location();\r\n        const {\r\n            useNative: useNative\r\n        } = this.option();\r\n        if (!useNative) {\r\n            const strategy = this._strategy;\r\n            targetLocation = strategy._applyScaleRatio(targetLocation);\r\n            location = strategy._applyScaleRatio(location)\r\n        }\r\n        if (this._isRtlNativeStrategy()) {\r\n            location.left -= this._getMaxOffset().left\r\n        }\r\n        const distance = this._normalizeLocation({\r\n            left: location.left - ensureDefined(targetLocation.left, location.left),\r\n            top: location.top - ensureDefined(targetLocation.top, location.top)\r\n        });\r\n        if (!distance.top && !distance.left) {\r\n            return\r\n        }\r\n        this._strategy.scrollBy(distance)\r\n    }\r\n    scrollToElement(element, offset) {\r\n        const $element = $(element);\r\n        const elementInsideContent = this.$content().find(element).length;\r\n        const elementIsInsideContent = $element.parents(\".dx-scrollable\").length - $element.parents(\".dx-scrollable-content\").length === 0;\r\n        if (!elementInsideContent || !elementIsInsideContent) {\r\n            return\r\n        }\r\n        const scrollPosition = {\r\n            top: 0,\r\n            left: 0\r\n        };\r\n        const {\r\n            direction: direction\r\n        } = this.option();\r\n        if (direction !== VERTICAL) {\r\n            scrollPosition.left = this.getScrollElementPosition($element, HORIZONTAL, offset)\r\n        }\r\n        if (direction !== HORIZONTAL) {\r\n            scrollPosition.top = this.getScrollElementPosition($element, VERTICAL, offset)\r\n        }\r\n        this.scrollTo(scrollPosition)\r\n    }\r\n    getScrollElementPosition($element, direction, offset) {\r\n        const scrollOffset = this.scrollOffset();\r\n        return getElementLocationInternal($element.get(0), direction, $(this.container()).get(0), scrollOffset, offset)\r\n    }\r\n    _updateIfNeed() {\r\n        if (!this.option(\"updateManually\")) {\r\n            this.update()\r\n        }\r\n    }\r\n    _useTemplates() {\r\n        return false\r\n    }\r\n    isRenovated() {\r\n        return !!Scrollable.IS_RENOVATED_WIDGET\r\n    }\r\n}\r\nregisterComponent(SCROLLABLE, Scrollable);\r\nexport default Scrollable;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAGA;AACA;AACA;AAAA;AAGA;AACA;AACA;AAAA;AAIA;AAAA;AAGA;AAAA;AAMA;AAAA;AAIA;AAAA;AAGA;AACA;AAGA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,aAAa;AACnB,MAAM,sBAAsB;AAC5B,MAAM,mBAAmB;AACzB,MAAM,4BAA4B;AAClC,MAAM,6BAA6B;AACnC,MAAM,2BAA2B;AACjC,MAAM,2BAA2B;AACjC,MAAM,WAAW;AACjB,MAAM,aAAa;AACnB,MAAM,OAAO;AACb,MAAM,mBAAmB,qLAAA,CAAA,UAAY;IACjC,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,UAAU;YACV,UAAU;YACV,WAAW;YACX,eAAe;YACf,WAAW;YACX,eAAe;YACf,iBAAiB;YACjB,eAAe;YACf,WAAW;YACX,SAAS;YACT,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,aAAa;YACb,gBAAgB;YAChB,gBAAgB;YAChB,sBAAsB,+KAAA,CAAA,OAAI;QAC9B;IACJ;IACA,uBAAuB;QACnB,OAAO,KAAK,CAAC,uBAAuB,MAAM,CAAC,CAAA,GAAA,iMAAA,CAAA,yBAAsB,AAAD,KAAK;YAAC;gBAClE,QAAQ,IAAM,gMAAA,CAAA,UAAY,CAAC,eAAe,IAAI,cAAc,oJAAA,CAAA,UAAO,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,6JAAA,CAAA,UAAO,CAAC,OAAO;gBACvG,SAAS;oBACL,uBAAuB;gBAC3B;YACJ;SAAE;IACN;IACA,aAAa,OAAO,EAAE;QAClB,KAAK,CAAC,aAAa;QACnB,IAAI,CAAC,CAAC,2BAA2B,OAAO,GAAG;YACvC,IAAI,CAAC,yBAAyB;QAClC;IACJ;IACA,4BAA4B;QACxB,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,0BAA0B;YAC9C,IAAI,CAAC,MAAM,CAAC,yBAAyB,CAAC,IAAI,CAAC,MAAM,CAAC;QACtD;IACJ;IACA,QAAQ;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,mBAAmB,OAAO,EAAE;QACxB,IAAI,SAAS;YACT,IAAI,CAAC,MAAM;YACX,IAAI,CAAC,kBAAkB;YACvB,IAAI,CAAC,kBAAkB,IAAI,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,kBAAkB;YAChE,OAAO,IAAI,CAAC,kBAAkB;YAC9B,MAAM,EACF,sBAAsB,mBAAmB,EAC5C,GAAG,IAAI,CAAC,MAAM;YACf,SAAS,uBAAuB,KAAK,MAAM,uBAAuB,oBAAoB,IAAI;QAC9F,OAAO;YACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,YAAY;QAC/C;IACJ;IACA,wBAAwB;QACpB,MAAM,WAAW,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC1C,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACvC,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACrC,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACrC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,SAAS,GAAG;QACjB,SAAS,MAAM,CAAC,SAAS,QAAQ,IAAI,QAAQ,CAAC;QAC9C,WAAW,QAAQ,CAAC;QACpB,SAAS,QAAQ,CAAC;IACtB;IACA,oBAAoB;QAChB,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,kBAAkB;IAC3B;IACA,cAAc;QACV,KAAK,CAAC;QACN,IAAI,CAAC,gBAAgB;IACzB;IACA,UAAU;QACN,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,MAAM;QACX,KAAK,CAAC;QACN,IAAI,CAAC,kBAAkB,CAAC;IAC5B;IACA,mBAAmB,uBAAuB,EAAE;QACxC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;IACrC;IACA,gBAAgB;QACZ,MAAM,EACF,aAAa,WAAW,EACxB,aAAa,WAAW,EACxB,cAAc,YAAY,EAC1B,cAAc,YAAY,EAC7B,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,SAAS,IAAI,GAAG,CAAC;QAC5B,OAAO;YACH,MAAM,cAAc;YACpB,KAAK,eAAe;QACxB;IACJ;IACA,uBAAuB;QACnB,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,gBAAgB;YAClB,cAAc,SAAS,YAAY,CAAC,IAAI,CAAC;YACzC,UAAU,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;YAClC,UAAU,IAAI,CAAC,MAAM,CAAC;YACtB,cAAc,IAAI,CAAC,WAAW;QAClC;QACA,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,EAAE,YAAY;QACjD,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yMAAA,CAAA,UAAY,CAAC,IAAI,EAAE,aAAa,eAAe,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QACvH,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yMAAA,CAAA,UAAY,CAAC,KAAK,EAAE,aAAa,SAAS,WAAW,CAAC,IAAI,CAAC;QACxG,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yMAAA,CAAA,UAAY,CAAC,IAAI,EAAE,aAAa,SAAS,UAAU,CAAC,IAAI,CAAC;QACtG,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yMAAA,CAAA,UAAY,CAAC,GAAG,EAAE,aAAa,SAAS,SAAS,CAAC,IAAI,CAAC;QACpG,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yMAAA,CAAA,UAAY,CAAC,MAAM,EAAE,aAAa,SAAS,YAAY,CAAC,IAAI,CAAC;QAC1G,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,yMAAA,CAAA,UAAY,CAAC,IAAI,EAAE,aAAa,SAAS,UAAU,CAAC,IAAI,CAAC;QACtG,uLAAA,CAAA,UAAY,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,YAAY;QACnD,uLAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,UAAU,aAAa,SAAS,YAAY,CAAC,IAAI,CAAC;IACrG;IACA,UAAU,CAAC,EAAE;QACT,IAAI,IAAI,CAAC,SAAS,IAAI;YAClB,OAAO;QACX;QACA,IAAI,CAAC,aAAa;QAClB,OAAO,IAAI,CAAC,cAAc,CAAC;IAC/B;IACA,eAAe,CAAC,EAAE;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IACnC;IACA,WAAW,CAAC,EAAE;QACV,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC;IAC9B;IACA,mBAAmB,KAAK,EAAE;QACtB,IAAI,CAAC,SAAS,CAAC,kBAAkB,CAAC;IACtC;IACA,eAAe;QACX,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,SAAS,UAAU,CAAC,KAAK,CAAC,UAAU;IACxC;IACA,uBAAuB;QACnB,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,0BAA0B;QACtD,IAAI,IAAI,CAAC,MAAM,CAAC,aAAa;YACzB,IAAI,CAAC,KAAK;QACd,OAAO;YACH,IAAI,CAAC,OAAO;QAChB;IACJ;IACA,mBAAmB;QACf,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,QAAQ,GAAG,WAAW,CAAC,CAAC,cAAc,EAAE,YAAY,EAAE,WAAW,CAAC,CAAC,cAAc,EAAE,UAAU,EAAE,WAAW,CAAC,CAAC,cAAc,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,cAAc,EAAE,WAAW;IAClL;IACA,kBAAkB;QACd,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,SAAS,CAAC,MAAM;QACrB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,qBAAqB,IAAI,CAAC,SAAS;IAC5D;IACA,kBAAkB;QACd,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,MAAM,CAAC,eAAe,IAAI,iMAAA,CAAA,UAAc,CAAC,IAAI,IAAI,IAAI,oMAAA,CAAA,oBAAiB,CAAC,IAAI;IACrG;IACA,iBAAiB;QACb,IAAI;QACJ,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,mBAAmB,gBAAgB,aAAa;IAC9G;IACA,SAAS;QACL,IAAI;QACJ,SAAS,CAAC,mBAAmB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,OAAO;IAC3G;IACA,eAAe,IAAI,EAAE;QACjB,IAAI;QACJ,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,cAAc;gBACnB;YACJ,KAAK;gBACD,IAAI,CAAC,uBAAuB;gBAC5B,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,yBAAyB;gBAC9B,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,oBAAoB;gBACzB,SAAS,CAAC,mBAAmB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,eAAe;gBAC/G;YACJ,KAAK;YACL,KAAK;YACL,KAAK;gBACD;YACJ,KAAK;gBACD,KAAK,CAAC,eAAe;gBACrB,IAAI,CAAC,kBAAkB;gBACvB;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,0BAA0B;QACtB,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,IAAI,CAAC,gBAAgB,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YAC/B;QACJ;QACA,MAAM,eAAe,IAAI,CAAC,YAAY;QACtC,YAAY,CAAC,aAAa,GAAG;QAC7B,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA,mBAAmB;QACf,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,cAAc,UAAU;YACxB,OAAO;QACX;QACA,IAAI,cAAc,YAAY;YAC1B,OAAO;QACX;IACJ;IACA,YAAY;QACR,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ;IAClC;IACA,mBAAmB,QAAQ,EAAE;QACzB,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;YACzB,MAAM,OAAO,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI,EAAE,SAAS,CAAC;YACpD,MAAM,MAAM,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC;YAClD,OAAO;gBACH,MAAM,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAC,OAAO,KAAK;gBACrC,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,MAAM,KAAK;YACtC;QACJ;QACA,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,MAAM;QACf,OAAO;YACH,MAAM,cAAc,WAAW,CAAC,WAAW,KAAK;YAChD,KAAK,cAAc,aAAa,CAAC,WAAW,KAAK;QACrD;IACJ;IACA,YAAY;QACR,OAAO,IAAI,CAAC,OAAO;IACvB;IACA,QAAQ;QACJ,IAAI,CAAC,OAAO,GAAG;IACnB;IACA,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,aAAa;YAC1B,IAAI,CAAC,OAAO,GAAG;QACnB;IACJ;IACA,aAAa,SAAS,EAAE;QACpB,MAAM,EACF,WAAW,OAAO,EACrB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,cAAc,UAAU;YACxB,OAAO,YAAY;QACvB;QACA,IAAI,cAAc,YAAY;YAC1B,OAAO,YAAY;QACvB;QACA,OAAO,YAAY;IACvB;IACA,0BAA0B;QACtB,MAAM,oBAAoB,IAAI,CAAC,SAAS,CAAC,kBAAkB;QAC3D,IAAI,IAAI,CAAC,YAAY,CAAC,SAAS,kBAAkB,QAAQ,IAAI,kBAAkB,UAAU,EAAE;YACvF,IAAI,CAAC,sBAAsB,GAAG;QAClC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,eAAe,kBAAkB,UAAU,EAAE;YACtE,IAAI,CAAC,sBAAsB,GAAG;QAClC,OAAO,IAAI,IAAI,CAAC,YAAY,CAAC,aAAa,kBAAkB,QAAQ,EAAE;YAClE,IAAI,CAAC,sBAAsB,GAAG;QAClC,OAAO;YACH,IAAI,CAAC,sBAAsB,GAAG;QAClC;IACJ;IACA,oBAAoB;QAChB,OAAO,IAAI,CAAC,sBAAsB;IACtC;IACA,WAAW;QACP,OAAO,IAAI,CAAC,SAAS;IACzB;IACA,UAAU;QACN,OAAO,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,SAAS;IAC1C;IACA,YAAY;QACR,OAAO,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,WAAW;IAC5C;IACA,eAAe;QACX,OAAO,IAAI,CAAC,SAAS,CAAC,gBAAgB;IAC1C;IACA,uBAAuB;QACnB,MAAM,EACF,WAAW,SAAS,EACpB,YAAY,UAAU,EACzB,GAAG,IAAI,CAAC,MAAM;QACf,OAAO,aAAa;IACxB;IACA,YAAY;QACR,OAAO,IAAI,CAAC,YAAY,GAAG,GAAG;IAClC;IACA,aAAa;QACT,OAAO,IAAI,CAAC,YAAY,GAAG,IAAI;IACnC;IACA,eAAe;QACX,OAAO,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,WAAW;IACrC;IACA,eAAe;QACX,OAAO,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,QAAQ;IACvC;IACA,cAAc;QACV,OAAO,CAAA,GAAA,6KAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,WAAW;IACpC;IACA,cAAc;QACV,OAAO,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,CAAC,QAAQ;IACtC;IACA,SAAS;QACL,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB;QACJ;QACA,OAAO,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,IAAI,IAAI,CAAE;YACvC,IAAI,CAAC,uBAAuB;QAChC;IACJ;IACA,SAAS,QAAQ,EAAE;QACf,WAAW,IAAI,CAAC,kBAAkB,CAAC;QACnC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE;YACjC;QACJ;QACA,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC5B;IACA,SAAS,cAAc,EAAE;QACrB,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,KAAK;YACd;QACJ;QACA,iBAAiB,IAAI,CAAC,kBAAkB,CAAC;QACzC,IAAI,CAAC,aAAa;QAClB,IAAI,WAAW,IAAI,CAAC,SAAS;QAC7B,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,WAAW;YACZ,MAAM,WAAW,IAAI,CAAC,SAAS;YAC/B,iBAAiB,SAAS,gBAAgB,CAAC;YAC3C,WAAW,SAAS,gBAAgB,CAAC;QACzC;QACA,IAAI,IAAI,CAAC,oBAAoB,IAAI;YAC7B,SAAS,IAAI,IAAI,IAAI,CAAC,aAAa,GAAG,IAAI;QAC9C;QACA,MAAM,WAAW,IAAI,CAAC,kBAAkB,CAAC;YACrC,MAAM,SAAS,IAAI,GAAG,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,IAAI,EAAE,SAAS,IAAI;YACtE,KAAK,SAAS,GAAG,GAAG,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,GAAG,EAAE,SAAS,GAAG;QACtE;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,SAAS,IAAI,EAAE;YACjC;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;IAC5B;IACA,gBAAgB,OAAO,EAAE,MAAM,EAAE;QAC7B,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE;QACnB,MAAM,uBAAuB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,SAAS,MAAM;QACjE,MAAM,yBAAyB,SAAS,OAAO,CAAC,kBAAkB,MAAM,GAAG,SAAS,OAAO,CAAC,0BAA0B,MAAM,KAAK;QACjI,IAAI,CAAC,wBAAwB,CAAC,wBAAwB;YAClD;QACJ;QACA,MAAM,iBAAiB;YACnB,KAAK;YACL,MAAM;QACV;QACA,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,cAAc,UAAU;YACxB,eAAe,IAAI,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,YAAY;QAC9E;QACA,IAAI,cAAc,YAAY;YAC1B,eAAe,GAAG,GAAG,IAAI,CAAC,wBAAwB,CAAC,UAAU,UAAU;QAC3E;QACA,IAAI,CAAC,QAAQ,CAAC;IAClB;IACA,yBAAyB,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE;QAClD,MAAM,eAAe,IAAI,CAAC,YAAY;QACtC,OAAO,CAAA,GAAA,iNAAA,CAAA,6BAA0B,AAAD,EAAE,SAAS,GAAG,CAAC,IAAI,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,SAAS,IAAI,GAAG,CAAC,IAAI,cAAc;IAC5G;IACA,gBAAgB;QACZ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB;YAChC,IAAI,CAAC,MAAM;QACf;IACJ;IACA,gBAAgB;QACZ,OAAO;IACX;IACA,cAAc;QACV,OAAO,CAAC,CAAC,WAAW,mBAAmB;IAC3C;AACJ;AACA,CAAA,GAAA,kKAAA,CAAA,UAAiB,AAAD,EAAE,YAAY;uCACf", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2271, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scroll_view.native.pull_down.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scroll_view.native.pull_down.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    move\r\n} from \"../../../common/core/animation/translator\";\r\nimport $ from \"../../../core/renderer\";\r\nimport Callbacks from \"../../../core/utils/callbacks\";\r\nimport {\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport LoadIndicator from \"../../../ui/load_indicator\";\r\nimport NativeStrategy from \"./m_scrollable.native\";\r\nconst SCROLLVIEW_PULLDOWN_REFRESHING_CLASS = \"dx-scrollview-pull-down-loading\";\r\nconst SCROLLVIEW_PULLDOWN_READY_CLASS = \"dx-scrollview-pull-down-ready\";\r\nconst SCROLLVIEW_PULLDOWN_IMAGE_CLASS = \"dx-scrollview-pull-down-image\";\r\nconst SCROLLVIEW_PULLDOWN_INDICATOR_CLASS = \"dx-scrollview-pull-down-indicator\";\r\nconst SCROLLVIEW_PULLDOWN_TEXT_CLASS = \"dx-scrollview-pull-down-text\";\r\nconst SCROLLVIEW_PULLDOWN_VISIBLE_TEXT_CLASS = \"dx-scrollview-pull-down-text-visible\";\r\nconst STATE_RELEASED = 0;\r\nconst STATE_READY = 1;\r\nconst STATE_REFRESHING = 2;\r\nconst STATE_LOADING = 3;\r\nconst PULLDOWN_RELEASE_TIME = 400;\r\nclass PullDownNativeScrollViewStrategy extends NativeStrategy {\r\n    _init(scrollView) {\r\n        super._init(scrollView);\r\n        this._$topPocket = scrollView._$topPocket;\r\n        this._$pullDown = scrollView._$pullDown;\r\n        this._$refreshingText = scrollView._$refreshingText;\r\n        this._$scrollViewContent = $(scrollView.content());\r\n        this._$container = $(scrollView.container());\r\n        this._initCallbacks()\r\n    }\r\n    _initCallbacks() {\r\n        this.pullDownCallbacks = Callbacks();\r\n        this.releaseCallbacks = Callbacks();\r\n        this.reachBottomCallbacks = Callbacks()\r\n    }\r\n    render() {\r\n        super.render();\r\n        this._renderPullDown();\r\n        this._releaseState()\r\n    }\r\n    _renderPullDown() {\r\n        const $image = $(\"<div>\").addClass(\"dx-scrollview-pull-down-image\");\r\n        const $loadContainer = $(\"<div>\").addClass(\"dx-scrollview-pull-down-indicator\");\r\n        const $loadIndicator = new LoadIndicator($(\"<div>\")).$element();\r\n        const $text = this._$pullDownText = $(\"<div>\").addClass(\"dx-scrollview-pull-down-text\");\r\n        this._$pullingDownText = $(\"<div>\").text(this.option(\"pullingDownText\")).appendTo($text);\r\n        this._$pulledDownText = $(\"<div>\").text(this.option(\"pulledDownText\")).appendTo($text);\r\n        this._$refreshingText = $(\"<div>\").text(this.option(\"refreshingText\")).appendTo($text);\r\n        this._$pullDown.empty().append($image).append($loadContainer.append($loadIndicator)).append($text)\r\n    }\r\n    _releaseState() {\r\n        this._state = 0;\r\n        this._refreshPullDownText()\r\n    }\r\n    _refreshPullDownText() {\r\n        const that = this;\r\n        const pullDownTextItems = [{\r\n            element: this._$pullingDownText,\r\n            visibleState: 0\r\n        }, {\r\n            element: this._$pulledDownText,\r\n            visibleState: 1\r\n        }, {\r\n            element: this._$refreshingText,\r\n            visibleState: 2\r\n        }];\r\n        each(pullDownTextItems, ((_, item) => {\r\n            const action = that._state === item.visibleState ? \"addClass\" : \"removeClass\";\r\n            item.element[action](\"dx-scrollview-pull-down-text-visible\")\r\n        }))\r\n    }\r\n    update() {\r\n        super.update();\r\n        this._setTopPocketOffset()\r\n    }\r\n    _updateDimensions() {\r\n        super._updateDimensions();\r\n        this._topPocketSize = this._$topPocket.get(0).clientHeight;\r\n        const contentEl = this._$scrollViewContent.get(0);\r\n        const containerEl = this._$container.get(0);\r\n        this._bottomBoundary = Math.max(contentEl.clientHeight - containerEl.clientHeight, 0)\r\n    }\r\n    _allowedDirections() {\r\n        const allowedDirections = super._allowedDirections();\r\n        allowedDirections.vertical = allowedDirections.vertical || this._pullDownEnabled;\r\n        return allowedDirections\r\n    }\r\n    _setTopPocketOffset() {\r\n        this._$topPocket.css({\r\n            top: -this._topPocketSize\r\n        })\r\n    }\r\n    handleEnd() {\r\n        super.handleEnd();\r\n        this._complete()\r\n    }\r\n    handleStop() {\r\n        super.handleStop();\r\n        this._complete()\r\n    }\r\n    _complete() {\r\n        if (1 === this._state) {\r\n            this._setPullDownOffset(this._topPocketSize);\r\n            clearTimeout(this._pullDownRefreshTimeout);\r\n            this._pullDownRefreshTimeout = setTimeout((() => {\r\n                this._pullDownRefreshing()\r\n            }), 400)\r\n        }\r\n    }\r\n    _setPullDownOffset(offset) {\r\n        move(this._$topPocket, {\r\n            top: offset\r\n        });\r\n        move(this._$scrollViewContent, {\r\n            top: offset\r\n        })\r\n    }\r\n    handleScroll(e) {\r\n        super.handleScroll(e);\r\n        if (2 === this._state) {\r\n            return\r\n        }\r\n        const currentLocation = this.location().top;\r\n        const scrollDelta = (this._location || 0) - currentLocation;\r\n        this._location = currentLocation;\r\n        if (this._isPullDown()) {\r\n            this._pullDownReady()\r\n        } else if (scrollDelta > 0 && this._isReachBottom()) {\r\n            this._reachBottom()\r\n        } else {\r\n            this._stateReleased()\r\n        }\r\n    }\r\n    _isPullDown() {\r\n        return this._pullDownEnabled && this._location >= this._topPocketSize\r\n    }\r\n    _isReachBottom() {\r\n        return this._reachBottomEnabled && this.isBottomReached()\r\n    }\r\n    isBottomReached() {\r\n        return Math.round(this._bottomBoundary + Math.floor(this._location)) <= 1\r\n    }\r\n    _reachBottom() {\r\n        if (3 === this._state) {\r\n            return\r\n        }\r\n        this._state = 3;\r\n        this.reachBottomCallbacks.fire()\r\n    }\r\n    _pullDownReady() {\r\n        if (1 === this._state) {\r\n            return\r\n        }\r\n        this._state = 1;\r\n        this._$pullDown.addClass(\"dx-scrollview-pull-down-ready\");\r\n        this._refreshPullDownText()\r\n    }\r\n    _stateReleased() {\r\n        if (0 === this._state) {\r\n            return\r\n        }\r\n        this._$pullDown.removeClass(\"dx-scrollview-pull-down-loading\").removeClass(\"dx-scrollview-pull-down-ready\");\r\n        this._releaseState()\r\n    }\r\n    _pullDownRefreshing() {\r\n        if (2 === this._state) {\r\n            return\r\n        }\r\n        this._state = 2;\r\n        this._$pullDown.addClass(\"dx-scrollview-pull-down-loading\").removeClass(\"dx-scrollview-pull-down-ready\");\r\n        this._refreshPullDownText();\r\n        this.pullDownCallbacks.fire()\r\n    }\r\n    pullDownEnable(enabled) {\r\n        if (enabled) {\r\n            this._updateDimensions();\r\n            this._setTopPocketOffset()\r\n        }\r\n        this._pullDownEnabled = enabled\r\n    }\r\n    reachBottomEnable(enabled) {\r\n        this._reachBottomEnabled = enabled\r\n    }\r\n    pendingRelease() {\r\n        this._state = 1\r\n    }\r\n    release() {\r\n        const deferred = Deferred();\r\n        this._updateDimensions();\r\n        clearTimeout(this._releaseTimeout);\r\n        if (3 === this._state) {\r\n            this._state = 0\r\n        }\r\n        this._releaseTimeout = setTimeout((() => {\r\n            this._setPullDownOffset(0);\r\n            this._stateReleased();\r\n            this.releaseCallbacks.fire();\r\n            this._updateAction();\r\n            deferred.resolve()\r\n        }), 400);\r\n        return deferred.promise()\r\n    }\r\n    dispose() {\r\n        clearTimeout(this._pullDownRefreshTimeout);\r\n        clearTimeout(this._releaseTimeout);\r\n        super.dispose()\r\n    }\r\n}\r\nexport default PullDownNativeScrollViewStrategy;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAGA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;;;;;;;;AACA,MAAM,uCAAuC;AAC7C,MAAM,kCAAkC;AACxC,MAAM,kCAAkC;AACxC,MAAM,sCAAsC;AAC5C,MAAM,iCAAiC;AACvC,MAAM,yCAAyC;AAC/C,MAAM,iBAAiB;AACvB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,gBAAgB;AACtB,MAAM,wBAAwB;AAC9B,MAAM,yCAAyC,iMAAA,CAAA,UAAc;IACzD,MAAM,UAAU,EAAE;QACd,KAAK,CAAC,MAAM;QACZ,IAAI,CAAC,WAAW,GAAG,WAAW,WAAW;QACzC,IAAI,CAAC,UAAU,GAAG,WAAW,UAAU;QACvC,IAAI,CAAC,gBAAgB,GAAG,WAAW,gBAAgB;QACnD,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO;QAC/C,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,SAAS;QACzC,IAAI,CAAC,cAAc;IACvB;IACA,iBAAiB;QACb,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QAChC,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;IACxC;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,aAAa;IACtB;IACA,kBAAkB;QACd,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACnC,MAAM,iBAAiB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC3C,MAAM,iBAAiB,IAAI,yJAAA,CAAA,UAAa,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ;QAC7D,MAAM,QAAQ,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACxD,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,QAAQ,CAAC;QAClF,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,QAAQ,CAAC;QAChF,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,QAAQ,CAAC;QAChF,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,MAAM,CAAC,eAAe,MAAM,CAAC,iBAAiB,MAAM,CAAC;IAChG;IACA,gBAAgB;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,oBAAoB;IAC7B;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,MAAM,oBAAoB;YAAC;gBACvB,SAAS,IAAI,CAAC,iBAAiB;gBAC/B,cAAc;YAClB;YAAG;gBACC,SAAS,IAAI,CAAC,gBAAgB;gBAC9B,cAAc;YAClB;YAAG;gBACC,SAAS,IAAI,CAAC,gBAAgB;gBAC9B,cAAc;YAClB;SAAE;QACF,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,mBAAoB,CAAC,GAAG;YACzB,MAAM,SAAS,KAAK,MAAM,KAAK,KAAK,YAAY,GAAG,aAAa;YAChE,KAAK,OAAO,CAAC,OAAO,CAAC;QACzB;IACJ;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,CAAC,mBAAmB;IAC5B;IACA,oBAAoB;QAChB,KAAK,CAAC;QACN,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,YAAY;QAC1D,MAAM,YAAY,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;QAC/C,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,UAAU,YAAY,GAAG,YAAY,YAAY,EAAE;IACvF;IACA,qBAAqB;QACjB,MAAM,oBAAoB,KAAK,CAAC;QAChC,kBAAkB,QAAQ,GAAG,kBAAkB,QAAQ,IAAI,IAAI,CAAC,gBAAgB;QAChF,OAAO;IACX;IACA,sBAAsB;QAClB,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YACjB,KAAK,CAAC,IAAI,CAAC,cAAc;QAC7B;IACJ;IACA,YAAY;QACR,KAAK,CAAC;QACN,IAAI,CAAC,SAAS;IAClB;IACA,aAAa;QACT,KAAK,CAAC;QACN,IAAI,CAAC,SAAS;IAClB;IACA,YAAY;QACR,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,cAAc;YAC3C,aAAa,IAAI,CAAC,uBAAuB;YACzC,IAAI,CAAC,uBAAuB,GAAG,WAAY;gBACvC,IAAI,CAAC,mBAAmB;YAC5B,GAAI;QACR;IACJ;IACA,mBAAmB,MAAM,EAAE;QACvB,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE;YACnB,KAAK;QACT;QACA,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,mBAAmB,EAAE;YAC3B,KAAK;QACT;IACJ;IACA,aAAa,CAAC,EAAE;QACZ,KAAK,CAAC,aAAa;QACnB,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,MAAM,kBAAkB,IAAI,CAAC,QAAQ,GAAG,GAAG;QAC3C,MAAM,cAAc,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI;QAC5C,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,IAAI,CAAC,cAAc;QACvB,OAAO,IAAI,cAAc,KAAK,IAAI,CAAC,cAAc,IAAI;YACjD,IAAI,CAAC,YAAY;QACrB,OAAO;YACH,IAAI,CAAC,cAAc;QACvB;IACJ;IACA,cAAc;QACV,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc;IACzE;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe;IAC3D;IACA,kBAAkB;QACd,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,MAAM;IAC5E;IACA,eAAe;QACX,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,oBAAoB,CAAC,IAAI;IAClC;IACA,iBAAiB;QACb,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACzB,IAAI,CAAC,oBAAoB;IAC7B;IACA,iBAAiB;QACb,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,mCAAmC,WAAW,CAAC;QAC3E,IAAI,CAAC,aAAa;IACtB;IACA,sBAAsB;QAClB,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mCAAmC,WAAW,CAAC;QACxE,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI;IAC/B;IACA,eAAe,OAAO,EAAE;QACpB,IAAI,SAAS;YACT,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,mBAAmB;QAC5B;QACA,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,mBAAmB,GAAG;IAC/B;IACA,iBAAiB;QACb,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,UAAU;QACN,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,IAAI,CAAC,iBAAiB;QACtB,aAAa,IAAI,CAAC,eAAe;QACjC,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,MAAM,GAAG;QAClB;QACA,IAAI,CAAC,eAAe,GAAG,WAAY;YAC/B,IAAI,CAAC,kBAAkB,CAAC;YACxB,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC1B,IAAI,CAAC,aAAa;YAClB,SAAS,OAAO;QACpB,GAAI;QACJ,OAAO,SAAS,OAAO;IAC3B;IACA,UAAU;QACN,aAAa,IAAI,CAAC,uBAAuB;QACzC,aAAa,IAAI,CAAC,eAAe;QACjC,KAAK,CAAC;IACV;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2505, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scroll_view.native.swipe_down.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scroll_view.native.swipe_down.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    move\r\n} from \"../../../common/core/animation/translator\";\r\nimport {\r\n    eventData\r\n} from \"../../../common/core/events/utils/index\";\r\nimport $ from \"../../../core/renderer\";\r\nimport Callbacks from \"../../../core/utils/callbacks\";\r\nimport {\r\n    Deferred\r\n} from \"../../../core/utils/deferred\";\r\nimport {\r\n    getOuterHeight\r\n} from \"../../../core/utils/size\";\r\nimport LoadIndicator from \"../../../ui/load_indicator\";\r\nimport NativeStrategy from \"./m_scrollable.native\";\r\nconst SCROLLVIEW_PULLDOWN_DOWN_LOADING_CLASS = \"dx-scrollview-pull-down-loading\";\r\nconst SCROLLVIEW_PULLDOWN_INDICATOR_CLASS = \"dx-scrollview-pull-down-indicator\";\r\nconst SCROLLVIEW_PULLDOWN_REFRESHING_CLASS = \"dx-scrollview-pull-down-refreshing\";\r\nconst PULLDOWN_ICON_CLASS = \"dx-icon-pulldown\";\r\nconst STATE_RELEASED = 0;\r\nconst STATE_READY = 1;\r\nconst STATE_REFRESHING = 2;\r\nconst STATE_TOUCHED = 4;\r\nconst STATE_PULLED = 5;\r\nclass SwipeDownNativeScrollViewStrategy extends NativeStrategy {\r\n    _init(scrollView) {\r\n        super._init(scrollView);\r\n        this._$topPocket = scrollView._$topPocket;\r\n        this._$pullDown = scrollView._$pullDown;\r\n        this._$scrollViewContent = $(scrollView.content());\r\n        this._$container = $(scrollView.container());\r\n        this._initCallbacks();\r\n        this._location = 0\r\n    }\r\n    _initCallbacks() {\r\n        this.pullDownCallbacks = Callbacks();\r\n        this.releaseCallbacks = Callbacks();\r\n        this.reachBottomCallbacks = Callbacks()\r\n    }\r\n    render() {\r\n        super.render();\r\n        this._renderPullDown();\r\n        this._releaseState()\r\n    }\r\n    _renderPullDown() {\r\n        const $loadContainer = $(\"<div>\").addClass(\"dx-scrollview-pull-down-indicator\");\r\n        const $loadIndicator = new LoadIndicator($(\"<div>\")).$element();\r\n        this._$icon = $(\"<div>\").addClass(\"dx-icon-pulldown\");\r\n        this._$pullDown.empty().append(this._$icon).append($loadContainer.append($loadIndicator))\r\n    }\r\n    _releaseState() {\r\n        this._state = 0;\r\n        this._releasePullDown();\r\n        this._updateDimensions()\r\n    }\r\n    _releasePullDown() {\r\n        this._$pullDown.css({\r\n            opacity: 0\r\n        })\r\n    }\r\n    _updateDimensions() {\r\n        super._updateDimensions();\r\n        this._topPocketSize = this._$topPocket.get(0).clientHeight;\r\n        const contentEl = this._$scrollViewContent.get(0);\r\n        const containerEl = this._$container.get(0);\r\n        this._bottomBoundary = Math.max(contentEl.clientHeight - containerEl.clientHeight, 0)\r\n    }\r\n    _allowedDirections() {\r\n        const allowedDirections = super._allowedDirections();\r\n        allowedDirections.vertical = allowedDirections.vertical || this._pullDownEnabled;\r\n        return allowedDirections\r\n    }\r\n    handleInit(e) {\r\n        super.handleInit(e);\r\n        if (0 === this._state && 0 === this._location) {\r\n            this._startClientY = eventData(e.originalEvent).y;\r\n            this._state = 4\r\n        }\r\n    }\r\n    handleMove(e) {\r\n        super.handleMove(e);\r\n        this._deltaY = eventData(e.originalEvent).y - this._startClientY;\r\n        if (4 === this._state) {\r\n            if (this._pullDownEnabled && this._deltaY > 0) {\r\n                this._state = 5\r\n            } else {\r\n                this._complete()\r\n            }\r\n        }\r\n        if (5 === this._state) {\r\n            e.preventDefault();\r\n            this._movePullDown()\r\n        }\r\n    }\r\n    _movePullDown() {\r\n        const pullDownHeight = this._getPullDownHeight();\r\n        const top = Math.min(3 * pullDownHeight, this._deltaY + this._getPullDownStartPosition());\r\n        const angle = 180 * top / pullDownHeight / 3;\r\n        this._$pullDown.css({\r\n            opacity: 1\r\n        }).toggleClass(\"dx-scrollview-pull-down-refreshing\", top < pullDownHeight);\r\n        move(this._$pullDown, {\r\n            top: top\r\n        });\r\n        this._$icon.css({\r\n            transform: `rotate(${angle}deg)`\r\n        })\r\n    }\r\n    _isPullDown() {\r\n        return this._pullDownEnabled && 5 === this._state && this._deltaY >= this._getPullDownHeight() - this._getPullDownStartPosition()\r\n    }\r\n    _getPullDownHeight() {\r\n        return Math.round(.05 * getOuterHeight(this._$element))\r\n    }\r\n    _getPullDownStartPosition() {\r\n        return -Math.round(1.5 * getOuterHeight(this._$pullDown))\r\n    }\r\n    handleEnd() {\r\n        if (this._isPullDown()) {\r\n            this._pullDownRefreshing()\r\n        }\r\n        this._complete()\r\n    }\r\n    handleStop() {\r\n        this._complete()\r\n    }\r\n    _complete() {\r\n        if (4 === this._state || 5 === this._state) {\r\n            this._releaseState()\r\n        }\r\n    }\r\n    handleScroll(e) {\r\n        super.handleScroll(e);\r\n        if (2 === this._state) {\r\n            return\r\n        }\r\n        const currentLocation = this.location().top;\r\n        const scrollDelta = this._location - currentLocation;\r\n        this._location = currentLocation;\r\n        if (scrollDelta > 0 && this._isReachBottom()) {\r\n            this._reachBottom()\r\n        } else {\r\n            this._stateReleased()\r\n        }\r\n    }\r\n    _isReachBottom() {\r\n        return this._reachBottomEnabled && this.isBottomReached()\r\n    }\r\n    isBottomReached() {\r\n        return Math.round(this._bottomBoundary + Math.floor(this._location)) <= 1\r\n    }\r\n    _reachBottom() {\r\n        this.reachBottomCallbacks.fire()\r\n    }\r\n    _stateReleased() {\r\n        if (0 === this._state) {\r\n            return\r\n        }\r\n        this._$pullDown.removeClass(\"dx-scrollview-pull-down-loading\");\r\n        this._releaseState()\r\n    }\r\n    _pullDownRefreshing() {\r\n        this._state = 2;\r\n        this._pullDownRefreshHandler()\r\n    }\r\n    _pullDownRefreshHandler() {\r\n        this._refreshPullDown();\r\n        this.pullDownCallbacks.fire()\r\n    }\r\n    _refreshPullDown() {\r\n        this._$pullDown.addClass(\"dx-scrollview-pull-down-loading\");\r\n        move(this._$pullDown, {\r\n            top: this._getPullDownHeight()\r\n        })\r\n    }\r\n    pullDownEnable(enabled) {\r\n        this._$topPocket.toggle(enabled);\r\n        this._pullDownEnabled = enabled\r\n    }\r\n    reachBottomEnable(enabled) {\r\n        this._reachBottomEnabled = enabled\r\n    }\r\n    pendingRelease() {\r\n        this._state = 1\r\n    }\r\n    release() {\r\n        const deferred = Deferred();\r\n        this._updateDimensions();\r\n        clearTimeout(this._releaseTimeout);\r\n        this._releaseTimeout = setTimeout((() => {\r\n            this._stateReleased();\r\n            this.releaseCallbacks.fire();\r\n            this._updateAction();\r\n            deferred.resolve()\r\n        }), 800);\r\n        return deferred.promise()\r\n    }\r\n    dispose() {\r\n        clearTimeout(this._pullDownRefreshTimeout);\r\n        clearTimeout(this._releaseTimeout);\r\n        super.dispose()\r\n    }\r\n}\r\nexport default SwipeDownNativeScrollViewStrategy;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAGA;AAAA;AAGA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;;;;;;;;;AACA,MAAM,yCAAyC;AAC/C,MAAM,sCAAsC;AAC5C,MAAM,uCAAuC;AAC7C,MAAM,sBAAsB;AAC5B,MAAM,iBAAiB;AACvB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,0CAA0C,iMAAA,CAAA,UAAc;IAC1D,MAAM,UAAU,EAAE;QACd,KAAK,CAAC,MAAM;QACZ,IAAI,CAAC,WAAW,GAAG,WAAW,WAAW;QACzC,IAAI,CAAC,UAAU,GAAG,WAAW,UAAU;QACvC,IAAI,CAAC,mBAAmB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO;QAC/C,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,SAAS;QACzC,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,iBAAiB;QACb,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QAChC,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;IACxC;IACA,SAAS;QACL,KAAK,CAAC;QACN,IAAI,CAAC,eAAe;QACpB,IAAI,CAAC,aAAa;IACtB;IACA,kBAAkB;QACd,MAAM,iBAAiB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC3C,MAAM,iBAAiB,IAAI,yJAAA,CAAA,UAAa,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ;QAC7D,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAClC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,eAAe,MAAM,CAAC;IAC7E;IACA,gBAAgB;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,iBAAiB;IAC1B;IACA,mBAAmB;QACf,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YAChB,SAAS;QACb;IACJ;IACA,oBAAoB;QAChB,KAAK,CAAC;QACN,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,YAAY;QAC1D,MAAM,YAAY,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;QAC/C,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACzC,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,UAAU,YAAY,GAAG,YAAY,YAAY,EAAE;IACvF;IACA,qBAAqB;QACjB,MAAM,oBAAoB,KAAK,CAAC;QAChC,kBAAkB,QAAQ,GAAG,kBAAkB,QAAQ,IAAI,IAAI,CAAC,gBAAgB;QAChF,OAAO;IACX;IACA,WAAW,CAAC,EAAE;QACV,KAAK,CAAC,WAAW;QACjB,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,SAAS,EAAE;YAC3C,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,EAAE,aAAa,EAAE,CAAC;YACjD,IAAI,CAAC,MAAM,GAAG;QAClB;IACJ;IACA,WAAW,CAAC,EAAE;QACV,KAAK,CAAC,WAAW;QACjB,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,EAAE,aAAa,EAAE,CAAC,GAAG,IAAI,CAAC,aAAa;QAChE,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB,IAAI,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,OAAO,GAAG,GAAG;gBAC3C,IAAI,CAAC,MAAM,GAAG;YAClB,OAAO;gBACH,IAAI,CAAC,SAAS;YAClB;QACJ;QACA,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB,EAAE,cAAc;YAChB,IAAI,CAAC,aAAa;QACtB;IACJ;IACA,gBAAgB;QACZ,MAAM,iBAAiB,IAAI,CAAC,kBAAkB;QAC9C,MAAM,MAAM,KAAK,GAAG,CAAC,IAAI,gBAAgB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,yBAAyB;QACtF,MAAM,QAAQ,MAAM,MAAM,iBAAiB;QAC3C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC;YAChB,SAAS;QACb,GAAG,WAAW,CAAC,sCAAsC,MAAM;QAC3D,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;YAClB,KAAK;QACT;QACA,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACZ,WAAW,CAAC,OAAO,EAAE,MAAM,IAAI,CAAC;QACpC;IACJ;IACA,cAAc;QACV,OAAO,IAAI,CAAC,gBAAgB,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,kBAAkB,KAAK,IAAI,CAAC,yBAAyB;IACnI;IACA,qBAAqB;QACjB,OAAO,KAAK,KAAK,CAAC,MAAM,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,SAAS;IACzD;IACA,4BAA4B;QACxB,OAAO,CAAC,KAAK,KAAK,CAAC,MAAM,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,UAAU;IAC3D;IACA,YAAY;QACR,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,IAAI,CAAC,mBAAmB;QAC5B;QACA,IAAI,CAAC,SAAS;IAClB;IACA,aAAa;QACT,IAAI,CAAC,SAAS;IAClB;IACA,YAAY;QACR,IAAI,MAAM,IAAI,CAAC,MAAM,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACxC,IAAI,CAAC,aAAa;QACtB;IACJ;IACA,aAAa,CAAC,EAAE;QACZ,KAAK,CAAC,aAAa;QACnB,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,MAAM,kBAAkB,IAAI,CAAC,QAAQ,GAAG,GAAG;QAC3C,MAAM,cAAc,IAAI,CAAC,SAAS,GAAG;QACrC,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,cAAc,KAAK,IAAI,CAAC,cAAc,IAAI;YAC1C,IAAI,CAAC,YAAY;QACrB,OAAO;YACH,IAAI,CAAC,cAAc;QACvB;IACJ;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe;IAC3D;IACA,kBAAkB;QACd,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,KAAK,KAAK,CAAC,IAAI,CAAC,SAAS,MAAM;IAC5E;IACA,eAAe;QACX,IAAI,CAAC,oBAAoB,CAAC,IAAI;IAClC;IACA,iBAAiB;QACb,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC;QAC5B,IAAI,CAAC,aAAa;IACtB;IACA,sBAAsB;QAClB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,uBAAuB;IAChC;IACA,0BAA0B;QACtB,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,iBAAiB,CAAC,IAAI;IAC/B;IACA,mBAAmB;QACf,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACzB,CAAA,GAAA,8KAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;YAClB,KAAK,IAAI,CAAC,kBAAkB;QAChC;IACJ;IACA,eAAe,OAAO,EAAE;QACpB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC;QACxB,IAAI,CAAC,gBAAgB,GAAG;IAC5B;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,mBAAmB,GAAG;IAC/B;IACA,iBAAiB;QACb,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,UAAU;QACN,MAAM,WAAW,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD;QACxB,IAAI,CAAC,iBAAiB;QACtB,aAAa,IAAI,CAAC,eAAe;QACjC,IAAI,CAAC,eAAe,GAAG,WAAY;YAC/B,IAAI,CAAC,cAAc;YACnB,IAAI,CAAC,gBAAgB,CAAC,IAAI;YAC1B,IAAI,CAAC,aAAa;YAClB,SAAS,OAAO;QACpB,GAAI;QACJ,OAAO,SAAS,OAAO;IAC3B;IACA,UAAU;QACN,aAAa,IAAI,CAAC,uBAAuB;QACzC,aAAa,IAAI,CAAC,eAAe;QACjC,KAAK,CAAC;IACV;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scroll_view.simulated.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scroll_view.simulated.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport $ from \"../../../core/renderer\";\r\nimport Callbacks from \"../../../core/utils/callbacks\";\r\nimport {\r\n    executeAsync\r\n} from \"../../../core/utils/common\";\r\nimport {\r\n    each\r\n} from \"../../../core/utils/iterator\";\r\nimport {\r\n    getHeight\r\n} from \"../../../core/utils/size\";\r\nimport LoadIndicator from \"../../../ui/load_indicator\";\r\nimport {\r\n    Scroller,\r\n    SimulatedStrategy\r\n} from \"./m_scrollable.simulated\";\r\nconst SCROLLVIEW_PULLDOWN_REFRESHING_CLASS = \"dx-scrollview-pull-down-loading\";\r\nconst SCROLLVIEW_PULLDOWN_READY_CLASS = \"dx-scrollview-pull-down-ready\";\r\nconst SCROLLVIEW_PULLDOWN_IMAGE_CLASS = \"dx-scrollview-pull-down-image\";\r\nconst SCROLLVIEW_PULLDOWN_INDICATOR_CLASS = \"dx-scrollview-pull-down-indicator\";\r\nconst SCROLLVIEW_PULLDOWN_TEXT_CLASS = \"dx-scrollview-pull-down-text\";\r\nconst SCROLLVIEW_PULLDOWN_VISIBLE_TEXT_CLASS = \"dx-scrollview-pull-down-text-visible\";\r\nconst STATE_RELEASED = 0;\r\nconst STATE_READY = 1;\r\nconst STATE_REFRESHING = 2;\r\nconst STATE_LOADING = 3;\r\nexport class ScrollViewScroller extends Scroller {\r\n    ctor() {\r\n        this._topPocketSize = 0;\r\n        this._bottomPocketSize = 0;\r\n        super.ctor.apply(this, arguments);\r\n        this._initCallbacks();\r\n        this._releaseState()\r\n    }\r\n    _releaseState() {\r\n        this._state = 0;\r\n        this._refreshPullDownText()\r\n    }\r\n    _refreshPullDownText() {\r\n        const that = this;\r\n        const pullDownTextItems = [{\r\n            element: this._$pullingDownText,\r\n            visibleState: 0\r\n        }, {\r\n            element: this._$pulledDownText,\r\n            visibleState: 1\r\n        }, {\r\n            element: this._$refreshingText,\r\n            visibleState: 2\r\n        }];\r\n        each(pullDownTextItems, ((_, item) => {\r\n            const action = that._state === item.visibleState ? \"addClass\" : \"removeClass\";\r\n            item.element[action](\"dx-scrollview-pull-down-text-visible\")\r\n        }))\r\n    }\r\n    _initCallbacks() {\r\n        this.pullDownCallbacks = Callbacks();\r\n        this.releaseCallbacks = Callbacks();\r\n        this.reachBottomCallbacks = Callbacks()\r\n    }\r\n    _updateBounds() {\r\n        const considerPockets = \"horizontal\" !== this._direction;\r\n        if (considerPockets) {\r\n            this._topPocketSize = this._$topPocket.get(0).clientHeight;\r\n            this._bottomPocketSize = this._$bottomPocket.get(0).clientHeight;\r\n            const containerEl = this._$container.get(0);\r\n            const contentEl = this._$content.get(0);\r\n            this._bottomBoundary = Math.max(contentEl.clientHeight - this._bottomPocketSize - containerEl.clientHeight, 0)\r\n        }\r\n        super._updateBounds()\r\n    }\r\n    _updateScrollbar() {\r\n        this._scrollbar.option({\r\n            containerSize: this._containerSize(),\r\n            contentSize: this._contentSize() - this._topPocketSize - this._bottomPocketSize,\r\n            scaleRatio: this._getScaleRatio()\r\n        })\r\n    }\r\n    _moveContent() {\r\n        super._moveContent();\r\n        if (this._isPullDown()) {\r\n            this._pullDownReady()\r\n        } else if (this._isReachBottom()) {\r\n            this._reachBottomReady()\r\n        } else if (0 !== this._state) {\r\n            this._stateReleased()\r\n        }\r\n    }\r\n    _moveScrollbar() {\r\n        this._scrollbar.moveTo(this._topPocketSize + this._location)\r\n    }\r\n    _isPullDown() {\r\n        return this._pullDownEnabled && this._location >= 0\r\n    }\r\n    _isReachBottom() {\r\n        return this._reachBottomEnabled && this.isBottomReached()\r\n    }\r\n    isBottomReached() {\r\n        const containerEl = this._$container.get(0);\r\n        return Math.round(this._bottomBoundary - Math.ceil(containerEl.scrollTop)) <= 1\r\n    }\r\n    _scrollComplete() {\r\n        if (this._inBounds() && 1 === this._state) {\r\n            this._pullDownRefreshing()\r\n        } else if (this._inBounds() && 3 === this._state) {\r\n            this._reachBottomLoading()\r\n        } else {\r\n            super._scrollComplete()\r\n        }\r\n    }\r\n    _reachBottomReady() {\r\n        if (3 === this._state) {\r\n            return\r\n        }\r\n        this._state = 3;\r\n        this._minOffset = this._getMinOffset()\r\n    }\r\n    _getMaxOffset() {\r\n        return -this._topPocketSize\r\n    }\r\n    _getMinOffset() {\r\n        return Math.min(super._getMinOffset(), -this._topPocketSize)\r\n    }\r\n    _reachBottomLoading() {\r\n        this.reachBottomCallbacks.fire()\r\n    }\r\n    _pullDownReady() {\r\n        if (1 === this._state) {\r\n            return\r\n        }\r\n        this._state = 1;\r\n        this._maxOffset = 0;\r\n        this._$pullDown.addClass(\"dx-scrollview-pull-down-ready\");\r\n        this._refreshPullDownText()\r\n    }\r\n    _stateReleased() {\r\n        if (0 === this._state) {\r\n            return\r\n        }\r\n        this._releaseState();\r\n        this._updateBounds();\r\n        this._$pullDown.removeClass(\"dx-scrollview-pull-down-loading\").removeClass(\"dx-scrollview-pull-down-ready\");\r\n        this.releaseCallbacks.fire()\r\n    }\r\n    _pullDownRefreshing() {\r\n        if (2 === this._state) {\r\n            return\r\n        }\r\n        this._state = 2;\r\n        this._$pullDown.addClass(\"dx-scrollview-pull-down-loading\").removeClass(\"dx-scrollview-pull-down-ready\");\r\n        this._refreshPullDownText();\r\n        this.pullDownCallbacks.fire()\r\n    }\r\n    _releaseHandler() {\r\n        var _this$_releaseTask;\r\n        if (0 === this._state) {\r\n            this._moveToBounds()\r\n        }\r\n        this._update();\r\n        if (this._releaseTask) {\r\n            this._releaseTask.abort()\r\n        }\r\n        this._releaseTask = executeAsync(this._release.bind(this));\r\n        return null === (_this$_releaseTask = this._releaseTask) || void 0 === _this$_releaseTask ? void 0 : _this$_releaseTask.promise\r\n    }\r\n    _release() {\r\n        this._stateReleased();\r\n        this._scrollComplete()\r\n    }\r\n    _reachBottomEnablingHandler(enabled) {\r\n        if (this._reachBottomEnabled === enabled) {\r\n            return\r\n        }\r\n        this._reachBottomEnabled = enabled;\r\n        this._updateBounds()\r\n    }\r\n    _pullDownEnablingHandler(enabled) {\r\n        if (this._pullDownEnabled === enabled) {\r\n            return\r\n        }\r\n        this._pullDownEnabled = enabled;\r\n        this._considerTopPocketChange();\r\n        this._updateHandler()\r\n    }\r\n    _considerTopPocketChange() {\r\n        this._location -= getHeight(this._$topPocket) || -this._topPocketSize;\r\n        this._maxOffset = 0;\r\n        this._move()\r\n    }\r\n    _pendingReleaseHandler() {\r\n        this._state = 1\r\n    }\r\n    dispose() {\r\n        if (this._releaseTask) {\r\n            this._releaseTask.abort()\r\n        }\r\n        super.dispose()\r\n    }\r\n}\r\nclass SimulatedScrollViewStrategy extends SimulatedStrategy {\r\n    _init(scrollView) {\r\n        super._init(scrollView);\r\n        this._$pullDown = scrollView._$pullDown;\r\n        this._$topPocket = scrollView._$topPocket;\r\n        this._$bottomPocket = scrollView._$bottomPocket;\r\n        this._initCallbacks()\r\n    }\r\n    _initCallbacks() {\r\n        this.pullDownCallbacks = Callbacks();\r\n        this.releaseCallbacks = Callbacks();\r\n        this.reachBottomCallbacks = Callbacks()\r\n    }\r\n    render() {\r\n        this._renderPullDown();\r\n        super.render()\r\n    }\r\n    _renderPullDown() {\r\n        const $image = $(\"<div>\").addClass(\"dx-scrollview-pull-down-image\");\r\n        const $loadContainer = $(\"<div>\").addClass(\"dx-scrollview-pull-down-indicator\");\r\n        const $loadIndicator = new LoadIndicator($(\"<div>\")).$element();\r\n        const $text = this._$pullDownText = $(\"<div>\").addClass(\"dx-scrollview-pull-down-text\");\r\n        this._$pullingDownText = $(\"<div>\").text(this.option(\"pullingDownText\")).appendTo($text);\r\n        this._$pulledDownText = $(\"<div>\").text(this.option(\"pulledDownText\")).appendTo($text);\r\n        this._$refreshingText = $(\"<div>\").text(this.option(\"refreshingText\")).appendTo($text);\r\n        this._$pullDown.empty().append($image).append($loadContainer.append($loadIndicator)).append($text)\r\n    }\r\n    pullDownEnable(enabled) {\r\n        this._eventHandler(\"pullDownEnabling\", enabled)\r\n    }\r\n    reachBottomEnable(enabled) {\r\n        this._eventHandler(\"reachBottomEnabling\", enabled)\r\n    }\r\n    _createScroller(direction) {\r\n        const that = this;\r\n        const scroller = that._scrollers[direction] = new ScrollViewScroller(that._scrollerOptions(direction));\r\n        scroller.pullDownCallbacks.add((() => {\r\n            that.pullDownCallbacks.fire()\r\n        }));\r\n        scroller.releaseCallbacks.add((() => {\r\n            that.releaseCallbacks.fire()\r\n        }));\r\n        scroller.reachBottomCallbacks.add((() => {\r\n            that.reachBottomCallbacks.fire()\r\n        }))\r\n    }\r\n    _scrollerOptions(direction) {\r\n        return _extends({}, super._scrollerOptions(direction), {\r\n            $topPocket: this._$topPocket,\r\n            $bottomPocket: this._$bottomPocket,\r\n            $pullDown: this._$pullDown,\r\n            $pullDownText: this._$pullDownText,\r\n            $pullingDownText: this._$pullingDownText,\r\n            $pulledDownText: this._$pulledDownText,\r\n            $refreshingText: this._$refreshingText\r\n        })\r\n    }\r\n    pendingRelease() {\r\n        this._eventHandler(\"pendingRelease\")\r\n    }\r\n    release() {\r\n        return this._eventHandler(\"release\").done(this._updateAction)\r\n    }\r\n    location() {\r\n        const location = super.location();\r\n        location.top += getHeight(this._$topPocket);\r\n        return location\r\n    }\r\n    isBottomReached() {\r\n        return this._scrollers.vertical.isBottomReached()\r\n    }\r\n    dispose() {\r\n        each(this._scrollers, (function() {\r\n            this.dispose()\r\n        }));\r\n        super.dispose()\r\n    }\r\n}\r\nexport default SimulatedScrollViewStrategy;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AACA;;;;;;;;;AAIA,MAAM,uCAAuC;AAC7C,MAAM,kCAAkC;AACxC,MAAM,kCAAkC;AACxC,MAAM,sCAAsC;AAC5C,MAAM,iCAAiC;AACvC,MAAM,yCAAyC;AAC/C,MAAM,iBAAiB;AACvB,MAAM,cAAc;AACpB,MAAM,mBAAmB;AACzB,MAAM,gBAAgB;AACf,MAAM,2BAA2B,oMAAA,CAAA,WAAQ;IAC5C,OAAO;QACH,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG;QACzB,KAAK,CAAC,KAAK,KAAK,CAAC,IAAI,EAAE;QACvB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,aAAa;IACtB;IACA,gBAAgB;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,oBAAoB;IAC7B;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,MAAM,oBAAoB;YAAC;gBACvB,SAAS,IAAI,CAAC,iBAAiB;gBAC/B,cAAc;YAClB;YAAG;gBACC,SAAS,IAAI,CAAC,gBAAgB;gBAC9B,cAAc;YAClB;YAAG;gBACC,SAAS,IAAI,CAAC,gBAAgB;gBAC9B,cAAc;YAClB;SAAE;QACF,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,mBAAoB,CAAC,GAAG;YACzB,MAAM,SAAS,KAAK,MAAM,KAAK,KAAK,YAAY,GAAG,aAAa;YAChE,KAAK,OAAO,CAAC,OAAO,CAAC;QACzB;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QAChC,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;IACxC;IACA,gBAAgB;QACZ,MAAM,kBAAkB,iBAAiB,IAAI,CAAC,UAAU;QACxD,IAAI,iBAAiB;YACjB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,YAAY;YAC1D,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,GAAG,YAAY;YAChE,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YACzC,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC;YACrC,IAAI,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,UAAU,YAAY,GAAG,IAAI,CAAC,iBAAiB,GAAG,YAAY,YAAY,EAAE;QAChH;QACA,KAAK,CAAC;IACV;IACA,mBAAmB;QACf,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACnB,eAAe,IAAI,CAAC,cAAc;YAClC,aAAa,IAAI,CAAC,YAAY,KAAK,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,iBAAiB;YAC/E,YAAY,IAAI,CAAC,cAAc;QACnC;IACJ;IACA,eAAe;QACX,KAAK,CAAC;QACN,IAAI,IAAI,CAAC,WAAW,IAAI;YACpB,IAAI,CAAC,cAAc;QACvB,OAAO,IAAI,IAAI,CAAC,cAAc,IAAI;YAC9B,IAAI,CAAC,iBAAiB;QAC1B,OAAO,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YAC1B,IAAI,CAAC,cAAc;QACvB;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,SAAS;IAC/D;IACA,cAAc;QACV,OAAO,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,SAAS,IAAI;IACtD;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,mBAAmB,IAAI,IAAI,CAAC,eAAe;IAC3D;IACA,kBAAkB;QACd,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;QACzC,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,eAAe,GAAG,KAAK,IAAI,CAAC,YAAY,SAAS,MAAM;IAClF;IACA,kBAAkB;QACd,IAAI,IAAI,CAAC,SAAS,MAAM,MAAM,IAAI,CAAC,MAAM,EAAE;YACvC,IAAI,CAAC,mBAAmB;QAC5B,OAAO,IAAI,IAAI,CAAC,SAAS,MAAM,MAAM,IAAI,CAAC,MAAM,EAAE;YAC9C,IAAI,CAAC,mBAAmB;QAC5B,OAAO;YACH,KAAK,CAAC;QACV;IACJ;IACA,oBAAoB;QAChB,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,aAAa;IACxC;IACA,gBAAgB;QACZ,OAAO,CAAC,IAAI,CAAC,cAAc;IAC/B;IACA,gBAAgB;QACZ,OAAO,KAAK,GAAG,CAAC,KAAK,CAAC,iBAAiB,CAAC,IAAI,CAAC,cAAc;IAC/D;IACA,sBAAsB;QAClB,IAAI,CAAC,oBAAoB,CAAC,IAAI;IAClC;IACA,iBAAiB;QACb,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC;QACzB,IAAI,CAAC,oBAAoB;IAC7B;IACA,iBAAiB;QACb,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,mCAAmC,WAAW,CAAC;QAC3E,IAAI,CAAC,gBAAgB,CAAC,IAAI;IAC9B;IACA,sBAAsB;QAClB,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB;QACJ;QACA,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,mCAAmC,WAAW,CAAC;QACxE,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,iBAAiB,CAAC,IAAI;IAC/B;IACA,kBAAkB;QACd,IAAI;QACJ,IAAI,MAAM,IAAI,CAAC,MAAM,EAAE;YACnB,IAAI,CAAC,aAAa;QACtB;QACA,IAAI,CAAC,OAAO;QACZ,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,KAAK;QAC3B;QACA,IAAI,CAAC,YAAY,GAAG,CAAA,GAAA,+KAAA,CAAA,eAAY,AAAD,EAAE,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI;QACxD,OAAO,SAAS,CAAC,qBAAqB,IAAI,CAAC,YAAY,KAAK,KAAK,MAAM,qBAAqB,KAAK,IAAI,mBAAmB,OAAO;IACnI;IACA,WAAW;QACP,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,eAAe;IACxB;IACA,4BAA4B,OAAO,EAAE;QACjC,IAAI,IAAI,CAAC,mBAAmB,KAAK,SAAS;YACtC;QACJ;QACA,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,aAAa;IACtB;IACA,yBAAyB,OAAO,EAAE;QAC9B,IAAI,IAAI,CAAC,gBAAgB,KAAK,SAAS;YACnC;QACJ;QACA,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,cAAc;IACvB;IACA,2BAA2B;QACvB,IAAI,CAAC,SAAS,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,cAAc;QACrE,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,KAAK;IACd;IACA,yBAAyB;QACrB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,UAAU;QACN,IAAI,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,YAAY,CAAC,KAAK;QAC3B;QACA,KAAK,CAAC;IACV;AACJ;AACA,MAAM,oCAAoC,oMAAA,CAAA,oBAAiB;IACvD,MAAM,UAAU,EAAE;QACd,KAAK,CAAC,MAAM;QACZ,IAAI,CAAC,UAAU,GAAG,WAAW,UAAU;QACvC,IAAI,CAAC,WAAW,GAAG,WAAW,WAAW;QACzC,IAAI,CAAC,cAAc,GAAG,WAAW,cAAc;QAC/C,IAAI,CAAC,cAAc;IACvB;IACA,iBAAiB;QACb,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QACjC,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;QAChC,IAAI,CAAC,oBAAoB,GAAG,CAAA,GAAA,+JAAA,CAAA,UAAS,AAAD;IACxC;IACA,SAAS;QACL,IAAI,CAAC,eAAe;QACpB,KAAK,CAAC;IACV;IACA,kBAAkB;QACd,MAAM,SAAS,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACnC,MAAM,iBAAiB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC3C,MAAM,iBAAiB,IAAI,yJAAA,CAAA,UAAa,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ;QAC7D,MAAM,QAAQ,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACxD,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,oBAAoB,QAAQ,CAAC;QAClF,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,QAAQ,CAAC;QAChF,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,mBAAmB,QAAQ,CAAC;QAChF,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,MAAM,CAAC,QAAQ,MAAM,CAAC,eAAe,MAAM,CAAC,iBAAiB,MAAM,CAAC;IAChG;IACA,eAAe,OAAO,EAAE;QACpB,IAAI,CAAC,aAAa,CAAC,oBAAoB;IAC3C;IACA,kBAAkB,OAAO,EAAE;QACvB,IAAI,CAAC,aAAa,CAAC,uBAAuB;IAC9C;IACA,gBAAgB,SAAS,EAAE;QACvB,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,UAAU,CAAC,UAAU,GAAG,IAAI,mBAAmB,KAAK,gBAAgB,CAAC;QAC3F,SAAS,iBAAiB,CAAC,GAAG,CAAE;YAC5B,KAAK,iBAAiB,CAAC,IAAI;QAC/B;QACA,SAAS,gBAAgB,CAAC,GAAG,CAAE;YAC3B,KAAK,gBAAgB,CAAC,IAAI;QAC9B;QACA,SAAS,oBAAoB,CAAC,GAAG,CAAE;YAC/B,KAAK,oBAAoB,CAAC,IAAI;QAClC;IACJ;IACA,iBAAiB,SAAS,EAAE;QACxB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,iBAAiB,YAAY;YACnD,YAAY,IAAI,CAAC,WAAW;YAC5B,eAAe,IAAI,CAAC,cAAc;YAClC,WAAW,IAAI,CAAC,UAAU;YAC1B,eAAe,IAAI,CAAC,cAAc;YAClC,kBAAkB,IAAI,CAAC,iBAAiB;YACxC,iBAAiB,IAAI,CAAC,gBAAgB;YACtC,iBAAiB,IAAI,CAAC,gBAAgB;QAC1C;IACJ;IACA,iBAAiB;QACb,IAAI,CAAC,aAAa,CAAC;IACvB;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,aAAa;IAChE;IACA,WAAW;QACP,MAAM,WAAW,KAAK,CAAC;QACvB,SAAS,GAAG,IAAI,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,WAAW;QAC1C,OAAO;IACX;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,eAAe;IACnD;IACA,UAAU;QACN,CAAA,GAAA,iLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,UAAU,EAAG;YACnB,IAAI,CAAC,OAAO;QAChB;QACA,KAAK,CAAC;IACV;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3027, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/m_scroll_view.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/m_scroll_view.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport messageLocalization from \"../../../common/core/localization/message\";\r\nimport registerComponent from \"../../../core/component_registrator\";\r\nimport devices from \"../../../core/devices\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../core/element\";\r\nimport $ from \"../../../core/renderer\";\r\nimport {\r\n    hasWindow\r\n} from \"../../../core/utils/window\";\r\nimport LoadIndicator from \"../../../ui/load_indicator\";\r\nimport {\r\n    isMaterialBased\r\n} from \"../../../ui/themes\";\r\nimport LoadPanel from \"../../ui/m_load_panel\";\r\nimport PullDownStrategy from \"./m_scroll_view.native.pull_down\";\r\nimport SwipeDownStrategy from \"./m_scroll_view.native.swipe_down\";\r\nimport SimulatedStrategy from \"./m_scroll_view.simulated\";\r\nimport Scrollable from \"./m_scrollable\";\r\nconst SCROLLVIEW_CLASS = \"dx-scrollview\";\r\nconst SCROLLVIEW_CONTENT_CLASS = \"dx-scrollview-content\";\r\nconst SCROLLVIEW_TOP_POCKET_CLASS = \"dx-scrollview-top-pocket\";\r\nconst SCROLLVIEW_BOTTOM_POCKET_CLASS = \"dx-scrollview-bottom-pocket\";\r\nconst SCROLLVIEW_PULLDOWN_CLASS = \"dx-scrollview-pull-down\";\r\nconst SCROLLVIEW_REACHBOTTOM_CLASS = \"dx-scrollview-scrollbottom\";\r\nconst SCROLLVIEW_REACHBOTTOM_INDICATOR_CLASS = \"dx-scrollview-scrollbottom-indicator\";\r\nconst SCROLLVIEW_REACHBOTTOM_TEXT_CLASS = \"dx-scrollview-scrollbottom-text\";\r\nconst SCROLLVIEW_LOADPANEL = \"dx-scrollview-loadpanel\";\r\nconst refreshStrategies = {\r\n    pullDown: PullDownStrategy,\r\n    swipeDown: SwipeDownStrategy,\r\n    simulated: SimulatedStrategy\r\n};\r\nconst isServerSide = !hasWindow();\r\nexport class ScrollViewServerSide extends Scrollable {\r\n    finishLoading() {}\r\n    release() {}\r\n    refresh() {}\r\n    scrollOffset() {\r\n        return {\r\n            top: 0,\r\n            left: 0\r\n        }\r\n    }\r\n    isBottomReached() {\r\n        return false\r\n    }\r\n    _optionChanged(args) {\r\n        const {\r\n            name: name\r\n        } = args;\r\n        if (\"onUpdated\" !== name) {\r\n            return super._optionChanged.apply(this, arguments)\r\n        }\r\n    }\r\n}\r\nexport class ScrollView extends Scrollable {\r\n    _getDefaultOptions() {\r\n        return _extends({}, super._getDefaultOptions(), {\r\n            pullingDownText: messageLocalization.format(\"dxScrollView-pullingDownText\"),\r\n            pulledDownText: messageLocalization.format(\"dxScrollView-pulledDownText\"),\r\n            refreshingText: messageLocalization.format(\"dxScrollView-refreshingText\"),\r\n            reachBottomText: messageLocalization.format(\"dxScrollView-reachBottomText\"),\r\n            onPullDown: null,\r\n            onReachBottom: null,\r\n            refreshStrategy: \"pullDown\"\r\n        })\r\n    }\r\n    _defaultOptionsRules() {\r\n        return super._defaultOptionsRules().concat([{\r\n            device() {\r\n                const realDevice = devices.real();\r\n                return \"android\" === realDevice.platform\r\n            },\r\n            options: {\r\n                refreshStrategy: \"swipeDown\"\r\n            }\r\n        }, {\r\n            device: () => isMaterialBased(),\r\n            options: {\r\n                pullingDownText: \"\",\r\n                pulledDownText: \"\",\r\n                refreshingText: \"\",\r\n                reachBottomText: \"\"\r\n            }\r\n        }])\r\n    }\r\n    _init() {\r\n        super._init();\r\n        this._loadingIndicatorEnabled = true\r\n    }\r\n    _initScrollableMarkup() {\r\n        super._initScrollableMarkup();\r\n        this.$element().addClass(\"dx-scrollview\");\r\n        this._initContent();\r\n        this._initTopPocket();\r\n        this._initBottomPocket();\r\n        this._initLoadPanel()\r\n    }\r\n    _initContent() {\r\n        const $content = $(\"<div>\").addClass(\"dx-scrollview-content\");\r\n        this._$content.wrapInner($content)\r\n    }\r\n    _initTopPocket() {\r\n        this._$topPocket = $(\"<div>\").addClass(\"dx-scrollview-top-pocket\");\r\n        this._$pullDown = $(\"<div>\").addClass(\"dx-scrollview-pull-down\");\r\n        this._$topPocket.append(this._$pullDown);\r\n        this._$content.prepend(this._$topPocket)\r\n    }\r\n    _initBottomPocket() {\r\n        this._$bottomPocket = $(\"<div>\").addClass(\"dx-scrollview-bottom-pocket\");\r\n        this._$reachBottom = $(\"<div>\").addClass(\"dx-scrollview-scrollbottom\");\r\n        const $loadContainer = $(\"<div>\").addClass(\"dx-scrollview-scrollbottom-indicator\");\r\n        const $loadIndicator = new LoadIndicator($(\"<div>\")).$element();\r\n        this._$reachBottomText = $(\"<div>\").addClass(\"dx-scrollview-scrollbottom-text\");\r\n        this._updateReachBottomText();\r\n        this._$reachBottom.append($loadContainer.append($loadIndicator)).append(this._$reachBottomText);\r\n        this._$bottomPocket.append(this._$reachBottom);\r\n        this._$content.append(this._$bottomPocket)\r\n    }\r\n    _initLoadPanel() {\r\n        const $loadPanelElement = $(\"<div>\").addClass(SCROLLVIEW_LOADPANEL).appendTo(this.$element());\r\n        const {\r\n            refreshingText: refreshingText\r\n        } = this.option();\r\n        this._loadPanel = this._createComponent($loadPanelElement, LoadPanel, {\r\n            shading: false,\r\n            delay: 400,\r\n            message: refreshingText,\r\n            position: {\r\n                of: this.$element()\r\n            }\r\n        })\r\n    }\r\n    _updateReachBottomText() {\r\n        const {\r\n            reachBottomText: reachBottomText\r\n        } = this.option();\r\n        this._$reachBottomText.text(reachBottomText)\r\n    }\r\n    _createStrategy() {\r\n        const {\r\n            useNative: useNative,\r\n            refreshStrategy: refreshStrategy\r\n        } = this.option();\r\n        const strategyName = useNative ? refreshStrategy : \"simulated\";\r\n        const strategyClass = refreshStrategies[strategyName];\r\n        this._strategy = new strategyClass(this);\r\n        this._strategy.pullDownCallbacks.add(this._pullDownHandler.bind(this));\r\n        this._strategy.releaseCallbacks.add(this._releaseHandler.bind(this));\r\n        this._strategy.reachBottomCallbacks.add(this._reachBottomHandler.bind(this))\r\n    }\r\n    _createActions() {\r\n        super._createActions();\r\n        this._pullDownAction = this._createActionByOption(\"onPullDown\");\r\n        this._reachBottomAction = this._createActionByOption(\"onReachBottom\");\r\n        this._tryRefreshPocketState()\r\n    }\r\n    _tryRefreshPocketState() {\r\n        this._pullDownEnable(this.hasActionSubscription(\"onPullDown\"));\r\n        this._reachBottomEnable(this.hasActionSubscription(\"onReachBottom\"))\r\n    }\r\n    on(eventName) {\r\n        const result = super.on.apply(this, arguments);\r\n        if (\"pullDown\" === eventName || \"reachBottom\" === eventName) {\r\n            this._tryRefreshPocketState()\r\n        }\r\n        return result\r\n    }\r\n    _pullDownEnable(enabled) {\r\n        if (0 === arguments.length) {\r\n            return this._pullDownEnabled\r\n        }\r\n        if (this._$pullDown && this._strategy) {\r\n            this._$pullDown.toggle(enabled);\r\n            this._strategy.pullDownEnable(enabled);\r\n            this._pullDownEnabled = enabled\r\n        }\r\n    }\r\n    _reachBottomEnable(enabled) {\r\n        if (0 === arguments.length) {\r\n            return this._reachBottomEnabled\r\n        }\r\n        if (this._$reachBottom && this._strategy) {\r\n            this._$reachBottom.toggle(enabled);\r\n            this._strategy.reachBottomEnable(enabled);\r\n            this._reachBottomEnabled = enabled\r\n        }\r\n    }\r\n    _pullDownHandler() {\r\n        this._loadingIndicator(false);\r\n        this._pullDownLoading()\r\n    }\r\n    _loadingIndicator(value) {\r\n        if (arguments.length < 1) {\r\n            return this._loadingIndicatorEnabled\r\n        }\r\n        this._loadingIndicatorEnabled = value\r\n    }\r\n    _pullDownLoading() {\r\n        var _this$_pullDownAction;\r\n        this.startLoading();\r\n        null === (_this$_pullDownAction = this._pullDownAction) || void 0 === _this$_pullDownAction || _this$_pullDownAction.call(this)\r\n    }\r\n    _reachBottomHandler() {\r\n        this._loadingIndicator(false);\r\n        this._reachBottomLoading()\r\n    }\r\n    _reachBottomLoading() {\r\n        var _this$_reachBottomAct;\r\n        this.startLoading();\r\n        null === (_this$_reachBottomAct = this._reachBottomAction) || void 0 === _this$_reachBottomAct || _this$_reachBottomAct.call(this)\r\n    }\r\n    _releaseHandler() {\r\n        this.finishLoading();\r\n        this._loadingIndicator(true)\r\n    }\r\n    _optionChanged(args) {\r\n        switch (args.name) {\r\n            case \"onPullDown\":\r\n            case \"onReachBottom\":\r\n                this._createActions();\r\n                break;\r\n            case \"pullingDownText\":\r\n            case \"pulledDownText\":\r\n            case \"refreshingText\":\r\n            case \"refreshStrategy\":\r\n                this._invalidate();\r\n                break;\r\n            case \"reachBottomText\":\r\n                this._updateReachBottomText();\r\n                break;\r\n            default:\r\n                super._optionChanged(args)\r\n        }\r\n    }\r\n    content() {\r\n        return getPublicElement(this._$content.children().eq(1))\r\n    }\r\n    release(preventReachBottom) {\r\n        if (void 0 !== preventReachBottom) {\r\n            this.toggleLoading(!preventReachBottom)\r\n        }\r\n        return this._strategy.release()\r\n    }\r\n    toggleLoading(showOrHide) {\r\n        this._reachBottomEnable(showOrHide)\r\n    }\r\n    refresh() {\r\n        if (!this.hasActionSubscription(\"onPullDown\")) {\r\n            return\r\n        }\r\n        this._strategy.pendingRelease();\r\n        this._pullDownLoading()\r\n    }\r\n    startLoading() {\r\n        if (this._loadingIndicator() && this.$element().is(\":visible\")) {\r\n            this._loadPanel.show()\r\n        }\r\n        this._lock()\r\n    }\r\n    finishLoading() {\r\n        this._loadPanel.hide();\r\n        this._unlock()\r\n    }\r\n    isBottomReached() {\r\n        return this._strategy.isBottomReached()\r\n    }\r\n    _dispose() {\r\n        this._strategy.dispose();\r\n        super._dispose();\r\n        if (this._loadPanel) {\r\n            this._loadPanel.$element().remove()\r\n        }\r\n    }\r\n}\r\nregisterComponent(\"dxScrollView\", isServerSide ? ScrollViewServerSide : ScrollView);\r\nexport default isServerSide ? ScrollViewServerSide : ScrollView;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;AACA;AACA;AACA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;AACA;AAGA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AACA,MAAM,mBAAmB;AACzB,MAAM,2BAA2B;AACjC,MAAM,8BAA8B;AACpC,MAAM,iCAAiC;AACvC,MAAM,4BAA4B;AAClC,MAAM,+BAA+B;AACrC,MAAM,yCAAyC;AAC/C,MAAM,oCAAoC;AAC1C,MAAM,uBAAuB;AAC7B,MAAM,oBAAoB;IACtB,UAAU,+MAAA,CAAA,UAAgB;IAC1B,WAAW,gNAAA,CAAA,UAAiB;IAC5B,WAAW,qMAAA,CAAA,UAAiB;AAChC;AACA,MAAM,eAAe,CAAC,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD;AACvB,MAAM,6BAA6B,uLAAA,CAAA,UAAU;IAChD,gBAAgB,CAAC;IACjB,UAAU,CAAC;IACX,UAAU,CAAC;IACX,eAAe;QACX,OAAO;YACH,KAAK;YACL,MAAM;QACV;IACJ;IACA,kBAAkB;QACd,OAAO;IACX;IACA,eAAe,IAAI,EAAE;QACjB,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,IAAI,gBAAgB,MAAM;YACtB,OAAO,KAAK,CAAC,eAAe,KAAK,CAAC,IAAI,EAAE;QAC5C;IACJ;AACJ;AACO,MAAM,mBAAmB,uLAAA,CAAA,UAAU;IACtC,qBAAqB;QACjB,OAAO,CAAA,GAAA,+JAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,KAAK,CAAC,sBAAsB;YAC5C,iBAAiB,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC5C,gBAAgB,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC3C,gBAAgB,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC3C,iBAAiB,8KAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YAC5C,YAAY;YACZ,eAAe;YACf,iBAAiB;QACrB;IACJ;IACA,uBAAuB;QACnB,OAAO,KAAK,CAAC,uBAAuB,MAAM,CAAC;YAAC;gBACxC;oBACI,MAAM,aAAa,oJAAA,CAAA,UAAO,CAAC,IAAI;oBAC/B,OAAO,cAAc,WAAW,QAAQ;gBAC5C;gBACA,SAAS;oBACL,iBAAiB;gBACrB;YACJ;YAAG;gBACC,QAAQ,IAAM,CAAA,GAAA,iJAAA,CAAA,kBAAe,AAAD;gBAC5B,SAAS;oBACL,iBAAiB;oBACjB,gBAAgB;oBAChB,gBAAgB;oBAChB,iBAAiB;gBACrB;YACJ;SAAE;IACN;IACA,QAAQ;QACJ,KAAK,CAAC;QACN,IAAI,CAAC,wBAAwB,GAAG;IACpC;IACA,wBAAwB;QACpB,KAAK,CAAC;QACN,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,YAAY;QACjB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,iBAAiB;QACtB,IAAI,CAAC,cAAc;IACvB;IACA,eAAe;QACX,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACrC,IAAI,CAAC,SAAS,CAAC,SAAS,CAAC;IAC7B;IACA,iBAAiB;QACb,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACtC,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;QACvC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,WAAW;IAC3C;IACA,oBAAoB;QAChB,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC1C,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QACzC,MAAM,iBAAiB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC3C,MAAM,iBAAiB,IAAI,yJAAA,CAAA,UAAa,CAAC,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,QAAQ;QAC7D,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC;QAC7C,IAAI,CAAC,sBAAsB;QAC3B,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,eAAe,MAAM,CAAC,iBAAiB,MAAM,CAAC,IAAI,CAAC,iBAAiB;QAC9F,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;QAC7C,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc;IAC7C;IACA,iBAAiB;QACb,MAAM,oBAAoB,CAAA,GAAA,qJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,sBAAsB,QAAQ,CAAC,IAAI,CAAC,QAAQ;QAC1F,MAAM,EACF,gBAAgB,cAAc,EACjC,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,gBAAgB,CAAC,mBAAmB,wKAAA,CAAA,UAAS,EAAE;YAClE,SAAS;YACT,OAAO;YACP,SAAS;YACT,UAAU;gBACN,IAAI,IAAI,CAAC,QAAQ;YACrB;QACJ;IACJ;IACA,yBAAyB;QACrB,MAAM,EACF,iBAAiB,eAAe,EACnC,GAAG,IAAI,CAAC,MAAM;QACf,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;IAChC;IACA,kBAAkB;QACd,MAAM,EACF,WAAW,SAAS,EACpB,iBAAiB,eAAe,EACnC,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,eAAe,YAAY,kBAAkB;QACnD,MAAM,gBAAgB,iBAAiB,CAAC,aAAa;QACrD,IAAI,CAAC,SAAS,GAAG,IAAI,cAAc,IAAI;QACvC,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI;QACpE,IAAI,CAAC,SAAS,CAAC,gBAAgB,CAAC,GAAG,CAAC,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;QAClE,IAAI,CAAC,SAAS,CAAC,oBAAoB,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI;IAC9E;IACA,iBAAiB;QACb,KAAK,CAAC;QACN,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,qBAAqB,CAAC;QAClD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,qBAAqB,CAAC;QACrD,IAAI,CAAC,sBAAsB;IAC/B;IACA,yBAAyB;QACrB,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,qBAAqB,CAAC;QAChD,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,qBAAqB,CAAC;IACvD;IACA,GAAG,SAAS,EAAE;QACV,MAAM,SAAS,KAAK,CAAC,GAAG,KAAK,CAAC,IAAI,EAAE;QACpC,IAAI,eAAe,aAAa,kBAAkB,WAAW;YACzD,IAAI,CAAC,sBAAsB;QAC/B;QACA,OAAO;IACX;IACA,gBAAgB,OAAO,EAAE;QACrB,IAAI,MAAM,UAAU,MAAM,EAAE;YACxB,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA,IAAI,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,SAAS,EAAE;YACnC,IAAI,CAAC,UAAU,CAAC,MAAM,CAAC;YACvB,IAAI,CAAC,SAAS,CAAC,cAAc,CAAC;YAC9B,IAAI,CAAC,gBAAgB,GAAG;QAC5B;IACJ;IACA,mBAAmB,OAAO,EAAE;QACxB,IAAI,MAAM,UAAU,MAAM,EAAE;YACxB,OAAO,IAAI,CAAC,mBAAmB;QACnC;QACA,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,SAAS,EAAE;YACtC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;YAC1B,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC;YACjC,IAAI,CAAC,mBAAmB,GAAG;QAC/B;IACJ;IACA,mBAAmB;QACf,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,gBAAgB;IACzB;IACA,kBAAkB,KAAK,EAAE;QACrB,IAAI,UAAU,MAAM,GAAG,GAAG;YACtB,OAAO,IAAI,CAAC,wBAAwB;QACxC;QACA,IAAI,CAAC,wBAAwB,GAAG;IACpC;IACA,mBAAmB;QACf,IAAI;QACJ,IAAI,CAAC,YAAY;QACjB,SAAS,CAAC,wBAAwB,IAAI,CAAC,eAAe,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,IAAI;IAClI;IACA,sBAAsB;QAClB,IAAI,CAAC,iBAAiB,CAAC;QACvB,IAAI,CAAC,mBAAmB;IAC5B;IACA,sBAAsB;QAClB,IAAI;QACJ,IAAI,CAAC,YAAY;QACjB,SAAS,CAAC,wBAAwB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC,IAAI;IACrI;IACA,kBAAkB;QACd,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,iBAAiB,CAAC;IAC3B;IACA,eAAe,IAAI,EAAE;QACjB,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,cAAc;gBACnB;YACJ,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW;gBAChB;YACJ,KAAK;gBACD,IAAI,CAAC,sBAAsB;gBAC3B;YACJ;gBACI,KAAK,CAAC,eAAe;QAC7B;IACJ;IACA,UAAU;QACN,OAAO,CAAA,GAAA,uKAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,CAAC;IACzD;IACA,QAAQ,kBAAkB,EAAE;QACxB,IAAI,KAAK,MAAM,oBAAoB;YAC/B,IAAI,CAAC,aAAa,CAAC,CAAC;QACxB;QACA,OAAO,IAAI,CAAC,SAAS,CAAC,OAAO;IACjC;IACA,cAAc,UAAU,EAAE;QACtB,IAAI,CAAC,kBAAkB,CAAC;IAC5B;IACA,UAAU;QACN,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,eAAe;YAC3C;QACJ;QACA,IAAI,CAAC,SAAS,CAAC,cAAc;QAC7B,IAAI,CAAC,gBAAgB;IACzB;IACA,eAAe;QACX,IAAI,IAAI,CAAC,iBAAiB,MAAM,IAAI,CAAC,QAAQ,GAAG,EAAE,CAAC,aAAa;YAC5D,IAAI,CAAC,UAAU,CAAC,IAAI;QACxB;QACA,IAAI,CAAC,KAAK;IACd;IACA,gBAAgB;QACZ,IAAI,CAAC,UAAU,CAAC,IAAI;QACpB,IAAI,CAAC,OAAO;IAChB;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,SAAS,CAAC,eAAe;IACzC;IACA,WAAW;QACP,IAAI,CAAC,SAAS,CAAC,OAAO;QACtB,KAAK,CAAC;QACN,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG,MAAM;QACrC;IACJ;AACJ;AACA,CAAA,GAAA,kKAAA,CAAA,UAAiB,AAAD,EAAE,gBAAgB,eAAe,uBAAuB;uCACzD,eAAe,uBAAuB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3326, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/get_element_style.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/get_element_style.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    titleize\r\n} from \"../../../../core/utils/inflector\";\r\nimport {\r\n    getWindow,\r\n    hasWindow\r\n} from \"../../../../core/utils/window\";\r\nimport {\r\n    toNumber\r\n} from \"../../../utils/type_conversion\";\r\nexport function getElementStyle(el) {\r\n    var _getWindow$getCompute, _getWindow;\r\n    return el && hasWindow() ? null === (_getWindow$getCompute = (_getWindow = getWindow()).getComputedStyle) || void 0 === _getWindow$getCompute ? void 0 : _getWindow$getCompute.call(_getWindow, el) : null\r\n}\r\nexport function getElementMargin(element, side) {\r\n    const style = getElementStyle(element);\r\n    return style ? toNumber(style[`margin${titleize(side)}`]) : 0\r\n}\r\nexport function getElementPadding(element, side) {\r\n    const style = getElementStyle(element);\r\n    return style ? toNumber(style[`padding${titleize(side)}`]) : 0\r\n}\r\nexport function getElementOverflowX(element) {\r\n    const style = getElementStyle(element);\r\n    return style ? style.overflowX : \"visible\"\r\n}\r\nexport function getElementOverflowY(element) {\r\n    const style = getElementStyle(element);\r\n    return style ? style.overflowY : \"visible\"\r\n}\r\nexport function getElementTransform(element) {\r\n    const style = getElementStyle(element);\r\n    return style ? style.transform : \"\"\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;AACD;AAAA;AAGA;AAAA;AAIA;;;;AAGO,SAAS,gBAAgB,EAAE;IAC9B,IAAI,uBAAuB;IAC3B,OAAO,MAAM,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,MAAM,SAAS,CAAC,wBAAwB,CAAC,aAAa,CAAA,GAAA,+KAAA,CAAA,YAAS,AAAD,GAAG,EAAE,gBAAgB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,IAAI,CAAC,YAAY,MAAM;AAC1M;AACO,SAAS,iBAAiB,OAAO,EAAE,IAAI;IAC1C,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,QAAQ,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,CAAC,MAAM,EAAE,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,IAAI;AAChE;AACO,SAAS,kBAAkB,OAAO,EAAE,IAAI;IAC3C,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,QAAQ,CAAA,GAAA,8KAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,CAAC,CAAC,OAAO,EAAE,CAAA,GAAA,kLAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,IAAI;AACjE;AACO,SAAS,oBAAoB,OAAO;IACvC,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,QAAQ,MAAM,SAAS,GAAG;AACrC;AACO,SAAS,oBAAoB,OAAO;IACvC,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,QAAQ,MAAM,SAAS,GAAG;AACrC;AACO,SAAS,oBAAoB,OAAO;IACvC,MAAM,QAAQ,gBAAgB;IAC9B,OAAO,QAAQ,MAAM,SAAS,GAAG;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3377, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/scroll_direction.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/scroll_direction.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    DIRECTION_BOTH,\r\n    DIRECTION_HORIZONTAL,\r\n    DIRECTION_VERTICAL\r\n} from \"../consts\";\r\nexport class ScrollDirection {\r\n    constructor(direction) {\r\n        this.DIRECTION_HORIZONTAL = \"horizontal\";\r\n        this.DIRECTION_VERTICAL = \"vertical\";\r\n        this.DIRECTION_BOTH = \"both\";\r\n        this.direction = direction ?? DIRECTION_VERTICAL\r\n    }\r\n    get isHorizontal() {\r\n        return this.direction === DIRECTION_HORIZONTAL || this.direction === DIRECTION_BOTH\r\n    }\r\n    get isVertical() {\r\n        return this.direction === DIRECTION_VERTICAL || this.direction === DIRECTION_BOTH\r\n    }\r\n    get isBoth() {\r\n        return this.direction === DIRECTION_BOTH\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AAKO,MAAM;IACT,YAAY,SAAS,CAAE;QACnB,IAAI,CAAC,oBAAoB,GAAG;QAC5B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,SAAS,GAAG,aAAa,iLAAA,CAAA,qBAAkB;IACpD;IACA,IAAI,eAAe;QACf,OAAO,IAAI,CAAC,SAAS,KAAK,iLAAA,CAAA,uBAAoB,IAAI,IAAI,CAAC,SAAS,KAAK,iLAAA,CAAA,iBAAc;IACvF;IACA,IAAI,aAAa;QACb,OAAO,IAAI,CAAC,SAAS,KAAK,iLAAA,CAAA,qBAAkB,IAAI,IAAI,CAAC,SAAS,KAAK,iLAAA,CAAA,iBAAc;IACrF;IACA,IAAI,SAAS;QACT,OAAO,IAAI,CAAC,SAAS,KAAK,iLAAA,CAAA,iBAAc;IAC5C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3410, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/convert_location.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/convert_location.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    ensureDefined\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    isDefined,\r\n    isPlainObject\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    ScrollDirection\r\n} from \"./scroll_direction\";\r\nexport function convertToLocation(location, direction) {\r\n    if (isPlainObject(location)) {\r\n        const left = ensureDefined(location.left, location.x);\r\n        const top = ensureDefined(location.top, location.y);\r\n        return {\r\n            left: isDefined(left) ? left : void 0,\r\n            top: isDefined(top) ? top : void 0\r\n        }\r\n    }\r\n    const {\r\n        isVertical: isVertical,\r\n        isHorizontal: isHorizontal\r\n    } = new ScrollDirection(direction);\r\n    return {\r\n        left: isHorizontal && isDefined(location) ? location : void 0,\r\n        top: isVertical && isDefined(location) ? location : void 0\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AAIA;;;;AAGO,SAAS,kBAAkB,QAAQ,EAAE,SAAS;IACjD,IAAI,CAAA,GAAA,6KAAA,CAAA,gBAAa,AAAD,EAAE,WAAW;QACzB,MAAM,OAAO,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,IAAI,EAAE,SAAS,CAAC;QACpD,MAAM,MAAM,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,GAAG,EAAE,SAAS,CAAC;QAClD,OAAO;YACH,MAAM,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,OAAO,KAAK;YACpC,KAAK,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,KAAK;QACrC;IACJ;IACA,MAAM,EACF,YAAY,UAAU,EACtB,cAAc,YAAY,EAC7B,GAAG,IAAI,oMAAA,CAAA,kBAAe,CAAC;IACxB,OAAO;QACH,MAAM,gBAAgB,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,WAAW,KAAK;QAC5D,KAAK,cAAc,CAAA,GAAA,6KAAA,CAAA,YAAS,AAAD,EAAE,YAAY,WAAW,KAAK;IAC7D;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3447, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/get_scroll_left_max.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/get_scroll_left_max.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport function getScrollLeftMax(element) {\r\n    return element.scrollWidth - element.clientWidth\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,iBAAiB,OAAO;IACpC,OAAO,QAAQ,WAAW,GAAG,QAAQ,WAAW;AACpD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3464, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/get_scroll_top_max.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/get_scroll_top_max.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport function getScrollTopMax(element) {\r\n    return element.scrollHeight - element.clientHeight\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACM,SAAS,gBAAgB,OAAO;IACnC,OAAO,QAAQ,YAAY,GAAG,QAAQ,YAAY;AACtD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3481, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/ui/scroll_view/utils/get_boundary_props.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/ui/scroll_view/utils/get_boundary_props.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    getScrollLeftMax\r\n} from \"./get_scroll_left_max\";\r\nimport {\r\n    getScrollTopMax\r\n} from \"./get_scroll_top_max\";\r\nimport {\r\n    ScrollDirection\r\n} from \"./scroll_direction\";\r\nexport function isReachedLeft(scrollOffsetLeft, epsilon) {\r\n    return Math.round(scrollOffsetLeft) <= epsilon\r\n}\r\nexport function isReachedRight(element, scrollOffsetLeft, epsilon) {\r\n    return Math.round(getScrollLeftMax(element) - scrollOffsetLeft) <= epsilon\r\n}\r\nexport function isReachedTop(scrollOffsetTop, epsilon) {\r\n    return Math.round(scrollOffsetTop) <= epsilon\r\n}\r\nexport function isReachedBottom(element, scrollOffsetTop, pocketHeight, epsilon) {\r\n    return Math.round(getScrollTopMax(element) - scrollOffsetTop - pocketHeight) <= epsilon\r\n}\r\nexport function getBoundaryProps(direction, scrollOffset, element) {\r\n    let pocketHeight = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : 0;\r\n    const {\r\n        left: left,\r\n        top: top\r\n    } = scrollOffset;\r\n    const boundaryProps = {};\r\n    const {\r\n        isHorizontal: isHorizontal,\r\n        isVertical: isVertical\r\n    } = new ScrollDirection(direction);\r\n    if (isHorizontal) {\r\n        boundaryProps.reachedLeft = isReachedLeft(left, 0);\r\n        boundaryProps.reachedRight = isReachedRight(element, left, 0)\r\n    }\r\n    if (isVertical) {\r\n        boundaryProps.reachedTop = isReachedTop(top, 0);\r\n        boundaryProps.reachedBottom = isReachedBottom(element, top, pocketHeight, 0)\r\n    }\r\n    return boundaryProps\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;AACD;AAGA;AAGA;;;;AAGO,SAAS,cAAc,gBAAgB,EAAE,OAAO;IACnD,OAAO,KAAK,KAAK,CAAC,qBAAqB;AAC3C;AACO,SAAS,eAAe,OAAO,EAAE,gBAAgB,EAAE,OAAO;IAC7D,OAAO,KAAK,KAAK,CAAC,CAAA,GAAA,uMAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW,qBAAqB;AACvE;AACO,SAAS,aAAa,eAAe,EAAE,OAAO;IACjD,OAAO,KAAK,KAAK,CAAC,oBAAoB;AAC1C;AACO,SAAS,gBAAgB,OAAO,EAAE,eAAe,EAAE,YAAY,EAAE,OAAO;IAC3E,OAAO,KAAK,KAAK,CAAC,CAAA,GAAA,sMAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,kBAAkB,iBAAiB;AACpF;AACO,SAAS,iBAAiB,SAAS,EAAE,YAAY,EAAE,OAAO;IAC7D,IAAI,eAAe,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;IACpF,MAAM,EACF,MAAM,IAAI,EACV,KAAK,GAAG,EACX,GAAG;IACJ,MAAM,gBAAgB,CAAC;IACvB,MAAM,EACF,cAAc,YAAY,EAC1B,YAAY,UAAU,EACzB,GAAG,IAAI,oMAAA,CAAA,kBAAe,CAAC;IACxB,IAAI,cAAc;QACd,cAAc,WAAW,GAAG,cAAc,MAAM;QAChD,cAAc,YAAY,GAAG,eAAe,SAAS,MAAM;IAC/D;IACA,IAAI,YAAY;QACZ,cAAc,UAAU,GAAG,aAAa,KAAK;QAC7C,cAAc,aAAa,GAAG,gBAAgB,SAAS,KAAK,cAAc;IAC9E;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}]}