﻿<UserControl x:Class="omsnext.wpf.AdminDashboardUserControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:dxg="http://schemas.devexpress.com/winfx/2008/xaml/grid"
             xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
             mc:Ignorable="d" 
             d:DesignHeight="700" d:DesignWidth="1200">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- Fejlec -->
        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="15" VerticalAlignment="Center">
            <TextBlock Text="Admin Dashboard - Jelszó Visszaállítási Kérelmek" 
                     FontSize="24" 
                     FontWeight="Bold" 
                     VerticalAlignment="Center" 
                     Margin="0,0,20,0"/>
            
            <Button x:Name="RefreshButton" 
                  Content="🔄 Frissítés" 
                  Click="RefreshButton_Click" 
                  Padding="12,8" 
                  Margin="10,0"/>
            
            <ComboBox x:Name="StatusFilterComboBox" 
                    Width="150" 
                    VerticalAlignment="Center" 
                    Margin="10,0"
                    SelectionChanged="StatusFilter_SelectionChanged">
                <ComboBoxItem Content="Minden állapot" Tag=""/>
                <ComboBoxItem Content="Függőben" Tag="Pending" IsSelected="True"/>
                <ComboBoxItem Content="Jóváhagyott" Tag="Approved"/>
                <ComboBoxItem Content="Elutasított" Tag="Rejected"/>
                <ComboBoxItem Content="Befejezett" Tag="Completed"/>
            </ComboBox>
        </StackPanel>

        <!-- Jelszó visszaállítási kérelmek grid -->
        <dxg:GridControl x:Name="RequestsGrid" Grid.Row="1" Margin="15">
            <dxg:GridControl.Columns>
                <dxg:GridColumn FieldName="Id" Header="ID" Width="60" AllowSorting="True"/>
                <dxg:GridColumn FieldName="UserEmail" Header="Felhasználó E-mail" Width="200" AllowSorting="True"/>
                <dxg:GridColumn FieldName="UserDisplayName" Header="Felhasználó Neve" Width="180" AllowSorting="True"/>
                <dxg:GridColumn FieldName="RequestDate" Header="Kérelem Dátuma" Width="140" AllowSorting="True"/>
                <dxg:GridColumn FieldName="Status" Header="Állapot" Width="100" AllowSorting="True">
                    <dxg:GridColumn.CellTemplate>
                        <DataTemplate>
                            <Border CornerRadius="12" Padding="8,4" HorizontalAlignment="Center">
                                <Border.Style>
                                    <Style TargetType="Border">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding Value}" Value="Pending">
                                                <Setter Property="Background" Value="#FEF3C7"/>
                                                <Setter Property="BorderBrush" Value="#F59E0B"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Approved">
                                                <Setter Property="Background" Value="#D1FAE5"/>
                                                <Setter Property="BorderBrush" Value="#10B981"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Rejected">
                                                <Setter Property="Background" Value="#FEE2E2"/>
                                                <Setter Property="BorderBrush" Value="#EF4444"/>
                                            </DataTrigger>
                                            <DataTrigger Binding="{Binding Value}" Value="Completed">
                                                <Setter Property="Background" Value="#E0E7FF"/>
                                                <Setter Property="BorderBrush" Value="#6366F1"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </Border.Style>
                                <TextBlock Text="{Binding Value}" 
                                         FontWeight="SemiBold" 
                                         FontSize="11"
                                         HorizontalAlignment="Center"/>
                            </Border>
                        </DataTemplate>
                    </dxg:GridColumn.CellTemplate>
                </dxg:GridColumn>
                <dxg:GridColumn FieldName="ProcessedByAdminName" Header="Feldolgozó Admin" Width="150" AllowSorting="True"/>
                <dxg:GridColumn FieldName="ProcessedDate" Header="Feldolgozás Dátuma" Width="140" AllowSorting="True"/>
                <dxg:GridColumn FieldName="RequestSource" Header="Forrás" Width="80" AllowSorting="True"/>
                <dxg:GridColumn Header="Műveletek" Width="200" AllowSorting="False">
                    <dxg:GridColumn.CellTemplate>
                        <DataTemplate>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="✅ Jóváhagy" 
                                      Margin="2"
                                      Padding="8,4"
                                      FontSize="10"
                                      Background="#10B981"
                                      Foreground="White"
                                      BorderThickness="0"
                                      Click="ApproveButton_Click"
                                      Tag="{Binding Row}">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Pending">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Approved">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Rejected">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Completed">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                                
                                <Button Content="❌ Elutasít" 
                                      Margin="2"
                                      Padding="8,4"
                                      FontSize="10"
                                      Background="#EF4444"
                                      Foreground="White"
                                      BorderThickness="0"
                                      Click="RejectButton_Click"
                                      Tag="{Binding Row}">
                                    <Button.Style>
                                        <Style TargetType="Button">
                                            <Style.Triggers>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Pending">
                                                    <Setter Property="Visibility" Value="Visible"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Approved">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Rejected">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                                <DataTrigger Binding="{Binding Row.Status}" Value="Completed">
                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                </DataTrigger>
                                            </Style.Triggers>
                                        </Style>
                                    </Button.Style>
                                </Button>
                                
                                <Button Content="📝 Megtekint" 
                                      Margin="2"
                                      Padding="8,4"
                                      FontSize="10"
                                      Background="#6366F1"
                                      Foreground="White"
                                      BorderThickness="0"
                                      Click="ViewButton_Click"
                                      Tag="{Binding Row}"/>
                            </StackPanel>
                        </DataTemplate>
                    </dxg:GridColumn.CellTemplate>
                </dxg:GridColumn>
            </dxg:GridControl.Columns>
            
            <dxg:GridControl.View>
                <dxg:TableView x:Name="TableView" 
                             AllowSorting="True" 
                             AllowGrouping="True" 
                             AllowColumnFiltering="True"
                             ShowGroupPanel="False"
                             AutoWidth="True"
                             NavigationStyle="Row"
                             ShowVerticalLines="False"
                             ShowHorizontalLines="True"
                             AllowPerPixelScrolling="True"/>
            </dxg:GridControl.View>
        </dxg:GridControl>

        <!-- Státusz sor -->
        <StatusBar Grid.Row="2" Height="30">
            <StatusBarItem>
                <TextBlock x:Name="StatusLabel" Text="Készen"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <StackPanel Orientation="Horizontal">
                    <TextBlock x:Name="RecordCountLabel" Text="Kérelmek: 0"/>
                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="15,0"/>
                    <TextBlock Text="🔴 Függőben:" Margin="5,0"/>
                    <TextBlock x:Name="PendingCountLabel" Text="0" FontWeight="Bold" Margin="0,0,15,0"/>
                    <TextBlock Text="🟢 Befejezett:" Margin="5,0"/>
                    <TextBlock x:Name="CompletedCountLabel" Text="0" FontWeight="Bold"/>
                </StackPanel>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</UserControl>