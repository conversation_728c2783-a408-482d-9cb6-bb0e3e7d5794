﻿using System.Collections.Generic;
using DevExpress.Xpo;

namespace omsnext.shared.Models
{
    public class Role : XPObject
    {
        public Role(Session session) : base(session) { }
        public string Name { get; set; }
        [Association("Role-Permissions")]
        public XPCollection<Permission> Permissions => GetCollection<Permission>(nameof(Permissions));
        [Association("User-Roles")]
        public XPCollection<User> Users => GetCollection<User>(nameof(Users));
    }

    public class Permission : XPObject
    {
        public Permission(Session session) : base(session) { }
        public string Name { get; set; }
        [Association("Role-Permissions")]
        public XPCollection<Role> Roles => GetCollection<Role>(nameof(Roles));
    }
}
