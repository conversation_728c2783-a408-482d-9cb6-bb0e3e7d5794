{"Version": 1, "WorkspaceRootPath": "C:\\Users\\<USER>\\source\\repos\\omsnext\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\requestdetaildialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\requestdetaildialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\admindashboardusercontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\admindashboardusercontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\adminnotesdialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\adminnotesdialog.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|solutionrelative:omsnext.api\\controllers\\authcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\dto\\logindto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|solutionrelative:omsnext.shared\\dto\\logindto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\dto\\ktforgevdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|solutionrelative:omsnext.shared\\dto\\ktforgevdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\controllers\\ktforgevcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|solutionrelative:omsnext.api\\controllers\\ktforgevcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\controllers\\ktforgevwpfcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|solutionrelative:omsnext.api\\controllers\\ktforgevwpfcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\requestdetaildialog.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\requestdetaildialog.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\adminnotesdialog.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\adminnotesdialog.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\adminnotesdialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\adminnotesdialog.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\admindashboardusercontrol.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\admindashboardusercontrol.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\admindashboardusercontrol.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\admindashboardusercontrol.xaml||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}, {"AbsoluteMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\dto\\passwordresetnotificationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|solutionrelative:omsnext.shared\\dto\\passwordresetnotificationdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\loginwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\loginwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\ktforgevusercontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\ktforgevusercontrol.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}, {"AbsoluteMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\models\\ktforgev.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|solutionrelative:omsnext.shared\\models\\ktforgev.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{30034FC9-763C-446C-8C43-96C8B0FC45A9}|omsnext.api\\omsnext.api.csproj|solutionrelative:omsnext.api\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\services\\apiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\services\\apiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\ktforgevusercontrol.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\ktforgevusercontrol.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\loginwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{83A19039-3757-40A6-813A-17A0C42F271E}|omsnext.wpf\\omsnext.wpf.csproj|solutionrelative:omsnext.wpf\\loginwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\models\\passwordresetrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|solutionrelative:omsnext.shared\\models\\passwordresetrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|c:\\users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\dto\\processpasswordresetdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{A7819686-141F-4DA8-887A-F92578E75408}|omsnext.shared\\omsnext.shared.csproj|solutionrelative:omsnext.shared\\dto\\processpasswordresetdto.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 7, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "AdminDashboardUserControl.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminDashboardUserControl.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\AdminDashboardUserControl.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminDashboardUserControl.xaml", "RelativeToolTip": "omsnext.wpf\\AdminDashboardUserControl.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-07T10:10:00.799Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "AdminNotesDialog.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminNotesDialog.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\AdminNotesDialog.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminNotesDialog.xaml", "RelativeToolTip": "omsnext.wpf\\AdminNotesDialog.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-07T10:09:56.027Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "LoginDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\LoginDto.cs", "RelativeDocumentMoniker": "omsnext.shared\\DTO\\LoginDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\LoginDto.cs", "RelativeToolTip": "omsnext.shared\\DTO\\LoginDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA0AAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T09:45:59.365Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "KtforgevDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\KtforgevDto.cs", "RelativeDocumentMoniker": "omsnext.shared\\DTO\\KtforgevDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\KtforgevDto.cs", "RelativeToolTip": "omsnext.shared\\DTO\\KtforgevDto.cs", "ViewState": "AgIAAAEAAAAAAAAAAAAawBMAAAAWAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T09:10:59.802Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 7, "Title": "KtforgevWpfController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Controllers\\KtforgevWpfController.cs", "RelativeDocumentMoniker": "omsnext.api\\Controllers\\KtforgevWpfController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Controllers\\KtforgevWpfController.cs", "RelativeToolTip": "omsnext.api\\Controllers\\KtforgevWpfController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:10:14.637Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "KtforgevController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Controllers\\KtforgevController.cs", "RelativeDocumentMoniker": "omsnext.api\\Controllers\\KtforgevController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Controllers\\KtforgevController.cs", "RelativeToolTip": "omsnext.api\\Controllers\\KtforgevController.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAFwAAAALAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T21:09:20.868Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "AuthController.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Controllers\\AuthController.cs", "RelativeDocumentMoniker": "omsnext.api\\Controllers\\AuthController.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Controllers\\AuthController.cs", "RelativeToolTip": "omsnext.api\\Controllers\\AuthController.cs", "ViewState": "AgIAAD8AAAAAAAAAAADwv0QAAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T21:32:35.035Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "RequestDetailDialog.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\RequestDetailDialog.xaml.cs", "RelativeDocumentMoniker": "omsnext.wpf\\RequestDetailDialog.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\RequestDetailDialog.xaml.cs", "RelativeToolTip": "omsnext.wpf\\RequestDetailDialog.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAMAAAAZAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:47:58.319Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 8, "Title": "RequestDetailDialog.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\RequestDetailDialog.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\RequestDetailDialog.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\RequestDetailDialog.xaml", "RelativeToolTip": "omsnext.wpf\\RequestDetailDialog.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-07T08:47:34.869Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 10, "Title": "AdminNotesDialog.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminNotesDialog.xaml.cs", "RelativeDocumentMoniker": "omsnext.wpf\\AdminNotesDialog.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminNotesDialog.xaml.cs", "RelativeToolTip": "omsnext.wpf\\AdminNotesDialog.xaml.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADgAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:46:55.997Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 9, "Title": "AdminNotesDialog.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminNotesDialog.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\AdminNotesDialog.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminNotesDialog.xaml", "RelativeToolTip": "omsnext.wpf\\AdminNotesDialog.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAHQAAAASAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-07T08:46:26.489Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 11, "Title": "AdminDashboardUserControl.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminDashboardUserControl.xaml.cs", "RelativeDocumentMoniker": "omsnext.wpf\\AdminDashboardUserControl.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminDashboardUserControl.xaml.cs", "RelativeToolTip": "omsnext.wpf\\AdminDashboardUserControl.xaml.cs", "ViewState": "AgIAACABAAAAAAAAAADwvz0BAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:35:07.748Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 13, "Title": "AdminDashboardUserControl.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminDashboardUserControl.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\AdminDashboardUserControl.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\AdminDashboardUserControl.xaml", "RelativeToolTip": "omsnext.wpf\\AdminDashboardUserControl.xaml", "ViewState": "AgIAAAAAAAAAAAAAAAAAAEkBAAAOAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-07T08:34:36.722Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 14, "Title": "PasswordResetNotificationDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\PasswordResetNotificationDto.cs", "RelativeDocumentMoniker": "omsnext.shared\\DTO\\PasswordResetNotificationDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\PasswordResetNotificationDto.cs", "RelativeToolTip": "omsnext.shared\\DTO\\PasswordResetNotificationDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAA4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:23:12.164Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 23, "Title": "PasswordResetRequest.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\Models\\PasswordResetRequest.cs", "RelativeDocumentMoniker": "omsnext.shared\\Models\\PasswordResetRequest.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\Models\\PasswordResetRequest.cs", "RelativeToolTip": "omsnext.shared\\Models\\PasswordResetRequest.cs", "ViewState": "AgIAAC0AAAAAAAAAAABBwC4AAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:32:43.801Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 24, "Title": "ProcessPasswordResetDto.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\ProcessPasswordResetDto.cs", "RelativeDocumentMoniker": "omsnext.shared\\DTO\\ProcessPasswordResetDto.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\DTO\\ProcessPasswordResetDto.cs", "RelativeToolTip": "omsnext.shared\\DTO\\ProcessPasswordResetDto.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAABsAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T08:33:56.254Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 22, "Title": "LoginWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\LoginWindow.xaml.cs", "RelativeDocumentMoniker": "omsnext.wpf\\LoginWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\LoginWindow.xaml.cs", "RelativeToolTip": "omsnext.wpf\\LoginWindow.xaml.cs", "ViewState": "AgIAAPsAAAAAAAAAAABBwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-07T07:33:10.818Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 15, "Title": "LoginWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\LoginWindow.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\LoginWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\LoginWindow.xaml", "RelativeToolTip": "omsnext.wpf\\LoginWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-07T07:29:31.376Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 17, "Title": "Ktforgev.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\Models\\Ktforgev.cs", "RelativeDocumentMoniker": "omsnext.shared\\Models\\Ktforgev.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.shared\\Models\\Ktforgev.cs", "RelativeToolTip": "omsnext.shared\\Models\\Ktforgev.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAsAAAAFAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T21:23:46.661Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 18, "Title": "Program.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Program.cs", "RelativeDocumentMoniker": "omsnext.api\\Program.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.api\\Program.cs", "RelativeToolTip": "omsnext.api\\Program.cs", "ViewState": "AgIAAAMAAAAAAAAAAAAAADEAAAAHAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T21:19:59.579Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 12, "Title": "MainWindow.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\MainWindow.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\MainWindow.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\MainWindow.xaml", "RelativeToolTip": "omsnext.wpf\\MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-06T19:28:48.791Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 16, "Title": "KtforgevUserControl.xaml", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\KtforgevUserControl.xaml", "RelativeDocumentMoniker": "omsnext.wpf\\KtforgevUserControl.xaml", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\KtforgevUserControl.xaml", "RelativeToolTip": "omsnext.wpf\\KtforgevUserControl.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-08-06T20:05:58.723Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 20, "Title": "KtforgevUserControl.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\KtforgevUserControl.xaml.cs", "RelativeDocumentMoniker": "omsnext.wpf\\KtforgevUserControl.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\KtforgevUserControl.xaml.cs", "RelativeToolTip": "omsnext.wpf\\KtforgevUserControl.xaml.cs", "ViewState": "AgIAAEkAAAAAAAAAAAAkwNEAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T19:56:43.068Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 21, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "omsnext.wpf\\MainWindow.xaml.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\MainWindow.xaml.cs", "RelativeToolTip": "omsnext.wpf\\MainWindow.xaml.cs", "ViewState": "AgIAAHYAAAAAAAAAAABBwAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T19:28:04.866Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 19, "Title": "ApiClient.cs", "DocumentMoniker": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\Services\\ApiClient.cs", "RelativeDocumentMoniker": "omsnext.wpf\\Services\\ApiClient.cs", "ToolTip": "C:\\Users\\<USER>\\source\\repos\\omsnext\\omsnext.wpf\\Services\\ApiClient.cs", "RelativeToolTip": "omsnext.wpf\\Services\\ApiClient.cs", "ViewState": "AgIAAMEAAAAAAAAAAAAAwEEAAAAQAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-06T19:25:03.176Z", "EditorCaption": ""}]}]}]}