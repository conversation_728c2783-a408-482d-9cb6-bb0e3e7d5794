{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextResponse } from \"next/server\";\r\nimport type { NextRequest } from \"next/server\";\r\nimport { isAuthenticated } from \"@/lib/auth\";\r\n\r\n// Define which routes are protected\r\nconst protectedRoutes = [\"/dashboard\"];\r\nconst authRoutes = [\"/login\", \"/forgot-password\", \"/reset-password\"];\r\n\r\nexport async function middleware(request: NextRequest) {\r\n  const { pathname } = request.nextUrl;\r\n\r\n  // Check if the route is protected\r\n  const isProtectedRoute = protectedRoutes.some((route) =>\r\n    pathname.startsWith(route)\r\n  );\r\n  const isAuthRoute = authRoutes.some((route) => pathname.startsWith(route));\r\n\r\n  // For protected routes, check authentication\r\n  if (isProtectedRoute) {\r\n    // In middleware, we can't use cookies() directly, so we need to check the cookie manually\r\n    const token = request.cookies.get(\"oms_token\")?.value;\r\n\r\n    if (!token) {\r\n      // Redirect to login if not authenticated\r\n      const url = request.nextUrl.clone();\r\n      url.pathname = \"/login\";\r\n      return NextResponse.redirect(url);\r\n    }\r\n\r\n    // Here we would normally verify the token, but since we can't use jwtVerify in middleware\r\n    // without additional setup, we'll just check if the token exists\r\n    // In a production app, you'd want to verify the token properly\r\n  }\r\n\r\n  // If the user is already authenticated and trying to access auth routes, redirect to dashboard\r\n  if (isAuthRoute) {\r\n    const token = request.cookies.get(\"oms_token\")?.value;\r\n\r\n    if (token) {\r\n      const url = request.nextUrl.clone();\r\n      url.pathname = \"/dashboard\";\r\n      return NextResponse.redirect(url);\r\n    }\r\n  }\r\n\r\n  return NextResponse.next();\r\n}\r\n\r\n// Configure which paths the middleware should run on\r\nexport const config = {\r\n  matcher: [\r\n    /*\r\n     * Match all request paths except for the ones starting with:\r\n     * - api (API routes)\r\n     * - _next/static (static files)\r\n     * - _next/image (image optimization files)\r\n     * - favicon.ico (favicon file)\r\n     */\r\n    \"/((?!api|_next/static|_next/image|favicon.ico).*)\",\r\n  ],\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAIA,oCAAoC;AACpC,MAAM,kBAAkB;IAAC;CAAa;AACtC,MAAM,aAAa;IAAC;IAAU;IAAoB;CAAkB;AAE7D,eAAe,WAAW,OAAoB;IACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,QAAQ,OAAO;IAEpC,kCAAkC;IAClC,MAAM,mBAAmB,gBAAgB,IAAI,CAAC,CAAC,QAC7C,SAAS,UAAU,CAAC;IAEtB,MAAM,cAAc,WAAW,IAAI,CAAC,CAAC,QAAU,SAAS,UAAU,CAAC;IAEnE,6CAA6C;IAC7C,IAAI,kBAAkB;QACpB,0FAA0F;QAC1F,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;QAEhD,IAAI,CAAC,OAAO;YACV,yCAAyC;YACzC,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IAEA,0FAA0F;IAC1F,iEAAiE;IACjE,+DAA+D;IACjE;IAEA,+FAA+F;IAC/F,IAAI,aAAa;QACf,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,cAAc;QAEhD,IAAI,OAAO;YACT,MAAM,MAAM,QAAQ,OAAO,CAAC,KAAK;YACjC,IAAI,QAAQ,GAAG;YACf,OAAO,6LAAA,CAAA,eAAY,CAAC,QAAQ,CAAC;QAC/B;IACF;IAEA,OAAO,6LAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAGO,MAAM,SAAS;IACpB,SAAS;QACP;;;;;;KAMC,GACD;KACD;AACH"}}]}