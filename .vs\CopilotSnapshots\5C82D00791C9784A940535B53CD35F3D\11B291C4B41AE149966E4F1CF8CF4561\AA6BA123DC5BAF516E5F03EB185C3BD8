﻿<dx:ThemedWindow x:Class="omsnext.wpf.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon"
        xmlns:dxdo="http://schemas.devexpress.com/winfx/2008/xaml/docking"
        xmlns:dxa="http://schemas.devexpress.com/winfx/2008/xaml/accordion"
        xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars" 
        Title="OmsNext" 
        Height="800" Width="1200"
        WindowStartupLocation="CenterScreen"
        WindowState="Maximized">
    <Grid>
        <dxdo:DockLayoutManager>
            <dxdo:LayoutGroup Orientation="Vertical">
                <dxdo:LayoutPanel ItemHeight="Auto" ShowCaption="False" ShowBorder="False" AllowClose="False">
                    <dxr:RibbonControl>
                        <dxr:RibbonDefaultPageCategory>
                            <dxr:RibbonPage Caption="Kezdőlap">
                                <dxr:RibbonPageGroup Caption="Navigáció">
                                    <dxb:BarButtonItem Content="Dashboard" Glyph="{dx:DXImage SvgImages/Hybrid/Home.svg}" ItemClick="DashboardButton_Click"/>
                                </dxr:RibbonPageGroup>
                                <dxr:RibbonPageGroup Caption="Rendszer">
                                    <dxb:BarButtonItem Content="Kijelentkezés" Glyph="{dx:DXImage SvgImages/Business Objects/BO_Security_Permission.svg}" ItemClick="LogoutButton_Click"/>
                                </dxr:RibbonPageGroup>
                            </dxr:RibbonPage>
                        </dxr:RibbonDefaultPageCategory>
                    </dxr:RibbonControl>
                </dxdo:LayoutPanel>
                <dxdo:LayoutGroup>
                    <dxdo:LayoutPanel ItemWidth="250" Caption="Navigáció" AllowClose="False">
                        <dxa:AccordionControl SelectedItemChanged="AccordionControl_SelectedItemChanged">
                            <dxa:AccordionItem Header="Dashboard" Glyph="{dx:DXImage SvgImages/Hybrid/Home.svg}" Tag="Dashboard"/>
                            <dxa:AccordionItem Header="Felhasználók" Glyph="{dx:DXImage SvgImages/Business Objects/BO_User.svg}">
                                <dxa:AccordionItem Header="Felhasználó lista" Tag="UsersList"/>
                                <dxa:AccordionItem Header="Szerepkörök" Tag="Roles"/>
                            </dxa:AccordionItem>
                            <dxa:AccordionItem Header="Beállítások" Glyph="{dx:DXImage SvgImages/Hybrid/Settings.svg}" Tag="Settings"/>
                        </dxa:AccordionControl>
                    </dxdo:LayoutPanel>
                    <dxdo:DocumentGroup x:Name="DocumentGroup"/>
                </dxdo:LayoutGroup>
            </dxdo:LayoutGroup>
        </dxdo:DockLayoutManager>
    </Grid>
</dx:ThemedWindow>
