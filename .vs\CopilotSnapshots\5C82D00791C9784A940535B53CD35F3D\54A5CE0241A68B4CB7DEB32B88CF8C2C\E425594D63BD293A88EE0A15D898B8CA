﻿<dx:ThemedWindow x:Class="omsnext.wpf.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        Title="OmsNext - Bejelentkezés" 
        Height="600" Width="450"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        WindowStyle="None"
        AllowsTransparency="True"
        Background="Transparent">
    
    <Border CornerRadius="15" Background="White" Effect="{dx:ShadowEffect ShadowDepth='8' Opacity='0.3' BlurRadius='20'}">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="*"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- Főtartalom -->
            <StackPanel Grid.Row="0" Margin="60,50,60,40" VerticalAlignment="Center">
                
                <!-- Logo és címsor -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                    <Ellipse Width="80" Height="80" Margin="0,0,0,20">
                        <Ellipse.Fill>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#4F46E5" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Ellipse.Fill>
                    </Ellipse>
                    <TextBlock Text="OmsNext" FontSize="32" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1F2937"/>
                    <TextBlock Text="Üdvözöljük vissza!" FontSize="16" HorizontalAlignment="Center" Foreground="#6B7280" Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Login form -->
                <StackPanel x:Name="LoginPanel">
                    
                    <!-- Email mező -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="E-mail cím" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                        <Border CornerRadius="8" BorderBrush="#D1D5DB" BorderThickness="1" Background="#F9FAFB">
                            <dxe:TextEdit x:Name="EmailTextEdit" 
                                        NullText="<EMAIL>" 
                                        FontSize="14"
                                        Padding="12"
                                        BorderThickness="0"
                                        Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- Jelszó mező -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Jelszó" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                        <Border CornerRadius="8" BorderBrush="#D1D5DB" BorderThickness="1" Background="#F9FAFB">
                            <dxe:PasswordBoxEdit x:Name="PasswordBoxEdit" 
                                               NullText="Írja be a jelszavát" 
                                               FontSize="14"
                                               Padding="12"
                                               BorderThickness="0"
                                               Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- Elfelejtett jelszó link -->
                    <TextBlock HorizontalAlignment="Right" Margin="0,0,0,25">
                        <Hyperlink x:Name="ForgotPasswordLink" 
                                 Click="ForgotPasswordLink_Click"
                                 Foreground="#4F46E5" 
                                 TextDecorations="None"
                                 FontSize="14">
                            Elfelejtett jelszó?
                        </Hyperlink>
                    </TextBlock>

                    <!-- Bejelentkezés gomb -->
                    <Border CornerRadius="8" Margin="0,0,0,20">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#4F46E5" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <dx:SimpleButton x:Name="LoginButton" 
                                       Content="Bejelentkezés" 
                                       Height="48" 
                                       FontSize="16" 
                                       FontWeight="SemiBold"
                                       Foreground="White"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       Click="LoginButton_Click"/>
                    </Border>

                    <!-- Hibaüzenet -->
                    <Border x:Name="ErrorPanel" 
                          CornerRadius="8" 
                          Background="#FEE2E2" 
                          BorderBrush="#FCA5A5" 
                          BorderThickness="1" 
                          Padding="12"
                          Margin="0,0,0,20"
                          Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚠" Foreground="#DC2626" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock x:Name="ErrorMessage" 
                                     Foreground="#DC2626" 
                                     FontSize="14"
                                     TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- Loading indikátor -->
                    <dx:WaitIndicator x:Name="LoadingIndicator" 
                                    Content="Bejelentkezés..." 
                                    DeferedVisibility="False" 
                                    Margin="0,10,0,0"
                                    Visibility="Collapsed"/>
                </StackPanel>

                <!-- Elfelejtett jelszó panel (kezdetben rejtett) -->
                <StackPanel x:Name="ForgotPasswordPanel" Visibility="Collapsed">
                    
                    <!-- Vissza gomb -->
                    <Button x:Name="BackButton" 
                          HorizontalAlignment="Left" 
                          Click="BackButton_Click"
                          Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}"
                          Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="←" FontSize="16" Margin="0,0,5,0"/>
                            <TextBlock Text="Vissza"/>
                        </StackPanel>
                    </Button>

                    <TextBlock Text="Jelszó visszaállítás" 
                             FontSize="24" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center" 
                             Foreground="#1F2937"
                             Margin="0,0,0,10"/>
                    
                    <TextBlock Text="Adja meg e-mail címét és küldünk egy jelszó visszaállítási linket." 
                             FontSize="14" 
                             Foreground="#6B7280" 
                             TextAlignment="Center"
                             TextWrapping="Wrap"
                             Margin="0,0,0,30"/>

                    <!-- Email mező jelszó visszaállításhoz -->
                    <StackPanel Margin="0,0,0,25">
                        <TextBlock Text="E-mail cím" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,8"/>
                        <Border CornerRadius="8" BorderBrush="#D1D5DB" BorderThickness="1" Background="#F9FAFB">
                            <dxe:TextEdit x:Name="ResetEmailTextEdit" 
                                        NullText="<EMAIL>" 
                                        FontSize="14"
                                        Padding="12"
                                        BorderThickness="0"
                                        Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- Visszaállítás gomb -->
                    <Border CornerRadius="8" Margin="0,0,0,20">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#059669" Offset="0"/>
                                <GradientStop Color="#047857" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <dx:SimpleButton x:Name="ResetPasswordButton" 
                                       Content="Jelszó visszaállítási link küldése" 
                                       Height="48" 
                                       FontSize="16" 
                                       FontWeight="SemiBold"
                                       Foreground="White"
                                       Background="Transparent"
                                       BorderThickness="0"
                                       Click="ResetPasswordButton_Click"/>
                    </Border>

                    <!-- Sikerüzenet -->
                    <Border x:Name="SuccessPanel" 
                          CornerRadius="8" 
                          Background="#D1FAE5" 
                          BorderBrush="#86EFAC" 
                          BorderThickness="1" 
                          Padding="12"
                          Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✓" Foreground="#059669" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock x:Name="SuccessMessage" 
                                     Foreground="#059669" 
                                     FontSize="14"
                                     TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

            </StackPanel>

            <!-- Bezárás gomb -->
            <Button Grid.Row="0" 
                  x:Name="CloseButton"
                  HorizontalAlignment="Right" 
                  VerticalAlignment="Top"
                  Width="30" Height="30"
                  Margin="0,15,15,0"
                  Click="CloseButton_Click"
                  Style="{StaticResource {x:Static ToolBar.ButtonStyleKey}}"
                  Background="Transparent"
                  BorderThickness="0">
                <TextBlock Text="✕" FontSize="14" Foreground="#6B7280"/>
            </Button>

            <!-- Alsó dekorációs csík -->
            <Rectangle Grid.Row="1" Height="4">
                <Rectangle.Fill>
                    <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                        <GradientStop Color="#4F46E5" Offset="0"/>
                        <GradientStop Color="#7C3AED" Offset="0.5"/>
                        <GradientStop Color="#EC4899" Offset="1"/>
                    </LinearGradientBrush>
                </Rectangle.Fill>
            </Rectangle>

        </Grid>
    </Border>
</dx:ThemedWindow>