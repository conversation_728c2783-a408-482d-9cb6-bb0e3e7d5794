﻿<dx:ThemedWindow x:Class="omsnext.wpf.AdminNotesDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        Title="Admin Jegyzet" 
        Height="350" Width="500"
        WindowStartupLocation="CenterOwner"
        ResizeMode="CanResize"
        ShowInTaskbar="False">
    
    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <TextBlock x:Name="TitleLabel" 
                 Grid.Row="0"
                 Text="Kerelem elutasitasa" 
                 FontSize="18" 
                 FontWeight="Bold" 
                 Margin="0,0,0,15"/>
        
        <TextBlock x:Name="DescriptionLabel" 
                 Grid.Row="1"
                 Text="Kerlek, adja meg az elutasitas okat:" 
                 FontSize="14" 
                 Margin="0,0,0,10"/>
        
        <Border Grid.Row="2" 
              CornerRadius="8" 
              BorderBrush="#E2E8F0" 
              BorderThickness="2" 
              Background="#F7FAFC"
              Margin="0,0,0,20">
            <dxe:MemoEdit x:Name="NotesTextEdit" 
                        FontSize="14"
                        Padding="10"
                        BorderThickness="0"
                        Background="Transparent"
                        AcceptsReturn="True"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto"/>
        </Border>
        
        <StackPanel Grid.Row="3" 
                  Orientation="Horizontal" 
                  HorizontalAlignment="Right">
            
            <Button x:Name="CancelButton" 
                  Content="Megse" 
                  Width="80" 
                  Height="35"
                  Margin="0,0,10,0"
                  Click="CancelButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Background" Value="#F3F4F6"/>
                        <Setter Property="Foreground" Value="#374151"/>
                        <Setter Property="BorderBrush" Value="#D1D5DB"/>
                        <Setter Property="BorderThickness" Value="1"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                          BorderBrush="{TemplateBinding BorderBrush}" 
                                          BorderThickness="{TemplateBinding BorderThickness}" 
                                          CornerRadius="6">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#E5E7EB"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
            
            <Button x:Name="OkButton" 
                  Content="OK" 
                  Width="80" 
                  Height="35"
                  Click="OkButton_Click">
                <Button.Style>
                    <Style TargetType="Button">
                        <Setter Property="Background" Value="#3B82F6"/>
                        <Setter Property="Foreground" Value="White"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="Button">
                                    <Border Background="{TemplateBinding Background}" 
                                          BorderThickness="{TemplateBinding BorderThickness}" 
                                          CornerRadius="6">
                                        <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                    </Border>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                        <Style.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#2563EB"/>
                            </Trigger>
                        </Style.Triggers>
                    </Style>
                </Button.Style>
            </Button>
        </StackPanel>
    </Grid>
</dx:ThemedWindow>