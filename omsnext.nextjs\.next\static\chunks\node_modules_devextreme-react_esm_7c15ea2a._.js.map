{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/configuration/utils.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nexport function mergeNameParts(...args) {\n    return args.filter((value) => value).join('.');\n}\nexport function parseOptionName(name) {\n    const parts = name.split('[');\n    if (parts.length === 1) {\n        return {\n            isCollectionItem: false,\n            name,\n        };\n    }\n    return {\n        isCollectionItem: true,\n        name: parts[0],\n        index: Number(parts[1].slice(0, -1)),\n    };\n}\nexport const isIE = () => {\n    const ua = window?.navigator?.userAgent ?? ''; // Check the userAgent property of the window.navigator object\n    const msie = ua.indexOf('MSIE'); // IE 10 or older\n    const trident = ua.indexOf('Trident/'); // IE 11\n    return msie > 0 || trident > 0;\n};\nexport const shallowEquals = (first, second) => {\n    if (Object.keys(first).length !== Object.keys(second).length) {\n        return false;\n    }\n    return Object.keys(first).every((key) => first[key] === second[key]);\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;AAEM,SAAS;IAAe,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,OAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,KAAH,QAAA,SAAA,CAAA,KAAO;;IAClC,OAAO,KAAK,MAAM,CAAC,CAAC,QAAU,OAAO,IAAI,CAAC;AAC9C;AACO,SAAS,gBAAgB,IAAI;IAChC,MAAM,QAAQ,KAAK,KAAK,CAAC;IACzB,IAAI,MAAM,MAAM,KAAK,GAAG;QACpB,OAAO;YACH,kBAAkB;YAClB;QACJ;IACJ;IACA,OAAO;QACH,kBAAkB;QAClB,MAAM,KAAK,CAAC,EAAE;QACd,OAAO,OAAO,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC;IACrC;AACJ;AACO,MAAM,OAAO;QACL,mBAAA;QAAA;IAAX,MAAM,KAAK,CAAA,+BAAA,UAAA,oBAAA,+BAAA,oBAAA,QAAQ,SAAS,cAAjB,wCAAA,kBAAmB,SAAS,cAA5B,yCAAA,8BAAgC,IAAI,8DAA8D;IAC7G,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,iBAAiB;IAClD,MAAM,UAAU,GAAG,OAAO,CAAC,aAAa,QAAQ;IAChD,OAAO,OAAO,KAAK,UAAU;AACjC;AACO,MAAM,gBAAgB,CAAC,OAAO;IACjC,IAAI,OAAO,IAAI,CAAC,OAAO,MAAM,KAAK,OAAO,IAAI,CAAC,QAAQ,MAAM,EAAE;QAC1D,OAAO;IACX;IACA,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,CAAC,CAAC,MAAQ,KAAK,CAAC,IAAI,KAAK,MAAM,CAAC,IAAI;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/widget-config.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nconst elementPropNames = ['style', 'id'];\nconst classNamePropName = 'className';\nconst refPropName = ['dropZone', 'dialogTrigger'];\nconst internalProps = {\n    WidgetClass: {},\n    isPortalComponent: false,\n    defaults: {},\n    templateProps: [],\n    expectedChildren: {},\n    subscribableOptions: [],\n    independentEvents: [],\n    useRequestAnimationFrameFlag: false,\n    clearExtensions: () => undefined,\n    renderChildren: () => undefined,\n    beforeCreateWidget: () => undefined,\n    afterCreateWidget: () => undefined,\n    children: null,\n};\nfunction isIgnoredProp(name) {\n    return name === 'children'\n        || name === classNamePropName\n        || elementPropNames.includes(name)\n        || Object.prototype.hasOwnProperty.call(internalProps, name);\n}\nfunction getRefElement(value) {\n    if (value?.current) {\n        if (value.current.instance?.().element()) {\n            return value.current.instance().element();\n        }\n        return value.current;\n    }\n    return value;\n}\nfunction separateProps(props, defaultsProps, templateProps) {\n    templateProps = templateProps || [];\n    const defaults = {};\n    const options = {};\n    const templates = {};\n    const knownTemplates = {};\n    templateProps.forEach((value) => {\n        knownTemplates[value.component] = true;\n        knownTemplates[value.render] = true;\n    });\n    Object.keys(props).forEach((key) => {\n        const defaultOptionName = defaultsProps ? defaultsProps[key] : null;\n        const value = props[key];\n        if (isIgnoredProp(key)) {\n            return;\n        }\n        if (defaultOptionName) {\n            defaults[defaultOptionName] = value;\n            return;\n        }\n        if (knownTemplates[key]) {\n            templates[key] = value;\n            return;\n        }\n        if (refPropName.includes(key)) {\n            options[key] = getRefElement(value);\n            return;\n        }\n        options[key] = props[key];\n    });\n    return { options, defaults, templates };\n}\nfunction getClassName(props) {\n    return props[classNamePropName];\n}\nexport { elementPropNames, getClassName, separateProps, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;AAED,MAAM,mBAAmB;IAAC;IAAS;CAAK;AACxC,MAAM,oBAAoB;AAC1B,MAAM,cAAc;IAAC;IAAY;CAAgB;AACjD,MAAM,gBAAgB;IAClB,aAAa,CAAC;IACd,mBAAmB;IACnB,UAAU,CAAC;IACX,eAAe,EAAE;IACjB,kBAAkB,CAAC;IACnB,qBAAqB,EAAE;IACvB,mBAAmB,EAAE;IACrB,8BAA8B;IAC9B,iBAAiB,IAAM;IACvB,gBAAgB,IAAM;IACtB,oBAAoB,IAAM;IAC1B,mBAAmB,IAAM;IACzB,UAAU;AACd;AACA,SAAS,cAAc,IAAI;IACvB,OAAO,SAAS,cACT,SAAS,qBACT,iBAAiB,QAAQ,CAAC,SAC1B,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe;AAC/D;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,kBAAA,4BAAA,MAAO,OAAO,EAAE;YACZ,yBAAA;QAAJ,KAAI,0BAAA,CAAA,iBAAA,MAAM,OAAO,EAAC,QAAQ,cAAtB,8CAAA,6BAAA,gBAA2B,OAAO,IAAI;YACtC,OAAO,MAAM,OAAO,CAAC,QAAQ,GAAG,OAAO;QAC3C;QACA,OAAO,MAAM,OAAO;IACxB;IACA,OAAO;AACX;AACA,SAAS,cAAc,KAAK,EAAE,aAAa,EAAE,aAAa;IACtD,gBAAgB,iBAAiB,EAAE;IACnC,MAAM,WAAW,CAAC;IAClB,MAAM,UAAU,CAAC;IACjB,MAAM,YAAY,CAAC;IACnB,MAAM,iBAAiB,CAAC;IACxB,cAAc,OAAO,CAAC,CAAC;QACnB,cAAc,CAAC,MAAM,SAAS,CAAC,GAAG;QAClC,cAAc,CAAC,MAAM,MAAM,CAAC,GAAG;IACnC;IACA,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC;QACxB,MAAM,oBAAoB,gBAAgB,aAAa,CAAC,IAAI,GAAG;QAC/D,MAAM,QAAQ,KAAK,CAAC,IAAI;QACxB,IAAI,cAAc,MAAM;YACpB;QACJ;QACA,IAAI,mBAAmB;YACnB,QAAQ,CAAC,kBAAkB,GAAG;YAC9B;QACJ;QACA,IAAI,cAAc,CAAC,IAAI,EAAE;YACrB,SAAS,CAAC,IAAI,GAAG;YACjB;QACJ;QACA,IAAI,YAAY,QAAQ,CAAC,MAAM;YAC3B,OAAO,CAAC,IAAI,GAAG,cAAc;YAC7B;QACJ;QACA,OAAO,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI;IAC7B;IACA,OAAO;QAAE;QAAS;QAAU;IAAU;AAC1C;AACA,SAAS,aAAa,KAAK;IACvB,OAAO,KAAK,CAAC,kBAAkB;AACnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/configuration/react/templates.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nfunction getAnonymousTemplate(props, templateMeta, hasTranscludedContent) {\n    if (templateMeta.tmplOption === 'template' && hasTranscludedContent) {\n        return {\n            optionName: templateMeta.tmplOption,\n            isAnonymous: true,\n            type: 'children',\n            content: props.children,\n        };\n    }\n    if (props[templateMeta.render]) {\n        return {\n            optionName: templateMeta.tmplOption,\n            isAnonymous: true,\n            type: 'render',\n            content: props[templateMeta.render],\n        };\n    }\n    if (props[templateMeta.component]) {\n        return {\n            optionName: templateMeta.tmplOption,\n            isAnonymous: true,\n            type: 'component',\n            content: props[templateMeta.component],\n        };\n    }\n    return null;\n}\nfunction getNamedTemplate(props) {\n    if (!props.name) {\n        return null;\n    }\n    if (props.component) {\n        return {\n            optionName: props.name,\n            isAnonymous: false,\n            type: 'component',\n            content: props.component,\n        };\n    }\n    if (props.render) {\n        return {\n            optionName: props.name,\n            isAnonymous: false,\n            type: 'render',\n            content: props.render,\n        };\n    }\n    return {\n        optionName: props.name,\n        isAnonymous: false,\n        type: 'children',\n        content: props.children,\n    };\n}\nexport { getAnonymousTemplate, getNamedTemplate, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAED,SAAS,qBAAqB,KAAK,EAAE,YAAY,EAAE,qBAAqB;IACpE,IAAI,aAAa,UAAU,KAAK,cAAc,uBAAuB;QACjE,OAAO;YACH,YAAY,aAAa,UAAU;YACnC,aAAa;YACb,MAAM;YACN,SAAS,MAAM,QAAQ;QAC3B;IACJ;IACA,IAAI,KAAK,CAAC,aAAa,MAAM,CAAC,EAAE;QAC5B,OAAO;YACH,YAAY,aAAa,UAAU;YACnC,aAAa;YACb,MAAM;YACN,SAAS,KAAK,CAAC,aAAa,MAAM,CAAC;QACvC;IACJ;IACA,IAAI,KAAK,CAAC,aAAa,SAAS,CAAC,EAAE;QAC/B,OAAO;YACH,YAAY,aAAa,UAAU;YACnC,aAAa;YACb,MAAM;YACN,SAAS,KAAK,CAAC,aAAa,SAAS,CAAC;QAC1C;IACJ;IACA,OAAO;AACX;AACA,SAAS,iBAAiB,KAAK;IAC3B,IAAI,CAAC,MAAM,IAAI,EAAE;QACb,OAAO;IACX;IACA,IAAI,MAAM,SAAS,EAAE;QACjB,OAAO;YACH,YAAY,MAAM,IAAI;YACtB,aAAa;YACb,MAAM;YACN,SAAS,MAAM,SAAS;QAC5B;IACJ;IACA,IAAI,MAAM,MAAM,EAAE;QACd,OAAO;YACH,YAAY,MAAM,IAAI;YACtB,aAAa;YACb,MAAM;YACN,SAAS,MAAM,MAAM;QACzB;IACJ;IACA,OAAO;QACH,YAAY,MAAM,IAAI;QACtB,aAAa;QACb,MAAM;QACN,SAAS,MAAM,QAAQ;IAC3B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 231, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/contexts.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport { createContext, } from 'react';\nexport const RemovalLockerContext = createContext(undefined);\n// eslint-disable-next-line @typescript-eslint/no-extra-parens\nexport const RestoreTreeContext = createContext(undefined);\nexport const NestedOptionContext = createContext({\n    parentExpectedChildren: {},\n    parentFullName: '',\n    onChildOptionsReady: () => undefined,\n    onNamedTemplateReady: () => undefined,\n    getOptionComponentKey: () => 0,\n    treeUpdateToken: Symbol('initial tree update token'),\n    parentType: 'component',\n});\nexport const TemplateRenderingContext = createContext({\n    isTemplateRendering: false,\n});\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;AAED;;AACO,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AAE3C,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;AACzC,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;IAC7C,wBAAwB,CAAC;IACzB,gBAAgB;IAChB,qBAAqB,IAAM;IAC3B,sBAAsB,IAAM;IAC5B,uBAAuB,IAAM;IAC7B,iBAAiB,OAAO;IACxB,YAAY;AAChB;AACO,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAAE;IAClD,qBAAqB;AACzB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 268, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/configuration/config-node.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport { separateProps } from '../widget-config';\nimport { getAnonymousTemplate } from './react/templates';\nimport { TemplateRenderingContext } from '../contexts';\nfunction buildNodeFullName(node) {\n    let currentNode = node;\n    let fullName = '';\n    while (currentNode && currentNode.name) {\n        fullName = currentNode.name.concat(typeof currentNode.index === 'number' ? `[${currentNode.index}]` : '', fullName ? `.${fullName}` : '');\n        currentNode = currentNode.parentNode;\n    }\n    return fullName;\n}\nconst renderContextValue = {\n    isTemplateRendering: true,\n};\nconst createConfigBuilder = (optionElement, parentFullName) => {\n    const separatedValues = separateProps(optionElement.props, optionElement.descriptor.initialValuesProps, optionElement.descriptor.templates);\n    return {\n        node: {\n            name: optionElement.descriptor.name,\n            predefinedOptions: optionElement.descriptor.predefinedValuesProps,\n            initialOptions: separatedValues.defaults,\n            options: separatedValues.options,\n            templates: [],\n            configCollections: {},\n            configs: {},\n        },\n        configCollectionMaps: {},\n        getConfigCollectionData(name) {\n            if (!this.node.configCollections[name]) {\n                this.node.configCollections[name] = [];\n                this.configCollectionMaps[name] = {};\n            }\n            return [this.node.configCollections[name], this.configCollectionMaps[name]];\n        },\n        addChildNode(name, childNode) {\n            childNode.parentNode = this.node;\n            this.node.configs[name] = childNode;\n        },\n        addCollectionNode(name, collectionNode, collectionNodeKey) {\n            const [collection, collectionMap] = this.getConfigCollectionData(name);\n            const itemIndex = collectionMap[collectionNodeKey] ?? collection.length;\n            collectionNode.index = itemIndex;\n            collectionNode.parentNode = this.node;\n            if (itemIndex < collection.length) {\n                collection[itemIndex] = collectionNode;\n            }\n            else {\n                collectionMap[collectionNodeKey] = itemIndex;\n                collection.push(collectionNode);\n            }\n        },\n        addTemplate(template) {\n            this.node.templates.push(template);\n        },\n        updateAnonymousTemplates(hasTemplateRendered) {\n            this.node.templates = this.node.templates.filter((template) => !template.isAnonymous);\n            optionElement.descriptor.templates.forEach((templateMeta) => {\n                const template = getAnonymousTemplate(optionElement.props, templateMeta, hasTemplateRendered && (optionElement.descriptor.isCollection || parentFullName.length > 0));\n                if (template) {\n                    this.node.templates.push(this.wrapTemplate(template));\n                }\n            });\n        },\n        wrapTemplate(template) {\n            return template.type === 'children' ? {\n                ...template,\n                content: React.createElement(TemplateRenderingContext.Provider, {\n                    value: renderContextValue,\n                }, template.content),\n            } : template;\n        },\n    };\n};\nexport { buildNodeFullName, createConfigBuilder, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAED;AACA;AACA;AACA;;;;;AACA,SAAS,kBAAkB,IAAI;IAC3B,IAAI,cAAc;IAClB,IAAI,WAAW;IACf,MAAO,eAAe,YAAY,IAAI,CAAE;QACpC,WAAW,YAAY,IAAI,CAAC,MAAM,CAAC,OAAO,YAAY,KAAK,KAAK,WAAW,AAAC,IAAqB,OAAlB,YAAY,KAAK,EAAC,OAAK,IAAI,WAAW,AAAC,IAAY,OAAT,YAAa;QACtI,cAAc,YAAY,UAAU;IACxC;IACA,OAAO;AACX;AACA,MAAM,qBAAqB;IACvB,qBAAqB;AACzB;AACA,MAAM,sBAAsB,CAAC,eAAe;IACxC,MAAM,kBAAkB,CAAA,GAAA,yKAAA,CAAA,gBAAa,AAAD,EAAE,cAAc,KAAK,EAAE,cAAc,UAAU,CAAC,kBAAkB,EAAE,cAAc,UAAU,CAAC,SAAS;IAC1I,OAAO;QACH,MAAM;YACF,MAAM,cAAc,UAAU,CAAC,IAAI;YACnC,mBAAmB,cAAc,UAAU,CAAC,qBAAqB;YACjE,gBAAgB,gBAAgB,QAAQ;YACxC,SAAS,gBAAgB,OAAO;YAChC,WAAW,EAAE;YACb,mBAAmB,CAAC;YACpB,SAAS,CAAC;QACd;QACA,sBAAsB,CAAC;QACvB,yBAAwB,IAAI;YACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,EAAE;gBACpC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK,GAAG,EAAE;gBACtC,IAAI,CAAC,oBAAoB,CAAC,KAAK,GAAG,CAAC;YACvC;YACA,OAAO;gBAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,KAAK;gBAAE,IAAI,CAAC,oBAAoB,CAAC,KAAK;aAAC;QAC/E;QACA,cAAa,IAAI,EAAE,SAAS;YACxB,UAAU,UAAU,GAAG,IAAI,CAAC,IAAI;YAChC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAAG;QAC9B;QACA,mBAAkB,IAAI,EAAE,cAAc,EAAE,iBAAiB;YACrD,MAAM,CAAC,YAAY,cAAc,GAAG,IAAI,CAAC,uBAAuB,CAAC;gBAC/C;YAAlB,MAAM,YAAY,CAAA,mCAAA,aAAa,CAAC,kBAAkB,cAAhC,8CAAA,mCAAoC,WAAW,MAAM;YACvE,eAAe,KAAK,GAAG;YACvB,eAAe,UAAU,GAAG,IAAI,CAAC,IAAI;YACrC,IAAI,YAAY,WAAW,MAAM,EAAE;gBAC/B,UAAU,CAAC,UAAU,GAAG;YAC5B,OACK;gBACD,aAAa,CAAC,kBAAkB,GAAG;gBACnC,WAAW,IAAI,CAAC;YACpB;QACJ;QACA,aAAY,QAAQ;YAChB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;QAC7B;QACA,0BAAyB,mBAAmB;YACxC,IAAI,CAAC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,WAAa,CAAC,SAAS,WAAW;YACpF,cAAc,UAAU,CAAC,SAAS,CAAC,OAAO,CAAC,CAAC;gBACxC,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,uBAAoB,AAAD,EAAE,cAAc,KAAK,EAAE,cAAc,uBAAuB,CAAC,cAAc,UAAU,CAAC,YAAY,IAAI,eAAe,MAAM,GAAG,CAAC;gBACnK,IAAI,UAAU;oBACV,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC;gBAC/C;YACJ;QACJ;QACA,cAAa,QAAQ;YACjB,OAAO,SAAS,IAAI,KAAK,aAAa;gBAClC,GAAG,QAAQ;gBACX,SAAS,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,2BAAwB,CAAC,QAAQ,EAAE;oBAC5D,OAAO;gBACX,GAAG,SAAS,OAAO;YACvB,IAAI;QACR;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 370, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/use-option-scanning.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport { useContext, useRef, useLayoutEffect, } from 'react';\nimport { mergeNameParts } from './configuration/utils';\nimport { createConfigBuilder } from './configuration/config-node';\nimport { NestedOptionContext } from './contexts';\nexport function useOptionScanning(optionElement, getHasTemplate, parentUpdateToken, parentType) {\n    const parentContext = useContext(NestedOptionContext);\n    const { parentFullName, } = parentContext;\n    const updateToken = Symbol('update token');\n    const configBuilder = createConfigBuilder(optionElement, parentFullName);\n    const childComponentCounter = useRef(0);\n    const context = {\n        parentExpectedChildren: optionElement.descriptor.expectedChildren,\n        parentFullName: mergeNameParts(parentFullName, optionElement.descriptor.name),\n        parentType,\n        treeUpdateToken: updateToken,\n        getOptionComponentKey: () => {\n            childComponentCounter.current += 1;\n            return childComponentCounter.current;\n        },\n        onNamedTemplateReady: (template, childUpdateToken) => {\n            if (childUpdateToken !== updateToken) {\n                return;\n            }\n            if (template) {\n                configBuilder.addTemplate(template);\n            }\n        },\n        onChildOptionsReady: (childConfigNode, childDescriptor, childUpdateToken, childComponentKey) => {\n            if (childUpdateToken !== updateToken) {\n                return;\n            }\n            const { isCollection, name } = childDescriptor;\n            if (isCollection) {\n                configBuilder.addCollectionNode(name, childConfigNode, childComponentKey);\n                return;\n            }\n            configBuilder.addChildNode(name, childConfigNode);\n        },\n    };\n    useLayoutEffect(() => {\n        configBuilder.updateAnonymousTemplates(getHasTemplate());\n    }, [parentUpdateToken]);\n    return [configBuilder.node, context];\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAED;AACA;AACA;AACA;;;;;AACO,SAAS,kBAAkB,aAAa,EAAE,cAAc,EAAE,iBAAiB,EAAE,UAAU;IAC1F,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,sBAAmB;IACpD,MAAM,EAAE,cAAc,EAAG,GAAG;IAC5B,MAAM,cAAc,OAAO;IAC3B,MAAM,gBAAgB,CAAA,GAAA,wLAAA,CAAA,sBAAmB,AAAD,EAAE,eAAe;IACzD,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,UAAU;QACZ,wBAAwB,cAAc,UAAU,CAAC,gBAAgB;QACjE,gBAAgB,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,cAAc,UAAU,CAAC,IAAI;QAC5E;QACA,iBAAiB;QACjB,uBAAuB;YACnB,sBAAsB,OAAO,IAAI;YACjC,OAAO,sBAAsB,OAAO;QACxC;QACA,sBAAsB,CAAC,UAAU;YAC7B,IAAI,qBAAqB,aAAa;gBAClC;YACJ;YACA,IAAI,UAAU;gBACV,cAAc,WAAW,CAAC;YAC9B;QACJ;QACA,qBAAqB,CAAC,iBAAiB,iBAAiB,kBAAkB;YACtE,IAAI,qBAAqB,aAAa;gBAClC;YACJ;YACA,MAAM,EAAE,YAAY,EAAE,IAAI,EAAE,GAAG;YAC/B,IAAI,cAAc;gBACd,cAAc,iBAAiB,CAAC,MAAM,iBAAiB;gBACvD;YACJ;YACA,cAAc,YAAY,CAAC,MAAM;QACrC;IACJ;IACA,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;6CAAE;YACZ,cAAc,wBAAwB,CAAC;QAC3C;4CAAG;QAAC;KAAkB;IACtB,OAAO;QAAC,cAAc,IAAI;QAAE;KAAQ;AACxC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/configuration/tree.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport { buildNodeFullName } from './config-node';\nimport { mergeNameParts, parseOptionName } from './utils';\nfunction buildTemplates(node, optionsAccum, templatesAccum) {\n    const fullName = buildNodeFullName(node);\n    node.templates.forEach((template) => {\n        if (template.isAnonymous) {\n            const templateName = mergeNameParts(fullName, template.optionName);\n            optionsAccum[template.optionName] = templateName;\n            templatesAccum[templateName] = template;\n        }\n        else {\n            templatesAccum[template.optionName] = template;\n        }\n    });\n}\nfunction buildNode(node, templatesAccum, ignoreInitialValues) {\n    const result = {};\n    Object.keys(node.predefinedOptions).forEach((key) => {\n        result[key] = node.predefinedOptions[key];\n    });\n    Object.keys(node.configs).forEach((key) => {\n        result[key] = buildNode(node.configs[key], templatesAccum, ignoreInitialValues);\n    });\n    Object.keys(node.configCollections).forEach((key) => {\n        result[key] = node.configCollections[key].map((item) => buildNode(item, templatesAccum, ignoreInitialValues));\n    });\n    if (!ignoreInitialValues) {\n        Object.keys(node.initialOptions).forEach((key) => {\n            result[key] = node.initialOptions[key];\n        });\n    }\n    Object.keys(node.options).forEach((key) => {\n        result[key] = node.options[key];\n    });\n    buildTemplates(node, result, templatesAccum);\n    return result;\n}\nfunction buildConfig(root, ignoreInitialValues) {\n    const templatesAccum = {};\n    const options = buildNode(root, templatesAccum, ignoreInitialValues);\n    return {\n        templates: templatesAccum,\n        options,\n    };\n}\nvar ValueType;\n(function (ValueType) {\n    ValueType[ValueType[\"Simple\"] = 0] = \"Simple\";\n    ValueType[ValueType[\"Complex\"] = 1] = \"Complex\";\n    ValueType[ValueType[\"Array\"] = 2] = \"Array\";\n})(ValueType || (ValueType = {}));\nfunction findValueInObject(obj, path) {\n    const key = path.shift();\n    if (!key) {\n        return {\n            value: obj,\n            type: ValueType.Simple,\n        };\n    }\n    if (obj instanceof Object && Object.keys(obj).includes(key)) {\n        return findValueInObject(obj[key], path);\n    }\n    return undefined;\n}\nfunction findValue(node, path) {\n    const name = path.shift();\n    if (!name) {\n        return {\n            value: buildConfig(node, true).options,\n            type: ValueType.Complex,\n        };\n    }\n    const optionInfo = parseOptionName(name);\n    if (optionInfo.name in node.options) {\n        const options = optionInfo.isCollectionItem\n            ? node.options[optionInfo.name][optionInfo.index]\n            : node.options[optionInfo.name];\n        return findValueInObject(options, path);\n    }\n    if (optionInfo.isCollectionItem) {\n        const collection = node.configCollections[optionInfo.name];\n        if (!collection) {\n            return undefined;\n        }\n        const item = collection[optionInfo.index];\n        if (!item) {\n            return undefined;\n        }\n        return findValue(item, path);\n    }\n    const child = node.configs[optionInfo.name];\n    if (child) {\n        return findValue(child, path);\n    }\n    const childCollection = node.configCollections[optionInfo.name];\n    if (childCollection) {\n        if (path.length !== 0) {\n            return undefined;\n        }\n        return {\n            value: childCollection.map((item) => buildNode(item, {}, true)),\n            type: ValueType.Array,\n        };\n    }\n    return undefined;\n}\nexport { ValueType, buildConfig, buildNode, buildTemplates, findValue, findValueInObject, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;;;AAED;AACA;;;AACA,SAAS,eAAe,IAAI,EAAE,YAAY,EAAE,cAAc;IACtD,MAAM,WAAW,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD,EAAE;IACnC,KAAK,SAAS,CAAC,OAAO,CAAC,CAAC;QACpB,IAAI,SAAS,WAAW,EAAE;YACtB,MAAM,eAAe,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,SAAS,UAAU;YACjE,YAAY,CAAC,SAAS,UAAU,CAAC,GAAG;YACpC,cAAc,CAAC,aAAa,GAAG;QACnC,OACK;YACD,cAAc,CAAC,SAAS,UAAU,CAAC,GAAG;QAC1C;IACJ;AACJ;AACA,SAAS,UAAU,IAAI,EAAE,cAAc,EAAE,mBAAmB;IACxD,MAAM,SAAS,CAAC;IAChB,OAAO,IAAI,CAAC,KAAK,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,GAAG,KAAK,iBAAiB,CAAC,IAAI;IAC7C;IACA,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,GAAG,UAAU,KAAK,OAAO,CAAC,IAAI,EAAE,gBAAgB;IAC/D;IACA,OAAO,IAAI,CAAC,KAAK,iBAAiB,EAAE,OAAO,CAAC,CAAC;QACzC,MAAM,CAAC,IAAI,GAAG,KAAK,iBAAiB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,OAAS,UAAU,MAAM,gBAAgB;IAC5F;IACA,IAAI,CAAC,qBAAqB;QACtB,OAAO,IAAI,CAAC,KAAK,cAAc,EAAE,OAAO,CAAC,CAAC;YACtC,MAAM,CAAC,IAAI,GAAG,KAAK,cAAc,CAAC,IAAI;QAC1C;IACJ;IACA,OAAO,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO,CAAC,CAAC;QAC/B,MAAM,CAAC,IAAI,GAAG,KAAK,OAAO,CAAC,IAAI;IACnC;IACA,eAAe,MAAM,QAAQ;IAC7B,OAAO;AACX;AACA,SAAS,YAAY,IAAI,EAAE,mBAAmB;IAC1C,MAAM,iBAAiB,CAAC;IACxB,MAAM,UAAU,UAAU,MAAM,gBAAgB;IAChD,OAAO;QACH,WAAW;QACX;IACJ;AACJ;AACA,IAAI;AACJ,CAAC,SAAU,SAAS;IAChB,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,EAAE,GAAG;IACrC,SAAS,CAAC,SAAS,CAAC,UAAU,GAAG,EAAE,GAAG;IACtC,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,EAAE,GAAG;AACxC,CAAC,EAAE,aAAa,CAAC,YAAY,CAAC,CAAC;AAC/B,SAAS,kBAAkB,GAAG,EAAE,IAAI;IAChC,MAAM,MAAM,KAAK,KAAK;IACtB,IAAI,CAAC,KAAK;QACN,OAAO;YACH,OAAO;YACP,MAAM,UAAU,MAAM;QAC1B;IACJ;IACA,IAAI,eAAe,UAAU,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC,MAAM;QACzD,OAAO,kBAAkB,GAAG,CAAC,IAAI,EAAE;IACvC;IACA,OAAO;AACX;AACA,SAAS,UAAU,IAAI,EAAE,IAAI;IACzB,MAAM,OAAO,KAAK,KAAK;IACvB,IAAI,CAAC,MAAM;QACP,OAAO;YACH,OAAO,YAAY,MAAM,MAAM,OAAO;YACtC,MAAM,UAAU,OAAO;QAC3B;IACJ;IACA,MAAM,aAAa,CAAA,GAAA,+KAAA,CAAA,kBAAe,AAAD,EAAE;IACnC,IAAI,WAAW,IAAI,IAAI,KAAK,OAAO,EAAE;QACjC,MAAM,UAAU,WAAW,gBAAgB,GACrC,KAAK,OAAO,CAAC,WAAW,IAAI,CAAC,CAAC,WAAW,KAAK,CAAC,GAC/C,KAAK,OAAO,CAAC,WAAW,IAAI,CAAC;QACnC,OAAO,kBAAkB,SAAS;IACtC;IACA,IAAI,WAAW,gBAAgB,EAAE;QAC7B,MAAM,aAAa,KAAK,iBAAiB,CAAC,WAAW,IAAI,CAAC;QAC1D,IAAI,CAAC,YAAY;YACb,OAAO;QACX;QACA,MAAM,OAAO,UAAU,CAAC,WAAW,KAAK,CAAC;QACzC,IAAI,CAAC,MAAM;YACP,OAAO;QACX;QACA,OAAO,UAAU,MAAM;IAC3B;IACA,MAAM,QAAQ,KAAK,OAAO,CAAC,WAAW,IAAI,CAAC;IAC3C,IAAI,OAAO;QACP,OAAO,UAAU,OAAO;IAC5B;IACA,MAAM,kBAAkB,KAAK,iBAAiB,CAAC,WAAW,IAAI,CAAC;IAC/D,IAAI,iBAAiB;QACjB,IAAI,KAAK,MAAM,KAAK,GAAG;YACnB,OAAO;QACX;QACA,OAAO;YACH,OAAO,gBAAgB,GAAG,CAAC,CAAC,OAAS,UAAU,MAAM,CAAC,GAAG;YACzD,MAAM,UAAU,KAAK;QACzB;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/configuration/comparer.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport { buildNodeFullName } from './config-node';\nimport { buildNode, buildTemplates } from './tree';\nimport { mergeNameParts } from './utils';\nfunction compareTemplates(current, currentFullName, prev, changesAccum) {\n    const currentTemplatesOptions = {};\n    const currentTemplates = {};\n    const prevTemplatesOptions = {};\n    const prevTemplates = {};\n    buildTemplates(current, currentTemplatesOptions, currentTemplates);\n    buildTemplates(prev, prevTemplatesOptions, prevTemplates);\n    changesAccum.addRemovedValues(currentTemplatesOptions, prevTemplatesOptions, currentFullName);\n    // TODO: support switching to default templates\n    // appendRemovedValues(currentTemplates, prevTemplates, \"\", changesAccum.templates);\n    Object.keys(currentTemplatesOptions).forEach((key) => {\n        if (currentTemplatesOptions[key] === prevTemplatesOptions[key]) {\n            return;\n        }\n        changesAccum.options[mergeNameParts(currentFullName, key)] = currentTemplatesOptions[key];\n    });\n    Object.keys(currentTemplates).forEach((key) => {\n        const currentTemplate = currentTemplates[key];\n        const prevTemplate = prevTemplates[key];\n        if (prevTemplate && currentTemplate.content === prevTemplate.content) {\n            return;\n        }\n        changesAccum.templates[key] = currentTemplate;\n    });\n}\nfunction compare(current, prev, changesAccum) {\n    const fullName = buildNodeFullName(current);\n    if (!prev) {\n        changesAccum.options[fullName] = buildNode(current, changesAccum.templates, true);\n        return;\n    }\n    changesAccum.addRemovedValues(current.options, prev.options, fullName);\n    changesAccum.addRemovedValues(current.configCollections, prev.configCollections, fullName);\n    changesAccum.addRemovedValues(current.configs, prev.configs, fullName);\n    // eslint-disable-next-line @typescript-eslint/no-use-before-define\n    compareCollections(current, fullName, prev, changesAccum);\n    Object.keys(current.configs).forEach((key) => {\n        compare(current.configs[key], prev.configs[key], changesAccum);\n    });\n    Object.keys(current.options).forEach((key) => {\n        if (current.options[key] === prev.options[key]) {\n            return;\n        }\n        changesAccum.options[mergeNameParts(fullName, key)] = current.options[key];\n    });\n    compareTemplates(current, fullName, prev, changesAccum);\n}\nfunction appendRemovedValues(current, prev, path, changesAccum) {\n    const removedKeys = Object.keys(prev).filter((key) => !Object.keys(current).includes(key));\n    removedKeys.forEach((key) => {\n        changesAccum.push(mergeNameParts(path, key));\n    });\n}\nfunction getChanges(current, prev) {\n    const changesAccum = {\n        options: {},\n        removedOptions: [],\n        templates: {},\n        addRemovedValues(currentOptions, prevOptions, path) {\n            appendRemovedValues(currentOptions, prevOptions, path, this.removedOptions);\n        },\n    };\n    compare(current, prev, changesAccum);\n    return changesAccum;\n}\nfunction compareCollections(current, currentFullName, prev, changesAccum) {\n    Object.keys(current.configCollections).forEach((key) => {\n        const currentCollection = current.configCollections[key];\n        const prevCollection = prev.configCollections[key] || [];\n        if (!currentCollection || currentCollection.length !== prevCollection.length) {\n            const updatedCollection = [];\n            currentCollection.forEach((item) => {\n                const config = buildNode(item, changesAccum.templates, true);\n                updatedCollection.push(config);\n            });\n            changesAccum.options[mergeNameParts(currentFullName, key)] = updatedCollection;\n            return;\n        }\n        for (let i = 0; i < currentCollection.length; i += 1) {\n            compare(currentCollection[i], prevCollection[i], changesAccum);\n        }\n    });\n}\nexport { getChanges, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAED;AACA;AACA;;;;AACA,SAAS,iBAAiB,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,YAAY;IAClE,MAAM,0BAA0B,CAAC;IACjC,MAAM,mBAAmB,CAAC;IAC1B,MAAM,uBAAuB,CAAC;IAC9B,MAAM,gBAAgB,CAAC;IACvB,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,yBAAyB;IACjD,CAAA,GAAA,8KAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,sBAAsB;IAC3C,aAAa,gBAAgB,CAAC,yBAAyB,sBAAsB;IAC7E,+CAA+C;IAC/C,oFAAoF;IACpF,OAAO,IAAI,CAAC,yBAAyB,OAAO,CAAC,CAAC;QAC1C,IAAI,uBAAuB,CAAC,IAAI,KAAK,oBAAoB,CAAC,IAAI,EAAE;YAC5D;QACJ;QACA,aAAa,OAAO,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,KAAK,GAAG,uBAAuB,CAAC,IAAI;IAC7F;IACA,OAAO,IAAI,CAAC,kBAAkB,OAAO,CAAC,CAAC;QACnC,MAAM,kBAAkB,gBAAgB,CAAC,IAAI;QAC7C,MAAM,eAAe,aAAa,CAAC,IAAI;QACvC,IAAI,gBAAgB,gBAAgB,OAAO,KAAK,aAAa,OAAO,EAAE;YAClE;QACJ;QACA,aAAa,SAAS,CAAC,IAAI,GAAG;IAClC;AACJ;AACA,SAAS,QAAQ,OAAO,EAAE,IAAI,EAAE,YAAY;IACxC,MAAM,WAAW,CAAA,GAAA,wLAAA,CAAA,oBAAiB,AAAD,EAAE;IACnC,IAAI,CAAC,MAAM;QACP,aAAa,OAAO,CAAC,SAAS,GAAG,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,SAAS,aAAa,SAAS,EAAE;QAC5E;IACJ;IACA,aAAa,gBAAgB,CAAC,QAAQ,OAAO,EAAE,KAAK,OAAO,EAAE;IAC7D,aAAa,gBAAgB,CAAC,QAAQ,iBAAiB,EAAE,KAAK,iBAAiB,EAAE;IACjF,aAAa,gBAAgB,CAAC,QAAQ,OAAO,EAAE,KAAK,OAAO,EAAE;IAC7D,mEAAmE;IACnE,mBAAmB,SAAS,UAAU,MAAM;IAC5C,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC;QAClC,QAAQ,QAAQ,OAAO,CAAC,IAAI,EAAE,KAAK,OAAO,CAAC,IAAI,EAAE;IACrD;IACA,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC;QAClC,IAAI,QAAQ,OAAO,CAAC,IAAI,KAAK,KAAK,OAAO,CAAC,IAAI,EAAE;YAC5C;QACJ;QACA,aAAa,OAAO,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,KAAK,GAAG,QAAQ,OAAO,CAAC,IAAI;IAC9E;IACA,iBAAiB,SAAS,UAAU,MAAM;AAC9C;AACA,SAAS,oBAAoB,OAAO,EAAE,IAAI,EAAE,IAAI,EAAE,YAAY;IAC1D,MAAM,cAAc,OAAO,IAAI,CAAC,MAAM,MAAM,CAAC,CAAC,MAAQ,CAAC,OAAO,IAAI,CAAC,SAAS,QAAQ,CAAC;IACrF,YAAY,OAAO,CAAC,CAAC;QACjB,aAAa,IAAI,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,MAAM;IAC3C;AACJ;AACA,SAAS,WAAW,OAAO,EAAE,IAAI;IAC7B,MAAM,eAAe;QACjB,SAAS,CAAC;QACV,gBAAgB,EAAE;QAClB,WAAW,CAAC;QACZ,kBAAiB,cAAc,EAAE,WAAW,EAAE,IAAI;YAC9C,oBAAoB,gBAAgB,aAAa,MAAM,IAAI,CAAC,cAAc;QAC9E;IACJ;IACA,QAAQ,SAAS,MAAM;IACvB,OAAO;AACX;AACA,SAAS,mBAAmB,OAAO,EAAE,eAAe,EAAE,IAAI,EAAE,YAAY;IACpE,OAAO,IAAI,CAAC,QAAQ,iBAAiB,EAAE,OAAO,CAAC,CAAC;QAC5C,MAAM,oBAAoB,QAAQ,iBAAiB,CAAC,IAAI;QACxD,MAAM,iBAAiB,KAAK,iBAAiB,CAAC,IAAI,IAAI,EAAE;QACxD,IAAI,CAAC,qBAAqB,kBAAkB,MAAM,KAAK,eAAe,MAAM,EAAE;YAC1E,MAAM,oBAAoB,EAAE;YAC5B,kBAAkB,OAAO,CAAC,CAAC;gBACvB,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,MAAM,aAAa,SAAS,EAAE;gBACvD,kBAAkB,IAAI,CAAC;YAC3B;YACA,aAAa,OAAO,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,KAAK,GAAG;YAC7D;QACJ;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,kBAAkB,MAAM,EAAE,KAAK,EAAG;YAClD,QAAQ,iBAAiB,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE,EAAE;QACrD;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 680, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/helpers.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nexport function generateID() {\n    return Math.random().toString(36).substring(2);\n}\nexport class DoubleKeyMap {\n    constructor() {\n        this._map = new Map();\n    }\n    set({ key1, key2 }, value) {\n        let innerMap = this._map.get(key1);\n        if (!innerMap) {\n            innerMap = new Map();\n            this._map.set(key1, innerMap);\n        }\n        innerMap.set(key2, value);\n    }\n    get({ key1, key2 }) {\n        const innerMap = this._map.get(key1);\n        return innerMap ? innerMap.get(key2) : undefined;\n    }\n    delete({ key1, key2 }) {\n        const innerMap = this._map.get(key1);\n        if (!innerMap) {\n            return;\n        }\n        innerMap.delete(key2);\n        if (innerMap.size === 0) {\n            this._map.delete(key1);\n        }\n    }\n    clear() {\n        this._map.clear();\n    }\n    get empty() {\n        return this._map.size === 0;\n    }\n    *[Symbol.iterator]() {\n        for (const [key1, innerMap] of this._map) {\n            for (const [key2, value] of innerMap) {\n                yield [{ key1, key2 }, value];\n            }\n        }\n    }\n}\nexport class TemplateInstantiationModels extends DoubleKeyMap {\n}\nexport function capitalizeFirstLetter(text) {\n    if (text.length) {\n        return `${text[0].toUpperCase()}${text.substr(1)}`;\n    }\n    return '';\n}\nexport function hasExpectedChildren(elementDescriptor) {\n    return !!Object.keys(elementDescriptor.ExpectedChildren || {}).length;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;;AAEM,SAAS;IACZ,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC;AAChD;AACO,MAAM;IAIT,IAAI,KAAc,EAAE,KAAK,EAAE;YAAvB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAd;QACA,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAC7B,IAAI,CAAC,UAAU;YACX,WAAW,IAAI;YACf,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,MAAM;QACxB;QACA,SAAS,GAAG,CAAC,MAAM;IACvB;IACA,IAAI,KAAc,EAAE;YAAhB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAd;QACA,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAC/B,OAAO,WAAW,SAAS,GAAG,CAAC,QAAQ;IAC3C;IACA,OAAO,KAAc,EAAE;YAAhB,EAAE,IAAI,EAAE,IAAI,EAAE,GAAd;QACH,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;QAC/B,IAAI,CAAC,UAAU;YACX;QACJ;QACA,SAAS,MAAM,CAAC;QAChB,IAAI,SAAS,IAAI,KAAK,GAAG;YACrB,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;QACrB;IACJ;IACA,QAAQ;QACJ,IAAI,CAAC,IAAI,CAAC,KAAK;IACnB;IACA,IAAI,QAAQ;QACR,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI,KAAK;IAC9B;IACA,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QACjB,KAAK,MAAM,CAAC,MAAM,SAAS,IAAI,IAAI,CAAC,IAAI,CAAE;YACtC,KAAK,MAAM,CAAC,MAAM,MAAM,IAAI,SAAU;gBAClC,MAAM;oBAAC;wBAAE;wBAAM;oBAAK;oBAAG;iBAAM;YACjC;QACJ;IACJ;IArCA,aAAc;QACV,IAAI,CAAC,IAAI,GAAG,IAAI;IACpB;AAoCJ;AACO,MAAM,oCAAoC;AACjD;AACO,SAAS,sBAAsB,IAAI;IACtC,IAAI,KAAK,MAAM,EAAE;QACb,OAAO,AAAC,GAA0B,OAAxB,IAAI,CAAC,EAAE,CAAC,WAAW,IAAoB,OAAf,KAAK,MAAM,CAAC;IAClD;IACA,OAAO;AACX;AACO,SAAS,oBAAoB,iBAAiB;IACjD,OAAO,CAAC,CAAC,OAAO,IAAI,CAAC,kBAAkB,gBAAgB,IAAI,CAAC,GAAG,MAAM;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 765, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/options-manager.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n/* eslint-disable no-restricted-globals */\nimport { getChanges } from './configuration/comparer';\nimport { buildConfig, findValue, ValueType } from './configuration/tree';\nimport { mergeNameParts, shallowEquals } from './configuration/utils';\nimport { capitalizeFirstLetter } from './helpers';\nconst optionsManagers = new Set();\nlet guardTimeoutHandler = -1;\nlet innerGuardTimeoutHandler = -1;\nexport function unscheduleGuards() {\n    clearTimeout(guardTimeoutHandler);\n    clearTimeout(innerGuardTimeoutHandler);\n}\nexport function scheduleGuards() {\n    unscheduleGuards();\n    guardTimeoutHandler = window.setTimeout(() => {\n        innerGuardTimeoutHandler = window.setTimeout(() => {\n            optionsManagers.forEach((optionManager) => optionManager.execGuards());\n        });\n    });\n}\nclass OptionsManager {\n    constructor() {\n        this.guards = {};\n        this.isUpdating = false;\n        this.onOptionChanged = this.onOptionChanged.bind(this);\n        this.wrapOptionValue = this.wrapOptionValue.bind(this);\n    }\n    setInstance(instance, config, subscribableOptions, independentEvents) {\n        this.instance = instance;\n        this.currentConfig = config;\n        this.subscribableOptions = new Set(subscribableOptions);\n        this.independentEvents = new Set(independentEvents);\n        optionsManagers.add(this);\n    }\n    getInitialOptions(rootNode) {\n        const config = buildConfig(rootNode, false);\n        const options = {};\n        Object.keys(config.options).forEach((key) => {\n            options[key] = this.wrapOptionValue(key, config.options[key]);\n        });\n        return options;\n    }\n    getTemplateOptions(rootNode) {\n        const config = buildConfig(rootNode, false);\n        return config.templates;\n    }\n    update(config, dxtemplates) {\n        const changedOptions = [];\n        const optionChangedHandler = ({ value, fullName }) => {\n            changedOptions.push([fullName, value]);\n        };\n        this.instance.on('optionChanged', optionChangedHandler);\n        const changes = getChanges(config, this.currentConfig);\n        if (!changes.options && !changes.templates && !changes.removedOptions.length) {\n            return;\n        }\n        this.instance.beginUpdate();\n        this.isUpdating = true;\n        changes.removedOptions.forEach((optionName) => {\n            this.resetOption(optionName);\n        });\n        if (Object.keys(dxtemplates).length > 0) {\n            this.setValue('integrationOptions', {\n                templates: dxtemplates,\n            });\n        }\n        Object.keys(changes.options).forEach((key) => {\n            this.setValue(key, changes.options[key]);\n        });\n        this.isUpdating = false;\n        this.instance.off('optionChanged', optionChangedHandler);\n        this.currentConfig = config;\n        changedOptions.forEach(([name, value]) => {\n            const currentPropValue = config.options[name];\n            if (Object.prototype.hasOwnProperty.call(config.options, name)\n                && currentPropValue !== value) {\n                this.setValue(name, currentPropValue);\n            }\n        });\n        this.instance.endUpdate();\n    }\n    onOptionChanged(e) {\n        if (this.isUpdating) {\n            return;\n        }\n        let valueDescriptor = findValue(this.currentConfig, e.fullName.split('.'));\n        if (!valueDescriptor || valueDescriptor.value !== e.value) {\n            this.callOptionChangeHandler(e.fullName, e.value);\n        }\n        valueDescriptor = findValue(this.currentConfig, e.fullName.split('.'));\n        if (!valueDescriptor) {\n            return;\n        }\n        const { value, type } = valueDescriptor;\n        if (value instanceof Array && type === ValueType.Array) {\n            for (let i = 0; i < value.length; i += 1) {\n                if (value[i] !== e.value?.[i]) {\n                    this.addGuard(e.fullName, value);\n                    return;\n                }\n            }\n        }\n        else if (type === ValueType.Complex && value instanceof Object) {\n            Object.keys(value).forEach((key) => {\n                if (value[key] === e.value?.[key]) {\n                    return;\n                }\n                this.addGuard(mergeNameParts(e.fullName, key), value[key]);\n            });\n        }\n        else {\n            const valuesAreEqual = value === e.value;\n            const valuesAreEqualObjects = !valuesAreEqual\n                && value instanceof Object\n                && e.value instanceof Object\n                && shallowEquals(value, e.value);\n            if (valuesAreEqual || valuesAreEqualObjects || this.instance.skipOptionsRollBack) {\n                return;\n            }\n            this.addGuard(e.fullName, value);\n        }\n    }\n    get isInstanceSet() {\n        return !!this.instance;\n    }\n    dispose() {\n        optionsManagers.delete(this);\n        Object.keys(this.guards).forEach((optionName) => {\n            delete this.guards[optionName];\n        });\n        this.instance = null;\n    }\n    isOptionSubscribable(optionName) {\n        return this.subscribableOptions.has(optionName);\n    }\n    isIndependentEvent(optionName) {\n        return this.independentEvents.has(optionName);\n    }\n    callOptionChangeHandler(optionName, optionValue) {\n        if (!this.isOptionSubscribable(optionName)) {\n            return;\n        }\n        const parts = optionName.split('.');\n        const propName = parts[parts.length - 1];\n        if (propName.startsWith('on')) {\n            return;\n        }\n        const eventName = `on${capitalizeFirstLetter(propName)}Change`;\n        parts[parts.length - 1] = eventName;\n        const changeEvent = findValue(this.currentConfig, parts);\n        if (!changeEvent) {\n            return;\n        }\n        if (typeof changeEvent.value !== 'function') {\n            throw new Error(`Invalid value for the ${eventName} property.\r\n                ${eventName} must be a function.`);\n        }\n        changeEvent.value(optionValue);\n    }\n    wrapOptionValue(name, value) {\n        if (name.substr(0, 2) === 'on' && typeof value === 'function') {\n            return (...args) => {\n                if (!this.isUpdating || this.isIndependentEvent(name)) {\n                    value(...args);\n                }\n            };\n        }\n        return value;\n    }\n    addGuard(optionName, optionValue) {\n        if (this.guards[optionName] !== undefined) {\n            return;\n        }\n        const handler = () => {\n            this.setValue(optionName, optionValue);\n            delete this.guards[optionName];\n        };\n        this.guards[optionName] = handler;\n        scheduleGuards();\n    }\n    execGuards() {\n        Object.values(this.guards)\n            .forEach((handler) => handler());\n    }\n    resetOption(name) {\n        if (this.isCollectionOption(name)) {\n            this.setValue(name, []);\n        }\n        else {\n            this.instance.resetOption(name);\n        }\n    }\n    isCollectionOption(name) {\n        const valueDescriptor = findValue(this.currentConfig, name.split('.'));\n        return valueDescriptor?.type === ValueType.Array;\n    }\n    setValue(name, value) {\n        if (this.guards[name]) {\n            delete this.guards[name];\n        }\n        this.instance.option(name, this.wrapOptionValue(name, value));\n    }\n}\nexport { OptionsManager, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC,GAED,wCAAwC;;;;;AACxC;AACA;AACA;AACA;;;;;AACA,MAAM,kBAAkB,IAAI;AAC5B,IAAI,sBAAsB,CAAC;AAC3B,IAAI,2BAA2B,CAAC;AACzB,SAAS;IACZ,aAAa;IACb,aAAa;AACjB;AACO,SAAS;IACZ;IACA,sBAAsB,OAAO,UAAU,CAAC;QACpC,2BAA2B,OAAO,UAAU,CAAC;YACzC,gBAAgB,OAAO,CAAC,CAAC,gBAAkB,cAAc,UAAU;QACvE;IACJ;AACJ;AACA,MAAM;IAOF,YAAY,QAAQ,EAAE,MAAM,EAAE,mBAAmB,EAAE,iBAAiB,EAAE;QAClE,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,mBAAmB,GAAG,IAAI,IAAI;QACnC,IAAI,CAAC,iBAAiB,GAAG,IAAI,IAAI;QACjC,gBAAgB,GAAG,CAAC,IAAI;IAC5B;IACA,kBAAkB,QAAQ,EAAE;QACxB,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,UAAU;QACrC,MAAM,UAAU,CAAC;QACjB,OAAO,IAAI,CAAC,OAAO,OAAO,EAAE,OAAO,CAAC,CAAC;YACjC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,KAAK,OAAO,OAAO,CAAC,IAAI;QAChE;QACA,OAAO;IACX;IACA,mBAAmB,QAAQ,EAAE;QACzB,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE,UAAU;QACrC,OAAO,OAAO,SAAS;IAC3B;IACA,OAAO,MAAM,EAAE,WAAW,EAAE;QACxB,MAAM,iBAAiB,EAAE;QACzB,MAAM,uBAAuB;gBAAC,EAAE,KAAK,EAAE,QAAQ,EAAE;YAC7C,eAAe,IAAI,CAAC;gBAAC;gBAAU;aAAM;QACzC;QACA,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,iBAAiB;QAClC,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,IAAI,CAAC,aAAa;QACrD,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,SAAS,IAAI,CAAC,QAAQ,cAAc,CAAC,MAAM,EAAE;YAC1E;QACJ;QACA,IAAI,CAAC,QAAQ,CAAC,WAAW;QACzB,IAAI,CAAC,UAAU,GAAG;QAClB,QAAQ,cAAc,CAAC,OAAO,CAAC,CAAC;YAC5B,IAAI,CAAC,WAAW,CAAC;QACrB;QACA,IAAI,OAAO,IAAI,CAAC,aAAa,MAAM,GAAG,GAAG;YACrC,IAAI,CAAC,QAAQ,CAAC,sBAAsB;gBAChC,WAAW;YACf;QACJ;QACA,OAAO,IAAI,CAAC,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC;YAClC,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,OAAO,CAAC,IAAI;QAC3C;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,iBAAiB;QACnC,IAAI,CAAC,aAAa,GAAG;QACrB,eAAe,OAAO,CAAC;gBAAC,CAAC,MAAM,MAAM;YACjC,MAAM,mBAAmB,OAAO,OAAO,CAAC,KAAK;YAC7C,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,OAAO,EAAE,SAClD,qBAAqB,OAAO;gBAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM;YACxB;QACJ;QACA,IAAI,CAAC,QAAQ,CAAC,SAAS;IAC3B;IACA,gBAAgB,CAAC,EAAE;QACf,IAAI,IAAI,CAAC,UAAU,EAAE;YACjB;QACJ;QACA,IAAI,kBAAkB,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC;QACrE,IAAI,CAAC,mBAAmB,gBAAgB,KAAK,KAAK,EAAE,KAAK,EAAE;YACvD,IAAI,CAAC,uBAAuB,CAAC,EAAE,QAAQ,EAAE,EAAE,KAAK;QACpD;QACA,kBAAkB,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,EAAE,QAAQ,CAAC,KAAK,CAAC;QACjE,IAAI,CAAC,iBAAiB;YAClB;QACJ;QACA,MAAM,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG;QACxB,IAAI,iBAAiB,SAAS,SAAS,8KAAA,CAAA,YAAS,CAAC,KAAK,EAAE;YACpD,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;oBACrB;gBAAjB,IAAI,KAAK,CAAC,EAAE,OAAK,WAAA,EAAE,KAAK,cAAP,+BAAA,QAAS,CAAC,EAAE,GAAE;oBAC3B,IAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE;oBAC1B;gBACJ;YACJ;QACJ,OACK,IAAI,SAAS,8KAAA,CAAA,YAAS,CAAC,OAAO,IAAI,iBAAiB,QAAQ;YAC5D,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAC;oBACL;gBAAnB,IAAI,KAAK,CAAC,IAAI,OAAK,WAAA,EAAE,KAAK,cAAP,+BAAA,QAAS,CAAC,IAAI,GAAE;oBAC/B;gBACJ;gBACA,IAAI,CAAC,QAAQ,CAAC,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,EAAE,QAAQ,EAAE,MAAM,KAAK,CAAC,IAAI;YAC7D;QACJ,OACK;YACD,MAAM,iBAAiB,UAAU,EAAE,KAAK;YACxC,MAAM,wBAAwB,CAAC,kBACxB,iBAAiB,UACjB,EAAE,KAAK,YAAY,UACnB,CAAA,GAAA,+KAAA,CAAA,gBAAa,AAAD,EAAE,OAAO,EAAE,KAAK;YACnC,IAAI,kBAAkB,yBAAyB,IAAI,CAAC,QAAQ,CAAC,mBAAmB,EAAE;gBAC9E;YACJ;YACA,IAAI,CAAC,QAAQ,CAAC,EAAE,QAAQ,EAAE;QAC9B;IACJ;IACA,IAAI,gBAAgB;QAChB,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ;IAC1B;IACA,UAAU;QACN,gBAAgB,MAAM,CAAC,IAAI;QAC3B,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;YAC9B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;QAClC;QACA,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,qBAAqB,UAAU,EAAE;QAC7B,OAAO,IAAI,CAAC,mBAAmB,CAAC,GAAG,CAAC;IACxC;IACA,mBAAmB,UAAU,EAAE;QAC3B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;IACtC;IACA,wBAAwB,UAAU,EAAE,WAAW,EAAE;QAC7C,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,aAAa;YACxC;QACJ;QACA,MAAM,QAAQ,WAAW,KAAK,CAAC;QAC/B,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QACxC,IAAI,SAAS,UAAU,CAAC,OAAO;YAC3B;QACJ;QACA,MAAM,YAAY,AAAC,KAAoC,OAAhC,CAAA,GAAA,gKAAA,CAAA,wBAAqB,AAAD,EAAE,WAAU;QACvD,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,GAAG;QAC1B,MAAM,cAAc,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE;QAClD,IAAI,CAAC,aAAa;YACd;QACJ;QACA,IAAI,OAAO,YAAY,KAAK,KAAK,YAAY;YACzC,MAAM,IAAI,MAAM,AAAC,yBACX,OADmC,WAAU,gCACnC,OAAV,WAAU;QACpB;QACA,YAAY,KAAK,CAAC;IACtB;IACA,gBAAgB,IAAI,EAAE,KAAK,EAAE;QACzB,IAAI,KAAK,MAAM,CAAC,GAAG,OAAO,QAAQ,OAAO,UAAU,YAAY;;YAC3D,OAAO;iDAAI;oBAAA;;gBACP,IAAI,CAAC,MAAK,UAAU,IAAI,MAAK,kBAAkB,CAAC,OAAO;oBACnD,SAAS;gBACb;YACJ;QACJ;QACA,OAAO;IACX;IACA,SAAS,UAAU,EAAE,WAAW,EAAE;QAC9B,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,WAAW;YACvC;QACJ;QACA,MAAM,UAAU;YACZ,IAAI,CAAC,QAAQ,CAAC,YAAY;YAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,WAAW;QAClC;QACA,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG;QAC1B;IACJ;IACA,aAAa;QACT,OAAO,MAAM,CAAC,IAAI,CAAC,MAAM,EACpB,OAAO,CAAC,CAAC,UAAY;IAC9B;IACA,YAAY,IAAI,EAAE;QACd,IAAI,IAAI,CAAC,kBAAkB,CAAC,OAAO;YAC/B,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;QAC1B,OACK;YACD,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC;QAC9B;IACJ;IACA,mBAAmB,IAAI,EAAE;QACrB,MAAM,kBAAkB,CAAA,GAAA,8KAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,aAAa,EAAE,KAAK,KAAK,CAAC;QACjE,OAAO,CAAA,4BAAA,sCAAA,gBAAiB,IAAI,MAAK,8KAAA,CAAA,YAAS,CAAC,KAAK;IACpD;IACA,SAAS,IAAI,EAAE,KAAK,EAAE;QAClB,IAAI,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACnB,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK;QAC5B;QACA,IAAI,CAAC,QAAQ,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM;IAC1D;IApLA,aAAc;QACV,IAAI,CAAC,MAAM,GAAG,CAAC;QACf,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;QACrD,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI;IACzD;AAgLJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 994, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/template-wrapper.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport * as events from 'devextreme/events';\nimport { useCallback, useLayoutEffect, useEffect, useState, useRef, useMemo, memo, } from 'react';\nimport { createPortal } from 'react-dom';\nimport { DX_REMOVE_EVENT } from './component-base';\nimport { RemovalLockerContext } from './contexts';\nconst createHiddenNode = (containerNodeName, ref, defaultElement) => {\n    const style = { display: 'none' };\n    switch (containerNodeName) {\n        case 'TABLE':\n            return React.createElement(\"tbody\", { style: style, ref: ref });\n        case 'TBODY':\n            return React.createElement(\"tr\", { style: style, ref: ref });\n        default:\n            return React.createElement(defaultElement, { style, ref });\n    }\n};\nconst TemplateWrapperComponent = ({ templateFactory, data, index, container, onRemoved, onRendered, }) => {\n    const [removalListenerRequired, setRemovalListenerRequired] = useState(false);\n    const isRemovalLocked = useRef(false);\n    const removalLocker = useMemo(() => ({\n        lock() { isRemovalLocked.current = true; },\n        unlock() { isRemovalLocked.current = false; },\n    }), []);\n    const element = useRef();\n    const hiddenNodeElement = useRef();\n    const removalListenerElement = useRef();\n    const onTemplateRemoved = useCallback((_, args) => {\n        if (args?.isUnmounting || isRemovalLocked.current) {\n            return;\n        }\n        if (element.current) {\n            events.off(element.current, DX_REMOVE_EVENT, onTemplateRemoved);\n        }\n        if (removalListenerElement.current) {\n            events.off(removalListenerElement.current, DX_REMOVE_EVENT, onTemplateRemoved);\n        }\n        onRemoved();\n    }, [onRemoved]);\n    useLayoutEffect(() => {\n        const el = element.current;\n        if (el && el.nodeType === Node.ELEMENT_NODE) {\n            events.off(el, DX_REMOVE_EVENT, onTemplateRemoved);\n            events.on(el, DX_REMOVE_EVENT, onTemplateRemoved);\n        }\n        else if (!removalListenerRequired) {\n            setRemovalListenerRequired(true);\n        }\n        else if (removalListenerElement.current) {\n            events.off(removalListenerElement.current, DX_REMOVE_EVENT, onTemplateRemoved);\n            events.on(removalListenerElement.current, DX_REMOVE_EVENT, onTemplateRemoved);\n        }\n        return () => {\n            const safeAppend = (child) => {\n                if (child?.current && container && !container.contains(child.current)) {\n                    container.appendChild(child.current);\n                }\n            };\n            safeAppend(element);\n            safeAppend(hiddenNodeElement);\n            safeAppend(removalListenerElement);\n            if (el) {\n                events.off(el, DX_REMOVE_EVENT, onTemplateRemoved);\n            }\n        };\n    }, [onTemplateRemoved, removalListenerRequired, container]);\n    useEffect(() => {\n        onRendered();\n    }, [onRendered]);\n    const hiddenNode = createHiddenNode(container?.nodeName, (node) => {\n        hiddenNodeElement.current = node;\n        element.current = node?.previousSibling;\n    }, 'div');\n    const removalListener = removalListenerRequired\n        ? createHiddenNode(container?.nodeName, (node) => { removalListenerElement.current = node; }, 'span')\n        : undefined;\n    return createPortal(React.createElement(React.Fragment, null,\n        React.createElement(RemovalLockerContext.Provider, { value: removalLocker },\n            templateFactory({ data, index, onRendered }),\n            hiddenNode,\n            removalListener)), container);\n};\nexport const TemplateWrapper = memo(TemplateWrapperComponent);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAED;AACA;AAAA;AAEA;AACA;AACA;;;;;;;AACA,MAAM,mBAAmB,CAAC,mBAAmB,KAAK;IAC9C,MAAM,QAAQ;QAAE,SAAS;IAAO;IAChC,OAAQ;QACJ,KAAK;YACD,OAAO,6JAAA,CAAA,gBAAmB,CAAC,SAAS;gBAAE,OAAO;gBAAO,KAAK;YAAI;QACjE,KAAK;YACD,OAAO,6JAAA,CAAA,gBAAmB,CAAC,MAAM;gBAAE,OAAO;gBAAO,KAAK;YAAI;QAC9D;YACI,OAAO,6JAAA,CAAA,gBAAmB,CAAC,gBAAgB;gBAAE;gBAAO;YAAI;IAChE;AACJ;AACA,MAAM,2BAA2B;QAAC,EAAE,eAAe,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAG;IACjG,MAAM,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvE,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC/B,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2DAAE,IAAM,CAAC;gBACjC;oBAAS,gBAAgB,OAAO,GAAG;gBAAM;gBACzC;oBAAW,gBAAgB,OAAO,GAAG;gBAAO;YAChD,CAAC;0DAAG,EAAE;IACN,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACrB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,yBAAyB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACpC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mEAAE,CAAC,GAAG;YACtC,IAAI,CAAA,iBAAA,2BAAA,KAAM,YAAY,KAAI,gBAAgB,OAAO,EAAE;gBAC/C;YACJ;YACA,IAAI,QAAQ,OAAO,EAAE;gBACjB,gKAAA,CAAA,MAAU,CAAC,QAAQ,OAAO,EAAE,0KAAA,CAAA,kBAAe,EAAE;YACjD;YACA,IAAI,uBAAuB,OAAO,EAAE;gBAChC,gKAAA,CAAA,MAAU,CAAC,uBAAuB,OAAO,EAAE,0KAAA,CAAA,kBAAe,EAAE;YAChE;YACA;QACJ;kEAAG;QAAC;KAAU;IACd,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;oDAAE;YACZ,MAAM,KAAK,QAAQ,OAAO;YAC1B,IAAI,MAAM,GAAG,QAAQ,KAAK,KAAK,YAAY,EAAE;gBACzC,gKAAA,CAAA,MAAU,CAAC,IAAI,0KAAA,CAAA,kBAAe,EAAE;gBAChC,gKAAA,CAAA,KAAS,CAAC,IAAI,0KAAA,CAAA,kBAAe,EAAE;YACnC,OACK,IAAI,CAAC,yBAAyB;gBAC/B,2BAA2B;YAC/B,OACK,IAAI,uBAAuB,OAAO,EAAE;gBACrC,gKAAA,CAAA,MAAU,CAAC,uBAAuB,OAAO,EAAE,0KAAA,CAAA,kBAAe,EAAE;gBAC5D,gKAAA,CAAA,KAAS,CAAC,uBAAuB,OAAO,EAAE,0KAAA,CAAA,kBAAe,EAAE;YAC/D;YACA;4DAAO;oBACH,MAAM;+EAAa,CAAC;4BAChB,IAAI,CAAA,kBAAA,4BAAA,MAAO,OAAO,KAAI,aAAa,CAAC,UAAU,QAAQ,CAAC,MAAM,OAAO,GAAG;gCACnE,UAAU,WAAW,CAAC,MAAM,OAAO;4BACvC;wBACJ;;oBACA,WAAW;oBACX,WAAW;oBACX,WAAW;oBACX,IAAI,IAAI;wBACJ,gKAAA,CAAA,MAAU,CAAC,IAAI,0KAAA,CAAA,kBAAe,EAAE;oBACpC;gBACJ;;QACJ;mDAAG;QAAC;QAAmB;QAAyB;KAAU;IAC1D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;8CAAE;YACN;QACJ;6CAAG;QAAC;KAAW;IACf,MAAM,aAAa,iBAAiB,sBAAA,gCAAA,UAAW,QAAQ,EAAE,CAAC;QACtD,kBAAkB,OAAO,GAAG;QAC5B,QAAQ,OAAO,GAAG,iBAAA,2BAAA,KAAM,eAAe;IAC3C,GAAG;IACH,MAAM,kBAAkB,0BAClB,iBAAiB,sBAAA,gCAAA,UAAW,QAAQ,EAAE,CAAC;QAAW,uBAAuB,OAAO,GAAG;IAAM,GAAG,UAC5F;IACN,OAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MACpD,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,uBAAoB,CAAC,QAAQ,EAAE;QAAE,OAAO;IAAc,GACtE,gBAAgB;QAAE;QAAM;QAAO;IAAW,IAC1C,YACA,mBAAmB;AAC/B;AACO,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1137, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/config.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nlet config = {\n    useLegacyTemplateEngine: false,\n};\nfunction setOptions(options) {\n    config = { ...config, ...options };\n}\nfunction getOption(optionName) {\n    return config[optionName];\n}\nexport default setOptions;\nexport { getOption };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAED,IAAI,SAAS;IACT,yBAAyB;AAC7B;AACA,SAAS,WAAW,OAAO;IACvB,SAAS;QAAE,GAAG,MAAM;QAAE,GAAG,OAAO;IAAC;AACrC;AACA,SAAS,UAAU,UAAU;IACzB,OAAO,MAAM,CAAC,WAAW;AAC7B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1170, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/template-manager.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport * as events from 'devextreme/events';\nimport { useState, useMemo, useCallback, useEffect, useRef, } from 'react';\nimport { TemplateWrapper } from './template-wrapper';\nimport { TemplateInstantiationModels, generateID } from './helpers';\nimport { DX_REMOVE_EVENT } from './component-base';\nimport { getOption as getConfigOption } from './config';\nfunction normalizeProps(props) {\n    if (getConfigOption('useLegacyTemplateEngine')) {\n        const model = props.data;\n        if (model && Object.prototype.hasOwnProperty.call(model, 'key')) {\n            model.dxkey = model.key;\n        }\n        return model;\n    }\n    return props;\n}\nconst createMapKey = (key1, key2) => ({ key1, key2 });\nconst unsubscribeOnRemoval = (container, onRemoved) => {\n    if (container.nodeType === Node.ELEMENT_NODE) {\n        events.off(container, DX_REMOVE_EVENT, onRemoved);\n    }\n};\nconst subscribeOnRemoval = (container, onRemoved) => {\n    if (container.nodeType === Node.ELEMENT_NODE) {\n        events.on(container, DX_REMOVE_EVENT, onRemoved);\n    }\n};\nconst unwrapElement = (element) => (element.get ? element.get(0) : element);\nconst getRandomId = () => `${generateID()}${generateID()}${generateID()}`;\nexport const TemplateManager = ({ init, onTemplatesRendered }) => {\n    const mounted = useRef(false);\n    const [instantiationModels, setInstantiationModels] = useState({\n        collection: new TemplateInstantiationModels(),\n    });\n    const [updateContext, setUpdateContext] = useState();\n    const widgetId = useRef('');\n    const templateFactories = useRef({});\n    const { collection } = instantiationModels;\n    const getRenderFunc = useCallback((templateKey) => ({ model: data, index, container, onRendered, }) => {\n        const containerElement = unwrapElement(container);\n        const key = createMapKey(data, containerElement);\n        const onRemoved = () => {\n            if (collection.get(key)) {\n                collection.delete(key);\n                setInstantiationModels({ collection });\n            }\n        };\n        const hostWidgetId = widgetId.current;\n        collection.set(key, {\n            templateKey,\n            index,\n            componentKey: getRandomId(),\n            onRendered: () => {\n                unsubscribeOnRemoval(containerElement, onRemoved);\n                if (hostWidgetId === widgetId.current) {\n                    onRendered?.();\n                }\n            },\n            onRemoved,\n        });\n        setInstantiationModels({ collection });\n        return containerElement;\n    }, [collection]);\n    useMemo(() => {\n        function getTemplateFunction(template) {\n            switch (template.type) {\n                case 'children': return () => template.content;\n                case 'render': return (props) => {\n                    normalizeProps(props);\n                    return template.content(props.data, props.index);\n                };\n                case 'component': return (props) => {\n                    props = normalizeProps(props);\n                    return React.createElement.bind(null, template.content)(props);\n                };\n                default: return () => React.createElement(React.Fragment);\n            }\n        }\n        function createDXTemplates(templateOptions) {\n            const factories = Object.entries(templateOptions)\n                .reduce((res, [key, template]) => ({\n                ...res,\n                [key]: getTemplateFunction(template),\n            }), {});\n            templateFactories.current = factories;\n            const dxTemplates = Object.keys(factories)\n                .reduce((templates, templateKey) => {\n                templates[templateKey] = { render: getRenderFunc(templateKey) };\n                return templates;\n            }, {});\n            return dxTemplates;\n        }\n        function clearInstantiationModels() {\n            widgetId.current = getRandomId();\n            instantiationModels.collection.clear();\n            setInstantiationModels({ ...instantiationModels });\n        }\n        function updateTemplates(onUpdated) {\n            if (mounted.current) {\n                setUpdateContext({ onUpdated });\n            }\n        }\n        init({ createDXTemplates, clearInstantiationModels, updateTemplates });\n    }, [init, getRenderFunc]);\n    useEffect(() => {\n        mounted.current = true;\n        return () => {\n            mounted.current = false;\n        };\n    }, []);\n    useEffect(() => {\n        if (updateContext) {\n            updateContext.onUpdated();\n        }\n        onTemplatesRendered();\n    }, [updateContext, onTemplatesRendered]);\n    if (instantiationModels.collection.empty) {\n        return null;\n    }\n    return (React.createElement(React.Fragment, null, Array.from(instantiationModels.collection).map(([{ key1: data, key2: container }, { index, templateKey, componentKey, onRendered, onRemoved, }]) => {\n        subscribeOnRemoval(container, onRemoved);\n        const factory = templateFactories.current[templateKey];\n        if (factory) {\n            return React.createElement(TemplateWrapper, { key: componentKey, templateFactory: factory, data: data, index: index, container: container, onRemoved: onRemoved, onRendered: onRendered });\n        }\n        return null;\n    })));\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAED;AACA;AAAA;AAEA;AACA;AACA;AACA;;;;;;;;AACA,SAAS,eAAe,KAAK;IACzB,IAAI,CAAA,GAAA,+JAAA,CAAA,YAAe,AAAD,EAAE,4BAA4B;QAC5C,MAAM,QAAQ,MAAM,IAAI;QACxB,IAAI,SAAS,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,QAAQ;YAC7D,MAAM,KAAK,GAAG,MAAM,GAAG;QAC3B;QACA,OAAO;IACX;IACA,OAAO;AACX;AACA,MAAM,eAAe,CAAC,MAAM,OAAS,CAAC;QAAE;QAAM;IAAK,CAAC;AACpD,MAAM,uBAAuB,CAAC,WAAW;IACrC,IAAI,UAAU,QAAQ,KAAK,KAAK,YAAY,EAAE;QAC1C,gKAAA,CAAA,MAAU,CAAC,WAAW,0KAAA,CAAA,kBAAe,EAAE;IAC3C;AACJ;AACA,MAAM,qBAAqB,CAAC,WAAW;IACnC,IAAI,UAAU,QAAQ,KAAK,KAAK,YAAY,EAAE;QAC1C,gKAAA,CAAA,KAAS,CAAC,WAAW,0KAAA,CAAA,kBAAe,EAAE;IAC1C;AACJ;AACA,MAAM,gBAAgB,CAAC,UAAa,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC,KAAK;AACnE,MAAM,cAAc,IAAM,AAAC,GAAiB,OAAf,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,KAAqB,OAAf,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,KAAmB,OAAb,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD;AAC7D,MAAM,kBAAkB;QAAC,EAAE,IAAI,EAAE,mBAAmB,EAAE;IACzD,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3D,YAAY,IAAI,gKAAA,CAAA,8BAA2B;IAC/C;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;IACjD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACxB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,CAAC;IAClC,MAAM,EAAE,UAAU,EAAE,GAAG;IACvB,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;8DAAgB;wBAAC,EAAE,OAAO,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,UAAU,EAAG;oBAC9F,MAAM,mBAAmB,cAAc;oBACvC,MAAM,MAAM,aAAa,MAAM;oBAC/B,MAAM;gFAAY;4BACd,IAAI,WAAW,GAAG,CAAC,MAAM;gCACrB,WAAW,MAAM,CAAC;gCAClB,uBAAuB;oCAAE;gCAAW;4BACxC;wBACJ;;oBACA,MAAM,eAAe,SAAS,OAAO;oBACrC,WAAW,GAAG,CAAC,KAAK;wBAChB;wBACA;wBACA,cAAc;wBACd,UAAU;0EAAE;gCACR,qBAAqB,kBAAkB;gCACvC,IAAI,iBAAiB,SAAS,OAAO,EAAE;oCACnC,uBAAA,iCAAA;gCACJ;4BACJ;;wBACA;oBACJ;oBACA,uBAAuB;wBAAE;oBAAW;oBACpC,OAAO;gBACX;;qDAAG;QAAC;KAAW;IACf,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE;YACJ,SAAS,oBAAoB,QAAQ;gBACjC,OAAQ,SAAS,IAAI;oBACjB,KAAK;wBAAY;2EAAO,IAAM,SAAS,OAAO;;oBAC9C,KAAK;wBAAU;2EAAO,CAAC;gCACnB,eAAe;gCACf,OAAO,SAAS,OAAO,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK;4BACnD;;oBACA,KAAK;wBAAa;2EAAO,CAAC;gCACtB,QAAQ,eAAe;gCACvB,OAAO,6JAAA,CAAA,gBAAmB,CAAC,IAAI,CAAC,MAAM,SAAS,OAAO,EAAE;4BAC5D;;oBACA;wBAAS;2EAAO,IAAM,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc;;gBAC5D;YACJ;YACA,SAAS,kBAAkB,eAAe;gBACtC,MAAM,YAAY,OAAO,OAAO,CAAC,iBAC5B,MAAM;2EAAC,CAAC;4BAAK,CAAC,KAAK,SAAS;+BAAM;4BACnC,GAAG,GAAG;4BACN,CAAC,IAAI,EAAE,oBAAoB;wBAC/B;;0EAAI,CAAC;gBACL,kBAAkB,OAAO,GAAG;gBAC5B,MAAM,cAAc,OAAO,IAAI,CAAC,WAC3B,MAAM;6EAAC,CAAC,WAAW;wBACpB,SAAS,CAAC,YAAY,GAAG;4BAAE,QAAQ,cAAc;wBAAa;wBAC9D,OAAO;oBACX;4EAAG,CAAC;gBACJ,OAAO;YACX;YACA,SAAS;gBACL,SAAS,OAAO,GAAG;gBACnB,oBAAoB,UAAU,CAAC,KAAK;gBACpC,uBAAuB;oBAAE,GAAG,mBAAmB;gBAAC;YACpD;YACA,SAAS,gBAAgB,SAAS;gBAC9B,IAAI,QAAQ,OAAO,EAAE;oBACjB,iBAAiB;wBAAE;oBAAU;gBACjC;YACJ;YACA,KAAK;gBAAE;gBAAmB;gBAA0B;YAAgB;QACxE;kCAAG;QAAC;QAAM;KAAc;IACxB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN,QAAQ,OAAO,GAAG;YAClB;6CAAO;oBACH,QAAQ,OAAO,GAAG;gBACtB;;QACJ;oCAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACN,IAAI,eAAe;gBACf,cAAc,SAAS;YAC3B;YACA;QACJ;oCAAG;QAAC;QAAe;KAAoB;IACvC,IAAI,oBAAoB,UAAU,CAAC,KAAK,EAAE;QACtC,OAAO;IACX;IACA,OAAQ,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,MAAM,MAAM,IAAI,CAAC,oBAAoB,UAAU,EAAE,GAAG,CAAC;YAAC,CAAC,EAAE,MAAM,IAAI,EAAE,MAAM,SAAS,EAAE,EAAE,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAG,CAAC;QAC7L,mBAAmB,WAAW;QAC9B,MAAM,UAAU,kBAAkB,OAAO,CAAC,YAAY;QACtD,IAAI,SAAS;YACT,OAAO,6JAAA,CAAA,gBAAmB,CAAC,4KAAA,CAAA,kBAAe,EAAE;gBAAE,KAAK;gBAAc,iBAAiB;gBAAS,MAAM;gBAAM,OAAO;gBAAO,WAAW;gBAAW,WAAW;gBAAW,YAAY;YAAW;QAC5L;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1393, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/template.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n'use client';\nimport { memo, useContext, useLayoutEffect } from 'react';\nimport { NestedOptionContext, TemplateRenderingContext } from './contexts';\nimport { getNamedTemplate } from './configuration/react/templates';\nconst Template = memo((props) => {\n    const { onNamedTemplateReady, treeUpdateToken, } = useContext(NestedOptionContext);\n    const { isTemplateRendering } = useContext(TemplateRenderingContext);\n    const template = getNamedTemplate(props);\n    useLayoutEffect(() => {\n        if (!isTemplateRendering) {\n            onNamedTemplateReady(template, treeUpdateToken);\n        }\n    }, [treeUpdateToken]);\n    return null;\n});\nfunction findProps(child) {\n    if (child.type !== Template) {\n        return undefined;\n    }\n    return {\n        name: child.props.name,\n        render: child.props.render,\n        component: child.props.component,\n        children: child.props.children,\n    };\n}\nexport { Template, findProps, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAGD;AACA;AACA;AAHA;;;;AAIA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAC;IACnB,MAAM,EAAE,oBAAoB,EAAE,eAAe,EAAG,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,sBAAmB;IACjF,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,2BAAwB;IACnE,MAAM,WAAW,CAAA,GAAA,4LAAA,CAAA,mBAAgB,AAAD,EAAE;IAClC,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;oCAAE;YACZ,IAAI,CAAC,qBAAqB;gBACtB,qBAAqB,UAAU;YACnC;QACJ;mCAAG;QAAC;KAAgB;IACpB,OAAO;AACX;AACA,SAAS,UAAU,KAAK;IACpB,IAAI,MAAM,IAAI,KAAK,UAAU;QACzB,OAAO;IACX;IACA,OAAO;QACH,MAAM,MAAM,KAAK,CAAC,IAAI;QACtB,QAAQ,MAAM,KAAK,CAAC,MAAM;QAC1B,WAAW,MAAM,KAAK,CAAC,SAAS;QAChC,UAAU,MAAM,KAAK,CAAC,QAAQ;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1446, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/configuration/react/element.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport { Template as TemplateComponent } from '../../template';\nvar ElementType;\n(function (ElementType) {\n    ElementType[ElementType[\"Option\"] = 0] = \"Option\";\n    ElementType[ElementType[\"Template\"] = 1] = \"Template\";\n    ElementType[ElementType[\"Unknown\"] = 2] = \"Unknown\";\n})(ElementType || (ElementType = {}));\nfunction getOptionInfo(elementDescriptor, props, parentExpectedChildren) {\n    let name = elementDescriptor.OptionName;\n    let isCollectionItem = elementDescriptor.IsCollectionItem;\n    const expectation = parentExpectedChildren && parentExpectedChildren[name];\n    if (expectation) {\n        isCollectionItem = expectation.isCollectionItem;\n        if (expectation.optionName) {\n            name = expectation.optionName;\n        }\n    }\n    return {\n        type: ElementType.Option,\n        descriptor: {\n            name,\n            isCollection: !!isCollectionItem,\n            templates: elementDescriptor.TemplateProps || [],\n            initialValuesProps: elementDescriptor.DefaultsProps || {},\n            predefinedValuesProps: elementDescriptor.PredefinedProps || {},\n            expectedChildren: elementDescriptor.ExpectedChildren || {},\n        },\n        props,\n    };\n}\nfunction getElementType(element) {\n    const reactElement = element;\n    if (!reactElement || !reactElement.type) {\n        return ElementType.Unknown;\n    }\n    if (reactElement.type === TemplateComponent) {\n        return ElementType.Template;\n    }\n    const nestedComponentMeta = reactElement.type;\n    if (nestedComponentMeta.componentType === 'option') {\n        return ElementType.Option;\n    }\n    return ElementType.Unknown;\n}\nexport { getElementType, getOptionInfo, ElementType, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;AAED;;AACA,IAAI;AACJ,CAAC,SAAU,WAAW;IAClB,WAAW,CAAC,WAAW,CAAC,SAAS,GAAG,EAAE,GAAG;IACzC,WAAW,CAAC,WAAW,CAAC,WAAW,GAAG,EAAE,GAAG;IAC3C,WAAW,CAAC,WAAW,CAAC,UAAU,GAAG,EAAE,GAAG;AAC9C,CAAC,EAAE,eAAe,CAAC,cAAc,CAAC,CAAC;AACnC,SAAS,cAAc,iBAAiB,EAAE,KAAK,EAAE,sBAAsB;IACnE,IAAI,OAAO,kBAAkB,UAAU;IACvC,IAAI,mBAAmB,kBAAkB,gBAAgB;IACzD,MAAM,cAAc,0BAA0B,sBAAsB,CAAC,KAAK;IAC1E,IAAI,aAAa;QACb,mBAAmB,YAAY,gBAAgB;QAC/C,IAAI,YAAY,UAAU,EAAE;YACxB,OAAO,YAAY,UAAU;QACjC;IACJ;IACA,OAAO;QACH,MAAM,YAAY,MAAM;QACxB,YAAY;YACR;YACA,cAAc,CAAC,CAAC;YAChB,WAAW,kBAAkB,aAAa,IAAI,EAAE;YAChD,oBAAoB,kBAAkB,aAAa,IAAI,CAAC;YACxD,uBAAuB,kBAAkB,eAAe,IAAI,CAAC;YAC7D,kBAAkB,kBAAkB,gBAAgB,IAAI,CAAC;QAC7D;QACA;IACJ;AACJ;AACA,SAAS,eAAe,OAAO;IAC3B,MAAM,eAAe;IACrB,IAAI,CAAC,gBAAgB,CAAC,aAAa,IAAI,EAAE;QACrC,OAAO,YAAY,OAAO;IAC9B;IACA,IAAI,aAAa,IAAI,KAAK,iKAAA,CAAA,WAAiB,EAAE;QACzC,OAAO,YAAY,QAAQ;IAC/B;IACA,MAAM,sBAAsB,aAAa,IAAI;IAC7C,IAAI,oBAAoB,aAAa,KAAK,UAAU;QAChD,OAAO,YAAY,MAAM;IAC7B;IACA,OAAO,YAAY,OAAO;AAC9B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1512, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/const.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nexport const UNITLESS_NUMBERS_SET = new Set([\n    'animationIterationCount',\n    'aspectRatio',\n    'borderImageOutset',\n    'borderImageSlice',\n    'borderImageWidth',\n    'boxFlex',\n    'boxFlexGroup',\n    'boxOrdinalGroup',\n    'columnCount',\n    'columns',\n    'flex',\n    'flexGrow',\n    'flexPositive',\n    'flexShrink',\n    'flexNegative',\n    'flexOrder',\n    'gridArea',\n    'gridRow',\n    'gridRowEnd',\n    'gridRowSpan',\n    'gridRowStart',\n    'gridColumn',\n    'gridColumnEnd',\n    'gridColumnSpan',\n    'gridColumnStart',\n    'fontWeight',\n    'lineClamp',\n    'lineHeight',\n    'opacity',\n    'order',\n    'orphans',\n    'scale',\n    'tabSize',\n    'widows',\n    'zIndex',\n    'zoom',\n    'fillOpacity',\n    'floodOpacity',\n    'stopOpacity',\n    'strokeDasharray',\n    'strokeDashoffset',\n    'strokeMiterlimit',\n    'strokeOpacity',\n    'strokeWidth',\n    'MozAnimationIterationCount',\n    'MozBoxFlex',\n    'MozBoxFlexGroup',\n    'MozLineClamp',\n    'msAnimationIterationCount',\n    'msFlex',\n    'msZoom',\n    'msFlexGrow',\n    'msFlexNegative',\n    'msFlexOrder',\n    'msFlexPositive',\n    'msFlexShrink',\n    'msGridColumn',\n    'msGridColumnSpan',\n    'msGridRow',\n    'msGridRowSpan',\n    'WebkitAnimationIterationCount',\n    'WebkitBoxFlex',\n    'WebKitBoxFlexGroup',\n    'WebkitBoxOrdinalGroup',\n    'WebkitColumnCount',\n    'WebkitColumns',\n    'WebkitFlex',\n    'WebkitFlexGrow',\n    'WebkitFlexPositive',\n    'WebkitFlexShrink',\n    'WebkitLineClamp',\n]);\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAEM,MAAM,uBAAuB,IAAI,IAAI;IACxC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1603, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/component-base.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport * as events from 'devextreme/events';\nimport { useContext, useMemo, useImperativeHandle, forwardRef, useRef, useLayoutEffect, useCallback, useState, } from 'react';\nimport { requestAnimationFrame } from 'devextreme/animation/frame';\nimport { deferUpdate } from 'devextreme/core/utils/common';\nimport config from 'devextreme/core/config';\nimport { createPortal } from 'react-dom';\nimport { useOptionScanning } from './use-option-scanning';\nimport { OptionsManager, scheduleGuards, unscheduleGuards } from './options-manager';\nimport { elementPropNames, getClassName } from './widget-config';\nimport { TemplateManager } from './template-manager';\nimport { ElementType } from './configuration/react/element';\nimport { NestedOptionContext, RemovalLockerContext, RestoreTreeContext, TemplateRenderingContext, } from './contexts';\nimport { UNITLESS_NUMBERS_SET } from './const';\nconst DX_REMOVE_EVENT = 'dxremove';\nconfig({\n    buyNowLink: 'https://go.devexpress.com/Licensing_Installer_Watermark_DevExtremeReact.aspx',\n    licensingDocLink: 'https://go.devexpress.com/Licensing_Documentation_DevExtremeReact.aspx',\n});\nconst ComponentBase = forwardRef((props, ref) => {\n    const { templateProps = [], defaults = {}, expectedChildren = {}, isPortalComponent = false, useRequestAnimationFrameFlag = false, subscribableOptions = [], WidgetClass, independentEvents = [], renderChildren, beforeCreateWidget = () => undefined, afterCreateWidget = () => undefined, } = props;\n    const [, setForceUpdateToken] = useState(Symbol('initial force update token'));\n    const removalLocker = useContext(RemovalLockerContext);\n    const restoreParentLink = useContext(RestoreTreeContext);\n    const instance = useRef();\n    const element = useRef();\n    const portalContainer = useRef();\n    const useDeferUpdateForTemplates = useRef(false);\n    const guardsUpdateScheduled = useRef(false);\n    const childElementsDetached = useRef(false);\n    const shouldRestoreFocus = useRef(false);\n    const optionsManager = useRef(new OptionsManager());\n    const childNodes = useRef();\n    const createDXTemplates = useRef();\n    const clearInstantiationModels = useRef();\n    const updateTemplates = useRef();\n    const prevPropsRef = useRef();\n    const childrenContainerRef = useRef(null);\n    const { parentType } = useContext(NestedOptionContext);\n    const [widgetConfig, context] = useOptionScanning({\n        type: ElementType.Option,\n        descriptor: {\n            name: '',\n            isCollection: false,\n            templates: templateProps,\n            initialValuesProps: defaults,\n            predefinedValuesProps: {},\n            expectedChildren,\n        },\n        props,\n    }, () => !!childrenContainerRef.current?.childNodes.length, Symbol('initial update token'), 'component');\n    const restoreTree = useCallback(() => {\n        if (childElementsDetached.current && childNodes.current?.length && element.current) {\n            element.current.append(...childNodes.current);\n            childElementsDetached.current = false;\n        }\n        if (restoreParentLink && element.current && !element.current.isConnected) {\n            restoreParentLink();\n        }\n    }, [\n        childNodes.current,\n        element.current,\n        childElementsDetached.current,\n        restoreParentLink,\n    ]);\n    const updateCssClasses = useCallback((prevProps, newProps) => {\n        const prevClassName = prevProps ? getClassName(prevProps) : undefined;\n        const newClassName = getClassName(newProps);\n        if (prevClassName === newClassName) {\n            return;\n        }\n        if (prevClassName) {\n            const classNames = prevClassName.split(' ').filter((c) => c);\n            if (classNames.length) {\n                element.current?.classList.remove(...classNames);\n            }\n        }\n        if (newClassName) {\n            const classNames = newClassName.split(' ').filter((c) => c);\n            if (classNames.length) {\n                element.current?.classList.add(...classNames);\n            }\n        }\n    }, [element.current]);\n    const setInlineStyles = useCallback((styles) => {\n        if (element.current) {\n            const el = element.current;\n            Object.entries(styles).forEach(([name, value]) => {\n                if (typeof value === 'number' && !UNITLESS_NUMBERS_SET.has(name)) {\n                    el.style[name] = `${value}px`;\n                }\n                else {\n                    el.style[name] = value;\n                }\n            });\n        }\n    }, [element.current]);\n    const setTemplateManagerHooks = useCallback(({ createDXTemplates: createDXTemplatesFn, clearInstantiationModels: clearInstantiationModelsFn, updateTemplates: updateTemplatesFn, }) => {\n        createDXTemplates.current = createDXTemplatesFn;\n        clearInstantiationModels.current = clearInstantiationModelsFn;\n        updateTemplates.current = updateTemplatesFn;\n    }, [\n        createDXTemplates.current,\n        clearInstantiationModels.current,\n        updateTemplates.current,\n    ]);\n    const getElementProps = useCallback(() => {\n        const elementProps = {\n            ref: (el) => {\n                if (el) {\n                    element.current = el;\n                }\n            },\n        };\n        elementPropNames.forEach((name) => {\n            if (name in props) {\n                elementProps[name] = props[name];\n            }\n        });\n        return elementProps;\n    }, [element.current, props]);\n    const scheduleTemplatesUpdate = useCallback(() => {\n        if (guardsUpdateScheduled.current) {\n            return;\n        }\n        guardsUpdateScheduled.current = true;\n        const updateFunc = useDeferUpdateForTemplates.current ? deferUpdate : requestAnimationFrame;\n        updateFunc(() => {\n            guardsUpdateScheduled.current = false;\n            updateTemplates.current?.(() => scheduleGuards());\n        });\n        unscheduleGuards();\n    }, [\n        guardsUpdateScheduled.current,\n        useDeferUpdateForTemplates.current,\n        updateTemplates.current,\n    ]);\n    const createWidget = useCallback((el) => {\n        beforeCreateWidget();\n        el = el || element.current;\n        let options = {\n            templatesRenderAsynchronously: true,\n            ...optionsManager.current.getInitialOptions(widgetConfig),\n        };\n        const templateOptions = optionsManager.current.getTemplateOptions(widgetConfig);\n        const dxTemplates = createDXTemplates.current?.(templateOptions);\n        if (dxTemplates && Object.keys(dxTemplates).length) {\n            options = {\n                ...options,\n                integrationOptions: {\n                    templates: dxTemplates,\n                },\n            };\n        }\n        clearInstantiationModels.current?.();\n        instance.current = new WidgetClass(el, options);\n        if (!useRequestAnimationFrameFlag) {\n            useDeferUpdateForTemplates.current = instance.current.option('integrationOptions.useDeferUpdateForTemplates');\n        }\n        optionsManager.current.setInstance(instance.current, widgetConfig, subscribableOptions, independentEvents);\n        instance.current.on('optionChanged', optionsManager.current.onOptionChanged);\n        afterCreateWidget();\n    }, [\n        beforeCreateWidget,\n        afterCreateWidget,\n        element.current,\n        optionsManager.current,\n        createDXTemplates.current,\n        clearInstantiationModels.current,\n        WidgetClass,\n        useRequestAnimationFrameFlag,\n        useDeferUpdateForTemplates.current,\n        instance.current,\n        subscribableOptions,\n        independentEvents,\n        widgetConfig,\n    ]);\n    const onTemplatesRendered = useCallback(() => {\n        if (shouldRestoreFocus.current && instance.current?.focus) {\n            instance.current.focus();\n            shouldRestoreFocus.current = false;\n        }\n    }, [shouldRestoreFocus.current, instance.current]);\n    const onComponentUpdated = useCallback(() => {\n        if (parentType === 'option') {\n            return;\n        }\n        if (!optionsManager.current?.isInstanceSet) {\n            return;\n        }\n        updateCssClasses(prevPropsRef.current, props);\n        const templateOptions = optionsManager.current.getTemplateOptions(widgetConfig);\n        const dxTemplates = createDXTemplates.current?.(templateOptions) || {};\n        optionsManager.current.update(widgetConfig, dxTemplates);\n        scheduleTemplatesUpdate();\n        prevPropsRef.current = props;\n    }, [\n        optionsManager.current,\n        prevPropsRef.current,\n        createDXTemplates.current,\n        scheduleTemplatesUpdate,\n        updateCssClasses,\n        props,\n        widgetConfig,\n    ]);\n    const onComponentMounted = useCallback(() => {\n        if (parentType === 'option') {\n            return;\n        }\n        const { style } = props;\n        if (childElementsDetached.current) {\n            restoreTree();\n        }\n        else if (element.current?.childNodes.length) {\n            childNodes.current = Array.from(element.current?.childNodes);\n        }\n        updateCssClasses(undefined, props);\n        if (style) {\n            setInlineStyles(style);\n        }\n        prevPropsRef.current = props;\n    }, [\n        childNodes.current,\n        element.current,\n        childElementsDetached.current,\n        updateCssClasses,\n        setInlineStyles,\n        props,\n    ]);\n    const onComponentUnmounted = useCallback(() => {\n        removalLocker?.lock();\n        if (instance.current) {\n            const dxRemoveArgs = { isUnmounting: true };\n            shouldRestoreFocus.current = !!element.current?.contains(document.activeElement);\n            childNodes.current?.forEach((child) => child.parentNode?.removeChild(child));\n            childElementsDetached.current = true;\n            if (element.current) {\n                const preventFocusOut = (e) => e.stopPropagation();\n                events.on(element.current, 'focusout', preventFocusOut);\n                events.triggerHandler(element.current, DX_REMOVE_EVENT, dxRemoveArgs);\n                events.off(element.current, 'focusout', preventFocusOut);\n            }\n            instance.current.dispose();\n            instance.current = null;\n        }\n        optionsManager.current.dispose();\n        removalLocker?.unlock();\n    }, [\n        removalLocker,\n        instance.current,\n        childNodes.current,\n        element.current,\n        optionsManager.current,\n        childElementsDetached.current,\n        shouldRestoreFocus.current,\n    ]);\n    useLayoutEffect(() => {\n        onComponentMounted();\n        return () => {\n            onComponentUnmounted();\n        };\n    }, []);\n    useLayoutEffect(() => {\n        onComponentUpdated();\n    });\n    useImperativeHandle(ref, () => ({\n        getInstance() {\n            return instance.current;\n        },\n        getElement() {\n            return element.current;\n        },\n        createWidget(el) {\n            createWidget(el);\n        },\n    }), [instance.current, element.current, createWidget]);\n    const _renderChildren = useCallback(() => {\n        if (renderChildren) {\n            return renderChildren();\n        }\n        const { children } = props;\n        return children;\n    }, [props, renderChildren]);\n    const renderPortal = useCallback(() => portalContainer.current && createPortal(_renderChildren(), portalContainer.current), [portalContainer.current, _renderChildren]);\n    const renderContent = useCallback(() => {\n        const { children } = props;\n        return isPortalComponent && children\n            ? React.createElement('div', {\n                ref: (node) => {\n                    if (node && portalContainer.current !== node) {\n                        portalContainer.current = node;\n                        setForceUpdateToken(Symbol('force update token'));\n                    }\n                },\n                style: { display: 'contents' },\n            })\n            : _renderChildren();\n    }, [\n        props,\n        isPortalComponent,\n        portalContainer.current,\n        _renderChildren,\n    ]);\n    const renderContextValue = useMemo(() => ({\n        isTemplateRendering: false,\n    }), []);\n    return (React.createElement(RestoreTreeContext.Provider, { value: restoreTree },\n        React.createElement(TemplateRenderingContext.Provider, { value: renderContextValue },\n            React.createElement(\"div\", { ref: childrenContainerRef, ...getElementProps() },\n                React.createElement(NestedOptionContext.Provider, { value: context }, renderContent()),\n                React.createElement(TemplateManager, { init: setTemplateManagerHooks, onTemplatesRendered: onTemplatesRendered }),\n                isPortalComponent\n                    && React.createElement(NestedOptionContext.Provider, { value: context }, renderPortal())))));\n});\nexport { ComponentBase, DX_REMOVE_EVENT, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAED;AACA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AACA,MAAM,kBAAkB;AACxB,CAAA,GAAA,sJAAA,CAAA,UAAM,AAAD,EAAE;IACH,YAAY;IACZ,kBAAkB;AACtB;AACA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACrC,MAAM,EAAE,gBAAgB,EAAE,EAAE,WAAW,CAAC,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAE,oBAAoB,KAAK,EAAE,+BAA+B,KAAK,EAAE,sBAAsB,EAAE,EAAE,WAAW,EAAE,oBAAoB,EAAE,EAAE,cAAc,EAAE,qBAAqB,IAAM,SAAS,EAAE,oBAAoB,IAAM,SAAS,EAAG,GAAG;IACjS,MAAM,GAAG,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,OAAO;IAChD,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,uBAAoB;IACrD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,qBAAkB;IACvD,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACtB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACrB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC7B,MAAM,6BAA6B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAC1C,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACrC,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAClC,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,IAAI,2KAAA,CAAA,iBAAc;IAChD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACxB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC/B,MAAM,2BAA2B,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IACtC,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC7B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD;IAC1B,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACpC,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,sBAAmB;IACrD,MAAM,CAAC,cAAc,QAAQ,GAAG,CAAA,GAAA,kLAAA,CAAA,oBAAiB,AAAD,EAAE;QAC9C,MAAM,0LAAA,CAAA,cAAW,CAAC,MAAM;QACxB,YAAY;YACR,MAAM;YACN,cAAc;YACd,WAAW;YACX,oBAAoB;YACpB,uBAAuB,CAAC;YACxB;QACJ;QACA;IACJ;2CAAG;gBAAQ;mBAAF,CAAC,GAAC,gCAAA,qBAAqB,OAAO,cAA5B,oDAAA,8BAA8B,UAAU,CAAC,MAAM;;0CAAE,OAAO,yBAAyB;IAC5F,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;gBACS;YAArC,IAAI,sBAAsB,OAAO,MAAI,sBAAA,WAAW,OAAO,cAAlB,0CAAA,oBAAoB,MAAM,KAAI,QAAQ,OAAO,EAAE;gBAChF,QAAQ,OAAO,CAAC,MAAM,IAAI,WAAW,OAAO;gBAC5C,sBAAsB,OAAO,GAAG;YACpC;YACA,IAAI,qBAAqB,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,WAAW,EAAE;gBACtE;YACJ;QACJ;iDAAG;QACC,WAAW,OAAO;QAClB,QAAQ,OAAO;QACf,sBAAsB,OAAO;QAC7B;KACH;IACD,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;uDAAE,CAAC,WAAW;YAC7C,MAAM,gBAAgB,YAAY,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE,aAAa;YAC5D,MAAM,eAAe,CAAA,GAAA,yKAAA,CAAA,eAAY,AAAD,EAAE;YAClC,IAAI,kBAAkB,cAAc;gBAChC;YACJ;YACA,IAAI,eAAe;gBACf,MAAM,aAAa,cAAc,KAAK,CAAC,KAAK,MAAM;8EAAC,CAAC,IAAM;;gBAC1D,IAAI,WAAW,MAAM,EAAE;wBACnB;qBAAA,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,SAAS,CAAC,MAAM,IAAI;gBACzC;YACJ;YACA,IAAI,cAAc;gBACd,MAAM,aAAa,aAAa,KAAK,CAAC,KAAK,MAAM;8EAAC,CAAC,IAAM;;gBACzD,IAAI,WAAW,MAAM,EAAE;wBACnB;qBAAA,oBAAA,QAAQ,OAAO,cAAf,wCAAA,kBAAiB,SAAS,CAAC,GAAG,IAAI;gBACtC;YACJ;QACJ;sDAAG;QAAC,QAAQ,OAAO;KAAC;IACpB,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YACjC,IAAI,QAAQ,OAAO,EAAE;gBACjB,MAAM,KAAK,QAAQ,OAAO;gBAC1B,OAAO,OAAO,CAAC,QAAQ,OAAO;kEAAC;4BAAC,CAAC,MAAM,MAAM;wBACzC,IAAI,OAAO,UAAU,YAAY,CAAC,8JAAA,CAAA,uBAAoB,CAAC,GAAG,CAAC,OAAO;4BAC9D,GAAG,KAAK,CAAC,KAAK,GAAG,AAAC,GAAQ,OAAN,OAAM;wBAC9B,OACK;4BACD,GAAG,KAAK,CAAC,KAAK,GAAG;wBACrB;oBACJ;;YACJ;QACJ;qDAAG;QAAC,QAAQ,OAAO;KAAC;IACpB,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;gBAAC,EAAE,mBAAmB,mBAAmB,EAAE,0BAA0B,0BAA0B,EAAE,iBAAiB,iBAAiB,EAAG;YAC9K,kBAAkB,OAAO,GAAG;YAC5B,yBAAyB,OAAO,GAAG;YACnC,gBAAgB,OAAO,GAAG;QAC9B;6DAAG;QACC,kBAAkB,OAAO;QACzB,yBAAyB,OAAO;QAChC,gBAAgB,OAAO;KAC1B;IACD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAChC,MAAM,eAAe;gBACjB,GAAG;kEAAE,CAAC;wBACF,IAAI,IAAI;4BACJ,QAAQ,OAAO,GAAG;wBACtB;oBACJ;;YACJ;YACA,yKAAA,CAAA,mBAAgB,CAAC,OAAO;8DAAC,CAAC;oBACtB,IAAI,QAAQ,OAAO;wBACf,YAAY,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;oBACpC;gBACJ;;YACA,OAAO;QACX;qDAAG;QAAC,QAAQ,OAAO;QAAE;KAAM;IAC3B,MAAM,0BAA0B,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;8DAAE;YACxC,IAAI,sBAAsB,OAAO,EAAE;gBAC/B;YACJ;YACA,sBAAsB,OAAO,GAAG;YAChC,MAAM,aAAa,2BAA2B,OAAO,GAAG,kLAAA,CAAA,cAAW,GAAG,4KAAA,CAAA,wBAAqB;YAC3F;sEAAW;wBAEP;oBADA,sBAAsB,OAAO,GAAG;qBAChC,2BAAA,gBAAgB,OAAO,cAAvB,+CAAA,8BAAA;8EAA0B,IAAM,CAAA,GAAA,2KAAA,CAAA,iBAAc,AAAD;;gBACjD;;YACA,CAAA,GAAA,2KAAA,CAAA,mBAAgB,AAAD;QACnB;6DAAG;QACC,sBAAsB,OAAO;QAC7B,2BAA2B,OAAO;QAClC,gBAAgB,OAAO;KAC1B;IACD,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,CAAC;gBAQV,4BASpB;YAhBA;YACA,KAAK,MAAM,QAAQ,OAAO;YAC1B,IAAI,UAAU;gBACV,+BAA+B;gBAC/B,GAAG,eAAe,OAAO,CAAC,iBAAiB,CAAC,aAAa;YAC7D;YACA,MAAM,kBAAkB,eAAe,OAAO,CAAC,kBAAkB,CAAC;YAClE,MAAM,eAAc,6BAAA,kBAAkB,OAAO,cAAzB,iDAAA,gCAAA,mBAA4B;YAChD,IAAI,eAAe,OAAO,IAAI,CAAC,aAAa,MAAM,EAAE;gBAChD,UAAU;oBACN,GAAG,OAAO;oBACV,oBAAoB;wBAChB,WAAW;oBACf;gBACJ;YACJ;aACA,oCAAA,yBAAyB,OAAO,cAAhC,wDAAA,uCAAA;YACA,SAAS,OAAO,GAAG,IAAI,YAAY,IAAI;YACvC,IAAI,CAAC,8BAA8B;gBAC/B,2BAA2B,OAAO,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC;YACjE;YACA,eAAe,OAAO,CAAC,WAAW,CAAC,SAAS,OAAO,EAAE,cAAc,qBAAqB;YACxF,SAAS,OAAO,CAAC,EAAE,CAAC,iBAAiB,eAAe,OAAO,CAAC,eAAe;YAC3E;QACJ;kDAAG;QACC;QACA;QACA,QAAQ,OAAO;QACf,eAAe,OAAO;QACtB,kBAAkB,OAAO;QACzB,yBAAyB,OAAO;QAChC;QACA;QACA,2BAA2B,OAAO;QAClC,SAAS,OAAO;QAChB;QACA;QACA;KACH;IACD,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;gBACF;YAAlC,IAAI,mBAAmB,OAAO,MAAI,oBAAA,SAAS,OAAO,cAAhB,wCAAA,kBAAkB,KAAK,GAAE;gBACvD,SAAS,OAAO,CAAC,KAAK;gBACtB,mBAAmB,OAAO,GAAG;YACjC;QACJ;yDAAG;QAAC,mBAAmB,OAAO;QAAE,SAAS,OAAO;KAAC;IACjD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;gBAI9B,yBAKe;YARpB,IAAI,eAAe,UAAU;gBACzB;YACJ;YACA,IAAI,GAAC,0BAAA,eAAe,OAAO,cAAtB,8CAAA,wBAAwB,aAAa,GAAE;gBACxC;YACJ;YACA,iBAAiB,aAAa,OAAO,EAAE;YACvC,MAAM,kBAAkB,eAAe,OAAO,CAAC,kBAAkB,CAAC;YAClE,MAAM,cAAc,EAAA,6BAAA,kBAAkB,OAAO,cAAzB,iDAAA,gCAAA,mBAA4B,qBAAoB,CAAC;YACrE,eAAe,OAAO,CAAC,MAAM,CAAC,cAAc;YAC5C;YACA,aAAa,OAAO,GAAG;QAC3B;wDAAG;QACC,eAAe,OAAO;QACtB,aAAa,OAAO;QACpB,kBAAkB,OAAO;QACzB;QACA;QACA;QACA;KACH;IACD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;yDAAE;gBAQ1B;YAPT,IAAI,eAAe,UAAU;gBACzB;YACJ;YACA,MAAM,EAAE,KAAK,EAAE,GAAG;YAClB,IAAI,sBAAsB,OAAO,EAAE;gBAC/B;YACJ,OACK,KAAI,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,UAAU,CAAC,MAAM,EAAE;oBACT;gBAAhC,WAAW,OAAO,GAAG,MAAM,IAAI,EAAC,oBAAA,QAAQ,OAAO,cAAf,wCAAA,kBAAiB,UAAU;YAC/D;YACA,iBAAiB,WAAW;YAC5B,IAAI,OAAO;gBACP,gBAAgB;YACpB;YACA,aAAa,OAAO,GAAG;QAC3B;wDAAG;QACC,WAAW,OAAO;QAClB,QAAQ,OAAO;QACf,sBAAsB,OAAO;QAC7B;QACA;QACA;KACH;IACD,MAAM,uBAAuB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACrC,0BAAA,oCAAA,cAAe,IAAI;YACnB,IAAI,SAAS,OAAO,EAAE;oBAEa,kBAC/B;gBAFA,MAAM,eAAe;oBAAE,cAAc;gBAAK;gBAC1C,mBAAmB,OAAO,GAAG,CAAC,GAAC,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,QAAQ,CAAC,SAAS,aAAa;iBAC/E,sBAAA,WAAW,OAAO,cAAlB,0CAAA,oBAAoB,OAAO;uEAAC,CAAC;4BAAU;gCAAA,oBAAA,MAAM,UAAU,cAAhB,wCAAA,kBAAkB,WAAW,CAAC;;;gBACrE,sBAAsB,OAAO,GAAG;gBAChC,IAAI,QAAQ,OAAO,EAAE;oBACjB,MAAM;2FAAkB,CAAC,IAAM,EAAE,eAAe;;oBAChD,gKAAA,CAAA,KAAS,CAAC,QAAQ,OAAO,EAAE,YAAY;oBACvC,iKAAA,CAAA,iBAAqB,CAAC,QAAQ,OAAO,EAAE,iBAAiB;oBACxD,gKAAA,CAAA,MAAU,CAAC,QAAQ,OAAO,EAAE,YAAY;gBAC5C;gBACA,SAAS,OAAO,CAAC,OAAO;gBACxB,SAAS,OAAO,GAAG;YACvB;YACA,eAAe,OAAO,CAAC,OAAO;YAC9B,0BAAA,oCAAA,cAAe,MAAM;QACzB;0DAAG;QACC;QACA,SAAS,OAAO;QAChB,WAAW,OAAO;QAClB,QAAQ,OAAO;QACf,eAAe,OAAO;QACtB,sBAAsB,OAAO;QAC7B,mBAAmB,OAAO;KAC7B;IACD,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;yCAAE;YACZ;YACA;iDAAO;oBACH;gBACJ;;QACJ;wCAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;yCAAE;YACZ;QACJ;;IACA,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;6CAAK,IAAM,CAAC;gBAC5B;oBACI,OAAO,SAAS,OAAO;gBAC3B;gBACA;oBACI,OAAO,QAAQ,OAAO;gBAC1B;gBACA,cAAa,EAAE;oBACX,aAAa;gBACjB;YACJ,CAAC;4CAAG;QAAC,SAAS,OAAO;QAAE,QAAQ,OAAO;QAAE;KAAa;IACrD,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE;YAChC,IAAI,gBAAgB;gBAChB,OAAO;YACX;YACA,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,OAAO;QACX;qDAAG;QAAC;QAAO;KAAe;IAC1B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,IAAM,gBAAgB,OAAO,IAAI,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,mBAAmB,gBAAgB,OAAO;kDAAG;QAAC,gBAAgB,OAAO;QAAE;KAAgB;IACtK,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE;YAC9B,MAAM,EAAE,QAAQ,EAAE,GAAG;YACrB,OAAO,qBAAqB,WACtB,6JAAA,CAAA,gBAAmB,CAAC,OAAO;gBACzB,GAAG;gEAAE,CAAC;wBACF,IAAI,QAAQ,gBAAgB,OAAO,KAAK,MAAM;4BAC1C,gBAAgB,OAAO,GAAG;4BAC1B,oBAAoB,OAAO;wBAC/B;oBACJ;;gBACA,OAAO;oBAAE,SAAS;gBAAW;YACjC,KACE;QACV;mDAAG;QACC;QACA;QACA,gBAAgB,OAAO;QACvB;KACH;IACD,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qDAAE,IAAM,CAAC;gBACtC,qBAAqB;YACzB,CAAC;oDAAG,EAAE;IACN,OAAQ,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,qBAAkB,CAAC,QAAQ,EAAE;QAAE,OAAO;IAAY,GAC1E,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,2BAAwB,CAAC,QAAQ,EAAE;QAAE,OAAO;IAAmB,GAC/E,6JAAA,CAAA,gBAAmB,CAAC,OAAO;QAAE,KAAK;QAAsB,GAAG,iBAAiB;IAAC,GACzE,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QAAE,OAAO;IAAQ,GAAG,kBACtE,6JAAA,CAAA,gBAAmB,CAAC,4KAAA,CAAA,kBAAe,EAAE;QAAE,MAAM;QAAyB,qBAAqB;IAAoB,IAC/G,qBACO,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QAAE,OAAO;IAAQ,GAAG;AAC7F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/extension-component.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport { useImperativeHandle, forwardRef, useRef, useLayoutEffect, useCallback, } from 'react';\nimport { ComponentBase } from './component-base';\n// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types\nfunction elementIsExtension(el) {\n    return el.type?.componentType === 'extension';\n}\nconst ExtensionComponent = forwardRef((props, ref) => {\n    const componentBaseRef = useRef(null);\n    const createWidget = useCallback((el) => {\n        componentBaseRef.current?.createWidget(el);\n    }, [componentBaseRef.current]);\n    useLayoutEffect(() => {\n        const { onMounted } = props;\n        if (onMounted) {\n            onMounted(createWidget);\n        }\n        else {\n            createWidget();\n        }\n    }, []);\n    useImperativeHandle(ref, () => ({\n        getInstance() {\n            return componentBaseRef.current?.getInstance();\n        },\n        getElement() {\n            return componentBaseRef.current?.getElement();\n        },\n        createWidget(el) {\n            createWidget(el);\n        },\n    }), [componentBaseRef.current, createWidget]);\n    return (React.createElement(ComponentBase, { ref: componentBaseRef, ...props }));\n});\nexport { ExtensionComponent, elementIsExtension, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAED;AAEA;;;;AACA,6EAA6E;AAC7E,SAAS,mBAAmB,EAAE;QACnB;IAAP,OAAO,EAAA,WAAA,GAAG,IAAI,cAAP,+BAAA,SAAS,aAAa,MAAK;AACtC;AACA,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC1C,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC;gBAC9B;aAAA,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,YAAY,CAAC;QAC3C;uDAAG;QAAC,iBAAiB,OAAO;KAAC;IAC7B,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;8CAAE;YACZ,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,IAAI,WAAW;gBACX,UAAU;YACd,OACK;gBACD;YACJ;QACJ;6CAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;kDAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,WAAW;gBAChD;gBACA;wBACW;oBAAP,QAAO,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,UAAU;gBAC/C;gBACA,cAAa,EAAE;oBACX,aAAa;gBACjB;YACJ,CAAC;iDAAG;QAAC,iBAAiB,OAAO;QAAE;KAAa;IAC5C,OAAQ,6JAAA,CAAA,gBAAmB,CAAC,0KAAA,CAAA,gBAAa,EAAE;QAAE,KAAK;QAAkB,GAAG,KAAK;IAAC;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2133, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/component.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport { useImperativeHandle, forwardRef, useRef, useLayoutEffect, useCallback, } from 'react';\nimport { ComponentBase } from './component-base';\nimport { elementIsExtension } from './extension-component';\nconst Component = forwardRef((props, ref) => {\n    const componentBaseRef = useRef(null);\n    const extensionCreators = useRef([]);\n    const registerExtension = useCallback((creator) => {\n        extensionCreators.current.push(creator);\n    }, [extensionCreators.current]);\n    const createExtensions = useCallback(() => {\n        extensionCreators.current.forEach((creator) => creator(componentBaseRef.current?.getElement()));\n    }, [extensionCreators.current, componentBaseRef.current]);\n    const renderChildren = useCallback(() => React.Children.map(props.children, (child) => {\n        if (React.isValidElement(child) && elementIsExtension(child)) {\n            return React.cloneElement(child, { onMounted: registerExtension });\n        }\n        return child;\n    }), [props, registerExtension]);\n    const createWidget = useCallback((el) => {\n        componentBaseRef.current?.createWidget(el);\n    }, [componentBaseRef.current]);\n    const clearExtensions = useCallback(() => {\n        if (props.clearExtensions) {\n            props.clearExtensions();\n        }\n        extensionCreators.current = [];\n    }, [\n        extensionCreators.current,\n        props.clearExtensions,\n    ]);\n    useLayoutEffect(() => {\n        createWidget();\n        createExtensions();\n        return () => {\n            clearExtensions();\n        };\n    }, []);\n    useImperativeHandle(ref, () => ({\n        getInstance() {\n            return componentBaseRef.current?.getInstance();\n        },\n        getElement() {\n            return componentBaseRef.current?.getElement();\n        },\n        createWidget(el) {\n            createWidget(el);\n        },\n        clearExtensions() {\n            clearExtensions();\n        },\n    }), [componentBaseRef.current, createWidget, clearExtensions]);\n    return (React.createElement(ComponentBase, { ref: componentBaseRef, renderChildren: renderChildren, ...props }));\n});\nexport { Component, };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAED;AAEA;AACA;;;;;AACA,MAAM,YAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACjC,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAChC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE,EAAE;IACnC,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;oDAAE,CAAC;YACnC,kBAAkB,OAAO,CAAC,IAAI,CAAC;QACnC;mDAAG;QAAC,kBAAkB,OAAO;KAAC;IAC9B,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE;YACjC,kBAAkB,OAAO,CAAC,OAAO;2DAAC,CAAC;wBAAoB;2BAAR,SAAQ,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,UAAU;;;QAC/F;kDAAG;QAAC,kBAAkB,OAAO;QAAE,iBAAiB,OAAO;KAAC;IACxD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iDAAE,IAAM,6JAAA,CAAA,WAAc,CAAC,GAAG,CAAC,MAAM,QAAQ;yDAAE,CAAC;oBACzE,IAAI,6JAAA,CAAA,iBAAoB,CAAC,UAAU,CAAA,GAAA,+KAAA,CAAA,qBAAkB,AAAD,EAAE,QAAQ;wBAC1D,OAAO,6JAAA,CAAA,eAAkB,CAAC,OAAO;4BAAE,WAAW;wBAAkB;oBACpE;oBACA,OAAO;gBACX;;gDAAI;QAAC;QAAO;KAAkB;IAC9B,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;+CAAE,CAAC;gBAC9B;aAAA,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,YAAY,CAAC;QAC3C;8CAAG;QAAC,iBAAiB,OAAO;KAAC;IAC7B,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;kDAAE;YAChC,IAAI,MAAM,eAAe,EAAE;gBACvB,MAAM,eAAe;YACzB;YACA,kBAAkB,OAAO,GAAG,EAAE;QAClC;iDAAG;QACC,kBAAkB,OAAO;QACzB,MAAM,eAAe;KACxB;IACD,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;qCAAE;YACZ;YACA;YACA;6CAAO;oBACH;gBACJ;;QACJ;oCAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;yCAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,WAAW;gBAChD;gBACA;wBACW;oBAAP,QAAO,4BAAA,iBAAiB,OAAO,cAAxB,gDAAA,0BAA0B,UAAU;gBAC/C;gBACA,cAAa,EAAE;oBACX,aAAa;gBACjB;gBACA;oBACI;gBACJ;YACJ,CAAC;wCAAG;QAAC,iBAAiB,OAAO;QAAE;QAAc;KAAgB;IAC7D,OAAQ,6JAAA,CAAA,gBAAmB,CAAC,0KAAA,CAAA,gBAAa,EAAE;QAAE,KAAK;QAAkB,gBAAgB;QAAgB,GAAG,KAAK;IAAC;AACjH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2255, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/core/nested-option.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\nimport * as React from 'react';\nimport { useContext, useLayoutEffect, useState, useMemo, } from 'react';\nimport { createPortal } from 'react-dom';\nimport { getOptionInfo, } from './configuration/react/element';\nimport { useOptionScanning } from './use-option-scanning';\nimport { NestedOptionContext, TemplateRenderingContext } from './contexts';\nimport { hasExpectedChildren } from './helpers';\nconst NestedOption = function NestedOption(props) {\n    const { children } = props;\n    const { elementDescriptor, ...restProps } = props;\n    const { isTemplateRendering } = useContext(TemplateRenderingContext);\n    if (!elementDescriptor || typeof document === 'undefined' || isTemplateRendering) {\n        return null;\n    }\n    const usesNamedTemplate = elementDescriptor.TemplateProps?.some((prop) => props[prop.tmplOption] && typeof props[prop.tmplOption] === 'string');\n    const { parentExpectedChildren, onChildOptionsReady: triggerParentOptionsReady, getOptionComponentKey, treeUpdateToken, } = useContext(NestedOptionContext);\n    const [optionComponentKey] = useState(getOptionComponentKey());\n    const optionElement = getOptionInfo(elementDescriptor, restProps, parentExpectedChildren);\n    const mainContainer = useMemo(() => document.createElement('div'), []);\n    const renderChildren = hasExpectedChildren(elementDescriptor) || usesNamedTemplate;\n    const getHasTemplate = renderChildren\n        ? () => !!mainContainer.childNodes.length\n        : () => !!children;\n    const [config, context,] = useOptionScanning(optionElement, getHasTemplate, treeUpdateToken, 'option');\n    useLayoutEffect(() => {\n        triggerParentOptionsReady(config, optionElement.descriptor, treeUpdateToken, optionComponentKey);\n    }, [treeUpdateToken]);\n    return renderChildren ? React.createElement(React.Fragment, {}, createPortal(React.createElement(NestedOptionContext.Provider, {\n        value: context,\n    }, children), mainContainer)) : null;\n};\nexport default NestedOption;\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;AAED;AAEA;AACA;AACA;AACA;AACA;;;;;;;;AACA,MAAM,eAAe,SAAS,aAAa,KAAK;QAOlB;IAN1B,MAAM,EAAE,QAAQ,EAAE,GAAG;IACrB,MAAM,EAAE,iBAAiB,EAAE,GAAG,WAAW,GAAG;IAC5C,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,2BAAwB;IACnE,IAAI,CAAC,qBAAqB,OAAO,aAAa,eAAe,qBAAqB;QAC9E,OAAO;IACX;IACA,MAAM,qBAAoB,mCAAA,kBAAkB,aAAa,cAA/B,uDAAA,iCAAiC,IAAI,CAAC,CAAC,OAAS,KAAK,CAAC,KAAK,UAAU,CAAC,IAAI,OAAO,KAAK,CAAC,KAAK,UAAU,CAAC,KAAK;IACtI,MAAM,EAAE,sBAAsB,EAAE,qBAAqB,yBAAyB,EAAE,qBAAqB,EAAE,eAAe,EAAG,GAAG,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,iKAAA,CAAA,sBAAmB;IAC1J,MAAM,CAAC,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACtC,MAAM,gBAAgB,CAAA,GAAA,0LAAA,CAAA,gBAAa,AAAD,EAAE,mBAAmB,WAAW;IAClE,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE,IAAM,SAAS,aAAa,CAAC;8CAAQ,EAAE;IACrE,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,sBAAmB,AAAD,EAAE,sBAAsB;IACjE,MAAM,iBAAiB,iBACjB,IAAM,CAAC,CAAC,cAAc,UAAU,CAAC,MAAM,GACvC,IAAM,CAAC,CAAC;IACd,MAAM,CAAC,QAAQ,QAAS,GAAG,CAAA,GAAA,kLAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe,gBAAgB,iBAAiB;IAC7F,CAAA,GAAA,6JAAA,CAAA,kBAAe,AAAD;wCAAE;YACZ,0BAA0B,QAAQ,cAAc,UAAU,EAAE,iBAAiB;QACjF;uCAAG;QAAC;KAAgB;IACpB,OAAO,iBAAiB,6JAAA,CAAA,gBAAmB,CAAC,6JAAA,CAAA,WAAc,EAAE,CAAC,GAAG,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,6JAAA,CAAA,gBAAmB,CAAC,iKAAA,CAAA,sBAAmB,CAAC,QAAQ,EAAE;QAC3H,OAAO;IACX,GAAG,WAAW,kBAAkB;AACpC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2316, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/data-grid.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n\"use client\";\nimport * as React from \"react\";\nimport { memo, forwardRef, useImperativeHandle, useRef, useMemo } from \"react\";\nimport dxDataGrid from \"devextreme/ui/data_grid\";\nimport { Component as BaseComponent } from \"./core/component\";\nimport NestedOption from \"./core/nested-option\";\nconst DataGrid = memo(forwardRef((props, ref) => {\n    const baseRef = useRef(null);\n    useImperativeHandle(ref, () => ({\n        instance() {\n            return baseRef.current?.getInstance();\n        }\n    }), [baseRef.current]);\n    const subscribableOptions = useMemo(() => ([\"columns\", \"editing\", \"editing.changes\", \"editing.editColumnName\", \"editing.editRowKey\", \"filterValue\", \"focusedColumnIndex\", \"focusedRowIndex\", \"focusedRowKey\", \"groupPanel\", \"groupPanel.visible\", \"paging\", \"paging.pageIndex\", \"paging.pageSize\", \"selectedRowKeys\", \"selectionFilter\", \"filterBuilder.value\", \"filterBuilderPopup.height\", \"filterBuilderPopup.position\", \"filterBuilderPopup.visible\", \"filterBuilderPopup.width\", \"filterPanel.filterEnabled\", \"editing.form.formData\", \"editing.popup.height\", \"editing.popup.position\", \"editing.popup.visible\", \"editing.popup.width\", \"searchPanel.text\"]), []);\n    const independentEvents = useMemo(() => ([\"onAdaptiveDetailRowPreparing\", \"onCellClick\", \"onCellDblClick\", \"onCellPrepared\", \"onContentReady\", \"onContextMenuPreparing\", \"onDataErrorOccurred\", \"onDisposing\", \"onEditCanceled\", \"onEditCanceling\", \"onEditingStart\", \"onEditorPrepared\", \"onEditorPreparing\", \"onExporting\", \"onFocusedCellChanging\", \"onFocusedRowChanging\", \"onInitialized\", \"onInitNewRow\", \"onKeyDown\", \"onRowClick\", \"onRowCollapsed\", \"onRowCollapsing\", \"onRowDblClick\", \"onRowExpanded\", \"onRowExpanding\", \"onRowInserted\", \"onRowInserting\", \"onRowPrepared\", \"onRowRemoved\", \"onRowRemoving\", \"onRowUpdated\", \"onRowUpdating\", \"onRowValidating\", \"onSaved\", \"onSaving\", \"onToolbarPreparing\"]), []);\n    const defaults = useMemo(() => ({\n        defaultColumns: \"columns\",\n        defaultEditing: \"editing\",\n        defaultFilterValue: \"filterValue\",\n        defaultFocusedColumnIndex: \"focusedColumnIndex\",\n        defaultFocusedRowIndex: \"focusedRowIndex\",\n        defaultFocusedRowKey: \"focusedRowKey\",\n        defaultGroupPanel: \"groupPanel\",\n        defaultPaging: \"paging\",\n        defaultSelectedRowKeys: \"selectedRowKeys\",\n        defaultSelectionFilter: \"selectionFilter\",\n    }), []);\n    const expectedChildren = useMemo(() => ({\n        column: { optionName: \"columns\", isCollectionItem: true },\n        columnChooser: { optionName: \"columnChooser\", isCollectionItem: false },\n        columnFixing: { optionName: \"columnFixing\", isCollectionItem: false },\n        dataGridHeaderFilter: { optionName: \"headerFilter\", isCollectionItem: false },\n        dataGridSelection: { optionName: \"selection\", isCollectionItem: false },\n        editing: { optionName: \"editing\", isCollectionItem: false },\n        export: { optionName: \"export\", isCollectionItem: false },\n        filterBuilder: { optionName: \"filterBuilder\", isCollectionItem: false },\n        filterBuilderPopup: { optionName: \"filterBuilderPopup\", isCollectionItem: false },\n        filterPanel: { optionName: \"filterPanel\", isCollectionItem: false },\n        filterRow: { optionName: \"filterRow\", isCollectionItem: false },\n        grouping: { optionName: \"grouping\", isCollectionItem: false },\n        groupPanel: { optionName: \"groupPanel\", isCollectionItem: false },\n        headerFilter: { optionName: \"headerFilter\", isCollectionItem: false },\n        keyboardNavigation: { optionName: \"keyboardNavigation\", isCollectionItem: false },\n        loadPanel: { optionName: \"loadPanel\", isCollectionItem: false },\n        masterDetail: { optionName: \"masterDetail\", isCollectionItem: false },\n        pager: { optionName: \"pager\", isCollectionItem: false },\n        paging: { optionName: \"paging\", isCollectionItem: false },\n        remoteOperations: { optionName: \"remoteOperations\", isCollectionItem: false },\n        rowDragging: { optionName: \"rowDragging\", isCollectionItem: false },\n        scrolling: { optionName: \"scrolling\", isCollectionItem: false },\n        searchPanel: { optionName: \"searchPanel\", isCollectionItem: false },\n        selection: { optionName: \"selection\", isCollectionItem: false },\n        sortByGroupSummaryInfo: { optionName: \"sortByGroupSummaryInfo\", isCollectionItem: true },\n        sorting: { optionName: \"sorting\", isCollectionItem: false },\n        stateStoring: { optionName: \"stateStoring\", isCollectionItem: false },\n        summary: { optionName: \"summary\", isCollectionItem: false },\n        toolbar: { optionName: \"toolbar\", isCollectionItem: false }\n    }), []);\n    const templateProps = useMemo(() => ([\n        {\n            tmplOption: \"dataRowTemplate\",\n            render: \"dataRowRender\",\n            component: \"dataRowComponent\"\n        },\n        {\n            tmplOption: \"rowTemplate\",\n            render: \"rowRender\",\n            component: \"rowComponent\"\n        },\n    ]), []);\n    return (React.createElement((BaseComponent), {\n        WidgetClass: dxDataGrid,\n        ref: baseRef,\n        useRequestAnimationFrameFlag: true,\n        subscribableOptions,\n        independentEvents,\n        defaults,\n        expectedChildren,\n        templateProps,\n        ...props,\n    }));\n}));\nconst _componentAnimation = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"animation\",\n            ExpectedChildren: {\n                hide: { optionName: \"hide\", isCollectionItem: false },\n                show: { optionName: \"show\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Animation = Object.assign(_componentAnimation, {\n    componentType: \"option\",\n});\nconst _componentAsyncRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"async\"\n            },\n        },\n    });\n};\nconst AsyncRule = Object.assign(_componentAsyncRule, {\n    componentType: \"option\",\n});\nconst _componentAt = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"at\",\n        },\n    });\n};\nconst At = Object.assign(_componentAt, {\n    componentType: \"option\",\n});\nconst _componentBoundaryOffset = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"boundaryOffset\",\n        },\n    });\n};\nconst BoundaryOffset = Object.assign(_componentBoundaryOffset, {\n    componentType: \"option\",\n});\nconst _componentButton = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"buttons\",\n            IsCollectionItem: true,\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst Button = Object.assign(_componentButton, {\n    componentType: \"option\",\n});\nconst _componentChange = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"changes\",\n            IsCollectionItem: true,\n        },\n    });\n};\nconst Change = Object.assign(_componentChange, {\n    componentType: \"option\",\n});\nconst _componentColCountByScreen = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"colCountByScreen\",\n        },\n    });\n};\nconst ColCountByScreen = Object.assign(_componentColCountByScreen, {\n    componentType: \"option\",\n});\nconst _componentCollision = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"collision\",\n        },\n    });\n};\nconst Collision = Object.assign(_componentCollision, {\n    componentType: \"option\",\n});\nconst _componentColumn = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"columns\",\n            IsCollectionItem: true,\n            DefaultsProps: {\n                defaultFilterValue: \"filterValue\",\n                defaultFilterValues: \"filterValues\",\n                defaultGroupIndex: \"groupIndex\",\n                defaultSelectedFilterOperation: \"selectedFilterOperation\",\n                defaultSortIndex: \"sortIndex\",\n                defaultSortOrder: \"sortOrder\",\n                defaultVisible: \"visible\",\n                defaultVisibleIndex: \"visibleIndex\"\n            },\n            ExpectedChildren: {\n                AsyncRule: { optionName: \"validationRules\", isCollectionItem: true },\n                button: { optionName: \"buttons\", isCollectionItem: true },\n                columnHeaderFilter: { optionName: \"headerFilter\", isCollectionItem: false },\n                columnLookup: { optionName: \"lookup\", isCollectionItem: false },\n                CompareRule: { optionName: \"validationRules\", isCollectionItem: true },\n                CustomRule: { optionName: \"validationRules\", isCollectionItem: true },\n                EmailRule: { optionName: \"validationRules\", isCollectionItem: true },\n                format: { optionName: \"format\", isCollectionItem: false },\n                formItem: { optionName: \"formItem\", isCollectionItem: false },\n                headerFilter: { optionName: \"headerFilter\", isCollectionItem: false },\n                lookup: { optionName: \"lookup\", isCollectionItem: false },\n                NumericRule: { optionName: \"validationRules\", isCollectionItem: true },\n                PatternRule: { optionName: \"validationRules\", isCollectionItem: true },\n                RangeRule: { optionName: \"validationRules\", isCollectionItem: true },\n                RequiredRule: { optionName: \"validationRules\", isCollectionItem: true },\n                StringLengthRule: { optionName: \"validationRules\", isCollectionItem: true },\n                validationRule: { optionName: \"validationRules\", isCollectionItem: true }\n            },\n            TemplateProps: [{\n                    tmplOption: \"cellTemplate\",\n                    render: \"cellRender\",\n                    component: \"cellComponent\"\n                }, {\n                    tmplOption: \"editCellTemplate\",\n                    render: \"editCellRender\",\n                    component: \"editCellComponent\"\n                }, {\n                    tmplOption: \"groupCellTemplate\",\n                    render: \"groupCellRender\",\n                    component: \"groupCellComponent\"\n                }, {\n                    tmplOption: \"headerCellTemplate\",\n                    render: \"headerCellRender\",\n                    component: \"headerCellComponent\"\n                }],\n        },\n    });\n};\nconst Column = Object.assign(_componentColumn, {\n    componentType: \"option\",\n});\nconst _componentColumnChooser = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"columnChooser\",\n            ExpectedChildren: {\n                columnChooserSearch: { optionName: \"search\", isCollectionItem: false },\n                columnChooserSelection: { optionName: \"selection\", isCollectionItem: false },\n                position: { optionName: \"position\", isCollectionItem: false },\n                search: { optionName: \"search\", isCollectionItem: false },\n                selection: { optionName: \"selection\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ColumnChooser = Object.assign(_componentColumnChooser, {\n    componentType: \"option\",\n});\nconst _componentColumnChooserSearch = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"search\",\n        },\n    });\n};\nconst ColumnChooserSearch = Object.assign(_componentColumnChooserSearch, {\n    componentType: \"option\",\n});\nconst _componentColumnChooserSelection = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"selection\",\n        },\n    });\n};\nconst ColumnChooserSelection = Object.assign(_componentColumnChooserSelection, {\n    componentType: \"option\",\n});\nconst _componentColumnFixing = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"columnFixing\",\n            ExpectedChildren: {\n                columnFixingTexts: { optionName: \"texts\", isCollectionItem: false },\n                icons: { optionName: \"icons\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ColumnFixing = Object.assign(_componentColumnFixing, {\n    componentType: \"option\",\n});\nconst _componentColumnFixingTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst ColumnFixingTexts = Object.assign(_componentColumnFixingTexts, {\n    componentType: \"option\",\n});\nconst _componentColumnHeaderFilter = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"headerFilter\",\n            ExpectedChildren: {\n                columnHeaderFilterSearch: { optionName: \"search\", isCollectionItem: false },\n                search: { optionName: \"search\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ColumnHeaderFilter = Object.assign(_componentColumnHeaderFilter, {\n    componentType: \"option\",\n});\nconst _componentColumnHeaderFilterSearch = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"search\",\n        },\n    });\n};\nconst ColumnHeaderFilterSearch = Object.assign(_componentColumnHeaderFilterSearch, {\n    componentType: \"option\",\n});\nconst _componentColumnLookup = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"lookup\",\n        },\n    });\n};\nconst ColumnLookup = Object.assign(_componentColumnLookup, {\n    componentType: \"option\",\n});\nconst _componentCompareRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"compare\"\n            },\n        },\n    });\n};\nconst CompareRule = Object.assign(_componentCompareRule, {\n    componentType: \"option\",\n});\nconst _componentCursorOffset = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"cursorOffset\",\n        },\n    });\n};\nconst CursorOffset = Object.assign(_componentCursorOffset, {\n    componentType: \"option\",\n});\nconst _componentCustomOperation = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"customOperations\",\n            IsCollectionItem: true,\n            TemplateProps: [{\n                    tmplOption: \"editorTemplate\",\n                    render: \"editorRender\",\n                    component: \"editorComponent\"\n                }],\n        },\n    });\n};\nconst CustomOperation = Object.assign(_componentCustomOperation, {\n    componentType: \"option\",\n});\nconst _componentCustomRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"custom\"\n            },\n        },\n    });\n};\nconst CustomRule = Object.assign(_componentCustomRule, {\n    componentType: \"option\",\n});\nconst _componentDataGridHeaderFilter = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"headerFilter\",\n            ExpectedChildren: {\n                dataGridHeaderFilterSearch: { optionName: \"search\", isCollectionItem: false },\n                dataGridHeaderFilterTexts: { optionName: \"texts\", isCollectionItem: false },\n                search: { optionName: \"search\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst DataGridHeaderFilter = Object.assign(_componentDataGridHeaderFilter, {\n    componentType: \"option\",\n});\nconst _componentDataGridHeaderFilterSearch = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"search\",\n        },\n    });\n};\nconst DataGridHeaderFilterSearch = Object.assign(_componentDataGridHeaderFilterSearch, {\n    componentType: \"option\",\n});\nconst _componentDataGridHeaderFilterTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst DataGridHeaderFilterTexts = Object.assign(_componentDataGridHeaderFilterTexts, {\n    componentType: \"option\",\n});\nconst _componentDataGridSelection = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"selection\",\n        },\n    });\n};\nconst DataGridSelection = Object.assign(_componentDataGridSelection, {\n    componentType: \"option\",\n});\nconst _componentEditing = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"editing\",\n            DefaultsProps: {\n                defaultChanges: \"changes\",\n                defaultEditColumnName: \"editColumnName\",\n                defaultEditRowKey: \"editRowKey\"\n            },\n            ExpectedChildren: {\n                change: { optionName: \"changes\", isCollectionItem: true },\n                editingTexts: { optionName: \"texts\", isCollectionItem: false },\n                form: { optionName: \"form\", isCollectionItem: false },\n                popup: { optionName: \"popup\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Editing = Object.assign(_componentEditing, {\n    componentType: \"option\",\n});\nconst _componentEditingTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst EditingTexts = Object.assign(_componentEditingTexts, {\n    componentType: \"option\",\n});\nconst _componentEmailRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"email\"\n            },\n        },\n    });\n};\nconst EmailRule = Object.assign(_componentEmailRule, {\n    componentType: \"option\",\n});\nconst _componentExport = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"export\",\n            ExpectedChildren: {\n                exportTexts: { optionName: \"texts\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Export = Object.assign(_componentExport, {\n    componentType: \"option\",\n});\nconst _componentExportTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst ExportTexts = Object.assign(_componentExportTexts, {\n    componentType: \"option\",\n});\nconst _componentField = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"fields\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                fieldLookup: { optionName: \"lookup\", isCollectionItem: false },\n                format: { optionName: \"format\", isCollectionItem: false },\n                lookup: { optionName: \"lookup\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"editorTemplate\",\n                    render: \"editorRender\",\n                    component: \"editorComponent\"\n                }],\n        },\n    });\n};\nconst Field = Object.assign(_componentField, {\n    componentType: \"option\",\n});\nconst _componentFieldLookup = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"lookup\",\n        },\n    });\n};\nconst FieldLookup = Object.assign(_componentFieldLookup, {\n    componentType: \"option\",\n});\nconst _componentFilterBuilder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"filterBuilder\",\n            DefaultsProps: {\n                defaultValue: \"value\"\n            },\n            ExpectedChildren: {\n                customOperation: { optionName: \"customOperations\", isCollectionItem: true },\n                field: { optionName: \"fields\", isCollectionItem: true },\n                filterOperationDescriptions: { optionName: \"filterOperationDescriptions\", isCollectionItem: false },\n                groupOperationDescriptions: { optionName: \"groupOperationDescriptions\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst FilterBuilder = Object.assign(_componentFilterBuilder, {\n    componentType: \"option\",\n});\nconst _componentFilterBuilderPopup = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"filterBuilderPopup\",\n            DefaultsProps: {\n                defaultHeight: \"height\",\n                defaultPosition: \"position\",\n                defaultVisible: \"visible\",\n                defaultWidth: \"width\"\n            },\n            ExpectedChildren: {\n                animation: { optionName: \"animation\", isCollectionItem: false },\n                position: { optionName: \"position\", isCollectionItem: false },\n                toolbarItem: { optionName: \"toolbarItems\", isCollectionItem: true }\n            },\n            TemplateProps: [{\n                    tmplOption: \"contentTemplate\",\n                    render: \"contentRender\",\n                    component: \"contentComponent\"\n                }, {\n                    tmplOption: \"titleTemplate\",\n                    render: \"titleRender\",\n                    component: \"titleComponent\"\n                }],\n        },\n    });\n};\nconst FilterBuilderPopup = Object.assign(_componentFilterBuilderPopup, {\n    componentType: \"option\",\n});\nconst _componentFilterOperationDescriptions = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"filterOperationDescriptions\",\n        },\n    });\n};\nconst FilterOperationDescriptions = Object.assign(_componentFilterOperationDescriptions, {\n    componentType: \"option\",\n});\nconst _componentFilterPanel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"filterPanel\",\n            DefaultsProps: {\n                defaultFilterEnabled: \"filterEnabled\"\n            },\n            ExpectedChildren: {\n                filterPanelTexts: { optionName: \"texts\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst FilterPanel = Object.assign(_componentFilterPanel, {\n    componentType: \"option\",\n});\nconst _componentFilterPanelTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst FilterPanelTexts = Object.assign(_componentFilterPanelTexts, {\n    componentType: \"option\",\n});\nconst _componentFilterRow = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"filterRow\",\n            ExpectedChildren: {\n                operationDescriptions: { optionName: \"operationDescriptions\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst FilterRow = Object.assign(_componentFilterRow, {\n    componentType: \"option\",\n});\nconst _componentForm = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"form\",\n            DefaultsProps: {\n                defaultFormData: \"formData\"\n            },\n            ExpectedChildren: {\n                colCountByScreen: { optionName: \"colCountByScreen\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Form = Object.assign(_componentForm, {\n    componentType: \"option\",\n});\nconst _componentFormat = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"format\",\n        },\n    });\n};\nconst Format = Object.assign(_componentFormat, {\n    componentType: \"option\",\n});\nconst _componentFormItem = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"formItem\",\n            ExpectedChildren: {\n                AsyncRule: { optionName: \"validationRules\", isCollectionItem: true },\n                CompareRule: { optionName: \"validationRules\", isCollectionItem: true },\n                CustomRule: { optionName: \"validationRules\", isCollectionItem: true },\n                EmailRule: { optionName: \"validationRules\", isCollectionItem: true },\n                label: { optionName: \"label\", isCollectionItem: false },\n                NumericRule: { optionName: \"validationRules\", isCollectionItem: true },\n                PatternRule: { optionName: \"validationRules\", isCollectionItem: true },\n                RangeRule: { optionName: \"validationRules\", isCollectionItem: true },\n                RequiredRule: { optionName: \"validationRules\", isCollectionItem: true },\n                StringLengthRule: { optionName: \"validationRules\", isCollectionItem: true },\n                validationRule: { optionName: \"validationRules\", isCollectionItem: true }\n            },\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst FormItem = Object.assign(_componentFormItem, {\n    componentType: \"option\",\n});\nconst _componentFrom = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"from\",\n            ExpectedChildren: {\n                position: { optionName: \"position\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst From = Object.assign(_componentFrom, {\n    componentType: \"option\",\n});\nconst _componentGrouping = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"grouping\",\n            ExpectedChildren: {\n                groupingTexts: { optionName: \"texts\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Grouping = Object.assign(_componentGrouping, {\n    componentType: \"option\",\n});\nconst _componentGroupingTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst GroupingTexts = Object.assign(_componentGroupingTexts, {\n    componentType: \"option\",\n});\nconst _componentGroupItem = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"groupItems\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                valueFormat: { optionName: \"valueFormat\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst GroupItem = Object.assign(_componentGroupItem, {\n    componentType: \"option\",\n});\nconst _componentGroupOperationDescriptions = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"groupOperationDescriptions\",\n        },\n    });\n};\nconst GroupOperationDescriptions = Object.assign(_componentGroupOperationDescriptions, {\n    componentType: \"option\",\n});\nconst _componentGroupPanel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"groupPanel\",\n            DefaultsProps: {\n                defaultVisible: \"visible\"\n            },\n        },\n    });\n};\nconst GroupPanel = Object.assign(_componentGroupPanel, {\n    componentType: \"option\",\n});\nconst _componentHeaderFilter = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"headerFilter\",\n            ExpectedChildren: {\n                columnHeaderFilterSearch: { optionName: \"search\", isCollectionItem: false },\n                dataGridHeaderFilterSearch: { optionName: \"search\", isCollectionItem: false },\n                dataGridHeaderFilterTexts: { optionName: \"texts\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst HeaderFilter = Object.assign(_componentHeaderFilter, {\n    componentType: \"option\",\n});\nconst _componentHide = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"hide\",\n            ExpectedChildren: {\n                from: { optionName: \"from\", isCollectionItem: false },\n                to: { optionName: \"to\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Hide = Object.assign(_componentHide, {\n    componentType: \"option\",\n});\nconst _componentIcons = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"icons\",\n        },\n    });\n};\nconst Icons = Object.assign(_componentIcons, {\n    componentType: \"option\",\n});\nconst _componentItem = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"items\",\n            IsCollectionItem: true,\n            TemplateProps: [{\n                    tmplOption: \"menuItemTemplate\",\n                    render: \"menuItemRender\",\n                    component: \"menuItemComponent\"\n                }, {\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst Item = Object.assign(_componentItem, {\n    componentType: \"option\",\n});\nconst _componentKeyboardNavigation = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"keyboardNavigation\",\n        },\n    });\n};\nconst KeyboardNavigation = Object.assign(_componentKeyboardNavigation, {\n    componentType: \"option\",\n});\nconst _componentLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst Label = Object.assign(_componentLabel, {\n    componentType: \"option\",\n});\nconst _componentLoadPanel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"loadPanel\",\n        },\n    });\n};\nconst LoadPanel = Object.assign(_componentLoadPanel, {\n    componentType: \"option\",\n});\nconst _componentLookup = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"lookup\",\n        },\n    });\n};\nconst Lookup = Object.assign(_componentLookup, {\n    componentType: \"option\",\n});\nconst _componentMasterDetail = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"masterDetail\",\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst MasterDetail = Object.assign(_componentMasterDetail, {\n    componentType: \"option\",\n});\nconst _componentMy = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"my\",\n        },\n    });\n};\nconst My = Object.assign(_componentMy, {\n    componentType: \"option\",\n});\nconst _componentNumericRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"numeric\"\n            },\n        },\n    });\n};\nconst NumericRule = Object.assign(_componentNumericRule, {\n    componentType: \"option\",\n});\nconst _componentOffset = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"offset\",\n        },\n    });\n};\nconst Offset = Object.assign(_componentOffset, {\n    componentType: \"option\",\n});\nconst _componentOperationDescriptions = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"operationDescriptions\",\n        },\n    });\n};\nconst OperationDescriptions = Object.assign(_componentOperationDescriptions, {\n    componentType: \"option\",\n});\nconst _componentPager = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"pager\",\n        },\n    });\n};\nconst Pager = Object.assign(_componentPager, {\n    componentType: \"option\",\n});\nconst _componentPaging = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"paging\",\n            DefaultsProps: {\n                defaultPageIndex: \"pageIndex\",\n                defaultPageSize: \"pageSize\"\n            },\n        },\n    });\n};\nconst Paging = Object.assign(_componentPaging, {\n    componentType: \"option\",\n});\nconst _componentPatternRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"pattern\"\n            },\n        },\n    });\n};\nconst PatternRule = Object.assign(_componentPatternRule, {\n    componentType: \"option\",\n});\nconst _componentPopup = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"popup\",\n            DefaultsProps: {\n                defaultHeight: \"height\",\n                defaultPosition: \"position\",\n                defaultVisible: \"visible\",\n                defaultWidth: \"width\"\n            },\n            ExpectedChildren: {\n                animation: { optionName: \"animation\", isCollectionItem: false },\n                position: { optionName: \"position\", isCollectionItem: false },\n                toolbarItem: { optionName: \"toolbarItems\", isCollectionItem: true }\n            },\n            TemplateProps: [{\n                    tmplOption: \"contentTemplate\",\n                    render: \"contentRender\",\n                    component: \"contentComponent\"\n                }, {\n                    tmplOption: \"titleTemplate\",\n                    render: \"titleRender\",\n                    component: \"titleComponent\"\n                }],\n        },\n    });\n};\nconst Popup = Object.assign(_componentPopup, {\n    componentType: \"option\",\n});\nconst _componentPosition = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"position\",\n            ExpectedChildren: {\n                at: { optionName: \"at\", isCollectionItem: false },\n                boundaryOffset: { optionName: \"boundaryOffset\", isCollectionItem: false },\n                collision: { optionName: \"collision\", isCollectionItem: false },\n                my: { optionName: \"my\", isCollectionItem: false },\n                offset: { optionName: \"offset\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Position = Object.assign(_componentPosition, {\n    componentType: \"option\",\n});\nconst _componentRangeRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"range\"\n            },\n        },\n    });\n};\nconst RangeRule = Object.assign(_componentRangeRule, {\n    componentType: \"option\",\n});\nconst _componentRemoteOperations = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"remoteOperations\",\n        },\n    });\n};\nconst RemoteOperations = Object.assign(_componentRemoteOperations, {\n    componentType: \"option\",\n});\nconst _componentRequiredRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"required\"\n            },\n        },\n    });\n};\nconst RequiredRule = Object.assign(_componentRequiredRule, {\n    componentType: \"option\",\n});\nconst _componentRowDragging = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"rowDragging\",\n            ExpectedChildren: {\n                cursorOffset: { optionName: \"cursorOffset\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"dragTemplate\",\n                    render: \"dragRender\",\n                    component: \"dragComponent\"\n                }],\n        },\n    });\n};\nconst RowDragging = Object.assign(_componentRowDragging, {\n    componentType: \"option\",\n});\nconst _componentScrolling = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"scrolling\",\n        },\n    });\n};\nconst Scrolling = Object.assign(_componentScrolling, {\n    componentType: \"option\",\n});\nconst _componentSearch = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"search\",\n        },\n    });\n};\nconst Search = Object.assign(_componentSearch, {\n    componentType: \"option\",\n});\nconst _componentSearchPanel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"searchPanel\",\n            DefaultsProps: {\n                defaultText: \"text\"\n            },\n        },\n    });\n};\nconst SearchPanel = Object.assign(_componentSearchPanel, {\n    componentType: \"option\",\n});\nconst _componentSelection = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"selection\",\n        },\n    });\n};\nconst Selection = Object.assign(_componentSelection, {\n    componentType: \"option\",\n});\nconst _componentShow = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"show\",\n            ExpectedChildren: {\n                from: { optionName: \"from\", isCollectionItem: false },\n                to: { optionName: \"to\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Show = Object.assign(_componentShow, {\n    componentType: \"option\",\n});\nconst _componentSortByGroupSummaryInfo = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"sortByGroupSummaryInfo\",\n            IsCollectionItem: true,\n        },\n    });\n};\nconst SortByGroupSummaryInfo = Object.assign(_componentSortByGroupSummaryInfo, {\n    componentType: \"option\",\n});\nconst _componentSorting = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"sorting\",\n        },\n    });\n};\nconst Sorting = Object.assign(_componentSorting, {\n    componentType: \"option\",\n});\nconst _componentStateStoring = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"stateStoring\",\n        },\n    });\n};\nconst StateStoring = Object.assign(_componentStateStoring, {\n    componentType: \"option\",\n});\nconst _componentStringLengthRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"stringLength\"\n            },\n        },\n    });\n};\nconst StringLengthRule = Object.assign(_componentStringLengthRule, {\n    componentType: \"option\",\n});\nconst _componentSummary = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"summary\",\n            ExpectedChildren: {\n                groupItem: { optionName: \"groupItems\", isCollectionItem: true },\n                summaryTexts: { optionName: \"texts\", isCollectionItem: false },\n                texts: { optionName: \"texts\", isCollectionItem: false },\n                totalItem: { optionName: \"totalItems\", isCollectionItem: true }\n            },\n        },\n    });\n};\nconst Summary = Object.assign(_componentSummary, {\n    componentType: \"option\",\n});\nconst _componentSummaryTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst SummaryTexts = Object.assign(_componentSummaryTexts, {\n    componentType: \"option\",\n});\nconst _componentTexts = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"texts\",\n        },\n    });\n};\nconst Texts = Object.assign(_componentTexts, {\n    componentType: \"option\",\n});\nconst _componentTo = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"to\",\n            ExpectedChildren: {\n                position: { optionName: \"position\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst To = Object.assign(_componentTo, {\n    componentType: \"option\",\n});\nconst _componentToolbar = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"toolbar\",\n            ExpectedChildren: {\n                item: { optionName: \"items\", isCollectionItem: true }\n            },\n        },\n    });\n};\nconst Toolbar = Object.assign(_componentToolbar, {\n    componentType: \"option\",\n});\nconst _componentToolbarItem = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"toolbarItems\",\n            IsCollectionItem: true,\n            TemplateProps: [{\n                    tmplOption: \"menuItemTemplate\",\n                    render: \"menuItemRender\",\n                    component: \"menuItemComponent\"\n                }, {\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst ToolbarItem = Object.assign(_componentToolbarItem, {\n    componentType: \"option\",\n});\nconst _componentTotalItem = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"totalItems\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                valueFormat: { optionName: \"valueFormat\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst TotalItem = Object.assign(_componentTotalItem, {\n    componentType: \"option\",\n});\nconst _componentValidationRule = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"validationRules\",\n            IsCollectionItem: true,\n            PredefinedProps: {\n                type: \"required\"\n            },\n        },\n    });\n};\nconst ValidationRule = Object.assign(_componentValidationRule, {\n    componentType: \"option\",\n});\nconst _componentValueFormat = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"valueFormat\",\n        },\n    });\n};\nconst ValueFormat = Object.assign(_componentValueFormat, {\n    componentType: \"option\",\n});\nexport default DataGrid;\nexport { DataGrid, Animation, AsyncRule, At, BoundaryOffset, Button, Change, ColCountByScreen, Collision, Column, ColumnChooser, ColumnChooserSearch, ColumnChooserSelection, ColumnFixing, ColumnFixingTexts, ColumnHeaderFilter, ColumnHeaderFilterSearch, ColumnLookup, CompareRule, CursorOffset, CustomOperation, CustomRule, DataGridHeaderFilter, DataGridHeaderFilterSearch, DataGridHeaderFilterTexts, DataGridSelection, Editing, EditingTexts, EmailRule, Export, ExportTexts, Field, FieldLookup, FilterBuilder, FilterBuilderPopup, FilterOperationDescriptions, FilterPanel, FilterPanelTexts, FilterRow, Form, Format, FormItem, From, Grouping, GroupingTexts, GroupItem, GroupOperationDescriptions, GroupPanel, HeaderFilter, Hide, Icons, Item, KeyboardNavigation, Label, LoadPanel, Lookup, MasterDetail, My, NumericRule, Offset, OperationDescriptions, Pager, Paging, PatternRule, Popup, Position, RangeRule, RemoteOperations, RequiredRule, RowDragging, Scrolling, Search, SearchPanel, Selection, Show, SortByGroupSummaryInfo, Sorting, StateStoring, StringLengthRule, Summary, SummaryTexts, Texts, To, Toolbar, ToolbarItem, TotalItem, ValidationRule, ValueFormat };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;AAEA;AACA;AACA;AALA;;;;;;AAMA,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACrC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;wCAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,WAAW;gBACvC;YACJ,CAAC;uCAAG;QAAC,QAAQ,OAAO;KAAC;IACrB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;iDAAE,IAAO;gBAAC;gBAAW;gBAAW;gBAAmB;gBAA0B;gBAAsB;gBAAe;gBAAsB;gBAAmB;gBAAiB;gBAAc;gBAAsB;gBAAU;gBAAoB;gBAAmB;gBAAmB;gBAAmB;gBAAuB;gBAA6B;gBAA+B;gBAA8B;gBAA4B;gBAA6B;gBAAyB;gBAAwB;gBAA0B;gBAAyB;gBAAuB;aAAmB;gDAAG,EAAE;IACtoB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE,IAAO;gBAAC;gBAAgC;gBAAe;gBAAkB;gBAAkB;gBAAkB;gBAA0B;gBAAuB;gBAAe;gBAAkB;gBAAmB;gBAAkB;gBAAoB;gBAAqB;gBAAe;gBAAyB;gBAAwB;gBAAiB;gBAAgB;gBAAa;gBAAc;gBAAkB;gBAAmB;gBAAiB;gBAAiB;gBAAkB;gBAAiB;gBAAkB;gBAAiB;gBAAgB;gBAAiB;gBAAgB;gBAAiB;gBAAmB;gBAAW;gBAAY;aAAqB;8CAAG,EAAE;IAC9rB,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;sCAAE,IAAM,CAAC;gBAC5B,gBAAgB;gBAChB,gBAAgB;gBAChB,oBAAoB;gBACpB,2BAA2B;gBAC3B,wBAAwB;gBACxB,sBAAsB;gBACtB,mBAAmB;gBACnB,eAAe;gBACf,wBAAwB;gBACxB,wBAAwB;YAC5B,CAAC;qCAAG,EAAE;IACN,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE,IAAM,CAAC;gBACpC,QAAQ;oBAAE,YAAY;oBAAW,kBAAkB;gBAAK;gBACxD,eAAe;oBAAE,YAAY;oBAAiB,kBAAkB;gBAAM;gBACtE,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,sBAAsB;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBAC5E,mBAAmB;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBACtE,SAAS;oBAAE,YAAY;oBAAW,kBAAkB;gBAAM;gBAC1D,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,eAAe;oBAAE,YAAY;oBAAiB,kBAAkB;gBAAM;gBACtE,oBAAoB;oBAAE,YAAY;oBAAsB,kBAAkB;gBAAM;gBAChF,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,oBAAoB;oBAAE,YAAY;oBAAsB,kBAAkB;gBAAM;gBAChF,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,kBAAkB;oBAAE,YAAY;oBAAoB,kBAAkB;gBAAM;gBAC5E,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,wBAAwB;oBAAE,YAAY;oBAA0B,kBAAkB;gBAAK;gBACvF,SAAS;oBAAE,YAAY;oBAAW,kBAAkB;gBAAM;gBAC1D,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,SAAS;oBAAE,YAAY;oBAAW,kBAAkB;gBAAM;gBAC1D,SAAS;oBAAE,YAAY;oBAAW,kBAAkB;gBAAM;YAC9D,CAAC;6CAAG,EAAE;IACN,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE,IAAO;gBACjC;oBACI,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBACA;oBACI,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aACH;0CAAG,EAAE;IACN,OAAQ,6JAAA,CAAA,gBAAmB,CAAE,kKAAA,CAAA,YAAa,EAAG;QACzC,aAAa,uJAAA,CAAA,UAAU;QACvB,KAAK;QACL,8BAA8B;QAC9B;QACA;QACA;QACA;QACA;QACA,GAAG,KAAK;IACZ;AACJ;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,eAAe,CAAC;IAClB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,KAAK,OAAO,MAAM,CAAC,cAAc;IACnC,eAAe;AACnB;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;QACtB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,6BAA6B,CAAC;IAChC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC,4BAA4B;IAC/D,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBACX,oBAAoB;gBACpB,qBAAqB;gBACrB,mBAAmB;gBACnB,gCAAgC;gBAChC,kBAAkB;gBAClB,kBAAkB;gBAClB,gBAAgB;gBAChB,qBAAqB;YACzB;YACA,kBAAkB;gBACd,WAAW;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACnE,QAAQ;oBAAE,YAAY;oBAAW,kBAAkB;gBAAK;gBACxD,oBAAoB;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBAC1E,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC9D,aAAa;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACrE,YAAY;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACpE,WAAW;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACnE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,aAAa;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACrE,aAAa;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACrE,WAAW;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACnE,cAAc;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACtE,kBAAkB;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBAC1E,gBAAgB;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;YAC5E;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,0BAA0B,CAAC;IAC7B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,qBAAqB;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACrE,wBAAwB;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC3E,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,gBAAgB,OAAO,MAAM,CAAC,yBAAyB;IACzD,eAAe;AACnB;AACA,MAAM,gCAAgC,CAAC;IACnC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,sBAAsB,OAAO,MAAM,CAAC,+BAA+B;IACrE,eAAe;AACnB;AACA,MAAM,mCAAmC,CAAC;IACtC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,yBAAyB,OAAO,MAAM,CAAC,kCAAkC;IAC3E,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,mBAAmB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAClE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,8BAA8B,CAAC;IACjC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,oBAAoB,OAAO,MAAM,CAAC,6BAA6B;IACjE,eAAe;AACnB;AACA,MAAM,+BAA+B,CAAC;IAClC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,0BAA0B;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC1E,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;QACJ;IACJ;AACJ;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,8BAA8B;IACnE,eAAe;AACnB;AACA,MAAM,qCAAqC,CAAC;IACxC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,2BAA2B,OAAO,MAAM,CAAC,oCAAoC;IAC/E,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,4BAA4B,CAAC;IAC/B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC,2BAA2B;IAC7D,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,iCAAiC,CAAC;IACpC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,4BAA4B;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC5E,2BAA2B;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1E,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,uBAAuB,OAAO,MAAM,CAAC,gCAAgC;IACvE,eAAe;AACnB;AACA,MAAM,uCAAuC,CAAC;IAC1C,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,6BAA6B,OAAO,MAAM,CAAC,sCAAsC;IACnF,eAAe;AACnB;AACA,MAAM,sCAAsC,CAAC;IACzC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,4BAA4B,OAAO,MAAM,CAAC,qCAAqC;IACjF,eAAe;AACnB;AACA,MAAM,8BAA8B,CAAC;IACjC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,oBAAoB,OAAO,MAAM,CAAC,6BAA6B;IACjE,eAAe;AACnB;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,gBAAgB;gBAChB,uBAAuB;gBACvB,mBAAmB;YACvB;YACA,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAW,kBAAkB;gBAAK;gBACxD,cAAc;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC7D,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,UAAU,OAAO,MAAM,CAAC,mBAAmB;IAC7C,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,aAAa;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC5D,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,aAAa;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC7D,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,0BAA0B,CAAC;IAC7B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,cAAc;YAClB;YACA,kBAAkB;gBACd,iBAAiB;oBAAE,YAAY;oBAAoB,kBAAkB;gBAAK;gBAC1E,OAAO;oBAAE,YAAY;oBAAU,kBAAkB;gBAAK;gBACtD,6BAA6B;oBAAE,YAAY;oBAA+B,kBAAkB;gBAAM;gBAClG,4BAA4B;oBAAE,YAAY;oBAA8B,kBAAkB;gBAAM;YACpG;QACJ;IACJ;AACJ;AACA,MAAM,gBAAgB,OAAO,MAAM,CAAC,yBAAyB;IACzD,eAAe;AACnB;AACA,MAAM,+BAA+B,CAAC;IAClC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,cAAc;YAClB;YACA,kBAAkB;gBACd,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,aAAa;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAK;YACtE;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,8BAA8B;IACnE,eAAe;AACnB;AACA,MAAM,wCAAwC,CAAC;IAC3C,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,8BAA8B,OAAO,MAAM,CAAC,uCAAuC;IACrF,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,sBAAsB;YAC1B;YACA,kBAAkB;gBACd,kBAAkB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACjE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,6BAA6B,CAAC;IAChC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC,4BAA4B;IAC/D,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,uBAAuB;oBAAE,YAAY;oBAAyB,kBAAkB;gBAAM;YAC1F;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,iBAAiB;YACrB;YACA,kBAAkB;gBACd,kBAAkB;oBAAE,YAAY;oBAAoB,kBAAkB;gBAAM;YAChF;QACJ;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,qBAAqB,CAAC;IACxB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,WAAW;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACnE,aAAa;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACrE,YAAY;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACpE,WAAW;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACnE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,aAAa;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACrE,aAAa;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACrE,WAAW;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACnE,cAAc;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBACtE,kBAAkB;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;gBAC1E,gBAAgB;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAK;YAC5E;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,WAAW,OAAO,MAAM,CAAC,oBAAoB;IAC/C,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;YAChE;QACJ;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,qBAAqB,CAAC;IACxB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,eAAe;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC9D,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,WAAW,OAAO,MAAM,CAAC,oBAAoB;IAC/C,eAAe;AACnB;AACA,MAAM,0BAA0B,CAAC;IAC7B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,gBAAgB,OAAO,MAAM,CAAC,yBAAyB;IACzD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;YACtE;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,uCAAuC,CAAC;IAC1C,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,6BAA6B,OAAO,MAAM,CAAC,sCAAsC;IACnF,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,gBAAgB;YACpB;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,0BAA0B;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC1E,4BAA4B;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC5E,2BAA2B;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC9E;QACJ;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,IAAI;oBAAE,YAAY;oBAAM,kBAAkB;gBAAM;YACpD;QACJ;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,+BAA+B,CAAC;IAClC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,8BAA8B;IACnE,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,eAAe,CAAC;IAClB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,KAAK,OAAO,MAAM,CAAC,cAAc;IACnC,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,kCAAkC,CAAC;IACrC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,wBAAwB,OAAO,MAAM,CAAC,iCAAiC;IACzE,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,kBAAkB;gBAClB,iBAAiB;YACrB;QACJ;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,eAAe;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,cAAc;YAClB;YACA,kBAAkB;gBACd,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,aAAa;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAK;YACtE;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,qBAAqB,CAAC;IACxB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,IAAI;oBAAE,YAAY;oBAAM,kBAAkB;gBAAM;gBAChD,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,IAAI;oBAAE,YAAY;oBAAM,kBAAkB;gBAAM;gBAChD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;QACJ;IACJ;AACJ;AACA,MAAM,WAAW,OAAO,MAAM,CAAC,oBAAoB;IAC/C,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,6BAA6B,CAAC;IAChC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC,4BAA4B;IAC/D,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;YACxE;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,aAAa;YACjB;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,IAAI;oBAAE,YAAY;oBAAM,kBAAkB;gBAAM;YACpD;QACJ;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,mCAAmC,CAAC;IACtC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;QACtB;IACJ;AACJ;AACA,MAAM,yBAAyB,OAAO,MAAM,CAAC,kCAAkC;IAC3E,eAAe;AACnB;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,UAAU,OAAO,MAAM,CAAC,mBAAmB;IAC7C,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,6BAA6B,CAAC;IAChC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC,4BAA4B;IAC/D,eAAe;AACnB;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,WAAW;oBAAE,YAAY;oBAAc,kBAAkB;gBAAK;gBAC9D,cAAc;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC7D,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,WAAW;oBAAE,YAAY;oBAAc,kBAAkB;gBAAK;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,UAAU,OAAO,MAAM,CAAC,mBAAmB;IAC7C,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,eAAe,CAAC;IAClB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;YAChE;QACJ;IACJ;AACJ;AACA,MAAM,KAAK,OAAO,MAAM,CAAC,cAAc;IACnC,eAAe;AACnB;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAS,kBAAkB;gBAAK;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,UAAU,OAAO,MAAM,CAAC,mBAAmB;IAC7C,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;YACtE;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,iBAAiB;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4274, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/chart.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n\"use client\";\nimport * as React from \"react\";\nimport { memo, forwardRef, useImperativeHandle, useRef, useMemo } from \"react\";\nimport dxChart from \"devextreme/viz/chart\";\nimport { Component as BaseComponent } from \"./core/component\";\nimport NestedOption from \"./core/nested-option\";\nconst Chart = memo(forwardRef((props, ref) => {\n    const baseRef = useRef(null);\n    useImperativeHandle(ref, () => ({\n        instance() {\n            return baseRef.current?.getInstance();\n        }\n    }), [baseRef.current]);\n    const subscribableOptions = useMemo(() => ([\"argumentAxis\", \"argumentAxis.categories\", \"argumentAxis.visualRange\", \"loadingIndicator\", \"loadingIndicator.show\", \"valueAxis\", \"valueAxis.categories\", \"valueAxis.visualRange\", \"argumentAxis.visualRange.endValue\", \"valueAxis.visualRange.endValue\", \"argumentAxis.visualRange.startValue\", \"valueAxis.visualRange.startValue\", \"argumentAxis.wholeRange.endValue\", \"valueAxis.wholeRange.endValue\", \"argumentAxis.wholeRange.startValue\", \"valueAxis.wholeRange.startValue\"]), []);\n    const independentEvents = useMemo(() => ([\"onArgumentAxisClick\", \"onDisposing\", \"onDone\", \"onDrawn\", \"onExported\", \"onExporting\", \"onFileSaving\", \"onIncidentOccurred\", \"onInitialized\", \"onLegendClick\", \"onPointClick\", \"onSeriesClick\", \"onTooltipHidden\", \"onTooltipShown\", \"onZoomEnd\", \"onZoomStart\"]), []);\n    const defaults = useMemo(() => ({\n        defaultArgumentAxis: \"argumentAxis\",\n        defaultLoadingIndicator: \"loadingIndicator\",\n        defaultValueAxis: \"valueAxis\",\n    }), []);\n    const expectedChildren = useMemo(() => ({\n        adaptiveLayout: { optionName: \"adaptiveLayout\", isCollectionItem: false },\n        animation: { optionName: \"animation\", isCollectionItem: false },\n        annotation: { optionName: \"annotations\", isCollectionItem: true },\n        argumentAxis: { optionName: \"argumentAxis\", isCollectionItem: false },\n        chartTitle: { optionName: \"title\", isCollectionItem: false },\n        commonAnnotationSettings: { optionName: \"commonAnnotationSettings\", isCollectionItem: false },\n        commonAxisSettings: { optionName: \"commonAxisSettings\", isCollectionItem: false },\n        commonPaneSettings: { optionName: \"commonPaneSettings\", isCollectionItem: false },\n        commonSeriesSettings: { optionName: \"commonSeriesSettings\", isCollectionItem: false },\n        crosshair: { optionName: \"crosshair\", isCollectionItem: false },\n        dataPrepareSettings: { optionName: \"dataPrepareSettings\", isCollectionItem: false },\n        export: { optionName: \"export\", isCollectionItem: false },\n        legend: { optionName: \"legend\", isCollectionItem: false },\n        loadingIndicator: { optionName: \"loadingIndicator\", isCollectionItem: false },\n        margin: { optionName: \"margin\", isCollectionItem: false },\n        pane: { optionName: \"panes\", isCollectionItem: true },\n        scrollBar: { optionName: \"scrollBar\", isCollectionItem: false },\n        series: { optionName: \"series\", isCollectionItem: true },\n        seriesTemplate: { optionName: \"seriesTemplate\", isCollectionItem: false },\n        size: { optionName: \"size\", isCollectionItem: false },\n        title: { optionName: \"title\", isCollectionItem: false },\n        tooltip: { optionName: \"tooltip\", isCollectionItem: false },\n        valueAxis: { optionName: \"valueAxis\", isCollectionItem: true },\n        zoomAndPan: { optionName: \"zoomAndPan\", isCollectionItem: false }\n    }), []);\n    return (React.createElement((BaseComponent), {\n        WidgetClass: dxChart,\n        ref: baseRef,\n        useRequestAnimationFrameFlag: true,\n        subscribableOptions,\n        independentEvents,\n        defaults,\n        expectedChildren,\n        ...props,\n    }));\n}));\nconst _componentAdaptiveLayout = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"adaptiveLayout\",\n        },\n    });\n};\nconst AdaptiveLayout = Object.assign(_componentAdaptiveLayout, {\n    componentType: \"option\",\n});\nconst _componentAggregation = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"aggregation\",\n        },\n    });\n};\nconst Aggregation = Object.assign(_componentAggregation, {\n    componentType: \"option\",\n});\nconst _componentAggregationInterval = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"aggregationInterval\",\n        },\n    });\n};\nconst AggregationInterval = Object.assign(_componentAggregationInterval, {\n    componentType: \"option\",\n});\nconst _componentAnimation = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"animation\",\n        },\n    });\n};\nconst Animation = Object.assign(_componentAnimation, {\n    componentType: \"option\",\n});\nconst _componentAnnotation = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"annotations\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                annotationBorder: { optionName: \"border\", isCollectionItem: false },\n                annotationImage: { optionName: \"image\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                image: { optionName: \"image\", isCollectionItem: false },\n                shadow: { optionName: \"shadow\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }, {\n                    tmplOption: \"tooltipTemplate\",\n                    render: \"tooltipRender\",\n                    component: \"tooltipComponent\"\n                }],\n        },\n    });\n};\nconst Annotation = Object.assign(_componentAnnotation, {\n    componentType: \"option\",\n});\nconst _componentAnnotationBorder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"border\",\n        },\n    });\n};\nconst AnnotationBorder = Object.assign(_componentAnnotationBorder, {\n    componentType: \"option\",\n});\nconst _componentAnnotationImage = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"image\",\n        },\n    });\n};\nconst AnnotationImage = Object.assign(_componentAnnotationImage, {\n    componentType: \"option\",\n});\nconst _componentArgumentAxis = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"argumentAxis\",\n            DefaultsProps: {\n                defaultCategories: \"categories\",\n                defaultVisualRange: \"visualRange\"\n            },\n            ExpectedChildren: {\n                aggregationInterval: { optionName: \"aggregationInterval\", isCollectionItem: false },\n                axisConstantLineStyle: { optionName: \"constantLineStyle\", isCollectionItem: false },\n                axisLabel: { optionName: \"label\", isCollectionItem: false },\n                axisTitle: { optionName: \"title\", isCollectionItem: false },\n                break: { optionName: \"breaks\", isCollectionItem: true },\n                breakStyle: { optionName: \"breakStyle\", isCollectionItem: false },\n                constantLine: { optionName: \"constantLines\", isCollectionItem: true },\n                constantLineStyle: { optionName: \"constantLineStyle\", isCollectionItem: false },\n                grid: { optionName: \"grid\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false },\n                minorGrid: { optionName: \"minorGrid\", isCollectionItem: false },\n                minorTick: { optionName: \"minorTick\", isCollectionItem: false },\n                minorTickInterval: { optionName: \"minorTickInterval\", isCollectionItem: false },\n                minVisualRangeLength: { optionName: \"minVisualRangeLength\", isCollectionItem: false },\n                strip: { optionName: \"strips\", isCollectionItem: true },\n                stripStyle: { optionName: \"stripStyle\", isCollectionItem: false },\n                tick: { optionName: \"tick\", isCollectionItem: false },\n                tickInterval: { optionName: \"tickInterval\", isCollectionItem: false },\n                title: { optionName: \"title\", isCollectionItem: false },\n                visualRange: { optionName: \"visualRange\", isCollectionItem: false },\n                wholeRange: { optionName: \"wholeRange\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ArgumentAxis = Object.assign(_componentArgumentAxis, {\n    componentType: \"option\",\n});\nconst _componentArgumentFormat = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"argumentFormat\",\n        },\n    });\n};\nconst ArgumentFormat = Object.assign(_componentArgumentFormat, {\n    componentType: \"option\",\n});\nconst _componentAxisConstantLineStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"constantLineStyle\",\n            ExpectedChildren: {\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst AxisConstantLineStyle = Object.assign(_componentAxisConstantLineStyle, {\n    componentType: \"option\",\n});\nconst _componentAxisConstantLineStyleLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst AxisConstantLineStyleLabel = Object.assign(_componentAxisConstantLineStyleLabel, {\n    componentType: \"option\",\n});\nconst _componentAxisLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false },\n                format: { optionName: \"format\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst AxisLabel = Object.assign(_componentAxisLabel, {\n    componentType: \"option\",\n});\nconst _componentAxisTitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"title\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst AxisTitle = Object.assign(_componentAxisTitle, {\n    componentType: \"option\",\n});\nconst _componentBackgroundColor = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"backgroundColor\",\n        },\n    });\n};\nconst BackgroundColor = Object.assign(_componentBackgroundColor, {\n    componentType: \"option\",\n});\nconst _componentBorder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"border\",\n        },\n    });\n};\nconst Border = Object.assign(_componentBorder, {\n    componentType: \"option\",\n});\nconst _componentBreak = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"breaks\",\n            IsCollectionItem: true,\n        },\n    });\n};\nconst Break = Object.assign(_componentBreak, {\n    componentType: \"option\",\n});\nconst _componentBreakStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"breakStyle\",\n        },\n    });\n};\nconst BreakStyle = Object.assign(_componentBreakStyle, {\n    componentType: \"option\",\n});\nconst _componentChartTitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"title\",\n            ExpectedChildren: {\n                chartTitleSubtitle: { optionName: \"subtitle\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                margin: { optionName: \"margin\", isCollectionItem: false },\n                subtitle: { optionName: \"subtitle\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ChartTitle = Object.assign(_componentChartTitle, {\n    componentType: \"option\",\n});\nconst _componentChartTitleSubtitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"subtitle\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ChartTitleSubtitle = Object.assign(_componentChartTitleSubtitle, {\n    componentType: \"option\",\n});\nconst _componentColor = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"color\",\n        },\n    });\n};\nconst Color = Object.assign(_componentColor, {\n    componentType: \"option\",\n});\nconst _componentCommonAnnotationSettings = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"commonAnnotationSettings\",\n            ExpectedChildren: {\n                annotationBorder: { optionName: \"border\", isCollectionItem: false },\n                annotationImage: { optionName: \"image\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                image: { optionName: \"image\", isCollectionItem: false },\n                shadow: { optionName: \"shadow\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }, {\n                    tmplOption: \"tooltipTemplate\",\n                    render: \"tooltipRender\",\n                    component: \"tooltipComponent\"\n                }],\n        },\n    });\n};\nconst CommonAnnotationSettings = Object.assign(_componentCommonAnnotationSettings, {\n    componentType: \"option\",\n});\nconst _componentCommonAxisSettings = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"commonAxisSettings\",\n            ExpectedChildren: {\n                breakStyle: { optionName: \"breakStyle\", isCollectionItem: false },\n                commonAxisSettingsConstantLineStyle: { optionName: \"constantLineStyle\", isCollectionItem: false },\n                commonAxisSettingsLabel: { optionName: \"label\", isCollectionItem: false },\n                commonAxisSettingsTitle: { optionName: \"title\", isCollectionItem: false },\n                constantLineStyle: { optionName: \"constantLineStyle\", isCollectionItem: false },\n                grid: { optionName: \"grid\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false },\n                minorGrid: { optionName: \"minorGrid\", isCollectionItem: false },\n                minorTick: { optionName: \"minorTick\", isCollectionItem: false },\n                stripStyle: { optionName: \"stripStyle\", isCollectionItem: false },\n                tick: { optionName: \"tick\", isCollectionItem: false },\n                title: { optionName: \"title\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonAxisSettings = Object.assign(_componentCommonAxisSettings, {\n    componentType: \"option\",\n});\nconst _componentCommonAxisSettingsConstantLineStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"constantLineStyle\",\n            ExpectedChildren: {\n                commonAxisSettingsConstantLineStyleLabel: { optionName: \"label\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonAxisSettingsConstantLineStyle = Object.assign(_componentCommonAxisSettingsConstantLineStyle, {\n    componentType: \"option\",\n});\nconst _componentCommonAxisSettingsConstantLineStyleLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonAxisSettingsConstantLineStyleLabel = Object.assign(_componentCommonAxisSettingsConstantLineStyleLabel, {\n    componentType: \"option\",\n});\nconst _componentCommonAxisSettingsLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst CommonAxisSettingsLabel = Object.assign(_componentCommonAxisSettingsLabel, {\n    componentType: \"option\",\n});\nconst _componentCommonAxisSettingsTitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"title\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonAxisSettingsTitle = Object.assign(_componentCommonAxisSettingsTitle, {\n    componentType: \"option\",\n});\nconst _componentCommonPaneSettings = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"commonPaneSettings\",\n            ExpectedChildren: {\n                backgroundColor: { optionName: \"backgroundColor\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                paneBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonPaneSettings = Object.assign(_componentCommonPaneSettings, {\n    componentType: \"option\",\n});\nconst _componentCommonSeriesSettings = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"commonSeriesSettings\",\n            ExpectedChildren: {\n                aggregation: { optionName: \"aggregation\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                commonSeriesSettingsHoverStyle: { optionName: \"hoverStyle\", isCollectionItem: false },\n                commonSeriesSettingsLabel: { optionName: \"label\", isCollectionItem: false },\n                commonSeriesSettingsSelectionStyle: { optionName: \"selectionStyle\", isCollectionItem: false },\n                hoverStyle: { optionName: \"hoverStyle\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false },\n                point: { optionName: \"point\", isCollectionItem: false },\n                reduction: { optionName: \"reduction\", isCollectionItem: false },\n                selectionStyle: { optionName: \"selectionStyle\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false },\n                valueErrorBar: { optionName: \"valueErrorBar\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonSeriesSettings = Object.assign(_componentCommonSeriesSettings, {\n    componentType: \"option\",\n});\nconst _componentCommonSeriesSettingsHoverStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"hoverStyle\",\n            ExpectedChildren: {\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                hatching: { optionName: \"hatching\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonSeriesSettingsHoverStyle = Object.assign(_componentCommonSeriesSettingsHoverStyle, {\n    componentType: \"option\",\n});\nconst _componentCommonSeriesSettingsLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                argumentFormat: { optionName: \"argumentFormat\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                connector: { optionName: \"connector\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                format: { optionName: \"format\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonSeriesSettingsLabel = Object.assign(_componentCommonSeriesSettingsLabel, {\n    componentType: \"option\",\n});\nconst _componentCommonSeriesSettingsSelectionStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"selectionStyle\",\n            ExpectedChildren: {\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                hatching: { optionName: \"hatching\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst CommonSeriesSettingsSelectionStyle = Object.assign(_componentCommonSeriesSettingsSelectionStyle, {\n    componentType: \"option\",\n});\nconst _componentConnector = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"connector\",\n        },\n    });\n};\nconst Connector = Object.assign(_componentConnector, {\n    componentType: \"option\",\n});\nconst _componentConstantLine = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"constantLines\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ConstantLine = Object.assign(_componentConstantLine, {\n    componentType: \"option\",\n});\nconst _componentConstantLineLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ConstantLineLabel = Object.assign(_componentConstantLineLabel, {\n    componentType: \"option\",\n});\nconst _componentConstantLineStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"constantLineStyle\",\n            ExpectedChildren: {\n                commonAxisSettingsConstantLineStyleLabel: { optionName: \"label\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ConstantLineStyle = Object.assign(_componentConstantLineStyle, {\n    componentType: \"option\",\n});\nconst _componentCrosshair = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"crosshair\",\n            ExpectedChildren: {\n                horizontalLine: { optionName: \"horizontalLine\", isCollectionItem: false },\n                horizontalLineLabel: { optionName: \"label\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false },\n                verticalLine: { optionName: \"verticalLine\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Crosshair = Object.assign(_componentCrosshair, {\n    componentType: \"option\",\n});\nconst _componentDataPrepareSettings = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"dataPrepareSettings\",\n        },\n    });\n};\nconst DataPrepareSettings = Object.assign(_componentDataPrepareSettings, {\n    componentType: \"option\",\n});\nconst _componentDragBoxStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"dragBoxStyle\",\n        },\n    });\n};\nconst DragBoxStyle = Object.assign(_componentDragBoxStyle, {\n    componentType: \"option\",\n});\nconst _componentExport = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"export\",\n        },\n    });\n};\nconst Export = Object.assign(_componentExport, {\n    componentType: \"option\",\n});\nconst _componentFont = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"font\",\n        },\n    });\n};\nconst Font = Object.assign(_componentFont, {\n    componentType: \"option\",\n});\nconst _componentFormat = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"format\",\n        },\n    });\n};\nconst Format = Object.assign(_componentFormat, {\n    componentType: \"option\",\n});\nconst _componentGrid = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"grid\",\n        },\n    });\n};\nconst Grid = Object.assign(_componentGrid, {\n    componentType: \"option\",\n});\nconst _componentHatching = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"hatching\",\n        },\n    });\n};\nconst Hatching = Object.assign(_componentHatching, {\n    componentType: \"option\",\n});\nconst _componentHeight = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"height\",\n        },\n    });\n};\nconst Height = Object.assign(_componentHeight, {\n    componentType: \"option\",\n});\nconst _componentHorizontalLine = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"horizontalLine\",\n            ExpectedChildren: {\n                horizontalLineLabel: { optionName: \"label\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst HorizontalLine = Object.assign(_componentHorizontalLine, {\n    componentType: \"option\",\n});\nconst _componentHorizontalLineLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false },\n                format: { optionName: \"format\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst HorizontalLineLabel = Object.assign(_componentHorizontalLineLabel, {\n    componentType: \"option\",\n});\nconst _componentHoverStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"hoverStyle\",\n            ExpectedChildren: {\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                hatching: { optionName: \"hatching\", isCollectionItem: false },\n                pointBorder: { optionName: \"border\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst HoverStyle = Object.assign(_componentHoverStyle, {\n    componentType: \"option\",\n});\nconst _componentImage = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"image\",\n            ExpectedChildren: {\n                height: { optionName: \"height\", isCollectionItem: false },\n                url: { optionName: \"url\", isCollectionItem: false },\n                width: { optionName: \"width\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Image = Object.assign(_componentImage, {\n    componentType: \"option\",\n});\nconst _componentLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                argumentFormat: { optionName: \"argumentFormat\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                connector: { optionName: \"connector\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                format: { optionName: \"format\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst Label = Object.assign(_componentLabel, {\n    componentType: \"option\",\n});\nconst _componentLegend = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"legend\",\n            ExpectedChildren: {\n                annotationBorder: { optionName: \"border\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                legendTitle: { optionName: \"title\", isCollectionItem: false },\n                margin: { optionName: \"margin\", isCollectionItem: false },\n                title: { optionName: \"title\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"markerTemplate\",\n                    render: \"markerRender\",\n                    component: \"markerComponent\"\n                }],\n        },\n    });\n};\nconst Legend = Object.assign(_componentLegend, {\n    componentType: \"option\",\n});\nconst _componentLegendTitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"title\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false },\n                legendTitleSubtitle: { optionName: \"subtitle\", isCollectionItem: false },\n                margin: { optionName: \"margin\", isCollectionItem: false },\n                subtitle: { optionName: \"subtitle\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst LegendTitle = Object.assign(_componentLegendTitle, {\n    componentType: \"option\",\n});\nconst _componentLegendTitleSubtitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"subtitle\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst LegendTitleSubtitle = Object.assign(_componentLegendTitleSubtitle, {\n    componentType: \"option\",\n});\nconst _componentLength = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"length\",\n        },\n    });\n};\nconst Length = Object.assign(_componentLength, {\n    componentType: \"option\",\n});\nconst _componentLoadingIndicator = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"loadingIndicator\",\n            DefaultsProps: {\n                defaultShow: \"show\"\n            },\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst LoadingIndicator = Object.assign(_componentLoadingIndicator, {\n    componentType: \"option\",\n});\nconst _componentMargin = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"margin\",\n        },\n    });\n};\nconst Margin = Object.assign(_componentMargin, {\n    componentType: \"option\",\n});\nconst _componentMinorGrid = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"minorGrid\",\n        },\n    });\n};\nconst MinorGrid = Object.assign(_componentMinorGrid, {\n    componentType: \"option\",\n});\nconst _componentMinorTick = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"minorTick\",\n        },\n    });\n};\nconst MinorTick = Object.assign(_componentMinorTick, {\n    componentType: \"option\",\n});\nconst _componentMinorTickInterval = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"minorTickInterval\",\n        },\n    });\n};\nconst MinorTickInterval = Object.assign(_componentMinorTickInterval, {\n    componentType: \"option\",\n});\nconst _componentMinVisualRangeLength = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"minVisualRangeLength\",\n        },\n    });\n};\nconst MinVisualRangeLength = Object.assign(_componentMinVisualRangeLength, {\n    componentType: \"option\",\n});\nconst _componentPane = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"panes\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                backgroundColor: { optionName: \"backgroundColor\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                paneBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Pane = Object.assign(_componentPane, {\n    componentType: \"option\",\n});\nconst _componentPaneBorder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"border\",\n        },\n    });\n};\nconst PaneBorder = Object.assign(_componentPaneBorder, {\n    componentType: \"option\",\n});\nconst _componentPoint = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"point\",\n            ExpectedChildren: {\n                color: { optionName: \"color\", isCollectionItem: false },\n                hoverStyle: { optionName: \"hoverStyle\", isCollectionItem: false },\n                image: { optionName: \"image\", isCollectionItem: false },\n                pointBorder: { optionName: \"border\", isCollectionItem: false },\n                selectionStyle: { optionName: \"selectionStyle\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Point = Object.assign(_componentPoint, {\n    componentType: \"option\",\n});\nconst _componentPointBorder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"border\",\n        },\n    });\n};\nconst PointBorder = Object.assign(_componentPointBorder, {\n    componentType: \"option\",\n});\nconst _componentPointHoverStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"hoverStyle\",\n            ExpectedChildren: {\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                pointBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst PointHoverStyle = Object.assign(_componentPointHoverStyle, {\n    componentType: \"option\",\n});\nconst _componentPointImage = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"image\",\n            ExpectedChildren: {\n                height: { optionName: \"height\", isCollectionItem: false },\n                url: { optionName: \"url\", isCollectionItem: false },\n                width: { optionName: \"width\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst PointImage = Object.assign(_componentPointImage, {\n    componentType: \"option\",\n});\nconst _componentPointSelectionStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"selectionStyle\",\n            ExpectedChildren: {\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                pointBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst PointSelectionStyle = Object.assign(_componentPointSelectionStyle, {\n    componentType: \"option\",\n});\nconst _componentReduction = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"reduction\",\n        },\n    });\n};\nconst Reduction = Object.assign(_componentReduction, {\n    componentType: \"option\",\n});\nconst _componentScrollBar = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"scrollBar\",\n        },\n    });\n};\nconst ScrollBar = Object.assign(_componentScrollBar, {\n    componentType: \"option\",\n});\nconst _componentSelectionStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"selectionStyle\",\n            ExpectedChildren: {\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                hatching: { optionName: \"hatching\", isCollectionItem: false },\n                pointBorder: { optionName: \"border\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst SelectionStyle = Object.assign(_componentSelectionStyle, {\n    componentType: \"option\",\n});\nconst _componentSeries = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"series\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                aggregation: { optionName: \"aggregation\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                color: { optionName: \"color\", isCollectionItem: false },\n                commonSeriesSettingsHoverStyle: { optionName: \"hoverStyle\", isCollectionItem: false },\n                commonSeriesSettingsLabel: { optionName: \"label\", isCollectionItem: false },\n                commonSeriesSettingsSelectionStyle: { optionName: \"selectionStyle\", isCollectionItem: false },\n                hoverStyle: { optionName: \"hoverStyle\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false },\n                point: { optionName: \"point\", isCollectionItem: false },\n                reduction: { optionName: \"reduction\", isCollectionItem: false },\n                selectionStyle: { optionName: \"selectionStyle\", isCollectionItem: false },\n                seriesBorder: { optionName: \"border\", isCollectionItem: false },\n                valueErrorBar: { optionName: \"valueErrorBar\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Series = Object.assign(_componentSeries, {\n    componentType: \"option\",\n});\nconst _componentSeriesBorder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"border\",\n        },\n    });\n};\nconst SeriesBorder = Object.assign(_componentSeriesBorder, {\n    componentType: \"option\",\n});\nconst _componentSeriesTemplate = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"seriesTemplate\",\n        },\n    });\n};\nconst SeriesTemplate = Object.assign(_componentSeriesTemplate, {\n    componentType: \"option\",\n});\nconst _componentShadow = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"shadow\",\n        },\n    });\n};\nconst Shadow = Object.assign(_componentShadow, {\n    componentType: \"option\",\n});\nconst _componentSize = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"size\",\n        },\n    });\n};\nconst Size = Object.assign(_componentSize, {\n    componentType: \"option\",\n});\nconst _componentStrip = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"strips\",\n            IsCollectionItem: true,\n            ExpectedChildren: {\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Strip = Object.assign(_componentStrip, {\n    componentType: \"option\",\n});\nconst _componentStripLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst StripLabel = Object.assign(_componentStripLabel, {\n    componentType: \"option\",\n});\nconst _componentStripStyle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"stripStyle\",\n            ExpectedChildren: {\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst StripStyle = Object.assign(_componentStripStyle, {\n    componentType: \"option\",\n});\nconst _componentStripStyleLabel = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"label\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst StripStyleLabel = Object.assign(_componentStripStyleLabel, {\n    componentType: \"option\",\n});\nconst _componentSubtitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"subtitle\",\n            ExpectedChildren: {\n                font: { optionName: \"font\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Subtitle = Object.assign(_componentSubtitle, {\n    componentType: \"option\",\n});\nconst _componentTick = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"tick\",\n        },\n    });\n};\nconst Tick = Object.assign(_componentTick, {\n    componentType: \"option\",\n});\nconst _componentTickInterval = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"tickInterval\",\n        },\n    });\n};\nconst TickInterval = Object.assign(_componentTickInterval, {\n    componentType: \"option\",\n});\nconst _componentTitle = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"title\",\n            ExpectedChildren: {\n                chartTitleSubtitle: { optionName: \"subtitle\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                legendTitleSubtitle: { optionName: \"subtitle\", isCollectionItem: false },\n                margin: { optionName: \"margin\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst Title = Object.assign(_componentTitle, {\n    componentType: \"option\",\n});\nconst _componentTooltip = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"tooltip\",\n            ExpectedChildren: {\n                argumentFormat: { optionName: \"argumentFormat\", isCollectionItem: false },\n                border: { optionName: \"border\", isCollectionItem: false },\n                font: { optionName: \"font\", isCollectionItem: false },\n                format: { optionName: \"format\", isCollectionItem: false },\n                shadow: { optionName: \"shadow\", isCollectionItem: false },\n                tooltipBorder: { optionName: \"border\", isCollectionItem: false }\n            },\n            TemplateProps: [{\n                    tmplOption: \"contentTemplate\",\n                    render: \"contentRender\",\n                    component: \"contentComponent\"\n                }],\n        },\n    });\n};\nconst Tooltip = Object.assign(_componentTooltip, {\n    componentType: \"option\",\n});\nconst _componentTooltipBorder = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"border\",\n        },\n    });\n};\nconst TooltipBorder = Object.assign(_componentTooltipBorder, {\n    componentType: \"option\",\n});\nconst _componentUrl = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"url\",\n        },\n    });\n};\nconst Url = Object.assign(_componentUrl, {\n    componentType: \"option\",\n});\nconst _componentValueAxis = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"valueAxis\",\n            IsCollectionItem: true,\n            DefaultsProps: {\n                defaultCategories: \"categories\",\n                defaultVisualRange: \"visualRange\"\n            },\n            ExpectedChildren: {\n                axisConstantLineStyle: { optionName: \"constantLineStyle\", isCollectionItem: false },\n                axisLabel: { optionName: \"label\", isCollectionItem: false },\n                axisTitle: { optionName: \"title\", isCollectionItem: false },\n                break: { optionName: \"breaks\", isCollectionItem: true },\n                breakStyle: { optionName: \"breakStyle\", isCollectionItem: false },\n                constantLine: { optionName: \"constantLines\", isCollectionItem: true },\n                constantLineStyle: { optionName: \"constantLineStyle\", isCollectionItem: false },\n                grid: { optionName: \"grid\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false },\n                minorGrid: { optionName: \"minorGrid\", isCollectionItem: false },\n                minorTick: { optionName: \"minorTick\", isCollectionItem: false },\n                minorTickInterval: { optionName: \"minorTickInterval\", isCollectionItem: false },\n                minVisualRangeLength: { optionName: \"minVisualRangeLength\", isCollectionItem: false },\n                strip: { optionName: \"strips\", isCollectionItem: true },\n                stripStyle: { optionName: \"stripStyle\", isCollectionItem: false },\n                tick: { optionName: \"tick\", isCollectionItem: false },\n                tickInterval: { optionName: \"tickInterval\", isCollectionItem: false },\n                title: { optionName: \"title\", isCollectionItem: false },\n                visualRange: { optionName: \"visualRange\", isCollectionItem: false },\n                wholeRange: { optionName: \"wholeRange\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ValueAxis = Object.assign(_componentValueAxis, {\n    componentType: \"option\",\n});\nconst _componentValueErrorBar = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"valueErrorBar\",\n        },\n    });\n};\nconst ValueErrorBar = Object.assign(_componentValueErrorBar, {\n    componentType: \"option\",\n});\nconst _componentVerticalLine = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"verticalLine\",\n            ExpectedChildren: {\n                horizontalLineLabel: { optionName: \"label\", isCollectionItem: false },\n                label: { optionName: \"label\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst VerticalLine = Object.assign(_componentVerticalLine, {\n    componentType: \"option\",\n});\nconst _componentVisualRange = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"visualRange\",\n            DefaultsProps: {\n                defaultEndValue: \"endValue\",\n                defaultStartValue: \"startValue\"\n            },\n            ExpectedChildren: {\n                length: { optionName: \"length\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst VisualRange = Object.assign(_componentVisualRange, {\n    componentType: \"option\",\n});\nconst _componentWholeRange = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"wholeRange\",\n            DefaultsProps: {\n                defaultEndValue: \"endValue\",\n                defaultStartValue: \"startValue\"\n            },\n            ExpectedChildren: {\n                length: { optionName: \"length\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst WholeRange = Object.assign(_componentWholeRange, {\n    componentType: \"option\",\n});\nconst _componentWidth = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"width\",\n        },\n    });\n};\nconst Width = Object.assign(_componentWidth, {\n    componentType: \"option\",\n});\nconst _componentZoomAndPan = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"zoomAndPan\",\n            ExpectedChildren: {\n                dragBoxStyle: { optionName: \"dragBoxStyle\", isCollectionItem: false }\n            },\n        },\n    });\n};\nconst ZoomAndPan = Object.assign(_componentZoomAndPan, {\n    componentType: \"option\",\n});\nexport default Chart;\nexport { Chart, AdaptiveLayout, Aggregation, AggregationInterval, Animation, Annotation, AnnotationBorder, AnnotationImage, ArgumentAxis, ArgumentFormat, AxisConstantLineStyle, AxisConstantLineStyleLabel, AxisLabel, AxisTitle, BackgroundColor, Border, Break, BreakStyle, ChartTitle, ChartTitleSubtitle, Color, CommonAnnotationSettings, CommonAxisSettings, CommonAxisSettingsConstantLineStyle, CommonAxisSettingsConstantLineStyleLabel, CommonAxisSettingsLabel, CommonAxisSettingsTitle, CommonPaneSettings, CommonSeriesSettings, CommonSeriesSettingsHoverStyle, CommonSeriesSettingsLabel, CommonSeriesSettingsSelectionStyle, Connector, ConstantLine, ConstantLineLabel, ConstantLineStyle, Crosshair, DataPrepareSettings, DragBoxStyle, Export, Font, Format, Grid, Hatching, Height, HorizontalLine, HorizontalLineLabel, HoverStyle, Image, Label, Legend, LegendTitle, LegendTitleSubtitle, Length, LoadingIndicator, Margin, MinorGrid, MinorTick, MinorTickInterval, MinVisualRangeLength, Pane, PaneBorder, Point, PointBorder, PointHoverStyle, PointImage, PointSelectionStyle, Reduction, ScrollBar, SelectionStyle, Series, SeriesBorder, SeriesTemplate, Shadow, Size, Strip, StripLabel, StripStyle, StripStyleLabel, Subtitle, Tick, TickInterval, Title, Tooltip, TooltipBorder, Url, ValueAxis, ValueErrorBar, VerticalLine, VisualRange, WholeRange, Width, ZoomAndPan };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGD;AAEA;AACA;AACA;AALA;;;;;;AAMA,MAAM,QAAQ,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAClC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;qCAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,WAAW;gBACvC;YACJ,CAAC;oCAAG;QAAC,QAAQ,OAAO;KAAC;IACrB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE,IAAO;gBAAC;gBAAgB;gBAA2B;gBAA4B;gBAAoB;gBAAyB;gBAAa;gBAAwB;gBAAyB;gBAAqC;gBAAkC;gBAAuC;gBAAoC;gBAAoC;gBAAiC;gBAAsC;aAAkC;6CAAG,EAAE;IAClgB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;4CAAE,IAAO;gBAAC;gBAAuB;gBAAe;gBAAU;gBAAW;gBAAc;gBAAe;gBAAgB;gBAAsB;gBAAiB;gBAAiB;gBAAgB;gBAAiB;gBAAmB;gBAAkB;gBAAa;aAAc;2CAAG,EAAE;IAChT,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mCAAE,IAAM,CAAC;gBAC5B,qBAAqB;gBACrB,yBAAyB;gBACzB,kBAAkB;YACtB,CAAC;kCAAG,EAAE;IACN,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;2CAAE,IAAM,CAAC;gBACpC,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,YAAY;oBAAE,YAAY;oBAAe,kBAAkB;gBAAK;gBAChE,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,YAAY;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC3D,0BAA0B;oBAAE,YAAY;oBAA4B,kBAAkB;gBAAM;gBAC5F,oBAAoB;oBAAE,YAAY;oBAAsB,kBAAkB;gBAAM;gBAChF,oBAAoB;oBAAE,YAAY;oBAAsB,kBAAkB;gBAAM;gBAChF,sBAAsB;oBAAE,YAAY;oBAAwB,kBAAkB;gBAAM;gBACpF,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,qBAAqB;oBAAE,YAAY;oBAAuB,kBAAkB;gBAAM;gBAClF,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,kBAAkB;oBAAE,YAAY;oBAAoB,kBAAkB;gBAAM;gBAC5E,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,MAAM;oBAAE,YAAY;oBAAS,kBAAkB;gBAAK;gBACpD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAK;gBACvD,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,SAAS;oBAAE,YAAY;oBAAW,kBAAkB;gBAAM;gBAC1D,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAK;gBAC7D,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;YACpE,CAAC;0CAAG,EAAE;IACN,OAAQ,6JAAA,CAAA,gBAAmB,CAAE,kKAAA,CAAA,YAAa,EAAG;QACzC,aAAa,oJAAA,CAAA,UAAO;QACpB,KAAK;QACL,8BAA8B;QAC9B;QACA;QACA;QACA;QACA,GAAG,KAAK;IACZ;AACJ;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,gCAAgC,CAAC;IACnC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,sBAAsB,OAAO,MAAM,CAAC,+BAA+B;IACrE,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,kBAAkB;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAClE,iBAAiB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAChE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,6BAA6B,CAAC;IAChC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC,4BAA4B;IAC/D,eAAe;AACnB;AACA,MAAM,4BAA4B,CAAC;IAC/B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC,2BAA2B;IAC7D,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,mBAAmB;gBACnB,oBAAoB;YACxB;YACA,kBAAkB;gBACd,qBAAqB;oBAAE,YAAY;oBAAuB,kBAAkB;gBAAM;gBAClF,uBAAuB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAClF,WAAW;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1D,WAAW;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1D,OAAO;oBAAE,YAAY;oBAAU,kBAAkB;gBAAK;gBACtD,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,cAAc;oBAAE,YAAY;oBAAiB,kBAAkB;gBAAK;gBACpE,mBAAmB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAC9E,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,mBAAmB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAC9E,sBAAsB;oBAAE,YAAY;oBAAwB,kBAAkB;gBAAM;gBACpF,OAAO;oBAAE,YAAY;oBAAU,kBAAkB;gBAAK;gBACtD,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;YACpE;QACJ;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,kCAAkC,CAAC;IACrC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,wBAAwB,OAAO,MAAM,CAAC,iCAAiC;IACzE,eAAe;AACnB;AACA,MAAM,uCAAuC,CAAC;IAC1C,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,6BAA6B,OAAO,MAAM,CAAC,sCAAsC;IACnF,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,4BAA4B,CAAC;IAC/B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC,2BAA2B;IAC7D,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;QACtB;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,oBAAoB;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBACtE,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;YAChE;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,+BAA+B,CAAC;IAClC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,8BAA8B;IACnE,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,qCAAqC,CAAC;IACxC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,kBAAkB;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAClE,iBAAiB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAChE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,2BAA2B,OAAO,MAAM,CAAC,oCAAoC;IAC/E,eAAe;AACnB;AACA,MAAM,+BAA+B,CAAC;IAClC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,qCAAqC;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAChG,yBAAyB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACxE,yBAAyB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACxE,mBAAmB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAC9E,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,8BAA8B;IACnE,eAAe;AACnB;AACA,MAAM,gDAAgD,CAAC;IACnD,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,0CAA0C;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACzF,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,sCAAsC,OAAO,MAAM,CAAC,+CAA+C;IACrG,eAAe;AACnB;AACA,MAAM,qDAAqD,CAAC;IACxD,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,2CAA2C,OAAO,MAAM,CAAC,oDAAoD;IAC/G,eAAe;AACnB;AACA,MAAM,oCAAoC,CAAC;IACvC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,0BAA0B,OAAO,MAAM,CAAC,mCAAmC;IAC7E,eAAe;AACnB;AACA,MAAM,oCAAoC,CAAC;IACvC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,0BAA0B,OAAO,MAAM,CAAC,mCAAmC;IAC7E,eAAe;AACnB;AACA,MAAM,+BAA+B,CAAC;IAClC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,iBAAiB;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAM;gBAC1E,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,YAAY;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAChE;QACJ;IACJ;AACJ;AACA,MAAM,qBAAqB,OAAO,MAAM,CAAC,8BAA8B;IACnE,eAAe;AACnB;AACA,MAAM,iCAAiC,CAAC;IACpC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,gCAAgC;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBACpF,2BAA2B;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1E,oCAAoC;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBAC5F,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC9D,eAAe;oBAAE,YAAY;oBAAiB,kBAAkB;gBAAM;YAC1E;QACJ;IACJ;AACJ;AACA,MAAM,uBAAuB,OAAO,MAAM,CAAC,gCAAgC;IACvE,eAAe;AACnB;AACA,MAAM,2CAA2C,CAAC;IAC9C,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,iCAAiC,OAAO,MAAM,CAAC,0CAA0C;IAC3F,eAAe;AACnB;AACA,MAAM,sCAAsC,CAAC;IACzC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,4BAA4B,OAAO,MAAM,CAAC,qCAAqC;IACjF,eAAe;AACnB;AACA,MAAM,+CAA+C,CAAC;IAClD,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,qCAAqC,OAAO,MAAM,CAAC,8CAA8C;IACnG,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,8BAA8B,CAAC;IACjC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,oBAAoB,OAAO,MAAM,CAAC,6BAA6B;IACjE,eAAe;AACnB;AACA,MAAM,8BAA8B,CAAC;IACjC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,0CAA0C;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACzF,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,oBAAoB,OAAO,MAAM,CAAC,6BAA6B;IACjE,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,qBAAqB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACpE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;YACxE;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,gCAAgC,CAAC;IACnC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,sBAAsB,OAAO,MAAM,CAAC,+BAA+B;IACrE,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,qBAAqB,CAAC;IACxB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,WAAW,OAAO,MAAM,CAAC,oBAAoB;IAC/C,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,qBAAqB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACpE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,gCAAgC,CAAC;IACnC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;QACJ;IACJ;AACJ;AACA,MAAM,sBAAsB,OAAO,MAAM,CAAC,+BAA+B;IACrE,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,aAAa;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC7D,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,KAAK;oBAAE,YAAY;oBAAO,kBAAkB;gBAAM;gBAClD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAClE;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,kBAAkB;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAClE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,aAAa;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC5D,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,qBAAqB;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBACvE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;YAChE;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,gCAAgC,CAAC;IACnC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,sBAAsB,OAAO,MAAM,CAAC,+BAA+B;IACrE,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,6BAA6B,CAAC;IAChC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,aAAa;YACjB;YACA,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,mBAAmB,OAAO,MAAM,CAAC,4BAA4B;IAC/D,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,8BAA8B,CAAC;IACjC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,oBAAoB,OAAO,MAAM,CAAC,6BAA6B;IACjE,eAAe;AACnB;AACA,MAAM,iCAAiC,CAAC;IACpC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,uBAAuB,OAAO,MAAM,CAAC,gCAAgC;IACvE,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,iBAAiB;oBAAE,YAAY;oBAAmB,kBAAkB;gBAAM;gBAC1E,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,YAAY;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAChE;QACJ;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,aAAa;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC7D,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;YAC5E;QACJ;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,4BAA4B,CAAC;IAC/B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,aAAa;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YACjE;QACJ;IACJ;AACJ;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC,2BAA2B;IAC7D,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,KAAK;oBAAE,YAAY;oBAAO,kBAAkB;gBAAM;gBAClD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,gCAAgC,CAAC;IACnC,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,aAAa;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YACjE;QACJ;IACJ;AACJ;AACA,MAAM,sBAAsB,OAAO,MAAM,CAAC,+BAA+B;IACrE,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,UAAU;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBAC5D,aAAa;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC7D,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAClE;QACJ;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,gCAAgC;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBACpF,2BAA2B;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1E,oCAAoC;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBAC5F,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,cAAc;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBAC9D,eAAe;oBAAE,YAAY;oBAAiB,kBAAkB;gBAAM;YAC1E;QACJ;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,2BAA2B,CAAC;IAC9B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,iBAAiB,OAAO,MAAM,CAAC,0BAA0B;IAC3D,eAAe;AACnB;AACA,MAAM,mBAAmB,CAAC;IACtB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,SAAS,OAAO,MAAM,CAAC,kBAAkB;IAC3C,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,kBAAkB;gBACd,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,4BAA4B,CAAC;IAC/B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,kBAAkB,OAAO,MAAM,CAAC,2BAA2B;IAC7D,eAAe;AACnB;AACA,MAAM,qBAAqB,CAAC;IACxB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;YACxD;QACJ;IACJ;AACJ;AACA,MAAM,WAAW,OAAO,MAAM,CAAC,oBAAoB;IAC/C,eAAe;AACnB;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,oBAAoB;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBACtE,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,qBAAqB;oBAAE,YAAY;oBAAY,kBAAkB;gBAAM;gBACvE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;QACJ;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,oBAAoB,CAAC;IACvB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,gBAAgB;oBAAE,YAAY;oBAAkB,kBAAkB;gBAAM;gBACxE,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;gBACxD,eAAe;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YACnE;YACA,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,UAAU,OAAO,MAAM,CAAC,mBAAmB;IAC7C,eAAe;AACnB;AACA,MAAM,0BAA0B,CAAC;IAC7B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,gBAAgB,OAAO,MAAM,CAAC,yBAAyB;IACzD,eAAe;AACnB;AACA,MAAM,gBAAgB,CAAC;IACnB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,MAAM,OAAO,MAAM,CAAC,eAAe;IACrC,eAAe;AACnB;AACA,MAAM,sBAAsB,CAAC;IACzB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBACX,mBAAmB;gBACnB,oBAAoB;YACxB;YACA,kBAAkB;gBACd,uBAAuB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAClF,WAAW;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1D,WAAW;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBAC1D,OAAO;oBAAE,YAAY;oBAAU,kBAAkB;gBAAK;gBACtD,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,cAAc;oBAAE,YAAY;oBAAiB,kBAAkB;gBAAK;gBACpE,mBAAmB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAC9E,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,WAAW;oBAAE,YAAY;oBAAa,kBAAkB;gBAAM;gBAC9D,mBAAmB;oBAAE,YAAY;oBAAqB,kBAAkB;gBAAM;gBAC9E,sBAAsB;oBAAE,YAAY;oBAAwB,kBAAkB;gBAAM;gBACpF,OAAO;oBAAE,YAAY;oBAAU,kBAAkB;gBAAK;gBACtD,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;gBAChE,MAAM;oBAAE,YAAY;oBAAQ,kBAAkB;gBAAM;gBACpD,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;gBACpE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACtD,aAAa;oBAAE,YAAY;oBAAe,kBAAkB;gBAAM;gBAClE,YAAY;oBAAE,YAAY;oBAAc,kBAAkB;gBAAM;YACpE;QACJ;IACJ;AACJ;AACA,MAAM,YAAY,OAAO,MAAM,CAAC,qBAAqB;IACjD,eAAe;AACnB;AACA,MAAM,0BAA0B,CAAC;IAC7B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,gBAAgB,OAAO,MAAM,CAAC,yBAAyB;IACzD,eAAe;AACnB;AACA,MAAM,yBAAyB,CAAC;IAC5B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,qBAAqB;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;gBACpE,OAAO;oBAAE,YAAY;oBAAS,kBAAkB;gBAAM;YAC1D;QACJ;IACJ;AACJ;AACA,MAAM,eAAe,OAAO,MAAM,CAAC,wBAAwB;IACvD,eAAe;AACnB;AACA,MAAM,wBAAwB,CAAC;IAC3B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,iBAAiB;gBACjB,mBAAmB;YACvB;YACA,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;QACJ;IACJ;AACJ;AACA,MAAM,cAAc,OAAO,MAAM,CAAC,uBAAuB;IACrD,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,eAAe;gBACX,iBAAiB;gBACjB,mBAAmB;YACvB;YACA,kBAAkB;gBACd,QAAQ;oBAAE,YAAY;oBAAU,kBAAkB;gBAAM;YAC5D;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;AACA,MAAM,kBAAkB,CAAC;IACrB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;QAChB;IACJ;AACJ;AACA,MAAM,QAAQ,OAAO,MAAM,CAAC,iBAAiB;IACzC,eAAe;AACnB;AACA,MAAM,uBAAuB,CAAC;IAC1B,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;gBACd,cAAc;oBAAE,YAAY;oBAAgB,kBAAkB;gBAAM;YACxE;QACJ;IACJ;AACJ;AACA,MAAM,aAAa,OAAO,MAAM,CAAC,sBAAsB;IACnD,eAAe;AACnB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6578, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/drawer.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n\"use client\";\nimport * as React from \"react\";\nimport { memo, forwardRef, useImperativeHandle, useRef, useMemo } from \"react\";\nimport dxDrawer from \"devextreme/ui/drawer\";\nimport { Component as BaseComponent } from \"./core/component\";\nconst Drawer = memo(forwardRef((props, ref) => {\n    const baseRef = useRef(null);\n    useImperativeHandle(ref, () => ({\n        instance() {\n            return baseRef.current?.getInstance();\n        }\n    }), [baseRef.current]);\n    const subscribableOptions = useMemo(() => ([\"opened\"]), []);\n    const independentEvents = useMemo(() => ([\"onDisposing\", \"onInitialized\"]), []);\n    const defaults = useMemo(() => ({\n        defaultOpened: \"opened\",\n    }), []);\n    const templateProps = useMemo(() => ([\n        {\n            tmplOption: \"template\",\n            render: \"render\",\n            component: \"component\"\n        },\n    ]), []);\n    return (React.createElement((BaseComponent), {\n        WidgetClass: dxDrawer,\n        ref: baseRef,\n        subscribableOptions,\n        independentEvents,\n        defaults,\n        templateProps,\n        ...props,\n    }));\n}));\nexport default Drawer;\nexport { Drawer };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAGD;AAEA;AACA;AAJA;;;;;AAKA,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACnC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;sCAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,WAAW;gBACvC;YACJ,CAAC;qCAAG;QAAC,QAAQ,OAAO;KAAC;IACrB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;+CAAE,IAAO;gBAAC;aAAS;8CAAG,EAAE;IAC1D,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE,IAAO;gBAAC;gBAAe;aAAgB;4CAAG,EAAE;IAC9E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oCAAE,IAAM,CAAC;gBAC5B,eAAe;YACnB,CAAC;mCAAG,EAAE;IACN,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;yCAAE,IAAO;gBACjC;oBACI,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aACH;wCAAG,EAAE;IACN,OAAQ,6JAAA,CAAA,gBAAmB,CAAE,kKAAA,CAAA,YAAa,EAAG;QACzC,aAAa,oJAAA,CAAA,UAAQ;QACrB,KAAK;QACL;QACA;QACA;QACA;QACA,GAAG,KAAK;IACZ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6654, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/toolbar.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n\"use client\";\nimport * as React from \"react\";\nimport { memo, forwardRef, useImperativeHandle, useRef, useMemo } from \"react\";\nimport dxToolbar from \"devextreme/ui/toolbar\";\nimport { Component as BaseComponent } from \"./core/component\";\nimport NestedOption from \"./core/nested-option\";\nconst Toolbar = memo(forwardRef((props, ref) => {\n    const baseRef = useRef(null);\n    useImperativeHandle(ref, () => ({\n        instance() {\n            return baseRef.current?.getInstance();\n        }\n    }), [baseRef.current]);\n    const subscribableOptions = useMemo(() => ([\"items\"]), []);\n    const independentEvents = useMemo(() => ([\"onContentReady\", \"onDisposing\", \"onInitialized\", \"onItemClick\", \"onItemContextMenu\", \"onItemHold\", \"onItemRendered\"]), []);\n    const defaults = useMemo(() => ({\n        defaultItems: \"items\",\n    }), []);\n    const expectedChildren = useMemo(() => ({\n        item: { optionName: \"items\", isCollectionItem: true }\n    }), []);\n    const templateProps = useMemo(() => ([\n        {\n            tmplOption: \"itemTemplate\",\n            render: \"itemRender\",\n            component: \"itemComponent\"\n        },\n        {\n            tmplOption: \"menuItemTemplate\",\n            render: \"menuItemRender\",\n            component: \"menuItemComponent\"\n        },\n    ]), []);\n    return (React.createElement((BaseComponent), {\n        WidgetClass: dxToolbar,\n        ref: baseRef,\n        subscribableOptions,\n        independentEvents,\n        defaults,\n        expectedChildren,\n        templateProps,\n        ...props,\n    }));\n}));\nconst _componentItem = (props) => {\n    return React.createElement((NestedOption), {\n        ...props,\n        elementDescriptor: {\n            OptionName: \"items\",\n            IsCollectionItem: true,\n            TemplateProps: [{\n                    tmplOption: \"menuItemTemplate\",\n                    render: \"menuItemRender\",\n                    component: \"menuItemComponent\"\n                }, {\n                    tmplOption: \"template\",\n                    render: \"render\",\n                    component: \"component\"\n                }],\n        },\n    });\n};\nconst Item = Object.assign(_componentItem, {\n    componentType: \"option\",\n});\nexport default Toolbar;\nexport { Toolbar, Item };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;;AAGD;AAEA;AACA;AACA;AALA;;;;;;AAMA,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IACpC,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;uCAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,WAAW;gBACvC;YACJ,CAAC;sCAAG;QAAC,QAAQ,OAAO;KAAC;IACrB,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAO;gBAAC;aAAQ;+CAAG,EAAE;IACzD,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;8CAAE,IAAO;gBAAC;gBAAkB;gBAAe;gBAAiB;gBAAe;gBAAqB;gBAAc;aAAiB;6CAAG,EAAE;IACpK,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;qCAAE,IAAM,CAAC;gBAC5B,cAAc;YAClB,CAAC;oCAAG,EAAE;IACN,MAAM,mBAAmB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;6CAAE,IAAM,CAAC;gBACpC,MAAM;oBAAE,YAAY;oBAAS,kBAAkB;gBAAK;YACxD,CAAC;4CAAG,EAAE;IACN,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;0CAAE,IAAO;gBACjC;oBACI,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBACA;oBACI,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aACH;yCAAG,EAAE;IACN,OAAQ,6JAAA,CAAA,gBAAmB,CAAE,kKAAA,CAAA,YAAa,EAAG;QACzC,aAAa,qJAAA,CAAA,UAAS;QACtB,KAAK;QACL;QACA;QACA;QACA;QACA;QACA,GAAG,KAAK;IACZ;AACJ;AACA,MAAM,iBAAiB,CAAC;IACpB,OAAO,6JAAA,CAAA,gBAAmB,CAAE,yKAAA,CAAA,UAAY,EAAG;QACvC,GAAG,KAAK;QACR,mBAAmB;YACf,YAAY;YACZ,kBAAkB;YAClB,eAAe;gBAAC;oBACR,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;gBAAG;oBACC,YAAY;oBACZ,QAAQ;oBACR,WAAW;gBACf;aAAE;QACV;IACJ;AACJ;AACA,MAAM,OAAO,OAAO,MAAM,CAAC,gBAAgB;IACvC,eAAe;AACnB;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6776, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme-react/esm/load-indicator.js"], "sourcesContent": ["/*!\n * devextreme-react\n * Version: 25.1.3\n * Build date: Wed Jun 25 2025\n *\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\n *\n * This software may be modified and distributed under the terms\n * of the MIT license. See the LICENSE file in the root of the project for details.\n *\n * https://github.com/DevExpress/devextreme-react\n */\n\n\"use client\";\nimport * as React from \"react\";\nimport { memo, forwardRef, useImperativeHandle, useRef, useMemo } from \"react\";\nimport dxLoadIndicator from \"devextreme/ui/load_indicator\";\nimport { Component as BaseComponent } from \"./core/component\";\nconst LoadIndicator = memo(forwardRef((props, ref) => {\n    const baseRef = useRef(null);\n    useImperativeHandle(ref, () => ({\n        instance() {\n            return baseRef.current?.getInstance();\n        }\n    }), [baseRef.current]);\n    const independentEvents = useMemo(() => ([\"onContentReady\", \"onDisposing\", \"onInitialized\"]), []);\n    return (React.createElement((BaseComponent), {\n        WidgetClass: dxLoadIndicator,\n        ref: baseRef,\n        independentEvents,\n        ...props,\n    }));\n}));\nexport default LoadIndicator;\nexport { LoadIndicator };\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;CAWC;;;;AAGD;AAEA;AACA;AAJA;;;;;AAKA,MAAM,gBAAgB,CAAA,GAAA,6JAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE,CAAC,OAAO;IAC1C,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IACvB,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD,EAAE;6CAAK,IAAM,CAAC;gBAC5B;wBACW;oBAAP,QAAO,mBAAA,QAAQ,OAAO,cAAf,uCAAA,iBAAiB,WAAW;gBACvC;YACJ,CAAC;4CAAG;QAAC,QAAQ,OAAO;KAAC;IACrB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;oDAAE,IAAO;gBAAC;gBAAkB;gBAAe;aAAgB;mDAAG,EAAE;IAChG,OAAQ,6JAAA,CAAA,gBAAmB,CAAE,kKAAA,CAAA,YAAa,EAAG;QACzC,aAAa,4JAAA,CAAA,UAAe;QAC5B,KAAK;QACL;QACA,GAAG,KAAK;IACZ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}]}