{"version": 3, "sources": [], "sections": [{"offset": {"line": 37, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/lib/auth-client.ts"], "sourcesContent": ["// src/lib/auth-client.ts\r\n\"use client\";\r\n\r\nimport { jwtVerify } from \"jose\";\r\n\r\n// Secret key for JWT verification (should be stored in environment variables)\r\nconst JWT_SECRET = new TextEncoder().encode(\r\n  process.env.JWT_SECRET || \"your-secret-key-here\"\r\n);\r\n\r\nexport interface UserJwtPayload {\r\n  jti: string;\r\n  iat: number;\r\n  exp: number;\r\n  userId: string;\r\n  email: string;\r\n  roles: string[];\r\n}\r\n\r\n/**\r\n * Verifies the JWT token and returns the payload if valid\r\n */\r\nexport async function verifyToken(\r\n  token: string\r\n): Promise<UserJwtPayload | null> {\r\n  try {\r\n    const verified = await jwtVerify(token, JWT_SECRET);\r\n    return verified.payload as unknown as UserJwtPayload;\r\n  } catch (error) {\r\n    console.error(\"Token verification failed:\", error);\r\n    return null;\r\n  }\r\n}\r\n\r\n/**\r\n * Gets the token from cookies (client-side)\r\n */\r\nexport function getTokenFromCookies() {\r\n  if (typeof document !== \"undefined\") {\r\n    const cookies = document.cookie.split(\";\");\r\n    for (const cookie of cookies) {\r\n      const [name, value] = cookie.trim().split(\"=\");\r\n      if (name === \"oms_token\") {\r\n        return decodeURIComponent(value);\r\n      }\r\n    }\r\n  }\r\n  return null;\r\n}\r\n\r\n/**\r\n * Checks if the user is authenticated (client-side)\r\n */\r\nexport async function isAuthenticated() {\r\n  const token = getTokenFromCookies();\r\n  if (!token) return false;\r\n\r\n  const payload = await verifyToken(token);\r\n  return payload !== null;\r\n}\r\n\r\n/**\r\n * Gets the user payload from the token (client-side)\r\n */\r\nexport async function getUserPayload() {\r\n  const token = getTokenFromCookies();\r\n  if (!token) return null;\r\n\r\n  return await verifyToken(token);\r\n}\r\n\r\n/**\r\n * Logs out the user by calling the logout API and clearing the token\r\n */\r\nexport async function logout() {\r\n  try {\r\n    const response = await fetch(\"/api/auth/logout\", {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n    });\r\n\r\n    if (!response.ok) {\r\n      throw new Error(\"Logout failed\");\r\n    }\r\n\r\n    // Redirect to login page\r\n    window.location.href = \"/login\";\r\n  } catch (error) {\r\n    console.error(\"Logout error:\", error);\r\n    // Even if API call fails, redirect to login\r\n    window.location.href = \"/login\";\r\n  }\r\n}\r\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;;AAGzB;AAFA;;AAIA,8EAA8E;AAC9E,MAAM,aAAa,IAAI,cAAc,MAAM,CACzC,QAAQ,GAAG,CAAC,UAAU,IAAI;AAerB,eAAe,YACpB,KAAa;IAEb,IAAI;QACF,MAAM,WAAW,MAAM,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QACxC,OAAO,SAAS,OAAO;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO;IACT;AACF;AAKO,SAAS;IACd,IAAI,OAAO,aAAa,aAAa;QACnC,MAAM,UAAU,SAAS,MAAM,CAAC,KAAK,CAAC;QACtC,KAAK,MAAM,UAAU,QAAS;YAC5B,MAAM,CAAC,MAAM,MAAM,GAAG,OAAO,IAAI,GAAG,KAAK,CAAC;YAC1C,IAAI,SAAS,aAAa;gBACxB,OAAO,mBAAmB;YAC5B;QACF;IACF;IACA,OAAO;AACT;AAKO,eAAe;IACpB,MAAM,QAAQ;IACd,IAAI,CAAC,OAAO,OAAO;IAEnB,MAAM,UAAU,MAAM,YAAY;IAClC,OAAO,YAAY;AACrB;AAKO,eAAe;IACpB,MAAM,QAAQ;IACd,IAAI,CAAC,OAAO,OAAO;IAEnB,OAAO,MAAM,YAAY;AAC3B;AAKO,eAAe;IACpB,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;YAC/C,QAAQ;YACR,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEA,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iBAAiB;QAC/B,4CAA4C;QAC5C,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF", "debugId": null}}, {"offset": {"line": 104, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/components/AppLayout.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"appLayout\": \"AppLayout-module___DqYSa__appLayout\",\n  \"content\": \"AppLayout-module___DqYSa__content\",\n  \"dark\": \"AppLayout-module___DqYSa__dark\",\n  \"menuHeader\": \"AppLayout-module___DqYSa__menuHeader\",\n  \"menuIcon\": \"AppLayout-module___DqYSa__menuIcon\",\n  \"menuItem\": \"AppLayout-module___DqYSa__menuItem\",\n  \"menuItems\": \"AppLayout-module___DqYSa__menuItems\",\n  \"pageContent\": \"AppLayout-module___DqYSa__pageContent\",\n  \"sideMenu\": \"AppLayout-module___DqYSa__sideMenu\",\n  \"toolbar\": \"AppLayout-module___DqYSa__toolbar\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/lib/permissions.ts"], "sourcesContent": ["// src/lib/permissions.ts\r\n\r\n// Define available roles\r\nexport type Role = \"admin\" | \"user\" | \"manager\";\r\n\r\n// Define available permissions\r\nexport type Permission =\r\n  | \"view_dashboard\"\r\n  | \"view_users\"\r\n  | \"create_users\"\r\n  | \"edit_users\"\r\n  | \"delete_users\"\r\n  | \"view_settings\"\r\n  | \"edit_settings\"\r\n  | \"view_profile\"\r\n  | \"edit_profile\";\r\n\r\n// Define role to permissions mapping\r\nconst rolePermissions: Record<Role, Permission[]> = {\r\n  admin: [\r\n    \"view_dashboard\",\r\n    \"view_users\",\r\n    \"create_users\",\r\n    \"edit_users\",\r\n    \"delete_users\",\r\n    \"view_settings\",\r\n    \"edit_settings\",\r\n    \"view_profile\",\r\n    \"edit_profile\",\r\n  ],\r\n  manager: [\r\n    \"view_dashboard\",\r\n    \"view_users\",\r\n    \"create_users\",\r\n    \"edit_users\",\r\n    \"view_settings\",\r\n    \"view_profile\",\r\n    \"edit_profile\",\r\n  ],\r\n  user: [\"view_dashboard\", \"view_profile\", \"edit_profile\"],\r\n};\r\n\r\n/**\r\n * Check if a user has a specific permission\r\n */\r\nexport function hasPermission(\r\n  userRoles: Role[],\r\n  permission: Permission\r\n): boolean {\r\n  // Check if any of the user's roles has the required permission\r\n  return userRoles.some((role) => {\r\n    const permissions = rolePermissions[role] || [];\r\n    return permissions.includes(permission);\r\n  });\r\n}\r\n\r\n/**\r\n * Check if a user has all the specified permissions\r\n */\r\nexport function hasAllPermissions(\r\n  userRoles: Role[],\r\n  permissions: Permission[]\r\n): boolean {\r\n  return permissions.every((permission) =>\r\n    hasPermission(userRoles, permission)\r\n  );\r\n}\r\n\r\n/**\r\n * Check if a user has any of the specified permissions\r\n */\r\nexport function hasAnyPermission(\r\n  userRoles: Role[],\r\n  permissions: Permission[]\r\n): boolean {\r\n  return permissions.some((permission) => hasPermission(userRoles, permission));\r\n}\r\n\r\n/**\r\n * Check if a user has a specific role\r\n */\r\nexport function hasRole(userRoles: Role[], role: Role): boolean {\r\n  return userRoles.includes(role);\r\n}\r\n\r\n/**\r\n * Check if a user has all the specified roles\r\n */\r\nexport function hasAllRoles(userRoles: Role[], roles: Role[]): boolean {\r\n  return roles.every((role) => hasRole(userRoles, role));\r\n}\r\n\r\n/**\r\n * Check if a user has any of the specified roles\r\n */\r\nexport function hasAnyRole(userRoles: Role[], roles: Role[]): boolean {\r\n  return roles.some((role) => hasRole(userRoles, role));\r\n}\r\n"], "names": [], "mappings": "AAAA,yBAAyB;AAEzB,yBAAyB;;;;;;;;;AAezB,qCAAqC;AACrC,MAAM,kBAA8C;IAClD,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,SAAS;QACP;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,MAAM;QAAC;QAAkB;QAAgB;KAAe;AAC1D;AAKO,SAAS,cACd,SAAiB,EACjB,UAAsB;IAEtB,+DAA+D;IAC/D,OAAO,UAAU,IAAI,CAAC,CAAC;QACrB,MAAM,cAAc,eAAe,CAAC,KAAK,IAAI,EAAE;QAC/C,OAAO,YAAY,QAAQ,CAAC;IAC9B;AACF;AAKO,SAAS,kBACd,SAAiB,EACjB,WAAyB;IAEzB,OAAO,YAAY,KAAK,CAAC,CAAC,aACxB,cAAc,WAAW;AAE7B;AAKO,SAAS,iBACd,SAAiB,EACjB,WAAyB;IAEzB,OAAO,YAAY,IAAI,CAAC,CAAC,aAAe,cAAc,WAAW;AACnE;AAKO,SAAS,QAAQ,SAAiB,EAAE,IAAU;IACnD,OAAO,UAAU,QAAQ,CAAC;AAC5B;AAKO,SAAS,YAAY,SAAiB,EAAE,KAAa;IAC1D,OAAO,MAAM,KAAK,CAAC,CAAC,OAAS,QAAQ,WAAW;AAClD;AAKO,SAAS,WAAW,SAAiB,EAAE,KAAa;IACzD,OAAO,MAAM,IAAI,CAAC,CAAC,OAAS,QAAQ,WAAW;AACjD", "debugId": null}}, {"offset": {"line": 184, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/components/AppLayout.tsx"], "sourcesContent": ["\"use client\";\nimport { useState, useEffect } from \"react\";\nimport { useRouter } from \"next/navigation\";\nimport Drawer from \"devextreme-react/drawer\";\nimport Toolbar, { Item } from \"devextreme-react/toolbar\";\nimport Button from \"devextreme-react/button\";\nimport { logout, getUserPayload } from \"@/lib/auth-client\";\nimport styles from \"./AppLayout.module.css\";\nimport { hasPermission } from \"@/lib/permissions\";\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n  title?: string;\n}\n\nconst AppLayout = ({ children, title = \"OMSNext\" }: AppLayoutProps) => {\n  const [drawerOpened, setDrawerOpened] = useState(false);\n  const [user, setUser] = useState<{ email: string; roles: string[] } | null>(\n    null\n  );\n  const router = useRouter();\n\n  useEffect(() => {\n    const fetchUser = async () => {\n      try {\n        const payload = await getUserPayload();\n        if (payload) {\n          setUser({\n            email: payload.email,\n            roles: payload.roles,\n          });\n        }\n      } catch (error) {\n        console.error(\"Failed to fetch user:\", error);\n      }\n    };\n\n    fetchUser();\n  }, []);\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  const menuItems = [\n    {\n      text: \"Dashboard\",\n      icon: \"home\",\n      path: \"/dashboard\",\n      permission: \"view_dashboard\",\n    },\n    {\n      text: \"Felhasználók\",\n      icon: \"user\",\n      path: \"/users\",\n      permission: \"view_users\",\n    },\n    {\n      text: \"Beállítások\",\n      icon: \"preferences\",\n      path: \"/settings\",\n      permission: \"view_settings\",\n    },\n  ].filter((item) => {\n    if (!user) return false;\n    return hasPermission(user.roles as any, item.permission as any);\n  });\n\n  const handleMenuItemClick = (path: string) => {\n    router.push(path);\n    setDrawerOpened(false);\n  };\n\n  const toolbarItems = [\n    {\n      widget: \"dxButton\",\n      location: \"before\" as const,\n      options: {\n        icon: \"menu\",\n        stylingMode: \"text\",\n        onClick: () => setDrawerOpened(!drawerOpened),\n      },\n    },\n    {\n      text: title,\n      location: \"center\" as const,\n    },\n    user && {\n      template: () => (\n        <div\n          style={{ display: \"flex\", alignItems: \"center\", padding: \"0 12px\" }}\n        >\n          <span style={{ marginRight: \"12px\", fontSize: \"14px\" }}>\n            {user.email}\n          </span>\n        </div>\n      ),\n      location: \"after\" as const,\n    },\n    {\n      widget: \"dxButton\",\n      location: \"after\" as const,\n      options: {\n        icon: \"runner\",\n        text: \"Kijelentkezés\",\n        stylingMode: \"text\",\n        onClick: handleLogout,\n      },\n    },\n  ].filter(Boolean) as any[];\n\n  const renderMenu = () => (\n    <div className={styles.sideMenu}>\n      <div className={styles.menuHeader}>\n        <h3>OMSNext</h3>\n      </div>\n      <div className={styles.menuItems}>\n        {menuItems.map((item, index) => (\n          <div\n            key={index}\n            className={styles.menuItem}\n            onClick={() => handleMenuItemClick(item.path)}\n          >\n            <i className={`dx-icon-${item.icon} ${styles.menuIcon}`}></i>\n            <span>{item.text}</span>\n          </div>\n        ))}\n      </div>\n    </div>\n  );\n\n  return (\n    <div className={styles.appLayout}>\n      <Drawer\n        opened={drawerOpened}\n        openedStateMode=\"overlap\"\n        position=\"left\"\n        revealMode=\"slide\"\n        render={renderMenu}\n        closeOnOutsideClick={true}\n        onOpenedChange={setDrawerOpened}\n      >\n        <div className={styles.content}>\n          <Toolbar items={toolbarItems} className={styles.toolbar} />\n          <div className={styles.pageContent}>{children}</div>\n        </div>\n      </Drawer>\n    </div>\n  );\n};\n\nexport default AppLayout;\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;;AAeA,MAAM,YAAY,CAAC,EAAE,QAAQ,EAAE,QAAQ,SAAS,EAAkB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7B;IAEF,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;gBACnC,IAAI,SAAS;oBACX,QAAQ;wBACN,OAAO,QAAQ,KAAK;wBACpB,OAAO,QAAQ,KAAK;oBACtB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;YACzC;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,eAAe;QACnB,MAAM,CAAA,GAAA,4HAAA,CAAA,SAAM,AAAD;IACb;IAEA,MAAM,YAAY;QAChB;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,YAAY;QACd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,YAAY;QACd;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM;YACN,YAAY;QACd;KACD,CAAC,MAAM,CAAC,CAAC;QACR,IAAI,CAAC,MAAM,OAAO;QAClB,OAAO,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,EAAS,KAAK,UAAU;IACzD;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAO,IAAI,CAAC;QACZ,gBAAgB;IAClB;IAEA,MAAM,eAAe;QACnB;YACE,QAAQ;YACR,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,aAAa;gBACb,SAAS,IAAM,gBAAgB,CAAC;YAClC;QACF;QACA;YACE,MAAM;YACN,UAAU;QACZ;QACA,QAAQ;YACN,UAAU,kBACR,8OAAC;oBACC,OAAO;wBAAE,SAAS;wBAAQ,YAAY;wBAAU,SAAS;oBAAS;8BAElE,cAAA,8OAAC;wBAAK,OAAO;4BAAE,aAAa;4BAAQ,UAAU;wBAAO;kCAClD,KAAK,KAAK;;;;;;;;;;;YAIjB,UAAU;QACZ;QACA;YACE,QAAQ;YACR,UAAU;YACV,SAAS;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;QACF;KACD,CAAC,MAAM,CAAC;IAET,MAAM,aAAa,kBACjB,8OAAC;YAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,QAAQ;;8BAC7B,8OAAC;oBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,UAAU;8BAC/B,cAAA,8OAAC;kCAAG;;;;;;;;;;;8BAEN,8OAAC;oBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,SAAS;8BAC7B,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,8OAAC;4BAEC,WAAW,0IAAA,CAAA,UAAM,CAAC,QAAQ;4BAC1B,SAAS,IAAM,oBAAoB,KAAK,IAAI;;8CAE5C,8OAAC;oCAAE,WAAW,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,CAAC,EAAE,0IAAA,CAAA,UAAM,CAAC,QAAQ,EAAE;;;;;;8CACvD,8OAAC;8CAAM,KAAK,IAAI;;;;;;;2BALX;;;;;;;;;;;;;;;;IAYf,qBACE,8OAAC;QAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,SAAS;kBAC9B,cAAA,8OAAC,oJAAA,CAAA,UAAM;YACL,QAAQ;YACR,iBAAgB;YAChB,UAAS;YACT,YAAW;YACX,QAAQ;YACR,qBAAqB;YACrB,gBAAgB;sBAEhB,cAAA,8OAAC;gBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,OAAO;;kCAC5B,8OAAC,qJAAA,CAAA,UAAO;wBAAC,OAAO;wBAAc,WAAW,0IAAA,CAAA,UAAM,CAAC,OAAO;;;;;;kCACvD,8OAAC;wBAAI,WAAW,0IAAA,CAAA,UAAM,CAAC,WAAW;kCAAG;;;;;;;;;;;;;;;;;;;;;;AAK/C;uCAEe", "debugId": null}}, {"offset": {"line": 409, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/hooks/useAuth.ts"], "sourcesContent": ["// src/hooks/useAuth.ts\r\n\"use client\";\r\n\r\nimport { useState, useEffect } from \"react\";\r\nimport { getUserPayload, isAuthenticated } from \"@/lib/auth-client\";\r\n\r\nexport interface User {\r\n  userId: string;\r\n  email: string;\r\n  roles: string[];\r\n}\r\n\r\nexport function useAuth() {\r\n  const [user, setUser] = useState<User | null>(null);\r\n  const [loading, setLoading] = useState(true);\r\n  const [authenticated, setAuthenticated] = useState(false);\r\n\r\n  useEffect(() => {\r\n    const checkAuth = async () => {\r\n      try {\r\n        const authStatus = await isAuthenticated();\r\n        setAuthenticated(authStatus);\r\n\r\n        if (authStatus) {\r\n          const payload = await getUserPayload();\r\n          if (payload) {\r\n            setUser({\r\n              userId: payload.userId,\r\n              email: payload.email,\r\n              roles: payload.roles,\r\n            });\r\n          }\r\n        }\r\n      } catch (error) {\r\n        console.error(\"Authentication check failed:\", error);\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    checkAuth();\r\n  }, []);\r\n\r\n  return { user, loading, authenticated };\r\n}\r\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;AAGvB;AACA;AAHA;;;AAWO,SAAS;IACd,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,aAAa,MAAM,CAAA,GAAA,4HAAA,CAAA,kBAAe,AAAD;gBACvC,iBAAiB;gBAEjB,IAAI,YAAY;oBACd,MAAM,UAAU,MAAM,CAAA,GAAA,4HAAA,CAAA,iBAAc,AAAD;oBACnC,IAAI,SAAS;wBACX,QAAQ;4BACN,QAAQ,QAAQ,MAAM;4BACtB,OAAO,QAAQ,KAAK;4BACpB,OAAO,QAAQ,KAAK;wBACtB;oBACF;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gCAAgC;YAChD,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,OAAO;QAAE;QAAM;QAAS;IAAc;AACxC", "debugId": null}}, {"offset": {"line": 455, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/components/PermissionGuard.tsx"], "sourcesContent": ["// src/components/PermissionGuard.tsx\r\n\"use client\";\r\n\r\nimport { ReactNode, useEffect } from \"react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport { hasPermission, Permission } from \"@/lib/permissions\";\r\n\r\ninterface PermissionGuardProps {\r\n  children: ReactNode;\r\n  permission: Permission;\r\n}\r\n\r\nexport default function PermissionGuard({\r\n  children,\r\n  permission,\r\n}: PermissionGuardProps) {\r\n  const router = useRouter();\r\n  const { user, loading, authenticated } = useAuth();\r\n\r\n  useEffect(() => {\r\n    // Redirect to login if not authenticated\r\n    if (!loading && !authenticated) {\r\n      router.push(\"/login\");\r\n    }\r\n\r\n    // Redirect to dashboard if authenticated but doesn't have permission\r\n    if (\r\n      !loading &&\r\n      authenticated &&\r\n      user &&\r\n      !hasPermission(user.roles as any, permission)\r\n    ) {\r\n      router.push(\"/dashboard\");\r\n    }\r\n  }, [authenticated, loading, permission, router, user]);\r\n\r\n  // Show nothing while checking permissions\r\n  if (loading || !authenticated || !user) {\r\n    return <div>Loading...</div>;\r\n  }\r\n\r\n  // Check if user has the required permission\r\n  if (!hasPermission(user.roles as any, permission)) {\r\n    return <div>Nincs jogosultsága az oldal megtekintéséhez.</div>;\r\n  }\r\n\r\n  return <>{children}</>;\r\n}\r\n"], "names": [], "mappings": "AAAA,qCAAqC;;;;;AAGrC;AACA;AACA;AACA;AALA;;;;;;AAYe,SAAS,gBAAgB,EACtC,QAAQ,EACR,UAAU,EACW;IACrB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAE/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,IAAI,CAAC,WAAW,CAAC,eAAe;YAC9B,OAAO,IAAI,CAAC;QACd;QAEA,qEAAqE;QACrE,IACE,CAAC,WACD,iBACA,QACA,CAAC,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,EAAS,aAClC;YACA,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAe;QAAS;QAAY;QAAQ;KAAK;IAErD,0CAA0C;IAC1C,IAAI,WAAW,CAAC,iBAAiB,CAAC,MAAM;QACtC,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,4CAA4C;IAC5C,IAAI,CAAC,CAAA,GAAA,yHAAA,CAAA,gBAAa,AAAD,EAAE,KAAK,KAAK,EAAS,aAAa;QACjD,qBAAO,8OAAC;sBAAI;;;;;;IACd;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 517, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/app/%28protected%29/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useEffect, useState } from \"react\";\r\nimport DataGrid, { Column } from \"devextreme-react/data-grid\";\r\nimport Chart, {\r\n  CommonSeriesSettings,\r\n  Series,\r\n  Export,\r\n  Legend,\r\n  Margin,\r\n  Tooltip,\r\n  Title,\r\n  Subtitle,\r\n} from \"devextreme-react/chart\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport AppLayout from \"../../../components/AppLayout\";\r\nimport LoadIndicator from \"devextreme-react/load-indicator\";\r\nimport { useAuth } from \"@/hooks/useAuth\";\r\nimport PermissionGuard from \"@/components/PermissionGuard\";\r\nimport { hasPermission } from \"@/lib/permissions\";\r\n\r\nexport default function Dashboard() {\r\n  const router = useRouter();\r\n  const { user, loading: authLoading, authenticated } = useAuth();\r\n  const [data, setData] = useState<any[]>([]);\r\n  const [chartData, setChartData] = useState<any[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n\r\n  useEffect(() => {\r\n    // Auth check is handled by useAuth hook\r\n    if (!authLoading && !authenticated) {\r\n      router.push(\"/login\");\r\n      return;\r\n    }\r\n\r\n    // Mock data for demonstration\r\n    setTimeout(() => {\r\n      setData([\r\n        { id: 1, name: \"Teszt Felhasználó 1\", balance: 15000 },\r\n        { id: 2, name: \"Teszt Felhasználó 2\", balance: 25000 },\r\n        { id: 3, name: \"Teszt Felhasználó 3\", balance: 8500 },\r\n        { id: 4, name: \"Teszt Felhasználó 4\", balance: 32000 },\r\n        { id: 5, name: \"Teszt Felhasználó 5\", balance: 12500 },\r\n      ]);\r\n\r\n      setChartData([\r\n        { month: \"Jan\", revenue: 10000, expense: 8000 },\r\n        { month: \"Feb\", revenue: 12000, expense: 9000 },\r\n        { month: \"Már\", revenue: 15000, expense: 10000 },\r\n        { month: \"Ápr\", revenue: 18000, expense: 12000 },\r\n        { month: \"Máj\", revenue: 20000, expense: 15000 },\r\n        { month: \"Jún\", revenue: 22000, expense: 17000 },\r\n      ]);\r\n\r\n      setLoading(false);\r\n    }, 1000);\r\n  }, [authLoading, authenticated, router]);\r\n\r\n  if (authLoading || loading) {\r\n    return (\r\n      <AppLayout title=\"Dashboard\">\r\n        <div\r\n          style={{\r\n            display: \"flex\",\r\n            justifyContent: \"center\",\r\n            alignItems: \"center\",\r\n            height: \"200px\",\r\n          }}\r\n        >\r\n          <LoadIndicator width={40} height={40} />\r\n        </div>\r\n      </AppLayout>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <PermissionGuard permission=\"view_dashboard\">\r\n      <AppLayout title=\"Dashboard\">\r\n        <div\r\n          className=\"dx-card\"\r\n          style={{ padding: \"24px\", marginBottom: \"20px\" }}\r\n        >\r\n          <h2\r\n            style={{\r\n              fontSize: \"24px\",\r\n              fontWeight: 500,\r\n              marginBottom: \"24px\",\r\n              color: \"var(--base-text-color)\",\r\n            }}\r\n          >\r\n            Pénzügyi Áttekintés\r\n          </h2>\r\n          <Chart id=\"chart\" dataSource={chartData} palette=\"Material\">\r\n            <CommonSeriesSettings argumentField=\"month\" type=\"line\" />\r\n            <Series valueField=\"revenue\" name=\"Bevétel\" />\r\n            <Series valueField=\"expense\" name=\"Kiadás\" />\r\n            <Margin bottom={20} />\r\n            <Title text=\"Bevétel és Kiadás\">\r\n              <Subtitle text=\"(ezer Ft)\" />\r\n            </Title>\r\n            <Export enabled={true} />\r\n            <Legend verticalAlignment=\"bottom\" horizontalAlignment=\"center\" />\r\n            <Tooltip enabled={true} />\r\n          </Chart>\r\n        </div>\r\n\r\n        <div className=\"dx-card\" style={{ padding: \"24px\" }}>\r\n          <h2\r\n            style={{\r\n              fontSize: \"24px\",\r\n              fontWeight: 500,\r\n              marginBottom: \"24px\",\r\n              color: \"var(--base-text-color)\",\r\n            }}\r\n          >\r\n            Felhasználói Adatok\r\n          </h2>\r\n          <DataGrid\r\n            dataSource={data}\r\n            height={400}\r\n            showBorders={true}\r\n            rowAlternationEnabled={true}\r\n            columnAutoWidth={true}\r\n          >\r\n            <Column dataField=\"id\" caption=\"ID\" width={80} />\r\n            <Column dataField=\"name\" caption=\"Név\" />\r\n            <Column\r\n              dataField=\"balance\"\r\n              caption=\"Egyenleg\"\r\n              dataType=\"number\"\r\n              format=\"currency\"\r\n            />\r\n          </DataGrid>\r\n        </div>\r\n      </AppLayout>\r\n    </PermissionGuard>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAUA;AACA;AACA;AACA;AACA;AAjBA;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,UAAO,AAAD;IAC5D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IACpD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wCAAwC;QACxC,IAAI,CAAC,eAAe,CAAC,eAAe;YAClC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,8BAA8B;QAC9B,WAAW;YACT,QAAQ;gBACN;oBAAE,IAAI;oBAAG,MAAM;oBAAuB,SAAS;gBAAM;gBACrD;oBAAE,IAAI;oBAAG,MAAM;oBAAuB,SAAS;gBAAM;gBACrD;oBAAE,IAAI;oBAAG,MAAM;oBAAuB,SAAS;gBAAK;gBACpD;oBAAE,IAAI;oBAAG,MAAM;oBAAuB,SAAS;gBAAM;gBACrD;oBAAE,IAAI;oBAAG,MAAM;oBAAuB,SAAS;gBAAM;aACtD;YAED,aAAa;gBACX;oBAAE,OAAO;oBAAO,SAAS;oBAAO,SAAS;gBAAK;gBAC9C;oBAAE,OAAO;oBAAO,SAAS;oBAAO,SAAS;gBAAK;gBAC9C;oBAAE,OAAO;oBAAO,SAAS;oBAAO,SAAS;gBAAM;gBAC/C;oBAAE,OAAO;oBAAO,SAAS;oBAAO,SAAS;gBAAM;gBAC/C;oBAAE,OAAO;oBAAO,SAAS;oBAAO,SAAS;gBAAM;gBAC/C;oBAAE,OAAO;oBAAO,SAAS;oBAAO,SAAS;gBAAM;aAChD;YAED,WAAW;QACb,GAAG;IACL,GAAG;QAAC;QAAa;QAAe;KAAO;IAEvC,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC,+HAAA,CAAA,UAAS;YAAC,OAAM;sBACf,cAAA,8OAAC;gBACC,OAAO;oBACL,SAAS;oBACT,gBAAgB;oBAChB,YAAY;oBACZ,QAAQ;gBACV;0BAEA,cAAA,8OAAC,+JAAA,CAAA,UAAa;oBAAC,OAAO;oBAAI,QAAQ;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,8OAAC,qIAAA,CAAA,UAAe;QAAC,YAAW;kBAC1B,cAAA,8OAAC,+HAAA,CAAA,UAAS;YAAC,OAAM;;8BACf,8OAAC;oBACC,WAAU;oBACV,OAAO;wBAAE,SAAS;wBAAQ,cAAc;oBAAO;;sCAE/C,8OAAC;4BACC,OAAO;gCACL,UAAU;gCACV,YAAY;gCACZ,cAAc;gCACd,OAAO;4BACT;sCACD;;;;;;sCAGD,8OAAC,mJAAA,CAAA,UAAK;4BAAC,IAAG;4BAAQ,YAAY;4BAAW,SAAQ;;8CAC/C,8OAAC,mJAAA,CAAA,uBAAoB;oCAAC,eAAc;oCAAQ,MAAK;;;;;;8CACjD,8OAAC,mJAAA,CAAA,SAAM;oCAAC,YAAW;oCAAU,MAAK;;;;;;8CAClC,8OAAC,mJAAA,CAAA,SAAM;oCAAC,YAAW;oCAAU,MAAK;;;;;;8CAClC,8OAAC,mJAAA,CAAA,SAAM;oCAAC,QAAQ;;;;;;8CAChB,8OAAC,mJAAA,CAAA,QAAK;oCAAC,MAAK;8CACV,cAAA,8OAAC,mJAAA,CAAA,WAAQ;wCAAC,MAAK;;;;;;;;;;;8CAEjB,8OAAC,mJAAA,CAAA,SAAM;oCAAC,SAAS;;;;;;8CACjB,8OAAC,mJAAA,CAAA,SAAM;oCAAC,mBAAkB;oCAAS,qBAAoB;;;;;;8CACvD,8OAAC,mJAAA,CAAA,UAAO;oCAAC,SAAS;;;;;;;;;;;;;;;;;;8BAItB,8OAAC;oBAAI,WAAU;oBAAU,OAAO;wBAAE,SAAS;oBAAO;;sCAChD,8OAAC;4BACC,OAAO;gCACL,UAAU;gCACV,YAAY;gCACZ,cAAc;gCACd,OAAO;4BACT;sCACD;;;;;;sCAGD,8OAAC,0JAAA,CAAA,UAAQ;4BACP,YAAY;4BACZ,QAAQ;4BACR,aAAa;4BACb,uBAAuB;4BACvB,iBAAiB;;8CAEjB,8OAAC,0JAAA,CAAA,SAAM;oCAAC,WAAU;oCAAK,SAAQ;oCAAK,OAAO;;;;;;8CAC3C,8OAAC,0JAAA,CAAA,SAAM;oCAAC,WAAU;oCAAO,SAAQ;;;;;;8CACjC,8OAAC,0JAAA,CAAA,SAAM;oCACL,WAAU;oCACV,SAAQ;oCACR,UAAS;oCACT,QAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB", "debugId": null}}]}