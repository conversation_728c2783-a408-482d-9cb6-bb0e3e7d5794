<dx:ThemedWindow x:Class="omsnext.wpf.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        Title="OmsNext - Bejelentkezés" 
        Height="800" Width="550"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize"
        Background="Transparent">
    
    <Grid>
        <!-- H<PERSON>ttér gradient -->
        <Grid.Background>
            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                <GradientStop Color="#667eea" Offset="0"/>
                <GradientStop Color="#764ba2" Offset="1"/>
            </LinearGradientBrush>
        </Grid.Background>
        
        <!-- F<PERSON>kártya -->
        <Border Margin="40" 
                CornerRadius="20" 
                Background="White" 
                Padding="50,40">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="315" ShadowDepth="8" Opacity="0.25" BlurRadius="25"/>
            </Border.Effect>
            
            <StackPanel VerticalAlignment="Center">
                
                <!-- Logo és címsor -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,35">
                    <!-- Modern logo ikon -->
                    <Border Width="80" Height="80" 
                           CornerRadius="40" 
                           Margin="0,0,0,20">
                        <Border.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#667eea" Offset="0"/>
                                <GradientStop Color="#764ba2" Offset="1"/>
                            </LinearGradientBrush>
                        </Border.Background>
                        <TextBlock Text="O" 
                                 FontSize="42" 
                                 FontWeight="Bold" 
                                 Foreground="White"
                                 HorizontalAlignment="Center"
                                 VerticalAlignment="Center"/>
                    </Border>
                    
                    <TextBlock Text="OmsNext" 
                             FontSize="32" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center" 
                             Foreground="#2D3748"/>
                    <TextBlock Text="Üdvözöljük!" 
                             FontSize="16" 
                             HorizontalAlignment="Center" 
                             Foreground="#718096" 
                             Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Login form -->
                <StackPanel x:Name="LoginPanel">
                    
                    <!-- Email mező -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="E-mail cím" 
                                 FontWeight="SemiBold" 
                                 Foreground="#4A5568" 
                                 Margin="0,0,0,8"/>
                        <Border CornerRadius="10" 
                              BorderBrush="#E2E8F0" 
                              BorderThickness="2" 
                              Background="#F7FAFC">
                            <dxe:TextEdit x:Name="EmailTextEdit" 
                                        NullText="<EMAIL>" 
                                        FontSize="14"
                                        Height="45"
                                        Padding="15,0"
                                        BorderThickness="0"
                                        Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- Jelszó mező -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="Jelszó" 
                                 FontWeight="SemiBold" 
                                 Foreground="#4A5568" 
                                 Margin="0,0,0,8"/>
                        <Border CornerRadius="10" 
                              BorderBrush="#E2E8F0" 
                              BorderThickness="2" 
                              Background="#F7FAFC">
                            <dxe:PasswordBoxEdit x:Name="PasswordBoxEdit" 
                                               NullText="••••••••" 
                                               FontSize="14"
                                               Height="45"
                                               Padding="15,0"
                                               BorderThickness="0"
                                               Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- Elfelejtett jelszó link -->
                    <TextBlock HorizontalAlignment="Right" Margin="0,0,0,25">
                        <Hyperlink x:Name="ForgotPasswordLink" 
                                 Click="ForgotPasswordLink_Click"
                                 Foreground="#667eea" 
                                 TextDecorations="None"
                                 FontSize="14"
                                 FontWeight="Medium">
                            <Hyperlink.Style>
                                <Style TargetType="Hyperlink">
                                    <Setter Property="TextDecorations" Value="None"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="TextDecorations" Value="Underline"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Hyperlink.Style>
                            Elfelejtett jelszó?
                        </Hyperlink>
                    </TextBlock>

                    <!-- Bejelentkezés gomb -->
                    <Button x:Name="LoginButton" 
                          Height="50" 
                          FontSize="16" 
                          FontWeight="SemiBold"
                          Foreground="White"
                          Margin="0,0,0,20"
                          Click="LoginButton_Click"
                          Cursor="Hand">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Name="border" 
                                      CornerRadius="12" 
                                      BorderThickness="0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#667eea" Offset="0"/>
                                            <GradientStop Color="#764ba2" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <ContentPresenter HorizontalAlignment="Center" 
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                                    </Trigger>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                        Bejelentkezés
                    </Button>

                    <!-- Hibaüzenet -->
                    <Border x:Name="ErrorPanel" 
                          CornerRadius="10" 
                          Background="#FED7D7" 
                          BorderBrush="#FC8181" 
                          BorderThickness="1" 
                          Padding="15"
                          Margin="0,0,0,15"
                          Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="⚠" 
                                     Foreground="#E53E3E" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     Margin="0,0,10,0"/>
                            <TextBlock x:Name="ErrorMessage" 
                                     Foreground="#E53E3E" 
                                     FontSize="14"
                                     FontWeight="Medium"
                                     TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>

                    <!-- Loading indikátor -->
                    <dx:WaitIndicator x:Name="LoadingIndicator" 
                                    Content="Bejelentkezés..." 
                                    DeferedVisibility="False" 
                                    Visibility="Collapsed"
                                    Margin="0,10,0,0"/>
                </StackPanel>

                <!-- Elfelejtett jelszó panel -->
                <StackPanel x:Name="ForgotPasswordPanel" Visibility="Collapsed">
                    
                    <TextBlock Text="Elfelejtett jelszó" 
                             FontSize="28" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center" 
                             Foreground="#2D3748"
                             Margin="0,0,0,12"/>
                    
                    <TextBlock Text="Adja meg e-mail címét és értesítjük az adminisztrátort a jelszó visszaállítási kérelemről." 
                             FontSize="14" 
                             Foreground="#718096" 
                             TextAlignment="Center"
                             TextWrapping="Wrap"
                             LineHeight="20"
                             Margin="0,0,0,30"/>

                    <!-- Email mező reset-hez -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="E-mail cím" 
                                 FontWeight="SemiBold" 
                                 Foreground="#4A5568" 
                                 Margin="0,0,0,8"/>
                        <Border CornerRadius="10" 
                              BorderBrush="#E2E8F0" 
                              BorderThickness="2" 
                              Background="#F7FAFC">
                            <dxe:TextEdit x:Name="ResetEmailTextEdit" 
                                        NullText="<EMAIL>" 
                                        FontSize="14"
                                        Height="45"
                                        Padding="15,0"
                                        BorderThickness="0"
                                        Background="Transparent"/>
                        </Border>
                    </StackPanel>

                    <!-- Vissza link -->
                    <TextBlock HorizontalAlignment="Center" Margin="0,0,0,25">
                        <Hyperlink x:Name="BackToLoginLink" 
                                 Click="BackToLoginLink_Click"
                                 Foreground="#667eea" 
                                 TextDecorations="None"
                                 FontSize="14"
                                 FontWeight="Medium">
                            <Hyperlink.Style>
                                <Style TargetType="Hyperlink">
                                    <Setter Property="TextDecorations" Value="None"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="TextDecorations" Value="Underline"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </Hyperlink.Style>
                            ← Vissza a bejelentkezéshez
                        </Hyperlink>
                    </TextBlock>

                    <!-- Admin értesítés gomb -->
                    <Button x:Name="NotifyAdminButton" 
                          Height="50" 
                          FontSize="16" 
                          FontWeight="SemiBold"
                          Foreground="White"
                          Margin="0,0,0,20"
                          Click="NotifyAdminButton_Click"
                          Cursor="Hand">
                        <Button.Template>
                            <ControlTemplate TargetType="Button">
                                <Border Name="border" 
                                      CornerRadius="12" 
                                      BorderThickness="0">
                                    <Border.Background>
                                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                            <GradientStop Color="#F59E0B" Offset="0"/>
                                            <GradientStop Color="#D97706" Offset="1"/>
                                        </LinearGradientBrush>
                                    </Border.Background>
                                    <ContentPresenter HorizontalAlignment="Center" 
                                                    VerticalAlignment="Center"/>
                                </Border>
                                <ControlTemplate.Triggers>
                                    <Trigger Property="IsMouseOver" Value="True">
                                        <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                                    </Trigger>
                                    <Trigger Property="IsPressed" Value="True">
                                        <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                                    </Trigger>
                                    <Trigger Property="IsEnabled" Value="False">
                                        <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                                    </Trigger>
                                </ControlTemplate.Triggers>
                            </ControlTemplate>
                        </Button.Template>
                        Adminisztrátor értesítése
                    </Button>

                    <!-- Sikerüzenet -->
                    <Border x:Name="SuccessPanel" 
                          CornerRadius="10" 
                          Background="#C6F6D5" 
                          BorderBrush="#68D391" 
                          BorderThickness="1" 
                          Padding="15"
                          Visibility="Collapsed">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock Text="✓" 
                                     Foreground="#38A169" 
                                     FontSize="16" 
                                     FontWeight="Bold"
                                     Margin="0,0,10,0"/>
                            <TextBlock x:Name="SuccessMessage" 
                                     Foreground="#38A169" 
                                     FontSize="14"
                                     FontWeight="Medium"
                                     TextWrapping="Wrap"/>
                        </StackPanel>
                    </Border>
                </StackPanel>

            </StackPanel>
        </Border>
    </Grid>
</dx:ThemedWindow>