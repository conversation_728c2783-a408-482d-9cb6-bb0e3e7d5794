﻿<dx:ThemedWindow x:Class="omsnext.wpf.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
        Title="OmsNext - Bejelentkezés" 
        Height="350" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <dxlc:LayoutControl Orientation="Vertical" Padding="20">
        <TextBlock Text="OmsNext" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,30"/>
        <dxe:TextEdit x:Name="EmailTextEdit" NullText="E-mail cím" Margin="0,0,0,15"/>
        <dxe:PasswordBoxEdit x:Name="PasswordBoxEdit" NullText="Jelszó" Margin="0,0,0,20"/>
        <dx:SimpleButton x:Name="LoginButton" Content="Bejelentkezés" Height="40" FontSize="16" FontWeight="SemiBold" Click="LoginButton_Click"/>
        <TextBlock x:Name="ErrorMessage" Foreground="Red" HorizontalAlignment="Center" Margin="0,15,0,0" TextWrapping="Wrap" Visibility="Collapsed"/>
        <dx:WaitIndicator x:Name="LoadingIndicator" Content="Bejelentkezés..." DeferedVisibility="True" Margin="0,10,0,0"/>
    </dxlc:LayoutControl>
</dx:ThemedWindow>