import type { Metadata } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mono, Inter } from "next/font/google";
import "./globals.css";
//import "devextreme/dist/css/dx.material.blue.light.compact.css";
import "devextreme/dist/css/dx.material.blue.light.compact.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "OMSNext - Új rendszer",
  description: "OMSNext alkalmazás DevExtreme UI-val",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="hu">
      <body
        className={`${geistSans.variable} ${geistMono.variable} ${inter.variable} antialiased dx-viewport`}
      >
        {children}
      </body>
    </html>
  );
}
