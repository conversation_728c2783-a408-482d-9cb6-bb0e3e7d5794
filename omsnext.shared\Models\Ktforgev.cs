﻿using DevExpress.Xpo;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace omsnext.core.Models
{
    [Persistent("omsnext.ktforgev")]
    public class Ktforgev : XPLiteObject
    {
        [Key(AutoGenerate = true)]
        public long id { get; set; }

        public string hiv_szam { get; set; }
        public string tk { get; set; }
        public string fkv_1 { get; set; }
        public string dev_nem { get; set; }
        public decimal? dev_ert { get; set; }
        public string fkv_2 { get; set; }
        public string fkv_3 { get; set; }
        public string mnk_szam { get; set; }
        public string fda_t { get; set; }
        public string rog_zito { get; set; }
        public string rog_dat { get; set; }
        public string meg_nev { get; set; }
        public int? rel_azon { get; set; }
        public string nap_lo { get; set; }
        public short? fej { get; set; }               // <-- smallint helyett bool NEM JÓ
        public decimal ert_ek { get; set; }
        public string biz_dat { get; set; }
        public string par_kod { get; set; }
        public string dol_kod { get; set; }
        public string cos_tcenter { get; set; }
        public int? rel_azon2 { get; set; }
        public string ber_kat { get; set; }
        public decimal? men_nyi { get; set; }
        public string pro_jekt { get; set; }
        public string afa_kod { get; set; }
        public DateTime? created_at { get; set; }
        public DateTime? updated_at { get; set; }

        public Ktforgev(Session session) : base(session) { }
    }
}
