"use client";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Drawer from "devextreme-react/drawer";
import Toolbar, { Item } from "devextreme-react/toolbar";
import Button from "devextreme-react/button";
import { logout, getUserPayload } from "@/lib/auth-client";
import styles from "./AppLayout.module.css";
import { hasPermission } from "@/lib/permissions";

interface AppLayoutProps {
  children: React.ReactNode;
  title?: string;
}

const AppLayout = ({ children, title = "OMSNext" }: AppLayoutProps) => {
  const [drawerOpened, setDrawerOpened] = useState(false);
  const [user, setUser] = useState<{ email: string; roles: string[] } | null>(
    null
  );
  const router = useRouter();

  useEffect(() => {
    const fetchUser = async () => {
      try {
        const payload = await getUserPayload();
        if (payload) {
          setUser({
            email: payload.email,
            roles: payload.roles,
          });
        }
      } catch (error) {
        console.error("Failed to fetch user:", error);
      }
    };

    fetchUser();
  }, []);

  const handleLogout = async () => {
    await logout();
  };

  const menuItems = [
    {
      text: "Dashboard",
      icon: "home",
      path: "/dashboard",
      permission: "view_dashboard",
    },
    {
      text: "Felhasználók",
      icon: "user",
      path: "/users",
      permission: "view_users",
    },
    {
      text: "Beállítások",
      icon: "preferences",
      path: "/settings",
      permission: "view_settings",
    },
  ].filter((item) => {
    if (!user) return false;
    return hasPermission(user.roles as any, item.permission as any);
  });

  const handleMenuItemClick = (path: string) => {
    router.push(path);
    setDrawerOpened(false);
  };

  const toolbarItems = [
    {
      widget: "dxButton",
      location: "before" as const,
      options: {
        icon: "menu",
        stylingMode: "text",
        onClick: () => setDrawerOpened(!drawerOpened),
      },
    },
    {
      text: title,
      location: "center" as const,
    },
    user && {
      template: () => (
        <div
          style={{ display: "flex", alignItems: "center", padding: "0 12px" }}
        >
          <span style={{ marginRight: "12px", fontSize: "14px" }}>
            {user.email}
          </span>
        </div>
      ),
      location: "after" as const,
    },
    {
      widget: "dxButton",
      location: "after" as const,
      options: {
        icon: "runner",
        text: "Kijelentkezés",
        stylingMode: "text",
        onClick: handleLogout,
      },
    },
  ].filter(Boolean) as any[];

  const renderMenu = () => (
    <div className={styles.sideMenu}>
      <div className={styles.menuHeader}>
        <h3>OMSNext</h3>
      </div>
      <div className={styles.menuItems}>
        {menuItems.map((item, index) => (
          <div
            key={index}
            className={styles.menuItem}
            onClick={() => handleMenuItemClick(item.path)}
          >
            <i className={`dx-icon-${item.icon} ${styles.menuIcon}`}></i>
            <span>{item.text}</span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className={styles.appLayout}>
      <Drawer
        opened={drawerOpened}
        openedStateMode="overlap"
        position="left"
        revealMode="slide"
        render={renderMenu}
        closeOnOutsideClick={true}
        onOpenedChange={setDrawerOpened}
      >
        <div className={styles.content}>
          <Toolbar items={toolbarItems} className={styles.toolbar} />
          <div className={styles.pageContent}>{children}</div>
        </div>
      </Drawer>
    </div>
  );
};

export default AppLayout;
