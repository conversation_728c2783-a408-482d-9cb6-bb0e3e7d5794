﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using DevExpress.Data.Linq;
using DevExpress.Data.Linq.Helpers;
using omsnext.core.Models;
using omsnext.wpf.Services;
using System.Linq;

namespace omsnext.wpf
{
    public partial class KtforgevUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private bool _isLoading;
        private LinqServerModeSource _serverModeSource;

        public event PropertyChangedEventHandler? PropertyChanged;

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
                StatusLabel.Text = value ? "Betöltés..." : "Készen";
            }
        }

        public KtforgevUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            
            DataContext = this;
            InitializeServerMode();
            
            Loaded += KtforgevUserControl_Loaded;
        }

        private void InitializeServerMode()
        {
            // Create a custom data provider for DevExpress Server Mode
            _serverModeSource = new LinqServerModeSource();
            _serverModeSource.QueryableSource = new KtforgevQueryableProvider(_apiClient);
            _serverModeSource.ElementType = typeof(KtforgevDto);
            
            KtforgevGrid.ItemsSource = _serverModeSource;
        }

        private async void KtforgevUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await RefreshDataAsync();
        }

        private async Task RefreshDataAsync()
        {
            IsLoading = true;
            try
            {
                // Refresh the server mode source
                _serverModeSource.Refresh();
                
                // Update status
                StatusLabel.Text = "Adatok betöltve - Virtual scrolling aktív";
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async void ApplyFilterButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = FilterFieldComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null && !string.IsNullOrWhiteSpace(FilterValueTextBox.Text))
            {
                var filterField = selectedItem.Tag?.ToString();
                var filterValue = FilterValueTextBox.Text.Trim();
                
                // Apply column filter using DevExpress Grid
                var column = KtforgevGrid.Columns.FirstOrDefault(c => c.FieldName == filterField);
                if (column != null)
                {
                    column.FilterCriteria = new DevExpress.Data.Filtering.BinaryOperator(
                        filterField, 
                        filterValue, 
                        DevExpress.Data.Filtering.BinaryOperatorType.Like);
                }
            }
            else
            {
                DXMessageBox.Show("Kérjük válasszon szűrő mezőt és adjon meg értéket!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            FilterFieldComboBox.SelectedIndex = -1;
            FilterValueTextBox.Clear();
            
            // Clear all filters
            KtforgevGrid.ClearFilter();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await RefreshDataAsync();
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // Custom queryable provider for server mode
    public class KtforgevQueryableProvider : IQueryable<KtforgevDto>
    {
        private readonly ApiClient _apiClient;
        private readonly KtforgevQueryProvider _provider;

        public KtforgevQueryableProvider(ApiClient apiClient)
        {
            _apiClient = apiClient;
            _provider = new KtforgevQueryProvider(apiClient);
        }

        public Type ElementType => typeof(KtforgevDto);
        public System.Linq.Expressions.Expression Expression => System.Linq.Expressions.Expression.Constant(this);
        public IQueryProvider Provider => _provider;

        public IEnumerator<KtforgevDto> GetEnumerator()
        {
            return _provider.ExecuteQuery().GetEnumerator();
        }

        System.Collections.IEnumerator System.Collections.IEnumerable.GetEnumerator()
        {
            return GetEnumerator();
        }
    }

    public class KtforgevQueryProvider : IQueryProvider
    {
        private readonly ApiClient _apiClient;

        public KtforgevQueryProvider(ApiClient apiClient)
        {
            _apiClient = apiClient;
        }

        public IQueryable CreateQuery(System.Linq.Expressions.Expression expression)
        {
            return new KtforgevQueryableProvider(_apiClient);
        }

        public IQueryable<TElement> CreateQuery<TElement>(System.Linq.Expressions.Expression expression)
        {
            return (IQueryable<TElement>)CreateQuery(expression);
        }

        public object Execute(System.Linq.Expressions.Expression expression)
        {
            return ExecuteQuery();
        }

        public TResult Execute<TResult>(System.Linq.Expressions.Expression expression)
        {
            return (TResult)Execute(expression);
        }

        public List<KtforgevDto> ExecuteQuery()
        {
            try
            {
                // This will be called by DevExpress with server mode parameters
                var result = _apiClient.GetKtforgevDataAsync(skip: 0, take: 1000).Result;
                return result?.Data ?? new List<KtforgevDto>();
            }
            catch
            {
                return new List<KtforgevDto>();
            }
        }
    }
}