module.exports = {

"[project]/node_modules/devextreme/esm/viz/components/consts.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/components/consts.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {
    events: {
        mouseover: "mouseover",
        mouseout: "mouseout",
        mousemove: "mousemove",
        touchstart: "touchstart",
        touchmove: "touchmove",
        touchend: "touchend",
        mousedown: "mousedown",
        mouseup: "mouseup",
        click: "click",
        selectSeries: "selectseries",
        deselectSeries: "deselectseries",
        selectPoint: "selectpoint",
        deselectPoint: "deselectpoint",
        showPointTooltip: "showpointtooltip",
        hidePointTooltip: "hidepointtooltip"
    },
    states: {
        hover: "hover",
        normal: "normal",
        selection: "selection",
        normalMark: 0,
        hoverMark: 1,
        selectedMark: 2,
        applyHover: "applyHover",
        applySelected: "applySelected",
        resetItem: "resetItem"
    },
    radialLabelIndent: 30,
    pieLabelSpacing: 10,
    pieSeriesSpacing: 4
};
}),
"[project]/node_modules/devextreme/esm/viz/components/parse_utils.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/components/parse_utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "correctValueType": ()=>correctValueType,
    "getParser": ()=>getParser
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date_serialization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date_serialization.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
;
;
;
const parsers = {
    string: function(val) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(val) ? "" + val : val;
    },
    numeric: function(val) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(val)) {
            return val;
        }
        let parsedVal = Number(val);
        if (isNaN(parsedVal)) {
            parsedVal = void 0;
        }
        return parsedVal;
    },
    datetime: function(val) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(val)) {
            return val;
        }
        let parsedVal;
        const numVal = Number(val);
        if (!isNaN(numVal)) {
            parsedVal = new Date(numVal);
        } else {
            parsedVal = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date_serialization$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].deserializeDate(val);
        }
        if (isNaN(Number(parsedVal))) {
            parsedVal = void 0;
        }
        return parsedVal;
    }
};
function correctValueType(type) {
    return "numeric" === type || "datetime" === type || "string" === type ? type : "";
}
const getParser = function(valueType) {
    return parsers[correctValueType(valueType)] || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
};
}),
"[project]/node_modules/devextreme/esm/viz/components/chart_theme_manager.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/components/chart_theme_manager.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "ThemeManager": ()=>ThemeManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_theme_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/base_theme_manager.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
;
;
;
;
;
const ThemeManager = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_theme_manager$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BaseThemeManager"].inherit(function() {
    const processAxisOptions = function(axisOptions) {
        if (!axisOptions) {
            return {};
        }
        axisOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, axisOptions);
        axisOptions.title = (options = axisOptions.title, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(options) ? {
            text: options
        } : options);
        var options;
        if ("logarithmic" === axisOptions.type && axisOptions.logarithmBase <= 0 || axisOptions.logarithmBase && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(axisOptions.logarithmBase)) {
            axisOptions.logarithmBase = void 0;
            axisOptions.logarithmBaseError = true;
        }
        if (axisOptions.label) {
            if (axisOptions.label.alignment) {
                axisOptions.label.userAlignment = true;
            }
        }
        return axisOptions;
    };
    const applyParticularAxisOptions = function(name, userOptions, rotated) {
        const theme = this._theme;
        const position = !(rotated ^ "valueAxis" === name) ? "horizontalAxis" : "verticalAxis";
        const processedUserOptions = processAxisOptions(userOptions);
        const commonAxisSettings = processAxisOptions(this._userOptions.commonAxisSettings);
        const mergeOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, theme.commonAxisSettings, theme[position], theme[name], commonAxisSettings, processedUserOptions);
        mergeOptions.workWeek = processedUserOptions.workWeek || theme[name].workWeek;
        mergeOptions.forceUserTickInterval |= (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(processedUserOptions.tickInterval) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(processedUserOptions.axisDivisionFactor);
        return mergeOptions;
    };
    const mergeOptions = function(name, userOptions) {
        userOptions = userOptions || this._userOptions[name];
        const theme = this._theme[name];
        let result = this._mergedSettings[name];
        if (result) {
            return result;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPlainObject"])(theme) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPlainObject"])(userOptions)) {
            result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, theme, userOptions);
        } else {
            result = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(userOptions) ? userOptions : theme;
        }
        this._mergedSettings[name] = result;
        return result;
    };
    const applyParticularTheme = {
        base: mergeOptions,
        argumentAxis: applyParticularAxisOptions,
        valueAxisRangeSelector: function() {
            return mergeOptions.call(this, "valueAxis");
        },
        valueAxis: applyParticularAxisOptions,
        series: function(name, userOptions, seriesCount) {
            const that = this;
            const theme = that._theme;
            let userCommonSettings = that._userOptions.commonSeriesSettings || {};
            const themeCommonSettings = theme.commonSeriesSettings;
            const widgetType = that._themeSection.split(".").slice(-1)[0];
            const type = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(userOptions.type || userCommonSettings.type || themeCommonSettings.type || "pie" === widgetType && theme.type);
            const palette = that.palette;
            const isBar = ~type.indexOf("bar");
            const isLine = ~type.indexOf("line");
            const isArea = ~type.indexOf("area");
            const isBubble = "bubble" === type;
            let mainSeriesColor;
            const resolveLabelsOverlapping = that.getOptions("resolveLabelsOverlapping");
            const containerBackgroundColor = that.getOptions("containerBackgroundColor");
            const seriesTemplate = applyParticularTheme.seriesTemplate.call(this);
            let seriesVisibility;
            if (isBar || isBubble) {
                userOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {}, userCommonSettings, userCommonSettings[type], userOptions);
                seriesVisibility = userOptions.visible;
                userCommonSettings = {
                    type: {}
                };
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, userOptions, userOptions.point);
                userOptions.visible = seriesVisibility;
            }
            const settings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(true, {
                aggregation: {}
            }, themeCommonSettings, themeCommonSettings[type], userCommonSettings, userCommonSettings[type], userOptions);
            settings.aggregation.enabled = "chart" === widgetType && !!settings.aggregation.enabled;
            settings.type = type;
            settings.widgetType = widgetType;
            settings.containerBackgroundColor = containerBackgroundColor;
            if ("pie" !== widgetType) {
                mainSeriesColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extractColor"])(settings.color, true) || palette.getNextColor(seriesCount);
            } else {
                mainSeriesColor = function(argument, index, count) {
                    const cat = `${argument}-${index}`;
                    if (!that._multiPieColors[cat]) {
                        that._multiPieColors[cat] = palette.getNextColor(count);
                    }
                    return that._multiPieColors[cat];
                };
            }
            settings.mainSeriesColor = mainSeriesColor;
            settings.resolveLabelsOverlapping = resolveLabelsOverlapping;
            if (settings.label && (isLine || isArea && "rangearea" !== type || "scatter" === type)) {
                settings.label.position = "outside";
            }
            if (seriesTemplate) {
                settings.nameField = seriesTemplate.nameField;
            }
            return settings;
        },
        animation: function(name) {
            let userOptions = this._userOptions[name];
            userOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isPlainObject"])(userOptions) ? userOptions : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(userOptions) ? {
                enabled: !!userOptions
            } : {};
            return mergeOptions.call(this, name, userOptions);
        },
        seriesTemplate () {
            const value = mergeOptions.call(this, "seriesTemplate");
            if (value) {
                value.nameField = value.nameField || "series";
            }
            return value;
        },
        zoomAndPan () {
            function parseOption(option) {
                option = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(option);
                const pan = "pan" === option || "both" === option;
                const zoom = "zoom" === option || "both" === option;
                return {
                    pan: pan,
                    zoom: zoom,
                    none: !pan && !zoom
                };
            }
            const options = mergeOptions.call(this, "zoomAndPan");
            return {
                valueAxis: parseOption(options.valueAxis),
                argumentAxis: parseOption(options.argumentAxis),
                dragToZoom: !!options.dragToZoom,
                dragBoxStyle: {
                    class: "dxc-shutter",
                    fill: options.dragBoxStyle.color,
                    opacity: options.dragBoxStyle.opacity
                },
                panKey: options.panKey,
                allowMouseWheel: !!options.allowMouseWheel,
                allowTouchGestures: !!options.allowTouchGestures
            };
        }
    };
    return {
        _themeSection: "chart",
        ctor: function(params) {
            this.callBase.apply(this, arguments);
            const options = params.options || {};
            this._userOptions = options;
            this._mergeAxisTitleOptions = [];
            this._multiPieColors = {};
            this._callback = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
        },
        dispose: function() {
            this.palette && this.palette.dispose();
            this.palette = this._userOptions = this._mergedSettings = this._multiPieColors = null;
            return this.callBase.apply(this, arguments);
        },
        resetPalette: function() {
            this.palette.reset();
            this._multiPieColors = {};
        },
        getOptions: function(name) {
            return (applyParticularTheme[name] || applyParticularTheme.base).apply(this, arguments);
        },
        refresh: function() {
            this._mergedSettings = {};
            return this.callBase.apply(this, arguments);
        },
        _initializeTheme: function() {
            this.callBase.apply(this, arguments);
            this.updatePalette();
        },
        resetOptions: function(name) {
            this._mergedSettings[name] = null;
        },
        update: function(options) {
            this._userOptions = options;
        },
        updatePalette: function() {
            this.palette = this.createPalette(this.getOptions("palette"), {
                useHighlight: true,
                extensionMode: this.getOptions("paletteExtensionMode")
            });
        }
    };
}());
}),
"[project]/node_modules/devextreme/esm/viz/components/data_validator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/components/data_validator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "validateData": ()=>validateData
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$parse_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/parse_utils.js [app-ssr] (ecmascript)");
;
const STRING = "string";
const NUMERIC = "numeric";
const DATETIME = "datetime";
const DISCRETE = "discrete";
const SEMIDISCRETE = "semidiscrete";
const CONTINUOUS = "continuous";
const LOGARITHMIC = "logarithmic";
const VALUE_TYPE = "valueType";
const ARGUMENT_TYPE = "argumentType";
;
;
const axisTypeParser = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enumParser"])([
    STRING,
    NUMERIC,
    DATETIME
]);
;
const _isArray = Array.isArray;
function groupingValues(data, others, valueField, index) {
    if (index >= 0) {
        data.slice(index).forEach(function(cell) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(cell[valueField])) {
                others[valueField] += cell[valueField];
                cell[valueField] = void 0;
            }
        });
    }
}
function processGroups(groups) {
    groups.forEach(function(group) {
        group.valueType = group.valueAxisType = null;
        group.series.forEach(function(series) {
            series.updateDataType({});
        });
        group.valueAxis && group.valueAxis.resetTypes(VALUE_TYPE);
    });
}
function sortValues(data, asc, selector) {
    const func = asc ? function(a, b) {
        return a - b;
    } : function(a, b) {
        return b - a;
    };
    data.sort(function(a, b) {
        const valA = selector(a);
        const valB = selector(b);
        const aa = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(valA) ? 1 : 0;
        const bb = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(valB) ? 1 : 0;
        return aa && bb ? func(valA, valB) : func(aa, bb);
    });
    return data;
}
function resetArgumentAxes(axes) {
    axes && axes.forEach(function(axis) {
        axis.resetTypes(ARGUMENT_TYPE);
    });
}
function parseCategories(categories, parser) {
    const newArray = [];
    categories.forEach(function(category) {
        const parsedCategory = parser(category);
        void 0 !== parsedCategory && newArray.push(parsedCategory);
    });
    return newArray;
}
function parseAxisCategories(groupsData, parsers) {
    const argumentCategories = groupsData.argumentOptions && groupsData.argumentOptions.categories;
    groupsData.groups.forEach(function(valueGroup, i) {
        const categories = valueGroup.valueOptions && valueGroup.valueOptions.categories;
        if (categories) {
            valueGroup.valueOptions.categories = parseCategories(categories, parsers[i + 1]);
        }
    });
    if (argumentCategories) {
        groupsData.argumentOptions.categories = parseCategories(argumentCategories, parsers[0]);
    }
}
function eigen(x) {
    return x;
}
function getType(unit, type) {
    let result = type;
    if (type === STRING || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(unit)) {
        result = STRING;
    } else if (type === DATETIME || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(unit)) {
        result = DATETIME;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(unit)) {
        result = NUMERIC;
    }
    return result;
}
function correctAxisType(type, axisType, hasCategories, incidentOccurred) {
    if (type === STRING && (axisType === CONTINUOUS || axisType === LOGARITHMIC || axisType === SEMIDISCRETE)) {
        incidentOccurred("E2002");
    }
    return axisType === LOGARITHMIC ? LOGARITHMIC : hasCategories || axisType === DISCRETE || type === STRING ? DISCRETE : axisType === SEMIDISCRETE ? SEMIDISCRETE : CONTINUOUS;
}
function validUnit(unit, field, incidentOccurred) {
    if (unit) {
        incidentOccurred(!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(unit) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(unit) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(unit) ? "E2003" : "E2004", [
            field
        ]);
    }
}
function createParserUnit(type, axisType, incidentOccurred) {
    const parser = type ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$parse_utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getParser"])(type) : eigen;
    const filterInfinity = axisType !== DISCRETE ? function(x) {
        return isFinite(x) || void 0 === x ? x : null;
    } : eigen;
    return function(unit, field) {
        const parseUnit = filterInfinity(parser(unit));
        if (void 0 === parseUnit) {
            validUnit(unit, field, incidentOccurred);
        }
        return parseUnit;
    };
}
function prepareParsers(groupsData, incidentOccurred) {
    const argumentParser = createParserUnit(groupsData.argumentType, groupsData.argumentAxisType, incidentOccurred);
    let sizeParser;
    let valueParser;
    const categoryParsers = [
        argumentParser
    ];
    const cache = {};
    const list = [];
    groupsData.groups.forEach(function(group, groupIndex) {
        group.series.forEach(function(series) {
            valueParser = createParserUnit(group.valueType, group.valueAxisType, incidentOccurred);
            sizeParser = createParserUnit(NUMERIC, CONTINUOUS, incidentOccurred);
            cache[series.getArgumentField()] = argumentParser;
            series.getValueFields().forEach(function(field) {
                categoryParsers[groupIndex + 1] = valueParser;
                cache[field] = valueParser;
            });
            if (series.getSizeField()) {
                cache[series.getSizeField()] = sizeParser;
            }
        });
    });
    for(const field in cache){
        list.push([
            field,
            cache[field]
        ]);
    }
    list.length && parseAxisCategories(groupsData, categoryParsers);
    return list;
}
function getParsedCell(cell, parsers) {
    let i;
    const ii = parsers.length;
    const obj = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, cell);
    let field;
    let value;
    for(i = 0; i < ii; ++i){
        field = parsers[i][0];
        value = cell[field];
        obj[field] = parsers[i][1](value, field);
    }
    return obj;
}
function parse(data, parsers) {
    const parsedData = [];
    let i;
    const ii = data.length;
    parsedData.length = ii;
    for(i = 0; i < ii; ++i){
        parsedData[i] = getParsedCell(data[i], parsers);
    }
    return parsedData;
}
function findIndexByThreshold(data, valueField, threshold) {
    let i;
    const ii = data.length;
    let value;
    for(i = 0; i < ii; ++i){
        value = data[i][valueField];
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value) && threshold > value) {
            break;
        }
    }
    return i;
}
function groupMinSlices(originalData, argumentField, valueField, smallValuesGrouping) {
    smallValuesGrouping = smallValuesGrouping || {};
    const mode = smallValuesGrouping.mode;
    const others = {};
    if (!mode || "none" === mode) {
        return;
    }
    others[argumentField] = String(smallValuesGrouping.groupName || "others");
    others[valueField] = 0;
    const data = sortValues(originalData.slice(), false, function(a) {
        return a[valueField];
    });
    groupingValues(data, others, valueField, "smallValueThreshold" === mode ? findIndexByThreshold(data, valueField, smallValuesGrouping.threshold) : smallValuesGrouping.topCount);
    others[valueField] && originalData.push(others);
}
function groupPieData(data, groupsData) {
    const firstSeries = groupsData.groups[0] && groupsData.groups[0].series[0];
    const isPie = firstSeries && ("pie" === firstSeries.type || "doughnut" === firstSeries.type || "donut" === firstSeries.type);
    if (!isPie) {
        return;
    }
    groupsData.groups.forEach(function(group) {
        group.series.forEach(function(series) {
            groupMinSlices(data, series.getArgumentField(), series.getValueFields()[0], series.getOptions().smallValuesGrouping);
        });
    });
}
function addUniqueItemToCollection(item, collection, itemsHash) {
    if (!itemsHash[item]) {
        collection.push(item);
        itemsHash[item] = true;
    }
}
function getUniqueArgumentFields(groupsData) {
    const uniqueArgumentFields = [];
    const hash = {};
    groupsData.groups.forEach(function(group) {
        group.series.forEach(function(series) {
            addUniqueItemToCollection(series.getArgumentField(), uniqueArgumentFields, hash);
        });
    });
    return uniqueArgumentFields;
}
function sort(a, b) {
    const result = a - b;
    if (isNaN(result)) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(a)) {
            return 1;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(b)) {
            return -1;
        }
        return 0;
    }
    return result;
}
function sortByArgument(data, argumentField) {
    return data.slice().sort(function(a, b) {
        return sort(a[argumentField], b[argumentField]);
    });
}
function sortByCallback(data, callback) {
    return data.slice().sort(callback);
}
function checkValueTypeOfGroup(group, cell) {
    group.series.forEach(function(series) {
        series.getValueFields().forEach(function(field) {
            group.valueType = getType(cell[field], group.valueType);
        });
    });
    return group.valueType;
}
function getSortByCategories(categories) {
    const hash = {};
    categories.forEach(function(value, i) {
        hash[value] = i;
    });
    return function(data, argumentField) {
        return sortValues(data.slice(), true, function(a) {
            return hash[a[argumentField]];
        });
    };
}
function sortData(data, groupsData, options, uniqueArgumentFields) {
    const dataByArguments = {};
    const isDiscrete = groupsData.argumentAxisType === DISCRETE;
    const userCategories = isDiscrete && groupsData.argumentOptions && groupsData.argumentOptions.categories;
    let sortFunction = function(data) {
        return data;
    };
    const sortingMethodOption = options.sortingMethod;
    let reSortCategories;
    if (!userCategories && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(sortingMethodOption)) {
        data = sortByCallback(data, sortingMethodOption);
    }
    if (isDiscrete) {
        groupsData.categories = getCategories(data, uniqueArgumentFields, userCategories);
    }
    if (userCategories || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(sortingMethodOption) && groupsData.argumentType === STRING && !options._skipArgumentSorting) {
        sortFunction = getSortByCategories(groupsData.categories);
    } else if (true === sortingMethodOption && groupsData.argumentType !== STRING) {
        sortFunction = sortByArgument;
        reSortCategories = isDiscrete;
    }
    uniqueArgumentFields.forEach(function(field) {
        dataByArguments[field] = sortFunction(data, field);
    });
    if (reSortCategories) {
        groupsData.categories = groupsData.categories.sort(sort);
    }
    return dataByArguments;
}
function getCategories(data, uniqueArgumentFields, userCategories) {
    const categories = userCategories ? userCategories.slice() : [];
    const existingValues = new Set(categories.map((item)=>item.valueOf()));
    uniqueArgumentFields.forEach(function(field) {
        data.forEach(function(item) {
            const dataItem = item[field];
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(dataItem)) {
                return;
            }
            const dataItemValue = dataItem.valueOf();
            if (!existingValues.has(dataItemValue)) {
                categories.push(dataItem);
                existingValues.add(dataItemValue);
            }
        });
    });
    return categories;
}
function checkArgumentTypeOfGroup(series, cell, groupsData) {
    series.forEach(function(currentSeries) {
        groupsData.argumentType = getType(cell[currentSeries.getArgumentField()], groupsData.argumentType);
    });
    return groupsData.argumentType;
}
function checkType(data, groupsData, checkTypeForAllData) {
    const groupsWithUndefinedValueType = [];
    const groupsWithUndefinedArgumentType = [];
    const argumentTypeGroup = groupsData.argumentOptions && axisTypeParser(groupsData.argumentOptions.argumentType);
    let groupsIndexes;
    groupsData.groups.forEach(function(group) {
        if (!group.series.length) {
            return;
        }
        const valueTypeGroup = group.valueOptions && axisTypeParser(group.valueOptions.valueType);
        group.valueType = valueTypeGroup;
        groupsData.argumentType = argumentTypeGroup;
        !valueTypeGroup && groupsWithUndefinedValueType.push(group);
        !argumentTypeGroup && groupsWithUndefinedArgumentType.push(group);
    });
    if (groupsWithUndefinedValueType.length || groupsWithUndefinedArgumentType.length) {
        groupsIndexes = groupsWithUndefinedValueType.map(function(_, index) {
            return index;
        });
        data.some(function(cell) {
            let defineArg;
            groupsWithUndefinedValueType.forEach(function(group, groupIndex) {
                if (checkValueTypeOfGroup(group, cell) && groupsIndexes.indexOf(groupIndex) >= 0) {
                    groupsIndexes.splice(groupIndex, 1);
                }
            });
            if (!defineArg) {
                groupsWithUndefinedArgumentType.forEach(function(group) {
                    defineArg = checkArgumentTypeOfGroup(group.series, cell, groupsData);
                });
            }
            if (!checkTypeForAllData && defineArg && 0 === groupsIndexes.length) {
                return true;
            }
        });
    }
}
function checkAxisType(groupsData, incidentOccurred) {
    const argumentOptions = groupsData.argumentOptions || {};
    const userArgumentCategories = argumentOptions && argumentOptions.categories || [];
    const argumentAxisType = correctAxisType(groupsData.argumentType, argumentOptions.type, !!userArgumentCategories.length, incidentOccurred);
    groupsData.groups.forEach(function(group) {
        const valueOptions = group.valueOptions || {};
        const valueCategories = valueOptions.categories || [];
        const valueAxisType = correctAxisType(group.valueType, valueOptions.type, !!valueCategories.length, incidentOccurred);
        group.series.forEach(function(series) {
            const optionsSeries = {};
            optionsSeries.argumentAxisType = argumentAxisType;
            optionsSeries.valueAxisType = valueAxisType;
            groupsData.argumentAxisType = groupsData.argumentAxisType || optionsSeries.argumentAxisType;
            group.valueAxisType = group.valueAxisType || optionsSeries.valueAxisType;
            optionsSeries.argumentType = groupsData.argumentType;
            optionsSeries.valueType = group.valueType;
            optionsSeries.showZero = valueOptions.showZero;
            series.updateDataType(optionsSeries);
        });
        group.valueAxisType = group.valueAxisType || valueAxisType;
        if (group.valueAxis) {
            group.valueAxis.setTypes(group.valueAxisType, group.valueType, VALUE_TYPE);
            group.valueAxis.validate();
        }
    });
    groupsData.argumentAxisType = groupsData.argumentAxisType || argumentAxisType;
    if (groupsData.argumentAxes) {
        groupsData.argumentAxes.forEach(function(axis) {
            axis.setTypes(groupsData.argumentAxisType, groupsData.argumentType, ARGUMENT_TYPE);
            axis.validate();
        });
    }
}
function verifyData(source, incidentOccurred) {
    const data = [];
    const sourceIsDefined = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(source);
    let hasError = sourceIsDefined && !_isArray(source);
    let i;
    let ii;
    let k;
    let item;
    if (sourceIsDefined && !hasError) {
        for(i = 0, ii = source.length, k = 0; i < ii; ++i){
            item = source[i];
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(item)) {
                data[k++] = item;
            } else if (item) {
                hasError = true;
            }
        }
    }
    if (hasError) {
        incidentOccurred("E2001");
    }
    return data;
}
function validateData(data, groupsData, incidentOccurred, options) {
    data = verifyData(data, incidentOccurred);
    groupsData.argumentType = groupsData.argumentAxisType = null;
    processGroups(groupsData.groups);
    resetArgumentAxes(groupsData.argumentAxes);
    checkType(data, groupsData, options.checkTypeForAllData);
    checkAxisType(groupsData, incidentOccurred);
    if (options.convertToAxisDataType) {
        data = parse(data, prepareParsers(groupsData, incidentOccurred));
    }
    groupPieData(data, groupsData);
    const dataByArgumentFields = sortData(data, groupsData, options, getUniqueArgumentFields(groupsData));
    return dataByArgumentFields;
}
}),
"[project]/node_modules/devextreme/esm/viz/components/legend.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/components/legend.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Legend": ()=>Legend,
    "plugin": ()=>plugin
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/layout_element.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$title$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/title.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/object.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
const _Number = Number;
const _math = Math;
const _round = _math.round;
const _max = _math.max;
const _min = _math.min;
const _ceil = _math.ceil;
const _isDefined = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"];
const _isFunction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"];
const _enumParser = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enumParser"];
const _normalizeEnum = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"];
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"];
const DEFAULT_MARGIN = 10;
const DEFAULT_MARKER_HATCHING_WIDTH = 2;
const DEFAULT_MARKER_HATCHING_STEP = 5;
const CENTER = "center";
const RIGHT = "right";
const LEFT = "left";
const TOP = "top";
const BOTTOM = "bottom";
const HORIZONTAL = "horizontal";
const VERTICAL = "vertical";
const INSIDE = "inside";
const OUTSIDE = "outside";
const NONE = "none";
const HEIGHT = "height";
const WIDTH = "width";
const parseHorizontalAlignment = _enumParser([
    LEFT,
    CENTER,
    RIGHT
]);
const parseVerticalAlignment = _enumParser([
    TOP,
    BOTTOM
]);
const parseOrientation = _enumParser([
    VERTICAL,
    HORIZONTAL
]);
const parseItemTextPosition = _enumParser([
    LEFT,
    RIGHT,
    TOP,
    BOTTOM
]);
const parsePosition = _enumParser([
    OUTSIDE,
    INSIDE
]);
const parseItemsAlignment = _enumParser([
    LEFT,
    CENTER,
    RIGHT
]);
function getState(state, color, stateName) {
    if (!state) {
        return;
    }
    const colorFromAction = state.fill;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, {
        state: stateName,
        fill: colorFromAction === NONE ? color : colorFromAction,
        opacity: state.opacity,
        filter: state.filter,
        hatching: _extend({}, state.hatching, {
            step: 5,
            width: 2
        })
    });
}
function getAttributes(item, state, size) {
    const attrs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processHatchingAttrs"])(item, state);
    if (attrs.fill && 0 === attrs.fill.indexOf("DevExpress")) {
        attrs.fill = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getFuncIri"])(attrs.fill);
    }
    attrs.opacity = attrs.opacity >= 0 ? attrs.opacity : 1;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, attrs, {
        size: size
    });
}
function parseMargins(options) {
    let margin = options.margin;
    if (margin >= 0) {
        margin = _Number(options.margin);
        margin = {
            top: margin,
            bottom: margin,
            left: margin,
            right: margin
        };
    } else {
        margin = {
            top: margin.top >= 0 ? _Number(margin.top) : 10,
            bottom: margin.bottom >= 0 ? _Number(margin.bottom) : 10,
            left: margin.left >= 0 ? _Number(margin.left) : 10,
            right: margin.right >= 0 ? _Number(margin.right) : 10
        };
    }
    options.margin = margin;
}
function getSizeItem(options, markerBBox, labelBBox) {
    let width;
    let height;
    switch(options.itemTextPosition){
        case LEFT:
        case RIGHT:
            width = markerBBox.width + 7 + labelBBox.width;
            height = _max(markerBBox.height, labelBBox.height);
            break;
        case TOP:
        case BOTTOM:
            width = _max(markerBBox.width, labelBBox.width);
            height = markerBBox.height + 4 + labelBBox.height;
    }
    return {
        width: width,
        height: height
    };
}
function calculateBBoxLabelAndMarker(markerBBox, labelBBox) {
    const bBox = {};
    bBox.left = _min(markerBBox.x, labelBBox.x);
    bBox.top = _min(markerBBox.y, labelBBox.y);
    bBox.right = _max(markerBBox.x + markerBBox.width, labelBBox.x + labelBBox.width);
    bBox.bottom = _max(markerBBox.y + markerBBox.height, labelBBox.y + labelBBox.height);
    return bBox;
}
function applyMarkerState(id, idToIndexMap, items, stateName) {
    const item = idToIndexMap && items[idToIndexMap[id]];
    if (item) {
        item.renderMarker(item.states[stateName]);
    }
}
function parseOptions(options, textField, allowInsidePosition) {
    if (!options) {
        return null;
    }
    parseMargins(options);
    options.horizontalAlignment = parseHorizontalAlignment(options.horizontalAlignment, RIGHT);
    options.verticalAlignment = parseVerticalAlignment(options.verticalAlignment, options.horizontalAlignment === CENTER ? BOTTOM : TOP);
    options.orientation = parseOrientation(options.orientation, options.horizontalAlignment === CENTER ? HORIZONTAL : VERTICAL);
    options.itemTextPosition = parseItemTextPosition(options.itemTextPosition, options.orientation === HORIZONTAL ? BOTTOM : RIGHT);
    options.position = allowInsidePosition ? parsePosition(options.position, OUTSIDE) : OUTSIDE;
    options.itemsAlignment = parseItemsAlignment(options.itemsAlignment, null);
    options.hoverMode = _normalizeEnum(options.hoverMode);
    options.customizeText = _isFunction(options.customizeText) ? options.customizeText : function() {
        return this[textField];
    };
    options.customizeHint = _isFunction(options.customizeHint) ? options.customizeHint : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
    options._incidentOccurred = options._incidentOccurred || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
    return options;
}
function createSquareMarker(renderer, size) {
    return renderer.rect(0, 0, size, size);
}
function createCircleMarker(renderer, size) {
    return renderer.circle(size / 2, size / 2, size / 2);
}
function isCircle(type) {
    return "circle" === _normalizeEnum(type);
}
function inRect(rect, x, y) {
    return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom;
}
function checkLinesSize(lines, layoutOptions, countItems, margins) {
    const position = {
        x: 0,
        y: 0
    };
    let maxMeasureLength = 0;
    let maxAltMeasureLength = 0;
    let margin = 0;
    if ("y" === layoutOptions.direction) {
        margin = margins.top + margins.bottom;
    } else {
        margin = margins.left + margins.right;
    }
    lines.forEach(function(line, i) {
        const firstItem = line[0];
        const lineLength = line.length;
        line.forEach(function(item, index) {
            const offset = item.offset || layoutOptions.spacing;
            position[layoutOptions.direction] += item[layoutOptions.measure] + (index !== lineLength - 1 ? offset : 0);
            maxMeasureLength = _max(maxMeasureLength, position[layoutOptions.direction]);
        });
        position[layoutOptions.direction] = 0;
        position[layoutOptions.altDirection] += firstItem[layoutOptions.altMeasure] + firstItem.altOffset || layoutOptions.altSpacing;
        maxAltMeasureLength = _max(maxAltMeasureLength, position[layoutOptions.altDirection]);
    });
    if (maxMeasureLength + margin > layoutOptions.length) {
        layoutOptions.countItem = decreaseItemCount(layoutOptions, countItems);
        return true;
    }
}
function decreaseItemCount(layoutOptions, countItems) {
    layoutOptions.altCountItem++;
    return _ceil(countItems / layoutOptions.altCountItem);
}
function getLineLength(line, layoutOptions) {
    return line.reduce((lineLength, item)=>{
        const offset = item.offset || layoutOptions.spacing;
        return lineLength + item[layoutOptions.measure] + offset;
    }, 0);
}
function getMaxLineLength(lines, layoutOptions) {
    return lines.reduce((maxLineLength, line)=>_max(maxLineLength, getLineLength(line, layoutOptions)), 0);
}
function getInitPositionForDirection(line, layoutOptions, maxLineLength) {
    const lineLength = getLineLength(line, layoutOptions);
    let initPosition;
    switch(layoutOptions.itemsAlignment){
        case RIGHT:
            initPosition = maxLineLength - lineLength;
            break;
        case CENTER:
            initPosition = (maxLineLength - lineLength) / 2;
            break;
        default:
            initPosition = 0;
    }
    return initPosition;
}
function getPos(layoutOptions) {
    switch(layoutOptions.itemTextPosition){
        case BOTTOM:
            return {
                horizontal: CENTER,
                vertical: TOP
            };
        case TOP:
            return {
                horizontal: CENTER,
                vertical: BOTTOM
            };
        case LEFT:
            return {
                horizontal: RIGHT,
                vertical: CENTER
            };
        case RIGHT:
            return {
                horizontal: LEFT,
                vertical: CENTER
            };
    }
}
function getLines(lines, layoutOptions, itemIndex) {
    const tableLine = {};
    if (itemIndex % layoutOptions.countItem === 0) {
        if (layoutOptions.markerOffset) {
            lines.push([], []);
        } else {
            lines.push([]);
        }
    }
    if (layoutOptions.markerOffset) {
        tableLine.firstLine = lines[lines.length - 1];
        tableLine.secondLine = lines[lines.length - 2];
    } else {
        tableLine.firstLine = tableLine.secondLine = lines[lines.length - 1];
    }
    return tableLine;
}
function setMaxInLine(line, measure) {
    const maxLineSize = line.reduce((maxLineSize, item)=>{
        const itemMeasure = item ? item[measure] : maxLineSize;
        return _max(maxLineSize, itemMeasure);
    }, 0);
    line.forEach((item)=>{
        if (item) {
            item[measure] = maxLineSize;
        }
    });
}
function transpose(array) {
    const width = array.length;
    const height = array[0].length;
    let i;
    let j;
    const transposeArray = [];
    for(i = 0; i < height; i++){
        transposeArray[i] = [];
        for(j = 0; j < width; j++){
            transposeArray[i][j] = array[j][i];
        }
    }
    return transposeArray;
}
function getAlign(position) {
    switch(position){
        case TOP:
        case BOTTOM:
            return CENTER;
        case LEFT:
            return RIGHT;
        case RIGHT:
            return LEFT;
    }
}
let getMarkerCreator = function(type) {
    return isCircle(type) ? createCircleMarker : createSquareMarker;
};
function getTitleHorizontalAlignment(options) {
    if (options.horizontalAlignment === CENTER) {
        return CENTER;
    } else if (options.itemTextPosition === RIGHT) {
        return LEFT;
    } else if (options.itemTextPosition === LEFT) {
        return RIGHT;
    } else {
        return CENTER;
    }
}
let Legend = function(settings) {
    this._renderer = settings.renderer;
    this._legendGroup = settings.group;
    this._backgroundClass = settings.backgroundClass;
    this._itemGroupClass = settings.itemGroupClass;
    this._textField = settings.textField;
    this._getCustomizeObject = settings.getFormatObject;
    this._titleGroupClass = settings.titleGroupClass;
    this._allowInsidePosition = settings.allowInsidePosition;
    this._widget = settings.widget;
    this._updated = false;
};
const _Legend = Legend;
const legendPrototype = _Legend.prototype = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["clone"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["LayoutElement"].prototype);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(legendPrototype, {
    constructor: _Legend,
    getOptions: function() {
        return this._options;
    },
    update: function() {
        let data = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];
        let options = arguments.length > 1 ? arguments[1] : void 0;
        let themeManagerTitleOptions = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
        const that = this;
        options = that._options = parseOptions(options, that._textField, that._allowInsidePosition) || {};
        const initMarkerSize = options.markerSize;
        this._updated = true;
        this._data = data.map((dataItem)=>{
            dataItem.size = _Number(dataItem.size > 0 ? dataItem.size : initMarkerSize);
            dataItem.marker = getAttributes(dataItem, dataItem.states.normal);
            Object.defineProperty(dataItem.marker, "size", {
                get: ()=>dataItem.size,
                set (value) {
                    dataItem.size = value;
                }
            });
            Object.defineProperty(dataItem.marker, "opacity", {
                get: ()=>dataItem.states.normal.opacity,
                set (value) {
                    dataItem.states.normal.opacity = dataItem.states.hover.opacity = dataItem.states.selection.opacity = value;
                }
            });
            return dataItem;
        });
        if (options.customizeItems) {
            that._data = options.customizeItems(data.slice()) || data;
        }
        that._boundingRect = {
            width: 0,
            height: 0,
            x: 0,
            y: 0
        };
        if (that.isVisible()) {
            var _that$_title;
            null === (_that$_title = that._title) || void 0 === _that$_title || _that$_title.dispose();
            that._title = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$title$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Title"]({
                renderer: that._renderer,
                cssClass: that._titleGroupClass,
                root: that._legendGroup
            });
        }
        if (that._title) {
            const titleOptions = options.title;
            themeManagerTitleOptions.horizontalAlignment = getTitleHorizontalAlignment(options);
            that._title.update(themeManagerTitleOptions, titleOptions);
        }
        this.erase();
        return that;
    },
    isVisible: function() {
        return this._options && this._options.visible;
    },
    draw: function(width, height) {
        const that = this;
        const items = that._getItemData();
        that.erase();
        if (!(that.isVisible() && items && items.length)) {
            return that;
        }
        that._insideLegendGroup = that._renderer.g().enableLinks().append(that._legendGroup);
        that._title.changeLink(that._insideLegendGroup);
        that._createBackground();
        if (that._title.hasText()) {
            const horizontalPadding = that._background ? 2 * that._options.paddingLeftRight : 0;
            that._title.draw(width - horizontalPadding, height);
        }
        that._markersGroup = that._renderer.g().attr({
            class: that._itemGroupClass
        }).append(that._insideLegendGroup);
        that._createItems(items);
        that._updateElementsPosition(width, height);
        return that;
    },
    _measureElements: function() {
        const options = this._options;
        let maxBBoxHeight = 0;
        this._items.forEach((item)=>{
            const labelBBox = item.label.getBBox();
            const markerBBox = item.marker.getBBox();
            item.markerBBox = markerBBox;
            item.markerSize = Math.max(markerBBox.width, markerBBox.height);
            const bBox = getSizeItem(options, markerBBox, labelBBox);
            item.labelBBox = labelBBox;
            item.bBox = bBox;
            maxBBoxHeight = _max(maxBBoxHeight, bBox.height);
        });
        if (options.equalRowHeight) {
            this._items.forEach((item)=>item.bBox.height = maxBBoxHeight);
        }
    },
    _updateElementsPosition: function(width, height) {
        const that = this;
        const options = that._options;
        this._size = {
            width: width,
            height: height
        };
        that._measureElements();
        that._locateElements(options);
        that._finalUpdate(options);
        const size = that.getLayoutOptions();
        if (size.width > width || size.height > height) {
            that.freeSpace();
        }
    },
    _createItems: function(items) {
        const that = this;
        const options = that._options;
        const renderer = that._renderer;
        const createMarker = getMarkerCreator(options.markerShape);
        that._markersId = {};
        const templateFunction = !options.markerTemplate ? (dataItem, group)=>{
            const attrs = dataItem.marker;
            createMarker(renderer, attrs.size).attr({
                fill: attrs.fill,
                opacity: attrs.opacity,
                filter: attrs.filter
            }).append({
                element: group
            });
        } : options.markerTemplate;
        const template = that._widget._getTemplate(templateFunction);
        const markersGroup = that._markersGroup;
        markersGroup.css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.font));
        that._deferredItems = [];
        that._templatesGroups = [];
        that._items = (items || []).map((dataItem, i)=>{
            const stateOfDataItem = dataItem.states;
            const normalState = stateOfDataItem.normal;
            const normalStateFill = normalState.fill;
            dataItem.size = dataItem.marker.size;
            const states = {
                normal: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(normalState, {
                    fill: normalStateFill || options.markerColor || options.defaultColor,
                    state: "normal"
                }),
                hover: getState(stateOfDataItem.hover, normalStateFill, "hovered"),
                selection: getState(stateOfDataItem.selection, normalStateFill, "selected")
            };
            dataItem.states = states;
            const itemGroup = renderer.g().append(markersGroup);
            const markerGroup = renderer.g().attr({
                class: "dxl-marker"
            }).append(itemGroup);
            that._deferredItems[i] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Deferred"];
            that._templatesGroups.push(markerGroup);
            const item = {
                label: that._createLabel(dataItem, itemGroup),
                marker: markerGroup,
                renderer: renderer,
                group: itemGroup,
                tracker: {
                    id: dataItem.id,
                    argument: dataItem.argument,
                    argumentIndex: dataItem.argumentIndex
                },
                states: states,
                itemTextPosition: options.itemTextPosition,
                markerOffset: 0,
                bBoxes: [],
                renderMarker (state) {
                    dataItem.marker = getAttributes(item, state, dataItem.size);
                    markerGroup.clear();
                    template.render({
                        model: dataItem,
                        container: markerGroup.element,
                        onRendered: that._deferredItems[i].resolve
                    });
                }
            };
            item.renderMarker(states.normal);
            that._createHint(dataItem, itemGroup);
            if (void 0 !== dataItem.id) {
                that._markersId[dataItem.id] = i;
            }
            return item;
        });
    },
    getTemplatesGroups: function() {
        return this._templatesGroups || [];
    },
    getTemplatesDef: function() {
        return this._deferredItems || [];
    },
    _getItemData: function() {
        let items = this._data || [];
        const options = this._options || {};
        if (options.inverted) {
            items = items.slice().reverse();
        }
        return items.filter((i)=>i.visible);
    },
    _finalUpdate: function(options) {
        this._adjustBackgroundSettings(options);
        this._setBoundingRect(options.margin);
    },
    erase: function() {
        const insideLegendGroup = this._insideLegendGroup;
        insideLegendGroup && insideLegendGroup.dispose();
        this._insideLegendGroup = this._markersGroup = this._x1 = this._x2 = this._y2 = this._y2 = null;
        return this;
    },
    _locateElements: function(locationOptions) {
        this._moveInInitialValues();
        this._locateRowsColumns(locationOptions);
    },
    _moveInInitialValues: function() {
        this._title.hasText() && this._title.move([
            0,
            0
        ]);
        this._legendGroup && this._legendGroup.move(0, 0);
        this._background && this._background.attr({
            x: 0,
            y: 0,
            width: 0,
            height: 0
        });
    },
    applySelected: function(id) {
        applyMarkerState(id, this._markersId, this._items, "selection");
        return this;
    },
    applyHover: function(id) {
        applyMarkerState(id, this._markersId, this._items, "hover");
        return this;
    },
    resetItem: function(id) {
        applyMarkerState(id, this._markersId, this._items, "normal");
        return this;
    },
    _createLabel: function(data, group) {
        const labelFormatObject = this._getCustomizeObject(data);
        const options = this._options;
        const align = getAlign(options.itemTextPosition);
        const text = options.customizeText.call(labelFormatObject, labelFormatObject);
        const fontStyle = _isDefined(data.textOpacity) ? {
            color: options.font.color,
            opacity: data.textOpacity
        } : {};
        return this._renderer.text(text, 0, 0).css((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["patchFontOptions"])(fontStyle)).attr({
            align: align,
            class: options.cssClass
        }).append(group);
    },
    _createHint: function(data, group) {
        const labelFormatObject = this._getCustomizeObject(data);
        const text = this._options.customizeHint.call(labelFormatObject, labelFormatObject);
        if (_isDefined(text) && "" !== text) {
            group.setTitle(text);
        }
    },
    _createBackground: function() {
        const that = this;
        const isInside = that._options.position === INSIDE;
        const color = that._options.backgroundColor;
        const fill = color || (isInside ? that._options.containerBackgroundColor : NONE);
        if (that._options.border.visible || (isInside || color) && color !== NONE) {
            that._background = that._renderer.rect(0, 0, 0, 0).attr({
                fill: fill,
                class: that._backgroundClass
            }).append(that._insideLegendGroup);
        }
    },
    _locateRowsColumns: function(options) {
        const that = this;
        let iteration = 0;
        const layoutOptions = that._getItemsLayoutOptions();
        const countItems = that._items.length;
        let lines;
        do {
            lines = [];
            that._createLines(lines, layoutOptions);
            that._alignLines(lines, layoutOptions);
            iteration++;
        }while (checkLinesSize(lines, layoutOptions, countItems, options.margin) && iteration < countItems)
        that._applyItemPosition(lines, layoutOptions);
    },
    _createLines: function(lines, layoutOptions) {
        this._items.forEach((item, i)=>{
            const tableLine = getLines(lines, layoutOptions, i);
            const labelBox = {
                width: item.labelBBox.width,
                height: item.labelBBox.height,
                element: item.label,
                bBox: item.labelBBox,
                pos: getPos(layoutOptions),
                itemIndex: i
            };
            const markerBox = {
                width: item.markerBBox.width,
                height: item.markerBBox.height,
                element: item.marker,
                pos: {
                    horizontal: CENTER,
                    vertical: CENTER
                },
                bBox: {
                    width: item.markerBBox.width,
                    height: item.markerBBox.height,
                    x: item.markerBBox.x,
                    y: item.markerBBox.y
                },
                itemIndex: i
            };
            let firstItem;
            let secondItem;
            const offsetDirection = layoutOptions.markerOffset ? "altOffset" : "offset";
            if (layoutOptions.inverseLabelPosition) {
                firstItem = labelBox;
                secondItem = markerBox;
            } else {
                firstItem = markerBox;
                secondItem = labelBox;
            }
            firstItem[offsetDirection] = layoutOptions.labelOffset;
            tableLine.secondLine.push(firstItem);
            tableLine.firstLine.push(secondItem);
        });
    },
    _alignLines: function(lines, layoutOptions) {
        let i;
        let measure = layoutOptions.altMeasure;
        lines.forEach((line)=>setMaxInLine(line, measure));
        measure = layoutOptions.measure;
        if (layoutOptions.itemsAlignment) {
            if (layoutOptions.markerOffset) {
                for(i = 0; i < lines.length;){
                    transpose([
                        lines[i++],
                        lines[i++]
                    ]).forEach(processLine);
                }
            }
        } else {
            transpose(lines).forEach(processLine);
        }
        function processLine(line) {
            setMaxInLine(line, measure);
        }
    },
    _applyItemPosition: function(lines, layoutOptions) {
        const that = this;
        const position = {
            x: 0,
            y: 0
        };
        const maxLineLength = getMaxLineLength(lines, layoutOptions);
        lines.forEach((line)=>{
            const firstItem = line[0];
            const altOffset = firstItem.altOffset || layoutOptions.altSpacing;
            position[layoutOptions.direction] = getInitPositionForDirection(line, layoutOptions, maxLineLength);
            line.forEach((item)=>{
                const offset = item.offset || layoutOptions.spacing;
                const wrap = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WrapperLayoutElement"](item.element, item.bBox);
                const itemBBoxOptions = {
                    x: position.x,
                    y: position.y,
                    width: item.width,
                    height: item.height
                };
                const itemBBox = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout_element$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["WrapperLayoutElement"](null, itemBBoxOptions);
                const itemLegend = that._items[item.itemIndex];
                wrap.position({
                    of: itemBBox,
                    my: item.pos,
                    at: item.pos
                });
                itemLegend.bBoxes.push(itemBBox);
                position[layoutOptions.direction] += item[layoutOptions.measure] + offset;
            });
            position[layoutOptions.altDirection] += firstItem[layoutOptions.altMeasure] + altOffset;
        });
        this._items.forEach((item)=>{
            const itemBBox = calculateBBoxLabelAndMarker(item.bBoxes[0].getLayoutOptions(), item.bBoxes[1].getLayoutOptions());
            const horizontal = that._options.columnItemSpacing / 2;
            const vertical = that._options.rowItemSpacing / 2;
            item.tracker.left = itemBBox.left - horizontal;
            item.tracker.right = itemBBox.right + horizontal;
            item.tracker.top = itemBBox.top - vertical;
            item.tracker.bottom = itemBBox.bottom + vertical;
        });
    },
    _getItemsLayoutOptions: function() {
        const that = this;
        const options = that._options;
        const orientation = options.orientation;
        const layoutOptions = {
            itemsAlignment: options.itemsAlignment,
            orientation: options.orientation
        };
        const width = that._size.width - (that._background ? 2 * options.paddingLeftRight : 0);
        const height = that._size.height - (that._background ? 2 * options.paddingTopBottom : 0);
        if (orientation === HORIZONTAL) {
            layoutOptions.length = width;
            layoutOptions.spacing = options.columnItemSpacing;
            layoutOptions.direction = "x";
            layoutOptions.measure = WIDTH;
            layoutOptions.altMeasure = HEIGHT;
            layoutOptions.altDirection = "y";
            layoutOptions.altSpacing = options.rowItemSpacing;
            layoutOptions.countItem = options.columnCount;
            layoutOptions.altCountItem = options.rowCount;
            layoutOptions.marginTextLabel = 4;
            layoutOptions.labelOffset = 7;
            if (options.itemTextPosition === BOTTOM || options.itemTextPosition === TOP) {
                layoutOptions.labelOffset = 4;
                layoutOptions.markerOffset = true;
            }
        } else {
            layoutOptions.length = height;
            layoutOptions.spacing = options.rowItemSpacing;
            layoutOptions.direction = "y";
            layoutOptions.measure = HEIGHT;
            layoutOptions.altMeasure = WIDTH;
            layoutOptions.altDirection = "x";
            layoutOptions.altSpacing = options.columnItemSpacing;
            layoutOptions.countItem = options.rowCount;
            layoutOptions.altCountItem = options.columnCount;
            layoutOptions.marginTextLabel = 7;
            layoutOptions.labelOffset = 4;
            if (options.itemTextPosition === RIGHT || options.itemTextPosition === LEFT) {
                layoutOptions.labelOffset = 7;
                layoutOptions.markerOffset = true;
            }
        }
        if (!layoutOptions.countItem) {
            if (layoutOptions.altCountItem) {
                layoutOptions.countItem = _ceil(that._items.length / layoutOptions.altCountItem);
            } else {
                layoutOptions.countItem = that._items.length;
            }
        }
        if (options.itemTextPosition === TOP || options.itemTextPosition === LEFT) {
            layoutOptions.inverseLabelPosition = true;
        }
        layoutOptions.itemTextPosition = options.itemTextPosition;
        layoutOptions.altCountItem = layoutOptions.altCountItem || _ceil(that._items.length / layoutOptions.countItem);
        return layoutOptions;
    },
    _adjustBackgroundSettings: function(locationOptions) {
        if (!this._background) {
            return;
        }
        const border = locationOptions.border;
        const legendBox = this._calculateTotalBox();
        const backgroundSettings = {
            x: _round(legendBox.x - locationOptions.paddingLeftRight),
            y: _round(legendBox.y - locationOptions.paddingTopBottom),
            width: _round(legendBox.width) + 2 * locationOptions.paddingLeftRight,
            height: _round(legendBox.height),
            opacity: locationOptions.backgroundOpacity
        };
        if (border.visible && border.width && border.color && border.color !== NONE) {
            backgroundSettings["stroke-width"] = border.width;
            backgroundSettings.stroke = border.color;
            backgroundSettings["stroke-opacity"] = border.opacity;
            backgroundSettings.dashStyle = border.dashStyle;
            backgroundSettings.rx = border.cornerRadius || 0;
            backgroundSettings.ry = border.cornerRadius || 0;
        }
        this._background.attr(backgroundSettings);
    },
    _setBoundingRect: function(margin) {
        if (!this._insideLegendGroup) {
            return;
        }
        const box = this._calculateTotalBox();
        box.height += margin.top + margin.bottom;
        box.widthWithoutMargins = box.width;
        box.width += margin.left + margin.right;
        box.x -= margin.left;
        box.y -= margin.top;
        this._boundingRect = box;
    },
    _calculateTotalBox: function() {
        const markerBox = this._markersGroup.getBBox();
        const titleBox = this._title.getCorrectedLayoutOptions();
        const box = this._insideLegendGroup.getBBox();
        const verticalPadding = this._background ? 2 * this._options.paddingTopBottom : 0;
        box.height = markerBox.height + titleBox.height + verticalPadding;
        titleBox.width > box.width && (box.width = titleBox.width);
        return box;
    },
    getActionCallback: function(point) {
        const that = this;
        if (that._options.visible) {
            return function(act) {
                that[act](point.index);
            };
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["noop"];
        }
    },
    getLayoutOptions: function() {
        const options = this._options;
        const boundingRect = this._insideLegendGroup ? this._boundingRect : {
            width: 0,
            height: 0,
            x: 0,
            y: 0
        };
        if (options) {
            boundingRect.verticalAlignment = options.verticalAlignment;
            boundingRect.horizontalAlignment = options.horizontalAlignment;
            if (options.orientation === HORIZONTAL) {
                boundingRect.cutLayoutSide = options.verticalAlignment;
                boundingRect.cutSide = "vertical";
            } else if (options.horizontalAlignment === CENTER) {
                boundingRect.cutLayoutSide = options.verticalAlignment;
                boundingRect.cutSide = "vertical";
            } else {
                boundingRect.cutLayoutSide = options.horizontalAlignment;
                boundingRect.cutSide = "horizontal";
            }
            boundingRect.position = {
                horizontal: options.horizontalAlignment,
                vertical: options.verticalAlignment
            };
            return boundingRect;
        }
        return null;
    },
    shift: function(x, y) {
        const that = this;
        let box = {};
        if (that._insideLegendGroup) {
            that._insideLegendGroup.attr({
                translateX: x - that._boundingRect.x,
                translateY: y - that._boundingRect.y
            });
        }
        that._title && that._shiftTitle(that._boundingRect.widthWithoutMargins);
        that._markersGroup && that._shiftMarkers();
        if (that._insideLegendGroup) {
            box = that._legendGroup.getBBox();
        }
        that._x1 = box.x;
        that._y1 = box.y;
        that._x2 = box.x + box.width;
        that._y2 = box.y + box.height;
        return that;
    },
    _shiftTitle: function(boxWidth) {
        const that = this;
        const title = that._title;
        const titleBox = title.getCorrectedLayoutOptions();
        if (!titleBox || !title.hasText()) {
            return;
        }
        const width = boxWidth - (that._background ? 2 * that._options.paddingLeftRight : 0);
        const titleOptions = title.getOptions();
        let titleY = titleBox.y + titleOptions.margin.top;
        let titleX = 0;
        if (titleOptions.verticalAlignment === BOTTOM && that._markersGroup) {
            titleY += that._markersGroup.getBBox().height;
        }
        if (titleOptions.horizontalAlignment === RIGHT) {
            titleX = width - titleBox.width;
        } else if (titleOptions.horizontalAlignment === CENTER) {
            titleX = (width - titleBox.width) / 2;
        }
        title.shift(titleX, titleY);
    },
    _shiftMarkers: function() {
        const titleBox = this._title.getLayoutOptions();
        const markerBox = this._markersGroup.getBBox();
        const titleOptions = this._title.getOptions() || {};
        let center = 0;
        let y = 0;
        if (titleBox.width > markerBox.width && this._options.horizontalAlignment === CENTER) {
            center = titleBox.width / 2 - markerBox.width / 2;
        }
        if (titleOptions.verticalAlignment === TOP) {
            y = titleBox.height;
        }
        if (0 !== center || 0 !== y) {
            this._markersGroup.attr({
                translateX: center,
                translateY: y
            });
            this._items.forEach((item)=>{
                item.tracker.left += center;
                item.tracker.right += center;
                item.tracker.top += y;
                item.tracker.bottom += y;
            });
        }
    },
    getPosition: function() {
        return this._options.position;
    },
    coordsIn: function(x, y) {
        return x >= this._x1 && x <= this._x2 && y >= this._y1 && y <= this._y2;
    },
    getItemByCoord: function(x, y) {
        const items = this._items;
        const legendGroup = this._insideLegendGroup;
        x -= legendGroup.attr("translateX");
        y -= legendGroup.attr("translateY");
        for(let i = 0; i < items.length; i++){
            if (inRect(items[i].tracker, x, y)) {
                return items[i].tracker;
            }
        }
        return null;
    },
    dispose: function() {
        this._title && this._title.dispose();
        this._legendGroup = this._insideLegendGroup = this._title = this._renderer = this._options = this._data = this._items = null;
        return this;
    },
    layoutOptions: function() {
        if (!this.isVisible()) {
            return null;
        }
        const pos = this.getLayoutOptions();
        return {
            horizontalAlignment: this._options.horizontalAlignment,
            verticalAlignment: this._options.verticalAlignment,
            side: pos.cutSide,
            priority: 1,
            position: this.getPosition()
        };
    },
    measure: function(size) {
        if (this._updated || !this._insideLegendGroup) {
            this.draw(size[0], size[1]);
            this._updated = false;
        } else {
            this._items.forEach((item)=>{
                item.bBoxes = [];
            });
            this._updateElementsPosition(size[0], size[1]);
        }
        const rect = this.getLayoutOptions();
        return [
            rect.width,
            rect.height
        ];
    },
    move: function(rect) {
        this.shift(rect[0], rect[1]);
    },
    freeSpace: function() {
        this._options._incidentOccurred("W2104");
        this.erase();
    }
});
const plugin = {
    name: "legend",
    init: function() {
        const group = this._renderer.g().attr({
            class: this._rootClassPrefix + "-legend"
        }).enableLinks().append(this._renderer.root);
        this._legend = new Legend({
            renderer: this._renderer,
            group: group,
            widget: this,
            itemGroupClass: this._rootClassPrefix + "-item",
            titleGroupClass: this._rootClassPrefix + "-title",
            textField: "text",
            getFormatObject: function(data) {
                return {
                    item: data.item,
                    text: data.text
                };
            }
        });
        this._layout.add(this._legend);
    },
    extenders: {
        _applyTilesAppearance: function() {
            const that = this;
            this._items.forEach(function(item) {
                that._applyLegendItemStyle(item.id, item.getState());
            });
        },
        _buildNodes: function() {
            this._createLegendItems();
        }
    },
    members: {
        _applyLegendItemStyle: function(id, state) {
            const legend = this._legend;
            switch(state){
                case "hover":
                    legend.applyHover(id);
                    break;
                case "selection":
                    legend.applySelected(id);
                    break;
                default:
                    legend.resetItem(id);
            }
        },
        _createLegendItems: function() {
            if (this._legend.update(this._getLegendData(), this._getOption("legend"), this._themeManager.theme("legend").title)) {
                this._requestChange([
                    "LAYOUT"
                ]);
            }
        }
    },
    dispose: function() {
        this._legend.dispose();
    },
    customize: function(constructor) {
        constructor.prototype._proxyData.push(function(x, y) {
            if (this._legend.coordsIn(x, y)) {
                const item = this._legend.getItemByCoord(x, y);
                if (item) {
                    return {
                        id: item.id,
                        type: "legend"
                    };
                }
            }
        });
        constructor.addChange({
            code: "LEGEND",
            handler: function() {
                this._createLegendItems();
            },
            isThemeDependent: true,
            option: "legend",
            isOptionChange: true
        });
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/translators/range.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/translators/range.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Range": ()=>Range
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
;
;
const _isDefined = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"];
const _isDate = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"];
const _isFunction = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"];
;
const minSelector = "min";
const maxSelector = "max";
const minVisibleSelector = "minVisible";
const maxVisibleSelector = "maxVisible";
const baseSelector = "base";
const axisTypeSelector = "axisType";
function otherLessThan(thisValue, otherValue) {
    return otherValue < thisValue;
}
function otherGreaterThan(thisValue, otherValue) {
    return otherValue > thisValue;
}
function compareAndReplace(thisValue, otherValue, setValue, compare) {
    const otherValueDefined = _isDefined(otherValue);
    if (_isDefined(thisValue)) {
        if (otherValueDefined && compare(thisValue, otherValue)) {
            setValue(otherValue);
        }
    } else if (otherValueDefined) {
        setValue(otherValue);
    }
}
const Range = function(range) {
    range && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(this, range);
};
const _Range = Range;
_Range.prototype = {
    constructor: _Range,
    addRange: function(otherRange) {
        const that = this;
        const categories = that.categories;
        const otherCategories = otherRange.categories;
        const isDiscrete = "discrete" === that.axisType;
        const compareAndReplaceByField = function(field, compare) {
            compareAndReplace(that[field], otherRange[field], function(value) {
                that[field] = value;
            }, compare);
        };
        const controlValuesByVisibleBounds = function(valueField, visibleValueField, compare) {
            compareAndReplace(that[valueField], that[visibleValueField], function(value) {
                _isDefined(that[valueField]) && (that[valueField] = value);
            }, compare);
        };
        const checkField = function(field) {
            that[field] = that[field] || otherRange[field];
        };
        checkField("invert");
        checkField("containsConstantLine");
        checkField("axisType");
        checkField("dataType");
        checkField("isSpacedMargin");
        if ("logarithmic" === that.axisType) {
            checkField("base");
        } else {
            that.base = void 0;
        }
        compareAndReplaceByField("min", otherLessThan);
        compareAndReplaceByField("max", otherGreaterThan);
        if (isDiscrete) {
            checkField("minVisible");
            checkField("maxVisible");
        } else {
            compareAndReplaceByField("minVisible", otherLessThan);
            compareAndReplaceByField("maxVisible", otherGreaterThan);
        }
        compareAndReplaceByField("interval", otherLessThan);
        if (!isDiscrete) {
            controlValuesByVisibleBounds("min", "minVisible", otherLessThan);
            controlValuesByVisibleBounds("min", "maxVisible", otherLessThan);
            controlValuesByVisibleBounds("max", "maxVisible", otherGreaterThan);
            controlValuesByVisibleBounds("max", "minVisible", otherGreaterThan);
        }
        if (void 0 === categories) {
            that.categories = otherCategories;
        } else {
            that.categories = otherCategories ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["unique"])(categories.concat(otherCategories)) : categories;
        }
        if ("logarithmic" === that.axisType) {
            checkField("allowNegatives");
            compareAndReplaceByField("linearThreshold", otherLessThan);
        }
        return that;
    },
    isEmpty: function() {
        return (!_isDefined(this.min) || !_isDefined(this.max)) && (!this.categories || 0 === this.categories.length);
    },
    correctValueZeroLevel: function() {
        const that = this;
        if (_isDate(that.max) || _isDate(that.min)) {
            return that;
        }
        function setZeroLevel(min, max) {
            that[min] < 0 && that[max] < 0 && (that[max] = 0);
            that[min] > 0 && that[max] > 0 && (that[min] = 0);
        }
        setZeroLevel("min", "max");
        setZeroLevel("minVisible", "maxVisible");
        return that;
    },
    sortCategories (sort) {
        if (false === sort || !this.categories) {
            return;
        }
        if (Array.isArray(sort)) {
            const sortValues = sort.map((item)=>item.valueOf());
            const filteredSeriesCategories = this.categories.filter((item)=>-1 === sortValues.indexOf(item.valueOf()));
            this.categories = sort.concat(filteredSeriesCategories);
        } else {
            const notAFunction = !_isFunction(sort);
            if (notAFunction && "string" !== this.dataType) {
                sort = (a, b)=>a.valueOf() - b.valueOf();
            } else if (notAFunction) {
                sort = false;
            }
            sort && this.categories.sort(sort);
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/translators/category_translator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/translators/category_translator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
;
;
const round = Math.round;
function getValue(value) {
    return value;
}
const MIN_VALID_SCALE_OFFSET = .05;
const __TURBOPACK__default__export__ = {
    translate: function(category, directionOffset) {
        const canvasOptions = this._canvasOptions;
        const categoryIndex = this._categoriesToPoints[null === category || void 0 === category ? void 0 : category.valueOf()];
        const specialValue = this.translateSpecialCase(category);
        const startPointIndex = canvasOptions.startPointIndex || 0;
        const stickInterval = this._options.stick ? 0 : .5;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(specialValue)) {
            return round(specialValue);
        }
        if (!categoryIndex && 0 !== categoryIndex) {
            return null;
        }
        directionOffset = directionOffset || 0;
        const stickDelta = categoryIndex + stickInterval - startPointIndex + .5 * directionOffset;
        return round(this._calculateProjection(canvasOptions.interval * stickDelta));
    },
    getInterval: function() {
        return this._canvasOptions.interval;
    },
    getEventScale: function(zoomEvent) {
        const scale = zoomEvent.deltaScale || 1;
        return 1 - (1 - scale) / (.75 + this.visibleCategories.length / this._categories.length);
    },
    zoom: function(translate, scale) {
        const scaleOffset = Math.abs(Math.abs(scale) - 1);
        const isZoomIn = scale > 1;
        if (1 !== scale && scaleOffset < .05) {
            scale = this.getMinScale(isZoomIn);
        }
        const categories = this._categories;
        const canvasOptions = this._canvasOptions;
        const stick = this._options.stick;
        const invert = canvasOptions.invert;
        const interval = canvasOptions.interval * scale;
        const translateCategories = translate / interval;
        const visibleCount = (this.visibleCategories || []).length;
        let startCategoryIndex = parseInt((canvasOptions.startPointIndex || 0) + translateCategories + .5);
        const categoriesLength = parseInt((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(canvasOptions.canvasLength / interval) + (stick ? 1 : 0)) || 1;
        let endCategoryIndex;
        if (invert) {
            startCategoryIndex = parseInt((canvasOptions.startPointIndex || 0) + visibleCount - translateCategories + .5) - categoriesLength;
        }
        if (startCategoryIndex < 0) {
            startCategoryIndex = 0;
        }
        endCategoryIndex = startCategoryIndex + categoriesLength;
        if (endCategoryIndex > categories.length) {
            endCategoryIndex = categories.length;
            startCategoryIndex = endCategoryIndex - categoriesLength;
            if (startCategoryIndex < 0) {
                startCategoryIndex = 0;
            }
        }
        const newVisibleCategories = categories.slice(parseInt(startCategoryIndex), parseInt(endCategoryIndex));
        const newInterval = this._getDiscreteInterval(newVisibleCategories.length, canvasOptions);
        scale = newInterval / canvasOptions.interval;
        translate = this.translate(!invert ? newVisibleCategories[0] : newVisibleCategories[newVisibleCategories.length - 1]) * scale - (canvasOptions.startPoint + (stick ? 0 : newInterval / 2));
        return {
            min: newVisibleCategories[0],
            max: newVisibleCategories[newVisibleCategories.length - 1],
            translate: translate,
            scale: scale
        };
    },
    getMinScale: function(zoom) {
        const canvasOptions = this._canvasOptions;
        let categoriesLength = (this.visibleCategories || this._categories).length;
        categoriesLength += (parseInt(.1 * categoriesLength) || 1) * (zoom ? -2 : 2);
        return canvasOptions.canvasLength / (Math.max(categoriesLength, 1) * canvasOptions.interval);
    },
    getScale: function(min, max) {
        const canvasOptions = this._canvasOptions;
        const visibleArea = this.getCanvasVisibleArea();
        const stickOffset = !this._options.stick && 1;
        let minPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(min) ? this.translate(min, -stickOffset) : null;
        let maxPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(max) ? this.translate(max, +stickOffset) : null;
        if (null === minPoint) {
            minPoint = canvasOptions.invert ? visibleArea.max : visibleArea.min;
        }
        if (null === maxPoint) {
            maxPoint = canvasOptions.invert ? visibleArea.min : visibleArea.max;
        }
        return this.canvasLength / Math.abs(maxPoint - minPoint);
    },
    isValid: function(value) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value) ? this._categoriesToPoints[value.valueOf()] >= 0 : false;
    },
    getCorrectValue: getValue,
    to: function(value, direction) {
        const canvasOptions = this._canvasOptions;
        const categoryIndex = this._categoriesToPoints[null === value || void 0 === value ? void 0 : value.valueOf()];
        const startPointIndex = canvasOptions.startPointIndex || 0;
        const stickDelta = categoryIndex + (this._options.stick ? 0 : .5) - startPointIndex + (this._businessRange.invert ? -1 : 1) * direction * .5;
        return round(this._calculateProjection(canvasOptions.interval * stickDelta));
    },
    from: function(position) {
        let direction = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;
        const canvasOptions = this._canvasOptions;
        const startPoint = canvasOptions.startPoint;
        const categories = this.visibleCategories || this._categories;
        const categoriesLength = categories.length;
        const stickInterval = this._options.stick ? .5 : 0;
        let result = round((position - startPoint) / canvasOptions.interval + stickInterval - .5 - .5 * direction);
        if (result >= categoriesLength) {
            result = categoriesLength - 1;
        }
        if (result < 0) {
            result = 0;
        }
        if (canvasOptions.invert) {
            result = categoriesLength - result - 1;
        }
        return categories[result];
    },
    _add: function() {
        return NaN;
    },
    toValue: getValue,
    isValueProlonged: true,
    getRangeByMinZoomValue (minZoom, visualRange) {
        const categories = this._categories;
        const minVisibleIndex = categories.indexOf(visualRange.minVisible);
        const maxVisibleIndex = categories.indexOf(visualRange.maxVisible);
        const startIndex = minVisibleIndex + minZoom - 1;
        const endIndex = maxVisibleIndex - minZoom + 1;
        if (categories[startIndex]) {
            return [
                visualRange.minVisible,
                categories[startIndex]
            ];
        } else {
            return [
                categories[endIndex],
                visualRange.maxVisible
            ];
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/translators/interval_translator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/translators/interval_translator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
;
;
const floor = Math.floor;
;
const __TURBOPACK__default__export__ = {
    _intervalize: function(value, interval) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(value)) {
            return;
        }
        if ("datetime" === this._businessRange.dataType) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(value)) {
                value = new Date(value);
            } else {
                value = new Date(value.getTime());
            }
            value = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].correctDateWithUnitBeginning(value, interval, null, this._options.firstDayOfWeek);
        } else {
            value = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(floor((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(value / interval)) * interval, interval);
        }
        return value;
    },
    translate: function(bp, direction, skipRound, interval) {
        const specialValue = this.translateSpecialCase(bp);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(specialValue)) {
            return Math.round(specialValue);
        }
        interval = interval || this._options.interval;
        if (!this.isValid(bp, interval)) {
            return null;
        }
        return this.to(bp, direction, skipRound, interval);
    },
    getInterval: function() {
        return Math.round(this._canvasOptions.ratioOfCanvasRange * (this._businessRange.interval || Math.abs(this._canvasOptions.rangeMax - this._canvasOptions.rangeMin)));
    },
    zoom: function() {},
    getMinScale: function() {},
    getScale: function() {},
    _parse: function(value) {
        return "datetime" === this._businessRange.dataType ? new Date(value) : Number(value);
    },
    fromValue: function(value) {
        return this._parse(value);
    },
    toValue: function(value) {
        return this._parse(value);
    },
    isValid: function(value, interval) {
        const that = this;
        const co = that._canvasOptions;
        let rangeMin = co.rangeMin;
        let rangeMax = co.rangeMax;
        interval = interval || that._options.interval;
        if (null === value || isNaN(value)) {
            return false;
        }
        value = "datetime" === that._businessRange.dataType && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumeric"])(value) ? new Date(value) : value;
        if (interval !== that._options.interval) {
            rangeMin = that._intervalize(rangeMin, interval);
            rangeMax = that._intervalize(rangeMax, interval);
        }
        if (value.valueOf() < rangeMin || value.valueOf() >= __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].addInterval(rangeMax, interval)) {
            return false;
        }
        return true;
    },
    to: function(bp, direction, skipRound, interval) {
        interval = interval || this._options.interval;
        const v1 = this._intervalize(bp, interval);
        const v2 = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].addInterval(v1, interval);
        let res = this._to(v1, skipRound);
        const p2 = this._to(v2, skipRound);
        if (!direction) {
            res = floor((res + p2) / 2);
        } else if (direction > 0) {
            res = p2;
        }
        return res;
    },
    _to: function(value, skipRound) {
        const co = this._canvasOptions;
        const rMin = co.rangeMinVisible;
        const rMax = co.rangeMaxVisible;
        let offset = value - rMin;
        if (value < rMin) {
            offset = 0;
        } else if (value > rMax) {
            offset = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].addInterval(rMax, this._options.interval) - rMin;
        }
        const projectedValue = this._calculateProjection(offset * this._canvasOptions.ratioOfCanvasRange);
        return this._conversionValue(projectedValue, skipRound);
    },
    from: function(position, direction) {
        const origInterval = this._options.interval;
        let interval = origInterval;
        const co = this._canvasOptions;
        const rMin = co.rangeMinVisible;
        const rMax = co.rangeMaxVisible;
        let value;
        if ("datetime" === this._businessRange.dataType) {
            interval = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds(origInterval);
        }
        value = this._calculateUnProjection((position - this._canvasOptions.startPoint) / this._canvasOptions.ratioOfCanvasRange);
        value = this._intervalize(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].addInterval(value, interval / 2, direction > 0), origInterval);
        if (value < rMin) {
            value = rMin;
        } else if (value > rMax) {
            value = rMax;
        }
        return value;
    },
    _add: function() {
        return NaN;
    },
    isValueProlonged: true
};
}),
"[project]/node_modules/devextreme/esm/viz/translators/datetime_translator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/translators/datetime_translator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)");
;
function parse(value) {
    return null !== value ? new Date(value) : value;
}
const __TURBOPACK__default__export__ = {
    fromValue: parse,
    toValue: parse,
    _add: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].addDateInterval,
    convert: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].dateToMilliseconds
};
}),
"[project]/node_modules/devextreme/esm/viz/translators/logarithmic_translator.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/translators/logarithmic_translator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
;
;
const __TURBOPACK__default__export__ = {
    fromValue: function(value) {
        return null !== value ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(value, this._canvasOptions.base, this._businessRange.allowNegatives, this._businessRange.linearThreshold) : value;
    },
    toValue: function(value) {
        return null !== value ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raiseToExt"])(value, this._canvasOptions.base, this._businessRange.allowNegatives, this._businessRange.linearThreshold) : value;
    },
    getMinBarSize: function(minBarSize) {
        const visibleArea = this.getCanvasVisibleArea();
        const minValue = this.from(visibleArea.min + minBarSize);
        const canvasOptions = this._canvasOptions;
        const startValue = this.fromValue(this.from(visibleArea.min));
        const endValue = this.fromValue(minValue ?? this.from(visibleArea.max));
        const value = Math.abs(startValue - endValue);
        return Math.pow(canvasOptions.base, value);
    },
    checkMinBarSize: function(initialValue, minShownValue, stackValue) {
        const canvasOptions = this._canvasOptions;
        const prevValue = stackValue ? stackValue - initialValue : 0;
        const baseMethod = this.constructor.prototype.checkMinBarSize;
        let minBarSize;
        let updateValue;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(minShownValue) && prevValue > 0) {
            minBarSize = baseMethod(this.fromValue(stackValue / prevValue), this.fromValue(minShownValue) - canvasOptions.rangeMinVisible);
            updateValue = Math.pow(canvasOptions.base, this.fromValue(prevValue) + minBarSize) - prevValue;
        } else {
            updateValue = baseMethod(initialValue, minShownValue);
        }
        return updateValue;
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/translators/translator2d.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/translators/translator2d.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Translator2D": ()=>_Translator2d
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/range.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$category_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/category_translator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$interval_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/interval_translator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$datetime_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/datetime_translator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$logarithmic_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/logarithmic_translator.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/date.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const _abs = Math.abs;
const CANVAS_PROP = [
    "width",
    "height",
    "left",
    "top",
    "bottom",
    "right"
];
const dummyTranslator = {
    to (value) {
        const coord = this._canvasOptions.startPoint + (this._options.conversionValue ? value : Math.round(value));
        return coord > this._canvasOptions.endPoint ? this._canvasOptions.endPoint : coord;
    },
    from (value) {
        return value - this._canvasOptions.startPoint;
    }
};
const validateCanvas = function(canvas) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(CANVAS_PROP, function(_, prop) {
        canvas[prop] = parseInt(canvas[prop]) || 0;
    });
    return canvas;
};
const makeCategoriesToPoints = function(categories) {
    const categoriesToPoints = {};
    categories.forEach(function(item, i) {
        categoriesToPoints[item.valueOf()] = i;
    });
    return categoriesToPoints;
};
const validateBusinessRange = function(businessRange) {
    if (!(businessRange instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"])) {
        businessRange = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Range"](businessRange);
    }
    function validate(valueSelector, baseValueSelector) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(businessRange[valueSelector]) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(businessRange[baseValueSelector])) {
            businessRange[valueSelector] = businessRange[baseValueSelector];
        }
    }
    validate("minVisible", "min");
    validate("maxVisible", "max");
    return businessRange;
};
function prepareBreaks(breaks, range) {
    const transform = "logarithmic" === range.axisType ? function(value) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(value, range.base);
    } : function(value) {
        return value;
    };
    const array = [];
    let br;
    let transformFrom;
    let transformTo;
    let i;
    const length = breaks.length;
    let sum = 0;
    for(i = 0; i < length; i++){
        br = breaks[i];
        transformFrom = transform(br.from);
        transformTo = transform(br.to);
        sum += transformTo - transformFrom;
        array.push({
            trFrom: transformFrom,
            trTo: transformTo,
            from: br.from,
            to: br.to,
            length: sum,
            cumulativeWidth: br.cumulativeWidth
        });
    }
    return array;
}
function getCanvasBounds(range) {
    let min = range.min;
    let max = range.max;
    let minVisible = range.minVisible;
    let maxVisible = range.maxVisible;
    const isLogarithmic = "logarithmic" === range.axisType;
    if (isLogarithmic) {
        maxVisible = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(maxVisible, range.base, range.allowNegatives, range.linearThreshold);
        minVisible = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(minVisible, range.base, range.allowNegatives, range.linearThreshold);
        min = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(min, range.base, range.allowNegatives, range.linearThreshold);
        max = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLogExt"])(max, range.base, range.allowNegatives, range.linearThreshold);
    }
    return {
        base: range.base,
        rangeMin: min,
        rangeMax: max,
        rangeMinVisible: minVisible,
        rangeMaxVisible: maxVisible
    };
}
function getCheckingMethodsAboutBreaks(inverted) {
    return {
        isStartSide: !inverted ? function(pos, breaks, start, end) {
            return pos < breaks[0][start];
        } : function(pos, breaks, start, end) {
            return pos <= breaks[breaks.length - 1][end];
        },
        isEndSide: !inverted ? function(pos, breaks, start, end) {
            return pos >= breaks[breaks.length - 1][end];
        } : function(pos, breaks, start, end) {
            return pos > breaks[0][start];
        },
        isInBreak: !inverted ? function(pos, br, start, end) {
            return pos >= br[start] && pos < br[end];
        } : function(pos, br, start, end) {
            return pos > br[end] && pos <= br[start];
        },
        isBetweenBreaks: !inverted ? function(pos, br, prevBreak, start, end) {
            return pos < br[start] && pos >= prevBreak[end];
        } : function(pos, br, prevBreak, start, end) {
            return pos >= br[end] && pos < prevBreak[start];
        },
        getLength: !inverted ? function(br) {
            return br.length;
        } : function(br, lastBreak) {
            return lastBreak.length - br.length;
        },
        getBreaksSize: !inverted ? function(br) {
            return br.cumulativeWidth;
        } : function(br, lastBreak) {
            return lastBreak.cumulativeWidth - br.cumulativeWidth;
        }
    };
}
const _Translator2d = function(businessRange, canvas, options) {
    this.update(businessRange, canvas, options);
};
_Translator2d.prototype = {
    constructor: _Translator2d,
    reinit: function() {
        const that = this;
        const options = that._options;
        const range = that._businessRange;
        const categories = range.categories || [];
        let script = {};
        const canvasOptions = that._prepareCanvasOptions();
        const visibleCategories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(categories, range.minVisible, range.maxVisible).categories;
        const categoriesLength = visibleCategories.length;
        if (range.isEmpty()) {
            script = dummyTranslator;
        } else {
            switch(range.axisType){
                case "logarithmic":
                    script = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$logarithmic_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                    break;
                case "semidiscrete":
                    script = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$interval_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                    canvasOptions.ratioOfCanvasRange = canvasOptions.canvasLength / (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$date$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].addInterval(canvasOptions.rangeMaxVisible, options.interval) - canvasOptions.rangeMinVisible);
                    break;
                case "discrete":
                    script = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$category_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                    that._categories = categories;
                    canvasOptions.interval = that._getDiscreteInterval(options.addSpiderCategory ? categoriesLength + 1 : categoriesLength, canvasOptions);
                    that._categoriesToPoints = makeCategoriesToPoints(categories);
                    if (categoriesLength) {
                        canvasOptions.startPointIndex = that._categoriesToPoints[visibleCategories[0].valueOf()];
                        that.visibleCategories = visibleCategories;
                    }
                    break;
                default:
                    if ("datetime" === range.dataType) {
                        script = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$datetime_translator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
                    }
            }
        }
        (that._oldMethods || []).forEach(function(methodName) {
            delete that[methodName];
        });
        that._oldMethods = Object.keys(script);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(that, script);
        that._conversionValue = options.conversionValue ? (value)=>value : (value, skipRound)=>skipRound ? value : Math.round(value);
        that.sc = {};
        that._checkingMethodsAboutBreaks = [
            getCheckingMethodsAboutBreaks(false),
            getCheckingMethodsAboutBreaks(that.isInverted())
        ];
        that._translateBreaks();
        that._calculateSpecialValues();
    },
    _translateBreaks: function() {
        const breaks = this._breaks;
        const size = this._options.breaksSize;
        let i;
        let b;
        let end;
        let length;
        if (void 0 === breaks) {
            return;
        }
        for(i = 0, length = breaks.length; i < length; i++){
            b = breaks[i];
            end = this.translate(b.to);
            b.end = end;
            b.start = !b.gapSize ? !this.isInverted() ? end - size : end + size : end;
        }
    },
    _checkValueAboutBreaks: function(breaks, pos, start, end, methods) {
        let i;
        let length;
        let prop = {
            length: 0,
            breaksSize: void 0,
            inBreak: false
        };
        let br;
        let prevBreak;
        const lastBreak = breaks[breaks.length - 1];
        if (methods.isStartSide(pos, breaks, start, end)) {
            return prop;
        } else if (methods.isEndSide(pos, breaks, start, end)) {
            return {
                length: lastBreak.length,
                breaksSize: lastBreak.cumulativeWidth,
                inBreak: false
            };
        }
        for(i = 0, length = breaks.length; i < length; i++){
            br = breaks[i];
            prevBreak = breaks[i - 1];
            if (methods.isInBreak(pos, br, start, end)) {
                prop.inBreak = true;
                prop.break = br;
                break;
            }
            if (prevBreak && methods.isBetweenBreaks(pos, br, prevBreak, start, end)) {
                prop = {
                    length: methods.getLength(prevBreak, lastBreak),
                    breaksSize: methods.getBreaksSize(prevBreak, lastBreak),
                    inBreak: false
                };
                break;
            }
        }
        return prop;
    },
    isInverted: function() {
        return !(this._options.isHorizontal ^ this._businessRange.invert);
    },
    _getDiscreteInterval: function(categoriesLength, canvasOptions) {
        const correctedCategoriesCount = categoriesLength - (this._options.stick ? 1 : 0);
        return correctedCategoriesCount > 0 ? canvasOptions.canvasLength / correctedCategoriesCount : canvasOptions.canvasLength;
    },
    _prepareCanvasOptions () {
        const businessRange = this._businessRange;
        const canvasOptions = this._canvasOptions = getCanvasBounds(businessRange);
        const canvas = this._canvas;
        const breaks = this._breaks;
        let length;
        canvasOptions.startPadding = canvas.startPadding || 0;
        canvasOptions.endPadding = canvas.endPadding || 0;
        if (this._options.isHorizontal) {
            canvasOptions.startPoint = canvas.left + canvasOptions.startPadding;
            length = canvas.width;
            canvasOptions.endPoint = canvas.width - canvas.right - canvasOptions.endPadding;
            canvasOptions.invert = businessRange.invert;
        } else {
            canvasOptions.startPoint = canvas.top + canvasOptions.startPadding;
            length = canvas.height;
            canvasOptions.endPoint = canvas.height - canvas.bottom - canvasOptions.endPadding;
            canvasOptions.invert = !businessRange.invert;
        }
        this.canvasLength = canvasOptions.canvasLength = canvasOptions.endPoint - canvasOptions.startPoint;
        canvasOptions.rangeDoubleError = Math.pow(10, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPower"])(canvasOptions.rangeMax - canvasOptions.rangeMin) - (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getPower"])(length) - 2);
        canvasOptions.ratioOfCanvasRange = canvasOptions.canvasLength / (canvasOptions.rangeMaxVisible - canvasOptions.rangeMinVisible);
        if (void 0 !== breaks) {
            const visibleRangeLength = canvasOptions.rangeMaxVisible - canvasOptions.rangeMinVisible - breaks[breaks.length - 1].length;
            if (0 !== visibleRangeLength) {
                canvasOptions.ratioOfCanvasRange = (canvasOptions.canvasLength - breaks[breaks.length - 1].cumulativeWidth) / visibleRangeLength;
            }
        }
        return canvasOptions;
    },
    updateCanvas: function(canvas) {
        this._canvas = validateCanvas(canvas);
        this.reinit();
    },
    updateBusinessRange: function(businessRange) {
        const breaks = businessRange.breaks || [];
        this._userBreaks = businessRange.userBreaks || [];
        this._businessRange = validateBusinessRange(businessRange);
        this._breaks = breaks.length ? prepareBreaks(breaks, this._businessRange) : void 0;
        this.reinit();
    },
    update: function(businessRange, canvas, options) {
        this._options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(this._options || {}, options);
        this._canvas = validateCanvas(canvas);
        this.updateBusinessRange(businessRange);
    },
    getBusinessRange: function() {
        return this._businessRange;
    },
    getEventScale: function(zoomEvent) {
        return zoomEvent.deltaScale || 1;
    },
    getCanvasVisibleArea: function() {
        return {
            min: this._canvasOptions.startPoint,
            max: this._canvasOptions.endPoint
        };
    },
    _calculateSpecialValues: function() {
        const that = this;
        const canvasOptions = that._canvasOptions;
        const startPoint = canvasOptions.startPoint - canvasOptions.startPadding;
        const endPoint = canvasOptions.endPoint + canvasOptions.endPadding;
        const range = that._businessRange;
        const minVisible = range.minVisible;
        const maxVisible = range.maxVisible;
        const canvas_position_center_middle = startPoint + canvasOptions.canvasLength / 2;
        let canvas_position_default;
        if (minVisible < 0 && maxVisible > 0 && minVisible !== maxVisible) {
            canvas_position_default = that.translate(0, 1);
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(canvas_position_default)) {
            const invert = range.invert ^ (minVisible < 0 && maxVisible <= 0);
            if (that._options.isHorizontal) {
                canvas_position_default = invert ? endPoint : startPoint;
            } else {
                canvas_position_default = invert ? startPoint : endPoint;
            }
        }
        that.sc = {
            canvas_position_default: canvas_position_default,
            canvas_position_left: startPoint,
            canvas_position_top: startPoint,
            canvas_position_center: canvas_position_center_middle,
            canvas_position_middle: canvas_position_center_middle,
            canvas_position_right: endPoint,
            canvas_position_bottom: endPoint,
            canvas_position_start: canvasOptions.invert ? endPoint : startPoint,
            canvas_position_end: canvasOptions.invert ? startPoint : endPoint
        };
    },
    translateSpecialCase (value) {
        return this.sc[value];
    },
    _calculateProjection: function(distance) {
        const canvasOptions = this._canvasOptions;
        return canvasOptions.invert ? canvasOptions.endPoint - distance : canvasOptions.startPoint + distance;
    },
    _calculateUnProjection: function(distance) {
        const canvasOptions = this._canvasOptions;
        "datetime" === this._businessRange.dataType && (distance = Math.round(distance));
        return canvasOptions.invert ? canvasOptions.rangeMaxVisible.valueOf() - distance : canvasOptions.rangeMinVisible.valueOf() + distance;
    },
    getMinBarSize: function(minBarSize) {
        const visibleArea = this.getCanvasVisibleArea();
        const minValue = this.from(visibleArea.min + minBarSize);
        return _abs(this.from(visibleArea.min) - (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(minValue) ? this.from(visibleArea.max) : minValue));
    },
    checkMinBarSize: function(value, minShownValue) {
        return _abs(value) < minShownValue ? value >= 0 ? minShownValue : -minShownValue : value;
    },
    translate (bp, direction, skipRound) {
        const specialValue = this.translateSpecialCase(bp);
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(specialValue)) {
            return Math.round(specialValue);
        }
        if (isNaN(bp)) {
            return null;
        }
        return this.to(bp, direction, skipRound);
    },
    getInterval: function(interval) {
        const canvasOptions = this._canvasOptions;
        interval = interval ?? this._businessRange.interval;
        if (interval) {
            return Math.round(canvasOptions.ratioOfCanvasRange * interval);
        }
        return Math.round(canvasOptions.endPoint - canvasOptions.startPoint);
    },
    zoom (translate, scale, wholeRange) {
        const canvasOptions = this._canvasOptions;
        if (canvasOptions.rangeMinVisible.valueOf() === canvasOptions.rangeMaxVisible.valueOf() && 0 !== translate) {
            return this.zoomZeroLengthRange(translate, scale);
        }
        const startPoint = canvasOptions.startPoint;
        const endPoint = canvasOptions.endPoint;
        const isInverted = this.isInverted();
        let newStart = (startPoint + translate) / scale;
        let newEnd = (endPoint + translate) / scale;
        wholeRange = wholeRange || {};
        const minPoint = this.to(isInverted ? wholeRange.endValue : wholeRange.startValue);
        const maxPoint = this.to(isInverted ? wholeRange.startValue : wholeRange.endValue);
        let min;
        let max;
        if (minPoint > newStart) {
            newEnd -= newStart - minPoint;
            newStart = minPoint;
            min = isInverted ? wholeRange.endValue : wholeRange.startValue;
        }
        if (maxPoint < newEnd) {
            newStart -= newEnd - maxPoint;
            newEnd = maxPoint;
            max = isInverted ? wholeRange.startValue : wholeRange.endValue;
        }
        if (maxPoint - minPoint < newEnd - newStart) {
            newStart = minPoint;
            newEnd = maxPoint;
        }
        translate = (endPoint - startPoint) * newStart / (newEnd - newStart) - startPoint;
        scale = (startPoint + translate) / newStart || 1;
        min = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(min) ? min : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(this.from(newStart, 1));
        max = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(max) ? max : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(this.from(newEnd, -1));
        if (scale <= 1) {
            min = this._correctValueAboutBreaks(min, 1 === scale ? translate : -1);
            max = this._correctValueAboutBreaks(max, 1 === scale ? translate : 1);
        }
        if (min > max) {
            min = min > wholeRange.endValue ? wholeRange.endValue : min;
            max = max < wholeRange.startValue ? wholeRange.startValue : max;
        } else {
            min = min < wholeRange.startValue ? wholeRange.startValue : min;
            max = max > wholeRange.endValue ? wholeRange.endValue : max;
        }
        return {
            min: min,
            max: max,
            translate: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(translate),
            scale: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])(scale)
        };
    },
    _correctValueAboutBreaks (value, direction) {
        const br = this._userBreaks.filter((br)=>value >= br.from && value <= br.to);
        if (br.length) {
            return direction > 0 ? br[0].to : br[0].from;
        } else {
            return value;
        }
    },
    zoomZeroLengthRange (translate, scale) {
        const canvasOptions = this._canvasOptions;
        const min = canvasOptions.rangeMin;
        const max = canvasOptions.rangeMax;
        const correction = (max.valueOf() !== min.valueOf() ? max.valueOf() - min.valueOf() : _abs(canvasOptions.rangeMinVisible.valueOf() - min.valueOf())) / canvasOptions.canvasLength;
        const isDateTime = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(max) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDate"])(min);
        const isLogarithmic = "logarithmic" === this._businessRange.axisType;
        let newMin = canvasOptions.rangeMinVisible.valueOf() - correction;
        let newMax = canvasOptions.rangeMaxVisible.valueOf() + correction;
        newMin = isLogarithmic ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raiseToExt"])(newMin, canvasOptions.base)) : isDateTime ? new Date(newMin) : newMin;
        newMax = isLogarithmic ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["adjust"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["raiseToExt"])(newMax, canvasOptions.base)) : isDateTime ? new Date(newMax) : newMax;
        return {
            min: newMin,
            max: newMax,
            translate: translate,
            scale: scale
        };
    },
    getMinScale: function(zoom) {
        const { dataType: dataType, interval: interval } = this._businessRange;
        if ("datetime" === dataType && 1 === interval) {
            return this.getDateTimeMinScale(zoom);
        }
        return zoom ? 1.1 : .9;
    },
    getDateTimeMinScale (zoom) {
        const canvasOptions = this._canvasOptions;
        let length = canvasOptions.canvasLength / canvasOptions.ratioOfCanvasRange;
        length += (parseInt(.1 * length) || 1) * (zoom ? -2 : 2);
        return canvasOptions.canvasLength / (Math.max(length, 1) * canvasOptions.ratioOfCanvasRange);
    },
    getScale: function(val1, val2) {
        const canvasOptions = this._canvasOptions;
        if (canvasOptions.rangeMax === canvasOptions.rangeMin) {
            return 1;
        }
        val1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(val1) ? this.fromValue(val1) : canvasOptions.rangeMin;
        val2 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(val2) ? this.fromValue(val2) : canvasOptions.rangeMax;
        return (canvasOptions.rangeMax - canvasOptions.rangeMin) / Math.abs(val1 - val2);
    },
    isValid: function(value) {
        const co = this._canvasOptions;
        value = this.fromValue(value);
        return null !== value && !isNaN(value) && value.valueOf() + co.rangeDoubleError >= co.rangeMin && value.valueOf() - co.rangeDoubleError <= co.rangeMax;
    },
    getCorrectValue: function(value, direction) {
        const that = this;
        const breaks = that._breaks;
        let prop;
        value = that.fromValue(value);
        if (that._breaks) {
            prop = that._checkValueAboutBreaks(breaks, value, "trFrom", "trTo", that._checkingMethodsAboutBreaks[0]);
            if (true === prop.inBreak) {
                return that.toValue(direction > 0 ? prop.break.trTo : prop.break.trFrom);
            }
        }
        return that.toValue(value);
    },
    to: function(bp, direction, skipRound) {
        const range = this.getBusinessRange();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(range.maxVisible) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(range.minVisible) && range.maxVisible.valueOf() === range.minVisible.valueOf()) {
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(bp) || range.maxVisible.valueOf() !== bp.valueOf()) {
                return null;
            }
            return this.translateSpecialCase(0 === bp && this._options.shiftZeroValue ? "canvas_position_default" : "canvas_position_middle");
        }
        bp = this.fromValue(bp);
        const that = this;
        const canvasOptions = that._canvasOptions;
        const breaks = that._breaks;
        let prop = {
            length: 0
        };
        let commonBreakSize = 0;
        if (void 0 !== breaks) {
            prop = that._checkValueAboutBreaks(breaks, bp, "trFrom", "trTo", that._checkingMethodsAboutBreaks[0]);
            commonBreakSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(prop.breaksSize) ? prop.breaksSize : 0;
        }
        if (true === prop.inBreak) {
            if (direction > 0) {
                return prop.break.start;
            } else if (direction < 0) {
                return prop.break.end;
            } else {
                return null;
            }
        }
        return that._conversionValue(that._calculateProjection((bp - canvasOptions.rangeMinVisible - prop.length) * canvasOptions.ratioOfCanvasRange + commonBreakSize), skipRound);
    },
    from: function(pos, direction) {
        const that = this;
        const breaks = that._breaks;
        let prop = {
            length: 0
        };
        const canvasOptions = that._canvasOptions;
        const startPoint = canvasOptions.startPoint;
        let commonBreakSize = 0;
        if (void 0 !== breaks) {
            prop = that._checkValueAboutBreaks(breaks, pos, "start", "end", that._checkingMethodsAboutBreaks[1]);
            commonBreakSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isDefined"])(prop.breaksSize) ? prop.breaksSize : 0;
        }
        if (true === prop.inBreak) {
            if (direction > 0) {
                return that.toValue(prop.break.trTo);
            } else if (direction < 0) {
                return that.toValue(prop.break.trFrom);
            } else {
                return null;
            }
        }
        return that.toValue(that._calculateUnProjection((pos - startPoint - commonBreakSize) / canvasOptions.ratioOfCanvasRange + prop.length));
    },
    isValueProlonged: false,
    getRange: function() {
        return [
            this.toValue(this._canvasOptions.rangeMin),
            this.toValue(this._canvasOptions.rangeMax)
        ];
    },
    getScreenRange: function() {
        return [
            this._canvasOptions.startPoint,
            this._canvasOptions.endPoint
        ];
    },
    add: function(value, diff, dir) {
        return this._add(value, diff, (this._businessRange.invert ? -1 : 1) * dir);
    },
    _add: function(value, diff, coeff) {
        return this.toValue(this.fromValue(value) + diff * coeff);
    },
    fromValue: function(value) {
        return null !== value ? Number(value) : null;
    },
    toValue: function(value) {
        return null !== value ? Number(value) : null;
    },
    ratioOfCanvasRange () {
        return this._canvasOptions.ratioOfCanvasRange;
    },
    convert: (value)=>value,
    getRangeByMinZoomValue (minZoom, visualRange) {
        if (visualRange.minVisible + minZoom <= this._businessRange.max) {
            return [
                visualRange.minVisible,
                visualRange.minVisible + minZoom
            ];
        } else {
            return [
                visualRange.maxVisible - minZoom,
                visualRange.maxVisible
            ];
        }
    }
};
;
}),
"[project]/node_modules/devextreme/esm/viz/utils.js [app-ssr] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/utils.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "areCanvasesDifferent": ()=>areCanvasesDifferent,
    "floorCanvasDimensions": ()=>floorCanvasDimensions,
    "prepareSegmentRectPoints": ()=>prepareSegmentRectPoints
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-ssr] (ecmascript)");
;
;
;
const { floor: floor } = Math;
let prepareSegmentRectPoints = function(left, top, width, height, borderOptions) {
    const maxSW = ~~((width < height ? width : height) / 2);
    const sw = borderOptions.width || 0;
    const newSW = sw < maxSW ? sw : maxSW;
    left += newSW / 2;
    top += newSW / 2;
    width -= newSW;
    height -= newSW;
    const right = left + width;
    const bottom = top + height;
    let points = [];
    let segments = [];
    let segmentSequence;
    let visiblyOpt = 0;
    let prevSegmentVisibility = 0;
    const allSegment = {
        top: [
            [
                left,
                top
            ],
            [
                right,
                top
            ]
        ],
        right: [
            [
                right,
                top
            ],
            [
                right,
                bottom
            ]
        ],
        bottom: [
            [
                right,
                bottom
            ],
            [
                left,
                bottom
            ]
        ],
        left: [
            [
                left,
                bottom
            ],
            [
                left,
                top
            ]
        ]
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(allSegment, function(seg) {
        const visibility = !!borderOptions[seg];
        visiblyOpt = 2 * visiblyOpt + ~~visibility;
    });
    switch(visiblyOpt){
        case 13:
        case 9:
            segmentSequence = [
                "left",
                "top",
                "right",
                "bottom"
            ];
            break;
        case 11:
            segmentSequence = [
                "bottom",
                "left",
                "top",
                "right"
            ];
            break;
        default:
            segmentSequence = [
                "top",
                "right",
                "bottom",
                "left"
            ];
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(segmentSequence, function(_, seg) {
        const segmentVisibility = !!borderOptions[seg];
        if (!prevSegmentVisibility && segments.length) {
            points.push(segments);
            segments = [];
        }
        if (segmentVisibility) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(allSegment[seg].slice(prevSegmentVisibility), function(_, segment) {
                segments = segments.concat(segment);
            });
        }
        prevSegmentVisibility = ~~segmentVisibility;
    });
    segments.length && points.push(segments);
    1 === points.length && (points = points[0]);
    return {
        points: points,
        pathType: 15 === visiblyOpt ? "area" : "line"
    };
};
;
const areCanvasesDifferent = function(canvas1, canvas2) {
    const sizeLessThreshold = [
        "width",
        "height"
    ].every((key)=>Math.abs(canvas1[key] - canvas2[key]) < 1);
    const canvasCoordsIsEqual = [
        "left",
        "right",
        "top",
        "bottom"
    ].every((key)=>canvas1[key] === canvas2[key]);
    return !(sizeLessThreshold && canvasCoordsIsEqual);
};
const floorCanvasDimensions = function(canvas) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({}, canvas, {
        height: floor(canvas.height),
        width: floor(canvas.width)
    });
};
}),
"[project]/node_modules/devextreme/esm/viz/utils.js [app-ssr] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-ssr] (ecmascript) <locals>");
}),
"[project]/node_modules/devextreme/esm/viz/palette.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/palette.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "createPalette": ()=>createPalette,
    "currentPalette": ()=>currentPalette,
    "generateColors": ()=>generateColors,
    "getAccentColor": ()=>getAccentColor,
    "getDiscretePalette": ()=>getDiscretePalette,
    "getGradientPalette": ()=>getGradientPalette,
    "getPalette": ()=>getPalette,
    "registerPalette": ()=>registerPalette
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/color.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
;
;
const _floor = Math.floor;
const _ceil = Math.ceil;
;
const _isArray = Array.isArray;
;
const HIGHLIGHTING_STEP = 50;
const DEFAULT_PALETTE = "material";
const officePalette = {
    simpleSet: [
        "#5f8b95",
        "#ba4d51",
        "#af8a53",
        "#955f71",
        "#859666",
        "#7e688c"
    ],
    indicatingSet: [
        "#a3b97c",
        "#e1b676",
        "#ec7f83"
    ],
    gradientSet: [
        "#5f8b95",
        "#ba4d51"
    ],
    accentColor: "#ba4d51"
};
const palettes = {
    [DEFAULT_PALETTE]: {
        simpleSet: [
            "#1db2f5",
            "#f5564a",
            "#97c95c",
            "#ffc720",
            "#eb3573",
            "#a63db8"
        ],
        indicatingSet: [
            "#97c95c",
            "#ffc720",
            "#f5564a"
        ],
        gradientSet: [
            "#1db2f5",
            "#97c95c"
        ],
        accentColor: "#1db2f5"
    },
    office: officePalette,
    "harmony light": {
        simpleSet: [
            "#fcb65e",
            "#679ec5",
            "#ad79ce",
            "#7abd5c",
            "#e18e92",
            "#b6d623",
            "#b7abea",
            "#85dbd5"
        ],
        indicatingSet: [
            "#b6d623",
            "#fcb65e",
            "#e18e92"
        ],
        gradientSet: [
            "#7abd5c",
            "#fcb65e"
        ],
        accentColor: "#679ec5"
    },
    "soft pastel": {
        simpleSet: [
            "#60a69f",
            "#78b6d9",
            "#6682bb",
            "#a37182",
            "#eeba69",
            "#90ba58",
            "#456c68",
            "#7565a4"
        ],
        indicatingSet: [
            "#90ba58",
            "#eeba69",
            "#a37182"
        ],
        gradientSet: [
            "#78b6d9",
            "#eeba69"
        ],
        accentColor: "#60a69f"
    },
    pastel: {
        simpleSet: [
            "#bb7862",
            "#70b3a1",
            "#bb626a",
            "#057d85",
            "#ab394b",
            "#dac599",
            "#153459",
            "#b1d2c6"
        ],
        indicatingSet: [
            "#70b3a1",
            "#dac599",
            "#bb626a"
        ],
        gradientSet: [
            "#bb7862",
            "#70b3a1"
        ],
        accentColor: "#bb7862"
    },
    bright: {
        simpleSet: [
            "#70c92f",
            "#f8ca00",
            "#bd1550",
            "#e97f02",
            "#9d419c",
            "#7e4452",
            "#9ab57e",
            "#36a3a6"
        ],
        indicatingSet: [
            "#70c92f",
            "#f8ca00",
            "#bd1550"
        ],
        gradientSet: [
            "#e97f02",
            "#f8ca00"
        ],
        accentColor: "#e97f02"
    },
    soft: {
        simpleSet: [
            "#cbc87b",
            "#9ab57e",
            "#e55253",
            "#7e4452",
            "#e8c267",
            "#565077",
            "#6babac",
            "#ad6082"
        ],
        indicatingSet: [
            "#9ab57e",
            "#e8c267",
            "#e55253"
        ],
        gradientSet: [
            "#9ab57e",
            "#e8c267"
        ],
        accentColor: "#565077"
    },
    ocean: {
        simpleSet: [
            "#75c099",
            "#acc371",
            "#378a8a",
            "#5fa26a",
            "#064970",
            "#38c5d2",
            "#00a7c6",
            "#6f84bb"
        ],
        indicatingSet: [
            "#c8e394",
            "#7bc59d",
            "#397c8b"
        ],
        gradientSet: [
            "#acc371",
            "#38c5d2"
        ],
        accentColor: "#378a8a"
    },
    vintage: {
        simpleSet: [
            "#dea484",
            "#efc59c",
            "#cb715e",
            "#eb9692",
            "#a85c4c",
            "#f2c0b5",
            "#c96374",
            "#dd956c"
        ],
        indicatingSet: [
            "#ffe5c6",
            "#f4bb9d",
            "#e57660"
        ],
        gradientSet: [
            "#efc59c",
            "#cb715e"
        ],
        accentColor: "#cb715e"
    },
    violet: {
        simpleSet: [
            "#d1a1d1",
            "#eeacc5",
            "#7b5685",
            "#7e7cad",
            "#a13d73",
            "#5b41ab",
            "#e287e2",
            "#689cc1"
        ],
        indicatingSet: [
            "#d8e2f6",
            "#d0b2da",
            "#d56a8a"
        ],
        gradientSet: [
            "#eeacc5",
            "#7b5685"
        ],
        accentColor: "#7b5685"
    },
    carmine: {
        simpleSet: [
            "#fb7764",
            "#73d47f",
            "#fed85e",
            "#d47683",
            "#dde392",
            "#757ab2"
        ],
        indicatingSet: [
            "#5cb85c",
            "#f0ad4e",
            "#d9534f"
        ],
        gradientSet: [
            "#fb7764",
            "#73d47f"
        ],
        accentColor: "#f05b41"
    },
    "dark moon": {
        simpleSet: [
            "#4ddac1",
            "#f4c99a",
            "#80dd9b",
            "#f998b3",
            "#4aaaa0",
            "#a5aef1"
        ],
        indicatingSet: [
            "#59d8a4",
            "#f0ad4e",
            "#f9517e"
        ],
        gradientSet: [
            "#4ddac1",
            "#f4c99a"
        ],
        accentColor: "#3debd3"
    },
    "soft blue": {
        simpleSet: [
            "#7ab8eb",
            "#97da97",
            "#facb86",
            "#e78683",
            "#839bda",
            "#4db7be"
        ],
        indicatingSet: [
            "#5cb85c",
            "#f0ad4e",
            "#d9534f"
        ],
        gradientSet: [
            "#7ab8eb",
            "#97da97"
        ],
        accentColor: "#7ab8eb"
    },
    "dark violet": {
        simpleSet: [
            "#9c63ff",
            "#64c064",
            "#eead51",
            "#d2504b",
            "#4b6bbf",
            "#2da7b0"
        ],
        indicatingSet: [
            "#5cb85c",
            "#f0ad4e",
            "#d9534f"
        ],
        gradientSet: [
            "#9c63ff",
            "#64c064"
        ],
        accentColor: "#9c63ff"
    },
    "green mist": {
        simpleSet: [
            "#3cbab2",
            "#8ed962",
            "#5b9d95",
            "#efcc7c",
            "#f1929f",
            "#4d8dab"
        ],
        indicatingSet: [
            "#72d63c",
            "#ffc852",
            "#f74a5e"
        ],
        gradientSet: [
            "#3cbab2",
            "#8ed962"
        ],
        accentColor: "#3cbab2"
    }
};
let currentPaletteName;
function currentPalette(name) {
    if (void 0 === name) {
        return currentPaletteName || "material";
    } else {
        name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(name);
        currentPaletteName = name in palettes ? name : void 0;
    }
}
function generateColors(palette, count) {
    let options = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {
        keepLastColorInEnd: false
    };
    options.type = options.baseColorSet;
    options.extensionMode = options.paletteExtensionMode;
    return createPalette(palette, options).generateColors(count);
}
function getPalette(palette, parameters) {
    parameters = parameters || {};
    palette = palette || (void 0 === currentPaletteName ? parameters.themeDefault : currentPalette());
    let result;
    const type = parameters.type;
    if (_isArray(palette)) {
        return palette.slice(0);
    } else {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(palette)) {
            result = palettes[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(palette)];
        }
        if (!result) {
            result = palettes[currentPalette()];
        }
    }
    return type ? result[type].slice(0) : result;
}
function registerPalette(name, palette) {
    const item = {};
    let paletteName;
    if (_isArray(palette)) {
        item.simpleSet = palette.slice(0);
    } else if (palette) {
        item.simpleSet = _isArray(palette.simpleSet) ? palette.simpleSet.slice(0) : void 0;
        item.indicatingSet = _isArray(palette.indicatingSet) ? palette.indicatingSet.slice(0) : void 0;
        item.gradientSet = _isArray(palette.gradientSet) ? palette.gradientSet.slice(0) : void 0;
        item.accentColor = palette.accentColor;
    }
    if (!item.accentColor) {
        item.accentColor = item.simpleSet && item.simpleSet[0];
    }
    if (item.simpleSet || item.indicatingSet || item.gradientSet) {
        paletteName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(name);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])(palettes[paletteName] = palettes[paletteName] || {}, item);
    }
}
function getAccentColor(palette, themeDefault) {
    palette = getPalette(palette, {
        themeDefault: themeDefault
    });
    return palette.accentColor || palette[0];
}
function RingBuf(buf) {
    let ind = 0;
    this.next = function() {
        const res = buf[ind++];
        if (ind === buf.length) {
            this.reset();
        }
        return res;
    };
    this.reset = function() {
        ind = 0;
    };
}
function getAlternateColorsStrategy(palette, parameters) {
    const stepHighlight = parameters.useHighlight ? 50 : 0;
    const paletteSteps = new RingBuf([
        0,
        stepHighlight,
        -stepHighlight
    ]);
    let currentPalette = [];
    function reset() {
        const step = paletteSteps.next();
        currentPalette = step ? getAlteredPalette(palette, step) : palette.slice(0);
    }
    return {
        getColor: function(index) {
            const color = currentPalette[index % palette.length];
            if (index % palette.length === palette.length - 1) {
                reset();
            }
            return color;
        },
        generateColors: function(count) {
            const colors = [];
            count = count || parameters.count;
            for(let i = 0; i < count; i++){
                colors.push(this.getColor(i));
            }
            return colors;
        },
        reset: function() {
            paletteSteps.reset();
            reset();
        }
    };
}
function getExtrapolateColorsStrategy(palette, parameters) {
    return {
        getColor: function(index, count) {
            const paletteCount = palette.length;
            const cycles = _floor((count - 1) / paletteCount + 1);
            const color = palette[index % paletteCount];
            if (cycles > 1) {
                return function(color, cycleIndex, cycleCount) {
                    const hsl = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](color).hsl;
                    let l = hsl.l / 100;
                    const diapason = cycleCount - 1 / cycleCount;
                    let minL = l - .5 * diapason;
                    let maxL = l + .5 * diapason;
                    const cycleMiddle = (cycleCount - 1) / 2;
                    const cycleDiff = cycleIndex - cycleMiddle;
                    if (minL < Math.min(.5, .9 * l)) {
                        minL = Math.min(.5, .9 * l);
                    }
                    if (maxL > Math.max(.8, l + .15 * (1 - l))) {
                        maxL = Math.max(.8, l + .15 * (1 - l));
                    }
                    if (cycleDiff < 0) {
                        l -= (minL - l) * cycleDiff / cycleMiddle;
                    } else {
                        l += cycleDiff / cycleMiddle * (maxL - l);
                    }
                    hsl.l = 100 * l;
                    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].prototype.fromHSL(hsl).toHex();
                }(color, _floor(index / paletteCount), cycles);
            }
            return color;
        },
        generateColors: function(count) {
            const colors = [];
            count = count || parameters.count;
            for(let i = 0; i < count; i++){
                colors.push(this.getColor(i, count));
            }
            return colors;
        },
        reset: function() {}
    };
}
function getColorMixer(palette, parameters) {
    const paletteCount = palette.length;
    let extendedPalette = [];
    function distributeColors(count, colorsCount, startIndex, distribution) {
        const groupSize = Math.floor(count / colorsCount);
        let extraItems = count - colorsCount * groupSize;
        let i = startIndex;
        let middleIndex;
        let size;
        while(i < startIndex + count){
            size = groupSize;
            if (extraItems > 0) {
                size += 1;
                extraItems--;
            }
            middleIndex = size > 2 ? Math.floor(size / 2) : 0;
            distribution.push(i + middleIndex);
            i += size;
        }
        return distribution.sort(function(a, b) {
            return a - b;
        });
    }
    function getColorAndDistance(arr, startIndex, count) {
        startIndex = (count + startIndex) % count;
        let distance = 0;
        for(let i = startIndex; i < 2 * count; i += 1){
            const index = (count + i) % count;
            if (arr[index]) {
                return [
                    arr[index],
                    distance
                ];
            }
            distance++;
        }
    }
    function extendPalette(count) {
        if (count <= paletteCount) {
            return palette;
        }
        let result = [];
        const colorInGroups = paletteCount - 2;
        let currentColorIndex = 0;
        let cleanColorIndices = [];
        if (parameters.keepLastColorInEnd) {
            cleanColorIndices = distributeColors(count - 2, colorInGroups, 1, [
                0,
                count - 1
            ]);
        } else {
            cleanColorIndices = distributeColors(count - 1, paletteCount - 1, 1, [
                0
            ]);
        }
        for(let i = 0; i < count; i++){
            if (cleanColorIndices.indexOf(i) > -1) {
                result[i] = palette[currentColorIndex++];
            }
        }
        result = function(paletteWithEmptyColors, paletteLength) {
            for(let i = 0; i < paletteLength; i++){
                const color = paletteWithEmptyColors[i];
                if (!color) {
                    let color1 = paletteWithEmptyColors[i - 1];
                    if (!color1) {
                        continue;
                    } else {
                        const c2 = getColorAndDistance(paletteWithEmptyColors, i, paletteLength);
                        const color2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](c2[0]);
                        color1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](color1);
                        for(let j = 0; j < c2[1]; j++, i++){
                            paletteWithEmptyColors[i] = color1.blend(color2, (j + 1) / (c2[1] + 1)).toHex();
                        }
                    }
                }
            }
            return paletteWithEmptyColors;
        }(result, count);
        return result;
    }
    return {
        getColor: function(index, count) {
            count = count || parameters.count || paletteCount;
            if (extendedPalette.length !== count) {
                extendedPalette = extendPalette(count);
            }
            return extendedPalette[index % count];
        },
        generateColors: function(count, repeat) {
            count = count || parameters.count || paletteCount;
            if (repeat && count > paletteCount) {
                const colors = extendPalette(paletteCount);
                for(let i = 0; i < count - paletteCount; i++){
                    colors.push(colors[i]);
                }
                return colors;
            } else {
                return paletteCount > 0 ? extendPalette(count).slice(0, count) : [];
            }
        },
        reset: function() {}
    };
}
function createPalette(palette, parameters, themeDefaultPalette) {
    const paletteObj = {
        dispose () {
            this._extensionStrategy = null;
        },
        getNextColor (count) {
            return this._extensionStrategy.getColor(this._currentColor++, count);
        },
        generateColors (count, parameters) {
            return this._extensionStrategy.generateColors(count, (parameters || {}).repeat);
        },
        reset () {
            this._currentColor = 0;
            this._extensionStrategy.reset();
            return this;
        }
    };
    parameters = parameters || {};
    const extensionMode = (parameters.extensionMode || "").toLowerCase();
    const colors = getPalette(palette, {
        type: parameters.type || "simpleSet",
        themeDefault: themeDefaultPalette
    });
    if ("alternate" === extensionMode) {
        paletteObj._extensionStrategy = getAlternateColorsStrategy(colors, parameters);
    } else if ("extrapolate" === extensionMode) {
        paletteObj._extensionStrategy = getExtrapolateColorsStrategy(colors, parameters);
    } else {
        paletteObj._extensionStrategy = getColorMixer(colors, parameters);
    }
    paletteObj.reset();
    return paletteObj;
}
function getAlteredPalette(originalPalette, step) {
    const palette = [];
    let i;
    const ii = originalPalette.length;
    for(i = 0; i < ii; ++i){
        palette.push(getNewColor(originalPalette[i], step));
    }
    return palette;
}
function getNewColor(currentColor, step) {
    let newColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](currentColor).alter(step);
    const lightness = getLightness(newColor);
    if (lightness > 200 || lightness < 55) {
        newColor = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](currentColor).alter(-step / 2);
    }
    return newColor.toHex();
}
function getLightness(color) {
    return .3 * color.r + .59 * color.g + .11 * color.b;
}
function getDiscretePalette(source, size, themeDefaultPalette) {
    const palette = size > 0 ? createDiscreteColors(getPalette(source, {
        type: "gradientSet",
        themeDefault: themeDefaultPalette
    }), size) : [];
    return {
        getColor: function(index) {
            return palette[index] || null;
        }
    };
}
function createDiscreteColors(source, count) {
    const colorCount = count - 1;
    const sourceCount = source.length - 1;
    const colors = [];
    const gradient = [];
    let i;
    function addColor(pos) {
        const k = sourceCount * pos;
        const kl = _floor(k);
        const kr = _ceil(k);
        gradient.push(colors[kl].blend(colors[kr], k - kl).toHex());
    }
    for(i = 0; i <= sourceCount; ++i){
        colors.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](source[i]));
    }
    if (colorCount > 0) {
        for(i = 0; i <= colorCount; ++i){
            addColor(i / colorCount);
        }
    } else {
        addColor(.5);
    }
    return gradient;
}
function getGradientPalette(source, themeDefaultPalette) {
    const palette = getPalette(source, {
        type: "gradientSet",
        themeDefault: themeDefaultPalette
    });
    const color1 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](palette[0]);
    const color2 = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$color$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](palette[1]);
    return {
        getColor: function(ratio) {
            return 0 <= ratio && ratio <= 1 ? color1.blend(color2, ratio).toHex() : null;
        }
    };
}
}),
"[project]/node_modules/devextreme/esm/viz/themes.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/themes.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "addCacheItem": ()=>addCacheItem,
    "currentTheme": ()=>currentTheme,
    "getTheme": ()=>getTheme,
    "refreshTheme": ()=>refreshTheme,
    "registerTheme": ()=>registerTheme,
    "registerThemeSchemeAlias": ()=>registerThemeSchemeAlias,
    "removeCacheItem": ()=>removeCacheItem
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/themes.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$carmine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/carmine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$dark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/dark.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$contrast$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/contrast.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$darkmoon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/darkmoon.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$darkviolet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/darkviolet.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$greenmist$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/greenmist.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$softblue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/softblue.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$material$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/material/index.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$fluent$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/fluent/index.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const themes = {};
const themesMapping = {};
const themesSchemeMapping = {};
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"];
let currentThemeName = null;
let defaultTheme;
let nextCacheUid = 0;
const widgetsCache = {};
function getTheme(themeName) {
    const name = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(themeName);
    return themes[name] || themes[themesMapping[name] || currentTheme()];
}
function findThemeNameByName(name, scheme) {
    const fullThemeKey = `${name}.${scheme}`;
    return themesMapping[fullThemeKey] || themesSchemeMapping[fullThemeKey] || themesMapping[name];
}
function findThemeNameByPlatform(platform, version, scheme) {
    return findThemeNameByName(platform + version, scheme) || findThemeNameByName(platform, scheme);
}
function currentTheme(themeName, colorScheme) {
    if (!arguments.length) {
        return currentThemeName || findThemeNameByName((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$themes$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["current"])()) || defaultTheme;
    }
    const scheme = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(colorScheme);
    currentThemeName = (null !== themeName && void 0 !== themeName && themeName.platform ? findThemeNameByPlatform((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(themeName.platform), themeName.version, scheme) : findThemeNameByName((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(themeName), scheme)) || currentThemeName;
    return this;
}
function getThemeInfo(themeName, splitter) {
    const k = themeName.indexOf(splitter);
    return k > 0 ? {
        name: themeName.substring(0, k),
        scheme: themeName.substring(k + 1)
    } : null;
}
function registerThemeName(themeName, targetThemeName) {
    const themeInfo = getThemeInfo(themeName, ".") || {
        name: themeName
    };
    const name = themeInfo.name;
    const scheme = themeInfo.scheme;
    if (scheme) {
        const fullThemeKey = `${name}.${scheme}`;
        themesMapping[name] = themesMapping[name] || targetThemeName;
        themesMapping[fullThemeKey] = targetThemeName;
    } else {
        themesMapping[name] = targetThemeName;
    }
}
function registerTheme(theme, baseThemeName) {
    const themeName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeEnum"])(theme && theme.name);
    if (themeName) {
        theme.isDefault && (defaultTheme = themeName);
        registerThemeName(themeName, themeName);
        themes[themeName] = _extend(true, {}, getTheme(baseThemeName), patchTheme(theme));
    }
}
function registerThemeSchemeAlias(from, to) {
    themesSchemeMapping[from] = to;
}
function mergeScalar(target, field, source, sourceValue) {
    const _value = (null === source || void 0 === source ? void 0 : source[field]) ?? sourceValue;
    if (void 0 !== _value && void 0 === target[field]) {
        target[field] = _value;
    }
}
function mergeObject(target, field, source, sourceValue) {
    const _value = (null === source || void 0 === source ? void 0 : source[field]) ?? sourceValue;
    if (void 0 !== _value) {
        target[field] = _extend(true, {}, _value, target[field]);
    }
}
function patchTheme(theme) {
    theme = _extend(true, {
        loadingIndicator: {
            font: {}
        },
        export: {
            font: {}
        },
        legend: {
            font: {},
            border: {}
        },
        title: {
            font: {}
        },
        tooltip: {
            font: {}
        },
        "chart:common": {},
        "chart:common:axis": {
            grid: {},
            minorGrid: {},
            tick: {},
            minorTick: {},
            title: {
                font: {}
            },
            label: {
                font: {}
            }
        },
        "chart:common:annotation": {
            font: {},
            border: {}
        },
        chart: {
            commonSeriesSettings: {
                candlestick: {}
            }
        },
        pie: {},
        polar: {},
        gauge: {
            scale: {
                tick: {},
                minorTick: {},
                label: {
                    font: {}
                }
            }
        },
        barGauge: {},
        funnel: {},
        sankey: {},
        map: {
            background: {}
        },
        treeMap: {
            tile: {
                selectionStyle: {
                    border: {}
                }
            },
            group: {
                border: {},
                selectionStyle: {
                    border: {}
                },
                label: {
                    font: {}
                }
            }
        },
        rangeSelector: {
            scale: {
                tick: {},
                minorTick: {},
                label: {
                    font: {}
                }
            },
            chart: {}
        },
        sparkline: {},
        bullet: {}
    }, theme);
    mergeScalar(theme.loadingIndicator, "backgroundColor", theme);
    mergeScalar(theme.chart.commonSeriesSettings.candlestick, "innerColor", null, theme.backgroundColor);
    mergeScalar(theme.map.background, "color", null, theme.backgroundColor);
    mergeScalar(theme.title.font, "color", null, theme.primaryTitleColor);
    mergeObject(theme.title, "subtitle", null, theme.title);
    mergeScalar(theme.legend.font, "color", null, theme.secondaryTitleColor);
    mergeScalar(theme.legend.border, "color", null, theme.gridColor);
    patchAxes(theme);
    [
        "chart",
        "pie",
        "polar",
        "gauge",
        "barGauge",
        "map",
        "treeMap",
        "funnel",
        "rangeSelector",
        "sparkline",
        "bullet",
        "sankey"
    ].forEach((section)=>{
        mergeScalar(theme[section], "redrawOnResize", theme);
        mergeScalar(theme[section], "containerBackgroundColor", null, theme.backgroundColor);
        mergeObject(theme[section], "tooltip", theme);
        mergeObject(theme[section], "export", theme);
    });
    [
        "chart",
        "pie",
        "polar",
        "gauge",
        "barGauge",
        "map",
        "treeMap",
        "funnel",
        "rangeSelector",
        "sankey"
    ].forEach((section)=>{
        mergeObject(theme[section], "loadingIndicator", theme);
        mergeObject(theme[section], "legend", theme);
        mergeObject(theme[section], "title", theme);
    });
    [
        "chart",
        "pie",
        "polar"
    ].forEach((section)=>{
        mergeObject(theme, section, null, theme["chart:common"]);
    });
    [
        "chart",
        "polar"
    ].forEach((section)=>{
        theme[section] = theme[section] || {};
        mergeObject(theme[section], "commonAxisSettings", null, theme["chart:common:axis"]);
    });
    [
        "chart",
        "polar",
        "map",
        "pie"
    ].forEach((section)=>{
        theme[section] = theme[section] || {};
        mergeObject(theme[section], "commonAnnotationSettings", null, theme["chart:common:annotation"]);
    });
    mergeObject(theme.rangeSelector.chart, "commonSeriesSettings", theme.chart);
    mergeObject(theme.rangeSelector.chart, "dataPrepareSettings", theme.chart);
    mergeScalar(theme.treeMap.group.border, "color", null, theme.gridColor);
    mergeScalar(theme.treeMap.tile.selectionStyle.border, "color", null, theme.primaryTitleColor);
    mergeScalar(theme.treeMap.group.selectionStyle.border, "color", null, theme.primaryTitleColor);
    mergeScalar(theme.map.legend, "backgroundColor", theme);
    patchMapLayers(theme);
    return theme;
}
function patchAxes(theme) {
    const commonAxisSettings = theme["chart:common:axis"];
    [
        commonAxisSettings.grid,
        commonAxisSettings.minorGrid
    ].forEach((obj)=>{
        mergeScalar(obj, "color", null, theme.gridColor);
    });
    [
        commonAxisSettings,
        commonAxisSettings.tick,
        commonAxisSettings.minorTick,
        commonAxisSettings.label.font
    ].forEach((obj)=>{
        mergeScalar(obj, "color", null, theme.axisColor);
    });
    mergeScalar(commonAxisSettings.title.font, "color", null, theme.secondaryTitleColor);
    mergeScalar(theme.gauge.scale.label.font, "color", null, theme.axisColor);
    mergeScalar(theme.gauge.scale.tick, "color", null, theme.backgroundColor);
    mergeScalar(theme.gauge.scale.minorTick, "color", null, theme.backgroundColor);
    mergeScalar(theme.rangeSelector.scale.label.font, "color", null, theme.axisColor);
}
function patchMapLayers(theme) {
    const map = theme.map;
    [
        "area",
        "line",
        "marker"
    ].forEach((section)=>{
        mergeObject(map, "layer:" + section, null, map.layer);
    });
    [
        "dot",
        "bubble",
        "pie",
        "image"
    ].forEach((section)=>{
        mergeObject(map, "layer:marker:" + section, null, map["layer:marker"]);
    });
}
function addCacheItem(target) {
    const cacheUid = ++nextCacheUid;
    target._cache = cacheUid;
    widgetsCache[cacheUid] = target;
}
function removeCacheItem(target) {
    delete widgetsCache[target._cache];
}
function refreshTheme() {
    Object.keys(widgetsCache).forEach((key)=>{
        widgetsCache[key].refresh();
    });
    return this;
}
if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isEmptyObject"])(themes) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isEmptyObject"])(themesMapping) && !defaultTheme) {
    [].concat(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$carmine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$dark$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$contrast$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$darkmoon$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$darkviolet$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$greenmist$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$softblue$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$material$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$fluent$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]).forEach((t)=>{
        registerTheme(t.theme, t.baseThemeName);
    });
}
}),
"[project]/node_modules/devextreme/esm/viz/chart.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$m_chart$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/m_chart.js [app-ssr] (ecmascript)");
;
const __TURBOPACK__default__export__ = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$m_chart$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
}),

};

//# sourceMappingURL=node_modules_devextreme_esm_viz_b487fee5._.js.map