{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/crosshair.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/crosshair.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    patchFontOptions\r\n} from \"../core/utils\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nconst math = Math;\r\nconst mathAbs = math.abs;\r\nconst mathMin = math.min;\r\nconst mathMax = math.max;\r\nconst mathFloor = math.floor;\r\nconst HORIZONTAL = \"horizontal\";\r\nconst VERTICAL = \"vertical\";\r\nconst LABEL_BACKGROUND_PADDING_X = 8;\r\nconst LABEL_BACKGROUND_PADDING_Y = 4;\r\nconst CENTER = \"center\";\r\nconst RIGHT = \"right\";\r\nconst LEFT = \"left\";\r\nconst TOP = \"top\";\r\nconst BOTTOM = \"bottom\";\r\nexport function getMargins() {\r\n    return {\r\n        x: 8,\r\n        y: 4\r\n    }\r\n}\r\n\r\nfunction getRectangleBBox(bBox) {\r\n    return {\r\n        x: bBox.x - 8,\r\n        y: bBox.y - 4,\r\n        width: bBox.width + 16,\r\n        height: bBox.height + 8\r\n    }\r\n}\r\n\r\nfunction getLabelCheckerPosition(x, y, isHorizontal, canvas) {\r\n    const params = isHorizontal ? [\"x\", \"width\", \"y\", \"height\", y, 0] : [\"y\", \"height\", \"x\", \"width\", x, 1];\r\n    return function(bBox, position, coord) {\r\n        const labelCoord = {\r\n            x: coord.x,\r\n            y: coord.y\r\n        };\r\n        const rectangleBBox = getRectangleBBox(bBox);\r\n        const delta = isHorizontal ? coord.y - bBox.y - bBox.height / 2 : coord.y - bBox.y;\r\n        labelCoord.y = isHorizontal || !isHorizontal && position === BOTTOM ? coord.y + delta : coord.y;\r\n        if (rectangleBBox[params[0]] < 0) {\r\n            labelCoord[params[0]] -= rectangleBBox[params[0]]\r\n        } else if (rectangleBBox[params[0]] + rectangleBBox[params[1]] + delta * params[5] > canvas[params[1]]) {\r\n            labelCoord[params[0]] -= rectangleBBox[params[0]] + rectangleBBox[params[1]] + delta * params[5] - canvas[params[1]]\r\n        }\r\n        if (params[4] - rectangleBBox[params[3]] / 2 < 0) {\r\n            labelCoord[params[2]] -= params[4] - rectangleBBox[params[3]] / 2\r\n        } else if (params[4] + rectangleBBox[params[3]] / 2 > canvas[params[3]]) {\r\n            labelCoord[params[2]] -= params[4] + rectangleBBox[params[3]] / 2 - canvas[params[3]]\r\n        }\r\n        return labelCoord\r\n    }\r\n}\r\nexport function Crosshair(renderer, options, params, group) {\r\n    this._renderer = renderer;\r\n    this._crosshairGroup = group;\r\n    this._options = {};\r\n    this.update(options, params)\r\n}\r\nCrosshair.prototype = {\r\n    constructor: Crosshair,\r\n    update: function(options, params) {\r\n        const canvas = params.canvas;\r\n        this._canvas = {\r\n            top: canvas.top,\r\n            bottom: canvas.height - canvas.bottom,\r\n            left: canvas.left,\r\n            right: canvas.width - canvas.right,\r\n            width: canvas.width,\r\n            height: canvas.height\r\n        };\r\n        this._axes = params.axes;\r\n        this._panes = params.panes;\r\n        this._prepareOptions(options, HORIZONTAL);\r\n        this._prepareOptions(options, VERTICAL)\r\n    },\r\n    dispose: function() {\r\n        this._renderer = this._crosshairGroup = this._options = this._axes = this._canvas = this._horizontalGroup = this._verticalGroup = this._horizontal = this._vertical = this._circle = this._panes = null\r\n    },\r\n    _prepareOptions: function(options, direction) {\r\n        const lineOptions = options[direction + \"Line\"];\r\n        this._options[direction] = {\r\n            visible: lineOptions.visible,\r\n            line: {\r\n                stroke: lineOptions.color || options.color,\r\n                \"stroke-width\": lineOptions.width || options.width,\r\n                dashStyle: lineOptions.dashStyle || options.dashStyle,\r\n                opacity: lineOptions.opacity || options.opacity,\r\n                \"stroke-linecap\": \"butt\"\r\n            },\r\n            label: extend(true, {}, options.label, lineOptions.label)\r\n        }\r\n    },\r\n    _createLines: function(options, sharpParam, group) {\r\n        const lines = [];\r\n        const canvas = this._canvas;\r\n        const points = [canvas.left, canvas.top, canvas.left, canvas.top];\r\n        for (let i = 0; i < 2; i++) {\r\n            lines.push(this._renderer.path(points, \"line\").attr(options).sharp(sharpParam).append(group))\r\n        }\r\n        return lines\r\n    },\r\n    render: function() {\r\n        const that = this;\r\n        const renderer = that._renderer;\r\n        const options = that._options;\r\n        const verticalOptions = options.vertical;\r\n        const horizontalOptions = options.horizontal;\r\n        const extraOptions = horizontalOptions.visible ? horizontalOptions.line : verticalOptions.line;\r\n        const circleOptions = {\r\n            stroke: extraOptions.stroke,\r\n            \"stroke-width\": extraOptions[\"stroke-width\"],\r\n            dashStyle: extraOptions.dashStyle,\r\n            opacity: extraOptions.opacity\r\n        };\r\n        const canvas = that._canvas;\r\n        that._horizontal = {};\r\n        that._vertical = {};\r\n        that._circle = renderer.circle(canvas.left, canvas.top, 0).attr(circleOptions).append(that._crosshairGroup);\r\n        that._horizontalGroup = renderer.g().append(that._crosshairGroup);\r\n        that._verticalGroup = renderer.g().append(that._crosshairGroup);\r\n        if (verticalOptions.visible) {\r\n            that._vertical.lines = that._createLines(verticalOptions.line, \"h\", that._verticalGroup);\r\n            that._vertical.labels = that._createLabels(that._axes[0], verticalOptions, false, that._verticalGroup)\r\n        }\r\n        if (horizontalOptions.visible) {\r\n            that._horizontal.lines = that._createLines(horizontalOptions.line, \"v\", that._horizontalGroup);\r\n            that._horizontal.labels = that._createLabels(that._axes[1], horizontalOptions, true, that._horizontalGroup)\r\n        }\r\n        that.hide()\r\n    },\r\n    _createLabels: function(axes, options, isHorizontal, group) {\r\n        const canvas = this._canvas;\r\n        const renderer = this._renderer;\r\n        let x;\r\n        let y;\r\n        let text;\r\n        const labels = [];\r\n        let background;\r\n        let currentLabelPos;\r\n        const labelOptions = options.label;\r\n        if (labelOptions.visible) {\r\n            axes.forEach((function(axis) {\r\n                const position = axis.getOptions().position;\r\n                if (axis.getTranslator().getBusinessRange().isEmpty()) {\r\n                    return\r\n                }\r\n                currentLabelPos = axis.getLabelsPosition();\r\n                if (isHorizontal) {\r\n                    y = canvas.top;\r\n                    x = currentLabelPos\r\n                } else {\r\n                    x = canvas.left;\r\n                    y = currentLabelPos\r\n                }\r\n                const align = position === TOP || position === BOTTOM ? CENTER : position === RIGHT ? LEFT : RIGHT;\r\n                background = renderer.rect(0, 0, 0, 0).attr({\r\n                    fill: labelOptions.backgroundColor || options.line.stroke\r\n                }).append(group);\r\n                text = renderer.text(\"0\", 0, 0).css(patchFontOptions(options.label.font)).attr({\r\n                    align: align,\r\n                    class: labelOptions.cssClass\r\n                }).append(group);\r\n                labels.push({\r\n                    text: text,\r\n                    background: background,\r\n                    axis: axis,\r\n                    options: labelOptions,\r\n                    pos: {\r\n                        coord: currentLabelPos,\r\n                        side: position\r\n                    },\r\n                    startXY: {\r\n                        x: x,\r\n                        y: y\r\n                    }\r\n                })\r\n            }))\r\n        }\r\n        return labels\r\n    },\r\n    _updateText: function(value, axisName, labels, point, func) {\r\n        const that = this;\r\n        labels.forEach((function(label) {\r\n            const axis = label.axis;\r\n            const coord = label.startXY;\r\n            const textElement = label.text;\r\n            const backgroundElement = label.background;\r\n            let text = \"\";\r\n            if (!axis.name || axis.name === axisName) {\r\n                text = axis.getFormattedValue(value, label.options, point)\r\n            }\r\n            if (text) {\r\n                textElement.attr({\r\n                    text: text,\r\n                    x: coord.x,\r\n                    y: coord.y\r\n                });\r\n                textElement.attr(func(textElement.getBBox(), label.pos.side, coord));\r\n                that._updateLinesCanvas(label);\r\n                backgroundElement.attr(getRectangleBBox(textElement.getBBox()))\r\n            } else {\r\n                textElement.attr({\r\n                    text: \"\"\r\n                });\r\n                backgroundElement.attr({\r\n                    x: 0,\r\n                    y: 0,\r\n                    width: 0,\r\n                    height: 0\r\n                })\r\n            }\r\n        }))\r\n    },\r\n    hide: function() {\r\n        this._crosshairGroup.attr({\r\n            visibility: \"hidden\"\r\n        })\r\n    },\r\n    _updateLinesCanvas: function(label) {\r\n        const position = label.pos.side;\r\n        const labelCoord = label.pos.coord;\r\n        const coords = this._linesCanvas;\r\n        const canvas = this._canvas;\r\n        coords[position] = coords[position] !== canvas[position] && mathAbs(coords[position] - canvas[position]) < mathAbs(labelCoord - canvas[position]) ? coords[position] : labelCoord\r\n    },\r\n    _updateLines: function(lines, x, y, r, isHorizontal) {\r\n        const coords = this._linesCanvas;\r\n        const canvas = this._canvas;\r\n        const points = isHorizontal ? [\r\n            [mathMin(x - r, coords.left), canvas.top, x - r, canvas.top],\r\n            [x + r, canvas.top, mathMax(coords.right, x + r), canvas.top]\r\n        ] : [\r\n            [canvas.left, mathMin(coords.top, y - r), canvas.left, y - r],\r\n            [canvas.left, y + r, canvas.left, mathMax(coords.bottom, y + r)]\r\n        ];\r\n        for (let i = 0; i < 2; i++) {\r\n            lines[i].attr({\r\n                points: points[i]\r\n            }).sharp(isHorizontal ? \"v\" : \"h\", isHorizontal ? y === canvas.bottom ? -1 : 1 : x === canvas.right ? -1 : 1)\r\n        }\r\n    },\r\n    _resetLinesCanvas: function() {\r\n        const canvas = this._canvas;\r\n        this._linesCanvas = {\r\n            left: canvas.left,\r\n            right: canvas.right,\r\n            top: canvas.top,\r\n            bottom: canvas.bottom\r\n        }\r\n    },\r\n    _getClipRectForPane: function(x, y) {\r\n        const panes = this._panes;\r\n        let i;\r\n        let coords;\r\n        for (i = 0; i < panes.length; i++) {\r\n            coords = panes[i].coords;\r\n            if (coords.left <= x && coords.right >= x && coords.top <= y && coords.bottom >= y) {\r\n                return panes[i].clipRect\r\n            }\r\n        }\r\n        return {\r\n            id: null\r\n        }\r\n    },\r\n    show: function(data) {\r\n        const that = this;\r\n        const point = data.point;\r\n        const pointData = point.getCrosshairData(data.x, data.y);\r\n        const r = point.getPointRadius();\r\n        const horizontal = that._horizontal;\r\n        const vertical = that._vertical;\r\n        const rad = !r ? 0 : r + 3;\r\n        const canvas = that._canvas;\r\n        const x = mathFloor(pointData.x);\r\n        const y = mathFloor(pointData.y);\r\n        if (x >= canvas.left && x <= canvas.right && y >= canvas.top && y <= canvas.bottom) {\r\n            that._crosshairGroup.attr({\r\n                visibility: \"visible\"\r\n            });\r\n            that._resetLinesCanvas();\r\n            that._circle.attr({\r\n                cx: x,\r\n                cy: y,\r\n                r: rad,\r\n                \"clip-path\": that._getClipRectForPane(x, y).id\r\n            });\r\n            if (horizontal.lines) {\r\n                that._updateText(pointData.yValue, pointData.axis, horizontal.labels, point, getLabelCheckerPosition(x, y, true, canvas));\r\n                that._updateLines(horizontal.lines, x, y, rad, true);\r\n                that._horizontalGroup.attr({\r\n                    translateY: y - canvas.top\r\n                })\r\n            }\r\n            if (vertical.lines) {\r\n                that._updateText(pointData.xValue, pointData.axis, vertical.labels, point, getLabelCheckerPosition(x, y, false, canvas));\r\n                that._updateLines(vertical.lines, x, y, rad, false);\r\n                that._verticalGroup.attr({\r\n                    translateX: x - canvas.left\r\n                })\r\n            }\r\n        } else {\r\n            that.hide()\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAGA;AAAA;;;AAGA,MAAM,OAAO;AACb,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,UAAU,KAAK,GAAG;AACxB,MAAM,YAAY,KAAK,KAAK;AAC5B,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM,6BAA6B;AACnC,MAAM,6BAA6B;AACnC,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,SAAS;AACR,SAAS;IACZ,OAAO;QACH,GAAG;QACH,GAAG;IACP;AACJ;AAEA,SAAS,iBAAiB,IAAI;IAC1B,OAAO;QACH,GAAG,KAAK,CAAC,GAAG;QACZ,GAAG,KAAK,CAAC,GAAG;QACZ,OAAO,KAAK,KAAK,GAAG;QACpB,QAAQ,KAAK,MAAM,GAAG;IAC1B;AACJ;AAEA,SAAS,wBAAwB,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,MAAM;IACvD,MAAM,SAAS,eAAe;QAAC;QAAK;QAAS;QAAK;QAAU;QAAG;KAAE,GAAG;QAAC;QAAK;QAAU;QAAK;QAAS;QAAG;KAAE;IACvG,OAAO,SAAS,IAAI,EAAE,QAAQ,EAAE,KAAK;QACjC,MAAM,aAAa;YACf,GAAG,MAAM,CAAC;YACV,GAAG,MAAM,CAAC;QACd;QACA,MAAM,gBAAgB,iBAAiB;QACvC,MAAM,QAAQ,eAAe,MAAM,CAAC,GAAG,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,GAAG,KAAK,CAAC;QAClF,WAAW,CAAC,GAAG,gBAAgB,CAAC,gBAAgB,aAAa,SAAS,MAAM,CAAC,GAAG,QAAQ,MAAM,CAAC;QAC/F,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,GAAG;YAC9B,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC;QACrD,OAAO,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACpG,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,QAAQ,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QACxH;QACA,IAAI,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,GAAG;YAC9C,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG;QACpE,OAAO,IAAI,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,EAAE;YACrE,UAAU,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,MAAM,CAAC,EAAE,GAAG,aAAa,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC;QACzF;QACA,OAAO;IACX;AACJ;AACO,SAAS,UAAU,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,KAAK;IACtD,IAAI,CAAC,SAAS,GAAG;IACjB,IAAI,CAAC,eAAe,GAAG;IACvB,IAAI,CAAC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAAC,MAAM,CAAC,SAAS;AACzB;AACA,UAAU,SAAS,GAAG;IAClB,aAAa;IACb,QAAQ,SAAS,OAAO,EAAE,MAAM;QAC5B,MAAM,SAAS,OAAO,MAAM;QAC5B,IAAI,CAAC,OAAO,GAAG;YACX,KAAK,OAAO,GAAG;YACf,QAAQ,OAAO,MAAM,GAAG,OAAO,MAAM;YACrC,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK,GAAG,OAAO,KAAK;YAClC,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;QACzB;QACA,IAAI,CAAC,KAAK,GAAG,OAAO,IAAI;QACxB,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK;QAC1B,IAAI,CAAC,eAAe,CAAC,SAAS;QAC9B,IAAI,CAAC,eAAe,CAAC,SAAS;IAClC;IACA,SAAS;QACL,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,MAAM,GAAG;IACvM;IACA,iBAAiB,SAAS,OAAO,EAAE,SAAS;QACxC,MAAM,cAAc,OAAO,CAAC,YAAY,OAAO;QAC/C,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG;YACvB,SAAS,YAAY,OAAO;YAC5B,MAAM;gBACF,QAAQ,YAAY,KAAK,IAAI,QAAQ,KAAK;gBAC1C,gBAAgB,YAAY,KAAK,IAAI,QAAQ,KAAK;gBAClD,WAAW,YAAY,SAAS,IAAI,QAAQ,SAAS;gBACrD,SAAS,YAAY,OAAO,IAAI,QAAQ,OAAO;gBAC/C,kBAAkB;YACtB;YACA,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,QAAQ,KAAK,EAAE,YAAY,KAAK;QAC5D;IACJ;IACA,cAAc,SAAS,OAAO,EAAE,UAAU,EAAE,KAAK;QAC7C,MAAM,QAAQ,EAAE;QAChB,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,SAAS;YAAC,OAAO,IAAI;YAAE,OAAO,GAAG;YAAE,OAAO,IAAI;YAAE,OAAO,GAAG;SAAC;QACjE,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,MAAM,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,QAAQ,QAAQ,IAAI,CAAC,SAAS,KAAK,CAAC,YAAY,MAAM,CAAC;QAC1F;QACA,OAAO;IACX;IACA,QAAQ;QACJ,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,SAAS;QAC/B,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,kBAAkB,QAAQ,QAAQ;QACxC,MAAM,oBAAoB,QAAQ,UAAU;QAC5C,MAAM,eAAe,kBAAkB,OAAO,GAAG,kBAAkB,IAAI,GAAG,gBAAgB,IAAI;QAC9F,MAAM,gBAAgB;YAClB,QAAQ,aAAa,MAAM;YAC3B,gBAAgB,YAAY,CAAC,eAAe;YAC5C,WAAW,aAAa,SAAS;YACjC,SAAS,aAAa,OAAO;QACjC;QACA,MAAM,SAAS,KAAK,OAAO;QAC3B,KAAK,WAAW,GAAG,CAAC;QACpB,KAAK,SAAS,GAAG,CAAC;QAClB,KAAK,OAAO,GAAG,SAAS,MAAM,CAAC,OAAO,IAAI,EAAE,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,eAAe,MAAM,CAAC,KAAK,eAAe;QAC1G,KAAK,gBAAgB,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC,KAAK,eAAe;QAChE,KAAK,cAAc,GAAG,SAAS,CAAC,GAAG,MAAM,CAAC,KAAK,eAAe;QAC9D,IAAI,gBAAgB,OAAO,EAAE;YACzB,KAAK,SAAS,CAAC,KAAK,GAAG,KAAK,YAAY,CAAC,gBAAgB,IAAI,EAAE,KAAK,KAAK,cAAc;YACvF,KAAK,SAAS,CAAC,MAAM,GAAG,KAAK,aAAa,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,iBAAiB,OAAO,KAAK,cAAc;QACzG;QACA,IAAI,kBAAkB,OAAO,EAAE;YAC3B,KAAK,WAAW,CAAC,KAAK,GAAG,KAAK,YAAY,CAAC,kBAAkB,IAAI,EAAE,KAAK,KAAK,gBAAgB;YAC7F,KAAK,WAAW,CAAC,MAAM,GAAG,KAAK,aAAa,CAAC,KAAK,KAAK,CAAC,EAAE,EAAE,mBAAmB,MAAM,KAAK,gBAAgB;QAC9G;QACA,KAAK,IAAI;IACb;IACA,eAAe,SAAS,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK;QACtD,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM,SAAS,EAAE;QACjB,IAAI;QACJ,IAAI;QACJ,MAAM,eAAe,QAAQ,KAAK;QAClC,IAAI,aAAa,OAAO,EAAE;YACtB,KAAK,OAAO,CAAE,SAAS,IAAI;gBACvB,MAAM,WAAW,KAAK,UAAU,GAAG,QAAQ;gBAC3C,IAAI,KAAK,aAAa,GAAG,gBAAgB,GAAG,OAAO,IAAI;oBACnD;gBACJ;gBACA,kBAAkB,KAAK,iBAAiB;gBACxC,IAAI,cAAc;oBACd,IAAI,OAAO,GAAG;oBACd,IAAI;gBACR,OAAO;oBACH,IAAI,OAAO,IAAI;oBACf,IAAI;gBACR;gBACA,MAAM,QAAQ,aAAa,OAAO,aAAa,SAAS,SAAS,aAAa,QAAQ,OAAO;gBAC7F,aAAa,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;oBACxC,MAAM,aAAa,eAAe,IAAI,QAAQ,IAAI,CAAC,MAAM;gBAC7D,GAAG,MAAM,CAAC;gBACV,OAAO,SAAS,IAAI,CAAC,KAAK,GAAG,GAAG,GAAG,CAAC,CAAA,GAAA,4JAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,KAAK,CAAC,IAAI,GAAG,IAAI,CAAC;oBAC3E,OAAO;oBACP,OAAO,aAAa,QAAQ;gBAChC,GAAG,MAAM,CAAC;gBACV,OAAO,IAAI,CAAC;oBACR,MAAM;oBACN,YAAY;oBACZ,MAAM;oBACN,SAAS;oBACT,KAAK;wBACD,OAAO;wBACP,MAAM;oBACV;oBACA,SAAS;wBACL,GAAG;wBACH,GAAG;oBACP;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,aAAa,SAAS,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,EAAE,IAAI;QACtD,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,CAAE,SAAS,KAAK;YAC1B,MAAM,OAAO,MAAM,IAAI;YACvB,MAAM,QAAQ,MAAM,OAAO;YAC3B,MAAM,cAAc,MAAM,IAAI;YAC9B,MAAM,oBAAoB,MAAM,UAAU;YAC1C,IAAI,OAAO;YACX,IAAI,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,KAAK,UAAU;gBACtC,OAAO,KAAK,iBAAiB,CAAC,OAAO,MAAM,OAAO,EAAE;YACxD;YACA,IAAI,MAAM;gBACN,YAAY,IAAI,CAAC;oBACb,MAAM;oBACN,GAAG,MAAM,CAAC;oBACV,GAAG,MAAM,CAAC;gBACd;gBACA,YAAY,IAAI,CAAC,KAAK,YAAY,OAAO,IAAI,MAAM,GAAG,CAAC,IAAI,EAAE;gBAC7D,KAAK,kBAAkB,CAAC;gBACxB,kBAAkB,IAAI,CAAC,iBAAiB,YAAY,OAAO;YAC/D,OAAO;gBACH,YAAY,IAAI,CAAC;oBACb,MAAM;gBACV;gBACA,kBAAkB,IAAI,CAAC;oBACnB,GAAG;oBACH,GAAG;oBACH,OAAO;oBACP,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,MAAM;QACF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtB,YAAY;QAChB;IACJ;IACA,oBAAoB,SAAS,KAAK;QAC9B,MAAM,WAAW,MAAM,GAAG,CAAC,IAAI;QAC/B,MAAM,aAAa,MAAM,GAAG,CAAC,KAAK;QAClC,MAAM,SAAS,IAAI,CAAC,YAAY;QAChC,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,KAAK,MAAM,CAAC,SAAS,IAAI,QAAQ,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,QAAQ,aAAa,MAAM,CAAC,SAAS,IAAI,MAAM,CAAC,SAAS,GAAG;IAC3K;IACA,cAAc,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY;QAC/C,MAAM,SAAS,IAAI,CAAC,YAAY;QAChC,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,SAAS,eAAe;YAC1B;gBAAC,QAAQ,IAAI,GAAG,OAAO,IAAI;gBAAG,OAAO,GAAG;gBAAE,IAAI;gBAAG,OAAO,GAAG;aAAC;YAC5D;gBAAC,IAAI;gBAAG,OAAO,GAAG;gBAAE,QAAQ,OAAO,KAAK,EAAE,IAAI;gBAAI,OAAO,GAAG;aAAC;SAChE,GAAG;YACA;gBAAC,OAAO,IAAI;gBAAE,QAAQ,OAAO,GAAG,EAAE,IAAI;gBAAI,OAAO,IAAI;gBAAE,IAAI;aAAE;YAC7D;gBAAC,OAAO,IAAI;gBAAE,IAAI;gBAAG,OAAO,IAAI;gBAAE,QAAQ,OAAO,MAAM,EAAE,IAAI;aAAG;SACnE;QACD,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YACxB,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC;gBACV,QAAQ,MAAM,CAAC,EAAE;YACrB,GAAG,KAAK,CAAC,eAAe,MAAM,KAAK,eAAe,MAAM,OAAO,MAAM,GAAG,CAAC,IAAI,IAAI,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI;QAC/G;IACJ;IACA,mBAAmB;QACf,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI,CAAC,YAAY,GAAG;YAChB,MAAM,OAAO,IAAI;YACjB,OAAO,OAAO,KAAK;YACnB,KAAK,OAAO,GAAG;YACf,QAAQ,OAAO,MAAM;QACzB;IACJ;IACA,qBAAqB,SAAS,CAAC,EAAE,CAAC;QAC9B,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,IAAI;QACJ,IAAI;QACJ,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YAC/B,SAAS,KAAK,CAAC,EAAE,CAAC,MAAM;YACxB,IAAI,OAAO,IAAI,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,OAAO,MAAM,IAAI,GAAG;gBAChF,OAAO,KAAK,CAAC,EAAE,CAAC,QAAQ;YAC5B;QACJ;QACA,OAAO;YACH,IAAI;QACR;IACJ;IACA,MAAM,SAAS,IAAI;QACf,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,KAAK,KAAK;QACxB,MAAM,YAAY,MAAM,gBAAgB,CAAC,KAAK,CAAC,EAAE,KAAK,CAAC;QACvD,MAAM,IAAI,MAAM,cAAc;QAC9B,MAAM,aAAa,KAAK,WAAW;QACnC,MAAM,WAAW,KAAK,SAAS;QAC/B,MAAM,MAAM,CAAC,IAAI,IAAI,IAAI;QACzB,MAAM,SAAS,KAAK,OAAO;QAC3B,MAAM,IAAI,UAAU,UAAU,CAAC;QAC/B,MAAM,IAAI,UAAU,UAAU,CAAC;QAC/B,IAAI,KAAK,OAAO,IAAI,IAAI,KAAK,OAAO,KAAK,IAAI,KAAK,OAAO,GAAG,IAAI,KAAK,OAAO,MAAM,EAAE;YAChF,KAAK,eAAe,CAAC,IAAI,CAAC;gBACtB,YAAY;YAChB;YACA,KAAK,iBAAiB;YACtB,KAAK,OAAO,CAAC,IAAI,CAAC;gBACd,IAAI;gBACJ,IAAI;gBACJ,GAAG;gBACH,aAAa,KAAK,mBAAmB,CAAC,GAAG,GAAG,EAAE;YAClD;YACA,IAAI,WAAW,KAAK,EAAE;gBAClB,KAAK,WAAW,CAAC,UAAU,MAAM,EAAE,UAAU,IAAI,EAAE,WAAW,MAAM,EAAE,OAAO,wBAAwB,GAAG,GAAG,MAAM;gBACjH,KAAK,YAAY,CAAC,WAAW,KAAK,EAAE,GAAG,GAAG,KAAK;gBAC/C,KAAK,gBAAgB,CAAC,IAAI,CAAC;oBACvB,YAAY,IAAI,OAAO,GAAG;gBAC9B;YACJ;YACA,IAAI,SAAS,KAAK,EAAE;gBAChB,KAAK,WAAW,CAAC,UAAU,MAAM,EAAE,UAAU,IAAI,EAAE,SAAS,MAAM,EAAE,OAAO,wBAAwB,GAAG,GAAG,OAAO;gBAChH,KAAK,YAAY,CAAC,SAAS,KAAK,EAAE,GAAG,GAAG,KAAK;gBAC7C,KAAK,cAAc,CAAC,IAAI,CAAC;oBACrB,YAAY,IAAI,OAAO,IAAI;gBAC/B;YACJ;QACJ,OAAO;YACH,KAAK,IAAI;QACb;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/layout_manager.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/layout_manager.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isNumeric as _isNumber\r\n} from \"../../core/utils/type\";\r\nimport consts from \"../components/consts\";\r\nimport {\r\n    WrapperLayoutElement\r\n} from \"../core/layout_element\";\r\nconst {\r\n    floor: floor,\r\n    sqrt: sqrt\r\n} = Math;\r\nconst _min = Math.min;\r\nconst _max = Math.max;\r\nconst DEFAULT_INNER_RADIUS = .5;\r\nconst RADIAL_LABEL_INDENT = consts.radialLabelIndent;\r\n\r\nfunction getNearestCoord(firstCoord, secondCoord, pointCenterCoord) {\r\n    let nearestCoord;\r\n    if (pointCenterCoord < firstCoord) {\r\n        nearestCoord = firstCoord\r\n    } else if (secondCoord < pointCenterCoord) {\r\n        nearestCoord = secondCoord\r\n    } else {\r\n        nearestCoord = pointCenterCoord\r\n    }\r\n    return nearestCoord\r\n}\r\n\r\nfunction getLabelLayout(point) {\r\n    if (point._label.isVisible() && \"inside\" !== point._label.getLayoutOptions().position) {\r\n        return point._label.getBoundingRect()\r\n    }\r\n}\r\n\r\nfunction getPieRadius(series, paneCenterX, paneCenterY, accessibleRadius, minR) {\r\n    series.some((function(singleSeries) {\r\n        return singleSeries.getVisiblePoints().reduce((function(radiusIsFound, point) {\r\n            const labelBBox = getLabelLayout(point);\r\n            if (labelBBox) {\r\n                const xCoords = getNearestCoord(labelBBox.x, labelBBox.x + labelBBox.width, paneCenterX);\r\n                const yCoords = getNearestCoord(labelBBox.y, labelBBox.y + labelBBox.height, paneCenterY);\r\n                accessibleRadius = _min(_max(getLengthFromCenter(xCoords, yCoords, paneCenterX, paneCenterY) - RADIAL_LABEL_INDENT, minR), accessibleRadius);\r\n                radiusIsFound = true\r\n            }\r\n            return radiusIsFound\r\n        }), false)\r\n    }));\r\n    return accessibleRadius\r\n}\r\n\r\nfunction getSizeLabels(series) {\r\n    return series.reduce((function(res, singleSeries) {\r\n        let maxWidth = singleSeries.getVisiblePoints().reduce((function(width, point) {\r\n            const labelBBox = getLabelLayout(point);\r\n            if (labelBBox && labelBBox.width > width) {\r\n                width = labelBBox.width\r\n            }\r\n            return width\r\n        }), 0);\r\n        let rWidth = maxWidth;\r\n        if (maxWidth) {\r\n            res.outerLabelsCount++;\r\n            if (res.outerLabelsCount > 1) {\r\n                maxWidth += consts.pieLabelSpacing\r\n            }\r\n            rWidth += consts.pieLabelSpacing\r\n        }\r\n        res.sizes.push(maxWidth);\r\n        res.rSizes.push(rWidth);\r\n        res.common += maxWidth;\r\n        return res\r\n    }), {\r\n        sizes: [],\r\n        rSizes: [],\r\n        common: 0,\r\n        outerLabelsCount: 0\r\n    })\r\n}\r\n\r\nfunction correctLabelRadius(labelSizes, radius, series, canvas, averageWidthLabels, centerX) {\r\n    let curRadius;\r\n    let i;\r\n    let runningWidth = 0;\r\n    const sizes = labelSizes.sizes;\r\n    const rSizes = labelSizes.rSizes;\r\n    for (i = 0; i < series.length; i++) {\r\n        if (0 === sizes[i]) {\r\n            curRadius && (curRadius += rSizes[i - 1]);\r\n            continue\r\n        }\r\n        curRadius = floor(curRadius ? curRadius + rSizes[i - 1] : radius);\r\n        series[i].correctLabelRadius(curRadius);\r\n        runningWidth += averageWidthLabels || sizes[i];\r\n        rSizes[i] = averageWidthLabels || rSizes[i];\r\n        series[i].setVisibleArea({\r\n            left: floor(centerX - radius - runningWidth),\r\n            right: floor(canvas.width - (centerX + radius + runningWidth)),\r\n            top: canvas.top,\r\n            bottom: canvas.bottom,\r\n            width: canvas.width,\r\n            height: canvas.height\r\n        })\r\n    }\r\n}\r\n\r\nfunction getLengthFromCenter(x, y, paneCenterX, paneCenterY) {\r\n    return sqrt((x - paneCenterX) * (x - paneCenterX) + (y - paneCenterY) * (y - paneCenterY))\r\n}\r\n\r\nfunction getInnerRadius(_ref) {\r\n    let {\r\n        type: type,\r\n        innerRadius: innerRadius\r\n    } = _ref;\r\n    return \"pie\" === type ? 0 : _isNumber(innerRadius) ? Number(innerRadius) : .5\r\n}\r\n\r\nfunction LayoutManager() {}\r\n\r\nfunction getAverageLabelWidth(centerX, radius, canvas, sizeLabels) {\r\n    return (centerX - radius - RADIAL_LABEL_INDENT - canvas.left) / sizeLabels.outerLabelsCount\r\n}\r\n\r\nfunction getFullRadiusWithLabels(centerX, canvas, sizeLabels) {\r\n    return centerX - canvas.left - (sizeLabels.outerLabelsCount > 0 ? sizeLabels.common + RADIAL_LABEL_INDENT : 0)\r\n}\r\n\r\nfunction correctAvailableRadius(availableRadius, canvas, series, minR, paneCenterX, paneCenterY) {\r\n    const sizeLabels = getSizeLabels(series);\r\n    let averageWidthLabels;\r\n    const fullRadiusWithLabels = getFullRadiusWithLabels(paneCenterX, canvas, sizeLabels);\r\n    if (fullRadiusWithLabels < minR) {\r\n        availableRadius = minR;\r\n        averageWidthLabels = getAverageLabelWidth(paneCenterX, availableRadius, canvas, sizeLabels)\r\n    } else {\r\n        availableRadius = _min(getPieRadius(series, paneCenterX, paneCenterY, availableRadius, minR), fullRadiusWithLabels)\r\n    }\r\n    correctLabelRadius(sizeLabels, availableRadius + RADIAL_LABEL_INDENT, series, canvas, averageWidthLabels, paneCenterX);\r\n    return availableRadius\r\n}\r\n\r\nfunction toLayoutElementCoords(canvas) {\r\n    return new WrapperLayoutElement(null, {\r\n        x: canvas.left,\r\n        y: canvas.top,\r\n        width: canvas.width - canvas.left - canvas.right,\r\n        height: canvas.height - canvas.top - canvas.bottom\r\n    })\r\n}\r\nLayoutManager.prototype = {\r\n    constructor: LayoutManager,\r\n    setOptions: function(options) {\r\n        this._options = options\r\n    },\r\n    applyPieChartSeriesLayout: function(canvas, series, hideLayoutLabels) {\r\n        const paneSpaceHeight = canvas.height - canvas.top - canvas.bottom;\r\n        const paneSpaceWidth = canvas.width - canvas.left - canvas.right;\r\n        const paneCenterX = paneSpaceWidth / 2 + canvas.left;\r\n        const paneCenterY = paneSpaceHeight / 2 + canvas.top;\r\n        const piePercentage = this._options.piePercentage;\r\n        let availableRadius;\r\n        let minR;\r\n        if (_isNumber(piePercentage)) {\r\n            availableRadius = minR = piePercentage * _min(canvas.height, canvas.width) / 2\r\n        } else {\r\n            availableRadius = _min(paneSpaceWidth, paneSpaceHeight) / 2;\r\n            minR = this._options.minPiePercentage * availableRadius\r\n        }\r\n        if (!hideLayoutLabels) {\r\n            availableRadius = correctAvailableRadius(availableRadius, canvas, series, minR, paneCenterX, paneCenterY)\r\n        }\r\n        return {\r\n            centerX: floor(paneCenterX),\r\n            centerY: floor(paneCenterY),\r\n            radiusInner: floor(availableRadius * getInnerRadius(series[0])),\r\n            radiusOuter: floor(availableRadius)\r\n        }\r\n    },\r\n    applyEqualPieChartLayout: function(series, layout) {\r\n        const radius = layout.radius;\r\n        return {\r\n            centerX: floor(layout.x),\r\n            centerY: floor(layout.y),\r\n            radiusInner: floor(radius * getInnerRadius(series[0])),\r\n            radiusOuter: floor(radius)\r\n        }\r\n    },\r\n    correctPieLabelRadius: function(series, layout, canvas) {\r\n        const sizeLabels = getSizeLabels(series);\r\n        let averageWidthLabels;\r\n        const radius = layout.radiusOuter + RADIAL_LABEL_INDENT;\r\n        const availableLabelWidth = layout.centerX - canvas.left - radius;\r\n        if (sizeLabels.common + RADIAL_LABEL_INDENT > availableLabelWidth) {\r\n            averageWidthLabels = getAverageLabelWidth(layout.centerX, layout.radiusOuter, canvas, sizeLabels)\r\n        }\r\n        correctLabelRadius(sizeLabels, radius, series, canvas, averageWidthLabels, layout.centerX)\r\n    },\r\n    needMoreSpaceForPanesCanvas(panes, rotated, fixedSizeCallback) {\r\n        const options = this._options;\r\n        const width = options.width;\r\n        const height = options.height;\r\n        const piePercentage = options.piePercentage;\r\n        const percentageIsValid = _isNumber(piePercentage);\r\n        let needHorizontalSpace = 0;\r\n        let needVerticalSpace = 0;\r\n        panes.forEach((pane => {\r\n            const paneCanvas = pane.canvas;\r\n            const minSize = percentageIsValid ? _min(paneCanvas.width, paneCanvas.height) * piePercentage : void 0;\r\n            const paneSized = fixedSizeCallback ? fixedSizeCallback(pane) : {\r\n                width: false,\r\n                height: false\r\n            };\r\n            const needPaneHorizontalSpace = !paneSized.width ? (percentageIsValid ? minSize : width) - (paneCanvas.width - paneCanvas.left - paneCanvas.right) : 0;\r\n            const needPaneVerticalSpace = !paneSized.height ? (percentageIsValid ? minSize : height) - (paneCanvas.height - paneCanvas.top - paneCanvas.bottom) : 0;\r\n            if (rotated) {\r\n                needHorizontalSpace += needPaneHorizontalSpace > 0 ? needPaneHorizontalSpace : 0;\r\n                needVerticalSpace = _max(needPaneVerticalSpace > 0 ? needPaneVerticalSpace : 0, needVerticalSpace)\r\n            } else {\r\n                needHorizontalSpace = _max(needPaneHorizontalSpace > 0 ? needPaneHorizontalSpace : 0, needHorizontalSpace);\r\n                needVerticalSpace += needPaneVerticalSpace > 0 ? needPaneVerticalSpace : 0\r\n            }\r\n        }));\r\n        return needHorizontalSpace > 0 || needVerticalSpace > 0 ? {\r\n            width: needHorizontalSpace,\r\n            height: needVerticalSpace\r\n        } : false\r\n    },\r\n    layoutInsideLegend: function(legend, canvas) {\r\n        const layoutOptions = legend.getLayoutOptions();\r\n        if (!layoutOptions) {\r\n            return\r\n        }\r\n        const position = layoutOptions.position;\r\n        const cutSide = layoutOptions.cutSide;\r\n        const my = {\r\n            horizontal: position.horizontal,\r\n            vertical: position.vertical\r\n        };\r\n        canvas[layoutOptions.cutLayoutSide] += \"horizontal\" === layoutOptions.cutSide ? layoutOptions.width : layoutOptions.height;\r\n        my[cutSide] = {\r\n            left: \"right\",\r\n            right: \"left\",\r\n            top: \"bottom\",\r\n            bottom: \"top\",\r\n            center: \"center\"\r\n        } [my[cutSide]];\r\n        legend.position({\r\n            of: toLayoutElementCoords(canvas),\r\n            my: my,\r\n            at: position\r\n        })\r\n    }\r\n};\r\nexport {\r\n    LayoutManager\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AACA;;;;AAGA,MAAM,EACF,OAAO,KAAK,EACZ,MAAM,IAAI,EACb,GAAG;AACJ,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB,mKAAA,CAAA,UAAM,CAAC,iBAAiB;AAEpD,SAAS,gBAAgB,UAAU,EAAE,WAAW,EAAE,gBAAgB;IAC9D,IAAI;IACJ,IAAI,mBAAmB,YAAY;QAC/B,eAAe;IACnB,OAAO,IAAI,cAAc,kBAAkB;QACvC,eAAe;IACnB,OAAO;QACH,eAAe;IACnB;IACA,OAAO;AACX;AAEA,SAAS,eAAe,KAAK;IACzB,IAAI,MAAM,MAAM,CAAC,SAAS,MAAM,aAAa,MAAM,MAAM,CAAC,gBAAgB,GAAG,QAAQ,EAAE;QACnF,OAAO,MAAM,MAAM,CAAC,eAAe;IACvC;AACJ;AAEA,SAAS,aAAa,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,IAAI;IAC1E,OAAO,IAAI,CAAE,SAAS,YAAY;QAC9B,OAAO,aAAa,gBAAgB,GAAG,MAAM,CAAE,SAAS,aAAa,EAAE,KAAK;YACxE,MAAM,YAAY,eAAe;YACjC,IAAI,WAAW;gBACX,MAAM,UAAU,gBAAgB,UAAU,CAAC,EAAE,UAAU,CAAC,GAAG,UAAU,KAAK,EAAE;gBAC5E,MAAM,UAAU,gBAAgB,UAAU,CAAC,EAAE,UAAU,CAAC,GAAG,UAAU,MAAM,EAAE;gBAC7E,mBAAmB,KAAK,KAAK,oBAAoB,SAAS,SAAS,aAAa,eAAe,qBAAqB,OAAO;gBAC3H,gBAAgB;YACpB;YACA,OAAO;QACX,GAAI;IACR;IACA,OAAO;AACX;AAEA,SAAS,cAAc,MAAM;IACzB,OAAO,OAAO,MAAM,CAAE,SAAS,GAAG,EAAE,YAAY;QAC5C,IAAI,WAAW,aAAa,gBAAgB,GAAG,MAAM,CAAE,SAAS,KAAK,EAAE,KAAK;YACxE,MAAM,YAAY,eAAe;YACjC,IAAI,aAAa,UAAU,KAAK,GAAG,OAAO;gBACtC,QAAQ,UAAU,KAAK;YAC3B;YACA,OAAO;QACX,GAAI;QACJ,IAAI,SAAS;QACb,IAAI,UAAU;YACV,IAAI,gBAAgB;YACpB,IAAI,IAAI,gBAAgB,GAAG,GAAG;gBAC1B,YAAY,mKAAA,CAAA,UAAM,CAAC,eAAe;YACtC;YACA,UAAU,mKAAA,CAAA,UAAM,CAAC,eAAe;QACpC;QACA,IAAI,KAAK,CAAC,IAAI,CAAC;QACf,IAAI,MAAM,CAAC,IAAI,CAAC;QAChB,IAAI,MAAM,IAAI;QACd,OAAO;IACX,GAAI;QACA,OAAO,EAAE;QACT,QAAQ,EAAE;QACV,QAAQ;QACR,kBAAkB;IACtB;AACJ;AAEA,SAAS,mBAAmB,UAAU,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,kBAAkB,EAAE,OAAO;IACvF,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe;IACnB,MAAM,QAAQ,WAAW,KAAK;IAC9B,MAAM,SAAS,WAAW,MAAM;IAChC,IAAK,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;QAChC,IAAI,MAAM,KAAK,CAAC,EAAE,EAAE;YAChB,aAAa,CAAC,aAAa,MAAM,CAAC,IAAI,EAAE;YACxC;QACJ;QACA,YAAY,MAAM,YAAY,YAAY,MAAM,CAAC,IAAI,EAAE,GAAG;QAC1D,MAAM,CAAC,EAAE,CAAC,kBAAkB,CAAC;QAC7B,gBAAgB,sBAAsB,KAAK,CAAC,EAAE;QAC9C,MAAM,CAAC,EAAE,GAAG,sBAAsB,MAAM,CAAC,EAAE;QAC3C,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YACrB,MAAM,MAAM,UAAU,SAAS;YAC/B,OAAO,MAAM,OAAO,KAAK,GAAG,CAAC,UAAU,SAAS,YAAY;YAC5D,KAAK,OAAO,GAAG;YACf,QAAQ,OAAO,MAAM;YACrB,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;QACzB;IACJ;AACJ;AAEA,SAAS,oBAAoB,CAAC,EAAE,CAAC,EAAE,WAAW,EAAE,WAAW;IACvD,OAAO,KAAK,CAAC,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW,IAAI,CAAC,IAAI,WAAW;AAC5F;AAEA,SAAS,eAAe,IAAI;IACxB,IAAI,EACA,MAAM,IAAI,EACV,aAAa,WAAW,EAC3B,GAAG;IACJ,OAAO,UAAU,OAAO,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe,OAAO,eAAe;AAC/E;AAEA,SAAS,iBAAiB;AAE1B,SAAS,qBAAqB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,UAAU;IAC7D,OAAO,CAAC,UAAU,SAAS,sBAAsB,OAAO,IAAI,IAAI,WAAW,gBAAgB;AAC/F;AAEA,SAAS,wBAAwB,OAAO,EAAE,MAAM,EAAE,UAAU;IACxD,OAAO,UAAU,OAAO,IAAI,GAAG,CAAC,WAAW,gBAAgB,GAAG,IAAI,WAAW,MAAM,GAAG,sBAAsB,CAAC;AACjH;AAEA,SAAS,uBAAuB,eAAe,EAAE,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW;IAC3F,MAAM,aAAa,cAAc;IACjC,IAAI;IACJ,MAAM,uBAAuB,wBAAwB,aAAa,QAAQ;IAC1E,IAAI,uBAAuB,MAAM;QAC7B,kBAAkB;QAClB,qBAAqB,qBAAqB,aAAa,iBAAiB,QAAQ;IACpF,OAAO;QACH,kBAAkB,KAAK,aAAa,QAAQ,aAAa,aAAa,iBAAiB,OAAO;IAClG;IACA,mBAAmB,YAAY,kBAAkB,qBAAqB,QAAQ,QAAQ,oBAAoB;IAC1G,OAAO;AACX;AAEA,SAAS,sBAAsB,MAAM;IACjC,OAAO,IAAI,qKAAA,CAAA,uBAAoB,CAAC,MAAM;QAClC,GAAG,OAAO,IAAI;QACd,GAAG,OAAO,GAAG;QACb,OAAO,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK;QAChD,QAAQ,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM;IACtD;AACJ;AACA,cAAc,SAAS,GAAG;IACtB,aAAa;IACb,YAAY,SAAS,OAAO;QACxB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,2BAA2B,SAAS,MAAM,EAAE,MAAM,EAAE,gBAAgB;QAChE,MAAM,kBAAkB,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM;QAClE,MAAM,iBAAiB,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK;QAChE,MAAM,cAAc,iBAAiB,IAAI,OAAO,IAAI;QACpD,MAAM,cAAc,kBAAkB,IAAI,OAAO,GAAG;QACpD,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,aAAa;QACjD,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;YAC1B,kBAAkB,OAAO,gBAAgB,KAAK,OAAO,MAAM,EAAE,OAAO,KAAK,IAAI;QACjF,OAAO;YACH,kBAAkB,KAAK,gBAAgB,mBAAmB;YAC1D,OAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG;QAC5C;QACA,IAAI,CAAC,kBAAkB;YACnB,kBAAkB,uBAAuB,iBAAiB,QAAQ,QAAQ,MAAM,aAAa;QACjG;QACA,OAAO;YACH,SAAS,MAAM;YACf,SAAS,MAAM;YACf,aAAa,MAAM,kBAAkB,eAAe,MAAM,CAAC,EAAE;YAC7D,aAAa,MAAM;QACvB;IACJ;IACA,0BAA0B,SAAS,MAAM,EAAE,MAAM;QAC7C,MAAM,SAAS,OAAO,MAAM;QAC5B,OAAO;YACH,SAAS,MAAM,OAAO,CAAC;YACvB,SAAS,MAAM,OAAO,CAAC;YACvB,aAAa,MAAM,SAAS,eAAe,MAAM,CAAC,EAAE;YACpD,aAAa,MAAM;QACvB;IACJ;IACA,uBAAuB,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM;QAClD,MAAM,aAAa,cAAc;QACjC,IAAI;QACJ,MAAM,SAAS,OAAO,WAAW,GAAG;QACpC,MAAM,sBAAsB,OAAO,OAAO,GAAG,OAAO,IAAI,GAAG;QAC3D,IAAI,WAAW,MAAM,GAAG,sBAAsB,qBAAqB;YAC/D,qBAAqB,qBAAqB,OAAO,OAAO,EAAE,OAAO,WAAW,EAAE,QAAQ;QAC1F;QACA,mBAAmB,YAAY,QAAQ,QAAQ,QAAQ,oBAAoB,OAAO,OAAO;IAC7F;IACA,6BAA4B,KAAK,EAAE,OAAO,EAAE,iBAAiB;QACzD,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,QAAQ,QAAQ,KAAK;QAC3B,MAAM,SAAS,QAAQ,MAAM;QAC7B,MAAM,gBAAgB,QAAQ,aAAa;QAC3C,MAAM,oBAAoB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;QACpC,IAAI,sBAAsB;QAC1B,IAAI,oBAAoB;QACxB,MAAM,OAAO,CAAE,CAAA;YACX,MAAM,aAAa,KAAK,MAAM;YAC9B,MAAM,UAAU,oBAAoB,KAAK,WAAW,KAAK,EAAE,WAAW,MAAM,IAAI,gBAAgB,KAAK;YACrG,MAAM,YAAY,oBAAoB,kBAAkB,QAAQ;gBAC5D,OAAO;gBACP,QAAQ;YACZ;YACA,MAAM,0BAA0B,CAAC,UAAU,KAAK,GAAG,CAAC,oBAAoB,UAAU,KAAK,IAAI,CAAC,WAAW,KAAK,GAAG,WAAW,IAAI,GAAG,WAAW,KAAK,IAAI;YACrJ,MAAM,wBAAwB,CAAC,UAAU,MAAM,GAAG,CAAC,oBAAoB,UAAU,MAAM,IAAI,CAAC,WAAW,MAAM,GAAG,WAAW,GAAG,GAAG,WAAW,MAAM,IAAI;YACtJ,IAAI,SAAS;gBACT,uBAAuB,0BAA0B,IAAI,0BAA0B;gBAC/E,oBAAoB,KAAK,wBAAwB,IAAI,wBAAwB,GAAG;YACpF,OAAO;gBACH,sBAAsB,KAAK,0BAA0B,IAAI,0BAA0B,GAAG;gBACtF,qBAAqB,wBAAwB,IAAI,wBAAwB;YAC7E;QACJ;QACA,OAAO,sBAAsB,KAAK,oBAAoB,IAAI;YACtD,OAAO;YACP,QAAQ;QACZ,IAAI;IACR;IACA,oBAAoB,SAAS,MAAM,EAAE,MAAM;QACvC,MAAM,gBAAgB,OAAO,gBAAgB;QAC7C,IAAI,CAAC,eAAe;YAChB;QACJ;QACA,MAAM,WAAW,cAAc,QAAQ;QACvC,MAAM,UAAU,cAAc,OAAO;QACrC,MAAM,KAAK;YACP,YAAY,SAAS,UAAU;YAC/B,UAAU,SAAS,QAAQ;QAC/B;QACA,MAAM,CAAC,cAAc,aAAa,CAAC,IAAI,iBAAiB,cAAc,OAAO,GAAG,cAAc,KAAK,GAAG,cAAc,MAAM;QAC1H,EAAE,CAAC,QAAQ,GAAG,CAAA;YACV,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;YACR,QAAQ;QACZ,CAAA,CAAE,CAAC,EAAE,CAAC,QAAQ,CAAC;QACf,OAAO,QAAQ,CAAC;YACZ,IAAI,sBAAsB;YAC1B,IAAI;YACJ,IAAI;QACR;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/multi_axes_synchronizer.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/multi_axes_synchronizer.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    debug\r\n} from \"../../core/utils/console\";\r\nimport {\r\n    isDefined,\r\n    isNumeric\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    getLogExt,\r\n    raiseToExt\r\n} from \"../core/utils\";\r\nimport {\r\n    adjust\r\n} from \"../../core/utils/math\";\r\nconst _math = Math;\r\nconst _floor = _math.floor;\r\nconst _max = _math.max;\r\nconst _abs = _math.abs;\r\n\r\nfunction getValueAxesPerPanes(valueAxes) {\r\n    const result = {};\r\n    valueAxes.forEach((axis => {\r\n        const pane = axis.pane;\r\n        if (!result[pane]) {\r\n            result[pane] = []\r\n        }\r\n        result[pane].push(axis)\r\n    }));\r\n    return result\r\n}\r\nconst linearConverter = br => ({\r\n    transform: function(v, b) {\r\n        return adjust(getLogExt(v, b, br.allowNegatives, br.linearThreshold))\r\n    },\r\n    getTicks: function(interval, tickValues, base) {\r\n        const ticks = [];\r\n        let tick = this.transform(tickValues[0], base);\r\n        while (ticks.length < tickValues.length) {\r\n            ticks.push(tick);\r\n            tick = adjust(tick + interval)\r\n        }\r\n        return ticks\r\n    }\r\n});\r\nconst logConverter = br => ({\r\n    transform: function(v, b) {\r\n        return adjust(raiseToExt(v, b, br.allowNegatives, br.linearThreshold))\r\n    },\r\n    getTicks: function(interval, tickValues, base) {\r\n        const ticks = [];\r\n        let tick;\r\n        for (let i = 0; i < tickValues.length; i += 1) {\r\n            tick = this.transform(tickValues[i], base);\r\n            ticks.push(tick)\r\n        }\r\n        return ticks\r\n    }\r\n});\r\n\r\nfunction convertAxisInfo(axisInfo, converter) {\r\n    if (!axisInfo.isLogarithmic) {\r\n        return\r\n    }\r\n    const base = axisInfo.logarithmicBase;\r\n    const tickValues = axisInfo.tickValues;\r\n    axisInfo.minValue = converter.transform(axisInfo.minValue, base);\r\n    axisInfo.oldMinValue = converter.transform(axisInfo.oldMinValue, base);\r\n    axisInfo.maxValue = converter.transform(axisInfo.maxValue, base);\r\n    axisInfo.oldMaxValue = converter.transform(axisInfo.oldMaxValue, base);\r\n    axisInfo.tickInterval = _math.round(axisInfo.tickInterval);\r\n    if (axisInfo.tickInterval < 1) {\r\n        axisInfo.tickInterval = 1\r\n    }\r\n    const ticks = converter.getTicks(axisInfo.tickInterval, tickValues, base);\r\n    ticks.tickInterval = axisInfo.tickInterval;\r\n    axisInfo.tickValues = ticks\r\n}\r\n\r\nfunction populateAxesInfo(axes) {\r\n    return axes.reduce((function(result, axis) {\r\n        const ticksValues = axis.getTicksValues();\r\n        const majorTicks = ticksValues.majorTicksValues;\r\n        const options = axis.getOptions();\r\n        const businessRange = axis.getTranslator().getBusinessRange();\r\n        const visibleArea = axis.getVisibleArea();\r\n        let axisInfo;\r\n        let tickInterval = axis._tickInterval;\r\n        const synchronizedValue = options.synchronizedValue;\r\n        const action = axis.getViewport().action;\r\n        if (majorTicks && majorTicks.length > 0 && isNumeric(majorTicks[0]) && \"discrete\" !== options.type && !businessRange.isEmpty() && !(businessRange.breaks && businessRange.breaks.length) && \"zoom\" !== action && \"pan\" !== action) {\r\n            axis.applyMargins();\r\n            const startValue = axis.getTranslator().from(visibleArea[0]);\r\n            const endValue = axis.getTranslator().from(visibleArea[1]);\r\n            let minValue = startValue < endValue ? startValue : endValue;\r\n            let maxValue = startValue < endValue ? endValue : startValue;\r\n            if (minValue === maxValue && isDefined(synchronizedValue)) {\r\n                tickInterval = _abs(majorTicks[0] - synchronizedValue) || 1;\r\n                minValue = majorTicks[0] - tickInterval;\r\n                maxValue = majorTicks[0] + tickInterval\r\n            }\r\n            axisInfo = {\r\n                axis: axis,\r\n                isLogarithmic: \"logarithmic\" === options.type,\r\n                logarithmicBase: businessRange.base,\r\n                tickValues: majorTicks,\r\n                minorValues: ticksValues.minorTicksValues,\r\n                minorTickInterval: axis._minorTickInterval,\r\n                minValue: minValue,\r\n                oldMinValue: minValue,\r\n                maxValue: maxValue,\r\n                oldMaxValue: maxValue,\r\n                inverted: businessRange.invert,\r\n                tickInterval: tickInterval,\r\n                synchronizedValue: synchronizedValue\r\n            };\r\n            convertAxisInfo(axisInfo, linearConverter(axis.getTranslator().getBusinessRange()));\r\n            result.push(axisInfo)\r\n        }\r\n        return result\r\n    }), [])\r\n}\r\n\r\nfunction updateTickValues(axesInfo) {\r\n    const maxTicksCount = axesInfo.reduce(((max, axisInfo) => _max(max, axisInfo.tickValues.length)), 0);\r\n    axesInfo.forEach((axisInfo => {\r\n        let ticksMultiplier;\r\n        let ticksCount;\r\n        let additionalStartTicksCount = 0;\r\n        const synchronizedValue = axisInfo.synchronizedValue;\r\n        const tickValues = axisInfo.tickValues;\r\n        const tickInterval = axisInfo.tickInterval;\r\n        if (isDefined(synchronizedValue)) {\r\n            axisInfo.baseTickValue = axisInfo.invertedBaseTickValue = synchronizedValue;\r\n            axisInfo.tickValues = [axisInfo.baseTickValue]\r\n        } else {\r\n            if (tickValues.length > 1 && tickInterval) {\r\n                ticksMultiplier = _floor((maxTicksCount + 1) / tickValues.length);\r\n                ticksCount = ticksMultiplier > 1 ? _floor((maxTicksCount + 1) / ticksMultiplier) : maxTicksCount;\r\n                additionalStartTicksCount = _floor((ticksCount - tickValues.length) / 2);\r\n                while (additionalStartTicksCount > 0 && 0 !== tickValues[0]) {\r\n                    tickValues.unshift(adjust(tickValues[0] - tickInterval));\r\n                    additionalStartTicksCount--\r\n                }\r\n                while (tickValues.length < ticksCount) {\r\n                    tickValues.push(adjust(tickValues[tickValues.length - 1] + tickInterval))\r\n                }\r\n                axisInfo.tickInterval = tickInterval / ticksMultiplier\r\n            }\r\n            axisInfo.baseTickValue = tickValues[0];\r\n            axisInfo.invertedBaseTickValue = tickValues[tickValues.length - 1]\r\n        }\r\n    }))\r\n}\r\n\r\nfunction getAxisRange(axisInfo) {\r\n    return axisInfo.maxValue - axisInfo.minValue || 1\r\n}\r\n\r\nfunction getMainAxisInfo(axesInfo) {\r\n    for (let i = 0; i < axesInfo.length; i++) {\r\n        if (!axesInfo[i].stubData) {\r\n            return axesInfo[i]\r\n        }\r\n    }\r\n    return null\r\n}\r\n\r\nfunction correctMinMaxValues(axesInfo) {\r\n    const mainAxisInfo = getMainAxisInfo(axesInfo);\r\n    const mainAxisInfoTickInterval = mainAxisInfo.tickInterval;\r\n    axesInfo.forEach((axisInfo => {\r\n        let scale;\r\n        let move;\r\n        let mainAxisBaseValueOffset;\r\n        let valueFromAxisInfo;\r\n        if (axisInfo !== mainAxisInfo) {\r\n            if (mainAxisInfoTickInterval && axisInfo.tickInterval) {\r\n                if (axisInfo.stubData && isDefined(axisInfo.synchronizedValue)) {\r\n                    axisInfo.oldMinValue = axisInfo.minValue = axisInfo.baseTickValue - (mainAxisInfo.baseTickValue - mainAxisInfo.minValue) / mainAxisInfoTickInterval * axisInfo.tickInterval;\r\n                    axisInfo.oldMaxValue = axisInfo.maxValue = axisInfo.baseTickValue - (mainAxisInfo.baseTickValue - mainAxisInfo.maxValue) / mainAxisInfoTickInterval * axisInfo.tickInterval\r\n                }\r\n                scale = mainAxisInfoTickInterval / getAxisRange(mainAxisInfo) / axisInfo.tickInterval * getAxisRange(axisInfo);\r\n                axisInfo.maxValue = axisInfo.minValue + getAxisRange(axisInfo) / scale\r\n            }\r\n            if (mainAxisInfo.inverted && !axisInfo.inverted || !mainAxisInfo.inverted && axisInfo.inverted) {\r\n                mainAxisBaseValueOffset = mainAxisInfo.maxValue - mainAxisInfo.invertedBaseTickValue\r\n            } else {\r\n                mainAxisBaseValueOffset = mainAxisInfo.baseTickValue - mainAxisInfo.minValue\r\n            }\r\n            valueFromAxisInfo = getAxisRange(axisInfo);\r\n            move = (mainAxisBaseValueOffset / getAxisRange(mainAxisInfo) - (axisInfo.baseTickValue - axisInfo.minValue) / valueFromAxisInfo) * valueFromAxisInfo;\r\n            axisInfo.minValue -= move;\r\n            axisInfo.maxValue -= move\r\n        }\r\n    }))\r\n}\r\n\r\nfunction calculatePaddings(axesInfo) {\r\n    let minPadding;\r\n    let maxPadding;\r\n    let startPadding = 0;\r\n    let endPadding = 0;\r\n    axesInfo.forEach((axisInfo => {\r\n        const inverted = axisInfo.inverted;\r\n        minPadding = axisInfo.minValue > axisInfo.oldMinValue ? (axisInfo.minValue - axisInfo.oldMinValue) / getAxisRange(axisInfo) : 0;\r\n        maxPadding = axisInfo.maxValue < axisInfo.oldMaxValue ? (axisInfo.oldMaxValue - axisInfo.maxValue) / getAxisRange(axisInfo) : 0;\r\n        startPadding = _max(startPadding, inverted ? maxPadding : minPadding);\r\n        endPadding = _max(endPadding, inverted ? minPadding : maxPadding)\r\n    }));\r\n    return {\r\n        start: startPadding,\r\n        end: endPadding\r\n    }\r\n}\r\n\r\nfunction correctMinMaxValuesByPaddings(axesInfo, paddings) {\r\n    axesInfo.forEach((info => {\r\n        const range = getAxisRange(info);\r\n        const inverted = info.inverted;\r\n        info.minValue = adjust(info.minValue - paddings[inverted ? \"end\" : \"start\"] * range);\r\n        info.maxValue = adjust(info.maxValue + paddings[inverted ? \"start\" : \"end\"] * range)\r\n    }))\r\n}\r\n\r\nfunction updateTickValuesIfSynchronizedValueUsed(axesInfo) {\r\n    let hasSynchronizedValue = false;\r\n    axesInfo.forEach((info => {\r\n        hasSynchronizedValue = hasSynchronizedValue || isDefined(info.synchronizedValue)\r\n    }));\r\n    axesInfo.forEach((info => {\r\n        const tickInterval = info.tickInterval;\r\n        const tickValues = info.tickValues;\r\n        const maxValue = info.maxValue;\r\n        const minValue = info.minValue;\r\n        let tick;\r\n        if (hasSynchronizedValue && tickInterval) {\r\n            while ((tick = adjust(tickValues[0] - tickInterval)) >= minValue) {\r\n                tickValues.unshift(tick)\r\n            }\r\n            tick = tickValues[tickValues.length - 1];\r\n            while ((tick = adjust(tick + tickInterval)) <= maxValue) {\r\n                tickValues.push(tick)\r\n            }\r\n        }\r\n        while (tickValues[0] + tickInterval / 10 < minValue) {\r\n            tickValues.shift()\r\n        }\r\n        while (tickValues[tickValues.length - 1] - tickInterval / 10 > maxValue) {\r\n            tickValues.pop()\r\n        }\r\n    }))\r\n}\r\n\r\nfunction applyMinMaxValues(axesInfo) {\r\n    axesInfo.forEach((info => {\r\n        const axis = info.axis;\r\n        const range = axis.getTranslator().getBusinessRange();\r\n        if (range.min === range.minVisible) {\r\n            range.min = info.minValue\r\n        }\r\n        if (range.max === range.maxVisible) {\r\n            range.max = info.maxValue\r\n        }\r\n        range.minVisible = info.minValue;\r\n        range.maxVisible = info.maxValue;\r\n        if (range.min > range.minVisible) {\r\n            range.min = range.minVisible\r\n        }\r\n        if (range.max < range.maxVisible) {\r\n            range.max = range.maxVisible\r\n        }\r\n        axis.getTranslator().updateBusinessRange(range);\r\n        axis.setTicks({\r\n            majorTicks: info.tickValues,\r\n            minorTicks: info.minorValues\r\n        })\r\n    }))\r\n}\r\n\r\nfunction correctAfterSynchronize(axesInfo) {\r\n    const invalidAxisInfo = [];\r\n    let correctValue;\r\n    axesInfo.forEach((info => {\r\n        if (info.oldMaxValue - info.oldMinValue === 0) {\r\n            invalidAxisInfo.push(info)\r\n        } else if (!isDefined(correctValue) && !isDefined(info.synchronizedValue)) {\r\n            correctValue = _abs((info.maxValue - info.minValue) / (info.tickValues[_floor(info.tickValues.length / 2)] - info.minValue || info.maxValue))\r\n        }\r\n    }));\r\n    if (!isDefined(correctValue)) {\r\n        return\r\n    }\r\n    invalidAxisInfo.forEach((info => {\r\n        const firstTick = info.tickValues[0];\r\n        const correctedTick = firstTick * correctValue;\r\n        if (firstTick > 0) {\r\n            info.maxValue = correctedTick;\r\n            info.minValue = 0\r\n        } else if (firstTick < 0) {\r\n            info.minValue = correctedTick;\r\n            info.maxValue = 0\r\n        }\r\n    }))\r\n}\r\n\r\nfunction updateMinorTicks(axesInfo) {\r\n    axesInfo.forEach((function(axisInfo) {\r\n        if (!axisInfo.minorTickInterval) {\r\n            return\r\n        }\r\n        const ticks = [];\r\n        const interval = axisInfo.minorTickInterval;\r\n        const tickCount = axisInfo.tickInterval / interval - 1;\r\n        for (let i = 1; i < axisInfo.tickValues.length; i++) {\r\n            let tick = axisInfo.tickValues[i - 1];\r\n            for (let j = 0; j < tickCount; j++) {\r\n                tick += interval;\r\n                ticks.push(tick)\r\n            }\r\n        }\r\n        axisInfo.minorValues = ticks\r\n    }))\r\n}\r\n\r\nfunction allAxesValuesOnSameSideFromZero(axesInfo) {\r\n    let allPositive = true;\r\n    let allNegative = true;\r\n    axesInfo.forEach((axis => {\r\n        if (axis.oldMinValue > 0 || axis.oldMaxValue > 0) {\r\n            allNegative = false\r\n        }\r\n        if (axis.oldMinValue < 0 || axis.oldMaxValue < 0) {\r\n            allPositive = false\r\n        }\r\n    }));\r\n    return allPositive || allNegative\r\n}\r\n\r\nfunction correctPaddings(axesInfo, paddings) {\r\n    if (!allAxesValuesOnSameSideFromZero(axesInfo)) {\r\n        return paddings\r\n    }\r\n    return axesInfo.reduce(((prev, info) => {\r\n        const inverted = info.inverted;\r\n        const {\r\n            start: start,\r\n            end: end\r\n        } = info.axis.getCorrectedValuesToZero(info.minValue, info.maxValue);\r\n        if (isDefined(start) || isDefined(end)) {\r\n            return inverted ? {\r\n                start: prev.start,\r\n                end: Math.min(prev.end, end)\r\n            } : {\r\n                start: Math.min(prev.start, start),\r\n                end: prev.end\r\n            }\r\n        }\r\n        return prev\r\n    }), paddings)\r\n}\r\nconst multiAxesSynchronizer = {\r\n    synchronize: function(valueAxes) {\r\n        each(getValueAxesPerPanes(valueAxes), (function(_, axes) {\r\n            let axesInfo;\r\n            let paddings;\r\n            if (axes.length > 1) {\r\n                axesInfo = populateAxesInfo(axes);\r\n                if (axesInfo.length < 2 || !getMainAxisInfo(axesInfo)) {\r\n                    return\r\n                }\r\n                updateTickValues(axesInfo);\r\n                correctMinMaxValues(axesInfo);\r\n                paddings = calculatePaddings(axesInfo);\r\n                paddings = correctPaddings(axesInfo, paddings);\r\n                correctMinMaxValuesByPaddings(axesInfo, paddings);\r\n                correctAfterSynchronize(axesInfo);\r\n                updateTickValuesIfSynchronizedValueUsed(axesInfo);\r\n                updateMinorTicks(axesInfo);\r\n                axesInfo.forEach((info => {\r\n                    convertAxisInfo(info, logConverter(info.axis.getTranslator().getBusinessRange()))\r\n                }));\r\n                applyMinMaxValues(axesInfo)\r\n            }\r\n        }))\r\n    }\r\n};\r\nexport default multiAxesSynchronizer;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAGA;AAAA;AAIA;AAAA;AAGA;AAIA;AAAA;;;;;;AAGA,MAAM,QAAQ;AACd,MAAM,SAAS,MAAM,KAAK;AAC1B,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,OAAO,MAAM,GAAG;AAEtB,SAAS,qBAAqB,SAAS;IACnC,MAAM,SAAS,CAAC;IAChB,UAAU,OAAO,CAAE,CAAA;QACf,MAAM,OAAO,KAAK,IAAI;QACtB,IAAI,CAAC,MAAM,CAAC,KAAK,EAAE;YACf,MAAM,CAAC,KAAK,GAAG,EAAE;QACrB;QACA,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC;IACtB;IACA,OAAO;AACX;AACA,MAAM,kBAAkB,CAAA,KAAM,CAAC;QAC3B,WAAW,SAAS,CAAC,EAAE,CAAC;YACpB,OAAO,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,YAAS,AAAD,EAAE,GAAG,GAAG,GAAG,cAAc,EAAE,GAAG,eAAe;QACvE;QACA,UAAU,SAAS,QAAQ,EAAE,UAAU,EAAE,IAAI;YACzC,MAAM,QAAQ,EAAE;YAChB,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE;YACzC,MAAO,MAAM,MAAM,GAAG,WAAW,MAAM,CAAE;gBACrC,MAAM,IAAI,CAAC;gBACX,OAAO,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,OAAO;YACzB;YACA,OAAO;QACX;IACJ,CAAC;AACD,MAAM,eAAe,CAAA,KAAM,CAAC;QACxB,WAAW,SAAS,CAAC,EAAE,CAAC;YACpB,OAAO,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,GAAG,GAAG,GAAG,cAAc,EAAE,GAAG,eAAe;QACxE;QACA,UAAU,SAAS,QAAQ,EAAE,UAAU,EAAE,IAAI;YACzC,MAAM,QAAQ,EAAE;YAChB,IAAI;YACJ,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,KAAK,EAAG;gBAC3C,OAAO,IAAI,CAAC,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE;gBACrC,MAAM,IAAI,CAAC;YACf;YACA,OAAO;QACX;IACJ,CAAC;AAED,SAAS,gBAAgB,QAAQ,EAAE,SAAS;IACxC,IAAI,CAAC,SAAS,aAAa,EAAE;QACzB;IACJ;IACA,MAAM,OAAO,SAAS,eAAe;IACrC,MAAM,aAAa,SAAS,UAAU;IACtC,SAAS,QAAQ,GAAG,UAAU,SAAS,CAAC,SAAS,QAAQ,EAAE;IAC3D,SAAS,WAAW,GAAG,UAAU,SAAS,CAAC,SAAS,WAAW,EAAE;IACjE,SAAS,QAAQ,GAAG,UAAU,SAAS,CAAC,SAAS,QAAQ,EAAE;IAC3D,SAAS,WAAW,GAAG,UAAU,SAAS,CAAC,SAAS,WAAW,EAAE;IACjE,SAAS,YAAY,GAAG,MAAM,KAAK,CAAC,SAAS,YAAY;IACzD,IAAI,SAAS,YAAY,GAAG,GAAG;QAC3B,SAAS,YAAY,GAAG;IAC5B;IACA,MAAM,QAAQ,UAAU,QAAQ,CAAC,SAAS,YAAY,EAAE,YAAY;IACpE,MAAM,YAAY,GAAG,SAAS,YAAY;IAC1C,SAAS,UAAU,GAAG;AAC1B;AAEA,SAAS,iBAAiB,IAAI;IAC1B,OAAO,KAAK,MAAM,CAAE,SAAS,MAAM,EAAE,IAAI;QACrC,MAAM,cAAc,KAAK,cAAc;QACvC,MAAM,aAAa,YAAY,gBAAgB;QAC/C,MAAM,UAAU,KAAK,UAAU;QAC/B,MAAM,gBAAgB,KAAK,aAAa,GAAG,gBAAgB;QAC3D,MAAM,cAAc,KAAK,cAAc;QACvC,IAAI;QACJ,IAAI,eAAe,KAAK,aAAa;QACrC,MAAM,oBAAoB,QAAQ,iBAAiB;QACnD,MAAM,SAAS,KAAK,WAAW,GAAG,MAAM;QACxC,IAAI,cAAc,WAAW,MAAM,GAAG,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAC,EAAE,KAAK,eAAe,QAAQ,IAAI,IAAI,CAAC,cAAc,OAAO,MAAM,CAAC,CAAC,cAAc,MAAM,IAAI,cAAc,MAAM,CAAC,MAAM,KAAK,WAAW,UAAU,UAAU,QAAQ;YAC/N,KAAK,YAAY;YACjB,MAAM,aAAa,KAAK,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE;YAC3D,MAAM,WAAW,KAAK,aAAa,GAAG,IAAI,CAAC,WAAW,CAAC,EAAE;YACzD,IAAI,WAAW,aAAa,WAAW,aAAa;YACpD,IAAI,WAAW,aAAa,WAAW,WAAW;YAClD,IAAI,aAAa,YAAY,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;gBACvD,eAAe,KAAK,UAAU,CAAC,EAAE,GAAG,sBAAsB;gBAC1D,WAAW,UAAU,CAAC,EAAE,GAAG;gBAC3B,WAAW,UAAU,CAAC,EAAE,GAAG;YAC/B;YACA,WAAW;gBACP,MAAM;gBACN,eAAe,kBAAkB,QAAQ,IAAI;gBAC7C,iBAAiB,cAAc,IAAI;gBACnC,YAAY;gBACZ,aAAa,YAAY,gBAAgB;gBACzC,mBAAmB,KAAK,kBAAkB;gBAC1C,UAAU;gBACV,aAAa;gBACb,UAAU;gBACV,aAAa;gBACb,UAAU,cAAc,MAAM;gBAC9B,cAAc;gBACd,mBAAmB;YACvB;YACA,gBAAgB,UAAU,gBAAgB,KAAK,aAAa,GAAG,gBAAgB;YAC/E,OAAO,IAAI,CAAC;QAChB;QACA,OAAO;IACX,GAAI,EAAE;AACV;AAEA,SAAS,iBAAiB,QAAQ;IAC9B,MAAM,gBAAgB,SAAS,MAAM,CAAE,CAAC,KAAK,WAAa,KAAK,KAAK,SAAS,UAAU,CAAC,MAAM,GAAI;IAClG,SAAS,OAAO,CAAE,CAAA;QACd,IAAI;QACJ,IAAI;QACJ,IAAI,4BAA4B;QAChC,MAAM,oBAAoB,SAAS,iBAAiB;QACpD,MAAM,aAAa,SAAS,UAAU;QACtC,MAAM,eAAe,SAAS,YAAY;QAC1C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;YAC9B,SAAS,aAAa,GAAG,SAAS,qBAAqB,GAAG;YAC1D,SAAS,UAAU,GAAG;gBAAC,SAAS,aAAa;aAAC;QAClD,OAAO;YACH,IAAI,WAAW,MAAM,GAAG,KAAK,cAAc;gBACvC,kBAAkB,OAAO,CAAC,gBAAgB,CAAC,IAAI,WAAW,MAAM;gBAChE,aAAa,kBAAkB,IAAI,OAAO,CAAC,gBAAgB,CAAC,IAAI,mBAAmB;gBACnF,4BAA4B,OAAO,CAAC,aAAa,WAAW,MAAM,IAAI;gBACtE,MAAO,4BAA4B,KAAK,MAAM,UAAU,CAAC,EAAE,CAAE;oBACzD,WAAW,OAAO,CAAC,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG;oBAC1C;gBACJ;gBACA,MAAO,WAAW,MAAM,GAAG,WAAY;oBACnC,WAAW,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,GAAG;gBAC/D;gBACA,SAAS,YAAY,GAAG,eAAe;YAC3C;YACA,SAAS,aAAa,GAAG,UAAU,CAAC,EAAE;YACtC,SAAS,qBAAqB,GAAG,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QACtE;IACJ;AACJ;AAEA,SAAS,aAAa,QAAQ;IAC1B,OAAO,SAAS,QAAQ,GAAG,SAAS,QAAQ,IAAI;AACpD;AAEA,SAAS,gBAAgB,QAAQ;IAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACtC,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,QAAQ,EAAE;YACvB,OAAO,QAAQ,CAAC,EAAE;QACtB;IACJ;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,QAAQ;IACjC,MAAM,eAAe,gBAAgB;IACrC,MAAM,2BAA2B,aAAa,YAAY;IAC1D,SAAS,OAAO,CAAE,CAAA;QACd,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,aAAa,cAAc;YAC3B,IAAI,4BAA4B,SAAS,YAAY,EAAE;gBACnD,IAAI,SAAS,QAAQ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,iBAAiB,GAAG;oBAC5D,SAAS,WAAW,GAAG,SAAS,QAAQ,GAAG,SAAS,aAAa,GAAG,CAAC,aAAa,aAAa,GAAG,aAAa,QAAQ,IAAI,2BAA2B,SAAS,YAAY;oBAC3K,SAAS,WAAW,GAAG,SAAS,QAAQ,GAAG,SAAS,aAAa,GAAG,CAAC,aAAa,aAAa,GAAG,aAAa,QAAQ,IAAI,2BAA2B,SAAS,YAAY;gBAC/K;gBACA,QAAQ,2BAA2B,aAAa,gBAAgB,SAAS,YAAY,GAAG,aAAa;gBACrG,SAAS,QAAQ,GAAG,SAAS,QAAQ,GAAG,aAAa,YAAY;YACrE;YACA,IAAI,aAAa,QAAQ,IAAI,CAAC,SAAS,QAAQ,IAAI,CAAC,aAAa,QAAQ,IAAI,SAAS,QAAQ,EAAE;gBAC5F,0BAA0B,aAAa,QAAQ,GAAG,aAAa,qBAAqB;YACxF,OAAO;gBACH,0BAA0B,aAAa,aAAa,GAAG,aAAa,QAAQ;YAChF;YACA,oBAAoB,aAAa;YACjC,OAAO,CAAC,0BAA0B,aAAa,gBAAgB,CAAC,SAAS,aAAa,GAAG,SAAS,QAAQ,IAAI,iBAAiB,IAAI;YACnI,SAAS,QAAQ,IAAI;YACrB,SAAS,QAAQ,IAAI;QACzB;IACJ;AACJ;AAEA,SAAS,kBAAkB,QAAQ;IAC/B,IAAI;IACJ,IAAI;IACJ,IAAI,eAAe;IACnB,IAAI,aAAa;IACjB,SAAS,OAAO,CAAE,CAAA;QACd,MAAM,WAAW,SAAS,QAAQ;QAClC,aAAa,SAAS,QAAQ,GAAG,SAAS,WAAW,GAAG,CAAC,SAAS,QAAQ,GAAG,SAAS,WAAW,IAAI,aAAa,YAAY;QAC9H,aAAa,SAAS,QAAQ,GAAG,SAAS,WAAW,GAAG,CAAC,SAAS,WAAW,GAAG,SAAS,QAAQ,IAAI,aAAa,YAAY;QAC9H,eAAe,KAAK,cAAc,WAAW,aAAa;QAC1D,aAAa,KAAK,YAAY,WAAW,aAAa;IAC1D;IACA,OAAO;QACH,OAAO;QACP,KAAK;IACT;AACJ;AAEA,SAAS,8BAA8B,QAAQ,EAAE,QAAQ;IACrD,SAAS,OAAO,CAAE,CAAA;QACd,MAAM,QAAQ,aAAa;QAC3B,MAAM,WAAW,KAAK,QAAQ;QAC9B,KAAK,QAAQ,GAAG,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,WAAW,QAAQ,QAAQ,GAAG;QAC9E,KAAK,QAAQ,GAAG,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,KAAK,QAAQ,GAAG,QAAQ,CAAC,WAAW,UAAU,MAAM,GAAG;IAClF;AACJ;AAEA,SAAS,wCAAwC,QAAQ;IACrD,IAAI,uBAAuB;IAC3B,SAAS,OAAO,CAAE,CAAA;QACd,uBAAuB,wBAAwB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,iBAAiB;IACnF;IACA,SAAS,OAAO,CAAE,CAAA;QACd,MAAM,eAAe,KAAK,YAAY;QACtC,MAAM,aAAa,KAAK,UAAU;QAClC,MAAM,WAAW,KAAK,QAAQ;QAC9B,MAAM,WAAW,KAAK,QAAQ;QAC9B,IAAI;QACJ,IAAI,wBAAwB,cAAc;YACtC,MAAO,CAAC,OAAO,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,UAAU,CAAC,EAAE,GAAG,aAAa,KAAK,SAAU;gBAC9D,WAAW,OAAO,CAAC;YACvB;YACA,OAAO,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;YACxC,MAAO,CAAC,OAAO,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,OAAO,aAAa,KAAK,SAAU;gBACrD,WAAW,IAAI,CAAC;YACpB;QACJ;QACA,MAAO,UAAU,CAAC,EAAE,GAAG,eAAe,KAAK,SAAU;YACjD,WAAW,KAAK;QACpB;QACA,MAAO,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,GAAG,eAAe,KAAK,SAAU;YACrE,WAAW,GAAG;QAClB;IACJ;AACJ;AAEA,SAAS,kBAAkB,QAAQ;IAC/B,SAAS,OAAO,CAAE,CAAA;QACd,MAAM,OAAO,KAAK,IAAI;QACtB,MAAM,QAAQ,KAAK,aAAa,GAAG,gBAAgB;QACnD,IAAI,MAAM,GAAG,KAAK,MAAM,UAAU,EAAE;YAChC,MAAM,GAAG,GAAG,KAAK,QAAQ;QAC7B;QACA,IAAI,MAAM,GAAG,KAAK,MAAM,UAAU,EAAE;YAChC,MAAM,GAAG,GAAG,KAAK,QAAQ;QAC7B;QACA,MAAM,UAAU,GAAG,KAAK,QAAQ;QAChC,MAAM,UAAU,GAAG,KAAK,QAAQ;QAChC,IAAI,MAAM,GAAG,GAAG,MAAM,UAAU,EAAE;YAC9B,MAAM,GAAG,GAAG,MAAM,UAAU;QAChC;QACA,IAAI,MAAM,GAAG,GAAG,MAAM,UAAU,EAAE;YAC9B,MAAM,GAAG,GAAG,MAAM,UAAU;QAChC;QACA,KAAK,aAAa,GAAG,mBAAmB,CAAC;QACzC,KAAK,QAAQ,CAAC;YACV,YAAY,KAAK,UAAU;YAC3B,YAAY,KAAK,WAAW;QAChC;IACJ;AACJ;AAEA,SAAS,wBAAwB,QAAQ;IACrC,MAAM,kBAAkB,EAAE;IAC1B,IAAI;IACJ,SAAS,OAAO,CAAE,CAAA;QACd,IAAI,KAAK,WAAW,GAAG,KAAK,WAAW,KAAK,GAAG;YAC3C,gBAAgB,IAAI,CAAC;QACzB,OAAO,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,iBAAiB,GAAG;YACvE,eAAe,KAAK,CAAC,KAAK,QAAQ,GAAG,KAAK,QAAQ,IAAI,CAAC,KAAK,UAAU,CAAC,OAAO,KAAK,UAAU,CAAC,MAAM,GAAG,GAAG,GAAG,KAAK,QAAQ,IAAI,KAAK,QAAQ;QAC/I;IACJ;IACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;QAC1B;IACJ;IACA,gBAAgB,OAAO,CAAE,CAAA;QACrB,MAAM,YAAY,KAAK,UAAU,CAAC,EAAE;QACpC,MAAM,gBAAgB,YAAY;QAClC,IAAI,YAAY,GAAG;YACf,KAAK,QAAQ,GAAG;YAChB,KAAK,QAAQ,GAAG;QACpB,OAAO,IAAI,YAAY,GAAG;YACtB,KAAK,QAAQ,GAAG;YAChB,KAAK,QAAQ,GAAG;QACpB;IACJ;AACJ;AAEA,SAAS,iBAAiB,QAAQ;IAC9B,SAAS,OAAO,CAAE,SAAS,QAAQ;QAC/B,IAAI,CAAC,SAAS,iBAAiB,EAAE;YAC7B;QACJ;QACA,MAAM,QAAQ,EAAE;QAChB,MAAM,WAAW,SAAS,iBAAiB;QAC3C,MAAM,YAAY,SAAS,YAAY,GAAG,WAAW;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,UAAU,CAAC,MAAM,EAAE,IAAK;YACjD,IAAI,OAAO,SAAS,UAAU,CAAC,IAAI,EAAE;YACrC,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAAK;gBAChC,QAAQ;gBACR,MAAM,IAAI,CAAC;YACf;QACJ;QACA,SAAS,WAAW,GAAG;IAC3B;AACJ;AAEA,SAAS,gCAAgC,QAAQ;IAC7C,IAAI,cAAc;IAClB,IAAI,cAAc;IAClB,SAAS,OAAO,CAAE,CAAA;QACd,IAAI,KAAK,WAAW,GAAG,KAAK,KAAK,WAAW,GAAG,GAAG;YAC9C,cAAc;QAClB;QACA,IAAI,KAAK,WAAW,GAAG,KAAK,KAAK,WAAW,GAAG,GAAG;YAC9C,cAAc;QAClB;IACJ;IACA,OAAO,eAAe;AAC1B;AAEA,SAAS,gBAAgB,QAAQ,EAAE,QAAQ;IACvC,IAAI,CAAC,gCAAgC,WAAW;QAC5C,OAAO;IACX;IACA,OAAO,SAAS,MAAM,CAAE,CAAC,MAAM;QAC3B,MAAM,WAAW,KAAK,QAAQ;QAC9B,MAAM,EACF,OAAO,KAAK,EACZ,KAAK,GAAG,EACX,GAAG,KAAK,IAAI,CAAC,wBAAwB,CAAC,KAAK,QAAQ,EAAE,KAAK,QAAQ;QACnE,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,UAAU,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM;YACpC,OAAO,WAAW;gBACd,OAAO,KAAK,KAAK;gBACjB,KAAK,KAAK,GAAG,CAAC,KAAK,GAAG,EAAE;YAC5B,IAAI;gBACA,OAAO,KAAK,GAAG,CAAC,KAAK,KAAK,EAAE;gBAC5B,KAAK,KAAK,GAAG;YACjB;QACJ;QACA,OAAO;IACX,GAAI;AACR;AACA,MAAM,wBAAwB;IAC1B,aAAa,SAAS,SAAS;QAC3B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,qBAAqB,YAAa,SAAS,CAAC,EAAE,IAAI;YACnD,IAAI;YACJ,IAAI;YACJ,IAAI,KAAK,MAAM,GAAG,GAAG;gBACjB,WAAW,iBAAiB;gBAC5B,IAAI,SAAS,MAAM,GAAG,KAAK,CAAC,gBAAgB,WAAW;oBACnD;gBACJ;gBACA,iBAAiB;gBACjB,oBAAoB;gBACpB,WAAW,kBAAkB;gBAC7B,WAAW,gBAAgB,UAAU;gBACrC,8BAA8B,UAAU;gBACxC,wBAAwB;gBACxB,wCAAwC;gBACxC,iBAAiB;gBACjB,SAAS,OAAO,CAAE,CAAA;oBACd,gBAAgB,MAAM,aAAa,KAAK,IAAI,CAAC,aAAa,GAAG,gBAAgB;gBACjF;gBACA,kBAAkB;YACtB;QACJ;IACJ;AACJ;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1002, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/scroll_bar.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/scroll_bar.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport eventsEngine from \"../../common/core/events/core/events_engine\";\r\nimport {\r\n    fireEvent\r\n} from \"../../common/core/events/utils/index\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    Translator2D\r\n} from \"../translators/translator2d\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    start as dragEventStart,\r\n    move as dragEventMove,\r\n    end as dragEventEnd\r\n} from \"../../common/core/events/drag\";\r\nconst _min = Math.min;\r\nconst _max = Math.max;\r\nconst MIN_SCROLL_BAR_SIZE = 2;\r\nexport const ScrollBar = function(renderer, group) {\r\n    this._translator = new Translator2D({}, {}, {});\r\n    this._scroll = renderer.rect().append(group);\r\n    this._addEvents()\r\n};\r\n\r\nfunction _getXCoord(canvas, pos, offset, width) {\r\n    let x = 0;\r\n    if (\"right\" === pos) {\r\n        x = canvas.width - canvas.right + offset\r\n    } else if (\"left\" === pos) {\r\n        x = canvas.left - offset - width\r\n    }\r\n    return x\r\n}\r\n\r\nfunction _getYCoord(canvas, pos, offset, width) {\r\n    let y = 0;\r\n    if (\"top\" === pos) {\r\n        y = canvas.top - offset\r\n    } else if (\"bottom\" === pos) {\r\n        y = canvas.height - canvas.bottom + width + offset\r\n    }\r\n    return y\r\n}\r\nScrollBar.prototype = {\r\n    _addEvents: function() {\r\n        const scrollElement = this._scroll.element;\r\n        eventsEngine.on(scrollElement, dragEventStart, (e => {\r\n            fireEvent({\r\n                type: \"dxc-scroll-start\",\r\n                originalEvent: e,\r\n                target: scrollElement\r\n            })\r\n        }));\r\n        eventsEngine.on(scrollElement, dragEventMove, (e => {\r\n            const dX = -e.offset.x * this._scale;\r\n            const dY = -e.offset.y * this._scale;\r\n            const lx = this._offset - (this._layoutOptions.vertical ? dY : dX) / this._scale;\r\n            this._applyPosition(lx, lx + this._translator.canvasLength / this._scale);\r\n            fireEvent({\r\n                type: \"dxc-scroll-move\",\r\n                originalEvent: e,\r\n                target: scrollElement,\r\n                offset: {\r\n                    x: dX,\r\n                    y: dY\r\n                }\r\n            })\r\n        }));\r\n        eventsEngine.on(scrollElement, dragEventEnd, (e => {\r\n            fireEvent({\r\n                type: \"dxc-scroll-end\",\r\n                originalEvent: e,\r\n                target: scrollElement,\r\n                offset: {\r\n                    x: -e.offset.x * this._scale,\r\n                    y: -e.offset.y * this._scale\r\n                }\r\n            })\r\n        }))\r\n    },\r\n    update: function(options) {\r\n        let position = options.position;\r\n        const isVertical = options.rotated;\r\n        const defaultPosition = isVertical ? \"right\" : \"top\";\r\n        const secondaryPosition = isVertical ? \"left\" : \"bottom\";\r\n        if (position !== defaultPosition && position !== secondaryPosition) {\r\n            position = defaultPosition\r\n        }\r\n        this._scroll.attr({\r\n            rotate: !options.rotated ? -90 : 0,\r\n            rotateX: 0,\r\n            rotateY: 0,\r\n            fill: options.color,\r\n            width: options.width,\r\n            opacity: options.opacity\r\n        });\r\n        this._layoutOptions = {\r\n            width: options.width,\r\n            offset: options.offset,\r\n            vertical: isVertical,\r\n            position: position\r\n        };\r\n        return this\r\n    },\r\n    init: function(range, stick) {\r\n        const isDiscrete = \"discrete\" === range.axisType;\r\n        this._translateWithOffset = isDiscrete && !stick ? 1 : 0;\r\n        this._translator.update(extend({}, range, {\r\n            minVisible: null,\r\n            maxVisible: null,\r\n            visibleCategories: null\r\n        }, isDiscrete && {\r\n            min: null,\r\n            max: null\r\n        } || {}), this._canvas, {\r\n            isHorizontal: !this._layoutOptions.vertical,\r\n            stick: stick\r\n        });\r\n        return this\r\n    },\r\n    getOptions: function() {\r\n        return this._layoutOptions\r\n    },\r\n    setPane: function(panes) {\r\n        const position = this._layoutOptions.position;\r\n        let pane;\r\n        if (\"left\" === position || \"top\" === position) {\r\n            pane = panes[0]\r\n        } else {\r\n            pane = panes[panes.length - 1]\r\n        }\r\n        this.pane = pane.name;\r\n        return this\r\n    },\r\n    updateSize: function(canvas) {\r\n        this._canvas = extend({}, canvas);\r\n        const options = this._layoutOptions;\r\n        const pos = options.position;\r\n        const offset = options.offset;\r\n        const width = options.width;\r\n        this._scroll.attr({\r\n            translateX: _getXCoord(canvas, pos, offset, width),\r\n            translateY: _getYCoord(canvas, pos, offset, width)\r\n        })\r\n    },\r\n    getMultipleAxesSpacing: function() {\r\n        return 0\r\n    },\r\n    estimateMargins: function() {\r\n        return this.getMargins()\r\n    },\r\n    getMargins: function() {\r\n        const options = this._layoutOptions;\r\n        const margins = {\r\n            left: 0,\r\n            top: 0,\r\n            right: 0,\r\n            bottom: 0\r\n        };\r\n        margins[options.position] = options.width + options.offset;\r\n        return margins\r\n    },\r\n    shift: function(margins) {\r\n        const options = this._layoutOptions;\r\n        const side = options.position;\r\n        const isVertical = options.vertical;\r\n        const attr = {\r\n            translateX: this._scroll.attr(\"translateX\") ?? 0,\r\n            translateY: this._scroll.attr(\"translateY\") ?? 0\r\n        };\r\n        const shift = margins[side];\r\n        attr[isVertical ? \"translateX\" : \"translateY\"] += (\"left\" === side || \"top\" === side ? -1 : 1) * shift;\r\n        this._scroll.attr(attr)\r\n    },\r\n    hideTitle: noop,\r\n    hideOuterElements: noop,\r\n    setPosition: function(min, max) {\r\n        const translator = this._translator;\r\n        const minPoint = isDefined(min) ? translator.translate(min, -this._translateWithOffset) : translator.translate(\"canvas_position_start\");\r\n        const maxPoint = isDefined(max) ? translator.translate(max, this._translateWithOffset) : translator.translate(\"canvas_position_end\");\r\n        this._offset = _min(minPoint, maxPoint);\r\n        this._scale = translator.getScale(min, max);\r\n        this._applyPosition(_min(minPoint, maxPoint), _max(minPoint, maxPoint))\r\n    },\r\n    customPositionIsAvailable: () => false,\r\n    dispose: function() {\r\n        this._scroll.dispose();\r\n        this._scroll = this._translator = null\r\n    },\r\n    _applyPosition: function(x1, x2) {\r\n        const visibleArea = this._translator.getCanvasVisibleArea();\r\n        x1 = _max(x1, visibleArea.min);\r\n        x1 = _min(x1, visibleArea.max);\r\n        x2 = _min(x2, visibleArea.max);\r\n        x2 = _max(x2, visibleArea.min);\r\n        const height = Math.abs(x2 - x1);\r\n        this._scroll.attr({\r\n            y: x1,\r\n            height: height < 2 ? 2 : height\r\n        })\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;;;;AAKA,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,sBAAsB;AACrB,MAAM,YAAY,SAAS,QAAQ,EAAE,KAAK;IAC7C,IAAI,CAAC,WAAW,GAAG,IAAI,0KAAA,CAAA,eAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC;IAC7C,IAAI,CAAC,OAAO,GAAG,SAAS,IAAI,GAAG,MAAM,CAAC;IACtC,IAAI,CAAC,UAAU;AACnB;AAEA,SAAS,WAAW,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;IAC1C,IAAI,IAAI;IACR,IAAI,YAAY,KAAK;QACjB,IAAI,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG;IACtC,OAAO,IAAI,WAAW,KAAK;QACvB,IAAI,OAAO,IAAI,GAAG,SAAS;IAC/B;IACA,OAAO;AACX;AAEA,SAAS,WAAW,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,KAAK;IAC1C,IAAI,IAAI;IACR,IAAI,UAAU,KAAK;QACf,IAAI,OAAO,GAAG,GAAG;IACrB,OAAO,IAAI,aAAa,KAAK;QACzB,IAAI,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG,QAAQ;IAChD;IACA,OAAO;AACX;AACA,UAAU,SAAS,GAAG;IAClB,YAAY;QACR,MAAM,gBAAgB,IAAI,CAAC,OAAO,CAAC,OAAO;QAC1C,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,eAAe,yKAAA,CAAA,QAAc,EAAG,CAAA;YAC5C,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE;gBACN,MAAM;gBACN,eAAe;gBACf,QAAQ;YACZ;QACJ;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,eAAe,yKAAA,CAAA,OAAa,EAAG,CAAA;YAC3C,MAAM,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;YACpC,MAAM,KAAK,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;YACpC,MAAM,KAAK,IAAI,CAAC,OAAO,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,KAAK,EAAE,IAAI,IAAI,CAAC,MAAM;YAChF,IAAI,CAAC,cAAc,CAAC,IAAI,KAAK,IAAI,CAAC,WAAW,CAAC,YAAY,GAAG,IAAI,CAAC,MAAM;YACxE,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE;gBACN,MAAM;gBACN,eAAe;gBACf,QAAQ;gBACR,QAAQ;oBACJ,GAAG;oBACH,GAAG;gBACP;YACJ;QACJ;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,eAAe,yKAAA,CAAA,MAAY,EAAG,CAAA;YAC1C,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE;gBACN,MAAM;gBACN,eAAe;gBACf,QAAQ;gBACR,QAAQ;oBACJ,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;oBAC5B,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,IAAI,CAAC,MAAM;gBAChC;YACJ;QACJ;IACJ;IACA,QAAQ,SAAS,OAAO;QACpB,IAAI,WAAW,QAAQ,QAAQ;QAC/B,MAAM,aAAa,QAAQ,OAAO;QAClC,MAAM,kBAAkB,aAAa,UAAU;QAC/C,MAAM,oBAAoB,aAAa,SAAS;QAChD,IAAI,aAAa,mBAAmB,aAAa,mBAAmB;YAChE,WAAW;QACf;QACA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,QAAQ,CAAC,QAAQ,OAAO,GAAG,CAAC,KAAK;YACjC,SAAS;YACT,SAAS;YACT,MAAM,QAAQ,KAAK;YACnB,OAAO,QAAQ,KAAK;YACpB,SAAS,QAAQ,OAAO;QAC5B;QACA,IAAI,CAAC,cAAc,GAAG;YAClB,OAAO,QAAQ,KAAK;YACpB,QAAQ,QAAQ,MAAM;YACtB,UAAU;YACV,UAAU;QACd;QACA,OAAO,IAAI;IACf;IACA,MAAM,SAAS,KAAK,EAAE,KAAK;QACvB,MAAM,aAAa,eAAe,MAAM,QAAQ;QAChD,IAAI,CAAC,oBAAoB,GAAG,cAAc,CAAC,QAAQ,IAAI;QACvD,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;YACtC,YAAY;YACZ,YAAY;YACZ,mBAAmB;QACvB,GAAG,cAAc;YACb,KAAK;YACL,KAAK;QACT,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;YACpB,cAAc,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ;YAC3C,OAAO;QACX;QACA,OAAO,IAAI;IACf;IACA,YAAY;QACR,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,SAAS,SAAS,KAAK;QACnB,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ;QAC7C,IAAI;QACJ,IAAI,WAAW,YAAY,UAAU,UAAU;YAC3C,OAAO,KAAK,CAAC,EAAE;QACnB,OAAO;YACH,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAClC;QACA,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI;QACrB,OAAO,IAAI;IACf;IACA,YAAY,SAAS,MAAM;QACvB,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;QAC1B,MAAM,UAAU,IAAI,CAAC,cAAc;QACnC,MAAM,MAAM,QAAQ,QAAQ;QAC5B,MAAM,SAAS,QAAQ,MAAM;QAC7B,MAAM,QAAQ,QAAQ,KAAK;QAC3B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,YAAY,WAAW,QAAQ,KAAK,QAAQ;YAC5C,YAAY,WAAW,QAAQ,KAAK,QAAQ;QAChD;IACJ;IACA,wBAAwB;QACpB,OAAO;IACX;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,YAAY;QACR,MAAM,UAAU,IAAI,CAAC,cAAc;QACnC,MAAM,UAAU;YACZ,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACZ;QACA,OAAO,CAAC,QAAQ,QAAQ,CAAC,GAAG,QAAQ,KAAK,GAAG,QAAQ,MAAM;QAC1D,OAAO;IACX;IACA,OAAO,SAAS,OAAO;QACnB,MAAM,UAAU,IAAI,CAAC,cAAc;QACnC,MAAM,OAAO,QAAQ,QAAQ;QAC7B,MAAM,aAAa,QAAQ,QAAQ;YAEnB,oBACA;QAFhB,MAAM,OAAO;YACT,YAAY,CAAA,qBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAAlB,gCAAA,qBAAmC;YAC/C,YAAY,CAAA,sBAAA,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,2BAAlB,iCAAA,sBAAmC;QACnD;QACA,MAAM,QAAQ,OAAO,CAAC,KAAK;QAC3B,IAAI,CAAC,aAAa,eAAe,aAAa,IAAI,CAAC,WAAW,QAAQ,UAAU,OAAO,CAAC,IAAI,CAAC,IAAI;QACjG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;IACtB;IACA,WAAW,kLAAA,CAAA,OAAI;IACf,mBAAmB,kLAAA,CAAA,OAAI;IACvB,aAAa,SAAS,GAAG,EAAE,GAAG;QAC1B,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,WAAW,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,oBAAoB,IAAI,WAAW,SAAS,CAAC;QAC/G,MAAM,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,WAAW,SAAS,CAAC,KAAK,IAAI,CAAC,oBAAoB,IAAI,WAAW,SAAS,CAAC;QAC9G,IAAI,CAAC,OAAO,GAAG,KAAK,UAAU;QAC9B,IAAI,CAAC,MAAM,GAAG,WAAW,QAAQ,CAAC,KAAK;QACvC,IAAI,CAAC,cAAc,CAAC,KAAK,UAAU,WAAW,KAAK,UAAU;IACjE;IACA,2BAA2B,IAAM;IACjC,SAAS;QACL,IAAI,CAAC,OAAO,CAAC,OAAO;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,WAAW,GAAG;IACtC;IACA,gBAAgB,SAAS,EAAE,EAAE,EAAE;QAC3B,MAAM,cAAc,IAAI,CAAC,WAAW,CAAC,oBAAoB;QACzD,KAAK,KAAK,IAAI,YAAY,GAAG;QAC7B,KAAK,KAAK,IAAI,YAAY,GAAG;QAC7B,KAAK,KAAK,IAAI,YAAY,GAAG;QAC7B,KAAK,KAAK,IAAI,YAAY,GAAG;QAC7B,MAAM,SAAS,KAAK,GAAG,CAAC,KAAK;QAC7B,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;YACd,GAAG;YACH,QAAQ,SAAS,IAAI,IAAI;QAC7B;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1222, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/shutter_zoom.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/shutter_zoom.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    start as dragEventStart,\r\n    move as dragEventMove,\r\n    end as dragEventEnd\r\n} from \"../../common/core/events/drag\";\r\nconst SHUTTER_EVENTS_NS = \".shutter-zoom\";\r\nconst DRAG_START_EVENT_NAME = dragEventStart + \".shutter-zoom\";\r\nconst DRAG_UPDATE_EVENT_NAME = dragEventMove + \".shutter-zoom\";\r\nconst DRAG_END_EVENT_NAME = dragEventEnd + \".shutter-zoom\";\r\n\r\nfunction getPointerCoord(rootOffset, canvas, rotated, e) {\r\n    let coord = Math.floor(rotated ? e.pageY - rootOffset.top : e.pageX - rootOffset.left);\r\n    const min = rotated ? canvas.y1 : canvas.x1;\r\n    const max = rotated ? canvas.y2 : canvas.x2;\r\n    if (coord < min) {\r\n        coord = min\r\n    } else if (coord > max) {\r\n        coord = max\r\n    }\r\n    return coord\r\n}\r\n\r\nfunction checkCoords(rootOffset, canvas, e) {\r\n    const x = e.pageX - rootOffset.left;\r\n    const y = e.pageY - rootOffset.top;\r\n    return x >= canvas.x1 && x <= canvas.x2 && y >= canvas.y1 && y <= canvas.y2\r\n}\r\n\r\nfunction dragStartHandler(ctx) {\r\n    return function(e) {\r\n        const offset = ctx.getRootOffset();\r\n        const canvas = ctx.getCanvas();\r\n        if (!checkCoords(offset, canvas, e)) {\r\n            e.cancel = true;\r\n            return\r\n        }\r\n        ctx.rootOffset = offset;\r\n        ctx.canvas = canvas;\r\n        ctx.startCoord = getPointerCoord(offset, canvas, ctx.rotated, e);\r\n        ctx.triggerStart();\r\n        ctx.rect.attr({\r\n            x: canvas.x1,\r\n            y: canvas.y1,\r\n            width: canvas.width,\r\n            height: canvas.height\r\n        }).append(ctx.root)\r\n    }\r\n}\r\n\r\nfunction dragHandler(ctx) {\r\n    return function(e) {\r\n        const curCoord = getPointerCoord(ctx.rootOffset, ctx.canvas, ctx.rotated, e);\r\n        const attr = {};\r\n        ctx.curCoord = curCoord;\r\n        attr[ctx.rotated ? \"y\" : \"x\"] = Math.min(ctx.startCoord, curCoord);\r\n        attr[ctx.rotated ? \"height\" : \"width\"] = Math.abs(ctx.startCoord - curCoord);\r\n        ctx.rect.attr(attr)\r\n    }\r\n}\r\n\r\nfunction dragEndHandler(ctx) {\r\n    return function(e) {\r\n        ctx.triggerEnd();\r\n        ctx.rect.remove()\r\n    }\r\n}\r\n\r\nfunction shutterZoom(options) {\r\n    const chart = options.chart;\r\n    const renderer = options.renderer;\r\n    const rotated = options.rotated;\r\n    const rect = renderer.rect(0, 0, 0, 0).attr(options.shutterOptions);\r\n    const shutter = {\r\n        rect: rect,\r\n        root: renderer.root,\r\n        rotated: rotated,\r\n        triggerStart: function() {\r\n            chart._eventTrigger(\"zoomStart\")\r\n        },\r\n        triggerEnd: function() {\r\n            const tr = chart._argumentAxes[0].getTranslator();\r\n            const rangeStart = Math.min(this.startCoord, this.curCoord);\r\n            const rangeEnd = Math.max(this.startCoord, this.curCoord);\r\n            chart._eventTrigger(\"zoomEnd\", {\r\n                rangeStart: tr.from(rangeStart),\r\n                rangeEnd: tr.from(rangeEnd)\r\n            })\r\n        },\r\n        dispose: function() {\r\n            renderer.root.off(\".shutter-zoom\");\r\n            rect.dispose()\r\n        },\r\n        getRootOffset: function() {\r\n            return renderer.getRootOffset()\r\n        },\r\n        getCanvas: function() {\r\n            const canvas = chart._canvas;\r\n            const panes = chart.panes;\r\n            const firstPane = panes[0].canvas;\r\n            const lastPane = panes[panes.length - 1].canvas;\r\n            return {\r\n                x1: firstPane.left,\r\n                y1: firstPane.top,\r\n                x2: canvas.width - lastPane.right,\r\n                y2: canvas.height - lastPane.bottom,\r\n                width: canvas.width - firstPane.left - lastPane.right,\r\n                height: canvas.height - firstPane.top - lastPane.bottom\r\n            }\r\n        }\r\n    };\r\n    renderer.root.off(\".shutter-zoom\").on(DRAG_START_EVENT_NAME, {\r\n        direction: rotated ? \"vertical\" : \"horizontal\",\r\n        immediate: true\r\n    }, dragStartHandler(shutter)).on(DRAG_UPDATE_EVENT_NAME, dragHandler(shutter)).on(DRAG_END_EVENT_NAME, dragEndHandler(shutter));\r\n    return shutter\r\n}\r\nexport default {\r\n    name: \"shutter_zoom\",\r\n    init: function() {\r\n        const options = this.option(\"shutterZoom\") || {};\r\n        if (!options.enabled) {\r\n            return\r\n        }\r\n        this._shutterZoom = shutterZoom({\r\n            chart: this,\r\n            renderer: this._renderer,\r\n            rotated: this.option(\"rotated\"),\r\n            shutterOptions: options\r\n        })\r\n    },\r\n    dispose: function() {\r\n        this._shutterZoom && this._shutterZoom.dispose()\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;;AAKA,MAAM,oBAAoB;AAC1B,MAAM,wBAAwB,yKAAA,CAAA,QAAc,GAAG;AAC/C,MAAM,yBAAyB,yKAAA,CAAA,OAAa,GAAG;AAC/C,MAAM,sBAAsB,yKAAA,CAAA,MAAY,GAAG;AAE3C,SAAS,gBAAgB,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;IACnD,IAAI,QAAQ,KAAK,KAAK,CAAC,UAAU,EAAE,KAAK,GAAG,WAAW,GAAG,GAAG,EAAE,KAAK,GAAG,WAAW,IAAI;IACrF,MAAM,MAAM,UAAU,OAAO,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,MAAM,UAAU,OAAO,EAAE,GAAG,OAAO,EAAE;IAC3C,IAAI,QAAQ,KAAK;QACb,QAAQ;IACZ,OAAO,IAAI,QAAQ,KAAK;QACpB,QAAQ;IACZ;IACA,OAAO;AACX;AAEA,SAAS,YAAY,UAAU,EAAE,MAAM,EAAE,CAAC;IACtC,MAAM,IAAI,EAAE,KAAK,GAAG,WAAW,IAAI;IACnC,MAAM,IAAI,EAAE,KAAK,GAAG,WAAW,GAAG;IAClC,OAAO,KAAK,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE,IAAI,KAAK,OAAO,EAAE;AAC/E;AAEA,SAAS,iBAAiB,GAAG;IACzB,OAAO,SAAS,CAAC;QACb,MAAM,SAAS,IAAI,aAAa;QAChC,MAAM,SAAS,IAAI,SAAS;QAC5B,IAAI,CAAC,YAAY,QAAQ,QAAQ,IAAI;YACjC,EAAE,MAAM,GAAG;YACX;QACJ;QACA,IAAI,UAAU,GAAG;QACjB,IAAI,MAAM,GAAG;QACb,IAAI,UAAU,GAAG,gBAAgB,QAAQ,QAAQ,IAAI,OAAO,EAAE;QAC9D,IAAI,YAAY;QAChB,IAAI,IAAI,CAAC,IAAI,CAAC;YACV,GAAG,OAAO,EAAE;YACZ,GAAG,OAAO,EAAE;YACZ,OAAO,OAAO,KAAK;YACnB,QAAQ,OAAO,MAAM;QACzB,GAAG,MAAM,CAAC,IAAI,IAAI;IACtB;AACJ;AAEA,SAAS,YAAY,GAAG;IACpB,OAAO,SAAS,CAAC;QACb,MAAM,WAAW,gBAAgB,IAAI,UAAU,EAAE,IAAI,MAAM,EAAE,IAAI,OAAO,EAAE;QAC1E,MAAM,OAAO,CAAC;QACd,IAAI,QAAQ,GAAG;QACf,IAAI,CAAC,IAAI,OAAO,GAAG,MAAM,IAAI,GAAG,KAAK,GAAG,CAAC,IAAI,UAAU,EAAE;QACzD,IAAI,CAAC,IAAI,OAAO,GAAG,WAAW,QAAQ,GAAG,KAAK,GAAG,CAAC,IAAI,UAAU,GAAG;QACnE,IAAI,IAAI,CAAC,IAAI,CAAC;IAClB;AACJ;AAEA,SAAS,eAAe,GAAG;IACvB,OAAO,SAAS,CAAC;QACb,IAAI,UAAU;QACd,IAAI,IAAI,CAAC,MAAM;IACnB;AACJ;AAEA,SAAS,YAAY,OAAO;IACxB,MAAM,QAAQ,QAAQ,KAAK;IAC3B,MAAM,WAAW,QAAQ,QAAQ;IACjC,MAAM,UAAU,QAAQ,OAAO;IAC/B,MAAM,OAAO,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,cAAc;IAClE,MAAM,UAAU;QACZ,MAAM;QACN,MAAM,SAAS,IAAI;QACnB,SAAS;QACT,cAAc;YACV,MAAM,aAAa,CAAC;QACxB;QACA,YAAY;YACR,MAAM,KAAK,MAAM,aAAa,CAAC,EAAE,CAAC,aAAa;YAC/C,MAAM,aAAa,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ;YAC1D,MAAM,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,QAAQ;YACxD,MAAM,aAAa,CAAC,WAAW;gBAC3B,YAAY,GAAG,IAAI,CAAC;gBACpB,UAAU,GAAG,IAAI,CAAC;YACtB;QACJ;QACA,SAAS;YACL,SAAS,IAAI,CAAC,GAAG,CAAC;YAClB,KAAK,OAAO;QAChB;QACA,eAAe;YACX,OAAO,SAAS,aAAa;QACjC;QACA,WAAW;YACP,MAAM,SAAS,MAAM,OAAO;YAC5B,MAAM,QAAQ,MAAM,KAAK;YACzB,MAAM,YAAY,KAAK,CAAC,EAAE,CAAC,MAAM;YACjC,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,MAAM;YAC/C,OAAO;gBACH,IAAI,UAAU,IAAI;gBAClB,IAAI,UAAU,GAAG;gBACjB,IAAI,OAAO,KAAK,GAAG,SAAS,KAAK;gBACjC,IAAI,OAAO,MAAM,GAAG,SAAS,MAAM;gBACnC,OAAO,OAAO,KAAK,GAAG,UAAU,IAAI,GAAG,SAAS,KAAK;gBACrD,QAAQ,OAAO,MAAM,GAAG,UAAU,GAAG,GAAG,SAAS,MAAM;YAC3D;QACJ;IACJ;IACA,SAAS,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,CAAC,uBAAuB;QACzD,WAAW,UAAU,aAAa;QAClC,WAAW;IACf,GAAG,iBAAiB,UAAU,EAAE,CAAC,wBAAwB,YAAY,UAAU,EAAE,CAAC,qBAAqB,eAAe;IACtH,OAAO;AACX;uCACe;IACX,MAAM;IACN,MAAM;QACF,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC;QAC/C,IAAI,CAAC,QAAQ,OAAO,EAAE;YAClB;QACJ;QACA,IAAI,CAAC,YAAY,GAAG,YAAY;YAC5B,OAAO,IAAI;YACX,UAAU,IAAI,CAAC,SAAS;YACxB,SAAS,IAAI,CAAC,MAAM,CAAC;YACrB,gBAAgB;QACpB;IACJ;IACA,SAAS;QACL,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO;IAClD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1362, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/zoom_and_pan.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/zoom_and_pan.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    normalizeEnum,\r\n    getVizRangeObject\r\n} from \"../core/utils\";\r\nimport {\r\n    name as wheelEvent\r\n} from \"../../common/core/events/core/wheel\";\r\nimport * as transformEvents from \"../../common/core/events/transform\";\r\nimport {\r\n    start as dragEventStart,\r\n    move as dragEventMove,\r\n    end as dragEventEnd\r\n} from \"../../common/core/events/drag\";\r\nconst EVENTS_NS = \".zoomAndPanNS\";\r\nconst DRAG_START_EVENT_NAME = dragEventStart + EVENTS_NS;\r\nconst DRAG_EVENT_NAME = dragEventMove + EVENTS_NS;\r\nconst DRAG_END_EVENT_NAME = dragEventEnd + EVENTS_NS;\r\nconst PINCH_START_EVENT_NAME = transformEvents.pinchstart + EVENTS_NS;\r\nconst PINCH_EVENT_NAME = transformEvents.pinch + EVENTS_NS;\r\nconst PINCH_END_EVENT_NAME = transformEvents.pinchend + EVENTS_NS;\r\nconst SCROLL_BAR_START_EVENT_NAME = \"dxc-scroll-start\" + EVENTS_NS;\r\nconst SCROLL_BAR_MOVE_EVENT_NAME = \"dxc-scroll-move\" + EVENTS_NS;\r\nconst SCROLL_BAR_END_EVENT_NAME = \"dxc-scroll-end\" + EVENTS_NS;\r\nconst GESTURE_TIMEOUT = 300;\r\nconst MIN_DRAG_DELTA = 5;\r\nconst _min = Math.min;\r\nconst _max = Math.max;\r\nconst _abs = Math.abs;\r\n\r\nfunction canvasToRect(canvas) {\r\n    return {\r\n        x: canvas.left,\r\n        y: canvas.top,\r\n        width: canvas.width - canvas.left - canvas.right,\r\n        height: canvas.height - canvas.top - canvas.bottom\r\n    }\r\n}\r\n\r\nfunction checkCoords(rect, coords) {\r\n    const x = coords.x;\r\n    const y = coords.y;\r\n    return x >= rect.x && x <= rect.width + rect.x && y >= rect.y && y <= rect.height + rect.y\r\n}\r\n\r\nfunction sortAxes(axes, onlyAxisToNotify) {\r\n    if (onlyAxisToNotify) {\r\n        axes = axes.sort(((a, b) => {\r\n            if (a === onlyAxisToNotify) {\r\n                return -1\r\n            }\r\n            if (b === onlyAxisToNotify) {\r\n                return 1\r\n            }\r\n            return 0\r\n        }))\r\n    }\r\n    return axes\r\n}\r\n\r\nfunction getFilteredAxes(axes) {\r\n    return axes.filter((a => !a.getTranslator().getBusinessRange().isEmpty()))\r\n}\r\n\r\nfunction isAxisAvailablePanning(axes) {\r\n    return axes.some((axis => !axis.isExtremePosition(false) || !axis.isExtremePosition(true)))\r\n}\r\n\r\nfunction axisZoom(axis, onlyAxisToNotify, getRange, getParameters, actionField, scale, e) {\r\n    const silent = onlyAxisToNotify && axis !== onlyAxisToNotify;\r\n    const range = getRange(axis);\r\n    const {\r\n        stopInteraction: stopInteraction,\r\n        correctedRange: correctedRange\r\n    } = axis.checkZoomingLowerLimitOvercome(actionField, scale, range);\r\n    const result = axis.handleZooming(stopInteraction ? null : correctedRange, getParameters(silent), e, actionField);\r\n    stopInteraction && axis.handleZoomEnd();\r\n    return {\r\n        stopInteraction: stopInteraction,\r\n        result: result\r\n    }\r\n}\r\n\r\nfunction zoomAxes(e, axes, getRange, zoom, params, onlyAxisToNotify) {\r\n    axes = sortAxes(axes, onlyAxisToNotify);\r\n    let zoomStarted = false;\r\n    const getParameters = silent => ({\r\n        start: !!silent,\r\n        end: !!silent\r\n    });\r\n    getFilteredAxes(axes).some((axis => {\r\n        const translator = axis.getTranslator();\r\n        const scale = translator.getMinScale(zoom);\r\n        const {\r\n            stopInteraction: stopInteraction,\r\n            result: result\r\n        } = axisZoom(axis, onlyAxisToNotify, getRange(_extends({\r\n            scale: scale,\r\n            translator: translator,\r\n            axis: axis\r\n        }, params)), getParameters, \"zoom\", scale, e);\r\n        zoomStarted = !stopInteraction;\r\n        return onlyAxisToNotify && result.isPrevented\r\n    }));\r\n    return zoomStarted\r\n}\r\n\r\nfunction cancelEvent(e) {\r\n    if (e.originalEvent) {\r\n        cancelEvent(e.originalEvent)\r\n    }\r\n    if (false !== e.cancelable) {\r\n        e.cancel = true\r\n    }\r\n}\r\nexport default {\r\n    name: \"zoom_and_pan\",\r\n    init: function() {\r\n        const chart = this;\r\n        const renderer = this._renderer;\r\n\r\n        function getAxesCopy(zoomAndPan, actionField) {\r\n            let axes = [];\r\n            const options = zoomAndPan.options;\r\n            const actionData = zoomAndPan.actionData;\r\n            if (options.argumentAxis[actionField]) {\r\n                axes.push(chart.getArgumentAxis())\r\n            }\r\n            if (options.valueAxis[actionField]) {\r\n                axes = axes.concat(actionData.valueAxes)\r\n            }\r\n            return axes\r\n        }\r\n\r\n        function startAxesViewportChanging(zoomAndPan, actionField, e) {\r\n            const axes = getAxesCopy(zoomAndPan, actionField);\r\n            getFilteredAxes(axes).some((axis => axis.handleZooming(null, {\r\n                end: true\r\n            }, e, actionField).isPrevented)) && cancelEvent(e)\r\n        }\r\n\r\n        function axesViewportChanging(zoomAndPan, actionField, e, offsetCalc, centerCalc) {\r\n            function zoomAxes(axes, criteria, coordField, e, actionData) {\r\n                let zoom = {\r\n                    zoomed: false\r\n                };\r\n                criteria && getFilteredAxes(axes).forEach((axis => {\r\n                    const options = axis.getOptions();\r\n                    const viewport = axis.visualRange();\r\n                    const scale = axis.getTranslator().getEventScale(e);\r\n                    const translate = -offsetCalc(e, actionData, coordField, scale);\r\n                    zoom = extend(true, zoom, axis.getTranslator().zoom(translate, scale, axis.getZoomBounds()));\r\n                    const range = axis.adjustRange(getVizRangeObject([zoom.min, zoom.max]));\r\n                    const {\r\n                        stopInteraction: stopInteraction,\r\n                        correctedRange: correctedRange\r\n                    } = axis.checkZoomingLowerLimitOvercome(actionField, scale, range);\r\n                    if (!isDefined(viewport) || viewport.startValue.valueOf() !== correctedRange.startValue.valueOf() || viewport.endValue.valueOf() !== correctedRange.endValue.valueOf()) {\r\n                        axis.handleZooming(stopInteraction ? null : correctedRange, {\r\n                            start: true,\r\n                            end: true\r\n                        }, e, actionField);\r\n                        if (!stopInteraction) {\r\n                            zoom.zoomed = true;\r\n                            zoom.deltaTranslate = translate - zoom.translate\r\n                        }\r\n                    } else if (\"touch\" === e.pointerType && \"discrete\" === options.type) {\r\n                        const isMinPosition = axis.isExtremePosition(false);\r\n                        const isMaxPosition = axis.isExtremePosition(true);\r\n                        const zoomInEnabled = scale > 1 && !stopInteraction;\r\n                        const zoomOutEnabled = scale < 1 && (!isMinPosition || !isMaxPosition);\r\n                        const panningEnabled = 1 === scale && !(isMinPosition && (translate < 0 && !options.inverted || translate > 0 && options.inverted) || isMaxPosition && (translate > 0 && !options.inverted || translate < 0 && options.inverted));\r\n                        zoom.enabled = zoomInEnabled || zoomOutEnabled || panningEnabled\r\n                    }\r\n                }));\r\n                return zoom\r\n            }\r\n\r\n            function storeOffset(e, actionData, zoom, coordField) {\r\n                if (zoom.zoomed) {\r\n                    actionData.offset[coordField] = (e.offset ? e.offset[coordField] : actionData.offset[coordField]) + zoom.deltaTranslate\r\n                }\r\n            }\r\n\r\n            function storeCenter(center, actionData, zoom, coordField) {\r\n                if (zoom.zoomed) {\r\n                    actionData.center[coordField] = center[coordField] + zoom.deltaTranslate\r\n                }\r\n            }\r\n            const rotated = chart.option(\"rotated\");\r\n            const actionData = zoomAndPan.actionData;\r\n            const options = zoomAndPan.options;\r\n            let argZoom = {};\r\n            let valZoom = {};\r\n            if (!actionData.fallback) {\r\n                argZoom = zoomAxes(chart._argumentAxes, options.argumentAxis[actionField], rotated ? \"y\" : \"x\", e, actionData);\r\n                valZoom = zoomAxes(actionData.valueAxes, options.valueAxis[actionField], rotated ? \"x\" : \"y\", e, actionData);\r\n                chart._requestChange([\"VISUAL_RANGE\"]);\r\n                storeOffset(e, actionData, argZoom, rotated ? \"y\" : \"x\");\r\n                storeOffset(e, actionData, valZoom, rotated ? \"x\" : \"y\")\r\n            }\r\n            const center = centerCalc(e);\r\n            storeCenter(center, actionData, argZoom, rotated ? \"y\" : \"x\");\r\n            storeCenter(center, actionData, valZoom, rotated ? \"x\" : \"y\");\r\n            if (!argZoom.zoomed && !valZoom.zoomed) {\r\n                actionData.center = center\r\n            }\r\n            return argZoom.zoomed || valZoom.zoomed || actionData.fallback || argZoom.enabled || valZoom.enabled\r\n        }\r\n\r\n        function finishAxesViewportChanging(zoomAndPan, actionField, e, offsetCalc) {\r\n            function zoomAxes(axes, coordField, actionData, onlyAxisToNotify) {\r\n                let zoomStarted = false;\r\n                const scale = e.scale || 1;\r\n                const getRange = axis => {\r\n                    const zoom = axis.getTranslator().zoom(-offsetCalc(e, actionData, coordField, scale), scale, axis.getZoomBounds());\r\n                    return {\r\n                        startValue: zoom.min,\r\n                        endValue: zoom.max\r\n                    }\r\n                };\r\n                const getParameters = silent => ({\r\n                    start: true,\r\n                    end: silent\r\n                });\r\n                getFilteredAxes(axes).forEach((axis => {\r\n                    zoomStarted = !axisZoom(axis, onlyAxisToNotify, getRange, getParameters, actionField, scale, e).stopInteraction\r\n                }));\r\n                return zoomStarted\r\n            }\r\n            const rotated = chart.option(\"rotated\");\r\n            const actionData = zoomAndPan.actionData;\r\n            const options = zoomAndPan.options;\r\n            let zoomStarted = true;\r\n            if (actionData.fallback) {\r\n                zoomStarted &= options.argumentAxis[actionField] && zoomAxes(chart._argumentAxes, rotated ? \"y\" : \"x\", actionData, chart.getArgumentAxis());\r\n                zoomStarted |= options.valueAxis[actionField] && zoomAxes(actionData.valueAxes, rotated ? \"x\" : \"y\", actionData)\r\n            } else {\r\n                const axes = getAxesCopy(zoomAndPan, actionField);\r\n                getFilteredAxes(axes).forEach((axis => {\r\n                    axis.handleZooming(null, {\r\n                        start: true\r\n                    }, e, actionField)\r\n                }));\r\n                zoomStarted = axes.length\r\n            }\r\n            zoomStarted && chart._requestChange([\"VISUAL_RANGE\"])\r\n        }\r\n\r\n        function prepareActionData(coords, action) {\r\n            const axes = chart._argumentAxes.filter((axis => checkCoords(canvasToRect(axis.getCanvas()), coords)));\r\n            return {\r\n                fallback: chart._lastRenderingTime > 300,\r\n                cancel: !axes.length || !isDefined(action),\r\n                action: action,\r\n                curAxisRect: axes.length && canvasToRect(axes[0].getCanvas()),\r\n                valueAxes: axes.length && chart._valueAxes.filter((axis => checkCoords(canvasToRect(axis.getCanvas()), coords))),\r\n                offset: {\r\n                    x: 0,\r\n                    y: 0\r\n                },\r\n                center: coords,\r\n                startCenter: coords\r\n            }\r\n        }\r\n\r\n        function getPointerCoord(rect, e) {\r\n            const rootOffset = renderer.getRootOffset();\r\n            return {\r\n                x: _min(_max(e.pageX - rootOffset.left, rect.x), rect.width + rect.x),\r\n                y: _min(_max(e.pageY - rootOffset.top, rect.y), rect.height + rect.y)\r\n            }\r\n        }\r\n\r\n        function calcCenterForPinch(e) {\r\n            const rootOffset = renderer.getRootOffset();\r\n            const x1 = e.pointers[0].pageX;\r\n            const x2 = e.pointers[1].pageX;\r\n            const y1 = e.pointers[0].pageY;\r\n            const y2 = e.pointers[1].pageY;\r\n            return {\r\n                x: _min(x1, x2) + _abs(x2 - x1) / 2 - rootOffset.left,\r\n                y: _min(y1, y2) + _abs(y2 - y1) / 2 - rootOffset.top\r\n            }\r\n        }\r\n\r\n        function calcCenterForDrag(e) {\r\n            const rootOffset = renderer.getRootOffset();\r\n            return {\r\n                x: e.pageX - rootOffset.left,\r\n                y: e.pageY - rootOffset.top\r\n            }\r\n        }\r\n\r\n        function calcOffsetForDrag(e, actionData, coordField) {\r\n            return e.offset[coordField] - actionData.offset[coordField]\r\n        }\r\n\r\n        function preventDefaults(e) {\r\n            if (false !== e.cancelable) {\r\n                e.preventDefault();\r\n                e.stopPropagation()\r\n            }\r\n            chart._stopCurrentHandling()\r\n        }\r\n        const zoomAndPan = {\r\n            dragStartHandler: function(e) {\r\n                const options = zoomAndPan.options;\r\n                const isTouch = \"touch\" === e.pointerType;\r\n                const wantPan = options.argumentAxis.pan || options.valueAxis.pan;\r\n                const wantZoom = options.argumentAxis.zoom || options.valueAxis.zoom;\r\n                const panKeyPressed = isDefined(options.panKey) && e[normalizeEnum(options.panKey) + \"Key\"];\r\n                const dragToZoom = options.dragToZoom;\r\n                let action;\r\n                e._cancelPreventDefault = true;\r\n                if (isTouch) {\r\n                    if (options.allowTouchGestures && wantPan) {\r\n                        const cancelPanning = !zoomAndPan.panningVisualRangeEnabled() || zoomAndPan.skipEvent;\r\n                        action = cancelPanning ? null : \"pan\"\r\n                    }\r\n                } else if (dragToZoom && wantPan && panKeyPressed || !dragToZoom && wantPan) {\r\n                    action = \"pan\"\r\n                } else if (dragToZoom && wantZoom) {\r\n                    action = \"zoom\"\r\n                }\r\n                const actionData = prepareActionData(calcCenterForDrag(e), action);\r\n                if (actionData.cancel) {\r\n                    zoomAndPan.skipEvent = false;\r\n                    if (false !== e.cancelable) {\r\n                        e.cancel = true\r\n                    }\r\n                    return\r\n                }\r\n                zoomAndPan.actionData = actionData;\r\n                if (\"zoom\" === action) {\r\n                    actionData.startCoords = getPointerCoord(actionData.curAxisRect, e);\r\n                    actionData.rect = renderer.rect(0, 0, 0, 0).attr(options.dragBoxStyle).append(renderer.root)\r\n                } else {\r\n                    startAxesViewportChanging(zoomAndPan, \"pan\", e)\r\n                }\r\n            },\r\n            dragHandler: function(e) {\r\n                const rotated = chart.option(\"rotated\");\r\n                const options = zoomAndPan.options;\r\n                const actionData = zoomAndPan.actionData;\r\n                const isTouch = \"touch\" === e.pointerType;\r\n                e._cancelPreventDefault = true;\r\n                if (!actionData || isTouch && !zoomAndPan.panningVisualRangeEnabled()) {\r\n                    return\r\n                }\r\n                if (\"zoom\" === actionData.action) {\r\n                    preventDefaults(e);\r\n                    const curCanvas = actionData.curAxisRect;\r\n                    const startCoords = actionData.startCoords;\r\n                    const curCoords = getPointerCoord(curCanvas, e);\r\n                    const zoomArg = options.argumentAxis.zoom;\r\n                    const zoomVal = options.valueAxis.zoom;\r\n                    const rect = {\r\n                        x: _min(startCoords.x, curCoords.x),\r\n                        y: _min(startCoords.y, curCoords.y),\r\n                        width: _abs(startCoords.x - curCoords.x),\r\n                        height: _abs(startCoords.y - curCoords.y)\r\n                    };\r\n                    if (!zoomArg || !zoomVal) {\r\n                        if (!zoomArg && !rotated || !zoomVal && rotated) {\r\n                            rect.x = curCanvas.x;\r\n                            rect.width = curCanvas.width\r\n                        } else {\r\n                            rect.y = curCanvas.y;\r\n                            rect.height = curCanvas.height\r\n                        }\r\n                    }\r\n                    actionData.rect.attr(rect)\r\n                } else if (\"pan\" === actionData.action) {\r\n                    axesViewportChanging(zoomAndPan, \"pan\", e, calcOffsetForDrag, (e => e.offset));\r\n                    const deltaOffsetY = Math.abs(e.offset.y - actionData.offset.y);\r\n                    const deltaOffsetX = Math.abs(e.offset.x - actionData.offset.x);\r\n                    if (isTouch && (deltaOffsetY > 5 && deltaOffsetY > Math.abs(actionData.offset.x) || deltaOffsetX > 5 && deltaOffsetX > Math.abs(actionData.offset.y))) {\r\n                        return\r\n                    }\r\n                    preventDefaults(e)\r\n                }\r\n            },\r\n            dragEndHandler: function(e) {\r\n                const rotated = chart.option(\"rotated\");\r\n                const options = zoomAndPan.options;\r\n                const actionData = zoomAndPan.actionData;\r\n                const isTouch = \"touch\" === e.pointerType;\r\n                const getRange = _ref => {\r\n                    let {\r\n                        translator: translator,\r\n                        startCoord: startCoord,\r\n                        curCoord: curCoord\r\n                    } = _ref;\r\n                    return () => [translator.from(startCoord), translator.from(curCoord)]\r\n                };\r\n                const getCoords = (curCoords, startCoords, field) => ({\r\n                    curCoord: curCoords[field],\r\n                    startCoord: startCoords[field]\r\n                });\r\n                const needToZoom = (axisOption, coords) => axisOption.zoom && _abs(coords.curCoord - coords.startCoord) > 5;\r\n                const panIsEmpty = actionData && \"pan\" === actionData.action && !actionData.fallback && 0 === actionData.offset.x && 0 === actionData.offset.y;\r\n                if (!actionData || isTouch && !zoomAndPan.panningVisualRangeEnabled() || panIsEmpty) {\r\n                    return\r\n                }!isTouch && preventDefaults(e);\r\n                if (\"zoom\" === actionData.action) {\r\n                    const curCoords = getPointerCoord(actionData.curAxisRect, e);\r\n                    const argumentCoords = getCoords(curCoords, actionData.startCoords, rotated ? \"y\" : \"x\");\r\n                    const valueCoords = getCoords(curCoords, actionData.startCoords, rotated ? \"x\" : \"y\");\r\n                    const argumentAxesZoomed = needToZoom(options.argumentAxis, argumentCoords) && zoomAxes(e, chart._argumentAxes, getRange, true, argumentCoords, chart.getArgumentAxis());\r\n                    const valueAxesZoomed = needToZoom(options.valueAxis, valueCoords) && zoomAxes(e, actionData.valueAxes, getRange, true, valueCoords);\r\n                    if (valueAxesZoomed || argumentAxesZoomed) {\r\n                        chart._requestChange([\"VISUAL_RANGE\"])\r\n                    }\r\n                    actionData.rect.dispose()\r\n                } else if (\"pan\" === actionData.action) {\r\n                    finishAxesViewportChanging(zoomAndPan, \"pan\", e, calcOffsetForDrag)\r\n                }\r\n                zoomAndPan.actionData = null\r\n            },\r\n            pinchStartHandler: function(e) {\r\n                const actionData = prepareActionData(calcCenterForPinch(e), \"zoom\");\r\n                if (actionData.cancel) {\r\n                    cancelEvent(e);\r\n                    return\r\n                }\r\n                zoomAndPan.actionData = actionData;\r\n                startAxesViewportChanging(zoomAndPan, \"zoom\", e)\r\n            },\r\n            pinchHandler: function(e) {\r\n                if (!zoomAndPan.actionData) {\r\n                    return\r\n                }\r\n                axesViewportChanging(zoomAndPan, \"zoom\", e, ((e, actionData, coordField, scale) => calcCenterForPinch(e)[coordField] - actionData.center[coordField] + (actionData.center[coordField] - actionData.center[coordField] * scale)), calcCenterForPinch);\r\n                preventDefaults(e)\r\n            },\r\n            pinchEndHandler: function(e) {\r\n                if (!zoomAndPan.actionData) {\r\n                    return\r\n                }\r\n                finishAxesViewportChanging(zoomAndPan, \"zoom\", e, ((e, actionData, coordField, scale) => actionData.center[coordField] - actionData.startCenter[coordField] + (actionData.startCenter[coordField] - actionData.startCenter[coordField] * scale)));\r\n                zoomAndPan.actionData = null\r\n            },\r\n            mouseWheelHandler: function(e) {\r\n                const options = zoomAndPan.options;\r\n                const rotated = chart.option(\"rotated\");\r\n                const getRange = _ref2 => {\r\n                    let {\r\n                        translator: translator,\r\n                        coord: coord,\r\n                        scale: scale,\r\n                        axis: axis\r\n                    } = _ref2;\r\n                    return () => {\r\n                        const zoom = translator.zoom(-(coord - coord * scale), scale, axis.getZoomBounds());\r\n                        return {\r\n                            startValue: zoom.min,\r\n                            endValue: zoom.max\r\n                        }\r\n                    }\r\n                };\r\n                const coords = calcCenterForDrag(e);\r\n                let axesZoomed = false;\r\n                let targetAxes;\r\n                if (options.valueAxis.zoom) {\r\n                    targetAxes = chart._valueAxes.filter((axis => checkCoords(canvasToRect(axis.getCanvas()), coords)));\r\n                    if (0 === targetAxes.length) {\r\n                        const targetCanvas = chart._valueAxes.reduce(((r, axis) => {\r\n                            if (!r && axis.coordsIn(coords.x, coords.y)) {\r\n                                r = axis.getCanvas()\r\n                            }\r\n                            return r\r\n                        }), null);\r\n                        if (targetCanvas) {\r\n                            targetAxes = chart._valueAxes.filter((axis => checkCoords(canvasToRect(axis.getCanvas()), {\r\n                                x: targetCanvas.left,\r\n                                y: targetCanvas.top\r\n                            })))\r\n                        }\r\n                    }\r\n                    axesZoomed |= zoomAxes(e, targetAxes, getRange, e.delta > 0, {\r\n                        coord: rotated ? coords.x : coords.y\r\n                    })\r\n                }\r\n                if (options.argumentAxis.zoom) {\r\n                    const canZoom = chart._argumentAxes.some((axis => {\r\n                        if (checkCoords(canvasToRect(axis.getCanvas()), coords) || axis.coordsIn(coords.x, coords.y)) {\r\n                            return true\r\n                        }\r\n                        return false\r\n                    }));\r\n                    axesZoomed |= canZoom && zoomAxes(e, chart._argumentAxes, getRange, e.delta > 0, {\r\n                        coord: rotated ? coords.y : coords.x\r\n                    }, chart.getArgumentAxis())\r\n                }\r\n                if (axesZoomed) {\r\n                    chart._requestChange([\"VISUAL_RANGE\"]);\r\n                    if (targetAxes && isAxisAvailablePanning(targetAxes) || !targetAxes && zoomAndPan.panningVisualRangeEnabled()) {\r\n                        preventDefaults(e)\r\n                    }\r\n                }\r\n            },\r\n            cleanup: function() {\r\n                renderer.root.off(EVENTS_NS);\r\n                zoomAndPan.actionData && zoomAndPan.actionData.rect && zoomAndPan.actionData.rect.dispose();\r\n                zoomAndPan.actionData = null;\r\n                renderer.root.css({\r\n                    \"touch-action\": \"\"\r\n                })\r\n            },\r\n            setup: function(options) {\r\n                zoomAndPan.cleanup();\r\n                if (!options.argumentAxis.pan) {\r\n                    renderer.root.on(SCROLL_BAR_START_EVENT_NAME, cancelEvent)\r\n                }\r\n                if (options.argumentAxis.none && options.valueAxis.none) {\r\n                    return\r\n                }\r\n                zoomAndPan.options = options;\r\n                if ((options.argumentAxis.zoom || options.valueAxis.zoom) && options.allowMouseWheel) {\r\n                    renderer.root.on(wheelEvent + EVENTS_NS, zoomAndPan.mouseWheelHandler)\r\n                }\r\n                if ((options.argumentAxis.zoom || options.valueAxis.zoom) && options.allowTouchGestures) {\r\n                    renderer.root.on(PINCH_START_EVENT_NAME, {\r\n                        passive: false\r\n                    }, zoomAndPan.pinchStartHandler).on(PINCH_EVENT_NAME, {\r\n                        passive: false\r\n                    }, zoomAndPan.pinchHandler).on(PINCH_END_EVENT_NAME, zoomAndPan.pinchEndHandler)\r\n                }\r\n                renderer.root.on(DRAG_START_EVENT_NAME, {\r\n                    immediate: true,\r\n                    passive: false\r\n                }, zoomAndPan.dragStartHandler).on(DRAG_EVENT_NAME, {\r\n                    immediate: true,\r\n                    passive: false\r\n                }, zoomAndPan.dragHandler).on(DRAG_END_EVENT_NAME, zoomAndPan.dragEndHandler);\r\n                renderer.root.on(SCROLL_BAR_START_EVENT_NAME, (function(e) {\r\n                    zoomAndPan.actionData = {\r\n                        valueAxes: [],\r\n                        offset: {\r\n                            x: 0,\r\n                            y: 0\r\n                        },\r\n                        center: {\r\n                            x: 0,\r\n                            y: 0\r\n                        }\r\n                    };\r\n                    preventDefaults(e);\r\n                    startAxesViewportChanging(zoomAndPan, \"pan\", e)\r\n                })).on(SCROLL_BAR_MOVE_EVENT_NAME, (function(e) {\r\n                    preventDefaults(e);\r\n                    axesViewportChanging(zoomAndPan, \"pan\", e, calcOffsetForDrag, (e => e.offset))\r\n                })).on(SCROLL_BAR_END_EVENT_NAME, (function(e) {\r\n                    preventDefaults(e);\r\n                    finishAxesViewportChanging(zoomAndPan, \"pan\", e, calcOffsetForDrag);\r\n                    zoomAndPan.actionData = null\r\n                }))\r\n            },\r\n            panningVisualRangeEnabled: function() {\r\n                return isAxisAvailablePanning(chart._valueAxes) || isAxisAvailablePanning(chart._argumentAxes)\r\n            }\r\n        };\r\n        this._zoomAndPan = zoomAndPan\r\n    },\r\n    members: {\r\n        _setupZoomAndPan: function() {\r\n            this._zoomAndPan.setup(this._themeManager.getOptions(\"zoomAndPan\"))\r\n        }\r\n    },\r\n    dispose: function() {\r\n        this._zoomAndPan.cleanup()\r\n    },\r\n    customize: function(constructor) {\r\n        constructor.addChange({\r\n            code: \"ZOOM_AND_PAN\",\r\n            handler: function() {\r\n                this._setupZoomAndPan()\r\n            },\r\n            isThemeDependent: true,\r\n            isOptionChange: true,\r\n            option: \"zoomAndPan\"\r\n        })\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AACA;AAAA;AAGA;AAAA;AAGA;AAIA;AAAA;AAGA;AACA;AAAA;;;;;;;;AAKA,MAAM,YAAY;AAClB,MAAM,wBAAwB,yKAAA,CAAA,QAAc,GAAG;AAC/C,MAAM,kBAAkB,yKAAA,CAAA,OAAa,GAAG;AACxC,MAAM,sBAAsB,yKAAA,CAAA,MAAY,GAAG;AAC3C,MAAM,yBAAyB,6KAAA,CAAA,aAA0B,GAAG;AAC5D,MAAM,mBAAmB,6KAAA,CAAA,QAAqB,GAAG;AACjD,MAAM,uBAAuB,6KAAA,CAAA,WAAwB,GAAG;AACxD,MAAM,8BAA8B,qBAAqB;AACzD,MAAM,6BAA6B,oBAAoB;AACvD,MAAM,4BAA4B,mBAAmB;AACrD,MAAM,kBAAkB;AACxB,MAAM,iBAAiB;AACvB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,OAAO,KAAK,GAAG;AAErB,SAAS,aAAa,MAAM;IACxB,OAAO;QACH,GAAG,OAAO,IAAI;QACd,GAAG,OAAO,GAAG;QACb,OAAO,OAAO,KAAK,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK;QAChD,QAAQ,OAAO,MAAM,GAAG,OAAO,GAAG,GAAG,OAAO,MAAM;IACtD;AACJ;AAEA,SAAS,YAAY,IAAI,EAAE,MAAM;IAC7B,MAAM,IAAI,OAAO,CAAC;IAClB,MAAM,IAAI,OAAO,CAAC;IAClB,OAAO,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,KAAK,GAAG,KAAK,CAAC,IAAI,KAAK,KAAK,CAAC,IAAI,KAAK,KAAK,MAAM,GAAG,KAAK,CAAC;AAC9F;AAEA,SAAS,SAAS,IAAI,EAAE,gBAAgB;IACpC,IAAI,kBAAkB;QAClB,OAAO,KAAK,IAAI,CAAE,CAAC,GAAG;YAClB,IAAI,MAAM,kBAAkB;gBACxB,OAAO,CAAC;YACZ;YACA,IAAI,MAAM,kBAAkB;gBACxB,OAAO;YACX;YACA,OAAO;QACX;IACJ;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,IAAI;IACzB,OAAO,KAAK,MAAM,CAAE,CAAA,IAAK,CAAC,EAAE,aAAa,GAAG,gBAAgB,GAAG,OAAO;AAC1E;AAEA,SAAS,uBAAuB,IAAI;IAChC,OAAO,KAAK,IAAI,CAAE,CAAA,OAAQ,CAAC,KAAK,iBAAiB,CAAC,UAAU,CAAC,KAAK,iBAAiB,CAAC;AACxF;AAEA,SAAS,SAAS,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,aAAa,EAAE,WAAW,EAAE,KAAK,EAAE,CAAC;IACpF,MAAM,SAAS,oBAAoB,SAAS;IAC5C,MAAM,QAAQ,SAAS;IACvB,MAAM,EACF,iBAAiB,eAAe,EAChC,gBAAgB,cAAc,EACjC,GAAG,KAAK,8BAA8B,CAAC,aAAa,OAAO;IAC5D,MAAM,SAAS,KAAK,aAAa,CAAC,kBAAkB,OAAO,gBAAgB,cAAc,SAAS,GAAG;IACrG,mBAAmB,KAAK,aAAa;IACrC,OAAO;QACH,iBAAiB;QACjB,QAAQ;IACZ;AACJ;AAEA,SAAS,SAAS,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,MAAM,EAAE,gBAAgB;IAC/D,OAAO,SAAS,MAAM;IACtB,IAAI,cAAc;IAClB,MAAM,gBAAgB,CAAA,SAAU,CAAC;YAC7B,OAAO,CAAC,CAAC;YACT,KAAK,CAAC,CAAC;QACX,CAAC;IACD,gBAAgB,MAAM,IAAI,CAAE,CAAA;QACxB,MAAM,aAAa,KAAK,aAAa;QACrC,MAAM,QAAQ,WAAW,WAAW,CAAC;QACrC,MAAM,EACF,iBAAiB,eAAe,EAChC,QAAQ,MAAM,EACjB,GAAG,SAAS,MAAM,kBAAkB,SAAS,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE;YACnD,OAAO;YACP,YAAY;YACZ,MAAM;QACV,GAAG,UAAU,eAAe,QAAQ,OAAO;QAC3C,cAAc,CAAC;QACf,OAAO,oBAAoB,OAAO,WAAW;IACjD;IACA,OAAO;AACX;AAEA,SAAS,YAAY,CAAC;IAClB,IAAI,EAAE,aAAa,EAAE;QACjB,YAAY,EAAE,aAAa;IAC/B;IACA,IAAI,UAAU,EAAE,UAAU,EAAE;QACxB,EAAE,MAAM,GAAG;IACf;AACJ;uCACe;IACX,MAAM;IACN,MAAM;QACF,MAAM,QAAQ,IAAI;QAClB,MAAM,WAAW,IAAI,CAAC,SAAS;QAE/B,SAAS,YAAY,UAAU,EAAE,WAAW;YACxC,IAAI,OAAO,EAAE;YACb,MAAM,UAAU,WAAW,OAAO;YAClC,MAAM,aAAa,WAAW,UAAU;YACxC,IAAI,QAAQ,YAAY,CAAC,YAAY,EAAE;gBACnC,KAAK,IAAI,CAAC,MAAM,eAAe;YACnC;YACA,IAAI,QAAQ,SAAS,CAAC,YAAY,EAAE;gBAChC,OAAO,KAAK,MAAM,CAAC,WAAW,SAAS;YAC3C;YACA,OAAO;QACX;QAEA,SAAS,0BAA0B,UAAU,EAAE,WAAW,EAAE,CAAC;YACzD,MAAM,OAAO,YAAY,YAAY;YACrC,gBAAgB,MAAM,IAAI,CAAE,CAAA,OAAQ,KAAK,aAAa,CAAC,MAAM;oBACzD,KAAK;gBACT,GAAG,GAAG,aAAa,WAAW,KAAM,YAAY;QACpD;QAEA,SAAS,qBAAqB,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU,EAAE,UAAU;YAC5E,SAAS,SAAS,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,EAAE,UAAU;gBACvD,IAAI,OAAO;oBACP,QAAQ;gBACZ;gBACA,YAAY,gBAAgB,MAAM,OAAO,CAAE,CAAA;oBACvC,MAAM,UAAU,KAAK,UAAU;oBAC/B,MAAM,WAAW,KAAK,WAAW;oBACjC,MAAM,QAAQ,KAAK,aAAa,GAAG,aAAa,CAAC;oBACjD,MAAM,YAAY,CAAC,WAAW,GAAG,YAAY,YAAY;oBACzD,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,MAAM,KAAK,aAAa,GAAG,IAAI,CAAC,WAAW,OAAO,KAAK,aAAa;oBACxF,MAAM,QAAQ,KAAK,WAAW,CAAC,CAAA,GAAA,4JAAA,CAAA,oBAAiB,AAAD,EAAE;wBAAC,KAAK,GAAG;wBAAE,KAAK,GAAG;qBAAC;oBACrE,MAAM,EACF,iBAAiB,eAAe,EAChC,gBAAgB,cAAc,EACjC,GAAG,KAAK,8BAA8B,CAAC,aAAa,OAAO;oBAC5D,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa,SAAS,UAAU,CAAC,OAAO,OAAO,eAAe,UAAU,CAAC,OAAO,MAAM,SAAS,QAAQ,CAAC,OAAO,OAAO,eAAe,QAAQ,CAAC,OAAO,IAAI;wBACpK,KAAK,aAAa,CAAC,kBAAkB,OAAO,gBAAgB;4BACxD,OAAO;4BACP,KAAK;wBACT,GAAG,GAAG;wBACN,IAAI,CAAC,iBAAiB;4BAClB,KAAK,MAAM,GAAG;4BACd,KAAK,cAAc,GAAG,YAAY,KAAK,SAAS;wBACpD;oBACJ,OAAO,IAAI,YAAY,EAAE,WAAW,IAAI,eAAe,QAAQ,IAAI,EAAE;wBACjE,MAAM,gBAAgB,KAAK,iBAAiB,CAAC;wBAC7C,MAAM,gBAAgB,KAAK,iBAAiB,CAAC;wBAC7C,MAAM,gBAAgB,QAAQ,KAAK,CAAC;wBACpC,MAAM,iBAAiB,QAAQ,KAAK,CAAC,CAAC,iBAAiB,CAAC,aAAa;wBACrE,MAAM,iBAAiB,MAAM,SAAS,CAAC,CAAC,iBAAiB,CAAC,YAAY,KAAK,CAAC,QAAQ,QAAQ,IAAI,YAAY,KAAK,QAAQ,QAAQ,KAAK,iBAAiB,CAAC,YAAY,KAAK,CAAC,QAAQ,QAAQ,IAAI,YAAY,KAAK,QAAQ,QAAQ,CAAC;wBAChO,KAAK,OAAO,GAAG,iBAAiB,kBAAkB;oBACtD;gBACJ;gBACA,OAAO;YACX;YAEA,SAAS,YAAY,CAAC,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU;gBAChD,IAAI,KAAK,MAAM,EAAE;oBACb,WAAW,MAAM,CAAC,WAAW,GAAG,CAAC,EAAE,MAAM,GAAG,EAAE,MAAM,CAAC,WAAW,GAAG,WAAW,MAAM,CAAC,WAAW,IAAI,KAAK,cAAc;gBAC3H;YACJ;YAEA,SAAS,YAAY,MAAM,EAAE,UAAU,EAAE,IAAI,EAAE,UAAU;gBACrD,IAAI,KAAK,MAAM,EAAE;oBACb,WAAW,MAAM,CAAC,WAAW,GAAG,MAAM,CAAC,WAAW,GAAG,KAAK,cAAc;gBAC5E;YACJ;YACA,MAAM,UAAU,MAAM,MAAM,CAAC;YAC7B,MAAM,aAAa,WAAW,UAAU;YACxC,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,UAAU,CAAC;YACf,IAAI,UAAU,CAAC;YACf,IAAI,CAAC,WAAW,QAAQ,EAAE;gBACtB,UAAU,SAAS,MAAM,aAAa,EAAE,QAAQ,YAAY,CAAC,YAAY,EAAE,UAAU,MAAM,KAAK,GAAG;gBACnG,UAAU,SAAS,WAAW,SAAS,EAAE,QAAQ,SAAS,CAAC,YAAY,EAAE,UAAU,MAAM,KAAK,GAAG;gBACjG,MAAM,cAAc,CAAC;oBAAC;iBAAe;gBACrC,YAAY,GAAG,YAAY,SAAS,UAAU,MAAM;gBACpD,YAAY,GAAG,YAAY,SAAS,UAAU,MAAM;YACxD;YACA,MAAM,SAAS,WAAW;YAC1B,YAAY,QAAQ,YAAY,SAAS,UAAU,MAAM;YACzD,YAAY,QAAQ,YAAY,SAAS,UAAU,MAAM;YACzD,IAAI,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ,MAAM,EAAE;gBACpC,WAAW,MAAM,GAAG;YACxB;YACA,OAAO,QAAQ,MAAM,IAAI,QAAQ,MAAM,IAAI,WAAW,QAAQ,IAAI,QAAQ,OAAO,IAAI,QAAQ,OAAO;QACxG;QAEA,SAAS,2BAA2B,UAAU,EAAE,WAAW,EAAE,CAAC,EAAE,UAAU;YACtE,SAAS,SAAS,IAAI,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB;gBAC5D,IAAI,cAAc;gBAClB,MAAM,QAAQ,EAAE,KAAK,IAAI;gBACzB,MAAM,WAAW,CAAA;oBACb,MAAM,OAAO,KAAK,aAAa,GAAG,IAAI,CAAC,CAAC,WAAW,GAAG,YAAY,YAAY,QAAQ,OAAO,KAAK,aAAa;oBAC/G,OAAO;wBACH,YAAY,KAAK,GAAG;wBACpB,UAAU,KAAK,GAAG;oBACtB;gBACJ;gBACA,MAAM,gBAAgB,CAAA,SAAU,CAAC;wBAC7B,OAAO;wBACP,KAAK;oBACT,CAAC;gBACD,gBAAgB,MAAM,OAAO,CAAE,CAAA;oBAC3B,cAAc,CAAC,SAAS,MAAM,kBAAkB,UAAU,eAAe,aAAa,OAAO,GAAG,eAAe;gBACnH;gBACA,OAAO;YACX;YACA,MAAM,UAAU,MAAM,MAAM,CAAC;YAC7B,MAAM,aAAa,WAAW,UAAU;YACxC,MAAM,UAAU,WAAW,OAAO;YAClC,IAAI,cAAc;YAClB,IAAI,WAAW,QAAQ,EAAE;gBACrB,eAAe,QAAQ,YAAY,CAAC,YAAY,IAAI,SAAS,MAAM,aAAa,EAAE,UAAU,MAAM,KAAK,YAAY,MAAM,eAAe;gBACxI,eAAe,QAAQ,SAAS,CAAC,YAAY,IAAI,SAAS,WAAW,SAAS,EAAE,UAAU,MAAM,KAAK;YACzG,OAAO;gBACH,MAAM,OAAO,YAAY,YAAY;gBACrC,gBAAgB,MAAM,OAAO,CAAE,CAAA;oBAC3B,KAAK,aAAa,CAAC,MAAM;wBACrB,OAAO;oBACX,GAAG,GAAG;gBACV;gBACA,cAAc,KAAK,MAAM;YAC7B;YACA,eAAe,MAAM,cAAc,CAAC;gBAAC;aAAe;QACxD;QAEA,SAAS,kBAAkB,MAAM,EAAE,MAAM;YACrC,MAAM,OAAO,MAAM,aAAa,CAAC,MAAM,CAAE,CAAA,OAAQ,YAAY,aAAa,KAAK,SAAS,KAAK;YAC7F,OAAO;gBACH,UAAU,MAAM,kBAAkB,GAAG;gBACrC,QAAQ,CAAC,KAAK,MAAM,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;gBACnC,QAAQ;gBACR,aAAa,KAAK,MAAM,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC,SAAS;gBAC1D,WAAW,KAAK,MAAM,IAAI,MAAM,UAAU,CAAC,MAAM,CAAE,CAAA,OAAQ,YAAY,aAAa,KAAK,SAAS,KAAK;gBACvG,QAAQ;oBACJ,GAAG;oBACH,GAAG;gBACP;gBACA,QAAQ;gBACR,aAAa;YACjB;QACJ;QAEA,SAAS,gBAAgB,IAAI,EAAE,CAAC;YAC5B,MAAM,aAAa,SAAS,aAAa;YACzC,OAAO;gBACH,GAAG,KAAK,KAAK,EAAE,KAAK,GAAG,WAAW,IAAI,EAAE,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG,KAAK,CAAC;gBACpE,GAAG,KAAK,KAAK,EAAE,KAAK,GAAG,WAAW,GAAG,EAAE,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG,KAAK,CAAC;YACxE;QACJ;QAEA,SAAS,mBAAmB,CAAC;YACzB,MAAM,aAAa,SAAS,aAAa;YACzC,MAAM,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC9B,MAAM,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC9B,MAAM,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC9B,MAAM,KAAK,EAAE,QAAQ,CAAC,EAAE,CAAC,KAAK;YAC9B,OAAO;gBACH,GAAG,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,IAAI,WAAW,IAAI;gBACrD,GAAG,KAAK,IAAI,MAAM,KAAK,KAAK,MAAM,IAAI,WAAW,GAAG;YACxD;QACJ;QAEA,SAAS,kBAAkB,CAAC;YACxB,MAAM,aAAa,SAAS,aAAa;YACzC,OAAO;gBACH,GAAG,EAAE,KAAK,GAAG,WAAW,IAAI;gBAC5B,GAAG,EAAE,KAAK,GAAG,WAAW,GAAG;YAC/B;QACJ;QAEA,SAAS,kBAAkB,CAAC,EAAE,UAAU,EAAE,UAAU;YAChD,OAAO,EAAE,MAAM,CAAC,WAAW,GAAG,WAAW,MAAM,CAAC,WAAW;QAC/D;QAEA,SAAS,gBAAgB,CAAC;YACtB,IAAI,UAAU,EAAE,UAAU,EAAE;gBACxB,EAAE,cAAc;gBAChB,EAAE,eAAe;YACrB;YACA,MAAM,oBAAoB;QAC9B;QACA,MAAM,aAAa;YACf,kBAAkB,SAAS,CAAC;gBACxB,MAAM,UAAU,WAAW,OAAO;gBAClC,MAAM,UAAU,YAAY,EAAE,WAAW;gBACzC,MAAM,UAAU,QAAQ,YAAY,CAAC,GAAG,IAAI,QAAQ,SAAS,CAAC,GAAG;gBACjE,MAAM,WAAW,QAAQ,YAAY,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,IAAI;gBACpE,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,KAAK,CAAC,CAAC,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,MAAM,IAAI,MAAM;gBAC3F,MAAM,aAAa,QAAQ,UAAU;gBACrC,IAAI;gBACJ,EAAE,qBAAqB,GAAG;gBAC1B,IAAI,SAAS;oBACT,IAAI,QAAQ,kBAAkB,IAAI,SAAS;wBACvC,MAAM,gBAAgB,CAAC,WAAW,yBAAyB,MAAM,WAAW,SAAS;wBACrF,SAAS,gBAAgB,OAAO;oBACpC;gBACJ,OAAO,IAAI,cAAc,WAAW,iBAAiB,CAAC,cAAc,SAAS;oBACzE,SAAS;gBACb,OAAO,IAAI,cAAc,UAAU;oBAC/B,SAAS;gBACb;gBACA,MAAM,aAAa,kBAAkB,kBAAkB,IAAI;gBAC3D,IAAI,WAAW,MAAM,EAAE;oBACnB,WAAW,SAAS,GAAG;oBACvB,IAAI,UAAU,EAAE,UAAU,EAAE;wBACxB,EAAE,MAAM,GAAG;oBACf;oBACA;gBACJ;gBACA,WAAW,UAAU,GAAG;gBACxB,IAAI,WAAW,QAAQ;oBACnB,WAAW,WAAW,GAAG,gBAAgB,WAAW,WAAW,EAAE;oBACjE,WAAW,IAAI,GAAG,SAAS,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC,QAAQ,YAAY,EAAE,MAAM,CAAC,SAAS,IAAI;gBAC/F,OAAO;oBACH,0BAA0B,YAAY,OAAO;gBACjD;YACJ;YACA,aAAa,SAAS,CAAC;gBACnB,MAAM,UAAU,MAAM,MAAM,CAAC;gBAC7B,MAAM,UAAU,WAAW,OAAO;gBAClC,MAAM,aAAa,WAAW,UAAU;gBACxC,MAAM,UAAU,YAAY,EAAE,WAAW;gBACzC,EAAE,qBAAqB,GAAG;gBAC1B,IAAI,CAAC,cAAc,WAAW,CAAC,WAAW,yBAAyB,IAAI;oBACnE;gBACJ;gBACA,IAAI,WAAW,WAAW,MAAM,EAAE;oBAC9B,gBAAgB;oBAChB,MAAM,YAAY,WAAW,WAAW;oBACxC,MAAM,cAAc,WAAW,WAAW;oBAC1C,MAAM,YAAY,gBAAgB,WAAW;oBAC7C,MAAM,UAAU,QAAQ,YAAY,CAAC,IAAI;oBACzC,MAAM,UAAU,QAAQ,SAAS,CAAC,IAAI;oBACtC,MAAM,OAAO;wBACT,GAAG,KAAK,YAAY,CAAC,EAAE,UAAU,CAAC;wBAClC,GAAG,KAAK,YAAY,CAAC,EAAE,UAAU,CAAC;wBAClC,OAAO,KAAK,YAAY,CAAC,GAAG,UAAU,CAAC;wBACvC,QAAQ,KAAK,YAAY,CAAC,GAAG,UAAU,CAAC;oBAC5C;oBACA,IAAI,CAAC,WAAW,CAAC,SAAS;wBACtB,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,WAAW,SAAS;4BAC7C,KAAK,CAAC,GAAG,UAAU,CAAC;4BACpB,KAAK,KAAK,GAAG,UAAU,KAAK;wBAChC,OAAO;4BACH,KAAK,CAAC,GAAG,UAAU,CAAC;4BACpB,KAAK,MAAM,GAAG,UAAU,MAAM;wBAClC;oBACJ;oBACA,WAAW,IAAI,CAAC,IAAI,CAAC;gBACzB,OAAO,IAAI,UAAU,WAAW,MAAM,EAAE;oBACpC,qBAAqB,YAAY,OAAO,GAAG,mBAAoB,CAAA,IAAK,EAAE,MAAM;oBAC5E,MAAM,eAAe,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC;oBAC9D,MAAM,eAAe,KAAK,GAAG,CAAC,EAAE,MAAM,CAAC,CAAC,GAAG,WAAW,MAAM,CAAC,CAAC;oBAC9D,IAAI,WAAW,CAAC,eAAe,KAAK,eAAe,KAAK,GAAG,CAAC,WAAW,MAAM,CAAC,CAAC,KAAK,eAAe,KAAK,eAAe,KAAK,GAAG,CAAC,WAAW,MAAM,CAAC,CAAC,CAAC,GAAG;wBACnJ;oBACJ;oBACA,gBAAgB;gBACpB;YACJ;YACA,gBAAgB,SAAS,CAAC;gBACtB,MAAM,UAAU,MAAM,MAAM,CAAC;gBAC7B,MAAM,UAAU,WAAW,OAAO;gBAClC,MAAM,aAAa,WAAW,UAAU;gBACxC,MAAM,UAAU,YAAY,EAAE,WAAW;gBACzC,MAAM,WAAW,CAAA;oBACb,IAAI,EACA,YAAY,UAAU,EACtB,YAAY,UAAU,EACtB,UAAU,QAAQ,EACrB,GAAG;oBACJ,OAAO,IAAM;4BAAC,WAAW,IAAI,CAAC;4BAAa,WAAW,IAAI,CAAC;yBAAU;gBACzE;gBACA,MAAM,YAAY,CAAC,WAAW,aAAa,QAAU,CAAC;wBAClD,UAAU,SAAS,CAAC,MAAM;wBAC1B,YAAY,WAAW,CAAC,MAAM;oBAClC,CAAC;gBACD,MAAM,aAAa,CAAC,YAAY,SAAW,WAAW,IAAI,IAAI,KAAK,OAAO,QAAQ,GAAG,OAAO,UAAU,IAAI;gBAC1G,MAAM,aAAa,cAAc,UAAU,WAAW,MAAM,IAAI,CAAC,WAAW,QAAQ,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC,IAAI,MAAM,WAAW,MAAM,CAAC,CAAC;gBAC9I,IAAI,CAAC,cAAc,WAAW,CAAC,WAAW,yBAAyB,MAAM,YAAY;oBACjF;gBACJ;gBAAC,CAAC,WAAW,gBAAgB;gBAC7B,IAAI,WAAW,WAAW,MAAM,EAAE;oBAC9B,MAAM,YAAY,gBAAgB,WAAW,WAAW,EAAE;oBAC1D,MAAM,iBAAiB,UAAU,WAAW,WAAW,WAAW,EAAE,UAAU,MAAM;oBACpF,MAAM,cAAc,UAAU,WAAW,WAAW,WAAW,EAAE,UAAU,MAAM;oBACjF,MAAM,qBAAqB,WAAW,QAAQ,YAAY,EAAE,mBAAmB,SAAS,GAAG,MAAM,aAAa,EAAE,UAAU,MAAM,gBAAgB,MAAM,eAAe;oBACrK,MAAM,kBAAkB,WAAW,QAAQ,SAAS,EAAE,gBAAgB,SAAS,GAAG,WAAW,SAAS,EAAE,UAAU,MAAM;oBACxH,IAAI,mBAAmB,oBAAoB;wBACvC,MAAM,cAAc,CAAC;4BAAC;yBAAe;oBACzC;oBACA,WAAW,IAAI,CAAC,OAAO;gBAC3B,OAAO,IAAI,UAAU,WAAW,MAAM,EAAE;oBACpC,2BAA2B,YAAY,OAAO,GAAG;gBACrD;gBACA,WAAW,UAAU,GAAG;YAC5B;YACA,mBAAmB,SAAS,CAAC;gBACzB,MAAM,aAAa,kBAAkB,mBAAmB,IAAI;gBAC5D,IAAI,WAAW,MAAM,EAAE;oBACnB,YAAY;oBACZ;gBACJ;gBACA,WAAW,UAAU,GAAG;gBACxB,0BAA0B,YAAY,QAAQ;YAClD;YACA,cAAc,SAAS,CAAC;gBACpB,IAAI,CAAC,WAAW,UAAU,EAAE;oBACxB;gBACJ;gBACA,qBAAqB,YAAY,QAAQ,GAAI,CAAC,GAAG,YAAY,YAAY,QAAU,mBAAmB,EAAE,CAAC,WAAW,GAAG,WAAW,MAAM,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,CAAC,WAAW,GAAG,WAAW,MAAM,CAAC,WAAW,GAAG,KAAK,GAAI;gBACjO,gBAAgB;YACpB;YACA,iBAAiB,SAAS,CAAC;gBACvB,IAAI,CAAC,WAAW,UAAU,EAAE;oBACxB;gBACJ;gBACA,2BAA2B,YAAY,QAAQ,GAAI,CAAC,GAAG,YAAY,YAAY,QAAU,WAAW,MAAM,CAAC,WAAW,GAAG,WAAW,WAAW,CAAC,WAAW,GAAG,CAAC,WAAW,WAAW,CAAC,WAAW,GAAG,WAAW,WAAW,CAAC,WAAW,GAAG,KAAK;gBAC9O,WAAW,UAAU,GAAG;YAC5B;YACA,mBAAmB,SAAS,CAAC;gBACzB,MAAM,UAAU,WAAW,OAAO;gBAClC,MAAM,UAAU,MAAM,MAAM,CAAC;gBAC7B,MAAM,WAAW,CAAA;oBACb,IAAI,EACA,YAAY,UAAU,EACtB,OAAO,KAAK,EACZ,OAAO,KAAK,EACZ,MAAM,IAAI,EACb,GAAG;oBACJ,OAAO;wBACH,MAAM,OAAO,WAAW,IAAI,CAAC,CAAC,CAAC,QAAQ,QAAQ,KAAK,GAAG,OAAO,KAAK,aAAa;wBAChF,OAAO;4BACH,YAAY,KAAK,GAAG;4BACpB,UAAU,KAAK,GAAG;wBACtB;oBACJ;gBACJ;gBACA,MAAM,SAAS,kBAAkB;gBACjC,IAAI,aAAa;gBACjB,IAAI;gBACJ,IAAI,QAAQ,SAAS,CAAC,IAAI,EAAE;oBACxB,aAAa,MAAM,UAAU,CAAC,MAAM,CAAE,CAAA,OAAQ,YAAY,aAAa,KAAK,SAAS,KAAK;oBAC1F,IAAI,MAAM,WAAW,MAAM,EAAE;wBACzB,MAAM,eAAe,MAAM,UAAU,CAAC,MAAM,CAAE,CAAC,GAAG;4BAC9C,IAAI,CAAC,KAAK,KAAK,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG;gCACzC,IAAI,KAAK,SAAS;4BACtB;4BACA,OAAO;wBACX,GAAI;wBACJ,IAAI,cAAc;4BACd,aAAa,MAAM,UAAU,CAAC,MAAM,CAAE,CAAA,OAAQ,YAAY,aAAa,KAAK,SAAS,KAAK;oCACtF,GAAG,aAAa,IAAI;oCACpB,GAAG,aAAa,GAAG;gCACvB;wBACJ;oBACJ;oBACA,cAAc,SAAS,GAAG,YAAY,UAAU,EAAE,KAAK,GAAG,GAAG;wBACzD,OAAO,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC;oBACxC;gBACJ;gBACA,IAAI,QAAQ,YAAY,CAAC,IAAI,EAAE;oBAC3B,MAAM,UAAU,MAAM,aAAa,CAAC,IAAI,CAAE,CAAA;wBACtC,IAAI,YAAY,aAAa,KAAK,SAAS,KAAK,WAAW,KAAK,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,CAAC,GAAG;4BAC1F,OAAO;wBACX;wBACA,OAAO;oBACX;oBACA,cAAc,WAAW,SAAS,GAAG,MAAM,aAAa,EAAE,UAAU,EAAE,KAAK,GAAG,GAAG;wBAC7E,OAAO,UAAU,OAAO,CAAC,GAAG,OAAO,CAAC;oBACxC,GAAG,MAAM,eAAe;gBAC5B;gBACA,IAAI,YAAY;oBACZ,MAAM,cAAc,CAAC;wBAAC;qBAAe;oBACrC,IAAI,cAAc,uBAAuB,eAAe,CAAC,cAAc,WAAW,yBAAyB,IAAI;wBAC3G,gBAAgB;oBACpB;gBACJ;YACJ;YACA,SAAS;gBACL,SAAS,IAAI,CAAC,GAAG,CAAC;gBAClB,WAAW,UAAU,IAAI,WAAW,UAAU,CAAC,IAAI,IAAI,WAAW,UAAU,CAAC,IAAI,CAAC,OAAO;gBACzF,WAAW,UAAU,GAAG;gBACxB,SAAS,IAAI,CAAC,GAAG,CAAC;oBACd,gBAAgB;gBACpB;YACJ;YACA,OAAO,SAAS,OAAO;gBACnB,WAAW,OAAO;gBAClB,IAAI,CAAC,QAAQ,YAAY,CAAC,GAAG,EAAE;oBAC3B,SAAS,IAAI,CAAC,EAAE,CAAC,6BAA6B;gBAClD;gBACA,IAAI,QAAQ,YAAY,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,IAAI,EAAE;oBACrD;gBACJ;gBACA,WAAW,OAAO,GAAG;gBACrB,IAAI,CAAC,QAAQ,YAAY,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,IAAI,KAAK,QAAQ,eAAe,EAAE;oBAClF,SAAS,IAAI,CAAC,EAAE,CAAC,kLAAA,CAAA,OAAU,GAAG,WAAW,WAAW,iBAAiB;gBACzE;gBACA,IAAI,CAAC,QAAQ,YAAY,CAAC,IAAI,IAAI,QAAQ,SAAS,CAAC,IAAI,KAAK,QAAQ,kBAAkB,EAAE;oBACrF,SAAS,IAAI,CAAC,EAAE,CAAC,wBAAwB;wBACrC,SAAS;oBACb,GAAG,WAAW,iBAAiB,EAAE,EAAE,CAAC,kBAAkB;wBAClD,SAAS;oBACb,GAAG,WAAW,YAAY,EAAE,EAAE,CAAC,sBAAsB,WAAW,eAAe;gBACnF;gBACA,SAAS,IAAI,CAAC,EAAE,CAAC,uBAAuB;oBACpC,WAAW;oBACX,SAAS;gBACb,GAAG,WAAW,gBAAgB,EAAE,EAAE,CAAC,iBAAiB;oBAChD,WAAW;oBACX,SAAS;gBACb,GAAG,WAAW,WAAW,EAAE,EAAE,CAAC,qBAAqB,WAAW,cAAc;gBAC5E,SAAS,IAAI,CAAC,EAAE,CAAC,6BAA8B,SAAS,CAAC;oBACrD,WAAW,UAAU,GAAG;wBACpB,WAAW,EAAE;wBACb,QAAQ;4BACJ,GAAG;4BACH,GAAG;wBACP;wBACA,QAAQ;4BACJ,GAAG;4BACH,GAAG;wBACP;oBACJ;oBACA,gBAAgB;oBAChB,0BAA0B,YAAY,OAAO;gBACjD,GAAI,EAAE,CAAC,4BAA6B,SAAS,CAAC;oBAC1C,gBAAgB;oBAChB,qBAAqB,YAAY,OAAO,GAAG,mBAAoB,CAAA,IAAK,EAAE,MAAM;gBAChF,GAAI,EAAE,CAAC,2BAA4B,SAAS,CAAC;oBACzC,gBAAgB;oBAChB,2BAA2B,YAAY,OAAO,GAAG;oBACjD,WAAW,UAAU,GAAG;gBAC5B;YACJ;YACA,2BAA2B;gBACvB,OAAO,uBAAuB,MAAM,UAAU,KAAK,uBAAuB,MAAM,aAAa;YACjG;QACJ;QACA,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,SAAS;QACL,kBAAkB;YACd,IAAI,CAAC,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,CAAC;QACzD;IACJ;IACA,SAAS;QACL,IAAI,CAAC,WAAW,CAAC,OAAO;IAC5B;IACA,WAAW,SAAS,WAAW;QAC3B,YAAY,SAAS,CAAC;YAClB,MAAM;YACN,SAAS;gBACL,IAAI,CAAC,gBAAgB;YACzB;YACA,kBAAkB;YAClB,gBAAgB;YAChB,QAAQ;QACZ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1942, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart_components/tracker.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart_components/tracker.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport domAdapter from \"../../core/dom_adapter\";\r\nimport eventsEngine from \"../../common/core/events/core/events_engine\";\r\nimport {\r\n    name as clickEventName\r\n} from \"../../common/core/events/click\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    each as _each\r\n} from \"../../core/utils/iterator\";\r\nimport consts from \"../components/consts\";\r\nimport {\r\n    getDistance,\r\n    pointInCanvas as inCanvas,\r\n    normalizeEnum as _normalizeEnum\r\n} from \"../core/utils\";\r\nimport pointerEvents from \"../../common/core/events/pointer\";\r\nimport {\r\n    addNamespace\r\n} from \"../../common/core/events/utils/index\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    noop as _noop\r\n} from \"../../core/utils/common\";\r\nconst _floor = Math.floor;\r\nconst eventsConsts = consts.events;\r\nconst statesConsts = consts.states;\r\nconst HOVER_STATE = statesConsts.hoverMark;\r\nconst NORMAL_STATE = statesConsts.normalMark;\r\nconst EVENT_NS = \"dxChartTracker\";\r\nconst DOT_EVENT_NS = \".\" + EVENT_NS;\r\nconst POINTER_ACTION = addNamespace([pointerEvents.down, pointerEvents.move], EVENT_NS);\r\nconst LEGEND_CLICK = \"legendClick\";\r\nconst SERIES_CLICK = \"seriesClick\";\r\nconst POINT_CLICK = \"pointClick\";\r\nconst POINT_DATA = \"chart-data-point\";\r\nconst SERIES_DATA = \"chart-data-series\";\r\nconst ARG_DATA = \"chart-data-argument\";\r\nconst DELAY = 100;\r\nconst HOLD_TIMEOUT = 300;\r\nconst NONE_MODE = \"none\";\r\nconst ALL_ARGUMENT_POINTS_MODE = \"allargumentpoints\";\r\nconst INCLUDE_POINTS_MODE = \"includepoints\";\r\nconst EXLUDE_POINTS_MODE = \"excludepoints\";\r\nconst LEGEND_HOVER_MODES = [\"includepoints\", \"excludepoints\", \"none\"];\r\n\r\nfunction getData(event, dataKey, tryCheckParent) {\r\n    const target = event.target;\r\n    if (\"tspan\" === target.tagName) {\r\n        return target.parentNode[dataKey]\r\n    }\r\n    const data = target[dataKey];\r\n    if (tryCheckParent && !isDefined(data)) {\r\n        const getParentData = function(node) {\r\n            if (node.parentNode) {\r\n                if (isDefined(node.parentNode[dataKey])) {\r\n                    return node.parentNode[dataKey]\r\n                } else {\r\n                    return getParentData(node.parentNode)\r\n                }\r\n            }\r\n            return\r\n        };\r\n        return getParentData(target)\r\n    }\r\n    return data\r\n}\r\n\r\nfunction eventCanceled(_ref, target) {\r\n    let {\r\n        cancel: cancel\r\n    } = _ref;\r\n    return cancel || !target.getOptions()\r\n}\r\n\r\nfunction correctLegendHoverMode(mode) {\r\n    if (LEGEND_HOVER_MODES.indexOf(mode) > -1) {\r\n        return mode\r\n    } else {\r\n        return \"includepoints\"\r\n    }\r\n}\r\n\r\nfunction correctHoverMode(target) {\r\n    const mode = target.getOptions().hoverMode;\r\n    return \"none\" === mode ? mode : \"allargumentpoints\"\r\n}\r\nconst baseTrackerPrototype = {\r\n    ctor: function(options) {\r\n        const that = this;\r\n        const data = {\r\n            tracker: that\r\n        };\r\n        that._renderer = options.renderer;\r\n        that._legend = options.legend;\r\n        that._tooltip = options.tooltip;\r\n        that._eventTrigger = options.eventTrigger;\r\n        that._seriesGroup = options.seriesGroup;\r\n        options.seriesGroup.off(DOT_EVENT_NS).on(addNamespace(eventsConsts.showPointTooltip, EVENT_NS), data, that._showPointTooltip).on(addNamespace(eventsConsts.hidePointTooltip, EVENT_NS), data, that._hidePointTooltip);\r\n        that._renderer.root.off(DOT_EVENT_NS).on(POINTER_ACTION, data, that._pointerHandler).on(addNamespace(pointerEvents.up, EVENT_NS), (() => clearTimeout(that._holdTimer))).on(addNamespace(clickEventName, EVENT_NS), data, that._clickHandler)\r\n    },\r\n    update: function(options) {\r\n        this._chart = options.chart\r\n    },\r\n    updateSeries(series, resetDecorations) {\r\n        const that = this;\r\n        const noHoveredSeries = !(null !== series && void 0 !== series && series.some((s => s === that.hoveredSeries)) || that._hoveredPoint && that._hoveredPoint.series);\r\n        if (that._storedSeries !== series) {\r\n            that._storedSeries = series || []\r\n        }\r\n        if (noHoveredSeries) {\r\n            that._clean();\r\n            that._renderer.initDefsElements()\r\n        }\r\n        if (resetDecorations) {\r\n            that.clearSelection();\r\n            if (!noHoveredSeries) {\r\n                that._hideTooltip(that.pointAtShownTooltip);\r\n                that.clearHover()\r\n            }\r\n        }\r\n    },\r\n    setCanvases: function(mainCanvas, paneCanvases) {\r\n        this._mainCanvas = mainCanvas;\r\n        this._canvases = paneCanvases\r\n    },\r\n    repairTooltip: function() {\r\n        const point = this.pointAtShownTooltip;\r\n        if (!point || !point.series || !point.isVisible()) {\r\n            this._hideTooltip(point, true)\r\n        } else {\r\n            this._showTooltip(point)\r\n        }\r\n    },\r\n    _setHoveredPoint: function(point) {\r\n        if (point === this._hoveredPoint) {\r\n            return\r\n        }\r\n        this._releaseHoveredPoint();\r\n        point.hover();\r\n        this._hoveredPoint = point\r\n    },\r\n    _releaseHoveredPoint: function(isPointerOut) {\r\n        if (this._hoveredPoint && this._hoveredPoint.getOptions()) {\r\n            this._hoveredPoint.clearHover();\r\n            this._hoveredPoint = null;\r\n            if (this._tooltip.isEnabled()) {\r\n                this._hideTooltip(this._hoveredPoint, false, isPointerOut)\r\n            }\r\n        }\r\n    },\r\n    _setHoveredSeries: function(series, mode) {\r\n        this._releaseHoveredSeries();\r\n        this._releaseHoveredPoint();\r\n        series.hover(mode);\r\n        this.hoveredSeries = series\r\n    },\r\n    _releaseHoveredSeries() {\r\n        if (this.hoveredSeries) {\r\n            this.hoveredSeries.clearHover();\r\n            this.hoveredSeries = null\r\n        }\r\n    },\r\n    clearSelection() {\r\n        this._storedSeries.forEach((series => {\r\n            if (series) {\r\n                series.clearSelection();\r\n                series.getPoints().forEach((point => point.clearSelection()))\r\n            }\r\n        }))\r\n    },\r\n    _clean: function() {\r\n        this.hoveredPoint = this.hoveredSeries = this._hoveredArgumentPoints = null;\r\n        this._hideTooltip(this.pointAtShownTooltip)\r\n    },\r\n    clearHover: function(isPointerOut) {\r\n        this._resetHoveredArgument();\r\n        this._releaseHoveredSeries();\r\n        this._releaseHoveredPoint(isPointerOut)\r\n    },\r\n    _hideTooltip: function(point, silent, isPointerOut) {\r\n        const that = this;\r\n        if (!that._tooltip || point && that.pointAtShownTooltip !== point) {\r\n            return\r\n        }\r\n        if (!silent && that.pointAtShownTooltip) {\r\n            that.pointAtShownTooltip = null\r\n        }\r\n        that._tooltip.hide(!!isPointerOut)\r\n    },\r\n    _showTooltip: function(point) {\r\n        const that = this;\r\n        let tooltipFormatObject;\r\n        const eventData = {\r\n            target: point\r\n        };\r\n        if (null !== point && void 0 !== point && point.getOptions()) {\r\n            tooltipFormatObject = point.getTooltipFormatObject(that._tooltip, that._tooltip.isShared() && that._chart.getStackedPoints(point));\r\n            if (!isDefined(tooltipFormatObject.valueText) && !tooltipFormatObject.points || !point.isVisible()) {\r\n                return\r\n            }\r\n            const coords = point.getTooltipParams(that._tooltip.getLocation());\r\n            const rootOffset = that._renderer.getRootOffset();\r\n            coords.x += rootOffset.left;\r\n            coords.y += rootOffset.top;\r\n            const callback = result => {\r\n                result && (that.pointAtShownTooltip = point)\r\n            };\r\n            callback(that._tooltip.show(tooltipFormatObject, coords, eventData, void 0, callback))\r\n        }\r\n    },\r\n    _showPointTooltip: function(event, point) {\r\n        const that = event.data.tracker;\r\n        const pointWithTooltip = that.pointAtShownTooltip;\r\n        if (pointWithTooltip && pointWithTooltip !== point) {\r\n            that._hideTooltip(pointWithTooltip)\r\n        }\r\n        that._showTooltip(point)\r\n    },\r\n    _hidePointTooltip: function(event, point) {\r\n        event.data.tracker._hideTooltip(point, false, true)\r\n    },\r\n    _enableOutHandler: function() {\r\n        if (this._outHandler) {\r\n            return\r\n        }\r\n        const that = this;\r\n        const handler = function(e) {\r\n            const rootOffset = that._renderer.getRootOffset();\r\n            const x = _floor(e.pageX - rootOffset.left);\r\n            const y = _floor(e.pageY - rootOffset.top);\r\n            if (!inCanvas(that._mainCanvas, x, y) && !that._isCursorOnTooltip(e)) {\r\n                that._pointerOut();\r\n                that._disableOutHandler()\r\n            }\r\n        };\r\n        eventsEngine.on(domAdapter.getDocument(), POINTER_ACTION, handler);\r\n        this._outHandler = handler\r\n    },\r\n    _isCursorOnTooltip: function(e) {\r\n        return this._tooltip.isEnabled() && this._tooltip.isCursorOnTooltip(e.pageX, e.pageY)\r\n    },\r\n    _disableOutHandler: function() {\r\n        this._outHandler && eventsEngine.off(domAdapter.getDocument(), POINTER_ACTION, this._outHandler);\r\n        this._outHandler = null\r\n    },\r\n    stopCurrentHandling: function() {\r\n        this._pointerOut(true)\r\n    },\r\n    _pointerOut: function(force) {\r\n        this.clearHover(true);\r\n        (force || this._tooltip.isEnabled()) && this._hideTooltip(this.pointAtShownTooltip, false, true)\r\n    },\r\n    _triggerLegendClick: function(eventArgs, elementClick) {\r\n        const eventTrigger = this._eventTrigger;\r\n        eventTrigger(LEGEND_CLICK, eventArgs, (function() {\r\n            !eventCanceled(eventArgs, eventArgs.target, \"legend\") && eventTrigger(elementClick, eventArgs)\r\n        }))\r\n    },\r\n    _hoverLegendItem: function(x, y) {\r\n        const that = this;\r\n        const item = that._legend.getItemByCoord(x, y);\r\n        let series;\r\n        const legendHoverMode = correctLegendHoverMode(that._legend.getOptions().hoverMode);\r\n        if (item) {\r\n            series = that._storedSeries[item.id];\r\n            if (!series.isHovered() || series.lastHoverMode !== legendHoverMode) {\r\n                that._setHoveredSeries(series, legendHoverMode)\r\n            }\r\n            that._tooltip.isEnabled() && that._hideTooltip(that.pointAtShownTooltip)\r\n        } else {\r\n            that.clearHover()\r\n        }\r\n    },\r\n    _hoverArgument: function(argument, argumentIndex) {\r\n        const that = this;\r\n        const hoverMode = that._getArgumentHoverMode();\r\n        if (isDefined(argument)) {\r\n            that._releaseHoveredPoint();\r\n            that._hoveredArgument = argument;\r\n            that._argumentIndex = argumentIndex;\r\n            that._notifySeries({\r\n                action: \"pointHover\",\r\n                notifyLegend: that._notifyLegendOnHoverArgument,\r\n                target: {\r\n                    argument: argument,\r\n                    fullState: HOVER_STATE,\r\n                    argumentIndex: argumentIndex,\r\n                    getOptions: function() {\r\n                        return {\r\n                            hoverMode: hoverMode\r\n                        }\r\n                    }\r\n                }\r\n            })\r\n        }\r\n    },\r\n    _resetHoveredArgument: function() {\r\n        const that = this;\r\n        let hoverMode;\r\n        if (isDefined(that._hoveredArgument)) {\r\n            hoverMode = that._getArgumentHoverMode();\r\n            that._notifySeries({\r\n                action: \"clearPointHover\",\r\n                notifyLegend: that._notifyLegendOnHoverArgument,\r\n                target: {\r\n                    fullState: NORMAL_STATE,\r\n                    argumentIndex: that._argumentIndex,\r\n                    argument: that._hoveredArgument,\r\n                    getOptions: function() {\r\n                        return {\r\n                            hoverMode: hoverMode\r\n                        }\r\n                    }\r\n                }\r\n            });\r\n            that._hoveredArgument = null\r\n        }\r\n    },\r\n    _notifySeries: function(data) {\r\n        this._storedSeries.forEach((function(series) {\r\n            series.notify(data)\r\n        }))\r\n    },\r\n    _pointerHandler: function(e) {\r\n        var _series;\r\n        const that = e.data.tracker;\r\n        const rootOffset = that._renderer.getRootOffset();\r\n        const x = _floor(e.pageX - rootOffset.left);\r\n        const y = _floor(e.pageY - rootOffset.top);\r\n        const canvas = that._getCanvas(x, y);\r\n        let series = getData(e, SERIES_DATA);\r\n        let point = getData(e, POINT_DATA) || (null === (_series = series) || void 0 === _series ? void 0 : _series.getPointByCoord(x, y));\r\n        that._isHolding = false;\r\n        clearTimeout(that._holdTimer);\r\n        if (e.type === pointerEvents.down) {\r\n            that._holdTimer = setTimeout((() => that._isHolding = true), 300)\r\n        }\r\n        if (point && !point.getMarkerVisibility()) {\r\n            point = void 0\r\n        }\r\n        that._enableOutHandler();\r\n        if (that._legend.coordsIn(x, y)) {\r\n            that._hoverLegendItem(x, y);\r\n            return\r\n        }\r\n        if (that.hoveredSeries && that.hoveredSeries !== that._stuckSeries) {\r\n            that._releaseHoveredSeries()\r\n        }\r\n        if (that._hoverArgumentAxis(x, y, e)) {\r\n            return\r\n        }\r\n        if (that._isPointerOut(canvas, point)) {\r\n            that._pointerOut()\r\n        }\r\n        if (!canvas && !point) {\r\n            return\r\n        }\r\n        if (series && !point) {\r\n            point = series.getNeighborPoint(x, y);\r\n            if (!that._stickyHovering && point && !point.coordsIn(x, y)) {\r\n                point = null\r\n            }\r\n            if (series !== that.hoveredSeries) {\r\n                that._setTimeout((function() {\r\n                    that._setHoveredSeries(series);\r\n                    that._setStuckSeries(e, series, x, y);\r\n                    that._pointerComplete(point, x, y)\r\n                }), series);\r\n                return\r\n            }\r\n        } else if (point) {\r\n            if (e.type !== pointerEvents.move && \"touch\" !== e.pointerType) {\r\n                return\r\n            }\r\n            if (that.hoveredSeries) {\r\n                that._setTimeout((() => that._pointerOnPoint(point, x, y, e)), point)\r\n            } else {\r\n                that._pointerOnPoint(point, x, y, e)\r\n            }\r\n            return\r\n        } else if (that._setStuckSeries(e, void 0, x, y) && that._stickyHovering) {\r\n            var _point;\r\n            series = that._stuckSeries;\r\n            point = series.getNeighborPoint(x, y);\r\n            that._releaseHoveredSeries();\r\n            (null === (_point = point) || void 0 === _point ? void 0 : _point.getMarkerVisibility()) && that._setHoveredPoint(point)\r\n        } else if (!that._stickyHovering) {\r\n            that._pointerOut()\r\n        }\r\n        that._pointerComplete(point, x, y)\r\n    },\r\n    _pointerOnPoint: function(point, x, y) {\r\n        this._resetHoveredArgument();\r\n        this._setHoveredPoint(point);\r\n        this._pointerComplete(point, x, y)\r\n    },\r\n    _pointerComplete: function(point) {\r\n        this.pointAtShownTooltip !== point && this._tooltip.isEnabled() && this._showTooltip(point)\r\n    },\r\n    _clickHandler: function(e) {\r\n        var _point2;\r\n        const that = e.data.tracker;\r\n        if (that._isHolding) {\r\n            return\r\n        }\r\n        const rootOffset = that._renderer.getRootOffset();\r\n        const x = _floor(e.pageX - rootOffset.left);\r\n        const y = _floor(e.pageY - rootOffset.top);\r\n        let point = getData(e, POINT_DATA);\r\n        const series = that._stuckSeries || getData(e, SERIES_DATA) || (null === (_point2 = point) || void 0 === _point2 ? void 0 : _point2.series);\r\n        const axis = that._argumentAxis;\r\n        if (that._legend.coordsIn(x, y)) {\r\n            const item = that._legend.getItemByCoord(x, y);\r\n            if (item) {\r\n                that._legendClick(item, e)\r\n            }\r\n        } else if (null !== axis && void 0 !== axis && axis.coordsIn(x, y)) {\r\n            const argument = getData(e, ARG_DATA, true);\r\n            if (isDefined(argument)) {\r\n                that._eventTrigger(\"argumentAxisClick\", {\r\n                    argument: argument,\r\n                    event: e\r\n                })\r\n            }\r\n        } else if (series) {\r\n            var _point3;\r\n            point = point || series.getPointByCoord(x, y);\r\n            if (null !== (_point3 = point) && void 0 !== _point3 && _point3.getMarkerVisibility()) {\r\n                that._pointClick(point, e)\r\n            } else {\r\n                getData(e, SERIES_DATA) && that._eventTrigger(SERIES_CLICK, {\r\n                    target: series,\r\n                    event: e\r\n                })\r\n            }\r\n        }\r\n    },\r\n    dispose: function() {\r\n        this._disableOutHandler();\r\n        this._renderer.root.off(DOT_EVENT_NS);\r\n        this._seriesGroup.off(DOT_EVENT_NS)\r\n    }\r\n};\r\nexport const ChartTracker = function(options) {\r\n    this.ctor(options)\r\n};\r\nextend(ChartTracker.prototype, baseTrackerPrototype, {\r\n    _pointClick: function(point, event) {\r\n        const eventTrigger = this._eventTrigger;\r\n        const series = point.series;\r\n        const eventArgs = {\r\n            target: point,\r\n            event: event\r\n        };\r\n        eventTrigger(POINT_CLICK, eventArgs, (function() {\r\n            !eventCanceled(eventArgs, series, \"point\") && eventTrigger(SERIES_CLICK, {\r\n                target: series,\r\n                event: event\r\n            })\r\n        }))\r\n    },\r\n    update: function(options) {\r\n        baseTrackerPrototype.update.call(this, options);\r\n        this._argumentAxis = options.argumentAxis || {};\r\n        this._axisHoverEnabled = this._argumentAxis && \"allargumentpoints\" === _normalizeEnum(this._argumentAxis.getOptions().hoverMode);\r\n        this._rotated = options.rotated;\r\n        this._crosshair = options.crosshair;\r\n        this._stickyHovering = options.stickyHovering\r\n    },\r\n    _getCanvas: function(x, y) {\r\n        const canvases = this._canvases || [];\r\n        for (let i = 0; i < canvases.length; i++) {\r\n            const c = canvases[i];\r\n            if (inCanvas(c, x, y)) {\r\n                return c\r\n            }\r\n        }\r\n        return null\r\n    },\r\n    _isPointerOut: function(canvas, point) {\r\n        return !canvas && this._stuckSeries && (null === point || void 0 === point ? void 0 : point.series) !== this._stuckSeries\r\n    },\r\n    _hideCrosshair: function() {\r\n        var _this$_crosshair;\r\n        null === (_this$_crosshair = this._crosshair) || void 0 === _this$_crosshair || _this$_crosshair.hide()\r\n    },\r\n    _moveCrosshair: function(point, x, y) {\r\n        if (this._crosshair && null !== point && void 0 !== point && point.isVisible()) {\r\n            this._crosshair.show({\r\n                point: point,\r\n                x: x,\r\n                y: y\r\n            })\r\n        }\r\n    },\r\n    _clean: function() {\r\n        baseTrackerPrototype._clean.call(this);\r\n        this._resetTimer();\r\n        this._stuckSeries = null\r\n    },\r\n    _getSeriesForShared: function(x, y) {\r\n        var _point4;\r\n        const that = this;\r\n        const points = [];\r\n        let point = null;\r\n        let distance = 1 / 0;\r\n        if (that._tooltip.isShared() && !that.hoveredSeries) {\r\n            _each(that._storedSeries, (function(_, series) {\r\n                const point = series.getNeighborPoint(x, y);\r\n                point && points.push(point)\r\n            }));\r\n            _each(points, (function(_, p) {\r\n                const coords = p.getCrosshairData(x, y);\r\n                const d = getDistance(x, y, coords.x, coords.y);\r\n                if (d < distance) {\r\n                    point = p;\r\n                    distance = d\r\n                }\r\n            }))\r\n        }\r\n        return null === (_point4 = point) || void 0 === _point4 ? void 0 : _point4.series\r\n    },\r\n    _setTimeout: function(callback, keeper) {\r\n        const that = this;\r\n        if (that._timeoutKeeper !== keeper) {\r\n            that._resetTimer();\r\n            that._hoverTimeout = setTimeout((function() {\r\n                callback();\r\n                that._timeoutKeeper = null\r\n            }), 100);\r\n            that._timeoutKeeper = keeper\r\n        }\r\n    },\r\n    _resetTimer: function() {\r\n        clearTimeout(this._hoverTimeout);\r\n        this._timeoutKeeper = this._hoverTimeout = null\r\n    },\r\n    _stopEvent: function(e) {\r\n        if (!isDefined(e.cancelable) || e.cancelable) {\r\n            e.preventDefault();\r\n            e.stopPropagation()\r\n        }\r\n    },\r\n    _setStuckSeries: function(e, series, x, y) {\r\n        if (\"mouse\" !== e.pointerType) {\r\n            this._stuckSeries = null\r\n        } else {\r\n            this._stuckSeries = series || this._stuckSeries || this._getSeriesForShared(x, y)\r\n        }\r\n        return !!this._stuckSeries\r\n    },\r\n    _pointerOut: function() {\r\n        this._stuckSeries = null;\r\n        this._hideCrosshair();\r\n        this._resetTimer();\r\n        baseTrackerPrototype._pointerOut.apply(this, arguments)\r\n    },\r\n    _hoverArgumentAxis: function(x, y, e) {\r\n        const that = this;\r\n        that._resetHoveredArgument();\r\n        if (that._axisHoverEnabled && that._argumentAxis.coordsIn(x, y)) {\r\n            that._hoverArgument(getData(e, ARG_DATA, true));\r\n            return true\r\n        }\r\n    },\r\n    _pointerComplete: function(point, x, y) {\r\n        this.hoveredSeries && this.hoveredSeries.updateHover(x, y);\r\n        this._resetTimer();\r\n        this._moveCrosshair(point, x, y);\r\n        baseTrackerPrototype._pointerComplete.call(this, point)\r\n    },\r\n    _legendClick: function(item, e) {\r\n        const series = this._storedSeries[item.id];\r\n        this._triggerLegendClick({\r\n            target: series,\r\n            event: e\r\n        }, SERIES_CLICK)\r\n    },\r\n    _hoverLegendItem: function(x, y) {\r\n        this._stuckSeries = null;\r\n        this._hideCrosshair();\r\n        baseTrackerPrototype._hoverLegendItem.call(this, x, y)\r\n    },\r\n    _pointerOnPoint: function(point, x, y, e) {\r\n        this._setStuckSeries(e, point.series, x, y);\r\n        this._releaseHoveredSeries();\r\n        baseTrackerPrototype._pointerOnPoint.call(this, point, x, y, e)\r\n    },\r\n    _notifyLegendOnHoverArgument: false,\r\n    _getArgumentHoverMode: function() {\r\n        return correctHoverMode(this._argumentAxis)\r\n    },\r\n    dispose: function() {\r\n        this._resetTimer();\r\n        baseTrackerPrototype.dispose.call(this)\r\n    }\r\n});\r\nexport const PieTracker = function(options) {\r\n    this.ctor(options)\r\n};\r\nextend(PieTracker.prototype, baseTrackerPrototype, {\r\n    _isPointerOut: function(_, point) {\r\n        return !point\r\n    },\r\n    _legendClick: function(item, e) {\r\n        const points = [];\r\n        this._storedSeries.forEach((s => points.push.apply(points, s.getPointsByKeys(item.argument, item.argumentIndex))));\r\n        this._eventTrigger(LEGEND_CLICK, {\r\n            target: item.argument,\r\n            points: points,\r\n            event: e\r\n        })\r\n    },\r\n    _pointClick: function(point, e) {\r\n        this._eventTrigger(POINT_CLICK, {\r\n            target: point,\r\n            event: e\r\n        })\r\n    },\r\n    _hoverLegendItem: function(x, y) {\r\n        const that = this;\r\n        const item = that._legend.getItemByCoord(x, y);\r\n        if (item && that._hoveredArgument !== item.argument) {\r\n            that._resetHoveredArgument();\r\n            that._hoverArgument(item.argument, item.argumentIndex)\r\n        } else if (!item) {\r\n            that.clearHover()\r\n        }\r\n    },\r\n    _getArgumentHoverMode: function() {\r\n        return correctHoverMode(this._legend)\r\n    },\r\n    _hoverArgumentAxis: _noop,\r\n    _setStuckSeries: _noop,\r\n    _getCanvas: _noop,\r\n    _notifyLegendOnHoverArgument: true\r\n});\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AACA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AACA;AAKA;AAAA;AACA;AAAA;AAGA;AAAA;AAGA;AAAA;;;;;;;;;;;;AAGA,MAAM,SAAS,KAAK,KAAK;AACzB,MAAM,eAAe,mKAAA,CAAA,UAAM,CAAC,MAAM;AAClC,MAAM,eAAe,mKAAA,CAAA,UAAM,CAAC,MAAM;AAClC,MAAM,cAAc,aAAa,SAAS;AAC1C,MAAM,eAAe,aAAa,UAAU;AAC5C,MAAM,WAAW;AACjB,MAAM,eAAe,MAAM;AAC3B,MAAM,iBAAiB,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE;IAAC,4KAAA,CAAA,UAAa,CAAC,IAAI;IAAE,4KAAA,CAAA,UAAa,CAAC,IAAI;CAAC,EAAE;AAC9E,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,WAAW;AACjB,MAAM,QAAQ;AACd,MAAM,eAAe;AACrB,MAAM,YAAY;AAClB,MAAM,2BAA2B;AACjC,MAAM,sBAAsB;AAC5B,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;IAAC;IAAiB;IAAiB;CAAO;AAErE,SAAS,QAAQ,KAAK,EAAE,OAAO,EAAE,cAAc;IAC3C,MAAM,SAAS,MAAM,MAAM;IAC3B,IAAI,YAAY,OAAO,OAAO,EAAE;QAC5B,OAAO,OAAO,UAAU,CAAC,QAAQ;IACrC;IACA,MAAM,OAAO,MAAM,CAAC,QAAQ;IAC5B,IAAI,kBAAkB,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QACpC,MAAM,gBAAgB,SAAS,IAAI;YAC/B,IAAI,KAAK,UAAU,EAAE;gBACjB,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,CAAC,QAAQ,GAAG;oBACrC,OAAO,KAAK,UAAU,CAAC,QAAQ;gBACnC,OAAO;oBACH,OAAO,cAAc,KAAK,UAAU;gBACxC;YACJ;YACA;QACJ;QACA,OAAO,cAAc;IACzB;IACA,OAAO;AACX;AAEA,SAAS,cAAc,IAAI,EAAE,MAAM;IAC/B,IAAI,EACA,QAAQ,MAAM,EACjB,GAAG;IACJ,OAAO,UAAU,CAAC,OAAO,UAAU;AACvC;AAEA,SAAS,uBAAuB,IAAI;IAChC,IAAI,mBAAmB,OAAO,CAAC,QAAQ,CAAC,GAAG;QACvC,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AAEA,SAAS,iBAAiB,MAAM;IAC5B,MAAM,OAAO,OAAO,UAAU,GAAG,SAAS;IAC1C,OAAO,WAAW,OAAO,OAAO;AACpC;AACA,MAAM,uBAAuB;IACzB,MAAM,SAAS,OAAO;QAClB,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO;YACT,SAAS;QACb;QACA,KAAK,SAAS,GAAG,QAAQ,QAAQ;QACjC,KAAK,OAAO,GAAG,QAAQ,MAAM;QAC7B,KAAK,QAAQ,GAAG,QAAQ,OAAO;QAC/B,KAAK,aAAa,GAAG,QAAQ,YAAY;QACzC,KAAK,YAAY,GAAG,QAAQ,WAAW;QACvC,QAAQ,WAAW,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB,EAAE,WAAW,MAAM,KAAK,iBAAiB,EAAE,EAAE,CAAC,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,aAAa,gBAAgB,EAAE,WAAW,MAAM,KAAK,iBAAiB;QACpN,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,cAAc,EAAE,CAAC,gBAAgB,MAAM,KAAK,eAAe,EAAE,EAAE,CAAC,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,4KAAA,CAAA,UAAa,CAAC,EAAE,EAAE,WAAY,IAAM,aAAa,KAAK,UAAU,GAAI,EAAE,CAAC,CAAA,GAAA,iLAAA,CAAA,eAAY,AAAD,EAAE,0KAAA,CAAA,OAAc,EAAE,WAAW,MAAM,KAAK,aAAa;IAChP;IACA,QAAQ,SAAS,OAAO;QACpB,IAAI,CAAC,MAAM,GAAG,QAAQ,KAAK;IAC/B;IACA,cAAa,MAAM,EAAE,gBAAgB;QACjC,MAAM,OAAO,IAAI;QACjB,MAAM,kBAAkB,CAAC,CAAC,SAAS,UAAU,KAAK,MAAM,UAAU,OAAO,IAAI,CAAE,CAAA,IAAK,MAAM,KAAK,aAAa,KAAM,KAAK,aAAa,IAAI,KAAK,aAAa,CAAC,MAAM;QACjK,IAAI,KAAK,aAAa,KAAK,QAAQ;YAC/B,KAAK,aAAa,GAAG,UAAU,EAAE;QACrC;QACA,IAAI,iBAAiB;YACjB,KAAK,MAAM;YACX,KAAK,SAAS,CAAC,gBAAgB;QACnC;QACA,IAAI,kBAAkB;YAClB,KAAK,cAAc;YACnB,IAAI,CAAC,iBAAiB;gBAClB,KAAK,YAAY,CAAC,KAAK,mBAAmB;gBAC1C,KAAK,UAAU;YACnB;QACJ;IACJ;IACA,aAAa,SAAS,UAAU,EAAE,YAAY;QAC1C,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,SAAS,GAAG;IACrB;IACA,eAAe;QACX,MAAM,QAAQ,IAAI,CAAC,mBAAmB;QACtC,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,IAAI,CAAC,MAAM,SAAS,IAAI;YAC/C,IAAI,CAAC,YAAY,CAAC,OAAO;QAC7B,OAAO;YACH,IAAI,CAAC,YAAY,CAAC;QACtB;IACJ;IACA,kBAAkB,SAAS,KAAK;QAC5B,IAAI,UAAU,IAAI,CAAC,aAAa,EAAE;YAC9B;QACJ;QACA,IAAI,CAAC,oBAAoB;QACzB,MAAM,KAAK;QACX,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,sBAAsB,SAAS,YAAY;QACvC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI;YACvD,IAAI,CAAC,aAAa,CAAC,UAAU;YAC7B,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI;gBAC3B,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,aAAa,EAAE,OAAO;YACjD;QACJ;IACJ;IACA,mBAAmB,SAAS,MAAM,EAAE,IAAI;QACpC,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,oBAAoB;QACzB,OAAO,KAAK,CAAC;QACb,IAAI,CAAC,aAAa,GAAG;IACzB;IACA;QACI,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,UAAU;YAC7B,IAAI,CAAC,aAAa,GAAG;QACzB;IACJ;IACA;QACI,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA;YACxB,IAAI,QAAQ;gBACR,OAAO,cAAc;gBACrB,OAAO,SAAS,GAAG,OAAO,CAAE,CAAA,QAAS,MAAM,cAAc;YAC7D;QACJ;IACJ;IACA,QAAQ;QACJ,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,sBAAsB,GAAG;QACvE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB;IAC9C;IACA,YAAY,SAAS,YAAY;QAC7B,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,oBAAoB,CAAC;IAC9B;IACA,cAAc,SAAS,KAAK,EAAE,MAAM,EAAE,YAAY;QAC9C,MAAM,OAAO,IAAI;QACjB,IAAI,CAAC,KAAK,QAAQ,IAAI,SAAS,KAAK,mBAAmB,KAAK,OAAO;YAC/D;QACJ;QACA,IAAI,CAAC,UAAU,KAAK,mBAAmB,EAAE;YACrC,KAAK,mBAAmB,GAAG;QAC/B;QACA,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;IACzB;IACA,cAAc,SAAS,KAAK;QACxB,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,MAAM,YAAY;YACd,QAAQ;QACZ;QACA,IAAI,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,UAAU,IAAI;YAC1D,sBAAsB,MAAM,sBAAsB,CAAC,KAAK,QAAQ,EAAE,KAAK,QAAQ,CAAC,QAAQ,MAAM,KAAK,MAAM,CAAC,gBAAgB,CAAC;YAC3H,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB,SAAS,KAAK,CAAC,oBAAoB,MAAM,IAAI,CAAC,MAAM,SAAS,IAAI;gBAChG;YACJ;YACA,MAAM,SAAS,MAAM,gBAAgB,CAAC,KAAK,QAAQ,CAAC,WAAW;YAC/D,MAAM,aAAa,KAAK,SAAS,CAAC,aAAa;YAC/C,OAAO,CAAC,IAAI,WAAW,IAAI;YAC3B,OAAO,CAAC,IAAI,WAAW,GAAG;YAC1B,MAAM,WAAW,CAAA;gBACb,UAAU,CAAC,KAAK,mBAAmB,GAAG,KAAK;YAC/C;YACA,SAAS,KAAK,QAAQ,CAAC,IAAI,CAAC,qBAAqB,QAAQ,WAAW,KAAK,GAAG;QAChF;IACJ;IACA,mBAAmB,SAAS,KAAK,EAAE,KAAK;QACpC,MAAM,OAAO,MAAM,IAAI,CAAC,OAAO;QAC/B,MAAM,mBAAmB,KAAK,mBAAmB;QACjD,IAAI,oBAAoB,qBAAqB,OAAO;YAChD,KAAK,YAAY,CAAC;QACtB;QACA,KAAK,YAAY,CAAC;IACtB;IACA,mBAAmB,SAAS,KAAK,EAAE,KAAK;QACpC,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,OAAO,OAAO;IAClD;IACA,mBAAmB;QACf,IAAI,IAAI,CAAC,WAAW,EAAE;YAClB;QACJ;QACA,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,SAAS,CAAC;YACtB,MAAM,aAAa,KAAK,SAAS,CAAC,aAAa;YAC/C,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,WAAW,IAAI;YAC1C,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,WAAW,GAAG;YACzC,IAAI,CAAC,CAAA,GAAA,4JAAA,CAAA,gBAAQ,AAAD,EAAE,KAAK,WAAW,EAAE,GAAG,MAAM,CAAC,KAAK,kBAAkB,CAAC,IAAI;gBAClE,KAAK,WAAW;gBAChB,KAAK,kBAAkB;YAC3B;QACJ;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,gBAAgB;QAC1D,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,oBAAoB,SAAS,CAAC;QAC1B,OAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,EAAE,KAAK,EAAE,EAAE,KAAK;IACxF;IACA,oBAAoB;QAChB,IAAI,CAAC,WAAW,IAAI,0LAAA,CAAA,UAAY,CAAC,GAAG,CAAC,2JAAA,CAAA,UAAU,CAAC,WAAW,IAAI,gBAAgB,IAAI,CAAC,WAAW;QAC/F,IAAI,CAAC,WAAW,GAAG;IACvB;IACA,qBAAqB;QACjB,IAAI,CAAC,WAAW,CAAC;IACrB;IACA,aAAa,SAAS,KAAK;QACvB,IAAI,CAAC,UAAU,CAAC;QAChB,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,mBAAmB,EAAE,OAAO;IAC/F;IACA,qBAAqB,SAAS,SAAS,EAAE,YAAY;QACjD,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,aAAa,cAAc,WAAY;YACnC,CAAC,cAAc,WAAW,UAAU,MAAM,EAAE,aAAa,aAAa,cAAc;QACxF;IACJ;IACA,kBAAkB,SAAS,CAAC,EAAE,CAAC;QAC3B,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,OAAO,CAAC,cAAc,CAAC,GAAG;QAC5C,IAAI;QACJ,MAAM,kBAAkB,uBAAuB,KAAK,OAAO,CAAC,UAAU,GAAG,SAAS;QAClF,IAAI,MAAM;YACN,SAAS,KAAK,aAAa,CAAC,KAAK,EAAE,CAAC;YACpC,IAAI,CAAC,OAAO,SAAS,MAAM,OAAO,aAAa,KAAK,iBAAiB;gBACjE,KAAK,iBAAiB,CAAC,QAAQ;YACnC;YACA,KAAK,QAAQ,CAAC,SAAS,MAAM,KAAK,YAAY,CAAC,KAAK,mBAAmB;QAC3E,OAAO;YACH,KAAK,UAAU;QACnB;IACJ;IACA,gBAAgB,SAAS,QAAQ,EAAE,aAAa;QAC5C,MAAM,OAAO,IAAI;QACjB,MAAM,YAAY,KAAK,qBAAqB;QAC5C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YACrB,KAAK,oBAAoB;YACzB,KAAK,gBAAgB,GAAG;YACxB,KAAK,cAAc,GAAG;YACtB,KAAK,aAAa,CAAC;gBACf,QAAQ;gBACR,cAAc,KAAK,4BAA4B;gBAC/C,QAAQ;oBACJ,UAAU;oBACV,WAAW;oBACX,eAAe;oBACf,YAAY;wBACR,OAAO;4BACH,WAAW;wBACf;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,gBAAgB,GAAG;YAClC,YAAY,KAAK,qBAAqB;YACtC,KAAK,aAAa,CAAC;gBACf,QAAQ;gBACR,cAAc,KAAK,4BAA4B;gBAC/C,QAAQ;oBACJ,WAAW;oBACX,eAAe,KAAK,cAAc;oBAClC,UAAU,KAAK,gBAAgB;oBAC/B,YAAY;wBACR,OAAO;4BACH,WAAW;wBACf;oBACJ;gBACJ;YACJ;YACA,KAAK,gBAAgB,GAAG;QAC5B;IACJ;IACA,eAAe,SAAS,IAAI;QACxB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,SAAS,MAAM;YACvC,OAAO,MAAM,CAAC;QAClB;IACJ;IACA,iBAAiB,SAAS,CAAC;QACvB,IAAI;QACJ,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;QAC3B,MAAM,aAAa,KAAK,SAAS,CAAC,aAAa;QAC/C,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,WAAW,IAAI;QAC1C,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,WAAW,GAAG;QACzC,MAAM,SAAS,KAAK,UAAU,CAAC,GAAG;QAClC,IAAI,SAAS,QAAQ,GAAG;QACxB,IAAI,QAAQ,QAAQ,GAAG,eAAe,CAAC,SAAS,CAAC,UAAU,MAAM,KAAK,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,eAAe,CAAC,GAAG,EAAE;QACjI,KAAK,UAAU,GAAG;QAClB,aAAa,KAAK,UAAU;QAC5B,IAAI,EAAE,IAAI,KAAK,4KAAA,CAAA,UAAa,CAAC,IAAI,EAAE;YAC/B,KAAK,UAAU,GAAG,WAAY,IAAM,KAAK,UAAU,GAAG,MAAO;QACjE;QACA,IAAI,SAAS,CAAC,MAAM,mBAAmB,IAAI;YACvC,QAAQ,KAAK;QACjB;QACA,KAAK,iBAAiB;QACtB,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;YAC7B,KAAK,gBAAgB,CAAC,GAAG;YACzB;QACJ;QACA,IAAI,KAAK,aAAa,IAAI,KAAK,aAAa,KAAK,KAAK,YAAY,EAAE;YAChE,KAAK,qBAAqB;QAC9B;QACA,IAAI,KAAK,kBAAkB,CAAC,GAAG,GAAG,IAAI;YAClC;QACJ;QACA,IAAI,KAAK,aAAa,CAAC,QAAQ,QAAQ;YACnC,KAAK,WAAW;QACpB;QACA,IAAI,CAAC,UAAU,CAAC,OAAO;YACnB;QACJ;QACA,IAAI,UAAU,CAAC,OAAO;YAClB,QAAQ,OAAO,gBAAgB,CAAC,GAAG;YACnC,IAAI,CAAC,KAAK,eAAe,IAAI,SAAS,CAAC,MAAM,QAAQ,CAAC,GAAG,IAAI;gBACzD,QAAQ;YACZ;YACA,IAAI,WAAW,KAAK,aAAa,EAAE;gBAC/B,KAAK,WAAW,CAAE;oBACd,KAAK,iBAAiB,CAAC;oBACvB,KAAK,eAAe,CAAC,GAAG,QAAQ,GAAG;oBACnC,KAAK,gBAAgB,CAAC,OAAO,GAAG;gBACpC,GAAI;gBACJ;YACJ;QACJ,OAAO,IAAI,OAAO;YACd,IAAI,EAAE,IAAI,KAAK,4KAAA,CAAA,UAAa,CAAC,IAAI,IAAI,YAAY,EAAE,WAAW,EAAE;gBAC5D;YACJ;YACA,IAAI,KAAK,aAAa,EAAE;gBACpB,KAAK,WAAW,CAAE,IAAM,KAAK,eAAe,CAAC,OAAO,GAAG,GAAG,IAAK;YACnE,OAAO;gBACH,KAAK,eAAe,CAAC,OAAO,GAAG,GAAG;YACtC;YACA;QACJ,OAAO,IAAI,KAAK,eAAe,CAAC,GAAG,KAAK,GAAG,GAAG,MAAM,KAAK,eAAe,EAAE;YACtE,IAAI;YACJ,SAAS,KAAK,YAAY;YAC1B,QAAQ,OAAO,gBAAgB,CAAC,GAAG;YACnC,KAAK,qBAAqB;YAC1B,CAAC,SAAS,CAAC,SAAS,KAAK,KAAK,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,mBAAmB,EAAE,KAAK,KAAK,gBAAgB,CAAC;QACtH,OAAO,IAAI,CAAC,KAAK,eAAe,EAAE;YAC9B,KAAK,WAAW;QACpB;QACA,KAAK,gBAAgB,CAAC,OAAO,GAAG;IACpC;IACA,iBAAiB,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC;QACjC,IAAI,CAAC,qBAAqB;QAC1B,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,gBAAgB,CAAC,OAAO,GAAG;IACpC;IACA,kBAAkB,SAAS,KAAK;QAC5B,IAAI,CAAC,mBAAmB,KAAK,SAAS,IAAI,CAAC,QAAQ,CAAC,SAAS,MAAM,IAAI,CAAC,YAAY,CAAC;IACzF;IACA,eAAe,SAAS,CAAC;QACrB,IAAI;QACJ,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO;QAC3B,IAAI,KAAK,UAAU,EAAE;YACjB;QACJ;QACA,MAAM,aAAa,KAAK,SAAS,CAAC,aAAa;QAC/C,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,WAAW,IAAI;QAC1C,MAAM,IAAI,OAAO,EAAE,KAAK,GAAG,WAAW,GAAG;QACzC,IAAI,QAAQ,QAAQ,GAAG;QACvB,MAAM,SAAS,KAAK,YAAY,IAAI,QAAQ,GAAG,gBAAgB,CAAC,SAAS,CAAC,UAAU,KAAK,KAAK,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,MAAM;QAC1I,MAAM,OAAO,KAAK,aAAa;QAC/B,IAAI,KAAK,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;YAC7B,MAAM,OAAO,KAAK,OAAO,CAAC,cAAc,CAAC,GAAG;YAC5C,IAAI,MAAM;gBACN,KAAK,YAAY,CAAC,MAAM;YAC5B;QACJ,OAAO,IAAI,SAAS,QAAQ,KAAK,MAAM,QAAQ,KAAK,QAAQ,CAAC,GAAG,IAAI;YAChE,MAAM,WAAW,QAAQ,GAAG,UAAU;YACtC,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW;gBACrB,KAAK,aAAa,CAAC,qBAAqB;oBACpC,UAAU;oBACV,OAAO;gBACX;YACJ;QACJ,OAAO,IAAI,QAAQ;YACf,IAAI;YACJ,QAAQ,SAAS,OAAO,eAAe,CAAC,GAAG;YAC3C,IAAI,SAAS,CAAC,UAAU,KAAK,KAAK,KAAK,MAAM,WAAW,QAAQ,mBAAmB,IAAI;gBACnF,KAAK,WAAW,CAAC,OAAO;YAC5B,OAAO;gBACH,QAAQ,GAAG,gBAAgB,KAAK,aAAa,CAAC,cAAc;oBACxD,QAAQ;oBACR,OAAO;gBACX;YACJ;QACJ;IACJ;IACA,SAAS;QACL,IAAI,CAAC,kBAAkB;QACvB,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC;QACxB,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC;IAC1B;AACJ;AACO,MAAM,eAAe,SAAS,OAAO;IACxC,IAAI,CAAC,IAAI,CAAC;AACd;AACA,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,aAAa,SAAS,EAAE,sBAAsB;IACjD,aAAa,SAAS,KAAK,EAAE,KAAK;QAC9B,MAAM,eAAe,IAAI,CAAC,aAAa;QACvC,MAAM,SAAS,MAAM,MAAM;QAC3B,MAAM,YAAY;YACd,QAAQ;YACR,OAAO;QACX;QACA,aAAa,aAAa,WAAY;YAClC,CAAC,cAAc,WAAW,QAAQ,YAAY,aAAa,cAAc;gBACrE,QAAQ;gBACR,OAAO;YACX;QACJ;IACJ;IACA,QAAQ,SAAS,OAAO;QACpB,qBAAqB,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE;QACvC,IAAI,CAAC,aAAa,GAAG,QAAQ,YAAY,IAAI,CAAC;QAC9C,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,aAAa,IAAI,wBAAwB,CAAA,GAAA,4JAAA,CAAA,gBAAc,AAAD,EAAE,IAAI,CAAC,aAAa,CAAC,UAAU,GAAG,SAAS;QAC/H,IAAI,CAAC,QAAQ,GAAG,QAAQ,OAAO;QAC/B,IAAI,CAAC,UAAU,GAAG,QAAQ,SAAS;QACnC,IAAI,CAAC,eAAe,GAAG,QAAQ,cAAc;IACjD;IACA,YAAY,SAAS,CAAC,EAAE,CAAC;QACrB,MAAM,WAAW,IAAI,CAAC,SAAS,IAAI,EAAE;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;YACtC,MAAM,IAAI,QAAQ,CAAC,EAAE;YACrB,IAAI,CAAA,GAAA,4JAAA,CAAA,gBAAQ,AAAD,EAAE,GAAG,GAAG,IAAI;gBACnB,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,eAAe,SAAS,MAAM,EAAE,KAAK;QACjC,OAAO,CAAC,UAAU,IAAI,CAAC,YAAY,IAAI,CAAC,SAAS,SAAS,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,MAAM,MAAM,IAAI,CAAC,YAAY;IAC7H;IACA,gBAAgB;QACZ,IAAI;QACJ,SAAS,CAAC,mBAAmB,IAAI,CAAC,UAAU,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,IAAI;IACzG;IACA,gBAAgB,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC;QAChC,IAAI,IAAI,CAAC,UAAU,IAAI,SAAS,SAAS,KAAK,MAAM,SAAS,MAAM,SAAS,IAAI;YAC5E,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;gBACjB,OAAO;gBACP,GAAG;gBACH,GAAG;YACP;QACJ;IACJ;IACA,QAAQ;QACJ,qBAAqB,MAAM,CAAC,IAAI,CAAC,IAAI;QACrC,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,qBAAqB,SAAS,CAAC,EAAE,CAAC;QAC9B,IAAI;QACJ,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,EAAE;QACjB,IAAI,QAAQ;QACZ,IAAI,WAAW,IAAI;QACnB,IAAI,KAAK,QAAQ,CAAC,QAAQ,MAAM,CAAC,KAAK,aAAa,EAAE;YACjD,CAAA,GAAA,oLAAA,CAAA,OAAK,AAAD,EAAE,KAAK,aAAa,EAAG,SAAS,CAAC,EAAE,MAAM;gBACzC,MAAM,QAAQ,OAAO,gBAAgB,CAAC,GAAG;gBACzC,SAAS,OAAO,IAAI,CAAC;YACzB;YACA,CAAA,GAAA,oLAAA,CAAA,OAAK,AAAD,EAAE,QAAS,SAAS,CAAC,EAAE,CAAC;gBACxB,MAAM,SAAS,EAAE,gBAAgB,CAAC,GAAG;gBACrC,MAAM,IAAI,CAAA,GAAA,4JAAA,CAAA,cAAW,AAAD,EAAE,GAAG,GAAG,OAAO,CAAC,EAAE,OAAO,CAAC;gBAC9C,IAAI,IAAI,UAAU;oBACd,QAAQ;oBACR,WAAW;gBACf;YACJ;QACJ;QACA,OAAO,SAAS,CAAC,UAAU,KAAK,KAAK,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,MAAM;IACrF;IACA,aAAa,SAAS,QAAQ,EAAE,MAAM;QAClC,MAAM,OAAO,IAAI;QACjB,IAAI,KAAK,cAAc,KAAK,QAAQ;YAChC,KAAK,WAAW;YAChB,KAAK,aAAa,GAAG,WAAY;gBAC7B;gBACA,KAAK,cAAc,GAAG;YAC1B,GAAI;YACJ,KAAK,cAAc,GAAG;QAC1B;IACJ;IACA,aAAa;QACT,aAAa,IAAI,CAAC,aAAa;QAC/B,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,aAAa,GAAG;IAC/C;IACA,YAAY,SAAS,CAAC;QAClB,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,EAAE,UAAU,KAAK,EAAE,UAAU,EAAE;YAC1C,EAAE,cAAc;YAChB,EAAE,eAAe;QACrB;IACJ;IACA,iBAAiB,SAAS,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QACrC,IAAI,YAAY,EAAE,WAAW,EAAE;YAC3B,IAAI,CAAC,YAAY,GAAG;QACxB,OAAO;YACH,IAAI,CAAC,YAAY,GAAG,UAAU,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,mBAAmB,CAAC,GAAG;QACnF;QACA,OAAO,CAAC,CAAC,IAAI,CAAC,YAAY;IAC9B;IACA,aAAa;QACT,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,WAAW;QAChB,qBAAqB,WAAW,CAAC,KAAK,CAAC,IAAI,EAAE;IACjD;IACA,oBAAoB,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC;QAChC,MAAM,OAAO,IAAI;QACjB,KAAK,qBAAqB;QAC1B,IAAI,KAAK,iBAAiB,IAAI,KAAK,aAAa,CAAC,QAAQ,CAAC,GAAG,IAAI;YAC7D,KAAK,cAAc,CAAC,QAAQ,GAAG,UAAU;YACzC,OAAO;QACX;IACJ;IACA,kBAAkB,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC;QAClC,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,GAAG;QACxD,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,cAAc,CAAC,OAAO,GAAG;QAC9B,qBAAqB,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE;IACrD;IACA,cAAc,SAAS,IAAI,EAAE,CAAC;QAC1B,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,CAAC;QAC1C,IAAI,CAAC,mBAAmB,CAAC;YACrB,QAAQ;YACR,OAAO;QACX,GAAG;IACP;IACA,kBAAkB,SAAS,CAAC,EAAE,CAAC;QAC3B,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,cAAc;QACnB,qBAAqB,gBAAgB,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG;IACxD;IACA,iBAAiB,SAAS,KAAK,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;QACpC,IAAI,CAAC,eAAe,CAAC,GAAG,MAAM,MAAM,EAAE,GAAG;QACzC,IAAI,CAAC,qBAAqB;QAC1B,qBAAqB,eAAe,CAAC,IAAI,CAAC,IAAI,EAAE,OAAO,GAAG,GAAG;IACjE;IACA,8BAA8B;IAC9B,uBAAuB;QACnB,OAAO,iBAAiB,IAAI,CAAC,aAAa;IAC9C;IACA,SAAS;QACL,IAAI,CAAC,WAAW;QAChB,qBAAqB,OAAO,CAAC,IAAI,CAAC,IAAI;IAC1C;AACJ;AACO,MAAM,aAAa,SAAS,OAAO;IACtC,IAAI,CAAC,IAAI,CAAC;AACd;AACA,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,WAAW,SAAS,EAAE,sBAAsB;IAC/C,eAAe,SAAS,CAAC,EAAE,KAAK;QAC5B,OAAO,CAAC;IACZ;IACA,cAAc,SAAS,IAAI,EAAE,CAAC;QAC1B,MAAM,SAAS,EAAE;QACjB,IAAI,CAAC,aAAa,CAAC,OAAO,CAAE,CAAA,IAAK,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE,eAAe,CAAC,KAAK,QAAQ,EAAE,KAAK,aAAa;QAC9G,IAAI,CAAC,aAAa,CAAC,cAAc;YAC7B,QAAQ,KAAK,QAAQ;YACrB,QAAQ;YACR,OAAO;QACX;IACJ;IACA,aAAa,SAAS,KAAK,EAAE,CAAC;QAC1B,IAAI,CAAC,aAAa,CAAC,aAAa;YAC5B,QAAQ;YACR,OAAO;QACX;IACJ;IACA,kBAAkB,SAAS,CAAC,EAAE,CAAC;QAC3B,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,OAAO,CAAC,cAAc,CAAC,GAAG;QAC5C,IAAI,QAAQ,KAAK,gBAAgB,KAAK,KAAK,QAAQ,EAAE;YACjD,KAAK,qBAAqB;YAC1B,KAAK,cAAc,CAAC,KAAK,QAAQ,EAAE,KAAK,aAAa;QACzD,OAAO,IAAI,CAAC,MAAM;YACd,KAAK,UAAU;QACnB;IACJ;IACA,uBAAuB;QACnB,OAAO,iBAAiB,IAAI,CAAC,OAAO;IACxC;IACA,oBAAoB,kLAAA,CAAA,OAAK;IACzB,iBAAiB,kLAAA,CAAA,OAAK;IACtB,YAAY,kLAAA,CAAA,OAAK;IACjB,8BAA8B;AAClC", "ignoreList": [0], "debugId": null}}]}