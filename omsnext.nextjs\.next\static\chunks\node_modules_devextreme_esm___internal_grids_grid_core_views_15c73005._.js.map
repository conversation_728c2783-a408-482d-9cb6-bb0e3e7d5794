{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/views/utils/update_views_borders.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/views/utils/update_views_borders.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\r\nconst _excluded = [\"rowsView\"];\r\nimport {\r\n    isDefined\r\n} from \"../../../../../core/utils/type\";\r\nconst CLASSES = {\r\n    borderedTop: \"dx-bordered-top-view\",\r\n    borderedBottom: \"dx-bordered-bottom-view\"\r\n};\r\nconst getFirstVisibleViewElement = _ref => {\r\n    let {\r\n        columnHeadersView: columnHeadersView,\r\n        rowsView: rowsView\r\n    } = _ref;\r\n    if (null !== columnHeadersView && void 0 !== columnHeadersView && columnHeadersView.isVisible()) {\r\n        return columnHeadersView.element()\r\n    }\r\n    return rowsView.element()\r\n};\r\nconst getLastVisibleViewElement = _ref2 => {\r\n    let {\r\n        filterPanelView: filterPanelView,\r\n        footerView: footerView,\r\n        rowsView: rowsView\r\n    } = _ref2;\r\n    if (null !== filterPanelView && void 0 !== filterPanelView && filterPanelView.isVisible()) {\r\n        return filterPanelView.element()\r\n    }\r\n    if (null !== footerView && void 0 !== footerView && footerView.isVisible()) {\r\n        return footerView.element()\r\n    }\r\n    return rowsView.element()\r\n};\r\nconst getViewElementWithClass = (viewsWithBorder, className) => {\r\n    const borderedView = Object.values(viewsWithBorder).find((view => {\r\n        var _view$element;\r\n        return null === view || void 0 === view || null === (_view$element = view.element()) || void 0 === _view$element ? void 0 : _view$element.hasClass(className)\r\n    }));\r\n    return (null === borderedView || void 0 === borderedView ? void 0 : borderedView.element()) ?? null\r\n};\r\nconst shouldUpdateBorders = (viewName, viewsWithBorder) => {\r\n    var _rowsView$element;\r\n    if (!Object.keys(viewsWithBorder).includes(viewName)) {\r\n        return false\r\n    }\r\n    const {\r\n        rowsView: rowsView\r\n    } = viewsWithBorder, otherViews = _objectWithoutPropertiesLoose(viewsWithBorder, _excluded);\r\n    if (!isDefined(null === rowsView || void 0 === rowsView || null === (_rowsView$element = rowsView.element) || void 0 === _rowsView$element ? void 0 : _rowsView$element.call(rowsView))) {\r\n        return false\r\n    }\r\n    return Object.values(otherViews).filter((view => {\r\n        var _view$isVisible;\r\n        return null === view || void 0 === view || null === (_view$isVisible = view.isVisible) || void 0 === _view$isVisible ? void 0 : _view$isVisible.call(view)\r\n    })).every((view => isDefined(null === view || void 0 === view ? void 0 : view.element())))\r\n};\r\nexport const updateViewsBorders = (viewName, viewsWithBorder) => {\r\n    if (!shouldUpdateBorders(viewName, viewsWithBorder)) {\r\n        return\r\n    }\r\n    const $oldFirst = getViewElementWithClass(viewsWithBorder, CLASSES.borderedTop);\r\n    const $oldLast = getViewElementWithClass(viewsWithBorder, CLASSES.borderedBottom);\r\n    const $newFirst = getFirstVisibleViewElement(viewsWithBorder);\r\n    const $newLast = getLastVisibleViewElement(viewsWithBorder);\r\n    if ($oldFirst && !$oldFirst.is($newFirst)) {\r\n        $oldFirst.removeClass(CLASSES.borderedTop)\r\n    }\r\n    if ($oldLast && !$oldLast.is($newLast)) {\r\n        $oldLast.removeClass(CLASSES.borderedBottom)\r\n    }\r\n    if (!$newFirst.hasClass(CLASSES.borderedTop)) {\r\n        $newFirst.addClass(CLASSES.borderedTop)\r\n    }\r\n    if (!$newLast.hasClass(CLASSES.borderedBottom)) {\r\n        $newLast.addClass(CLASSES.borderedBottom)\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAEA;AAAA;;AADA,MAAM,YAAY;IAAC;CAAW;;AAI9B,MAAM,UAAU;IACZ,aAAa;IACb,gBAAgB;AACpB;AACA,MAAM,6BAA6B,CAAA;IAC/B,IAAI,EACA,mBAAmB,iBAAiB,EACpC,UAAU,QAAQ,EACrB,GAAG;IACJ,IAAI,SAAS,qBAAqB,KAAK,MAAM,qBAAqB,kBAAkB,SAAS,IAAI;QAC7F,OAAO,kBAAkB,OAAO;IACpC;IACA,OAAO,SAAS,OAAO;AAC3B;AACA,MAAM,4BAA4B,CAAA;IAC9B,IAAI,EACA,iBAAiB,eAAe,EAChC,YAAY,UAAU,EACtB,UAAU,QAAQ,EACrB,GAAG;IACJ,IAAI,SAAS,mBAAmB,KAAK,MAAM,mBAAmB,gBAAgB,SAAS,IAAI;QACvF,OAAO,gBAAgB,OAAO;IAClC;IACA,IAAI,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,SAAS,IAAI;QACxE,OAAO,WAAW,OAAO;IAC7B;IACA,OAAO,SAAS,OAAO;AAC3B;AACA,MAAM,0BAA0B,CAAC,iBAAiB;IAC9C,MAAM,eAAe,OAAO,MAAM,CAAC,iBAAiB,IAAI,CAAE,CAAA;QACtD,IAAI;QACJ,OAAO,SAAS,QAAQ,KAAK,MAAM,QAAQ,SAAS,CAAC,gBAAgB,KAAK,OAAO,EAAE,KAAK,KAAK,MAAM,gBAAgB,KAAK,IAAI,cAAc,QAAQ,CAAC;IACvJ;QACO;IAAP,OAAO,CAAA,OAAC,SAAS,gBAAgB,KAAK,MAAM,eAAe,KAAK,IAAI,aAAa,OAAO,gBAAjF,kBAAA,OAAwF;AACnG;AACA,MAAM,sBAAsB,CAAC,UAAU;IACnC,IAAI;IACJ,IAAI,CAAC,OAAO,IAAI,CAAC,iBAAiB,QAAQ,CAAC,WAAW;QAClD,OAAO;IACX;IACA,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG,iBAAiB,aAAa,CAAA,GAAA,uLAAA,CAAA,UAA6B,AAAD,EAAE,iBAAiB;IACjF,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,YAAY,KAAK,MAAM,YAAY,SAAS,CAAC,oBAAoB,SAAS,OAAO,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,IAAI,CAAC,YAAY;QACrL,OAAO;IACX;IACA,OAAO,OAAO,MAAM,CAAC,YAAY,MAAM,CAAE,CAAA;QACrC,IAAI;QACJ,OAAO,SAAS,QAAQ,KAAK,MAAM,QAAQ,SAAS,CAAC,kBAAkB,KAAK,SAAS,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,IAAI,CAAC;IACzJ,GAAI,KAAK,CAAE,CAAA,OAAQ,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,QAAQ,KAAK,MAAM,OAAO,KAAK,IAAI,KAAK,OAAO;AACzF;AACO,MAAM,qBAAqB,CAAC,UAAU;IACzC,IAAI,CAAC,oBAAoB,UAAU,kBAAkB;QACjD;IACJ;IACA,MAAM,YAAY,wBAAwB,iBAAiB,QAAQ,WAAW;IAC9E,MAAM,WAAW,wBAAwB,iBAAiB,QAAQ,cAAc;IAChF,MAAM,YAAY,2BAA2B;IAC7C,MAAM,WAAW,0BAA0B;IAC3C,IAAI,aAAa,CAAC,UAAU,EAAE,CAAC,YAAY;QACvC,UAAU,WAAW,CAAC,QAAQ,WAAW;IAC7C;IACA,IAAI,YAAY,CAAC,SAAS,EAAE,CAAC,WAAW;QACpC,SAAS,WAAW,CAAC,QAAQ,cAAc;IAC/C;IACA,IAAI,CAAC,UAAU,QAAQ,CAAC,QAAQ,WAAW,GAAG;QAC1C,UAAU,QAAQ,CAAC,QAAQ,WAAW;IAC1C;IACA,IAAI,CAAC,SAAS,QAAQ,CAAC,QAAQ,cAAc,GAAG;QAC5C,SAAS,QAAQ,CAAC,QAAQ,cAAc;IAC5C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/views/m_columns_view.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/views/m_columns_view.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    name as clickEventName\r\n} from \"../../../../common/core/events/click\";\r\nimport eventsEngine from \"../../../../common/core/events/core/events_engine\";\r\nimport {\r\n    name as dblclickEvent\r\n} from \"../../../../common/core/events/double_click\";\r\nimport pointerEvents from \"../../../../common/core/events/pointer\";\r\nimport {\r\n    removeEvent\r\n} from \"../../../../common/core/events/remove\";\r\nimport domAdapter from \"../../../../core/dom_adapter\";\r\nimport {\r\n    getPublicElement\r\n} from \"../../../../core/element\";\r\nimport {\r\n    data as elementData\r\n} from \"../../../../core/element_data\";\r\nimport Guid from \"../../../../core/guid\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport browser from \"../../../../core/utils/browser\";\r\nimport {\r\n    noop\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../../../core/utils/deferred\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport * as iteratorUtils from \"../../../../core/utils/iterator\";\r\nimport {\r\n    getBoundingRect,\r\n    getDefaultAlignment\r\n} from \"../../../../core/utils/position\";\r\nimport {\r\n    getHeight,\r\n    getOuterHeight,\r\n    getOuterWidth,\r\n    getWidth\r\n} from \"../../../../core/utils/size\";\r\nimport {\r\n    setWidth\r\n} from \"../../../../core/utils/style\";\r\nimport {\r\n    isDefined,\r\n    isFunction,\r\n    isNumeric,\r\n    isRenderer,\r\n    isString\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    getWindow,\r\n    hasWindow\r\n} from \"../../../../core/utils/window\";\r\nimport supportUtils from \"../../../core/utils/m_support\";\r\nimport {\r\n    ColumnStateMixin\r\n} from \"../../../grids/grid_core/column_state_mixin/m_column_state_mixin\";\r\nimport modules from \"../m_modules\";\r\nimport gridCoreUtils from \"../m_utils\";\r\nconst SCROLL_CONTAINER_CLASS = \"scroll-container\";\r\nconst SCROLLABLE_SIMULATED_CLASS = \"scrollable-simulated\";\r\nconst GROUP_SPACE_CLASS = \"group-space\";\r\nconst CONTENT_CLASS = \"content\";\r\nconst TABLE_CLASS = \"table\";\r\nconst TABLE_FIXED_CLASS = \"table-fixed\";\r\nconst CONTENT_FIXED_CLASS = \"content-fixed\";\r\nconst ROW_CLASS = \"dx-row\";\r\nconst GROUP_ROW_CLASS = \"dx-group-row\";\r\nconst GROUP_CELL_CLASS = \"dx-group-cell\";\r\nconst DETAIL_ROW_CLASS = \"dx-master-detail-row\";\r\nconst FILTER_ROW_CLASS = \"filter-row\";\r\nconst ERROR_ROW_CLASS = \"dx-error-row\";\r\nconst CELL_UPDATED_ANIMATION_CLASS = \"cell-updated-animation\";\r\nconst GROUP_ROW_CONTAINER = \"group-row-container\";\r\nconst HIDDEN_COLUMNS_WIDTH = \"0.0001px\";\r\nconst CELL_HINT_VISIBLE = \"dxCellHintVisible\";\r\nconst FORM_FIELD_ITEM_CONTENT_CLASS = \"dx-field-item-content\";\r\nconst appendElementTemplate = {\r\n    render(options) {\r\n        options.container.append(options.content)\r\n    }\r\n};\r\nconst subscribeToRowEvents = function(that, $table) {\r\n    let touchTarget;\r\n    let touchCurrentTarget;\r\n    let timeoutId;\r\n\r\n    function clearTouchTargets(timeout) {\r\n        return setTimeout((() => {\r\n            touchTarget = touchCurrentTarget = null\r\n        }), timeout)\r\n    }\r\n    eventsEngine.on($table, \"touchstart touchend\", \".dx-row\", (e => {\r\n        clearTimeout(timeoutId);\r\n        if (\"touchstart\" === e.type) {\r\n            touchTarget = e.target;\r\n            touchCurrentTarget = e.currentTarget;\r\n            timeoutId = clearTouchTargets(1e3)\r\n        } else {\r\n            timeoutId = clearTouchTargets()\r\n        }\r\n    }));\r\n    eventsEngine.on($table, [clickEventName, dblclickEvent, pointerEvents.down].join(\" \"), \".dx-row\", that.createAction((e => {\r\n        const {\r\n            event: event\r\n        } = e;\r\n        if (touchTarget) {\r\n            event.target = touchTarget;\r\n            event.currentTarget = touchCurrentTarget\r\n        }\r\n        if (!$(event.target).closest(\"a\").length) {\r\n            e.rowIndex = that.getRowIndex(event.currentTarget);\r\n            if (e.rowIndex >= 0) {\r\n                e.rowElement = getPublicElement($(event.currentTarget));\r\n                e.columns = that.getColumns();\r\n                if (event.type === pointerEvents.down) {\r\n                    that._rowPointerDown(e)\r\n                } else if (event.type === clickEventName) {\r\n                    that._rowClick(e)\r\n                } else {\r\n                    that._rowDblClick(e)\r\n                }\r\n            }\r\n        }\r\n    })))\r\n};\r\nconst getWidthStyle = function(width) {\r\n    if (\"auto\" === width) {\r\n        return \"\"\r\n    }\r\n    return isNumeric(width) ? `${width}px` : width\r\n};\r\nconst setCellWidth = function(cell, column, width) {\r\n    cell.style.width = cell.style.maxWidth = \"auto\" === column.width ? \"\" : width\r\n};\r\nconst copyAttributes = function(element, newElement) {\r\n    if (!element || !newElement) {\r\n        return\r\n    }\r\n    const oldAttributes = element.attributes;\r\n    const newAttributes = newElement.attributes;\r\n    let i;\r\n    for (i = 0; i < oldAttributes.length; i++) {\r\n        const name = oldAttributes[i].nodeName;\r\n        if (!newElement.hasAttribute(name)) {\r\n            element.removeAttribute(name)\r\n        }\r\n    }\r\n    for (i = 0; i < newAttributes.length; i++) {\r\n        element.setAttribute(newAttributes[i].nodeName, newAttributes[i].nodeValue)\r\n    }\r\n};\r\nconst removeHandler = function(templateDeferred) {\r\n    templateDeferred.resolve()\r\n};\r\nexport const normalizeWidth = width => {\r\n    if (\"number\" === typeof width) {\r\n        return `${width.toFixed(3)}px`\r\n    }\r\n    if (\"adaptiveHidden\" === width) {\r\n        return \"0.0001px\"\r\n    }\r\n    return width\r\n};\r\nexport class ColumnsView extends(ColumnStateMixin(modules.View)) {\r\n    init() {\r\n        this._scrollLeft = -1;\r\n        this._columnsController = this.getController(\"columns\");\r\n        this._dataController = this.getController(\"data\");\r\n        this._adaptiveColumnsController = this.getController(\"adaptiveColumns\");\r\n        this._columnChooserController = this.getController(\"columnChooser\");\r\n        this._editorFactoryController = this.getController(\"editorFactory\");\r\n        this._selectionController = this.getController(\"selection\");\r\n        this._columnChooserView = this.getView(\"columnChooserView\");\r\n        this._delayedTemplates = [];\r\n        this._templateDeferreds = new Set;\r\n        this._templatesCache = {};\r\n        this._templateTimeouts = new Set;\r\n        this.createAction(\"onCellClick\");\r\n        this.createAction(\"onRowClick\");\r\n        this.createAction(\"onCellDblClick\");\r\n        this.createAction(\"onRowDblClick\");\r\n        this.createAction(\"onCellHoverChanged\", {\r\n            excludeValidators: [\"disabled\", \"readOnly\"]\r\n        });\r\n        this.createAction(\"onCellPrepared\", {\r\n            excludeValidators: [\"disabled\", \"readOnly\"],\r\n            category: \"rendering\"\r\n        });\r\n        this.createAction(\"onRowPrepared\", {\r\n            excludeValidators: [\"disabled\", \"readOnly\"],\r\n            category: \"rendering\",\r\n            afterExecute: e => {\r\n                this._afterRowPrepared(e)\r\n            }\r\n        });\r\n        this._columnsController.columnsChanged.add(this._columnOptionChanged.bind(this));\r\n        this._dataController && this._dataController.changed.add(this._handleDataChanged.bind(this))\r\n    }\r\n    dispose() {\r\n        if (hasWindow()) {\r\n            var _this$_templateTimeou, _this$_templateTimeou2;\r\n            const window = getWindow();\r\n            null === (_this$_templateTimeou = this._templateTimeouts) || void 0 === _this$_templateTimeou || _this$_templateTimeou.forEach((templateTimeout => window.clearTimeout(templateTimeout)));\r\n            null === (_this$_templateTimeou2 = this._templateTimeouts) || void 0 === _this$_templateTimeou2 || _this$_templateTimeou2.clear()\r\n        }\r\n    }\r\n    optionChanged(args) {\r\n        super.optionChanged(args);\r\n        switch (args.name) {\r\n            case \"cellHintEnabled\":\r\n            case \"onCellPrepared\":\r\n            case \"onRowPrepared\":\r\n            case \"onCellHoverChanged\":\r\n                this._invalidate(true, true);\r\n                args.handled = true;\r\n                break;\r\n            case \"keyboardNavigation\":\r\n                if (\"keyboardNavigation.enabled\" === args.fullName) {\r\n                    this._invalidate(true, true)\r\n                }\r\n                args.handled = true\r\n        }\r\n    }\r\n    _createScrollableOptions() {\r\n        const scrollingOptions = this.option(\"scrolling\");\r\n        let useNativeScrolling = this.option(\"scrolling.useNative\");\r\n        const options = extend({}, scrollingOptions, {\r\n            direction: \"both\",\r\n            bounceEnabled: false,\r\n            useKeyboard: false\r\n        });\r\n        if (void 0 === useNativeScrolling) {\r\n            useNativeScrolling = true\r\n        }\r\n        if (\"auto\" === useNativeScrolling) {\r\n            delete options.useNative;\r\n            delete options.useSimulatedScrollbar\r\n        } else {\r\n            options.useNative = !!useNativeScrolling;\r\n            options.useSimulatedScrollbar = !useNativeScrolling\r\n        }\r\n        return options\r\n    }\r\n    _updateCell($cell, parameters) {\r\n        if (parameters.rowType) {\r\n            this._cellPrepared($cell, parameters)\r\n        }\r\n    }\r\n    _needToSetCellWidths() {\r\n        return this.option(\"columnAutoWidth\")\r\n    }\r\n    _createCell(options) {\r\n        const {\r\n            column: column\r\n        } = options;\r\n        const alignment = column.alignment || getDefaultAlignment(this.option(\"rtlEnabled\"));\r\n        const needToSetCellWidths = this._needToSetCellWidths();\r\n        const cell = domAdapter.createElement(\"td\");\r\n        cell.style.textAlign = alignment;\r\n        const $cell = $(cell);\r\n        if (column.cssClass) {\r\n            $cell.addClass(column.cssClass)\r\n        }\r\n        if (Array.isArray(column.elementAttr)) {\r\n            column.elementAttr.forEach((_ref => {\r\n                let {\r\n                    name: name,\r\n                    value: value\r\n                } = _ref;\r\n                $cell.attr(name, value)\r\n            }))\r\n        }\r\n        if (\"expand\" === column.command) {\r\n            $cell.addClass(column.cssClass);\r\n            $cell.addClass(this.addWidgetPrefix(\"group-space\"))\r\n        }\r\n        if (column.colspan > 1) {\r\n            $cell.attr(\"colSpan\", column.colspan)\r\n        } else if (!column.isBand && \"auto\" !== column.visibleWidth && needToSetCellWidths) {\r\n            if (column.width || column.minWidth) {\r\n                cell.style.minWidth = getWidthStyle(column.minWidth || column.width)\r\n            }\r\n            if (column.width) {\r\n                setCellWidth(cell, column, getWidthStyle(column.width))\r\n            }\r\n        }\r\n        return $cell\r\n    }\r\n    _createRow(rowObject, tagName) {\r\n        tagName = tagName || \"tr\";\r\n        const $element = $(`<${tagName}>`).addClass(\"dx-row\");\r\n        if (\"tr\" === tagName) {\r\n            this.setAria(\"role\", \"row\", $element)\r\n        }\r\n        return $element\r\n    }\r\n    _isAltRow(row) {\r\n        return row && row.dataIndex % 2 === 1\r\n    }\r\n    _createTable(columns, isAppend) {\r\n        const $table = $(\"<table>\").addClass(this.addWidgetPrefix(\"table\")).addClass(this.addWidgetPrefix(\"table-fixed\"));\r\n        if (columns && !isAppend) {\r\n            $table.attr(\"id\", `dx-${new Guid}`).append(this._createColGroup(columns));\r\n            if (browser.safari) {\r\n                $table.append($(\"<thead>\").append(\"<tr>\"))\r\n            }\r\n            this.setAria(\"role\", \"presentation\", $table)\r\n        } else {\r\n            this.setAria(\"hidden\", true, $table)\r\n        }\r\n        this.setAria(\"role\", \"presentation\", $(\"<tbody>\").appendTo($table));\r\n        if (isAppend) {\r\n            return $table\r\n        }\r\n        if (browser.mozilla) {\r\n            eventsEngine.on($table, \"mousedown\", \"td\", (e => {\r\n                if (e.ctrlKey) {\r\n                    e.preventDefault()\r\n                }\r\n            }))\r\n        }\r\n        if (this.option(\"cellHintEnabled\")) {\r\n            eventsEngine.on($table, \"mousemove\", \".dx-row > td\", this.createAction((args => {\r\n                const e = args.event;\r\n                const $element = $(e.target);\r\n                const $cell = $(e.currentTarget);\r\n                const $row = $cell.parent();\r\n                const visibleColumns = this._columnsController.getVisibleColumns();\r\n                const rowOptions = $row.data(\"options\");\r\n                const columnIndex = $cell.index();\r\n                const cellOptions = rowOptions && rowOptions.cells && rowOptions.cells[columnIndex];\r\n                const column = cellOptions ? cellOptions.column : visibleColumns[columnIndex];\r\n                const isHeaderRow = $row.hasClass(\"dx-header-row\");\r\n                const isDataRow = $row.hasClass(\"dx-data-row\");\r\n                const isMasterDetailRow = $row.hasClass(DETAIL_ROW_CLASS);\r\n                const isGroupRow = $row.hasClass(\"dx-group-row\");\r\n                const isFilterRow = $row.hasClass(this.addWidgetPrefix(\"filter-row\"));\r\n                const isDataRowWithTemplate = isDataRow && (!column || column.cellTemplate);\r\n                const isEditorShown = isDataRow && cellOptions && (rowOptions.isEditing || cellOptions.isEditing || (null === column || void 0 === column ? void 0 : column.showEditorAlways));\r\n                const isHeaderRowWithTemplate = isHeaderRow && (!column || column.headerCellTemplate);\r\n                const isGroupCellWithTemplate = isGroupRow && (!column || column.groupIndex && column.groupCellTemplate);\r\n                const shouldShowHint = !isMasterDetailRow && !isFilterRow && !isEditorShown && !isDataRowWithTemplate && !isHeaderRowWithTemplate && !isGroupCellWithTemplate;\r\n                if (shouldShowHint) {\r\n                    if ($element.data(CELL_HINT_VISIBLE)) {\r\n                        $element.removeAttr(\"title\");\r\n                        $element.data(CELL_HINT_VISIBLE, false)\r\n                    }\r\n                    const difference = $element[0].scrollWidth - $element[0].clientWidth;\r\n                    if (difference > 0 && !isDefined($element.attr(\"title\"))) {\r\n                        $element.attr(\"title\", $element.text());\r\n                        $element.data(CELL_HINT_VISIBLE, true)\r\n                    }\r\n                }\r\n            })))\r\n        }\r\n        const getOptions = event => {\r\n            const $cell = $(event.currentTarget);\r\n            const $fieldItemContent = $(event.target).closest(\".dx-field-item-content\");\r\n            const $row = $cell.parent();\r\n            const rowOptions = $row.data(\"options\");\r\n            const options = rowOptions && rowOptions.cells && rowOptions.cells[$cell.index()];\r\n            if (!$cell.closest(\"table\").is(event.delegateTarget)) {\r\n                return\r\n            }\r\n            const resultOptions = extend({}, options, {\r\n                cellElement: getPublicElement($cell),\r\n                event: event,\r\n                eventType: event.type\r\n            });\r\n            resultOptions.rowIndex = this.getRowIndex($row);\r\n            if ($fieldItemContent.length) {\r\n                const formItemOptions = $fieldItemContent.data(\"dx-form-item\");\r\n                if (formItemOptions.column) {\r\n                    resultOptions.column = formItemOptions.column;\r\n                    resultOptions.columnIndex = this._columnsController.getVisibleIndex(resultOptions.column.index)\r\n                }\r\n            }\r\n            return resultOptions\r\n        };\r\n        eventsEngine.on($table, \"mouseover\", \".dx-row > td\", (e => {\r\n            const options = getOptions(e);\r\n            options && this.executeAction(\"onCellHoverChanged\", options)\r\n        }));\r\n        eventsEngine.on($table, \"mouseout\", \".dx-row > td\", (e => {\r\n            const options = getOptions(e);\r\n            options && this.executeAction(\"onCellHoverChanged\", options)\r\n        }));\r\n        eventsEngine.on($table, clickEventName, \".dx-row > td\", (e => {\r\n            const options = getOptions(e);\r\n            options && this.executeAction(\"onCellClick\", options)\r\n        }));\r\n        eventsEngine.on($table, dblclickEvent, \".dx-row > td\", (e => {\r\n            const options = getOptions(e);\r\n            options && this.executeAction(\"onCellDblClick\", options)\r\n        }));\r\n        subscribeToRowEvents(this, $table);\r\n        return $table\r\n    }\r\n    _rowPointerDown(e) {}\r\n    _rowClick() {}\r\n    _rowDblClick() {}\r\n    _createColGroup(columns) {\r\n        const colgroupElement = $(\"<colgroup>\");\r\n        for (let i = 0; i < columns.length; i++) {\r\n            const colspan = columns[i].colspan || 1;\r\n            for (let j = 0; j < colspan; j++) {\r\n                colgroupElement.append(this._createCol(columns[i]))\r\n            }\r\n        }\r\n        return colgroupElement\r\n    }\r\n    _createCol(column) {\r\n        let width = column.visibleWidth || column.width;\r\n        if (\"adaptiveHidden\" === width) {\r\n            width = \"0.0001px\"\r\n        }\r\n        const col = $(\"<col>\");\r\n        setWidth(col, width);\r\n        return col\r\n    }\r\n    renderDelayedTemplates(change) {\r\n        const delayedTemplates = this._delayedTemplates;\r\n        const syncTemplates = delayedTemplates.filter((template => !template.async));\r\n        const asyncTemplates = delayedTemplates.filter((template => template.async));\r\n        this._delayedTemplates = [];\r\n        this._renderDelayedTemplatesCore(syncTemplates, false, change);\r\n        this._renderDelayedTemplatesCoreAsync(asyncTemplates)\r\n    }\r\n    _renderDelayedTemplatesCoreAsync(templates) {\r\n        if (templates.length) {\r\n            const templateTimeout = getWindow().setTimeout((() => {\r\n                this._templateTimeouts.delete(templateTimeout);\r\n                this._renderDelayedTemplatesCore(templates, true)\r\n            }));\r\n            this._templateTimeouts.add(templateTimeout)\r\n        }\r\n    }\r\n    _renderDelayedTemplatesCore(templates, isAsync, change) {\r\n        const date = new Date;\r\n        while (templates.length) {\r\n            const templateParameters = templates.shift();\r\n            const {\r\n                options: options\r\n            } = templateParameters;\r\n            const doc = domAdapter.getRootNode($(options.container).get(0));\r\n            const needWaitAsyncTemplates = this.needWaitAsyncTemplates();\r\n            if (!isAsync || $(options.container).closest(doc).length || needWaitAsyncTemplates) {\r\n                if (change) {\r\n                    options.change = change\r\n                }\r\n                templateParameters.template.render(options)\r\n            }\r\n            if (isAsync && new Date - date > 30) {\r\n                this._renderDelayedTemplatesCoreAsync(templates);\r\n                break\r\n            }\r\n        }\r\n        if (!templates.length && this._delayedTemplates.length) {\r\n            this.renderDelayedTemplates()\r\n        }\r\n    }\r\n    _processTemplate(template, options) {\r\n        const that = this;\r\n        let renderingTemplate;\r\n        if (template && template.render && !isRenderer(template)) {\r\n            renderingTemplate = {\r\n                allowRenderToDetachedContainer: template.allowRenderToDetachedContainer,\r\n                render(options) {\r\n                    template.render(options.container, options.model, options.change);\r\n                    options.deferred && options.deferred.resolve()\r\n                }\r\n            }\r\n        } else if (isFunction(template)) {\r\n            renderingTemplate = {\r\n                render(options) {\r\n                    const renderedTemplate = template(getPublicElement(options.container), options.model, options.change);\r\n                    if (renderedTemplate && (renderedTemplate.nodeType || isRenderer(renderedTemplate))) {\r\n                        options.container.append(renderedTemplate)\r\n                    }\r\n                    options.deferred && options.deferred.resolve()\r\n                }\r\n            }\r\n        } else {\r\n            const templateID = isString(template) ? template : $(template).attr(\"id\");\r\n            if (!templateID) {\r\n                renderingTemplate = that.getTemplate(template)\r\n            } else {\r\n                if (!that._templatesCache[templateID]) {\r\n                    that._templatesCache[templateID] = that.getTemplate(template)\r\n                }\r\n                renderingTemplate = that._templatesCache[templateID]\r\n            }\r\n        }\r\n        return renderingTemplate\r\n    }\r\n    renderTemplate(container, template, options, allowRenderToDetachedContainer, change) {\r\n        const renderingTemplate = this._processTemplate(template, options);\r\n        const {\r\n            column: column\r\n        } = options;\r\n        const isDataRow = \"data\" === options.rowType;\r\n        const templateDeferred = new Deferred;\r\n        const templateOptions = {\r\n            container: container,\r\n            model: options,\r\n            deferred: templateDeferred,\r\n            onRendered: () => {\r\n                if (this.isDisposed()) {\r\n                    templateDeferred.reject()\r\n                } else {\r\n                    templateDeferred.resolve()\r\n                }\r\n            }\r\n        };\r\n        if (renderingTemplate) {\r\n            options.component = this.component;\r\n            const columnAsync = column && (column.renderAsync && isDataRow || this.option(\"renderAsync\") && (false !== column.renderAsync && (column.command || column.showEditorAlways) && isDataRow || \"filter\" === options.rowType));\r\n            const async = options.renderAsync ?? columnAsync;\r\n            if ((renderingTemplate.allowRenderToDetachedContainer || allowRenderToDetachedContainer) && !async) {\r\n                renderingTemplate.render(templateOptions)\r\n            } else {\r\n                this._delayedTemplates.push({\r\n                    template: renderingTemplate,\r\n                    options: templateOptions,\r\n                    async: async\r\n                })\r\n            }\r\n            this._templateDeferreds.add(templateDeferred);\r\n            eventsEngine.on(container, removeEvent, removeHandler.bind(null, templateDeferred))\r\n        } else {\r\n            templateDeferred.reject()\r\n        }\r\n        return templateDeferred.promise().always((() => {\r\n            this._templateDeferreds.delete(templateDeferred)\r\n        }))\r\n    }\r\n    _getBodies(tableElement) {\r\n        return $(tableElement).children(\"tbody\").not(\".dx-header\").not(\".dx-footer\")\r\n    }\r\n    _needWrapRow($tableElement) {\r\n        var _this$_getBodies;\r\n        const hasRowTemplate = !!this.option().rowTemplate;\r\n        return hasRowTemplate && !!(null !== (_this$_getBodies = this._getBodies($tableElement)) && void 0 !== _this$_getBodies && _this$_getBodies.filter(\".dx-row\").length)\r\n    }\r\n    _wrapRowIfNeed($table, $row, isRefreshing) {\r\n        const $tableElement = isRefreshing ? $table || this._tableElement : this._tableElement || $table;\r\n        const needWrapRow = this._needWrapRow($tableElement);\r\n        if (needWrapRow) {\r\n            const $tbody = $(\"<tbody>\").addClass($row.attr(\"class\"));\r\n            this.setAria(\"role\", \"presentation\", $tbody);\r\n            return $tbody.append($row)\r\n        }\r\n        return $row\r\n    }\r\n    _appendRow($table, $row, appendTemplate) {\r\n        appendTemplate = appendTemplate || appendElementTemplate;\r\n        appendTemplate.render({\r\n            content: $row,\r\n            container: $table\r\n        })\r\n    }\r\n    _resizeCore() {\r\n        const scrollLeft = this._scrollLeft;\r\n        if (scrollLeft >= 0) {\r\n            this._scrollLeft = 0;\r\n            this.scrollTo({\r\n                left: scrollLeft\r\n            })\r\n        }\r\n    }\r\n    _renderCore(e) {\r\n        const $root = this.element().parent();\r\n        if (!$root || $root.parent().length) {\r\n            this.renderDelayedTemplates(e)\r\n        }\r\n        return (new Deferred).resolve()\r\n    }\r\n    _renderTable(options) {\r\n        options = options || {};\r\n        options.columns = this._columnsController.getVisibleColumns();\r\n        const changeType = options.change && options.change.changeType;\r\n        const $table = this._createTable(options.columns, \"append\" === changeType || \"prepend\" === changeType || \"update\" === changeType);\r\n        this._renderRows($table, options);\r\n        return $table\r\n    }\r\n    _renderRows($table, options) {\r\n        const that = this;\r\n        const rows = that._getRows(options.change);\r\n        const columnIndices = options.change && options.change.columnIndices || [];\r\n        const changeTypes = options.change && options.change.changeTypes || [];\r\n        for (let i = 0; i < rows.length; i++) {\r\n            that._renderRow($table, extend({\r\n                row: rows[i],\r\n                columnIndices: columnIndices[i],\r\n                changeType: changeTypes[i]\r\n            }, options))\r\n        }\r\n    }\r\n    _renderRow($table, options) {\r\n        if (!options.columnIndices) {\r\n            options.row.cells = []\r\n        }\r\n        const $row = this._createRow(options.row);\r\n        const $wrappedRow = this._wrapRowIfNeed($table, $row);\r\n        if (\"remove\" !== options.changeType) {\r\n            this._renderCells($row, options)\r\n        }\r\n        this._appendRow($table, $wrappedRow);\r\n        const rowOptions = extend({\r\n            columns: options.columns\r\n        }, options.row);\r\n        this._addWatchMethod(rowOptions, options.row);\r\n        this._rowPrepared($wrappedRow, rowOptions, options.row)\r\n    }\r\n    _needRenderCell(columnIndex, columnIndices) {\r\n        return !columnIndices || columnIndices.indexOf(columnIndex) >= 0\r\n    }\r\n    _renderCells($row, options) {\r\n        const that = this;\r\n        let columnIndex = 0;\r\n        const {\r\n            row: row\r\n        } = options;\r\n        const {\r\n            columns: columns\r\n        } = options;\r\n        for (let i = 0; i < columns.length; i++) {\r\n            if (this._needRenderCell(i, options.columnIndices)) {\r\n                that._renderCell($row, extend({\r\n                    column: columns[i],\r\n                    columnIndex: columnIndex,\r\n                    value: row.values && row.values[columnIndex],\r\n                    oldValue: row.oldValues && row.oldValues[columnIndex]\r\n                }, options))\r\n            }\r\n            if (columns[i].colspan > 1) {\r\n                columnIndex += columns[i].colspan\r\n            } else {\r\n                columnIndex++\r\n            }\r\n        }\r\n    }\r\n    _updateCells($rowElement, $newRowElement, columnIndices, options) {\r\n        var _options$node;\r\n        const that = this;\r\n        const $cells = $rowElement.children();\r\n        const $newCells = $newRowElement.children();\r\n        const highlightChanges = this.option(\"highlightChanges\");\r\n        const cellUpdatedClass = this.addWidgetPrefix(\"cell-updated-animation\");\r\n        if (null !== options && void 0 !== options && null !== (_options$node = options.node) && void 0 !== _options$node && _options$node.hasChildren) {\r\n            $cells.each((function() {\r\n                that.setAria(\"expanded\", options.isExpanded, $(this))\r\n            }))\r\n        }\r\n        columnIndices.forEach(((columnIndex, index) => {\r\n            const $cell = $cells.eq(columnIndex);\r\n            const $newCell = $newCells.eq(index);\r\n            $cell.replaceWith($newCell);\r\n            if (highlightChanges && !$newCell.hasClass(\"dx-command-expand\")) {\r\n                $newCell.addClass(cellUpdatedClass)\r\n            }\r\n        }));\r\n        copyAttributes($rowElement.get(0), $newRowElement.get(0))\r\n    }\r\n    _setCellAriaAttributes($cell, cellOptions, options) {\r\n        var _row$node;\r\n        const {\r\n            row: row\r\n        } = options;\r\n        const isFreeSpaceRow = \"freeSpace\" === cellOptions.rowType;\r\n        const isGroupRow = \"group\" === cellOptions.rowType;\r\n        const rowHasChildren = null === row || void 0 === row || null === (_row$node = row.node) || void 0 === _row$node ? void 0 : _row$node.hasChildren;\r\n        if (isFreeSpaceRow) {\r\n            return\r\n        }\r\n        this.setAria(\"role\", \"gridcell\", $cell);\r\n        if (rowHasChildren) {\r\n            this.setAria(\"expanded\", row.isExpanded, $cell)\r\n        }\r\n        const columnIndexOffset = this._columnsController.getColumnIndexOffset();\r\n        const ariaColIndex = isGroupRow ? cellOptions.columnIndex + 1 : cellOptions.columnIndex + columnIndexOffset + 1;\r\n        this.setAria(\"colindex\", ariaColIndex, $cell)\r\n    }\r\n    _renderCell($row, options) {\r\n        const cellOptions = this._getCellOptions(options);\r\n        if (options.columnIndices) {\r\n            if (options.row.cells) {\r\n                const cellIndex = options.row.cells.findIndex((cell => cell.columnIndex === cellOptions.columnIndex));\r\n                options.row.cells[cellIndex] = cellOptions\r\n            }\r\n        } else {\r\n            options.row.cells.push(cellOptions)\r\n        }\r\n        const $cell = this._createCell(cellOptions);\r\n        this._setCellAriaAttributes($cell, cellOptions, options);\r\n        this._renderCellContent($cell, cellOptions, options);\r\n        $row.get(0).appendChild($cell.get(0));\r\n        return $cell\r\n    }\r\n    _renderCellContent($cell, options, renderOptions) {\r\n        const template = this._getCellTemplate(options);\r\n        when(!template || this.renderTemplate($cell, template, options, void 0, renderOptions.change)).done((() => {\r\n            this._updateCell($cell, options)\r\n        }))\r\n    }\r\n    _getCellTemplate(options) {}\r\n    _getRows(change) {\r\n        return []\r\n    }\r\n    _getCellOptions(options) {\r\n        const cellOptions = {\r\n            column: options.column,\r\n            columnIndex: options.columnIndex,\r\n            rowType: options.row.rowType,\r\n            rowIndex: options.row.rowIndex,\r\n            isAltRow: this._isAltRow(options.row)\r\n        };\r\n        this._addWatchMethod(cellOptions);\r\n        return cellOptions\r\n    }\r\n    _addWatchMethod(options, source) {\r\n        if (!this.option(\"repaintChangesOnly\")) {\r\n            return\r\n        }\r\n        const watchers = [];\r\n        source = source || options;\r\n        source.watch = source.watch || function(getter, updateValueFunc, updateRowFunc) {\r\n            let oldValue = getter(source.data);\r\n            const watcher = function(row) {\r\n                if (row && updateRowFunc) {\r\n                    updateRowFunc(row)\r\n                }\r\n                const newValue = getter(source.data);\r\n                if (JSON.stringify(oldValue) !== JSON.stringify(newValue)) {\r\n                    if (row) {\r\n                        updateValueFunc(newValue)\r\n                    }\r\n                    oldValue = newValue\r\n                }\r\n            };\r\n            watchers.push(watcher);\r\n            return function() {\r\n                const index = watchers.indexOf(watcher);\r\n                if (index >= 0) {\r\n                    watchers.splice(index, 1)\r\n                }\r\n            }\r\n        };\r\n        source.update = source.update || function(row, keepRow) {\r\n            if (row) {\r\n                this.data = options.data = row.data;\r\n                this.rowIndex = options.rowIndex = row.rowIndex;\r\n                this.dataIndex = options.dataIndex = row.dataIndex;\r\n                this.isExpanded = options.isExpanded = row.isExpanded;\r\n                if (options.row && !keepRow) {\r\n                    options.row = row\r\n                }\r\n            }\r\n            watchers.forEach((watcher => {\r\n                watcher(row)\r\n            }))\r\n        };\r\n        if (source !== options) {\r\n            options.watch = source.watch.bind(source)\r\n        }\r\n        return options\r\n    }\r\n    _cellPrepared(cell, options) {\r\n        options.cellElement = getPublicElement($(cell));\r\n        this.executeAction(\"onCellPrepared\", options)\r\n    }\r\n    _rowPrepared($row, options, row) {\r\n        elementData($row.get(0), \"options\", options);\r\n        options.rowElement = getPublicElement($row);\r\n        this.executeAction(\"onRowPrepared\", options)\r\n    }\r\n    _columnOptionChanged(e) {\r\n        const {\r\n            optionNames: optionNames\r\n        } = e;\r\n        if (gridCoreUtils.checkChanges(optionNames, [\"width\", \"visibleWidth\"])) {\r\n            const visibleColumns = this._columnsController.getVisibleColumns();\r\n            const widths = visibleColumns.map((column => column.visibleWidth || column.width));\r\n            this.setColumnWidths({\r\n                widths: widths,\r\n                optionNames: optionNames\r\n            });\r\n            return\r\n        }\r\n        if (!this._requireReady) {\r\n            this.render()\r\n        }\r\n    }\r\n    getCellIndex($cell, rowIndex) {\r\n        const cellIndex = $cell.length ? $cell[0].cellIndex : -1;\r\n        return cellIndex\r\n    }\r\n    getTableElements() {\r\n        return this._tableElement || $()\r\n    }\r\n    getTableElement(isFixedTableRendering) {\r\n        return this._tableElement\r\n    }\r\n    setTableElement(tableElement, isFixedTableRendering) {\r\n        this._tableElement = tableElement\r\n    }\r\n    _afterRowPrepared(e) {}\r\n    _handleDataChanged(e) {}\r\n    callbackNames() {\r\n        return [\"scrollChanged\"]\r\n    }\r\n    _updateScrollLeftPosition() {\r\n        const scrollLeft = this._scrollLeft;\r\n        if (scrollLeft >= 0) {\r\n            this._scrollLeft = 0;\r\n            this.scrollTo({\r\n                left: scrollLeft\r\n            })\r\n        }\r\n    }\r\n    scrollTo(pos) {\r\n        const $element = this.element();\r\n        const $scrollContainer = $element && $element.children(`.${this.addWidgetPrefix(\"scroll-container\")}`).not(`.${this.addWidgetPrefix(\"content-fixed\")}`);\r\n        if (isDefined(pos) && isDefined(pos.left) && this._scrollLeft !== pos.left) {\r\n            this._scrollLeft = pos.left;\r\n            $scrollContainer && $scrollContainer.scrollLeft(pos.left)\r\n        }\r\n    }\r\n    getContent(isFixedTableRendering) {\r\n        var _this$_tableElement;\r\n        return null === (_this$_tableElement = this._tableElement) || void 0 === _this$_tableElement ? void 0 : _this$_tableElement.parent()\r\n    }\r\n    _removeContent(isFixedTableRendering) {\r\n        const $scrollContainer = this.getContent(isFixedTableRendering);\r\n        if (null !== $scrollContainer && void 0 !== $scrollContainer && $scrollContainer.length) {\r\n            $scrollContainer.remove()\r\n        }\r\n    }\r\n    handleScroll(e) {\r\n        const scrollLeft = $(e.target).scrollLeft();\r\n        if (scrollLeft !== this._scrollLeft) {\r\n            this.scrollChanged.fire({\r\n                left: scrollLeft\r\n            }, this.name)\r\n        }\r\n    }\r\n    _wrapTableInScrollContainer($table, isFixedTableRendering) {\r\n        const $scrollContainer = $(\"<div>\");\r\n        const useNative = this.option(\"scrolling.useNative\");\r\n        if (false === useNative || \"auto\" === useNative && !supportUtils.nativeScrolling) {\r\n            $scrollContainer.addClass(this.addWidgetPrefix(\"scrollable-simulated\"))\r\n        }\r\n        eventsEngine.on($scrollContainer, \"scroll\", this.handleScroll.bind(this));\r\n        $scrollContainer.addClass(this.addWidgetPrefix(\"content\")).addClass(this.addWidgetPrefix(\"scroll-container\")).append($table).appendTo(this.element());\r\n        this.setAria(\"role\", \"presentation\", $scrollContainer);\r\n        return $scrollContainer\r\n    }\r\n    needWaitAsyncTemplates() {\r\n        return this.option(\"templatesRenderAsynchronously\") && false === this.option(\"renderAsync\")\r\n    }\r\n    waitAsyncTemplates() {\r\n        let forceWaiting = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : false;\r\n        const result = new Deferred;\r\n        const needWaitAsyncTemplates = forceWaiting || this.needWaitAsyncTemplates();\r\n        if (!needWaitAsyncTemplates || !isDefined(this._templateDeferreds)) {\r\n            return result.resolve()\r\n        }\r\n        const waitTemplatesRecursion = () => when.apply(this, Array.from(this._templateDeferreds)).done((() => {\r\n            if (this.isDisposed()) {\r\n                result.reject()\r\n            } else if (this._templateDeferreds.size > 0) {\r\n                waitTemplatesRecursion()\r\n            } else {\r\n                result.resolve()\r\n            }\r\n        })).fail(result.reject);\r\n        waitTemplatesRecursion();\r\n        return result.promise()\r\n    }\r\n    _updateContent($newTableElement, change, isFixedTableRendering) {\r\n        return this.waitAsyncTemplates().done((() => {\r\n            this._removeContent(isFixedTableRendering);\r\n            this.setTableElement($newTableElement, isFixedTableRendering);\r\n            this._wrapTableInScrollContainer($newTableElement, isFixedTableRendering)\r\n        }))\r\n    }\r\n    _findContentElement(isFixedTableRendering) {}\r\n    _getWidths($cellElements) {\r\n        if (!$cellElements) {\r\n            return []\r\n        }\r\n        const result = [];\r\n        const cellElements = $cellElements.toArray();\r\n        cellElements.forEach((cell => {\r\n            let width = cell.offsetWidth;\r\n            if (cell.getBoundingClientRect) {\r\n                const rect = getBoundingRect(cell);\r\n                if (rect.width > cell.offsetWidth - 1) {\r\n                    width = rect.width\r\n                }\r\n            }\r\n            result.push(width)\r\n        }));\r\n        return result\r\n    }\r\n    getColumnWidths($tableElement, rowIndex) {\r\n        (this.option(\"forceApplyBindings\") || noop)();\r\n        $tableElement = $tableElement ?? this.getTableElement();\r\n        if ($tableElement) {\r\n            const $rows = $tableElement.children(\"tbody:not(.dx-header)\").children();\r\n            for (let i = 0; i < $rows.length; i++) {\r\n                const $row = $rows.eq(i);\r\n                const isGroupRow = $row.hasClass(\"dx-group-row\");\r\n                const isDetailRow = $row.hasClass(DETAIL_ROW_CLASS);\r\n                const isErrorRow = $row.hasClass(\"dx-error-row\");\r\n                const isRowVisible = \"none\" !== $row.get(0).style.display && !$row.hasClass(\"dx-state-invisible\");\r\n                const isRelevantRow = !isGroupRow && !isDetailRow && !isErrorRow;\r\n                if (isRowVisible && isRelevantRow) {\r\n                    const $cells = $row.children(\"td\");\r\n                    const result = this._getWidths($cells);\r\n                    return result\r\n                }\r\n            }\r\n        }\r\n        return []\r\n    }\r\n    getVisibleColumnIndex(columnIndex, rowIndex) {\r\n        return columnIndex\r\n    }\r\n    setCellPropertiesCore(styleProps, $row, visibleCellIndex) {\r\n        const $cell = $row.hasClass(\"dx-group-row\") ? $row.find(`td[aria-colindex='${visibleCellIndex+1}']:not(.dx-group-cell)`) : $row.find(\"td\").eq(visibleCellIndex);\r\n        for (let i = 0; i < $cell.length; i += 1) {\r\n            const cell = $cell.get(i);\r\n            Object.assign(cell.style, styleProps)\r\n        }\r\n    }\r\n    setCellProperties(styleProps, columnIndex, rowIndex) {\r\n        const $tableElement = this.getTableElement();\r\n        if (!(null !== $tableElement && void 0 !== $tableElement && $tableElement.length)) {\r\n            return\r\n        }\r\n        const $rows = $tableElement.children().children(\".dx-row\").not(`.${DETAIL_ROW_CLASS}`);\r\n        if (isDefined(rowIndex)) {\r\n            this.setCellPropertiesCore(styleProps, $rows.eq(rowIndex), columnIndex)\r\n        } else {\r\n            for (let rowIndex = 0; rowIndex < $rows.length; rowIndex++) {\r\n                const visibleIndex = this.getVisibleColumnIndex(columnIndex, rowIndex);\r\n                if (visibleIndex >= 0) {\r\n                    this.setCellPropertiesCore(styleProps, $rows.eq(rowIndex), visibleIndex)\r\n                }\r\n            }\r\n        }\r\n    }\r\n    setColumnWidths(_ref2) {\r\n        let {\r\n            widths: widths,\r\n            optionNames: optionNames\r\n        } = _ref2;\r\n        const $tableElement = this.getTableElement();\r\n        if (!(null !== $tableElement && void 0 !== $tableElement && $tableElement.length) || !widths) {\r\n            return\r\n        }\r\n        const columns = this.getColumns();\r\n        const needToSetCellWidths = this._needToSetCellWidths();\r\n        const $cols = $tableElement.children(\"colgroup\").children(\"col\");\r\n        $cols.toArray().forEach((col => col.removeAttribute(\"style\")));\r\n        columns.forEach(((column, columnIndex) => {\r\n            if (needToSetCellWidths && column.width && !column.command) {\r\n                const styleProps = {};\r\n                const width = getWidthStyle(column.visibleWidth || column.width);\r\n                const minWidth = getWidthStyle(column.minWidth || width);\r\n                styleProps.width = \"auto\" === column.width ? \"\" : width;\r\n                styleProps.maxWidth = styleProps.width;\r\n                styleProps.minWidth = minWidth;\r\n                this.setCellProperties(styleProps, columnIndex)\r\n            }\r\n            const colWidth = normalizeWidth(widths[columnIndex]);\r\n            if (isDefined(colWidth)) {\r\n                setWidth($cols.eq(columnIndex), colWidth)\r\n            }\r\n        }))\r\n    }\r\n    getCellElements(rowIndex) {\r\n        return this._getCellElementsCore(rowIndex)\r\n    }\r\n    _getCellElementsCore(rowIndex) {\r\n        if (rowIndex < 0) {\r\n            return\r\n        }\r\n        const $row = this._getRowElements().eq(rowIndex);\r\n        return $row.children()\r\n    }\r\n    _getCellElement(rowIndex, columnIdentifier) {\r\n        const $cells = this.getCellElements(rowIndex);\r\n        const columnVisibleIndex = this._getVisibleColumnIndex($cells, rowIndex, columnIdentifier);\r\n        if (!(null !== $cells && void 0 !== $cells && $cells.length) || columnVisibleIndex < 0) {\r\n            return\r\n        }\r\n        const $cell = $cells.eq(columnVisibleIndex);\r\n        return $cell.length > 0 ? $cell : void 0\r\n    }\r\n    _getRowElement(rowIndex) {\r\n        const that = this;\r\n        let $rowElement = $();\r\n        const $tableElements = that.getTableElements();\r\n        iteratorUtils.each($tableElements, ((_, tableElement) => {\r\n            $rowElement = $rowElement.add(that._getRowElements($(tableElement)).eq(rowIndex))\r\n        }));\r\n        if ($rowElement.length) {\r\n            return $rowElement\r\n        }\r\n        return\r\n    }\r\n    getCellElement(rowIndex, columnIdentifier) {\r\n        const $cell = this._getCellElement(rowIndex, columnIdentifier);\r\n        if ($cell) {\r\n            return getPublicElement($cell)\r\n        }\r\n        return\r\n    }\r\n    getRowElement(rowIndex) {\r\n        const $rows = this._getRowElement(rowIndex);\r\n        let elements = [];\r\n        if ($rows && !getPublicElement($rows).get) {\r\n            for (let i = 0; i < $rows.length; i++) {\r\n                elements.push($rows[i])\r\n            }\r\n        } else {\r\n            elements = $rows\r\n        }\r\n        return elements\r\n    }\r\n    _getVisibleColumnIndex($cells, rowIndex, columnIdentifier) {\r\n        if (isString(columnIdentifier)) {\r\n            const columnIndex = this._columnsController.columnOption(columnIdentifier, \"index\");\r\n            return this._columnsController.getVisibleIndex(columnIndex)\r\n        }\r\n        return columnIdentifier\r\n    }\r\n    getColumnElements() {}\r\n    getColumns(rowIndex, $tableElement) {\r\n        return this._columnsController.getVisibleColumns(rowIndex)\r\n    }\r\n    getCell(cellPosition, rows, cells) {\r\n        const $rows = rows || this._getRowElements();\r\n        let $cells;\r\n        if ($rows.length > 0 && cellPosition.rowIndex >= 0) {\r\n            var _$cells;\r\n            if (\"virtual\" !== this.option(\"scrolling.mode\") && \"virtual\" !== this.option(\"scrolling.rowRenderingMode\")) {\r\n                cellPosition.rowIndex = cellPosition.rowIndex < $rows.length ? cellPosition.rowIndex : $rows.length - 1\r\n            }\r\n            $cells = cells || this.getCellElements(cellPosition.rowIndex);\r\n            if ((null === (_$cells = $cells) || void 0 === _$cells ? void 0 : _$cells.length) > 0) {\r\n                return $cells.eq($cells.length > cellPosition.columnIndex ? cellPosition.columnIndex : $cells.length - 1)\r\n            }\r\n        }\r\n    }\r\n    getRowsCount() {\r\n        const tableElement = this.getTableElement();\r\n        if (tableElement && 1 === tableElement.length) {\r\n            return tableElement[0].rows.length\r\n        }\r\n        return 0\r\n    }\r\n    _getRowElementsCore(tableElement) {\r\n        tableElement = tableElement || this.getTableElement();\r\n        if (tableElement) {\r\n            const hasRowTemplate = this.option().rowTemplate || this.option(\"dataRowTemplate\");\r\n            const tBodies = hasRowTemplate && tableElement.find(\"> tbody.dx-row\");\r\n            return tBodies && tBodies.length ? tBodies : tableElement.find(\"> tbody > .dx-row, > .dx-row\")\r\n        }\r\n        return $()\r\n    }\r\n    _getRowElements(tableElement) {\r\n        return this._getRowElementsCore(tableElement)\r\n    }\r\n    getRowIndex($row) {\r\n        return this._getRowElements().index($row)\r\n    }\r\n    getBoundingRect() {}\r\n    getName() {}\r\n    setScrollerSpacing(width) {\r\n        const $element = this.element();\r\n        const rtlEnabled = this.option(\"rtlEnabled\");\r\n        $element && $element.css({\r\n            paddingLeft: rtlEnabled ? width : \"\",\r\n            paddingRight: !rtlEnabled ? width : \"\"\r\n        })\r\n    }\r\n    isScrollbarVisible(isHorizontal) {\r\n        const $element = this.element();\r\n        const $tableElement = this._tableElement;\r\n        if ($element && $tableElement) {\r\n            return isHorizontal ? getOuterWidth($tableElement) - getWidth($element) > 0 : getOuterHeight($tableElement) - getHeight($element) > 0\r\n        }\r\n        return false\r\n    }\r\n    isDisposed() {\r\n        var _this$component;\r\n        return null === (_this$component = this.component) || void 0 === _this$component ? void 0 : _this$component._disposed\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AAAA;AACA;AAAA;AAGA;AACA;AAAA;AAGA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAGA;AAAA;AAIA;AAAA;AAGA;AAAA;AACA;AAAA;AAIA;AAAA;AAMA;AAAA;AAGA;AAAA;AAOA;AAAA;AAIA;AAAA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;AACA,MAAM,yBAAyB;AAC/B,MAAM,6BAA6B;AACnC,MAAM,oBAAoB;AAC1B,MAAM,gBAAgB;AACtB,MAAM,cAAc;AACpB,MAAM,oBAAoB;AAC1B,MAAM,sBAAsB;AAC5B,MAAM,YAAY;AAClB,MAAM,kBAAkB;AACxB,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AACzB,MAAM,mBAAmB;AACzB,MAAM,kBAAkB;AACxB,MAAM,+BAA+B;AACrC,MAAM,sBAAsB;AAC5B,MAAM,uBAAuB;AAC7B,MAAM,oBAAoB;AAC1B,MAAM,gCAAgC;AACtC,MAAM,wBAAwB;IAC1B,QAAO,OAAO;QACV,QAAQ,SAAS,CAAC,MAAM,CAAC,QAAQ,OAAO;IAC5C;AACJ;AACA,MAAM,uBAAuB,SAAS,IAAI,EAAE,MAAM;IAC9C,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,SAAS,kBAAkB,OAAO;QAC9B,OAAO,WAAY;YACf,cAAc,qBAAqB;QACvC,GAAI;IACR;IACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,uBAAuB,WAAY,CAAA;QACvD,aAAa;QACb,IAAI,iBAAiB,EAAE,IAAI,EAAE;YACzB,cAAc,EAAE,MAAM;YACtB,qBAAqB,EAAE,aAAa;YACpC,YAAY,kBAAkB;QAClC,OAAO;YACH,YAAY;QAChB;IACJ;IACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ;QAAC,0KAAA,CAAA,OAAc;QAAE,6KAAA,CAAA,OAAa;QAAE,4KAAA,CAAA,UAAa,CAAC,IAAI;KAAC,CAAC,IAAI,CAAC,MAAM,WAAW,KAAK,YAAY,CAAE,CAAA;QACjH,MAAM,EACF,OAAO,KAAK,EACf,GAAG;QACJ,IAAI,aAAa;YACb,MAAM,MAAM,GAAG;YACf,MAAM,aAAa,GAAG;QAC1B;QACA,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,MAAM,EAAE,OAAO,CAAC,KAAK,MAAM,EAAE;YACtC,EAAE,QAAQ,GAAG,KAAK,WAAW,CAAC,MAAM,aAAa;YACjD,IAAI,EAAE,QAAQ,IAAI,GAAG;gBACjB,EAAE,UAAU,GAAG,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,aAAa;gBACrD,EAAE,OAAO,GAAG,KAAK,UAAU;gBAC3B,IAAI,MAAM,IAAI,KAAK,4KAAA,CAAA,UAAa,CAAC,IAAI,EAAE;oBACnC,KAAK,eAAe,CAAC;gBACzB,OAAO,IAAI,MAAM,IAAI,KAAK,0KAAA,CAAA,OAAc,EAAE;oBACtC,KAAK,SAAS,CAAC;gBACnB,OAAO;oBACH,KAAK,YAAY,CAAC;gBACtB;YACJ;QACJ;IACJ;AACJ;AACA,MAAM,gBAAgB,SAAS,KAAK;IAChC,IAAI,WAAW,OAAO;QAClB,OAAO;IACX;IACA,OAAO,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,AAAC,GAAQ,OAAN,OAAM,QAAM;AAC7C;AACA,MAAM,eAAe,SAAS,IAAI,EAAE,MAAM,EAAE,KAAK;IAC7C,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK,KAAK,CAAC,QAAQ,GAAG,WAAW,OAAO,KAAK,GAAG,KAAK;AAC5E;AACA,MAAM,iBAAiB,SAAS,OAAO,EAAE,UAAU;IAC/C,IAAI,CAAC,WAAW,CAAC,YAAY;QACzB;IACJ;IACA,MAAM,gBAAgB,QAAQ,UAAU;IACxC,MAAM,gBAAgB,WAAW,UAAU;IAC3C,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QACvC,MAAM,OAAO,aAAa,CAAC,EAAE,CAAC,QAAQ;QACtC,IAAI,CAAC,WAAW,YAAY,CAAC,OAAO;YAChC,QAAQ,eAAe,CAAC;QAC5B;IACJ;IACA,IAAK,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;QACvC,QAAQ,YAAY,CAAC,aAAa,CAAC,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC,EAAE,CAAC,SAAS;IAC9E;AACJ;AACA,MAAM,gBAAgB,SAAS,gBAAgB;IAC3C,iBAAiB,OAAO;AAC5B;AACO,MAAM,iBAAiB,CAAA;IAC1B,IAAI,aAAa,OAAO,OAAO;QAC3B,OAAO,AAAC,GAAmB,OAAjB,MAAM,OAAO,CAAC,IAAG;IAC/B;IACA,IAAI,qBAAqB,OAAO;QAC5B,OAAO;IACX;IACA,OAAO;AACX;AACO,MAAM,oBAAoB,CAAA,GAAA,yNAAA,CAAA,mBAAgB,AAAD,EAAE,wLAAA,CAAA,UAAO,CAAC,IAAI;IAC1D,OAAO;QACH,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAC1C,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,aAAa,CAAC;QACrD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC/C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAAC,kBAAkB,GAAG,IAAI;QAC9B,IAAI,CAAC,eAAe,GAAG,CAAC;QACxB,IAAI,CAAC,iBAAiB,GAAG,IAAI;QAC7B,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC;QAClB,IAAI,CAAC,YAAY,CAAC,sBAAsB;YACpC,mBAAmB;gBAAC;gBAAY;aAAW;QAC/C;QACA,IAAI,CAAC,YAAY,CAAC,kBAAkB;YAChC,mBAAmB;gBAAC;gBAAY;aAAW;YAC3C,UAAU;QACd;QACA,IAAI,CAAC,YAAY,CAAC,iBAAiB;YAC/B,mBAAmB;gBAAC;gBAAY;aAAW;YAC3C,UAAU;YACV,cAAc,CAAA;gBACV,IAAI,CAAC,iBAAiB,CAAC;YAC3B;QACJ;QACA,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,IAAI;QAC9E,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,IAAI;IAC9F;IACA,UAAU;QACN,IAAI,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,KAAK;YACb,IAAI,uBAAuB;YAC3B,MAAM,SAAS,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD;YACvB,SAAS,CAAC,wBAAwB,IAAI,CAAC,iBAAiB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,OAAO,CAAE,CAAA,kBAAmB,OAAO,YAAY,CAAC;YACvK,SAAS,CAAC,yBAAyB,IAAI,CAAC,iBAAiB,KAAK,KAAK,MAAM,0BAA0B,uBAAuB,KAAK;QACnI;IACJ;IACA,cAAc,IAAI,EAAE;QAChB,KAAK,CAAC,cAAc;QACpB,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,WAAW,CAAC,MAAM;gBACvB,KAAK,OAAO,GAAG;gBACf;YACJ,KAAK;gBACD,IAAI,iCAAiC,KAAK,QAAQ,EAAE;oBAChD,IAAI,CAAC,WAAW,CAAC,MAAM;gBAC3B;gBACA,KAAK,OAAO,GAAG;QACvB;IACJ;IACA,2BAA2B;QACvB,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC;QACrC,IAAI,qBAAqB,IAAI,CAAC,MAAM,CAAC;QACrC,MAAM,UAAU,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,kBAAkB;YACzC,WAAW;YACX,eAAe;YACf,aAAa;QACjB;QACA,IAAI,KAAK,MAAM,oBAAoB;YAC/B,qBAAqB;QACzB;QACA,IAAI,WAAW,oBAAoB;YAC/B,OAAO,QAAQ,SAAS;YACxB,OAAO,QAAQ,qBAAqB;QACxC,OAAO;YACH,QAAQ,SAAS,GAAG,CAAC,CAAC;YACtB,QAAQ,qBAAqB,GAAG,CAAC;QACrC;QACA,OAAO;IACX;IACA,YAAY,KAAK,EAAE,UAAU,EAAE;QAC3B,IAAI,WAAW,OAAO,EAAE;YACpB,IAAI,CAAC,aAAa,CAAC,OAAO;QAC9B;IACJ;IACA,uBAAuB;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,YAAY,OAAO,EAAE;QACjB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,MAAM,YAAY,OAAO,SAAS,IAAI,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC;QACtE,MAAM,sBAAsB,IAAI,CAAC,oBAAoB;QACrD,MAAM,OAAO,2JAAA,CAAA,UAAU,CAAC,aAAa,CAAC;QACtC,KAAK,KAAK,CAAC,SAAS,GAAG;QACvB,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QAChB,IAAI,OAAO,QAAQ,EAAE;YACjB,MAAM,QAAQ,CAAC,OAAO,QAAQ;QAClC;QACA,IAAI,MAAM,OAAO,CAAC,OAAO,WAAW,GAAG;YACnC,OAAO,WAAW,CAAC,OAAO,CAAE,CAAA;gBACxB,IAAI,EACA,MAAM,IAAI,EACV,OAAO,KAAK,EACf,GAAG;gBACJ,MAAM,IAAI,CAAC,MAAM;YACrB;QACJ;QACA,IAAI,aAAa,OAAO,OAAO,EAAE;YAC7B,MAAM,QAAQ,CAAC,OAAO,QAAQ;YAC9B,MAAM,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;QACxC;QACA,IAAI,OAAO,OAAO,GAAG,GAAG;YACpB,MAAM,IAAI,CAAC,WAAW,OAAO,OAAO;QACxC,OAAO,IAAI,CAAC,OAAO,MAAM,IAAI,WAAW,OAAO,YAAY,IAAI,qBAAqB;YAChF,IAAI,OAAO,KAAK,IAAI,OAAO,QAAQ,EAAE;gBACjC,KAAK,KAAK,CAAC,QAAQ,GAAG,cAAc,OAAO,QAAQ,IAAI,OAAO,KAAK;YACvE;YACA,IAAI,OAAO,KAAK,EAAE;gBACd,aAAa,MAAM,QAAQ,cAAc,OAAO,KAAK;YACzD;QACJ;QACA,OAAO;IACX;IACA,WAAW,SAAS,EAAE,OAAO,EAAE;QAC3B,UAAU,WAAW;QACrB,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,AAAC,IAAW,OAAR,SAAQ,MAAI,QAAQ,CAAC;QAC5C,IAAI,SAAS,SAAS;YAClB,IAAI,CAAC,OAAO,CAAC,QAAQ,OAAO;QAChC;QACA,OAAO;IACX;IACA,UAAU,GAAG,EAAE;QACX,OAAO,OAAO,IAAI,SAAS,GAAG,MAAM;IACxC;IACA,aAAa,OAAO,EAAE,QAAQ,EAAE;QAC5B,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,UAAU,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;QAClG,IAAI,WAAW,CAAC,UAAU;YACtB,OAAO,IAAI,CAAC,MAAM,AAAC,MAAc,OAAT,IAAI,oJAAA,CAAA,UAAI,GAAI,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC;YAChE,IAAI,gKAAA,CAAA,UAAO,CAAC,MAAM,EAAE;gBAChB,OAAO,MAAM,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,MAAM,CAAC;YACtC;YACA,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB;QACzC,OAAO;YACH,IAAI,CAAC,OAAO,CAAC,UAAU,MAAM;QACjC;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,QAAQ,CAAC;QAC3D,IAAI,UAAU;YACV,OAAO;QACX;QACA,IAAI,gKAAA,CAAA,UAAO,CAAC,OAAO,EAAE;YACjB,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,aAAa,MAAO,CAAA;gBACxC,IAAI,EAAE,OAAO,EAAE;oBACX,EAAE,cAAc;gBACpB;YACJ;QACJ;QACA,IAAI,IAAI,CAAC,MAAM,CAAC,oBAAoB;YAChC,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,aAAa,gBAAgB,IAAI,CAAC,YAAY,CAAE,CAAA;gBACpE,MAAM,IAAI,KAAK,KAAK;gBACpB,MAAM,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM;gBAC3B,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,aAAa;gBAC/B,MAAM,OAAO,MAAM,MAAM;gBACzB,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;gBAChE,MAAM,aAAa,KAAK,IAAI,CAAC;gBAC7B,MAAM,cAAc,MAAM,KAAK;gBAC/B,MAAM,cAAc,cAAc,WAAW,KAAK,IAAI,WAAW,KAAK,CAAC,YAAY;gBACnF,MAAM,SAAS,cAAc,YAAY,MAAM,GAAG,cAAc,CAAC,YAAY;gBAC7E,MAAM,cAAc,KAAK,QAAQ,CAAC;gBAClC,MAAM,YAAY,KAAK,QAAQ,CAAC;gBAChC,MAAM,oBAAoB,KAAK,QAAQ,CAAC;gBACxC,MAAM,aAAa,KAAK,QAAQ,CAAC;gBACjC,MAAM,cAAc,KAAK,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBACvD,MAAM,wBAAwB,aAAa,CAAC,CAAC,UAAU,OAAO,YAAY;gBAC1E,MAAM,gBAAgB,aAAa,eAAe,CAAC,WAAW,SAAS,IAAI,YAAY,SAAS,IAAI,CAAC,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,gBAAgB,CAAC;gBAC7K,MAAM,0BAA0B,eAAe,CAAC,CAAC,UAAU,OAAO,kBAAkB;gBACpF,MAAM,0BAA0B,cAAc,CAAC,CAAC,UAAU,OAAO,UAAU,IAAI,OAAO,iBAAiB;gBACvG,MAAM,iBAAiB,CAAC,qBAAqB,CAAC,eAAe,CAAC,iBAAiB,CAAC,yBAAyB,CAAC,2BAA2B,CAAC;gBACtI,IAAI,gBAAgB;oBAChB,IAAI,SAAS,IAAI,CAAC,oBAAoB;wBAClC,SAAS,UAAU,CAAC;wBACpB,SAAS,IAAI,CAAC,mBAAmB;oBACrC;oBACA,MAAM,aAAa,QAAQ,CAAC,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,EAAE,CAAC,WAAW;oBACpE,IAAI,aAAa,KAAK,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,WAAW;wBACtD,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI;wBACpC,SAAS,IAAI,CAAC,mBAAmB;oBACrC;gBACJ;YACJ;QACJ;QACA,MAAM,aAAa,CAAA;YACf,MAAM,QAAQ,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,aAAa;YACnC,MAAM,oBAAoB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,MAAM,EAAE,OAAO,CAAC;YAClD,MAAM,OAAO,MAAM,MAAM;YACzB,MAAM,aAAa,KAAK,IAAI,CAAC;YAC7B,MAAM,UAAU,cAAc,WAAW,KAAK,IAAI,WAAW,KAAK,CAAC,MAAM,KAAK,GAAG;YACjF,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,EAAE,CAAC,MAAM,cAAc,GAAG;gBAClD;YACJ;YACA,MAAM,gBAAgB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,SAAS;gBACtC,aAAa,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;gBAC9B,OAAO;gBACP,WAAW,MAAM,IAAI;YACzB;YACA,cAAc,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC;YAC1C,IAAI,kBAAkB,MAAM,EAAE;gBAC1B,MAAM,kBAAkB,kBAAkB,IAAI,CAAC;gBAC/C,IAAI,gBAAgB,MAAM,EAAE;oBACxB,cAAc,MAAM,GAAG,gBAAgB,MAAM;oBAC7C,cAAc,WAAW,GAAG,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC,cAAc,MAAM,CAAC,KAAK;gBAClG;YACJ;YACA,OAAO;QACX;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,aAAa,gBAAiB,CAAA;YAClD,MAAM,UAAU,WAAW;YAC3B,WAAW,IAAI,CAAC,aAAa,CAAC,sBAAsB;QACxD;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,YAAY,gBAAiB,CAAA;YACjD,MAAM,UAAU,WAAW;YAC3B,WAAW,IAAI,CAAC,aAAa,CAAC,sBAAsB;QACxD;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,0KAAA,CAAA,OAAc,EAAE,gBAAiB,CAAA;YACrD,MAAM,UAAU,WAAW;YAC3B,WAAW,IAAI,CAAC,aAAa,CAAC,eAAe;QACjD;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,QAAQ,6KAAA,CAAA,OAAa,EAAE,gBAAiB,CAAA;YACpD,MAAM,UAAU,WAAW;YAC3B,WAAW,IAAI,CAAC,aAAa,CAAC,kBAAkB;QACpD;QACA,qBAAqB,IAAI,EAAE;QAC3B,OAAO;IACX;IACA,gBAAgB,CAAC,EAAE,CAAC;IACpB,YAAY,CAAC;IACb,eAAe,CAAC;IAChB,gBAAgB,OAAO,EAAE;QACrB,MAAM,kBAAkB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,MAAM,UAAU,OAAO,CAAC,EAAE,CAAC,OAAO,IAAI;YACtC,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,IAAK;gBAC9B,gBAAgB,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;YACrD;QACJ;QACA,OAAO;IACX;IACA,WAAW,MAAM,EAAE;QACf,IAAI,QAAQ,OAAO,YAAY,IAAI,OAAO,KAAK;QAC/C,IAAI,qBAAqB,OAAO;YAC5B,QAAQ;QACZ;QACA,MAAM,MAAM,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACd,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;QACd,OAAO;IACX;IACA,uBAAuB,MAAM,EAAE;QAC3B,MAAM,mBAAmB,IAAI,CAAC,iBAAiB;QAC/C,MAAM,gBAAgB,iBAAiB,MAAM,CAAE,CAAA,WAAY,CAAC,SAAS,KAAK;QAC1E,MAAM,iBAAiB,iBAAiB,MAAM,CAAE,CAAA,WAAY,SAAS,KAAK;QAC1E,IAAI,CAAC,iBAAiB,GAAG,EAAE;QAC3B,IAAI,CAAC,2BAA2B,CAAC,eAAe,OAAO;QACvD,IAAI,CAAC,gCAAgC,CAAC;IAC1C;IACA,iCAAiC,SAAS,EAAE;QACxC,IAAI,UAAU,MAAM,EAAE;YAClB,MAAM,kBAAkB,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,IAAI,UAAU,CAAE;gBAC5C,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC;gBAC9B,IAAI,CAAC,2BAA2B,CAAC,WAAW;YAChD;YACA,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QAC/B;IACJ;IACA,4BAA4B,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;QACpD,MAAM,OAAO,IAAI;QACjB,MAAO,UAAU,MAAM,CAAE;YACrB,MAAM,qBAAqB,UAAU,KAAK;YAC1C,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;YACJ,MAAM,MAAM,2JAAA,CAAA,UAAU,CAAC,WAAW,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,SAAS,EAAE,GAAG,CAAC;YAC5D,MAAM,yBAAyB,IAAI,CAAC,sBAAsB;YAC1D,IAAI,CAAC,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,QAAQ,SAAS,EAAE,OAAO,CAAC,KAAK,MAAM,IAAI,wBAAwB;gBAChF,IAAI,QAAQ;oBACR,QAAQ,MAAM,GAAG;gBACrB;gBACA,mBAAmB,QAAQ,CAAC,MAAM,CAAC;YACvC;YACA,IAAI,WAAW,IAAI,OAAO,OAAO,IAAI;gBACjC,IAAI,CAAC,gCAAgC,CAAC;gBACtC;YACJ;QACJ;QACA,IAAI,CAAC,UAAU,MAAM,IAAI,IAAI,CAAC,iBAAiB,CAAC,MAAM,EAAE;YACpD,IAAI,CAAC,sBAAsB;QAC/B;IACJ;IACA,iBAAiB,QAAQ,EAAE,OAAO,EAAE;QAChC,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI,YAAY,SAAS,MAAM,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,WAAW;YACtD,oBAAoB;gBAChB,gCAAgC,SAAS,8BAA8B;gBACvE,QAAO,OAAO;oBACV,SAAS,MAAM,CAAC,QAAQ,SAAS,EAAE,QAAQ,KAAK,EAAE,QAAQ,MAAM;oBAChE,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,OAAO;gBAChD;YACJ;QACJ,OAAO,IAAI,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,WAAW;YAC7B,oBAAoB;gBAChB,QAAO,OAAO;oBACV,MAAM,mBAAmB,SAAS,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,SAAS,GAAG,QAAQ,KAAK,EAAE,QAAQ,MAAM;oBACpG,IAAI,oBAAoB,CAAC,iBAAiB,QAAQ,IAAI,CAAA,GAAA,gLAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,GAAG;wBACjF,QAAQ,SAAS,CAAC,MAAM,CAAC;oBAC7B;oBACA,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,OAAO;gBAChD;YACJ;QACJ,OAAO;YACH,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,UAAU,IAAI,CAAC;YACpE,IAAI,CAAC,YAAY;gBACb,oBAAoB,KAAK,WAAW,CAAC;YACzC,OAAO;gBACH,IAAI,CAAC,KAAK,eAAe,CAAC,WAAW,EAAE;oBACnC,KAAK,eAAe,CAAC,WAAW,GAAG,KAAK,WAAW,CAAC;gBACxD;gBACA,oBAAoB,KAAK,eAAe,CAAC,WAAW;YACxD;QACJ;QACA,OAAO;IACX;IACA,eAAe,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,8BAA8B,EAAE,MAAM,EAAE;QACjF,MAAM,oBAAoB,IAAI,CAAC,gBAAgB,CAAC,UAAU;QAC1D,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,MAAM,YAAY,WAAW,QAAQ,OAAO;QAC5C,MAAM,mBAAmB,IAAI,oLAAA,CAAA,WAAQ;QACrC,MAAM,kBAAkB;YACpB,WAAW;YACX,OAAO;YACP,UAAU;YACV,YAAY;gBACR,IAAI,IAAI,CAAC,UAAU,IAAI;oBACnB,iBAAiB,MAAM;gBAC3B,OAAO;oBACH,iBAAiB,OAAO;gBAC5B;YACJ;QACJ;QACA,IAAI,mBAAmB;YACnB,QAAQ,SAAS,GAAG,IAAI,CAAC,SAAS;YAClC,MAAM,cAAc,UAAU,CAAC,OAAO,WAAW,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,UAAU,OAAO,WAAW,IAAI,CAAC,OAAO,OAAO,IAAI,OAAO,gBAAgB,KAAK,aAAa,aAAa,QAAQ,OAAO,CAAC;gBAC5M;YAAd,MAAM,QAAQ,CAAA,uBAAA,QAAQ,WAAW,cAAnB,kCAAA,uBAAuB;YACrC,IAAI,CAAC,kBAAkB,8BAA8B,IAAI,8BAA8B,KAAK,CAAC,OAAO;gBAChG,kBAAkB,MAAM,CAAC;YAC7B,OAAO;gBACH,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC;oBACxB,UAAU;oBACV,SAAS;oBACT,OAAO;gBACX;YACJ;YACA,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC;YAC5B,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,WAAW,2KAAA,CAAA,cAAW,EAAE,cAAc,IAAI,CAAC,MAAM;QACrE,OAAO;YACH,iBAAiB,MAAM;QAC3B;QACA,OAAO,iBAAiB,OAAO,GAAG,MAAM,CAAE;YACtC,IAAI,CAAC,kBAAkB,CAAC,MAAM,CAAC;QACnC;IACJ;IACA,WAAW,YAAY,EAAE;QACrB,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,cAAc,QAAQ,CAAC,SAAS,GAAG,CAAC,cAAc,GAAG,CAAC;IACnE;IACA,aAAa,aAAa,EAAE;QACxB,IAAI;QACJ,MAAM,iBAAiB,CAAC,CAAC,IAAI,CAAC,MAAM,GAAG,WAAW;QAClD,OAAO,kBAAkB,CAAC,CAAC,CAAC,SAAS,CAAC,mBAAmB,IAAI,CAAC,UAAU,CAAC,cAAc,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,MAAM,CAAC,WAAW,MAAM;IACxK;IACA,eAAe,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE;QACvC,MAAM,gBAAgB,eAAe,UAAU,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,aAAa,IAAI;QAC1F,MAAM,cAAc,IAAI,CAAC,YAAY,CAAC;QACtC,IAAI,aAAa;YACb,MAAM,SAAS,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,QAAQ,CAAC,KAAK,IAAI,CAAC;YAC/C,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB;YACrC,OAAO,OAAO,MAAM,CAAC;QACzB;QACA,OAAO;IACX;IACA,WAAW,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE;QACrC,iBAAiB,kBAAkB;QACnC,eAAe,MAAM,CAAC;YAClB,SAAS;YACT,WAAW;QACf;IACJ;IACA,cAAc;QACV,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI,cAAc,GAAG;YACjB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,QAAQ,CAAC;gBACV,MAAM;YACV;QACJ;IACJ;IACA,YAAY,CAAC,EAAE;QACX,MAAM,QAAQ,IAAI,CAAC,OAAO,GAAG,MAAM;QACnC,IAAI,CAAC,SAAS,MAAM,MAAM,GAAG,MAAM,EAAE;YACjC,IAAI,CAAC,sBAAsB,CAAC;QAChC;QACA,OAAO,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO;IACjC;IACA,aAAa,OAAO,EAAE;QAClB,UAAU,WAAW,CAAC;QACtB,QAAQ,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;QAC3D,MAAM,aAAa,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,UAAU;QAC9D,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC,QAAQ,OAAO,EAAE,aAAa,cAAc,cAAc,cAAc,aAAa;QACtH,IAAI,CAAC,WAAW,CAAC,QAAQ;QACzB,OAAO;IACX;IACA,YAAY,MAAM,EAAE,OAAO,EAAE;QACzB,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,QAAQ,CAAC,QAAQ,MAAM;QACzC,MAAM,gBAAgB,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,aAAa,IAAI,EAAE;QAC1E,MAAM,cAAc,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,WAAW,IAAI,EAAE;QACtE,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;YAClC,KAAK,UAAU,CAAC,QAAQ,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;gBAC3B,KAAK,IAAI,CAAC,EAAE;gBACZ,eAAe,aAAa,CAAC,EAAE;gBAC/B,YAAY,WAAW,CAAC,EAAE;YAC9B,GAAG;QACP;IACJ;IACA,WAAW,MAAM,EAAE,OAAO,EAAE;QACxB,IAAI,CAAC,QAAQ,aAAa,EAAE;YACxB,QAAQ,GAAG,CAAC,KAAK,GAAG,EAAE;QAC1B;QACA,MAAM,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,GAAG;QACxC,MAAM,cAAc,IAAI,CAAC,cAAc,CAAC,QAAQ;QAChD,IAAI,aAAa,QAAQ,UAAU,EAAE;YACjC,IAAI,CAAC,YAAY,CAAC,MAAM;QAC5B;QACA,IAAI,CAAC,UAAU,CAAC,QAAQ;QACxB,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;YACtB,SAAS,QAAQ,OAAO;QAC5B,GAAG,QAAQ,GAAG;QACd,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,GAAG;QAC5C,IAAI,CAAC,YAAY,CAAC,aAAa,YAAY,QAAQ,GAAG;IAC1D;IACA,gBAAgB,WAAW,EAAE,aAAa,EAAE;QACxC,OAAO,CAAC,iBAAiB,cAAc,OAAO,CAAC,gBAAgB;IACnE;IACA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,MAAM,OAAO,IAAI;QACjB,IAAI,cAAc;QAClB,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;QACJ,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,QAAQ,aAAa,GAAG;gBAChD,KAAK,WAAW,CAAC,MAAM,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;oBAC1B,QAAQ,OAAO,CAAC,EAAE;oBAClB,aAAa;oBACb,OAAO,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,YAAY;oBAC5C,UAAU,IAAI,SAAS,IAAI,IAAI,SAAS,CAAC,YAAY;gBACzD,GAAG;YACP;YACA,IAAI,OAAO,CAAC,EAAE,CAAC,OAAO,GAAG,GAAG;gBACxB,eAAe,OAAO,CAAC,EAAE,CAAC,OAAO;YACrC,OAAO;gBACH;YACJ;QACJ;IACJ;IACA,aAAa,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,OAAO,EAAE;QAC9D,IAAI;QACJ,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,YAAY,QAAQ;QACnC,MAAM,YAAY,eAAe,QAAQ;QACzC,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC;QACrC,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC;QAC9C,IAAI,SAAS,WAAW,KAAK,MAAM,WAAW,SAAS,CAAC,gBAAgB,QAAQ,IAAI,KAAK,KAAK,MAAM,iBAAiB,cAAc,WAAW,EAAE;YAC5I,OAAO,IAAI,CAAE;gBACT,KAAK,OAAO,CAAC,YAAY,QAAQ,UAAU,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,IAAI;YACvD;QACJ;QACA,cAAc,OAAO,CAAE,CAAC,aAAa;YACjC,MAAM,QAAQ,OAAO,EAAE,CAAC;YACxB,MAAM,WAAW,UAAU,EAAE,CAAC;YAC9B,MAAM,WAAW,CAAC;YAClB,IAAI,oBAAoB,CAAC,SAAS,QAAQ,CAAC,sBAAsB;gBAC7D,SAAS,QAAQ,CAAC;YACtB;QACJ;QACA,eAAe,YAAY,GAAG,CAAC,IAAI,eAAe,GAAG,CAAC;IAC1D;IACA,uBAAuB,KAAK,EAAE,WAAW,EAAE,OAAO,EAAE;QAChD,IAAI;QACJ,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,MAAM,iBAAiB,gBAAgB,YAAY,OAAO;QAC1D,MAAM,aAAa,YAAY,YAAY,OAAO;QAClD,MAAM,iBAAiB,SAAS,OAAO,KAAK,MAAM,OAAO,SAAS,CAAC,YAAY,IAAI,IAAI,KAAK,KAAK,MAAM,YAAY,KAAK,IAAI,UAAU,WAAW;QACjJ,IAAI,gBAAgB;YAChB;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,QAAQ,YAAY;QACjC,IAAI,gBAAgB;YAChB,IAAI,CAAC,OAAO,CAAC,YAAY,IAAI,UAAU,EAAE;QAC7C;QACA,MAAM,oBAAoB,IAAI,CAAC,kBAAkB,CAAC,oBAAoB;QACtE,MAAM,eAAe,aAAa,YAAY,WAAW,GAAG,IAAI,YAAY,WAAW,GAAG,oBAAoB;QAC9G,IAAI,CAAC,OAAO,CAAC,YAAY,cAAc;IAC3C;IACA,YAAY,IAAI,EAAE,OAAO,EAAE;QACvB,MAAM,cAAc,IAAI,CAAC,eAAe,CAAC;QACzC,IAAI,QAAQ,aAAa,EAAE;YACvB,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE;gBACnB,MAAM,YAAY,QAAQ,GAAG,CAAC,KAAK,CAAC,SAAS,CAAE,CAAA,OAAQ,KAAK,WAAW,KAAK,YAAY,WAAW;gBACnG,QAAQ,GAAG,CAAC,KAAK,CAAC,UAAU,GAAG;YACnC;QACJ,OAAO;YACH,QAAQ,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B;QACA,MAAM,QAAQ,IAAI,CAAC,WAAW,CAAC;QAC/B,IAAI,CAAC,sBAAsB,CAAC,OAAO,aAAa;QAChD,IAAI,CAAC,kBAAkB,CAAC,OAAO,aAAa;QAC5C,KAAK,GAAG,CAAC,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC;QAClC,OAAO;IACX;IACA,mBAAmB,KAAK,EAAE,OAAO,EAAE,aAAa,EAAE;QAC9C,MAAM,WAAW,IAAI,CAAC,gBAAgB,CAAC;QACvC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,CAAC,YAAY,IAAI,CAAC,cAAc,CAAC,OAAO,UAAU,SAAS,KAAK,GAAG,cAAc,MAAM,GAAG,IAAI,CAAE;YACjG,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5B;IACJ;IACA,iBAAiB,OAAO,EAAE,CAAC;IAC3B,SAAS,MAAM,EAAE;QACb,OAAO,EAAE;IACb;IACA,gBAAgB,OAAO,EAAE;QACrB,MAAM,cAAc;YAChB,QAAQ,QAAQ,MAAM;YACtB,aAAa,QAAQ,WAAW;YAChC,SAAS,QAAQ,GAAG,CAAC,OAAO;YAC5B,UAAU,QAAQ,GAAG,CAAC,QAAQ;YAC9B,UAAU,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG;QACxC;QACA,IAAI,CAAC,eAAe,CAAC;QACrB,OAAO;IACX;IACA,gBAAgB,OAAO,EAAE,MAAM,EAAE;QAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,uBAAuB;YACpC;QACJ;QACA,MAAM,WAAW,EAAE;QACnB,SAAS,UAAU;QACnB,OAAO,KAAK,GAAG,OAAO,KAAK,IAAI,SAAS,MAAM,EAAE,eAAe,EAAE,aAAa;YAC1E,IAAI,WAAW,OAAO,OAAO,IAAI;YACjC,MAAM,UAAU,SAAS,GAAG;gBACxB,IAAI,OAAO,eAAe;oBACtB,cAAc;gBAClB;gBACA,MAAM,WAAW,OAAO,OAAO,IAAI;gBACnC,IAAI,KAAK,SAAS,CAAC,cAAc,KAAK,SAAS,CAAC,WAAW;oBACvD,IAAI,KAAK;wBACL,gBAAgB;oBACpB;oBACA,WAAW;gBACf;YACJ;YACA,SAAS,IAAI,CAAC;YACd,OAAO;gBACH,MAAM,QAAQ,SAAS,OAAO,CAAC;gBAC/B,IAAI,SAAS,GAAG;oBACZ,SAAS,MAAM,CAAC,OAAO;gBAC3B;YACJ;QACJ;QACA,OAAO,MAAM,GAAG,OAAO,MAAM,IAAI,SAAS,GAAG,EAAE,OAAO;YAClD,IAAI,KAAK;gBACL,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,GAAG,IAAI,IAAI;gBACnC,IAAI,CAAC,QAAQ,GAAG,QAAQ,QAAQ,GAAG,IAAI,QAAQ;gBAC/C,IAAI,CAAC,SAAS,GAAG,QAAQ,SAAS,GAAG,IAAI,SAAS;gBAClD,IAAI,CAAC,UAAU,GAAG,QAAQ,UAAU,GAAG,IAAI,UAAU;gBACrD,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS;oBACzB,QAAQ,GAAG,GAAG;gBAClB;YACJ;YACA,SAAS,OAAO,CAAE,CAAA;gBACd,QAAQ;YACZ;QACJ;QACA,IAAI,WAAW,SAAS;YACpB,QAAQ,KAAK,GAAG,OAAO,KAAK,CAAC,IAAI,CAAC;QACtC;QACA,OAAO;IACX;IACA,cAAc,IAAI,EAAE,OAAO,EAAE;QACzB,QAAQ,WAAW,GAAG,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACzC,IAAI,CAAC,aAAa,CAAC,kBAAkB;IACzC;IACA,aAAa,IAAI,EAAE,OAAO,EAAE,GAAG,EAAE;QAC7B,CAAA,GAAA,+KAAA,CAAA,OAAW,AAAD,EAAE,KAAK,GAAG,CAAC,IAAI,WAAW;QACpC,QAAQ,UAAU,GAAG,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;QACtC,IAAI,CAAC,aAAa,CAAC,iBAAiB;IACxC;IACA,qBAAqB,CAAC,EAAE;QACpB,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG;QACJ,IAAI,sLAAA,CAAA,UAAa,CAAC,YAAY,CAAC,aAAa;YAAC;YAAS;SAAe,GAAG;YACpE,MAAM,iBAAiB,IAAI,CAAC,kBAAkB,CAAC,iBAAiB;YAChE,MAAM,SAAS,eAAe,GAAG,CAAE,CAAA,SAAU,OAAO,YAAY,IAAI,OAAO,KAAK;YAChF,IAAI,CAAC,eAAe,CAAC;gBACjB,QAAQ;gBACR,aAAa;YACjB;YACA;QACJ;QACA,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE;YACrB,IAAI,CAAC,MAAM;QACf;IACJ;IACA,aAAa,KAAK,EAAE,QAAQ,EAAE;QAC1B,MAAM,YAAY,MAAM,MAAM,GAAG,KAAK,CAAC,EAAE,CAAC,SAAS,GAAG,CAAC;QACvD,OAAO;IACX;IACA,mBAAmB;QACf,OAAO,IAAI,CAAC,aAAa,IAAI,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD;IACjC;IACA,gBAAgB,qBAAqB,EAAE;QACnC,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,gBAAgB,YAAY,EAAE,qBAAqB,EAAE;QACjD,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,kBAAkB,CAAC,EAAE,CAAC;IACtB,mBAAmB,CAAC,EAAE,CAAC;IACvB,gBAAgB;QACZ,OAAO;YAAC;SAAgB;IAC5B;IACA,4BAA4B;QACxB,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,IAAI,cAAc,GAAG;YACjB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,QAAQ,CAAC;gBACV,MAAM;YACV;QACJ;IACJ;IACA,SAAS,GAAG,EAAE;QACV,MAAM,WAAW,IAAI,CAAC,OAAO;QAC7B,MAAM,mBAAmB,YAAY,SAAS,QAAQ,CAAC,AAAC,IAA4C,OAAzC,IAAI,CAAC,eAAe,CAAC,sBAAuB,GAAG,CAAC,AAAC,IAAyC,OAAtC,IAAI,CAAC,eAAe,CAAC;QACpI,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW,KAAK,IAAI,IAAI,EAAE;YACxE,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI;YAC3B,oBAAoB,iBAAiB,UAAU,CAAC,IAAI,IAAI;QAC5D;IACJ;IACA,WAAW,qBAAqB,EAAE;QAC9B,IAAI;QACJ,OAAO,SAAS,CAAC,sBAAsB,IAAI,CAAC,aAAa,KAAK,KAAK,MAAM,sBAAsB,KAAK,IAAI,oBAAoB,MAAM;IACtI;IACA,eAAe,qBAAqB,EAAE;QAClC,MAAM,mBAAmB,IAAI,CAAC,UAAU,CAAC;QACzC,IAAI,SAAS,oBAAoB,KAAK,MAAM,oBAAoB,iBAAiB,MAAM,EAAE;YACrF,iBAAiB,MAAM;QAC3B;IACJ;IACA,aAAa,CAAC,EAAE;QACZ,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,EAAE,MAAM,EAAE,UAAU;QACzC,IAAI,eAAe,IAAI,CAAC,WAAW,EAAE;YACjC,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,MAAM;YACV,GAAG,IAAI,CAAC,IAAI;QAChB;IACJ;IACA,4BAA4B,MAAM,EAAE,qBAAqB,EAAE;QACvD,MAAM,mBAAmB,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QAC3B,MAAM,YAAY,IAAI,CAAC,MAAM,CAAC;QAC9B,IAAI,UAAU,aAAa,WAAW,aAAa,CAAC,mMAAA,CAAA,UAAY,CAAC,eAAe,EAAE;YAC9E,iBAAiB,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;QACnD;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,kBAAkB,UAAU,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI;QACvE,iBAAiB,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,qBAAqB,MAAM,CAAC,QAAQ,QAAQ,CAAC,IAAI,CAAC,OAAO;QAClJ,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB;QACrC,OAAO;IACX;IACA,yBAAyB;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC,oCAAoC,UAAU,IAAI,CAAC,MAAM,CAAC;IACjF;IACA,qBAAqB;QACjB,IAAI,eAAe,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QACpF,MAAM,SAAS,IAAI,oLAAA,CAAA,WAAQ;QAC3B,MAAM,yBAAyB,gBAAgB,IAAI,CAAC,sBAAsB;QAC1E,IAAI,CAAC,0BAA0B,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,kBAAkB,GAAG;YAChE,OAAO,OAAO,OAAO;QACzB;QACA,MAAM,yBAAyB,IAAM,oLAAA,CAAA,OAAI,CAAC,KAAK,CAAC,IAAI,EAAE,MAAM,IAAI,CAAC,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAE;gBAC7F,IAAI,IAAI,CAAC,UAAU,IAAI;oBACnB,OAAO,MAAM;gBACjB,OAAO,IAAI,IAAI,CAAC,kBAAkB,CAAC,IAAI,GAAG,GAAG;oBACzC;gBACJ,OAAO;oBACH,OAAO,OAAO;gBAClB;YACJ,GAAI,IAAI,CAAC,OAAO,MAAM;QACtB;QACA,OAAO,OAAO,OAAO;IACzB;IACA,eAAe,gBAAgB,EAAE,MAAM,EAAE,qBAAqB,EAAE;QAC5D,OAAO,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAE;YACnC,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,eAAe,CAAC,kBAAkB;YACvC,IAAI,CAAC,2BAA2B,CAAC,kBAAkB;QACvD;IACJ;IACA,oBAAoB,qBAAqB,EAAE,CAAC;IAC5C,WAAW,aAAa,EAAE;QACtB,IAAI,CAAC,eAAe;YAChB,OAAO,EAAE;QACb;QACA,MAAM,SAAS,EAAE;QACjB,MAAM,eAAe,cAAc,OAAO;QAC1C,aAAa,OAAO,CAAE,CAAA;YAClB,IAAI,QAAQ,KAAK,WAAW;YAC5B,IAAI,KAAK,qBAAqB,EAAE;gBAC5B,MAAM,OAAO,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE;gBAC7B,IAAI,KAAK,KAAK,GAAG,KAAK,WAAW,GAAG,GAAG;oBACnC,QAAQ,KAAK,KAAK;gBACtB;YACJ;YACA,OAAO,IAAI,CAAC;QAChB;QACA,OAAO;IACX;IACA,gBAAgB,aAAa,EAAE,QAAQ,EAAE;QACrC,CAAC,IAAI,CAAC,MAAM,CAAC,yBAAyB,kLAAA,CAAA,OAAI;QAC1C,gBAAgB,0BAAA,2BAAA,gBAAiB,IAAI,CAAC,eAAe;QACrD,IAAI,eAAe;YACf,MAAM,QAAQ,cAAc,QAAQ,CAAC,yBAAyB,QAAQ;YACtE,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,MAAM,OAAO,MAAM,EAAE,CAAC;gBACtB,MAAM,aAAa,KAAK,QAAQ,CAAC;gBACjC,MAAM,cAAc,KAAK,QAAQ,CAAC;gBAClC,MAAM,aAAa,KAAK,QAAQ,CAAC;gBACjC,MAAM,eAAe,WAAW,KAAK,GAAG,CAAC,GAAG,KAAK,CAAC,OAAO,IAAI,CAAC,KAAK,QAAQ,CAAC;gBAC5E,MAAM,gBAAgB,CAAC,cAAc,CAAC,eAAe,CAAC;gBACtD,IAAI,gBAAgB,eAAe;oBAC/B,MAAM,SAAS,KAAK,QAAQ,CAAC;oBAC7B,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC;oBAC/B,OAAO;gBACX;YACJ;QACJ;QACA,OAAO,EAAE;IACb;IACA,sBAAsB,WAAW,EAAE,QAAQ,EAAE;QACzC,OAAO;IACX;IACA,sBAAsB,UAAU,EAAE,IAAI,EAAE,gBAAgB,EAAE;QACtD,MAAM,QAAQ,KAAK,QAAQ,CAAC,kBAAkB,KAAK,IAAI,CAAC,AAAC,qBAAuC,OAAnB,mBAAiB,GAAE,6BAA2B,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;QAC9I,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,EAAG;YACtC,MAAM,OAAO,MAAM,GAAG,CAAC;YACvB,OAAO,MAAM,CAAC,KAAK,KAAK,EAAE;QAC9B;IACJ;IACA,kBAAkB,UAAU,EAAE,WAAW,EAAE,QAAQ,EAAE;QACjD,MAAM,gBAAgB,IAAI,CAAC,eAAe;QAC1C,IAAI,CAAC,CAAC,SAAS,iBAAiB,KAAK,MAAM,iBAAiB,cAAc,MAAM,GAAG;YAC/E;QACJ;QACA,MAAM,QAAQ,cAAc,QAAQ,GAAG,QAAQ,CAAC,WAAW,GAAG,CAAC,AAAC,IAAoB,OAAjB;QACnE,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YACrB,IAAI,CAAC,qBAAqB,CAAC,YAAY,MAAM,EAAE,CAAC,WAAW;QAC/D,OAAO;YACH,IAAK,IAAI,WAAW,GAAG,WAAW,MAAM,MAAM,EAAE,WAAY;gBACxD,MAAM,eAAe,IAAI,CAAC,qBAAqB,CAAC,aAAa;gBAC7D,IAAI,gBAAgB,GAAG;oBACnB,IAAI,CAAC,qBAAqB,CAAC,YAAY,MAAM,EAAE,CAAC,WAAW;gBAC/D;YACJ;QACJ;IACJ;IACA,gBAAgB,KAAK,EAAE;QACnB,IAAI,EACA,QAAQ,MAAM,EACd,aAAa,WAAW,EAC3B,GAAG;QACJ,MAAM,gBAAgB,IAAI,CAAC,eAAe;QAC1C,IAAI,CAAC,CAAC,SAAS,iBAAiB,KAAK,MAAM,iBAAiB,cAAc,MAAM,KAAK,CAAC,QAAQ;YAC1F;QACJ;QACA,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,MAAM,sBAAsB,IAAI,CAAC,oBAAoB;QACrD,MAAM,QAAQ,cAAc,QAAQ,CAAC,YAAY,QAAQ,CAAC;QAC1D,MAAM,OAAO,GAAG,OAAO,CAAE,CAAA,MAAO,IAAI,eAAe,CAAC;QACpD,QAAQ,OAAO,CAAE,CAAC,QAAQ;YACtB,IAAI,uBAAuB,OAAO,KAAK,IAAI,CAAC,OAAO,OAAO,EAAE;gBACxD,MAAM,aAAa,CAAC;gBACpB,MAAM,QAAQ,cAAc,OAAO,YAAY,IAAI,OAAO,KAAK;gBAC/D,MAAM,WAAW,cAAc,OAAO,QAAQ,IAAI;gBAClD,WAAW,KAAK,GAAG,WAAW,OAAO,KAAK,GAAG,KAAK;gBAClD,WAAW,QAAQ,GAAG,WAAW,KAAK;gBACtC,WAAW,QAAQ,GAAG;gBACtB,IAAI,CAAC,iBAAiB,CAAC,YAAY;YACvC;YACA,MAAM,WAAW,eAAe,MAAM,CAAC,YAAY;YACnD,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW;gBACrB,CAAA,GAAA,iLAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,EAAE,CAAC,cAAc;YACpC;QACJ;IACJ;IACA,gBAAgB,QAAQ,EAAE;QACtB,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC;IACA,qBAAqB,QAAQ,EAAE;QAC3B,IAAI,WAAW,GAAG;YACd;QACJ;QACA,MAAM,OAAO,IAAI,CAAC,eAAe,GAAG,EAAE,CAAC;QACvC,OAAO,KAAK,QAAQ;IACxB;IACA,gBAAgB,QAAQ,EAAE,gBAAgB,EAAE;QACxC,MAAM,SAAS,IAAI,CAAC,eAAe,CAAC;QACpC,MAAM,qBAAqB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,UAAU;QACzE,IAAI,CAAC,CAAC,SAAS,UAAU,KAAK,MAAM,UAAU,OAAO,MAAM,KAAK,qBAAqB,GAAG;YACpF;QACJ;QACA,MAAM,QAAQ,OAAO,EAAE,CAAC;QACxB,OAAO,MAAM,MAAM,GAAG,IAAI,QAAQ,KAAK;IAC3C;IACA,eAAe,QAAQ,EAAE;QACrB,MAAM,OAAO,IAAI;QACjB,IAAI,cAAc,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD;QAClB,MAAM,iBAAiB,KAAK,gBAAgB;QAC5C,oLAAA,CAAA,OAAkB,CAAC,gBAAiB,CAAC,GAAG;YACpC,cAAc,YAAY,GAAG,CAAC,KAAK,eAAe,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,eAAe,EAAE,CAAC;QAC3E;QACA,IAAI,YAAY,MAAM,EAAE;YACpB,OAAO;QACX;QACA;IACJ;IACA,eAAe,QAAQ,EAAE,gBAAgB,EAAE;QACvC,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,UAAU;QAC7C,IAAI,OAAO;YACP,OAAO,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE;QAC5B;QACA;IACJ;IACA,cAAc,QAAQ,EAAE;QACpB,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC;QAClC,IAAI,WAAW,EAAE;QACjB,IAAI,SAAS,CAAC,CAAA,GAAA,0KAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,GAAG,EAAE;YACvC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;gBACnC,SAAS,IAAI,CAAC,KAAK,CAAC,EAAE;YAC1B;QACJ,OAAO;YACH,WAAW;QACf;QACA,OAAO;IACX;IACA,uBAAuB,MAAM,EAAE,QAAQ,EAAE,gBAAgB,EAAE;QACvD,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB;YAC5B,MAAM,cAAc,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC,kBAAkB;YAC3E,OAAO,IAAI,CAAC,kBAAkB,CAAC,eAAe,CAAC;QACnD;QACA,OAAO;IACX;IACA,oBAAoB,CAAC;IACrB,WAAW,QAAQ,EAAE,aAAa,EAAE;QAChC,OAAO,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,CAAC;IACrD;IACA,QAAQ,YAAY,EAAE,IAAI,EAAE,KAAK,EAAE;QAC/B,MAAM,QAAQ,QAAQ,IAAI,CAAC,eAAe;QAC1C,IAAI;QACJ,IAAI,MAAM,MAAM,GAAG,KAAK,aAAa,QAAQ,IAAI,GAAG;YAChD,IAAI;YACJ,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,qBAAqB,cAAc,IAAI,CAAC,MAAM,CAAC,+BAA+B;gBACxG,aAAa,QAAQ,GAAG,aAAa,QAAQ,GAAG,MAAM,MAAM,GAAG,aAAa,QAAQ,GAAG,MAAM,MAAM,GAAG;YAC1G;YACA,SAAS,SAAS,IAAI,CAAC,eAAe,CAAC,aAAa,QAAQ;YAC5D,IAAI,CAAC,SAAS,CAAC,UAAU,MAAM,KAAK,KAAK,MAAM,UAAU,KAAK,IAAI,QAAQ,MAAM,IAAI,GAAG;gBACnF,OAAO,OAAO,EAAE,CAAC,OAAO,MAAM,GAAG,aAAa,WAAW,GAAG,aAAa,WAAW,GAAG,OAAO,MAAM,GAAG;YAC3G;QACJ;IACJ;IACA,eAAe;QACX,MAAM,eAAe,IAAI,CAAC,eAAe;QACzC,IAAI,gBAAgB,MAAM,aAAa,MAAM,EAAE;YAC3C,OAAO,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM;QACtC;QACA,OAAO;IACX;IACA,oBAAoB,YAAY,EAAE;QAC9B,eAAe,gBAAgB,IAAI,CAAC,eAAe;QACnD,IAAI,cAAc;YACd,MAAM,iBAAiB,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,IAAI,CAAC,MAAM,CAAC;YAChE,MAAM,UAAU,kBAAkB,aAAa,IAAI,CAAC;YACpD,OAAO,WAAW,QAAQ,MAAM,GAAG,UAAU,aAAa,IAAI,CAAC;QACnE;QACA,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD;IACX;IACA,gBAAgB,YAAY,EAAE;QAC1B,OAAO,IAAI,CAAC,mBAAmB,CAAC;IACpC;IACA,YAAY,IAAI,EAAE;QACd,OAAO,IAAI,CAAC,eAAe,GAAG,KAAK,CAAC;IACxC;IACA,kBAAkB,CAAC;IACnB,UAAU,CAAC;IACX,mBAAmB,KAAK,EAAE;QACtB,MAAM,WAAW,IAAI,CAAC,OAAO;QAC7B,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,YAAY,SAAS,GAAG,CAAC;YACrB,aAAa,aAAa,QAAQ;YAClC,cAAc,CAAC,aAAa,QAAQ;QACxC;IACJ;IACA,mBAAmB,YAAY,EAAE;QAC7B,MAAM,WAAW,IAAI,CAAC,OAAO;QAC7B,MAAM,gBAAgB,IAAI,CAAC,aAAa;QACxC,IAAI,YAAY,eAAe;YAC3B,OAAO,eAAe,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,IAAI,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE,iBAAiB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY;QACxI;QACA,OAAO;IACX;IACA,aAAa;QACT,IAAI;QACJ,OAAO,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,SAAS;IACzH;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1211, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/views/m_rows_view.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/views/m_rows_view.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport eventsEngine from \"../../../../common/core/events/core/events_engine\";\r\nimport {\r\n    removeEvent\r\n} from \"../../../../common/core/events/remove\";\r\nimport messageLocalization from \"../../../../common/core/localization/message\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport browser from \"../../../../core/utils/browser\";\r\nimport {\r\n    deferRender,\r\n    deferUpdate\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    compileGetter\r\n} from \"../../../../core/utils/data\";\r\nimport {\r\n    extend\r\n} from \"../../../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../../../core/utils/iterator\";\r\nimport {\r\n    getBoundingRect,\r\n    getDefaultAlignment\r\n} from \"../../../../core/utils/position\";\r\nimport {\r\n    getHeight,\r\n    getOuterHeight,\r\n    getWidth\r\n} from \"../../../../core/utils/size\";\r\nimport {\r\n    isEmpty\r\n} from \"../../../../core/utils/string\";\r\nimport {\r\n    setHeight\r\n} from \"../../../../core/utils/style\";\r\nimport {\r\n    isDefined,\r\n    isNumeric,\r\n    isString\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    getWindow,\r\n    hasWindow\r\n} from \"../../../../core/utils/window\";\r\nimport Scrollable from \"../../../../ui/scroll_view/ui.scrollable\";\r\nimport {\r\n    CLASSES as REORDERING_CLASSES\r\n} from \"../columns_resizing_reordering/const\";\r\nimport gridCoreUtils from \"../m_utils\";\r\nimport {\r\n    CLASSES\r\n} from \"../sticky_columns/const\";\r\nimport {\r\n    ColumnsView\r\n} from \"./m_columns_view\";\r\nconst ROWS_VIEW_CLASS = \"rowsview\";\r\nconst CONTENT_CLASS = \"content\";\r\nconst NOWRAP_CLASS = \"nowrap\";\r\nconst GROUP_ROW_CLASS = \"dx-group-row\";\r\nconst GROUP_CELL_CLASS = \"dx-group-cell\";\r\nconst DATA_ROW_CLASS = \"dx-data-row\";\r\nconst FREE_SPACE_CLASS = \"dx-freespace-row\";\r\nconst COLUMN_LINES_CLASS = \"dx-column-lines\";\r\nconst ROW_ALTERNATION_CLASS = \"dx-row-alt\";\r\nconst LAST_ROW_BORDER = \"dx-last-row-border\";\r\nconst EMPTY_CLASS = \"dx-empty\";\r\nconst ROW_INSERTED_ANIMATION_CLASS = \"row-inserted-animation\";\r\nconst CONTENT_FIXED_CLASS = \"content-fixed\";\r\nexport const ROW_LINES_CLASS = \"dx-row-lines\";\r\nconst LOADPANEL_HIDE_TIMEOUT = 200;\r\n\r\nfunction getMaxHorizontalScrollOffset(scrollable) {\r\n    return scrollable ? Math.round(scrollable.scrollWidth() - scrollable.clientWidth()) : 0\r\n}\r\nexport function isGroupRow(_ref) {\r\n    let {\r\n        rowType: rowType,\r\n        column: column\r\n    } = _ref;\r\n    return \"group\" === rowType && isDefined(column.groupIndex) && !column.showWhenGrouped && !column.command\r\n}\r\n\r\nfunction setWatcher(_ref2) {\r\n    let {\r\n        element: element,\r\n        watch: watch,\r\n        getter: getter,\r\n        callBack: callBack\r\n    } = _ref2;\r\n    if (watch) {\r\n        const dispose = watch(getter, callBack);\r\n        eventsEngine.on(element, removeEvent, dispose)\r\n    }\r\n}\r\nconst defaultCellTemplate = function($container, options) {\r\n    const isDataTextEmpty = isEmpty(options.text) && \"data\" === options.rowType;\r\n    const {\r\n        text: text\r\n    } = options;\r\n    const container = $container.get(0);\r\n    if (isDataTextEmpty) {\r\n        gridCoreUtils.setEmptyText($container)\r\n    } else if (options.column.encodeHtml) {\r\n        container.textContent = text\r\n    } else {\r\n        container.innerHTML = text\r\n    }\r\n};\r\nconst getScrollableBottomPadding = function(that) {\r\n    const scrollable = that.getScrollable();\r\n    return scrollable ? Math.ceil(parseFloat($(scrollable.content()).css(\"paddingBottom\"))) : 0\r\n};\r\nexport class RowsView extends ColumnsView {\r\n    init() {\r\n        super.init();\r\n        this._editingController = this.getController(\"editing\");\r\n        this._resizingController = this.getController(\"resizing\");\r\n        this._columnsResizerController = this.getController(\"columnsResizer\");\r\n        this._focusController = this.getController(\"focus\");\r\n        this._keyboardNavigationController = this.getController(\"keyboardNavigation\");\r\n        this._validatingController = this.getController(\"validating\");\r\n        this._errorHandlingController = this.getController(\"errorHandling\");\r\n        this._columnHeadersView = this.getView(\"columnHeadersView\");\r\n        this._rowHeight = 0;\r\n        this._scrollTop = 0;\r\n        this._scrollLeft = -1;\r\n        this._scrollRight = 0;\r\n        this._hasHeight = void 0;\r\n        this._contentChanges = [];\r\n        this._dataController.loadingChanged.add(((isLoading, messageText) => {\r\n            this.setLoading(isLoading, messageText)\r\n        }));\r\n        this._dataController.dataSourceChanged.add((() => {\r\n            if (this._scrollLeft >= 0 && !this._dataController.isLoading()) {\r\n                this._handleScroll({\r\n                    component: this.getScrollable(),\r\n                    forceUpdateScrollPosition: true,\r\n                    scrollOffset: {\r\n                        top: this._scrollTop,\r\n                        left: this._scrollLeft\r\n                    }\r\n                })\r\n            }\r\n        }))\r\n    }\r\n    _getDefaultTemplate(column) {\r\n        if (\"empty\" === column.command) {\r\n            return function(container) {\r\n                container.html(\"&nbsp;\")\r\n            }\r\n        } else {\r\n            return defaultCellTemplate\r\n        }\r\n    }\r\n    renderFocusState(params) {}\r\n    _getDefaultGroupTemplate(column) {\r\n        const summaryTexts = this.option(\"summary.texts\");\r\n        return function($container, options) {\r\n            const {\r\n                data: data\r\n            } = options;\r\n            let text = `${options.column.caption}: ${options.text}`;\r\n            const container = $container.get(0);\r\n            if (options.summaryItems && options.summaryItems.length) {\r\n                text += ` ${gridCoreUtils.getGroupRowSummaryText(options.summaryItems,summaryTexts)}`\r\n            }\r\n            if (data) {\r\n                if (options.groupContinuedMessage && options.groupContinuesMessage) {\r\n                    text += ` (${options.groupContinuedMessage}. ${options.groupContinuesMessage})`\r\n                } else if (options.groupContinuesMessage) {\r\n                    text += ` (${options.groupContinuesMessage})`\r\n                } else if (options.groupContinuedMessage) {\r\n                    text += ` (${options.groupContinuedMessage})`\r\n                }\r\n            }\r\n            if (column.encodeHtml) {\r\n                container.textContent = text\r\n            } else {\r\n                container.innerHTML = text\r\n            }\r\n        }\r\n    }\r\n    _update(change) {}\r\n    _updateCell($cell, options) {\r\n        if (isGroupRow(options)) {\r\n            const isGroupContainer = $cell.is(`.${this.addWidgetPrefix(CLASSES.groupRowContainer)}`);\r\n            const $groupCell = isGroupContainer ? $cell.parent() : $cell;\r\n            $groupCell.addClass(\"dx-group-cell\")\r\n        }\r\n        super._updateCell.apply(this, arguments)\r\n    }\r\n    _getCellTemplate(options) {\r\n        const that = this;\r\n        const {\r\n            column: column\r\n        } = options;\r\n        let template;\r\n        if (isGroupRow(options)) {\r\n            template = column.groupCellTemplate || {\r\n                allowRenderToDetachedContainer: true,\r\n                render: that._getDefaultGroupTemplate(column)\r\n            }\r\n        } else if ((\"data\" === options.rowType || column.command) && column.cellTemplate) {\r\n            template = column.cellTemplate\r\n        } else {\r\n            template = {\r\n                allowRenderToDetachedContainer: true,\r\n                render: that._getDefaultTemplate(column)\r\n            }\r\n        }\r\n        return template\r\n    }\r\n    _createRow(row, tag) {\r\n        const $row = super._createRow.apply(this, arguments);\r\n        if (row) {\r\n            const isGroup = \"group\" === row.rowType;\r\n            const isDataRow = \"data\" === row.rowType;\r\n            isDataRow && $row.addClass(\"dx-data-row\");\r\n            isDataRow && this.option(\"showRowLines\") && $row.addClass(\"dx-row-lines\");\r\n            this.option(\"showColumnLines\") && $row.addClass(\"dx-column-lines\");\r\n            if (false === row.visible) {\r\n                $row.hide()\r\n            }\r\n            if (isGroup) {\r\n                $row.addClass(\"dx-group-row\");\r\n                this.setAriaExpandedAttribute($row, row)\r\n            }\r\n        }\r\n        return $row\r\n    }\r\n    _rowPrepared($row, rowOptions, row) {\r\n        if (\"data\" === rowOptions.rowType) {\r\n            if (this.option(\"rowAlternationEnabled\")) {\r\n                this._isAltRow(row) && $row.addClass(\"dx-row-alt\");\r\n                setWatcher({\r\n                    element: $row.get(0),\r\n                    watch: rowOptions.watch,\r\n                    getter: () => this._isAltRow(row),\r\n                    callBack: value => {\r\n                        $row.toggleClass(\"dx-row-alt\", value)\r\n                    }\r\n                })\r\n            }\r\n            this._setAriaRowIndex(rowOptions, $row);\r\n            setWatcher({\r\n                element: $row.get(0),\r\n                watch: rowOptions.watch,\r\n                getter: () => rowOptions.rowIndex,\r\n                callBack: () => this._setAriaRowIndex(rowOptions, $row)\r\n            })\r\n        }\r\n        super._rowPrepared.apply(this, arguments)\r\n    }\r\n    _setAriaRowIndex(row, $row) {\r\n        if (!$row.is(\"tr\")) {\r\n            return\r\n        }\r\n        const {\r\n            component: component\r\n        } = this;\r\n        const isPagerMode = \"standard\" === component.option(\"scrolling.mode\") && !gridCoreUtils.isVirtualRowRendering(component);\r\n        let rowIndex = row.rowIndex + 1;\r\n        if (isPagerMode) {\r\n            rowIndex = component.pageIndex() * component.pageSize() + rowIndex\r\n        } else {\r\n            rowIndex += this._dataController.getRowIndexOffset()\r\n        }\r\n        this.setAria(\"rowindex\", rowIndex, $row)\r\n    }\r\n    setAriaExpandedAttribute($row, row) {\r\n        const description = row.isExpanded ? this.localize(\"dxDataGrid-ariaExpandedRow\") : this.localize(\"dxDataGrid-ariaCollapsedRow\");\r\n        this.setAria(\"roledescription\", description, $row)\r\n    }\r\n    _afterRowPrepared(e) {\r\n        const arg = e.args[0];\r\n        const dataController = this._dataController;\r\n        const row = dataController.getVisibleRows()[arg.rowIndex];\r\n        const watch = this.option(\"integrationOptions.watchMethod\");\r\n        if (!arg.data || \"data\" !== arg.rowType || arg.isNewRow || !this.option(\"twoWayBindingEnabled\") || !watch || !row) {\r\n            return\r\n        }\r\n        const dispose = watch((() => dataController.generateDataValues(arg.data, arg.columns)), (() => {\r\n            dataController.repaintRows([row.rowIndex], this.option(\"repaintChangesOnly\"))\r\n        }), {\r\n            deep: true,\r\n            skipImmediate: true\r\n        });\r\n        eventsEngine.on(arg.rowElement, removeEvent, dispose)\r\n    }\r\n    _renderScrollable(force) {\r\n        const that = this;\r\n        const $element = that.element();\r\n        if (!$element.children().length) {\r\n            $element.append(\"<div>\")\r\n        }\r\n        if (force || !that._loadPanel) {\r\n            that._renderLoadPanel($element, $element.parent(), that._dataController.isLocalStore())\r\n        }\r\n        if ((force || !that.getScrollable()) && that._dataController.isLoaded()) {\r\n            const columns = that.getColumns();\r\n            let allColumnsHasWidth = true;\r\n            for (let i = 0; i < columns.length; i++) {\r\n                if (!columns[i].width && !columns[i].minWidth) {\r\n                    allColumnsHasWidth = false;\r\n                    break\r\n                }\r\n            }\r\n            if (that.option(\"columnAutoWidth\") || that._hasHeight || allColumnsHasWidth || that._columnsController._isColumnFixing()) {\r\n                that._renderScrollableCore($element)\r\n            }\r\n        }\r\n    }\r\n    _handleScroll(e) {\r\n        const rtlEnabled = this.option(\"rtlEnabled\");\r\n        const isNativeScrolling = e.component.option(\"useNative\");\r\n        this._scrollTop = e.scrollOffset.top;\r\n        this._scrollLeft = e.scrollOffset.left;\r\n        let scrollLeft = e.scrollOffset.left;\r\n        if (rtlEnabled) {\r\n            this._scrollRight = getMaxHorizontalScrollOffset(e.component) - this._scrollLeft;\r\n            if (isNativeScrolling) {\r\n                scrollLeft = -this._scrollRight\r\n            }\r\n            if (!this.isScrollbarVisible(true)) {\r\n                this._scrollLeft = -1\r\n            }\r\n        }\r\n        this.scrollChanged.fire(_extends({}, e.scrollOffset, {\r\n            left: scrollLeft\r\n        }), this.name)\r\n    }\r\n    _renderScrollableCore($element) {\r\n        const dxScrollableOptions = this._createScrollableOptions();\r\n        const scrollHandler = this._handleScroll.bind(this);\r\n        dxScrollableOptions.onScroll = scrollHandler;\r\n        this._scrollable = this._createComponent($element, Scrollable, dxScrollableOptions);\r\n        this._scrollableContainer = this._scrollable && $(this._scrollable.container())\r\n    }\r\n    _renderLoadPanel() {\r\n        for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\r\n            args[_key] = arguments[_key]\r\n        }\r\n        return gridCoreUtils.renderLoadPanel.apply(this, arguments)\r\n    }\r\n    _renderContent(contentElement, tableElement, isFixedTableRendering) {\r\n        contentElement.empty().append(tableElement);\r\n        return this._findContentElement()\r\n    }\r\n    _updateContent(newTableElement, change, isFixedTableRendering) {\r\n        this._contentChanges.push({\r\n            newTableElement: newTableElement,\r\n            change: change,\r\n            isFixedTableRendering: isFixedTableRendering\r\n        });\r\n        return this.waitAsyncTemplates().done((() => {\r\n            const contentChanges = this._contentChanges;\r\n            this._contentChanges = [];\r\n            contentChanges.forEach((_ref3 => {\r\n                let {\r\n                    newTableElement: newTableElement,\r\n                    change: change,\r\n                    isFixedTableRendering: isFixedTableRendering\r\n                } = _ref3;\r\n                const tableElement = this.getTableElement(isFixedTableRendering);\r\n                const contentElement = this._findContentElement(isFixedTableRendering);\r\n                const changeType = null === change || void 0 === change ? void 0 : change.changeType;\r\n                const executors = [];\r\n                const highlightChanges = this.option(\"highlightChanges\");\r\n                const rowInsertedClass = this.addWidgetPrefix(\"row-inserted-animation\");\r\n                if (\"update\" === changeType) {\r\n                    each(change.rowIndices, ((index, rowIndex) => {\r\n                        var _change$changeTypes;\r\n                        const $newRowElement = this._getRowElements(newTableElement).eq(index);\r\n                        const dataChangeType = null === (_change$changeTypes = change.changeTypes) || void 0 === _change$changeTypes ? void 0 : _change$changeTypes[index];\r\n                        const item = change.items && change.items[index];\r\n                        executors.push((() => {\r\n                            const $rowElements = this._getRowElements(tableElement);\r\n                            const $rowElement = $rowElements.eq(rowIndex);\r\n                            switch (dataChangeType) {\r\n                                case \"update\":\r\n                                    if (item) {\r\n                                        var _change$columnIndices;\r\n                                        const columnIndices = null === (_change$columnIndices = change.columnIndices) || void 0 === _change$columnIndices ? void 0 : _change$columnIndices[index];\r\n                                        if (isDefined(item.visible) && item.visible !== $rowElement.is(\":visible\")) {\r\n                                            $rowElement.toggle(item.visible)\r\n                                        } else if (columnIndices) {\r\n                                            this._updateCells($rowElement, $newRowElement, columnIndices, item)\r\n                                        } else {\r\n                                            $rowElement.replaceWith($newRowElement)\r\n                                        }\r\n                                    }\r\n                                    break;\r\n                                case \"insert\":\r\n                                    if (!$rowElements.length) {\r\n                                        if (tableElement) {\r\n                                            const target = $newRowElement.is(\"tbody\") ? tableElement : tableElement.children(\"tbody\");\r\n                                            $newRowElement.prependTo(target)\r\n                                        }\r\n                                    } else if ($rowElement.length) {\r\n                                        $newRowElement.insertBefore($rowElement)\r\n                                    } else {\r\n                                        $newRowElement.insertAfter($rowElements.last())\r\n                                    }\r\n                                    if (highlightChanges && change.isLiveUpdate) {\r\n                                        $newRowElement.addClass(rowInsertedClass)\r\n                                    }\r\n                                    break;\r\n                                case \"remove\":\r\n                                    $rowElement.remove()\r\n                            }\r\n                        }))\r\n                    }));\r\n                    each(executors, (function() {\r\n                        this()\r\n                    }));\r\n                    newTableElement.remove()\r\n                } else {\r\n                    this.setTableElement(newTableElement, isFixedTableRendering);\r\n                    contentElement.addClass(this.addWidgetPrefix(\"content\"));\r\n                    this._setGridRole(contentElement);\r\n                    this._renderContent(contentElement, newTableElement, isFixedTableRendering)\r\n                }\r\n            }))\r\n        })).fail((() => {\r\n            this._contentChanges = []\r\n        }))\r\n    }\r\n    _getGridRoleName() {\r\n        return \"grid\"\r\n    }\r\n    _setGridRole($element) {\r\n        var _this$_dataController;\r\n        const hasData = !(null !== (_this$_dataController = this._dataController) && void 0 !== _this$_dataController && _this$_dataController.isEmpty());\r\n        const gridRoleName = this._getGridRoleName();\r\n        if (null !== $element && void 0 !== $element && $element.length && hasData) {\r\n            this.setAria(\"role\", gridRoleName, $element)\r\n        }\r\n    }\r\n    _createEmptyRow(className, isFixed, height) {\r\n        const that = this;\r\n        let $cell;\r\n        const $row = that._createRow();\r\n        const columns = isFixed ? this.getFixedColumns() : this.getColumns();\r\n        $row.addClass(className).toggleClass(\"dx-column-lines\", that.option(\"showColumnLines\"));\r\n        for (let i = 0; i < columns.length; i++) {\r\n            $cell = that._createCell({\r\n                column: columns[i],\r\n                rowType: \"freeSpace\",\r\n                columnIndex: i,\r\n                columns: columns\r\n            });\r\n            isNumeric(height) && $cell.css(\"height\", height);\r\n            $row.append($cell)\r\n        }\r\n        that.setAria(\"role\", \"presentation\", $row);\r\n        return $row\r\n    }\r\n    getFixedColumns() {\r\n        throw new Error(\"Method not implemented.\")\r\n    }\r\n    _appendEmptyRow($table, $emptyRow, location) {\r\n        const $tBodies = this._getBodies($table);\r\n        const isTableContainer = !$tBodies.length || $emptyRow.is(\"tbody\");\r\n        const $container = isTableContainer ? $table : $tBodies;\r\n        if (\"top\" === location) {\r\n            $container.first().prepend($emptyRow);\r\n            if (isTableContainer) {\r\n                const $colgroup = $container.children(\"colgroup\");\r\n                $container.prepend($colgroup)\r\n            }\r\n        } else {\r\n            $container.last().append($emptyRow)\r\n        }\r\n    }\r\n    _renderFreeSpaceRow($tableElement, change) {\r\n        let $freeSpaceRowElement = this._createEmptyRow(FREE_SPACE_CLASS);\r\n        $freeSpaceRowElement = this._wrapRowIfNeed($tableElement, $freeSpaceRowElement, \"refresh\" === (null === change || void 0 === change ? void 0 : change.changeType));\r\n        this._appendEmptyRow($tableElement, $freeSpaceRowElement)\r\n    }\r\n    _checkRowKeys(options) {\r\n        const that = this;\r\n        const rows = that._getRows(options);\r\n        const keyExpr = that._dataController.store() && that._dataController.store().key();\r\n        keyExpr && rows.some((row => {\r\n            if (\"data\" === row.rowType && void 0 === row.key) {\r\n                that._dataController.fireError(\"E1046\", keyExpr);\r\n                return true\r\n            }\r\n            return\r\n        }))\r\n    }\r\n    _needUpdateRowHeight(itemsCount) {\r\n        return itemsCount > 0 && !this._rowHeight\r\n    }\r\n    _getRowsHeight($tableElement) {\r\n        $tableElement = $tableElement || this._tableElement;\r\n        const $rowElements = $tableElement.children(\"tbody\").children().not(\".dx-virtual-row\").not(`.${FREE_SPACE_CLASS}`);\r\n        return $rowElements.toArray().reduce(((sum, row) => sum + getBoundingRect(row).height), 0)\r\n    }\r\n    _updateRowHeight() {\r\n        const that = this;\r\n        const $tableElement = that.getTableElement();\r\n        const itemsCount = that._dataController.items().length;\r\n        if ($tableElement && that._needUpdateRowHeight(itemsCount)) {\r\n            const rowsHeight = that._getRowsHeight($tableElement);\r\n            that._rowHeight = rowsHeight / itemsCount\r\n        }\r\n    }\r\n    _findContentElement(isFixedTableRendering) {\r\n        let $content = this.element();\r\n        const scrollable = this.getScrollable();\r\n        if ($content) {\r\n            if (scrollable) {\r\n                $content = $(scrollable.content())\r\n            }\r\n            return $content.children().first()\r\n        }\r\n    }\r\n    _getRowElements(tableElement) {\r\n        const $rows = super._getRowElements(tableElement);\r\n        return $rows && $rows.not(`.${FREE_SPACE_CLASS}`)\r\n    }\r\n    _getFreeSpaceRowElements($table) {\r\n        const tableElements = $table || this.getTableElements();\r\n        return tableElements && tableElements.children(\"tbody\").children(`.${FREE_SPACE_CLASS}`)\r\n    }\r\n    _getNoDataText() {\r\n        return this.option(\"noDataText\")\r\n    }\r\n    _rowClick(e) {\r\n        const item = this._dataController.items()[e.rowIndex] || {};\r\n        this.executeAction(\"onRowClick\", extend({\r\n            evaluate(expr) {\r\n                const getter = compileGetter(expr);\r\n                return getter(item.data)\r\n            }\r\n        }, e, item))\r\n    }\r\n    _rowDblClick(e) {\r\n        const item = this._dataController.items()[e.rowIndex] || {};\r\n        this.executeAction(\"onRowDblClick\", extend({}, e, item))\r\n    }\r\n    _getColumnsCountBeforeGroups(columns) {\r\n        for (let i = 0; i < columns.length; i++) {\r\n            if (\"groupExpand\" === columns[i].type) {\r\n                return i\r\n            }\r\n        }\r\n        return 0\r\n    }\r\n    _getGroupCellOptions(options) {\r\n        const columnsCountBeforeGroups = this._getColumnsCountBeforeGroups(options.columns);\r\n        const columnIndex = (options.row.groupIndex || 0) + columnsCountBeforeGroups;\r\n        return {\r\n            columnIndex: columnIndex,\r\n            colspan: options.columns.length - columnIndex - 1\r\n        }\r\n    }\r\n    _needWrapRow() {\r\n        return super._needWrapRow.apply(this, arguments) || !!this.option(\"dataRowTemplate\")\r\n    }\r\n    _renderCells($row, options) {\r\n        if (\"group\" === options.row.rowType) {\r\n            this._renderGroupedCells($row, options)\r\n        } else if (options.row.values) {\r\n            super._renderCells($row, options)\r\n        }\r\n    }\r\n    _renderGroupedCells($row, options) {\r\n        const {\r\n            row: row\r\n        } = options;\r\n        let expandColumn;\r\n        const {\r\n            columns: columns\r\n        } = options;\r\n        const {\r\n            rowIndex: rowIndex\r\n        } = row;\r\n        let isExpanded;\r\n        const groupCellOptions = this._getGroupCellOptions(options);\r\n        for (let i = 0; i <= groupCellOptions.columnIndex; i++) {\r\n            if (i === groupCellOptions.columnIndex && columns[i].allowCollapsing && \"infinite\" !== options.scrollingMode) {\r\n                isExpanded = !!row.isExpanded;\r\n                expandColumn = columns[i]\r\n            } else {\r\n                isExpanded = null;\r\n                expandColumn = {\r\n                    command: \"expand\",\r\n                    cssClass: columns[i].cssClass,\r\n                    fixed: columns[i].fixed\r\n                }\r\n            }\r\n            if (this._needRenderCell(i, options.columnIndices)) {\r\n                this._renderCell($row, {\r\n                    value: isExpanded,\r\n                    row: row,\r\n                    rowIndex: rowIndex,\r\n                    column: expandColumn,\r\n                    columnIndex: i,\r\n                    columnIndices: options.columnIndices,\r\n                    change: options.change\r\n                })\r\n            }\r\n        }\r\n        const groupColumnAlignment = getDefaultAlignment(this.option(\"rtlEnabled\"));\r\n        const groupColumn = extend({}, columns[groupCellOptions.columnIndex], {\r\n            command: null,\r\n            type: null,\r\n            cssClass: null,\r\n            width: null,\r\n            showWhenGrouped: false,\r\n            alignment: groupColumnAlignment\r\n        });\r\n        if (groupCellOptions.colspan > 1) {\r\n            groupColumn.colspan = groupCellOptions.colspan\r\n        }\r\n        if (this._needRenderCell(groupCellOptions.columnIndex + 1, options.columnIndices)) {\r\n            this._renderCell($row, {\r\n                value: row.values[row.groupIndex],\r\n                row: row,\r\n                rowIndex: rowIndex,\r\n                column: groupColumn,\r\n                columnIndex: groupCellOptions.columnIndex + 1,\r\n                columnIndices: options.columnIndices,\r\n                change: options.change\r\n            })\r\n        }\r\n    }\r\n    _renderRows($table, options) {\r\n        const that = this;\r\n        const scrollingMode = that.option(\"scrolling.mode\");\r\n        super._renderRows($table, extend({\r\n            scrollingMode: scrollingMode\r\n        }, options));\r\n        that._checkRowKeys(options.change);\r\n        that._renderFreeSpaceRow($table, options.change);\r\n        if (!that._hasHeight) {\r\n            that.updateFreeSpaceRowHeight($table)\r\n        }\r\n    }\r\n    _renderDataRowByTemplate($table, options, dataRowTemplate) {\r\n        const {\r\n            row: row\r\n        } = options;\r\n        const rowOptions = extend({\r\n            columns: options.columns\r\n        }, row);\r\n        const $tbody = this._createRow(row, \"tbody\");\r\n        $tbody.appendTo($table);\r\n        this.renderTemplate($tbody, dataRowTemplate, rowOptions, true, options.change);\r\n        this._rowPrepared($tbody, rowOptions, options.row)\r\n    }\r\n    _renderRow($table, options) {\r\n        const {\r\n            row: row\r\n        } = options;\r\n        const {\r\n            rowTemplate: rowTemplate\r\n        } = this.option();\r\n        const dataRowTemplate = this.option(\"dataRowTemplate\");\r\n        if (\"data\" === row.rowType && dataRowTemplate) {\r\n            this._renderDataRowByTemplate($table, options, dataRowTemplate)\r\n        } else if ((\"data\" === row.rowType || \"group\" === row.rowType) && !isDefined(row.groupIndex) && rowTemplate) {\r\n            this.renderTemplate($table, rowTemplate, extend({\r\n                columns: options.columns\r\n            }, row), true)\r\n        } else {\r\n            super._renderRow($table, options)\r\n        }\r\n    }\r\n    _renderTable(options) {\r\n        const that = this;\r\n        const $table = super._renderTable(options);\r\n        const resizeCompletedHandler = function() {\r\n            const scrollableInstance = that.getScrollable();\r\n            if (scrollableInstance && that.element().closest(getWindow().document).length) {\r\n                that.resizeCompleted.remove(resizeCompletedHandler);\r\n                scrollableInstance._visibilityChanged(true)\r\n            }\r\n        };\r\n        if (!isDefined(that.getTableElement())) {\r\n            that.setTableElement($table);\r\n            that._renderScrollable(true);\r\n            that.resizeCompleted.add(resizeCompletedHandler)\r\n        } else {\r\n            that._renderScrollable()\r\n        }\r\n        return $table\r\n    }\r\n    _createTable() {\r\n        const $table = super._createTable.apply(this, arguments);\r\n        if (this.option().rowTemplate || this.option().dataRowTemplate) {\r\n            $table.appendTo(this.component.$element())\r\n        }\r\n        return $table\r\n    }\r\n    _renderCore(change) {\r\n        const $element = this.element();\r\n        $element.addClass(this.addWidgetPrefix(\"rowsview\")).toggleClass(this.addWidgetPrefix(\"nowrap\"), !this.option(\"wordWrapEnabled\"));\r\n        $element.toggleClass(\"dx-empty\", this._dataController.isEmpty());\r\n        this.setAria(\"role\", \"presentation\", $element);\r\n        const $table = this._renderTable({\r\n            change: change\r\n        });\r\n        const deferred = this._updateContent($table, change);\r\n        super._renderCore(change);\r\n        this._lastColumnWidths = null;\r\n        return deferred\r\n    }\r\n    _getRows(change) {\r\n        return change && change.items || this._dataController.items()\r\n    }\r\n    _getCellOptions(options) {\r\n        const that = this;\r\n        const {\r\n            column: column\r\n        } = options;\r\n        const {\r\n            row: row\r\n        } = options;\r\n        const {\r\n            data: data\r\n        } = row;\r\n        const summaryCells = row && row.summaryCells;\r\n        const {\r\n            value: value\r\n        } = options;\r\n        const displayValue = gridCoreUtils.getDisplayValue(column, value, data, row.rowType);\r\n        const parameters = super._getCellOptions(options);\r\n        parameters.value = value;\r\n        parameters.oldValue = options.oldValue;\r\n        parameters.displayValue = displayValue;\r\n        parameters.row = row;\r\n        parameters.key = row.key;\r\n        parameters.data = data;\r\n        parameters.rowType = row.rowType;\r\n        parameters.values = row.values;\r\n        parameters.text = !column.command ? gridCoreUtils.formatValue(displayValue, column) : \"\";\r\n        parameters.rowIndex = row.rowIndex;\r\n        parameters.summaryItems = summaryCells && summaryCells[options.columnIndex];\r\n        parameters.resized = column.resizedCallbacks;\r\n        if (isDefined(column.groupIndex) && !column.command) {\r\n            const groupingTextsOptions = that.option(\"grouping.texts\");\r\n            const scrollingMode = that.option(\"scrolling.mode\");\r\n            if (\"virtual\" !== scrollingMode && \"infinite\" !== scrollingMode) {\r\n                parameters.groupContinuesMessage = data && data.isContinuationOnNextPage && groupingTextsOptions && groupingTextsOptions.groupContinuesMessage;\r\n                parameters.groupContinuedMessage = data && data.isContinuation && groupingTextsOptions && groupingTextsOptions.groupContinuedMessage\r\n            }\r\n        }\r\n        return parameters\r\n    }\r\n    _toggleDraggableSourceColumnClass($rows, visibleColumns, columnIndex, value) {\r\n        const columnsController = this._columnsController;\r\n        const columns = columnsController.getColumns();\r\n        const column = columns && columns[columnIndex];\r\n        const columnID = column && column.isBand && column.index;\r\n        each($rows, ((rowIndex, row) => {\r\n            if (!$(row).hasClass(\"dx-group-row\")) {\r\n                for (let i = 0; i < visibleColumns.length; i++) {\r\n                    if (isNumeric(columnID) && columnsController.isParentBandColumn(visibleColumns[i].index, columnID) || visibleColumns[i].index === columnIndex) {\r\n                        $rows.eq(rowIndex).children().eq(i).toggleClass(this.addWidgetPrefix(REORDERING_CLASSES.draggableColumn), value);\r\n                        if (!isNumeric(columnID)) {\r\n                            break\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }))\r\n    }\r\n    _getDevicePixelRatio() {\r\n        return getWindow().devicePixelRatio\r\n    }\r\n    renderNoDataText() {\r\n        return gridCoreUtils.renderNoDataText.apply(this, arguments)\r\n    }\r\n    getCellOptions(rowIndex, columnIdentifier) {\r\n        const rowOptions = this._dataController.items()[rowIndex];\r\n        let cellOptions;\r\n        let column;\r\n        if (rowOptions) {\r\n            if (isString(columnIdentifier)) {\r\n                column = this._columnsController.columnOption(columnIdentifier)\r\n            } else {\r\n                column = this._columnsController.getVisibleColumns()[columnIdentifier]\r\n            }\r\n            if (column) {\r\n                cellOptions = this._getCellOptions({\r\n                    value: column.calculateCellValue(rowOptions.data),\r\n                    rowIndex: rowOptions.rowIndex,\r\n                    row: rowOptions,\r\n                    column: column\r\n                })\r\n            }\r\n        }\r\n        return cellOptions\r\n    }\r\n    getRow(index) {\r\n        if (index >= 0) {\r\n            const rows = this._getRowElements();\r\n            if (rows.length > index) {\r\n                return $(rows[index])\r\n            }\r\n        }\r\n        return\r\n    }\r\n    updateFreeSpaceRowHeight($table) {\r\n        const dataController = this._dataController;\r\n        const itemCount = dataController.items(true).length;\r\n        const contentElement = this._findContentElement();\r\n        const freeSpaceRowElements = this._getFreeSpaceRowElements($table);\r\n        if (freeSpaceRowElements && contentElement && dataController.totalCount() >= 0) {\r\n            let isFreeSpaceRowVisible = false;\r\n            if (itemCount > 0) {\r\n                if (!this._hasHeight) {\r\n                    const freeSpaceRowCount = dataController.pageSize() - itemCount;\r\n                    const scrollingMode = this.option(\"scrolling.mode\");\r\n                    if (freeSpaceRowCount > 0 && dataController.pageCount() > 1 && \"virtual\" !== scrollingMode && \"infinite\" !== scrollingMode) {\r\n                        setHeight(freeSpaceRowElements, freeSpaceRowCount * this._rowHeight);\r\n                        isFreeSpaceRowVisible = true\r\n                    }\r\n                    if (!isFreeSpaceRowVisible && $table) {\r\n                        setHeight(freeSpaceRowElements, 0)\r\n                    } else {\r\n                        freeSpaceRowElements.toggle(isFreeSpaceRowVisible)\r\n                    }\r\n                    this._updateLastRowBorder(isFreeSpaceRowVisible)\r\n                } else {\r\n                    freeSpaceRowElements.hide();\r\n                    deferUpdate((() => {\r\n                        const scrollbarWidth = this.getScrollbarWidth(true);\r\n                        const elementHeightWithoutScrollbar = getHeight(this.element()) - scrollbarWidth;\r\n                        const contentHeight = getOuterHeight(contentElement);\r\n                        const showFreeSpaceRow = elementHeightWithoutScrollbar - contentHeight > 0;\r\n                        const rowsHeight = this._getRowsHeight(contentElement.children().first());\r\n                        const $tableElement = $table || this.getTableElements();\r\n                        const borderTopWidth = Math.ceil(parseFloat($tableElement.css(\"borderTopWidth\")));\r\n                        const heightCorrection = this._getHeightCorrection();\r\n                        const resultHeight = elementHeightWithoutScrollbar - rowsHeight - borderTopWidth - heightCorrection;\r\n                        if (showFreeSpaceRow) {\r\n                            deferRender((() => {\r\n                                freeSpaceRowElements.css(\"height\", resultHeight);\r\n                                isFreeSpaceRowVisible = true;\r\n                                freeSpaceRowElements.show()\r\n                            }))\r\n                        }\r\n                        deferRender((() => this._updateLastRowBorder(isFreeSpaceRowVisible)))\r\n                    }))\r\n                }\r\n            } else {\r\n                freeSpaceRowElements.css(\"height\", 0);\r\n                freeSpaceRowElements.show();\r\n                this._updateLastRowBorder(true)\r\n            }\r\n        }\r\n    }\r\n    _getHeightCorrection() {\r\n        const isZoomedWebkit = browser.webkit && this._getDevicePixelRatio() >= 2;\r\n        const isChromeLatest = browser.chrome && browser.version >= 91;\r\n        const hasExtraBorderTop = browser.mozilla && browser.version >= 70 && !this.option(\"showRowLines\");\r\n        return isZoomedWebkit || hasExtraBorderTop || isChromeLatest ? 1 : 0\r\n    }\r\n    _columnOptionChanged(e) {\r\n        const {\r\n            optionNames: optionNames\r\n        } = e;\r\n        if (e.changeTypes.grouping) {\r\n            return\r\n        }\r\n        if (optionNames.width || optionNames.visibleWidth) {\r\n            super._columnOptionChanged(e);\r\n            this._fireColumnResizedCallbacks()\r\n        }\r\n    }\r\n    getScrollable() {\r\n        return this._scrollable\r\n    }\r\n    _handleDataChanged(change) {\r\n        const that = this;\r\n        switch (change.changeType) {\r\n            case \"refresh\":\r\n            case \"prepend\":\r\n            case \"append\":\r\n            case \"update\":\r\n                that.render(null, change);\r\n                break;\r\n            default:\r\n                that._update(change)\r\n        }\r\n    }\r\n    publicMethods() {\r\n        return [\"isScrollbarVisible\", \"getTopVisibleRowData\", \"getScrollbarWidth\", \"getCellElement\", \"getRowElement\", \"getScrollable\"]\r\n    }\r\n    contentWidth() {\r\n        return getWidth(this.element()) - this.getScrollbarWidth()\r\n    }\r\n    getScrollbarWidth(isHorizontal) {\r\n        const scrollableContainer = this._scrollableContainer && this._scrollableContainer.get(0);\r\n        let scrollbarWidth = 0;\r\n        if (scrollableContainer) {\r\n            if (!isHorizontal) {\r\n                scrollbarWidth = scrollableContainer.clientWidth ? scrollableContainer.offsetWidth - scrollableContainer.clientWidth : 0\r\n            } else {\r\n                scrollbarWidth = scrollableContainer.clientHeight ? scrollableContainer.offsetHeight - scrollableContainer.clientHeight : 0;\r\n                scrollbarWidth += getScrollableBottomPadding(this)\r\n            }\r\n        }\r\n        return scrollbarWidth > 0 ? scrollbarWidth : 0\r\n    }\r\n    _fireColumnResizedCallbacks() {\r\n        const lastColumnWidths = this._lastColumnWidths || [];\r\n        const columnWidths = [];\r\n        const columns = this.getColumns();\r\n        for (let i = 0; i < columns.length; i++) {\r\n            columnWidths[i] = columns[i].visibleWidth;\r\n            if (columns[i].resizedCallbacks && !isDefined(columns[i].groupIndex) && lastColumnWidths[i] !== columnWidths[i]) {\r\n                columns[i].resizedCallbacks.fire(columnWidths[i])\r\n            }\r\n        }\r\n        this._lastColumnWidths = columnWidths\r\n    }\r\n    _updateLastRowBorder(isFreeSpaceRowVisible) {\r\n        if (this.option(\"showBorders\") && !isFreeSpaceRowVisible) {\r\n            this.element().addClass(LAST_ROW_BORDER)\r\n        } else {\r\n            this.element().removeClass(LAST_ROW_BORDER)\r\n        }\r\n    }\r\n    _updateScrollable() {\r\n        const scrollable = Scrollable.getInstance(this.element());\r\n        if (scrollable) {\r\n            scrollable.update();\r\n            if (scrollable.option(\"useNative\") || !(null !== scrollable && void 0 !== scrollable && scrollable.isRenovated())) {\r\n                this._updateHorizontalScrollPosition()\r\n            }\r\n        }\r\n    }\r\n    _updateHorizontalScrollPosition() {\r\n        const scrollable = this.getScrollable();\r\n        const scrollLeft = scrollable && scrollable.scrollOffset().left;\r\n        const rtlEnabled = this.option(\"rtlEnabled\");\r\n        if (rtlEnabled) {\r\n            const maxHorizontalScrollOffset = getMaxHorizontalScrollOffset(scrollable);\r\n            const scrollRight = maxHorizontalScrollOffset - scrollLeft;\r\n            if (scrollRight !== this._scrollRight) {\r\n                this._scrollLeft = maxHorizontalScrollOffset - this._scrollRight\r\n            }\r\n        }\r\n        if (this._scrollLeft >= 0 && scrollLeft !== this._scrollLeft) {\r\n            scrollable.scrollTo({\r\n                x: this._scrollLeft\r\n            })\r\n        }\r\n    }\r\n    _resizeCore() {\r\n        const that = this;\r\n        that._fireColumnResizedCallbacks();\r\n        that._updateRowHeight();\r\n        deferRender((() => {\r\n            that._renderScrollable();\r\n            that.renderNoDataText();\r\n            that.updateFreeSpaceRowHeight();\r\n            deferUpdate((() => {\r\n                that._updateScrollable()\r\n            }))\r\n        }))\r\n    }\r\n    scrollTo(location) {\r\n        const $element = this.element();\r\n        const dxScrollable = $element && Scrollable.getInstance($element);\r\n        if (dxScrollable) {\r\n            dxScrollable.scrollTo(location)\r\n        }\r\n    }\r\n    height(height) {\r\n        const that = this;\r\n        const $element = this.element();\r\n        if (0 === arguments.length) {\r\n            return $element ? getOuterHeight($element, true) : 0\r\n        }\r\n        if (isDefined(height) && $element) {\r\n            that.hasHeight(\"auto\" !== height);\r\n            setHeight($element, height)\r\n        }\r\n    }\r\n    hasHeight(hasHeight) {\r\n        if (0 === arguments.length) {\r\n            return !!this._hasHeight\r\n        }\r\n        this._hasHeight = hasHeight;\r\n        return\r\n    }\r\n    setLoading(isLoading, messageText) {\r\n        const that = this;\r\n        let loadPanel = that._loadPanel;\r\n        const dataController = that._dataController;\r\n        const loadPanelOptions = that.option(\"loadPanel\") || {};\r\n        const animation = dataController.isLoaded() ? loadPanelOptions.animation : null;\r\n        const $element = that.element();\r\n        if (!hasWindow()) {\r\n            return\r\n        }\r\n        if (!loadPanel && void 0 !== messageText && dataController.isLocalStore() && \"auto\" === loadPanelOptions.enabled && $element) {\r\n            that._renderLoadPanel($element, $element.parent());\r\n            loadPanel = that._loadPanel\r\n        }\r\n        if (loadPanel) {\r\n            const visibilityOptions = {\r\n                message: messageText || loadPanelOptions.text,\r\n                animation: animation,\r\n                visible: isLoading\r\n            };\r\n            if (isLoading) {\r\n                visibilityOptions.position = gridCoreUtils.calculateLoadPanelPosition($element)\r\n            }\r\n            clearTimeout(that._hideLoadingTimeoutID);\r\n            if (loadPanel.option(\"visible\") && !isLoading) {\r\n                that._hideLoadingTimeoutID = setTimeout((() => {\r\n                    loadPanel.option(visibilityOptions)\r\n                }), 200)\r\n            } else {\r\n                loadPanel.option(visibilityOptions)\r\n            }\r\n        }\r\n    }\r\n    toggleDraggableColumnClass(columnIndex, value) {\r\n        const $rows = this._getRowElements().not(\".dx-group-row\") || [];\r\n        this._toggleDraggableSourceColumnClass($rows, this.getColumns(), columnIndex, value)\r\n    }\r\n    _getCellElementsCore(rowIndex) {\r\n        const $cells = super._getCellElementsCore.apply(this, arguments);\r\n        if ($cells) {\r\n            const groupCellIndex = $cells.filter(\".dx-group-cell\").index();\r\n            if (groupCellIndex >= 0 && $cells.length > groupCellIndex + 1) {\r\n                return $cells.slice(0, groupCellIndex + 1)\r\n            }\r\n        }\r\n        return $cells\r\n    }\r\n    _getBoundaryVisibleItemIndex(isTop, isFloor) {\r\n        const that = this;\r\n        let itemIndex = 0;\r\n        let prevOffset = 0;\r\n        let offset = 0;\r\n        let viewportBoundary = that._scrollTop;\r\n        const $contentElement = that._findContentElement();\r\n        const contentElementOffsetTop = $contentElement && $contentElement.offset().top;\r\n        const items = this._dataController.items();\r\n        const tableElement = that.getTableElement();\r\n        if (items.length && tableElement) {\r\n            const rowElements = that._getRowElements(tableElement).filter(\":visible\");\r\n            if (!isTop) {\r\n                const height = getOuterHeight(this._hasHeight ? this.element() : getWindow());\r\n                viewportBoundary += height\r\n            }\r\n            for (itemIndex = 0; itemIndex < items.length; itemIndex++) {\r\n                prevOffset = offset;\r\n                const $rowElement = $(rowElements).eq(itemIndex);\r\n                if ($rowElement.length) {\r\n                    offset = $rowElement.offset();\r\n                    offset = (isTop ? offset.top : offset.top + getOuterHeight($rowElement)) - contentElementOffsetTop;\r\n                    if (offset > viewportBoundary) {\r\n                        if (itemIndex) {\r\n                            if (isFloor || 2 * viewportBoundary < Math.round(offset + prevOffset)) {\r\n                                itemIndex--\r\n                            }\r\n                        }\r\n                        break\r\n                    }\r\n                }\r\n            }\r\n            if (itemIndex && itemIndex === items.length) {\r\n                itemIndex--\r\n            }\r\n        }\r\n        return itemIndex\r\n    }\r\n    getTopVisibleItemIndex(isFloor) {\r\n        return this._getBoundaryVisibleItemIndex(true, isFloor)\r\n    }\r\n    getBottomVisibleItemIndex(isFloor) {\r\n        return this._getBoundaryVisibleItemIndex(false, isFloor)\r\n    }\r\n    getTopVisibleRowData() {\r\n        const itemIndex = this.getTopVisibleItemIndex();\r\n        const items = this._dataController.items();\r\n        if (items[itemIndex]) {\r\n            return items[itemIndex].data\r\n        }\r\n        return\r\n    }\r\n    _scrollToElement($element, offset) {\r\n        const scrollable = this.getScrollable();\r\n        scrollable && scrollable.scrollToElement($element, offset)\r\n    }\r\n    optionChanged(args) {\r\n        const that = this;\r\n        super.optionChanged(args);\r\n        switch (args.name) {\r\n            case \"wordWrapEnabled\":\r\n            case \"showColumnLines\":\r\n            case \"showRowLines\":\r\n            case \"rowAlternationEnabled\":\r\n            case \"rowTemplate\":\r\n            case \"dataRowTemplate\":\r\n            case \"twoWayBindingEnabled\":\r\n                that._invalidate(true, true);\r\n                args.handled = true;\r\n                break;\r\n            case \"scrolling\":\r\n                that._rowHeight = null;\r\n                that._tableElement = null;\r\n                args.handled = true;\r\n                break;\r\n            case \"rtlEnabled\":\r\n                that._rowHeight = null;\r\n                that._tableElement = null;\r\n                break;\r\n            case \"loadPanel\":\r\n                that._tableElement = null;\r\n                that._invalidate(true, \"loadPanel.enabled\" !== args.fullName);\r\n                args.handled = true;\r\n                break;\r\n            case \"noDataText\":\r\n                that.renderNoDataText();\r\n                args.handled = true\r\n        }\r\n    }\r\n    setAriaOwns(headerTableId, footerTableId, isFixed) {\r\n        const $contentElement = this._findContentElement();\r\n        const $tableElement = this.getTableElement();\r\n        if (null !== $tableElement && void 0 !== $tableElement && $tableElement.length) {\r\n            this.setAria(\"owns\", `${headerTableId??\"\"} ${$tableElement.attr(\"id\")??\"\"} ${footerTableId??\"\"}`.trim(), $contentElement)\r\n        }\r\n    }\r\n    dispose() {\r\n        super.dispose();\r\n        clearTimeout(this._hideLoadingTimeoutID);\r\n        this._scrollable && this._scrollable.dispose()\r\n    }\r\n    setScrollerSpacing(vScrollbarWidth, hScrollbarWidth) {}\r\n    getFixedContentElement() {\r\n        var _this$element;\r\n        const fixedContentClass = this.addWidgetPrefix(\"content-fixed\");\r\n        return null === (_this$element = this.element()) || void 0 === _this$element ? void 0 : _this$element.children(`.${fixedContentClass}`)\r\n    }\r\n    _restoreErrorRow(contentTable) {}\r\n    isElementInside($element) {\r\n        const $rowsViewElement = $element.closest(`.${this.addWidgetPrefix(\"rowsview\")}`);\r\n        return $rowsViewElement.is(this.element())\r\n    }\r\n}\r\nexport const rowsModule = {\r\n    defaultOptions: () => ({\r\n        hoverStateEnabled: false,\r\n        scrolling: {\r\n            useNative: \"auto\"\r\n        },\r\n        loadPanel: {\r\n            enabled: \"auto\",\r\n            text: messageLocalization.format(\"Loading\"),\r\n            width: 200,\r\n            height: 90,\r\n            showIndicator: true,\r\n            indicatorSrc: \"\",\r\n            showPane: true\r\n        },\r\n        dataRowTemplate: null,\r\n        columnAutoWidth: false,\r\n        noDataText: messageLocalization.format(\"dxDataGrid-noDataText\"),\r\n        wordWrapEnabled: false,\r\n        showColumnLines: true,\r\n        showRowLines: false,\r\n        rowAlternationEnabled: false,\r\n        activeStateEnabled: false,\r\n        twoWayBindingEnabled: true\r\n    }),\r\n    views: {\r\n        rowsView: RowsView\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AACA;AAAA;AACA;AAAA;AAGA;AACA;AACA;AACA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAKA;AAAA;AAGA;AAAA;AAGA;AAAA;AAKA;AAAA;AAIA;AACA;AAGA;AACA;AAGA;;;;;;;;;;;;;;;;;;;;;;AAGA,MAAM,kBAAkB;AACxB,MAAM,gBAAgB;AACtB,MAAM,eAAe;AACrB,MAAM,kBAAkB;AACxB,MAAM,mBAAmB;AACzB,MAAM,iBAAiB;AACvB,MAAM,mBAAmB;AACzB,MAAM,qBAAqB;AAC3B,MAAM,wBAAwB;AAC9B,MAAM,kBAAkB;AACxB,MAAM,cAAc;AACpB,MAAM,+BAA+B;AACrC,MAAM,sBAAsB;AACrB,MAAM,kBAAkB;AAC/B,MAAM,yBAAyB;AAE/B,SAAS,6BAA6B,UAAU;IAC5C,OAAO,aAAa,KAAK,KAAK,CAAC,WAAW,WAAW,KAAK,WAAW,WAAW,MAAM;AAC1F;AACO,SAAS,WAAW,IAAI;IAC3B,IAAI,EACA,SAAS,OAAO,EAChB,QAAQ,MAAM,EACjB,GAAG;IACJ,OAAO,YAAY,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,UAAU,KAAK,CAAC,OAAO,eAAe,IAAI,CAAC,OAAO,OAAO;AAC5G;AAEA,SAAS,WAAW,KAAK;IACrB,IAAI,EACA,SAAS,OAAO,EAChB,OAAO,KAAK,EACZ,QAAQ,MAAM,EACd,UAAU,QAAQ,EACrB,GAAG;IACJ,IAAI,OAAO;QACP,MAAM,UAAU,MAAM,QAAQ;QAC9B,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,SAAS,2KAAA,CAAA,cAAW,EAAE;IAC1C;AACJ;AACA,MAAM,sBAAsB,SAAS,UAAU,EAAE,OAAO;IACpD,MAAM,kBAAkB,CAAA,GAAA,kLAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,IAAI,KAAK,WAAW,QAAQ,OAAO;IAC3E,MAAM,EACF,MAAM,IAAI,EACb,GAAG;IACJ,MAAM,YAAY,WAAW,GAAG,CAAC;IACjC,IAAI,iBAAiB;QACjB,sLAAA,CAAA,UAAa,CAAC,YAAY,CAAC;IAC/B,OAAO,IAAI,QAAQ,MAAM,CAAC,UAAU,EAAE;QAClC,UAAU,WAAW,GAAG;IAC5B,OAAO;QACH,UAAU,SAAS,GAAG;IAC1B;AACJ;AACA,MAAM,6BAA6B,SAAS,IAAI;IAC5C,MAAM,aAAa,KAAK,aAAa;IACrC,OAAO,aAAa,KAAK,IAAI,CAAC,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO,IAAI,GAAG,CAAC,qBAAqB;AAC9F;AACO,MAAM,iBAAiB,sMAAA,CAAA,cAAW;IACrC,OAAO;QACH,KAAK,CAAC;QACN,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC9C,IAAI,CAAC,yBAAyB,GAAG,IAAI,CAAC,aAAa,CAAC;QACpD,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC3C,IAAI,CAAC,6BAA6B,GAAG,IAAI,CAAC,aAAa,CAAC;QACxD,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,aAAa,CAAC;QAChD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG,CAAC;QACpB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,UAAU,GAAG,KAAK;QACvB,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,IAAI,CAAC,eAAe,CAAC,cAAc,CAAC,GAAG,CAAE,CAAC,WAAW;YACjD,IAAI,CAAC,UAAU,CAAC,WAAW;QAC/B;QACA,IAAI,CAAC,eAAe,CAAC,iBAAiB,CAAC,GAAG,CAAE;YACxC,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,SAAS,IAAI;gBAC5D,IAAI,CAAC,aAAa,CAAC;oBACf,WAAW,IAAI,CAAC,aAAa;oBAC7B,2BAA2B;oBAC3B,cAAc;wBACV,KAAK,IAAI,CAAC,UAAU;wBACpB,MAAM,IAAI,CAAC,WAAW;oBAC1B;gBACJ;YACJ;QACJ;IACJ;IACA,oBAAoB,MAAM,EAAE;QACxB,IAAI,YAAY,OAAO,OAAO,EAAE;YAC5B,OAAO,SAAS,SAAS;gBACrB,UAAU,IAAI,CAAC;YACnB;QACJ,OAAO;YACH,OAAO;QACX;IACJ;IACA,iBAAiB,MAAM,EAAE,CAAC;IAC1B,yBAAyB,MAAM,EAAE;QAC7B,MAAM,eAAe,IAAI,CAAC,MAAM,CAAC;QACjC,OAAO,SAAS,UAAU,EAAE,OAAO;YAC/B,MAAM,EACF,MAAM,IAAI,EACb,GAAG;YACJ,IAAI,OAAO,AAAC,GAA6B,OAA3B,QAAQ,MAAM,CAAC,OAAO,EAAC,MAAiB,OAAb,QAAQ,IAAI;YACrD,MAAM,YAAY,WAAW,GAAG,CAAC;YACjC,IAAI,QAAQ,YAAY,IAAI,QAAQ,YAAY,CAAC,MAAM,EAAE;gBACrD,QAAQ,AAAC,IAA2E,OAAxE,sLAAA,CAAA,UAAa,CAAC,sBAAsB,CAAC,QAAQ,YAAY,EAAC;YAC1E;YACA,IAAI,MAAM;gBACN,IAAI,QAAQ,qBAAqB,IAAI,QAAQ,qBAAqB,EAAE;oBAChE,QAAQ,AAAC,KAAsC,OAAlC,QAAQ,qBAAqB,EAAC,MAAkC,OAA9B,QAAQ,qBAAqB,EAAC;gBACjF,OAAO,IAAI,QAAQ,qBAAqB,EAAE;oBACtC,QAAQ,AAAC,KAAkC,OAA9B,QAAQ,qBAAqB,EAAC;gBAC/C,OAAO,IAAI,QAAQ,qBAAqB,EAAE;oBACtC,QAAQ,AAAC,KAAkC,OAA9B,QAAQ,qBAAqB,EAAC;gBAC/C;YACJ;YACA,IAAI,OAAO,UAAU,EAAE;gBACnB,UAAU,WAAW,GAAG;YAC5B,OAAO;gBACH,UAAU,SAAS,GAAG;YAC1B;QACJ;IACJ;IACA,QAAQ,MAAM,EAAE,CAAC;IACjB,YAAY,KAAK,EAAE,OAAO,EAAE;QACxB,IAAI,WAAW,UAAU;YACrB,MAAM,mBAAmB,MAAM,EAAE,CAAC,AAAC,IAAmD,OAAhD,IAAI,CAAC,eAAe,CAAC,sMAAA,CAAA,UAAO,CAAC,iBAAiB;YACpF,MAAM,aAAa,mBAAmB,MAAM,MAAM,KAAK;YACvD,WAAW,QAAQ,CAAC;QACxB;QACA,KAAK,CAAC,YAAY,KAAK,CAAC,IAAI,EAAE;IAClC;IACA,iBAAiB,OAAO,EAAE;QACtB,MAAM,OAAO,IAAI;QACjB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,IAAI;QACJ,IAAI,WAAW,UAAU;YACrB,WAAW,OAAO,iBAAiB,IAAI;gBACnC,gCAAgC;gBAChC,QAAQ,KAAK,wBAAwB,CAAC;YAC1C;QACJ,OAAO,IAAI,CAAC,WAAW,QAAQ,OAAO,IAAI,OAAO,OAAO,KAAK,OAAO,YAAY,EAAE;YAC9E,WAAW,OAAO,YAAY;QAClC,OAAO;YACH,WAAW;gBACP,gCAAgC;gBAChC,QAAQ,KAAK,mBAAmB,CAAC;YACrC;QACJ;QACA,OAAO;IACX;IACA,WAAW,GAAG,EAAE,GAAG,EAAE;QACjB,MAAM,OAAO,KAAK,CAAC,WAAW,KAAK,CAAC,IAAI,EAAE;QAC1C,IAAI,KAAK;YACL,MAAM,UAAU,YAAY,IAAI,OAAO;YACvC,MAAM,YAAY,WAAW,IAAI,OAAO;YACxC,aAAa,KAAK,QAAQ,CAAC;YAC3B,aAAa,IAAI,CAAC,MAAM,CAAC,mBAAmB,KAAK,QAAQ,CAAC;YAC1D,IAAI,CAAC,MAAM,CAAC,sBAAsB,KAAK,QAAQ,CAAC;YAChD,IAAI,UAAU,IAAI,OAAO,EAAE;gBACvB,KAAK,IAAI;YACb;YACA,IAAI,SAAS;gBACT,KAAK,QAAQ,CAAC;gBACd,IAAI,CAAC,wBAAwB,CAAC,MAAM;YACxC;QACJ;QACA,OAAO;IACX;IACA,aAAa,IAAI,EAAE,UAAU,EAAE,GAAG,EAAE;QAChC,IAAI,WAAW,WAAW,OAAO,EAAE;YAC/B,IAAI,IAAI,CAAC,MAAM,CAAC,0BAA0B;gBACtC,IAAI,CAAC,SAAS,CAAC,QAAQ,KAAK,QAAQ,CAAC;gBACrC,WAAW;oBACP,SAAS,KAAK,GAAG,CAAC;oBAClB,OAAO,WAAW,KAAK;oBACvB,QAAQ,IAAM,IAAI,CAAC,SAAS,CAAC;oBAC7B,UAAU,CAAA;wBACN,KAAK,WAAW,CAAC,cAAc;oBACnC;gBACJ;YACJ;YACA,IAAI,CAAC,gBAAgB,CAAC,YAAY;YAClC,WAAW;gBACP,SAAS,KAAK,GAAG,CAAC;gBAClB,OAAO,WAAW,KAAK;gBACvB,QAAQ,IAAM,WAAW,QAAQ;gBACjC,UAAU,IAAM,IAAI,CAAC,gBAAgB,CAAC,YAAY;YACtD;QACJ;QACA,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE;IACnC;IACA,iBAAiB,GAAG,EAAE,IAAI,EAAE;QACxB,IAAI,CAAC,KAAK,EAAE,CAAC,OAAO;YAChB;QACJ;QACA,MAAM,EACF,WAAW,SAAS,EACvB,GAAG,IAAI;QACR,MAAM,cAAc,eAAe,UAAU,MAAM,CAAC,qBAAqB,CAAC,sLAAA,CAAA,UAAa,CAAC,qBAAqB,CAAC;QAC9G,IAAI,WAAW,IAAI,QAAQ,GAAG;QAC9B,IAAI,aAAa;YACb,WAAW,UAAU,SAAS,KAAK,UAAU,QAAQ,KAAK;QAC9D,OAAO;YACH,YAAY,IAAI,CAAC,eAAe,CAAC,iBAAiB;QACtD;QACA,IAAI,CAAC,OAAO,CAAC,YAAY,UAAU;IACvC;IACA,yBAAyB,IAAI,EAAE,GAAG,EAAE;QAChC,MAAM,cAAc,IAAI,UAAU,GAAG,IAAI,CAAC,QAAQ,CAAC,gCAAgC,IAAI,CAAC,QAAQ,CAAC;QACjG,IAAI,CAAC,OAAO,CAAC,mBAAmB,aAAa;IACjD;IACA,kBAAkB,CAAC,EAAE;QACjB,MAAM,MAAM,EAAE,IAAI,CAAC,EAAE;QACrB,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,MAAM,eAAe,cAAc,EAAE,CAAC,IAAI,QAAQ,CAAC;QACzD,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC;QAC1B,IAAI,CAAC,IAAI,IAAI,IAAI,WAAW,IAAI,OAAO,IAAI,IAAI,QAAQ,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC,2BAA2B,CAAC,SAAS,CAAC,KAAK;YAC/G;QACJ;QACA,MAAM,UAAU,MAAO,IAAM,eAAe,kBAAkB,CAAC,IAAI,IAAI,EAAE,IAAI,OAAO,GAAK;YACrF,eAAe,WAAW,CAAC;gBAAC,IAAI,QAAQ;aAAC,EAAE,IAAI,CAAC,MAAM,CAAC;QAC3D,GAAI;YACA,MAAM;YACN,eAAe;QACnB;QACA,0LAAA,CAAA,UAAY,CAAC,EAAE,CAAC,IAAI,UAAU,EAAE,2KAAA,CAAA,cAAW,EAAE;IACjD;IACA,kBAAkB,KAAK,EAAE;QACrB,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,OAAO;QAC7B,IAAI,CAAC,SAAS,QAAQ,GAAG,MAAM,EAAE;YAC7B,SAAS,MAAM,CAAC;QACpB;QACA,IAAI,SAAS,CAAC,KAAK,UAAU,EAAE;YAC3B,KAAK,gBAAgB,CAAC,UAAU,SAAS,MAAM,IAAI,KAAK,eAAe,CAAC,YAAY;QACxF;QACA,IAAI,CAAC,SAAS,CAAC,KAAK,aAAa,EAAE,KAAK,KAAK,eAAe,CAAC,QAAQ,IAAI;YACrE,MAAM,UAAU,KAAK,UAAU;YAC/B,IAAI,qBAAqB;YACzB,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACrC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE;oBAC3C,qBAAqB;oBACrB;gBACJ;YACJ;YACA,IAAI,KAAK,MAAM,CAAC,sBAAsB,KAAK,UAAU,IAAI,sBAAsB,KAAK,kBAAkB,CAAC,eAAe,IAAI;gBACtH,KAAK,qBAAqB,CAAC;YAC/B;QACJ;IACJ;IACA,cAAc,CAAC,EAAE;QACb,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,MAAM,oBAAoB,EAAE,SAAS,CAAC,MAAM,CAAC;QAC7C,IAAI,CAAC,UAAU,GAAG,EAAE,YAAY,CAAC,GAAG;QACpC,IAAI,CAAC,WAAW,GAAG,EAAE,YAAY,CAAC,IAAI;QACtC,IAAI,aAAa,EAAE,YAAY,CAAC,IAAI;QACpC,IAAI,YAAY;YACZ,IAAI,CAAC,YAAY,GAAG,6BAA6B,EAAE,SAAS,IAAI,IAAI,CAAC,WAAW;YAChF,IAAI,mBAAmB;gBACnB,aAAa,CAAC,IAAI,CAAC,YAAY;YACnC;YACA,IAAI,CAAC,IAAI,CAAC,kBAAkB,CAAC,OAAO;gBAChC,IAAI,CAAC,WAAW,GAAG,CAAC;YACxB;QACJ;QACA,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,EAAE,YAAY,EAAE;YACjD,MAAM;QACV,IAAI,IAAI,CAAC,IAAI;IACjB;IACA,sBAAsB,QAAQ,EAAE;QAC5B,MAAM,sBAAsB,IAAI,CAAC,wBAAwB;QACzD,MAAM,gBAAgB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI;QAClD,oBAAoB,QAAQ,GAAG;QAC/B,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,UAAU,6KAAA,CAAA,UAAU,EAAE;QAC/D,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,WAAW,IAAI,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,WAAW,CAAC,SAAS;IAChF;IACA,mBAAmB;QACf,IAAK,IAAI,OAAO,UAAU,MAAM,EAAE,OAAO,IAAI,MAAM,OAAO,OAAO,GAAG,OAAO,MAAM,OAAQ;YACrF,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC,KAAK;QAChC;QACA,OAAO,sLAAA,CAAA,UAAa,CAAC,eAAe,CAAC,KAAK,CAAC,IAAI,EAAE;IACrD;IACA,eAAe,cAAc,EAAE,YAAY,EAAE,qBAAqB,EAAE;QAChE,eAAe,KAAK,GAAG,MAAM,CAAC;QAC9B,OAAO,IAAI,CAAC,mBAAmB;IACnC;IACA,eAAe,eAAe,EAAE,MAAM,EAAE,qBAAqB,EAAE;QAC3D,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC;YACtB,iBAAiB;YACjB,QAAQ;YACR,uBAAuB;QAC3B;QACA,OAAO,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAE;YACnC,MAAM,iBAAiB,IAAI,CAAC,eAAe;YAC3C,IAAI,CAAC,eAAe,GAAG,EAAE;YACzB,eAAe,OAAO,CAAE,CAAA;gBACpB,IAAI,EACA,iBAAiB,eAAe,EAChC,QAAQ,MAAM,EACd,uBAAuB,qBAAqB,EAC/C,GAAG;gBACJ,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;gBAC1C,MAAM,iBAAiB,IAAI,CAAC,mBAAmB,CAAC;gBAChD,MAAM,aAAa,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,UAAU;gBACpF,MAAM,YAAY,EAAE;gBACpB,MAAM,mBAAmB,IAAI,CAAC,MAAM,CAAC;gBACrC,MAAM,mBAAmB,IAAI,CAAC,eAAe,CAAC;gBAC9C,IAAI,aAAa,YAAY;oBACzB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,OAAO,UAAU,EAAG,CAAC,OAAO;wBAC7B,IAAI;wBACJ,MAAM,iBAAiB,IAAI,CAAC,eAAe,CAAC,iBAAiB,EAAE,CAAC;wBAChE,MAAM,iBAAiB,SAAS,CAAC,sBAAsB,OAAO,WAAW,KAAK,KAAK,MAAM,sBAAsB,KAAK,IAAI,mBAAmB,CAAC,MAAM;wBAClJ,MAAM,OAAO,OAAO,KAAK,IAAI,OAAO,KAAK,CAAC,MAAM;wBAChD,UAAU,IAAI,CAAE;4BACZ,MAAM,eAAe,IAAI,CAAC,eAAe,CAAC;4BAC1C,MAAM,cAAc,aAAa,EAAE,CAAC;4BACpC,OAAQ;gCACJ,KAAK;oCACD,IAAI,MAAM;wCACN,IAAI;wCACJ,MAAM,gBAAgB,SAAS,CAAC,wBAAwB,OAAO,aAAa,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,qBAAqB,CAAC,MAAM;wCACzJ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,OAAO,KAAK,KAAK,OAAO,KAAK,YAAY,EAAE,CAAC,aAAa;4CACxE,YAAY,MAAM,CAAC,KAAK,OAAO;wCACnC,OAAO,IAAI,eAAe;4CACtB,IAAI,CAAC,YAAY,CAAC,aAAa,gBAAgB,eAAe;wCAClE,OAAO;4CACH,YAAY,WAAW,CAAC;wCAC5B;oCACJ;oCACA;gCACJ,KAAK;oCACD,IAAI,CAAC,aAAa,MAAM,EAAE;wCACtB,IAAI,cAAc;4CACd,MAAM,SAAS,eAAe,EAAE,CAAC,WAAW,eAAe,aAAa,QAAQ,CAAC;4CACjF,eAAe,SAAS,CAAC;wCAC7B;oCACJ,OAAO,IAAI,YAAY,MAAM,EAAE;wCAC3B,eAAe,YAAY,CAAC;oCAChC,OAAO;wCACH,eAAe,WAAW,CAAC,aAAa,IAAI;oCAChD;oCACA,IAAI,oBAAoB,OAAO,YAAY,EAAE;wCACzC,eAAe,QAAQ,CAAC;oCAC5B;oCACA;gCACJ,KAAK;oCACD,YAAY,MAAM;4BAC1B;wBACJ;oBACJ;oBACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,WAAY;wBACb,IAAI;oBACR;oBACA,gBAAgB,MAAM;gBAC1B,OAAO;oBACH,IAAI,CAAC,eAAe,CAAC,iBAAiB;oBACtC,eAAe,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;oBAC7C,IAAI,CAAC,YAAY,CAAC;oBAClB,IAAI,CAAC,cAAc,CAAC,gBAAgB,iBAAiB;gBACzD;YACJ;QACJ,GAAI,IAAI,CAAE;YACN,IAAI,CAAC,eAAe,GAAG,EAAE;QAC7B;IACJ;IACA,mBAAmB;QACf,OAAO;IACX;IACA,aAAa,QAAQ,EAAE;QACnB,IAAI;QACJ,MAAM,UAAU,CAAC,CAAC,SAAS,CAAC,wBAAwB,IAAI,CAAC,eAAe,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,OAAO,EAAE;QAChJ,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,IAAI,SAAS,YAAY,KAAK,MAAM,YAAY,SAAS,MAAM,IAAI,SAAS;YACxE,IAAI,CAAC,OAAO,CAAC,QAAQ,cAAc;QACvC;IACJ;IACA,gBAAgB,SAAS,EAAE,OAAO,EAAE,MAAM,EAAE;QACxC,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,MAAM,OAAO,KAAK,UAAU;QAC5B,MAAM,UAAU,UAAU,IAAI,CAAC,eAAe,KAAK,IAAI,CAAC,UAAU;QAClE,KAAK,QAAQ,CAAC,WAAW,WAAW,CAAC,mBAAmB,KAAK,MAAM,CAAC;QACpE,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,QAAQ,KAAK,WAAW,CAAC;gBACrB,QAAQ,OAAO,CAAC,EAAE;gBAClB,SAAS;gBACT,aAAa;gBACb,SAAS;YACb;YACA,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,MAAM,GAAG,CAAC,UAAU;YACzC,KAAK,MAAM,CAAC;QAChB;QACA,KAAK,OAAO,CAAC,QAAQ,gBAAgB;QACrC,OAAO;IACX;IACA,kBAAkB;QACd,MAAM,IAAI,MAAM;IACpB;IACA,gBAAgB,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE;QACzC,MAAM,WAAW,IAAI,CAAC,UAAU,CAAC;QACjC,MAAM,mBAAmB,CAAC,SAAS,MAAM,IAAI,UAAU,EAAE,CAAC;QAC1D,MAAM,aAAa,mBAAmB,SAAS;QAC/C,IAAI,UAAU,UAAU;YACpB,WAAW,KAAK,GAAG,OAAO,CAAC;YAC3B,IAAI,kBAAkB;gBAClB,MAAM,YAAY,WAAW,QAAQ,CAAC;gBACtC,WAAW,OAAO,CAAC;YACvB;QACJ,OAAO;YACH,WAAW,IAAI,GAAG,MAAM,CAAC;QAC7B;IACJ;IACA,oBAAoB,aAAa,EAAE,MAAM,EAAE;QACvC,IAAI,uBAAuB,IAAI,CAAC,eAAe,CAAC;QAChD,uBAAuB,IAAI,CAAC,cAAc,CAAC,eAAe,sBAAsB,cAAc,CAAC,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,OAAO,UAAU;QAChK,IAAI,CAAC,eAAe,CAAC,eAAe;IACxC;IACA,cAAc,OAAO,EAAE;QACnB,MAAM,OAAO,IAAI;QACjB,MAAM,OAAO,KAAK,QAAQ,CAAC;QAC3B,MAAM,UAAU,KAAK,eAAe,CAAC,KAAK,MAAM,KAAK,eAAe,CAAC,KAAK,GAAG,GAAG;QAChF,WAAW,KAAK,IAAI,CAAE,CAAA;YAClB,IAAI,WAAW,IAAI,OAAO,IAAI,KAAK,MAAM,IAAI,GAAG,EAAE;gBAC9C,KAAK,eAAe,CAAC,SAAS,CAAC,SAAS;gBACxC,OAAO;YACX;YACA;QACJ;IACJ;IACA,qBAAqB,UAAU,EAAE;QAC7B,OAAO,aAAa,KAAK,CAAC,IAAI,CAAC,UAAU;IAC7C;IACA,eAAe,aAAa,EAAE;QAC1B,gBAAgB,iBAAiB,IAAI,CAAC,aAAa;QACnD,MAAM,eAAe,cAAc,QAAQ,CAAC,SAAS,QAAQ,GAAG,GAAG,CAAC,mBAAmB,GAAG,CAAC,AAAC,IAAoB,OAAjB;QAC/F,OAAO,aAAa,OAAO,GAAG,MAAM,CAAE,CAAC,KAAK,MAAQ,MAAM,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,MAAM,EAAG;IAC5F;IACA,mBAAmB;QACf,MAAM,OAAO,IAAI;QACjB,MAAM,gBAAgB,KAAK,eAAe;QAC1C,MAAM,aAAa,KAAK,eAAe,CAAC,KAAK,GAAG,MAAM;QACtD,IAAI,iBAAiB,KAAK,oBAAoB,CAAC,aAAa;YACxD,MAAM,aAAa,KAAK,cAAc,CAAC;YACvC,KAAK,UAAU,GAAG,aAAa;QACnC;IACJ;IACA,oBAAoB,qBAAqB,EAAE;QACvC,IAAI,WAAW,IAAI,CAAC,OAAO;QAC3B,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,IAAI,UAAU;YACV,IAAI,YAAY;gBACZ,WAAW,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,WAAW,OAAO;YACnC;YACA,OAAO,SAAS,QAAQ,GAAG,KAAK;QACpC;IACJ;IACA,gBAAgB,YAAY,EAAE;QAC1B,MAAM,QAAQ,KAAK,CAAC,gBAAgB;QACpC,OAAO,SAAS,MAAM,GAAG,CAAC,AAAC,IAAoB,OAAjB;IAClC;IACA,yBAAyB,MAAM,EAAE;QAC7B,MAAM,gBAAgB,UAAU,IAAI,CAAC,gBAAgB;QACrD,OAAO,iBAAiB,cAAc,QAAQ,CAAC,SAAS,QAAQ,CAAC,AAAC,IAAoB,OAAjB;IACzE;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,UAAU,CAAC,EAAE;QACT,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC;QAC1D,IAAI,CAAC,aAAa,CAAC,cAAc,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;YACpC,UAAS,IAAI;gBACT,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE;gBAC7B,OAAO,OAAO,KAAK,IAAI;YAC3B;QACJ,GAAG,GAAG;IACV;IACA,aAAa,CAAC,EAAE;QACZ,MAAM,OAAO,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,EAAE,QAAQ,CAAC,IAAI,CAAC;QAC1D,IAAI,CAAC,aAAa,CAAC,iBAAiB,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,GAAG;IACtD;IACA,6BAA6B,OAAO,EAAE;QAClC,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,IAAI,kBAAkB,OAAO,CAAC,EAAE,CAAC,IAAI,EAAE;gBACnC,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,qBAAqB,OAAO,EAAE;QAC1B,MAAM,2BAA2B,IAAI,CAAC,4BAA4B,CAAC,QAAQ,OAAO;QAClF,MAAM,cAAc,CAAC,QAAQ,GAAG,CAAC,UAAU,IAAI,CAAC,IAAI;QACpD,OAAO;YACH,aAAa;YACb,SAAS,QAAQ,OAAO,CAAC,MAAM,GAAG,cAAc;QACpD;IACJ;IACA,eAAe;QACX,OAAO,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE,cAAc,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;IACtE;IACA,aAAa,IAAI,EAAE,OAAO,EAAE;QACxB,IAAI,YAAY,QAAQ,GAAG,CAAC,OAAO,EAAE;YACjC,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACnC,OAAO,IAAI,QAAQ,GAAG,CAAC,MAAM,EAAE;YAC3B,KAAK,CAAC,aAAa,MAAM;QAC7B;IACJ;IACA,oBAAoB,IAAI,EAAE,OAAO,EAAE;QAC/B,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,IAAI;QACJ,MAAM,EACF,SAAS,OAAO,EACnB,GAAG;QACJ,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;QACJ,IAAI;QACJ,MAAM,mBAAmB,IAAI,CAAC,oBAAoB,CAAC;QACnD,IAAK,IAAI,IAAI,GAAG,KAAK,iBAAiB,WAAW,EAAE,IAAK;YACpD,IAAI,MAAM,iBAAiB,WAAW,IAAI,OAAO,CAAC,EAAE,CAAC,eAAe,IAAI,eAAe,QAAQ,aAAa,EAAE;gBAC1G,aAAa,CAAC,CAAC,IAAI,UAAU;gBAC7B,eAAe,OAAO,CAAC,EAAE;YAC7B,OAAO;gBACH,aAAa;gBACb,eAAe;oBACX,SAAS;oBACT,UAAU,OAAO,CAAC,EAAE,CAAC,QAAQ;oBAC7B,OAAO,OAAO,CAAC,EAAE,CAAC,KAAK;gBAC3B;YACJ;YACA,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,QAAQ,aAAa,GAAG;gBAChD,IAAI,CAAC,WAAW,CAAC,MAAM;oBACnB,OAAO;oBACP,KAAK;oBACL,UAAU;oBACV,QAAQ;oBACR,aAAa;oBACb,eAAe,QAAQ,aAAa;oBACpC,QAAQ,QAAQ,MAAM;gBAC1B;YACJ;QACJ;QACA,MAAM,uBAAuB,CAAA,GAAA,oLAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC;QAC7D,MAAM,cAAc,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO,CAAC,iBAAiB,WAAW,CAAC,EAAE;YAClE,SAAS;YACT,MAAM;YACN,UAAU;YACV,OAAO;YACP,iBAAiB;YACjB,WAAW;QACf;QACA,IAAI,iBAAiB,OAAO,GAAG,GAAG;YAC9B,YAAY,OAAO,GAAG,iBAAiB,OAAO;QAClD;QACA,IAAI,IAAI,CAAC,eAAe,CAAC,iBAAiB,WAAW,GAAG,GAAG,QAAQ,aAAa,GAAG;YAC/E,IAAI,CAAC,WAAW,CAAC,MAAM;gBACnB,OAAO,IAAI,MAAM,CAAC,IAAI,UAAU,CAAC;gBACjC,KAAK;gBACL,UAAU;gBACV,QAAQ;gBACR,aAAa,iBAAiB,WAAW,GAAG;gBAC5C,eAAe,QAAQ,aAAa;gBACpC,QAAQ,QAAQ,MAAM;YAC1B;QACJ;IACJ;IACA,YAAY,MAAM,EAAE,OAAO,EAAE;QACzB,MAAM,OAAO,IAAI;QACjB,MAAM,gBAAgB,KAAK,MAAM,CAAC;QAClC,KAAK,CAAC,YAAY,QAAQ,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;YAC7B,eAAe;QACnB,GAAG;QACH,KAAK,aAAa,CAAC,QAAQ,MAAM;QACjC,KAAK,mBAAmB,CAAC,QAAQ,QAAQ,MAAM;QAC/C,IAAI,CAAC,KAAK,UAAU,EAAE;YAClB,KAAK,wBAAwB,CAAC;QAClC;IACJ;IACA,yBAAyB,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE;QACvD,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,MAAM,aAAa,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;YACtB,SAAS,QAAQ,OAAO;QAC5B,GAAG;QACH,MAAM,SAAS,IAAI,CAAC,UAAU,CAAC,KAAK;QACpC,OAAO,QAAQ,CAAC;QAChB,IAAI,CAAC,cAAc,CAAC,QAAQ,iBAAiB,YAAY,MAAM,QAAQ,MAAM;QAC7E,IAAI,CAAC,YAAY,CAAC,QAAQ,YAAY,QAAQ,GAAG;IACrD;IACA,WAAW,MAAM,EAAE,OAAO,EAAE;QACxB,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG,IAAI,CAAC,MAAM;QACf,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC;QACpC,IAAI,WAAW,IAAI,OAAO,IAAI,iBAAiB;YAC3C,IAAI,CAAC,wBAAwB,CAAC,QAAQ,SAAS;QACnD,OAAO,IAAI,CAAC,WAAW,IAAI,OAAO,IAAI,YAAY,IAAI,OAAO,KAAK,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,UAAU,KAAK,aAAa;YACzG,IAAI,CAAC,cAAc,CAAC,QAAQ,aAAa,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE;gBAC5C,SAAS,QAAQ,OAAO;YAC5B,GAAG,MAAM;QACb,OAAO;YACH,KAAK,CAAC,WAAW,QAAQ;QAC7B;IACJ;IACA,aAAa,OAAO,EAAE;QAClB,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,KAAK,CAAC,aAAa;QAClC,MAAM,yBAAyB;YAC3B,MAAM,qBAAqB,KAAK,aAAa;YAC7C,IAAI,sBAAsB,KAAK,OAAO,GAAG,OAAO,CAAC,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,IAAI,QAAQ,EAAE,MAAM,EAAE;gBAC3E,KAAK,eAAe,CAAC,MAAM,CAAC;gBAC5B,mBAAmB,kBAAkB,CAAC;YAC1C;QACJ;QACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,eAAe,KAAK;YACpC,KAAK,eAAe,CAAC;YACrB,KAAK,iBAAiB,CAAC;YACvB,KAAK,eAAe,CAAC,GAAG,CAAC;QAC7B,OAAO;YACH,KAAK,iBAAiB;QAC1B;QACA,OAAO;IACX;IACA,eAAe;QACX,MAAM,SAAS,KAAK,CAAC,aAAa,KAAK,CAAC,IAAI,EAAE;QAC9C,IAAI,IAAI,CAAC,MAAM,GAAG,WAAW,IAAI,IAAI,CAAC,MAAM,GAAG,eAAe,EAAE;YAC5D,OAAO,QAAQ,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ;QAC3C;QACA,OAAO;IACX;IACA,YAAY,MAAM,EAAE;QAChB,MAAM,WAAW,IAAI,CAAC,OAAO;QAC7B,SAAS,QAAQ,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,IAAI,CAAC,MAAM,CAAC;QAC7G,SAAS,WAAW,CAAC,YAAY,IAAI,CAAC,eAAe,CAAC,OAAO;QAC7D,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB;QACrC,MAAM,SAAS,IAAI,CAAC,YAAY,CAAC;YAC7B,QAAQ;QACZ;QACA,MAAM,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ;QAC7C,KAAK,CAAC,YAAY;QAClB,IAAI,CAAC,iBAAiB,GAAG;QACzB,OAAO;IACX;IACA,SAAS,MAAM,EAAE;QACb,OAAO,UAAU,OAAO,KAAK,IAAI,IAAI,CAAC,eAAe,CAAC,KAAK;IAC/D;IACA,gBAAgB,OAAO,EAAE;QACrB,MAAM,OAAO,IAAI;QACjB,MAAM,EACF,QAAQ,MAAM,EACjB,GAAG;QACJ,MAAM,EACF,KAAK,GAAG,EACX,GAAG;QACJ,MAAM,EACF,MAAM,IAAI,EACb,GAAG;QACJ,MAAM,eAAe,OAAO,IAAI,YAAY;QAC5C,MAAM,EACF,OAAO,KAAK,EACf,GAAG;QACJ,MAAM,eAAe,sLAAA,CAAA,UAAa,CAAC,eAAe,CAAC,QAAQ,OAAO,MAAM,IAAI,OAAO;QACnF,MAAM,aAAa,KAAK,CAAC,gBAAgB;QACzC,WAAW,KAAK,GAAG;QACnB,WAAW,QAAQ,GAAG,QAAQ,QAAQ;QACtC,WAAW,YAAY,GAAG;QAC1B,WAAW,GAAG,GAAG;QACjB,WAAW,GAAG,GAAG,IAAI,GAAG;QACxB,WAAW,IAAI,GAAG;QAClB,WAAW,OAAO,GAAG,IAAI,OAAO;QAChC,WAAW,MAAM,GAAG,IAAI,MAAM;QAC9B,WAAW,IAAI,GAAG,CAAC,OAAO,OAAO,GAAG,sLAAA,CAAA,UAAa,CAAC,WAAW,CAAC,cAAc,UAAU;QACtF,WAAW,QAAQ,GAAG,IAAI,QAAQ;QAClC,WAAW,YAAY,GAAG,gBAAgB,YAAY,CAAC,QAAQ,WAAW,CAAC;QAC3E,WAAW,OAAO,GAAG,OAAO,gBAAgB;QAC5C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,UAAU,KAAK,CAAC,OAAO,OAAO,EAAE;YACjD,MAAM,uBAAuB,KAAK,MAAM,CAAC;YACzC,MAAM,gBAAgB,KAAK,MAAM,CAAC;YAClC,IAAI,cAAc,iBAAiB,eAAe,eAAe;gBAC7D,WAAW,qBAAqB,GAAG,QAAQ,KAAK,wBAAwB,IAAI,wBAAwB,qBAAqB,qBAAqB;gBAC9I,WAAW,qBAAqB,GAAG,QAAQ,KAAK,cAAc,IAAI,wBAAwB,qBAAqB,qBAAqB;YACxI;QACJ;QACA,OAAO;IACX;IACA,kCAAkC,KAAK,EAAE,cAAc,EAAE,WAAW,EAAE,KAAK,EAAE;QACzE,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;QACjD,MAAM,UAAU,kBAAkB,UAAU;QAC5C,MAAM,SAAS,WAAW,OAAO,CAAC,YAAY;QAC9C,MAAM,WAAW,UAAU,OAAO,MAAM,IAAI,OAAO,KAAK;QACxD,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,OAAQ,CAAC,UAAU;YACpB,IAAI,CAAC,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,KAAK,QAAQ,CAAC,iBAAiB;gBAClC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;oBAC5C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa,kBAAkB,kBAAkB,CAAC,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,aAAa,cAAc,CAAC,EAAE,CAAC,KAAK,KAAK,aAAa;wBAC3I,MAAM,EAAE,CAAC,UAAU,QAAQ,GAAG,EAAE,CAAC,GAAG,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,mNAAA,CAAA,UAAkB,CAAC,eAAe,GAAG;wBAC1G,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW;4BACtB;wBACJ;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,uBAAuB;QACnB,OAAO,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,IAAI,gBAAgB;IACvC;IACA,mBAAmB;QACf,OAAO,sLAAA,CAAA,UAAa,CAAC,gBAAgB,CAAC,KAAK,CAAC,IAAI,EAAE;IACtD;IACA,eAAe,QAAQ,EAAE,gBAAgB,EAAE;QACvC,MAAM,aAAa,IAAI,CAAC,eAAe,CAAC,KAAK,EAAE,CAAC,SAAS;QACzD,IAAI;QACJ,IAAI;QACJ,IAAI,YAAY;YACZ,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,mBAAmB;gBAC5B,SAAS,IAAI,CAAC,kBAAkB,CAAC,YAAY,CAAC;YAClD,OAAO;gBACH,SAAS,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,EAAE,CAAC,iBAAiB;YAC1E;YACA,IAAI,QAAQ;gBACR,cAAc,IAAI,CAAC,eAAe,CAAC;oBAC/B,OAAO,OAAO,kBAAkB,CAAC,WAAW,IAAI;oBAChD,UAAU,WAAW,QAAQ;oBAC7B,KAAK;oBACL,QAAQ;gBACZ;YACJ;QACJ;QACA,OAAO;IACX;IACA,OAAO,KAAK,EAAE;QACV,IAAI,SAAS,GAAG;YACZ,MAAM,OAAO,IAAI,CAAC,eAAe;YACjC,IAAI,KAAK,MAAM,GAAG,OAAO;gBACrB,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,IAAI,CAAC,MAAM;YACxB;QACJ;QACA;IACJ;IACA,yBAAyB,MAAM,EAAE;QAC7B,MAAM,iBAAiB,IAAI,CAAC,eAAe;QAC3C,MAAM,YAAY,eAAe,KAAK,CAAC,MAAM,MAAM;QACnD,MAAM,iBAAiB,IAAI,CAAC,mBAAmB;QAC/C,MAAM,uBAAuB,IAAI,CAAC,wBAAwB,CAAC;QAC3D,IAAI,wBAAwB,kBAAkB,eAAe,UAAU,MAAM,GAAG;YAC5E,IAAI,wBAAwB;YAC5B,IAAI,YAAY,GAAG;gBACf,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;oBAClB,MAAM,oBAAoB,eAAe,QAAQ,KAAK;oBACtD,MAAM,gBAAgB,IAAI,CAAC,MAAM,CAAC;oBAClC,IAAI,oBAAoB,KAAK,eAAe,SAAS,KAAK,KAAK,cAAc,iBAAiB,eAAe,eAAe;wBACxH,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,sBAAsB,oBAAoB,IAAI,CAAC,UAAU;wBACnE,wBAAwB;oBAC5B;oBACA,IAAI,CAAC,yBAAyB,QAAQ;wBAClC,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,sBAAsB;oBACpC,OAAO;wBACH,qBAAqB,MAAM,CAAC;oBAChC;oBACA,IAAI,CAAC,oBAAoB,CAAC;gBAC9B,OAAO;oBACH,qBAAqB,IAAI;oBACzB,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;wBACT,MAAM,iBAAiB,IAAI,CAAC,iBAAiB,CAAC;wBAC9C,MAAM,gCAAgC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,OAAO,MAAM;wBAClE,MAAM,gBAAgB,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE;wBACrC,MAAM,mBAAmB,gCAAgC,gBAAgB;wBACzE,MAAM,aAAa,IAAI,CAAC,cAAc,CAAC,eAAe,QAAQ,GAAG,KAAK;wBACtE,MAAM,gBAAgB,UAAU,IAAI,CAAC,gBAAgB;wBACrD,MAAM,iBAAiB,KAAK,IAAI,CAAC,WAAW,cAAc,GAAG,CAAC;wBAC9D,MAAM,mBAAmB,IAAI,CAAC,oBAAoB;wBAClD,MAAM,eAAe,gCAAgC,aAAa,iBAAiB;wBACnF,IAAI,kBAAkB;4BAClB,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gCACT,qBAAqB,GAAG,CAAC,UAAU;gCACnC,wBAAwB;gCACxB,qBAAqB,IAAI;4BAC7B;wBACJ;wBACA,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG,IAAM,IAAI,CAAC,oBAAoB,CAAC;oBACjD;gBACJ;YACJ,OAAO;gBACH,qBAAqB,GAAG,CAAC,UAAU;gBACnC,qBAAqB,IAAI;gBACzB,IAAI,CAAC,oBAAoB,CAAC;YAC9B;QACJ;IACJ;IACA,uBAAuB;QACnB,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,MAAM,IAAI,IAAI,CAAC,oBAAoB,MAAM;QACxE,MAAM,iBAAiB,gKAAA,CAAA,UAAO,CAAC,MAAM,IAAI,gKAAA,CAAA,UAAO,CAAC,OAAO,IAAI;QAC5D,MAAM,oBAAoB,gKAAA,CAAA,UAAO,CAAC,OAAO,IAAI,gKAAA,CAAA,UAAO,CAAC,OAAO,IAAI,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC;QACnF,OAAO,kBAAkB,qBAAqB,iBAAiB,IAAI;IACvE;IACA,qBAAqB,CAAC,EAAE;QACpB,MAAM,EACF,aAAa,WAAW,EAC3B,GAAG;QACJ,IAAI,EAAE,WAAW,CAAC,QAAQ,EAAE;YACxB;QACJ;QACA,IAAI,YAAY,KAAK,IAAI,YAAY,YAAY,EAAE;YAC/C,KAAK,CAAC,qBAAqB;YAC3B,IAAI,CAAC,2BAA2B;QACpC;IACJ;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,mBAAmB,MAAM,EAAE;QACvB,MAAM,OAAO,IAAI;QACjB,OAAQ,OAAO,UAAU;YACrB,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,KAAK,MAAM,CAAC,MAAM;gBAClB;YACJ;gBACI,KAAK,OAAO,CAAC;QACrB;IACJ;IACA,gBAAgB;QACZ,OAAO;YAAC;YAAsB;YAAwB;YAAqB;YAAkB;YAAiB;SAAgB;IAClI;IACA,eAAe;QACX,OAAO,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,CAAC,OAAO,MAAM,IAAI,CAAC,iBAAiB;IAC5D;IACA,kBAAkB,YAAY,EAAE;QAC5B,MAAM,sBAAsB,IAAI,CAAC,oBAAoB,IAAI,IAAI,CAAC,oBAAoB,CAAC,GAAG,CAAC;QACvF,IAAI,iBAAiB;QACrB,IAAI,qBAAqB;YACrB,IAAI,CAAC,cAAc;gBACf,iBAAiB,oBAAoB,WAAW,GAAG,oBAAoB,WAAW,GAAG,oBAAoB,WAAW,GAAG;YAC3H,OAAO;gBACH,iBAAiB,oBAAoB,YAAY,GAAG,oBAAoB,YAAY,GAAG,oBAAoB,YAAY,GAAG;gBAC1H,kBAAkB,2BAA2B,IAAI;YACrD;QACJ;QACA,OAAO,iBAAiB,IAAI,iBAAiB;IACjD;IACA,8BAA8B;QAC1B,MAAM,mBAAmB,IAAI,CAAC,iBAAiB,IAAI,EAAE;QACrD,MAAM,eAAe,EAAE;QACvB,MAAM,UAAU,IAAI,CAAC,UAAU;QAC/B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACrC,YAAY,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,CAAC,YAAY;YACzC,IAAI,OAAO,CAAC,EAAE,CAAC,gBAAgB,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,CAAC,EAAE,CAAC,UAAU,KAAK,gBAAgB,CAAC,EAAE,KAAK,YAAY,CAAC,EAAE,EAAE;gBAC7G,OAAO,CAAC,EAAE,CAAC,gBAAgB,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YACpD;QACJ;QACA,IAAI,CAAC,iBAAiB,GAAG;IAC7B;IACA,qBAAqB,qBAAqB,EAAE;QACxC,IAAI,IAAI,CAAC,MAAM,CAAC,kBAAkB,CAAC,uBAAuB;YACtD,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC;QAC5B,OAAO;YACH,IAAI,CAAC,OAAO,GAAG,WAAW,CAAC;QAC/B;IACJ;IACA,oBAAoB;QAChB,MAAM,aAAa,6KAAA,CAAA,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,OAAO;QACtD,IAAI,YAAY;YACZ,WAAW,MAAM;YACjB,IAAI,WAAW,MAAM,CAAC,gBAAgB,CAAC,CAAC,SAAS,cAAc,KAAK,MAAM,cAAc,WAAW,WAAW,EAAE,GAAG;gBAC/G,IAAI,CAAC,+BAA+B;YACxC;QACJ;IACJ;IACA,kCAAkC;QAC9B,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,MAAM,aAAa,cAAc,WAAW,YAAY,GAAG,IAAI;QAC/D,MAAM,aAAa,IAAI,CAAC,MAAM,CAAC;QAC/B,IAAI,YAAY;YACZ,MAAM,4BAA4B,6BAA6B;YAC/D,MAAM,cAAc,4BAA4B;YAChD,IAAI,gBAAgB,IAAI,CAAC,YAAY,EAAE;gBACnC,IAAI,CAAC,WAAW,GAAG,4BAA4B,IAAI,CAAC,YAAY;YACpE;QACJ;QACA,IAAI,IAAI,CAAC,WAAW,IAAI,KAAK,eAAe,IAAI,CAAC,WAAW,EAAE;YAC1D,WAAW,QAAQ,CAAC;gBAChB,GAAG,IAAI,CAAC,WAAW;YACvB;QACJ;IACJ;IACA,cAAc;QACV,MAAM,OAAO,IAAI;QACjB,KAAK,2BAA2B;QAChC,KAAK,gBAAgB;QACrB,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;YACT,KAAK,iBAAiB;YACtB,KAAK,gBAAgB;YACrB,KAAK,wBAAwB;YAC7B,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gBACT,KAAK,iBAAiB;YAC1B;QACJ;IACJ;IACA,SAAS,QAAQ,EAAE;QACf,MAAM,WAAW,IAAI,CAAC,OAAO;QAC7B,MAAM,eAAe,YAAY,6KAAA,CAAA,UAAU,CAAC,WAAW,CAAC;QACxD,IAAI,cAAc;YACd,aAAa,QAAQ,CAAC;QAC1B;IACJ;IACA,OAAO,MAAM,EAAE;QACX,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,IAAI,CAAC,OAAO;QAC7B,IAAI,MAAM,UAAU,MAAM,EAAE;YACxB,OAAO,WAAW,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE,UAAU,QAAQ;QACvD;QACA,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,WAAW,UAAU;YAC/B,KAAK,SAAS,CAAC,WAAW;YAC1B,CAAA,GAAA,iLAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QACxB;IACJ;IACA,UAAU,SAAS,EAAE;QACjB,IAAI,MAAM,UAAU,MAAM,EAAE;YACxB,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU;QAC5B;QACA,IAAI,CAAC,UAAU,GAAG;QAClB;IACJ;IACA,WAAW,SAAS,EAAE,WAAW,EAAE;QAC/B,MAAM,OAAO,IAAI;QACjB,IAAI,YAAY,KAAK,UAAU;QAC/B,MAAM,iBAAiB,KAAK,eAAe;QAC3C,MAAM,mBAAmB,KAAK,MAAM,CAAC,gBAAgB,CAAC;QACtD,MAAM,YAAY,eAAe,QAAQ,KAAK,iBAAiB,SAAS,GAAG;QAC3E,MAAM,WAAW,KAAK,OAAO;QAC7B,IAAI,CAAC,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,KAAK;YACd;QACJ;QACA,IAAI,CAAC,aAAa,KAAK,MAAM,eAAe,eAAe,YAAY,MAAM,WAAW,iBAAiB,OAAO,IAAI,UAAU;YAC1H,KAAK,gBAAgB,CAAC,UAAU,SAAS,MAAM;YAC/C,YAAY,KAAK,UAAU;QAC/B;QACA,IAAI,WAAW;YACX,MAAM,oBAAoB;gBACtB,SAAS,eAAe,iBAAiB,IAAI;gBAC7C,WAAW;gBACX,SAAS;YACb;YACA,IAAI,WAAW;gBACX,kBAAkB,QAAQ,GAAG,sLAAA,CAAA,UAAa,CAAC,0BAA0B,CAAC;YAC1E;YACA,aAAa,KAAK,qBAAqB;YACvC,IAAI,UAAU,MAAM,CAAC,cAAc,CAAC,WAAW;gBAC3C,KAAK,qBAAqB,GAAG,WAAY;oBACrC,UAAU,MAAM,CAAC;gBACrB,GAAI;YACR,OAAO;gBACH,UAAU,MAAM,CAAC;YACrB;QACJ;IACJ;IACA,2BAA2B,WAAW,EAAE,KAAK,EAAE;QAC3C,MAAM,QAAQ,IAAI,CAAC,eAAe,GAAG,GAAG,CAAC,oBAAoB,EAAE;QAC/D,IAAI,CAAC,iCAAiC,CAAC,OAAO,IAAI,CAAC,UAAU,IAAI,aAAa;IAClF;IACA,qBAAqB,QAAQ,EAAE;QAC3B,MAAM,SAAS,KAAK,CAAC,qBAAqB,KAAK,CAAC,IAAI,EAAE;QACtD,IAAI,QAAQ;YACR,MAAM,iBAAiB,OAAO,MAAM,CAAC,kBAAkB,KAAK;YAC5D,IAAI,kBAAkB,KAAK,OAAO,MAAM,GAAG,iBAAiB,GAAG;gBAC3D,OAAO,OAAO,KAAK,CAAC,GAAG,iBAAiB;YAC5C;QACJ;QACA,OAAO;IACX;IACA,6BAA6B,KAAK,EAAE,OAAO,EAAE;QACzC,MAAM,OAAO,IAAI;QACjB,IAAI,YAAY;QAChB,IAAI,aAAa;QACjB,IAAI,SAAS;QACb,IAAI,mBAAmB,KAAK,UAAU;QACtC,MAAM,kBAAkB,KAAK,mBAAmB;QAChD,MAAM,0BAA0B,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;QAC/E,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK;QACxC,MAAM,eAAe,KAAK,eAAe;QACzC,IAAI,MAAM,MAAM,IAAI,cAAc;YAC9B,MAAM,cAAc,KAAK,eAAe,CAAC,cAAc,MAAM,CAAC;YAC9D,IAAI,CAAC,OAAO;gBACR,MAAM,SAAS,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,OAAO,KAAK,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD;gBACzE,oBAAoB;YACxB;YACA,IAAK,YAAY,GAAG,YAAY,MAAM,MAAM,EAAE,YAAa;gBACvD,aAAa;gBACb,MAAM,cAAc,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,aAAa,EAAE,CAAC;gBACtC,IAAI,YAAY,MAAM,EAAE;oBACpB,SAAS,YAAY,MAAM;oBAC3B,SAAS,CAAC,QAAQ,OAAO,GAAG,GAAG,OAAO,GAAG,GAAG,CAAA,GAAA,gLAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,IAAI;oBAC3E,IAAI,SAAS,kBAAkB;wBAC3B,IAAI,WAAW;4BACX,IAAI,WAAW,IAAI,mBAAmB,KAAK,KAAK,CAAC,SAAS,aAAa;gCACnE;4BACJ;wBACJ;wBACA;oBACJ;gBACJ;YACJ;YACA,IAAI,aAAa,cAAc,MAAM,MAAM,EAAE;gBACzC;YACJ;QACJ;QACA,OAAO;IACX;IACA,uBAAuB,OAAO,EAAE;QAC5B,OAAO,IAAI,CAAC,4BAA4B,CAAC,MAAM;IACnD;IACA,0BAA0B,OAAO,EAAE;QAC/B,OAAO,IAAI,CAAC,4BAA4B,CAAC,OAAO;IACpD;IACA,uBAAuB;QACnB,MAAM,YAAY,IAAI,CAAC,sBAAsB;QAC7C,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK;QACxC,IAAI,KAAK,CAAC,UAAU,EAAE;YAClB,OAAO,KAAK,CAAC,UAAU,CAAC,IAAI;QAChC;QACA;IACJ;IACA,iBAAiB,QAAQ,EAAE,MAAM,EAAE;QAC/B,MAAM,aAAa,IAAI,CAAC,aAAa;QACrC,cAAc,WAAW,eAAe,CAAC,UAAU;IACvD;IACA,cAAc,IAAI,EAAE;QAChB,MAAM,OAAO,IAAI;QACjB,KAAK,CAAC,cAAc;QACpB,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACD,KAAK,WAAW,CAAC,MAAM;gBACvB,KAAK,OAAO,GAAG;gBACf;YACJ,KAAK;gBACD,KAAK,UAAU,GAAG;gBAClB,KAAK,aAAa,GAAG;gBACrB,KAAK,OAAO,GAAG;gBACf;YACJ,KAAK;gBACD,KAAK,UAAU,GAAG;gBAClB,KAAK,aAAa,GAAG;gBACrB;YACJ,KAAK;gBACD,KAAK,aAAa,GAAG;gBACrB,KAAK,WAAW,CAAC,MAAM,wBAAwB,KAAK,QAAQ;gBAC5D,KAAK,OAAO,GAAG;gBACf;YACJ,KAAK;gBACD,KAAK,gBAAgB;gBACrB,KAAK,OAAO,GAAG;QACvB;IACJ;IACA,YAAY,aAAa,EAAE,aAAa,EAAE,OAAO,EAAE;QAC/C,MAAM,kBAAkB,IAAI,CAAC,mBAAmB;QAChD,MAAM,gBAAgB,IAAI,CAAC,eAAe;QAC1C,IAAI,SAAS,iBAAiB,KAAK,MAAM,iBAAiB,cAAc,MAAM,EAAE;gBAC/B;YAA7C,IAAI,CAAC,OAAO,CAAC,QAAQ,AAAC,GAAuB,OAArB,0BAAA,2BAAA,gBAAe,IAAG,KAAmC,OAAhC,CAAA,sBAAA,cAAc,IAAI,CAAC,mBAAnB,iCAAA,sBAA0B,IAAG,KAAqB,OAAlB,0BAAA,2BAAA,gBAAe,IAAK,IAAI,IAAI;QAC7G;IACJ;IACA,UAAU;QACN,KAAK,CAAC;QACN,aAAa,IAAI,CAAC,qBAAqB;QACvC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,OAAO;IAChD;IACA,mBAAmB,eAAe,EAAE,eAAe,EAAE,CAAC;IACtD,yBAAyB;QACrB,IAAI;QACJ,MAAM,oBAAoB,IAAI,CAAC,eAAe,CAAC;QAC/C,OAAO,SAAS,CAAC,gBAAgB,IAAI,CAAC,OAAO,EAAE,KAAK,KAAK,MAAM,gBAAgB,KAAK,IAAI,cAAc,QAAQ,CAAC,AAAC,IAAqB,OAAlB;IACvH;IACA,iBAAiB,YAAY,EAAE,CAAC;IAChC,gBAAgB,QAAQ,EAAE;QACtB,MAAM,mBAAmB,SAAS,OAAO,CAAC,AAAC,IAAoC,OAAjC,IAAI,CAAC,eAAe,CAAC;QACnE,OAAO,iBAAiB,EAAE,CAAC,IAAI,CAAC,OAAO;IAC3C;AACJ;AACO,MAAM,aAAa;IACtB,gBAAgB,IAAM,CAAC;YACnB,mBAAmB;YACnB,WAAW;gBACP,WAAW;YACf;YACA,WAAW;gBACP,SAAS;gBACT,MAAM,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;gBACjC,OAAO;gBACP,QAAQ;gBACR,eAAe;gBACf,cAAc;gBACd,UAAU;YACd;YACA,iBAAiB;YACjB,iBAAiB;YACjB,YAAY,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC;YACvC,iBAAiB;YACjB,iBAAiB;YACjB,cAAc;YACd,uBAAuB;YACvB,oBAAoB;YACpB,sBAAsB;QAC1B,CAAC;IACD,OAAO;QACH,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2374, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/views/a11y_status_container_component.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/views/a11y_status_container_component.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport $ from \"../../../../core/renderer\";\r\nconst CLASSES = {\r\n    container: \"dx-gridbase-a11y-status-container\"\r\n};\r\nexport const A11yStatusContainerComponent = _ref => {\r\n    let {\r\n        statusText: statusText\r\n    } = _ref;\r\n    return $(\"<div>\").text(statusText ?? \"\").addClass(CLASSES.container).attr(\"role\", \"status\")\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AACA,MAAM,UAAU;IACZ,WAAW;AACf;AACO,MAAM,+BAA+B,CAAA;IACxC,IAAI,EACA,YAAY,UAAU,EACzB,GAAG;IACJ,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,IAAI,CAAC,uBAAA,wBAAA,aAAc,IAAI,QAAQ,CAAC,QAAQ,SAAS,EAAE,IAAI,CAAC,QAAQ;AACtF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2397, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/__internal/grids/grid_core/views/m_grid_view.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/__internal/grids/grid_core/views/m_grid_view.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport messageLocalization from \"../../../../common/core/localization/message\";\r\nimport domAdapter from \"../../../../core/dom_adapter\";\r\nimport $ from \"../../../../core/renderer\";\r\nimport browser from \"../../../../core/utils/browser\";\r\nimport {\r\n    deferRender,\r\n    deferUpdate\r\n} from \"../../../../core/utils/common\";\r\nimport {\r\n    Deferred,\r\n    when\r\n} from \"../../../../core/utils/deferred\";\r\nimport {\r\n    each\r\n} from \"../../../../core/utils/iterator\";\r\nimport {\r\n    getBoundingRect\r\n} from \"../../../../core/utils/position\";\r\nimport {\r\n    getHeight,\r\n    getWidth\r\n} from \"../../../../core/utils/size\";\r\nimport {\r\n    isDefined,\r\n    isNumeric,\r\n    isString\r\n} from \"../../../../core/utils/type\";\r\nimport {\r\n    getWindow,\r\n    hasWindow\r\n} from \"../../../../core/utils/window\";\r\nimport * as accessibility from \"../../../../ui/shared/accessibility\";\r\nimport {\r\n    A11yStatusContainerComponent\r\n} from \"../../../grids/grid_core/views/a11y_status_container_component\";\r\nimport modules from \"../m_modules\";\r\nimport gridCoreUtils from \"../m_utils\";\r\nconst BORDERS_CLASS = \"borders\";\r\nconst TABLE_FIXED_CLASS = \"table-fixed\";\r\nconst IMPORTANT_MARGIN_CLASS = \"important-margin\";\r\nconst GRIDBASE_CONTAINER_CLASS = \"dx-gridbase-container\";\r\nconst GROUP_ROW_SELECTOR = \"tr.dx-group-row\";\r\nconst HIDDEN_COLUMNS_WIDTH = \"adaptiveHidden\";\r\nconst VIEW_NAMES = [\"columnsSeparatorView\", \"blockSeparatorView\", \"trackerView\", \"headerPanel\", \"columnHeadersView\", \"rowsView\", \"footerView\", \"columnChooserView\", \"filterPanelView\", \"pagerView\", \"draggingHeaderView\", \"contextMenuView\", \"errorView\", \"headerFilterView\", \"filterBuilderView\"];\r\nconst E2E_ATTRIBUTES = {\r\n    a11yStatusContainer: \"e2e-a11y-general-status-container\"\r\n};\r\nconst isPercentWidth = function(width) {\r\n    return isString(width) && width.endsWith(\"%\")\r\n};\r\nconst isPixelWidth = function(width) {\r\n    return isString(width) && width.endsWith(\"px\")\r\n};\r\nconst calculateFreeWidth = function(that, widths) {\r\n    const contentWidth = that._rowsView.contentWidth();\r\n    const totalWidth = that._getTotalWidth(widths, contentWidth);\r\n    return contentWidth - totalWidth\r\n};\r\nconst calculateFreeWidthWithCurrentMinWidth = function(that, columnIndex, currentMinWidth, widths) {\r\n    return calculateFreeWidth(that, widths.map(((width, index) => index === columnIndex ? currentMinWidth : width)))\r\n};\r\nconst restoreFocus = function(focusedElement, selectionRange) {\r\n    accessibility.hiddenFocus(focusedElement, true);\r\n    gridCoreUtils.setSelectionRange(focusedElement, selectionRange)\r\n};\r\nexport class ResizingController extends modules.ViewController {\r\n    callbackNames() {\r\n        return [\"resizeCompleted\"]\r\n    }\r\n    init() {\r\n        this._prevContentMinHeight = null;\r\n        this._dataController = this.getController(\"data\");\r\n        this._columnsController = this.getController(\"columns\");\r\n        this._columnHeadersView = this.getView(\"columnHeadersView\");\r\n        this._adaptiveColumnsController = this.getController(\"adaptiveColumns\");\r\n        this._editorFactoryController = this.getController(\"editorFactory\");\r\n        this._footerView = this.getView(\"footerView\");\r\n        this._rowsView = this.getView(\"rowsView\");\r\n        this._gridView = this.getView(\"gridView\")\r\n    }\r\n    _initPostRenderHandlers() {\r\n        if (!this._refreshSizesHandler) {\r\n            this._refreshSizesHandler = e => {\r\n                let resizeDeferred = (new Deferred).resolve(null);\r\n                const changeType = null === e || void 0 === e ? void 0 : e.changeType;\r\n                const isDelayed = null === e || void 0 === e ? void 0 : e.isDelayed;\r\n                const needFireContentReady = changeType && \"updateSelection\" !== changeType && \"updateFocusedRow\" !== changeType && \"pageIndex\" !== changeType && !isDelayed;\r\n                this._dataController.changed.remove(this._refreshSizesHandler);\r\n                if (this._checkSize()) {\r\n                    resizeDeferred = this._refreshSizes(e)\r\n                }\r\n                if (needFireContentReady) {\r\n                    when(resizeDeferred).done((() => {\r\n                        this._setAriaLabel(e);\r\n                        this.fireContentReadyAction()\r\n                    }))\r\n                }\r\n            };\r\n            this._dataController.changed.add((() => {\r\n                this._dataController.changed.add(this._refreshSizesHandler)\r\n            }))\r\n        }\r\n    }\r\n    _refreshSizes(e) {\r\n        let resizeDeferred = (new Deferred).resolve(null);\r\n        const changeType = null === e || void 0 === e ? void 0 : e.changeType;\r\n        const isDelayed = null === e || void 0 === e ? void 0 : e.isDelayed;\r\n        const items = this._dataController.items();\r\n        if (!e || \"refresh\" === changeType || \"prepend\" === changeType || \"append\" === changeType) {\r\n            if (!isDelayed) {\r\n                resizeDeferred = this.resize()\r\n            }\r\n        } else if (\"update\" === changeType) {\r\n            var _e$changeTypes;\r\n            if (0 === (null === (_e$changeTypes = e.changeTypes) || void 0 === _e$changeTypes ? void 0 : _e$changeTypes.length)) {\r\n                return resizeDeferred\r\n            }\r\n            if ((items.length > 1 || \"insert\" !== e.changeTypes[0]) && !(0 === items.length && \"remove\" === e.changeTypes[0]) && !e.needUpdateDimensions) {\r\n                resizeDeferred = new Deferred;\r\n                this._waitAsyncTemplates().done((() => {\r\n                    deferUpdate((() => deferRender((() => deferUpdate((() => {\r\n                        this._setScrollerSpacing();\r\n                        this._rowsView.resize();\r\n                        resizeDeferred.resolve()\r\n                    }))))))\r\n                })).fail(resizeDeferred.reject)\r\n            } else {\r\n                resizeDeferred = this.resize()\r\n            }\r\n        }\r\n        return resizeDeferred\r\n    }\r\n    fireContentReadyAction() {\r\n        this.component._fireContentReadyAction()\r\n    }\r\n    _getWidgetAriaLabel() {\r\n        return \"dxDataGrid-ariaDataGrid\"\r\n    }\r\n    _setAriaLabel(e) {\r\n        var _this$_columnsControl;\r\n        let widgetStatusText = \"\";\r\n        let labelParts = [];\r\n        const columnCount = (null === (_this$_columnsControl = this._columnsController) || void 0 === _this$_columnsControl || null === (_this$_columnsControl = _this$_columnsControl._columns) || void 0 === _this$_columnsControl ? void 0 : _this$_columnsControl.filter((_ref => {\r\n            let {\r\n                visible: visible\r\n            } = _ref;\r\n            return !!visible\r\n        })).length) ?? 0;\r\n        const totalItemsCount = Math.max(0, this._dataController.totalItemsCount());\r\n        const widgetAriaLabel = this._getWidgetAriaLabel();\r\n        widgetStatusText = messageLocalization.format(widgetAriaLabel, totalItemsCount, columnCount);\r\n        const expandableWidgetAriaLabel = messageLocalization.format(this._expandableWidgetAriaId);\r\n        labelParts = [widgetStatusText];\r\n        if (expandableWidgetAriaLabel) {\r\n            labelParts.push(expandableWidgetAriaLabel)\r\n        }\r\n        const $ariaLabelElement = this.component.$element().children(\".dx-gridbase-container\");\r\n        this.component.setAria(\"label\", labelParts.join(\". \"), $ariaLabelElement);\r\n        if (!(null !== e && void 0 !== e && e.isFirstRender)) {\r\n            this._gridView.setWidgetA11yStatusText(widgetStatusText)\r\n        }\r\n    }\r\n    _getBestFitWidths() {\r\n        var _widths;\r\n        const rowsView = this._rowsView;\r\n        const columnHeadersView = this._columnHeadersView;\r\n        let widths = rowsView.getColumnWidths();\r\n        if (!(null !== (_widths = widths) && void 0 !== _widths && _widths.length)) {\r\n            var _rowsView$getTableEle;\r\n            const headersTableElement = columnHeadersView.getTableElement();\r\n            columnHeadersView.setTableElement(null === (_rowsView$getTableEle = rowsView.getTableElement()) || void 0 === _rowsView$getTableEle ? void 0 : _rowsView$getTableEle.children(\".dx-header\"));\r\n            widths = columnHeadersView.getColumnWidths();\r\n            columnHeadersView.setTableElement(headersTableElement)\r\n        }\r\n        return widths\r\n    }\r\n    _setVisibleWidths(visibleColumns, widths) {\r\n        const columnsController = this._columnsController;\r\n        columnsController.beginUpdate();\r\n        each(visibleColumns, ((index, column) => {\r\n            const columnId = columnsController.getColumnId(column);\r\n            columnsController.columnOption(columnId, \"visibleWidth\", widths[index])\r\n        }));\r\n        columnsController.endUpdate()\r\n    }\r\n    _toggleBestFitModeForView(view, className, isBestFit) {\r\n        if (!view || !view.isVisible()) {\r\n            return\r\n        }\r\n        const $rowsTables = this._rowsView.getTableElements();\r\n        const $viewTables = view.getTableElements();\r\n        each($rowsTables, ((index, tableElement) => {\r\n            let $tableBody;\r\n            const $rowsTable = $(tableElement);\r\n            const $viewTable = $viewTables.eq(index);\r\n            if ($viewTable && $viewTable.length) {\r\n                if (isBestFit) {\r\n                    $tableBody = $viewTable.children(\"tbody\").appendTo($rowsTable)\r\n                } else {\r\n                    $tableBody = $rowsTable.children(`.${className}`).appendTo($viewTable)\r\n                }\r\n                $tableBody.toggleClass(className, isBestFit);\r\n                $tableBody.toggleClass(this.addWidgetPrefix(\"best-fit\"), isBestFit)\r\n            }\r\n        }))\r\n    }\r\n    _toggleBestFitMode(isBestFit) {\r\n        const $rowsTable = this._rowsView.getTableElement();\r\n        const $rowsFixedTable = this._rowsView.getTableElements().eq(1);\r\n        if (!$rowsTable) {\r\n            return\r\n        }\r\n        $rowsTable.css(\"tableLayout\", isBestFit ? \"auto\" : \"fixed\");\r\n        $rowsTable.children(\"colgroup\").css(\"display\", isBestFit ? \"none\" : \"\");\r\n        each($rowsFixedTable.find(\"tr.dx-group-row\"), ((idx, item) => {\r\n            $(item).css(\"display\", isBestFit ? \"none\" : \"\")\r\n        }));\r\n        $rowsFixedTable.toggleClass(this.addWidgetPrefix(\"table-fixed\"), !isBestFit);\r\n        this._toggleBestFitModeForView(this._columnHeadersView, \"dx-header\", isBestFit);\r\n        this._toggleBestFitModeForView(this._footerView, \"dx-footer\", isBestFit);\r\n        if (this._needStretch()) {\r\n            $rowsTable.get(0).style.width = isBestFit ? \"auto\" : \"\"\r\n        }\r\n    }\r\n    _toggleContentMinHeight(value) {\r\n        const scrollable = this._rowsView.getScrollable();\r\n        const $contentElement = this._rowsView._findContentElement();\r\n        if (false === (null === scrollable || void 0 === scrollable ? void 0 : scrollable.option(\"useNative\"))) {\r\n            if (true === value) {\r\n                this._prevContentMinHeight = $contentElement.get(0).style.minHeight\r\n            }\r\n            if (isDefined(this._prevContentMinHeight)) {\r\n                $contentElement.css({\r\n                    minHeight: value ? gridCoreUtils.getContentHeightLimit(browser) : this._prevContentMinHeight\r\n                })\r\n            }\r\n        }\r\n    }\r\n    _synchronizeColumns() {\r\n        const columnsController = this._columnsController;\r\n        const visibleColumns = columnsController.getVisibleColumns();\r\n        const columnAutoWidth = this.option(\"columnAutoWidth\");\r\n        const wordWrapEnabled = this.option(\"wordWrapEnabled\");\r\n        const hasUndefinedColumnWidth = visibleColumns.some((column => !isDefined(column.width)));\r\n        let needBestFit = this._needBestFit();\r\n        let hasMinWidth = false;\r\n        let resetBestFitMode;\r\n        let isColumnWidthsCorrected = false;\r\n        let resultWidths = [];\r\n        let focusedElement;\r\n        let selectionRange;\r\n        !needBestFit && each(visibleColumns, ((index, column) => {\r\n            if (\"auto\" === column.width) {\r\n                needBestFit = true;\r\n                return false\r\n            }\r\n            return\r\n        }));\r\n        each(visibleColumns, ((index, column) => {\r\n            if (column.minWidth) {\r\n                hasMinWidth = true;\r\n                return false\r\n            }\r\n            return\r\n        }));\r\n        this._setVisibleWidths(visibleColumns, []);\r\n        const $element = this.component.$element();\r\n        if (needBestFit) {\r\n            focusedElement = domAdapter.getActiveElement($element.get(0));\r\n            selectionRange = gridCoreUtils.getSelectionRange(focusedElement);\r\n            this._toggleBestFitMode(true);\r\n            resetBestFitMode = true\r\n        }\r\n        this._toggleContentMinHeight(wordWrapEnabled);\r\n        if ($element && $element.get(0) && this._maxWidth) {\r\n            delete this._maxWidth;\r\n            $element[0].style.maxWidth = \"\"\r\n        }\r\n        deferUpdate((() => {\r\n            if (needBestFit) {\r\n                resultWidths = this._getBestFitWidths();\r\n                each(visibleColumns, ((index, column) => {\r\n                    const columnId = columnsController.getColumnId(column);\r\n                    columnsController.columnOption(columnId, \"bestFitWidth\", resultWidths[index], true)\r\n                }))\r\n            } else if (hasMinWidth) {\r\n                resultWidths = this._getBestFitWidths()\r\n            }\r\n            each(visibleColumns, (function(index) {\r\n                const {\r\n                    width: width\r\n                } = this;\r\n                if (\"auto\" !== width) {\r\n                    if (isDefined(width)) {\r\n                        resultWidths[index] = isNumeric(width) || isPixelWidth(width) ? parseFloat(width) : width\r\n                    } else if (!columnAutoWidth) {\r\n                        resultWidths[index] = void 0\r\n                    }\r\n                }\r\n            }));\r\n            if (resetBestFitMode) {\r\n                this._toggleBestFitMode(false);\r\n                resetBestFitMode = false;\r\n                if (focusedElement && focusedElement !== domAdapter.getActiveElement()) {\r\n                    const isFocusOutsideWindow = getBoundingRect(focusedElement).bottom < 0;\r\n                    if (!isFocusOutsideWindow) {\r\n                        restoreFocus(focusedElement, selectionRange)\r\n                    }\r\n                }\r\n            }\r\n            isColumnWidthsCorrected = this._correctColumnWidths(resultWidths, visibleColumns);\r\n            if (columnAutoWidth) {\r\n                ! function() {\r\n                    let expandColumnWidth;\r\n                    each(visibleColumns, ((index, column) => {\r\n                        if (\"groupExpand\" === column.type) {\r\n                            expandColumnWidth = resultWidths[index]\r\n                        }\r\n                    }));\r\n                    each(visibleColumns, ((index, column) => {\r\n                        if (\"groupExpand\" === column.type && expandColumnWidth) {\r\n                            resultWidths[index] = expandColumnWidth\r\n                        }\r\n                    }))\r\n                }();\r\n                if (this._needStretch()) {\r\n                    this._processStretch(resultWidths, visibleColumns)\r\n                }\r\n            }\r\n            deferRender((() => {\r\n                if (needBestFit || isColumnWidthsCorrected || hasUndefinedColumnWidth) {\r\n                    this._setVisibleWidths(visibleColumns, resultWidths)\r\n                }\r\n                if (wordWrapEnabled) {\r\n                    this._toggleContentMinHeight(false)\r\n                }\r\n            }))\r\n        }))\r\n    }\r\n    _needBestFit() {\r\n        return this.option(\"columnAutoWidth\")\r\n    }\r\n    _needStretch() {\r\n        return this._columnsController.getVisibleColumns().some((c => \"auto\" === c.width && !c.command))\r\n    }\r\n    _getAverageColumnsWidth(resultWidths) {\r\n        const freeWidth = calculateFreeWidth(this, resultWidths);\r\n        const columnCountWithoutWidth = resultWidths.filter((width => void 0 === width)).length;\r\n        return freeWidth / columnCountWithoutWidth\r\n    }\r\n    _correctColumnWidths(resultWidths, visibleColumns) {\r\n        const that = this;\r\n        let i;\r\n        let hasPercentWidth = false;\r\n        let hasAutoWidth = false;\r\n        let isColumnWidthsCorrected = false;\r\n        const $element = that.component.$element();\r\n        const hasWidth = that._hasWidth;\r\n        for (i = 0; i < visibleColumns.length; i++) {\r\n            const index = i;\r\n            const column = visibleColumns[index];\r\n            const isHiddenColumn = \"adaptiveHidden\" === resultWidths[index];\r\n            let width = resultWidths[index];\r\n            const {\r\n                minWidth: minWidth\r\n            } = column;\r\n            if (minWidth) {\r\n                if (void 0 === width) {\r\n                    const averageColumnsWidth = that._getAverageColumnsWidth(resultWidths);\r\n                    width = averageColumnsWidth\r\n                } else if (isPercentWidth(width)) {\r\n                    const freeWidth = calculateFreeWidthWithCurrentMinWidth(that, index, minWidth, resultWidths);\r\n                    if (freeWidth < 0) {\r\n                        width = -1\r\n                    }\r\n                }\r\n            }\r\n            const realColumnWidth = that._getRealColumnWidth(index, resultWidths.map(((columnWidth, columnIndex) => index === columnIndex ? width : columnWidth)));\r\n            if (minWidth && !isHiddenColumn && realColumnWidth < minWidth) {\r\n                resultWidths[index] = minWidth;\r\n                isColumnWidthsCorrected = true;\r\n                i = -1\r\n            }\r\n            if (!isDefined(column.width)) {\r\n                hasAutoWidth = true\r\n            }\r\n            if (isPercentWidth(column.width)) {\r\n                hasPercentWidth = true\r\n            }\r\n        }\r\n        if (!hasAutoWidth && resultWidths.length) {\r\n            const $rowsViewElement = that._rowsView.element();\r\n            const contentWidth = that._rowsView.contentWidth();\r\n            const scrollbarWidth = that._rowsView.getScrollbarWidth();\r\n            const totalWidth = that._getTotalWidth(resultWidths, contentWidth);\r\n            if (totalWidth < contentWidth) {\r\n                const lastColumnIndex = gridCoreUtils.getLastResizableColumnIndex(visibleColumns, resultWidths);\r\n                if (lastColumnIndex >= 0) {\r\n                    resultWidths[lastColumnIndex] = \"auto\";\r\n                    isColumnWidthsCorrected = true;\r\n                    if (false === hasWidth && !hasPercentWidth) {\r\n                        const borderWidth = gridCoreUtils.getComponentBorderWidth(this, $rowsViewElement);\r\n                        that._maxWidth = totalWidth + scrollbarWidth + borderWidth;\r\n                        $element.css(\"maxWidth\", that._maxWidth)\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        return isColumnWidthsCorrected\r\n    }\r\n    _processStretch(resultSizes, visibleColumns) {\r\n        const groupSize = this._rowsView.contentWidth();\r\n        const tableSize = this._getTotalWidth(resultSizes, groupSize);\r\n        const unusedIndexes = {\r\n            length: 0\r\n        };\r\n        if (!resultSizes.length) {\r\n            return\r\n        }\r\n        each(visibleColumns, (function(index) {\r\n            if (this.width || \"adaptiveHidden\" === resultSizes[index]) {\r\n                unusedIndexes[index] = true;\r\n                unusedIndexes.length++\r\n            }\r\n        }));\r\n        const diff = groupSize - tableSize;\r\n        const diffElement = Math.floor(diff / (resultSizes.length - unusedIndexes.length));\r\n        let onePixelElementsCount = diff - diffElement * (resultSizes.length - unusedIndexes.length);\r\n        if (diff >= 0) {\r\n            for (let i = 0; i < resultSizes.length; i++) {\r\n                if (unusedIndexes[i]) {\r\n                    continue\r\n                }\r\n                resultSizes[i] += diffElement;\r\n                if (onePixelElementsCount > 0) {\r\n                    if (onePixelElementsCount < 1) {\r\n                        resultSizes[i] += onePixelElementsCount;\r\n                        onePixelElementsCount = 0\r\n                    } else {\r\n                        resultSizes[i]++;\r\n                        onePixelElementsCount--\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    _getRealColumnWidth(columnIndex, columnWidths, groupWidth) {\r\n        let ratio = 1;\r\n        const width = columnWidths[columnIndex];\r\n        if (!isPercentWidth(width)) {\r\n            return parseFloat(width)\r\n        }\r\n        const percentTotalWidth = columnWidths.reduce(((sum, width, index) => {\r\n            if (!isPercentWidth(width)) {\r\n                return sum\r\n            }\r\n            return sum + parseFloat(width)\r\n        }), 0);\r\n        const pixelTotalWidth = columnWidths.reduce(((sum, width) => {\r\n            if (!width || \"adaptiveHidden\" === width || isPercentWidth(width)) {\r\n                return sum\r\n            }\r\n            return sum + parseFloat(width)\r\n        }), 0);\r\n        groupWidth = groupWidth || this._rowsView.contentWidth();\r\n        const freeSpace = groupWidth - pixelTotalWidth;\r\n        const percentTotalWidthInPixel = percentTotalWidth * groupWidth / 100;\r\n        if (pixelTotalWidth > 0 && percentTotalWidthInPixel + pixelTotalWidth >= groupWidth) {\r\n            ratio = percentTotalWidthInPixel > freeSpace ? freeSpace / percentTotalWidthInPixel : 1\r\n        }\r\n        return parseFloat(width) * groupWidth * ratio / 100\r\n    }\r\n    _getTotalWidth(widths, groupWidth) {\r\n        let result = 0;\r\n        for (let i = 0; i < widths.length; i++) {\r\n            const width = widths[i];\r\n            if (width && \"adaptiveHidden\" !== width) {\r\n                result += this._getRealColumnWidth(i, widths, groupWidth)\r\n            }\r\n        }\r\n        return Math.ceil(result)\r\n    }\r\n    _getGroupElement() {\r\n        return this.component.$element().children().get(0)\r\n    }\r\n    updateSize(rootElement) {\r\n        const that = this;\r\n        const $rootElement = $(rootElement);\r\n        const importantMarginClass = that.addWidgetPrefix(\"important-margin\");\r\n        if (void 0 === that._hasHeight && $rootElement && $rootElement.is(\":visible\") && getWidth($rootElement)) {\r\n            const $groupElement = $rootElement.children(`.${that.getWidgetContainerClass()}`);\r\n            if ($groupElement.length) {\r\n                $groupElement.detach()\r\n            }\r\n            that._hasHeight = !!getHeight($rootElement);\r\n            const width = getWidth($rootElement);\r\n            $rootElement.addClass(importantMarginClass);\r\n            that._hasWidth = getWidth($rootElement) === width;\r\n            $rootElement.removeClass(importantMarginClass);\r\n            if ($groupElement.length) {\r\n                $groupElement.appendTo($rootElement)\r\n            }\r\n        }\r\n    }\r\n    publicMethods() {\r\n        return [\"resize\", \"updateDimensions\"]\r\n    }\r\n    _waitAsyncTemplates() {\r\n        var _this$_columnHeadersV, _this$_rowsView, _this$_footerView;\r\n        return when(null === (_this$_columnHeadersV = this._columnHeadersView) || void 0 === _this$_columnHeadersV ? void 0 : _this$_columnHeadersV.waitAsyncTemplates(true), null === (_this$_rowsView = this._rowsView) || void 0 === _this$_rowsView ? void 0 : _this$_rowsView.waitAsyncTemplates(true), null === (_this$_footerView = this._footerView) || void 0 === _this$_footerView ? void 0 : _this$_footerView.waitAsyncTemplates(true))\r\n    }\r\n    resize() {\r\n        if (this.component._requireResize) {\r\n            return (new Deferred).resolve()\r\n        }\r\n        const d = new Deferred;\r\n        this._waitAsyncTemplates().done((() => {\r\n            when(this.updateDimensions()).done(d.resolve).fail(d.reject)\r\n        })).fail(d.reject);\r\n        return d.promise().done((() => {\r\n            this.resizeCompleted.fire()\r\n        }))\r\n    }\r\n    updateDimensions(checkSize) {\r\n        const that = this;\r\n        that._initPostRenderHandlers();\r\n        if (!that._checkSize(checkSize)) {\r\n            return\r\n        }\r\n        const prevResult = that._resizeDeferred;\r\n        const result = that._resizeDeferred = new Deferred;\r\n        when(prevResult).always((() => {\r\n            deferRender((() => {\r\n                if (that._dataController.isLoaded()) {\r\n                    that._synchronizeColumns()\r\n                }\r\n                that._resetGroupElementHeight();\r\n                deferUpdate((() => {\r\n                    deferRender((() => {\r\n                        deferUpdate((() => {\r\n                            that._updateDimensionsCore()\r\n                        }))\r\n                    }))\r\n                }))\r\n            })).done(result.resolve).fail(result.reject)\r\n        }));\r\n        return result.promise()\r\n    }\r\n    _resetGroupElementHeight() {\r\n        const groupElement = this._getGroupElement();\r\n        const scrollable = this._rowsView.getScrollable();\r\n        if (groupElement && groupElement.style.height && (!scrollable || !scrollable.scrollTop())) {\r\n            groupElement.style.height = \"\"\r\n        }\r\n    }\r\n    _checkSize(checkSize) {\r\n        const $rootElement = this.component.$element();\r\n        const isWidgetVisible = $rootElement.is(\":visible\");\r\n        const isGridSizeChanged = this._lastWidth !== getWidth($rootElement) || this._lastHeight !== getHeight($rootElement) || this._devicePixelRatio !== getWindow().devicePixelRatio;\r\n        return isWidgetVisible && (!checkSize || isGridSizeChanged)\r\n    }\r\n    _setScrollerSpacingCore() {\r\n        const that = this;\r\n        const vScrollbarWidth = that._rowsView.getScrollbarWidth();\r\n        const hScrollbarWidth = that._rowsView.getScrollbarWidth(true);\r\n        deferRender((() => {\r\n            that._columnHeadersView && that._columnHeadersView.setScrollerSpacing(vScrollbarWidth);\r\n            that._footerView && that._footerView.setScrollerSpacing(vScrollbarWidth);\r\n            that._rowsView.setScrollerSpacing(vScrollbarWidth, hScrollbarWidth)\r\n        }))\r\n    }\r\n    _setScrollerSpacing() {\r\n        const scrollable = this._rowsView.getScrollable();\r\n        const isNativeScrolling = true === this.option(\"scrolling.useNative\");\r\n        if (!scrollable || isNativeScrolling) {\r\n            deferRender((() => {\r\n                deferUpdate((() => {\r\n                    this._setScrollerSpacingCore()\r\n                }))\r\n            }))\r\n        } else {\r\n            this._setScrollerSpacingCore()\r\n        }\r\n    }\r\n    _setAriaOwns() {\r\n        var _this$_columnHeadersV2, _this$_footerView2, _this$_rowsView2;\r\n        const headerTable = null === (_this$_columnHeadersV2 = this._columnHeadersView) || void 0 === _this$_columnHeadersV2 ? void 0 : _this$_columnHeadersV2.getTableElement();\r\n        const footerTable = null === (_this$_footerView2 = this._footerView) || void 0 === _this$_footerView2 ? void 0 : _this$_footerView2.getTableElement();\r\n        null === (_this$_rowsView2 = this._rowsView) || void 0 === _this$_rowsView2 || _this$_rowsView2.setAriaOwns(null === headerTable || void 0 === headerTable ? void 0 : headerTable.attr(\"id\"), null === footerTable || void 0 === footerTable ? void 0 : footerTable.attr(\"id\"))\r\n    }\r\n    _updateDimensionsCore() {\r\n        const that = this;\r\n        const dataController = that._dataController;\r\n        const rowsView = that._rowsView;\r\n        const $rootElement = that.component.$element();\r\n        const groupElement = this._getGroupElement();\r\n        const rootElementHeight = getHeight($rootElement);\r\n        const height = that.option(\"height\") ?? $rootElement.get(0).style.height;\r\n        const isHeightSpecified = !!height && \"auto\" !== height;\r\n        const maxHeight = parseInt($rootElement.css(\"maxHeight\"));\r\n        const maxHeightHappened = maxHeight && rootElementHeight >= maxHeight;\r\n        const isMaxHeightApplied = groupElement && groupElement.scrollHeight === groupElement.offsetHeight;\r\n        that.updateSize($rootElement);\r\n        deferRender((() => {\r\n            const hasHeight = that._hasHeight || !!maxHeight || isHeightSpecified;\r\n            rowsView.hasHeight(hasHeight);\r\n            this._setAriaOwns();\r\n            if (maxHeightHappened && !isMaxHeightApplied) {\r\n                $(groupElement).css(\"height\", maxHeight)\r\n            }\r\n            if (!dataController.isLoaded()) {\r\n                rowsView.setLoading(dataController.isLoading());\r\n                return\r\n            }\r\n            deferUpdate((() => {\r\n                that._updateLastSizes($rootElement);\r\n                that._setScrollerSpacing();\r\n                each(VIEW_NAMES, ((index, viewName) => {\r\n                    const view = that.getView(viewName);\r\n                    if (view) {\r\n                        view.resize()\r\n                    }\r\n                }));\r\n                this._editorFactoryController && this._editorFactoryController.resize()\r\n            }))\r\n        }))\r\n    }\r\n    _updateLastSizes($rootElement) {\r\n        this._lastWidth = getWidth($rootElement);\r\n        this._lastHeight = getHeight($rootElement);\r\n        this._devicePixelRatio = getWindow().devicePixelRatio\r\n    }\r\n    optionChanged(args) {\r\n        switch (args.name) {\r\n            case \"width\":\r\n            case \"height\":\r\n                this.component._renderDimensions();\r\n                this.resize();\r\n            case \"renderAsync\":\r\n                args.handled = true;\r\n                return;\r\n            default:\r\n                super.optionChanged(args)\r\n        }\r\n    }\r\n    resetLastResizeTime() {}\r\n}\r\nexport class SynchronizeScrollingController extends modules.ViewController {\r\n    _scrollChangedHandler(views, pos, viewName) {\r\n        for (let j = 0; j < views.length; j++) {\r\n            if (views[j] && views[j].name !== viewName) {\r\n                views[j].scrollTo({\r\n                    left: pos.left,\r\n                    top: pos.top\r\n                })\r\n            }\r\n        }\r\n    }\r\n    init() {\r\n        const views = [this.getView(\"columnHeadersView\"), this.getView(\"footerView\"), this.getView(\"rowsView\")];\r\n        for (let i = 0; i < views.length; i++) {\r\n            const view = views[i];\r\n            if (view) {\r\n                view.scrollChanged.add(this._scrollChangedHandler.bind(this, views))\r\n            }\r\n        }\r\n    }\r\n}\r\nexport class GridView extends modules.View {\r\n    init() {\r\n        this._resizingController = this.getController(\"resizing\");\r\n        this._dataController = this.getController(\"data\")\r\n    }\r\n    _endUpdateCore() {\r\n        if (this.component._requireResize) {\r\n            this.component._requireResize = false;\r\n            this._resizingController.resize()\r\n        }\r\n    }\r\n    getView(name) {\r\n        return this.component._views[name]\r\n    }\r\n    element() {\r\n        return this._groupElement\r\n    }\r\n    optionChanged(args) {\r\n        const that = this;\r\n        if (isDefined(that._groupElement) && \"showBorders\" === args.name) {\r\n            that._groupElement.toggleClass(that.addWidgetPrefix(\"borders\"), !!args.value);\r\n            args.handled = true\r\n        } else {\r\n            super.optionChanged(args)\r\n        }\r\n    }\r\n    _renderViews($groupElement) {\r\n        const that = this;\r\n        each(VIEW_NAMES, ((index, viewName) => {\r\n            const view = that.getView(viewName);\r\n            if (view) {\r\n                view.render($groupElement)\r\n            }\r\n        }))\r\n    }\r\n    _getTableRoleName() {\r\n        return \"group\"\r\n    }\r\n    render($rootElement) {\r\n        const isFirstRender = !this._groupElement;\r\n        const $groupElement = this._groupElement || $(\"<div>\").addClass(this.getWidgetContainerClass());\r\n        $groupElement.addClass(\"dx-gridbase-container\");\r\n        $groupElement.toggleClass(this.addWidgetPrefix(\"borders\"), !!this.option(\"showBorders\"));\r\n        this.setAria(\"role\", \"presentation\", $rootElement);\r\n        this.component.setAria(\"role\", this._getTableRoleName(), $groupElement);\r\n        this._rootElement = $rootElement || this._rootElement;\r\n        if (isFirstRender) {\r\n            this._groupElement = $groupElement;\r\n            hasWindow() && this._resizingController.updateSize($rootElement);\r\n            $groupElement.appendTo($rootElement)\r\n        }\r\n        if (!this._a11yGeneralStatusElement) {\r\n            this._a11yGeneralStatusElement = A11yStatusContainerComponent({});\r\n            this._a11yGeneralStatusElement.attr(E2E_ATTRIBUTES.a11yStatusContainer, \"true\");\r\n            $groupElement.append(this._a11yGeneralStatusElement)\r\n        }\r\n        this._renderViews($groupElement)\r\n    }\r\n    update() {\r\n        const that = this;\r\n        const $rootElement = that._rootElement;\r\n        const $groupElement = that._groupElement;\r\n        if ($rootElement && $groupElement) {\r\n            this._resizingController.resize();\r\n            if (that._dataController.isLoaded()) {\r\n                that._resizingController.fireContentReadyAction()\r\n            }\r\n        }\r\n    }\r\n    setWidgetA11yStatusText(statusText) {\r\n        var _this$_a11yGeneralSta;\r\n        null === (_this$_a11yGeneralSta = this._a11yGeneralStatusElement) || void 0 === _this$_a11yGeneralSta || _this$_a11yGeneralSta.text(statusText)\r\n    }\r\n}\r\nexport const gridViewModule = {\r\n    defaultOptions: () => ({\r\n        showBorders: false,\r\n        renderAsync: false\r\n    }),\r\n    controllers: {\r\n        resizing: ResizingController,\r\n        synchronizeScrolling: SynchronizeScrollingController\r\n    },\r\n    views: {\r\n        gridView: GridView\r\n    },\r\n    VIEW_NAMES: VIEW_NAMES\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;AACD;AACA;AACA;AACA;AACA;AAAA;AAIA;AAAA;AAIA;AAAA;AAGA;AAAA;AAGA;AAAA;AAIA;AAAA;AAKA;AAAA;AAIA;AACA;AAGA;AACA;;;;;;;;;;;;;;;;AACA,MAAM,gBAAgB;AACtB,MAAM,oBAAoB;AAC1B,MAAM,yBAAyB;AAC/B,MAAM,2BAA2B;AACjC,MAAM,qBAAqB;AAC3B,MAAM,uBAAuB;AAC7B,MAAM,aAAa;IAAC;IAAwB;IAAsB;IAAe;IAAe;IAAqB;IAAY;IAAc;IAAqB;IAAmB;IAAa;IAAsB;IAAmB;IAAa;IAAoB;CAAoB;AAClS,MAAM,iBAAiB;IACnB,qBAAqB;AACzB;AACA,MAAM,iBAAiB,SAAS,KAAK;IACjC,OAAO,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,MAAM,QAAQ,CAAC;AAC7C;AACA,MAAM,eAAe,SAAS,KAAK;IAC/B,OAAO,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,MAAM,QAAQ,CAAC;AAC7C;AACA,MAAM,qBAAqB,SAAS,IAAI,EAAE,MAAM;IAC5C,MAAM,eAAe,KAAK,SAAS,CAAC,YAAY;IAChD,MAAM,aAAa,KAAK,cAAc,CAAC,QAAQ;IAC/C,OAAO,eAAe;AAC1B;AACA,MAAM,wCAAwC,SAAS,IAAI,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM;IAC7F,OAAO,mBAAmB,MAAM,OAAO,GAAG,CAAE,CAAC,OAAO,QAAU,UAAU,cAAc,kBAAkB;AAC5G;AACA,MAAM,eAAe,SAAS,cAAc,EAAE,cAAc;IACxD,qKAAA,CAAA,cAAyB,CAAC,gBAAgB;IAC1C,sLAAA,CAAA,UAAa,CAAC,iBAAiB,CAAC,gBAAgB;AACpD;AACO,MAAM,2BAA2B,wLAAA,CAAA,UAAO,CAAC,cAAc;IAC1D,gBAAgB;QACZ,OAAO;YAAC;SAAkB;IAC9B;IACA,OAAO;QACH,IAAI,CAAC,qBAAqB,GAAG;QAC7B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;QAC1C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC7C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,OAAO,CAAC;QACvC,IAAI,CAAC,0BAA0B,GAAG,IAAI,CAAC,aAAa,CAAC;QACrD,IAAI,CAAC,wBAAwB,GAAG,IAAI,CAAC,aAAa,CAAC;QACnD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,OAAO,CAAC;QAChC,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;QAC9B,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC;IAClC;IACA,0BAA0B;QACtB,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,IAAI,CAAC,oBAAoB,GAAG,CAAA;gBACxB,IAAI,iBAAiB,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO,CAAC;gBAC5C,MAAM,aAAa,SAAS,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,UAAU;gBACrE,MAAM,YAAY,SAAS,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,SAAS;gBACnE,MAAM,uBAAuB,cAAc,sBAAsB,cAAc,uBAAuB,cAAc,gBAAgB,cAAc,CAAC;gBACnJ,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,oBAAoB;gBAC7D,IAAI,IAAI,CAAC,UAAU,IAAI;oBACnB,iBAAiB,IAAI,CAAC,aAAa,CAAC;gBACxC;gBACA,IAAI,sBAAsB;oBACtB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,IAAI,CAAE;wBACvB,IAAI,CAAC,aAAa,CAAC;wBACnB,IAAI,CAAC,sBAAsB;oBAC/B;gBACJ;YACJ;YACA,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAE;gBAC9B,IAAI,CAAC,eAAe,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,oBAAoB;YAC9D;QACJ;IACJ;IACA,cAAc,CAAC,EAAE;QACb,IAAI,iBAAiB,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO,CAAC;QAC5C,MAAM,aAAa,SAAS,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,UAAU;QACrE,MAAM,YAAY,SAAS,KAAK,KAAK,MAAM,IAAI,KAAK,IAAI,EAAE,SAAS;QACnE,MAAM,QAAQ,IAAI,CAAC,eAAe,CAAC,KAAK;QACxC,IAAI,CAAC,KAAK,cAAc,cAAc,cAAc,cAAc,aAAa,YAAY;YACvF,IAAI,CAAC,WAAW;gBACZ,iBAAiB,IAAI,CAAC,MAAM;YAChC;QACJ,OAAO,IAAI,aAAa,YAAY;YAChC,IAAI;YACJ,IAAI,MAAM,CAAC,SAAS,CAAC,iBAAiB,EAAE,WAAW,KAAK,KAAK,MAAM,iBAAiB,KAAK,IAAI,eAAe,MAAM,GAAG;gBACjH,OAAO;YACX;YACA,IAAI,CAAC,MAAM,MAAM,GAAG,KAAK,aAAa,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,CAAC,MAAM,MAAM,MAAM,IAAI,aAAa,EAAE,WAAW,CAAC,EAAE,KAAK,CAAC,EAAE,oBAAoB,EAAE;gBAC1I,iBAAiB,IAAI,oLAAA,CAAA,WAAQ;gBAC7B,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAE;oBAC7B,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG,IAAM,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG,IAAM,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gCAC/C,IAAI,CAAC,mBAAmB;gCACxB,IAAI,CAAC,SAAS,CAAC,MAAM;gCACrB,eAAe,OAAO;4BAC1B;gBACJ,GAAI,IAAI,CAAC,eAAe,MAAM;YAClC,OAAO;gBACH,iBAAiB,IAAI,CAAC,MAAM;YAChC;QACJ;QACA,OAAO;IACX;IACA,yBAAyB;QACrB,IAAI,CAAC,SAAS,CAAC,uBAAuB;IAC1C;IACA,sBAAsB;QAClB,OAAO;IACX;IACA,cAAc,CAAC,EAAE;QACb,IAAI;QACJ,IAAI,mBAAmB;QACvB,IAAI,aAAa,EAAE;YACC;QAApB,MAAM,cAAc,CAAA,OAAC,SAAS,CAAC,wBAAwB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,yBAAyB,SAAS,CAAC,wBAAwB,sBAAsB,QAAQ,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,MAAM,CAAE,CAAA;YAClQ,IAAI,EACA,SAAS,OAAO,EACnB,GAAG;YACJ,OAAO,CAAC,CAAC;QACb,GAAI,MAAM,cALU,kBAAA,OAKL;QACf,MAAM,kBAAkB,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,eAAe;QACxE,MAAM,kBAAkB,IAAI,CAAC,mBAAmB;QAChD,mBAAmB,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC,iBAAiB,iBAAiB;QAChF,MAAM,4BAA4B,iLAAA,CAAA,UAAmB,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB;QACzF,aAAa;YAAC;SAAiB;QAC/B,IAAI,2BAA2B;YAC3B,WAAW,IAAI,CAAC;QACpB;QACA,MAAM,oBAAoB,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,CAAC;QAC7D,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,SAAS,WAAW,IAAI,CAAC,OAAO;QACvD,IAAI,CAAC,CAAC,SAAS,KAAK,KAAK,MAAM,KAAK,EAAE,aAAa,GAAG;YAClD,IAAI,CAAC,SAAS,CAAC,uBAAuB,CAAC;QAC3C;IACJ;IACA,oBAAoB;QAChB,IAAI;QACJ,MAAM,WAAW,IAAI,CAAC,SAAS;QAC/B,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;QACjD,IAAI,SAAS,SAAS,eAAe;QACrC,IAAI,CAAC,CAAC,SAAS,CAAC,UAAU,MAAM,KAAK,KAAK,MAAM,WAAW,QAAQ,MAAM,GAAG;YACxE,IAAI;YACJ,MAAM,sBAAsB,kBAAkB,eAAe;YAC7D,kBAAkB,eAAe,CAAC,SAAS,CAAC,wBAAwB,SAAS,eAAe,EAAE,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,QAAQ,CAAC;YAC9K,SAAS,kBAAkB,eAAe;YAC1C,kBAAkB,eAAe,CAAC;QACtC;QACA,OAAO;IACX;IACA,kBAAkB,cAAc,EAAE,MAAM,EAAE;QACtC,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;QACjD,kBAAkB,WAAW;QAC7B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;YAC1B,MAAM,WAAW,kBAAkB,WAAW,CAAC;YAC/C,kBAAkB,YAAY,CAAC,UAAU,gBAAgB,MAAM,CAAC,MAAM;QAC1E;QACA,kBAAkB,SAAS;IAC/B;IACA,0BAA0B,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE;QAClD,IAAI,CAAC,QAAQ,CAAC,KAAK,SAAS,IAAI;YAC5B;QACJ;QACA,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC,gBAAgB;QACnD,MAAM,cAAc,KAAK,gBAAgB;QACzC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,aAAc,CAAC,OAAO;YACvB,IAAI;YACJ,MAAM,aAAa,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;YACrB,MAAM,aAAa,YAAY,EAAE,CAAC;YAClC,IAAI,cAAc,WAAW,MAAM,EAAE;gBACjC,IAAI,WAAW;oBACX,aAAa,WAAW,QAAQ,CAAC,SAAS,QAAQ,CAAC;gBACvD,OAAO;oBACH,aAAa,WAAW,QAAQ,CAAC,AAAC,IAAa,OAAV,YAAa,QAAQ,CAAC;gBAC/D;gBACA,WAAW,WAAW,CAAC,WAAW;gBAClC,WAAW,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,aAAa;YAC7D;QACJ;IACJ;IACA,mBAAmB,SAAS,EAAE;QAC1B,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,eAAe;QACjD,MAAM,kBAAkB,IAAI,CAAC,SAAS,CAAC,gBAAgB,GAAG,EAAE,CAAC;QAC7D,IAAI,CAAC,YAAY;YACb;QACJ;QACA,WAAW,GAAG,CAAC,eAAe,YAAY,SAAS;QACnD,WAAW,QAAQ,CAAC,YAAY,GAAG,CAAC,WAAW,YAAY,SAAS;QACpE,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,IAAI,CAAC,oBAAqB,CAAC,KAAK;YACjD,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,MAAM,GAAG,CAAC,WAAW,YAAY,SAAS;QAChD;QACA,gBAAgB,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC;QAClE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,kBAAkB,EAAE,aAAa;QACrE,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,WAAW,EAAE,aAAa;QAC9D,IAAI,IAAI,CAAC,YAAY,IAAI;YACrB,WAAW,GAAG,CAAC,GAAG,KAAK,CAAC,KAAK,GAAG,YAAY,SAAS;QACzD;IACJ;IACA,wBAAwB,KAAK,EAAE;QAC3B,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa;QAC/C,MAAM,kBAAkB,IAAI,CAAC,SAAS,CAAC,mBAAmB;QAC1D,IAAI,UAAU,CAAC,SAAS,cAAc,KAAK,MAAM,aAAa,KAAK,IAAI,WAAW,MAAM,CAAC,YAAY,GAAG;YACpG,IAAI,SAAS,OAAO;gBAChB,IAAI,CAAC,qBAAqB,GAAG,gBAAgB,GAAG,CAAC,GAAG,KAAK,CAAC,SAAS;YACvE;YACA,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,IAAI,CAAC,qBAAqB,GAAG;gBACvC,gBAAgB,GAAG,CAAC;oBAChB,WAAW,QAAQ,sLAAA,CAAA,UAAa,CAAC,qBAAqB,CAAC,gKAAA,CAAA,UAAO,IAAI,IAAI,CAAC,qBAAqB;gBAChG;YACJ;QACJ;IACJ;IACA,sBAAsB;QAClB,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;QACjD,MAAM,iBAAiB,kBAAkB,iBAAiB;QAC1D,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM,kBAAkB,IAAI,CAAC,MAAM,CAAC;QACpC,MAAM,0BAA0B,eAAe,IAAI,CAAE,CAAA,SAAU,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK;QACtF,IAAI,cAAc,IAAI,CAAC,YAAY;QACnC,IAAI,cAAc;QAClB,IAAI;QACJ,IAAI,0BAA0B;QAC9B,IAAI,eAAe,EAAE;QACrB,IAAI;QACJ,IAAI;QACJ,CAAC,eAAe,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;YAC1C,IAAI,WAAW,OAAO,KAAK,EAAE;gBACzB,cAAc;gBACd,OAAO;YACX;YACA;QACJ;QACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;YAC1B,IAAI,OAAO,QAAQ,EAAE;gBACjB,cAAc;gBACd,OAAO;YACX;YACA;QACJ;QACA,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,EAAE;QACzC,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,QAAQ;QACxC,IAAI,aAAa;YACb,iBAAiB,2JAAA,CAAA,UAAU,CAAC,gBAAgB,CAAC,SAAS,GAAG,CAAC;YAC1D,iBAAiB,sLAAA,CAAA,UAAa,CAAC,iBAAiB,CAAC;YACjD,IAAI,CAAC,kBAAkB,CAAC;YACxB,mBAAmB;QACvB;QACA,IAAI,CAAC,uBAAuB,CAAC;QAC7B,IAAI,YAAY,SAAS,GAAG,CAAC,MAAM,IAAI,CAAC,SAAS,EAAE;YAC/C,OAAO,IAAI,CAAC,SAAS;YACrB,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QACA,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;YACT,IAAI,aAAa;gBACb,eAAe,IAAI,CAAC,iBAAiB;gBACrC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;oBAC1B,MAAM,WAAW,kBAAkB,WAAW,CAAC;oBAC/C,kBAAkB,YAAY,CAAC,UAAU,gBAAgB,YAAY,CAAC,MAAM,EAAE;gBAClF;YACJ,OAAO,IAAI,aAAa;gBACpB,eAAe,IAAI,CAAC,iBAAiB;YACzC;YACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,SAAS,KAAK;gBAChC,MAAM,EACF,OAAO,KAAK,EACf,GAAG,IAAI;gBACR,IAAI,WAAW,OAAO;oBAClB,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;wBAClB,YAAY,CAAC,MAAM,GAAG,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,UAAU,aAAa,SAAS,WAAW,SAAS;oBACxF,OAAO,IAAI,CAAC,iBAAiB;wBACzB,YAAY,CAAC,MAAM,GAAG,KAAK;oBAC/B;gBACJ;YACJ;YACA,IAAI,kBAAkB;gBAClB,IAAI,CAAC,kBAAkB,CAAC;gBACxB,mBAAmB;gBACnB,IAAI,kBAAkB,mBAAmB,2JAAA,CAAA,UAAU,CAAC,gBAAgB,IAAI;oBACpE,MAAM,uBAAuB,CAAA,GAAA,oLAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB,MAAM,GAAG;oBACtE,IAAI,CAAC,sBAAsB;wBACvB,aAAa,gBAAgB;oBACjC;gBACJ;YACJ;YACA,0BAA0B,IAAI,CAAC,oBAAoB,CAAC,cAAc;YAClE,IAAI,iBAAiB;gBACjB,CAAE;oBACE,IAAI;oBACJ,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;wBAC1B,IAAI,kBAAkB,OAAO,IAAI,EAAE;4BAC/B,oBAAoB,YAAY,CAAC,MAAM;wBAC3C;oBACJ;oBACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,CAAC,OAAO;wBAC1B,IAAI,kBAAkB,OAAO,IAAI,IAAI,mBAAmB;4BACpD,YAAY,CAAC,MAAM,GAAG;wBAC1B;oBACJ;gBACJ;gBACA,IAAI,IAAI,CAAC,YAAY,IAAI;oBACrB,IAAI,CAAC,eAAe,CAAC,cAAc;gBACvC;YACJ;YACA,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gBACT,IAAI,eAAe,2BAA2B,yBAAyB;oBACnE,IAAI,CAAC,iBAAiB,CAAC,gBAAgB;gBAC3C;gBACA,IAAI,iBAAiB;oBACjB,IAAI,CAAC,uBAAuB,CAAC;gBACjC;YACJ;QACJ;IACJ;IACA,eAAe;QACX,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,eAAe;QACX,OAAO,IAAI,CAAC,kBAAkB,CAAC,iBAAiB,GAAG,IAAI,CAAE,CAAA,IAAK,WAAW,EAAE,KAAK,IAAI,CAAC,EAAE,OAAO;IAClG;IACA,wBAAwB,YAAY,EAAE;QAClC,MAAM,YAAY,mBAAmB,IAAI,EAAE;QAC3C,MAAM,0BAA0B,aAAa,MAAM,CAAE,CAAA,QAAS,KAAK,MAAM,OAAQ,MAAM;QACvF,OAAO,YAAY;IACvB;IACA,qBAAqB,YAAY,EAAE,cAAc,EAAE;QAC/C,MAAM,OAAO,IAAI;QACjB,IAAI;QACJ,IAAI,kBAAkB;QACtB,IAAI,eAAe;QACnB,IAAI,0BAA0B;QAC9B,MAAM,WAAW,KAAK,SAAS,CAAC,QAAQ;QACxC,MAAM,WAAW,KAAK,SAAS;QAC/B,IAAK,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YACxC,MAAM,QAAQ;YACd,MAAM,SAAS,cAAc,CAAC,MAAM;YACpC,MAAM,iBAAiB,qBAAqB,YAAY,CAAC,MAAM;YAC/D,IAAI,QAAQ,YAAY,CAAC,MAAM;YAC/B,MAAM,EACF,UAAU,QAAQ,EACrB,GAAG;YACJ,IAAI,UAAU;gBACV,IAAI,KAAK,MAAM,OAAO;oBAClB,MAAM,sBAAsB,KAAK,uBAAuB,CAAC;oBACzD,QAAQ;gBACZ,OAAO,IAAI,eAAe,QAAQ;oBAC9B,MAAM,YAAY,sCAAsC,MAAM,OAAO,UAAU;oBAC/E,IAAI,YAAY,GAAG;wBACf,QAAQ,CAAC;oBACb;gBACJ;YACJ;YACA,MAAM,kBAAkB,KAAK,mBAAmB,CAAC,OAAO,aAAa,GAAG,CAAE,CAAC,aAAa,cAAgB,UAAU,cAAc,QAAQ;YACxI,IAAI,YAAY,CAAC,kBAAkB,kBAAkB,UAAU;gBAC3D,YAAY,CAAC,MAAM,GAAG;gBACtB,0BAA0B;gBAC1B,IAAI,CAAC;YACT;YACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK,GAAG;gBAC1B,eAAe;YACnB;YACA,IAAI,eAAe,OAAO,KAAK,GAAG;gBAC9B,kBAAkB;YACtB;QACJ;QACA,IAAI,CAAC,gBAAgB,aAAa,MAAM,EAAE;YACtC,MAAM,mBAAmB,KAAK,SAAS,CAAC,OAAO;YAC/C,MAAM,eAAe,KAAK,SAAS,CAAC,YAAY;YAChD,MAAM,iBAAiB,KAAK,SAAS,CAAC,iBAAiB;YACvD,MAAM,aAAa,KAAK,cAAc,CAAC,cAAc;YACrD,IAAI,aAAa,cAAc;gBAC3B,MAAM,kBAAkB,sLAAA,CAAA,UAAa,CAAC,2BAA2B,CAAC,gBAAgB;gBAClF,IAAI,mBAAmB,GAAG;oBACtB,YAAY,CAAC,gBAAgB,GAAG;oBAChC,0BAA0B;oBAC1B,IAAI,UAAU,YAAY,CAAC,iBAAiB;wBACxC,MAAM,cAAc,sLAAA,CAAA,UAAa,CAAC,uBAAuB,CAAC,IAAI,EAAE;wBAChE,KAAK,SAAS,GAAG,aAAa,iBAAiB;wBAC/C,SAAS,GAAG,CAAC,YAAY,KAAK,SAAS;oBAC3C;gBACJ;YACJ;QACJ;QACA,OAAO;IACX;IACA,gBAAgB,WAAW,EAAE,cAAc,EAAE;QACzC,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC,YAAY;QAC7C,MAAM,YAAY,IAAI,CAAC,cAAc,CAAC,aAAa;QACnD,MAAM,gBAAgB;YAClB,QAAQ;QACZ;QACA,IAAI,CAAC,YAAY,MAAM,EAAE;YACrB;QACJ;QACA,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,gBAAiB,SAAS,KAAK;YAChC,IAAI,IAAI,CAAC,KAAK,IAAI,qBAAqB,WAAW,CAAC,MAAM,EAAE;gBACvD,aAAa,CAAC,MAAM,GAAG;gBACvB,cAAc,MAAM;YACxB;QACJ;QACA,MAAM,OAAO,YAAY;QACzB,MAAM,cAAc,KAAK,KAAK,CAAC,OAAO,CAAC,YAAY,MAAM,GAAG,cAAc,MAAM;QAChF,IAAI,wBAAwB,OAAO,cAAc,CAAC,YAAY,MAAM,GAAG,cAAc,MAAM;QAC3F,IAAI,QAAQ,GAAG;YACX,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;gBACzC,IAAI,aAAa,CAAC,EAAE,EAAE;oBAClB;gBACJ;gBACA,WAAW,CAAC,EAAE,IAAI;gBAClB,IAAI,wBAAwB,GAAG;oBAC3B,IAAI,wBAAwB,GAAG;wBAC3B,WAAW,CAAC,EAAE,IAAI;wBAClB,wBAAwB;oBAC5B,OAAO;wBACH,WAAW,CAAC,EAAE;wBACd;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,oBAAoB,WAAW,EAAE,YAAY,EAAE,UAAU,EAAE;QACvD,IAAI,QAAQ;QACZ,MAAM,QAAQ,YAAY,CAAC,YAAY;QACvC,IAAI,CAAC,eAAe,QAAQ;YACxB,OAAO,WAAW;QACtB;QACA,MAAM,oBAAoB,aAAa,MAAM,CAAE,CAAC,KAAK,OAAO;YACxD,IAAI,CAAC,eAAe,QAAQ;gBACxB,OAAO;YACX;YACA,OAAO,MAAM,WAAW;QAC5B,GAAI;QACJ,MAAM,kBAAkB,aAAa,MAAM,CAAE,CAAC,KAAK;YAC/C,IAAI,CAAC,SAAS,qBAAqB,SAAS,eAAe,QAAQ;gBAC/D,OAAO;YACX;YACA,OAAO,MAAM,WAAW;QAC5B,GAAI;QACJ,aAAa,cAAc,IAAI,CAAC,SAAS,CAAC,YAAY;QACtD,MAAM,YAAY,aAAa;QAC/B,MAAM,2BAA2B,oBAAoB,aAAa;QAClE,IAAI,kBAAkB,KAAK,2BAA2B,mBAAmB,YAAY;YACjF,QAAQ,2BAA2B,YAAY,YAAY,2BAA2B;QAC1F;QACA,OAAO,WAAW,SAAS,aAAa,QAAQ;IACpD;IACA,eAAe,MAAM,EAAE,UAAU,EAAE;QAC/B,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,IAAK;YACpC,MAAM,QAAQ,MAAM,CAAC,EAAE;YACvB,IAAI,SAAS,qBAAqB,OAAO;gBACrC,UAAU,IAAI,CAAC,mBAAmB,CAAC,GAAG,QAAQ;YAClD;QACJ;QACA,OAAO,KAAK,IAAI,CAAC;IACrB;IACA,mBAAmB;QACf,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,GAAG,QAAQ,GAAG,GAAG,CAAC;IACpD;IACA,WAAW,WAAW,EAAE;QACpB,MAAM,OAAO,IAAI;QACjB,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE;QACvB,MAAM,uBAAuB,KAAK,eAAe,CAAC;QAClD,IAAI,KAAK,MAAM,KAAK,UAAU,IAAI,gBAAgB,aAAa,EAAE,CAAC,eAAe,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;YACrG,MAAM,gBAAgB,aAAa,QAAQ,CAAC,AAAC,IAAkC,OAA/B,KAAK,uBAAuB;YAC5E,IAAI,cAAc,MAAM,EAAE;gBACtB,cAAc,MAAM;YACxB;YACA,KAAK,UAAU,GAAG,CAAC,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;YAC9B,MAAM,QAAQ,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;YACvB,aAAa,QAAQ,CAAC;YACtB,KAAK,SAAS,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB;YAC5C,aAAa,WAAW,CAAC;YACzB,IAAI,cAAc,MAAM,EAAE;gBACtB,cAAc,QAAQ,CAAC;YAC3B;QACJ;IACJ;IACA,gBAAgB;QACZ,OAAO;YAAC;YAAU;SAAmB;IACzC;IACA,sBAAsB;QAClB,IAAI,uBAAuB,iBAAiB;QAC5C,OAAO,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,SAAS,CAAC,wBAAwB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,wBAAwB,KAAK,IAAI,sBAAsB,kBAAkB,CAAC,OAAO,SAAS,CAAC,kBAAkB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,kBAAkB,KAAK,IAAI,gBAAgB,kBAAkB,CAAC,OAAO,SAAS,CAAC,oBAAoB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,oBAAoB,KAAK,IAAI,kBAAkB,kBAAkB,CAAC;IACza;IACA,SAAS;QACL,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;YAC/B,OAAO,CAAC,IAAI,oLAAA,CAAA,WAAQ,EAAE,OAAO;QACjC;QACA,MAAM,IAAI,IAAI,oLAAA,CAAA,WAAQ;QACtB,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAE;YAC7B,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,gBAAgB,IAAI,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,CAAC,EAAE,MAAM;QAC/D,GAAI,IAAI,CAAC,EAAE,MAAM;QACjB,OAAO,EAAE,OAAO,GAAG,IAAI,CAAE;YACrB,IAAI,CAAC,eAAe,CAAC,IAAI;QAC7B;IACJ;IACA,iBAAiB,SAAS,EAAE;QACxB,MAAM,OAAO,IAAI;QACjB,KAAK,uBAAuB;QAC5B,IAAI,CAAC,KAAK,UAAU,CAAC,YAAY;YAC7B;QACJ;QACA,MAAM,aAAa,KAAK,eAAe;QACvC,MAAM,SAAS,KAAK,eAAe,GAAG,IAAI,oLAAA,CAAA,WAAQ;QAClD,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,YAAY,MAAM,CAAE;YACrB,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gBACT,IAAI,KAAK,eAAe,CAAC,QAAQ,IAAI;oBACjC,KAAK,mBAAmB;gBAC5B;gBACA,KAAK,wBAAwB;gBAC7B,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;oBACT,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;wBACT,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;4BACT,KAAK,qBAAqB;wBAC9B;oBACJ;gBACJ;YACJ,GAAI,IAAI,CAAC,OAAO,OAAO,EAAE,IAAI,CAAC,OAAO,MAAM;QAC/C;QACA,OAAO,OAAO,OAAO;IACzB;IACA,2BAA2B;QACvB,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa;QAC/C,IAAI,gBAAgB,aAAa,KAAK,CAAC,MAAM,IAAI,CAAC,CAAC,cAAc,CAAC,WAAW,SAAS,EAAE,GAAG;YACvF,aAAa,KAAK,CAAC,MAAM,GAAG;QAChC;IACJ;IACA,WAAW,SAAS,EAAE;QAClB,MAAM,eAAe,IAAI,CAAC,SAAS,CAAC,QAAQ;QAC5C,MAAM,kBAAkB,aAAa,EAAE,CAAC;QACxC,MAAM,oBAAoB,IAAI,CAAC,UAAU,KAAK,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,IAAI,CAAC,WAAW,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB,IAAI,CAAC,iBAAiB,KAAK,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,IAAI,gBAAgB;QAC/K,OAAO,mBAAmB,CAAC,CAAC,aAAa,iBAAiB;IAC9D;IACA,0BAA0B;QACtB,MAAM,OAAO,IAAI;QACjB,MAAM,kBAAkB,KAAK,SAAS,CAAC,iBAAiB;QACxD,MAAM,kBAAkB,KAAK,SAAS,CAAC,iBAAiB,CAAC;QACzD,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;YACT,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,kBAAkB,CAAC;YACtE,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,kBAAkB,CAAC;YACxD,KAAK,SAAS,CAAC,kBAAkB,CAAC,iBAAiB;QACvD;IACJ;IACA,sBAAsB;QAClB,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa;QAC/C,MAAM,oBAAoB,SAAS,IAAI,CAAC,MAAM,CAAC;QAC/C,IAAI,CAAC,cAAc,mBAAmB;YAClC,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gBACT,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;oBACT,IAAI,CAAC,uBAAuB;gBAChC;YACJ;QACJ,OAAO;YACH,IAAI,CAAC,uBAAuB;QAChC;IACJ;IACA,eAAe;QACX,IAAI,wBAAwB,oBAAoB;QAChD,MAAM,cAAc,SAAS,CAAC,yBAAyB,IAAI,CAAC,kBAAkB,KAAK,KAAK,MAAM,yBAAyB,KAAK,IAAI,uBAAuB,eAAe;QACtK,MAAM,cAAc,SAAS,CAAC,qBAAqB,IAAI,CAAC,WAAW,KAAK,KAAK,MAAM,qBAAqB,KAAK,IAAI,mBAAmB,eAAe;QACnJ,SAAS,CAAC,mBAAmB,IAAI,CAAC,SAAS,KAAK,KAAK,MAAM,oBAAoB,iBAAiB,WAAW,CAAC,SAAS,eAAe,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,IAAI,CAAC,OAAO,SAAS,eAAe,KAAK,MAAM,cAAc,KAAK,IAAI,YAAY,IAAI,CAAC;IAC7Q;IACA,wBAAwB;QACpB,MAAM,OAAO,IAAI;QACjB,MAAM,iBAAiB,KAAK,eAAe;QAC3C,MAAM,WAAW,KAAK,SAAS;QAC/B,MAAM,eAAe,KAAK,SAAS,CAAC,QAAQ;QAC5C,MAAM,eAAe,IAAI,CAAC,gBAAgB;QAC1C,MAAM,oBAAoB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;YACrB;QAAf,MAAM,SAAS,CAAA,eAAA,KAAK,MAAM,CAAC,uBAAZ,0BAAA,eAAyB,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,MAAM;QACxE,MAAM,oBAAoB,CAAC,CAAC,UAAU,WAAW;QACjD,MAAM,YAAY,SAAS,aAAa,GAAG,CAAC;QAC5C,MAAM,oBAAoB,aAAa,qBAAqB;QAC5D,MAAM,qBAAqB,gBAAgB,aAAa,YAAY,KAAK,aAAa,YAAY;QAClG,KAAK,UAAU,CAAC;QAChB,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;YACT,MAAM,YAAY,KAAK,UAAU,IAAI,CAAC,CAAC,aAAa;YACpD,SAAS,SAAS,CAAC;YACnB,IAAI,CAAC,YAAY;YACjB,IAAI,qBAAqB,CAAC,oBAAoB;gBAC1C,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,cAAc,GAAG,CAAC,UAAU;YAClC;YACA,IAAI,CAAC,eAAe,QAAQ,IAAI;gBAC5B,SAAS,UAAU,CAAC,eAAe,SAAS;gBAC5C;YACJ;YACA,CAAA,GAAA,kLAAA,CAAA,cAAW,AAAD,EAAG;gBACT,KAAK,gBAAgB,CAAC;gBACtB,KAAK,mBAAmB;gBACxB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,YAAa,CAAC,OAAO;oBACtB,MAAM,OAAO,KAAK,OAAO,CAAC;oBAC1B,IAAI,MAAM;wBACN,KAAK,MAAM;oBACf;gBACJ;gBACA,IAAI,CAAC,wBAAwB,IAAI,IAAI,CAAC,wBAAwB,CAAC,MAAM;YACzE;QACJ;IACJ;IACA,iBAAiB,YAAY,EAAE;QAC3B,IAAI,CAAC,UAAU,GAAG,CAAA,GAAA,gLAAA,CAAA,WAAQ,AAAD,EAAE;QAC3B,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE;QAC7B,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,IAAI,gBAAgB;IACzD;IACA,cAAc,IAAI,EAAE;QAChB,OAAQ,KAAK,IAAI;YACb,KAAK;YACL,KAAK;gBACD,IAAI,CAAC,SAAS,CAAC,iBAAiB;gBAChC,IAAI,CAAC,MAAM;YACf,KAAK;gBACD,KAAK,OAAO,GAAG;gBACf;YACJ;gBACI,KAAK,CAAC,cAAc;QAC5B;IACJ;IACA,sBAAsB,CAAC;AAC3B;AACO,MAAM,uCAAuC,wLAAA,CAAA,UAAO,CAAC,cAAc;IACtE,sBAAsB,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE;QACxC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,CAAC,IAAI,KAAK,UAAU;gBACxC,KAAK,CAAC,EAAE,CAAC,QAAQ,CAAC;oBACd,MAAM,IAAI,IAAI;oBACd,KAAK,IAAI,GAAG;gBAChB;YACJ;QACJ;IACJ;IACA,OAAO;QACH,MAAM,QAAQ;YAAC,IAAI,CAAC,OAAO,CAAC;YAAsB,IAAI,CAAC,OAAO,CAAC;YAAe,IAAI,CAAC,OAAO,CAAC;SAAY;QACvG,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,MAAM,OAAO,KAAK,CAAC,EAAE;YACrB,IAAI,MAAM;gBACN,KAAK,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,IAAI,EAAE;YACjE;QACJ;IACJ;AACJ;AACO,MAAM,iBAAiB,wLAAA,CAAA,UAAO,CAAC,IAAI;IACtC,OAAO;QACH,IAAI,CAAC,mBAAmB,GAAG,IAAI,CAAC,aAAa,CAAC;QAC9C,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,aAAa,CAAC;IAC9C;IACA,iBAAiB;QACb,IAAI,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE;YAC/B,IAAI,CAAC,SAAS,CAAC,cAAc,GAAG;YAChC,IAAI,CAAC,mBAAmB,CAAC,MAAM;QACnC;IACJ;IACA,QAAQ,IAAI,EAAE;QACV,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,KAAK;IACtC;IACA,UAAU;QACN,OAAO,IAAI,CAAC,aAAa;IAC7B;IACA,cAAc,IAAI,EAAE;QAChB,MAAM,OAAO,IAAI;QACjB,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,aAAa,KAAK,kBAAkB,KAAK,IAAI,EAAE;YAC9D,KAAK,aAAa,CAAC,WAAW,CAAC,KAAK,eAAe,CAAC,YAAY,CAAC,CAAC,KAAK,KAAK;YAC5E,KAAK,OAAO,GAAG;QACnB,OAAO;YACH,KAAK,CAAC,cAAc;QACxB;IACJ;IACA,aAAa,aAAa,EAAE;QACxB,MAAM,OAAO,IAAI;QACjB,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,YAAa,CAAC,OAAO;YACtB,MAAM,OAAO,KAAK,OAAO,CAAC;YAC1B,IAAI,MAAM;gBACN,KAAK,MAAM,CAAC;YAChB;QACJ;IACJ;IACA,oBAAoB;QAChB,OAAO;IACX;IACA,OAAO,YAAY,EAAE;QACjB,MAAM,gBAAgB,CAAC,IAAI,CAAC,aAAa;QACzC,MAAM,gBAAgB,IAAI,CAAC,aAAa,IAAI,CAAA,GAAA,wJAAA,CAAA,UAAC,AAAD,EAAE,SAAS,QAAQ,CAAC,IAAI,CAAC,uBAAuB;QAC5F,cAAc,QAAQ,CAAC;QACvB,cAAc,WAAW,CAAC,IAAI,CAAC,eAAe,CAAC,YAAY,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;QACzE,IAAI,CAAC,OAAO,CAAC,QAAQ,gBAAgB;QACrC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,QAAQ,IAAI,CAAC,iBAAiB,IAAI;QACzD,IAAI,CAAC,YAAY,GAAG,gBAAgB,IAAI,CAAC,YAAY;QACrD,IAAI,eAAe;YACf,IAAI,CAAC,aAAa,GAAG;YACrB,CAAA,GAAA,kLAAA,CAAA,YAAS,AAAD,OAAO,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC;YACnD,cAAc,QAAQ,CAAC;QAC3B;QACA,IAAI,CAAC,IAAI,CAAC,yBAAyB,EAAE;YACjC,IAAI,CAAC,yBAAyB,GAAG,CAAA,GAAA,uNAAA,CAAA,+BAA4B,AAAD,EAAE,CAAC;YAC/D,IAAI,CAAC,yBAAyB,CAAC,IAAI,CAAC,eAAe,mBAAmB,EAAE;YACxE,cAAc,MAAM,CAAC,IAAI,CAAC,yBAAyB;QACvD;QACA,IAAI,CAAC,YAAY,CAAC;IACtB;IACA,SAAS;QACL,MAAM,OAAO,IAAI;QACjB,MAAM,eAAe,KAAK,YAAY;QACtC,MAAM,gBAAgB,KAAK,aAAa;QACxC,IAAI,gBAAgB,eAAe;YAC/B,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC/B,IAAI,KAAK,eAAe,CAAC,QAAQ,IAAI;gBACjC,KAAK,mBAAmB,CAAC,sBAAsB;YACnD;QACJ;IACJ;IACA,wBAAwB,UAAU,EAAE;QAChC,IAAI;QACJ,SAAS,CAAC,wBAAwB,IAAI,CAAC,yBAAyB,KAAK,KAAK,MAAM,yBAAyB,sBAAsB,IAAI,CAAC;IACxI;AACJ;AACO,MAAM,iBAAiB;IAC1B,gBAAgB,IAAM,CAAC;YACnB,aAAa;YACb,aAAa;QACjB,CAAC;IACD,aAAa;QACT,UAAU;QACV,sBAAsB;IAC1B;IACA,OAAO;QACH,UAAU;IACd;IACA,YAAY;AAChB", "ignoreList": [0], "debugId": null}}]}