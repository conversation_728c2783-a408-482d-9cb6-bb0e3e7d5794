(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/contants.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "BLACK": ()=>BLACK,
    "BOTTOM": ()=>BOTTOM,
    "CENTER": ()=>CENTER,
    "GREY_GREEN": ()=>GREY_GREEN,
    "INSIDE": ()=>INSIDE,
    "LEFT": ()=>LEFT,
    "LIGHT_GREY": ()=>LIGHT_GREY,
    "NONE": ()=>NONE,
    "OUTSIDE": ()=>OUTSIDE,
    "PRIMARY_TITLE_COLOR": ()=>PRIMARY_TITLE_COLOR,
    "RED": ()=>RED,
    "RIGHT": ()=>RIGHT,
    "SECONDARY_TITLE_COLOR": ()=>SECONDARY_TITLE_COLOR,
    "SOLID": ()=>SOLID,
    "SOME_GREY": ()=>SOME_GREY,
    "TOP": ()=>TOP,
    "WHITE": ()=>WHITE
});
const WHITE = "#ffffff";
const BLACK = "#000000";
const LIGHT_GREY = "#d3d3d3";
const GREY_GREEN = "#303030";
const SOME_GREY = "#2b2b2b";
const RED = "#ff0000";
const PRIMARY_TITLE_COLOR = "#232323";
const SECONDARY_TITLE_COLOR = "#767676";
const NONE = "none";
const SOLID = "solid";
const TOP = "top";
const RIGHT = "right";
const BOTTOM = "bottom";
const LEFT = "left";
const CENTER = "center";
const INSIDE = "inside";
const OUTSIDE = "outside";
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/bar_gauge.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/bar_gauge.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    barGauge: {
        backgroundColor: "#e0e0e0",
        relativeInnerRadius: .3,
        barSpacing: 4,
        resolveLabelOverlapping: "hide",
        label: {
            indent: 20,
            connectorWidth: 2,
            font: {
                size: 16
            }
        },
        legend: {
            visible: false
        },
        indicator: {
            hasPositiveMeaning: true,
            layout: {
                horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"]
            },
            text: {
                font: {
                    size: 18
                }
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/bullet.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/bullet.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const __TURBOPACK__default__export__ = {
    bullet: {
        color: "#e8c267",
        targetColor: "#666666",
        targetWidth: 4,
        showTarget: true,
        showZeroLevel: true,
        tooltip: {
            enabled: true
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    "chart:common": {
        animation: {
            enabled: true,
            duration: 1e3,
            easing: "easeOutCubic",
            maxPointCountSupported: 300
        },
        commonSeriesSettings: {
            border: {
                visible: false,
                width: 2
            },
            showInLegend: true,
            visible: true,
            hoverMode: "nearestPoint",
            selectionMode: "includePoints",
            hoverStyle: {
                hatching: {
                    direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                    width: 2,
                    step: 6,
                    opacity: .75
                },
                highlight: true,
                border: {
                    visible: false,
                    width: 3
                }
            },
            selectionStyle: {
                hatching: {
                    direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                    width: 2,
                    step: 6,
                    opacity: .5
                },
                highlight: true,
                border: {
                    visible: false,
                    width: 3
                }
            },
            valueErrorBar: {
                displayMode: "auto",
                value: 1,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
                lineWidth: 2,
                edgeLength: 8
            },
            label: {
                visible: false,
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                rotationAngle: 0,
                horizontalOffset: 0,
                verticalOffset: 0,
                radialOffset: 0,
                showForZeroValues: true,
                customizeText: void 0,
                maxLabelCount: void 0,
                position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTSIDE"],
                font: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
                },
                border: {
                    visible: false,
                    width: 1,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LIGHT_GREY"],
                    dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"]
                },
                connector: {
                    visible: false,
                    width: 1
                }
            }
        },
        seriesSelectionMode: "single",
        pointSelectionMode: "single",
        equalRowHeight: true,
        dataPrepareSettings: {
            checkTypeForAllData: false,
            convertToAxisDataType: true,
            sortingMethod: true
        },
        title: {
            margin: 10
        },
        adaptiveLayout: {
            width: 80,
            height: 80,
            keepLabels: true
        },
        _rtl: {
            legend: {
                itemTextPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"]
            }
        },
        resolveLabelOverlapping: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
    },
    "chart:common:axis": {
        visible: true,
        valueMarginsEnabled: true,
        placeholderSize: null,
        logarithmBase: 10,
        discreteAxisDivisionMode: "betweenLabels",
        aggregatedPointsPosition: "betweenTicks",
        width: 1,
        label: {
            visible: true
        },
        grid: {
            visible: false,
            width: 1
        },
        minorGrid: {
            visible: false,
            width: 1,
            opacity: .3
        },
        tick: {
            visible: true,
            width: 1,
            length: 7,
            shift: 3
        },
        minorTick: {
            visible: false,
            width: 1,
            opacity: .3,
            length: 7,
            shift: 3
        },
        stripStyle: {
            paddingLeftRight: 10,
            paddingTopBottom: 5
        },
        constantLineStyle: {
            width: 1,
            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
            dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
            label: {
                visible: true,
                position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSIDE"]
            }
        },
        marker: {
            label: {}
        }
    },
    "chart:common:annotation": {
        font: {
            color: "#333333"
        },
        tooltipEnabled: true,
        border: {
            width: 1,
            color: "#dddddd",
            dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
            visible: true
        },
        color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
        opacity: .9,
        arrowLength: 14,
        arrowWidth: 14,
        paddingLeftRight: 10,
        paddingTopBottom: 10,
        shadow: {
            opacity: .15,
            offsetX: 0,
            offsetY: 1,
            blur: 4,
            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"]
        },
        image: {
            width: 30,
            height: 30
        },
        wordWrap: "normal",
        textOverflow: "ellipsis",
        allowDragging: false
    },
    chart: {
        commonSeriesSettings: {
            type: "line",
            stack: "default",
            aggregation: {
                enabled: void 0
            },
            point: {
                visible: true,
                symbol: "circle",
                size: 12,
                border: {
                    visible: false,
                    width: 1
                },
                hoverMode: "onlyPoint",
                selectionMode: "onlyPoint",
                hoverStyle: {
                    border: {
                        visible: true,
                        width: 4
                    }
                },
                selectionStyle: {
                    border: {
                        visible: true,
                        width: 4
                    }
                }
            },
            scatter: {},
            line: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            stackedline: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            stackedspline: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            fullstackedline: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            fullstackedspline: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            stepline: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            area: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            stackedarea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            fullstackedarea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            fullstackedsplinearea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            steparea: {
                border: {
                    visible: true,
                    width: 2
                },
                point: {
                    visible: false
                },
                hoverStyle: {
                    border: {
                        visible: true,
                        width: 3
                    }
                },
                selectionStyle: {
                    border: {
                        visible: true,
                        width: 3
                    }
                },
                opacity: .5
            },
            spline: {
                width: 2,
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3
                }
            },
            splinearea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            stackedsplinearea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            bar: {
                cornerRadius: 0,
                point: {
                    hoverStyle: {
                        border: {
                            visible: false
                        }
                    },
                    selectionStyle: {
                        border: {
                            visible: false
                        }
                    }
                }
            },
            stackedbar: {
                cornerRadius: 0,
                point: {
                    hoverStyle: {
                        border: {
                            visible: false
                        }
                    },
                    selectionStyle: {
                        border: {
                            visible: false
                        }
                    }
                },
                label: {
                    position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSIDE"]
                }
            },
            fullstackedbar: {
                cornerRadius: 0,
                point: {
                    hoverStyle: {
                        border: {
                            visible: false
                        }
                    },
                    selectionStyle: {
                        border: {
                            visible: false
                        }
                    }
                },
                label: {
                    position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSIDE"]
                }
            },
            rangebar: {
                cornerRadius: 0,
                point: {
                    hoverStyle: {
                        border: {
                            visible: false
                        }
                    },
                    selectionStyle: {
                        border: {
                            visible: false
                        }
                    }
                }
            },
            rangearea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            rangesplinearea: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            bubble: {
                opacity: .5,
                point: {
                    hoverStyle: {
                        border: {
                            visible: false
                        }
                    },
                    selectionStyle: {
                        border: {
                            visible: false
                        }
                    }
                }
            },
            candlestick: {
                width: 1,
                reduction: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RED"]
                },
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3,
                    highlight: false
                },
                point: {
                    border: {
                        visible: true
                    }
                }
            },
            stock: {
                width: 1,
                reduction: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RED"]
                },
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    },
                    highlight: false
                },
                selectionStyle: {
                    width: 3,
                    highlight: false
                },
                point: {
                    border: {
                        visible: true
                    }
                }
            }
        },
        crosshair: {
            enabled: false,
            color: "#f05b41",
            width: 1,
            dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
            label: {
                visible: false,
                font: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                    size: 12
                }
            },
            verticalLine: {
                visible: true
            },
            horizontalLine: {
                visible: true
            }
        },
        commonAxisSettings: {
            multipleAxesSpacing: 5,
            forceUserTickInterval: false,
            breakStyle: {
                width: 5,
                color: "#ababab",
                line: "waved"
            },
            label: {
                displayMode: "standard",
                overlappingBehavior: "hide",
                indentFromAxis: 10,
                wordWrap: "normal",
                textOverflow: "none"
            },
            title: {
                font: {
                    size: 16
                },
                margin: 6,
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"]
            },
            constantLineStyle: {
                paddingLeftRight: 10,
                paddingTopBottom: 10
            }
        },
        horizontalAxis: {
            position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"],
            axisDivisionFactor: 70,
            label: {
                rotationAngle: 90,
                staggeringSpacing: 5,
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"]
            },
            stripStyle: {
                label: {
                    horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                    verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"]
                }
            },
            constantLineStyle: {
                label: {
                    horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                    verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"]
                }
            },
            constantLines: []
        },
        verticalAxis: {
            position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"],
            axisDivisionFactor: 40,
            label: {
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"]
            },
            stripStyle: {
                label: {
                    horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"],
                    verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"]
                }
            },
            constantLineStyle: {
                label: {
                    horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"],
                    verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"]
                }
            },
            constantLines: []
        },
        argumentAxis: {
            endOnTick: false,
            workWeek: [
                1,
                2,
                3,
                4,
                5
            ]
        },
        valueAxis: {
            grid: {
                visible: true
            },
            autoBreaksEnabled: false,
            maxAutoBreakCount: 4
        },
        commonPaneSettings: {
            backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"],
            border: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LIGHT_GREY"],
                width: 1,
                visible: false,
                top: true,
                bottom: true,
                left: true,
                right: true,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"]
            }
        },
        scrollBar: {
            visible: false,
            offset: 5,
            color: "gray",
            width: 10
        },
        adjustOnZoom: true,
        autoHidePointMarkers: true,
        rotated: false,
        synchronizeMultiAxes: true,
        stickyHovering: true,
        barGroupPadding: .3,
        minBubbleSize: 12,
        maxBubbleSize: .2,
        zoomAndPan: {
            dragBoxStyle: {
                color: "#2a2a2a",
                opacity: .2
            },
            panKey: "shift",
            allowMouseWheel: true,
            allowTouchGestures: true
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/funnel.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/funnel.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    funnel: {
        sortData: true,
        valueField: "val",
        colorField: "color",
        argumentField: "arg",
        hoverEnabled: true,
        selectionMode: "single",
        item: {
            border: {
                visible: false,
                width: 2,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
            },
            hoverStyle: {
                hatching: {
                    opacity: .75,
                    step: 6,
                    width: 2,
                    direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"]
                },
                border: {}
            },
            selectionStyle: {
                hatching: {
                    opacity: .5,
                    step: 6,
                    width: 2,
                    direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"]
                },
                border: {}
            }
        },
        title: {
            margin: 10
        },
        adaptiveLayout: {
            width: 80,
            height: 80,
            keepLabels: true
        },
        legend: {
            visible: false
        },
        _rtl: {
            legend: {
                itemTextPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"]
            }
        },
        tooltip: {
            customizeTooltip: (info)=>({
                    text: "".concat(info.item.argument, " ").concat(info.valueText)
                })
        },
        inverted: false,
        algorithm: "dynamicSlope",
        neckWidth: 0,
        neckHeight: 0,
        resolveLabelOverlapping: "shift",
        label: {
            textOverflow: "ellipsis",
            wordWrap: "normal",
            visible: true,
            horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
            horizontalOffset: 0,
            verticalOffset: 0,
            showForZeroValues: false,
            customizeText: (info)=>"".concat(info.item.argument, " ").concat(info.valueText),
            position: "columns",
            font: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
            },
            border: {
                visible: false,
                width: 1,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LIGHT_GREY"],
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"]
            },
            connector: {
                visible: true,
                width: 1,
                opacity: .5
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/gauge.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/gauge.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    gauge: {
        scale: {
            tick: {
                visible: true,
                length: 5,
                width: 2,
                opacity: 1
            },
            minorTick: {
                visible: false,
                length: 3,
                width: 1,
                opacity: 1
            },
            label: {
                visible: true,
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                hideFirstOrLast: "last",
                overlappingBehavior: "hide"
            },
            position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"],
            endOnTick: false
        },
        rangeContainer: {
            offset: 0,
            width: 5,
            backgroundColor: "#808080"
        },
        valueIndicators: {
            _default: {
                color: "#c2c2c2"
            },
            rangebar: {
                space: 2,
                size: 10,
                color: "#cbc5cf",
                backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"],
                text: {
                    indent: 0,
                    font: {
                        size: 14,
                        color: null
                    }
                }
            },
            twocolorneedle: {
                secondColor: "#e18e92"
            },
            trianglemarker: {
                space: 2,
                length: 14,
                width: 13,
                color: "#8798a5"
            },
            textcloud: {
                arrowLength: 5,
                horizontalOffset: 6,
                verticalOffset: 3,
                color: "#679ec5",
                text: {
                    font: {
                        color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                        size: 18
                    }
                }
            }
        },
        indicator: {
            hasPositiveMeaning: true,
            layout: {
                horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"]
            },
            text: {
                font: {
                    size: 18
                }
            }
        },
        _circular: {
            scale: {
                scaleDivisionFactor: 17,
                orientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTSIDE"],
                label: {
                    indentFromTick: 10
                }
            },
            rangeContainer: {
                orientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTSIDE"]
            },
            valueIndicatorType: "rectangleneedle",
            subvalueIndicatorType: "trianglemarker",
            valueIndicators: {
                _type: "rectangleneedle",
                _default: {
                    offset: 20,
                    indentFromCenter: 0,
                    width: 2,
                    spindleSize: 14,
                    spindleGapSize: 10,
                    beginAdaptingAtRadius: 50
                },
                triangleneedle: {
                    width: 4
                },
                twocolorneedle: {
                    space: 2,
                    secondFraction: .4
                },
                rangebar: {
                    offset: 30
                },
                trianglemarker: {
                    offset: 6
                },
                textcloud: {
                    offset: -6
                }
            }
        },
        _linear: {
            scale: {
                scaleDivisionFactor: 25,
                horizontalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                verticalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"],
                label: {
                    indentFromTick: -10
                }
            },
            rangeContainer: {
                horizontalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                verticalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"]
            },
            valueIndicatorType: "rangebar",
            subvalueIndicatorType: "trianglemarker",
            valueIndicators: {
                _type: "rectangle",
                _default: {
                    offset: 2.5,
                    length: 15,
                    width: 15
                },
                rectangle: {
                    width: 10
                },
                rangebar: {
                    offset: 10,
                    horizontalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                    verticalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"]
                },
                trianglemarker: {
                    offset: 10,
                    horizontalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"],
                    verticalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"]
                },
                textcloud: {
                    offset: -1,
                    horizontalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"],
                    verticalOrientation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"]
                }
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/pie_chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/pie_chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    pie: {
        innerRadius: .5,
        minDiameter: .5,
        type: "pie",
        dataPrepareSettings: {
            _skipArgumentSorting: true
        },
        commonSeriesSettings: {
            pie: {
                border: {
                    visible: false,
                    width: 2,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
                },
                hoverStyle: {
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                        width: 4,
                        step: 10,
                        opacity: .75
                    },
                    highlight: true,
                    border: {
                        visible: false,
                        width: 2
                    }
                },
                selectionStyle: {
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                        width: 4,
                        step: 10,
                        opacity: .5
                    },
                    highlight: true,
                    border: {
                        visible: false,
                        width: 2
                    }
                }
            },
            doughnut: {
                border: {
                    visible: false,
                    width: 2,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
                },
                hoverStyle: {
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                        width: 4,
                        step: 10,
                        opacity: .75
                    },
                    highlight: true,
                    border: {
                        visible: false,
                        width: 2
                    }
                },
                selectionStyle: {
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                        width: 4,
                        step: 10,
                        opacity: .5
                    },
                    highlight: true,
                    border: {
                        visible: false,
                        width: 2
                    }
                }
            },
            donut: {
                border: {
                    visible: false,
                    width: 2,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
                },
                hoverStyle: {
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                        width: 4,
                        step: 10,
                        opacity: .75
                    },
                    highlight: true,
                    border: {
                        visible: false,
                        width: 2
                    }
                },
                selectionStyle: {
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                        width: 4,
                        step: 10,
                        opacity: .5
                    },
                    highlight: true,
                    border: {
                        visible: false,
                        width: 2
                    }
                }
            },
            label: {
                textOverflow: "ellipsis",
                wordWrap: "normal"
            }
        },
        legend: {
            hoverMode: "allArgumentPoints",
            backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
        },
        adaptiveLayout: {
            keepLabels: false
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/polar_chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/polar_chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    polar: {
        commonSeriesSettings: {
            type: "scatter",
            closed: true,
            point: {
                visible: true,
                symbol: "circle",
                size: 12,
                border: {
                    visible: false,
                    width: 1
                },
                hoverMode: "onlyPoint",
                selectionMode: "onlyPoint",
                hoverStyle: {
                    border: {
                        visible: true,
                        width: 4
                    },
                    size: 12
                },
                selectionStyle: {
                    border: {
                        visible: true,
                        width: 4
                    },
                    size: 12
                }
            },
            scatter: {},
            line: {
                width: 2,
                dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                hoverStyle: {
                    width: 3,
                    hatching: {
                        direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NONE"]
                    }
                },
                selectionStyle: {
                    width: 3
                }
            },
            area: {
                point: {
                    visible: false
                },
                opacity: .5
            },
            stackedline: {
                width: 2
            },
            bar: {
                opacity: .8
            },
            stackedbar: {
                opacity: .8
            }
        },
        adaptiveLayout: {
            width: 80,
            height: 80,
            keepLabels: true
        },
        barGroupPadding: .3,
        commonAxisSettings: {
            visible: true,
            forceUserTickInterval: false,
            label: {
                overlappingBehavior: "hide",
                indentFromAxis: 5
            },
            grid: {
                visible: true
            },
            minorGrid: {
                visible: true
            },
            tick: {
                visible: true
            },
            title: {
                font: {
                    size: 16
                },
                margin: 10
            }
        },
        argumentAxis: {
            startAngle: 0,
            firstPointOnStartAngle: false,
            period: void 0
        },
        valueAxis: {
            endOnTick: false,
            tick: {
                visible: false
            }
        },
        horizontalAxis: {
            position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"],
            axisDivisionFactor: 50,
            label: {
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"]
            }
        },
        verticalAxis: {
            position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"],
            axisDivisionFactor: 30,
            label: {
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"]
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/range_selector.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/range_selector.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    rangeSelector: {
        scale: {
            valueMarginsEnabled: true,
            width: 1,
            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
            opacity: .1,
            showCustomBoundaryTicks: true,
            label: {
                overlappingBehavior: "hide",
                alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                visible: true,
                topIndent: 7,
                font: {
                    size: 11
                }
            },
            tick: {
                width: 1,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
                opacity: .17,
                visible: true,
                length: 12
            },
            minorTick: {
                width: 1,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
                opacity: .05,
                visible: true,
                length: 12
            },
            marker: {
                width: 1,
                color: "#000000",
                opacity: .1,
                visible: true,
                separatorHeight: 33,
                topIndent: 10,
                textLeftIndent: 7,
                textTopIndent: 11,
                label: {}
            },
            logarithmBase: 10,
            workWeek: [
                1,
                2,
                3,
                4,
                5
            ],
            breakStyle: {
                width: 5,
                color: "#ababab",
                line: "waved"
            },
            endOnTick: false
        },
        selectedRangeColor: "#606060",
        sliderMarker: {
            visible: true,
            paddingTopBottom: 2,
            paddingLeftRight: 4,
            color: "#606060",
            invalidRangeColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RED"],
            font: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                size: 11
            }
        },
        sliderHandle: {
            width: 1,
            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
            opacity: .2
        },
        shutter: {
            opacity: .75
        },
        background: {
            color: "#c0bae1",
            visible: true,
            image: {
                location: "full"
            }
        },
        behavior: {
            snapToTicks: true,
            animationEnabled: true,
            moveSelectedRangeByClick: true,
            manualRangeSelectionEnabled: true,
            allowSlidersSwap: true,
            valueChangeMode: "onHandleRelease"
        },
        redrawOnResize: true,
        chart: {
            barGroupPadding: .3,
            minBubbleSize: 12,
            maxBubbleSize: .2,
            topIndent: .1,
            bottomIndent: 0,
            valueAxis: {
                inverted: false,
                logarithmBase: 10
            },
            commonSeriesSettings: {
                type: "area",
                aggregation: {
                    enabled: void 0
                },
                point: {
                    visible: false
                },
                scatter: {
                    point: {
                        visible: true
                    }
                }
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/sankey.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/sankey.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    sankey: {
        sourceField: "source",
        targetField: "target",
        weightField: "weight",
        hoverEnabled: true,
        alignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
        adaptiveLayout: {
            width: 80,
            height: 80,
            keepLabels: true
        },
        label: {
            visible: true,
            horizontalOffset: 8,
            verticalOffset: 0,
            overlappingBehavior: "ellipsis",
            useNodeColors: false,
            font: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
                weight: 500
            },
            border: {
                visible: false,
                width: 2,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
            },
            customizeText: (info)=>info.label,
            shadow: {
                opacity: .2,
                offsetX: 0,
                offsetY: 1,
                blur: 1,
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
            }
        },
        title: {
            margin: 10,
            font: {
                size: 28,
                weight: 200
            },
            subtitle: {
                font: {
                    size: 16
                }
            }
        },
        tooltip: {
            enabled: true
        },
        node: {
            padding: 30,
            width: 8,
            opacity: 1,
            border: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                width: 1,
                visible: false
            },
            hoverStyle: {
                hatching: {
                    opacity: .75,
                    step: 6,
                    width: 2,
                    direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"]
                },
                border: {}
            }
        },
        link: {
            color: "#888888",
            colorMode: "none",
            opacity: .3,
            border: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                width: 1,
                visible: false
            },
            hoverStyle: {
                opacity: .5,
                hatching: {
                    opacity: .75,
                    step: 6,
                    width: 2,
                    direction: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"]
                },
                border: {}
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/sparkline.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/sparkline.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    sparkline: {
        lineColor: "#666666",
        lineWidth: 2,
        areaOpacity: .2,
        minColor: "#e8c267",
        maxColor: "#e55253",
        barPositiveColor: "#a9a9a9",
        barNegativeColor: "#d7d7d7",
        winColor: "#a9a9a9",
        lossColor: "#d7d7d7",
        firstLastColor: "#666666",
        pointSymbol: "circle",
        pointColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
        pointSize: 4,
        type: "line",
        argumentField: "arg",
        valueField: "val",
        winlossThreshold: 0,
        showFirstLast: true,
        showMinMax: false,
        tooltip: {
            enabled: true
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/tree_map.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/tree_map.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    treeMap: {
        tile: {
            border: {
                width: 1,
                opacity: .2,
                color: "#000000"
            },
            color: "#5f8b95",
            hoverStyle: {
                hatching: {
                    opacity: .75,
                    step: 6,
                    width: 2,
                    direction: "right"
                },
                border: {}
            },
            selectionStyle: {
                hatching: {
                    opacity: .5,
                    step: 6,
                    width: 2,
                    direction: "right"
                },
                border: {
                    opacity: 1
                }
            },
            label: {
                visible: true,
                paddingLeftRight: 5,
                paddingTopBottom: 4,
                font: {
                    color: "#ffffff",
                    weight: 600
                },
                shadow: {
                    opacity: .6,
                    offsetX: 0,
                    offsetY: 1,
                    blur: 2,
                    color: "#000000"
                },
                wordWrap: "normal",
                textOverflow: "ellipsis"
            }
        },
        group: {
            padding: 4,
            border: {
                width: 1
            },
            color: "#eeeeee",
            hoverStyle: {
                hatching: {
                    opacity: 0,
                    step: 6,
                    width: 2,
                    direction: "right"
                },
                border: {}
            },
            selectionStyle: {
                hatching: {
                    opacity: 0,
                    step: 6,
                    width: 2,
                    direction: "right"
                },
                border: {}
            },
            label: {
                visible: true,
                paddingLeftRight: 5,
                paddingTopBottom: 4,
                font: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SECONDARY_TITLE_COLOR"],
                    weight: 600
                },
                textOverflow: "ellipsis"
            }
        },
        title: {
            subtitle: {}
        },
        tooltip: {},
        loadingIndicator: {}
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/vector_map.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/vector_map.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
;
const __TURBOPACK__default__export__ = {
    map: {
        title: {
            margin: 10
        },
        background: {
            borderWidth: 1,
            borderColor: "#cacaca"
        },
        layer: {
            label: {
                enabled: false,
                stroke: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                "stroke-width": 1,
                "stroke-opacity": .7,
                font: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOME_GREY"],
                    size: 12
                }
            }
        },
        "layer:area": {
            borderWidth: 1,
            borderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
            color: "#d2d2d2",
            hoveredBorderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GREY_GREEN"],
            selectedBorderWidth: 2,
            selectedBorderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GREY_GREEN"],
            label: {
                "stroke-width": 2,
                font: {
                    size: 16
                }
            }
        },
        "layer:line": {
            borderWidth: 2,
            color: "#ba8365",
            hoveredColor: "#a94813",
            selectedBorderWidth: 3,
            selectedColor: "#e55100",
            label: {
                "stroke-width": 2,
                font: {
                    size: 16
                }
            }
        },
        "layer:marker": {
            label: {
                enabled: true,
                "stroke-width": 1,
                font: {
                    size: 12
                }
            }
        },
        "layer:marker:dot": {
            borderWidth: 2,
            borderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
            size: 8,
            selectedStep: 2,
            backStep: 18,
            backColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
            backOpacity: .32,
            shadow: true
        },
        "layer:marker:bubble": {
            minSize: 20,
            maxSize: 50,
            hoveredBorderWidth: 1,
            hoveredBorderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GREY_GREEN"],
            selectedBorderWidth: 2,
            selectedBorderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GREY_GREEN"]
        },
        "layer:marker:pie": {
            size: 50,
            hoveredBorderWidth: 1,
            hoveredBorderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GREY_GREEN"],
            selectedBorderWidth: 2,
            selectedBorderColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["GREY_GREEN"]
        },
        "layer:marker:image": {
            size: 20
        },
        legend: {
            verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BOTTOM"],
            horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
            position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["INSIDE"],
            backgroundOpacity: .65,
            border: {
                visible: true
            },
            paddingLeftRight: 16,
            paddingTopBottom: 12
        },
        controlBar: {
            borderColor: "#5d5d5d",
            borderWidth: 3,
            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
            margin: 20,
            opacity: .3
        },
        _rtl: {
            legend: {
                itemTextPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LEFT"]
            }
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/light/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$bar_gauge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/bar_gauge.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$bullet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/bullet.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/chart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/contants.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/funnel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$gauge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/gauge.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$pie_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/pie_chart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$polar_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/polar_chart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$range_selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/range_selector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$sankey$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/sankey.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$sparkline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/sparkline.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$tree_map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/tree_map.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$vector_map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/light/vector_map.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: void 0,
        theme: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
            name: "generic.light",
            isDefault: true,
            font: {
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SECONDARY_TITLE_COLOR"],
                family: "'Segoe UI', 'Helvetica Neue', 'Trebuchet MS', Verdana, sans-serif",
                weight: 400,
                size: 12,
                cursor: "default"
            },
            redrawOnResize: true,
            backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
            primaryTitleColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIMARY_TITLE_COLOR"],
            secondaryTitleColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SECONDARY_TITLE_COLOR"],
            gridColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LIGHT_GREY"],
            axisColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SECONDARY_TITLE_COLOR"],
            title: {
                backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                font: {
                    size: 28,
                    family: "'Segoe UI Light', 'Helvetica Neue Light', 'Segoe UI', 'Helvetica Neue', 'Trebuchet MS', Verdana, sans-serif",
                    weight: 200
                },
                subtitle: {
                    font: {
                        size: 16
                    },
                    offset: 0,
                    wordWrap: "normal",
                    textOverflow: "ellipsis"
                },
                wordWrap: "normal",
                textOverflow: "ellipsis"
            },
            loadingIndicator: {
                text: "Loading..."
            },
            export: {
                backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                margin: 10,
                font: {
                    size: 14,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIMARY_TITLE_COLOR"],
                    weight: 400
                },
                button: {
                    margin: {
                        top: 8,
                        left: 10,
                        right: 10,
                        bottom: 8
                    },
                    default: {
                        color: "#333",
                        borderColor: "#ddd",
                        backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"]
                    },
                    hover: {
                        color: "#333",
                        borderColor: "#bebebe",
                        backgroundColor: "#e6e6e6"
                    },
                    focus: {
                        color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"],
                        borderColor: "#9d9d9d",
                        backgroundColor: "#e6e6e6"
                    },
                    active: {
                        color: "#333",
                        borderColor: "#9d9d9d",
                        backgroundColor: "#d4d4d4"
                    }
                },
                shadowColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LIGHT_GREY"]
            },
            tooltip: {
                enabled: false,
                border: {
                    width: 1,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LIGHT_GREY"],
                    dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"],
                    visible: true
                },
                font: {
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PRIMARY_TITLE_COLOR"]
                },
                color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                arrowLength: 10,
                paddingLeftRight: 18,
                paddingTopBottom: 15,
                textAlignment: "center",
                shared: false,
                location: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CENTER"],
                shadow: {
                    opacity: .4,
                    offsetX: 0,
                    offsetY: 4,
                    blur: 2,
                    color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BLACK"]
                },
                interactive: false
            },
            legend: {
                hoverMode: "includePoints",
                verticalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TOP"],
                horizontalAlignment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RIGHT"],
                position: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OUTSIDE"],
                visible: true,
                margin: 10,
                markerSize: 12,
                border: {
                    visible: false,
                    width: 1,
                    cornerRadius: 0,
                    dashStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SOLID"]
                },
                paddingLeftRight: 20,
                paddingTopBottom: 15,
                columnCount: 0,
                rowCount: 0,
                columnItemSpacing: 20,
                rowItemSpacing: 8,
                title: {
                    backgroundColor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$contants$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["WHITE"],
                    margin: {
                        left: 0,
                        bottom: 9,
                        right: 0,
                        top: 0
                    },
                    font: {
                        size: 18,
                        weight: 200
                    },
                    subtitle: {
                        offset: 0,
                        font: {
                            size: 14
                        },
                        wordWrap: "none",
                        textOverflow: "ellipsis"
                    },
                    wordWrap: "none",
                    textOverflow: "ellipsis"
                }
            }
        }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$funnel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$gauge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$bar_gauge$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$vector_map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$pie_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$polar_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$range_selector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$sankey$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$sparkline$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$bullet$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$themes$2f$generic$2f$light$2f$tree_map$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])
    },
    {
        baseThemeName: "generic.light",
        theme: {
            name: "generic.light.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/carmine.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/carmine.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const ACCENT_COLOR = "#f05b41";
const BACKGROUND_COLOR = "#fff";
const TITLE_COLOR = "#333";
const SUBTITLE_COLOR = "#8899a8";
const TEXT_COLOR = "#707070";
const BORDER_COLOR = "#dee1e3";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.light",
        theme: {
            name: "generic.carmine",
            defaultPalette: "Carmine",
            backgroundColor: "#fff",
            primaryTitleColor: "#333",
            secondaryTitleColor: "#8899a8",
            gridColor: "#dee1e3",
            axisColor: "#707070",
            export: {
                backgroundColor: "#fff",
                font: {
                    color: "#333"
                },
                button: {
                    default: {
                        color: "#333",
                        borderColor: "#b1b7bd",
                        backgroundColor: "#fff"
                    },
                    hover: {
                        color: "#333",
                        borderColor: "#b1b7bd",
                        backgroundColor: "#faf2f0"
                    },
                    focus: {
                        color: "#333",
                        borderColor: "#6d7781",
                        backgroundColor: "#faf2f0"
                    },
                    active: {
                        color: "#333",
                        borderColor: "#6d7781",
                        backgroundColor: "#f5e7e4"
                    }
                }
            },
            legend: {
                font: {
                    color: "#707070"
                }
            },
            tooltip: {
                color: "#fff",
                border: {
                    color: "#dee1e3"
                },
                font: {
                    color: "#333"
                }
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#dee1e3"
                        }
                    }
                }
            },
            "chart:common:annotation": {
                font: {
                    color: "#333"
                },
                border: {
                    color: "#dee1e3"
                },
                color: "#fff"
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#dee1e3"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#c1c5c7"
                    }
                }
            },
            rangeSelector: {
                scale: {
                    breakStyle: {
                        color: "#c1c5c7"
                    },
                    tick: {
                        opacity: .12
                    }
                },
                selectedRangeColor: "#f05b41",
                sliderMarker: {
                    color: "#f05b41"
                },
                sliderHandle: {
                    color: "#f05b41",
                    opacity: .5
                }
            },
            sparkline: {
                pointColor: "#fff",
                minColor: "#f0ad4e",
                maxColor: "#f74d61"
            },
            treeMap: {
                group: {
                    color: "#dee1e3",
                    label: {
                        font: {
                            color: "#8899a8"
                        }
                    }
                }
            },
            bullet: {
                color: "#f05b41"
            },
            gauge: {
                valueIndicators: {
                    rangebar: {
                        color: "#f05b41"
                    },
                    textcloud: {
                        color: "#f05b41"
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.carmine",
        theme: {
            name: "generic.carmine.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/dark.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/dark.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const WHITE = "#ffffff";
const BLACK = "#000000";
const SOME_GREY = "#2b2b2b";
const RANGE_COLOR = "#b5b5b5";
const GREY_GREEN = "#303030";
const AREA_LAYER_COLOR = "#686868";
const LINE_COLOR = "#c7c7c7";
const TARGET_COLOR = "#8e8e8e";
const POSITIVE_COLOR = "#b8b8b8";
const BORDER_COLOR = "#494949";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.light",
        theme: {
            name: "generic.dark",
            font: {
                color: "#808080"
            },
            backgroundColor: "#2a2a2a",
            primaryTitleColor: "#dedede",
            secondaryTitleColor: "#a3a3a3",
            gridColor: "#555555",
            axisColor: "#a3a3a3",
            export: {
                backgroundColor: "#2a2a2a",
                font: {
                    color: "#dbdbdb"
                },
                button: {
                    default: {
                        color: "#dedede",
                        borderColor: "#4d4d4d",
                        backgroundColor: "#2e2e2e"
                    },
                    hover: {
                        color: "#dedede",
                        borderColor: "#6c6c6c",
                        backgroundColor: "#444"
                    },
                    focus: {
                        color: "#dedede",
                        borderColor: "#8d8d8d",
                        backgroundColor: "#444444"
                    },
                    active: {
                        color: "#dedede",
                        borderColor: "#8d8d8d",
                        backgroundColor: "#555555"
                    }
                },
                shadowColor: "#292929"
            },
            tooltip: {
                color: "#2b2b2b",
                border: {
                    color: "#494949"
                },
                font: {
                    color: "#929292"
                }
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#494949"
                        }
                    },
                    valueErrorBar: {
                        color: WHITE
                    }
                }
            },
            "chart:common:axis": {
                constantLineStyle: {
                    color: WHITE
                }
            },
            "chart:common:annotation": {
                font: {
                    color: "#929292"
                },
                border: {
                    color: "#494949"
                },
                color: "#2b2b2b",
                shadow: {
                    opacity: .008,
                    offsetY: 4,
                    blur: 8
                }
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#494949"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#818181"
                    }
                },
                zoomAndPan: {
                    dragBoxStyle: {
                        color: WHITE
                    }
                }
            },
            gauge: {
                rangeContainer: {
                    backgroundColor: "#b5b5b5"
                },
                valueIndicators: {
                    _default: {
                        color: "#b5b5b5"
                    },
                    rangebar: {
                        color: "#84788b"
                    },
                    twocolorneedle: {
                        secondColor: "#ba544d"
                    },
                    trianglemarker: {
                        color: "#b7918f"
                    },
                    textcloud: {
                        color: "#ba544d"
                    }
                }
            },
            barGauge: {
                backgroundColor: "#3c3c3c"
            },
            rangeSelector: {
                scale: {
                    tick: {
                        color: WHITE,
                        opacity: .32
                    },
                    minorTick: {
                        color: WHITE,
                        opacity: .1
                    },
                    breakStyle: {
                        color: "#818181"
                    }
                },
                selectedRangeColor: "#b5b5b5",
                sliderMarker: {
                    color: "#b5b5b5",
                    font: {
                        color: "#303030"
                    }
                },
                sliderHandle: {
                    color: WHITE,
                    opacity: .2
                },
                shutter: {
                    color: "#2b2b2b",
                    opacity: .9
                }
            },
            map: {
                background: {
                    borderColor: "#3f3f3f"
                },
                layer: {
                    label: {
                        stroke: BLACK,
                        font: {
                            color: WHITE
                        }
                    }
                },
                "layer:area": {
                    borderColor: "#303030",
                    color: "#686868",
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                "layer:line": {
                    color: "#c77244",
                    hoveredColor: "#ff5d04",
                    selectedColor: "#ff784f"
                },
                "layer:marker:bubble": {
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                "layer:marker:pie": {
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                legend: {
                    border: {
                        color: "#3f3f3f"
                    },
                    font: {
                        color: WHITE
                    }
                },
                controlBar: {
                    borderColor: "#c7c7c7",
                    color: "#303030"
                }
            },
            treeMap: {
                group: {
                    color: "#4c4c4c",
                    label: {
                        font: {
                            color: "#a3a3a3"
                        }
                    }
                }
            },
            sparkline: {
                lineColor: "#c7c7c7",
                firstLastColor: "#c7c7c7",
                barPositiveColor: "#b8b8b8",
                barNegativeColor: "#8e8e8e",
                winColor: "#b8b8b8",
                lossColor: "#8e8e8e",
                pointColor: "#303030"
            },
            bullet: {
                targetColor: "#8e8e8e"
            },
            funnel: {
                item: {
                    border: {
                        color: "#2a2a2a"
                    }
                }
            },
            sankey: {
                label: {
                    font: {
                        color: WHITE
                    },
                    shadow: {
                        opacity: 0
                    }
                },
                node: {
                    border: {
                        color: "#2a2a2a"
                    }
                },
                link: {
                    color: "#888888",
                    border: {
                        color: "#2a2a2a"
                    },
                    hoverStyle: {
                        color: "#bbbbbb"
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.dark",
        theme: {
            name: "generic.dark.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/contrast.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/contrast.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const WHITE = "#ffffff";
const BLACK = "#000000";
const CONTRAST_ACTIVE = "#cf00da";
const MARKER_COLOR = "#f8ca00";
const AREA_LAYER_COLOR = "#686868";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.light",
        theme: {
            name: "generic.contrast",
            defaultPalette: "Bright",
            font: {
                color: WHITE
            },
            backgroundColor: BLACK,
            primaryTitleColor: WHITE,
            secondaryTitleColor: WHITE,
            gridColor: WHITE,
            axisColor: WHITE,
            export: {
                backgroundColor: BLACK,
                font: {
                    color: WHITE
                },
                button: {
                    default: {
                        color: WHITE,
                        borderColor: WHITE,
                        backgroundColor: BLACK
                    },
                    hover: {
                        color: WHITE,
                        borderColor: WHITE,
                        backgroundColor: "#cf00d7"
                    },
                    focus: {
                        color: WHITE,
                        borderColor: "#cf00d7",
                        backgroundColor: BLACK
                    },
                    active: {
                        color: BLACK,
                        borderColor: WHITE,
                        backgroundColor: WHITE
                    }
                },
                borderColor: WHITE,
                menuButtonColor: BLACK,
                activeBackgroundColor: WHITE,
                activeColor: BLACK,
                selectedBorderColor: "#cf00da",
                selectedColor: "#cf00da",
                shadowColor: "none"
            },
            tooltip: {
                border: {
                    color: WHITE
                },
                font: {
                    color: WHITE
                },
                color: BLACK
            },
            "chart:common": {
                commonSeriesSettings: {
                    valueErrorBar: {
                        color: WHITE
                    },
                    hoverStyle: {
                        hatching: {
                            opacity: .5
                        }
                    },
                    selectionStyle: {
                        hatching: {
                            opacity: .35
                        }
                    },
                    label: {
                        font: {
                            color: WHITE
                        },
                        border: {
                            color: WHITE
                        }
                    }
                }
            },
            "chart:common:axis": {
                constantLineStyle: {
                    color: WHITE
                }
            },
            "chart:common:annotation": {
                font: {
                    color: WHITE
                },
                border: {
                    color: WHITE
                },
                color: BLACK
            },
            chart: {
                commonSeriesSettings: {},
                crosshair: {
                    color: "#cf00d7"
                },
                commonPaneSettings: {
                    backgroundColor: BLACK,
                    border: {
                        color: WHITE
                    }
                },
                scrollBar: {
                    color: WHITE
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#cf00d7"
                    }
                },
                zoomAndPan: {
                    dragBoxStyle: {
                        color: WHITE,
                        opacity: .7
                    }
                }
            },
            pie: {
                commonSeriesSettings: {
                    pie: {
                        hoverStyle: {
                            hatching: {
                                opacity: .5
                            }
                        },
                        selectionStyle: {
                            hatching: {
                                opacity: .35
                            }
                        }
                    },
                    doughnut: {
                        hoverStyle: {
                            hatching: {
                                opacity: .5
                            }
                        },
                        selectionStyle: {
                            hatching: {
                                opacity: .35
                            }
                        }
                    },
                    donut: {
                        hoverStyle: {
                            hatching: {
                                opacity: .5
                            }
                        },
                        selectionStyle: {
                            hatching: {
                                opacity: .35
                            }
                        }
                    }
                }
            },
            gauge: {
                rangeContainer: {
                    backgroundColor: WHITE
                },
                valueIndicators: {
                    _default: {
                        color: WHITE
                    },
                    rangebar: {
                        color: WHITE,
                        backgroundColor: BLACK
                    },
                    twocolorneedle: {
                        secondColor: WHITE
                    },
                    trianglemarker: {
                        color: WHITE
                    },
                    textcloud: {
                        color: WHITE,
                        text: {
                            font: {
                                color: BLACK
                            }
                        }
                    }
                }
            },
            barGauge: {
                backgroundColor: "#3c3c3c"
            },
            rangeSelector: {
                scale: {
                    tick: {
                        color: WHITE,
                        opacity: .4
                    },
                    minorTick: {
                        color: WHITE,
                        opacity: .12
                    },
                    breakStyle: {
                        color: "#cf00d7"
                    }
                },
                selectedRangeColor: "#cf00da",
                sliderMarker: {
                    color: "#cf00da"
                },
                sliderHandle: {
                    color: "#cf00da",
                    opacity: 1
                },
                shutter: {
                    opacity: .75
                },
                background: {
                    color: BLACK
                }
            },
            map: {
                background: {
                    borderColor: WHITE
                },
                layer: {
                    label: {
                        stroke: BLACK,
                        font: {
                            color: WHITE
                        }
                    }
                },
                "layer:area": {
                    borderColor: BLACK,
                    color: "#686868",
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE,
                    label: {
                        font: {
                            opacity: 1
                        }
                    }
                },
                "layer:line": {
                    color: "#267cff",
                    hoveredColor: "#f613ff",
                    selectedColor: WHITE
                },
                "layer:marker:dot": {
                    borderColor: BLACK,
                    color: "#f8ca00",
                    backColor: BLACK,
                    backOpacity: .32
                },
                "layer:marker:bubble": {
                    color: "#f8ca00",
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                "layer:marker:pie": {
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                controlBar: {
                    borderColor: WHITE,
                    color: BLACK,
                    opacity: .3
                }
            },
            treeMap: {
                tile: {
                    color: "#70c92f"
                },
                group: {
                    color: "#797979",
                    label: {
                        font: {
                            color: WHITE
                        }
                    }
                }
            },
            sparkline: {
                pointColor: BLACK
            },
            bullet: {},
            polar: {
                commonSeriesSettings: {}
            },
            funnel: {
                label: {
                    connector: {
                        opacity: 1
                    }
                }
            },
            sankey: {
                label: {
                    font: {
                        color: WHITE
                    },
                    shadow: {
                        opacity: 0
                    }
                },
                node: {
                    border: {
                        visible: true,
                        width: 1,
                        color: WHITE
                    }
                },
                link: {
                    opacity: .5,
                    border: {
                        visible: true,
                        width: 1,
                        color: WHITE
                    },
                    hoverStyle: {
                        opacity: .9
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.contrast",
        theme: {
            name: "generic.contrast.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/darkmoon.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/darkmoon.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const ACCENT_COLOR = "#3debd3";
const BACKGROUND_COLOR = "#465672";
const TITLE_COLOR = "#fff";
const SUBTITLE_COLOR = "#919bac";
const TEXT_COLOR = "#c7ccd4";
const BORDER_COLOR = "#596980";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.dark",
        theme: {
            name: "generic.darkmoon",
            defaultPalette: "Dark Moon",
            backgroundColor: "#465672",
            primaryTitleColor: "#fff",
            secondaryTitleColor: "#919bac",
            gridColor: "#596980",
            axisColor: "#c7ccd4",
            export: {
                backgroundColor: "#465672",
                font: {
                    color: "#fff"
                },
                button: {
                    default: {
                        color: "#fff",
                        borderColor: "#7a889e",
                        backgroundColor: "#465672"
                    },
                    hover: {
                        color: "#fff",
                        borderColor: "#9da8b8",
                        backgroundColor: "#596e92"
                    },
                    focus: {
                        color: "#fff",
                        borderColor: "#c4cad4",
                        backgroundColor: "#596e92"
                    },
                    active: {
                        color: "#fff",
                        borderColor: "#c4cad4",
                        backgroundColor: "#6b80a4"
                    }
                }
            },
            legend: {
                font: {
                    color: "#c7ccd4"
                }
            },
            tooltip: {
                color: "#62789e",
                border: {
                    color: "#596980"
                },
                font: {
                    color: "#fff"
                }
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#596980"
                        }
                    }
                }
            },
            "chart:common:annotation": {
                font: {
                    color: "#fff"
                },
                border: {
                    color: "#596980"
                },
                color: "#62789e"
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#596980"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#73869e"
                    }
                }
            },
            gauge: {
                valueIndicators: {
                    rangebar: {
                        color: "#3debd3"
                    },
                    textcloud: {
                        color: "#3debd3",
                        text: {
                            font: {
                                color: "#465672"
                            }
                        }
                    }
                }
            },
            barGauge: {
                backgroundColor: "#526280"
            },
            funnel: {
                item: {
                    border: {
                        color: "#465672"
                    }
                }
            },
            sparkline: {
                pointColor: "#465672",
                minColor: "#f0ad4e",
                maxColor: "#f9517e"
            },
            treeMap: {
                group: {
                    color: "#596980",
                    label: {
                        font: {
                            color: "#fff"
                        }
                    }
                }
            },
            map: {
                background: {
                    borderColor: "#596980"
                },
                "layer:area": {
                    color: "#97a3b6",
                    borderColor: "#465672"
                }
            },
            rangeSelector: {
                shutter: {
                    color: "#465672"
                },
                scale: {
                    breakStyle: {
                        color: "#73869e"
                    },
                    tick: {
                        opacity: .2
                    }
                },
                selectedRangeColor: "#3debd3",
                sliderMarker: {
                    color: "#3debd3",
                    font: {
                        color: "#000"
                    }
                },
                sliderHandle: {
                    color: "#3debd3",
                    opacity: .5
                }
            },
            bullet: {
                color: "#3debd3"
            },
            sankey: {
                link: {
                    border: {
                        color: "#465672"
                    }
                },
                node: {
                    border: {
                        color: "#465672"
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.darkmoon",
        theme: {
            name: "generic.darkmoon.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/darkviolet.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/darkviolet.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const ACCENT_COLOR = "#9c63ff";
const BACKGROUND_COLOR = "#17171f";
const TITLE_COLOR = "#f5f6f7";
const SUBTITLE_COLOR = "#fff";
const TEXT_COLOR = "#b2b2b6";
const BORDER_COLOR = "#343840";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.dark",
        theme: {
            name: "generic.darkviolet",
            defaultPalette: "Dark Violet",
            backgroundColor: "#17171f",
            primaryTitleColor: "#f5f6f7",
            secondaryTitleColor: "#fff",
            gridColor: "#343840",
            axisColor: "#b2b2b6",
            export: {
                backgroundColor: "#17171f",
                font: {
                    color: "#f5f6f7"
                },
                button: {
                    default: {
                        color: "#f5f6f7",
                        borderColor: "#414152",
                        backgroundColor: "#17171f"
                    },
                    hover: {
                        color: "#f5f6f7",
                        borderColor: "#5c5c74",
                        backgroundColor: "#2d2d3c"
                    },
                    focus: {
                        color: "#f5f6f7",
                        borderColor: "#7c7c97",
                        backgroundColor: "#2d2d3c"
                    },
                    active: {
                        color: "#f5f6f7",
                        borderColor: "#7c7c97",
                        backgroundColor: "#3c3c51"
                    }
                }
            },
            legend: {
                font: {
                    color: "#b2b2b6"
                }
            },
            tooltip: {
                color: "#17171f",
                border: {
                    color: "#414152"
                },
                font: {
                    color: "#f5f6f7"
                }
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#343840"
                        }
                    }
                }
            },
            "chart:common:annotation": {
                font: {
                    color: "#f5f6f7"
                },
                border: {
                    color: "#414152"
                },
                color: "#17171f"
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#343840"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#575e6b"
                    }
                }
            },
            funnel: {
                item: {
                    border: {
                        color: "#17171f"
                    }
                }
            },
            sparkline: {
                pointColor: "#17171f",
                minColor: "#f0ad4e",
                maxColor: "#d9534f"
            },
            treeMap: {
                group: {
                    color: "#343840",
                    label: {
                        font: {
                            color: "#fff"
                        }
                    }
                }
            },
            rangeSelector: {
                shutter: {
                    color: "#17171f"
                },
                scale: {
                    breakStyle: {
                        color: "#575e6b"
                    },
                    tick: {
                        opacity: .2
                    }
                },
                selectedRangeColor: "#9c63ff",
                sliderMarker: {
                    color: "#9c63ff",
                    font: {
                        color: "#fff"
                    }
                },
                sliderHandle: {
                    color: "#9c63ff",
                    opacity: .5
                }
            },
            bullet: {
                color: "#9c63ff"
            },
            gauge: {
                valueIndicators: {
                    rangebar: {
                        color: "#9c63ff"
                    },
                    textcloud: {
                        color: "#9c63ff"
                    }
                }
            },
            sankey: {
                link: {
                    border: {
                        color: "#17171f"
                    }
                },
                node: {
                    border: {
                        color: "#17171f"
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.darkviolet",
        theme: {
            name: "generic.darkviolet.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/greenmist.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/greenmist.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const ACCENT_COLOR = "#3cbab2";
const BACKGROUND_COLOR = "#f5f5f5";
const TITLE_COLOR = "#28484f";
const SUBTITLE_COLOR = "#7eb2be";
const TEXT_COLOR = "#657c80";
const BORDER_COLOR = "#dedede";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.light",
        theme: {
            name: "generic.greenmist",
            defaultPalette: "Green Mist",
            backgroundColor: "#f5f5f5",
            primaryTitleColor: "#28484f",
            secondaryTitleColor: "#7eb2be",
            gridColor: "#dedede",
            axisColor: "#657c80",
            export: {
                backgroundColor: "#f5f5f5",
                font: {
                    color: "#28484f"
                },
                button: {
                    default: {
                        color: "#28484f",
                        borderColor: "#a2b4b8",
                        backgroundColor: "#f5f5f5"
                    },
                    hover: {
                        color: "#28484f",
                        borderColor: "#7f989e",
                        backgroundColor: "rgba(222, 222, 222, 0.4)"
                    },
                    focus: {
                        color: "#28484f",
                        borderColor: "#5f777c",
                        backgroundColor: "rgba(222, 222, 222, 0.4)"
                    },
                    active: {
                        color: "#28484f",
                        borderColor: "#5f777c",
                        backgroundColor: "rgba(222, 222, 222, 0.8)"
                    }
                }
            },
            legend: {
                font: {
                    color: "#657c80"
                }
            },
            tooltip: {
                color: "#fff",
                border: {
                    color: "#dedede"
                },
                font: {
                    color: "#28484f"
                }
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#dedede"
                        }
                    }
                }
            },
            "chart:common:annotation": {
                color: "#fff",
                border: {
                    color: "#dedede"
                },
                font: {
                    color: "#28484f"
                }
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#dedede"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#c1c1c1"
                    }
                }
            },
            funnel: {
                item: {
                    border: {
                        color: "#f5f5f5"
                    }
                }
            },
            sparkline: {
                pointColor: "#f5f5f5",
                minColor: "#ffc852",
                maxColor: "#f74a5e"
            },
            treeMap: {
                group: {
                    color: "#dedede",
                    label: {
                        font: {
                            color: "#7eb2be"
                        }
                    }
                }
            },
            rangeSelector: {
                shutter: {
                    color: "#f5f5f5"
                },
                scale: {
                    breakStyle: {
                        color: "#c1c1c1"
                    },
                    tick: {
                        opacity: .12
                    }
                },
                selectedRangeColor: "#3cbab2",
                sliderMarker: {
                    color: "#3cbab2"
                },
                sliderHandle: {
                    color: "#3cbab2",
                    opacity: .5
                }
            },
            bullet: {
                color: "#3cbab2"
            },
            gauge: {
                valueIndicators: {
                    rangebar: {
                        color: "#3cbab2"
                    },
                    textcloud: {
                        color: "#3cbab2"
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.greenmist",
        theme: {
            name: "generic.greenmist.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/generic/softblue.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/generic/softblue.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const ACCENT_COLOR = "#7ab8eb";
const BACKGROUND_COLOR = "#fff";
const TITLE_COLOR = "#333";
const SUBTITLE_COLOR = "#99a1a8";
const TEXT_COLOR = "#707070";
const BORDER_COLOR = "#e8eaeb";
const __TURBOPACK__default__export__ = [
    {
        baseThemeName: "generic.light",
        theme: {
            name: "generic.softblue",
            defaultPalette: "Soft Blue",
            backgroundColor: "#fff",
            primaryTitleColor: "#333",
            secondaryTitleColor: "#99a1a8",
            gridColor: "#e8eaeb",
            axisColor: "#707070",
            export: {
                backgroundColor: "#fff",
                font: {
                    color: "#333"
                },
                button: {
                    default: {
                        color: "#333",
                        borderColor: "#c9d0d4",
                        backgroundColor: "#fff"
                    },
                    hover: {
                        color: "#333",
                        borderColor: "#a7b2b9",
                        backgroundColor: "#e6e6e6"
                    },
                    focus: {
                        color: "#333",
                        borderColor: "#82929b",
                        backgroundColor: "#e6e6e6"
                    },
                    active: {
                        color: "#333",
                        borderColor: "#82929b",
                        backgroundColor: "#d4d4d4"
                    }
                }
            },
            legend: {
                font: {
                    color: "#707070"
                }
            },
            tooltip: {
                color: "#fff",
                border: {
                    color: "#e8eaeb"
                },
                font: {
                    color: "#333"
                }
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#e8eaeb"
                        }
                    }
                }
            },
            "chart:common:annotation": {
                color: "#fff",
                border: {
                    color: "#e8eaeb"
                },
                font: {
                    color: "#333"
                }
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#e8eaeb"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#cfd2d3"
                    }
                }
            },
            rangeSelector: {
                scale: {
                    breakStyle: {
                        color: "#cfd2d3"
                    },
                    tick: {
                        opacity: .12
                    }
                },
                selectedRangeColor: "#7ab8eb",
                sliderMarker: {
                    color: "#7ab8eb"
                },
                sliderHandle: {
                    color: "#7ab8eb",
                    opacity: .5
                }
            },
            sparkline: {
                pointColor: "#fff",
                minColor: "#f0ad4e",
                maxColor: "#d9534f"
            },
            treeMap: {
                group: {
                    color: "#e8eaeb",
                    label: {
                        font: {
                            color: "#99a1a8"
                        }
                    }
                }
            },
            bullet: {
                color: "#7ab8eb"
            },
            gauge: {
                valueIndicators: {
                    rangebar: {
                        color: "#7ab8eb"
                    },
                    textcloud: {
                        color: "#7ab8eb"
                    }
                }
            }
        }
    },
    {
        baseThemeName: "generic.softblue",
        theme: {
            name: "generic.softblue.compact"
        }
    }
];
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/material/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/material/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const FONT_FAMILY = "'Roboto', 'RobotoFallback', 'Helvetica', 'Arial', sans-serif";
const LIGHT_TITLE_COLOR = "rgba(0,0,0,0.87)";
const LIGHT_LABEL_COLOR = "rgba(0,0,0,0.54)";
const DARK_TITLE_COLOR = "rgba(255,255,255,0.87)";
const DARK_LABEL_COLOR = "rgba(255,255,255,0.54)";
const DARK_BACKGROUND_COLOR = "#363640";
const WHITE = "#ffffff";
const BLACK = "#000000";
const RANGE_COLOR = "#b5b5b5";
const AREA_LAYER_COLOR = "#686868";
const LINE_COLOR = "#c7c7c7";
const TARGET_COLOR = "#8e8e8e";
const POSITIVE_COLOR = "#b8b8b8";
const LABEL_BORDER_COLOR = "#494949";
const BREAK_STYLE_COLOR = "#818181";
const themes = [
    {
        baseThemeName: "generic.light",
        theme: {
            name: "material",
            defaultPalette: "Material",
            font: {
                family: FONT_FAMILY
            },
            title: {
                margin: {
                    top: 20,
                    bottom: 20,
                    left: 0,
                    right: 0
                },
                font: {
                    size: 20,
                    family: FONT_FAMILY,
                    weight: 500
                },
                horizontalAlignment: "left",
                subtitle: {
                    font: {
                        size: 14
                    },
                    horizontalAlignment: "left"
                }
            },
            tooltip: {
                shadow: {
                    opacity: 0
                },
                border: {
                    visible: false
                },
                paddingLeftRight: 8,
                paddingTopBottom: 6,
                arrowLength: 0,
                location: "edge",
                color: "#616161",
                font: {
                    color: WHITE
                },
                cornerRadius: 4
            },
            chart: {
                commonAxisSettings: {
                    minorTick: {
                        opacity: .5
                    },
                    label: {
                        font: {
                            size: 11
                        }
                    }
                },
                commonAnnotationSettings: {
                    font: {
                        color: WHITE
                    },
                    border: {
                        color: "#616161"
                    },
                    color: "#616161",
                    arrowLength: 14,
                    arrowWidth: 0,
                    shadow: {
                        opacity: .08,
                        offsetY: 4,
                        blur: 8
                    },
                    cornerRadius: 4
                }
            },
            pie: {
                title: {
                    horizontalAlignment: "center",
                    subtitle: {
                        horizontalAlignment: "center"
                    }
                }
            },
            polar: {
                commonAxisSettings: {
                    minorTick: {
                        opacity: .5
                    }
                },
                title: {
                    horizontalAlignment: "center",
                    subtitle: {
                        horizontalAlignment: "center"
                    }
                }
            },
            funnel: {
                title: {
                    horizontalAlignment: "center",
                    subtitle: {
                        horizontalAlignment: "center"
                    }
                }
            },
            gauge: {
                title: {
                    horizontalAlignment: "center",
                    subtitle: {
                        horizontalAlignment: "center"
                    }
                }
            },
            barGauge: {
                title: {
                    horizontalAlignment: "center",
                    subtitle: {
                        horizontalAlignment: "center"
                    }
                }
            },
            rangeSelector: {
                sliderHandle: {
                    opacity: .5
                }
            },
            treeMap: {
                group: {
                    label: {
                        font: {
                            weight: 500
                        }
                    }
                }
            }
        }
    },
    {
        baseThemeName: "material",
        theme: {
            name: "material.light",
            gridColor: "#e0e0e0",
            axisColor: LIGHT_LABEL_COLOR,
            primaryTitleColor: LIGHT_TITLE_COLOR,
            legend: {
                font: {
                    color: LIGHT_LABEL_COLOR
                }
            },
            chart: {
                scrollBar: {
                    color: "#bfbfbf",
                    opacity: .7
                }
            },
            gauge: {
                rangeContainer: {
                    backgroundColor: "rgba(0,0,0,0.2)"
                }
            },
            barGauge: {
                backgroundColor: "#efefef"
            }
        }
    },
    {
        baseThemeName: "material",
        theme: {
            name: "material.dark",
            gridColor: "#515159",
            backgroundColor: "#363640",
            axisColor: DARK_LABEL_COLOR,
            font: {
                color: DARK_LABEL_COLOR
            },
            primaryTitleColor: DARK_TITLE_COLOR,
            secondaryTitleColor: DARK_TITLE_COLOR,
            tooltip: {
                color: "#000"
            },
            export: {
                backgroundColor: "#363640",
                font: {
                    color: "#dbdbdb"
                },
                button: {
                    default: {
                        color: "#dedede",
                        borderColor: "#4d4d4d",
                        backgroundColor: "#363640"
                    },
                    hover: {
                        color: "#dedede",
                        borderColor: "#6c6c6c",
                        backgroundColor: "#3f3f4b"
                    },
                    focus: {
                        color: "#dedede",
                        borderColor: "#8d8d8d",
                        backgroundColor: "#494956"
                    },
                    active: {
                        color: "#dedede",
                        borderColor: "#8d8d8d",
                        backgroundColor: "#494956"
                    }
                },
                shadowColor: "#292929"
            },
            "chart:common": {
                commonSeriesSettings: {
                    label: {
                        border: {
                            color: "#494949"
                        }
                    },
                    valueErrorBar: {
                        color: WHITE
                    }
                }
            },
            "chart:common:axis": {
                constantLineStyle: {
                    color: WHITE
                }
            },
            "chart:common:annotation": {
                border: {
                    color: "#000"
                },
                color: "#000"
            },
            chart: {
                commonPaneSettings: {
                    border: {
                        color: "#494949"
                    }
                },
                commonAxisSettings: {
                    breakStyle: {
                        color: "#818181"
                    }
                },
                zoomAndPan: {
                    dragBoxStyle: {
                        color: WHITE
                    }
                }
            },
            gauge: {
                rangeContainer: {
                    backgroundColor: "#b5b5b5"
                },
                valueIndicators: {
                    _default: {
                        color: "#b5b5b5"
                    },
                    rangebar: {
                        color: "#84788b"
                    },
                    twocolorneedle: {
                        secondColor: "#ba544d"
                    },
                    trianglemarker: {
                        color: "#b7918f"
                    },
                    textcloud: {
                        color: "#ba544d"
                    }
                }
            },
            barGauge: {
                backgroundColor: "#3c3c3c"
            },
            rangeSelector: {
                scale: {
                    tick: {
                        color: WHITE,
                        opacity: .32
                    },
                    minorTick: {
                        color: WHITE,
                        opacity: .1
                    },
                    breakStyle: {
                        color: "#818181"
                    }
                },
                selectedRangeColor: "#b5b5b5",
                sliderMarker: {
                    color: "#b5b5b5",
                    font: {
                        color: "#363640"
                    }
                },
                sliderHandle: {
                    color: WHITE,
                    opacity: .2
                },
                shutter: {
                    color: WHITE,
                    opacity: .1
                }
            },
            map: {
                background: {
                    borderColor: "#3f3f3f"
                },
                layer: {
                    label: {
                        stroke: BLACK,
                        font: {
                            color: WHITE
                        }
                    }
                },
                "layer:area": {
                    borderColor: "#363640",
                    color: "#686868",
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                "layer:line": {
                    color: "#c77244",
                    hoveredColor: "#ff5d04",
                    selectedColor: "#ff784f"
                },
                "layer:marker:bubble": {
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                "layer:marker:pie": {
                    hoveredBorderColor: WHITE,
                    selectedBorderColor: WHITE
                },
                legend: {
                    border: {
                        color: "#3f3f3f"
                    },
                    font: {
                        color: WHITE
                    }
                },
                controlBar: {
                    borderColor: "#c7c7c7",
                    color: "#363640"
                }
            },
            treeMap: {
                group: {
                    color: "#4c4c4c",
                    label: {
                        font: {
                            color: "#a3a3a3"
                        }
                    }
                }
            },
            sparkline: {
                lineColor: "#c7c7c7",
                firstLastColor: "#c7c7c7",
                barPositiveColor: "#b8b8b8",
                barNegativeColor: "#8e8e8e",
                winColor: "#b8b8b8",
                lossColor: "#8e8e8e",
                pointColor: "#363640"
            },
            bullet: {
                targetColor: "#8e8e8e"
            },
            funnel: {
                item: {
                    border: {
                        color: "#363640"
                    }
                }
            },
            sankey: {
                label: {
                    font: {
                        color: WHITE
                    }
                }
            }
        }
    }
];
function getMaterialColorScheme(accentName, themeName, accentColor) {
    return {
        theme: {
            name: "material.".concat(accentName, ".").concat(themeName),
            rangeSelector: {
                selectedRangeColor: accentColor,
                sliderMarker: {
                    color: accentColor
                },
                sliderHandle: {
                    color: accentColor
                }
            },
            map: {
                "layer:marker:dot": {
                    color: accentColor
                },
                "layer:marker:bubble": {
                    color: accentColor
                },
                legend: {
                    markerColor: accentColor
                }
            },
            bullet: {
                color: accentColor
            },
            gauge: {
                valueIndicators: {
                    rangebar: {
                        color: accentColor
                    },
                    textcloud: {
                        color: accentColor
                    }
                }
            }
        },
        baseThemeName: "material.".concat(themeName)
    };
}
const materialAccents = {
    blue: "#03a9f4",
    lime: "#cddc39",
    orange: "#ff5722",
    purple: "#9c27b0",
    teal: "#009688"
};
Object.keys(materialAccents).forEach((accent)=>{
    const color = materialAccents[accent];
    themes.push(getMaterialColorScheme(accent, "light", color), getMaterialColorScheme(accent, "dark", color), {
        theme: {
            name: "material.".concat(accent, ".light.compact")
        },
        baseThemeName: "material.".concat(accent, ".light")
    }, {
        theme: {
            name: "material.".concat(accent, ".dark.compact")
        },
        baseThemeName: "material.".concat(accent, ".dark")
    });
});
const __TURBOPACK__default__export__ = themes;
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/themes/fluent/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/themes/fluent/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const themes = [
    {
        baseThemeName: "material.blue.light",
        theme: {
            name: "fluent.blue.light"
        }
    },
    {
        baseThemeName: "fluent.blue.light",
        theme: {
            name: "fluent.blue.light.compact"
        }
    },
    {
        baseThemeName: "fluent.blue.light",
        theme: {
            name: "fluent.saas.light"
        }
    },
    {
        baseThemeName: "fluent.saas.light",
        theme: {
            name: "fluent.saas.light.compact"
        }
    },
    {
        baseThemeName: "material.blue.dark",
        theme: {
            name: "fluent.blue.dark"
        }
    },
    {
        baseThemeName: "fluent.blue.dark",
        theme: {
            name: "fluent.blue.dark.compact"
        }
    },
    {
        baseThemeName: "fluent.blue.dark",
        theme: {
            name: "fluent.saas.dark"
        }
    },
    {
        baseThemeName: "fluent.saas.dark",
        theme: {
            name: "fluent.saas.dark.compact"
        }
    }
];
const __TURBOPACK__default__export__ = themes;
}),
"[project]/node_modules/devextreme/esm/__internal/viz/core/m_base_widget.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/core/m_base_widget.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/deferred.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_deferred.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_theme_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/base_theme_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_widget$2e$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/base_widget.utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$errors_warnings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/errors_warnings.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/layout.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/renderers/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$common$2f$m_charts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/common/m_charts.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const { log: log } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$errors_warnings$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const OPTION_RTL_ENABLED = "rtlEnabled";
const SIZED_ELEMENT_CLASS = "dx-sized-element";
const baseOptionMethod = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].prototype.option;
function getTrue() {
    return true;
}
function getFalse() {
    return false;
}
function defaultOnIncidentOccurred(e) {
    if (!e.component._eventsStrategy.hasEvent("incidentOccurred")) {
        log.apply(null, [
            e.target.id
        ].concat(e.target.args || []));
    }
}
function pickPositiveValue(values) {
    return values.reduce((result, value)=>value > 0 && !result ? value : result, 0);
}
const getEmptyComponent = function() {
    const emptyComponentConfig = {
        _initTemplates () {},
        ctor (element, options) {
            this.callBase(element, options);
            const sizedElement = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].createElement("div");
            const width = options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumeric"])(options.width) ? "".concat(options.width, "px") : "100%";
            const height = options && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumeric"])(options.height) ? "".concat(options.height, "px") : "".concat(this._getDefaultSize().height, "px");
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setStyle(sizedElement, "width", width);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setStyle(sizedElement, "height", height);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].setClass(sizedElement, "dx-sized-element", false);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].insertElement(element, sizedElement);
        }
    };
    const EmptyComponent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit(emptyComponentConfig);
    const originalInherit = EmptyComponent.inherit;
    EmptyComponent.inherit = function(config) {
        Object.keys(config).forEach((field)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(config[field]) && "_" !== field.substr(0, 1) && "option" !== field || "_dispose" === field || "_optionChanged" === field) {
                config[field] = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
            }
        });
        return originalInherit.call(this, config);
    };
    return EmptyComponent;
};
function callForEach(functions) {
    functions.forEach((c)=>c());
}
const isServerSide = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasWindow"])();
function sizeIsValid(value) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value) && value > 0;
}
const baseWidget = isServerSide ? getEmptyComponent() : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    _eventsMap: {
        onIncidentOccurred: {
            name: "incidentOccurred",
            actionSettings: {
                excludeValidators: [
                    "disabled"
                ]
            }
        },
        onDrawn: {
            name: "drawn",
            actionSettings: {
                excludeValidators: [
                    "disabled"
                ]
            }
        }
    },
    _getDefaultOptions () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this.callBase(), {
            onIncidentOccurred: defaultOnIncidentOccurred
        });
    },
    _useLinks: true,
    _init () {
        this._$element.children(".dx-sized-element").remove();
        this._graphicObjects = {};
        this.callBase(...arguments);
        this._changesLocker = 0;
        this._optionChangedLocker = 0;
        this._asyncFirstDrawing = true;
        this._changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["changes"])();
        this._suspendChanges();
        this._themeManager = this._createThemeManager();
        this._themeManager.setCallback(()=>{
            this._requestChange(this._themeDependentChanges);
        });
        this._renderElementAttributes();
        this._initRenderer();
        const useLinks = this._useLinks;
        if (useLinks) {
            this._renderer.root.enableLinks().virtualLink("core").virtualLink("peripheral");
        }
        this._renderVisibilityChange();
        this._attachVisibilityChangeHandlers();
        this._toggleParentsScrollSubscription(this._isVisible());
        this._initEventTrigger();
        this._incidentOccurred = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_widget$2e$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createIncidentOccurred"])(this.NAME, this._eventTrigger);
        this._layout = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$layout$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        if (useLinks) {
            this._renderer.root.linkAfter("core");
        }
        this._initPlugins();
        this._initCore();
        if (useLinks) {
            this._renderer.root.linkAfter();
        }
        this._change(this._initialChanges);
    },
    _createThemeManager () {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_theme_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseThemeManager"](this._getThemeManagerOptions());
    },
    _getThemeManagerOptions () {
        return {
            themeSection: this._themeSection,
            fontFields: this._fontFields
        };
    },
    _initialChanges: [
        "LAYOUT",
        "RESIZE_HANDLER",
        "THEME",
        "DISABLED"
    ],
    _initPlugins () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._plugins, (_, plugin)=>{
            plugin.init.call(this);
        });
    },
    _disposePlugins () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._plugins.slice().reverse(), (_, plugin)=>{
            plugin.dispose.call(this);
        });
    },
    _change (codes) {
        this._changes.add(codes);
    },
    _suspendChanges () {
        this._changesLocker += 1;
    },
    _resumeChanges () {
        if (0 === --this._changesLocker && this._changes.count() > 0 && !this._applyingChanges) {
            this._renderer.lock();
            this._applyingChanges = true;
            this._applyChanges();
            this._changes.reset();
            this._applyingChanges = false;
            this._changesApplied();
            this._renderer.unlock();
            if (this._optionsQueue) {
                this._applyQueuedOptions();
            }
            this.resolveItemsDeferred(this._legend ? [
                this._legend
            ] : []);
            this._optionChangedLocker += 1;
            this._notify();
            this._optionChangedLocker -= 1;
        }
    },
    resolveItemsDeferred (items) {
        this._resolveDeferred(this._getTemplatesItems(items));
    },
    _collectTemplatesFromItems: (items)=>items.reduce((prev, i)=>({
                items: prev.items.concat(i.getTemplatesDef()),
                groups: prev.groups.concat(i.getTemplatesGroups())
            }), {
            items: [],
            groups: []
        }),
    _getTemplatesItems (items) {
        const elements = this._collectTemplatesFromItems(items);
        const extraItems = this._getExtraTemplatesItems();
        return {
            items: extraItems.items.concat(elements.items),
            groups: extraItems.groups.concat(elements.groups),
            launchRequest: [
                extraItems.launchRequest
            ],
            doneRequest: [
                extraItems.doneRequest
            ]
        };
    },
    _getExtraTemplatesItems: ()=>({
            items: [],
            groups: [],
            launchRequest: ()=>{},
            doneRequest: ()=>{}
        }),
    _resolveDeferred (_ref) {
        let { items: items, launchRequest: launchRequest, doneRequest: doneRequest, groups: groups } = _ref;
        this._setGroupsVisibility(groups, "hidden");
        if (this._changesApplying) {
            this._changesApplying = false;
            callForEach(doneRequest);
            return;
        }
        let syncRendering = true;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_deferred$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["when"].apply(this, items).done(()=>{
            var _groups$;
            const isGroupInDom = !(null !== (_groups$ = groups[0]) && void 0 !== _groups$ && _groups$.element) || !!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(groups[0].element.closest("svg")).length;
            if (!isGroupInDom) {
                return;
            }
            if (syncRendering) {
                this._setGroupsVisibility(groups, "visible");
                return;
            }
            callForEach(launchRequest);
            this._changesApplying = true;
            const changes = [
                "LAYOUT",
                "FULL_RENDER"
            ];
            if (this._asyncFirstDrawing) {
                changes.push("FORCE_FIRST_DRAWING");
                this._asyncFirstDrawing = false;
            } else {
                changes.push("FORCE_DRAWING");
            }
            this._requestChange(changes);
            this._setGroupsVisibility(groups, "visible");
        });
        syncRendering = false;
    },
    _setGroupsVisibility (groups, visibility) {
        groups.forEach((g)=>g.attr({
                visibility: visibility
            }));
    },
    _applyQueuedOptions () {
        const queue = this._optionsQueue;
        this._optionsQueue = null;
        this.beginUpdate();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(queue, (_, action)=>{
            action();
        });
        this.endUpdate();
    },
    _requestChange (codes) {
        this._suspendChanges();
        this._change(codes);
        this._resumeChanges();
    },
    _applyChanges () {
        const changes = this._changes;
        const order = this._totalChangesOrder;
        const changesOrderLength = order.length;
        for(let i = 0; i < changesOrderLength; i += 1){
            if (changes.has(order[i])) {
                this["_change_".concat(order[i])]();
            }
        }
    },
    _optionChangesOrder: [
        "EVENTS",
        "THEME",
        "RENDERER",
        "RESIZE_HANDLER"
    ],
    _layoutChangesOrder: [
        "ELEMENT_ATTR",
        "CONTAINER_SIZE",
        "LAYOUT"
    ],
    _customChangesOrder: [
        "DISABLED"
    ],
    _change_EVENTS () {
        this._eventTrigger.applyChanges();
    },
    _change_THEME () {
        this._setThemeAndRtl();
    },
    _change_RENDERER () {
        this._setRendererOptions();
    },
    _change_RESIZE_HANDLER () {
        this._setupResizeHandler();
    },
    _change_ELEMENT_ATTR () {
        this._renderElementAttributes();
        this._change([
            "CONTAINER_SIZE"
        ]);
    },
    _change_CONTAINER_SIZE () {
        this._updateSize();
    },
    _change_LAYOUT () {
        this._setContentSize();
    },
    _change_DISABLED () {
        const renderer = this._renderer;
        const { root: root } = renderer;
        if (this.option("disabled")) {
            this._initDisabledState = root.attr("pointer-events");
            root.attr({
                "pointer-events": "none",
                filter: renderer.getGrayScaleFilter().id
            });
        } else if ("none" === root.attr("pointer-events")) {
            root.attr({
                "pointer-events": (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this._initDisabledState) ? this._initDisabledState : null,
                filter: null
            });
        }
    },
    _themeDependentChanges: [
        "RENDERER"
    ],
    _initRenderer () {
        const rawCanvas = this._calculateRawCanvas();
        this._canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["floorCanvasDimensions"])(rawCanvas);
        this._renderer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$renderers$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Renderer"]({
            cssClass: "".concat(this._rootClassPrefix, " ").concat(this._rootClass),
            pathModified: this.option("pathModified"),
            container: this._$element[0]
        });
        this._renderer.resize(this._canvas.width, this._canvas.height);
    },
    _disposeRenderer () {
        this._renderer.dispose();
    },
    _disposeGraphicObjects () {
        Object.keys(this._graphicObjects).forEach((id)=>{
            this._graphicObjects[id].dispose();
        });
        this._graphicObjects = null;
    },
    _getAnimationOptions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    render () {
        this._requestChange([
            "CONTAINER_SIZE"
        ]);
        const visible = this._isVisible();
        this._toggleParentsScrollSubscription(visible);
        !visible && this._stopCurrentHandling();
    },
    _toggleParentsScrollSubscription (subscribe) {
        let $parents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this._renderer.root.element).parents();
        if ("generic" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real().platform) {
            $parents = $parents.add((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])());
        }
        this._proxiedTargetParentsScrollHandler = this._proxiedTargetParentsScrollHandler || (function() {
            this._stopCurrentHandling();
        }).bind(this);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("").add(this._$prevRootParents), "scroll.viz_widgets", this._proxiedTargetParentsScrollHandler);
        if (subscribe) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($parents, "scroll.viz_widgets", this._proxiedTargetParentsScrollHandler);
            this._$prevRootParents = $parents;
        }
    },
    _stopCurrentHandling: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _dispose () {
        if (this._disposed) {
            return;
        }
        this.callBase(...arguments);
        this._toggleParentsScrollSubscription(false);
        this._removeResizeHandler();
        this._layout.dispose();
        this._eventTrigger.dispose();
        this._disposeCore();
        this._disposePlugins();
        this._disposeGraphicObjects();
        this._disposeRenderer();
        this._themeManager.dispose();
        this._themeManager = null;
        this._renderer = null;
        this._eventTrigger = null;
    },
    _initEventTrigger () {
        this._eventTrigger = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_widget$2e$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createEventTrigger"])(this._eventsMap, (name, actionSettings)=>this._createActionByOption(name, actionSettings));
    },
    _calculateRawCanvas () {
        const size = this.option("size") || {};
        const margin = this.option("margin") || {};
        const defaultCanvas = this._getDefaultSize() || {};
        const getSizeOfSide = (size, side, getter)=>{
            if (sizeIsValid(size[side]) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasWindow"])()) {
                return 0;
            }
            const elementSize = getter(this._$element);
            return elementSize <= 1 ? 0 : elementSize;
        };
        const elementWidth = getSizeOfSide(size, "width", (x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(x));
        const elementHeight = getSizeOfSide(size, "height", (x)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])(x));
        let canvas = {
            width: size.width <= 0 ? 0 : pickPositiveValue([
                size.width,
                elementWidth,
                defaultCanvas.width
            ]),
            height: size.height <= 0 ? 0 : pickPositiveValue([
                size.height,
                elementHeight,
                defaultCanvas.height
            ]),
            left: pickPositiveValue([
                margin.left,
                defaultCanvas.left
            ]),
            top: pickPositiveValue([
                margin.top,
                defaultCanvas.top
            ]),
            right: pickPositiveValue([
                margin.right,
                defaultCanvas.right
            ]),
            bottom: pickPositiveValue([
                margin.bottom,
                defaultCanvas.bottom
            ])
        };
        if (canvas.width - canvas.left - canvas.right <= 0 || canvas.height - canvas.top - canvas.bottom <= 0) {
            canvas = {
                width: 0,
                height: 0
            };
        }
        return canvas;
    },
    _updateSize () {
        const rawCanvas = this._calculateRawCanvas();
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["areCanvasesDifferent"])(this._canvas, rawCanvas) || this.__forceRender) {
            this._canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["floorCanvasDimensions"])(rawCanvas);
            this._recreateSizeDependentObjects(true);
            this._renderer.resize(this._canvas.width, this._canvas.height);
            this._change([
                "LAYOUT"
            ]);
        }
    },
    _recreateSizeDependentObjects: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _getMinSize: ()=>[
            0,
            0
        ],
    _getAlignmentRect: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _setContentSize () {
        const canvas = this._canvas;
        const layout = this._layout;
        let rect = canvas.width > 0 && canvas.height > 0 ? [
            canvas.left,
            canvas.top,
            canvas.width - canvas.right,
            canvas.height - canvas.bottom
        ] : [
            0,
            0,
            0,
            0
        ];
        rect = layout.forward(rect, this._getMinSize());
        const nextRect = this._applySize(rect) || rect;
        layout.backward(nextRect, this._getAlignmentRect() || nextRect);
    },
    _getOption (name, isScalar) {
        const theme = this._themeManager.theme(name);
        const option = this.option(name);
        return isScalar ? void 0 !== option ? option : theme : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, theme, option);
    },
    _setupResizeHandler () {
        const redrawOnResize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseScalar"])(this._getOption("redrawOnResize", true), true);
        if (this._disposeResizeHandler) {
            this._removeResizeHandler();
        }
        this._disposeResizeHandler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$base_widget$2e$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createResizeHandler"])(this._$element[0], redrawOnResize, ()=>this._requestChange([
                "CONTAINER_SIZE"
            ]));
    },
    _removeResizeHandler () {
        if (this._disposeResizeHandler) {
            this._disposeResizeHandler();
            this._disposeResizeHandler = null;
        }
    },
    _onBeginUpdate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    beginUpdate () {
        if (this._initialized && this._isUpdateAllowed()) {
            this._onBeginUpdate();
            this._suspendChanges();
        }
        this.callBase(...arguments);
        return this;
    },
    endUpdate () {
        this.callBase();
        this._isUpdateAllowed() && this._resumeChanges();
        return this;
    },
    option (name) {
        if (this._initialized && this._applyingChanges && (arguments.length > 1 || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isObject"])(name))) {
            this._optionsQueue = this._optionsQueue || [];
            this._optionsQueue.push(this._getActionForUpdating(arguments));
        } else {
            return baseOptionMethod.apply(this, arguments);
        }
    },
    _getActionForUpdating (args) {
        return ()=>{
            baseOptionMethod.apply(this, args);
        };
    },
    _clean: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _render: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _optionChanged (arg) {
        if (this._optionChangedLocker) {
            return;
        }
        const partialChanges = this.getPartialChangeOptionsName(arg);
        let changes = [];
        if (partialChanges.length > 0) {
            partialChanges.forEach((pc)=>changes.push(this._partialOptionChangesMap[pc]));
        } else {
            changes.push(this._optionChangesMap[arg.name]);
        }
        changes = changes.filter((c)=>!!c);
        if (this._eventTrigger.change(arg.name)) {
            this._change([
                "EVENTS"
            ]);
        } else if (changes.length > 0) {
            this._change(changes);
        } else {
            this.callBase.apply(this, arguments);
        }
    },
    _notify: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _changesApplied: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _optionChangesMap: {
        size: "CONTAINER_SIZE",
        margin: "CONTAINER_SIZE",
        redrawOnResize: "RESIZE_HANDLER",
        theme: "THEME",
        rtlEnabled: "THEME",
        encodeHtml: "THEME",
        elementAttr: "ELEMENT_ATTR",
        disabled: "DISABLED"
    },
    _partialOptionChangesMap: {},
    _partialOptionChangesPath: {},
    getPartialChangeOptionsName (changedOption) {
        const { fullName: fullName } = changedOption;
        const sections = fullName.split(/[.]/);
        const { name: name } = changedOption;
        const { value: value } = changedOption;
        const options = this._partialOptionChangesPath[name];
        const partialChangeOptionsName = [];
        if (options) {
            if (true === options) {
                partialChangeOptionsName.push(name);
            } else {
                options.forEach((op)=>{
                    fullName.indexOf(op) >= 0 && partialChangeOptionsName.push(op);
                });
                if (1 === sections.length) {
                    if ("object" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(value)) {
                        this._addOptionsNameForPartialUpdate(value, options, partialChangeOptionsName);
                    } else if ("array" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(value)) {
                        if (value.length > 0 && value.every((item)=>this._checkOptionsForPartialUpdate(item, options))) {
                            value.forEach((item)=>{
                                this._addOptionsNameForPartialUpdate(item, options, partialChangeOptionsName);
                            });
                        }
                    }
                }
            }
        }
        return partialChangeOptionsName.filter((value, index, self)=>self.indexOf(value) === index);
    },
    _checkOptionsForPartialUpdate: (optionObject, options)=>!Object.keys(optionObject).some((key)=>-1 === options.indexOf(key)),
    _addOptionsNameForPartialUpdate (optionObject, options, partialChangeOptionsName) {
        const optionKeys = Object.keys(optionObject);
        if (this._checkOptionsForPartialUpdate(optionObject, options)) {
            optionKeys.forEach((key)=>options.indexOf(key) > -1 && partialChangeOptionsName.push(key));
        }
    },
    _visibilityChanged () {
        this.render();
    },
    _setThemeAndRtl () {
        this._themeManager.setTheme(this.option("theme"), this.option("rtlEnabled"));
    },
    _getRendererOptions () {
        return {
            rtl: this.option("rtlEnabled"),
            encodeHtml: this.option("encodeHtml"),
            animation: this._getAnimationOptions()
        };
    },
    _setRendererOptions () {
        this._renderer.setOptions(this._getRendererOptions());
    },
    svg () {
        return this._renderer.svg();
    },
    getSize () {
        const canvas = this._canvas || {};
        return {
            width: canvas.width,
            height: canvas.height
        };
    },
    isReady: getFalse,
    _dataIsReady: getTrue,
    _resetIsReady () {
        this.isReady = getFalse;
    },
    _renderGraphicObjects () {
        const renderer = this._renderer;
        const graphics = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$common$2f$m_charts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getGraphicObjects();
        Object.keys(graphics).forEach((id)=>{
            if (!this._graphicObjects[id]) {
                const { type: type, colors: colors, rotationAngle: rotationAngle, template: template, width: width, height: height } = graphics[id];
                switch(type){
                    case "linear":
                        this._graphicObjects[id] = renderer.linearGradient(colors, id, rotationAngle);
                        break;
                    case "radial":
                        this._graphicObjects[id] = renderer.radialGradient(colors, id);
                        break;
                    case "pattern":
                        this._graphicObjects[id] = renderer.customPattern(id, this._getTemplate(template), width, height);
                }
            }
        });
    },
    _drawn () {
        this.isReady = getFalse;
        if (this._dataIsReady()) {
            this._renderer.onEndAnimation(()=>{
                this.isReady = getTrue;
            });
        }
        this._eventTrigger("drawn", {});
    }
});
const __TURBOPACK__default__export__ = baseWidget;
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["replaceInherit"])(baseWidget);
}),
"[project]/node_modules/devextreme/esm/__internal/viz/chart_components/rolling_stock.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/chart_components/rolling_stock.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "RollingStock": ()=>RollingStock
});
class RollingStock {
    toChain(nextRollingStock) {
        const nextRollingStockBBox = nextRollingStock.getBoundingRect();
        nextRollingStock.shift(nextRollingStockBBox.start - this.bBox.end);
        this.changeBoxWidth(nextRollingStockBBox.width);
        this.labels = this.labels.concat(nextRollingStock.labels);
    }
    getBoundingRect() {
        return this.bBox;
    }
    shift(shiftLength) {
        this.labels.forEach((label)=>{
            const bBox = label.getBoundingRect();
            const coords = this.shiftFunction(bBox, shiftLength);
            if (!label.hideInsideLabel(coords)) {
                label.shift(coords.x, coords.y);
            }
        });
        this.bBox.end -= shiftLength;
        this.bBox.start -= shiftLength;
    }
    setRollingStockInCanvas(canvas) {
        if (this.bBox.end > canvas.end) {
            this.shift(this.bBox.end - canvas.end);
        }
    }
    getLabels() {
        return this.labels;
    }
    value() {
        return this.labels[0].getData().value;
    }
    getInitialPosition() {
        return this.initialPosition;
    }
    changeBoxWidth(width) {
        this.bBox.end += width;
        this.bBox.width += width;
    }
    constructor(label, isRotated, shiftFunction){
        const bBox = label.getBoundingRect();
        const { x: x } = bBox;
        const { y: y } = bBox;
        const endX = bBox.x + bBox.width;
        const endY = bBox.y + bBox.height;
        this.labels = [
            label
        ];
        this.shiftFunction = shiftFunction;
        this.bBox = {
            start: isRotated ? x : y,
            width: isRotated ? bBox.width : bBox.height,
            end: isRotated ? endX : endY,
            oppositeStart: isRotated ? y : x,
            oppositeEnd: isRotated ? endY : endX
        };
        this.initialPosition = isRotated ? bBox.x : bBox.y;
    }
}
}),
"[project]/node_modules/devextreme/esm/__internal/viz/chart_components/m_base_chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/chart_components/m_base_chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "BaseChart": ()=>BaseChart,
    "overlapping": ()=>overlapping
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$layout_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/layout_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$tracker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/tracker.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$chart_theme_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/chart_theme_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$data_validator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/data_validator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$legend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/legend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/data_source.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$export$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/export.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$loading_indicator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/loading_indicator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$title$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/title.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$tooltip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/tooltip.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$base_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/base_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$m_base_widget$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/core/m_base_widget.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$rolling_stock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/chart_components/rolling_stock.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const { isArray: isArray } = Array;
const REINIT_REFRESH_ACTION = "_reinit";
const REINIT_DATA_SOURCE_REFRESH_ACTION = "_updateDataSource";
const DATA_INIT_REFRESH_ACTION = "_dataInit";
const FORCE_RENDER_REFRESH_ACTION = "_forceRender";
const RESIZE_REFRESH_ACTION = "_resize";
const ACTIONS_BY_PRIORITY = [
    "_reinit",
    "_updateDataSource",
    "_dataInit",
    "_forceRender",
    "_resize"
];
const DEFAULT_OPACITY = .3;
const REFRESH_SERIES_DATA_INIT_ACTION_OPTIONS = [
    "series",
    "commonSeriesSettings",
    "dataPrepareSettings",
    "seriesSelectionMode",
    "pointSelectionMode",
    "synchronizeMultiAxes",
    "resolveLabelsOverlapping"
];
const REFRESH_SERIES_FAMILIES_ACTION_OPTIONS = [
    "minBubbleSize",
    "maxBubbleSize",
    "barGroupPadding",
    "barGroupWidth",
    "negativesAsZeroes",
    "negativesAsZeros"
];
const FORCE_RENDER_REFRESH_ACTION_OPTIONS = [
    "adaptiveLayout",
    "crosshair",
    "resolveLabelOverlapping",
    "adjustOnZoom",
    "stickyHovering"
];
const FONT = "font";
function checkHeightRollingStock(rollingStocks, stubCanvas) {
    const canvasSize = stubCanvas.end - stubCanvas.start;
    let size = 0;
    rollingStocks.forEach((rollingStock)=>{
        size += rollingStock.getBoundingRect().width;
    });
    while(canvasSize < size){
        size -= findAndKillSmallValue(rollingStocks);
    }
}
function findAndKillSmallValue(rollingStocks) {
    const smallestObject = rollingStocks.reduce((prev, rollingStock, index)=>{
        if (!rollingStock) {
            return prev;
        }
        const value = rollingStock.value();
        return value < prev.value ? {
            value: value,
            rollingStock: rollingStock,
            index: index
        } : prev;
    }, {
        rollingStock: void 0,
        value: 1 / 0,
        index: void 0
    });
    smallestObject.rollingStock.getLabels()[0].draw(false);
    const { width: width } = smallestObject.rollingStock.getBoundingRect();
    rollingStocks[smallestObject.index] = null;
    return width;
}
function checkStackOverlap(rollingStocks) {
    let i;
    let j;
    let iLength;
    let jLength;
    let overlap = false;
    for(i = 0, iLength = rollingStocks.length - 1; i < iLength; i++){
        for(j = i + 1, jLength = rollingStocks.length; j < jLength; j++){
            if (i !== j && checkStacksOverlapping(rollingStocks[i], rollingStocks[j], true)) {
                overlap = true;
                break;
            }
        }
        if (overlap) {
            break;
        }
    }
    return overlap;
}
function resolveLabelOverlappingInOneDirection(points, canvas, isRotated, isInverted, shiftFunction) {
    let customSorting = arguments.length > 5 && void 0 !== arguments[5] ? arguments[5] : ()=>0;
    const rollingStocks = [];
    const stubCanvas = {
        start: isRotated ? canvas.left : canvas.top,
        end: isRotated ? canvas.width - canvas.right : canvas.height - canvas.bottom
    };
    let hasStackedSeries = false;
    let sortRollingStocks;
    points.forEach((p)=>{
        if (!p) {
            return;
        }
        hasStackedSeries = hasStackedSeries || p.series.isStackedSeries() || p.series.isFullStackedSeries();
        p.getLabels().forEach((l)=>{
            if (l.isVisible()) {
                rollingStocks.push(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$rolling_stock$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RollingStock"](l, isRotated, shiftFunction));
            }
        });
    });
    if (hasStackedSeries) {
        if (Number(!isRotated) ^ Number(isInverted)) {
            rollingStocks.reverse();
        }
        sortRollingStocks = isInverted ? rollingStocks : sortRollingStocksByValue(rollingStocks);
    } else {
        const rollingStocksTmp = rollingStocks.slice();
        sortRollingStocks = rollingStocks.sort((a, b)=>customSorting(a, b) || a.getInitialPosition() - b.getInitialPosition() || rollingStocksTmp.indexOf(a) - rollingStocksTmp.indexOf(b));
    }
    if (!checkStackOverlap(sortRollingStocks)) {
        return false;
    }
    checkHeightRollingStock(sortRollingStocks, stubCanvas);
    prepareOverlapStacks(sortRollingStocks);
    sortRollingStocks.reverse();
    moveRollingStock(sortRollingStocks, stubCanvas);
    return true;
}
function checkStacksOverlapping(firstRolling, secondRolling, inTwoSides) {
    if (!firstRolling || !secondRolling) {
        return;
    }
    const firstRect = firstRolling.getBoundingRect();
    const secondRect = secondRolling.getBoundingRect();
    const oppositeOverlapping = inTwoSides ? firstRect.oppositeStart <= secondRect.oppositeStart && firstRect.oppositeEnd > secondRect.oppositeStart || secondRect.oppositeStart <= firstRect.oppositeStart && secondRect.oppositeEnd > firstRect.oppositeStart : true;
    return firstRect.end > secondRect.start && oppositeOverlapping;
}
function sortRollingStocksByValue(rollingStocks) {
    const positiveRollingStocks = [];
    const negativeRollingStocks = [];
    rollingStocks.forEach((stock)=>{
        if (stock.value() > 0) {
            positiveRollingStocks.push(stock);
        } else {
            negativeRollingStocks.unshift(stock);
        }
    });
    return positiveRollingStocks.concat(negativeRollingStocks);
}
function prepareOverlapStacks(rollingStocks) {
    let root;
    for(let i = 0; i < rollingStocks.length - 1; i += 1){
        const currentRollingStock = root || rollingStocks[i];
        if (checkStacksOverlapping(currentRollingStock, rollingStocks[i + 1])) {
            currentRollingStock.toChain(rollingStocks[i + 1]);
            rollingStocks[i + 1] = null;
            root = currentRollingStock;
        } else {
            root = rollingStocks[i + 1] || currentRollingStock;
        }
    }
}
function rollingStocksIsOut(rollingStock, canvas) {
    return rollingStock.getBoundingRect().end > canvas.end;
}
function moveRollingStock(rollingStocks, canvas) {
    for(let i = 0; i < rollingStocks.length; i += 1){
        const currentRollingStock = rollingStocks[i];
        let shouldSetCanvas = true;
        if (null !== currentRollingStock && rollingStocksIsOut(currentRollingStock, canvas)) {
            const currentBBox = currentRollingStock.getBoundingRect();
            for(let j = i + 1; j < rollingStocks.length; j += 1){
                const nextRollingStock = rollingStocks[j];
                if (nextRollingStock) {
                    const nextBBox = nextRollingStock.getBoundingRect();
                    if (nextBBox.end > currentBBox.start - (currentBBox.end - canvas.end)) {
                        nextRollingStock.toChain(currentRollingStock);
                        shouldSetCanvas = false;
                        break;
                    }
                }
            }
        }
        if (shouldSetCanvas) {
            null === currentRollingStock || void 0 === currentRollingStock || currentRollingStock.setRollingStockInCanvas(canvas);
        }
    }
}
function getLegendFields(name) {
    return {
        nameField: "".concat(name, "Name"),
        colorField: "".concat(name, "Color"),
        indexField: "".concat(name, "Index")
    };
}
function getLegendSettings(legendDataField) {
    const formatObjectFields = getLegendFields(legendDataField);
    return {
        getFormatObject (data) {
            const res = {};
            res[formatObjectFields.indexField] = data.id;
            res[formatObjectFields.colorField] = data.states.normal.fill;
            res[formatObjectFields.nameField] = data.text;
            return res;
        },
        textField: formatObjectFields.nameField
    };
}
function checkOverlapping(firstRect, secondRect) {
    return (firstRect.x <= secondRect.x && secondRect.x <= firstRect.x + firstRect.width || firstRect.x >= secondRect.x && firstRect.x <= secondRect.x + secondRect.width) && (firstRect.y <= secondRect.y && secondRect.y <= firstRect.y + firstRect.height || firstRect.y >= secondRect.y && firstRect.y <= secondRect.y + secondRect.height);
}
const overlapping = {
    resolveLabelOverlappingInOneDirection: resolveLabelOverlappingInOneDirection
};
const BaseChart = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$core$2f$m_base_widget$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    _eventsMap: {
        onSeriesClick: {
            name: "seriesClick"
        },
        onPointClick: {
            name: "pointClick"
        },
        onArgumentAxisClick: {
            name: "argumentAxisClick"
        },
        onLegendClick: {
            name: "legendClick"
        },
        onSeriesSelectionChanged: {
            name: "seriesSelectionChanged"
        },
        onPointSelectionChanged: {
            name: "pointSelectionChanged"
        },
        onSeriesHoverChanged: {
            name: "seriesHoverChanged"
        },
        onPointHoverChanged: {
            name: "pointHoverChanged"
        },
        onDone: {
            name: "done",
            actionSettings: {
                excludeValidators: [
                    "disabled"
                ]
            }
        },
        onZoomStart: {
            name: "zoomStart"
        },
        onZoomEnd: {
            name: "zoomEnd"
        }
    },
    _fontFields: [
        "legend.".concat(FONT),
        "legend.title.".concat(FONT),
        "legend.title.subtitle.".concat(FONT),
        "commonSeriesSettings.label.".concat(FONT)
    ],
    _rootClassPrefix: "dxc",
    _rootClass: "dxc-chart",
    _initialChanges: [
        "INIT"
    ],
    _themeDependentChanges: [
        "REFRESH_SERIES_REINIT"
    ],
    _getThemeManagerOptions () {
        const themeOptions = this.callBase.apply(this, arguments);
        themeOptions.options = this.option();
        return themeOptions;
    },
    _createThemeManager () {
        const chartOption = this.option();
        const themeManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$chart_theme_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ThemeManager"](this._getThemeManagerOptions());
        themeManager.setTheme(chartOption.theme, chartOption.rtlEnabled);
        return themeManager;
    },
    _initCore () {
        this._canvasClipRect = this._renderer.clipRect();
        this._createHtmlStructure();
        this._createLegend();
        this._createTracker();
        this._needHandleRenderComplete = true;
        this.layoutManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$layout_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LayoutManager"];
        this._createScrollBar();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this._$element, "contextmenu", (event)=>{
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTouchEvent"])(event) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isPointerEvent"])(event)) {
                event.preventDefault();
            }
        });
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this._$element, "MSHoldVisual", (event)=>{
            event.preventDefault();
        });
    },
    _getLayoutItems: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _layoutManagerOptions () {
        return this._themeManager.getOptions("adaptiveLayout");
    },
    _reinit () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCanvasValues"])(this._canvas);
        this._reinitAxes();
        this._requestChange([
            "DATA_SOURCE",
            "DATA_INIT",
            "CORRECT_AXIS",
            "FULL_RENDER"
        ]);
    },
    _correctAxes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _createHtmlStructure () {
        const renderer = this._renderer;
        const { root: root } = renderer;
        const createConstantLinesGroup = function() {
            return renderer.g().attr({
                class: "dxc-constant-lines-group"
            }).linkOn(root, "constant-lines");
        };
        this._constantLinesGroup = {
            dispose () {
                this.under.dispose();
                this.above.dispose();
            },
            linkOff () {
                this.under.linkOff();
                this.above.linkOff();
            },
            clear () {
                this.under.linkRemove().clear();
                this.above.linkRemove().clear();
            },
            linkAppend () {
                this.under.linkAppend();
                this.above.linkAppend();
            }
        };
        this._labelsAxesGroup = renderer.g().attr({
            class: "dxc-elements-axes-group"
        });
        const appendLabelsAxesGroup = ()=>{
            this._labelsAxesGroup.linkOn(root, "elements");
        };
        this._backgroundRect = renderer.rect().attr({
            fill: "gray",
            opacity: 1e-4
        }).append(root);
        this._panesBackgroundGroup = renderer.g().attr({
            class: "dxc-background"
        }).append(root);
        this._stripsGroup = renderer.g().attr({
            class: "dxc-strips-group"
        }).linkOn(root, "strips");
        this._gridGroup = renderer.g().attr({
            class: "dxc-grids-group"
        }).linkOn(root, "grids");
        this._panesBorderGroup = renderer.g().attr({
            class: "dxc-border"
        }).linkOn(root, "border");
        this._axesGroup = renderer.g().attr({
            class: "dxc-axes-group"
        }).linkOn(root, "axes");
        this._executeAppendBeforeSeries(appendLabelsAxesGroup);
        this._stripLabelAxesGroup = renderer.g().attr({
            class: "dxc-strips-labels-group"
        }).linkOn(root, "strips-labels");
        this._constantLinesGroup.under = createConstantLinesGroup();
        this._seriesGroup = renderer.g().attr({
            class: "dxc-series-group"
        }).linkOn(root, "series");
        this._executeAppendAfterSeries(appendLabelsAxesGroup);
        this._constantLinesGroup.above = createConstantLinesGroup();
        this._scaleBreaksGroup = renderer.g().attr({
            class: "dxc-scale-breaks"
        }).linkOn(root, "scale-breaks");
        this._labelsGroup = renderer.g().attr({
            class: "dxc-labels-group"
        }).linkOn(root, "labels");
        this._crosshairCursorGroup = renderer.g().attr({
            class: "dxc-crosshair-cursor"
        }).linkOn(root, "crosshair");
        this._legendGroup = renderer.g().attr({
            class: "dxc-legend",
            "clip-path": this._getCanvasClipRectID()
        }).linkOn(root, "legend").linkAppend(root).enableLinks();
        this._scrollBarGroup = renderer.g().attr({
            class: "dxc-scroll-bar"
        }).linkOn(root, "scroll-bar");
    },
    _executeAppendBeforeSeries () {},
    _executeAppendAfterSeries () {},
    _disposeObjectsInArray (propName, fieldNames) {
        (this[propName] || []).forEach((item)=>{
            if (fieldNames && item) {
                fieldNames.forEach((field)=>{
                    var _item$field;
                    null === (_item$field = item[field]) || void 0 === _item$field || _item$field.dispose();
                });
            } else {
                null === item || void 0 === item || item.dispose();
            }
        });
        this[propName] = null;
    },
    _disposeCore () {
        const disposeObject = (propName)=>{
            if (this[propName]) {
                this[propName].dispose();
                this[propName] = null;
            }
        };
        const unlinkGroup = (name)=>{
            this[name].linkOff();
        };
        const disposeObjectsInArray = this._disposeObjectsInArray;
        this._renderer.stopAllAnimations();
        disposeObjectsInArray.call(this, "series");
        disposeObject("_tracker");
        disposeObject("_crosshair");
        this.layoutManager = this._userOptions = this._canvas = this._groupsData = null;
        unlinkGroup("_stripsGroup");
        unlinkGroup("_gridGroup");
        unlinkGroup("_axesGroup");
        unlinkGroup("_constantLinesGroup");
        unlinkGroup("_stripLabelAxesGroup");
        unlinkGroup("_panesBorderGroup");
        unlinkGroup("_seriesGroup");
        unlinkGroup("_labelsGroup");
        unlinkGroup("_crosshairCursorGroup");
        unlinkGroup("_legendGroup");
        unlinkGroup("_scrollBarGroup");
        unlinkGroup("_scaleBreaksGroup");
        disposeObject("_canvasClipRect");
        disposeObject("_panesBackgroundGroup");
        disposeObject("_backgroundRect");
        disposeObject("_stripsGroup");
        disposeObject("_gridGroup");
        disposeObject("_axesGroup");
        disposeObject("_constantLinesGroup");
        disposeObject("_stripLabelAxesGroup");
        disposeObject("_panesBorderGroup");
        disposeObject("_seriesGroup");
        disposeObject("_labelsGroup");
        disposeObject("_crosshairCursorGroup");
        disposeObject("_legendGroup");
        disposeObject("_scrollBarGroup");
        disposeObject("_scaleBreaksGroup");
    },
    _getAnimationOptions () {
        return this._themeManager.getOptions("animation");
    },
    _getDefaultSize: ()=>({
            width: 400,
            height: 400
        }),
    _getOption (name) {
        return this._themeManager.getOptions(name);
    },
    _applySize (rect) {
        this._rect = rect.slice();
        if (!this._changes.has("FULL_RENDER")) {
            this._processRefreshData("_resize");
        }
    },
    _resize () {
        this._doRender(this.__renderOptions || {
            animate: false,
            isResize: true
        });
    },
    _trackerType: "ChartTracker",
    _createTracker () {
        this._tracker = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$tracker$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__[this._trackerType]({
            seriesGroup: this._seriesGroup,
            renderer: this._renderer,
            tooltip: this._tooltip,
            legend: this._legend,
            eventTrigger: this._eventTrigger
        });
    },
    _getTrackerSettings () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
            chart: this
        }, this._getSelectionModes());
    },
    _getSelectionModes () {
        const themeManager = this._themeManager;
        return {
            seriesSelectionMode: themeManager.getOptions("seriesSelectionMode"),
            pointSelectionMode: themeManager.getOptions("pointSelectionMode")
        };
    },
    _updateTracker (trackerCanvases) {
        this._tracker.update(this._getTrackerSettings());
        this._tracker.setCanvases({
            left: 0,
            right: this._canvas.width,
            top: 0,
            bottom: this._canvas.height
        }, trackerCanvases);
    },
    _createCanvasFromRect (rect) {
        const currentCanvas = this._canvas;
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCanvasValues"])({
            left: rect[0],
            top: rect[1],
            right: currentCanvas.width - rect[2],
            bottom: currentCanvas.height - rect[3],
            width: currentCanvas.width,
            height: currentCanvas.height
        });
    },
    _doRender (_options) {
        if (0 === this._canvas.width && 0 === this._canvas.height) {
            return;
        }
        this._resetIsReady();
        const drawOptions = this._prepareDrawOptions(_options);
        const { recreateCanvas: recreateCanvas } = drawOptions;
        this._preserveOriginalCanvas();
        if (recreateCanvas) {
            this.__currentCanvas = this._canvas;
        } else {
            this._canvas = this.__currentCanvas;
        }
        recreateCanvas && this._updateCanvasClipRect(this._canvas);
        this._canvas = this._createCanvasFromRect(this._rect);
        this._renderer.stopAllAnimations(true);
        this._cleanGroups();
        const startTime = new Date;
        this._renderElements(drawOptions);
        this._lastRenderingTime = Number(new Date) - Number(startTime);
    },
    _preserveOriginalCanvas () {
        this.__originalCanvas = this._canvas;
        this._canvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, this._canvas);
    },
    _layoutAxes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _renderElements (drawOptions) {
        const preparedOptions = this._prepareToRender(drawOptions);
        const isRotated = this._isRotated();
        const isLegendInside = this._isLegendInside();
        const trackerCanvases = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, this._canvas);
        let argBusinessRange;
        let zoomMinArg;
        let zoomMaxArg;
        this._renderer.lock();
        if (drawOptions.drawLegend && this._legend) {
            this._legendGroup.linkAppend();
        }
        this.layoutManager.setOptions(this._layoutManagerOptions());
        const layoutTargets = this._getLayoutTargets();
        this._layoutAxes((needSpace)=>{
            const axisDrawOptions = needSpace ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, drawOptions, {
                animate: false,
                recreateCanvas: true
            }) : drawOptions;
            const canvas = this._renderAxes(axisDrawOptions, preparedOptions);
            this._shrinkAxes(needSpace, canvas);
        });
        this._applyClipRects(preparedOptions);
        this._appendSeriesGroups();
        this._createCrosshairCursor();
        layoutTargets.forEach((_ref)=>{
            let { canvas: canvas } = _ref;
            trackerCanvases.push({
                left: canvas.left,
                right: canvas.width - canvas.right,
                top: canvas.top,
                bottom: canvas.height - canvas.bottom
            });
        });
        if (this._scrollBar) {
            argBusinessRange = this._argumentAxes[0].getTranslator().getBusinessRange();
            if ("discrete" === argBusinessRange.axisType && argBusinessRange.categories && argBusinessRange.categories.length <= 1 || "discrete" !== argBusinessRange.axisType && argBusinessRange.min === argBusinessRange.max) {
                zoomMinArg = zoomMaxArg = void 0;
            } else {
                zoomMinArg = argBusinessRange.minVisible;
                zoomMaxArg = argBusinessRange.maxVisible;
            }
            this._scrollBar.init(argBusinessRange, !this._argumentAxes[0].getOptions().valueMarginsEnabled).setPosition(zoomMinArg, zoomMaxArg);
        }
        this._updateTracker(trackerCanvases);
        this._updateLegendPosition(drawOptions, isLegendInside);
        this._applyPointMarkersAutoHiding();
        this._renderSeries(drawOptions, isRotated, isLegendInside);
        this._renderGraphicObjects();
        this._renderer.unlock();
    },
    _updateLegendPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _createCrosshairCursor: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _appendSeriesGroups () {
        this._seriesGroup.linkAppend();
        this._labelsGroup.linkAppend();
        this._appendAdditionalSeriesGroups();
    },
    _renderSeries (drawOptions, isRotated, isLegendInside) {
        this._calculateSeriesLayout(drawOptions, isRotated);
        this._renderSeriesElements(drawOptions, isLegendInside);
    },
    _calculateSeriesLayout (drawOptions, isRotated) {
        drawOptions.hideLayoutLabels = this.layoutManager.needMoreSpaceForPanesCanvas(this._getLayoutTargets(), isRotated) && !this._themeManager.getOptions("adaptiveLayout").keepLabels;
        this._updateSeriesDimensions(drawOptions);
    },
    _getArgFilter: ()=>()=>true,
    _getValFilter: ()=>()=>true,
    _getPointsToAnimation (series) {
        const argViewPortFilter = this._getArgFilter();
        return series.map((s)=>{
            const valViewPortFilter = this._getValFilter(s);
            return s.getPoints().filter((p)=>p.getOptions().visible && argViewPortFilter(p.argument) && (valViewPortFilter(p.getMinValue(true)) || valViewPortFilter(p.getMaxValue(true)))).length;
        });
    },
    _renderSeriesElements (drawOptions, isLegendInside) {
        const { series: series } = this;
        const resolveLabelOverlapping = this._themeManager.getOptions("resolveLabelOverlapping");
        const pointsToAnimation = this._getPointsToAnimation(series);
        series.forEach((singleSeries, index)=>{
            this._applyExtraSettings(singleSeries, drawOptions);
            const animationEnabled = drawOptions.animate && pointsToAnimation[index] <= drawOptions.animationPointsLimit && this._renderer.animationEnabled();
            singleSeries.draw(animationEnabled, drawOptions.hideLayoutLabels, this._getLegendCallBack(singleSeries));
        });
        if ("none" === resolveLabelOverlapping) {
            this._adjustSeriesLabels(false);
        } else {
            this._locateLabels(resolveLabelOverlapping);
        }
        this._renderTrackers(isLegendInside);
        this._tracker.repairTooltip();
        this._renderExtraElements();
        this._clearCanvas();
        this._seriesElementsDrawn = true;
    },
    _changesApplied () {
        if (this._seriesElementsDrawn) {
            this._seriesElementsDrawn = false;
            this._drawn();
            this._renderCompleteHandler();
        }
    },
    _locateLabels (resolveLabelOverlapping) {
        this._resolveLabelOverlapping(resolveLabelOverlapping);
    },
    _renderExtraElements () {},
    _clearCanvas () {
        this._canvas = this.__originalCanvas;
    },
    _resolveLabelOverlapping (resolveLabelOverlapping) {
        let func;
        switch(resolveLabelOverlapping){
            case "stack":
                func = this._resolveLabelOverlappingStack;
                break;
            case "hide":
                func = this._resolveLabelOverlappingHide;
                break;
            case "shift":
                func = this._resolveLabelOverlappingShift;
        }
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(func) && func.call(this);
    },
    _getVisibleSeries () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["grep"])(this.getAllSeries(), (series)=>series.isVisible());
    },
    _resolveLabelOverlappingHide () {
        const labels = [];
        let currentLabel;
        let nextLabel;
        let currentLabelRect;
        let nextLabelRect;
        let i;
        let j;
        let points;
        const series = this._getVisibleSeries();
        for(i = 0; i < series.length; i++){
            points = series[i].getVisiblePoints();
            for(j = 0; j < points.length; j++){
                labels.push.apply(labels, points[j].getLabels());
            }
        }
        for(i = 0; i < labels.length; i++){
            currentLabel = labels[i];
            if (!currentLabel.isVisible()) {
                continue;
            }
            currentLabelRect = currentLabel.getBoundingRect();
            for(j = i + 1; j < labels.length; j++){
                nextLabel = labels[j];
                nextLabelRect = nextLabel.getBoundingRect();
                if (checkOverlapping(currentLabelRect, nextLabelRect)) {
                    nextLabel.draw(false);
                }
            }
        }
    },
    _cleanGroups () {
        this._stripsGroup.linkRemove().clear();
        this._gridGroup.linkRemove().clear();
        this._axesGroup.linkRemove().clear();
        this._constantLinesGroup.clear();
        this._stripLabelAxesGroup.linkRemove().clear();
        this._labelsGroup.linkRemove().clear();
        this._crosshairCursorGroup.linkRemove().clear();
        this._scaleBreaksGroup.linkRemove().clear();
    },
    _allowLegendInsidePosition: ()=>false,
    _createLegend () {
        const legendSettings = getLegendSettings(this._legendDataField);
        this._legend = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$legend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Legend"]({
            renderer: this._renderer,
            widget: this,
            group: this._legendGroup,
            backgroundClass: "dxc-border",
            itemGroupClass: "dxc-item",
            titleGroupClass: "dxc-title",
            textField: legendSettings.textField,
            getFormatObject: legendSettings.getFormatObject,
            allowInsidePosition: this._allowLegendInsidePosition()
        });
        this._updateLegend();
        this._layout.add(this._legend);
    },
    _updateLegend () {
        const themeManager = this._themeManager;
        const legendOptions = themeManager.getOptions("legend");
        const legendData = this._getLegendData();
        legendOptions.containerBackgroundColor = themeManager.getOptions("containerBackgroundColor");
        legendOptions._incidentOccurred = this._incidentOccurred;
        this._legend.update(legendData, legendOptions, themeManager.theme("legend").title);
        this._change([
            "LAYOUT"
        ]);
    },
    _prepareDrawOptions (drawOptions) {
        const animationOptions = this._getAnimationOptions();
        const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, {
            force: false,
            adjustAxes: true,
            drawLegend: true,
            drawTitle: true,
            animate: animationOptions.enabled,
            animationPointsLimit: animationOptions.maxPointCountSupported
        }, drawOptions, this.__renderOptions);
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(options.recreateCanvas)) {
            options.recreateCanvas = options.adjustAxes && options.drawLegend && options.drawTitle;
        }
        return options;
    },
    _processRefreshData (newRefreshAction) {
        const currentRefreshActionPosition = ACTIONS_BY_PRIORITY.indexOf(this._currentRefreshData);
        const newRefreshActionPosition = ACTIONS_BY_PRIORITY.indexOf(newRefreshAction);
        if (!this._currentRefreshData || currentRefreshActionPosition >= 0 && newRefreshActionPosition < currentRefreshActionPosition) {
            this._currentRefreshData = newRefreshAction;
        }
        this._requestChange([
            "REFRESH"
        ]);
    },
    _getLegendData () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(this._getLegendTargets(), (item)=>{
            const { legendData: legendData } = item;
            const style = item.getLegendStyles;
            let { opacity: opacity } = style.normal;
            if (!item.visible) {
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(opacity) || opacity > .3) {
                    opacity = .3;
                }
                legendData.textOpacity = .3;
            }
            const opacityStyle = {
                opacity: opacity
            };
            legendData.states = {
                hover: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, style.hover, opacityStyle),
                selection: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, style.selection, opacityStyle),
                normal: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, style.normal, opacityStyle)
            };
            return legendData;
        });
    },
    _getLegendOptions (item) {
        return {
            legendData: {
                text: item[this._legendItemTextField],
                id: item.index,
                visible: true
            },
            getLegendStyles: item.getLegendStyles(),
            visible: item.isVisible()
        };
    },
    _disposeSeries (seriesIndex) {
        var _this$series;
        if (this.series) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(seriesIndex)) {
                this.series[seriesIndex].dispose();
                this.series.splice(seriesIndex, 1);
            } else {
                this.series.forEach((s)=>s.dispose());
                this.series.length = 0;
            }
        }
        if (!(null !== (_this$series = this.series) && void 0 !== _this$series && _this$series.length)) {
            this.series = [];
        }
    },
    _disposeSeriesFamilies () {
        (this.seriesFamilies || []).forEach((family)=>{
            family.dispose();
        });
        this.seriesFamilies = null;
        this._needHandleRenderComplete = true;
    },
    _optionChanged (arg) {
        this._themeManager.resetOptions(arg.name);
        this.callBase.apply(this, arguments);
    },
    _applyChanges () {
        this._themeManager.update(this._options.silent());
        this.callBase(...arguments);
    },
    _optionChangesMap: {
        animation: "ANIMATION",
        dataSource: "DATA_SOURCE",
        palette: "PALETTE",
        paletteExtensionMode: "PALETTE",
        legend: "FORCE_DATA_INIT",
        seriesTemplate: "FORCE_DATA_INIT",
        export: "FORCE_RENDER",
        valueAxis: "AXES_AND_PANES",
        argumentAxis: "AXES_AND_PANES",
        commonAxisSettings: "AXES_AND_PANES",
        panes: "AXES_AND_PANES",
        commonPaneSettings: "AXES_AND_PANES",
        defaultPane: "AXES_AND_PANES",
        containerBackgroundColor: "AXES_AND_PANES",
        rotated: "ROTATED",
        autoHidePointMarkers: "REFRESH_SERIES_REINIT",
        customizePoint: "REFRESH_SERIES_REINIT",
        customizeLabel: "REFRESH_SERIES_REINIT",
        scrollBar: "SCROLL_BAR"
    },
    _optionChangesOrder: [
        "ROTATED",
        "PALETTE",
        "REFRESH_SERIES_REINIT",
        "USE_SPIDER_WEB",
        "AXES_AND_PANES",
        "INIT",
        "REINIT",
        "DATA_SOURCE",
        "REFRESH_SERIES_DATA_INIT",
        "DATA_INIT",
        "FORCE_DATA_INIT",
        "REFRESH_AXES",
        "CORRECT_AXIS"
    ],
    _customChangesOrder: [
        "ANIMATION",
        "REFRESH_SERIES_FAMILIES",
        "FORCE_FIRST_DRAWING",
        "FORCE_DRAWING",
        "FORCE_RENDER",
        "VISUAL_RANGE",
        "SCROLL_BAR",
        "REINIT",
        "REFRESH",
        "FULL_RENDER"
    ],
    _change_ANIMATION () {
        this._renderer.updateAnimationOptions(this._getAnimationOptions());
    },
    _change_DATA_SOURCE () {
        this._needHandleRenderComplete = true;
        this._updateDataSource();
    },
    _change_PALETTE () {
        this._themeManager.updatePalette();
        this._refreshSeries("DATA_INIT");
    },
    _change_REFRESH_SERIES_DATA_INIT () {
        this._refreshSeries("DATA_INIT");
    },
    _change_DATA_INIT () {
        if ((!this.series || this.needToPopulateSeries) && !this._changes.has("FORCE_DATA_INIT")) {
            this._dataInit();
        }
    },
    _change_FORCE_DATA_INIT () {
        this._dataInit();
    },
    _change_REFRESH_SERIES_FAMILIES () {
        this._processSeriesFamilies();
        this._populateBusinessRange();
        this._processRefreshData("_forceRender");
    },
    _change_FORCE_RENDER () {
        this._processRefreshData("_forceRender");
    },
    _change_AXES_AND_PANES () {
        this._refreshSeries("INIT");
    },
    _change_ROTATED () {
        this._createScrollBar();
        this._refreshSeries("INIT");
    },
    _change_REFRESH_SERIES_REINIT () {
        this._refreshSeries("INIT");
    },
    _change_REFRESH_AXES () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCanvasValues"])(this._canvas);
        this._reinitAxes();
        this._requestChange([
            "CORRECT_AXIS",
            "FULL_RENDER"
        ]);
    },
    _change_SCROLL_BAR () {
        this._createScrollBar();
        this._processRefreshData("_forceRender");
    },
    _change_REINIT () {
        this._processRefreshData("_reinit");
    },
    _change_FORCE_DRAWING () {
        this._resetComponentsAnimation();
    },
    _change_FORCE_FIRST_DRAWING () {
        this._resetComponentsAnimation(true);
    },
    _resetComponentsAnimation (isFirstDrawing) {
        this.series.forEach((s)=>{
            s.resetApplyingAnimation(isFirstDrawing);
        });
        this._resetAxesAnimation(isFirstDrawing);
    },
    _resetAxesAnimation: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _refreshSeries (actionName) {
        this.needToPopulateSeries = true;
        this._requestChange([
            actionName
        ]);
    },
    _change_CORRECT_AXIS () {
        this._correctAxes();
    },
    _doRefresh () {
        const methodName = this._currentRefreshData;
        if (methodName) {
            this._currentRefreshData = null;
            this._renderer.stopAllAnimations(true);
            this[methodName]();
        }
    },
    _updateCanvasClipRect (canvas) {
        const width = Math.max(canvas.width - canvas.left - canvas.right, 0);
        const height = Math.max(canvas.height - canvas.top - canvas.bottom, 0);
        this._canvasClipRect.attr({
            x: canvas.left,
            y: canvas.top,
            width: width,
            height: height
        });
        this._backgroundRect.attr({
            x: canvas.left,
            y: canvas.top,
            width: width,
            height: height
        });
    },
    _getCanvasClipRectID () {
        return this._canvasClipRect.id;
    },
    _dataSourceChangedHandler () {
        if (this._changes.has("INIT")) {
            this._requestChange([
                "DATA_INIT"
            ]);
        } else {
            this._requestChange([
                "FORCE_DATA_INIT"
            ]);
        }
    },
    _dataInit () {
        this._dataSpecificInit(true);
    },
    _processSingleSeries (singleSeries) {
        singleSeries.createPoints(false);
    },
    _handleSeriesDataUpdated () {
        if (this._getVisibleSeries().some((s)=>s.useAggregation())) {
            this._populateMarginOptions();
        }
        this.series.forEach((s)=>this._processSingleSeries(s), this);
    },
    _dataSpecificInit (needRedraw) {
        if (!this.series || this.needToPopulateSeries) {
            this.series = this._populateSeries();
        }
        this._repopulateSeries();
        this._seriesPopulatedHandlerCore();
        this._populateBusinessRange();
        this._tracker.updateSeries(this.series, this._changes.has("INIT"));
        this._updateLegend();
        if (needRedraw) {
            this._requestChange([
                "FULL_RENDER"
            ]);
        }
    },
    _forceRender () {
        this._doRender({
            force: true
        });
    },
    _repopulateSeries () {
        const themeManager = this._themeManager;
        const data = this._dataSourceItems();
        const dataValidatorOptions = themeManager.getOptions("dataPrepareSettings");
        const seriesTemplate = themeManager.getOptions("seriesTemplate");
        if (seriesTemplate) {
            this._populateSeries(data);
        }
        this._groupSeries();
        const parsedData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$data_validator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateData"])(data, this._groupsData, this._incidentOccurred, dataValidatorOptions);
        themeManager.resetPalette();
        this.series.forEach((singleSeries)=>{
            singleSeries.updateData(parsedData[singleSeries.getArgumentField()]);
        });
        this._handleSeriesDataUpdated();
    },
    _renderCompleteHandler () {
        let allSeriesInited = true;
        if (this._needHandleRenderComplete) {
            this.series.forEach((s)=>{
                allSeriesInited = allSeriesInited && s.canRenderCompleteHandle();
            });
            if (allSeriesInited) {
                this._needHandleRenderComplete = false;
                this._eventTrigger("done", {
                    target: this
                });
            }
        }
    },
    _dataIsReady () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.option("dataSource")) && this._dataIsLoaded();
    },
    _populateSeriesOptions (data) {
        const themeManager = this._themeManager;
        const seriesTemplate = themeManager.getOptions("seriesTemplate");
        const seriesOptions = seriesTemplate ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processSeriesTemplate"])(seriesTemplate, data || []) : this.option("series");
        const allSeriesOptions = isArray(seriesOptions) ? seriesOptions : seriesOptions ? [
            seriesOptions
        ] : [];
        const extraOptions = this._getExtraOptions();
        let particularSeriesOptions;
        let seriesTheme;
        const seriesThemes = [];
        const seriesVisibilityChanged = (target)=>{
            this._specialProcessSeries();
            this._populateBusinessRange(target && target.getValueAxis(), true);
            this._renderer.stopAllAnimations(true);
            this._updateLegend();
            this._requestChange([
                "FULL_RENDER"
            ]);
        };
        for(let i = 0; i < allSeriesOptions.length; i++){
            particularSeriesOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, allSeriesOptions[i], extraOptions);
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(particularSeriesOptions.name) || "" === particularSeriesOptions.name) {
                particularSeriesOptions.name = "Series ".concat((i + 1).toString());
            }
            particularSeriesOptions.rotated = this._isRotated();
            particularSeriesOptions.customizePoint = themeManager.getOptions("customizePoint");
            particularSeriesOptions.customizeLabel = themeManager.getOptions("customizeLabel");
            particularSeriesOptions.visibilityChanged = seriesVisibilityChanged;
            particularSeriesOptions.incidentOccurred = this._incidentOccurred;
            seriesTheme = themeManager.getOptions("series", particularSeriesOptions, allSeriesOptions.length);
            if (this._checkPaneName(seriesTheme)) {
                seriesThemes.push(seriesTheme);
            }
        }
        return seriesThemes;
    },
    _populateSeries (data) {
        var _this$series3;
        const seriesBasis = [];
        const incidentOccurred = this._incidentOccurred;
        const seriesThemes = this._populateSeriesOptions(data);
        let particularSeries;
        let disposeSeriesFamilies = false;
        this.needToPopulateSeries = false;
        seriesThemes.forEach((theme)=>{
            var _this$series2;
            const curSeries = null === (_this$series2 = this.series) || void 0 === _this$series2 ? void 0 : _this$series2.find((s)=>s.name === theme.name && !seriesBasis.map((sb)=>sb.series).includes(s));
            if (curSeries && curSeries.type === theme.type) {
                seriesBasis.push({
                    series: curSeries,
                    options: theme
                });
            } else {
                seriesBasis.push({
                    options: theme
                });
                disposeSeriesFamilies = true;
            }
        });
        0 !== (null === (_this$series3 = this.series) || void 0 === _this$series3 ? void 0 : _this$series3.length) && this._tracker.clearHover();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reverseEach"])(this.series, (index, series)=>{
            if (!seriesBasis.some((s)=>series === s.series)) {
                this._disposeSeries(index);
                disposeSeriesFamilies = true;
            }
        });
        !disposeSeriesFamilies && (disposeSeriesFamilies = seriesBasis.some((sb)=>sb.series.name !== seriesThemes[sb.series.index].name));
        this.series = [];
        disposeSeriesFamilies && this._disposeSeriesFamilies();
        this._themeManager.resetPalette();
        const eventPipe = (data)=>{
            this.series.forEach((currentSeries)=>{
                currentSeries.notify(data);
            });
        };
        seriesBasis.forEach((basis)=>{
            var _this$_argumentAxes;
            const seriesTheme = basis.options;
            var _ref;
            const argumentAxis = (_ref = null === (_this$_argumentAxes = this._argumentAxes) || void 0 === _this$_argumentAxes ? void 0 : _this$_argumentAxes.filter((a)=>a.pane === seriesTheme.pane)[0]) !== null && _ref !== void 0 ? _ref : this.getArgumentAxis();
            const renderSettings = {
                commonSeriesModes: this._getSelectionModes(),
                argumentAxis: argumentAxis,
                valueAxis: this._getValueAxis(seriesTheme.pane, seriesTheme.axis)
            };
            if (basis.series) {
                particularSeries = basis.series;
                particularSeries.updateOptions(seriesTheme, renderSettings);
            } else {
                particularSeries = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$base_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Series"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                    renderer: this._renderer,
                    seriesGroup: this._seriesGroup,
                    labelsGroup: this._labelsGroup,
                    eventTrigger: this._eventTrigger,
                    eventPipe: eventPipe,
                    incidentOccurred: incidentOccurred
                }, renderSettings), seriesTheme);
            }
            if (!particularSeries.isUpdated) {
                incidentOccurred("E2101", [
                    seriesTheme.type
                ]);
            } else {
                particularSeries.index = this.series.length;
                this.series.push(particularSeries);
            }
        });
        return this.series;
    },
    getStackedPoints (point) {
        const stackName = point.series.getStackName();
        return this._getVisibleSeries().reduce((stackPoints, series)=>{
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(series.getStackName()) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(stackName) || stackName === series.getStackName()) {
                stackPoints = stackPoints.concat(series.getPointsByArg(point.argument));
            }
            return stackPoints;
        }, []);
    },
    getAllSeries: function() {
        return (this.series || []).slice();
    },
    getSeriesByName: function(name) {
        const found = (this.series || []).find((singleSeries)=>singleSeries.name === name);
        return found || null;
    },
    getSeriesByPos: function(pos) {
        return (this.series || [])[pos];
    },
    clearSelection: function() {
        this._tracker.clearSelection();
    },
    hideTooltip () {
        this._tracker._hideTooltip();
    },
    clearHover () {
        this._tracker.clearHover();
    },
    render (renderOptions) {
        this.__renderOptions = renderOptions;
        this.__forceRender = renderOptions && renderOptions.force;
        this.callBase.apply(this, arguments);
        this.__renderOptions = this.__forceRender = null;
        return this;
    },
    refresh () {
        this._disposeSeries();
        this._disposeSeriesFamilies();
        this._requestChange([
            "CONTAINER_SIZE",
            "REFRESH_SERIES_REINIT"
        ]);
    },
    _getMinSize () {
        const adaptiveLayout = this._layoutManagerOptions();
        return [
            adaptiveLayout.width,
            adaptiveLayout.height
        ];
    },
    _change_REFRESH () {
        if (!this._changes.has("INIT")) {
            this._doRefresh();
        } else {
            this._currentRefreshData = null;
        }
    },
    _change_FULL_RENDER () {
        this._forceRender();
    },
    _change_INIT () {
        this._reinit();
    },
    _stopCurrentHandling () {
        if (this._disposed) {
            return;
        }
        this._tracker.stopCurrentHandling();
    }
});
REFRESH_SERIES_DATA_INIT_ACTION_OPTIONS.forEach((name)=>{
    BaseChart.prototype._optionChangesMap[name] = "REFRESH_SERIES_DATA_INIT";
});
FORCE_RENDER_REFRESH_ACTION_OPTIONS.forEach((name)=>{
    BaseChart.prototype._optionChangesMap[name] = "FORCE_RENDER";
});
REFRESH_SERIES_FAMILIES_ACTION_OPTIONS.forEach((name)=>{
    BaseChart.prototype._optionChangesMap[name] = "REFRESH_SERIES_FAMILIES";
});
BaseChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$export$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugin"]);
BaseChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$title$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugin"]);
BaseChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$data_source$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugin"]);
BaseChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$tooltip$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugin"]);
BaseChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$loading_indicator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugin"]);
const { _change_TITLE: _change_TITLE } = BaseChart.prototype;
BaseChart.prototype._change_TITLE = function() {
    _change_TITLE.apply(this, arguments);
    this._change([
        "FORCE_RENDER"
    ]);
};
}),
"[project]/node_modules/devextreme/esm/__internal/viz/chart_components/m_advanced_chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/chart_components/m_advanced_chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "AdvancedChart": ()=>AdvancedChart
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$base_axis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/axes/base_axis.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$series_family$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/series_family.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/helpers/range_data_calculator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/range.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$m_base_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/chart_components/m_base_chart.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const { isArray: isArray } = Array;
const DEFAULT_AXIS_NAME = "defaultAxisName";
const FONT = "font";
const COMMON_AXIS_SETTINGS = "commonAxisSettings";
const DEFAULT_PANE_NAME = "default";
const VISUAL_RANGE = "VISUAL_RANGE";
function prepareAxis(axisOptions) {
    if (isArray(axisOptions)) {
        return 0 === axisOptions.length ? [
            {}
        ] : axisOptions;
    }
    return [
        axisOptions
    ];
}
function processBubbleMargin(marginOptions, bubbleSize) {
    if (marginOptions.processBubbleSize) {
        marginOptions.size = bubbleSize;
    }
    return marginOptions;
}
function estimateBubbleSize(size, panesCount, maxSize, rotated) {
    const width = rotated ? size.width / panesCount : size.width;
    const height = rotated ? size.height : size.height / panesCount;
    return Math.min(width, height) * maxSize;
}
function setAxisVisualRangeByOption(arg, axis, isDirectOption, index) {
    let options;
    let visualRange;
    if (isDirectOption) {
        visualRange = arg.value;
        options = {
            skipEventRising: true
        };
        const wrappedVisualRange = wrapVisualRange(arg.fullName, visualRange);
        if (wrappedVisualRange) {
            options = {
                allowPartialUpdate: true
            };
            visualRange = wrappedVisualRange;
        }
    } else {
        visualRange = ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(index) ? arg.value[index] : arg.value).visualRange;
    }
    axis.visualRange(visualRange, options);
}
function getAxisTypes(groupsData, axis, isArgumentAxes) {
    if (isArgumentAxes) {
        return {
            argumentAxisType: groupsData.argumentAxisType,
            argumentType: groupsData.argumentType
        };
    }
    const { valueAxisType: valueAxisType, valueType: valueType } = groupsData.groups.find((g)=>g.valueAxis === axis);
    return {
        valueAxisType: valueAxisType,
        valueType: valueType
    };
}
function wrapVisualRange(fullName, value) {
    const pathElements = fullName.split(".");
    const destElem = pathElements.at(-1);
    if ("endValue" === destElem || "startValue" === destElem) {
        return {
            [destElem]: value
        };
    }
    return;
}
const AdvancedChart = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$m_base_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["BaseChart"].inherit({
    _fontFields: [
        "commonAxisSettings.label.".concat(FONT),
        "commonAxisSettings.title.".concat(FONT)
    ],
    _partialOptionChangesMap: {
        visualRange: VISUAL_RANGE,
        _customVisualRange: VISUAL_RANGE,
        strips: "REFRESH_AXES",
        constantLines: "REFRESH_AXES"
    },
    _partialOptionChangesPath: {
        argumentAxis: [
            "strips",
            "constantLines",
            "visualRange",
            "_customVisualRange"
        ],
        valueAxis: [
            "strips",
            "constantLines",
            "visualRange",
            "_customVisualRange"
        ]
    },
    _initCore () {
        this._panesClipRects = {};
        this.callBase();
    },
    _disposeCore () {
        const disposeObjectsInArray = this._disposeObjectsInArray;
        const panesClipRects = this._panesClipRects;
        this.callBase();
        disposeObjectsInArray.call(panesClipRects, "fixed");
        disposeObjectsInArray.call(panesClipRects, "base");
        disposeObjectsInArray.call(panesClipRects, "wide");
        this._panesClipRects = null;
        this._labelsAxesGroup.linkOff();
        this._labelsAxesGroup.dispose();
        this._labelsAxesGroup = null;
    },
    _dispose () {
        const disposeObjectsInArray = this._disposeObjectsInArray;
        this.callBase();
        this.panes = null;
        if (this._legend) {
            this._legend.dispose();
            this._legend = null;
        }
        disposeObjectsInArray.call(this, "panesBackground");
        disposeObjectsInArray.call(this, "seriesFamilies");
        this._disposeAxes();
    },
    _createPanes () {
        this._cleanPanesClipRects("fixed");
        this._cleanPanesClipRects("base");
        this._cleanPanesClipRects("wide");
    },
    _cleanPanesClipRects (clipArrayName) {
        const clipArray = this._panesClipRects[clipArrayName];
        (clipArray || []).forEach((clipRect)=>{
            null === clipRect || void 0 === clipRect || clipRect.dispose();
        });
        this._panesClipRects[clipArrayName] = [];
    },
    _getElementsClipRectID (paneName) {
        const clipShape = this._panesClipRects.fixed[this._getPaneIndex(paneName)];
        return null === clipShape || void 0 === clipShape ? void 0 : clipShape.id;
    },
    _getPaneIndex (paneName) {
        const name = paneName || "default";
        return this.panes.findIndex((pane)=>pane.name === name);
    },
    _updateSize (forceUpdateCanvas) {
        this.callBase();
        if (forceUpdateCanvas && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["areCanvasesDifferent"])(this.__currentCanvas, this._canvas)) {
            this.__currentCanvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["floorCanvasDimensions"])(this._canvas);
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setCanvasValues"])(this._canvas);
    },
    _reinitAxes () {
        this.panes = this._createPanes();
        this._populateAxes();
        this._axesReinitialized = true;
    },
    _populateAxes () {
        const { panes: panes } = this;
        const rotated = this._isRotated();
        const argumentAxesOptions = prepareAxis(this.option("argumentAxis") || {})[0];
        const valueAxisOption = this.option("valueAxis");
        const valueAxesOptions = prepareAxis(valueAxisOption || {});
        let argumentAxesPopulatedOptions = [];
        const valueAxesPopulatedOptions = [];
        const axisNames = [];
        let valueAxesCounter = 0;
        let paneWithNonVirtualAxis;
        const crosshairMargins = this._getCrosshairMargins();
        function getNextAxisName() {
            const name = "defaultAxisName" + String(valueAxesCounter);
            valueAxesCounter += 1;
            return name;
        }
        if (rotated) {
            paneWithNonVirtualAxis = "right" === argumentAxesOptions.position ? panes[panes.length - 1].name : panes[0].name;
        } else {
            paneWithNonVirtualAxis = "top" === argumentAxesOptions.position ? panes[0].name : panes[panes.length - 1].name;
        }
        argumentAxesPopulatedOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(panes, (pane)=>{
            const virtual = pane.name !== paneWithNonVirtualAxis;
            return this._populateAxesOptions("argumentAxis", argumentAxesOptions, {
                pane: pane.name,
                name: null,
                optionPath: "argumentAxis",
                crosshairMargin: rotated ? crosshairMargins.x : crosshairMargins.y
            }, rotated, virtual);
        });
        valueAxesOptions.forEach((axisOptions, priority)=>{
            var _axisOptions$panes;
            let axisPanes = [];
            const { name: name } = axisOptions;
            if (name && axisNames.includes(name)) {
                this._incidentOccurred("E2102");
                return;
            }
            if (name) {
                axisNames.push(name);
            }
            if (axisOptions.pane) {
                axisPanes.push(axisOptions.pane);
            }
            if (null !== (_axisOptions$panes = axisOptions.panes) && void 0 !== _axisOptions$panes && _axisOptions$panes.length) {
                axisPanes = axisPanes.concat(axisOptions.panes.slice(0));
            }
            axisPanes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unique"])(axisPanes);
            if (!axisPanes.length) {
                axisPanes.push(void 0);
            }
            axisPanes.forEach((pane)=>{
                const optionPath = isArray(valueAxisOption) ? "valueAxis[".concat(String(priority), "]") : "valueAxis";
                valueAxesPopulatedOptions.push(this._populateAxesOptions("valueAxis", axisOptions, {
                    name: name || getNextAxisName(),
                    pane: pane,
                    priority: priority,
                    optionPath: optionPath,
                    crosshairMargin: rotated ? crosshairMargins.y : crosshairMargins.x
                }, rotated));
            });
        });
        this._redesignAxes(argumentAxesPopulatedOptions, true, paneWithNonVirtualAxis);
        this._redesignAxes(valueAxesPopulatedOptions, false);
    },
    _redesignAxes (options, isArgumentAxes, paneWithNonVirtualAxis) {
        const axesBasis = [];
        let axes = isArgumentAxes ? this._argumentAxes : this._valueAxes;
        options.forEach((opt)=>{
            var _axes;
            const curAxes = null === (_axes = axes) || void 0 === _axes ? void 0 : _axes.filter((a)=>a.name === opt.name && (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(opt.pane) && this.panes.some((p)=>p.name === a.pane) || a.pane === opt.pane));
            if (null !== curAxes && void 0 !== curAxes && curAxes.length) {
                curAxes.forEach((axis)=>{
                    const axisTypes = getAxisTypes(this._groupsData, axis, isArgumentAxes);
                    axis.updateOptions(opt);
                    if (isArgumentAxes) {
                        axis.setTypes(axisTypes.argumentAxisType, axisTypes.argumentType, "argumentType");
                    } else {
                        axis.setTypes(axisTypes.valueAxisType, axisTypes.valueType, "valueType");
                    }
                    axis.validate();
                    axesBasis.push({
                        axis: axis
                    });
                });
            } else {
                axesBasis.push({
                    options: opt
                });
            }
        });
        if (axes) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["reverseEach"])(axes, (index, axis)=>{
                if (!axesBasis.some((basis)=>basis.axis && basis.axis === axis)) {
                    this._disposeAxis(index, isArgumentAxes);
                }
            });
        } else if (isArgumentAxes) {
            axes = this._argumentAxes = [];
        } else {
            axes = this._valueAxes = [];
        }
        axesBasis.forEach((basis)=>{
            let { axis: axis } = basis;
            if (basis.axis && isArgumentAxes) {
                basis.axis.isVirtual = basis.axis.pane !== paneWithNonVirtualAxis;
            } else if (basis.options) {
                axis = this._createAxis(isArgumentAxes, basis.options, isArgumentAxes ? basis.options.pane !== paneWithNonVirtualAxis : void 0);
                axes.push(axis);
            }
            axis.applyVisualRangeSetter(this._getVisualRangeSetter());
        });
    },
    _disposeAxis (index, isArgumentAxis) {
        const axes = isArgumentAxis ? this._argumentAxes : this._valueAxes;
        const axis = axes[index];
        if (!axis) {
            return;
        }
        axis.dispose();
        axes.splice(index, 1);
    },
    _disposeAxes () {
        const disposeObjectsInArray = this._disposeObjectsInArray;
        disposeObjectsInArray.call(this, "_argumentAxes");
        disposeObjectsInArray.call(this, "_valueAxes");
    },
    _appendAdditionalSeriesGroups () {
        this._crosshairCursorGroup.linkAppend();
        if (this._scrollBar) {
            this._scrollBarGroup.linkAppend();
        }
    },
    _getLegendTargets () {
        return (this.series || []).map((s)=>{
            const item = this._getLegendOptions(s);
            item.legendData.series = s;
            if (!s.getOptions().showInLegend) {
                item.legendData.visible = false;
            }
            return item;
        });
    },
    _legendItemTextField: "name",
    _seriesPopulatedHandlerCore () {
        this._processSeriesFamilies();
        this._processValueAxisFormat();
    },
    _renderTrackers () {
        for(let i = 0; i < this.series.length; i += 1){
            this.series[i].drawTrackers();
        }
    },
    _specialProcessSeries () {
        this._processSeriesFamilies();
    },
    _processSeriesFamilies () {
        var _this$seriesFamilies;
        const types = [];
        const families = [];
        let paneSeries;
        const themeManager = this._themeManager;
        const negativesAsZeroes = themeManager.getOptions("negativesAsZeroes");
        const negativesAsZeros = themeManager.getOptions("negativesAsZeros");
        const familyOptions = {
            minBubbleSize: themeManager.getOptions("minBubbleSize"),
            maxBubbleSize: themeManager.getOptions("maxBubbleSize"),
            barGroupPadding: themeManager.getOptions("barGroupPadding"),
            barGroupWidth: themeManager.getOptions("barGroupWidth"),
            negativesAsZeroes: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(negativesAsZeroes) ? negativesAsZeroes : negativesAsZeros
        };
        if (null !== (_this$seriesFamilies = this.seriesFamilies) && void 0 !== _this$seriesFamilies && _this$seriesFamilies.length) {
            this.seriesFamilies.forEach((family)=>{
                family.updateOptions(familyOptions);
                family.adjustSeriesValues();
            });
            return;
        }
        this.series.forEach((item)=>{
            if (!types.includes(item.type)) {
                types.push(item.type);
            }
        });
        this._getLayoutTargets().forEach((pane)=>{
            paneSeries = this._getSeriesForPane(pane.name);
            types.forEach((type)=>{
                const family = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$series_family$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["SeriesFamily"]({
                    type: type,
                    pane: pane.name,
                    minBubbleSize: familyOptions.minBubbleSize,
                    maxBubbleSize: familyOptions.maxBubbleSize,
                    barGroupPadding: familyOptions.barGroupPadding,
                    barGroupWidth: familyOptions.barGroupWidth,
                    negativesAsZeroes: familyOptions.negativesAsZeroes,
                    rotated: this._isRotated()
                });
                family.add(paneSeries);
                family.adjustSeriesValues();
                families.push(family);
            });
        });
        this.seriesFamilies = families;
    },
    _updateSeriesDimensions () {
        const seriesFamilies = this.seriesFamilies || [];
        for(let i = 0; i < seriesFamilies.length; i += 1){
            const family = seriesFamilies[i];
            family.updateSeriesValues();
            family.adjustSeriesDimensions();
        }
    },
    _getLegendCallBack (series) {
        var _this$_legend;
        return null === (_this$_legend = this._legend) || void 0 === _this$_legend ? void 0 : _this$_legend.getActionCallback(series);
    },
    _appendAxesGroups () {
        this._stripsGroup.linkAppend();
        this._gridGroup.linkAppend();
        this._axesGroup.linkAppend();
        this._labelsAxesGroup.linkAppend();
        this._constantLinesGroup.linkAppend();
        this._stripLabelAxesGroup.linkAppend();
        this._scaleBreaksGroup.linkAppend();
    },
    _populateMarginOptions () {
        const bubbleSize = estimateBubbleSize(this.getSize(), this.panes.length, this._themeManager.getOptions("maxBubbleSize"), this._isRotated());
        let argumentMarginOptions = {};
        this._valueAxes.forEach((valueAxis)=>{
            const groupSeries = this.series.filter((series)=>series.getValueAxis() === valueAxis);
            let marginOptions = {};
            groupSeries.forEach((series)=>{
                if (series.isVisible()) {
                    const seriesMarginOptions = processBubbleMargin(series.getMarginOptions(), bubbleSize);
                    marginOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeMarginOptions"])(marginOptions, seriesMarginOptions);
                    argumentMarginOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeMarginOptions"])(argumentMarginOptions, seriesMarginOptions);
                }
            });
            valueAxis.setMarginOptions(marginOptions);
        });
        this._argumentAxes.forEach((a)=>a.setMarginOptions(argumentMarginOptions));
    },
    _populateBusinessRange (updatedAxis, keepRange) {
        const rotated = this._isRotated();
        const series = this._getVisibleSeries();
        const argRanges = {};
        const commonArgRange = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"]({
            rotated: !!rotated
        });
        const getPaneName = (axis)=>axis.pane || "default";
        this.panes.forEach((p)=>{
            argRanges[p.name] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"]({
                rotated: !!rotated
            });
        });
        this._valueAxes.forEach((valueAxis)=>{
            const groupRange = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"]({
                rotated: !!rotated,
                pane: valueAxis.pane,
                axis: valueAxis.name
            });
            const groupSeries = series.filter((series)=>series.getValueAxis() === valueAxis);
            groupSeries.forEach((series)=>{
                const seriesRange = series.getRangeData();
                groupRange.addRange(seriesRange.val);
                argRanges[getPaneName(valueAxis)].addRange(seriesRange.arg);
            });
            if (!updatedAxis || updatedAxis && groupSeries.length && valueAxis === updatedAxis) {
                valueAxis.setGroupSeries(groupSeries);
                valueAxis.setBusinessRange(groupRange, this._axesReinitialized || keepRange, this._argumentAxes[0]._lastVisualRangeUpdateMode);
            }
        });
        if (!updatedAxis || updatedAxis && series.length) {
            Object.keys(argRanges).forEach((p)=>commonArgRange.addRange(argRanges[p]));
            const commonInterval = commonArgRange.interval;
            this._argumentAxes.forEach((a)=>{
                var _argRanges_getPaneName_interval;
                const currentInterval = (_argRanges_getPaneName_interval = argRanges[getPaneName(a)].interval) !== null && _argRanges_getPaneName_interval !== void 0 ? _argRanges_getPaneName_interval : commonInterval;
                a.setBusinessRange(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"]((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, commonArgRange, {
                    interval: currentInterval
                })), this._axesReinitialized, void 0, this._groupsData.categories);
            });
        }
        this._populateMarginOptions();
    },
    getArgumentAxis () {
        return (this._argumentAxes || []).find((a)=>!a.isVirtual);
    },
    getValueAxis (name) {
        return (this._valueAxes || []).find((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(name) ? (a)=>a.name === name : (a)=>a.pane === this.defaultPane);
    },
    _getGroupsData () {
        const groups = [];
        this._valueAxes.forEach((axis)=>{
            groups.push({
                series: this.series.filter((series)=>series.getValueAxis() === axis),
                valueAxis: axis,
                valueOptions: axis.getOptions()
            });
        });
        return {
            groups: groups,
            argumentAxes: this._argumentAxes,
            argumentOptions: this._argumentAxes[0].getOptions()
        };
    },
    _groupSeries () {
        this._correctValueAxes(false);
        this._groupsData = this._getGroupsData();
    },
    _processValueAxisFormat () {
        const axesWithFullStackedFormat = [];
        this.series.forEach((series)=>{
            const axis = series.getValueAxis();
            if (series.isFullStackedSeries()) {
                axis.setPercentLabelFormat();
                axesWithFullStackedFormat.push(axis);
            }
        });
        this._valueAxes.forEach((axis)=>{
            if (!axesWithFullStackedFormat.includes(axis)) {
                axis.resetAutoLabelFormat();
            }
        });
    },
    _populateAxesOptions (typeSelector, userOptions, axisOptions, rotated, virtual) {
        const preparedUserOptions = this._prepareStripsAndConstantLines(typeSelector, userOptions, rotated);
        const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, preparedUserOptions, axisOptions, this._prepareAxisOptions(typeSelector, preparedUserOptions, rotated));
        if (virtual) {
            options.visible = false;
            options.tick.visible = false;
            options.minorTick.visible = false;
            options.label.visible = false;
            options.title = {};
        }
        return options;
    },
    _getValFilter: (series)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getViewPortFilter(series.getValueAxis().visualRange() || {}),
    _createAxis (isArgumentAxes, options, virtual) {
        const typeSelector = isArgumentAxes ? "argumentAxis" : "valueAxis";
        const renderingSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
            renderer: this._renderer,
            incidentOccurred: this._incidentOccurred,
            eventTrigger: this._eventTrigger,
            axisClass: isArgumentAxes ? "arg" : "val",
            widgetClass: "dxc",
            stripsGroup: this._stripsGroup,
            stripLabelAxesGroup: this._stripLabelAxesGroup,
            constantLinesGroup: this._constantLinesGroup,
            scaleBreaksGroup: this._scaleBreaksGroup,
            axesContainerGroup: this._axesGroup,
            labelsAxesGroup: this._labelsAxesGroup,
            gridGroup: this._gridGroup,
            isArgumentAxis: isArgumentAxes,
            getTemplate: (template)=>this._getTemplate(template)
        }, this._getAxisRenderingOptions(typeSelector));
        const axis = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$axes$2f$base_axis$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Axis"](renderingSettings);
        axis.updateOptions(options);
        axis.isVirtual = virtual;
        return axis;
    },
    _applyVisualRangeByVirtualAxes: ()=>false,
    _applyCustomVisualRangeOption (axis, range) {
        if (axis.getOptions().optionPath) {
            this._parseVisualRangeOption("".concat(axis.getOptions().optionPath, ".visualRange"), range);
        }
    },
    _getVisualRangeSetter () {
        return (axis, _ref)=>{
            let { skipEventRising: skipEventRising, range: range } = _ref;
            this._applyCustomVisualRangeOption(axis, range);
            axis.setCustomVisualRange(range);
            axis.skipEventRising = skipEventRising;
            if (!this._applyVisualRangeByVirtualAxes(axis, range)) {
                if (this._applyingChanges) {
                    this._change_VISUAL_RANGE();
                } else {
                    this._requestChange([
                        VISUAL_RANGE
                    ]);
                }
            }
        };
    },
    _getTrackerSettings () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this.callBase(), {
            argumentAxis: this.getArgumentAxis()
        });
    },
    _prepareStripsAndConstantLines (typeSelector, userOptions, rotated) {
        userOptions = this._themeManager.getOptions(typeSelector, userOptions, rotated);
        if (userOptions.strips) {
            userOptions.strips.forEach((line, i)=>{
                userOptions.strips[i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, userOptions.stripStyle, line);
            });
        }
        if (userOptions.constantLines) {
            userOptions.constantLines.forEach((line, i)=>{
                userOptions.constantLines[i] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, userOptions.constantLineStyle, line);
            });
        }
        return userOptions;
    },
    refresh () {
        this._disposeAxes();
        this.callBase();
    },
    _layoutAxes (drawAxes) {
        drawAxes();
        const needSpace = this.checkForMoreSpaceForPanesCanvas();
        if (needSpace) {
            const rect = this._rect.slice();
            const size = this._layout.backward(rect, rect, [
                needSpace.width,
                needSpace.height
            ]);
            needSpace.width = Math.max(0, size[0]);
            needSpace.height = Math.max(0, size[1]);
            this._canvas = this._createCanvasFromRect(rect);
            drawAxes(needSpace);
        }
    },
    checkForMoreSpaceForPanesCanvas () {
        return this.layoutManager.needMoreSpaceForPanesCanvas(this._getLayoutTargets(), this._isRotated());
    },
    _parseVisualRangeOption (fullName, value) {
        const name = fullName.split(/[.[]/)[0];
        let index = fullName.match(/\d+/g);
        index = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(index) ? parseInt(index[0], 10) : index;
        if (fullName.indexOf("visualRange") > 0) {
            if ("object" !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(value)) {
                var _wrapVisualRange;
                value = (_wrapVisualRange = wrapVisualRange(fullName, value)) !== null && _wrapVisualRange !== void 0 ? _wrapVisualRange : value;
            }
            this._setCustomVisualRange(name, index, value);
        } else if (("object" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(value) || isArray(value)) && name.indexOf("Axis") > 0 && JSON.stringify(value).indexOf("visualRange") > 0) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value.visualRange)) {
                this._setCustomVisualRange(name, index, value.visualRange);
            } else if (isArray(value)) {
                value.forEach((a, i)=>{
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(a.visualRange)) {
                        this._setCustomVisualRange(name, i, a.visualRange);
                    }
                });
            }
        }
    },
    _setCustomVisualRange (axesName, index, value) {
        const options = this._options.silent(axesName);
        if (!options) {
            return;
        }
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(index)) {
            options._customVisualRange = value;
        } else {
            options[index]._customVisualRange = value;
        }
        this._axesReinitialized = true;
    },
    _raiseZoomEndHandlers () {
        this._valueAxes.forEach((axis)=>axis.handleZoomEnd());
    },
    _setOptionsByReference () {
        this.callBase();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this._optionsByReference, {
            "valueAxis.visualRange": true
        });
    },
    _notifyOptionChanged (option, value) {
        this.callBase.apply(this, arguments);
        if (!this._optionChangedLocker) {
            this._parseVisualRangeOption(option, value);
        }
    },
    _notifyVisualRange () {
        this._valueAxes.forEach((axis)=>{
            const axisPath = axis.getOptions().optionPath;
            if (axisPath) {
                const path = "".concat(axisPath, ".visualRange");
                const visualRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertVisualRangeObject"])(axis.visualRange(), !isArray(this.option(path)));
                if (!axis.skipEventRising || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rangesAreEqual"])(visualRange, this.option(path))) {
                    if (!this.option(axisPath) && "valueAxis" !== axisPath) {
                        this.option(axisPath, {
                            name: axis.name,
                            visualRange: visualRange
                        });
                    } else {
                        this.option(path, visualRange);
                    }
                } else {
                    axis.skipEventRising = null;
                }
            }
        });
    },
    _notify () {
        this.callBase();
        this._axesReinitialized = false;
        if (true !== this.option("disableTwoWayBinding")) {
            this.skipOptionsRollBack = true;
            this._notifyVisualRange();
            this.skipOptionsRollBack = false;
        }
    },
    _getAxesForScaling () {
        return this._valueAxes;
    },
    _getAxesByOptionPath (arg, isDirectOption, optionName) {
        const sourceAxes = this._getAxesForScaling();
        let axes = [];
        if (isDirectOption) {
            let axisPath;
            if (arg.fullName) {
                axisPath = arg.fullName.slice(0, arg.fullName.indexOf("."));
            }
            axes = sourceAxes.filter((a)=>a.getOptions().optionPath === axisPath);
        } else if ("object" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(arg.value)) {
            axes = sourceAxes.filter((a)=>a.getOptions().optionPath === arg.name);
        } else if (isArray(arg.value)) {
            arg.value.forEach((v, index)=>{
                const axis = sourceAxes.filter((a)=>a.getOptions().optionPath === "".concat(arg.name, "[").concat(index, "]"))[0];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(v[optionName]) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(axis)) {
                    axes[index] = axis;
                }
            });
        }
        return axes;
    },
    _optionChanged (arg) {
        if (!this._optionChangedLocker) {
            const optionName = "visualRange";
            let axes;
            const isDirectOption = arg.fullName.indexOf(optionName) > 0 ? true : this.getPartialChangeOptionsName(arg).indexOf(optionName) > -1 ? false : void 0;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(isDirectOption)) {
                axes = this._getAxesByOptionPath(arg, isDirectOption, optionName);
                if (axes) {
                    if (axes.length > 1 || isArray(arg.value)) {
                        axes.forEach((a, index)=>setAxisVisualRangeByOption(arg, a, isDirectOption, index));
                    } else if (1 === axes.length) {
                        setAxisVisualRangeByOption(arg, axes[0], isDirectOption);
                    }
                }
            }
        }
        this.callBase(arg);
    },
    _change_VISUAL_RANGE () {
        this._recreateSizeDependentObjects(false);
        if (!this._changes.has("FULL_RENDER")) {
            const resizePanesOnZoom = this.option("resizePanesOnZoom");
            this._doRender({
                force: true,
                drawTitle: false,
                drawLegend: false,
                adjustAxes: resizePanesOnZoom !== null && resizePanesOnZoom !== void 0 ? resizePanesOnZoom : this.option("adjustAxesOnZoom") || false,
                animate: false
            });
            this._raiseZoomEndHandlers();
        }
    },
    resetVisualRange () {
        this._valueAxes.forEach((axis)=>{
            axis.resetVisualRange(false);
            this._applyCustomVisualRangeOption(axis);
        });
        this._requestChange([
            VISUAL_RANGE
        ]);
    },
    _getCrosshairMargins: ()=>({
            x: 0,
            y: 0
        }),
    _legendDataField: "series",
    _adjustSeriesLabels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _correctValueAxes: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"]
});
}),
"[project]/node_modules/devextreme/esm/__internal/viz/m_chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/viz/m_chart.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/component_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$crosshair$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/crosshair.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$layout_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/layout_manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$multi_axes_synchronizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/multi_axes_synchronizer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$scroll_bar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/scroll_bar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$shutter_zoom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/shutter_zoom.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$zoom_and_pan$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart_components/zoom_and_pan.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$annotations$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/annotations.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/helpers/range_data_calculator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/translators/range.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/utils.js [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$m_advanced_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/chart_components/m_advanced_chart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$m_base_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/viz/chart_components/m_base_chart.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const DEFAULT_PANE_NAME = "default";
const VISUAL_RANGE = "VISUAL_RANGE";
const DEFAULT_PANES = [
    {
        name: "default",
        border: {}
    }
];
const DISCRETE = "discrete";
const { isArray: isArray } = Array;
function getFirstAxisNameForPane(axes, paneName, defaultPane) {
    let result;
    for(let i = 0; i < axes.length; i += 1){
        if (axes[i].pane === paneName || void 0 === axes[i].pane && paneName === defaultPane) {
            result = axes[i].name;
            break;
        }
    }
    if (!result) {
        result = axes[0].name;
    }
    return result;
}
function changeVisibilityAxisGrids(axis, gridVisibility, minorGridVisibility) {
    const gridOpt = axis.getOptions().grid;
    const minorGridOpt = axis.getOptions().minorGrid;
    gridOpt.visible = gridVisibility;
    minorGridOpt && (minorGridOpt.visible = minorGridVisibility);
}
function hideGridsOnNonFirstValueAxisForPane(axesForPane) {
    let axisShown = false;
    const hiddenStubAxis = [];
    const minorGridVisibility = axesForPane.some((axis)=>{
        const minorGridOptions = axis.getOptions().minorGrid;
        return null === minorGridOptions || void 0 === minorGridOptions ? void 0 : minorGridOptions.visible;
    });
    const gridVisibility = axesForPane.some((axis)=>{
        const gridOptions = axis.getOptions().grid;
        return null === gridOptions || void 0 === gridOptions ? void 0 : gridOptions.visible;
    });
    if (axesForPane.length > 1) {
        axesForPane.forEach((axis)=>{
            const gridOpt = axis.getOptions().grid;
            if (axisShown) {
                changeVisibilityAxisGrids(axis, false, false);
            } else if (null !== gridOpt && void 0 !== gridOpt && gridOpt.visible) {
                if (axis.getTranslator().getBusinessRange().isEmpty()) {
                    changeVisibilityAxisGrids(axis, false, false);
                    hiddenStubAxis.push(axis);
                } else {
                    axisShown = true;
                    changeVisibilityAxisGrids(axis, gridVisibility, minorGridVisibility);
                }
            }
        });
        if (!axisShown && hiddenStubAxis.length) {
            changeVisibilityAxisGrids(hiddenStubAxis[0], gridVisibility, minorGridVisibility);
        }
    }
}
function findAxisOptions(valueAxes, valueAxesOptions, axisName) {
    let result;
    let axInd;
    for(axInd = 0; axInd < valueAxesOptions.length; axInd += 1){
        if (valueAxesOptions[axInd].name === axisName) {
            result = valueAxesOptions[axInd];
            result.priority = axInd;
            break;
        }
    }
    if (!result) {
        for(axInd = 0; axInd < valueAxes.length; axInd += 1){
            if (valueAxes[axInd].name === axisName) {
                result = valueAxes[axInd].getOptions();
                result.priority = valueAxes[axInd].priority;
                break;
            }
        }
    }
    return result;
}
function findAxis(paneName, axisName, axes) {
    const axisByName = axes.find((axis)=>axis.name === axisName && axis.pane === paneName);
    if (axisByName) {
        return axisByName;
    }
    if (paneName) {
        return findAxis(void 0, axisName, axes);
    }
}
function compareAxes(a, b) {
    return a.priority - b.priority;
}
function doesPaneExist(panes, paneName) {
    let found = false;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(panes, (_, pane)=>{
        if (pane.name === paneName) {
            found = true;
            return false;
        }
        return;
    });
    return found;
}
function accumulate(field, src1, src2, auxSpacing) {
    const val1 = src1[field] || 0;
    const val2 = src2[field] || 0;
    return val1 + val2 + (val1 && val2 ? auxSpacing : 0);
}
function pickMax(field, src1, src2) {
    return pickMaxValue(src1[field], src2[field]);
}
function pickMaxValue(val1, val2) {
    return Math.max(val1 || 0, val2 || 0);
}
function getAxisMargins(axis) {
    return axis.getMargins();
}
function getHorizontalAxesMargins(axes, getMarginsFunc) {
    return axes.reduce((margins, axis)=>{
        var _axis$getOrthogonalAx;
        const axisMargins = getMarginsFunc(axis);
        const paneMargins = margins.panes[axis.pane] = margins.panes[axis.pane] || {};
        const spacing = axis.getMultipleAxesSpacing();
        paneMargins.top = accumulate("top", paneMargins, axisMargins, spacing);
        paneMargins.bottom = accumulate("bottom", paneMargins, axisMargins, spacing);
        paneMargins.left = pickMax("left", paneMargins, axisMargins);
        paneMargins.right = pickMax("right", paneMargins, axisMargins);
        margins.top = pickMax("top", paneMargins, margins);
        margins.bottom = pickMax("bottom", paneMargins, margins);
        margins.left = pickMax("left", paneMargins, margins);
        margins.right = pickMax("right", paneMargins, margins);
        const orthogonalAxis = null === (_axis$getOrthogonalAx = axis.getOrthogonalAxis) || void 0 === _axis$getOrthogonalAx ? void 0 : _axis$getOrthogonalAx.call(axis);
        const shouldResetPositionMargin = (null === orthogonalAxis || void 0 === orthogonalAxis ? void 0 : orthogonalAxis.customPositionIsAvailable()) && (!axis.customPositionIsBoundaryOrthogonalAxis() || !orthogonalAxis.customPositionEqualsToPredefined());
        if (shouldResetPositionMargin) {
            margins[orthogonalAxis.getResolvedBoundaryPosition()] = 0;
        }
        return margins;
    }, {
        panes: {}
    });
}
function getVerticalAxesMargins(axes) {
    return axes.reduce((margins, axis)=>{
        const axisMargins = axis.getMargins();
        const paneMargins = margins.panes[axis.pane] = margins.panes[axis.pane] || {};
        const spacing = axis.getMultipleAxesSpacing();
        paneMargins.top = pickMax("top", paneMargins, axisMargins);
        paneMargins.bottom = pickMax("bottom", paneMargins, axisMargins);
        paneMargins.left = accumulate("left", paneMargins, axisMargins, spacing);
        paneMargins.right = accumulate("right", paneMargins, axisMargins, spacing);
        margins.top = pickMax("top", paneMargins, margins);
        margins.bottom = pickMax("bottom", paneMargins, margins);
        margins.left = pickMax("left", paneMargins, margins);
        margins.right = pickMax("right", paneMargins, margins);
        return margins;
    }, {
        panes: {}
    });
}
function performActionOnAxes(axes, action, actionArgument1, actionArgument2, actionArgument3) {
    axes.forEach((axis)=>{
        axis[action](null === actionArgument1 || void 0 === actionArgument1 ? void 0 : actionArgument1[axis.pane], (null === actionArgument2 || void 0 === actionArgument2 ? void 0 : actionArgument2[axis.pane]) || actionArgument2, actionArgument3);
    });
}
function shrinkCanvases(isRotated, canvases, sizes, verticalMargins, horizontalMargins) {
    function getMargin(side, margins, pane) {
        const m = !(isRotated ? [
            "left",
            "right"
        ] : [
            "top",
            "bottom"
        ]).includes(side) ? margins : margins.panes[pane] || {};
        return m[side];
    }
    function getMaxMargin(side, margins1, margins2, pane) {
        return pickMaxValue(getMargin(side, margins1, pane), getMargin(side, margins2, pane));
    }
    const getOriginalField = (field)=>"original".concat(field[0].toUpperCase()).concat(field.slice(1));
    function shrink(canvases, paneNames, sizeField, startMargin, endMargin, oppositeMargins) {
        paneNames = paneNames.sort((p1, p2)=>canvases[p2][startMargin] - canvases[p1][startMargin]);
        paneNames.forEach((pane)=>{
            const canvas = canvases[pane];
            oppositeMargins.forEach((margin)=>{
                canvas[margin] = canvas[getOriginalField(margin)] + getMaxMargin(margin, verticalMargins, horizontalMargins, pane);
            });
        });
        const firstPane = canvases[paneNames[0]];
        const initialEmptySpace = firstPane[sizeField] - firstPane[getOriginalField(endMargin)] - canvases[paneNames.at(-1)][getOriginalField(startMargin)];
        let emptySpace = paneNames.reduce((space, paneName)=>{
            const maxStartMargin = getMaxMargin(startMargin, verticalMargins, horizontalMargins, paneName);
            const maxEndMargin = getMaxMargin(endMargin, verticalMargins, horizontalMargins, paneName);
            return space - maxStartMargin - maxEndMargin;
        }, initialEmptySpace) - __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PANE_PADDING"] * (paneNames.length - 1);
        emptySpace -= Object.keys(sizes).reduce((prev, key)=>{
            const currentHeight = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isRelativeHeightPane"])(sizes[key]) ? sizes[key].height : 0;
            return prev + currentHeight;
        }, 0);
        const initialOffset = firstPane[sizeField] - firstPane[getOriginalField(endMargin)] - (emptySpace < 0 ? emptySpace : 0);
        paneNames.reduce((offset, pane)=>{
            const canvas = canvases[pane];
            const paneSize = sizes[pane];
            offset -= getMaxMargin(endMargin, verticalMargins, horizontalMargins, pane);
            canvas[endMargin] = firstPane[sizeField] - offset;
            offset -= !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isRelativeHeightPane"])(paneSize) ? paneSize.height : Math.floor(emptySpace * paneSize.height);
            canvas[startMargin] = offset;
            offset -= getMaxMargin(startMargin, verticalMargins, horizontalMargins, pane) + __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["PANE_PADDING"];
            return offset;
        }, initialOffset);
    }
    const paneNames = Object.keys(canvases);
    if (!isRotated) {
        shrink(canvases, paneNames, "height", "top", "bottom", [
            "left",
            "right"
        ]);
    } else {
        shrink(canvases, paneNames, "width", "left", "right", [
            "top",
            "bottom"
        ]);
    }
    return canvases;
}
function drawAxesWithTicks(axes, condition, canvases, panesBorderOptions) {
    if (condition) {
        performActionOnAxes(axes, "createTicks", canvases);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$multi_axes_synchronizer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].synchronize(axes);
    }
    performActionOnAxes(axes, "draw", !condition && canvases, panesBorderOptions);
}
function shiftAxis(side1, side2) {
    const shifts = {};
    return function(axis) {
        if (!axis.customPositionIsAvailable() || axis.customPositionEqualsToPredefined()) {
            const shift = shifts[axis.pane] = shifts[axis.pane] || {
                top: 0,
                left: 0,
                bottom: 0,
                right: 0
            };
            const spacing = axis.getMultipleAxesSpacing();
            const margins = axis.getMargins();
            axis.shift(shift);
            shift[side1] = accumulate(side1, shift, margins, spacing);
            shift[side2] = accumulate(side2, shift, margins, spacing);
        } else {
            axis.shift({
                top: 0,
                left: 0,
                bottom: 0,
                right: 0
            });
        }
    };
}
function getCommonSize(side, margins) {
    let size = 0;
    let paneMargins;
    Object.keys(margins.panes).forEach((pane)=>{
        paneMargins = margins.panes[pane];
        size += "height" === side ? paneMargins.top + paneMargins.bottom : paneMargins.left + paneMargins.right;
    });
    return size;
}
function checkUsedSpace(sizeShortage, side, axes, getMarginFunc) {
    let size = 0;
    if (sizeShortage[side] > 0) {
        size = getCommonSize(side, getMarginFunc(axes, getAxisMargins));
        performActionOnAxes(axes, "hideTitle");
        sizeShortage[side] -= size - getCommonSize(side, getMarginFunc(axes, getAxisMargins));
    }
    if (sizeShortage[side] > 0) {
        performActionOnAxes(axes, "hideOuterElements");
    }
}
function axisAnimationEnabled(drawOptions, pointsToAnimation) {
    const pointsCount = pointsToAnimation.reduce((sum, count)=>sum + count, 0) / pointsToAnimation.length;
    return drawOptions.animate && pointsCount <= drawOptions.animationPointsLimit;
}
function collectMarkersInfoBySeries(allSeries, filteredSeries, argAxis) {
    const series = [];
    const overloadedSeries = {};
    const argVisualRange = argAxis.visualRange();
    const argTranslator = argAxis.getTranslator();
    const argViewPortFilter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getViewPortFilter(argVisualRange || {});
    filteredSeries.forEach((s)=>{
        const valAxis = s.getValueAxis();
        const valVisualRange = valAxis.getCanvasRange();
        const valTranslator = valAxis.getTranslator();
        const seriesIndex = allSeries.indexOf(s);
        const valViewPortFilter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getViewPortFilter(valVisualRange || {});
        overloadedSeries[seriesIndex] = {};
        filteredSeries.forEach((sr)=>{
            overloadedSeries[seriesIndex][allSeries.indexOf(sr)] = 0;
        });
        const seriesPoints = [];
        const pointsInViewport = s.getPoints().filter((p)=>p.getOptions().visible && argViewPortFilter(p.argument) && (valViewPortFilter(p.getMinValue(true)) || valViewPortFilter(p.getMaxValue(true))));
        pointsInViewport.forEach((p)=>{
            const tp = {
                seriesIndex: seriesIndex,
                argument: p.argument,
                value: p.getMaxValue(true),
                size: p.bubbleSize || p.getOptions().size,
                x: void 0,
                y: void 0
            };
            if (p.getMinValue(true) !== p.getMaxValue(true)) {
                const mp = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, tp);
                mp.value = p.getMinValue(true);
                mp.x = argTranslator.to(mp.argument, 1);
                mp.y = valTranslator.to(mp.value, 1);
                seriesPoints.push(mp);
            }
            tp.x = argTranslator.to(tp.argument, 1);
            tp.y = valTranslator.to(tp.value, 1);
            seriesPoints.push(tp);
        });
        overloadedSeries[seriesIndex].pointsCount = seriesPoints.length;
        overloadedSeries[seriesIndex].total = 0;
        overloadedSeries[seriesIndex].continuousSeries = 0;
        series.push({
            name: s.name,
            index: seriesIndex,
            points: seriesPoints
        });
    });
    return {
        series: series,
        overloadedSeries: overloadedSeries
    };
}
const isOverlay = (currentPoint, overlayPoint, pointRadius)=>{
    const pointHitsLeftBorder = overlayPoint.x - pointRadius <= currentPoint.x;
    const pointHitsRightBorder = overlayPoint.x + pointRadius >= currentPoint.x;
    const pointHitsTopBorder = overlayPoint.y - pointRadius <= currentPoint.y;
    const pointHitsBottomBorder = overlayPoint.y + pointRadius >= currentPoint.y;
    const isPointOverlappedHorizontally = pointHitsLeftBorder && pointHitsRightBorder;
    const isPointOverlappedVertically = pointHitsTopBorder && pointHitsBottomBorder;
    return isPointOverlappedHorizontally && isPointOverlappedVertically;
};
const isPointOverlapped = (currentPoint, points, skipSamePointsComparing)=>{
    const radiusPoint = currentPoint.getOptions().size / 2;
    for(let i = 0; i < points.length; i += 1){
        if (!skipSamePointsComparing) {
            const isXCoordinateSame = points[i].x === currentPoint.x;
            const isYCoordinateSame = points[i].y === currentPoint.y;
            if (isXCoordinateSame && isYCoordinateSame) {
                continue;
            }
        }
        if (isOverlay(currentPoint, points[i], radiusPoint)) {
            return true;
        }
    }
    return false;
};
function fastHidingPointMarkersByArea(canvas, markersInfo, series) {
    const area = canvas.width * canvas.height;
    const seriesPoints = markersInfo.series;
    for(let i = seriesPoints.length - 1; i >= 0; i -= 1){
        const currentSeries = series.filter((s)=>s.name === seriesPoints[i].name)[0];
        const { points: points } = seriesPoints[i];
        const pointSize = points.length ? points[0].size : 0;
        const pointsArea = pointSize * pointSize * points.length;
        if (currentSeries.autoHidePointMarkersEnabled() && pointsArea >= area / seriesPoints.length) {
            const { index: index } = seriesPoints[i];
            currentSeries.autoHidePointMarkers = true;
            seriesPoints.splice(i, 1);
            series.splice(series.indexOf(currentSeries), 1);
            markersInfo.overloadedSeries[index] = null;
        }
    }
}
function updateMarkersInfo(points, overloadedSeries) {
    let isContinuousSeries = false;
    for(let i = 0; i < points.length - 1; i += 1){
        const curPoint = points[i];
        const { size: size } = curPoint;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(curPoint.x) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(curPoint.y)) {
            for(let j = i + 1; j < points.length; j += 1){
                const nextPoint = points[j];
                const nextX = null === nextPoint || void 0 === nextPoint ? void 0 : nextPoint.x;
                const nextY = null === nextPoint || void 0 === nextPoint ? void 0 : nextPoint.y;
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(nextX) || Math.abs(curPoint.x - nextX) >= size) {
                    isContinuousSeries = isContinuousSeries && j !== i + 1;
                    break;
                } else {
                    const distance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(nextX) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(nextY) && Math.sqrt((curPoint.x - nextX) ** 2 + (curPoint.y - nextY) ** 2);
                    if (distance && distance < size) {
                        overloadedSeries[curPoint.seriesIndex][nextPoint.seriesIndex] += 1;
                        overloadedSeries[curPoint.seriesIndex].total += 1;
                        if (!isContinuousSeries) {
                            overloadedSeries[curPoint.seriesIndex].continuousSeries += 1;
                            isContinuousSeries = true;
                        }
                    }
                }
            }
        }
    }
}
const dxChart = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$m_advanced_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["AdvancedChart"].inherit({
    _themeSection: "chart",
    _fontFields: [
        "crosshair.label.font"
    ],
    _initCore () {
        this.paneAxis = {};
        this.callBase();
    },
    _init () {
        this._containerInitialHeight = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasWindow"])() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])(this._$element) : 0;
        this.callBase();
    },
    _correctAxes () {
        this._correctValueAxes(true);
    },
    _getExtraOptions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _createPanes () {
        let panes = this.option("panes");
        let panesNameCounter = 0;
        let defaultPane;
        if (!panes || isArray(panes) && !panes.length) {
            panes = DEFAULT_PANES;
        }
        this.callBase();
        defaultPane = this.option("defaultPane");
        panes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, [], isArray(panes) ? panes : [
            panes
        ]);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(panes, (_, pane)=>{
            pane.name = !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(pane.name) ? "default" + panesNameCounter++ : pane.name;
        });
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(defaultPane)) {
            if (!doesPaneExist(panes, defaultPane)) {
                this._incidentOccurred("W2101", [
                    defaultPane
                ]);
                defaultPane = panes[panes.length - 1].name;
            }
        } else {
            defaultPane = panes[panes.length - 1].name;
        }
        this.defaultPane = defaultPane;
        panes = this._isRotated() ? panes.reverse() : panes;
        return panes;
    },
    _getAxisRenderingOptions: ()=>({
            axisType: "xyAxes",
            drawingType: "linear"
        }),
    _prepareAxisOptions (typeSelector, userOptions, rotated) {
        return {
            isHorizontal: "argumentAxis" === typeSelector !== rotated,
            containerColor: this._themeManager.getOptions("containerBackgroundColor")
        };
    },
    _checkPaneName (seriesTheme) {
        const paneList = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(this.panes, (pane)=>pane.name);
        seriesTheme.pane = seriesTheme.pane || this.defaultPane;
        return paneList.includes(seriesTheme.pane);
    },
    _initCustomPositioningAxes () {
        const argumentAxis = this.getArgumentAxis();
        const valueAxisName = argumentAxis.getOptions().customPositionAxis;
        const valueAxis = this._valueAxes.find((v)=>v.pane === argumentAxis.pane && (!valueAxisName || valueAxisName === v.name));
        this._valueAxes.forEach((v)=>{
            if (argumentAxis !== v.getOrthogonalAxis()) {
                v.getOrthogonalAxis = ()=>argumentAxis;
                v.customPositionIsBoundaryOrthogonalAxis = ()=>argumentAxis.customPositionIsBoundary();
            }
        });
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(valueAxis) && valueAxis !== argumentAxis.getOrthogonalAxis()) {
            argumentAxis.getOrthogonalAxis = ()=>valueAxis;
            argumentAxis.customPositionIsBoundaryOrthogonalAxis = ()=>this._valueAxes.some((v)=>v.customPositionIsBoundary());
        } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(argumentAxis.getOrthogonalAxis()) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(valueAxis)) {
            argumentAxis.getOrthogonalAxis = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
        }
    },
    _getAllAxes () {
        return this._argumentAxes.concat(this._valueAxes);
    },
    _resetAxesAnimation (isFirstDrawing, isHorizontal) {
        let axes;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(isHorizontal)) {
            axes = isHorizontal ^ this._isRotated() ? this._argumentAxes : this._valueAxes;
        } else {
            axes = this._getAllAxes();
        }
        axes.forEach((a)=>{
            a.resetApplyingAnimation(isFirstDrawing);
        });
    },
    _axesBoundaryPositioning () {
        const allAxes = this._getAllAxes();
        let boundaryStateChanged = false;
        allAxes.forEach((a)=>{
            if (!a.customPositionIsAvailable()) {
                return;
            }
            const prevBoundaryState = a.customPositionIsBoundary();
            a._customBoundaryPosition = a.getCustomBoundaryPosition();
            boundaryStateChanged = boundaryStateChanged || prevBoundaryState !== a.customPositionIsBoundary();
        });
        return boundaryStateChanged;
    },
    _getCrosshairMargins () {
        const crosshairOptions = this._getCrosshairOptions() || {};
        const crosshairEnabled = crosshairOptions.enabled;
        const margins = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$crosshair$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getMargins"])();
        const horizontalLabel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, crosshairOptions.label, crosshairOptions.horizontalLine.label);
        const verticalLabel = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, crosshairOptions.label, crosshairOptions.verticalLine.label);
        return {
            x: crosshairEnabled && crosshairOptions.horizontalLine.visible && horizontalLabel.visible ? margins.x : 0,
            y: crosshairEnabled && crosshairOptions.verticalLine.visible && verticalLabel.visible ? margins.y : 0
        };
    },
    _getValueAxis (paneName, axisName) {
        const valueAxes = this._valueAxes;
        const valueAxisOptions = this.option("valueAxis") || {};
        const valueAxesOptions = isArray(valueAxisOptions) ? valueAxisOptions : [
            valueAxisOptions
        ];
        const rotated = this._isRotated();
        const crosshairMargins = this._getCrosshairMargins();
        let axisOptions;
        let axis;
        axisName = axisName || getFirstAxisNameForPane(valueAxes, paneName, this.defaultPane);
        axis = findAxis(paneName, axisName, valueAxes);
        if (!axis) {
            axisOptions = findAxisOptions(valueAxes, valueAxesOptions, axisName);
            if (!axisOptions) {
                this._incidentOccurred("W2102", [
                    axisName
                ]);
                axisOptions = {
                    name: axisName,
                    priority: valueAxes.length
                };
            }
            axis = this._createAxis(false, this._populateAxesOptions("valueAxis", axisOptions, {
                pane: paneName,
                name: axisName,
                optionPath: isArray(valueAxisOptions) ? "valueAxis[".concat(axisOptions.priority, "]") : "valueAxis",
                crosshairMargin: rotated ? crosshairMargins.y : crosshairMargins.x
            }, rotated));
            axis.applyVisualRangeSetter(this._getVisualRangeSetter());
            valueAxes.push(axis);
        }
        axis.setPane(paneName);
        return axis;
    },
    _correctValueAxes (needHideGrids) {
        const synchronizeMultiAxes = this._themeManager.getOptions("synchronizeMultiAxes");
        const valueAxes = this._valueAxes;
        const paneWithAxis = {};
        this.series.forEach((series)=>{
            const axis = series.getValueAxis();
            paneWithAxis[axis.pane] = true;
        });
        this.panes.forEach((pane)=>{
            const paneName = pane.name;
            if (!paneWithAxis[paneName]) {
                this._getValueAxis(paneName);
            }
            if (needHideGrids && synchronizeMultiAxes) {
                hideGridsOnNonFirstValueAxisForPane(valueAxes.filter((axis)=>axis.pane === paneName));
            }
        });
        this._valueAxes = valueAxes.filter((axis)=>{
            if (!axis.pane) {
                axis.setPane(this.defaultPane);
            }
            const paneExists = doesPaneExist(this.panes, axis.pane);
            if (!paneExists) {
                axis.dispose();
                axis = null;
            }
            return paneExists;
        }).sort(compareAxes);
        const defaultAxis = this.getValueAxis();
        this._valueAxes.forEach((axis)=>{
            const { optionPath: optionPath } = axis.getOptions();
            if (optionPath) {
                const axesWithSamePath = this._valueAxes.filter((a)=>a.getOptions().optionPath === optionPath);
                if (axesWithSamePath.length > 1) {
                    if (axesWithSamePath.some((a)=>a === defaultAxis)) {
                        axesWithSamePath.forEach((a)=>{
                            if (a !== defaultAxis) {
                                a.getOptions().optionPath = null;
                            }
                        });
                    } else {
                        axesWithSamePath.forEach((a, i)=>{
                            if (0 !== i) {
                                a.getOptions().optionPath = null;
                            }
                        });
                    }
                }
            }
        });
    },
    _getSeriesForPane (paneName) {
        const paneSeries = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this.series, (_, oneSeries)=>{
            if (oneSeries.pane === paneName) {
                paneSeries.push(oneSeries);
            }
        });
        return paneSeries;
    },
    _createPanesBorderOptions () {
        const commonBorderOptions = this._themeManager.getOptions("commonPaneSettings").border;
        const panesBorderOptions = {};
        this.panes.forEach((pane)=>{
            panesBorderOptions[pane.name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, commonBorderOptions, pane.border);
        });
        return panesBorderOptions;
    },
    _createScrollBar () {
        const scrollBarOptions = this._themeManager.getOptions("scrollBar") || {};
        const scrollBarGroup = this._scrollBarGroup;
        if (scrollBarOptions.visible) {
            scrollBarOptions.rotated = this._isRotated();
            this._scrollBar = (this._scrollBar || new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$scroll_bar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ScrollBar"](this._renderer, scrollBarGroup)).update(scrollBarOptions);
        } else {
            var _this$_scrollBar;
            scrollBarGroup.linkRemove();
            null === (_this$_scrollBar = this._scrollBar) || void 0 === _this$_scrollBar || _this$_scrollBar.dispose();
            this._scrollBar = null;
        }
    },
    _executeAppendAfterSeries (append) {
        append();
    },
    _prepareToRender () {
        const panesBorderOptions = this._createPanesBorderOptions();
        this._createPanesBackground();
        this._appendAxesGroups();
        this._adjustViewport();
        return panesBorderOptions;
    },
    _adjustViewport () {
        const adjustOnZoom = this._themeManager.getOptions("adjustOnZoom");
        if (!adjustOnZoom) {
            return;
        }
        this._valueAxes.forEach((axis)=>axis.adjust());
    },
    _recreateSizeDependentObjects (isCanvasChanged) {
        const series = this._getVisibleSeries();
        const useAggregation = series.some((s)=>s.useAggregation());
        const zoomChanged = this._isZooming();
        if (!useAggregation) {
            return;
        }
        this._argumentAxes.forEach((axis)=>{
            axis.updateCanvas(this._canvas, true);
        });
        series.forEach((series)=>{
            if (series.useAggregation() && (isCanvasChanged || zoomChanged || !series._useAllAggregatedPoints)) {
                series.createPoints();
            }
        });
        this._processSeriesFamilies();
    },
    _isZooming () {
        const argumentAxis = this.getArgumentAxis();
        if (!(null !== argumentAxis && void 0 !== argumentAxis && argumentAxis.getTranslator())) {
            return false;
        }
        const businessRange = argumentAxis.getTranslator().getBusinessRange();
        const zoomRange = argumentAxis.getViewport();
        let min = zoomRange ? zoomRange.min : 0;
        let max = zoomRange ? zoomRange.max : 0;
        if ("logarithmic" === businessRange.axisType) {
            min = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLog"])(min, businessRange.base);
            max = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLog"])(max, businessRange.base);
        }
        const viewportDistance = businessRange.axisType === DISCRETE ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCategoriesInfo"])(businessRange.categories, min, max).categories.length : Math.abs(max - min);
        let precision = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getPrecision"])(viewportDistance);
        precision = precision > 1 ? 10 ** (precision - 2) : 1;
        const zoomChanged = Math.round((this._zoomLength - viewportDistance) * precision) / precision !== 0;
        this._zoomLength = viewportDistance;
        return zoomChanged;
    },
    _handleSeriesDataUpdated () {
        const viewport = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$translators$2f$range$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Range"];
        this.series.forEach((s)=>{
            viewport.addRange(s.getArgumentRange());
        });
        this._argumentAxes.forEach((axis)=>{
            axis.updateCanvas(this._canvas, true);
            axis.setBusinessRange(viewport, this._axesReinitialized);
        });
        this.callBase();
    },
    _isLegendInside () {
        return this._legend && "inside" === this._legend.getPosition();
    },
    _isRotated () {
        return this._themeManager.getOptions("rotated");
    },
    _getLayoutTargets () {
        return this.panes;
    },
    _applyClipRects (panesBorderOptions) {
        this._drawPanesBorders(panesBorderOptions);
        this._createClipRectsForPanes();
        this._applyClipRectsForAxes();
        this._fillPanesBackground();
    },
    _updateLegendPosition (drawOptions, legendHasInsidePosition) {
        if (drawOptions.drawLegend && this._legend && legendHasInsidePosition) {
            const { panes: panes } = this;
            const newCanvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, panes[0].canvas);
            const layoutManager = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$layout_manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["LayoutManager"];
            newCanvas.right = panes[panes.length - 1].canvas.right;
            newCanvas.bottom = panes[panes.length - 1].canvas.bottom;
            layoutManager.layoutInsideLegend(this._legend, newCanvas);
        }
    },
    _allowLegendInsidePosition: ()=>true,
    _applyExtraSettings (series) {
        const paneIndex = this._getPaneIndex(series.pane);
        const panesClipRects = this._panesClipRects;
        const wideClipRect = panesClipRects.wide[paneIndex];
        series.setClippingParams(panesClipRects.base[paneIndex].id, null === wideClipRect || void 0 === wideClipRect ? void 0 : wideClipRect.id, this._getPaneBorderVisibility(paneIndex));
    },
    _updatePanesCanvases (drawOptions) {
        if (!drawOptions.recreateCanvas) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["updatePanesCanvases"])(this.panes, this._canvas, this._isRotated());
    },
    _normalizePanesHeight () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizePanesHeight"])(this.panes);
    },
    _renderScaleBreaks () {
        this._valueAxes.concat(this._argumentAxes).forEach((axis)=>{
            axis.drawScaleBreaks();
        });
    },
    _getArgFilter () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getViewPortFilter(this.getArgumentAxis().visualRange() || {});
    },
    _hidePointsForSingleSeriesIfNeeded (series) {
        const seriesPoints = series.getPoints();
        let overlappedPointsCount = 0;
        for(let i = 0; i < seriesPoints.length; i += 1){
            const currentPoint = seriesPoints[i];
            const overlappingPoints = seriesPoints.slice(i + 1);
            overlappedPointsCount += Number(isPointOverlapped(currentPoint, overlappingPoints));
            if (overlappedPointsCount > seriesPoints.length / 2) {
                series.autoHidePointMarkers = true;
                break;
            }
        }
    },
    _applyAutoHidePointMarkers (filteredSeries) {
        let overlappingPoints = [];
        const overlappedPointsCalculator = (pointsCount, currentPoint)=>pointsCount + isPointOverlapped(currentPoint, overlappingPoints, true);
        for(let i = filteredSeries.length - 1; i >= 0; i -= 1){
            const currentSeries = filteredSeries[i];
            if (!currentSeries.autoHidePointMarkersEnabled()) {
                continue;
            }
            currentSeries.autoHidePointMarkers = false;
            this._hidePointsForSingleSeriesIfNeeded(currentSeries);
            if (!currentSeries.autoHidePointMarkers) {
                const seriesPoints = currentSeries.getPoints();
                const overlappingPointsCount = seriesPoints.reduce(overlappedPointsCalculator, 0);
                if (overlappingPointsCount < seriesPoints.length) {
                    overlappingPoints = overlappingPoints.concat(seriesPoints);
                } else {
                    currentSeries.autoHidePointMarkers = true;
                }
            }
        }
    },
    _applyPointMarkersAutoHiding () {
        const allSeries = this.series;
        if (!this._themeManager.getOptions("autoHidePointMarkers")) {
            allSeries.forEach((s)=>{
                s.autoHidePointMarkers = false;
            });
            return;
        }
        this.panes.forEach((_ref)=>{
            let { borderCoords: borderCoords, name: name } = _ref;
            const series = allSeries.filter((s)=>s.pane === name && s.usePointsToDefineAutoHiding());
            series.forEach((singleSeries)=>{
                singleSeries.prepareCoordinatesForPoints();
            });
            const argAxis = this.getArgumentAxis();
            const markersInfo = collectMarkersInfoBySeries(allSeries, series, argAxis);
            fastHidingPointMarkersByArea(borderCoords, markersInfo, series);
            if (markersInfo.series.length) {
                const argVisualRange = argAxis.visualRange();
                const argAxisIsDiscrete = argAxis.getOptions().type === DISCRETE;
                const sortingCallback = argAxisIsDiscrete ? (p1, p2)=>argVisualRange.categories.indexOf(p1.argument) - argVisualRange.categories.indexOf(p2.argument) : (p1, p2)=>p1.argument - p2.argument;
                let points = [];
                markersInfo.series.forEach((s)=>{
                    points = points.concat(s.points);
                });
                points.sort(sortingCallback);
                updateMarkersInfo(points, markersInfo.overloadedSeries);
                this._applyAutoHidePointMarkers(series);
            }
        });
    },
    _renderAxes (drawOptions, panesBorderOptions) {
        function calculateTitlesWidth(axes) {
            return axes.map((axis)=>{
                if (!axis.getTitle) {
                    return 0;
                }
                const title = axis.getTitle();
                return title ? title.bBox.width : 0;
            });
        }
        const rotated = this._isRotated();
        const synchronizeMultiAxes = this._themeManager.getOptions("synchronizeMultiAxes");
        const scrollBar = this._scrollBar ? [
            this._scrollBar
        ] : [];
        const extendedArgAxes = this._isArgumentAxisBeforeScrollBar() ? this._argumentAxes.concat(scrollBar) : scrollBar.concat(this._argumentAxes);
        const verticalAxes = rotated ? this._argumentAxes : this._valueAxes;
        const verticalElements = rotated ? extendedArgAxes : this._valueAxes;
        const horizontalAxes = rotated ? this._valueAxes : this._argumentAxes;
        const horizontalElements = rotated ? this._valueAxes : extendedArgAxes;
        const allAxes = verticalAxes.concat(horizontalAxes);
        const allElements = allAxes.concat(scrollBar);
        const verticalAxesFirstDrawing = verticalAxes.some((v)=>v.isFirstDrawing());
        this._normalizePanesHeight();
        this._updatePanesCanvases(drawOptions);
        let panesCanvases = this.panes.reduce((canvases, pane)=>{
            canvases[pane.name] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, pane.canvas);
            return canvases;
        }, {});
        const paneSizes = this.panes.reduce((sizes, pane)=>{
            sizes[pane.name] = {
                height: pane.height,
                unit: pane.unit
            };
            return sizes;
        }, {});
        const cleanPanesCanvases = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, panesCanvases);
        this._initCustomPositioningAxes();
        const needCustomAdjustAxes = this._axesBoundaryPositioning();
        if (!drawOptions.adjustAxes && !needCustomAdjustAxes) {
            drawAxesWithTicks(verticalAxes, !rotated && synchronizeMultiAxes, panesCanvases, panesBorderOptions);
            drawAxesWithTicks(horizontalAxes, rotated && synchronizeMultiAxes, panesCanvases, panesBorderOptions);
            performActionOnAxes(allAxes, "prepareAnimation");
            this._renderScaleBreaks();
            horizontalAxes.forEach((a)=>a.resolveOverlappingForCustomPositioning(verticalAxes));
            verticalAxes.forEach((a)=>a.resolveOverlappingForCustomPositioning(horizontalAxes));
            return false;
        }
        if (needCustomAdjustAxes) {
            allAxes.forEach((a)=>a.customPositionIsAvailable() && a.shift({
                    top: 0,
                    left: 0,
                    bottom: 0,
                    right: 0
                }));
        }
        if (this._scrollBar) {
            this._scrollBar.setPane(this.panes);
        }
        let vAxesMargins = {
            panes: {},
            left: 0,
            right: 0
        };
        let hAxesMargins = getHorizontalAxesMargins(horizontalElements, (axis)=>axis.estimateMargins(panesCanvases[axis.pane]));
        panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, vAxesMargins, hAxesMargins);
        const drawAxesAndSetCanvases = (isHorizontal)=>{
            const axes = isHorizontal ? horizontalAxes : verticalAxes;
            const condition = (isHorizontal ? rotated : !rotated) && synchronizeMultiAxes;
            drawAxesWithTicks(axes, condition, panesCanvases, panesBorderOptions);
            if (isHorizontal) {
                hAxesMargins = getHorizontalAxesMargins(horizontalElements, getAxisMargins);
            } else {
                vAxesMargins = getVerticalAxesMargins(verticalElements);
            }
            panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, vAxesMargins, hAxesMargins);
        };
        drawAxesAndSetCanvases(false);
        drawAxesAndSetCanvases(true);
        if (!this._changesApplying && this._estimateTickIntervals(verticalAxes, panesCanvases)) {
            drawAxesAndSetCanvases(false);
        }
        let oldTitlesWidth = calculateTitlesWidth(verticalAxes);
        const visibleSeries = this._getVisibleSeries();
        const pointsToAnimation = this._getPointsToAnimation(visibleSeries);
        const axesIsAnimated = axisAnimationEnabled(drawOptions, pointsToAnimation);
        performActionOnAxes(allElements, "updateSize", panesCanvases, axesIsAnimated);
        horizontalElements.forEach(shiftAxis("top", "bottom"));
        verticalElements.forEach(shiftAxis("left", "right"));
        this._renderScaleBreaks();
        this.panes.forEach((pane)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(pane.canvas, panesCanvases[pane.name]);
        });
        this._valueAxes.forEach((axis)=>{
            axis.setInitRange();
        });
        verticalAxes.forEach((axis, i)=>{
            var _axis$hasWrap;
            if (null !== (_axis$hasWrap = axis.hasWrap) && void 0 !== _axis$hasWrap && _axis$hasWrap.call(axis)) {
                const title = axis.getTitle();
                const newTitleWidth = title ? title.bBox.width : 0;
                const offset = newTitleWidth - oldTitlesWidth[i];
                if ("right" === axis.getOptions().position) {
                    vAxesMargins.right += offset;
                } else {
                    vAxesMargins.left += offset;
                    this.panes.forEach((_ref2)=>{
                        let { name: name } = _ref2;
                        vAxesMargins.panes[name].left += offset;
                    });
                }
                panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, vAxesMargins, hAxesMargins);
                performActionOnAxes(allElements, "updateSize", panesCanvases, false, false);
                oldTitlesWidth = calculateTitlesWidth(verticalAxes);
            }
        });
        if (verticalAxes.some((v)=>v.customPositionIsAvailable() && v.getCustomPosition() !== v._axisPosition)) {
            axesIsAnimated && this._resetAxesAnimation(verticalAxesFirstDrawing, false);
            performActionOnAxes(verticalAxes, "updateSize", panesCanvases, axesIsAnimated);
        }
        horizontalAxes.forEach((a)=>a.resolveOverlappingForCustomPositioning(verticalAxes));
        verticalAxes.forEach((a)=>a.resolveOverlappingForCustomPositioning(horizontalAxes));
        return cleanPanesCanvases;
    },
    _getExtraTemplatesItems () {
        const allAxes = (this._argumentAxes || []).concat(this._valueAxes || []);
        const elements = this._collectTemplatesFromItems(allAxes);
        return {
            items: elements.items,
            groups: elements.groups,
            launchRequest () {
                allAxes.forEach((a)=>{
                    a.setRenderedState(true);
                });
            },
            doneRequest () {
                allAxes.forEach((a)=>{
                    a.setRenderedState(false);
                });
            }
        };
    },
    _estimateTickIntervals: (axes, canvases)=>axes.some((axis)=>axis.estimateTickInterval(canvases[axis.pane])),
    checkForMoreSpaceForPanesCanvas () {
        const rotated = this._isRotated();
        const panesAreCustomSized = this.panes.filter((p)=>p.unit).length === this.panes.length;
        let needSpace = false;
        if (panesAreCustomSized) {
            let needHorizontalSpace = 0;
            let needVerticalSpace = 0;
            if (rotated) {
                const argAxisRightMargin = this.getArgumentAxis().getMargins().right;
                const rightPanesIndent = Math.min(...this.panes.map((p)=>p.canvas.right));
                needHorizontalSpace = this._canvas.right + argAxisRightMargin - rightPanesIndent;
            } else {
                const argAxisBottomMargin = this.getArgumentAxis().getMargins().bottom;
                const bottomPanesIndent = Math.min(...this.panes.map((p)=>p.canvas.bottom));
                needVerticalSpace = this._canvas.bottom + argAxisBottomMargin - bottomPanesIndent;
            }
            needSpace = needHorizontalSpace > 0 || needVerticalSpace > 0 ? {
                width: needHorizontalSpace,
                height: needVerticalSpace
            } : false;
            if (0 !== needVerticalSpace) {
                const realSize = this.getSize();
                const customSize = this.option("size");
                const container = this._$element[0];
                const containerHasStyledHeight = !!parseInt(container.style.height, 10) || 0 !== this._containerInitialHeight;
                if (!rotated && !(null !== customSize && void 0 !== customSize && customSize.height) && !containerHasStyledHeight) {
                    this._forceResize(realSize.width, realSize.height + needVerticalSpace);
                    needSpace = false;
                }
            }
        } else {
            needSpace = this.layoutManager.needMoreSpaceForPanesCanvas(this._getLayoutTargets(), rotated, (pane)=>({
                    width: rotated && !!pane.unit,
                    height: !rotated && !!pane.unit
                }));
        }
        return needSpace;
    },
    _forceResize (width, height) {
        this._renderer.resize(width, height);
        this._updateSize(true);
        this._setContentSize();
        this._preserveOriginalCanvas();
        this._updateCanvasClipRect(this._canvas);
    },
    _shrinkAxes (sizeShortage, panesCanvases) {
        if (!sizeShortage || !panesCanvases) {
            return;
        }
        this._renderer.stopAllAnimations(true);
        const rotated = this._isRotated();
        const scrollBar = this._scrollBar ? [
            this._scrollBar
        ] : [];
        const extendedArgAxes = this._isArgumentAxisBeforeScrollBar() ? this._argumentAxes.concat(scrollBar) : scrollBar.concat(this._argumentAxes);
        const verticalAxes = rotated ? extendedArgAxes : this._valueAxes;
        const horizontalAxes = rotated ? this._valueAxes : extendedArgAxes;
        const allAxes = verticalAxes.concat(horizontalAxes);
        if (sizeShortage.width || sizeShortage.height) {
            checkUsedSpace(sizeShortage, "height", horizontalAxes, getHorizontalAxesMargins);
            checkUsedSpace(sizeShortage, "width", verticalAxes, getVerticalAxesMargins);
            performActionOnAxes(allAxes, "updateSize", panesCanvases);
            const paneSizes = this.panes.reduce((sizes, pane)=>{
                sizes[pane.name] = {
                    height: pane.height,
                    unit: pane.unit
                };
                return sizes;
            }, {});
            panesCanvases = shrinkCanvases(rotated, panesCanvases, paneSizes, getVerticalAxesMargins(verticalAxes), getHorizontalAxesMargins(horizontalAxes, getAxisMargins));
            performActionOnAxes(allAxes, "updateSize", panesCanvases);
            horizontalAxes.forEach(shiftAxis("top", "bottom"));
            verticalAxes.forEach(shiftAxis("left", "right"));
            this.panes.forEach((pane)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(pane.canvas, panesCanvases[pane.name]));
        }
    },
    _isArgumentAxisBeforeScrollBar () {
        const argumentAxis = this.getArgumentAxis();
        if (this._scrollBar) {
            var _argumentAxis$getOpti;
            const argAxisPosition = argumentAxis.getResolvedBoundaryPosition();
            const argAxisLabelPosition = null === (_argumentAxis$getOpti = argumentAxis.getOptions().label) || void 0 === _argumentAxis$getOpti ? void 0 : _argumentAxis$getOpti.position;
            const scrollBarPosition = this._scrollBar.getOptions().position;
            return argumentAxis.hasNonBoundaryPosition() || scrollBarPosition === argAxisPosition && argAxisLabelPosition !== scrollBarPosition;
        }
        return false;
    },
    _getPanesParameters () {
        const { panes: panes } = this;
        const params = [];
        for(let i = 0; i < panes.length; i += 1){
            if (this._getPaneBorderVisibility(i)) {
                params.push({
                    coords: panes[i].borderCoords,
                    clipRect: this._panesClipRects.fixed[i]
                });
            }
        }
        return params;
    },
    _createCrosshairCursor () {
        const options = this._themeManager.getOptions("crosshair") || {};
        const argumentAxis = this.getArgumentAxis();
        const axes = this._isRotated() ? [
            this._valueAxes,
            [
                argumentAxis
            ]
        ] : [
            [
                argumentAxis
            ],
            this._valueAxes
        ];
        const parameters = {
            canvas: this._getCommonCanvas(),
            panes: this._getPanesParameters(),
            axes: axes
        };
        if (!(null !== options && void 0 !== options && options.enabled)) {
            return;
        }
        if (this._crosshair) {
            this._crosshair.update(options, parameters);
        } else {
            this._crosshair = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$crosshair$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Crosshair"](this._renderer, options, parameters, this._crosshairCursorGroup);
        }
        this._crosshair.render();
    },
    _getCommonCanvas () {
        let commonCanvas;
        const { panes: panes } = this;
        for(let i = 0; i < panes.length; i += 1){
            const { canvas: canvas } = panes[i];
            if (!commonCanvas) {
                commonCanvas = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, canvas);
            } else {
                commonCanvas.right = canvas.right;
                commonCanvas.bottom = canvas.bottom;
            }
        }
        return commonCanvas;
    },
    _createPanesBackground () {
        const defaultBackgroundColor = this._themeManager.getOptions("commonPaneSettings").backgroundColor;
        const renderer = this._renderer;
        const rects = [];
        this._panesBackgroundGroup.clear();
        for(let i = 0; i < this.panes.length; i += 1){
            const backgroundColor = this.panes[i].backgroundColor || defaultBackgroundColor;
            if (!backgroundColor || "none" === backgroundColor) {
                rects.push(null);
                continue;
            }
            const rect = renderer.rect(0, 0, 0, 0).attr({
                fill: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(backgroundColor),
                "stroke-width": 0
            }).append(this._panesBackgroundGroup);
            rects.push(rect);
        }
        this.panesBackground = rects;
    },
    _fillPanesBackground () {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this.panes, (i, pane)=>{
            const bc = pane.borderCoords;
            if (null !== this.panesBackground[i]) {
                this.panesBackground[i].attr({
                    x: bc.left,
                    y: bc.top,
                    width: bc.width,
                    height: bc.height
                });
            }
        });
    },
    _calcPaneBorderCoords (pane) {
        const { canvas: canvas } = pane;
        const bc = pane.borderCoords = pane.borderCoords || {};
        bc.left = canvas.left;
        bc.top = canvas.top;
        bc.right = canvas.width - canvas.right;
        bc.bottom = canvas.height - canvas.bottom;
        bc.width = Math.max(bc.right - bc.left, 0);
        bc.height = Math.max(bc.bottom - bc.top, 0);
    },
    _drawPanesBorders (panesBorderOptions) {
        const rotated = this._isRotated();
        this._panesBorderGroup.linkRemove().clear();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this.panes, (i, pane)=>{
            const borderOptions = panesBorderOptions[pane.name];
            const attr = {
                fill: "none",
                stroke: borderOptions.color,
                "stroke-opacity": borderOptions.opacity,
                "stroke-width": borderOptions.width,
                dashStyle: borderOptions.dashStyle,
                "stroke-linecap": "square"
            };
            this._calcPaneBorderCoords(pane, rotated);
            if (!borderOptions.visible) {
                return;
            }
            const bc = pane.borderCoords;
            const segmentRectParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["prepareSegmentRectPoints"])(bc.left, bc.top, bc.width, bc.height, borderOptions);
            this._renderer.path(segmentRectParams.points, segmentRectParams.pathType).attr(attr).append(this._panesBorderGroup);
        });
        this._panesBorderGroup.linkAppend();
    },
    _createClipRect (clipArray, index, left, top, width, height) {
        let clipRect = clipArray[index];
        if (!clipRect) {
            clipRect = this._renderer.clipRect(left, top, width, height);
            clipArray[index] = clipRect;
        } else {
            clipRect.attr({
                x: left,
                y: top,
                width: width,
                height: height
            });
        }
    },
    _createClipRectsForPanes () {
        const canvas = this._canvas;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this.panes, (i, pane)=>{
            let needWideClipRect = false;
            const bc = pane.borderCoords;
            let { left: left } = bc;
            let { top: top } = bc;
            let { width: width } = bc;
            let { height: height } = bc;
            const panesClipRects = this._panesClipRects;
            this._createClipRect(panesClipRects.fixed, i, left, top, width, height);
            this._createClipRect(panesClipRects.base, i, left, top, width, height);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this.series, (_, series)=>{
                if (series.pane === pane.name && (series.isFinancialSeries() || series.areErrorBarsVisible())) {
                    needWideClipRect = true;
                }
            });
            if (needWideClipRect) {
                if (this._isRotated()) {
                    top = 0;
                    height = canvas.height;
                } else {
                    left = 0;
                    width = canvas.width;
                }
                this._createClipRect(panesClipRects.wide, i, left, top, width, height);
            } else {
                panesClipRects.wide[i] = null;
            }
        });
    },
    _applyClipRectsForAxes () {
        const axes = this._getAllAxes();
        const chartCanvasClipRectID = this._getCanvasClipRectID();
        for(let i = 0; i < axes.length; i += 1){
            const elementsClipRectID = this._getElementsClipRectID(axes[i].pane);
            axes[i].applyClipRects(elementsClipRectID, chartCanvasClipRectID);
        }
    },
    _getPaneBorderVisibility (paneIndex) {
        var _pane$border;
        const commonPaneBorderVisible = this._themeManager.getOptions("commonPaneSettings").border.visible;
        const pane = this.panes[paneIndex];
        const paneVisibility = null === pane || void 0 === pane || null === (_pane$border = pane.border) || void 0 === _pane$border ? void 0 : _pane$border.visible;
        return void 0 === paneVisibility ? commonPaneBorderVisible : paneVisibility;
    },
    _getCanvasForPane (paneName) {
        var _this$panes$find;
        return null === (_this$panes$find = this.panes.find((pane)=>pane.name === paneName)) || void 0 === _this$panes$find ? void 0 : _this$panes$find.canvas;
    },
    _getTrackerSettings () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this.callBase(), {
            chart: this,
            rotated: this._isRotated(),
            crosshair: this._getCrosshairOptions().enabled ? this._crosshair : null,
            stickyHovering: this._themeManager.getOptions("stickyHovering")
        });
    },
    _resolveLabelOverlappingStack () {
        const isRotated = this._isRotated();
        const shiftDirection = isRotated ? (box, length)=>({
                x: box.x - length,
                y: box.y
            }) : (box, length)=>({
                x: box.x,
                y: box.y - length
            });
        const processor = (a, b)=>{
            const coordPosition = isRotated ? 1 : 0;
            const figureCenter1 = a.labels[0].getFigureCenter()[coordPosition];
            const figureCenter12 = b.labels[0].getFigureCenter()[coordPosition];
            if (figureCenter1 - figureCenter12 === 0) {
                const translator = a.labels[0].getPoint().series.getValueAxis().getTranslator();
                const direction = translator.isInverted() ? -1 : 1;
                return (a.value() - b.value()) * direction;
            }
            return 0;
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._getStackPoints(), (_, stacks)=>{
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(stacks, (_, points)=>{
                const isInverted = points[0].series.getValueAxis().getOptions().inverted;
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$viz$2f$chart_components$2f$m_base_chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["overlapping"].resolveLabelOverlappingInOneDirection(points, this._getCommonCanvas(), isRotated, isInverted, shiftDirection, processor);
            });
        });
    },
    _getStackPoints () {
        const stackPoints = {};
        const visibleSeries = this._getVisibleSeries();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(visibleSeries, (_, singleSeries)=>{
            const points = singleSeries.getPoints();
            const stackName = singleSeries.getStackName() || null;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(points, (_, point)=>{
                const { argument: argument } = point;
                if (!stackPoints[argument]) {
                    stackPoints[argument] = {};
                }
                if (!stackPoints[argument][stackName]) {
                    stackPoints[argument][stackName] = [];
                }
                stackPoints[argument][stackName].push(point);
            });
        });
        return stackPoints;
    },
    _getCrosshairOptions () {
        return this._getOption("crosshair");
    },
    zoomArgument (min, max) {
        if (!this._initialized || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(min) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(max)) {
            return;
        }
        this.getArgumentAxis().visualRange([
            min,
            max
        ]);
    },
    resetVisualRange () {
        const axes = this._argumentAxes;
        const nonVirtualArgumentAxis = this.getArgumentAxis();
        axes.forEach((axis)=>{
            axis.resetVisualRange(nonVirtualArgumentAxis !== axis);
            this._applyCustomVisualRangeOption(axis);
        });
        this.callBase();
    },
    getVisibleArgumentBounds () {
        const translator = this._argumentAxes[0].getTranslator();
        const range = translator.getBusinessRange();
        const isDiscrete = range.axisType === DISCRETE;
        const { categories: categories } = range;
        return {
            minVisible: isDiscrete ? range.minVisible || categories[0] : range.minVisible,
            maxVisible: isDiscrete ? range.maxVisible || categories[categories.length - 1] : range.maxVisible
        };
    },
    _change_FULL_RENDER () {
        this.callBase();
        if (this._changes.has(VISUAL_RANGE)) {
            this._raiseZoomEndHandlers();
        }
    },
    _getAxesForScaling () {
        return [
            this.getArgumentAxis()
        ].concat(this._valueAxes);
    },
    _applyVisualRangeByVirtualAxes (axis, range) {
        if (axis.isArgumentAxis) {
            if (axis !== this.getArgumentAxis()) {
                return true;
            }
            this._argumentAxes.filter((a)=>a !== axis).forEach((a)=>a.visualRange(range, {
                    start: true,
                    end: true
                }));
        }
        return false;
    },
    _raiseZoomEndHandlers () {
        this._argumentAxes.forEach((axis)=>axis.handleZoomEnd());
        this.callBase();
    },
    _setOptionsByReference () {
        this.callBase();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this._optionsByReference, {
            "argumentAxis.visualRange": true
        });
    },
    option () {
        const option = this.callBase(...arguments);
        const valueAxis = this._options.silent("valueAxis");
        if ("array" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["type"])(valueAxis)) {
            for(let i = 0; i < valueAxis.length; i += 1){
                const optionPath = "valueAxis[".concat(i, "].visualRange");
                this._optionsByReference[optionPath] = true;
            }
        }
        return option;
    },
    _notifyVisualRange () {
        const argAxis = this._argumentAxes[0];
        const argumentVisualRange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertVisualRangeObject"])(argAxis.visualRange(), !isArray(this.option("argumentAxis.visualRange")));
        if (!argAxis.skipEventRising || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rangesAreEqual"])(argumentVisualRange, this.option("argumentAxis.visualRange"))) {
            this.option("argumentAxis.visualRange", argumentVisualRange);
        } else {
            argAxis.skipEventRising = null;
        }
        this.callBase();
    }
});
dxChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$shutter_zoom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
dxChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart_components$2f$zoom_and_pan$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]);
dxChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$annotations$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugins"].core);
dxChart.addPlugin(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$annotations$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["plugins"].chart);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$component_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("dxChart", dxChart);
const __TURBOPACK__default__export__ = dxChart;
}),
}]);

//# sourceMappingURL=node_modules_devextreme_esm___internal_viz_3316d7fe._.js.map