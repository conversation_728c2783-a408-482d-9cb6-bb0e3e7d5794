﻿<dx:ThemedWindow x:Class="omsnext.wpf.LoginWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:dx="http://schemas.devexpress.com/winfx/2008/xaml/core"
        xmlns:dxe="http://schemas.devexpress.com/winfx/2008/xaml/editors"
        xmlns:dxlc="http://schemas.devexpress.com/winfx/2008/xaml/layoutcontrol"
        Title="OmsNext - Bejelentkezés" 
        Height="500" Width="400"
        WindowStartupLocation="CenterScreen"
        ResizeMode="NoResize">
    <Grid Background="#F8FAFC">
        
        <!-- Főkártya -->
        <Border Margin="30" 
                CornerRadius="12" 
                Background="White" 
                BorderBrush="#E5E7EB" 
                BorderThickness="1"
                Padding="40">
            <Border.Effect>
                <DropShadowEffect Color="Black" Direction="315" ShadowDepth="5" Opacity="0.1" BlurRadius="15"/>
            </Border.Effect>
            
            <StackPanel VerticalAlignment="Center">
                
                <!-- Logo és címsor -->
                <StackPanel HorizontalAlignment="Center" Margin="0,0,0,40">
                    <Ellipse Width="60" Height="60" Margin="0,0,0,15">
                        <Ellipse.Fill>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#4F46E5" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </Ellipse.Fill>
                    </Ellipse>
                    <TextBlock Text="OmsNext" FontSize="28" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1F2937"/>
                    <TextBlock Text="Üdvözöljük vissza!" FontSize="14" HorizontalAlignment="Center" Foreground="#6B7280" Margin="0,5,0,0"/>
                </StackPanel>

                <!-- Login form -->
                <StackPanel x:Name="LoginPanel">
                    
                    <!-- Email mező -->
                    <StackPanel Margin="0,0,0,15">
                        <TextBlock Text="E-mail cím" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,6"/>
                        <dxe:TextEdit x:Name="EmailTextEdit" 
                                    NullText="<EMAIL>" 
                                    FontSize="14"
                                    Height="40"/>
                    </StackPanel>

                    <!-- Jelszó mező -->
                    <StackPanel Margin="0,0,0,10">
                        <TextBlock Text="Jelszó" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,6"/>
                        <dxe:PasswordBoxEdit x:Name="PasswordBoxEdit" 
                                           NullText="Írja be a jelszavát" 
                                           FontSize="14"
                                           Height="40"/>
                    </StackPanel>

                    <!-- Elfelejtett jelszó link -->
                    <TextBlock HorizontalAlignment="Right" Margin="0,0,0,20">
                        <Hyperlink x:Name="ForgotPasswordLink" 
                                 Click="ForgotPasswordLink_Click"
                                 Foreground="#4F46E5" 
                                 TextDecorations="None"
                                 FontSize="13">
                            Elfelejtett jelszó?
                        </Hyperlink>
                    </TextBlock>

                    <!-- Bejelentkezés gomb -->
                    <dx:SimpleButton x:Name="LoginButton" 
                                   Content="Bejelentkezés" 
                                   Height="44" 
                                   FontSize="16" 
                                   FontWeight="SemiBold"
                                   Margin="0,0,0,15"
                                   Click="LoginButton_Click">
                        <dx:SimpleButton.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#4F46E5" Offset="0"/>
                                <GradientStop Color="#7C3AED" Offset="1"/>
                            </LinearGradientBrush>
                        </dx:SimpleButton.Background>
                    </dx:SimpleButton>

                    <!-- Hibaüzenet -->
                    <Border x:Name="ErrorPanel" 
                          CornerRadius="6" 
                          Background="#FEE2E2" 
                          BorderBrush="#FCA5A5" 
                          BorderThickness="1" 
                          Padding="10"
                          Margin="0,0,0,15"
                          Visibility="Collapsed">
                        <TextBlock x:Name="ErrorMessage" 
                                 Foreground="#DC2626" 
                                 FontSize="13"
                                 TextWrapping="Wrap"/>
                    </Border>

                    <!-- Loading indikátor -->
                    <dx:WaitIndicator x:Name="LoadingIndicator" 
                                    Content="Bejelentkezés..." 
                                    DeferedVisibility="False" 
                                    Visibility="Collapsed"/>
                </StackPanel>

                <!-- Elfelejtett jelszó panel -->
                <StackPanel x:Name="ForgotPasswordPanel" Visibility="Collapsed">
                    
                    <!-- Vissza gomb -->
                    <dx:SimpleButton x:Name="BackButton" 
                                  Content="← Vissza"
                                  HorizontalAlignment="Left" 
                                  Click="BackButton_Click"
                                  Background="Transparent"
                                  Foreground="#6B7280"
                                  BorderThickness="0"
                                  Margin="0,0,0,20"/>

                    <TextBlock Text="Jelszó visszaállítás" 
                             FontSize="22" 
                             FontWeight="Bold" 
                             HorizontalAlignment="Center" 
                             Foreground="#1F2937"
                             Margin="0,0,0,10"/>
                    
                    <TextBlock Text="Adja meg e-mail címét és küldünk egy jelszó visszaállítási linket." 
                             FontSize="13" 
                             Foreground="#6B7280" 
                             TextAlignment="Center"
                             TextWrapping="Wrap"
                             Margin="0,0,0,25"/>

                    <!-- Email mező -->
                    <StackPanel Margin="0,0,0,20">
                        <TextBlock Text="E-mail cím" FontWeight="Medium" Foreground="#374151" Margin="0,0,0,6"/>
                        <dxe:TextEdit x:Name="ResetEmailTextEdit" 
                                    NullText="<EMAIL>" 
                                    FontSize="14"
                                    Height="40"/>
                    </StackPanel>

                    <!-- Visszaállítás gomb -->
                    <dx:SimpleButton x:Name="ResetPasswordButton" 
                                   Content="Jelszó visszaállítási link küldése" 
                                   Height="44" 
                                   FontSize="14" 
                                   FontWeight="SemiBold"
                                   Margin="0,0,0,15"
                                   Click="ResetPasswordButton_Click">
                        <dx:SimpleButton.Background>
                            <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                <GradientStop Color="#059669" Offset="0"/>
                                <GradientStop Color="#047857" Offset="1"/>
                            </LinearGradientBrush>
                        </dx:SimpleButton.Background>
                    </dx:SimpleButton>

                    <!-- Sikerüzenet -->
                    <Border x:Name="SuccessPanel" 
                          CornerRadius="6" 
                          Background="#D1FAE5" 
                          BorderBrush="#86EFAC" 
                          BorderThickness="1" 
                          Padding="10"
                          Visibility="Collapsed">
                        <TextBlock x:Name="SuccessMessage" 
                                 Foreground="#059669" 
                                 FontSize="13"
                                 TextWrapping="Wrap"/>
                    </Border>
                </StackPanel>

            </StackPanel>
        </Border>
    </Grid>
</dx:ThemedWindow>