(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme/esm/__internal/events/core/m_hook_touch_props.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_hook_touch_props.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
const touchPropsToHook = [
    "pageX",
    "pageY",
    "screenX",
    "screenY",
    "clientX",
    "clientY"
];
const touchPropHook = function(name, event) {
    if (event[name] && !event.touches || !event.touches) {
        return event[name];
    }
    const touches = event.touches.length ? event.touches : event.changedTouches;
    if (!touches.length) {
        return;
    }
    return touches[0][name];
};
function __TURBOPACK__default__export__(callback) {
    touchPropsToHook.forEach((name)=>{
        callback(name, (event)=>touchPropHook(name, event));
    }, this);
}
}),
"[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_target.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/utils/m_event_target.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "getEventTarget": ()=>getEventTarget
});
const getEventTarget = (event)=>{
    var _originalEvent$target, _originalEvent$compos;
    const { originalEvent: originalEvent } = event;
    if (!originalEvent) {
        return event.target;
    }
    const isShadowDOMUsed = Boolean(null === (_originalEvent$target = originalEvent.target) || void 0 === _originalEvent$target ? void 0 : _originalEvent$target.shadowRoot);
    if (!isShadowDOMUsed) {
        return originalEvent.target;
    }
    var _originalEvent_path;
    const path = (_originalEvent_path = originalEvent.path) !== null && _originalEvent_path !== void 0 ? _originalEvent_path : null === (_originalEvent$compos = originalEvent.composedPath) || void 0 === _originalEvent$compos ? void 0 : _originalEvent$compos.call(originalEvent);
    var _ref;
    const target = (_ref = null === path || void 0 === path ? void 0 : path[0]) !== null && _ref !== void 0 ? _ref : event.target;
    return target;
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_events_engine.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator_callbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$hook_touch_props$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/hook_touch_props.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_hook_touch_props$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_hook_touch_props.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/event_target.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_target.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/call_once.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/callbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dependency_injector.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
const EMPTY_EVENT_NAME = "dxEmptyEventType";
const NATIVE_EVENTS_TO_SUBSCRIBE = {
    mouseenter: "mouseover",
    mouseleave: "mouseout",
    pointerenter: "pointerover",
    pointerleave: "pointerout"
};
const NATIVE_EVENTS_TO_TRIGGER = {
    focusin: "focus",
    focusout: "blur"
};
const NO_BUBBLE_EVENTS = [
    "blur",
    "focus",
    "load"
];
const forcePassiveFalseEventNames = [
    "touchmove",
    "wheel",
    "mousewheel",
    "touchstart"
];
const EVENT_PROPERTIES = [
    "target",
    "relatedTarget",
    "delegateTarget",
    "altKey",
    "bubbles",
    "cancelable",
    "changedTouches",
    "ctrlKey",
    "detail",
    "eventPhase",
    "metaKey",
    "shiftKey",
    "view",
    "char",
    "code",
    "charCode",
    "key",
    "keyCode",
    "button",
    "buttons",
    "offsetX",
    "offsetY",
    "pointerId",
    "pointerType",
    "targetTouches",
    "toElement",
    "touches"
];
function matchesSafe(target, selector) {
    return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(target) && "#document" !== target.nodeName && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].elementMatches(target, selector);
}
const elementDataMap = new WeakMap;
let guid = 0;
let skipEvent;
const special = function() {
    const specialData = {};
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].add((eventName, eventObject)=>{
        specialData[eventName] = eventObject;
    });
    return {
        getField: (eventName, field)=>specialData[eventName] && specialData[eventName][field],
        callMethod: (eventName, methodName, context, args)=>specialData[eventName] && specialData[eventName][methodName] && specialData[eventName][methodName].apply(context, args)
    };
}();
const eventsEngine = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dependency_injector$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    on: getHandler(normalizeOnArguments(iterate((element, eventName, selector, data, handler)=>{
        const handlersController = getHandlersController(element, eventName);
        handlersController.addHandler(handler, selector, data);
    }))),
    one: getHandler(normalizeOnArguments((element, eventName, selector, data, handler)=>{
        const oneTimeHandler = function() {
            eventsEngine.off(element, eventName, selector, oneTimeHandler);
            handler.apply(this, arguments);
        };
        eventsEngine.on(element, eventName, selector, data, oneTimeHandler);
    })),
    off: getHandler(normalizeOffArguments(iterate((element, eventName, selector, handler)=>{
        const handlersController = getHandlersController(element, eventName);
        handlersController.removeHandler(handler, selector);
    }))),
    trigger: getHandler(normalizeTriggerArguments((element, event, extraParameters)=>{
        const eventName = event.type;
        const handlersController = getHandlersController(element, event.type);
        special.callMethod(eventName, "trigger", element, [
            event,
            extraParameters
        ]);
        handlersController.callHandlers(event, extraParameters);
        const noBubble = special.getField(eventName, "noBubble") || event.isPropagationStopped() || NO_BUBBLE_EVENTS.includes(eventName);
        if (!noBubble) {
            const parents = [];
            const getParents = function(element) {
                var _element_parentNode;
                const parent = (_element_parentNode = element.parentNode) !== null && _element_parentNode !== void 0 ? _element_parentNode : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isObject"])(element.host) ? element.host : null;
                if (parent) {
                    parents.push(parent);
                    getParents(parent);
                }
            };
            getParents(element);
            parents.push(window);
            let i = 0;
            while(parents[i] && !event.isPropagationStopped()){
                const parentDataByEvent = getHandlersController(parents[i], event.type);
                parentDataByEvent.callHandlers((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(event, {
                    currentTarget: parents[i]
                }), extraParameters);
                i++;
            }
        }
        if (element.nodeType || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(element)) {
            special.callMethod(eventName, "_default", element, [
                event,
                extraParameters
            ]);
            callNativeMethod(eventName, element);
        }
    })),
    triggerHandler: getHandler(normalizeTriggerArguments((element, event, extraParameters)=>{
        const handlersController = getHandlersController(element, event.type);
        handlersController.callHandlers(event, extraParameters);
    }))
});
function applyForEach(args, method) {
    const element = args[0];
    if (!element) {
        return;
    }
    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isNode(element) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(element)) {
        method.apply(eventsEngine, args);
    } else if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isString"])(element) && "length" in element) {
        const itemArgs = Array.prototype.slice.call(args, 0);
        Array.prototype.forEach.call(element, (itemElement)=>{
            itemArgs[0] = itemElement;
            applyForEach(itemArgs, method);
        });
    } else {
        throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error("E0025");
    }
}
function getHandler(method) {
    return function() {
        applyForEach(arguments, method);
    };
}
function detectPassiveEventHandlersSupport() {
    let isSupported = false;
    try {
        const options = Object.defineProperty({}, "passive", {
            get () {
                isSupported = true;
                return true;
            }
        });
        window.addEventListener("test", null, options);
    } catch (e) {}
    return isSupported;
}
const passiveEventHandlersSupported = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(detectPassiveEventHandlersSupport);
const contains = (container, element)=>{
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isWindow"])(container)) {
        return contains(container.document, element);
    }
    return container.contains ? container.contains(element) : !!(element.compareDocumentPosition(container) & element.DOCUMENT_POSITION_CONTAINS);
};
function getHandlersController(element, eventName) {
    let elementData = elementDataMap.get(element);
    eventName = eventName || "";
    const eventNameParts = eventName.split(".");
    const namespaces = eventNameParts.slice(1);
    const eventNameIsDefined = !!eventNameParts[0];
    eventName = eventNameParts[0] || EMPTY_EVENT_NAME;
    if (!elementData) {
        elementData = {};
        elementDataMap.set(element, elementData);
    }
    if (!elementData[eventName]) {
        elementData[eventName] = {
            handleObjects: [],
            nativeHandler: null
        };
    }
    const eventData = elementData[eventName];
    return {
        addHandler (handler, selector, data) {
            const callHandler = function(e, extraParameters) {
                const handlerArgs = [
                    e
                ];
                const target = e.currentTarget;
                const { relatedTarget: relatedTarget } = e;
                let secondaryTargetIsInside;
                let result;
                if (eventName in NATIVE_EVENTS_TO_SUBSCRIBE) {
                    secondaryTargetIsInside = relatedTarget && target && (relatedTarget === target || contains(target, relatedTarget));
                }
                if (void 0 !== extraParameters) {
                    handlerArgs.push(extraParameters);
                }
                special.callMethod(eventName, "handle", element, [
                    e,
                    data
                ]);
                if (!secondaryTargetIsInside) {
                    result = handler.apply(target, handlerArgs);
                }
                if (false === result) {
                    e.preventDefault();
                    e.stopPropagation();
                }
            };
            const handleObject = {
                handler: handler,
                wrappedHandler: function(e, extraParameters) {
                    if (skipEvent && e.type === skipEvent) {
                        return;
                    }
                    e.data = data;
                    e.delegateTarget = element;
                    if (selector) {
                        let currentTarget = e.target;
                        while(currentTarget && currentTarget !== element){
                            if (matchesSafe(currentTarget, selector)) {
                                e.currentTarget = currentTarget;
                                callHandler(e, extraParameters);
                            }
                            currentTarget = currentTarget.parentNode;
                        }
                    } else {
                        var _e$target;
                        e.currentTarget = e.delegateTarget || e.target;
                        const isTargetInShadowDOM = Boolean(null === (_e$target = e.target) || void 0 === _e$target ? void 0 : _e$target.shadowRoot);
                        if (isTargetInShadowDOM) {
                            const target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEventTarget"])(e);
                            e.target = target;
                        }
                        callHandler(e, extraParameters);
                    }
                },
                selector: selector,
                type: eventName,
                data: data,
                namespace: namespaces.join("."),
                namespaces: namespaces,
                guid: ++guid
            };
            eventData.handleObjects.push(handleObject);
            const firstHandlerForTheType = 1 === eventData.handleObjects.length;
            let shouldAddNativeListener = firstHandlerForTheType && eventNameIsDefined;
            let nativeListenerOptions;
            if (shouldAddNativeListener) {
                shouldAddNativeListener = !special.callMethod(eventName, "setup", element, [
                    data,
                    namespaces,
                    handler
                ]);
            }
            if (shouldAddNativeListener) {
                eventData.nativeHandler = getNativeHandler(eventName);
                if (passiveEventHandlersSupported() && forcePassiveFalseEventNames.includes(eventName)) {
                    nativeListenerOptions = {
                        passive: false
                    };
                }
                eventData.removeListener = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].listen(element, NATIVE_EVENTS_TO_SUBSCRIBE[eventName] || eventName, eventData.nativeHandler, nativeListenerOptions);
            }
            special.callMethod(eventName, "add", element, [
                handleObject
            ]);
        },
        removeHandler (handler, selector) {
            const removeByEventName = function(eventName) {
                const eventData = elementData[eventName];
                if (!eventData.handleObjects.length) {
                    delete elementData[eventName];
                    return;
                }
                let removedHandler;
                eventData.handleObjects = eventData.handleObjects.filter((handleObject)=>{
                    const skip = namespaces.length && !isSubset(handleObject.namespaces, namespaces) || handler && handleObject.handler !== handler || selector && handleObject.selector !== selector;
                    if (!skip) {
                        removedHandler = handleObject.handler;
                        special.callMethod(eventName, "remove", element, [
                            handleObject
                        ]);
                    }
                    return skip;
                });
                const lastHandlerForTheType = !eventData.handleObjects.length;
                const shouldRemoveNativeListener = lastHandlerForTheType && eventName !== EMPTY_EVENT_NAME;
                if (shouldRemoveNativeListener) {
                    special.callMethod(eventName, "teardown", element, [
                        namespaces,
                        removedHandler
                    ]);
                    if (eventData.nativeHandler) {
                        eventData.removeListener();
                    }
                    delete elementData[eventName];
                }
            };
            if (eventNameIsDefined) {
                removeByEventName(eventName);
            } else {
                for(const name in elementData){
                    removeByEventName(name);
                }
            }
            const elementDataIsEmpty = 0 === Object.keys(elementData).length;
            if (elementDataIsEmpty) {
                elementDataMap.delete(element);
            }
        },
        callHandlers (event, extraParameters) {
            let forceStop = false;
            const handleCallback = function(handleObject) {
                if (forceStop) {
                    return;
                }
                if (!namespaces.length || isSubset(handleObject.namespaces, namespaces)) {
                    handleObject.wrappedHandler(event, extraParameters);
                    forceStop = event.isImmediatePropagationStopped();
                }
            };
            eventData.handleObjects.forEach(handleCallback);
            if (namespaces.length && elementData[EMPTY_EVENT_NAME]) {
                elementData[EMPTY_EVENT_NAME].handleObjects.forEach(handleCallback);
            }
        }
    };
}
function getNativeHandler(subscribeName) {
    return function(event, extraParameters) {
        const handlersController = getHandlersController(this, subscribeName);
        event = eventsEngine.Event(event);
        handlersController.callHandlers(event, extraParameters);
    };
}
function isSubset(original, checked) {
    for(let i = 0; i < checked.length; i++){
        if (original.indexOf(checked[i]) < 0) {
            return false;
        }
    }
    return true;
}
function normalizeOnArguments(callback) {
    return function(element, eventName, selector, data, handler) {
        if (!handler) {
            handler = data;
            data = void 0;
        }
        if ("string" !== typeof selector) {
            data = selector;
            selector = void 0;
        }
        if (!handler && "string" === typeof eventName) {
            handler = data || selector;
            selector = void 0;
            data = void 0;
        }
        callback(element, eventName, selector, data, handler);
    };
}
function normalizeOffArguments(callback) {
    return function(element, eventName, selector, handler) {
        if ("function" === typeof selector) {
            handler = selector;
            selector = void 0;
        }
        callback(element, eventName, selector, handler);
    };
}
function normalizeTriggerArguments(callback) {
    return function(element, src, extraParameters) {
        if ("string" === typeof src) {
            src = {
                type: src
            };
        }
        if (!src.target) {
            src.target = element;
        }
        src.currentTarget = element;
        if (!src.delegateTarget) {
            src.delegateTarget = element;
        }
        if (!src.type && src.originalEvent) {
            src.type = src.originalEvent.type;
        }
        callback(element, src instanceof eventsEngine.Event ? src : eventsEngine.Event(src), extraParameters);
    };
}
function normalizeEventArguments(callback) {
    eventsEngine.Event = function(src, config) {
        if (!(this instanceof eventsEngine.Event)) {
            return new eventsEngine.Event(src, config);
        }
        if (!src) {
            src = {};
        }
        if ("string" === typeof src) {
            src = {
                type: src
            };
        }
        if (!config) {
            config = {};
        }
        callback.call(this, src, config);
    };
    Object.assign(eventsEngine.Event.prototype, {
        _propagationStopped: false,
        _immediatePropagationStopped: false,
        _defaultPrevented: false,
        isPropagationStopped () {
            return !!(this._propagationStopped || this.originalEvent && this.originalEvent.propagationStopped);
        },
        stopPropagation () {
            this._propagationStopped = true;
            this.originalEvent && this.originalEvent.stopPropagation();
        },
        isImmediatePropagationStopped () {
            return this._immediatePropagationStopped;
        },
        stopImmediatePropagation () {
            this.stopPropagation();
            this._immediatePropagationStopped = true;
            this.originalEvent && this.originalEvent.stopImmediatePropagation();
        },
        isDefaultPrevented () {
            return !!(this._defaultPrevented || this.originalEvent && this.originalEvent.defaultPrevented);
        },
        preventDefault () {
            this._defaultPrevented = true;
            this.originalEvent && this.originalEvent.preventDefault();
        }
    });
    return eventsEngine.Event;
}
function iterate(callback) {
    const iterateEventNames = function(element, eventName) {
        if (eventName && eventName.indexOf(" ") > -1) {
            const args = Array.prototype.slice.call(arguments, 0);
            eventName.split(" ").forEach(function(eventName) {
                args[1] = eventName;
                callback.apply(this, args);
            });
        } else {
            callback.apply(this, arguments);
        }
    };
    return function(element, eventName) {
        if ("object" === typeof eventName) {
            const args = Array.prototype.slice.call(arguments, 0);
            for(const name in eventName){
                args[1] = name;
                args[args.length - 1] = eventName[name];
                iterateEventNames.apply(this, args);
            }
        } else {
            iterateEventNames.apply(this, arguments);
        }
    };
}
function callNativeMethod(eventName, element) {
    const nativeMethodName = NATIVE_EVENTS_TO_TRIGGER[eventName] || eventName;
    if (function(eventName, element) {
        return "click" === eventName && "a" === element.localName;
    }(eventName, element)) {
        return;
    }
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(element[nativeMethodName])) {
        skipEvent = eventName;
        element[nativeMethodName]();
        skipEvent = void 0;
    }
}
function calculateWhich(event) {
    if (function(event) {
        return null == event.which && 0 === event.type.indexOf("key");
    }(event)) {
        return null != event.charCode ? event.charCode : event.keyCode;
    }
    if (function(event) {
        return !event.which && void 0 !== event.button && /^(?:mouse|pointer|contextmenu|drag|drop)|click/.test(event.type);
    }(event)) {
        const whichByButton = {
            1: 1,
            2: 3,
            3: 1,
            4: 2
        };
        return whichByButton[event.button];
    }
    return event.which;
}
function initEvent(EventClass) {
    if (EventClass) {
        eventsEngine.Event = EventClass;
        eventsEngine.Event.prototype = EventClass.prototype;
    }
}
initEvent(normalizeEventArguments(function(src, config) {
    var _src$view;
    const srcIsEvent = src instanceof eventsEngine.Event || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasWindow"])() && src instanceof window.Event || (null === (_src$view = src.view) || void 0 === _src$view ? void 0 : _src$view.Event) && src instanceof src.view.Event;
    if (srcIsEvent) {
        this.originalEvent = src;
        this.type = src.type;
        this.currentTarget = void 0;
        if (Object.prototype.hasOwnProperty.call(src, "isTrusted")) {
            this.isTrusted = src.isTrusted;
        }
        this.timeStamp = src.timeStamp || Date.now();
    } else {
        Object.assign(this, src);
    }
    addProperty("which", calculateWhich, this);
    if (0 === src.type.indexOf("touch")) {
        delete config.pageX;
        delete config.pageY;
    }
    Object.assign(this, config);
    this.guid = ++guid;
}));
function addProperty(propName, hook, eventInstance) {
    Object.defineProperty(eventInstance || eventsEngine.Event.prototype, propName, {
        enumerable: true,
        configurable: true,
        get () {
            return this.originalEvent && hook(this.originalEvent);
        },
        set (value) {
            Object.defineProperty(this, propName, {
                enumerable: true,
                configurable: true,
                writable: true,
                value: value
            });
        }
    });
}
EVENT_PROPERTIES.forEach((prop)=>addProperty(prop, (event)=>event[prop]));
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_hook_touch_props$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(addProperty);
const beforeSetStrategy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
const afterSetStrategy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
eventsEngine.set = function(engine) {
    beforeSetStrategy.fire();
    eventsEngine.inject(engine);
    initEvent(engine.Event);
    afterSetStrategy.fire();
};
eventsEngine.subscribeGlobal = function() {
    applyForEach(arguments, normalizeOnArguments(function() {
        const args = arguments;
        eventsEngine.on.apply(this, args);
        beforeSetStrategy.add(function() {
            const offArgs = Array.prototype.slice.call(args, 0);
            offArgs.splice(3, 1);
            eventsEngine.off.apply(this, offArgs);
        });
        afterSetStrategy.add(function() {
            eventsEngine.on.apply(this, args);
        });
    }));
};
eventsEngine.forcePassiveFalseEventNames = forcePassiveFalseEventNames;
eventsEngine.passiveEventHandlersSupported = passiveEventHandlersSupported;
const __TURBOPACK__default__export__ = eventsEngine;
}),
"[project]/node_modules/devextreme/esm/__internal/events/utils/m_add_namespace.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/utils/m_add_namespace.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/errors.js [app-client] (ecmascript)");
;
const addNamespace = (eventNames, namespace)=>{
    if (!namespace) {
        throw __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$errors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Error("E0017");
    }
    if (Array.isArray(eventNames)) {
        return eventNames.map((eventName)=>addNamespace(eventName, namespace)).join(" ");
    }
    if (-1 !== eventNames.indexOf(" ")) {
        return addNamespace(eventNames.split(/\s+/g), namespace);
    }
    return "".concat(eventNames, ".").concat(namespace);
};
const __TURBOPACK__default__export__ = addNamespace;
}),
"[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/utils/index.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "addNamespace": ()=>addNamespace,
    "createEvent": ()=>createEvent,
    "eventData": ()=>eventData,
    "eventDelta": ()=>eventDelta,
    "eventSource": ()=>eventSource,
    "fireEvent": ()=>fireEvent,
    "forceSkipEvents": ()=>forceSkipEvents,
    "getChar": ()=>getChar,
    "hasTouches": ()=>hasTouches,
    "isCommandKeyPressed": ()=>isCommandKeyPressed,
    "isDxMouseWheelEvent": ()=>isDxMouseWheelEvent,
    "isFakeClickEvent": ()=>isFakeClickEvent,
    "isKeyboardEvent": ()=>isKeyboardEvent,
    "isMouseEvent": ()=>isMouseEvent,
    "isPointerEvent": ()=>isPointerEvent,
    "isTouchEvent": ()=>isTouchEvent,
    "needSkipEvent": ()=>needSkipEvent,
    "normalizeKeyName": ()=>normalizeKeyName,
    "setEventFixMethod": ()=>setEventFixMethod,
    "stopEventsSkipping": ()=>stopEventsSkipping
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$selectors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/widget/selectors.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_add_namespace$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_add_namespace.js [app-client] (ecmascript)");
;
;
;
;
;
;
const KEY_MAP = {
    backspace: "backspace",
    tab: "tab",
    enter: "enter",
    escape: "escape",
    pageup: "pageUp",
    pagedown: "pageDown",
    end: "end",
    home: "home",
    arrowleft: "leftArrow",
    arrowup: "upArrow",
    arrowright: "rightArrow",
    arrowdown: "downArrow",
    delete: "del",
    " ": "space",
    f: "F",
    a: "A",
    "*": "asterisk",
    "-": "minus",
    alt: "alt",
    control: "control",
    shift: "shift"
};
const LEGACY_KEY_CODES = {
    8: "backspace",
    9: "tab",
    13: "enter",
    27: "escape",
    33: "pageUp",
    34: "pageDown",
    35: "end",
    36: "home",
    37: "leftArrow",
    38: "upArrow",
    39: "rightArrow",
    40: "downArrow",
    46: "del",
    32: "space",
    70: "F",
    65: "A",
    106: "asterisk",
    109: "minus",
    189: "minus",
    173: "minus",
    16: "shift",
    17: "control",
    18: "alt"
};
const EVENT_SOURCES_REGEX = {
    dx: /^dx/i,
    mouse: /(mouse|wheel)/i,
    touch: /^touch/i,
    keyboard: /^key/i,
    pointer: /^(ms)?pointer/i
};
const eventSource = (_ref)=>{
    let { type: type } = _ref;
    let result = "other";
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(EVENT_SOURCES_REGEX, function(key) {
        if (this.test(type)) {
            result = key;
            return false;
        }
    });
    return result;
};
let fixMethod = (e)=>e;
const getEvent = (originalEvent)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Event(originalEvent, originalEvent);
const copyEvent = (originalEvent)=>fixMethod(getEvent(originalEvent), originalEvent);
const isDxEvent = (e)=>"dx" === eventSource(e);
const isNativeMouseEvent = (e)=>"mouse" === eventSource(e);
const isNativeTouchEvent = (e)=>"touch" === eventSource(e);
const isPointerEvent = (e)=>"pointer" === eventSource(e);
const isMouseEvent = (e)=>isNativeMouseEvent(e) || (isPointerEvent(e) || isDxEvent(e)) && "mouse" === e.pointerType;
const isDxMouseWheelEvent = (e)=>e && "dxmousewheel" === e.type;
const isTouchEvent = (e)=>isNativeTouchEvent(e) || (isPointerEvent(e) || isDxEvent(e)) && "touch" === e.pointerType;
const isKeyboardEvent = (e)=>"keyboard" === eventSource(e);
const isFakeClickEvent = (_ref2)=>{
    let { screenX: screenX, offsetX: offsetX, pageX: pageX } = _ref2;
    return 0 === screenX && !offsetX && 0 === pageX;
};
const eventData = (_ref3)=>{
    let { pageX: pageX, pageY: pageY, timeStamp: timeStamp } = _ref3;
    return {
        x: pageX,
        y: pageY,
        time: timeStamp
    };
};
const eventDelta = (from, to)=>({
        x: to.x - from.x,
        y: to.y - from.y,
        time: to.time - from.time || 1
    });
const hasTouches = (e)=>{
    const { originalEvent: originalEvent, pointers: pointers } = e;
    if (isNativeTouchEvent(e)) {
        return (originalEvent.touches || []).length;
    }
    if (isDxEvent(e)) {
        return (pointers || []).length;
    }
    return 0;
};
let skipEvents = false;
const forceSkipEvents = ()=>{
    skipEvents = true;
};
const stopEventsSkipping = ()=>{
    skipEvents = false;
};
const needSkipEvent = (e)=>{
    if (skipEvents) {
        return true;
    }
    const { target: target } = e;
    const $target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(target);
    const isContentEditable = (null === target || void 0 === target ? void 0 : target.isContentEditable) || (null === target || void 0 === target ? void 0 : target.hasAttribute("contenteditable"));
    const touchInEditable = $target.is("input, textarea, select") || isContentEditable;
    if (isDxMouseWheelEvent(e)) {
        const isTextArea = $target.is("textarea") && $target.hasClass("dx-texteditor-input");
        if (isTextArea || isContentEditable) {
            return false;
        }
        const isInputFocused = $target.is("input[type='number'], textarea, select") && $target.is(":focus");
        return isInputFocused;
    }
    if (isMouseEvent(e)) {
        return touchInEditable || e.which > 1;
    }
    if (isTouchEvent(e)) {
        return touchInEditable && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$widget$2f$selectors$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["focused"])($target);
    }
};
const setEventFixMethod = (func)=>{
    fixMethod = func;
};
const createEvent = (originalEvent, args)=>{
    const event = copyEvent(originalEvent);
    if (args) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(event, args);
    }
    return event;
};
const fireEvent = (props)=>{
    const { originalEvent: originalEvent, delegateTarget: delegateTarget } = props;
    const event = createEvent(originalEvent, props);
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger(delegateTarget || event.target, event);
    return event;
};
const normalizeKeyName = (_ref4)=>{
    let { key: key, which: which } = _ref4;
    const normalizedKey = KEY_MAP[null === key || void 0 === key ? void 0 : key.toLowerCase()] || key;
    const normalizedKeyFromWhich = LEGACY_KEY_CODES[which];
    if (normalizedKeyFromWhich && normalizedKey === key) {
        return normalizedKeyFromWhich;
    }
    if (!normalizedKey && which) {
        return String.fromCharCode(which);
    }
    return normalizedKey;
};
const getChar = (_ref5)=>{
    let { key: key, which: which } = _ref5;
    return key || String.fromCharCode(which);
};
const addNamespace = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_add_namespace$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const isCommandKeyPressed = (_ref6)=>{
    let { ctrlKey: ctrlKey, metaKey: metaKey } = _ref6;
    return ctrlKey || metaKey;
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_event_registrator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator_callbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
;
;
const registerEvent = function(name, eventObject) {
    const strategy = {};
    if ("noBubble" in eventObject) {
        strategy.noBubble = eventObject.noBubble;
    }
    if ("bindType" in eventObject) {
        strategy.bindType = eventObject.bindType;
    }
    if ("delegateType" in eventObject) {
        strategy.delegateType = eventObject.delegateType;
    }
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])([
        "setup",
        "teardown",
        "add",
        "remove",
        "trigger",
        "handle",
        "_default",
        "dispose"
    ], (_, methodName)=>{
        if (!eventObject[methodName]) {
            return;
        }
        strategy[methodName] = function() {
            const args = [].slice.call(arguments);
            args.unshift(this);
            return eventObject[methodName].apply(eventObject, args);
        };
    });
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].fire(name, strategy);
};
registerEvent.callbacks = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const __TURBOPACK__default__export__ = registerEvent;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_remove.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_remove.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "removeEvent": ()=>removeEvent
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element_data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
;
;
;
;
const removeEvent = "dxremove";
const eventPropName = "dxRemoveEvent";
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["beforeCleanData"])((elements)=>{
    elements = [].slice.call(elements);
    for(let i = 0; i < elements.length; i++){
        const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(elements[i]);
        if ($element.prop(eventPropName)) {
            $element[0][eventPropName] = null;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].triggerHandler($element, "dxremove");
        }
    }
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("dxremove", {
    noBubble: true,
    setup (element) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element).prop(eventPropName, true);
    }
});
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_emitter.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/callbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
;
;
;
;
;
const Emitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor (element) {
        this._$element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
        this._cancelCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
        this._acceptCallback = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])();
    },
    getElement () {
        return this._$element;
    },
    validate: (e)=>!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e),
    validatePointers: (e)=>1 === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasTouches"])(e),
    allowInterruptionByMouseWheel: ()=>true,
    configure (data) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(this, data);
    },
    addCancelCallback (callback) {
        this._cancelCallback.add(callback);
    },
    removeCancelCallback () {
        this._cancelCallback.empty();
    },
    _cancel (e) {
        this._cancelCallback.fire(this, e);
    },
    addAcceptCallback (callback) {
        this._acceptCallback.add(callback);
    },
    removeAcceptCallback () {
        this._acceptCallback.empty();
    },
    _accept (e) {
        this._acceptCallback.fire(this, e);
    },
    _requestAccept (e) {
        this._acceptRequestEvent = e;
    },
    _forgetAccept () {
        this._accept(this._acceptRequestEvent);
        this._acceptRequestEvent = null;
    },
    start: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    move: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    end: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    cancel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    reset () {
        if (this._acceptRequestEvent) {
            this._accept(this._acceptRequestEvent);
        }
    },
    _fireEvent (eventName, e, params) {
        const eventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
            type: eventName,
            originalEvent: e,
            target: this._getEmitterTarget(e),
            delegateTarget: this.getElement().get(0)
        }, params);
        e = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])(eventData);
        if (e.cancel) {
            this._cancel(e);
        }
        return e;
    },
    _getEmitterTarget (e) {
        return (this.delegateSelector ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e.target).closest(this.delegateSelector) : this.getElement()).get(0);
    },
    dispose: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"]
});
const __TURBOPACK__default__export__ = Emitter;
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_wheel.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_wheel.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "name": ()=>EVENT_NAME
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
;
;
;
;
const EVENT_NAME = "dxmousewheel";
const EVENT_NAMESPACE = "dxWheel";
const NATIVE_EVENT_NAME = "wheel";
const PIXEL_MODE = 0;
const DELTA_MUTLIPLIER = 30;
const wheel = {
    setup (element) {
        const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("wheel", "dxWheel"), wheel._wheelHandler.bind(wheel));
    },
    teardown (element) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(element, ".dxWheel");
    },
    _wheelHandler (e) {
        const { deltaMode: deltaMode, deltaY: deltaY, deltaX: deltaX, deltaZ: deltaZ } = e.originalEvent;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])({
            type: EVENT_NAME,
            originalEvent: e,
            delta: this._normalizeDelta(deltaY, deltaMode),
            deltaX: deltaX,
            deltaY: deltaY,
            deltaZ: deltaZ,
            deltaMode: deltaMode,
            pointerType: "mouse"
        });
        e.stopPropagation();
    },
    _normalizeDelta (delta) {
        let deltaMode = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;
        if (0 === deltaMode) {
            return -delta;
        }
        return -30 * delta;
    }
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(EVENT_NAME, wheel);
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/pointer/m_base.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/pointer/m_base.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/event_target.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_target.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/browser.js [app-client] (ecmascript)");
;
;
;
;
;
;
const POINTER_EVENTS_NAMESPACE = "dxPointerEvents";
const BaseStrategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor (eventName, originalEvents) {
        this._eventName = eventName;
        this._originalEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(originalEvents, "dxPointerEvents");
        this._handlerCount = 0;
        this.noBubble = this._isNoBubble();
    },
    _isNoBubble () {
        const eventName = this._eventName;
        return "dxpointerenter" === eventName || "dxpointerleave" === eventName;
    },
    _handler (e) {
        const delegateTarget = this._getDelegateTarget(e);
        const event = {
            type: this._eventName,
            pointerType: e.pointerType || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventSource"])(e),
            originalEvent: e,
            delegateTarget: delegateTarget,
            timeStamp: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].mozilla ? (new Date).getTime() : e.timeStamp
        };
        const target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEventTarget"])(e);
        event.target = target;
        return this._fireEvent(event);
    },
    _getDelegateTarget (e) {
        let delegateTarget;
        if (this.noBubble) {
            delegateTarget = e.delegateTarget;
        }
        return delegateTarget;
    },
    _fireEvent: (args)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])(args),
    _setSelector (handleObj) {
        this._selector = this.noBubble && handleObj ? handleObj.selector : null;
    },
    _getSelector () {
        return this._selector;
    },
    setup: ()=>true,
    add (element, handleObj) {
        if (this._handlerCount <= 0 || this.noBubble) {
            element = this.noBubble ? element : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
            this._setSelector(handleObj);
            const that = this;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(element, this._originalEvents, this._getSelector(), (e)=>{
                that._handler(e);
            });
        }
        if (!this.noBubble) {
            this._handlerCount++;
        }
    },
    remove (handleObj) {
        this._setSelector(handleObj);
        if (!this.noBubble) {
            this._handlerCount--;
        }
    },
    teardown (element) {
        if (this._handlerCount && !this.noBubble) {
            return;
        }
        element = this.noBubble ? element : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
        if (".dxPointerEvents" !== this._originalEvents) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(element, this._originalEvents, this._getSelector());
        }
    },
    dispose (element) {
        element = this.noBubble ? element : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(element, this._originalEvents);
    }
});
const __TURBOPACK__default__export__ = BaseStrategy;
}),
"[project]/node_modules/devextreme/esm/__internal/events/pointer/m_observer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/pointer/m_observer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ready_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/ready_callbacks.js [app-client] (ecmascript)");
;
;
;
const addEventsListener = function(events, handler) {
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ready_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].add(()=>{
        events.split(" ").forEach((event)=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].listen(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument(), event, handler, true);
        });
    });
};
const Observer = function(eventMap, pointerEquals, onPointerAdding) {
    onPointerAdding = onPointerAdding || function() {};
    let pointers = [];
    const getPointerIndex = function(e) {
        let index = -1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(pointers, (i, pointer)=>{
            if (!pointerEquals(e, pointer)) {
                return true;
            }
            index = i;
            return false;
        });
        return index;
    };
    const removePointer = function(e) {
        const index = getPointerIndex(e);
        if (index > -1) {
            pointers.splice(index, 1);
        }
    };
    addEventsListener(eventMap.dxpointerdown, function(e) {
        if (-1 === getPointerIndex(e)) {
            onPointerAdding(e);
            pointers.push(e);
        }
    });
    addEventsListener(eventMap.dxpointermove, function(e) {
        pointers[getPointerIndex(e)] = e;
    });
    addEventsListener(eventMap.dxpointerup, removePointer);
    addEventsListener(eventMap.dxpointercancel, removePointer);
    this.pointers = function() {
        return pointers;
    };
    this.reset = function() {
        pointers = [];
    };
};
const __TURBOPACK__default__export__ = Observer;
}),
"[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/pointer/m_mouse.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/base.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$observer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/observer.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_observer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_observer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/browser.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
;
;
;
const eventMap = {
    dxpointerdown: "mousedown",
    dxpointermove: "mousemove",
    dxpointerup: "mouseup",
    dxpointercancel: "pointercancel",
    dxpointerover: "mouseover",
    dxpointerout: "mouseout",
    dxpointerenter: "mouseenter",
    dxpointerleave: "mouseleave"
};
if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$browser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].safari) {
    eventMap.dxpointercancel += " dragstart";
}
const normalizeMouseEvent = function(e) {
    e.pointerId = 1;
    return {
        pointers: observer.pointers(),
        pointerId: 1
    };
};
let observer;
let activated = false;
const activateStrategy = function() {
    if (activated) {
        return;
    }
    observer = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_observer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"](eventMap, ()=>true);
    activated = true;
};
const MouseStrategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor () {
        this.callBase.apply(this, arguments);
        activateStrategy();
    },
    _fireEvent (args) {
        return this.callBase((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(normalizeMouseEvent(args.originalEvent), args));
    }
});
MouseStrategy.map = eventMap;
MouseStrategy.normalize = normalizeMouseEvent;
MouseStrategy.activate = activateStrategy;
MouseStrategy.resetObserver = function() {
    observer.reset();
};
const __TURBOPACK__default__export__ = MouseStrategy;
}),
"[project]/node_modules/devextreme/esm/__internal/events/pointer/m_touch.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/pointer/m_touch.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/base.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
;
;
;
;
const eventMap = {
    dxpointerdown: "touchstart",
    dxpointermove: "touchmove",
    dxpointerup: "touchend",
    dxpointercancel: "touchcancel",
    dxpointerover: "",
    dxpointerout: "",
    dxpointerenter: "",
    dxpointerleave: ""
};
const normalizeTouchEvent = function(e) {
    const pointers = [];
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(e.touches, (_, touch)=>{
        pointers.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
            pointerId: touch.identifier
        }, touch));
    });
    return {
        pointers: pointers,
        pointerId: e.changedTouches[0].identifier
    };
};
const skipTouchWithSameIdentifier = function(pointerEvent) {
    return "ios" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real().platform && ("dxpointerdown" === pointerEvent || "dxpointerup" === pointerEvent);
};
const TouchStrategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor () {
        this.callBase.apply(this, arguments);
        this._pointerId = 0;
    },
    _handler (e) {
        if (skipTouchWithSameIdentifier(this._eventName)) {
            const touch = e.changedTouches[0];
            if (this._pointerId === touch.identifier && 0 !== this._pointerId) {
                return;
            }
            this._pointerId = touch.identifier;
        }
        return this.callBase.apply(this, arguments);
    },
    _fireEvent (args) {
        return this.callBase((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(normalizeTouchEvent(args.originalEvent), args));
    }
});
TouchStrategy.map = eventMap;
TouchStrategy.normalize = normalizeTouchEvent;
const __TURBOPACK__default__export__ = TouchStrategy;
}),
"[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse_and_touch.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/pointer/m_mouse_and_touch.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/base.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/touch.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_touch.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
;
;
;
;
;
const eventMap = {
    dxpointerdown: "touchstart mousedown",
    dxpointermove: "touchmove mousemove",
    dxpointerup: "touchend mouseup",
    dxpointercancel: "touchcancel",
    dxpointerover: "mouseover",
    dxpointerout: "mouseout",
    dxpointerenter: "mouseenter",
    dxpointerleave: "mouseleave"
};
let activated = false;
const activateStrategy = function() {
    if (activated) {
        return;
    }
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].activate();
    activated = true;
};
const MouseAndTouchStrategy = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    EVENT_LOCK_TIMEOUT: 100,
    ctor () {
        this.callBase.apply(this, arguments);
        activateStrategy();
    },
    _handler (e) {
        const isMouse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMouseEvent"])(e);
        if (!isMouse) {
            this._skipNextEvents = true;
        }
        if (isMouse && this._mouseLocked) {
            return;
        }
        if (isMouse && this._skipNextEvents) {
            this._skipNextEvents = false;
            this._mouseLocked = true;
            clearTimeout(this._unlockMouseTimer);
            const that = this;
            this._unlockMouseTimer = setTimeout(()=>{
                that._mouseLocked = false;
            }, this.EVENT_LOCK_TIMEOUT);
            return;
        }
        return this.callBase(e);
    },
    _fireEvent (args) {
        const normalizer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMouseEvent"])(args.originalEvent) ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].normalize : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].normalize;
        return this.callBase((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(normalizer(args.originalEvent), args));
    },
    dispose () {
        this.callBase();
        this._skipNextEvents = false;
        this._mouseLocked = false;
        clearTimeout(this._unlockMouseTimer);
    }
});
MouseAndTouchStrategy.map = eventMap;
MouseAndTouchStrategy.resetObserver = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resetObserver;
const __TURBOPACK__default__export__ = MouseAndTouchStrategy;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_pointer.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/mouse_and_touch.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_mouse_and_touch.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2f$touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer/touch.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/pointer/m_touch.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
const getStrategy = (support, _ref)=>{
    let { tablet: tablet, phone: phone } = _ref;
    const pointerEventStrategy = getStrategyFromGlobalConfig();
    if (pointerEventStrategy) {
        return pointerEventStrategy;
    }
    if (support.touch && !(tablet || phone)) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
    }
    if (support.touch) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
};
const EventStrategy = getStrategy(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real());
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(EventStrategy.map, (pointerEvent, originalEvents)=>{
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(pointerEvent, new EventStrategy(pointerEvent, originalEvents));
});
const pointer = {
    down: "dxpointerdown",
    up: "dxpointerup",
    move: "dxpointermove",
    cancel: "dxpointercancel",
    enter: "dxpointerenter",
    leave: "dxpointerleave",
    over: "dxpointerover",
    out: "dxpointerout"
};
function getStrategyFromGlobalConfig() {
    const eventStrategyName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])().pointerEventStrategy;
    return ({
        "mouse-and-touch": __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse_and_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        touch: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_touch$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        mouse: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$pointer$2f$m_mouse$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    })[eventStrategyName];
}
const __TURBOPACK__default__export__ = pointer;
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_emitter_registrator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$wheel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/wheel.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_wheel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_wheel.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element_data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ready_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/ready_callbacks.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const MANAGER_EVENT = "dxEventManager";
const EMITTER_DATA = "dxEmitter";
const EventManager = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor () {
        this._attachHandlers();
        this.reset();
        this._proxiedCancelHandler = this._cancelHandler.bind(this);
        this._proxiedAcceptHandler = this._acceptHandler.bind(this);
    },
    _attachHandlers () {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ready_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].add(()=>{
            const document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].down, MANAGER_EVENT), this._pointerDownHandler.bind(this));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].move, MANAGER_EVENT), this._pointerMoveHandler.bind(this));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])([
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].up,
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].cancel
            ].join(" "), MANAGER_EVENT), this._pointerUpHandler.bind(this));
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_wheel$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["name"], MANAGER_EVENT), this._mouseWheelHandler.bind(this));
        });
    },
    _eachEmitter (callback) {
        const activeEmitters = this._activeEmitters || [];
        let i = 0;
        while(activeEmitters.length > i){
            const emitter = activeEmitters[i];
            if (false === callback(emitter)) {
                break;
            }
            if (activeEmitters[i] === emitter) {
                i++;
            }
        }
    },
    _applyToEmitters (method, arg) {
        this._eachEmitter((emitter)=>{
            emitter[method].call(emitter, arg);
        });
    },
    reset () {
        this._eachEmitter(this._proxiedCancelHandler);
        this._activeEmitters = [];
    },
    resetEmitter (emitter) {
        this._proxiedCancelHandler(emitter);
    },
    _pointerDownHandler (e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMouseEvent"])(e) && e.which > 1) {
            return;
        }
        this._updateEmitters(e);
    },
    _updateEmitters (e) {
        if (!this._isSetChanged(e)) {
            return;
        }
        this._cleanEmitters(e);
        this._fetchEmitters(e);
    },
    _isSetChanged (e) {
        const currentSet = this._closestEmitter(e);
        const previousSet = this._emittersSet || [];
        let setChanged = currentSet.length !== previousSet.length;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(currentSet, (index, emitter)=>{
            setChanged = setChanged || previousSet[index] !== emitter;
            return !setChanged;
        });
        this._emittersSet = currentSet;
        return setChanged;
    },
    _closestEmitter (e) {
        const that = this;
        const result = [];
        let $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e.target);
        function handleEmitter(_, emitter) {
            if (!!emitter && emitter.validatePointers(e) && emitter.validate(e)) {
                emitter.addCancelCallback(that._proxiedCancelHandler);
                emitter.addAcceptCallback(that._proxiedAcceptHandler);
                result.push(emitter);
            }
        }
        while($element.length){
            const emitters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])($element.get(0), "dxEmitter") || [];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(emitters, handleEmitter);
            $element = $element.parent();
        }
        return result;
    },
    _acceptHandler (acceptedEmitter, e) {
        this._eachEmitter((emitter)=>{
            if (emitter !== acceptedEmitter) {
                this._cancelEmitter(emitter, e);
            }
        });
    },
    _cancelHandler (canceledEmitter, e) {
        this._cancelEmitter(canceledEmitter, e);
    },
    _cancelEmitter (emitter, e) {
        const activeEmitters = this._activeEmitters;
        if (e) {
            emitter.cancel(e);
        } else {
            emitter.reset();
        }
        emitter.removeCancelCallback();
        emitter.removeAcceptCallback();
        const emitterIndex = activeEmitters.indexOf(emitter);
        if (emitterIndex > -1) {
            activeEmitters.splice(emitterIndex, 1);
        }
    },
    _cleanEmitters (e) {
        this._applyToEmitters("end", e);
        this.reset(e);
    },
    _fetchEmitters (e) {
        this._activeEmitters = this._emittersSet.slice();
        this._applyToEmitters("start", e);
    },
    _pointerMoveHandler (e) {
        this._applyToEmitters("move", e);
    },
    _pointerUpHandler (e) {
        this._updateEmitters(e);
    },
    _mouseWheelHandler (e) {
        if (!this._allowInterruptionByMouseWheel()) {
            return;
        }
        e.pointers = [
            null
        ];
        this._pointerDownHandler(e);
        this._adjustWheelEvent(e);
        this._pointerMoveHandler(e);
        e.pointers = [];
        this._pointerUpHandler(e);
    },
    _allowInterruptionByMouseWheel () {
        let allowInterruption = true;
        this._eachEmitter((emitter)=>{
            allowInterruption = emitter.allowInterruptionByMouseWheel() && allowInterruption;
            return allowInterruption;
        });
        return allowInterruption;
    },
    _adjustWheelEvent (e) {
        let closestGestureEmitter = null;
        this._eachEmitter((emitter)=>{
            if (!emitter.gesture) {
                return;
            }
            const direction = emitter.getDirection(e);
            if ("horizontal" !== direction && !e.shiftKey || "vertical" !== direction && e.shiftKey) {
                closestGestureEmitter = emitter;
                return false;
            }
        });
        if (!closestGestureEmitter) {
            return;
        }
        const direction = closestGestureEmitter.getDirection(e);
        const verticalGestureDirection = "both" === direction && !e.shiftKey || "vertical" === direction;
        const prop = verticalGestureDirection ? "pageY" : "pageX";
        e[prop] += e.delta;
    },
    isActive (element) {
        let result = false;
        this._eachEmitter((emitter)=>{
            result = result || emitter.getElement().is(element);
        });
        return result;
    }
});
const eventManager = new EventManager;
const EMITTER_SUBSCRIPTION_DATA = "dxEmitterSubscription";
const registerEmitter = function(emitterConfig) {
    const EmitterClass = emitterConfig.emitter;
    const emitterName = emitterConfig.events[0];
    const emitterEvents = emitterConfig.events;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(emitterEvents, (_, eventName)=>{
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(eventName, {
            noBubble: !emitterConfig.bubble,
            setup (element) {
                const subscriptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitterSubscription") || {};
                const emitters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitter") || {};
                const emitter = emitters[emitterName] || new EmitterClass(element);
                subscriptions[eventName] = true;
                emitters[emitterName] = emitter;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitter", emitters);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitterSubscription", subscriptions);
            },
            add (element, handleObj) {
                const emitters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitter");
                const emitter = emitters[emitterName];
                emitter.configure((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                    delegateSelector: handleObj.selector
                }, handleObj.data), handleObj.type);
            },
            teardown (element) {
                const subscriptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitterSubscription");
                const emitters = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxEmitter");
                const emitter = emitters[emitterName];
                delete subscriptions[eventName];
                let disposeEmitter = true;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(emitterEvents, (_, eventName)=>{
                    disposeEmitter = disposeEmitter && !subscriptions[eventName];
                    return disposeEmitter;
                });
                if (disposeEmitter) {
                    if (eventManager.isActive(element)) {
                        eventManager.resetEmitter(emitter);
                    }
                    emitter && emitter.dispose();
                    delete emitters[emitterName];
                }
            }
        });
    });
};
const __TURBOPACK__default__export__ = registerEmitter;
}),
"[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_nodes_disposing.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/utils/m_event_nodes_disposing.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "subscribeNodesDisposing": ()=>subscribeNodesDisposing,
    "unsubscribeNodesDisposing": ()=>unsubscribeNodesDisposing
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/remove.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_remove.js [app-client] (ecmascript)");
;
;
function nodesByEvent(event) {
    return event && [
        event.target,
        event.delegateTarget,
        event.relatedTarget,
        event.currentTarget
    ].filter((node)=>!!node);
}
const subscribeNodesDisposing = (event, callback)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].one(nodesByEvent(event), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeEvent"], callback);
};
const unsubscribeNodesDisposing = (event, callback)=>{
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(nodesByEvent(event), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_remove$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeEvent"], callback);
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_click.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_click.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "name": ()=>CLICK_EVENT_NAME
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/animation/frame.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/event_nodes_disposing.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_nodes_disposing.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/event_target.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/m_event_target.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const CLICK_EVENT_NAME = "dxclick";
const misc = {
    requestAnimationFrame: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["requestAnimationFrame"],
    cancelAnimationFrame: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"]
};
let prevented = null;
let lastFiredEvent = null;
const onNodeRemove = ()=>{
    lastFiredEvent = null;
};
const clickHandler = function(e) {
    const { originalEvent: originalEvent } = e;
    const eventAlreadyFired = lastFiredEvent === originalEvent || originalEvent && originalEvent.DXCLICK_FIRED;
    const leftButton = !e.which || 1 === e.which;
    if (leftButton && !prevented && !eventAlreadyFired) {
        if (originalEvent) {
            originalEvent.DXCLICK_FIRED = true;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unsubscribeNodesDisposing"])(lastFiredEvent, onNodeRemove);
        lastFiredEvent = originalEvent;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_nodes_disposing$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["subscribeNodesDisposing"])(lastFiredEvent, onNodeRemove);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])({
            type: "dxclick",
            originalEvent: e
        });
    }
};
const ClickEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor (element) {
        this.callBase(element);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this.getElement(), "click", clickHandler);
    },
    start () {
        prevented = null;
    },
    cancel () {
        prevented = true;
    },
    dispose () {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this.getElement(), "click", clickHandler);
    }
});
!function() {
    const desktopDevice = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real().generic;
    if (!desktopDevice) {
        let startTarget = null;
        let blurPrevented = false;
        const isInput = function(element) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element).is("input, textarea, select, button ,:focus, :focus *");
        };
        const pointerDownHandler = function(e) {
            startTarget = e.target;
            blurPrevented = e.isDefaultPrevented();
        };
        const getTarget = function(e) {
            const target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$m_event_target$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getEventTarget"])(e);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(target);
        };
        const clickHandler = function(e) {
            const $target = getTarget(e);
            if (!blurPrevented && startTarget && !$target.is(startTarget) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(startTarget).is("label") && isInput($target)) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resetActiveElement();
            }
            startTarget = null;
            blurPrevented = false;
        };
        const NATIVE_CLICK_FIXER_NAMESPACE = "NATIVE_CLICK_FIXER";
        const document = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument();
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].down, NATIVE_CLICK_FIXER_NAMESPACE), pointerDownHandler);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal(document, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("click", NATIVE_CLICK_FIXER_NAMESPACE), clickHandler);
    }
}();
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: ClickEmitter,
    bubble: true,
    events: [
        "dxclick"
    ]
});
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_dblclick.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_dblclick.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "dblClick": ()=>dblClick,
    "name": ()=>DBLCLICK_EVENT_NAME
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/click.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_click.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/dom_adapter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dom.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-client] (ecmascript)");
;
;
;
;
;
;
const DBLCLICK_EVENT_NAME = "dxdblclick";
const DBLCLICK_NAMESPACE = "dxDblClick";
const NAMESPACED_CLICK_EVENT = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_click$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["name"], "dxDblClick");
const DBLCLICK_TIMEOUT = 300;
const DblClick = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor () {
        this._handlerCount = 0;
        this._forgetLastClick();
    },
    _forgetLastClick () {
        this._firstClickTarget = null;
        this._lastClickTimeStamp = -300;
    },
    add () {
        if (this._handlerCount <= 0) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument(), NAMESPACED_CLICK_EVENT, this._clickHandler.bind(this));
        }
        this._handlerCount += 1;
    },
    _clickHandler (e) {
        const timeStamp = e.timeStamp || Date.now();
        const timeBetweenClicks = timeStamp - this._lastClickTimeStamp;
        const isSimulated = timeBetweenClicks < 0;
        const isDouble = !isSimulated && timeBetweenClicks < 300;
        if (isDouble) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])({
                type: "dxdblclick",
                target: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["closestCommonParent"])(this._firstClickTarget, e.target),
                originalEvent: e
            });
            this._forgetLastClick();
        } else {
            this._firstClickTarget = e.target;
            this._lastClickTimeStamp = timeStamp;
            clearTimeout(this._lastClickClearTimeout);
            this._lastClickClearTimeout = setTimeout(()=>{
                this._forgetLastClick();
            }, 600);
        }
    },
    remove () {
        this._handlerCount -= 1;
        if (this._handlerCount <= 0) {
            this._forgetLastClick();
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$dom_adapter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getDocument(), NAMESPACED_CLICK_EVENT, void 0);
            clearTimeout(this._lastClickClearTimeout);
            this._handlerCount = 0;
        }
    }
});
const dblClick = new DblClick;
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.feedback.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_emitter.feedback.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "active": ()=>ACTIVE_EVENT_NAME,
    "inactive": ()=>INACTIVE_EVENT_NAME,
    "lock": ()=>lock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dom.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
const ACTIVE_EVENT_NAME = "dxactive";
const INACTIVE_EVENT_NAME = "dxinactive";
const ACTIVE_TIMEOUT = 30;
const INACTIVE_TIMEOUT = 400;
const FeedbackEvent = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor (timeout, fire) {
        this._timeout = timeout;
        this._fire = fire;
    },
    start () {
        const that = this;
        this._schedule(()=>{
            that.force();
        });
    },
    _schedule (fn) {
        this.stop();
        this._timer = setTimeout(fn, this._timeout);
    },
    stop () {
        clearTimeout(this._timer);
    },
    force () {
        if (this._fired) {
            return;
        }
        this.stop();
        this._fire();
        this._fired = true;
    },
    fired () {
        return this._fired;
    }
});
let activeFeedback;
const FeedbackEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor () {
        this.callBase.apply(this, arguments);
        this._active = new FeedbackEvent(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"]);
        this._inactive = new FeedbackEvent(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"]);
    },
    configure (data, eventName) {
        switch(eventName){
            case "dxactive":
                data.activeTimeout = data.timeout;
                break;
            case "dxinactive":
                data.inactiveTimeout = data.timeout;
        }
        this.callBase(data);
    },
    start (e) {
        if (activeFeedback) {
            const activeChildExists = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["contains"])(this.getElement().get(0), activeFeedback.getElement().get(0));
            const childJustActivated = !activeFeedback._active.fired();
            if (activeChildExists && childJustActivated) {
                this._cancel();
                return;
            }
            activeFeedback._inactive.force();
        }
        activeFeedback = this;
        this._initEvents(e);
        this._active.start();
    },
    _initEvents (e) {
        const that = this;
        const eventTarget = this._getEmitterTarget(e);
        const mouseEvent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMouseEvent"])(e);
        const isSimulator = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isSimulator();
        const deferFeedback = isSimulator || !mouseEvent;
        const activeTimeout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureDefined"])(this.activeTimeout, 30);
        const inactiveTimeout = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ensureDefined"])(this.inactiveTimeout, 400);
        this._active = new FeedbackEvent(deferFeedback ? activeTimeout : 0, ()=>{
            that._fireEvent("dxactive", e, {
                target: eventTarget
            });
        });
        this._inactive = new FeedbackEvent(deferFeedback ? inactiveTimeout : 0, ()=>{
            that._fireEvent("dxinactive", e, {
                target: eventTarget
            });
            activeFeedback = null;
        });
    },
    cancel (e) {
        this.end(e);
    },
    end (e) {
        const skipTimers = e.type !== __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].up;
        if (skipTimers) {
            this._active.stop();
        } else {
            this._active.force();
        }
        this._inactive.start();
        if (skipTimers) {
            this._inactive.force();
        }
    },
    dispose () {
        this._active.stop();
        this._inactive.stop();
        if (activeFeedback === this) {
            activeFeedback = null;
        }
        this.callBase();
    },
    lockInactive () {
        this._active.force();
        this._inactive.stop();
        activeFeedback = null;
        this._cancel();
        return this._inactive.force.bind(this._inactive);
    }
});
FeedbackEmitter.lock = function(deferred) {
    const lockInactive = activeFeedback ? activeFeedback.lockInactive() : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
    deferred.done(lockInactive);
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: FeedbackEmitter,
    events: [
        "dxactive",
        "dxinactive"
    ]
});
const { lock: lock } = FeedbackEmitter;
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_hover.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_hover.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "end": ()=>HOVEREND,
    "start": ()=>HOVERSTART
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/pointer.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_pointer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element_data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const HOVERSTART_NAMESPACE = "dxHoverStart";
const HOVERSTART = "dxhoverstart";
const POINTERENTER_NAMESPACED_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].enter, "dxHoverStart");
const HOVEREND_NAMESPACE = "dxHoverEnd";
const HOVEREND = "dxhoverend";
const POINTERLEAVE_NAMESPACED_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_pointer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].leave, "dxHoverEnd");
const Hover = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    noBubble: true,
    ctor () {
        this._handlerArrayKeyPath = "".concat(this._eventNamespace, "_HandlerStore");
    },
    setup (element) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, this._handlerArrayKeyPath, {});
    },
    add (element, handleObj) {
        const that = this;
        const handler = function(e) {
            that._handler(e);
        };
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(element, this._originalEventName, handleObj.selector, handler);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, this._handlerArrayKeyPath)[handleObj.guid] = handler;
    },
    _handler (e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTouchEvent"])(e) || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isSimulator()) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])({
            type: this._eventName,
            originalEvent: e,
            delegateTarget: e.delegateTarget
        });
    },
    remove (element, handleObj) {
        const handler = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, this._handlerArrayKeyPath)[handleObj.guid];
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(element, this._originalEventName, handleObj.selector, handler);
    },
    teardown (element) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeData"])(element, this._handlerArrayKeyPath);
    }
});
const HoverStart = Hover.inherit({
    ctor () {
        this._eventNamespace = "dxHoverStart";
        this._eventName = HOVERSTART;
        this._originalEventName = POINTERENTER_NAMESPACED_EVENT_NAME;
        this.callBase();
    },
    _handler (e) {
        const pointers = e.pointers || [];
        if (!pointers.length) {
            this.callBase(e);
        }
    }
});
const HoverEnd = Hover.inherit({
    ctor () {
        this._eventNamespace = "dxHoverEnd";
        this._eventName = HOVEREND;
        this._originalEventName = POINTERLEAVE_NAMESPACED_EVENT_NAME;
        this.callBase();
    }
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(HOVERSTART, new HoverStart);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(HOVEREND, new HoverEnd);
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/core/m_keyboard_processor.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/core/m_keyboard_processor.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
;
;
;
;
const COMPOSITION_START_EVENT = "compositionstart";
const COMPOSITION_END_EVENT = "compositionend";
const KEYDOWN_EVENT = "keydown";
const NAMESPACE = "KeyboardProcessor";
const createKeyDownOptions = (e)=>({
        keyName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeKeyName"])(e),
        key: e.key,
        code: e.code,
        ctrl: e.ctrlKey,
        location: e.location,
        metaKey: e.metaKey,
        shift: e.shiftKey,
        alt: e.altKey,
        which: e.which,
        originalEvent: e
    });
const KeyboardProcessor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    _keydown: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("keydown", NAMESPACE),
    _compositionStart: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("compositionstart", NAMESPACE),
    _compositionEnd: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("compositionend", NAMESPACE),
    ctor (options) {
        options = options || {};
        if (options.element) {
            this._element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(options.element);
        }
        if (options.focusTarget) {
            this._focusTarget = options.focusTarget;
        }
        this._handler = options.handler;
        if (this._element) {
            this._processFunction = (e)=>{
                const focusTargets = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(this._focusTarget).toArray();
                const isNotFocusTarget = this._focusTarget && this._focusTarget !== e.target && !focusTargets.includes(e.target);
                const shouldSkipProcessing = this._isComposingJustFinished && 229 === e.which || this._isComposing || isNotFocusTarget;
                this._isComposingJustFinished = false;
                if (!shouldSkipProcessing) {
                    this.process(e);
                }
            };
            this._toggleProcessingWithContext = this.toggleProcessing.bind(this);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this._element, this._keydown, this._processFunction);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this._element, this._compositionStart, this._toggleProcessingWithContext);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this._element, this._compositionEnd, this._toggleProcessingWithContext);
        }
    },
    dispose () {
        if (this._element) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._element, this._keydown, this._processFunction);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._element, this._compositionStart, this._toggleProcessingWithContext);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._element, this._compositionEnd, this._toggleProcessingWithContext);
        }
        this._element = void 0;
        this._handler = void 0;
    },
    process (e) {
        this._handler(createKeyDownOptions(e));
    },
    toggleProcessing (_ref) {
        let { type: type } = _ref;
        this._isComposing = "compositionstart" === type;
        this._isComposingJustFinished = !this._isComposing;
    }
});
KeyboardProcessor.createKeyDownOptions = createKeyDownOptions;
const __TURBOPACK__default__export__ = KeyboardProcessor;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_short.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_short.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "active": ()=>active,
    "click": ()=>click,
    "dxClick": ()=>dxClick,
    "focus": ()=>focus,
    "hover": ()=>hover,
    "keyboard": ()=>keyboard,
    "resize": ()=>resize,
    "visibility": ()=>visibility
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$keyboard_processor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/keyboard_processor.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_keyboard_processor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_keyboard_processor.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
;
;
;
function addNamespace(event, namespace) {
    return namespace ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(event, namespace) : event;
}
function executeAction(action, args) {
    return "function" === typeof action ? action(args) : action.execute(args);
}
const active = {
    on: ($el, active, inactive, opts)=>{
        const { selector: selector, showTimeout: showTimeout, hideTimeout: hideTimeout, namespace: namespace } = opts;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxactive", namespace), selector, {
            timeout: showTimeout
        }, (event)=>executeAction(active, {
                event: event,
                element: event.currentTarget
            }));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxinactive", namespace), selector, {
            timeout: hideTimeout
        }, (event)=>executeAction(inactive, {
                event: event,
                element: event.currentTarget
            }));
    },
    off: ($el, _ref)=>{
        let { namespace: namespace, selector: selector } = _ref;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxactive", namespace), selector);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxinactive", namespace), selector);
    }
};
const resize = {
    on: function($el, resize) {
        let { namespace: namespace } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxresize", namespace), resize);
    },
    off: function($el) {
        let { namespace: namespace } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxresize", namespace));
    }
};
const hover = {
    on: ($el, start, end, _ref2)=>{
        let { selector: selector, namespace: namespace } = _ref2;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxhoverend", namespace), selector, (event)=>end(event));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxhoverstart", namespace), selector, (event)=>executeAction(start, {
                element: event.target,
                event: event
            }));
    },
    off: ($el, _ref3)=>{
        let { selector: selector, namespace: namespace } = _ref3;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxhoverstart", namespace), selector);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxhoverend", namespace), selector);
    }
};
const visibility = {
    on: ($el, shown, hiding, _ref4)=>{
        let { namespace: namespace } = _ref4;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxhiding", namespace), hiding);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxshown", namespace), shown);
    },
    off: ($el, _ref5)=>{
        let { namespace: namespace } = _ref5;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxhiding", namespace));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxshown", namespace));
    }
};
const focus = {
    on: ($el, focusIn, focusOut, _ref6)=>{
        let { namespace: namespace } = _ref6;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("focusin", namespace), focusIn);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("focusout", namespace), focusOut);
    },
    off: ($el, _ref7)=>{
        let { namespace: namespace } = _ref7;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("focusin", namespace));
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("focusout", namespace));
    },
    trigger: ($el)=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].trigger($el, "focus")
};
const dxClick = {
    on: function($el, click) {
        let { namespace: namespace } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("dxclick", namespace), click);
    },
    off: function($el) {
        let { namespace: namespace } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("dxclick", namespace));
    }
};
const click = {
    on: function($el, click) {
        let { namespace: namespace } = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($el, addNamespace("click", namespace), click);
    },
    off: function($el) {
        let { namespace: namespace } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off($el, addNamespace("click", namespace));
    }
};
let index = 0;
const keyboardProcessors = {};
const generateListenerId = ()=>"keyboardProcessorId" + index++;
const keyboard = {
    on: (element, focusTarget, handler)=>{
        const listenerId = generateListenerId();
        keyboardProcessors[listenerId] = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_keyboard_processor$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]({
            element: element,
            focusTarget: focusTarget,
            handler: handler
        });
        return listenerId;
    },
    off: (listenerId)=>{
        if (listenerId && keyboardProcessors[listenerId]) {
            keyboardProcessors[listenerId].dispose();
            delete keyboardProcessors[listenerId];
        }
    },
    _getProcessor: (listenerId)=>keyboardProcessors[listenerId]
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_visibility_change.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_visibility_change.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "triggerHidingEvent": ()=>triggerHidingEvent,
    "triggerResizeEvent": ()=>triggerResizeEvent,
    "triggerShownEvent": ()=>triggerShownEvent
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
;
;
const triggerVisibilityChangeEvent = function(eventName) {
    return function(element) {
        const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element || "body");
        const changeHandlers = $element.filter(".dx-visibility-change-handler").add($element.find(".dx-visibility-change-handler"));
        for(let i = 0; i < changeHandlers.length; i++){
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].triggerHandler(changeHandlers[i], eventName);
        }
    };
};
const triggerShownEvent = triggerVisibilityChangeEvent("dxshown");
const triggerHidingEvent = triggerVisibilityChangeEvent("dxhiding");
const triggerResizeEvent = triggerVisibilityChangeEvent("dxresize");
const __TURBOPACK__default__export__ = {
    triggerHidingEvent: triggerHidingEvent,
    triggerResizeEvent: triggerResizeEvent,
    triggerShownEvent: triggerShownEvent
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/gesture/m_emitter.gesture.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/call_once.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ready_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/ready_callbacks.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/style.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_style.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
const ready = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$ready_callbacks$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].add;
const { abs: abs } = Math;
const SLEEP = 0;
const INITED = 1;
const STARTED = 2;
let TOUCH_BOUNDARY = 10;
const IMMEDIATE_TOUCH_BOUNDARY = 0;
const IMMEDIATE_TIMEOUT = 180;
const supportPointerEvents = function() {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_style$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["styleProp"])("pointer-events");
};
const setGestureCover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$call_once$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(()=>{
    const isDesktop = "desktop" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real().deviceType;
    if (!supportPointerEvents() || !isDesktop) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
    }
    const $cover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("<div>").addClass("dx-gesture-cover").css("pointerEvents", "none");
    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].subscribeGlobal($cover, "dxmousewheel", (e)=>{
        e.preventDefault();
    });
    ready(()=>{
        $cover.appendTo("body");
    });
    return function(toggle, cursor) {
        $cover.css("pointerEvents", toggle ? "all" : "none");
        toggle && $cover.css("cursor", cursor);
    };
});
const gestureCover = function(toggle, cursor) {
    const gestureCoverStrategy = setGestureCover();
    gestureCoverStrategy(toggle, cursor);
};
const GestureEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    gesture: true,
    configure (data) {
        this.getElement().css("msTouchAction", data.immediate ? "pinch-zoom" : "");
        this.callBase(data);
    },
    allowInterruptionByMouseWheel () {
        return 2 !== this._stage;
    },
    getDirection () {
        return this.direction;
    },
    _cancel () {
        this.callBase.apply(this, arguments);
        this._toggleGestureCover(false);
        this._stage = 0;
    },
    start (e) {
        if (e._needSkipEvent || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["needSkipEvent"])(e)) {
            this._cancel(e);
            return;
        }
        this._startEvent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createEvent"])(e);
        this._startEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        this._stage = 1;
        this._init(e);
        this._setupImmediateTimer();
    },
    _setupImmediateTimer () {
        clearTimeout(this._immediateTimer);
        this._immediateAccepted = false;
        if (!this.immediate) {
            return;
        }
        if (0 === this.immediateTimeout) {
            this._immediateAccepted = true;
            return;
        }
        var _this_immediateTimeout;
        this._immediateTimer = setTimeout(()=>{
            this._immediateAccepted = true;
        }, (_this_immediateTimeout = this.immediateTimeout) !== null && _this_immediateTimeout !== void 0 ? _this_immediateTimeout : 180);
    },
    move (e) {
        if (1 === this._stage && this._directionConfirmed(e)) {
            this._stage = 2;
            this._resetActiveElement();
            this._toggleGestureCover(true);
            this._clearSelection(e);
            this._adjustStartEvent(e);
            this._start(this._startEvent);
            if (0 === this._stage) {
                return;
            }
            this._requestAccept(e);
            this._move(e);
            this._forgetAccept();
        } else if (2 === this._stage) {
            this._clearSelection(e);
            this._move(e);
        }
    },
    _directionConfirmed (e) {
        const touchBoundary = this._getTouchBoundary(e);
        const delta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._startEventData, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e));
        const deltaX = abs(delta.x);
        const deltaY = abs(delta.y);
        const horizontalMove = this._validateMove(touchBoundary, deltaX, deltaY);
        const verticalMove = this._validateMove(touchBoundary, deltaY, deltaX);
        const direction = this.getDirection(e);
        const bothAccepted = "both" === direction && (horizontalMove || verticalMove);
        const horizontalAccepted = "horizontal" === direction && horizontalMove;
        const verticalAccepted = "vertical" === direction && verticalMove;
        return bothAccepted || horizontalAccepted || verticalAccepted || this._immediateAccepted;
    },
    _validateMove (touchBoundary, mainAxis, crossAxis) {
        return mainAxis && mainAxis >= touchBoundary && (this.immediate ? mainAxis >= crossAxis : true);
    },
    _getTouchBoundary (e) {
        return this.immediate || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e) ? 0 : TOUCH_BOUNDARY;
    },
    _adjustStartEvent (e) {
        const touchBoundary = this._getTouchBoundary(e);
        const delta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._startEventData, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e));
        this._startEvent.pageX += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sign"])(delta.x) * touchBoundary;
        this._startEvent.pageY += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sign"])(delta.y) * touchBoundary;
    },
    _resetActiveElement () {
        if ("ios" === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real().platform && this.getElement().find(":focus").length) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].resetActiveElement();
        }
    },
    _toggleGestureCover (toggle) {
        this._toggleGestureCoverImpl(toggle);
    },
    _toggleGestureCoverImpl (toggle) {
        const isStarted = 2 === this._stage;
        if (isStarted) {
            gestureCover(toggle, this.getElement().css("cursor"));
        }
    },
    _clearSelection (e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isTouchEvent"])(e)) {
            return;
        }
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].clearSelection();
    },
    end (e) {
        this._toggleGestureCover(false);
        if (2 === this._stage) {
            this._end(e);
        } else if (1 === this._stage) {
            this._stop(e);
        }
        this._stage = 0;
    },
    dispose () {
        clearTimeout(this._immediateTimer);
        this.callBase.apply(this, arguments);
        this._toggleGestureCover(false);
    },
    _init: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _start: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _move: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _stop: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _end: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"]
});
GestureEmitter.initialTouchBoundary = TOUCH_BOUNDARY;
GestureEmitter.touchBoundary = function(newBoundary) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(newBoundary)) {
        TOUCH_BOUNDARY = newBoundary;
        return;
    }
    return TOUCH_BOUNDARY;
};
const __TURBOPACK__default__export__ = GestureEmitter;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_drag.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_drag.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "drop": ()=>DROP_EVENT,
    "end": ()=>DRAG_END_EVENT,
    "enter": ()=>DRAG_ENTER_EVENT,
    "leave": ()=>DRAG_LEAVE_EVENT,
    "move": ()=>DRAG_EVENT,
    "start": ()=>DRAG_START_EVENT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$gesture$2f$emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/element_data.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_element_data.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/array.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_array.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/dom.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_dom.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
const DRAG_START_EVENT = "dxdragstart";
const DRAG_EVENT = "dxdrag";
const DRAG_END_EVENT = "dxdragend";
const DRAG_ENTER_EVENT = "dxdragenter";
const DRAG_LEAVE_EVENT = "dxdragleave";
const DROP_EVENT = "dxdrop";
const DX_DRAG_EVENTS_COUNT_KEY = "dxDragEventsCount";
const knownDropTargets = [];
const knownDropTargetSelectors = [];
const knownDropTargetConfigs = [];
const dropTargetRegistration = {
    setup (element, data) {
        const knownDropTarget = knownDropTargets.includes(element);
        if (!knownDropTarget) {
            knownDropTargets.push(element);
            knownDropTargetSelectors.push([]);
            knownDropTargetConfigs.push(data || {});
        }
    },
    add (element, handleObj) {
        const index = knownDropTargets.indexOf(element);
        this.updateEventsCounter(element, handleObj.type, 1);
        const { selector: selector } = handleObj;
        if (!knownDropTargetSelectors[index].includes(selector)) {
            knownDropTargetSelectors[index].push(selector);
        }
    },
    updateEventsCounter (element, event, value) {
        if ([
            DRAG_ENTER_EVENT,
            DRAG_LEAVE_EVENT,
            DROP_EVENT
        ].includes(event)) {
            const eventsCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxDragEventsCount") || 0;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxDragEventsCount", Math.max(0, eventsCount + value));
        }
    },
    remove (element, handleObj) {
        this.updateEventsCounter(element, handleObj.type, -1);
    },
    teardown (element) {
        const handlersCount = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["data"])(element, "dxDragEventsCount");
        if (!handlersCount) {
            const index = knownDropTargets.indexOf(element);
            knownDropTargets.splice(index, 1);
            knownDropTargetSelectors.splice(index, 1);
            knownDropTargetConfigs.splice(index, 1);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_element_data$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["removeData"])(element, "dxDragEventsCount");
        }
    }
};
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(DRAG_ENTER_EVENT, dropTargetRegistration);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(DRAG_LEAVE_EVENT, dropTargetRegistration);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(DROP_EVENT, dropTargetRegistration);
const getItemDelegatedTargets = function($element) {
    const dropTargetIndex = knownDropTargets.indexOf($element.get(0));
    const dropTargetSelectors = knownDropTargetSelectors[dropTargetIndex].filter((selector)=>selector);
    let $delegatedTargets = $element.find(dropTargetSelectors.join(", "));
    if (knownDropTargetSelectors[dropTargetIndex].includes(void 0)) {
        $delegatedTargets = $delegatedTargets.add($element);
    }
    return $delegatedTargets;
};
const getItemConfig = function($element) {
    const dropTargetIndex = knownDropTargets.indexOf($element.get(0));
    return knownDropTargetConfigs[dropTargetIndex];
};
const getItemPosition = function(dropTargetConfig, $element) {
    if (dropTargetConfig.itemPositionFunc) {
        return dropTargetConfig.itemPositionFunc($element);
    }
    return $element.offset();
};
const getItemSize = function(dropTargetConfig, $element) {
    if (dropTargetConfig.itemSizeFunc) {
        return dropTargetConfig.itemSizeFunc($element);
    }
    return {
        width: $element.get(0).getBoundingClientRect().width,
        height: $element.get(0).getBoundingClientRect().height
    };
};
const DragEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    ctor (element) {
        this.callBase(element);
        this.direction = "both";
    },
    _init (e) {
        this._initEvent = e;
    },
    _start (e) {
        e = this._fireEvent("dxdragstart", this._initEvent);
        this._maxLeftOffset = e.maxLeftOffset;
        this._maxRightOffset = e.maxRightOffset;
        this._maxTopOffset = e.maxTopOffset;
        this._maxBottomOffset = e.maxBottomOffset;
        if (e.targetElements || null === e.targetElements) {
            const dropTargets = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_array$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["wrapToArray"])(e.targetElements || []);
            this._dropTargets = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"](dropTargets, (element)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element).get(0));
        } else {
            this._dropTargets = knownDropTargets;
        }
    },
    _move (e) {
        const eventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        const dragOffset = this._calculateOffset(eventData);
        e = this._fireEvent("dxdrag", e, {
            offset: dragOffset
        });
        this._processDropTargets(e);
        if (!e._cancelPreventDefault) {
            e.preventDefault();
        }
    },
    _calculateOffset (eventData) {
        return {
            x: this._calculateXOffset(eventData),
            y: this._calculateYOffset(eventData)
        };
    },
    _calculateXOffset (eventData) {
        if ("vertical" !== this.direction) {
            const offset = eventData.x - this._startEventData.x;
            return this._fitOffset(offset, this._maxLeftOffset, this._maxRightOffset);
        }
        return 0;
    },
    _calculateYOffset (eventData) {
        if ("horizontal" !== this.direction) {
            const offset = eventData.y - this._startEventData.y;
            return this._fitOffset(offset, this._maxTopOffset, this._maxBottomOffset);
        }
        return 0;
    },
    _fitOffset (offset, minOffset, maxOffset) {
        if (null != minOffset) {
            offset = Math.max(offset, -minOffset);
        }
        if (null != maxOffset) {
            offset = Math.min(offset, maxOffset);
        }
        return offset;
    },
    _processDropTargets (e) {
        const target = this._findDropTarget(e);
        const sameTarget = target === this._currentDropTarget;
        if (!sameTarget) {
            this._fireDropTargetEvent(e, DRAG_LEAVE_EVENT);
            this._currentDropTarget = target;
            this._fireDropTargetEvent(e, DRAG_ENTER_EVENT);
        }
    },
    _fireDropTargetEvent (event, eventName) {
        if (!this._currentDropTarget) {
            return;
        }
        const eventData = {
            type: eventName,
            originalEvent: event,
            draggingElement: this._$element.get(0),
            target: this._currentDropTarget
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])(eventData);
    },
    _findDropTarget (e) {
        const that = this;
        let result;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"](knownDropTargets, (_, target)=>{
            if (!that._checkDropTargetActive(target)) {
                return;
            }
            const $target = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(target);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"](getItemDelegatedTargets($target), (_, delegatedTarget)=>{
                const $delegatedTarget = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(delegatedTarget);
                if (that._checkDropTarget(getItemConfig($target), $delegatedTarget, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(result), e)) {
                    result = delegatedTarget;
                }
            });
        });
        return result;
    },
    _checkDropTargetActive (target) {
        let active = false;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"](this._dropTargets, (_, activeTarget)=>{
            active = active || activeTarget === target || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_dom$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["contains"])(activeTarget, target);
            return !active;
        });
        return active;
    },
    _checkDropTarget (config, $target, $prevTarget, e) {
        const isDraggingElement = $target.get(0) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(e.target).get(0);
        if (isDraggingElement) {
            return false;
        }
        const targetPosition = getItemPosition(config, $target);
        if (e.pageX < targetPosition.left) {
            return false;
        }
        if (e.pageY < targetPosition.top) {
            return false;
        }
        const targetSize = getItemSize(config, $target);
        if (e.pageX > targetPosition.left + targetSize.width) {
            return false;
        }
        if (e.pageY > targetPosition.top + targetSize.height) {
            return false;
        }
        if ($prevTarget.length && $prevTarget.closest($target).length) {
            return false;
        }
        if (config.checkDropTarget && !config.checkDropTarget($target, e)) {
            return false;
        }
        return $target;
    },
    _end (e) {
        const eventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        this._fireEvent("dxdragend", e, {
            offset: this._calculateOffset(eventData)
        });
        this._fireDropTargetEvent(e, DROP_EVENT);
        delete this._currentDropTarget;
    }
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: DragEmitter,
    events: [
        "dxdragstart",
        "dxdrag",
        "dxdragend"
    ]
});
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.scroll.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/gesture/m_emitter.gesture.scroll.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$gesture$2f$emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const { abstract: abstract } = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
const realDevice = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].real();
const SCROLL_EVENT = "scroll";
const SCROLL_INIT_EVENT = "dxscrollinit";
const SCROLL_START_EVENT = "dxscrollstart";
const SCROLL_MOVE_EVENT = "dxscroll";
const SCROLL_END_EVENT = "dxscrollend";
const SCROLL_STOP_EVENT = "dxscrollstop";
const SCROLL_CANCEL_EVENT = "dxscrollcancel";
const Locker = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit(function() {
    const NAMESPACED_SCROLL_EVENT = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("scroll", "dxScrollEmitter");
    return {
        ctor (element) {
            this._element = element;
            this._locked = false;
            this._proxiedScroll = (e)=>{
                if (!this._disposed) {
                    this._scroll(e);
                }
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this._element, NAMESPACED_SCROLL_EVENT, this._proxiedScroll);
        },
        _scroll: abstract,
        check (e, callback) {
            if (this._locked) {
                callback();
            }
        },
        dispose () {
            this._disposed = true;
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this._element, NAMESPACED_SCROLL_EVENT, this._proxiedScroll);
        }
    };
}());
const TimeoutLocker = Locker.inherit({
    ctor (element, timeout) {
        this.callBase(element);
        this._timeout = timeout;
    },
    _scroll () {
        this._prepare();
        this._forget();
    },
    _prepare () {
        if (this._timer) {
            this._clearTimer();
        }
        this._locked = true;
    },
    _clearTimer () {
        clearTimeout(this._timer);
        this._locked = false;
        this._timer = null;
    },
    _forget () {
        const that = this;
        this._timer = setTimeout(()=>{
            that._clearTimer();
        }, this._timeout);
    },
    dispose () {
        this.callBase();
        this._clearTimer();
    }
});
const WheelLocker = TimeoutLocker.inherit({
    ctor (element) {
        this.callBase(element, 400);
        this._lastWheelDirection = null;
    },
    check (e, callback) {
        this._checkDirectionChanged(e);
        this.callBase(e, callback);
    },
    _checkDirectionChanged (e) {
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e)) {
            this._lastWheelDirection = null;
            return;
        }
        const direction = e.shiftKey || false;
        const directionChange = null !== this._lastWheelDirection && direction !== this._lastWheelDirection;
        this._lastWheelDirection = direction;
        this._locked = this._locked && !directionChange;
    }
});
let PointerLocker = TimeoutLocker.inherit({
    ctor (element) {
        this.callBase(element, 400);
    }
});
!function() {
    const { ios: isIos, android: isAndroid } = realDevice;
    if (!(isIos || isAndroid)) {
        return;
    }
    PointerLocker = Locker.inherit({
        _scroll () {
            this._locked = true;
            const that = this;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(this._scrollFrame);
            this._scrollFrame = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["requestAnimationFrame"])(()=>{
                that._locked = false;
            });
        },
        check (e, callback) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(this._scrollFrame);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(this._checkFrame);
            const that = this;
            const { callBase: callBase } = this;
            this._checkFrame = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["requestAnimationFrame"])(()=>{
                callBase.call(that, e, callback);
                that._locked = false;
            });
        },
        dispose () {
            this.callBase();
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(this._scrollFrame);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cancelAnimationFrame"])(this._checkFrame);
        }
    });
}();
const ScrollEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit(function() {
    const FRAME_DURATION = Math.round(1e3 / 60);
    return {
        ctor (element) {
            this.callBase.apply(this, arguments);
            this.direction = "both";
            this._pointerLocker = new PointerLocker(element);
            this._wheelLocker = new WheelLocker(element);
        },
        validate: ()=>true,
        configure (data) {
            if (data.scrollTarget) {
                this._pointerLocker.dispose();
                this._wheelLocker.dispose();
                this._pointerLocker = new PointerLocker(data.scrollTarget);
                this._wheelLocker = new WheelLocker(data.scrollTarget);
            }
            this.callBase(data);
        },
        _init (e) {
            this._wheelLocker.check(e, ()=>{
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e)) {
                    this._accept(e);
                }
            });
            this._pointerLocker.check(e, ()=>{
                const skipCheck = this.isNative && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMouseEvent"])(e);
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e) && !skipCheck) {
                    this._accept(e);
                }
            });
            this._fireEvent("dxscrollinit", e);
            this._prevEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        },
        move (e) {
            this.callBase.apply(this, arguments);
            e.isScrollingEvent = this.isNative || e.isScrollingEvent;
        },
        _start (e) {
            this._savedEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
            this._fireEvent("dxscrollstart", e);
            this._prevEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        },
        _move (e) {
            const currentEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
            this._fireEvent("dxscroll", e, {
                delta: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._prevEventData, currentEventData)
            });
            const delta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._savedEventData, currentEventData);
            if (delta.time > 200) {
                this._savedEventData = this._prevEventData;
            }
            this._prevEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        },
        _end (e) {
            const endEventDelta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._prevEventData, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e));
            let velocity = {
                x: 0,
                y: 0
            };
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDxMouseWheelEvent"])(e) && endEventDelta.time < 100) {
                const delta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._savedEventData, this._prevEventData);
                const velocityMultiplier = FRAME_DURATION / delta.time;
                velocity = {
                    x: delta.x * velocityMultiplier,
                    y: delta.y * velocityMultiplier
                };
            }
            this._fireEvent("dxscrollend", e, {
                velocity: velocity
            });
        },
        _stop (e) {
            this._fireEvent("dxscrollstop", e);
        },
        cancel (e) {
            this.callBase.apply(this, arguments);
            this._fireEvent("dxscrollcancel", e);
        },
        dispose () {
            this.callBase.apply(this, arguments);
            this._pointerLocker.dispose();
            this._wheelLocker.dispose();
        },
        _clearSelection () {
            if (this.isNative) {
                return;
            }
            return this.callBase.apply(this, arguments);
        },
        _toggleGestureCover () {
            if (this.isNative) {
                return;
            }
            return this.callBase.apply(this, arguments);
        }
    };
}());
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: ScrollEmitter,
    events: [
        "dxscrollinit",
        "dxscrollstart",
        "dxscroll",
        "dxscrollend",
        "dxscrollstop",
        "dxscrollcancel"
    ]
});
const __TURBOPACK__default__export__ = {
    init: "dxscrollinit",
    start: "dxscrollstart",
    move: "dxscroll",
    end: "dxscrollend",
    stop: "dxscrollstop",
    cancel: "dxscrollcancel",
    scroll: "scroll"
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_hold.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_hold.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
;
;
;
const { abs: abs } = Math;
const HOLD_EVENT_NAME = "dxhold";
const HOLD_TIMEOUT = 750;
const TOUCH_BOUNDARY = 5;
const HoldEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    start (e) {
        this._startEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        this._startTimer(e);
    },
    _startTimer (e) {
        const holdTimeout = "timeout" in this ? this.timeout : 750;
        this._holdTimer = setTimeout(()=>{
            this._requestAccept(e);
            this._fireEvent("dxhold", e, {
                target: e.target
            });
            this._forgetAccept();
        }, holdTimeout);
    },
    move (e) {
        if (this._touchWasMoved(e)) {
            this._cancel(e);
        }
    },
    _touchWasMoved (e) {
        const delta = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventDelta"])(this._startEventData, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e));
        return abs(delta.x) > 5 || abs(delta.y) > 5;
    },
    end () {
        this._stopTimer();
    },
    _stopTimer () {
        clearTimeout(this._holdTimer);
    },
    cancel () {
        this._stopTimer();
    },
    dispose () {
        this._stopTimer();
    }
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: HoldEmitter,
    bubble: true,
    events: [
        "dxhold"
    ]
});
const __TURBOPACK__default__export__ = {
    name: "dxhold"
};
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_contextmenu.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_contextmenu.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "name": ()=>name
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/event_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_event_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$hold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/hold.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_hold.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/class.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/renderer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/m_devices.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_support.js [app-client] (ecmascript) <locals>");
;
;
;
;
;
;
;
;
const CONTEXTMENU_NAMESPACE = "dxContexMenu";
const CONTEXTMENU_NAMESPACED_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])("contextmenu", "dxContexMenu");
const HOLD_NAMESPACED_EVENT_NAME = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_hold$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].name, "dxContexMenu");
const CONTEXTMENU_EVENT_NAME = "dxcontextmenu";
const ContextMenu = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$class$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    setup (element) {
        const $element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$renderer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])(element);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, CONTEXTMENU_NAMESPACED_EVENT_NAME, this._contextMenuHandler.bind(this));
        if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_support$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"].touch || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isSimulator()) {
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on($element, HOLD_NAMESPACED_EVENT_NAME, this._holdHandler.bind(this));
        }
    },
    _holdHandler (e) {
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isMouseEvent"])(e) && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$m_devices$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].isSimulator()) {
            return;
        }
        this._fireContextMenu(e);
    },
    _contextMenuHandler (e) {
        this._fireContextMenu(e);
    },
    _fireContextMenu: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fireEvent"])({
            type: "dxcontextmenu",
            originalEvent: e
        }),
    teardown (element) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(element, ".dxContexMenu");
    }
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_event_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])("dxcontextmenu", new ContextMenu);
const name = "dxcontextmenu";
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_swipe.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_swipe.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "end": ()=>SWIPE_END_EVENT,
    "start": ()=>SWIPE_START_EVENT,
    "swipe": ()=>SWIPE_EVENT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$gesture$2f$emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/gesture/emitter.gesture.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/gesture/m_emitter.gesture.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/size.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_size.js [app-client] (ecmascript)");
;
;
;
;
const SWIPE_START_EVENT = "dxswipestart";
const SWIPE_EVENT = "dxswipe";
const SWIPE_END_EVENT = "dxswipeend";
const HorizontalStrategy = {
    defaultItemSizeFunc () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWidth"])(this.getElement());
    },
    getBounds () {
        return [
            this._maxLeftOffset,
            this._maxRightOffset
        ];
    },
    calcOffsetRatio (e) {
        const endEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        return (endEventData.x - (this._savedEventData && this._savedEventData.x || 0)) / this._itemSizeFunc().call(this, e);
    },
    isFastSwipe (e) {
        const endEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        return this.FAST_SWIPE_SPEED_LIMIT * Math.abs(endEventData.x - this._tickData.x) >= endEventData.time - this._tickData.time;
    }
};
const VerticalStrategy = {
    defaultItemSizeFunc () {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_size$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getHeight"])(this.getElement());
    },
    getBounds () {
        return [
            this._maxTopOffset,
            this._maxBottomOffset
        ];
    },
    calcOffsetRatio (e) {
        const endEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        return (endEventData.y - (this._savedEventData && this._savedEventData.y || 0)) / this._itemSizeFunc().call(this, e);
    },
    isFastSwipe (e) {
        const endEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        return this.FAST_SWIPE_SPEED_LIMIT * Math.abs(endEventData.y - this._tickData.y) >= endEventData.time - this._tickData.time;
    }
};
const STRATEGIES = {
    horizontal: HorizontalStrategy,
    vertical: VerticalStrategy
};
const SwipeEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$gesture$2f$m_emitter$2e$gesture$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    TICK_INTERVAL: 300,
    FAST_SWIPE_SPEED_LIMIT: 10,
    ctor (element) {
        this.callBase(element);
        this.direction = "horizontal";
        this.elastic = true;
    },
    _getStrategy () {
        return STRATEGIES[this.direction];
    },
    _defaultItemSizeFunc () {
        return this._getStrategy().defaultItemSizeFunc.call(this);
    },
    _itemSizeFunc () {
        return this.itemSizeFunc || this._defaultItemSizeFunc;
    },
    _init (e) {
        this._tickData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
    },
    _start (e) {
        this._savedEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        e = this._fireEvent("dxswipestart", e);
        if (!e.cancel) {
            this._maxLeftOffset = e.maxLeftOffset;
            this._maxRightOffset = e.maxRightOffset;
            this._maxTopOffset = e.maxTopOffset;
            this._maxBottomOffset = e.maxBottomOffset;
        }
    },
    _move (e) {
        const strategy = this._getStrategy();
        const moveEventData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["eventData"])(e);
        let offset = strategy.calcOffsetRatio.call(this, e);
        offset = this._fitOffset(offset, this.elastic);
        if (moveEventData.time - this._tickData.time > this.TICK_INTERVAL) {
            this._tickData = moveEventData;
        }
        this._fireEvent("dxswipe", e, {
            offset: offset
        });
        if (false !== e.cancelable) {
            e.preventDefault();
        }
    },
    _end (e) {
        const strategy = this._getStrategy();
        const offsetRatio = strategy.calcOffsetRatio.call(this, e);
        const isFast = strategy.isFastSwipe.call(this, e);
        let startOffset = offsetRatio;
        let targetOffset = this._calcTargetOffset(offsetRatio, isFast);
        startOffset = this._fitOffset(startOffset, this.elastic);
        targetOffset = this._fitOffset(targetOffset, false);
        this._fireEvent("dxswipeend", e, {
            offset: startOffset,
            targetOffset: targetOffset
        });
    },
    _fitOffset (offset, elastic) {
        const strategy = this._getStrategy();
        const bounds = strategy.getBounds.call(this);
        if (offset < -bounds[0]) {
            return elastic ? (-2 * bounds[0] + offset) / 3 : -bounds[0];
        }
        if (offset > bounds[1]) {
            return elastic ? (2 * bounds[1] + offset) / 3 : bounds[1];
        }
        return offset;
    },
    _calcTargetOffset (offsetRatio, isFast) {
        let result;
        if (isFast) {
            result = Math.ceil(Math.abs(offsetRatio));
            if (offsetRatio < 0) {
                result = -result;
            }
        } else {
            result = Math.round(offsetRatio);
        }
        return result;
    }
});
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: SwipeEmitter,
    events: [
        "dxswipestart",
        "dxswipe",
        "dxswipeend"
    ]
});
;
}),
"[project]/node_modules/devextreme/esm/__internal/events/gesture/m_swipeable.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/gesture/m_swipeable.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@babel/runtime/helpers/esm/extends.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/events_engine.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_events_engine.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/swipe.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/m_swipe.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$public_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/public_component.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_public_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_public_component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/widget/dom_component.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const DX_SWIPEABLE = "dxSwipeable";
const SWIPEABLE_CLASS = "dx-swipeable";
const ACTION_TO_EVENT_MAP = {
    onStart: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["start"],
    onUpdated: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["swipe"],
    onEnd: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$m_swipe$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["end"],
    onCancel: "dxswipecancel"
};
const IMMEDIATE_TIMEOUT = 180;
class Swipeable extends __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$widget$2f$dom_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"] {
    _getDefaultOptions() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$babel$2f$runtime$2f$helpers$2f$esm$2f$extends$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({}, super._getDefaultOptions(), {
            elastic: true,
            immediate: false,
            immediateTimeout: 180,
            direction: "horizontal",
            itemSizeFunc: null,
            onStart: null,
            onUpdated: null,
            onEnd: null,
            onCancel: null
        });
    }
    _render() {
        super._render();
        this.$element().addClass("dx-swipeable");
        this._attachEventHandlers();
    }
    _attachEventHandlers() {
        this._detachEventHandlers();
        if (this.option("disabled")) {
            return;
        }
        const { NAME: NAME } = this;
        this._createEventData();
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(ACTION_TO_EVENT_MAP, (actionName, eventName)=>{
            const action = this._createActionByOption(actionName, {
                context: this
            });
            eventName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addNamespace"])(eventName, NAME);
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].on(this.$element(), eventName, this._eventData, (e)=>action({
                    event: e
                }));
        });
    }
    _createEventData() {
        this._eventData = {
            elastic: this.option("elastic"),
            itemSizeFunc: this.option("itemSizeFunc"),
            direction: this.option("direction"),
            immediate: this.option("immediate"),
            immediateTimeout: this.option("immediateTimeout")
        };
    }
    _detachEventHandlers() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_events_engine$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].off(this.$element(), ".".concat(DX_SWIPEABLE));
    }
    _optionChanged(args) {
        switch(args.name){
            case "disabled":
            case "onStart":
            case "onUpdated":
            case "onEnd":
            case "onCancel":
            case "elastic":
            case "immediate":
            case "itemSizeFunc":
            case "direction":
                this._detachEventHandlers();
                this._attachEventHandlers();
                break;
            case "rtlEnabled":
                break;
            default:
                super._optionChanged(args);
        }
    }
    _useTemplates() {
        return false;
    }
}
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_public_component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["name"])(Swipeable, DX_SWIPEABLE);
const __TURBOPACK__default__export__ = Swipeable;
}),
"[project]/node_modules/devextreme/esm/__internal/events/m_transform.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/__internal/events/m_transform.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "exportNames": ()=>exportNames
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$core$2f$emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/core/emitter_registrator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/core/m_emitter_registrator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events/utils/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/events/utils/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
;
;
;
;
;
const DX_PREFIX = "dx";
const TRANSFORM = "transform";
const TRANSLATE = "translate";
const PINCH = "pinch";
const ROTATE = "rotate";
const START_POSTFIX = "start";
const UPDATE_POSTFIX = "";
const END_POSTFIX = "end";
const eventAliases = [];
const addAlias = function(eventName, eventArgs) {
    eventAliases.push({
        name: eventName,
        args: eventArgs
    });
};
addAlias(TRANSFORM, {
    scale: true,
    deltaScale: true,
    rotation: true,
    deltaRotation: true,
    translation: true,
    deltaTranslation: true
});
addAlias(TRANSLATE, {
    translation: true,
    deltaTranslation: true
});
addAlias(PINCH, {
    scale: true,
    deltaScale: true
});
addAlias(ROTATE, {
    rotation: true,
    deltaRotation: true
});
const getVector = function(first, second) {
    return {
        x: second.pageX - first.pageX,
        y: -second.pageY + first.pageY,
        centerX: .5 * (second.pageX + first.pageX),
        centerY: .5 * (second.pageY + first.pageY)
    };
};
const getEventVector = function(e) {
    const { pointers: pointers } = e;
    return getVector(pointers[0], pointers[1]);
};
const getDistance = function(vector) {
    return Math.sqrt(vector.x * vector.x + vector.y * vector.y);
};
const getScale = function(firstVector, secondVector) {
    return getDistance(firstVector) / getDistance(secondVector);
};
const getRotation = function(firstVector, secondVector) {
    const scalarProduct = firstVector.x * secondVector.x + firstVector.y * secondVector.y;
    const distanceProduct = getDistance(firstVector) * getDistance(secondVector);
    if (0 === distanceProduct) {
        return 0;
    }
    const sign = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["sign"])(firstVector.x * secondVector.y - secondVector.x * firstVector.y);
    const angle = Math.acos((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["fitIntoRange"])(scalarProduct / distanceProduct, -1, 1));
    return sign * angle;
};
const getTranslation = function(firstVector, secondVector) {
    return {
        x: firstVector.centerX - secondVector.centerX,
        y: firstVector.centerY - secondVector.centerY
    };
};
const TransformEmitter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].inherit({
    validatePointers: (e)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$utils$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasTouches"])(e) > 1,
    start (e) {
        this._accept(e);
        const startVector = getEventVector(e);
        this._startVector = startVector;
        this._prevVector = startVector;
        this._fireEventAliases(START_POSTFIX, e);
    },
    move (e) {
        const currentVector = getEventVector(e);
        const eventArgs = this._getEventArgs(currentVector);
        this._fireEventAliases(UPDATE_POSTFIX, e, eventArgs);
        this._prevVector = currentVector;
    },
    end (e) {
        const eventArgs = this._getEventArgs(this._prevVector);
        this._fireEventAliases(END_POSTFIX, e, eventArgs);
    },
    _getEventArgs (vector) {
        return {
            scale: getScale(vector, this._startVector),
            deltaScale: getScale(vector, this._prevVector),
            rotation: getRotation(vector, this._startVector),
            deltaRotation: getRotation(vector, this._prevVector),
            translation: getTranslation(vector, this._startVector),
            deltaTranslation: getTranslation(vector, this._prevVector)
        };
    },
    _fireEventAliases (eventPostfix, originalEvent, eventArgs) {
        eventArgs = eventArgs || {};
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"](eventAliases, (_, eventAlias)=>{
            const args = {};
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"](eventAlias.args, (name)=>{
                if (name in eventArgs) {
                    args[name] = eventArgs[name];
                }
            });
            this._fireEvent("dx" + eventAlias.name + eventPostfix, originalEvent, args);
        });
    }
});
const eventNames = eventAliases.reduce((result, eventAlias)=>{
    [
        START_POSTFIX,
        UPDATE_POSTFIX,
        END_POSTFIX
    ].forEach((eventPostfix)=>{
        result.push("dx" + eventAlias.name + eventPostfix);
    });
    return result;
}, []);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$events$2f$core$2f$m_emitter_registrator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    emitter: TransformEmitter,
    events: eventNames
});
const exportNames = {};
__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"](eventNames, (_, eventName)=>{
    exportNames[eventName.substring(2)] = eventName;
});
;
}),
}]);

//# sourceMappingURL=node_modules_devextreme_esm___internal_events_c51f37ef._.js.map