"use client";

import { useState } from "react";
import Form, {
  SimpleItem,
  GroupItem,
  Label,
  RequiredRule,
  EmailRule,
} from "devextreme-react/form";
import Button from "devextreme-react/button";
import { useAuth } from "@/hooks/useAuth";
import PermissionGuard from "@/components/PermissionGuard";
import { hasPermission } from "@/lib/permissions";

interface Settings {
  appName: string;
  appVersion: string;
  supportEmail: string;
  notificationsEnabled: boolean;
  theme: "light" | "dark";
  language: "hu" | "en";
}

export default function SettingsPage() {
  const { user } = useAuth();
  const [settings, setSettings] = useState<Settings>({
    appName: "OMSNext",
    appVersion: "1.0.0",
    supportEmail: "<EMAIL>",
    notificationsEnabled: true,
    theme: "light",
    language: "hu",
  });
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setSuccessMessage("");

    try {
      // In a real application, this would be an API call
      // await api.updateSettings(settings);

      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1000));

      setSuccessMessage("Beállítások sikeresen mentve!");
    } catch (error) {
      console.error("Failed to save settings:", error);
      setSuccessMessage("Hiba történt a beállítások mentése során.");
    } finally {
      setLoading(false);
    }
  };

  const handleFieldChange = (e: any) => {
    const field = e.dataField as string;
    const value = e.value;
    setSettings({ ...settings, [field]: value });
  };

  return (
    <PermissionGuard permission="edit_settings">
      <div className="content-block">
        <h2 className="content-block-title">Alkalmazás beállítások</h2>

        <form onSubmit={handleSubmit}>
          <Form
            formData={settings}
            onFieldDataChanged={handleFieldChange}
            labelLocation="top"
            colCount={2}
          >
            <GroupItem caption="Alkalmazás beállítások">
              <SimpleItem dataField="appName" editorType="dxTextBox">
                <Label text="Alkalmazás neve" />
                <RequiredRule message="Az alkalmazás neve kötelező" />
              </SimpleItem>

              <SimpleItem dataField="appVersion" editorType="dxTextBox">
                <Label text="Verzió" />
              </SimpleItem>

              <SimpleItem dataField="supportEmail" editorType="dxTextBox">
                <Label text="Támogatás email" />
                <EmailRule message="Érvényes email cím megadása kötelező" />
              </SimpleItem>
            </GroupItem>

            <GroupItem caption="Felhasználói beállítások">
              <SimpleItem
                dataField="notificationsEnabled"
                editorType="dxCheckBox"
                editorOptions={{ text: "Értesítések engedélyezése" }}
              >
                <Label text="Értesítések" />
              </SimpleItem>

              <SimpleItem
                dataField="theme"
                editorType="dxSelectBox"
                editorOptions={{
                  items: [
                    { value: "light", text: "Világos" },
                    { value: "dark", text: "Sötét" },
                  ],
                  displayExpr: "text",
                  valueExpr: "value",
                }}
              >
                <Label text="Téma" />
              </SimpleItem>

              <SimpleItem
                dataField="language"
                editorType="dxSelectBox"
                editorOptions={{
                  items: [
                    { value: "hu", text: "Magyar" },
                    { value: "en", text: "Angol" },
                  ],
                  displayExpr: "text",
                  valueExpr: "value",
                }}
              >
                <Label text="Nyelv" />
              </SimpleItem>
            </GroupItem>
          </Form>

          <div
            style={{
              marginTop: "20px",
              display: "flex",
              justifyContent: "flex-end",
            }}
          >
            {successMessage && (
              <div
                style={{
                  marginRight: "20px",
                  padding: "10px",
                  backgroundColor: successMessage.includes("sikeresen")
                    ? "#d4edda"
                    : "#f8d7da",
                  color: successMessage.includes("sikeresen")
                    ? "#155724"
                    : "#721c24",
                  borderRadius: "4px",
                }}
              >
                {successMessage}
              </div>
            )}
            <Button
              text="Mentés"
              type="success"
              useSubmitBehavior={true}
              disabled={loading}
            />
          </div>
        </form>
      </div>
    </PermissionGuard>
  );
}
