<Window x:Class="omsnext.wpf.DashboardWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="OmsNext - Dashboard" Height="600" Width="900"
        WindowStartupLocation="CenterScreen">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="60"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        
        <!-- Header -->
        <Border Grid.Row="0" Background="#2c3e50" Padding="20,0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>
                
                <StackPanel Grid.Column="0" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock Text="OmsNext Dashboard" 
                               FontSize="20" 
                               FontWeight="Bold" 
                               Foreground="White"/>
                </StackPanel>
                
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center">
                    <TextBlock x:Name="UserNameLabel" 
                               Foreground="White" 
                               FontSize="14" 
                               Margin="0,0,15,0"
                               VerticalAlignment="Center"/>
                    <Button x:Name="LogoutButton" 
                            Content="Kijelentkezés" 
                            Background="Transparent" 
                            Foreground="White" 
                            BorderBrush="White" 
                            BorderThickness="1"
                            Padding="15,5"
                            Click="LogoutButton_Click"/>
                </StackPanel>
            </Grid>
        </Border>
        
        <!-- Main Content -->
        <Grid Grid.Row="1" Margin="20">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="*"/>
            </Grid.RowDefinitions>
            
            <!-- Welcome Section -->
            <StackPanel Grid.Row="0" Margin="0,0,0,20">
                <TextBlock x:Name="WelcomeLabel" 
                           FontSize="24" 
                           FontWeight="SemiBold" 
                           Margin="0,0,0,10"/>
                <TextBlock Text="Válasszon a következő lehetőségek közül:" 
                           FontSize="14" 
                           Foreground="Gray"/>
            </StackPanel>
            
            <!-- Dashboard Cards -->
            <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                <WrapPanel Orientation="Horizontal">
                    
                    <!-- Users Card -->
                    <Border Background="White" 
                            CornerRadius="10" 
                            Margin="0,0,20,20" 
                            Width="250" 
                            Height="150"
                            Effect="{x:Static SystemParameters.DropShadowKey}">
                        <StackPanel Margin="20" VerticalAlignment="Center">
                            <TextBlock Text="👥" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="Felhasználók" 
                                       FontSize="18" 
                                       FontWeight="SemiBold" 
                                       HorizontalAlignment="Center" 
                                       Margin="0,0,0,5"/>
                            <TextBlock Text="Felhasználók kezelése" 
                                       FontSize="12" 
                                       Foreground="Gray" 
                                       HorizontalAlignment="Center"
                                       TextWrapping="Wrap"/>
                            <Button Content="Megnyitás" 
                                    Background="#007bff" 
                                    Foreground="White" 
                                    BorderThickness="0"
                                    Margin="0,10,0,0"
                                    Padding="10,5"
                                    Click="UsersButton_Click"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Settings Card -->
                    <Border Background="White" 
                            CornerRadius="10" 
                            Margin="0,0,20,20" 
                            Width="250" 
                            Height="150"
                            Effect="{x:Static SystemParameters.DropShadowKey}">
                        <StackPanel Margin="20" VerticalAlignment="Center">
                            <TextBlock Text="⚙️" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="Beállítások" 
                                       FontSize="18" 
                                       FontWeight="SemiBold" 
                                       HorizontalAlignment="Center" 
                                       Margin="0,0,0,5"/>
                            <TextBlock Text="Rendszer beállítások" 
                                       FontSize="12" 
                                       Foreground="Gray" 
                                       HorizontalAlignment="Center"
                                       TextWrapping="Wrap"/>
                            <Button Content="Megnyitás" 
                                    Background="#28a745" 
                                    Foreground="White" 
                                    BorderThickness="0"
                                    Margin="0,10,0,0"
                                    Padding="10,5"
                                    Click="SettingsButton_Click"/>
                        </StackPanel>
                    </Border>
                    
                    <!-- Reports Card -->
                    <Border Background="White" 
                            CornerRadius="10" 
                            Margin="0,0,20,20" 
                            Width="250" 
                            Height="150"
                            Effect="{x:Static SystemParameters.DropShadowKey}">
                        <StackPanel Margin="20" VerticalAlignment="Center">
                            <TextBlock Text="📊" FontSize="30" HorizontalAlignment="Center" Margin="0,0,0,10"/>
                            <TextBlock Text="Jelentések" 
                                       FontSize="18" 
                                       FontWeight="SemiBold" 
                                       HorizontalAlignment="Center" 
                                       Margin="0,0,0,5"/>
                            <TextBlock Text="Statisztikák és jelentések" 
                                       FontSize="12" 
                                       Foreground="Gray" 
                                       HorizontalAlignment="Center"
                                       TextWrapping="Wrap"/>
                            <Button Content="Megnyitás" 
                                    Background="#ffc107" 
                                    Foreground="Black" 
                                    BorderThickness="0"
                                    Margin="0,10,0,0"
                                    Padding="10,5"
                                    Click="ReportsButton_Click"/>
                        </StackPanel>
                    </Border>
                    
                </WrapPanel>
            </ScrollViewer>
        </Grid>
    </Grid>
</Window>