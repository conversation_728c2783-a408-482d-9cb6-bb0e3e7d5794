import axios from "axios";

export async function login(email: string, password: string) {
  try {
    // Ha CORS be van <PERSON>ll<PERSON>, hív<PERSON><PERSON> k<PERSON>lenül a backend API-t
    const response = await axios.post(
      "http://localhost:5292/api/auth/login", // vagy az <PERSON> API URL-ed
      { email, password }
    );
    // Token tárolása (cookie helyett localStorage egyszerűen)
    localStorage.setItem("oms_token", response.data.token);
    return response.data;
  } catch (error: any) {
    throw new Error(
      error.response?.data?.message ||
        error.response?.data?.error ||
        "Hiba történt a bejelentkezés során."
    );
  }
}

// Auth header helper
export function getAuthHeaders() {
  const token =
    typeof window !== "undefined" ? localStorage.getItem("oms_token") : null;
  return token ? { Authorization: `Bearer ${token}` } : {};
}
