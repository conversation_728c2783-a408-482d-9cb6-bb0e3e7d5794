(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme/esm/viz/series/helpers/range_data_calculator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/helpers/range_data_calculator.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
;
;
const DISCRETE = "discrete";
const { abs: abs, floor: floor, ceil: ceil, min: min } = Math;
function continuousRangeCalculator(range, minValue, maxValue) {
    range.min = range.min < minValue ? range.min : minValue;
    range.max = range.max > maxValue ? range.max : maxValue;
}
function createGetLogFunction(axisType, axis) {
    if ("logarithmic" !== axisType) {
        return null;
    }
    const base = axis.getOptions().logarithmBase;
    return (value)=>{
        const log = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLog"])(abs(value), base);
        const round = log < 0 ? floor : ceil;
        return round(log);
    };
}
function getRangeCalculator(axisType, axis, getLog) {
    let rangeCalculator = continuousRangeCalculator;
    if (axisType === DISCRETE) {
        rangeCalculator = function(range, minValue, maxValue) {
            if (minValue !== maxValue) {
                range.categories.push(maxValue);
            }
            range.categories.push(minValue);
        };
    } else if (axis) {
        rangeCalculator = function(range, value) {
            const interval = axis.calculateInterval(value, range.prevValue);
            const minInterval = range.interval;
            range.interval = (minInterval < interval ? minInterval : interval) || minInterval;
            range.prevValue = value;
            continuousRangeCalculator(range, value, value);
        };
    }
    if (getLog) {
        return (range, minValue, maxValue)=>{
            const minArgs = [];
            rangeCalculator(range, minValue, maxValue);
            0 !== minValue && minArgs.push(getLog(minValue));
            0 !== maxValue && minArgs.push(getLog(maxValue));
            const linearThreshold = min.apply(null, minArgs);
            range.linearThreshold = range.linearThreshold < linearThreshold ? range.linearThreshold : linearThreshold;
        };
    }
    return rangeCalculator;
}
function getInitialRange(axisType, dataType, firstValue) {
    const range = {
        axisType: axisType,
        dataType: dataType
    };
    if (axisType === DISCRETE) {
        range.categories = [];
    } else {
        range.min = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isObject"])(firstValue) ? firstValue.min : firstValue;
        range.max = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isObject"])(firstValue) ? firstValue.max : firstValue;
    }
    return range;
}
function processCategories(range) {
    if (range.categories) {
        range.categories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unique"])(range.categories);
    }
    return range;
}
function getValueForArgument(point, extraPoint, x, range) {
    if (extraPoint && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(extraPoint.value)) {
        const y1 = point.value;
        const y2 = extraPoint.value;
        const x1 = point.argument;
        const x2 = extraPoint.argument;
        const r = (x - x1) * (y2 - y1) / (x2 - x1) + y1.valueOf();
        return "datetime" === range.dataType ? new Date(r) : r;
    } else {
        return point.value;
    }
}
function calculateRangeBetweenPoints(rangeCalculator, range, point, prevPoint, bound) {
    const value = getValueForArgument(point, prevPoint, bound, range);
    rangeCalculator(range, value, value);
}
function isLineSeries(series) {
    return series.type.toLowerCase().indexOf("line") >= 0 || series.type.toLowerCase().indexOf("area") >= 0;
}
function getViewportReducer(series) {
    const rangeCalculator = getRangeCalculator(series.valueAxisType);
    const argumentAxis = series.getArgumentAxis();
    const viewport = argumentAxis && series.getArgumentAxis().visualRange() || {};
    const calculatePointBetweenPoints = isLineSeries(series) ? calculateRangeBetweenPoints : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
    if (argumentAxis && argumentAxis.getMarginOptions().checkInterval) {
        const range = series.getArgumentAxis().getTranslator().getBusinessRange();
        const add = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAddFunction"])(range, false);
        const interval = range.interval;
        if (isFinite(interval) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.endValue)) {
            viewport.startValue = add(viewport.startValue, interval, -1);
            viewport.endValue = add(viewport.endValue, interval);
        }
    }
    const viewportFilter = getViewPortFilter(viewport);
    return function(range, point, index, points) {
        const argument = point.argument;
        if (!point.hasValue()) {
            return range;
        }
        if (viewportFilter(argument)) {
            if (!range.startCalc) {
                range.startCalc = true;
                calculatePointBetweenPoints(rangeCalculator, range, point, points[index - 1], viewport.startValue);
            }
            rangeCalculator(range, point.getMinValue(), point.getMaxValue());
        } else if (!viewport.categories && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue) && argument > viewport.startValue) {
            if (!range.startCalc) {
                calculatePointBetweenPoints(rangeCalculator, range, point, points[index - 1], viewport.startValue);
            }
            range.endCalc = true;
            calculatePointBetweenPoints(rangeCalculator, range, point, points[index - 1], viewport.endValue);
        }
        return range;
    };
}
function getViewPortFilter(viewport) {
    if (viewport.categories) {
        const dictionary = viewport.categories.reduce((result, category)=>{
            result[category.valueOf()] = true;
            return result;
        }, {});
        return (argument)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(argument) && dictionary[argument.valueOf()];
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue) && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.endValue)) {
        return ()=>true;
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.endValue)) {
        return (argument)=>argument >= viewport.startValue;
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue)) {
        return (argument)=>argument <= viewport.endValue;
    }
    return (argument)=>argument >= viewport.startValue && argument <= viewport.endValue;
}
const __TURBOPACK__default__export__ = {
    getViewPortFilter: getViewPortFilter,
    getArgumentRange: function(series) {
        const data = series._data || [];
        let range = {};
        if (data.length) {
            if (series.argumentAxisType === DISCRETE) {
                range = {
                    categories: data.map((item)=>item.argument)
                };
            } else {
                let interval;
                if (data.length > 1) {
                    const i1 = series.getArgumentAxis().calculateInterval(data[0].argument, data[1].argument);
                    const i2 = series.getArgumentAxis().calculateInterval(data[data.length - 1].argument, data[data.length - 2].argument);
                    interval = min(i1, i2);
                }
                range = {
                    min: data[0].argument,
                    max: data[data.length - 1].argument,
                    interval: interval
                };
            }
        }
        return processCategories(range);
    },
    getRangeData: function(series) {
        const points = series.getPoints();
        const useAggregation = series.useAggregation();
        const argumentAxis = series.getArgumentAxis();
        const argumentCalculator = getRangeCalculator(series.argumentAxisType, points.length > 1 && argumentAxis, createGetLogFunction(series.argumentAxisType, argumentAxis));
        const valueRangeCalculator = getRangeCalculator(series.valueAxisType, null, createGetLogFunction(series.valueAxisType, series.getValueAxis()));
        const viewportReducer = getViewportReducer(series);
        const range = points.reduce(function(range, point, index, points) {
            const argument = point.argument;
            if (!point.isArgumentCorrect()) {
                return range;
            }
            argumentCalculator(range.arg, argument, argument);
            if (point.hasValue()) {
                valueRangeCalculator(range.val, point.getMinValue(), point.getMaxValue());
                viewportReducer(range.viewport, point, index, points);
            }
            return range;
        }, {
            arg: getInitialRange(series.argumentAxisType, series.argumentType, null !== argumentAxis && void 0 !== argumentAxis && argumentAxis.aggregatedPointBetweenTicks() ? void 0 : series.getArgumentRangeInitialValue()),
            val: getInitialRange(series.valueAxisType, series.valueType, points.length ? series.getValueRangeInitialValue() : void 0),
            viewport: getInitialRange(series.valueAxisType, series.valueType, points.length ? series.getValueRangeInitialValue() : void 0)
        });
        if (useAggregation) {
            const argumentRange = this.getArgumentRange(series);
            if (series.argumentAxisType === DISCRETE) {
                range.arg = argumentRange;
            } else {
                const viewport = argumentAxis.getViewport();
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.length)) {
                    argumentCalculator(range.arg, argumentRange.min, argumentRange.min);
                }
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.endValue) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.length) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(viewport.startValue)) {
                    argumentCalculator(range.arg, argumentRange.max, argumentRange.max);
                }
            }
        }
        processCategories(range.arg);
        processCategories(range.val);
        return range;
    },
    getViewport: function(series) {
        const points = series.getPoints();
        let range = {};
        const reducer = getViewportReducer(series);
        range = getInitialRange(series.valueAxisType, series.valueType, points.length ? series.getValueRangeInitialValue() : void 0);
        points.some(function(point, index) {
            reducer(range, point, index, points);
            return range.endCalc;
        });
        return range;
    },
    getPointsInViewPort: function(series) {
        const argumentViewPortFilter = getViewPortFilter(series.getArgumentAxis().visualRange() || {});
        const valueViewPort = series.getValueAxis().visualRange() || {};
        const valueViewPortFilter = getViewPortFilter(valueViewPort);
        const points = series.getPoints();
        const addValue = function(values, point, isEdge) {
            const minValue = point.getMinValue();
            const maxValue = point.getMaxValue();
            const isMinValueInViewPort = valueViewPortFilter(minValue);
            const isMaxValueInViewPort = valueViewPortFilter(maxValue);
            if (isMinValueInViewPort) {
                values.push(minValue);
            }
            if (maxValue !== minValue && isMaxValueInViewPort) {
                values.push(maxValue);
            }
            if (isEdge && !isMinValueInViewPort && !isMaxValueInViewPort) {
                if (!values.length) {
                    values.push(valueViewPort.startValue);
                } else {
                    values.push(valueViewPort.endValue);
                }
            }
        };
        const addEdgePoints = isLineSeries(series) ? function(result, points, index) {
            const point = points[index];
            const prevPoint = points[index - 1];
            const nextPoint = points[index + 1];
            if (nextPoint && argumentViewPortFilter(nextPoint.argument)) {
                addValue(result[1], point, true);
            }
            if (prevPoint && argumentViewPortFilter(prevPoint.argument)) {
                addValue(result[1], point, true);
            }
        } : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
        return points.reduce(function(result, point, index) {
            if (argumentViewPortFilter(point.argument)) {
                addValue(result[0], point);
            } else {
                addEdgePoints(result, points, index);
            }
            return result;
        }, [
            [],
            []
        ]);
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/series/helpers/display_format_parser.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/helpers/display_format_parser.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "processDisplayFormat": ()=>processDisplayFormat
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$localization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/localization.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/localization.js [app-client] (ecmascript) <locals>");
;
const startPlaceHolderChar = "{";
const endPlaceHolderChar = "}";
const placeholderFormatDelimiter = ":";
function formatValue(value, format) {
    if (format) {
        if (value instanceof Date) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["formatDate"])(value, format);
        }
        if ("number" === typeof value) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$localization$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["formatNumber"])(value, format);
        }
    }
    return value;
}
function getValueByPlaceHolder(placeHolder, pointInfo) {
    let customFormat = "";
    const customFormatIndex = placeHolder.indexOf(":");
    if (customFormatIndex > 0) {
        customFormat = placeHolder.substr(customFormatIndex + 1);
        placeHolder = placeHolder.substr(0, customFormatIndex);
    }
    return formatValue(pointInfo[placeHolder], customFormat);
}
function processDisplayFormat(displayFormat, pointInfo) {
    let actualText = displayFormat;
    let continueProcess = true;
    while(continueProcess){
        const startBracketIndex = actualText.indexOf("{");
        const endBracketIndex = actualText.indexOf("}");
        if (startBracketIndex >= 0 && endBracketIndex > 0) {
            const placeHolder = actualText.substring(startBracketIndex + 1, endBracketIndex);
            const value = getValueByPlaceHolder(placeHolder, pointInfo);
            actualText = actualText.substr(0, startBracketIndex) + value + actualText.substr(endBracketIndex + 1);
        } else {
            continueProcess = false;
        }
    }
    return actualText;
}
}),
"[project]/node_modules/devextreme/esm/viz/series/points/label.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/label.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Label": ()=>Label
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/format_helper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$display_format_parser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/helpers/display_format_parser.js [app-client] (ecmascript)");
;
;
;
;
;
const _format = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$format_helper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].format;
const _math = Math;
const _round = _math.round;
const _floor = _math.floor;
const _abs = _math.abs;
const CONNECTOR_LENGTH = 12;
const LABEL_BACKGROUND_PADDING_X = 8;
const LABEL_BACKGROUND_PADDING_Y = 4;
function getClosestCoord(point, coords) {
    let closestDistance = 1 / 0;
    let closestCoord;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(coords, function(_, coord) {
        const x = point[0] - coord[0];
        const y = point[1] - coord[1];
        const distance = x * x + y * y;
        if (distance < closestDistance) {
            closestDistance = distance;
            closestCoord = coord;
        }
    });
    return [
        _floor(closestCoord[0]),
        _floor(closestCoord[1])
    ];
}
function getCrossCoord(rect, coord, indexOffset) {
    return (coord - rect[0 + indexOffset]) / (rect[2 + indexOffset] - rect[0 + indexOffset]) * (rect[3 - indexOffset] - rect[1 - indexOffset]) + rect[1 - indexOffset];
}
const barPointStrategy = {
    isLabelInside: function(labelPoint, figure) {
        const xc = labelPoint.x + labelPoint.width / 2;
        const yc = labelPoint.y + labelPoint.height / 2;
        return figure.x <= xc && xc <= figure.x + figure.width && figure.y <= yc && yc <= figure.y + figure.height;
    },
    prepareLabelPoints: function(bBox, rotatedBBox, isHorizontal, angle, figureCenter) {
        const x1 = rotatedBBox.x;
        const xc = x1 + rotatedBBox.width / 2;
        const x2 = x1 + rotatedBBox.width - 1;
        const y1 = rotatedBBox.y;
        const yc = y1 + rotatedBBox.height / 2;
        const y2 = y1 + rotatedBBox.height - 1;
        let labelPoints;
        const isRectangular = _abs(angle) % 90 === 0;
        if (figureCenter[0] > x1 && figureCenter[0] < x2) {
            if (isRectangular) {
                labelPoints = [
                    [
                        figureCenter[0],
                        _abs(figureCenter[1] - y1) < _abs(figureCenter[1] - y2) ? y1 : y2
                    ]
                ];
            } else {
                labelPoints = [
                    [
                        figureCenter[0],
                        getCrossCoord([
                            x1,
                            y1,
                            x2,
                            y2
                        ], figureCenter[0], 0)
                    ]
                ];
            }
        } else if (figureCenter[1] > y1 && figureCenter[1] < y2) {
            if (isRectangular) {
                labelPoints = [
                    [
                        _abs(figureCenter[0] - x1) < _abs(figureCenter[0] - x2) ? x1 : x2,
                        figureCenter[1]
                    ]
                ];
            } else {
                labelPoints = [
                    [
                        getCrossCoord([
                            x1,
                            y1,
                            x2,
                            y2
                        ], figureCenter[1], 1),
                        figureCenter[1]
                    ]
                ];
            }
        } else if (isRectangular) {
            labelPoints = [
                [
                    x1,
                    y1
                ],
                [
                    isHorizontal ? x1 : xc,
                    isHorizontal ? yc : y1
                ],
                [
                    x2,
                    y1
                ],
                [
                    x1,
                    y2
                ],
                [
                    isHorizontal ? x2 : xc,
                    isHorizontal ? yc : y2
                ],
                [
                    x2,
                    y2
                ]
            ];
        } else {
            labelPoints = [
                [
                    xc,
                    yc
                ]
            ];
        }
        return labelPoints;
    },
    isHorizontal: function(bBox, figure) {
        return bBox.x > figure.x + figure.width || bBox.x + bBox.width < figure.x;
    },
    getFigureCenter: function(figure) {
        return [
            _floor(figure.x + figure.width / 2),
            _floor(figure.y + figure.height / 2)
        ];
    },
    findFigurePoint: function(figure, labelPoint) {
        const figureCenter = barPointStrategy.getFigureCenter(figure);
        const point = getClosestCoord(labelPoint, [
            [
                figure.x,
                figureCenter[1]
            ],
            [
                figureCenter[0],
                figure.y + figure.height
            ],
            [
                figure.x + figure.width,
                figureCenter[1]
            ],
            [
                figureCenter[0],
                figure.y
            ]
        ]);
        return point;
    },
    adjustPoints: function(points) {
        const lineIsVertical = _abs(points[1] - points[3]) <= 1;
        const lineIsHorizontal = _abs(points[0] - points[2]) <= 1;
        if (lineIsHorizontal) {
            points[0] = points[2];
        }
        if (lineIsVertical) {
            points[1] = points[3];
        }
        return points;
    }
};
const symbolPointStrategy = {
    isLabelInside: function() {
        return false;
    },
    prepareLabelPoints: barPointStrategy.prepareLabelPoints,
    isHorizontal: function(bBox, figure) {
        return bBox.x > figure.x + figure.r || bBox.x + bBox.width < figure.x - figure.r;
    },
    getFigureCenter: function(figure) {
        return [
            figure.x,
            figure.y
        ];
    },
    findFigurePoint: function(figure, labelPoint) {
        const angle = Math.atan2(figure.y - labelPoint[1], labelPoint[0] - figure.x);
        return [
            _round(figure.x + figure.r * Math.cos(angle)),
            _round(figure.y - figure.r * Math.sin(angle))
        ];
    },
    adjustPoints: barPointStrategy.adjustPoints
};
const piePointStrategy = {
    isLabelInside: function(_0, _1, isOutside) {
        return !isOutside;
    },
    prepareLabelPoints: function(bBox, rotatedBBox, isHorizontal, angle) {
        const xl = bBox.x;
        const xr = xl + bBox.width;
        const xc = xl + _round(bBox.width / 2);
        const yt = bBox.y;
        const yb = yt + bBox.height;
        const yc = yt + _round(bBox.height / 2);
        let points = [
            [
                [
                    xl,
                    yt
                ],
                [
                    xr,
                    yt
                ]
            ],
            [
                [
                    xr,
                    yt
                ],
                [
                    xr,
                    yb
                ]
            ],
            [
                [
                    xr,
                    yb
                ],
                [
                    xl,
                    yb
                ]
            ],
            [
                [
                    xl,
                    yb
                ],
                [
                    xl,
                    yt
                ]
            ]
        ];
        const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(angle);
        if (0 === angle) {
            points = isHorizontal ? [
                [
                    xl,
                    yc
                ],
                [
                    xr,
                    yc
                ]
            ] : [
                [
                    xc,
                    yt
                ],
                [
                    xc,
                    yb
                ]
            ];
        } else {
            points = points.map(function(pair) {
                return pair.map(function(point) {
                    return [
                        _round((point[0] - xc) * cosSin.cos + (point[1] - yc) * cosSin.sin + xc),
                        _round(-(point[0] - xc) * cosSin.sin + (point[1] - yc) * cosSin.cos + yc)
                    ];
                });
            }).reduce(function(r, pair) {
                const point1x = pair[0][0];
                const point1y = pair[0][1];
                const point2x = pair[1][0];
                const point2y = pair[1][1];
                if (isHorizontal) {
                    if (point1y >= yc && yc >= point2y || point1y <= yc && yc <= point2y) {
                        r.push([
                            (yc - point1y) * (point2x - point1x) / (point2y - point1y) + point1x,
                            yc
                        ]);
                    }
                } else if (point1x >= xc && xc >= point2x || point1x <= xc && xc <= point2x) {
                    r.push([
                        xc,
                        (xc - point1x) * (point2y - point1y) / (point2x - point1x) + point1y
                    ]);
                }
                return r;
            }, []);
        }
        return points;
    },
    isHorizontal: function(bBox, figure) {
        return bBox.x > figure.x || figure.x > bBox.x + bBox.width;
    },
    getFigureCenter: symbolPointStrategy.getFigureCenter,
    findFigurePoint: function(figure, labelPoint, isHorizontal) {
        if (!isHorizontal) {
            return [
                figure.x,
                figure.y
            ];
        }
        const labelX = labelPoint[0];
        const x = _round(figure.x + (figure.y - labelPoint[1]) / Math.tan((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["degreesToRadians"])(figure.angle)));
        let points = [
            figure.x,
            figure.y,
            x,
            labelPoint[1]
        ];
        if (!(figure.x <= x && x <= labelX) && !(labelX <= x && x <= figure.x)) {
            if (_abs(figure.x - labelX) < 12) {
                points = [
                    figure.x,
                    figure.y
                ];
            } else if (figure.x <= labelX) {
                points[2] = figure.x + 12;
            } else {
                points[2] = figure.x - 12;
            }
        }
        return points;
    },
    adjustPoints: function(points) {
        return points;
    }
};
function selectStrategy(figure) {
    return void 0 !== figure.angle && piePointStrategy || void 0 !== figure.r && symbolPointStrategy || barPointStrategy;
}
function disposeItem(obj, field) {
    obj[field] && obj[field].dispose();
    obj[field] = null;
}
function checkBackground(background) {
    return background && (background.fill && "none" !== background.fill || background["stroke-width"] > 0 && background.stroke && "none" !== background.stroke);
}
function checkConnector(connector) {
    return connector && connector["stroke-width"] > 0 && connector.stroke && "none" !== connector.stroke;
}
function formatText(data, options) {
    const format = options.format;
    data.valueText = _format(data.value, format);
    data.argumentText = _format(data.argument, options.argumentFormat);
    if (void 0 !== data.percent) {
        data.percentText = _format(data.percent, {
            type: "percent",
            precision: format && format.percentPrecision
        });
    }
    if (void 0 !== data.total) {
        data.totalText = _format(data.total, format);
    }
    if (void 0 !== data.openValue) {
        data.openValueText = _format(data.openValue, format);
    }
    if (void 0 !== data.closeValue) {
        data.closeValueText = _format(data.closeValue, format);
    }
    if (void 0 !== data.lowValue) {
        data.lowValueText = _format(data.lowValue, format);
    }
    if (void 0 !== data.highValue) {
        data.highValueText = _format(data.highValue, format);
    }
    if (void 0 !== data.reductionValue) {
        data.reductionValueText = _format(data.reductionValue, format);
    }
    return options.customizeText ? options.customizeText.call(data, data) : options.displayFormat ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$display_format_parser$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processDisplayFormat"])(options.displayFormat, data) : data.valueText;
}
function Label(renderSettings) {
    this._renderer = renderSettings.renderer;
    this._container = renderSettings.labelsGroup;
    this._point = renderSettings.point;
    this._strategy = renderSettings.strategy;
    this._rowCount = 1;
}
Label.prototype = {
    constructor: Label,
    setColor: function(color) {
        this._color = color;
    },
    setOptions: function(options) {
        this._options = options;
    },
    setData: function(data) {
        this._data = data;
    },
    setDataField: function(fieldName, fieldValue) {
        this._data = this._data || {};
        this._data[fieldName] = fieldValue;
    },
    getData: function() {
        return this._data;
    },
    setFigureToDrawConnector: function(figure) {
        this._figure = figure;
    },
    dispose: function() {
        disposeItem(this, "_group");
        this._data = this._options = this._textContent = this._visible = this._insideGroup = this._text = this._background = this._connector = this._figure = null;
    },
    _setVisibility: function(value, state) {
        this._group && this._group.attr({
            visibility: value
        });
        this._visible = state;
    },
    isVisible: function() {
        return this._visible;
    },
    hide: function(holdInvisible) {
        this._holdVisibility = !!holdInvisible;
        this._hide();
    },
    _hide: function() {
        this._setVisibility("hidden", false);
    },
    show: function(holdVisible) {
        const correctPosition = !this._drawn;
        if (this._point.hasValue()) {
            this._holdVisibility = !!holdVisible;
            this._show();
            correctPosition && this._point.correctLabelPosition(this);
        }
    },
    _show: function() {
        const that = this;
        const renderer = that._renderer;
        const container = that._container;
        const options = that._options || {};
        const text = that._textContent = formatText(that._data, options) || null;
        if (text) {
            if (!that._group) {
                that._group = renderer.g().append(container);
                that._insideGroup = renderer.g().append(that._group);
                that._text = renderer.text("", 0, 0).append(that._insideGroup);
            }
            that._text.css(options.attributes ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["patchFontOptions"])(options.attributes.font) : {});
            if (checkBackground(options.background)) {
                that._background = that._background || renderer.rect().append(that._insideGroup).toBackground();
                that._background.attr(options.background);
                that._color && that._background.attr({
                    fill: that._color
                });
            } else {
                disposeItem(that, "_background");
            }
            if (checkConnector(options.connector)) {
                that._connector = that._connector || renderer.path([], "line").sharp().append(that._group).toBackground();
                that._connector.attr(options.connector);
                that._color && that._connector.attr({
                    stroke: that._color
                });
            } else {
                disposeItem(that, "_connector");
            }
            that._text.attr({
                text: text,
                align: options.textAlignment,
                class: options.cssClass
            });
            that._updateBackground(that._text.getBBox());
            that._setVisibility("visible", true);
            that._drawn = true;
        } else {
            that._hide();
        }
    },
    _getLabelVisibility: function(isVisible) {
        return this._holdVisibility ? this.isVisible() : isVisible;
    },
    draw: function(isVisible) {
        if (this._getLabelVisibility(isVisible)) {
            this._show();
            this._point && this._point.correctLabelPosition(this);
        } else {
            this._drawn = false;
            this._hide();
        }
        return this;
    },
    _updateBackground: function(bBox) {
        const that = this;
        if (that._background) {
            bBox.x -= 8;
            bBox.y -= 4;
            bBox.width += 16;
            bBox.height += 8;
            that._background.attr(bBox);
        }
        that._bBoxWithoutRotation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, bBox);
        const rotationAngle = that._options.rotationAngle || 0;
        that._insideGroup.rotate(rotationAngle, bBox.x + bBox.width / 2, bBox.y + bBox.height / 2);
        bBox = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["rotateBBox"])(bBox, [
            bBox.x + bBox.width / 2,
            bBox.y + bBox.height / 2
        ], -rotationAngle);
        that._bBox = bBox;
    },
    getFigureCenter () {
        const figure = this._figure;
        const strategy = this._strategy || selectStrategy(figure);
        return strategy.getFigureCenter(figure);
    },
    _getConnectorPoints: function() {
        const that = this;
        const figure = that._figure;
        const options = that._options;
        const strategy = that._strategy || selectStrategy(figure);
        const bBox = that._shiftBBox(that._bBoxWithoutRotation);
        const rotatedBBox = that.getBoundingRect();
        let labelPoint;
        let points = [];
        let isHorizontal;
        if (!strategy.isLabelInside(bBox, figure, "inside" !== options.position)) {
            isHorizontal = strategy.isHorizontal(bBox, figure);
            const figureCenter = that.getFigureCenter();
            points = strategy.prepareLabelPoints(bBox, rotatedBBox, isHorizontal, -options.rotationAngle || 0, figureCenter);
            labelPoint = getClosestCoord(figureCenter, points);
            points = strategy.findFigurePoint(figure, labelPoint, isHorizontal);
            points = points.concat(labelPoint);
        }
        return strategy.adjustPoints(points);
    },
    fit: function(maxWidth) {
        const padding = this._background ? 16 : 0;
        let rowCountChanged = false;
        if (this._text) {
            const result = this._text.setMaxSize(maxWidth - padding, void 0, this._options);
            let rowCount = result.rowCount;
            if (0 === rowCount) {
                rowCount = 1;
            }
            if (rowCount !== this._rowCount) {
                rowCountChanged = true;
                this._rowCount = rowCount;
            }
            result.textIsEmpty && disposeItem(this, "_background");
        }
        this._updateBackground(this._text.getBBox());
        return rowCountChanged;
    },
    resetEllipsis: function() {
        this._text && this._text.restoreText();
        this._updateBackground(this._text.getBBox());
    },
    setTrackerData: function(point) {
        this._text.data({
            "chart-data-point": point
        });
        this._background && this._background.data({
            "chart-data-point": point
        });
    },
    hideInsideLabel: function(coords) {
        return this._point.hideInsideLabel(this, coords);
    },
    getPoint () {
        return this._point;
    },
    shift: function(x, y) {
        const that = this;
        if (that._textContent) {
            that._insideGroup.attr({
                translateX: that._x = _round(x - that._bBox.x),
                translateY: that._y = _round(y - that._bBox.y)
            });
            if (that._connector) {
                that._connector.attr({
                    points: that._getConnectorPoints()
                });
            }
        }
        return that;
    },
    getBoundingRect: function() {
        return this._shiftBBox(this._bBox);
    },
    _shiftBBox: function(bBox) {
        return this._textContent ? {
            x: bBox.x + this._x,
            y: bBox.y + this._y,
            width: bBox.width,
            height: bBox.height
        } : {};
    },
    getLayoutOptions: function() {
        const options = this._options;
        return {
            alignment: options.alignment,
            background: checkBackground(options.background),
            horizontalOffset: options.horizontalOffset,
            verticalOffset: options.verticalOffset,
            radialOffset: options.radialOffset,
            position: options.position,
            connectorOffset: (checkConnector(options.connector) ? 12 : 0) + (checkBackground(options.background) ? 8 : 0)
        };
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/symbol_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/window.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_window.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/label.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
;
;
;
;
const window = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getWindow"])();
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
;
;
const _math = Math;
const _round = _math.round;
const _floor = _math.floor;
const _ceil = _math.ceil;
const DEFAULT_IMAGE_WIDTH = 20;
const DEFAULT_IMAGE_HEIGHT = 20;
const LABEL_OFFSET = 10;
const CANVAS_POSITION_DEFAULT = "canvas_position_default";
function getSquareMarkerCoords(radius) {
    return [
        -radius,
        -radius,
        radius,
        -radius,
        radius,
        radius,
        -radius,
        radius,
        -radius,
        -radius
    ];
}
function getPolygonMarkerCoords(radius) {
    const r = _ceil(radius);
    return [
        -r,
        0,
        0,
        -r,
        r,
        0,
        0,
        r,
        -r,
        0
    ];
}
function getCrossMarkerCoords(radius) {
    const r = _ceil(radius);
    const floorHalfRadius = _floor(r / 2);
    const ceilHalfRadius = _ceil(r / 2);
    return [
        -r,
        -floorHalfRadius,
        -floorHalfRadius,
        -r,
        0,
        -ceilHalfRadius,
        floorHalfRadius,
        -r,
        r,
        -floorHalfRadius,
        ceilHalfRadius,
        0,
        r,
        floorHalfRadius,
        floorHalfRadius,
        r,
        0,
        ceilHalfRadius,
        -floorHalfRadius,
        r,
        -r,
        floorHalfRadius,
        -ceilHalfRadius,
        0
    ];
}
function getTriangleDownMarkerCoords(radius) {
    return [
        -radius,
        -radius,
        radius,
        -radius,
        0,
        radius,
        -radius,
        -radius
    ];
}
function getTriangleUpMarkerCoords(radius) {
    return [
        -radius,
        radius,
        radius,
        radius,
        0,
        -radius,
        -radius,
        radius
    ];
}
const __TURBOPACK__default__export__ = {
    deleteLabel: function() {
        this._label.dispose();
        this._label = null;
    },
    _hasGraphic: function() {
        return this.graphic;
    },
    clearVisibility: function() {
        const graphic = this.graphic;
        if (graphic && graphic.attr("visibility")) {
            graphic.attr({
                visibility: null
            });
        }
    },
    isVisible: function() {
        return this.inVisibleArea && this.series.isVisible();
    },
    setInvisibility: function() {
        const graphic = this.graphic;
        if (graphic && "hidden" !== graphic.attr("visibility")) {
            graphic.attr({
                visibility: "hidden"
            });
        }
        this._errorBar && this._errorBar.attr({
            visibility: "hidden"
        });
        this._label.draw(false);
    },
    clearMarker: function() {
        const graphic = this.graphic;
        graphic && graphic.attr(this._emptySettings);
    },
    _createLabel: function() {
        this._label = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"]({
            renderer: this.series._renderer,
            labelsGroup: this.series._labelsGroup,
            point: this
        });
    },
    _calculateVisibility: function(x, y) {
        const { minX: minX, maxX: maxX, minY: minY, maxY: maxY } = this._getVisibleArea();
        this.inVisibleArea = minX <= x && maxX >= x && minY <= y && maxY >= y;
    },
    _updateLabelData: function() {
        this._label.setData(this._getLabelFormatObject());
    },
    _updateLabelOptions: function() {
        !this._label && this._createLabel();
        this._label.setOptions(this._options.label);
    },
    _checkImage: function(image) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(image) && ("string" === typeof image || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(image.url));
    },
    _fillStyle: function() {
        this._styles = this._options.styles;
    },
    _checkSymbol: function(oldOptions, newOptions) {
        const oldSymbol = oldOptions.symbol;
        const newSymbol = newOptions.symbol;
        const symbolChanged = "circle" === oldSymbol && "circle" !== newSymbol || "circle" !== oldSymbol && "circle" === newSymbol;
        const imageChanged = this._checkImage(oldOptions.image) !== this._checkImage(newOptions.image);
        return !!(symbolChanged || imageChanged);
    },
    _populatePointShape: function(symbol, radius) {
        switch(symbol){
            case "square":
                return getSquareMarkerCoords(radius);
            case "polygon":
                return getPolygonMarkerCoords(radius);
            case "triangle":
            case "triangleDown":
                return getTriangleDownMarkerCoords(radius);
            case "triangleUp":
                return getTriangleUpMarkerCoords(radius);
            case "cross":
                return getCrossMarkerCoords(radius);
        }
    },
    hasCoords: function() {
        return null !== this.x && null !== this.y;
    },
    correctValue: function(correction) {
        const that = this;
        const axis = that.series.getValueAxis();
        if (that.hasValue()) {
            that.value = that.properValue = axis.validateUnit(that.initialValue.valueOf() + correction.valueOf());
            that.minValue = axis.validateUnit(correction);
        }
    },
    resetCorrection: function() {
        this.value = this.properValue = this.initialValue;
        this.minValue = CANVAS_POSITION_DEFAULT;
    },
    resetValue: function() {
        const that = this;
        if (that.hasValue()) {
            that.value = that.properValue = that.initialValue = 0;
            that.minValue = 0;
            that._label.setDataField("value", that.value);
        }
    },
    _getTranslates: function(animationEnabled) {
        let translateX = this.x;
        let translateY = this.y;
        if (animationEnabled) {
            if (this._options.rotated) {
                translateX = this.defaultX;
            } else {
                translateY = this.defaultY;
            }
        }
        return {
            x: translateX,
            y: translateY
        };
    },
    _createImageMarker: function(renderer, settings, options) {
        const width = options.width || 20;
        const height = options.height || 20;
        return renderer.image(-_round(.5 * width), -_round(.5 * height), width, height, options.url ? options.url.toString() : options.toString(), "center").attr({
            translateX: settings.translateX,
            translateY: settings.translateY,
            visibility: settings.visibility
        });
    },
    _createSymbolMarker: function(renderer, pointSettings) {
        let marker;
        const symbol = this._options.symbol;
        if ("circle" === symbol) {
            delete pointSettings.points;
            marker = renderer.circle().attr(pointSettings);
        } else if ("square" === symbol || "polygon" === symbol || "triangle" === symbol || "triangleDown" === symbol || "triangleUp" === symbol || "cross" === symbol) {
            marker = renderer.path([], "area").attr(pointSettings).sharp();
        }
        return marker;
    },
    _createMarker: function(renderer, group, image, settings) {
        const that = this;
        const marker = that._checkImage(image) ? that._createImageMarker(renderer, settings, image) : that._createSymbolMarker(renderer, settings);
        if (marker) {
            marker.data({
                "chart-data-point": that
            }).append(group);
        }
        return marker;
    },
    _getSymbolBBox: function(x, y, r) {
        return {
            x: x - r,
            y: y - r,
            width: 2 * r,
            height: 2 * r
        };
    },
    _getImageBBox: function(x, y) {
        const image = this._options.image;
        const width = image.width || 20;
        const height = image.height || 20;
        return {
            x: x - _round(width / 2),
            y: y - _round(height / 2),
            width: width,
            height: height
        };
    },
    _getGraphicBBox: function() {
        const that = this;
        const options = that._options;
        const x = that.x;
        const y = that.y;
        let bBox;
        if (options.visible) {
            bBox = that._checkImage(options.image) ? that._getImageBBox(x, y) : that._getSymbolBBox(x, y, options.styles.normal.r);
        } else {
            bBox = {
                x: x,
                y: y,
                width: 0,
                height: 0
            };
        }
        return bBox;
    },
    hideInsideLabel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _getShiftLabelCoords: function(label) {
        const coord = this._addLabelAlignmentAndOffset(label, this._getLabelCoords(label));
        return this._checkLabelPosition(label, coord);
    },
    _drawLabel: function() {
        const customVisibility = this._getCustomLabelVisibility();
        const label = this._label;
        const isVisible = this._showForZeroValues() && this.hasValue() && false !== customVisibility && (this.series.getLabelVisibility() || customVisibility);
        label.draw(!!isVisible);
    },
    correctLabelPosition: function(label) {
        const that = this;
        const coord = that._getShiftLabelCoords(label);
        if (!that.hideInsideLabel(label, coord)) {
            label.setFigureToDrawConnector(that._getLabelConnector(label.pointPosition));
            label.shift(_round(coord.x), _round(coord.y));
        }
    },
    _showForZeroValues: function() {
        return true;
    },
    _getLabelConnector: function(pointPosition) {
        const bBox = this._getGraphicBBox(pointPosition);
        const w2 = bBox.width / 2;
        const h2 = bBox.height / 2;
        return {
            x: bBox.x + w2,
            y: bBox.y + h2,
            r: this._options.visible ? Math.max(w2, h2) : 0
        };
    },
    _getPositionFromLocation: function() {
        return {
            x: this.x,
            y: this.y
        };
    },
    _isPointInVisibleArea: function(visibleArea, graphicBBox) {
        return visibleArea.minX <= graphicBBox.x + graphicBBox.width && visibleArea.maxX >= graphicBBox.x && visibleArea.minY <= graphicBBox.y + graphicBBox.height && visibleArea.maxY >= graphicBBox.y;
    },
    _checkLabelPosition: function(label, coord) {
        const that = this;
        const visibleArea = that._getVisibleArea();
        const labelBBox = label.getBoundingRect();
        const graphicBBox = that._getGraphicBBox(label.pointPosition);
        const fullGraphicBBox = that._getGraphicBBox();
        const isInside = "inside" === label.getLayoutOptions().position;
        if (that._isPointInVisibleArea(visibleArea, fullGraphicBBox)) {
            if (!that._options.rotated) {
                if (visibleArea.minX > coord.x) {
                    coord.x = visibleArea.minX;
                }
                if (visibleArea.maxX < coord.x + labelBBox.width) {
                    coord.x = visibleArea.maxX - labelBBox.width;
                }
                if (visibleArea.minY > coord.y) {
                    coord.y = isInside ? visibleArea.minY : graphicBBox.y + graphicBBox.height + 10;
                }
                if (visibleArea.maxY < coord.y + labelBBox.height) {
                    coord.y = isInside ? visibleArea.maxY - labelBBox.height : graphicBBox.y - labelBBox.height - 10;
                }
            } else {
                if (visibleArea.minX > coord.x) {
                    coord.x = isInside ? visibleArea.minX : graphicBBox.x + graphicBBox.width + 10;
                }
                if (visibleArea.maxX < coord.x + labelBBox.width) {
                    coord.x = isInside ? visibleArea.maxX - labelBBox.width : graphicBBox.x - 10 - labelBBox.width;
                }
                if (visibleArea.minY > coord.y) {
                    coord.y = visibleArea.minY;
                }
                if (visibleArea.maxY < coord.y + labelBBox.height) {
                    coord.y = visibleArea.maxY - labelBBox.height;
                }
            }
        }
        return coord;
    },
    _addLabelAlignmentAndOffset: function(label, coord) {
        const labelBBox = label.getBoundingRect();
        const labelOptions = label.getLayoutOptions();
        if (!this._options.rotated) {
            if ("left" === labelOptions.alignment) {
                coord.x += labelBBox.width / 2;
            } else if ("right" === labelOptions.alignment) {
                coord.x -= labelBBox.width / 2;
            }
        }
        coord.x += labelOptions.horizontalOffset;
        coord.y += labelOptions.verticalOffset;
        return coord;
    },
    _getLabelCoords: function(label) {
        return this._getLabelCoordOfPosition(label, this._getLabelPosition(label.pointPosition));
    },
    _getLabelCoordOfPosition: function(label, position) {
        const labelBBox = label.getBoundingRect();
        const graphicBBox = this._getGraphicBBox(label.pointPosition);
        const centerY = graphicBBox.height / 2 - labelBBox.height / 2;
        const centerX = graphicBBox.width / 2 - labelBBox.width / 2;
        let x = graphicBBox.x;
        let y = graphicBBox.y;
        switch(position){
            case "left":
                x -= labelBBox.width + 10;
                y += centerY;
                break;
            case "right":
                x += graphicBBox.width + 10;
                y += centerY;
                break;
            case "top":
                x += centerX;
                y -= labelBBox.height + 10;
                break;
            case "bottom":
                x += centerX;
                y += graphicBBox.height + 10;
                break;
            case "inside":
                x += centerX;
                y += centerY;
        }
        return {
            x: x,
            y: y
        };
    },
    _drawMarker: function(renderer, group, animationEnabled) {
        const options = this._options;
        const translates = this._getTranslates(animationEnabled);
        const style = this._getStyle();
        this.graphic = this._createMarker(renderer, group, options.image, _extend({
            translateX: translates.x,
            translateY: translates.y,
            points: this._populatePointShape(options.symbol, style.r)
        }, style));
    },
    _getErrorBarSettings: function() {
        return {
            visibility: "visible"
        };
    },
    _getErrorBarBaseEdgeLength () {
        return 2 * this.getPointRadius();
    },
    _drawErrorBar: function(renderer, group) {
        if (!this._options.errorBars) {
            return;
        }
        const that = this;
        const options = that._options;
        const errorBarOptions = options.errorBars;
        const points = [];
        let settings;
        const pos = that._errorBarPos;
        let high = that._highErrorCoord;
        let low = that._lowErrorCoord;
        const displayMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(errorBarOptions.displayMode);
        const isHighDisplayMode = "high" === displayMode;
        const isLowDisplayMode = "low" === displayMode;
        const highErrorOnly = (isHighDisplayMode || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(low)) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(high) && !isLowDisplayMode;
        const lowErrorOnly = (isLowDisplayMode || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(high)) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(low) && !isHighDisplayMode;
        let edgeLength = errorBarOptions.edgeLength;
        if (edgeLength <= 1 && edgeLength > 0) {
            edgeLength = this._getErrorBarBaseEdgeLength() * errorBarOptions.edgeLength;
        }
        edgeLength = _floor(parseInt(edgeLength) / 2);
        highErrorOnly && (low = that._baseErrorBarPos);
        lowErrorOnly && (high = that._baseErrorBarPos);
        if ("none" !== displayMode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(high) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(low) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(pos)) {
            !lowErrorOnly && points.push([
                pos - edgeLength,
                high,
                pos + edgeLength,
                high
            ]);
            points.push([
                pos,
                high,
                pos,
                low
            ]);
            !highErrorOnly && points.push([
                pos + edgeLength,
                low,
                pos - edgeLength,
                low
            ]);
            options.rotated && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(points, function(_, p) {
                p.reverse();
            });
            settings = that._getErrorBarSettings(errorBarOptions);
            if (!that._errorBar) {
                that._errorBar = renderer.path(points, "line").attr(settings).append(group);
            } else {
                settings.points = points;
                that._errorBar.attr(settings);
            }
        } else {
            that._errorBar && that._errorBar.attr({
                visibility: "hidden"
            });
        }
    },
    getTooltipParams: function() {
        const graphic = this.graphic;
        return {
            x: this.x,
            y: this.y,
            offset: graphic ? graphic.getBBox().height / 2 : 0
        };
    },
    setPercentValue: function(absTotal, total, leftHoleTotal, rightHoleTotal) {
        const that = this;
        const valuePercent = that.value / absTotal || 0;
        const minValuePercent = that.minValue / absTotal || 0;
        const percent = valuePercent - minValuePercent;
        that._label.setDataField("percent", percent);
        that._label.setDataField("total", total);
        if (that.series.isFullStackedSeries() && that.hasValue()) {
            if (that.leftHole) {
                that.leftHole /= absTotal - leftHoleTotal;
                that.minLeftHole /= absTotal - leftHoleTotal;
            }
            if (that.rightHole) {
                that.rightHole /= absTotal - rightHoleTotal;
                that.minRightHole /= absTotal - rightHoleTotal;
            }
            that.value = that.properValue = valuePercent;
            that.minValue = !minValuePercent ? that.minValue : minValuePercent;
        }
    },
    _storeTrackerR: function() {
        let navigator = window.navigator;
        const r = this._options.styles.normal.r;
        const minTrackerSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_window$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasProperty"])("ontouchstart") || navigator.msPointerEnabled && navigator.msMaxTouchPoints || navigator.pointerEnabled && navigator.maxTouchPoints ? 20 : 6;
        this._options.trackerR = r < minTrackerSize ? minTrackerSize : r;
        return this._options.trackerR;
    },
    _translateErrorBars: function() {
        const options = this._options;
        const rotated = options.rotated;
        const errorBars = options.errorBars;
        const translator = this._getValTranslator();
        if (!errorBars) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.lowError) && (this._lowErrorCoord = translator.translate(this.lowError));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.highError) && (this._highErrorCoord = translator.translate(this.highError));
        this._errorBarPos = _floor(rotated ? this.vy : this.vx);
        this._baseErrorBarPos = "stdDeviation" === errorBars.type ? this._lowErrorCoord + (this._highErrorCoord - this._lowErrorCoord) / 2 : rotated ? this.vx : this.vy;
    },
    _translate: function() {
        const that = this;
        const valTranslator = that._getValTranslator();
        const argTranslator = that._getArgTranslator();
        if (that._options.rotated) {
            that.vx = that.x = valTranslator.translate(that.value, void 0, true);
            that.vy = that.y = argTranslator.translate(that.argument, void 0, true);
            that.minX = valTranslator.translate(that.minValue, void 0, true);
            that.defaultX = valTranslator.translate(CANVAS_POSITION_DEFAULT);
        } else {
            that.vy = that.y = valTranslator.translate(that.value, void 0, true);
            that.vx = that.x = argTranslator.translate(that.argument, void 0, true);
            that.minY = valTranslator.translate(that.minValue, void 0, true);
            that.defaultY = valTranslator.translate(CANVAS_POSITION_DEFAULT);
        }
        that._translateErrorBars();
        that._calculateVisibility(that.x, that.y);
    },
    _updateData: function(data) {
        this.value = this.properValue = this.initialValue = this.originalValue = data.value;
        this.minValue = this.initialMinValue = this.originalMinValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(data.minValue) ? data.minValue : CANVAS_POSITION_DEFAULT;
    },
    _getImageSettings: function(image) {
        return {
            href: image.url || image.toString(),
            width: image.width || 20,
            height: image.height || 20
        };
    },
    getCrosshairData: function() {
        const r = this._options.rotated;
        const value = this.properValue;
        const argument = this.argument;
        return {
            x: this.vx,
            y: this.vy,
            xValue: r ? value : argument,
            yValue: r ? argument : value,
            axis: this.series.axis
        };
    },
    getPointRadius: function() {
        const style = this._getStyle();
        const options = this._options;
        const r = style.r;
        let extraSpace;
        const symbol = options.symbol;
        const isSquare = "square" === symbol;
        const isTriangle = "triangle" === symbol || "triangleDown" === symbol || "triangleUp" === symbol;
        if (options.visible && !options.image && r) {
            extraSpace = style["stroke-width"] / 2;
            return (isSquare || isTriangle ? 1.4 * r : r) + extraSpace;
        }
        return 0;
    },
    _updateMarker: function(animationEnabled, style) {
        const that = this;
        const options = that._options;
        let settings;
        const image = options.image;
        const visibility = !that.isVisible() ? {
            visibility: "hidden"
        } : {};
        if (that._checkImage(image)) {
            settings = _extend({}, {
                visibility: style.visibility
            }, visibility, that._getImageSettings(image));
        } else {
            settings = _extend({}, style, visibility, {
                points: that._populatePointShape(options.symbol, style.r)
            });
        }
        if (!animationEnabled) {
            settings.translateX = that.x;
            settings.translateY = that.y;
        }
        that.graphic.attr(settings).sharp();
    },
    _getLabelFormatObject: function() {
        return {
            argument: this.initialArgument,
            value: this.initialValue,
            originalArgument: this.originalArgument,
            originalValue: this.originalValue,
            seriesName: this.series.name,
            lowErrorValue: this.lowError,
            highErrorValue: this.highError,
            point: this
        };
    },
    _getLabelPosition: function() {
        const rotated = this._options.rotated;
        if (this.initialValue > 0) {
            return rotated ? "right" : "top";
        } else {
            return rotated ? "left" : "bottom";
        }
    },
    _getFormatObject: function(tooltip) {
        const labelFormatObject = this._label.getData();
        return _extend({}, labelFormatObject, {
            argumentText: tooltip.formatValue(this.initialArgument, "argument"),
            valueText: tooltip.formatValue(this.initialValue)
        }, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(labelFormatObject.percent) ? {
            percentText: tooltip.formatValue(labelFormatObject.percent, "percent")
        } : {}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(labelFormatObject.total) ? {
            totalText: tooltip.formatValue(labelFormatObject.total)
        } : {});
    },
    getMarkerVisibility: function() {
        return this._options.visible;
    },
    coordsIn: function(x, y) {
        const trackerRadius = this._storeTrackerR();
        return x >= this.x - trackerRadius && x <= this.x + trackerRadius && y >= this.y - trackerRadius && y <= this.y + trackerRadius;
    },
    getMinValue: function(noErrorBar) {
        const errorBarOptions = this._options.errorBars;
        if (errorBarOptions && !noErrorBar) {
            const displayMode = errorBarOptions.displayMode;
            const lowValue = "high" !== displayMode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.lowError) ? this.lowError : this.value;
            const highValue = "low" !== displayMode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.highError) ? this.highError : this.value;
            return lowValue < highValue ? lowValue : highValue;
        } else {
            return this.value;
        }
    },
    getMaxValue: function(noErrorBar) {
        const errorBarOptions = this._options.errorBars;
        if (errorBarOptions && !noErrorBar) {
            const displayMode = errorBarOptions.displayMode;
            const lowValue = "high" !== displayMode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.lowError) ? this.lowError : this.value;
            const highValue = "low" !== displayMode && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.highError) ? this.highError : this.value;
            return lowValue > highValue ? lowValue : highValue;
        } else {
            return this.value;
        }
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/series/points/bar_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/bar_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const _math = Math;
const _floor = _math.floor;
const _abs = _math.abs;
;
const CANVAS_POSITION_DEFAULT = "canvas_position_default";
const DEFAULT_BAR_TRACKER_SIZE = 9;
const CORRECTING_BAR_TRACKER_VALUE = 4;
const RIGHT = "right";
const LEFT = "left";
const TOP = "top";
const BOTTOM = "bottom";
function getLabelOrientation(point) {
    const initialValue = point.initialValue;
    const invert = point._getValTranslator().getBusinessRange().invert;
    const isDiscreteValue = "discrete" === point.series.valueAxisType;
    const isFullStacked = point.series.isFullStackedSeries();
    const notAxisInverted = !isDiscreteValue && (initialValue >= 0 && !invert || initialValue < 0 && invert) || isDiscreteValue && !invert || isFullStacked;
    return notAxisInverted ? TOP : BOTTOM;
}
const __TURBOPACK__default__export__ = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    correctCoordinates (correctOptions) {
        const that = this;
        const correction = _floor(correctOptions.offset - correctOptions.width / 2);
        if (that._options.rotated) {
            that.height = correctOptions.width;
            that.yCorrection = correction;
            that.xCorrection = null;
        } else {
            that.width = correctOptions.width;
            that.xCorrection = correction;
            that.yCorrection = null;
        }
    },
    _calculateVisibility: function(x, y, width, height) {
        const { minX: minX, maxX: maxX, minY: minY, maxY: maxY } = this._getVisibleArea();
        this.inVisibleArea = minX <= x + width && maxX >= x && minY <= y + height && maxY >= y;
    },
    _cacheVisibility: function(x, y, minY, rotated) {
        const size = Math.abs(y - minY);
        y = Math.min(y, minY);
        if (rotated) {
            this._calculateVisibility(y, x, size, this.height);
        } else {
            this._calculateVisibility(x, y, this.width, size);
        }
    },
    _getGraphicBBox: function(location) {
        const bBox = {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
        if (location) {
            const isTop = "top" === location;
            if (!this._options.rotated) {
                bBox.y = isTop ? bBox.y : bBox.y + bBox.height;
                bBox.height = 0;
            } else {
                bBox.x = isTop ? bBox.x + bBox.width : bBox.x;
                bBox.width = 0;
            }
        }
        return bBox;
    },
    _getLabelConnector: function(location) {
        return this._getGraphicBBox(location);
    },
    _getLabelPosition: function() {
        let position = getLabelOrientation(this);
        if (this._options.rotated) {
            position = position === TOP ? RIGHT : LEFT;
        }
        return position;
    },
    _getLabelCoords: function(label) {
        const that = this;
        let coords;
        if (0 === that.initialValue && that.series.isFullStackedSeries()) {
            if (!this._options.rotated) {
                coords = that._getLabelCoordOfPosition(label, TOP);
            } else {
                coords = that._getLabelCoordOfPosition(label, RIGHT);
            }
        } else if ("inside" === label.getLayoutOptions().position) {
            coords = that._getLabelCoordOfPosition(label, "inside");
        } else {
            coords = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelCoords.call(this, label);
        }
        return coords;
    },
    _drawLabel: function() {
        this._label.pointPosition = "inside" !== this._label.getLayoutOptions().position && getLabelOrientation(this);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._drawLabel.call(this);
    },
    hideInsideLabel: function(label, coord) {
        const graphicBBox = this._getGraphicBBox();
        const labelBBox = label.getBoundingRect();
        if (this._options.resolveLabelsOverlapping) {
            if ((coord.y <= graphicBBox.y && coord.y + labelBBox.height >= graphicBBox.y + graphicBBox.height || coord.x <= graphicBBox.x && coord.x + labelBBox.width >= graphicBBox.x + graphicBBox.width) && !(coord.y > graphicBBox.y + graphicBBox.height || coord.y + labelBBox.height < graphicBBox.y || coord.x > graphicBBox.x + graphicBBox.width || coord.x + labelBBox.width < graphicBBox.x)) {
                label.draw(false);
                return true;
            }
        }
        return false;
    },
    _showForZeroValues: function() {
        return this._options.label.showForZeroValues || this.initialValue;
    },
    _drawMarker (renderer, group, animationEnabled) {
        const that = this;
        const style = that._getStyle();
        const r = that._options.cornerRadius;
        const rotated = that._options.rotated;
        let { x: x, y: y, width: width, height: height } = that.getMarkerCoords();
        if (animationEnabled) {
            if (rotated) {
                width = 0;
                x = that.defaultX;
            } else {
                height = 0;
                y = that.defaultY;
            }
        }
        that.graphic = renderer.rect(x, y, width, height).attr({
            rx: r,
            ry: r
        }).smartAttr(style).data({
            "chart-data-point": that
        }).append(group);
    },
    _getSettingsForTracker: function() {
        let y = this.y;
        let height = this.height;
        let x = this.x;
        let width = this.width;
        if (this._options.rotated) {
            if (1 === width) {
                width = 9;
                x -= 4;
            }
        } else if (1 === height) {
            height = 9;
            y -= 4;
        }
        return {
            x: x,
            y: y,
            width: width,
            height: height
        };
    },
    getGraphicSettings: function() {
        const graphic = this.graphic;
        return {
            x: graphic.attr("x"),
            y: graphic.attr("y"),
            height: graphic.attr("height"),
            width: graphic.attr("width")
        };
    },
    _getEdgeTooltipParams () {
        const isPositive = this.value >= 0;
        let xCoord;
        let yCoord;
        const invertedBusinessRange = this._getValTranslator().getBusinessRange().invert;
        const { x: x, y: y, width: width, height: height } = this;
        if (this._options.rotated) {
            yCoord = y + height / 2;
            if (invertedBusinessRange) {
                xCoord = isPositive ? x : x + width;
            } else {
                xCoord = isPositive ? x + width : x;
            }
        } else {
            xCoord = x + width / 2;
            if (invertedBusinessRange) {
                yCoord = isPositive ? y + height : y;
            } else {
                yCoord = isPositive ? y : y + height;
            }
        }
        return {
            x: xCoord,
            y: yCoord,
            offset: 0
        };
    },
    getTooltipParams: function(location) {
        if ("edge" === location) {
            return this._getEdgeTooltipParams();
        }
        const center = this.getCenterCoord();
        center.offset = 0;
        return center;
    },
    getCenterCoord () {
        const { width: width, height: height, x: x, y: y } = this;
        return {
            x: x + width / 2,
            y: y + height / 2
        };
    },
    _truncateCoord: function(coord, bounds) {
        if (null === coord) {
            return coord;
        }
        if (coord < bounds[0]) {
            return bounds[0];
        }
        if (coord > bounds[1]) {
            return bounds[1];
        }
        return coord;
    },
    _getErrorBarBaseEdgeLength () {
        return this._options.rotated ? this.height : this.width;
    },
    _translateErrorBars: function(argVisibleArea) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._translateErrorBars.call(this);
        if (this._errorBarPos < argVisibleArea[0] || this._errorBarPos > argVisibleArea[1]) {
            this._errorBarPos = void 0;
        }
    },
    _translate: function() {
        const that = this;
        const rotated = that._options.rotated;
        const valAxis = rotated ? "x" : "y";
        const argAxis = rotated ? "y" : "x";
        const valIntervalName = rotated ? "width" : "height";
        const argIntervalName = rotated ? "height" : "width";
        const argTranslator = that._getArgTranslator();
        const valTranslator = that._getValTranslator();
        const argVisibleArea = that.series.getArgumentAxis().getVisibleArea();
        const valVisibleArea = that.series.getValueAxis().getVisibleArea();
        let arg = argTranslator.translate(that.argument);
        let val = valTranslator.translate(that.value, 1);
        let minVal = valTranslator.translate(that.minValue, -1);
        that[argAxis] = arg = null === arg ? arg : arg + (that[argAxis + "Correction"] || 0);
        that["v" + valAxis] = val;
        that["v" + argAxis] = arg + that[argIntervalName] / 2;
        this._cacheVisibility(arg, val, minVal, rotated);
        val = that._truncateCoord(val, valVisibleArea);
        minVal = that._truncateCoord(minVal, valVisibleArea);
        that[valIntervalName] = _abs(val - minVal);
        val = val < minVal ? val : minVal;
        that[valAxis] = null === val ? val : val + (that[valAxis + "Correction"] || 0);
        that["min" + valAxis.toUpperCase()] = null === minVal ? minVal : minVal + (that[valAxis + "Correction"] || 0);
        that["default" + valAxis.toUpperCase()] = valTranslator.translate(CANVAS_POSITION_DEFAULT);
        that._translateErrorBars(argVisibleArea);
        if (that.inVisibleArea && null !== that[argAxis]) {
            if (that[argAxis] < argVisibleArea[0]) {
                that[argIntervalName] = that[argIntervalName] - (argVisibleArea[0] - that[argAxis]);
                that[argAxis] = argVisibleArea[0];
            }
            if (that[argAxis] + that[argIntervalName] > argVisibleArea[1]) {
                that[argIntervalName] = argVisibleArea[1] - that[argAxis];
            }
        }
    },
    _updateMarker: function(animationEnabled, style) {
        this.graphic.smartAttr(_extend({}, style, !animationEnabled ? this.getMarkerCoords() : {}));
    },
    getMarkerCoords: function() {
        const that = this;
        let x = that.x;
        const y = that.y;
        let width = that.width;
        let height = that.height;
        const argAxis = that.series.getArgumentAxis();
        const rotated = that._options.rotated;
        if (argAxis.getAxisPosition) {
            const axisOptions = argAxis.getOptions();
            const edgeOffset = Math.round(axisOptions.width / 2);
            const argAxisPosition = argAxis.getAxisPosition();
            if (axisOptions.visible) {
                if (!rotated) {
                    height -= that.minY === that.defaultY && that.minY === argAxisPosition - argAxis.getAxisShift() ? edgeOffset : 0;
                    height < 0 && (height = 0);
                } else {
                    const isStartFromAxis = that.minX === that.defaultX && that.minX === argAxisPosition - argAxis.getAxisShift();
                    x += isStartFromAxis ? edgeOffset : 0;
                    width -= isStartFromAxis ? edgeOffset : 0;
                    width < 0 && (width = 0);
                }
            }
        }
        return {
            x: x,
            y: y,
            width: width,
            height: height
        };
    },
    coordsIn: function(x, y) {
        return x >= this.x && x <= this.x + this.width && y >= this.y && y <= this.y + this.height;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/bubble_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/bubble_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const MIN_BUBBLE_HEIGHT = 20;
const __TURBOPACK__default__export__ = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    correctCoordinates: function(diameter) {
        this.bubbleSize = diameter / 2;
    },
    _drawMarker: function(renderer, group, animationEnabled) {
        const attr = _extend({
            translateX: this.x,
            translateY: this.y
        }, this._getStyle());
        this.graphic = renderer.circle(0, 0, animationEnabled ? 0 : this.bubbleSize).smartAttr(attr).data({
            "chart-data-point": this
        }).append(group);
    },
    getTooltipParams: function(location) {
        const graphic = this.graphic;
        if (!graphic) {
            return;
        }
        const height = graphic.getBBox().height;
        return {
            x: this.x,
            y: this.y,
            offset: height < 20 || "edge" === location ? height / 2 : 0
        };
    },
    _getLabelFormatObject: function() {
        const formatObject = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelFormatObject.call(this);
        formatObject.size = this.initialSize;
        return formatObject;
    },
    _updateData: function(data) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._updateData.call(this, data);
        this.size = this.initialSize = data.size;
    },
    _getGraphicBBox: function() {
        return this._getSymbolBBox(this.x, this.y, this.bubbleSize);
    },
    _updateMarker: function(animationEnabled, style) {
        const that = this;
        if (!animationEnabled) {
            style = _extend({
                r: that.bubbleSize,
                translateX: that.x,
                translateY: that.y
            }, style);
        }
        that.graphic.smartAttr(style);
    },
    _getFormatObject: function(tooltip) {
        const formatObject = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getFormatObject.call(this, tooltip);
        formatObject.sizeText = tooltip.formatValue(this.initialSize);
        return formatObject;
    },
    _storeTrackerR: function() {
        return this.bubbleSize;
    },
    _getLabelCoords: function(label) {
        let coords;
        if ("inside" === label.getLayoutOptions().position) {
            coords = this._getLabelCoordOfPosition(label, "inside");
        } else {
            coords = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelCoords.call(this, label);
        }
        return coords;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/pie_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/pie_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/consts.js [app-client] (ecmascript)");
;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const _round = Math.round;
const _sqrt = Math.sqrt;
const _acos = Math.acos;
const DEG = 180 / Math.PI;
const _abs = Math.abs;
;
;
;
const RADIAL_LABEL_INDENT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].radialLabelIndent;
const __TURBOPACK__default__export__ = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    _updateData: function(data, argumentChanged) {
        const that = this;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._updateData.call(this, data);
        if (argumentChanged || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(that._visible)) {
            that._visible = true;
        }
        that.minValue = that.initialMinValue = that.originalMinValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(data.minValue) ? data.minValue : 0;
    },
    animate: function(complete, duration, delay) {
        this.graphic.animate({
            x: this.centerX,
            y: this.centerY,
            outerRadius: this.radiusOuter,
            innerRadius: this.radiusInner,
            startAngle: this.toAngle,
            endAngle: this.fromAngle
        }, {
            delay: delay,
            partitionDuration: duration
        }, complete);
    },
    correctPosition: function(correction) {
        this.correctRadius(correction);
        this.correctLabelRadius(correction.radiusOuter + RADIAL_LABEL_INDENT);
        this.centerX = correction.centerX;
        this.centerY = correction.centerY;
    },
    correctRadius: function(correction) {
        this.radiusInner = correction.radiusInner;
        this.radiusOuter = correction.radiusOuter;
    },
    correctLabelRadius: function(radiusLabels) {
        this.radiusLabels = radiusLabels;
    },
    correctValue: function(correction, percent, base) {
        this.value = (base || this.normalInitialValue) + correction;
        this.minValue = correction;
        this.percent = percent;
        this._label.setDataField("percent", percent);
    },
    _updateLabelData: function() {
        this._label.setData(this._getLabelFormatObject());
    },
    _getShiftLabelCoords: function() {
        const that = this;
        const bBox = that._label.getBoundingRect();
        const coord = that._getLabelCoords(that._label);
        const visibleArea = that._getVisibleArea();
        if (that._isLabelDrawingWithoutPoints) {
            return that._checkLabelPosition(coord, bBox, visibleArea);
        } else {
            return that._getLabelExtraCoord(coord, that._checkVerticalLabelPosition(coord, bBox, visibleArea), bBox);
        }
    },
    _getLabelPosition: function(options) {
        return options.position;
    },
    getAnnotationCoords: function(location) {
        return this._getElementCoords("edge" !== location ? "inside" : "outside", this.radiusOuter, 0);
    },
    _getElementCoords: function(position, elementRadius, radialOffset) {
        let bBox = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : {
            x: 0,
            y: 0,
            width: 0,
            height: 0
        };
        const that = this;
        const angleFunctions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(that.middleAngle);
        const radiusInner = that.radiusInner;
        const radiusOuter = that.radiusOuter;
        const columnsPosition = "columns" === position;
        let rad;
        let x;
        if ("inside" === position) {
            rad = radiusInner + (radiusOuter - radiusInner) / 2 + radialOffset;
            x = that.centerX + rad * angleFunctions.cos - bBox.width / 2;
        } else {
            rad = elementRadius + radialOffset;
            if (angleFunctions.cos > .1 || columnsPosition && angleFunctions.cos >= 0) {
                x = that.centerX + rad * angleFunctions.cos;
            } else if (angleFunctions.cos < -.1 || columnsPosition && angleFunctions.cos < 0) {
                x = that.centerX + rad * angleFunctions.cos - bBox.width;
            } else {
                x = that.centerX + rad * angleFunctions.cos - bBox.width / 2;
            }
        }
        return {
            x: x,
            y: _round(that.centerY - rad * angleFunctions.sin - bBox.height / 2)
        };
    },
    _getLabelCoords: function(label) {
        const bBox = label.getBoundingRect();
        const options = label.getLayoutOptions();
        const position = this._getLabelPosition(options);
        return this._getElementCoords(position, this.radiusLabels, options.radialOffset, bBox);
    },
    _correctLabelCoord: function(coord, moveLabelsFromCenter) {
        const label = this._label;
        const bBox = label.getBoundingRect();
        const labelWidth = bBox.width;
        const options = label.getLayoutOptions();
        const visibleArea = this._getVisibleArea();
        const rightBorderX = visibleArea.maxX - labelWidth;
        const leftBorderX = visibleArea.minX;
        const angleOfPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(this.middleAngle);
        const centerX = this.centerX;
        const connectorOffset = options.connectorOffset;
        let x = coord.x;
        if ("columns" === options.position) {
            if (angleOfPoint <= 90 || angleOfPoint >= 270) {
                x = rightBorderX;
            } else {
                x = leftBorderX;
            }
            coord.x = x;
        } else if ("inside" !== options.position && moveLabelsFromCenter) {
            if (angleOfPoint <= 90 || angleOfPoint >= 270) {
                if (x - connectorOffset < centerX) {
                    x = centerX + connectorOffset;
                }
            } else if (x + labelWidth + connectorOffset > centerX) {
                x = centerX - labelWidth - connectorOffset;
            }
            coord.x = x;
        }
        return coord;
    },
    drawLabel: function() {
        this.translate();
        this._isLabelDrawingWithoutPoints = true;
        this._drawLabel();
        this._isLabelDrawingWithoutPoints = false;
    },
    updateLabelCoord: function(moveLabelsFromCenter) {
        const bBox = this._label.getBoundingRect();
        let coord = this._correctLabelCoord(bBox, moveLabelsFromCenter);
        coord = this._checkHorizontalLabelPosition(coord, bBox, this._getVisibleArea());
        this._label.shift(_round(coord.x), _round(bBox.y));
    },
    _checkVerticalLabelPosition: function(coord, box, visibleArea) {
        const x = coord.x;
        let y = coord.y;
        if (coord.y + box.height > visibleArea.maxY) {
            y = visibleArea.maxY - box.height;
        } else if (coord.y < visibleArea.minY) {
            y = visibleArea.minY;
        }
        return {
            x: x,
            y: y
        };
    },
    _getLabelExtraCoord: function(coord, shiftCoord, box) {
        return coord.y !== shiftCoord.y ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getVerticallyShiftedAngularCoords"])({
            x: coord.x,
            y: coord.y,
            width: box.width,
            height: box.height
        }, shiftCoord.y - coord.y, {
            x: this.centerX,
            y: this.centerY
        }) : coord;
    },
    _checkHorizontalLabelPosition: function(coord, box, visibleArea) {
        let x = coord.x;
        const y = coord.y;
        if (coord.x + box.width > visibleArea.maxX) {
            x = visibleArea.maxX - box.width;
        } else if (coord.x < visibleArea.minX) {
            x = visibleArea.minX;
        }
        return {
            x: x,
            y: y
        };
    },
    applyWordWrap: function(moveLabelsFromCenter) {
        const that = this;
        const label = that._label;
        const box = label.getBoundingRect();
        const visibleArea = that._getVisibleArea();
        const position = label.getLayoutOptions().position;
        let width = box.width;
        let rowCountChanged = false;
        if ("columns" === position && that.series.index > 0) {
            width = visibleArea.maxX - that.centerX - that.radiusLabels;
        } else if ("inside" === position) {
            if (width > visibleArea.maxX - visibleArea.minX) {
                width = visibleArea.maxX - visibleArea.minX;
            }
        } else if (moveLabelsFromCenter && box.x < that.centerX && box.width + box.x > that.centerX) {
            width = Math.floor((visibleArea.maxX - visibleArea.minX) / 2);
        } else if (box.x + width > visibleArea.maxX) {
            width = visibleArea.maxX - box.x;
        } else if (box.x < visibleArea.minX) {
            width = box.x + width - visibleArea.minX;
        }
        if (width < box.width) {
            rowCountChanged = label.fit(width);
        }
        return rowCountChanged;
    },
    setLabelTrackerData: function() {
        this._label.setTrackerData(this);
    },
    _checkLabelPosition: function(coord, bBox, visibleArea) {
        coord = this._checkHorizontalLabelPosition(coord, bBox, visibleArea);
        return this._checkVerticalLabelPosition(coord, bBox, visibleArea);
    },
    _getLabelConnector: function() {
        const rad = this.radiusOuter;
        const seriesStyle = this._options.styles.normal;
        const strokeWidthBy2 = seriesStyle["stroke-width"] / 2;
        const borderWidth = this.series.getOptions().containerBackgroundColor === seriesStyle.stroke ? _round(strokeWidthBy2) : _round(-strokeWidthBy2);
        const angleFunctions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(_round(this.middleAngle));
        return {
            x: _round(this.centerX + (rad - borderWidth) * angleFunctions.cos),
            y: _round(this.centerY - (rad - borderWidth) * angleFunctions.sin),
            angle: this.middleAngle
        };
    },
    _drawMarker: function(renderer, group, animationEnabled, firstDrawing) {
        const that = this;
        let radiusOuter = that.radiusOuter;
        let radiusInner = that.radiusInner;
        let fromAngle = that.fromAngle;
        let toAngle = that.toAngle;
        if (animationEnabled) {
            radiusInner = radiusOuter = 0;
            if (!firstDrawing) {
                fromAngle = toAngle = that.shiftedAngle;
            }
        }
        that.graphic = renderer.arc(that.centerX, that.centerY, radiusInner, radiusOuter, toAngle, fromAngle).attr({
            "stroke-linejoin": "round"
        }).smartAttr(that._getStyle()).data({
            "chart-data-point": that
        }).sharp().append(group);
    },
    getTooltipParams: function() {
        const angleFunctions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(this.middleAngle);
        const radiusInner = this.radiusInner;
        const radiusOuter = this.radiusOuter;
        return {
            x: this.centerX + (radiusInner + (radiusOuter - radiusInner) / 2) * angleFunctions.cos,
            y: this.centerY - (radiusInner + (radiusOuter - radiusInner) / 2) * angleFunctions.sin,
            offset: 0
        };
    },
    _translate: function() {
        const that = this;
        const angle = that.shiftedAngle || 0;
        const value = that.value;
        const minValue = that.minValue;
        const translator = that._getValTranslator();
        that.fromAngle = translator.translate(minValue) + angle;
        that.toAngle = translator.translate(value) + angle;
        that.middleAngle = translator.translate((value - minValue) / 2 + minValue) + angle;
        if (!that.isVisible()) {
            that.middleAngle = that.toAngle = that.fromAngle = that.fromAngle || angle;
        }
    },
    getMarkerVisibility: function() {
        return true;
    },
    _updateMarker: function(animationEnabled, style, _, callback) {
        const that = this;
        if (!animationEnabled) {
            style = _extend({
                x: that.centerX,
                y: that.centerY,
                outerRadius: that.radiusOuter,
                innerRadius: that.radiusInner,
                startAngle: that.toAngle,
                endAngle: that.fromAngle
            }, style);
        }
        that.graphic.smartAttr(style).sharp();
        callback && callback();
    },
    getLegendStyles: function() {
        return this._styles.legendStyles;
    },
    isInVisibleArea: function() {
        return true;
    },
    hide: function() {
        const that = this;
        if (that._visible) {
            that._visible = false;
            that.hideTooltip();
            that._options.visibilityChanged();
        }
    },
    show: function() {
        const that = this;
        if (!that._visible) {
            that._visible = true;
            that._options.visibilityChanged();
        }
    },
    setInvisibility: function() {
        this._label.draw(false);
    },
    isVisible: function() {
        return this._visible;
    },
    _getFormatObject: function(tooltip) {
        const formatObject = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getFormatObject.call(this, tooltip);
        const percent = this.percent;
        formatObject.percent = percent;
        formatObject.percentText = tooltip.formatValue(percent, "percent");
        return formatObject;
    },
    getColor: function() {
        return this._styles.normal.fill;
    },
    coordsIn: function(x, y) {
        const lx = x - this.centerX;
        const ly = y - this.centerY;
        const r = _sqrt(lx * lx + ly * ly);
        const fromAngle = this.fromAngle % 360;
        const toAngle = this.toAngle % 360;
        let angle;
        if (r < this.radiusInner || r > this.radiusOuter || 0 === r) {
            return false;
        }
        angle = _acos(lx / r) * DEG * (ly > 0 ? -1 : 1);
        if (angle < 0) {
            angle += 360;
        }
        if (fromAngle === toAngle && _abs(this.toAngle - this.fromAngle) > 1e-4) {
            return true;
        } else {
            return fromAngle >= toAngle ? angle <= fromAngle && angle >= toAngle : !(angle >= fromAngle && angle <= toAngle);
        }
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/range_symbol_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/range_symbol_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/label.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
;
;
;
;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
;
const _math = Math;
const _abs = _math.abs;
const _min = _math.min;
const _max = _math.max;
const _round = _math.round;
const DEFAULT_IMAGE_WIDTH = 20;
const DEFAULT_IMAGE_HEIGHT = 20;
const __TURBOPACK__default__export__ = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    deleteLabel: function() {
        this._topLabel.dispose();
        this._topLabel = null;
        this._bottomLabel.dispose();
        this._bottomLabel = null;
    },
    hideMarker: function(type) {
        const graphic = this.graphic;
        const marker = graphic && graphic[type + "Marker"];
        const label = this["_" + type + "Label"];
        if (marker && "hidden" !== marker.attr("visibility")) {
            marker.attr({
                visibility: "hidden"
            });
        }
        label.draw(false);
    },
    setInvisibility: function() {
        this.hideMarker("top");
        this.hideMarker("bottom");
    },
    clearVisibility: function() {
        const graphic = this.graphic;
        const topMarker = graphic && graphic.topMarker;
        const bottomMarker = graphic && graphic.bottomMarker;
        if (topMarker && topMarker.attr("visibility")) {
            topMarker.attr({
                visibility: null
            });
        }
        if (bottomMarker && bottomMarker.attr("visibility")) {
            bottomMarker.attr({
                visibility: null
            });
        }
    },
    clearMarker: function() {
        const graphic = this.graphic;
        const topMarker = graphic && graphic.topMarker;
        const bottomMarker = graphic && graphic.bottomMarker;
        const emptySettings = this._emptySettings;
        topMarker && topMarker.attr(emptySettings);
        bottomMarker && bottomMarker.attr(emptySettings);
    },
    _getLabelPosition: function(markerType) {
        let position;
        const labelsInside = "inside" === this._options.label.position;
        if (!this._options.rotated) {
            position = "top" === markerType ^ labelsInside ? "top" : "bottom";
        } else {
            position = "top" === markerType ^ labelsInside ? "right" : "left";
        }
        return position;
    },
    _getLabelMinFormatObject: function() {
        return {
            index: 0,
            argument: this.initialArgument,
            value: this.initialMinValue,
            seriesName: this.series.name,
            originalValue: this.originalMinValue,
            originalArgument: this.originalArgument,
            point: this
        };
    },
    _updateLabelData: function() {
        const maxFormatObject = this._getLabelFormatObject();
        maxFormatObject.index = 1;
        this._topLabel.setData(maxFormatObject);
        this._bottomLabel.setData(this._getLabelMinFormatObject());
    },
    _updateLabelOptions: function() {
        const options = this._options.label;
        (!this._topLabel || !this._bottomLabel) && this._createLabel();
        this._topLabel.setOptions(options);
        this._bottomLabel.setOptions(options);
    },
    _createLabel: function() {
        const options = {
            renderer: this.series._renderer,
            labelsGroup: this.series._labelsGroup,
            point: this
        };
        this._topLabel = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"](options);
        this._bottomLabel = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$label$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Label"](options);
    },
    _getGraphicBBox: function(location) {
        const options = this._options;
        const images = this._getImage(options.image);
        const image = "top" === location ? this._checkImage(images.top) : this._checkImage(images.bottom);
        let bBox;
        const coord = this._getPositionFromLocation(location);
        if (options.visible) {
            bBox = image ? this._getImageBBox(coord.x, coord.y) : this._getSymbolBBox(coord.x, coord.y, options.styles.normal.r);
        } else {
            bBox = {
                x: coord.x,
                y: coord.y,
                width: 0,
                height: 0
            };
        }
        return bBox;
    },
    _getPositionFromLocation: function(location) {
        let x;
        let y;
        const isTop = "top" === location;
        if (!this._options.rotated) {
            x = this.x;
            y = isTop ? _min(this.y, this.minY) : _max(this.y, this.minY);
        } else {
            x = isTop ? _max(this.x, this.minX) : _min(this.x, this.minX);
            y = this.y;
        }
        return {
            x: x,
            y: y
        };
    },
    _checkOverlay: function(bottomCoord, topCoord, topValue) {
        return bottomCoord < topCoord + topValue;
    },
    _getOverlayCorrections: function(topCoords, bottomCoords) {
        const rotated = this._options.rotated;
        const coordSelector = !rotated ? "y" : "x";
        const valueSelector = !rotated ? "height" : "width";
        const visibleArea = this.series.getValueAxis().getVisibleArea();
        const minBound = visibleArea[0];
        const maxBound = visibleArea[1];
        let delta = _round((topCoords[coordSelector] + topCoords[valueSelector] - bottomCoords[coordSelector]) / 2);
        let coord1 = topCoords[coordSelector] - delta;
        let coord2 = bottomCoords[coordSelector] + delta;
        if (coord1 < minBound) {
            delta = minBound - coord1;
            coord1 += delta;
            coord2 += delta;
        } else if (coord2 + bottomCoords[valueSelector] > maxBound) {
            delta = maxBound - coord2 - bottomCoords[valueSelector];
            coord1 += delta;
            coord2 += delta;
        }
        return {
            coord1: coord1,
            coord2: coord2
        };
    },
    _checkLabelsOverlay: function(topLocation) {
        const that = this;
        const topCoords = that._topLabel.getBoundingRect();
        const bottomCoords = that._bottomLabel.getBoundingRect();
        let corrections = {};
        if (!that._options.rotated) {
            if ("top" === topLocation) {
                if (this._checkOverlay(bottomCoords.y, topCoords.y, topCoords.height)) {
                    corrections = this._getOverlayCorrections(topCoords, bottomCoords);
                    that._topLabel.shift(topCoords.x, corrections.coord1);
                    that._bottomLabel.shift(bottomCoords.x, corrections.coord2);
                }
            } else if (this._checkOverlay(topCoords.y, bottomCoords.y, bottomCoords.height)) {
                corrections = this._getOverlayCorrections(bottomCoords, topCoords);
                that._topLabel.shift(topCoords.x, corrections.coord2);
                that._bottomLabel.shift(bottomCoords.x, corrections.coord1);
            }
        } else if ("top" === topLocation) {
            if (this._checkOverlay(topCoords.x, bottomCoords.x, bottomCoords.width)) {
                corrections = this._getOverlayCorrections(bottomCoords, topCoords);
                that._topLabel.shift(corrections.coord2, topCoords.y);
                that._bottomLabel.shift(corrections.coord1, bottomCoords.y);
            }
        } else if (this._checkOverlay(bottomCoords.x, topCoords.x, topCoords.width)) {
            corrections = this._getOverlayCorrections(topCoords, bottomCoords);
            that._topLabel.shift(corrections.coord1, topCoords.y);
            that._bottomLabel.shift(corrections.coord2, bottomCoords.y);
        }
    },
    _drawLabel: function() {
        const that = this;
        const labels = [];
        const notInverted = that._options.rotated ? that.x >= that.minX : that.y < that.minY;
        const customVisibility = that._getCustomLabelVisibility();
        const topLabel = that._topLabel;
        const bottomLabel = that._bottomLabel;
        topLabel.pointPosition = notInverted ? "top" : "bottom";
        bottomLabel.pointPosition = notInverted ? "bottom" : "top";
        if ((that.series.getLabelVisibility() || customVisibility) && that.hasValue() && false !== customVisibility) {
            false !== that.visibleTopMarker && labels.push(topLabel);
            false !== that.visibleBottomMarker && labels.push(bottomLabel);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(labels, function(_, label) {
                label.draw(true);
            });
            that._checkLabelsOverlay(that._topLabel.pointPosition);
        } else {
            topLabel.draw(false);
            bottomLabel.draw(false);
        }
    },
    _getImage: function(imageOption) {
        const image = {};
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(imageOption)) {
            if ("string" === typeof imageOption) {
                image.top = image.bottom = imageOption;
            } else {
                image.top = {
                    url: "string" === typeof imageOption.url ? imageOption.url : imageOption.url && imageOption.url.rangeMaxPoint,
                    width: "number" === typeof imageOption.width ? imageOption.width : imageOption.width && imageOption.width.rangeMaxPoint,
                    height: "number" === typeof imageOption.height ? imageOption.height : imageOption.height && imageOption.height.rangeMaxPoint
                };
                image.bottom = {
                    url: "string" === typeof imageOption.url ? imageOption.url : imageOption.url && imageOption.url.rangeMinPoint,
                    width: "number" === typeof imageOption.width ? imageOption.width : imageOption.width && imageOption.width.rangeMinPoint,
                    height: "number" === typeof imageOption.height ? imageOption.height : imageOption.height && imageOption.height.rangeMinPoint
                };
            }
        }
        return image;
    },
    _checkSymbol: function(oldOptions, newOptions) {
        const oldSymbol = oldOptions.symbol;
        const newSymbol = newOptions.symbol;
        const symbolChanged = "circle" === oldSymbol && "circle" !== newSymbol || "circle" !== oldSymbol && "circle" === newSymbol;
        const oldImages = this._getImage(oldOptions.image);
        const newImages = this._getImage(newOptions.image);
        const topImageChanged = this._checkImage(oldImages.top) !== this._checkImage(newImages.top);
        const bottomImageChanged = this._checkImage(oldImages.bottom) !== this._checkImage(newImages.bottom);
        return symbolChanged || topImageChanged || bottomImageChanged;
    },
    _getSettingsForTwoMarkers: function(style) {
        const options = this._options;
        const settings = {};
        const x = options.rotated ? _min(this.x, this.minX) : this.x;
        const y = options.rotated ? this.y : _min(this.y, this.minY);
        const radius = style.r;
        const points = this._populatePointShape(options.symbol, radius);
        settings.top = _extend({
            translateX: x + this.width,
            translateY: y,
            r: radius
        }, style);
        settings.bottom = _extend({
            translateX: x,
            translateY: y + this.height,
            r: radius
        }, style);
        if (points) {
            settings.top.points = settings.bottom.points = points;
        }
        return settings;
    },
    _hasGraphic: function() {
        return this.graphic && this.graphic.topMarker && this.graphic.bottomMarker;
    },
    _drawOneMarker: function(renderer, markerType, imageSettings, settings) {
        const that = this;
        const graphic = that.graphic;
        if (graphic[markerType]) {
            that._updateOneMarker(markerType, settings);
        } else {
            graphic[markerType] = that._createMarker(renderer, graphic, imageSettings, settings);
        }
    },
    _drawMarker: function(renderer, group, animationEnabled, firstDrawing, style) {
        const that = this;
        const settings = that._getSettingsForTwoMarkers(style || that._getStyle());
        const image = that._getImage(that._options.image);
        if (that._checkImage(image.top)) {
            settings.top = that._getImageSettings(settings.top, image.top);
        }
        if (that._checkImage(image.bottom)) {
            settings.bottom = that._getImageSettings(settings.bottom, image.bottom);
        }
        that.graphic = that.graphic || renderer.g().append(group);
        that.visibleTopMarker && that._drawOneMarker(renderer, "topMarker", image.top, settings.top);
        that.visibleBottomMarker && that._drawOneMarker(renderer, "bottomMarker", image.bottom, settings.bottom);
    },
    _getSettingsForTracker: function(radius) {
        const rotated = this._options.rotated;
        return {
            translateX: rotated ? _min(this.x, this.minX) - radius : this.x - radius,
            translateY: rotated ? this.y - radius : _min(this.y, this.minY) - radius,
            width: this.width + 2 * radius,
            height: this.height + 2 * radius
        };
    },
    isInVisibleArea: function() {
        const rotated = this._options.rotated;
        const argument = !rotated ? this.x : this.y;
        const maxValue = !rotated ? _max(this.minY, this.y) : _max(this.minX, this.x);
        const minValue = !rotated ? _min(this.minY, this.y) : _min(this.minX, this.x);
        let tmp;
        let visibleTopMarker;
        let visibleBottomMarker;
        let visibleRangeArea = true;
        const visibleArgArea = this.series.getArgumentAxis().getVisibleArea();
        const visibleValArea = this.series.getValueAxis().getVisibleArea();
        const notVisibleByArg = visibleArgArea[1] < argument || visibleArgArea[0] > argument;
        const notVisibleByVal = visibleValArea[0] > minValue && visibleValArea[0] > maxValue || visibleValArea[1] < minValue && visibleValArea[1] < maxValue;
        if (notVisibleByArg || notVisibleByVal) {
            visibleTopMarker = visibleBottomMarker = visibleRangeArea = false;
        } else {
            visibleTopMarker = visibleValArea[0] <= minValue && visibleValArea[1] > minValue;
            visibleBottomMarker = visibleValArea[0] < maxValue && visibleValArea[1] >= maxValue;
            if (rotated) {
                tmp = visibleTopMarker;
                visibleTopMarker = visibleBottomMarker;
                visibleBottomMarker = tmp;
            }
        }
        this.visibleTopMarker = visibleTopMarker;
        this.visibleBottomMarker = visibleBottomMarker;
        return visibleRangeArea;
    },
    getTooltipParams: function() {
        const that = this;
        let x;
        let y;
        const rotated = that._options.rotated;
        const minValue = !rotated ? _min(that.y, that.minY) : _min(that.x, that.minX);
        const side = !rotated ? "height" : "width";
        const visibleArea = that._getVisibleArea();
        const minVisible = rotated ? visibleArea.minX : visibleArea.minY;
        const maxVisible = rotated ? visibleArea.maxX : visibleArea.maxY;
        const min = _max(minVisible, minValue);
        const max = _min(maxVisible, minValue + that[side]);
        if (!rotated) {
            x = that.x;
            y = min + (max - min) / 2;
        } else {
            y = that.y;
            x = min + (max - min) / 2;
        }
        return {
            x: x,
            y: y,
            offset: 0
        };
    },
    _translate: function() {
        const rotated = this._options.rotated;
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._translate.call(this);
        this.height = rotated ? 0 : _abs(this.minY - this.y);
        this.width = rotated ? _abs(this.x - this.minX) : 0;
    },
    hasCoords: function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].hasCoords.call(this) && !(null === this.minX || null === this.minY);
    },
    _updateData: function(data) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._updateData.call(this, data);
        this.minValue = this.initialMinValue = this.originalMinValue = data.minValue;
    },
    _getImageSettings: function(settings, image) {
        return {
            href: image.url || image.toString(),
            width: image.width || 20,
            height: image.height || 20,
            translateX: settings.translateX,
            translateY: settings.translateY
        };
    },
    getCrosshairData: function(x, y) {
        const rotated = this._options.rotated;
        const minX = this.minX;
        const minY = this.minY;
        const vx = this.vx;
        const vy = this.vy;
        const value = this.value;
        const minValue = this.minValue;
        const argument = this.argument;
        const coords = {
            axis: this.series.axis,
            x: vx,
            y: vy,
            yValue: value,
            xValue: argument
        };
        if (rotated) {
            coords.yValue = argument;
            if (_abs(vx - x) < _abs(minX - x)) {
                coords.xValue = value;
            } else {
                coords.x = minX;
                coords.xValue = minValue;
            }
        } else if (_abs(vy - y) >= _abs(minY - y)) {
            coords.y = minY;
            coords.yValue = minValue;
        }
        return coords;
    },
    _updateOneMarker: function(markerType, settings) {
        this.graphic && this.graphic[markerType] && this.graphic[markerType].attr(settings);
    },
    _updateMarker: function(animationEnabled, style) {
        this._drawMarker(void 0, void 0, false, false, style);
    },
    _getFormatObject: function(tooltip) {
        const initialMinValue = this.initialMinValue;
        const initialValue = this.initialValue;
        const initialArgument = this.initialArgument;
        const minValue = tooltip.formatValue(initialMinValue);
        const value = tooltip.formatValue(initialValue);
        return {
            argument: initialArgument,
            argumentText: tooltip.formatValue(initialArgument, "argument"),
            valueText: minValue + " - " + value,
            rangeValue1Text: minValue,
            rangeValue2Text: value,
            rangeValue1: initialMinValue,
            rangeValue2: initialValue,
            seriesName: this.series.name,
            point: this,
            originalMinValue: this.originalMinValue,
            originalValue: this.originalValue,
            originalArgument: this.originalArgument
        };
    },
    getLabel: function() {
        return [
            this._topLabel,
            this._bottomLabel
        ];
    },
    getLabels: function() {
        return [
            this._topLabel,
            this._bottomLabel
        ];
    },
    getBoundingRect: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    coordsIn: function(x, y) {
        const trackerRadius = this._storeTrackerR();
        const xCond = x >= this.x - trackerRadius && x <= this.x + trackerRadius;
        const yCond = y >= this.y - trackerRadius && y <= this.y + trackerRadius;
        if (this._options.rotated) {
            return yCond && (xCond || x >= this.minX - trackerRadius && x <= this.minX + trackerRadius);
        } else {
            return xCond && (yCond || y >= this.minY - trackerRadius && y <= this.minY + trackerRadius);
        }
    },
    getMaxValue: function() {
        if ("discrete" !== this.series.valueAxisType) {
            return this.minValue > this.value ? this.minValue : this.value;
        }
        return this.value;
    },
    getMinValue: function() {
        if ("discrete" !== this.series.valueAxisType) {
            return this.minValue < this.value ? this.minValue : this.value;
        }
        return this.minValue;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/range_bar_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/range_bar_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/bar_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/range_symbol_point.js [app-client] (ecmascript)");
;
;
;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const __TURBOPACK__default__export__ = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    deleteLabel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].deleteLabel,
    _getFormatObject: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getFormatObject,
    clearVisibility: function() {
        const graphic = this.graphic;
        if (graphic && graphic.attr("visibility")) {
            graphic.attr({
                visibility: null
            });
        }
    },
    setInvisibility: function() {
        const graphic = this.graphic;
        if (graphic && "hidden" !== graphic.attr("visibility")) {
            graphic.attr({
                visibility: "hidden"
            });
        }
        this._topLabel.draw(false);
        this._bottomLabel.draw(false);
    },
    getTooltipParams: function(location) {
        const that = this;
        const edgeLocation = "edge" === location;
        let x;
        let y;
        if (that._options.rotated) {
            x = edgeLocation ? that.x + that.width : that.x + that.width / 2;
            y = that.y + that.height / 2;
        } else {
            x = that.x + that.width / 2;
            y = edgeLocation ? that.y : that.y + that.height / 2;
        }
        return {
            x: x,
            y: y,
            offset: 0
        };
    },
    _translate: function() {
        const that = this;
        const barMethods = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        barMethods._translate.call(that);
        if (that._options.rotated) {
            that.width = that.width || 1;
        } else {
            that.height = that.height || 1;
        }
    },
    hasCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].hasCoords,
    _updateData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._updateData,
    _getLabelPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelPosition,
    _getLabelMinFormatObject: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelMinFormatObject,
    _updateLabelData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._updateLabelData,
    _updateLabelOptions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._updateLabelOptions,
    getCrosshairData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getCrosshairData,
    _createLabel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._createLabel,
    _checkOverlay: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._checkOverlay,
    _checkLabelsOverlay: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._checkLabelsOverlay,
    _getOverlayCorrections: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getOverlayCorrections,
    _drawLabel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._drawLabel,
    _getLabelCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelCoords,
    getLabel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getLabel,
    getLabels: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getLabels,
    getBoundingRect: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getMinValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getMinValue,
    getMaxValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getMaxValue
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/candlestick_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/candlestick_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/bar_point.js [app-client] (ecmascript)");
;
;
;
const _math = Math;
const _abs = _math.abs;
const _min = _math.min;
const _max = _math.max;
const _round = _math.round;
const DEFAULT_FINANCIAL_TRACKER_MARGIN = 2;
const __TURBOPACK__default__export__ = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    _calculateVisibility: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._calculateVisibility,
    _getContinuousPoints: function(openCoord, closeCoord) {
        const that = this;
        const x = that.x;
        const createPoint = that._options.rotated ? function(x, y) {
            return [
                y,
                x
            ];
        } : function(x, y) {
            return [
                x,
                y
            ];
        };
        const width = that.width;
        const highCoord = that.highY;
        const max = _abs(highCoord - openCoord) < _abs(highCoord - closeCoord) ? openCoord : closeCoord;
        const min = max === closeCoord ? openCoord : closeCoord;
        let points;
        if (min === max) {
            points = [].concat(createPoint(x, that.highY)).concat(createPoint(x, that.lowY)).concat(createPoint(x, that.closeY)).concat(createPoint(x - width / 2, that.closeY)).concat(createPoint(x + width / 2, that.closeY)).concat(createPoint(x, that.closeY));
        } else {
            points = [].concat(createPoint(x, that.highY)).concat(createPoint(x, max)).concat(createPoint(x + width / 2, max)).concat(createPoint(x + width / 2, min)).concat(createPoint(x, min)).concat(createPoint(x, that.lowY)).concat(createPoint(x, min)).concat(createPoint(x - width / 2, min)).concat(createPoint(x - width / 2, max)).concat(createPoint(x, max));
        }
        return points;
    },
    _getCrockPoints: function(y) {
        const x = this.x;
        const createPoint = this._options.rotated ? function(x, y) {
            return [
                y,
                x
            ];
        } : function(x, y) {
            return [
                x,
                y
            ];
        };
        return [].concat(createPoint(x, this.highY)).concat(createPoint(x, this.lowY)).concat(createPoint(x, y)).concat(createPoint(x - this.width / 2, y)).concat(createPoint(x + this.width / 2, y)).concat(createPoint(x, y));
    },
    _getPoints: function() {
        const that = this;
        let points;
        const closeCoord = that.closeY;
        const openCoord = that.openY;
        if (null !== closeCoord && null !== openCoord) {
            points = that._getContinuousPoints(openCoord, closeCoord);
        } else if (openCoord === closeCoord) {
            points = [
                that.x,
                that.highY,
                that.x,
                that.lowY
            ];
        } else {
            points = that._getCrockPoints(null !== openCoord ? openCoord : closeCoord);
        }
        return points;
    },
    getColor: function() {
        return this._isReduction ? this._options.reduction.color : this._styles.normal.stroke || this.series.getColor();
    },
    _drawMarkerInGroup: function(group, attributes, renderer) {
        this.graphic = renderer.path(this._getPoints(), "area").attr({
            "stroke-linecap": "square"
        }).attr(attributes).data({
            "chart-data-point": this
        }).sharp().append(group);
    },
    _fillStyle: function() {
        const that = this;
        const styles = that._options.styles;
        if (that._isReduction && that._isPositive) {
            that._styles = styles.reductionPositive;
        } else if (that._isReduction) {
            that._styles = styles.reduction;
        } else if (that._isPositive) {
            that._styles = styles.positive;
        } else {
            that._styles = styles;
        }
    },
    _getMinTrackerWidth: function() {
        return 2 + 2 * this._styles.normal["stroke-width"];
    },
    correctCoordinates: function(correctOptions) {
        const minWidth = this._getMinTrackerWidth();
        let width = correctOptions.width;
        width = width < minWidth ? minWidth : width > 10 ? 10 : width;
        this.width = width + width % 2;
        this.xCorrection = correctOptions.offset;
    },
    _getMarkerGroup: function(group) {
        let markerGroup;
        if (this._isReduction && this._isPositive) {
            markerGroup = group.reductionPositiveMarkersGroup;
        } else if (this._isReduction) {
            markerGroup = group.reductionMarkersGroup;
        } else if (this._isPositive) {
            markerGroup = group.defaultPositiveMarkersGroup;
        } else {
            markerGroup = group.defaultMarkersGroup;
        }
        return markerGroup;
    },
    _drawMarker: function(renderer, group) {
        this._drawMarkerInGroup(this._getMarkerGroup(group), this._getStyle(), renderer);
    },
    _getSettingsForTracker: function() {
        const that = this;
        let highY = that.highY;
        let lowY = that.lowY;
        const rotated = that._options.rotated;
        let x;
        let y;
        let width;
        let height;
        if (highY === lowY) {
            highY = rotated ? highY + 2 : highY - 2;
            lowY = rotated ? lowY - 2 : lowY + 2;
        }
        if (rotated) {
            x = _min(lowY, highY);
            y = that.x - that.width / 2;
            width = _abs(lowY - highY);
            height = that.width;
        } else {
            x = that.x - that.width / 2;
            y = _min(lowY, highY);
            width = that.width;
            height = _abs(lowY - highY);
        }
        return {
            x: x,
            y: y,
            width: width,
            height: height
        };
    },
    _getGraphicBBox: function(location) {
        const that = this;
        const rotated = that._options.rotated;
        const x = that.x;
        const width = that.width;
        let lowY = that.lowY;
        let highY = that.highY;
        if (location) {
            const valVisibleArea = that.series.getValueAxis().getVisibleArea();
            highY = that._truncateCoord(highY, valVisibleArea);
            lowY = that._truncateCoord(lowY, valVisibleArea);
        }
        const bBox = {
            x: !rotated ? x - _round(width / 2) : lowY,
            y: !rotated ? highY : x - _round(width / 2),
            width: !rotated ? width : highY - lowY,
            height: !rotated ? lowY - highY : width
        };
        if (location) {
            const isTop = "top" === location;
            if (!this._options.rotated) {
                bBox.y = isTop ? bBox.y : bBox.y + bBox.height;
                bBox.height = 0;
            } else {
                bBox.x = isTop ? bBox.x + bBox.width : bBox.x;
                bBox.width = 0;
            }
        }
        return bBox;
    },
    getTooltipParams: function(location) {
        const that = this;
        if (that.graphic) {
            const minValue = _min(that.lowY, that.highY);
            const maxValue = _max(that.lowY, that.highY);
            const visibleArea = that._getVisibleArea();
            const rotated = that._options.rotated;
            const minVisible = rotated ? visibleArea.minX : visibleArea.minY;
            const maxVisible = rotated ? visibleArea.maxX : visibleArea.maxY;
            const min = _max(minVisible, minValue);
            const max = _min(maxVisible, maxValue);
            const centerCoord = that.getCenterCoord();
            if ("edge" === location) {
                centerCoord[rotated ? "x" : "y"] = rotated ? max : min;
            }
            centerCoord.offset = 0;
            return centerCoord;
        }
    },
    getCenterCoord () {
        if (this.graphic) {
            const that = this;
            let x;
            let y;
            const minValue = _min(that.lowY, that.highY);
            const maxValue = _max(that.lowY, that.highY);
            const visibleArea = that._getVisibleArea();
            const rotated = that._options.rotated;
            const minVisible = rotated ? visibleArea.minX : visibleArea.minY;
            const maxVisible = rotated ? visibleArea.maxX : visibleArea.maxY;
            const min = _max(minVisible, minValue);
            const max = _min(maxVisible, maxValue);
            const center = min + (max - min) / 2;
            if (rotated) {
                y = that.x;
                x = center;
            } else {
                x = that.x;
                y = center;
            }
            return {
                x: x,
                y: y
            };
        }
    },
    hasValue: function() {
        return null !== this.highValue && null !== this.lowValue;
    },
    hasCoords: function() {
        return null !== this.x && null !== this.lowY && null !== this.highY;
    },
    _translate: function() {
        const rotated = this._options.rotated;
        const valTranslator = this._getValTranslator();
        const x = this._getArgTranslator().translate(this.argument);
        this.vx = this.vy = this.x = null === x ? x : x + (this.xCorrection || 0);
        this.openY = null !== this.openValue ? valTranslator.translate(this.openValue) : null;
        this.highY = valTranslator.translate(this.highValue);
        this.lowY = valTranslator.translate(this.lowValue);
        this.closeY = null !== this.closeValue ? valTranslator.translate(this.closeValue) : null;
        const centerValue = _min(this.lowY, this.highY) + _abs(this.lowY - this.highY) / 2;
        this._calculateVisibility(!rotated ? this.x : centerValue, !rotated ? centerValue : this.x);
    },
    getCrosshairData: function(x, y) {
        const that = this;
        const rotated = that._options.rotated;
        const origY = rotated ? x : y;
        let yValue;
        const argument = that.argument;
        let coords;
        let coord = "low";
        if (_abs(that.lowY - origY) < _abs(that.closeY - origY)) {
            yValue = that.lowY;
        } else {
            yValue = that.closeY;
            coord = "close";
        }
        if (_abs(yValue - origY) >= _abs(that.openY - origY)) {
            yValue = that.openY;
            coord = "open";
        }
        if (_abs(yValue - origY) >= _abs(that.highY - origY)) {
            yValue = that.highY;
            coord = "high";
        }
        if (rotated) {
            coords = {
                y: that.vy,
                x: yValue,
                xValue: that[coord + "Value"],
                yValue: argument
            };
        } else {
            coords = {
                x: that.vx,
                y: yValue,
                xValue: argument,
                yValue: that[coord + "Value"]
            };
        }
        coords.axis = that.series.axis;
        return coords;
    },
    _updateData: function(data) {
        const label = this._label;
        const reductionColor = this._options.reduction.color;
        this.value = this.initialValue = data.reductionValue;
        this.originalValue = data.value;
        this.lowValue = this.originalLowValue = data.lowValue;
        this.highValue = this.originalHighValue = data.highValue;
        this.openValue = this.originalOpenValue = data.openValue;
        this.closeValue = this.originalCloseValue = data.closeValue;
        this._isPositive = data.openValue < data.closeValue;
        this._isReduction = data.isReduction;
        if (this._isReduction) {
            label.setColor(reductionColor);
        }
    },
    _updateMarker: function(animationEnabled, style, group) {
        const graphic = this.graphic;
        graphic.attr({
            points: this._getPoints()
        }).smartAttr(style).sharp();
        group && graphic.append(this._getMarkerGroup(group));
    },
    _getLabelFormatObject: function() {
        return {
            openValue: this.openValue,
            highValue: this.highValue,
            lowValue: this.lowValue,
            closeValue: this.closeValue,
            reductionValue: this.initialValue,
            argument: this.initialArgument,
            value: this.initialValue,
            seriesName: this.series.name,
            originalOpenValue: this.originalOpenValue,
            originalCloseValue: this.originalCloseValue,
            originalLowValue: this.originalLowValue,
            originalHighValue: this.originalHighValue,
            originalArgument: this.originalArgument,
            point: this
        };
    },
    _getFormatObject: function(tooltip) {
        const highValue = tooltip.formatValue(this.highValue);
        const openValue = tooltip.formatValue(this.openValue);
        const closeValue = tooltip.formatValue(this.closeValue);
        const lowValue = tooltip.formatValue(this.lowValue);
        const symbolMethods = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
        const formatObject = symbolMethods._getFormatObject.call(this, tooltip);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, formatObject, {
            valueText: "h: " + highValue + ("" !== openValue ? " o: " + openValue : "") + ("" !== closeValue ? " c: " + closeValue : "") + " l: " + lowValue,
            highValueText: highValue,
            openValueText: openValue,
            closeValueText: closeValue,
            lowValueText: lowValue
        });
    },
    getMaxValue: function() {
        return this.highValue;
    },
    getMinValue: function() {
        return this.lowValue;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/stock_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/stock_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$candlestick_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/candlestick_point.js [app-client] (ecmascript)");
;
;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const _isNumeric = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isNumeric"];
const __TURBOPACK__default__export__ = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$candlestick_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    _getPoints: function() {
        const createPoint = this._options.rotated ? function(x, y) {
            return [
                y,
                x
            ];
        } : function(x, y) {
            return [
                x,
                y
            ];
        };
        const openYExist = _isNumeric(this.openY);
        const closeYExist = _isNumeric(this.closeY);
        const x = this.x;
        const width = this.width;
        let points = [].concat(createPoint(x, this.highY));
        openYExist && (points = points.concat(createPoint(x, this.openY)));
        openYExist && (points = points.concat(createPoint(x - width / 2, this.openY)));
        openYExist && (points = points.concat(createPoint(x, this.openY)));
        closeYExist && (points = points.concat(createPoint(x, this.closeY)));
        closeYExist && (points = points.concat(createPoint(x + width / 2, this.closeY)));
        closeYExist && (points = points.concat(createPoint(x, this.closeY)));
        points = points.concat(createPoint(x, this.lowY));
        return points;
    },
    _drawMarkerInGroup: function(group, attributes, renderer) {
        this.graphic = renderer.path(this._getPoints(), "line").attr({
            "stroke-linecap": "square"
        }).attr(attributes).data({
            "chart-data-point": this
        }).sharp().append(group);
    },
    _getMinTrackerWidth: function() {
        const width = 2 + this._styles.normal["stroke-width"];
        return width + width % 2;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/polar_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/polar_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "polarBarPoint": ()=>polarBarPoint,
    "polarSymbolPoint": ()=>polarSymbolPoint
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/bar_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/pie_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/consts.js [app-client] (ecmascript)");
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
;
;
;
;
;
const _math = Math;
const _max = _math.max;
;
const RADIAL_LABEL_INDENT = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].radialLabelIndent;
const ERROR_BARS_ANGLE_OFFSET = 90;
const CANVAS_POSITION_START = "canvas_position_start";
const CANVAS_POSITION_END = "canvas_position_end";
const CANVAS_POSITION_DEFAULT = "canvas_position_default";
const polarSymbolPoint = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    _getLabelCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelCoords,
    _getElementCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getElementCoords,
    _moveLabelOnCanvas: function(coord, visibleArea, labelBBox) {
        let x = coord.x;
        let y = coord.y;
        if (visibleArea.minX > x) {
            x = visibleArea.minX;
        }
        if (visibleArea.maxX < x + labelBBox.width) {
            x = visibleArea.maxX - labelBBox.width;
        }
        if (visibleArea.minY > y) {
            y = visibleArea.minY;
        }
        if (visibleArea.maxY < y + labelBBox.height) {
            y = visibleArea.maxY - labelBBox.height;
        }
        return {
            x: x,
            y: y
        };
    },
    _getLabelPosition: function() {
        return "outside";
    },
    _getCoords: function(argument, value) {
        const axis = this.series.getValueAxis();
        const startAngle = axis.getAngles()[0];
        const angle = this._getArgTranslator().translate(argument);
        const radius = this._getValTranslator().translate(value);
        const coords = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertPolarToXY"])(axis.getCenter(), axis.getAngles()[0], angle, radius);
        coords.angle = angle + startAngle - 90, coords.radius = radius;
        return coords;
    },
    _translate () {
        const center = this.series.getValueAxis().getCenter();
        const coord = this._getCoords(this.argument, this.value);
        const translator = this._getValTranslator();
        const maxRadius = translator.isInverted() ? translator.translate(CANVAS_POSITION_START) : translator.translate(CANVAS_POSITION_END);
        const normalizedRadius = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(coord.radius) && coord.radius >= 0 ? coord.radius : null;
        this.vx = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(coord.angle);
        this.vy = this.radiusOuter = this.radiusLabels = normalizedRadius;
        this.radiusLabels += RADIAL_LABEL_INDENT;
        this.radius = normalizedRadius;
        this.middleAngle = -coord.angle;
        this.angle = -coord.angle;
        this.x = coord.x;
        this.y = coord.y;
        this.defaultX = this.centerX = center.x;
        this.defaultY = this.centerY = center.y;
        this._translateErrorBars();
        this.inVisibleArea = this._checkRadiusForVisibleArea(normalizedRadius, maxRadius);
    },
    _checkRadiusForVisibleArea: (radius, maxRadius)=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(radius) && radius <= maxRadius,
    _translateErrorBars: function() {
        const errorBars = this._options.errorBars;
        const translator = this._getValTranslator();
        if (!errorBars) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.lowError) && (this._lowErrorCoord = this.centerY - translator.translate(this.lowError));
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this.highError) && (this._highErrorCoord = this.centerY - translator.translate(this.highError));
        this._errorBarPos = this.centerX;
        this._baseErrorBarPos = "stdDeviation" === errorBars.type ? this._lowErrorCoord + (this._highErrorCoord - this._lowErrorCoord) / 2 : this.centerY - this.radius;
    },
    _getTranslates: function(animationEnabled) {
        return animationEnabled ? this.getDefaultCoords() : {
            x: this.x,
            y: this.y
        };
    },
    getDefaultCoords: function() {
        const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(-this.angle);
        const radius = this._getValTranslator().translate(CANVAS_POSITION_DEFAULT);
        const x = this.defaultX + radius * cosSin.cos;
        const y = this.defaultY + radius * cosSin.sin;
        return {
            x: x,
            y: y
        };
    },
    _addLabelAlignmentAndOffset: function(label, coord) {
        return coord;
    },
    _checkLabelPosition: function(label, coord) {
        const that = this;
        const visibleArea = that._getVisibleArea();
        const graphicBBox = that._getGraphicBBox();
        if (that._isPointInVisibleArea(visibleArea, graphicBBox)) {
            coord = that._moveLabelOnCanvas(coord, visibleArea, label.getBoundingRect());
        }
        return coord;
    },
    _getErrorBarSettings: function(errorBarOptions, animationEnabled) {
        const settings = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getErrorBarSettings.call(this, errorBarOptions, animationEnabled);
        settings.rotate = 90 - this.angle;
        settings.rotateX = this.centerX;
        settings.rotateY = this.centerY;
        return settings;
    },
    getCoords: function(min) {
        return min ? this.getDefaultCoords() : {
            x: this.x,
            y: this.y
        };
    }
});
const polarBarPoint = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
    _translateErrorBars: polarSymbolPoint._translateErrorBars,
    _getErrorBarSettings: polarSymbolPoint._getErrorBarSettings,
    _moveLabelOnCanvas: polarSymbolPoint._moveLabelOnCanvas,
    _getLabelCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelCoords,
    _getElementCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getElementCoords,
    _getLabelConnector: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelConnector,
    getTooltipParams: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getTooltipParams,
    _getLabelPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]._getLabelPosition,
    _getCoords: polarSymbolPoint._getCoords,
    _translate () {
        const that = this;
        const translator = that._getValTranslator();
        const businessRange = translator.getBusinessRange();
        const maxRadius = translator.isInverted() ? translator.translate(CANVAS_POSITION_START) : translator.translate(CANVAS_POSITION_END);
        that.radiusInner = translator.translate(that.minValue);
        polarSymbolPoint._translate.call(that);
        if (null === that.radiusInner) {
            that.radiusInner = that.radius = maxRadius;
        } else if (null === that.radius) {
            that.radius = that.value >= businessRange.minVisible ? maxRadius : 0;
        } else if (that.radius > maxRadius) {
            that.radius = maxRadius;
        }
        that.radiusOuter = that.radiusLabels = _max(that.radiusInner, that.radius);
        that.radiusLabels += RADIAL_LABEL_INDENT;
        that.radiusInner = that.defaultRadius = _math.min(that.radiusInner, that.radius);
        that.middleAngle = that.angle = -(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(that.middleAngleCorrection - that.angle);
    },
    _checkRadiusForVisibleArea (radius) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(radius) || this._getValTranslator().translate(this.minValue) > 0;
    },
    _getErrorBarBaseEdgeLength () {
        const coord = this.getMarkerCoords();
        return _math.PI * coord.outerRadius * _math.abs(coord.startAngle - coord.endAngle) / 180;
    },
    getMarkerCoords: function() {
        return {
            x: this.centerX,
            y: this.centerY,
            outerRadius: this.radiusOuter,
            innerRadius: this.defaultRadius,
            startAngle: this.middleAngle - this.interval / 2,
            endAngle: this.middleAngle + this.interval / 2
        };
    },
    _drawMarker: function(renderer, group, animationEnabled) {
        const styles = this._getStyle();
        const coords = this.getMarkerCoords();
        let innerRadius = coords.innerRadius;
        let outerRadius = coords.outerRadius;
        const start = this._getCoords(this.argument, CANVAS_POSITION_DEFAULT);
        let x = coords.x;
        let y = coords.y;
        if (animationEnabled) {
            innerRadius = 0;
            outerRadius = 0;
            x = start.x;
            y = start.y;
        }
        this.graphic = renderer.arc(x, y, innerRadius, outerRadius, coords.startAngle, coords.endAngle).attr(styles).data({
            "chart-data-point": this
        }).append(group);
    },
    _checkLabelPosition: function(label, coord) {
        const that = this;
        const visibleArea = that._getVisibleArea();
        const angleFunctions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(that.middleAngle);
        const x = that.centerX + that.defaultRadius * angleFunctions.cos;
        const y = that.centerY - that.defaultRadius * angleFunctions.sin;
        if (x > visibleArea.minX && x < visibleArea.maxX && y > visibleArea.minY && y < visibleArea.maxY) {
            coord = that._moveLabelOnCanvas(coord, visibleArea, label.getBoundingRect());
        }
        return coord;
    },
    _addLabelAlignmentAndOffset: function(label, coord) {
        return coord;
    },
    correctCoordinates: function(correctOptions) {
        this.middleAngleCorrection = correctOptions.offset;
        this.interval = correctOptions.width;
    },
    coordsIn: function(x, y) {
        const val = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertXYToPolar"])(this.series.getValueAxis().getCenter(), x, y);
        const coords = this.getMarkerCoords();
        const isBetweenAngles = coords.startAngle < coords.endAngle ? -val.phi >= coords.startAngle && -val.phi <= coords.endAngle : -val.phi <= coords.startAngle && -val.phi >= coords.endAngle;
        return val.r >= coords.innerRadius && val.r <= coords.outerRadius && isBetweenAngles;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/points/base_point.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/points/base_point.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Point": ()=>Point
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/consts.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/symbol_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/bar_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bubble_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/bubble_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/pie_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/range_symbol_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/range_bar_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$candlestick_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/candlestick_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$stock_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/stock_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$polar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/polar_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
const mixins = {};
;
;
;
;
;
;
;
;
;
;
;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
;
;
const statesConsts = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].states;
const SYMBOL_POINT = "symbolPoint";
const POLAR_SYMBOL_POINT = "polarSymbolPoint";
const BAR_POINT = "barPoint";
const POLAR_BAR_POINT = "polarBarPoint";
const PIE_POINT = "piePoint";
const SELECTED_STATE = statesConsts.selectedMark;
const HOVER_STATE = statesConsts.hoverMark;
const NORMAL_STATE = statesConsts.normalMark;
const HOVER = statesConsts.hover;
const NORMAL = statesConsts.normal;
const SELECTION = statesConsts.selection;
const pointTypes = {
    chart: {
        scatter: SYMBOL_POINT,
        line: SYMBOL_POINT,
        spline: SYMBOL_POINT,
        stepline: SYMBOL_POINT,
        stackedline: SYMBOL_POINT,
        fullstackedline: SYMBOL_POINT,
        stackedspline: SYMBOL_POINT,
        fullstackedspline: SYMBOL_POINT,
        stackedsplinearea: SYMBOL_POINT,
        fullstackedsplinearea: SYMBOL_POINT,
        area: SYMBOL_POINT,
        splinearea: SYMBOL_POINT,
        steparea: SYMBOL_POINT,
        stackedarea: SYMBOL_POINT,
        fullstackedarea: SYMBOL_POINT,
        rangearea: "rangeSymbolPoint",
        bar: BAR_POINT,
        stackedbar: BAR_POINT,
        fullstackedbar: BAR_POINT,
        rangebar: "rangeBarPoint",
        bubble: "bubblePoint",
        stock: "stockPoint",
        candlestick: "candlestickPoint"
    },
    pie: {
        pie: PIE_POINT,
        doughnut: PIE_POINT,
        donut: PIE_POINT
    },
    polar: {
        scatter: "polarSymbolPoint",
        line: "polarSymbolPoint",
        area: "polarSymbolPoint",
        bar: "polarBarPoint",
        stackedbar: "polarBarPoint"
    }
};
function isNoneMode(mode) {
    return "none" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(mode);
}
function Point(series, dataItem, options) {
    this.fullState = NORMAL_STATE;
    this.series = series;
    this.update(dataItem, options);
    this._viewCounters = {
        hover: 0,
        selection: 0
    };
    this._emptySettings = {
        fill: null,
        stroke: null,
        dashStyle: null,
        filter: null
    };
}
mixins.symbolPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.barPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.bubblePoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$bubble_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.piePoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$pie_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.rangeSymbolPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_symbol_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.rangeBarPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$range_bar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.candlestickPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$candlestick_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.stockPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$stock_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"];
mixins.polarSymbolPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$polar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polarSymbolPoint"];
mixins.polarBarPoint = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$polar_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polarBarPoint"];
Point.prototype = {
    constructor: Point,
    getColor: function() {
        if (!this.hasValue() && !this._styles.usePointCustomOptions) {
            this.series.customizePoint(this, this._dataItem);
        }
        return this._styles.normal.fill || this.series.getColor();
    },
    _getStyle: function() {
        return this._styles[this._currentStyle || "normal"];
    },
    update: function(dataItem, options) {
        this.updateOptions(options);
        this.updateData(dataItem);
    },
    updateData: function(dataItem) {
        const argumentWasChanged = this.argument !== dataItem.argument;
        this.argument = this.initialArgument = this.originalArgument = dataItem.argument;
        this.tag = dataItem.tag;
        this.index = dataItem.index;
        this._dataItem = dataItem;
        this.data = dataItem.data;
        this.lowError = dataItem.lowError;
        this.highError = dataItem.highError;
        this.aggregationInfo = dataItem.aggregationInfo;
        this._updateData(dataItem, argumentWasChanged);
        !this.hasValue() && this.setInvisibility();
        this._fillStyle();
        this._updateLabelData();
    },
    deleteMarker: function() {
        const that = this;
        if (that.graphic) {
            that.graphic.dispose();
        }
        that.graphic = null;
    },
    draw: function(renderer, groups, animationEnabled, firstDrawing) {
        const that = this;
        if (that._needDeletingOnDraw || that.series.autoHidePointMarkers && !that.isSelected()) {
            that.deleteMarker();
            that._needDeletingOnDraw = false;
        }
        if (that._needClearingOnDraw) {
            that.clearMarker();
            that._needClearingOnDraw = false;
        }
        if (!that._hasGraphic()) {
            that.getMarkerVisibility() && !that.series.autoHidePointMarkers && that._drawMarker(renderer, groups.markers, animationEnabled, firstDrawing);
        } else {
            that._updateMarker(animationEnabled, this._getStyle(), groups.markers);
        }
        that._drawLabel();
        that._drawErrorBar(renderer, groups.errorBars, animationEnabled);
        return that;
    },
    _getViewStyle: function() {
        let state = NORMAL_STATE;
        let fullState = this.fullState;
        const styles = [
            NORMAL,
            HOVER,
            SELECTION,
            SELECTION
        ];
        if (this._viewCounters.hover) {
            state |= HOVER_STATE;
        }
        if (this._viewCounters.selection) {
            state |= SELECTED_STATE;
        }
        if (isNoneMode(this.getOptions().selectionMode)) {
            fullState &= ~SELECTED_STATE;
        }
        if (isNoneMode(this.getOptions().hoverMode)) {
            fullState &= ~HOVER_STATE;
        }
        state |= fullState;
        return styles[state];
    },
    applyView: function(legendCallback) {
        const that = this;
        const style = that._getViewStyle();
        that._currentStyle = style;
        if (!that.graphic && that.getMarkerVisibility() && that.series.autoHidePointMarkers && (style === SELECTION || style === HOVER)) {
            that._drawMarker(that.series.getRenderer(), that.series.getMarkersGroup());
        }
        if (that.graphic) {
            if (that.series.autoHidePointMarkers && style !== SELECTION && style !== HOVER) {
                that.deleteMarker();
            } else {
                if ("normal" === style) {
                    that.clearMarker();
                } else {
                    that.graphic.toForeground();
                }
                that._updateMarker(true, that._styles[style], void 0, legendCallback);
            }
        }
    },
    setView: function(style) {
        this._viewCounters[style]++;
        this.applyView();
    },
    resetView: function(style) {
        const viewCounters = this._viewCounters;
        --viewCounters[style];
        if (viewCounters[style] < 0) {
            viewCounters[style] = 0;
        }
        this.applyView();
    },
    releaseHoverState: function() {
        const that = this;
        if (that.graphic && !that.isSelected()) {
            that.graphic.toBackground();
        }
    },
    select: function() {
        this.series.selectPoint(this);
    },
    clearSelection: function() {
        this.series.deselectPoint(this);
    },
    hover: function() {
        this.series.hoverPoint(this);
    },
    clearHover: function() {
        this.series.clearPointHover();
    },
    showTooltip: function() {
        this.series.showPointTooltip(this);
    },
    hideTooltip: function() {
        this.series.hidePointTooltip(this);
    },
    _checkLabelsChanging: function(oldType, newType) {
        const isNewRange = ~newType.indexOf("range");
        const isOldRange = ~oldType.indexOf("range");
        return isOldRange && !isNewRange || !isOldRange && isNewRange;
    },
    updateOptions: function(newOptions) {
        if (!newOptions) {
            return;
        }
        const that = this;
        const oldOptions = that._options;
        const widgetType = newOptions.widgetType;
        const oldType = oldOptions && oldOptions.type;
        const newType = newOptions.type;
        const newPointTypeMixin = pointTypes[widgetType][newType];
        if (oldType !== newType) {
            that._needDeletingOnDraw = true;
            that._needClearingOnDraw = false;
            if (oldType) {
                that._checkLabelsChanging(oldType, newType) && that.deleteLabel();
                that._resetType(mixins[pointTypes[oldType]]);
            }
            that._setType(mixins[newPointTypeMixin]);
        } else {
            that._needDeletingOnDraw = that._needDeletingOnDraw || that._checkSymbol(oldOptions, newOptions);
            that._needClearingOnDraw = that._checkCustomize(oldOptions, newOptions);
        }
        that._options = newOptions;
        that._fillStyle();
        that._updateLabelOptions(newPointTypeMixin);
    },
    translate: function() {
        if (this.hasValue()) {
            this._translate();
            this.translated = true;
        }
    },
    _checkCustomize: function(oldOptions, newOptions) {
        return oldOptions.styles.usePointCustomOptions && !newOptions.styles.usePointCustomOptions;
    },
    _getCustomLabelVisibility: function() {
        return this._styles.useLabelCustomOptions ? !!this._options.label.visible : null;
    },
    getBoundingRect: function() {
        return this._getGraphicBBox();
    },
    _resetType: function(methods) {
        for(const methodName in methods){
            delete this[methodName];
        }
    },
    _setType: function(methods) {
        for(const methodName in methods){
            this[methodName] = methods[methodName];
        }
    },
    isInVisibleArea: function() {
        return this.inVisibleArea;
    },
    isSelected: function() {
        return !!(this.fullState & SELECTED_STATE);
    },
    isHovered: function() {
        return !!(this.fullState & HOVER_STATE);
    },
    getOptions: function() {
        return this._options;
    },
    animate: function(complete, settings, partitionDuration) {
        if (!this.graphic) {
            complete && complete();
            return;
        }
        this.graphic.animate(settings, {
            partitionDuration: partitionDuration
        }, complete);
    },
    getCoords: function(min) {
        const that = this;
        if (!min) {
            return {
                x: that.x,
                y: that.y
            };
        }
        if (!that._options.rotated) {
            return {
                x: that.x,
                y: that.minY + (that.y - that.minY ? 0 : 1)
            };
        }
        return {
            x: that.minX - (that.x - that.minX ? 0 : 1),
            y: that.y
        };
    },
    getDefaultCoords: function() {
        return !this._options.rotated ? {
            x: this.x,
            y: this.defaultY
        } : {
            x: this.defaultX,
            y: this.y
        };
    },
    setDefaultCoords () {
        const coords = this.getDefaultCoords();
        this.x = coords.x;
        this.y = coords.y;
    },
    _getVisibleArea: function() {
        return this.series.getVisibleArea();
    },
    _getArgTranslator: function() {
        return this.series.getArgumentAxis().getTranslator();
    },
    _getValTranslator: function() {
        return this.series.getValueAxis().getTranslator();
    },
    isArgumentCorrect () {
        return this.series._argumentChecker(this.argument);
    },
    isValueCorrect () {
        const valueChecker = this.series._valueChecker;
        return valueChecker(this.getMinValue()) && valueChecker(this.getMaxValue());
    },
    hasValue: function() {
        return null !== this.value && null !== this.minValue && this.isArgumentCorrect() && this.isValueCorrect();
    },
    hasCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    correctPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    correctRadius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    correctLabelRadius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getCrosshairData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getPointRadius: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _populatePointShape: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _checkSymbol: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getMarkerCoords: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    hide: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    show: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    hideMarker: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    setInvisibility: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    clearVisibility: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    isVisible: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    resetCorrection: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    correctValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    resetValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    setPercentValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    correctCoordinates: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    coordsIn: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getTooltipParams: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    applyWordWrap: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    setLabelTrackerData: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    updateLabelCoord: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    drawLabel: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    correctLabelPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getMinValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getMaxValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _drawErrorBar: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getMarkerVisibility: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    dispose: function() {
        this.deleteMarker();
        this.deleteLabel();
        this._errorBar && this._errorBar.dispose();
        this._options = this._styles = this.series = this._errorBar = null;
    },
    getTooltipFormatObject: function(tooltip, stackPoints) {
        const that = this;
        const tooltipFormatObject = that._getFormatObject(tooltip);
        const sharedTooltipValuesArray = [];
        const tooltipStackPointsFormatObject = [];
        if (stackPoints) {
            stackPoints.forEach((point)=>{
                if (!point.isVisible()) {
                    return;
                }
                const formatObject = point._getFormatObject(tooltip);
                tooltipStackPointsFormatObject.push(formatObject);
                sharedTooltipValuesArray.push(formatObject.seriesName + ": " + formatObject.valueText);
            });
            _extend(tooltipFormatObject, {
                points: tooltipStackPointsFormatObject,
                valueText: sharedTooltipValuesArray.join("\n"),
                stackName: that.series.getStackName() || null
            });
        }
        const aggregationInfo = that.aggregationInfo;
        if (aggregationInfo) {
            const axis = that.series.getArgumentAxis();
            const rangeText = axis.formatRange(aggregationInfo.intervalStart, aggregationInfo.intervalEnd, aggregationInfo.aggregationInterval, tooltip.getOptions().argumentFormat);
            if (rangeText) {
                tooltipFormatObject.valueText += "\n".concat(rangeText);
            }
        }
        return tooltipFormatObject;
    },
    setHole: function(holeValue, position) {
        const that = this;
        const minValue = isFinite(that.minValue) ? that.minValue : 0;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(holeValue)) {
            if ("left" === position) {
                that.leftHole = that.value - holeValue;
                that.minLeftHole = minValue - holeValue;
            } else {
                that.rightHole = that.value - holeValue;
                that.minRightHole = minValue - holeValue;
            }
        }
    },
    resetHoles: function() {
        this.leftHole = null;
        this.minLeftHole = null;
        this.rightHole = null;
        this.minRightHole = null;
    },
    getLabel: function() {
        return this._label;
    },
    getLabels: function() {
        return [
            this._label
        ];
    },
    getCenterCoord () {
        return {
            x: this.x,
            y: this.y
        };
    }
};
}),
"[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/scatter_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart,
    "polar": ()=>polar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/helpers/range_data_calculator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
;
;
;
;
;
const math = Math;
const _abs = math.abs;
const _sqrt = math.sqrt;
const _max = math.max;
const DEFAULT_TRACKER_WIDTH = 12;
const DEFAULT_DURATION = 400;
const HIGH_ERROR = "highError";
const LOW_ERROR = "lowError";
const VARIANCE = "variance";
const STANDARD_DEVIATION = "stddeviation";
const STANDARD_ERROR = "stderror";
const PERCENT = "percent";
const FIXED = "fixed";
const UNDEFINED = "undefined";
const DISCRETE = "discrete";
const LOGARITHMIC = "logarithmic";
const DATETIME = "datetime";
let chart = {};
let polar = {};
function sum(array) {
    let result = 0;
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(array, function(_, value) {
        result += value;
    });
    return result;
}
function isErrorBarTypeCorrect(type) {
    return [
        FIXED,
        PERCENT,
        VARIANCE,
        STANDARD_DEVIATION,
        STANDARD_ERROR
    ].includes(type);
}
function variance(array, expectedValue) {
    return sum((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(array, function(value) {
        return (value - expectedValue) * (value - expectedValue);
    })) / array.length;
}
function calculateAvgErrorBars(result, data, series) {
    const errorBarsOptions = series.getOptions().valueErrorBar;
    const valueField = series.getValueFields()[0];
    const lowValueField = errorBarsOptions.lowValueField || LOW_ERROR;
    const highValueField = errorBarsOptions.highValueField || HIGH_ERROR;
    if (series.areErrorBarsVisible() && void 0 === errorBarsOptions.type) {
        const fusionData = data.reduce(function(result, item) {
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(item[lowValueField])) {
                result[0] += item[valueField] - item[lowValueField];
                result[1]++;
            }
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(item[highValueField])) {
                result[2] += item[highValueField] - item[valueField];
                result[3]++;
            }
            return result;
        }, [
            0,
            0,
            0,
            0
        ]);
        if (fusionData[1]) {
            result[lowValueField] = result[valueField] - fusionData[0] / fusionData[1];
        }
        if (fusionData[2]) {
            result[highValueField] = result[valueField] + fusionData[2] / fusionData[3];
        }
    }
    return result;
}
function calculateSumErrorBars(result, data, series) {
    const errorBarsOptions = series.getOptions().valueErrorBar;
    const lowValueField = errorBarsOptions.lowValueField || LOW_ERROR;
    const highValueField = errorBarsOptions.highValueField || HIGH_ERROR;
    if (series.areErrorBarsVisible() && void 0 === errorBarsOptions.type) {
        result[lowValueField] = 0;
        result[highValueField] = 0;
        result = data.reduce(function(result, item) {
            result[lowValueField] += item[lowValueField];
            result[highValueField] += item[highValueField];
            return result;
        }, result);
    }
    return result;
}
function getMinMaxAggregator(compare) {
    return (_ref, series)=>{
        let { intervalStart: intervalStart, intervalEnd: intervalEnd, data: data } = _ref;
        const valueField = series.getValueFields()[0];
        let targetData = data[0];
        targetData = data.reduce((result, item)=>{
            const value = item[valueField];
            if (null === result[valueField]) {
                result = item;
            }
            if (null !== value && compare(value, result[valueField])) {
                return item;
            }
            return result;
        }, targetData);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, targetData, {
            [series.getArgumentField()]: series._getIntervalCenter(intervalStart, intervalEnd)
        });
    };
}
function checkFields(data, fieldsToCheck, skippedFields) {
    let allFieldsIsValid = true;
    for(const field in fieldsToCheck){
        const isArgument = "argument" === field;
        if (isArgument || "size" === field ? !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(data[field]) : void 0 === data[field]) {
            const selector = fieldsToCheck[field];
            if (!isArgument) {
                skippedFields[selector] = (skippedFields[selector] || 0) + 1;
            }
            allFieldsIsValid = false;
        }
    }
    return allFieldsIsValid;
}
const baseScatterMethods = {
    _defaultDuration: 400,
    _defaultTrackerWidth: 12,
    _applyStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _updateOptions: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _parseStyle: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _prepareSegment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _drawSegment: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _appendInGroup: function() {
        this._group.append(this._extGroups.seriesGroup);
    },
    _createLegendState: function(styleOptions, defaultColor) {
        return {
            fill: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(styleOptions.color, true) || defaultColor,
            hatching: styleOptions.hatching ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, styleOptions.hatching, {
                direction: "right"
            }) : void 0
        };
    },
    _getColorId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _applyElementsClipRect: function(settings) {
        settings["clip-path"] = this._paneClipRectID;
    },
    _applyMarkerClipRect: function(settings) {
        settings["clip-path"] = this._forceClipping ? this._paneClipRectID : null;
    },
    _createGroup: function(groupName, parent, target, settings) {
        const group = parent[groupName] = parent[groupName] || this._renderer.g();
        target && group.append(target);
        settings && group.attr(settings);
    },
    _applyClearingSettings: function(settings) {
        settings.opacity = null;
        settings.scale = null;
        if (this._options.rotated) {
            settings.translateX = null;
        } else {
            settings.translateY = null;
        }
    },
    _createGroups: function() {
        this._createGroup("_markersGroup", this, this._group);
        this._createGroup("_labelsGroup", this);
    },
    _setMarkerGroupSettings: function() {
        const settings = this._createPointStyles(this._getMarkerGroupOptions()).normal;
        settings.class = "dxc-markers";
        settings.opacity = 1;
        this._applyMarkerClipRect(settings);
        this._markersGroup.attr(settings);
    },
    getVisibleArea: function() {
        return this._visibleArea;
    },
    areErrorBarsVisible: function() {
        const errorBarOptions = this._options.valueErrorBar;
        return errorBarOptions && this._errorBarsEnabled() && "none" !== errorBarOptions.displayMode && (isErrorBarTypeCorrect((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(errorBarOptions.type)) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(errorBarOptions.lowValueField) || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(errorBarOptions.highValueField));
    },
    groupPointsByCoords (rotated) {
        const cat = [];
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this.getVisiblePoints(), function(_, p) {
            const pointCoord = parseInt(rotated ? p.vy : p.vx);
            if (!cat[pointCoord]) {
                cat[pointCoord] = p;
            } else {
                Array.isArray(cat[pointCoord]) ? cat[pointCoord].push(p) : cat[pointCoord] = [
                    cat[pointCoord],
                    p
                ];
            }
        });
        return cat;
    },
    _createErrorBarGroup: function(animationEnabled) {
        const that = this;
        const errorBarOptions = that._options.valueErrorBar;
        let settings;
        if (that.areErrorBarsVisible()) {
            settings = {
                class: "dxc-error-bars",
                stroke: errorBarOptions.color,
                "stroke-width": errorBarOptions.lineWidth,
                opacity: animationEnabled ? .001 : errorBarOptions.opacity || 1,
                "stroke-linecap": "square",
                sharp: true,
                "clip-path": that._forceClipping ? that._paneClipRectID : that._widePaneClipRectID
            };
            that._createGroup("_errorBarGroup", that, that._group, settings);
        }
    },
    _setGroupsSettings: function(animationEnabled) {
        this._setMarkerGroupSettings();
        this._setLabelGroupSettings(animationEnabled);
        this._createErrorBarGroup(animationEnabled);
    },
    _getCreatingPointOptions: function() {
        const that = this;
        let defaultPointOptions;
        let creatingPointOptions = that._predefinedPointOptions;
        let normalStyle;
        if (!creatingPointOptions) {
            defaultPointOptions = that._getPointOptions();
            that._predefinedPointOptions = creatingPointOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {
                styles: {}
            }, defaultPointOptions);
            normalStyle = defaultPointOptions.styles && defaultPointOptions.styles.normal || {};
            creatingPointOptions.styles = creatingPointOptions.styles || {};
            creatingPointOptions.styles.normal = {
                "stroke-width": normalStyle["stroke-width"],
                r: normalStyle.r,
                opacity: normalStyle.opacity
            };
        }
        return creatingPointOptions;
    },
    _getPointOptions: function() {
        return this._parsePointOptions(this._preparePointOptions(), this._options.label);
    },
    _getOptionsForPoint: function() {
        return this._options.point;
    },
    _parsePointStyle: function(style, defaultColor, defaultBorderColor, defaultSize) {
        const border = style.border || {};
        const sizeValue = void 0 !== style.size ? style.size : defaultSize;
        return {
            fill: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(style.color, true) || defaultColor,
            stroke: border.color || defaultBorderColor,
            "stroke-width": border.visible ? border.width : 0,
            r: sizeValue / 2 + (border.visible && 0 !== sizeValue ? ~~(border.width / 2) || 0 : 0)
        };
    },
    _createPointStyles: function(pointOptions) {
        const mainPointColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(pointOptions.color, true) || this._options.mainSeriesColor;
        const containerColor = this._options.containerBackgroundColor;
        const normalStyle = this._parsePointStyle(pointOptions, mainPointColor, mainPointColor);
        normalStyle.visibility = pointOptions.visible ? "visible" : "hidden";
        return {
            labelColor: mainPointColor,
            normal: normalStyle,
            hover: this._parsePointStyle(pointOptions.hoverStyle, containerColor, mainPointColor, pointOptions.size),
            selection: this._parsePointStyle(pointOptions.selectionStyle, containerColor, mainPointColor, pointOptions.size)
        };
    },
    _checkData: function(data, skippedFields, fieldsToCheck) {
        fieldsToCheck = fieldsToCheck || {
            value: this.getValueFields()[0]
        };
        fieldsToCheck.argument = this.getArgumentField();
        return checkFields(data, fieldsToCheck, skippedFields || {}) && data.value === data.value;
    },
    getArgumentRangeInitialValue () {
        const points = this.getPoints();
        if (this.useAggregation() && points.length) {
            var _points$0$aggregation, _points$aggregationIn;
            return {
                min: null === (_points$0$aggregation = points[0].aggregationInfo) || void 0 === _points$0$aggregation ? void 0 : _points$0$aggregation.intervalStart,
                max: null === (_points$aggregationIn = points[points.length - 1].aggregationInfo) || void 0 === _points$aggregationIn ? void 0 : _points$aggregationIn.intervalEnd
            };
        }
        return;
    },
    getValueRangeInitialValue: function() {
        return;
    },
    _getRangeData: function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getRangeData(this);
    },
    _getPointDataSelector: function() {
        const valueField = this.getValueFields()[0];
        const argumentField = this.getArgumentField();
        const tagField = this.getTagField();
        const areErrorBarsVisible = this.areErrorBarsVisible();
        let lowValueField;
        let highValueField;
        if (areErrorBarsVisible) {
            const errorBarOptions = this._options.valueErrorBar;
            lowValueField = errorBarOptions.lowValueField || LOW_ERROR;
            highValueField = errorBarOptions.highValueField || HIGH_ERROR;
        }
        return (data)=>{
            const pointData = {
                value: this._processEmptyValue(data[valueField]),
                argument: data[argumentField],
                tag: data[tagField],
                data: data
            };
            if (areErrorBarsVisible) {
                pointData.lowError = data[lowValueField];
                pointData.highError = data[highValueField];
            }
            return pointData;
        };
    },
    _errorBarsEnabled: function() {
        return this.valueAxisType !== DISCRETE && this.valueAxisType !== LOGARITHMIC && this.valueType !== DATETIME;
    },
    _drawPoint: function(options) {
        const point = options.point;
        if (point.isInVisibleArea()) {
            point.clearVisibility();
            point.draw(this._renderer, options.groups, options.hasAnimation, options.firstDrawing);
            this._drawnPoints.push(point);
        } else {
            point.setInvisibility();
        }
    },
    _animateComplete: function() {
        const animationSettings = {
            duration: this._defaultDuration
        };
        this._labelsGroup && this._labelsGroup.animate({
            opacity: 1
        }, animationSettings);
        this._errorBarGroup && this._errorBarGroup.animate({
            opacity: this._options.valueErrorBar.opacity || 1
        }, animationSettings);
    },
    _animate: function() {
        const that = this;
        const lastPointIndex = that._drawnPoints.length - 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(that._drawnPoints || [], function(i, p) {
            p.animate(i === lastPointIndex ? function() {
                that._animateComplete();
            } : void 0, {
                translateX: p.x,
                translateY: p.y
            });
        });
    },
    _getIntervalCenter (intervalStart, intervalEnd) {
        const argAxis = this.getArgumentAxis();
        const axisOptions = argAxis.getOptions();
        if (argAxis.aggregatedPointBetweenTicks()) {
            return intervalStart;
        }
        return "discrete" !== axisOptions.type ? argAxis.getVisualRangeCenter({
            minVisible: intervalStart,
            maxVisible: intervalEnd
        }, true) : intervalStart;
    },
    _defaultAggregator: "avg",
    _aggregators: {
        avg (_ref2, series) {
            let { data: data, intervalStart: intervalStart, intervalEnd: intervalEnd } = _ref2;
            if (!data.length) {
                return;
            }
            const valueField = series.getValueFields()[0];
            const aggregationResult = data.reduce((result, item)=>{
                const value = item[valueField];
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(value)) {
                    result[0] += value;
                    result[1]++;
                } else if (null === value) {
                    result[2]++;
                }
                return result;
            }, [
                0,
                0,
                0
            ]);
            return calculateAvgErrorBars({
                [valueField]: aggregationResult[2] === data.length ? null : aggregationResult[0] / aggregationResult[1],
                [series.getArgumentField()]: series._getIntervalCenter(intervalStart, intervalEnd)
            }, data, series);
        },
        sum (_ref3, series) {
            let { intervalStart: intervalStart, intervalEnd: intervalEnd, data: data } = _ref3;
            if (!data.length) {
                return;
            }
            const valueField = series.getValueFields()[0];
            const aggregationResult = data.reduce((result, item)=>{
                const value = item[valueField];
                if (void 0 !== value) {
                    result[0] += value;
                }
                if (null === value) {
                    result[1]++;
                } else if (void 0 === value) {
                    result[2]++;
                }
                return result;
            }, [
                0,
                0,
                0
            ]);
            let value = aggregationResult[0];
            if (aggregationResult[1] === data.length) {
                value = null;
            }
            if (aggregationResult[2] === data.length) {
                return;
            }
            return calculateSumErrorBars({
                [valueField]: value,
                [series.getArgumentField()]: series._getIntervalCenter(intervalStart, intervalEnd)
            }, data, series);
        },
        count (_ref4, series) {
            let { data: data, intervalStart: intervalStart, intervalEnd: intervalEnd } = _ref4;
            const valueField = series.getValueFields()[0];
            return {
                [series.getArgumentField()]: series._getIntervalCenter(intervalStart, intervalEnd),
                [valueField]: data.filter((i)=>void 0 !== i[valueField]).length
            };
        },
        min: getMinMaxAggregator((a, b)=>a < b),
        max: getMinMaxAggregator((a, b)=>a > b)
    },
    _endUpdateData: function() {
        delete this._predefinedPointOptions;
    },
    getArgumentField: function() {
        return this._options.argumentField || "arg";
    },
    getValueFields: function() {
        const options = this._options;
        const errorBarsOptions = options.valueErrorBar;
        const valueFields = [
            options.valueField || "val"
        ];
        let lowValueField;
        let highValueField;
        if (errorBarsOptions) {
            lowValueField = errorBarsOptions.lowValueField;
            highValueField = errorBarsOptions.highValueField;
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isString"])(lowValueField) && valueFields.push(lowValueField);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isString"])(highValueField) && valueFields.push(highValueField);
        }
        return valueFields;
    },
    _calculateErrorBars: function(data) {
        if (!this.areErrorBarsVisible()) {
            return;
        }
        const options = this._options;
        const errorBarsOptions = options.valueErrorBar;
        const errorBarType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(errorBarsOptions.type);
        let floatErrorValue = parseFloat(errorBarsOptions.value);
        const valueField = this.getValueFields()[0];
        let value;
        const lowValueField = errorBarsOptions.lowValueField || LOW_ERROR;
        const highValueField = errorBarsOptions.highValueField || HIGH_ERROR;
        let valueArray;
        let valueArrayLength;
        let meanValue;
        let processDataItem;
        const addSubError = function(_i, item) {
            value = item.value;
            item.lowError = value - floatErrorValue;
            item.highError = value + floatErrorValue;
        };
        switch(errorBarType){
            case FIXED:
                processDataItem = addSubError;
                break;
            case PERCENT:
                processDataItem = function(_, item) {
                    value = item.value;
                    const error = value * floatErrorValue / 100;
                    item.lowError = value - error;
                    item.highError = value + error;
                };
                break;
            case UNDEFINED:
                processDataItem = function(_, item) {
                    item.lowError = item.data[lowValueField];
                    item.highError = item.data[highValueField];
                };
                break;
            default:
                valueArray = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(data, function(item) {
                    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(item.data[valueField]) ? item.data[valueField] : null;
                });
                valueArrayLength = valueArray.length;
                floatErrorValue = floatErrorValue || 1;
                switch(errorBarType){
                    case VARIANCE:
                        floatErrorValue = variance(valueArray, sum(valueArray) / valueArrayLength) * floatErrorValue;
                        processDataItem = addSubError;
                        break;
                    case STANDARD_DEVIATION:
                        meanValue = sum(valueArray) / valueArrayLength;
                        floatErrorValue = _sqrt(variance(valueArray, meanValue)) * floatErrorValue;
                        processDataItem = function(_, item) {
                            item.lowError = meanValue - floatErrorValue;
                            item.highError = meanValue + floatErrorValue;
                        };
                        break;
                    case STANDARD_ERROR:
                        floatErrorValue = _sqrt(variance(valueArray, sum(valueArray) / valueArrayLength) / valueArrayLength) * floatErrorValue;
                        processDataItem = addSubError;
                }
        }
        processDataItem && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(data, processDataItem);
    },
    _patchMarginOptions: function(options) {
        const pointOptions = this._getCreatingPointOptions();
        const styles = pointOptions.styles;
        const maxSize = [
            styles.normal,
            styles.hover,
            styles.selection
        ].reduce(function(max, style) {
            return _max(max, 2 * style.r + style["stroke-width"]);
        }, 0);
        options.size = pointOptions.visible ? maxSize : 0;
        options.sizePointNormalState = pointOptions.visible ? 2 * styles.normal.r + styles.normal["stroke-width"] : 2;
        return options;
    },
    usePointsToDefineAutoHiding () {
        return !!this._getOptionsForPoint().visible;
    }
};
chart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, baseScatterMethods, {
    drawTrackers: function() {
        const that = this;
        let trackers;
        let trackersGroup;
        const segments = that._segments || [];
        const rotated = that._options.rotated;
        if (!that.isVisible()) {
            return;
        }
        if (segments.length) {
            trackers = that._trackers = that._trackers || [];
            trackersGroup = that._trackersGroup = (that._trackersGroup || that._renderer.g().attr({
                fill: "gray",
                opacity: .001,
                stroke: "gray",
                class: "dxc-trackers"
            })).attr({
                "clip-path": this._paneClipRectID || null
            }).append(that._group);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(segments, function(i, segment) {
                if (!trackers[i]) {
                    trackers[i] = that._drawTrackerElement(segment).data({
                        "chart-data-series": that
                    }).append(trackersGroup);
                } else {
                    that._updateTrackerElement(segment, trackers[i]);
                }
            });
        }
        that._trackersTranslator = that.groupPointsByCoords(rotated);
    },
    _checkAxisVisibleAreaCoord (isArgument, coord) {
        const axis = isArgument ? this.getArgumentAxis() : this.getValueAxis();
        const visibleArea = axis.getVisibleArea();
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(coord) && visibleArea[0] <= coord && visibleArea[1] >= coord;
    },
    checkSeriesViewportCoord (axis, coord) {
        return this.getPoints().length && this.isVisible();
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord = null;
        const isOpposite = !isArgument && !this._options.rotated || isArgument && this._options.rotated;
        const coordName = !isOpposite ? "vx" : "vy";
        const oppositeCoordName = !isOpposite ? "vy" : "vx";
        const points = this.getVisiblePoints();
        for(let i = 0; i < points.length; i++){
            const p = points[i];
            const tmpCoord = p[coordName] === coord ? p[oppositeCoordName] : void 0;
            if (this._checkAxisVisibleAreaCoord(!isArgument, tmpCoord)) {
                oppositeCoord = tmpCoord;
                break;
            }
        }
        return oppositeCoord;
    },
    _getNearestPoints: (point, nextPoint)=>[
            point,
            nextPoint
        ],
    _getBezierPoints: ()=>[],
    _getNearestPointsByCoord (coord, isArgument) {
        const that = this;
        const rotated = that.getOptions().rotated;
        const isOpposite = !isArgument && !rotated || isArgument && rotated;
        const coordName = isOpposite ? "vy" : "vx";
        const allPoints = that.getPoints();
        const bezierPoints = that._getBezierPoints();
        const nearestPoints = [];
        if (allPoints.length > 1) {
            allPoints.forEach((point, i)=>{
                const nextPoint = allPoints[i + 1];
                if (nextPoint && (point[coordName] <= coord && nextPoint[coordName] >= coord || point[coordName] >= coord && nextPoint[coordName] <= coord)) {
                    nearestPoints.push(that._getNearestPoints(point, nextPoint, bezierPoints));
                }
            });
        } else {
            nearestPoints.push([
                allPoints[0],
                allPoints[0]
            ]);
        }
        return nearestPoints;
    },
    getNeighborPoint: function(x, y) {
        let pCoord = this._options.rotated ? y : x;
        let nCoord = pCoord;
        const cat = this._trackersTranslator;
        let point = null;
        let minDistance;
        const oppositeCoord = this._options.rotated ? x : y;
        const oppositeCoordName = this._options.rotated ? "vx" : "vy";
        if (this.isVisible() && cat) {
            point = cat[pCoord];
            do {
                point = cat[nCoord] || cat[pCoord];
                pCoord--;
                nCoord++;
            }while ((pCoord >= 0 || nCoord < cat.length) && !point)
            if (Array.isArray(point)) {
                minDistance = _abs(point[0][oppositeCoordName] - oppositeCoord);
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(point, function(i, p) {
                    const distance = _abs(p[oppositeCoordName] - oppositeCoord);
                    if (minDistance >= distance) {
                        minDistance = distance;
                        point = p;
                    }
                });
            }
        }
        return point;
    },
    _applyVisibleArea: function() {
        const rotated = this._options.rotated;
        const visibleX = (rotated ? this.getValueAxis() : this.getArgumentAxis()).getVisibleArea();
        const visibleY = (rotated ? this.getArgumentAxis() : this.getValueAxis()).getVisibleArea();
        this._visibleArea = {
            minX: visibleX[0],
            maxX: visibleX[1],
            minY: visibleY[0],
            maxY: visibleY[1]
        };
    },
    getPointCenterByArg (arg) {
        const point = this.getPointsByArg(arg)[0];
        return point ? point.getCenterCoord() : void 0;
    }
});
polar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, baseScatterMethods, {
    drawTrackers: function() {
        chart.drawTrackers.call(this);
        const cat = this._trackersTranslator;
        let index;
        if (!this.isVisible()) {
            return;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(cat, function(i, category) {
            if (category) {
                index = i;
                return false;
            }
        });
        cat[index + 360] = cat[index];
    },
    getNeighborPoint: function(x, y) {
        const pos = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertXYToPolar"])(this.getValueAxis().getCenter(), x, y);
        return chart.getNeighborPoint.call(this, pos.phi, pos.r);
    },
    _applyVisibleArea: function() {
        const canvas = this.getValueAxis().getCanvas();
        this._visibleArea = {
            minX: canvas.left,
            maxX: canvas.width - canvas.right,
            minY: canvas.top,
            maxY: canvas.height - canvas.bottom
        };
    },
    getSeriesPairCoord (params, isArgument) {
        let coords = null;
        const paramName = isArgument ? "argument" : "radius";
        const points = this.getVisiblePoints();
        for(let i = 0; i < points.length; i++){
            const p = points[i];
            const tmpPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(p[paramName]) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(params[paramName]) && p[paramName].valueOf() === params[paramName].valueOf() ? {
                x: p.x,
                y: p.y
            } : void 0;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tmpPoint)) {
                coords = tmpPoint;
                break;
            }
        }
        return coords;
    }
});
;
}),
"[project]/node_modules/devextreme/esm/viz/series/line_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/line_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart,
    "polar": ()=>polar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/object.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/math.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_math.js [app-client] (ecmascript)");
;
;
;
;
;
;
const DISCRETE = "discrete";
const { round: round, sqrt: sqrt, pow: pow, min: min, max: max, abs: abs } = Math;
const chart = {};
const polar = {};
function clonePoint(point, newX, newY, newAngle) {
    const p = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clone"])(point);
    p.x = newX;
    p.y = newY;
    p.angle = newAngle;
    return p;
}
function getTangentPoint(point, prevPoint, centerPoint, tan, nextStepAngle) {
    const correctAngle = point.angle + nextStepAngle;
    const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(correctAngle);
    const x = centerPoint.x + (point.radius + tan * nextStepAngle) * cosSin.cos;
    const y = centerPoint.y - (point.radius + tan * nextStepAngle) * cosSin.sin;
    return clonePoint(prevPoint, x, y, correctAngle);
}
function obtainCubicBezierTCoef(p, p0, p1, p2, p3) {
    const d = p0 - p;
    const c = 3 * p1 - 3 * p0;
    const b = 3 * p2 - 6 * p1 + 3 * p0;
    const a = p3 - 3 * p2 + 3 * p1 - p0;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["solveCubicEquation"])(a, b, c, d);
}
const lineMethods = {
    autoHidePointMarkersEnabled: ()=>true,
    _applyGroupSettings: function(style, settings, group) {
        settings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(settings, style);
        this._applyElementsClipRect(settings);
        group.attr(settings);
    },
    _setGroupsSettings: function(animationEnabled) {
        const style = this._styles.normal;
        this._applyGroupSettings(style.elements, {
            class: "dxc-elements"
        }, this._elementsGroup);
        this._bordersGroup && this._applyGroupSettings(style.border, {
            class: "dxc-borders"
        }, this._bordersGroup);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._setGroupsSettings.call(this, animationEnabled);
        animationEnabled && this._markersGroup && this._markersGroup.attr({
            opacity: .001
        });
    },
    _createGroups: function() {
        this._createGroup("_elementsGroup", this, this._group);
        this._areBordersVisible() && this._createGroup("_bordersGroup", this, this._group);
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._createGroups.call(this);
    },
    _areBordersVisible: function() {
        return false;
    },
    _getDefaultSegment: function(segment) {
        return {
            line: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(segment.line || [], function(pt) {
                return pt.getDefaultCoords();
            })
        };
    },
    _prepareSegment: function(points) {
        return {
            line: points
        };
    },
    _parseLineOptions: function(options, defaultColor) {
        return {
            stroke: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(options.color, true) || defaultColor,
            "stroke-width": options.width,
            dashStyle: options.dashStyle || "solid"
        };
    },
    _parseStyle: function(options, defaultColor) {
        return {
            elements: this._parseLineOptions(options, defaultColor)
        };
    },
    _applyStyle: function(style) {
        this._elementsGroup && this._elementsGroup.attr(style.elements);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._graphics || [], function(_, graphic) {
            graphic.line && graphic.line.attr({
                "stroke-width": style.elements["stroke-width"]
            }).sharp();
        });
    },
    _drawElement: function(segment, group) {
        return {
            line: this._createMainElement(segment.line, {
                "stroke-width": this._styles.normal.elements["stroke-width"]
            }).append(group)
        };
    },
    _removeElement: function(element) {
        element.line.remove();
    },
    _updateElement: function(element, segment, animate, animationComplete) {
        const params = {
            points: segment.line
        };
        const lineElement = element.line;
        animate ? lineElement.animate(params, {}, animationComplete) : lineElement.attr(params);
    },
    _animateComplete: function() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._animateComplete.call(this);
        this._markersGroup && this._markersGroup.animate({
            opacity: 1
        }, {
            duration: this._defaultDuration
        });
    },
    _animate: function() {
        const that = this;
        const lastIndex = that._graphics.length - 1;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(that._graphics || [], function(i, elem) {
            let complete;
            if (i === lastIndex) {
                complete = function() {
                    that._animateComplete();
                };
            }
            that._updateElement(elem, that._segments[i], true, complete);
        });
    },
    _drawPoint: function(options) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._drawPoint.call(this, {
            point: options.point,
            groups: options.groups
        });
    },
    _createMainElement: function(points, settings) {
        return this._renderer.path(points, "line").attr(settings);
    },
    _sortPoints: function(points, rotated) {
        return rotated ? points.sort(function(p1, p2) {
            return p2.y - p1.y;
        }) : points.sort(function(p1, p2) {
            return p1.x - p2.x;
        });
    },
    _drawSegment: function(points, animationEnabled, segmentCount, lastSegment) {
        const that = this;
        const rotated = that._options.rotated;
        const segment = that._prepareSegment(points, rotated, lastSegment);
        that._segments.push(segment);
        if (!that._graphics[segmentCount]) {
            that._graphics[segmentCount] = that._drawElement(animationEnabled ? that._getDefaultSegment(segment) : segment, that._elementsGroup);
        } else if (!animationEnabled) {
            that._updateElement(that._graphics[segmentCount], segment);
        }
    },
    _getTrackerSettings: function() {
        const defaultTrackerWidth = this._defaultTrackerWidth;
        const strokeWidthFromElements = this._styles.normal.elements["stroke-width"];
        return {
            "stroke-width": strokeWidthFromElements > defaultTrackerWidth ? strokeWidthFromElements : defaultTrackerWidth,
            fill: "none"
        };
    },
    _getMainPointsFromSegment: function(segment) {
        return segment.line;
    },
    _drawTrackerElement: function(segment) {
        return this._createMainElement(this._getMainPointsFromSegment(segment), this._getTrackerSettings(segment));
    },
    _updateTrackerElement: function(segment, element) {
        const settings = this._getTrackerSettings(segment);
        settings.points = this._getMainPointsFromSegment(segment);
        element.attr(settings);
    },
    checkSeriesViewportCoord (axis, coord) {
        if (!__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].checkSeriesViewportCoord.call(this)) {
            return false;
        }
        const range = axis.isArgumentAxis ? this.getArgumentRange() : this.getViewport();
        const min = axis.getTranslator().translate(range.categories ? range.categories[0] : range.min);
        const max = axis.getTranslator().translate(range.categories ? range.categories[range.categories.length - 1] : range.max);
        const rotated = this.getOptions().rotated;
        const inverted = axis.getOptions().inverted;
        return axis.isArgumentAxis && (!rotated && !inverted || rotated && inverted) || !axis.isArgumentAxis && (rotated && !inverted || !rotated && inverted) ? coord >= min && coord <= max : coord >= max && coord <= min;
    }
};
const lineSeries = chart.line = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], lineMethods, {
    getPointCenterByArg (arg) {
        const value = this.getArgumentAxis().getTranslator().translate(arg);
        return {
            x: value,
            y: value
        };
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord = null;
        const nearestPoints = this._getNearestPointsByCoord(coord, isArgument);
        const needValueCoord = isArgument && !this._options.rotated || !isArgument && this._options.rotated;
        for(let i = 0; i < nearestPoints.length; i++){
            const p = nearestPoints[i];
            const k = (p[1].vy - p[0].vy) / (p[1].vx - p[0].vx);
            const b = p[0].vy - p[0].vx * k;
            let tmpCoord;
            if (p[1].vx - p[0].vx === 0) {
                tmpCoord = needValueCoord ? p[0].vy : p[0].vx;
            } else {
                tmpCoord = needValueCoord ? k * coord + b : (coord - b) / k;
            }
            if (this._checkAxisVisibleAreaCoord(!isArgument, tmpCoord)) {
                oppositeCoord = tmpCoord;
                break;
            }
        }
        return oppositeCoord;
    }
});
chart.stepline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, lineSeries, {
    _calculateStepLinePoints (points) {
        const segment = [];
        const coordName = this._options.rotated ? "x" : "y";
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(points, function(i, pt) {
            let point;
            if (!i) {
                segment.push(pt);
                return;
            }
            const step = segment[segment.length - 1][coordName];
            if (step !== pt[coordName]) {
                point = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clone"])(pt);
                point[coordName] = step;
                segment.push(point);
            }
            segment.push(pt);
        });
        return segment;
    },
    _prepareSegment: function(points) {
        return lineSeries._prepareSegment(this._calculateStepLinePoints(points));
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord;
        const rotated = this._options.rotated;
        const isOpposite = !isArgument && !rotated || isArgument && rotated;
        const coordName = !isOpposite ? "vx" : "vy";
        const oppositeCoordName = !isOpposite ? "vy" : "vx";
        const nearestPoints = this._getNearestPointsByCoord(coord, isArgument);
        for(let i = 0; i < nearestPoints.length; i++){
            const p = nearestPoints[i];
            let tmpCoord;
            if (isArgument) {
                tmpCoord = coord !== p[1][coordName] ? p[0][oppositeCoordName] : p[1][oppositeCoordName];
            } else {
                tmpCoord = coord === p[0][coordName] ? p[0][oppositeCoordName] : p[1][oppositeCoordName];
            }
            if (this._checkAxisVisibleAreaCoord(!isArgument, tmpCoord)) {
                oppositeCoord = tmpCoord;
                break;
            }
        }
        return oppositeCoord;
    }
});
chart.spline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, lineSeries, {
    _calculateBezierPoints: function(src, rotated) {
        const bezierPoints = [];
        const pointsCopy = src;
        const checkExtremum = function(otherPointCoord, pointCoord, controlCoord) {
            return otherPointCoord > pointCoord && controlCoord > otherPointCoord || otherPointCoord < pointCoord && controlCoord < otherPointCoord ? otherPointCoord : controlCoord;
        };
        if (1 !== pointsCopy.length) {
            pointsCopy.forEach(function(curPoint, i) {
                let leftControlX;
                let leftControlY;
                let rightControlX;
                let rightControlY;
                const prevPoint = pointsCopy[i - 1];
                const nextPoint = pointsCopy[i + 1];
                let x1;
                let x2;
                let y1;
                let y2;
                let a;
                let b;
                let c;
                let xc;
                let yc;
                let shift;
                if (!i || i === pointsCopy.length - 1) {
                    bezierPoints.push(curPoint, curPoint);
                    return;
                }
                const xCur = curPoint.x;
                const yCur = curPoint.y;
                x1 = prevPoint.x;
                x2 = nextPoint.x;
                y1 = prevPoint.y;
                y2 = nextPoint.y;
                const curIsExtremum = !!(!rotated && (yCur <= prevPoint.y && yCur <= nextPoint.y || yCur >= prevPoint.y && yCur >= nextPoint.y) || rotated && (xCur <= prevPoint.x && xCur <= nextPoint.x || xCur >= prevPoint.x && xCur >= nextPoint.x));
                if (curIsExtremum) {
                    if (!rotated) {
                        rightControlY = leftControlY = yCur;
                        rightControlX = (xCur + nextPoint.x) / 2;
                        leftControlX = (xCur + prevPoint.x) / 2;
                    } else {
                        rightControlX = leftControlX = xCur;
                        rightControlY = (yCur + nextPoint.y) / 2;
                        leftControlY = (yCur + prevPoint.y) / 2;
                    }
                } else {
                    a = y2 - y1;
                    b = x1 - x2;
                    c = y1 * x2 - x1 * y2;
                    if (!rotated) {
                        if (!b) {
                            bezierPoints.push(curPoint, curPoint, curPoint);
                            return;
                        }
                        xc = xCur;
                        yc = -1 * (a * xc + c) / b;
                        shift = yc - yCur;
                        y1 -= shift;
                        y2 -= shift;
                    } else {
                        if (!a) {
                            bezierPoints.push(curPoint, curPoint, curPoint);
                            return;
                        }
                        yc = yCur;
                        xc = -1 * (b * yc + c) / a;
                        shift = xc - xCur;
                        x1 -= shift;
                        x2 -= shift;
                    }
                    rightControlX = (xCur + .5 * x2) / 1.5;
                    rightControlY = (yCur + .5 * y2) / 1.5;
                    leftControlX = (xCur + .5 * x1) / 1.5;
                    leftControlY = (yCur + .5 * y1) / 1.5;
                }
                if (!rotated) {
                    leftControlY = checkExtremum(prevPoint.y, yCur, leftControlY);
                    rightControlY = checkExtremum(nextPoint.y, yCur, rightControlY);
                } else {
                    leftControlX = checkExtremum(prevPoint.x, xCur, leftControlX);
                    rightControlX = checkExtremum(nextPoint.x, xCur, rightControlX);
                }
                const leftPoint = clonePoint(curPoint, leftControlX, leftControlY);
                const rightPoint = clonePoint(curPoint, rightControlX, rightControlY);
                bezierPoints.push(leftPoint, curPoint, rightPoint);
            });
        } else {
            bezierPoints.push(pointsCopy[0]);
        }
        return bezierPoints;
    },
    _prepareSegment: function(points, rotated) {
        return lineSeries._prepareSegment(this._calculateBezierPoints(points, rotated));
    },
    _createMainElement: function(points, settings) {
        return this._renderer.path(points, "bezier").attr(settings);
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord = null;
        const isOpposite = !isArgument && !this._options.rotated || isArgument && this._options.rotated;
        const coordName = !isOpposite ? "vx" : "vy";
        const bezierCoordName = !isOpposite ? "x" : "y";
        const oppositeCoordName = !isOpposite ? "vy" : "vx";
        const bezierOppositeCoordName = !isOpposite ? "y" : "x";
        const axis = !isArgument ? this.getArgumentAxis() : this.getValueAxis();
        const visibleArea = axis.getVisibleArea();
        const nearestPoints = this._getNearestPointsByCoord(coord, isArgument);
        for(let i = 0; i < nearestPoints.length; i++){
            const p = nearestPoints[i];
            if (1 === p.length) {
                visibleArea[0] <= p[0][oppositeCoordName] && visibleArea[1] >= p[0][oppositeCoordName] && (oppositeCoord = p[0][oppositeCoordName]);
            } else {
                const ts = obtainCubicBezierTCoef(coord, p[0][coordName], p[1][bezierCoordName], p[2][bezierCoordName], p[3][coordName]);
                ts.forEach((t)=>{
                    if (t >= 0 && t <= 1) {
                        const tmpCoord = Math.pow(1 - t, 3) * p[0][oppositeCoordName] + 3 * Math.pow(1 - t, 2) * t * p[1][bezierOppositeCoordName] + 3 * (1 - t) * t * t * p[2][bezierOppositeCoordName] + t * t * t * p[3][oppositeCoordName];
                        if (visibleArea[0] <= tmpCoord && visibleArea[1] >= tmpCoord) {
                            oppositeCoord = tmpCoord;
                        }
                    }
                });
            }
            if (null !== oppositeCoord) {
                break;
            }
        }
        return oppositeCoord;
    },
    _getNearestPoints (point, nextPoint, bezierPoints) {
        const index = bezierPoints.indexOf(point);
        return [
            point,
            bezierPoints[index + 1],
            bezierPoints[index + 2],
            nextPoint
        ];
    },
    _getBezierPoints () {
        return this._segments.length > 0 ? this._segments.reduce((a, seg)=>a.concat(seg.line), []) : [];
    }
});
polar.line = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"], lineMethods, {
    _sortPoints: function(points) {
        return points;
    },
    _prepareSegment: function(points, rotated, lastSegment) {
        let preparedPoints = [];
        const centerPoint = this.getValueAxis().getCenter();
        let i;
        lastSegment && this._closeSegment(points);
        if (this.argumentAxisType !== DISCRETE && this.valueAxisType !== DISCRETE) {
            for(i = 1; i < points.length; i++){
                preparedPoints = preparedPoints.concat(this._getTangentPoints(points[i], points[i - 1], centerPoint, i === points.length - 1));
            }
            if (!preparedPoints.length) {
                preparedPoints = points;
            }
        } else {
            return lineSeries._prepareSegment.call(this, points);
        }
        return {
            line: preparedPoints
        };
    },
    _getRemainingAngle: function(angle) {
        const normAngle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(angle);
        return angle >= 0 ? 360 - normAngle : -normAngle;
    },
    _closeSegment (points) {
        const point = this._segments.length ? this._segments[0].line[0] : points[0];
        let newPoint = clonePoint(point, point.x, point.y, point.angle);
        newPoint = this._modifyReflectedPoint(newPoint, points.at(-1));
        if (newPoint) {
            points.push(newPoint);
        }
    },
    _modifyReflectedPoint (point, lastPoint) {
        if (lastPoint.angle === point.angle) {
            return;
        }
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(round(lastPoint.angle)) === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(round(point.angle))) {
            point.angle = lastPoint.angle;
        } else {
            const differenceAngle = lastPoint.angle - point.angle;
            point.angle = lastPoint.angle + this._getRemainingAngle(differenceAngle);
        }
        return point;
    },
    _getTangentPoints: function(point, prevPoint, centerPoint, isLastSegment) {
        let tangentPoints = [];
        const betweenAngle = Math.round(prevPoint.angle - point.angle);
        const tan = (prevPoint.radius - point.radius) / betweenAngle;
        let i;
        if (0 === betweenAngle) {
            tangentPoints = [
                prevPoint,
                point
            ];
        } else if (betweenAngle > 0) {
            const angle = isLastSegment ? betweenAngle : betweenAngle - 1;
            for(i = angle; i >= 0; i--){
                tangentPoints.push(getTangentPoint(point, prevPoint, centerPoint, tan, i));
            }
        } else {
            const angle = isLastSegment ? betweenAngle : betweenAngle + 1;
            for(i = 0; i >= angle; i--){
                tangentPoints.push(getTangentPoint(point, prevPoint, centerPoint, tan, betweenAngle - i));
            }
        }
        return tangentPoints;
    },
    getSeriesPairCoord (params, isArgument) {
        const that = this;
        const argAxis = that.getArgumentAxis();
        const paramName = isArgument ? "angle" : "radius";
        const coordParam = params[paramName];
        const centerPoint = argAxis.getCenter();
        const isInsideInterval = (prevPoint, point, _ref)=>{
            let { x: x, y: y } = _ref;
            return (p1 = {
                x: x,
                y: y
            }, p2 = centerPoint, sqrt(pow(p1.x - p2.x, 2) + pow(p1.y - p2.y, 2))) <= argAxis.getRadius() && min(prevPoint.x, point.x) <= x && max(prevPoint.x, point.x) >= x && min(prevPoint.y, point.y) <= y && max(prevPoint.y, point.y) >= y;
            //TURBOPACK unreachable
            ;
            var p1, p2;
        };
        let coords;
        const neighborPoints = that.getNeighborPoints(coordParam, paramName);
        if (1 === neighborPoints.length) {
            coords = neighborPoints[0];
        } else if (neighborPoints.length > 1) {
            const prevPoint = neighborPoints[0];
            const point = neighborPoints[1];
            if (that.argumentAxisType !== DISCRETE && that.valueAxisType !== DISCRETE) {
                let tan;
                let stepAngle;
                if (isArgument) {
                    tan = (prevPoint.radius - point.radius) / (prevPoint.angle - point.angle);
                    stepAngle = coordParam - point.angle;
                } else {
                    tan = (prevPoint.radius - point.radius) / (prevPoint.angle - point.angle);
                    stepAngle = (coordParam - point.radius) / tan;
                }
                coords = getTangentPoint(point, prevPoint, centerPoint, tan, stepAngle);
            } else if (isArgument) {
                const cosSin = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getCosAndSin"])(-coordParam);
                const k1 = (point.y - prevPoint.y) / (point.x - prevPoint.x);
                const b1 = prevPoint.y - prevPoint.x * k1;
                const k2 = cosSin.sin / cosSin.cos;
                const b2 = centerPoint.y - k2 * centerPoint.x;
                const x = (b2 - b1) / (k1 - k2);
                const y = k1 * x + b1;
                if (isInsideInterval(prevPoint, point, {
                    x: x,
                    y: y
                })) {
                    const quarter = abs((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_math$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["trunc"])((360 + coordParam) / 90) % 4);
                    if (0 === quarter && x >= centerPoint.x && y <= centerPoint.y || 1 === quarter && x <= centerPoint.x && y <= centerPoint.y || 2 === quarter && x <= centerPoint.x && y >= centerPoint.y || 3 === quarter && x >= centerPoint.x && y >= centerPoint.y) {
                        coords = {
                            x: x,
                            y: y
                        };
                    }
                }
            } else {
                const k = (point.y - prevPoint.y) / (point.x - prevPoint.x);
                const y0 = prevPoint.y - prevPoint.x * k;
                const a = 1 + k * k;
                const b = -2 * centerPoint.x + 2 * k * y0 - 2 * k * centerPoint.y;
                const c = -pow(coordParam, 2) + pow(y0 - centerPoint.y, 2) + pow(centerPoint.x, 2);
                const d = b * b - 4 * a * c;
                if (d >= 0) {
                    const x1 = (-b - sqrt(d)) / (2 * a);
                    const x2 = (-b + sqrt(d)) / (2 * a);
                    const y1 = k * x1 + y0;
                    const y2 = k * x2 + y0;
                    coords = isInsideInterval(prevPoint, point, {
                        x: x1,
                        y: y1
                    }) ? {
                        x: x1,
                        y: y1
                    } : isInsideInterval(prevPoint, point, {
                        x: x2,
                        y: y2
                    }) ? {
                        x: x2,
                        y: y2
                    } : void 0;
                }
            }
        }
        return coords;
    },
    getNeighborPoints (param, paramName) {
        let points = this.getPoints();
        const neighborPoints = [];
        if (this.getOptions().closed) {
            points = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, [], points);
            const lastPoint = points[points.length - 1];
            const firstPointCopy = clonePoint(points[0], points[0].x, points[0].y, points[0].angle);
            const lastPointCopy = clonePoint(lastPoint, lastPoint.x, lastPoint.y, lastPoint.angle);
            const rearwardRefPoint = this._modifyReflectedPoint(firstPointCopy, lastPoint);
            const forwardRefPoint = this._modifyReflectedPoint(lastPointCopy, points[0]);
            if (forwardRefPoint) {
                points.unshift(forwardRefPoint);
            }
            if (rearwardRefPoint) {
                points.push(rearwardRefPoint);
            }
        }
        for(let i = 1; i < points.length; i++){
            if (points[i - 1][paramName] === param) {
                neighborPoints.push(points[i - 1]);
            } else if (points[i][paramName] === param) {
                neighborPoints.push(points[i]);
            } else if (points[i][paramName] > param && points[i - 1][paramName] < param || points[i - 1][paramName] > param && points[i][paramName] < param) {
                neighborPoints.push(points[i - 1]);
                neighborPoints.push(points[i]);
            }
            if (neighborPoints.length > 0) {
                break;
            }
        }
        return neighborPoints;
    }
});
;
}),
"[project]/node_modules/devextreme/esm/viz/series/area_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/area_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart,
    "polar": ()=>polar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/object.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/line_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
;
;
;
;
const chartLineSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].line;
const polarLineSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"].line;
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const calculateBezierPoints = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline._calculateBezierPoints;
const chart = {};
const polar = {};
const baseAreaMethods = {
    _createBorderElement: chartLineSeries._createMainElement,
    _createLegendState: function(styleOptions, defaultColor) {
        return {
            fill: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(styleOptions.color) || defaultColor,
            opacity: styleOptions.opacity,
            hatching: styleOptions.hatching,
            filter: styleOptions.highlight
        };
    },
    _getColorId: function(options) {
        var _options$color;
        return null === (_options$color = options.color) || void 0 === _options$color ? void 0 : _options$color.fillId;
    },
    getValueRangeInitialValue: function() {
        if ("logarithmic" !== this.valueAxisType && "datetime" !== this.valueType && false !== this.showZero) {
            return 0;
        } else {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].getValueRangeInitialValue.call(this);
        }
    },
    _getDefaultSegment: function(segment) {
        const defaultSegment = chartLineSeries._getDefaultSegment(segment);
        defaultSegment.area = defaultSegment.line.concat(defaultSegment.line.slice().reverse());
        return defaultSegment;
    },
    _updateElement: function(element, segment, animate, complete) {
        const lineParams = {
            points: segment.line
        };
        const areaParams = {
            points: segment.area
        };
        const borderElement = element.line;
        if (animate) {
            borderElement && borderElement.animate(lineParams);
            element.area.animate(areaParams, {}, complete);
        } else {
            borderElement && borderElement.attr(lineParams);
            element.area.attr(areaParams);
        }
    },
    _removeElement: function(element) {
        element.line && element.line.remove();
        element.area.remove();
    },
    _drawElement: function(segment) {
        return {
            line: this._bordersGroup && this._createBorderElement(segment.line, {
                "stroke-width": this._styles.normal.border["stroke-width"]
            }).append(this._bordersGroup),
            area: this._createMainElement(segment.area).append(this._elementsGroup)
        };
    },
    _applyStyle: function(style) {
        this._elementsGroup && this._elementsGroup.smartAttr(style.elements);
        this._bordersGroup && this._bordersGroup.attr(style.border);
        (this._graphics || []).forEach(function(graphic) {
            graphic.line && graphic.line.attr({
                "stroke-width": style.border["stroke-width"]
            }).sharp();
        });
    },
    _parseStyle: function(options, defaultColor, defaultBorderColor) {
        const borderOptions = options.border || {};
        const borderStyle = chartLineSeries._parseLineOptions(borderOptions, defaultBorderColor);
        borderStyle.stroke = borderOptions.visible && borderStyle["stroke-width"] ? borderStyle.stroke : "none";
        borderStyle["stroke-width"] = borderStyle["stroke-width"] || 1;
        var _options_highlight;
        return {
            border: borderStyle,
            elements: {
                stroke: "none",
                fill: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(options.color) || defaultColor,
                hatching: options.hatching,
                opacity: options.opacity,
                filter: (_options_highlight = options.highlight) !== null && _options_highlight !== void 0 ? _options_highlight : null
            }
        };
    },
    _areBordersVisible: function() {
        const options = this._options;
        return options.border.visible || options.hoverStyle.border.visible || options.selectionStyle.border.visible;
    },
    _createMainElement: function(points, settings) {
        return this._renderer.path(points, "area").attr(settings);
    },
    _getTrackerSettings: function(segment) {
        return {
            "stroke-width": segment.singlePointSegment ? this._defaultTrackerWidth : 0
        };
    },
    _getMainPointsFromSegment: function(segment) {
        return segment.area;
    }
};
function createAreaPoints(points) {
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(points, function(pt) {
        return pt.getCoords();
    }).concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(points.slice().reverse(), function(pt) {
        return pt.getCoords(true);
    }));
}
const areaSeries = chart.area = _extend({}, chartLineSeries, baseAreaMethods, {
    _prepareSegment (points, rotated) {
        const processedPoints = this._processSinglePointsAreaSegment(points, rotated);
        const areaPoints = createAreaPoints(processedPoints);
        const argAxis = this.getArgumentAxis();
        if (argAxis.getAxisPosition) {
            const argAxisPosition = argAxis.getAxisPosition();
            const axisOptions = argAxis.getOptions();
            const edgeOffset = (!rotated ? -1 : 1) * Math.round(axisOptions.width / 2);
            if (axisOptions.visible) {
                areaPoints.forEach((p, i)=>{
                    if (p) {
                        const index = 1 === points.length ? 0 : i < points.length ? i : areaPoints.length - 1 - i;
                        rotated && p.x === points[index].defaultX && p.x === argAxisPosition - argAxis.getAxisShift() && (p.x += edgeOffset);
                        !rotated && p.y === points[index].defaultY && p.y === argAxisPosition - argAxis.getAxisShift() && (p.y += edgeOffset);
                    }
                });
            }
        }
        return {
            line: processedPoints,
            area: areaPoints,
            singlePointSegment: processedPoints !== points
        };
    },
    _processSinglePointsAreaSegment: function(points, rotated) {
        if (points && 1 === points.length) {
            const p = points[0];
            const p1 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clone"])(p);
            p1[rotated ? "y" : "x"] += 1;
            p1.argument = null;
            return [
                p,
                p1
            ];
        }
        return points;
    }
});
polar.area = _extend({}, polarLineSeries, baseAreaMethods, {
    _prepareSegment: function(points, rotated, lastSegment) {
        lastSegment && polarLineSeries._closeSegment.call(this, points);
        return areaSeries._prepareSegment.call(this, points);
    },
    _processSinglePointsAreaSegment: function(points) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"].line._prepareSegment.call(this, points).line;
    }
});
chart.steparea = _extend({}, areaSeries, {
    _prepareSegment: function(points, rotated) {
        const stepLineSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].stepline;
        points = areaSeries._processSinglePointsAreaSegment(points, rotated);
        return areaSeries._prepareSegment.call(this, stepLineSeries._calculateStepLinePoints.call(this, points), rotated);
    },
    getSeriesPairCoord: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].stepline.getSeriesPairCoord
});
chart.splinearea = _extend({}, areaSeries, {
    _areaPointsToSplineAreaPoints: function(areaPoints) {
        const previousMiddlePoint = areaPoints[areaPoints.length / 2 - 1];
        const middlePoint = areaPoints[areaPoints.length / 2];
        areaPoints.splice(areaPoints.length / 2, 0, {
            x: previousMiddlePoint.x,
            y: previousMiddlePoint.y
        }, {
            x: middlePoint.x,
            y: middlePoint.y
        });
    },
    _prepareSegment: function(points, rotated) {
        const processedPoints = areaSeries._processSinglePointsAreaSegment(points, rotated);
        const areaSegment = areaSeries._prepareSegment.call(this, calculateBezierPoints(processedPoints, rotated));
        this._areaPointsToSplineAreaPoints(areaSegment.area);
        areaSegment.singlePointSegment = processedPoints !== points;
        return areaSegment;
    },
    _getDefaultSegment: function(segment) {
        const areaDefaultSegment = areaSeries._getDefaultSegment(segment);
        this._areaPointsToSplineAreaPoints(areaDefaultSegment.area);
        return areaDefaultSegment;
    },
    _createMainElement: function(points, settings) {
        return this._renderer.path(points, "bezierarea").attr(settings);
    },
    _createBorderElement: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline._createMainElement,
    getSeriesPairCoord: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline.getSeriesPairCoord,
    _getNearestPoints: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline._getNearestPoints,
    _getBezierPoints: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline._getBezierPoints,
    obtainCubicBezierTCoef: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline.obtainCubicBezierTCoef
});
;
}),
"[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/bar_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart,
    "polar": ()=>polar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/area_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
;
;
;
;
const areaSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].area;
;
const chartSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"];
const polarSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"];
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const _each = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"];
const chart = {};
const polar = {};
const baseBarSeriesMethods = {
    _createLegendState: function(styleOptions, defaultColor) {
        return {
            fill: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(styleOptions.color) || defaultColor,
            hatching: styleOptions.hatching,
            filter: styleOptions.highlight
        };
    },
    _getColorId: areaSeries._getColorId,
    _parsePointStyle: function(style, defaultColor, defaultBorderColor) {
        const color = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(style.color) || defaultColor;
        const base = chartSeries._parsePointStyle.call(this, style, color, defaultBorderColor);
        base.fill = color;
        base.hatching = style.hatching;
        base.filter = style.highlight;
        base.dashStyle = style.border && style.border.dashStyle || "solid";
        delete base.r;
        return base;
    },
    _applyMarkerClipRect: function(settings) {
        settings["clip-path"] = null;
    },
    _setGroupsSettings: function(animationEnabled, firstDrawing) {
        let settings = {};
        chartSeries._setGroupsSettings.apply(this, arguments);
        if (animationEnabled && firstDrawing) {
            settings = this._getAffineCoordOptions();
        } else if (!animationEnabled) {
            settings = {
                scaleX: 1,
                scaleY: 1,
                translateX: 0,
                translateY: 0
            };
        }
        this._markersGroup.attr(settings);
    },
    _drawPoint: function(options) {
        options.hasAnimation = options.hasAnimation && !options.firstDrawing;
        options.firstDrawing = false;
        chartSeries._drawPoint.call(this, options);
    },
    _getMainColor: function() {
        return this._options.mainSeriesColor;
    },
    _createPointStyles: function(pointOptions) {
        var _pointOptions$color;
        const that = this;
        const mainColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(pointOptions.color, true) || that._getMainColor();
        const colorId = null === (_pointOptions$color = pointOptions.color) || void 0 === _pointOptions$color ? void 0 : _pointOptions$color.fillId;
        const hoverStyle = pointOptions.hoverStyle || {};
        const selectionStyle = pointOptions.selectionStyle || {};
        if (colorId) {
            that._turnOffHatching(hoverStyle, selectionStyle);
        }
        return {
            labelColor: mainColor,
            normal: that._parsePointStyle(pointOptions, mainColor, mainColor),
            hover: that._parsePointStyle(hoverStyle, colorId || mainColor, mainColor),
            selection: that._parsePointStyle(selectionStyle, colorId || mainColor, mainColor)
        };
    },
    _updatePointsVisibility: function() {
        const visibility = this._options.visible;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._points, function(_, point) {
            point._options.visible = visibility;
        });
    },
    _getOptionsForPoint: function() {
        return this._options;
    },
    _animate: function(firstDrawing) {
        const that = this;
        that._animatePoints(firstDrawing, function() {
            that._animateComplete();
        }, function(drawnPoints, complete) {
            const lastPointIndex = drawnPoints.length - 1;
            _each(drawnPoints || [], function(i, point) {
                point.animate(i === lastPointIndex ? complete : void 0, point.getMarkerCoords());
            });
        });
    },
    getValueRangeInitialValue: areaSeries.getValueRangeInitialValue,
    _patchMarginOptions: function(options) {
        var _this$getArgumentAxis;
        options.checkInterval = !this.useAggregation() || (null === (_this$getArgumentAxis = this.getArgumentAxis()) || void 0 === _this$getArgumentAxis ? void 0 : _this$getArgumentAxis.aggregatedPointBetweenTicks());
        return options;
    },
    _defaultAggregator: "sum",
    _defineDrawingState () {},
    usePointsToDefineAutoHiding: ()=>false
};
chart.bar = _extend({}, chartSeries, baseBarSeriesMethods, {
    _getAffineCoordOptions: function() {
        const rotated = this._options.rotated;
        const direction = rotated ? "X" : "Y";
        const settings = {
            scaleX: rotated ? .001 : 1,
            scaleY: rotated ? 1 : .001
        };
        settings["translate" + direction] = this.getValueAxis().getTranslator().translate("canvas_position_default");
        return settings;
    },
    _animatePoints: function(firstDrawing, complete, animateFunc) {
        const that = this;
        that._markersGroup.animate({
            scaleX: 1,
            scaleY: 1,
            translateY: 0,
            translateX: 0
        }, void 0, complete);
        if (!firstDrawing) {
            animateFunc(that._drawnPoints, complete);
        }
    },
    checkSeriesViewportCoord (axis, coord) {
        if (!chartSeries.checkSeriesViewportCoord.call(this)) {
            return false;
        }
        if (axis.isArgumentAxis) {
            return true;
        }
        const translator = axis.getTranslator();
        const range = this.getViewport();
        const min = translator.translate(range.categories ? range.categories[0] : range.min);
        const max = translator.translate(range.categories ? range.categories[range.categories.length - 1] : range.max);
        const rotated = this.getOptions().rotated;
        const inverted = axis.getOptions().inverted;
        return rotated && !inverted || !rotated && inverted ? coord >= min && coord <= max : coord >= max && coord <= min;
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord = null;
        const { rotated: rotated } = this._options;
        const isOpposite = !isArgument && !rotated || isArgument && rotated;
        const coordName = isOpposite ? "vy" : "vx";
        const oppositeCoordName = isOpposite ? "vx" : "vy";
        const points = this.getPoints();
        for(let i = 0; i < points.length; i++){
            const p = points[i];
            let tmpCoord;
            if (isArgument) {
                tmpCoord = p.getCenterCoord()[coordName[1]] === coord ? p[oppositeCoordName] : void 0;
            } else {
                tmpCoord = p[coordName] === coord ? p[oppositeCoordName] : void 0;
            }
            if (this._checkAxisVisibleAreaCoord(!isArgument, tmpCoord)) {
                oppositeCoord = tmpCoord;
                break;
            }
        }
        return oppositeCoord;
    }
});
polar.bar = _extend({}, polarSeries, baseBarSeriesMethods, {
    _animatePoints: function(firstDrawing, complete, animateFunc) {
        animateFunc(this._drawnPoints, complete);
    },
    _setGroupsSettings: chartSeries._setGroupsSettings,
    _drawPoint: function(point, groups, animationEnabled) {
        chartSeries._drawPoint.call(this, point, groups, animationEnabled);
    },
    _parsePointStyle: function(style) {
        const base = baseBarSeriesMethods._parsePointStyle.apply(this, arguments);
        base.opacity = style.opacity;
        return base;
    },
    _createGroups: chartSeries._createGroups,
    _setMarkerGroupSettings: function() {
        const markersSettings = this._createPointStyles(this._getMarkerGroupOptions()).normal;
        markersSettings.class = "dxc-markers";
        this._applyMarkerClipRect(markersSettings);
        const groupSettings = _extend({}, markersSettings);
        delete groupSettings.opacity;
        this._markersGroup.attr(groupSettings);
    },
    getSeriesPairCoord (params, isArgument) {
        let coords = null;
        const paramName = isArgument ? "argument" : "radius";
        const points = this.getVisiblePoints();
        const argAxis = this.getArgumentAxis();
        const startAngle = argAxis.getAngles()[0];
        for(let i = 0; i < points.length; i++){
            const p = points[i];
            const tmpPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(p[paramName]) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(params[paramName]) && p[paramName].valueOf() === params[paramName].valueOf() ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["convertPolarToXY"])(argAxis.getCenter(), startAngle, -argAxis.getTranslatedAngle(p.angle), p.radius) : void 0;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(tmpPoint)) {
                coords = tmpPoint;
                break;
            }
        }
        return coords;
    },
    _createLegendState: areaSeries._createLegendState
});
;
}),
"[project]/node_modules/devextreme/esm/viz/series/range_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/range_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/area_series.js [app-client] (ecmascript)");
;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
;
;
;
;
;
;
const barSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].bar;
const areaSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].area;
const chart = {};
const baseRangeSeries = {
    areErrorBarsVisible: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _createErrorBarGroup: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _checkData: function(data, skippedFields) {
        const valueFields = this.getValueFields();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._checkData.call(this, data, skippedFields, {
            minValue: valueFields[0],
            value: valueFields[1]
        }) && data.minValue === data.minValue;
    },
    getValueRangeInitialValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].getValueRangeInitialValue,
    _getPointDataSelector: function(data) {
        const valueFields = this.getValueFields();
        const val1Field = valueFields[0];
        const val2Field = valueFields[1];
        const tagField = this.getTagField();
        const argumentField = this.getArgumentField();
        return (data)=>({
                tag: data[tagField],
                minValue: this._processEmptyValue(data[val1Field]),
                value: this._processEmptyValue(data[val2Field]),
                argument: data[argumentField],
                data: data
            });
    },
    _defaultAggregator: "range",
    _aggregators: {
        range (_ref, series) {
            let { intervalStart: intervalStart, intervalEnd: intervalEnd, data: data } = _ref;
            if (!data.length) {
                return;
            }
            const valueFields = series.getValueFields();
            const val1Field = valueFields[0];
            const val2Field = valueFields[1];
            const result = data.reduce((result, item)=>{
                const val1 = item[val1Field];
                const val2 = item[val2Field];
                if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(val1) || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(val2)) {
                    return result;
                }
                result[val1Field] = Math.min(result[val1Field], Math.min(val1, val2));
                result[val2Field] = Math.max(result[val2Field], Math.max(val1, val2));
                return result;
            }, {
                [val1Field]: 1 / 0,
                [val2Field]: -1 / 0,
                [series.getArgumentField()]: series._getIntervalCenter(intervalStart, intervalEnd)
            });
            if (!isFinite(result[val1Field]) || !isFinite(result[val2Field])) {
                if (data.filter((i)=>null === i[val1Field] && null === i[val2Field]).length === data.length) {
                    result[val1Field] = result[val2Field] = null;
                } else {
                    return;
                }
            }
            return result;
        }
    },
    getValueFields: function() {
        return [
            this._options.rangeValue1Field || "val1",
            this._options.rangeValue2Field || "val2"
        ];
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord = null;
        const { rotated: rotated } = this._options;
        const isOpposite = !isArgument && !rotated || isArgument && rotated;
        const coordName = isOpposite ? "vy" : "vx";
        const minCoordName = rotated ? "minX" : "minY";
        const oppositeCoordName = isOpposite ? "vx" : "vy";
        const points = this.getPoints();
        for(let i = 0; i < points.length; i++){
            const p = points[i];
            let tmpCoord;
            if (isArgument) {
                tmpCoord = p.getCenterCoord()[coordName[1]] === coord ? p[oppositeCoordName] : void 0;
            } else {
                const coords = [
                    Math.min(p[coordName], p[minCoordName]),
                    Math.max(p[coordName], p[minCoordName])
                ];
                tmpCoord = coord >= coords[0] && coord <= coords[1] ? p[oppositeCoordName] : void 0;
            }
            if (this._checkAxisVisibleAreaCoord(!isArgument, tmpCoord)) {
                oppositeCoord = tmpCoord;
                break;
            }
        }
        return oppositeCoord;
    }
};
chart.rangebar = _extend({}, barSeries, baseRangeSeries);
chart.rangearea = _extend({}, areaSeries, {
    _drawPoint: function(options) {
        const point = options.point;
        if (point.isInVisibleArea()) {
            point.clearVisibility();
            point.draw(this._renderer, options.groups);
            this._drawnPoints.push(point);
            if (!point.visibleTopMarker) {
                point.hideMarker("top");
            }
            if (!point.visibleBottomMarker) {
                point.hideMarker("bottom");
            }
        } else {
            point.setInvisibility();
        }
    },
    _prepareSegment: function(points, rotated) {
        const processedPoints = this._processSinglePointsAreaSegment(points, rotated);
        const processedMinPointsCoords = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(processedPoints, function(pt) {
            return pt.getCoords(true);
        });
        return {
            line: processedPoints,
            bottomLine: processedMinPointsCoords,
            area: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(processedPoints, function(pt) {
                return pt.getCoords();
            }).concat(processedMinPointsCoords.slice().reverse()),
            singlePointSegment: processedPoints !== points
        };
    },
    _getDefaultSegment: function(segment) {
        const defaultSegment = areaSeries._getDefaultSegment.call(this, segment);
        defaultSegment.bottomLine = defaultSegment.line;
        return defaultSegment;
    },
    _removeElement: function(element) {
        areaSeries._removeElement.call(this, element);
        element.bottomLine && element.bottomLine.remove();
    },
    _drawElement: function(segment, group) {
        const drawnElement = areaSeries._drawElement.call(this, segment, group);
        drawnElement.bottomLine = this._bordersGroup && this._createBorderElement(segment.bottomLine, {
            "stroke-width": this._styles.normal.border["stroke-width"]
        }).append(this._bordersGroup);
        return drawnElement;
    },
    _applyStyle: function(style) {
        const elementsGroup = this._elementsGroup;
        const bordersGroup = this._bordersGroup;
        elementsGroup && elementsGroup.smartAttr(style.elements);
        bordersGroup && bordersGroup.attr(style.border);
        (this._graphics || []).forEach(function(graphic) {
            graphic.line && graphic.line.attr({
                "stroke-width": style.border["stroke-width"]
            });
            graphic.bottomLine && graphic.bottomLine.attr({
                "stroke-width": style.border["stroke-width"]
            });
        });
    },
    _updateElement: function(element, segment, animate, complete) {
        const bottomLineParams = {
            points: segment.bottomLine
        };
        const bottomBorderElement = element.bottomLine;
        areaSeries._updateElement.apply(this, arguments);
        if (bottomBorderElement) {
            animate ? bottomBorderElement.animate(bottomLineParams) : bottomBorderElement.attr(bottomLineParams);
        }
    }
}, baseRangeSeries);
;
}),
"[project]/node_modules/devextreme/esm/viz/series/bubble_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/bubble_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/line_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/area_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const lineSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].line;
const areaSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].area;
const chartBarSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].bar;
const polarBarSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"].bar;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const _each = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"];
const _noop = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
const chart = {};
chart.bubble = _extend({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], {
    _calculateErrorBars: _noop,
    _getMainColor: chartBarSeries._getMainColor,
    _createPointStyles: chartBarSeries._createPointStyles,
    _updatePointsVisibility: chartBarSeries._updatePointsVisibility,
    _getOptionsForPoint: chartBarSeries._getOptionsForPoint,
    _applyMarkerClipRect: lineSeries._applyElementsClipRect,
    _parsePointStyle: polarBarSeries._parsePointStyle,
    _createLegendState: areaSeries._createLegendState,
    _getColorId: areaSeries._getColorId,
    _setMarkerGroupSettings: polarBarSeries._setMarkerGroupSettings,
    areErrorBarsVisible: _noop,
    _createErrorBarGroup: _noop,
    _checkData: function(data, skippedFields) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._checkData.call(this, data, skippedFields, {
            value: this.getValueFields()[0],
            size: this.getSizeField()
        });
    },
    _getPointDataSelector: function(data, options) {
        const sizeField = this.getSizeField();
        const baseGetter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._getPointDataSelector.call(this);
        return (data)=>{
            const pointData = baseGetter(data);
            pointData.size = data[sizeField];
            return pointData;
        };
    },
    _aggregators: {
        avg (_ref, series) {
            let { data: data, intervalStart: intervalStart, intervalEnd: intervalEnd } = _ref;
            if (!data.length) {
                return;
            }
            const valueField = series.getValueFields()[0];
            const sizeField = series.getSizeField();
            const aggregate = data.reduce((result, item)=>{
                result[0] += item[valueField];
                result[1] += item[sizeField];
                result[2]++;
                return result;
            }, [
                0,
                0,
                0
            ]);
            return {
                [valueField]: aggregate[0] / aggregate[2],
                [sizeField]: aggregate[1] / aggregate[2],
                [series.getArgumentField()]: series._getIntervalCenter(intervalStart, intervalEnd)
            };
        }
    },
    getValueFields: function() {
        return [
            this._options.valueField || "val"
        ];
    },
    getSizeField: function() {
        return this._options.sizeField || "size";
    },
    _animate: function() {
        const that = this;
        const lastPointIndex = that._drawnPoints.length - 1;
        const labelsGroup = that._labelsGroup;
        const labelAnimFunc = function() {
            labelsGroup && labelsGroup.animate({
                opacity: 1
            }, {
                duration: that._defaultDuration
            });
        };
        _each(that._drawnPoints || [], function(i, p) {
            p.animate(i === lastPointIndex ? labelAnimFunc : void 0, {
                r: p.bubbleSize,
                translateX: p.x,
                translateY: p.y
            });
        });
    },
    _patchMarginOptions: function(options) {
        options.processBubbleSize = true;
        return options;
    }
});
;
}),
"[project]/node_modules/devextreme/esm/viz/series/pie_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/pie_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "donut": ()=>donut,
    "doughnut": ()=>doughnut,
    "pie": ()=>pie
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)");
;
;
;
;
;
;
const chartScatterSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"];
const barSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].bar;
const _extend = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"];
const _each = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"];
const _noop = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
const _map = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"];
const _isFinite = isFinite;
const _max = Math.max;
const ANIMATION_DURATION = .7;
const INSIDE = "inside";
const pie = _extend({}, barSeries, {
    _setGroupsSettings: function() {
        chartScatterSeries._setGroupsSettings.apply(this, arguments);
        this._labelsGroup.attr({
            "pointer-events": null
        });
    },
    _createErrorBarGroup: _noop,
    _drawPoint: function(options) {
        const point = options.point;
        const legendCallback = this._legendCallback;
        chartScatterSeries._drawPoint.call(this, options);
        !point.isVisible() && point.setInvisibility();
        point.isSelected() && legendCallback();
    },
    _getOldPoint: function(data, oldPointsByArgument, index) {
        const point = (this._points || [])[index];
        if (point) {
            oldPointsByArgument[point.argument.valueOf()] = oldPointsByArgument[point.argument.valueOf()].filter((p)=>p !== point);
        }
        return point;
    },
    adjustLabels: function(moveLabelsFromCenter) {
        return (this._points || []).reduce((r, p)=>{
            if (p._label.isVisible()) {
                p.setLabelTrackerData();
                r = p.applyWordWrap(moveLabelsFromCenter) || r;
                p.updateLabelCoord(moveLabelsFromCenter);
                return r;
            }
        }, false);
    },
    _applyElementsClipRect: _noop,
    getColor: _noop,
    areErrorBarsVisible: _noop,
    drawLabelsWOPoints: function() {
        if (this._options.label.position === INSIDE) {
            return false;
        }
        this._labelsGroup.append(this._extGroups.labelsGroup);
        (this._points || []).forEach(function(point) {
            point.drawLabel();
        });
        return true;
    },
    getPointsCount: function() {
        return this._data.filter((d)=>this._checkData(d)).length;
    },
    setMaxPointsCount: function(count) {
        this._pointsCount = count;
    },
    _getCreatingPointOptions: function(data, dataIndex) {
        return this._getPointOptions(data, dataIndex);
    },
    _updateOptions: function(options) {
        this.labelSpace = 0;
        this.innerRadius = "pie" === this.type ? 0 : options.innerRadius;
    },
    _checkData: function(data, skippedFields) {
        const base = barSeries._checkData.call(this, data, skippedFields, {
            value: this.getValueFields()[0]
        });
        return this._options.paintNullPoints ? base : base && null !== data.value;
    },
    _createGroups: chartScatterSeries._createGroups,
    _setMarkerGroupSettings: function() {
        this._markersGroup.attr({
            class: "dxc-markers"
        });
    },
    _getMainColor (data, point) {
        const pointsByArg = this.getPointsByArg(data.argument);
        const argumentIndex = point ? pointsByArg.indexOf(point) : pointsByArg.length;
        return this._options.mainSeriesColor(data.argument, argumentIndex, this._pointsCount);
    },
    _getPointOptions: function(data) {
        return this._parsePointOptions(this._preparePointOptions(), this._options.label, data);
    },
    _getRangeData: function() {
        return this._rangeData;
    },
    _createPointStyles: function(pointOptions, data, point) {
        var _pointOptions$color;
        const that = this;
        const mainColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(pointOptions.color, true) || that._getMainColor(data, point);
        const colorId = null === (_pointOptions$color = pointOptions.color) || void 0 === _pointOptions$color ? void 0 : _pointOptions$color.fillId;
        const hoverStyle = pointOptions.hoverStyle || {};
        const selectionStyle = pointOptions.selectionStyle || {};
        if (colorId) {
            that._turnOffHatching(hoverStyle, selectionStyle);
        }
        return {
            labelColor: mainColor,
            normal: that._parsePointStyle(pointOptions, mainColor, mainColor),
            hover: that._parsePointStyle(hoverStyle, colorId || mainColor, mainColor),
            selection: that._parsePointStyle(selectionStyle, colorId || mainColor, mainColor),
            legendStyles: {
                normal: that._createLegendState(pointOptions, mainColor),
                hover: that._createLegendState(hoverStyle, colorId || mainColor),
                selection: that._createLegendState(selectionStyle, colorId || mainColor)
            }
        };
    },
    _getArrangeMinShownValue: function(points, total) {
        const minSegmentSize = this._options.minSegmentSize;
        let totalMinSegmentSize = 0;
        let totalNotMinValues = 0;
        total = total || points.length;
        _each(points, function(_, point) {
            if (point.isVisible()) {
                if (point.normalInitialValue < minSegmentSize * total / 360) {
                    totalMinSegmentSize += minSegmentSize;
                } else {
                    totalNotMinValues += point.normalInitialValue;
                }
            }
        });
        return totalMinSegmentSize < 360 ? minSegmentSize * totalNotMinValues / (360 - totalMinSegmentSize) : 0;
    },
    _applyArrangeCorrection: function(points, minShownValue, total) {
        const options = this._options;
        const isClockWise = "anticlockwise" !== options.segmentsDirection;
        const shiftedAngle = _isFinite(options.startAngle) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeAngle"])(options.startAngle) : 0;
        const minSegmentSize = options.minSegmentSize;
        let percent;
        let correction = 0;
        let zeroTotalCorrection = 0;
        if (0 === total) {
            total = points.filter(function(el) {
                return el.isVisible();
            }).length;
            zeroTotalCorrection = 1;
        }
        _each(isClockWise ? points : points.concat([]).reverse(), function(_, point) {
            const val = point.isVisible() ? zeroTotalCorrection || point.normalInitialValue : 0;
            let updatedZeroValue;
            if (minSegmentSize && point.isVisible() && val < minShownValue) {
                updatedZeroValue = minShownValue;
            }
            percent = val / total;
            point.correctValue(correction, percent, zeroTotalCorrection + (updatedZeroValue || 0));
            point.shiftedAngle = shiftedAngle;
            correction += updatedZeroValue || val;
        });
        this._rangeData = {
            val: {
                min: 0,
                max: correction
            }
        };
    },
    _removePoint: function(point) {
        const points = this.getPointsByArg(point.argument);
        points.splice(points.indexOf(point), 1);
        point.dispose();
    },
    arrangePoints: function() {
        const that = this;
        const originalPoints = that._points || [];
        const minSegmentSize = that._options.minSegmentSize;
        let minShownValue;
        let isAllPointsNegative = true;
        let i = 0;
        const len = originalPoints.length;
        while(i < len && isAllPointsNegative){
            isAllPointsNegative = originalPoints[i].value <= 0;
            i++;
        }
        const points = that._points = _map(originalPoints, function(point) {
            if (null === point.value || !isAllPointsNegative && point.value < 0) {
                that._removePoint(point);
                return null;
            } else {
                return point;
            }
        });
        const maxValue = points.reduce(function(max, p) {
            return _max(max, Math.abs(p.initialValue));
        }, 0);
        points.forEach(function(p) {
            p.normalInitialValue = p.initialValue / (0 !== maxValue ? maxValue : 1);
        });
        const total = points.reduce(function(total, point) {
            return total + (point.isVisible() ? point.normalInitialValue : 0);
        }, 0);
        if (minSegmentSize) {
            minShownValue = this._getArrangeMinShownValue(points, total);
        }
        that._applyArrangeCorrection(points, minShownValue, total);
    },
    correctPosition: function(correction, canvas) {
        _each(this._points, function(_, point) {
            point.correctPosition(correction);
        });
        this.setVisibleArea(canvas);
    },
    correctRadius: function(correction) {
        this._points.forEach(function(point) {
            point.correctRadius(correction);
        });
    },
    correctLabelRadius: function(labelRadius) {
        this._points.forEach(function(point) {
            point.correctLabelRadius(labelRadius);
        });
    },
    setVisibleArea: function(canvas) {
        this._visibleArea = {
            minX: canvas.left,
            maxX: canvas.width - canvas.right,
            minY: canvas.top,
            maxY: canvas.height - canvas.bottom
        };
    },
    _applyVisibleArea: _noop,
    _animate: function(firstDrawing) {
        const that = this;
        const points = that._points;
        const pointsCount = points && points.length;
        const completeFunc = function() {
            that._animateComplete();
        };
        let animatePoint;
        if (firstDrawing) {
            animatePoint = function(p, i) {
                p.animate(i === pointsCount - 1 ? completeFunc : void 0, .7, (1 - .7) * i / (pointsCount - 1));
            };
        } else {
            animatePoint = function(p, i) {
                p.animate(i === pointsCount - 1 ? completeFunc : void 0);
            };
        }
        points.forEach(animatePoint);
    },
    getVisiblePoints: function() {
        return _map(this._points, function(p) {
            return p.isVisible() ? p : null;
        });
    },
    getPointsByKeys: function(arg, argumentIndex) {
        const pointsByArg = this.getPointsByArg(arg);
        return pointsByArg[argumentIndex] && [
            pointsByArg[argumentIndex]
        ] || [];
    }
});
const doughnut = pie;
const donut = pie;
}),
"[project]/node_modules/devextreme/esm/viz/series/financial_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/financial_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "candlestick": ()=>candlestick,
    "stock": ()=>stock
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
;
;
;
;
;
;
const barSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].bar;
const DEFAULT_FINANCIAL_POINT_SIZE = 10;
const stock = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], {
    _animate: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _applyMarkerClipRect: function(settings) {
        settings["clip-path"] = this._forceClipping ? this._paneClipRectID : this._widePaneClipRectID;
    },
    _updatePointsVisibility: barSeries._updatePointsVisibility,
    _getOptionsForPoint: barSeries._getOptionsForPoint,
    _createErrorBarGroup: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    areErrorBarsVisible: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _createGroups: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._createGroups,
    _setMarkerGroupSettings: function() {
        const markersGroup = this._markersGroup;
        const styles = this._createPointStyles(this._getMarkerGroupOptions());
        const defaultStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(styles.normal, {
            class: "default-markers"
        });
        const defaultPositiveStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(styles.positive.normal, {
            class: "default-positive-markers"
        });
        const reductionStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(styles.reduction.normal, {
            class: "reduction-markers"
        });
        const reductionPositiveStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(styles.reductionPositive.normal, {
            class: "reduction-positive-markers"
        });
        const markerSettings = {
            class: "dxc-markers"
        };
        this._applyMarkerClipRect(markerSettings);
        markersGroup.attr(markerSettings);
        this._createGroup("defaultMarkersGroup", markersGroup, markersGroup, defaultStyle);
        this._createGroup("reductionMarkersGroup", markersGroup, markersGroup, reductionStyle);
        this._createGroup("defaultPositiveMarkersGroup", markersGroup, markersGroup, defaultPositiveStyle);
        this._createGroup("reductionPositiveMarkersGroup", markersGroup, markersGroup, reductionPositiveStyle);
    },
    _setGroupsSettings: function() {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._setGroupsSettings.call(this, false);
    },
    _getCreatingPointOptions: function() {
        const that = this;
        let defaultPointOptions;
        let creatingPointOptions = that._predefinedPointOptions;
        if (!creatingPointOptions) {
            defaultPointOptions = this._getPointOptions();
            that._predefinedPointOptions = creatingPointOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {
                styles: {}
            }, defaultPointOptions);
            creatingPointOptions.styles.normal = creatingPointOptions.styles.positive.normal = creatingPointOptions.styles.reduction.normal = creatingPointOptions.styles.reductionPositive.normal = {
                "stroke-width": defaultPointOptions.styles && defaultPointOptions.styles.normal && defaultPointOptions.styles.normal["stroke-width"]
            };
        }
        return creatingPointOptions;
    },
    _checkData: function(data, skippedFields) {
        const valueFields = this.getValueFields();
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]._checkData.call(this, data, skippedFields, {
            openValue: valueFields[0],
            highValue: valueFields[1],
            lowValue: valueFields[2],
            closeValue: valueFields[3]
        }) && data.highValue === data.highValue && data.lowValue === data.lowValue;
    },
    _getPointDataSelector: function(data, options) {
        const that = this;
        let level;
        const valueFields = that.getValueFields();
        const argumentField = that.getArgumentField();
        const openValueField = valueFields[0];
        const highValueField = valueFields[1];
        const lowValueField = valueFields[2];
        const closeValueField = valueFields[3];
        that.level = that._options.reduction.level;
        switch((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(that.level)){
            case "open":
                level = openValueField;
                break;
            case "high":
                level = highValueField;
                break;
            case "low":
                level = lowValueField;
                break;
            default:
                level = closeValueField;
                that.level = "close";
        }
        let prevLevelValue;
        return (data)=>{
            const reductionValue = data[level];
            let isReduction = false;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(reductionValue)) {
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(prevLevelValue)) {
                    isReduction = reductionValue < prevLevelValue;
                }
                prevLevelValue = reductionValue;
            }
            return {
                argument: data[argumentField],
                highValue: this._processEmptyValue(data[highValueField]),
                lowValue: this._processEmptyValue(data[lowValueField]),
                closeValue: this._processEmptyValue(data[closeValueField]),
                openValue: this._processEmptyValue(data[openValueField]),
                reductionValue: reductionValue,
                tag: data[that.getTagField()],
                isReduction: isReduction,
                data: data
            };
        };
    },
    _parsePointStyle: function(style, defaultColor, innerColor) {
        const color = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(style.color, true);
        return {
            stroke: color || defaultColor,
            "stroke-width": style.width,
            fill: color || innerColor
        };
    },
    _getDefaultStyle: function(options) {
        const mainPointColor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(options.color, true) || this._options.mainSeriesColor;
        return {
            normal: this._parsePointStyle(options, mainPointColor, mainPointColor),
            hover: this._parsePointStyle(options.hoverStyle, mainPointColor, mainPointColor),
            selection: this._parsePointStyle(options.selectionStyle, mainPointColor, mainPointColor)
        };
    },
    _getReductionStyle: function(options) {
        const reductionColor = options.reduction.color;
        return {
            normal: this._parsePointStyle({
                color: reductionColor,
                width: options.width,
                hatching: options.hatching
            }, reductionColor, reductionColor),
            hover: this._parsePointStyle(options.hoverStyle, reductionColor, reductionColor),
            selection: this._parsePointStyle(options.selectionStyle, reductionColor, reductionColor)
        };
    },
    _createPointStyles: function(pointOptions) {
        const innerColor = this._options.innerColor;
        const styles = this._getDefaultStyle(pointOptions);
        const positiveStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, styles);
        const reductionStyle = this._getReductionStyle(pointOptions);
        const reductionPositiveStyle = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, reductionStyle);
        positiveStyle.normal.fill = positiveStyle.hover.fill = positiveStyle.selection.fill = innerColor;
        reductionPositiveStyle.normal.fill = reductionPositiveStyle.hover.fill = reductionPositiveStyle.selection.fill = innerColor;
        styles.positive = positiveStyle;
        styles.reduction = reductionStyle;
        styles.reductionPositive = reductionPositiveStyle;
        styles.labelColor = this._options.mainSeriesColor;
        return styles;
    },
    _endUpdateData: function() {
        delete this._predefinedPointOptions;
    },
    _defaultAggregator: "ohlc",
    _aggregators: {
        ohlc: (_ref, series)=>{
            let { intervalStart: intervalStart, intervalEnd: intervalEnd, data: data } = _ref;
            if (!data.length) {
                return;
            }
            let result = {};
            const valueFields = series.getValueFields();
            const highValueField = valueFields[1];
            const lowValueField = valueFields[2];
            result[highValueField] = -1 / 0;
            result[lowValueField] = 1 / 0;
            result = data.reduce(function(result, item) {
                if (null !== item[highValueField]) {
                    result[highValueField] = Math.max(result[highValueField], item[highValueField]);
                }
                if (null !== item[lowValueField]) {
                    result[lowValueField] = Math.min(result[lowValueField], item[lowValueField]);
                }
                return result;
            }, result);
            result[valueFields[0]] = data[0][valueFields[0]];
            result[valueFields[3]] = data[data.length - 1][valueFields[3]];
            if (!isFinite(result[highValueField])) {
                result[highValueField] = null;
            }
            if (!isFinite(result[lowValueField])) {
                result[lowValueField] = null;
            }
            result[series.getArgumentField()] = series._getIntervalCenter(intervalStart, intervalEnd);
            return result;
        }
    },
    getValueFields: function() {
        const options = this._options;
        return [
            options.openValueField || "open",
            options.highValueField || "high",
            options.lowValueField || "low",
            options.closeValueField || "close"
        ];
    },
    getArgumentField: function() {
        return this._options.argumentField || "date";
    },
    _patchMarginOptions: function(options) {
        const pointOptions = this._getCreatingPointOptions();
        const styles = pointOptions.styles;
        const border = [
            styles.normal,
            styles.hover,
            styles.selection
        ].reduce(function(max, style) {
            return Math.max(max, style["stroke-width"]);
        }, 0);
        options.size = 10 + border;
        options.sizePointNormalState = 10;
        return options;
    },
    getSeriesPairCoord (coord, isArgument) {
        let oppositeCoord = null;
        const points = this.getVisiblePoints();
        for(let i = 0; i < points.length; i++){
            const p = points[i];
            let tmpCoord;
            if (isArgument) {
                tmpCoord = p.vx === coord ? (p.openY + p.closeY) / 2 : void 0;
            } else {
                const coords = [
                    Math.min(p.lowY, p.highY),
                    Math.max(p.lowY, p.highY)
                ];
                tmpCoord = coord >= coords[0] && coord <= coords[1] ? p.vx : void 0;
            }
            if (this._checkAxisVisibleAreaCoord(!isArgument, tmpCoord)) {
                oppositeCoord = tmpCoord;
                break;
            }
        }
        return oppositeCoord;
    },
    usePointsToDefineAutoHiding: ()=>false
});
const candlestick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, stock, {
    _parsePointStyle: function(style, defaultColor, innerColor) {
        const color = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extractColor"])(style.color, true) || innerColor;
        const base = stock._parsePointStyle.call(this, style, defaultColor, color);
        base.fill = color;
        base.hatching = style.hatching;
        return base;
    }
});
}),
"[project]/node_modules/devextreme/esm/viz/series/stacked_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/stacked_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "chart": ()=>chart,
    "polar": ()=>polar
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/area_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/line_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/object.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_object.js [app-client] (ecmascript)");
;
;
;
;
const chartAreaSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].area;
;
const chartBarSeries = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].bar;
;
;
;
const baseStackedSeries = {
    _calculateErrorBars: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _updateOptions: function(options) {
        this._stackName = "axis_" + (options.axis || "default");
    }
};
const chart = {};
const polar = {};
chart.stackedline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].line, baseStackedSeries, {});
chart.stackedspline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline, baseStackedSeries, {});
chart.fullstackedline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].line, baseStackedSeries, {
    getValueRangeInitialValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].area.getValueRangeInitialValue
});
chart.fullstackedspline = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline, baseStackedSeries, {
    getValueRangeInitialValue: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].area.getValueRangeInitialValue
});
const stackedBar = chart.stackedbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, chartBarSeries, baseStackedSeries, {
    _updateOptions: function(options) {
        baseStackedSeries._updateOptions.call(this, options);
        this._stackName = this._stackName + "_stack_" + (options.stack || "default");
    }
});
chart.fullstackedbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, chartBarSeries, baseStackedSeries, {
    _updateOptions: stackedBar._updateOptions
});
function clonePoint(point, value, minValue, position) {
    point = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_object$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["clone"])(point);
    point.value = value;
    point.minValue = minValue;
    point.translate();
    point.argument = point.argument + position;
    return point;
}
function preparePointsForStackedAreaSegment(points) {
    let i = 0;
    let p;
    const result = [];
    let array;
    const len = points.length;
    while(i < len){
        p = points[i];
        array = [
            p
        ];
        if (p.leftHole) {
            array = [
                clonePoint(p, p.leftHole, p.minLeftHole, "left"),
                p
            ];
        }
        if (p.rightHole) {
            array.push(clonePoint(p, p.rightHole, p.minRightHole, "right"));
        }
        result.push(array);
        i++;
    }
    return [].concat.apply([], result);
}
chart.stackedarea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, chartAreaSeries, baseStackedSeries, {
    _prepareSegment: function(points, rotated) {
        return chartAreaSeries._prepareSegment.call(this, preparePointsForStackedAreaSegment(points), rotated);
    },
    _appendInGroup: function() {
        this._group.append(this._extGroups.seriesGroup).toBackground();
    }
});
function getPointsByArgFromPrevSeries(prevSeries, argument) {
    let result;
    while(!result && prevSeries){
        result = prevSeries._segmentByArg && prevSeries._segmentByArg[argument];
        prevSeries = prevSeries._prevSeries;
    }
    return result;
}
chart.stackedsplinearea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].splinearea, baseStackedSeries, {
    _prepareSegment: function(points, rotated) {
        const that = this;
        let areaSegment;
        points = preparePointsForStackedAreaSegment(points);
        if (!this._prevSeries || 1 === points.length) {
            areaSegment = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].splinearea._prepareSegment.call(this, points, rotated);
        } else {
            const forwardPoints = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline._calculateBezierPoints(points, rotated);
            let backwardPoints = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["map"])(points, function(p) {
                const point = p.getCoords(true);
                point.argument = p.argument;
                return point;
            });
            let prevSeriesForwardPoints = [];
            const pointByArg = {};
            let i = 0;
            const len = that._prevSeries._segments.length;
            while(i < len){
                prevSeriesForwardPoints = prevSeriesForwardPoints.concat(that._prevSeries._segments[i].line);
                i++;
            }
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(prevSeriesForwardPoints, function(_, p) {
                if (null !== p.argument) {
                    const argument = p.argument.valueOf();
                    if (!pointByArg[argument]) {
                        pointByArg[argument] = [
                            p
                        ];
                    } else {
                        pointByArg[argument].push(p);
                    }
                }
            });
            that._prevSeries._segmentByArg = pointByArg;
            backwardPoints = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].spline._calculateBezierPoints(backwardPoints, rotated);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(backwardPoints, function(i, p) {
                const argument = p.argument.valueOf();
                let prevSeriesPoints;
                if (i % 3 === 0) {
                    prevSeriesPoints = pointByArg[argument] || getPointsByArgFromPrevSeries(that._prevSeries, argument);
                    if (prevSeriesPoints) {
                        backwardPoints[i - 1] && prevSeriesPoints[0] && (backwardPoints[i - 1] = prevSeriesPoints[0]);
                        backwardPoints[i + 1] && (backwardPoints[i + 1] = prevSeriesPoints[2] || p);
                    }
                }
            });
            areaSegment = {
                line: forwardPoints,
                area: forwardPoints.concat(backwardPoints.reverse())
            };
            that._areaPointsToSplineAreaPoints(areaSegment.area);
        }
        return areaSegment;
    },
    _appendInGroup: chart.stackedarea._appendInGroup
});
chart.fullstackedarea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, chartAreaSeries, baseStackedSeries, {
    _prepareSegment: chart.stackedarea._prepareSegment,
    _appendInGroup: chart.stackedarea._appendInGroup
});
chart.fullstackedsplinearea = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"].splinearea, baseStackedSeries, {
    _prepareSegment: chart.stackedsplinearea._prepareSegment,
    _appendInGroup: chart.stackedarea._appendInGroup
});
polar.stackedbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"].bar, baseStackedSeries, {
    _updateOptions: function(options) {
        baseStackedSeries._updateOptions.call(this, options);
        this._stackName = this._stackName + "_stack_" + (options.stack || "default");
    }
});
;
}),
"[project]/node_modules/devextreme/esm/viz/series/base_series.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/**
 * DevExtreme (esm/viz/series/base_series.js)
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/
 */ __turbopack_context__.s({
    "Series": ()=>Series,
    "mixins": ()=>mixins
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/type.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_type.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/extend.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_extend.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/iterator.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_iterator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$base_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/points/base_point.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/core/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/components/consts.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/helpers/range_data_calculator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/scatter_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/line_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/area_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bar_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$range_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/range_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bubble_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/bubble_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$pie_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/pie_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$financial_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/financial_series.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$stacked_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/series/stacked_series.js [app-client] (ecmascript)");
const seriesNS = {};
;
;
;
;
;
;
;
const states = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$components$2f$consts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].states;
;
;
;
;
;
;
;
;
;
;
const DISCRETE = "discrete";
const SELECTED_STATE = states.selectedMark;
const HOVER_STATE = states.hoverMark;
const HOVER = states.hover;
const NORMAL = states.normal;
const SELECTION = states.selection;
const APPLY_SELECTED = states.applySelected;
const APPLY_HOVER = states.applyHover;
const RESET_ITEM = states.resetItem;
const NONE_MODE = "none";
const INCLUDE_POINTS = "includepoints";
const NEAREST_POINT = "nearestpoint";
const SERIES_SELECTION_CHANGED = "seriesSelectionChanged";
const POINT_SELECTION_CHANGED = "pointSelectionChanged";
const SERIES_HOVER_CHANGED = "seriesHoverChanged";
const POINT_HOVER_CHANGED = "pointHoverChanged";
const ALL_SERIES_POINTS = "allseriespoints";
const ALL_ARGUMENT_POINTS = "allargumentpoints";
const POINT_HOVER = "pointHover";
const CLEAR_POINT_HOVER = "clearPointHover";
const SERIES_SELECT = "seriesSelect";
const POINT_SELECT = "pointSelect";
const POINT_DESELECT = "pointDeselect";
const getEmptyBusinessRange = function() {
    return {
        arg: {},
        val: {}
    };
};
function triggerEvent(element, event, point) {
    element && element.trigger(event, point);
}
seriesNS.mixins = {
    chart: {},
    pie: {},
    polar: {}
};
seriesNS.mixins.chart.scatter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"];
seriesNS.mixins.polar.scatter = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$scatter_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"];
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(seriesNS.mixins.pie, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$pie_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(seriesNS.mixins.chart, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$range_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bubble_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$financial_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$stacked_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["chart"]);
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(seriesNS.mixins.polar, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$line_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$area_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$bar_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"], __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$stacked_series$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["polar"]);
function includePointsMode(mode) {
    mode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(mode);
    return mode === INCLUDE_POINTS || "allseriespoints" === mode;
}
function getLabelOptions(labelOptions, defaultColor) {
    const opt = labelOptions || {};
    const labelFont = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, opt.font) || {};
    const labelBorder = opt.border || {};
    const labelConnector = opt.connector || {};
    const backgroundAttr = {
        fill: opt.backgroundColor || defaultColor,
        "stroke-width": labelBorder.visible ? labelBorder.width || 0 : 0,
        stroke: labelBorder.visible && labelBorder.width ? labelBorder.color : "none",
        dashStyle: labelBorder.dashStyle
    };
    const connectorAttr = {
        stroke: labelConnector.visible && labelConnector.width ? labelConnector.color || defaultColor : "none",
        "stroke-width": labelConnector.visible ? labelConnector.width || 0 : 0
    };
    labelFont.color = "none" === opt.backgroundColor && "#ffffff" === (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(labelFont.color) && "inside" !== opt.position ? defaultColor : labelFont.color;
    return {
        alignment: opt.alignment,
        format: opt.format,
        argumentFormat: opt.argumentFormat,
        customizeText: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isFunction"])(opt.customizeText) ? opt.customizeText : void 0,
        attributes: {
            font: labelFont
        },
        visible: 0 !== labelFont.size ? opt.visible : false,
        showForZeroValues: opt.showForZeroValues,
        horizontalOffset: opt.horizontalOffset,
        verticalOffset: opt.verticalOffset,
        radialOffset: opt.radialOffset,
        background: backgroundAttr,
        position: opt.position,
        connector: connectorAttr,
        rotationAngle: opt.rotationAngle,
        wordWrap: opt.wordWrap,
        textOverflow: opt.textOverflow,
        cssClass: opt.cssClass,
        displayFormat: opt.displayFormat
    };
}
function setPointHoverState(point, legendCallback) {
    point.fullState |= HOVER_STATE;
    point.applyView(legendCallback);
}
function releasePointHoverState(point, legendCallback) {
    point.fullState &= ~HOVER_STATE;
    point.applyView(legendCallback);
    point.releaseHoverState();
}
function setPointSelectedState(point, legendCallback) {
    point.fullState |= SELECTED_STATE;
    point.applyView(legendCallback);
}
function releasePointSelectedState(point, legendCallback) {
    point.fullState &= ~SELECTED_STATE;
    point.applyView(legendCallback);
}
function mergePointOptionsCore(base, extra) {
    const options = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, base, extra);
    options.border = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, base && base.border, extra && extra.border);
    return options;
}
function mergePointOptions(base, extra) {
    const options = mergePointOptionsCore(base, extra);
    options.image = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, base.image, extra.image);
    options.selectionStyle = mergePointOptionsCore(base.selectionStyle, extra.selectionStyle);
    options.hoverStyle = mergePointOptionsCore(base.hoverStyle, extra.hoverStyle);
    return options;
}
function Series(settings, options) {
    this.fullState = 0;
    this._extGroups = settings;
    this._renderer = settings.renderer;
    this._group = settings.renderer.g().attr({
        class: "dxc-series"
    });
    this._eventTrigger = settings.eventTrigger;
    this._eventPipe = settings.eventPipe;
    this._incidentOccurred = settings.incidentOccurred;
    this._legendCallback = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"];
    this.updateOptions(options, settings);
}
function getData(pointData) {
    return pointData.data;
}
function getValueChecker(axisType, axis) {
    if (!axis || "logarithmic" !== axisType || false !== axis.getOptions().allowNegatives) {
        return ()=>true;
    } else {
        return (value)=>value > 0;
    }
}
Series.prototype = {
    constructor: Series,
    _createLegendState: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getLegendStyles: function() {
        return this._styles.legendStyles;
    },
    _createStyles: function(options) {
        const that = this;
        const mainSeriesColor = options.mainSeriesColor;
        const colorId = this._getColorId(options);
        const hoverStyle = options.hoverStyle || {};
        const selectionStyle = options.selectionStyle || {};
        if (colorId) {
            that._turnOffHatching(hoverStyle, selectionStyle);
        }
        that._styles = {
            labelColor: mainSeriesColor,
            normal: that._parseStyle(options, mainSeriesColor, mainSeriesColor),
            hover: that._parseStyle(hoverStyle, colorId || mainSeriesColor, mainSeriesColor),
            selection: that._parseStyle(selectionStyle, colorId || mainSeriesColor, mainSeriesColor),
            legendStyles: {
                normal: that._createLegendState(options, colorId || mainSeriesColor),
                hover: that._createLegendState(hoverStyle, colorId || mainSeriesColor),
                selection: that._createLegendState(selectionStyle, colorId || mainSeriesColor)
            }
        };
    },
    setClippingParams (baseId, wideId, forceClipping) {
        let clipLabels = arguments.length > 3 && void 0 !== arguments[3] ? arguments[3] : true;
        this._paneClipRectID = baseId;
        this._widePaneClipRectID = wideId;
        this._forceClipping = forceClipping;
        this._clipLabels = clipLabels;
    },
    applyClip: function() {
        this._group.attr({
            "clip-path": this._paneClipRectID
        });
    },
    resetClip: function() {
        this._group.attr({
            "clip-path": null
        });
    },
    getTagField: function() {
        return this._options.tagField || "tag";
    },
    getValueFields: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getSizeField: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getArgumentField: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getPoints: function() {
        return this._points;
    },
    getPointsInViewPort: function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getPointsInViewPort(this);
    },
    _createPoint: function(data, index, oldPoint) {
        data.index = index;
        const that = this;
        const pointsByArgument = that.pointsByArgument;
        const options = that._getCreatingPointOptions(data);
        const arg = data.argument.valueOf();
        let point = oldPoint;
        if (point) {
            point.update(data, options);
        } else {
            point = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$points$2f$base_point$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Point"](that, data, options);
            if (that.isSelected() && includePointsMode(that.lastSelectionMode)) {
                point.setView(SELECTION);
            }
        }
        const pointByArgument = pointsByArgument[arg];
        if (pointByArgument) {
            pointByArgument.push(point);
        } else {
            pointsByArgument[arg] = [
                point
            ];
        }
        if (point.hasValue()) {
            that.customizePoint(point, data);
        }
        return point;
    },
    getRangeData: function() {
        return this._visible ? this._getRangeData() : getEmptyBusinessRange();
    },
    getArgumentRange: function() {
        return this._visible ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getArgumentRange(this) : getEmptyBusinessRange();
    },
    getViewport: function() {
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$series$2f$helpers$2f$range_data_calculator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].getViewport(this);
    },
    _deleteGroup: function(groupName) {
        const group = this[groupName];
        if (group) {
            group.dispose();
            this[groupName] = null;
        }
    },
    updateOptions (newOptions, settings) {
        const that = this;
        const widgetType = newOptions.widgetType;
        const oldType = that.type;
        const newType = newOptions.type;
        that.type = newType && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(newType.toString());
        if (!that._checkType(widgetType) || that._checkPolarBarType(widgetType, newOptions)) {
            that.dispose();
            that.isUpdated = false;
            return;
        }
        if (oldType !== that.type) {
            that._firstDrawing = true;
            that._resetType(oldType, widgetType);
            that._setType(that.type, widgetType);
        } else {
            that._defineDrawingState();
        }
        that._options = newOptions;
        that._pointOptions = null;
        that.name = newOptions.name;
        that.pane = newOptions.pane;
        that.tag = newOptions.tag;
        if (settings) {
            that._seriesModes = settings.commonSeriesModes || that._seriesModes;
            that._valueAxis = settings.valueAxis || that._valueAxis;
            that.axis = that._valueAxis && that._valueAxis.name;
            that._argumentAxis = settings.argumentAxis || that._argumentAxis;
        }
        that._createStyles(newOptions);
        that._stackName = null;
        that._updateOptions(newOptions);
        that._visible = newOptions.visible;
        that.isUpdated = true;
        that.stack = newOptions.stack;
        that.barOverlapGroup = newOptions.barOverlapGroup;
        that._createGroups();
        that._processEmptyValue = newOptions.ignoreEmptyPoints ? (x)=>null === x ? void 0 : x : (x)=>x;
    },
    _defineDrawingState () {
        this._firstDrawing = true;
    },
    _disposePoints: function(points) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(points || [], function(_, p) {
            p.dispose();
        });
    },
    updateDataType: function(settings) {
        this.argumentType = settings.argumentType;
        this.valueType = settings.valueType;
        this.argumentAxisType = settings.argumentAxisType;
        this.valueAxisType = settings.valueAxisType;
        this.showZero = settings.showZero;
        this._argumentChecker = getValueChecker(settings.argumentAxisType, this.getArgumentAxis());
        this._valueChecker = getValueChecker(settings.valueAxisType, this.getValueAxis());
        return this;
    },
    _argumentChecker: function() {
        return true;
    },
    _valueChecker: function() {
        return true;
    },
    getOptions: function() {
        return this._options;
    },
    _getOldPoint: function(data, oldPointsByArgument, index) {
        const arg = data.argument && data.argument.valueOf();
        const point = (oldPointsByArgument[arg] || [])[0];
        if (point) {
            oldPointsByArgument[arg].splice(0, 1);
        }
        return point;
    },
    updateData: function(data) {
        const that = this;
        const options = that._options;
        const nameField = options.nameField;
        data = data || [];
        if (data.length) {
            that._canRenderCompleteHandle = true;
        }
        const dataSelector = this._getPointDataSelector();
        let itemsWithoutArgument = 0;
        that._data = data.reduce((data, dataItem, index)=>{
            const pointDataItem = dataSelector(dataItem);
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(pointDataItem.argument)) {
                if (!nameField || dataItem[nameField] === options.nameFieldValue) {
                    pointDataItem.index = index;
                    data.push(pointDataItem);
                }
            } else {
                itemsWithoutArgument++;
            }
            return data;
        }, []);
        if (itemsWithoutArgument && itemsWithoutArgument === data.length) {
            that._incidentOccurred("W2002", [
                that.name,
                that.getArgumentField()
            ]);
        }
        that._endUpdateData();
    },
    _getData () {
        let data = this._data || [];
        if (this.useAggregation()) {
            const aggregateByCategory = this.argumentAxisType === DISCRETE;
            const argumentRange = aggregateByCategory ? {} : this.getArgumentRange();
            const aggregationInfo = aggregateByCategory ? {} : this.getArgumentAxis().getAggregationInfo(this._useAllAggregatedPoints, argumentRange);
            data = this._resample(aggregationInfo, data);
        }
        return data;
    },
    useAggregation: function() {
        const aggregation = this.getOptions().aggregation;
        return aggregation && aggregation.enabled;
    },
    autoHidePointMarkersEnabled: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    usePointsToDefineAutoHiding: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    createPoints (useAllAggregatedPoints) {
        this._normalizeUsingAllAggregatedPoints(useAllAggregatedPoints);
        this._createPoints();
    },
    _normalizeUsingAllAggregatedPoints: function(useAllAggregatedPoints) {
        this._useAllAggregatedPoints = this.useAggregation() && (this.argumentAxisType === DISCRETE || (this._data || []).length > 1 && !!useAllAggregatedPoints);
    },
    _createPoints: function() {
        const that = this;
        const oldPointsByArgument = that.pointsByArgument || {};
        const data = that._getData();
        that.pointsByArgument = {};
        that._calculateErrorBars(data);
        const skippedFields = {};
        const points = data.reduce((points, pointDataItem)=>{
            if (that._checkData(pointDataItem, skippedFields)) {
                const pointIndex = points.length;
                const oldPoint = that._getOldPoint(pointDataItem, oldPointsByArgument, pointIndex);
                const point = that._createPoint(pointDataItem, pointIndex, oldPoint);
                points.push(point);
            }
            return points;
        }, []);
        for(const field in skippedFields){
            if (skippedFields[field] === data.length) {
                that._incidentOccurred("W2002", [
                    that.name,
                    field
                ]);
            }
        }
        Object.keys(oldPointsByArgument).forEach((key)=>that._disposePoints(oldPointsByArgument[key]));
        that._points = points;
    },
    _removeOldSegments: function() {
        const that = this;
        const startIndex = that._segments.length;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(that._graphics.splice(startIndex, that._graphics.length) || [], function(_, elem) {
            that._removeElement(elem);
        });
        if (that._trackers) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(that._trackers.splice(startIndex, that._trackers.length) || [], function(_, elem) {
                elem.remove();
            });
        }
    },
    _prepareSegmentsPosition () {
        const points = this._points || [];
        const isCloseSegment = points[0] && points[0].hasValue() && this._options.closed;
        const segments = points.reduce(function(segments, p) {
            const segment = segments.at(-1);
            if (!p.translated) {
                p.setDefaultCoords();
            }
            if (p.hasValue() && p.hasCoords()) {
                segment.push(p);
            } else if (!p.hasValue() && segment.length) {
                segments.push([]);
            }
            return segments;
        }, [
            []
        ]);
        this._drawSegments(segments, isCloseSegment, false);
    },
    _drawElements (animationEnabled, firstDrawing) {
        const that = this;
        const points = that._points || [];
        const isCloseSegment = points[0] && points[0].hasValue() && that._options.closed;
        const groupForPoint = {
            markers: that._markersGroup,
            errorBars: that._errorBarGroup
        };
        that._drawnPoints = [];
        that._graphics = that._graphics || [];
        that._segments = [];
        const segments = points.reduce(function(segments, p) {
            const segment = segments.at(-1);
            if (p.hasValue() && p.hasCoords()) {
                that._drawPoint({
                    point: p,
                    groups: groupForPoint,
                    hasAnimation: animationEnabled,
                    firstDrawing: firstDrawing
                });
                segment.push(p);
            } else if (!p.hasValue()) {
                segment.length && segments.push([]);
            } else {
                p.setInvisibility();
            }
            return segments;
        }, [
            []
        ]);
        that._drawSegments(segments, isCloseSegment, animationEnabled);
        that._firstDrawing = !points.length;
        that._removeOldSegments();
        animationEnabled && that._animate(firstDrawing);
    },
    _drawSegments (segments, closeSegment, animationEnabled) {
        segments.forEach((segment, index)=>{
            if (segment.length) {
                const lastSegment = closeSegment && index === segments.length - 1;
                this._drawSegment(segment, animationEnabled, index, lastSegment);
            }
        });
    },
    draw (animationEnabled, hideLayoutLabels, legendCallback) {
        const that = this;
        const firstDrawing = that._firstDrawing;
        that._legendCallback = legendCallback || that._legendCallback;
        if (!that._visible) {
            that._group.remove();
            return;
        }
        that._appendInGroup();
        if (!that._isAllPointsTranslated) {
            that.prepareCoordinatesForPoints();
        }
        that._setGroupsSettings(animationEnabled, firstDrawing);
        !firstDrawing && !that._resetApplyingAnimation && that._prepareSegmentsPosition();
        that._drawElements(animationEnabled, firstDrawing);
        hideLayoutLabels && that.hideLabels();
        if (that.isSelected()) {
            that._changeStyle(that.lastSelectionMode, void 0, true);
        } else if (that.isHovered()) {
            that._changeStyle(that.lastHoverMode, void 0, true);
        } else {
            that._applyStyle(that._styles.normal);
        }
        that._isAllPointsTranslated = false;
        that._resetApplyingAnimation = false;
    },
    _translatePoints () {
        var _this__points;
        const points = (_this__points = this._points) !== null && _this__points !== void 0 ? _this__points : [];
        points.forEach((p)=>{
            p.translate();
        });
    },
    prepareCoordinatesForPoints () {
        this._applyVisibleArea();
        this._translatePoints();
        this._isAllPointsTranslated = true;
    },
    _setLabelGroupSettings: function(animationEnabled) {
        const settings = {
            class: "dxc-labels",
            "pointer-events": "none"
        };
        this._clipLabels && this._applyElementsClipRect(settings);
        this._applyClearingSettings(settings);
        animationEnabled && (settings.opacity = .001);
        this._labelsGroup.attr(settings).append(this._extGroups.labelsGroup);
    },
    _checkType: function(widgetType) {
        return !!seriesNS.mixins[widgetType][this.type];
    },
    _checkPolarBarType: function(widgetType, options) {
        return "polar" === widgetType && options.spiderWidget && -1 !== this.type.indexOf("bar");
    },
    _resetType: function(seriesType, widgetType) {
        let methodName;
        let methods;
        if (seriesType) {
            methods = seriesNS.mixins[widgetType][seriesType];
            for(methodName in methods){
                delete this[methodName];
            }
        }
    },
    _setType: function(seriesType, widgetType) {
        let methodName;
        const methods = seriesNS.mixins[widgetType][seriesType];
        for(methodName in methods){
            this[methodName] = methods[methodName];
        }
    },
    _setPointsView: function(view, target) {
        this.getPoints().forEach(function(point) {
            if (target !== point) {
                point.setView(view);
            }
        });
    },
    _resetPointsView: function(view, target) {
        this.getPoints().forEach(function(point) {
            if (target !== point) {
                point.resetView(view);
            }
        });
    },
    _resetNearestPoint: function() {
        this._nearestPoint && null !== this._nearestPoint.series && this._nearestPoint.resetView(HOVER);
        this._nearestPoint = null;
    },
    _setSelectedState: function(mode) {
        const that = this;
        that.lastSelectionMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(mode || that._options.selectionMode);
        that.fullState = that.fullState | SELECTED_STATE;
        that._resetNearestPoint();
        that._changeStyle(that.lastSelectionMode);
        if ("none" !== that.lastSelectionMode && that.isHovered() && includePointsMode(that.lastHoverMode)) {
            that._resetPointsView(HOVER);
        }
    },
    _releaseSelectedState: function() {
        const that = this;
        that.fullState = that.fullState & ~SELECTED_STATE;
        that._changeStyle(that.lastSelectionMode, SELECTION);
        if ("none" !== that.lastSelectionMode && that.isHovered() && includePointsMode(that.lastHoverMode)) {
            that._setPointsView(HOVER);
        }
    },
    isFullStackedSeries: function() {
        return 0 === this.type.indexOf("fullstacked");
    },
    isStackedSeries: function() {
        return 0 === this.type.indexOf("stacked");
    },
    resetApplyingAnimation: function(isFirstDrawing) {
        this._resetApplyingAnimation = true;
        if (isFirstDrawing) {
            this._firstDrawing = true;
        }
    },
    isFinancialSeries: function() {
        return "stock" === this.type || "candlestick" === this.type;
    },
    _canChangeView: function() {
        return !this.isSelected() && "none" !== (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(this._options.hoverMode);
    },
    _changeStyle: function(mode, resetView, skipPoints) {
        const that = this;
        let state = that.fullState;
        const styles = [
            NORMAL,
            HOVER,
            SELECTION,
            SELECTION
        ];
        if ("none" === that.lastHoverMode) {
            state &= ~HOVER_STATE;
        }
        if ("none" === that.lastSelectionMode) {
            state &= ~SELECTED_STATE;
        }
        if (includePointsMode(mode) && !skipPoints) {
            if (!resetView) {
                that._setPointsView(styles[state]);
            } else {
                that._resetPointsView(resetView);
            }
        }
        that._legendCallback([
            RESET_ITEM,
            APPLY_HOVER,
            APPLY_SELECTED,
            APPLY_SELECTED
        ][state]);
        that._applyStyle(that._styles[styles[state]]);
    },
    updateHover: function(x, y) {
        const that = this;
        const currentNearestPoint = that._nearestPoint;
        const point = that.isHovered() && that.lastHoverMode === NEAREST_POINT && that.getNeighborPoint(x, y);
        if (point !== currentNearestPoint && !(that.isSelected() && "none" !== that.lastSelectionMode)) {
            that._resetNearestPoint();
            if (point) {
                point.setView(HOVER);
                that._nearestPoint = point;
            }
        }
    },
    _getMainAxisName: function() {
        return this._options.rotated ? "X" : "Y";
    },
    areLabelsVisible: function() {
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isDefined"])(this._options.maxLabelCount) || this._points.length <= this._options.maxLabelCount;
    },
    getLabelVisibility: function() {
        return this.areLabelsVisible() && this._options.label && this._options.label.visible;
    },
    customizePoint: function(point, pointData) {
        const that = this;
        const options = that._options;
        const customizePoint = options.customizePoint;
        let customizeObject;
        let pointOptions;
        let customLabelOptions;
        let customOptions;
        const customizeLabel = options.customizeLabel;
        let useLabelCustomOptions;
        let usePointCustomOptions;
        if (customizeLabel && customizeLabel.call) {
            customizeObject = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                seriesName: that.name
            }, pointData);
            customizeObject.series = that;
            customLabelOptions = customizeLabel.call(customizeObject, customizeObject);
            useLabelCustomOptions = customLabelOptions && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isEmptyObject"])(customLabelOptions);
            customLabelOptions = useLabelCustomOptions ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(true, {}, options.label, customLabelOptions) : null;
        }
        if (customizePoint && customizePoint.call) {
            customizeObject = customizeObject || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({
                seriesName: that.name
            }, pointData);
            customizeObject.series = that;
            customOptions = customizePoint.call(customizeObject, customizeObject);
            usePointCustomOptions = customOptions && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_type$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isEmptyObject"])(customOptions);
        }
        if (useLabelCustomOptions || usePointCustomOptions) {
            pointOptions = that._parsePointOptions(that._preparePointOptions(customOptions), customLabelOptions || options.label, pointData, point);
            pointOptions.styles.useLabelCustomOptions = useLabelCustomOptions;
            pointOptions.styles.usePointCustomOptions = usePointCustomOptions;
            point.updateOptions(pointOptions);
        }
    },
    show: function() {
        if (!this._visible) {
            this._changeVisibility(true);
        }
    },
    hide: function() {
        if (this._visible) {
            this._changeVisibility(false);
        }
    },
    _changeVisibility: function(visibility) {
        this._visible = this._options.visible = visibility;
        this._updatePointsVisibility();
        this.hidePointTooltip();
        this._options.visibilityChanged(this);
    },
    _updatePointsVisibility: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    hideLabels: function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._points, function(_, point) {
            point._label.draw(false);
        });
    },
    _turnOffHatching (hoverStyle, selectionStyle) {
        if (hoverStyle.hatching) {
            hoverStyle.hatching.direction = "none";
        }
        if (selectionStyle.hatching) {
            selectionStyle.hatching.direction = "none";
        }
    },
    _parsePointOptions: function(pointOptions, labelOptions, data, point) {
        const options = this._options;
        const styles = this._createPointStyles(pointOptions, data, point);
        const parsedOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])({}, pointOptions, {
            type: options.type,
            rotated: options.rotated,
            styles: styles,
            widgetType: options.widgetType,
            visibilityChanged: options.visibilityChanged
        });
        parsedOptions.label = getLabelOptions(labelOptions, styles.labelColor);
        if (this.areErrorBarsVisible()) {
            parsedOptions.errorBars = options.valueErrorBar;
        }
        return parsedOptions;
    },
    _preparePointOptions: function(customOptions) {
        const pointOptions = this._getOptionsForPoint();
        return customOptions ? mergePointOptions(pointOptions, customOptions) : pointOptions;
    },
    _getMarkerGroupOptions: function() {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_extend$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["extend"])(false, {}, this._getOptionsForPoint(), {
            hoverStyle: {},
            selectionStyle: {}
        });
    },
    _getAggregationMethod: function(isValueAxisDiscrete) {
        const options = this.getOptions().aggregation;
        const method = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(options.method);
        const customAggregator = "custom" === method && options.calculate;
        if (customAggregator) {
            return customAggregator;
        }
        if (isValueAxisDiscrete) {
            return (_ref)=>{
                let { data: data } = _ref;
                return data[0];
            };
        }
        return this._aggregators[method] || this._aggregators[this._defaultAggregator];
    },
    _resample (_ref2, data) {
        let { interval: interval, ticks: ticks } = _ref2;
        const that = this;
        const options = that.getOptions();
        const dataSelector = this._getPointDataSelector();
        const addAggregatedData = (target, data, aggregationInfo)=>{
            if (!data) {
                return;
            }
            const processData = (d)=>{
                const pointData = d && dataSelector(d, options);
                if (pointData && that._checkData(pointData)) {
                    pointData.aggregationInfo = aggregationInfo;
                    target.push(pointData);
                }
            };
            if (Array.isArray(data)) {
                data.forEach(processData);
            } else {
                processData(data);
            }
        };
        const isValueAxisDiscrete = that.valueAxisType === DISCRETE;
        const aggregateByCategory = that.argumentAxisType === DISCRETE;
        const aggregationMethod = this._getAggregationMethod(isValueAxisDiscrete);
        if (aggregateByCategory) {
            const categories = this.getArgumentAxis().getTranslator().getBusinessRange().categories;
            const groups = categories.reduce((g, category)=>{
                g[category.valueOf()] = [];
                return g;
            }, {});
            data.forEach((dataItem)=>{
                groups[dataItem.argument.valueOf()].push(dataItem);
            });
            return categories.reduce((result, c)=>{
                addAggregatedData(result, aggregationMethod({
                    aggregationInterval: null,
                    intervalStart: c,
                    intervalEnd: c,
                    data: groups[c.valueOf()].map(getData)
                }, that));
                return result;
            }, []);
        }
        if (isValueAxisDiscrete) {
            return data.reduce((result, dataItem, index, data)=>{
                result[1].push(dataItem);
                if (index === data.length - 1 || (index + 1) % interval === 0) {
                    const dataInInterval = result[1];
                    const aggregationInfo = {
                        aggregationInterval: interval,
                        data: dataInInterval.map(getData)
                    };
                    addAggregatedData(result[0], aggregationMethod(aggregationInfo, that));
                    result[1] = [];
                }
                return result;
            }, [
                [],
                []
            ])[0];
        }
        const aggregatedData = [];
        if (1 === ticks.length) {
            const aggregationInfo = {
                intervalStart: ticks[0],
                intervalEnd: ticks[0],
                aggregationInterval: null,
                data: data.map(getData)
            };
            addAggregatedData(aggregatedData, aggregationMethod(aggregationInfo, that), aggregationInfo);
        } else {
            let dataIndex = 0;
            for(let i = 1; i < ticks.length; i++){
                const intervalEnd = ticks[i];
                const intervalStart = ticks[i - 1];
                const dataInInterval = [];
                while(data[dataIndex] && data[dataIndex].argument < intervalEnd){
                    if (data[dataIndex].argument >= intervalStart) {
                        dataInInterval.push(data[dataIndex]);
                    }
                    dataIndex++;
                }
                const aggregationInfo = {
                    intervalStart: intervalStart,
                    intervalEnd: intervalEnd,
                    aggregationInterval: interval,
                    data: dataInInterval.map(getData)
                };
                addAggregatedData(aggregatedData, aggregationMethod(aggregationInfo, that), aggregationInfo);
            }
        }
        that._endUpdateData();
        return aggregatedData;
    },
    canRenderCompleteHandle: function() {
        const result = this._canRenderCompleteHandle;
        delete this._canRenderCompleteHandle;
        return !!result;
    },
    isHovered: function() {
        return !!(1 & this.fullState);
    },
    isSelected: function() {
        return !!(2 & this.fullState);
    },
    isVisible: function() {
        return this._visible;
    },
    getAllPoints: function() {
        this._createAllAggregatedPoints();
        return (this._points || []).slice();
    },
    getPointByPos: function(pos) {
        this._createAllAggregatedPoints();
        return (this._points || [])[pos];
    },
    getVisiblePoints: function() {
        return (this._drawnPoints || []).slice();
    },
    selectPoint: function(point) {
        if (!point.isSelected()) {
            setPointSelectedState(point, this._legendCallback);
            this._eventPipe({
                action: POINT_SELECT,
                target: point
            });
            this._eventTrigger("pointSelectionChanged", {
                target: point
            });
        }
    },
    deselectPoint: function(point) {
        if (point.isSelected()) {
            releasePointSelectedState(point, this._legendCallback);
            this._eventPipe({
                action: POINT_DESELECT,
                target: point
            });
            this._eventTrigger("pointSelectionChanged", {
                target: point
            });
        }
    },
    hover: function(mode) {
        const eventTrigger = this._eventTrigger;
        if (this.isHovered()) {
            return;
        }
        this.lastHoverMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(mode || this._options.hoverMode);
        this.fullState = this.fullState | HOVER_STATE;
        this._changeStyle(this.lastHoverMode, void 0, this.isSelected() && "none" !== this.lastSelectionMode);
        eventTrigger("seriesHoverChanged", {
            target: this
        });
    },
    clearHover: function() {
        const eventTrigger = this._eventTrigger;
        if (!this.isHovered()) {
            return;
        }
        this._resetNearestPoint();
        this.fullState = this.fullState & ~HOVER_STATE;
        this._changeStyle(this.lastHoverMode, HOVER, this.isSelected() && "none" !== this.lastSelectionMode);
        eventTrigger("seriesHoverChanged", {
            target: this
        });
    },
    hoverPoint: function(point) {
        const that = this;
        if (!point.isHovered()) {
            point.clearHover();
            setPointHoverState(point, that._legendCallback);
            that._canChangeView() && that._applyStyle(that._styles.hover);
            that._eventPipe({
                action: POINT_HOVER,
                target: point
            });
            that._eventTrigger("pointHoverChanged", {
                target: point
            });
        }
    },
    clearPointHover: function() {
        const that = this;
        that.getPoints().some(function(currentPoint) {
            if (currentPoint.isHovered()) {
                releasePointHoverState(currentPoint, that._legendCallback);
                that._canChangeView() && that._applyStyle(that._styles.normal);
                that._eventPipe({
                    action: "clearPointHover",
                    target: currentPoint
                });
                that._eventTrigger("pointHoverChanged", {
                    target: currentPoint
                });
                return true;
            }
            return false;
        });
    },
    showPointTooltip: function(point) {
        triggerEvent(this._extGroups.seriesGroup, "showpointtooltip", point);
    },
    hidePointTooltip: function(point) {
        triggerEvent(this._extGroups.seriesGroup, "hidepointtooltip", point);
    },
    select: function() {
        const that = this;
        if (!that.isSelected()) {
            that._setSelectedState(that._options.selectionMode);
            that._eventPipe({
                action: SERIES_SELECT,
                target: that
            });
            that._group.toForeground();
            that._eventTrigger("seriesSelectionChanged", {
                target: that
            });
        }
    },
    clearSelection: function() {
        const that = this;
        if (that.isSelected()) {
            that._releaseSelectedState();
            that._eventTrigger("seriesSelectionChanged", {
                target: that
            });
        }
    },
    getPointsByArg: function(arg, skipPointsCreation) {
        const that = this;
        const argValue = arg.valueOf();
        let points = that.pointsByArgument[argValue];
        if (!points && !skipPointsCreation && that._createAllAggregatedPoints()) {
            points = that.pointsByArgument[argValue];
        }
        return points || [];
    },
    _createAllAggregatedPoints: function() {
        if (this.useAggregation() && !this._useAllAggregatedPoints) {
            this.createPoints(true);
            return true;
        }
        return false;
    },
    getPointsByKeys: function(arg) {
        return this.getPointsByArg(arg);
    },
    notify: function(data) {
        const that = this;
        const action = data.action;
        const seriesModes = that._seriesModes;
        const target = data.target;
        const targetOptions = target.getOptions();
        const pointHoverMode = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(targetOptions.hoverMode);
        const selectionModeOfPoint = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$core$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["normalizeEnum"])(targetOptions.selectionMode);
        if (action === POINT_HOVER) {
            that._hoverPointHandler(target, pointHoverMode, data.notifyLegend);
        } else if ("clearPointHover" === action) {
            that._clearPointHoverHandler(target, pointHoverMode, data.notifyLegend);
        } else if (action === SERIES_SELECT) {
            target !== that && "single" === seriesModes.seriesSelectionMode && that.clearSelection();
        } else if (action === POINT_SELECT) {
            if ("single" === seriesModes.pointSelectionMode) {
                that.getPoints().some(function(currentPoint) {
                    if (currentPoint !== target && currentPoint.isSelected()) {
                        that.deselectPoint(currentPoint);
                        return true;
                    }
                    return false;
                });
            }
            that._selectPointHandler(target, selectionModeOfPoint);
        } else if (action === POINT_DESELECT) {
            that._deselectPointHandler(target, selectionModeOfPoint);
        }
    },
    _selectPointHandler: function(target, mode) {
        const that = this;
        if ("allseriespoints" === mode) {
            target.series === that && that._setPointsView(SELECTION, target);
        } else if ("allargumentpoints" === mode) {
            that.getPointsByKeys(target.argument, target.argumentIndex).forEach(function(currentPoint) {
                currentPoint !== target && currentPoint.setView(SELECTION);
            });
        }
    },
    _deselectPointHandler: function(target, mode) {
        if ("allseriespoints" === mode) {
            target.series === this && this._resetPointsView(SELECTION, target);
        } else if ("allargumentpoints" === mode) {
            this.getPointsByKeys(target.argument, target.argumentIndex).forEach(function(currentPoint) {
                currentPoint !== target && currentPoint.resetView(SELECTION);
            });
        }
    },
    _hoverPointHandler: function(target, mode, notifyLegend) {
        const that = this;
        if (target.series !== that && "allargumentpoints" === mode) {
            that.getPointsByKeys(target.argument, target.argumentIndex).forEach(function(currentPoint) {
                currentPoint.setView(HOVER);
            });
            notifyLegend && that._legendCallback(target);
        } else if ("allseriespoints" === mode && target.series === that) {
            that._setPointsView(HOVER, target);
        }
    },
    _clearPointHoverHandler: function(target, mode, notifyLegend) {
        const that = this;
        if ("allargumentpoints" === mode) {
            target.series !== that && that.getPointsByKeys(target.argument, target.argumentIndex).forEach(function(currentPoint) {
                currentPoint.resetView(HOVER);
            });
            notifyLegend && that._legendCallback(target);
        } else if ("allseriespoints" === mode && target.series === that) {
            that._resetPointsView(HOVER, target);
        }
    },
    _deletePoints: function() {
        this._disposePoints(this._points);
        this._points = this._drawnPoints = null;
    },
    _deleteTrackers: function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._trackers || [], function(_, tracker) {
            tracker.remove();
        });
        this._trackersGroup && this._trackersGroup.dispose();
        this._trackers = this._trackersGroup = null;
    },
    dispose: function() {
        this._deletePoints();
        this._group.dispose();
        this._labelsGroup && this._labelsGroup.dispose();
        this._errorBarGroup && this._errorBarGroup.dispose();
        this._deleteTrackers();
        this._group = this._extGroups = this._markersGroup = this._elementsGroup = this._bordersGroup = this._labelsGroup = this._errorBarGroup = this._graphics = this._rangeData = this._renderer = this._styles = this._options = this._pointOptions = this._drawnPoints = this.pointsByArgument = this._segments = this._prevSeries = null;
    },
    correctPosition: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    drawTrackers: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getNeighborPoint: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    areErrorBarsVisible: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    _getColorId: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["noop"],
    getMarginOptions: function() {
        return this._patchMarginOptions({
            percentStick: this.isFullStackedSeries()
        });
    },
    getColor: function() {
        return this.getLegendStyles().normal.fill;
    },
    getOpacity: function() {
        return this._options.opacity;
    },
    getStackName: function() {
        return this._stackName;
    },
    getBarOverlapGroup: function() {
        return this._options.barOverlapGroup;
    },
    getPointByCoord: function(x, y) {
        const point = this.getNeighborPoint(x, y);
        return null !== point && void 0 !== point && point.coordsIn(x, y) ? point : null;
    },
    getValueAxis: function() {
        return this._valueAxis;
    },
    getArgumentAxis: function() {
        return this._argumentAxis;
    },
    getMarkersGroup () {
        return this._markersGroup;
    },
    getRenderer () {
        return this._renderer;
    },
    removePointElements () {
        if (this._markersGroup) {
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(this._points, (_, p)=>p.deleteMarker());
            this._markersGroup.dispose();
            this._markersGroup = null;
        }
    },
    removeGraphicElements () {
        const that = this;
        if (that._elementsGroup) {
            that._elementsGroup.dispose();
            that._elementsGroup = null;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_iterator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["each"])(that._graphics || [], (_, elem)=>{
            that._removeElement(elem);
        });
        that._graphics = null;
    },
    removeBordersGroup () {
        if (this._bordersGroup) {
            this._bordersGroup.dispose();
            this._bordersGroup = null;
        }
    }
};
const mixins = seriesNS.mixins;
}),
}]);

//# sourceMappingURL=node_modules_devextreme_esm_viz_series_d3fb0bfe._.js.map