﻿#pragma checksum "..\..\..\RequestDetailDialog.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "1E2E7D0AA1D26D7DA1662AAF2980117691B38A3E"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DevExpress.Core;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Core.ConditionalFormatting;
using DevExpress.Xpf.Core.DataSources;
using DevExpress.Xpf.Core.Serialization;
using DevExpress.Xpf.Core.ServerMode;
using DevExpress.Xpf.DXBinding;
using DevExpress.Xpf.Data;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace omsnext.wpf {
    
    
    /// <summary>
    /// RequestDetailDialog
    /// </summary>
    public partial class RequestDetailDialog : DevExpress.Xpf.Core.ThemedWindow, System.Windows.Markup.IComponentConnector {
        
        
        #line 25 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RequestIdLabel;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserNameLabel;
        
        #line default
        #line hidden
        
        
        #line 64 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock UserEmailLabel;
        
        #line default
        #line hidden
        
        
        #line 67 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RequestSourceLabel;
        
        #line default
        #line hidden
        
        
        #line 98 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock RequestDateLabel;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border StatusBorder;
        
        #line default
        #line hidden
        
        
        #line 106 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusLabel;
        
        #line default
        #line hidden
        
        
        #line 113 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock IpAddressLabel;
        
        #line default
        #line hidden
        
        
        #line 119 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border ProcessingInfoBorder;
        
        #line default
        #line hidden
        
        
        #line 145 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessedByLabel;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock ProcessedDateLabel;
        
        #line default
        #line hidden
        
        
        #line 154 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border AdminNotesBorder;
        
        #line default
        #line hidden
        
        
        #line 173 "..\..\..\RequestDetailDialog.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock AdminNotesLabel;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/omsnext.wpf;component/requestdetaildialog.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\RequestDetailDialog.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.RequestIdLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.UserNameLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.UserEmailLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.RequestSourceLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.RequestDateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 6:
            this.StatusBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 7:
            this.StatusLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 8:
            this.IpAddressLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 9:
            this.ProcessingInfoBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 10:
            this.ProcessedByLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 11:
            this.ProcessedDateLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 12:
            this.AdminNotesBorder = ((System.Windows.Controls.Border)(target));
            return;
            case 13:
            this.AdminNotesLabel = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 14:
            
            #line 190 "..\..\..\RequestDetailDialog.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.CloseButton_Click);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

