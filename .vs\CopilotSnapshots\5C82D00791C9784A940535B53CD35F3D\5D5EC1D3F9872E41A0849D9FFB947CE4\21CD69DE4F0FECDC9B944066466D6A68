﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using DevExpress.Xpf.Grid;
using omsnext.core.Models;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class KtforgevUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<KtforgevDto> _ktforgevData;
        private bool _isLoading;
        private bool _isLoadingMore;
        private int _currentPage = 0;
        private int _pageSize = 1000; // Nagyobb page size
        private int _totalRecords = 0;
        private string? _currentSortField;
        private string? _currentSortDirection = "asc";
        private string? _currentFilterField;
        private string? _currentFilterValue;
        private bool _hasMoreData = true;

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<KtforgevDto> KtforgevData
        {
            get => _ktforgevData;
            set
            {
                _ktforgevData = value;
                OnPropertyChanged(nameof(KtforgevData));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
                if (StatusLabel != null)
                    StatusLabel.Text = value ? "Betöltés..." : "Készen";
            }
        }

        public KtforgevUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _ktforgevData = new ObservableCollection<KtforgevDto>();
            
            DataContext = this;
            KtforgevGrid.ItemsSource = KtforgevData;
            
            // Grid események
            if (TableView != null)
            {
                TableView.FocusedRowChanged += TableView_FocusedRowChanged;
            }
            
            Loaded += KtforgevUserControl_Loaded;
        }

        private async void TableView_FocusedRowChanged(object sender, FocusedRowChangedEventArgs e)
        {
            // Infinite scrolling: amikor közel a végéhez ér, tölt be újabb adatokat
            if (TableView != null && _hasMoreData && !_isLoadingMore)
            {
                var visibleRowCount = TableView.VisibleRowCount;
                var focusedRowHandle = TableView.FocusedRowHandle;
                
                // Ha az utolsó 50 sorban van, töltse be a következő oldalt
                if (focusedRowHandle >= KtforgevData.Count - 50)
                {
                    await LoadMoreDataAsync();
                }
            }
        }

        private async void KtforgevUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync(true);
        }

        private async Task LoadDataAsync(bool clearExisting = false)
        {
            if (_isLoading) return;

            IsLoading = true;
            try
            {
                if (clearExisting)
                {
                    _currentPage = 0;
                    KtforgevData.Clear();
                    _hasMoreData = true;
                }

                var skip = _currentPage * _pageSize;
                var result = await _apiClient.GetKtforgevDataAsync(
                    skip: skip,
                    take: _pageSize,
                    sortField: _currentSortField,
                    sortDirection: _currentSortDirection,
                    filterField: _currentFilterField,
                    filterValue: _currentFilterValue,
                    filterOperator: "contains");

                if (result != null)
                {
                    if (clearExisting)
                    {
                        KtforgevData.Clear();
                    }

                    foreach (var item in result.Data)
                    {
                        KtforgevData.Add(item);
                    }

                    _totalRecords = result.TotalCount;
                    _hasMoreData = KtforgevData.Count < _totalRecords;
                    
                    UpdateStatusLabels();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt az adatok betöltése során.", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private async Task LoadMoreDataAsync()
        {
            if (_isLoadingMore || !_hasMoreData) return;

            _isLoadingMore = true;
            if (StatusLabel != null)
                StatusLabel.Text = "További adatok betöltése...";

            try
            {
                _currentPage++;
                var skip = _currentPage * _pageSize;
                
                var result = await _apiClient.GetKtforgevDataAsync(
                    skip: skip,
                    take: _pageSize,
                    sortField: _currentSortField,
                    sortDirection: _currentSortDirection,
                    filterField: _currentFilterField,
                    filterValue: _currentFilterValue,
                    filterOperator: "contains");

                if (result != null && result.Data.Any())
                {
                    foreach (var item in result.Data)
                    {
                        KtforgevData.Add(item);
                    }

                    _hasMoreData = KtforgevData.Count < _totalRecords;
                    UpdateStatusLabels();
                }
                else
                {
                    _hasMoreData = false;
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba a további adatok betöltése során: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                _isLoadingMore = false;
                if (StatusLabel != null)
                    StatusLabel.Text = _hasMoreData ? "Készen - további adatok elérhető scroll-nál" : "Minden adat betöltve";
            }
        }

        private void UpdateStatusLabels()
        {
            if (RecordCountLabel != null)
            {
                RecordCountLabel.Text = $"Betöltött rekordok: {KtforgevData.Count:N0} / {_totalRecords:N0}";
            }
            
            if (PageInfoLabel != null)
            {
                var loadedPercent = _totalRecords > 0 ? (double)KtforgevData.Count / _totalRecords * 100 : 0;
                PageInfoLabel.Text = $"Betöltve: {loadedPercent:F1}% | {(_hasMoreData ? "Van még adat" : "Minden adat betöltve")}";
            }
        }

        private async void ApplyFilterButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedItem = FilterFieldComboBox.SelectedItem as ComboBoxItem;
            if (selectedItem != null && !string.IsNullOrWhiteSpace(FilterValueTextBox.Text))
            {
                _currentFilterField = selectedItem.Tag?.ToString();
                _currentFilterValue = FilterValueTextBox.Text.Trim();
                _currentPage = 0;
                await LoadDataAsync(true);
            }
            else
            {
                DXMessageBox.Show("Kérjük válasszon szűrő mezőt és adjon meg értéket!", "Figyelmeztetés", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
            }
        }

        private async void ClearFilterButton_Click(object sender, RoutedEventArgs e)
        {
            FilterFieldComboBox.SelectedIndex = -1;
            FilterValueTextBox.Clear();
            _currentFilterField = null;
            _currentFilterValue = null;
            _currentPage = 0;
            await LoadDataAsync(true);
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadDataAsync(true);
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}