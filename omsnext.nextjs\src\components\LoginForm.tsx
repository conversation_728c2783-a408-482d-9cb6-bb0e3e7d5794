"use client";
import { useState, useRef, useCallback } from "react";
import { useRouter } from "next/navigation";
import Form, {
  Item,
  Label,
  ButtonItem,
  RequiredRule,
  EmailRule,
} from "devextreme-react/form";
import LoadIndicator from "devextreme-react/load-indicator";
import styles from "./LoginForm.module.css";

// CardAuth komponens
const CardAuth = ({
  title,
  description,
  children,
}: {
  title: string;
  description: string;
  children: React.ReactNode;
}) => {
  return (
    <div className={styles.authCard}>
      <div className={`dx-card ${styles.cardContent}`}>
        <div className={styles.header}>
          <div className={styles.title}>{title}</div>
          <div className={styles.description}>{description}</div>
        </div>
        {children}
      </div>
    </div>
  );
};

export default function LoginForm() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const formData = useRef({ email: "", password: "" });

  const onSubmit = useCallback(
    async (e: any) => {
      e.preventDefault();
      const { email, password } = formData.current;
      setLoading(true);
      setError(null);

      try {
        const res = await fetch("/api/auth/login", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ email, password }),
        });

        if (!res.ok) {
          const errorData = await res.json();
          throw new Error(
            errorData.error || "Hiba történt a bejelentkezés során."
          );
        }

        const data = await res.json();
        const { token, user } = data;

        // Set token in localStorage
        localStorage.setItem("oms_token", token);

        router.push("/dashboard");
      } catch (err: any) {
        setError(err.message || "Hiba történt a bejelentkezés során.");
        setLoading(false);
      }
    },
    [router]
  );

  const emailEditorOptions = {
    stylingMode: "filled" as const,
    mode: "email" as const,
    placeholder: "pl. <EMAIL>",
    onValueChanged: (e: any) => {
      formData.current.email = e.value;
    },
  };

  const passwordEditorOptions = {
    stylingMode: "filled" as const,
    mode: "password" as const,
    placeholder: "Erős jelszó",
    onValueChanged: (e: any) => {
      formData.current.password = e.value;
    },
  };

  const submitButtonOptions = {
    width: "100%",
    type: "default" as const,
    useSubmitBehavior: true,
    text: loading ? undefined : "Bejelentkezés",
    template: loading
      ? () => <LoadIndicator width={24} height={24} />
      : undefined,
    disabled: loading,
  };

  return (
    <CardAuth
      title="OMSNext bejelentkezés"
      description="Üdvözlünk az új rendszerben!"
    >
      <form onSubmit={onSubmit} className={styles.loginForm}>
        <Form formData={formData.current}>
          <Item
            dataField="email"
            editorType="dxTextBox"
            editorOptions={emailEditorOptions}
          >
            <RequiredRule message="E-mail cím megadása kötelező" />
            <EmailRule message="Érvényes e-mail címet adjon meg" />
            <Label text="E-mail" />
          </Item>

          <Item
            dataField="password"
            editorType="dxTextBox"
            editorOptions={passwordEditorOptions}
          >
            <RequiredRule message="Jelszó megadása kötelező" />
            <Label text="Jelszó" />
          </Item>

          {error && (
            <Item>
              <div className={styles.errorMessage}>{error}</div>
            </Item>
          )}

          <ButtonItem
            buttonOptions={submitButtonOptions}
            cssClass="login-button"
          />
        </Form>

        <div
          className={styles.formText}
          style={{ marginTop: "20px", textAlign: "center" }}
        >
          <a href="/forgot-password" style={{ color: "var(--accent-color)" }}>
            Elfelejtette jelszavát?
          </a>
        </div>
      </form>
    </CardAuth>
  );
}
