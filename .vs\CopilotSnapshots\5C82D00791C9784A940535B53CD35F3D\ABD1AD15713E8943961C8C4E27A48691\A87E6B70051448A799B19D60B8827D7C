﻿using DevExpress.Xpo;
using DevExpress.Xpo.DB.Helpers;
using DevExtreme.AspNet.Data;
using DevExtreme.AspNet.Mvc;
using Microsoft.AspNetCore.Mvc;
using omsnext.core.Models;
using System.Linq;

[ApiController]
[Route("api/[controller]")]
public class KtforgevController : ControllerBase
{
    private readonly Session _session;
    public KtforgevController(Session session)
    {
        _session = session;
    }

    [HttpGet]
    public IActionResult Get([FromQuery] DataSourceLoadOptions loadOptions)
    {
        try
        {
            var query = new XPQuery<Ktforgev>(_session)
                .Select(x => new KtforgevDto
                {
                    id = x.id,
                    hiv_szam = x.hiv_szam,
                    tk = x.tk,
                    fkv_1 = x.fkv_1,
                    dev_nem = x.dev_nem,
                    dev_ert = x.dev_ert,
                    fkv_2 = x.fkv_2,
                    fkv_3 = x.fkv_3,
                    mnk_szam = x.mnk_szam,
                    fda_t = x.fda_t,
                    rog_zito = x.rog_zito,
                    rog_dat = x.rog_dat,
                    meg_nev = x.meg_nev,
                    rel_azon = x.rel_azon,
                    nap_lo = x.nap_lo,
                    fej = x.fej,
                    ert_ek = x.ert_ek,
                    biz_dat = x.biz_dat,
                    par_kod = x.par_kod,
                    dol_kod = x.dol_kod,
                    cos_tcenter = x.cos_tcenter,
                    rel_azon2 = x.rel_azon2,
                    ber_kat = x.ber_kat,
                    men_nyi = x.men_nyi,
                    pro_jekt = x.pro_jekt,
                    afa_kod = x.afa_kod,
                    ert_ek_signed = x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek),
                    created_at = x.created_at,
                    updated_at = x.updated_at
                });

            // DevExtreme DataSourceLoader automatikusan kezeli a group summary-kat
            var result = DataSourceLoader.Load(query, loadOptions);
            return Ok(result);
        }
        catch (Exception ex)
        {
            return BadRequest(ex.ToString());
        }
    }

    
}