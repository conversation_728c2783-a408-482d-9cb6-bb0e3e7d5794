﻿using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using DevExpress.Xpf.Core;
using omsnext.wpf.Services;

namespace omsnext.wpf
{
    public partial class AdminDashboardUserControl : UserControl, INotifyPropertyChanged
    {
        private readonly ApiClient _apiClient;
        private ObservableCollection<PasswordResetRequestDto> _requests;
        private bool _isLoading;
        private string _currentStatusFilter = "Pending";

        public event PropertyChangedEventHandler? PropertyChanged;

        public ObservableCollection<PasswordResetRequestDto> Requests
        {
            get => _requests;
            set
            {
                _requests = value;
                OnPropertyChanged(nameof(Requests));
            }
        }

        public bool IsLoading
        {
            get => _isLoading;
            set
            {
                _isLoading = value;
                OnPropertyChanged(nameof(IsLoading));
                StatusLabel.Text = value ? "Betöltés..." : "Készen";
            }
        }

        public AdminDashboardUserControl(ApiClient apiClient)
        {
            InitializeComponent();
            _apiClient = apiClient;
            _requests = new ObservableCollection<PasswordResetRequestDto>();
            
            DataContext = this;
            RequestsGrid.ItemsSource = Requests;
            
            Loaded += AdminDashboardUserControl_Loaded;
        }

        private async void AdminDashboardUserControl_Loaded(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
        }

        private async Task LoadRequestsAsync()
        {
            if (IsLoading) return;

            IsLoading = true;
            try
            {
                var requests = await _apiClient.GetPasswordResetRequestsAsync();
                
                if (requests != null)
                {
                    Requests.Clear();
                    
                    var filteredRequests = string.IsNullOrEmpty(_currentStatusFilter) 
                        ? requests 
                        : requests.Where(r => r.Status == _currentStatusFilter);
                    
                    foreach (var request in filteredRequests)
                    {
                        Requests.Add(request);
                    }
                    
                    UpdateStatusLabels(requests);
                }
                else
                {
                    DXMessageBox.Show("Hiba történt az adatok betöltése során.", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        private void UpdateStatusLabels(List<PasswordResetRequestDto> allRequests)
        {
            RecordCountLabel.Text = $"Kérelmek: {Requests.Count}";
            PendingCountLabel.Text = allRequests.Count(r => r.Status == "Pending").ToString();
            CompletedCountLabel.Text = allRequests.Count(r => r.Status == "Completed").ToString();
        }

        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            await LoadRequestsAsync();
        }

        private async void StatusFilter_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (sender is ComboBox comboBox && comboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                _currentStatusFilter = selectedItem.Tag?.ToString() ?? "";
                await LoadRequestsAsync();
            }
        }

        private async void ApproveButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PasswordResetRequestDto request)
            {
                var result = DXMessageBox.Show(
                    $"Biztosan jóváhagyja a jelszó visszaállítási kérelmet?\n\nFelhasználó: {request.UserDisplayName}\nE-mail: {request.UserEmail}", 
                    "Kérelem jóváhagyása", 
                    MessageBoxButton.YesNo, 
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    await ProcessRequestAsync(request.Id, "Approved", "Adminisztrátor által jóváhagyva");
                }
            }
        }

        private async void RejectButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PasswordResetRequestDto request)
            {
                var inputDialog = new AdminNotesDialog("Kérelem elutasítása", "Kérjük, adja meg az elutasítás okát:");
                if (inputDialog.ShowDialog() == true)
                {
                    await ProcessRequestAsync(request.Id, "Rejected", inputDialog.Notes);
                }
            }
        }

        private async void ViewButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is PasswordResetRequestDto request)
            {
                var detailDialog = new RequestDetailDialog(request);
                detailDialog.ShowDialog();
            }
        }

        private async Task ProcessRequestAsync(long requestId, string action, string notes)
        {
            IsLoading = true;
            try
            {
                var success = await _apiClient.ProcessPasswordResetRequestAsync(requestId, action, notes);
                
                if (success)
                {
                    DXMessageBox.Show("Kérelem sikeresen feldolgozva!", "Siker", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                    await LoadRequestsAsync();
                }
                else
                {
                    DXMessageBox.Show("Hiba történt a kérelem feldolgozása során!", "Hiba", 
                        MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                DXMessageBox.Show($"Hiba: {ex.Message}", "Hiba", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                IsLoading = false;
            }
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // DTO a WPF projekthez
    public class PasswordResetRequestDto
    {
        public long Id { get; set; }
        public string UserEmail { get; set; } = string.Empty;
        public string UserDisplayName { get; set; } = string.Empty;
        public DateTime RequestDate { get; set; }
        public string Status { get; set; } = string.Empty;
        public string? ProcessedByAdminName { get; set; }
        public DateTime? ProcessedDate { get; set; }
        public string? AdminNotes { get; set; }
        public string RequestSource { get; set; } = string.Empty;
    }
}