{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/src/components/AppLayout.module.css"], "sourcesContent": ["/* App Layout stílusok */\n.appLayout {\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n  background-color: var(--base-bg);\n  font-family: \"Inter\", sans-serif;\n}\n\n.content {\n  display: flex;\n  flex-direction: column;\n  height: 100%;\n  width: 100%;\n}\n\n.toolbar {\n  background-color: white;\n  border-bottom: 1px solid var(--border-color);\n  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  z-index: 1000;\n}\n\n.pageContent {\n  flex: 1;\n  padding: var(--content-padding);\n  overflow-y: auto;\n  background-color: var(--base-bg);\n}\n\n/* Side Menu stílusok */\n.sideMenu {\n  width: 280px;\n  height: 100%;\n  background-color: white;\n  border-right: 1px solid var(--border-color);\n  box-shadow: 2px 0 4px rgba(0, 0, 0, 0.1);\n  display: flex;\n  flex-direction: column;\n}\n\n.menuHeader {\n  padding: 24px 20px;\n  border-bottom: 1px solid var(--border-color);\n  background-color: var(--accent-color);\n  color: white;\n}\n\n.menuHeader h3 {\n  margin: 0;\n  font-size: 18px;\n  font-weight: 500;\n}\n\n.menuItems {\n  flex: 1;\n  padding: 16px 0;\n}\n\n.menuItem {\n  display: flex;\n  align-items: center;\n  padding: 12px 20px;\n  cursor: pointer;\n  transition: background-color 0.2s ease;\n  color: var(--base-text-color);\n}\n\n.menuItem:hover {\n  background-color: #f5f5f5;\n}\n\n.menuIcon {\n  margin-right: 12px;\n  font-size: 16px;\n  width: 20px;\n  text-align: center;\n}\n\n/* DevExtreme komponensek felülírása */\n.appLayout :global(.dx-toolbar) {\n  background-color: white;\n  border: none;\n  padding: 0 16px;\n  height: 56px;\n}\n\n.appLayout :global(.dx-toolbar-item) {\n  margin: 0 4px;\n}\n\n.appLayout :global(.dx-toolbar-center) {\n  font-size: 18px;\n  font-weight: 500;\n  color: var(--base-text-color);\n}\n\n.appLayout :global(.dx-button.dx-button-mode-text) {\n  color: var(--base-text-color);\n}\n\n.appLayout :global(.dx-button.dx-button-mode-text:hover) {\n  background-color: rgba(0, 0, 0, 0.04);\n}\n\n.appLayout :global(.dx-drawer-panel-content) {\n  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);\n}\n\n/* Responsive design */\n@media (max-width: 768px) {\n  .sideMenu {\n    width: 260px;\n  }\n  \n  .pageContent {\n    padding: 16px;\n  }\n  \n  .menuHeader {\n    padding: 20px 16px;\n  }\n  \n  .menuItem {\n    padding: 14px 16px;\n  }\n}\n\n/* Dark mode támogatás */\n.dark .sideMenu {\n  background-color: #2d2d2d;\n  border-right-color: #404040;\n}\n\n.dark .menuHeader {\n  background-color: var(--accent-color);\n}\n\n.dark .menuItem:hover {\n  background-color: #404040;\n}\n\n.dark .appLayout :global(.dx-toolbar) {\n  background-color: #2d2d2d;\n  border-bottom-color: #404040;\n}\n\n.dark .toolbar {\n  border-bottom-color: #404040;\n}\n\n/* Card stílusok a tartalomhoz */\n.pageContent :global(.dx-card) {\n  background-color: white;\n  border-radius: 8px;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.04);\n  border: none;\n  margin-bottom: 20px;\n}\n\n.dark .pageContent :global(.dx-card) {\n  background-color: #2d2d2d;\n  box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.3);\n}\n"], "names": [], "mappings": "AACA;;;;;;;;AAQA;;;;;;;AAOA;;;;;;;AAOA;;;;;;;AAQA;;;;;;;;;;AAUA;;;;;;;AAOA;;;;;;AAMA;;;;;AAKA;;;;;;;;;AASA;;;;AAIA;;;;;;;AAQA;;;;;;;AAOA;;;;AAIA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;EAIA;;;;EAIA;;;;EAIA;;;;;AAMF;;;;;AAKA;;;;AAIA;;;;AAIA;;;;;AAKA;;;;AAKA;;;;;;;;AAQA", "debugId": null}}]}