﻿using DevExpress.Xpo;
using Microsoft.AspNetCore.Mvc;
using omsnext.core.Models;
using System.Linq.Expressions;

namespace omsnext.api.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class KtforgevWpfController : ControllerBase
    {
        private readonly Session _session;

        public KtforgevWpfController(Session session)
        {
            _session = session;
        }

        [HttpGet]
        public async Task<IActionResult> Get(
            [FromQuery] int skip = 0,
            [FromQuery] int take = 100,
            [FromQuery] string? sortField = null,
            [FromQuery] string? sortDirection = "asc",
            [FromQuery] string? filterField = null,
            [FromQuery] string? filterValue = null,
            [FromQuery] string? filterOperator = "contains")
        {
            try
            {
                var query = new XPQuery<Ktforgev>(_session);

                // Szűrés alkalmazása
                if (!string.IsNullOrEmpty(filterField) && !string.IsNullOrEmpty(filterValue))
                {
                    query = ApplyFilter(query, filterField, filterValue, filterOperator);
                }

                // Rendezés alkalmazása
                IOrderedQueryable<Ktforgev> orderedQuery;
                if (!string.IsNullOrEmpty(sortField))
                {
                    orderedQuery = ApplySort(query, sortField, sortDirection);
                }
                else
                {
                    orderedQuery = query.OrderBy(x => x.id); // Default rendezés
                }

                // Teljes rekordszám lekérdezése a szűrés után
                var totalCount = await Task.Run(() => query.Count());

                // Lapozás és projekció
                var items = await Task.Run(() =>
                    orderedQuery
                        .Skip(skip)
                        .Take(take)
                        .Select(x => new KtforgevDto
                        {
                            id = x.id,
                            hiv_szam = x.hiv_szam,
                            tk = x.tk,
                            fkv_1 = x.fkv_1,
                            dev_nem = x.dev_nem,
                            dev_ert = x.dev_ert,
                            fkv_2 = x.fkv_2,
                            fkv_3 = x.fkv_3,
                            mnk_szam = x.mnk_szam,
                            fda_t = x.fda_t,
                            rog_zito = x.rog_zito,
                            rog_dat = x.rog_dat,
                            meg_nev = x.meg_nev,
                            rel_azon = x.rel_azon,
                            nap_lo = x.nap_lo,
                            fej = x.fej,
                            ert_ek = x.ert_ek,
                            biz_dat = x.biz_dat,
                            par_kod = x.par_kod,
                            dol_kod = x.dol_kod,
                            cos_tcenter = x.cos_tcenter,
                            rel_azon2 = x.rel_azon2,
                            ber_kat = x.ber_kat,
                            men_nyi = x.men_nyi,
                            pro_jekt = x.pro_jekt,
                            afa_kod = x.afa_kod,
                            ert_ek_signed = x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek),
                            created_at = x.created_at,
                            updated_at = x.updated_at
                        })
                        .ToList());

                var result = new
                {
                    data = items,
                    totalCount = totalCount,
                    skip = skip,
                    take = take
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("summary")]
        public async Task<IActionResult> GetSummary(
            [FromQuery] string? groupField = null,
            [FromQuery] string? filterField = null,
            [FromQuery] string? filterValue = null,
            [FromQuery] string? filterOperator = "contains")
        {
            try
            {
                var query = new XPQuery<Ktforgev>(_session);

                // Szűrés alkalmazása
                if (!string.IsNullOrEmpty(filterField) && !string.IsNullOrEmpty(filterValue))
                {
                    query = ApplyFilter(query, filterField, filterValue, filterOperator);
                }

                var summary = await Task.Run(() => new
                {
                    totalRecords = query.Count(),
                    totalErtEk = query.Sum(x => x.ert_ek),
                    totalErtEkSigned = query.Sum(x => x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek)),
                    avgErtEk = query.Average(x => x.ert_ek)
                });

                return Ok(summary);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        private XPQuery<Ktforgev> ApplyFilter(XPQuery<Ktforgev> query, string field, string value, string op)
        {
            return field.ToLower() switch
            {
                "hiv_szam" => op switch
                {
                    "contains" => query.Where(x => x.hiv_szam.Contains(value)),
                    "equals" => query.Where(x => x.hiv_szam == value),
                    "startswith" => query.Where(x => x.hiv_szam.StartsWith(value)),
                    _ => query.Where(x => x.hiv_szam.Contains(value))
                },
                "tk" => query.Where(x => x.tk == value),
                "fkv_1" => op switch
                {
                    "contains" => query.Where(x => x.fkv_1.Contains(value)),
                    "equals" => query.Where(x => x.fkv_1 == value),
                    _ => query.Where(x => x.fkv_1.Contains(value))
                },
                "meg_nev" => op switch
                {
                    "contains" => query.Where(x => x.meg_nev.Contains(value)),
                    "equals" => query.Where(x => x.meg_nev == value),
                    _ => query.Where(x => x.meg_nev.Contains(value))
                },
                "par_kod" => op switch
                {
                    "contains" => query.Where(x => x.par_kod.Contains(value)),
                    "equals" => query.Where(x => x.par_kod == value),
                    _ => query.Where(x => x.par_kod.Contains(value))
                },
                "ert_ek" => decimal.TryParse(value, out var ertEkValue) ? op switch
                {
                    "equals" => query.Where(x => x.ert_ek == ertEkValue),
                    "greater" => query.Where(x => x.ert_ek > ertEkValue),
                    "less" => query.Where(x => x.ert_ek < ertEkValue),
                    _ => query.Where(x => x.ert_ek == ertEkValue)
                } : query,
                _ => query
            };
        }

        private IOrderedQueryable<Ktforgev> ApplySort(XPQuery<Ktforgev> query, string field, string direction)
        {
            var isAscending = direction.ToLower() != "desc";
            
            return field.ToLower() switch
            {
                "id" => isAscending ? query.OrderBy(x => x.id) : query.OrderByDescending(x => x.id),
                "hiv_szam" => isAscending ? query.OrderBy(x => x.hiv_szam) : query.OrderByDescending(x => x.hiv_szam),
                "tk" => isAscending ? query.OrderBy(x => x.tk) : query.OrderByDescending(x => x.tk),
                "fkv_1" => isAscending ? query.OrderBy(x => x.fkv_1) : query.OrderByDescending(x => x.fkv_1),
                "meg_nev" => isAscending ? query.OrderBy(x => x.meg_nev) : query.OrderByDescending(x => x.meg_nev),
                "ert_ek" => isAscending ? query.OrderBy(x => x.ert_ek) : query.OrderByDescending(x => x.ert_ek),
                "ert_ek_signed" => isAscending ? 
                    query.OrderBy(x => x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek)) : 
                    query.OrderByDescending(x => x.tk == "T" ? x.ert_ek : (x.tk == "K" ? -x.ert_ek : x.ert_ek)),
                "created_at" => isAscending ? query.OrderBy(x => x.created_at) : query.OrderByDescending(x => x.created_at),
                _ => isAscending ? query.OrderBy(x => x.id) : query.OrderByDescending(x => x.id)
            };
        }
    }
}