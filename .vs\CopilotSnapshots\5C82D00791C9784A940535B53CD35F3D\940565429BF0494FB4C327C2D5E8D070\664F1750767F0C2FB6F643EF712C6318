﻿using System.Windows;
using System.Windows.Media;
using DevExpress.Xpf.Core;
using omsnext.shared.DTO;

namespace omsnext.wpf
{
    public partial class RequestDetailDialog : ThemedWindow
    {
        public RequestDetailDialog(PasswordResetRequestDto request)
        {
            InitializeComponent();
            
            LoadRequestData(request);
        }

        private void LoadRequestData(PasswordResetRequestDto request)
        {
            // Alapinformációk
            RequestIdLabel.Text = $"Kérelem ID: #{request.Id}";
            UserNameLabel.Text = request.UserDisplayName;
            UserEmailLabel.Text = request.UserEmail;
            RequestSourceLabel.Text = request.RequestSource;
            RequestDateLabel.Text = request.RequestDate.ToString("yyyy. MM. dd. HH:mm:ss");
            IpAddressLabel.Text = "N/A"; // TODO: IP cím ha van

            // Állapot
            StatusLabel.Text = GetStatusText(request.Status);
            SetStatusColors(request.Status);

            // Feldolgozás információk
            if (!string.IsNullOrEmpty(request.ProcessedByAdminName) || request.ProcessedDate.HasValue)
            {
                ProcessingInfoBorder.Visibility = Visibility.Visible;
                ProcessedByLabel.Text = request.ProcessedByAdminName ?? "N/A";
                ProcessedDateLabel.Text = request.ProcessedDate?.ToString("yyyy. MM. dd. HH:mm:ss") ?? "N/A";
            }

            // Admin jegyzet
            if (!string.IsNullOrEmpty(request.AdminNotes))
            {
                AdminNotesBorder.Visibility = Visibility.Visible;
                AdminNotesLabel.Text = request.AdminNotes;
            }
        }

        private string GetStatusText(string status)
        {
            return status switch
            {
                "Pending" => "⏳ Függőben",
                "Approved" => "✅ Jóváhagyott",
                "Rejected" => "❌ Elutasított",
                "Completed" => "🎉 Befejezett",
                _ => status
            };
        }

        private void SetStatusColors(string status)
        {
            switch (status)
            {
                case "Pending":
                    StatusBorder.Background = new SolidColorBrush(Color.FromRgb(254, 243, 199)); // #FEF3C7
                    StatusBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(245, 158, 11)); // #F59E0B
                    StatusLabel.Foreground = new SolidColorBrush(Color.FromRgb(146, 64, 14)); // #92400E
                    break;
                case "Approved":
                    StatusBorder.Background = new SolidColorBrush(Color.FromRgb(209, 250, 229)); // #D1FAE5
                    StatusBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(16, 185, 129)); // #10B981
                    StatusLabel.Foreground = new SolidColorBrush(Color.FromRgb(5, 150, 105)); // #059669
                    break;
                case "Rejected":
                    StatusBorder.Background = new SolidColorBrush(Color.FromRgb(254, 226, 226)); // #FEE2E2
                    StatusBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(239, 68, 68)); // #EF4444
                    StatusLabel.Foreground = new SolidColorBrush(Color.FromRgb(220, 38, 38)); // #DC2626
                    break;
                case "Completed":
                    StatusBorder.Background = new SolidColorBrush(Color.FromRgb(224, 231, 255)); // #E0E7FF
                    StatusBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(99, 102, 241)); // #6366F1
                    StatusLabel.Foreground = new SolidColorBrush(Color.FromRgb(67, 56, 202)); // #4338CA
                    break;
                default:
                    StatusBorder.Background = new SolidColorBrush(Color.FromRgb(243, 244, 246)); // #F3F4F6
                    StatusBorder.BorderBrush = new SolidColorBrush(Color.FromRgb(209, 213, 219)); // #D1D5DB
                    StatusLabel.Foreground = new SolidColorBrush(Color.FromRgb(55, 65, 81)); // #374151
                    break;
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        protected override void OnKeyDown(System.Windows.Input.KeyEventArgs e)
        {
            if (e.Key == System.Windows.Input.Key.Escape)
            {
                Close();
            }
            
            base.OnKeyDown(e);
        }
    }
}