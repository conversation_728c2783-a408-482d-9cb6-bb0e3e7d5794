﻿using omsnext.shared.Models;
using DevExpress.Xpo;
using BCrypt.Net;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Linq;

namespace omsnext.api.Data
{
    public class SeedData
    {
        public static async Task EnsureSeededAsync(UnitOfWork uow, IConfiguration config, ILogger logger)
        {
            try
            {
                string adminEmail = config["Seed:AdminEmail"] ?? "<EMAIL>";
                string adminPassword = config["Seed:AdminPassword"] ?? "OmsNext123!";
                string adminDisplayName = config["Seed:AdminDisplayName"] ?? "Rendszergazda";
                string adminRoleName = "Admin";

                // Create Admin Role if not exists
                var adminRole = uow.Query<Role>().FirstOrDefault(r => r.Name == adminRoleName);
                if (adminRole == null)
                {
                    adminRole = new Role(uow) { Name = adminRoleName };
                    adminRole.Save();
                }

                // Create Admin User if not exists
                var admin = uow.Query<User>().FirstOrDefault(u => u.Email == adminEmail);
                if (admin == null)
                {
                    admin = new User(uow)
                    {
                        Email = adminEmail,
                        PasswordHash = BCrypt.Net.BCrypt.HashPassword(adminPassword),
                        DisplayName = adminDisplayName,
                        IsActive = true,
                    };
                    admin.Roles.Add(adminRole);
                    admin.Save();
                    await uow.CommitChangesAsync();
                    logger.LogInformation("Admin user seeded: {Email}", adminEmail);
                }
                else
                {
                    // Ensure admin user has admin role
                    if (!admin.Roles.Contains(adminRole))
                    {
                        admin.Roles.Add(adminRole);
                        admin.Save();
                        await uow.CommitChangesAsync();
                    }
                    logger.LogInformation("Admin user already exists: {Email}", adminEmail);
                }
            }
            catch (Exception ex)
            {
                logger.LogError(ex, "Error occurred while seeding admin user.");
                throw;
            }
        }
    }
}
