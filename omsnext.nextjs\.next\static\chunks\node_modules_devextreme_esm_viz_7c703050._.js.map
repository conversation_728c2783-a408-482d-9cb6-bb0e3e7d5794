{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/components/consts.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/components/consts.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nexport default {\r\n    events: {\r\n        mouseover: \"mouseover\",\r\n        mouseout: \"mouseout\",\r\n        mousemove: \"mousemove\",\r\n        touchstart: \"touchstart\",\r\n        touchmove: \"touchmove\",\r\n        touchend: \"touchend\",\r\n        mousedown: \"mousedown\",\r\n        mouseup: \"mouseup\",\r\n        click: \"click\",\r\n        selectSeries: \"selectseries\",\r\n        deselectSeries: \"deselectseries\",\r\n        selectPoint: \"selectpoint\",\r\n        deselectPoint: \"deselectpoint\",\r\n        showPointTooltip: \"showpointtooltip\",\r\n        hidePointTooltip: \"hidepointtooltip\"\r\n    },\r\n    states: {\r\n        hover: \"hover\",\r\n        normal: \"normal\",\r\n        selection: \"selection\",\r\n        normalMark: 0,\r\n        hoverMark: 1,\r\n        selectedMark: 2,\r\n        applyHover: \"applyHover\",\r\n        applySelected: \"applySelected\",\r\n        resetItem: \"resetItem\"\r\n    },\r\n    radialLabelIndent: 30,\r\n    pieLabelSpacing: 10,\r\n    pieSeriesSpacing: 4\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;uCACc;IACX,QAAQ;QACJ,WAAW;QACX,UAAU;QACV,WAAW;QACX,YAAY;QACZ,WAAW;QACX,UAAU;QACV,WAAW;QACX,SAAS;QACT,OAAO;QACP,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,eAAe;QACf,kBAAkB;QAClB,kBAAkB;IACtB;IACA,QAAQ;QACJ,OAAO;QACP,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,WAAW;QACX,cAAc;QACd,YAAY;QACZ,eAAe;QACf,WAAW;IACf;IACA,mBAAmB;IACnB,iBAAiB;IACjB,kBAAkB;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/components/parse_utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/components/parse_utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport dateSerialization from \"../../core/utils/date_serialization\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nconst parsers = {\r\n    string: function(val) {\r\n        return isDefined(val) ? \"\" + val : val\r\n    },\r\n    numeric: function(val) {\r\n        if (!isDefined(val)) {\r\n            return val\r\n        }\r\n        let parsedVal = Number(val);\r\n        if (isNaN(parsedVal)) {\r\n            parsedVal = void 0\r\n        }\r\n        return parsedVal\r\n    },\r\n    datetime: function(val) {\r\n        if (!isDefined(val)) {\r\n            return val\r\n        }\r\n        let parsedVal;\r\n        const numVal = Number(val);\r\n        if (!isNaN(numVal)) {\r\n            parsedVal = new Date(numVal)\r\n        } else {\r\n            parsedVal = dateSerialization.deserializeDate(val)\r\n        }\r\n        if (isNaN(Number(parsedVal))) {\r\n            parsedVal = void 0\r\n        }\r\n        return parsedVal\r\n    }\r\n};\r\nexport function correctValueType(type) {\r\n    return \"numeric\" === type || \"datetime\" === type || \"string\" === type ? type : \"\"\r\n}\r\nexport const getParser = function(valueType) {\r\n    return parsers[correctValueType(valueType)] || noop\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAAA;AAGA;AACA;AAAA;;;;AAGA,MAAM,UAAU;IACZ,QAAQ,SAAS,GAAG;QAChB,OAAO,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,KAAK,MAAM;IACvC;IACA,SAAS,SAAS,GAAG;QACjB,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM;YACjB,OAAO;QACX;QACA,IAAI,YAAY,OAAO;QACvB,IAAI,MAAM,YAAY;YAClB,YAAY,KAAK;QACrB;QACA,OAAO;IACX;IACA,UAAU,SAAS,GAAG;QAClB,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM;YACjB,OAAO;QACX;QACA,IAAI;QACJ,MAAM,SAAS,OAAO;QACtB,IAAI,CAAC,MAAM,SAAS;YAChB,YAAY,IAAI,KAAK;QACzB,OAAO;YACH,YAAY,2KAAA,CAAA,UAAiB,CAAC,eAAe,CAAC;QAClD;QACA,IAAI,MAAM,OAAO,aAAa;YAC1B,YAAY,KAAK;QACrB;QACA,OAAO;IACX;AACJ;AACO,SAAS,iBAAiB,IAAI;IACjC,OAAO,cAAc,QAAQ,eAAe,QAAQ,aAAa,OAAO,OAAO;AACnF;AACO,MAAM,YAAY,SAAS,SAAS;IACvC,OAAO,OAAO,CAAC,iBAAiB,WAAW,IAAI,kLAAA,CAAA,OAAI;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 112, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/components/chart_theme_manager.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/components/chart_theme_manager.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    isString as _isString,\r\n    isDefined as _isDefined,\r\n    isNumeric,\r\n    isPlainObject\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    BaseThemeManager\r\n} from \"../core/base_theme_manager\";\r\nimport {\r\n    normalizeEnum as _normalizeEnum,\r\n    extractColor\r\n} from \"../core/utils\";\r\nexport const ThemeManager = BaseThemeManager.inherit(function() {\r\n    const processAxisOptions = function(axisOptions) {\r\n        if (!axisOptions) {\r\n            return {}\r\n        }\r\n        axisOptions = extend(true, {}, axisOptions);\r\n        axisOptions.title = (options = axisOptions.title, _isString(options) ? {\r\n            text: options\r\n        } : options);\r\n        var options;\r\n        if (\"logarithmic\" === axisOptions.type && axisOptions.logarithmBase <= 0 || axisOptions.logarithmBase && !isNumeric(axisOptions.logarithmBase)) {\r\n            axisOptions.logarithmBase = void 0;\r\n            axisOptions.logarithmBaseError = true\r\n        }\r\n        if (axisOptions.label) {\r\n            if (axisOptions.label.alignment) {\r\n                axisOptions.label.userAlignment = true\r\n            }\r\n        }\r\n        return axisOptions\r\n    };\r\n    const applyParticularAxisOptions = function(name, userOptions, rotated) {\r\n        const theme = this._theme;\r\n        const position = !(rotated ^ \"valueAxis\" === name) ? \"horizontalAxis\" : \"verticalAxis\";\r\n        const processedUserOptions = processAxisOptions(userOptions);\r\n        const commonAxisSettings = processAxisOptions(this._userOptions.commonAxisSettings);\r\n        const mergeOptions = extend(true, {}, theme.commonAxisSettings, theme[position], theme[name], commonAxisSettings, processedUserOptions);\r\n        mergeOptions.workWeek = processedUserOptions.workWeek || theme[name].workWeek;\r\n        mergeOptions.forceUserTickInterval |= _isDefined(processedUserOptions.tickInterval) && !_isDefined(processedUserOptions.axisDivisionFactor);\r\n        return mergeOptions\r\n    };\r\n    const mergeOptions = function(name, userOptions) {\r\n        userOptions = userOptions || this._userOptions[name];\r\n        const theme = this._theme[name];\r\n        let result = this._mergedSettings[name];\r\n        if (result) {\r\n            return result\r\n        }\r\n        if (isPlainObject(theme) && isPlainObject(userOptions)) {\r\n            result = extend(true, {}, theme, userOptions)\r\n        } else {\r\n            result = _isDefined(userOptions) ? userOptions : theme\r\n        }\r\n        this._mergedSettings[name] = result;\r\n        return result\r\n    };\r\n    const applyParticularTheme = {\r\n        base: mergeOptions,\r\n        argumentAxis: applyParticularAxisOptions,\r\n        valueAxisRangeSelector: function() {\r\n            return mergeOptions.call(this, \"valueAxis\")\r\n        },\r\n        valueAxis: applyParticularAxisOptions,\r\n        series: function(name, userOptions, seriesCount) {\r\n            const that = this;\r\n            const theme = that._theme;\r\n            let userCommonSettings = that._userOptions.commonSeriesSettings || {};\r\n            const themeCommonSettings = theme.commonSeriesSettings;\r\n            const widgetType = that._themeSection.split(\".\").slice(-1)[0];\r\n            const type = _normalizeEnum(userOptions.type || userCommonSettings.type || themeCommonSettings.type || \"pie\" === widgetType && theme.type);\r\n            const palette = that.palette;\r\n            const isBar = ~type.indexOf(\"bar\");\r\n            const isLine = ~type.indexOf(\"line\");\r\n            const isArea = ~type.indexOf(\"area\");\r\n            const isBubble = \"bubble\" === type;\r\n            let mainSeriesColor;\r\n            const resolveLabelsOverlapping = that.getOptions(\"resolveLabelsOverlapping\");\r\n            const containerBackgroundColor = that.getOptions(\"containerBackgroundColor\");\r\n            const seriesTemplate = applyParticularTheme.seriesTemplate.call(this);\r\n            let seriesVisibility;\r\n            if (isBar || isBubble) {\r\n                userOptions = extend(true, {}, userCommonSettings, userCommonSettings[type], userOptions);\r\n                seriesVisibility = userOptions.visible;\r\n                userCommonSettings = {\r\n                    type: {}\r\n                };\r\n                extend(true, userOptions, userOptions.point);\r\n                userOptions.visible = seriesVisibility\r\n            }\r\n            const settings = extend(true, {\r\n                aggregation: {}\r\n            }, themeCommonSettings, themeCommonSettings[type], userCommonSettings, userCommonSettings[type], userOptions);\r\n            settings.aggregation.enabled = \"chart\" === widgetType && !!settings.aggregation.enabled;\r\n            settings.type = type;\r\n            settings.widgetType = widgetType;\r\n            settings.containerBackgroundColor = containerBackgroundColor;\r\n            if (\"pie\" !== widgetType) {\r\n                mainSeriesColor = extractColor(settings.color, true) || palette.getNextColor(seriesCount)\r\n            } else {\r\n                mainSeriesColor = function(argument, index, count) {\r\n                    const cat = `${argument}-${index}`;\r\n                    if (!that._multiPieColors[cat]) {\r\n                        that._multiPieColors[cat] = palette.getNextColor(count)\r\n                    }\r\n                    return that._multiPieColors[cat]\r\n                }\r\n            }\r\n            settings.mainSeriesColor = mainSeriesColor;\r\n            settings.resolveLabelsOverlapping = resolveLabelsOverlapping;\r\n            if (settings.label && (isLine || isArea && \"rangearea\" !== type || \"scatter\" === type)) {\r\n                settings.label.position = \"outside\"\r\n            }\r\n            if (seriesTemplate) {\r\n                settings.nameField = seriesTemplate.nameField\r\n            }\r\n            return settings\r\n        },\r\n        animation: function(name) {\r\n            let userOptions = this._userOptions[name];\r\n            userOptions = isPlainObject(userOptions) ? userOptions : _isDefined(userOptions) ? {\r\n                enabled: !!userOptions\r\n            } : {};\r\n            return mergeOptions.call(this, name, userOptions)\r\n        },\r\n        seriesTemplate() {\r\n            const value = mergeOptions.call(this, \"seriesTemplate\");\r\n            if (value) {\r\n                value.nameField = value.nameField || \"series\"\r\n            }\r\n            return value\r\n        },\r\n        zoomAndPan() {\r\n            function parseOption(option) {\r\n                option = _normalizeEnum(option);\r\n                const pan = \"pan\" === option || \"both\" === option;\r\n                const zoom = \"zoom\" === option || \"both\" === option;\r\n                return {\r\n                    pan: pan,\r\n                    zoom: zoom,\r\n                    none: !pan && !zoom\r\n                }\r\n            }\r\n            const options = mergeOptions.call(this, \"zoomAndPan\");\r\n            return {\r\n                valueAxis: parseOption(options.valueAxis),\r\n                argumentAxis: parseOption(options.argumentAxis),\r\n                dragToZoom: !!options.dragToZoom,\r\n                dragBoxStyle: {\r\n                    class: \"dxc-shutter\",\r\n                    fill: options.dragBoxStyle.color,\r\n                    opacity: options.dragBoxStyle.opacity\r\n                },\r\n                panKey: options.panKey,\r\n                allowMouseWheel: !!options.allowMouseWheel,\r\n                allowTouchGestures: !!options.allowTouchGestures\r\n            }\r\n        }\r\n    };\r\n    return {\r\n        _themeSection: \"chart\",\r\n        ctor: function(params) {\r\n            this.callBase.apply(this, arguments);\r\n            const options = params.options || {};\r\n            this._userOptions = options;\r\n            this._mergeAxisTitleOptions = [];\r\n            this._multiPieColors = {};\r\n            this._callback = noop\r\n        },\r\n        dispose: function() {\r\n            this.palette && this.palette.dispose();\r\n            this.palette = this._userOptions = this._mergedSettings = this._multiPieColors = null;\r\n            return this.callBase.apply(this, arguments)\r\n        },\r\n        resetPalette: function() {\r\n            this.palette.reset();\r\n            this._multiPieColors = {}\r\n        },\r\n        getOptions: function(name) {\r\n            return (applyParticularTheme[name] || applyParticularTheme.base).apply(this, arguments)\r\n        },\r\n        refresh: function() {\r\n            this._mergedSettings = {};\r\n            return this.callBase.apply(this, arguments)\r\n        },\r\n        _initializeTheme: function() {\r\n            this.callBase.apply(this, arguments);\r\n            this.updatePalette()\r\n        },\r\n        resetOptions: function(name) {\r\n            this._mergedSettings[name] = null\r\n        },\r\n        update: function(options) {\r\n            this._userOptions = options\r\n        },\r\n        updatePalette: function() {\r\n            this.palette = this.createPalette(this.getOptions(\"palette\"), {\r\n                useHighlight: true,\r\n                extensionMode: this.getOptions(\"paletteExtensionMode\")\r\n            })\r\n        }\r\n    }\r\n}());\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AAMA;AAAA;AAGA;AAGA;;;;;;AAIO,MAAM,eAAe,yKAAA,CAAA,mBAAgB,CAAC,OAAO,CAAC;IACjD,MAAM,qBAAqB,SAAS,WAAW;QAC3C,IAAI,CAAC,aAAa;YACd,OAAO,CAAC;QACZ;QACA,cAAc,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG;QAC/B,YAAY,KAAK,GAAG,CAAC,UAAU,YAAY,KAAK,EAAE,CAAA,GAAA,gLAAA,CAAA,WAAS,AAAD,EAAE,WAAW;YACnE,MAAM;QACV,IAAI,OAAO;QACX,IAAI;QACJ,IAAI,kBAAkB,YAAY,IAAI,IAAI,YAAY,aAAa,IAAI,KAAK,YAAY,aAAa,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY,aAAa,GAAG;YAC5I,YAAY,aAAa,GAAG,KAAK;YACjC,YAAY,kBAAkB,GAAG;QACrC;QACA,IAAI,YAAY,KAAK,EAAE;YACnB,IAAI,YAAY,KAAK,CAAC,SAAS,EAAE;gBAC7B,YAAY,KAAK,CAAC,aAAa,GAAG;YACtC;QACJ;QACA,OAAO;IACX;IACA,MAAM,6BAA6B,SAAS,IAAI,EAAE,WAAW,EAAE,OAAO;QAClE,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,WAAW,CAAC,CAAC,UAAU,gBAAgB,IAAI,IAAI,mBAAmB;QACxE,MAAM,uBAAuB,mBAAmB;QAChD,MAAM,qBAAqB,mBAAmB,IAAI,CAAC,YAAY,CAAC,kBAAkB;QAClF,MAAM,eAAe,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,MAAM,kBAAkB,EAAE,KAAK,CAAC,SAAS,EAAE,KAAK,CAAC,KAAK,EAAE,oBAAoB;QAClH,aAAa,QAAQ,GAAG,qBAAqB,QAAQ,IAAI,KAAK,CAAC,KAAK,CAAC,QAAQ;QAC7E,aAAa,qBAAqB,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,qBAAqB,YAAY,KAAK,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,qBAAqB,kBAAkB;QAC1I,OAAO;IACX;IACA,MAAM,eAAe,SAAS,IAAI,EAAE,WAAW;QAC3C,cAAc,eAAe,IAAI,CAAC,YAAY,CAAC,KAAK;QACpD,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;QAC/B,IAAI,SAAS,IAAI,CAAC,eAAe,CAAC,KAAK;QACvC,IAAI,QAAQ;YACR,OAAO;QACX;QACA,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,cAAc;YACpD,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,OAAO;QACrC,OAAO;YACH,SAAS,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,eAAe,cAAc;QACrD;QACA,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG;QAC7B,OAAO;IACX;IACA,MAAM,uBAAuB;QACzB,MAAM;QACN,cAAc;QACd,wBAAwB;YACpB,OAAO,aAAa,IAAI,CAAC,IAAI,EAAE;QACnC;QACA,WAAW;QACX,QAAQ,SAAS,IAAI,EAAE,WAAW,EAAE,WAAW;YAC3C,MAAM,OAAO,IAAI;YACjB,MAAM,QAAQ,KAAK,MAAM;YACzB,IAAI,qBAAqB,KAAK,YAAY,CAAC,oBAAoB,IAAI,CAAC;YACpE,MAAM,sBAAsB,MAAM,oBAAoB;YACtD,MAAM,aAAa,KAAK,aAAa,CAAC,KAAK,CAAC,KAAK,KAAK,CAAC,CAAC,EAAE,CAAC,EAAE;YAC7D,MAAM,OAAO,CAAA,GAAA,4JAAA,CAAA,gBAAc,AAAD,EAAE,YAAY,IAAI,IAAI,mBAAmB,IAAI,IAAI,oBAAoB,IAAI,IAAI,UAAU,cAAc,MAAM,IAAI;YACzI,MAAM,UAAU,KAAK,OAAO;YAC5B,MAAM,QAAQ,CAAC,KAAK,OAAO,CAAC;YAC5B,MAAM,SAAS,CAAC,KAAK,OAAO,CAAC;YAC7B,MAAM,SAAS,CAAC,KAAK,OAAO,CAAC;YAC7B,MAAM,WAAW,aAAa;YAC9B,IAAI;YACJ,MAAM,2BAA2B,KAAK,UAAU,CAAC;YACjD,MAAM,2BAA2B,KAAK,UAAU,CAAC;YACjD,MAAM,iBAAiB,qBAAqB,cAAc,CAAC,IAAI,CAAC,IAAI;YACpE,IAAI;YACJ,IAAI,SAAS,UAAU;gBACnB,cAAc,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,GAAG,oBAAoB,kBAAkB,CAAC,KAAK,EAAE;gBAC7E,mBAAmB,YAAY,OAAO;gBACtC,qBAAqB;oBACjB,MAAM,CAAC;gBACX;gBACA,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,aAAa,YAAY,KAAK;gBAC3C,YAAY,OAAO,GAAG;YAC1B;YACA,MAAM,WAAW,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM;gBAC1B,aAAa,CAAC;YAClB,GAAG,qBAAqB,mBAAmB,CAAC,KAAK,EAAE,oBAAoB,kBAAkB,CAAC,KAAK,EAAE;YACjG,SAAS,WAAW,CAAC,OAAO,GAAG,YAAY,cAAc,CAAC,CAAC,SAAS,WAAW,CAAC,OAAO;YACvF,SAAS,IAAI,GAAG;YAChB,SAAS,UAAU,GAAG;YACtB,SAAS,wBAAwB,GAAG;YACpC,IAAI,UAAU,YAAY;gBACtB,kBAAkB,CAAA,GAAA,4JAAA,CAAA,eAAY,AAAD,EAAE,SAAS,KAAK,EAAE,SAAS,QAAQ,YAAY,CAAC;YACjF,OAAO;gBACH,kBAAkB,SAAS,QAAQ,EAAE,KAAK,EAAE,KAAK;oBAC7C,MAAM,MAAM,AAAC,GAAc,OAAZ,UAAS,KAAS,OAAN;oBAC3B,IAAI,CAAC,KAAK,eAAe,CAAC,IAAI,EAAE;wBAC5B,KAAK,eAAe,CAAC,IAAI,GAAG,QAAQ,YAAY,CAAC;oBACrD;oBACA,OAAO,KAAK,eAAe,CAAC,IAAI;gBACpC;YACJ;YACA,SAAS,eAAe,GAAG;YAC3B,SAAS,wBAAwB,GAAG;YACpC,IAAI,SAAS,KAAK,IAAI,CAAC,UAAU,UAAU,gBAAgB,QAAQ,cAAc,IAAI,GAAG;gBACpF,SAAS,KAAK,CAAC,QAAQ,GAAG;YAC9B;YACA,IAAI,gBAAgB;gBAChB,SAAS,SAAS,GAAG,eAAe,SAAS;YACjD;YACA,OAAO;QACX;QACA,WAAW,SAAS,IAAI;YACpB,IAAI,cAAc,IAAI,CAAC,YAAY,CAAC,KAAK;YACzC,cAAc,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,eAAe,cAAc,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,eAAe;gBAC/E,SAAS,CAAC,CAAC;YACf,IAAI,CAAC;YACL,OAAO,aAAa,IAAI,CAAC,IAAI,EAAE,MAAM;QACzC;QACA;YACI,MAAM,QAAQ,aAAa,IAAI,CAAC,IAAI,EAAE;YACtC,IAAI,OAAO;gBACP,MAAM,SAAS,GAAG,MAAM,SAAS,IAAI;YACzC;YACA,OAAO;QACX;QACA;YACI,SAAS,YAAY,MAAM;gBACvB,SAAS,CAAA,GAAA,4JAAA,CAAA,gBAAc,AAAD,EAAE;gBACxB,MAAM,MAAM,UAAU,UAAU,WAAW;gBAC3C,MAAM,OAAO,WAAW,UAAU,WAAW;gBAC7C,OAAO;oBACH,KAAK;oBACL,MAAM;oBACN,MAAM,CAAC,OAAO,CAAC;gBACnB;YACJ;YACA,MAAM,UAAU,aAAa,IAAI,CAAC,IAAI,EAAE;YACxC,OAAO;gBACH,WAAW,YAAY,QAAQ,SAAS;gBACxC,cAAc,YAAY,QAAQ,YAAY;gBAC9C,YAAY,CAAC,CAAC,QAAQ,UAAU;gBAChC,cAAc;oBACV,OAAO;oBACP,MAAM,QAAQ,YAAY,CAAC,KAAK;oBAChC,SAAS,QAAQ,YAAY,CAAC,OAAO;gBACzC;gBACA,QAAQ,QAAQ,MAAM;gBACtB,iBAAiB,CAAC,CAAC,QAAQ,eAAe;gBAC1C,oBAAoB,CAAC,CAAC,QAAQ,kBAAkB;YACpD;QACJ;IACJ;IACA,OAAO;QACH,eAAe;QACf,MAAM,SAAS,MAAM;YACjB,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;YAC1B,MAAM,UAAU,OAAO,OAAO,IAAI,CAAC;YACnC,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,sBAAsB,GAAG,EAAE;YAChC,IAAI,CAAC,eAAe,GAAG,CAAC;YACxB,IAAI,CAAC,SAAS,GAAG,kLAAA,CAAA,OAAI;QACzB;QACA,SAAS;YACL,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO;YACpC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,eAAe,GAAG;YACjF,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QACrC;QACA,cAAc;YACV,IAAI,CAAC,OAAO,CAAC,KAAK;YAClB,IAAI,CAAC,eAAe,GAAG,CAAC;QAC5B;QACA,YAAY,SAAS,IAAI;YACrB,OAAO,CAAC,oBAAoB,CAAC,KAAK,IAAI,qBAAqB,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE;QACjF;QACA,SAAS;YACL,IAAI,CAAC,eAAe,GAAG,CAAC;YACxB,OAAO,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;QACrC;QACA,kBAAkB;YACd,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,EAAE;YAC1B,IAAI,CAAC,aAAa;QACtB;QACA,cAAc,SAAS,IAAI;YACvB,IAAI,CAAC,eAAe,CAAC,KAAK,GAAG;QACjC;QACA,QAAQ,SAAS,OAAO;YACpB,IAAI,CAAC,YAAY,GAAG;QACxB;QACA,eAAe;YACX,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,UAAU,CAAC,YAAY;gBAC1D,cAAc;gBACd,eAAe,IAAI,CAAC,UAAU,CAAC;YACnC;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 331, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/components/data_validator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/components/data_validator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isDefined as _isDefined,\r\n    isFunction as _isFunction,\r\n    isString as _isString,\r\n    isDate as _isDate,\r\n    isNumeric as _isNumber,\r\n    isObject as _isObject\r\n} from \"../../core/utils/type\";\r\nconst STRING = \"string\";\r\nconst NUMERIC = \"numeric\";\r\nconst DATETIME = \"datetime\";\r\nconst DISCRETE = \"discrete\";\r\nconst SEMIDISCRETE = \"semidiscrete\";\r\nconst CONTINUOUS = \"continuous\";\r\nconst LOGARITHMIC = \"logarithmic\";\r\nconst VALUE_TYPE = \"valueType\";\r\nconst ARGUMENT_TYPE = \"argumentType\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    enumParser\r\n} from \"../core/utils\";\r\nconst axisTypeParser = enumParser([STRING, NUMERIC, DATETIME]);\r\nimport {\r\n    getParser as _getParser\r\n} from \"./parse_utils\";\r\nconst _isArray = Array.isArray;\r\n\r\nfunction groupingValues(data, others, valueField, index) {\r\n    if (index >= 0) {\r\n        data.slice(index).forEach((function(cell) {\r\n            if (_isDefined(cell[valueField])) {\r\n                others[valueField] += cell[valueField];\r\n                cell[valueField] = void 0\r\n            }\r\n        }))\r\n    }\r\n}\r\n\r\nfunction processGroups(groups) {\r\n    groups.forEach((function(group) {\r\n        group.valueType = group.valueAxisType = null;\r\n        group.series.forEach((function(series) {\r\n            series.updateDataType({})\r\n        }));\r\n        group.valueAxis && group.valueAxis.resetTypes(VALUE_TYPE)\r\n    }))\r\n}\r\n\r\nfunction sortValues(data, asc, selector) {\r\n    const func = asc ? function(a, b) {\r\n        return a - b\r\n    } : function(a, b) {\r\n        return b - a\r\n    };\r\n    data.sort((function(a, b) {\r\n        const valA = selector(a);\r\n        const valB = selector(b);\r\n        const aa = _isDefined(valA) ? 1 : 0;\r\n        const bb = _isDefined(valB) ? 1 : 0;\r\n        return aa && bb ? func(valA, valB) : func(aa, bb)\r\n    }));\r\n    return data\r\n}\r\n\r\nfunction resetArgumentAxes(axes) {\r\n    axes && axes.forEach((function(axis) {\r\n        axis.resetTypes(ARGUMENT_TYPE)\r\n    }))\r\n}\r\n\r\nfunction parseCategories(categories, parser) {\r\n    const newArray = [];\r\n    categories.forEach((function(category) {\r\n        const parsedCategory = parser(category);\r\n        void 0 !== parsedCategory && newArray.push(parsedCategory)\r\n    }));\r\n    return newArray\r\n}\r\n\r\nfunction parseAxisCategories(groupsData, parsers) {\r\n    const argumentCategories = groupsData.argumentOptions && groupsData.argumentOptions.categories;\r\n    groupsData.groups.forEach((function(valueGroup, i) {\r\n        const categories = valueGroup.valueOptions && valueGroup.valueOptions.categories;\r\n        if (categories) {\r\n            valueGroup.valueOptions.categories = parseCategories(categories, parsers[i + 1])\r\n        }\r\n    }));\r\n    if (argumentCategories) {\r\n        groupsData.argumentOptions.categories = parseCategories(argumentCategories, parsers[0])\r\n    }\r\n}\r\n\r\nfunction eigen(x) {\r\n    return x\r\n}\r\n\r\nfunction getType(unit, type) {\r\n    let result = type;\r\n    if (type === STRING || _isString(unit)) {\r\n        result = STRING\r\n    } else if (type === DATETIME || _isDate(unit)) {\r\n        result = DATETIME\r\n    } else if (_isNumber(unit)) {\r\n        result = NUMERIC\r\n    }\r\n    return result\r\n}\r\n\r\nfunction correctAxisType(type, axisType, hasCategories, incidentOccurred) {\r\n    if (type === STRING && (axisType === CONTINUOUS || axisType === LOGARITHMIC || axisType === SEMIDISCRETE)) {\r\n        incidentOccurred(\"E2002\")\r\n    }\r\n    return axisType === LOGARITHMIC ? LOGARITHMIC : hasCategories || axisType === DISCRETE || type === STRING ? DISCRETE : axisType === SEMIDISCRETE ? SEMIDISCRETE : CONTINUOUS\r\n}\r\n\r\nfunction validUnit(unit, field, incidentOccurred) {\r\n    if (unit) {\r\n        incidentOccurred(!_isNumber(unit) && !_isDate(unit) && !_isString(unit) ? \"E2003\" : \"E2004\", [field])\r\n    }\r\n}\r\n\r\nfunction createParserUnit(type, axisType, incidentOccurred) {\r\n    const parser = type ? _getParser(type) : eigen;\r\n    const filterInfinity = axisType !== DISCRETE ? function(x) {\r\n        return isFinite(x) || void 0 === x ? x : null\r\n    } : eigen;\r\n    return function(unit, field) {\r\n        const parseUnit = filterInfinity(parser(unit));\r\n        if (void 0 === parseUnit) {\r\n            validUnit(unit, field, incidentOccurred)\r\n        }\r\n        return parseUnit\r\n    }\r\n}\r\n\r\nfunction prepareParsers(groupsData, incidentOccurred) {\r\n    const argumentParser = createParserUnit(groupsData.argumentType, groupsData.argumentAxisType, incidentOccurred);\r\n    let sizeParser;\r\n    let valueParser;\r\n    const categoryParsers = [argumentParser];\r\n    const cache = {};\r\n    const list = [];\r\n    groupsData.groups.forEach((function(group, groupIndex) {\r\n        group.series.forEach((function(series) {\r\n            valueParser = createParserUnit(group.valueType, group.valueAxisType, incidentOccurred);\r\n            sizeParser = createParserUnit(NUMERIC, CONTINUOUS, incidentOccurred);\r\n            cache[series.getArgumentField()] = argumentParser;\r\n            series.getValueFields().forEach((function(field) {\r\n                categoryParsers[groupIndex + 1] = valueParser;\r\n                cache[field] = valueParser\r\n            }));\r\n            if (series.getSizeField()) {\r\n                cache[series.getSizeField()] = sizeParser\r\n            }\r\n        }))\r\n    }));\r\n    for (const field in cache) {\r\n        list.push([field, cache[field]])\r\n    }\r\n    list.length && parseAxisCategories(groupsData, categoryParsers);\r\n    return list\r\n}\r\n\r\nfunction getParsedCell(cell, parsers) {\r\n    let i;\r\n    const ii = parsers.length;\r\n    const obj = extend({}, cell);\r\n    let field;\r\n    let value;\r\n    for (i = 0; i < ii; ++i) {\r\n        field = parsers[i][0];\r\n        value = cell[field];\r\n        obj[field] = parsers[i][1](value, field)\r\n    }\r\n    return obj\r\n}\r\n\r\nfunction parse(data, parsers) {\r\n    const parsedData = [];\r\n    let i;\r\n    const ii = data.length;\r\n    parsedData.length = ii;\r\n    for (i = 0; i < ii; ++i) {\r\n        parsedData[i] = getParsedCell(data[i], parsers)\r\n    }\r\n    return parsedData\r\n}\r\n\r\nfunction findIndexByThreshold(data, valueField, threshold) {\r\n    let i;\r\n    const ii = data.length;\r\n    let value;\r\n    for (i = 0; i < ii; ++i) {\r\n        value = data[i][valueField];\r\n        if (_isDefined(value) && threshold > value) {\r\n            break\r\n        }\r\n    }\r\n    return i\r\n}\r\n\r\nfunction groupMinSlices(originalData, argumentField, valueField, smallValuesGrouping) {\r\n    smallValuesGrouping = smallValuesGrouping || {};\r\n    const mode = smallValuesGrouping.mode;\r\n    const others = {};\r\n    if (!mode || \"none\" === mode) {\r\n        return\r\n    }\r\n    others[argumentField] = String(smallValuesGrouping.groupName || \"others\");\r\n    others[valueField] = 0;\r\n    const data = sortValues(originalData.slice(), false, (function(a) {\r\n        return a[valueField]\r\n    }));\r\n    groupingValues(data, others, valueField, \"smallValueThreshold\" === mode ? findIndexByThreshold(data, valueField, smallValuesGrouping.threshold) : smallValuesGrouping.topCount);\r\n    others[valueField] && originalData.push(others)\r\n}\r\n\r\nfunction groupPieData(data, groupsData) {\r\n    const firstSeries = groupsData.groups[0] && groupsData.groups[0].series[0];\r\n    const isPie = firstSeries && (\"pie\" === firstSeries.type || \"doughnut\" === firstSeries.type || \"donut\" === firstSeries.type);\r\n    if (!isPie) {\r\n        return\r\n    }\r\n    groupsData.groups.forEach((function(group) {\r\n        group.series.forEach((function(series) {\r\n            groupMinSlices(data, series.getArgumentField(), series.getValueFields()[0], series.getOptions().smallValuesGrouping)\r\n        }))\r\n    }))\r\n}\r\n\r\nfunction addUniqueItemToCollection(item, collection, itemsHash) {\r\n    if (!itemsHash[item]) {\r\n        collection.push(item);\r\n        itemsHash[item] = true\r\n    }\r\n}\r\n\r\nfunction getUniqueArgumentFields(groupsData) {\r\n    const uniqueArgumentFields = [];\r\n    const hash = {};\r\n    groupsData.groups.forEach((function(group) {\r\n        group.series.forEach((function(series) {\r\n            addUniqueItemToCollection(series.getArgumentField(), uniqueArgumentFields, hash)\r\n        }))\r\n    }));\r\n    return uniqueArgumentFields\r\n}\r\n\r\nfunction sort(a, b) {\r\n    const result = a - b;\r\n    if (isNaN(result)) {\r\n        if (!_isDefined(a)) {\r\n            return 1\r\n        }\r\n        if (!_isDefined(b)) {\r\n            return -1\r\n        }\r\n        return 0\r\n    }\r\n    return result\r\n}\r\n\r\nfunction sortByArgument(data, argumentField) {\r\n    return data.slice().sort((function(a, b) {\r\n        return sort(a[argumentField], b[argumentField])\r\n    }))\r\n}\r\n\r\nfunction sortByCallback(data, callback) {\r\n    return data.slice().sort(callback)\r\n}\r\n\r\nfunction checkValueTypeOfGroup(group, cell) {\r\n    group.series.forEach((function(series) {\r\n        series.getValueFields().forEach((function(field) {\r\n            group.valueType = getType(cell[field], group.valueType)\r\n        }))\r\n    }));\r\n    return group.valueType\r\n}\r\n\r\nfunction getSortByCategories(categories) {\r\n    const hash = {};\r\n    categories.forEach((function(value, i) {\r\n        hash[value] = i\r\n    }));\r\n    return function(data, argumentField) {\r\n        return sortValues(data.slice(), true, (function(a) {\r\n            return hash[a[argumentField]]\r\n        }))\r\n    }\r\n}\r\n\r\nfunction sortData(data, groupsData, options, uniqueArgumentFields) {\r\n    const dataByArguments = {};\r\n    const isDiscrete = groupsData.argumentAxisType === DISCRETE;\r\n    const userCategories = isDiscrete && groupsData.argumentOptions && groupsData.argumentOptions.categories;\r\n    let sortFunction = function(data) {\r\n        return data\r\n    };\r\n    const sortingMethodOption = options.sortingMethod;\r\n    let reSortCategories;\r\n    if (!userCategories && _isFunction(sortingMethodOption)) {\r\n        data = sortByCallback(data, sortingMethodOption)\r\n    }\r\n    if (isDiscrete) {\r\n        groupsData.categories = getCategories(data, uniqueArgumentFields, userCategories)\r\n    }\r\n    if (userCategories || !_isFunction(sortingMethodOption) && groupsData.argumentType === STRING && !options._skipArgumentSorting) {\r\n        sortFunction = getSortByCategories(groupsData.categories)\r\n    } else if (true === sortingMethodOption && groupsData.argumentType !== STRING) {\r\n        sortFunction = sortByArgument;\r\n        reSortCategories = isDiscrete\r\n    }\r\n    uniqueArgumentFields.forEach((function(field) {\r\n        dataByArguments[field] = sortFunction(data, field)\r\n    }));\r\n    if (reSortCategories) {\r\n        groupsData.categories = groupsData.categories.sort(sort)\r\n    }\r\n    return dataByArguments\r\n}\r\n\r\nfunction getCategories(data, uniqueArgumentFields, userCategories) {\r\n    const categories = userCategories ? userCategories.slice() : [];\r\n    const existingValues = new Set(categories.map((item => item.valueOf())));\r\n    uniqueArgumentFields.forEach((function(field) {\r\n        data.forEach((function(item) {\r\n            const dataItem = item[field];\r\n            if (!_isDefined(dataItem)) {\r\n                return\r\n            }\r\n            const dataItemValue = dataItem.valueOf();\r\n            if (!existingValues.has(dataItemValue)) {\r\n                categories.push(dataItem);\r\n                existingValues.add(dataItemValue)\r\n            }\r\n        }))\r\n    }));\r\n    return categories\r\n}\r\n\r\nfunction checkArgumentTypeOfGroup(series, cell, groupsData) {\r\n    series.forEach((function(currentSeries) {\r\n        groupsData.argumentType = getType(cell[currentSeries.getArgumentField()], groupsData.argumentType)\r\n    }));\r\n    return groupsData.argumentType\r\n}\r\n\r\nfunction checkType(data, groupsData, checkTypeForAllData) {\r\n    const groupsWithUndefinedValueType = [];\r\n    const groupsWithUndefinedArgumentType = [];\r\n    const argumentTypeGroup = groupsData.argumentOptions && axisTypeParser(groupsData.argumentOptions.argumentType);\r\n    let groupsIndexes;\r\n    groupsData.groups.forEach((function(group) {\r\n        if (!group.series.length) {\r\n            return\r\n        }\r\n        const valueTypeGroup = group.valueOptions && axisTypeParser(group.valueOptions.valueType);\r\n        group.valueType = valueTypeGroup;\r\n        groupsData.argumentType = argumentTypeGroup;\r\n        !valueTypeGroup && groupsWithUndefinedValueType.push(group);\r\n        !argumentTypeGroup && groupsWithUndefinedArgumentType.push(group)\r\n    }));\r\n    if (groupsWithUndefinedValueType.length || groupsWithUndefinedArgumentType.length) {\r\n        groupsIndexes = groupsWithUndefinedValueType.map((function(_, index) {\r\n            return index\r\n        }));\r\n        data.some((function(cell) {\r\n            let defineArg;\r\n            groupsWithUndefinedValueType.forEach((function(group, groupIndex) {\r\n                if (checkValueTypeOfGroup(group, cell) && groupsIndexes.indexOf(groupIndex) >= 0) {\r\n                    groupsIndexes.splice(groupIndex, 1)\r\n                }\r\n            }));\r\n            if (!defineArg) {\r\n                groupsWithUndefinedArgumentType.forEach((function(group) {\r\n                    defineArg = checkArgumentTypeOfGroup(group.series, cell, groupsData)\r\n                }))\r\n            }\r\n            if (!checkTypeForAllData && defineArg && 0 === groupsIndexes.length) {\r\n                return true\r\n            }\r\n        }))\r\n    }\r\n}\r\n\r\nfunction checkAxisType(groupsData, incidentOccurred) {\r\n    const argumentOptions = groupsData.argumentOptions || {};\r\n    const userArgumentCategories = argumentOptions && argumentOptions.categories || [];\r\n    const argumentAxisType = correctAxisType(groupsData.argumentType, argumentOptions.type, !!userArgumentCategories.length, incidentOccurred);\r\n    groupsData.groups.forEach((function(group) {\r\n        const valueOptions = group.valueOptions || {};\r\n        const valueCategories = valueOptions.categories || [];\r\n        const valueAxisType = correctAxisType(group.valueType, valueOptions.type, !!valueCategories.length, incidentOccurred);\r\n        group.series.forEach((function(series) {\r\n            const optionsSeries = {};\r\n            optionsSeries.argumentAxisType = argumentAxisType;\r\n            optionsSeries.valueAxisType = valueAxisType;\r\n            groupsData.argumentAxisType = groupsData.argumentAxisType || optionsSeries.argumentAxisType;\r\n            group.valueAxisType = group.valueAxisType || optionsSeries.valueAxisType;\r\n            optionsSeries.argumentType = groupsData.argumentType;\r\n            optionsSeries.valueType = group.valueType;\r\n            optionsSeries.showZero = valueOptions.showZero;\r\n            series.updateDataType(optionsSeries)\r\n        }));\r\n        group.valueAxisType = group.valueAxisType || valueAxisType;\r\n        if (group.valueAxis) {\r\n            group.valueAxis.setTypes(group.valueAxisType, group.valueType, VALUE_TYPE);\r\n            group.valueAxis.validate()\r\n        }\r\n    }));\r\n    groupsData.argumentAxisType = groupsData.argumentAxisType || argumentAxisType;\r\n    if (groupsData.argumentAxes) {\r\n        groupsData.argumentAxes.forEach((function(axis) {\r\n            axis.setTypes(groupsData.argumentAxisType, groupsData.argumentType, ARGUMENT_TYPE);\r\n            axis.validate()\r\n        }))\r\n    }\r\n}\r\n\r\nfunction verifyData(source, incidentOccurred) {\r\n    const data = [];\r\n    const sourceIsDefined = _isDefined(source);\r\n    let hasError = sourceIsDefined && !_isArray(source);\r\n    let i;\r\n    let ii;\r\n    let k;\r\n    let item;\r\n    if (sourceIsDefined && !hasError) {\r\n        for (i = 0, ii = source.length, k = 0; i < ii; ++i) {\r\n            item = source[i];\r\n            if (_isObject(item)) {\r\n                data[k++] = item\r\n            } else if (item) {\r\n                hasError = true\r\n            }\r\n        }\r\n    }\r\n    if (hasError) {\r\n        incidentOccurred(\"E2001\")\r\n    }\r\n    return data\r\n}\r\nexport function validateData(data, groupsData, incidentOccurred, options) {\r\n    data = verifyData(data, incidentOccurred);\r\n    groupsData.argumentType = groupsData.argumentAxisType = null;\r\n    processGroups(groupsData.groups);\r\n    resetArgumentAxes(groupsData.argumentAxes);\r\n    checkType(data, groupsData, options.checkTypeForAllData);\r\n    checkAxisType(groupsData, incidentOccurred);\r\n    if (options.convertToAxisDataType) {\r\n        data = parse(data, prepareParsers(groupsData, incidentOccurred))\r\n    }\r\n    groupPieData(data, groupsData);\r\n    const dataByArgumentFields = sortData(data, groupsData, options, getUniqueArgumentFields(groupsData));\r\n    return dataByArgumentFields\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAiBA;AAAA;AAGA;AAIA;;AAhBA,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,WAAW;AACjB,MAAM,WAAW;AACjB,MAAM,eAAe;AACrB,MAAM,aAAa;AACnB,MAAM,cAAc;AACpB,MAAM,aAAa;AACnB,MAAM,gBAAgB;;;AAOtB,MAAM,iBAAiB,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE;IAAC;IAAQ;IAAS;CAAS;;AAI7D,MAAM,WAAW,MAAM,OAAO;AAE9B,SAAS,eAAe,IAAI,EAAE,MAAM,EAAE,UAAU,EAAE,KAAK;IACnD,IAAI,SAAS,GAAG;QACZ,KAAK,KAAK,CAAC,OAAO,OAAO,CAAE,SAAS,IAAI;YACpC,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,IAAI,CAAC,WAAW,GAAG;gBAC9B,MAAM,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW;gBACtC,IAAI,CAAC,WAAW,GAAG,KAAK;YAC5B;QACJ;IACJ;AACJ;AAEA,SAAS,cAAc,MAAM;IACzB,OAAO,OAAO,CAAE,SAAS,KAAK;QAC1B,MAAM,SAAS,GAAG,MAAM,aAAa,GAAG;QACxC,MAAM,MAAM,CAAC,OAAO,CAAE,SAAS,MAAM;YACjC,OAAO,cAAc,CAAC,CAAC;QAC3B;QACA,MAAM,SAAS,IAAI,MAAM,SAAS,CAAC,UAAU,CAAC;IAClD;AACJ;AAEA,SAAS,WAAW,IAAI,EAAE,GAAG,EAAE,QAAQ;IACnC,MAAM,OAAO,MAAM,SAAS,CAAC,EAAE,CAAC;QAC5B,OAAO,IAAI;IACf,IAAI,SAAS,CAAC,EAAE,CAAC;QACb,OAAO,IAAI;IACf;IACA,KAAK,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;QACpB,MAAM,OAAO,SAAS;QACtB,MAAM,OAAO,SAAS;QACtB,MAAM,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,QAAQ,IAAI;QAClC,MAAM,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,QAAQ,IAAI;QAClC,OAAO,MAAM,KAAK,KAAK,MAAM,QAAQ,KAAK,IAAI;IAClD;IACA,OAAO;AACX;AAEA,SAAS,kBAAkB,IAAI;IAC3B,QAAQ,KAAK,OAAO,CAAE,SAAS,IAAI;QAC/B,KAAK,UAAU,CAAC;IACpB;AACJ;AAEA,SAAS,gBAAgB,UAAU,EAAE,MAAM;IACvC,MAAM,WAAW,EAAE;IACnB,WAAW,OAAO,CAAE,SAAS,QAAQ;QACjC,MAAM,iBAAiB,OAAO;QAC9B,KAAK,MAAM,kBAAkB,SAAS,IAAI,CAAC;IAC/C;IACA,OAAO;AACX;AAEA,SAAS,oBAAoB,UAAU,EAAE,OAAO;IAC5C,MAAM,qBAAqB,WAAW,eAAe,IAAI,WAAW,eAAe,CAAC,UAAU;IAC9F,WAAW,MAAM,CAAC,OAAO,CAAE,SAAS,UAAU,EAAE,CAAC;QAC7C,MAAM,aAAa,WAAW,YAAY,IAAI,WAAW,YAAY,CAAC,UAAU;QAChF,IAAI,YAAY;YACZ,WAAW,YAAY,CAAC,UAAU,GAAG,gBAAgB,YAAY,OAAO,CAAC,IAAI,EAAE;QACnF;IACJ;IACA,IAAI,oBAAoB;QACpB,WAAW,eAAe,CAAC,UAAU,GAAG,gBAAgB,oBAAoB,OAAO,CAAC,EAAE;IAC1F;AACJ;AAEA,SAAS,MAAM,CAAC;IACZ,OAAO;AACX;AAEA,SAAS,QAAQ,IAAI,EAAE,IAAI;IACvB,IAAI,SAAS;IACb,IAAI,SAAS,UAAU,CAAA,GAAA,gLAAA,CAAA,WAAS,AAAD,EAAE,OAAO;QACpC,SAAS;IACb,OAAO,IAAI,SAAS,YAAY,CAAA,GAAA,gLAAA,CAAA,SAAO,AAAD,EAAE,OAAO;QAC3C,SAAS;IACb,OAAO,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO;QACxB,SAAS;IACb;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,gBAAgB;IACpE,IAAI,SAAS,UAAU,CAAC,aAAa,cAAc,aAAa,eAAe,aAAa,YAAY,GAAG;QACvG,iBAAiB;IACrB;IACA,OAAO,aAAa,cAAc,cAAc,iBAAiB,aAAa,YAAY,SAAS,SAAS,WAAW,aAAa,eAAe,eAAe;AACtK;AAEA,SAAS,UAAU,IAAI,EAAE,KAAK,EAAE,gBAAgB;IAC5C,IAAI,MAAM;QACN,iBAAiB,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,gLAAA,CAAA,SAAO,AAAD,EAAE,SAAS,CAAC,CAAA,GAAA,gLAAA,CAAA,WAAS,AAAD,EAAE,QAAQ,UAAU,SAAS;YAAC;SAAM;IACxG;AACJ;AAEA,SAAS,iBAAiB,IAAI,EAAE,QAAQ,EAAE,gBAAgB;IACtD,MAAM,SAAS,OAAO,CAAA,GAAA,wKAAA,CAAA,YAAU,AAAD,EAAE,QAAQ;IACzC,MAAM,iBAAiB,aAAa,WAAW,SAAS,CAAC;QACrD,OAAO,SAAS,MAAM,KAAK,MAAM,IAAI,IAAI;IAC7C,IAAI;IACJ,OAAO,SAAS,IAAI,EAAE,KAAK;QACvB,MAAM,YAAY,eAAe,OAAO;QACxC,IAAI,KAAK,MAAM,WAAW;YACtB,UAAU,MAAM,OAAO;QAC3B;QACA,OAAO;IACX;AACJ;AAEA,SAAS,eAAe,UAAU,EAAE,gBAAgB;IAChD,MAAM,iBAAiB,iBAAiB,WAAW,YAAY,EAAE,WAAW,gBAAgB,EAAE;IAC9F,IAAI;IACJ,IAAI;IACJ,MAAM,kBAAkB;QAAC;KAAe;IACxC,MAAM,QAAQ,CAAC;IACf,MAAM,OAAO,EAAE;IACf,WAAW,MAAM,CAAC,OAAO,CAAE,SAAS,KAAK,EAAE,UAAU;QACjD,MAAM,MAAM,CAAC,OAAO,CAAE,SAAS,MAAM;YACjC,cAAc,iBAAiB,MAAM,SAAS,EAAE,MAAM,aAAa,EAAE;YACrE,aAAa,iBAAiB,SAAS,YAAY;YACnD,KAAK,CAAC,OAAO,gBAAgB,GAAG,GAAG;YACnC,OAAO,cAAc,GAAG,OAAO,CAAE,SAAS,KAAK;gBAC3C,eAAe,CAAC,aAAa,EAAE,GAAG;gBAClC,KAAK,CAAC,MAAM,GAAG;YACnB;YACA,IAAI,OAAO,YAAY,IAAI;gBACvB,KAAK,CAAC,OAAO,YAAY,GAAG,GAAG;YACnC;QACJ;IACJ;IACA,IAAK,MAAM,SAAS,MAAO;QACvB,KAAK,IAAI,CAAC;YAAC;YAAO,KAAK,CAAC,MAAM;SAAC;IACnC;IACA,KAAK,MAAM,IAAI,oBAAoB,YAAY;IAC/C,OAAO;AACX;AAEA,SAAS,cAAc,IAAI,EAAE,OAAO;IAChC,IAAI;IACJ,MAAM,KAAK,QAAQ,MAAM;IACzB,MAAM,MAAM,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;IACvB,IAAI;IACJ,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,QAAQ,OAAO,CAAC,EAAE,CAAC,EAAE;QACrB,QAAQ,IAAI,CAAC,MAAM;QACnB,GAAG,CAAC,MAAM,GAAG,OAAO,CAAC,EAAE,CAAC,EAAE,CAAC,OAAO;IACtC;IACA,OAAO;AACX;AAEA,SAAS,MAAM,IAAI,EAAE,OAAO;IACxB,MAAM,aAAa,EAAE;IACrB,IAAI;IACJ,MAAM,KAAK,KAAK,MAAM;IACtB,WAAW,MAAM,GAAG;IACpB,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,UAAU,CAAC,EAAE,GAAG,cAAc,IAAI,CAAC,EAAE,EAAE;IAC3C;IACA,OAAO;AACX;AAEA,SAAS,qBAAqB,IAAI,EAAE,UAAU,EAAE,SAAS;IACrD,IAAI;IACJ,MAAM,KAAK,KAAK,MAAM;IACtB,IAAI;IACJ,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,QAAQ,IAAI,CAAC,EAAE,CAAC,WAAW;QAC3B,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,UAAU,YAAY,OAAO;YACxC;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,eAAe,YAAY,EAAE,aAAa,EAAE,UAAU,EAAE,mBAAmB;IAChF,sBAAsB,uBAAuB,CAAC;IAC9C,MAAM,OAAO,oBAAoB,IAAI;IACrC,MAAM,SAAS,CAAC;IAChB,IAAI,CAAC,QAAQ,WAAW,MAAM;QAC1B;IACJ;IACA,MAAM,CAAC,cAAc,GAAG,OAAO,oBAAoB,SAAS,IAAI;IAChE,MAAM,CAAC,WAAW,GAAG;IACrB,MAAM,OAAO,WAAW,aAAa,KAAK,IAAI,OAAQ,SAAS,CAAC;QAC5D,OAAO,CAAC,CAAC,WAAW;IACxB;IACA,eAAe,MAAM,QAAQ,YAAY,0BAA0B,OAAO,qBAAqB,MAAM,YAAY,oBAAoB,SAAS,IAAI,oBAAoB,QAAQ;IAC9K,MAAM,CAAC,WAAW,IAAI,aAAa,IAAI,CAAC;AAC5C;AAEA,SAAS,aAAa,IAAI,EAAE,UAAU;IAClC,MAAM,cAAc,WAAW,MAAM,CAAC,EAAE,IAAI,WAAW,MAAM,CAAC,EAAE,CAAC,MAAM,CAAC,EAAE;IAC1E,MAAM,QAAQ,eAAe,CAAC,UAAU,YAAY,IAAI,IAAI,eAAe,YAAY,IAAI,IAAI,YAAY,YAAY,IAAI;IAC3H,IAAI,CAAC,OAAO;QACR;IACJ;IACA,WAAW,MAAM,CAAC,OAAO,CAAE,SAAS,KAAK;QACrC,MAAM,MAAM,CAAC,OAAO,CAAE,SAAS,MAAM;YACjC,eAAe,MAAM,OAAO,gBAAgB,IAAI,OAAO,cAAc,EAAE,CAAC,EAAE,EAAE,OAAO,UAAU,GAAG,mBAAmB;QACvH;IACJ;AACJ;AAEA,SAAS,0BAA0B,IAAI,EAAE,UAAU,EAAE,SAAS;IAC1D,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE;QAClB,WAAW,IAAI,CAAC;QAChB,SAAS,CAAC,KAAK,GAAG;IACtB;AACJ;AAEA,SAAS,wBAAwB,UAAU;IACvC,MAAM,uBAAuB,EAAE;IAC/B,MAAM,OAAO,CAAC;IACd,WAAW,MAAM,CAAC,OAAO,CAAE,SAAS,KAAK;QACrC,MAAM,MAAM,CAAC,OAAO,CAAE,SAAS,MAAM;YACjC,0BAA0B,OAAO,gBAAgB,IAAI,sBAAsB;QAC/E;IACJ;IACA,OAAO;AACX;AAEA,SAAS,KAAK,CAAC,EAAE,CAAC;IACd,MAAM,SAAS,IAAI;IACnB,IAAI,MAAM,SAAS;QACf,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,IAAI;YAChB,OAAO;QACX;QACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,IAAI;YAChB,OAAO,CAAC;QACZ;QACA,OAAO;IACX;IACA,OAAO;AACX;AAEA,SAAS,eAAe,IAAI,EAAE,aAAa;IACvC,OAAO,KAAK,KAAK,GAAG,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;QACnC,OAAO,KAAK,CAAC,CAAC,cAAc,EAAE,CAAC,CAAC,cAAc;IAClD;AACJ;AAEA,SAAS,eAAe,IAAI,EAAE,QAAQ;IAClC,OAAO,KAAK,KAAK,GAAG,IAAI,CAAC;AAC7B;AAEA,SAAS,sBAAsB,KAAK,EAAE,IAAI;IACtC,MAAM,MAAM,CAAC,OAAO,CAAE,SAAS,MAAM;QACjC,OAAO,cAAc,GAAG,OAAO,CAAE,SAAS,KAAK;YAC3C,MAAM,SAAS,GAAG,QAAQ,IAAI,CAAC,MAAM,EAAE,MAAM,SAAS;QAC1D;IACJ;IACA,OAAO,MAAM,SAAS;AAC1B;AAEA,SAAS,oBAAoB,UAAU;IACnC,MAAM,OAAO,CAAC;IACd,WAAW,OAAO,CAAE,SAAS,KAAK,EAAE,CAAC;QACjC,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,OAAO,SAAS,IAAI,EAAE,aAAa;QAC/B,OAAO,WAAW,KAAK,KAAK,IAAI,MAAO,SAAS,CAAC;YAC7C,OAAO,IAAI,CAAC,CAAC,CAAC,cAAc,CAAC;QACjC;IACJ;AACJ;AAEA,SAAS,SAAS,IAAI,EAAE,UAAU,EAAE,OAAO,EAAE,oBAAoB;IAC7D,MAAM,kBAAkB,CAAC;IACzB,MAAM,aAAa,WAAW,gBAAgB,KAAK;IACnD,MAAM,iBAAiB,cAAc,WAAW,eAAe,IAAI,WAAW,eAAe,CAAC,UAAU;IACxG,IAAI,eAAe,SAAS,IAAI;QAC5B,OAAO;IACX;IACA,MAAM,sBAAsB,QAAQ,aAAa;IACjD,IAAI;IACJ,IAAI,CAAC,kBAAkB,CAAA,GAAA,gLAAA,CAAA,aAAW,AAAD,EAAE,sBAAsB;QACrD,OAAO,eAAe,MAAM;IAChC;IACA,IAAI,YAAY;QACZ,WAAW,UAAU,GAAG,cAAc,MAAM,sBAAsB;IACtE;IACA,IAAI,kBAAkB,CAAC,CAAA,GAAA,gLAAA,CAAA,aAAW,AAAD,EAAE,wBAAwB,WAAW,YAAY,KAAK,UAAU,CAAC,QAAQ,oBAAoB,EAAE;QAC5H,eAAe,oBAAoB,WAAW,UAAU;IAC5D,OAAO,IAAI,SAAS,uBAAuB,WAAW,YAAY,KAAK,QAAQ;QAC3E,eAAe;QACf,mBAAmB;IACvB;IACA,qBAAqB,OAAO,CAAE,SAAS,KAAK;QACxC,eAAe,CAAC,MAAM,GAAG,aAAa,MAAM;IAChD;IACA,IAAI,kBAAkB;QAClB,WAAW,UAAU,GAAG,WAAW,UAAU,CAAC,IAAI,CAAC;IACvD;IACA,OAAO;AACX;AAEA,SAAS,cAAc,IAAI,EAAE,oBAAoB,EAAE,cAAc;IAC7D,MAAM,aAAa,iBAAiB,eAAe,KAAK,KAAK,EAAE;IAC/D,MAAM,iBAAiB,IAAI,IAAI,WAAW,GAAG,CAAE,CAAA,OAAQ,KAAK,OAAO;IACnE,qBAAqB,OAAO,CAAE,SAAS,KAAK;QACxC,KAAK,OAAO,CAAE,SAAS,IAAI;YACvB,MAAM,WAAW,IAAI,CAAC,MAAM;YAC5B,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE,WAAW;gBACvB;YACJ;YACA,MAAM,gBAAgB,SAAS,OAAO;YACtC,IAAI,CAAC,eAAe,GAAG,CAAC,gBAAgB;gBACpC,WAAW,IAAI,CAAC;gBAChB,eAAe,GAAG,CAAC;YACvB;QACJ;IACJ;IACA,OAAO;AACX;AAEA,SAAS,yBAAyB,MAAM,EAAE,IAAI,EAAE,UAAU;IACtD,OAAO,OAAO,CAAE,SAAS,aAAa;QAClC,WAAW,YAAY,GAAG,QAAQ,IAAI,CAAC,cAAc,gBAAgB,GAAG,EAAE,WAAW,YAAY;IACrG;IACA,OAAO,WAAW,YAAY;AAClC;AAEA,SAAS,UAAU,IAAI,EAAE,UAAU,EAAE,mBAAmB;IACpD,MAAM,+BAA+B,EAAE;IACvC,MAAM,kCAAkC,EAAE;IAC1C,MAAM,oBAAoB,WAAW,eAAe,IAAI,eAAe,WAAW,eAAe,CAAC,YAAY;IAC9G,IAAI;IACJ,WAAW,MAAM,CAAC,OAAO,CAAE,SAAS,KAAK;QACrC,IAAI,CAAC,MAAM,MAAM,CAAC,MAAM,EAAE;YACtB;QACJ;QACA,MAAM,iBAAiB,MAAM,YAAY,IAAI,eAAe,MAAM,YAAY,CAAC,SAAS;QACxF,MAAM,SAAS,GAAG;QAClB,WAAW,YAAY,GAAG;QAC1B,CAAC,kBAAkB,6BAA6B,IAAI,CAAC;QACrD,CAAC,qBAAqB,gCAAgC,IAAI,CAAC;IAC/D;IACA,IAAI,6BAA6B,MAAM,IAAI,gCAAgC,MAAM,EAAE;QAC/E,gBAAgB,6BAA6B,GAAG,CAAE,SAAS,CAAC,EAAE,KAAK;YAC/D,OAAO;QACX;QACA,KAAK,IAAI,CAAE,SAAS,IAAI;YACpB,IAAI;YACJ,6BAA6B,OAAO,CAAE,SAAS,KAAK,EAAE,UAAU;gBAC5D,IAAI,sBAAsB,OAAO,SAAS,cAAc,OAAO,CAAC,eAAe,GAAG;oBAC9E,cAAc,MAAM,CAAC,YAAY;gBACrC;YACJ;YACA,IAAI,CAAC,WAAW;gBACZ,gCAAgC,OAAO,CAAE,SAAS,KAAK;oBACnD,YAAY,yBAAyB,MAAM,MAAM,EAAE,MAAM;gBAC7D;YACJ;YACA,IAAI,CAAC,uBAAuB,aAAa,MAAM,cAAc,MAAM,EAAE;gBACjE,OAAO;YACX;QACJ;IACJ;AACJ;AAEA,SAAS,cAAc,UAAU,EAAE,gBAAgB;IAC/C,MAAM,kBAAkB,WAAW,eAAe,IAAI,CAAC;IACvD,MAAM,yBAAyB,mBAAmB,gBAAgB,UAAU,IAAI,EAAE;IAClF,MAAM,mBAAmB,gBAAgB,WAAW,YAAY,EAAE,gBAAgB,IAAI,EAAE,CAAC,CAAC,uBAAuB,MAAM,EAAE;IACzH,WAAW,MAAM,CAAC,OAAO,CAAE,SAAS,KAAK;QACrC,MAAM,eAAe,MAAM,YAAY,IAAI,CAAC;QAC5C,MAAM,kBAAkB,aAAa,UAAU,IAAI,EAAE;QACrD,MAAM,gBAAgB,gBAAgB,MAAM,SAAS,EAAE,aAAa,IAAI,EAAE,CAAC,CAAC,gBAAgB,MAAM,EAAE;QACpG,MAAM,MAAM,CAAC,OAAO,CAAE,SAAS,MAAM;YACjC,MAAM,gBAAgB,CAAC;YACvB,cAAc,gBAAgB,GAAG;YACjC,cAAc,aAAa,GAAG;YAC9B,WAAW,gBAAgB,GAAG,WAAW,gBAAgB,IAAI,cAAc,gBAAgB;YAC3F,MAAM,aAAa,GAAG,MAAM,aAAa,IAAI,cAAc,aAAa;YACxE,cAAc,YAAY,GAAG,WAAW,YAAY;YACpD,cAAc,SAAS,GAAG,MAAM,SAAS;YACzC,cAAc,QAAQ,GAAG,aAAa,QAAQ;YAC9C,OAAO,cAAc,CAAC;QAC1B;QACA,MAAM,aAAa,GAAG,MAAM,aAAa,IAAI;QAC7C,IAAI,MAAM,SAAS,EAAE;YACjB,MAAM,SAAS,CAAC,QAAQ,CAAC,MAAM,aAAa,EAAE,MAAM,SAAS,EAAE;YAC/D,MAAM,SAAS,CAAC,QAAQ;QAC5B;IACJ;IACA,WAAW,gBAAgB,GAAG,WAAW,gBAAgB,IAAI;IAC7D,IAAI,WAAW,YAAY,EAAE;QACzB,WAAW,YAAY,CAAC,OAAO,CAAE,SAAS,IAAI;YAC1C,KAAK,QAAQ,CAAC,WAAW,gBAAgB,EAAE,WAAW,YAAY,EAAE;YACpE,KAAK,QAAQ;QACjB;IACJ;AACJ;AAEA,SAAS,WAAW,MAAM,EAAE,gBAAgB;IACxC,MAAM,OAAO,EAAE;IACf,MAAM,kBAAkB,CAAA,GAAA,gLAAA,CAAA,YAAU,AAAD,EAAE;IACnC,IAAI,WAAW,mBAAmB,CAAC,SAAS;IAC5C,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,mBAAmB,CAAC,UAAU;QAC9B,IAAK,IAAI,GAAG,KAAK,OAAO,MAAM,EAAE,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;YAChD,OAAO,MAAM,CAAC,EAAE;YAChB,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAS,AAAD,EAAE,OAAO;gBACjB,IAAI,CAAC,IAAI,GAAG;YAChB,OAAO,IAAI,MAAM;gBACb,WAAW;YACf;QACJ;IACJ;IACA,IAAI,UAAU;QACV,iBAAiB;IACrB;IACA,OAAO;AACX;AACO,SAAS,aAAa,IAAI,EAAE,UAAU,EAAE,gBAAgB,EAAE,OAAO;IACpE,OAAO,WAAW,MAAM;IACxB,WAAW,YAAY,GAAG,WAAW,gBAAgB,GAAG;IACxD,cAAc,WAAW,MAAM;IAC/B,kBAAkB,WAAW,YAAY;IACzC,UAAU,MAAM,YAAY,QAAQ,mBAAmB;IACvD,cAAc,YAAY;IAC1B,IAAI,QAAQ,qBAAqB,EAAE;QAC/B,OAAO,MAAM,MAAM,eAAe,YAAY;IAClD;IACA,aAAa,MAAM;IACnB,MAAM,uBAAuB,SAAS,MAAM,YAAY,SAAS,wBAAwB;IACzF,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 779, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/components/legend.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/components/legend.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    enumParser,\r\n    normalizeEnum,\r\n    patchFontOptions\r\n} from \"../core/utils\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    LayoutElement,\r\n    WrapperLayoutElement\r\n} from \"../core/layout_element\";\r\nimport {\r\n    isDefined,\r\n    isFunction\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    Title\r\n} from \"../core/title\";\r\nimport {\r\n    clone\r\n} from \"../../core/utils/object\";\r\nimport {\r\n    noop\r\n} from \"../../core/utils/common\";\r\nimport {\r\n    processHatchingAttrs,\r\n    getFuncIri\r\n} from \"../core/renderers/renderer\";\r\nimport {\r\n    Deferred\r\n} from \"../../core/utils/deferred\";\r\nconst _Number = Number;\r\nconst _math = Math;\r\nconst _round = _math.round;\r\nconst _max = _math.max;\r\nconst _min = _math.min;\r\nconst _ceil = _math.ceil;\r\nconst _isDefined = isDefined;\r\nconst _isFunction = isFunction;\r\nconst _enumParser = enumParser;\r\nconst _normalizeEnum = normalizeEnum;\r\nconst _extend = extend;\r\nconst DEFAULT_MARGIN = 10;\r\nconst DEFAULT_MARKER_HATCHING_WIDTH = 2;\r\nconst DEFAULT_MARKER_HATCHING_STEP = 5;\r\nconst CENTER = \"center\";\r\nconst RIGHT = \"right\";\r\nconst LEFT = \"left\";\r\nconst TOP = \"top\";\r\nconst BOTTOM = \"bottom\";\r\nconst HORIZONTAL = \"horizontal\";\r\nconst VERTICAL = \"vertical\";\r\nconst INSIDE = \"inside\";\r\nconst OUTSIDE = \"outside\";\r\nconst NONE = \"none\";\r\nconst HEIGHT = \"height\";\r\nconst WIDTH = \"width\";\r\nconst parseHorizontalAlignment = _enumParser([LEFT, CENTER, RIGHT]);\r\nconst parseVerticalAlignment = _enumParser([TOP, BOTTOM]);\r\nconst parseOrientation = _enumParser([VERTICAL, HORIZONTAL]);\r\nconst parseItemTextPosition = _enumParser([LEFT, RIGHT, TOP, BOTTOM]);\r\nconst parsePosition = _enumParser([OUTSIDE, INSIDE]);\r\nconst parseItemsAlignment = _enumParser([LEFT, CENTER, RIGHT]);\r\n\r\nfunction getState(state, color, stateName) {\r\n    if (!state) {\r\n        return\r\n    }\r\n    const colorFromAction = state.fill;\r\n    return extend({}, {\r\n        state: stateName,\r\n        fill: colorFromAction === NONE ? color : colorFromAction,\r\n        opacity: state.opacity,\r\n        filter: state.filter,\r\n        hatching: _extend({}, state.hatching, {\r\n            step: 5,\r\n            width: 2\r\n        })\r\n    })\r\n}\r\n\r\nfunction getAttributes(item, state, size) {\r\n    const attrs = processHatchingAttrs(item, state);\r\n    if (attrs.fill && 0 === attrs.fill.indexOf(\"DevExpress\")) {\r\n        attrs.fill = getFuncIri(attrs.fill)\r\n    }\r\n    attrs.opacity = attrs.opacity >= 0 ? attrs.opacity : 1;\r\n    return extend({}, attrs, {\r\n        size: size\r\n    })\r\n}\r\n\r\nfunction parseMargins(options) {\r\n    let margin = options.margin;\r\n    if (margin >= 0) {\r\n        margin = _Number(options.margin);\r\n        margin = {\r\n            top: margin,\r\n            bottom: margin,\r\n            left: margin,\r\n            right: margin\r\n        }\r\n    } else {\r\n        margin = {\r\n            top: margin.top >= 0 ? _Number(margin.top) : 10,\r\n            bottom: margin.bottom >= 0 ? _Number(margin.bottom) : 10,\r\n            left: margin.left >= 0 ? _Number(margin.left) : 10,\r\n            right: margin.right >= 0 ? _Number(margin.right) : 10\r\n        }\r\n    }\r\n    options.margin = margin\r\n}\r\n\r\nfunction getSizeItem(options, markerBBox, labelBBox) {\r\n    let width;\r\n    let height;\r\n    switch (options.itemTextPosition) {\r\n        case LEFT:\r\n        case RIGHT:\r\n            width = markerBBox.width + 7 + labelBBox.width;\r\n            height = _max(markerBBox.height, labelBBox.height);\r\n            break;\r\n        case TOP:\r\n        case BOTTOM:\r\n            width = _max(markerBBox.width, labelBBox.width);\r\n            height = markerBBox.height + 4 + labelBBox.height\r\n    }\r\n    return {\r\n        width: width,\r\n        height: height\r\n    }\r\n}\r\n\r\nfunction calculateBBoxLabelAndMarker(markerBBox, labelBBox) {\r\n    const bBox = {};\r\n    bBox.left = _min(markerBBox.x, labelBBox.x);\r\n    bBox.top = _min(markerBBox.y, labelBBox.y);\r\n    bBox.right = _max(markerBBox.x + markerBBox.width, labelBBox.x + labelBBox.width);\r\n    bBox.bottom = _max(markerBBox.y + markerBBox.height, labelBBox.y + labelBBox.height);\r\n    return bBox\r\n}\r\n\r\nfunction applyMarkerState(id, idToIndexMap, items, stateName) {\r\n    const item = idToIndexMap && items[idToIndexMap[id]];\r\n    if (item) {\r\n        item.renderMarker(item.states[stateName])\r\n    }\r\n}\r\n\r\nfunction parseOptions(options, textField, allowInsidePosition) {\r\n    if (!options) {\r\n        return null\r\n    }\r\n    parseMargins(options);\r\n    options.horizontalAlignment = parseHorizontalAlignment(options.horizontalAlignment, RIGHT);\r\n    options.verticalAlignment = parseVerticalAlignment(options.verticalAlignment, options.horizontalAlignment === CENTER ? BOTTOM : TOP);\r\n    options.orientation = parseOrientation(options.orientation, options.horizontalAlignment === CENTER ? HORIZONTAL : VERTICAL);\r\n    options.itemTextPosition = parseItemTextPosition(options.itemTextPosition, options.orientation === HORIZONTAL ? BOTTOM : RIGHT);\r\n    options.position = allowInsidePosition ? parsePosition(options.position, OUTSIDE) : OUTSIDE;\r\n    options.itemsAlignment = parseItemsAlignment(options.itemsAlignment, null);\r\n    options.hoverMode = _normalizeEnum(options.hoverMode);\r\n    options.customizeText = _isFunction(options.customizeText) ? options.customizeText : function() {\r\n        return this[textField]\r\n    };\r\n    options.customizeHint = _isFunction(options.customizeHint) ? options.customizeHint : noop;\r\n    options._incidentOccurred = options._incidentOccurred || noop;\r\n    return options\r\n}\r\n\r\nfunction createSquareMarker(renderer, size) {\r\n    return renderer.rect(0, 0, size, size)\r\n}\r\n\r\nfunction createCircleMarker(renderer, size) {\r\n    return renderer.circle(size / 2, size / 2, size / 2)\r\n}\r\n\r\nfunction isCircle(type) {\r\n    return \"circle\" === _normalizeEnum(type)\r\n}\r\n\r\nfunction inRect(rect, x, y) {\r\n    return x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom\r\n}\r\n\r\nfunction checkLinesSize(lines, layoutOptions, countItems, margins) {\r\n    const position = {\r\n        x: 0,\r\n        y: 0\r\n    };\r\n    let maxMeasureLength = 0;\r\n    let maxAltMeasureLength = 0;\r\n    let margin = 0;\r\n    if (\"y\" === layoutOptions.direction) {\r\n        margin = margins.top + margins.bottom\r\n    } else {\r\n        margin = margins.left + margins.right\r\n    }\r\n    lines.forEach((function(line, i) {\r\n        const firstItem = line[0];\r\n        const lineLength = line.length;\r\n        line.forEach((function(item, index) {\r\n            const offset = item.offset || layoutOptions.spacing;\r\n            position[layoutOptions.direction] += item[layoutOptions.measure] + (index !== lineLength - 1 ? offset : 0);\r\n            maxMeasureLength = _max(maxMeasureLength, position[layoutOptions.direction])\r\n        }));\r\n        position[layoutOptions.direction] = 0;\r\n        position[layoutOptions.altDirection] += firstItem[layoutOptions.altMeasure] + firstItem.altOffset || layoutOptions.altSpacing;\r\n        maxAltMeasureLength = _max(maxAltMeasureLength, position[layoutOptions.altDirection])\r\n    }));\r\n    if (maxMeasureLength + margin > layoutOptions.length) {\r\n        layoutOptions.countItem = decreaseItemCount(layoutOptions, countItems);\r\n        return true\r\n    }\r\n}\r\n\r\nfunction decreaseItemCount(layoutOptions, countItems) {\r\n    layoutOptions.altCountItem++;\r\n    return _ceil(countItems / layoutOptions.altCountItem)\r\n}\r\n\r\nfunction getLineLength(line, layoutOptions) {\r\n    return line.reduce(((lineLength, item) => {\r\n        const offset = item.offset || layoutOptions.spacing;\r\n        return lineLength + item[layoutOptions.measure] + offset\r\n    }), 0)\r\n}\r\n\r\nfunction getMaxLineLength(lines, layoutOptions) {\r\n    return lines.reduce(((maxLineLength, line) => _max(maxLineLength, getLineLength(line, layoutOptions))), 0)\r\n}\r\n\r\nfunction getInitPositionForDirection(line, layoutOptions, maxLineLength) {\r\n    const lineLength = getLineLength(line, layoutOptions);\r\n    let initPosition;\r\n    switch (layoutOptions.itemsAlignment) {\r\n        case RIGHT:\r\n            initPosition = maxLineLength - lineLength;\r\n            break;\r\n        case CENTER:\r\n            initPosition = (maxLineLength - lineLength) / 2;\r\n            break;\r\n        default:\r\n            initPosition = 0\r\n    }\r\n    return initPosition\r\n}\r\n\r\nfunction getPos(layoutOptions) {\r\n    switch (layoutOptions.itemTextPosition) {\r\n        case BOTTOM:\r\n            return {\r\n                horizontal: CENTER, vertical: TOP\r\n            };\r\n        case TOP:\r\n            return {\r\n                horizontal: CENTER, vertical: BOTTOM\r\n            };\r\n        case LEFT:\r\n            return {\r\n                horizontal: RIGHT, vertical: CENTER\r\n            };\r\n        case RIGHT:\r\n            return {\r\n                horizontal: LEFT, vertical: CENTER\r\n            }\r\n    }\r\n}\r\n\r\nfunction getLines(lines, layoutOptions, itemIndex) {\r\n    const tableLine = {};\r\n    if (itemIndex % layoutOptions.countItem === 0) {\r\n        if (layoutOptions.markerOffset) {\r\n            lines.push([], [])\r\n        } else {\r\n            lines.push([])\r\n        }\r\n    }\r\n    if (layoutOptions.markerOffset) {\r\n        tableLine.firstLine = lines[lines.length - 1];\r\n        tableLine.secondLine = lines[lines.length - 2]\r\n    } else {\r\n        tableLine.firstLine = tableLine.secondLine = lines[lines.length - 1]\r\n    }\r\n    return tableLine\r\n}\r\n\r\nfunction setMaxInLine(line, measure) {\r\n    const maxLineSize = line.reduce(((maxLineSize, item) => {\r\n        const itemMeasure = item ? item[measure] : maxLineSize;\r\n        return _max(maxLineSize, itemMeasure)\r\n    }), 0);\r\n    line.forEach((item => {\r\n        if (item) {\r\n            item[measure] = maxLineSize\r\n        }\r\n    }))\r\n}\r\n\r\nfunction transpose(array) {\r\n    const width = array.length;\r\n    const height = array[0].length;\r\n    let i;\r\n    let j;\r\n    const transposeArray = [];\r\n    for (i = 0; i < height; i++) {\r\n        transposeArray[i] = [];\r\n        for (j = 0; j < width; j++) {\r\n            transposeArray[i][j] = array[j][i]\r\n        }\r\n    }\r\n    return transposeArray\r\n}\r\n\r\nfunction getAlign(position) {\r\n    switch (position) {\r\n        case TOP:\r\n        case BOTTOM:\r\n            return CENTER;\r\n        case LEFT:\r\n            return RIGHT;\r\n        case RIGHT:\r\n            return LEFT\r\n    }\r\n}\r\nlet getMarkerCreator = function(type) {\r\n    return isCircle(type) ? createCircleMarker : createSquareMarker\r\n};\r\n\r\nfunction getTitleHorizontalAlignment(options) {\r\n    if (options.horizontalAlignment === CENTER) {\r\n        return CENTER\r\n    } else if (options.itemTextPosition === RIGHT) {\r\n        return LEFT\r\n    } else if (options.itemTextPosition === LEFT) {\r\n        return RIGHT\r\n    } else {\r\n        return CENTER\r\n    }\r\n}\r\nexport let Legend = function(settings) {\r\n    this._renderer = settings.renderer;\r\n    this._legendGroup = settings.group;\r\n    this._backgroundClass = settings.backgroundClass;\r\n    this._itemGroupClass = settings.itemGroupClass;\r\n    this._textField = settings.textField;\r\n    this._getCustomizeObject = settings.getFormatObject;\r\n    this._titleGroupClass = settings.titleGroupClass;\r\n    this._allowInsidePosition = settings.allowInsidePosition;\r\n    this._widget = settings.widget;\r\n    this._updated = false\r\n};\r\nconst _Legend = Legend;\r\nconst legendPrototype = _Legend.prototype = clone(LayoutElement.prototype);\r\nextend(legendPrototype, {\r\n    constructor: _Legend,\r\n    getOptions: function() {\r\n        return this._options\r\n    },\r\n    update: function() {\r\n        let data = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : [];\r\n        let options = arguments.length > 1 ? arguments[1] : void 0;\r\n        let themeManagerTitleOptions = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {};\r\n        const that = this;\r\n        options = that._options = parseOptions(options, that._textField, that._allowInsidePosition) || {};\r\n        const initMarkerSize = options.markerSize;\r\n        this._updated = true;\r\n        this._data = data.map((dataItem => {\r\n            dataItem.size = _Number(dataItem.size > 0 ? dataItem.size : initMarkerSize);\r\n            dataItem.marker = getAttributes(dataItem, dataItem.states.normal);\r\n            Object.defineProperty(dataItem.marker, \"size\", {\r\n                get: () => dataItem.size,\r\n                set(value) {\r\n                    dataItem.size = value\r\n                }\r\n            });\r\n            Object.defineProperty(dataItem.marker, \"opacity\", {\r\n                get: () => dataItem.states.normal.opacity,\r\n                set(value) {\r\n                    dataItem.states.normal.opacity = dataItem.states.hover.opacity = dataItem.states.selection.opacity = value\r\n                }\r\n            });\r\n            return dataItem\r\n        }));\r\n        if (options.customizeItems) {\r\n            that._data = options.customizeItems(data.slice()) || data\r\n        }\r\n        that._boundingRect = {\r\n            width: 0,\r\n            height: 0,\r\n            x: 0,\r\n            y: 0\r\n        };\r\n        if (that.isVisible()) {\r\n            var _that$_title;\r\n            null === (_that$_title = that._title) || void 0 === _that$_title || _that$_title.dispose();\r\n            that._title = new Title({\r\n                renderer: that._renderer,\r\n                cssClass: that._titleGroupClass,\r\n                root: that._legendGroup\r\n            })\r\n        }\r\n        if (that._title) {\r\n            const titleOptions = options.title;\r\n            themeManagerTitleOptions.horizontalAlignment = getTitleHorizontalAlignment(options);\r\n            that._title.update(themeManagerTitleOptions, titleOptions)\r\n        }\r\n        this.erase();\r\n        return that\r\n    },\r\n    isVisible: function() {\r\n        return this._options && this._options.visible\r\n    },\r\n    draw: function(width, height) {\r\n        const that = this;\r\n        const items = that._getItemData();\r\n        that.erase();\r\n        if (!(that.isVisible() && items && items.length)) {\r\n            return that\r\n        }\r\n        that._insideLegendGroup = that._renderer.g().enableLinks().append(that._legendGroup);\r\n        that._title.changeLink(that._insideLegendGroup);\r\n        that._createBackground();\r\n        if (that._title.hasText()) {\r\n            const horizontalPadding = that._background ? 2 * that._options.paddingLeftRight : 0;\r\n            that._title.draw(width - horizontalPadding, height)\r\n        }\r\n        that._markersGroup = that._renderer.g().attr({\r\n            class: that._itemGroupClass\r\n        }).append(that._insideLegendGroup);\r\n        that._createItems(items);\r\n        that._updateElementsPosition(width, height);\r\n        return that\r\n    },\r\n    _measureElements: function() {\r\n        const options = this._options;\r\n        let maxBBoxHeight = 0;\r\n        this._items.forEach((item => {\r\n            const labelBBox = item.label.getBBox();\r\n            const markerBBox = item.marker.getBBox();\r\n            item.markerBBox = markerBBox;\r\n            item.markerSize = Math.max(markerBBox.width, markerBBox.height);\r\n            const bBox = getSizeItem(options, markerBBox, labelBBox);\r\n            item.labelBBox = labelBBox;\r\n            item.bBox = bBox;\r\n            maxBBoxHeight = _max(maxBBoxHeight, bBox.height)\r\n        }));\r\n        if (options.equalRowHeight) {\r\n            this._items.forEach((item => item.bBox.height = maxBBoxHeight))\r\n        }\r\n    },\r\n    _updateElementsPosition: function(width, height) {\r\n        const that = this;\r\n        const options = that._options;\r\n        this._size = {\r\n            width: width,\r\n            height: height\r\n        };\r\n        that._measureElements();\r\n        that._locateElements(options);\r\n        that._finalUpdate(options);\r\n        const size = that.getLayoutOptions();\r\n        if (size.width > width || size.height > height) {\r\n            that.freeSpace()\r\n        }\r\n    },\r\n    _createItems: function(items) {\r\n        const that = this;\r\n        const options = that._options;\r\n        const renderer = that._renderer;\r\n        const createMarker = getMarkerCreator(options.markerShape);\r\n        that._markersId = {};\r\n        const templateFunction = !options.markerTemplate ? (dataItem, group) => {\r\n            const attrs = dataItem.marker;\r\n            createMarker(renderer, attrs.size).attr({\r\n                fill: attrs.fill,\r\n                opacity: attrs.opacity,\r\n                filter: attrs.filter\r\n            }).append({\r\n                element: group\r\n            })\r\n        } : options.markerTemplate;\r\n        const template = that._widget._getTemplate(templateFunction);\r\n        const markersGroup = that._markersGroup;\r\n        markersGroup.css(patchFontOptions(options.font));\r\n        that._deferredItems = [];\r\n        that._templatesGroups = [];\r\n        that._items = (items || []).map(((dataItem, i) => {\r\n            const stateOfDataItem = dataItem.states;\r\n            const normalState = stateOfDataItem.normal;\r\n            const normalStateFill = normalState.fill;\r\n            dataItem.size = dataItem.marker.size;\r\n            const states = {\r\n                normal: extend(normalState, {\r\n                    fill: normalStateFill || options.markerColor || options.defaultColor,\r\n                    state: \"normal\"\r\n                }),\r\n                hover: getState(stateOfDataItem.hover, normalStateFill, \"hovered\"),\r\n                selection: getState(stateOfDataItem.selection, normalStateFill, \"selected\")\r\n            };\r\n            dataItem.states = states;\r\n            const itemGroup = renderer.g().append(markersGroup);\r\n            const markerGroup = renderer.g().attr({\r\n                class: \"dxl-marker\"\r\n            }).append(itemGroup);\r\n            that._deferredItems[i] = new Deferred;\r\n            that._templatesGroups.push(markerGroup);\r\n            const item = {\r\n                label: that._createLabel(dataItem, itemGroup),\r\n                marker: markerGroup,\r\n                renderer: renderer,\r\n                group: itemGroup,\r\n                tracker: {\r\n                    id: dataItem.id,\r\n                    argument: dataItem.argument,\r\n                    argumentIndex: dataItem.argumentIndex\r\n                },\r\n                states: states,\r\n                itemTextPosition: options.itemTextPosition,\r\n                markerOffset: 0,\r\n                bBoxes: [],\r\n                renderMarker(state) {\r\n                    dataItem.marker = getAttributes(item, state, dataItem.size);\r\n                    markerGroup.clear();\r\n                    template.render({\r\n                        model: dataItem,\r\n                        container: markerGroup.element,\r\n                        onRendered: that._deferredItems[i].resolve\r\n                    })\r\n                }\r\n            };\r\n            item.renderMarker(states.normal);\r\n            that._createHint(dataItem, itemGroup);\r\n            if (void 0 !== dataItem.id) {\r\n                that._markersId[dataItem.id] = i\r\n            }\r\n            return item\r\n        }))\r\n    },\r\n    getTemplatesGroups: function() {\r\n        return this._templatesGroups || []\r\n    },\r\n    getTemplatesDef: function() {\r\n        return this._deferredItems || []\r\n    },\r\n    _getItemData: function() {\r\n        let items = this._data || [];\r\n        const options = this._options || {};\r\n        if (options.inverted) {\r\n            items = items.slice().reverse()\r\n        }\r\n        return items.filter((i => i.visible))\r\n    },\r\n    _finalUpdate: function(options) {\r\n        this._adjustBackgroundSettings(options);\r\n        this._setBoundingRect(options.margin)\r\n    },\r\n    erase: function() {\r\n        const insideLegendGroup = this._insideLegendGroup;\r\n        insideLegendGroup && insideLegendGroup.dispose();\r\n        this._insideLegendGroup = this._markersGroup = this._x1 = this._x2 = this._y2 = this._y2 = null;\r\n        return this\r\n    },\r\n    _locateElements: function(locationOptions) {\r\n        this._moveInInitialValues();\r\n        this._locateRowsColumns(locationOptions)\r\n    },\r\n    _moveInInitialValues: function() {\r\n        this._title.hasText() && this._title.move([0, 0]);\r\n        this._legendGroup && this._legendGroup.move(0, 0);\r\n        this._background && this._background.attr({\r\n            x: 0,\r\n            y: 0,\r\n            width: 0,\r\n            height: 0\r\n        })\r\n    },\r\n    applySelected: function(id) {\r\n        applyMarkerState(id, this._markersId, this._items, \"selection\");\r\n        return this\r\n    },\r\n    applyHover: function(id) {\r\n        applyMarkerState(id, this._markersId, this._items, \"hover\");\r\n        return this\r\n    },\r\n    resetItem: function(id) {\r\n        applyMarkerState(id, this._markersId, this._items, \"normal\");\r\n        return this\r\n    },\r\n    _createLabel: function(data, group) {\r\n        const labelFormatObject = this._getCustomizeObject(data);\r\n        const options = this._options;\r\n        const align = getAlign(options.itemTextPosition);\r\n        const text = options.customizeText.call(labelFormatObject, labelFormatObject);\r\n        const fontStyle = _isDefined(data.textOpacity) ? {\r\n            color: options.font.color,\r\n            opacity: data.textOpacity\r\n        } : {};\r\n        return this._renderer.text(text, 0, 0).css(patchFontOptions(fontStyle)).attr({\r\n            align: align,\r\n            class: options.cssClass\r\n        }).append(group)\r\n    },\r\n    _createHint: function(data, group) {\r\n        const labelFormatObject = this._getCustomizeObject(data);\r\n        const text = this._options.customizeHint.call(labelFormatObject, labelFormatObject);\r\n        if (_isDefined(text) && \"\" !== text) {\r\n            group.setTitle(text)\r\n        }\r\n    },\r\n    _createBackground: function() {\r\n        const that = this;\r\n        const isInside = that._options.position === INSIDE;\r\n        const color = that._options.backgroundColor;\r\n        const fill = color || (isInside ? that._options.containerBackgroundColor : NONE);\r\n        if (that._options.border.visible || (isInside || color) && color !== NONE) {\r\n            that._background = that._renderer.rect(0, 0, 0, 0).attr({\r\n                fill: fill,\r\n                class: that._backgroundClass\r\n            }).append(that._insideLegendGroup)\r\n        }\r\n    },\r\n    _locateRowsColumns: function(options) {\r\n        const that = this;\r\n        let iteration = 0;\r\n        const layoutOptions = that._getItemsLayoutOptions();\r\n        const countItems = that._items.length;\r\n        let lines;\r\n        do {\r\n            lines = [];\r\n            that._createLines(lines, layoutOptions);\r\n            that._alignLines(lines, layoutOptions);\r\n            iteration++\r\n        } while (checkLinesSize(lines, layoutOptions, countItems, options.margin) && iteration < countItems);\r\n        that._applyItemPosition(lines, layoutOptions)\r\n    },\r\n    _createLines: function(lines, layoutOptions) {\r\n        this._items.forEach(((item, i) => {\r\n            const tableLine = getLines(lines, layoutOptions, i);\r\n            const labelBox = {\r\n                width: item.labelBBox.width,\r\n                height: item.labelBBox.height,\r\n                element: item.label,\r\n                bBox: item.labelBBox,\r\n                pos: getPos(layoutOptions),\r\n                itemIndex: i\r\n            };\r\n            const markerBox = {\r\n                width: item.markerBBox.width,\r\n                height: item.markerBBox.height,\r\n                element: item.marker,\r\n                pos: {\r\n                    horizontal: CENTER,\r\n                    vertical: CENTER\r\n                },\r\n                bBox: {\r\n                    width: item.markerBBox.width,\r\n                    height: item.markerBBox.height,\r\n                    x: item.markerBBox.x,\r\n                    y: item.markerBBox.y\r\n                },\r\n                itemIndex: i\r\n            };\r\n            let firstItem;\r\n            let secondItem;\r\n            const offsetDirection = layoutOptions.markerOffset ? \"altOffset\" : \"offset\";\r\n            if (layoutOptions.inverseLabelPosition) {\r\n                firstItem = labelBox;\r\n                secondItem = markerBox\r\n            } else {\r\n                firstItem = markerBox;\r\n                secondItem = labelBox\r\n            }\r\n            firstItem[offsetDirection] = layoutOptions.labelOffset;\r\n            tableLine.secondLine.push(firstItem);\r\n            tableLine.firstLine.push(secondItem)\r\n        }))\r\n    },\r\n    _alignLines: function(lines, layoutOptions) {\r\n        let i;\r\n        let measure = layoutOptions.altMeasure;\r\n        lines.forEach((line => setMaxInLine(line, measure)));\r\n        measure = layoutOptions.measure;\r\n        if (layoutOptions.itemsAlignment) {\r\n            if (layoutOptions.markerOffset) {\r\n                for (i = 0; i < lines.length;) {\r\n                    transpose([lines[i++], lines[i++]]).forEach(processLine)\r\n                }\r\n            }\r\n        } else {\r\n            transpose(lines).forEach(processLine)\r\n        }\r\n\r\n        function processLine(line) {\r\n            setMaxInLine(line, measure)\r\n        }\r\n    },\r\n    _applyItemPosition: function(lines, layoutOptions) {\r\n        const that = this;\r\n        const position = {\r\n            x: 0,\r\n            y: 0\r\n        };\r\n        const maxLineLength = getMaxLineLength(lines, layoutOptions);\r\n        lines.forEach((line => {\r\n            const firstItem = line[0];\r\n            const altOffset = firstItem.altOffset || layoutOptions.altSpacing;\r\n            position[layoutOptions.direction] = getInitPositionForDirection(line, layoutOptions, maxLineLength);\r\n            line.forEach((item => {\r\n                const offset = item.offset || layoutOptions.spacing;\r\n                const wrap = new WrapperLayoutElement(item.element, item.bBox);\r\n                const itemBBoxOptions = {\r\n                    x: position.x,\r\n                    y: position.y,\r\n                    width: item.width,\r\n                    height: item.height\r\n                };\r\n                const itemBBox = new WrapperLayoutElement(null, itemBBoxOptions);\r\n                const itemLegend = that._items[item.itemIndex];\r\n                wrap.position({\r\n                    of: itemBBox,\r\n                    my: item.pos,\r\n                    at: item.pos\r\n                });\r\n                itemLegend.bBoxes.push(itemBBox);\r\n                position[layoutOptions.direction] += item[layoutOptions.measure] + offset\r\n            }));\r\n            position[layoutOptions.altDirection] += firstItem[layoutOptions.altMeasure] + altOffset\r\n        }));\r\n        this._items.forEach((item => {\r\n            const itemBBox = calculateBBoxLabelAndMarker(item.bBoxes[0].getLayoutOptions(), item.bBoxes[1].getLayoutOptions());\r\n            const horizontal = that._options.columnItemSpacing / 2;\r\n            const vertical = that._options.rowItemSpacing / 2;\r\n            item.tracker.left = itemBBox.left - horizontal;\r\n            item.tracker.right = itemBBox.right + horizontal;\r\n            item.tracker.top = itemBBox.top - vertical;\r\n            item.tracker.bottom = itemBBox.bottom + vertical\r\n        }))\r\n    },\r\n    _getItemsLayoutOptions: function() {\r\n        const that = this;\r\n        const options = that._options;\r\n        const orientation = options.orientation;\r\n        const layoutOptions = {\r\n            itemsAlignment: options.itemsAlignment,\r\n            orientation: options.orientation\r\n        };\r\n        const width = that._size.width - (that._background ? 2 * options.paddingLeftRight : 0);\r\n        const height = that._size.height - (that._background ? 2 * options.paddingTopBottom : 0);\r\n        if (orientation === HORIZONTAL) {\r\n            layoutOptions.length = width;\r\n            layoutOptions.spacing = options.columnItemSpacing;\r\n            layoutOptions.direction = \"x\";\r\n            layoutOptions.measure = WIDTH;\r\n            layoutOptions.altMeasure = HEIGHT;\r\n            layoutOptions.altDirection = \"y\";\r\n            layoutOptions.altSpacing = options.rowItemSpacing;\r\n            layoutOptions.countItem = options.columnCount;\r\n            layoutOptions.altCountItem = options.rowCount;\r\n            layoutOptions.marginTextLabel = 4;\r\n            layoutOptions.labelOffset = 7;\r\n            if (options.itemTextPosition === BOTTOM || options.itemTextPosition === TOP) {\r\n                layoutOptions.labelOffset = 4;\r\n                layoutOptions.markerOffset = true\r\n            }\r\n        } else {\r\n            layoutOptions.length = height;\r\n            layoutOptions.spacing = options.rowItemSpacing;\r\n            layoutOptions.direction = \"y\";\r\n            layoutOptions.measure = HEIGHT;\r\n            layoutOptions.altMeasure = WIDTH;\r\n            layoutOptions.altDirection = \"x\";\r\n            layoutOptions.altSpacing = options.columnItemSpacing;\r\n            layoutOptions.countItem = options.rowCount;\r\n            layoutOptions.altCountItem = options.columnCount;\r\n            layoutOptions.marginTextLabel = 7;\r\n            layoutOptions.labelOffset = 4;\r\n            if (options.itemTextPosition === RIGHT || options.itemTextPosition === LEFT) {\r\n                layoutOptions.labelOffset = 7;\r\n                layoutOptions.markerOffset = true\r\n            }\r\n        }\r\n        if (!layoutOptions.countItem) {\r\n            if (layoutOptions.altCountItem) {\r\n                layoutOptions.countItem = _ceil(that._items.length / layoutOptions.altCountItem)\r\n            } else {\r\n                layoutOptions.countItem = that._items.length\r\n            }\r\n        }\r\n        if (options.itemTextPosition === TOP || options.itemTextPosition === LEFT) {\r\n            layoutOptions.inverseLabelPosition = true\r\n        }\r\n        layoutOptions.itemTextPosition = options.itemTextPosition;\r\n        layoutOptions.altCountItem = layoutOptions.altCountItem || _ceil(that._items.length / layoutOptions.countItem);\r\n        return layoutOptions\r\n    },\r\n    _adjustBackgroundSettings: function(locationOptions) {\r\n        if (!this._background) {\r\n            return\r\n        }\r\n        const border = locationOptions.border;\r\n        const legendBox = this._calculateTotalBox();\r\n        const backgroundSettings = {\r\n            x: _round(legendBox.x - locationOptions.paddingLeftRight),\r\n            y: _round(legendBox.y - locationOptions.paddingTopBottom),\r\n            width: _round(legendBox.width) + 2 * locationOptions.paddingLeftRight,\r\n            height: _round(legendBox.height),\r\n            opacity: locationOptions.backgroundOpacity\r\n        };\r\n        if (border.visible && border.width && border.color && border.color !== NONE) {\r\n            backgroundSettings[\"stroke-width\"] = border.width;\r\n            backgroundSettings.stroke = border.color;\r\n            backgroundSettings[\"stroke-opacity\"] = border.opacity;\r\n            backgroundSettings.dashStyle = border.dashStyle;\r\n            backgroundSettings.rx = border.cornerRadius || 0;\r\n            backgroundSettings.ry = border.cornerRadius || 0\r\n        }\r\n        this._background.attr(backgroundSettings)\r\n    },\r\n    _setBoundingRect: function(margin) {\r\n        if (!this._insideLegendGroup) {\r\n            return\r\n        }\r\n        const box = this._calculateTotalBox();\r\n        box.height += margin.top + margin.bottom;\r\n        box.widthWithoutMargins = box.width;\r\n        box.width += margin.left + margin.right;\r\n        box.x -= margin.left;\r\n        box.y -= margin.top;\r\n        this._boundingRect = box\r\n    },\r\n    _calculateTotalBox: function() {\r\n        const markerBox = this._markersGroup.getBBox();\r\n        const titleBox = this._title.getCorrectedLayoutOptions();\r\n        const box = this._insideLegendGroup.getBBox();\r\n        const verticalPadding = this._background ? 2 * this._options.paddingTopBottom : 0;\r\n        box.height = markerBox.height + titleBox.height + verticalPadding;\r\n        titleBox.width > box.width && (box.width = titleBox.width);\r\n        return box\r\n    },\r\n    getActionCallback: function(point) {\r\n        const that = this;\r\n        if (that._options.visible) {\r\n            return function(act) {\r\n                that[act](point.index)\r\n            }\r\n        } else {\r\n            return noop\r\n        }\r\n    },\r\n    getLayoutOptions: function() {\r\n        const options = this._options;\r\n        const boundingRect = this._insideLegendGroup ? this._boundingRect : {\r\n            width: 0,\r\n            height: 0,\r\n            x: 0,\r\n            y: 0\r\n        };\r\n        if (options) {\r\n            boundingRect.verticalAlignment = options.verticalAlignment;\r\n            boundingRect.horizontalAlignment = options.horizontalAlignment;\r\n            if (options.orientation === HORIZONTAL) {\r\n                boundingRect.cutLayoutSide = options.verticalAlignment;\r\n                boundingRect.cutSide = \"vertical\"\r\n            } else if (options.horizontalAlignment === CENTER) {\r\n                boundingRect.cutLayoutSide = options.verticalAlignment;\r\n                boundingRect.cutSide = \"vertical\"\r\n            } else {\r\n                boundingRect.cutLayoutSide = options.horizontalAlignment;\r\n                boundingRect.cutSide = \"horizontal\"\r\n            }\r\n            boundingRect.position = {\r\n                horizontal: options.horizontalAlignment,\r\n                vertical: options.verticalAlignment\r\n            };\r\n            return boundingRect\r\n        }\r\n        return null\r\n    },\r\n    shift: function(x, y) {\r\n        const that = this;\r\n        let box = {};\r\n        if (that._insideLegendGroup) {\r\n            that._insideLegendGroup.attr({\r\n                translateX: x - that._boundingRect.x,\r\n                translateY: y - that._boundingRect.y\r\n            })\r\n        }\r\n        that._title && that._shiftTitle(that._boundingRect.widthWithoutMargins);\r\n        that._markersGroup && that._shiftMarkers();\r\n        if (that._insideLegendGroup) {\r\n            box = that._legendGroup.getBBox()\r\n        }\r\n        that._x1 = box.x;\r\n        that._y1 = box.y;\r\n        that._x2 = box.x + box.width;\r\n        that._y2 = box.y + box.height;\r\n        return that\r\n    },\r\n    _shiftTitle: function(boxWidth) {\r\n        const that = this;\r\n        const title = that._title;\r\n        const titleBox = title.getCorrectedLayoutOptions();\r\n        if (!titleBox || !title.hasText()) {\r\n            return\r\n        }\r\n        const width = boxWidth - (that._background ? 2 * that._options.paddingLeftRight : 0);\r\n        const titleOptions = title.getOptions();\r\n        let titleY = titleBox.y + titleOptions.margin.top;\r\n        let titleX = 0;\r\n        if (titleOptions.verticalAlignment === BOTTOM && that._markersGroup) {\r\n            titleY += that._markersGroup.getBBox().height\r\n        }\r\n        if (titleOptions.horizontalAlignment === RIGHT) {\r\n            titleX = width - titleBox.width\r\n        } else if (titleOptions.horizontalAlignment === CENTER) {\r\n            titleX = (width - titleBox.width) / 2\r\n        }\r\n        title.shift(titleX, titleY)\r\n    },\r\n    _shiftMarkers: function() {\r\n        const titleBox = this._title.getLayoutOptions();\r\n        const markerBox = this._markersGroup.getBBox();\r\n        const titleOptions = this._title.getOptions() || {};\r\n        let center = 0;\r\n        let y = 0;\r\n        if (titleBox.width > markerBox.width && this._options.horizontalAlignment === CENTER) {\r\n            center = titleBox.width / 2 - markerBox.width / 2\r\n        }\r\n        if (titleOptions.verticalAlignment === TOP) {\r\n            y = titleBox.height\r\n        }\r\n        if (0 !== center || 0 !== y) {\r\n            this._markersGroup.attr({\r\n                translateX: center,\r\n                translateY: y\r\n            });\r\n            this._items.forEach((item => {\r\n                item.tracker.left += center;\r\n                item.tracker.right += center;\r\n                item.tracker.top += y;\r\n                item.tracker.bottom += y\r\n            }))\r\n        }\r\n    },\r\n    getPosition: function() {\r\n        return this._options.position\r\n    },\r\n    coordsIn: function(x, y) {\r\n        return x >= this._x1 && x <= this._x2 && y >= this._y1 && y <= this._y2\r\n    },\r\n    getItemByCoord: function(x, y) {\r\n        const items = this._items;\r\n        const legendGroup = this._insideLegendGroup;\r\n        x -= legendGroup.attr(\"translateX\");\r\n        y -= legendGroup.attr(\"translateY\");\r\n        for (let i = 0; i < items.length; i++) {\r\n            if (inRect(items[i].tracker, x, y)) {\r\n                return items[i].tracker\r\n            }\r\n        }\r\n        return null\r\n    },\r\n    dispose: function() {\r\n        this._title && this._title.dispose();\r\n        this._legendGroup = this._insideLegendGroup = this._title = this._renderer = this._options = this._data = this._items = null;\r\n        return this\r\n    },\r\n    layoutOptions: function() {\r\n        if (!this.isVisible()) {\r\n            return null\r\n        }\r\n        const pos = this.getLayoutOptions();\r\n        return {\r\n            horizontalAlignment: this._options.horizontalAlignment,\r\n            verticalAlignment: this._options.verticalAlignment,\r\n            side: pos.cutSide,\r\n            priority: 1,\r\n            position: this.getPosition()\r\n        }\r\n    },\r\n    measure: function(size) {\r\n        if (this._updated || !this._insideLegendGroup) {\r\n            this.draw(size[0], size[1]);\r\n            this._updated = false\r\n        } else {\r\n            this._items.forEach((item => {\r\n                item.bBoxes = []\r\n            }));\r\n            this._updateElementsPosition(size[0], size[1])\r\n        }\r\n        const rect = this.getLayoutOptions();\r\n        return [rect.width, rect.height]\r\n    },\r\n    move: function(rect) {\r\n        this.shift(rect[0], rect[1])\r\n    },\r\n    freeSpace: function() {\r\n        this._options._incidentOccurred(\"W2104\");\r\n        this.erase()\r\n    }\r\n});\r\nexport const plugin = {\r\n    name: \"legend\",\r\n    init: function() {\r\n        const group = this._renderer.g().attr({\r\n            class: this._rootClassPrefix + \"-legend\"\r\n        }).enableLinks().append(this._renderer.root);\r\n        this._legend = new Legend({\r\n            renderer: this._renderer,\r\n            group: group,\r\n            widget: this,\r\n            itemGroupClass: this._rootClassPrefix + \"-item\",\r\n            titleGroupClass: this._rootClassPrefix + \"-title\",\r\n            textField: \"text\",\r\n            getFormatObject: function(data) {\r\n                return {\r\n                    item: data.item,\r\n                    text: data.text\r\n                }\r\n            }\r\n        });\r\n        this._layout.add(this._legend)\r\n    },\r\n    extenders: {\r\n        _applyTilesAppearance: function() {\r\n            const that = this;\r\n            this._items.forEach((function(item) {\r\n                that._applyLegendItemStyle(item.id, item.getState())\r\n            }))\r\n        },\r\n        _buildNodes: function() {\r\n            this._createLegendItems()\r\n        }\r\n    },\r\n    members: {\r\n        _applyLegendItemStyle: function(id, state) {\r\n            const legend = this._legend;\r\n            switch (state) {\r\n                case \"hover\":\r\n                    legend.applyHover(id);\r\n                    break;\r\n                case \"selection\":\r\n                    legend.applySelected(id);\r\n                    break;\r\n                default:\r\n                    legend.resetItem(id)\r\n            }\r\n        },\r\n        _createLegendItems: function() {\r\n            if (this._legend.update(this._getLegendData(), this._getOption(\"legend\"), this._themeManager.theme(\"legend\").title)) {\r\n                this._requestChange([\"LAYOUT\"])\r\n            }\r\n        }\r\n    },\r\n    dispose: function() {\r\n        this._legend.dispose()\r\n    },\r\n    customize: function(constructor) {\r\n        constructor.prototype._proxyData.push((function(x, y) {\r\n            if (this._legend.coordsIn(x, y)) {\r\n                const item = this._legend.getItemByCoord(x, y);\r\n                if (item) {\r\n                    return {\r\n                        id: item.id,\r\n                        type: \"legend\"\r\n                    }\r\n                }\r\n            }\r\n        }));\r\n        constructor.addChange({\r\n            code: \"LEGEND\",\r\n            handler: function() {\r\n                this._createLegendItems()\r\n            },\r\n            isThemeDependent: true,\r\n            option: \"legend\",\r\n            isOptionChange: true\r\n        })\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;AACD;AAKA;AAAA;AAGA;AAIA;AAAA;AAIA;AAGA;AAAA;AAGA;AAAA;AAGA;AAIA;AAAA;;;;;;;;;;AAGA,MAAM,UAAU;AAChB,MAAM,QAAQ;AACd,MAAM,SAAS,MAAM,KAAK;AAC1B,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,OAAO,MAAM,GAAG;AACtB,MAAM,QAAQ,MAAM,IAAI;AACxB,MAAM,aAAa,gLAAA,CAAA,YAAS;AAC5B,MAAM,cAAc,gLAAA,CAAA,aAAU;AAC9B,MAAM,cAAc,4JAAA,CAAA,aAAU;AAC9B,MAAM,iBAAiB,4JAAA,CAAA,gBAAa;AACpC,MAAM,UAAU,kLAAA,CAAA,SAAM;AACtB,MAAM,iBAAiB;AACvB,MAAM,gCAAgC;AACtC,MAAM,+BAA+B;AACrC,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,SAAS;AACf,MAAM,aAAa;AACnB,MAAM,WAAW;AACjB,MAAM,SAAS;AACf,MAAM,UAAU;AAChB,MAAM,OAAO;AACb,MAAM,SAAS;AACf,MAAM,QAAQ;AACd,MAAM,2BAA2B,YAAY;IAAC;IAAM;IAAQ;CAAM;AAClE,MAAM,yBAAyB,YAAY;IAAC;IAAK;CAAO;AACxD,MAAM,mBAAmB,YAAY;IAAC;IAAU;CAAW;AAC3D,MAAM,wBAAwB,YAAY;IAAC;IAAM;IAAO;IAAK;CAAO;AACpE,MAAM,gBAAgB,YAAY;IAAC;IAAS;CAAO;AACnD,MAAM,sBAAsB,YAAY;IAAC;IAAM;IAAQ;CAAM;AAE7D,SAAS,SAAS,KAAK,EAAE,KAAK,EAAE,SAAS;IACrC,IAAI,CAAC,OAAO;QACR;IACJ;IACA,MAAM,kBAAkB,MAAM,IAAI;IAClC,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;QACd,OAAO;QACP,MAAM,oBAAoB,OAAO,QAAQ;QACzC,SAAS,MAAM,OAAO;QACtB,QAAQ,MAAM,MAAM;QACpB,UAAU,QAAQ,CAAC,GAAG,MAAM,QAAQ,EAAE;YAClC,MAAM;YACN,OAAO;QACX;IACJ;AACJ;AAEA,SAAS,cAAc,IAAI,EAAE,KAAK,EAAE,IAAI;IACpC,MAAM,QAAQ,CAAA,GAAA,4KAAA,CAAA,uBAAoB,AAAD,EAAE,MAAM;IACzC,IAAI,MAAM,IAAI,IAAI,MAAM,MAAM,IAAI,CAAC,OAAO,CAAC,eAAe;QACtD,MAAM,IAAI,GAAG,CAAA,GAAA,4KAAA,CAAA,aAAU,AAAD,EAAE,MAAM,IAAI;IACtC;IACA,MAAM,OAAO,GAAG,MAAM,OAAO,IAAI,IAAI,MAAM,OAAO,GAAG;IACrD,OAAO,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;QACrB,MAAM;IACV;AACJ;AAEA,SAAS,aAAa,OAAO;IACzB,IAAI,SAAS,QAAQ,MAAM;IAC3B,IAAI,UAAU,GAAG;QACb,SAAS,QAAQ,QAAQ,MAAM;QAC/B,SAAS;YACL,KAAK;YACL,QAAQ;YACR,MAAM;YACN,OAAO;QACX;IACJ,OAAO;QACH,SAAS;YACL,KAAK,OAAO,GAAG,IAAI,IAAI,QAAQ,OAAO,GAAG,IAAI;YAC7C,QAAQ,OAAO,MAAM,IAAI,IAAI,QAAQ,OAAO,MAAM,IAAI;YACtD,MAAM,OAAO,IAAI,IAAI,IAAI,QAAQ,OAAO,IAAI,IAAI;YAChD,OAAO,OAAO,KAAK,IAAI,IAAI,QAAQ,OAAO,KAAK,IAAI;QACvD;IACJ;IACA,QAAQ,MAAM,GAAG;AACrB;AAEA,SAAS,YAAY,OAAO,EAAE,UAAU,EAAE,SAAS;IAC/C,IAAI;IACJ,IAAI;IACJ,OAAQ,QAAQ,gBAAgB;QAC5B,KAAK;QACL,KAAK;YACD,QAAQ,WAAW,KAAK,GAAG,IAAI,UAAU,KAAK;YAC9C,SAAS,KAAK,WAAW,MAAM,EAAE,UAAU,MAAM;YACjD;QACJ,KAAK;QACL,KAAK;YACD,QAAQ,KAAK,WAAW,KAAK,EAAE,UAAU,KAAK;YAC9C,SAAS,WAAW,MAAM,GAAG,IAAI,UAAU,MAAM;IACzD;IACA,OAAO;QACH,OAAO;QACP,QAAQ;IACZ;AACJ;AAEA,SAAS,4BAA4B,UAAU,EAAE,SAAS;IACtD,MAAM,OAAO,CAAC;IACd,KAAK,IAAI,GAAG,KAAK,WAAW,CAAC,EAAE,UAAU,CAAC;IAC1C,KAAK,GAAG,GAAG,KAAK,WAAW,CAAC,EAAE,UAAU,CAAC;IACzC,KAAK,KAAK,GAAG,KAAK,WAAW,CAAC,GAAG,WAAW,KAAK,EAAE,UAAU,CAAC,GAAG,UAAU,KAAK;IAChF,KAAK,MAAM,GAAG,KAAK,WAAW,CAAC,GAAG,WAAW,MAAM,EAAE,UAAU,CAAC,GAAG,UAAU,MAAM;IACnF,OAAO;AACX;AAEA,SAAS,iBAAiB,EAAE,EAAE,YAAY,EAAE,KAAK,EAAE,SAAS;IACxD,MAAM,OAAO,gBAAgB,KAAK,CAAC,YAAY,CAAC,GAAG,CAAC;IACpD,IAAI,MAAM;QACN,KAAK,YAAY,CAAC,KAAK,MAAM,CAAC,UAAU;IAC5C;AACJ;AAEA,SAAS,aAAa,OAAO,EAAE,SAAS,EAAE,mBAAmB;IACzD,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,aAAa;IACb,QAAQ,mBAAmB,GAAG,yBAAyB,QAAQ,mBAAmB,EAAE;IACpF,QAAQ,iBAAiB,GAAG,uBAAuB,QAAQ,iBAAiB,EAAE,QAAQ,mBAAmB,KAAK,SAAS,SAAS;IAChI,QAAQ,WAAW,GAAG,iBAAiB,QAAQ,WAAW,EAAE,QAAQ,mBAAmB,KAAK,SAAS,aAAa;IAClH,QAAQ,gBAAgB,GAAG,sBAAsB,QAAQ,gBAAgB,EAAE,QAAQ,WAAW,KAAK,aAAa,SAAS;IACzH,QAAQ,QAAQ,GAAG,sBAAsB,cAAc,QAAQ,QAAQ,EAAE,WAAW;IACpF,QAAQ,cAAc,GAAG,oBAAoB,QAAQ,cAAc,EAAE;IACrE,QAAQ,SAAS,GAAG,eAAe,QAAQ,SAAS;IACpD,QAAQ,aAAa,GAAG,YAAY,QAAQ,aAAa,IAAI,QAAQ,aAAa,GAAG;QACjF,OAAO,IAAI,CAAC,UAAU;IAC1B;IACA,QAAQ,aAAa,GAAG,YAAY,QAAQ,aAAa,IAAI,QAAQ,aAAa,GAAG,kLAAA,CAAA,OAAI;IACzF,QAAQ,iBAAiB,GAAG,QAAQ,iBAAiB,IAAI,kLAAA,CAAA,OAAI;IAC7D,OAAO;AACX;AAEA,SAAS,mBAAmB,QAAQ,EAAE,IAAI;IACtC,OAAO,SAAS,IAAI,CAAC,GAAG,GAAG,MAAM;AACrC;AAEA,SAAS,mBAAmB,QAAQ,EAAE,IAAI;IACtC,OAAO,SAAS,MAAM,CAAC,OAAO,GAAG,OAAO,GAAG,OAAO;AACtD;AAEA,SAAS,SAAS,IAAI;IAClB,OAAO,aAAa,eAAe;AACvC;AAEA,SAAS,OAAO,IAAI,EAAE,CAAC,EAAE,CAAC;IACtB,OAAO,KAAK,KAAK,IAAI,IAAI,KAAK,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,IAAI,KAAK,KAAK,MAAM;AACjF;AAEA,SAAS,eAAe,KAAK,EAAE,aAAa,EAAE,UAAU,EAAE,OAAO;IAC7D,MAAM,WAAW;QACb,GAAG;QACH,GAAG;IACP;IACA,IAAI,mBAAmB;IACvB,IAAI,sBAAsB;IAC1B,IAAI,SAAS;IACb,IAAI,QAAQ,cAAc,SAAS,EAAE;QACjC,SAAS,QAAQ,GAAG,GAAG,QAAQ,MAAM;IACzC,OAAO;QACH,SAAS,QAAQ,IAAI,GAAG,QAAQ,KAAK;IACzC;IACA,MAAM,OAAO,CAAE,SAAS,IAAI,EAAE,CAAC;QAC3B,MAAM,YAAY,IAAI,CAAC,EAAE;QACzB,MAAM,aAAa,KAAK,MAAM;QAC9B,KAAK,OAAO,CAAE,SAAS,IAAI,EAAE,KAAK;YAC9B,MAAM,SAAS,KAAK,MAAM,IAAI,cAAc,OAAO;YACnD,QAAQ,CAAC,cAAc,SAAS,CAAC,IAAI,IAAI,CAAC,cAAc,OAAO,CAAC,GAAG,CAAC,UAAU,aAAa,IAAI,SAAS,CAAC;YACzG,mBAAmB,KAAK,kBAAkB,QAAQ,CAAC,cAAc,SAAS,CAAC;QAC/E;QACA,QAAQ,CAAC,cAAc,SAAS,CAAC,GAAG;QACpC,QAAQ,CAAC,cAAc,YAAY,CAAC,IAAI,SAAS,CAAC,cAAc,UAAU,CAAC,GAAG,UAAU,SAAS,IAAI,cAAc,UAAU;QAC7H,sBAAsB,KAAK,qBAAqB,QAAQ,CAAC,cAAc,YAAY,CAAC;IACxF;IACA,IAAI,mBAAmB,SAAS,cAAc,MAAM,EAAE;QAClD,cAAc,SAAS,GAAG,kBAAkB,eAAe;QAC3D,OAAO;IACX;AACJ;AAEA,SAAS,kBAAkB,aAAa,EAAE,UAAU;IAChD,cAAc,YAAY;IAC1B,OAAO,MAAM,aAAa,cAAc,YAAY;AACxD;AAEA,SAAS,cAAc,IAAI,EAAE,aAAa;IACtC,OAAO,KAAK,MAAM,CAAE,CAAC,YAAY;QAC7B,MAAM,SAAS,KAAK,MAAM,IAAI,cAAc,OAAO;QACnD,OAAO,aAAa,IAAI,CAAC,cAAc,OAAO,CAAC,GAAG;IACtD,GAAI;AACR;AAEA,SAAS,iBAAiB,KAAK,EAAE,aAAa;IAC1C,OAAO,MAAM,MAAM,CAAE,CAAC,eAAe,OAAS,KAAK,eAAe,cAAc,MAAM,iBAAkB;AAC5G;AAEA,SAAS,4BAA4B,IAAI,EAAE,aAAa,EAAE,aAAa;IACnE,MAAM,aAAa,cAAc,MAAM;IACvC,IAAI;IACJ,OAAQ,cAAc,cAAc;QAChC,KAAK;YACD,eAAe,gBAAgB;YAC/B;QACJ,KAAK;YACD,eAAe,CAAC,gBAAgB,UAAU,IAAI;YAC9C;QACJ;YACI,eAAe;IACvB;IACA,OAAO;AACX;AAEA,SAAS,OAAO,aAAa;IACzB,OAAQ,cAAc,gBAAgB;QAClC,KAAK;YACD,OAAO;gBACH,YAAY;gBAAQ,UAAU;YAClC;QACJ,KAAK;YACD,OAAO;gBACH,YAAY;gBAAQ,UAAU;YAClC;QACJ,KAAK;YACD,OAAO;gBACH,YAAY;gBAAO,UAAU;YACjC;QACJ,KAAK;YACD,OAAO;gBACH,YAAY;gBAAM,UAAU;YAChC;IACR;AACJ;AAEA,SAAS,SAAS,KAAK,EAAE,aAAa,EAAE,SAAS;IAC7C,MAAM,YAAY,CAAC;IACnB,IAAI,YAAY,cAAc,SAAS,KAAK,GAAG;QAC3C,IAAI,cAAc,YAAY,EAAE;YAC5B,MAAM,IAAI,CAAC,EAAE,EAAE,EAAE;QACrB,OAAO;YACH,MAAM,IAAI,CAAC,EAAE;QACjB;IACJ;IACA,IAAI,cAAc,YAAY,EAAE;QAC5B,UAAU,SAAS,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC7C,UAAU,UAAU,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IAClD,OAAO;QACH,UAAU,SAAS,GAAG,UAAU,UAAU,GAAG,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;IACxE;IACA,OAAO;AACX;AAEA,SAAS,aAAa,IAAI,EAAE,OAAO;IAC/B,MAAM,cAAc,KAAK,MAAM,CAAE,CAAC,aAAa;QAC3C,MAAM,cAAc,OAAO,IAAI,CAAC,QAAQ,GAAG;QAC3C,OAAO,KAAK,aAAa;IAC7B,GAAI;IACJ,KAAK,OAAO,CAAE,CAAA;QACV,IAAI,MAAM;YACN,IAAI,CAAC,QAAQ,GAAG;QACpB;IACJ;AACJ;AAEA,SAAS,UAAU,KAAK;IACpB,MAAM,QAAQ,MAAM,MAAM;IAC1B,MAAM,SAAS,KAAK,CAAC,EAAE,CAAC,MAAM;IAC9B,IAAI;IACJ,IAAI;IACJ,MAAM,iBAAiB,EAAE;IACzB,IAAK,IAAI,GAAG,IAAI,QAAQ,IAAK;QACzB,cAAc,CAAC,EAAE,GAAG,EAAE;QACtB,IAAK,IAAI,GAAG,IAAI,OAAO,IAAK;YACxB,cAAc,CAAC,EAAE,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,CAAC,EAAE;QACtC;IACJ;IACA,OAAO;AACX;AAEA,SAAS,SAAS,QAAQ;IACtB,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;QACX,KAAK;YACD,OAAO;IACf;AACJ;AACA,IAAI,mBAAmB,SAAS,IAAI;IAChC,OAAO,SAAS,QAAQ,qBAAqB;AACjD;AAEA,SAAS,4BAA4B,OAAO;IACxC,IAAI,QAAQ,mBAAmB,KAAK,QAAQ;QACxC,OAAO;IACX,OAAO,IAAI,QAAQ,gBAAgB,KAAK,OAAO;QAC3C,OAAO;IACX,OAAO,IAAI,QAAQ,gBAAgB,KAAK,MAAM;QAC1C,OAAO;IACX,OAAO;QACH,OAAO;IACX;AACJ;AACO,IAAI,SAAS,SAAS,QAAQ;IACjC,IAAI,CAAC,SAAS,GAAG,SAAS,QAAQ;IAClC,IAAI,CAAC,YAAY,GAAG,SAAS,KAAK;IAClC,IAAI,CAAC,gBAAgB,GAAG,SAAS,eAAe;IAChD,IAAI,CAAC,eAAe,GAAG,SAAS,cAAc;IAC9C,IAAI,CAAC,UAAU,GAAG,SAAS,SAAS;IACpC,IAAI,CAAC,mBAAmB,GAAG,SAAS,eAAe;IACnD,IAAI,CAAC,gBAAgB,GAAG,SAAS,eAAe;IAChD,IAAI,CAAC,oBAAoB,GAAG,SAAS,mBAAmB;IACxD,IAAI,CAAC,OAAO,GAAG,SAAS,MAAM;IAC9B,IAAI,CAAC,QAAQ,GAAG;AACpB;AACA,MAAM,UAAU;AAChB,MAAM,kBAAkB,QAAQ,SAAS,GAAG,CAAA,GAAA,kLAAA,CAAA,QAAK,AAAD,EAAE,qKAAA,CAAA,gBAAa,CAAC,SAAS;AACzE,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,iBAAiB;IACpB,aAAa;IACb,YAAY;QACR,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,QAAQ;QACJ,IAAI,OAAO,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,EAAE;QAC9E,IAAI,UAAU,UAAU,MAAM,GAAG,IAAI,SAAS,CAAC,EAAE,GAAG,KAAK;QACzD,IAAI,2BAA2B,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG,CAAC;QACjG,MAAM,OAAO,IAAI;QACjB,UAAU,KAAK,QAAQ,GAAG,aAAa,SAAS,KAAK,UAAU,EAAE,KAAK,oBAAoB,KAAK,CAAC;QAChG,MAAM,iBAAiB,QAAQ,UAAU;QACzC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,KAAK,GAAG,KAAK,GAAG,CAAE,CAAA;YACnB,SAAS,IAAI,GAAG,QAAQ,SAAS,IAAI,GAAG,IAAI,SAAS,IAAI,GAAG;YAC5D,SAAS,MAAM,GAAG,cAAc,UAAU,SAAS,MAAM,CAAC,MAAM;YAChE,OAAO,cAAc,CAAC,SAAS,MAAM,EAAE,QAAQ;gBAC3C,KAAK,IAAM,SAAS,IAAI;gBACxB,KAAI,KAAK;oBACL,SAAS,IAAI,GAAG;gBACpB;YACJ;YACA,OAAO,cAAc,CAAC,SAAS,MAAM,EAAE,WAAW;gBAC9C,KAAK,IAAM,SAAS,MAAM,CAAC,MAAM,CAAC,OAAO;gBACzC,KAAI,KAAK;oBACL,SAAS,MAAM,CAAC,MAAM,CAAC,OAAO,GAAG,SAAS,MAAM,CAAC,KAAK,CAAC,OAAO,GAAG,SAAS,MAAM,CAAC,SAAS,CAAC,OAAO,GAAG;gBACzG;YACJ;YACA,OAAO;QACX;QACA,IAAI,QAAQ,cAAc,EAAE;YACxB,KAAK,KAAK,GAAG,QAAQ,cAAc,CAAC,KAAK,KAAK,OAAO;QACzD;QACA,KAAK,aAAa,GAAG;YACjB,OAAO;YACP,QAAQ;YACR,GAAG;YACH,GAAG;QACP;QACA,IAAI,KAAK,SAAS,IAAI;YAClB,IAAI;YACJ,SAAS,CAAC,eAAe,KAAK,MAAM,KAAK,KAAK,MAAM,gBAAgB,aAAa,OAAO;YACxF,KAAK,MAAM,GAAG,IAAI,4JAAA,CAAA,QAAK,CAAC;gBACpB,UAAU,KAAK,SAAS;gBACxB,UAAU,KAAK,gBAAgB;gBAC/B,MAAM,KAAK,YAAY;YAC3B;QACJ;QACA,IAAI,KAAK,MAAM,EAAE;YACb,MAAM,eAAe,QAAQ,KAAK;YAClC,yBAAyB,mBAAmB,GAAG,4BAA4B;YAC3E,KAAK,MAAM,CAAC,MAAM,CAAC,0BAA0B;QACjD;QACA,IAAI,CAAC,KAAK;QACV,OAAO;IACX;IACA,WAAW;QACP,OAAO,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO;IACjD;IACA,MAAM,SAAS,KAAK,EAAE,MAAM;QACxB,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,KAAK,YAAY;QAC/B,KAAK,KAAK;QACV,IAAI,CAAC,CAAC,KAAK,SAAS,MAAM,SAAS,MAAM,MAAM,GAAG;YAC9C,OAAO;QACX;QACA,KAAK,kBAAkB,GAAG,KAAK,SAAS,CAAC,CAAC,GAAG,WAAW,GAAG,MAAM,CAAC,KAAK,YAAY;QACnF,KAAK,MAAM,CAAC,UAAU,CAAC,KAAK,kBAAkB;QAC9C,KAAK,iBAAiB;QACtB,IAAI,KAAK,MAAM,CAAC,OAAO,IAAI;YACvB,MAAM,oBAAoB,KAAK,WAAW,GAAG,IAAI,KAAK,QAAQ,CAAC,gBAAgB,GAAG;YAClF,KAAK,MAAM,CAAC,IAAI,CAAC,QAAQ,mBAAmB;QAChD;QACA,KAAK,aAAa,GAAG,KAAK,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;YACzC,OAAO,KAAK,eAAe;QAC/B,GAAG,MAAM,CAAC,KAAK,kBAAkB;QACjC,KAAK,YAAY,CAAC;QAClB,KAAK,uBAAuB,CAAC,OAAO;QACpC,OAAO;IACX;IACA,kBAAkB;QACd,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,IAAI,gBAAgB;QACpB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,MAAM,YAAY,KAAK,KAAK,CAAC,OAAO;YACpC,MAAM,aAAa,KAAK,MAAM,CAAC,OAAO;YACtC,KAAK,UAAU,GAAG;YAClB,KAAK,UAAU,GAAG,KAAK,GAAG,CAAC,WAAW,KAAK,EAAE,WAAW,MAAM;YAC9D,MAAM,OAAO,YAAY,SAAS,YAAY;YAC9C,KAAK,SAAS,GAAG;YACjB,KAAK,IAAI,GAAG;YACZ,gBAAgB,KAAK,eAAe,KAAK,MAAM;QACnD;QACA,IAAI,QAAQ,cAAc,EAAE;YACxB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA,OAAQ,KAAK,IAAI,CAAC,MAAM,GAAG;QACpD;IACJ;IACA,yBAAyB,SAAS,KAAK,EAAE,MAAM;QAC3C,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,IAAI,CAAC,KAAK,GAAG;YACT,OAAO;YACP,QAAQ;QACZ;QACA,KAAK,gBAAgB;QACrB,KAAK,eAAe,CAAC;QACrB,KAAK,YAAY,CAAC;QAClB,MAAM,OAAO,KAAK,gBAAgB;QAClC,IAAI,KAAK,KAAK,GAAG,SAAS,KAAK,MAAM,GAAG,QAAQ;YAC5C,KAAK,SAAS;QAClB;IACJ;IACA,cAAc,SAAS,KAAK;QACxB,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,WAAW,KAAK,SAAS;QAC/B,MAAM,eAAe,iBAAiB,QAAQ,WAAW;QACzD,KAAK,UAAU,GAAG,CAAC;QACnB,MAAM,mBAAmB,CAAC,QAAQ,cAAc,GAAG,CAAC,UAAU;YAC1D,MAAM,QAAQ,SAAS,MAAM;YAC7B,aAAa,UAAU,MAAM,IAAI,EAAE,IAAI,CAAC;gBACpC,MAAM,MAAM,IAAI;gBAChB,SAAS,MAAM,OAAO;gBACtB,QAAQ,MAAM,MAAM;YACxB,GAAG,MAAM,CAAC;gBACN,SAAS;YACb;QACJ,IAAI,QAAQ,cAAc;QAC1B,MAAM,WAAW,KAAK,OAAO,CAAC,YAAY,CAAC;QAC3C,MAAM,eAAe,KAAK,aAAa;QACvC,aAAa,GAAG,CAAC,CAAA,GAAA,4JAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,IAAI;QAC9C,KAAK,cAAc,GAAG,EAAE;QACxB,KAAK,gBAAgB,GAAG,EAAE;QAC1B,KAAK,MAAM,GAAG,CAAC,SAAS,EAAE,EAAE,GAAG,CAAE,CAAC,UAAU;YACxC,MAAM,kBAAkB,SAAS,MAAM;YACvC,MAAM,cAAc,gBAAgB,MAAM;YAC1C,MAAM,kBAAkB,YAAY,IAAI;YACxC,SAAS,IAAI,GAAG,SAAS,MAAM,CAAC,IAAI;YACpC,MAAM,SAAS;gBACX,QAAQ,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,aAAa;oBACxB,MAAM,mBAAmB,QAAQ,WAAW,IAAI,QAAQ,YAAY;oBACpE,OAAO;gBACX;gBACA,OAAO,SAAS,gBAAgB,KAAK,EAAE,iBAAiB;gBACxD,WAAW,SAAS,gBAAgB,SAAS,EAAE,iBAAiB;YACpE;YACA,SAAS,MAAM,GAAG;YAClB,MAAM,YAAY,SAAS,CAAC,GAAG,MAAM,CAAC;YACtC,MAAM,cAAc,SAAS,CAAC,GAAG,IAAI,CAAC;gBAClC,OAAO;YACX,GAAG,MAAM,CAAC;YACV,KAAK,cAAc,CAAC,EAAE,GAAG,IAAI,oLAAA,CAAA,WAAQ;YACrC,KAAK,gBAAgB,CAAC,IAAI,CAAC;YAC3B,MAAM,OAAO;gBACT,OAAO,KAAK,YAAY,CAAC,UAAU;gBACnC,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,SAAS;oBACL,IAAI,SAAS,EAAE;oBACf,UAAU,SAAS,QAAQ;oBAC3B,eAAe,SAAS,aAAa;gBACzC;gBACA,QAAQ;gBACR,kBAAkB,QAAQ,gBAAgB;gBAC1C,cAAc;gBACd,QAAQ,EAAE;gBACV,cAAa,KAAK;oBACd,SAAS,MAAM,GAAG,cAAc,MAAM,OAAO,SAAS,IAAI;oBAC1D,YAAY,KAAK;oBACjB,SAAS,MAAM,CAAC;wBACZ,OAAO;wBACP,WAAW,YAAY,OAAO;wBAC9B,YAAY,KAAK,cAAc,CAAC,EAAE,CAAC,OAAO;oBAC9C;gBACJ;YACJ;YACA,KAAK,YAAY,CAAC,OAAO,MAAM;YAC/B,KAAK,WAAW,CAAC,UAAU;YAC3B,IAAI,KAAK,MAAM,SAAS,EAAE,EAAE;gBACxB,KAAK,UAAU,CAAC,SAAS,EAAE,CAAC,GAAG;YACnC;YACA,OAAO;QACX;IACJ;IACA,oBAAoB;QAChB,OAAO,IAAI,CAAC,gBAAgB,IAAI,EAAE;IACtC;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,cAAc,IAAI,EAAE;IACpC;IACA,cAAc;QACV,IAAI,QAAQ,IAAI,CAAC,KAAK,IAAI,EAAE;QAC5B,MAAM,UAAU,IAAI,CAAC,QAAQ,IAAI,CAAC;QAClC,IAAI,QAAQ,QAAQ,EAAE;YAClB,QAAQ,MAAM,KAAK,GAAG,OAAO;QACjC;QACA,OAAO,MAAM,MAAM,CAAE,CAAA,IAAK,EAAE,OAAO;IACvC;IACA,cAAc,SAAS,OAAO;QAC1B,IAAI,CAAC,yBAAyB,CAAC;QAC/B,IAAI,CAAC,gBAAgB,CAAC,QAAQ,MAAM;IACxC;IACA,OAAO;QACH,MAAM,oBAAoB,IAAI,CAAC,kBAAkB;QACjD,qBAAqB,kBAAkB,OAAO;QAC9C,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,GAAG,GAAG;QAC3F,OAAO,IAAI;IACf;IACA,iBAAiB,SAAS,eAAe;QACrC,IAAI,CAAC,oBAAoB;QACzB,IAAI,CAAC,kBAAkB,CAAC;IAC5B;IACA,sBAAsB;QAClB,IAAI,CAAC,MAAM,CAAC,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YAAC;YAAG;SAAE;QAChD,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG;QAC/C,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;YACtC,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;QACZ;IACJ;IACA,eAAe,SAAS,EAAE;QACtB,iBAAiB,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;QACnD,OAAO,IAAI;IACf;IACA,YAAY,SAAS,EAAE;QACnB,iBAAiB,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;QACnD,OAAO,IAAI;IACf;IACA,WAAW,SAAS,EAAE;QAClB,iBAAiB,IAAI,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,MAAM,EAAE;QACnD,OAAO,IAAI;IACf;IACA,cAAc,SAAS,IAAI,EAAE,KAAK;QAC9B,MAAM,oBAAoB,IAAI,CAAC,mBAAmB,CAAC;QACnD,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,QAAQ,SAAS,QAAQ,gBAAgB;QAC/C,MAAM,OAAO,QAAQ,aAAa,CAAC,IAAI,CAAC,mBAAmB;QAC3D,MAAM,YAAY,WAAW,KAAK,WAAW,IAAI;YAC7C,OAAO,QAAQ,IAAI,CAAC,KAAK;YACzB,SAAS,KAAK,WAAW;QAC7B,IAAI,CAAC;QACL,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,GAAG,GAAG,CAAC,CAAA,GAAA,4JAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY,IAAI,CAAC;YACzE,OAAO;YACP,OAAO,QAAQ,QAAQ;QAC3B,GAAG,MAAM,CAAC;IACd;IACA,aAAa,SAAS,IAAI,EAAE,KAAK;QAC7B,MAAM,oBAAoB,IAAI,CAAC,mBAAmB,CAAC;QACnD,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,IAAI,CAAC,mBAAmB;QACjE,IAAI,WAAW,SAAS,OAAO,MAAM;YACjC,MAAM,QAAQ,CAAC;QACnB;IACJ;IACA,mBAAmB;QACf,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW,KAAK,QAAQ,CAAC,QAAQ,KAAK;QAC5C,MAAM,QAAQ,KAAK,QAAQ,CAAC,eAAe;QAC3C,MAAM,OAAO,SAAS,CAAC,WAAW,KAAK,QAAQ,CAAC,wBAAwB,GAAG,IAAI;QAC/E,IAAI,KAAK,QAAQ,CAAC,MAAM,CAAC,OAAO,IAAI,CAAC,YAAY,KAAK,KAAK,UAAU,MAAM;YACvE,KAAK,WAAW,GAAG,KAAK,SAAS,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG,IAAI,CAAC;gBACpD,MAAM;gBACN,OAAO,KAAK,gBAAgB;YAChC,GAAG,MAAM,CAAC,KAAK,kBAAkB;QACrC;IACJ;IACA,oBAAoB,SAAS,OAAO;QAChC,MAAM,OAAO,IAAI;QACjB,IAAI,YAAY;QAChB,MAAM,gBAAgB,KAAK,sBAAsB;QACjD,MAAM,aAAa,KAAK,MAAM,CAAC,MAAM;QACrC,IAAI;QACJ,GAAG;YACC,QAAQ,EAAE;YACV,KAAK,YAAY,CAAC,OAAO;YACzB,KAAK,WAAW,CAAC,OAAO;YACxB;QACJ,QAAS,eAAe,OAAO,eAAe,YAAY,QAAQ,MAAM,KAAK,YAAY,WAAY;QACrG,KAAK,kBAAkB,CAAC,OAAO;IACnC;IACA,cAAc,SAAS,KAAK,EAAE,aAAa;QACvC,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAC,MAAM;YACxB,MAAM,YAAY,SAAS,OAAO,eAAe;YACjD,MAAM,WAAW;gBACb,OAAO,KAAK,SAAS,CAAC,KAAK;gBAC3B,QAAQ,KAAK,SAAS,CAAC,MAAM;gBAC7B,SAAS,KAAK,KAAK;gBACnB,MAAM,KAAK,SAAS;gBACpB,KAAK,OAAO;gBACZ,WAAW;YACf;YACA,MAAM,YAAY;gBACd,OAAO,KAAK,UAAU,CAAC,KAAK;gBAC5B,QAAQ,KAAK,UAAU,CAAC,MAAM;gBAC9B,SAAS,KAAK,MAAM;gBACpB,KAAK;oBACD,YAAY;oBACZ,UAAU;gBACd;gBACA,MAAM;oBACF,OAAO,KAAK,UAAU,CAAC,KAAK;oBAC5B,QAAQ,KAAK,UAAU,CAAC,MAAM;oBAC9B,GAAG,KAAK,UAAU,CAAC,CAAC;oBACpB,GAAG,KAAK,UAAU,CAAC,CAAC;gBACxB;gBACA,WAAW;YACf;YACA,IAAI;YACJ,IAAI;YACJ,MAAM,kBAAkB,cAAc,YAAY,GAAG,cAAc;YACnE,IAAI,cAAc,oBAAoB,EAAE;gBACpC,YAAY;gBACZ,aAAa;YACjB,OAAO;gBACH,YAAY;gBACZ,aAAa;YACjB;YACA,SAAS,CAAC,gBAAgB,GAAG,cAAc,WAAW;YACtD,UAAU,UAAU,CAAC,IAAI,CAAC;YAC1B,UAAU,SAAS,CAAC,IAAI,CAAC;QAC7B;IACJ;IACA,aAAa,SAAS,KAAK,EAAE,aAAa;QACtC,IAAI;QACJ,IAAI,UAAU,cAAc,UAAU;QACtC,MAAM,OAAO,CAAE,CAAA,OAAQ,aAAa,MAAM;QAC1C,UAAU,cAAc,OAAO;QAC/B,IAAI,cAAc,cAAc,EAAE;YAC9B,IAAI,cAAc,YAAY,EAAE;gBAC5B,IAAK,IAAI,GAAG,IAAI,MAAM,MAAM,EAAG;oBAC3B,UAAU;wBAAC,KAAK,CAAC,IAAI;wBAAE,KAAK,CAAC,IAAI;qBAAC,EAAE,OAAO,CAAC;gBAChD;YACJ;QACJ,OAAO;YACH,UAAU,OAAO,OAAO,CAAC;QAC7B;QAEA,SAAS,YAAY,IAAI;YACrB,aAAa,MAAM;QACvB;IACJ;IACA,oBAAoB,SAAS,KAAK,EAAE,aAAa;QAC7C,MAAM,OAAO,IAAI;QACjB,MAAM,WAAW;YACb,GAAG;YACH,GAAG;QACP;QACA,MAAM,gBAAgB,iBAAiB,OAAO;QAC9C,MAAM,OAAO,CAAE,CAAA;YACX,MAAM,YAAY,IAAI,CAAC,EAAE;YACzB,MAAM,YAAY,UAAU,SAAS,IAAI,cAAc,UAAU;YACjE,QAAQ,CAAC,cAAc,SAAS,CAAC,GAAG,4BAA4B,MAAM,eAAe;YACrF,KAAK,OAAO,CAAE,CAAA;gBACV,MAAM,SAAS,KAAK,MAAM,IAAI,cAAc,OAAO;gBACnD,MAAM,OAAO,IAAI,qKAAA,CAAA,uBAAoB,CAAC,KAAK,OAAO,EAAE,KAAK,IAAI;gBAC7D,MAAM,kBAAkB;oBACpB,GAAG,SAAS,CAAC;oBACb,GAAG,SAAS,CAAC;oBACb,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;gBACvB;gBACA,MAAM,WAAW,IAAI,qKAAA,CAAA,uBAAoB,CAAC,MAAM;gBAChD,MAAM,aAAa,KAAK,MAAM,CAAC,KAAK,SAAS,CAAC;gBAC9C,KAAK,QAAQ,CAAC;oBACV,IAAI;oBACJ,IAAI,KAAK,GAAG;oBACZ,IAAI,KAAK,GAAG;gBAChB;gBACA,WAAW,MAAM,CAAC,IAAI,CAAC;gBACvB,QAAQ,CAAC,cAAc,SAAS,CAAC,IAAI,IAAI,CAAC,cAAc,OAAO,CAAC,GAAG;YACvE;YACA,QAAQ,CAAC,cAAc,YAAY,CAAC,IAAI,SAAS,CAAC,cAAc,UAAU,CAAC,GAAG;QAClF;QACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;YACjB,MAAM,WAAW,4BAA4B,KAAK,MAAM,CAAC,EAAE,CAAC,gBAAgB,IAAI,KAAK,MAAM,CAAC,EAAE,CAAC,gBAAgB;YAC/G,MAAM,aAAa,KAAK,QAAQ,CAAC,iBAAiB,GAAG;YACrD,MAAM,WAAW,KAAK,QAAQ,CAAC,cAAc,GAAG;YAChD,KAAK,OAAO,CAAC,IAAI,GAAG,SAAS,IAAI,GAAG;YACpC,KAAK,OAAO,CAAC,KAAK,GAAG,SAAS,KAAK,GAAG;YACtC,KAAK,OAAO,CAAC,GAAG,GAAG,SAAS,GAAG,GAAG;YAClC,KAAK,OAAO,CAAC,MAAM,GAAG,SAAS,MAAM,GAAG;QAC5C;IACJ;IACA,wBAAwB;QACpB,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,cAAc,QAAQ,WAAW;QACvC,MAAM,gBAAgB;YAClB,gBAAgB,QAAQ,cAAc;YACtC,aAAa,QAAQ,WAAW;QACpC;QACA,MAAM,QAAQ,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,KAAK,WAAW,GAAG,IAAI,QAAQ,gBAAgB,GAAG,CAAC;QACrF,MAAM,SAAS,KAAK,KAAK,CAAC,MAAM,GAAG,CAAC,KAAK,WAAW,GAAG,IAAI,QAAQ,gBAAgB,GAAG,CAAC;QACvF,IAAI,gBAAgB,YAAY;YAC5B,cAAc,MAAM,GAAG;YACvB,cAAc,OAAO,GAAG,QAAQ,iBAAiB;YACjD,cAAc,SAAS,GAAG;YAC1B,cAAc,OAAO,GAAG;YACxB,cAAc,UAAU,GAAG;YAC3B,cAAc,YAAY,GAAG;YAC7B,cAAc,UAAU,GAAG,QAAQ,cAAc;YACjD,cAAc,SAAS,GAAG,QAAQ,WAAW;YAC7C,cAAc,YAAY,GAAG,QAAQ,QAAQ;YAC7C,cAAc,eAAe,GAAG;YAChC,cAAc,WAAW,GAAG;YAC5B,IAAI,QAAQ,gBAAgB,KAAK,UAAU,QAAQ,gBAAgB,KAAK,KAAK;gBACzE,cAAc,WAAW,GAAG;gBAC5B,cAAc,YAAY,GAAG;YACjC;QACJ,OAAO;YACH,cAAc,MAAM,GAAG;YACvB,cAAc,OAAO,GAAG,QAAQ,cAAc;YAC9C,cAAc,SAAS,GAAG;YAC1B,cAAc,OAAO,GAAG;YACxB,cAAc,UAAU,GAAG;YAC3B,cAAc,YAAY,GAAG;YAC7B,cAAc,UAAU,GAAG,QAAQ,iBAAiB;YACpD,cAAc,SAAS,GAAG,QAAQ,QAAQ;YAC1C,cAAc,YAAY,GAAG,QAAQ,WAAW;YAChD,cAAc,eAAe,GAAG;YAChC,cAAc,WAAW,GAAG;YAC5B,IAAI,QAAQ,gBAAgB,KAAK,SAAS,QAAQ,gBAAgB,KAAK,MAAM;gBACzE,cAAc,WAAW,GAAG;gBAC5B,cAAc,YAAY,GAAG;YACjC;QACJ;QACA,IAAI,CAAC,cAAc,SAAS,EAAE;YAC1B,IAAI,cAAc,YAAY,EAAE;gBAC5B,cAAc,SAAS,GAAG,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG,cAAc,YAAY;YACnF,OAAO;gBACH,cAAc,SAAS,GAAG,KAAK,MAAM,CAAC,MAAM;YAChD;QACJ;QACA,IAAI,QAAQ,gBAAgB,KAAK,OAAO,QAAQ,gBAAgB,KAAK,MAAM;YACvE,cAAc,oBAAoB,GAAG;QACzC;QACA,cAAc,gBAAgB,GAAG,QAAQ,gBAAgB;QACzD,cAAc,YAAY,GAAG,cAAc,YAAY,IAAI,MAAM,KAAK,MAAM,CAAC,MAAM,GAAG,cAAc,SAAS;QAC7G,OAAO;IACX;IACA,2BAA2B,SAAS,eAAe;QAC/C,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;YACnB;QACJ;QACA,MAAM,SAAS,gBAAgB,MAAM;QACrC,MAAM,YAAY,IAAI,CAAC,kBAAkB;QACzC,MAAM,qBAAqB;YACvB,GAAG,OAAO,UAAU,CAAC,GAAG,gBAAgB,gBAAgB;YACxD,GAAG,OAAO,UAAU,CAAC,GAAG,gBAAgB,gBAAgB;YACxD,OAAO,OAAO,UAAU,KAAK,IAAI,IAAI,gBAAgB,gBAAgB;YACrE,QAAQ,OAAO,UAAU,MAAM;YAC/B,SAAS,gBAAgB,iBAAiB;QAC9C;QACA,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,MAAM;YACzE,kBAAkB,CAAC,eAAe,GAAG,OAAO,KAAK;YACjD,mBAAmB,MAAM,GAAG,OAAO,KAAK;YACxC,kBAAkB,CAAC,iBAAiB,GAAG,OAAO,OAAO;YACrD,mBAAmB,SAAS,GAAG,OAAO,SAAS;YAC/C,mBAAmB,EAAE,GAAG,OAAO,YAAY,IAAI;YAC/C,mBAAmB,EAAE,GAAG,OAAO,YAAY,IAAI;QACnD;QACA,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC;IAC1B;IACA,kBAAkB,SAAS,MAAM;QAC7B,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC1B;QACJ;QACA,MAAM,MAAM,IAAI,CAAC,kBAAkB;QACnC,IAAI,MAAM,IAAI,OAAO,GAAG,GAAG,OAAO,MAAM;QACxC,IAAI,mBAAmB,GAAG,IAAI,KAAK;QACnC,IAAI,KAAK,IAAI,OAAO,IAAI,GAAG,OAAO,KAAK;QACvC,IAAI,CAAC,IAAI,OAAO,IAAI;QACpB,IAAI,CAAC,IAAI,OAAO,GAAG;QACnB,IAAI,CAAC,aAAa,GAAG;IACzB;IACA,oBAAoB;QAChB,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,OAAO;QAC5C,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,yBAAyB;QACtD,MAAM,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO;QAC3C,MAAM,kBAAkB,IAAI,CAAC,WAAW,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,gBAAgB,GAAG;QAChF,IAAI,MAAM,GAAG,UAAU,MAAM,GAAG,SAAS,MAAM,GAAG;QAClD,SAAS,KAAK,GAAG,IAAI,KAAK,IAAI,CAAC,IAAI,KAAK,GAAG,SAAS,KAAK;QACzD,OAAO;IACX;IACA,mBAAmB,SAAS,KAAK;QAC7B,MAAM,OAAO,IAAI;QACjB,IAAI,KAAK,QAAQ,CAAC,OAAO,EAAE;YACvB,OAAO,SAAS,GAAG;gBACf,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK;YACzB;QACJ,OAAO;YACH,OAAO,kLAAA,CAAA,OAAI;QACf;IACJ;IACA,kBAAkB;QACd,MAAM,UAAU,IAAI,CAAC,QAAQ;QAC7B,MAAM,eAAe,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,aAAa,GAAG;YAChE,OAAO;YACP,QAAQ;YACR,GAAG;YACH,GAAG;QACP;QACA,IAAI,SAAS;YACT,aAAa,iBAAiB,GAAG,QAAQ,iBAAiB;YAC1D,aAAa,mBAAmB,GAAG,QAAQ,mBAAmB;YAC9D,IAAI,QAAQ,WAAW,KAAK,YAAY;gBACpC,aAAa,aAAa,GAAG,QAAQ,iBAAiB;gBACtD,aAAa,OAAO,GAAG;YAC3B,OAAO,IAAI,QAAQ,mBAAmB,KAAK,QAAQ;gBAC/C,aAAa,aAAa,GAAG,QAAQ,iBAAiB;gBACtD,aAAa,OAAO,GAAG;YAC3B,OAAO;gBACH,aAAa,aAAa,GAAG,QAAQ,mBAAmB;gBACxD,aAAa,OAAO,GAAG;YAC3B;YACA,aAAa,QAAQ,GAAG;gBACpB,YAAY,QAAQ,mBAAmB;gBACvC,UAAU,QAAQ,iBAAiB;YACvC;YACA,OAAO;QACX;QACA,OAAO;IACX;IACA,OAAO,SAAS,CAAC,EAAE,CAAC;QAChB,MAAM,OAAO,IAAI;QACjB,IAAI,MAAM,CAAC;QACX,IAAI,KAAK,kBAAkB,EAAE;YACzB,KAAK,kBAAkB,CAAC,IAAI,CAAC;gBACzB,YAAY,IAAI,KAAK,aAAa,CAAC,CAAC;gBACpC,YAAY,IAAI,KAAK,aAAa,CAAC,CAAC;YACxC;QACJ;QACA,KAAK,MAAM,IAAI,KAAK,WAAW,CAAC,KAAK,aAAa,CAAC,mBAAmB;QACtE,KAAK,aAAa,IAAI,KAAK,aAAa;QACxC,IAAI,KAAK,kBAAkB,EAAE;YACzB,MAAM,KAAK,YAAY,CAAC,OAAO;QACnC;QACA,KAAK,GAAG,GAAG,IAAI,CAAC;QAChB,KAAK,GAAG,GAAG,IAAI,CAAC;QAChB,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,KAAK;QAC5B,KAAK,GAAG,GAAG,IAAI,CAAC,GAAG,IAAI,MAAM;QAC7B,OAAO;IACX;IACA,aAAa,SAAS,QAAQ;QAC1B,MAAM,OAAO,IAAI;QACjB,MAAM,QAAQ,KAAK,MAAM;QACzB,MAAM,WAAW,MAAM,yBAAyB;QAChD,IAAI,CAAC,YAAY,CAAC,MAAM,OAAO,IAAI;YAC/B;QACJ;QACA,MAAM,QAAQ,WAAW,CAAC,KAAK,WAAW,GAAG,IAAI,KAAK,QAAQ,CAAC,gBAAgB,GAAG,CAAC;QACnF,MAAM,eAAe,MAAM,UAAU;QACrC,IAAI,SAAS,SAAS,CAAC,GAAG,aAAa,MAAM,CAAC,GAAG;QACjD,IAAI,SAAS;QACb,IAAI,aAAa,iBAAiB,KAAK,UAAU,KAAK,aAAa,EAAE;YACjE,UAAU,KAAK,aAAa,CAAC,OAAO,GAAG,MAAM;QACjD;QACA,IAAI,aAAa,mBAAmB,KAAK,OAAO;YAC5C,SAAS,QAAQ,SAAS,KAAK;QACnC,OAAO,IAAI,aAAa,mBAAmB,KAAK,QAAQ;YACpD,SAAS,CAAC,QAAQ,SAAS,KAAK,IAAI;QACxC;QACA,MAAM,KAAK,CAAC,QAAQ;IACxB;IACA,eAAe;QACX,MAAM,WAAW,IAAI,CAAC,MAAM,CAAC,gBAAgB;QAC7C,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC,OAAO;QAC5C,MAAM,eAAe,IAAI,CAAC,MAAM,CAAC,UAAU,MAAM,CAAC;QAClD,IAAI,SAAS;QACb,IAAI,IAAI;QACR,IAAI,SAAS,KAAK,GAAG,UAAU,KAAK,IAAI,IAAI,CAAC,QAAQ,CAAC,mBAAmB,KAAK,QAAQ;YAClF,SAAS,SAAS,KAAK,GAAG,IAAI,UAAU,KAAK,GAAG;QACpD;QACA,IAAI,aAAa,iBAAiB,KAAK,KAAK;YACxC,IAAI,SAAS,MAAM;QACvB;QACA,IAAI,MAAM,UAAU,MAAM,GAAG;YACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;gBACpB,YAAY;gBACZ,YAAY;YAChB;YACA,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;gBACjB,KAAK,OAAO,CAAC,IAAI,IAAI;gBACrB,KAAK,OAAO,CAAC,KAAK,IAAI;gBACtB,KAAK,OAAO,CAAC,GAAG,IAAI;gBACpB,KAAK,OAAO,CAAC,MAAM,IAAI;YAC3B;QACJ;IACJ;IACA,aAAa;QACT,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;IACjC;IACA,UAAU,SAAS,CAAC,EAAE,CAAC;QACnB,OAAO,KAAK,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,KAAK,IAAI,CAAC,GAAG;IAC3E;IACA,gBAAgB,SAAS,CAAC,EAAE,CAAC;QACzB,MAAM,QAAQ,IAAI,CAAC,MAAM;QACzB,MAAM,cAAc,IAAI,CAAC,kBAAkB;QAC3C,KAAK,YAAY,IAAI,CAAC;QACtB,KAAK,YAAY,IAAI,CAAC;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;YACnC,IAAI,OAAO,KAAK,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,IAAI;gBAChC,OAAO,KAAK,CAAC,EAAE,CAAC,OAAO;YAC3B;QACJ;QACA,OAAO;IACX;IACA,SAAS;QACL,IAAI,CAAC,MAAM,IAAI,IAAI,CAAC,MAAM,CAAC,OAAO;QAClC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,SAAS,GAAG,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,MAAM,GAAG;QACxH,OAAO,IAAI;IACf;IACA,eAAe;QACX,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI;YACnB,OAAO;QACX;QACA,MAAM,MAAM,IAAI,CAAC,gBAAgB;QACjC,OAAO;YACH,qBAAqB,IAAI,CAAC,QAAQ,CAAC,mBAAmB;YACtD,mBAAmB,IAAI,CAAC,QAAQ,CAAC,iBAAiB;YAClD,MAAM,IAAI,OAAO;YACjB,UAAU;YACV,UAAU,IAAI,CAAC,WAAW;QAC9B;IACJ;IACA,SAAS,SAAS,IAAI;QAClB,IAAI,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE;YAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;YAC1B,IAAI,CAAC,QAAQ,GAAG;QACpB,OAAO;YACH,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,CAAA;gBACjB,KAAK,MAAM,GAAG,EAAE;YACpB;YACA,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QACjD;QACA,MAAM,OAAO,IAAI,CAAC,gBAAgB;QAClC,OAAO;YAAC,KAAK,KAAK;YAAE,KAAK,MAAM;SAAC;IACpC;IACA,MAAM,SAAS,IAAI;QACf,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;IAC/B;IACA,WAAW;QACP,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC;QAChC,IAAI,CAAC,KAAK;IACd;AACJ;AACO,MAAM,SAAS;IAClB,MAAM;IACN,MAAM;QACF,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,CAAC,GAAG,IAAI,CAAC;YAClC,OAAO,IAAI,CAAC,gBAAgB,GAAG;QACnC,GAAG,WAAW,GAAG,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI;QAC3C,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO;YACtB,UAAU,IAAI,CAAC,SAAS;YACxB,OAAO;YACP,QAAQ,IAAI;YACZ,gBAAgB,IAAI,CAAC,gBAAgB,GAAG;YACxC,iBAAiB,IAAI,CAAC,gBAAgB,GAAG;YACzC,WAAW;YACX,iBAAiB,SAAS,IAAI;gBAC1B,OAAO;oBACH,MAAM,KAAK,IAAI;oBACf,MAAM,KAAK,IAAI;gBACnB;YACJ;QACJ;QACA,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,OAAO;IACjC;IACA,WAAW;QACP,uBAAuB;YACnB,MAAM,OAAO,IAAI;YACjB,IAAI,CAAC,MAAM,CAAC,OAAO,CAAE,SAAS,IAAI;gBAC9B,KAAK,qBAAqB,CAAC,KAAK,EAAE,EAAE,KAAK,QAAQ;YACrD;QACJ;QACA,aAAa;YACT,IAAI,CAAC,kBAAkB;QAC3B;IACJ;IACA,SAAS;QACL,uBAAuB,SAAS,EAAE,EAAE,KAAK;YACrC,MAAM,SAAS,IAAI,CAAC,OAAO;YAC3B,OAAQ;gBACJ,KAAK;oBACD,OAAO,UAAU,CAAC;oBAClB;gBACJ,KAAK;oBACD,OAAO,aAAa,CAAC;oBACrB;gBACJ;oBACI,OAAO,SAAS,CAAC;YACzB;QACJ;QACA,oBAAoB;YAChB,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,UAAU,KAAK,GAAG;gBACjH,IAAI,CAAC,cAAc,CAAC;oBAAC;iBAAS;YAClC;QACJ;IACJ;IACA,SAAS;QACL,IAAI,CAAC,OAAO,CAAC,OAAO;IACxB;IACA,WAAW,SAAS,WAAW;QAC3B,YAAY,SAAS,CAAC,UAAU,CAAC,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;YAChD,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,GAAG,IAAI;gBAC7B,MAAM,OAAO,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG;gBAC5C,IAAI,MAAM;oBACN,OAAO;wBACH,IAAI,KAAK,EAAE;wBACX,MAAM;oBACV;gBACJ;YACJ;QACJ;QACA,YAAY,SAAS,CAAC;YAClB,MAAM;YACN,SAAS;gBACL,IAAI,CAAC,kBAAkB;YAC3B;YACA,kBAAkB;YAClB,QAAQ;YACR,gBAAgB;QACpB;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1881, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/translators/range.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/translators/range.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isDefined,\r\n    isDate,\r\n    isFunction\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nconst _isDefined = isDefined;\r\nconst _isDate = isDate;\r\nconst _isFunction = isFunction;\r\nimport {\r\n    unique\r\n} from \"../core/utils\";\r\nconst minSelector = \"min\";\r\nconst maxSelector = \"max\";\r\nconst minVisibleSelector = \"minVisible\";\r\nconst maxVisibleSelector = \"maxVisible\";\r\nconst baseSelector = \"base\";\r\nconst axisTypeSelector = \"axisType\";\r\n\r\nfunction otherLessThan(thisValue, otherValue) {\r\n    return otherValue < thisValue\r\n}\r\n\r\nfunction otherGreaterThan(thisValue, otherValue) {\r\n    return otherValue > thisValue\r\n}\r\n\r\nfunction compareAndReplace(thisValue, otherValue, setValue, compare) {\r\n    const otherValueDefined = _isDefined(otherValue);\r\n    if (_isDefined(thisValue)) {\r\n        if (otherValueDefined && compare(thisValue, otherValue)) {\r\n            setValue(otherValue)\r\n        }\r\n    } else if (otherValueDefined) {\r\n        setValue(otherValue)\r\n    }\r\n}\r\nexport const Range = function(range) {\r\n    range && extend(this, range)\r\n};\r\nconst _Range = Range;\r\n_Range.prototype = {\r\n    constructor: _Range,\r\n    addRange: function(otherRange) {\r\n        const that = this;\r\n        const categories = that.categories;\r\n        const otherCategories = otherRange.categories;\r\n        const isDiscrete = \"discrete\" === that.axisType;\r\n        const compareAndReplaceByField = function(field, compare) {\r\n            compareAndReplace(that[field], otherRange[field], (function(value) {\r\n                that[field] = value\r\n            }), compare)\r\n        };\r\n        const controlValuesByVisibleBounds = function(valueField, visibleValueField, compare) {\r\n            compareAndReplace(that[valueField], that[visibleValueField], (function(value) {\r\n                _isDefined(that[valueField]) && (that[valueField] = value)\r\n            }), compare)\r\n        };\r\n        const checkField = function(field) {\r\n            that[field] = that[field] || otherRange[field]\r\n        };\r\n        checkField(\"invert\");\r\n        checkField(\"containsConstantLine\");\r\n        checkField(\"axisType\");\r\n        checkField(\"dataType\");\r\n        checkField(\"isSpacedMargin\");\r\n        if (\"logarithmic\" === that.axisType) {\r\n            checkField(\"base\")\r\n        } else {\r\n            that.base = void 0\r\n        }\r\n        compareAndReplaceByField(\"min\", otherLessThan);\r\n        compareAndReplaceByField(\"max\", otherGreaterThan);\r\n        if (isDiscrete) {\r\n            checkField(\"minVisible\");\r\n            checkField(\"maxVisible\")\r\n        } else {\r\n            compareAndReplaceByField(\"minVisible\", otherLessThan);\r\n            compareAndReplaceByField(\"maxVisible\", otherGreaterThan)\r\n        }\r\n        compareAndReplaceByField(\"interval\", otherLessThan);\r\n        if (!isDiscrete) {\r\n            controlValuesByVisibleBounds(\"min\", \"minVisible\", otherLessThan);\r\n            controlValuesByVisibleBounds(\"min\", \"maxVisible\", otherLessThan);\r\n            controlValuesByVisibleBounds(\"max\", \"maxVisible\", otherGreaterThan);\r\n            controlValuesByVisibleBounds(\"max\", \"minVisible\", otherGreaterThan)\r\n        }\r\n        if (void 0 === categories) {\r\n            that.categories = otherCategories\r\n        } else {\r\n            that.categories = otherCategories ? unique(categories.concat(otherCategories)) : categories\r\n        }\r\n        if (\"logarithmic\" === that.axisType) {\r\n            checkField(\"allowNegatives\");\r\n            compareAndReplaceByField(\"linearThreshold\", otherLessThan)\r\n        }\r\n        return that\r\n    },\r\n    isEmpty: function() {\r\n        return (!_isDefined(this.min) || !_isDefined(this.max)) && (!this.categories || 0 === this.categories.length)\r\n    },\r\n    correctValueZeroLevel: function() {\r\n        const that = this;\r\n        if (_isDate(that.max) || _isDate(that.min)) {\r\n            return that\r\n        }\r\n\r\n        function setZeroLevel(min, max) {\r\n            that[min] < 0 && that[max] < 0 && (that[max] = 0);\r\n            that[min] > 0 && that[max] > 0 && (that[min] = 0)\r\n        }\r\n        setZeroLevel(\"min\", \"max\");\r\n        setZeroLevel(\"minVisible\", \"maxVisible\");\r\n        return that\r\n    },\r\n    sortCategories(sort) {\r\n        if (false === sort || !this.categories) {\r\n            return\r\n        }\r\n        if (Array.isArray(sort)) {\r\n            const sortValues = sort.map((item => item.valueOf()));\r\n            const filteredSeriesCategories = this.categories.filter((item => -1 === sortValues.indexOf(item.valueOf())));\r\n            this.categories = sort.concat(filteredSeriesCategories)\r\n        } else {\r\n            const notAFunction = !_isFunction(sort);\r\n            if (notAFunction && \"string\" !== this.dataType) {\r\n                sort = (a, b) => a.valueOf() - b.valueOf()\r\n            } else if (notAFunction) {\r\n                sort = false\r\n            }\r\n            sort && this.categories.sort(sort)\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAKA;AAAA;AAMA;;;AAHA,MAAM,aAAa,gLAAA,CAAA,YAAS;AAC5B,MAAM,UAAU,gLAAA,CAAA,SAAM;AACtB,MAAM,cAAc,gLAAA,CAAA,aAAU;;AAI9B,MAAM,cAAc;AACpB,MAAM,cAAc;AACpB,MAAM,qBAAqB;AAC3B,MAAM,qBAAqB;AAC3B,MAAM,eAAe;AACrB,MAAM,mBAAmB;AAEzB,SAAS,cAAc,SAAS,EAAE,UAAU;IACxC,OAAO,aAAa;AACxB;AAEA,SAAS,iBAAiB,SAAS,EAAE,UAAU;IAC3C,OAAO,aAAa;AACxB;AAEA,SAAS,kBAAkB,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,OAAO;IAC/D,MAAM,oBAAoB,WAAW;IACrC,IAAI,WAAW,YAAY;QACvB,IAAI,qBAAqB,QAAQ,WAAW,aAAa;YACrD,SAAS;QACb;IACJ,OAAO,IAAI,mBAAmB;QAC1B,SAAS;IACb;AACJ;AACO,MAAM,QAAQ,SAAS,KAAK;IAC/B,SAAS,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,EAAE;AAC1B;AACA,MAAM,SAAS;AACf,OAAO,SAAS,GAAG;IACf,aAAa;IACb,UAAU,SAAS,UAAU;QACzB,MAAM,OAAO,IAAI;QACjB,MAAM,aAAa,KAAK,UAAU;QAClC,MAAM,kBAAkB,WAAW,UAAU;QAC7C,MAAM,aAAa,eAAe,KAAK,QAAQ;QAC/C,MAAM,2BAA2B,SAAS,KAAK,EAAE,OAAO;YACpD,kBAAkB,IAAI,CAAC,MAAM,EAAE,UAAU,CAAC,MAAM,EAAG,SAAS,KAAK;gBAC7D,IAAI,CAAC,MAAM,GAAG;YAClB,GAAI;QACR;QACA,MAAM,+BAA+B,SAAS,UAAU,EAAE,iBAAiB,EAAE,OAAO;YAChF,kBAAkB,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,kBAAkB,EAAG,SAAS,KAAK;gBACxE,WAAW,IAAI,CAAC,WAAW,KAAK,CAAC,IAAI,CAAC,WAAW,GAAG,KAAK;YAC7D,GAAI;QACR;QACA,MAAM,aAAa,SAAS,KAAK;YAC7B,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,IAAI,UAAU,CAAC,MAAM;QAClD;QACA,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,WAAW;QACX,IAAI,kBAAkB,KAAK,QAAQ,EAAE;YACjC,WAAW;QACf,OAAO;YACH,KAAK,IAAI,GAAG,KAAK;QACrB;QACA,yBAAyB,OAAO;QAChC,yBAAyB,OAAO;QAChC,IAAI,YAAY;YACZ,WAAW;YACX,WAAW;QACf,OAAO;YACH,yBAAyB,cAAc;YACvC,yBAAyB,cAAc;QAC3C;QACA,yBAAyB,YAAY;QACrC,IAAI,CAAC,YAAY;YACb,6BAA6B,OAAO,cAAc;YAClD,6BAA6B,OAAO,cAAc;YAClD,6BAA6B,OAAO,cAAc;YAClD,6BAA6B,OAAO,cAAc;QACtD;QACA,IAAI,KAAK,MAAM,YAAY;YACvB,KAAK,UAAU,GAAG;QACtB,OAAO;YACH,KAAK,UAAU,GAAG,kBAAkB,CAAA,GAAA,4JAAA,CAAA,SAAM,AAAD,EAAE,WAAW,MAAM,CAAC,oBAAoB;QACrF;QACA,IAAI,kBAAkB,KAAK,QAAQ,EAAE;YACjC,WAAW;YACX,yBAAyB,mBAAmB;QAChD;QACA,OAAO;IACX;IACA,SAAS;QACL,OAAO,CAAC,CAAC,WAAW,IAAI,CAAC,GAAG,KAAK,CAAC,WAAW,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,MAAM,IAAI,CAAC,UAAU,CAAC,MAAM;IAChH;IACA,uBAAuB;QACnB,MAAM,OAAO,IAAI;QACjB,IAAI,QAAQ,KAAK,GAAG,KAAK,QAAQ,KAAK,GAAG,GAAG;YACxC,OAAO;QACX;QAEA,SAAS,aAAa,GAAG,EAAE,GAAG;YAC1B,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;YAChD,IAAI,CAAC,IAAI,GAAG,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,IAAI,GAAG,CAAC;QACpD;QACA,aAAa,OAAO;QACpB,aAAa,cAAc;QAC3B,OAAO;IACX;IACA,gBAAe,IAAI;QACf,IAAI,UAAU,QAAQ,CAAC,IAAI,CAAC,UAAU,EAAE;YACpC;QACJ;QACA,IAAI,MAAM,OAAO,CAAC,OAAO;YACrB,MAAM,aAAa,KAAK,GAAG,CAAE,CAAA,OAAQ,KAAK,OAAO;YACjD,MAAM,2BAA2B,IAAI,CAAC,UAAU,CAAC,MAAM,CAAE,CAAA,OAAQ,CAAC,MAAM,WAAW,OAAO,CAAC,KAAK,OAAO;YACvG,IAAI,CAAC,UAAU,GAAG,KAAK,MAAM,CAAC;QAClC,OAAO;YACH,MAAM,eAAe,CAAC,YAAY;YAClC,IAAI,gBAAgB,aAAa,IAAI,CAAC,QAAQ,EAAE;gBAC5C,OAAO,CAAC,GAAG,IAAM,EAAE,OAAO,KAAK,EAAE,OAAO;YAC5C,OAAO,IAAI,cAAc;gBACrB,OAAO;YACX;YACA,QAAQ,IAAI,CAAC,UAAU,CAAC,IAAI,CAAC;QACjC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2024, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/translators/category_translator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/translators/category_translator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    adjust\r\n} from \"../../core/utils/math\";\r\nconst round = Math.round;\r\n\r\nfunction getValue(value) {\r\n    return value\r\n}\r\nconst MIN_VALID_SCALE_OFFSET = .05;\r\nexport default {\r\n    translate: function(category, directionOffset) {\r\n        const canvasOptions = this._canvasOptions;\r\n        const categoryIndex = this._categoriesToPoints[null === category || void 0 === category ? void 0 : category.valueOf()];\r\n        const specialValue = this.translateSpecialCase(category);\r\n        const startPointIndex = canvasOptions.startPointIndex || 0;\r\n        const stickInterval = this._options.stick ? 0 : .5;\r\n        if (isDefined(specialValue)) {\r\n            return round(specialValue)\r\n        }\r\n        if (!categoryIndex && 0 !== categoryIndex) {\r\n            return null\r\n        }\r\n        directionOffset = directionOffset || 0;\r\n        const stickDelta = categoryIndex + stickInterval - startPointIndex + .5 * directionOffset;\r\n        return round(this._calculateProjection(canvasOptions.interval * stickDelta))\r\n    },\r\n    getInterval: function() {\r\n        return this._canvasOptions.interval\r\n    },\r\n    getEventScale: function(zoomEvent) {\r\n        const scale = zoomEvent.deltaScale || 1;\r\n        return 1 - (1 - scale) / (.75 + this.visibleCategories.length / this._categories.length)\r\n    },\r\n    zoom: function(translate, scale) {\r\n        const scaleOffset = Math.abs(Math.abs(scale) - 1);\r\n        const isZoomIn = scale > 1;\r\n        if (1 !== scale && scaleOffset < .05) {\r\n            scale = this.getMinScale(isZoomIn)\r\n        }\r\n        const categories = this._categories;\r\n        const canvasOptions = this._canvasOptions;\r\n        const stick = this._options.stick;\r\n        const invert = canvasOptions.invert;\r\n        const interval = canvasOptions.interval * scale;\r\n        const translateCategories = translate / interval;\r\n        const visibleCount = (this.visibleCategories || []).length;\r\n        let startCategoryIndex = parseInt((canvasOptions.startPointIndex || 0) + translateCategories + .5);\r\n        const categoriesLength = parseInt(adjust(canvasOptions.canvasLength / interval) + (stick ? 1 : 0)) || 1;\r\n        let endCategoryIndex;\r\n        if (invert) {\r\n            startCategoryIndex = parseInt((canvasOptions.startPointIndex || 0) + visibleCount - translateCategories + .5) - categoriesLength\r\n        }\r\n        if (startCategoryIndex < 0) {\r\n            startCategoryIndex = 0\r\n        }\r\n        endCategoryIndex = startCategoryIndex + categoriesLength;\r\n        if (endCategoryIndex > categories.length) {\r\n            endCategoryIndex = categories.length;\r\n            startCategoryIndex = endCategoryIndex - categoriesLength;\r\n            if (startCategoryIndex < 0) {\r\n                startCategoryIndex = 0\r\n            }\r\n        }\r\n        const newVisibleCategories = categories.slice(parseInt(startCategoryIndex), parseInt(endCategoryIndex));\r\n        const newInterval = this._getDiscreteInterval(newVisibleCategories.length, canvasOptions);\r\n        scale = newInterval / canvasOptions.interval;\r\n        translate = this.translate(!invert ? newVisibleCategories[0] : newVisibleCategories[newVisibleCategories.length - 1]) * scale - (canvasOptions.startPoint + (stick ? 0 : newInterval / 2));\r\n        return {\r\n            min: newVisibleCategories[0],\r\n            max: newVisibleCategories[newVisibleCategories.length - 1],\r\n            translate: translate,\r\n            scale: scale\r\n        }\r\n    },\r\n    getMinScale: function(zoom) {\r\n        const canvasOptions = this._canvasOptions;\r\n        let categoriesLength = (this.visibleCategories || this._categories).length;\r\n        categoriesLength += (parseInt(.1 * categoriesLength) || 1) * (zoom ? -2 : 2);\r\n        return canvasOptions.canvasLength / (Math.max(categoriesLength, 1) * canvasOptions.interval)\r\n    },\r\n    getScale: function(min, max) {\r\n        const canvasOptions = this._canvasOptions;\r\n        const visibleArea = this.getCanvasVisibleArea();\r\n        const stickOffset = !this._options.stick && 1;\r\n        let minPoint = isDefined(min) ? this.translate(min, -stickOffset) : null;\r\n        let maxPoint = isDefined(max) ? this.translate(max, +stickOffset) : null;\r\n        if (null === minPoint) {\r\n            minPoint = canvasOptions.invert ? visibleArea.max : visibleArea.min\r\n        }\r\n        if (null === maxPoint) {\r\n            maxPoint = canvasOptions.invert ? visibleArea.min : visibleArea.max\r\n        }\r\n        return this.canvasLength / Math.abs(maxPoint - minPoint)\r\n    },\r\n    isValid: function(value) {\r\n        return isDefined(value) ? this._categoriesToPoints[value.valueOf()] >= 0 : false\r\n    },\r\n    getCorrectValue: getValue,\r\n    to: function(value, direction) {\r\n        const canvasOptions = this._canvasOptions;\r\n        const categoryIndex = this._categoriesToPoints[null === value || void 0 === value ? void 0 : value.valueOf()];\r\n        const startPointIndex = canvasOptions.startPointIndex || 0;\r\n        const stickDelta = categoryIndex + (this._options.stick ? 0 : .5) - startPointIndex + (this._businessRange.invert ? -1 : 1) * direction * .5;\r\n        return round(this._calculateProjection(canvasOptions.interval * stickDelta))\r\n    },\r\n    from: function(position) {\r\n        let direction = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : 0;\r\n        const canvasOptions = this._canvasOptions;\r\n        const startPoint = canvasOptions.startPoint;\r\n        const categories = this.visibleCategories || this._categories;\r\n        const categoriesLength = categories.length;\r\n        const stickInterval = this._options.stick ? .5 : 0;\r\n        let result = round((position - startPoint) / canvasOptions.interval + stickInterval - .5 - .5 * direction);\r\n        if (result >= categoriesLength) {\r\n            result = categoriesLength - 1\r\n        }\r\n        if (result < 0) {\r\n            result = 0\r\n        }\r\n        if (canvasOptions.invert) {\r\n            result = categoriesLength - result - 1\r\n        }\r\n        return categories[result]\r\n    },\r\n    _add: function() {\r\n        return NaN\r\n    },\r\n    toValue: getValue,\r\n    isValueProlonged: true,\r\n    getRangeByMinZoomValue(minZoom, visualRange) {\r\n        const categories = this._categories;\r\n        const minVisibleIndex = categories.indexOf(visualRange.minVisible);\r\n        const maxVisibleIndex = categories.indexOf(visualRange.maxVisible);\r\n        const startIndex = minVisibleIndex + minZoom - 1;\r\n        const endIndex = maxVisibleIndex - minZoom + 1;\r\n        if (categories[startIndex]) {\r\n            return [visualRange.minVisible, categories[startIndex]]\r\n        } else {\r\n            return [categories[endIndex], visualRange.maxVisible]\r\n        }\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;;;AAGA,MAAM,QAAQ,KAAK,KAAK;AAExB,SAAS,SAAS,KAAK;IACnB,OAAO;AACX;AACA,MAAM,yBAAyB;uCAChB;IACX,WAAW,SAAS,QAAQ,EAAE,eAAe;QACzC,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,SAAS,YAAY,KAAK,MAAM,WAAW,KAAK,IAAI,SAAS,OAAO,GAAG;QACtH,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC;QAC/C,MAAM,kBAAkB,cAAc,eAAe,IAAI;QACzD,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI;QAChD,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YACzB,OAAO,MAAM;QACjB;QACA,IAAI,CAAC,iBAAiB,MAAM,eAAe;YACvC,OAAO;QACX;QACA,kBAAkB,mBAAmB;QACrC,MAAM,aAAa,gBAAgB,gBAAgB,kBAAkB,KAAK;QAC1E,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,QAAQ,GAAG;IACpE;IACA,aAAa;QACT,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ;IACvC;IACA,eAAe,SAAS,SAAS;QAC7B,MAAM,QAAQ,UAAU,UAAU,IAAI;QACtC,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI,CAAC,MAAM,IAAI,CAAC,iBAAiB,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC,MAAM;IAC3F;IACA,MAAM,SAAS,SAAS,EAAE,KAAK;QAC3B,MAAM,cAAc,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,SAAS;QAC/C,MAAM,WAAW,QAAQ;QACzB,IAAI,MAAM,SAAS,cAAc,KAAK;YAClC,QAAQ,IAAI,CAAC,WAAW,CAAC;QAC7B;QACA,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,QAAQ,IAAI,CAAC,QAAQ,CAAC,KAAK;QACjC,MAAM,SAAS,cAAc,MAAM;QACnC,MAAM,WAAW,cAAc,QAAQ,GAAG;QAC1C,MAAM,sBAAsB,YAAY;QACxC,MAAM,eAAe,CAAC,IAAI,CAAC,iBAAiB,IAAI,EAAE,EAAE,MAAM;QAC1D,IAAI,qBAAqB,SAAS,CAAC,cAAc,eAAe,IAAI,CAAC,IAAI,sBAAsB;QAC/F,MAAM,mBAAmB,SAAS,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,cAAc,YAAY,GAAG,YAAY,CAAC,QAAQ,IAAI,CAAC,MAAM;QACtG,IAAI;QACJ,IAAI,QAAQ;YACR,qBAAqB,SAAS,CAAC,cAAc,eAAe,IAAI,CAAC,IAAI,eAAe,sBAAsB,MAAM;QACpH;QACA,IAAI,qBAAqB,GAAG;YACxB,qBAAqB;QACzB;QACA,mBAAmB,qBAAqB;QACxC,IAAI,mBAAmB,WAAW,MAAM,EAAE;YACtC,mBAAmB,WAAW,MAAM;YACpC,qBAAqB,mBAAmB;YACxC,IAAI,qBAAqB,GAAG;gBACxB,qBAAqB;YACzB;QACJ;QACA,MAAM,uBAAuB,WAAW,KAAK,CAAC,SAAS,qBAAqB,SAAS;QACrF,MAAM,cAAc,IAAI,CAAC,oBAAoB,CAAC,qBAAqB,MAAM,EAAE;QAC3E,QAAQ,cAAc,cAAc,QAAQ;QAC5C,YAAY,IAAI,CAAC,SAAS,CAAC,CAAC,SAAS,oBAAoB,CAAC,EAAE,GAAG,oBAAoB,CAAC,qBAAqB,MAAM,GAAG,EAAE,IAAI,QAAQ,CAAC,cAAc,UAAU,GAAG,CAAC,QAAQ,IAAI,cAAc,CAAC,CAAC;QACzL,OAAO;YACH,KAAK,oBAAoB,CAAC,EAAE;YAC5B,KAAK,oBAAoB,CAAC,qBAAqB,MAAM,GAAG,EAAE;YAC1D,WAAW;YACX,OAAO;QACX;IACJ;IACA,aAAa,SAAS,IAAI;QACtB,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,IAAI,mBAAmB,CAAC,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,WAAW,EAAE,MAAM;QAC1E,oBAAoB,CAAC,SAAS,KAAK,qBAAqB,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3E,OAAO,cAAc,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,kBAAkB,KAAK,cAAc,QAAQ;IAC/F;IACA,UAAU,SAAS,GAAG,EAAE,GAAG;QACvB,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,cAAc,IAAI,CAAC,oBAAoB;QAC7C,MAAM,cAAc,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI;QAC5C,IAAI,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe;QACpE,IAAI,WAAW,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe;QACpE,IAAI,SAAS,UAAU;YACnB,WAAW,cAAc,MAAM,GAAG,YAAY,GAAG,GAAG,YAAY,GAAG;QACvE;QACA,IAAI,SAAS,UAAU;YACnB,WAAW,cAAc,MAAM,GAAG,YAAY,GAAG,GAAG,YAAY,GAAG;QACvE;QACA,OAAO,IAAI,CAAC,YAAY,GAAG,KAAK,GAAG,CAAC,WAAW;IACnD;IACA,SAAS,SAAS,KAAK;QACnB,OAAO,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,SAAS,IAAI,CAAC,mBAAmB,CAAC,MAAM,OAAO,GAAG,IAAI,IAAI;IAC/E;IACA,iBAAiB;IACjB,IAAI,SAAS,KAAK,EAAE,SAAS;QACzB,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,gBAAgB,IAAI,CAAC,mBAAmB,CAAC,SAAS,SAAS,KAAK,MAAM,QAAQ,KAAK,IAAI,MAAM,OAAO,GAAG;QAC7G,MAAM,kBAAkB,cAAc,eAAe,IAAI;QACzD,MAAM,aAAa,gBAAgB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,EAAE,IAAI,kBAAkB,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI,YAAY;QAC1I,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,cAAc,QAAQ,GAAG;IACpE;IACA,MAAM,SAAS,QAAQ;QACnB,IAAI,YAAY,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QACjF,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,aAAa,cAAc,UAAU;QAC3C,MAAM,aAAa,IAAI,CAAC,iBAAiB,IAAI,IAAI,CAAC,WAAW;QAC7D,MAAM,mBAAmB,WAAW,MAAM;QAC1C,MAAM,gBAAgB,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,KAAK;QACjD,IAAI,SAAS,MAAM,CAAC,WAAW,UAAU,IAAI,cAAc,QAAQ,GAAG,gBAAgB,KAAK,KAAK;QAChG,IAAI,UAAU,kBAAkB;YAC5B,SAAS,mBAAmB;QAChC;QACA,IAAI,SAAS,GAAG;YACZ,SAAS;QACb;QACA,IAAI,cAAc,MAAM,EAAE;YACtB,SAAS,mBAAmB,SAAS;QACzC;QACA,OAAO,UAAU,CAAC,OAAO;IAC7B;IACA,MAAM;QACF,OAAO;IACX;IACA,SAAS;IACT,kBAAkB;IAClB,wBAAuB,OAAO,EAAE,WAAW;QACvC,MAAM,aAAa,IAAI,CAAC,WAAW;QACnC,MAAM,kBAAkB,WAAW,OAAO,CAAC,YAAY,UAAU;QACjE,MAAM,kBAAkB,WAAW,OAAO,CAAC,YAAY,UAAU;QACjE,MAAM,aAAa,kBAAkB,UAAU;QAC/C,MAAM,WAAW,kBAAkB,UAAU;QAC7C,IAAI,UAAU,CAAC,WAAW,EAAE;YACxB,OAAO;gBAAC,YAAY,UAAU;gBAAE,UAAU,CAAC,WAAW;aAAC;QAC3D,OAAO;YACH,OAAO;gBAAC,UAAU,CAAC,SAAS;gBAAE,YAAY,UAAU;aAAC;QACzD;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2188, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/translators/interval_translator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/translators/interval_translator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    isNumeric as isNumber,\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nconst floor = Math.floor;\r\nimport {\r\n    adjust\r\n} from \"../../core/utils/math\";\r\nexport default {\r\n    _intervalize: function(value, interval) {\r\n        if (!isDefined(value)) {\r\n            return\r\n        }\r\n        if (\"datetime\" === this._businessRange.dataType) {\r\n            if (isNumber(value)) {\r\n                value = new Date(value)\r\n            } else {\r\n                value = new Date(value.getTime())\r\n            }\r\n            value = dateUtils.correctDateWithUnitBeginning(value, interval, null, this._options.firstDayOfWeek)\r\n        } else {\r\n            value = adjust(floor(adjust(value / interval)) * interval, interval)\r\n        }\r\n        return value\r\n    },\r\n    translate: function(bp, direction, skipRound, interval) {\r\n        const specialValue = this.translateSpecialCase(bp);\r\n        if (isDefined(specialValue)) {\r\n            return Math.round(specialValue)\r\n        }\r\n        interval = interval || this._options.interval;\r\n        if (!this.isValid(bp, interval)) {\r\n            return null\r\n        }\r\n        return this.to(bp, direction, skipRound, interval)\r\n    },\r\n    getInterval: function() {\r\n        return Math.round(this._canvasOptions.ratioOfCanvasRange * (this._businessRange.interval || Math.abs(this._canvasOptions.rangeMax - this._canvasOptions.rangeMin)))\r\n    },\r\n    zoom: function() {},\r\n    getMinScale: function() {},\r\n    getScale: function() {},\r\n    _parse: function(value) {\r\n        return \"datetime\" === this._businessRange.dataType ? new Date(value) : Number(value)\r\n    },\r\n    fromValue: function(value) {\r\n        return this._parse(value)\r\n    },\r\n    toValue: function(value) {\r\n        return this._parse(value)\r\n    },\r\n    isValid: function(value, interval) {\r\n        const that = this;\r\n        const co = that._canvasOptions;\r\n        let rangeMin = co.rangeMin;\r\n        let rangeMax = co.rangeMax;\r\n        interval = interval || that._options.interval;\r\n        if (null === value || isNaN(value)) {\r\n            return false\r\n        }\r\n        value = \"datetime\" === that._businessRange.dataType && isNumber(value) ? new Date(value) : value;\r\n        if (interval !== that._options.interval) {\r\n            rangeMin = that._intervalize(rangeMin, interval);\r\n            rangeMax = that._intervalize(rangeMax, interval)\r\n        }\r\n        if (value.valueOf() < rangeMin || value.valueOf() >= dateUtils.addInterval(rangeMax, interval)) {\r\n            return false\r\n        }\r\n        return true\r\n    },\r\n    to: function(bp, direction, skipRound, interval) {\r\n        interval = interval || this._options.interval;\r\n        const v1 = this._intervalize(bp, interval);\r\n        const v2 = dateUtils.addInterval(v1, interval);\r\n        let res = this._to(v1, skipRound);\r\n        const p2 = this._to(v2, skipRound);\r\n        if (!direction) {\r\n            res = floor((res + p2) / 2)\r\n        } else if (direction > 0) {\r\n            res = p2\r\n        }\r\n        return res\r\n    },\r\n    _to: function(value, skipRound) {\r\n        const co = this._canvasOptions;\r\n        const rMin = co.rangeMinVisible;\r\n        const rMax = co.rangeMaxVisible;\r\n        let offset = value - rMin;\r\n        if (value < rMin) {\r\n            offset = 0\r\n        } else if (value > rMax) {\r\n            offset = dateUtils.addInterval(rMax, this._options.interval) - rMin\r\n        }\r\n        const projectedValue = this._calculateProjection(offset * this._canvasOptions.ratioOfCanvasRange);\r\n        return this._conversionValue(projectedValue, skipRound)\r\n    },\r\n    from: function(position, direction) {\r\n        const origInterval = this._options.interval;\r\n        let interval = origInterval;\r\n        const co = this._canvasOptions;\r\n        const rMin = co.rangeMinVisible;\r\n        const rMax = co.rangeMaxVisible;\r\n        let value;\r\n        if (\"datetime\" === this._businessRange.dataType) {\r\n            interval = dateUtils.dateToMilliseconds(origInterval)\r\n        }\r\n        value = this._calculateUnProjection((position - this._canvasOptions.startPoint) / this._canvasOptions.ratioOfCanvasRange);\r\n        value = this._intervalize(dateUtils.addInterval(value, interval / 2, direction > 0), origInterval);\r\n        if (value < rMin) {\r\n            value = rMin\r\n        } else if (value > rMax) {\r\n            value = rMax\r\n        }\r\n        return value\r\n    },\r\n    _add: function() {\r\n        return NaN\r\n    },\r\n    isValueProlonged: true\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAIA;AAEA;AAAA;;;AADA,MAAM,QAAQ,KAAK,KAAK;;uCAIT;IACX,cAAc,SAAS,KAAK,EAAE,QAAQ;QAClC,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ;YACnB;QACJ;QACA,IAAI,eAAe,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;YAC7C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAQ,AAAD,EAAE,QAAQ;gBACjB,QAAQ,IAAI,KAAK;YACrB,OAAO;gBACH,QAAQ,IAAI,KAAK,MAAM,OAAO;YAClC;YACA,QAAQ,6JAAA,CAAA,UAAS,CAAC,4BAA4B,CAAC,OAAO,UAAU,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc;QACtG,OAAO;YACH,QAAQ,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,aAAa,UAAU;QAC/D;QACA,OAAO;IACX;IACA,WAAW,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ;QAClD,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC;QAC/C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YACzB,OAAO,KAAK,KAAK,CAAC;QACtB;QACA,WAAW,YAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAC7C,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,WAAW;YAC7B,OAAO;QACX;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,WAAW,WAAW;IAC7C;IACA,aAAa;QACT,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,kBAAkB,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,CAAC;IACrK;IACA,MAAM,YAAY;IAClB,aAAa,YAAY;IACzB,UAAU,YAAY;IACtB,QAAQ,SAAS,KAAK;QAClB,OAAO,eAAe,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,KAAK,SAAS,OAAO;IAClF;IACA,WAAW,SAAS,KAAK;QACrB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,SAAS,SAAS,KAAK;QACnB,OAAO,IAAI,CAAC,MAAM,CAAC;IACvB;IACA,SAAS,SAAS,KAAK,EAAE,QAAQ;QAC7B,MAAM,OAAO,IAAI;QACjB,MAAM,KAAK,KAAK,cAAc;QAC9B,IAAI,WAAW,GAAG,QAAQ;QAC1B,IAAI,WAAW,GAAG,QAAQ;QAC1B,WAAW,YAAY,KAAK,QAAQ,CAAC,QAAQ;QAC7C,IAAI,SAAS,SAAS,MAAM,QAAQ;YAChC,OAAO;QACX;QACA,QAAQ,eAAe,KAAK,cAAc,CAAC,QAAQ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAQ,AAAD,EAAE,SAAS,IAAI,KAAK,SAAS;QAC3F,IAAI,aAAa,KAAK,QAAQ,CAAC,QAAQ,EAAE;YACrC,WAAW,KAAK,YAAY,CAAC,UAAU;YACvC,WAAW,KAAK,YAAY,CAAC,UAAU;QAC3C;QACA,IAAI,MAAM,OAAO,KAAK,YAAY,MAAM,OAAO,MAAM,6JAAA,CAAA,UAAS,CAAC,WAAW,CAAC,UAAU,WAAW;YAC5F,OAAO;QACX;QACA,OAAO;IACX;IACA,IAAI,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS,EAAE,QAAQ;QAC3C,WAAW,YAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAC7C,MAAM,KAAK,IAAI,CAAC,YAAY,CAAC,IAAI;QACjC,MAAM,KAAK,6JAAA,CAAA,UAAS,CAAC,WAAW,CAAC,IAAI;QACrC,IAAI,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI;QACvB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,IAAI;QACxB,IAAI,CAAC,WAAW;YACZ,MAAM,MAAM,CAAC,MAAM,EAAE,IAAI;QAC7B,OAAO,IAAI,YAAY,GAAG;YACtB,MAAM;QACV;QACA,OAAO;IACX;IACA,KAAK,SAAS,KAAK,EAAE,SAAS;QAC1B,MAAM,KAAK,IAAI,CAAC,cAAc;QAC9B,MAAM,OAAO,GAAG,eAAe;QAC/B,MAAM,OAAO,GAAG,eAAe;QAC/B,IAAI,SAAS,QAAQ;QACrB,IAAI,QAAQ,MAAM;YACd,SAAS;QACb,OAAO,IAAI,QAAQ,MAAM;YACrB,SAAS,6JAAA,CAAA,UAAS,CAAC,WAAW,CAAC,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI;QACnE;QACA,MAAM,iBAAiB,IAAI,CAAC,oBAAoB,CAAC,SAAS,IAAI,CAAC,cAAc,CAAC,kBAAkB;QAChG,OAAO,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;IACjD;IACA,MAAM,SAAS,QAAQ,EAAE,SAAS;QAC9B,MAAM,eAAe,IAAI,CAAC,QAAQ,CAAC,QAAQ;QAC3C,IAAI,WAAW;QACf,MAAM,KAAK,IAAI,CAAC,cAAc;QAC9B,MAAM,OAAO,GAAG,eAAe;QAC/B,MAAM,OAAO,GAAG,eAAe;QAC/B,IAAI;QACJ,IAAI,eAAe,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE;YAC7C,WAAW,6JAAA,CAAA,UAAS,CAAC,kBAAkB,CAAC;QAC5C;QACA,QAAQ,IAAI,CAAC,sBAAsB,CAAC,CAAC,WAAW,IAAI,CAAC,cAAc,CAAC,UAAU,IAAI,IAAI,CAAC,cAAc,CAAC,kBAAkB;QACxH,QAAQ,IAAI,CAAC,YAAY,CAAC,6JAAA,CAAA,UAAS,CAAC,WAAW,CAAC,OAAO,WAAW,GAAG,YAAY,IAAI;QACrF,IAAI,QAAQ,MAAM;YACd,QAAQ;QACZ,OAAO,IAAI,QAAQ,MAAM;YACrB,QAAQ;QACZ;QACA,OAAO;IACX;IACA,MAAM;QACF,OAAO;IACX;IACA,kBAAkB;AACtB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2323, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/translators/datetime_translator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/translators/datetime_translator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport dateUtils from \"../../core/utils/date\";\r\n\r\nfunction parse(value) {\r\n    return null !== value ? new Date(value) : value\r\n}\r\nexport default {\r\n    fromValue: parse,\r\n    toValue: parse,\r\n    _add: dateUtils.addDateInterval,\r\n    convert: dateUtils.dateToMilliseconds\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;AAEA,SAAS,MAAM,KAAK;IAChB,OAAO,SAAS,QAAQ,IAAI,KAAK,SAAS;AAC9C;uCACe;IACX,WAAW;IACX,SAAS;IACT,MAAM,6JAAA,CAAA,UAAS,CAAC,eAAe;IAC/B,SAAS,6JAAA,CAAA,UAAS,CAAC,kBAAkB;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2348, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/translators/logarithmic_translator.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/translators/logarithmic_translator.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    raiseToExt as raiseTo,\r\n    getLogExt as getLog\r\n} from \"../core/utils\";\r\nimport {\r\n    isDefined\r\n} from \"../../core/utils/type\";\r\nexport default {\r\n    fromValue: function(value) {\r\n        return null !== value ? getLog(value, this._canvasOptions.base, this._businessRange.allowNegatives, this._businessRange.linearThreshold) : value\r\n    },\r\n    toValue: function(value) {\r\n        return null !== value ? raiseTo(value, this._canvasOptions.base, this._businessRange.allowNegatives, this._businessRange.linearThreshold) : value\r\n    },\r\n    getMinBarSize: function(minBarSize) {\r\n        const visibleArea = this.getCanvasVisibleArea();\r\n        const minValue = this.from(visibleArea.min + minBarSize);\r\n        const canvasOptions = this._canvasOptions;\r\n        const startValue = this.fromValue(this.from(visibleArea.min));\r\n        const endValue = this.fromValue(minValue ?? this.from(visibleArea.max));\r\n        const value = Math.abs(startValue - endValue);\r\n        return Math.pow(canvasOptions.base, value)\r\n    },\r\n    checkMinBarSize: function(initialValue, minShownValue, stackValue) {\r\n        const canvasOptions = this._canvasOptions;\r\n        const prevValue = stackValue ? stackValue - initialValue : 0;\r\n        const baseMethod = this.constructor.prototype.checkMinBarSize;\r\n        let minBarSize;\r\n        let updateValue;\r\n        if (isDefined(minShownValue) && prevValue > 0) {\r\n            minBarSize = baseMethod(this.fromValue(stackValue / prevValue), this.fromValue(minShownValue) - canvasOptions.rangeMinVisible);\r\n            updateValue = Math.pow(canvasOptions.base, this.fromValue(prevValue) + minBarSize) - prevValue\r\n        } else {\r\n            updateValue = baseMethod(initialValue, minShownValue)\r\n        }\r\n        return updateValue\r\n    }\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAIA;AAAA;;;uCAGe;IACX,WAAW,SAAS,KAAK;QACrB,OAAO,SAAS,QAAQ,CAAA,GAAA,4JAAA,CAAA,YAAM,AAAD,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,eAAe,IAAI;IAC/I;IACA,SAAS,SAAS,KAAK;QACnB,OAAO,SAAS,QAAQ,CAAA,GAAA,4JAAA,CAAA,aAAO,AAAD,EAAE,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,EAAE,IAAI,CAAC,cAAc,CAAC,cAAc,EAAE,IAAI,CAAC,cAAc,CAAC,eAAe,IAAI;IAChJ;IACA,eAAe,SAAS,UAAU;QAC9B,MAAM,cAAc,IAAI,CAAC,oBAAoB;QAC7C,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG;QAC7C,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,aAAa,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG;QAC3D,MAAM,WAAW,IAAI,CAAC,SAAS,CAAC,qBAAA,sBAAA,WAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG;QACrE,MAAM,QAAQ,KAAK,GAAG,CAAC,aAAa;QACpC,OAAO,KAAK,GAAG,CAAC,cAAc,IAAI,EAAE;IACxC;IACA,iBAAiB,SAAS,YAAY,EAAE,aAAa,EAAE,UAAU;QAC7D,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,YAAY,aAAa,aAAa,eAAe;QAC3D,MAAM,aAAa,IAAI,CAAC,WAAW,CAAC,SAAS,CAAC,eAAe;QAC7D,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB,YAAY,GAAG;YAC3C,aAAa,WAAW,IAAI,CAAC,SAAS,CAAC,aAAa,YAAY,IAAI,CAAC,SAAS,CAAC,iBAAiB,cAAc,eAAe;YAC7H,cAAc,KAAK,GAAG,CAAC,cAAc,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,aAAa,cAAc;QACzF,OAAO;YACH,cAAc,WAAW,cAAc;QAC3C;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2398, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/translators/translator2d.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/translators/translator2d.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend\r\n} from \"../../core/utils/extend\";\r\nimport {\r\n    each\r\n} from \"../../core/utils/iterator\";\r\nimport {\r\n    Range\r\n} from \"./range\";\r\nimport categoryTranslator from \"./category_translator\";\r\nimport intervalTranslator from \"./interval_translator\";\r\nimport datetimeTranslator from \"./datetime_translator\";\r\nimport logarithmicTranslator from \"./logarithmic_translator\";\r\nimport {\r\n    getLogExt as getLog,\r\n    getPower,\r\n    raiseToExt,\r\n    getCategoriesInfo\r\n} from \"../core/utils\";\r\nimport {\r\n    isDefined,\r\n    isDate\r\n} from \"../../core/utils/type\";\r\nimport {\r\n    adjust\r\n} from \"../../core/utils/math\";\r\nimport dateUtils from \"../../core/utils/date\";\r\nconst _abs = Math.abs;\r\nconst CANVAS_PROP = [\"width\", \"height\", \"left\", \"top\", \"bottom\", \"right\"];\r\nconst dummyTranslator = {\r\n    to(value) {\r\n        const coord = this._canvasOptions.startPoint + (this._options.conversionValue ? value : Math.round(value));\r\n        return coord > this._canvasOptions.endPoint ? this._canvasOptions.endPoint : coord\r\n    },\r\n    from(value) {\r\n        return value - this._canvasOptions.startPoint\r\n    }\r\n};\r\nconst validateCanvas = function(canvas) {\r\n    each(CANVAS_PROP, (function(_, prop) {\r\n        canvas[prop] = parseInt(canvas[prop]) || 0\r\n    }));\r\n    return canvas\r\n};\r\nconst makeCategoriesToPoints = function(categories) {\r\n    const categoriesToPoints = {};\r\n    categories.forEach((function(item, i) {\r\n        categoriesToPoints[item.valueOf()] = i\r\n    }));\r\n    return categoriesToPoints\r\n};\r\nconst validateBusinessRange = function(businessRange) {\r\n    if (!(businessRange instanceof Range)) {\r\n        businessRange = new Range(businessRange)\r\n    }\r\n\r\n    function validate(valueSelector, baseValueSelector) {\r\n        if (!isDefined(businessRange[valueSelector]) && isDefined(businessRange[baseValueSelector])) {\r\n            businessRange[valueSelector] = businessRange[baseValueSelector]\r\n        }\r\n    }\r\n    validate(\"minVisible\", \"min\");\r\n    validate(\"maxVisible\", \"max\");\r\n    return businessRange\r\n};\r\n\r\nfunction prepareBreaks(breaks, range) {\r\n    const transform = \"logarithmic\" === range.axisType ? function(value) {\r\n        return getLog(value, range.base)\r\n    } : function(value) {\r\n        return value\r\n    };\r\n    const array = [];\r\n    let br;\r\n    let transformFrom;\r\n    let transformTo;\r\n    let i;\r\n    const length = breaks.length;\r\n    let sum = 0;\r\n    for (i = 0; i < length; i++) {\r\n        br = breaks[i];\r\n        transformFrom = transform(br.from);\r\n        transformTo = transform(br.to);\r\n        sum += transformTo - transformFrom;\r\n        array.push({\r\n            trFrom: transformFrom,\r\n            trTo: transformTo,\r\n            from: br.from,\r\n            to: br.to,\r\n            length: sum,\r\n            cumulativeWidth: br.cumulativeWidth\r\n        })\r\n    }\r\n    return array\r\n}\r\n\r\nfunction getCanvasBounds(range) {\r\n    let min = range.min;\r\n    let max = range.max;\r\n    let minVisible = range.minVisible;\r\n    let maxVisible = range.maxVisible;\r\n    const isLogarithmic = \"logarithmic\" === range.axisType;\r\n    if (isLogarithmic) {\r\n        maxVisible = getLog(maxVisible, range.base, range.allowNegatives, range.linearThreshold);\r\n        minVisible = getLog(minVisible, range.base, range.allowNegatives, range.linearThreshold);\r\n        min = getLog(min, range.base, range.allowNegatives, range.linearThreshold);\r\n        max = getLog(max, range.base, range.allowNegatives, range.linearThreshold)\r\n    }\r\n    return {\r\n        base: range.base,\r\n        rangeMin: min,\r\n        rangeMax: max,\r\n        rangeMinVisible: minVisible,\r\n        rangeMaxVisible: maxVisible\r\n    }\r\n}\r\n\r\nfunction getCheckingMethodsAboutBreaks(inverted) {\r\n    return {\r\n        isStartSide: !inverted ? function(pos, breaks, start, end) {\r\n            return pos < breaks[0][start]\r\n        } : function(pos, breaks, start, end) {\r\n            return pos <= breaks[breaks.length - 1][end]\r\n        },\r\n        isEndSide: !inverted ? function(pos, breaks, start, end) {\r\n            return pos >= breaks[breaks.length - 1][end]\r\n        } : function(pos, breaks, start, end) {\r\n            return pos > breaks[0][start]\r\n        },\r\n        isInBreak: !inverted ? function(pos, br, start, end) {\r\n            return pos >= br[start] && pos < br[end]\r\n        } : function(pos, br, start, end) {\r\n            return pos > br[end] && pos <= br[start]\r\n        },\r\n        isBetweenBreaks: !inverted ? function(pos, br, prevBreak, start, end) {\r\n            return pos < br[start] && pos >= prevBreak[end]\r\n        } : function(pos, br, prevBreak, start, end) {\r\n            return pos >= br[end] && pos < prevBreak[start]\r\n        },\r\n        getLength: !inverted ? function(br) {\r\n            return br.length\r\n        } : function(br, lastBreak) {\r\n            return lastBreak.length - br.length\r\n        },\r\n        getBreaksSize: !inverted ? function(br) {\r\n            return br.cumulativeWidth\r\n        } : function(br, lastBreak) {\r\n            return lastBreak.cumulativeWidth - br.cumulativeWidth\r\n        }\r\n    }\r\n}\r\nconst _Translator2d = function(businessRange, canvas, options) {\r\n    this.update(businessRange, canvas, options)\r\n};\r\n_Translator2d.prototype = {\r\n    constructor: _Translator2d,\r\n    reinit: function() {\r\n        const that = this;\r\n        const options = that._options;\r\n        const range = that._businessRange;\r\n        const categories = range.categories || [];\r\n        let script = {};\r\n        const canvasOptions = that._prepareCanvasOptions();\r\n        const visibleCategories = getCategoriesInfo(categories, range.minVisible, range.maxVisible).categories;\r\n        const categoriesLength = visibleCategories.length;\r\n        if (range.isEmpty()) {\r\n            script = dummyTranslator\r\n        } else {\r\n            switch (range.axisType) {\r\n                case \"logarithmic\":\r\n                    script = logarithmicTranslator;\r\n                    break;\r\n                case \"semidiscrete\":\r\n                    script = intervalTranslator;\r\n                    canvasOptions.ratioOfCanvasRange = canvasOptions.canvasLength / (dateUtils.addInterval(canvasOptions.rangeMaxVisible, options.interval) - canvasOptions.rangeMinVisible);\r\n                    break;\r\n                case \"discrete\":\r\n                    script = categoryTranslator;\r\n                    that._categories = categories;\r\n                    canvasOptions.interval = that._getDiscreteInterval(options.addSpiderCategory ? categoriesLength + 1 : categoriesLength, canvasOptions);\r\n                    that._categoriesToPoints = makeCategoriesToPoints(categories);\r\n                    if (categoriesLength) {\r\n                        canvasOptions.startPointIndex = that._categoriesToPoints[visibleCategories[0].valueOf()];\r\n                        that.visibleCategories = visibleCategories\r\n                    }\r\n                    break;\r\n                default:\r\n                    if (\"datetime\" === range.dataType) {\r\n                        script = datetimeTranslator\r\n                    }\r\n            }\r\n        }(that._oldMethods || []).forEach((function(methodName) {\r\n            delete that[methodName]\r\n        }));\r\n        that._oldMethods = Object.keys(script);\r\n        extend(that, script);\r\n        that._conversionValue = options.conversionValue ? value => value : (value, skipRound) => skipRound ? value : Math.round(value);\r\n        that.sc = {};\r\n        that._checkingMethodsAboutBreaks = [getCheckingMethodsAboutBreaks(false), getCheckingMethodsAboutBreaks(that.isInverted())];\r\n        that._translateBreaks();\r\n        that._calculateSpecialValues()\r\n    },\r\n    _translateBreaks: function() {\r\n        const breaks = this._breaks;\r\n        const size = this._options.breaksSize;\r\n        let i;\r\n        let b;\r\n        let end;\r\n        let length;\r\n        if (void 0 === breaks) {\r\n            return\r\n        }\r\n        for (i = 0, length = breaks.length; i < length; i++) {\r\n            b = breaks[i];\r\n            end = this.translate(b.to);\r\n            b.end = end;\r\n            b.start = !b.gapSize ? !this.isInverted() ? end - size : end + size : end\r\n        }\r\n    },\r\n    _checkValueAboutBreaks: function(breaks, pos, start, end, methods) {\r\n        let i;\r\n        let length;\r\n        let prop = {\r\n            length: 0,\r\n            breaksSize: void 0,\r\n            inBreak: false\r\n        };\r\n        let br;\r\n        let prevBreak;\r\n        const lastBreak = breaks[breaks.length - 1];\r\n        if (methods.isStartSide(pos, breaks, start, end)) {\r\n            return prop\r\n        } else if (methods.isEndSide(pos, breaks, start, end)) {\r\n            return {\r\n                length: lastBreak.length,\r\n                breaksSize: lastBreak.cumulativeWidth,\r\n                inBreak: false\r\n            }\r\n        }\r\n        for (i = 0, length = breaks.length; i < length; i++) {\r\n            br = breaks[i];\r\n            prevBreak = breaks[i - 1];\r\n            if (methods.isInBreak(pos, br, start, end)) {\r\n                prop.inBreak = true;\r\n                prop.break = br;\r\n                break\r\n            }\r\n            if (prevBreak && methods.isBetweenBreaks(pos, br, prevBreak, start, end)) {\r\n                prop = {\r\n                    length: methods.getLength(prevBreak, lastBreak),\r\n                    breaksSize: methods.getBreaksSize(prevBreak, lastBreak),\r\n                    inBreak: false\r\n                };\r\n                break\r\n            }\r\n        }\r\n        return prop\r\n    },\r\n    isInverted: function() {\r\n        return !(this._options.isHorizontal ^ this._businessRange.invert)\r\n    },\r\n    _getDiscreteInterval: function(categoriesLength, canvasOptions) {\r\n        const correctedCategoriesCount = categoriesLength - (this._options.stick ? 1 : 0);\r\n        return correctedCategoriesCount > 0 ? canvasOptions.canvasLength / correctedCategoriesCount : canvasOptions.canvasLength\r\n    },\r\n    _prepareCanvasOptions() {\r\n        const businessRange = this._businessRange;\r\n        const canvasOptions = this._canvasOptions = getCanvasBounds(businessRange);\r\n        const canvas = this._canvas;\r\n        const breaks = this._breaks;\r\n        let length;\r\n        canvasOptions.startPadding = canvas.startPadding || 0;\r\n        canvasOptions.endPadding = canvas.endPadding || 0;\r\n        if (this._options.isHorizontal) {\r\n            canvasOptions.startPoint = canvas.left + canvasOptions.startPadding;\r\n            length = canvas.width;\r\n            canvasOptions.endPoint = canvas.width - canvas.right - canvasOptions.endPadding;\r\n            canvasOptions.invert = businessRange.invert\r\n        } else {\r\n            canvasOptions.startPoint = canvas.top + canvasOptions.startPadding;\r\n            length = canvas.height;\r\n            canvasOptions.endPoint = canvas.height - canvas.bottom - canvasOptions.endPadding;\r\n            canvasOptions.invert = !businessRange.invert\r\n        }\r\n        this.canvasLength = canvasOptions.canvasLength = canvasOptions.endPoint - canvasOptions.startPoint;\r\n        canvasOptions.rangeDoubleError = Math.pow(10, getPower(canvasOptions.rangeMax - canvasOptions.rangeMin) - getPower(length) - 2);\r\n        canvasOptions.ratioOfCanvasRange = canvasOptions.canvasLength / (canvasOptions.rangeMaxVisible - canvasOptions.rangeMinVisible);\r\n        if (void 0 !== breaks) {\r\n            const visibleRangeLength = canvasOptions.rangeMaxVisible - canvasOptions.rangeMinVisible - breaks[breaks.length - 1].length;\r\n            if (0 !== visibleRangeLength) {\r\n                canvasOptions.ratioOfCanvasRange = (canvasOptions.canvasLength - breaks[breaks.length - 1].cumulativeWidth) / visibleRangeLength\r\n            }\r\n        }\r\n        return canvasOptions\r\n    },\r\n    updateCanvas: function(canvas) {\r\n        this._canvas = validateCanvas(canvas);\r\n        this.reinit()\r\n    },\r\n    updateBusinessRange: function(businessRange) {\r\n        const breaks = businessRange.breaks || [];\r\n        this._userBreaks = businessRange.userBreaks || [];\r\n        this._businessRange = validateBusinessRange(businessRange);\r\n        this._breaks = breaks.length ? prepareBreaks(breaks, this._businessRange) : void 0;\r\n        this.reinit()\r\n    },\r\n    update: function(businessRange, canvas, options) {\r\n        this._options = extend(this._options || {}, options);\r\n        this._canvas = validateCanvas(canvas);\r\n        this.updateBusinessRange(businessRange)\r\n    },\r\n    getBusinessRange: function() {\r\n        return this._businessRange\r\n    },\r\n    getEventScale: function(zoomEvent) {\r\n        return zoomEvent.deltaScale || 1\r\n    },\r\n    getCanvasVisibleArea: function() {\r\n        return {\r\n            min: this._canvasOptions.startPoint,\r\n            max: this._canvasOptions.endPoint\r\n        }\r\n    },\r\n    _calculateSpecialValues: function() {\r\n        const that = this;\r\n        const canvasOptions = that._canvasOptions;\r\n        const startPoint = canvasOptions.startPoint - canvasOptions.startPadding;\r\n        const endPoint = canvasOptions.endPoint + canvasOptions.endPadding;\r\n        const range = that._businessRange;\r\n        const minVisible = range.minVisible;\r\n        const maxVisible = range.maxVisible;\r\n        const canvas_position_center_middle = startPoint + canvasOptions.canvasLength / 2;\r\n        let canvas_position_default;\r\n        if (minVisible < 0 && maxVisible > 0 && minVisible !== maxVisible) {\r\n            canvas_position_default = that.translate(0, 1)\r\n        }\r\n        if (!isDefined(canvas_position_default)) {\r\n            const invert = range.invert ^ (minVisible < 0 && maxVisible <= 0);\r\n            if (that._options.isHorizontal) {\r\n                canvas_position_default = invert ? endPoint : startPoint\r\n            } else {\r\n                canvas_position_default = invert ? startPoint : endPoint\r\n            }\r\n        }\r\n        that.sc = {\r\n            canvas_position_default: canvas_position_default,\r\n            canvas_position_left: startPoint,\r\n            canvas_position_top: startPoint,\r\n            canvas_position_center: canvas_position_center_middle,\r\n            canvas_position_middle: canvas_position_center_middle,\r\n            canvas_position_right: endPoint,\r\n            canvas_position_bottom: endPoint,\r\n            canvas_position_start: canvasOptions.invert ? endPoint : startPoint,\r\n            canvas_position_end: canvasOptions.invert ? startPoint : endPoint\r\n        }\r\n    },\r\n    translateSpecialCase(value) {\r\n        return this.sc[value]\r\n    },\r\n    _calculateProjection: function(distance) {\r\n        const canvasOptions = this._canvasOptions;\r\n        return canvasOptions.invert ? canvasOptions.endPoint - distance : canvasOptions.startPoint + distance\r\n    },\r\n    _calculateUnProjection: function(distance) {\r\n        const canvasOptions = this._canvasOptions;\r\n        \"datetime\" === this._businessRange.dataType && (distance = Math.round(distance));\r\n        return canvasOptions.invert ? canvasOptions.rangeMaxVisible.valueOf() - distance : canvasOptions.rangeMinVisible.valueOf() + distance\r\n    },\r\n    getMinBarSize: function(minBarSize) {\r\n        const visibleArea = this.getCanvasVisibleArea();\r\n        const minValue = this.from(visibleArea.min + minBarSize);\r\n        return _abs(this.from(visibleArea.min) - (!isDefined(minValue) ? this.from(visibleArea.max) : minValue))\r\n    },\r\n    checkMinBarSize: function(value, minShownValue) {\r\n        return _abs(value) < minShownValue ? value >= 0 ? minShownValue : -minShownValue : value\r\n    },\r\n    translate(bp, direction, skipRound) {\r\n        const specialValue = this.translateSpecialCase(bp);\r\n        if (isDefined(specialValue)) {\r\n            return Math.round(specialValue)\r\n        }\r\n        if (isNaN(bp)) {\r\n            return null\r\n        }\r\n        return this.to(bp, direction, skipRound)\r\n    },\r\n    getInterval: function(interval) {\r\n        const canvasOptions = this._canvasOptions;\r\n        interval = interval ?? this._businessRange.interval;\r\n        if (interval) {\r\n            return Math.round(canvasOptions.ratioOfCanvasRange * interval)\r\n        }\r\n        return Math.round(canvasOptions.endPoint - canvasOptions.startPoint)\r\n    },\r\n    zoom(translate, scale, wholeRange) {\r\n        const canvasOptions = this._canvasOptions;\r\n        if (canvasOptions.rangeMinVisible.valueOf() === canvasOptions.rangeMaxVisible.valueOf() && 0 !== translate) {\r\n            return this.zoomZeroLengthRange(translate, scale)\r\n        }\r\n        const startPoint = canvasOptions.startPoint;\r\n        const endPoint = canvasOptions.endPoint;\r\n        const isInverted = this.isInverted();\r\n        let newStart = (startPoint + translate) / scale;\r\n        let newEnd = (endPoint + translate) / scale;\r\n        wholeRange = wholeRange || {};\r\n        const minPoint = this.to(isInverted ? wholeRange.endValue : wholeRange.startValue);\r\n        const maxPoint = this.to(isInverted ? wholeRange.startValue : wholeRange.endValue);\r\n        let min;\r\n        let max;\r\n        if (minPoint > newStart) {\r\n            newEnd -= newStart - minPoint;\r\n            newStart = minPoint;\r\n            min = isInverted ? wholeRange.endValue : wholeRange.startValue\r\n        }\r\n        if (maxPoint < newEnd) {\r\n            newStart -= newEnd - maxPoint;\r\n            newEnd = maxPoint;\r\n            max = isInverted ? wholeRange.startValue : wholeRange.endValue\r\n        }\r\n        if (maxPoint - minPoint < newEnd - newStart) {\r\n            newStart = minPoint;\r\n            newEnd = maxPoint\r\n        }\r\n        translate = (endPoint - startPoint) * newStart / (newEnd - newStart) - startPoint;\r\n        scale = (startPoint + translate) / newStart || 1;\r\n        min = isDefined(min) ? min : adjust(this.from(newStart, 1));\r\n        max = isDefined(max) ? max : adjust(this.from(newEnd, -1));\r\n        if (scale <= 1) {\r\n            min = this._correctValueAboutBreaks(min, 1 === scale ? translate : -1);\r\n            max = this._correctValueAboutBreaks(max, 1 === scale ? translate : 1)\r\n        }\r\n        if (min > max) {\r\n            min = min > wholeRange.endValue ? wholeRange.endValue : min;\r\n            max = max < wholeRange.startValue ? wholeRange.startValue : max\r\n        } else {\r\n            min = min < wholeRange.startValue ? wholeRange.startValue : min;\r\n            max = max > wholeRange.endValue ? wholeRange.endValue : max\r\n        }\r\n        return {\r\n            min: min,\r\n            max: max,\r\n            translate: adjust(translate),\r\n            scale: adjust(scale)\r\n        }\r\n    },\r\n    _correctValueAboutBreaks(value, direction) {\r\n        const br = this._userBreaks.filter((br => value >= br.from && value <= br.to));\r\n        if (br.length) {\r\n            return direction > 0 ? br[0].to : br[0].from\r\n        } else {\r\n            return value\r\n        }\r\n    },\r\n    zoomZeroLengthRange(translate, scale) {\r\n        const canvasOptions = this._canvasOptions;\r\n        const min = canvasOptions.rangeMin;\r\n        const max = canvasOptions.rangeMax;\r\n        const correction = (max.valueOf() !== min.valueOf() ? max.valueOf() - min.valueOf() : _abs(canvasOptions.rangeMinVisible.valueOf() - min.valueOf())) / canvasOptions.canvasLength;\r\n        const isDateTime = isDate(max) || isDate(min);\r\n        const isLogarithmic = \"logarithmic\" === this._businessRange.axisType;\r\n        let newMin = canvasOptions.rangeMinVisible.valueOf() - correction;\r\n        let newMax = canvasOptions.rangeMaxVisible.valueOf() + correction;\r\n        newMin = isLogarithmic ? adjust(raiseToExt(newMin, canvasOptions.base)) : isDateTime ? new Date(newMin) : newMin;\r\n        newMax = isLogarithmic ? adjust(raiseToExt(newMax, canvasOptions.base)) : isDateTime ? new Date(newMax) : newMax;\r\n        return {\r\n            min: newMin,\r\n            max: newMax,\r\n            translate: translate,\r\n            scale: scale\r\n        }\r\n    },\r\n    getMinScale: function(zoom) {\r\n        const {\r\n            dataType: dataType,\r\n            interval: interval\r\n        } = this._businessRange;\r\n        if (\"datetime\" === dataType && 1 === interval) {\r\n            return this.getDateTimeMinScale(zoom)\r\n        }\r\n        return zoom ? 1.1 : .9\r\n    },\r\n    getDateTimeMinScale(zoom) {\r\n        const canvasOptions = this._canvasOptions;\r\n        let length = canvasOptions.canvasLength / canvasOptions.ratioOfCanvasRange;\r\n        length += (parseInt(.1 * length) || 1) * (zoom ? -2 : 2);\r\n        return canvasOptions.canvasLength / (Math.max(length, 1) * canvasOptions.ratioOfCanvasRange)\r\n    },\r\n    getScale: function(val1, val2) {\r\n        const canvasOptions = this._canvasOptions;\r\n        if (canvasOptions.rangeMax === canvasOptions.rangeMin) {\r\n            return 1\r\n        }\r\n        val1 = isDefined(val1) ? this.fromValue(val1) : canvasOptions.rangeMin;\r\n        val2 = isDefined(val2) ? this.fromValue(val2) : canvasOptions.rangeMax;\r\n        return (canvasOptions.rangeMax - canvasOptions.rangeMin) / Math.abs(val1 - val2)\r\n    },\r\n    isValid: function(value) {\r\n        const co = this._canvasOptions;\r\n        value = this.fromValue(value);\r\n        return null !== value && !isNaN(value) && value.valueOf() + co.rangeDoubleError >= co.rangeMin && value.valueOf() - co.rangeDoubleError <= co.rangeMax\r\n    },\r\n    getCorrectValue: function(value, direction) {\r\n        const that = this;\r\n        const breaks = that._breaks;\r\n        let prop;\r\n        value = that.fromValue(value);\r\n        if (that._breaks) {\r\n            prop = that._checkValueAboutBreaks(breaks, value, \"trFrom\", \"trTo\", that._checkingMethodsAboutBreaks[0]);\r\n            if (true === prop.inBreak) {\r\n                return that.toValue(direction > 0 ? prop.break.trTo : prop.break.trFrom)\r\n            }\r\n        }\r\n        return that.toValue(value)\r\n    },\r\n    to: function(bp, direction, skipRound) {\r\n        const range = this.getBusinessRange();\r\n        if (isDefined(range.maxVisible) && isDefined(range.minVisible) && range.maxVisible.valueOf() === range.minVisible.valueOf()) {\r\n            if (!isDefined(bp) || range.maxVisible.valueOf() !== bp.valueOf()) {\r\n                return null\r\n            }\r\n            return this.translateSpecialCase(0 === bp && this._options.shiftZeroValue ? \"canvas_position_default\" : \"canvas_position_middle\")\r\n        }\r\n        bp = this.fromValue(bp);\r\n        const that = this;\r\n        const canvasOptions = that._canvasOptions;\r\n        const breaks = that._breaks;\r\n        let prop = {\r\n            length: 0\r\n        };\r\n        let commonBreakSize = 0;\r\n        if (void 0 !== breaks) {\r\n            prop = that._checkValueAboutBreaks(breaks, bp, \"trFrom\", \"trTo\", that._checkingMethodsAboutBreaks[0]);\r\n            commonBreakSize = isDefined(prop.breaksSize) ? prop.breaksSize : 0\r\n        }\r\n        if (true === prop.inBreak) {\r\n            if (direction > 0) {\r\n                return prop.break.start\r\n            } else if (direction < 0) {\r\n                return prop.break.end\r\n            } else {\r\n                return null\r\n            }\r\n        }\r\n        return that._conversionValue(that._calculateProjection((bp - canvasOptions.rangeMinVisible - prop.length) * canvasOptions.ratioOfCanvasRange + commonBreakSize), skipRound)\r\n    },\r\n    from: function(pos, direction) {\r\n        const that = this;\r\n        const breaks = that._breaks;\r\n        let prop = {\r\n            length: 0\r\n        };\r\n        const canvasOptions = that._canvasOptions;\r\n        const startPoint = canvasOptions.startPoint;\r\n        let commonBreakSize = 0;\r\n        if (void 0 !== breaks) {\r\n            prop = that._checkValueAboutBreaks(breaks, pos, \"start\", \"end\", that._checkingMethodsAboutBreaks[1]);\r\n            commonBreakSize = isDefined(prop.breaksSize) ? prop.breaksSize : 0\r\n        }\r\n        if (true === prop.inBreak) {\r\n            if (direction > 0) {\r\n                return that.toValue(prop.break.trTo)\r\n            } else if (direction < 0) {\r\n                return that.toValue(prop.break.trFrom)\r\n            } else {\r\n                return null\r\n            }\r\n        }\r\n        return that.toValue(that._calculateUnProjection((pos - startPoint - commonBreakSize) / canvasOptions.ratioOfCanvasRange + prop.length))\r\n    },\r\n    isValueProlonged: false,\r\n    getRange: function() {\r\n        return [this.toValue(this._canvasOptions.rangeMin), this.toValue(this._canvasOptions.rangeMax)]\r\n    },\r\n    getScreenRange: function() {\r\n        return [this._canvasOptions.startPoint, this._canvasOptions.endPoint]\r\n    },\r\n    add: function(value, diff, dir) {\r\n        return this._add(value, diff, (this._businessRange.invert ? -1 : 1) * dir)\r\n    },\r\n    _add: function(value, diff, coeff) {\r\n        return this.toValue(this.fromValue(value) + diff * coeff)\r\n    },\r\n    fromValue: function(value) {\r\n        return null !== value ? Number(value) : null\r\n    },\r\n    toValue: function(value) {\r\n        return null !== value ? Number(value) : null\r\n    },\r\n    ratioOfCanvasRange() {\r\n        return this._canvasOptions.ratioOfCanvasRange\r\n    },\r\n    convert: value => value,\r\n    getRangeByMinZoomValue(minZoom, visualRange) {\r\n        if (visualRange.minVisible + minZoom <= this._businessRange.max) {\r\n            return [visualRange.minVisible, visualRange.minVisible + minZoom]\r\n        } else {\r\n            return [visualRange.maxVisible - minZoom, visualRange.maxVisible]\r\n        }\r\n    }\r\n};\r\nexport {\r\n    _Translator2d as Translator2D\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;AAAA;AAGA;AAAA;AAGA;AAGA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAIA;AAAA;AAGA;;;;;;;;;;;;AACA,MAAM,OAAO,KAAK,GAAG;AACrB,MAAM,cAAc;IAAC;IAAS;IAAU;IAAQ;IAAO;IAAU;CAAQ;AACzE,MAAM,kBAAkB;IACpB,IAAG,KAAK;QACJ,MAAM,QAAQ,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG,CAAC,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,QAAQ,KAAK,KAAK,CAAC,MAAM;QACzG,OAAO,QAAQ,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG,IAAI,CAAC,cAAc,CAAC,QAAQ,GAAG;IACjF;IACA,MAAK,KAAK;QACN,OAAO,QAAQ,IAAI,CAAC,cAAc,CAAC,UAAU;IACjD;AACJ;AACA,MAAM,iBAAiB,SAAS,MAAM;IAClC,CAAA,GAAA,oLAAA,CAAA,OAAI,AAAD,EAAE,aAAc,SAAS,CAAC,EAAE,IAAI;QAC/B,MAAM,CAAC,KAAK,GAAG,SAAS,MAAM,CAAC,KAAK,KAAK;IAC7C;IACA,OAAO;AACX;AACA,MAAM,yBAAyB,SAAS,UAAU;IAC9C,MAAM,qBAAqB,CAAC;IAC5B,WAAW,OAAO,CAAE,SAAS,IAAI,EAAE,CAAC;QAChC,kBAAkB,CAAC,KAAK,OAAO,GAAG,GAAG;IACzC;IACA,OAAO;AACX;AACA,MAAM,wBAAwB,SAAS,aAAa;IAChD,IAAI,CAAC,CAAC,yBAAyB,mKAAA,CAAA,QAAK,GAAG;QACnC,gBAAgB,IAAI,mKAAA,CAAA,QAAK,CAAC;IAC9B;IAEA,SAAS,SAAS,aAAa,EAAE,iBAAiB;QAC9C,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa,CAAC,cAAc,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,aAAa,CAAC,kBAAkB,GAAG;YACzF,aAAa,CAAC,cAAc,GAAG,aAAa,CAAC,kBAAkB;QACnE;IACJ;IACA,SAAS,cAAc;IACvB,SAAS,cAAc;IACvB,OAAO;AACX;AAEA,SAAS,cAAc,MAAM,EAAE,KAAK;IAChC,MAAM,YAAY,kBAAkB,MAAM,QAAQ,GAAG,SAAS,KAAK;QAC/D,OAAO,CAAA,GAAA,4JAAA,CAAA,YAAM,AAAD,EAAE,OAAO,MAAM,IAAI;IACnC,IAAI,SAAS,KAAK;QACd,OAAO;IACX;IACA,MAAM,QAAQ,EAAE;IAChB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,MAAM,SAAS,OAAO,MAAM;IAC5B,IAAI,MAAM;IACV,IAAK,IAAI,GAAG,IAAI,QAAQ,IAAK;QACzB,KAAK,MAAM,CAAC,EAAE;QACd,gBAAgB,UAAU,GAAG,IAAI;QACjC,cAAc,UAAU,GAAG,EAAE;QAC7B,OAAO,cAAc;QACrB,MAAM,IAAI,CAAC;YACP,QAAQ;YACR,MAAM;YACN,MAAM,GAAG,IAAI;YACb,IAAI,GAAG,EAAE;YACT,QAAQ;YACR,iBAAiB,GAAG,eAAe;QACvC;IACJ;IACA,OAAO;AACX;AAEA,SAAS,gBAAgB,KAAK;IAC1B,IAAI,MAAM,MAAM,GAAG;IACnB,IAAI,MAAM,MAAM,GAAG;IACnB,IAAI,aAAa,MAAM,UAAU;IACjC,IAAI,aAAa,MAAM,UAAU;IACjC,MAAM,gBAAgB,kBAAkB,MAAM,QAAQ;IACtD,IAAI,eAAe;QACf,aAAa,CAAA,GAAA,4JAAA,CAAA,YAAM,AAAD,EAAE,YAAY,MAAM,IAAI,EAAE,MAAM,cAAc,EAAE,MAAM,eAAe;QACvF,aAAa,CAAA,GAAA,4JAAA,CAAA,YAAM,AAAD,EAAE,YAAY,MAAM,IAAI,EAAE,MAAM,cAAc,EAAE,MAAM,eAAe;QACvF,MAAM,CAAA,GAAA,4JAAA,CAAA,YAAM,AAAD,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,cAAc,EAAE,MAAM,eAAe;QACzE,MAAM,CAAA,GAAA,4JAAA,CAAA,YAAM,AAAD,EAAE,KAAK,MAAM,IAAI,EAAE,MAAM,cAAc,EAAE,MAAM,eAAe;IAC7E;IACA,OAAO;QACH,MAAM,MAAM,IAAI;QAChB,UAAU;QACV,UAAU;QACV,iBAAiB;QACjB,iBAAiB;IACrB;AACJ;AAEA,SAAS,8BAA8B,QAAQ;IAC3C,OAAO;QACH,aAAa,CAAC,WAAW,SAAS,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;YACrD,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM;QACjC,IAAI,SAAS,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;YAChC,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,IAAI;QAChD;QACA,WAAW,CAAC,WAAW,SAAS,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;YACnD,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,IAAI;QAChD,IAAI,SAAS,GAAG,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;YAChC,OAAO,MAAM,MAAM,CAAC,EAAE,CAAC,MAAM;QACjC;QACA,WAAW,CAAC,WAAW,SAAS,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG;YAC/C,OAAO,OAAO,EAAE,CAAC,MAAM,IAAI,MAAM,EAAE,CAAC,IAAI;QAC5C,IAAI,SAAS,GAAG,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG;YAC5B,OAAO,MAAM,EAAE,CAAC,IAAI,IAAI,OAAO,EAAE,CAAC,MAAM;QAC5C;QACA,iBAAiB,CAAC,WAAW,SAAS,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;YAChE,OAAO,MAAM,EAAE,CAAC,MAAM,IAAI,OAAO,SAAS,CAAC,IAAI;QACnD,IAAI,SAAS,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;YACvC,OAAO,OAAO,EAAE,CAAC,IAAI,IAAI,MAAM,SAAS,CAAC,MAAM;QACnD;QACA,WAAW,CAAC,WAAW,SAAS,EAAE;YAC9B,OAAO,GAAG,MAAM;QACpB,IAAI,SAAS,EAAE,EAAE,SAAS;YACtB,OAAO,UAAU,MAAM,GAAG,GAAG,MAAM;QACvC;QACA,eAAe,CAAC,WAAW,SAAS,EAAE;YAClC,OAAO,GAAG,eAAe;QAC7B,IAAI,SAAS,EAAE,EAAE,SAAS;YACtB,OAAO,UAAU,eAAe,GAAG,GAAG,eAAe;QACzD;IACJ;AACJ;AACA,MAAM,gBAAgB,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO;IACzD,IAAI,CAAC,MAAM,CAAC,eAAe,QAAQ;AACvC;AACA,cAAc,SAAS,GAAG;IACtB,aAAa;IACb,QAAQ;QACJ,MAAM,OAAO,IAAI;QACjB,MAAM,UAAU,KAAK,QAAQ;QAC7B,MAAM,QAAQ,KAAK,cAAc;QACjC,MAAM,aAAa,MAAM,UAAU,IAAI,EAAE;QACzC,IAAI,SAAS,CAAC;QACd,MAAM,gBAAgB,KAAK,qBAAqB;QAChD,MAAM,oBAAoB,CAAA,GAAA,4JAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,MAAM,UAAU,EAAE,MAAM,UAAU,EAAE,UAAU;QACtG,MAAM,mBAAmB,kBAAkB,MAAM;QACjD,IAAI,MAAM,OAAO,IAAI;YACjB,SAAS;QACb,OAAO;YACH,OAAQ,MAAM,QAAQ;gBAClB,KAAK;oBACD,SAAS,oLAAA,CAAA,UAAqB;oBAC9B;gBACJ,KAAK;oBACD,SAAS,iLAAA,CAAA,UAAkB;oBAC3B,cAAc,kBAAkB,GAAG,cAAc,YAAY,GAAG,CAAC,6JAAA,CAAA,UAAS,CAAC,WAAW,CAAC,cAAc,eAAe,EAAE,QAAQ,QAAQ,IAAI,cAAc,eAAe;oBACvK;gBACJ,KAAK;oBACD,SAAS,iLAAA,CAAA,UAAkB;oBAC3B,KAAK,WAAW,GAAG;oBACnB,cAAc,QAAQ,GAAG,KAAK,oBAAoB,CAAC,QAAQ,iBAAiB,GAAG,mBAAmB,IAAI,kBAAkB;oBACxH,KAAK,mBAAmB,GAAG,uBAAuB;oBAClD,IAAI,kBAAkB;wBAClB,cAAc,eAAe,GAAG,KAAK,mBAAmB,CAAC,iBAAiB,CAAC,EAAE,CAAC,OAAO,GAAG;wBACxF,KAAK,iBAAiB,GAAG;oBAC7B;oBACA;gBACJ;oBACI,IAAI,eAAe,MAAM,QAAQ,EAAE;wBAC/B,SAAS,iLAAA,CAAA,UAAkB;oBAC/B;YACR;QACJ;QAAC,CAAC,KAAK,WAAW,IAAI,EAAE,EAAE,OAAO,CAAE,SAAS,UAAU;YAClD,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,KAAK,WAAW,GAAG,OAAO,IAAI,CAAC;QAC/B,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,MAAM;QACb,KAAK,gBAAgB,GAAG,QAAQ,eAAe,GAAG,CAAA,QAAS,QAAQ,CAAC,OAAO,YAAc,YAAY,QAAQ,KAAK,KAAK,CAAC;QACxH,KAAK,EAAE,GAAG,CAAC;QACX,KAAK,2BAA2B,GAAG;YAAC,8BAA8B;YAAQ,8BAA8B,KAAK,UAAU;SAAI;QAC3H,KAAK,gBAAgB;QACrB,KAAK,uBAAuB;IAChC;IACA,kBAAkB;QACd,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU;QACrC,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI,KAAK,MAAM,QAAQ;YACnB;QACJ;QACA,IAAK,IAAI,GAAG,SAAS,OAAO,MAAM,EAAE,IAAI,QAAQ,IAAK;YACjD,IAAI,MAAM,CAAC,EAAE;YACb,MAAM,IAAI,CAAC,SAAS,CAAC,EAAE,EAAE;YACzB,EAAE,GAAG,GAAG;YACR,EAAE,KAAK,GAAG,CAAC,EAAE,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,MAAM,OAAO;QAC1E;IACJ;IACA,wBAAwB,SAAS,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,EAAE,OAAO;QAC7D,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO;YACP,QAAQ;YACR,YAAY,KAAK;YACjB,SAAS;QACb;QACA,IAAI;QACJ,IAAI;QACJ,MAAM,YAAY,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;QAC3C,IAAI,QAAQ,WAAW,CAAC,KAAK,QAAQ,OAAO,MAAM;YAC9C,OAAO;QACX,OAAO,IAAI,QAAQ,SAAS,CAAC,KAAK,QAAQ,OAAO,MAAM;YACnD,OAAO;gBACH,QAAQ,UAAU,MAAM;gBACxB,YAAY,UAAU,eAAe;gBACrC,SAAS;YACb;QACJ;QACA,IAAK,IAAI,GAAG,SAAS,OAAO,MAAM,EAAE,IAAI,QAAQ,IAAK;YACjD,KAAK,MAAM,CAAC,EAAE;YACd,YAAY,MAAM,CAAC,IAAI,EAAE;YACzB,IAAI,QAAQ,SAAS,CAAC,KAAK,IAAI,OAAO,MAAM;gBACxC,KAAK,OAAO,GAAG;gBACf,KAAK,KAAK,GAAG;gBACb;YACJ;YACA,IAAI,aAAa,QAAQ,eAAe,CAAC,KAAK,IAAI,WAAW,OAAO,MAAM;gBACtE,OAAO;oBACH,QAAQ,QAAQ,SAAS,CAAC,WAAW;oBACrC,YAAY,QAAQ,aAAa,CAAC,WAAW;oBAC7C,SAAS;gBACb;gBACA;YACJ;QACJ;QACA,OAAO;IACX;IACA,YAAY;QACR,OAAO,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,cAAc,CAAC,MAAM;IACpE;IACA,sBAAsB,SAAS,gBAAgB,EAAE,aAAa;QAC1D,MAAM,2BAA2B,mBAAmB,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,GAAG,IAAI,CAAC;QAChF,OAAO,2BAA2B,IAAI,cAAc,YAAY,GAAG,2BAA2B,cAAc,YAAY;IAC5H;IACA;QACI,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,gBAAgB,IAAI,CAAC,cAAc,GAAG,gBAAgB;QAC5D,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,MAAM,SAAS,IAAI,CAAC,OAAO;QAC3B,IAAI;QACJ,cAAc,YAAY,GAAG,OAAO,YAAY,IAAI;QACpD,cAAc,UAAU,GAAG,OAAO,UAAU,IAAI;QAChD,IAAI,IAAI,CAAC,QAAQ,CAAC,YAAY,EAAE;YAC5B,cAAc,UAAU,GAAG,OAAO,IAAI,GAAG,cAAc,YAAY;YACnE,SAAS,OAAO,KAAK;YACrB,cAAc,QAAQ,GAAG,OAAO,KAAK,GAAG,OAAO,KAAK,GAAG,cAAc,UAAU;YAC/E,cAAc,MAAM,GAAG,cAAc,MAAM;QAC/C,OAAO;YACH,cAAc,UAAU,GAAG,OAAO,GAAG,GAAG,cAAc,YAAY;YAClE,SAAS,OAAO,MAAM;YACtB,cAAc,QAAQ,GAAG,OAAO,MAAM,GAAG,OAAO,MAAM,GAAG,cAAc,UAAU;YACjF,cAAc,MAAM,GAAG,CAAC,cAAc,MAAM;QAChD;QACA,IAAI,CAAC,YAAY,GAAG,cAAc,YAAY,GAAG,cAAc,QAAQ,GAAG,cAAc,UAAU;QAClG,cAAc,gBAAgB,GAAG,KAAK,GAAG,CAAC,IAAI,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,QAAQ,GAAG,cAAc,QAAQ,IAAI,CAAA,GAAA,4JAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;QAC7H,cAAc,kBAAkB,GAAG,cAAc,YAAY,GAAG,CAAC,cAAc,eAAe,GAAG,cAAc,eAAe;QAC9H,IAAI,KAAK,MAAM,QAAQ;YACnB,MAAM,qBAAqB,cAAc,eAAe,GAAG,cAAc,eAAe,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,MAAM;YAC3H,IAAI,MAAM,oBAAoB;gBAC1B,cAAc,kBAAkB,GAAG,CAAC,cAAc,YAAY,GAAG,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE,CAAC,eAAe,IAAI;YAClH;QACJ;QACA,OAAO;IACX;IACA,cAAc,SAAS,MAAM;QACzB,IAAI,CAAC,OAAO,GAAG,eAAe;QAC9B,IAAI,CAAC,MAAM;IACf;IACA,qBAAqB,SAAS,aAAa;QACvC,MAAM,SAAS,cAAc,MAAM,IAAI,EAAE;QACzC,IAAI,CAAC,WAAW,GAAG,cAAc,UAAU,IAAI,EAAE;QACjD,IAAI,CAAC,cAAc,GAAG,sBAAsB;QAC5C,IAAI,CAAC,OAAO,GAAG,OAAO,MAAM,GAAG,cAAc,QAAQ,IAAI,CAAC,cAAc,IAAI,KAAK;QACjF,IAAI,CAAC,MAAM;IACf;IACA,QAAQ,SAAS,aAAa,EAAE,MAAM,EAAE,OAAO;QAC3C,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG;QAC5C,IAAI,CAAC,OAAO,GAAG,eAAe;QAC9B,IAAI,CAAC,mBAAmB,CAAC;IAC7B;IACA,kBAAkB;QACd,OAAO,IAAI,CAAC,cAAc;IAC9B;IACA,eAAe,SAAS,SAAS;QAC7B,OAAO,UAAU,UAAU,IAAI;IACnC;IACA,sBAAsB;QAClB,OAAO;YACH,KAAK,IAAI,CAAC,cAAc,CAAC,UAAU;YACnC,KAAK,IAAI,CAAC,cAAc,CAAC,QAAQ;QACrC;IACJ;IACA,yBAAyB;QACrB,MAAM,OAAO,IAAI;QACjB,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,aAAa,cAAc,UAAU,GAAG,cAAc,YAAY;QACxE,MAAM,WAAW,cAAc,QAAQ,GAAG,cAAc,UAAU;QAClE,MAAM,QAAQ,KAAK,cAAc;QACjC,MAAM,aAAa,MAAM,UAAU;QACnC,MAAM,aAAa,MAAM,UAAU;QACnC,MAAM,gCAAgC,aAAa,cAAc,YAAY,GAAG;QAChF,IAAI;QACJ,IAAI,aAAa,KAAK,aAAa,KAAK,eAAe,YAAY;YAC/D,0BAA0B,KAAK,SAAS,CAAC,GAAG;QAChD;QACA,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,0BAA0B;YACrC,MAAM,SAAS,MAAM,MAAM,GAAG,CAAC,aAAa,KAAK,cAAc,CAAC;YAChE,IAAI,KAAK,QAAQ,CAAC,YAAY,EAAE;gBAC5B,0BAA0B,SAAS,WAAW;YAClD,OAAO;gBACH,0BAA0B,SAAS,aAAa;YACpD;QACJ;QACA,KAAK,EAAE,GAAG;YACN,yBAAyB;YACzB,sBAAsB;YACtB,qBAAqB;YACrB,wBAAwB;YACxB,wBAAwB;YACxB,uBAAuB;YACvB,wBAAwB;YACxB,uBAAuB,cAAc,MAAM,GAAG,WAAW;YACzD,qBAAqB,cAAc,MAAM,GAAG,aAAa;QAC7D;IACJ;IACA,sBAAqB,KAAK;QACtB,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM;IACzB;IACA,sBAAsB,SAAS,QAAQ;QACnC,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,OAAO,cAAc,MAAM,GAAG,cAAc,QAAQ,GAAG,WAAW,cAAc,UAAU,GAAG;IACjG;IACA,wBAAwB,SAAS,QAAQ;QACrC,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,eAAe,IAAI,CAAC,cAAc,CAAC,QAAQ,IAAI,CAAC,WAAW,KAAK,KAAK,CAAC,SAAS;QAC/E,OAAO,cAAc,MAAM,GAAG,cAAc,eAAe,CAAC,OAAO,KAAK,WAAW,cAAc,eAAe,CAAC,OAAO,KAAK;IACjI;IACA,eAAe,SAAS,UAAU;QAC9B,MAAM,cAAc,IAAI,CAAC,oBAAoB;QAC7C,MAAM,WAAW,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,GAAG;QAC7C,OAAO,KAAK,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,QAAQ;IAC1G;IACA,iBAAiB,SAAS,KAAK,EAAE,aAAa;QAC1C,OAAO,KAAK,SAAS,gBAAgB,SAAS,IAAI,gBAAgB,CAAC,gBAAgB;IACvF;IACA,WAAU,EAAE,EAAE,SAAS,EAAE,SAAS;QAC9B,MAAM,eAAe,IAAI,CAAC,oBAAoB,CAAC;QAC/C,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,eAAe;YACzB,OAAO,KAAK,KAAK,CAAC;QACtB;QACA,IAAI,MAAM,KAAK;YACX,OAAO;QACX;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,WAAW;IAClC;IACA,aAAa,SAAS,QAAQ;QAC1B,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,WAAW,qBAAA,sBAAA,WAAY,IAAI,CAAC,cAAc,CAAC,QAAQ;QACnD,IAAI,UAAU;YACV,OAAO,KAAK,KAAK,CAAC,cAAc,kBAAkB,GAAG;QACzD;QACA,OAAO,KAAK,KAAK,CAAC,cAAc,QAAQ,GAAG,cAAc,UAAU;IACvE;IACA,MAAK,SAAS,EAAE,KAAK,EAAE,UAAU;QAC7B,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,IAAI,cAAc,eAAe,CAAC,OAAO,OAAO,cAAc,eAAe,CAAC,OAAO,MAAM,MAAM,WAAW;YACxG,OAAO,IAAI,CAAC,mBAAmB,CAAC,WAAW;QAC/C;QACA,MAAM,aAAa,cAAc,UAAU;QAC3C,MAAM,WAAW,cAAc,QAAQ;QACvC,MAAM,aAAa,IAAI,CAAC,UAAU;QAClC,IAAI,WAAW,CAAC,aAAa,SAAS,IAAI;QAC1C,IAAI,SAAS,CAAC,WAAW,SAAS,IAAI;QACtC,aAAa,cAAc,CAAC;QAC5B,MAAM,WAAW,IAAI,CAAC,EAAE,CAAC,aAAa,WAAW,QAAQ,GAAG,WAAW,UAAU;QACjF,MAAM,WAAW,IAAI,CAAC,EAAE,CAAC,aAAa,WAAW,UAAU,GAAG,WAAW,QAAQ;QACjF,IAAI;QACJ,IAAI;QACJ,IAAI,WAAW,UAAU;YACrB,UAAU,WAAW;YACrB,WAAW;YACX,MAAM,aAAa,WAAW,QAAQ,GAAG,WAAW,UAAU;QAClE;QACA,IAAI,WAAW,QAAQ;YACnB,YAAY,SAAS;YACrB,SAAS;YACT,MAAM,aAAa,WAAW,UAAU,GAAG,WAAW,QAAQ;QAClE;QACA,IAAI,WAAW,WAAW,SAAS,UAAU;YACzC,WAAW;YACX,SAAS;QACb;QACA,YAAY,CAAC,WAAW,UAAU,IAAI,WAAW,CAAC,SAAS,QAAQ,IAAI;QACvE,QAAQ,CAAC,aAAa,SAAS,IAAI,YAAY;QAC/C,MAAM,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,UAAU;QACxD,MAAM,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;QACvD,IAAI,SAAS,GAAG;YACZ,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,MAAM,QAAQ,YAAY,CAAC;YACpE,MAAM,IAAI,CAAC,wBAAwB,CAAC,KAAK,MAAM,QAAQ,YAAY;QACvE;QACA,IAAI,MAAM,KAAK;YACX,MAAM,MAAM,WAAW,QAAQ,GAAG,WAAW,QAAQ,GAAG;YACxD,MAAM,MAAM,WAAW,UAAU,GAAG,WAAW,UAAU,GAAG;QAChE,OAAO;YACH,MAAM,MAAM,WAAW,UAAU,GAAG,WAAW,UAAU,GAAG;YAC5D,MAAM,MAAM,WAAW,QAAQ,GAAG,WAAW,QAAQ,GAAG;QAC5D;QACA,OAAO;YACH,KAAK;YACL,KAAK;YACL,WAAW,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE;YAClB,OAAO,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE;QAClB;IACJ;IACA,0BAAyB,KAAK,EAAE,SAAS;QACrC,MAAM,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAE,CAAA,KAAM,SAAS,GAAG,IAAI,IAAI,SAAS,GAAG,EAAE;QAC5E,IAAI,GAAG,MAAM,EAAE;YACX,OAAO,YAAY,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI;QAChD,OAAO;YACH,OAAO;QACX;IACJ;IACA,qBAAoB,SAAS,EAAE,KAAK;QAChC,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,MAAM,MAAM,cAAc,QAAQ;QAClC,MAAM,MAAM,cAAc,QAAQ;QAClC,MAAM,aAAa,CAAC,IAAI,OAAO,OAAO,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,IAAI,OAAO,KAAK,KAAK,cAAc,eAAe,CAAC,OAAO,KAAK,IAAI,OAAO,GAAG,IAAI,cAAc,YAAY;QACjL,MAAM,aAAa,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE;QACzC,MAAM,gBAAgB,kBAAkB,IAAI,CAAC,cAAc,CAAC,QAAQ;QACpE,IAAI,SAAS,cAAc,eAAe,CAAC,OAAO,KAAK;QACvD,IAAI,SAAS,cAAc,eAAe,CAAC,OAAO,KAAK;QACvD,SAAS,gBAAgB,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,cAAc,IAAI,KAAK,aAAa,IAAI,KAAK,UAAU;QAC1G,SAAS,gBAAgB,CAAA,GAAA,gLAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,4JAAA,CAAA,aAAU,AAAD,EAAE,QAAQ,cAAc,IAAI,KAAK,aAAa,IAAI,KAAK,UAAU;QAC1G,OAAO;YACH,KAAK;YACL,KAAK;YACL,WAAW;YACX,OAAO;QACX;IACJ;IACA,aAAa,SAAS,IAAI;QACtB,MAAM,EACF,UAAU,QAAQ,EAClB,UAAU,QAAQ,EACrB,GAAG,IAAI,CAAC,cAAc;QACvB,IAAI,eAAe,YAAY,MAAM,UAAU;YAC3C,OAAO,IAAI,CAAC,mBAAmB,CAAC;QACpC;QACA,OAAO,OAAO,MAAM;IACxB;IACA,qBAAoB,IAAI;QACpB,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,IAAI,SAAS,cAAc,YAAY,GAAG,cAAc,kBAAkB;QAC1E,UAAU,CAAC,SAAS,KAAK,WAAW,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC;QACvD,OAAO,cAAc,YAAY,GAAG,CAAC,KAAK,GAAG,CAAC,QAAQ,KAAK,cAAc,kBAAkB;IAC/F;IACA,UAAU,SAAS,IAAI,EAAE,IAAI;QACzB,MAAM,gBAAgB,IAAI,CAAC,cAAc;QACzC,IAAI,cAAc,QAAQ,KAAK,cAAc,QAAQ,EAAE;YACnD,OAAO;QACX;QACA,OAAO,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,cAAc,QAAQ;QACtE,OAAO,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,IAAI,CAAC,SAAS,CAAC,QAAQ,cAAc,QAAQ;QACtE,OAAO,CAAC,cAAc,QAAQ,GAAG,cAAc,QAAQ,IAAI,KAAK,GAAG,CAAC,OAAO;IAC/E;IACA,SAAS,SAAS,KAAK;QACnB,MAAM,KAAK,IAAI,CAAC,cAAc;QAC9B,QAAQ,IAAI,CAAC,SAAS,CAAC;QACvB,OAAO,SAAS,SAAS,CAAC,MAAM,UAAU,MAAM,OAAO,KAAK,GAAG,gBAAgB,IAAI,GAAG,QAAQ,IAAI,MAAM,OAAO,KAAK,GAAG,gBAAgB,IAAI,GAAG,QAAQ;IAC1J;IACA,iBAAiB,SAAS,KAAK,EAAE,SAAS;QACtC,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,KAAK,OAAO;QAC3B,IAAI;QACJ,QAAQ,KAAK,SAAS,CAAC;QACvB,IAAI,KAAK,OAAO,EAAE;YACd,OAAO,KAAK,sBAAsB,CAAC,QAAQ,OAAO,UAAU,QAAQ,KAAK,2BAA2B,CAAC,EAAE;YACvG,IAAI,SAAS,KAAK,OAAO,EAAE;gBACvB,OAAO,KAAK,OAAO,CAAC,YAAY,IAAI,KAAK,KAAK,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,MAAM;YAC3E;QACJ;QACA,OAAO,KAAK,OAAO,CAAC;IACxB;IACA,IAAI,SAAS,EAAE,EAAE,SAAS,EAAE,SAAS;QACjC,MAAM,QAAQ,IAAI,CAAC,gBAAgB;QACnC,IAAI,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,UAAU,KAAK,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,MAAM,UAAU,KAAK,MAAM,UAAU,CAAC,OAAO,OAAO,MAAM,UAAU,CAAC,OAAO,IAAI;YACzH,IAAI,CAAC,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,UAAU,CAAC,OAAO,OAAO,GAAG,OAAO,IAAI;gBAC/D,OAAO;YACX;YACA,OAAO,IAAI,CAAC,oBAAoB,CAAC,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,cAAc,GAAG,4BAA4B;QAC5G;QACA,KAAK,IAAI,CAAC,SAAS,CAAC;QACpB,MAAM,OAAO,IAAI;QACjB,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,SAAS,KAAK,OAAO;QAC3B,IAAI,OAAO;YACP,QAAQ;QACZ;QACA,IAAI,kBAAkB;QACtB,IAAI,KAAK,MAAM,QAAQ;YACnB,OAAO,KAAK,sBAAsB,CAAC,QAAQ,IAAI,UAAU,QAAQ,KAAK,2BAA2B,CAAC,EAAE;YACpG,kBAAkB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG;QACrE;QACA,IAAI,SAAS,KAAK,OAAO,EAAE;YACvB,IAAI,YAAY,GAAG;gBACf,OAAO,KAAK,KAAK,CAAC,KAAK;YAC3B,OAAO,IAAI,YAAY,GAAG;gBACtB,OAAO,KAAK,KAAK,CAAC,GAAG;YACzB,OAAO;gBACH,OAAO;YACX;QACJ;QACA,OAAO,KAAK,gBAAgB,CAAC,KAAK,oBAAoB,CAAC,CAAC,KAAK,cAAc,eAAe,GAAG,KAAK,MAAM,IAAI,cAAc,kBAAkB,GAAG,kBAAkB;IACrK;IACA,MAAM,SAAS,GAAG,EAAE,SAAS;QACzB,MAAM,OAAO,IAAI;QACjB,MAAM,SAAS,KAAK,OAAO;QAC3B,IAAI,OAAO;YACP,QAAQ;QACZ;QACA,MAAM,gBAAgB,KAAK,cAAc;QACzC,MAAM,aAAa,cAAc,UAAU;QAC3C,IAAI,kBAAkB;QACtB,IAAI,KAAK,MAAM,QAAQ;YACnB,OAAO,KAAK,sBAAsB,CAAC,QAAQ,KAAK,SAAS,OAAO,KAAK,2BAA2B,CAAC,EAAE;YACnG,kBAAkB,CAAA,GAAA,gLAAA,CAAA,YAAS,AAAD,EAAE,KAAK,UAAU,IAAI,KAAK,UAAU,GAAG;QACrE;QACA,IAAI,SAAS,KAAK,OAAO,EAAE;YACvB,IAAI,YAAY,GAAG;gBACf,OAAO,KAAK,OAAO,CAAC,KAAK,KAAK,CAAC,IAAI;YACvC,OAAO,IAAI,YAAY,GAAG;gBACtB,OAAO,KAAK,OAAO,CAAC,KAAK,KAAK,CAAC,MAAM;YACzC,OAAO;gBACH,OAAO;YACX;QACJ;QACA,OAAO,KAAK,OAAO,CAAC,KAAK,sBAAsB,CAAC,CAAC,MAAM,aAAa,eAAe,IAAI,cAAc,kBAAkB,GAAG,KAAK,MAAM;IACzI;IACA,kBAAkB;IAClB,UAAU;QACN,OAAO;YAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ;YAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,QAAQ;SAAE;IACnG;IACA,gBAAgB;QACZ,OAAO;YAAC,IAAI,CAAC,cAAc,CAAC,UAAU;YAAE,IAAI,CAAC,cAAc,CAAC,QAAQ;SAAC;IACzE;IACA,KAAK,SAAS,KAAK,EAAE,IAAI,EAAE,GAAG;QAC1B,OAAO,IAAI,CAAC,IAAI,CAAC,OAAO,MAAM,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI;IAC1E;IACA,MAAM,SAAS,KAAK,EAAE,IAAI,EAAE,KAAK;QAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,OAAO;IACvD;IACA,WAAW,SAAS,KAAK;QACrB,OAAO,SAAS,QAAQ,OAAO,SAAS;IAC5C;IACA,SAAS,SAAS,KAAK;QACnB,OAAO,SAAS,QAAQ,OAAO,SAAS;IAC5C;IACA;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,kBAAkB;IACjD;IACA,SAAS,CAAA,QAAS;IAClB,wBAAuB,OAAO,EAAE,WAAW;QACvC,IAAI,YAAY,UAAU,GAAG,WAAW,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE;YAC7D,OAAO;gBAAC,YAAY,UAAU;gBAAE,YAAY,UAAU,GAAG;aAAQ;QACrE,OAAO;YACH,OAAO;gBAAC,YAAY,UAAU,GAAG;gBAAS,YAAY,UAAU;aAAC;QACrE;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3028, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/utils.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/utils.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\r\nimport {\r\n    refreshPaths\r\n} from \"./core/renderers/renderer\";\r\nimport {\r\n    each as _each\r\n} from \"../core/utils/iterator\";\r\nconst {\r\n    floor: floor\r\n} = Math;\r\nexport let prepareSegmentRectPoints = function(left, top, width, height, borderOptions) {\r\n    const maxSW = ~~((width < height ? width : height) / 2);\r\n    const sw = borderOptions.width || 0;\r\n    const newSW = sw < maxSW ? sw : maxSW;\r\n    left += newSW / 2;\r\n    top += newSW / 2;\r\n    width -= newSW;\r\n    height -= newSW;\r\n    const right = left + width;\r\n    const bottom = top + height;\r\n    let points = [];\r\n    let segments = [];\r\n    let segmentSequence;\r\n    let visiblyOpt = 0;\r\n    let prevSegmentVisibility = 0;\r\n    const allSegment = {\r\n        top: [\r\n            [left, top],\r\n            [right, top]\r\n        ],\r\n        right: [\r\n            [right, top],\r\n            [right, bottom]\r\n        ],\r\n        bottom: [\r\n            [right, bottom],\r\n            [left, bottom]\r\n        ],\r\n        left: [\r\n            [left, bottom],\r\n            [left, top]\r\n        ]\r\n    };\r\n    _each(allSegment, (function(seg) {\r\n        const visibility = !!borderOptions[seg];\r\n        visiblyOpt = 2 * visiblyOpt + ~~visibility\r\n    }));\r\n    switch (visiblyOpt) {\r\n        case 13:\r\n        case 9:\r\n            segmentSequence = [\"left\", \"top\", \"right\", \"bottom\"];\r\n            break;\r\n        case 11:\r\n            segmentSequence = [\"bottom\", \"left\", \"top\", \"right\"];\r\n            break;\r\n        default:\r\n            segmentSequence = [\"top\", \"right\", \"bottom\", \"left\"]\r\n    }\r\n    _each(segmentSequence, (function(_, seg) {\r\n        const segmentVisibility = !!borderOptions[seg];\r\n        if (!prevSegmentVisibility && segments.length) {\r\n            points.push(segments);\r\n            segments = []\r\n        }\r\n        if (segmentVisibility) {\r\n            _each(allSegment[seg].slice(prevSegmentVisibility), (function(_, segment) {\r\n                segments = segments.concat(segment)\r\n            }))\r\n        }\r\n        prevSegmentVisibility = ~~segmentVisibility\r\n    }));\r\n    segments.length && points.push(segments);\r\n    1 === points.length && (points = points[0]);\r\n    return {\r\n        points: points,\r\n        pathType: 15 === visiblyOpt ? \"area\" : \"line\"\r\n    }\r\n};\r\nexport {\r\n    refreshPaths\r\n};\r\nexport const areCanvasesDifferent = function(canvas1, canvas2) {\r\n    const sizeLessThreshold = [\"width\", \"height\"].every((key => Math.abs(canvas1[key] - canvas2[key]) < 1));\r\n    const canvasCoordsIsEqual = [\"left\", \"right\", \"top\", \"bottom\"].every((key => canvas1[key] === canvas2[key]));\r\n    return !(sizeLessThreshold && canvasCoordsIsEqual)\r\n};\r\nexport const floorCanvasDimensions = function(canvas) {\r\n    return _extends({}, canvas, {\r\n        height: floor(canvas.height),\r\n        width: floor(canvas.width)\r\n    })\r\n};\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;AACD;AACA;AAGA;AAAA;;;;AAGA,MAAM,EACF,OAAO,KAAK,EACf,GAAG;AACG,IAAI,2BAA2B,SAAS,IAAI,EAAE,GAAG,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa;IAClF,MAAM,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,SAAS,QAAQ,MAAM,IAAI,CAAC;IACtD,MAAM,KAAK,cAAc,KAAK,IAAI;IAClC,MAAM,QAAQ,KAAK,QAAQ,KAAK;IAChC,QAAQ,QAAQ;IAChB,OAAO,QAAQ;IACf,SAAS;IACT,UAAU;IACV,MAAM,QAAQ,OAAO;IACrB,MAAM,SAAS,MAAM;IACrB,IAAI,SAAS,EAAE;IACf,IAAI,WAAW,EAAE;IACjB,IAAI;IACJ,IAAI,aAAa;IACjB,IAAI,wBAAwB;IAC5B,MAAM,aAAa;QACf,KAAK;YACD;gBAAC;gBAAM;aAAI;YACX;gBAAC;gBAAO;aAAI;SACf;QACD,OAAO;YACH;gBAAC;gBAAO;aAAI;YACZ;gBAAC;gBAAO;aAAO;SAClB;QACD,QAAQ;YACJ;gBAAC;gBAAO;aAAO;YACf;gBAAC;gBAAM;aAAO;SACjB;QACD,MAAM;YACF;gBAAC;gBAAM;aAAO;YACd;gBAAC;gBAAM;aAAI;SACd;IACL;IACA,CAAA,GAAA,oLAAA,CAAA,OAAK,AAAD,EAAE,YAAa,SAAS,GAAG;QAC3B,MAAM,aAAa,CAAC,CAAC,aAAa,CAAC,IAAI;QACvC,aAAa,IAAI,aAAa,CAAC,CAAC;IACpC;IACA,OAAQ;QACJ,KAAK;QACL,KAAK;YACD,kBAAkB;gBAAC;gBAAQ;gBAAO;gBAAS;aAAS;YACpD;QACJ,KAAK;YACD,kBAAkB;gBAAC;gBAAU;gBAAQ;gBAAO;aAAQ;YACpD;QACJ;YACI,kBAAkB;gBAAC;gBAAO;gBAAS;gBAAU;aAAO;IAC5D;IACA,CAAA,GAAA,oLAAA,CAAA,OAAK,AAAD,EAAE,iBAAkB,SAAS,CAAC,EAAE,GAAG;QACnC,MAAM,oBAAoB,CAAC,CAAC,aAAa,CAAC,IAAI;QAC9C,IAAI,CAAC,yBAAyB,SAAS,MAAM,EAAE;YAC3C,OAAO,IAAI,CAAC;YACZ,WAAW,EAAE;QACjB;QACA,IAAI,mBAAmB;YACnB,CAAA,GAAA,oLAAA,CAAA,OAAK,AAAD,EAAE,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,wBAAyB,SAAS,CAAC,EAAE,OAAO;gBACpE,WAAW,SAAS,MAAM,CAAC;YAC/B;QACJ;QACA,wBAAwB,CAAC,CAAC;IAC9B;IACA,SAAS,MAAM,IAAI,OAAO,IAAI,CAAC;IAC/B,MAAM,OAAO,MAAM,IAAI,CAAC,SAAS,MAAM,CAAC,EAAE;IAC1C,OAAO;QACH,QAAQ;QACR,UAAU,OAAO,aAAa,SAAS;IAC3C;AACJ;;AAIO,MAAM,uBAAuB,SAAS,OAAO,EAAE,OAAO;IACzD,MAAM,oBAAoB;QAAC;QAAS;KAAS,CAAC,KAAK,CAAE,CAAA,MAAO,KAAK,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI;IACpG,MAAM,sBAAsB;QAAC;QAAQ;QAAS;QAAO;KAAS,CAAC,KAAK,CAAE,CAAA,MAAO,OAAO,CAAC,IAAI,KAAK,OAAO,CAAC,IAAI;IAC1G,OAAO,CAAC,CAAC,qBAAqB,mBAAmB;AACrD;AACO,MAAM,wBAAwB,SAAS,MAAM;IAChD,OAAO,CAAA,GAAA,kKAAA,CAAA,UAAQ,AAAD,EAAE,CAAC,GAAG,QAAQ;QACxB,QAAQ,MAAM,OAAO,MAAM;QAC3B,OAAO,MAAM,OAAO,KAAK;IAC7B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3188, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/palette.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/palette.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    normalizeEnum\r\n} from \"./core/utils\";\r\nimport {\r\n    extend\r\n} from \"../core/utils/extend\";\r\nconst _floor = Math.floor;\r\nconst _ceil = Math.ceil;\r\nimport _Color from \"../color\";\r\nconst _isArray = Array.isArray;\r\nimport {\r\n    isString as _isString\r\n} from \"../core/utils/type\";\r\nconst HIGHLIGHTING_STEP = 50;\r\nconst DEFAULT_PALETTE = \"material\";\r\nconst officePalette = {\r\n    simpleSet: [\"#5f8b95\", \"#ba4d51\", \"#af8a53\", \"#955f71\", \"#859666\", \"#7e688c\"],\r\n    indicatingSet: [\"#a3b97c\", \"#e1b676\", \"#ec7f83\"],\r\n    gradientSet: [\"#5f8b95\", \"#ba4d51\"],\r\n    accentColor: \"#ba4d51\"\r\n};\r\nconst palettes = {\r\n    [DEFAULT_PALETTE]: {\r\n        simpleSet: [\"#1db2f5\", \"#f5564a\", \"#97c95c\", \"#ffc720\", \"#eb3573\", \"#a63db8\"],\r\n        indicatingSet: [\"#97c95c\", \"#ffc720\", \"#f5564a\"],\r\n        gradientSet: [\"#1db2f5\", \"#97c95c\"],\r\n        accentColor: \"#1db2f5\"\r\n    },\r\n    office: officePalette,\r\n    \"harmony light\": {\r\n        simpleSet: [\"#fcb65e\", \"#679ec5\", \"#ad79ce\", \"#7abd5c\", \"#e18e92\", \"#b6d623\", \"#b7abea\", \"#85dbd5\"],\r\n        indicatingSet: [\"#b6d623\", \"#fcb65e\", \"#e18e92\"],\r\n        gradientSet: [\"#7abd5c\", \"#fcb65e\"],\r\n        accentColor: \"#679ec5\"\r\n    },\r\n    \"soft pastel\": {\r\n        simpleSet: [\"#60a69f\", \"#78b6d9\", \"#6682bb\", \"#a37182\", \"#eeba69\", \"#90ba58\", \"#456c68\", \"#7565a4\"],\r\n        indicatingSet: [\"#90ba58\", \"#eeba69\", \"#a37182\"],\r\n        gradientSet: [\"#78b6d9\", \"#eeba69\"],\r\n        accentColor: \"#60a69f\"\r\n    },\r\n    pastel: {\r\n        simpleSet: [\"#bb7862\", \"#70b3a1\", \"#bb626a\", \"#057d85\", \"#ab394b\", \"#dac599\", \"#153459\", \"#b1d2c6\"],\r\n        indicatingSet: [\"#70b3a1\", \"#dac599\", \"#bb626a\"],\r\n        gradientSet: [\"#bb7862\", \"#70b3a1\"],\r\n        accentColor: \"#bb7862\"\r\n    },\r\n    bright: {\r\n        simpleSet: [\"#70c92f\", \"#f8ca00\", \"#bd1550\", \"#e97f02\", \"#9d419c\", \"#7e4452\", \"#9ab57e\", \"#36a3a6\"],\r\n        indicatingSet: [\"#70c92f\", \"#f8ca00\", \"#bd1550\"],\r\n        gradientSet: [\"#e97f02\", \"#f8ca00\"],\r\n        accentColor: \"#e97f02\"\r\n    },\r\n    soft: {\r\n        simpleSet: [\"#cbc87b\", \"#9ab57e\", \"#e55253\", \"#7e4452\", \"#e8c267\", \"#565077\", \"#6babac\", \"#ad6082\"],\r\n        indicatingSet: [\"#9ab57e\", \"#e8c267\", \"#e55253\"],\r\n        gradientSet: [\"#9ab57e\", \"#e8c267\"],\r\n        accentColor: \"#565077\"\r\n    },\r\n    ocean: {\r\n        simpleSet: [\"#75c099\", \"#acc371\", \"#378a8a\", \"#5fa26a\", \"#064970\", \"#38c5d2\", \"#00a7c6\", \"#6f84bb\"],\r\n        indicatingSet: [\"#c8e394\", \"#7bc59d\", \"#397c8b\"],\r\n        gradientSet: [\"#acc371\", \"#38c5d2\"],\r\n        accentColor: \"#378a8a\"\r\n    },\r\n    vintage: {\r\n        simpleSet: [\"#dea484\", \"#efc59c\", \"#cb715e\", \"#eb9692\", \"#a85c4c\", \"#f2c0b5\", \"#c96374\", \"#dd956c\"],\r\n        indicatingSet: [\"#ffe5c6\", \"#f4bb9d\", \"#e57660\"],\r\n        gradientSet: [\"#efc59c\", \"#cb715e\"],\r\n        accentColor: \"#cb715e\"\r\n    },\r\n    violet: {\r\n        simpleSet: [\"#d1a1d1\", \"#eeacc5\", \"#7b5685\", \"#7e7cad\", \"#a13d73\", \"#5b41ab\", \"#e287e2\", \"#689cc1\"],\r\n        indicatingSet: [\"#d8e2f6\", \"#d0b2da\", \"#d56a8a\"],\r\n        gradientSet: [\"#eeacc5\", \"#7b5685\"],\r\n        accentColor: \"#7b5685\"\r\n    },\r\n    carmine: {\r\n        simpleSet: [\"#fb7764\", \"#73d47f\", \"#fed85e\", \"#d47683\", \"#dde392\", \"#757ab2\"],\r\n        indicatingSet: [\"#5cb85c\", \"#f0ad4e\", \"#d9534f\"],\r\n        gradientSet: [\"#fb7764\", \"#73d47f\"],\r\n        accentColor: \"#f05b41\"\r\n    },\r\n    \"dark moon\": {\r\n        simpleSet: [\"#4ddac1\", \"#f4c99a\", \"#80dd9b\", \"#f998b3\", \"#4aaaa0\", \"#a5aef1\"],\r\n        indicatingSet: [\"#59d8a4\", \"#f0ad4e\", \"#f9517e\"],\r\n        gradientSet: [\"#4ddac1\", \"#f4c99a\"],\r\n        accentColor: \"#3debd3\"\r\n    },\r\n    \"soft blue\": {\r\n        simpleSet: [\"#7ab8eb\", \"#97da97\", \"#facb86\", \"#e78683\", \"#839bda\", \"#4db7be\"],\r\n        indicatingSet: [\"#5cb85c\", \"#f0ad4e\", \"#d9534f\"],\r\n        gradientSet: [\"#7ab8eb\", \"#97da97\"],\r\n        accentColor: \"#7ab8eb\"\r\n    },\r\n    \"dark violet\": {\r\n        simpleSet: [\"#9c63ff\", \"#64c064\", \"#eead51\", \"#d2504b\", \"#4b6bbf\", \"#2da7b0\"],\r\n        indicatingSet: [\"#5cb85c\", \"#f0ad4e\", \"#d9534f\"],\r\n        gradientSet: [\"#9c63ff\", \"#64c064\"],\r\n        accentColor: \"#9c63ff\"\r\n    },\r\n    \"green mist\": {\r\n        simpleSet: [\"#3cbab2\", \"#8ed962\", \"#5b9d95\", \"#efcc7c\", \"#f1929f\", \"#4d8dab\"],\r\n        indicatingSet: [\"#72d63c\", \"#ffc852\", \"#f74a5e\"],\r\n        gradientSet: [\"#3cbab2\", \"#8ed962\"],\r\n        accentColor: \"#3cbab2\"\r\n    }\r\n};\r\nlet currentPaletteName;\r\nexport function currentPalette(name) {\r\n    if (void 0 === name) {\r\n        return currentPaletteName || \"material\"\r\n    } else {\r\n        name = normalizeEnum(name);\r\n        currentPaletteName = name in palettes ? name : void 0\r\n    }\r\n}\r\nexport function generateColors(palette, count) {\r\n    let options = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : {\r\n        keepLastColorInEnd: false\r\n    };\r\n    options.type = options.baseColorSet;\r\n    options.extensionMode = options.paletteExtensionMode;\r\n    return createPalette(palette, options).generateColors(count)\r\n}\r\nexport function getPalette(palette, parameters) {\r\n    parameters = parameters || {};\r\n    palette = palette || (void 0 === currentPaletteName ? parameters.themeDefault : currentPalette());\r\n    let result;\r\n    const type = parameters.type;\r\n    if (_isArray(palette)) {\r\n        return palette.slice(0)\r\n    } else {\r\n        if (_isString(palette)) {\r\n            result = palettes[normalizeEnum(palette)]\r\n        }\r\n        if (!result) {\r\n            result = palettes[currentPalette()]\r\n        }\r\n    }\r\n    return type ? result[type].slice(0) : result\r\n}\r\nexport function registerPalette(name, palette) {\r\n    const item = {};\r\n    let paletteName;\r\n    if (_isArray(palette)) {\r\n        item.simpleSet = palette.slice(0)\r\n    } else if (palette) {\r\n        item.simpleSet = _isArray(palette.simpleSet) ? palette.simpleSet.slice(0) : void 0;\r\n        item.indicatingSet = _isArray(palette.indicatingSet) ? palette.indicatingSet.slice(0) : void 0;\r\n        item.gradientSet = _isArray(palette.gradientSet) ? palette.gradientSet.slice(0) : void 0;\r\n        item.accentColor = palette.accentColor\r\n    }\r\n    if (!item.accentColor) {\r\n        item.accentColor = item.simpleSet && item.simpleSet[0]\r\n    }\r\n    if (item.simpleSet || item.indicatingSet || item.gradientSet) {\r\n        paletteName = normalizeEnum(name);\r\n        extend(palettes[paletteName] = palettes[paletteName] || {}, item)\r\n    }\r\n}\r\nexport function getAccentColor(palette, themeDefault) {\r\n    palette = getPalette(palette, {\r\n        themeDefault: themeDefault\r\n    });\r\n    return palette.accentColor || palette[0]\r\n}\r\n\r\nfunction RingBuf(buf) {\r\n    let ind = 0;\r\n    this.next = function() {\r\n        const res = buf[ind++];\r\n        if (ind === buf.length) {\r\n            this.reset()\r\n        }\r\n        return res\r\n    };\r\n    this.reset = function() {\r\n        ind = 0\r\n    }\r\n}\r\n\r\nfunction getAlternateColorsStrategy(palette, parameters) {\r\n    const stepHighlight = parameters.useHighlight ? 50 : 0;\r\n    const paletteSteps = new RingBuf([0, stepHighlight, -stepHighlight]);\r\n    let currentPalette = [];\r\n\r\n    function reset() {\r\n        const step = paletteSteps.next();\r\n        currentPalette = step ? getAlteredPalette(palette, step) : palette.slice(0)\r\n    }\r\n    return {\r\n        getColor: function(index) {\r\n            const color = currentPalette[index % palette.length];\r\n            if (index % palette.length === palette.length - 1) {\r\n                reset()\r\n            }\r\n            return color\r\n        },\r\n        generateColors: function(count) {\r\n            const colors = [];\r\n            count = count || parameters.count;\r\n            for (let i = 0; i < count; i++) {\r\n                colors.push(this.getColor(i))\r\n            }\r\n            return colors\r\n        },\r\n        reset: function() {\r\n            paletteSteps.reset();\r\n            reset()\r\n        }\r\n    }\r\n}\r\n\r\nfunction getExtrapolateColorsStrategy(palette, parameters) {\r\n    return {\r\n        getColor: function(index, count) {\r\n            const paletteCount = palette.length;\r\n            const cycles = _floor((count - 1) / paletteCount + 1);\r\n            const color = palette[index % paletteCount];\r\n            if (cycles > 1) {\r\n                return function(color, cycleIndex, cycleCount) {\r\n                    const hsl = new _Color(color).hsl;\r\n                    let l = hsl.l / 100;\r\n                    const diapason = cycleCount - 1 / cycleCount;\r\n                    let minL = l - .5 * diapason;\r\n                    let maxL = l + .5 * diapason;\r\n                    const cycleMiddle = (cycleCount - 1) / 2;\r\n                    const cycleDiff = cycleIndex - cycleMiddle;\r\n                    if (minL < Math.min(.5, .9 * l)) {\r\n                        minL = Math.min(.5, .9 * l)\r\n                    }\r\n                    if (maxL > Math.max(.8, l + .15 * (1 - l))) {\r\n                        maxL = Math.max(.8, l + .15 * (1 - l))\r\n                    }\r\n                    if (cycleDiff < 0) {\r\n                        l -= (minL - l) * cycleDiff / cycleMiddle\r\n                    } else {\r\n                        l += cycleDiff / cycleMiddle * (maxL - l)\r\n                    }\r\n                    hsl.l = 100 * l;\r\n                    return _Color.prototype.fromHSL(hsl).toHex()\r\n                }(color, _floor(index / paletteCount), cycles)\r\n            }\r\n            return color\r\n        },\r\n        generateColors: function(count) {\r\n            const colors = [];\r\n            count = count || parameters.count;\r\n            for (let i = 0; i < count; i++) {\r\n                colors.push(this.getColor(i, count))\r\n            }\r\n            return colors\r\n        },\r\n        reset: function() {}\r\n    }\r\n}\r\n\r\nfunction getColorMixer(palette, parameters) {\r\n    const paletteCount = palette.length;\r\n    let extendedPalette = [];\r\n\r\n    function distributeColors(count, colorsCount, startIndex, distribution) {\r\n        const groupSize = Math.floor(count / colorsCount);\r\n        let extraItems = count - colorsCount * groupSize;\r\n        let i = startIndex;\r\n        let middleIndex;\r\n        let size;\r\n        while (i < startIndex + count) {\r\n            size = groupSize;\r\n            if (extraItems > 0) {\r\n                size += 1;\r\n                extraItems--\r\n            }\r\n            middleIndex = size > 2 ? Math.floor(size / 2) : 0;\r\n            distribution.push(i + middleIndex);\r\n            i += size\r\n        }\r\n        return distribution.sort((function(a, b) {\r\n            return a - b\r\n        }))\r\n    }\r\n\r\n    function getColorAndDistance(arr, startIndex, count) {\r\n        startIndex = (count + startIndex) % count;\r\n        let distance = 0;\r\n        for (let i = startIndex; i < 2 * count; i += 1) {\r\n            const index = (count + i) % count;\r\n            if (arr[index]) {\r\n                return [arr[index], distance]\r\n            }\r\n            distance++\r\n        }\r\n    }\r\n\r\n    function extendPalette(count) {\r\n        if (count <= paletteCount) {\r\n            return palette\r\n        }\r\n        let result = [];\r\n        const colorInGroups = paletteCount - 2;\r\n        let currentColorIndex = 0;\r\n        let cleanColorIndices = [];\r\n        if (parameters.keepLastColorInEnd) {\r\n            cleanColorIndices = distributeColors(count - 2, colorInGroups, 1, [0, count - 1])\r\n        } else {\r\n            cleanColorIndices = distributeColors(count - 1, paletteCount - 1, 1, [0])\r\n        }\r\n        for (let i = 0; i < count; i++) {\r\n            if (cleanColorIndices.indexOf(i) > -1) {\r\n                result[i] = palette[currentColorIndex++]\r\n            }\r\n        }\r\n        result = function(paletteWithEmptyColors, paletteLength) {\r\n            for (let i = 0; i < paletteLength; i++) {\r\n                const color = paletteWithEmptyColors[i];\r\n                if (!color) {\r\n                    let color1 = paletteWithEmptyColors[i - 1];\r\n                    if (!color1) {\r\n                        continue\r\n                    } else {\r\n                        const c2 = getColorAndDistance(paletteWithEmptyColors, i, paletteLength);\r\n                        const color2 = new _Color(c2[0]);\r\n                        color1 = new _Color(color1);\r\n                        for (let j = 0; j < c2[1]; j++, i++) {\r\n                            paletteWithEmptyColors[i] = color1.blend(color2, (j + 1) / (c2[1] + 1)).toHex()\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n            return paletteWithEmptyColors\r\n        }(result, count);\r\n        return result\r\n    }\r\n    return {\r\n        getColor: function(index, count) {\r\n            count = count || parameters.count || paletteCount;\r\n            if (extendedPalette.length !== count) {\r\n                extendedPalette = extendPalette(count)\r\n            }\r\n            return extendedPalette[index % count]\r\n        },\r\n        generateColors: function(count, repeat) {\r\n            count = count || parameters.count || paletteCount;\r\n            if (repeat && count > paletteCount) {\r\n                const colors = extendPalette(paletteCount);\r\n                for (let i = 0; i < count - paletteCount; i++) {\r\n                    colors.push(colors[i])\r\n                }\r\n                return colors\r\n            } else {\r\n                return paletteCount > 0 ? extendPalette(count).slice(0, count) : []\r\n            }\r\n        },\r\n        reset: function() {}\r\n    }\r\n}\r\nexport function createPalette(palette, parameters, themeDefaultPalette) {\r\n    const paletteObj = {\r\n        dispose() {\r\n            this._extensionStrategy = null\r\n        },\r\n        getNextColor(count) {\r\n            return this._extensionStrategy.getColor(this._currentColor++, count)\r\n        },\r\n        generateColors(count, parameters) {\r\n            return this._extensionStrategy.generateColors(count, (parameters || {}).repeat)\r\n        },\r\n        reset() {\r\n            this._currentColor = 0;\r\n            this._extensionStrategy.reset();\r\n            return this\r\n        }\r\n    };\r\n    parameters = parameters || {};\r\n    const extensionMode = (parameters.extensionMode || \"\").toLowerCase();\r\n    const colors = getPalette(palette, {\r\n        type: parameters.type || \"simpleSet\",\r\n        themeDefault: themeDefaultPalette\r\n    });\r\n    if (\"alternate\" === extensionMode) {\r\n        paletteObj._extensionStrategy = getAlternateColorsStrategy(colors, parameters)\r\n    } else if (\"extrapolate\" === extensionMode) {\r\n        paletteObj._extensionStrategy = getExtrapolateColorsStrategy(colors, parameters)\r\n    } else {\r\n        paletteObj._extensionStrategy = getColorMixer(colors, parameters)\r\n    }\r\n    paletteObj.reset();\r\n    return paletteObj\r\n}\r\n\r\nfunction getAlteredPalette(originalPalette, step) {\r\n    const palette = [];\r\n    let i;\r\n    const ii = originalPalette.length;\r\n    for (i = 0; i < ii; ++i) {\r\n        palette.push(getNewColor(originalPalette[i], step))\r\n    }\r\n    return palette\r\n}\r\n\r\nfunction getNewColor(currentColor, step) {\r\n    let newColor = new _Color(currentColor).alter(step);\r\n    const lightness = getLightness(newColor);\r\n    if (lightness > 200 || lightness < 55) {\r\n        newColor = new _Color(currentColor).alter(-step / 2)\r\n    }\r\n    return newColor.toHex()\r\n}\r\n\r\nfunction getLightness(color) {\r\n    return .3 * color.r + .59 * color.g + .11 * color.b\r\n}\r\nexport function getDiscretePalette(source, size, themeDefaultPalette) {\r\n    const palette = size > 0 ? createDiscreteColors(getPalette(source, {\r\n        type: \"gradientSet\",\r\n        themeDefault: themeDefaultPalette\r\n    }), size) : [];\r\n    return {\r\n        getColor: function(index) {\r\n            return palette[index] || null\r\n        }\r\n    }\r\n}\r\n\r\nfunction createDiscreteColors(source, count) {\r\n    const colorCount = count - 1;\r\n    const sourceCount = source.length - 1;\r\n    const colors = [];\r\n    const gradient = [];\r\n    let i;\r\n\r\n    function addColor(pos) {\r\n        const k = sourceCount * pos;\r\n        const kl = _floor(k);\r\n        const kr = _ceil(k);\r\n        gradient.push(colors[kl].blend(colors[kr], k - kl).toHex())\r\n    }\r\n    for (i = 0; i <= sourceCount; ++i) {\r\n        colors.push(new _Color(source[i]))\r\n    }\r\n    if (colorCount > 0) {\r\n        for (i = 0; i <= colorCount; ++i) {\r\n            addColor(i / colorCount)\r\n        }\r\n    } else {\r\n        addColor(.5)\r\n    }\r\n    return gradient\r\n}\r\nexport function getGradientPalette(source, themeDefaultPalette) {\r\n    const palette = getPalette(source, {\r\n        type: \"gradientSet\",\r\n        themeDefault: themeDefaultPalette\r\n    });\r\n    const color1 = new _Color(palette[0]);\r\n    const color2 = new _Color(palette[1]);\r\n    return {\r\n        getColor: function(ratio) {\r\n            return 0 <= ratio && ratio <= 1 ? color1.blend(color2, ratio).toHex() : null\r\n        }\r\n    }\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;;AACD;AAGA;AAAA;AAKA;AAEA;AAAA;;;AAJA,MAAM,SAAS,KAAK,KAAK;AACzB,MAAM,QAAQ,KAAK,IAAI;;AAEvB,MAAM,WAAW,MAAM,OAAO;;AAI9B,MAAM,oBAAoB;AAC1B,MAAM,kBAAkB;AACxB,MAAM,gBAAgB;IAClB,WAAW;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAC7E,eAAe;QAAC;QAAW;QAAW;KAAU;IAChD,aAAa;QAAC;QAAW;KAAU;IACnC,aAAa;AACjB;AACA,MAAM,WAAW;IACb,CAAC,gBAAgB,EAAE;QACf,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,QAAQ;IACR,iBAAiB;QACb,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,eAAe;QACX,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,QAAQ;QACJ,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,QAAQ;QACJ,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,MAAM;QACF,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,OAAO;QACH,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,SAAS;QACL,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,QAAQ;QACJ,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QACnG,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,SAAS;QACL,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,aAAa;QACT,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,aAAa;QACT,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,eAAe;QACX,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;IACA,cAAc;QACV,WAAW;YAAC;YAAW;YAAW;YAAW;YAAW;YAAW;SAAU;QAC7E,eAAe;YAAC;YAAW;YAAW;SAAU;QAChD,aAAa;YAAC;YAAW;SAAU;QACnC,aAAa;IACjB;AACJ;AACA,IAAI;AACG,SAAS,eAAe,IAAI;IAC/B,IAAI,KAAK,MAAM,MAAM;QACjB,OAAO,sBAAsB;IACjC,OAAO;QACH,OAAO,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE;QACrB,qBAAqB,QAAQ,WAAW,OAAO,KAAK;IACxD;AACJ;AACO,SAAS,eAAe,OAAO,EAAE,KAAK;IACzC,IAAI,UAAU,UAAU,MAAM,GAAG,KAAK,KAAK,MAAM,SAAS,CAAC,EAAE,GAAG,SAAS,CAAC,EAAE,GAAG;QAC3E,oBAAoB;IACxB;IACA,QAAQ,IAAI,GAAG,QAAQ,YAAY;IACnC,QAAQ,aAAa,GAAG,QAAQ,oBAAoB;IACpD,OAAO,cAAc,SAAS,SAAS,cAAc,CAAC;AAC1D;AACO,SAAS,WAAW,OAAO,EAAE,UAAU;IAC1C,aAAa,cAAc,CAAC;IAC5B,UAAU,WAAW,CAAC,KAAK,MAAM,qBAAqB,WAAW,YAAY,GAAG,gBAAgB;IAChG,IAAI;IACJ,MAAM,OAAO,WAAW,IAAI;IAC5B,IAAI,SAAS,UAAU;QACnB,OAAO,QAAQ,KAAK,CAAC;IACzB,OAAO;QACH,IAAI,CAAA,GAAA,gLAAA,CAAA,WAAS,AAAD,EAAE,UAAU;YACpB,SAAS,QAAQ,CAAC,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,SAAS;QAC7C;QACA,IAAI,CAAC,QAAQ;YACT,SAAS,QAAQ,CAAC,iBAAiB;QACvC;IACJ;IACA,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,KAAK,CAAC,KAAK;AAC1C;AACO,SAAS,gBAAgB,IAAI,EAAE,OAAO;IACzC,MAAM,OAAO,CAAC;IACd,IAAI;IACJ,IAAI,SAAS,UAAU;QACnB,KAAK,SAAS,GAAG,QAAQ,KAAK,CAAC;IACnC,OAAO,IAAI,SAAS;QAChB,KAAK,SAAS,GAAG,SAAS,QAAQ,SAAS,IAAI,QAAQ,SAAS,CAAC,KAAK,CAAC,KAAK,KAAK;QACjF,KAAK,aAAa,GAAG,SAAS,QAAQ,aAAa,IAAI,QAAQ,aAAa,CAAC,KAAK,CAAC,KAAK,KAAK;QAC7F,KAAK,WAAW,GAAG,SAAS,QAAQ,WAAW,IAAI,QAAQ,WAAW,CAAC,KAAK,CAAC,KAAK,KAAK;QACvF,KAAK,WAAW,GAAG,QAAQ,WAAW;IAC1C;IACA,IAAI,CAAC,KAAK,WAAW,EAAE;QACnB,KAAK,WAAW,GAAG,KAAK,SAAS,IAAI,KAAK,SAAS,CAAC,EAAE;IAC1D;IACA,IAAI,KAAK,SAAS,IAAI,KAAK,aAAa,IAAI,KAAK,WAAW,EAAE;QAC1D,cAAc,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE;QAC5B,CAAA,GAAA,kLAAA,CAAA,SAAM,AAAD,EAAE,QAAQ,CAAC,YAAY,GAAG,QAAQ,CAAC,YAAY,IAAI,CAAC,GAAG;IAChE;AACJ;AACO,SAAS,eAAe,OAAO,EAAE,YAAY;IAChD,UAAU,WAAW,SAAS;QAC1B,cAAc;IAClB;IACA,OAAO,QAAQ,WAAW,IAAI,OAAO,CAAC,EAAE;AAC5C;AAEA,SAAS,QAAQ,GAAG;IAChB,IAAI,MAAM;IACV,IAAI,CAAC,IAAI,GAAG;QACR,MAAM,MAAM,GAAG,CAAC,MAAM;QACtB,IAAI,QAAQ,IAAI,MAAM,EAAE;YACpB,IAAI,CAAC,KAAK;QACd;QACA,OAAO;IACX;IACA,IAAI,CAAC,KAAK,GAAG;QACT,MAAM;IACV;AACJ;AAEA,SAAS,2BAA2B,OAAO,EAAE,UAAU;IACnD,MAAM,gBAAgB,WAAW,YAAY,GAAG,KAAK;IACrD,MAAM,eAAe,IAAI,QAAQ;QAAC;QAAG;QAAe,CAAC;KAAc;IACnE,IAAI,iBAAiB,EAAE;IAEvB,SAAS;QACL,MAAM,OAAO,aAAa,IAAI;QAC9B,iBAAiB,OAAO,kBAAkB,SAAS,QAAQ,QAAQ,KAAK,CAAC;IAC7E;IACA,OAAO;QACH,UAAU,SAAS,KAAK;YACpB,MAAM,QAAQ,cAAc,CAAC,QAAQ,QAAQ,MAAM,CAAC;YACpD,IAAI,QAAQ,QAAQ,MAAM,KAAK,QAAQ,MAAM,GAAG,GAAG;gBAC/C;YACJ;YACA,OAAO;QACX;QACA,gBAAgB,SAAS,KAAK;YAC1B,MAAM,SAAS,EAAE;YACjB,QAAQ,SAAS,WAAW,KAAK;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC;YAC9B;YACA,OAAO;QACX;QACA,OAAO;YACH,aAAa,KAAK;YAClB;QACJ;IACJ;AACJ;AAEA,SAAS,6BAA6B,OAAO,EAAE,UAAU;IACrD,OAAO;QACH,UAAU,SAAS,KAAK,EAAE,KAAK;YAC3B,MAAM,eAAe,QAAQ,MAAM;YACnC,MAAM,SAAS,OAAO,CAAC,QAAQ,CAAC,IAAI,eAAe;YACnD,MAAM,QAAQ,OAAO,CAAC,QAAQ,aAAa;YAC3C,IAAI,SAAS,GAAG;gBACZ,OAAO,SAAS,KAAK,EAAE,UAAU,EAAE,UAAU;oBACzC,MAAM,MAAM,IAAI,6IAAA,CAAA,UAAM,CAAC,OAAO,GAAG;oBACjC,IAAI,IAAI,IAAI,CAAC,GAAG;oBAChB,MAAM,WAAW,aAAa,IAAI;oBAClC,IAAI,OAAO,IAAI,KAAK;oBACpB,IAAI,OAAO,IAAI,KAAK;oBACpB,MAAM,cAAc,CAAC,aAAa,CAAC,IAAI;oBACvC,MAAM,YAAY,aAAa;oBAC/B,IAAI,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI;wBAC7B,OAAO,KAAK,GAAG,CAAC,IAAI,KAAK;oBAC7B;oBACA,IAAI,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;wBACxC,OAAO,KAAK,GAAG,CAAC,IAAI,IAAI,MAAM,CAAC,IAAI,CAAC;oBACxC;oBACA,IAAI,YAAY,GAAG;wBACf,KAAK,CAAC,OAAO,CAAC,IAAI,YAAY;oBAClC,OAAO;wBACH,KAAK,YAAY,cAAc,CAAC,OAAO,CAAC;oBAC5C;oBACA,IAAI,CAAC,GAAG,MAAM;oBACd,OAAO,6IAAA,CAAA,UAAM,CAAC,SAAS,CAAC,OAAO,CAAC,KAAK,KAAK;gBAC9C,EAAE,OAAO,OAAO,QAAQ,eAAe;YAC3C;YACA,OAAO;QACX;QACA,gBAAgB,SAAS,KAAK;YAC1B,MAAM,SAAS,EAAE;YACjB,QAAQ,SAAS,WAAW,KAAK;YACjC,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;gBAC5B,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,GAAG;YACjC;YACA,OAAO;QACX;QACA,OAAO,YAAY;IACvB;AACJ;AAEA,SAAS,cAAc,OAAO,EAAE,UAAU;IACtC,MAAM,eAAe,QAAQ,MAAM;IACnC,IAAI,kBAAkB,EAAE;IAExB,SAAS,iBAAiB,KAAK,EAAE,WAAW,EAAE,UAAU,EAAE,YAAY;QAClE,MAAM,YAAY,KAAK,KAAK,CAAC,QAAQ;QACrC,IAAI,aAAa,QAAQ,cAAc;QACvC,IAAI,IAAI;QACR,IAAI;QACJ,IAAI;QACJ,MAAO,IAAI,aAAa,MAAO;YAC3B,OAAO;YACP,IAAI,aAAa,GAAG;gBAChB,QAAQ;gBACR;YACJ;YACA,cAAc,OAAO,IAAI,KAAK,KAAK,CAAC,OAAO,KAAK;YAChD,aAAa,IAAI,CAAC,IAAI;YACtB,KAAK;QACT;QACA,OAAO,aAAa,IAAI,CAAE,SAAS,CAAC,EAAE,CAAC;YACnC,OAAO,IAAI;QACf;IACJ;IAEA,SAAS,oBAAoB,GAAG,EAAE,UAAU,EAAE,KAAK;QAC/C,aAAa,CAAC,QAAQ,UAAU,IAAI;QACpC,IAAI,WAAW;QACf,IAAK,IAAI,IAAI,YAAY,IAAI,IAAI,OAAO,KAAK,EAAG;YAC5C,MAAM,QAAQ,CAAC,QAAQ,CAAC,IAAI;YAC5B,IAAI,GAAG,CAAC,MAAM,EAAE;gBACZ,OAAO;oBAAC,GAAG,CAAC,MAAM;oBAAE;iBAAS;YACjC;YACA;QACJ;IACJ;IAEA,SAAS,cAAc,KAAK;QACxB,IAAI,SAAS,cAAc;YACvB,OAAO;QACX;QACA,IAAI,SAAS,EAAE;QACf,MAAM,gBAAgB,eAAe;QACrC,IAAI,oBAAoB;QACxB,IAAI,oBAAoB,EAAE;QAC1B,IAAI,WAAW,kBAAkB,EAAE;YAC/B,oBAAoB,iBAAiB,QAAQ,GAAG,eAAe,GAAG;gBAAC;gBAAG,QAAQ;aAAE;QACpF,OAAO;YACH,oBAAoB,iBAAiB,QAAQ,GAAG,eAAe,GAAG,GAAG;gBAAC;aAAE;QAC5E;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC5B,IAAI,kBAAkB,OAAO,CAAC,KAAK,CAAC,GAAG;gBACnC,MAAM,CAAC,EAAE,GAAG,OAAO,CAAC,oBAAoB;YAC5C;QACJ;QACA,SAAS,SAAS,sBAAsB,EAAE,aAAa;YACnD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACpC,MAAM,QAAQ,sBAAsB,CAAC,EAAE;gBACvC,IAAI,CAAC,OAAO;oBACR,IAAI,SAAS,sBAAsB,CAAC,IAAI,EAAE;oBAC1C,IAAI,CAAC,QAAQ;wBACT;oBACJ,OAAO;wBACH,MAAM,KAAK,oBAAoB,wBAAwB,GAAG;wBAC1D,MAAM,SAAS,IAAI,6IAAA,CAAA,UAAM,CAAC,EAAE,CAAC,EAAE;wBAC/B,SAAS,IAAI,6IAAA,CAAA,UAAM,CAAC;wBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,EAAE,CAAC,EAAE,EAAE,KAAK,IAAK;4BACjC,sBAAsB,CAAC,EAAE,GAAG,OAAO,KAAK,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,GAAG,CAAC,GAAG,KAAK;wBACjF;oBACJ;gBACJ;YACJ;YACA,OAAO;QACX,EAAE,QAAQ;QACV,OAAO;IACX;IACA,OAAO;QACH,UAAU,SAAS,KAAK,EAAE,KAAK;YAC3B,QAAQ,SAAS,WAAW,KAAK,IAAI;YACrC,IAAI,gBAAgB,MAAM,KAAK,OAAO;gBAClC,kBAAkB,cAAc;YACpC;YACA,OAAO,eAAe,CAAC,QAAQ,MAAM;QACzC;QACA,gBAAgB,SAAS,KAAK,EAAE,MAAM;YAClC,QAAQ,SAAS,WAAW,KAAK,IAAI;YACrC,IAAI,UAAU,QAAQ,cAAc;gBAChC,MAAM,SAAS,cAAc;gBAC7B,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,cAAc,IAAK;oBAC3C,OAAO,IAAI,CAAC,MAAM,CAAC,EAAE;gBACzB;gBACA,OAAO;YACX,OAAO;gBACH,OAAO,eAAe,IAAI,cAAc,OAAO,KAAK,CAAC,GAAG,SAAS,EAAE;YACvE;QACJ;QACA,OAAO,YAAY;IACvB;AACJ;AACO,SAAS,cAAc,OAAO,EAAE,UAAU,EAAE,mBAAmB;IAClE,MAAM,aAAa;QACf;YACI,IAAI,CAAC,kBAAkB,GAAG;QAC9B;QACA,cAAa,KAAK;YACd,OAAO,IAAI,CAAC,kBAAkB,CAAC,QAAQ,CAAC,IAAI,CAAC,aAAa,IAAI;QAClE;QACA,gBAAe,KAAK,EAAE,UAAU;YAC5B,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,EAAE,MAAM;QAClF;QACA;YACI,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,kBAAkB,CAAC,KAAK;YAC7B,OAAO,IAAI;QACf;IACJ;IACA,aAAa,cAAc,CAAC;IAC5B,MAAM,gBAAgB,CAAC,WAAW,aAAa,IAAI,EAAE,EAAE,WAAW;IAClE,MAAM,SAAS,WAAW,SAAS;QAC/B,MAAM,WAAW,IAAI,IAAI;QACzB,cAAc;IAClB;IACA,IAAI,gBAAgB,eAAe;QAC/B,WAAW,kBAAkB,GAAG,2BAA2B,QAAQ;IACvE,OAAO,IAAI,kBAAkB,eAAe;QACxC,WAAW,kBAAkB,GAAG,6BAA6B,QAAQ;IACzE,OAAO;QACH,WAAW,kBAAkB,GAAG,cAAc,QAAQ;IAC1D;IACA,WAAW,KAAK;IAChB,OAAO;AACX;AAEA,SAAS,kBAAkB,eAAe,EAAE,IAAI;IAC5C,MAAM,UAAU,EAAE;IAClB,IAAI;IACJ,MAAM,KAAK,gBAAgB,MAAM;IACjC,IAAK,IAAI,GAAG,IAAI,IAAI,EAAE,EAAG;QACrB,QAAQ,IAAI,CAAC,YAAY,eAAe,CAAC,EAAE,EAAE;IACjD;IACA,OAAO;AACX;AAEA,SAAS,YAAY,YAAY,EAAE,IAAI;IACnC,IAAI,WAAW,IAAI,6IAAA,CAAA,UAAM,CAAC,cAAc,KAAK,CAAC;IAC9C,MAAM,YAAY,aAAa;IAC/B,IAAI,YAAY,OAAO,YAAY,IAAI;QACnC,WAAW,IAAI,6IAAA,CAAA,UAAM,CAAC,cAAc,KAAK,CAAC,CAAC,OAAO;IACtD;IACA,OAAO,SAAS,KAAK;AACzB;AAEA,SAAS,aAAa,KAAK;IACvB,OAAO,KAAK,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC,GAAG,MAAM,MAAM,CAAC;AACvD;AACO,SAAS,mBAAmB,MAAM,EAAE,IAAI,EAAE,mBAAmB;IAChE,MAAM,UAAU,OAAO,IAAI,qBAAqB,WAAW,QAAQ;QAC/D,MAAM;QACN,cAAc;IAClB,IAAI,QAAQ,EAAE;IACd,OAAO;QACH,UAAU,SAAS,KAAK;YACpB,OAAO,OAAO,CAAC,MAAM,IAAI;QAC7B;IACJ;AACJ;AAEA,SAAS,qBAAqB,MAAM,EAAE,KAAK;IACvC,MAAM,aAAa,QAAQ;IAC3B,MAAM,cAAc,OAAO,MAAM,GAAG;IACpC,MAAM,SAAS,EAAE;IACjB,MAAM,WAAW,EAAE;IACnB,IAAI;IAEJ,SAAS,SAAS,GAAG;QACjB,MAAM,IAAI,cAAc;QACxB,MAAM,KAAK,OAAO;QAClB,MAAM,KAAK,MAAM;QACjB,SAAS,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,CAAC,MAAM,CAAC,GAAG,EAAE,IAAI,IAAI,KAAK;IAC5D;IACA,IAAK,IAAI,GAAG,KAAK,aAAa,EAAE,EAAG;QAC/B,OAAO,IAAI,CAAC,IAAI,6IAAA,CAAA,UAAM,CAAC,MAAM,CAAC,EAAE;IACpC;IACA,IAAI,aAAa,GAAG;QAChB,IAAK,IAAI,GAAG,KAAK,YAAY,EAAE,EAAG;YAC9B,SAAS,IAAI;QACjB;IACJ,OAAO;QACH,SAAS;IACb;IACA,OAAO;AACX;AACO,SAAS,mBAAmB,MAAM,EAAE,mBAAmB;IAC1D,MAAM,UAAU,WAAW,QAAQ;QAC/B,MAAM;QACN,cAAc;IAClB;IACA,MAAM,SAAS,IAAI,6IAAA,CAAA,UAAM,CAAC,OAAO,CAAC,EAAE;IACpC,MAAM,SAAS,IAAI,6IAAA,CAAA,UAAM,CAAC,OAAO,CAAC,EAAE;IACpC,OAAO;QACH,UAAU,SAAS,KAAK;YACpB,OAAO,KAAK,SAAS,SAAS,IAAI,OAAO,KAAK,CAAC,QAAQ,OAAO,KAAK,KAAK;QAC5E;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3897, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/themes.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/themes.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport {\r\n    extend\r\n} from \"../core/utils/extend\";\r\nimport {\r\n    normalizeEnum\r\n} from \"./core/utils\";\r\nimport {\r\n    current as getCurrentTheme\r\n} from \"../ui/themes\";\r\nimport {\r\n    isEmptyObject\r\n} from \"../core/utils/type\";\r\nimport lightThemes from \"../__internal/viz/core/themes/generic/light/index\";\r\nimport carmineThemes from \"../__internal/viz/core/themes/generic/carmine\";\r\nimport darkThemes from \"../__internal/viz/core/themes/generic/dark\";\r\nimport contrastThemes from \"../__internal/viz/core/themes/generic/contrast\";\r\nimport darkMoonThemes from \"../__internal/viz/core/themes/generic/darkmoon\";\r\nimport darkVioletThemes from \"../__internal/viz/core/themes/generic/darkviolet\";\r\nimport greenMistThemes from \"../__internal/viz/core/themes/generic/greenmist\";\r\nimport softBlueThemes from \"../__internal/viz/core/themes/generic/softblue\";\r\nimport materialThemes from \"../__internal/viz/core/themes/material/index\";\r\nimport fluentThemes from \"../__internal/viz/core/themes/fluent/index\";\r\nconst themes = {};\r\nconst themesMapping = {};\r\nconst themesSchemeMapping = {};\r\nconst _extend = extend;\r\nlet currentThemeName = null;\r\nlet defaultTheme;\r\nlet nextCacheUid = 0;\r\nconst widgetsCache = {};\r\nexport function getTheme(themeName) {\r\n    const name = normalizeEnum(themeName);\r\n    return themes[name] || themes[themesMapping[name] || currentTheme()]\r\n}\r\n\r\nfunction findThemeNameByName(name, scheme) {\r\n    const fullThemeKey = `${name}.${scheme}`;\r\n    return themesMapping[fullThemeKey] || themesSchemeMapping[fullThemeKey] || themesMapping[name]\r\n}\r\n\r\nfunction findThemeNameByPlatform(platform, version, scheme) {\r\n    return findThemeNameByName(platform + version, scheme) || findThemeNameByName(platform, scheme)\r\n}\r\nexport function currentTheme(themeName, colorScheme) {\r\n    if (!arguments.length) {\r\n        return currentThemeName || findThemeNameByName(getCurrentTheme()) || defaultTheme\r\n    }\r\n    const scheme = normalizeEnum(colorScheme);\r\n    currentThemeName = (null !== themeName && void 0 !== themeName && themeName.platform ? findThemeNameByPlatform(normalizeEnum(themeName.platform), themeName.version, scheme) : findThemeNameByName(normalizeEnum(themeName), scheme)) || currentThemeName;\r\n    return this\r\n}\r\n\r\nfunction getThemeInfo(themeName, splitter) {\r\n    const k = themeName.indexOf(splitter);\r\n    return k > 0 ? {\r\n        name: themeName.substring(0, k),\r\n        scheme: themeName.substring(k + 1)\r\n    } : null\r\n}\r\n\r\nfunction registerThemeName(themeName, targetThemeName) {\r\n    const themeInfo = getThemeInfo(themeName, \".\") || {\r\n        name: themeName\r\n    };\r\n    const name = themeInfo.name;\r\n    const scheme = themeInfo.scheme;\r\n    if (scheme) {\r\n        const fullThemeKey = `${name}.${scheme}`;\r\n        themesMapping[name] = themesMapping[name] || targetThemeName;\r\n        themesMapping[fullThemeKey] = targetThemeName\r\n    } else {\r\n        themesMapping[name] = targetThemeName\r\n    }\r\n}\r\nexport function registerTheme(theme, baseThemeName) {\r\n    const themeName = normalizeEnum(theme && theme.name);\r\n    if (themeName) {\r\n        theme.isDefault && (defaultTheme = themeName);\r\n        registerThemeName(themeName, themeName);\r\n        themes[themeName] = _extend(true, {}, getTheme(baseThemeName), patchTheme(theme))\r\n    }\r\n}\r\nexport function registerThemeSchemeAlias(from, to) {\r\n    themesSchemeMapping[from] = to\r\n}\r\n\r\nfunction mergeScalar(target, field, source, sourceValue) {\r\n    const _value = (null === source || void 0 === source ? void 0 : source[field]) ?? sourceValue;\r\n    if (void 0 !== _value && void 0 === target[field]) {\r\n        target[field] = _value\r\n    }\r\n}\r\n\r\nfunction mergeObject(target, field, source, sourceValue) {\r\n    const _value = (null === source || void 0 === source ? void 0 : source[field]) ?? sourceValue;\r\n    if (void 0 !== _value) {\r\n        target[field] = _extend(true, {}, _value, target[field])\r\n    }\r\n}\r\n\r\nfunction patchTheme(theme) {\r\n    theme = _extend(true, {\r\n        loadingIndicator: {\r\n            font: {}\r\n        },\r\n        export: {\r\n            font: {}\r\n        },\r\n        legend: {\r\n            font: {},\r\n            border: {}\r\n        },\r\n        title: {\r\n            font: {}\r\n        },\r\n        tooltip: {\r\n            font: {}\r\n        },\r\n        \"chart:common\": {},\r\n        \"chart:common:axis\": {\r\n            grid: {},\r\n            minorGrid: {},\r\n            tick: {},\r\n            minorTick: {},\r\n            title: {\r\n                font: {}\r\n            },\r\n            label: {\r\n                font: {}\r\n            }\r\n        },\r\n        \"chart:common:annotation\": {\r\n            font: {},\r\n            border: {}\r\n        },\r\n        chart: {\r\n            commonSeriesSettings: {\r\n                candlestick: {}\r\n            }\r\n        },\r\n        pie: {},\r\n        polar: {},\r\n        gauge: {\r\n            scale: {\r\n                tick: {},\r\n                minorTick: {},\r\n                label: {\r\n                    font: {}\r\n                }\r\n            }\r\n        },\r\n        barGauge: {},\r\n        funnel: {},\r\n        sankey: {},\r\n        map: {\r\n            background: {}\r\n        },\r\n        treeMap: {\r\n            tile: {\r\n                selectionStyle: {\r\n                    border: {}\r\n                }\r\n            },\r\n            group: {\r\n                border: {},\r\n                selectionStyle: {\r\n                    border: {}\r\n                },\r\n                label: {\r\n                    font: {}\r\n                }\r\n            }\r\n        },\r\n        rangeSelector: {\r\n            scale: {\r\n                tick: {},\r\n                minorTick: {},\r\n                label: {\r\n                    font: {}\r\n                }\r\n            },\r\n            chart: {}\r\n        },\r\n        sparkline: {},\r\n        bullet: {}\r\n    }, theme);\r\n    mergeScalar(theme.loadingIndicator, \"backgroundColor\", theme);\r\n    mergeScalar(theme.chart.commonSeriesSettings.candlestick, \"innerColor\", null, theme.backgroundColor);\r\n    mergeScalar(theme.map.background, \"color\", null, theme.backgroundColor);\r\n    mergeScalar(theme.title.font, \"color\", null, theme.primaryTitleColor);\r\n    mergeObject(theme.title, \"subtitle\", null, theme.title);\r\n    mergeScalar(theme.legend.font, \"color\", null, theme.secondaryTitleColor);\r\n    mergeScalar(theme.legend.border, \"color\", null, theme.gridColor);\r\n    patchAxes(theme);\r\n    [\"chart\", \"pie\", \"polar\", \"gauge\", \"barGauge\", \"map\", \"treeMap\", \"funnel\", \"rangeSelector\", \"sparkline\", \"bullet\", \"sankey\"].forEach((section => {\r\n        mergeScalar(theme[section], \"redrawOnResize\", theme);\r\n        mergeScalar(theme[section], \"containerBackgroundColor\", null, theme.backgroundColor);\r\n        mergeObject(theme[section], \"tooltip\", theme);\r\n        mergeObject(theme[section], \"export\", theme)\r\n    }));\r\n    [\"chart\", \"pie\", \"polar\", \"gauge\", \"barGauge\", \"map\", \"treeMap\", \"funnel\", \"rangeSelector\", \"sankey\"].forEach((section => {\r\n        mergeObject(theme[section], \"loadingIndicator\", theme);\r\n        mergeObject(theme[section], \"legend\", theme);\r\n        mergeObject(theme[section], \"title\", theme)\r\n    }));\r\n    [\"chart\", \"pie\", \"polar\"].forEach((section => {\r\n        mergeObject(theme, section, null, theme[\"chart:common\"])\r\n    }));\r\n    [\"chart\", \"polar\"].forEach((section => {\r\n        theme[section] = theme[section] || {};\r\n        mergeObject(theme[section], \"commonAxisSettings\", null, theme[\"chart:common:axis\"])\r\n    }));\r\n    [\"chart\", \"polar\", \"map\", \"pie\"].forEach((section => {\r\n        theme[section] = theme[section] || {};\r\n        mergeObject(theme[section], \"commonAnnotationSettings\", null, theme[\"chart:common:annotation\"])\r\n    }));\r\n    mergeObject(theme.rangeSelector.chart, \"commonSeriesSettings\", theme.chart);\r\n    mergeObject(theme.rangeSelector.chart, \"dataPrepareSettings\", theme.chart);\r\n    mergeScalar(theme.treeMap.group.border, \"color\", null, theme.gridColor);\r\n    mergeScalar(theme.treeMap.tile.selectionStyle.border, \"color\", null, theme.primaryTitleColor);\r\n    mergeScalar(theme.treeMap.group.selectionStyle.border, \"color\", null, theme.primaryTitleColor);\r\n    mergeScalar(theme.map.legend, \"backgroundColor\", theme);\r\n    patchMapLayers(theme);\r\n    return theme\r\n}\r\n\r\nfunction patchAxes(theme) {\r\n    const commonAxisSettings = theme[\"chart:common:axis\"];\r\n    [commonAxisSettings.grid, commonAxisSettings.minorGrid].forEach((obj => {\r\n        mergeScalar(obj, \"color\", null, theme.gridColor)\r\n    }));\r\n    [commonAxisSettings, commonAxisSettings.tick, commonAxisSettings.minorTick, commonAxisSettings.label.font].forEach((obj => {\r\n        mergeScalar(obj, \"color\", null, theme.axisColor)\r\n    }));\r\n    mergeScalar(commonAxisSettings.title.font, \"color\", null, theme.secondaryTitleColor);\r\n    mergeScalar(theme.gauge.scale.label.font, \"color\", null, theme.axisColor);\r\n    mergeScalar(theme.gauge.scale.tick, \"color\", null, theme.backgroundColor);\r\n    mergeScalar(theme.gauge.scale.minorTick, \"color\", null, theme.backgroundColor);\r\n    mergeScalar(theme.rangeSelector.scale.label.font, \"color\", null, theme.axisColor)\r\n}\r\n\r\nfunction patchMapLayers(theme) {\r\n    const map = theme.map;\r\n    [\"area\", \"line\", \"marker\"].forEach((section => {\r\n        mergeObject(map, \"layer:\" + section, null, map.layer)\r\n    }));\r\n    [\"dot\", \"bubble\", \"pie\", \"image\"].forEach((section => {\r\n        mergeObject(map, \"layer:marker:\" + section, null, map[\"layer:marker\"])\r\n    }))\r\n}\r\nexport function addCacheItem(target) {\r\n    const cacheUid = ++nextCacheUid;\r\n    target._cache = cacheUid;\r\n    widgetsCache[cacheUid] = target\r\n}\r\nexport function removeCacheItem(target) {\r\n    delete widgetsCache[target._cache]\r\n}\r\nexport function refreshTheme() {\r\n    Object.keys(widgetsCache).forEach((key => {\r\n        widgetsCache[key].refresh()\r\n    }));\r\n    return this\r\n}\r\nif (isEmptyObject(themes) && isEmptyObject(themesMapping) && !defaultTheme) {\r\n    [].concat(lightThemes, carmineThemes, darkThemes, contrastThemes, darkMoonThemes, darkVioletThemes, greenMistThemes, softBlueThemes, materialThemes, fluentThemes).forEach((t => {\r\n        registerTheme(t.theme, t.baseThemeName)\r\n    }))\r\n}\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;;;;;;;AACD;AAAA;AAGA;AAGA;AAGA;AAAA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;AACA,MAAM,SAAS,CAAC;AAChB,MAAM,gBAAgB,CAAC;AACvB,MAAM,sBAAsB,CAAC;AAC7B,MAAM,UAAU,kLAAA,CAAA,SAAM;AACtB,IAAI,mBAAmB;AACvB,IAAI;AACJ,IAAI,eAAe;AACnB,MAAM,eAAe,CAAC;AACf,SAAS,SAAS,SAAS;IAC9B,MAAM,OAAO,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE;IAC3B,OAAO,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,aAAa,CAAC,KAAK,IAAI,eAAe;AACxE;AAEA,SAAS,oBAAoB,IAAI,EAAE,MAAM;IACrC,MAAM,eAAe,AAAC,GAAU,OAAR,MAAK,KAAU,OAAP;IAChC,OAAO,aAAa,CAAC,aAAa,IAAI,mBAAmB,CAAC,aAAa,IAAI,aAAa,CAAC,KAAK;AAClG;AAEA,SAAS,wBAAwB,QAAQ,EAAE,OAAO,EAAE,MAAM;IACtD,OAAO,oBAAoB,WAAW,SAAS,WAAW,oBAAoB,UAAU;AAC5F;AACO,SAAS,aAAa,SAAS,EAAE,WAAW;IAC/C,IAAI,CAAC,UAAU,MAAM,EAAE;QACnB,OAAO,oBAAoB,oBAAoB,CAAA,GAAA,oJAAA,CAAA,UAAe,AAAD,QAAQ;IACzE;IACA,MAAM,SAAS,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE;IAC7B,mBAAmB,CAAC,SAAS,aAAa,KAAK,MAAM,aAAa,UAAU,QAAQ,GAAG,wBAAwB,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,UAAU,QAAQ,GAAG,UAAU,OAAO,EAAE,UAAU,oBAAoB,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,YAAY,OAAO,KAAK;IACzO,OAAO,IAAI;AACf;AAEA,SAAS,aAAa,SAAS,EAAE,QAAQ;IACrC,MAAM,IAAI,UAAU,OAAO,CAAC;IAC5B,OAAO,IAAI,IAAI;QACX,MAAM,UAAU,SAAS,CAAC,GAAG;QAC7B,QAAQ,UAAU,SAAS,CAAC,IAAI;IACpC,IAAI;AACR;AAEA,SAAS,kBAAkB,SAAS,EAAE,eAAe;IACjD,MAAM,YAAY,aAAa,WAAW,QAAQ;QAC9C,MAAM;IACV;IACA,MAAM,OAAO,UAAU,IAAI;IAC3B,MAAM,SAAS,UAAU,MAAM;IAC/B,IAAI,QAAQ;QACR,MAAM,eAAe,AAAC,GAAU,OAAR,MAAK,KAAU,OAAP;QAChC,aAAa,CAAC,KAAK,GAAG,aAAa,CAAC,KAAK,IAAI;QAC7C,aAAa,CAAC,aAAa,GAAG;IAClC,OAAO;QACH,aAAa,CAAC,KAAK,GAAG;IAC1B;AACJ;AACO,SAAS,cAAc,KAAK,EAAE,aAAa;IAC9C,MAAM,YAAY,CAAA,GAAA,4JAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,MAAM,IAAI;IACnD,IAAI,WAAW;QACX,MAAM,SAAS,IAAI,CAAC,eAAe,SAAS;QAC5C,kBAAkB,WAAW;QAC7B,MAAM,CAAC,UAAU,GAAG,QAAQ,MAAM,CAAC,GAAG,SAAS,gBAAgB,WAAW;IAC9E;AACJ;AACO,SAAS,yBAAyB,IAAI,EAAE,EAAE;IAC7C,mBAAmB,CAAC,KAAK,GAAG;AAChC;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW;QACpC;IAAf,MAAM,SAAS,CAAA,OAAC,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,CAAC,MAAM,cAA9D,kBAAA,OAAmE;IAClF,IAAI,KAAK,MAAM,UAAU,KAAK,MAAM,MAAM,CAAC,MAAM,EAAE;QAC/C,MAAM,CAAC,MAAM,GAAG;IACpB;AACJ;AAEA,SAAS,YAAY,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,WAAW;QACpC;IAAf,MAAM,SAAS,CAAA,OAAC,SAAS,UAAU,KAAK,MAAM,SAAS,KAAK,IAAI,MAAM,CAAC,MAAM,cAA9D,kBAAA,OAAmE;IAClF,IAAI,KAAK,MAAM,QAAQ;QACnB,MAAM,CAAC,MAAM,GAAG,QAAQ,MAAM,CAAC,GAAG,QAAQ,MAAM,CAAC,MAAM;IAC3D;AACJ;AAEA,SAAS,WAAW,KAAK;IACrB,QAAQ,QAAQ,MAAM;QAClB,kBAAkB;YACd,MAAM,CAAC;QACX;QACA,QAAQ;YACJ,MAAM,CAAC;QACX;QACA,QAAQ;YACJ,MAAM,CAAC;YACP,QAAQ,CAAC;QACb;QACA,OAAO;YACH,MAAM,CAAC;QACX;QACA,SAAS;YACL,MAAM,CAAC;QACX;QACA,gBAAgB,CAAC;QACjB,qBAAqB;YACjB,MAAM,CAAC;YACP,WAAW,CAAC;YACZ,MAAM,CAAC;YACP,WAAW,CAAC;YACZ,OAAO;gBACH,MAAM,CAAC;YACX;YACA,OAAO;gBACH,MAAM,CAAC;YACX;QACJ;QACA,2BAA2B;YACvB,MAAM,CAAC;YACP,QAAQ,CAAC;QACb;QACA,OAAO;YACH,sBAAsB;gBAClB,aAAa,CAAC;YAClB;QACJ;QACA,KAAK,CAAC;QACN,OAAO,CAAC;QACR,OAAO;YACH,OAAO;gBACH,MAAM,CAAC;gBACP,WAAW,CAAC;gBACZ,OAAO;oBACH,MAAM,CAAC;gBACX;YACJ;QACJ;QACA,UAAU,CAAC;QACX,QAAQ,CAAC;QACT,QAAQ,CAAC;QACT,KAAK;YACD,YAAY,CAAC;QACjB;QACA,SAAS;YACL,MAAM;gBACF,gBAAgB;oBACZ,QAAQ,CAAC;gBACb;YACJ;YACA,OAAO;gBACH,QAAQ,CAAC;gBACT,gBAAgB;oBACZ,QAAQ,CAAC;gBACb;gBACA,OAAO;oBACH,MAAM,CAAC;gBACX;YACJ;QACJ;QACA,eAAe;YACX,OAAO;gBACH,MAAM,CAAC;gBACP,WAAW,CAAC;gBACZ,OAAO;oBACH,MAAM,CAAC;gBACX;YACJ;YACA,OAAO,CAAC;QACZ;QACA,WAAW,CAAC;QACZ,QAAQ,CAAC;IACb,GAAG;IACH,YAAY,MAAM,gBAAgB,EAAE,mBAAmB;IACvD,YAAY,MAAM,KAAK,CAAC,oBAAoB,CAAC,WAAW,EAAE,cAAc,MAAM,MAAM,eAAe;IACnG,YAAY,MAAM,GAAG,CAAC,UAAU,EAAE,SAAS,MAAM,MAAM,eAAe;IACtE,YAAY,MAAM,KAAK,CAAC,IAAI,EAAE,SAAS,MAAM,MAAM,iBAAiB;IACpE,YAAY,MAAM,KAAK,EAAE,YAAY,MAAM,MAAM,KAAK;IACtD,YAAY,MAAM,MAAM,CAAC,IAAI,EAAE,SAAS,MAAM,MAAM,mBAAmB;IACvE,YAAY,MAAM,MAAM,CAAC,MAAM,EAAE,SAAS,MAAM,MAAM,SAAS;IAC/D,UAAU;IACV;QAAC;QAAS;QAAO;QAAS;QAAS;QAAY;QAAO;QAAW;QAAU;QAAiB;QAAa;QAAU;KAAS,CAAC,OAAO,CAAE,CAAA;QAClI,YAAY,KAAK,CAAC,QAAQ,EAAE,kBAAkB;QAC9C,YAAY,KAAK,CAAC,QAAQ,EAAE,4BAA4B,MAAM,MAAM,eAAe;QACnF,YAAY,KAAK,CAAC,QAAQ,EAAE,WAAW;QACvC,YAAY,KAAK,CAAC,QAAQ,EAAE,UAAU;IAC1C;IACA;QAAC;QAAS;QAAO;QAAS;QAAS;QAAY;QAAO;QAAW;QAAU;QAAiB;KAAS,CAAC,OAAO,CAAE,CAAA;QAC3G,YAAY,KAAK,CAAC,QAAQ,EAAE,oBAAoB;QAChD,YAAY,KAAK,CAAC,QAAQ,EAAE,UAAU;QACtC,YAAY,KAAK,CAAC,QAAQ,EAAE,SAAS;IACzC;IACA;QAAC;QAAS;QAAO;KAAQ,CAAC,OAAO,CAAE,CAAA;QAC/B,YAAY,OAAO,SAAS,MAAM,KAAK,CAAC,eAAe;IAC3D;IACA;QAAC;QAAS;KAAQ,CAAC,OAAO,CAAE,CAAA;QACxB,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,CAAC;QACpC,YAAY,KAAK,CAAC,QAAQ,EAAE,sBAAsB,MAAM,KAAK,CAAC,oBAAoB;IACtF;IACA;QAAC;QAAS;QAAS;QAAO;KAAM,CAAC,OAAO,CAAE,CAAA;QACtC,KAAK,CAAC,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI,CAAC;QACpC,YAAY,KAAK,CAAC,QAAQ,EAAE,4BAA4B,MAAM,KAAK,CAAC,0BAA0B;IAClG;IACA,YAAY,MAAM,aAAa,CAAC,KAAK,EAAE,wBAAwB,MAAM,KAAK;IAC1E,YAAY,MAAM,aAAa,CAAC,KAAK,EAAE,uBAAuB,MAAM,KAAK;IACzE,YAAY,MAAM,OAAO,CAAC,KAAK,CAAC,MAAM,EAAE,SAAS,MAAM,MAAM,SAAS;IACtE,YAAY,MAAM,OAAO,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,MAAM,MAAM,iBAAiB;IAC5F,YAAY,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,CAAC,MAAM,EAAE,SAAS,MAAM,MAAM,iBAAiB;IAC7F,YAAY,MAAM,GAAG,CAAC,MAAM,EAAE,mBAAmB;IACjD,eAAe;IACf,OAAO;AACX;AAEA,SAAS,UAAU,KAAK;IACpB,MAAM,qBAAqB,KAAK,CAAC,oBAAoB;IACrD;QAAC,mBAAmB,IAAI;QAAE,mBAAmB,SAAS;KAAC,CAAC,OAAO,CAAE,CAAA;QAC7D,YAAY,KAAK,SAAS,MAAM,MAAM,SAAS;IACnD;IACA;QAAC;QAAoB,mBAAmB,IAAI;QAAE,mBAAmB,SAAS;QAAE,mBAAmB,KAAK,CAAC,IAAI;KAAC,CAAC,OAAO,CAAE,CAAA;QAChH,YAAY,KAAK,SAAS,MAAM,MAAM,SAAS;IACnD;IACA,YAAY,mBAAmB,KAAK,CAAC,IAAI,EAAE,SAAS,MAAM,MAAM,mBAAmB;IACnF,YAAY,MAAM,KAAK,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,MAAM,MAAM,SAAS;IACxE,YAAY,MAAM,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,MAAM,MAAM,eAAe;IACxE,YAAY,MAAM,KAAK,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,MAAM,MAAM,eAAe;IAC7E,YAAY,MAAM,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,EAAE,SAAS,MAAM,MAAM,SAAS;AACpF;AAEA,SAAS,eAAe,KAAK;IACzB,MAAM,MAAM,MAAM,GAAG;IACrB;QAAC;QAAQ;QAAQ;KAAS,CAAC,OAAO,CAAE,CAAA;QAChC,YAAY,KAAK,WAAW,SAAS,MAAM,IAAI,KAAK;IACxD;IACA;QAAC;QAAO;QAAU;QAAO;KAAQ,CAAC,OAAO,CAAE,CAAA;QACvC,YAAY,KAAK,kBAAkB,SAAS,MAAM,GAAG,CAAC,eAAe;IACzE;AACJ;AACO,SAAS,aAAa,MAAM;IAC/B,MAAM,WAAW,EAAE;IACnB,OAAO,MAAM,GAAG;IAChB,YAAY,CAAC,SAAS,GAAG;AAC7B;AACO,SAAS,gBAAgB,MAAM;IAClC,OAAO,YAAY,CAAC,OAAO,MAAM,CAAC;AACtC;AACO,SAAS;IACZ,OAAO,IAAI,CAAC,cAAc,OAAO,CAAE,CAAA;QAC/B,YAAY,CAAC,IAAI,CAAC,OAAO;IAC7B;IACA,OAAO,IAAI;AACf;AACA,IAAI,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,WAAW,CAAA,GAAA,gLAAA,CAAA,gBAAa,AAAD,EAAE,kBAAkB,CAAC,cAAc;IACxE,EAAE,CAAC,MAAM,CAAC,2MAAA,CAAA,UAAW,EAAE,oMAAA,CAAA,UAAa,EAAE,iMAAA,CAAA,UAAU,EAAE,qMAAA,CAAA,UAAc,EAAE,qMAAA,CAAA,UAAc,EAAE,uMAAA,CAAA,UAAgB,EAAE,sMAAA,CAAA,UAAe,EAAE,qMAAA,CAAA,UAAc,EAAE,mMAAA,CAAA,UAAc,EAAE,iMAAA,CAAA,UAAY,EAAE,OAAO,CAAE,CAAA;QACxK,cAAc,EAAE,KAAK,EAAE,EAAE,aAAa;IAC1C;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4240, "column": 0}, "map": {"version": 3, "sources": ["file:///c:/Users/<USER>/source/repos/omsnext/omsnext.nextjs/node_modules/devextreme/esm/viz/chart.js"], "sourcesContent": ["/**\r\n * DevExtreme (esm/viz/chart.js)\r\n * Version: 25.1.3\r\n * Build date: Wed Jun 25 2025\r\n *\r\n * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED\r\n * Read about DevExtreme licensing here: https://js.devexpress.com/Licensing/\r\n */\r\nimport Chart from \"../__internal/viz/m_chart\";\r\nexport default Chart;\r\n"], "names": [], "mappings": "AAAA;;;;;;;CAOC;;;AACD;;uCACe,uKAAA,CAAA,UAAK", "ignoreList": [0], "debugId": null}}]}