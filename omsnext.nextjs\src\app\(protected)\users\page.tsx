"use client";

import { useEffect, useState } from "react";
import DataGrid, {
  <PERSON>umn,
  Paging,
  Pager,
  Editing,
  Selection,
  FilterRow,
  <PERSON>er<PERSON>ilter,
  SearchPanel,
  Toolbar as DataGridToolbar,
  Item as ToolbarItem,
} from "devextreme-react/data-grid";
import <PERSON>ton from "devextreme-react/button";
import TextBox from "devextreme-react/text-box";
import { useAuth } from "@/hooks/useAuth";
import PermissionGuard from "@/components/PermissionGuard";
import { hasPermission } from "@/lib/permissions";

interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  roles: string[];
  isActive: boolean;
  createdAt: string;
}

export default function UsersPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // In a real application, this would be an API call
    // For now, we'll use mock data
    const mockUsers: User[] = [
      {
        id: "1",
        email: "<EMAIL>",
        firstName: "Admin",
        lastName: "User",
        roles: ["admin"],
        isActive: true,
        createdAt: "2023-01-01T00:00:00Z",
      },
      {
        id: "2",
        email: "<EMAIL>",
        firstName: "Regular",
        lastName: "User",
        roles: ["user"],
        isActive: true,
        createdAt: "2023-01-02T00:00:00Z",
      },
    ];

    setUsers(mockUsers);
    setLoading(false);
  }, []);

  const handleAddUser = () => {
    // Implementation for adding a new user
    console.log("Add user");
  };

  const handleEditUser = (userId: string) => {
    // Implementation for editing a user
    console.log("Edit user", userId);
  };

  const handleDeleteUser = (userId: string) => {
    // Implementation for deleting a user
    console.log("Delete user", userId);
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <PermissionGuard permission="view_users">
      <div className="content-block">
        <h2 className="content-block-title">Felhasználók kezelése</h2>

        <DataGrid
          dataSource={users}
          showBorders={true}
          showRowLines={true}
          showColumnLines={true}
          columnAutoWidth={true}
          allowColumnResizing={true}
        >
          <DataGridToolbar>
            <ToolbarItem location="before">
              <Button
                icon="add"
                text="Új felhasználó"
                type="default"
                onClick={handleAddUser}
              />
            </ToolbarItem>
            <ToolbarItem location="after">
              <SearchPanel visible={true} />
            </ToolbarItem>
          </DataGridToolbar>

          <FilterRow visible={true} />
          <HeaderFilter visible={true} />
          <Selection mode="single" />
          <Paging defaultPageSize={10} />
          <Pager
            visible={true}
            allowedPageSizes={[5, 10, 20]}
            displayMode="full"
            showPageSizeSelector={true}
            showInfo={true}
            showNavigationButtons={true}
          />

          <Column dataField="email" caption="Email" dataType="string" />
          <Column
            dataField="firstName"
            caption="Keresztnév"
            dataType="string"
          />
          <Column dataField="lastName" caption="Vezetéknév" dataType="string" />
          <Column
            dataField="roles"
            caption="Szerepkörök"
            dataType="string"
            cellRender={(data) => data.value.join(", ")}
          />
          <Column dataField="isActive" caption="Aktív" dataType="boolean" />
          <Column
            dataField="createdAt"
            caption="Létrehozva"
            dataType="date"
            format="yyyy.MM.dd"
          />
          <Column
            type="buttons"
            caption="Műveletek"
            width={110}
            buttons={[
              {
                hint: "Szerkesztés",
                icon: "edit",
                onClick: (e) => e.row && handleEditUser(e.row.data.id),
              },
              {
                hint: "Törlés",
                icon: "trash",
                onClick: (e) => e.row && handleDeleteUser(e.row.data.id),
              },
            ]}
          />
        </DataGrid>
      </div>
    </PermissionGuard>
  );
}
