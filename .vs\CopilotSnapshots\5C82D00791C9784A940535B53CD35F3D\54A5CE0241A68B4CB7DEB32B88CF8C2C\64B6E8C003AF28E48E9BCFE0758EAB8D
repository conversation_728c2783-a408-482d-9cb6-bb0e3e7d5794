﻿using System.Windows;
using DevExpress.Xpf.Core;
using omsnext.wpf.Services;
using System.Text.RegularExpressions;

namespace omsnext.wpf;

public partial class LoginWindow : ThemedWindow
{
    private readonly ApiClient _apiClient;

    public LoginWindow()
    {
        InitializeComponent();
        _apiClient = new ApiClient();
        
        // Enter key támogatás
        KeyDown += LoginWindow_KeyDown;
        EmailTextEdit.KeyDown += Input_KeyDown;
        PasswordBoxEdit.KeyDown += Input_KeyDown;
        ResetEmailTextEdit.KeyDown += ResetInput_KeyDown;
    }

    private void LoginWindow_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == System.Windows.Input.Key.Escape)
        {
            Close();
        }
    }

    private void Input_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == System.Windows.Input.Key.Enter && LoginPanel.Visibility == Visibility.Visible)
        {
            LoginButton_Click(sender, new RoutedEventArgs());
        }
    }

    private void ResetInput_KeyDown(object sender, System.Windows.Input.KeyEventArgs e)
    {
        if (e.Key == System.Windows.Input.Key.Enter && ForgotPasswordPanel.Visibility == Visibility.Visible)
        {
            ResetPasswordButton_Click(sender, new RoutedEventArgs());
        }
    }

    private async void LoginButton_Click(object sender, RoutedEventArgs e)
    {
        HideMessages();
        SetLoadingState(true);

        try
        {
            var email = EmailTextEdit.Text?.Trim();
            var password = PasswordBoxEdit.Password;

            // Validálás
            if (string.IsNullOrEmpty(email))
            {
                ShowError("Kérjük, adja meg az e-mail címét!");
                return;
            }

            if (!IsValidEmail(email))
            {
                ShowError("Kérjük, adjon meg egy érvényes e-mail címet!");
                return;
            }

            if (string.IsNullOrEmpty(password))
            {
                ShowError("Kérjük, adja meg a jelszavát!");
                return;
            }

            if (password.Length < 6)
            {
                ShowError("A jelszó legalább 6 karakter hosszú kell legyen!");
                return;
            }

            // Bejelentkezés
            var loginResponse = await _apiClient.LoginAsync(email, password);

            if (loginResponse != null && !string.IsNullOrEmpty(loginResponse.Token))
            {
                var mainWindow = new MainWindow(loginResponse, _apiClient);
                mainWindow.Show();
                this.Close();
            }
            else
            {
                ShowError("Hibás e-mail cím vagy jelszó!");
                // Focus visszaállítása a jelszó mezőre
                PasswordBoxEdit.Focus();
                PasswordBoxEdit.SelectAll();
            }
        }
        catch (Exception ex)
        {
            ShowError($"Kapcsolódási hiba: {ex.Message}");
        }
        finally
        {
            SetLoadingState(false);
        }
    }

    private void ForgotPasswordLink_Click(object sender, RoutedEventArgs e)
    {
        // Átváltás elfelejtett jelszó módra
        LoginPanel.Visibility = Visibility.Collapsed;
        ForgotPasswordPanel.Visibility = Visibility.Visible;
        HideMessages();
        
        // Email másolása ha már be van írva
        if (!string.IsNullOrEmpty(EmailTextEdit.Text))
        {
            ResetEmailTextEdit.Text = EmailTextEdit.Text;
        }
        
        ResetEmailTextEdit.Focus();
    }

    private void BackButton_Click(object sender, RoutedEventArgs e)
    {
        // Vissza a bejelentkezéshez
        ForgotPasswordPanel.Visibility = Visibility.Collapsed;
        LoginPanel.Visibility = Visibility.Visible;
        HideMessages();
        EmailTextEdit.Focus();
    }

    private async void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
    {
        HideMessages();

        var email = ResetEmailTextEdit.Text?.Trim();

        // Validálás
        if (string.IsNullOrEmpty(email))
        {
            ShowError("Kérjük, adja meg az e-mail címét!");
            return;
        }

        if (!IsValidEmail(email))
        {
            ShowError("Kérjük, adjon meg egy érvényes e-mail címet!");
            return;
        }

        try
        {
            SetLoadingState(true);
            
            // TODO: Itt hívd meg az API-t a jelszó visszaállításhoz
            // var success = await _apiClient.RequestPasswordResetAsync(email);
            
            // Egyelőre szimuláljuk a sikeres küldést
            await Task.Delay(2000); // Szimulált várakozás
            var success = true; // Szimulált siker

            if (success)
            {
                ShowSuccess($"Jelszó visszaállítási linket küldtünk a {email} címre. Kérjük, ellenőrizze a postafiókját!");
                
                // 3 másodperc után visszatérés a login panelhez
                await Task.Delay(3000);
                BackButton_Click(sender, e);
            }
            else
            {
                ShowError("Nem található felhasználó ezzel az e-mail címmel!");
            }
        }
        catch (Exception ex)
        {
            ShowError($"Hiba történt: {ex.Message}");
        }
        finally
        {
            SetLoadingState(false);
        }
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Application.Current.Shutdown();
    }

    private void SetLoadingState(bool isLoading)
    {
        LoginButton.IsEnabled = !isLoading;
        ResetPasswordButton.IsEnabled = !isLoading;
        EmailTextEdit.IsEnabled = !isLoading;
        PasswordBoxEdit.IsEnabled = !isLoading;
        ResetEmailTextEdit.IsEnabled = !isLoading;
        
        LoadingIndicator.Visibility = isLoading ? Visibility.Visible : Visibility.Collapsed;
        LoadingIndicator.DeferedVisibility = isLoading;
    }

    private void ShowError(string message)
    {
        ErrorMessage.Text = message;
        ErrorPanel.Visibility = Visibility.Visible;
        SuccessPanel.Visibility = Visibility.Collapsed;
    }

    private void ShowSuccess(string message)
    {
        SuccessMessage.Text = message;
        SuccessPanel.Visibility = Visibility.Visible;
        ErrorPanel.Visibility = Visibility.Collapsed;
    }

    private void HideMessages()
    {
        ErrorPanel.Visibility = Visibility.Collapsed;
        SuccessPanel.Visibility = Visibility.Collapsed;
    }

    private bool IsValidEmail(string email)
    {
        try
        {
            var emailRegex = new Regex(@"^[^@\s]+@[^@\s]+\.[^@\s]+$");
            return emailRegex.IsMatch(email);
        }
        catch
        {
            return false;
        }
    }
}