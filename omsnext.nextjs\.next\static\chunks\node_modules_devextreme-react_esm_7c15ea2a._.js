(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/node_modules/devextreme-react/esm/core/configuration/utils.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "isIE": ()=>isIE,
    "mergeNameParts": ()=>mergeNameParts,
    "parseOptionName": ()=>parseOptionName,
    "shallowEquals": ()=>shallowEquals
});
function mergeNameParts() {
    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
        args[_key] = arguments[_key];
    }
    return args.filter((value)=>value).join('.');
}
function parseOptionName(name) {
    const parts = name.split('[');
    if (parts.length === 1) {
        return {
            isCollectionItem: false,
            name
        };
    }
    return {
        isCollectionItem: true,
        name: parts[0],
        index: Number(parts[1].slice(0, -1))
    };
}
const isIE = ()=>{
    var _window_navigator, _window;
    var _window_navigator_userAgent;
    const ua = (_window_navigator_userAgent = (_window = window) === null || _window === void 0 ? void 0 : (_window_navigator = _window.navigator) === null || _window_navigator === void 0 ? void 0 : _window_navigator.userAgent) !== null && _window_navigator_userAgent !== void 0 ? _window_navigator_userAgent : ''; // Check the userAgent property of the window.navigator object
    const msie = ua.indexOf('MSIE'); // IE 10 or older
    const trident = ua.indexOf('Trident/'); // IE 11
    return msie > 0 || trident > 0;
};
const shallowEquals = (first, second)=>{
    if (Object.keys(first).length !== Object.keys(second).length) {
        return false;
    }
    return Object.keys(first).every((key)=>first[key] === second[key]);
};
}),
"[project]/node_modules/devextreme-react/esm/core/widget-config.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "elementPropNames": ()=>elementPropNames,
    "getClassName": ()=>getClassName,
    "separateProps": ()=>separateProps
});
const elementPropNames = [
    'style',
    'id'
];
const classNamePropName = 'className';
const refPropName = [
    'dropZone',
    'dialogTrigger'
];
const internalProps = {
    WidgetClass: {},
    isPortalComponent: false,
    defaults: {},
    templateProps: [],
    expectedChildren: {},
    subscribableOptions: [],
    independentEvents: [],
    useRequestAnimationFrameFlag: false,
    clearExtensions: ()=>undefined,
    renderChildren: ()=>undefined,
    beforeCreateWidget: ()=>undefined,
    afterCreateWidget: ()=>undefined,
    children: null
};
function isIgnoredProp(name) {
    return name === 'children' || name === classNamePropName || elementPropNames.includes(name) || Object.prototype.hasOwnProperty.call(internalProps, name);
}
function getRefElement(value) {
    if (value === null || value === void 0 ? void 0 : value.current) {
        var _value_current_instance, _value_current;
        if ((_value_current_instance = (_value_current = value.current).instance) === null || _value_current_instance === void 0 ? void 0 : _value_current_instance.call(_value_current).element()) {
            return value.current.instance().element();
        }
        return value.current;
    }
    return value;
}
function separateProps(props, defaultsProps, templateProps) {
    templateProps = templateProps || [];
    const defaults = {};
    const options = {};
    const templates = {};
    const knownTemplates = {};
    templateProps.forEach((value)=>{
        knownTemplates[value.component] = true;
        knownTemplates[value.render] = true;
    });
    Object.keys(props).forEach((key)=>{
        const defaultOptionName = defaultsProps ? defaultsProps[key] : null;
        const value = props[key];
        if (isIgnoredProp(key)) {
            return;
        }
        if (defaultOptionName) {
            defaults[defaultOptionName] = value;
            return;
        }
        if (knownTemplates[key]) {
            templates[key] = value;
            return;
        }
        if (refPropName.includes(key)) {
            options[key] = getRefElement(value);
            return;
        }
        options[key] = props[key];
    });
    return {
        options,
        defaults,
        templates
    };
}
function getClassName(props) {
    return props[classNamePropName];
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/configuration/react/templates.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "getAnonymousTemplate": ()=>getAnonymousTemplate,
    "getNamedTemplate": ()=>getNamedTemplate
});
function getAnonymousTemplate(props, templateMeta, hasTranscludedContent) {
    if (templateMeta.tmplOption === 'template' && hasTranscludedContent) {
        return {
            optionName: templateMeta.tmplOption,
            isAnonymous: true,
            type: 'children',
            content: props.children
        };
    }
    if (props[templateMeta.render]) {
        return {
            optionName: templateMeta.tmplOption,
            isAnonymous: true,
            type: 'render',
            content: props[templateMeta.render]
        };
    }
    if (props[templateMeta.component]) {
        return {
            optionName: templateMeta.tmplOption,
            isAnonymous: true,
            type: 'component',
            content: props[templateMeta.component]
        };
    }
    return null;
}
function getNamedTemplate(props) {
    if (!props.name) {
        return null;
    }
    if (props.component) {
        return {
            optionName: props.name,
            isAnonymous: false,
            type: 'component',
            content: props.component
        };
    }
    if (props.render) {
        return {
            optionName: props.name,
            isAnonymous: false,
            type: 'render',
            content: props.render
        };
    }
    return {
        optionName: props.name,
        isAnonymous: false,
        type: 'children',
        content: props.children
    };
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "NestedOptionContext": ()=>NestedOptionContext,
    "RemovalLockerContext": ()=>RemovalLockerContext,
    "RestoreTreeContext": ()=>RestoreTreeContext,
    "TemplateRenderingContext": ()=>TemplateRenderingContext
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
const RemovalLockerContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const RestoreTreeContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const NestedOptionContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    parentExpectedChildren: {},
    parentFullName: '',
    onChildOptionsReady: ()=>undefined,
    onNamedTemplateReady: ()=>undefined,
    getOptionComponentKey: ()=>0,
    treeUpdateToken: Symbol('initial tree update token'),
    parentType: 'component'
});
const TemplateRenderingContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createContext"])({
    isTemplateRendering: false
});
}),
"[project]/node_modules/devextreme-react/esm/core/configuration/config-node.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "buildNodeFullName": ()=>buildNodeFullName,
    "createConfigBuilder": ()=>createConfigBuilder
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$widget$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/widget-config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$templates$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/react/templates.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)");
;
;
;
;
function buildNodeFullName(node) {
    let currentNode = node;
    let fullName = '';
    while(currentNode && currentNode.name){
        fullName = currentNode.name.concat(typeof currentNode.index === 'number' ? "[".concat(currentNode.index, "]") : '', fullName ? ".".concat(fullName) : '');
        currentNode = currentNode.parentNode;
    }
    return fullName;
}
const renderContextValue = {
    isTemplateRendering: true
};
const createConfigBuilder = (optionElement, parentFullName)=>{
    const separatedValues = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$widget$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["separateProps"])(optionElement.props, optionElement.descriptor.initialValuesProps, optionElement.descriptor.templates);
    return {
        node: {
            name: optionElement.descriptor.name,
            predefinedOptions: optionElement.descriptor.predefinedValuesProps,
            initialOptions: separatedValues.defaults,
            options: separatedValues.options,
            templates: [],
            configCollections: {},
            configs: {}
        },
        configCollectionMaps: {},
        getConfigCollectionData (name) {
            if (!this.node.configCollections[name]) {
                this.node.configCollections[name] = [];
                this.configCollectionMaps[name] = {};
            }
            return [
                this.node.configCollections[name],
                this.configCollectionMaps[name]
            ];
        },
        addChildNode (name, childNode) {
            childNode.parentNode = this.node;
            this.node.configs[name] = childNode;
        },
        addCollectionNode (name, collectionNode, collectionNodeKey) {
            const [collection, collectionMap] = this.getConfigCollectionData(name);
            var _collectionMap_collectionNodeKey;
            const itemIndex = (_collectionMap_collectionNodeKey = collectionMap[collectionNodeKey]) !== null && _collectionMap_collectionNodeKey !== void 0 ? _collectionMap_collectionNodeKey : collection.length;
            collectionNode.index = itemIndex;
            collectionNode.parentNode = this.node;
            if (itemIndex < collection.length) {
                collection[itemIndex] = collectionNode;
            } else {
                collectionMap[collectionNodeKey] = itemIndex;
                collection.push(collectionNode);
            }
        },
        addTemplate (template) {
            this.node.templates.push(template);
        },
        updateAnonymousTemplates (hasTemplateRendered) {
            this.node.templates = this.node.templates.filter((template)=>!template.isAnonymous);
            optionElement.descriptor.templates.forEach((templateMeta)=>{
                const template = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$templates$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getAnonymousTemplate"])(optionElement.props, templateMeta, hasTemplateRendered && (optionElement.descriptor.isCollection || parentFullName.length > 0));
                if (template) {
                    this.node.templates.push(this.wrapTemplate(template));
                }
            });
        },
        wrapTemplate (template) {
            return template.type === 'children' ? {
                ...template,
                content: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateRenderingContext"].Provider, {
                    value: renderContextValue
                }, template.content)
            } : template;
        }
    };
};
;
}),
"[project]/node_modules/devextreme-react/esm/core/use-option-scanning.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "useOptionScanning": ()=>useOptionScanning
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$config$2d$node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/config-node.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)");
;
;
;
;
function useOptionScanning(optionElement, getHasTemplate, parentUpdateToken, parentType) {
    const parentContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"]);
    const { parentFullName } = parentContext;
    const updateToken = Symbol('update token');
    const configBuilder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$config$2d$node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createConfigBuilder"])(optionElement, parentFullName);
    const childComponentCounter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(0);
    const context = {
        parentExpectedChildren: optionElement.descriptor.expectedChildren,
        parentFullName: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(parentFullName, optionElement.descriptor.name),
        parentType,
        treeUpdateToken: updateToken,
        getOptionComponentKey: ()=>{
            childComponentCounter.current += 1;
            return childComponentCounter.current;
        },
        onNamedTemplateReady: (template, childUpdateToken)=>{
            if (childUpdateToken !== updateToken) {
                return;
            }
            if (template) {
                configBuilder.addTemplate(template);
            }
        },
        onChildOptionsReady: (childConfigNode, childDescriptor, childUpdateToken, childComponentKey)=>{
            if (childUpdateToken !== updateToken) {
                return;
            }
            const { isCollection, name } = childDescriptor;
            if (isCollection) {
                configBuilder.addCollectionNode(name, childConfigNode, childComponentKey);
                return;
            }
            configBuilder.addChildNode(name, childConfigNode);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "useOptionScanning.useLayoutEffect": ()=>{
            configBuilder.updateAnonymousTemplates(getHasTemplate());
        }
    }["useOptionScanning.useLayoutEffect"], [
        parentUpdateToken
    ]);
    return [
        configBuilder.node,
        context
    ];
}
}),
"[project]/node_modules/devextreme-react/esm/core/configuration/tree.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "ValueType": ()=>ValueType,
    "buildConfig": ()=>buildConfig,
    "buildNode": ()=>buildNode,
    "buildTemplates": ()=>buildTemplates,
    "findValue": ()=>findValue,
    "findValueInObject": ()=>findValueInObject
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$config$2d$node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/config-node.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/utils.js [app-client] (ecmascript)");
;
;
function buildTemplates(node, optionsAccum, templatesAccum) {
    const fullName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$config$2d$node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNodeFullName"])(node);
    node.templates.forEach((template)=>{
        if (template.isAnonymous) {
            const templateName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(fullName, template.optionName);
            optionsAccum[template.optionName] = templateName;
            templatesAccum[templateName] = template;
        } else {
            templatesAccum[template.optionName] = template;
        }
    });
}
function buildNode(node, templatesAccum, ignoreInitialValues) {
    const result = {};
    Object.keys(node.predefinedOptions).forEach((key)=>{
        result[key] = node.predefinedOptions[key];
    });
    Object.keys(node.configs).forEach((key)=>{
        result[key] = buildNode(node.configs[key], templatesAccum, ignoreInitialValues);
    });
    Object.keys(node.configCollections).forEach((key)=>{
        result[key] = node.configCollections[key].map((item)=>buildNode(item, templatesAccum, ignoreInitialValues));
    });
    if (!ignoreInitialValues) {
        Object.keys(node.initialOptions).forEach((key)=>{
            result[key] = node.initialOptions[key];
        });
    }
    Object.keys(node.options).forEach((key)=>{
        result[key] = node.options[key];
    });
    buildTemplates(node, result, templatesAccum);
    return result;
}
function buildConfig(root, ignoreInitialValues) {
    const templatesAccum = {};
    const options = buildNode(root, templatesAccum, ignoreInitialValues);
    return {
        templates: templatesAccum,
        options
    };
}
var ValueType;
(function(ValueType) {
    ValueType[ValueType["Simple"] = 0] = "Simple";
    ValueType[ValueType["Complex"] = 1] = "Complex";
    ValueType[ValueType["Array"] = 2] = "Array";
})(ValueType || (ValueType = {}));
function findValueInObject(obj, path) {
    const key = path.shift();
    if (!key) {
        return {
            value: obj,
            type: ValueType.Simple
        };
    }
    if (obj instanceof Object && Object.keys(obj).includes(key)) {
        return findValueInObject(obj[key], path);
    }
    return undefined;
}
function findValue(node, path) {
    const name = path.shift();
    if (!name) {
        return {
            value: buildConfig(node, true).options,
            type: ValueType.Complex
        };
    }
    const optionInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["parseOptionName"])(name);
    if (optionInfo.name in node.options) {
        const options = optionInfo.isCollectionItem ? node.options[optionInfo.name][optionInfo.index] : node.options[optionInfo.name];
        return findValueInObject(options, path);
    }
    if (optionInfo.isCollectionItem) {
        const collection = node.configCollections[optionInfo.name];
        if (!collection) {
            return undefined;
        }
        const item = collection[optionInfo.index];
        if (!item) {
            return undefined;
        }
        return findValue(item, path);
    }
    const child = node.configs[optionInfo.name];
    if (child) {
        return findValue(child, path);
    }
    const childCollection = node.configCollections[optionInfo.name];
    if (childCollection) {
        if (path.length !== 0) {
            return undefined;
        }
        return {
            value: childCollection.map((item)=>buildNode(item, {}, true)),
            type: ValueType.Array
        };
    }
    return undefined;
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/configuration/comparer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "getChanges": ()=>getChanges
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$config$2d$node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/config-node.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/tree.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/utils.js [app-client] (ecmascript)");
;
;
;
function compareTemplates(current, currentFullName, prev, changesAccum) {
    const currentTemplatesOptions = {};
    const currentTemplates = {};
    const prevTemplatesOptions = {};
    const prevTemplates = {};
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildTemplates"])(current, currentTemplatesOptions, currentTemplates);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildTemplates"])(prev, prevTemplatesOptions, prevTemplates);
    changesAccum.addRemovedValues(currentTemplatesOptions, prevTemplatesOptions, currentFullName);
    // TODO: support switching to default templates
    // appendRemovedValues(currentTemplates, prevTemplates, "", changesAccum.templates);
    Object.keys(currentTemplatesOptions).forEach((key)=>{
        if (currentTemplatesOptions[key] === prevTemplatesOptions[key]) {
            return;
        }
        changesAccum.options[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(currentFullName, key)] = currentTemplatesOptions[key];
    });
    Object.keys(currentTemplates).forEach((key)=>{
        const currentTemplate = currentTemplates[key];
        const prevTemplate = prevTemplates[key];
        if (prevTemplate && currentTemplate.content === prevTemplate.content) {
            return;
        }
        changesAccum.templates[key] = currentTemplate;
    });
}
function compare(current, prev, changesAccum) {
    const fullName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$config$2d$node$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNodeFullName"])(current);
    if (!prev) {
        changesAccum.options[fullName] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNode"])(current, changesAccum.templates, true);
        return;
    }
    changesAccum.addRemovedValues(current.options, prev.options, fullName);
    changesAccum.addRemovedValues(current.configCollections, prev.configCollections, fullName);
    changesAccum.addRemovedValues(current.configs, prev.configs, fullName);
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    compareCollections(current, fullName, prev, changesAccum);
    Object.keys(current.configs).forEach((key)=>{
        compare(current.configs[key], prev.configs[key], changesAccum);
    });
    Object.keys(current.options).forEach((key)=>{
        if (current.options[key] === prev.options[key]) {
            return;
        }
        changesAccum.options[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(fullName, key)] = current.options[key];
    });
    compareTemplates(current, fullName, prev, changesAccum);
}
function appendRemovedValues(current, prev, path, changesAccum) {
    const removedKeys = Object.keys(prev).filter((key)=>!Object.keys(current).includes(key));
    removedKeys.forEach((key)=>{
        changesAccum.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(path, key));
    });
}
function getChanges(current, prev) {
    const changesAccum = {
        options: {},
        removedOptions: [],
        templates: {},
        addRemovedValues (currentOptions, prevOptions, path) {
            appendRemovedValues(currentOptions, prevOptions, path, this.removedOptions);
        }
    };
    compare(current, prev, changesAccum);
    return changesAccum;
}
function compareCollections(current, currentFullName, prev, changesAccum) {
    Object.keys(current.configCollections).forEach((key)=>{
        const currentCollection = current.configCollections[key];
        const prevCollection = prev.configCollections[key] || [];
        if (!currentCollection || currentCollection.length !== prevCollection.length) {
            const updatedCollection = [];
            currentCollection.forEach((item)=>{
                const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildNode"])(item, changesAccum.templates, true);
                updatedCollection.push(config);
            });
            changesAccum.options[(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(currentFullName, key)] = updatedCollection;
            return;
        }
        for(let i = 0; i < currentCollection.length; i += 1){
            compare(currentCollection[i], prevCollection[i], changesAccum);
        }
    });
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/helpers.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "DoubleKeyMap": ()=>DoubleKeyMap,
    "TemplateInstantiationModels": ()=>TemplateInstantiationModels,
    "capitalizeFirstLetter": ()=>capitalizeFirstLetter,
    "generateID": ()=>generateID,
    "hasExpectedChildren": ()=>hasExpectedChildren
});
function generateID() {
    return Math.random().toString(36).substring(2);
}
class DoubleKeyMap {
    set(param, value) {
        let { key1, key2 } = param;
        let innerMap = this._map.get(key1);
        if (!innerMap) {
            innerMap = new Map();
            this._map.set(key1, innerMap);
        }
        innerMap.set(key2, value);
    }
    get(param) {
        let { key1, key2 } = param;
        const innerMap = this._map.get(key1);
        return innerMap ? innerMap.get(key2) : undefined;
    }
    delete(param) {
        let { key1, key2 } = param;
        const innerMap = this._map.get(key1);
        if (!innerMap) {
            return;
        }
        innerMap.delete(key2);
        if (innerMap.size === 0) {
            this._map.delete(key1);
        }
    }
    clear() {
        this._map.clear();
    }
    get empty() {
        return this._map.size === 0;
    }
    *[Symbol.iterator]() {
        for (const [key1, innerMap] of this._map){
            for (const [key2, value] of innerMap){
                yield [
                    {
                        key1,
                        key2
                    },
                    value
                ];
            }
        }
    }
    constructor(){
        this._map = new Map();
    }
}
class TemplateInstantiationModels extends DoubleKeyMap {
}
function capitalizeFirstLetter(text) {
    if (text.length) {
        return "".concat(text[0].toUpperCase()).concat(text.substr(1));
    }
    return '';
}
function hasExpectedChildren(elementDescriptor) {
    return !!Object.keys(elementDescriptor.ExpectedChildren || {}).length;
}
}),
"[project]/node_modules/devextreme-react/esm/core/options-manager.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ /* eslint-disable no-restricted-globals */ __turbopack_context__.s({
    "OptionsManager": ()=>OptionsManager,
    "scheduleGuards": ()=>scheduleGuards,
    "unscheduleGuards": ()=>unscheduleGuards
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$comparer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/comparer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/tree.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/utils.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/helpers.js [app-client] (ecmascript)");
;
;
;
;
const optionsManagers = new Set();
let guardTimeoutHandler = -1;
let innerGuardTimeoutHandler = -1;
function unscheduleGuards() {
    clearTimeout(guardTimeoutHandler);
    clearTimeout(innerGuardTimeoutHandler);
}
function scheduleGuards() {
    unscheduleGuards();
    guardTimeoutHandler = window.setTimeout(()=>{
        innerGuardTimeoutHandler = window.setTimeout(()=>{
            optionsManagers.forEach((optionManager)=>optionManager.execGuards());
        });
    });
}
class OptionsManager {
    setInstance(instance, config, subscribableOptions, independentEvents) {
        this.instance = instance;
        this.currentConfig = config;
        this.subscribableOptions = new Set(subscribableOptions);
        this.independentEvents = new Set(independentEvents);
        optionsManagers.add(this);
    }
    getInitialOptions(rootNode) {
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildConfig"])(rootNode, false);
        const options = {};
        Object.keys(config.options).forEach((key)=>{
            options[key] = this.wrapOptionValue(key, config.options[key]);
        });
        return options;
    }
    getTemplateOptions(rootNode) {
        const config = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["buildConfig"])(rootNode, false);
        return config.templates;
    }
    update(config, dxtemplates) {
        const changedOptions = [];
        const optionChangedHandler = (param)=>{
            let { value, fullName } = param;
            changedOptions.push([
                fullName,
                value
            ]);
        };
        this.instance.on('optionChanged', optionChangedHandler);
        const changes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$comparer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getChanges"])(config, this.currentConfig);
        if (!changes.options && !changes.templates && !changes.removedOptions.length) {
            return;
        }
        this.instance.beginUpdate();
        this.isUpdating = true;
        changes.removedOptions.forEach((optionName)=>{
            this.resetOption(optionName);
        });
        if (Object.keys(dxtemplates).length > 0) {
            this.setValue('integrationOptions', {
                templates: dxtemplates
            });
        }
        Object.keys(changes.options).forEach((key)=>{
            this.setValue(key, changes.options[key]);
        });
        this.isUpdating = false;
        this.instance.off('optionChanged', optionChangedHandler);
        this.currentConfig = config;
        changedOptions.forEach((param)=>{
            let [name, value] = param;
            const currentPropValue = config.options[name];
            if (Object.prototype.hasOwnProperty.call(config.options, name) && currentPropValue !== value) {
                this.setValue(name, currentPropValue);
            }
        });
        this.instance.endUpdate();
    }
    onOptionChanged(e) {
        if (this.isUpdating) {
            return;
        }
        let valueDescriptor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findValue"])(this.currentConfig, e.fullName.split('.'));
        if (!valueDescriptor || valueDescriptor.value !== e.value) {
            this.callOptionChangeHandler(e.fullName, e.value);
        }
        valueDescriptor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findValue"])(this.currentConfig, e.fullName.split('.'));
        if (!valueDescriptor) {
            return;
        }
        const { value, type } = valueDescriptor;
        if (value instanceof Array && type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValueType"].Array) {
            for(let i = 0; i < value.length; i += 1){
                var _e_value;
                if (value[i] !== ((_e_value = e.value) === null || _e_value === void 0 ? void 0 : _e_value[i])) {
                    this.addGuard(e.fullName, value);
                    return;
                }
            }
        } else if (type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValueType"].Complex && value instanceof Object) {
            Object.keys(value).forEach((key)=>{
                var _e_value;
                if (value[key] === ((_e_value = e.value) === null || _e_value === void 0 ? void 0 : _e_value[key])) {
                    return;
                }
                this.addGuard((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["mergeNameParts"])(e.fullName, key), value[key]);
            });
        } else {
            const valuesAreEqual = value === e.value;
            const valuesAreEqualObjects = !valuesAreEqual && value instanceof Object && e.value instanceof Object && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$utils$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["shallowEquals"])(value, e.value);
            if (valuesAreEqual || valuesAreEqualObjects || this.instance.skipOptionsRollBack) {
                return;
            }
            this.addGuard(e.fullName, value);
        }
    }
    get isInstanceSet() {
        return !!this.instance;
    }
    dispose() {
        optionsManagers.delete(this);
        Object.keys(this.guards).forEach((optionName)=>{
            delete this.guards[optionName];
        });
        this.instance = null;
    }
    isOptionSubscribable(optionName) {
        return this.subscribableOptions.has(optionName);
    }
    isIndependentEvent(optionName) {
        return this.independentEvents.has(optionName);
    }
    callOptionChangeHandler(optionName, optionValue) {
        if (!this.isOptionSubscribable(optionName)) {
            return;
        }
        const parts = optionName.split('.');
        const propName = parts[parts.length - 1];
        if (propName.startsWith('on')) {
            return;
        }
        const eventName = "on".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["capitalizeFirstLetter"])(propName), "Change");
        parts[parts.length - 1] = eventName;
        const changeEvent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findValue"])(this.currentConfig, parts);
        if (!changeEvent) {
            return;
        }
        if (typeof changeEvent.value !== 'function') {
            throw new Error("Invalid value for the ".concat(eventName, " property.\n                ").concat(eventName, " must be a function."));
        }
        changeEvent.value(optionValue);
    }
    wrapOptionValue(name, value) {
        if (name.substr(0, 2) === 'on' && typeof value === 'function') {
            var _this = this;
            return function() {
                for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){
                    args[_key] = arguments[_key];
                }
                if (!_this.isUpdating || _this.isIndependentEvent(name)) {
                    value(...args);
                }
            };
        }
        return value;
    }
    addGuard(optionName, optionValue) {
        if (this.guards[optionName] !== undefined) {
            return;
        }
        const handler = ()=>{
            this.setValue(optionName, optionValue);
            delete this.guards[optionName];
        };
        this.guards[optionName] = handler;
        scheduleGuards();
    }
    execGuards() {
        Object.values(this.guards).forEach((handler)=>handler());
    }
    resetOption(name) {
        if (this.isCollectionOption(name)) {
            this.setValue(name, []);
        } else {
            this.instance.resetOption(name);
        }
    }
    isCollectionOption(name) {
        const valueDescriptor = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["findValue"])(this.currentConfig, name.split('.'));
        return (valueDescriptor === null || valueDescriptor === void 0 ? void 0 : valueDescriptor.type) === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$tree$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ValueType"].Array;
    }
    setValue(name, value) {
        if (this.guards[name]) {
            delete this.guards[name];
        }
        this.instance.option(name, this.wrapOptionValue(name, value));
    }
    constructor(){
        this.guards = {};
        this.isUpdating = false;
        this.onOptionChanged = this.onOptionChanged.bind(this);
        this.wrapOptionValue = this.wrapOptionValue.bind(this);
    }
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/template-wrapper.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "TemplateWrapper": ()=>TemplateWrapper
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component-base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)");
;
;
;
;
;
;
const createHiddenNode = (containerNodeName, ref, defaultElement)=>{
    const style = {
        display: 'none'
    };
    switch(containerNodeName){
        case 'TABLE':
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("tbody", {
                style: style,
                ref: ref
            });
        case 'TBODY':
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("tr", {
                style: style,
                ref: ref
            });
        default:
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](defaultElement, {
                style,
                ref
            });
    }
};
const TemplateWrapperComponent = (param)=>{
    let { templateFactory, data, index, container, onRemoved, onRendered } = param;
    const [removalListenerRequired, setRemovalListenerRequired] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const isRemovalLocked = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const removalLocker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TemplateWrapperComponent.useMemo[removalLocker]": ()=>({
                lock () {
                    isRemovalLocked.current = true;
                },
                unlock () {
                    isRemovalLocked.current = false;
                }
            })
    }["TemplateWrapperComponent.useMemo[removalLocker]"], []);
    const element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const hiddenNodeElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const removalListenerElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const onTemplateRemoved = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TemplateWrapperComponent.useCallback[onTemplateRemoved]": (_, args)=>{
            if ((args === null || args === void 0 ? void 0 : args.isUnmounting) || isRemovalLocked.current) {
                return;
            }
            if (element.current) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](element.current, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
            }
            if (removalListenerElement.current) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](removalListenerElement.current, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
            }
            onRemoved();
        }
    }["TemplateWrapperComponent.useCallback[onTemplateRemoved]"], [
        onRemoved
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "TemplateWrapperComponent.useLayoutEffect": ()=>{
            const el = element.current;
            if (el && el.nodeType === Node.ELEMENT_NODE) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](el, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["on"](el, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
            } else if (!removalListenerRequired) {
                setRemovalListenerRequired(true);
            } else if (removalListenerElement.current) {
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](removalListenerElement.current, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
                __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["on"](removalListenerElement.current, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
            }
            return ({
                "TemplateWrapperComponent.useLayoutEffect": ()=>{
                    const safeAppend = {
                        "TemplateWrapperComponent.useLayoutEffect.safeAppend": (child)=>{
                            if ((child === null || child === void 0 ? void 0 : child.current) && container && !container.contains(child.current)) {
                                container.appendChild(child.current);
                            }
                        }
                    }["TemplateWrapperComponent.useLayoutEffect.safeAppend"];
                    safeAppend(element);
                    safeAppend(hiddenNodeElement);
                    safeAppend(removalListenerElement);
                    if (el) {
                        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](el, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onTemplateRemoved);
                    }
                }
            })["TemplateWrapperComponent.useLayoutEffect"];
        }
    }["TemplateWrapperComponent.useLayoutEffect"], [
        onTemplateRemoved,
        removalListenerRequired,
        container
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TemplateWrapperComponent.useEffect": ()=>{
            onRendered();
        }
    }["TemplateWrapperComponent.useEffect"], [
        onRendered
    ]);
    const hiddenNode = createHiddenNode(container === null || container === void 0 ? void 0 : container.nodeName, (node)=>{
        hiddenNodeElement.current = node;
        element.current = node === null || node === void 0 ? void 0 : node.previousSibling;
    }, 'div');
    const removalListener = removalListenerRequired ? createHiddenNode(container === null || container === void 0 ? void 0 : container.nodeName, (node)=>{
        removalListenerElement.current = node;
    }, 'span') : undefined;
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RemovalLockerContext"].Provider, {
        value: removalLocker
    }, templateFactory({
        data,
        index,
        onRendered
    }), hiddenNode, removalListener)), container);
};
const TemplateWrapper = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])(TemplateWrapperComponent);
}),
"[project]/node_modules/devextreme-react/esm/core/config.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "getOption": ()=>getOption
});
let config = {
    useLegacyTemplateEngine: false
};
function setOptions(options) {
    config = {
        ...config,
        ...options
    };
}
function getOption(optionName) {
    return config[optionName];
}
const __TURBOPACK__default__export__ = setOptions;
;
}),
"[project]/node_modules/devextreme-react/esm/core/template-manager.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "TemplateManager": ()=>TemplateManager
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$template$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/template-wrapper.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/helpers.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component-base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/config.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
function normalizeProps(props) {
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOption"])('useLegacyTemplateEngine')) {
        const model = props.data;
        if (model && Object.prototype.hasOwnProperty.call(model, 'key')) {
            model.dxkey = model.key;
        }
        return model;
    }
    return props;
}
const createMapKey = (key1, key2)=>({
        key1,
        key2
    });
const unsubscribeOnRemoval = (container, onRemoved)=>{
    if (container.nodeType === Node.ELEMENT_NODE) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](container, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onRemoved);
    }
};
const subscribeOnRemoval = (container, onRemoved)=>{
    if (container.nodeType === Node.ELEMENT_NODE) {
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["on"](container, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["DX_REMOVE_EVENT"], onRemoved);
    }
};
const unwrapElement = (element)=>element.get ? element.get(0) : element;
const getRandomId = ()=>"".concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateID"])()).concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateID"])()).concat((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["generateID"])());
const TemplateManager = (param)=>{
    let { init, onTemplatesRendered } = param;
    const mounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const [instantiationModels, setInstantiationModels] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        collection: new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateInstantiationModels"]()
    });
    const [updateContext, setUpdateContext] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])();
    const widgetId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])('');
    const templateFactories = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])({});
    const { collection } = instantiationModels;
    const getRenderFunc = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "TemplateManager.useCallback[getRenderFunc]": (templateKey)=>({
                "TemplateManager.useCallback[getRenderFunc]": (param)=>{
                    let { model: data, index, container, onRendered } = param;
                    const containerElement = unwrapElement(container);
                    const key = createMapKey(data, containerElement);
                    const onRemoved = {
                        "TemplateManager.useCallback[getRenderFunc].onRemoved": ()=>{
                            if (collection.get(key)) {
                                collection.delete(key);
                                setInstantiationModels({
                                    collection
                                });
                            }
                        }
                    }["TemplateManager.useCallback[getRenderFunc].onRemoved"];
                    const hostWidgetId = widgetId.current;
                    collection.set(key, {
                        templateKey,
                        index,
                        componentKey: getRandomId(),
                        onRendered: {
                            "TemplateManager.useCallback[getRenderFunc]": ()=>{
                                unsubscribeOnRemoval(containerElement, onRemoved);
                                if (hostWidgetId === widgetId.current) {
                                    onRendered === null || onRendered === void 0 ? void 0 : onRendered();
                                }
                            }
                        }["TemplateManager.useCallback[getRenderFunc]"],
                        onRemoved
                    });
                    setInstantiationModels({
                        collection
                    });
                    return containerElement;
                }
            })["TemplateManager.useCallback[getRenderFunc]"]
    }["TemplateManager.useCallback[getRenderFunc]"], [
        collection
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "TemplateManager.useMemo": ()=>{
            function getTemplateFunction(template) {
                switch(template.type){
                    case 'children':
                        return ({
                            "TemplateManager.useMemo.getTemplateFunction": ()=>template.content
                        })["TemplateManager.useMemo.getTemplateFunction"];
                    case 'render':
                        return ({
                            "TemplateManager.useMemo.getTemplateFunction": (props)=>{
                                normalizeProps(props);
                                return template.content(props.data, props.index);
                            }
                        })["TemplateManager.useMemo.getTemplateFunction"];
                    case 'component':
                        return ({
                            "TemplateManager.useMemo.getTemplateFunction": (props)=>{
                                props = normalizeProps(props);
                                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"].bind(null, template.content)(props);
                            }
                        })["TemplateManager.useMemo.getTemplateFunction"];
                    default:
                        return ({
                            "TemplateManager.useMemo.getTemplateFunction": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"])
                        })["TemplateManager.useMemo.getTemplateFunction"];
                }
            }
            function createDXTemplates(templateOptions) {
                const factories = Object.entries(templateOptions).reduce({
                    "TemplateManager.useMemo.createDXTemplates.factories": (res, param)=>{
                        let [key, template] = param;
                        return {
                            ...res,
                            [key]: getTemplateFunction(template)
                        };
                    }
                }["TemplateManager.useMemo.createDXTemplates.factories"], {});
                templateFactories.current = factories;
                const dxTemplates = Object.keys(factories).reduce({
                    "TemplateManager.useMemo.createDXTemplates.dxTemplates": (templates, templateKey)=>{
                        templates[templateKey] = {
                            render: getRenderFunc(templateKey)
                        };
                        return templates;
                    }
                }["TemplateManager.useMemo.createDXTemplates.dxTemplates"], {});
                return dxTemplates;
            }
            function clearInstantiationModels() {
                widgetId.current = getRandomId();
                instantiationModels.collection.clear();
                setInstantiationModels({
                    ...instantiationModels
                });
            }
            function updateTemplates(onUpdated) {
                if (mounted.current) {
                    setUpdateContext({
                        onUpdated
                    });
                }
            }
            init({
                createDXTemplates,
                clearInstantiationModels,
                updateTemplates
            });
        }
    }["TemplateManager.useMemo"], [
        init,
        getRenderFunc
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TemplateManager.useEffect": ()=>{
            mounted.current = true;
            return ({
                "TemplateManager.useEffect": ()=>{
                    mounted.current = false;
                }
            })["TemplateManager.useEffect"];
        }
    }["TemplateManager.useEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "TemplateManager.useEffect": ()=>{
            if (updateContext) {
                updateContext.onUpdated();
            }
            onTemplatesRendered();
        }
    }["TemplateManager.useEffect"], [
        updateContext,
        onTemplatesRendered
    ]);
    if (instantiationModels.collection.empty) {
        return null;
    }
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], null, Array.from(instantiationModels.collection).map((param)=>{
        let [{ key1: data, key2: container }, { index, templateKey, componentKey, onRendered, onRemoved }] = param;
        subscribeOnRemoval(container, onRemoved);
        const factory = templateFactories.current[templateKey];
        if (factory) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$template$2d$wrapper$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateWrapper"], {
                key: componentKey,
                templateFactory: factory,
                data: data,
                index: index,
                container: container,
                onRemoved: onRemoved,
                onRendered: onRendered
            });
        }
        return null;
    }));
};
}),
"[project]/node_modules/devextreme-react/esm/core/template.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "Template": ()=>Template,
    "findProps": ()=>findProps
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$templates$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/react/templates.js [app-client] (ecmascript)");
'use client';
;
;
;
const Template = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((props)=>{
    const { onNamedTemplateReady, treeUpdateToken } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"]);
    const { isTemplateRendering } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateRenderingContext"]);
    const template = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$templates$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getNamedTemplate"])(props);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "Template.useLayoutEffect": ()=>{
            if (!isTemplateRendering) {
                onNamedTemplateReady(template, treeUpdateToken);
            }
        }
    }["Template.useLayoutEffect"], [
        treeUpdateToken
    ]);
    return null;
});
function findProps(child) {
    if (child.type !== Template) {
        return undefined;
    }
    return {
        name: child.props.name,
        render: child.props.render,
        component: child.props.component,
        children: child.props.children
    };
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/configuration/react/element.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "ElementType": ()=>ElementType,
    "getElementType": ()=>getElementType,
    "getOptionInfo": ()=>getOptionInfo
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$template$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/template.js [app-client] (ecmascript)");
;
var ElementType;
(function(ElementType) {
    ElementType[ElementType["Option"] = 0] = "Option";
    ElementType[ElementType["Template"] = 1] = "Template";
    ElementType[ElementType["Unknown"] = 2] = "Unknown";
})(ElementType || (ElementType = {}));
function getOptionInfo(elementDescriptor, props, parentExpectedChildren) {
    let name = elementDescriptor.OptionName;
    let isCollectionItem = elementDescriptor.IsCollectionItem;
    const expectation = parentExpectedChildren && parentExpectedChildren[name];
    if (expectation) {
        isCollectionItem = expectation.isCollectionItem;
        if (expectation.optionName) {
            name = expectation.optionName;
        }
    }
    return {
        type: ElementType.Option,
        descriptor: {
            name,
            isCollection: !!isCollectionItem,
            templates: elementDescriptor.TemplateProps || [],
            initialValuesProps: elementDescriptor.DefaultsProps || {},
            predefinedValuesProps: elementDescriptor.PredefinedProps || {},
            expectedChildren: elementDescriptor.ExpectedChildren || {}
        },
        props
    };
}
function getElementType(element) {
    const reactElement = element;
    if (!reactElement || !reactElement.type) {
        return ElementType.Unknown;
    }
    if (reactElement.type === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$template$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Template"]) {
        return ElementType.Template;
    }
    const nestedComponentMeta = reactElement.type;
    if (nestedComponentMeta.componentType === 'option') {
        return ElementType.Option;
    }
    return ElementType.Unknown;
}
;
}),
"[project]/node_modules/devextreme-react/esm/core/const.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "UNITLESS_NUMBERS_SET": ()=>UNITLESS_NUMBERS_SET
});
const UNITLESS_NUMBERS_SET = new Set([
    'animationIterationCount',
    'aspectRatio',
    'borderImageOutset',
    'borderImageSlice',
    'borderImageWidth',
    'boxFlex',
    'boxFlexGroup',
    'boxOrdinalGroup',
    'columnCount',
    'columns',
    'flex',
    'flexGrow',
    'flexPositive',
    'flexShrink',
    'flexNegative',
    'flexOrder',
    'gridArea',
    'gridRow',
    'gridRowEnd',
    'gridRowSpan',
    'gridRowStart',
    'gridColumn',
    'gridColumnEnd',
    'gridColumnSpan',
    'gridColumnStart',
    'fontWeight',
    'lineClamp',
    'lineHeight',
    'opacity',
    'order',
    'orphans',
    'scale',
    'tabSize',
    'widows',
    'zIndex',
    'zoom',
    'fillOpacity',
    'floodOpacity',
    'stopOpacity',
    'strokeDasharray',
    'strokeDashoffset',
    'strokeMiterlimit',
    'strokeOpacity',
    'strokeWidth',
    'MozAnimationIterationCount',
    'MozBoxFlex',
    'MozBoxFlexGroup',
    'MozLineClamp',
    'msAnimationIterationCount',
    'msFlex',
    'msZoom',
    'msFlexGrow',
    'msFlexNegative',
    'msFlexOrder',
    'msFlexPositive',
    'msFlexShrink',
    'msGridColumn',
    'msGridColumnSpan',
    'msGridRow',
    'msGridRowSpan',
    'WebkitAnimationIterationCount',
    'WebkitBoxFlex',
    'WebKitBoxFlexGroup',
    'WebkitBoxOrdinalGroup',
    'WebkitColumnCount',
    'WebkitColumns',
    'WebkitFlex',
    'WebkitFlexGrow',
    'WebkitFlexPositive',
    'WebkitFlexShrink',
    'WebkitLineClamp'
]);
}),
"[project]/node_modules/devextreme-react/esm/core/component-base.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "ComponentBase": ()=>ComponentBase,
    "DX_REMOVE_EVENT": ()=>DX_REMOVE_EVENT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/index.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/events.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$events$2e$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/events/events.types.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/animation/frame.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/common/core/animation/frame.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$utils$2f$common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/utils/common.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/__internal/core/utils/m_common.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/core/config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$use$2d$option$2d$scanning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/use-option-scanning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$options$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/options-manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$widget$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/widget-config.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$template$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/template-manager.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/react/element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$const$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/const.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
;
;
;
const DX_REMOVE_EVENT = 'dxremove';
(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$core$2f$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"])({
    buyNowLink: 'https://go.devexpress.com/Licensing_Installer_Watermark_DevExtremeReact.aspx',
    licensingDocLink: 'https://go.devexpress.com/Licensing_Documentation_DevExtremeReact.aspx'
});
const ComponentBase = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const { templateProps = [], defaults = {}, expectedChildren = {}, isPortalComponent = false, useRequestAnimationFrameFlag = false, subscribableOptions = [], WidgetClass, independentEvents = [], renderChildren, beforeCreateWidget = ()=>undefined, afterCreateWidget = ()=>undefined } = props;
    const [, setForceUpdateToken] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(Symbol('initial force update token'));
    const removalLocker = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RemovalLockerContext"]);
    const restoreParentLink = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RestoreTreeContext"]);
    const instance = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const element = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const portalContainer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const useDeferUpdateForTemplates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const guardsUpdateScheduled = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const childElementsDetached = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const shouldRestoreFocus = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(false);
    const optionsManager = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$options$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["OptionsManager"]());
    const childNodes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const createDXTemplates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const clearInstantiationModels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const updateTemplates = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const prevPropsRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])();
    const childrenContainerRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const { parentType } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"]);
    const [widgetConfig, context] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$use$2d$option$2d$scanning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptionScanning"])({
        type: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ElementType"].Option,
        descriptor: {
            name: '',
            isCollection: false,
            templates: templateProps,
            initialValuesProps: defaults,
            predefinedValuesProps: {},
            expectedChildren
        },
        props
    }, {
        "ComponentBase.useOptionScanning": ()=>{
            var _childrenContainerRef_current;
            return !!((_childrenContainerRef_current = childrenContainerRef.current) === null || _childrenContainerRef_current === void 0 ? void 0 : _childrenContainerRef_current.childNodes.length);
        }
    }["ComponentBase.useOptionScanning"], Symbol('initial update token'), 'component');
    const restoreTree = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[restoreTree]": ()=>{
            var _childNodes_current;
            if (childElementsDetached.current && ((_childNodes_current = childNodes.current) === null || _childNodes_current === void 0 ? void 0 : _childNodes_current.length) && element.current) {
                element.current.append(...childNodes.current);
                childElementsDetached.current = false;
            }
            if (restoreParentLink && element.current && !element.current.isConnected) {
                restoreParentLink();
            }
        }
    }["ComponentBase.useCallback[restoreTree]"], [
        childNodes.current,
        element.current,
        childElementsDetached.current,
        restoreParentLink
    ]);
    const updateCssClasses = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[updateCssClasses]": (prevProps, newProps)=>{
            const prevClassName = prevProps ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$widget$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getClassName"])(prevProps) : undefined;
            const newClassName = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$widget$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getClassName"])(newProps);
            if (prevClassName === newClassName) {
                return;
            }
            if (prevClassName) {
                const classNames = prevClassName.split(' ').filter({
                    "ComponentBase.useCallback[updateCssClasses].classNames": (c)=>c
                }["ComponentBase.useCallback[updateCssClasses].classNames"]);
                if (classNames.length) {
                    var _element_current;
                    (_element_current = element.current) === null || _element_current === void 0 ? void 0 : _element_current.classList.remove(...classNames);
                }
            }
            if (newClassName) {
                const classNames = newClassName.split(' ').filter({
                    "ComponentBase.useCallback[updateCssClasses].classNames": (c)=>c
                }["ComponentBase.useCallback[updateCssClasses].classNames"]);
                if (classNames.length) {
                    var _element_current1;
                    (_element_current1 = element.current) === null || _element_current1 === void 0 ? void 0 : _element_current1.classList.add(...classNames);
                }
            }
        }
    }["ComponentBase.useCallback[updateCssClasses]"], [
        element.current
    ]);
    const setInlineStyles = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[setInlineStyles]": (styles)=>{
            if (element.current) {
                const el = element.current;
                Object.entries(styles).forEach({
                    "ComponentBase.useCallback[setInlineStyles]": (param)=>{
                        let [name, value] = param;
                        if (typeof value === 'number' && !__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$const$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["UNITLESS_NUMBERS_SET"].has(name)) {
                            el.style[name] = "".concat(value, "px");
                        } else {
                            el.style[name] = value;
                        }
                    }
                }["ComponentBase.useCallback[setInlineStyles]"]);
            }
        }
    }["ComponentBase.useCallback[setInlineStyles]"], [
        element.current
    ]);
    const setTemplateManagerHooks = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[setTemplateManagerHooks]": (param)=>{
            let { createDXTemplates: createDXTemplatesFn, clearInstantiationModels: clearInstantiationModelsFn, updateTemplates: updateTemplatesFn } = param;
            createDXTemplates.current = createDXTemplatesFn;
            clearInstantiationModels.current = clearInstantiationModelsFn;
            updateTemplates.current = updateTemplatesFn;
        }
    }["ComponentBase.useCallback[setTemplateManagerHooks]"], [
        createDXTemplates.current,
        clearInstantiationModels.current,
        updateTemplates.current
    ]);
    const getElementProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[getElementProps]": ()=>{
            const elementProps = {
                ref: {
                    "ComponentBase.useCallback[getElementProps]": (el)=>{
                        if (el) {
                            element.current = el;
                        }
                    }
                }["ComponentBase.useCallback[getElementProps]"]
            };
            __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$widget$2d$config$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["elementPropNames"].forEach({
                "ComponentBase.useCallback[getElementProps]": (name)=>{
                    if (name in props) {
                        elementProps[name] = props[name];
                    }
                }
            }["ComponentBase.useCallback[getElementProps]"]);
            return elementProps;
        }
    }["ComponentBase.useCallback[getElementProps]"], [
        element.current,
        props
    ]);
    const scheduleTemplatesUpdate = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[scheduleTemplatesUpdate]": ()=>{
            if (guardsUpdateScheduled.current) {
                return;
            }
            guardsUpdateScheduled.current = true;
            const updateFunc = useDeferUpdateForTemplates.current ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$_$5f$internal$2f$core$2f$utils$2f$m_common$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["deferUpdate"] : __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$animation$2f$frame$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["requestAnimationFrame"];
            updateFunc({
                "ComponentBase.useCallback[scheduleTemplatesUpdate]": ()=>{
                    var _updateTemplates_current;
                    guardsUpdateScheduled.current = false;
                    (_updateTemplates_current = updateTemplates.current) === null || _updateTemplates_current === void 0 ? void 0 : _updateTemplates_current.call(updateTemplates, {
                        "ComponentBase.useCallback[scheduleTemplatesUpdate]": ()=>(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$options$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["scheduleGuards"])()
                    }["ComponentBase.useCallback[scheduleTemplatesUpdate]"]);
                }
            }["ComponentBase.useCallback[scheduleTemplatesUpdate]"]);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$options$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["unscheduleGuards"])();
        }
    }["ComponentBase.useCallback[scheduleTemplatesUpdate]"], [
        guardsUpdateScheduled.current,
        useDeferUpdateForTemplates.current,
        updateTemplates.current
    ]);
    const createWidget = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[createWidget]": (el)=>{
            var _createDXTemplates_current, _clearInstantiationModels_current;
            beforeCreateWidget();
            el = el || element.current;
            let options = {
                templatesRenderAsynchronously: true,
                ...optionsManager.current.getInitialOptions(widgetConfig)
            };
            const templateOptions = optionsManager.current.getTemplateOptions(widgetConfig);
            const dxTemplates = (_createDXTemplates_current = createDXTemplates.current) === null || _createDXTemplates_current === void 0 ? void 0 : _createDXTemplates_current.call(createDXTemplates, templateOptions);
            if (dxTemplates && Object.keys(dxTemplates).length) {
                options = {
                    ...options,
                    integrationOptions: {
                        templates: dxTemplates
                    }
                };
            }
            (_clearInstantiationModels_current = clearInstantiationModels.current) === null || _clearInstantiationModels_current === void 0 ? void 0 : _clearInstantiationModels_current.call(clearInstantiationModels);
            instance.current = new WidgetClass(el, options);
            if (!useRequestAnimationFrameFlag) {
                useDeferUpdateForTemplates.current = instance.current.option('integrationOptions.useDeferUpdateForTemplates');
            }
            optionsManager.current.setInstance(instance.current, widgetConfig, subscribableOptions, independentEvents);
            instance.current.on('optionChanged', optionsManager.current.onOptionChanged);
            afterCreateWidget();
        }
    }["ComponentBase.useCallback[createWidget]"], [
        beforeCreateWidget,
        afterCreateWidget,
        element.current,
        optionsManager.current,
        createDXTemplates.current,
        clearInstantiationModels.current,
        WidgetClass,
        useRequestAnimationFrameFlag,
        useDeferUpdateForTemplates.current,
        instance.current,
        subscribableOptions,
        independentEvents,
        widgetConfig
    ]);
    const onTemplatesRendered = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[onTemplatesRendered]": ()=>{
            var _instance_current;
            if (shouldRestoreFocus.current && ((_instance_current = instance.current) === null || _instance_current === void 0 ? void 0 : _instance_current.focus)) {
                instance.current.focus();
                shouldRestoreFocus.current = false;
            }
        }
    }["ComponentBase.useCallback[onTemplatesRendered]"], [
        shouldRestoreFocus.current,
        instance.current
    ]);
    const onComponentUpdated = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[onComponentUpdated]": ()=>{
            var _optionsManager_current, _createDXTemplates_current;
            if (parentType === 'option') {
                return;
            }
            if (!((_optionsManager_current = optionsManager.current) === null || _optionsManager_current === void 0 ? void 0 : _optionsManager_current.isInstanceSet)) {
                return;
            }
            updateCssClasses(prevPropsRef.current, props);
            const templateOptions = optionsManager.current.getTemplateOptions(widgetConfig);
            const dxTemplates = ((_createDXTemplates_current = createDXTemplates.current) === null || _createDXTemplates_current === void 0 ? void 0 : _createDXTemplates_current.call(createDXTemplates, templateOptions)) || {};
            optionsManager.current.update(widgetConfig, dxTemplates);
            scheduleTemplatesUpdate();
            prevPropsRef.current = props;
        }
    }["ComponentBase.useCallback[onComponentUpdated]"], [
        optionsManager.current,
        prevPropsRef.current,
        createDXTemplates.current,
        scheduleTemplatesUpdate,
        updateCssClasses,
        props,
        widgetConfig
    ]);
    const onComponentMounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[onComponentMounted]": ()=>{
            var _element_current;
            if (parentType === 'option') {
                return;
            }
            const { style } = props;
            if (childElementsDetached.current) {
                restoreTree();
            } else if ((_element_current = element.current) === null || _element_current === void 0 ? void 0 : _element_current.childNodes.length) {
                var _element_current1;
                childNodes.current = Array.from((_element_current1 = element.current) === null || _element_current1 === void 0 ? void 0 : _element_current1.childNodes);
            }
            updateCssClasses(undefined, props);
            if (style) {
                setInlineStyles(style);
            }
            prevPropsRef.current = props;
        }
    }["ComponentBase.useCallback[onComponentMounted]"], [
        childNodes.current,
        element.current,
        childElementsDetached.current,
        updateCssClasses,
        setInlineStyles,
        props
    ]);
    const onComponentUnmounted = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[onComponentUnmounted]": ()=>{
            removalLocker === null || removalLocker === void 0 ? void 0 : removalLocker.lock();
            if (instance.current) {
                var _element_current, _childNodes_current;
                const dxRemoveArgs = {
                    isUnmounting: true
                };
                shouldRestoreFocus.current = !!((_element_current = element.current) === null || _element_current === void 0 ? void 0 : _element_current.contains(document.activeElement));
                (_childNodes_current = childNodes.current) === null || _childNodes_current === void 0 ? void 0 : _childNodes_current.forEach({
                    "ComponentBase.useCallback[onComponentUnmounted]": (child)=>{
                        var _child_parentNode;
                        return (_child_parentNode = child.parentNode) === null || _child_parentNode === void 0 ? void 0 : _child_parentNode.removeChild(child);
                    }
                }["ComponentBase.useCallback[onComponentUnmounted]"]);
                childElementsDetached.current = true;
                if (element.current) {
                    const preventFocusOut = {
                        "ComponentBase.useCallback[onComponentUnmounted].preventFocusOut": (e)=>e.stopPropagation()
                    }["ComponentBase.useCallback[onComponentUnmounted].preventFocusOut"];
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["on"](element.current, 'focusout', preventFocusOut);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$events$2f$events$2e$types$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["triggerHandler"](element.current, DX_REMOVE_EVENT, dxRemoveArgs);
                    __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$common$2f$core$2f$events$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["off"](element.current, 'focusout', preventFocusOut);
                }
                instance.current.dispose();
                instance.current = null;
            }
            optionsManager.current.dispose();
            removalLocker === null || removalLocker === void 0 ? void 0 : removalLocker.unlock();
        }
    }["ComponentBase.useCallback[onComponentUnmounted]"], [
        removalLocker,
        instance.current,
        childNodes.current,
        element.current,
        optionsManager.current,
        childElementsDetached.current,
        shouldRestoreFocus.current
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "ComponentBase.useLayoutEffect": ()=>{
            onComponentMounted();
            return ({
                "ComponentBase.useLayoutEffect": ()=>{
                    onComponentUnmounted();
                }
            })["ComponentBase.useLayoutEffect"];
        }
    }["ComponentBase.useLayoutEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "ComponentBase.useLayoutEffect": ()=>{
            onComponentUpdated();
        }
    }["ComponentBase.useLayoutEffect"]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "ComponentBase.useImperativeHandle": ()=>({
                getInstance () {
                    return instance.current;
                },
                getElement () {
                    return element.current;
                },
                createWidget (el) {
                    createWidget(el);
                }
            })
    }["ComponentBase.useImperativeHandle"], [
        instance.current,
        element.current,
        createWidget
    ]);
    const _renderChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[_renderChildren]": ()=>{
            if (renderChildren) {
                return renderChildren();
            }
            const { children } = props;
            return children;
        }
    }["ComponentBase.useCallback[_renderChildren]"], [
        props,
        renderChildren
    ]);
    const renderPortal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[renderPortal]": ()=>portalContainer.current && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(_renderChildren(), portalContainer.current)
    }["ComponentBase.useCallback[renderPortal]"], [
        portalContainer.current,
        _renderChildren
    ]);
    const renderContent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ComponentBase.useCallback[renderContent]": ()=>{
            const { children } = props;
            return isPortalComponent && children ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]('div', {
                ref: {
                    "ComponentBase.useCallback[renderContent]": (node)=>{
                        if (node && portalContainer.current !== node) {
                            portalContainer.current = node;
                            setForceUpdateToken(Symbol('force update token'));
                        }
                    }
                }["ComponentBase.useCallback[renderContent]"],
                style: {
                    display: 'contents'
                }
            }) : _renderChildren();
        }
    }["ComponentBase.useCallback[renderContent]"], [
        props,
        isPortalComponent,
        portalContainer.current,
        _renderChildren
    ]);
    const renderContextValue = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "ComponentBase.useMemo[renderContextValue]": ()=>({
                isTemplateRendering: false
            })
    }["ComponentBase.useMemo[renderContextValue]"], []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["RestoreTreeContext"].Provider, {
        value: restoreTree
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateRenderingContext"].Provider, {
        value: renderContextValue
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"]("div", {
        ref: childrenContainerRef,
        ...getElementProps()
    }, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"].Provider, {
        value: context
    }, renderContent()), __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$template$2d$manager$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateManager"], {
        init: setTemplateManagerHooks,
        onTemplatesRendered: onTemplatesRendered
    }), isPortalComponent && __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"].Provider, {
        value: context
    }, renderPortal()))));
});
;
}),
"[project]/node_modules/devextreme-react/esm/core/extension-component.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "ExtensionComponent": ()=>ExtensionComponent,
    "elementIsExtension": ()=>elementIsExtension
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component-base.js [app-client] (ecmascript)");
;
;
;
// eslint-disable-next-line @typescript-eslint/explicit-module-boundary-types
function elementIsExtension(el) {
    var _el_type;
    return ((_el_type = el.type) === null || _el_type === void 0 ? void 0 : _el_type.componentType) === 'extension';
}
const ExtensionComponent = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const componentBaseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const createWidget = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ExtensionComponent.useCallback[createWidget]": (el)=>{
            var _componentBaseRef_current;
            (_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.createWidget(el);
        }
    }["ExtensionComponent.useCallback[createWidget]"], [
        componentBaseRef.current
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "ExtensionComponent.useLayoutEffect": ()=>{
            const { onMounted } = props;
            if (onMounted) {
                onMounted(createWidget);
            } else {
                createWidget();
            }
        }
    }["ExtensionComponent.useLayoutEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "ExtensionComponent.useImperativeHandle": ()=>({
                getInstance () {
                    var _componentBaseRef_current;
                    return (_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.getInstance();
                },
                getElement () {
                    var _componentBaseRef_current;
                    return (_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.getElement();
                },
                createWidget (el) {
                    createWidget(el);
                }
            })
    }["ExtensionComponent.useImperativeHandle"], [
        componentBaseRef.current,
        createWidget
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ComponentBase"], {
        ref: componentBaseRef,
        ...props
    });
});
;
}),
"[project]/node_modules/devextreme-react/esm/core/component.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "Component": ()=>Component
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component-base.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$extension$2d$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/extension-component.js [app-client] (ecmascript)");
;
;
;
;
const Component = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const componentBaseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    const extensionCreators = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])([]);
    const registerExtension = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Component.useCallback[registerExtension]": (creator)=>{
            extensionCreators.current.push(creator);
        }
    }["Component.useCallback[registerExtension]"], [
        extensionCreators.current
    ]);
    const createExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Component.useCallback[createExtensions]": ()=>{
            extensionCreators.current.forEach({
                "Component.useCallback[createExtensions]": (creator)=>{
                    var _componentBaseRef_current;
                    return creator((_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.getElement());
                }
            }["Component.useCallback[createExtensions]"]);
        }
    }["Component.useCallback[createExtensions]"], [
        extensionCreators.current,
        componentBaseRef.current
    ]);
    const renderChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Component.useCallback[renderChildren]": ()=>__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Children"].map(props.children, {
                "Component.useCallback[renderChildren]": (child)=>{
                    if (__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["isValidElement"](child) && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$extension$2d$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["elementIsExtension"])(child)) {
                        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cloneElement"](child, {
                            onMounted: registerExtension
                        });
                    }
                    return child;
                }
            }["Component.useCallback[renderChildren]"])
    }["Component.useCallback[renderChildren]"], [
        props,
        registerExtension
    ]);
    const createWidget = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Component.useCallback[createWidget]": (el)=>{
            var _componentBaseRef_current;
            (_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.createWidget(el);
        }
    }["Component.useCallback[createWidget]"], [
        componentBaseRef.current
    ]);
    const clearExtensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "Component.useCallback[clearExtensions]": ()=>{
            if (props.clearExtensions) {
                props.clearExtensions();
            }
            extensionCreators.current = [];
        }
    }["Component.useCallback[clearExtensions]"], [
        extensionCreators.current,
        props.clearExtensions
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "Component.useLayoutEffect": ()=>{
            createWidget();
            createExtensions();
            return ({
                "Component.useLayoutEffect": ()=>{
                    clearExtensions();
                }
            })["Component.useLayoutEffect"];
        }
    }["Component.useLayoutEffect"], []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Component.useImperativeHandle": ()=>({
                getInstance () {
                    var _componentBaseRef_current;
                    return (_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.getInstance();
                },
                getElement () {
                    var _componentBaseRef_current;
                    return (_componentBaseRef_current = componentBaseRef.current) === null || _componentBaseRef_current === void 0 ? void 0 : _componentBaseRef_current.getElement();
                },
                createWidget (el) {
                    createWidget(el);
                },
                clearExtensions () {
                    clearExtensions();
                }
            })
    }["Component.useImperativeHandle"], [
        componentBaseRef.current,
        createWidget,
        clearExtensions
    ]);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2d$base$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["ComponentBase"], {
        ref: componentBaseRef,
        renderChildren: renderChildren,
        ...props
    });
});
;
}),
"[project]/node_modules/devextreme-react/esm/core/nested-option.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react-dom/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/configuration/react/element.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$use$2d$option$2d$scanning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/use-option-scanning.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/contexts.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/helpers.js [app-client] (ecmascript)");
;
;
;
;
;
;
;
const NestedOption = function NestedOption(props) {
    var _elementDescriptor_TemplateProps;
    const { children } = props;
    const { elementDescriptor, ...restProps } = props;
    const { isTemplateRendering } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["TemplateRenderingContext"]);
    if (!elementDescriptor || typeof document === 'undefined' || isTemplateRendering) {
        return null;
    }
    const usesNamedTemplate = (_elementDescriptor_TemplateProps = elementDescriptor.TemplateProps) === null || _elementDescriptor_TemplateProps === void 0 ? void 0 : _elementDescriptor_TemplateProps.some((prop)=>props[prop.tmplOption] && typeof props[prop.tmplOption] === 'string');
    const { parentExpectedChildren, onChildOptionsReady: triggerParentOptionsReady, getOptionComponentKey, treeUpdateToken } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useContext"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"]);
    const [optionComponentKey] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(getOptionComponentKey());
    const optionElement = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$configuration$2f$react$2f$element$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getOptionInfo"])(elementDescriptor, restProps, parentExpectedChildren);
    const mainContainer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "NestedOption.useMemo[mainContainer]": ()=>document.createElement('div')
    }["NestedOption.useMemo[mainContainer]"], []);
    const renderChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$helpers$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["hasExpectedChildren"])(elementDescriptor) || usesNamedTemplate;
    const getHasTemplate = renderChildren ? ()=>!!mainContainer.childNodes.length : ()=>!!children;
    const [config, context] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$use$2d$option$2d$scanning$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useOptionScanning"])(optionElement, getHasTemplate, treeUpdateToken, 'option');
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLayoutEffect"])({
        "NestedOption.useLayoutEffect": ()=>{
            triggerParentOptionsReady(config, optionElement.descriptor, treeUpdateToken, optionComponentKey);
        }
    }["NestedOption.useLayoutEffect"], [
        treeUpdateToken
    ]);
    return renderChildren ? __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {}, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2d$dom$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createPortal"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$contexts$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["NestedOptionContext"].Provider, {
        value: context
    }, children), mainContainer)) : null;
};
const __TURBOPACK__default__export__ = NestedOption;
}),
"[project]/node_modules/devextreme-react/esm/data-grid.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "Animation": ()=>Animation,
    "AsyncRule": ()=>AsyncRule,
    "At": ()=>At,
    "BoundaryOffset": ()=>BoundaryOffset,
    "Button": ()=>Button,
    "Change": ()=>Change,
    "ColCountByScreen": ()=>ColCountByScreen,
    "Collision": ()=>Collision,
    "Column": ()=>Column,
    "ColumnChooser": ()=>ColumnChooser,
    "ColumnChooserSearch": ()=>ColumnChooserSearch,
    "ColumnChooserSelection": ()=>ColumnChooserSelection,
    "ColumnFixing": ()=>ColumnFixing,
    "ColumnFixingTexts": ()=>ColumnFixingTexts,
    "ColumnHeaderFilter": ()=>ColumnHeaderFilter,
    "ColumnHeaderFilterSearch": ()=>ColumnHeaderFilterSearch,
    "ColumnLookup": ()=>ColumnLookup,
    "CompareRule": ()=>CompareRule,
    "CursorOffset": ()=>CursorOffset,
    "CustomOperation": ()=>CustomOperation,
    "CustomRule": ()=>CustomRule,
    "DataGrid": ()=>DataGrid,
    "DataGridHeaderFilter": ()=>DataGridHeaderFilter,
    "DataGridHeaderFilterSearch": ()=>DataGridHeaderFilterSearch,
    "DataGridHeaderFilterTexts": ()=>DataGridHeaderFilterTexts,
    "DataGridSelection": ()=>DataGridSelection,
    "Editing": ()=>Editing,
    "EditingTexts": ()=>EditingTexts,
    "EmailRule": ()=>EmailRule,
    "Export": ()=>Export,
    "ExportTexts": ()=>ExportTexts,
    "Field": ()=>Field,
    "FieldLookup": ()=>FieldLookup,
    "FilterBuilder": ()=>FilterBuilder,
    "FilterBuilderPopup": ()=>FilterBuilderPopup,
    "FilterOperationDescriptions": ()=>FilterOperationDescriptions,
    "FilterPanel": ()=>FilterPanel,
    "FilterPanelTexts": ()=>FilterPanelTexts,
    "FilterRow": ()=>FilterRow,
    "Form": ()=>Form,
    "FormItem": ()=>FormItem,
    "Format": ()=>Format,
    "From": ()=>From,
    "GroupItem": ()=>GroupItem,
    "GroupOperationDescriptions": ()=>GroupOperationDescriptions,
    "GroupPanel": ()=>GroupPanel,
    "Grouping": ()=>Grouping,
    "GroupingTexts": ()=>GroupingTexts,
    "HeaderFilter": ()=>HeaderFilter,
    "Hide": ()=>Hide,
    "Icons": ()=>Icons,
    "Item": ()=>Item,
    "KeyboardNavigation": ()=>KeyboardNavigation,
    "Label": ()=>Label,
    "LoadPanel": ()=>LoadPanel,
    "Lookup": ()=>Lookup,
    "MasterDetail": ()=>MasterDetail,
    "My": ()=>My,
    "NumericRule": ()=>NumericRule,
    "Offset": ()=>Offset,
    "OperationDescriptions": ()=>OperationDescriptions,
    "Pager": ()=>Pager,
    "Paging": ()=>Paging,
    "PatternRule": ()=>PatternRule,
    "Popup": ()=>Popup,
    "Position": ()=>Position,
    "RangeRule": ()=>RangeRule,
    "RemoteOperations": ()=>RemoteOperations,
    "RequiredRule": ()=>RequiredRule,
    "RowDragging": ()=>RowDragging,
    "Scrolling": ()=>Scrolling,
    "Search": ()=>Search,
    "SearchPanel": ()=>SearchPanel,
    "Selection": ()=>Selection,
    "Show": ()=>Show,
    "SortByGroupSummaryInfo": ()=>SortByGroupSummaryInfo,
    "Sorting": ()=>Sorting,
    "StateStoring": ()=>StateStoring,
    "StringLengthRule": ()=>StringLengthRule,
    "Summary": ()=>Summary,
    "SummaryTexts": ()=>SummaryTexts,
    "Texts": ()=>Texts,
    "To": ()=>To,
    "Toolbar": ()=>Toolbar,
    "ToolbarItem": ()=>ToolbarItem,
    "TotalItem": ()=>TotalItem,
    "ValidationRule": ()=>ValidationRule,
    "ValueFormat": ()=>ValueFormat,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$data_grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/data_grid.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/nested-option.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const DataGrid = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const baseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "DataGrid.useImperativeHandle": ()=>({
                instance () {
                    var _baseRef_current;
                    return (_baseRef_current = baseRef.current) === null || _baseRef_current === void 0 ? void 0 : _baseRef_current.getInstance();
                }
            })
    }["DataGrid.useImperativeHandle"], [
        baseRef.current
    ]);
    const subscribableOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DataGrid.useMemo[subscribableOptions]": ()=>[
                "columns",
                "editing",
                "editing.changes",
                "editing.editColumnName",
                "editing.editRowKey",
                "filterValue",
                "focusedColumnIndex",
                "focusedRowIndex",
                "focusedRowKey",
                "groupPanel",
                "groupPanel.visible",
                "paging",
                "paging.pageIndex",
                "paging.pageSize",
                "selectedRowKeys",
                "selectionFilter",
                "filterBuilder.value",
                "filterBuilderPopup.height",
                "filterBuilderPopup.position",
                "filterBuilderPopup.visible",
                "filterBuilderPopup.width",
                "filterPanel.filterEnabled",
                "editing.form.formData",
                "editing.popup.height",
                "editing.popup.position",
                "editing.popup.visible",
                "editing.popup.width",
                "searchPanel.text"
            ]
    }["DataGrid.useMemo[subscribableOptions]"], []);
    const independentEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DataGrid.useMemo[independentEvents]": ()=>[
                "onAdaptiveDetailRowPreparing",
                "onCellClick",
                "onCellDblClick",
                "onCellPrepared",
                "onContentReady",
                "onContextMenuPreparing",
                "onDataErrorOccurred",
                "onDisposing",
                "onEditCanceled",
                "onEditCanceling",
                "onEditingStart",
                "onEditorPrepared",
                "onEditorPreparing",
                "onExporting",
                "onFocusedCellChanging",
                "onFocusedRowChanging",
                "onInitialized",
                "onInitNewRow",
                "onKeyDown",
                "onRowClick",
                "onRowCollapsed",
                "onRowCollapsing",
                "onRowDblClick",
                "onRowExpanded",
                "onRowExpanding",
                "onRowInserted",
                "onRowInserting",
                "onRowPrepared",
                "onRowRemoved",
                "onRowRemoving",
                "onRowUpdated",
                "onRowUpdating",
                "onRowValidating",
                "onSaved",
                "onSaving",
                "onToolbarPreparing"
            ]
    }["DataGrid.useMemo[independentEvents]"], []);
    const defaults = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DataGrid.useMemo[defaults]": ()=>({
                defaultColumns: "columns",
                defaultEditing: "editing",
                defaultFilterValue: "filterValue",
                defaultFocusedColumnIndex: "focusedColumnIndex",
                defaultFocusedRowIndex: "focusedRowIndex",
                defaultFocusedRowKey: "focusedRowKey",
                defaultGroupPanel: "groupPanel",
                defaultPaging: "paging",
                defaultSelectedRowKeys: "selectedRowKeys",
                defaultSelectionFilter: "selectionFilter"
            })
    }["DataGrid.useMemo[defaults]"], []);
    const expectedChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DataGrid.useMemo[expectedChildren]": ()=>({
                column: {
                    optionName: "columns",
                    isCollectionItem: true
                },
                columnChooser: {
                    optionName: "columnChooser",
                    isCollectionItem: false
                },
                columnFixing: {
                    optionName: "columnFixing",
                    isCollectionItem: false
                },
                dataGridHeaderFilter: {
                    optionName: "headerFilter",
                    isCollectionItem: false
                },
                dataGridSelection: {
                    optionName: "selection",
                    isCollectionItem: false
                },
                editing: {
                    optionName: "editing",
                    isCollectionItem: false
                },
                export: {
                    optionName: "export",
                    isCollectionItem: false
                },
                filterBuilder: {
                    optionName: "filterBuilder",
                    isCollectionItem: false
                },
                filterBuilderPopup: {
                    optionName: "filterBuilderPopup",
                    isCollectionItem: false
                },
                filterPanel: {
                    optionName: "filterPanel",
                    isCollectionItem: false
                },
                filterRow: {
                    optionName: "filterRow",
                    isCollectionItem: false
                },
                grouping: {
                    optionName: "grouping",
                    isCollectionItem: false
                },
                groupPanel: {
                    optionName: "groupPanel",
                    isCollectionItem: false
                },
                headerFilter: {
                    optionName: "headerFilter",
                    isCollectionItem: false
                },
                keyboardNavigation: {
                    optionName: "keyboardNavigation",
                    isCollectionItem: false
                },
                loadPanel: {
                    optionName: "loadPanel",
                    isCollectionItem: false
                },
                masterDetail: {
                    optionName: "masterDetail",
                    isCollectionItem: false
                },
                pager: {
                    optionName: "pager",
                    isCollectionItem: false
                },
                paging: {
                    optionName: "paging",
                    isCollectionItem: false
                },
                remoteOperations: {
                    optionName: "remoteOperations",
                    isCollectionItem: false
                },
                rowDragging: {
                    optionName: "rowDragging",
                    isCollectionItem: false
                },
                scrolling: {
                    optionName: "scrolling",
                    isCollectionItem: false
                },
                searchPanel: {
                    optionName: "searchPanel",
                    isCollectionItem: false
                },
                selection: {
                    optionName: "selection",
                    isCollectionItem: false
                },
                sortByGroupSummaryInfo: {
                    optionName: "sortByGroupSummaryInfo",
                    isCollectionItem: true
                },
                sorting: {
                    optionName: "sorting",
                    isCollectionItem: false
                },
                stateStoring: {
                    optionName: "stateStoring",
                    isCollectionItem: false
                },
                summary: {
                    optionName: "summary",
                    isCollectionItem: false
                },
                toolbar: {
                    optionName: "toolbar",
                    isCollectionItem: false
                }
            })
    }["DataGrid.useMemo[expectedChildren]"], []);
    const templateProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "DataGrid.useMemo[templateProps]": ()=>[
                {
                    tmplOption: "dataRowTemplate",
                    render: "dataRowRender",
                    component: "dataRowComponent"
                },
                {
                    tmplOption: "rowTemplate",
                    render: "rowRender",
                    component: "rowComponent"
                }
            ]
    }["DataGrid.useMemo[templateProps]"], []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"], {
        WidgetClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$data_grid$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        ref: baseRef,
        useRequestAnimationFrameFlag: true,
        subscribableOptions,
        independentEvents,
        defaults,
        expectedChildren,
        templateProps,
        ...props
    });
}));
const _componentAnimation = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "animation",
            ExpectedChildren: {
                hide: {
                    optionName: "hide",
                    isCollectionItem: false
                },
                show: {
                    optionName: "show",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Animation = Object.assign(_componentAnimation, {
    componentType: "option"
});
const _componentAsyncRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "async"
            }
        }
    });
};
const AsyncRule = Object.assign(_componentAsyncRule, {
    componentType: "option"
});
const _componentAt = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "at"
        }
    });
};
const At = Object.assign(_componentAt, {
    componentType: "option"
});
const _componentBoundaryOffset = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "boundaryOffset"
        }
    });
};
const BoundaryOffset = Object.assign(_componentBoundaryOffset, {
    componentType: "option"
});
const _componentButton = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "buttons",
            IsCollectionItem: true,
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const Button = Object.assign(_componentButton, {
    componentType: "option"
});
const _componentChange = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "changes",
            IsCollectionItem: true
        }
    });
};
const Change = Object.assign(_componentChange, {
    componentType: "option"
});
const _componentColCountByScreen = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "colCountByScreen"
        }
    });
};
const ColCountByScreen = Object.assign(_componentColCountByScreen, {
    componentType: "option"
});
const _componentCollision = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "collision"
        }
    });
};
const Collision = Object.assign(_componentCollision, {
    componentType: "option"
});
const _componentColumn = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "columns",
            IsCollectionItem: true,
            DefaultsProps: {
                defaultFilterValue: "filterValue",
                defaultFilterValues: "filterValues",
                defaultGroupIndex: "groupIndex",
                defaultSelectedFilterOperation: "selectedFilterOperation",
                defaultSortIndex: "sortIndex",
                defaultSortOrder: "sortOrder",
                defaultVisible: "visible",
                defaultVisibleIndex: "visibleIndex"
            },
            ExpectedChildren: {
                AsyncRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                button: {
                    optionName: "buttons",
                    isCollectionItem: true
                },
                columnHeaderFilter: {
                    optionName: "headerFilter",
                    isCollectionItem: false
                },
                columnLookup: {
                    optionName: "lookup",
                    isCollectionItem: false
                },
                CompareRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                CustomRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                EmailRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                },
                formItem: {
                    optionName: "formItem",
                    isCollectionItem: false
                },
                headerFilter: {
                    optionName: "headerFilter",
                    isCollectionItem: false
                },
                lookup: {
                    optionName: "lookup",
                    isCollectionItem: false
                },
                NumericRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                PatternRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                RangeRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                RequiredRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                StringLengthRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                validationRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                }
            },
            TemplateProps: [
                {
                    tmplOption: "cellTemplate",
                    render: "cellRender",
                    component: "cellComponent"
                },
                {
                    tmplOption: "editCellTemplate",
                    render: "editCellRender",
                    component: "editCellComponent"
                },
                {
                    tmplOption: "groupCellTemplate",
                    render: "groupCellRender",
                    component: "groupCellComponent"
                },
                {
                    tmplOption: "headerCellTemplate",
                    render: "headerCellRender",
                    component: "headerCellComponent"
                }
            ]
        }
    });
};
const Column = Object.assign(_componentColumn, {
    componentType: "option"
});
const _componentColumnChooser = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "columnChooser",
            ExpectedChildren: {
                columnChooserSearch: {
                    optionName: "search",
                    isCollectionItem: false
                },
                columnChooserSelection: {
                    optionName: "selection",
                    isCollectionItem: false
                },
                position: {
                    optionName: "position",
                    isCollectionItem: false
                },
                search: {
                    optionName: "search",
                    isCollectionItem: false
                },
                selection: {
                    optionName: "selection",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ColumnChooser = Object.assign(_componentColumnChooser, {
    componentType: "option"
});
const _componentColumnChooserSearch = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "search"
        }
    });
};
const ColumnChooserSearch = Object.assign(_componentColumnChooserSearch, {
    componentType: "option"
});
const _componentColumnChooserSelection = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "selection"
        }
    });
};
const ColumnChooserSelection = Object.assign(_componentColumnChooserSelection, {
    componentType: "option"
});
const _componentColumnFixing = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "columnFixing",
            ExpectedChildren: {
                columnFixingTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                icons: {
                    optionName: "icons",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ColumnFixing = Object.assign(_componentColumnFixing, {
    componentType: "option"
});
const _componentColumnFixingTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const ColumnFixingTexts = Object.assign(_componentColumnFixingTexts, {
    componentType: "option"
});
const _componentColumnHeaderFilter = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "headerFilter",
            ExpectedChildren: {
                columnHeaderFilterSearch: {
                    optionName: "search",
                    isCollectionItem: false
                },
                search: {
                    optionName: "search",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ColumnHeaderFilter = Object.assign(_componentColumnHeaderFilter, {
    componentType: "option"
});
const _componentColumnHeaderFilterSearch = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "search"
        }
    });
};
const ColumnHeaderFilterSearch = Object.assign(_componentColumnHeaderFilterSearch, {
    componentType: "option"
});
const _componentColumnLookup = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "lookup"
        }
    });
};
const ColumnLookup = Object.assign(_componentColumnLookup, {
    componentType: "option"
});
const _componentCompareRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "compare"
            }
        }
    });
};
const CompareRule = Object.assign(_componentCompareRule, {
    componentType: "option"
});
const _componentCursorOffset = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "cursorOffset"
        }
    });
};
const CursorOffset = Object.assign(_componentCursorOffset, {
    componentType: "option"
});
const _componentCustomOperation = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "customOperations",
            IsCollectionItem: true,
            TemplateProps: [
                {
                    tmplOption: "editorTemplate",
                    render: "editorRender",
                    component: "editorComponent"
                }
            ]
        }
    });
};
const CustomOperation = Object.assign(_componentCustomOperation, {
    componentType: "option"
});
const _componentCustomRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "custom"
            }
        }
    });
};
const CustomRule = Object.assign(_componentCustomRule, {
    componentType: "option"
});
const _componentDataGridHeaderFilter = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "headerFilter",
            ExpectedChildren: {
                dataGridHeaderFilterSearch: {
                    optionName: "search",
                    isCollectionItem: false
                },
                dataGridHeaderFilterTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                search: {
                    optionName: "search",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const DataGridHeaderFilter = Object.assign(_componentDataGridHeaderFilter, {
    componentType: "option"
});
const _componentDataGridHeaderFilterSearch = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "search"
        }
    });
};
const DataGridHeaderFilterSearch = Object.assign(_componentDataGridHeaderFilterSearch, {
    componentType: "option"
});
const _componentDataGridHeaderFilterTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const DataGridHeaderFilterTexts = Object.assign(_componentDataGridHeaderFilterTexts, {
    componentType: "option"
});
const _componentDataGridSelection = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "selection"
        }
    });
};
const DataGridSelection = Object.assign(_componentDataGridSelection, {
    componentType: "option"
});
const _componentEditing = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "editing",
            DefaultsProps: {
                defaultChanges: "changes",
                defaultEditColumnName: "editColumnName",
                defaultEditRowKey: "editRowKey"
            },
            ExpectedChildren: {
                change: {
                    optionName: "changes",
                    isCollectionItem: true
                },
                editingTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                form: {
                    optionName: "form",
                    isCollectionItem: false
                },
                popup: {
                    optionName: "popup",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Editing = Object.assign(_componentEditing, {
    componentType: "option"
});
const _componentEditingTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const EditingTexts = Object.assign(_componentEditingTexts, {
    componentType: "option"
});
const _componentEmailRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "email"
            }
        }
    });
};
const EmailRule = Object.assign(_componentEmailRule, {
    componentType: "option"
});
const _componentExport = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "export",
            ExpectedChildren: {
                exportTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Export = Object.assign(_componentExport, {
    componentType: "option"
});
const _componentExportTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const ExportTexts = Object.assign(_componentExportTexts, {
    componentType: "option"
});
const _componentField = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "fields",
            IsCollectionItem: true,
            ExpectedChildren: {
                fieldLookup: {
                    optionName: "lookup",
                    isCollectionItem: false
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                },
                lookup: {
                    optionName: "lookup",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "editorTemplate",
                    render: "editorRender",
                    component: "editorComponent"
                }
            ]
        }
    });
};
const Field = Object.assign(_componentField, {
    componentType: "option"
});
const _componentFieldLookup = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "lookup"
        }
    });
};
const FieldLookup = Object.assign(_componentFieldLookup, {
    componentType: "option"
});
const _componentFilterBuilder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "filterBuilder",
            DefaultsProps: {
                defaultValue: "value"
            },
            ExpectedChildren: {
                customOperation: {
                    optionName: "customOperations",
                    isCollectionItem: true
                },
                field: {
                    optionName: "fields",
                    isCollectionItem: true
                },
                filterOperationDescriptions: {
                    optionName: "filterOperationDescriptions",
                    isCollectionItem: false
                },
                groupOperationDescriptions: {
                    optionName: "groupOperationDescriptions",
                    isCollectionItem: false
                }
            }
        }
    });
};
const FilterBuilder = Object.assign(_componentFilterBuilder, {
    componentType: "option"
});
const _componentFilterBuilderPopup = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "filterBuilderPopup",
            DefaultsProps: {
                defaultHeight: "height",
                defaultPosition: "position",
                defaultVisible: "visible",
                defaultWidth: "width"
            },
            ExpectedChildren: {
                animation: {
                    optionName: "animation",
                    isCollectionItem: false
                },
                position: {
                    optionName: "position",
                    isCollectionItem: false
                },
                toolbarItem: {
                    optionName: "toolbarItems",
                    isCollectionItem: true
                }
            },
            TemplateProps: [
                {
                    tmplOption: "contentTemplate",
                    render: "contentRender",
                    component: "contentComponent"
                },
                {
                    tmplOption: "titleTemplate",
                    render: "titleRender",
                    component: "titleComponent"
                }
            ]
        }
    });
};
const FilterBuilderPopup = Object.assign(_componentFilterBuilderPopup, {
    componentType: "option"
});
const _componentFilterOperationDescriptions = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "filterOperationDescriptions"
        }
    });
};
const FilterOperationDescriptions = Object.assign(_componentFilterOperationDescriptions, {
    componentType: "option"
});
const _componentFilterPanel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "filterPanel",
            DefaultsProps: {
                defaultFilterEnabled: "filterEnabled"
            },
            ExpectedChildren: {
                filterPanelTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const FilterPanel = Object.assign(_componentFilterPanel, {
    componentType: "option"
});
const _componentFilterPanelTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const FilterPanelTexts = Object.assign(_componentFilterPanelTexts, {
    componentType: "option"
});
const _componentFilterRow = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "filterRow",
            ExpectedChildren: {
                operationDescriptions: {
                    optionName: "operationDescriptions",
                    isCollectionItem: false
                }
            }
        }
    });
};
const FilterRow = Object.assign(_componentFilterRow, {
    componentType: "option"
});
const _componentForm = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "form",
            DefaultsProps: {
                defaultFormData: "formData"
            },
            ExpectedChildren: {
                colCountByScreen: {
                    optionName: "colCountByScreen",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Form = Object.assign(_componentForm, {
    componentType: "option"
});
const _componentFormat = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "format"
        }
    });
};
const Format = Object.assign(_componentFormat, {
    componentType: "option"
});
const _componentFormItem = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "formItem",
            ExpectedChildren: {
                AsyncRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                CompareRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                CustomRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                EmailRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                NumericRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                PatternRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                RangeRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                RequiredRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                StringLengthRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                },
                validationRule: {
                    optionName: "validationRules",
                    isCollectionItem: true
                }
            },
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const FormItem = Object.assign(_componentFormItem, {
    componentType: "option"
});
const _componentFrom = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "from",
            ExpectedChildren: {
                position: {
                    optionName: "position",
                    isCollectionItem: false
                }
            }
        }
    });
};
const From = Object.assign(_componentFrom, {
    componentType: "option"
});
const _componentGrouping = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "grouping",
            ExpectedChildren: {
                groupingTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Grouping = Object.assign(_componentGrouping, {
    componentType: "option"
});
const _componentGroupingTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const GroupingTexts = Object.assign(_componentGroupingTexts, {
    componentType: "option"
});
const _componentGroupItem = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "groupItems",
            IsCollectionItem: true,
            ExpectedChildren: {
                valueFormat: {
                    optionName: "valueFormat",
                    isCollectionItem: false
                }
            }
        }
    });
};
const GroupItem = Object.assign(_componentGroupItem, {
    componentType: "option"
});
const _componentGroupOperationDescriptions = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "groupOperationDescriptions"
        }
    });
};
const GroupOperationDescriptions = Object.assign(_componentGroupOperationDescriptions, {
    componentType: "option"
});
const _componentGroupPanel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "groupPanel",
            DefaultsProps: {
                defaultVisible: "visible"
            }
        }
    });
};
const GroupPanel = Object.assign(_componentGroupPanel, {
    componentType: "option"
});
const _componentHeaderFilter = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "headerFilter",
            ExpectedChildren: {
                columnHeaderFilterSearch: {
                    optionName: "search",
                    isCollectionItem: false
                },
                dataGridHeaderFilterSearch: {
                    optionName: "search",
                    isCollectionItem: false
                },
                dataGridHeaderFilterTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                }
            }
        }
    });
};
const HeaderFilter = Object.assign(_componentHeaderFilter, {
    componentType: "option"
});
const _componentHide = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "hide",
            ExpectedChildren: {
                from: {
                    optionName: "from",
                    isCollectionItem: false
                },
                to: {
                    optionName: "to",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Hide = Object.assign(_componentHide, {
    componentType: "option"
});
const _componentIcons = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "icons"
        }
    });
};
const Icons = Object.assign(_componentIcons, {
    componentType: "option"
});
const _componentItem = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "items",
            IsCollectionItem: true,
            TemplateProps: [
                {
                    tmplOption: "menuItemTemplate",
                    render: "menuItemRender",
                    component: "menuItemComponent"
                },
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const Item = Object.assign(_componentItem, {
    componentType: "option"
});
const _componentKeyboardNavigation = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "keyboardNavigation"
        }
    });
};
const KeyboardNavigation = Object.assign(_componentKeyboardNavigation, {
    componentType: "option"
});
const _componentLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const Label = Object.assign(_componentLabel, {
    componentType: "option"
});
const _componentLoadPanel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "loadPanel"
        }
    });
};
const LoadPanel = Object.assign(_componentLoadPanel, {
    componentType: "option"
});
const _componentLookup = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "lookup"
        }
    });
};
const Lookup = Object.assign(_componentLookup, {
    componentType: "option"
});
const _componentMasterDetail = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "masterDetail",
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const MasterDetail = Object.assign(_componentMasterDetail, {
    componentType: "option"
});
const _componentMy = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "my"
        }
    });
};
const My = Object.assign(_componentMy, {
    componentType: "option"
});
const _componentNumericRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "numeric"
            }
        }
    });
};
const NumericRule = Object.assign(_componentNumericRule, {
    componentType: "option"
});
const _componentOffset = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "offset"
        }
    });
};
const Offset = Object.assign(_componentOffset, {
    componentType: "option"
});
const _componentOperationDescriptions = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "operationDescriptions"
        }
    });
};
const OperationDescriptions = Object.assign(_componentOperationDescriptions, {
    componentType: "option"
});
const _componentPager = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "pager"
        }
    });
};
const Pager = Object.assign(_componentPager, {
    componentType: "option"
});
const _componentPaging = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "paging",
            DefaultsProps: {
                defaultPageIndex: "pageIndex",
                defaultPageSize: "pageSize"
            }
        }
    });
};
const Paging = Object.assign(_componentPaging, {
    componentType: "option"
});
const _componentPatternRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "pattern"
            }
        }
    });
};
const PatternRule = Object.assign(_componentPatternRule, {
    componentType: "option"
});
const _componentPopup = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "popup",
            DefaultsProps: {
                defaultHeight: "height",
                defaultPosition: "position",
                defaultVisible: "visible",
                defaultWidth: "width"
            },
            ExpectedChildren: {
                animation: {
                    optionName: "animation",
                    isCollectionItem: false
                },
                position: {
                    optionName: "position",
                    isCollectionItem: false
                },
                toolbarItem: {
                    optionName: "toolbarItems",
                    isCollectionItem: true
                }
            },
            TemplateProps: [
                {
                    tmplOption: "contentTemplate",
                    render: "contentRender",
                    component: "contentComponent"
                },
                {
                    tmplOption: "titleTemplate",
                    render: "titleRender",
                    component: "titleComponent"
                }
            ]
        }
    });
};
const Popup = Object.assign(_componentPopup, {
    componentType: "option"
});
const _componentPosition = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "position",
            ExpectedChildren: {
                at: {
                    optionName: "at",
                    isCollectionItem: false
                },
                boundaryOffset: {
                    optionName: "boundaryOffset",
                    isCollectionItem: false
                },
                collision: {
                    optionName: "collision",
                    isCollectionItem: false
                },
                my: {
                    optionName: "my",
                    isCollectionItem: false
                },
                offset: {
                    optionName: "offset",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Position = Object.assign(_componentPosition, {
    componentType: "option"
});
const _componentRangeRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "range"
            }
        }
    });
};
const RangeRule = Object.assign(_componentRangeRule, {
    componentType: "option"
});
const _componentRemoteOperations = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "remoteOperations"
        }
    });
};
const RemoteOperations = Object.assign(_componentRemoteOperations, {
    componentType: "option"
});
const _componentRequiredRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "required"
            }
        }
    });
};
const RequiredRule = Object.assign(_componentRequiredRule, {
    componentType: "option"
});
const _componentRowDragging = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "rowDragging",
            ExpectedChildren: {
                cursorOffset: {
                    optionName: "cursorOffset",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "dragTemplate",
                    render: "dragRender",
                    component: "dragComponent"
                }
            ]
        }
    });
};
const RowDragging = Object.assign(_componentRowDragging, {
    componentType: "option"
});
const _componentScrolling = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "scrolling"
        }
    });
};
const Scrolling = Object.assign(_componentScrolling, {
    componentType: "option"
});
const _componentSearch = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "search"
        }
    });
};
const Search = Object.assign(_componentSearch, {
    componentType: "option"
});
const _componentSearchPanel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "searchPanel",
            DefaultsProps: {
                defaultText: "text"
            }
        }
    });
};
const SearchPanel = Object.assign(_componentSearchPanel, {
    componentType: "option"
});
const _componentSelection = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "selection"
        }
    });
};
const Selection = Object.assign(_componentSelection, {
    componentType: "option"
});
const _componentShow = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "show",
            ExpectedChildren: {
                from: {
                    optionName: "from",
                    isCollectionItem: false
                },
                to: {
                    optionName: "to",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Show = Object.assign(_componentShow, {
    componentType: "option"
});
const _componentSortByGroupSummaryInfo = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "sortByGroupSummaryInfo",
            IsCollectionItem: true
        }
    });
};
const SortByGroupSummaryInfo = Object.assign(_componentSortByGroupSummaryInfo, {
    componentType: "option"
});
const _componentSorting = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "sorting"
        }
    });
};
const Sorting = Object.assign(_componentSorting, {
    componentType: "option"
});
const _componentStateStoring = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "stateStoring"
        }
    });
};
const StateStoring = Object.assign(_componentStateStoring, {
    componentType: "option"
});
const _componentStringLengthRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "stringLength"
            }
        }
    });
};
const StringLengthRule = Object.assign(_componentStringLengthRule, {
    componentType: "option"
});
const _componentSummary = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "summary",
            ExpectedChildren: {
                groupItem: {
                    optionName: "groupItems",
                    isCollectionItem: true
                },
                summaryTexts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                texts: {
                    optionName: "texts",
                    isCollectionItem: false
                },
                totalItem: {
                    optionName: "totalItems",
                    isCollectionItem: true
                }
            }
        }
    });
};
const Summary = Object.assign(_componentSummary, {
    componentType: "option"
});
const _componentSummaryTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const SummaryTexts = Object.assign(_componentSummaryTexts, {
    componentType: "option"
});
const _componentTexts = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "texts"
        }
    });
};
const Texts = Object.assign(_componentTexts, {
    componentType: "option"
});
const _componentTo = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "to",
            ExpectedChildren: {
                position: {
                    optionName: "position",
                    isCollectionItem: false
                }
            }
        }
    });
};
const To = Object.assign(_componentTo, {
    componentType: "option"
});
const _componentToolbar = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "toolbar",
            ExpectedChildren: {
                item: {
                    optionName: "items",
                    isCollectionItem: true
                }
            }
        }
    });
};
const Toolbar = Object.assign(_componentToolbar, {
    componentType: "option"
});
const _componentToolbarItem = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "toolbarItems",
            IsCollectionItem: true,
            TemplateProps: [
                {
                    tmplOption: "menuItemTemplate",
                    render: "menuItemRender",
                    component: "menuItemComponent"
                },
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const ToolbarItem = Object.assign(_componentToolbarItem, {
    componentType: "option"
});
const _componentTotalItem = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "totalItems",
            IsCollectionItem: true,
            ExpectedChildren: {
                valueFormat: {
                    optionName: "valueFormat",
                    isCollectionItem: false
                }
            }
        }
    });
};
const TotalItem = Object.assign(_componentTotalItem, {
    componentType: "option"
});
const _componentValidationRule = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "validationRules",
            IsCollectionItem: true,
            PredefinedProps: {
                type: "required"
            }
        }
    });
};
const ValidationRule = Object.assign(_componentValidationRule, {
    componentType: "option"
});
const _componentValueFormat = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "valueFormat"
        }
    });
};
const ValueFormat = Object.assign(_componentValueFormat, {
    componentType: "option"
});
const __TURBOPACK__default__export__ = DataGrid;
;
}),
"[project]/node_modules/devextreme-react/esm/chart.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "AdaptiveLayout": ()=>AdaptiveLayout,
    "Aggregation": ()=>Aggregation,
    "AggregationInterval": ()=>AggregationInterval,
    "Animation": ()=>Animation,
    "Annotation": ()=>Annotation,
    "AnnotationBorder": ()=>AnnotationBorder,
    "AnnotationImage": ()=>AnnotationImage,
    "ArgumentAxis": ()=>ArgumentAxis,
    "ArgumentFormat": ()=>ArgumentFormat,
    "AxisConstantLineStyle": ()=>AxisConstantLineStyle,
    "AxisConstantLineStyleLabel": ()=>AxisConstantLineStyleLabel,
    "AxisLabel": ()=>AxisLabel,
    "AxisTitle": ()=>AxisTitle,
    "BackgroundColor": ()=>BackgroundColor,
    "Border": ()=>Border,
    "Break": ()=>Break,
    "BreakStyle": ()=>BreakStyle,
    "Chart": ()=>Chart,
    "ChartTitle": ()=>ChartTitle,
    "ChartTitleSubtitle": ()=>ChartTitleSubtitle,
    "Color": ()=>Color,
    "CommonAnnotationSettings": ()=>CommonAnnotationSettings,
    "CommonAxisSettings": ()=>CommonAxisSettings,
    "CommonAxisSettingsConstantLineStyle": ()=>CommonAxisSettingsConstantLineStyle,
    "CommonAxisSettingsConstantLineStyleLabel": ()=>CommonAxisSettingsConstantLineStyleLabel,
    "CommonAxisSettingsLabel": ()=>CommonAxisSettingsLabel,
    "CommonAxisSettingsTitle": ()=>CommonAxisSettingsTitle,
    "CommonPaneSettings": ()=>CommonPaneSettings,
    "CommonSeriesSettings": ()=>CommonSeriesSettings,
    "CommonSeriesSettingsHoverStyle": ()=>CommonSeriesSettingsHoverStyle,
    "CommonSeriesSettingsLabel": ()=>CommonSeriesSettingsLabel,
    "CommonSeriesSettingsSelectionStyle": ()=>CommonSeriesSettingsSelectionStyle,
    "Connector": ()=>Connector,
    "ConstantLine": ()=>ConstantLine,
    "ConstantLineLabel": ()=>ConstantLineLabel,
    "ConstantLineStyle": ()=>ConstantLineStyle,
    "Crosshair": ()=>Crosshair,
    "DataPrepareSettings": ()=>DataPrepareSettings,
    "DragBoxStyle": ()=>DragBoxStyle,
    "Export": ()=>Export,
    "Font": ()=>Font,
    "Format": ()=>Format,
    "Grid": ()=>Grid,
    "Hatching": ()=>Hatching,
    "Height": ()=>Height,
    "HorizontalLine": ()=>HorizontalLine,
    "HorizontalLineLabel": ()=>HorizontalLineLabel,
    "HoverStyle": ()=>HoverStyle,
    "Image": ()=>Image,
    "Label": ()=>Label,
    "Legend": ()=>Legend,
    "LegendTitle": ()=>LegendTitle,
    "LegendTitleSubtitle": ()=>LegendTitleSubtitle,
    "Length": ()=>Length,
    "LoadingIndicator": ()=>LoadingIndicator,
    "Margin": ()=>Margin,
    "MinVisualRangeLength": ()=>MinVisualRangeLength,
    "MinorGrid": ()=>MinorGrid,
    "MinorTick": ()=>MinorTick,
    "MinorTickInterval": ()=>MinorTickInterval,
    "Pane": ()=>Pane,
    "PaneBorder": ()=>PaneBorder,
    "Point": ()=>Point,
    "PointBorder": ()=>PointBorder,
    "PointHoverStyle": ()=>PointHoverStyle,
    "PointImage": ()=>PointImage,
    "PointSelectionStyle": ()=>PointSelectionStyle,
    "Reduction": ()=>Reduction,
    "ScrollBar": ()=>ScrollBar,
    "SelectionStyle": ()=>SelectionStyle,
    "Series": ()=>Series,
    "SeriesBorder": ()=>SeriesBorder,
    "SeriesTemplate": ()=>SeriesTemplate,
    "Shadow": ()=>Shadow,
    "Size": ()=>Size,
    "Strip": ()=>Strip,
    "StripLabel": ()=>StripLabel,
    "StripStyle": ()=>StripStyle,
    "StripStyleLabel": ()=>StripStyleLabel,
    "Subtitle": ()=>Subtitle,
    "Tick": ()=>Tick,
    "TickInterval": ()=>TickInterval,
    "Title": ()=>Title,
    "Tooltip": ()=>Tooltip,
    "TooltipBorder": ()=>TooltipBorder,
    "Url": ()=>Url,
    "ValueAxis": ()=>ValueAxis,
    "ValueErrorBar": ()=>ValueErrorBar,
    "VerticalLine": ()=>VerticalLine,
    "VisualRange": ()=>VisualRange,
    "WholeRange": ()=>WholeRange,
    "Width": ()=>Width,
    "ZoomAndPan": ()=>ZoomAndPan,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/viz/chart.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/nested-option.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const Chart = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const baseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Chart.useImperativeHandle": ()=>({
                instance () {
                    var _baseRef_current;
                    return (_baseRef_current = baseRef.current) === null || _baseRef_current === void 0 ? void 0 : _baseRef_current.getInstance();
                }
            })
    }["Chart.useImperativeHandle"], [
        baseRef.current
    ]);
    const subscribableOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Chart.useMemo[subscribableOptions]": ()=>[
                "argumentAxis",
                "argumentAxis.categories",
                "argumentAxis.visualRange",
                "loadingIndicator",
                "loadingIndicator.show",
                "valueAxis",
                "valueAxis.categories",
                "valueAxis.visualRange",
                "argumentAxis.visualRange.endValue",
                "valueAxis.visualRange.endValue",
                "argumentAxis.visualRange.startValue",
                "valueAxis.visualRange.startValue",
                "argumentAxis.wholeRange.endValue",
                "valueAxis.wholeRange.endValue",
                "argumentAxis.wholeRange.startValue",
                "valueAxis.wholeRange.startValue"
            ]
    }["Chart.useMemo[subscribableOptions]"], []);
    const independentEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Chart.useMemo[independentEvents]": ()=>[
                "onArgumentAxisClick",
                "onDisposing",
                "onDone",
                "onDrawn",
                "onExported",
                "onExporting",
                "onFileSaving",
                "onIncidentOccurred",
                "onInitialized",
                "onLegendClick",
                "onPointClick",
                "onSeriesClick",
                "onTooltipHidden",
                "onTooltipShown",
                "onZoomEnd",
                "onZoomStart"
            ]
    }["Chart.useMemo[independentEvents]"], []);
    const defaults = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Chart.useMemo[defaults]": ()=>({
                defaultArgumentAxis: "argumentAxis",
                defaultLoadingIndicator: "loadingIndicator",
                defaultValueAxis: "valueAxis"
            })
    }["Chart.useMemo[defaults]"], []);
    const expectedChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Chart.useMemo[expectedChildren]": ()=>({
                adaptiveLayout: {
                    optionName: "adaptiveLayout",
                    isCollectionItem: false
                },
                animation: {
                    optionName: "animation",
                    isCollectionItem: false
                },
                annotation: {
                    optionName: "annotations",
                    isCollectionItem: true
                },
                argumentAxis: {
                    optionName: "argumentAxis",
                    isCollectionItem: false
                },
                chartTitle: {
                    optionName: "title",
                    isCollectionItem: false
                },
                commonAnnotationSettings: {
                    optionName: "commonAnnotationSettings",
                    isCollectionItem: false
                },
                commonAxisSettings: {
                    optionName: "commonAxisSettings",
                    isCollectionItem: false
                },
                commonPaneSettings: {
                    optionName: "commonPaneSettings",
                    isCollectionItem: false
                },
                commonSeriesSettings: {
                    optionName: "commonSeriesSettings",
                    isCollectionItem: false
                },
                crosshair: {
                    optionName: "crosshair",
                    isCollectionItem: false
                },
                dataPrepareSettings: {
                    optionName: "dataPrepareSettings",
                    isCollectionItem: false
                },
                export: {
                    optionName: "export",
                    isCollectionItem: false
                },
                legend: {
                    optionName: "legend",
                    isCollectionItem: false
                },
                loadingIndicator: {
                    optionName: "loadingIndicator",
                    isCollectionItem: false
                },
                margin: {
                    optionName: "margin",
                    isCollectionItem: false
                },
                pane: {
                    optionName: "panes",
                    isCollectionItem: true
                },
                scrollBar: {
                    optionName: "scrollBar",
                    isCollectionItem: false
                },
                series: {
                    optionName: "series",
                    isCollectionItem: true
                },
                seriesTemplate: {
                    optionName: "seriesTemplate",
                    isCollectionItem: false
                },
                size: {
                    optionName: "size",
                    isCollectionItem: false
                },
                title: {
                    optionName: "title",
                    isCollectionItem: false
                },
                tooltip: {
                    optionName: "tooltip",
                    isCollectionItem: false
                },
                valueAxis: {
                    optionName: "valueAxis",
                    isCollectionItem: true
                },
                zoomAndPan: {
                    optionName: "zoomAndPan",
                    isCollectionItem: false
                }
            })
    }["Chart.useMemo[expectedChildren]"], []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"], {
        WidgetClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$viz$2f$chart$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        ref: baseRef,
        useRequestAnimationFrameFlag: true,
        subscribableOptions,
        independentEvents,
        defaults,
        expectedChildren,
        ...props
    });
}));
const _componentAdaptiveLayout = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "adaptiveLayout"
        }
    });
};
const AdaptiveLayout = Object.assign(_componentAdaptiveLayout, {
    componentType: "option"
});
const _componentAggregation = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "aggregation"
        }
    });
};
const Aggregation = Object.assign(_componentAggregation, {
    componentType: "option"
});
const _componentAggregationInterval = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "aggregationInterval"
        }
    });
};
const AggregationInterval = Object.assign(_componentAggregationInterval, {
    componentType: "option"
});
const _componentAnimation = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "animation"
        }
    });
};
const Animation = Object.assign(_componentAnimation, {
    componentType: "option"
});
const _componentAnnotation = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "annotations",
            IsCollectionItem: true,
            ExpectedChildren: {
                annotationBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                annotationImage: {
                    optionName: "image",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                image: {
                    optionName: "image",
                    isCollectionItem: false
                },
                shadow: {
                    optionName: "shadow",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                },
                {
                    tmplOption: "tooltipTemplate",
                    render: "tooltipRender",
                    component: "tooltipComponent"
                }
            ]
        }
    });
};
const Annotation = Object.assign(_componentAnnotation, {
    componentType: "option"
});
const _componentAnnotationBorder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "border"
        }
    });
};
const AnnotationBorder = Object.assign(_componentAnnotationBorder, {
    componentType: "option"
});
const _componentAnnotationImage = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "image"
        }
    });
};
const AnnotationImage = Object.assign(_componentAnnotationImage, {
    componentType: "option"
});
const _componentArgumentAxis = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "argumentAxis",
            DefaultsProps: {
                defaultCategories: "categories",
                defaultVisualRange: "visualRange"
            },
            ExpectedChildren: {
                aggregationInterval: {
                    optionName: "aggregationInterval",
                    isCollectionItem: false
                },
                axisConstantLineStyle: {
                    optionName: "constantLineStyle",
                    isCollectionItem: false
                },
                axisLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                axisTitle: {
                    optionName: "title",
                    isCollectionItem: false
                },
                break: {
                    optionName: "breaks",
                    isCollectionItem: true
                },
                breakStyle: {
                    optionName: "breakStyle",
                    isCollectionItem: false
                },
                constantLine: {
                    optionName: "constantLines",
                    isCollectionItem: true
                },
                constantLineStyle: {
                    optionName: "constantLineStyle",
                    isCollectionItem: false
                },
                grid: {
                    optionName: "grid",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                minorGrid: {
                    optionName: "minorGrid",
                    isCollectionItem: false
                },
                minorTick: {
                    optionName: "minorTick",
                    isCollectionItem: false
                },
                minorTickInterval: {
                    optionName: "minorTickInterval",
                    isCollectionItem: false
                },
                minVisualRangeLength: {
                    optionName: "minVisualRangeLength",
                    isCollectionItem: false
                },
                strip: {
                    optionName: "strips",
                    isCollectionItem: true
                },
                stripStyle: {
                    optionName: "stripStyle",
                    isCollectionItem: false
                },
                tick: {
                    optionName: "tick",
                    isCollectionItem: false
                },
                tickInterval: {
                    optionName: "tickInterval",
                    isCollectionItem: false
                },
                title: {
                    optionName: "title",
                    isCollectionItem: false
                },
                visualRange: {
                    optionName: "visualRange",
                    isCollectionItem: false
                },
                wholeRange: {
                    optionName: "wholeRange",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ArgumentAxis = Object.assign(_componentArgumentAxis, {
    componentType: "option"
});
const _componentArgumentFormat = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "argumentFormat"
        }
    });
};
const ArgumentFormat = Object.assign(_componentArgumentFormat, {
    componentType: "option"
});
const _componentAxisConstantLineStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "constantLineStyle",
            ExpectedChildren: {
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const AxisConstantLineStyle = Object.assign(_componentAxisConstantLineStyle, {
    componentType: "option"
});
const _componentAxisConstantLineStyleLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const AxisConstantLineStyleLabel = Object.assign(_componentAxisConstantLineStyleLabel, {
    componentType: "option"
});
const _componentAxisLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const AxisLabel = Object.assign(_componentAxisLabel, {
    componentType: "option"
});
const _componentAxisTitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "title",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const AxisTitle = Object.assign(_componentAxisTitle, {
    componentType: "option"
});
const _componentBackgroundColor = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "backgroundColor"
        }
    });
};
const BackgroundColor = Object.assign(_componentBackgroundColor, {
    componentType: "option"
});
const _componentBorder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "border"
        }
    });
};
const Border = Object.assign(_componentBorder, {
    componentType: "option"
});
const _componentBreak = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "breaks",
            IsCollectionItem: true
        }
    });
};
const Break = Object.assign(_componentBreak, {
    componentType: "option"
});
const _componentBreakStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "breakStyle"
        }
    });
};
const BreakStyle = Object.assign(_componentBreakStyle, {
    componentType: "option"
});
const _componentChartTitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "title",
            ExpectedChildren: {
                chartTitleSubtitle: {
                    optionName: "subtitle",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                margin: {
                    optionName: "margin",
                    isCollectionItem: false
                },
                subtitle: {
                    optionName: "subtitle",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ChartTitle = Object.assign(_componentChartTitle, {
    componentType: "option"
});
const _componentChartTitleSubtitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "subtitle",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ChartTitleSubtitle = Object.assign(_componentChartTitleSubtitle, {
    componentType: "option"
});
const _componentColor = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "color"
        }
    });
};
const Color = Object.assign(_componentColor, {
    componentType: "option"
});
const _componentCommonAnnotationSettings = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "commonAnnotationSettings",
            ExpectedChildren: {
                annotationBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                annotationImage: {
                    optionName: "image",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                image: {
                    optionName: "image",
                    isCollectionItem: false
                },
                shadow: {
                    optionName: "shadow",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                },
                {
                    tmplOption: "tooltipTemplate",
                    render: "tooltipRender",
                    component: "tooltipComponent"
                }
            ]
        }
    });
};
const CommonAnnotationSettings = Object.assign(_componentCommonAnnotationSettings, {
    componentType: "option"
});
const _componentCommonAxisSettings = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "commonAxisSettings",
            ExpectedChildren: {
                breakStyle: {
                    optionName: "breakStyle",
                    isCollectionItem: false
                },
                commonAxisSettingsConstantLineStyle: {
                    optionName: "constantLineStyle",
                    isCollectionItem: false
                },
                commonAxisSettingsLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                commonAxisSettingsTitle: {
                    optionName: "title",
                    isCollectionItem: false
                },
                constantLineStyle: {
                    optionName: "constantLineStyle",
                    isCollectionItem: false
                },
                grid: {
                    optionName: "grid",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                minorGrid: {
                    optionName: "minorGrid",
                    isCollectionItem: false
                },
                minorTick: {
                    optionName: "minorTick",
                    isCollectionItem: false
                },
                stripStyle: {
                    optionName: "stripStyle",
                    isCollectionItem: false
                },
                tick: {
                    optionName: "tick",
                    isCollectionItem: false
                },
                title: {
                    optionName: "title",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonAxisSettings = Object.assign(_componentCommonAxisSettings, {
    componentType: "option"
});
const _componentCommonAxisSettingsConstantLineStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "constantLineStyle",
            ExpectedChildren: {
                commonAxisSettingsConstantLineStyleLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonAxisSettingsConstantLineStyle = Object.assign(_componentCommonAxisSettingsConstantLineStyle, {
    componentType: "option"
});
const _componentCommonAxisSettingsConstantLineStyleLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonAxisSettingsConstantLineStyleLabel = Object.assign(_componentCommonAxisSettingsConstantLineStyleLabel, {
    componentType: "option"
});
const _componentCommonAxisSettingsLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const CommonAxisSettingsLabel = Object.assign(_componentCommonAxisSettingsLabel, {
    componentType: "option"
});
const _componentCommonAxisSettingsTitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "title",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonAxisSettingsTitle = Object.assign(_componentCommonAxisSettingsTitle, {
    componentType: "option"
});
const _componentCommonPaneSettings = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "commonPaneSettings",
            ExpectedChildren: {
                backgroundColor: {
                    optionName: "backgroundColor",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                paneBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonPaneSettings = Object.assign(_componentCommonPaneSettings, {
    componentType: "option"
});
const _componentCommonSeriesSettings = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "commonSeriesSettings",
            ExpectedChildren: {
                aggregation: {
                    optionName: "aggregation",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                commonSeriesSettingsHoverStyle: {
                    optionName: "hoverStyle",
                    isCollectionItem: false
                },
                commonSeriesSettingsLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                commonSeriesSettingsSelectionStyle: {
                    optionName: "selectionStyle",
                    isCollectionItem: false
                },
                hoverStyle: {
                    optionName: "hoverStyle",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                point: {
                    optionName: "point",
                    isCollectionItem: false
                },
                reduction: {
                    optionName: "reduction",
                    isCollectionItem: false
                },
                selectionStyle: {
                    optionName: "selectionStyle",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                valueErrorBar: {
                    optionName: "valueErrorBar",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonSeriesSettings = Object.assign(_componentCommonSeriesSettings, {
    componentType: "option"
});
const _componentCommonSeriesSettingsHoverStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "hoverStyle",
            ExpectedChildren: {
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                hatching: {
                    optionName: "hatching",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonSeriesSettingsHoverStyle = Object.assign(_componentCommonSeriesSettingsHoverStyle, {
    componentType: "option"
});
const _componentCommonSeriesSettingsLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                argumentFormat: {
                    optionName: "argumentFormat",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                connector: {
                    optionName: "connector",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonSeriesSettingsLabel = Object.assign(_componentCommonSeriesSettingsLabel, {
    componentType: "option"
});
const _componentCommonSeriesSettingsSelectionStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "selectionStyle",
            ExpectedChildren: {
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                hatching: {
                    optionName: "hatching",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const CommonSeriesSettingsSelectionStyle = Object.assign(_componentCommonSeriesSettingsSelectionStyle, {
    componentType: "option"
});
const _componentConnector = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "connector"
        }
    });
};
const Connector = Object.assign(_componentConnector, {
    componentType: "option"
});
const _componentConstantLine = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "constantLines",
            IsCollectionItem: true,
            ExpectedChildren: {
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ConstantLine = Object.assign(_componentConstantLine, {
    componentType: "option"
});
const _componentConstantLineLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ConstantLineLabel = Object.assign(_componentConstantLineLabel, {
    componentType: "option"
});
const _componentConstantLineStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "constantLineStyle",
            ExpectedChildren: {
                commonAxisSettingsConstantLineStyleLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ConstantLineStyle = Object.assign(_componentConstantLineStyle, {
    componentType: "option"
});
const _componentCrosshair = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "crosshair",
            ExpectedChildren: {
                horizontalLine: {
                    optionName: "horizontalLine",
                    isCollectionItem: false
                },
                horizontalLineLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                verticalLine: {
                    optionName: "verticalLine",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Crosshair = Object.assign(_componentCrosshair, {
    componentType: "option"
});
const _componentDataPrepareSettings = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "dataPrepareSettings"
        }
    });
};
const DataPrepareSettings = Object.assign(_componentDataPrepareSettings, {
    componentType: "option"
});
const _componentDragBoxStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "dragBoxStyle"
        }
    });
};
const DragBoxStyle = Object.assign(_componentDragBoxStyle, {
    componentType: "option"
});
const _componentExport = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "export"
        }
    });
};
const Export = Object.assign(_componentExport, {
    componentType: "option"
});
const _componentFont = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "font"
        }
    });
};
const Font = Object.assign(_componentFont, {
    componentType: "option"
});
const _componentFormat = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "format"
        }
    });
};
const Format = Object.assign(_componentFormat, {
    componentType: "option"
});
const _componentGrid = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "grid"
        }
    });
};
const Grid = Object.assign(_componentGrid, {
    componentType: "option"
});
const _componentHatching = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "hatching"
        }
    });
};
const Hatching = Object.assign(_componentHatching, {
    componentType: "option"
});
const _componentHeight = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "height"
        }
    });
};
const Height = Object.assign(_componentHeight, {
    componentType: "option"
});
const _componentHorizontalLine = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "horizontalLine",
            ExpectedChildren: {
                horizontalLineLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const HorizontalLine = Object.assign(_componentHorizontalLine, {
    componentType: "option"
});
const _componentHorizontalLineLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                }
            }
        }
    });
};
const HorizontalLineLabel = Object.assign(_componentHorizontalLineLabel, {
    componentType: "option"
});
const _componentHoverStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "hoverStyle",
            ExpectedChildren: {
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                hatching: {
                    optionName: "hatching",
                    isCollectionItem: false
                },
                pointBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const HoverStyle = Object.assign(_componentHoverStyle, {
    componentType: "option"
});
const _componentImage = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "image",
            ExpectedChildren: {
                height: {
                    optionName: "height",
                    isCollectionItem: false
                },
                url: {
                    optionName: "url",
                    isCollectionItem: false
                },
                width: {
                    optionName: "width",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Image = Object.assign(_componentImage, {
    componentType: "option"
});
const _componentLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                argumentFormat: {
                    optionName: "argumentFormat",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                connector: {
                    optionName: "connector",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const Label = Object.assign(_componentLabel, {
    componentType: "option"
});
const _componentLegend = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "legend",
            ExpectedChildren: {
                annotationBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                legendTitle: {
                    optionName: "title",
                    isCollectionItem: false
                },
                margin: {
                    optionName: "margin",
                    isCollectionItem: false
                },
                title: {
                    optionName: "title",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "markerTemplate",
                    render: "markerRender",
                    component: "markerComponent"
                }
            ]
        }
    });
};
const Legend = Object.assign(_componentLegend, {
    componentType: "option"
});
const _componentLegendTitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "title",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                legendTitleSubtitle: {
                    optionName: "subtitle",
                    isCollectionItem: false
                },
                margin: {
                    optionName: "margin",
                    isCollectionItem: false
                },
                subtitle: {
                    optionName: "subtitle",
                    isCollectionItem: false
                }
            }
        }
    });
};
const LegendTitle = Object.assign(_componentLegendTitle, {
    componentType: "option"
});
const _componentLegendTitleSubtitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "subtitle",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const LegendTitleSubtitle = Object.assign(_componentLegendTitleSubtitle, {
    componentType: "option"
});
const _componentLength = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "length"
        }
    });
};
const Length = Object.assign(_componentLength, {
    componentType: "option"
});
const _componentLoadingIndicator = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "loadingIndicator",
            DefaultsProps: {
                defaultShow: "show"
            },
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const LoadingIndicator = Object.assign(_componentLoadingIndicator, {
    componentType: "option"
});
const _componentMargin = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "margin"
        }
    });
};
const Margin = Object.assign(_componentMargin, {
    componentType: "option"
});
const _componentMinorGrid = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "minorGrid"
        }
    });
};
const MinorGrid = Object.assign(_componentMinorGrid, {
    componentType: "option"
});
const _componentMinorTick = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "minorTick"
        }
    });
};
const MinorTick = Object.assign(_componentMinorTick, {
    componentType: "option"
});
const _componentMinorTickInterval = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "minorTickInterval"
        }
    });
};
const MinorTickInterval = Object.assign(_componentMinorTickInterval, {
    componentType: "option"
});
const _componentMinVisualRangeLength = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "minVisualRangeLength"
        }
    });
};
const MinVisualRangeLength = Object.assign(_componentMinVisualRangeLength, {
    componentType: "option"
});
const _componentPane = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "panes",
            IsCollectionItem: true,
            ExpectedChildren: {
                backgroundColor: {
                    optionName: "backgroundColor",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                paneBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Pane = Object.assign(_componentPane, {
    componentType: "option"
});
const _componentPaneBorder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "border"
        }
    });
};
const PaneBorder = Object.assign(_componentPaneBorder, {
    componentType: "option"
});
const _componentPoint = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "point",
            ExpectedChildren: {
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                hoverStyle: {
                    optionName: "hoverStyle",
                    isCollectionItem: false
                },
                image: {
                    optionName: "image",
                    isCollectionItem: false
                },
                pointBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                selectionStyle: {
                    optionName: "selectionStyle",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Point = Object.assign(_componentPoint, {
    componentType: "option"
});
const _componentPointBorder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "border"
        }
    });
};
const PointBorder = Object.assign(_componentPointBorder, {
    componentType: "option"
});
const _componentPointHoverStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "hoverStyle",
            ExpectedChildren: {
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                pointBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const PointHoverStyle = Object.assign(_componentPointHoverStyle, {
    componentType: "option"
});
const _componentPointImage = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "image",
            ExpectedChildren: {
                height: {
                    optionName: "height",
                    isCollectionItem: false
                },
                url: {
                    optionName: "url",
                    isCollectionItem: false
                },
                width: {
                    optionName: "width",
                    isCollectionItem: false
                }
            }
        }
    });
};
const PointImage = Object.assign(_componentPointImage, {
    componentType: "option"
});
const _componentPointSelectionStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "selectionStyle",
            ExpectedChildren: {
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                pointBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const PointSelectionStyle = Object.assign(_componentPointSelectionStyle, {
    componentType: "option"
});
const _componentReduction = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "reduction"
        }
    });
};
const Reduction = Object.assign(_componentReduction, {
    componentType: "option"
});
const _componentScrollBar = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "scrollBar"
        }
    });
};
const ScrollBar = Object.assign(_componentScrollBar, {
    componentType: "option"
});
const _componentSelectionStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "selectionStyle",
            ExpectedChildren: {
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                hatching: {
                    optionName: "hatching",
                    isCollectionItem: false
                },
                pointBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            }
        }
    });
};
const SelectionStyle = Object.assign(_componentSelectionStyle, {
    componentType: "option"
});
const _componentSeries = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "series",
            IsCollectionItem: true,
            ExpectedChildren: {
                aggregation: {
                    optionName: "aggregation",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                color: {
                    optionName: "color",
                    isCollectionItem: false
                },
                commonSeriesSettingsHoverStyle: {
                    optionName: "hoverStyle",
                    isCollectionItem: false
                },
                commonSeriesSettingsLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                commonSeriesSettingsSelectionStyle: {
                    optionName: "selectionStyle",
                    isCollectionItem: false
                },
                hoverStyle: {
                    optionName: "hoverStyle",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                point: {
                    optionName: "point",
                    isCollectionItem: false
                },
                reduction: {
                    optionName: "reduction",
                    isCollectionItem: false
                },
                selectionStyle: {
                    optionName: "selectionStyle",
                    isCollectionItem: false
                },
                seriesBorder: {
                    optionName: "border",
                    isCollectionItem: false
                },
                valueErrorBar: {
                    optionName: "valueErrorBar",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Series = Object.assign(_componentSeries, {
    componentType: "option"
});
const _componentSeriesBorder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "border"
        }
    });
};
const SeriesBorder = Object.assign(_componentSeriesBorder, {
    componentType: "option"
});
const _componentSeriesTemplate = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "seriesTemplate"
        }
    });
};
const SeriesTemplate = Object.assign(_componentSeriesTemplate, {
    componentType: "option"
});
const _componentShadow = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "shadow"
        }
    });
};
const Shadow = Object.assign(_componentShadow, {
    componentType: "option"
});
const _componentSize = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "size"
        }
    });
};
const Size = Object.assign(_componentSize, {
    componentType: "option"
});
const _componentStrip = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "strips",
            IsCollectionItem: true,
            ExpectedChildren: {
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Strip = Object.assign(_componentStrip, {
    componentType: "option"
});
const _componentStripLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const StripLabel = Object.assign(_componentStripLabel, {
    componentType: "option"
});
const _componentStripStyle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "stripStyle",
            ExpectedChildren: {
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const StripStyle = Object.assign(_componentStripStyle, {
    componentType: "option"
});
const _componentStripStyleLabel = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "label",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const StripStyleLabel = Object.assign(_componentStripStyleLabel, {
    componentType: "option"
});
const _componentSubtitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "subtitle",
            ExpectedChildren: {
                font: {
                    optionName: "font",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Subtitle = Object.assign(_componentSubtitle, {
    componentType: "option"
});
const _componentTick = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "tick"
        }
    });
};
const Tick = Object.assign(_componentTick, {
    componentType: "option"
});
const _componentTickInterval = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "tickInterval"
        }
    });
};
const TickInterval = Object.assign(_componentTickInterval, {
    componentType: "option"
});
const _componentTitle = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "title",
            ExpectedChildren: {
                chartTitleSubtitle: {
                    optionName: "subtitle",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                legendTitleSubtitle: {
                    optionName: "subtitle",
                    isCollectionItem: false
                },
                margin: {
                    optionName: "margin",
                    isCollectionItem: false
                }
            }
        }
    });
};
const Title = Object.assign(_componentTitle, {
    componentType: "option"
});
const _componentTooltip = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "tooltip",
            ExpectedChildren: {
                argumentFormat: {
                    optionName: "argumentFormat",
                    isCollectionItem: false
                },
                border: {
                    optionName: "border",
                    isCollectionItem: false
                },
                font: {
                    optionName: "font",
                    isCollectionItem: false
                },
                format: {
                    optionName: "format",
                    isCollectionItem: false
                },
                shadow: {
                    optionName: "shadow",
                    isCollectionItem: false
                },
                tooltipBorder: {
                    optionName: "border",
                    isCollectionItem: false
                }
            },
            TemplateProps: [
                {
                    tmplOption: "contentTemplate",
                    render: "contentRender",
                    component: "contentComponent"
                }
            ]
        }
    });
};
const Tooltip = Object.assign(_componentTooltip, {
    componentType: "option"
});
const _componentTooltipBorder = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "border"
        }
    });
};
const TooltipBorder = Object.assign(_componentTooltipBorder, {
    componentType: "option"
});
const _componentUrl = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "url"
        }
    });
};
const Url = Object.assign(_componentUrl, {
    componentType: "option"
});
const _componentValueAxis = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "valueAxis",
            IsCollectionItem: true,
            DefaultsProps: {
                defaultCategories: "categories",
                defaultVisualRange: "visualRange"
            },
            ExpectedChildren: {
                axisConstantLineStyle: {
                    optionName: "constantLineStyle",
                    isCollectionItem: false
                },
                axisLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                axisTitle: {
                    optionName: "title",
                    isCollectionItem: false
                },
                break: {
                    optionName: "breaks",
                    isCollectionItem: true
                },
                breakStyle: {
                    optionName: "breakStyle",
                    isCollectionItem: false
                },
                constantLine: {
                    optionName: "constantLines",
                    isCollectionItem: true
                },
                constantLineStyle: {
                    optionName: "constantLineStyle",
                    isCollectionItem: false
                },
                grid: {
                    optionName: "grid",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                },
                minorGrid: {
                    optionName: "minorGrid",
                    isCollectionItem: false
                },
                minorTick: {
                    optionName: "minorTick",
                    isCollectionItem: false
                },
                minorTickInterval: {
                    optionName: "minorTickInterval",
                    isCollectionItem: false
                },
                minVisualRangeLength: {
                    optionName: "minVisualRangeLength",
                    isCollectionItem: false
                },
                strip: {
                    optionName: "strips",
                    isCollectionItem: true
                },
                stripStyle: {
                    optionName: "stripStyle",
                    isCollectionItem: false
                },
                tick: {
                    optionName: "tick",
                    isCollectionItem: false
                },
                tickInterval: {
                    optionName: "tickInterval",
                    isCollectionItem: false
                },
                title: {
                    optionName: "title",
                    isCollectionItem: false
                },
                visualRange: {
                    optionName: "visualRange",
                    isCollectionItem: false
                },
                wholeRange: {
                    optionName: "wholeRange",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ValueAxis = Object.assign(_componentValueAxis, {
    componentType: "option"
});
const _componentValueErrorBar = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "valueErrorBar"
        }
    });
};
const ValueErrorBar = Object.assign(_componentValueErrorBar, {
    componentType: "option"
});
const _componentVerticalLine = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "verticalLine",
            ExpectedChildren: {
                horizontalLineLabel: {
                    optionName: "label",
                    isCollectionItem: false
                },
                label: {
                    optionName: "label",
                    isCollectionItem: false
                }
            }
        }
    });
};
const VerticalLine = Object.assign(_componentVerticalLine, {
    componentType: "option"
});
const _componentVisualRange = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "visualRange",
            DefaultsProps: {
                defaultEndValue: "endValue",
                defaultStartValue: "startValue"
            },
            ExpectedChildren: {
                length: {
                    optionName: "length",
                    isCollectionItem: false
                }
            }
        }
    });
};
const VisualRange = Object.assign(_componentVisualRange, {
    componentType: "option"
});
const _componentWholeRange = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "wholeRange",
            DefaultsProps: {
                defaultEndValue: "endValue",
                defaultStartValue: "startValue"
            },
            ExpectedChildren: {
                length: {
                    optionName: "length",
                    isCollectionItem: false
                }
            }
        }
    });
};
const WholeRange = Object.assign(_componentWholeRange, {
    componentType: "option"
});
const _componentWidth = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "width"
        }
    });
};
const Width = Object.assign(_componentWidth, {
    componentType: "option"
});
const _componentZoomAndPan = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "zoomAndPan",
            ExpectedChildren: {
                dragBoxStyle: {
                    optionName: "dragBoxStyle",
                    isCollectionItem: false
                }
            }
        }
    });
};
const ZoomAndPan = Object.assign(_componentZoomAndPan, {
    componentType: "option"
});
const __TURBOPACK__default__export__ = Chart;
;
}),
"[project]/node_modules/devextreme-react/esm/drawer.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "Drawer": ()=>Drawer,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$drawer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/drawer.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component.js [app-client] (ecmascript)");
"use client";
;
;
;
;
const Drawer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const baseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Drawer.useImperativeHandle": ()=>({
                instance () {
                    var _baseRef_current;
                    return (_baseRef_current = baseRef.current) === null || _baseRef_current === void 0 ? void 0 : _baseRef_current.getInstance();
                }
            })
    }["Drawer.useImperativeHandle"], [
        baseRef.current
    ]);
    const subscribableOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Drawer.useMemo[subscribableOptions]": ()=>[
                "opened"
            ]
    }["Drawer.useMemo[subscribableOptions]"], []);
    const independentEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Drawer.useMemo[independentEvents]": ()=>[
                "onDisposing",
                "onInitialized"
            ]
    }["Drawer.useMemo[independentEvents]"], []);
    const defaults = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Drawer.useMemo[defaults]": ()=>({
                defaultOpened: "opened"
            })
    }["Drawer.useMemo[defaults]"], []);
    const templateProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Drawer.useMemo[templateProps]": ()=>[
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
    }["Drawer.useMemo[templateProps]"], []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"], {
        WidgetClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$drawer$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        ref: baseRef,
        subscribableOptions,
        independentEvents,
        defaults,
        templateProps,
        ...props
    });
}));
const __TURBOPACK__default__export__ = Drawer;
;
}),
"[project]/node_modules/devextreme-react/esm/toolbar.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "Item": ()=>Item,
    "Toolbar": ()=>Toolbar,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$toolbar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/toolbar.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/nested-option.js [app-client] (ecmascript)");
"use client";
;
;
;
;
;
const Toolbar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const baseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "Toolbar.useImperativeHandle": ()=>({
                instance () {
                    var _baseRef_current;
                    return (_baseRef_current = baseRef.current) === null || _baseRef_current === void 0 ? void 0 : _baseRef_current.getInstance();
                }
            })
    }["Toolbar.useImperativeHandle"], [
        baseRef.current
    ]);
    const subscribableOptions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Toolbar.useMemo[subscribableOptions]": ()=>[
                "items"
            ]
    }["Toolbar.useMemo[subscribableOptions]"], []);
    const independentEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Toolbar.useMemo[independentEvents]": ()=>[
                "onContentReady",
                "onDisposing",
                "onInitialized",
                "onItemClick",
                "onItemContextMenu",
                "onItemHold",
                "onItemRendered"
            ]
    }["Toolbar.useMemo[independentEvents]"], []);
    const defaults = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Toolbar.useMemo[defaults]": ()=>({
                defaultItems: "items"
            })
    }["Toolbar.useMemo[defaults]"], []);
    const expectedChildren = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Toolbar.useMemo[expectedChildren]": ()=>({
                item: {
                    optionName: "items",
                    isCollectionItem: true
                }
            })
    }["Toolbar.useMemo[expectedChildren]"], []);
    const templateProps = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "Toolbar.useMemo[templateProps]": ()=>[
                {
                    tmplOption: "itemTemplate",
                    render: "itemRender",
                    component: "itemComponent"
                },
                {
                    tmplOption: "menuItemTemplate",
                    render: "menuItemRender",
                    component: "menuItemComponent"
                }
            ]
    }["Toolbar.useMemo[templateProps]"], []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"], {
        WidgetClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$toolbar$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        ref: baseRef,
        subscribableOptions,
        independentEvents,
        defaults,
        expectedChildren,
        templateProps,
        ...props
    });
}));
const _componentItem = (props)=>{
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$nested$2d$option$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        ...props,
        elementDescriptor: {
            OptionName: "items",
            IsCollectionItem: true,
            TemplateProps: [
                {
                    tmplOption: "menuItemTemplate",
                    render: "menuItemRender",
                    component: "menuItemComponent"
                },
                {
                    tmplOption: "template",
                    render: "render",
                    component: "component"
                }
            ]
        }
    });
};
const Item = Object.assign(_componentItem, {
    componentType: "option"
});
const __TURBOPACK__default__export__ = Toolbar;
;
}),
"[project]/node_modules/devextreme-react/esm/load-indicator.js [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

/*!
 * devextreme-react
 * Version: 25.1.3
 * Build date: Wed Jun 25 2025
 *
 * Copyright (c) 2012 - 2025 Developer Express Inc. ALL RIGHTS RESERVED
 *
 * This software may be modified and distributed under the terms
 * of the MIT license. See the LICENSE file in the root of the project for details.
 *
 * https://github.com/DevExpress/devextreme-react
 */ __turbopack_context__.s({
    "LoadIndicator": ()=>LoadIndicator,
    "default": ()=>__TURBOPACK__default__export__
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$load_indicator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme/esm/ui/load_indicator.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/devextreme-react/esm/core/component.js [app-client] (ecmascript)");
"use client";
;
;
;
;
const LoadIndicator = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["memo"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])((props, ref)=>{
    const baseRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRef"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useImperativeHandle"])(ref, {
        "LoadIndicator.useImperativeHandle": ()=>({
                instance () {
                    var _baseRef_current;
                    return (_baseRef_current = baseRef.current) === null || _baseRef_current === void 0 ? void 0 : _baseRef_current.getInstance();
                }
            })
    }["LoadIndicator.useImperativeHandle"], [
        baseRef.current
    ]);
    const independentEvents = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "LoadIndicator.useMemo[independentEvents]": ()=>[
                "onContentReady",
                "onDisposing",
                "onInitialized"
            ]
    }["LoadIndicator.useMemo[independentEvents]"], []);
    return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["createElement"](__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2d$react$2f$esm$2f$core$2f$component$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Component"], {
        WidgetClass: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$devextreme$2f$esm$2f$ui$2f$load_indicator$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"],
        ref: baseRef,
        independentEvents,
        ...props
    });
}));
const __TURBOPACK__default__export__ = LoadIndicator;
;
}),
}]);

//# sourceMappingURL=node_modules_devextreme-react_esm_7c15ea2a._.js.map